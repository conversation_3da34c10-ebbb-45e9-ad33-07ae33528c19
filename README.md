# 安装虚拟环境

python3 -m venv .venv

# 激活虚拟环境

```bash 
    source .venv/bin/activate 
```

On Windows use 

```bat
    .venv\Scripts\activate
```
`.venv\Scripts\activate`

# 可编辑安装

    pip install -e .

>pip install -e . 是一个用于在开发环境中安装 Python 包的命令。这个命令中的 -e 选项表示“editable”，即可编辑模式，而 . 表示当前目录。

# 生成 requirements.txt

    pip freeze > requirements.txt

# 从 requirements.txt 安装包

    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/ --trusted-host pypi.tuna.tsinghua.edu.cn

# 下载离线安装包
Use the following command to download the packages:

    pip download -r requirements.txt -d ./dependencies -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 通过离线安装包安装
On the offline machine, install the packages using:

    pip install --no-index --find-links=/path/to/dependencies -r requirements.txt