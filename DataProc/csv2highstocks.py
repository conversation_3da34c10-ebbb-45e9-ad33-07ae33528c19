# -*- coding: utf-8 -*-
import sys
import os
import pandas as pd
import numpy as np
import json
from itertools import islice
from stock_indicators import indicators
from stock_indicators.indicators.common.enums import EndType
from stock_indicators.indicators.common.quote import Quote
from stock_indicators.indicators.zig_zag import Zig<PERSON>agResult
 


def csv_to_quotes(csv_file):
    """
    Transform CSV file with OHLC data to Highstock format

    Args:
        csv_file (str): Path to the CSV file

    Returns:
        list: OHLC data in format [[timestamp, open, high, low, close], ...]
    """
    # Read CSV file
    df = pd.read_csv(csv_file, encoding='utf-8')

    # Print column names for debugging
    print("CSV columns:", df.columns.tolist())
    print("First few rows:")
    print(df.head())

    # Convert time column to datetime and then to timestamp (milliseconds)
    df['timestamp'] = pd.to_datetime(df['时间'])

    quotes_list = [
        Quote(d,o,h,l,c,v) 
        for d,o,h,l,c,v 
        in zip(df['timestamp'], df['开盘价'], df['最高价'], df['最低价'], df['收盘价'], df['成交量'])
    ]
 
    return quotes_list


def csv_to_ohlc_list(csv_file):
    """
    Transform CSV file to simple OHLC list format

    Args:
        csv_file (str): Path to the CSV file

    Returns:
        list: OHLC data in format [[timestamp, open, high, low, close], ...]
    """
    # Read CSV file
    df = pd.read_csv(csv_file, encoding='utf-8')

    # Convert time column to datetime and then to timestamp (milliseconds)
    df['时间'] = pd.to_datetime(df['时间'])
    df['timestamp'] = df['时间'].astype(np.int64) // 10**6  # Convert to milliseconds

    # Prepare OHLC data
    ohlc_data = []
    for _, row in df.iterrows():
        timestamp = int(row['timestamp'])
        open_price = float(row['开盘价'])
        high_price = float(row['最高价'])
        low_price = float(row['最低价'])
        close_price = float(row['收盘价'])

        # OHLC format for Highstock
        ohlc_data.append([timestamp, open_price, high_price, low_price, close_price])

    return ohlc_data


def print_dict_format(results, limit=5):
 
    print(type(results))
    
    print("\n===Format (Preview) ===")
    print("// results Data (first {} records):".format(limit))
    print("var ohlcData = [")
    for r in islice(results, 0, limit):
         print(f"{r.date},{r.open},{r.high},{r.close},{r.low},{r.volume} ")

    if len(results) > limit:
        print(f"    // ... and {len(results) - limit} more records")
    print("];")


    print(f"\n// Total records: {len(results)}")
    print(f"\n// start: {results[0].date}")
    print(f"\n// end: {results[-1].date}")

 

def ohlc_to_quotes(ohlc_data):
    """
    Convert OHLC data to Quote objects for stock_indicators

    Args:
        ohlc_data (list): List of [timestamp, open, high, low, close] data

    Returns:
        list: List of Quote objects
    """
    quotes = []
    for candle in ohlc_data:
        timestamp, open_price, high_price, low_price, close_price = candle

        # Convert timestamp to datetime
        from datetime import datetime
        dt = datetime.fromtimestamp(timestamp / 1000)  # Convert from milliseconds

        # Create Quote object
        quote = Quote(
            date=dt,
            open=float(open_price),
            high=float(high_price),
            low=float(low_price),
            close=float(close_price),
            volume=0  # Volume not used for ZigZag
        )
        quotes.append(quote)
    return quotes


def quotes_to_json(quotes):
    """
    Convert Quote objects to JSON-serializable format

    Args:
        quotes (list): List of Quote objects

    Returns:
        list: List of dictionaries with OHLC data in Highstock format
    """
    json_data = []
    for quote in quotes:
        # Convert to Highstock OHLC format: [timestamp, open, high, low, close]
        timestamp = int(quote.date.timestamp() * 1000)  # Convert to milliseconds
        json_data.append([
            timestamp,
            float(quote.open),
            float(quote.high),
            float(quote.low),
            float(quote.close), 
            float(quote.volume)
        ])
    return json_data

 

 

def export_to_json(data, output_file):
    """
    Export data to JSON file with proper serialization

    Args:
        data (dict): Data to export (may contain Quote or ZigZagResult objects)
        output_file (str): Output JSON file path
    """
    # Convert complex objects to JSON-serializable format
    json_data = {}

    for key, value in data.items():
        if key == "ohlcv" and hasattr(value, '__iter__') and len(value) > 0:
            # Check if it's a list of Quote objects
            if hasattr(value[0], 'date') and hasattr(value[0], 'open'):
                json_data[key] = quotes_to_json(value)
            else:
                json_data[key] = value
        elif key == "zigzag" and hasattr(value, '__iter__') and len(value) > 0:
            # Check if it's a list of ZigZagResult objects
            if hasattr(value[0], 'date') and hasattr(value[0], 'zig_zag'):
                json_data[key] = zigzag_results_to_json(value)
            else:
                json_data[key] = value
        else:
            json_data[key] = value

    # Ensure output directory exists
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, ensure_ascii=False, indent=2)

    print(f"Data exported to {output_file}")
    print(f"File size: {os.path.getsize(output_file)} bytes")


def main():
    """
    Main function to process the rubber weighted 15-minute CSV file
    """
    # File paths
    source_path = "./data/"
    target_path = "./WebApp/data"
    csv_file = "橡胶加权-15分钟.csv"
    json_output = "rubber-15m_highstock.json"

    print(f"Processing CSV file: {csv_file}")

    # Transform CSV to OHLC list format
    quotes = csv_to_quotes(os.path.join(source_path, csv_file))
 
 


 

    # Prepare data for JSON export
    json_data = {
        "ohlcv": quotes,  # Already in correct format
        "title": "橡胶加权-15分钟",
        "data_info": {
            "total_records": len(quotes)
 
        }
    }

    # Export to JSON file
    export_to_json(json_data, os.path.join(target_path, json_output))

    print(f"\n✅ Successfully transformed {csv_file} to Highstock format!")
    print(f"📁 JSON output saved as: {json_output}")
    print(f"📊 Total OHLC records: {len(quotes)}")
 


if __name__ == '__main__':
    main()