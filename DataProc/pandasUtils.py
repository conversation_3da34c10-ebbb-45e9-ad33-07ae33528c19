# -*- coding: utf-8 -*-
import sys
import csv
import os
import codecs


import pandas as pd
import numpy as np
import datetime
import time


def main():

    print np.__version__

    pd.set_option('display.max_rows', 1000)
    pd.set_option('display.width', 1000)
    pd.set_option('display.max_columns', 14)

    df = pd.DataFrame(np.random.randn(10, 18))
 

    out=df.pct_change(periods=3)

    print(out)

 

    pass


if __name__ == '__main__':
    # print sys.getdefaultencoding()
    reload(sys)
    sys.setdefaultencoding('utf-8')
    # print sys.getdefaultencoding()
    main()
