var express = require('express');//引用express
var app = express();//创建express实例

const path = require('path')
app.use(express.static(path.join(__dirname, 'public')))
app.use(express.static(path.join(__dirname, 'data')))


app.get('/', function (req, res) {//当路由url匹配为'/'时，执行function，返回Hello World
    res.send('Hello World');
});

app.get('/:name', function (req, res) {//当路由url匹配为'/'时，执行function，返回Hello World
    console.log( req.params.name)
    res.redirect('./report.html'); 
}); 
 

var server = app.listen(8000, function () {//应用启动端口为8081

    var host = server.address().address;
    var port = server.address().port;

    console.log('Web app listening at %s :%s', host, port);


});