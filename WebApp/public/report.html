<!DOCTYPE HTML>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>橡胶加权 - 15分钟数据分析</title>
    <style>
        @import url("/lib/highstock/css/highcharts.css");
        @import url("/lib/highstock/css/stocktools/gui.css");
        @import url("/lib/highstock/css/annotations/popup.css");

        #container {
            height: 800px;
        }

        @media screen and (max-width: 600px) {
            #container {
                height: 400px;
            }
        }

        #loading,
        #error {
            text-align: center;
            padding: 50px;
            font-size: 18px;
        }

        #error {
            color: #d32f2f;
        }
    </style>

</head>

<body>
    <script src="/lib/highstock/highstock.js"></script>
    <script src="/lib/highstock/indicators/indicators-all.js"></script>
    <script src="/lib/highstock/modules/drag-panes.js"></script>
    <script src="/lib/highstock/modules/accessibility.js"></script>
    <script src="/lib/highstock/modules/full-screen.js"></script>
    <script src="/lib/highstock/modules/stock-tools.js"></script>
    <script src="/lib/highstock/modules/hollowcandlestick.js"></script>
    <div id="container">
        <div id="loading">
            正在加载数据...
        </div>
    </div>
</body>
<script type="text/javascript">
    // Load data from local JSON file

    colors=Highcharts.getOptions().colors;
    async function loadRubberData() {
        try {
            const response = await fetch('rubber-15m_highstock.json');
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Error loading rubber data:', error);
            return null;
        }
    }

    (async () => {

        const loadingElement = document.getElementById('loading');

        try {
            const rubberData = await loadRubberData();
            const ohlc = [],
                volume = [],
                dataLength = rubberData.ohlcv.length

            if (rubberData.ohlcv) {
                // set the allowed units for data grouping
                groupingUnits = [
                    [
                        'minute', [1, 2, 5, 10, 15, 30]
                    ],
                    [
                        'hour', [1, 2, 3, 4, 6, 8, 12]
                    ],
                    [
                        'day', [1]
                    ],
                    [
                        'week', [1]
                    ],
                    [
                        'month', [1, 2, 3, 4, 6]
                    ],
                    [
                        'year',
                        null
                    ]
                ];
                let data = rubberData.ohlcv
                // split the data set into ohlc and volume
                for (let i = dataLength - 800; i < dataLength; i += 1) {
                    ohlc.push([
                        data[i][0], // the date
                        data[i][1], // open
                        data[i][2], // high
                        data[i][3], // low
                        data[i][4] // close
                    ]);

                    volume.push([
                        data[i][0], // the date
                        data[i][5] // the volume
                    ]);
                }
            } else {
                console.error('Error loading rubber data: no data returned');
            }

            // Hide loading indicator
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }
            Highcharts.stockChart('container', {
                title: {
                    text: '橡胶加权 - 15分钟数据'
                },

                credits: {
                    enabled: false
                },
                yAxis: [{
                    labels: {
                        align: 'left'
                    },
                    title: {
                        text: 'OHLC'
                    },
                    height: '80%',
                    resize: {
                        enabled: true
                    }
                }, {
                    labels: {
                        align: 'left'
                    },
                    title: {
                        text: 'Volume'
                    },
                    top: '80%',
                    height: '20%',
                    offset: 0
                }],



                plotOptions: {
                    series: {
                        dataGrouping: {
                            units: groupingUnits
                        }
                    },
                    candlestick: {
                        // shared options for all ohlc series
                        color:colors[0],
                        negativeColor: colors[1]
                    },
                    zigzag: {
                        // shared options for all zigzag series
                    }
                },
                tooltip: {
                    shape: 'square',
                    headerShape: 'callout',
                    borderWidth: 0,
                    shadow: false,
                    fixed: true
                },
                series: [{
                    type: 'candlestick',
                    id: 'rubber-ohlc',
                    name: '橡胶加权价格',
                    data: ohlc

                }, {
                    type: 'zigzag',
                    linkedTo: 'rubber-ohlc',
                    params: {
                        deviation: 5
                    }
                }, {
                    type: 'column',
                    id: 'rubber-volume',
                    name: '成交量',
                    data: volume,
                    yAxis: 1
                }],
                rangeSelector: {
                    inputPosition: {
                        align: 'left',
                        x: 0,
                        y: 0
                    },
                    buttonPosition: {
                        align: 'right',
                        x: 0,
                        y: 0
                    },
                    allButtonsEnabled: true,
                    buttons: [{
                        type: 'month',
                        count: 1,
                        text: '1m'
                    }, {
                        type: 'month',
                        count: 3,
                        text: '3m'
                    }, {
                        type: 'month',
                        count: 6,
                        text: '6m'
                    }, {
                        type: 'ytd',
                        text: 'YTD'
                    }, {
                        type: 'year',
                        count: 1,
                        text: '1y'
                    }, {
                        type: 'all',
                        text: 'All'
                    }],
                    buttonTheme: {
                        width: 60
                    },
                    selected: 3
                },

                responsive: {
                    rules: [{
                        condition: {
                            maxWidth: 800
                        },
                        chartOptions: {
                            rangeSelector: {
                                inputEnabled: false
                            }
                        }
                    }]
                }
            });

        } catch (error) {
            console.error('Error loading chart:', error);

            // Hide loading indicator and show error
            if (loadingElement) {
                loadingElement.innerHTML = '<div id="error">加载数据时出错: ' + error.message + '</div>';
            }
        }
    })();
</script>

</html>