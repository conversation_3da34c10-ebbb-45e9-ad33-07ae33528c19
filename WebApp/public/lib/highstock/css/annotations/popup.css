.highcharts-popup.highcharts-annotation-toolbar {
    right: 10%;
    left: auto;
    height: 40px;
    overflow: hidden;
    padding-right: 40px;
    width: auto;
    min-width: 0;
}

.highcharts-annotation-toolbar button {
    margin-top: 0;
    cursor: pointer;
    background-color: var(--highcharts-neutral-color-3, #f7f7f7);
}

.highcharts-popup.highcharts-annotation-toolbar > p {
    margin: 0;
    display: block;
    float: left;
    padding: 12px;
}

.highcharts-popup {
    font-family:
        -apple-system,
        BlinkMacSystemFont,
        "Segoe UI",
        Roboto,
        Helvetica,
        Arial,
        "Apple Color Emoji",
        "Segoe UI Emoji",
        "Segoe UI Symbol",
        sans-serif;
    background-color: var(--highcharts-background-color, #ffffff);
    border-radius: 4px;
    color: var(--highcharts-neutral-color-60, #666666);
    display: none;
    font-size: 0.9em;
    max-height: 90%;
    top: 5%;
    left: 15%;
    overflow-x: hidden;
    overflow-y: hidden;
    width: 75%;
    min-width: 300px;
    max-width: 600px;
    position: absolute;
    z-index: 100;
    -webkit-box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.5);
    -moz-box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.5);
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.5);
}

.highcharts-popup div,
.highcharts-popup span {
    box-sizing: border-box;
}

.highcharts-popup input,
.highcharts-popup label,
.highcharts-popup select {
    clear: both;
    float: left;
    width: 100%;
    margin-bottom: 10px;
    box-sizing: border-box;
}

.highcharts-popup input,
.highcharts-popup select {
    background: var(--highcharts-neutral-color-3, #f7f7f7);
    color: var(--highcharts-neutral-color-100, #000000);
}

.highcharts-popup input {
    border: 1px solid var(--highcharts-neutral-color-10, #e6e6e6);
    border-radius: 0.3rem;
    padding: 5px;
    width: 100%;
}

.highcharts-popup-lhs-col,
.highcharts-popup-rhs-col {
    height: 100%;
    float: left;
    overflow-y: auto;
}

.highcharts-popup-lhs-col.highcharts-popup-lhs-full {
    width: 100%;
    overflow-x: hidden;
    height: calc(100% - 104px);
    border: none;
    padding: 20px;
    padding-bottom: 10px;
}

.highcharts-popup-lhs-col.highcharts-popup-lhs-full + .highcharts-popup-bottom-row {
    width: 100%;
}

.highcharts-popup-lhs-col {
    clear: both;
    width: 30%;
    border-right: 1px solid var(--highcharts-neutral-color-10, #e6e6e6);
}

.highcharts-popup-bottom-row {
    float: left;
    padding: 0 20px;
    padding-bottom: 12px;
    width: 100%;
    border-top: 1px solid var(--highcharts-neutral-color-10, #e6e6e6);
}

.highcharts-popup .highcharts-popup-bottom-row > button {
    margin-top: 10px;
    padding: 5px 10px;
    border-radius: 4px;
}

.highcharts-popup-rhs-col {
    width: 70%;
    height: calc(100% - 40px);
    padding: 20px;
}

.highcharts-popup-rhs-col-wrapper {
    width: 100%;
    overflow-x: hidden;
}

.highcharts-popup-rhs-col-wrapper h3 {
    margin-top: 0;
    padding-bottom: 0;
}

.highcharts-bindings-wrapper ul.highcharts-indicator-list,
.highcharts-indicator-list {
    float: left;
    color: var(--highcharts-neutral-color-60, #666666);
    height: calc(100% - 150px);
    width: 100%;
    overflow-x: hidden;
    margin: 0;
    padding: 15px 0;
}

button.highcharts-indicator-list-item {
    cursor: pointer;
    padding: 5px 20px;
    width: 100%;
    height: auto;
    overflow: hidden;
    word-break: break-all;
    box-sizing: border-box;
    color: var(--highcharts-neutral-color-100, #000000);
    background-color: var(--highcharts-background-color, #ffffff);
    text-align: left;
}

.highcharts-indicator-list li {
    width: 100%;
    padding: 0;
    height: auto;
}

.highcharts-popup > button.highcharts-tab-item {
    margin: 0;
    color: var(--highcharts-neutral-color-100, #000000);
    background-color: var(--highcharts-neutral-color-3, #f7f7f7);
    cursor: pointer;
    display: block;
    float: left;
    padding: 0 10px;
    height: 40px;
    line-height: 40px;
}

button.highcharts-tab-item.highcharts-tab-item-active {
    background-color: var(--highcharts-neutral-color-10, #e6e6e6);
}

.highcharts-tab-item-content {
    display: none;
    float: left;
    height: 100%;
    overflow: hidden;
    width: 100%;
    border-top: 1px solid var(--highcharts-neutral-color-10, #e6e6e6);
}

.highcharts-tab-item-show {
    display: block;
}

.highcharts-popup > .highcharts-popup-close {
    margin: 0;
    background-color: var(--highcharts-background-color, #ffffff);
    width: 40px;
    height: 40px;
    cursor: pointer;
    position: absolute;
    padding: 10px;
    top: 0%;
    right: 0%;
    color: var(--highcharts-neutral-color-80, #333333);
}

.highcharts-popup .highcharts-icon {
    position: absolute;
    top: 0;
    left: 0;
    display: block;
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-position: 50% 50%;
}

.highcharts-dark .highcharts-popup {
    color-scheme: dark;
    border: 1px solid var(--highcharts-neutral-color-10, #e6e6e6);
}

.highcharts-dark .highcharts-popup .highcharts-icon {
    filter: invert(1);
}

@media (prefers-color-scheme: dark) {
    .highcharts-popup {
        color-scheme: dark;
        border: 1px solid var(--highcharts-neutral-color-10, #e6e6e6);
    }

    .highcharts-popup .highcharts-icon {
        filter: invert(1);
    }

    .highcharts-light .highcharts-popup {
        color-scheme: light;
        border: none;
    }

    .highcharts-light .highcharts-popup .highcharts-icon {
        filter: none;
    }
}

.highcharts-popup-close:hover,
.highcharts-popup button:hover,
.highcharts-popup button.highcharts-annotation-edit-button:hover,
.highcharts-popup button.highcharts-annotation-remove-button:hover {
    background-color: var(--highcharts-neutral-color-10, #e6e6e6);
}

div.highcharts-popup-rhs-col > button {
    float: right;
    padding: 5px 10px;
    border-radius: 4px;
}

.highcharts-popup button {
    border: none;
    margin: 0 5px 0 0;
}

button.highcharts-tab-item.highcharts-tab-disabled {
    color: var(--highcharts-neutral-color-20, #cccccc);
}

button.highcharts-tab-item.highcharts-tab-disabled:hover {
    background-color: var(--highcharts-neutral-color-3, #f7f7f7);
}

/* annotation edit small popup */
.highcharts-popup button.highcharts-annotation-edit-button,
.highcharts-popup button.highcharts-annotation-remove-button {
    width: 20px;
    height: 40px;
    padding: 20px;
}

.highcharts-popup button.highcharts-annotation-edit-button {
    position: relative;
    text-indent: -9999px;
}

.highcharts-popup button.highcharts-annotation-remove-button {
    position: relative;
    text-indent: -9999px;
}

.highcharts-popup .highcharts-annotation-title {
    display: block;
    float: left;
    font-size: 1.2em;
    font-weight: bold;
    margin-bottom: 15px;
    width: 100%;
}

.highcharts-popup .highcharts-popup-main-title {
    border-bottom: 1px solid var(--highcharts-neutral-color-10, #e6e6e6);
    margin: 0;
    padding: 8px 0 6px 20px;
}

.highcharts-indicator-title {
    float: left;
    padding-bottom: 15px;
}

.highcharts-input-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    flex-wrap: wrap;
}

.highcharts-input-search-indicators-label {
    text-align: center;
    font-weight: bold;
    color: var(--highcharts-neutral-color-100, #000000);
    margin-top: 0.5rem;
}

input.highcharts-input-search-indicators {
    width: 80%;
    margin: 0 auto;
    float: none;
    border-color: var(--highcharts-neutral-color-40, #999999);
}

.highcharts-popup a.clear-filter-button {
    margin: 0 auto;
    display: none;
    color: var(--highcharts-highlight-color-80, #335cad);
    background-color: var(--highcharts-background-color, #ffffff);
    border: 1px solid var(--highcharts-highlight-color-80, #335cad);
    margin-top: 10px;
}

.highcharts-popup a.clear-filter-button:hover {
    color: var(--highcharts-background-color, #ffffff);
    background-color: var(--highcharts-highlight-color-80, #335cad);
    cursor: pointer;
}
