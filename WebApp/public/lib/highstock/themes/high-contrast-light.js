!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/themes/high-contrast-light
 * @requires highcharts
 *
 * (c) 2009-2025 Highsoft AS
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts):"function"==typeof define&&define.amd?define("highcharts/themes/high-contrast-light",["highcharts/highcharts"],function(t){return e(t)}):"object"==typeof exports?exports["highcharts/themes/high-contrast-light"]=e(t._Highcharts):t.Highcharts=e(t.Highcharts)}("undefined"==typeof window?this:window,t=>(()=>{"use strict";var e,o={944:e=>{e.exports=t}},r={};function i(t){var e=r[t];if(void 0!==e)return e.exports;var n=r[t]={exports:{}};return o[t](n,n.exports,i),n.exports}i.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return i.d(e,{a:e}),e},i.d=(t,e)=>{for(var o in e)i.o(e,o)&&!i.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var n={};i.d(n,{default:()=>f});var s=i(944),h=i.n(s);let{setOptions:a}=h();!function(t){t.options={colors:["#265FB5","#222","#698F01","#F4693E","#4C0684","#0FA388","#B7104A","#AF9023","#1A704C","#B02FDD"],credits:{style:{color:"#767676"}},navigator:{series:{color:"#5f98cf",lineColor:"#5f98cf"}}},t.apply=function(){a(t.options)}}(e||(e={}));let c=e;h().theme=c.options,c.apply();let f=h();return n.default})());