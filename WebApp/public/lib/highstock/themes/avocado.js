!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/themes/avocado
 * @requires highcharts
 *
 * (c) 2009-2025 Highsoft AS
 *
 * License: www.highcharts.com/license
 */function(o,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(o._Highcharts):"function"==typeof define&&define.amd?define("highcharts/themes/avocado",["highcharts/highcharts"],function(o){return e(o)}):"object"==typeof exports?exports["highcharts/themes/avocado"]=e(o._Highcharts):o.Highcharts=e(o.Highcharts)}("undefined"==typeof window?this:window,o=>(()=>{"use strict";var e,t={944:e=>{e.exports=o}},r={};function n(o){var e=r[o];if(void 0!==e)return e.exports;var i=r[o]={exports:{}};return t[o](i,i.exports,n),i.exports}n.n=o=>{var e=o&&o.__esModule?()=>o.default:()=>o;return n.d(e,{a:e}),e},n.d=(o,e)=>{for(var t in e)n.o(e,t)&&!n.o(o,t)&&Object.defineProperty(o,t,{enumerable:!0,get:e[t]})},n.o=(o,e)=>Object.prototype.hasOwnProperty.call(o,e);var i={};n.d(i,{default:()=>c});var a=n(944),s=n.n(a);let{setOptions:p}=s();!function(o){o.options={colors:["#F3E796","#95C471","#35729E","#251735"],colorAxis:{maxColor:"#05426E",minColor:"#F3E796"},plotOptions:{map:{nullColor:"#FCFEFE"}},navigator:{maskFill:"rgba(170, 205, 170, 0.5)",series:{color:"#95C471",lineColor:"#35729E"}}},o.apply=function(){p(o.options)}}(e||(e={}));let l=e;s().theme=l.options,l.apply();let c=s();return i.default})());