!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/themes/dark-unica
 * @requires highcharts
 *
 * (c) 2009-2025 Torstein Honsi
 *
 * License: www.highcharts.com/license
 */function(o,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(o._Highcharts):"function"==typeof define&&define.amd?define("highcharts/themes/dark-unica",["highcharts/highcharts"],function(o){return e(o)}):"object"==typeof exports?exports["highcharts/themes/dark-unica"]=e(o._Highcharts):o.Highcharts=e(o.Highcharts)}("undefined"==typeof window?this:window,o=>(()=>{"use strict";var e,r={944:e=>{e.exports=o}},t={};function l(o){var e=t[o];if(void 0!==e)return e.exports;var i=t[o]={exports:{}};return r[o](i,i.exports,l),i.exports}l.n=o=>{var e=o&&o.__esModule?()=>o.default:()=>o;return l.d(e,{a:e}),e},l.d=(o,e)=>{for(var r in e)l.o(e,r)&&!l.o(o,r)&&Object.defineProperty(o,r,{enumerable:!0,get:e[r]})},l.o=(o,e)=>Object.prototype.hasOwnProperty.call(o,e);var i={};l.d(i,{default:()=>p});var a=l(944),n=l.n(a);let{setOptions:s}=n(),{createElement:c}=n();!function(o){o.options={colors:["#2b908f","#90ee7e","#f45b5b","#7798BF","#aaeeee","#ff0066","#eeaaee","#55BF3B","#DF5353","#7798BF","#aaeeee"],chart:{backgroundColor:{linearGradient:{x1:0,y1:0,x2:1,y2:1},stops:[[0,"#2a2a2b"],[1,"#3e3e40"]]},style:{fontFamily:"'Unica One', sans-serif"},plotBorderColor:"#606063"},title:{style:{color:"#E0E0E3",textTransform:"uppercase",fontSize:"20px"}},subtitle:{style:{color:"#E0E0E3",textTransform:"uppercase"}},xAxis:{gridLineColor:"#707073",labels:{style:{color:"#E0E0E3"}},lineColor:"#707073",minorGridLineColor:"#505053",tickColor:"#707073",title:{style:{color:"#A0A0A3"}}},yAxis:{gridLineColor:"#707073",labels:{style:{color:"#E0E0E3"}},lineColor:"#707073",minorGridLineColor:"#505053",tickColor:"#707073",tickWidth:1,title:{style:{color:"#A0A0A3"}}},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.85)",style:{color:"#F0F0F0"}},plotOptions:{series:{dataLabels:{color:"#F0F0F3",style:{fontSize:"13px"}},marker:{lineColor:"#333"}},boxplot:{fillColor:"#505053"},candlestick:{lineColor:"white"},errorbar:{color:"white"}},legend:{backgroundColor:"rgba(0, 0, 0, 0.5)",itemStyle:{color:"#E0E0E3"},itemHoverStyle:{color:"#FFF"},itemHiddenStyle:{color:"#606063"},title:{style:{color:"#C0C0C0"}}},credits:{style:{color:"#666"}},drilldown:{activeAxisLabelStyle:{color:"#F0F0F3"},activeDataLabelStyle:{color:"#F0F0F3"}},navigation:{buttonOptions:{symbolStroke:"#DDDDDD",theme:{fill:"#505053"}}},rangeSelector:{buttonTheme:{fill:"#505053",stroke:"#000000",style:{color:"#CCC"},states:{hover:{fill:"#707073",stroke:"#000000",style:{color:"white"}},select:{fill:"#000003",stroke:"#000000",style:{color:"white"}}}},inputBoxBorderColor:"#505053",inputStyle:{backgroundColor:"#333",color:"silver"},labelStyle:{color:"silver"}},navigator:{handles:{backgroundColor:"#666",borderColor:"#AAA"},outlineColor:"#CCC",maskFill:"rgba(255,255,255,0.1)",series:{color:"#7798BF",lineColor:"#A6C7ED"},xAxis:{gridLineColor:"#505053"}},scrollbar:{barBackgroundColor:"#808083",barBorderColor:"#808083",buttonArrowColor:"#CCC",buttonBackgroundColor:"#606063",buttonBorderColor:"#606063",rifleColor:"#FFF",trackBackgroundColor:"#404043",trackBorderColor:"#404043"}},o.apply=function(){c("link",{href:"https://fonts.googleapis.com/css?family=Unica+One",rel:"stylesheet",type:"text/css"},null,document.getElementsByTagName("head")[0]),s(o.options)}}(e||(e={}));let d=e;n().theme=d.options,d.apply();let p=n();return i.default})());