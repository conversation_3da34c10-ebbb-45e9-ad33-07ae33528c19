{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/themes/dark-green\n * @requires highcharts\n *\n * (c) 2009-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/themes/dark-green\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/themes/dark-green\"] = factory(root[\"_Highcharts\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ dark_green_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Extensions/Themes/DarkGreen.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  Dark blue theme for Highcharts JS\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { setOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Theme\n *\n * */\nvar DarkGreenTheme;\n(function (DarkGreenTheme) {\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    DarkGreenTheme.options = {\n        colors: [\n            '#DDDF0D', '#55BF3B', '#DF5353', '#7798BF', '#aaeeee',\n            '#ff0066', '#eeaaee', '#55BF3B', '#DF5353', '#7798BF', '#aaeeee'\n        ],\n        chart: {\n            backgroundColor: {\n                linearGradient: { x1: 0, y1: 0, x2: 1, y2: 1 },\n                stops: [\n                    [0, 'rgb(48, 96, 48)'],\n                    [1, 'rgb(0, 0, 0)']\n                ]\n            },\n            borderColor: '#000000',\n            borderWidth: 2,\n            className: 'dark-container',\n            plotBackgroundColor: 'rgba(255, 255, 255, .1)',\n            plotBorderColor: '#CCCCCC',\n            plotBorderWidth: 1\n        },\n        title: {\n            style: {\n                color: '#C0C0C0',\n                font: 'bold 16px \"Trebuchet MS\", Verdana, sans-serif'\n            }\n        },\n        subtitle: {\n            style: {\n                color: '#666666',\n                font: 'bold 12px \"Trebuchet MS\", Verdana, sans-serif'\n            }\n        },\n        xAxis: {\n            gridLineColor: '#333333',\n            gridLineWidth: 1,\n            labels: {\n                style: {\n                    color: '#A0A0A0'\n                }\n            },\n            lineColor: '#A0A0A0',\n            tickColor: '#A0A0A0',\n            title: {\n                style: {\n                    color: '#CCC',\n                    fontWeight: 'bold',\n                    fontSize: '12px',\n                    fontFamily: 'Trebuchet MS, Verdana, sans-serif'\n                }\n            }\n        },\n        yAxis: {\n            gridLineColor: '#333333',\n            labels: {\n                style: {\n                    color: '#A0A0A0'\n                }\n            },\n            lineColor: '#A0A0A0',\n            tickColor: '#A0A0A0',\n            tickWidth: 1,\n            title: {\n                style: {\n                    color: '#CCC',\n                    fontWeight: 'bold',\n                    fontSize: '12px',\n                    fontFamily: 'Trebuchet MS, Verdana, sans-serif'\n                }\n            }\n        },\n        tooltip: {\n            backgroundColor: 'rgba(0, 0, 0, 0.75)',\n            style: {\n                color: '#F0F0F0'\n            }\n        },\n        plotOptions: {\n            line: {\n                dataLabels: {\n                    color: '#CCC'\n                },\n                marker: {\n                    lineColor: '#333'\n                }\n            },\n            spline: {\n                marker: {\n                    lineColor: '#333'\n                }\n            },\n            scatter: {\n                marker: {\n                    lineColor: '#333'\n                }\n            },\n            candlestick: {\n                lineColor: 'white'\n            }\n        },\n        legend: {\n            backgroundColor: 'rgba(0, 0, 0, 0.5)',\n            itemStyle: {\n                font: '9pt Trebuchet MS, Verdana, sans-serif',\n                color: '#A0A0A0'\n            },\n            itemHoverStyle: {\n                color: '#FFF'\n            },\n            itemHiddenStyle: {\n                color: '#444'\n            },\n            title: {\n                style: {\n                    color: '#C0C0C0'\n                }\n            }\n        },\n        credits: {\n            style: {\n                color: '#666'\n            }\n        },\n        navigation: {\n            buttonOptions: {\n                symbolStroke: '#DDDDDD',\n                theme: {\n                    fill: {\n                        linearGradient: { x1: 0, y1: 0, x2: 0, y2: 1 },\n                        stops: [\n                            [0.4, '#606060'],\n                            [0.6, '#333333']\n                        ]\n                    },\n                    stroke: '#000000'\n                }\n            }\n        },\n        // Scroll charts\n        rangeSelector: {\n            buttonTheme: {\n                fill: {\n                    linearGradient: { x1: 0, y1: 0, x2: 0, y2: 1 },\n                    stops: [\n                        [0.4, '#888'],\n                        [0.6, '#555']\n                    ]\n                },\n                stroke: '#000000',\n                style: {\n                    color: '#CCC',\n                    fontWeight: 'bold'\n                },\n                states: {\n                    hover: {\n                        fill: {\n                            linearGradient: { x1: 0, y1: 0, x2: 0, y2: 1 },\n                            stops: [\n                                [0.4, '#BBB'],\n                                [0.6, '#888']\n                            ]\n                        },\n                        stroke: '#000000',\n                        style: {\n                            color: 'white'\n                        }\n                    },\n                    select: {\n                        fill: {\n                            linearGradient: { x1: 0, y1: 0, x2: 0, y2: 1 },\n                            stops: [\n                                [0.1, '#000'],\n                                [0.3, '#333']\n                            ]\n                        },\n                        stroke: '#000000',\n                        style: {\n                            color: 'yellow'\n                        }\n                    }\n                }\n            },\n            inputStyle: {\n                backgroundColor: '#333',\n                color: 'silver'\n            },\n            labelStyle: {\n                color: 'silver'\n            }\n        },\n        navigator: {\n            handles: {\n                backgroundColor: '#666',\n                borderColor: '#AAA'\n            },\n            outlineColor: '#CCC',\n            maskFill: 'rgba(16, 16, 16, 0.5)',\n            series: {\n                color: '#7798BF',\n                lineColor: '#A6C7ED'\n            }\n        },\n        scrollbar: {\n            barBackgroundColor: {\n                linearGradient: { x1: 0, y1: 0, x2: 0, y2: 1 },\n                stops: [\n                    [0.4, '#888'],\n                    [0.6, '#555']\n                ]\n            },\n            barBorderColor: '#CCC',\n            buttonArrowColor: '#CCC',\n            buttonBackgroundColor: {\n                linearGradient: { x1: 0, y1: 0, x2: 0, y2: 1 },\n                stops: [\n                    [0.4, '#888'],\n                    [0.6, '#555']\n                ]\n            },\n            buttonBorderColor: '#CCC',\n            rifleColor: '#FFF',\n            trackBackgroundColor: {\n                linearGradient: { x1: 0, y1: 0, x2: 0, y2: 1 },\n                stops: [\n                    [0, '#000'],\n                    [1, '#333']\n                ]\n            },\n            trackBorderColor: '#666'\n        }\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Apply the theme.\n     */\n    function apply() {\n        setOptions(DarkGreenTheme.options);\n    }\n    DarkGreenTheme.apply = apply;\n})(DarkGreenTheme || (DarkGreenTheme = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const DarkGreen = (DarkGreenTheme);\n\n;// ./code/es-modules/masters/themes/dark-green.js\n\n\n\n\n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).theme = DarkGreen.options;\nDarkGreen.apply();\n/* harmony default export */ const dark_green_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "DarkGreenTheme", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "dark_green_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "setOptions", "options", "colors", "chart", "backgroundColor", "linearGradient", "x1", "y1", "x2", "y2", "stops", "borderColor", "borderWidth", "className", "plotBackgroundColor", "plotBorderColor", "plotBorder<PERSON>idth", "title", "style", "color", "font", "subtitle", "xAxis", "gridLineColor", "gridLineWidth", "labels", "lineColor", "tickColor", "fontWeight", "fontSize", "fontFamily", "yAxis", "tickWidth", "tooltip", "plotOptions", "line", "dataLabels", "marker", "spline", "scatter", "candlestick", "legend", "itemStyle", "itemHoverStyle", "itemHiddenStyle", "credits", "navigation", "buttonOptions", "symbolStroke", "theme", "fill", "stroke", "rangeSelector", "buttonTheme", "states", "hover", "select", "inputStyle", "labelStyle", "navigator", "handles", "outlineColor", "maskFill", "series", "scrollbar", "barBackgroundColor", "barBorderColor", "buttonArrowColor", "buttonBackgroundColor", "buttonBorderColor", "rifleColor", "trackBackgroundColor", "trackBorderColor", "apply", "<PERSON><PERSON><PERSON>"], "mappings": "CASA,AATA;;;;;;;;CAQC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,EACrC,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,+BAAgC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAM,GACjG,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,+BAA+B,CAAGD,EAAQD,EAAK,WAAc,EAErEA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CACjD,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,AAACC,GACnC,AAAC,CAAA,KACP,aACA,IAgGNC,EAhGUC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAerH,GAAM,CAAEE,WAAAA,CAAU,CAAE,CAAID,KAOxB,AAAC,SAAU3B,CAAc,EAMrBA,EAAe6B,OAAO,CAAG,CACrBC,OAAQ,CACJ,UAAW,UAAW,UAAW,UAAW,UAC5C,UAAW,UAAW,UAAW,UAAW,UAAW,UAC1D,CACDC,MAAO,CACHC,gBAAiB,CACbC,eAAgB,CAAEC,GAAI,EAAGC,GAAI,EAAGC,GAAI,EAAGC,GAAI,CAAE,EAC7CC,MAAO,CACH,CAAC,EAAG,kBAAkB,CACtB,CAAC,EAAG,eAAe,CACtB,AACL,EACAC,YAAa,UACbC,YAAa,EACbC,UAAW,iBACXC,oBAAqB,0BACrBC,gBAAiB,UACjBC,gBAAiB,CACrB,EACAC,MAAO,CACHC,MAAO,CACHC,MAAO,UACPC,KAAM,+CACV,CACJ,EACAC,SAAU,CACNH,MAAO,CACHC,MAAO,UACPC,KAAM,+CACV,CACJ,EACAE,MAAO,CACHC,cAAe,UACfC,cAAe,EACfC,OAAQ,CACJP,MAAO,CACHC,MAAO,SACX,CACJ,EACAO,UAAW,UACXC,UAAW,UACXV,MAAO,CACHC,MAAO,CACHC,MAAO,OACPS,WAAY,OACZC,SAAU,OACVC,WAAY,mCAChB,CACJ,CACJ,EACAC,MAAO,CACHR,cAAe,UACfE,OAAQ,CACJP,MAAO,CACHC,MAAO,SACX,CACJ,EACAO,UAAW,UACXC,UAAW,UACXK,UAAW,EACXf,MAAO,CACHC,MAAO,CACHC,MAAO,OACPS,WAAY,OACZC,SAAU,OACVC,WAAY,mCAChB,CACJ,CACJ,EACAG,QAAS,CACL7B,gBAAiB,sBACjBc,MAAO,CACHC,MAAO,SACX,CACJ,EACAe,YAAa,CACTC,KAAM,CACFC,WAAY,CACRjB,MAAO,MACX,EACAkB,OAAQ,CACJX,UAAW,MACf,CACJ,EACAY,OAAQ,CACJD,OAAQ,CACJX,UAAW,MACf,CACJ,EACAa,QAAS,CACLF,OAAQ,CACJX,UAAW,MACf,CACJ,EACAc,YAAa,CACTd,UAAW,OACf,CACJ,EACAe,OAAQ,CACJrC,gBAAiB,qBACjBsC,UAAW,CACPtB,KAAM,wCACND,MAAO,SACX,EACAwB,eAAgB,CACZxB,MAAO,MACX,EACAyB,gBAAiB,CACbzB,MAAO,MACX,EACAF,MAAO,CACHC,MAAO,CACHC,MAAO,SACX,CACJ,CACJ,EACA0B,QAAS,CACL3B,MAAO,CACHC,MAAO,MACX,CACJ,EACA2B,WAAY,CACRC,cAAe,CACXC,aAAc,UACdC,MAAO,CACHC,KAAM,CACF7C,eAAgB,CAAEC,GAAI,EAAGC,GAAI,EAAGC,GAAI,EAAGC,GAAI,CAAE,EAC7CC,MAAO,CACH,CAAC,GAAK,UAAU,CAChB,CAAC,GAAK,UAAU,CACnB,AACL,EACAyC,OAAQ,SACZ,CACJ,CACJ,EAEAC,cAAe,CACXC,YAAa,CACTH,KAAM,CACF7C,eAAgB,CAAEC,GAAI,EAAGC,GAAI,EAAGC,GAAI,EAAGC,GAAI,CAAE,EAC7CC,MAAO,CACH,CAAC,GAAK,OAAO,CACb,CAAC,GAAK,OAAO,CAChB,AACL,EACAyC,OAAQ,UACRjC,MAAO,CACHC,MAAO,OACPS,WAAY,MAChB,EACA0B,OAAQ,CACJC,MAAO,CACHL,KAAM,CACF7C,eAAgB,CAAEC,GAAI,EAAGC,GAAI,EAAGC,GAAI,EAAGC,GAAI,CAAE,EAC7CC,MAAO,CACH,CAAC,GAAK,OAAO,CACb,CAAC,GAAK,OAAO,CAChB,AACL,EACAyC,OAAQ,UACRjC,MAAO,CACHC,MAAO,OACX,CACJ,EACAqC,OAAQ,CACJN,KAAM,CACF7C,eAAgB,CAAEC,GAAI,EAAGC,GAAI,EAAGC,GAAI,EAAGC,GAAI,CAAE,EAC7CC,MAAO,CACH,CAAC,GAAK,OAAO,CACb,CAAC,GAAK,OAAO,CAChB,AACL,EACAyC,OAAQ,UACRjC,MAAO,CACHC,MAAO,QACX,CACJ,CACJ,CACJ,EACAsC,WAAY,CACRrD,gBAAiB,OACjBe,MAAO,QACX,EACAuC,WAAY,CACRvC,MAAO,QACX,CACJ,EACAwC,UAAW,CACPC,QAAS,CACLxD,gBAAiB,OACjBO,YAAa,MACjB,EACAkD,aAAc,OACdC,SAAU,wBACVC,OAAQ,CACJ5C,MAAO,UACPO,UAAW,SACf,CACJ,EACAsC,UAAW,CACPC,mBAAoB,CAChB5D,eAAgB,CAAEC,GAAI,EAAGC,GAAI,EAAGC,GAAI,EAAGC,GAAI,CAAE,EAC7CC,MAAO,CACH,CAAC,GAAK,OAAO,CACb,CAAC,GAAK,OAAO,CAChB,AACL,EACAwD,eAAgB,OAChBC,iBAAkB,OAClBC,sBAAuB,CACnB/D,eAAgB,CAAEC,GAAI,EAAGC,GAAI,EAAGC,GAAI,EAAGC,GAAI,CAAE,EAC7CC,MAAO,CACH,CAAC,GAAK,OAAO,CACb,CAAC,GAAK,OAAO,CAChB,AACL,EACA2D,kBAAmB,OACnBC,WAAY,OACZC,qBAAsB,CAClBlE,eAAgB,CAAEC,GAAI,EAAGC,GAAI,EAAGC,GAAI,EAAGC,GAAI,CAAE,EAC7CC,MAAO,CACH,CAAC,EAAG,OAAO,CACX,CAAC,EAAG,OAAO,CACd,AACL,EACA8D,iBAAkB,MACtB,CACJ,EAYApG,EAAeqG,KAAK,CAHpB,WACIzE,EAAW5B,EAAe6B,OAAO,CACrC,CAEJ,EAAG7B,GAAmBA,CAAAA,EAAiB,CAAC,CAAA,GAMX,IAAMsG,EAAatG,CAOhD,CAAC2B,IAA+EkD,KAAK,CAAGyB,EAAUzE,OAAO,CACzGyE,EAAUD,KAAK,GACc,IAAM5E,EAAmBE,IAG5C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}