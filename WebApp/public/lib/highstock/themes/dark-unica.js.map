{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/themes/dark-unica\n * @requires highcharts\n *\n * (c) 2009-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/themes/dark-unica\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/themes/dark-unica\"] = factory(root[\"_Highcharts\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ dark_unica_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Extensions/Themes/DarkUnica.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  Dark theme for Highcharts JS\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { setOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { createElement } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Theme\n *\n * */\nvar DarkUnicaTheme;\n(function (DarkUnicaTheme) {\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    DarkUnicaTheme.options = {\n        colors: [\n            '#2b908f', '#90ee7e', '#f45b5b', '#7798BF', '#aaeeee', '#ff0066',\n            '#eeaaee', '#55BF3B', '#DF5353', '#7798BF', '#aaeeee'\n        ],\n        chart: {\n            backgroundColor: {\n                linearGradient: { x1: 0, y1: 0, x2: 1, y2: 1 },\n                stops: [\n                    [0, '#2a2a2b'],\n                    [1, '#3e3e40']\n                ]\n            },\n            style: {\n                fontFamily: '\\'Unica One\\', sans-serif'\n            },\n            plotBorderColor: '#606063'\n        },\n        title: {\n            style: {\n                color: '#E0E0E3',\n                textTransform: 'uppercase',\n                fontSize: '20px'\n            }\n        },\n        subtitle: {\n            style: {\n                color: '#E0E0E3',\n                textTransform: 'uppercase'\n            }\n        },\n        xAxis: {\n            gridLineColor: '#707073',\n            labels: {\n                style: {\n                    color: '#E0E0E3'\n                }\n            },\n            lineColor: '#707073',\n            minorGridLineColor: '#505053',\n            tickColor: '#707073',\n            title: {\n                style: {\n                    color: '#A0A0A3'\n                }\n            }\n        },\n        yAxis: {\n            gridLineColor: '#707073',\n            labels: {\n                style: {\n                    color: '#E0E0E3'\n                }\n            },\n            lineColor: '#707073',\n            minorGridLineColor: '#505053',\n            tickColor: '#707073',\n            tickWidth: 1,\n            title: {\n                style: {\n                    color: '#A0A0A3'\n                }\n            }\n        },\n        tooltip: {\n            backgroundColor: 'rgba(0, 0, 0, 0.85)',\n            style: {\n                color: '#F0F0F0'\n            }\n        },\n        plotOptions: {\n            series: {\n                dataLabels: {\n                    color: '#F0F0F3',\n                    style: {\n                        fontSize: '13px'\n                    }\n                },\n                marker: {\n                    lineColor: '#333'\n                }\n            },\n            boxplot: {\n                fillColor: '#505053'\n            },\n            candlestick: {\n                lineColor: 'white'\n            },\n            errorbar: {\n                color: 'white'\n            }\n        },\n        legend: {\n            backgroundColor: 'rgba(0, 0, 0, 0.5)',\n            itemStyle: {\n                color: '#E0E0E3'\n            },\n            itemHoverStyle: {\n                color: '#FFF'\n            },\n            itemHiddenStyle: {\n                color: '#606063'\n            },\n            title: {\n                style: {\n                    color: '#C0C0C0'\n                }\n            }\n        },\n        credits: {\n            style: {\n                color: '#666'\n            }\n        },\n        drilldown: {\n            activeAxisLabelStyle: {\n                color: '#F0F0F3'\n            },\n            activeDataLabelStyle: {\n                color: '#F0F0F3'\n            }\n        },\n        navigation: {\n            buttonOptions: {\n                symbolStroke: '#DDDDDD',\n                theme: {\n                    fill: '#505053'\n                }\n            }\n        },\n        // Scroll charts\n        rangeSelector: {\n            buttonTheme: {\n                fill: '#505053',\n                stroke: '#000000',\n                style: {\n                    color: '#CCC'\n                },\n                states: {\n                    hover: {\n                        fill: '#707073',\n                        stroke: '#000000',\n                        style: {\n                            color: 'white'\n                        }\n                    },\n                    select: {\n                        fill: '#000003',\n                        stroke: '#000000',\n                        style: {\n                            color: 'white'\n                        }\n                    }\n                }\n            },\n            inputBoxBorderColor: '#505053',\n            inputStyle: {\n                backgroundColor: '#333',\n                color: 'silver'\n            },\n            labelStyle: {\n                color: 'silver'\n            }\n        },\n        navigator: {\n            handles: {\n                backgroundColor: '#666',\n                borderColor: '#AAA'\n            },\n            outlineColor: '#CCC',\n            maskFill: 'rgba(255,255,255,0.1)',\n            series: {\n                color: '#7798BF',\n                lineColor: '#A6C7ED'\n            },\n            xAxis: {\n                gridLineColor: '#505053'\n            }\n        },\n        scrollbar: {\n            barBackgroundColor: '#808083',\n            barBorderColor: '#808083',\n            buttonArrowColor: '#CCC',\n            buttonBackgroundColor: '#606063',\n            buttonBorderColor: '#606063',\n            rifleColor: '#FFF',\n            trackBackgroundColor: '#404043',\n            trackBorderColor: '#404043'\n        }\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Apply the theme.\n     */\n    function apply() {\n        // Load the fonts\n        createElement('link', {\n            href: 'https://fonts.googleapis.com/css?family=Unica+One',\n            rel: 'stylesheet',\n            type: 'text/css'\n        }, null, document.getElementsByTagName('head')[0]);\n        // Apply the theme\n        setOptions(DarkUnicaTheme.options);\n    }\n    DarkUnicaTheme.apply = apply;\n})(DarkUnicaTheme || (DarkUnicaTheme = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const DarkUnica = (DarkUnicaTheme);\n\n;// ./code/es-modules/masters/themes/dark-unica.js\n\n\n\n\n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).theme = DarkUnica.options;\nDarkUnica.apply();\n/* harmony default export */ const dark_unica_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "DarkUnicaTheme", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "dark_unica_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "setOptions", "createElement", "options", "colors", "chart", "backgroundColor", "linearGradient", "x1", "y1", "x2", "y2", "stops", "style", "fontFamily", "plotBorderColor", "title", "color", "textTransform", "fontSize", "subtitle", "xAxis", "gridLineColor", "labels", "lineColor", "minorGridLineColor", "tickColor", "yAxis", "tickWidth", "tooltip", "plotOptions", "series", "dataLabels", "marker", "boxplot", "fillColor", "candlestick", "errorbar", "legend", "itemStyle", "itemHoverStyle", "itemHiddenStyle", "credits", "drilldown", "activeAxisLabelStyle", "activeDataLabelStyle", "navigation", "buttonOptions", "symbolStroke", "theme", "fill", "rangeSelector", "buttonTheme", "stroke", "states", "hover", "select", "inputBoxBorderColor", "inputStyle", "labelStyle", "navigator", "handles", "borderColor", "outlineColor", "maskFill", "scrollbar", "barBackgroundColor", "barBorderColor", "buttonArrowColor", "buttonBackgroundColor", "buttonBorderColor", "rifleColor", "trackBackgroundColor", "trackBorderColor", "apply", "href", "rel", "type", "document", "getElementsByTagName", "DarkUnica"], "mappings": "CASA,AATA;;;;;;;;CAQC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,EACrC,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,+BAAgC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAM,GACjG,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,+BAA+B,CAAGD,EAAQD,EAAK,WAAc,EAErEA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CACjD,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,AAACC,GACnC,AAAC,CAAA,KACP,aACA,IAkGNC,EAlGUC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAerH,GAAM,CAAEE,WAAAA,CAAU,CAAE,CAAID,IAElB,CAAEE,cAAAA,CAAa,CAAE,CAAIF,KAO3B,AAAC,SAAU3B,CAAc,EAMrBA,EAAe8B,OAAO,CAAG,CACrBC,OAAQ,CACJ,UAAW,UAAW,UAAW,UAAW,UAAW,UACvD,UAAW,UAAW,UAAW,UAAW,UAC/C,CACDC,MAAO,CACHC,gBAAiB,CACbC,eAAgB,CAAEC,GAAI,EAAGC,GAAI,EAAGC,GAAI,EAAGC,GAAI,CAAE,EAC7CC,MAAO,CACH,CAAC,EAAG,UAAU,CACd,CAAC,EAAG,UAAU,CACjB,AACL,EACAC,MAAO,CACHC,WAAY,yBAChB,EACAC,gBAAiB,SACrB,EACAC,MAAO,CACHH,MAAO,CACHI,MAAO,UACPC,cAAe,YACfC,SAAU,MACd,CACJ,EACAC,SAAU,CACNP,MAAO,CACHI,MAAO,UACPC,cAAe,WACnB,CACJ,EACAG,MAAO,CACHC,cAAe,UACfC,OAAQ,CACJV,MAAO,CACHI,MAAO,SACX,CACJ,EACAO,UAAW,UACXC,mBAAoB,UACpBC,UAAW,UACXV,MAAO,CACHH,MAAO,CACHI,MAAO,SACX,CACJ,CACJ,EACAU,MAAO,CACHL,cAAe,UACfC,OAAQ,CACJV,MAAO,CACHI,MAAO,SACX,CACJ,EACAO,UAAW,UACXC,mBAAoB,UACpBC,UAAW,UACXE,UAAW,EACXZ,MAAO,CACHH,MAAO,CACHI,MAAO,SACX,CACJ,CACJ,EACAY,QAAS,CACLvB,gBAAiB,sBACjBO,MAAO,CACHI,MAAO,SACX,CACJ,EACAa,YAAa,CACTC,OAAQ,CACJC,WAAY,CACRf,MAAO,UACPJ,MAAO,CACHM,SAAU,MACd,CACJ,EACAc,OAAQ,CACJT,UAAW,MACf,CACJ,EACAU,QAAS,CACLC,UAAW,SACf,EACAC,YAAa,CACTZ,UAAW,OACf,EACAa,SAAU,CACNpB,MAAO,OACX,CACJ,EACAqB,OAAQ,CACJhC,gBAAiB,qBACjBiC,UAAW,CACPtB,MAAO,SACX,EACAuB,eAAgB,CACZvB,MAAO,MACX,EACAwB,gBAAiB,CACbxB,MAAO,SACX,EACAD,MAAO,CACHH,MAAO,CACHI,MAAO,SACX,CACJ,CACJ,EACAyB,QAAS,CACL7B,MAAO,CACHI,MAAO,MACX,CACJ,EACA0B,UAAW,CACPC,qBAAsB,CAClB3B,MAAO,SACX,EACA4B,qBAAsB,CAClB5B,MAAO,SACX,CACJ,EACA6B,WAAY,CACRC,cAAe,CACXC,aAAc,UACdC,MAAO,CACHC,KAAM,SACV,CACJ,CACJ,EAEAC,cAAe,CACXC,YAAa,CACTF,KAAM,UACNG,OAAQ,UACRxC,MAAO,CACHI,MAAO,MACX,EACAqC,OAAQ,CACJC,MAAO,CACHL,KAAM,UACNG,OAAQ,UACRxC,MAAO,CACHI,MAAO,OACX,CACJ,EACAuC,OAAQ,CACJN,KAAM,UACNG,OAAQ,UACRxC,MAAO,CACHI,MAAO,OACX,CACJ,CACJ,CACJ,EACAwC,oBAAqB,UACrBC,WAAY,CACRpD,gBAAiB,OACjBW,MAAO,QACX,EACA0C,WAAY,CACR1C,MAAO,QACX,CACJ,EACA2C,UAAW,CACPC,QAAS,CACLvD,gBAAiB,OACjBwD,YAAa,MACjB,EACAC,aAAc,OACdC,SAAU,wBACVjC,OAAQ,CACJd,MAAO,UACPO,UAAW,SACf,EACAH,MAAO,CACHC,cAAe,SACnB,CACJ,EACA2C,UAAW,CACPC,mBAAoB,UACpBC,eAAgB,UAChBC,iBAAkB,OAClBC,sBAAuB,UACvBC,kBAAmB,UACnBC,WAAY,OACZC,qBAAsB,UACtBC,iBAAkB,SACtB,CACJ,EAmBApG,EAAeqG,KAAK,CAVpB,WAEIxE,EAAc,OAAQ,CAClByE,KAAM,oDACNC,IAAK,aACLC,KAAM,UACV,EAAG,KAAMC,SAASC,oBAAoB,CAAC,OAAO,CAAC,EAAE,EAEjD9E,EAAW5B,EAAe8B,OAAO,CACrC,CAEJ,EAAG9B,GAAmBA,CAAAA,EAAiB,CAAC,CAAA,GAMX,IAAM2G,EAAa3G,CAOhD,CAAC2B,IAA+EiD,KAAK,CAAG+B,EAAU7E,OAAO,CACzG6E,EAAUN,KAAK,GACc,IAAM5E,EAAmBE,IAG5C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}