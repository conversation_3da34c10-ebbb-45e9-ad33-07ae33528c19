!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("jspdf")):"function"==typeof define&&define.amd?define(["exports","jspdf"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).svg2pdf={},t.jspdf)}(this,(function(t,e){"use strict";var r=function(t,e){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},r(t,e)};function i(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}var n=function(){return n=Object.assign||function(t){for(var e,r=1,i=arguments.length;r<i;r++)for(var n in e=arguments[r])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t},n.apply(this,arguments)};function a(t,e,r,i){return new(r||(r=Promise))((function(n,a){function s(t){try{l(i.next(t))}catch(t){a(t)}}function o(t){try{l(i.throw(t))}catch(t){a(t)}}function l(t){var e;t.done?n(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(s,o)}l((i=i.apply(t,e||[])).next())}))}function s(t,e){var r,i,n,a,s={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return a={next:o(0),throw:o(1),return:o(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function o(a){return function(o){return function(a){if(r)throw new TypeError("Generator is already executing.");for(;s;)try{if(r=1,i&&(n=2&a[0]?i.return:a[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,a[1])).done)return n;switch(i=0,n&&(a=[2&a[0],n.value]),a[0]){case 0:case 1:n=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,i=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(n=s.trys,(n=n.length>0&&n[n.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!n||a[1]>n[0]&&a[1]<n[3])){s.label=a[1];break}if(6===a[0]&&s.label<n[1]){s.label=n[1],n=a;break}if(n&&s.label<n[2]){s.label=n[2],s.ops.push(a);break}n[2]&&s.ops.pop(),s.trys.pop();continue}a=e.call(t,s)}catch(t){a=[6,t],i=0}finally{r=n=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,o])}}}var o,l,u=function(){function t(t){if(this.a=void 0,this.r=0,this.g=0,this.b=0,this.simpleColors={},this.colorDefs=[],this.ok=!1,t){for(var e in"#"==t.charAt(0)&&(t=t.substr(1,6)),t=(t=t.replace(/ /g,"")).toLowerCase(),this.simpleColors={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgrey:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",grey:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightslategrey:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"},this.simpleColors)t==e&&(t=this.simpleColors[e]);this.colorDefs=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3])]}},{re:/^rgb\(([0-9.]+)%,\s*([0-9.]+)%,\s*([0-9.]+)%\)$/,example:["rgb(50.5%, 25.75%, 75.5%)","rgb(100%,0%,0%)"],process:function(t){return[Math.round(2.55*parseFloat(t[1])),Math.round(2.55*parseFloat(t[2])),Math.round(2.55*parseFloat(t[3]))]}},{re:/^(\w{2})(\w{2})(\w{2})$/,example:["#00ff00","336699"],process:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,example:["#fb0","f0f"],process:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}}];for(var r=0;r<this.colorDefs.length;r++){var i=this.colorDefs[r].re,n=this.colorDefs[r].process,a=i.exec(t);if(a){var s=n(a);this.r=s[0],this.g=s[1],this.b=s[2],this.ok=!0}}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b}}return t.prototype.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},t.prototype.toRGBA=function(){return"rgba("+this.r+", "+this.g+", "+this.b+", "+(this.a||"1")+")"},t.prototype.toHex=function(){var t=this.r.toString(16),e=this.g.toString(16),r=this.b.toString(16);return 1==t.length&&(t="0"+t),1==e.length&&(e="0"+e),1==r.length&&(r="0"+r),"#"+t+e+r},t.prototype.getHelpXML=function(){for(var e=[],r=0;r<this.colorDefs.length;r++)for(var i=this.colorDefs[r].example,n=0;n<i.length;n++)e[e.length]=i[n];for(var a in this.simpleColors)e[e.length]=a;var s=document.createElement("ul");s.setAttribute("id","rgbcolor-examples");for(r=0;r<e.length;r++)try{var o=document.createElement("li"),l=new t(e[r]),u=document.createElement("div");u.style.cssText="margin: 3px; border: 1px solid black; background:"+l.toHex()+"; color:"+l.toHex(),u.appendChild(document.createTextNode("test"));var h=document.createTextNode(" "+e[r]+" -> "+l.toRGB()+" -> "+l.toHex());o.appendChild(u),o.appendChild(h),s.appendChild(o)}catch(t){}return s},t}(),h=function(){function t(t){this.color=t}return t.prototype.getFillData=function(t,e){return a(this,void 0,void 0,(function(){return s(this,(function(t){return[2,void 0]}))}))},t}(),c=function(){function t(){this.xmlSpace="",this.fill=null,this.fillOpacity=1,this.fontFamily="",this.fontSize=16,this.fontStyle="",this.fontWeight="",this.opacity=1,this.stroke=null,this.strokeDasharray=null,this.strokeDashoffset=0,this.strokeLinecap="",this.strokeLinejoin="",this.strokeMiterlimit=4,this.strokeOpacity=1,this.strokeWidth=1,this.alignmentBaseline="",this.textAnchor="",this.visibility="",this.color=null,this.contextFill=null,this.contextStroke=null,this.fillRule=null}return t.prototype.clone=function(){var e=new t;return e.xmlSpace=this.xmlSpace,e.fill=this.fill,e.fillOpacity=this.fillOpacity,e.fontFamily=this.fontFamily,e.fontSize=this.fontSize,e.fontStyle=this.fontStyle,e.fontWeight=this.fontWeight,e.opacity=this.opacity,e.stroke=this.stroke,e.strokeDasharray=this.strokeDasharray,e.strokeDashoffset=this.strokeDashoffset,e.strokeLinecap=this.strokeLinecap,e.strokeLinejoin=this.strokeLinejoin,e.strokeMiterlimit=this.strokeMiterlimit,e.strokeOpacity=this.strokeOpacity,e.strokeWidth=this.strokeWidth,e.textAnchor=this.textAnchor,e.alignmentBaseline=this.alignmentBaseline,e.visibility=this.visibility,e.color=this.color,e.fillRule=this.fillRule,e.contextFill=this.contextFill,e.contextStroke=this.contextStroke,e},t.default=function(){var e=new t;return e.xmlSpace="default",e.fill=new h(new u("rgb(0, 0, 0)")),e.fillOpacity=1,e.fontFamily="times",e.fontSize=16,e.fontStyle="normal",e.fontWeight="normal",e.opacity=1,e.stroke=null,e.strokeDasharray=null,e.strokeDashoffset=0,e.strokeLinecap="butt",e.strokeLinejoin="miter",e.strokeMiterlimit=4,e.strokeOpacity=1,e.strokeWidth=1,e.alignmentBaseline="baseline",e.textAnchor="start",e.visibility="visible",e.color=new u("rgb(0, 0, 0)"),e.fillRule="nonzero",e.contextFill=null,e.contextStroke=null,e},t.getContextColors=function(t,e){void 0===e&&(e=!1);var r={};return t.attributeState.contextFill&&(r.contextFill=t.attributeState.contextFill),t.attributeState.contextStroke&&(r.contextStroke=t.attributeState.contextStroke),e&&t.attributeState.color&&(r.color=t.attributeState.color),r},t}(),f=function(){function t(t,e){var r,i,n;this.pdf=t,this.svg2pdfParameters=e.svg2pdfParameters,this.attributeState=e.attributeState?e.attributeState.clone():c.default(),this.viewport=e.viewport,this.refsHandler=e.refsHandler,this.styleSheets=e.styleSheets,this.textMeasure=e.textMeasure,this.transform=null!==(r=e.transform)&&void 0!==r?r:this.pdf.unitMatrix,this.withinClipPath=null!==(i=e.withinClipPath)&&void 0!==i&&i,this.withinUse=null!==(n=e.withinUse)&&void 0!==n&&n}return t.prototype.clone=function(e){var r,i,n,a;return void 0===e&&(e={}),new t(this.pdf,{svg2pdfParameters:this.svg2pdfParameters,attributeState:e.attributeState?e.attributeState.clone():this.attributeState.clone(),viewport:null!==(r=e.viewport)&&void 0!==r?r:this.viewport,refsHandler:this.refsHandler,styleSheets:this.styleSheets,textMeasure:this.textMeasure,transform:null!==(i=e.transform)&&void 0!==i?i:this.transform,withinClipPath:null!==(n=e.withinClipPath)&&void 0!==n?n:this.withinClipPath,withinUse:null!==(a=e.withinUse)&&void 0!==a?a:this.withinUse})},t}();function p(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}
/*! https://mths.be/cssesc v3.0.0 by @mathias */var d=function(){if(l)return o;l=1;var t={}.hasOwnProperty,e=/[ -,\.\/:-@\[-\^`\{-~]/,r=/[ -,\.\/:-@\[\]\^`\{-~]/,i=/(^|\\+)?(\\[A-F0-9]{1,6})\x20(?![a-fA-F0-9\x20])/g,n=function n(a,s){"single"!=(s=function(e,r){if(!e)return r;var i={};for(var n in r)i[n]=t.call(e,n)?e[n]:r[n];return i}(s,n.options)).quotes&&"double"!=s.quotes&&(s.quotes="single");for(var o="double"==s.quotes?'"':"'",l=s.isIdentifier,u=a.charAt(0),h="",c=0,f=a.length;c<f;){var p=a.charAt(c++),d=p.charCodeAt(),g=void 0;if(d<32||d>126){if(d>=55296&&d<=56319&&c<f){var m=a.charCodeAt(c++);56320==(64512&m)?d=((1023&d)<<10)+(1023&m)+65536:c--}g="\\"+d.toString(16).toUpperCase()+" "}else g=s.escapeEverything?e.test(p)?"\\"+p:"\\"+d.toString(16).toUpperCase()+" ":/[\t\n\f\r\x0B]/.test(p)?"\\"+d.toString(16).toUpperCase()+" ":"\\"==p||!l&&('"'==p&&o==p||"'"==p&&o==p)||l&&r.test(p)?"\\"+p:p;h+=g}return l&&(/^-[-\d]/.test(h)?h="\\-"+h.slice(1):/\d/.test(u)&&(h="\\3"+u+" "+h.slice(1))),h=h.replace(i,(function(t,e,r){return e&&e.length%2?t:(e||"")+r})),!l&&s.wrap?o+h+o:h};return n.options={escapeEverything:!1,isIdentifier:!1,quotes:"single",wrap:!1},n.version="3.0.0",o=n}(),g=p(d),m=function(){function t(e){this.renderedElements={},this.idMap=e,this.idPrefix=String(t.instanceCounter++)}return t.prototype.getRendered=function(t,e,r){return a(this,void 0,void 0,(function(){var i,n;return s(this,(function(a){switch(a.label){case 0:return i=this.generateKey(t,e),this.renderedElements.hasOwnProperty(i)?[2,this.renderedElements[t]]:(n=this.get(t),this.renderedElements[i]=n,[4,r(n)]);case 1:return a.sent(),[2,n]}}))}))},t.prototype.get=function(t){return this.idMap[g(t,{isIdentifier:!0})]},t.prototype.generateKey=function(t,e){var r="";return e&&(r=["color","contextFill","contextStroke"].map((function(t){var r,i;return null!==(i=null===(r=e[t])||void 0===r?void 0:r.toRGBA())&&void 0!==i?i:""})).join("|")),this.idPrefix+"|"+t+"|"+r},t.instanceCounter=0,t}();function y(t,e){return Math.atan2(e[1]-t[1],e[0]-t[0])}var v=2/3;function b(t,e){return[v*(e[0]-t[0])+t[0],v*(e[1]-t[1])+t[1]]}function x(t){var e=Math.sqrt(t[0]*t[0]+t[1]*t[1]);return[t[0]/e,t[1]/e]}function S(t,e){return x([e[0]-t[0],e[1]-t[1]])}function w(t,e){return[t[0]+e[0],t[1]+e[1]]}function k(t,e){var r=t[0],i=t[1];return[e.a*r+e.c*i+e.e,e.b*r+e.d*i+e.f]}var M=function(){function t(){this.segments=[]}return t.prototype.moveTo=function(t,e){return this.segments.push(new C(t,e)),this},t.prototype.lineTo=function(t,e){return this.segments.push(new F(t,e)),this},t.prototype.curveTo=function(t,e,r,i,n,a){return this.segments.push(new A(t,e,r,i,n,a)),this},t.prototype.close=function(){return this.segments.push(new T),this},t.prototype.transform=function(t){this.segments.forEach((function(e){if(e instanceof C||e instanceof F||e instanceof A){var r=k([e.x,e.y],t);e.x=r[0],e.y=r[1]}if(e instanceof A){var i=k([e.x1,e.y1],t),n=k([e.x2,e.y2],t);e.x1=i[0],e.y1=i[1],e.x2=n[0],e.y2=n[1]}}))},t.prototype.draw=function(t){var e=t.pdf;this.segments.forEach((function(t){t instanceof C?e.moveTo(t.x,t.y):t instanceof F?e.lineTo(t.x,t.y):t instanceof A?e.curveTo(t.x1,t.y1,t.x2,t.y2,t.x,t.y):e.close()}))},t}(),C=function(t,e){this.x=t,this.y=e},F=function(t,e){this.x=t,this.y=e},A=function(t,e,r,i,n,a){this.x1=t,this.y1=e,this.x2=r,this.y2=i,this.x=n,this.y=a},T=function(){};function P(t,e){return e.split(",").indexOf((t.nodeName||t.tagName).toLowerCase())>=0}function B(t,e,r,i){var n;void 0===i&&(i=r);var a=null===(n=t.style)||void 0===n?void 0:n.getPropertyValue(i);if(a)return a;var s=e.getPropertyValue(t,i);return s||(t.hasAttribute(r)&&t.getAttribute(r)||void 0)}function N(t,e,r){if("none"===B(t.element,r.styleSheets,"display"))return!1;var i=e,n=B(t.element,r.styleSheets,"visibility");return n&&(i="hidden"!==n),i}function E(t,e,r){var i=N(t,e,r);return 0!==t.element.childNodes.length&&(t.children.forEach((function(t){t.isVisible(i,r)&&(i=!0)})),i)}var O,L,I=function(){function t(){this.markers=[]}return t.prototype.addMarker=function(t){this.markers.push(t)},t.prototype.draw=function(t){return a(this,void 0,void 0,(function(){var e,r,i,n,a,o,l,u;return s(this,(function(s){switch(s.label){case 0:e=0,s.label=1;case 1:return e<this.markers.length?(r=this.markers[e],i=void 0,n=r.angle,a=r.anchor,o=Math.cos(n),l=Math.sin(n),i=t.pdf.Matrix(o,l,-l,o,a[0],a[1]),i=t.pdf.matrixMult(t.pdf.Matrix(t.attributeState.strokeWidth,0,0,t.attributeState.strokeWidth,0,0),i),i=t.pdf.matrixMult(i,t.transform),t.pdf.saveGraphicsState(),u=c.getContextColors(t),[4,t.refsHandler.getRendered(r.id,u,(function(e){return e.apply(t)}))]):[3,4];case 2:s.sent(),t.pdf.doFormObject(t.refsHandler.generateKey(r.id,u),i),t.pdf.restoreGraphicsState(),s.label=3;case 3:return e++,[3,1];case 4:return[2]}}))}))},t}(),_=function(t,e,r,i){void 0===i&&(i=!1),this.id=t,this.anchor=e,this.angle=r,this.isStartMarker=i},H=/url\(["']?#([^"']+)["']?\)/,R={bottom:"bottom","text-bottom":"bottom",top:"top","text-top":"top",hanging:"hanging",middle:"middle",central:"middle",center:"middle",mathematical:"middle",ideographic:"ideographic",alphabetic:"alphabetic",baseline:"alphabetic"},D="http://www.w3.org/2000/svg";function j(t,e){var r;return(r=t&&t.toString().match(/^([\-0-9.]+)em$/))?parseFloat(r[1])*e:(r=t&&t.toString().match(/^([\-0-9.]+)(px|)$/))?parseFloat(r[1]):0}function q(t){return R[t]||"alphabetic"}function V(t){for(var e,r=[],i=/[+-]?(?:(?:\d+\.?\d*)|(?:\d*\.?\d+))(?:[eE][+-]?\d+)?/g;e=i.exec(t);)r.push(parseFloat(e[0]));return r}function W(t,e){if("transparent"===t){var r=new u("rgb(0,0,0)");return r.a=0,r}if(e&&"currentcolor"===t.toLowerCase())return e.color||new u("rgb(0,0,0)");if(e&&"context-stroke"===t.toLowerCase())return e.contextStroke||new u("rgb(0,0,0)");if(e&&"context-fill"===t.toLowerCase())return e.contextFill||new u("rgb(0,0,0)");var i=/\s*rgba\(((?:[^,\)]*,){3}[^,\)]*)\)\s*/.exec(t);if(i){var n=V(i[1]),a=new u("rgb("+n.slice(0,3).join(",")+")");return a.a=n[3],a}return new u(t)}var G=p(function(){if(L)return O;L=1;var t=/[a-z0-9_-]/i,e=/[\s\t]/,r=/[^a-z0-9_-]/i;return O={parse:function(r){for(var i,n,a=!0,s=0,o="",l=0,u=[];;){if(n=r[l],0===s){if(!n&&a)break;if(!n&&!a)throw new Error("Parse error");if('"'===n||"'"===n)i=n,s=1,a=!1;else if(e.test(n));else{if(!t.test(n))throw new Error("Parse error");s=3,a=!1,l--}}else if(1===s){if(!n)throw new Error("Parse Error");"\\"===n?s=2:n===i?(u.push(o),o="",s=4):o+=n}else if(2===s){if(n!==i&&"\\"!==n)throw new Error("Parse error");o+=n,s=1}else if(3===s){if(!n){u.push(o);break}if(t.test(n))o+=n;else if(","===n)u.push(o),o="",s=0;else{if(!e.test(n))throw new Error("Parse error");s=5}}else if(5===s){if(!n){u.push(o);break}if(t.test(n))o+=" "+n,s=3;else if(","===n)u.push(o),o="",s=0;else if(!e.test(n))throw new Error("Parse error")}else if(4===s){if(!n)break;if(","===n)s=0;else if(!e.test(n))throw new Error("Parse error")}l++}return u},stringify:function(t,e){var i=e&&e.quote||'"';if('"'!==i&&"'"!==i)throw new Error("Quote must be `'` or `\"`");for(var n=new RegExp(i,"g"),a=[],s=0;s<t.length;++s){var o=t[s];r.test(o)&&(o=i+(o=o.replace(/\\/g,"\\\\").replace(n,"\\"+i))+i),a.push(o)}return a.join(", ")}}}()),U={"sans-serif":"helvetica",verdana:"helvetica",arial:"helvetica",fixed:"courier",monospace:"courier",terminal:"courier",serif:"times",cursive:"times",fantasy:"times"};var z,Y=(z=e.jsPDF.version.split("."),2===parseFloat(z[0])&&3===parseFloat(z[1]));function X(t,e){return Y?400==e?"italic"==t?"italic":"normal":700==e&&"italic"!==t?"bold":t+""+e:400==e||"normal"===e?"italic"===t?"italic":"normal":700!=e&&"bold"!==e||"normal"!==t?(700==e?"bold":e)+""+t:"bold"}function Q(t,e){if("none"===B(e.element,t.styleSheets,"display"))return[0,0,0,0];var r=[];return e.children.forEach((function(e){var i=e.getBoundingBox(t);if(0!==i[0]||0!==i[1]||0!==i[2]||0!==i[3]){var n=e.computeNodeTransform(t);i[0]=i[0]*n.sx+n.tx,i[1]=i[1]*n.sy+n.ty,i[2]=i[2]*n.sx,i[3]=i[3]*n.sy,r=0===r.length?i:[Math.min(r[0],i[0]),Math.min(r[1],i[1]),Math.max(r[0]+r[2],i[0]+i[2])-Math.min(r[0],i[0]),Math.max(r[1]+r[3],i[1]+i[3])-Math.min(r[1],i[1])]}})),0===r.length?[0,0,0,0]:r}function $(t,e){var r=parseFloat,i=r(t.getAttribute("x1"))||r(B(t,e.styleSheets,"x"))||r(B(t,e.styleSheets,"cx"))-r(B(t,e.styleSheets,"r"))||0,n=r(t.getAttribute("x2"))||i+r(B(t,e.styleSheets,"width"))||r(B(t,e.styleSheets,"cx"))+r(B(t,e.styleSheets,"r"))||0,a=r(t.getAttribute("y1"))||r(B(t,e.styleSheets,"y"))||r(B(t,e.styleSheets,"cy"))-r(B(t,e.styleSheets,"r"))||0,s=r(t.getAttribute("y2"))||a+r(B(t,e.styleSheets,"height"))||r(B(t,e.styleSheets,"cy"))+r(B(t,e.styleSheets,"r"))||0;return[Math.min(i,n),Math.min(a,s),Math.max(i,n)-Math.min(i,n),Math.max(a,s)-Math.min(a,s)]}function K(t,e,r,i,n,a,s,o){void 0===o&&(o=!1);var l,u,h=e[0],c=e[1],f=e[2],p=e[3],d=n/f,g=a/p,m=t.getAttribute("preserveAspectRatio");if(m){var y=m.split(" ");"defer"===y[0]&&(y=y.slice(1)),l=y[0],u=y[1]||"meet"}else l="xMidYMid",u="meet";if("none"!==l&&("meet"===u?d=g=Math.min(d,g):"slice"===u&&(d=g=Math.max(d,g))),o)return s.pdf.Matrix(d,0,0,g,0,0);var v=r-h*d,b=i-c*g;l.indexOf("xMid")>=0?v+=(n-f*d)/2:l.indexOf("xMax")>=0&&(v+=n-f*d),l.indexOf("YMid")>=0?b+=(a-p*g)/2:l.indexOf("YMax")>=0&&(b+=a-p*g);var x=s.pdf.Matrix(1,0,0,1,v,b),S=s.pdf.Matrix(d,0,0,g,0,0);return s.pdf.matrixMult(S,x)}function Z(t,e){if(!t||"none"===t)return e.pdf.unitMatrix;for(var r,i,n=/^[\s,]*matrix\(([^)]+)\)\s*/,a=/^[\s,]*translate\(([^)]+)\)\s*/,s=/^[\s,]*rotate\(([^)]+)\)\s*/,o=/^[\s,]*scale\(([^)]+)\)\s*/,l=/^[\s,]*skewX\(([^)]+)\)\s*/,u=/^[\s,]*skewY\(([^)]+)\)\s*/,h=e.pdf.unitMatrix;t.length>0&&t.length!==i;){i=t.length;var c=n.exec(t);if(c&&(r=V(c[1]),h=e.pdf.matrixMult(e.pdf.Matrix(r[0],r[1],r[2],r[3],r[4],r[5]),h),t=t.substr(c[0].length)),c=s.exec(t)){r=V(c[1]);var f=Math.PI*r[0]/180;if(h=e.pdf.matrixMult(e.pdf.Matrix(Math.cos(f),Math.sin(f),-Math.sin(f),Math.cos(f),0,0),h),r[1]||r[2]){var p=e.pdf.Matrix(1,0,0,1,r[1],r[2]),d=e.pdf.Matrix(1,0,0,1,-r[1],-r[2]);h=e.pdf.matrixMult(d,e.pdf.matrixMult(h,p))}t=t.substr(c[0].length)}(c=a.exec(t))&&(r=V(c[1]),h=e.pdf.matrixMult(e.pdf.Matrix(1,0,0,1,r[0],r[1]||0),h),t=t.substr(c[0].length)),(c=o.exec(t))&&((r=V(c[1]))[1]||(r[1]=r[0]),h=e.pdf.matrixMult(e.pdf.Matrix(r[0],0,0,r[1],0,0),h),t=t.substr(c[0].length)),(c=l.exec(t))&&(r=parseFloat(c[1]),r*=Math.PI/180,h=e.pdf.matrixMult(e.pdf.Matrix(1,0,Math.tan(r),1,0,0),h),t=t.substr(c[0].length)),(c=u.exec(t))&&(r=parseFloat(c[1]),r*=Math.PI/180,h=e.pdf.matrixMult(e.pdf.Matrix(1,Math.tan(r),0,1,0,0),h),t=t.substr(c[0].length))}return h}var J=function(){function t(t,e){this.element=t,this.children=e,this.parent=null}return t.prototype.setParent=function(t){this.parent=t},t.prototype.getParent=function(){return this.parent},t.prototype.getBoundingBox=function(t){return"none"===B(this.element,t.styleSheets,"display")?[0,0,0,0]:this.getBoundingBoxCore(t)},t.prototype.computeNodeTransform=function(t){var e=this.computeNodeTransformCore(t),r=B(this.element,t.styleSheets,"transform");return r?t.pdf.matrixMult(e,Z(r,t)):e},t}(),tt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.render=function(t){return Promise.resolve()},e.prototype.getBoundingBoxCore=function(t){return[]},e.prototype.computeNodeTransformCore=function(t){return t.pdf.unitMatrix},e}(J),et=function(t){function r(e,r,i){var n=t.call(this,r,i)||this;return n.pdfGradientType=e,n.contextColor=void 0,n}return i(r,t),r.prototype.apply=function(t){return a(this,void 0,void 0,(function(){var r,i,n,a,o,l;return s(this,(function(s){return(r=this.element.getAttribute("id"))?(i=this.getStops(t.styleSheets),n=0,a=!1,i.forEach((function(t){var e=t.opacity;e&&1!==e&&(n+=e,a=!0)})),a&&(o=new e.GState({opacity:n/i.length})),l=new e.ShadingPattern(this.pdfGradientType,this.getCoordinates(),i,o),t.pdf.addShadingPattern(r,l),[2]):[2]}))}))},r.prototype.getStops=function(t){var e=this;if(this.stops)return this.stops;if(void 0===this.contextColor){this.contextColor=null;for(var i=this;i;){var n=B(i.element,t,"color");if(n){this.contextColor=W(n,null);break}i=i.getParent()}}var a=[];return this.children.forEach((function(i){if("stop"===i.element.tagName.toLowerCase()){var n=B(i.element,t,"color"),s=W(B(i.element,t,"stop-color")||"",n?{color:W(n,null)}:{color:e.contextColor}),o=parseFloat(B(i.element,t,"stop-opacity")||"1");a.push({offset:r.parseGradientOffset(i.element.getAttribute("offset")||"0"),color:[s.r,s.g,s.b],opacity:o})}})),this.stops=a},r.prototype.getBoundingBoxCore=function(t){return $(this.element,t)},r.prototype.computeNodeTransformCore=function(t){return t.pdf.unitMatrix},r.prototype.isVisible=function(t,e){return E(this,t,e)},r.parseGradientOffset=function(t){var e=parseFloat(t);return!isNaN(e)&&t.indexOf("%")>=0?e/100:e},r}(tt),rt=function(t){function e(e,r){return t.call(this,"axial",e,r)||this}return i(e,t),e.prototype.getCoordinates=function(){return[parseFloat(this.element.getAttribute("x1")||"0"),parseFloat(this.element.getAttribute("y1")||"0"),parseFloat(this.element.getAttribute("x2")||"1"),parseFloat(this.element.getAttribute("y2")||"0")]},e}(et),it=function(t){function e(e,r){return t.call(this,"radial",e,r)||this}return i(e,t),e.prototype.getCoordinates=function(){var t=this.element.getAttribute("cx"),e=this.element.getAttribute("cy"),r=this.element.getAttribute("fx"),i=this.element.getAttribute("fy");return[parseFloat(r||t||"0.5"),parseFloat(i||e||"0.5"),0,parseFloat(t||"0.5"),parseFloat(e||"0.5"),parseFloat(this.element.getAttribute("r")||"0.5")]},e}(et),nt=function(){function t(t,e){this.key=t,this.gradient=e}return t.prototype.getFillData=function(t,e){return a(this,void 0,void 0,(function(){var r,i,n;return s(this,(function(a){switch(a.label){case 0:return[4,e.refsHandler.getRendered(this.key,null,(function(t){return t.apply(new f(e.pdf,{refsHandler:e.refsHandler,textMeasure:e.textMeasure,styleSheets:e.styleSheets,viewport:e.viewport,svg2pdfParameters:e.svg2pdfParameters}))}))];case 1:return a.sent(),this.gradient.element.hasAttribute("gradientUnits")&&"objectboundingbox"!==this.gradient.element.getAttribute("gradientUnits").toLowerCase()?r=e.pdf.unitMatrix:(i=t.getBoundingBox(e),r=e.pdf.Matrix(i[2],0,0,i[3],i[0],i[1])),n=Z(B(this.gradient.element,e.styleSheets,"gradientTransform","transform"),e),[2,{key:this.key,matrix:e.pdf.matrixMult(n,r)}]}}))}))},t}(),at=function(t){function r(){return null!==t&&t.apply(this,arguments)||this}return i(r,t),r.prototype.apply=function(t){return a(this,void 0,void 0,(function(){var r,i,n,a,o;return s(this,(function(s){switch(s.label){case 0:if(!(r=this.element.getAttribute("id")))return[2];i=this.getBoundingBox(t),n=new e.TilingPattern([i[0],i[1],i[0]+i[2],i[1]+i[3]],i[2],i[3]),t.pdf.beginTilingPattern(n),a=0,o=this.children,s.label=1;case 1:return a<o.length?[4,o[a].render(new f(t.pdf,{attributeState:t.attributeState,refsHandler:t.refsHandler,styleSheets:t.styleSheets,viewport:t.viewport,svg2pdfParameters:t.svg2pdfParameters,textMeasure:t.textMeasure}))]:[3,4];case 2:s.sent(),s.label=3;case 3:return a++,[3,1];case 4:return t.pdf.endTilingPattern(r,n),[2]}}))}))},r.prototype.getBoundingBoxCore=function(t){return $(this.element,t)},r.prototype.computeNodeTransformCore=function(t){return t.pdf.unitMatrix},r.prototype.isVisible=function(t,e){return E(this,t,e)},r}(tt),st=function(){function t(t,e){this.key=t,this.pattern=e}return t.prototype.getFillData=function(t,e){return a(this,void 0,void 0,(function(){var r,i,n,a,o,l,u,h,c,p,d,g;return s(this,(function(s){switch(s.label){case 0:return[4,e.refsHandler.getRendered(this.key,null,(function(t){return t.apply(new f(e.pdf,{refsHandler:e.refsHandler,textMeasure:e.textMeasure,styleSheets:e.styleSheets,viewport:e.viewport,svg2pdfParameters:e.svg2pdfParameters}))}))];case 1:return s.sent(),r={key:this.key,boundingBox:void 0,xStep:0,yStep:0,matrix:void 0},n=e.pdf.unitMatrix,this.pattern.element.hasAttribute("patternUnits")&&"objectboundingbox"!==this.pattern.element.getAttribute("patternUnits").toLowerCase()||(i=t.getBoundingBox(e),n=e.pdf.Matrix(1,0,0,1,i[0],i[1]),o=this.pattern.getBoundingBox(e),l=o[0]*i[0]||0,u=o[1]*i[1]||0,h=o[2]*i[2]||0,c=o[3]*i[3]||0,r.boundingBox=[l,u,l+h,u+c],r.xStep=h,r.yStep=c),a=e.pdf.unitMatrix,this.pattern.element.hasAttribute("patternContentUnits")&&"objectboundingbox"===this.pattern.element.getAttribute("patternContentUnits").toLowerCase()&&(i||(i=t.getBoundingBox(e)),a=e.pdf.Matrix(i[2],0,0,i[3],0,0),o=r.boundingBox||this.pattern.getBoundingBox(e),l=o[0]/i[0]||0,u=o[1]/i[1]||0,h=o[2]/i[2]||0,c=o[3]/i[3]||0,r.boundingBox=[l,u,l+h,u+c],r.xStep=h,r.yStep=c),p=e.pdf.unitMatrix,(d=B(this.pattern.element,e.styleSheets,"patternTransform","transform"))&&(p=Z(d,e)),g=a,g=e.pdf.matrixMult(g,n),g=e.pdf.matrixMult(g,p),g=e.pdf.matrixMult(g,e.transform),r.matrix=g,[2,r]}}))}))},t}();function ot(t,e){var r=H.exec(t);if(r){var i=r[1],n=e.refsHandler.get(i);return n&&(n instanceof rt||n instanceof it)?function(t,e,r){var i=e.getStops(r.styleSheets);if(0===i.length)return null;if(1===i.length){var n=i[0].color,a=new u;return a.ok=!0,a.r=n[0],a.g=n[1],a.b=n[2],a.a=i[0].opacity,new h(a)}return new nt(t,e)}(i,n,e):n&&n instanceof at?new st(i,n):new h(new u("rgb(0, 0, 0)"))}var a=W(t,e.attributeState);return a.ok?new h(a):null}function lt(t,e,r){var i=r||e.element,n=B(i,t.styleSheets,"color");if(n){var a=W(n,t.attributeState);a.ok?t.attributeState.color=a:t.attributeState.color=new u("rgb(0,0,0)")}var s=B(i,t.styleSheets,"visibility");s&&(t.attributeState.visibility=s);var o=B(i,t.styleSheets,"fill");o&&(t.attributeState.fill=ot(o,t));var l=B(i,t.styleSheets,"fill-opacity");l&&(t.attributeState.fillOpacity=parseFloat(l));var c=B(i,t.styleSheets,"stroke-opacity");c&&(t.attributeState.strokeOpacity=parseFloat(c));var f=B(i,t.styleSheets,"opacity");f&&(t.attributeState.opacity=parseFloat(f));var p=B(i,t.styleSheets,"stroke-width");void 0!==p&&""!==p&&(t.attributeState.strokeWidth=Math.abs(parseFloat(p)));var d=B(i,t.styleSheets,"stroke");if(d)if("none"===d)t.attributeState.stroke=null;else{var g=W(d,t.attributeState);g.ok&&(t.attributeState.stroke=new h(g))}d&&t.attributeState.stroke instanceof h&&(t.attributeState.contextStroke=t.attributeState.stroke.color),o&&t.attributeState.fill instanceof h&&(t.attributeState.contextFill=t.attributeState.fill.color);var m=B(i,t.styleSheets,"stroke-linecap");m&&(t.attributeState.strokeLinecap=m);var y=B(i,t.styleSheets,"stroke-linejoin");y&&(t.attributeState.strokeLinejoin=y);var v=B(i,t.styleSheets,"stroke-dasharray");if(v){var b=parseInt(B(i,t.styleSheets,"stroke-dashoffset")||"0");t.attributeState.strokeDasharray=V(v),t.attributeState.strokeDashoffset=b}var x=B(i,t.styleSheets,"stroke-miterlimit");void 0!==x&&""!==x&&(t.attributeState.strokeMiterlimit=parseFloat(x));var S=i.getAttribute("xml:space");S&&(t.attributeState.xmlSpace=S);var w=B(i,t.styleSheets,"font-weight");w&&(t.attributeState.fontWeight=w);var k=B(i,t.styleSheets,"font-style");k&&(t.attributeState.fontStyle=k);var M=B(i,t.styleSheets,"font-family");if(M){var C=G.parse(M);t.attributeState.fontFamily=function(t,e,r){var i=X(t.fontStyle,t.fontWeight),n=r.pdf.getFontList(),a="";return e.some((function(t){var e=n[t];return e&&e.indexOf(i)>=0?(a=t,!0):(t=t.toLowerCase(),!!U.hasOwnProperty(t)&&(a=t,!0))}))||(a="times"),a}(t.attributeState,C,t)}var F=B(i,t.styleSheets,"font-size");if(F){var A=t.pdf.getFontSize();t.attributeState.fontSize=j(F,A)}var T=B(i,t.styleSheets,"vertical-align")||B(i,t.styleSheets,"alignment-baseline");if(T){var P=T.match(/(baseline|text-bottom|alphabetic|ideographic|middle|central|mathematical|text-top|bottom|center|top|hanging)/);P&&(t.attributeState.alignmentBaseline=P[0])}var N=B(i,t.styleSheets,"text-anchor");N&&(t.attributeState.textAnchor=N);var E=B(i,t.styleSheets,"fill-rule");E&&(t.attributeState.fillRule=E)}function ut(t,r,i){var n=1,a=1;n*=t.attributeState.fillOpacity,n*=t.attributeState.opacity,t.attributeState.fill instanceof h&&void 0!==t.attributeState.fill.color.a&&(n*=t.attributeState.fill.color.a),a*=t.attributeState.strokeOpacity,a*=t.attributeState.opacity,t.attributeState.stroke instanceof h&&void 0!==t.attributeState.stroke.color.a&&(a*=t.attributeState.stroke.color.a);var s,o,l=n<1,u=a<1;if(P(i,"use")?(l=!0,u=!0,n*=t.attributeState.fill?1:0,a*=t.attributeState.stroke?1:0):t.withinUse&&(t.attributeState.fill!==r.attributeState.fill?(l=!0,n*=t.attributeState.fill?1:0):l&&!t.attributeState.fill&&(n=0),t.attributeState.stroke!==r.attributeState.stroke?(u=!0,a*=t.attributeState.stroke?1:0):u&&!t.attributeState.stroke&&(a=0)),l||u){var c={};l&&(c.opacity=n),u&&(c["stroke-opacity"]=a),t.pdf.setGState(new e.GState(c))}if(t.attributeState.fill&&t.attributeState.fill!==r.attributeState.fill&&t.attributeState.fill instanceof h&&t.attributeState.fill.color.ok&&!P(i,"text")&&t.pdf.setFillColor(t.attributeState.fill.color.r,t.attributeState.fill.color.g,t.attributeState.fill.color.b),t.attributeState.strokeWidth!==r.attributeState.strokeWidth&&t.pdf.setLineWidth(t.attributeState.strokeWidth),t.attributeState.stroke!==r.attributeState.stroke&&t.attributeState.stroke instanceof h&&t.pdf.setDrawColor(t.attributeState.stroke.color.r,t.attributeState.stroke.color.g,t.attributeState.stroke.color.b),t.attributeState.strokeLinecap!==r.attributeState.strokeLinecap&&t.pdf.setLineCap(t.attributeState.strokeLinecap),t.attributeState.strokeLinejoin!==r.attributeState.strokeLinejoin&&t.pdf.setLineJoin(t.attributeState.strokeLinejoin),t.attributeState.strokeDasharray===r.attributeState.strokeDasharray&&t.attributeState.strokeDashoffset===r.attributeState.strokeDashoffset||!t.attributeState.strokeDasharray||t.pdf.setLineDashPattern(t.attributeState.strokeDasharray,t.attributeState.strokeDashoffset),t.attributeState.strokeMiterlimit!==r.attributeState.strokeMiterlimit&&t.pdf.setLineMiterLimit(t.attributeState.strokeMiterlimit),t.attributeState.fontFamily!==r.attributeState.fontFamily&&(s=U.hasOwnProperty(t.attributeState.fontFamily)?U[t.attributeState.fontFamily]:t.attributeState.fontFamily),t.attributeState.fill&&t.attributeState.fill!==r.attributeState.fill&&t.attributeState.fill instanceof h&&t.attributeState.fill.color.ok){var f=t.attributeState.fill.color;t.pdf.setTextColor(f.r,f.g,f.b)}t.attributeState.fontWeight===r.attributeState.fontWeight&&t.attributeState.fontStyle===r.attributeState.fontStyle||(o=X(t.attributeState.fontStyle,t.attributeState.fontWeight)),void 0===s&&void 0===o||(void 0===s&&(s=U.hasOwnProperty(t.attributeState.fontFamily)?U[t.attributeState.fontFamily]:t.attributeState.fontFamily),t.pdf.setFont(s,o)),t.attributeState.fontSize!==r.attributeState.fontSize&&t.pdf.setFontSize(t.attributeState.fontSize*t.pdf.internal.scaleFactor)}function ht(t,e,r){var i=H.exec(t);if(i){var n=i[1];return r.refsHandler.get(n)||void 0}}function ct(t,e,r){return a(this,void 0,void 0,(function(){var i,n;return s(this,(function(a){switch(a.label){case 0:return i=r.clone(),e.element.hasAttribute("clipPathUnits")&&"objectboundingbox"===e.element.getAttribute("clipPathUnits").toLowerCase()&&(n=t.getBoundingBox(r),i.transform=r.pdf.matrixMult(r.pdf.Matrix(n[2],0,0,n[3],n[0],n[1]),r.transform)),[4,e.apply(i)];case 1:return a.sent(),[2]}}))}))}var ft=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.render=function(t){return a(this,void 0,void 0,(function(){var e,r,i,n;return s(this,(function(a){switch(a.label){case 0:return this.isVisible("hidden"!==t.attributeState.visibility,t)?((e=t.clone()).transform=e.pdf.matrixMult(this.computeNodeTransform(e),t.transform),lt(e,this),r=B(this.element,e.styleSheets,"clip-path"),(i=r&&"none"!==r)?(n=ht(r,0,e))?n.isVisible(!0,e)?(e.pdf.saveGraphicsState(),[4,ct(this,n,e)]):[3,2]:[3,4]:[3,5]):[2];case 1:return a.sent(),[3,3];case 2:return[2];case 3:return[3,5];case 4:i=!1,a.label=5;case 5:return e.withinClipPath||e.pdf.saveGraphicsState(),ut(e,t,this.element),[4,this.renderCore(e)];case 6:return a.sent(),e.withinClipPath||e.pdf.restoreGraphicsState(),i&&e.pdf.restoreGraphicsState(),[2]}}))}))},e}(J),pt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e}(ft),dt=function(t){function e(e,r,i){var n=t.call(this,r,i)||this;return n.cachedPath=null,n.hasMarkers=e,n}return i(e,t),e.prototype.renderCore=function(t){return a(this,void 0,void 0,(function(){var e;return s(this,(function(r){switch(r.label){case 0:return null===(e=this.getCachedPath(t))||0===e.segments.length?[2]:(t.withinClipPath?e.transform(t.transform):t.pdf.setCurrentTransformationMatrix(t.transform),e.draw(t),[4,this.fillOrStroke(t)]);case 1:return r.sent(),this.hasMarkers?[4,this.drawMarkers(t,e)]:[3,3];case 2:r.sent(),r.label=3;case 3:return[2]}}))}))},e.prototype.getCachedPath=function(t){return this.cachedPath||(this.cachedPath=this.getPath(t))},e.prototype.drawMarkers=function(t,e){return a(this,void 0,void 0,(function(){return s(this,(function(r){switch(r.label){case 0:return[4,this.getMarkers(e,t).draw(t.clone({transform:t.pdf.unitMatrix}))];case 1:return r.sent(),[2]}}))}))},e.prototype.fillOrStroke=function(t){return a(this,void 0,void 0,(function(){var e,r,i,n,a;return s(this,(function(s){switch(s.label){case 0:return t.withinClipPath?[2]:(e=t.attributeState.fill,r=t.attributeState.stroke&&0!==t.attributeState.strokeWidth,e?[4,e.getFillData(this,t)]:[3,2]);case 1:return n=s.sent(),[3,3];case 2:n=void 0,s.label=3;case 3:return i=n,a="evenodd"===t.attributeState.fillRule,e&&r||t.withinUse?a?t.pdf.fillStrokeEvenOdd(i):t.pdf.fillStroke(i):e?a?t.pdf.fillEvenOdd(i):t.pdf.fill(i):r?t.pdf.stroke():t.pdf.discardPath(),[2]}}))}))},e.prototype.getBoundingBoxCore=function(t){var e=this.getCachedPath(t);if(!e||!e.segments.length)return[0,0,0,0];for(var r=Number.POSITIVE_INFINITY,i=Number.POSITIVE_INFINITY,n=Number.NEGATIVE_INFINITY,a=Number.NEGATIVE_INFINITY,s=0,o=0,l=0;l<e.segments.length;l++){var u=e.segments[l];(u instanceof C||u instanceof F||u instanceof A)&&(s=u.x,o=u.y),u instanceof A?(r=Math.min(r,s,u.x1,u.x2,u.x),n=Math.max(n,s,u.x1,u.x2,u.x),i=Math.min(i,o,u.y1,u.y2,u.y),a=Math.max(a,o,u.y1,u.y2,u.y)):(r=Math.min(r,s),n=Math.max(n,s),i=Math.min(i,o),a=Math.max(a,o))}return[r,i,n-r,a-i]},e.prototype.getMarkers=function(t,e){var r=B(this.element,e.styleSheets,"marker-start"),i=B(this.element,e.styleSheets,"marker-mid"),n=B(this.element,e.styleSheets,"marker-end"),a=new I;if(r||i||n){n&&(n=gt(n)),r&&(r=gt(r)),i&&(i=gt(i));for(var s=t.segments,o=[1,0],l=void 0,u=!1,h=[1,0],c=!1,f=function(t){var e=s[t],f=r&&(1===t||!(s[t]instanceof C)&&s[t-1]instanceof C);f&&s.forEach((function(e,r){if(!c&&e instanceof T&&r>t){var i=s[r-1];c=(i instanceof C||i instanceof F||i instanceof A)&&i}}));var p=n&&(t===s.length-1||!(s[t]instanceof C)&&s[t+1]instanceof C),d=i&&t>0&&!(1===t&&s[t-1]instanceof C),g=s[t-1]||null;if(g instanceof C||g instanceof F||g instanceof A){if(e instanceof A)f&&a.addMarker(new _(r,[g.x,g.y],y(c?[c.x,c.y]:[g.x,g.y],[e.x1,e.y1]),!0)),p&&a.addMarker(new _(n,[e.x,e.y],y([e.x2,e.y2],[e.x,e.y]))),d&&(l=S([g.x,g.y],[e.x1,e.y1]),l=g instanceof C?l:x(w(o,l)),a.addMarker(new _(i,[g.x,g.y],Math.atan2(l[1],l[0])))),o=S([e.x2,e.y2],[e.x,e.y]);else if(e instanceof C||e instanceof F){if(l=S([g.x,g.y],[e.x,e.y]),f){var m=c?S([c.x,c.y],[e.x,e.y]):l;a.addMarker(new _(r,[g.x,g.y],Math.atan2(m[1],m[0]),!0))}if(p&&a.addMarker(new _(n,[e.x,e.y],Math.atan2(l[1],l[0]))),d){m=e instanceof C?o:g instanceof C?l:x(w(o,l));a.addMarker(new _(i,[g.x,g.y],Math.atan2(m[1],m[0])))}o=l}else if(e instanceof T){if(l=S([g.x,g.y],[u.x,u.y]),d){m=g instanceof C?l:x(w(o,l));a.addMarker(new _(i,[g.x,g.y],Math.atan2(m[1],m[0])))}if(p){m=x(w(l,h));a.addMarker(new _(n,[u.x,u.y],Math.atan2(m[1],m[0])))}o=l}}else{u=e instanceof C&&e;var v=s[t+1];(v instanceof C||v instanceof F||v instanceof A)&&(h=S([u.x,u.y],[v.x,v.y]))}},p=0;p<s.length;p++)f(p)}return a.markers.forEach((function(t){var r=e.refsHandler.get(t.id);if(r){var i=B(r.element,e.styleSheets,"orient");null!=i&&(t.isStartMarker&&"auto-start-reverse"===i&&(t.angle+=Math.PI),isNaN(Number(i))||(t.angle=parseFloat(i)/180*Math.PI))}})),a},e}(pt);function gt(t){var e=H.exec(t);return e&&e[1]||void 0}var mt=function(t){function e(e,r){return t.call(this,!0,e,r)||this}return i(e,t),e.prototype.getPath=function(t){if(t.withinClipPath||null===t.attributeState.stroke)return null;var e=parseFloat(this.element.getAttribute("x1")||"0"),r=parseFloat(this.element.getAttribute("y1")||"0"),i=parseFloat(this.element.getAttribute("x2")||"0"),n=parseFloat(this.element.getAttribute("y2")||"0");return e||i||r||n?(new M).moveTo(e,r).lineTo(i,n):null},e.prototype.computeNodeTransformCore=function(t){return t.pdf.unitMatrix},e.prototype.isVisible=function(t,e){return N(this,t,e)},e.prototype.fillOrStroke=function(e){return a(this,void 0,void 0,(function(){return s(this,(function(r){switch(r.label){case 0:return e.attributeState.fill=null,[4,t.prototype.fillOrStroke.call(this,e)];case 1:return r.sent(),[2]}}))}))},e}(dt),yt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.apply=function(t){return a(this,void 0,void 0,(function(){var e,r,i,n,a;return s(this,(function(s){switch(s.label){case 0:return this.isVisible("hidden"!==t.attributeState.visibility,t)?((e=t.clone()).transform=e.pdf.unitMatrix,lt(e,this),r=B(this.element,e.styleSheets,"clip-path"),r&&"none"!==r&&(i=ht(r,0,e))?i.isVisible(!0,e)?[4,ct(this,i,e)]:[3,2]:[3,3]):[2];case 1:return s.sent(),[3,3];case 2:return[2];case 3:ut(e,t,this.element),n=0,a=this.children,s.label=4;case 4:return n<a.length?[4,a[n].render(e)]:[3,7];case 5:s.sent(),s.label=6;case 6:return n++,[3,4];case 7:return[2]}}))}))},e.prototype.getBoundingBoxCore=function(t){return Q(t,this)},e.prototype.isVisible=function(t,e){return E(this,t,e)},e.prototype.computeNodeTransformCore=function(t){var e=parseFloat(B(this.element,t.styleSheets,"x")||"0"),r=parseFloat(B(this.element,t.styleSheets,"y")||"0"),i=this.element.getAttribute("viewBox");if(i){var n=V(i),a=parseFloat(B(this.element,t.styleSheets,"width")||B(this.element.ownerSVGElement,t.styleSheets,"width")||i[2]),s=parseFloat(B(this.element,t.styleSheets,"height")||B(this.element.ownerSVGElement,t.styleSheets,"height")||i[3]);return K(this.element,n,e,r,a,s,t)}return t.pdf.Matrix(1,0,0,1,e,r)},e}(tt),vt=function(t,e){this.width=t,this.height=e},bt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.renderCore=function(t){return a(this,void 0,void 0,(function(){var r,i,n,a,o,l,u,h,p,d,g,m,y;return s(this,(function(s){switch(s.label){case 0:return r=parseFloat,(i=this.element.getAttribute("href")||this.element.getAttribute("xlink:href"))?(n=i.substring(1),a=t.refsHandler.get(n),o=P(a.element,"symbol,svg")&&a.element.hasAttribute("viewBox"),l=r(B(this.element,t.styleSheets,"x")||"0"),u=r(B(this.element,t.styleSheets,"y")||"0"),h=void 0,p=void 0,o?(h=r(B(this.element,t.styleSheets,"width")||B(a.element,t.styleSheets,"width")||"0"),p=r(B(this.element,t.styleSheets,"height")||B(a.element,t.styleSheets,"height")||"0"),l+=r(B(a.element,t.styleSheets,"x")||"0"),u+=r(B(a.element,t.styleSheets,"y")||"0"),g=V(a.element.getAttribute("viewBox")),d=K(a.element,g,l,u,h,p,t)):d=t.pdf.Matrix(1,0,0,1,l,u),m=c.getContextColors(t,!0),y=new f(t.pdf,{refsHandler:t.refsHandler,styleSheets:t.styleSheets,withinUse:!0,viewport:o?new vt(h,p):t.viewport,svg2pdfParameters:t.svg2pdfParameters,textMeasure:t.textMeasure,attributeState:Object.assign(c.default(),m)}),[4,t.refsHandler.getRendered(n,m,(function(t){return e.renderReferencedNode(t,n,y)}))]):[2];case 1:return s.sent(),t.pdf.saveGraphicsState(),t.pdf.setCurrentTransformationMatrix(t.transform),o&&"visible"!==B(a.element,t.styleSheets,"overflow")&&(t.pdf.rect(l,u,h,p),t.pdf.clip().discardPath()),t.pdf.doFormObject(t.refsHandler.generateKey(n,m),d),t.pdf.restoreGraphicsState(),[2]}}))}))},e.renderReferencedNode=function(t,e,r){return a(this,void 0,void 0,(function(){var i;return s(this,(function(n){switch(n.label){case 0:return i=[(i=t.getBoundingBox(r))[0]-.5*i[2],i[1]-.5*i[3],2*i[2],2*i[3]],r.pdf.beginFormObject(i[0],i[1],i[2],i[3],r.pdf.unitMatrix),t instanceof yt?[4,t.apply(r)]:[3,2];case 1:return n.sent(),[3,4];case 2:return[4,t.render(r)];case 3:n.sent(),n.label=4;case 4:return r.pdf.endFormObject(r.refsHandler.generateKey(e,r.attributeState)),[2]}}))}))},e.prototype.getBoundingBoxCore=function(t){return $(this.element,t)},e.prototype.isVisible=function(t,e){return N(this,t,e)},e.prototype.computeNodeTransformCore=function(t){return t.pdf.unitMatrix},e}(pt),xt=function(t){function e(e,r){return t.call(this,!1,e,r)||this}return i(e,t),e.prototype.getPath=function(t){var e=parseFloat(B(this.element,t.styleSheets,"width")||"0"),r=parseFloat(B(this.element,t.styleSheets,"height")||"0");if(!isFinite(e)||e<=0||!isFinite(r)||r<=0)return null;var i=B(this.element,t.styleSheets,"rx"),n=B(this.element,t.styleSheets,"ry"),a=Math.min(parseFloat(i||n||"0"),.5*e),s=Math.min(parseFloat(n||i||"0"),.5*r),o=parseFloat(B(this.element,t.styleSheets,"x")||"0"),l=parseFloat(B(this.element,t.styleSheets,"y")||"0"),u=4/3*(Math.SQRT2-1);return 0===a&&0===s?(new M).moveTo(o,l).lineTo(o+e,l).lineTo(o+e,l+r).lineTo(o,l+r).close():(new M).moveTo(o+=a,l).lineTo(o+=e-2*a,l).curveTo(o+a*u,l,o+a,l+(s-s*u),o+=a,l+=s).lineTo(o,l+=r-2*s).curveTo(o,l+s*u,o-a*u,l+s,o-=a,l+=s).lineTo(o+=2*a-e,l).curveTo(o-a*u,l,o-a,l-s*u,o-=a,l-=s).lineTo(o,l+=2*s-r).curveTo(o,l-s*u,o+a*u,l-s,o+=a,l-=s).close()},e.prototype.computeNodeTransformCore=function(t){return t.pdf.unitMatrix},e.prototype.isVisible=function(t,e){return N(this,t,e)},e}(dt),St=function(t){function e(e,r){return t.call(this,!1,e,r)||this}return i(e,t),e.prototype.getPath=function(t){var e=this.getRx(t),r=this.getRy(t);if(!isFinite(e)||r<=0||!isFinite(r)||r<=0)return null;var i=parseFloat(B(this.element,t.styleSheets,"cx")||"0"),n=parseFloat(B(this.element,t.styleSheets,"cy")||"0"),a=4/3*(Math.SQRT2-1)*e,s=4/3*(Math.SQRT2-1)*r;return(new M).moveTo(i+e,n).curveTo(i+e,n-s,i+a,n-r,i,n-r).curveTo(i-a,n-r,i-e,n-s,i-e,n).curveTo(i-e,n+s,i-a,n+r,i,n+r).curveTo(i+a,n+r,i+e,n+s,i+e,n)},e.prototype.computeNodeTransformCore=function(t){return t.pdf.unitMatrix},e.prototype.isVisible=function(t,e){return N(this,t,e)},e}(dt),wt=function(t){function e(e,r){return t.call(this,e,r)||this}return i(e,t),e.prototype.getRx=function(t){return parseFloat(B(this.element,t.styleSheets,"rx")||"0")},e.prototype.getRy=function(t){return parseFloat(B(this.element,t.styleSheets,"ry")||"0")},e}(St);function kt(t){var e="invisible",r=t.stroke&&0!==t.strokeWidth,i=t.fill;return i&&r?e="fillThenStroke":i?e="fill":r&&(e="stroke"),e}function Mt(t){return t.replace(/[\n\r]/g,"")}function Ct(t){return t.replace(/[\t]/g," ")}function Ft(t){return t.replace(/ +/g," ")}function At(t,e,r){switch(B(t,r.styleSheets,"text-transform")){case"uppercase":return e.toUpperCase();case"lowercase":return e.toLowerCase();default:return e}}var Tt,Pt,Bt,Nt,Et,Ot,Lt,It,_t,Ht,Rt,Dt,jt,qt,Vt=function(){function t(t,e,r,i){this.textNode=t,this.texts=[],this.textNodes=[],this.contexts=[],this.textAnchor=e,this.originX=r,this.originY=i,this.textMeasures=[]}return t.prototype.setX=function(t){this.originX=t},t.prototype.setY=function(t){this.originY=t},t.prototype.add=function(t,e,r){this.texts.push(e),this.textNodes.push(t),this.contexts.push(r)},t.prototype.rightTrimText=function(){for(var t=this.texts.length-1;t>=0;t--)if("default"===this.contexts[t].attributeState.xmlSpace&&(this.texts[t]=this.texts[t].replace(/\s+$/,"")),this.texts[t].match(/[^\s]/))return!1;return!0},t.prototype.measureText=function(t){for(var e=0;e<this.texts.length;e++)this.textMeasures.push({width:t.textMeasure.measureTextWidth(this.texts[e],this.contexts[e].attributeState),length:this.texts[e].length})},t.prototype.put=function(e,r){var i,n,a,s,o=[],l=[],u=[],h=this.originX,c=this.originY,f=h,p=h;for(i=0;i<this.textNodes.length;i++){n=this.textNodes[i],a=this.contexts[i],s=this.textMeasures[i]||{width:e.textMeasure.measureTextWidth(this.texts[i],this.contexts[i].attributeState),length:this.texts[i].length};var d=h,g=c;if("#text"!==n.nodeName&&!o.includes(n)){o.push(n);var m=t.resolveRelativePositionAttribute(n,"dx");null!==m&&(d+=j(m,a.attributeState.fontSize));var y=t.resolveRelativePositionAttribute(n,"dy");null!==y&&(g+=j(y,a.attributeState.fontSize))}l[i]=d,u[i]=g,h=d+s.width+s.length*r,c=g,f=Math.min(f,d),p=Math.max(p,h)}var v=0;switch(this.textAnchor){case"start":v=0;break;case"middle":v=(p-f)/2;break;case"end":v=p-f}for(i=0;i<this.textNodes.length;i++)if(n=this.textNodes[i],a=this.contexts[i],"#text"===n.nodeName||"hidden"!==a.attributeState.visibility){e.pdf.saveGraphicsState(),ut(a,e,n);var b=a.attributeState.alignmentBaseline,x=kt(a.attributeState);e.pdf.text(this.texts[i],l[i]-v,u[i],{baseline:q(b),angle:e.transform,renderingMode:"fill"===x?void 0:x,charSpace:0===r?void 0:r}),e.pdf.restoreGraphicsState()}return[h,c]},t.resolveRelativePositionAttribute=function(t,e){for(var r,i=t;i&&P(i,"tspan");){if(i.hasAttribute(e))return i.getAttribute(e);if((null===(r=t.parentElement)||void 0===r?void 0:r.firstChild)!==t)break;i=i.parentElement}return null},t}(),Wt=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.boundingBox=[],e}return i(e,t),e.prototype.processTSpans=function(t,e,r,i,n,a){for(var s=r.pdf.getFontSize(),o=r.attributeState.xmlSpace,l=!0,u=!1,h=0;h<e.childNodes.length;h++){var c=e.childNodes[h];if(c.textContent){var f=c.textContent;if("#text"===c.nodeName){var p=Mt(f);p=Ct(p),"default"===o&&(p=Ft(p),l&&p.match(/^\s/)&&(u=!0),p.match(/[^\s]/)&&(l=!1),a.prevText.match(/\s$/)&&(p=p.replace(/^\s+/,"")));var d=At(e,p,r);n.add(e,d,r),a.prevText=f,a.prevContext=r}else if(P(c,"title"));else if(P(c,"tspan")){var g=c,m=g.getAttribute("x");if(null!==m){var y=j(m,s);n=new Vt(this,B(g,r.styleSheets,"text-anchor")||r.attributeState.textAnchor,y,0),i.push({type:"y",chunk:n})}var v=g.getAttribute("y");if(null!==v){var b=j(v,s);n=new Vt(this,B(g,r.styleSheets,"text-anchor")||r.attributeState.textAnchor,0,b),i.push({type:"x",chunk:n})}var x=r.clone();lt(x,t,g),this.processTSpans(t,g,x,i,n,a)}}}return u},e.prototype.renderCore=function(t){return a(this,void 0,void 0,(function(){var e,r,i,n,a,o,l,u,h,c,f,p,d,g,m,y,v,b,x,S,w,k,M;return s(this,(function(s){if(t.pdf.saveGraphicsState(),e=0,r=0,i=1,n=t.pdf.getFontSize(),a=j(this.element.getAttribute("x"),n),o=j(this.element.getAttribute("y"),n),l=j(this.element.getAttribute("dx"),n),u=j(this.element.getAttribute("dy"),n),h=parseFloat(this.element.getAttribute("textLength")||"0"),c=t.attributeState.visibility,0===this.element.childElementCount)f=this.element.textContent||"",p=function(t,e){return t=Ct(t=Mt(t)),"default"===e.xmlSpace&&(t=Ft(t=t.trim())),t}(f,t.attributeState),d=At(this.element,p,t),e=t.textMeasure.getTextOffset(d,t.attributeState),h>0&&(g=t.textMeasure.measureTextWidth(d,t.attributeState),"default"===t.attributeState.xmlSpace&&f.match(/^\s/)&&(i=0),r=(h-g)/(d.length-i)||0),"visible"===c&&(m=t.attributeState.alignmentBaseline,y=kt(t.attributeState),t.pdf.text(d,a+l-e,o+u,{baseline:q(m),angle:t.transform,renderingMode:"fill"===y?void 0:y,charSpace:0===r?void 0:r}),this.boundingBox=[a+l-e,o+u+.1*n,t.textMeasure.measureTextWidth(d,t.attributeState),n]);else{for(v=[],b=new Vt(this,t.attributeState.textAnchor,a+l,o+u),v.push({type:"",chunk:b}),x=this.processTSpans(this,this.element,t,v,b,{prevText:" ",prevContext:t}),i=x?0:1,S=!0,w=v.length-1;w>=0;w--)S&&(S=v[w].chunk.rightTrimText());h>0&&(k=0,M=0,v.forEach((function(e){var r=e.chunk;r.measureText(t),r.textMeasures.forEach((function(t){var e=t.width,r=t.length;k+=e,M+=r}))})),r=(h-k)/(M-i)),v.reduce((function(e,i){var n=i.type,a=i.chunk;return"x"===n?a.setX(e[0]):"y"===n&&a.setY(e[1]),a.put(t,r)}),[0,0])}return t.pdf.restoreGraphicsState(),[2]}))}))},e.prototype.isVisible=function(t,e){return E(this,t,e)},e.prototype.getBoundingBoxCore=function(t){return this.boundingBox.length>0?this.boundingBox:$(this.element,t)},e.prototype.computeNodeTransformCore=function(t){return t.pdf.unitMatrix},e}(pt);function Gt(){if(Nt)return Bt;function t(){if(!(this instanceof t))return new t;this.queue=[],this.cache=null}return Nt=1,t.prototype.matrix=function(t){return 1===t[0]&&0===t[1]&&0===t[2]&&1===t[3]&&0===t[4]&&0===t[5]||(this.cache=null,this.queue.push(t)),this},t.prototype.translate=function(t,e){return 0===t&&0===e||(this.cache=null,this.queue.push([1,0,0,1,t,e])),this},t.prototype.scale=function(t,e){return 1===t&&1===e||(this.cache=null,this.queue.push([t,0,0,e,0,0])),this},t.prototype.rotate=function(t,e,r){var i,n,a;return 0!==t&&(this.translate(e,r),i=t*Math.PI/180,n=Math.cos(i),a=Math.sin(i),this.queue.push([n,a,-a,n,0,0]),this.cache=null,this.translate(-e,-r)),this},t.prototype.skewX=function(t){return 0!==t&&(this.cache=null,this.queue.push([1,0,Math.tan(t*Math.PI/180),1,0,0])),this},t.prototype.skewY=function(t){return 0!==t&&(this.cache=null,this.queue.push([1,Math.tan(t*Math.PI/180),0,1,0,0])),this},t.prototype.toArray=function(){if(this.cache)return this.cache;if(!this.queue.length)return this.cache=[1,0,0,1,0,0],this.cache;if(this.cache=this.queue[0],1===this.queue.length)return this.cache;for(var t=1;t<this.queue.length;t++)this.cache=(e=this.cache,r=this.queue[t],[e[0]*r[0]+e[2]*r[1],e[1]*r[0]+e[3]*r[1],e[0]*r[2]+e[2]*r[3],e[1]*r[2]+e[3]*r[3],e[0]*r[4]+e[2]*r[5]+e[4],e[1]*r[4]+e[3]*r[5]+e[5]]);var e,r;return this.cache},t.prototype.calc=function(t,e,r){var i;return this.queue.length?(this.cache||(this.cache=this.toArray()),[t*(i=this.cache)[0]+e*i[2]+(r?0:i[4]),t*i[1]+e*i[3]+(r?0:i[5])]):[t,e]},Bt=t}function Ut(){if(It)return Lt;It=1;var t=2*Math.PI;function e(t,e,r,i){var n=t*r+e*i;return n>1&&(n=1),n<-1&&(n=-1),(t*i-e*r<0?-1:1)*Math.acos(n)}function r(t,e){var r=4/3*Math.tan(e/4),i=Math.cos(t),n=Math.sin(t),a=Math.cos(t+e),s=Math.sin(t+e);return[i,n,i-n*r,n+i*r,a+s*r,s-a*r,a,s]}return Lt=function(i,n,a,s,o,l,u,h,c){var f=Math.sin(c*t/360),p=Math.cos(c*t/360),d=p*(i-a)/2+f*(n-s)/2,g=-f*(i-a)/2+p*(n-s)/2;if(0===d&&0===g)return[];if(0===u||0===h)return[];u=Math.abs(u),h=Math.abs(h);var m=d*d/(u*u)+g*g/(h*h);m>1&&(u*=Math.sqrt(m),h*=Math.sqrt(m));var y=function(r,i,n,a,s,o,l,u,h,c){var f=c*(r-n)/2+h*(i-a)/2,p=-h*(r-n)/2+c*(i-a)/2,d=l*l,g=u*u,m=f*f,y=p*p,v=d*g-d*y-g*m;v<0&&(v=0),v/=d*y+g*m;var b=(v=Math.sqrt(v)*(s===o?-1:1))*l/u*p,x=v*-u/l*f,S=c*b-h*x+(r+n)/2,w=h*b+c*x+(i+a)/2,k=(f-b)/l,M=(p-x)/u,C=(-f-b)/l,F=(-p-x)/u,A=e(1,0,k,M),T=e(k,M,C,F);return 0===o&&T>0&&(T-=t),1===o&&T<0&&(T+=t),[S,w,A,T]}(i,n,a,s,o,l,u,h,f,p),v=[],b=y[2],x=y[3],S=Math.max(Math.ceil(Math.abs(x)/(t/4)),1);x/=S;for(var w=0;w<S;w++)v.push(r(b,x)),b+=x;return v.map((function(t){for(var e=0;e<t.length;e+=2){var r=t[e+0],i=t[e+1],n=p*(r*=u)-f*(i*=h),a=f*r+p*i;t[e+0]=n+y[0],t[e+1]=a+y[1]}return t}))}}function zt(){if(Ht)return _t;Ht=1;var t=1e-10,e=Math.PI/180;function r(t,e,i){if(!(this instanceof r))return new r(t,e,i);this.rx=t,this.ry=e,this.ax=i}return r.prototype.transform=function(r){var i=Math.cos(this.ax*e),n=Math.sin(this.ax*e),a=[this.rx*(r[0]*i+r[2]*n),this.rx*(r[1]*i+r[3]*n),this.ry*(-r[0]*n+r[2]*i),this.ry*(-r[1]*n+r[3]*i)],s=a[0]*a[0]+a[2]*a[2],o=a[1]*a[1]+a[3]*a[3],l=((a[0]-a[3])*(a[0]-a[3])+(a[2]+a[1])*(a[2]+a[1]))*((a[0]+a[3])*(a[0]+a[3])+(a[2]-a[1])*(a[2]-a[1])),u=(s+o)/2;if(l<t*u)return this.rx=this.ry=Math.sqrt(u),this.ax=0,this;var h=a[0]*a[1]+a[2]*a[3],c=u+(l=Math.sqrt(l))/2,f=u-l/2;return this.ax=Math.abs(h)<t&&Math.abs(c-o)<t?90:180*Math.atan(Math.abs(h)>Math.abs(c-o)?(c-s)/h:h/(c-o))/Math.PI,this.ax>=0?(this.rx=Math.sqrt(c),this.ry=Math.sqrt(f)):(this.ax+=90,this.rx=Math.sqrt(f),this.ry=Math.sqrt(c)),this},r.prototype.isDegenerate=function(){return this.rx<t*this.ry||this.ry<t*this.rx},_t=r}function Yt(){if(Dt)return Rt;Dt=1;var t=function(){if(Pt)return Tt;Pt=1;var t={a:7,c:6,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,z:0},e=[5760,6158,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8239,8287,12288,65279];function r(t){return t>=48&&t<=57}function i(t){return t>=48&&t<=57||43===t||45===t||46===t}function n(t){this.index=0,this.path=t,this.max=t.length,this.result=[],this.param=0,this.err="",this.segmentStart=0,this.data=[]}function a(t){for(;t.index<t.max&&(10===(r=t.path.charCodeAt(t.index))||13===r||8232===r||8233===r||32===r||9===r||11===r||12===r||160===r||r>=5760&&e.indexOf(r)>=0);)t.index++;var r}function s(t){var e=t.path.charCodeAt(t.index);return 48===e?(t.param=0,void t.index++):49===e?(t.param=1,void t.index++):void(t.err="SvgPath: arc flag can be 0 or 1 only (at pos "+t.index+")")}function o(t){var e,i=t.index,n=i,a=t.max,s=!1,o=!1,l=!1,u=!1;if(n>=a)t.err="SvgPath: missed param (at pos "+n+")";else if(43!==(e=t.path.charCodeAt(n))&&45!==e||(e=++n<a?t.path.charCodeAt(n):0),r(e)||46===e){if(46!==e){if(s=48===e,e=++n<a?t.path.charCodeAt(n):0,s&&n<a&&e&&r(e))return void(t.err="SvgPath: numbers started with `0` such as `09` are illegal (at pos "+i+")");for(;n<a&&r(t.path.charCodeAt(n));)n++,o=!0;e=n<a?t.path.charCodeAt(n):0}if(46===e){for(u=!0,n++;r(t.path.charCodeAt(n));)n++,l=!0;e=n<a?t.path.charCodeAt(n):0}if(101===e||69===e){if(u&&!o&&!l)return void(t.err="SvgPath: invalid float exponent (at pos "+n+")");if(43!==(e=++n<a?t.path.charCodeAt(n):0)&&45!==e||n++,!(n<a&&r(t.path.charCodeAt(n))))return void(t.err="SvgPath: invalid float exponent (at pos "+n+")");for(;n<a&&r(t.path.charCodeAt(n));)n++}t.index=n,t.param=parseFloat(t.path.slice(i,n))+0}else t.err="SvgPath: param should start with 0..9 or `.` (at pos "+n+")"}function l(e){var r,i;i=(r=e.path[e.segmentStart]).toLowerCase();var n=e.data;if("m"===i&&n.length>2&&(e.result.push([r,n[0],n[1]]),n=n.slice(2),i="l",r="m"===r?"l":"L"),"r"===i)e.result.push([r].concat(n));else for(;n.length>=t[i]&&(e.result.push([r].concat(n.splice(0,t[i]))),t[i]););}function u(e){var r,n,u,h,c,f=e.max;if(e.segmentStart=e.index,n=97==(32|(r=e.path.charCodeAt(e.index))),function(t){switch(32|t){case 109:case 122:case 108:case 104:case 118:case 99:case 115:case 113:case 116:case 97:case 114:return!0}return!1}(r))if(h=t[e.path[e.index].toLowerCase()],e.index++,a(e),e.data=[],h){for(u=!1;;){for(c=h;c>0;c--){if(!n||3!==c&&4!==c?o(e):s(e),e.err.length)return;e.data.push(e.param),a(e),u=!1,e.index<f&&44===e.path.charCodeAt(e.index)&&(e.index++,a(e),u=!0)}if(!u){if(e.index>=e.max)break;if(!i(e.path.charCodeAt(e.index)))break}}l(e)}else l(e);else e.err="SvgPath: bad command "+e.path[e.index]+" (at pos "+e.index+")"}return Tt=function(t){var e=new n(t),r=e.max;for(a(e);e.index<r&&!e.err.length;)u(e);return e.err.length?e.result=[]:e.result.length&&("mM".indexOf(e.result[0][0])<0?(e.err="SvgPath: string should start with `M` or `m`",e.result=[]):e.result[0][0]="M"),{err:e.err,segments:e.result}}}(),e=function(){if(Ot)return Et;Ot=1;var t=Gt(),e={matrix:!0,scale:!0,rotate:!0,translate:!0,skewX:!0,skewY:!0},r=/\s*(matrix|translate|scale|rotate|skewX|skewY)\s*\(\s*(.+?)\s*\)[\s,]*/,i=/[\s,]+/;return Et=function(n){var a,s,o=new t;return n.split(r).forEach((function(t){if(t.length)if(void 0===e[t])switch(s=t.split(i).map((function(t){return+t||0})),a){case"matrix":return void(6===s.length&&o.matrix(s));case"scale":return void(1===s.length?o.scale(s[0],s[0]):2===s.length&&o.scale(s[0],s[1]));case"rotate":return void(1===s.length?o.rotate(s[0],0,0):3===s.length&&o.rotate(s[0],s[1],s[2]));case"translate":return void(1===s.length?o.translate(s[0],0):2===s.length&&o.translate(s[0],s[1]));case"skewX":return void(1===s.length&&o.skewX(s[0]));case"skewY":return void(1===s.length&&o.skewY(s[0]))}else a=t})),o},Et}(),r=Gt(),i=Ut(),n=zt();function a(e){if(!(this instanceof a))return new a(e);var r=t(e);this.segments=r.segments,this.err=r.err,this.__stack=[]}return a.from=function(t){if("string"==typeof t)return new a(t);if(t instanceof a){var e=new a("");return e.err=t.err,e.segments=t.segments.map((function(t){return t.slice()})),e.__stack=t.__stack.map((function(t){return r().matrix(t.toArray())})),e}throw new Error("SvgPath.from: invalid param type "+t)},a.prototype.__matrix=function(t){var e,r=this;t.queue.length&&this.iterate((function(i,a,s,o){var l,u,h,c;switch(i[0]){case"v":u=0===(l=t.calc(0,i[1],!0))[0]?["v",l[1]]:["l",l[0],l[1]];break;case"V":u=(l=t.calc(s,i[1],!1))[0]===t.calc(s,o,!1)[0]?["V",l[1]]:["L",l[0],l[1]];break;case"h":u=0===(l=t.calc(i[1],0,!0))[1]?["h",l[0]]:["l",l[0],l[1]];break;case"H":u=(l=t.calc(i[1],o,!1))[1]===t.calc(s,o,!1)[1]?["H",l[0]]:["L",l[0],l[1]];break;case"a":case"A":var f=t.toArray(),p=n(i[1],i[2],i[3]).transform(f);if(f[0]*f[3]-f[1]*f[2]<0&&(i[5]=i[5]?"0":"1"),l=t.calc(i[6],i[7],"a"===i[0]),"A"===i[0]&&i[6]===s&&i[7]===o||"a"===i[0]&&0===i[6]&&0===i[7]){u=["a"===i[0]?"l":"L",l[0],l[1]];break}u=p.isDegenerate()?["a"===i[0]?"l":"L",l[0],l[1]]:[i[0],p.rx,p.ry,p.ax,i[4],i[5],l[0],l[1]];break;case"m":c=a>0,u=["m",(l=t.calc(i[1],i[2],c))[0],l[1]];break;default:for(u=[h=i[0]],c=h.toLowerCase()===h,e=1;e<i.length;e+=2)l=t.calc(i[e],i[e+1],c),u.push(l[0],l[1])}r.segments[a]=u}),!0)},a.prototype.__evaluateStack=function(){var t,e;if(this.__stack.length){if(1===this.__stack.length)return this.__matrix(this.__stack[0]),void(this.__stack=[]);for(t=r(),e=this.__stack.length;--e>=0;)t.matrix(this.__stack[e].toArray());this.__matrix(t),this.__stack=[]}},a.prototype.toString=function(){var t="",e="",r=!1;this.__evaluateStack();for(var i=0,n=this.segments.length;i<n;i++){var a=this.segments[i],s=a[0];s!==e||"m"===s||"M"===s?("m"===s&&"z"===e&&(t+=" "),t+=s,r=!1):r=!0;for(var o=1;o<a.length;o++){var l=a[o];1===o?r&&l>=0&&(t+=" "):l>=0&&(t+=" "),t+=l}e=s}return t},a.prototype.translate=function(t,e){return this.__stack.push(r().translate(t,e||0)),this},a.prototype.scale=function(t,e){return this.__stack.push(r().scale(t,e||0===e?e:t)),this},a.prototype.rotate=function(t,e,i){return this.__stack.push(r().rotate(t,e||0,i||0)),this},a.prototype.skewX=function(t){return this.__stack.push(r().skewX(t)),this},a.prototype.skewY=function(t){return this.__stack.push(r().skewY(t)),this},a.prototype.matrix=function(t){return this.__stack.push(r().matrix(t)),this},a.prototype.transform=function(t){return t.trim()?(this.__stack.push(e(t)),this):this},a.prototype.round=function(t){var e,r=0,i=0,n=0,a=0;return t=t||0,this.__evaluateStack(),this.segments.forEach((function(s){var o=s[0].toLowerCase()===s[0];switch(s[0]){case"H":case"h":return o&&(s[1]+=n),n=s[1]-s[1].toFixed(t),void(s[1]=+s[1].toFixed(t));case"V":case"v":return o&&(s[1]+=a),a=s[1]-s[1].toFixed(t),void(s[1]=+s[1].toFixed(t));case"Z":case"z":return n=r,void(a=i);case"M":case"m":return o&&(s[1]+=n,s[2]+=a),n=s[1]-s[1].toFixed(t),a=s[2]-s[2].toFixed(t),r=n,i=a,s[1]=+s[1].toFixed(t),void(s[2]=+s[2].toFixed(t));case"A":case"a":return o&&(s[6]+=n,s[7]+=a),n=s[6]-s[6].toFixed(t),a=s[7]-s[7].toFixed(t),s[1]=+s[1].toFixed(t),s[2]=+s[2].toFixed(t),s[3]=+s[3].toFixed(t+2),s[6]=+s[6].toFixed(t),void(s[7]=+s[7].toFixed(t));default:return e=s.length,o&&(s[e-2]+=n,s[e-1]+=a),n=s[e-2]-s[e-2].toFixed(t),a=s[e-1]-s[e-1].toFixed(t),void s.forEach((function(e,r){r&&(s[r]=+s[r].toFixed(t))}))}})),this},a.prototype.iterate=function(t,e){var r,i,n,a=this.segments,s={},o=!1,l=0,u=0,h=0,c=0;if(e||this.__evaluateStack(),a.forEach((function(e,r){var i=t(e,r,l,u);Array.isArray(i)&&(s[r]=i,o=!0);var n=e[0]===e[0].toLowerCase();switch(e[0]){case"m":case"M":return l=e[1]+(n?l:0),u=e[2]+(n?u:0),h=l,void(c=u);case"h":case"H":return void(l=e[1]+(n?l:0));case"v":case"V":return void(u=e[1]+(n?u:0));case"z":case"Z":return l=h,void(u=c);default:l=e[e.length-2]+(n?l:0),u=e[e.length-1]+(n?u:0)}})),!o)return this;for(n=[],r=0;r<a.length;r++)if(void 0!==s[r])for(i=0;i<s[r].length;i++)n.push(s[r][i]);else n.push(a[r]);return this.segments=n,this},a.prototype.abs=function(){return this.iterate((function(t,e,r,i){var n,a=t[0],s=a.toUpperCase();if(a!==s)switch(t[0]=s,a){case"v":return void(t[1]+=i);case"a":return t[6]+=r,void(t[7]+=i);default:for(n=1;n<t.length;n++)t[n]+=n%2?r:i}}),!0),this},a.prototype.rel=function(){return this.iterate((function(t,e,r,i){var n,a=t[0],s=a.toLowerCase();if(a!==s&&(0!==e||"M"!==a))switch(t[0]=s,a){case"V":return void(t[1]-=i);case"A":return t[6]-=r,void(t[7]-=i);default:for(n=1;n<t.length;n++)t[n]-=n%2?r:i}}),!0),this},a.prototype.unarc=function(){return this.iterate((function(t,e,r,n){var a,s,o,l=[],u=t[0];return"A"!==u&&"a"!==u?null:("a"===u?(s=r+t[6],o=n+t[7]):(s=t[6],o=t[7]),0===(a=i(r,n,s,o,t[4],t[5],t[1],t[2],t[3])).length?[["a"===t[0]?"l":"L",t[6],t[7]]]:(a.forEach((function(t){l.push(["C",t[2],t[3],t[4],t[5],t[6],t[7]])})),l))})),this},a.prototype.unshort=function(){var t,e,r,i,n,a=this.segments;return this.iterate((function(s,o,l,u){var h,c=s[0],f=c.toUpperCase();o&&("T"===f?(h="t"===c,"Q"===(r=a[o-1])[0]?(t=r[1]-l,e=r[2]-u):"q"===r[0]?(t=r[1]-r[3],e=r[2]-r[4]):(t=0,e=0),i=-t,n=-e,h||(i+=l,n+=u),a[o]=[h?"q":"Q",i,n,s[1],s[2]]):"S"===f&&(h="s"===c,"C"===(r=a[o-1])[0]?(t=r[3]-l,e=r[4]-u):"c"===r[0]?(t=r[3]-r[5],e=r[4]-r[6]):(t=0,e=0),i=-t,n=-e,h||(i+=l,n+=u),a[o]=[h?"c":"C",i,n,s[1],s[2],s[3],s[4]]))})),this},Rt=a}var Xt=p(qt?jt:(qt=1,jt=Yt())),Qt=function(t){function e(e,r){return t.call(this,!0,e,r)||this}return i(e,t),e.prototype.computeNodeTransformCore=function(t){return t.pdf.unitMatrix},e.prototype.isVisible=function(t,e){return N(this,t,e)},e.prototype.getPath=function(t){var e,r,i=new Xt(B(this.element,t.styleSheets,"d")||"").unshort().unarc().abs(),n=new M;return i.iterate((function(t){switch(t[0]){case"M":n.moveTo(t[1],t[2]);break;case"L":n.lineTo(t[1],t[2]);break;case"H":n.lineTo(t[1],r);break;case"V":n.lineTo(e,t[1]);break;case"C":n.curveTo(t[1],t[2],t[3],t[4],t[5],t[6]);break;case"Q":var i=b([e,r],[t[1],t[2]]),a=b([t[3],t[4]],[t[1],t[2]]);n.curveTo(i[0],i[1],a[0],a[1],t[3],t[4]);break;case"Z":n.close()}switch(t[0]){case"M":case"L":e=t[1],r=t[2];break;case"H":e=t[1];break;case"V":r=t[1];break;case"C":e=t[5],r=t[6];break;case"Q":e=t[3],r=t[4]}})),n},e}(dt),$t=/^\s*data:(([^/,;]+\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,((?:.|\s)*)$/i,Kt=function(t){function e(r,i){var n=t.call(this,r,i)||this;return n.imageLoadingPromise=null,n.imageUrl=n.element.getAttribute("xlink:href")||n.element.getAttribute("href"),n.imageUrl&&(n.imageLoadingPromise=e.fetchImageData(n.imageUrl)),n}return i(e,t),e.prototype.renderCore=function(t){return a(this,void 0,void 0,(function(){var r,i,n,a,o,l,u,h,c,p,d,g,y,v,b,x,S,w;return s(this,(function(s){switch(s.label){case 0:return this.imageLoadingPromise?(t.pdf.setCurrentTransformationMatrix(t.transform),r=parseFloat(B(this.element,t.styleSheets,"width")||"0"),i=parseFloat(B(this.element,t.styleSheets,"height")||"0"),n=parseFloat(B(this.element,t.styleSheets,"x")||"0"),a=parseFloat(B(this.element,t.styleSheets,"y")||"0"),!isFinite(r)||r<=0||!isFinite(i)||i<=0?[2]:[4,this.imageLoadingPromise]):[2];case 1:return o=s.sent(),l=o.data,0!==(u=o.format).indexOf("svg")?[3,3]:(h=new DOMParser,c=h.parseFromString(l,"image/svg+xml").firstElementChild,(!(p=this.element.getAttribute("preserveAspectRatio"))||p.indexOf("defer")<0||!c.getAttribute("preserveAspectRatio"))&&c.setAttribute("preserveAspectRatio",p||""),c.setAttribute("x",String(n)),c.setAttribute("y",String(a)),c.setAttribute("width",String(r)),c.setAttribute("height",String(i)),[4,ue(c,d={}).render(new f(t.pdf,{refsHandler:new m(d),styleSheets:t.styleSheets,viewport:new vt(r,i),svg2pdfParameters:t.svg2pdfParameters,textMeasure:t.textMeasure}))]);case 2:return s.sent(),[2];case 3:g="data:image/".concat(u,";base64,").concat(btoa(l)),s.label=4;case 4:return s.trys.push([4,6,,7]),[4,e.getImageDimensions(g)];case 5:return y=s.sent(),v=y[0],b=y[1],x=[0,0,v,b],S=K(this.element,x,n,a,r,i,t),t.pdf.setCurrentTransformationMatrix(S),t.pdf.addImage(g,"",0,0,v,b),[3,7];case 6:return w=s.sent(),"object"==typeof console&&console.warn&&console.warn("Could not load image ".concat(this.imageUrl,". \n").concat(w)),[3,7];case 7:return[2]}}))}))},e.prototype.getBoundingBoxCore=function(t){return $(this.element,t)},e.prototype.computeNodeTransformCore=function(t){return t.pdf.unitMatrix},e.prototype.isVisible=function(t,e){return N(this,t,e)},e.fetchImageData=function(t){return a(this,void 0,void 0,(function(){var r,i,n,a,o;return s(this,(function(s){switch(s.label){case 0:if(!(n=t.match($t)))return[3,1];if(a=n[2],"image"!==(o=a.split("/"))[0])throw new Error("Unsupported image URL: ".concat(t));return i=o[1],r=n[5],"base64"===n[4]?(r=r.replace(/\s/g,""),r=atob(r)):r=decodeURIComponent(r),[3,3];case 1:return[4,e.fetchImage(t)];case 2:r=s.sent(),i=t.substring(t.lastIndexOf(".")+1),s.label=3;case 3:return[2,{data:r,format:i}]}}))}))},e.fetchImage=function(t){return new Promise((function(e,r){var i=new XMLHttpRequest;i.open("GET",t,!0),i.responseType="arraybuffer",i.onload=function(){if(200!==i.status)throw new Error("Error ".concat(i.status,": Failed to load image '").concat(t,"'"));for(var r=new Uint8Array(i.response),n="",a=0;a<r.length;a++)n+=String.fromCharCode(r[a]);e(n)},i.onerror=r,i.onabort=r,i.send(null)}))},e.getMimeType=function(t){switch(t=t.toLowerCase()){case"jpg":case"jpeg":return"image/jpeg";default:return"image/".concat(t)}},e.getImageDimensions=function(t){return new Promise((function(e,r){var i=new Image;i.onload=function(){e([i.width,i.height])},i.onerror=r,i.src=t}))},e}(pt),Zt=function(t){function e(e,r,i){var n=t.call(this,!0,r,i)||this;return n.closed=e,n}return i(e,t),e.prototype.getPath=function(t){if(!this.element.hasAttribute("points")||""===this.element.getAttribute("points"))return null;var r=e.parsePointsString(this.element.getAttribute("points")),i=new M;if(r.length<1)return i;i.moveTo(r[0][0],r[0][1]);for(var n=1;n<r.length;n++)i.lineTo(r[n][0],r[n][1]);return this.closed&&i.close(),i},e.prototype.isVisible=function(t,e){return N(this,t,e)},e.prototype.computeNodeTransformCore=function(t){return t.pdf.unitMatrix},e.parsePointsString=function(t){for(var e=V(t),r=[],i=0;i<e.length-1;i+=2){var n=e[i],a=e[i+1];r.push([n,a])}return r},e}(dt),Jt=function(t){function e(e,r){return t.call(this,!0,e,r)||this}return i(e,t),e}(Zt),te=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.render=function(t){return Promise.resolve()},e.prototype.getBoundingBoxCore=function(t){return[0,0,0,0]},e.prototype.computeNodeTransformCore=function(t){return t.pdf.unitMatrix},e.prototype.isVisible=function(t,e){return N(this,t,e)},e}(J),ee=function(t){function r(){return null!==t&&t.apply(this,arguments)||this}return i(r,t),r.prototype.apply=function(t){return a(this,void 0,void 0,(function(){var r,i,n,a,o,l;return s(this,(function(s){switch(s.label){case 0:r=this.computeNodeTransform(t),i=this.getBoundingBox(t),t.pdf.beginFormObject(i[0],i[1],i[2],i[3],r),n=c.getContextColors(t),function(t){var r=t.attributeState,i=t.pdf,n=1,a=1;n*=r.fillOpacity,n*=r.opacity,r.fill instanceof h&&void 0!==r.fill.color.a&&(n*=r.fill.color.a),a*=r.strokeOpacity,a*=r.opacity,r.stroke instanceof h&&void 0!==r.stroke.color.a&&(a*=r.stroke.color.a);var s,o={};if(o.opacity=n,o["stroke-opacity"]=a,i.setGState(new e.GState(o)),r.fill&&r.fill instanceof h&&r.fill.color.ok?i.setFillColor(r.fill.color.r,r.fill.color.g,r.fill.color.b):i.setFillColor(0,0,0),i.setLineWidth(r.strokeWidth),r.stroke instanceof h?i.setDrawColor(r.stroke.color.r,r.stroke.color.g,r.stroke.color.b):i.setDrawColor(0,0,0),i.setLineCap(r.strokeLinecap),i.setLineJoin(r.strokeLinejoin),r.strokeDasharray?i.setLineDashPattern(r.strokeDasharray,r.strokeDashoffset):i.setLineDashPattern([],0),i.setLineMiterLimit(r.strokeMiterlimit),s=U.hasOwnProperty(r.fontFamily)?U[r.fontFamily]:r.fontFamily,r.fill&&r.fill instanceof h&&r.fill.color.ok){var l=r.fill.color;i.setTextColor(l.r,l.g,l.b)}else i.setTextColor(0,0,0);var u="";"bold"===r.fontWeight&&(u="bold"),"italic"===r.fontStyle&&(u+="italic"),""===u&&(u="normal"),void 0!==s||void 0!==u?(void 0===s&&(s=U.hasOwnProperty(r.fontFamily)?U[r.fontFamily]:r.fontFamily),i.setFont(s,u)):i.setFont("helvetica",u),i.setFontSize(r.fontSize*i.internal.scaleFactor)}(a=new f(t.pdf,{refsHandler:t.refsHandler,styleSheets:t.styleSheets,viewport:t.viewport,svg2pdfParameters:t.svg2pdfParameters,textMeasure:t.textMeasure,attributeState:Object.assign(c.default(),n)})),o=0,l=this.children,s.label=1;case 1:return o<l.length?[4,l[o].render(a)]:[3,4];case 2:s.sent(),s.label=3;case 3:return o++,[3,1];case 4:return t.pdf.endFormObject(a.refsHandler.generateKey(this.element.getAttribute("id"),n)),[2]}}))}))},r.prototype.getBoundingBoxCore=function(t){var e,r=this.element.getAttribute("viewBox");return r&&(e=V(r)),[e&&e[0]||0,e&&e[1]||0,e&&e[2]||parseFloat(this.element.getAttribute("markerWidth")||"3"),e&&e[3]||parseFloat(this.element.getAttribute("markerHeight")||"3")]},r.prototype.computeNodeTransformCore=function(t){var e,r=parseFloat(this.element.getAttribute("refX")||"0"),i=parseFloat(this.element.getAttribute("refY")||"0"),n=this.element.getAttribute("viewBox");if(n){var a=V(n);e=K(this.element,a,0,0,parseFloat(this.element.getAttribute("markerWidth")||"3"),parseFloat(this.element.getAttribute("markerHeight")||"3"),t,!0),e=t.pdf.matrixMult(t.pdf.Matrix(1,0,0,1,-r,-i),e)}else e=t.pdf.Matrix(1,0,0,1,-r,-i);return e},r.prototype.isVisible=function(t,e){return E(this,t,e)},r}(tt),re=function(t){function e(e,r){return t.call(this,e,r)||this}return i(e,t),e.prototype.getR=function(t){var e;return null!==(e=this.r)&&void 0!==e?e:this.r=parseFloat(B(this.element,t.styleSheets,"r")||"0")},e.prototype.getRx=function(t){return this.getR(t)},e.prototype.getRy=function(t){return this.getR(t)},e}(St),ie=function(t){function e(e,r){return t.call(this,!1,e,r)||this}return i(e,t),e}(Zt),ne=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.renderCore=function(t){return a(this,void 0,void 0,(function(){var e,r;return s(this,(function(i){switch(i.label){case 0:e=0,r=this.children,i.label=1;case 1:return e<r.length?[4,r[e].render(t)]:[3,4];case 2:i.sent(),i.label=3;case 3:return e++,[3,1];case 4:return[2]}}))}))},e.prototype.getBoundingBoxCore=function(t){return Q(t,this)},e}(ft),ae=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.isVisible=function(t,e){return E(this,t,e)},e.prototype.render=function(e){return a(this,void 0,void 0,(function(){var r,i,n,a,o;return s(this,(function(s){switch(s.label){case 0:return this.isVisible("hidden"!==e.attributeState.visibility,e)?(r=this.getX(e),i=this.getY(e),n=this.getWidth(e),a=this.getHeight(e),e.pdf.saveGraphicsState(),o=e.transform,this.element.hasAttribute("transform")&&(o=e.pdf.matrixMult(Z(this.element.getAttribute("transform"),e),o)),e.pdf.setCurrentTransformationMatrix(o),e.withinUse||"visible"===B(this.element,e.styleSheets,"overflow")||e.pdf.rect(r,i,n,a).clip().discardPath(),[4,t.prototype.render.call(this,e.clone({transform:e.pdf.unitMatrix,viewport:e.withinUse?e.viewport:new vt(n,a)}))]):[2];case 1:return s.sent(),e.pdf.restoreGraphicsState(),[2]}}))}))},e.prototype.computeNodeTransform=function(t){return this.computeNodeTransformCore(t)},e.prototype.computeNodeTransformCore=function(t){if(t.withinUse)return t.pdf.unitMatrix;var e,r=this.getX(t),i=this.getY(t),n=this.getViewBox();if(n){var a=this.getWidth(t),s=this.getHeight(t);e=K(this.element,n,r,i,a,s,t)}else e=t.pdf.Matrix(1,0,0,1,r,i);return e},e.prototype.getWidth=function(t){if(void 0!==this.width)return this.width;var e,r,i=t.svg2pdfParameters;if(this.isOutermostSvg(t))if(null!=i.width)e=i.width;else if(r=B(this.element,t.styleSheets,"width"))e=parseFloat(r);else{var n=this.getViewBox();if(n&&(null!=i.height||B(this.element,t.styleSheets,"height"))){var a=n[2]/n[3];e=this.getHeight(t)*a}else e=Math.min(300,t.viewport.width,2*t.viewport.height)}else e=(r=B(this.element,t.styleSheets,"width"))?parseFloat(r):t.viewport.width;return this.width=e},e.prototype.getHeight=function(t){if(void 0!==this.height)return this.height;var e,r,i=t.svg2pdfParameters;if(this.isOutermostSvg(t))if(null!=i.height)e=i.height;else if(r=B(this.element,t.styleSheets,"height"))e=parseFloat(r);else{var n=this.getViewBox();if(n){var a=n[2]/n[3];e=this.getWidth(t)/a}else e=Math.min(150,t.viewport.width/2,t.viewport.height)}else e=(r=B(this.element,t.styleSheets,"height"))?parseFloat(r):t.viewport.height;return this.height=e},e.prototype.getX=function(t){if(void 0!==this.x)return this.x;if(this.isOutermostSvg(t))return this.x=0;var e=B(this.element,t.styleSheets,"x");return this.x=e?parseFloat(e):0},e.prototype.getY=function(t){if(void 0!==this.y)return this.y;if(this.isOutermostSvg(t))return this.y=0;var e=B(this.element,t.styleSheets,"y");return this.y=e?parseFloat(e):0},e.prototype.getViewBox=function(){if(void 0!==this.viewBox)return this.viewBox;var t=this.element.getAttribute("viewBox");return this.viewBox=t?V(t):void 0},e.prototype.isOutermostSvg=function(t){return t.svg2pdfParameters.element===this.element},e}(ne),se=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.isVisible=function(t,e){return E(this,t,e)},e.prototype.computeNodeTransformCore=function(t){return t.pdf.unitMatrix},e}(ne),oe=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.renderCore=function(e){return a(this,void 0,void 0,(function(){var r,i,n,a;return s(this,(function(s){switch(s.label){case 0:return[4,t.prototype.renderCore.call(this,e)];case 1:return s.sent(),(r=B(this.element,e.styleSheets,"href"))&&(i=this.getBoundingBox(e),n=e.pdf.internal.scaleFactor,a=e.pdf.internal.pageSize.getHeight(),e.pdf.link(n*(i[0]*e.transform.sx+e.transform.tx),a-n*(i[1]*e.transform.sy+e.transform.ty),n*i[2],n*i[3],{url:r})),[2]}}))}))},e}(se),le=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.apply=function(t){return a(this,void 0,void 0,(function(){var e,r,i,n,a;return s(this,(function(s){switch(s.label){case 0:if(!this.isVisible(!0,t))return[2];e=t.pdf.matrixMult(this.computeNodeTransform(t),t.transform),t.pdf.setCurrentTransformationMatrix(e),r=0,i=this.children,s.label=1;case 1:return r<i.length?[4,i[r].render(new f(t.pdf,{refsHandler:t.refsHandler,styleSheets:t.styleSheets,viewport:t.viewport,withinClipPath:!0,svg2pdfParameters:t.svg2pdfParameters,textMeasure:t.textMeasure}))]:[3,4];case 2:s.sent(),s.label=3;case 3:return r++,[3,1];case 4:return n=this.children.length>0&&!!B(this.children[0].element,t.styleSheets,"clip-rule"),a=n?this.getClipRuleAttr(this.children[0].element,t.styleSheets):this.getClipRuleAttr(this.element,t.styleSheets),t.pdf.clip(a).discardPath(),t.pdf.setCurrentTransformationMatrix(e.inversed()),[2]}}))}))},e.prototype.getBoundingBoxCore=function(t){return Q(t,this)},e.prototype.isVisible=function(t,e){return E(this,t,e)},e.prototype.getClipRuleAttr=function(t,e){return"evenodd"===B(t,e,"clip-rule")?"evenodd":void 0},e}(tt);function ue(t,e){var r,i=[];switch(function(t,e){for(var r=[],i=0;i<t.childNodes.length;i++){var n=t.childNodes[i];"#"!==n.nodeName.charAt(0)&&r.push(n)}for(i=0;i<r.length;i++)e(i,r[i])}(t,(function(t,r){return i.push(ue(r,e))})),t.tagName.toLowerCase()){case"a":r=new oe(t,i);break;case"g":r=new se(t,i);break;case"circle":r=new re(t,i);break;case"clippath":r=new le(t,i);break;case"ellipse":r=new wt(t,i);break;case"lineargradient":r=new rt(t,i);break;case"image":r=new Kt(t,i);break;case"line":r=new mt(t,i);break;case"marker":r=new ee(t,i);break;case"path":r=new Qt(t,i);break;case"pattern":r=new at(t,i);break;case"polygon":r=new Jt(t,i);break;case"polyline":r=new ie(t,i);break;case"radialgradient":r=new it(t,i);break;case"rect":r=new xt(t,i);break;case"svg":r=new ae(t,i);break;case"symbol":r=new yt(t,i);break;case"text":r=new Wt(t,i);break;case"use":r=new bt(t,i);break;default:r=new te(t,i)}if(null!=e&&r.element.hasAttribute("id")){var n=g(r.element.id,{isIdentifier:!0});e[n]=e[n]||r}return r.children.forEach((function(t){return t.setParent(r)})),r}var he=function(t){var e,r,i=t,n={a:0,b:0,c:0},a=[];return e=function(e,r){var s,o,l,u,h,c;if(e.test(i))for(o=0,l=(s=i.match(e)).length;o<l;o+=1)n[r]+=1,u=s[o],h=i.indexOf(u),c=u.length,a.push({selector:t.substr(h,c),type:r,index:h,length:c}),i=i.replace(u,Array(c+1).join(" "))},(r=function(t){var e,r,n,a;if(t.test(i))for(r=0,n=(e=i.match(t)).length;r<n;r+=1)a=e[r],i=i.replace(a,Array(a.length+1).join("A"))})(/\\[0-9A-Fa-f]{6}\s?/g),r(/\\[0-9A-Fa-f]{1,5}\s/g),r(/\\./g),function(){var t,e,r,n,a=/{[^]*/gm;if(a.test(i))for(e=0,r=(t=i.match(a)).length;e<r;e+=1)n=t[e],i=i.replace(n,Array(n.length+1).join(" "))}(),e(/(\[[^\]]+\])/g,"b"),e(/(#[^\#\s\+>~\.\[:\)]+)/g,"a"),e(/(\.[^\s\+>~\.\[:\)]+)/g,"b"),e(/(::[^\s\+>~\.\[:]+|:first-line|:first-letter|:before|:after)/gi,"c"),e(/(:(?!not|global|local)[\w-]+\([^\)]*\))/gi,"b"),e(/(:(?!not|global|local)[^\s\+>~\.\[:]+)/g,"b"),i=(i=(i=(i=(i=(i=i.replace(/[\*\s\+>~]/g," ")).replace(/[#\.]/g," ")).replace(/:not/g,"    ")).replace(/:local/g,"      ")).replace(/:global/g,"       ")).replace(/[\(\)]/g," "),e(/([^\s\+>~\.\[:]+)/g,"c"),a.sort((function(t,e){return t.index-e.index})),{selector:t,specificity:"0,"+n.a.toString()+","+n.b.toString()+","+n.c.toString(),specificityArray:[0,n.a,n.b,n.c],parts:a}},ce=function(){function t(t,e){this.rootSvg=t,this.loadExternalSheets=e,this.styleSheets=[]}return t.prototype.load=function(){return a(this,void 0,void 0,(function(){var t;return s(this,(function(e){switch(e.label){case 0:return[4,this.collectStyleSheetTexts()];case 1:return t=e.sent(),this.parseCssSheets(t),[2]}}))}))},t.prototype.collectStyleSheetTexts=function(){return a(this,void 0,void 0,(function(){var e,r,i,n,a;return s(this,(function(s){switch(s.label){case 0:if(e=[],this.loadExternalSheets&&this.rootSvg.ownerDocument)for(n=0;n<this.rootSvg.ownerDocument.childNodes.length;n++)"xml-stylesheet"===(r=this.rootSvg.ownerDocument.childNodes[n]).nodeName&&"string"==typeof r.data&&e.push(t.loadSheet(r.data.match(/href=["'].*?["']/)[0].split("=")[1].slice(1,-1)));for(i=this.rootSvg.querySelectorAll("style,link"),n=0;n<i.length;n++)P(a=i[n],"style")?e.push(a.textContent):this.loadExternalSheets&&P(a,"link")&&"stylesheet"===a.getAttribute("rel")&&a.hasAttribute("href")&&e.push(t.loadSheet(a.getAttribute("href")));return[4,Promise.all(e)];case 1:return[2,s.sent().filter((function(t){return null!==t}))]}}))}))},t.prototype.parseCssSheets=function(e){for(var r=document.implementation.createHTMLDocument(""),i=0,n=e;i<n.length;i++){var a=n[i],s=r.createElement("style");s.textContent=a,r.body.appendChild(s);var o=s.sheet;if(o instanceof CSSStyleSheet){for(var l=o.cssRules.length-1;l>=0;l--){var u=o.cssRules[l];if(u instanceof CSSStyleRule){var h=u;if(h.selectorText.indexOf(",")>=0){o.deleteRule(l);for(var c=h.cssText.substring(h.selectorText.length),f=t.splitSelectorAtCommas(h.selectorText),p=0;p<f.length;p++)o.insertRule(f[p]+c,l+p)}}else o.deleteRule(l)}this.styleSheets.push(o)}}},t.splitSelectorAtCommas=function(t){for(var e,r=/,|["']/g,i=/[^\\]["]/g,n=/[^\\][']/g,a=[],s="initial",o=-1,l=i,u=0;u<t.length;)switch(s){case"initial":r.lastIndex=u,(e=r.exec(t))?(","===e[0]?(a.push(t.substring(o+1,r.lastIndex-1).trim()),o=r.lastIndex-1):(s="withinQuotes",l='"'===e[0]?i:n),u=r.lastIndex):(a.push(t.substring(o+1).trim()),u=t.length);break;case"withinQuotes":l.lastIndex=u,(e=l.exec(t))&&(u=l.lastIndex,s="initial")}return a},t.loadSheet=function(t){return new Promise((function(e,r){var i=new XMLHttpRequest;i.open("GET",t,!0),i.responseType="text",i.onload=function(){200!==i.status&&r(new Error("Error ".concat(i.status,": Failed to load '").concat(t,"'"))),e(i.responseText)},i.onerror=r,i.onabort=r,i.send(null)})).catch((function(){return null}))},t.prototype.getPropertyValue=function(t,e){for(var r=[],i=0,n=this.styleSheets;i<n.length;i++)for(var a=n[i],s=0;s<a.cssRules.length;s++){var o=a.cssRules[s];o.style.getPropertyValue(e)&&t.matches(o.selectorText)&&r.push(o)}if(0!==r.length){var l=function(t,r){var i=t.style.getPropertyPriority(e);return i!==r.style.getPropertyPriority(e)?"important"===i?1:-1:function(t,e){var r,i,n;if("string"==typeof t){if(-1!==t.indexOf(","))throw"Invalid CSS selector";r=he(t).specificityArray}else{if(!Array.isArray(t))throw"Invalid CSS selector or specificity array";if(4!==t.filter((function(t){return"number"==typeof t})).length)throw"Invalid specificity array";r=t}if("string"==typeof e){if(-1!==e.indexOf(","))throw"Invalid CSS selector";i=he(e).specificityArray}else{if(!Array.isArray(e))throw"Invalid CSS selector or specificity array";if(4!==e.filter((function(t){return"number"==typeof t})).length)throw"Invalid specificity array";i=e}for(n=0;n<4;n+=1){if(r[n]<i[n])return-1;if(r[n]>i[n])return 1}return 0}(t.selectorText,r.selectorText)};return r.reduce((function(t,e){return 1===l(t,e)?t:e})).style.getPropertyValue(e)||void 0}},t}(),fe=function(){function t(){this.measureMethods={}}return t.prototype.getTextOffset=function(t,e){var r=e.textAnchor;if("start"===r)return 0;var i=this.measureTextWidth(t,e),n=0;switch(r){case"end":n=i;break;case"middle":n=i/2}return n},t.prototype.measureTextWidth=function(t,e){if(0===t.length)return 0;var r=e.fontFamily;return this.getMeasureFunction(r).call(this,t,e.fontFamily,e.fontSize+"px",e.fontStyle,e.fontWeight)},t.prototype.getMeasurementTextNode=function(){if(!this.textMeasuringTextElement){this.textMeasuringTextElement=document.createElementNS(D,"text");var t=document.createElementNS(D,"svg");t.appendChild(this.textMeasuringTextElement),t.style.setProperty("position","absolute"),t.style.setProperty("visibility","hidden"),document.body.appendChild(t)}return this.textMeasuringTextElement},t.prototype.canvasTextMeasure=function(t,e,r,i,n){var a=document.createElement("canvas").getContext("2d");return null!=a?(a.font=[i,n,r,e].join(" "),a.measureText(t).width):0},t.prototype.svgTextMeasure=function(t,e,r,i,n,a){void 0===a&&(a=this.getMeasurementTextNode());var s=a;return s.setAttribute("font-family",e),s.setAttribute("font-size",r),s.setAttribute("font-style",i),s.setAttribute("font-weight",n),s.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),s.textContent=t,s.getBBox().width},t.prototype.getMeasureFunction=function(e){var r=this.measureMethods[e];if(!r){var i="16px",n="normal",a="normal",s=this.canvasTextMeasure(t.testString,e,i,n,a),o=this.svgTextMeasure(t.testString,e,i,n,a);r=Math.abs(s-o)<t.epsilon?this.canvasTextMeasure:this.svgTextMeasure,this.measureMethods[e]=r}return r},t.prototype.cleanupTextMeasuring=function(){if(this.textMeasuringTextElement){var t=this.textMeasuringTextElement.parentNode;t&&document.body.removeChild(t),this.textMeasuringTextElement=void 0}},t.testString="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ 0123456789!\"$%&/()=?'\\+*-_.:,;^}][{#~|<>",t.epsilon=.1,t}();function pe(t,e){return a(this,arguments,void 0,(function(t,e,r){var i,a,o,l,u,h,c,p,d,g,y,v,b,x;return void 0===r&&(r={}),s(this,(function(s){switch(s.label){case 0:return i=null!==(v=r.x)&&void 0!==v?v:0,a=null!==(b=r.y)&&void 0!==b?b:0,o=null!==(x=r.loadExternalStyleSheets)&&void 0!==x&&x,u=new m(l={}),[4,(h=new ce(t,o)).load()];case 1:return s.sent(),c=new vt(e.internal.pageSize.getWidth(),e.internal.pageSize.getHeight()),p=n(n({},r),{element:t}),d=new fe,g=new f(e,{refsHandler:u,styleSheets:h,viewport:c,svg2pdfParameters:p,textMeasure:d}),e.advancedAPI(),e.saveGraphicsState(),e.setCurrentTransformationMatrix(e.Matrix(1,0,0,1,i,a)),e.setLineWidth(g.attributeState.strokeWidth),y=g.attributeState.fill.color,e.setFillColor(y.r,y.g,y.b),e.setFont(g.attributeState.fontFamily),e.setFontSize(g.attributeState.fontSize*e.internal.scaleFactor),[4,ue(t,l).render(g)];case 2:return s.sent(),e.restoreGraphicsState(),e.compatAPI(),g.textMeasure.cleanupTextMeasuring(),[2,e]}}))}))}e.jsPDF.API.svg=function(t,e){return void 0===e&&(e={}),pe(t,this,e)},t.svg2pdf=pe}));
//# sourceMappingURL=svg2pdf.umd.min.js.map
