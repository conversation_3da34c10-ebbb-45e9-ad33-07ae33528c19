!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/highcharts-3d
 * @requires highcharts
 *
 * 3D features for Highcharts JS
 *
 * (c) 2009-2025 Torstein Honsi
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.Color,t._Highcharts.SeriesRegistry,t._Highcharts.RendererRegistry,t._Highcharts.Series,t._Highcharts.StackItem,t._Highcharts.Axis,t._Highcharts.Series.types.scatter):"function"==typeof define&&define.amd?define("highcharts/highcharts-3d",["highcharts/highcharts"],function(t){return e(t,t.Color,t.SeriesRegistry,t.RendererRegistry,t.Series,t.StackItem,t.Axis,t.Series,["types"],["scatter"])}):"object"==typeof exports?exports["highcharts/highcharts-3d"]=e(t._Highcharts,t._Highcharts.Color,t._Highcharts.SeriesRegistry,t._Highcharts.RendererRegistry,t._Highcharts.Series,t._Highcharts.StackItem,t._Highcharts.Axis,t._Highcharts.Series.types.scatter):t.Highcharts=e(t.Highcharts,t.Highcharts.Color,t.Highcharts.SeriesRegistry,t.Highcharts.RendererRegistry,t.Highcharts.Series,t.Highcharts.StackItem,t.Highcharts.Axis,t.Highcharts.Series.types.scatter)}("undefined"==typeof window?this:window,(t,e,i,s,o,a,r,h)=>(()=>{"use strict";var n,l,p={184:t=>{t.exports=a},512:t=>{t.exports=i},532:t=>{t.exports=r},608:t=>{t.exports=s},620:t=>{t.exports=e},632:t=>{t.exports=h},820:t=>{t.exports=o},944:e=>{e.exports=t}},c={};function d(t){var e=c[t];if(void 0!==e)return e.exports;var i=c[t]={exports:{}};return p[t](i,i.exports,d),i.exports}d.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return d.d(e,{a:e}),e},d.d=(t,e)=>{for(var i in e)d.o(e,i)&&!d.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},d.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var x={};d.d(x,{default:()=>eZ});var y=d(944),f=d.n(y),u=d(620),z=d.n(u);let{deg2rad:g}=f(),{pick:b}=f();function m(t,e,i,s){let o=e.options.chart.options3d,a=b(s,!!i&&e.inverted),r={x:e.plotWidth/2,y:e.plotHeight/2,z:o.depth/2,vd:b(o.depth,1)*b(o.viewDistance,0)},h=e.scale3d||1,n=g*o.beta*(a?-1:1),l=g*o.alpha*(a?-1:1),p={cosA:Math.cos(l),cosB:Math.cos(-n),sinA:Math.sin(l),sinB:Math.sin(-n)};return i||(r.x+=e.plotLeft,r.y+=e.plotTop),t.map(function(t){var e,i,s;let o=(e=(a?t.y:t.x)-r.x,i=(a?t.x:t.y)-r.y,s=(t.z||0)-r.z,{x:p.cosB*e-p.sinB*s,y:-p.sinA*p.sinB*e+p.cosA*i-p.cosB*p.sinA*s,z:p.cosA*p.sinB*e+p.sinA*i+p.cosA*p.cosB*s}),n=v(o,r,r.vd);return n.x=n.x*h+r.x,n.y=n.y*h+r.y,n.z=o.z*h+r.z,{x:a?n.y:n.x,y:a?n.x:n.y,z:n.z}})}function v(t,e,i){let s=i>0&&i<Number.POSITIVE_INFINITY?i/(t.z+e.z+i):1;return{x:t.x*s,y:t.y*s}}function M(t){let e=0,i,s;for(i=0;i<t.length;i++)s=(i+1)%t.length,e+=t[i].x*t[s].y-t[s].x*t[i].y;return e/2}let P={perspective:m,perspective3D:v,pointCameraDistance:function(t,e){let i=e.options.chart.options3d,s={x:e.plotWidth/2,y:e.plotHeight/2,z:b(i.depth,1)*b(i.viewDistance,0)+i.depth};return Math.sqrt(Math.pow(s.x-b(t.plotX,t.x),2)+Math.pow(s.y-b(t.plotY,t.y),2)+Math.pow(s.z-b(t.plotZ,t.z),2))},shapeArea:M,shapeArea3D:function(t,e,i){return M(m(t,e,i))}},{parse:A}=z(),{defaultOptions:k}=f(),{perspective:S,shapeArea3D:w}=P,{addEvent:I,isArray:D,merge:L,pick:T,wrap:X}=f();!function(t){function e(t){this.is3d()&&"scatter"===t.options.type&&(t.options.type="scatter3d")}function i(){if(this.chart3d&&this.is3d()){let t=this.renderer,e=this.options.chart.options3d,i=this.chart3d.get3dFrame(),s=this.plotLeft,o=this.plotLeft+this.plotWidth,a=this.plotTop,r=this.plotTop+this.plotHeight,h=e.depth,n=s-(i.left.visible?i.left.size:0),l=o+(i.right.visible?i.right.size:0),p=a-(i.top.visible?i.top.size:0),c=r+(i.bottom.visible?i.bottom.size:0),d=0-(i.front.visible?i.front.size:0),x=h+(i.back.visible?i.back.size:0),y=this.hasRendered?"animate":"attr";this.chart3d.frame3d=i,this.frameShapes||(this.frameShapes={bottom:t.polyhedron().add(),top:t.polyhedron().add(),left:t.polyhedron().add(),right:t.polyhedron().add(),back:t.polyhedron().add(),front:t.polyhedron().add()}),this.frameShapes.bottom[y]({class:"highcharts-3d-frame highcharts-3d-frame-bottom",zIndex:i.bottom.frontFacing?-1e3:1e3,faces:[{fill:A(i.bottom.color).brighten(.1).get(),vertexes:[{x:n,y:c,z:d},{x:l,y:c,z:d},{x:l,y:c,z:x},{x:n,y:c,z:x}],enabled:i.bottom.visible},{fill:A(i.bottom.color).brighten(.1).get(),vertexes:[{x:s,y:r,z:h},{x:o,y:r,z:h},{x:o,y:r,z:0},{x:s,y:r,z:0}],enabled:i.bottom.visible},{fill:A(i.bottom.color).brighten(-.1).get(),vertexes:[{x:n,y:c,z:d},{x:n,y:c,z:x},{x:s,y:r,z:h},{x:s,y:r,z:0}],enabled:i.bottom.visible&&!i.left.visible},{fill:A(i.bottom.color).brighten(-.1).get(),vertexes:[{x:l,y:c,z:x},{x:l,y:c,z:d},{x:o,y:r,z:0},{x:o,y:r,z:h}],enabled:i.bottom.visible&&!i.right.visible},{fill:A(i.bottom.color).get(),vertexes:[{x:l,y:c,z:d},{x:n,y:c,z:d},{x:s,y:r,z:0},{x:o,y:r,z:0}],enabled:i.bottom.visible&&!i.front.visible},{fill:A(i.bottom.color).get(),vertexes:[{x:n,y:c,z:x},{x:l,y:c,z:x},{x:o,y:r,z:h},{x:s,y:r,z:h}],enabled:i.bottom.visible&&!i.back.visible}]}),this.frameShapes.top[y]({class:"highcharts-3d-frame highcharts-3d-frame-top",zIndex:i.top.frontFacing?-1e3:1e3,faces:[{fill:A(i.top.color).brighten(.1).get(),vertexes:[{x:n,y:p,z:x},{x:l,y:p,z:x},{x:l,y:p,z:d},{x:n,y:p,z:d}],enabled:i.top.visible},{fill:A(i.top.color).brighten(.1).get(),vertexes:[{x:s,y:a,z:0},{x:o,y:a,z:0},{x:o,y:a,z:h},{x:s,y:a,z:h}],enabled:i.top.visible},{fill:A(i.top.color).brighten(-.1).get(),vertexes:[{x:n,y:p,z:x},{x:n,y:p,z:d},{x:s,y:a,z:0},{x:s,y:a,z:h}],enabled:i.top.visible&&!i.left.visible},{fill:A(i.top.color).brighten(-.1).get(),vertexes:[{x:l,y:p,z:d},{x:l,y:p,z:x},{x:o,y:a,z:h},{x:o,y:a,z:0}],enabled:i.top.visible&&!i.right.visible},{fill:A(i.top.color).get(),vertexes:[{x:n,y:p,z:d},{x:l,y:p,z:d},{x:o,y:a,z:0},{x:s,y:a,z:0}],enabled:i.top.visible&&!i.front.visible},{fill:A(i.top.color).get(),vertexes:[{x:l,y:p,z:x},{x:n,y:p,z:x},{x:s,y:a,z:h},{x:o,y:a,z:h}],enabled:i.top.visible&&!i.back.visible}]}),this.frameShapes.left[y]({class:"highcharts-3d-frame highcharts-3d-frame-left",zIndex:i.left.frontFacing?-1e3:1e3,faces:[{fill:A(i.left.color).brighten(.1).get(),vertexes:[{x:n,y:c,z:d},{x:s,y:r,z:0},{x:s,y:r,z:h},{x:n,y:c,z:x}],enabled:i.left.visible&&!i.bottom.visible},{fill:A(i.left.color).brighten(.1).get(),vertexes:[{x:n,y:p,z:x},{x:s,y:a,z:h},{x:s,y:a,z:0},{x:n,y:p,z:d}],enabled:i.left.visible&&!i.top.visible},{fill:A(i.left.color).brighten(-.1).get(),vertexes:[{x:n,y:c,z:x},{x:n,y:p,z:x},{x:n,y:p,z:d},{x:n,y:c,z:d}],enabled:i.left.visible},{fill:A(i.left.color).brighten(-.1).get(),vertexes:[{x:s,y:a,z:h},{x:s,y:r,z:h},{x:s,y:r,z:0},{x:s,y:a,z:0}],enabled:i.left.visible},{fill:A(i.left.color).get(),vertexes:[{x:n,y:c,z:d},{x:n,y:p,z:d},{x:s,y:a,z:0},{x:s,y:r,z:0}],enabled:i.left.visible&&!i.front.visible},{fill:A(i.left.color).get(),vertexes:[{x:n,y:p,z:x},{x:n,y:c,z:x},{x:s,y:r,z:h},{x:s,y:a,z:h}],enabled:i.left.visible&&!i.back.visible}]}),this.frameShapes.right[y]({class:"highcharts-3d-frame highcharts-3d-frame-right",zIndex:i.right.frontFacing?-1e3:1e3,faces:[{fill:A(i.right.color).brighten(.1).get(),vertexes:[{x:l,y:c,z:x},{x:o,y:r,z:h},{x:o,y:r,z:0},{x:l,y:c,z:d}],enabled:i.right.visible&&!i.bottom.visible},{fill:A(i.right.color).brighten(.1).get(),vertexes:[{x:l,y:p,z:d},{x:o,y:a,z:0},{x:o,y:a,z:h},{x:l,y:p,z:x}],enabled:i.right.visible&&!i.top.visible},{fill:A(i.right.color).brighten(-.1).get(),vertexes:[{x:o,y:a,z:0},{x:o,y:r,z:0},{x:o,y:r,z:h},{x:o,y:a,z:h}],enabled:i.right.visible},{fill:A(i.right.color).brighten(-.1).get(),vertexes:[{x:l,y:c,z:d},{x:l,y:p,z:d},{x:l,y:p,z:x},{x:l,y:c,z:x}],enabled:i.right.visible},{fill:A(i.right.color).get(),vertexes:[{x:l,y:p,z:d},{x:l,y:c,z:d},{x:o,y:r,z:0},{x:o,y:a,z:0}],enabled:i.right.visible&&!i.front.visible},{fill:A(i.right.color).get(),vertexes:[{x:l,y:c,z:x},{x:l,y:p,z:x},{x:o,y:a,z:h},{x:o,y:r,z:h}],enabled:i.right.visible&&!i.back.visible}]}),this.frameShapes.back[y]({class:"highcharts-3d-frame highcharts-3d-frame-back",zIndex:i.back.frontFacing?-1e3:1e3,faces:[{fill:A(i.back.color).brighten(.1).get(),vertexes:[{x:l,y:c,z:x},{x:n,y:c,z:x},{x:s,y:r,z:h},{x:o,y:r,z:h}],enabled:i.back.visible&&!i.bottom.visible},{fill:A(i.back.color).brighten(.1).get(),vertexes:[{x:n,y:p,z:x},{x:l,y:p,z:x},{x:o,y:a,z:h},{x:s,y:a,z:h}],enabled:i.back.visible&&!i.top.visible},{fill:A(i.back.color).brighten(-.1).get(),vertexes:[{x:n,y:c,z:x},{x:n,y:p,z:x},{x:s,y:a,z:h},{x:s,y:r,z:h}],enabled:i.back.visible&&!i.left.visible},{fill:A(i.back.color).brighten(-.1).get(),vertexes:[{x:l,y:p,z:x},{x:l,y:c,z:x},{x:o,y:r,z:h},{x:o,y:a,z:h}],enabled:i.back.visible&&!i.right.visible},{fill:A(i.back.color).get(),vertexes:[{x:s,y:a,z:h},{x:o,y:a,z:h},{x:o,y:r,z:h},{x:s,y:r,z:h}],enabled:i.back.visible},{fill:A(i.back.color).get(),vertexes:[{x:n,y:c,z:x},{x:l,y:c,z:x},{x:l,y:p,z:x},{x:n,y:p,z:x}],enabled:i.back.visible}]}),this.frameShapes.front[y]({class:"highcharts-3d-frame highcharts-3d-frame-front",zIndex:i.front.frontFacing?-1e3:1e3,faces:[{fill:A(i.front.color).brighten(.1).get(),vertexes:[{x:n,y:c,z:d},{x:l,y:c,z:d},{x:o,y:r,z:0},{x:s,y:r,z:0}],enabled:i.front.visible&&!i.bottom.visible},{fill:A(i.front.color).brighten(.1).get(),vertexes:[{x:l,y:p,z:d},{x:n,y:p,z:d},{x:s,y:a,z:0},{x:o,y:a,z:0}],enabled:i.front.visible&&!i.top.visible},{fill:A(i.front.color).brighten(-.1).get(),vertexes:[{x:n,y:p,z:d},{x:n,y:c,z:d},{x:s,y:r,z:0},{x:s,y:a,z:0}],enabled:i.front.visible&&!i.left.visible},{fill:A(i.front.color).brighten(-.1).get(),vertexes:[{x:l,y:c,z:d},{x:l,y:p,z:d},{x:o,y:a,z:0},{x:o,y:r,z:0}],enabled:i.front.visible&&!i.right.visible},{fill:A(i.front.color).get(),vertexes:[{x:o,y:a,z:0},{x:s,y:a,z:0},{x:s,y:r,z:0},{x:o,y:r,z:0}],enabled:i.front.visible},{fill:A(i.front.color).get(),vertexes:[{x:l,y:c,z:d},{x:n,y:c,z:d},{x:n,y:p,z:d},{x:l,y:p,z:d}],enabled:i.front.visible}]})}}function s(){this.styledMode&&[{name:"darker",slope:.6},{name:"brighter",slope:1.4}].forEach(function(t){this.renderer.definition({tagName:"filter",attributes:{id:"highcharts-"+t.name},children:[{tagName:"feComponentTransfer",children:[{tagName:"feFuncR",attributes:{type:"linear",slope:t.slope}},{tagName:"feFuncG",attributes:{type:"linear",slope:t.slope}},{tagName:"feFuncB",attributes:{type:"linear",slope:t.slope}}]}]})},this)}function o(){let t=this.options;this.is3d()&&(t.series||[]).forEach(function(e){"scatter"===(e.type||t.chart.type||t.chart.defaultSeriesType)&&(e.type="scatter3d")})}function a(){let t=this.options.chart.options3d;if(this.chart3d&&this.is3d()){t&&(t.alpha=t.alpha%360+(t.alpha>=0?0:360),t.beta=t.beta%360+(t.beta>=0?0:360));let e=this.inverted,i=this.clipBox,s=this.margin;i[e?"y":"x"]=-(s[3]||0),i[e?"x":"y"]=-(s[0]||0),i[e?"height":"width"]=this.chartWidth+(s[3]||0)+(s[1]||0),i[e?"width":"height"]=this.chartHeight+(s[0]||0)+(s[2]||0),this.scale3d=1,!0===t.fitToPlot&&(this.scale3d=this.chart3d.getScale(t.depth)),this.chart3d.frame3d=this.chart3d.get3dFrame()}}function r(){this.is3d()&&(this.isDirtyBox=!0)}function h(){this.chart3d&&this.is3d()&&(this.chart3d.frame3d=this.chart3d.get3dFrame())}function n(){this.chart3d||(this.chart3d=new d(this))}function l(t){return this.is3d()||t.apply(this,[].slice.call(arguments,1))}function p(t){let e,i=this.series.length;if(this.is3d())for(;i--;)(e=this.series[i]).translate(),e.render();else t.call(this)}function c(t){t.apply(this,[].slice.call(arguments,1)),this.is3d()&&(this.container.className+=" highcharts-3d-chart")}t.defaultOptions={chart:{options3d:{enabled:!1,alpha:0,beta:0,depth:100,fitToPlot:!0,viewDistance:25,axisLabelPosition:null,frame:{visible:"default",size:1,bottom:{},top:{},left:{},right:{},back:{},front:{}}}}},t.compose=function(d,x){let y=d.prototype,f=x.prototype;y.is3d=function(){return!!this.options.chart.options3d?.enabled},y.propsRequireDirtyBox.push("chart.options3d"),y.propsRequireUpdateSeries.push("chart.options3d"),f.matrixSetter=function(){let t;if(this.pos<1&&(D(this.start)||D(this.end))){let e=this.start||[1,0,0,1,0,0],i=this.end||[1,0,0,1,0,0];t=[];for(let s=0;s<6;s++)t.push(this.pos*i[s]+(1-this.pos)*e[s])}else t=this.end;this.elem.attr(this.prop,t,null,!0)},L(!0,k,t.defaultOptions),I(d,"init",n),I(d,"addSeries",e),I(d,"afterDrawChartBox",i),I(d,"afterGetContainer",s),I(d,"afterInit",o),I(d,"afterSetChartSize",a),I(d,"beforeRedraw",r),I(d,"beforeRender",h),X(y,"isInsidePlot",l),X(y,"renderSeries",p),X(y,"setClassName",c)};class d{constructor(t){this.chart=t}get3dFrame(){let t=this.chart,e=t.options.chart.options3d,i=e.frame,s=t.plotLeft,o=t.plotLeft+t.plotWidth,a=t.plotTop,r=t.plotTop+t.plotHeight,h=e.depth,n=function(e){let i=w(e,t);return i>.5?1:i<-.5?-1:0},l=n([{x:s,y:r,z:h},{x:o,y:r,z:h},{x:o,y:r,z:0},{x:s,y:r,z:0}]),p=n([{x:s,y:a,z:0},{x:o,y:a,z:0},{x:o,y:a,z:h},{x:s,y:a,z:h}]),c=n([{x:s,y:a,z:0},{x:s,y:a,z:h},{x:s,y:r,z:h},{x:s,y:r,z:0}]),d=n([{x:o,y:a,z:h},{x:o,y:a,z:0},{x:o,y:r,z:0},{x:o,y:r,z:h}]),x=n([{x:s,y:r,z:0},{x:o,y:r,z:0},{x:o,y:a,z:0},{x:s,y:a,z:0}]),y=n([{x:s,y:a,z:h},{x:o,y:a,z:h},{x:o,y:r,z:h},{x:s,y:r,z:h}]),f=!1,u=!1,z=!1,g=!1;[].concat(t.xAxis,t.yAxis,t.zAxis).forEach(function(t){t&&(t.horiz?t.opposite?u=!0:f=!0:t.opposite?g=!0:z=!0)});let b=function(t,e,i){let s=["size","color","visible"],o={};for(let e=0;e<s.length;e++){let i=s[e];for(let e=0;e<t.length;e++)if("object"==typeof t[e]){let s=t[e][i];if(null!=s){o[i]=s;break}}}let a=i;return!0===o.visible||!1===o.visible?a=o.visible:"auto"===o.visible&&(a=e>0),{size:T(o.size,1),color:T(o.color,"none"),frontFacing:e>0,visible:a}},m={axes:{},bottom:b([i.bottom,i.top,i],l,f),top:b([i.top,i.bottom,i],p,u),left:b([i.left,i.right,i.side,i],c,z),right:b([i.right,i.left,i.side,i],d,g),back:b([i.back,i.front,i],y,!0),front:b([i.front,i.back,i],x,!1)};if("auto"===e.axisLabelPosition){let e=function(t,e){return t.visible!==e.visible||t.visible&&e.visible&&t.frontFacing!==e.frontFacing},i=[];e(m.left,m.front)&&i.push({y:(a+r)/2,x:s,z:0,xDir:{x:1,y:0,z:0}}),e(m.left,m.back)&&i.push({y:(a+r)/2,x:s,z:h,xDir:{x:0,y:0,z:-1}}),e(m.right,m.front)&&i.push({y:(a+r)/2,x:o,z:0,xDir:{x:0,y:0,z:1}}),e(m.right,m.back)&&i.push({y:(a+r)/2,x:o,z:h,xDir:{x:-1,y:0,z:0}});let n=[];e(m.bottom,m.front)&&n.push({x:(s+o)/2,y:r,z:0,xDir:{x:1,y:0,z:0}}),e(m.bottom,m.back)&&n.push({x:(s+o)/2,y:r,z:h,xDir:{x:-1,y:0,z:0}});let l=[];e(m.top,m.front)&&l.push({x:(s+o)/2,y:a,z:0,xDir:{x:1,y:0,z:0}}),e(m.top,m.back)&&l.push({x:(s+o)/2,y:a,z:h,xDir:{x:-1,y:0,z:0}});let p=[];e(m.bottom,m.left)&&p.push({z:(0+h)/2,y:r,x:s,xDir:{x:0,y:0,z:-1}}),e(m.bottom,m.right)&&p.push({z:(0+h)/2,y:r,x:o,xDir:{x:0,y:0,z:1}});let c=[];e(m.top,m.left)&&c.push({z:(0+h)/2,y:a,x:s,xDir:{x:0,y:0,z:-1}}),e(m.top,m.right)&&c.push({z:(0+h)/2,y:a,x:o,xDir:{x:0,y:0,z:1}});let d=function(e,i,s){if(0===e.length)return null;if(1===e.length)return e[0];let o=S(e,t,!1),a=0;for(let t=1;t<o.length;t++)s*o[t][i]>s*o[a][i]?a=t:s*o[t][i]==s*o[a][i]&&o[t].z<o[a].z&&(a=t);return e[a]};m.axes={y:{left:d(i,"x",-1),right:d(i,"x",1)},x:{top:d(l,"y",-1),bottom:d(n,"y",1)},z:{top:d(c,"y",-1),bottom:d(p,"y",1)}}}else m.axes={y:{left:{x:s,z:0,xDir:{x:1,y:0,z:0}},right:{x:o,z:0,xDir:{x:0,y:0,z:1}}},x:{top:{y:a,z:0,xDir:{x:1,y:0,z:0}},bottom:{y:r,z:0,xDir:{x:1,y:0,z:0}}},z:{top:{x:z?o:s,y:a,xDir:z?{x:0,y:0,z:1}:{x:0,y:0,z:-1}},bottom:{x:z?o:s,y:r,xDir:z?{x:0,y:0,z:1}:{x:0,y:0,z:-1}}}};return m}getScale(t){let e=this.chart,i=e.plotLeft,s=e.plotWidth+i,o=e.plotTop,a=e.plotHeight+o,r=i+e.plotWidth/2,h=o+e.plotHeight/2,n={minX:Number.MAX_VALUE,maxX:-Number.MAX_VALUE,minY:Number.MAX_VALUE,maxY:-Number.MAX_VALUE},l,p=1;return l=[{x:i,y:o,z:0},{x:i,y:o,z:t}],[0,1].forEach(function(t){l.push({x:s,y:l[t].y,z:l[t].z})}),[0,1,2,3].forEach(function(t){l.push({x:l[t].x,y:a,z:l[t].z})}),(l=S(l,e,!1)).forEach(function(t){n.minX=Math.min(n.minX,t.x),n.maxX=Math.max(n.maxX,t.x),n.minY=Math.min(n.minY,t.y),n.maxY=Math.max(n.maxY,t.y)}),i>n.minX&&(p=Math.min(p,1-Math.abs((i+r)/(n.minX+r))%1)),s<n.maxX&&(p=Math.min(p,(s-r)/(n.maxX-r))),o>n.minY&&(p=n.minY<0?Math.min(p,(o+h)/(-n.minY+o+h)):Math.min(p,1-(o+h)/(n.minY+h)%1)),a<n.maxY&&(p=Math.min(p,Math.abs((a-h)/(n.maxY-h)))),p}}t.Additions=d}(n||(n={}));let Y=n;var H=d(512),Z=d.n(H);let{composed:O}=f(),{perspective:C}=P,{line:{prototype:E}}=Z().seriesTypes,{pushUnique:F,wrap:R}=f();function W(t){let e=t.apply(this,[].slice.call(arguments,1));if(!this.chart.is3d())return e;let i=E.getGraphPath,s=this.options,o=Math.round(this.yAxis.getThreshold(s.threshold)),a=[];if(this.rawPointsX)for(let t=0;t<this.points.length;t++)a.push({x:this.rawPointsX[t],y:s.stacking?this.points[t].yBottom:o,z:this.zPadding});let r=this.chart.options.chart.options3d;a=C(a,this.chart,!0).map(t=>({plotX:t.x,plotY:t.y,plotZ:t.z})),this.group&&r&&r.depth&&r.beta&&(this.markerGroup&&(this.markerGroup.add(this.group),this.markerGroup.attr({translateX:0,translateY:0})),this.group.attr({zIndex:Math.max(1,r.beta>270||r.beta<90?r.depth-Math.round(this.zPadding||0):Math.round(this.zPadding||0))})),a.reversed=!0;let h=i.call(this,a,!0,!0);if(h[0]&&"M"===h[0][0]&&(h[0]=["L",h[0][1],h[0][2]]),this.areaPath){let t=this.areaPath.splice(0,this.areaPath.length/2).concat(h);t.xMap=this.areaPath.xMap,this.areaPath=t}return this.graphPath=e,e}let _={labels:{position3d:"offset",skew3d:!1},title:{position3d:null,skew3d:null}},{composed:B}=f(),{addEvent:G,extend:N,pushUnique:j,wrap:U}=f();function V(t){let e=this.axis.axis3D;e&&N(t.pos,e.fix3dPosition(t.pos))}function q(t){let e=this.axis.axis3D,i=t.apply(this,[].slice.call(arguments,1));if(e){let t=i[0],s=i[1];if("M"===t[0]&&"L"===s[0]){let i=[e.fix3dPosition({x:t[1],y:t[2],z:0}),e.fix3dPosition({x:s[1],y:s[2],z:0})];return this.axis.chart.renderer.toLineSegments(i)}}return i}let J={compose:function(t){j(B,"Axis.Tick3D")&&(G(t,"afterGetLabelPosition",V),U(t.prototype,"getMarkPath",q))}},{defaultOptions:K}=f(),{deg2rad:Q}=f(),{perspective:$,perspective3D:tt,shapeArea:te}=P,{addEvent:ti,merge:ts,pick:to,wrap:ta}=f();function tr(){let t=this.chart,e=this.options;t.is3d?.()&&"colorAxis"!==this.coll&&(e.tickWidth=to(e.tickWidth,0),e.gridLineWidth=to(e.gridLineWidth,1))}function th(t){this.chart.is3d()&&"colorAxis"!==this.coll&&t.point&&(t.point.crosshairPos=this.isXAxis?t.point.axisXpos:this.len-t.point.axisYpos)}function tn(){this.axis3D||(this.axis3D=new ty(this))}function tl(t){return this.chart.is3d()&&"colorAxis"!==this.coll?[]:t.apply(this,[].slice.call(arguments,1))}function tp(t){if(!this.chart.is3d()||"colorAxis"===this.coll)return t.apply(this,[].slice.call(arguments,1));let e=arguments,i=e[1],s=e[2],o=[],a=this.getPlotLinePath({value:i}),r=this.getPlotLinePath({value:s});if(a&&r)for(let t=0;t<a.length;t+=2){let e=a[t],i=a[t+1],s=r[t],h=r[t+1];"M"===e[0]&&"L"===i[0]&&"M"===s[0]&&"L"===h[0]&&o.push(e,i,h,["L",s[1],s[2]],["Z"])}return o}function tc(t){let e=this.axis3D,i=this.chart,s=t.apply(this,[].slice.call(arguments,1));if("colorAxis"===this.coll||!i.chart3d||!i.is3d()||null===s)return s;let o=i.options.chart.options3d,a=this.isZAxis?i.plotWidth:o.depth,r=i.chart3d.frame3d,h=s[0],n=s[1],l,p=[];return"M"===h[0]&&"L"===n[0]&&(l=[e.swapZ({x:h[1],y:h[2],z:0}),e.swapZ({x:h[1],y:h[2],z:a}),e.swapZ({x:n[1],y:n[2],z:0}),e.swapZ({x:n[1],y:n[2],z:a})],this.horiz?(this.isZAxis?(r.left.visible&&p.push(l[0],l[2]),r.right.visible&&p.push(l[1],l[3])):(r.front.visible&&p.push(l[0],l[2]),r.back.visible&&p.push(l[1],l[3])),r.top.visible&&p.push(l[0],l[1]),r.bottom.visible&&p.push(l[2],l[3])):(r.front.visible&&p.push(l[0],l[2]),r.back.visible&&p.push(l[1],l[3]),r.left.visible&&p.push(l[0],l[1]),r.right.visible&&p.push(l[2],l[3])),p=$(p,this.chart,!1)),i.renderer.toLineSegments(p)}function td(t,e){let{chart:i,gridGroup:s,tickPositions:o,ticks:a}=this;if(this.categories&&i.frameShapes&&i.is3d()&&s&&e&&e.label){let t,r,h,n=s.element.childNodes[0].getBBox(),l=i.frameShapes.left.getBBox(),p=i.options.chart.options3d,c={x:i.plotWidth/2,y:i.plotHeight/2,z:p.depth/2,vd:to(p.depth,1)*to(p.viewDistance,0)},d=o.indexOf(e.pos),x=a[o[d-1]],y=a[o[d+1]];return x?.label?.xy&&(r=tt({x:x.label.xy.x,y:x.label.xy.y,z:null},c,c.vd)),y?.label?.xy&&(h=tt({x:y.label.xy.x,y:y.label.xy.y,z:null},c,c.vd)),t=tt(t={x:e.label.xy.x,y:e.label.xy.y,z:null},c,c.vd),Math.abs(r?t.x-r.x:h?h.x-t.x:n.x-l.x)}return t.apply(this,[].slice.call(arguments,1))}function tx(t){let e=t.apply(this,[].slice.call(arguments,1));return this.axis3D?this.axis3D.fix3dPosition(e,!0):e}class ty{static compose(t,e){if(J.compose(e),!t.keepProps.includes("axis3D")){ts(!0,K.xAxis,_),t.keepProps.push("axis3D"),ti(t,"init",tn),ti(t,"afterSetOptions",tr),ti(t,"drawCrosshair",th);let e=t.prototype;ta(e,"getLinePath",tl),ta(e,"getPlotBandPath",tp),ta(e,"getPlotLinePath",tc),ta(e,"getSlotWidth",td),ta(e,"getTitlePosition",tx)}}constructor(t){this.axis=t}fix3dPosition(t,e){let i=this.axis,s=i.chart;if("colorAxis"===i.coll||!s.chart3d||!s.is3d())return t;let o=Q*s.options.chart.options3d.alpha,a=Q*s.options.chart.options3d.beta,r=to(e&&i.options.title.position3d,i.options.labels.position3d),h=to(e&&i.options.title.skew3d,i.options.labels.skew3d),n=s.chart3d.frame3d,l=s.plotLeft,p=s.plotWidth+l,c=s.plotTop,d=s.plotHeight+c,x=0,y=0,f,u={x:0,y:1,z:0},z=!1;if(t=i.axis3D.swapZ({x:t.x,y:t.y,z:0}),i.isZAxis)if(i.opposite){if(null===n.axes.z.top)return{};y=t.y-c,t.x=n.axes.z.top.x,t.y=n.axes.z.top.y,f=n.axes.z.top.xDir,z=!n.top.frontFacing}else{if(null===n.axes.z.bottom)return{};y=t.y-d,t.x=n.axes.z.bottom.x,t.y=n.axes.z.bottom.y,f=n.axes.z.bottom.xDir,z=!n.bottom.frontFacing}else if(i.horiz)if(i.opposite){if(null===n.axes.x.top)return{};y=t.y-c,t.y=n.axes.x.top.y,t.z=n.axes.x.top.z,f=n.axes.x.top.xDir,z=!n.top.frontFacing}else{if(null===n.axes.x.bottom)return{};y=t.y-d,t.y=n.axes.x.bottom.y,t.z=n.axes.x.bottom.z,f=n.axes.x.bottom.xDir,z=!n.bottom.frontFacing}else if(i.opposite){if(null===n.axes.y.right)return{};x=t.x-p,t.x=n.axes.y.right.x,t.z=n.axes.y.right.z,f={x:(f=n.axes.y.right.xDir).z,y:f.y,z:-f.x}}else{if(null===n.axes.y.left)return{};x=t.x-l,t.x=n.axes.y.left.x,t.z=n.axes.y.left.z,f=n.axes.y.left.xDir}if("chart"===r);else if("flap"===r)if(i.horiz){let t=Math.sin(o),e=Math.cos(o);i.opposite&&(t=-t),z&&(t=-t),u={x:f.z*t,y:e,z:-f.x*t}}else f={x:Math.cos(a),y:0,z:Math.sin(a)};else if("ortho"===r)if(i.horiz){let t=Math.sin(o),e=Math.cos(o),i={x:Math.sin(a)*e,y:-t,z:-e*Math.cos(a)},s=1/Math.sqrt((u={x:f.y*i.z-f.z*i.y,y:f.z*i.x-f.x*i.z,z:f.x*i.y-f.y*i.x}).x*u.x+u.y*u.y+u.z*u.z);z&&(s=-s),u={x:s*u.x,y:s*u.y,z:s*u.z}}else f={x:Math.cos(a),y:0,z:Math.sin(a)};else i.horiz?u={x:Math.sin(a)*Math.sin(o),y:Math.cos(o),z:-Math.cos(a)*Math.sin(o)}:f={x:Math.cos(a),y:0,z:Math.sin(a)};t.x+=x*f.x+y*u.x,t.y+=x*f.y+y*u.y,t.z+=x*f.z+y*u.z;let g=$([t],i.chart)[0];if(h){0>te($([t,{x:t.x+f.x,y:t.y+f.y,z:t.z+f.z},{x:t.x+u.x,y:t.y+u.y,z:t.z+u.z}],i.chart))&&(f={x:-f.x,y:-f.y,z:-f.z});let e=$([{x:t.x,y:t.y,z:t.z},{x:t.x+f.x,y:t.y+f.y,z:t.z+f.z},{x:t.x+u.x,y:t.y+u.y,z:t.z+u.z}],i.chart);g.matrix=[e[1].x-e[0].x,e[1].y-e[0].y,e[2].x-e[0].x,e[2].y-e[0].y,g.x,g.y],g.matrix[4]-=g.x*g.matrix[0]+g.y*g.matrix[2],g.matrix[5]-=g.x*g.matrix[1]+g.y*g.matrix[3]}return g}swapZ(t,e){let i=this.axis;if(i.isZAxis){let s=e?0:i.chart.plotLeft;return{x:s+t.z,y:t.y,z:t.x-s}}return t}}var tf=d(608),tu=d.n(tf),tz=d(820),tg=d.n(tz);let{composed:tb}=f(),{perspective:tm}=P,{addEvent:tv,extend:tM,isNumber:tP,merge:tA,pick:tk,pushUnique:tS}=f();class tw extends tg(){static compose(t){tS(tb,"Core.Series3D")&&(tv(t,"afterTranslate",function(){this.chart.is3d()&&this.translate3dPoints()}),tM(t.prototype,{translate3dPoints:tw.prototype.translate3dPoints}))}translate3dPoints(){let t,e,i=this,s=i.options,o=i.chart,a=tk(i.zAxis,o.options.zAxis[0]),r=[],h=[],n=s.stacking?tP(s.stack)?s.stack:0:i.index||0;i.zPadding=n*(s.depth||0+(s.groupZPadding||1)),i.data.forEach(t=>{a?.translate?(e=a.logarithmic&&a.val2lin?a.val2lin(t.z):t.z,t.plotZ=a.translate(e),t.isInside=!!t.isInside&&e>=a.min&&e<=a.max):t.plotZ=i.zPadding,t.axisXpos=t.plotX,t.axisYpos=t.plotY,t.axisZpos=t.plotZ,r.push({x:t.plotX,y:t.plotY,z:t.plotZ}),h.push(t.plotX||0)}),i.rawPointsX=h;let l=tm(r,o,!0);i.data.forEach((e,i)=>{e.plotX=(t=l[i]).x,e.plotY=t.y,e.plotZ=t.z})}}tw.defaultOptions=tA(tg().defaultOptions);var tI=d(184),tD=d.n(tI);let{parse:tL}=z(),{Element:tT}=tu().getRendererType().prototype,{defined:tX,pick:tY}=f();class tH extends tT{constructor(){super(...arguments),this.parts=["front","top","side"],this.pathType="cuboid"}initArgs(t){let e=this.renderer,i=e[this.pathType+"Path"](t),s=i.zIndexes;for(let t of this.parts){let o={class:"highcharts-3d-"+t,zIndex:s[t]||0};e.styledMode&&("top"===t?o.filter="url(#highcharts-brighter)":"side"===t&&(o.filter="url(#highcharts-darker)")),this[t]=e.path(i[t]).attr(o).add(this)}this.attr({"stroke-linejoin":"round",zIndex:s.group}),this.forcedSides=i.forcedSides}singleSetterForParts(t,e,i,s,o,a){let r={},h=[null,null,s||"attr",o,a],n=i?.zIndexes;if(i){for(let e of(n?.group&&this.attr({zIndex:n.group}),Object.keys(i)))r[e]={},r[e][t]=i[e],n&&(r[e].zIndex=i.zIndexes[e]||0);h[1]=r}else r[t]=e,h[0]=r;return this.processParts.apply(this,h)}processParts(t,e,i,s,o){for(let a of this.parts)e&&(t=tY(e[a],!1)),!1!==t&&this[a][i](t,s,o);return this}destroy(){return this.processParts(null,null,"destroy"),super.destroy()}attr(t,e,i,s){if("string"==typeof t&&void 0!==e){let i=t;(t={})[i]=e}return t.shapeArgs||tX(t.x)?this.singleSetterForParts("d",null,this.renderer[this.pathType+"Path"](t.shapeArgs||t)):super.attr(t,void 0,i,s)}animate(t,e,i){if(tX(t.x)&&tX(t.y)){let s=this.renderer[this.pathType+"Path"](t),o=s.forcedSides;this.singleSetterForParts("d",null,s,"animate",e,i),this.attr({zIndex:s.zIndexes.group}),o!==this.forcedSides&&(this.forcedSides=o,this.renderer.styledMode||this.fillSetter(this.fill))}else super.animate(t,e,i);return this}fillSetter(t){return this.forcedSides=this.forcedSides||[],this.singleSetterForParts("fill",null,{front:t,top:tL(t).brighten(this.forcedSides.indexOf("top")>=0?0:.1).get(),side:tL(t).brighten(this.forcedSides.indexOf("side")>=0?0:-.1).get()}),this.color=this.fill=t,this}}tH.types={base:tH,cuboid:tH};let{animObject:tZ}=f(),{parse:tO}=z(),{charts:tC,deg2rad:tE}=f(),{perspective:tF,shapeArea:tR}=P,{defined:tW,extend:t_,merge:tB,pick:tG}=f(),tN=Math.cos,tj=Math.sin,tU=Math.PI,tV=4*(Math.sqrt(2)-1)/3/(tU/2);function tq(t,e,i,s,o,a,r,h){let n=a-o,l=[];return a>o&&a-o>Math.PI/2+1e-4?l=(l=l.concat(tq(t,e,i,s,o,o+Math.PI/2,r,h))).concat(tq(t,e,i,s,o+Math.PI/2,a,r,h)):a<o&&o-a>Math.PI/2+1e-4?l=(l=l.concat(tq(t,e,i,s,o,o-Math.PI/2,r,h))).concat(tq(t,e,i,s,o-Math.PI/2,a,r,h)):[["C",t+i*Math.cos(o)-i*tV*n*Math.sin(o)+r,e+s*Math.sin(o)+s*tV*n*Math.cos(o)+h,t+i*Math.cos(a)+i*tV*n*Math.sin(a)+r,e+s*Math.sin(a)-s*tV*n*Math.cos(a)+h,t+i*Math.cos(a)+r,e+s*Math.sin(a)+h]]}!function(t){function e(t,e){let i=[];for(let e of t)i.push(["L",e.x,e.y]);return t.length&&(i[0][0]="M",e&&i.push(["Z"])),i}function i(t){let e=[],i=!0;for(let s of t)e.push(i?["M",s.x,s.y]:["L",s.x,s.y]),i=!i;return e}function s(t){let e=this,i=e.Element.prototype,s=e.createElement("path");return s.vertexes=[],s.insidePlotArea=!1,s.enabled=!0,s.attr=function(t){if("object"==typeof t&&(tW(t.enabled)||tW(t.vertexes)||tW(t.insidePlotArea))){this.enabled=tG(t.enabled,this.enabled),this.vertexes=tG(t.vertexes,this.vertexes),this.insidePlotArea=tG(t.insidePlotArea,this.insidePlotArea),delete t.enabled,delete t.vertexes,delete t.insidePlotArea;let i=tC[e.chartIndex],s=tF(this.vertexes,i,this.insidePlotArea),o=e.toLinePath(s,!0),a=tR(s);t.d=o,t.visibility=this.enabled&&a>0?"inherit":"hidden"}return i.attr.apply(this,arguments)},s.animate=function(t){if("object"==typeof t&&(tW(t.enabled)||tW(t.vertexes)||tW(t.insidePlotArea))){this.enabled=tG(t.enabled,this.enabled),this.vertexes=tG(t.vertexes,this.vertexes),this.insidePlotArea=tG(t.insidePlotArea,this.insidePlotArea),delete t.enabled,delete t.vertexes,delete t.insidePlotArea;let i=tC[e.chartIndex],s=tF(this.vertexes,i,this.insidePlotArea),o=e.toLinePath(s,!0),a=tR(s),r=this.enabled&&a>0?"visible":"hidden";t.d=o,this.attr("visibility",r)}return i.animate.apply(this,arguments)},s.attr(t)}function o(t){let e=this,i=e.Element.prototype,s=e.g(),o=s.destroy;return this.styledMode||s.attr({"stroke-linejoin":"round"}),s.faces=[],s.destroy=function(){for(let t=0;t<s.faces.length;t++)s.faces[t].destroy();return o.call(this)},s.attr=function(t,o,a,r){if("object"==typeof t&&tW(t.faces)){for(;s.faces.length>t.faces.length;)s.faces.pop().destroy();for(;s.faces.length<t.faces.length;)s.faces.push(e.face3d().add(s));for(let i=0;i<t.faces.length;i++)e.styledMode&&delete t.faces[i].fill,s.faces[i].attr(t.faces[i],null,a,r);delete t.faces}return i.attr.apply(this,arguments)},s.animate=function(t,o,a){if(t?.faces){for(;s.faces.length>t.faces.length;)s.faces.pop().destroy();for(;s.faces.length<t.faces.length;)s.faces.push(e.face3d().add(s));for(let e=0;e<t.faces.length;e++)s.faces[e].animate(t.faces[e],o,a);delete t.faces}return i.animate.apply(this,arguments)},s.attr(t)}function a(t,e){let i=new tH.types[t](this,"g");return i.initArgs(e),i}function r(t){return this.element3d("cuboid",t)}function h(t){let e=t.x||0,i=t.y||0,s=t.z||0,o=t.height||0,a=t.width||0,r=t.depth||0,h=tC[this.chartIndex],n=h.options.chart.options3d.alpha,l=[],p,c=0,d=[{x:e,y:i,z:s},{x:e+a,y:i,z:s},{x:e+a,y:i+o,z:s},{x:e,y:i+o,z:s},{x:e,y:i+o,z:s+r},{x:e+a,y:i+o,z:s+r},{x:e+a,y:i,z:s+r},{x:e,y:i,z:s+r}];d=tF(d,h,t.insidePlotArea);let x=t=>0===o&&t>1&&t<6?{x:d[t].x,y:d[t].y+10,z:d[t].z}:d[0].x===d[7].x&&t>=4?{x:d[t].x+10,y:d[t].y,z:d[t].z}:0===r&&t<2||t>5?{x:d[t].x,y:d[t].y,z:d[t].z+10}:d[t],y=t=>d[t],f=(t,e,i)=>{let s=t.map(y),o=e.map(y),a=t.map(x),r=e.map(x),h=[[],-1];return 0>tR(s)?h=[s,0]:0>tR(o)?h=[o,1]:i&&(l.push(i),h=0>tR(a)?[s,0]:0>tR(r)?[o,1]:[s,0]),h},u=(p=f([3,2,1,0],[7,6,5,4],"front"))[0],z=p[1],g=(p=f([1,6,7,0],[4,5,2,3],"top"))[0],b=p[1],m=(p=f([1,2,5,6],[0,7,4,3],"side"))[0],v=p[1];return 1===v?c+=1e6*(h.plotWidth-e):v||(c+=1e6*e),c+=10*(!b||n>=0&&n<=180||n<360&&n>357.5?h.plotHeight-i:10+i),1===z?c+=100*s:z||(c+=100*(1e3-s)),{front:this.toLinePath(u,!0),top:this.toLinePath(g,!0),side:this.toLinePath(m,!0),zIndexes:{group:Math.round(c)},forcedSides:l,isFront:z,isTop:b}}function n(t){let e=this.g(),i=this.Element.prototype,s=["alpha","beta","x","y","r","innerR","start","end","depth"];function o(t){let e,i={};for(e in t=tB(t))-1!==s.indexOf(e)&&(i[e]=t[e],delete t[e]);return!!Object.keys(i).length&&[i,t]}for(let i of((t=tB(t)).alpha=(t.alpha||0)*tE,t.beta=(t.beta||0)*tE,e.top=this.path(),e.side1=this.path(),e.side2=this.path(),e.inn=this.path(),e.out=this.path(),e.onAdd=function(){let t=e.parentGroup,i=e.attr("class");for(let s of(e.top.add(e),["out","inn","side1","side2"]))e[s].attr({class:i+" highcharts-3d-side"}).add(t)},["addClass","removeClass"]))e[i]=function(){let t=arguments;for(let s of["top","out","inn","side1","side2"])e[s][i].apply(e[s],t)};for(let i of(e.setPaths=function(t){let i=e.renderer.arc3dPath(t),s=100*i.zTop;e.attribs=t,e.top.attr({d:i.top,zIndex:i.zTop}),e.inn.attr({d:i.inn,zIndex:i.zInn}),e.out.attr({d:i.out,zIndex:i.zOut}),e.side1.attr({d:i.side1,zIndex:i.zSide1}),e.side2.attr({d:i.side2,zIndex:i.zSide2}),e.zIndex=s,e.attr({zIndex:s}),t.center&&(e.top.setRadialReference(t.center),delete t.center)},e.setPaths(t),e.fillSetter=function(t){let e=tO(t).brighten(-.1).get();return this.fill=t,this.side1.attr({fill:e}),this.side2.attr({fill:e}),this.inn.attr({fill:e}),this.out.attr({fill:e}),this.top.attr({fill:t}),this},["opacity","translateX","translateY","visibility"]))e[i+"Setter"]=function(t,i){for(let s of(e[i]=t,["out","inn","side1","side2","top"]))e[s].attr(i,t)};return e.attr=function(t){if("object"==typeof t){let i=o(t);if(i){let t=i[0];arguments[0]=i[1],void 0!==t.alpha&&(t.alpha*=tE),void 0!==t.beta&&(t.beta*=tE),t_(e.attribs,t),e.attribs&&e.setPaths(e.attribs)}}return i.attr.apply(e,arguments)},e.animate=function(t,s,a){let r=this.attribs,h="data-"+Math.random().toString(26).substring(2,9);delete t.center,delete t.z;let n=tZ(tG(s,this.renderer.globalAnimation));if(n.duration){let i=o(t);if(e[h]=0,t[h]=1,e[h+"Setter"]=f().noop,i){let t=i[0],e=(e,i)=>r[e]+(tG(t[e],r[e])-r[e])*i;n.step=function(t,i){i.prop===h&&i.elem.setPaths(tB(r,{x:e("x",i.pos),y:e("y",i.pos),r:e("r",i.pos),innerR:e("innerR",i.pos),start:e("start",i.pos),end:e("end",i.pos),depth:e("depth",i.pos)}))}}s=n}return i.animate.call(this,t,s,a)},e.destroy=function(){return this.top.destroy(),this.out.destroy(),this.inn.destroy(),this.side1.destroy(),this.side2.destroy(),i.destroy.call(this)},e.hide=function(){this.top.hide(),this.out.hide(),this.inn.hide(),this.side1.hide(),this.side2.hide()},e.show=function(t){this.top.show(t),this.out.show(t),this.inn.show(t),this.side1.show(t),this.side2.show(t)},e}function l(t){let e=t.x||0,i=t.y||0,s=t.start||0,o=(t.end||0)-1e-5,a=t.r||0,r=t.innerR||0,h=t.depth||0,n=t.alpha||0,l=t.beta||0,p=Math.cos(s),c=Math.sin(s),d=Math.cos(o),x=Math.sin(o),y=a*Math.cos(l),f=a*Math.cos(n),u=r*Math.cos(l),z=r*Math.cos(n),g=h*Math.sin(l),b=h*Math.sin(n),m=[["M",e+y*p,i+f*c]];(m=m.concat(tq(e,i,y,f,s,o,0,0))).push(["L",e+u*d,i+z*x]),(m=m.concat(tq(e,i,u,z,o,s,0,0))).push(["Z"]);let v=l>0?Math.PI/2:0,M=n>0?0:Math.PI/2,P=s>-v?s:o>-v?-v:s,A=o<tU-M?o:s<tU-M?tU-M:o,k=2*tU-M,S=[["M",e+y*tN(P),i+f*tj(P)]];S=S.concat(tq(e,i,y,f,P,A,0,0)),o>k&&s<k?(S.push(["L",e+y*tN(A)+g,i+f*tj(A)+b]),(S=S.concat(tq(e,i,y,f,A,k,g,b))).push(["L",e+y*tN(k),i+f*tj(k)]),(S=S.concat(tq(e,i,y,f,k,o,0,0))).push(["L",e+y*tN(o)+g,i+f*tj(o)+b]),(S=S.concat(tq(e,i,y,f,o,k,g,b))).push(["L",e+y*tN(k),i+f*tj(k)]),S=S.concat(tq(e,i,y,f,k,A,0,0))):o>tU-M&&s<tU-M&&(S.push(["L",e+y*Math.cos(A)+g,i+f*Math.sin(A)+b]),(S=S.concat(tq(e,i,y,f,A,o,g,b))).push(["L",e+y*Math.cos(o),i+f*Math.sin(o)]),S=S.concat(tq(e,i,y,f,o,A,0,0))),S.push(["L",e+y*Math.cos(A)+g,i+f*Math.sin(A)+b]),(S=S.concat(tq(e,i,y,f,A,P,g,b))).push(["Z"]);let w=[["M",e+u*p,i+z*c]];(w=w.concat(tq(e,i,u,z,s,o,0,0))).push(["L",e+u*Math.cos(o)+g,i+z*Math.sin(o)+b]),(w=w.concat(tq(e,i,u,z,o,s,g,b))).push(["Z"]);let I=[["M",e+y*p,i+f*c],["L",e+y*p+g,i+f*c+b],["L",e+u*p+g,i+z*c+b],["L",e+u*p,i+z*c],["Z"]],D=[["M",e+y*d,i+f*x],["L",e+y*d+g,i+f*x+b],["L",e+u*d+g,i+z*x+b],["L",e+u*d,i+z*x],["Z"]],L=Math.atan2(b,-g),T=Math.abs(o+L),X=Math.abs(s+L),Y=Math.abs((s+o)/2+L);function H(t){return(t%=2*Math.PI)>Math.PI&&(t=2*Math.PI-t),t}T=H(T),X=H(X);let Z=1e5*(Y=H(Y)),O=1e5*X,C=1e5*T;return{top:m,zTop:1e5*Math.PI+1,out:S,zOut:Math.max(Z,O,C),inn:w,zInn:Math.max(Z,O,C),side1:I,zSide1:.99*C,side2:D,zSide2:.99*O}}t.compose=function(t){let p=t.prototype;p.element3d||t_(p,{Element3D:tH,arc3d:n,arc3dPath:l,cuboid:r,cuboidPath:h,element3d:a,face3d:s,polyhedron:o,toLinePath:e,toLineSegments:i})}}(l||(l={}));let tJ=l;var tK=d(532),tQ=d.n(tK);let{defaultOptions:t$}=f(),{addEvent:t0,merge:t1,pick:t3,splat:t2}=f();function t5(t){return new t9(this,t)}function t6(){let t=this.options.zAxis=t2(this.options.zAxis||{});this.is3d()&&(this.zAxis=[],t.forEach(t=>{this.addZAxis(t).setScale()}))}class t9 extends tQ(){constructor(){super(...arguments),this.isZAxis=!0}static compose(t){let e=t.prototype;e.addZAxis||(t$.zAxis=t1(t$.xAxis,{offset:0,lineWidth:0}),e.addZAxis=t5,e.collectionsWithInit.zAxis=[e.addZAxis],e.collectionsWithUpdate.push("zAxis"),t0(t,"afterCreateAxes",t6))}init(t,e){this.isZAxis=!0,super.init(t,e,"zAxis")}getSeriesExtremes(){this.hasVisibleSeries=!1,this.dataMin=this.dataMax=this.ignoreMinPadding=this.ignoreMaxPadding=void 0,this.stacking&&this.stacking.buildStacks(),this.series.forEach(t=>{if(t.reserveSpace()){let e=t.options.threshold;this.hasVisibleSeries=!0,this.positiveValuesOnly&&e<=0&&(e=void 0);let i=t.getColumn("z");i.length&&(this.dataMin=Math.min(t3(this.dataMin,i[0]),Math.min.apply(null,i)),this.dataMax=Math.max(t3(this.dataMax,i[0]),Math.max.apply(null,i)))}})}setAxisSize(){let t=this.chart;super.setAxisSize(),this.width=this.len=t.options.chart.options3d?.depth||0,this.right=t.chartWidth-this.width-this.left}}let{composed:t4}=f(),{perspective:t7}=P,{addEvent:t8,extend:et,pick:ee,pushUnique:ei,wrap:es}=f();function eo(){let t=this.chart,e=this.options,i=e.depth,s=(e.stacking?e.stack||0:this.index)*(i+(e.groupZPadding||1)),o=this.borderWidth%2?.5:0,a;for(let r of(t.inverted&&!this.yAxis.reversed&&(o*=-1),!1!==e.grouping&&(s=0),s+=e.groupZPadding||1,this.points))if(r.outside3dPlot=null,null!==r.y){let e,h=et({x:0,y:0,width:0,height:0},r.shapeArgs||{}),n=[["x","width"],["y","height"]],l=r.tooltipPos;for(let t of n)if((e=h[t[0]]-o)<0&&(h[t[1]]+=h[t[0]]+o,h[t[0]]=-o,e=0),e+h[t[1]]>this[t[0]+"Axis"].len&&0!==h[t[1]]&&(h[t[1]]=this[t[0]+"Axis"].len-h[t[0]]),0!==h[t[1]]&&(h[t[0]]>=this[t[0]+"Axis"].len||h[t[0]]+h[t[1]]<=o)){for(let t in h)h[t]="y"===t?-9999:0;r.outside3dPlot=!0}if("roundedRect"===r.shapeType&&(r.shapeType="cuboid"),r.shapeArgs=et(h,{z:s,depth:i,insidePlotArea:!0}),a={x:h.x+h.width/2,y:h.y,z:s+i/2},t.inverted&&(a.x=h.height,a.y=r.clientX||0),r.axisXpos=a.x,r.axisYpos=a.y,r.axisZpos=a.z,r.plot3d=t7([a],t,!0,!1)[0],l){let e=t7([{x:l[0],y:l[1],z:s+i/2}],t,!0,!1)[0];r.tooltipPos=[e.x,e.y]}}this.z=s}function ea(){if(this.chart.is3d()){let t=this.options,e=t.grouping,i=t.stacking,s=this.yAxis.options.reversedStacks,o=0;if(!(void 0!==e&&!e)){let e,a=function(t,e){let i=t.series,s={totalStacks:0},o,a=1;return i.forEach(function(t){s[o=ee(t.options.stack,e?0:i.length-1-t.index)]?s[o].series.push(t):(s[o]={series:[t],position:a},a++)}),s.totalStacks=a+1,s}(this.chart,i),r=t.stack||0;for(e=0;e<a[r].series.length&&a[r].series[e]!==this;e++);o=10*(a.totalStacks-a[r].position)+(s?e:-e),this.xAxis.reversed||(o=10*a.totalStacks-o)}t.depth=t.depth||25,this.z=this.z||0,t.zIndex=o}}function er(t,...e){return this.series.chart.is3d()?this.graphic&&"g"!==this.graphic.element.nodeName:t.apply(this,e)}function eh(t){if(this.chart.is3d()){let t=arguments,e=t[1],i=this.yAxis,s=this.yAxis.reversed;if(e)for(let t of this.points)null!==t.y&&(t.height=t.shapeArgs.height,t.shapey=t.shapeArgs.y,t.shapeArgs.height=1,s||(t.stackY?t.shapeArgs.y=t.plotY+i.translate(t.stackY):t.shapeArgs.y=t.plotY+(t.negative?-t.height:t.height)));else{for(let t of this.points)null!==t.y&&(t.shapeArgs.height=t.height,t.shapeArgs.y=t.shapey,t.graphic&&t.graphic[t.outside3dPlot?"attr":"animate"](t.shapeArgs,this.options.animation));this.drawDataLabels()}}else t.apply(this,[].slice.call(arguments,1))}function en(t,e,i,s,o,a){return"dataLabelsGroup"!==e&&"markerGroup"!==e&&this.chart.is3d()&&(this[e]&&delete this[e],a&&(this.chart.columnGroup||(this.chart.columnGroup=this.chart.renderer.g("columnGroup").add(a)),this[e]=this.chart.columnGroup,this.chart.columnGroup.attr(this.getPlotBox()),this[e].survive=!0,"group"===e&&(arguments[3]="visible"))),t.apply(this,Array.prototype.slice.call(arguments,1))}function el(t){let e=t.apply(this,[].slice.call(arguments,1));return this.chart.is3d&&this.chart.is3d()&&(e.stroke=this.options.edgeColor||e.fill,e["stroke-width"]=ee(this.options.edgeWidth,1)),e}function ep(t,e,i){let s=this.chart.is3d&&this.chart.is3d();s&&(this.options.inactiveOtherPoints=!0),t.call(this,e,i),s&&(this.options.inactiveOtherPoints=!1)}function ec(t,e){if(this.chart.is3d())for(let t of this.points)t.visible=t.options.visible=e=void 0===e?!ee(this.visible,t.visible):e,this.options.data[this.data.indexOf(t)]=t.options,t.graphic&&t.graphic.attr({visibility:e?"visible":"hidden"});t.apply(this,Array.prototype.slice.call(arguments,1))}function ed(t){t.apply(this,[].slice.call(arguments,1)),this.chart.is3d()&&this.translate3dShapes()}function ex(t,e,i,s,o){let a=this.chart;if(s.outside3dPlot=e.outside3dPlot,a.is3d()&&this.is("column")){let t=this.options,i=ee(s.inside,!!this.options.stacking),r=a.options.chart.options3d,h=e.pointWidth/2||0,n={x:o.x+h,y:o.y,z:this.z+t.depth/2};a.inverted&&(i&&(o.width=0,n.x+=e.shapeArgs.height/2),r.alpha>=90&&r.alpha<=270&&(n.y+=e.shapeArgs.width)),o.x=(n=t7([n],a,!0,!1)[0]).x-h,o.y=e.outside3dPlot?-9e9:n.y}t.apply(this,[].slice.call(arguments,1))}function ey(t){return!arguments[2].outside3dPlot&&t.apply(this,[].slice.call(arguments,1))}function ef(t,e){let i=t.apply(this,[].slice.call(arguments,1)),s=this.axis.chart,{width:o}=e;if(s.is3d()&&this.base){let t=+this.base.split(",")[0],e=s.series[t],a=s.options.chart.options3d;if(e&&"column"===e.type){let t={x:i.x+(s.inverted?i.height:o/2),y:i.y,z:e.options.depth/2};s.inverted&&(i.width=0,a.alpha>=90&&a.alpha<=270&&(t.y+=o)),i.x=(t=t7([t],s,!0,!1)[0]).x-o/2,i.y=t.y}}return i}let{pie:{prototype:{pointClass:eu}}}=Z().seriesTypes,ez=class extends eu{haloPath(){return this.series?.chart.is3d()?[]:super.haloPath.apply(this,arguments)}},{composed:eg,deg2rad:eb}=f(),{pie:em}=Z().seriesTypes,{extend:ev,pick:eM,pushUnique:eP}=f();class eA extends em{static compose(t){eP(eg,"Pie3D")&&(t.types.pie=eA)}addPoint(){super.addPoint.apply(this,arguments),this.chart.is3d()&&this.update(this.userOptions,!0)}animate(t){if(this.chart.is3d()){let e=this.center,i=this.group,s=this.markerGroup,o=this.options.animation,a;!0===o&&(o={}),t?(i.oldtranslateX=eM(i.oldtranslateX,i.translateX),i.oldtranslateY=eM(i.oldtranslateY,i.translateY),a={translateX:e[0],translateY:e[1],scaleX:.001,scaleY:.001},i.attr(a),s&&(s.attrSetters=i.attrSetters,s.attr(a))):(a={translateX:i.oldtranslateX,translateY:i.oldtranslateY,scaleX:1,scaleY:1},i.animate(a,o),s&&s.animate(a,o))}else super.animate.apply(this,arguments)}getDataLabelPosition(t,e){let i=super.getDataLabelPosition(t,e);if(this.chart.is3d()){let e=this.chart.options.chart.options3d,s=t.shapeArgs,o=s.r,a=(s.alpha||e?.alpha)*eb,r=(s.beta||e?.beta)*eb,h=(s.start+s.end)/2,n=i.connectorPosition,l=-o*(1-Math.cos(a))*Math.sin(h),p=o*(Math.cos(r)-1)*Math.cos(h);for(let t of[i?.natural,n.breakAt,n.touchingSliceAt])t.x+=p,t.y+=l}return i}pointAttribs(t){let e=super.pointAttribs.apply(this,arguments),i=this.options;return this.chart.is3d()&&!this.chart.styledMode&&(e.stroke=i.edgeColor||t.color||this.color,e["stroke-width"]=eM(i.edgeWidth,1)),e}translate(){if(super.translate.apply(this,arguments),!this.chart.is3d())return;let t=this.options,e=t.depth||0,i=this.chart.options.chart.options3d,s=i.alpha,o=i.beta,a=t.stacking?(t.stack||0)*e:this._i*e;for(let i of(a+=e/2,!1!==t.grouping&&(a=0),this.points)){let r=i.shapeArgs;i.shapeType="arc3d",r.z=a,r.depth=.75*e,r.alpha=s,r.beta=o,r.center=this.center;let h=(r.end+r.start)/2;i.slicedTranslation={translateX:Math.round(Math.cos(h)*t.slicedOffset*Math.cos(s*eb)),translateY:Math.round(Math.sin(h)*t.slicedOffset*Math.cos(s*eb))}}}drawTracker(){if(super.drawTracker.apply(this,arguments),this.chart.is3d()){for(let t of this.points)if(t.graphic)for(let e of["out","inn","side1","side2"])t.graphic&&(t.graphic[e].element.point=t)}}}ev(eA.prototype,{pointClass:ez});var ek=d(632),eS=d.n(ek);let{pointClass:ew}=eS().prototype,{defined:eI}=f(),eD=class extends ew{applyOptions(){return super.applyOptions.apply(this,arguments),eI(this.z)||(this.z=0),this}},{pointCameraDistance:eL}=P,{extend:eT,merge:eX}=f();class eY extends eS(){pointAttribs(t){let e=super.pointAttribs.apply(this,arguments);return this.chart.is3d()&&t&&(e.zIndex=eL(t,this.chart)),e}}eY.defaultOptions=eX(eS().defaultOptions,{tooltip:{pointFormat:"x: <b>{point.x}</b><br/>y: <b>{point.y}</b><br/>z: <b>{point.z}</b><br/>"}}),eT(eY.prototype,{axisTypes:["xAxis","yAxis","zAxis"],directTouch:!0,parallelArrays:["x","y","z"],pointArrayMap:["x","y","z"],pointClass:eD}),Z().registerSeriesType("scatter3d",eY);let eH=f();({compose:function(t){F(O,"Area3DSeries")&&R(t.prototype,"getGraphPath",W)}}).compose(eH.Series.types.area),ty.compose(eH.Axis,eH.Tick),Y.compose(eH.Chart,eH.Fx),({compose:function(t,e){if(ei(t4,"Column3D")){let i=t.prototype,s=e.prototype,{column:o,columnRange:a}=t.types;if(es(i,"alignDataLabel",ex),es(i,"justifyDataLabel",ey),es(s,"getStackBox",ef),o){let t=o.prototype,e=t.pointClass.prototype;t.translate3dPoints=()=>void 0,t.translate3dShapes=eo,t8(t,"afterInit",ea),es(e,"hasNewShapeType",er),es(t,"animate",eh),es(t,"plotGroup",en),es(t,"pointAttribs",el),es(t,"setState",ep),es(t,"setVisible",ec),es(t,"translate",ed)}if(a){let t=a.prototype;es(t.pointClass.prototype,"hasNewShapeType",er),es(t,"plotGroup",en),es(t,"pointAttribs",el),es(t,"setState",ep),es(t,"setVisible",ec)}}}}).compose(eH.Series,tD()),eA.compose(eH.Series),tw.compose(eH.Series),tJ.compose(tu().getRendererType()),t9.compose(eH.Chart);let eZ=eH;return x.default})());