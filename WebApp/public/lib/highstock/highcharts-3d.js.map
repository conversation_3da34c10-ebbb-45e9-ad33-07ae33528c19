{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/highcharts-3d\n * @requires highcharts\n *\n * 3D features for Highcharts JS\n *\n * (c) 2009-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"RendererRegistry\"], root[\"_Highcharts\"][\"Series\"], root[\"_Highcharts\"][\"StackItem\"], root[\"_Highcharts\"][\"Axis\"], root[\"_Highcharts\"][\"Series\"][\"types\"][\"scatter\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/highcharts-3d\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"Color\"],amd1[\"SeriesRegistry\"],amd1[\"RendererRegistry\"],amd1[\"Series\"],amd1[\"StackItem\"],amd1[\"Axis\"],amd1[\"Series\"],[\"types\"],[\"scatter\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/highcharts-3d\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"RendererRegistry\"], root[\"_Highcharts\"][\"Series\"], root[\"_Highcharts\"][\"StackItem\"], root[\"_Highcharts\"][\"Axis\"], root[\"_Highcharts\"][\"Series\"][\"types\"][\"scatter\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Color\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"RendererRegistry\"], root[\"Highcharts\"][\"Series\"], root[\"Highcharts\"][\"StackItem\"], root[\"Highcharts\"][\"Axis\"], root[\"Highcharts\"][\"Series\"][\"types\"][\"scatter\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__620__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__608__, __WEBPACK_EXTERNAL_MODULE__820__, __WEBPACK_EXTERNAL_MODULE__184__, __WEBPACK_EXTERNAL_MODULE__532__, __WEBPACK_EXTERNAL_MODULE__632__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 184:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__184__;\n\n/***/ }),\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 532:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__532__;\n\n/***/ }),\n\n/***/ 608:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__608__;\n\n/***/ }),\n\n/***/ 620:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__620__;\n\n/***/ }),\n\n/***/ 632:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__632__;\n\n/***/ }),\n\n/***/ 820:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__820__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ highcharts_3d_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Color\"],\"commonjs\":[\"highcharts\",\"Color\"],\"commonjs2\":[\"highcharts\",\"Color\"],\"root\":[\"Highcharts\",\"Color\"]}\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_ = __webpack_require__(620);\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default = /*#__PURE__*/__webpack_require__.n(highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_);\n;// ./code/es-modules/Core/Math3D.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { deg2rad } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/* eslint-disable max-len */\n/**\n * Apply 3-D rotation\n * Euler Angles (XYZ):\n *     cosA = cos(Alfa|Roll)\n *     cosB = cos(Beta|Pitch)\n *     cosG = cos(Gamma|Yaw)\n *\n * Composite rotation:\n * |          cosB * cosG             |           cosB * sinG            |    -sinB    |\n * | sinA * sinB * cosG - cosA * sinG | sinA * sinB * sinG + cosA * cosG | sinA * cosB |\n * | cosA * sinB * cosG + sinA * sinG | cosA * sinB * sinG - sinA * cosG | cosA * cosB |\n *\n * Now, Gamma/Yaw is not used (angle=0), so we assume cosG = 1 and sinG = 0, so\n * we get:\n * |     cosB    |   0    |   - sinB    |\n * | sinA * sinB |  cosA  | sinA * cosB |\n * | cosA * sinB | - sinA | cosA * cosB |\n *\n * But in browsers, y is reversed, so we get sinA => -sinA. The general result\n * is:\n * |      cosB     |   0    |    - sinB     |     | x |     | px |\n * | - sinA * sinB |  cosA  | - sinA * cosB |  x  | y |  =  | py |\n * |  cosA * sinB  |  sinA  |  cosA * cosB  |     | z |     | pz |\n *\n * @private\n * @function rotate3D\n */\n/* eslint-enable max-len */\n/**\n * Rotates the position as defined in angles.\n * @private\n * @param {number} x\n *        X coordinate\n * @param {number} y\n *        Y coordinate\n * @param {number} z\n *        Z coordinate\n * @param {Highcharts.Rotation3DObject} angles\n *        Rotation angles\n * @return {Highcharts.Position3DObject}\n *         Rotated position\n */\nfunction rotate3D(x, y, z, angles) {\n    return {\n        x: angles.cosB * x - angles.sinB * z,\n        y: -angles.sinA * angles.sinB * x + angles.cosA * y -\n            angles.cosB * angles.sinA * z,\n        z: angles.cosA * angles.sinB * x + angles.sinA * y +\n            angles.cosA * angles.cosB * z\n    };\n}\n/**\n * Transforms a given array of points according to the angles in chart.options.\n *\n * @private\n * @function Highcharts.perspective\n *\n * @param {Array<Highcharts.Position3DObject>} points\n * The array of points\n *\n * @param {Highcharts.Chart} chart\n * The chart\n *\n * @param {boolean} [insidePlotArea]\n * Whether to verify that the points are inside the plotArea\n *\n * @param {boolean} [useInvertedPersp]\n * Whether to use inverted perspective in calculations\n *\n * @return {Array<Highcharts.Position3DObject>}\n * An array of transformed points\n *\n * @requires highcharts-3d\n */\nfunction perspective(points, chart, insidePlotArea, useInvertedPersp) {\n    const options3d = chart.options.chart.options3d, \n    /* The useInvertedPersp argument is used for inverted charts with\n     * already inverted elements, such as dataLabels or tooltip positions.\n     */\n    inverted = pick(useInvertedPersp, insidePlotArea ? chart.inverted : false), origin = {\n        x: chart.plotWidth / 2,\n        y: chart.plotHeight / 2,\n        z: options3d.depth / 2,\n        vd: pick(options3d.depth, 1) * pick(options3d.viewDistance, 0)\n    }, scale = chart.scale3d || 1, beta = deg2rad * options3d.beta * (inverted ? -1 : 1), alpha = deg2rad * options3d.alpha * (inverted ? -1 : 1), angles = {\n        cosA: Math.cos(alpha),\n        cosB: Math.cos(-beta),\n        sinA: Math.sin(alpha),\n        sinB: Math.sin(-beta)\n    };\n    if (!insidePlotArea) {\n        origin.x += chart.plotLeft;\n        origin.y += chart.plotTop;\n    }\n    // Transform each point\n    return points.map(function (point) {\n        const rotated = rotate3D((inverted ? point.y : point.x) - origin.x, (inverted ? point.x : point.y) - origin.y, (point.z || 0) - origin.z, angles), \n        // Apply perspective\n        coordinate = perspective3D(rotated, origin, origin.vd);\n        // Apply translation\n        coordinate.x = coordinate.x * scale + origin.x;\n        coordinate.y = coordinate.y * scale + origin.y;\n        coordinate.z = rotated.z * scale + origin.z;\n        return {\n            x: (inverted ? coordinate.y : coordinate.x),\n            y: (inverted ? coordinate.x : coordinate.y),\n            z: coordinate.z\n        };\n    });\n}\n/**\n * Perspective3D function is available in global Highcharts scope because is\n * needed also outside of perspective() function (#8042).\n * @private\n * @function Highcharts.perspective3D\n *\n * @param {Highcharts.Position3DObject} coordinate\n * 3D position\n *\n * @param {Highcharts.Position3DObject} origin\n * 3D root position\n *\n * @param {number} distance\n * Perspective distance\n *\n * @return {Highcharts.PositionObject}\n * Perspective 3D Position\n *\n * @requires highcharts-3d\n */\nfunction perspective3D(coordinate, origin, distance) {\n    const projection = ((distance > 0) &&\n        (distance < Number.POSITIVE_INFINITY)) ?\n        distance / (coordinate.z + origin.z + distance) :\n        1;\n    return {\n        x: coordinate.x * projection,\n        y: coordinate.y * projection\n    };\n}\n/**\n * Calculate a distance from camera to points - made for calculating zIndex of\n * scatter points.\n *\n * @private\n * @function Highcharts.pointCameraDistance\n *\n * @param {Highcharts.Dictionary<number>} coordinates\n * Coordinates of the specific point\n *\n * @param {Highcharts.Chart} chart\n * Related chart\n *\n * @return {number}\n * Distance from camera to point\n *\n * @requires highcharts-3d\n */\nfunction pointCameraDistance(coordinates, chart) {\n    const options3d = chart.options.chart.options3d, cameraPosition = {\n        x: chart.plotWidth / 2,\n        y: chart.plotHeight / 2,\n        z: pick(options3d.depth, 1) * pick(options3d.viewDistance, 0) +\n            options3d.depth\n    }, \n    // Added support for objects with plotX or x coordinates.\n    distance = Math.sqrt(Math.pow(cameraPosition.x - pick(coordinates.plotX, coordinates.x), 2) +\n        Math.pow(cameraPosition.y - pick(coordinates.plotY, coordinates.y), 2) +\n        Math.pow(cameraPosition.z - pick(coordinates.plotZ, coordinates.z), 2));\n    return distance;\n}\n/**\n * Calculate area of a 2D polygon using Shoelace algorithm\n * https://en.wikipedia.org/wiki/Shoelace_formula\n *\n * @private\n * @function Highcharts.shapeArea\n *\n * @param {Array<Highcharts.PositionObject>} vertexes\n * 2D Polygon\n *\n * @return {number}\n * Calculated area\n *\n * @requires highcharts-3d\n */\nfunction shapeArea(vertexes) {\n    let area = 0, i, j;\n    for (i = 0; i < vertexes.length; i++) {\n        j = (i + 1) % vertexes.length;\n        area += vertexes[i].x * vertexes[j].y - vertexes[j].x * vertexes[i].y;\n    }\n    return area / 2;\n}\n/**\n * Calculate area of a 3D polygon after perspective projection\n *\n * @private\n * @function Highcharts.shapeArea3d\n *\n * @param {Array<Highcharts.Position3DObject>} vertexes\n * 3D Polygon\n *\n * @param {Highcharts.Chart} chart\n * Related chart\n *\n * @param {boolean} [insidePlotArea]\n * Whether to verify that the points are inside the plotArea\n *\n * @return {number}\n * Calculated area\n *\n * @requires highcharts-3d\n */\nfunction shapeArea3D(vertexes, chart, insidePlotArea) {\n    return shapeArea(perspective(vertexes, chart, insidePlotArea));\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst Math3D = {\n    perspective,\n    perspective3D,\n    pointCameraDistance,\n    shapeArea,\n    shapeArea3D\n};\n/* harmony default export */ const Core_Math3D = (Math3D);\n\n;// ./code/es-modules/Core/Chart/Chart3D.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  Extension for 3D charts\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { parse: color } = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default());\n\nconst { defaultOptions: genericDefaultOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { perspective: Chart3D_perspective, shapeArea3D: Chart3D_shapeArea3D } = Core_Math3D;\n\nconst { addEvent, isArray, merge, pick: Chart3D_pick, wrap } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\nvar Chart3D;\n(function (Chart3D) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    /**\n     * @optionparent\n     * @private\n     */\n    Chart3D.defaultOptions = {\n        chart: {\n            /**\n             * Options to render charts in 3 dimensions. This feature requires\n             * `highcharts-3d.js`, found in the download package or online at\n             * [code.highcharts.com/highcharts-3d.js](https://code.highcharts.com/highcharts-3d.js).\n             *\n             * @since    4.0\n             * @product  highcharts\n             * @requires highcharts-3d\n             */\n            options3d: {\n                /**\n                 * Whether to render the chart using the 3D functionality.\n                 *\n                 * @since   4.0\n                 * @product highcharts\n                 */\n                enabled: false,\n                /**\n                 * One of the two rotation angles for the chart.\n                 *\n                 * @since   4.0\n                 * @product highcharts\n                 */\n                alpha: 0,\n                /**\n                 * One of the two rotation angles for the chart.\n                 *\n                 * @since   4.0\n                 * @product highcharts\n                 */\n                beta: 0,\n                /**\n                 * The total depth of the chart.\n                 *\n                 * @since   4.0\n                 * @product highcharts\n                 */\n                depth: 100,\n                /**\n                 * Whether the 3d box should automatically adjust to the chart\n                 * plot area.\n                 *\n                 * @since   4.2.4\n                 * @product highcharts\n                 */\n                fitToPlot: true,\n                /**\n                 * Defines the distance the viewer is standing in front of the\n                 * chart, this setting is important to calculate the perspective\n                 * effect in column and scatter charts. It is not used for 3D\n                 * pie charts.\n                 *\n                 * @since   4.0\n                 * @product highcharts\n                 */\n                viewDistance: 25,\n                /**\n                 * Set it to `\"auto\"` to automatically move the labels to the\n                 * best edge.\n                 *\n                 * @type    {\"auto\"|null}\n                 * @since   5.0.12\n                 * @product highcharts\n                 */\n                axisLabelPosition: null,\n                /**\n                 * Provides the option to draw a frame around the charts by\n                 * defining a bottom, front and back panel.\n                 *\n                 * @since    4.0\n                 * @product  highcharts\n                 * @requires highcharts-3d\n                 */\n                frame: {\n                    /**\n                     * Whether the frames are visible.\n                     */\n                    visible: 'default',\n                    /**\n                     * General pixel thickness for the frame faces.\n                     */\n                    size: 1,\n                    /**\n                     * The bottom of the frame around a 3D chart.\n                     *\n                     * @since    4.0\n                     * @product  highcharts\n                     * @requires highcharts-3d\n                     */\n                    /**\n                     * The color of the panel.\n                     *\n                     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n                     * @default   transparent\n                     * @since     4.0\n                     * @product   highcharts\n                     * @apioption chart.options3d.frame.bottom.color\n                     */\n                    /**\n                     * The thickness of the panel.\n                     *\n                     * @type      {number}\n                     * @default   1\n                     * @since     4.0\n                     * @product   highcharts\n                     * @apioption chart.options3d.frame.bottom.size\n                     */\n                    /**\n                     * Whether to display the frame. Possible values are `true`,\n                     * `false`, `\"auto\"` to display only the frames behind the\n                     * data, and `\"default\"` to display faces behind the data\n                     * based on the axis layout, ignoring the point of view.\n                     *\n                     * @sample {highcharts} highcharts/3d/scatter-frame/\n                     *         Auto frames\n                     *\n                     * @type      {boolean|\"default\"|\"auto\"}\n                     * @default   default\n                     * @since     5.0.12\n                     * @product   highcharts\n                     * @apioption chart.options3d.frame.bottom.visible\n                     */\n                    /**\n                     * The bottom of the frame around a 3D chart.\n                     */\n                    bottom: {},\n                    /**\n                     * The top of the frame around a 3D chart.\n                     *\n                     * @extends chart.options3d.frame.bottom\n                     */\n                    top: {},\n                    /**\n                     * The left side of the frame around a 3D chart.\n                     *\n                     * @extends chart.options3d.frame.bottom\n                     */\n                    left: {},\n                    /**\n                     * The right of the frame around a 3D chart.\n                     *\n                     * @extends chart.options3d.frame.bottom\n                     */\n                    right: {},\n                    /**\n                     * The back side of the frame around a 3D chart.\n                     *\n                     * @extends chart.options3d.frame.bottom\n                     */\n                    back: {},\n                    /**\n                     * The front of the frame around a 3D chart.\n                     *\n                     * @extends chart.options3d.frame.bottom\n                     */\n                    front: {}\n                }\n            }\n        }\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    function compose(ChartClass, FxClass) {\n        const chartProto = ChartClass.prototype;\n        const fxProto = FxClass.prototype;\n        /**\n         * Shorthand to check the is3d flag.\n         * @private\n         * @return {boolean}\n         * Whether it is a 3D chart.\n         */\n        chartProto.is3d = function () {\n            return !!this.options.chart.options3d?.enabled;\n        };\n        chartProto.propsRequireDirtyBox.push('chart.options3d');\n        chartProto.propsRequireUpdateSeries.push('chart.options3d');\n        /**\n         * Animation setter for matrix property.\n         * @private\n         */\n        fxProto.matrixSetter = function () {\n            let interpolated;\n            if (this.pos < 1 &&\n                (isArray(this.start) || isArray(this.end))) {\n                const start = (this.start ||\n                    [1, 0, 0, 1, 0, 0]), end = this.end || [1, 0, 0, 1, 0, 0];\n                interpolated = [];\n                for (let i = 0; i < 6; i++) {\n                    interpolated.push(this.pos * end[i] + (1 - this.pos) * start[i]);\n                }\n            }\n            else {\n                interpolated = this.end;\n            }\n            this.elem.attr(this.prop, interpolated, null, true);\n        };\n        merge(true, genericDefaultOptions, Chart3D.defaultOptions);\n        addEvent(ChartClass, 'init', onInit);\n        addEvent(ChartClass, 'addSeries', onAddSeries);\n        addEvent(ChartClass, 'afterDrawChartBox', onAfterDrawChartBox);\n        addEvent(ChartClass, 'afterGetContainer', onAfterGetContainer);\n        addEvent(ChartClass, 'afterInit', onAfterInit);\n        addEvent(ChartClass, 'afterSetChartSize', onAfterSetChartSize);\n        addEvent(ChartClass, 'beforeRedraw', onBeforeRedraw);\n        addEvent(ChartClass, 'beforeRender', onBeforeRender);\n        wrap(chartProto, 'isInsidePlot', wrapIsInsidePlot);\n        wrap(chartProto, 'renderSeries', wrapRenderSeries);\n        wrap(chartProto, 'setClassName', wrapSetClassName);\n    }\n    Chart3D.compose = compose;\n    /**\n     * Legacy support for HC < 6 to make 'scatter' series in a 3D chart route to\n     * the real 'scatter3d' series type. (#8407)\n     * @private\n     */\n    function onAddSeries(e) {\n        if (this.is3d()) {\n            if (e.options.type === 'scatter') {\n                e.options.type = 'scatter3d';\n            }\n        }\n    }\n    /**\n     * @private\n     */\n    function onAfterDrawChartBox() {\n        if (this.chart3d &&\n            this.is3d()) {\n            const chart = this, renderer = chart.renderer, options3d = chart.options.chart.options3d, frame = chart.chart3d.get3dFrame(), xm = chart.plotLeft, xp = chart.plotLeft + chart.plotWidth, ym = chart.plotTop, yp = chart.plotTop + chart.plotHeight, zm = 0, zp = options3d.depth, xmm = xm - (frame.left.visible ? frame.left.size : 0), xpp = xp + (frame.right.visible ? frame.right.size : 0), ymm = ym - (frame.top.visible ? frame.top.size : 0), ypp = yp + (frame.bottom.visible ? frame.bottom.size : 0), zmm = zm - (frame.front.visible ? frame.front.size : 0), zpp = zp + (frame.back.visible ? frame.back.size : 0), verb = chart.hasRendered ? 'animate' : 'attr';\n            chart.chart3d.frame3d = frame;\n            if (!chart.frameShapes) {\n                chart.frameShapes = {\n                    bottom: renderer.polyhedron().add(),\n                    top: renderer.polyhedron().add(),\n                    left: renderer.polyhedron().add(),\n                    right: renderer.polyhedron().add(),\n                    back: renderer.polyhedron().add(),\n                    front: renderer.polyhedron().add()\n                };\n            }\n            chart.frameShapes.bottom[verb]({\n                'class': 'highcharts-3d-frame highcharts-3d-frame-bottom',\n                zIndex: frame.bottom.frontFacing ? -1000 : 1000,\n                faces: [{\n                        fill: color(frame.bottom.color).brighten(0.1).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xpp,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xpp,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xmm,\n                                y: ypp,\n                                z: zpp\n                            }],\n                        enabled: frame.bottom.visible\n                    },\n                    {\n                        fill: color(frame.bottom.color).brighten(0.1).get(),\n                        vertexes: [{\n                                x: xm,\n                                y: yp,\n                                z: zp\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zp\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zm\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zm\n                            }],\n                        enabled: frame.bottom.visible\n                    },\n                    {\n                        fill: color(frame.bottom.color).brighten(-0.1).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xmm,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zp\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zm\n                            }],\n                        enabled: frame.bottom.visible && !frame.left.visible\n                    },\n                    {\n                        fill: color(frame.bottom.color).brighten(-0.1).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xpp,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zm\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zp\n                            }],\n                        enabled: frame.bottom.visible && !frame.right.visible\n                    },\n                    {\n                        fill: color(frame.bottom.color).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xmm,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zm\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zm\n                            }],\n                        enabled: frame.bottom.visible && !frame.front.visible\n                    },\n                    {\n                        fill: color(frame.bottom.color).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xpp,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zp\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zp\n                            }],\n                        enabled: frame.bottom.visible && !frame.back.visible\n                    }]\n            });\n            chart.frameShapes.top[verb]({\n                'class': 'highcharts-3d-frame highcharts-3d-frame-top',\n                zIndex: frame.top.frontFacing ? -1000 : 1000,\n                faces: [{\n                        fill: color(frame.top.color).brighten(0.1).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xpp,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xpp,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xmm,\n                                y: ymm,\n                                z: zmm\n                            }],\n                        enabled: frame.top.visible\n                    },\n                    {\n                        fill: color(frame.top.color).brighten(0.1).get(),\n                        vertexes: [{\n                                x: xm,\n                                y: ym,\n                                z: zm\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zm\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zp\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zp\n                            }],\n                        enabled: frame.top.visible\n                    },\n                    {\n                        fill: color(frame.top.color).brighten(-0.1).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xmm,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zm\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zp\n                            }],\n                        enabled: frame.top.visible && !frame.left.visible\n                    },\n                    {\n                        fill: color(frame.top.color).brighten(-0.1).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xpp,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zp\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zm\n                            }],\n                        enabled: frame.top.visible && !frame.right.visible\n                    },\n                    {\n                        fill: color(frame.top.color).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xpp,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zm\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zm\n                            }],\n                        enabled: frame.top.visible && !frame.front.visible\n                    },\n                    {\n                        fill: color(frame.top.color).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xmm,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zp\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zp\n                            }],\n                        enabled: frame.top.visible && !frame.back.visible\n                    }]\n            });\n            chart.frameShapes.left[verb]({\n                'class': 'highcharts-3d-frame highcharts-3d-frame-left',\n                zIndex: frame.left.frontFacing ? -1000 : 1000,\n                faces: [{\n                        fill: color(frame.left.color).brighten(0.1).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zm\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zp\n                            }, {\n                                x: xmm,\n                                y: ypp,\n                                z: zpp\n                            }],\n                        enabled: frame.left.visible && !frame.bottom.visible\n                    },\n                    {\n                        fill: color(frame.left.color).brighten(0.1).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zp\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zm\n                            }, {\n                                x: xmm,\n                                y: ymm,\n                                z: zmm\n                            }],\n                        enabled: frame.left.visible && !frame.top.visible\n                    },\n                    {\n                        fill: color(frame.left.color).brighten(-0.1).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xmm,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xmm,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xmm,\n                                y: ypp,\n                                z: zmm\n                            }],\n                        enabled: frame.left.visible\n                    },\n                    {\n                        fill: color(frame.left.color).brighten(-0.1).get(),\n                        vertexes: [{\n                                x: xm,\n                                y: ym,\n                                z: zp\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zp\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zm\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zm\n                            }],\n                        enabled: frame.left.visible\n                    },\n                    {\n                        fill: color(frame.left.color).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xmm,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zm\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zm\n                            }],\n                        enabled: frame.left.visible && !frame.front.visible\n                    },\n                    {\n                        fill: color(frame.left.color).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xmm,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zp\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zp\n                            }],\n                        enabled: frame.left.visible && !frame.back.visible\n                    }]\n            });\n            chart.frameShapes.right[verb]({\n                'class': 'highcharts-3d-frame highcharts-3d-frame-right',\n                zIndex: frame.right.frontFacing ? -1000 : 1000,\n                faces: [{\n                        fill: color(frame.right.color).brighten(0.1).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zp\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zm\n                            }, {\n                                x: xpp,\n                                y: ypp,\n                                z: zmm\n                            }],\n                        enabled: frame.right.visible && !frame.bottom.visible\n                    },\n                    {\n                        fill: color(frame.right.color).brighten(0.1).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zm\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zp\n                            }, {\n                                x: xpp,\n                                y: ymm,\n                                z: zpp\n                            }],\n                        enabled: frame.right.visible && !frame.top.visible\n                    },\n                    {\n                        fill: color(frame.right.color).brighten(-0.1).get(),\n                        vertexes: [{\n                                x: xp,\n                                y: ym,\n                                z: zm\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zm\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zp\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zp\n                            }],\n                        enabled: frame.right.visible\n                    },\n                    {\n                        fill: color(frame.right.color).brighten(-0.1).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xpp,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xpp,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xpp,\n                                y: ypp,\n                                z: zpp\n                            }],\n                        enabled: frame.right.visible\n                    },\n                    {\n                        fill: color(frame.right.color).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xpp,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zm\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zm\n                            }],\n                        enabled: frame.right.visible && !frame.front.visible\n                    },\n                    {\n                        fill: color(frame.right.color).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xpp,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zp\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zp\n                            }],\n                        enabled: frame.right.visible && !frame.back.visible\n                    }]\n            });\n            chart.frameShapes.back[verb]({\n                'class': 'highcharts-3d-frame highcharts-3d-frame-back',\n                zIndex: frame.back.frontFacing ? -1000 : 1000,\n                faces: [{\n                        fill: color(frame.back.color).brighten(0.1).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xmm,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zp\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zp\n                            }],\n                        enabled: frame.back.visible && !frame.bottom.visible\n                    },\n                    {\n                        fill: color(frame.back.color).brighten(0.1).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xpp,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zp\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zp\n                            }],\n                        enabled: frame.back.visible && !frame.top.visible\n                    },\n                    {\n                        fill: color(frame.back.color).brighten(-0.1).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xmm,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zp\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zp\n                            }],\n                        enabled: frame.back.visible && !frame.left.visible\n                    },\n                    {\n                        fill: color(frame.back.color).brighten(-0.1).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xpp,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zp\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zp\n                            }],\n                        enabled: frame.back.visible && !frame.right.visible\n                    },\n                    {\n                        fill: color(frame.back.color).get(),\n                        vertexes: [{\n                                x: xm,\n                                y: ym,\n                                z: zp\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zp\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zp\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zp\n                            }],\n                        enabled: frame.back.visible\n                    },\n                    {\n                        fill: color(frame.back.color).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xpp,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xpp,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xmm,\n                                y: ymm,\n                                z: zpp\n                            }],\n                        enabled: frame.back.visible\n                    }]\n            });\n            chart.frameShapes.front[verb]({\n                'class': 'highcharts-3d-frame highcharts-3d-frame-front',\n                zIndex: frame.front.frontFacing ? -1000 : 1000,\n                faces: [{\n                        fill: color(frame.front.color).brighten(0.1).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xpp,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zm\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zm\n                            }],\n                        enabled: frame.front.visible && !frame.bottom.visible\n                    },\n                    {\n                        fill: color(frame.front.color).brighten(0.1).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xmm,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zm\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zm\n                            }],\n                        enabled: frame.front.visible && !frame.top.visible\n                    },\n                    {\n                        fill: color(frame.front.color).brighten(-0.1).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xmm,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zm\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zm\n                            }],\n                        enabled: frame.front.visible && !frame.left.visible\n                    },\n                    {\n                        fill: color(frame.front.color).brighten(-0.1).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xpp,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zm\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zm\n                            }],\n                        enabled: frame.front.visible && !frame.right.visible\n                    },\n                    {\n                        fill: color(frame.front.color).get(),\n                        vertexes: [{\n                                x: xp,\n                                y: ym,\n                                z: zm\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zm\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zm\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zm\n                            }],\n                        enabled: frame.front.visible\n                    },\n                    {\n                        fill: color(frame.front.color).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xmm,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xmm,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xpp,\n                                y: ymm,\n                                z: zmm\n                            }],\n                        enabled: frame.front.visible\n                    }]\n            });\n        }\n    }\n    /**\n     * Add the required CSS classes for column sides (#6018)\n     * @private\n     */\n    function onAfterGetContainer() {\n        if (this.styledMode) {\n            // Add definitions used by brighter and darker faces of the cuboids.\n            [{\n                    name: 'darker',\n                    slope: 0.6\n                }, {\n                    name: 'brighter',\n                    slope: 1.4\n                }].forEach(function (cfg) {\n                this.renderer.definition({\n                    tagName: 'filter',\n                    attributes: {\n                        id: 'highcharts-' + cfg.name\n                    },\n                    children: [{\n                            tagName: 'feComponentTransfer',\n                            children: [{\n                                    tagName: 'feFuncR',\n                                    attributes: {\n                                        type: 'linear',\n                                        slope: cfg.slope\n                                    }\n                                }, {\n                                    tagName: 'feFuncG',\n                                    attributes: {\n                                        type: 'linear',\n                                        slope: cfg.slope\n                                    }\n                                }, {\n                                    tagName: 'feFuncB',\n                                    attributes: {\n                                        type: 'linear',\n                                        slope: cfg.slope\n                                    }\n                                }]\n                        }]\n                });\n            }, this);\n        }\n    }\n    /**\n     * Legacy support for HC < 6 to make 'scatter' series in a 3D chart route to\n     * the real 'scatter3d' series type. (#8407)\n     * @private\n     */\n    function onAfterInit() {\n        const options = this.options;\n        if (this.is3d()) {\n            (options.series || []).forEach(function (s) {\n                const type = (s.type ||\n                    options.chart.type ||\n                    options.chart.defaultSeriesType);\n                if (type === 'scatter') {\n                    s.type = 'scatter3d';\n                }\n            });\n        }\n    }\n    /**\n     * @private\n     */\n    function onAfterSetChartSize() {\n        const chart = this, options3d = chart.options.chart.options3d;\n        if (chart.chart3d &&\n            chart.is3d()) {\n            // Add a 0-360 normalisation for alfa and beta angles in 3d graph\n            if (options3d) {\n                options3d.alpha = options3d.alpha % 360 +\n                    (options3d.alpha >= 0 ? 0 : 360);\n                options3d.beta = options3d.beta % 360 +\n                    (options3d.beta >= 0 ? 0 : 360);\n            }\n            const inverted = chart.inverted, clipBox = chart.clipBox, margin = chart.margin, x = inverted ? 'y' : 'x', y = inverted ? 'x' : 'y', w = inverted ? 'height' : 'width', h = inverted ? 'width' : 'height';\n            clipBox[x] = -(margin[3] || 0);\n            clipBox[y] = -(margin[0] || 0);\n            clipBox[w] = (chart.chartWidth + (margin[3] || 0) + (margin[1] || 0));\n            clipBox[h] = (chart.chartHeight + (margin[0] || 0) + (margin[2] || 0));\n            // Set scale, used later in perspective method():\n            // getScale uses perspective, so scale3d has to be reset.\n            chart.scale3d = 1;\n            if (options3d.fitToPlot === true) {\n                chart.scale3d = chart.chart3d.getScale(options3d.depth);\n            }\n            // Recalculate the 3d frame with every call of setChartSize,\n            // instead of doing it after every redraw(). It avoids ticks\n            // and axis title outside of chart.\n            chart.chart3d.frame3d = chart.chart3d.get3dFrame(); // #7942\n        }\n    }\n    /**\n     * @private\n     */\n    function onBeforeRedraw() {\n        if (this.is3d()) {\n            // Set to force a redraw of all elements\n            this.isDirtyBox = true;\n        }\n    }\n    /**\n     * @private\n     */\n    function onBeforeRender() {\n        if (this.chart3d && this.is3d()) {\n            this.chart3d.frame3d = this.chart3d.get3dFrame();\n        }\n    }\n    /**\n     * @private\n     */\n    function onInit() {\n        if (!this.chart3d) {\n            this.chart3d = new Additions(this);\n        }\n    }\n    /**\n     * @private\n     */\n    function wrapIsInsidePlot(proceed) {\n        return this.is3d() || proceed.apply(this, [].slice.call(arguments, 1));\n    }\n    /**\n     * Draw the series in the reverse order (#3803, #3917)\n     * @private\n     */\n    function wrapRenderSeries(proceed) {\n        let series, i = this.series.length;\n        if (this.is3d()) {\n            while (i--) {\n                series = this.series[i];\n                series.translate();\n                series.render();\n            }\n        }\n        else {\n            proceed.call(this);\n        }\n    }\n    /**\n     * @private\n     */\n    function wrapSetClassName(proceed) {\n        proceed.apply(this, [].slice.call(arguments, 1));\n        if (this.is3d()) {\n            this.container.className += ' highcharts-3d-chart';\n        }\n    }\n    /* *\n     *\n     *  Class\n     *\n     * */\n    class Additions {\n        /* *\n         *\n         *  Constructors\n         *\n         * */\n        constructor(chart) {\n            this.chart = chart;\n        }\n        /* *\n         *\n         *  Functions\n         *\n         * */\n        get3dFrame() {\n            const chart = this.chart, options3d = chart.options.chart.options3d, frameOptions = options3d.frame, xm = chart.plotLeft, xp = chart.plotLeft + chart.plotWidth, ym = chart.plotTop, yp = chart.plotTop + chart.plotHeight, zm = 0, zp = options3d.depth, faceOrientation = function (vertexes) {\n                const area = Chart3D_shapeArea3D(vertexes, chart);\n                // Give it 0.5 squared-pixel as a margin for rounding errors\n                if (area > 0.5) {\n                    return 1;\n                }\n                if (area < -0.5) {\n                    return -1;\n                }\n                return 0;\n            }, bottomOrientation = faceOrientation([\n                { x: xm, y: yp, z: zp },\n                { x: xp, y: yp, z: zp },\n                { x: xp, y: yp, z: zm },\n                { x: xm, y: yp, z: zm }\n            ]), topOrientation = faceOrientation([\n                { x: xm, y: ym, z: zm },\n                { x: xp, y: ym, z: zm },\n                { x: xp, y: ym, z: zp },\n                { x: xm, y: ym, z: zp }\n            ]), leftOrientation = faceOrientation([\n                { x: xm, y: ym, z: zm },\n                { x: xm, y: ym, z: zp },\n                { x: xm, y: yp, z: zp },\n                { x: xm, y: yp, z: zm }\n            ]), rightOrientation = faceOrientation([\n                { x: xp, y: ym, z: zp },\n                { x: xp, y: ym, z: zm },\n                { x: xp, y: yp, z: zm },\n                { x: xp, y: yp, z: zp }\n            ]), frontOrientation = faceOrientation([\n                { x: xm, y: yp, z: zm },\n                { x: xp, y: yp, z: zm },\n                { x: xp, y: ym, z: zm },\n                { x: xm, y: ym, z: zm }\n            ]), backOrientation = faceOrientation([\n                { x: xm, y: ym, z: zp },\n                { x: xp, y: ym, z: zp },\n                { x: xp, y: yp, z: zp },\n                { x: xm, y: yp, z: zp }\n            ]), defaultShowFront = false, defaultShowBack = true;\n            let defaultShowBottom = false, defaultShowTop = false, defaultShowLeft = false, defaultShowRight = false;\n            // The 'default' criteria to visible faces of the frame is looking\n            // up every axis to decide whenever the left/right//top/bottom sides\n            // of the frame will be shown\n            []\n                .concat(chart.xAxis, chart.yAxis, chart.zAxis)\n                .forEach(function (axis) {\n                if (axis) {\n                    if (axis.horiz) {\n                        if (axis.opposite) {\n                            defaultShowTop = true;\n                        }\n                        else {\n                            defaultShowBottom = true;\n                        }\n                    }\n                    else {\n                        if (axis.opposite) {\n                            defaultShowRight = true;\n                        }\n                        else {\n                            defaultShowLeft = true;\n                        }\n                    }\n                }\n            });\n            const getFaceOptions = function (sources, faceOrientation, defaultVisible) {\n                const faceAttrs = ['size', 'color', 'visible'], options = {};\n                for (let i = 0; i < faceAttrs.length; i++) {\n                    const attr = faceAttrs[i];\n                    for (let j = 0; j < sources.length; j++) {\n                        if (typeof sources[j] === 'object') {\n                            const val = sources[j][attr];\n                            if (typeof val !== 'undefined' && val !== null) {\n                                options[attr] = val;\n                                break;\n                            }\n                        }\n                    }\n                }\n                let isVisible = defaultVisible;\n                if (options.visible === true || options.visible === false) {\n                    isVisible = options.visible;\n                }\n                else if (options.visible === 'auto') {\n                    isVisible = faceOrientation > 0;\n                }\n                return {\n                    size: Chart3D_pick(options.size, 1),\n                    color: Chart3D_pick(options.color, 'none'),\n                    frontFacing: faceOrientation > 0,\n                    visible: isVisible\n                };\n            };\n            // Docs @TODO: Add all frame options (left, right, top, bottom,\n            // front, back) to apioptions JSDoc once the new system is up.\n            const ret = {\n                axes: {},\n                // FIXME: Previously, left/right, top/bottom and front/back\n                // pairs shared size and color.\n                // For compatibility and consistency sake, when one face have\n                // size/color/visibility set, the opposite face will default to\n                // the same values. Also, left/right used to be called 'side',\n                // so that's also added as a fallback.\n                bottom: getFaceOptions([frameOptions.bottom, frameOptions.top, frameOptions], bottomOrientation, defaultShowBottom),\n                top: getFaceOptions([frameOptions.top, frameOptions.bottom, frameOptions], topOrientation, defaultShowTop),\n                left: getFaceOptions([\n                    frameOptions.left,\n                    frameOptions.right,\n                    frameOptions.side,\n                    frameOptions\n                ], leftOrientation, defaultShowLeft),\n                right: getFaceOptions([\n                    frameOptions.right,\n                    frameOptions.left,\n                    frameOptions.side,\n                    frameOptions\n                ], rightOrientation, defaultShowRight),\n                back: getFaceOptions([frameOptions.back, frameOptions.front, frameOptions], backOrientation, defaultShowBack),\n                front: getFaceOptions([frameOptions.front, frameOptions.back, frameOptions], frontOrientation, defaultShowFront)\n            };\n            // Decide the bast place to put axis title/labels based on the\n            // visible faces. Ideally, The labels can only be on the edge\n            // between a visible face and an invisible one. Also, the Y label\n            // should be one the left-most edge (right-most if opposite).\n            if (options3d.axisLabelPosition === 'auto') {\n                const isValidEdge = function (face1, face2) {\n                    return ((face1.visible !== face2.visible) ||\n                        (face1.visible &&\n                            face2.visible &&\n                            (face1.frontFacing !== face2.frontFacing)));\n                };\n                const yEdges = [];\n                if (isValidEdge(ret.left, ret.front)) {\n                    yEdges.push({\n                        y: (ym + yp) / 2,\n                        x: xm,\n                        z: zm,\n                        xDir: { x: 1, y: 0, z: 0 }\n                    });\n                }\n                if (isValidEdge(ret.left, ret.back)) {\n                    yEdges.push({\n                        y: (ym + yp) / 2,\n                        x: xm,\n                        z: zp,\n                        xDir: { x: 0, y: 0, z: -1 }\n                    });\n                }\n                if (isValidEdge(ret.right, ret.front)) {\n                    yEdges.push({\n                        y: (ym + yp) / 2,\n                        x: xp,\n                        z: zm,\n                        xDir: { x: 0, y: 0, z: 1 }\n                    });\n                }\n                if (isValidEdge(ret.right, ret.back)) {\n                    yEdges.push({\n                        y: (ym + yp) / 2,\n                        x: xp,\n                        z: zp,\n                        xDir: { x: -1, y: 0, z: 0 }\n                    });\n                }\n                const xBottomEdges = [];\n                if (isValidEdge(ret.bottom, ret.front)) {\n                    xBottomEdges.push({\n                        x: (xm + xp) / 2,\n                        y: yp,\n                        z: zm,\n                        xDir: { x: 1, y: 0, z: 0 }\n                    });\n                }\n                if (isValidEdge(ret.bottom, ret.back)) {\n                    xBottomEdges.push({\n                        x: (xm + xp) / 2,\n                        y: yp,\n                        z: zp,\n                        xDir: { x: -1, y: 0, z: 0 }\n                    });\n                }\n                const xTopEdges = [];\n                if (isValidEdge(ret.top, ret.front)) {\n                    xTopEdges.push({\n                        x: (xm + xp) / 2,\n                        y: ym,\n                        z: zm,\n                        xDir: { x: 1, y: 0, z: 0 }\n                    });\n                }\n                if (isValidEdge(ret.top, ret.back)) {\n                    xTopEdges.push({\n                        x: (xm + xp) / 2,\n                        y: ym,\n                        z: zp,\n                        xDir: { x: -1, y: 0, z: 0 }\n                    });\n                }\n                const zBottomEdges = [];\n                if (isValidEdge(ret.bottom, ret.left)) {\n                    zBottomEdges.push({\n                        z: (zm + zp) / 2,\n                        y: yp,\n                        x: xm,\n                        xDir: { x: 0, y: 0, z: -1 }\n                    });\n                }\n                if (isValidEdge(ret.bottom, ret.right)) {\n                    zBottomEdges.push({\n                        z: (zm + zp) / 2,\n                        y: yp,\n                        x: xp,\n                        xDir: { x: 0, y: 0, z: 1 }\n                    });\n                }\n                const zTopEdges = [];\n                if (isValidEdge(ret.top, ret.left)) {\n                    zTopEdges.push({\n                        z: (zm + zp) / 2,\n                        y: ym,\n                        x: xm,\n                        xDir: { x: 0, y: 0, z: -1 }\n                    });\n                }\n                if (isValidEdge(ret.top, ret.right)) {\n                    zTopEdges.push({\n                        z: (zm + zp) / 2,\n                        y: ym,\n                        x: xp,\n                        xDir: { x: 0, y: 0, z: 1 }\n                    });\n                }\n                const pickEdge = function (edges, axis, mult) {\n                    if (edges.length === 0) {\n                        return null;\n                    }\n                    if (edges.length === 1) {\n                        return edges[0];\n                    }\n                    const projections = Chart3D_perspective(edges, chart, false);\n                    let best = 0;\n                    for (let i = 1; i < projections.length; i++) {\n                        if (mult * projections[i][axis] >\n                            mult * projections[best][axis]) {\n                            best = i;\n                        }\n                        else if ((mult * projections[i][axis] ===\n                            mult * projections[best][axis]) &&\n                            (projections[i].z < projections[best].z)) {\n                            best = i;\n                        }\n                    }\n                    return edges[best];\n                };\n                ret.axes = {\n                    y: {\n                        'left': pickEdge(yEdges, 'x', -1),\n                        'right': pickEdge(yEdges, 'x', +1)\n                    },\n                    x: {\n                        'top': pickEdge(xTopEdges, 'y', -1),\n                        'bottom': pickEdge(xBottomEdges, 'y', +1)\n                    },\n                    z: {\n                        'top': pickEdge(zTopEdges, 'y', -1),\n                        'bottom': pickEdge(zBottomEdges, 'y', +1)\n                    }\n                };\n            }\n            else {\n                ret.axes = {\n                    y: {\n                        'left': {\n                            x: xm, z: zm, xDir: { x: 1, y: 0, z: 0 }\n                        },\n                        'right': {\n                            x: xp, z: zm, xDir: { x: 0, y: 0, z: 1 }\n                        }\n                    },\n                    x: {\n                        'top': {\n                            y: ym, z: zm, xDir: { x: 1, y: 0, z: 0 }\n                        },\n                        'bottom': {\n                            y: yp,\n                            z: zm,\n                            xDir: { x: 1, y: 0, z: 0 }\n                        }\n                    },\n                    z: {\n                        'top': {\n                            x: defaultShowLeft ? xp : xm,\n                            y: ym,\n                            xDir: defaultShowLeft ?\n                                { x: 0, y: 0, z: 1 } :\n                                { x: 0, y: 0, z: -1 }\n                        },\n                        'bottom': {\n                            x: defaultShowLeft ? xp : xm,\n                            y: yp,\n                            xDir: defaultShowLeft ?\n                                { x: 0, y: 0, z: 1 } :\n                                { x: 0, y: 0, z: -1 }\n                        }\n                    }\n                };\n            }\n            return ret;\n        }\n        /**\n         * Calculate scale of the 3D view. That is required to fit chart's 3D\n         * projection into the actual plotting area. Reported as #4933.\n         *\n         * **Note:**\n         * This function should ideally take the plot values instead of a chart\n         * object, but since the chart object is needed for perspective it is\n         * not practical. Possible to make both getScale and perspective more\n         * logical and also immutable.\n         *\n         * @private\n         * @function getScale\n         *\n         * @param {number} depth\n         * The depth of the chart\n         *\n         * @return {number}\n         * The scale to fit the 3D chart into the plotting area.\n         *\n         * @requires highcharts-3d\n         */\n        getScale(depth) {\n            const chart = this.chart, plotLeft = chart.plotLeft, plotRight = chart.plotWidth + plotLeft, plotTop = chart.plotTop, plotBottom = chart.plotHeight + plotTop, originX = plotLeft + chart.plotWidth / 2, originY = plotTop + chart.plotHeight / 2, bbox3d = {\n                minX: Number.MAX_VALUE,\n                maxX: -Number.MAX_VALUE,\n                minY: Number.MAX_VALUE,\n                maxY: -Number.MAX_VALUE\n            };\n            let corners, scale = 1;\n            // Top left corners:\n            corners = [{\n                    x: plotLeft,\n                    y: plotTop,\n                    z: 0\n                }, {\n                    x: plotLeft,\n                    y: plotTop,\n                    z: depth\n                }];\n            // Top right corners:\n            [0, 1].forEach(function (i) {\n                corners.push({\n                    x: plotRight,\n                    y: corners[i].y,\n                    z: corners[i].z\n                });\n            });\n            // All bottom corners:\n            [0, 1, 2, 3].forEach(function (i) {\n                corners.push({\n                    x: corners[i].x,\n                    y: plotBottom,\n                    z: corners[i].z\n                });\n            });\n            // Calculate 3D corners:\n            corners = Chart3D_perspective(corners, chart, false);\n            // Get bounding box of 3D element:\n            corners.forEach(function (corner) {\n                bbox3d.minX = Math.min(bbox3d.minX, corner.x);\n                bbox3d.maxX = Math.max(bbox3d.maxX, corner.x);\n                bbox3d.minY = Math.min(bbox3d.minY, corner.y);\n                bbox3d.maxY = Math.max(bbox3d.maxY, corner.y);\n            });\n            // Left edge:\n            if (plotLeft > bbox3d.minX) {\n                scale = Math.min(scale, 1 - Math.abs((plotLeft + originX) / (bbox3d.minX + originX)) % 1);\n            }\n            // Right edge:\n            if (plotRight < bbox3d.maxX) {\n                scale = Math.min(scale, (plotRight - originX) / (bbox3d.maxX - originX));\n            }\n            // Top edge:\n            if (plotTop > bbox3d.minY) {\n                if (bbox3d.minY < 0) {\n                    scale = Math.min(scale, (plotTop + originY) / (-bbox3d.minY + plotTop + originY));\n                }\n                else {\n                    scale = Math.min(scale, 1 - (plotTop + originY) / (bbox3d.minY + originY) % 1);\n                }\n            }\n            // Bottom edge:\n            if (plotBottom < bbox3d.maxY) {\n                scale = Math.min(scale, Math.abs((plotBottom - originY) / (bbox3d.maxY - originY)));\n            }\n            return scale;\n        }\n    }\n    Chart3D.Additions = Additions;\n})(Chart3D || (Chart3D = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Chart_Chart3D = (Chart3D);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Note: As of v5.0.12, `frame.left` or `frame.right` should be used instead.\n *\n * The side for the frame around a 3D chart.\n *\n * @deprecated\n * @since     4.0\n * @product   highcharts\n * @requires  highcharts-3d\n * @apioption chart.options3d.frame.side\n */\n/**\n * The color of the panel.\n *\n * @deprecated\n * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n * @default   transparent\n * @since     4.0\n * @product   highcharts\n * @apioption chart.options3d.frame.side.color\n */\n/**\n * The thickness of the panel.\n *\n * @deprecated\n * @type      {number}\n * @default   1\n * @since     4.0\n * @product   highcharts\n * @apioption chart.options3d.frame.side.size\n */\n''; // Keeps doclets above in JS file\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/Area3D/Area3DSeries.js\n/* *\n *\n *  (c) 2010-2025 Grzegorz Blachliński\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { perspective: Area3DSeries_perspective } = Core_Math3D;\n\nconst { line: { prototype: lineProto } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { pushUnique, wrap: Area3DSeries_wrap } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n *\n */\nfunction compose(AreaSeriesClass) {\n    if (pushUnique(composed, 'Area3DSeries')) {\n        Area3DSeries_wrap(AreaSeriesClass.prototype, 'getGraphPath', wrapAreaSeriesGetGraphPath);\n    }\n}\n/**\n *\n */\nfunction wrapAreaSeriesGetGraphPath(proceed) {\n    const series = this, svgPath = proceed.apply(series, [].slice.call(arguments, 1));\n    // Do not do this if the chart is not 3D\n    if (!series.chart.is3d()) {\n        return svgPath;\n    }\n    const getGraphPath = lineProto.getGraphPath, options = series.options, translatedThreshold = Math.round(// #10909\n    series.yAxis.getThreshold(options.threshold));\n    let bottomPoints = [];\n    if (series.rawPointsX) {\n        for (let i = 0; i < series.points.length; i++) {\n            bottomPoints.push({\n                x: series.rawPointsX[i],\n                y: options.stacking ?\n                    series.points[i].yBottom : translatedThreshold,\n                z: series.zPadding\n            });\n        }\n    }\n    const options3d = series.chart.options.chart.options3d;\n    bottomPoints = Area3DSeries_perspective(bottomPoints, series.chart, true).map((point) => ({ plotX: point.x, plotY: point.y, plotZ: point.z }));\n    if (series.group && options3d && options3d.depth && options3d.beta) {\n        // Markers should take the global zIndex of series group.\n        if (series.markerGroup) {\n            series.markerGroup.add(series.group);\n            series.markerGroup.attr({\n                translateX: 0,\n                translateY: 0\n            });\n        }\n        series.group.attr({\n            zIndex: Math.max(1, (options3d.beta > 270 || options3d.beta < 90) ?\n                options3d.depth - Math.round(series.zPadding || 0) :\n                Math.round(series.zPadding || 0))\n        });\n    }\n    bottomPoints.reversed = true;\n    const bottomPath = getGraphPath.call(series, bottomPoints, true, true);\n    if (bottomPath[0] && bottomPath[0][0] === 'M') {\n        bottomPath[0] = ['L', bottomPath[0][1], bottomPath[0][2]];\n    }\n    if (series.areaPath) {\n        // Remove previously used bottomPath and add the new one.\n        const areaPath = series.areaPath.splice(0, series.areaPath.length / 2).concat(bottomPath);\n        // Use old xMap in the new areaPath\n        areaPath.xMap = series.areaPath.xMap;\n        series.areaPath = areaPath;\n    }\n    series.graphPath = svgPath;\n    return svgPath;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst Area3DSeries = {\n    compose\n};\n/* harmony default export */ const Area3D_Area3DSeries = (Area3DSeries);\n\n;// ./code/es-modules/Core/Axis/Axis3DDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  Extension for 3d axes\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n/**\n * @optionparent xAxis\n */\nconst Axis3DDefaults = {\n    labels: {\n        /**\n         * Defines how the labels are be repositioned according to the 3D\n         * chart orientation.\n         *\n         * - `'offset'`: Maintain a fixed horizontal/vertical distance from\n         *   the tick marks, despite the chart orientation. This is the\n         *   backwards compatible behavior, and causes skewing of X and Z\n         *   axes.\n         *\n         * - `'chart'`: Preserve 3D position relative to the chart. This\n         *   looks nice, but hard to read if the text isn't forward-facing.\n         *\n         * - `'flap'`: Rotated text along the axis to compensate for the\n         *   chart orientation. This tries to maintain text as legible as\n         *   possible on all orientations.\n         *\n         * - `'ortho'`: Rotated text along the axis direction so that the\n         *   labels are orthogonal to the axis. This is very similar to\n         *   `'flap'`, but prevents skewing the labels (X and Y scaling are\n         *   still present).\n         *\n         * @sample highcharts/3d/skewed-labels/\n         *         Skewed labels\n         *\n         * @since      5.0.15\n         * @validvalue ['offset', 'chart', 'flap', 'ortho']\n         * @product    highcharts\n         * @requires   highcharts-3d\n         */\n        position3d: 'offset',\n        /**\n         * If enabled, the axis labels will skewed to follow the\n         * perspective.\n         *\n         * This will fix overlapping labels and titles, but texts become\n         * less legible due to the distortion.\n         *\n         * The final appearance depends heavily on `labels.position3d`.\n         *\n         * @sample highcharts/3d/skewed-labels/\n         *         Skewed labels\n         *\n         * @since    5.0.15\n         * @product  highcharts\n         * @requires highcharts-3d\n         */\n        skew3d: false\n    },\n    title: {\n        /**\n         * Defines how the title is repositioned according to the 3D chart\n         * orientation.\n         *\n         * - `'offset'`: Maintain a fixed horizontal/vertical distance from\n         *   the tick marks, despite the chart orientation. This is the\n         *   backwards compatible behavior, and causes skewing of X and Z\n         *   axes.\n         *\n         * - `'chart'`: Preserve 3D position relative to the chart. This\n         *   looks nice, but hard to read if the text isn't forward-facing.\n         *\n         * - `'flap'`: Rotated text along the axis to compensate for the\n         *   chart orientation. This tries to maintain text as legible as\n         *   possible on all orientations.\n         *\n         * - `'ortho'`: Rotated text along the axis direction so that the\n         *   labels are orthogonal to the axis. This is very similar to\n         *   `'flap'`, but prevents skewing the labels (X and Y scaling are\n         *   still present).\n         *\n         * - `undefined`: Will use the config from `labels.position3d`\n         *\n         * @sample highcharts/3d/skewed-labels/\n         *         Skewed labels\n         *\n         * @type     {\"offset\"|\"chart\"|\"flap\"|\"ortho\"|null}\n         * @since    5.0.15\n         * @product  highcharts\n         * @requires highcharts-3d\n         */\n        position3d: null,\n        /**\n         * If enabled, the axis title will skewed to follow the perspective.\n         *\n         * This will fix overlapping labels and titles, but texts become\n         * less legible due to the distortion.\n         *\n         * The final appearance depends heavily on `title.position3d`.\n         *\n         * A `null` value will use the config from `labels.skew3d`.\n         *\n         * @sample highcharts/3d/skewed-labels/\n         *         Skewed labels\n         *\n         * @type     {boolean|null}\n         * @since    5.0.15\n         * @product  highcharts\n         * @requires highcharts-3d\n         */\n        skew3d: null\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Axis_Axis3DDefaults = (Axis3DDefaults);\n\n;// ./code/es-modules/Core/Axis/Tick3DComposition.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  Extension for 3d axes\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { composed: Tick3DComposition_composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent: Tick3DComposition_addEvent, extend, pushUnique: Tick3DComposition_pushUnique, wrap: Tick3DComposition_wrap } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction Tick3DComposition_compose(TickClass) {\n    if (Tick3DComposition_pushUnique(Tick3DComposition_composed, 'Axis.Tick3D')) {\n        Tick3DComposition_addEvent(TickClass, 'afterGetLabelPosition', onTickAfterGetLabelPosition);\n        Tick3DComposition_wrap(TickClass.prototype, 'getMarkPath', wrapTickGetMarkPath);\n    }\n}\n/**\n * @private\n */\nfunction onTickAfterGetLabelPosition(e) {\n    const axis3D = this.axis.axis3D;\n    if (axis3D) {\n        extend(e.pos, axis3D.fix3dPosition(e.pos));\n    }\n}\n/**\n * @private\n */\nfunction wrapTickGetMarkPath(proceed) {\n    const axis3D = this.axis.axis3D, path = proceed.apply(this, [].slice.call(arguments, 1));\n    if (axis3D) {\n        const start = path[0];\n        const end = path[1];\n        if (start[0] === 'M' && end[0] === 'L') {\n            const pArr = [\n                axis3D.fix3dPosition({ x: start[1], y: start[2], z: 0 }),\n                axis3D.fix3dPosition({ x: end[1], y: end[2], z: 0 })\n            ];\n            return this.axis.chart.renderer.toLineSegments(pArr);\n        }\n    }\n    return path;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst Tick3DAdditions = {\n    compose: Tick3DComposition_compose\n};\n/* harmony default export */ const Tick3DComposition = (Tick3DAdditions);\n\n;// ./code/es-modules/Core/Axis/Axis3DComposition.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  Extension for 3d axes\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { defaultOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { deg2rad: Axis3DComposition_deg2rad } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { perspective: Axis3DComposition_perspective, perspective3D: Axis3DComposition_perspective3D, shapeArea: Axis3DComposition_shapeArea } = Core_Math3D;\n\n\nconst { addEvent: Axis3DComposition_addEvent, merge: Axis3DComposition_merge, pick: Axis3DComposition_pick, wrap: Axis3DComposition_wrap } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction onAxisAfterSetOptions() {\n    const axis = this, chart = axis.chart, options = axis.options;\n    if (chart.is3d?.() && axis.coll !== 'colorAxis') {\n        options.tickWidth = Axis3DComposition_pick(options.tickWidth, 0);\n        options.gridLineWidth = Axis3DComposition_pick(options.gridLineWidth, 1);\n    }\n}\n/**\n * @private\n */\nfunction onAxisDrawCrosshair(e) {\n    const axis = this;\n    if (axis.chart.is3d() &&\n        axis.coll !== 'colorAxis') {\n        if (e.point) {\n            e.point.crosshairPos = axis.isXAxis ?\n                e.point.axisXpos :\n                axis.len - e.point.axisYpos;\n        }\n    }\n}\n/**\n * @private\n */\nfunction onAxisInit() {\n    const axis = this;\n    if (!axis.axis3D) {\n        axis.axis3D = new Axis3DAdditions(axis);\n    }\n}\n/**\n * Do not draw axislines in 3D.\n * @private\n */\nfunction wrapAxisGetLinePath(proceed) {\n    const axis = this;\n    // Do not do this if the chart is not 3D\n    if (!axis.chart.is3d() || axis.coll === 'colorAxis') {\n        return proceed.apply(axis, [].slice.call(arguments, 1));\n    }\n    return [];\n}\n/**\n * @private\n */\nfunction wrapAxisGetPlotBandPath(proceed) {\n    // Do not do this if the chart is not 3D\n    if (!this.chart.is3d() || this.coll === 'colorAxis') {\n        return proceed.apply(this, [].slice.call(arguments, 1));\n    }\n    const args = arguments, from = args[1], to = args[2], path = [], fromPath = this.getPlotLinePath({ value: from }), toPath = this.getPlotLinePath({ value: to });\n    if (fromPath && toPath) {\n        for (let i = 0; i < fromPath.length; i += 2) {\n            const fromStartSeg = fromPath[i], fromEndSeg = fromPath[i + 1], toStartSeg = toPath[i], toEndSeg = toPath[i + 1];\n            if (fromStartSeg[0] === 'M' &&\n                fromEndSeg[0] === 'L' &&\n                toStartSeg[0] === 'M' &&\n                toEndSeg[0] === 'L') {\n                path.push(fromStartSeg, fromEndSeg, toEndSeg, \n                // `lineTo` instead of `moveTo`\n                ['L', toStartSeg[1], toStartSeg[2]], ['Z']);\n            }\n        }\n    }\n    return path;\n}\n/**\n * @private\n */\nfunction wrapAxisGetPlotLinePath(proceed) {\n    const axis = this, axis3D = axis.axis3D, chart = axis.chart, path = proceed.apply(axis, [].slice.call(arguments, 1));\n    // Do not do this if the chart is not 3D\n    if (axis.coll === 'colorAxis' ||\n        !chart.chart3d ||\n        !chart.is3d()) {\n        return path;\n    }\n    if (path === null) {\n        return path;\n    }\n    const options3d = chart.options.chart.options3d, d = axis.isZAxis ? chart.plotWidth : options3d.depth, frame = chart.chart3d.frame3d, startSegment = path[0], endSegment = path[1];\n    let pArr, pathSegments = [];\n    if (startSegment[0] === 'M' && endSegment[0] === 'L') {\n        pArr = [\n            axis3D.swapZ({ x: startSegment[1], y: startSegment[2], z: 0 }),\n            axis3D.swapZ({ x: startSegment[1], y: startSegment[2], z: d }),\n            axis3D.swapZ({ x: endSegment[1], y: endSegment[2], z: 0 }),\n            axis3D.swapZ({ x: endSegment[1], y: endSegment[2], z: d })\n        ];\n        if (!this.horiz) { // Y-Axis\n            if (frame.front.visible) {\n                pathSegments.push(pArr[0], pArr[2]);\n            }\n            if (frame.back.visible) {\n                pathSegments.push(pArr[1], pArr[3]);\n            }\n            if (frame.left.visible) {\n                pathSegments.push(pArr[0], pArr[1]);\n            }\n            if (frame.right.visible) {\n                pathSegments.push(pArr[2], pArr[3]);\n            }\n        }\n        else if (this.isZAxis) { // Z-Axis\n            if (frame.left.visible) {\n                pathSegments.push(pArr[0], pArr[2]);\n            }\n            if (frame.right.visible) {\n                pathSegments.push(pArr[1], pArr[3]);\n            }\n            if (frame.top.visible) {\n                pathSegments.push(pArr[0], pArr[1]);\n            }\n            if (frame.bottom.visible) {\n                pathSegments.push(pArr[2], pArr[3]);\n            }\n        }\n        else { // X-Axis\n            if (frame.front.visible) {\n                pathSegments.push(pArr[0], pArr[2]);\n            }\n            if (frame.back.visible) {\n                pathSegments.push(pArr[1], pArr[3]);\n            }\n            if (frame.top.visible) {\n                pathSegments.push(pArr[0], pArr[1]);\n            }\n            if (frame.bottom.visible) {\n                pathSegments.push(pArr[2], pArr[3]);\n            }\n        }\n        pathSegments = Axis3DComposition_perspective(pathSegments, this.chart, false);\n    }\n    return chart.renderer.toLineSegments(pathSegments);\n}\n/**\n * Wrap getSlotWidth function to calculate individual width value for each\n * slot (#8042).\n * @private\n */\nfunction wrapAxisGetSlotWidth(proceed, tick) {\n    const axis = this, { chart, gridGroup, tickPositions, ticks } = axis;\n    if (axis.categories &&\n        chart.frameShapes &&\n        chart.is3d() &&\n        gridGroup &&\n        tick &&\n        tick.label) {\n        const firstGridLine = (gridGroup.element.childNodes[0].getBBox()), frame3DLeft = chart.frameShapes.left.getBBox(), options3d = chart.options.chart.options3d, origin = {\n            x: chart.plotWidth / 2,\n            y: chart.plotHeight / 2,\n            z: options3d.depth / 2,\n            vd: (Axis3DComposition_pick(options3d.depth, 1) *\n                Axis3DComposition_pick(options3d.viewDistance, 0))\n        }, index = tickPositions.indexOf(tick.pos), prevTick = ticks[tickPositions[index - 1]], nextTick = ticks[tickPositions[index + 1]];\n        let labelPos, prevLabelPos, nextLabelPos;\n        // Check whether the tick is not the first one and previous tick\n        // exists, then calculate position of previous label.\n        if (prevTick?.label?.xy) {\n            prevLabelPos = Axis3DComposition_perspective3D({\n                x: prevTick.label.xy.x,\n                y: prevTick.label.xy.y,\n                z: null\n            }, origin, origin.vd);\n        }\n        // If next label position is defined, then recalculate its position\n        // basing on the perspective.\n        if (nextTick?.label?.xy) {\n            nextLabelPos = Axis3DComposition_perspective3D({\n                x: nextTick.label.xy.x,\n                y: nextTick.label.xy.y,\n                z: null\n            }, origin, origin.vd);\n        }\n        labelPos = {\n            x: tick.label.xy.x,\n            y: tick.label.xy.y,\n            z: null\n        };\n        labelPos = Axis3DComposition_perspective3D(labelPos, origin, origin.vd);\n        // If tick is first one, check whether next label position is\n        // already calculated, then return difference between the first and\n        // the second label. If there is no next label position calculated,\n        // return the difference between the first grid line and left 3d\n        // frame.\n        return Math.abs(prevLabelPos ?\n            labelPos.x - prevLabelPos.x : nextLabelPos ?\n            nextLabelPos.x - labelPos.x :\n            firstGridLine.x - frame3DLeft.x);\n    }\n    return proceed.apply(axis, [].slice.call(arguments, 1));\n}\n/**\n * @private\n */\nfunction wrapAxisGetTitlePosition(proceed) {\n    const pos = proceed.apply(this, [].slice.call(arguments, 1));\n    return this.axis3D ?\n        this.axis3D.fix3dPosition(pos, true) :\n        pos;\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * Adds 3D support to axes.\n * @private\n * @class\n */\nclass Axis3DAdditions {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Extends axis class with 3D support.\n     * @private\n     */\n    static compose(AxisClass, TickClass) {\n        Tick3DComposition.compose(TickClass);\n        if (!AxisClass.keepProps.includes('axis3D')) {\n            Axis3DComposition_merge(true, defaultOptions.xAxis, Axis_Axis3DDefaults);\n            AxisClass.keepProps.push('axis3D');\n            Axis3DComposition_addEvent(AxisClass, 'init', onAxisInit);\n            Axis3DComposition_addEvent(AxisClass, 'afterSetOptions', onAxisAfterSetOptions);\n            Axis3DComposition_addEvent(AxisClass, 'drawCrosshair', onAxisDrawCrosshair);\n            const axisProto = AxisClass.prototype;\n            Axis3DComposition_wrap(axisProto, 'getLinePath', wrapAxisGetLinePath);\n            Axis3DComposition_wrap(axisProto, 'getPlotBandPath', wrapAxisGetPlotBandPath);\n            Axis3DComposition_wrap(axisProto, 'getPlotLinePath', wrapAxisGetPlotLinePath);\n            Axis3DComposition_wrap(axisProto, 'getSlotWidth', wrapAxisGetSlotWidth);\n            Axis3DComposition_wrap(axisProto, 'getTitlePosition', wrapAxisGetTitlePosition);\n        }\n    }\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    /**\n     * @private\n     */\n    constructor(axis) {\n        this.axis = axis;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     * @param {Highcharts.Axis} axis\n     * Related axis.\n     * @param {Highcharts.Position3DObject} pos\n     * Position to fix.\n     * @param {boolean} [isTitle]\n     * Whether this is a title position.\n     * @return {Highcharts.Position3DObject}\n     * Fixed position.\n     */\n    fix3dPosition(pos, isTitle) {\n        const axis3D = this;\n        const axis = axis3D.axis;\n        const chart = axis.chart;\n        // Do not do this if the chart is not 3D\n        if (axis.coll === 'colorAxis' ||\n            !chart.chart3d ||\n            !chart.is3d()) {\n            return pos;\n        }\n        const alpha = Axis3DComposition_deg2rad * chart.options.chart.options3d.alpha, beta = Axis3DComposition_deg2rad * chart.options.chart.options3d.beta, positionMode = Axis3DComposition_pick(isTitle && axis.options.title.position3d, axis.options.labels.position3d), skew = Axis3DComposition_pick(isTitle && axis.options.title.skew3d, axis.options.labels.skew3d), frame = chart.chart3d.frame3d, plotLeft = chart.plotLeft, plotRight = chart.plotWidth + plotLeft, plotTop = chart.plotTop, plotBottom = chart.plotHeight + plotTop;\n        let offsetX = 0, offsetY = 0, vecX, vecY = { x: 0, y: 1, z: 0 }, \n        // Indicates that we are labelling an X or Z axis on the \"back\" of\n        // the chart\n        reverseFlap = false;\n        pos = axis.axis3D.swapZ({ x: pos.x, y: pos.y, z: 0 });\n        if (axis.isZAxis) { // Z Axis\n            if (axis.opposite) {\n                if (frame.axes.z.top === null) {\n                    return {};\n                }\n                offsetY = pos.y - plotTop;\n                pos.x = frame.axes.z.top.x;\n                pos.y = frame.axes.z.top.y;\n                vecX = frame.axes.z.top.xDir;\n                reverseFlap = !frame.top.frontFacing;\n            }\n            else {\n                if (frame.axes.z.bottom === null) {\n                    return {};\n                }\n                offsetY = pos.y - plotBottom;\n                pos.x = frame.axes.z.bottom.x;\n                pos.y = frame.axes.z.bottom.y;\n                vecX = frame.axes.z.bottom.xDir;\n                reverseFlap = !frame.bottom.frontFacing;\n            }\n        }\n        else if (axis.horiz) { // X Axis\n            if (axis.opposite) {\n                if (frame.axes.x.top === null) {\n                    return {};\n                }\n                offsetY = pos.y - plotTop;\n                pos.y = frame.axes.x.top.y;\n                pos.z = frame.axes.x.top.z;\n                vecX = frame.axes.x.top.xDir;\n                reverseFlap = !frame.top.frontFacing;\n            }\n            else {\n                if (frame.axes.x.bottom === null) {\n                    return {};\n                }\n                offsetY = pos.y - plotBottom;\n                pos.y = frame.axes.x.bottom.y;\n                pos.z = frame.axes.x.bottom.z;\n                vecX = frame.axes.x.bottom.xDir;\n                reverseFlap = !frame.bottom.frontFacing;\n            }\n        }\n        else { // Y Axis\n            if (axis.opposite) {\n                if (frame.axes.y.right === null) {\n                    return {};\n                }\n                offsetX = pos.x - plotRight;\n                pos.x = frame.axes.y.right.x;\n                pos.z = frame.axes.y.right.z;\n                vecX = frame.axes.y.right.xDir;\n                // Rotate 90º on opposite edge\n                vecX = { x: vecX.z, y: vecX.y, z: -vecX.x };\n            }\n            else {\n                if (frame.axes.y.left === null) {\n                    return {};\n                }\n                offsetX = pos.x - plotLeft;\n                pos.x = frame.axes.y.left.x;\n                pos.z = frame.axes.y.left.z;\n                vecX = frame.axes.y.left.xDir;\n            }\n        }\n        if (positionMode === 'chart') {\n            // Labels preserve their direction relative to the chart\n            // nothing to do\n        }\n        else if (positionMode === 'flap') {\n            // Labels are rotated around the axis direction to face the screen\n            if (!axis.horiz) { // Y Axis\n                vecX = { x: Math.cos(beta), y: 0, z: Math.sin(beta) };\n            }\n            else { // X and Z Axis\n                let sin = Math.sin(alpha);\n                const cos = Math.cos(alpha);\n                if (axis.opposite) {\n                    sin = -sin;\n                }\n                if (reverseFlap) {\n                    sin = -sin;\n                }\n                vecY = { x: vecX.z * sin, y: cos, z: -vecX.x * sin };\n            }\n        }\n        else if (positionMode === 'ortho') {\n            // Labels will be rotated to be orthogonal to the axis\n            if (!axis.horiz) { // Y Axis\n                vecX = { x: Math.cos(beta), y: 0, z: Math.sin(beta) };\n            }\n            else { // X and Z Axis\n                const sina = Math.sin(alpha);\n                const cosa = Math.cos(alpha);\n                const sinb = Math.sin(beta);\n                const cosb = Math.cos(beta);\n                const vecZ = { x: sinb * cosa, y: -sina, z: -cosa * cosb };\n                vecY = {\n                    x: vecX.y * vecZ.z - vecX.z * vecZ.y,\n                    y: vecX.z * vecZ.x - vecX.x * vecZ.z,\n                    z: vecX.x * vecZ.y - vecX.y * vecZ.x\n                };\n                let scale = 1 / Math.sqrt(vecY.x * vecY.x + vecY.y * vecY.y + vecY.z * vecY.z);\n                if (reverseFlap) {\n                    scale = -scale;\n                }\n                vecY = {\n                    x: scale * vecY.x, y: scale * vecY.y, z: scale * vecY.z\n                };\n            }\n        }\n        else { // Position mode  == 'offset'\n            // Labels will be skewd to maintain vertical / horizontal offsets\n            // from axis\n            if (!axis.horiz) { // Y Axis\n                vecX = { x: Math.cos(beta), y: 0, z: Math.sin(beta) };\n            }\n            else { // X and Z Axis\n                vecY = {\n                    x: Math.sin(beta) * Math.sin(alpha),\n                    y: Math.cos(alpha),\n                    z: -Math.cos(beta) * Math.sin(alpha)\n                };\n            }\n        }\n        pos.x += offsetX * vecX.x + offsetY * vecY.x;\n        pos.y += offsetX * vecX.y + offsetY * vecY.y;\n        pos.z += offsetX * vecX.z + offsetY * vecY.z;\n        const projected = Axis3DComposition_perspective([pos], axis.chart)[0];\n        if (skew) {\n            // Check if the label text would be mirrored\n            const isMirrored = Axis3DComposition_shapeArea(Axis3DComposition_perspective([\n                pos,\n                { x: pos.x + vecX.x, y: pos.y + vecX.y, z: pos.z + vecX.z },\n                { x: pos.x + vecY.x, y: pos.y + vecY.y, z: pos.z + vecY.z }\n            ], axis.chart)) < 0;\n            if (isMirrored) {\n                vecX = { x: -vecX.x, y: -vecX.y, z: -vecX.z };\n            }\n            const pointsProjected = Axis3DComposition_perspective([\n                { x: pos.x, y: pos.y, z: pos.z },\n                { x: pos.x + vecX.x, y: pos.y + vecX.y, z: pos.z + vecX.z },\n                { x: pos.x + vecY.x, y: pos.y + vecY.y, z: pos.z + vecY.z }\n            ], axis.chart);\n            projected.matrix = [\n                pointsProjected[1].x - pointsProjected[0].x,\n                pointsProjected[1].y - pointsProjected[0].y,\n                pointsProjected[2].x - pointsProjected[0].x,\n                pointsProjected[2].y - pointsProjected[0].y,\n                projected.x,\n                projected.y\n            ];\n            projected.matrix[4] -= projected.x * projected.matrix[0] +\n                projected.y * projected.matrix[2];\n            projected.matrix[5] -= projected.x * projected.matrix[1] +\n                projected.y * projected.matrix[3];\n        }\n        return projected;\n    }\n    /**\n     * @private\n     */\n    swapZ(p, insidePlotArea) {\n        const axis = this.axis;\n        if (axis.isZAxis) {\n            const plotLeft = insidePlotArea ? 0 : axis.chart.plotLeft;\n            return {\n                x: plotLeft + p.z,\n                y: p.y,\n                z: p.x - plotLeft\n            };\n        }\n        return p;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Axis3DComposition = (Axis3DAdditions);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"RendererRegistry\"],\"commonjs\":[\"highcharts\",\"RendererRegistry\"],\"commonjs2\":[\"highcharts\",\"RendererRegistry\"],\"root\":[\"Highcharts\",\"RendererRegistry\"]}\nvar highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_ = __webpack_require__(608);\nvar highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Series\"],\"commonjs\":[\"highcharts\",\"Series\"],\"commonjs2\":[\"highcharts\",\"Series\"],\"root\":[\"Highcharts\",\"Series\"]}\nvar highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_ = __webpack_require__(820);\nvar highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default = /*#__PURE__*/__webpack_require__.n(highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_);\n;// ./code/es-modules/Core/Series/Series3D.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  Extension to the Series object in 3D charts.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { composed: Series3D_composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { perspective: Series3D_perspective } = Core_Math3D;\n\n\nconst { addEvent: Series3D_addEvent, extend: Series3D_extend, isNumber, merge: Series3D_merge, pick: Series3D_pick, pushUnique: Series3D_pushUnique } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass Series3D extends (highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default()) {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static compose(SeriesClass) {\n        if (Series3D_pushUnique(Series3D_composed, 'Core.Series3D')) {\n            Series3D_addEvent(SeriesClass, 'afterTranslate', function () {\n                if (this.chart.is3d()) {\n                    this.translate3dPoints();\n                }\n            });\n            Series3D_extend(SeriesClass.prototype, {\n                translate3dPoints: Series3D.prototype.translate3dPoints\n            });\n        }\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Translate the plotX, plotY properties and add plotZ.\n     * @private\n     */\n    translate3dPoints() {\n        const series = this, seriesOptions = series.options, chart = series.chart, zAxis = Series3D_pick(series.zAxis, chart.options.zAxis[0]), rawPoints = [], rawPointsX = [], stack = seriesOptions.stacking ?\n            (isNumber(seriesOptions.stack) ? seriesOptions.stack : 0) :\n            series.index || 0;\n        let projectedPoint, zValue;\n        series.zPadding = stack *\n            (seriesOptions.depth || 0 + (seriesOptions.groupZPadding || 1));\n        series.data.forEach((rawPoint) => {\n            if (zAxis?.translate) {\n                zValue = zAxis.logarithmic && zAxis.val2lin ?\n                    zAxis.val2lin(rawPoint.z) :\n                    rawPoint.z; // #4562\n                rawPoint.plotZ = zAxis.translate(zValue);\n                rawPoint.isInside = rawPoint.isInside ?\n                    (zValue >= zAxis.min &&\n                        zValue <= zAxis.max) :\n                    false;\n            }\n            else {\n                rawPoint.plotZ = series.zPadding;\n            }\n            rawPoint.axisXpos = rawPoint.plotX;\n            rawPoint.axisYpos = rawPoint.plotY;\n            rawPoint.axisZpos = rawPoint.plotZ;\n            rawPoints.push({\n                x: rawPoint.plotX,\n                y: rawPoint.plotY,\n                z: rawPoint.plotZ\n            });\n            rawPointsX.push(rawPoint.plotX || 0);\n        });\n        series.rawPointsX = rawPointsX;\n        const projectedPoints = Series3D_perspective(rawPoints, chart, true);\n        series.data.forEach((rawPoint, i) => {\n            projectedPoint = projectedPoints[i];\n            rawPoint.plotX = projectedPoint.x;\n            rawPoint.plotY = projectedPoint.y;\n            rawPoint.plotZ = projectedPoint.z;\n        });\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nSeries3D.defaultOptions = Series3D_merge((highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default()).defaultOptions);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Series_Series3D = (Series3D);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"StackItem\"],\"commonjs\":[\"highcharts\",\"StackItem\"],\"commonjs2\":[\"highcharts\",\"StackItem\"],\"root\":[\"Highcharts\",\"StackItem\"]}\nvar highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_ = __webpack_require__(184);\nvar highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_default = /*#__PURE__*/__webpack_require__.n(highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_);\n;// ./code/es-modules/Core/Renderer/SVG/SVGElement3D.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  Extensions to the SVGRenderer class to enable 3D shapes\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { parse: SVGElement3D_color } = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default());\n\nconst { Element: SVGElement } = highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default().getRendererType().prototype;\n\nconst { defined, pick: SVGElement3D_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass SVGElement3D extends SVGElement {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.parts = ['front', 'top', 'side'];\n        this.pathType = 'cuboid';\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * The init is used by base - renderer.Element\n     * @private\n     */\n    initArgs(args) {\n        const elem3d = this, renderer = elem3d.renderer, paths = renderer[elem3d.pathType + 'Path'](args), zIndexes = paths.zIndexes;\n        // Build parts\n        for (const part of elem3d.parts) {\n            const attribs = {\n                'class': 'highcharts-3d-' + part,\n                zIndex: zIndexes[part] || 0\n            };\n            if (renderer.styledMode) {\n                if (part === 'top') {\n                    attribs.filter = 'url(#highcharts-brighter)';\n                }\n                else if (part === 'side') {\n                    attribs.filter = 'url(#highcharts-darker)';\n                }\n            }\n            elem3d[part] = renderer.path(paths[part])\n                .attr(attribs)\n                .add(elem3d);\n        }\n        elem3d.attr({\n            'stroke-linejoin': 'round',\n            zIndex: zIndexes.group\n        });\n        // Store information if any side of element was rendered by force.\n        elem3d.forcedSides = paths.forcedSides;\n    }\n    /**\n     * Single property setter that applies options to each part\n     * @private\n     */\n    singleSetterForParts(prop, val, values, verb, duration, complete) {\n        const elem3d = this, newAttr = {}, optionsToApply = [null, null, (verb || 'attr'), duration, complete], hasZIndexes = values?.zIndexes;\n        if (!values) {\n            newAttr[prop] = val;\n            optionsToApply[0] = newAttr;\n        }\n        else {\n            // It is needed to deal with the whole group zIndexing\n            // in case of graph rotation\n            if (hasZIndexes?.group) {\n                elem3d.attr({\n                    zIndex: hasZIndexes.group\n                });\n            }\n            for (const part of Object.keys(values)) {\n                newAttr[part] = {};\n                newAttr[part][prop] = values[part];\n                // Include zIndexes if provided\n                if (hasZIndexes) {\n                    newAttr[part].zIndex = values.zIndexes[part] || 0;\n                }\n            }\n            optionsToApply[1] = newAttr;\n        }\n        return this.processParts.apply(elem3d, optionsToApply);\n    }\n    /**\n     * Calls function for each part. Used for attr, animate and destroy.\n     * @private\n     */\n    processParts(props, partsProps, verb, duration, complete) {\n        const elem3d = this;\n        for (const part of elem3d.parts) {\n            // If different props for different parts\n            if (partsProps) {\n                props = SVGElement3D_pick(partsProps[part], false);\n            }\n            // Only if something to set, but allow undefined\n            if (props !== false) {\n                elem3d[part][verb](props, duration, complete);\n            }\n        }\n        return elem3d;\n    }\n    /**\n     * Destroy all parts\n     * @private\n     */\n    destroy() {\n        this.processParts(null, null, 'destroy');\n        return super.destroy();\n    }\n    // Following functions are SVGElement3DCuboid (= base)\n    attr(args, val, complete, continueAnimation) {\n        // Resolve setting attributes by string name\n        if (typeof args === 'string' && typeof val !== 'undefined') {\n            const key = args;\n            args = {};\n            args[key] = val;\n        }\n        if (args.shapeArgs || defined(args.x)) {\n            return this.singleSetterForParts('d', null, this.renderer[this.pathType + 'Path'](args.shapeArgs || args));\n        }\n        return super.attr(args, void 0, complete, continueAnimation);\n    }\n    animate(args, duration, complete) {\n        if (defined(args.x) && defined(args.y)) {\n            const paths = this.renderer[this.pathType + 'Path'](args), forcedSides = paths.forcedSides;\n            this.singleSetterForParts('d', null, paths, 'animate', duration, complete);\n            this.attr({\n                zIndex: paths.zIndexes.group\n            });\n            // If sides that are forced to render changed, recalculate colors.\n            if (forcedSides !== this.forcedSides) {\n                this.forcedSides = forcedSides;\n                if (!this.renderer.styledMode) {\n                    this.fillSetter(this.fill);\n                }\n            }\n        }\n        else {\n            super.animate(args, duration, complete);\n        }\n        return this;\n    }\n    fillSetter(fill) {\n        const elem3d = this;\n        elem3d.forcedSides = elem3d.forcedSides || [];\n        elem3d.singleSetterForParts('fill', null, {\n            front: fill,\n            // Do not change color if side was forced to render.\n            top: SVGElement3D_color(fill).brighten(elem3d.forcedSides.indexOf('top') >= 0 ? 0 : 0.1).get(),\n            side: SVGElement3D_color(fill).brighten(elem3d.forcedSides.indexOf('side') >= 0 ? 0 : -0.1).get()\n        });\n        // Fill for animation getter (#6776)\n        elem3d.color = elem3d.fill = fill;\n        return elem3d;\n    }\n}\nSVGElement3D.types = {\n    base: SVGElement3D,\n    cuboid: SVGElement3D\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const SVG_SVGElement3D = (SVGElement3D);\n\n;// ./code/es-modules/Core/Renderer/SVG/SVGRenderer3D.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  Extensions to the SVGRenderer class to enable 3D shapes\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { animObject } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { parse: SVGRenderer3D_color } = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default());\n\nconst { charts, deg2rad: SVGRenderer3D_deg2rad } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { perspective: SVGRenderer3D_perspective, shapeArea: SVGRenderer3D_shapeArea } = Core_Math3D;\n\n\nconst { defined: SVGRenderer3D_defined, extend: SVGRenderer3D_extend, merge: SVGRenderer3D_merge, pick: SVGRenderer3D_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\nconst cos = Math.cos, sin = Math.sin, PI = Math.PI, dFactor = (4 * (Math.sqrt(2) - 1) / 3) / (PI / 2);\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Method to construct a curved path. Can 'wrap' around more then 180\n * degrees.\n * @private\n */\nfunction curveTo(cx, cy, rx, ry, start, end, dx, dy) {\n    const arcAngle = end - start;\n    let result = [];\n    if ((end > start) && (end - start > Math.PI / 2 + 0.0001)) {\n        result = result.concat(curveTo(cx, cy, rx, ry, start, start + (Math.PI / 2), dx, dy));\n        result = result.concat(curveTo(cx, cy, rx, ry, start + (Math.PI / 2), end, dx, dy));\n        return result;\n    }\n    if ((end < start) && (start - end > Math.PI / 2 + 0.0001)) {\n        result = result.concat(curveTo(cx, cy, rx, ry, start, start - (Math.PI / 2), dx, dy));\n        result = result.concat(curveTo(cx, cy, rx, ry, start - (Math.PI / 2), end, dx, dy));\n        return result;\n    }\n    return [[\n            'C',\n            cx + (rx * Math.cos(start)) -\n                ((rx * dFactor * arcAngle) * Math.sin(start)) + dx,\n            cy + (ry * Math.sin(start)) +\n                ((ry * dFactor * arcAngle) * Math.cos(start)) + dy,\n            cx + (rx * Math.cos(end)) +\n                ((rx * dFactor * arcAngle) * Math.sin(end)) + dx,\n            cy + (ry * Math.sin(end)) -\n                ((ry * dFactor * arcAngle) * Math.cos(end)) + dy,\n            cx + (rx * Math.cos(end)) + dx,\n            cy + (ry * Math.sin(end)) + dy\n        ]];\n}\n/* *\n *\n *  Composition\n *\n * */\nvar SVGRenderer3D;\n(function (SVGRenderer3D) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /** @private */\n    function compose(SVGRendererClass) {\n        const rendererProto = SVGRendererClass.prototype;\n        if (!rendererProto.element3d) {\n            SVGRenderer3D_extend(rendererProto, {\n                Element3D: SVG_SVGElement3D,\n                arc3d,\n                arc3dPath,\n                cuboid,\n                cuboidPath,\n                element3d,\n                face3d,\n                polyhedron,\n                toLinePath,\n                toLineSegments\n            });\n        }\n    }\n    SVGRenderer3D.compose = compose;\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /** @private */\n    function toLinePath(points, closed) {\n        const result = [];\n        // Put \"L x y\" for each point\n        for (const point of points) {\n            result.push(['L', point.x, point.y]);\n        }\n        if (points.length) {\n            // Set the first element to M\n            result[0][0] = 'M';\n            // If it is a closed line, add Z\n            if (closed) {\n                result.push(['Z']);\n            }\n        }\n        return result;\n    }\n    /** @private */\n    function toLineSegments(points) {\n        const result = [];\n        let m = true;\n        for (const point of points) {\n            result.push(m ? ['M', point.x, point.y] : ['L', point.x, point.y]);\n            m = !m;\n        }\n        return result;\n    }\n    /**\n     * A 3-D Face is defined by it's 3D vertexes, and is only visible if it's\n     * vertexes are counter-clockwise (Back-face culling). It is used as a\n     * polyhedron Element.\n     * @private\n     */\n    function face3d(args) {\n        const renderer = this, elementProto = renderer.Element.prototype, ret = renderer.createElement('path');\n        ret.vertexes = [];\n        ret.insidePlotArea = false;\n        ret.enabled = true;\n        /* eslint-disable no-invalid-this */\n        ret.attr = function (hash) {\n            if (typeof hash === 'object' &&\n                (SVGRenderer3D_defined(hash.enabled) ||\n                    SVGRenderer3D_defined(hash.vertexes) ||\n                    SVGRenderer3D_defined(hash.insidePlotArea))) {\n                this.enabled = SVGRenderer3D_pick(hash.enabled, this.enabled);\n                this.vertexes = SVGRenderer3D_pick(hash.vertexes, this.vertexes);\n                this.insidePlotArea = SVGRenderer3D_pick(hash.insidePlotArea, this.insidePlotArea);\n                delete hash.enabled;\n                delete hash.vertexes;\n                delete hash.insidePlotArea;\n                const chart = charts[renderer.chartIndex], vertexes2d = SVGRenderer3D_perspective(this.vertexes, chart, this.insidePlotArea), path = renderer.toLinePath(vertexes2d, true), area = SVGRenderer3D_shapeArea(vertexes2d);\n                hash.d = path;\n                hash.visibility = (this.enabled && area > 0) ?\n                    'inherit' : 'hidden';\n            }\n            return elementProto.attr.apply(this, arguments);\n        };\n        ret.animate = function (params) {\n            if (typeof params === 'object' &&\n                (SVGRenderer3D_defined(params.enabled) ||\n                    SVGRenderer3D_defined(params.vertexes) ||\n                    SVGRenderer3D_defined(params.insidePlotArea))) {\n                this.enabled = SVGRenderer3D_pick(params.enabled, this.enabled);\n                this.vertexes = SVGRenderer3D_pick(params.vertexes, this.vertexes);\n                this.insidePlotArea = SVGRenderer3D_pick(params.insidePlotArea, this.insidePlotArea);\n                delete params.enabled;\n                delete params.vertexes;\n                delete params.insidePlotArea;\n                const chart = charts[renderer.chartIndex], vertexes2d = SVGRenderer3D_perspective(this.vertexes, chart, this.insidePlotArea), path = renderer.toLinePath(vertexes2d, true), area = SVGRenderer3D_shapeArea(vertexes2d), visibility = (this.enabled && area > 0) ?\n                    'visible' : 'hidden';\n                params.d = path;\n                this.attr('visibility', visibility);\n            }\n            return elementProto.animate.apply(this, arguments);\n        };\n        /* eslint-enable no-invalid-this */\n        return ret.attr(args);\n    }\n    /**\n     * A Polyhedron is a handy way of defining a group of 3-D faces. It's only\n     * attribute is `faces`, an array of attributes of each one of it's Face3D\n     * instances.\n     * @private\n     */\n    function polyhedron(args) {\n        const renderer = this, elementProto = renderer.Element.prototype, result = renderer.g(), destroy = result.destroy;\n        if (!this.styledMode) {\n            result.attr({\n                'stroke-linejoin': 'round'\n            });\n        }\n        result.faces = [];\n        // Destroy all children\n        result.destroy = function () {\n            for (let i = 0; i < result.faces.length; i++) {\n                result.faces[i].destroy();\n            }\n            return destroy.call(this);\n        };\n        result.attr = function (hash, val, complete, continueAnimation) {\n            if (typeof hash === 'object' && SVGRenderer3D_defined(hash.faces)) {\n                while (result.faces.length > hash.faces.length) {\n                    result.faces.pop().destroy();\n                }\n                while (result.faces.length < hash.faces.length) {\n                    result.faces.push(renderer.face3d().add(result));\n                }\n                for (let i = 0; i < hash.faces.length; i++) {\n                    if (renderer.styledMode) {\n                        delete hash.faces[i].fill;\n                    }\n                    result.faces[i].attr(hash.faces[i], null, complete, continueAnimation);\n                }\n                delete hash.faces;\n            }\n            return elementProto.attr.apply(this, arguments);\n        };\n        result.animate = function (params, duration, complete) {\n            if (params?.faces) {\n                while (result.faces.length > params.faces.length) {\n                    result.faces.pop().destroy();\n                }\n                while (result.faces.length < params.faces.length) {\n                    result.faces.push(renderer.face3d().add(result));\n                }\n                for (let i = 0; i < params.faces.length; i++) {\n                    result.faces[i].animate(params.faces[i], duration, complete);\n                }\n                delete params.faces;\n            }\n            return elementProto.animate.apply(this, arguments);\n        };\n        return result.attr(args);\n    }\n    /**\n     * Return result, generalization\n     * @private\n     * @requires highcharts-3d\n     */\n    function element3d(type, shapeArgs) {\n        const elem3d = new SVG_SVGElement3D.types[type](this, 'g');\n        elem3d.initArgs(shapeArgs);\n        return elem3d;\n    }\n    /**\n     * Generalized, so now use simply\n     * @private\n     */\n    function cuboid(shapeArgs) {\n        return this.element3d('cuboid', shapeArgs);\n    }\n    /**\n     * Generates a cuboid path and zIndexes\n     * @private\n     */\n    function cuboidPath(shapeArgs) {\n        const x = shapeArgs.x || 0, y = shapeArgs.y || 0, z = shapeArgs.z || 0, \n        // For side calculation (right/left)\n        // there is a need for height (and other shapeArgs arguments)\n        // to be at least 1px\n        h = shapeArgs.height || 0, w = shapeArgs.width || 0, d = shapeArgs.depth || 0, chart = charts[this.chartIndex], options3d = chart.options.chart.options3d, alpha = options3d.alpha, \n        // Priority for x axis is the biggest,\n        // because of x direction has biggest influence on zIndex\n        incrementX = 1000000, \n        // Y axis has the smallest priority in case of our charts\n        // (needs to be set because of stacking)\n        incrementY = 10, incrementZ = 100, forcedSides = [];\n        let shape, zIndex = 0, \n        // The 8 corners of the cube\n        pArr = [{\n                x: x,\n                y: y,\n                z: z\n            }, {\n                x: x + w,\n                y: y,\n                z: z\n            }, {\n                x: x + w,\n                y: y + h,\n                z: z\n            }, {\n                x: x,\n                y: y + h,\n                z: z\n            }, {\n                x: x,\n                y: y + h,\n                z: z + d\n            }, {\n                x: x + w,\n                y: y + h,\n                z: z + d\n            }, {\n                x: x + w,\n                y: y,\n                z: z + d\n            }, {\n                x: x,\n                y: y,\n                z: z + d\n            }];\n        // Apply perspective\n        pArr = SVGRenderer3D_perspective(pArr, chart, shapeArgs.insidePlotArea);\n        /**\n         * Helper method to decide which side is visible\n         * @private\n         */\n        const mapSidePath = (i) => {\n            // Added support for 0 value in columns, where height is 0\n            // but the shape is rendered.\n            // Height is used from 1st to 6th element of pArr\n            if (h === 0 && i > 1 && i < 6) { // [2, 3, 4, 5]\n                return {\n                    x: pArr[i].x,\n                    // When height is 0 instead of cuboid we render plane\n                    // so it is needed to add fake 10 height to imitate\n                    // cuboid for side calculation\n                    y: pArr[i].y + 10,\n                    z: pArr[i].z\n                };\n            }\n            // It is needed to calculate dummy sides (front/back) for\n            // breaking points in case of x and depth values. If column has\n            // side, it means that x values of front and back side are\n            // different.\n            if (pArr[0].x === pArr[7].x && i >= 4) { // [4, 5, 6, 7]\n                return {\n                    x: pArr[i].x + 10,\n                    // When height is 0 instead of cuboid we render plane\n                    // so it is needed to add fake 10 height to imitate\n                    // cuboid for side calculation\n                    y: pArr[i].y,\n                    z: pArr[i].z\n                };\n            }\n            // Added dummy depth\n            if (d === 0 && i < 2 || i > 5) { // [0, 1, 6, 7]\n                return {\n                    x: pArr[i].x,\n                    // When height is 0 instead of cuboid we render plane\n                    // so it is needed to add fake 10 height to imitate\n                    // cuboid for side calculation\n                    y: pArr[i].y,\n                    z: pArr[i].z + 10\n                };\n            }\n            return pArr[i];\n        }, \n        /**\n         * Method creating the final side\n         * @private\n         */\n        mapPath = (i) => (pArr[i]), \n        /**\n         * First value - path with specific face\n         * Second value - added info about side for later calculations.\n         *                 Possible second values are 0 for path1, 1 for\n         *                 path2 and -1 for no path chosen.\n         * Third value - string containing information about current side of\n         *               cuboid for forcing side rendering.\n         * @private\n         */\n        pickShape = (verticesIndex1, verticesIndex2, side) => {\n            const // An array of vertices for cuboid face\n            face1 = verticesIndex1.map(mapPath), face2 = verticesIndex2.map(mapPath), \n            // Dummy face is calculated the same way as standard face,\n            // but if cuboid height is 0 additional height is added so\n            // it is possible to use this vertices array for visible\n            // face calculation\n            dummyFace1 = verticesIndex1.map(mapSidePath), dummyFace2 = verticesIndex2.map(mapSidePath);\n            let ret = [[], -1];\n            if (SVGRenderer3D_shapeArea(face1) < 0) {\n                ret = [face1, 0];\n            }\n            else if (SVGRenderer3D_shapeArea(face2) < 0) {\n                ret = [face2, 1];\n            }\n            else if (side) {\n                forcedSides.push(side);\n                if (SVGRenderer3D_shapeArea(dummyFace1) < 0) {\n                    ret = [face1, 0];\n                }\n                else if (SVGRenderer3D_shapeArea(dummyFace2) < 0) {\n                    ret = [face2, 1];\n                }\n                else {\n                    ret = [face1, 0]; // Force side calculation.\n                }\n            }\n            return ret;\n        };\n        // Front or back\n        const front = [3, 2, 1, 0], back = [7, 6, 5, 4];\n        shape = pickShape(front, back, 'front');\n        const path1 = shape[0], isFront = shape[1];\n        // Top or bottom\n        const top = [1, 6, 7, 0], bottom = [4, 5, 2, 3];\n        shape = pickShape(top, bottom, 'top');\n        const path2 = shape[0], isTop = shape[1];\n        // Side\n        const right = [1, 2, 5, 6], left = [0, 7, 4, 3];\n        shape = pickShape(right, left, 'side');\n        const path3 = shape[0], isRight = shape[1];\n        /* New block used for calculating zIndex. It is basing on X, Y and Z\n        position of specific columns. All zIndexes (for X, Y and Z values) are\n        added to the final zIndex, where every value has different priority. The\n        biggest priority is in X and Z directions, the lowest index is for\n        stacked columns (Y direction and the same X and Z positions). Big\n        differences between priorities is made because we need to ensure that\n        even for big changes in Y and Z parameters all columns will be drawn\n        correctly. */\n        if (isRight === 1) {\n            // It is needed to connect value with current chart width\n            // for big chart size.\n            zIndex += incrementX * (chart.plotWidth - x);\n        }\n        else if (!isRight) {\n            zIndex += incrementX * x;\n        }\n        zIndex += incrementY * (!isTop ||\n            // Numbers checked empirically\n            (alpha >= 0 && alpha <= 180 || alpha < 360 && alpha > 357.5) ?\n            chart.plotHeight - y : 10 + y);\n        if (isFront === 1) {\n            zIndex += incrementZ * (z);\n        }\n        else if (!isFront) {\n            zIndex += incrementZ * (1000 - z);\n        }\n        return {\n            front: this.toLinePath(path1, true),\n            top: this.toLinePath(path2, true),\n            side: this.toLinePath(path3, true),\n            zIndexes: {\n                group: Math.round(zIndex)\n            },\n            forcedSides: forcedSides,\n            // Additional info about zIndexes\n            isFront: isFront,\n            isTop: isTop\n        }; // #4774\n    }\n    /** @private */\n    function arc3d(attribs) {\n        const renderer = this, wrapper = renderer.g(), elementProto = renderer.Element.prototype, customAttribs = [\n            'alpha', 'beta',\n            'x', 'y', 'r', 'innerR', 'start', 'end', 'depth'\n        ];\n        /**\n         * Get custom attributes. Don't mutate the original object and return an\n         * object with only custom attr.\n         * @private\n         */\n        function extractCustom(params) {\n            const ca = {};\n            params = SVGRenderer3D_merge(params); // Don't mutate the original object\n            let key;\n            for (key in params) {\n                if (customAttribs.indexOf(key) !== -1) {\n                    ca[key] = params[key];\n                    delete params[key];\n                }\n            }\n            return Object.keys(ca).length ? [ca, params] : false;\n        }\n        attribs = SVGRenderer3D_merge(attribs);\n        attribs.alpha = (attribs.alpha || 0) * SVGRenderer3D_deg2rad;\n        attribs.beta = (attribs.beta || 0) * SVGRenderer3D_deg2rad;\n        // Create the different sub sections of the shape\n        wrapper.top = renderer.path();\n        wrapper.side1 = renderer.path();\n        wrapper.side2 = renderer.path();\n        wrapper.inn = renderer.path();\n        wrapper.out = renderer.path();\n        /* eslint-disable no-invalid-this */\n        // Add all faces\n        wrapper.onAdd = function () {\n            const parent = wrapper.parentGroup, className = wrapper.attr('class');\n            wrapper.top.add(wrapper);\n            // These faces are added outside the wrapper group because the\n            // z-index relates to neighbour elements as well\n            for (const face of ['out', 'inn', 'side1', 'side2']) {\n                wrapper[face]\n                    .attr({\n                    'class': className + ' highcharts-3d-side'\n                })\n                    .add(parent);\n            }\n        };\n        // Cascade to faces\n        for (const fn of ['addClass', 'removeClass']) {\n            wrapper[fn] = function () {\n                const args = arguments;\n                for (const face of ['top', 'out', 'inn', 'side1', 'side2']) {\n                    wrapper[face][fn].apply(wrapper[face], args);\n                }\n            };\n        }\n        /**\n         * Compute the transformed paths and set them to the composite shapes\n         * @private\n         */\n        wrapper.setPaths = function (attribs) {\n            const paths = wrapper.renderer.arc3dPath(attribs), zIndex = paths.zTop * 100;\n            wrapper.attribs = attribs;\n            wrapper.top.attr({ d: paths.top, zIndex: paths.zTop });\n            wrapper.inn.attr({ d: paths.inn, zIndex: paths.zInn });\n            wrapper.out.attr({ d: paths.out, zIndex: paths.zOut });\n            wrapper.side1.attr({ d: paths.side1, zIndex: paths.zSide1 });\n            wrapper.side2.attr({ d: paths.side2, zIndex: paths.zSide2 });\n            // Show all children\n            wrapper.zIndex = zIndex;\n            wrapper.attr({ zIndex: zIndex });\n            // Set the radial gradient center the first time\n            if (attribs.center) {\n                wrapper.top.setRadialReference(attribs.center);\n                delete attribs.center;\n            }\n        };\n        wrapper.setPaths(attribs);\n        /**\n         * Apply the fill to the top and a darker shade to the sides\n         * @private\n         */\n        wrapper.fillSetter = function (value) {\n            const darker = SVGRenderer3D_color(value).brighten(-0.1).get();\n            this.fill = value;\n            this.side1.attr({ fill: darker });\n            this.side2.attr({ fill: darker });\n            this.inn.attr({ fill: darker });\n            this.out.attr({ fill: darker });\n            this.top.attr({ fill: value });\n            return this;\n        };\n        // Apply the same value to all. These properties cascade down to the\n        // children when set to the composite arc3d.\n        for (const setter of ['opacity', 'translateX', 'translateY', 'visibility']) {\n            wrapper[setter + 'Setter'] = function (value, key) {\n                wrapper[key] = value;\n                for (const el of ['out', 'inn', 'side1', 'side2', 'top']) {\n                    wrapper[el].attr(key, value);\n                }\n            };\n        }\n        // Override attr to remove shape attributes and use those to set child\n        // paths\n        wrapper.attr = function (params) {\n            if (typeof params === 'object') {\n                const paramArr = extractCustom(params);\n                if (paramArr) {\n                    const ca = paramArr[0];\n                    arguments[0] = paramArr[1];\n                    // Translate alpha and beta to rotation\n                    if (ca.alpha !== void 0) {\n                        ca.alpha *= SVGRenderer3D_deg2rad;\n                    }\n                    if (ca.beta !== void 0) {\n                        ca.beta *= SVGRenderer3D_deg2rad;\n                    }\n                    SVGRenderer3D_extend(wrapper.attribs, ca);\n                    if (wrapper.attribs) {\n                        wrapper.setPaths(wrapper.attribs);\n                    }\n                }\n            }\n            return elementProto.attr.apply(wrapper, arguments);\n        };\n        // Override the animate function by sucking out custom parameters\n        // related to the shapes directly, and update the shapes from the\n        // animation step.\n        wrapper.animate = function (params, animation, complete) {\n            const from = this.attribs, randomProp = 'data-' +\n                Math.random().toString(26).substring(2, 9);\n            // Attribute-line properties connected to 3D. These shouldn't have\n            // been in the attribs collection in the first place.\n            delete params.center;\n            delete params.z;\n            const anim = animObject(SVGRenderer3D_pick(animation, this.renderer.globalAnimation));\n            if (anim.duration) {\n                const paramArr = extractCustom(params);\n                // Params need to have a property in order for the step to run\n                // (#5765, #7097, #7437)\n                wrapper[randomProp] = 0;\n                params[randomProp] = 1;\n                wrapper[randomProp + 'Setter'] = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).noop;\n                if (paramArr) {\n                    const to = paramArr[0], // Custom attr\n                    interpolate = (key, pos) => (from[key] + (SVGRenderer3D_pick(to[key], from[key]) -\n                        from[key]) * pos);\n                    anim.step = function (a, fx) {\n                        if (fx.prop === randomProp) {\n                            fx.elem.setPaths(SVGRenderer3D_merge(from, {\n                                x: interpolate('x', fx.pos),\n                                y: interpolate('y', fx.pos),\n                                r: interpolate('r', fx.pos),\n                                innerR: interpolate('innerR', fx.pos),\n                                start: interpolate('start', fx.pos),\n                                end: interpolate('end', fx.pos),\n                                depth: interpolate('depth', fx.pos)\n                            }));\n                        }\n                    };\n                }\n                animation = anim; // Only when duration (#5572)\n            }\n            return elementProto.animate.call(this, params, animation, complete);\n        };\n        // Destroy all children\n        wrapper.destroy = function () {\n            this.top.destroy();\n            this.out.destroy();\n            this.inn.destroy();\n            this.side1.destroy();\n            this.side2.destroy();\n            return elementProto.destroy.call(this);\n        };\n        // Hide all children\n        wrapper.hide = function () {\n            this.top.hide();\n            this.out.hide();\n            this.inn.hide();\n            this.side1.hide();\n            this.side2.hide();\n        };\n        wrapper.show = function (inherit) {\n            this.top.show(inherit);\n            this.out.show(inherit);\n            this.inn.show(inherit);\n            this.side1.show(inherit);\n            this.side2.show(inherit);\n        };\n        /* eslint-enable no-invalid-this */\n        return wrapper;\n    }\n    /**\n     * Generate the paths required to draw a 3D arc.\n     * @private\n     */\n    function arc3dPath(shapeArgs) {\n        const cx = shapeArgs.x || 0, // X coordinate of the center\n        cy = shapeArgs.y || 0, // Y coordinate of the center\n        start = shapeArgs.start || 0, // Start angle\n        end = (shapeArgs.end || 0) - 0.00001, // End angle\n        r = shapeArgs.r || 0, // Radius\n        ir = shapeArgs.innerR || 0, // Inner radius\n        d = shapeArgs.depth || 0, // Depth\n        alpha = shapeArgs.alpha || 0, // Alpha rotation of the chart\n        beta = shapeArgs.beta || 0; // Beta rotation of the chart\n        // Derived Variables\n        const cs = Math.cos(start), // Cosinus of the start angle\n        ss = Math.sin(start), // Sinus of the start angle\n        ce = Math.cos(end), // Cosinus of the end angle\n        se = Math.sin(end), // Sinus of the end angle\n        rx = r * Math.cos(beta), // X-radius\n        ry = r * Math.cos(alpha), // Y-radius\n        irx = ir * Math.cos(beta), // X-radius (inner)\n        iry = ir * Math.cos(alpha), // Y-radius (inner)\n        dx = d * Math.sin(beta), // Distance between top and bottom in x\n        dy = d * Math.sin(alpha); // Distance between top and bottom in y\n        // TOP\n        let top = [\n            ['M', cx + (rx * cs), cy + (ry * ss)]\n        ];\n        top = top.concat(curveTo(cx, cy, rx, ry, start, end, 0, 0));\n        top.push([\n            'L', cx + (irx * ce), cy + (iry * se)\n        ]);\n        top = top.concat(curveTo(cx, cy, irx, iry, end, start, 0, 0));\n        top.push(['Z']);\n        // OUTSIDE\n        const b = (beta > 0 ? Math.PI / 2 : 0), a = (alpha > 0 ? 0 : Math.PI / 2);\n        const start2 = start > -b ? start : (end > -b ? -b : start), end2 = end < PI - a ? end : (start < PI - a ? PI - a : end), midEnd = 2 * PI - a;\n        // When slice goes over bottom middle, need to add both, left and right\n        // outer side. Additionally, when we cross right hand edge, create sharp\n        // edge. Outer shape/wall:\n        //\n        //            -------\n        //          /    ^    \\\n        //    4)   /   /   \\   \\  1)\n        //        /   /     \\   \\\n        //       /   /       \\   \\\n        // (c)=> ====         ==== <=(d)\n        //       \\   \\       /   /\n        //        \\   \\<=(a)/   /\n        //         \\   \\   /   / <=(b)\n        //    3)    \\    v    /  2)\n        //            -------\n        //\n        // (a) - inner side\n        // (b) - outer side\n        // (c) - left edge (sharp)\n        // (d) - right edge (sharp)\n        // 1..n - rendering order for startAngle = 0, when set to e.g 90, order\n        // changes clockwise (1->2, 2->3, n->1) and counterclockwise for\n        // negative startAngle\n        let out = [\n            ['M', cx + (rx * cos(start2)), cy + (ry * sin(start2))]\n        ];\n        out = out.concat(curveTo(cx, cy, rx, ry, start2, end2, 0, 0));\n        // When shape is wide, it can cross both, (c) and (d) edges, when using\n        // startAngle\n        if (end > midEnd && start < midEnd) {\n            // Go to outer side\n            out.push([\n                'L', cx + (rx * cos(end2)) + dx, cy + (ry * sin(end2)) + dy\n            ]);\n            // Curve to the right edge of the slice (d)\n            out = out.concat(curveTo(cx, cy, rx, ry, end2, midEnd, dx, dy));\n            // Go to the inner side\n            out.push([\n                'L', cx + (rx * cos(midEnd)), cy + (ry * sin(midEnd))\n            ]);\n            // Curve to the true end of the slice\n            out = out.concat(curveTo(cx, cy, rx, ry, midEnd, end, 0, 0));\n            // Go to the outer side\n            out.push([\n                'L', cx + (rx * cos(end)) + dx, cy + (ry * sin(end)) + dy\n            ]);\n            // Go back to middle (d)\n            out = out.concat(curveTo(cx, cy, rx, ry, end, midEnd, dx, dy));\n            out.push([\n                'L', cx + (rx * cos(midEnd)), cy + (ry * sin(midEnd))\n            ]);\n            // Go back to the left edge\n            out = out.concat(curveTo(cx, cy, rx, ry, midEnd, end2, 0, 0));\n            // But shape can cross also only (c) edge:\n        }\n        else if (end > PI - a && start < PI - a) {\n            // Go to outer side\n            out.push([\n                'L',\n                cx + (rx * Math.cos(end2)) + dx,\n                cy + (ry * Math.sin(end2)) + dy\n            ]);\n            // Curve to the true end of the slice\n            out = out.concat(curveTo(cx, cy, rx, ry, end2, end, dx, dy));\n            // Go to the inner side\n            out.push([\n                'L', cx + (rx * Math.cos(end)), cy + (ry * Math.sin(end))\n            ]);\n            // Go back to the artificial end2\n            out = out.concat(curveTo(cx, cy, rx, ry, end, end2, 0, 0));\n        }\n        out.push([\n            'L',\n            cx + (rx * Math.cos(end2)) + dx,\n            cy + (ry * Math.sin(end2)) + dy\n        ]);\n        out = out.concat(curveTo(cx, cy, rx, ry, end2, start2, dx, dy));\n        out.push(['Z']);\n        // INSIDE\n        let inn = [\n            ['M', cx + (irx * cs), cy + (iry * ss)]\n        ];\n        inn = inn.concat(curveTo(cx, cy, irx, iry, start, end, 0, 0));\n        inn.push([\n            'L',\n            cx + (irx * Math.cos(end)) + dx,\n            cy + (iry * Math.sin(end)) + dy\n        ]);\n        inn = inn.concat(curveTo(cx, cy, irx, iry, end, start, dx, dy));\n        inn.push(['Z']);\n        // SIDES\n        const side1 = [\n            ['M', cx + (rx * cs), cy + (ry * ss)],\n            ['L', cx + (rx * cs) + dx, cy + (ry * ss) + dy],\n            ['L', cx + (irx * cs) + dx, cy + (iry * ss) + dy],\n            ['L', cx + (irx * cs), cy + (iry * ss)],\n            ['Z']\n        ];\n        const side2 = [\n            ['M', cx + (rx * ce), cy + (ry * se)],\n            ['L', cx + (rx * ce) + dx, cy + (ry * se) + dy],\n            ['L', cx + (irx * ce) + dx, cy + (iry * se) + dy],\n            ['L', cx + (irx * ce), cy + (iry * se)],\n            ['Z']\n        ];\n        // Correction for changed position of vanishing point caused by alpha\n        // and beta rotations\n        const angleCorr = Math.atan2(dy, -dx);\n        let angleEnd = Math.abs(end + angleCorr), angleStart = Math.abs(start + angleCorr), angleMid = Math.abs((start + end) / 2 + angleCorr);\n        /**\n         * Set to 0-PI range\n         * @private\n         */\n        function toZeroPIRange(angle) {\n            angle = angle % (2 * Math.PI);\n            if (angle > Math.PI) {\n                angle = 2 * Math.PI - angle;\n            }\n            return angle;\n        }\n        angleEnd = toZeroPIRange(angleEnd);\n        angleStart = toZeroPIRange(angleStart);\n        angleMid = toZeroPIRange(angleMid);\n        // *1e5 is to compensate pInt in zIndexSetter\n        const incPrecision = 1e5, a1 = angleMid * incPrecision, a2 = angleStart * incPrecision, a3 = angleEnd * incPrecision;\n        return {\n            top: top,\n            // Max angle is PI, so this is always higher\n            zTop: Math.PI * incPrecision + 1,\n            out: out,\n            zOut: Math.max(a1, a2, a3),\n            inn: inn,\n            zInn: Math.max(a1, a2, a3),\n            side1: side1,\n            // To keep below zOut and zInn in case of same values\n            zSide1: a3 * 0.99,\n            side2: side2,\n            zSide2: a2 * 0.99\n        };\n    }\n})(SVGRenderer3D || (SVGRenderer3D = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const SVG_SVGRenderer3D = (SVGRenderer3D);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Axis\"],\"commonjs\":[\"highcharts\",\"Axis\"],\"commonjs2\":[\"highcharts\",\"Axis\"],\"root\":[\"Highcharts\",\"Axis\"]}\nvar highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_ = __webpack_require__(532);\nvar highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default = /*#__PURE__*/__webpack_require__.n(highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_);\n;// ./code/es-modules/Core/Axis/ZAxis.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { defaultOptions: ZAxis_defaultOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent: ZAxis_addEvent, merge: ZAxis_merge, pick: ZAxis_pick, splat } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction chartAddZAxis(options) {\n    return new ZAxis(this, options);\n}\n/**\n * Get the Z axis in addition to the default X and Y.\n * @private\n */\nfunction onChartAfterCreateAxes() {\n    const zAxisOptions = this.options.zAxis = splat(this.options.zAxis || {});\n    if (!this.is3d()) {\n        return;\n    }\n    this.zAxis = [];\n    zAxisOptions.forEach((axisOptions) => {\n        this.addZAxis(axisOptions).setScale();\n    });\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * 3D axis for z coordinates.\n * @private\n */\nclass ZAxis extends (highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default()) {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        this.isZAxis = true;\n    }\n    static compose(ChartClass) {\n        const chartProto = ChartClass.prototype;\n        if (!chartProto.addZAxis) {\n            ZAxis_defaultOptions.zAxis = ZAxis_merge(ZAxis_defaultOptions.xAxis, {\n                offset: 0,\n                lineWidth: 0\n            });\n            chartProto.addZAxis = chartAddZAxis;\n            chartProto.collectionsWithInit.zAxis = [chartProto.addZAxis];\n            chartProto.collectionsWithUpdate.push('zAxis');\n            ZAxis_addEvent(ChartClass, 'afterCreateAxes', onChartAfterCreateAxes);\n        }\n    }\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    init(chart, userOptions) {\n        // #14793, this used to be set on the prototype\n        this.isZAxis = true;\n        super.init(chart, userOptions, 'zAxis');\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getSeriesExtremes() {\n        this.hasVisibleSeries = false;\n        // Reset properties in case we're redrawing (#3353)\n        this.dataMin = this.dataMax = this.ignoreMinPadding = (this.ignoreMaxPadding = void 0);\n        if (this.stacking) {\n            this.stacking.buildStacks();\n        }\n        // Loop through this axis' series\n        this.series.forEach((series) => {\n            if (series.reserveSpace()) {\n                let threshold = series.options.threshold;\n                this.hasVisibleSeries = true;\n                // Validate threshold in logarithmic axes\n                if (this.positiveValuesOnly && threshold <= 0) {\n                    threshold = void 0;\n                }\n                const zData = series.getColumn('z');\n                if (zData.length) {\n                    this.dataMin = Math.min(ZAxis_pick(this.dataMin, zData[0]), Math.min.apply(null, zData));\n                    this.dataMax = Math.max(ZAxis_pick(this.dataMax, zData[0]), Math.max.apply(null, zData));\n                }\n            }\n        });\n    }\n    /**\n     * @private\n     */\n    setAxisSize() {\n        const chart = this.chart;\n        super.setAxisSize();\n        this.width = this.len = chart.options.chart.options3d?.depth || 0;\n        this.right = chart.chartWidth - this.width - this.left;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Axis_ZAxis = (ZAxis);\n\n;// ./code/es-modules/Series/Column3D/Column3DComposition.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { composed: Column3DComposition_composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { perspective: Column3DComposition_perspective } = Core_Math3D;\n\nconst { addEvent: Column3DComposition_addEvent, extend: Column3DComposition_extend, pick: Column3DComposition_pick, pushUnique: Column3DComposition_pushUnique, wrap: Column3DComposition_wrap } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/** @private */\nfunction columnSeriesTranslate3dShapes() {\n    const series = this, chart = series.chart, seriesOptions = series.options, depth = seriesOptions.depth, stack = seriesOptions.stacking ?\n        (seriesOptions.stack || 0) :\n        series.index; // #4743\n    let z = stack * (depth + (seriesOptions.groupZPadding || 1)), borderCrisp = series.borderWidth % 2 ? 0.5 : 0, point2dPos; // Position of point in 2D, used for 3D position calculation\n    if (chart.inverted && !series.yAxis.reversed) {\n        borderCrisp *= -1;\n    }\n    if (seriesOptions.grouping !== false) {\n        z = 0;\n    }\n    z += (seriesOptions.groupZPadding || 1);\n    for (const point of series.points) {\n        // #7103 Reset outside3dPlot flag\n        point.outside3dPlot = null;\n        if (point.y !== null) {\n            const shapeArgs = Column3DComposition_extend({ x: 0, y: 0, width: 0, height: 0 }, point.shapeArgs || {}), \n            // Array for final shapeArgs calculation.\n            // We are checking two dimensions (x and y).\n            dimensions = [['x', 'width'], ['y', 'height']], tooltipPos = point.tooltipPos;\n            let borderlessBase; // Crisped rects can have +/- 0.5 pixels offset.\n            // #3131 We need to check if column is inside plotArea.\n            for (const d of dimensions) {\n                borderlessBase = shapeArgs[d[0]] - borderCrisp;\n                if (borderlessBase < 0) {\n                    // If borderLessBase is smaller than 0, it is needed to set\n                    // its value to 0 or 0.5 depending on borderWidth\n                    // borderWidth may be even or odd.\n                    shapeArgs[d[1]] += shapeArgs[d[0]] + borderCrisp;\n                    shapeArgs[d[0]] = -borderCrisp;\n                    borderlessBase = 0;\n                }\n                if ((borderlessBase + shapeArgs[d[1]] >\n                    series[d[0] + 'Axis'].len) &&\n                    // Do not change height/width of column if 0 (#6708)\n                    shapeArgs[d[1]] !== 0) {\n                    shapeArgs[d[1]] =\n                        series[d[0] + 'Axis'].len -\n                            shapeArgs[d[0]];\n                }\n                if (\n                // Do not remove columns with zero height/width.\n                shapeArgs[d[1]] !== 0 &&\n                    (shapeArgs[d[0]] >= series[d[0] + 'Axis'].len ||\n                        shapeArgs[d[0]] + shapeArgs[d[1]] <= borderCrisp)) {\n                    // Set args to 0 if column is outside the chart.\n                    for (const key in shapeArgs) { // eslint-disable-line guard-for-in\n                        // #13840\n                        shapeArgs[key] = key === 'y' ? -9999 : 0;\n                    }\n                    // #7103 outside3dPlot flag is set on Points which are\n                    // currently outside of plot.\n                    point.outside3dPlot = true;\n                }\n            }\n            // Change from 2d to 3d\n            if (point.shapeType === 'roundedRect') {\n                point.shapeType = 'cuboid';\n            }\n            point.shapeArgs = Column3DComposition_extend(shapeArgs, {\n                z,\n                depth,\n                insidePlotArea: true\n            });\n            // Point's position in 2D\n            point2dPos = {\n                x: shapeArgs.x + shapeArgs.width / 2,\n                y: shapeArgs.y,\n                z: z + depth / 2 // The center of column in Z dimension\n            };\n            // Recalculate point positions for inverted graphs\n            if (chart.inverted) {\n                point2dPos.x = shapeArgs.height;\n                point2dPos.y = point.clientX || 0;\n            }\n            // Crosshair positions\n            point.axisXpos = point2dPos.x;\n            point.axisYpos = point2dPos.y;\n            point.axisZpos = point2dPos.z;\n            // Calculate and store point's position in 3D,\n            // using perspective method.\n            point.plot3d = Column3DComposition_perspective([point2dPos], chart, true, false)[0];\n            // Translate the tooltip position in 3d space\n            if (tooltipPos) {\n                const translatedTTPos = Column3DComposition_perspective([{\n                        x: tooltipPos[0],\n                        y: tooltipPos[1],\n                        z: z + depth / 2 // The center of column in Z dimension\n                    }], chart, true, false)[0];\n                point.tooltipPos = [translatedTTPos.x, translatedTTPos.y];\n            }\n        }\n    }\n    // Store for later use #4067\n    series.z = z;\n}\n/** @private */\nfunction Column3DComposition_compose(SeriesClass, StackItemClass) {\n    if (Column3DComposition_pushUnique(Column3DComposition_composed, 'Column3D')) {\n        const seriesProto = SeriesClass.prototype, stackItemProto = StackItemClass.prototype, { column: ColumnSeriesClass, columnRange: ColumnRangeSeriesClass } = SeriesClass.types;\n        Column3DComposition_wrap(seriesProto, 'alignDataLabel', wrapSeriesAlignDataLabel);\n        Column3DComposition_wrap(seriesProto, 'justifyDataLabel', wrapSeriesJustifyDataLabel);\n        Column3DComposition_wrap(stackItemProto, 'getStackBox', wrapStackItemGetStackBox);\n        if (ColumnSeriesClass) {\n            const columnSeriesProto = ColumnSeriesClass.prototype, columnPointProto = columnSeriesProto.pointClass.prototype;\n            columnSeriesProto.translate3dPoints = () => void 0;\n            columnSeriesProto.translate3dShapes = columnSeriesTranslate3dShapes;\n            Column3DComposition_addEvent(columnSeriesProto, 'afterInit', onColumnSeriesAfterInit);\n            Column3DComposition_wrap(columnPointProto, 'hasNewShapeType', wrapColumnPointHasNewShapeType);\n            Column3DComposition_wrap(columnSeriesProto, 'animate', wrapColumnSeriesAnimate);\n            Column3DComposition_wrap(columnSeriesProto, 'plotGroup', wrapColumnSeriesPlotGroup);\n            Column3DComposition_wrap(columnSeriesProto, 'pointAttribs', wrapColumnSeriesPointAttribs);\n            Column3DComposition_wrap(columnSeriesProto, 'setState', wrapColumnSeriesSetState);\n            Column3DComposition_wrap(columnSeriesProto, 'setVisible', wrapColumnSeriesSetVisible);\n            Column3DComposition_wrap(columnSeriesProto, 'translate', wrapColumnSeriesTranslate);\n        }\n        if (ColumnRangeSeriesClass) {\n            const columnRangeSeriesProto = ColumnRangeSeriesClass.prototype, columnRangePointProto = columnRangeSeriesProto.pointClass.prototype;\n            Column3DComposition_wrap(columnRangePointProto, 'hasNewShapeType', wrapColumnPointHasNewShapeType);\n            Column3DComposition_wrap(columnRangeSeriesProto, 'plotGroup', wrapColumnSeriesPlotGroup);\n            Column3DComposition_wrap(columnRangeSeriesProto, 'pointAttribs', wrapColumnSeriesPointAttribs);\n            Column3DComposition_wrap(columnRangeSeriesProto, 'setState', wrapColumnSeriesSetState);\n            Column3DComposition_wrap(columnRangeSeriesProto, 'setVisible', wrapColumnSeriesSetVisible);\n        }\n    }\n}\n/**\n * @private\n * @param {Highcharts.Chart} chart\n * Chart with stacks\n * @param {string} stacking\n * Stacking option\n */\nfunction retrieveStacks(chart, stacking) {\n    const series = chart.series, stacks = { totalStacks: 0 };\n    let stackNumber, i = 1;\n    series.forEach(function (s) {\n        stackNumber = Column3DComposition_pick(s.options.stack, (stacking ? 0 : series.length - 1 - s.index)); // #3841, #4532\n        if (!stacks[stackNumber]) {\n            stacks[stackNumber] = { series: [s], position: i };\n            i++;\n        }\n        else {\n            stacks[stackNumber].series.push(s);\n        }\n    });\n    stacks.totalStacks = i + 1;\n    return stacks;\n}\n/** @private */\nfunction onColumnSeriesAfterInit() {\n    if (this.chart.is3d()) {\n        const series = this, seriesOptions = series.options, grouping = seriesOptions.grouping, stacking = seriesOptions.stacking, reversedStacks = series.yAxis.options.reversedStacks;\n        let z = 0;\n        // @todo grouping === true ?\n        if (!(typeof grouping !== 'undefined' && !grouping)) {\n            const stacks = retrieveStacks(this.chart, stacking), stack = seriesOptions.stack || 0;\n            let i; // Position within the stack\n            for (i = 0; i < stacks[stack].series.length; i++) {\n                if (stacks[stack].series[i] === this) {\n                    break;\n                }\n            }\n            z = (10 * (stacks.totalStacks - stacks[stack].position)) +\n                (reversedStacks ? i : -i); // #4369\n            // In case when axis is reversed, columns are also reversed inside\n            // the group (#3737)\n            if (!this.xAxis.reversed) {\n                z = (stacks.totalStacks * 10) - z;\n            }\n        }\n        seriesOptions.depth = seriesOptions.depth || 25;\n        series.z = series.z || 0;\n        seriesOptions.zIndex = z;\n    }\n}\n/**\n * In 3D mode, simple checking for a new shape to animate is not enough.\n * Additionally check if graphic is a group of elements\n * @private\n */\nfunction wrapColumnPointHasNewShapeType(proceed, ...args) {\n    return this.series.chart.is3d() ?\n        this.graphic && this.graphic.element.nodeName !== 'g' :\n        proceed.apply(this, args);\n}\n/** @private */\nfunction wrapColumnSeriesAnimate(proceed) {\n    if (!this.chart.is3d()) {\n        proceed.apply(this, [].slice.call(arguments, 1));\n    }\n    else {\n        const args = arguments, init = args[1], yAxis = this.yAxis, series = this, reversed = this.yAxis.reversed;\n        if (init) {\n            for (const point of series.points) {\n                if (point.y !== null) {\n                    point.height = point.shapeArgs.height;\n                    point.shapey = point.shapeArgs.y; // #2968\n                    point.shapeArgs.height = 1;\n                    if (!reversed) {\n                        if (point.stackY) {\n                            point.shapeArgs.y =\n                                point.plotY +\n                                    yAxis.translate(point.stackY);\n                        }\n                        else {\n                            point.shapeArgs.y =\n                                point.plotY +\n                                    (point.negative ?\n                                        -point.height :\n                                        point.height);\n                        }\n                    }\n                }\n            }\n        }\n        else { // Run the animation\n            for (const point of series.points) {\n                if (point.y !== null) {\n                    point.shapeArgs.height = point.height;\n                    point.shapeArgs.y = point.shapey; // #2968\n                    // null value do not have a graphic\n                    if (point.graphic) {\n                        point.graphic[point.outside3dPlot ?\n                            'attr' :\n                            'animate'](point.shapeArgs, series.options.animation);\n                    }\n                }\n            }\n            // Redraw datalabels to the correct position\n            this.drawDataLabels();\n        }\n    }\n}\n/**\n * In case of 3d columns there is no sense to add these columns to a specific\n * series group. If a series is added to a group all columns will have the same\n * zIndex in comparison to another series.\n * @private\n */\nfunction wrapColumnSeriesPlotGroup(proceed, prop, _name, _visibility, _zIndex, parent) {\n    if (prop !== 'dataLabelsGroup' && prop !== 'markerGroup') {\n        if (this.chart.is3d()) {\n            if (this[prop]) {\n                delete this[prop];\n            }\n            if (parent) {\n                if (!this.chart.columnGroup) {\n                    this.chart.columnGroup =\n                        this.chart.renderer.g('columnGroup').add(parent);\n                }\n                this[prop] = this.chart.columnGroup;\n                this.chart.columnGroup.attr(this.getPlotBox());\n                this[prop].survive = true;\n                if (prop === 'group') {\n                    arguments[3] = 'visible';\n                    // For 3D column group and markerGroup should be visible\n                }\n            }\n        }\n    }\n    return proceed.apply(this, Array.prototype.slice.call(arguments, 1));\n}\n/** @private */\nfunction wrapColumnSeriesPointAttribs(proceed) {\n    const attr = proceed.apply(this, [].slice.call(arguments, 1));\n    if (this.chart.is3d && this.chart.is3d()) {\n        // Set the fill color to the fill color to provide a smooth edge\n        attr.stroke = this.options.edgeColor || attr.fill;\n        attr['stroke-width'] = Column3DComposition_pick(this.options.edgeWidth, 1); // #4055\n    }\n    return attr;\n}\n/**\n * In 3D mode, all column-series are rendered in one main group. Because of that\n * we need to apply inactive state on all points.\n * @private\n */\nfunction wrapColumnSeriesSetState(proceed, state, inherit) {\n    const is3d = this.chart.is3d && this.chart.is3d();\n    if (is3d) {\n        this.options.inactiveOtherPoints = true;\n    }\n    proceed.call(this, state, inherit);\n    if (is3d) {\n        this.options.inactiveOtherPoints = false;\n    }\n}\n/**\n * When series is not added to group it is needed to change setVisible method to\n * allow correct Legend funcionality. This wrap is basing on pie chart series.\n * @private\n */\nfunction wrapColumnSeriesSetVisible(proceed, vis) {\n    const series = this;\n    if (series.chart.is3d()) {\n        for (const point of series.points) {\n            point.visible = point.options.visible = vis =\n                typeof vis === 'undefined' ?\n                    !Column3DComposition_pick(series.visible, point.visible) : vis;\n            series.options.data[series.data.indexOf(point)] =\n                point.options;\n            if (point.graphic) {\n                point.graphic.attr({\n                    visibility: vis ? 'visible' : 'hidden'\n                });\n            }\n        }\n    }\n    proceed.apply(this, Array.prototype.slice.call(arguments, 1));\n}\n/** @private */\nfunction wrapColumnSeriesTranslate(proceed) {\n    proceed.apply(this, [].slice.call(arguments, 1));\n    // Do not do this if the chart is not 3D\n    if (this.chart.is3d()) {\n        this.translate3dShapes();\n    }\n}\n/** @private */\nfunction wrapSeriesAlignDataLabel(proceed, point, _dataLabel, options, alignTo) {\n    const chart = this.chart;\n    // In 3D we need to pass point.outsidePlot option to the justifyDataLabel\n    // method for disabling justifying dataLabels in columns outside plot\n    options.outside3dPlot = point.outside3dPlot;\n    // Only do this for 3D columns and it's derived series\n    if (chart.is3d() &&\n        this.is('column')) {\n        const series = this, seriesOptions = series.options, inside = Column3DComposition_pick(options.inside, !!series.options.stacking), options3d = chart.options.chart.options3d, xOffset = point.pointWidth / 2 || 0;\n        let dLPosition = {\n            x: alignTo.x + xOffset,\n            y: alignTo.y,\n            z: series.z + seriesOptions.depth / 2\n        };\n        if (chart.inverted) {\n            // Inside dataLabels are positioned according to above\n            // logic and there is no need to position them using\n            // non-3D algorighm (that use alignTo.width)\n            if (inside) {\n                alignTo.width = 0;\n                dLPosition.x += point.shapeArgs.height / 2;\n            }\n            // When chart is upside down\n            // (alpha angle between 180 and 360 degrees)\n            // it is needed to add column width to calculated value.\n            if (options3d.alpha >= 90 && options3d.alpha <= 270) {\n                dLPosition.y += point.shapeArgs.width;\n            }\n        }\n        // `dLPosition` is recalculated for 3D graphs\n        dLPosition = Column3DComposition_perspective([dLPosition], chart, true, false)[0];\n        alignTo.x = dLPosition.x - xOffset;\n        // #7103 If point is outside of plotArea, hide data label.\n        alignTo.y = point.outside3dPlot ? -9e9 : dLPosition.y;\n    }\n    proceed.apply(this, [].slice.call(arguments, 1));\n}\n/**\n * Don't use justifyDataLabel when point is outsidePlot.\n * @private\n */\nfunction wrapSeriesJustifyDataLabel(proceed) {\n    return (!(arguments[2].outside3dPlot) ?\n        proceed.apply(this, [].slice.call(arguments, 1)) :\n        false);\n}\n/**\n * Added stackLabels position calculation for 3D charts.\n * @private\n */\nfunction wrapStackItemGetStackBox(proceed, stackBoxProps) {\n    const stackBox = proceed.apply(this, [].slice.call(arguments, 1));\n    // Only do this for 3D graph\n    const stackItem = this, chart = this.axis.chart, { width: xWidth } = stackBoxProps;\n    if (chart.is3d() && stackItem.base) {\n        // First element of stackItem.base is an index of base series.\n        const baseSeriesInd = +(stackItem.base).split(',')[0];\n        const columnSeries = chart.series[baseSeriesInd];\n        const options3d = chart.options.chart.options3d;\n        // Only do this if base series is a column or inherited type,\n        // use its barW, z and depth parameters\n        // for correct stackLabels position calculation\n        if (columnSeries &&\n            columnSeries.type === 'column') {\n            let dLPosition = {\n                x: stackBox.x + (chart.inverted ? stackBox.height : xWidth / 2),\n                y: stackBox.y,\n                z: columnSeries.options.depth / 2\n            };\n            if (chart.inverted) {\n                // Do not use default offset calculation logic\n                // for 3D inverted stackLabels.\n                stackBox.width = 0;\n                // When chart is upside down\n                // (alpha angle between 180 and 360 degrees)\n                // it is needed to add column width to calculated value.\n                if (options3d.alpha >= 90 && options3d.alpha <= 270) {\n                    dLPosition.y += xWidth;\n                }\n            }\n            dLPosition = Column3DComposition_perspective([dLPosition], chart, true, false)[0];\n            stackBox.x = dLPosition.x - xWidth / 2;\n            stackBox.y = dLPosition.y;\n        }\n    }\n    return stackBox;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst Column3DComposition = {\n    compose: Column3DComposition_compose\n};\n/* harmony default export */ const Column3D_Column3DComposition = (Column3DComposition);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Depth of the columns in a 3D column chart.\n *\n * @type      {number}\n * @default   25\n * @since     4.0\n * @product   highcharts\n * @requires  highcharts-3d\n * @apioption plotOptions.column.depth\n */\n/**\n * 3D columns only. The color of the edges. Similar to `borderColor`, except it\n * defaults to the same color as the column.\n *\n * @type      {Highcharts.ColorString}\n * @product   highcharts\n * @requires  highcharts-3d\n * @apioption plotOptions.column.edgeColor\n */\n/**\n * 3D columns only. The width of the colored edges.\n *\n * @type      {number}\n * @default   1\n * @product   highcharts\n * @requires  highcharts-3d\n * @apioption plotOptions.column.edgeWidth\n */\n/**\n * The spacing between columns on the Z Axis in a 3D chart.\n *\n * @type      {number}\n * @default   1\n * @since     4.0\n * @product   highcharts\n * @requires  highcharts-3d\n * @apioption plotOptions.column.groupZPadding\n */\n''; // Keeps doclets above in transpiled file\n\n;// ./code/es-modules/Series/Pie3D/Pie3DPoint.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  3D pie series\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { pie: { prototype: { pointClass: PiePoint } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n/* *\n *\n *  Class\n *\n * */\nclass Pie3DPoint extends PiePoint {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    haloPath() {\n        return this.series?.chart.is3d() ?\n            [] : super.haloPath.apply(this, arguments);\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Pie3D_Pie3DPoint = (Pie3DPoint);\n\n;// ./code/es-modules/Series/Pie3D/Pie3DSeries.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  3D pie series\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { composed: Pie3DSeries_composed, deg2rad: Pie3DSeries_deg2rad } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nconst { pie: PieSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: Pie3DSeries_extend, pick: Pie3DSeries_pick, pushUnique: Pie3DSeries_pushUnique } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass Pie3DSeries extends PieSeries {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static compose(SeriesClass) {\n        if (Pie3DSeries_pushUnique(Pie3DSeries_composed, 'Pie3D')) {\n            SeriesClass.types.pie = Pie3DSeries;\n        }\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    addPoint() {\n        super.addPoint.apply(this, arguments);\n        if (this.chart.is3d()) {\n            // Destroy (and rebuild) everything!!!\n            this.update(this.userOptions, true); // #3845 pass the old options\n        }\n    }\n    /**\n     * @private\n     */\n    animate(init) {\n        if (!this.chart.is3d()) {\n            super.animate.apply(this, arguments);\n        }\n        else {\n            const center = this.center, group = this.group, markerGroup = this.markerGroup;\n            let animation = this.options.animation, attribs;\n            if (animation === true) {\n                animation = {};\n            }\n            // Initialize the animation\n            if (init) {\n                // Scale down the group and place it in the center\n                group.oldtranslateX = Pie3DSeries_pick(group.oldtranslateX, group.translateX);\n                group.oldtranslateY = Pie3DSeries_pick(group.oldtranslateY, group.translateY);\n                attribs = {\n                    translateX: center[0],\n                    translateY: center[1],\n                    scaleX: 0.001, // #1499\n                    scaleY: 0.001\n                };\n                group.attr(attribs);\n                if (markerGroup) {\n                    markerGroup.attrSetters = group.attrSetters;\n                    markerGroup.attr(attribs);\n                }\n                // Run the animation\n            }\n            else {\n                attribs = {\n                    translateX: group.oldtranslateX,\n                    translateY: group.oldtranslateY,\n                    scaleX: 1,\n                    scaleY: 1\n                };\n                group.animate(attribs, animation);\n                if (markerGroup) {\n                    markerGroup.animate(attribs, animation);\n                }\n            }\n        }\n    }\n    /**\n     * @private\n     */\n    getDataLabelPosition(point, distance) {\n        const labelPosition = super.getDataLabelPosition(point, distance);\n        if (this.chart.is3d()) {\n            const options3d = this.chart.options.chart.options3d, shapeArgs = point.shapeArgs, r = shapeArgs.r, \n            // #3240 issue with datalabels for 0 and null values\n            a1 = ((shapeArgs.alpha || options3d?.alpha) *\n                Pie3DSeries_deg2rad), b1 = ((shapeArgs.beta || options3d?.beta) *\n                Pie3DSeries_deg2rad), a2 = (shapeArgs.start + shapeArgs.end) / 2, connectorPosition = labelPosition.connectorPosition, yOffset = (-r * (1 - Math.cos(a1)) * Math.sin(a2)), xOffset = r * (Math.cos(b1) - 1) * Math.cos(a2);\n            // Apply perspective on label positions\n            for (const coordinates of [\n                labelPosition?.natural,\n                connectorPosition.breakAt,\n                connectorPosition.touchingSliceAt\n            ]) {\n                coordinates.x += xOffset;\n                coordinates.y += yOffset;\n            }\n        }\n        return labelPosition;\n    }\n    /**\n     * @private\n     */\n    pointAttribs(point) {\n        const attr = super.pointAttribs.apply(this, arguments), options = this.options;\n        if (this.chart.is3d() && !this.chart.styledMode) {\n            attr.stroke = options.edgeColor || point.color || this.color;\n            attr['stroke-width'] = Pie3DSeries_pick(options.edgeWidth, 1);\n        }\n        return attr;\n    }\n    /**\n     * @private\n     */\n    translate() {\n        super.translate.apply(this, arguments);\n        // Do not do this if the chart is not 3D\n        if (!this.chart.is3d()) {\n            return;\n        }\n        const series = this, seriesOptions = series.options, depth = seriesOptions.depth || 0, options3d = series.chart.options.chart.options3d, alpha = options3d.alpha, beta = options3d.beta;\n        let z = seriesOptions.stacking ?\n            (seriesOptions.stack || 0) * depth :\n            series._i * depth;\n        z += depth / 2;\n        if (seriesOptions.grouping !== false) {\n            z = 0;\n        }\n        for (const point of series.points) {\n            const shapeArgs = point.shapeArgs;\n            point.shapeType = 'arc3d';\n            shapeArgs.z = z;\n            shapeArgs.depth = depth * 0.75;\n            shapeArgs.alpha = alpha;\n            shapeArgs.beta = beta;\n            shapeArgs.center = series.center;\n            const angle = (shapeArgs.end + shapeArgs.start) / 2;\n            point.slicedTranslation = {\n                translateX: Math.round(Math.cos(angle) *\n                    seriesOptions.slicedOffset *\n                    Math.cos(alpha * Pie3DSeries_deg2rad)),\n                translateY: Math.round(Math.sin(angle) *\n                    seriesOptions.slicedOffset *\n                    Math.cos(alpha * Pie3DSeries_deg2rad))\n            };\n        }\n    }\n    /**\n     * @private\n     */\n    drawTracker() {\n        super.drawTracker.apply(this, arguments);\n        // Do not do this if the chart is not 3D\n        if (!this.chart.is3d()) {\n            return;\n        }\n        for (const point of this.points) {\n            if (point.graphic) {\n                for (const face of ['out', 'inn', 'side1', 'side2']) {\n                    if (point.graphic) {\n                        point.graphic[face].element.point = point;\n                    }\n                }\n            }\n        }\n    }\n}\nPie3DSeries_extend(Pie3DSeries.prototype, {\n    pointClass: Pie3D_Pie3DPoint\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Pie3D_Pie3DSeries = (Pie3DSeries);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * The thickness of a 3D pie.\n *\n * @type      {number}\n * @default   0\n * @since     4.0\n * @product   highcharts\n * @requires  highcharts-3d\n * @apioption plotOptions.pie.depth\n */\n''; // Keeps doclets above after transpiledion\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Series\",\"types\",\"scatter\"],\"commonjs\":[\"highcharts\",\"Series\",\"types\",\"scatter\"],\"commonjs2\":[\"highcharts\",\"Series\",\"types\",\"scatter\"],\"root\":[\"Highcharts\",\"Series\",\"types\",\"scatter\"]}\nvar highcharts_Series_types_scatter_commonjs_highcharts_Series_types_scatter_commonjs2_highcharts_Series_types_scatter_root_Highcharts_Series_types_scatter_ = __webpack_require__(632);\nvar highcharts_Series_types_scatter_commonjs_highcharts_Series_types_scatter_commonjs2_highcharts_Series_types_scatter_root_Highcharts_Series_types_scatter_default = /*#__PURE__*/__webpack_require__.n(highcharts_Series_types_scatter_commonjs_highcharts_Series_types_scatter_commonjs2_highcharts_Series_types_scatter_root_Highcharts_Series_types_scatter_);\n;// ./code/es-modules/Series/Scatter3D/Scatter3DPoint.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  Scatter 3D series.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { pointClass: ScatterPoint } = (highcharts_Series_types_scatter_commonjs_highcharts_Series_types_scatter_commonjs2_highcharts_Series_types_scatter_root_Highcharts_Series_types_scatter_default()).prototype;\n\nconst { defined: Scatter3DPoint_defined } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass Scatter3DPoint extends ScatterPoint {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    applyOptions() {\n        super.applyOptions.apply(this, arguments);\n        if (!Scatter3DPoint_defined(this.z)) {\n            this.z = 0;\n        }\n        return this;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Scatter3D_Scatter3DPoint = (Scatter3DPoint);\n\n;// ./code/es-modules/Series/Scatter3D/Scatter3DSeriesDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  Scatter 3D series.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A 3D scatter plot uses x, y and z coordinates to display values for three\n * variables for a set of data.\n *\n * @sample {highcharts} highcharts/3d/scatter/\n *         Simple 3D scatter\n * @sample {highcharts} highcharts/demo/3d-scatter-draggable\n *         Draggable 3d scatter\n *\n * @extends      plotOptions.scatter\n * @excluding    boostThreshold, boostBlending, cluster, dragDrop,\n *               legendSymbolColor\n * @product      highcharts\n * @requires     highcharts-3d\n * @optionparent plotOptions.scatter3d\n */\nconst Scatter3DSeriesDefaults = {\n    tooltip: {\n        pointFormat: 'x: <b>{point.x}</b><br/>y: <b>{point.y}</b><br/>z: <b>{point.z}</b><br/>'\n    }\n};\n/**\n * A `scatter3d` series. If the [type](#series.scatter3d.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * scatter3d](#plotOptions.scatter3d).\n *\n * @extends   series,plotOptions.scatter3d\n * @excluding boostThreshold, boostBlending\n * @product   highcharts\n * @requires  highcharts-3d\n * @apioption series.scatter3d\n */\n/**\n * An array of data points for the series. For the `scatter3d` series\n * type, points can be given in the following ways:\n *\n * 1.  An array of arrays with 3 values. In this case, the values correspond\n * to `x,y,z`. If the first value is a string, it is applied as the name\n * of the point, and the `x` value is inferred.\n *\n *  ```js\n *     data: [\n *         [0, 0, 1],\n *         [1, 8, 7],\n *         [2, 9, 2]\n *     ]\n *  ```\n *\n * 3.  An array of objects with named values. The following snippet shows only a\n * few settings, see the complete options set below. If the total number of data\n * points exceeds the series'\n * [turboThreshold](#series.scatter3d.turboThreshold), this option is not\n * available.\n *\n *  ```js\n *     data: [{\n *         x: 1,\n *         y: 2,\n *         z: 24,\n *         name: \"Point2\",\n *         color: \"#00FF00\"\n *     }, {\n *         x: 1,\n *         y: 4,\n *         z: 12,\n *         name: \"Point1\",\n *         color: \"#FF00FF\"\n *     }]\n *  ```\n *\n * @sample {highcharts} highcharts/chart/reflow-true/\n *         Numerical values\n * @sample {highcharts} highcharts/series/data-array-of-arrays/\n *         Arrays of numeric x and y\n * @sample {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *         Arrays of datetime x and y\n * @sample {highcharts} highcharts/series/data-array-of-name-value/\n *         Arrays of point.name and y\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<Array<number>|*>}\n * @extends   series.scatter.data\n * @product   highcharts\n * @apioption series.scatter3d.data\n */\n/**\n * The z value for each data point.\n *\n * @type      {number}\n * @product   highcharts\n * @apioption series.scatter3d.data.z\n */\n''; // Detachs doclets above\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Scatter3D_Scatter3DSeriesDefaults = (Scatter3DSeriesDefaults);\n\n;// ./code/es-modules/Series/Scatter3D/Scatter3DSeries.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  Scatter 3D series.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { pointCameraDistance: Scatter3DSeries_pointCameraDistance } = Core_Math3D;\n\n\n\n\n\nconst { extend: Scatter3DSeries_extend, merge: Scatter3DSeries_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.scatter3d\n *\n * @augments Highcharts.Series\n */\nclass Scatter3DSeries extends (highcharts_Series_types_scatter_commonjs_highcharts_Series_types_scatter_commonjs2_highcharts_Series_types_scatter_root_Highcharts_Series_types_scatter_default()) {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    pointAttribs(point) {\n        const attribs = super.pointAttribs.apply(this, arguments);\n        if (this.chart.is3d() && point) {\n            attribs.zIndex =\n                Scatter3DSeries_pointCameraDistance(point, this.chart);\n        }\n        return attribs;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nScatter3DSeries.defaultOptions = Scatter3DSeries_merge((highcharts_Series_types_scatter_commonjs_highcharts_Series_types_scatter_commonjs2_highcharts_Series_types_scatter_root_Highcharts_Series_types_scatter_default()).defaultOptions, Scatter3D_Scatter3DSeriesDefaults);\nScatter3DSeries_extend(Scatter3DSeries.prototype, {\n    axisTypes: ['xAxis', 'yAxis', 'zAxis'],\n    // Require direct touch rather than using the k-d-tree, because the\n    // k-d-tree currently doesn't take the xyz coordinate system into\n    // account (#4552)\n    directTouch: true,\n    parallelArrays: ['x', 'y', 'z'],\n    pointArrayMap: ['x', 'y', 'z'],\n    pointClass: Scatter3D_Scatter3DPoint\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('scatter3d', Scatter3DSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Scatter3D_Scatter3DSeries = ((/* unused pure expression or super */ null && (Scatter3DSeries)));\n\n;// ./code/es-modules/masters/highcharts-3d.js\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n// Compositions\nArea3D_Area3DSeries.compose(G.Series.types.area);\nAxis3DComposition.compose(G.Axis, G.Tick);\nChart_Chart3D.compose(G.Chart, G.Fx);\nColumn3D_Column3DComposition.compose(G.Series, (highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_default()));\nPie3D_Pie3DSeries.compose(G.Series);\nSeries_Series3D.compose(G.Series);\nSVG_SVGRenderer3D.compose(highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default().getRendererType());\nAxis_ZAxis.compose(G.Chart);\n/* harmony default export */ const highcharts_3d_src = (G);\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__620__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__608__", "__WEBPACK_EXTERNAL_MODULE__820__", "__WEBPACK_EXTERNAL_MODULE__184__", "__WEBPACK_EXTERNAL_MODULE__532__", "__WEBPACK_EXTERNAL_MODULE__632__", "Chart3D", "SVGRenderer3D", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "highcharts_3d_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default", "deg2rad", "pick", "perspective", "points", "chart", "insidePlotArea", "useInvertedPersp", "options3d", "options", "inverted", "origin", "x", "plot<PERSON>id<PERSON>", "y", "plotHeight", "z", "depth", "vd", "viewDistance", "scale", "scale3d", "beta", "alpha", "angles", "cosA", "Math", "cos", "cosB", "sinA", "sin", "sinB", "plotLeft", "plotTop", "map", "point", "rotated", "coordinate", "perspective3D", "distance", "projection", "Number", "POSITIVE_INFINITY", "shapeArea", "vertexes", "area", "i", "j", "length", "Core_Math3D", "pointCameraDistance", "coordinates", "cameraPosition", "sqrt", "pow", "plotX", "plotY", "plotZ", "shapeArea3D", "parse", "color", "defaultOptions", "genericDefaultOptions", "Chart3D_perspective", "Chart3D_shapeArea3D", "addEvent", "isArray", "merge", "Chart3D_pick", "wrap", "onAddSeries", "e", "is3d", "type", "onAfterDrawChartBox", "chart3d", "renderer", "frame", "get3dFrame", "xm", "xp", "ym", "yp", "zp", "xmm", "left", "visible", "size", "xpp", "right", "ymm", "top", "ypp", "bottom", "zmm", "zm", "front", "zpp", "back", "verb", "hasRendered", "frame3d", "frameShapes", "polyhedron", "add", "zIndex", "frontFacing", "faces", "fill", "brighten", "enabled", "onAfterGetContainer", "styledMode", "name", "slope", "for<PERSON>ach", "cfg", "tagName", "attributes", "id", "children", "onAfterInit", "series", "s", "defaultSeriesType", "onAfterSetChartSize", "clipBox", "margin", "chartWidth", "chartHeight", "fitToPlot", "getScale", "onBeforeRedraw", "isDirtyBox", "onBeforeRender", "onInit", "Additions", "wrapIsInsidePlot", "proceed", "apply", "slice", "arguments", "wrapRenderSeries", "translate", "render", "wrapSetClassName", "container", "className", "axisLabelPosition", "compose", "ChartClass", "FxClass", "chartProto", "fxProto", "propsRequireDirtyBox", "push", "propsRequireUpdateSeries", "matrixSetter", "interpolated", "pos", "start", "end", "elem", "attr", "constructor", "frameOptions", "faceOrientation", "bottomOrientation", "topOrientation", "leftOrientation", "rightOrientation", "frontOrientation", "backOrientation", "defaultShowBottom", "defaultShowTop", "defaultShowLeft", "defaultShowRight", "concat", "xAxis", "yAxis", "zAxis", "axis", "horiz", "opposite", "getFaceOptions", "sources", "defaultVisible", "faceAttrs", "val", "isVisible", "ret", "axes", "side", "isValidEdge", "face1", "face2", "y<PERSON><PERSON>", "xDir", "xBottomEdges", "xTopEdges", "zBottomEdges", "zTopEdges", "pickEdge", "edges", "mult", "projections", "best", "plotRight", "plotBottom", "originX", "originY", "bbox3d", "minX", "MAX_VALUE", "maxX", "minY", "maxY", "corners", "corner", "min", "max", "abs", "Chart_Chart3D", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "composed", "Area3DSeries_perspective", "line", "lineProto", "seriesTypes", "pushUnique", "Area3DSeries_wrap", "wrapAreaSeriesGetGraphPath", "svgPath", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "round", "get<PERSON><PERSON><PERSON>old", "threshold", "bottomPoints", "rawPointsX", "stacking", "yBottom", "zPadding", "group", "markerGroup", "translateX", "translateY", "reversed", "bottomPath", "areaPath", "splice", "xMap", "graphPath", "Axis_Axis3DDefaults", "labels", "position3d", "skew3d", "title", "Tick3DComposition_composed", "Tick3DComposition_addEvent", "extend", "Tick3DComposition_pushUnique", "Tick3DComposition_wrap", "onTickAfterGetLabelPosition", "axis3D", "fix3dPosition", "wrapTickGetMarkPath", "path", "pArr", "toLineSegments", "Tick3DComposition", "TickClass", "Axis3DComposition_deg2rad", "Axis3DComposition_perspective", "Axis3DComposition_perspective3D", "Axis3DComposition_shapeArea", "Axis3DComposition_addEvent", "Axis3DComposition_merge", "Axis3DComposition_pick", "Axis3DComposition_wrap", "onAxisAfterSetOptions", "coll", "tickWidth", "gridLineWidth", "onAxisDrawCrosshair", "crosshairPos", "isXAxis", "axisXpos", "len", "axisYpos", "onAxisInit", "Axis3DAdditions", "wrapAxisGetLinePath", "wrapAxisGetPlotBandPath", "args", "from", "to", "fromPath", "getPlotLinePath", "value", "to<PERSON><PERSON>", "fromStartSeg", "fromEndSeg", "toStartSeg", "toEndSeg", "wrapAxisGetPlotLinePath", "isZAxis", "startSegment", "endSegment", "pathSegments", "swapZ", "wrapAxisGetSlotWidth", "tick", "gridGroup", "tickPositions", "ticks", "categories", "label", "labelPos", "prevLabelPos", "nextLabelPos", "firstGridLine", "element", "childNodes", "getBBox", "frame3DLeft", "index", "indexOf", "prevTick", "nextTick", "xy", "wrapAxisGetTitlePosition", "AxisClass", "keepProps", "includes", "axisProto", "isTitle", "positionMode", "skew", "offsetX", "offsetY", "vecX", "vecY", "reverseFlap", "sina", "cosa", "vecZ", "sinb", "projected", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pointsProjected", "matrix", "p", "highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_", "highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default", "highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_", "highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default", "Series3D_composed", "Series3D_perspective", "Series3D_addEvent", "Series3D_extend", "isNumber", "Series3D_merge", "Series3D_pick", "Series3D_pushUnique", "Series3D", "SeriesClass", "translate3dPoints", "projectedPoint", "zValue", "seriesOptions", "rawPoints", "stack", "groupZPadding", "data", "rawPoint", "logarithmic", "val2lin", "isInside", "axisZpos", "projectedPoints", "highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_", "highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_default", "SVGElement3D_color", "Element", "SVGElement", "getRendererType", "defined", "SVGElement3D_pick", "SVGElement3D", "parts", "pathType", "initArgs", "elem3d", "paths", "zIndexes", "part", "attribs", "filter", "forcedSides", "singleSetterForParts", "values", "duration", "complete", "newAttr", "optionsToApply", "hasZIndexes", "keys", "processParts", "props", "partsProps", "destroy", "continueAnimation", "shapeArgs", "animate", "fillSetter", "types", "base", "cuboid", "animObject", "SVGRenderer3D_color", "charts", "SVGRenderer3D_deg2rad", "SVGRenderer3D_perspective", "SVGRenderer3D_shapeArea", "SVGRenderer3D_defined", "SVGRenderer3D_extend", "SVGRenderer3D_merge", "SVGRenderer3D_pick", "PI", "dFactor", "curveTo", "cx", "cy", "rx", "ry", "dx", "dy", "arcAngle", "result", "to<PERSON><PERSON><PERSON><PERSON>", "closed", "m", "face3d", "elementProto", "createElement", "hash", "chartIndex", "vertexes2d", "visibility", "params", "g", "pop", "element3d", "SVG_SVGElement3D", "cuboidPath", "h", "height", "w", "width", "shape", "mapSidePath", "mapPath", "pickShape", "verticesIndex1", "verticesIndex2", "dummyFace1", "dummyFace2", "path1", "isFront", "path2", "isTop", "path3", "isRight", "incrementX", "incrementY", "incrementZ", "arc3d", "wrapper", "customAttribs", "extractCustom", "ca", "fn", "side1", "side2", "inn", "out", "onAdd", "parent", "parentGroup", "face", "setter", "setPaths", "arc3dPath", "zTop", "zInn", "zOut", "zSide1", "zSide2", "center", "setRadialReference", "darker", "el", "paramArr", "animation", "randomProp", "random", "toString", "substring", "anim", "globalAnimation", "noop", "interpolate", "step", "fx", "r", "innerR", "hide", "show", "inherit", "ir", "cs", "ss", "ce", "se", "irx", "iry", "b", "start2", "end2", "midEnd", "angleCorr", "atan2", "angleEnd", "angleStart", "angleMid", "toZeroPIRange", "angle", "a1", "a2", "a3", "SVGRendererClass", "rendererProto", "Element3D", "SVG_SVGRenderer3D", "highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_", "highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default", "ZAxis_defaultOptions", "ZAxis_addEvent", "ZAxis_merge", "ZAxis_pick", "splat", "chartAddZAxis", "ZAxis", "onChartAfterCreateAxes", "zAxisOptions", "axisOptions", "addZAxis", "setScale", "offset", "lineWidth", "collectionsWithInit", "collectionsWithUpdate", "init", "userOptions", "getSeriesExtremes", "hasVisibleSeries", "dataMin", "dataMax", "ignoreMinPadding", "ignoreMaxPadding", "buildStacks", "reserveSpace", "positive<PERSON><PERSON><PERSON><PERSON>nly", "zData", "getColumn", "setAxisSize", "Column3DComposition_composed", "Column3DComposition_perspective", "Column3DComposition_addEvent", "Column3DComposition_extend", "Column3DComposition_pick", "Column3DComposition_pushUnique", "Column3DComposition_wrap", "columnSeriesTranslate3dShapes", "borderCrisp", "borderWidth", "point2dPos", "grouping", "outside3dPlot", "borderlessBase", "dimensions", "tooltipPos", "shapeType", "clientX", "plot3d", "translatedTTPos", "onColumnSeriesAfterInit", "reversedStacks", "stacks", "retrieveStacks", "totalStacks", "stackNumber", "position", "wrapColumnPointHasNewShapeType", "graphic", "nodeName", "wrapColumnSeriesAnimate", "shapey", "stackY", "negative", "drawDataLabels", "wrapColumnSeriesPlotGroup", "_name", "_visibility", "_zIndex", "columnGroup", "getPlotBox", "survive", "Array", "wrapColumnSeriesPointAttribs", "stroke", "edgeColor", "edgeWidth", "wrapColumnSeriesSetState", "state", "inactiveOtherPoints", "wrapColumnSeriesSetVisible", "vis", "wrapColumnSeriesTranslate", "translate3dShapes", "wrapSeriesAlignDataLabel", "_dataLabel", "alignTo", "is", "inside", "xOffset", "pointWidth", "dLPosition", "wrapSeriesJustifyDataLabel", "wrapStackItemGetStackBox", "stackBoxProps", "stackBox", "xWidth", "stackItem", "baseSeriesInd", "split", "columnSeries", "pie", "pointClass", "PiePoint", "Pie3D_Pie3DPoint", "haloPath", "Pie3DSeries_composed", "Pie3DSeries_deg2rad", "PieSeries", "Pie3DSeries_extend", "Pie3DSeries_pick", "Pie3DSeries_pushUnique", "Pie3DSeries", "addPoint", "update", "oldtranslateX", "oldtranslateY", "scaleX", "scaleY", "attrSetters", "getDataLabelPosition", "labelPosition", "b1", "connectorPosition", "yOffset", "natural", "breakAt", "touchingSliceAt", "pointAttribs", "_i", "slicedTranslation", "slicedOffset", "drawTracker", "highcharts_Series_types_scatter_commonjs_highcharts_Series_types_scatter_commonjs2_highcharts_Series_types_scatter_root_Highcharts_Series_types_scatter_", "highcharts_Series_types_scatter_commonjs_highcharts_Series_types_scatter_commonjs2_highcharts_Series_types_scatter_root_Highcharts_Series_types_scatter_default", "ScatterPoint", "Scatter3DPoint_defined", "Scatter3D_Scatter3DPoint", "applyOptions", "Scatter3DSeries_pointCameraDistance", "Scatter3DSeries_extend", "Scatter3DSeries_merge", "Scatter3DSeries", "tooltip", "pointFormat", "axisTypes", "directTouch", "parallelArrays", "pointArrayMap", "registerSeriesType", "G", "Area3D_Area3DSeries", "AreaSeriesClass", "Series", "Axis3DComposition", "Axis", "Tick", "Chart", "Fx", "Column3D_Column3DComposition", "StackItemClass", "seriesProto", "stackItemProto", "column", "ColumnSeriesClass", "columnRange", "ColumnRangeSeriesClass", "columnSeriesProto", "columnPointProto", "columnRangeSeriesProto", "Pie3D_Pie3DSeries", "Series_Series3D", "Axis_ZAxis"], "mappings": "CAWA,AAXA;;;;;;;;;;CAUC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,gBAAmB,CAAEA,EAAK,WAAc,CAAC,MAAS,CAAEA,EAAK,WAAc,CAAC,SAAY,CAAEA,EAAK,WAAc,CAAC,IAAO,CAAEA,EAAK,WAAc,CAAC,MAAS,CAAC,KAAQ,CAAC,OAAU,EACpS,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,2BAA4B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,KAAQ,CAACA,EAAK,cAAiB,CAACA,EAAK,gBAAmB,CAACA,EAAK,MAAS,CAACA,EAAK,SAAY,CAACA,EAAK,IAAO,CAACA,EAAK,MAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAE,GAC9O,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,2BAA2B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,gBAAmB,CAAEA,EAAK,WAAc,CAAC,MAAS,CAAEA,EAAK,WAAc,CAAC,SAAY,CAAEA,EAAK,WAAc,CAAC,IAAO,CAAEA,EAAK,WAAc,CAAC,MAAS,CAAC,KAAQ,CAAC,OAAU,EAEhUA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,gBAAmB,CAAEA,EAAK,UAAa,CAAC,MAAS,CAAEA,EAAK,UAAa,CAAC,SAAY,CAAEA,EAAK,UAAa,CAAC,IAAO,CAAEA,EAAK,UAAa,CAAC,MAAS,CAAC,KAAQ,CAAC,OAAU,CACzS,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,IACjR,AAAC,CAAA,KACP,aACA,IAsZNC,EAgxFAC,EAtqGUC,EAAuB,CAE/B,IACC,AAACf,IAERA,EAAOD,OAAO,CAAGW,CAEX,EAEA,IACC,AAACV,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGY,CAEX,EAEA,IACC,AAACX,IAERA,EAAOD,OAAO,CAAGS,CAEX,EAEA,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGa,CAEX,EAEA,IACC,AAACZ,IAERA,EAAOD,OAAO,CAAGU,CAEX,EAEA,IACC,AAACT,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIW,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAapB,OAAO,CAG5B,IAAIC,EAASgB,CAAwB,CAACE,EAAS,CAAG,CAGjDnB,QAAS,CAAC,CACX,EAMA,OAHAgB,CAAmB,CAACG,EAAS,CAAClB,EAAQA,EAAOD,OAAO,CAAEkB,GAG/CjB,EAAOD,OAAO,AACtB,CAMCkB,EAAoBI,CAAC,CAAG,AAACrB,IACxB,IAAIsB,EAAStB,GAAUA,EAAOuB,UAAU,CACvC,IAAOvB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAiB,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACzB,EAAS2B,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAAC7B,EAAS4B,IAC5EE,OAAOC,cAAc,CAAC/B,EAAS4B,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,EAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAA+FzB,EAAoB,KACnH0B,EAAmH1B,EAAoBI,CAAC,CAACqB,GAa7I,GAAM,CAAEE,QAAAA,CAAO,CAAE,CAAIH,IAEf,CAAEI,KAAAA,CAAI,CAAE,CAAIJ,IAiFlB,SAASK,EAAYC,CAAM,CAAEC,CAAK,CAAEC,CAAc,CAAEC,CAAgB,EAChE,IAAMC,EAAYH,EAAMI,OAAO,CAACJ,KAAK,CAACG,SAAS,CAI/CE,EAAWR,EAAKK,EAAkBD,EAAAA,GAAiBD,EAAMK,QAAQ,EAAWC,EAAS,CACjFC,EAAGP,EAAMQ,SAAS,CAAG,EACrBC,EAAGT,EAAMU,UAAU,CAAG,EACtBC,EAAGR,EAAUS,KAAK,CAAG,EACrBC,GAAIhB,EAAKM,EAAUS,KAAK,CAAE,GAAKf,EAAKM,EAAUW,YAAY,CAAE,EAChE,EAAGC,EAAQf,EAAMgB,OAAO,EAAI,EAAGC,EAAOrB,EAAUO,EAAUc,IAAI,CAAIZ,CAAAA,EAAW,GAAK,CAAA,EAAIa,EAAQtB,EAAUO,EAAUe,KAAK,CAAIb,CAAAA,EAAW,GAAK,CAAA,EAAIc,EAAS,CACpJC,KAAMC,KAAKC,GAAG,CAACJ,GACfK,KAAMF,KAAKC,GAAG,CAAC,CAACL,GAChBO,KAAMH,KAAKI,GAAG,CAACP,GACfQ,KAAML,KAAKI,GAAG,CAAC,CAACR,EACpB,EAMA,OALKhB,IACDK,EAAOC,CAAC,EAAIP,EAAM2B,QAAQ,CAC1BrB,EAAOG,CAAC,EAAIT,EAAM4B,OAAO,EAGtB7B,EAAO8B,GAAG,CAAC,SAAUC,CAAK,MArDnBvB,EAAGE,EAAGE,EAsDhB,IAAMoB,GAtDIxB,EAsDe,AAACF,CAAAA,EAAWyB,EAAMrB,CAAC,CAAGqB,EAAMvB,CAAC,AAADA,EAAKD,EAAOC,CAAC,CAtDrDE,EAsDuD,AAACJ,CAAAA,EAAWyB,EAAMvB,CAAC,CAAGuB,EAAMrB,CAAC,AAADA,EAAKH,EAAOG,CAAC,CAtD7FE,EAsD+F,AAACmB,CAAAA,EAAMnB,CAAC,EAAI,CAAA,EAAKL,EAAOK,CAAC,CArDrI,CACHJ,EAAGY,AAoDuIA,EApDhII,IAAI,CAAGhB,EAAIY,AAoDqHA,EApD9GO,IAAI,CAAGf,EACnCF,EAAG,CAACU,AAmDsIA,EAnD/HK,IAAI,CAAGL,AAmDwHA,EAnDjHO,IAAI,CAAGnB,EAAIY,AAmDsGA,EAnD/FC,IAAI,CAAGX,EAC9CU,AAkDsIA,EAlD/HI,IAAI,CAAGJ,AAkDwHA,EAlDjHK,IAAI,CAAGb,EAChCA,EAAGQ,AAiDuIA,EAjDhIC,IAAI,CAAGD,AAiDyHA,EAjDlHO,IAAI,CAAGnB,EAAIY,AAiDuGA,EAjDhGK,IAAI,CAAGf,EAC7CU,AAgDsIA,EAhD/HC,IAAI,CAAGD,AAgDwHA,EAhDjHI,IAAI,CAAGZ,CACpC,GAiDIqB,EAAaC,EAAcF,EAASzB,EAAQA,EAAOO,EAAE,EAKrD,OAHAmB,EAAWzB,CAAC,CAAGyB,EAAWzB,CAAC,CAAGQ,EAAQT,EAAOC,CAAC,CAC9CyB,EAAWvB,CAAC,CAAGuB,EAAWvB,CAAC,CAAGM,EAAQT,EAAOG,CAAC,CAC9CuB,EAAWrB,CAAC,CAAGoB,EAAQpB,CAAC,CAAGI,EAAQT,EAAOK,CAAC,CACpC,CACHJ,EAAIF,EAAW2B,EAAWvB,CAAC,CAAGuB,EAAWzB,CAAC,CAC1CE,EAAIJ,EAAW2B,EAAWzB,CAAC,CAAGyB,EAAWvB,CAAC,CAC1CE,EAAGqB,EAAWrB,CAAC,AACnB,CACJ,EACJ,CAqBA,SAASsB,EAAcD,CAAU,CAAE1B,CAAM,CAAE4B,CAAQ,EAC/C,IAAMC,EAAa,AAAC,AAACD,EAAW,GAC3BA,EAAWE,OAAOC,iBAAiB,CACpCH,EAAYF,CAAAA,EAAWrB,CAAC,CAAGL,EAAOK,CAAC,CAAGuB,CAAO,EAC7C,EACJ,MAAO,CACH3B,EAAGyB,EAAWzB,CAAC,CAAG4B,EAClB1B,EAAGuB,EAAWvB,CAAC,CAAG0B,CACtB,CACJ,CA+CA,SAASG,EAAUC,CAAQ,EACvB,IAAIC,EAAO,EAAGC,EAAGC,EACjB,IAAKD,EAAI,EAAGA,EAAIF,EAASI,MAAM,CAAEF,IAC7BC,EAAI,AAACD,CAAAA,EAAI,CAAA,EAAKF,EAASI,MAAM,CAC7BH,GAAQD,CAAQ,CAACE,EAAE,CAAClC,CAAC,CAAGgC,CAAQ,CAACG,EAAE,CAACjC,CAAC,CAAG8B,CAAQ,CAACG,EAAE,CAACnC,CAAC,CAAGgC,CAAQ,CAACE,EAAE,CAAChC,CAAC,CAEzE,OAAO+B,EAAO,CAClB,CAoC6B,IAAMI,EAPpB,CACX9C,YAAAA,EACAmC,cAAAA,EACAY,oBAnEJ,SAA6BC,CAAW,CAAE9C,CAAK,EAC3C,IAAMG,EAAYH,EAAMI,OAAO,CAACJ,KAAK,CAACG,SAAS,CAAE4C,EAAiB,CAC9DxC,EAAGP,EAAMQ,SAAS,CAAG,EACrBC,EAAGT,EAAMU,UAAU,CAAG,EACtBC,EAAGd,EAAKM,EAAUS,KAAK,CAAE,GAAKf,EAAKM,EAAUW,YAAY,CAAE,GACvDX,EAAUS,KAAK,AACvB,EAKA,OAHWS,KAAK2B,IAAI,CAAC3B,KAAK4B,GAAG,CAACF,EAAexC,CAAC,CAAGV,EAAKiD,EAAYI,KAAK,CAAEJ,EAAYvC,CAAC,EAAG,GACrFc,KAAK4B,GAAG,CAACF,EAAetC,CAAC,CAAGZ,EAAKiD,EAAYK,KAAK,CAAEL,EAAYrC,CAAC,EAAG,GACpEY,KAAK4B,GAAG,CAACF,EAAepC,CAAC,CAAGd,EAAKiD,EAAYM,KAAK,CAAEN,EAAYnC,CAAC,EAAG,GAE5E,EAwDI2B,UAAAA,EACAe,YAbJ,SAAqBd,CAAQ,CAAEvC,CAAK,CAAEC,CAAc,EAChD,OAAOqC,EAAUxC,EAAYyC,EAAUvC,EAAOC,GAClD,CAYA,EAiBM,CAAEqD,MAAOC,CAAK,CAAE,CAAI5D,IAEpB,CAAE6D,eAAgBC,CAAqB,CAAE,CAAIhE,IAE7C,CAAEK,YAAa4D,CAAmB,CAAEL,YAAaM,CAAmB,CAAE,CAAGf,EAEzE,CAAEgB,SAAAA,CAAQ,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAEjE,KAAMkE,CAAY,CAAEC,KAAAA,CAAI,CAAE,CAAIvE,KAOhE,AAAC,SAAU5B,CAAO,EA8Od,SAASoG,EAAYC,CAAC,EACd,IAAI,CAACC,IAAI,IACLD,AAAmB,YAAnBA,EAAE9D,OAAO,CAACgE,IAAI,EACdF,CAAAA,EAAE9D,OAAO,CAACgE,IAAI,CAAG,WAAU,CAGvC,CAIA,SAASC,IACL,GAAI,IAAI,CAACC,OAAO,EACZ,IAAI,CAACH,IAAI,GAAI,CACb,IAAoBI,EAAWvE,AAAjB,IAAI,CAAmBuE,QAAQ,CAAEpE,EAAYH,AAA7C,IAAI,CAA+CI,OAAO,CAACJ,KAAK,CAACG,SAAS,CAAEqE,EAAQxE,AAApF,IAAI,CAAsFsE,OAAO,CAACG,UAAU,GAAIC,EAAK1E,AAArH,IAAI,CAAuH2B,QAAQ,CAAEgD,EAAK3E,AAA1I,IAAI,CAA4I2B,QAAQ,CAAG3B,AAA3J,IAAI,CAA6JQ,SAAS,CAAEoE,EAAK5E,AAAjL,IAAI,CAAmL4B,OAAO,CAAEiD,EAAK7E,AAArM,IAAI,CAAuM4B,OAAO,CAAG5B,AAArN,IAAI,CAAuNU,UAAU,CAAUoE,EAAK3E,EAAUS,KAAK,CAAEmE,EAAML,EAAMF,CAAAA,EAAMQ,IAAI,CAACC,OAAO,CAAGT,EAAMQ,IAAI,CAACE,IAAI,CAAG,CAAA,EAAIC,EAAMR,EAAMH,CAAAA,EAAMY,KAAK,CAACH,OAAO,CAAGT,EAAMY,KAAK,CAACF,IAAI,CAAG,CAAA,EAAIG,EAAMT,EAAMJ,CAAAA,EAAMc,GAAG,CAACL,OAAO,CAAGT,EAAMc,GAAG,CAACJ,IAAI,CAAG,CAAA,EAAIK,EAAMV,EAAML,CAAAA,EAAMgB,MAAM,CAACP,OAAO,CAAGT,EAAMgB,MAAM,CAACN,IAAI,CAAG,CAAA,EAAIO,EAAMC,AAA/P,EAAqQlB,CAAAA,EAAMmB,KAAK,CAACV,OAAO,CAAGT,EAAMmB,KAAK,CAACT,IAAI,CAAG,CAAA,EAAIU,EAAMd,EAAMN,CAAAA,EAAMqB,IAAI,CAACZ,OAAO,CAAGT,EAAMqB,IAAI,CAACX,IAAI,CAAG,CAAA,EAAIY,EAAO9F,AAA5lB,IAAI,CAA8lB+F,WAAW,CAAG,UAAY,MAC1oB/F,CADc,IAAI,CACZsE,OAAO,CAAC0B,OAAO,CAAGxB,EACpB,AAACxE,AAFS,IAAI,CAEPiG,WAAW,EAClBjG,CAAAA,AAHU,IAAI,CAGRiG,WAAW,CAAG,CAChBT,OAAQjB,EAAS2B,UAAU,GAAGC,GAAG,GACjCb,IAAKf,EAAS2B,UAAU,GAAGC,GAAG,GAC9BnB,KAAMT,EAAS2B,UAAU,GAAGC,GAAG,GAC/Bf,MAAOb,EAAS2B,UAAU,GAAGC,GAAG,GAChCN,KAAMtB,EAAS2B,UAAU,GAAGC,GAAG,GAC/BR,MAAOpB,EAAS2B,UAAU,GAAGC,GAAG,EACpC,CAAA,EAEJnG,AAZc,IAAI,CAYZiG,WAAW,CAACT,MAAM,CAACM,EAAK,CAAC,CAC3B,MAAS,iDACTM,OAAQ5B,EAAMgB,MAAM,CAACa,WAAW,CAAG,KAAQ,IAC3CC,MAAO,CAAC,CACAC,KAAMhD,EAAMiB,EAAMgB,MAAM,CAACjC,KAAK,EAAEiD,QAAQ,CAAC,IAAKxH,GAAG,GACjDuD,SAAU,CAAC,CACHhC,EAAGwE,EACHtE,EAAG8E,EACH5E,EAAG8E,CACP,EAAG,CACClF,EAAG4E,EACH1E,EAAG8E,EACH5E,EAAG8E,CACP,EAAG,CACClF,EAAG4E,EACH1E,EAAG8E,EACH5E,EAAGiF,CACP,EAAG,CACCrF,EAAGwE,EACHtE,EAAG8E,EACH5E,EAAGiF,CACP,EAAE,CACNa,QAASjC,EAAMgB,MAAM,CAACP,OAAO,AACjC,EACA,CACIsB,KAAMhD,EAAMiB,EAAMgB,MAAM,CAACjC,KAAK,EAAEiD,QAAQ,CAAC,IAAKxH,GAAG,GACjDuD,SAAU,CAAC,CACHhC,EAAGmE,EACHjE,EAAGoE,EACHlE,EAAGmE,CACP,EAAG,CACCvE,EAAGoE,EACHlE,EAAGoE,EACHlE,EAAGmE,CACP,EAAG,CACCvE,EAAGoE,EACHlE,EAAGoE,EACHlE,EAjDsO,CAkD1O,EAAG,CACCJ,EAAGmE,EACHjE,EAAGoE,EACHlE,EArDsO,CAsD1O,EAAE,CACN8F,QAASjC,EAAMgB,MAAM,CAACP,OAAO,AACjC,EACA,CACIsB,KAAMhD,EAAMiB,EAAMgB,MAAM,CAACjC,KAAK,EAAEiD,QAAQ,CAAC,KAAMxH,GAAG,GAClDuD,SAAU,CAAC,CACHhC,EAAGwE,EACHtE,EAAG8E,EACH5E,EAAG8E,CACP,EAAG,CACClF,EAAGwE,EACHtE,EAAG8E,EACH5E,EAAGiF,CACP,EAAG,CACCrF,EAAGmE,EACHjE,EAAGoE,EACHlE,EAAGmE,CACP,EAAG,CACCvE,EAAGmE,EACHjE,EAAGoE,EACHlE,EA1EsO,CA2E1O,EAAE,CACN8F,QAASjC,EAAMgB,MAAM,CAACP,OAAO,EAAI,CAACT,EAAMQ,IAAI,CAACC,OAAO,AACxD,EACA,CACIsB,KAAMhD,EAAMiB,EAAMgB,MAAM,CAACjC,KAAK,EAAEiD,QAAQ,CAAC,KAAMxH,GAAG,GAClDuD,SAAU,CAAC,CACHhC,EAAG4E,EACH1E,EAAG8E,EACH5E,EAAGiF,CACP,EAAG,CACCrF,EAAG4E,EACH1E,EAAG8E,EACH5E,EAAG8E,CACP,EAAG,CACClF,EAAGoE,EACHlE,EAAGoE,EACHlE,EA3FsO,CA4F1O,EAAG,CACCJ,EAAGoE,EACHlE,EAAGoE,EACHlE,EAAGmE,CACP,EAAE,CACN2B,QAASjC,EAAMgB,MAAM,CAACP,OAAO,EAAI,CAACT,EAAMY,KAAK,CAACH,OAAO,AACzD,EACA,CACIsB,KAAMhD,EAAMiB,EAAMgB,MAAM,CAACjC,KAAK,EAAEvE,GAAG,GACnCuD,SAAU,CAAC,CACHhC,EAAG4E,EACH1E,EAAG8E,EACH5E,EAAG8E,CACP,EAAG,CACClF,EAAGwE,EACHtE,EAAG8E,EACH5E,EAAG8E,CACP,EAAG,CACClF,EAAGmE,EACHjE,EAAGoE,EACHlE,EAhHsO,CAiH1O,EAAG,CACCJ,EAAGoE,EACHlE,EAAGoE,EACHlE,EApHsO,CAqH1O,EAAE,CACN8F,QAASjC,EAAMgB,MAAM,CAACP,OAAO,EAAI,CAACT,EAAMmB,KAAK,CAACV,OAAO,AACzD,EACA,CACIsB,KAAMhD,EAAMiB,EAAMgB,MAAM,CAACjC,KAAK,EAAEvE,GAAG,GACnCuD,SAAU,CAAC,CACHhC,EAAGwE,EACHtE,EAAG8E,EACH5E,EAAGiF,CACP,EAAG,CACCrF,EAAG4E,EACH1E,EAAG8E,EACH5E,EAAGiF,CACP,EAAG,CACCrF,EAAGoE,EACHlE,EAAGoE,EACHlE,EAAGmE,CACP,EAAG,CACCvE,EAAGmE,EACHjE,EAAGoE,EACHlE,EAAGmE,CACP,EAAE,CACN2B,QAASjC,EAAMgB,MAAM,CAACP,OAAO,EAAI,CAACT,EAAMqB,IAAI,CAACZ,OAAO,AACxD,EAAE,AACV,GACAjF,AA9Ic,IAAI,CA8IZiG,WAAW,CAACX,GAAG,CAACQ,EAAK,CAAC,CACxB,MAAS,8CACTM,OAAQ5B,EAAMc,GAAG,CAACe,WAAW,CAAG,KAAQ,IACxCC,MAAO,CAAC,CACAC,KAAMhD,EAAMiB,EAAMc,GAAG,CAAC/B,KAAK,EAAEiD,QAAQ,CAAC,IAAKxH,GAAG,GAC9CuD,SAAU,CAAC,CACHhC,EAAGwE,EACHtE,EAAG4E,EACH1E,EAAGiF,CACP,EAAG,CACCrF,EAAG4E,EACH1E,EAAG4E,EACH1E,EAAGiF,CACP,EAAG,CACCrF,EAAG4E,EACH1E,EAAG4E,EACH1E,EAAG8E,CACP,EAAG,CACClF,EAAGwE,EACHtE,EAAG4E,EACH1E,EAAG8E,CACP,EAAE,CACNgB,QAASjC,EAAMc,GAAG,CAACL,OAAO,AAC9B,EACA,CACIsB,KAAMhD,EAAMiB,EAAMc,GAAG,CAAC/B,KAAK,EAAEiD,QAAQ,CAAC,IAAKxH,GAAG,GAC9CuD,SAAU,CAAC,CACHhC,EAAGmE,EACHjE,EAAGmE,EACHjE,EA3KsO,CA4K1O,EAAG,CACCJ,EAAGoE,EACHlE,EAAGmE,EACHjE,EA/KsO,CAgL1O,EAAG,CACCJ,EAAGoE,EACHlE,EAAGmE,EACHjE,EAAGmE,CACP,EAAG,CACCvE,EAAGmE,EACHjE,EAAGmE,EACHjE,EAAGmE,CACP,EAAE,CACN2B,QAASjC,EAAMc,GAAG,CAACL,OAAO,AAC9B,EACA,CACIsB,KAAMhD,EAAMiB,EAAMc,GAAG,CAAC/B,KAAK,EAAEiD,QAAQ,CAAC,KAAMxH,GAAG,GAC/CuD,SAAU,CAAC,CACHhC,EAAGwE,EACHtE,EAAG4E,EACH1E,EAAGiF,CACP,EAAG,CACCrF,EAAGwE,EACHtE,EAAG4E,EACH1E,EAAG8E,CACP,EAAG,CACClF,EAAGmE,EACHjE,EAAGmE,EACHjE,EAxMsO,CAyM1O,EAAG,CACCJ,EAAGmE,EACHjE,EAAGmE,EACHjE,EAAGmE,CACP,EAAE,CACN2B,QAASjC,EAAMc,GAAG,CAACL,OAAO,EAAI,CAACT,EAAMQ,IAAI,CAACC,OAAO,AACrD,EACA,CACIsB,KAAMhD,EAAMiB,EAAMc,GAAG,CAAC/B,KAAK,EAAEiD,QAAQ,CAAC,KAAMxH,GAAG,GAC/CuD,SAAU,CAAC,CACHhC,EAAG4E,EACH1E,EAAG4E,EACH1E,EAAG8E,CACP,EAAG,CACClF,EAAG4E,EACH1E,EAAG4E,EACH1E,EAAGiF,CACP,EAAG,CACCrF,EAAGoE,EACHlE,EAAGmE,EACHjE,EAAGmE,CACP,EAAG,CACCvE,EAAGoE,EACHlE,EAAGmE,EACHjE,EAjOsO,CAkO1O,EAAE,CACN8F,QAASjC,EAAMc,GAAG,CAACL,OAAO,EAAI,CAACT,EAAMY,KAAK,CAACH,OAAO,AACtD,EACA,CACIsB,KAAMhD,EAAMiB,EAAMc,GAAG,CAAC/B,KAAK,EAAEvE,GAAG,GAChCuD,SAAU,CAAC,CACHhC,EAAGwE,EACHtE,EAAG4E,EACH1E,EAAG8E,CACP,EAAG,CACClF,EAAG4E,EACH1E,EAAG4E,EACH1E,EAAG8E,CACP,EAAG,CACClF,EAAGoE,EACHlE,EAAGmE,EACHjE,EAlPsO,CAmP1O,EAAG,CACCJ,EAAGmE,EACHjE,EAAGmE,EACHjE,EAtPsO,CAuP1O,EAAE,CACN8F,QAASjC,EAAMc,GAAG,CAACL,OAAO,EAAI,CAACT,EAAMmB,KAAK,CAACV,OAAO,AACtD,EACA,CACIsB,KAAMhD,EAAMiB,EAAMc,GAAG,CAAC/B,KAAK,EAAEvE,GAAG,GAChCuD,SAAU,CAAC,CACHhC,EAAG4E,EACH1E,EAAG4E,EACH1E,EAAGiF,CACP,EAAG,CACCrF,EAAGwE,EACHtE,EAAG4E,EACH1E,EAAGiF,CACP,EAAG,CACCrF,EAAGmE,EACHjE,EAAGmE,EACHjE,EAAGmE,CACP,EAAG,CACCvE,EAAGoE,EACHlE,EAAGmE,EACHjE,EAAGmE,CACP,EAAE,CACN2B,QAASjC,EAAMc,GAAG,CAACL,OAAO,EAAI,CAACT,EAAMqB,IAAI,CAACZ,OAAO,AACrD,EAAE,AACV,GACAjF,AAhRc,IAAI,CAgRZiG,WAAW,CAACjB,IAAI,CAACc,EAAK,CAAC,CACzB,MAAS,+CACTM,OAAQ5B,EAAMQ,IAAI,CAACqB,WAAW,CAAG,KAAQ,IACzCC,MAAO,CAAC,CACAC,KAAMhD,EAAMiB,EAAMQ,IAAI,CAACzB,KAAK,EAAEiD,QAAQ,CAAC,IAAKxH,GAAG,GAC/CuD,SAAU,CAAC,CACHhC,EAAGwE,EACHtE,EAAG8E,EACH5E,EAAG8E,CACP,EAAG,CACClF,EAAGmE,EACHjE,EAAGoE,EACHlE,EA5RsO,CA6R1O,EAAG,CACCJ,EAAGmE,EACHjE,EAAGoE,EACHlE,EAAGmE,CACP,EAAG,CACCvE,EAAGwE,EACHtE,EAAG8E,EACH5E,EAAGiF,CACP,EAAE,CACNa,QAASjC,EAAMQ,IAAI,CAACC,OAAO,EAAI,CAACT,EAAMgB,MAAM,CAACP,OAAO,AACxD,EACA,CACIsB,KAAMhD,EAAMiB,EAAMQ,IAAI,CAACzB,KAAK,EAAEiD,QAAQ,CAAC,IAAKxH,GAAG,GAC/CuD,SAAU,CAAC,CACHhC,EAAGwE,EACHtE,EAAG4E,EACH1E,EAAGiF,CACP,EAAG,CACCrF,EAAGmE,EACHjE,EAAGmE,EACHjE,EAAGmE,CACP,EAAG,CACCvE,EAAGmE,EACHjE,EAAGmE,EACHjE,EArTsO,CAsT1O,EAAG,CACCJ,EAAGwE,EACHtE,EAAG4E,EACH1E,EAAG8E,CACP,EAAE,CACNgB,QAASjC,EAAMQ,IAAI,CAACC,OAAO,EAAI,CAACT,EAAMc,GAAG,CAACL,OAAO,AACrD,EACA,CACIsB,KAAMhD,EAAMiB,EAAMQ,IAAI,CAACzB,KAAK,EAAEiD,QAAQ,CAAC,KAAMxH,GAAG,GAChDuD,SAAU,CAAC,CACHhC,EAAGwE,EACHtE,EAAG8E,EACH5E,EAAGiF,CACP,EAAG,CACCrF,EAAGwE,EACHtE,EAAG4E,EACH1E,EAAGiF,CACP,EAAG,CACCrF,EAAGwE,EACHtE,EAAG4E,EACH1E,EAAG8E,CACP,EAAG,CACClF,EAAGwE,EACHtE,EAAG8E,EACH5E,EAAG8E,CACP,EAAE,CACNgB,QAASjC,EAAMQ,IAAI,CAACC,OAAO,AAC/B,EACA,CACIsB,KAAMhD,EAAMiB,EAAMQ,IAAI,CAACzB,KAAK,EAAEiD,QAAQ,CAAC,KAAMxH,GAAG,GAChDuD,SAAU,CAAC,CACHhC,EAAGmE,EACHjE,EAAGmE,EACHjE,EAAGmE,CACP,EAAG,CACCvE,EAAGmE,EACHjE,EAAGoE,EACHlE,EAAGmE,CACP,EAAG,CACCvE,EAAGmE,EACHjE,EAAGoE,EACHlE,EA/VsO,CAgW1O,EAAG,CACCJ,EAAGmE,EACHjE,EAAGmE,EACHjE,EAnWsO,CAoW1O,EAAE,CACN8F,QAASjC,EAAMQ,IAAI,CAACC,OAAO,AAC/B,EACA,CACIsB,KAAMhD,EAAMiB,EAAMQ,IAAI,CAACzB,KAAK,EAAEvE,GAAG,GACjCuD,SAAU,CAAC,CACHhC,EAAGwE,EACHtE,EAAG8E,EACH5E,EAAG8E,CACP,EAAG,CACClF,EAAGwE,EACHtE,EAAG4E,EACH1E,EAAG8E,CACP,EAAG,CACClF,EAAGmE,EACHjE,EAAGmE,EACHjE,EApXsO,CAqX1O,EAAG,CACCJ,EAAGmE,EACHjE,EAAGoE,EACHlE,EAxXsO,CAyX1O,EAAE,CACN8F,QAASjC,EAAMQ,IAAI,CAACC,OAAO,EAAI,CAACT,EAAMmB,KAAK,CAACV,OAAO,AACvD,EACA,CACIsB,KAAMhD,EAAMiB,EAAMQ,IAAI,CAACzB,KAAK,EAAEvE,GAAG,GACjCuD,SAAU,CAAC,CACHhC,EAAGwE,EACHtE,EAAG4E,EACH1E,EAAGiF,CACP,EAAG,CACCrF,EAAGwE,EACHtE,EAAG8E,EACH5E,EAAGiF,CACP,EAAG,CACCrF,EAAGmE,EACHjE,EAAGoE,EACHlE,EAAGmE,CACP,EAAG,CACCvE,EAAGmE,EACHjE,EAAGmE,EACHjE,EAAGmE,CACP,EAAE,CACN2B,QAASjC,EAAMQ,IAAI,CAACC,OAAO,EAAI,CAACT,EAAMqB,IAAI,CAACZ,OAAO,AACtD,EAAE,AACV,GACAjF,AAlZc,IAAI,CAkZZiG,WAAW,CAACb,KAAK,CAACU,EAAK,CAAC,CAC1B,MAAS,gDACTM,OAAQ5B,EAAMY,KAAK,CAACiB,WAAW,CAAG,KAAQ,IAC1CC,MAAO,CAAC,CACAC,KAAMhD,EAAMiB,EAAMY,KAAK,CAAC7B,KAAK,EAAEiD,QAAQ,CAAC,IAAKxH,GAAG,GAChDuD,SAAU,CAAC,CACHhC,EAAG4E,EACH1E,EAAG8E,EACH5E,EAAGiF,CACP,EAAG,CACCrF,EAAGoE,EACHlE,EAAGoE,EACHlE,EAAGmE,CACP,EAAG,CACCvE,EAAGoE,EACHlE,EAAGoE,EACHlE,EAlasO,CAma1O,EAAG,CACCJ,EAAG4E,EACH1E,EAAG8E,EACH5E,EAAG8E,CACP,EAAE,CACNgB,QAASjC,EAAMY,KAAK,CAACH,OAAO,EAAI,CAACT,EAAMgB,MAAM,CAACP,OAAO,AACzD,EACA,CACIsB,KAAMhD,EAAMiB,EAAMY,KAAK,CAAC7B,KAAK,EAAEiD,QAAQ,CAAC,IAAKxH,GAAG,GAChDuD,SAAU,CAAC,CACHhC,EAAG4E,EACH1E,EAAG4E,EACH1E,EAAG8E,CACP,EAAG,CACClF,EAAGoE,EACHlE,EAAGmE,EACHjE,EAnbsO,CAob1O,EAAG,CACCJ,EAAGoE,EACHlE,EAAGmE,EACHjE,EAAGmE,CACP,EAAG,CACCvE,EAAG4E,EACH1E,EAAG4E,EACH1E,EAAGiF,CACP,EAAE,CACNa,QAASjC,EAAMY,KAAK,CAACH,OAAO,EAAI,CAACT,EAAMc,GAAG,CAACL,OAAO,AACtD,EACA,CACIsB,KAAMhD,EAAMiB,EAAMY,KAAK,CAAC7B,KAAK,EAAEiD,QAAQ,CAAC,KAAMxH,GAAG,GACjDuD,SAAU,CAAC,CACHhC,EAAGoE,EACHlE,EAAGmE,EACHjE,EApcsO,CAqc1O,EAAG,CACCJ,EAAGoE,EACHlE,EAAGoE,EACHlE,EAxcsO,CAyc1O,EAAG,CACCJ,EAAGoE,EACHlE,EAAGoE,EACHlE,EAAGmE,CACP,EAAG,CACCvE,EAAGoE,EACHlE,EAAGmE,EACHjE,EAAGmE,CACP,EAAE,CACN2B,QAASjC,EAAMY,KAAK,CAACH,OAAO,AAChC,EACA,CACIsB,KAAMhD,EAAMiB,EAAMY,KAAK,CAAC7B,KAAK,EAAEiD,QAAQ,CAAC,KAAMxH,GAAG,GACjDuD,SAAU,CAAC,CACHhC,EAAG4E,EACH1E,EAAG8E,EACH5E,EAAG8E,CACP,EAAG,CACClF,EAAG4E,EACH1E,EAAG4E,EACH1E,EAAG8E,CACP,EAAG,CACClF,EAAG4E,EACH1E,EAAG4E,EACH1E,EAAGiF,CACP,EAAG,CACCrF,EAAG4E,EACH1E,EAAG8E,EACH5E,EAAGiF,CACP,EAAE,CACNa,QAASjC,EAAMY,KAAK,CAACH,OAAO,AAChC,EACA,CACIsB,KAAMhD,EAAMiB,EAAMY,KAAK,CAAC7B,KAAK,EAAEvE,GAAG,GAClCuD,SAAU,CAAC,CACHhC,EAAG4E,EACH1E,EAAG4E,EACH1E,EAAG8E,CACP,EAAG,CACClF,EAAG4E,EACH1E,EAAG8E,EACH5E,EAAG8E,CACP,EAAG,CACClF,EAAGoE,EACHlE,EAAGoE,EACHlE,EAtfsO,CAuf1O,EAAG,CACCJ,EAAGoE,EACHlE,EAAGmE,EACHjE,EA1fsO,CA2f1O,EAAE,CACN8F,QAASjC,EAAMY,KAAK,CAACH,OAAO,EAAI,CAACT,EAAMmB,KAAK,CAACV,OAAO,AACxD,EACA,CACIsB,KAAMhD,EAAMiB,EAAMY,KAAK,CAAC7B,KAAK,EAAEvE,GAAG,GAClCuD,SAAU,CAAC,CACHhC,EAAG4E,EACH1E,EAAG8E,EACH5E,EAAGiF,CACP,EAAG,CACCrF,EAAG4E,EACH1E,EAAG4E,EACH1E,EAAGiF,CACP,EAAG,CACCrF,EAAGoE,EACHlE,EAAGmE,EACHjE,EAAGmE,CACP,EAAG,CACCvE,EAAGoE,EACHlE,EAAGoE,EACHlE,EAAGmE,CACP,EAAE,CACN2B,QAASjC,EAAMY,KAAK,CAACH,OAAO,EAAI,CAACT,EAAMqB,IAAI,CAACZ,OAAO,AACvD,EAAE,AACV,GACAjF,AAphBc,IAAI,CAohBZiG,WAAW,CAACJ,IAAI,CAACC,EAAK,CAAC,CACzB,MAAS,+CACTM,OAAQ5B,EAAMqB,IAAI,CAACQ,WAAW,CAAG,KAAQ,IACzCC,MAAO,CAAC,CACAC,KAAMhD,EAAMiB,EAAMqB,IAAI,CAACtC,KAAK,EAAEiD,QAAQ,CAAC,IAAKxH,GAAG,GAC/CuD,SAAU,CAAC,CACHhC,EAAG4E,EACH1E,EAAG8E,EACH5E,EAAGiF,CACP,EAAG,CACCrF,EAAGwE,EACHtE,EAAG8E,EACH5E,EAAGiF,CACP,EAAG,CACCrF,EAAGmE,EACHjE,EAAGoE,EACHlE,EAAGmE,CACP,EAAG,CACCvE,EAAGoE,EACHlE,EAAGoE,EACHlE,EAAGmE,CACP,EAAE,CACN2B,QAASjC,EAAMqB,IAAI,CAACZ,OAAO,EAAI,CAACT,EAAMgB,MAAM,CAACP,OAAO,AACxD,EACA,CACIsB,KAAMhD,EAAMiB,EAAMqB,IAAI,CAACtC,KAAK,EAAEiD,QAAQ,CAAC,IAAKxH,GAAG,GAC/CuD,SAAU,CAAC,CACHhC,EAAGwE,EACHtE,EAAG4E,EACH1E,EAAGiF,CACP,EAAG,CACCrF,EAAG4E,EACH1E,EAAG4E,EACH1E,EAAGiF,CACP,EAAG,CACCrF,EAAGoE,EACHlE,EAAGmE,EACHjE,EAAGmE,CACP,EAAG,CACCvE,EAAGmE,EACHjE,EAAGmE,EACHjE,EAAGmE,CACP,EAAE,CACN2B,QAASjC,EAAMqB,IAAI,CAACZ,OAAO,EAAI,CAACT,EAAMc,GAAG,CAACL,OAAO,AACrD,EACA,CACIsB,KAAMhD,EAAMiB,EAAMqB,IAAI,CAACtC,KAAK,EAAEiD,QAAQ,CAAC,KAAMxH,GAAG,GAChDuD,SAAU,CAAC,CACHhC,EAAGwE,EACHtE,EAAG8E,EACH5E,EAAGiF,CACP,EAAG,CACCrF,EAAGwE,EACHtE,EAAG4E,EACH1E,EAAGiF,CACP,EAAG,CACCrF,EAAGmE,EACHjE,EAAGmE,EACHjE,EAAGmE,CACP,EAAG,CACCvE,EAAGmE,EACHjE,EAAGoE,EACHlE,EAAGmE,CACP,EAAE,CACN2B,QAASjC,EAAMqB,IAAI,CAACZ,OAAO,EAAI,CAACT,EAAMQ,IAAI,CAACC,OAAO,AACtD,EACA,CACIsB,KAAMhD,EAAMiB,EAAMqB,IAAI,CAACtC,KAAK,EAAEiD,QAAQ,CAAC,KAAMxH,GAAG,GAChDuD,SAAU,CAAC,CACHhC,EAAG4E,EACH1E,EAAG4E,EACH1E,EAAGiF,CACP,EAAG,CACCrF,EAAG4E,EACH1E,EAAG8E,EACH5E,EAAGiF,CACP,EAAG,CACCrF,EAAGoE,EACHlE,EAAGoE,EACHlE,EAAGmE,CACP,EAAG,CACCvE,EAAGoE,EACHlE,EAAGmE,EACHjE,EAAGmE,CACP,EAAE,CACN2B,QAASjC,EAAMqB,IAAI,CAACZ,OAAO,EAAI,CAACT,EAAMY,KAAK,CAACH,OAAO,AACvD,EACA,CACIsB,KAAMhD,EAAMiB,EAAMqB,IAAI,CAACtC,KAAK,EAAEvE,GAAG,GACjCuD,SAAU,CAAC,CACHhC,EAAGmE,EACHjE,EAAGmE,EACHjE,EAAGmE,CACP,EAAG,CACCvE,EAAGoE,EACHlE,EAAGmE,EACHjE,EAAGmE,CACP,EAAG,CACCvE,EAAGoE,EACHlE,EAAGoE,EACHlE,EAAGmE,CACP,EAAG,CACCvE,EAAGmE,EACHjE,EAAGoE,EACHlE,EAAGmE,CACP,EAAE,CACN2B,QAASjC,EAAMqB,IAAI,CAACZ,OAAO,AAC/B,EACA,CACIsB,KAAMhD,EAAMiB,EAAMqB,IAAI,CAACtC,KAAK,EAAEvE,GAAG,GACjCuD,SAAU,CAAC,CACHhC,EAAGwE,EACHtE,EAAG8E,EACH5E,EAAGiF,CACP,EAAG,CACCrF,EAAG4E,EACH1E,EAAG8E,EACH5E,EAAGiF,CACP,EAAG,CACCrF,EAAG4E,EACH1E,EAAG4E,EACH1E,EAAGiF,CACP,EAAG,CACCrF,EAAGwE,EACHtE,EAAG4E,EACH1E,EAAGiF,CACP,EAAE,CACNa,QAASjC,EAAMqB,IAAI,CAACZ,OAAO,AAC/B,EAAE,AACV,GACAjF,AAtpBc,IAAI,CAspBZiG,WAAW,CAACN,KAAK,CAACG,EAAK,CAAC,CAC1B,MAAS,gDACTM,OAAQ5B,EAAMmB,KAAK,CAACU,WAAW,CAAG,KAAQ,IAC1CC,MAAO,CAAC,CACAC,KAAMhD,EAAMiB,EAAMmB,KAAK,CAACpC,KAAK,EAAEiD,QAAQ,CAAC,IAAKxH,GAAG,GAChDuD,SAAU,CAAC,CACHhC,EAAGwE,EACHtE,EAAG8E,EACH5E,EAAG8E,CACP,EAAG,CACClF,EAAG4E,EACH1E,EAAG8E,EACH5E,EAAG8E,CACP,EAAG,CACClF,EAAGoE,EACHlE,EAAGoE,EACHlE,EAtqBsO,CAuqB1O,EAAG,CACCJ,EAAGmE,EACHjE,EAAGoE,EACHlE,EA1qBsO,CA2qB1O,EAAE,CACN8F,QAASjC,EAAMmB,KAAK,CAACV,OAAO,EAAI,CAACT,EAAMgB,MAAM,CAACP,OAAO,AACzD,EACA,CACIsB,KAAMhD,EAAMiB,EAAMmB,KAAK,CAACpC,KAAK,EAAEiD,QAAQ,CAAC,IAAKxH,GAAG,GAChDuD,SAAU,CAAC,CACHhC,EAAG4E,EACH1E,EAAG4E,EACH1E,EAAG8E,CACP,EAAG,CACClF,EAAGwE,EACHtE,EAAG4E,EACH1E,EAAG8E,CACP,EAAG,CACClF,EAAGmE,EACHjE,EAAGmE,EACHjE,EA3rBsO,CA4rB1O,EAAG,CACCJ,EAAGoE,EACHlE,EAAGmE,EACHjE,EA/rBsO,CAgsB1O,EAAE,CACN8F,QAASjC,EAAMmB,KAAK,CAACV,OAAO,EAAI,CAACT,EAAMc,GAAG,CAACL,OAAO,AACtD,EACA,CACIsB,KAAMhD,EAAMiB,EAAMmB,KAAK,CAACpC,KAAK,EAAEiD,QAAQ,CAAC,KAAMxH,GAAG,GACjDuD,SAAU,CAAC,CACHhC,EAAGwE,EACHtE,EAAG4E,EACH1E,EAAG8E,CACP,EAAG,CACClF,EAAGwE,EACHtE,EAAG8E,EACH5E,EAAG8E,CACP,EAAG,CACClF,EAAGmE,EACHjE,EAAGoE,EACHlE,EAhtBsO,CAitB1O,EAAG,CACCJ,EAAGmE,EACHjE,EAAGmE,EACHjE,EAptBsO,CAqtB1O,EAAE,CACN8F,QAASjC,EAAMmB,KAAK,CAACV,OAAO,EAAI,CAACT,EAAMQ,IAAI,CAACC,OAAO,AACvD,EACA,CACIsB,KAAMhD,EAAMiB,EAAMmB,KAAK,CAACpC,KAAK,EAAEiD,QAAQ,CAAC,KAAMxH,GAAG,GACjDuD,SAAU,CAAC,CACHhC,EAAG4E,EACH1E,EAAG8E,EACH5E,EAAG8E,CACP,EAAG,CACClF,EAAG4E,EACH1E,EAAG4E,EACH1E,EAAG8E,CACP,EAAG,CACClF,EAAGoE,EACHlE,EAAGmE,EACHjE,EAruBsO,CAsuB1O,EAAG,CACCJ,EAAGoE,EACHlE,EAAGoE,EACHlE,EAzuBsO,CA0uB1O,EAAE,CACN8F,QAASjC,EAAMmB,KAAK,CAACV,OAAO,EAAI,CAACT,EAAMY,KAAK,CAACH,OAAO,AACxD,EACA,CACIsB,KAAMhD,EAAMiB,EAAMmB,KAAK,CAACpC,KAAK,EAAEvE,GAAG,GAClCuD,SAAU,CAAC,CACHhC,EAAGoE,EACHlE,EAAGmE,EACHjE,EAlvBsO,CAmvB1O,EAAG,CACCJ,EAAGmE,EACHjE,EAAGmE,EACHjE,EAtvBsO,CAuvB1O,EAAG,CACCJ,EAAGmE,EACHjE,EAAGoE,EACHlE,EA1vBsO,CA2vB1O,EAAG,CACCJ,EAAGoE,EACHlE,EAAGoE,EACHlE,EA9vBsO,CA+vB1O,EAAE,CACN8F,QAASjC,EAAMmB,KAAK,CAACV,OAAO,AAChC,EACA,CACIsB,KAAMhD,EAAMiB,EAAMmB,KAAK,CAACpC,KAAK,EAAEvE,GAAG,GAClCuD,SAAU,CAAC,CACHhC,EAAG4E,EACH1E,EAAG8E,EACH5E,EAAG8E,CACP,EAAG,CACClF,EAAGwE,EACHtE,EAAG8E,EACH5E,EAAG8E,CACP,EAAG,CACClF,EAAGwE,EACHtE,EAAG4E,EACH1E,EAAG8E,CACP,EAAG,CACClF,EAAG4E,EACH1E,EAAG4E,EACH1E,EAAG8E,CACP,EAAE,CACNgB,QAASjC,EAAMmB,KAAK,CAACV,OAAO,AAChC,EAAE,AACV,EACJ,CACJ,CAKA,SAASyB,IACD,IAAI,CAACC,UAAU,EAEf,CAAC,CACOC,KAAM,SACNC,MAAO,EACX,EAAG,CACCD,KAAM,WACNC,MAAO,GACX,EAAE,CAACC,OAAO,CAAC,SAAUC,CAAG,EACxB,IAAI,CAACxC,QAAQ,CAAC7F,UAAU,CAAC,CACrBsI,QAAS,SACTC,WAAY,CACRC,GAAI,cAAgBH,EAAIH,IAAI,AAChC,EACAO,SAAU,CAAC,CACHH,QAAS,sBACTG,SAAU,CAAC,CACHH,QAAS,UACTC,WAAY,CACR7C,KAAM,SACNyC,MAAOE,EAAIF,KAAK,AACpB,CACJ,EAAG,CACCG,QAAS,UACTC,WAAY,CACR7C,KAAM,SACNyC,MAAOE,EAAIF,KAAK,AACpB,CACJ,EAAG,CACCG,QAAS,UACTC,WAAY,CACR7C,KAAM,SACNyC,MAAOE,EAAIF,KAAK,AACpB,CACJ,EAAE,AACV,EAAE,AACV,EACJ,EAAG,IAAI,CAEf,CAMA,SAASO,IACL,IAAMhH,EAAU,IAAI,CAACA,OAAO,AACxB,CAAA,IAAI,CAAC+D,IAAI,IACT,AAAC/D,CAAAA,EAAQiH,MAAM,EAAI,EAAE,AAAD,EAAGP,OAAO,CAAC,SAAUQ,CAAC,EAIlClD,AAAS,YAHCkD,CAAAA,EAAElD,IAAI,EAChBhE,EAAQJ,KAAK,CAACoE,IAAI,EAClBhE,EAAQJ,KAAK,CAACuH,iBAAiB,AAAD,GAE9BD,CAAAA,EAAElD,IAAI,CAAG,WAAU,CAE3B,EAER,CAIA,SAASoD,IACL,IAAoBrH,EAAYH,AAAlB,IAAI,CAAoBI,OAAO,CAACJ,KAAK,CAACG,SAAS,CAC7D,GAAIH,AADU,IAAI,CACRsE,OAAO,EACbtE,AAFU,IAAI,CAERmE,IAAI,GAAI,CAEVhE,IACAA,EAAUe,KAAK,CAAGf,EAAUe,KAAK,CAAG,IAC/Bf,CAAAA,EAAUe,KAAK,EAAI,EAAI,EAAI,GAAE,EAClCf,EAAUc,IAAI,CAAGd,EAAUc,IAAI,CAAG,IAC7Bd,CAAAA,EAAUc,IAAI,EAAI,EAAI,EAAI,GAAE,GAErC,IAAMZ,EAAWL,AAVP,IAAI,CAUSK,QAAQ,CAAEoH,EAAUzH,AAVjC,IAAI,CAUmCyH,OAAO,CAAEC,EAAS1H,AAVzD,IAAI,CAU2D0H,MAAM,AAC/ED,CAAAA,CAAO,CAD8EpH,EAAW,IAAM,IAC5F,CAAG,CAAEqH,CAAAA,CAAM,CAAC,EAAE,EAAI,CAAA,EAC5BD,CAAO,CAFwGpH,EAAW,IAAM,IAEtH,CAAG,CAAEqH,CAAAA,CAAM,CAAC,EAAE,EAAI,CAAA,EAC5BD,CAAO,CAHkIpH,EAAW,SAAW,QAGrJ,CAAIL,AAbJ,IAAI,CAaM2H,UAAU,CAAID,CAAAA,CAAM,CAAC,EAAE,EAAI,CAAA,EAAMA,CAAAA,CAAM,CAAC,EAAE,EAAI,CAAA,EAClED,CAAO,CAJqKpH,EAAW,QAAU,SAIvL,CAAIL,AAdJ,IAAI,CAcM4H,WAAW,CAAIF,CAAAA,CAAM,CAAC,EAAE,EAAI,CAAA,EAAMA,CAAAA,CAAM,CAAC,EAAE,EAAI,CAAA,EAGnE1H,AAjBU,IAAI,CAiBRgB,OAAO,CAAG,EACZb,AAAwB,CAAA,IAAxBA,EAAU0H,SAAS,EACnB7H,CAAAA,AAnBM,IAAI,CAmBJgB,OAAO,CAAGhB,AAnBV,IAAI,CAmBYsE,OAAO,CAACwD,QAAQ,CAAC3H,EAAUS,KAAK,CAAA,EAK1DZ,AAxBU,IAAI,CAwBRsE,OAAO,CAAC0B,OAAO,CAAGhG,AAxBd,IAAI,CAwBgBsE,OAAO,CAACG,UAAU,EACpD,CACJ,CAIA,SAASsD,IACD,IAAI,CAAC5D,IAAI,IAET,CAAA,IAAI,CAAC6D,UAAU,CAAG,CAAA,CAAG,CAE7B,CAIA,SAASC,IACD,IAAI,CAAC3D,OAAO,EAAI,IAAI,CAACH,IAAI,IACzB,CAAA,IAAI,CAACG,OAAO,CAAC0B,OAAO,CAAG,IAAI,CAAC1B,OAAO,CAACG,UAAU,EAAC,CAEvD,CAIA,SAASyD,IACD,AAAC,IAAI,CAAC5D,OAAO,EACb,CAAA,IAAI,CAACA,OAAO,CAAG,IAAI6D,EAAU,IAAI,CAAA,CAEzC,CAIA,SAASC,EAAiBC,CAAO,EAC7B,OAAO,IAAI,CAAClE,IAAI,IAAMkE,EAAQC,KAAK,CAAC,IAAI,CAAE,EAAE,CAACC,KAAK,CAAClJ,IAAI,CAACmJ,UAAW,GACvE,CAKA,SAASC,EAAiBJ,CAAO,EAC7B,IAAIhB,EAAQ5E,EAAI,IAAI,CAAC4E,MAAM,CAAC1E,MAAM,CAClC,GAAI,IAAI,CAACwB,IAAI,GACT,KAAO1B,KAEH4E,AADAA,CAAAA,EAAS,IAAI,CAACA,MAAM,CAAC5E,EAAE,AAAD,EACfiG,SAAS,GAChBrB,EAAOsB,MAAM,QAIjBN,EAAQhJ,IAAI,CAAC,IAAI,CAEzB,CAIA,SAASuJ,EAAiBP,CAAO,EAC7BA,EAAQC,KAAK,CAAC,IAAI,CAAE,EAAE,CAACC,KAAK,CAAClJ,IAAI,CAACmJ,UAAW,IACzC,IAAI,CAACrE,IAAI,IACT,CAAA,IAAI,CAAC0E,SAAS,CAACC,SAAS,EAAI,sBAAqB,CAEzD,CA5pCAjL,EAAQ2F,cAAc,CAAG,CACrBxD,MAAO,CAUHG,UAAW,CAOPsG,QAAS,CAAA,EAOTvF,MAAO,EAOPD,KAAM,EAONL,MAAO,IAQPiH,UAAW,CAAA,EAUX/G,aAAc,GASdiI,kBAAmB,KASnBvE,MAAO,CAIHS,QAAS,UAITC,KAAM,EA4CNM,OAAQ,CAAC,EAMTF,IAAK,CAAC,EAMNN,KAAM,CAAC,EAMPI,MAAO,CAAC,EAMRS,KAAM,CAAC,EAMPF,MAAO,CAAC,CACZ,CACJ,CACJ,CACJ,EAwDA9H,EAAQmL,OAAO,CA/Cf,SAAiBC,CAAU,CAAEC,CAAO,EAChC,IAAMC,EAAaF,EAAW9J,SAAS,CACjCiK,EAAUF,EAAQ/J,SAAS,AAOjCgK,CAAAA,EAAWhF,IAAI,CAAG,WACd,MAAO,CAAC,CAAC,IAAI,CAAC/D,OAAO,CAACJ,KAAK,CAACG,SAAS,EAAEsG,OAC3C,EACA0C,EAAWE,oBAAoB,CAACC,IAAI,CAAC,mBACrCH,EAAWI,wBAAwB,CAACD,IAAI,CAAC,mBAKzCF,EAAQI,YAAY,CAAG,WACnB,IAAIC,EACJ,GAAI,IAAI,CAACC,GAAG,CAAG,GACV7F,CAAAA,EAAQ,IAAI,CAAC8F,KAAK,GAAK9F,EAAQ,IAAI,CAAC+F,GAAG,CAAA,EAAI,CAC5C,IAAMD,EAAS,IAAI,CAACA,KAAK,EACrB,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAE,CAAGC,EAAM,IAAI,CAACA,GAAG,EAAI,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAE,CAC7DH,EAAe,EAAE,CACjB,IAAK,IAAIhH,EAAI,EAAGA,EAAI,EAAGA,IACnBgH,EAAaH,IAAI,CAAC,IAAI,CAACI,GAAG,CAAGE,CAAG,CAACnH,EAAE,CAAG,AAAC,CAAA,EAAI,IAAI,CAACiH,GAAG,AAAD,EAAKC,CAAK,CAAClH,EAAE,CAEvE,MAEIgH,EAAe,IAAI,CAACG,GAAG,CAE3B,IAAI,CAACC,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC5K,IAAI,CAAEuK,EAAc,KAAM,CAAA,EAClD,EACA3F,EAAM,CAAA,EAAML,EAAuB5F,EAAQ2F,cAAc,EACzDI,EAASqF,EAAY,OAAQf,GAC7BtE,EAASqF,EAAY,YAAahF,GAClCL,EAASqF,EAAY,oBAAqB5E,GAC1CT,EAASqF,EAAY,oBAAqBvC,GAC1C9C,EAASqF,EAAY,YAAa7B,GAClCxD,EAASqF,EAAY,oBAAqBzB,GAC1C5D,EAASqF,EAAY,eAAgBlB,GACrCnE,EAASqF,EAAY,eAAgBhB,GACrCjE,EAAKmF,EAAY,eAAgBf,GACjCpE,EAAKmF,EAAY,eAAgBV,GACjCzE,EAAKmF,EAAY,eAAgBP,EACrC,CA08BA,OAAMT,EAMF4B,YAAY/J,CAAK,CAAE,CACf,IAAI,CAACA,KAAK,CAAGA,CACjB,CAMAyE,YAAa,CACT,IAAMzE,EAAQ,IAAI,CAACA,KAAK,CAAEG,EAAYH,EAAMI,OAAO,CAACJ,KAAK,CAACG,SAAS,CAAE6J,EAAe7J,EAAUqE,KAAK,CAAEE,EAAK1E,EAAM2B,QAAQ,CAAEgD,EAAK3E,EAAM2B,QAAQ,CAAG3B,EAAMQ,SAAS,CAAEoE,EAAK5E,EAAM4B,OAAO,CAAEiD,EAAK7E,EAAM4B,OAAO,CAAG5B,EAAMU,UAAU,CAAUoE,EAAK3E,EAAUS,KAAK,CAAEqJ,EAAkB,SAAU1H,CAAQ,EAC1R,IAAMC,EAAOmB,EAAoBpB,EAAUvC,UAE3C,AAAIwC,EAAO,GACA,EAEPA,EAAO,IACA,GAEJ,CACX,EAAG0H,EAAoBD,EAAgB,CACnC,CAAE1J,EAAGmE,EAAIjE,EAAGoE,EAAIlE,EAAGmE,CAAG,EACtB,CAAEvE,EAAGoE,EAAIlE,EAAGoE,EAAIlE,EAAGmE,CAAG,EACtB,CAAEvE,EAAGoE,EAAIlE,EAAGoE,EAAIlE,EAb6M,CAavM,EACtB,CAAEJ,EAAGmE,EAAIjE,EAAGoE,EAAIlE,EAd6M,CAcvM,EACzB,EAAGwJ,EAAiBF,EAAgB,CACjC,CAAE1J,EAAGmE,EAAIjE,EAAGmE,EAAIjE,EAhB6M,CAgBvM,EACtB,CAAEJ,EAAGoE,EAAIlE,EAAGmE,EAAIjE,EAjB6M,CAiBvM,EACtB,CAAEJ,EAAGoE,EAAIlE,EAAGmE,EAAIjE,EAAGmE,CAAG,EACtB,CAAEvE,EAAGmE,EAAIjE,EAAGmE,EAAIjE,EAAGmE,CAAG,EACzB,EAAGsF,EAAkBH,EAAgB,CAClC,CAAE1J,EAAGmE,EAAIjE,EAAGmE,EAAIjE,EArB6M,CAqBvM,EACtB,CAAEJ,EAAGmE,EAAIjE,EAAGmE,EAAIjE,EAAGmE,CAAG,EACtB,CAAEvE,EAAGmE,EAAIjE,EAAGoE,EAAIlE,EAAGmE,CAAG,EACtB,CAAEvE,EAAGmE,EAAIjE,EAAGoE,EAAIlE,EAxB6M,CAwBvM,EACzB,EAAG0J,EAAmBJ,EAAgB,CACnC,CAAE1J,EAAGoE,EAAIlE,EAAGmE,EAAIjE,EAAGmE,CAAG,EACtB,CAAEvE,EAAGoE,EAAIlE,EAAGmE,EAAIjE,EA3B6M,CA2BvM,EACtB,CAAEJ,EAAGoE,EAAIlE,EAAGoE,EAAIlE,EA5B6M,CA4BvM,EACtB,CAAEJ,EAAGoE,EAAIlE,EAAGoE,EAAIlE,EAAGmE,CAAG,EACzB,EAAGwF,EAAmBL,EAAgB,CACnC,CAAE1J,EAAGmE,EAAIjE,EAAGoE,EAAIlE,EA/B6M,CA+BvM,EACtB,CAAEJ,EAAGoE,EAAIlE,EAAGoE,EAAIlE,EAhC6M,CAgCvM,EACtB,CAAEJ,EAAGoE,EAAIlE,EAAGmE,EAAIjE,EAjC6M,CAiCvM,EACtB,CAAEJ,EAAGmE,EAAIjE,EAAGmE,EAAIjE,EAlC6M,CAkCvM,EACzB,EAAG4J,EAAkBN,EAAgB,CAClC,CAAE1J,EAAGmE,EAAIjE,EAAGmE,EAAIjE,EAAGmE,CAAG,EACtB,CAAEvE,EAAGoE,EAAIlE,EAAGmE,EAAIjE,EAAGmE,CAAG,EACtB,CAAEvE,EAAGoE,EAAIlE,EAAGoE,EAAIlE,EAAGmE,CAAG,EACtB,CAAEvE,EAAGmE,EAAIjE,EAAGoE,EAAIlE,EAAGmE,CAAG,EACzB,EACG0F,EAAoB,CAAA,EAAOC,EAAiB,CAAA,EAAOC,EAAkB,CAAA,EAAOC,EAAmB,CAAA,EAInG,EAAE,CACGC,MAAM,CAAC5K,EAAM6K,KAAK,CAAE7K,EAAM8K,KAAK,CAAE9K,EAAM+K,KAAK,EAC5CjE,OAAO,CAAC,SAAUkE,CAAI,EACnBA,IACIA,EAAKC,KAAK,CACND,EAAKE,QAAQ,CACbT,EAAiB,CAAA,EAGjBD,EAAoB,CAAA,EAIpBQ,EAAKE,QAAQ,CACbP,EAAmB,CAAA,EAGnBD,EAAkB,CAAA,EAIlC,GACA,IAAMS,EAAiB,SAAUC,CAAO,CAAEnB,CAAe,CAAEoB,CAAc,EACrE,IAAMC,EAAY,CAAC,OAAQ,QAAS,UAAU,CAAElL,EAAU,CAAC,EAC3D,IAAK,IAAIqC,EAAI,EAAGA,EAAI6I,EAAU3I,MAAM,CAAEF,IAAK,CACvC,IAAMqH,EAAOwB,CAAS,CAAC7I,EAAE,CACzB,IAAK,IAAIC,EAAI,EAAGA,EAAI0I,EAAQzI,MAAM,CAAED,IAChC,GAAI,AAAsB,UAAtB,OAAO0I,CAAO,CAAC1I,EAAE,CAAe,CAChC,IAAM6I,EAAMH,CAAO,CAAC1I,EAAE,CAACoH,EAAK,CAC5B,GAAI,MAAOyB,EAAqC,CAC5CnL,CAAO,CAAC0J,EAAK,CAAGyB,EAChB,KACJ,CACJ,CAER,CACA,IAAIC,EAAYH,EAOhB,MANIjL,AAAoB,CAAA,IAApBA,EAAQ6E,OAAO,EAAa7E,AAAoB,CAAA,IAApBA,EAAQ6E,OAAO,CAC3CuG,EAAYpL,EAAQ6E,OAAO,CAEtB7E,AAAoB,SAApBA,EAAQ6E,OAAO,EACpBuG,CAAAA,EAAYvB,EAAkB,CAAA,EAE3B,CACH/E,KAAMnB,EAAa3D,EAAQ8E,IAAI,CAAE,GACjC3B,MAAOQ,EAAa3D,EAAQmD,KAAK,CAAE,QACnC8C,YAAa4D,EAAkB,EAC/BhF,QAASuG,CACb,CACJ,EAGMC,EAAM,CACRC,KAAM,CAAC,EAOPlG,OAAQ2F,EAAe,CAACnB,EAAaxE,MAAM,CAAEwE,EAAa1E,GAAG,CAAE0E,EAAa,CAAEE,EAAmBM,GACjGlF,IAAK6F,EAAe,CAACnB,EAAa1E,GAAG,CAAE0E,EAAaxE,MAAM,CAAEwE,EAAa,CAAEG,EAAgBM,GAC3FzF,KAAMmG,EAAe,CACjBnB,EAAahF,IAAI,CACjBgF,EAAa5E,KAAK,CAClB4E,EAAa2B,IAAI,CACjB3B,EACH,CAAEI,EAAiBM,GACpBtF,MAAO+F,EAAe,CAClBnB,EAAa5E,KAAK,CAClB4E,EAAahF,IAAI,CACjBgF,EAAa2B,IAAI,CACjB3B,EACH,CAAEK,EAAkBM,GACrB9E,KAAMsF,EAAe,CAACnB,EAAanE,IAAI,CAAEmE,EAAarE,KAAK,CAAEqE,EAAa,CAAEO,EA/EhC,CAAA,GAgF5C5E,MAAOwF,EAAe,CAACnB,EAAarE,KAAK,CAAEqE,EAAanE,IAAI,CAAEmE,EAAa,CAAEM,EAhF1D,CAAA,EAiFvB,EAKA,GAAInK,AAAgC,SAAhCA,EAAU4I,iBAAiB,CAAa,CACxC,IAAM6C,EAAc,SAAUC,CAAK,CAAEC,CAAK,EACtC,OAAQ,AAACD,EAAM5G,OAAO,GAAK6G,EAAM7G,OAAO,EACnC4G,EAAM5G,OAAO,EACV6G,EAAM7G,OAAO,EACZ4G,EAAMxF,WAAW,GAAKyF,EAAMzF,WAAW,AACpD,EACM0F,EAAS,EAAE,AACbH,CAAAA,EAAYH,EAAIzG,IAAI,CAAEyG,EAAI9F,KAAK,GAC/BoG,EAAOzC,IAAI,CAAC,CACR7I,EAAG,AAACmE,CAAAA,EAAKC,CAAC,EAAK,EACftE,EAAGmE,EACH/D,EA1IqN,EA2IrNqL,KAAM,CAAEzL,EAAG,EAAGE,EAAG,EAAGE,EAAG,CAAE,CAC7B,GAEAiL,EAAYH,EAAIzG,IAAI,CAAEyG,EAAI5F,IAAI,GAC9BkG,EAAOzC,IAAI,CAAC,CACR7I,EAAG,AAACmE,CAAAA,EAAKC,CAAC,EAAK,EACftE,EAAGmE,EACH/D,EAAGmE,EACHkH,KAAM,CAAEzL,EAAG,EAAGE,EAAG,EAAGE,EAAG,EAAG,CAC9B,GAEAiL,EAAYH,EAAIrG,KAAK,CAAEqG,EAAI9F,KAAK,GAChCoG,EAAOzC,IAAI,CAAC,CACR7I,EAAG,AAACmE,CAAAA,EAAKC,CAAC,EAAK,EACftE,EAAGoE,EACHhE,EA1JqN,EA2JrNqL,KAAM,CAAEzL,EAAG,EAAGE,EAAG,EAAGE,EAAG,CAAE,CAC7B,GAEAiL,EAAYH,EAAIrG,KAAK,CAAEqG,EAAI5F,IAAI,GAC/BkG,EAAOzC,IAAI,CAAC,CACR7I,EAAG,AAACmE,CAAAA,EAAKC,CAAC,EAAK,EACftE,EAAGoE,EACHhE,EAAGmE,EACHkH,KAAM,CAAEzL,EAAG,GAAIE,EAAG,EAAGE,EAAG,CAAE,CAC9B,GAEJ,IAAMsL,EAAe,EAAE,AACnBL,CAAAA,EAAYH,EAAIjG,MAAM,CAAEiG,EAAI9F,KAAK,GACjCsG,EAAa3C,IAAI,CAAC,CACd/I,EAAG,AAACmE,CAAAA,EAAKC,CAAC,EAAK,EACflE,EAAGoE,EACHlE,EA3KqN,EA4KrNqL,KAAM,CAAEzL,EAAG,EAAGE,EAAG,EAAGE,EAAG,CAAE,CAC7B,GAEAiL,EAAYH,EAAIjG,MAAM,CAAEiG,EAAI5F,IAAI,GAChCoG,EAAa3C,IAAI,CAAC,CACd/I,EAAG,AAACmE,CAAAA,EAAKC,CAAC,EAAK,EACflE,EAAGoE,EACHlE,EAAGmE,EACHkH,KAAM,CAAEzL,EAAG,GAAIE,EAAG,EAAGE,EAAG,CAAE,CAC9B,GAEJ,IAAMuL,EAAY,EAAE,AAChBN,CAAAA,EAAYH,EAAInG,GAAG,CAAEmG,EAAI9F,KAAK,GAC9BuG,EAAU5C,IAAI,CAAC,CACX/I,EAAG,AAACmE,CAAAA,EAAKC,CAAC,EAAK,EACflE,EAAGmE,EACHjE,EA5LqN,EA6LrNqL,KAAM,CAAEzL,EAAG,EAAGE,EAAG,EAAGE,EAAG,CAAE,CAC7B,GAEAiL,EAAYH,EAAInG,GAAG,CAAEmG,EAAI5F,IAAI,GAC7BqG,EAAU5C,IAAI,CAAC,CACX/I,EAAG,AAACmE,CAAAA,EAAKC,CAAC,EAAK,EACflE,EAAGmE,EACHjE,EAAGmE,EACHkH,KAAM,CAAEzL,EAAG,GAAIE,EAAG,EAAGE,EAAG,CAAE,CAC9B,GAEJ,IAAMwL,EAAe,EAAE,AACnBP,CAAAA,EAAYH,EAAIjG,MAAM,CAAEiG,EAAIzG,IAAI,GAChCmH,EAAa7C,IAAI,CAAC,CACd3I,EAAG,AAAC+E,CAAAA,AA3MiN,EA2M5MZ,CAAC,EAAK,EACfrE,EAAGoE,EACHtE,EAAGmE,EACHsH,KAAM,CAAEzL,EAAG,EAAGE,EAAG,EAAGE,EAAG,EAAG,CAC9B,GAEAiL,EAAYH,EAAIjG,MAAM,CAAEiG,EAAIrG,KAAK,GACjC+G,EAAa7C,IAAI,CAAC,CACd3I,EAAG,AAAC+E,CAAAA,AAnNiN,EAmN5MZ,CAAC,EAAK,EACfrE,EAAGoE,EACHtE,EAAGoE,EACHqH,KAAM,CAAEzL,EAAG,EAAGE,EAAG,EAAGE,EAAG,CAAE,CAC7B,GAEJ,IAAMyL,EAAY,EAAE,AAChBR,CAAAA,EAAYH,EAAInG,GAAG,CAAEmG,EAAIzG,IAAI,GAC7BoH,EAAU9C,IAAI,CAAC,CACX3I,EAAG,AAAC+E,CAAAA,AA5NiN,EA4N5MZ,CAAC,EAAK,EACfrE,EAAGmE,EACHrE,EAAGmE,EACHsH,KAAM,CAAEzL,EAAG,EAAGE,EAAG,EAAGE,EAAG,EAAG,CAC9B,GAEAiL,EAAYH,EAAInG,GAAG,CAAEmG,EAAIrG,KAAK,GAC9BgH,EAAU9C,IAAI,CAAC,CACX3I,EAAG,AAAC+E,CAAAA,AApOiN,EAoO5MZ,CAAC,EAAK,EACfrE,EAAGmE,EACHrE,EAAGoE,EACHqH,KAAM,CAAEzL,EAAG,EAAGE,EAAG,EAAGE,EAAG,CAAE,CAC7B,GAEJ,IAAM0L,EAAW,SAAUC,CAAK,CAAEtB,CAAI,CAAEuB,CAAI,EACxC,GAAID,AAAiB,IAAjBA,EAAM3J,MAAM,CACZ,OAAO,KAEX,GAAI2J,AAAiB,IAAjBA,EAAM3J,MAAM,CACZ,OAAO2J,CAAK,CAAC,EAAE,CAEnB,IAAME,EAAc9I,EAAoB4I,EAAOtM,EAAO,CAAA,GAClDyM,EAAO,EACX,IAAK,IAAIhK,EAAI,EAAGA,EAAI+J,EAAY7J,MAAM,CAAEF,IAChC8J,EAAOC,CAAW,CAAC/J,EAAE,CAACuI,EAAK,CAC3BuB,EAAOC,CAAW,CAACC,EAAK,CAACzB,EAAK,CAC9ByB,EAAOhK,EAEF,AAAC8J,EAAOC,CAAW,CAAC/J,EAAE,CAACuI,EAAK,EACjCuB,EAAOC,CAAW,CAACC,EAAK,CAACzB,EAAK,EAC7BwB,CAAW,CAAC/J,EAAE,CAAC9B,CAAC,CAAG6L,CAAW,CAACC,EAAK,CAAC9L,CAAC,EACvC8L,CAAAA,EAAOhK,CAAAA,EAGf,OAAO6J,CAAK,CAACG,EAAK,AACtB,CACAhB,CAAAA,EAAIC,IAAI,CAAG,CACPjL,EAAG,CACC,KAAQ4L,EAASN,EAAQ,IAAK,IAC9B,MAASM,EAASN,EAAQ,IAAK,EACnC,EACAxL,EAAG,CACC,IAAO8L,EAASH,EAAW,IAAK,IAChC,OAAUG,EAASJ,EAAc,IAAK,EAC1C,EACAtL,EAAG,CACC,IAAO0L,EAASD,EAAW,IAAK,IAChC,OAAUC,EAASF,EAAc,IAAK,EAC1C,CACJ,CACJ,MAEIV,EAAIC,IAAI,CAAG,CACPjL,EAAG,CACC,KAAQ,CACJF,EAAGmE,EAAI/D,EAnR0M,EAmRnMqL,KAAM,CAAEzL,EAAG,EAAGE,EAAG,EAAGE,EAAG,CAAE,CAC3C,EACA,MAAS,CACLJ,EAAGoE,EAAIhE,EAtR0M,EAsRnMqL,KAAM,CAAEzL,EAAG,EAAGE,EAAG,EAAGE,EAAG,CAAE,CAC3C,CACJ,EACAJ,EAAG,CACC,IAAO,CACHE,EAAGmE,EAAIjE,EA3R0M,EA2RnMqL,KAAM,CAAEzL,EAAG,EAAGE,EAAG,EAAGE,EAAG,CAAE,CAC3C,EACA,OAAU,CACNF,EAAGoE,EACHlE,EA/RiN,EAgSjNqL,KAAM,CAAEzL,EAAG,EAAGE,EAAG,EAAGE,EAAG,CAAE,CAC7B,CACJ,EACAA,EAAG,CACC,IAAO,CACHJ,EAAGmK,EAAkB/F,EAAKD,EAC1BjE,EAAGmE,EACHoH,KAAMtB,EACF,CAAEnK,EAAG,EAAGE,EAAG,EAAGE,EAAG,CAAE,EACnB,CAAEJ,EAAG,EAAGE,EAAG,EAAGE,EAAG,EAAG,CAC5B,EACA,OAAU,CACNJ,EAAGmK,EAAkB/F,EAAKD,EAC1BjE,EAAGoE,EACHmH,KAAMtB,EACF,CAAEnK,EAAG,EAAGE,EAAG,EAAGE,EAAG,CAAE,EACnB,CAAEJ,EAAG,EAAGE,EAAG,EAAGE,EAAG,EAAG,CAC5B,CACJ,CACJ,EAEJ,OAAO8K,CACX,CAsBA3D,SAASlH,CAAK,CAAE,CACZ,IAAMZ,EAAQ,IAAI,CAACA,KAAK,CAAE2B,EAAW3B,EAAM2B,QAAQ,CAAE+K,EAAY1M,EAAMQ,SAAS,CAAGmB,EAAUC,EAAU5B,EAAM4B,OAAO,CAAE+K,EAAa3M,EAAMU,UAAU,CAAGkB,EAASgL,EAAUjL,EAAW3B,EAAMQ,SAAS,CAAG,EAAGqM,EAAUjL,EAAU5B,EAAMU,UAAU,CAAG,EAAGoM,EAAS,CACxPC,KAAM3K,OAAO4K,SAAS,CACtBC,KAAM,CAAC7K,OAAO4K,SAAS,CACvBE,KAAM9K,OAAO4K,SAAS,CACtBG,KAAM,CAAC/K,OAAO4K,SAAS,AAC3B,EACII,EAASrM,EAAQ,EAyDrB,OAvDAqM,EAAU,CAAC,CACH7M,EAAGoB,EACHlB,EAAGmB,EACHjB,EAAG,CACP,EAAG,CACCJ,EAAGoB,EACHlB,EAAGmB,EACHjB,EAAGC,CACP,EAAE,CAEN,CAAC,EAAG,EAAE,CAACkG,OAAO,CAAC,SAAUrE,CAAC,EACtB2K,EAAQ9D,IAAI,CAAC,CACT/I,EAAGmM,EACHjM,EAAG2M,CAAO,CAAC3K,EAAE,CAAChC,CAAC,CACfE,EAAGyM,CAAO,CAAC3K,EAAE,CAAC9B,CAAC,AACnB,EACJ,GAEA,CAAC,EAAG,EAAG,EAAG,EAAE,CAACmG,OAAO,CAAC,SAAUrE,CAAC,EAC5B2K,EAAQ9D,IAAI,CAAC,CACT/I,EAAG6M,CAAO,CAAC3K,EAAE,CAAClC,CAAC,CACfE,EAAGkM,EACHhM,EAAGyM,CAAO,CAAC3K,EAAE,CAAC9B,CAAC,AACnB,EACJ,GAIAyM,AAFAA,CAAAA,EAAU1J,EAAoB0J,EAASpN,EAAO,CAAA,EAAK,EAE3C8G,OAAO,CAAC,SAAUuG,CAAM,EAC5BP,EAAOC,IAAI,CAAG1L,KAAKiM,GAAG,CAACR,EAAOC,IAAI,CAAEM,EAAO9M,CAAC,EAC5CuM,EAAOG,IAAI,CAAG5L,KAAKkM,GAAG,CAACT,EAAOG,IAAI,CAAEI,EAAO9M,CAAC,EAC5CuM,EAAOI,IAAI,CAAG7L,KAAKiM,GAAG,CAACR,EAAOI,IAAI,CAAEG,EAAO5M,CAAC,EAC5CqM,EAAOK,IAAI,CAAG9L,KAAKkM,GAAG,CAACT,EAAOK,IAAI,CAAEE,EAAO5M,CAAC,CAChD,GAEIkB,EAAWmL,EAAOC,IAAI,EACtBhM,CAAAA,EAAQM,KAAKiM,GAAG,CAACvM,EAAO,EAAIM,KAAKmM,GAAG,CAAC,AAAC7L,CAAAA,EAAWiL,CAAM,EAAME,CAAAA,EAAOC,IAAI,CAAGH,CAAM,GAAM,EAAC,EAGxFF,EAAYI,EAAOG,IAAI,EACvBlM,CAAAA,EAAQM,KAAKiM,GAAG,CAACvM,EAAO,AAAC2L,CAAAA,EAAYE,CAAM,EAAME,CAAAA,EAAOG,IAAI,CAAGL,CAAM,EAAE,EAGvEhL,EAAUkL,EAAOI,IAAI,GAEjBnM,EADA+L,EAAOI,IAAI,CAAG,EACN7L,KAAKiM,GAAG,CAACvM,EAAO,AAACa,CAAAA,EAAUiL,CAAM,EAAM,CAAA,CAACC,EAAOI,IAAI,CAAGtL,EAAUiL,CAAM,GAGtExL,KAAKiM,GAAG,CAACvM,EAAO,EAAI,AAACa,CAAAA,EAAUiL,CAAM,EAAMC,CAAAA,EAAOI,IAAI,CAAGL,CAAM,EAAK,IAIhFF,EAAaG,EAAOK,IAAI,EACxBpM,CAAAA,EAAQM,KAAKiM,GAAG,CAACvM,EAAOM,KAAKmM,GAAG,CAAC,AAACb,CAAAA,EAAaE,CAAM,EAAMC,CAAAA,EAAOK,IAAI,CAAGN,CAAM,GAAG,EAE/E9L,CACX,CACJ,CACAlD,EAAQsK,SAAS,CAAGA,CACxB,EAAGtK,GAAYA,CAAAA,EAAU,CAAC,CAAA,GAMG,IAAM4P,EAAiB5P,EAwCpD,IAAI6P,EAAmIzP,EAAoB,KACvJ0P,EAAuJ1P,EAAoBI,CAAC,CAACqP,GAajL,GAAM,CAAEE,SAAAA,CAAQ,CAAE,CAAInO,IAEhB,CAAEK,YAAa+N,CAAwB,CAAE,CAAGjL,EAE5C,CAAEkL,KAAM,CAAE3O,UAAW4O,CAAS,CAAE,CAAE,CAAG,AAACJ,IAA2IK,WAAW,CAE5L,CAAEC,WAAAA,CAAU,CAAEjK,KAAMkK,CAAiB,CAAE,CAAIzO,IAiBjD,SAAS0O,EAA2B9F,CAAO,EACvC,IAAqB+F,EAAU/F,EAAQC,KAAK,CAA7B,IAAI,CAAkC,EAAE,CAACC,KAAK,CAAClJ,IAAI,CAACmJ,UAAW,IAE9E,GAAI,CAACnB,AAFU,IAAI,CAEPrH,KAAK,CAACmE,IAAI,GAClB,OAAOiK,EAEX,IAAMC,EAAeN,EAAUM,YAAY,CAAEjO,EAAUiH,AALxC,IAAI,CAK2CjH,OAAO,CAAEkO,EAAsBjN,KAAKkN,KAAK,CACvGlH,AANe,IAAI,CAMZyD,KAAK,CAAC0D,YAAY,CAACpO,EAAQqO,SAAS,GACvCC,EAAe,EAAE,CACrB,GAAIrH,AARW,IAAI,CAQRsH,UAAU,CACjB,IAAK,IAAIlM,EAAI,EAAGA,EAAI4E,AATT,IAAI,CASYtH,MAAM,CAAC4C,MAAM,CAAEF,IACtCiM,EAAapF,IAAI,CAAC,CACd/I,EAAG8G,AAXA,IAAI,CAWGsH,UAAU,CAAClM,EAAE,CACvBhC,EAAGL,EAAQwO,QAAQ,CACfvH,AAbD,IAAI,CAaItH,MAAM,CAAC0C,EAAE,CAACoM,OAAO,CAAGP,EAC/B3N,EAAG0G,AAdA,IAAI,CAcGyH,QAAQ,AACtB,GAGR,IAAM3O,EAAYkH,AAlBH,IAAI,CAkBMrH,KAAK,CAACI,OAAO,CAACJ,KAAK,CAACG,SAAS,CACtDuO,EAAeb,EAAyBa,EAAcrH,AAnBvC,IAAI,CAmB0CrH,KAAK,CAAE,CAAA,GAAM6B,GAAG,CAAC,AAACC,GAAW,CAAA,CAAEoB,MAAOpB,EAAMvB,CAAC,CAAE4C,MAAOrB,EAAMrB,CAAC,CAAE2C,MAAOtB,EAAMnB,CAAC,AAAC,CAAA,GACvI0G,AApBW,IAAI,CAoBR0H,KAAK,EAAI5O,GAAaA,EAAUS,KAAK,EAAIT,EAAUc,IAAI,GAE1DoG,AAtBO,IAAI,CAsBJ2H,WAAW,GAClB3H,AAvBO,IAAI,CAuBJ2H,WAAW,CAAC7I,GAAG,CAACkB,AAvBhB,IAAI,CAuBmB0H,KAAK,EACnC1H,AAxBO,IAAI,CAwBJ2H,WAAW,CAAClF,IAAI,CAAC,CACpBmF,WAAY,EACZC,WAAY,CAChB,IAEJ7H,AA7BW,IAAI,CA6BR0H,KAAK,CAACjF,IAAI,CAAC,CACd1D,OAAQ/E,KAAKkM,GAAG,CAAC,EAAG,AAACpN,EAAUc,IAAI,CAAG,KAAOd,EAAUc,IAAI,CAAG,GAC1Dd,EAAUS,KAAK,CAAGS,KAAKkN,KAAK,CAAClH,AA/B1B,IAAI,CA+B6ByH,QAAQ,EAAI,GAChDzN,KAAKkN,KAAK,CAAClH,AAhCR,IAAI,CAgCWyH,QAAQ,EAAI,GACtC,IAEJJ,EAAaS,QAAQ,CAAG,CAAA,EACxB,IAAMC,EAAaf,EAAahP,IAAI,CApCrB,IAAI,CAoC0BqP,EAAc,CAAA,EAAM,CAAA,GAIjE,GAHIU,CAAU,CAAC,EAAE,EAAIA,AAAqB,MAArBA,CAAU,CAAC,EAAE,CAAC,EAAE,EACjCA,CAAAA,CAAU,CAAC,EAAE,CAAG,CAAC,IAAKA,CAAU,CAAC,EAAE,CAAC,EAAE,CAAEA,CAAU,CAAC,EAAE,CAAC,EAAE,CAAC,AAAD,EAExD/H,AAxCW,IAAI,CAwCRgI,QAAQ,CAAE,CAEjB,IAAMA,EAAWhI,AA1CN,IAAI,CA0CSgI,QAAQ,CAACC,MAAM,CAAC,EAAGjI,AA1ChC,IAAI,CA0CmCgI,QAAQ,CAAC1M,MAAM,CAAG,GAAGiI,MAAM,CAACwE,EAE9EC,CAAAA,EAASE,IAAI,CAAGlI,AA5CL,IAAI,CA4CQgI,QAAQ,CAACE,IAAI,CACpClI,AA7CW,IAAI,CA6CRgI,QAAQ,CAAGA,CACtB,CAEA,OADAhI,AA/Ce,IAAI,CA+CZmI,SAAS,CAAGpB,EACZA,CACX,CA6I6B,IAAMqB,EA7GZ,CACnBC,OAAQ,CA8BJC,WAAY,SAiBZC,OAAQ,CAAA,CACZ,EACAC,MAAO,CAgCHF,WAAY,KAmBZC,OAAQ,IACZ,CACJ,EAsBM,CAAEhC,SAAUkC,CAA0B,CAAE,CAAIrQ,IAE5C,CAAEmE,SAAUmM,CAA0B,CAAEC,OAAAA,CAAM,CAAE/B,WAAYgC,CAA4B,CAAEjM,KAAMkM,CAAsB,CAAE,CAAIzQ,IAkBlI,SAAS0Q,EAA4BjM,CAAC,EAClC,IAAMkM,EAAS,IAAI,CAACpF,IAAI,CAACoF,MAAM,AAC3BA,CAAAA,GACAJ,EAAO9L,EAAEwF,GAAG,CAAE0G,EAAOC,aAAa,CAACnM,EAAEwF,GAAG,EAEhD,CAIA,SAAS4G,EAAoBjI,CAAO,EAChC,IAAM+H,EAAS,IAAI,CAACpF,IAAI,CAACoF,MAAM,CAAEG,EAAOlI,EAAQC,KAAK,CAAC,IAAI,CAAE,EAAE,CAACC,KAAK,CAAClJ,IAAI,CAACmJ,UAAW,IACrF,GAAI4H,EAAQ,CACR,IAAMzG,EAAQ4G,CAAI,CAAC,EAAE,CACf3G,EAAM2G,CAAI,CAAC,EAAE,CACnB,GAAI5G,AAAa,MAAbA,CAAK,CAAC,EAAE,EAAYC,AAAW,MAAXA,CAAG,CAAC,EAAE,CAAU,CACpC,IAAM4G,EAAO,CACTJ,EAAOC,aAAa,CAAC,CAAE9P,EAAGoJ,CAAK,CAAC,EAAE,CAAElJ,EAAGkJ,CAAK,CAAC,EAAE,CAAEhJ,EAAG,CAAE,GACtDyP,EAAOC,aAAa,CAAC,CAAE9P,EAAGqJ,CAAG,CAAC,EAAE,CAAEnJ,EAAGmJ,CAAG,CAAC,EAAE,CAAEjJ,EAAG,CAAE,GACrD,CACD,OAAO,IAAI,CAACqK,IAAI,CAAChL,KAAK,CAACuE,QAAQ,CAACkM,cAAc,CAACD,EACnD,CACJ,CACA,OAAOD,CACX,CAS6B,IAAMG,EAHX,CACpB1H,QAvCJ,SAAmC2H,CAAS,EACpCV,EAA6BH,EAA4B,iBACzDC,EAA2BY,EAAW,wBAAyBR,GAC/DD,EAAuBS,EAAUxR,SAAS,CAAE,cAAemR,GAEnE,CAmCA,EAkBM,CAAE9M,eAAAA,CAAc,CAAE,CAAI/D,IAEtB,CAAEG,QAASgR,CAAyB,CAAE,CAAInR,IAE1C,CAAEK,YAAa+Q,CAA6B,CAAE5O,cAAe6O,EAA+B,CAAExO,UAAWyO,EAA2B,CAAE,CAAGnO,EAGzI,CAAEgB,SAAUoN,EAA0B,CAAElN,MAAOmN,EAAuB,CAAEpR,KAAMqR,EAAsB,CAAElN,KAAMmN,EAAsB,CAAE,CAAI1R,IAS9I,SAAS2R,KACL,IAAmBpR,EAAQgL,AAAd,IAAI,CAAehL,KAAK,CAAEI,EAAU4K,AAApC,IAAI,CAAqC5K,OAAO,AACzDJ,CAAAA,EAAMmE,IAAI,MAAQ6G,AAAc,cAAdA,AADT,IAAI,CACUqG,IAAI,GAC3BjR,EAAQkR,SAAS,CAAGJ,GAAuB9Q,EAAQkR,SAAS,CAAE,GAC9DlR,EAAQmR,aAAa,CAAGL,GAAuB9Q,EAAQmR,aAAa,CAAE,GAE9E,CAIA,SAASC,GAAoBtN,CAAC,EAEtB8G,AADS,IAAI,CACRhL,KAAK,CAACmE,IAAI,IACf6G,AAAc,cAAdA,AAFS,IAAI,CAERqG,IAAI,EACLnN,EAAEpC,KAAK,EACPoC,CAAAA,EAAEpC,KAAK,CAAC2P,YAAY,CAAGzG,AAJlB,IAAI,CAImB0G,OAAO,CAC/BxN,EAAEpC,KAAK,CAAC6P,QAAQ,CAChB3G,AANC,IAAI,CAMA4G,GAAG,CAAG1N,EAAEpC,KAAK,CAAC+P,QAAQ,AAAD,CAG1C,CAIA,SAASC,KAED,AAAC9G,AADQ,IAAI,CACPoF,MAAM,EACZpF,CAAAA,AAFS,IAAI,CAERoF,MAAM,CAAG,IAAI2B,GAFT,IAAI,CAEyB,CAE9C,CAKA,SAASC,GAAoB3J,CAAO,SAGhC,AAAI,AAAC2C,AAFQ,IAAI,CAEPhL,KAAK,CAACmE,IAAI,IAAM6G,AAAc,cAAdA,AAFb,IAAI,CAEcqG,IAAI,CAG5B,EAAE,CAFEhJ,EAAQC,KAAK,CAHX,IAAI,CAGc,EAAE,CAACC,KAAK,CAAClJ,IAAI,CAACmJ,UAAW,GAG5D,CAIA,SAASyJ,GAAwB5J,CAAO,EAEpC,GAAI,CAAC,IAAI,CAACrI,KAAK,CAACmE,IAAI,IAAM,AAAc,cAAd,IAAI,CAACkN,IAAI,CAC/B,OAAOhJ,EAAQC,KAAK,CAAC,IAAI,CAAE,EAAE,CAACC,KAAK,CAAClJ,IAAI,CAACmJ,UAAW,IAExD,IAAM0J,EAAO1J,UAAW2J,EAAOD,CAAI,CAAC,EAAE,CAAEE,EAAKF,CAAI,CAAC,EAAE,CAAE3B,EAAO,EAAE,CAAE8B,EAAW,IAAI,CAACC,eAAe,CAAC,CAAEC,MAAOJ,CAAK,GAAIK,EAAS,IAAI,CAACF,eAAe,CAAC,CAAEC,MAAOH,CAAG,GAC7J,GAAIC,GAAYG,EACZ,IAAK,IAAI/P,EAAI,EAAGA,EAAI4P,EAAS1P,MAAM,CAAEF,GAAK,EAAG,CACzC,IAAMgQ,EAAeJ,CAAQ,CAAC5P,EAAE,CAAEiQ,EAAaL,CAAQ,CAAC5P,EAAI,EAAE,CAAEkQ,EAAaH,CAAM,CAAC/P,EAAE,CAAEmQ,EAAWJ,CAAM,CAAC/P,EAAI,EAAE,AAC5GgQ,AAAoB,CAAA,MAApBA,CAAY,CAAC,EAAE,EACfC,AAAkB,MAAlBA,CAAU,CAAC,EAAE,EACbC,AAAkB,MAAlBA,CAAU,CAAC,EAAE,EACbC,AAAgB,MAAhBA,CAAQ,CAAC,EAAE,EACXrC,EAAKjH,IAAI,CAACmJ,EAAcC,EAAYE,EAEpC,CAAC,IAAKD,CAAU,CAAC,EAAE,CAAEA,CAAU,CAAC,EAAE,CAAC,CAAE,CAAC,IAAI,CAElD,CAEJ,OAAOpC,CACX,CAIA,SAASsC,GAAwBxK,CAAO,EACpC,IAAmB+H,EAASpF,AAAf,IAAI,CAAgBoF,MAAM,CAAEpQ,EAAQgL,AAApC,IAAI,CAAqChL,KAAK,CAAEuQ,EAAOlI,EAAQC,KAAK,CAApE,IAAI,CAAuE,EAAE,CAACC,KAAK,CAAClJ,IAAI,CAACmJ,UAAW,IAEjH,GAAkB,cAAdwC,AAFS,IAAI,CAERqG,IAAI,EACT,CAACrR,EAAMsE,OAAO,EACd,CAACtE,EAAMmE,IAAI,IAGXoM,AAAS,OAATA,EAFA,OAAOA,EAKX,IAAMpQ,EAAYH,EAAMI,OAAO,CAACJ,KAAK,CAACG,SAAS,CAAE3B,EAAIwM,AAVxC,IAAI,CAUyC8H,OAAO,CAAG9S,EAAMQ,SAAS,CAAGL,EAAUS,KAAK,CAAE4D,EAAQxE,EAAMsE,OAAO,CAAC0B,OAAO,CAAE+M,EAAexC,CAAI,CAAC,EAAE,CAAEyC,EAAazC,CAAI,CAAC,EAAE,CAC9KC,EAAMyC,EAAe,EAAE,CAoD3B,MAnDwB,MAApBF,CAAY,CAAC,EAAE,EAAYC,AAAkB,MAAlBA,CAAU,CAAC,EAAE,GACxCxC,EAAO,CACHJ,EAAO8C,KAAK,CAAC,CAAE3S,EAAGwS,CAAY,CAAC,EAAE,CAAEtS,EAAGsS,CAAY,CAAC,EAAE,CAAEpS,EAAG,CAAE,GAC5DyP,EAAO8C,KAAK,CAAC,CAAE3S,EAAGwS,CAAY,CAAC,EAAE,CAAEtS,EAAGsS,CAAY,CAAC,EAAE,CAAEpS,EAAGnC,CAAE,GAC5D4R,EAAO8C,KAAK,CAAC,CAAE3S,EAAGyS,CAAU,CAAC,EAAE,CAAEvS,EAAGuS,CAAU,CAAC,EAAE,CAAErS,EAAG,CAAE,GACxDyP,EAAO8C,KAAK,CAAC,CAAE3S,EAAGyS,CAAU,CAAC,EAAE,CAAEvS,EAAGuS,CAAU,CAAC,EAAE,CAAErS,EAAGnC,CAAE,GAC3D,CACI,IAAI,CAACyM,KAAK,EAcN,IAAI,CAAC6H,OAAO,EACbtO,EAAMQ,IAAI,CAACC,OAAO,EAClBgO,EAAa3J,IAAI,CAACkH,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,EAElChM,EAAMY,KAAK,CAACH,OAAO,EACnBgO,EAAa3J,IAAI,CAACkH,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,IAUlChM,EAAMmB,KAAK,CAACV,OAAO,EACnBgO,EAAa3J,IAAI,CAACkH,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,EAElChM,EAAMqB,IAAI,CAACZ,OAAO,EAClBgO,EAAa3J,IAAI,CAACkH,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,GAElChM,EAAMc,GAAG,CAACL,OAAO,EACjBgO,EAAa3J,IAAI,CAACkH,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,EAElChM,EAAMgB,MAAM,CAACP,OAAO,EACpBgO,EAAa3J,IAAI,CAACkH,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,IAtClChM,EAAMmB,KAAK,CAACV,OAAO,EACnBgO,EAAa3J,IAAI,CAACkH,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,EAElChM,EAAMqB,IAAI,CAACZ,OAAO,EAClBgO,EAAa3J,IAAI,CAACkH,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,EAElChM,EAAMQ,IAAI,CAACC,OAAO,EAClBgO,EAAa3J,IAAI,CAACkH,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,EAElChM,EAAMY,KAAK,CAACH,OAAO,EACnBgO,EAAa3J,IAAI,CAACkH,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,GA+B1CyC,EAAepC,EAA8BoC,EAAc,IAAI,CAACjT,KAAK,CAAE,CAAA,IAEpEA,EAAMuE,QAAQ,CAACkM,cAAc,CAACwC,EACzC,CAMA,SAASE,GAAqB9K,CAAO,CAAE+K,CAAI,EACvC,GAAmB,CAAEpT,MAAAA,CAAK,CAAEqT,UAAAA,CAAS,CAAEC,cAAAA,CAAa,CAAEC,MAAAA,CAAK,CAAE,CAAhD,IAAI,CACjB,GAAIvI,AADS,IAAI,CACRwI,UAAU,EACfxT,EAAMiG,WAAW,EACjBjG,EAAMmE,IAAI,IACVkP,GACAD,GACAA,EAAKK,KAAK,CAAE,CACZ,IAOIC,EAAUC,EAAcC,EAPtBC,EAAiBR,EAAUS,OAAO,CAACC,UAAU,CAAC,EAAE,CAACC,OAAO,GAAKC,EAAcjU,EAAMiG,WAAW,CAACjB,IAAI,CAACgP,OAAO,GAAI7T,EAAYH,EAAMI,OAAO,CAACJ,KAAK,CAACG,SAAS,CAAEG,EAAS,CACnKC,EAAGP,EAAMQ,SAAS,CAAG,EACrBC,EAAGT,EAAMU,UAAU,CAAG,EACtBC,EAAGR,EAAUS,KAAK,CAAG,EACrBC,GAAKqQ,GAAuB/Q,EAAUS,KAAK,CAAE,GACzCsQ,GAAuB/Q,EAAUW,YAAY,CAAE,EACvD,EAAGoT,EAAQZ,EAAca,OAAO,CAACf,EAAK1J,GAAG,EAAG0K,EAAWb,CAAK,CAACD,CAAa,CAACY,EAAQ,EAAE,CAAC,CAAEG,EAAWd,CAAK,CAACD,CAAa,CAACY,EAAQ,EAAE,CAAC,CA+BlI,OA3BIE,GAAUX,OAAOa,IACjBX,CAAAA,EAAe7C,GAAgC,CAC3CvQ,EAAG6T,EAASX,KAAK,CAACa,EAAE,CAAC/T,CAAC,CACtBE,EAAG2T,EAASX,KAAK,CAACa,EAAE,CAAC7T,CAAC,CACtBE,EAAG,IACP,EAAGL,EAAQA,EAAOO,EAAE,CAAA,EAIpBwT,GAAUZ,OAAOa,IACjBV,CAAAA,EAAe9C,GAAgC,CAC3CvQ,EAAG8T,EAASZ,KAAK,CAACa,EAAE,CAAC/T,CAAC,CACtBE,EAAG4T,EAASZ,KAAK,CAACa,EAAE,CAAC7T,CAAC,CACtBE,EAAG,IACP,EAAGL,EAAQA,EAAOO,EAAE,CAAA,EAOxB6S,EAAW5C,GALX4C,EAAW,CACPnT,EAAG6S,EAAKK,KAAK,CAACa,EAAE,CAAC/T,CAAC,CAClBE,EAAG2S,EAAKK,KAAK,CAACa,EAAE,CAAC7T,CAAC,CAClBE,EAAG,IACP,EACqDL,EAAQA,EAAOO,EAAE,EAM/DQ,KAAKmM,GAAG,CAACmG,EACZD,EAASnT,CAAC,CAAGoT,EAAapT,CAAC,CAAGqT,EAC9BA,EAAarT,CAAC,CAAGmT,EAASnT,CAAC,CAC3BsT,EAActT,CAAC,CAAG0T,EAAY1T,CAAC,CACvC,CACA,OAAO8H,EAAQC,KAAK,CAjDP,IAAI,CAiDU,EAAE,CAACC,KAAK,CAAClJ,IAAI,CAACmJ,UAAW,GACxD,CAIA,SAAS+L,GAAyBlM,CAAO,EACrC,IAAMqB,EAAMrB,EAAQC,KAAK,CAAC,IAAI,CAAE,EAAE,CAACC,KAAK,CAAClJ,IAAI,CAACmJ,UAAW,IACzD,OAAO,IAAI,CAAC4H,MAAM,CACd,IAAI,CAACA,MAAM,CAACC,aAAa,CAAC3G,EAAK,CAAA,GAC/BA,CACR,CAWA,MAAMqI,GAUF,OAAO/I,QAAQwL,CAAS,CAAE7D,CAAS,CAAE,CAEjC,GADAD,EAAkB1H,OAAO,CAAC2H,GACtB,CAAC6D,EAAUC,SAAS,CAACC,QAAQ,CAAC,UAAW,CACzCzD,GAAwB,CAAA,EAAMzN,EAAeqH,KAAK,CAAE4E,GACpD+E,EAAUC,SAAS,CAACnL,IAAI,CAAC,UACzB0H,GAA2BwD,EAAW,OAAQ1C,IAC9Cd,GAA2BwD,EAAW,kBAAmBpD,IACzDJ,GAA2BwD,EAAW,gBAAiBhD,IACvD,IAAMmD,EAAYH,EAAUrV,SAAS,CACrCgS,GAAuBwD,EAAW,cAAe3C,IACjDb,GAAuBwD,EAAW,kBAAmB1C,IACrDd,GAAuBwD,EAAW,kBAAmB9B,IACrD1B,GAAuBwD,EAAW,eAAgBxB,IAClDhC,GAAuBwD,EAAW,mBAAoBJ,GAC1D,CACJ,CASAxK,YAAYiB,CAAI,CAAE,CACd,IAAI,CAACA,IAAI,CAAGA,CAChB,CAiBAqF,cAAc3G,CAAG,CAAEkL,CAAO,CAAE,CAExB,IAAM5J,EAAOoF,AADE,IAAI,CACCpF,IAAI,CAClBhL,EAAQgL,EAAKhL,KAAK,CAExB,GAAIgL,AAAc,cAAdA,EAAKqG,IAAI,EACT,CAACrR,EAAMsE,OAAO,EACd,CAACtE,EAAMmE,IAAI,GACX,OAAOuF,EAEX,IAAMxI,EAAQ0P,EAA4B5Q,EAAMI,OAAO,CAACJ,KAAK,CAACG,SAAS,CAACe,KAAK,CAAED,EAAO2P,EAA4B5Q,EAAMI,OAAO,CAACJ,KAAK,CAACG,SAAS,CAACc,IAAI,CAAE4T,EAAe3D,GAAuB0D,GAAW5J,EAAK5K,OAAO,CAACyP,KAAK,CAACF,UAAU,CAAE3E,EAAK5K,OAAO,CAACsP,MAAM,CAACC,UAAU,EAAGmF,EAAO5D,GAAuB0D,GAAW5J,EAAK5K,OAAO,CAACyP,KAAK,CAACD,MAAM,CAAE5E,EAAK5K,OAAO,CAACsP,MAAM,CAACE,MAAM,EAAGpL,EAAQxE,EAAMsE,OAAO,CAAC0B,OAAO,CAAErE,EAAW3B,EAAM2B,QAAQ,CAAE+K,EAAY1M,EAAMQ,SAAS,CAAGmB,EAAUC,EAAU5B,EAAM4B,OAAO,CAAE+K,EAAa3M,EAAMU,UAAU,CAAGkB,EAC/fmT,EAAU,EAAGC,EAAU,EAAGC,EAAMC,EAAO,CAAE3U,EAAG,EAAGE,EAAG,EAAGE,EAAG,CAAE,EAG9DwU,EAAc,CAAA,EAEd,GADAzL,EAAMsB,EAAKoF,MAAM,CAAC8C,KAAK,CAAC,CAAE3S,EAAGmJ,EAAInJ,CAAC,CAAEE,EAAGiJ,EAAIjJ,CAAC,CAAEE,EAAG,CAAE,GAC/CqK,EAAK8H,OAAO,CACZ,GAAI9H,EAAKE,QAAQ,CAAE,CACf,GAAI1G,AAAqB,OAArBA,EAAMkH,IAAI,CAAC/K,CAAC,CAAC2E,GAAG,CAChB,MAAO,CAAC,EAEZ0P,EAAUtL,EAAIjJ,CAAC,CAAGmB,EAClB8H,EAAInJ,CAAC,CAAGiE,EAAMkH,IAAI,CAAC/K,CAAC,CAAC2E,GAAG,CAAC/E,CAAC,CAC1BmJ,EAAIjJ,CAAC,CAAG+D,EAAMkH,IAAI,CAAC/K,CAAC,CAAC2E,GAAG,CAAC7E,CAAC,CAC1BwU,EAAOzQ,EAAMkH,IAAI,CAAC/K,CAAC,CAAC2E,GAAG,CAAC0G,IAAI,CAC5BmJ,EAAc,CAAC3Q,EAAMc,GAAG,CAACe,WAAW,AACxC,KACK,CACD,GAAI7B,AAAwB,OAAxBA,EAAMkH,IAAI,CAAC/K,CAAC,CAAC6E,MAAM,CACnB,MAAO,CAAC,EAEZwP,EAAUtL,EAAIjJ,CAAC,CAAGkM,EAClBjD,EAAInJ,CAAC,CAAGiE,EAAMkH,IAAI,CAAC/K,CAAC,CAAC6E,MAAM,CAACjF,CAAC,CAC7BmJ,EAAIjJ,CAAC,CAAG+D,EAAMkH,IAAI,CAAC/K,CAAC,CAAC6E,MAAM,CAAC/E,CAAC,CAC7BwU,EAAOzQ,EAAMkH,IAAI,CAAC/K,CAAC,CAAC6E,MAAM,CAACwG,IAAI,CAC/BmJ,EAAc,CAAC3Q,EAAMgB,MAAM,CAACa,WAAW,AAC3C,MAEC,GAAI2E,EAAKC,KAAK,CACf,GAAID,EAAKE,QAAQ,CAAE,CACf,GAAI1G,AAAqB,OAArBA,EAAMkH,IAAI,CAACnL,CAAC,CAAC+E,GAAG,CAChB,MAAO,CAAC,EAEZ0P,EAAUtL,EAAIjJ,CAAC,CAAGmB,EAClB8H,EAAIjJ,CAAC,CAAG+D,EAAMkH,IAAI,CAACnL,CAAC,CAAC+E,GAAG,CAAC7E,CAAC,CAC1BiJ,EAAI/I,CAAC,CAAG6D,EAAMkH,IAAI,CAACnL,CAAC,CAAC+E,GAAG,CAAC3E,CAAC,CAC1BsU,EAAOzQ,EAAMkH,IAAI,CAACnL,CAAC,CAAC+E,GAAG,CAAC0G,IAAI,CAC5BmJ,EAAc,CAAC3Q,EAAMc,GAAG,CAACe,WAAW,AACxC,KACK,CACD,GAAI7B,AAAwB,OAAxBA,EAAMkH,IAAI,CAACnL,CAAC,CAACiF,MAAM,CACnB,MAAO,CAAC,EAEZwP,EAAUtL,EAAIjJ,CAAC,CAAGkM,EAClBjD,EAAIjJ,CAAC,CAAG+D,EAAMkH,IAAI,CAACnL,CAAC,CAACiF,MAAM,CAAC/E,CAAC,CAC7BiJ,EAAI/I,CAAC,CAAG6D,EAAMkH,IAAI,CAACnL,CAAC,CAACiF,MAAM,CAAC7E,CAAC,CAC7BsU,EAAOzQ,EAAMkH,IAAI,CAACnL,CAAC,CAACiF,MAAM,CAACwG,IAAI,CAC/BmJ,EAAc,CAAC3Q,EAAMgB,MAAM,CAACa,WAAW,AAC3C,MAGA,GAAI2E,EAAKE,QAAQ,CAAE,CACf,GAAI1G,AAAuB,OAAvBA,EAAMkH,IAAI,CAACjL,CAAC,CAAC2E,KAAK,CAClB,MAAO,CAAC,EAEZ2P,EAAUrL,EAAInJ,CAAC,CAAGmM,EAClBhD,EAAInJ,CAAC,CAAGiE,EAAMkH,IAAI,CAACjL,CAAC,CAAC2E,KAAK,CAAC7E,CAAC,CAC5BmJ,EAAI/I,CAAC,CAAG6D,EAAMkH,IAAI,CAACjL,CAAC,CAAC2E,KAAK,CAACzE,CAAC,CAG5BsU,EAAO,CAAE1U,EAAG0U,AAFZA,CAAAA,EAAOzQ,EAAMkH,IAAI,CAACjL,CAAC,CAAC2E,KAAK,CAAC4G,IAAI,AAAD,EAEZrL,CAAC,CAAEF,EAAGwU,EAAKxU,CAAC,CAAEE,EAAG,CAACsU,EAAK1U,CAAC,AAAC,CAC9C,KACK,CACD,GAAIiE,AAAsB,OAAtBA,EAAMkH,IAAI,CAACjL,CAAC,CAACuE,IAAI,CACjB,MAAO,CAAC,EAEZ+P,EAAUrL,EAAInJ,CAAC,CAAGoB,EAClB+H,EAAInJ,CAAC,CAAGiE,EAAMkH,IAAI,CAACjL,CAAC,CAACuE,IAAI,CAACzE,CAAC,CAC3BmJ,EAAI/I,CAAC,CAAG6D,EAAMkH,IAAI,CAACjL,CAAC,CAACuE,IAAI,CAACrE,CAAC,CAC3BsU,EAAOzQ,EAAMkH,IAAI,CAACjL,CAAC,CAACuE,IAAI,CAACgH,IAAI,AACjC,CAEJ,GAAI6I,AAAiB,UAAjBA,QAIC,GAAIA,AAAiB,SAAjBA,EAEL,GAAK7J,EAAKC,KAAK,CAGV,CACD,IAAIxJ,EAAMJ,KAAKI,GAAG,CAACP,GACbI,EAAMD,KAAKC,GAAG,CAACJ,EACjB8J,CAAAA,EAAKE,QAAQ,EACbzJ,CAAAA,EAAM,CAACA,CAAE,EAET0T,GACA1T,CAAAA,EAAM,CAACA,CAAE,EAEbyT,EAAO,CAAE3U,EAAG0U,EAAKtU,CAAC,CAAGc,EAAKhB,EAAGa,EAAKX,EAAG,CAACsU,EAAK1U,CAAC,CAAGkB,CAAI,CACvD,MAZIwT,EAAO,CAAE1U,EAAGc,KAAKC,GAAG,CAACL,GAAOR,EAAG,EAAGE,EAAGU,KAAKI,GAAG,CAACR,EAAM,OAcvD,GAAI4T,AAAiB,UAAjBA,EAEL,GAAK7J,EAAKC,KAAK,CAGV,CACD,IAAMmK,EAAO/T,KAAKI,GAAG,CAACP,GAChBmU,EAAOhU,KAAKC,GAAG,CAACJ,GAGhBoU,EAAO,CAAE/U,EAAGgV,AAFLlU,KAAKI,GAAG,CAACR,GAEGoU,EAAM5U,EAAG,CAAC2U,EAAMzU,EAAG,CAAC0U,EADhChU,KAAKC,GAAG,CAACL,EACmC,EAMrDF,EAAQ,EAAIM,KAAK2B,IAAI,CAACkS,AAL1BA,CAAAA,EAAO,CACH3U,EAAG0U,EAAKxU,CAAC,CAAG6U,EAAK3U,CAAC,CAAGsU,EAAKtU,CAAC,CAAG2U,EAAK7U,CAAC,CACpCA,EAAGwU,EAAKtU,CAAC,CAAG2U,EAAK/U,CAAC,CAAG0U,EAAK1U,CAAC,CAAG+U,EAAK3U,CAAC,CACpCA,EAAGsU,EAAK1U,CAAC,CAAG+U,EAAK7U,CAAC,CAAGwU,EAAKxU,CAAC,CAAG6U,EAAK/U,CAAC,AACxC,CAAA,EAC+BA,CAAC,CAAG2U,EAAK3U,CAAC,CAAG2U,EAAKzU,CAAC,CAAGyU,EAAKzU,CAAC,CAAGyU,EAAKvU,CAAC,CAAGuU,EAAKvU,CAAC,CACzEwU,CAAAA,GACApU,CAAAA,EAAQ,CAACA,CAAI,EAEjBmU,EAAO,CACH3U,EAAGQ,EAAQmU,EAAK3U,CAAC,CAAEE,EAAGM,EAAQmU,EAAKzU,CAAC,CAAEE,EAAGI,EAAQmU,EAAKvU,CAAC,AAC3D,CACJ,MApBIsU,EAAO,CAAE1U,EAAGc,KAAKC,GAAG,CAACL,GAAOR,EAAG,EAAGE,EAAGU,KAAKI,GAAG,CAACR,EAAM,OAyBnD+J,EAAKC,KAAK,CAIXiK,EAAO,CACH3U,EAAGc,KAAKI,GAAG,CAACR,GAAQI,KAAKI,GAAG,CAACP,GAC7BT,EAAGY,KAAKC,GAAG,CAACJ,GACZP,EAAG,CAACU,KAAKC,GAAG,CAACL,GAAQI,KAAKI,GAAG,CAACP,EAClC,EAPA+T,EAAO,CAAE1U,EAAGc,KAAKC,GAAG,CAACL,GAAOR,EAAG,EAAGE,EAAGU,KAAKI,GAAG,CAACR,EAAM,CAU5DyI,CAAAA,EAAInJ,CAAC,EAAIwU,EAAUE,EAAK1U,CAAC,CAAGyU,EAAUE,EAAK3U,CAAC,CAC5CmJ,EAAIjJ,CAAC,EAAIsU,EAAUE,EAAKxU,CAAC,CAAGuU,EAAUE,EAAKzU,CAAC,CAC5CiJ,EAAI/I,CAAC,EAAIoU,EAAUE,EAAKtU,CAAC,CAAGqU,EAAUE,EAAKvU,CAAC,CAC5C,IAAM6U,EAAY3E,EAA8B,CAACnH,EAAI,CAAEsB,EAAKhL,KAAK,CAAC,CAAC,EAAE,CACrE,GAAI8U,EAAM,CAOFW,AADc,EAJC1E,GAA4BF,EAA8B,CACzEnH,EACA,CAAEnJ,EAAGmJ,EAAInJ,CAAC,CAAG0U,EAAK1U,CAAC,CAAEE,EAAGiJ,EAAIjJ,CAAC,CAAGwU,EAAKxU,CAAC,CAAEE,EAAG+I,EAAI/I,CAAC,CAAGsU,EAAKtU,CAAC,AAAC,EAC1D,CAAEJ,EAAGmJ,EAAInJ,CAAC,CAAG2U,EAAK3U,CAAC,CAAEE,EAAGiJ,EAAIjJ,CAAC,CAAGyU,EAAKzU,CAAC,CAAEE,EAAG+I,EAAI/I,CAAC,CAAGuU,EAAKvU,CAAC,AAAC,EAC7D,CAAEqK,EAAKhL,KAAK,IAETiV,CAAAA,EAAO,CAAE1U,EAAG,CAAC0U,EAAK1U,CAAC,CAAEE,EAAG,CAACwU,EAAKxU,CAAC,CAAEE,EAAG,CAACsU,EAAKtU,CAAC,AAAC,CAAA,EAEhD,IAAM+U,EAAkB7E,EAA8B,CAClD,CAAEtQ,EAAGmJ,EAAInJ,CAAC,CAAEE,EAAGiJ,EAAIjJ,CAAC,CAAEE,EAAG+I,EAAI/I,CAAC,AAAC,EAC/B,CAAEJ,EAAGmJ,EAAInJ,CAAC,CAAG0U,EAAK1U,CAAC,CAAEE,EAAGiJ,EAAIjJ,CAAC,CAAGwU,EAAKxU,CAAC,CAAEE,EAAG+I,EAAI/I,CAAC,CAAGsU,EAAKtU,CAAC,AAAC,EAC1D,CAAEJ,EAAGmJ,EAAInJ,CAAC,CAAG2U,EAAK3U,CAAC,CAAEE,EAAGiJ,EAAIjJ,CAAC,CAAGyU,EAAKzU,CAAC,CAAEE,EAAG+I,EAAI/I,CAAC,CAAGuU,EAAKvU,CAAC,AAAC,EAC7D,CAAEqK,EAAKhL,KAAK,CACbwV,CAAAA,EAAUG,MAAM,CAAG,CACfD,CAAe,CAAC,EAAE,CAACnV,CAAC,CAAGmV,CAAe,CAAC,EAAE,CAACnV,CAAC,CAC3CmV,CAAe,CAAC,EAAE,CAACjV,CAAC,CAAGiV,CAAe,CAAC,EAAE,CAACjV,CAAC,CAC3CiV,CAAe,CAAC,EAAE,CAACnV,CAAC,CAAGmV,CAAe,CAAC,EAAE,CAACnV,CAAC,CAC3CmV,CAAe,CAAC,EAAE,CAACjV,CAAC,CAAGiV,CAAe,CAAC,EAAE,CAACjV,CAAC,CAC3C+U,EAAUjV,CAAC,CACXiV,EAAU/U,CAAC,CACd,CACD+U,EAAUG,MAAM,CAAC,EAAE,EAAIH,EAAUjV,CAAC,CAAGiV,EAAUG,MAAM,CAAC,EAAE,CACpDH,EAAU/U,CAAC,CAAG+U,EAAUG,MAAM,CAAC,EAAE,CACrCH,EAAUG,MAAM,CAAC,EAAE,EAAIH,EAAUjV,CAAC,CAAGiV,EAAUG,MAAM,CAAC,EAAE,CACpDH,EAAU/U,CAAC,CAAG+U,EAAUG,MAAM,CAAC,EAAE,AACzC,CACA,OAAOH,CACX,CAIAtC,MAAM0C,CAAC,CAAE3V,CAAc,CAAE,CACrB,IAAM+K,EAAO,IAAI,CAACA,IAAI,CACtB,GAAIA,EAAK8H,OAAO,CAAE,CACd,IAAMnR,EAAW1B,EAAiB,EAAI+K,EAAKhL,KAAK,CAAC2B,QAAQ,CACzD,MAAO,CACHpB,EAAGoB,EAAWiU,EAAEjV,CAAC,CACjBF,EAAGmV,EAAEnV,CAAC,CACNE,EAAGiV,EAAErV,CAAC,CAAGoB,CACb,CACJ,CACA,OAAOiU,CACX,CACJ,CASA,IAAIC,GAA2I5X,EAAoB,KAC/J6X,GAA+J7X,EAAoBI,CAAC,CAACwX,IAErLE,GAAmG9X,EAAoB,KACvH+X,GAAuH/X,EAAoBI,CAAC,CAAC0X,IAejJ,GAAM,CAAEnI,SAAUqI,EAAiB,CAAE,CAAIxW,IAEnC,CAAEK,YAAaoW,EAAoB,CAAE,CAAGtT,EAGxC,CAAEgB,SAAUuS,EAAiB,CAAEnG,OAAQoG,EAAe,CAAEC,SAAAA,EAAQ,CAAEvS,MAAOwS,EAAc,CAAEzW,KAAM0W,EAAa,CAAEtI,WAAYuI,EAAmB,CAAE,CAAI/W,GAMzJ,OAAMgX,WAAkBT,KAMpB,OAAOhN,QAAQ0N,CAAW,CAAE,CACpBF,GAAoBP,GAAmB,mBACvCE,GAAkBO,EAAa,iBAAkB,WACzC,IAAI,CAAC1W,KAAK,CAACmE,IAAI,IACf,IAAI,CAACwS,iBAAiB,EAE9B,GACAP,GAAgBM,EAAYvX,SAAS,CAAE,CACnCwX,kBAAmBF,GAAStX,SAAS,CAACwX,iBAAiB,AAC3D,GAER,CAUAA,mBAAoB,CAChB,IAGIC,EAAgBC,EAHdxP,EAAS,IAAI,CAAEyP,EAAgBzP,EAAOjH,OAAO,CAAEJ,EAAQqH,EAAOrH,KAAK,CAAE+K,EAAQwL,GAAclP,EAAO0D,KAAK,CAAE/K,EAAMI,OAAO,CAAC2K,KAAK,CAAC,EAAE,EAAGgM,EAAY,EAAE,CAAEpI,EAAa,EAAE,CAAEqI,EAAQF,EAAclI,QAAQ,CAClMyH,GAASS,EAAcE,KAAK,EAAIF,EAAcE,KAAK,CAAG,EACvD3P,EAAO6M,KAAK,EAAI,CAEpB7M,CAAAA,EAAOyH,QAAQ,CAAGkI,EACbF,CAAAA,EAAclW,KAAK,EAAI,EAAKkW,CAAAA,EAAcG,aAAa,EAAI,CAAA,CAAC,EACjE5P,EAAO6P,IAAI,CAACpQ,OAAO,CAAC,AAACqQ,IACbpM,GAAOrC,WACPmO,EAAS9L,EAAMqM,WAAW,EAAIrM,EAAMsM,OAAO,CACvCtM,EAAMsM,OAAO,CAACF,EAASxW,CAAC,EACxBwW,EAASxW,CAAC,CACdwW,EAAS/T,KAAK,CAAG2H,EAAMrC,SAAS,CAACmO,GACjCM,EAASG,QAAQ,CAAGH,EAAAA,EAASG,QAAQ,EAChCT,GAAU9L,EAAMuC,GAAG,EAChBuJ,GAAU9L,EAAMwC,GAAG,EAI3B4J,EAAS/T,KAAK,CAAGiE,EAAOyH,QAAQ,CAEpCqI,EAASxF,QAAQ,CAAGwF,EAASjU,KAAK,CAClCiU,EAAStF,QAAQ,CAAGsF,EAAShU,KAAK,CAClCgU,EAASI,QAAQ,CAAGJ,EAAS/T,KAAK,CAClC2T,EAAUzN,IAAI,CAAC,CACX/I,EAAG4W,EAASjU,KAAK,CACjBzC,EAAG0W,EAAShU,KAAK,CACjBxC,EAAGwW,EAAS/T,KAAK,AACrB,GACAuL,EAAWrF,IAAI,CAAC6N,EAASjU,KAAK,EAAI,EACtC,GACAmE,EAAOsH,UAAU,CAAGA,EACpB,IAAM6I,EAAkBtB,GAAqBa,EAAW/W,EAAO,CAAA,GAC/DqH,EAAO6P,IAAI,CAACpQ,OAAO,CAAC,CAACqQ,EAAU1U,KAE3B0U,EAASjU,KAAK,CAAG0T,AADjBA,CAAAA,EAAiBY,CAAe,CAAC/U,EAAE,AAAD,EACFlC,CAAC,CACjC4W,EAAShU,KAAK,CAAGyT,EAAenW,CAAC,CACjC0W,EAAS/T,KAAK,CAAGwT,EAAejW,CAAC,AACrC,EACJ,CACJ,CAMA8V,GAASjT,cAAc,CAAG8S,GAAe,AAACN,KAA2GxS,cAAc,EASnK,IAAIiU,GAA+GxZ,EAAoB,KACnIyZ,GAAmIzZ,EAAoBI,CAAC,CAACoZ,IAe7J,GAAM,CAAEnU,MAAOqU,EAAkB,CAAE,CAAIhY,IAEjC,CAAEiY,QAASC,EAAU,CAAE,CAAG/B,KAAkJgC,eAAe,GAAG3Y,SAAS,CAEvM,CAAE4Y,QAAAA,EAAO,CAAElY,KAAMmY,EAAiB,CAAE,CAAIvY,GAM9C,OAAMwY,WAAqBJ,GACvB9N,aAAc,CAMV,KAAK,IAAIvB,WAMT,IAAI,CAAC0P,KAAK,CAAG,CAAC,QAAS,MAAO,OAAO,CACrC,IAAI,CAACC,QAAQ,CAAG,QACpB,CAUAC,SAASlG,CAAI,CAAE,CACX,IAAqB3N,EAAW8T,AAAjB,IAAI,CAAoB9T,QAAQ,CAAE+T,EAAQ/T,CAAQ,CAAC8T,AAAnD,IAAI,CAAsDF,QAAQ,CAAG,OAAO,CAACjG,GAAOqG,EAAWD,EAAMC,QAAQ,CAE5H,IAAK,IAAMC,KAAQH,AAFJ,IAAI,CAEOH,KAAK,CAAE,CAC7B,IAAMO,EAAU,CACZ,MAAS,iBAAmBD,EAC5BpS,OAAQmS,CAAQ,CAACC,EAAK,EAAI,CAC9B,CACIjU,CAAAA,EAASoC,UAAU,GACf6R,AAAS,QAATA,EACAC,EAAQC,MAAM,CAAG,4BAEZF,AAAS,SAATA,GACLC,CAAAA,EAAQC,MAAM,CAAG,yBAAwB,GAGjDL,AAfW,IAAI,AAeT,CAACG,EAAK,CAAGjU,EAASgM,IAAI,CAAC+H,CAAK,CAACE,EAAK,EACnC1O,IAAI,CAAC2O,GACLtS,GAAG,CAjBG,IAAI,CAkBnB,CACAkS,AAnBe,IAAI,CAmBZvO,IAAI,CAAC,CACR,kBAAmB,QACnB1D,OAAQmS,EAASxJ,KAAK,AAC1B,GAEAsJ,AAxBe,IAAI,CAwBZM,WAAW,CAAGL,EAAMK,WAAW,AAC1C,CAKAC,qBAAqB1Z,CAAI,CAAEqM,CAAG,CAAEsN,CAAM,CAAE/S,CAAI,CAAEgT,CAAQ,CAAEC,CAAQ,CAAE,CAC9D,IAAqBC,EAAU,CAAC,EAAGC,EAAiB,CAAC,KAAM,KAAOnT,GAAQ,OAASgT,EAAUC,EAAS,CAAEG,EAAcL,GAAQN,SAC9H,GAAKM,EAIA,CAQD,IAAK,IAAML,KALPU,GAAanK,OACbsJ,AATO,IAAI,CASJvO,IAAI,CAAC,CACR1D,OAAQ8S,EAAYnK,KAAK,AAC7B,GAEelQ,OAAOsa,IAAI,CAACN,IAC3BG,CAAO,CAACR,EAAK,CAAG,CAAC,EACjBQ,CAAO,CAACR,EAAK,CAACtZ,EAAK,CAAG2Z,CAAM,CAACL,EAAK,CAE9BU,GACAF,CAAAA,CAAO,CAACR,EAAK,CAACpS,MAAM,CAAGyS,EAAON,QAAQ,CAACC,EAAK,EAAI,CAAA,CAGxDS,CAAAA,CAAc,CAAC,EAAE,CAAGD,CACxB,MApBIA,CAAO,CAAC9Z,EAAK,CAAGqM,EAChB0N,CAAc,CAAC,EAAE,CAAGD,EAoBxB,OAAO,IAAI,CAACI,YAAY,CAAC9Q,KAAK,CAvBf,IAAI,CAuBoB2Q,EAC3C,CAKAG,aAAaC,CAAK,CAAEC,CAAU,CAAExT,CAAI,CAAEgT,CAAQ,CAAEC,CAAQ,CAAE,CAEtD,IAAK,IAAMP,KAAQH,AADJ,IAAI,CACOH,KAAK,CAEvBoB,GACAD,CAAAA,EAAQrB,GAAkBsB,CAAU,CAACd,EAAK,CAAE,CAAA,EAAK,EAGjDa,AAAU,CAAA,IAAVA,GACAhB,AARO,IAAI,AAQL,CAACG,EAAK,CAAC1S,EAAK,CAACuT,EAAOP,EAAUC,GAG5C,OAXe,IAAI,AAYvB,CAKAQ,SAAU,CAEN,OADA,IAAI,CAACH,YAAY,CAAC,KAAM,KAAM,WACvB,KAAK,CAACG,SACjB,CAEAzP,KAAKoI,CAAI,CAAE3G,CAAG,CAAEwN,CAAQ,CAAES,CAAiB,CAAE,CAEzC,GAAI,AAAgB,UAAhB,OAAOtH,GAAqB,AAAe,KAAA,IAAR3G,EAAqB,CACxD,IAAM5M,EAAMuT,CAEZA,CADAA,CAAAA,EAAO,CAAC,CAAA,CACJ,CAACvT,EAAI,CAAG4M,CAChB,QACA,AAAI2G,EAAKuH,SAAS,EAAI1B,GAAQ7F,EAAK3R,CAAC,EACzB,IAAI,CAACqY,oBAAoB,CAAC,IAAK,KAAM,IAAI,CAACrU,QAAQ,CAAC,IAAI,CAAC4T,QAAQ,CAAG,OAAO,CAACjG,EAAKuH,SAAS,EAAIvH,IAEjG,KAAK,CAACpI,KAAKoI,EAAM,KAAK,EAAG6G,EAAUS,EAC9C,CACAE,QAAQxH,CAAI,CAAE4G,CAAQ,CAAEC,CAAQ,CAAE,CAC9B,GAAIhB,GAAQ7F,EAAK3R,CAAC,GAAKwX,GAAQ7F,EAAKzR,CAAC,EAAG,CACpC,IAAM6X,EAAQ,IAAI,CAAC/T,QAAQ,CAAC,IAAI,CAAC4T,QAAQ,CAAG,OAAO,CAACjG,GAAOyG,EAAcL,EAAMK,WAAW,CAC1F,IAAI,CAACC,oBAAoB,CAAC,IAAK,KAAMN,EAAO,UAAWQ,EAAUC,GACjE,IAAI,CAACjP,IAAI,CAAC,CACN1D,OAAQkS,EAAMC,QAAQ,CAACxJ,KAAK,AAChC,GAEI4J,IAAgB,IAAI,CAACA,WAAW,GAChC,IAAI,CAACA,WAAW,CAAGA,EACf,AAAC,IAAI,CAACpU,QAAQ,CAACoC,UAAU,EACzB,IAAI,CAACgT,UAAU,CAAC,IAAI,CAACpT,IAAI,EAGrC,MAEI,KAAK,CAACmT,QAAQxH,EAAM4G,EAAUC,GAElC,OAAO,IAAI,AACf,CACAY,WAAWpT,CAAI,CAAE,CAWb,OATA8R,AADe,IAAI,CACZM,WAAW,CAAGN,AADN,IAAI,CACSM,WAAW,EAAI,EAAE,CAC7CN,AAFe,IAAI,CAEZO,oBAAoB,CAAC,OAAQ,KAAM,CACtCjT,MAAOY,EAEPjB,IAAKqS,GAAmBpR,GAAMC,QAAQ,CAAC6R,AAL5B,IAAI,CAK+BM,WAAW,CAACxE,OAAO,CAAC,QAAU,EAAI,EAAI,IAAKnV,GAAG,GAC5F2M,KAAMgM,GAAmBpR,GAAMC,QAAQ,CAAC6R,AAN7B,IAAI,CAMgCM,WAAW,CAACxE,OAAO,CAAC,SAAW,EAAI,EAAI,KAAMnV,GAAG,EACnG,GAEAqZ,AATe,IAAI,CASZ9U,KAAK,CAAG8U,AATA,IAAI,CASG9R,IAAI,CAAGA,EATd,IAAI,AAWvB,CACJ,CACA0R,GAAa2B,KAAK,CAAG,CACjBC,KAAM5B,GACN6B,OAAQ7B,EACZ,EAsBA,GAAM,CAAE8B,WAAAA,EAAU,CAAE,CAAIta,IAElB,CAAE6D,MAAO0W,EAAmB,CAAE,CAAIra,IAElC,CAAEsa,OAAAA,EAAM,CAAEra,QAASsa,EAAqB,CAAE,CAAIza,IAE9C,CAAEK,YAAaqa,EAAyB,CAAE7X,UAAW8X,EAAuB,CAAE,CAAGxX,EAGjF,CAAEmV,QAASsC,EAAqB,CAAErK,OAAQsK,EAAoB,CAAExW,MAAOyW,EAAmB,CAAE1a,KAAM2a,EAAkB,CAAE,CAAI/a,IAM1H6B,GAAMD,KAAKC,GAAG,CAAEG,GAAMJ,KAAKI,GAAG,CAAEgZ,GAAKpZ,KAAKoZ,EAAE,CAAEC,GAAU,AAAC,EAAKrZ,CAAAA,KAAK2B,IAAI,CAAC,GAAK,CAAA,EAAK,EAAMyX,CAAAA,GAAK,CAAA,EAWnG,SAASE,GAAQC,CAAE,CAAEC,CAAE,CAAEC,CAAE,CAAEC,CAAE,CAAEpR,CAAK,CAAEC,CAAG,CAAEoR,CAAE,CAAEC,CAAE,EAC/C,IAAMC,EAAWtR,EAAMD,EACnBwR,EAAS,EAAE,QACf,AAAI,AAACvR,EAAMD,GAAWC,EAAMD,EAAQtI,KAAKoZ,EAAE,CAAG,EAAI,KAE9CU,EAASA,AADTA,CAAAA,EAASA,EAAOvQ,MAAM,CAAC+P,GAAQC,EAAIC,EAAIC,EAAIC,EAAIpR,EAAOA,EAAStI,KAAKoZ,EAAE,CAAG,EAAIO,EAAIC,GAAG,EACpErQ,MAAM,CAAC+P,GAAQC,EAAIC,EAAIC,EAAIC,EAAIpR,EAAStI,KAAKoZ,EAAE,CAAG,EAAI7Q,EAAKoR,EAAIC,IAG/E,AAACrR,EAAMD,GAAWA,EAAQC,EAAMvI,KAAKoZ,EAAE,CAAG,EAAI,KAE9CU,EAASA,AADTA,CAAAA,EAASA,EAAOvQ,MAAM,CAAC+P,GAAQC,EAAIC,EAAIC,EAAIC,EAAIpR,EAAOA,EAAStI,KAAKoZ,EAAE,CAAG,EAAIO,EAAIC,GAAG,EACpErQ,MAAM,CAAC+P,GAAQC,EAAIC,EAAIC,EAAIC,EAAIpR,EAAStI,KAAKoZ,EAAE,CAAG,EAAI7Q,EAAKoR,EAAIC,IAG5E,CAAC,CACA,IACAL,EAAME,EAAKzZ,KAAKC,GAAG,CAACqI,GACf,AAACmR,EAAKJ,GAAUQ,EAAY7Z,KAAKI,GAAG,CAACkI,GAAUqR,EACpDH,EAAME,EAAK1Z,KAAKI,GAAG,CAACkI,GACf,AAACoR,EAAKL,GAAUQ,EAAY7Z,KAAKC,GAAG,CAACqI,GAAUsR,EACpDL,EAAME,EAAKzZ,KAAKC,GAAG,CAACsI,GACf,AAACkR,EAAKJ,GAAUQ,EAAY7Z,KAAKI,GAAG,CAACmI,GAAQoR,EAClDH,EAAME,EAAK1Z,KAAKI,GAAG,CAACmI,GACf,AAACmR,EAAKL,GAAUQ,EAAY7Z,KAAKC,GAAG,CAACsI,GAAQqR,EAClDL,EAAME,EAAKzZ,KAAKC,GAAG,CAACsI,GAAQoR,EAC5BH,EAAME,EAAK1Z,KAAKI,GAAG,CAACmI,GAAQqR,EAC/B,CAAC,AACV,EAOA,AAAC,SAAUnd,CAAa,EAoCpB,SAASsd,EAAWrb,CAAM,CAAEsb,CAAM,EAC9B,IAAMF,EAAS,EAAE,CAEjB,IAAK,IAAMrZ,KAAS/B,EAChBob,EAAO7R,IAAI,CAAC,CAAC,IAAKxH,EAAMvB,CAAC,CAAEuB,EAAMrB,CAAC,CAAC,EAUvC,OARIV,EAAO4C,MAAM,GAEbwY,CAAM,CAAC,EAAE,CAAC,EAAE,CAAG,IAEXE,GACAF,EAAO7R,IAAI,CAAC,CAAC,IAAI,GAGlB6R,CACX,CAEA,SAAS1K,EAAe1Q,CAAM,EAC1B,IAAMob,EAAS,EAAE,CACbG,EAAI,CAAA,EACR,IAAK,IAAMxZ,KAAS/B,EAChBob,EAAO7R,IAAI,CAACgS,EAAI,CAAC,IAAKxZ,EAAMvB,CAAC,CAAEuB,EAAMrB,CAAC,CAAC,CAAG,CAAC,IAAKqB,EAAMvB,CAAC,CAAEuB,EAAMrB,CAAC,CAAC,EACjE6a,EAAI,CAACA,EAET,OAAOH,CACX,CAOA,SAASI,EAAOrJ,CAAI,EAChB,IAAM3N,EAAW,IAAI,CAAEiX,EAAejX,EAASqT,OAAO,CAACzY,SAAS,CAAEsM,EAAMlH,EAASkX,aAAa,CAAC,QA0C/F,OAzCAhQ,EAAIlJ,QAAQ,CAAG,EAAE,CACjBkJ,EAAIxL,cAAc,CAAG,CAAA,EACrBwL,EAAIhF,OAAO,CAAG,CAAA,EAEdgF,EAAI3B,IAAI,CAAG,SAAU4R,CAAI,EACrB,GAAI,AAAgB,UAAhB,OAAOA,GACNrB,CAAAA,GAAsBqB,EAAKjV,OAAO,GAC/B4T,GAAsBqB,EAAKnZ,QAAQ,GACnC8X,GAAsBqB,EAAKzb,cAAc,CAAA,EAAI,CACjD,IAAI,CAACwG,OAAO,CAAG+T,GAAmBkB,EAAKjV,OAAO,CAAE,IAAI,CAACA,OAAO,EAC5D,IAAI,CAAClE,QAAQ,CAAGiY,GAAmBkB,EAAKnZ,QAAQ,CAAE,IAAI,CAACA,QAAQ,EAC/D,IAAI,CAACtC,cAAc,CAAGua,GAAmBkB,EAAKzb,cAAc,CAAE,IAAI,CAACA,cAAc,EACjF,OAAOyb,EAAKjV,OAAO,CACnB,OAAOiV,EAAKnZ,QAAQ,CACpB,OAAOmZ,EAAKzb,cAAc,CAC1B,IAAMD,EAAQia,EAAM,CAAC1V,EAASoX,UAAU,CAAC,CAAEC,EAAazB,GAA0B,IAAI,CAAC5X,QAAQ,CAAEvC,EAAO,IAAI,CAACC,cAAc,EAAGsQ,EAAOhM,EAAS6W,UAAU,CAACQ,EAAY,CAAA,GAAOpZ,EAAO4X,GAAwBwB,EAC3MF,CAAAA,EAAKld,CAAC,CAAG+R,EACTmL,EAAKG,UAAU,CAAG,AAAC,IAAI,CAACpV,OAAO,EAAIjE,EAAO,EACtC,UAAY,QACpB,CACA,OAAOgZ,EAAa1R,IAAI,CAACxB,KAAK,CAAC,IAAI,CAAEE,UACzC,EACAiD,EAAIiO,OAAO,CAAG,SAAUoC,CAAM,EAC1B,GAAI,AAAkB,UAAlB,OAAOA,GACNzB,CAAAA,GAAsByB,EAAOrV,OAAO,GACjC4T,GAAsByB,EAAOvZ,QAAQ,GACrC8X,GAAsByB,EAAO7b,cAAc,CAAA,EAAI,CACnD,IAAI,CAACwG,OAAO,CAAG+T,GAAmBsB,EAAOrV,OAAO,CAAE,IAAI,CAACA,OAAO,EAC9D,IAAI,CAAClE,QAAQ,CAAGiY,GAAmBsB,EAAOvZ,QAAQ,CAAE,IAAI,CAACA,QAAQ,EACjE,IAAI,CAACtC,cAAc,CAAGua,GAAmBsB,EAAO7b,cAAc,CAAE,IAAI,CAACA,cAAc,EACnF,OAAO6b,EAAOrV,OAAO,CACrB,OAAOqV,EAAOvZ,QAAQ,CACtB,OAAOuZ,EAAO7b,cAAc,CAC5B,IAAMD,EAAQia,EAAM,CAAC1V,EAASoX,UAAU,CAAC,CAAEC,EAAazB,GAA0B,IAAI,CAAC5X,QAAQ,CAAEvC,EAAO,IAAI,CAACC,cAAc,EAAGsQ,EAAOhM,EAAS6W,UAAU,CAACQ,EAAY,CAAA,GAAOpZ,EAAO4X,GAAwBwB,GAAaC,EAAa,AAAC,IAAI,CAACpV,OAAO,EAAIjE,EAAO,EACzP,UAAY,QAChBsZ,CAAAA,EAAOtd,CAAC,CAAG+R,EACX,IAAI,CAACzG,IAAI,CAAC,aAAc+R,EAC5B,CACA,OAAOL,EAAa9B,OAAO,CAACpR,KAAK,CAAC,IAAI,CAAEE,UAC5C,EAEOiD,EAAI3B,IAAI,CAACoI,EACpB,CAOA,SAAShM,EAAWgM,CAAI,EACpB,IAAM3N,EAAW,IAAI,CAAEiX,EAAejX,EAASqT,OAAO,CAACzY,SAAS,CAAEgc,EAAS5W,EAASwX,CAAC,GAAIxC,EAAU4B,EAAO5B,OAAO,CA+CjH,OA9CI,AAAC,IAAI,CAAC5S,UAAU,EAChBwU,EAAOrR,IAAI,CAAC,CACR,kBAAmB,OACvB,GAEJqR,EAAO7U,KAAK,CAAG,EAAE,CAEjB6U,EAAO5B,OAAO,CAAG,WACb,IAAK,IAAI9W,EAAI,EAAGA,EAAI0Y,EAAO7U,KAAK,CAAC3D,MAAM,CAAEF,IACrC0Y,EAAO7U,KAAK,CAAC7D,EAAE,CAAC8W,OAAO,GAE3B,OAAOA,EAAQla,IAAI,CAAC,IAAI,CAC5B,EACA8b,EAAOrR,IAAI,CAAG,SAAU4R,CAAI,CAAEnQ,CAAG,CAAEwN,CAAQ,CAAES,CAAiB,EAC1D,GAAI,AAAgB,UAAhB,OAAOkC,GAAqBrB,GAAsBqB,EAAKpV,KAAK,EAAG,CAC/D,KAAO6U,EAAO7U,KAAK,CAAC3D,MAAM,CAAG+Y,EAAKpV,KAAK,CAAC3D,MAAM,EAC1CwY,EAAO7U,KAAK,CAAC0V,GAAG,GAAGzC,OAAO,GAE9B,KAAO4B,EAAO7U,KAAK,CAAC3D,MAAM,CAAG+Y,EAAKpV,KAAK,CAAC3D,MAAM,EAC1CwY,EAAO7U,KAAK,CAACgD,IAAI,CAAC/E,EAASgX,MAAM,GAAGpV,GAAG,CAACgV,IAE5C,IAAK,IAAI1Y,EAAI,EAAGA,EAAIiZ,EAAKpV,KAAK,CAAC3D,MAAM,CAAEF,IAC/B8B,EAASoC,UAAU,EACnB,OAAO+U,EAAKpV,KAAK,CAAC7D,EAAE,CAAC8D,IAAI,CAE7B4U,EAAO7U,KAAK,CAAC7D,EAAE,CAACqH,IAAI,CAAC4R,EAAKpV,KAAK,CAAC7D,EAAE,CAAE,KAAMsW,EAAUS,EAExD,QAAOkC,EAAKpV,KAAK,AACrB,CACA,OAAOkV,EAAa1R,IAAI,CAACxB,KAAK,CAAC,IAAI,CAAEE,UACzC,EACA2S,EAAOzB,OAAO,CAAG,SAAUoC,CAAM,CAAEhD,CAAQ,CAAEC,CAAQ,EACjD,GAAI+C,GAAQxV,MAAO,CACf,KAAO6U,EAAO7U,KAAK,CAAC3D,MAAM,CAAGmZ,EAAOxV,KAAK,CAAC3D,MAAM,EAC5CwY,EAAO7U,KAAK,CAAC0V,GAAG,GAAGzC,OAAO,GAE9B,KAAO4B,EAAO7U,KAAK,CAAC3D,MAAM,CAAGmZ,EAAOxV,KAAK,CAAC3D,MAAM,EAC5CwY,EAAO7U,KAAK,CAACgD,IAAI,CAAC/E,EAASgX,MAAM,GAAGpV,GAAG,CAACgV,IAE5C,IAAK,IAAI1Y,EAAI,EAAGA,EAAIqZ,EAAOxV,KAAK,CAAC3D,MAAM,CAAEF,IACrC0Y,EAAO7U,KAAK,CAAC7D,EAAE,CAACiX,OAAO,CAACoC,EAAOxV,KAAK,CAAC7D,EAAE,CAAEqW,EAAUC,EAEvD,QAAO+C,EAAOxV,KAAK,AACvB,CACA,OAAOkV,EAAa9B,OAAO,CAACpR,KAAK,CAAC,IAAI,CAAEE,UAC5C,EACO2S,EAAOrR,IAAI,CAACoI,EACvB,CAMA,SAAS+J,EAAU7X,CAAI,CAAEqV,CAAS,EAC9B,IAAMpB,EAAS,IAAI6D,AA1P4BjE,GA0PX2B,KAAK,CAACxV,EAAK,CAAC,IAAI,CAAE,KAEtD,OADAiU,EAAOD,QAAQ,CAACqB,GACTpB,CACX,CAKA,SAASyB,EAAOL,CAAS,EACrB,OAAO,IAAI,CAACwC,SAAS,CAAC,SAAUxC,EACpC,CAKA,SAAS0C,EAAW1C,CAAS,EACzB,IAAMlZ,EAAIkZ,EAAUlZ,CAAC,EAAI,EAAGE,EAAIgZ,EAAUhZ,CAAC,EAAI,EAAGE,EAAI8Y,EAAU9Y,CAAC,EAAI,EAIrEyb,EAAI3C,EAAU4C,MAAM,EAAI,EAAGC,EAAI7C,EAAU8C,KAAK,EAAI,EAAG/d,EAAIib,EAAU7Y,KAAK,EAAI,EAAGZ,EAAQia,EAAM,CAAC,IAAI,CAAC0B,UAAU,CAAC,CAA6Cza,EAAQf,AAAvCH,EAAMI,OAAO,CAACJ,KAAK,CAACG,SAAS,CAAoBe,KAAK,CAM/IyX,EAAc,EAAE,CAC/C6D,EAAOpW,EAAS,EAEpBoK,EAAO,CAAC,CACAjQ,EAAGA,EACHE,EAAGA,EACHE,EAAGA,CACP,EAAG,CACCJ,EAAGA,EAAI+b,EACP7b,EAAGA,EACHE,EAAGA,CACP,EAAG,CACCJ,EAAGA,EAAI+b,EACP7b,EAAGA,EAAI2b,EACPzb,EAAGA,CACP,EAAG,CACCJ,EAAGA,EACHE,EAAGA,EAAI2b,EACPzb,EAAGA,CACP,EAAG,CACCJ,EAAGA,EACHE,EAAGA,EAAI2b,EACPzb,EAAGA,EAAInC,CACX,EAAG,CACC+B,EAAGA,EAAI+b,EACP7b,EAAGA,EAAI2b,EACPzb,EAAGA,EAAInC,CACX,EAAG,CACC+B,EAAGA,EAAI+b,EACP7b,EAAGA,EACHE,EAAGA,EAAInC,CACX,EAAG,CACC+B,EAAGA,EACHE,EAAGA,EACHE,EAAGA,EAAInC,CACX,EAAE,CAENgS,EAAO2J,GAA0B3J,EAAMxQ,EAAOyZ,EAAUxZ,cAAc,EAKtE,IAAMwc,EAAc,AAACha,GAIjB,AAAI2Z,AAAM,IAANA,GAAW3Z,EAAI,GAAKA,EAAI,EACjB,CACHlC,EAAGiQ,CAAI,CAAC/N,EAAE,CAAClC,CAAC,CAIZE,EAAG+P,CAAI,CAAC/N,EAAE,CAAChC,CAAC,CAAG,GACfE,EAAG6P,CAAI,CAAC/N,EAAE,CAAC9B,CAAC,AAChB,EAMA6P,CAAI,CAAC,EAAE,CAACjQ,CAAC,GAAKiQ,CAAI,CAAC,EAAE,CAACjQ,CAAC,EAAIkC,GAAK,EACzB,CACHlC,EAAGiQ,CAAI,CAAC/N,EAAE,CAAClC,CAAC,CAAG,GAIfE,EAAG+P,CAAI,CAAC/N,EAAE,CAAChC,CAAC,CACZE,EAAG6P,CAAI,CAAC/N,EAAE,CAAC9B,CAAC,AAChB,EAGAnC,AAAM,IAANA,GAAWiE,EAAI,GAAKA,EAAI,EACjB,CACHlC,EAAGiQ,CAAI,CAAC/N,EAAE,CAAClC,CAAC,CAIZE,EAAG+P,CAAI,CAAC/N,EAAE,CAAChC,CAAC,CACZE,EAAG6P,CAAI,CAAC/N,EAAE,CAAC9B,CAAC,CAAG,EACnB,EAEG6P,CAAI,CAAC/N,EAAE,CAMlBia,EAAU,AAACja,GAAO+N,CAAI,CAAC/N,EAAE,CAUzBka,EAAY,CAACC,EAAgBC,EAAgBlR,KACzC,IACAE,EAAQ+Q,EAAe/a,GAAG,CAAC6a,GAAU5Q,EAAQ+Q,EAAehb,GAAG,CAAC6a,GAKhEI,EAAaF,EAAe/a,GAAG,CAAC4a,GAAcM,EAAaF,EAAehb,GAAG,CAAC4a,GAC1EhR,EAAM,CAAC,EAAE,CAAE,GAAG,CAmBlB,OAlBI2O,AAAiC,EAAjCA,GAAwBvO,GACxBJ,EAAM,CAACI,EAAO,EAAE,CAEXuO,AAAiC,EAAjCA,GAAwBtO,GAC7BL,EAAM,CAACK,EAAO,EAAE,CAEXH,IACLgN,EAAYrP,IAAI,CAACqC,GAEbF,EADA2O,AAAsC,EAAtCA,GAAwB0C,GAClB,CAACjR,EAAO,EAAE,CAEXuO,AAAsC,EAAtCA,GAAwB2C,GACvB,CAACjR,EAAO,EAAE,CAGV,CAACD,EAAO,EAAE,EAGjBJ,CACX,EAIMuR,EAAQR,AADdA,CAAAA,EAAQG,EADM,CAAC,EAAG,EAAG,EAAG,EAAE,CAAS,CAAC,EAAG,EAAG,EAAG,EAAE,CAChB,QAAO,CACnB,CAAC,EAAE,CAAEM,EAAUT,CAAK,CAAC,EAAE,CAIpCU,EAAQV,AADdA,CAAAA,EAAQG,EADI,CAAC,EAAG,EAAG,EAAG,EAAE,CAAW,CAAC,EAAG,EAAG,EAAG,EAAE,CAChB,MAAK,CACjB,CAAC,EAAE,CAAEQ,EAAQX,CAAK,CAAC,EAAE,CAIlCY,EAAQZ,AADdA,CAAAA,EAAQG,EADM,CAAC,EAAG,EAAG,EAAG,EAAE,CAAS,CAAC,EAAG,EAAG,EAAG,EAAE,CAChB,OAAM,CAClB,CAAC,EAAE,CAAEU,EAAUb,CAAK,CAAC,EAAE,CA2B1C,OAlBIa,AAAY,IAAZA,EAGAjX,GAAUkX,AAxJD,IAwJetd,CAAAA,EAAMQ,SAAS,CAAGD,CAAAA,EAErC,AAAC8c,GACNjX,CAAAA,GAAUkX,AA3JD,IA2Jc/c,CAAAA,EAE3B6F,GAAUmX,AA1JG,GA0JW,CAAA,CAACJ,GAEpBjc,GAAS,GAAKA,GAAS,KAAOA,EAAQ,KAAOA,EAAQ,MACtDlB,EAAMU,UAAU,CAAGD,EAAI,GAAKA,CAAAA,EAC5Bwc,AAAY,IAAZA,EACA7W,GAAUoX,AA/JgB,IA+JF7c,EAEnB,AAACsc,GACN7W,CAAAA,GAAUoX,AAlKgB,IAkKF,CAAA,IAAO7c,CAAAA,CAAC,EAE7B,CACHgF,MAAO,IAAI,CAACyV,UAAU,CAAC4B,EAAO,CAAA,GAC9B1X,IAAK,IAAI,CAAC8V,UAAU,CAAC8B,EAAO,CAAA,GAC5BvR,KAAM,IAAI,CAACyP,UAAU,CAACgC,EAAO,CAAA,GAC7B7E,SAAU,CACNxJ,MAAO1N,KAAKkN,KAAK,CAACnI,EACtB,EACAuS,YAAaA,EAEbsE,QAASA,EACTE,MAAOA,CACX,CACJ,CAEA,SAASM,EAAMhF,CAAO,EAClB,IAAuBiF,EAAUnZ,AAAhB,IAAI,CAAqBwX,CAAC,GAAIP,EAAejX,AAA7C,IAAI,CAAkDqT,OAAO,CAACzY,SAAS,CAAEwe,EAAgB,CACtG,QAAS,OACT,IAAK,IAAK,IAAK,SAAU,QAAS,MAAO,QAC5C,CAMD,SAASC,EAAc9B,CAAM,EACzB,IAEInd,EAFEkf,EAAK,CAAC,EAGZ,IAAKlf,KAFLmd,EAASvB,GAAoBuB,GAGU,KAA/B6B,EAAcxJ,OAAO,CAACxV,KACtBkf,CAAE,CAAClf,EAAI,CAAGmd,CAAM,CAACnd,EAAI,CACrB,OAAOmd,CAAM,CAACnd,EAAI,EAG1B,MAAOE,EAAAA,OAAOsa,IAAI,CAAC0E,GAAIlb,MAAM,EAAG,CAACkb,EAAI/B,EAAO,AAChD,CA0BA,IAAK,IAAMgC,KAxBXrF,AADAA,CAAAA,EAAU8B,GAAoB9B,EAAO,EAC7BvX,KAAK,CAAG,AAACuX,CAAAA,EAAQvX,KAAK,EAAI,CAAA,EAAKgZ,GACvCzB,EAAQxX,IAAI,CAAG,AAACwX,CAAAA,EAAQxX,IAAI,EAAI,CAAA,EAAKiZ,GAErCwD,EAAQpY,GAAG,CAAGf,AAzBG,IAAI,CAyBEgM,IAAI,GAC3BmN,EAAQK,KAAK,CAAGxZ,AA1BC,IAAI,CA0BIgM,IAAI,GAC7BmN,EAAQM,KAAK,CAAGzZ,AA3BC,IAAI,CA2BIgM,IAAI,GAC7BmN,EAAQO,GAAG,CAAG1Z,AA5BG,IAAI,CA4BEgM,IAAI,GAC3BmN,EAAQQ,GAAG,CAAG3Z,AA7BG,IAAI,CA6BEgM,IAAI,GAG3BmN,EAAQS,KAAK,CAAG,WACZ,IAAMC,EAASV,EAAQW,WAAW,CAAEvV,EAAY4U,EAAQ5T,IAAI,CAAC,SAI7D,IAAK,IAAMwU,KAHXZ,EAAQpY,GAAG,CAACa,GAAG,CAACuX,GAGG,CAAC,MAAO,MAAO,QAAS,QAAQ,EAC/CA,CAAO,CAACY,EAAK,CACRxU,IAAI,CAAC,CACN,MAAShB,EAAY,qBACzB,GACK3C,GAAG,CAACiY,EAEjB,EAEiB,CAAC,WAAY,cAAc,EACxCV,CAAO,CAACI,EAAG,CAAG,WACV,IAAM5L,EAAO1J,UACb,IAAK,IAAM8V,IAAQ,CAAC,MAAO,MAAO,MAAO,QAAS,QAAQ,CACtDZ,CAAO,CAACY,EAAK,CAACR,EAAG,CAACxV,KAAK,CAACoV,CAAO,CAACY,EAAK,CAAEpM,EAE/C,EAwCJ,IAAK,IAAMqM,KAlCXb,EAAQc,QAAQ,CAAG,SAAU/F,CAAO,EAChC,IAAMH,EAAQoF,EAAQnZ,QAAQ,CAACka,SAAS,CAAChG,GAAUrS,EAASkS,AAAa,IAAbA,EAAMoG,IAAI,AACtEhB,CAAAA,EAAQjF,OAAO,CAAGA,EAClBiF,EAAQpY,GAAG,CAACwE,IAAI,CAAC,CAAEtL,EAAG8Z,EAAMhT,GAAG,CAAEc,OAAQkS,EAAMoG,IAAI,AAAC,GACpDhB,EAAQO,GAAG,CAACnU,IAAI,CAAC,CAAEtL,EAAG8Z,EAAM2F,GAAG,CAAE7X,OAAQkS,EAAMqG,IAAI,AAAC,GACpDjB,EAAQQ,GAAG,CAACpU,IAAI,CAAC,CAAEtL,EAAG8Z,EAAM4F,GAAG,CAAE9X,OAAQkS,EAAMsG,IAAI,AAAC,GACpDlB,EAAQK,KAAK,CAACjU,IAAI,CAAC,CAAEtL,EAAG8Z,EAAMyF,KAAK,CAAE3X,OAAQkS,EAAMuG,MAAM,AAAC,GAC1DnB,EAAQM,KAAK,CAAClU,IAAI,CAAC,CAAEtL,EAAG8Z,EAAM0F,KAAK,CAAE5X,OAAQkS,EAAMwG,MAAM,AAAC,GAE1DpB,EAAQtX,MAAM,CAAGA,EACjBsX,EAAQ5T,IAAI,CAAC,CAAE1D,OAAQA,CAAO,GAE1BqS,EAAQsG,MAAM,GACdrB,EAAQpY,GAAG,CAAC0Z,kBAAkB,CAACvG,EAAQsG,MAAM,EAC7C,OAAOtG,EAAQsG,MAAM,CAE7B,EACArB,EAAQc,QAAQ,CAAC/F,GAKjBiF,EAAQ/D,UAAU,CAAG,SAAUpH,CAAK,EAChC,IAAM0M,EAASjF,GAAoBzH,GAAO/L,QAAQ,CAAC,KAAMxH,GAAG,GAO5D,OANA,IAAI,CAACuH,IAAI,CAAGgM,EACZ,IAAI,CAACwL,KAAK,CAACjU,IAAI,CAAC,CAAEvD,KAAM0Y,CAAO,GAC/B,IAAI,CAACjB,KAAK,CAAClU,IAAI,CAAC,CAAEvD,KAAM0Y,CAAO,GAC/B,IAAI,CAAChB,GAAG,CAACnU,IAAI,CAAC,CAAEvD,KAAM0Y,CAAO,GAC7B,IAAI,CAACf,GAAG,CAACpU,IAAI,CAAC,CAAEvD,KAAM0Y,CAAO,GAC7B,IAAI,CAAC3Z,GAAG,CAACwE,IAAI,CAAC,CAAEvD,KAAMgM,CAAM,GACrB,IAAI,AACf,EAGqB,CAAC,UAAW,aAAc,aAAc,aAAa,EACtEmL,CAAO,CAACa,EAAS,SAAS,CAAG,SAAUhM,CAAK,CAAE5T,CAAG,EAE7C,IAAK,IAAMugB,KADXxB,CAAO,CAAC/e,EAAI,CAAG4T,EACE,CAAC,MAAO,MAAO,QAAS,QAAS,MAAM,EACpDmL,CAAO,CAACwB,EAAG,CAACpV,IAAI,CAACnL,EAAK4T,EAE9B,EA0FJ,OAtFAmL,EAAQ5T,IAAI,CAAG,SAAUgS,CAAM,EAC3B,GAAI,AAAkB,UAAlB,OAAOA,EAAqB,CAC5B,IAAMqD,EAAWvB,EAAc9B,GAC/B,GAAIqD,EAAU,CACV,IAAMtB,EAAKsB,CAAQ,CAAC,EAAE,AACtB3W,CAAAA,SAAS,CAAC,EAAE,CAAG2W,CAAQ,CAAC,EAAE,CAEtBtB,AAAa,KAAK,IAAlBA,EAAG3c,KAAK,EACR2c,CAAAA,EAAG3c,KAAK,EAAIgZ,EAAoB,EAEhC2D,AAAY,KAAK,IAAjBA,EAAG5c,IAAI,EACP4c,CAAAA,EAAG5c,IAAI,EAAIiZ,EAAoB,EAEnCI,GAAqBoD,EAAQjF,OAAO,CAAEoF,GAClCH,EAAQjF,OAAO,EACfiF,EAAQc,QAAQ,CAACd,EAAQjF,OAAO,CAExC,CACJ,CACA,OAAO+C,EAAa1R,IAAI,CAACxB,KAAK,CAACoV,EAASlV,UAC5C,EAIAkV,EAAQhE,OAAO,CAAG,SAAUoC,CAAM,CAAEsD,CAAS,CAAErG,CAAQ,EACnD,IAAM5G,EAAO,IAAI,CAACsG,OAAO,CAAE4G,EAAa,QACpChe,KAAKie,MAAM,GAAGC,QAAQ,CAAC,IAAIC,SAAS,CAAC,EAAG,EAG5C,QAAO1D,EAAOiD,MAAM,CACpB,OAAOjD,EAAOnb,CAAC,CACf,IAAM8e,EAAO1F,GAAWS,GAAmB4E,EAAW,IAAI,CAAC7a,QAAQ,CAACmb,eAAe,GACnF,GAAID,EAAK3G,QAAQ,CAAE,CACf,IAAMqG,EAAWvB,EAAc9B,GAM/B,GAHA4B,CAAO,CAAC2B,EAAW,CAAG,EACtBvD,CAAM,CAACuD,EAAW,CAAG,EACrB3B,CAAO,CAAC2B,EAAa,SAAS,CAAG,AAAC5f,IAA+EkgB,IAAI,CACjHR,EAAU,CACV,IAAM/M,EAAK+M,CAAQ,CAAC,EAAE,CACtBS,EAAc,CAACjhB,EAAK+K,IAASyI,CAAI,CAACxT,EAAI,CAAG,AAAC6b,CAAAA,GAAmBpI,CAAE,CAACzT,EAAI,CAAEwT,CAAI,CAACxT,EAAI,EAC3EwT,CAAI,CAACxT,EAAI,AAAD,EAAK+K,CACjB+V,CAAAA,EAAKI,IAAI,CAAG,SAAUphB,CAAC,CAAEqhB,CAAE,EACnBA,EAAG5gB,IAAI,GAAKmgB,GACZS,EAAGjW,IAAI,CAAC2U,QAAQ,CAACjE,GAAoBpI,EAAM,CACvC5R,EAAGqf,EAAY,IAAKE,EAAGpW,GAAG,EAC1BjJ,EAAGmf,EAAY,IAAKE,EAAGpW,GAAG,EAC1BqW,EAAGH,EAAY,IAAKE,EAAGpW,GAAG,EAC1BsW,OAAQJ,EAAY,SAAUE,EAAGpW,GAAG,EACpCC,MAAOiW,EAAY,QAASE,EAAGpW,GAAG,EAClCE,IAAKgW,EAAY,MAAOE,EAAGpW,GAAG,EAC9B9I,MAAOgf,EAAY,QAASE,EAAGpW,GAAG,CACtC,GAER,CACJ,CACA0V,EAAYK,CAChB,CACA,OAAOjE,EAAa9B,OAAO,CAACra,IAAI,CAAC,IAAI,CAAEyc,EAAQsD,EAAWrG,EAC9D,EAEA2E,EAAQnE,OAAO,CAAG,WAMd,OALA,IAAI,CAACjU,GAAG,CAACiU,OAAO,GAChB,IAAI,CAAC2E,GAAG,CAAC3E,OAAO,GAChB,IAAI,CAAC0E,GAAG,CAAC1E,OAAO,GAChB,IAAI,CAACwE,KAAK,CAACxE,OAAO,GAClB,IAAI,CAACyE,KAAK,CAACzE,OAAO,GACXiC,EAAajC,OAAO,CAACla,IAAI,CAAC,IAAI,CACzC,EAEAqe,EAAQuC,IAAI,CAAG,WACX,IAAI,CAAC3a,GAAG,CAAC2a,IAAI,GACb,IAAI,CAAC/B,GAAG,CAAC+B,IAAI,GACb,IAAI,CAAChC,GAAG,CAACgC,IAAI,GACb,IAAI,CAAClC,KAAK,CAACkC,IAAI,GACf,IAAI,CAACjC,KAAK,CAACiC,IAAI,EACnB,EACAvC,EAAQwC,IAAI,CAAG,SAAUC,CAAO,EAC5B,IAAI,CAAC7a,GAAG,CAAC4a,IAAI,CAACC,GACd,IAAI,CAACjC,GAAG,CAACgC,IAAI,CAACC,GACd,IAAI,CAAClC,GAAG,CAACiC,IAAI,CAACC,GACd,IAAI,CAACpC,KAAK,CAACmC,IAAI,CAACC,GAChB,IAAI,CAACnC,KAAK,CAACkC,IAAI,CAACC,EACpB,EAEOzC,CACX,CAKA,SAASe,EAAUhF,CAAS,EACxB,IAAMmB,EAAKnB,EAAUlZ,CAAC,EAAI,EAC1Bsa,EAAKpB,EAAUhZ,CAAC,EAAI,EACpBkJ,EAAQ8P,EAAU9P,KAAK,EAAI,EAC3BC,EAAM,AAAC6P,CAAAA,EAAU7P,GAAG,EAAI,CAAA,EAAK,KAC7BmW,EAAItG,EAAUsG,CAAC,EAAI,EACnBK,EAAK3G,EAAUuG,MAAM,EAAI,EACzBxhB,EAAIib,EAAU7Y,KAAK,EAAI,EACvBM,EAAQuY,EAAUvY,KAAK,EAAI,EAC3BD,EAAOwY,EAAUxY,IAAI,EAAI,EAEnBof,EAAKhf,KAAKC,GAAG,CAACqI,GACpB2W,EAAKjf,KAAKI,GAAG,CAACkI,GACd4W,EAAKlf,KAAKC,GAAG,CAACsI,GACd4W,EAAKnf,KAAKI,GAAG,CAACmI,GACdkR,EAAKiF,EAAI1e,KAAKC,GAAG,CAACL,GAClB8Z,EAAKgF,EAAI1e,KAAKC,GAAG,CAACJ,GAClBuf,EAAML,EAAK/e,KAAKC,GAAG,CAACL,GACpByf,EAAMN,EAAK/e,KAAKC,GAAG,CAACJ,GACpB8Z,EAAKxc,EAAI6C,KAAKI,GAAG,CAACR,GAClBga,EAAKzc,EAAI6C,KAAKI,GAAG,CAACP,GAEdoE,EAAM,CACN,CAAC,IAAKsV,EAAME,EAAKuF,EAAKxF,EAAME,EAAKuF,EAAI,CACxC,CAEDhb,AADAA,CAAAA,EAAMA,EAAIsF,MAAM,CAAC+P,GAAQC,EAAIC,EAAIC,EAAIC,EAAIpR,EAAOC,EAAK,EAAG,GAAE,EACtDN,IAAI,CAAC,CACL,IAAKsR,EAAM6F,EAAMF,EAAK1F,EAAM6F,EAAMF,EACrC,EAEDlb,AADAA,CAAAA,EAAMA,EAAIsF,MAAM,CAAC+P,GAAQC,EAAIC,EAAI4F,EAAKC,EAAK9W,EAAKD,EAAO,EAAG,GAAE,EACxDL,IAAI,CAAC,CAAC,IAAI,EAEd,IAAMqX,EAAK1f,EAAO,EAAII,KAAKoZ,EAAE,CAAG,EAAI,EAAIhc,EAAKyC,EAAQ,EAAI,EAAIG,KAAKoZ,EAAE,CAAG,EACjEmG,EAASjX,EAAQ,CAACgX,EAAIhX,EAASC,EAAM,CAAC+W,EAAI,CAACA,EAAIhX,EAAQkX,EAAOjX,EAAM6Q,GAAKhc,EAAImL,EAAOD,EAAQ8Q,GAAKhc,EAAIgc,GAAKhc,EAAImL,EAAMkX,EAAS,EAAIrG,GAAKhc,EAwBxIyf,EAAM,CACN,CAAC,IAAKtD,EAAME,EAAKxZ,GAAIsf,GAAU/F,EAAME,EAAKtZ,GAAImf,GAAS,CAC1D,CACD1C,EAAMA,EAAItT,MAAM,CAAC+P,GAAQC,EAAIC,EAAIC,EAAIC,EAAI6F,EAAQC,EAAM,EAAG,IAGtDjX,EAAMkX,GAAUnX,EAAQmX,GAExB5C,EAAI5U,IAAI,CAAC,CACL,IAAKsR,EAAME,EAAKxZ,GAAIuf,GAAS7F,EAAIH,EAAME,EAAKtZ,GAAIof,GAAS5F,EAC5D,EAIDiD,AAFAA,CAAAA,EAAMA,EAAItT,MAAM,CAAC+P,GAAQC,EAAIC,EAAIC,EAAIC,EAAI8F,EAAMC,EAAQ9F,EAAIC,GAAG,EAE1D3R,IAAI,CAAC,CACL,IAAKsR,EAAME,EAAKxZ,GAAIwf,GAAUjG,EAAME,EAAKtZ,GAAIqf,GAChD,EAID5C,AAFAA,CAAAA,EAAMA,EAAItT,MAAM,CAAC+P,GAAQC,EAAIC,EAAIC,EAAIC,EAAI+F,EAAQlX,EAAK,EAAG,GAAE,EAEvDN,IAAI,CAAC,CACL,IAAKsR,EAAME,EAAKxZ,GAAIsI,GAAQoR,EAAIH,EAAME,EAAKtZ,GAAImI,GAAQqR,EAC1D,EAGDiD,AADAA,CAAAA,EAAMA,EAAItT,MAAM,CAAC+P,GAAQC,EAAIC,EAAIC,EAAIC,EAAInR,EAAKkX,EAAQ9F,EAAIC,GAAG,EACzD3R,IAAI,CAAC,CACL,IAAKsR,EAAME,EAAKxZ,GAAIwf,GAAUjG,EAAME,EAAKtZ,GAAIqf,GAChD,EAED5C,EAAMA,EAAItT,MAAM,CAAC+P,GAAQC,EAAIC,EAAIC,EAAIC,EAAI+F,EAAQD,EAAM,EAAG,KAGrDjX,EAAM6Q,GAAKhc,GAAKkL,EAAQ8Q,GAAKhc,IAElCyf,EAAI5U,IAAI,CAAC,CACL,IACAsR,EAAME,EAAKzZ,KAAKC,GAAG,CAACuf,GAAS7F,EAC7BH,EAAME,EAAK1Z,KAAKI,GAAG,CAACof,GAAS5F,EAChC,EAIDiD,AAFAA,CAAAA,EAAMA,EAAItT,MAAM,CAAC+P,GAAQC,EAAIC,EAAIC,EAAIC,EAAI8F,EAAMjX,EAAKoR,EAAIC,GAAG,EAEvD3R,IAAI,CAAC,CACL,IAAKsR,EAAME,EAAKzZ,KAAKC,GAAG,CAACsI,GAAOiR,EAAME,EAAK1Z,KAAKI,GAAG,CAACmI,GACvD,EAEDsU,EAAMA,EAAItT,MAAM,CAAC+P,GAAQC,EAAIC,EAAIC,EAAIC,EAAInR,EAAKiX,EAAM,EAAG,KAE3D3C,EAAI5U,IAAI,CAAC,CACL,IACAsR,EAAME,EAAKzZ,KAAKC,GAAG,CAACuf,GAAS7F,EAC7BH,EAAME,EAAK1Z,KAAKI,GAAG,CAACof,GAAS5F,EAChC,EAEDiD,AADAA,CAAAA,EAAMA,EAAItT,MAAM,CAAC+P,GAAQC,EAAIC,EAAIC,EAAIC,EAAI8F,EAAMD,EAAQ5F,EAAIC,GAAG,EAC1D3R,IAAI,CAAC,CAAC,IAAI,EAEd,IAAI2U,EAAM,CACN,CAAC,IAAKrD,EAAM6F,EAAMJ,EAAKxF,EAAM6F,EAAMJ,EAAI,CAC1C,CAEDrC,AADAA,CAAAA,EAAMA,EAAIrT,MAAM,CAAC+P,GAAQC,EAAIC,EAAI4F,EAAKC,EAAK/W,EAAOC,EAAK,EAAG,GAAE,EACxDN,IAAI,CAAC,CACL,IACAsR,EAAM6F,EAAMpf,KAAKC,GAAG,CAACsI,GAAQoR,EAC7BH,EAAM6F,EAAMrf,KAAKI,GAAG,CAACmI,GAAQqR,EAChC,EAEDgD,AADAA,CAAAA,EAAMA,EAAIrT,MAAM,CAAC+P,GAAQC,EAAIC,EAAI4F,EAAKC,EAAK9W,EAAKD,EAAOqR,EAAIC,GAAG,EAC1D3R,IAAI,CAAC,CAAC,IAAI,EAEd,IAAMyU,EAAQ,CACV,CAAC,IAAKnD,EAAME,EAAKuF,EAAKxF,EAAME,EAAKuF,EAAI,CACrC,CAAC,IAAK1F,EAAME,EAAKuF,EAAMrF,EAAIH,EAAME,EAAKuF,EAAMrF,EAAG,CAC/C,CAAC,IAAKL,EAAM6F,EAAMJ,EAAMrF,EAAIH,EAAM6F,EAAMJ,EAAMrF,EAAG,CACjD,CAAC,IAAKL,EAAM6F,EAAMJ,EAAKxF,EAAM6F,EAAMJ,EAAI,CACvC,CAAC,IAAI,CACR,CACKtC,EAAQ,CACV,CAAC,IAAKpD,EAAME,EAAKyF,EAAK1F,EAAME,EAAKyF,EAAI,CACrC,CAAC,IAAK5F,EAAME,EAAKyF,EAAMvF,EAAIH,EAAME,EAAKyF,EAAMvF,EAAG,CAC/C,CAAC,IAAKL,EAAM6F,EAAMF,EAAMvF,EAAIH,EAAM6F,EAAMF,EAAMvF,EAAG,CACjD,CAAC,IAAKL,EAAM6F,EAAMF,EAAK1F,EAAM6F,EAAMF,EAAI,CACvC,CAAC,IAAI,CACR,CAGKO,EAAY1f,KAAK2f,KAAK,CAAC/F,EAAI,CAACD,GAC9BiG,EAAW5f,KAAKmM,GAAG,CAAC5D,EAAMmX,GAAYG,EAAa7f,KAAKmM,GAAG,CAAC7D,EAAQoX,GAAYI,EAAW9f,KAAKmM,GAAG,CAAC,AAAC7D,CAAAA,EAAQC,CAAE,EAAK,EAAImX,GAK5H,SAASK,EAAcC,CAAK,EAKxB,MAHIA,AADJA,CAAAA,GAAiB,EAAIhgB,KAAKoZ,EAAE,EAChBpZ,KAAKoZ,EAAE,EACf4G,CAAAA,EAAQ,EAAIhgB,KAAKoZ,EAAE,CAAG4G,CAAI,EAEvBA,CACX,CACAJ,EAAWG,EAAcH,GACzBC,EAAaE,EAAcF,GAG3B,IAA0BI,EAAKH,AAAV,IAFrBA,CAAAA,EAAWC,EAAcD,EAAQ,EAEuBI,EAAKL,AAAxC,IAAwCA,EAA2BM,EAAKP,AAAxE,IAAwEA,EAC7F,MAAO,CACH3b,IAAKA,EAELoZ,KAAMrd,AAJW,IAIXA,KAAKoZ,EAAE,CAAkB,EAC/ByD,IAAKA,EACLU,KAAMvd,KAAKkM,GAAG,CAAC+T,EAAIC,EAAIC,GACvBvD,IAAKA,EACLU,KAAMtd,KAAKkM,GAAG,CAAC+T,EAAIC,EAAIC,GACvBzD,MAAOA,EAEPc,OAAQ2C,AAAK,IAALA,EACRxD,MAAOA,EACPc,OAAQyC,AAAK,IAALA,CACZ,CACJ,CA9sBAzjB,EAAckL,OAAO,CAjBrB,SAAiByY,CAAgB,EAC7B,IAAMC,EAAgBD,EAAiBtiB,SAAS,AAC5C,AAACuiB,CAAAA,EAAczF,SAAS,EACxB3B,GAAqBoH,EAAe,CAChCC,UA3FuC1J,GA4FvCwF,MAAAA,EACAgB,UAAAA,EACA3E,OAAAA,EACAqC,WAAAA,EACAF,UAAAA,EACAV,OAAAA,EACArV,WAAAA,EACAkV,WAAAA,EACA3K,eAAAA,CACJ,EAER,CAgtBJ,EAAG3S,GAAkBA,CAAAA,EAAgB,CAAC,CAAA,GAMT,IAAM8jB,GAAqB9jB,EAGxD,IAAI+jB,GAA2F5jB,EAAoB,KAC/G6jB,GAA+G7jB,EAAoBI,CAAC,CAACwjB,IAczI,GAAM,CAAEre,eAAgBue,EAAoB,CAAE,CAAItiB,IAE5C,CAAEmE,SAAUoe,EAAc,CAAEle,MAAOme,EAAW,CAAEpiB,KAAMqiB,EAAU,CAAEC,MAAAA,EAAK,CAAE,CAAI1iB,IASnF,SAAS2iB,GAAchiB,CAAO,EAC1B,OAAO,IAAIiiB,GAAM,IAAI,CAAEjiB,EAC3B,CAKA,SAASkiB,KACL,IAAMC,EAAe,IAAI,CAACniB,OAAO,CAAC2K,KAAK,CAAGoX,GAAM,IAAI,CAAC/hB,OAAO,CAAC2K,KAAK,EAAI,CAAC,GAClE,IAAI,CAAC5G,IAAI,KAGd,IAAI,CAAC4G,KAAK,CAAG,EAAE,CACfwX,EAAazb,OAAO,CAAC,AAAC0b,IAClB,IAAI,CAACC,QAAQ,CAACD,GAAaE,QAAQ,EACvC,GACJ,CAUA,MAAML,WAAeP,KACjB/X,aAAc,CAMV,KAAK,IAAIvB,WACT,IAAI,CAACsK,OAAO,CAAG,CAAA,CACnB,CACA,OAAO9J,QAAQC,CAAU,CAAE,CACvB,IAAME,EAAaF,EAAW9J,SAAS,AAClCgK,CAAAA,EAAWsZ,QAAQ,GACpBV,GAAqBhX,KAAK,CAAGkX,GAAYF,GAAqBlX,KAAK,CAAE,CACjE8X,OAAQ,EACRC,UAAW,CACf,GACAzZ,EAAWsZ,QAAQ,CAAGL,GACtBjZ,EAAW0Z,mBAAmB,CAAC9X,KAAK,CAAG,CAAC5B,EAAWsZ,QAAQ,CAAC,CAC5DtZ,EAAW2Z,qBAAqB,CAACxZ,IAAI,CAAC,SACtC0Y,GAAe/Y,EAAY,kBAAmBqZ,IAEtD,CAMAS,KAAK/iB,CAAK,CAAEgjB,CAAW,CAAE,CAErB,IAAI,CAAClQ,OAAO,CAAG,CAAA,EACf,KAAK,CAACiQ,KAAK/iB,EAAOgjB,EAAa,QACnC,CAMAC,mBAAoB,CAChB,IAAI,CAACC,gBAAgB,CAAG,CAAA,EAExB,IAAI,CAACC,OAAO,CAAG,IAAI,CAACC,OAAO,CAAG,IAAI,CAACC,gBAAgB,CAAI,IAAI,CAACC,gBAAgB,CAAG,KAAK,EAChF,IAAI,CAAC1U,QAAQ,EACb,IAAI,CAACA,QAAQ,CAAC2U,WAAW,GAG7B,IAAI,CAAClc,MAAM,CAACP,OAAO,CAAC,AAACO,IACjB,GAAIA,EAAOmc,YAAY,GAAI,CACvB,IAAI/U,EAAYpH,EAAOjH,OAAO,CAACqO,SAAS,AACxC,CAAA,IAAI,CAACyU,gBAAgB,CAAG,CAAA,EAEpB,IAAI,CAACO,kBAAkB,EAAIhV,GAAa,GACxCA,CAAAA,EAAY,KAAK,CAAA,EAErB,IAAMiV,EAAQrc,EAAOsc,SAAS,CAAC,IAC3BD,CAAAA,EAAM/gB,MAAM,GACZ,IAAI,CAACwgB,OAAO,CAAG9hB,KAAKiM,GAAG,CAAC4U,GAAW,IAAI,CAACiB,OAAO,CAAEO,CAAK,CAAC,EAAE,EAAGriB,KAAKiM,GAAG,CAAChF,KAAK,CAAC,KAAMob,IACjF,IAAI,CAACN,OAAO,CAAG/hB,KAAKkM,GAAG,CAAC2U,GAAW,IAAI,CAACkB,OAAO,CAAEM,CAAK,CAAC,EAAE,EAAGriB,KAAKkM,GAAG,CAACjF,KAAK,CAAC,KAAMob,IAEzF,CACJ,EACJ,CAIAE,aAAc,CACV,IAAM5jB,EAAQ,IAAI,CAACA,KAAK,CACxB,KAAK,CAAC4jB,cACN,IAAI,CAACrH,KAAK,CAAG,IAAI,CAAC3K,GAAG,CAAG5R,EAAMI,OAAO,CAACJ,KAAK,CAACG,SAAS,EAAES,OAAS,EAChE,IAAI,CAACwE,KAAK,CAAGpF,EAAM2H,UAAU,CAAG,IAAI,CAAC4U,KAAK,CAAG,IAAI,CAACvX,IAAI,AAC1D,CACJ,CAoBA,GAAM,CAAE4I,SAAUiW,EAA4B,CAAE,CAAIpkB,IAE9C,CAAEK,YAAagkB,EAA+B,CAAE,CAAGlhB,EAEnD,CAAEgB,SAAUmgB,EAA4B,CAAE/T,OAAQgU,EAA0B,CAAEnkB,KAAMokB,EAAwB,CAAEhW,WAAYiW,EAA8B,CAAElgB,KAAMmgB,EAAwB,CAAE,CAAI1kB,IAOpM,SAAS2kB,KACL,IAAqBpkB,EAAQqH,AAAd,IAAI,CAAiBrH,KAAK,CAAE8W,EAAgBzP,AAA5C,IAAI,CAA+CjH,OAAO,CAAEQ,EAAQkW,EAAclW,KAAK,CAGlGD,EAAIqW,AAHwGF,CAAAA,EAAclI,QAAQ,CACjIkI,EAAcE,KAAK,EAAI,EACxB3P,AAFW,IAAI,CAER6M,KAAK,AAAD,EACEtT,CAAAA,EAASkW,CAAAA,EAAcG,aAAa,EAAI,CAAA,CAAC,EAAIoN,EAAchd,AAH7D,IAAI,CAGgEid,WAAW,CAAG,EAAI,GAAM,EAAGC,EAQ9G,IAAK,IAAMziB,KAPP9B,EAAMK,QAAQ,EAAI,CAACgH,AAJR,IAAI,CAIWyD,KAAK,CAACqE,QAAQ,EACxCkV,CAAAA,GAAe,EAAC,EAEhBvN,AAA2B,CAAA,IAA3BA,EAAc0N,QAAQ,EACtB7jB,CAAAA,EAAI,CAAA,EAERA,GAAMmW,EAAcG,aAAa,EAAI,EACjB5P,AAXL,IAAI,CAWQtH,MAAM,EAG7B,GADA+B,EAAM2iB,aAAa,CAAG,KAClB3iB,AAAY,OAAZA,EAAMrB,CAAC,CAAW,CAClB,IAIIikB,EAJEjL,EAAYuK,GAA2B,CAAEzjB,EAAG,EAAGE,EAAG,EAAG8b,MAAO,EAAGF,OAAQ,CAAE,EAAGva,EAAM2X,SAAS,EAAI,CAAC,GAGtGkL,EAAa,CAAC,CAAC,IAAK,QAAQ,CAAE,CAAC,IAAK,SAAS,CAAC,CAAEC,EAAa9iB,EAAM8iB,UAAU,CAG7E,IAAK,IAAMpmB,KAAKmmB,EAkBZ,GAjBAD,CAAAA,EAAiBjL,CAAS,CAACjb,CAAC,CAAC,EAAE,CAAC,CAAG6lB,CAAU,EACxB,IAIjB5K,CAAS,CAACjb,CAAC,CAAC,EAAE,CAAC,EAAIib,CAAS,CAACjb,CAAC,CAAC,EAAE,CAAC,CAAG6lB,EACrC5K,CAAS,CAACjb,CAAC,CAAC,EAAE,CAAC,CAAG,CAAC6lB,EACnBK,EAAiB,GAEjB,AAACA,EAAiBjL,CAAS,CAACjb,CAAC,CAAC,EAAE,CAAC,CACjC6I,AAhCD,IAAI,AAgCG,CAAC7I,CAAC,CAAC,EAAE,CAAG,OAAO,CAACoT,GAAG,EAEzB6H,AAAoB,IAApBA,CAAS,CAACjb,CAAC,CAAC,EAAE,CAAC,EACfib,CAAAA,CAAS,CAACjb,CAAC,CAAC,EAAE,CAAC,CACX6I,AApCL,IAAI,AAoCO,CAAC7I,CAAC,CAAC,EAAE,CAAG,OAAO,CAACoT,GAAG,CACrB6H,CAAS,CAACjb,CAAC,CAAC,EAAE,CAAC,AAAD,EAI1Bib,AAAoB,IAApBA,CAAS,CAACjb,CAAC,CAAC,EAAE,CAAC,EACVib,CAAAA,CAAS,CAACjb,CAAC,CAAC,EAAE,CAAC,EAAI6I,AA1CrB,IAAI,AA0CuB,CAAC7I,CAAC,CAAC,EAAE,CAAG,OAAO,CAACoT,GAAG,EACzC6H,CAAS,CAACjb,CAAC,CAAC,EAAE,CAAC,CAAGib,CAAS,CAACjb,CAAC,CAAC,EAAE,CAAC,EAAI6lB,CAAU,EAAI,CAEvD,IAAK,IAAM1lB,KAAO8a,EAEdA,CAAS,CAAC9a,EAAI,CAAGA,AAAQ,MAARA,EAAc,MAAQ,CAI3CmD,CAAAA,EAAM2iB,aAAa,CAAG,CAAA,CAC1B,CA8BJ,GA3BI3iB,AAAoB,gBAApBA,EAAM+iB,SAAS,EACf/iB,CAAAA,EAAM+iB,SAAS,CAAG,QAAO,EAE7B/iB,EAAM2X,SAAS,CAAGuK,GAA2BvK,EAAW,CACpD9Y,EAAAA,EACAC,MAAAA,EACAX,eAAgB,CAAA,CACpB,GAEAskB,EAAa,CACThkB,EAAGkZ,EAAUlZ,CAAC,CAAGkZ,EAAU8C,KAAK,CAAG,EACnC9b,EAAGgZ,EAAUhZ,CAAC,CACdE,EAAGA,EAAIC,EAAQ,CACnB,EAEIZ,EAAMK,QAAQ,GACdkkB,EAAWhkB,CAAC,CAAGkZ,EAAU4C,MAAM,CAC/BkI,EAAW9jB,CAAC,CAAGqB,EAAMgjB,OAAO,EAAI,GAGpChjB,EAAM6P,QAAQ,CAAG4S,EAAWhkB,CAAC,CAC7BuB,EAAM+P,QAAQ,CAAG0S,EAAW9jB,CAAC,CAC7BqB,EAAMyV,QAAQ,CAAGgN,EAAW5jB,CAAC,CAG7BmB,EAAMijB,MAAM,CAAGjB,GAAgC,CAACS,EAAW,CAAEvkB,EAAO,CAAA,EAAM,CAAA,EAAM,CAAC,EAAE,CAE/E4kB,EAAY,CACZ,IAAMI,EAAkBlB,GAAgC,CAAC,CACjDvjB,EAAGqkB,CAAU,CAAC,EAAE,CAChBnkB,EAAGmkB,CAAU,CAAC,EAAE,CAChBjkB,EAAGA,EAAIC,EAAQ,CACnB,EAAE,CAAEZ,EAAO,CAAA,EAAM,CAAA,EAAM,CAAC,EAAE,AAC9B8B,CAAAA,EAAM8iB,UAAU,CAAG,CAACI,EAAgBzkB,CAAC,CAAEykB,EAAgBvkB,CAAC,CAAC,AAC7D,CACJ,CAGJ4G,AA7Fe,IAAI,CA6FZ1G,CAAC,CAAGA,CACf,CAuDA,SAASskB,KACL,GAAI,IAAI,CAACjlB,KAAK,CAACmE,IAAI,GAAI,CACnB,IAAqB2S,EAAgBzP,AAAtB,IAAI,CAAyBjH,OAAO,CAAEokB,EAAW1N,EAAc0N,QAAQ,CAAE5V,EAAWkI,EAAclI,QAAQ,CAAEsW,EAAiB7d,AAA7H,IAAI,CAAgIyD,KAAK,CAAC1K,OAAO,CAAC8kB,cAAc,CAC3KvkB,EAAI,EAER,GAAI,CAAE,CAAA,AAAoB,KAAA,IAAb6jB,GAA4B,CAACA,CAAO,EAAI,CACjD,IACI/hB,EADE0iB,EAASC,AAvB3B,SAAwBplB,CAAK,CAAE4O,CAAQ,EACnC,IAAMvH,EAASrH,EAAMqH,MAAM,CAAE8d,EAAS,CAAEE,YAAa,CAAE,EACnDC,EAAa7iB,EAAI,EAYrB,OAXA4E,EAAOP,OAAO,CAAC,SAAUQ,CAAC,EAEjB6d,CAAM,CADXG,EAAcrB,GAAyB3c,EAAElH,OAAO,CAAC4W,KAAK,CAAGpI,EAAW,EAAIvH,EAAO1E,MAAM,CAAG,EAAI2E,EAAE4M,KAAK,EAC3E,CAKpBiR,CAAM,CAACG,EAAY,CAACje,MAAM,CAACiC,IAAI,CAAChC,IAJhC6d,CAAM,CAACG,EAAY,CAAG,CAAEje,OAAQ,CAACC,EAAE,CAAEie,SAAU9iB,CAAE,EACjDA,IAKR,GACA0iB,EAAOE,WAAW,CAAG5iB,EAAI,EAClB0iB,CACX,EAQ0C,IAAI,CAACnlB,KAAK,CAAE4O,GAAWoI,EAAQF,EAAcE,KAAK,EAAI,EAEpF,IAAKvU,EAAI,EAAGA,EAAI0iB,CAAM,CAACnO,EAAM,CAAC3P,MAAM,CAAC1E,MAAM,EACnCwiB,CAAM,CAACnO,EAAM,CAAC3P,MAAM,CAAC5E,EAAE,GAAK,IAAI,CADKA,KAK7C9B,EAAI,AAAC,GAAMwkB,CAAAA,EAAOE,WAAW,CAAGF,CAAM,CAACnO,EAAM,CAACuO,QAAQ,AAAD,EAChDL,CAAAA,EAAiBziB,EAAI,CAACA,CAAAA,EAGvB,AAAC,IAAI,CAACoI,KAAK,CAACsE,QAAQ,EACpBxO,CAAAA,EAAI,AAAsB,GAArBwkB,EAAOE,WAAW,CAAS1kB,CAAAA,CAExC,CACAmW,EAAclW,KAAK,CAAGkW,EAAclW,KAAK,EAAI,GAC7CyG,AApBe,IAAI,CAoBZ1G,CAAC,CAAG0G,AApBI,IAAI,CAoBD1G,CAAC,EAAI,EACvBmW,EAAc1Q,MAAM,CAAGzF,CAC3B,CACJ,CAMA,SAAS6kB,GAA+Bnd,CAAO,CAAE,GAAG6J,CAAI,EACpD,OAAO,IAAI,CAAC7K,MAAM,CAACrH,KAAK,CAACmE,IAAI,GACzB,IAAI,CAACshB,OAAO,EAAI,AAAkC,MAAlC,IAAI,CAACA,OAAO,CAAC3R,OAAO,CAAC4R,QAAQ,CAC7Crd,EAAQC,KAAK,CAAC,IAAI,CAAE4J,EAC5B,CAEA,SAASyT,GAAwBtd,CAAO,EACpC,GAAK,IAAI,CAACrI,KAAK,CAACmE,IAAI,GAGf,CACD,IAAM+N,EAAO1J,UAAWua,EAAO7Q,CAAI,CAAC,EAAE,CAAEpH,EAAQ,IAAI,CAACA,KAAK,CAAiBqE,EAAW,IAAI,CAACrE,KAAK,CAACqE,QAAQ,CACzG,GAAI4T,EACA,IAAK,IAAMjhB,KAASuF,AAF6C,IAAI,CAE1CtH,MAAM,CACb,OAAZ+B,EAAMrB,CAAC,GACPqB,EAAMua,MAAM,CAAGva,EAAM2X,SAAS,CAAC4C,MAAM,CACrCva,EAAM8jB,MAAM,CAAG9jB,EAAM2X,SAAS,CAAChZ,CAAC,CAChCqB,EAAM2X,SAAS,CAAC4C,MAAM,CAAG,EACpBlN,IACGrN,EAAM+jB,MAAM,CACZ/jB,EAAM2X,SAAS,CAAChZ,CAAC,CACbqB,EAAMqB,KAAK,CACP2H,EAAMpC,SAAS,CAAC5G,EAAM+jB,MAAM,EAGpC/jB,EAAM2X,SAAS,CAAChZ,CAAC,CACbqB,EAAMqB,KAAK,CACNrB,CAAAA,EAAMgkB,QAAQ,CACX,CAAChkB,EAAMua,MAAM,CACbva,EAAMua,MAAM,AAAD,QAMtC,CACD,IAAK,IAAMva,KAASuF,AAzB6C,IAAI,CAyB1CtH,MAAM,CACb,OAAZ+B,EAAMrB,CAAC,GACPqB,EAAM2X,SAAS,CAAC4C,MAAM,CAAGva,EAAMua,MAAM,CACrCva,EAAM2X,SAAS,CAAChZ,CAAC,CAAGqB,EAAM8jB,MAAM,CAE5B9jB,EAAM2jB,OAAO,EACb3jB,EAAM2jB,OAAO,CAAC3jB,EAAM2iB,aAAa,CAC7B,OACA,UAAU,CAAC3iB,EAAM2X,SAAS,CAAEpS,AAjCqB,IAAI,CAiClBjH,OAAO,CAACgf,SAAS,GAKpE,IAAI,CAAC2G,cAAc,EACvB,CACJ,MA3CI1d,EAAQC,KAAK,CAAC,IAAI,CAAE,EAAE,CAACC,KAAK,CAAClJ,IAAI,CAACmJ,UAAW,GA4CrD,CAOA,SAASwd,GAA0B3d,CAAO,CAAEnJ,CAAI,CAAE+mB,CAAK,CAAEC,CAAW,CAAEC,CAAO,CAAE/H,CAAM,EAqBjF,MApBa,oBAATlf,GAA8BA,AAAS,gBAATA,GAC1B,IAAI,CAACc,KAAK,CAACmE,IAAI,KACX,IAAI,CAACjF,EAAK,EACV,OAAO,IAAI,CAACA,EAAK,CAEjBkf,IACI,AAAC,IAAI,CAACpe,KAAK,CAAComB,WAAW,EACvB,CAAA,IAAI,CAACpmB,KAAK,CAAComB,WAAW,CAClB,IAAI,CAACpmB,KAAK,CAACuE,QAAQ,CAACwX,CAAC,CAAC,eAAe5V,GAAG,CAACiY,EAAM,EAEvD,IAAI,CAAClf,EAAK,CAAG,IAAI,CAACc,KAAK,CAAComB,WAAW,CACnC,IAAI,CAACpmB,KAAK,CAAComB,WAAW,CAACtc,IAAI,CAAC,IAAI,CAACuc,UAAU,IAC3C,IAAI,CAACnnB,EAAK,CAAConB,OAAO,CAAG,CAAA,EACjBpnB,AAAS,UAATA,GACAsJ,CAAAA,SAAS,CAAC,EAAE,CAAG,SAAQ,IAMhCH,EAAQC,KAAK,CAAC,IAAI,CAAEie,MAAMpnB,SAAS,CAACoJ,KAAK,CAAClJ,IAAI,CAACmJ,UAAW,GACrE,CAEA,SAASge,GAA6Bne,CAAO,EACzC,IAAMyB,EAAOzB,EAAQC,KAAK,CAAC,IAAI,CAAE,EAAE,CAACC,KAAK,CAAClJ,IAAI,CAACmJ,UAAW,IAM1D,OALI,IAAI,CAACxI,KAAK,CAACmE,IAAI,EAAI,IAAI,CAACnE,KAAK,CAACmE,IAAI,KAElC2F,EAAK2c,MAAM,CAAG,IAAI,CAACrmB,OAAO,CAACsmB,SAAS,EAAI5c,EAAKvD,IAAI,CACjDuD,CAAI,CAAC,eAAe,CAAGma,GAAyB,IAAI,CAAC7jB,OAAO,CAACumB,SAAS,CAAE,IAErE7c,CACX,CAMA,SAAS8c,GAAyBve,CAAO,CAAEwe,CAAK,CAAE1G,CAAO,EACrD,IAAMhc,EAAO,IAAI,CAACnE,KAAK,CAACmE,IAAI,EAAI,IAAI,CAACnE,KAAK,CAACmE,IAAI,EAC3CA,CAAAA,GACA,CAAA,IAAI,CAAC/D,OAAO,CAAC0mB,mBAAmB,CAAG,CAAA,CAAG,EAE1Cze,EAAQhJ,IAAI,CAAC,IAAI,CAAEwnB,EAAO1G,GACtBhc,GACA,CAAA,IAAI,CAAC/D,OAAO,CAAC0mB,mBAAmB,CAAG,CAAA,CAAI,CAE/C,CAMA,SAASC,GAA2B1e,CAAO,CAAE2e,CAAG,EAE5C,GAAI3f,AADW,IAAI,CACRrH,KAAK,CAACmE,IAAI,GACjB,IAAK,IAAMrC,KAASuF,AAFT,IAAI,CAEYtH,MAAM,CAC7B+B,EAAMmD,OAAO,CAAGnD,EAAM1B,OAAO,CAAC6E,OAAO,CAAG+hB,EACpC,AAAe,KAAA,IAARA,EACH,CAAC/C,GAAyB5c,AAL3B,IAAI,CAK8BpC,OAAO,CAAEnD,EAAMmD,OAAO,EAAI+hB,EACnE3f,AANO,IAAI,CAMJjH,OAAO,CAAC8W,IAAI,CAAC7P,AANb,IAAI,CAMgB6P,IAAI,CAAC/C,OAAO,CAACrS,GAAO,CAC3CA,EAAM1B,OAAO,CACb0B,EAAM2jB,OAAO,EACb3jB,EAAM2jB,OAAO,CAAC3b,IAAI,CAAC,CACf+R,WAAYmL,EAAM,UAAY,QAClC,GAIZ3e,EAAQC,KAAK,CAAC,IAAI,CAAEie,MAAMpnB,SAAS,CAACoJ,KAAK,CAAClJ,IAAI,CAACmJ,UAAW,GAC9D,CAEA,SAASye,GAA0B5e,CAAO,EACtCA,EAAQC,KAAK,CAAC,IAAI,CAAE,EAAE,CAACC,KAAK,CAAClJ,IAAI,CAACmJ,UAAW,IAEzC,IAAI,CAACxI,KAAK,CAACmE,IAAI,IACf,IAAI,CAAC+iB,iBAAiB,EAE9B,CAEA,SAASC,GAAyB9e,CAAO,CAAEvG,CAAK,CAAEslB,CAAU,CAAEhnB,CAAO,CAAEinB,CAAO,EAC1E,IAAMrnB,EAAQ,IAAI,CAACA,KAAK,CAKxB,GAFAI,EAAQqkB,aAAa,CAAG3iB,EAAM2iB,aAAa,CAEvCzkB,EAAMmE,IAAI,IACV,IAAI,CAACmjB,EAAE,CAAC,UAAW,CACnB,IAAqBxQ,EAAgBzP,AAAtB,IAAI,CAAyBjH,OAAO,CAAEmnB,EAAStD,GAAyB7jB,EAAQmnB,MAAM,CAAE,CAAC,CAAClgB,AAA1F,IAAI,CAA6FjH,OAAO,CAACwO,QAAQ,EAAGzO,EAAYH,EAAMI,OAAO,CAACJ,KAAK,CAACG,SAAS,CAAEqnB,EAAU1lB,EAAM2lB,UAAU,CAAG,GAAK,EAC5MC,EAAa,CACbnnB,EAAG8mB,EAAQ9mB,CAAC,CAAGinB,EACf/mB,EAAG4mB,EAAQ5mB,CAAC,CACZE,EAAG0G,AAJQ,IAAI,CAIL1G,CAAC,CAAGmW,EAAclW,KAAK,CAAG,CACxC,CACIZ,CAAAA,EAAMK,QAAQ,GAIVknB,IACAF,EAAQ9K,KAAK,CAAG,EAChBmL,EAAWnnB,CAAC,EAAIuB,EAAM2X,SAAS,CAAC4C,MAAM,CAAG,GAKzClc,EAAUe,KAAK,EAAI,IAAMf,EAAUe,KAAK,EAAI,KAC5CwmB,CAAAA,EAAWjnB,CAAC,EAAIqB,EAAM2X,SAAS,CAAC8C,KAAK,AAAD,GAK5C8K,EAAQ9mB,CAAC,CAAGmnB,AADZA,CAAAA,EAAa5D,GAAgC,CAAC4D,EAAW,CAAE1nB,EAAO,CAAA,EAAM,CAAA,EAAM,CAAC,EAAE,AAAD,EACzDO,CAAC,CAAGinB,EAE3BH,EAAQ5mB,CAAC,CAAGqB,EAAM2iB,aAAa,CAAG,KAAOiD,EAAWjnB,CAAC,AACzD,CACA4H,EAAQC,KAAK,CAAC,IAAI,CAAE,EAAE,CAACC,KAAK,CAAClJ,IAAI,CAACmJ,UAAW,GACjD,CAKA,SAASmf,GAA2Btf,CAAO,EACvC,MAAQ,CAAEG,SAAS,CAAC,EAAE,CAACic,aAAa,EAChCpc,EAAQC,KAAK,CAAC,IAAI,CAAE,EAAE,CAACC,KAAK,CAAClJ,IAAI,CAACmJ,UAAW,GAErD,CAKA,SAASof,GAAyBvf,CAAO,CAAEwf,CAAa,EACpD,IAAMC,EAAWzf,EAAQC,KAAK,CAAC,IAAI,CAAE,EAAE,CAACC,KAAK,CAAClJ,IAAI,CAACmJ,UAAW,IAEtCxI,EAAQ,IAAI,CAACgL,IAAI,CAAChL,KAAK,CAAE,CAAEuc,MAAOwL,CAAM,CAAE,CAAGF,EACrE,GAAI7nB,EAAMmE,IAAI,IAAM6jB,AADF,IAAI,CACQnO,IAAI,CAAE,CAEhC,IAAMoO,EAAgB,CAAC,AAACD,AAHV,IAAI,CAGgBnO,IAAI,CAAEqO,KAAK,CAAC,IAAI,CAAC,EAAE,CAC/CC,EAAenoB,EAAMqH,MAAM,CAAC4gB,EAAc,CAC1C9nB,EAAYH,EAAMI,OAAO,CAACJ,KAAK,CAACG,SAAS,CAI/C,GAAIgoB,GACAA,AAAsB,WAAtBA,EAAa/jB,IAAI,CAAe,CAChC,IAAIsjB,EAAa,CACbnnB,EAAGunB,EAASvnB,CAAC,CAAIP,CAAAA,EAAMK,QAAQ,CAAGynB,EAASzL,MAAM,CAAG0L,EAAS,CAAA,EAC7DtnB,EAAGqnB,EAASrnB,CAAC,CACbE,EAAGwnB,EAAa/nB,OAAO,CAACQ,KAAK,CAAG,CACpC,CACIZ,CAAAA,EAAMK,QAAQ,GAGdynB,EAASvL,KAAK,CAAG,EAIbpc,EAAUe,KAAK,EAAI,IAAMf,EAAUe,KAAK,EAAI,KAC5CwmB,CAAAA,EAAWjnB,CAAC,EAAIsnB,CAAK,GAI7BD,EAASvnB,CAAC,CAAGmnB,AADbA,CAAAA,EAAa5D,GAAgC,CAAC4D,EAAW,CAAE1nB,EAAO,CAAA,EAAM,CAAA,EAAM,CAAC,EAAE,AAAD,EACxDO,CAAC,CAAGwnB,EAAS,EACrCD,EAASrnB,CAAC,CAAGinB,EAAWjnB,CAAC,AAC7B,CACJ,CACA,OAAOqnB,CACX,CAqEA,GAAM,CAAEM,IAAK,CAAEjpB,UAAW,CAAEkpB,WAAYC,EAAQ,CAAE,CAAE,CAAE,CAAG,AAAC3a,IAA2IK,WAAW,CAyB7Kua,GAnBnC,cAAyBD,GASrBE,UAAW,CACP,OAAO,IAAI,CAACnhB,MAAM,EAAErH,MAAMmE,OACtB,EAAE,CAAG,KAAK,CAACqkB,SAASlgB,KAAK,CAAC,IAAI,CAAEE,UACxC,CACJ,EAsBM,CAAEoF,SAAU6a,EAAoB,CAAE7oB,QAAS8oB,EAAmB,CAAE,CAAIjpB,IAGpE,CAAE2oB,IAAKO,EAAS,CAAE,CAAG,AAAChb,IAA2IK,WAAW,CAE5K,CAAEgC,OAAQ4Y,EAAkB,CAAE/oB,KAAMgpB,EAAgB,CAAE5a,WAAY6a,EAAsB,CAAE,CAAIrpB,GAMpG,OAAMspB,WAAoBJ,GAMtB,OAAO3f,QAAQ0N,CAAW,CAAE,CACpBoS,GAAuBL,GAAsB,UAC7C/R,CAAAA,EAAYkD,KAAK,CAACwO,GAAG,CAAGW,EAAU,CAE1C,CASAC,UAAW,CACP,KAAK,CAACA,SAAS1gB,KAAK,CAAC,IAAI,CAAEE,WACvB,IAAI,CAACxI,KAAK,CAACmE,IAAI,IAEf,IAAI,CAAC8kB,MAAM,CAAC,IAAI,CAACjG,WAAW,CAAE,CAAA,EAEtC,CAIAtJ,QAAQqJ,CAAI,CAAE,CACV,GAAK,IAAI,CAAC/iB,KAAK,CAACmE,IAAI,GAGf,CACD,IAAM4a,EAAS,IAAI,CAACA,MAAM,CAAEhQ,EAAQ,IAAI,CAACA,KAAK,CAAEC,EAAc,IAAI,CAACA,WAAW,CAC1EoQ,EAAY,IAAI,CAAChf,OAAO,CAACgf,SAAS,CAAE3G,CACpC2G,AAAc,EAAA,IAAdA,GACAA,CAAAA,EAAY,CAAC,CAAA,EAGb2D,GAEAhU,EAAMma,aAAa,CAAGL,GAAiB9Z,EAAMma,aAAa,CAAEna,EAAME,UAAU,EAC5EF,EAAMoa,aAAa,CAAGN,GAAiB9Z,EAAMoa,aAAa,CAAEpa,EAAMG,UAAU,EAC5EuJ,EAAU,CACNxJ,WAAY8P,CAAM,CAAC,EAAE,CACrB7P,WAAY6P,CAAM,CAAC,EAAE,CACrBqK,OAAQ,KACRC,OAAQ,IACZ,EACAta,EAAMjF,IAAI,CAAC2O,GACPzJ,IACAA,EAAYsa,WAAW,CAAGva,EAAMua,WAAW,CAC3Cta,EAAYlF,IAAI,CAAC2O,MAKrBA,EAAU,CACNxJ,WAAYF,EAAMma,aAAa,CAC/Bha,WAAYH,EAAMoa,aAAa,CAC/BC,OAAQ,EACRC,OAAQ,CACZ,EACAta,EAAM2K,OAAO,CAACjB,EAAS2G,GACnBpQ,GACAA,EAAY0K,OAAO,CAACjB,EAAS2G,GAGzC,MAtCI,KAAK,CAAC1F,QAAQpR,KAAK,CAAC,IAAI,CAAEE,UAuClC,CAIA+gB,qBAAqBznB,CAAK,CAAEI,CAAQ,CAAE,CAClC,IAAMsnB,EAAgB,KAAK,CAACD,qBAAqBznB,EAAOI,GACxD,GAAI,IAAI,CAAClC,KAAK,CAACmE,IAAI,GAAI,CACnB,IAAMhE,EAAY,IAAI,CAACH,KAAK,CAACI,OAAO,CAACJ,KAAK,CAACG,SAAS,CAAEsZ,EAAY3X,EAAM2X,SAAS,CAAEsG,EAAItG,EAAUsG,CAAC,CAElGuB,EAAM,AAAC7H,CAAAA,EAAUvY,KAAK,EAAIf,GAAWe,KAAI,EACrCwnB,GAAsBe,EAAM,AAAChQ,CAAAA,EAAUxY,IAAI,EAAId,GAAWc,IAAG,EAC7DynB,GAAsBnH,EAAK,AAAC9H,CAAAA,EAAU9P,KAAK,CAAG8P,EAAU7P,GAAG,AAAD,EAAK,EAAG8f,EAAoBF,EAAcE,iBAAiB,CAAEC,EAAW,CAAC5J,EAAK,CAAA,EAAI1e,KAAKC,GAAG,CAACggB,EAAE,EAAKjgB,KAAKI,GAAG,CAAC8f,GAAMiG,EAAUzH,EAAK1e,CAAAA,KAAKC,GAAG,CAACmoB,GAAM,CAAA,EAAKpoB,KAAKC,GAAG,CAACigB,GAE3N,IAAK,IAAMze,IAAe,CACtB0mB,GAAeI,QACfF,EAAkBG,OAAO,CACzBH,EAAkBI,eAAe,CACpC,CACGhnB,EAAYvC,CAAC,EAAIinB,EACjB1kB,EAAYrC,CAAC,EAAIkpB,CAEzB,CACA,OAAOH,CACX,CAIAO,aAAajoB,CAAK,CAAE,CAChB,IAAMgI,EAAO,KAAK,CAACigB,aAAazhB,KAAK,CAAC,IAAI,CAAEE,WAAYpI,EAAU,IAAI,CAACA,OAAO,CAK9E,OAJI,IAAI,CAACJ,KAAK,CAACmE,IAAI,IAAM,CAAC,IAAI,CAACnE,KAAK,CAAC2G,UAAU,GAC3CmD,EAAK2c,MAAM,CAAGrmB,EAAQsmB,SAAS,EAAI5kB,EAAMyB,KAAK,EAAI,IAAI,CAACA,KAAK,CAC5DuG,CAAI,CAAC,eAAe,CAAG+e,GAAiBzoB,EAAQumB,SAAS,CAAE,IAExD7c,CACX,CAIApB,WAAY,CAGR,GAFA,KAAK,CAACA,UAAUJ,KAAK,CAAC,IAAI,CAAEE,WAExB,CAAC,IAAI,CAACxI,KAAK,CAACmE,IAAI,GAChB,OAEJ,IAAqB2S,EAAgBzP,AAAtB,IAAI,CAAyBjH,OAAO,CAAEQ,EAAQkW,EAAclW,KAAK,EAAI,EAAGT,EAAYkH,AAApF,IAAI,CAAuFrH,KAAK,CAACI,OAAO,CAACJ,KAAK,CAACG,SAAS,CAAEe,EAAQf,EAAUe,KAAK,CAAED,EAAOd,EAAUc,IAAI,CACnLN,EAAImW,EAAclI,QAAQ,CAC1B,AAACkI,CAAAA,EAAcE,KAAK,EAAI,CAAA,EAAKpW,EAC7ByG,AAHW,IAAI,CAGR2iB,EAAE,CAAGppB,EAKhB,IAAK,IAAMkB,KAJXnB,GAAKC,EAAQ,EACTkW,AAA2B,CAAA,IAA3BA,EAAc0N,QAAQ,EACtB7jB,CAAAA,EAAI,CAAA,EAEY0G,AARL,IAAI,CAQQtH,MAAM,EAAE,CAC/B,IAAM0Z,EAAY3X,EAAM2X,SAAS,AACjC3X,CAAAA,EAAM+iB,SAAS,CAAG,QAClBpL,EAAU9Y,CAAC,CAAGA,EACd8Y,EAAU7Y,KAAK,CAAGA,AAAQ,IAARA,EAClB6Y,EAAUvY,KAAK,CAAGA,EAClBuY,EAAUxY,IAAI,CAAGA,EACjBwY,EAAUsF,MAAM,CAAG1X,AAfR,IAAI,CAeW0X,MAAM,CAChC,IAAMsC,EAAQ,AAAC5H,CAAAA,EAAU7P,GAAG,CAAG6P,EAAU9P,KAAK,AAAD,EAAK,CAClD7H,CAAAA,EAAMmoB,iBAAiB,CAAG,CACtBhb,WAAY5N,KAAKkN,KAAK,CAAClN,KAAKC,GAAG,CAAC+f,GAC5BvK,EAAcoT,YAAY,CAC1B7oB,KAAKC,GAAG,CAACJ,EAAQwnB,KACrBxZ,WAAY7N,KAAKkN,KAAK,CAAClN,KAAKI,GAAG,CAAC4f,GAC5BvK,EAAcoT,YAAY,CAC1B7oB,KAAKC,GAAG,CAACJ,EAAQwnB,IACzB,CACJ,CACJ,CAIAyB,aAAc,CAGV,GAFA,KAAK,CAACA,YAAY7hB,KAAK,CAAC,IAAI,CAAEE,WAEzB,IAAI,CAACxI,KAAK,CAACmE,IAAI,GAGpB,CAAA,IAAK,IAAMrC,KAAS,IAAI,CAAC/B,MAAM,CAC3B,GAAI+B,EAAM2jB,OAAO,CACb,IAAK,IAAMnH,IAAQ,CAAC,MAAO,MAAO,QAAS,QAAQ,CAC3Cxc,EAAM2jB,OAAO,EACb3jB,CAAAA,EAAM2jB,OAAO,CAACnH,EAAK,CAACxK,OAAO,CAAChS,KAAK,CAAGA,CAAI,CAIxD,CACJ,CACJ,CACA8mB,GAAmBG,GAAY5pB,SAAS,CAAE,CACtCkpB,WAAYE,EAChB,GAyBA,IAAI6B,GAA2JnsB,EAAoB,KAC/KosB,GAA+KpsB,EAAoBI,CAAC,CAAC+rB,IAezM,GAAM,CAAE/B,WAAYiC,EAAY,CAAE,CAAG,AAACD,KAAmKlrB,SAAS,CAE5M,CAAE4Y,QAASwS,EAAsB,CAAE,CAAI9qB,IAyBV+qB,GAnBnC,cAA6BF,GAMzBG,cAAe,CAKX,OAJA,KAAK,CAACA,aAAaniB,KAAK,CAAC,IAAI,CAAEE,WAC3B,AAAC+hB,GAAuB,IAAI,CAAC5pB,CAAC,GAC9B,CAAA,IAAI,CAACA,CAAC,CAAG,CAAA,EAEN,IAAI,AACf,CACJ,EA8IM,CAAEkC,oBAAqB6nB,EAAmC,CAAE,CAAG9nB,EAM/D,CAAEoN,OAAQ2a,EAAsB,CAAE7mB,MAAO8mB,EAAqB,CAAE,CAAInrB,GAa1E,OAAMorB,WAAyBR,KAM3BN,aAAajoB,CAAK,CAAE,CAChB,IAAM2W,EAAU,KAAK,CAACsR,aAAazhB,KAAK,CAAC,IAAI,CAAEE,WAK/C,OAJI,IAAI,CAACxI,KAAK,CAACmE,IAAI,IAAMrC,GACrB2W,CAAAA,EAAQrS,MAAM,CACVskB,GAAoC5oB,EAAO,IAAI,CAAC9B,KAAK,CAAA,EAEtDyY,CACX,CACJ,CAMAoS,GAAgBrnB,cAAc,CAAGonB,GAAsB,AAACP,KAAmK7mB,cAAc,CA3IzM,CAC5BsnB,QAAS,CACLC,YAAa,0EACjB,CACJ,GAwIAJ,GAAuBE,GAAgB1rB,SAAS,CAAE,CAC9C6rB,UAAW,CAAC,QAAS,QAAS,QAAQ,CAItCC,YAAa,CAAA,EACbC,eAAgB,CAAC,IAAK,IAAK,IAAI,CAC/BC,cAAe,CAAC,IAAK,IAAK,IAAI,CAC9B9C,WAAYmC,EAChB,GACA7c,IAA0Iyd,kBAAkB,CAAC,YAAaP,IAuB1K,IAAMQ,GAAK5rB,IAEX6rB,AA74FqB,CAAA,CACjBtiB,QAjEJ,SAAiBuiB,CAAe,EACxBtd,EAAWL,EAAU,iBACrBM,EAAkBqd,EAAgBpsB,SAAS,CAAE,eAAgBgP,EAErE,CA8DA,CAAA,EA24FoBnF,OAAO,CAACqiB,GAAEG,MAAM,CAAC5R,KAAK,CAACpX,IAAI,EAC/CipB,AArtEwD1Z,GAqtEtC/I,OAAO,CAACqiB,GAAEK,IAAI,CAAEL,GAAEM,IAAI,EACxCle,EAAczE,OAAO,CAACqiB,GAAEO,KAAK,CAAEP,GAAEQ,EAAE,EACnCC,AAhjB4B,CAAA,CACxB9iB,QA5TJ,SAAqC0N,CAAW,CAAEqV,CAAc,EAC5D,GAAI7H,GAA+BL,GAA8B,YAAa,CAC1E,IAAMmI,EAActV,EAAYvX,SAAS,CAAE8sB,EAAiBF,EAAe5sB,SAAS,CAAE,CAAE+sB,OAAQC,CAAiB,CAAEC,YAAaC,CAAsB,CAAE,CAAG3V,EAAYkD,KAAK,CAI5K,GAHAuK,GAAyB6H,EAAa,iBAAkB7E,IACxDhD,GAAyB6H,EAAa,mBAAoBrE,IAC1DxD,GAAyB8H,EAAgB,cAAerE,IACpDuE,EAAmB,CACnB,IAAMG,EAAoBH,EAAkBhtB,SAAS,CAAEotB,EAAmBD,EAAkBjE,UAAU,CAAClpB,SAAS,AAChHmtB,CAAAA,EAAkB3V,iBAAiB,CAAG,IAAM,KAAK,EACjD2V,EAAkBpF,iBAAiB,CAAG9C,GACtCL,GAA6BuI,EAAmB,YAAarH,IAC7Dd,GAAyBoI,EAAkB,kBAAmB/G,IAC9DrB,GAAyBmI,EAAmB,UAAW3G,IACvDxB,GAAyBmI,EAAmB,YAAatG,IACzD7B,GAAyBmI,EAAmB,eAAgB9F,IAC5DrC,GAAyBmI,EAAmB,WAAY1F,IACxDzC,GAAyBmI,EAAmB,aAAcvF,IAC1D5C,GAAyBmI,EAAmB,YAAarF,GAC7D,CACA,GAAIoF,EAAwB,CACxB,IAAMG,EAAyBH,EAAuBltB,SAAS,CAC/DglB,GADyFqI,EAAuBnE,UAAU,CAAClpB,SAAS,CACpF,kBAAmBqmB,IACnErB,GAAyBqI,EAAwB,YAAaxG,IAC9D7B,GAAyBqI,EAAwB,eAAgBhG,IACjErC,GAAyBqI,EAAwB,WAAY5F,IAC7DzC,GAAyBqI,EAAwB,aAAczF,GACnE,CACJ,CACJ,CAiSA,CAAA,EA8iB6B/d,OAAO,CAACqiB,GAAEG,MAAM,CAAG9T,MAChD+U,AArRwD1D,GAqRtC/f,OAAO,CAACqiB,GAAEG,MAAM,EAClCkB,AAzmEsDjW,GAymEtCzN,OAAO,CAACqiB,GAAEG,MAAM,EAChC5J,GAAkB5Y,OAAO,CAAC8M,KAAkJgC,eAAe,IAC3L6U,AAz+BiDtK,GAy+BtCrZ,OAAO,CAACqiB,GAAEO,KAAK,EACG,IAAMrsB,GAAqB8rB,GAG9C,OADY/rB,EAAoB,OAAU,AAE3C,CAAA"}