{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/williams-r\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Wojciech Chmiel\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/williams-r\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/williams-r\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ williams_r_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Stock/Indicators/ArrayUtilities.js\n/**\n *\n *  (c) 2010-2025 Pawel Fus & Daniel Studencki\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Get extremes of array filled by OHLC data.\n *\n * @private\n *\n * @param {Array<Array<number>>} arr\n * Array of OHLC points (arrays).\n *\n * @param {number} minIndex\n * Index of \"low\" value in point array.\n *\n * @param {number} maxIndex\n * Index of \"high\" value in point array.\n *\n * @return {Array<number,number>}\n * Returns array with min and max value.\n */\nfunction getArrayExtremes(arr, minIndex, maxIndex) {\n    return arr.reduce((prev, target) => [\n        Math.min(prev[0], target[minIndex]),\n        Math.max(prev[1], target[maxIndex])\n    ], [Number.MAX_VALUE, -Number.MAX_VALUE]);\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst ArrayUtilities = {\n    getArrayExtremes\n};\n/* harmony default export */ const Indicators_ArrayUtilities = (ArrayUtilities);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/WilliamsR/WilliamsRIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { sma: SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { extend, isArray, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Williams %R series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.williamsr\n *\n * @augments Highcharts.Series\n */\nclass WilliamsRIndicator extends SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, WR = [], // 0- date, 1- Williams %R\n        xData = [], yData = [], close = 3, low = 2, high = 1;\n        let slicedY, extremes, R, HH, // Highest high value in period\n        LL, // Lowest low value in period\n        CC, // Current close value\n        i;\n        // Williams %R requires close value\n        if (xVal.length < period ||\n            !isArray(yVal[0]) ||\n            yVal[0].length !== 4) {\n            return;\n        }\n        // For a N-period, we start from N-1 point, to calculate Nth point\n        // That is why we later need to comprehend slice() elements list\n        // with (+1)\n        for (i = period - 1; i < yValLen; i++) {\n            slicedY = yVal.slice(i - period + 1, i + 1);\n            extremes = Indicators_ArrayUtilities.getArrayExtremes(slicedY, low, high);\n            LL = extremes[0];\n            HH = extremes[1];\n            CC = yVal[i][close];\n            R = ((HH - CC) / (HH - LL)) * -100;\n            if (xVal[i]) {\n                WR.push([xVal[i], R]);\n                xData.push(xVal[i]);\n                yData.push(R);\n            }\n        }\n        return {\n            values: WR,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Williams %R. This series requires the `linkedTo` option to be\n * set and should be loaded after the `stock/indicators/indicators.js`.\n *\n * @sample {highstock} stock/indicators/williams-r\n *         Williams %R\n *\n * @extends      plotOptions.sma\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, joinBy, keys, navigatorOptions,\n *               pointInterval, pointIntervalUnit, pointPlacement,\n *               pointRange, pointStart, showInNavigator, stacking\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/williams-r\n * @optionparent plotOptions.williamsr\n */\nWilliamsRIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    /**\n     * Parameters used in calculation of Williams %R series points.\n     * @excluding index\n     */\n    params: {\n        index: void 0, // Unchangeable index, do not inherit (#15362)\n        /**\n         * Period for Williams %R oscillator\n         */\n        period: 14\n    }\n});\nextend(WilliamsRIndicator.prototype, {\n    nameBase: 'Williams %R'\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('williamsr', WilliamsRIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const WilliamsR_WilliamsRIndicator = ((/* unused pure expression or super */ null && (WilliamsRIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `Williams %R Oscillator` series. If the [type](#series.williamsr.type)\n * option is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.williamsr\n * @since     7.0.0\n * @product   highstock\n * @excluding allAreas, colorAxis, dataParser, dataURL, joinBy, keys,\n *            navigatorOptions, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointRange, pointStart, showInNavigator, stacking\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/williams-r\n * @apioption series.williamsr\n */\n''; // Adds doclets above to the transpiled file\n\n;// ./code/es-modules/masters/indicators/williams-r.js\n\n\n\n\n/* harmony default export */ const williams_r_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "williams_r_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "Indicators_ArrayUtilities", "getArrayExtremes", "arr", "minIndex", "maxIndex", "reduce", "prev", "target", "Math", "min", "max", "Number", "MAX_VALUE", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "sma", "SMAIndicator", "seriesTypes", "extend", "isArray", "merge", "WilliamsRIndicator", "getV<PERSON>ues", "series", "params", "slicedY", "extremes", "R", "HH", "LL", "i", "period", "xVal", "xData", "yVal", "yData", "yValLen", "length", "WR", "slice", "push", "values", "defaultOptions", "index", "nameBase", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,mCAAoC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GAC5H,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,mCAAmC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAEhHA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAgDxF,IAAME,EAHZ,CACnBC,iBAZJ,SAA0BC,CAAG,CAAEC,CAAQ,CAAEC,CAAQ,EAC7C,OAAOF,EAAIG,MAAM,CAAC,CAACC,EAAMC,IAAW,CAChCC,KAAKC,GAAG,CAACH,CAAI,CAAC,EAAE,CAAEC,CAAM,CAACJ,EAAS,EAClCK,KAAKE,GAAG,CAACJ,CAAI,CAAC,EAAE,CAAEC,CAAM,CAACH,EAAS,EACrC,CAAE,CAACO,OAAOC,SAAS,CAAE,CAACD,OAAOC,SAAS,CAAC,CAC5C,CAQA,EAIA,IAAIC,EAAmItC,EAAoB,KACvJuC,EAAuJvC,EAAoBI,CAAC,CAACkC,GAYjL,GAAM,CAAEE,IAAKC,CAAY,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAE/K,CAAEC,OAAAA,CAAM,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAE,CAAIrB,GAepC,OAAMsB,UAA2BL,EAM7BM,UAAUC,CAAM,CAAEC,CAAM,CAAE,CACtB,IAEIC,EAASC,EAAUC,EAAGC,EAC1BC,EAEAC,EALMC,EAASP,EAAOO,MAAM,CAAEC,EAAOT,EAAOU,KAAK,CAAEC,EAAOX,EAAOY,KAAK,CAAEC,EAAUF,EAAOA,EAAKG,MAAM,CAAG,EAAGC,EAAK,EAAE,CACjHL,EAAQ,EAAE,CAAEE,EAAQ,EAAE,CAMtB,GAAIH,CAAAA,CAAAA,EAAKK,MAAM,CAAGN,CAAK,GAClBZ,EAAQe,CAAI,CAAC,EAAE,GAChBA,AAAmB,IAAnBA,CAAI,CAAC,EAAE,CAACG,MAAM,EAMlB,IAAKP,EAAIC,EAAS,EAAGD,EAAIM,EAASN,IAC9BL,EAAUS,EAAKK,KAAK,CAACT,EAAIC,EAAS,EAAGD,EAAI,GAEzCD,EAAKH,AADLA,CAAAA,EAAW1B,EAA0BC,gBAAgB,CAACwB,EAhBjB,EAAU,EAgByB,CAC3D,CAAC,EAAE,CAGhBE,EAAI,CAAA,CAAA,AAAEC,CAAAA,AAFNA,CAAAA,EAAKF,CAAQ,CAAC,EAAE,AAAD,EACVQ,CAAI,CAACJ,EAAE,CAnBgB,EAmBT,AACP,EAAMF,CAAAA,EAAKC,CAAC,EAAM,GAAG,EAC7BG,CAAI,CAACF,EAAE,GACPQ,EAAGE,IAAI,CAAC,CAACR,CAAI,CAACF,EAAE,CAAEH,EAAE,EACpBM,EAAMO,IAAI,CAACR,CAAI,CAACF,EAAE,EAClBK,EAAMK,IAAI,CAACb,IAGnB,MAAO,CACHc,OAAQH,EACRL,MAAOA,EACPE,MAAOA,CACX,EACJ,CACJ,CAuBAd,EAAmBqB,cAAc,CAAGtB,EAAMJ,EAAa0B,cAAc,CAAE,CAKnElB,OAAQ,CACJmB,MAAO,KAAK,EAIZZ,OAAQ,EACZ,CACJ,GACAb,EAAOG,EAAmB5B,SAAS,CAAE,CACjCmD,SAAU,aACd,GACA9B,IAA0I+B,kBAAkB,CAAC,YAAaxB,GAiC7I,IAAMxB,EAAmBE,IAG5C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}