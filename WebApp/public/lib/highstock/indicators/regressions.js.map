{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/regressions\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Ka<PERSON>l Kulig\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/regressions\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/regressions\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ regressions_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/LinearRegression/LinearRegressionIndicator.js\n/**\n *\n *  (c) 2010-2025 Kamil Kulig\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { isArray, extend, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * Linear regression series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.linearregression\n *\n * @augments Highcharts.Series\n */\nclass LinearRegressionIndicator extends SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Return the slope and intercept of a straight line function.\n     *\n     * @private\n     *\n     * @param {Array<number>} xData\n     * List of all x coordinates in a period.\n     *\n     * @param {Array<number>} yData\n     * List of all y coordinates in a period.\n     *\n     * @return {Highcharts.RegressionLineParametersObject}\n     * Object that contains the slope and the intercept of a straight line\n     * function.\n     */\n    getRegressionLineParameters(xData, yData) {\n        // Least squares method\n        const yIndex = this.options.params.index, getSingleYValue = function (yValue, yIndex) {\n            return isArray(yValue) ? yValue[yIndex] : yValue;\n        }, xSum = xData.reduce(function (accX, val) {\n            return val + accX;\n        }, 0), ySum = yData.reduce(function (accY, val) {\n            return getSingleYValue(val, yIndex) + accY;\n        }, 0), xMean = xSum / xData.length, yMean = ySum / yData.length;\n        let xError, yError, i, formulaNumerator = 0, formulaDenominator = 0;\n        for (i = 0; i < xData.length; i++) {\n            xError = xData[i] - xMean;\n            yError = getSingleYValue(yData[i], yIndex) - yMean;\n            formulaNumerator += xError * yError;\n            formulaDenominator += Math.pow(xError, 2);\n        }\n        const slope = formulaDenominator ?\n            formulaNumerator / formulaDenominator : 0; // Don't divide by 0\n        return {\n            slope: slope,\n            intercept: yMean - slope * xMean\n        };\n    }\n    /**\n     * Return the y value on a straight line.\n     *\n     * @private\n     *\n     * @param {Highcharts.RegressionLineParametersObject} lineParameters\n     * Object that contains the slope and the intercept of a straight line\n     * function.\n     *\n     * @param {number} endPointX\n     * X coordinate of the point.\n     *\n     * @return {number}\n     * Y value of the point that lies on the line.\n     */\n    getEndPointY(lineParameters, endPointX) {\n        return lineParameters.slope * endPointX + lineParameters.intercept;\n    }\n    /**\n     * Transform the coordinate system so that x values start at 0 and\n     * apply xAxisUnit.\n     *\n     * @private\n     *\n     * @param {Array<number>} xData\n     * List of all x coordinates in a period\n     *\n     * @param {number} xAxisUnit\n     * Option (see the API)\n     *\n     * @return {Array<number>}\n     * Array of transformed x data\n     */\n    transformXData(xData, xAxisUnit) {\n        const xOffset = xData[0];\n        return xData.map(function (xValue) {\n            return (xValue - xOffset) / xAxisUnit;\n        });\n    }\n    /**\n     * Find the closest distance between points in the base series.\n     * @private\n     * @param {Array<number>} xData list of all x coordinates in the base series\n     * @return {number} - closest distance between points in the base series\n     */\n    findClosestDistance(xData) {\n        let distance, closestDistance, i;\n        for (i = 1; i < xData.length - 1; i++) {\n            distance = xData[i] - xData[i - 1];\n            if (distance > 0 &&\n                (typeof closestDistance === 'undefined' ||\n                    distance < closestDistance)) {\n                closestDistance = distance;\n            }\n        }\n        return closestDistance;\n    }\n    // Required to be implemented - starting point for indicator's logic\n    getValues(baseSeries, regressionSeriesParams) {\n        const xData = baseSeries.xData, yData = baseSeries.yData, period = regressionSeriesParams.period, \n        // Format required to be returned\n        indicatorData = {\n            xData: [], // By getValues() method\n            yData: [],\n            values: []\n        }, xAxisUnit = this.options.params.xAxisUnit ||\n            this.findClosestDistance(xData);\n        let lineParameters, i, periodStart, periodEnd, endPointX, endPointY, periodXData, periodYData, periodTransformedXData;\n        // Iteration logic: x value of the last point within the period\n        // (end point) is used to represent the y value (regression)\n        // of the entire period.\n        for (i = period - 1; i <= xData.length - 1; i++) {\n            periodStart = i - period + 1; // Adjusted for slice() function\n            periodEnd = i + 1; // (as above)\n            endPointX = xData[i];\n            periodXData = xData.slice(periodStart, periodEnd);\n            periodYData = yData.slice(periodStart, periodEnd);\n            periodTransformedXData = this.transformXData(periodXData, xAxisUnit);\n            lineParameters = this.getRegressionLineParameters(periodTransformedXData, periodYData);\n            endPointY = this.getEndPointY(lineParameters, periodTransformedXData[periodTransformedXData.length - 1]);\n            // @todo this is probably not used anywhere\n            indicatorData.values.push({\n                regressionLineParameters: lineParameters,\n                x: endPointX,\n                y: endPointY\n            });\n            if (isArray(indicatorData.xData)) {\n                indicatorData.xData.push(endPointX);\n            }\n            if (isArray(indicatorData.yData)) {\n                indicatorData.yData.push(endPointY);\n            }\n        }\n        return indicatorData;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Linear regression indicator. This series requires `linkedTo` option to be\n * set.\n *\n * @sample {highstock} stock/indicators/linear-regression\n *         Linear regression indicator\n *\n * @extends      plotOptions.sma\n * @since        7.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/regressions\n * @optionparent plotOptions.linearregression\n */\nLinearRegressionIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    params: {\n        /**\n         * Unit (in milliseconds) for the x axis distances used to\n         * compute the regression line parameters (slope & intercept)\n         * for every range. In Highcharts Stock the x axis values are\n         * always represented in milliseconds which may cause that\n         * distances between points are \"big\" integer numbers.\n         *\n         * Highcharts Stock's linear regression algorithm (least squares\n         * method) will utilize these \"big\" integers for finding the\n         * slope and the intercept of the regression line for each\n         * period. In consequence, this value may be a very \"small\"\n         * decimal number that's hard to interpret by a human.\n         *\n         * For instance: `xAxisUnit` equaled to `86400000` ms (1 day)\n         * forces the algorithm to treat `86400000` as `1` while\n         * computing the slope and the intercept. This may enhance the\n         * legibility of the indicator's values.\n         *\n         * Default value is the closest distance between two data\n         * points.\n         *\n         * In `v9.0.2`, the default value has been changed\n         * from `undefined` to `null`.\n         *\n         * @sample {highstock} stock/plotoptions/linear-regression-xaxisunit\n         *         xAxisUnit set to 1 minute\n         *\n         * @example\n         * // In Liniear Regression Slope Indicator series `xAxisUnit`is\n         * // `86400000` (1 day) and period is `3`. There're 3 points in\n         * // the base series:\n         *\n         * data: [\n         *   [Date.UTC(2020, 0, 1), 1],\n         *   [Date.UTC(2020, 0, 2), 3],\n         *   [Date.UTC(2020, 0, 3), 5]\n         * ]\n         *\n         * // This will produce one point in the indicator series that\n         * // has a `y` value of `2` (slope of the regression line). If\n         * // we change the `xAxisUnit` to `1` (ms) the value of the\n         * // indicator's point will be `2.3148148148148148e-8` which is\n         * // harder to interpert for a human.\n         *\n         * @type    {null|number}\n         * @product highstock\n         */\n        xAxisUnit: null\n    },\n    tooltip: {\n        valueDecimals: 4\n    }\n});\nextend(LinearRegressionIndicator.prototype, {\n    nameBase: 'Linear Regression Indicator'\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('linearRegression', LinearRegressionIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const LinearRegression_LinearRegressionIndicator = ((/* unused pure expression or super */ null && (LinearRegressionIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A linear regression series. If the\n * [type](#series.linearregression.type) option is not specified, it is\n * inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.linearregression\n * @since     7.0.0\n * @product   highstock\n * @excluding dataParser,dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/regressions\n * @apioption series.linearregression\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/LinearRegressionSlopes/LinearRegressionSlopesIndicator.js\n/**\n *\n *  (c) 2010-2025 Kamil Kulig\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { linearRegression: LinearRegressionSlopesIndicator_LinearRegressionIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: LinearRegressionSlopesIndicator_extend, merge: LinearRegressionSlopesIndicator_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Linear Regression Slope series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.linearRegressionSlope\n *\n * @augments Highcharts.Series\n */\nclass LinearRegressionSlopesIndicator extends LinearRegressionSlopesIndicator_LinearRegressionIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getEndPointY(lineParameters) {\n        return lineParameters.slope;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Linear regression slope indicator. This series requires `linkedTo`\n * option to be set.\n *\n * @sample {highstock} stock/indicators/linear-regression-slope\n *         Linear regression slope indicator\n *\n * @extends      plotOptions.linearregression\n * @since        7.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires  stock/indicators/regressions\n * @optionparent plotOptions.linearregressionslope\n */\nLinearRegressionSlopesIndicator.defaultOptions = LinearRegressionSlopesIndicator_merge(LinearRegressionSlopesIndicator_LinearRegressionIndicator.defaultOptions);\nLinearRegressionSlopesIndicator_extend(LinearRegressionSlopesIndicator.prototype, {\n    nameBase: 'Linear Regression Slope Indicator'\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('linearRegressionSlope', LinearRegressionSlopesIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const LinearRegressionSlopes_LinearRegressionSlopesIndicator = ((/* unused pure expression or super */ null && (LinearRegressionSlopesIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A linear regression intercept series. If the\n * [type](#series.linearregressionslope.type) option is not specified, it is\n * inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.linearregressionslope\n * @since     7.0.0\n * @product   highstock\n * @excluding dataParser,dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/regressions\n * @apioption series.linearregressionslope\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/LinearRegressionIntercept/LinearRegressionInterceptIndicator.js\n/**\n *\n *  (c) 2010-2025 Kamil Kulig\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { linearRegression: LinearRegressionInterceptIndicator_LinearRegressionIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: LinearRegressionInterceptIndicator_extend, merge: LinearRegressionInterceptIndicator_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Linear Regression Intercept series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.linearRegressionIntercept\n *\n * @augments Highcharts.Series\n */\nclass LinearRegressionInterceptIndicator extends LinearRegressionInterceptIndicator_LinearRegressionIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getEndPointY(lineParameters) {\n        return lineParameters.intercept;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Linear regression intercept indicator. This series requires `linkedTo`\n * option to be set.\n *\n * @sample {highstock} stock/indicators/linear-regression-intercept\n *         Linear intercept slope indicator\n *\n * @extends      plotOptions.linearregression\n * @since        7.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires  stock/indicators/regressions\n * @optionparent plotOptions.linearregressionintercept\n */\nLinearRegressionInterceptIndicator.defaultOptions = LinearRegressionInterceptIndicator_merge(LinearRegressionInterceptIndicator_LinearRegressionIndicator.defaultOptions);\nLinearRegressionInterceptIndicator_extend(LinearRegressionInterceptIndicator.prototype, {\n    nameBase: 'Linear Regression Intercept Indicator'\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('linearRegressionIntercept', LinearRegressionInterceptIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const LinearRegressionIntercept_LinearRegressionInterceptIndicator = ((/* unused pure expression or super */ null && (LinearRegressionInterceptIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A linear regression intercept series. If the\n * [type](#series.linearregressionintercept.type) option is not specified, it is\n * inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.linearregressionintercept\n * @since     7.0.0\n * @product   highstock\n * @excluding dataParser,dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/regressions\n * @apioption series.linearregressionintercept\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/LinearRegressionAngle/LinearRegressionAngleIndicator.js\n/**\n *\n *  (c) 2010-2025 Kamil Kulig\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { linearRegression: LinearRegressionAngleIndicator_LinearRegressionIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: LinearRegressionAngleIndicator_extend, merge: LinearRegressionAngleIndicator_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Linear Regression Angle series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.linearRegressionAngle\n *\n * @augments Highcharts.Series\n */\nclass LinearRegressionAngleIndicator extends LinearRegressionAngleIndicator_LinearRegressionIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Convert a slope of a line to angle (in degrees) between\n     * the line and x axis\n     * @private\n     * @param {number} slope of the straight line function\n     * @return {number} angle in degrees\n     */\n    slopeToAngle(slope) {\n        return Math.atan(slope) * (180 / Math.PI); // Rad to deg\n    }\n    getEndPointY(lineParameters) {\n        return this.slopeToAngle(lineParameters.slope);\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Linear regression angle indicator. This series requires `linkedTo`\n * option to be set.\n *\n * @sample {highstock} stock/indicators/linear-regression-angle\n *         Linear intercept angle indicator\n *\n * @extends      plotOptions.linearregression\n * @since        7.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires  stock/indicators/regressions\n * @optionparent plotOptions.linearregressionangle\n */\nLinearRegressionAngleIndicator.defaultOptions = LinearRegressionAngleIndicator_merge(LinearRegressionAngleIndicator_LinearRegressionIndicator.defaultOptions, {\n    tooltip: {\n        pointFormat: '<span style=\"color:{point.color}\">\\u25CF</span>' +\n            '{series.name}: <b>{point.y}°</b><br/>'\n    }\n});\nLinearRegressionAngleIndicator_extend(LinearRegressionAngleIndicator.prototype, {\n    nameBase: 'Linear Regression Angle Indicator'\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('linearRegressionAngle', LinearRegressionAngleIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const LinearRegressionAngle_LinearRegressionAngleIndicator = ((/* unused pure expression or super */ null && (LinearRegressionAngleIndicator)));\n/**\n * A linear regression intercept series. If the\n * [type](#series.linearregressionangle.type) option is not specified, it is\n * inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.linearregressionangle\n * @since     7.0.0\n * @product   highstock\n * @excluding dataParser,dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/regressions\n * @apioption series.linearregressionangle\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/masters/indicators/regressions.js\n\n\n\n\n// eslint-disable-next-line max-len\n\n// eslint-disable-next-line max-len\n\n\n/* harmony default export */ const regressions_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "regressions_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "sma", "SMAIndicator", "seriesTypes", "isArray", "extend", "merge", "LinearRegressionIndicator", "getRegressionLineParameters", "xData", "yData", "yIndex", "options", "params", "index", "getSingleYValue", "yValue", "xSum", "reduce", "accX", "val", "ySum", "accY", "xMean", "length", "y<PERSON><PERSON>", "xError", "i", "formulaNumerator", "formulaDenominator", "Math", "pow", "slope", "intercept", "getEndPointY", "lineParameters", "endPointX", "transformXData", "xAxisUnit", "xOffset", "map", "xValue", "findClosestDistance", "distance", "closestDistance", "getV<PERSON>ues", "baseSeries", "regressionSeriesParams", "periodStart", "periodEnd", "endPointY", "periodXData", "periodYData", "periodTransformedXData", "period", "indicatorData", "values", "slice", "push", "regressionLineParameters", "x", "y", "defaultOptions", "tooltip", "valueDecimals", "nameBase", "registerSeriesType", "linearRegression", "LinearRegressionSlopesIndicator_LinearRegressionIndicator", "LinearRegressionSlopesIndicator_extend", "LinearRegressionSlopesIndicator_merge", "LinearRegressionSlopesIndicator", "LinearRegressionInterceptIndicator_LinearRegressionIndicator", "LinearRegressionInterceptIndicator_extend", "LinearRegressionInterceptIndicator_merge", "LinearRegressionInterceptIndicator", "LinearRegressionAngleIndicator_LinearRegressionIndicator", "LinearRegressionAngleIndicator_extend", "LinearRegressionAngleIndicator_merge", "LinearRegressionAngleIndicator", "slopeToAngle", "PI", "atan", "pointFormat"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,oCAAqC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GAC7H,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,oCAAoC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAEjHA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAajL,GAAM,CAAEE,IAAKC,CAAY,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAE/K,CAAEC,QAAAA,CAAO,CAAEC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAE,CAAIR,GAepC,OAAMS,UAAkCL,EAqBpCM,4BAA4BC,CAAK,CAAEC,CAAK,CAAE,CAEtC,IAAMC,EAAS,IAAI,CAACC,OAAO,CAACC,MAAM,CAACC,KAAK,CAAEC,EAAkB,SAAUC,CAAM,CAAEL,CAAM,EAChF,OAAOP,EAAQY,GAAUA,CAAM,CAACL,EAAO,CAAGK,CAC9C,EAAGC,EAAOR,EAAMS,MAAM,CAAC,SAAUC,CAAI,CAAEC,CAAG,EACtC,OAAOA,EAAMD,CACjB,EAAG,GAAIE,EAAOX,EAAMQ,MAAM,CAAC,SAAUI,CAAI,CAAEF,CAAG,EAC1C,OAAOL,EAAgBK,EAAKT,GAAUW,CAC1C,EAAG,GAAIC,EAAQN,EAAOR,EAAMe,MAAM,CAAEC,EAAQJ,EAAOX,EAAMc,MAAM,CAC3DE,EAAgBC,EAAGC,EAAmB,EAAGC,EAAqB,EAClE,IAAKF,EAAI,EAAGA,EAAIlB,EAAMe,MAAM,CAAEG,IAG1BC,GAAoBF,AAFpBA,CAAAA,EAASjB,CAAK,CAACkB,EAAE,CAAGJ,CAAI,EACfR,CAAAA,EAAgBL,CAAK,CAACiB,EAAE,CAAEhB,GAAUc,CAAI,EAEjDI,GAAsBC,KAAKC,GAAG,CAACL,EAAQ,GAE3C,IAAMM,EAAQH,EACVD,EAAmBC,EAAqB,EAC5C,MAAO,CACHG,MAAOA,EACPC,UAAWR,EAAQO,EAAQT,CAC/B,CACJ,CAgBAW,aAAaC,CAAc,CAAEC,CAAS,CAAE,CACpC,OAAOD,EAAeH,KAAK,CAAGI,EAAYD,EAAeF,SAAS,AACtE,CAgBAI,eAAe5B,CAAK,CAAE6B,CAAS,CAAE,CAC7B,IAAMC,EAAU9B,CAAK,CAAC,EAAE,CACxB,OAAOA,EAAM+B,GAAG,CAAC,SAAUC,CAAM,EAC7B,MAAO,AAACA,CAAAA,EAASF,CAAM,EAAKD,CAChC,EACJ,CAOAI,oBAAoBjC,CAAK,CAAE,CACvB,IAAIkC,EAAUC,EAAiBjB,EAC/B,IAAKA,EAAI,EAAGA,EAAIlB,EAAMe,MAAM,CAAG,EAAGG,IAE1BgB,AADJA,CAAAA,EAAWlC,CAAK,CAACkB,EAAE,CAAGlB,CAAK,CAACkB,EAAI,EAAE,AAAD,EAClB,GACV,CAAA,AAA2B,KAAA,IAApBiB,GACJD,EAAWC,CAAc,GAC7BA,CAAAA,EAAkBD,CAAO,EAGjC,OAAOC,CACX,CAEAC,UAAUC,CAAU,CAAEC,CAAsB,CAAE,CAC1C,IAQIZ,EAAgBR,EAAGqB,EAAaC,EAAWb,EAAWc,EAAWC,EAAaC,EAAaC,EARzF5C,EAAQqC,EAAWrC,KAAK,CAAEC,EAAQoC,EAAWpC,KAAK,CAAE4C,EAASP,EAAuBO,MAAM,CAEhGC,EAAgB,CACZ9C,MAAO,EAAE,CACTC,MAAO,EAAE,CACT8C,OAAQ,EAAE,AACd,EAAGlB,EAAY,IAAI,CAAC1B,OAAO,CAACC,MAAM,CAACyB,SAAS,EACxC,IAAI,CAACI,mBAAmB,CAACjC,GAK7B,IAAKkB,EAAI2B,EAAS,EAAG3B,GAAKlB,EAAMe,MAAM,CAAG,EAAGG,IACxCqB,EAAcrB,EAAI2B,EAAS,EAC3BL,EAAYtB,EAAI,EAChBS,EAAY3B,CAAK,CAACkB,EAAE,CACpBwB,EAAc1C,EAAMgD,KAAK,CAACT,EAAaC,GACvCG,EAAc1C,EAAM+C,KAAK,CAACT,EAAaC,GACvCI,EAAyB,IAAI,CAAChB,cAAc,CAACc,EAAab,GAC1DH,EAAiB,IAAI,CAAC3B,2BAA2B,CAAC6C,EAAwBD,GAC1EF,EAAY,IAAI,CAAChB,YAAY,CAACC,EAAgBkB,CAAsB,CAACA,EAAuB7B,MAAM,CAAG,EAAE,EAEvG+B,EAAcC,MAAM,CAACE,IAAI,CAAC,CACtBC,yBAA0BxB,EAC1ByB,EAAGxB,EACHyB,EAAGX,CACP,GACI9C,EAAQmD,EAAc9C,KAAK,GAC3B8C,EAAc9C,KAAK,CAACiD,IAAI,CAACtB,GAEzBhC,EAAQmD,EAAc7C,KAAK,GAC3B6C,EAAc7C,KAAK,CAACgD,IAAI,CAACR,GAGjC,OAAOK,CACX,CACJ,CAoBAhD,EAA0BuD,cAAc,CAAGxD,EAAMJ,EAAa4D,cAAc,CAAE,CAC1EjD,OAAQ,CAgDJyB,UAAW,IACf,EACAyB,QAAS,CACLC,cAAe,CACnB,CACJ,GACA3D,EAAOE,EAA0Bf,SAAS,CAAE,CACxCyE,SAAU,6BACd,GACAjE,IAA0IkE,kBAAkB,CAAC,mBAAoB3D,GAuCjL,GAAM,CAAE4D,iBAAkBC,CAAyD,CAAE,CAAG,AAACpE,IAA2IG,WAAW,CAEzO,CAAEE,OAAQgE,CAAsC,CAAE/D,MAAOgE,CAAqC,CAAE,CAAIxE,GAe1G,OAAMyE,UAAwCH,EAM1ClC,aAAaC,CAAc,CAAE,CACzB,OAAOA,EAAeH,KAAK,AAC/B,CACJ,CAoBAuC,EAAgCT,cAAc,CAAGQ,EAAsCF,EAA0DN,cAAc,EAC/JO,EAAuCE,EAAgC/E,SAAS,CAAE,CAC9EyE,SAAU,mCACd,GACAjE,IAA0IkE,kBAAkB,CAAC,wBAAyBK,GAuCtL,GAAM,CAAEJ,iBAAkBK,CAA4D,CAAE,CAAG,AAACxE,IAA2IG,WAAW,CAE5O,CAAEE,OAAQoE,CAAyC,CAAEnE,MAAOoE,CAAwC,CAAE,CAAI5E,GAehH,OAAM6E,UAA2CH,EAM7CtC,aAAaC,CAAc,CAAE,CACzB,OAAOA,EAAeF,SAAS,AACnC,CACJ,CAoBA0C,EAAmCb,cAAc,CAAGY,EAAyCF,EAA6DV,cAAc,EACxKW,EAA0CE,EAAmCnF,SAAS,CAAE,CACpFyE,SAAU,uCACd,GACAjE,IAA0IkE,kBAAkB,CAAC,4BAA6BS,GAuC1L,GAAM,CAAER,iBAAkBS,CAAwD,CAAE,CAAG,AAAC5E,IAA2IG,WAAW,CAExO,CAAEE,OAAQwE,CAAqC,CAAEvE,MAAOwE,CAAoC,CAAE,CAAIhF,GAexG,OAAMiF,UAAuCH,EAazCI,aAAahD,CAAK,CAAE,CAChB,OAAOF,AAAoB,IAAMA,KAAKmD,EAAE,CAAjCnD,KAAKoD,IAAI,CAAClD,EACrB,CACAE,aAAaC,CAAc,CAAE,CACzB,OAAO,IAAI,CAAC6C,YAAY,CAAC7C,EAAeH,KAAK,CACjD,CACJ,CAoBA+C,EAA+BjB,cAAc,CAAGgB,EAAqCF,EAAyDd,cAAc,CAAE,CAC1JC,QAAS,CACLoB,YAAa,oFAEjB,CACJ,GACAN,EAAsCE,EAA+BvF,SAAS,CAAE,CAC5EyE,SAAU,mCACd,GACAjE,IAA0IkE,kBAAkB,CAAC,wBAAyBa,GAgCzJ,IAAMnF,EAAoBE,IAG7C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}