{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/roc\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Kacper Madej\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/roc\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/roc\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ roc_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/ROC/ROCIndicator.js\n/* *\n *\n *  (c) 2010-2025 Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { isArray, merge, extend } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n// Utils:\n/**\n *\n */\nfunction populateAverage(xVal, yVal, i, period, index) {\n    /* Calculated as:\n\n       (Closing Price [today] - Closing Price [n days ago]) /\n        Closing Price [n days ago] * 100\n\n       Return y as null when avoiding division by zero */\n    let nDaysAgoY, rocY;\n    if (index < 0) {\n        // Y data given as an array of values\n        nDaysAgoY = yVal[i - period];\n        rocY = nDaysAgoY ?\n            (yVal[i] - nDaysAgoY) / nDaysAgoY * 100 :\n            null;\n    }\n    else {\n        // Y data given as an array of arrays and the index should be used\n        nDaysAgoY = yVal[i - period][index];\n        rocY = nDaysAgoY ?\n            (yVal[i][index] - nDaysAgoY) / nDaysAgoY * 100 :\n            null;\n    }\n    return [xVal[i], rocY];\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The ROC series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.roc\n *\n * @augments Highcharts.Series\n */\nclass ROCIndicator extends SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, ROC = [], xData = [], yData = [];\n        let i, index = -1, ROCPoint;\n        // Period is used as a number of time periods ago, so we need more\n        // (at least 1 more) data than the period value\n        if (xVal.length <= period) {\n            return;\n        }\n        // Switch index for OHLC / Candlestick / Arearange\n        if (isArray(yVal[0])) {\n            index = params.index;\n        }\n        // I = period <-- skip first N-points\n        // Calculate value one-by-one for each period in visible data\n        for (i = period; i < yValLen; i++) {\n            ROCPoint = populateAverage(xVal, yVal, i, period, index);\n            ROC.push(ROCPoint);\n            xData.push(ROCPoint[0]);\n            yData.push(ROCPoint[1]);\n        }\n        return {\n            values: ROC,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Rate of change indicator (ROC). The indicator value for each point\n * is defined as:\n *\n * `(C - Cn) / Cn * 100`\n *\n * where: `C` is the close value of the point of the same x in the\n * linked series and `Cn` is the close value of the point `n` periods\n * ago. `n` is set through [period](#plotOptions.roc.params.period).\n *\n * This series requires `linkedTo` option to be set.\n *\n * @sample stock/indicators/roc\n *         Rate of change indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/roc\n * @optionparent plotOptions.roc\n */\nROCIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    params: {\n        index: 3,\n        period: 9\n    }\n});\nextend(ROCIndicator.prototype, {\n    nameBase: 'Rate of Change'\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('roc', ROCIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const ROC_ROCIndicator = ((/* unused pure expression or super */ null && (ROCIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `ROC` series. If the [type](#series.wma.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * Rate of change indicator (ROC). The indicator value for each point\n * is defined as:\n *\n * `(C - Cn) / Cn * 100`\n *\n * where: `C` is the close value of the point of the same x in the\n * linked series and `Cn` is the close value of the point `n` periods\n * ago. `n` is set through [period](#series.roc.params.period).\n *\n * This series requires `linkedTo` option to be set.\n *\n * @extends   series,plotOptions.roc\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/roc\n * @apioption series.roc\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/masters/indicators/roc.js\n\n\n\n\n/* harmony default export */ const roc_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "roc_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "sma", "SMAIndicator", "seriesTypes", "isArray", "merge", "extend", "ROCIndicator", "getV<PERSON>ues", "series", "params", "period", "xVal", "xData", "yVal", "yData", "yValLen", "length", "ROC", "i", "index", "ROCPoint", "populateAverage", "nDaysAgoY", "rocY", "push", "values", "defaultOptions", "nameBase", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,4BAA6B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACrH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,4BAA4B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAEzGA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAajL,GAAM,CAAEE,IAAKC,CAAY,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAE/K,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAE,CAAIR,GAgDpC,OAAMS,UAAqBL,EAMvBM,UAAUC,CAAM,CAAEC,CAAM,CAAE,CACtB,IAAMC,EAASD,EAAOC,MAAM,CAAEC,EAAOH,EAAOI,KAAK,CAAEC,EAAOL,EAAOM,KAAK,CAAEC,EAAUF,EAAOA,EAAKG,MAAM,CAAG,EAAGC,EAAM,EAAE,CAAEL,EAAQ,EAAE,CAAEE,EAAQ,EAAE,CACtII,EAAGC,EAAQ,GAAIC,EAGnB,IAAIT,CAAAA,EAAKK,MAAM,EAAIN,CAAK,GASxB,IALIP,EAAQU,CAAI,CAAC,EAAE,GACfM,CAAAA,EAAQV,EAAOU,KAAK,AAAD,EAIlBD,EAAIR,EAAQQ,EAAIH,EAASG,IAC1BE,EAAWC,AA3DvB,SAAyBV,CAAI,CAAEE,CAAI,CAAEK,CAAC,CAAER,CAAM,CAAES,CAAK,EAOjD,IAAIG,EAAWC,EAef,OAXIA,EAHAJ,EAAQ,EAGDG,AADPA,CAAAA,EAAYT,CAAI,CAACK,EAAIR,EAAO,AAAD,EAEvB,AAACG,CAAAA,CAAI,CAACK,EAAE,CAAGI,CAAQ,EAAKA,EAAY,IACpC,KAKGA,AADPA,CAAAA,EAAYT,CAAI,CAACK,EAAIR,EAAO,CAACS,EAAM,AAAD,EAE9B,AAACN,CAAAA,CAAI,CAACK,EAAE,CAACC,EAAM,CAAGG,CAAQ,EAAKA,EAAY,IAC3C,KAED,CAACX,CAAI,CAACO,EAAE,CAAEK,EAAK,AAC1B,EAoCuCZ,EAAME,EAAMK,EAAGR,EAAQS,GAClDF,EAAIO,IAAI,CAACJ,GACTR,EAAMY,IAAI,CAACJ,CAAQ,CAAC,EAAE,EACtBN,EAAMU,IAAI,CAACJ,CAAQ,CAAC,EAAE,EAE1B,MAAO,CACHK,OAAQR,EACRL,MAAOA,EACPE,MAAOA,CACX,EACJ,CACJ,CA4BAR,EAAaoB,cAAc,CAAGtB,EAAMH,EAAayB,cAAc,CAAE,CAC7DjB,OAAQ,CACJU,MAAO,EACPT,OAAQ,CACZ,CACJ,GACAL,EAAOC,EAAaf,SAAS,CAAE,CAC3BoC,SAAU,gBACd,GACA5B,IAA0I6B,kBAAkB,CAAC,MAAOtB,GA0CvI,IAAMX,EAAYE,IAGrC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}