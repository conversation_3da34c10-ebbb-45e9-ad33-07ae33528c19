{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/trendline\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Sebastian <PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/trendline\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/trendline\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ trendline_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/TrendLine/TrendLineIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { extend, merge, isArray } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Trend line series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.trendline\n *\n * @augments Highcharts.Series\n */\nclass TrendLineIndicator extends SMAIndicator {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        this.updateAllPoints = true;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const orgXVal = series.xData, yVal = series.yData, xVal = [], LR = [], xData = [], yData = [], index = params.index;\n        let numerator = 0, denominator = 0, xValSum = 0, yValSum = 0, counter = 0;\n        // Create an array of consecutive xValues, (don't remove duplicates)\n        for (let i = 0; i < orgXVal.length; i++) {\n            if (i === 0 || orgXVal[i] !== orgXVal[i - 1]) {\n                counter++;\n            }\n            xVal.push(counter);\n        }\n        for (let i = 0; i < xVal.length; i++) {\n            xValSum += xVal[i];\n            yValSum += isArray(yVal[i]) ? yVal[i][index] : yVal[i];\n        }\n        const meanX = xValSum / xVal.length, meanY = yValSum / yVal.length;\n        for (let i = 0; i < xVal.length; i++) {\n            const y = isArray(yVal[i]) ? yVal[i][index] : yVal[i];\n            numerator += (xVal[i] - meanX) * (y - meanY);\n            denominator += Math.pow(xVal[i] - meanX, 2);\n        }\n        // Calculate linear regression:\n        for (let i = 0; i < xVal.length; i++) {\n            // Check if the xVal is already used\n            if (orgXVal[i] === xData[xData.length - 1]) {\n                continue;\n            }\n            const x = orgXVal[i], y = meanY + (numerator / denominator) * (xVal[i] - meanX);\n            LR.push([x, y]);\n            xData.push(x);\n            yData.push(y);\n        }\n        return {\n            xData: xData,\n            yData: yData,\n            values: LR\n        };\n    }\n}\n/**\n * Trendline (linear regression) fits a straight line to the selected data\n * using a method called the Sum Of Least Squares. This series requires the\n * `linkedTo` option to be set.\n *\n * @sample stock/indicators/trendline\n *         Trendline indicator\n *\n * @extends      plotOptions.sma\n * @since        7.1.3\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/trendline\n * @optionparent plotOptions.trendline\n */\nTrendLineIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    /**\n     * @excluding period\n     */\n    params: {\n        period: void 0, // Unchangeable period, do not inherit (#15362)\n        /**\n         * The point index which indicator calculations will base. For\n         * example using OHLC data, index=2 means the indicator will be\n         * calculated using Low values.\n         *\n         * @default 3\n         */\n        index: 3\n    }\n});\nextend(TrendLineIndicator.prototype, {\n    nameBase: 'Trendline',\n    nameComponents: void 0\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('trendline', TrendLineIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const TrendLine_TrendLineIndicator = ((/* unused pure expression or super */ null && (TrendLineIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `TrendLine` series. If the [type](#series.trendline.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.trendline\n * @since     7.1.3\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/trendline\n * @apioption series.trendline\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/masters/indicators/trendline.js\n\n\n\n\n/* harmony default export */ const trendline_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "trendline_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "sma", "SMAIndicator", "seriesTypes", "extend", "merge", "isArray", "TrendLineIndicator", "constructor", "arguments", "updateAllPoints", "getV<PERSON>ues", "series", "params", "orgXVal", "xData", "yVal", "yData", "xVal", "LR", "index", "numerator", "denominator", "xValSum", "yValSum", "counter", "i", "length", "push", "meanX", "meanY", "y", "Math", "pow", "x", "values", "defaultOptions", "period", "nameBase", "nameComponents", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,kCAAmC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GAC3H,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,kCAAkC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAE/GA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAWjL,GAAM,CAAEE,IAAKC,CAAY,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAE/K,CAAEC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAEC,QAAAA,CAAO,CAAE,CAAIR,GAepC,OAAMS,UAA2BL,EAC7BM,aAAc,CAMV,KAAK,IAAIC,WACT,IAAI,CAACC,eAAe,CAAG,CAAA,CAC3B,CAMAC,UAAUC,CAAM,CAAEC,CAAM,CAAE,CACtB,IAAMC,EAAUF,EAAOG,KAAK,CAAEC,EAAOJ,EAAOK,KAAK,CAAEC,EAAO,EAAE,CAAEC,EAAK,EAAE,CAAEJ,EAAQ,EAAE,CAAEE,EAAQ,EAAE,CAAEG,EAAQP,EAAOO,KAAK,CAC/GC,EAAY,EAAGC,EAAc,EAAGC,EAAU,EAAGC,EAAU,EAAGC,EAAU,EAExE,IAAK,IAAIC,EAAI,EAAGA,EAAIZ,EAAQa,MAAM,CAAED,IAC5BA,CAAAA,AAAM,IAANA,GAAWZ,CAAO,CAACY,EAAE,GAAKZ,CAAO,CAACY,EAAI,EAAE,AAAD,GACvCD,IAEJP,EAAKU,IAAI,CAACH,GAEd,IAAK,IAAIC,EAAI,EAAGA,EAAIR,EAAKS,MAAM,CAAED,IAC7BH,GAAWL,CAAI,CAACQ,EAAE,CAClBF,GAAWlB,EAAQU,CAAI,CAACU,EAAE,EAAIV,CAAI,CAACU,EAAE,CAACN,EAAM,CAAGJ,CAAI,CAACU,EAAE,CAE1D,IAAMG,EAAQN,EAAUL,EAAKS,MAAM,CAAEG,EAAQN,EAAUR,EAAKW,MAAM,CAClE,IAAK,IAAID,EAAI,EAAGA,EAAIR,EAAKS,MAAM,CAAED,IAAK,CAClC,IAAMK,EAAIzB,EAAQU,CAAI,CAACU,EAAE,EAAIV,CAAI,CAACU,EAAE,CAACN,EAAM,CAAGJ,CAAI,CAACU,EAAE,CACrDL,GAAa,AAACH,CAAAA,CAAI,CAACQ,EAAE,CAAGG,CAAI,EAAME,CAAAA,EAAID,CAAI,EAC1CR,GAAeU,KAAKC,GAAG,CAACf,CAAI,CAACQ,EAAE,CAAGG,EAAO,EAC7C,CAEA,IAAK,IAAIH,EAAI,EAAGA,EAAIR,EAAKS,MAAM,CAAED,IAAK,CAElC,GAAIZ,CAAO,CAACY,EAAE,GAAKX,CAAK,CAACA,EAAMY,MAAM,CAAG,EAAE,CACtC,SAEJ,IAAMO,EAAIpB,CAAO,CAACY,EAAE,CAAEK,EAAID,EAAQ,AAACT,EAAYC,EAAgBJ,CAAAA,CAAI,CAACQ,EAAE,CAAGG,CAAI,EAC7EV,EAAGS,IAAI,CAAC,CAACM,EAAGH,EAAE,EACdhB,EAAMa,IAAI,CAACM,GACXjB,EAAMW,IAAI,CAACG,EACf,CACA,MAAO,CACHhB,MAAOA,EACPE,MAAOA,EACPkB,OAAQhB,CACZ,CACJ,CACJ,CAgBAZ,EAAmB6B,cAAc,CAAG/B,EAAMH,EAAakC,cAAc,CAAE,CAInEvB,OAAQ,CACJwB,OAAQ,KAAK,EAQbjB,MAAO,CACX,CACJ,GACAhB,EAAOG,EAAmBf,SAAS,CAAE,CACjC8C,SAAU,YACVC,eAAgB,KAAK,CACzB,GACAvC,IAA0IwC,kBAAkB,CAAC,YAAajC,GA+B7I,IAAMX,EAAkBE,IAG3C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}