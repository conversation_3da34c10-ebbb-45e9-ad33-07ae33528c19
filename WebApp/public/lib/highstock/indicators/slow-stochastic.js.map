{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/indicators\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Slow Stochastic series type for Highcharts Stock\n *\n * (c) 2010-2025 Pawel Fus\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/slow-stochastic\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/slow-stochastic\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ slow_stochastic_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/SlowStochastic/SlowStochasticIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: SMAIndicator, stochastic: StochasticIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { extend, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Slow Stochastic series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.slowstochastic\n *\n * @augments Highcharts.Series\n */\nclass SlowStochasticIndicator extends StochasticIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const periods = params.periods, fastValues = super.getValues.call(this, series, params), slowValues = {\n            values: [],\n            xData: [],\n            yData: []\n        };\n        if (!fastValues) {\n            return;\n        }\n        slowValues.xData = fastValues.xData.slice(periods[1] - 1);\n        const fastYData = fastValues.yData.slice(periods[1] - 1);\n        // Get SMA(%D)\n        const smoothedValues = SMAIndicator.prototype.getValues.call(this, {\n            xData: slowValues.xData,\n            yData: fastYData\n        }, {\n            index: 1,\n            period: periods[2]\n        });\n        if (!smoothedValues) {\n            return;\n        }\n        // Format data\n        for (let i = 0, xDataLen = slowValues.xData.length; i < xDataLen; i++) {\n            slowValues.yData[i] = [\n                fastYData[i][1],\n                smoothedValues.yData[i - periods[2] + 1] || null\n            ];\n            slowValues.values[i] = [\n                slowValues.xData[i],\n                fastYData[i][1],\n                smoothedValues.yData[i - periods[2] + 1] || null\n            ];\n        }\n        return slowValues;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Slow Stochastic oscillator. This series requires the `linkedTo` option\n * to be set and should be loaded after `stock/indicators/indicators.js`\n * and `stock/indicators/stochastic.js` files.\n *\n * @sample stock/indicators/slow-stochastic\n *         Slow Stochastic oscillator\n *\n * @extends      plotOptions.stochastic\n * @since        8.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/stochastic\n * @requires     stock/indicators/slow-stochastic\n * @optionparent plotOptions.slowstochastic\n */\nSlowStochasticIndicator.defaultOptions = merge(StochasticIndicator.defaultOptions, {\n    params: {\n        /**\n         * Periods for Slow Stochastic oscillator: [%K, %D, SMA(%D)].\n         *\n         * @type    {Array<number,number,number>}\n         * @default [14, 3, 3]\n         */\n        periods: [14, 3, 3]\n    }\n});\nextend(SlowStochasticIndicator.prototype, {\n    nameBase: 'Slow Stochastic'\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('slowstochastic', SlowStochasticIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const SlowStochastic_SlowStochasticIndicator = ((/* unused pure expression or super */ null && (SlowStochasticIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A Slow Stochastic indicator. If the [type](#series.slowstochastic.type)\n * option is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.slowstochastic\n * @since     8.0.0\n * @product   highstock\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/stochastic\n * @requires  stock/indicators/slow-stochastic\n * @apioption series.slowstochastic\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/masters/indicators/slow-stochastic.js\n\n\n\n\n/* harmony default export */ const slow_stochastic_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "slow_stochastic_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "sma", "SMAIndicator", "stochastic", "StochasticIndicator", "seriesTypes", "extend", "merge", "SlowStochasticIndicator", "getV<PERSON>ues", "series", "params", "periods", "fastValues", "slowValues", "values", "xData", "yData", "slice", "fastYData", "smoothedValues", "index", "period", "i", "xDataLen", "length", "defaultOptions", "nameBase", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,wCAAyC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACjI,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,wCAAwC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAErHA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAWjL,GAAM,CAAEE,IAAKC,CAAY,CAAEC,WAAYC,CAAmB,CAAE,CAAG,AAACJ,IAA2IK,WAAW,CAEhN,CAAEC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAE,CAAIT,GAe3B,OAAMU,UAAgCJ,EAMlCK,UAAUC,CAAM,CAAEC,CAAM,CAAE,CACtB,IAAMC,EAAUD,EAAOC,OAAO,CAAEC,EAAa,KAAK,CAACJ,UAAUf,IAAI,CAAC,IAAI,CAAEgB,EAAQC,GAASG,EAAa,CAClGC,OAAQ,EAAE,CACVC,MAAO,EAAE,CACTC,MAAO,EAAE,AACb,EACA,GAAI,CAACJ,EACD,MAEJC,CAAAA,EAAWE,KAAK,CAAGH,EAAWG,KAAK,CAACE,KAAK,CAACN,CAAO,CAAC,EAAE,CAAG,GACvD,IAAMO,EAAYN,EAAWI,KAAK,CAACC,KAAK,CAACN,CAAO,CAAC,EAAE,CAAG,GAEhDQ,EAAiBlB,EAAaV,SAAS,CAACiB,SAAS,CAACf,IAAI,CAAC,IAAI,CAAE,CAC/DsB,MAAOF,EAAWE,KAAK,CACvBC,MAAOE,CACX,EAAG,CACCE,MAAO,EACPC,OAAQV,CAAO,CAAC,EAAE,AACtB,GACA,GAAKQ,GAIL,IAAK,IAAIG,EAAI,EAAGC,EAAWV,EAAWE,KAAK,CAACS,MAAM,CAAEF,EAAIC,EAAUD,IAC9DT,EAAWG,KAAK,CAACM,EAAE,CAAG,CAClBJ,CAAS,CAACI,EAAE,CAAC,EAAE,CACfH,EAAeH,KAAK,CAACM,EAAIX,CAAO,CAAC,EAAE,CAAG,EAAE,EAAI,KAC/C,CACDE,EAAWC,MAAM,CAACQ,EAAE,CAAG,CACnBT,EAAWE,KAAK,CAACO,EAAE,CACnBJ,CAAS,CAACI,EAAE,CAAC,EAAE,CACfH,EAAeH,KAAK,CAACM,EAAIX,CAAO,CAAC,EAAE,CAAG,EAAE,EAAI,KAC/C,CAEL,OAAOE,EACX,CACJ,CAsBAN,EAAwBkB,cAAc,CAAGnB,EAAMH,EAAoBsB,cAAc,CAAE,CAC/Ef,OAAQ,CAOJC,QAAS,CAAC,GAAI,EAAG,EAAE,AACvB,CACJ,GACAN,EAAOE,EAAwBhB,SAAS,CAAE,CACtCmC,SAAU,iBACd,GACA3B,IAA0I4B,kBAAkB,CAAC,iBAAkBpB,GA+BlJ,IAAMZ,EAAwBE,IAGjD,OADYH,EAAoB,OAAU,AAE3C,CAAA"}