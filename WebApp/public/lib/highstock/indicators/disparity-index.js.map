{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/disparity-index\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highstock\n *\n * (c) 2010-2025 <PERSON><PERSON><PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/disparity-index\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/disparity-index\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ disparity_index_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/DisparityIndex/DisparityIndexIndicator.js\n/* *\n *  (c) 2010-2025 Rafal Sebestjanski\n *\n *  Disparity Index technical indicator for Highcharts Stock\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { correctFloat, defined, extend, isArray, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Disparity Index series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.disparityindex\n *\n * @augments Highcharts.Series\n */\nclass DisparityIndexIndicator extends SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    init() {\n        const args = arguments, ctx = this, // Disparity Index indicator\n        params = args[1].params, // Options.params\n        averageType = params && params.average ? params.average : void 0;\n        ctx.averageIndicator = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes[averageType] || SMAIndicator;\n        ctx.averageIndicator.prototype.init.apply(ctx, args);\n    }\n    calculateDisparityIndex(curPrice, periodAverage) {\n        return correctFloat(curPrice - periodAverage) / periodAverage * 100;\n    }\n    getValues(series, params) {\n        const index = params.index, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, disparityIndexPoint = [], xData = [], yData = [], \n        // \"as any\" because getValues doesn't exist on typeof Series\n        averageIndicator = this.averageIndicator, isOHLC = isArray(yVal[0]), \n        // Get the average indicator's values\n        values = averageIndicator.prototype.getValues(series, params), yValues = values.yData, start = xVal.indexOf(values.xData[0]);\n        // Check period, if bigger than points length, skip\n        if (!yValues || yValues.length === 0 ||\n            !defined(index) ||\n            yVal.length <= start) {\n            return;\n        }\n        // Get the Disparity Index indicator's values\n        for (let i = start; i < yValLen; i++) {\n            const disparityIndexValue = this.calculateDisparityIndex(isOHLC ? yVal[i][index] : yVal[i], yValues[i - start]);\n            disparityIndexPoint.push([\n                xVal[i],\n                disparityIndexValue\n            ]);\n            xData.push(xVal[i]);\n            yData.push(disparityIndexValue);\n        }\n        return {\n            values: disparityIndexPoint,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Disparity Index.\n * This series requires the `linkedTo` option to be set and should\n * be loaded after the `stock/indicators/indicators.js` file.\n *\n * @sample stock/indicators/disparity-index\n *         Disparity Index indicator\n *\n * @extends      plotOptions.sma\n * @since 9.1.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, joinBy, keys, navigatorOptions,\n *               pointInterval, pointIntervalUnit, pointPlacement,\n *               pointRange, pointStart, showInNavigator, stacking\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/disparity-index\n * @optionparent plotOptions.disparityindex\n */\nDisparityIndexIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    params: {\n        /**\n         * The average used to calculate the Disparity Index indicator.\n         * By default it uses SMA, with EMA as an option. To use other\n         * averages, e.g. TEMA, the `stock/indicators/tema.js` file needs to\n         * be loaded.\n         *\n         * If value is different than `ema`, `dema`, `tema` or `wma`,\n         * then sma is used.\n         */\n        average: 'sma',\n        index: 3\n    },\n    marker: {\n        enabled: false\n    },\n    dataGrouping: {\n        approximation: 'averages'\n    }\n});\nextend(DisparityIndexIndicator.prototype, {\n    nameBase: 'Disparity Index',\n    nameComponents: ['period', 'average']\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('disparityindex', DisparityIndexIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const DisparityIndex_DisparityIndexIndicator = ((/* unused pure expression or super */ null && (DisparityIndexIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * The Disparity Index indicator series.\n * If the [type](#series.disparityindex.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.disparityindex\n * @since 9.1.0\n * @product   highstock\n * @excluding allAreas, colorAxis,  dataParser, dataURL, joinBy, keys,\n *            navigatorOptions, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointRange, pointStart, showInNavigator, stacking\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/disparity-index\n * @apioption series.disparityindex\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/masters/indicators/disparity-index.js\n\n\n\n\n/* harmony default export */ const disparity_index_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "disparity_index_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "sma", "SMAIndicator", "seriesTypes", "correctFloat", "defined", "extend", "isArray", "merge", "DisparityIndexIndicator", "init", "args", "arguments", "params", "averageType", "average", "ctx", "averageIndicator", "apply", "calculateDisparityIndex", "curPrice", "periodAverage", "getV<PERSON>ues", "series", "index", "xVal", "xData", "yVal", "yData", "yValLen", "length", "disparityIndexPoint", "isOHLC", "values", "yV<PERSON><PERSON>", "start", "indexOf", "i", "disparityIndexValue", "push", "defaultOptions", "marker", "enabled", "dataGrouping", "approximation", "nameBase", "nameComponents", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,wCAAyC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACjI,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,wCAAwC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAErHA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAcjL,GAAM,CAAEE,IAAKC,CAAY,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAE/K,CAAEC,aAAAA,CAAY,CAAEC,QAAAA,CAAO,CAAEC,OAAAA,CAAM,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAE,CAAIV,GAe3D,OAAMW,UAAgCP,EAMlCQ,MAAO,CACH,IAAMC,EAAOC,UACbC,EAASF,CAAI,CAAC,EAAE,CAACE,MAAM,CACvBC,EAAcD,GAAUA,EAAOE,OAAO,CAAGF,EAAOE,OAAO,CAAG,KAAK,CAC/DC,CAH8B,IAAI,CAG9BC,gBAAgB,CAAG,AAACjB,IAA2IG,WAAW,CAACW,EAAY,EAAIZ,EAC/Lc,AAJ8B,IAAI,CAI9BC,gBAAgB,CAACzB,SAAS,CAACkB,IAAI,CAACQ,KAAK,CAJX,IAAI,CAIaP,EACnD,CACAQ,wBAAwBC,CAAQ,CAAEC,CAAa,CAAE,CAC7C,OAAOjB,EAAagB,EAAWC,GAAiBA,EAAgB,GACpE,CACAC,UAAUC,CAAM,CAAEV,CAAM,CAAE,CACtB,IAAMW,EAAQX,EAAOW,KAAK,CAAEC,EAAOF,EAAOG,KAAK,CAAEC,EAAOJ,EAAOK,KAAK,CAAEC,EAAUF,EAAOA,EAAKG,MAAM,CAAG,EAAGC,EAAsB,EAAE,CAAEL,EAAQ,EAAE,CAAEE,EAAQ,EAAE,CAExJX,EAAmB,IAAI,CAACA,gBAAgB,CAAEe,EAASzB,EAAQoB,CAAI,CAAC,EAAE,EAElEM,EAAShB,EAAiBzB,SAAS,CAAC8B,SAAS,CAACC,EAAQV,GAASqB,EAAUD,EAAOL,KAAK,CAAEO,EAAQV,EAAKW,OAAO,CAACH,EAAOP,KAAK,CAAC,EAAE,EAE3H,GAAI,AAACQ,GAAWA,AAAmB,IAAnBA,EAAQJ,MAAM,EACzBzB,EAAQmB,KACTG,CAAAA,EAAKG,MAAM,EAAIK,CAAI,GAIvB,IAAK,IAAIE,EAAIF,EAAOE,EAAIR,EAASQ,IAAK,CAClC,IAAMC,EAAsB,IAAI,CAACnB,uBAAuB,CAACa,EAASL,CAAI,CAACU,EAAE,CAACb,EAAM,CAAGG,CAAI,CAACU,EAAE,CAAEH,CAAO,CAACG,EAAIF,EAAM,EAC9GJ,EAAoBQ,IAAI,CAAC,CACrBd,CAAI,CAACY,EAAE,CACPC,EACH,EACDZ,EAAMa,IAAI,CAACd,CAAI,CAACY,EAAE,EAClBT,EAAMW,IAAI,CAACD,EACf,CACA,MAAO,CACHL,OAAQF,EACRL,MAAOA,EACPE,MAAOA,CACX,EACJ,CACJ,CAwBAnB,EAAwB+B,cAAc,CAAGhC,EAAMN,EAAasC,cAAc,CAAE,CACxE3B,OAAQ,CAUJE,QAAS,MACTS,MAAO,CACX,EACAiB,OAAQ,CACJC,QAAS,CAAA,CACb,EACAC,aAAc,CACVC,cAAe,UACnB,CACJ,GACAtC,EAAOG,EAAwBjB,SAAS,CAAE,CACtCqD,SAAU,kBACVC,eAAgB,CAAC,SAAU,UAAU,AACzC,GACA9C,IAA0I+C,kBAAkB,CAAC,iBAAkBtC,GAkClJ,IAAMb,EAAwBE,IAGjD,OADYH,EAAoB,OAAU,AAE3C,CAAA"}