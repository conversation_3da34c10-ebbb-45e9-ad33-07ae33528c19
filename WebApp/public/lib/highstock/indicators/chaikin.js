!/**
 * Highstock JS v12.3.0 (2025-06-21)
 * @module highcharts/indicators/chaikin
 * @requires highcharts
 * @requires highcharts/modules/stock
 *
 * Indicator series type for Highcharts Stock
 *
 * (c) 2010-2025 Wojciech Chmiel
 *
 * License: www.highcharts.com/license
 */function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(e._Highcharts,e._Highcharts.SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/indicators/chaikin",["highcharts/highcharts"],function(e){return t(e,e.SeriesRegistry)}):"object"==typeof exports?exports["highcharts/indicators/chaikin"]=t(e._Highcharts,e._Highcharts.SeriesRegistry):e.Highcharts=t(e.Highcharts,e.Highcharts.SeriesRegistry)}("undefined"==typeof window?this:window,(e,t)=>(()=>{"use strict";var r={512:e=>{e.exports=t},944:t=>{t.exports=e}},i={};function s(e){var t=i[e];if(void 0!==t)return t.exports;var a=i[e]={exports:{}};return r[e](a,a.exports,s),a.exports}s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},s.d=(e,t)=>{for(var r in t)s.o(t,r)&&!s.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var a={};s.d(a,{default:()=>S});var o=s(944),n=s.n(o),u=s(512),p=s.n(u);let{sma:l}=p().seriesTypes,{error:h,extend:d,merge:c}=n();class g extends l{static populateAverage(e,t,r,i,s){let a=t[i][1],o=t[i][2],n=t[i][3],u=r[i],p=n===a&&n===o||a===o?0:(2*n-o-a)/(a-o)*u;return[e[i],p]}getValues(e,t){let r,i,s,a=t.period,o=e.xData,n=e.yData,u=t.volumeSeriesID,p=e.chart.get(u),l=p?.getColumn("y"),d=n?n.length:0,c=[],f=[],y=[];if(!(o.length<=a)||!d||4===n[0].length){if(!p)return void h("Series "+u+" not found! Check `volumeSeriesID`.",!0,e.chart);for(i=a;i<d;i++)r=c.length,s=g.populateAverage(o,n,l,i,a),r>0&&(s[1]+=c[r-1][1]),c.push(s),f.push(s[0]),y.push(s[1]);return{values:c,xData:f,yData:y}}}}g.defaultOptions=c(l.defaultOptions,{params:{index:void 0,volumeSeriesID:"volume"}}),d(g.prototype,{nameComponents:!1,nameBase:"Accumulation/Distribution"}),p().registerSeriesType("ad",g);let{ema:f}=p().seriesTypes,{correctFloat:y,extend:m,merge:v,error:x}=n();class D extends f{getValues(e,t){let r,i,s=t.periods,a=t.period,o=[],n=[],u=[];if(2!==s.length||s[1]<=s[0])return void x('Error: "Chaikin requires two periods. Notice, first period should be lower than the second one."');let p=g.prototype.getValues.call(this,e,{volumeSeriesID:t.volumeSeriesID,period:a});if(!p)return;let l=super.getValues.call(this,p,{period:s[0]}),h=super.getValues.call(this,p,{period:s[1]});if(!l||!h)return;let d=s[1]-s[0];for(i=0;i<h.yData.length;i++)r=y(l.yData[i+d]-h.yData[i]),o.push([h.xData[i],r]),n.push(h.xData[i]),u.push(r);return{values:o,xData:n,yData:u}}}D.defaultOptions=v(f.defaultOptions,{params:{index:void 0,volumeSeriesID:"volume",period:9,periods:[3,10]}}),m(D.prototype,{nameBase:"Chaikin Osc",nameComponents:["periods"]}),p().registerSeriesType("chaikin",D);let S=n();return a.default})());