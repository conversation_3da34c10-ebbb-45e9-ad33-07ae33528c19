{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/psar\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Parabolic SAR Indicator for Highcharts Stock\n *\n * (c) 2010-2025 <PERSON><PERSON><PERSON><PERSON>la<PERSON>ński\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/psar\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/psar\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ psar_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/PSAR/PSARIndicator.js\n/* *\n *\n *  Parabolic SAR indicator for Highcharts Stock\n *\n *  (c) 2010-2025 Grzegorz Blachliński\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n// Utils:\n/**\n *\n */\nfunction toFixed(a, n) {\n    return parseFloat(a.toFixed(n));\n}\n/**\n *\n */\nfunction calculateDirection(previousDirection, low, high, PSAR) {\n    if ((previousDirection === 1 && low > PSAR) ||\n        (previousDirection === -1 && high > PSAR)) {\n        return 1;\n    }\n    return -1;\n}\n/* *\n * Method for calculating acceleration factor\n * dir - direction\n * pDir - previous Direction\n * eP - extreme point\n * pEP - previous extreme point\n * inc - increment for acceleration factor\n * maxAcc - maximum acceleration factor\n * initAcc - initial acceleration factor\n */\n/**\n *\n */\nfunction getAccelerationFactor(dir, pDir, eP, pEP, pAcc, inc, maxAcc, initAcc) {\n    if (dir === pDir) {\n        if (dir === 1 && (eP > pEP)) {\n            return (pAcc === maxAcc) ? maxAcc : toFixed(pAcc + inc, 2);\n        }\n        if (dir === -1 && (eP < pEP)) {\n            return (pAcc === maxAcc) ? maxAcc : toFixed(pAcc + inc, 2);\n        }\n        return pAcc;\n    }\n    return initAcc;\n}\n/**\n *\n */\nfunction getExtremePoint(high, low, previousDirection, previousExtremePoint) {\n    if (previousDirection === 1) {\n        return (high > previousExtremePoint) ? high : previousExtremePoint;\n    }\n    return (low < previousExtremePoint) ? low : previousExtremePoint;\n}\n/**\n *\n */\nfunction getEPMinusPSAR(EP, PSAR) {\n    return EP - PSAR;\n}\n/**\n *\n */\nfunction getAccelerationFactorMultiply(accelerationFactor, EPMinusSAR) {\n    return accelerationFactor * EPMinusSAR;\n}\n/* *\n * Method for calculating PSAR\n * pdir - previous direction\n * sDir - second previous Direction\n * PSAR - previous PSAR\n * pACCMultiply - previous acceleration factor multiply\n * sLow - second previous low\n * pLow - previous low\n * sHigh - second previous high\n * pHigh - previous high\n * pEP - previous extreme point\n */\n/**\n *\n */\nfunction getPSAR(pdir, sDir, PSAR, pACCMulti, sLow, pLow, pHigh, sHigh, pEP) {\n    if (pdir === sDir) {\n        if (pdir === 1) {\n            return (PSAR + pACCMulti < Math.min(sLow, pLow)) ?\n                PSAR + pACCMulti :\n                Math.min(sLow, pLow);\n        }\n        return (PSAR + pACCMulti > Math.max(sHigh, pHigh)) ?\n            PSAR + pACCMulti :\n            Math.max(sHigh, pHigh);\n    }\n    return pEP;\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Parabolic SAR series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.psar\n *\n * @augments Highcharts.Series\n */\nclass PSARIndicator extends SMAIndicator {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        this.nameComponents = void 0;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const xVal = series.xData, yVal = series.yData, maxAccelerationFactor = params.maxAccelerationFactor, increment = params.increment, \n        // Set initial acc factor (for every new trend!)\n        initialAccelerationFactor = params.initialAccelerationFactor, decimals = params.decimals, index = params.index, PSARArr = [], xData = [], yData = [];\n        let accelerationFactor = params.initialAccelerationFactor, direction, \n        // Extreme point is the lowest low for falling and highest high\n        // for rising psar - and we are starting with falling\n        extremePoint = yVal[0][1], EPMinusPSAR, accelerationFactorMultiply, newDirection, previousDirection = 1, prevLow, prevPrevLow, prevHigh, prevPrevHigh, PSAR = yVal[0][2], newExtremePoint, high, low, ind;\n        if (index >= yVal.length) {\n            return;\n        }\n        for (ind = 0; ind < index; ind++) {\n            extremePoint = Math.max(yVal[ind][1], extremePoint);\n            PSAR = Math.min(yVal[ind][2], toFixed(PSAR, decimals));\n        }\n        direction = (yVal[ind][1] > PSAR) ? 1 : -1;\n        EPMinusPSAR = getEPMinusPSAR(extremePoint, PSAR);\n        accelerationFactor = params.initialAccelerationFactor;\n        accelerationFactorMultiply = getAccelerationFactorMultiply(accelerationFactor, EPMinusPSAR);\n        PSARArr.push([xVal[index], PSAR]);\n        xData.push(xVal[index]);\n        yData.push(toFixed(PSAR, decimals));\n        for (ind = index + 1; ind < yVal.length; ind++) {\n            prevLow = yVal[ind - 1][2];\n            prevPrevLow = yVal[ind - 2][2];\n            prevHigh = yVal[ind - 1][1];\n            prevPrevHigh = yVal[ind - 2][1];\n            high = yVal[ind][1];\n            low = yVal[ind][2];\n            // Null points break PSAR\n            if (prevPrevLow !== null &&\n                prevPrevHigh !== null &&\n                prevLow !== null &&\n                prevHigh !== null &&\n                high !== null &&\n                low !== null) {\n                PSAR = getPSAR(direction, previousDirection, PSAR, accelerationFactorMultiply, prevPrevLow, prevLow, prevHigh, prevPrevHigh, extremePoint);\n                newExtremePoint = getExtremePoint(high, low, direction, extremePoint);\n                newDirection = calculateDirection(previousDirection, low, high, PSAR);\n                accelerationFactor = getAccelerationFactor(newDirection, direction, newExtremePoint, extremePoint, accelerationFactor, increment, maxAccelerationFactor, initialAccelerationFactor);\n                EPMinusPSAR = getEPMinusPSAR(newExtremePoint, PSAR);\n                accelerationFactorMultiply = getAccelerationFactorMultiply(accelerationFactor, EPMinusPSAR);\n                PSARArr.push([xVal[ind], toFixed(PSAR, decimals)]);\n                xData.push(xVal[ind]);\n                yData.push(toFixed(PSAR, decimals));\n                previousDirection = direction;\n                direction = newDirection;\n                extremePoint = newExtremePoint;\n            }\n        }\n        return {\n            values: PSARArr,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/**\n * Parabolic SAR. This series requires `linkedTo`\n * option to be set and should be loaded\n * after `stock/indicators/indicators.js` file.\n *\n * @sample stock/indicators/psar\n *         Parabolic SAR Indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/psar\n * @optionparent plotOptions.psar\n */\nPSARIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    lineWidth: 0,\n    marker: {\n        enabled: true\n    },\n    states: {\n        hover: {\n            lineWidthPlus: 0\n        }\n    },\n    /**\n     * @excluding period\n     */\n    params: {\n        period: void 0, // Unchangeable period, do not inherit (#15362)\n        /**\n         * The initial value for acceleration factor.\n         * Acceleration factor is starting with this value\n         * and increases by specified increment each time\n         * the extreme point makes a new high.\n         * AF can reach a maximum of maxAccelerationFactor,\n         * no matter how long the uptrend extends.\n         */\n        initialAccelerationFactor: 0.02,\n        /**\n         * The Maximum value for acceleration factor.\n         * AF can reach a maximum of maxAccelerationFactor,\n         * no matter how long the uptrend extends.\n         */\n        maxAccelerationFactor: 0.2,\n        /**\n         * Acceleration factor increases by increment each time\n         * the extreme point makes a new high.\n         *\n         * @since 6.0.0\n         */\n        increment: 0.02,\n        /**\n         * Index from which PSAR is starting calculation\n         *\n         * @since 6.0.0\n         */\n        index: 2,\n        /**\n         * Number of maximum decimals that are used in PSAR calculations.\n         *\n         * @since 6.0.0\n         */\n        decimals: 4\n    }\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('psar', PSARIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const PSAR_PSARIndicator = ((/* unused pure expression or super */ null && (PSARIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `PSAR` series. If the [type](#series.psar.type) option is not specified, it\n * is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.psar\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/psar\n * @apioption series.psar\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/masters/indicators/psar.js\n\n\n\n\n/* harmony default export */ const psar_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "psar_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "sma", "SMAIndicator", "seriesTypes", "merge", "toFixed", "parseFloat", "PSARIndicator", "constructor", "arguments", "nameComponents", "getV<PERSON>ues", "series", "params", "xVal", "xData", "yVal", "yData", "maxAccelerationFactor", "increment", "initialAccelerationFactor", "decimals", "index", "PSARArr", "accelerationFactor", "direction", "extremePoint", "EPMinusPSAR", "accelerationFactorMultiply", "newDirection", "previousDirection", "prevLow", "prevPrevLow", "prevHigh", "prevPrevHigh", "PSAR", "newExtremePoint", "high", "low", "ind", "length", "Math", "max", "min", "EP", "push", "previousExtremePoint", "pdir", "sDir", "pACCMulti", "sLow", "pLow", "pHigh", "sHigh", "pEP", "dir", "pDir", "eP", "pAcc", "inc", "maxAcc", "initAcc", "values", "defaultOptions", "lineWidth", "marker", "enabled", "states", "hover", "lineWidthPlus", "period", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,6BAA8B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACtH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,6BAA6B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAE1GA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAejL,GAAM,CAAEE,IAAKC,CAAY,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAE/K,CAAEC,MAAAA,CAAK,CAAE,CAAIN,IAUnB,SAASO,EAAQvB,CAAC,CAAEJ,CAAC,EACjB,OAAO4B,WAAWxB,EAAEuB,OAAO,CAAC3B,GAChC,CAmGA,MAAM6B,UAAsBL,EACxBM,aAAc,CAMV,KAAK,IAAIC,WACT,IAAI,CAACC,cAAc,CAAG,KAAK,CAC/B,CAMAC,UAAUC,CAAM,CAAEC,CAAM,CAAE,CACtB,IAAMC,EAAOF,EAAOG,KAAK,CAAEC,EAAOJ,EAAOK,KAAK,CAAEC,EAAwBL,EAAOK,qBAAqB,CAAEC,EAAYN,EAAOM,SAAS,CAElIC,EAA4BP,EAAOO,yBAAyB,CAAEC,EAAWR,EAAOQ,QAAQ,CAAEC,EAAQT,EAAOS,KAAK,CAAEC,EAAU,EAAE,CAAER,EAAQ,EAAE,CAAEE,EAAQ,EAAE,CAChJO,EAAqBX,EAAOO,yBAAyB,CAAEK,EAG3DC,EAAeV,CAAI,CAAC,EAAE,CAAC,EAAE,CAAEW,EAAaC,EAA4BC,EAAcC,EAAoB,EAAGC,EAASC,EAAaC,EAAUC,EAAcC,EAAOnB,CAAI,CAAC,EAAE,CAAC,EAAE,CAAEoB,EAAiBC,EAAMC,EAAKC,EACtM,IAAIjB,CAAAA,GAASN,EAAKwB,MAAM,AAAD,GAGvB,IAAKD,EAAM,EAAGA,EAAMjB,EAAOiB,IACvBb,EAAee,KAAKC,GAAG,CAAC1B,CAAI,CAACuB,EAAI,CAAC,EAAE,CAAEb,GACtCS,EAAOM,KAAKE,GAAG,CAAC3B,CAAI,CAACuB,EAAI,CAAC,EAAE,CAAElC,EAAQ8B,EAAMd,IAShD,IAPAI,EAAY,AAACT,CAAI,CAACuB,EAAI,CAAC,EAAE,CAAGJ,EAAQ,EAAI,GACxCR,EAjFGiB,AAiF0BlB,EAAcS,EAE3CP,EA7EGJ,AA4EHA,CAAAA,EAAqBX,EAAOO,yBAAyB,AAAD,EAC2BO,EAC/EJ,EAAQsB,IAAI,CAAC,CAAC/B,CAAI,CAACQ,EAAM,CAAEa,EAAK,EAChCpB,EAAM8B,IAAI,CAAC/B,CAAI,CAACQ,EAAM,EACtBL,EAAM4B,IAAI,CAACxC,EAAQ8B,EAAMd,IACpBkB,EAAMjB,EAAQ,EAAGiB,EAAMvB,EAAKwB,MAAM,CAAED,IAQrC,GAPAR,EAAUf,CAAI,CAACuB,EAAM,EAAE,CAAC,EAAE,CAC1BP,EAAchB,CAAI,CAACuB,EAAM,EAAE,CAAC,EAAE,CAC9BN,EAAWjB,CAAI,CAACuB,EAAM,EAAE,CAAC,EAAE,CAC3BL,EAAelB,CAAI,CAACuB,EAAM,EAAE,CAAC,EAAE,CAC/BF,EAAOrB,CAAI,CAACuB,EAAI,CAAC,EAAE,CACnBD,EAAMtB,CAAI,CAACuB,EAAI,CAAC,EAAE,CAEdP,AAAgB,OAAhBA,GACAE,AAAiB,OAAjBA,GACAH,AAAY,OAAZA,GACAE,AAAa,OAAbA,GACAI,AAAS,OAATA,GACAC,AAAQ,OAARA,EAAc,KA9GMR,EAAmBgB,EAnC3BhB,EAA8BK,EAoEzCY,EAAMC,EAAMb,EAAMc,EAAWC,EAAMC,EAAMC,EAAOC,EAAOC,EAhDzCC,EAAKC,EAAMC,EAAIH,EAAKI,EAAMC,EAAKC,EAAQC,EAgDrDd,EA8EctB,EA9ERuB,EA8EmBlB,EA9EbK,EA8EgCA,EA9E1Bc,EA8EgCrB,EA9ErBsB,EA8EiDlB,EA9E3CmB,EA8EwDpB,EA9ElDqB,EA8E2DnB,EA9EpDoB,EA8E8DnB,EA9EvDoB,EA8EqE5B,EAA7HS,EA7EZ,AAAIY,IAASC,EACT,AAAID,AAAS,IAATA,EACO,AAACZ,EAAOc,EAAYR,KAAKE,GAAG,CAACO,EAAMC,GACtChB,EAAOc,EACPR,KAAKE,GAAG,CAACO,EAAMC,GAEhB,AAAChB,EAAOc,EAAYR,KAAKC,GAAG,CAACW,EAAOD,GACvCjB,EAAOc,EACPR,KAAKC,GAAG,CAACW,EAAOD,GAEjBE,EA5CyBxB,EAgHyBL,EAhHNqB,EAgHiBpB,EAAxDU,EA/GZ,AAAIN,AAAsB,IAAtBA,EACO,AAACO,AA8GkCA,EA9G3BS,EA8G2BT,EA9GIS,EAE3C,AAACR,AA4G4CA,EA5GtCQ,EA4GsCR,EA5GRQ,EAvCpBhB,EAoJsBA,EApJQK,EAoJsBA,EAhIjDoB,EAgIf1B,EAnJZ,AAAI,AAAuB,IAAtBC,GAA2BQ,AAmJiCA,EAnJ3BH,GACjCL,AAAsB,KAAtBA,GAA4BO,AAkJqCA,EAlJ9BF,EAC7B,EAEJ,GAeyBqB,EAiIqC/B,EAjI/BgC,EAiI0CrB,EAjItCkB,EAiIuD5B,EAjIlDgC,EAiIgElC,EAjI1DmC,EAiI8ExC,EAjIzEyC,EAiIoF1C,EAjI5E2C,EAiImGzC,EAEzJQ,EApGLJ,AAkGKA,CAAAA,EAhIZ,AAAI+B,IAAQC,EACR,AAAY,IAARD,GAAcE,EAAKH,GAGnBC,AAAQ,KAARA,GAAeE,EAAKH,EAFb,AAACI,IAASE,EAAUA,EAASvD,EAAQqD,EAAOC,EAAK,GAKrDD,EAEJG,CAuHuL,EAClLlC,CAAAA,EAzGLiB,AAyGkCR,EAAiBD,CAAI,EAElDZ,EAAQsB,IAAI,CAAC,CAAC/B,CAAI,CAACyB,EAAI,CAAElC,EAAQ8B,EAAMd,GAAU,EACjDN,EAAM8B,IAAI,CAAC/B,CAAI,CAACyB,EAAI,EACpBtB,EAAM4B,IAAI,CAACxC,EAAQ8B,EAAMd,IACzBS,EAAoBL,EACpBA,EAAYI,EACZH,EAAeU,CACnB,CAEJ,MAAO,CACH0B,OAAQvC,EACRR,MAAOA,EACPE,MAAOA,CACX,EACJ,CACJ,CAgBAV,EAAcwD,cAAc,CAAG3D,EAAMF,EAAa6D,cAAc,CAAE,CAC9DC,UAAW,EACXC,OAAQ,CACJC,QAAS,CAAA,CACb,EACAC,OAAQ,CACJC,MAAO,CACHC,cAAe,CACnB,CACJ,EAIAxD,OAAQ,CACJyD,OAAQ,KAAK,EASblD,0BAA2B,IAM3BF,sBAAuB,GAOvBC,UAAW,IAMXG,MAAO,EAMPD,SAAU,CACd,CACJ,GACArB,IAA0IuE,kBAAkB,CAAC,OAAQhE,GA+BxI,IAAMX,EAAaE,IAGtC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}