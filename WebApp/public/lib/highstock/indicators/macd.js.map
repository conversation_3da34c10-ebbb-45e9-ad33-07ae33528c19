{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/macd\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Sebastian <PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/macd\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/macd\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ macd_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/MACD/MACDIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { noop } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { column: ColumnSeries, sma: SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { extend, correctFloat, defined, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The MACD series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.macd\n *\n * @augments Highcharts.Series\n */\nclass MACDIndicator extends SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    init() {\n        highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().seriesTypes.sma.prototype.init.apply(this, arguments);\n        const originalColor = this.color;\n        // Check whether series is initialized. It may be not initialized,\n        // when any of required indicators is missing.\n        if (this.options) {\n            // If the default colour doesn't set, get the next available from\n            // the array and apply it #15608.\n            if (defined(this.colorIndex)) {\n                if (this.options.signalLine &&\n                    this.options.signalLine.styles &&\n                    !this.options.signalLine.styles.lineColor) {\n                    this.options.colorIndex = this.colorIndex + 1;\n                    this.getCyclic('color', void 0, this.chart.options.colors);\n                    this.options.signalLine.styles.lineColor =\n                        this.color;\n                }\n                if (this.options.macdLine &&\n                    this.options.macdLine.styles &&\n                    !this.options.macdLine.styles.lineColor) {\n                    this.options.colorIndex = this.colorIndex + 1;\n                    this.getCyclic('color', void 0, this.chart.options.colors);\n                    this.options.macdLine.styles.lineColor =\n                        this.color;\n                }\n            }\n            // Zones have indexes automatically calculated, we need to\n            // translate them to support multiple lines within one indicator\n            this.macdZones = {\n                zones: this.options.macdLine.zones,\n                startIndex: 0\n            };\n            this.signalZones = {\n                zones: this.macdZones.zones.concat(this.options.signalLine.zones),\n                startIndex: this.macdZones.zones.length\n            };\n        }\n        // Reset color and index #15608.\n        this.color = originalColor;\n    }\n    toYData(point) {\n        return [point.y, point.signal, point.MACD];\n    }\n    translate() {\n        const indicator = this, plotNames = ['plotSignal', 'plotMACD'];\n        highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().seriesTypes.column.prototype.translate.apply(indicator);\n        indicator.points.forEach(function (point) {\n            [point.signal, point.MACD].forEach(function (value, i) {\n                if (value !== null) {\n                    point[plotNames[i]] =\n                        indicator.yAxis.toPixels(value, true);\n                }\n            });\n        });\n    }\n    destroy() {\n        // This.graph is null due to removing two times the same SVG element\n        this.graph = null;\n        this.graphmacd = this.graphmacd && this.graphmacd.destroy();\n        this.graphsignal = this.graphsignal && this.graphsignal.destroy();\n        highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().seriesTypes.sma.prototype.destroy.apply(this, arguments);\n    }\n    drawGraph() {\n        const indicator = this, mainLinePoints = indicator.points, mainLineOptions = indicator.options, histogramZones = indicator.zones, gappedExtend = {\n            options: {\n                gapSize: mainLineOptions.gapSize\n            }\n        }, otherSignals = [[], []];\n        let point, pointsLength = mainLinePoints.length;\n        // Generate points for top and bottom lines:\n        while (pointsLength--) {\n            point = mainLinePoints[pointsLength];\n            if (defined(point.plotMACD)) {\n                otherSignals[0].push({\n                    plotX: point.plotX,\n                    plotY: point.plotMACD,\n                    isNull: !defined(point.plotMACD)\n                });\n            }\n            if (defined(point.plotSignal)) {\n                otherSignals[1].push({\n                    plotX: point.plotX,\n                    plotY: point.plotSignal,\n                    isNull: !defined(point.plotMACD)\n                });\n            }\n        }\n        // Modify options and generate smoothing line:\n        ['macd', 'signal'].forEach((lineName, i) => {\n            indicator.points = otherSignals[i];\n            indicator.options = merge(mainLineOptions[`${lineName}Line`]?.styles || {}, gappedExtend);\n            indicator.graph = indicator[`graph${lineName}`];\n            // Zones extension:\n            indicator.zones = (indicator[`${lineName}Zones`].zones || []).slice(indicator[`${lineName}Zones`].startIndex || 0);\n            highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().seriesTypes.sma.prototype.drawGraph.call(indicator);\n            indicator[`graph${lineName}`] = indicator.graph;\n        });\n        // Restore options:\n        indicator.points = mainLinePoints;\n        indicator.options = mainLineOptions;\n        indicator.zones = histogramZones;\n    }\n    applyZones() {\n        // Histogram zones are handled by drawPoints method\n        // Here we need to apply zones for all lines\n        const histogramZones = this.zones;\n        // `signalZones.zones` contains all zones:\n        this.zones = this.signalZones.zones;\n        highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().seriesTypes.sma.prototype.applyZones.call(this);\n        // `applyZones` hides only main series.graph, hide macd line manually\n        if (this.graphmacd && this.options.macdLine.zones.length) {\n            this.graphmacd.hide();\n        }\n        this.zones = histogramZones;\n    }\n    getValues(series, params) {\n        const indexToShift = (params.longPeriod - params.shortPeriod), // #14197\n        MACD = [], xMACD = [], yMACD = [];\n        let shortEMA, longEMA, i, j = 0, signalLine = [];\n        if (series.xData.length <\n            params.longPeriod + params.signalPeriod) {\n            return;\n        }\n        // Calculating the short and long EMA used when calculating the MACD\n        shortEMA = highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().seriesTypes.ema.prototype.getValues(series, {\n            period: params.shortPeriod,\n            index: params.index\n        });\n        longEMA = highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().seriesTypes.ema.prototype.getValues(series, {\n            period: params.longPeriod,\n            index: params.index\n        });\n        shortEMA = shortEMA.values;\n        longEMA = longEMA.values;\n        // Subtract each Y value from the EMA's and create the new dataset\n        // (MACD)\n        for (i = 0; i <= shortEMA.length; i++) {\n            if (defined(longEMA[i]) &&\n                defined(longEMA[i][1]) &&\n                defined(shortEMA[i + indexToShift]) &&\n                defined(shortEMA[i + indexToShift][0])) {\n                MACD.push([\n                    shortEMA[i + indexToShift][0],\n                    0,\n                    null,\n                    shortEMA[i + indexToShift][1] -\n                        longEMA[i][1]\n                ]);\n            }\n        }\n        // Set the Y and X data of the MACD. This is used in calculating the\n        // signal line.\n        for (i = 0; i < MACD.length; i++) {\n            xMACD.push(MACD[i][0]);\n            yMACD.push([0, null, MACD[i][3]]);\n        }\n        // Setting the signalline (Signal Line: X-day EMA of MACD line).\n        signalLine = highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().seriesTypes.ema.prototype.getValues({\n            xData: xMACD,\n            yData: yMACD\n        }, {\n            period: params.signalPeriod,\n            index: 2\n        });\n        signalLine = signalLine.values;\n        // Setting the MACD Histogram. In comparison to the loop with pure\n        // MACD this loop uses MACD x value not xData.\n        for (i = 0; i < MACD.length; i++) {\n            // Detect the first point\n            if (MACD[i][0] >= signalLine[0][0]) {\n                MACD[i][2] = signalLine[j][1];\n                yMACD[i] = [0, signalLine[j][1], MACD[i][3]];\n                if (MACD[i][3] === null) {\n                    MACD[i][1] = 0;\n                    yMACD[i][0] = 0;\n                }\n                else {\n                    MACD[i][1] = correctFloat(MACD[i][3] -\n                        signalLine[j][1]);\n                    yMACD[i][0] = correctFloat(MACD[i][3] -\n                        signalLine[j][1]);\n                }\n                j++;\n            }\n        }\n        return {\n            values: MACD,\n            xData: xMACD,\n            yData: yMACD\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Moving Average Convergence Divergence (MACD). This series requires\n * `linkedTo` option to be set and should be loaded after the\n * `stock/indicators/indicators.js`.\n *\n * @sample stock/indicators/macd\n *         MACD indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/macd\n * @optionparent plotOptions.macd\n */\nMACDIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    params: {\n        /**\n         * The short period for indicator calculations.\n         */\n        shortPeriod: 12,\n        /**\n         * The long period for indicator calculations.\n         */\n        longPeriod: 26,\n        /**\n         * The base period for signal calculations.\n         */\n        signalPeriod: 9,\n        period: 26\n    },\n    /**\n     * The styles for signal line\n     */\n    signalLine: {\n        /**\n         * @sample stock/indicators/macd-zones\n         *         Zones in MACD\n         *\n         * @extends plotOptions.macd.zones\n         */\n        zones: [],\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line.\n             *\n             * @type  {Highcharts.ColorString}\n             */\n            lineColor: void 0\n        }\n    },\n    /**\n     * The styles for macd line\n     */\n    macdLine: {\n        /**\n         * @sample stock/indicators/macd-zones\n         *         Zones in MACD\n         *\n         * @extends plotOptions.macd.zones\n         */\n        zones: [],\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line.\n             *\n             * @type  {Highcharts.ColorString}\n             */\n            lineColor: void 0\n        }\n    },\n    /**\n     * @type {number|null}\n     */\n    threshold: 0,\n    groupPadding: 0.1,\n    pointPadding: 0.1,\n    crisp: false,\n    states: {\n        hover: {\n            halo: {\n                size: 0\n            }\n        }\n    },\n    tooltip: {\n        pointFormat: '<span style=\"color:{point.color}\">\\u25CF</span> <b> {series.name}</b><br/>' +\n            'Value: {point.MACD}<br/>' +\n            'Signal: {point.signal}<br/>' +\n            'Histogram: {point.y}<br/>'\n    },\n    dataGrouping: {\n        approximation: 'averages'\n    },\n    minPointLength: 0\n});\nextend(MACDIndicator.prototype, {\n    nameComponents: ['longPeriod', 'shortPeriod', 'signalPeriod'],\n    // \"y\" value is treated as Histogram data\n    pointArrayMap: ['y', 'signal', 'MACD'],\n    parallelArrays: ['x', 'y', 'signal', 'MACD'],\n    pointValKey: 'y',\n    // Columns support:\n    markerAttribs: noop,\n    getColumnMetrics: (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).seriesTypes.column.prototype.getColumnMetrics,\n    crispCol: (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).seriesTypes.column.prototype.crispCol,\n    drawPoints: (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).seriesTypes.column.prototype.drawPoints\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('macd', MACDIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const MACD_MACDIndicator = ((/* unused pure expression or super */ null && (MACDIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `MACD` series. If the [type](#series.macd.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.macd\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/macd\n * @apioption series.macd\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/masters/indicators/macd.js\n\n\n\n\n/* harmony default export */ const macd_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "macd_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "noop", "column", "ColumnSeries", "sma", "SMAIndicator", "seriesTypes", "extend", "correctFloat", "defined", "merge", "MACDIndicator", "init", "apply", "arguments", "originalColor", "color", "options", "colorIndex", "signalLine", "styles", "lineColor", "getCyclic", "chart", "colors", "macdLine", "macdZones", "zones", "startIndex", "signalZones", "concat", "length", "toYData", "point", "y", "signal", "MACD", "translate", "indicator", "plotNames", "points", "for<PERSON>ach", "value", "i", "yAxis", "toPixels", "destroy", "graph", "graphmacd", "graphsignal", "drawGraph", "mainLinePoints", "mainLineOptions", "histogramZones", "gappedExtend", "gapSize", "otherSignals", "pointsLength", "plotMACD", "push", "plotX", "plotY", "isNull", "plotSignal", "lineName", "slice", "applyZones", "hide", "getV<PERSON>ues", "series", "params", "indexToShift", "<PERSON>P<PERSON><PERSON>", "shortPeriod", "xMACD", "yMACD", "shortEMA", "longEMA", "j", "xData", "signalPeriod", "ema", "period", "index", "values", "yData", "defaultOptions", "lineWidth", "threshold", "groupPadding", "pointPadding", "crisp", "states", "hover", "halo", "size", "tooltip", "pointFormat", "dataGrouping", "approximation", "minP<PERSON><PERSON><PERSON>th", "nameComponents", "pointArrayMap", "parallelArrays", "pointVal<PERSON>ey", "markerAttribs", "getColumnMetrics", "crispCol", "drawPoints", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,6BAA8B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACtH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,6BAA6B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAE1GA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAWjL,GAAM,CAAEE,KAAAA,CAAI,CAAE,CAAIH,IAEZ,CAAEI,OAAQC,CAAY,CAAEC,IAAKC,CAAY,CAAE,CAAG,AAACL,IAA2IM,WAAW,CAErM,CAAEC,OAAAA,CAAM,CAAEC,aAAAA,CAAY,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAE,CAAIZ,GAelD,OAAMa,UAAsBN,EAMxBO,MAAO,CACHZ,IAA0IM,WAAW,CAACF,GAAG,CAACZ,SAAS,CAACoB,IAAI,CAACC,KAAK,CAAC,IAAI,CAAEC,WACrL,IAAMC,EAAgB,IAAI,CAACC,KAAK,AAG5B,CAAA,IAAI,CAACC,OAAO,GAGRR,EAAQ,IAAI,CAACS,UAAU,IACnB,IAAI,CAACD,OAAO,CAACE,UAAU,EACvB,IAAI,CAACF,OAAO,CAACE,UAAU,CAACC,MAAM,EAC9B,CAAC,IAAI,CAACH,OAAO,CAACE,UAAU,CAACC,MAAM,CAACC,SAAS,GACzC,IAAI,CAACJ,OAAO,CAACC,UAAU,CAAG,IAAI,CAACA,UAAU,CAAG,EAC5C,IAAI,CAACI,SAAS,CAAC,QAAS,KAAK,EAAG,IAAI,CAACC,KAAK,CAACN,OAAO,CAACO,MAAM,EACzD,IAAI,CAACP,OAAO,CAACE,UAAU,CAACC,MAAM,CAACC,SAAS,CACpC,IAAI,CAACL,KAAK,EAEd,IAAI,CAACC,OAAO,CAACQ,QAAQ,EACrB,IAAI,CAACR,OAAO,CAACQ,QAAQ,CAACL,MAAM,EAC5B,CAAC,IAAI,CAACH,OAAO,CAACQ,QAAQ,CAACL,MAAM,CAACC,SAAS,GACvC,IAAI,CAACJ,OAAO,CAACC,UAAU,CAAG,IAAI,CAACA,UAAU,CAAG,EAC5C,IAAI,CAACI,SAAS,CAAC,QAAS,KAAK,EAAG,IAAI,CAACC,KAAK,CAACN,OAAO,CAACO,MAAM,EACzD,IAAI,CAACP,OAAO,CAACQ,QAAQ,CAACL,MAAM,CAACC,SAAS,CAClC,IAAI,CAACL,KAAK,GAKtB,IAAI,CAACU,SAAS,CAAG,CACbC,MAAO,IAAI,CAACV,OAAO,CAACQ,QAAQ,CAACE,KAAK,CAClCC,WAAY,CAChB,EACA,IAAI,CAACC,WAAW,CAAG,CACfF,MAAO,IAAI,CAACD,SAAS,CAACC,KAAK,CAACG,MAAM,CAAC,IAAI,CAACb,OAAO,CAACE,UAAU,CAACQ,KAAK,EAChEC,WAAY,IAAI,CAACF,SAAS,CAACC,KAAK,CAACI,MAAM,AAC3C,GAGJ,IAAI,CAACf,KAAK,CAAGD,CACjB,CACAiB,QAAQC,CAAK,CAAE,CACX,MAAO,CAACA,EAAMC,CAAC,CAAED,EAAME,MAAM,CAAEF,EAAMG,IAAI,CAAC,AAC9C,CACAC,WAAY,CACR,IAAMC,EAAY,IAAI,CAAEC,EAAY,CAAC,aAAc,WAAW,CAC9DzC,IAA8EQ,WAAW,CAACJ,MAAM,CAACV,SAAS,CAAC6C,SAAS,CAACxB,KAAK,CAACyB,GAC3HA,EAAUE,MAAM,CAACC,OAAO,CAAC,SAAUR,CAAK,EACpC,CAACA,EAAME,MAAM,CAAEF,EAAMG,IAAI,CAAC,CAACK,OAAO,CAAC,SAAUC,CAAK,CAAEC,CAAC,EAC7CD,AAAU,OAAVA,GACAT,CAAAA,CAAK,CAACM,CAAS,CAACI,EAAE,CAAC,CACfL,EAAUM,KAAK,CAACC,QAAQ,CAACH,EAAO,CAAA,EAAI,CAEhD,EACJ,EACJ,CACAI,SAAU,CAEN,IAAI,CAACC,KAAK,CAAG,KACb,IAAI,CAACC,SAAS,CAAG,IAAI,CAACA,SAAS,EAAI,IAAI,CAACA,SAAS,CAACF,OAAO,GACzD,IAAI,CAACG,WAAW,CAAG,IAAI,CAACA,WAAW,EAAI,IAAI,CAACA,WAAW,CAACH,OAAO,GAC/D9C,IAA0IM,WAAW,CAACF,GAAG,CAACZ,SAAS,CAACsD,OAAO,CAACjC,KAAK,CAAC,IAAI,CAAEC,UAC5L,CACAoC,WAAY,CACR,IAAMZ,EAAY,IAAI,CAAEa,EAAiBb,EAAUE,MAAM,CAAEY,EAAkBd,EAAUrB,OAAO,CAAEoC,EAAiBf,EAAUX,KAAK,CAAE2B,EAAe,CAC7IrC,QAAS,CACLsC,QAASH,EAAgBG,OAAO,AACpC,CACJ,EAAGC,EAAe,CAAC,EAAE,CAAE,EAAE,CAAC,CACtBvB,EAAOwB,EAAeN,EAAepB,MAAM,CAE/C,KAAO0B,KAEChD,EAAQwB,AADZA,CAAAA,EAAQkB,CAAc,CAACM,EAAa,AAAD,EACjBC,QAAQ,GACtBF,CAAY,CAAC,EAAE,CAACG,IAAI,CAAC,CACjBC,MAAO3B,EAAM2B,KAAK,CAClBC,MAAO5B,EAAMyB,QAAQ,CACrBI,OAAQ,CAACrD,EAAQwB,EAAMyB,QAAQ,CACnC,GAEAjD,EAAQwB,EAAM8B,UAAU,GACxBP,CAAY,CAAC,EAAE,CAACG,IAAI,CAAC,CACjBC,MAAO3B,EAAM2B,KAAK,CAClBC,MAAO5B,EAAM8B,UAAU,CACvBD,OAAQ,CAACrD,EAAQwB,EAAMyB,QAAQ,CACnC,GAIR,CAAC,OAAQ,SAAS,CAACjB,OAAO,CAAC,CAACuB,EAAUrB,KAClCL,EAAUE,MAAM,CAAGgB,CAAY,CAACb,EAAE,CAClCL,EAAUrB,OAAO,CAAGP,EAAM0C,CAAe,CAAC,CAAC,EAAEY,EAAS,IAAI,CAAC,CAAC,EAAE5C,QAAU,CAAC,EAAGkC,GAC5EhB,EAAUS,KAAK,CAAGT,CAAS,CAAC,CAAC,KAAK,EAAE0B,EAAS,CAAC,CAAC,CAE/C1B,EAAUX,KAAK,CAAG,AAACW,CAAAA,CAAS,CAAC,CAAC,EAAE0B,EAAS,KAAK,CAAC,CAAC,CAACrC,KAAK,EAAI,EAAE,AAAD,EAAGsC,KAAK,CAAC3B,CAAS,CAAC,CAAC,EAAE0B,EAAS,KAAK,CAAC,CAAC,CAACpC,UAAU,EAAI,GAChH5B,IAA0IM,WAAW,CAACF,GAAG,CAACZ,SAAS,CAAC0D,SAAS,CAACxD,IAAI,CAAC4C,GACnLA,CAAS,CAAC,CAAC,KAAK,EAAE0B,EAAS,CAAC,CAAC,CAAG1B,EAAUS,KAAK,AACnD,GAEAT,EAAUE,MAAM,CAAGW,EACnBb,EAAUrB,OAAO,CAAGmC,EACpBd,EAAUX,KAAK,CAAG0B,CACtB,CACAa,YAAa,CAGT,IAAMb,EAAiB,IAAI,CAAC1B,KAAK,AAEjC,CAAA,IAAI,CAACA,KAAK,CAAG,IAAI,CAACE,WAAW,CAACF,KAAK,CACnC3B,IAA0IM,WAAW,CAACF,GAAG,CAACZ,SAAS,CAAC0E,UAAU,CAACxE,IAAI,CAAC,IAAI,EAEpL,IAAI,CAACsD,SAAS,EAAI,IAAI,CAAC/B,OAAO,CAACQ,QAAQ,CAACE,KAAK,CAACI,MAAM,EACpD,IAAI,CAACiB,SAAS,CAACmB,IAAI,GAEvB,IAAI,CAACxC,KAAK,CAAG0B,CACjB,CACAe,UAAUC,CAAM,CAAEC,CAAM,CAAE,CACtB,IAAMC,EAAgBD,EAAOE,UAAU,CAAGF,EAAOG,WAAW,CAC5DrC,EAAO,EAAE,CAAEsC,EAAQ,EAAE,CAAEC,EAAQ,EAAE,CAC7BC,EAAUC,EAASlC,EAAGmC,EAAI,EAAG3D,EAAa,EAAE,CAChD,IAAIkD,CAAAA,EAAOU,KAAK,CAAChD,MAAM,CACnBuC,EAAOE,UAAU,CAAGF,EAAOU,YAAY,AAAD,GAgB1C,IAAKrC,EAAI,EAZTiC,EAAW5E,IAA0IM,WAAW,CAAC2E,GAAG,CAACzF,SAAS,CAAC4E,SAAS,CAACC,EAAQ,CAC7La,OAAQZ,EAAOG,WAAW,CAC1BU,MAAOb,EAAOa,KAAK,AACvB,GACAN,EAAU7E,IAA0IM,WAAW,CAAC2E,GAAG,CAACzF,SAAS,CAAC4E,SAAS,CAACC,EAAQ,CAC5La,OAAQZ,EAAOE,UAAU,CACzBW,MAAOb,EAAOa,KAAK,AACvB,GACAP,EAAWA,EAASQ,MAAM,CAC1BP,EAAUA,EAAQO,MAAM,CAGZzC,GAAKiC,EAAS7C,MAAM,CAAEY,IAC1BlC,EAAQoE,CAAO,CAAClC,EAAE,GAClBlC,EAAQoE,CAAO,CAAClC,EAAE,CAAC,EAAE,GACrBlC,EAAQmE,CAAQ,CAACjC,EAAI4B,EAAa,GAClC9D,EAAQmE,CAAQ,CAACjC,EAAI4B,EAAa,CAAC,EAAE,GACrCnC,EAAKuB,IAAI,CAAC,CACNiB,CAAQ,CAACjC,EAAI4B,EAAa,CAAC,EAAE,CAC7B,EACA,KACAK,CAAQ,CAACjC,EAAI4B,EAAa,CAAC,EAAE,CACzBM,CAAO,CAAClC,EAAE,CAAC,EAAE,CACpB,EAKT,IAAKA,EAAI,EAAGA,EAAIP,EAAKL,MAAM,CAAEY,IACzB+B,EAAMf,IAAI,CAACvB,CAAI,CAACO,EAAE,CAAC,EAAE,EACrBgC,EAAMhB,IAAI,CAAC,CAAC,EAAG,KAAMvB,CAAI,CAACO,EAAE,CAAC,EAAE,CAAC,EAapC,IAAKA,EAAI,EAHTxB,EAAaA,AAPbA,CAAAA,EAAanB,IAA0IM,WAAW,CAAC2E,GAAG,CAACzF,SAAS,CAAC4E,SAAS,CAAC,CACvLW,MAAOL,EACPW,MAAOV,CACX,EAAG,CACCO,OAAQZ,EAAOU,YAAY,CAC3BG,MAAO,CACX,EAAC,EACuBC,MAAM,CAGlBzC,EAAIP,EAAKL,MAAM,CAAEY,IAErBP,CAAI,CAACO,EAAE,CAAC,EAAE,EAAIxB,CAAU,CAAC,EAAE,CAAC,EAAE,GAC9BiB,CAAI,CAACO,EAAE,CAAC,EAAE,CAAGxB,CAAU,CAAC2D,EAAE,CAAC,EAAE,CAC7BH,CAAK,CAAChC,EAAE,CAAG,CAAC,EAAGxB,CAAU,CAAC2D,EAAE,CAAC,EAAE,CAAE1C,CAAI,CAACO,EAAE,CAAC,EAAE,CAAC,CACxCP,AAAe,OAAfA,CAAI,CAACO,EAAE,CAAC,EAAE,EACVP,CAAI,CAACO,EAAE,CAAC,EAAE,CAAG,EACbgC,CAAK,CAAChC,EAAE,CAAC,EAAE,CAAG,IAGdP,CAAI,CAACO,EAAE,CAAC,EAAE,CAAGnC,EAAa4B,CAAI,CAACO,EAAE,CAAC,EAAE,CAChCxB,CAAU,CAAC2D,EAAE,CAAC,EAAE,EACpBH,CAAK,CAAChC,EAAE,CAAC,EAAE,CAAGnC,EAAa4B,CAAI,CAACO,EAAE,CAAC,EAAE,CACjCxB,CAAU,CAAC2D,EAAE,CAAC,EAAE,GAExBA,KAGR,MAAO,CACHM,OAAQhD,EACR2C,MAAOL,EACPW,MAAOV,CACX,EACJ,CACJ,CAqBAhE,EAAc2E,cAAc,CAAG5E,EAAML,EAAaiF,cAAc,CAAE,CAC9DhB,OAAQ,CAIJG,YAAa,GAIbD,WAAY,GAIZQ,aAAc,EACdE,OAAQ,EACZ,EAIA/D,WAAY,CAORQ,MAAO,EAAE,CACTP,OAAQ,CAIJmE,UAAW,EAMXlE,UAAW,KAAK,CACpB,CACJ,EAIAI,SAAU,CAONE,MAAO,EAAE,CACTP,OAAQ,CAIJmE,UAAW,EAMXlE,UAAW,KAAK,CACpB,CACJ,EAIAmE,UAAW,EACXC,aAAc,GACdC,aAAc,GACdC,MAAO,CAAA,EACPC,OAAQ,CACJC,MAAO,CACHC,KAAM,CACFC,KAAM,CACV,CACJ,CACJ,EACAC,QAAS,CACLC,YAAa,mJAIjB,EACAC,aAAc,CACVC,cAAe,UACnB,EACAC,eAAgB,CACpB,GACA7F,EAAOI,EAAcnB,SAAS,CAAE,CAC5B6G,eAAgB,CAAC,aAAc,cAAe,eAAe,CAE7DC,cAAe,CAAC,IAAK,SAAU,OAAO,CACtCC,eAAgB,CAAC,IAAK,IAAK,SAAU,OAAO,CAC5CC,YAAa,IAEbC,cAAexG,EACfyG,iBAAkB,AAAC5G,IAA+EQ,WAAW,CAACJ,MAAM,CAACV,SAAS,CAACkH,gBAAgB,CAC/IC,SAAU,AAAC7G,IAA+EQ,WAAW,CAACJ,MAAM,CAACV,SAAS,CAACmH,QAAQ,CAC/HC,WAAY,AAAC9G,IAA+EQ,WAAW,CAACJ,MAAM,CAACV,SAAS,CAACoH,UAAU,AACvI,GACA5G,IAA0I6G,kBAAkB,CAAC,OAAQlG,GA+BxI,IAAMf,EAAaE,IAGtC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}