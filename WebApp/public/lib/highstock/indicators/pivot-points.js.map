{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/pivot-points\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Paweł Fus\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/pivot-points\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/pivot-points\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ pivot_points_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/PivotPoints/PivotPointsPoint.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst SMAPoint = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.sma.prototype.pointClass;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction destroyExtraLabels(point, functionName) {\n    const props = point.series.pointArrayMap;\n    let prop, i = props.length;\n    (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.sma.prototype.pointClass.prototype[functionName].call(point);\n    while (i--) {\n        prop = 'dataLabel' + props[i];\n        // S4 dataLabel could be removed by parent method:\n        if (point[prop] && point[prop].element) {\n            point[prop].destroy();\n        }\n        point[prop] = null;\n    }\n}\n/* *\n *\n *  Class\n *\n * */\nclass PivotPointsPoint extends SMAPoint {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    destroyElements() {\n        destroyExtraLabels(this, 'destroyElements');\n    }\n    // This method is called when removing points, e.g. series.update()\n    destroy() {\n        destroyExtraLabels(this, 'destroyElements');\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const PivotPoints_PivotPointsPoint = (PivotPointsPoint);\n\n;// ./code/es-modules/Stock/Indicators/PivotPoints/PivotPointsIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { sma: SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { merge, extend, defined, isArray } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/**\n *\n *  Class\n *\n **/\n/**\n * The Pivot Points series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.pivotpoints\n *\n * @augments Highcharts.Series\n */\nclass PivotPointsIndicator extends SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    toYData(point) {\n        return [point.P]; // The rest should not affect extremes\n    }\n    translate() {\n        const indicator = this;\n        super.translate.apply(indicator);\n        indicator.points.forEach(function (point) {\n            indicator.pointArrayMap.forEach(function (value) {\n                if (defined(point[value])) {\n                    point['plot' + value] = (indicator.yAxis.toPixels(point[value], true));\n                }\n            });\n        });\n        // Pivot points are rendered as horizontal lines\n        // And last point start not from the next one (as it's the last one)\n        // But from the approximated last position in a given range\n        indicator.plotEndPoint = indicator.xAxis.toPixels(indicator.endPoint, true);\n    }\n    getGraphPath(points) {\n        const indicator = this, allPivotPoints = ([[], [], [], [], [], [], [], [], []]), pointArrayMapLength = indicator.pointArrayMap.length;\n        let endPoint = indicator.plotEndPoint, path = [], position, point, pointsLength = points.length, i;\n        while (pointsLength--) {\n            point = points[pointsLength];\n            for (i = 0; i < pointArrayMapLength; i++) {\n                position = indicator.pointArrayMap[i];\n                if (defined(point[position])) {\n                    allPivotPoints[i].push({\n                        // Start left:\n                        plotX: point.plotX,\n                        plotY: point['plot' + position],\n                        isNull: false\n                    }, {\n                        // Go to right:\n                        plotX: endPoint,\n                        plotY: point['plot' + position],\n                        isNull: false\n                    }, {\n                        // And add null points in path to generate breaks:\n                        plotX: endPoint,\n                        plotY: null,\n                        isNull: true\n                    });\n                }\n            }\n            endPoint = point.plotX;\n        }\n        allPivotPoints.forEach((pivotPoints) => {\n            path = path.concat(super.getGraphPath.call(indicator, pivotPoints));\n        });\n        return path;\n    }\n    // TODO: Rewrite this logic to use multiple datalabels\n    drawDataLabels() {\n        const indicator = this, pointMapping = indicator.pointArrayMap;\n        let currentLabel, pointsLength, point, i;\n        if (indicator.options.dataLabels.enabled) {\n            pointsLength = indicator.points.length;\n            // For every Resistance/Support group we need to render labels.\n            // Add one more item, which will just store dataLabels from\n            // previous iteration\n            pointMapping.concat([false]).forEach((position, k) => {\n                i = pointsLength;\n                while (i--) {\n                    point = indicator.points[i];\n                    if (!position) {\n                        // Store S4 dataLabel too:\n                        point['dataLabel' + pointMapping[k - 1]] =\n                            point.dataLabel;\n                    }\n                    else {\n                        point.y = point[position];\n                        point.pivotLine = position;\n                        point.plotY = point['plot' + position];\n                        currentLabel = point['dataLabel' + position];\n                        // Store previous label\n                        if (k) {\n                            point['dataLabel' + pointMapping[k - 1]] = point.dataLabel;\n                        }\n                        if (!point.dataLabels) {\n                            point.dataLabels = [];\n                        }\n                        point.dataLabels[0] = point.dataLabel =\n                            currentLabel =\n                                currentLabel && currentLabel.element ?\n                                    currentLabel :\n                                    null;\n                    }\n                }\n                super.drawDataLabels\n                    .call(indicator);\n            });\n        }\n    }\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, placement = this[params.algorithm + 'Placement'], \n        // 0- from, 1- to, 2- R1, 3- R2, 4- pivot, 5- S1 etc.\n        PP = [], xData = [], yData = [];\n        let endTimestamp, slicedXLen, slicedX, slicedY, lastPP, pivot, avg, i;\n        // Pivot Points requires high, low and close values\n        if (xVal.length < period ||\n            !isArray(yVal[0]) ||\n            yVal[0].length !== 4) {\n            return;\n        }\n        for (i = period + 1; i <= yValLen + period; i += period) {\n            slicedX = xVal.slice(i - period - 1, i);\n            slicedY = yVal.slice(i - period - 1, i);\n            slicedXLen = slicedX.length;\n            endTimestamp = slicedX[slicedXLen - 1];\n            pivot = this.getPivotAndHLC(slicedY);\n            avg = placement(pivot);\n            lastPP = PP.push([endTimestamp]\n                .concat(avg));\n            xData.push(endTimestamp);\n            yData.push(PP[lastPP - 1].slice(1));\n        }\n        // We don't know exact position in ordinal axis\n        // So we use simple logic:\n        // Get first point in last range, calculate visible average range\n        // and multiply by period\n        this.endPoint = slicedX[0] + ((endTimestamp - slicedX[0]) /\n            slicedXLen) * period;\n        return {\n            values: PP,\n            xData: xData,\n            yData: yData\n        };\n    }\n    getPivotAndHLC(values) {\n        const close = values[values.length - 1][3];\n        let high = -Infinity, low = Infinity;\n        values.forEach(function (p) {\n            high = Math.max(high, p[1]);\n            low = Math.min(low, p[2]);\n        });\n        const pivot = (high + low + close) / 3;\n        return [pivot, high, low, close];\n    }\n    standardPlacement(values) {\n        const diff = values[1] - values[2], avg = [\n            null,\n            null,\n            values[0] + diff,\n            values[0] * 2 - values[2],\n            values[0],\n            values[0] * 2 - values[1],\n            values[0] - diff,\n            null,\n            null\n        ];\n        return avg;\n    }\n    camarillaPlacement(values) {\n        const diff = values[1] - values[2], avg = [\n            values[3] + diff * 1.5,\n            values[3] + diff * 1.25,\n            values[3] + diff * 1.1666,\n            values[3] + diff * 1.0833,\n            values[0],\n            values[3] - diff * 1.0833,\n            values[3] - diff * 1.1666,\n            values[3] - diff * 1.25,\n            values[3] - diff * 1.5\n        ];\n        return avg;\n    }\n    fibonacciPlacement(values) {\n        const diff = values[1] - values[2], avg = [\n            null,\n            values[0] + diff,\n            values[0] + diff * 0.618,\n            values[0] + diff * 0.382,\n            values[0],\n            values[0] - diff * 0.382,\n            values[0] - diff * 0.618,\n            values[0] - diff,\n            null\n        ];\n        return avg;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Pivot points indicator. This series requires the `linkedTo` option to be\n * set and should be loaded after `stock/indicators/indicators.js` file.\n *\n * @sample stock/indicators/pivot-points\n *         Pivot points\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/pivot-points\n * @optionparent plotOptions.pivotpoints\n */\nPivotPointsIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    /**\n     * @excluding index\n     */\n    params: {\n        index: void 0, // Unchangeable index, do not inherit (#15362)\n        period: 28,\n        /**\n         * Algorithm used to calculate resistance and support lines based\n         * on pivot points. Implemented algorithms: `'standard'`,\n         * `'fibonacci'` and `'camarilla'`\n         */\n        algorithm: 'standard'\n    },\n    marker: {\n        enabled: false\n    },\n    enableMouseTracking: false,\n    dataLabels: {\n        enabled: true,\n        format: '{point.pivotLine}'\n    },\n    dataGrouping: {\n        approximation: 'averages'\n    }\n});\nextend(PivotPointsIndicator.prototype, {\n    nameBase: 'Pivot Points',\n    pointArrayMap: ['R4', 'R3', 'R2', 'R1', 'P', 'S1', 'S2', 'S3', 'S4'],\n    pointValKey: 'P',\n    pointClass: PivotPoints_PivotPointsPoint\n});\n/* *\n *\n *  Registry\n *\n * */\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('pivotpoints', PivotPointsIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const PivotPoints_PivotPointsIndicator = ((/* unused pure expression or super */ null && (PivotPointsIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A pivot points indicator. If the [type](#series.pivotpoints.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.pivotpoints\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/pivot-points\n * @apioption series.pivotpoints\n */\n''; // To include the above in the js output'\n\n;// ./code/es-modules/masters/indicators/pivot-points.js\n\n\n\n\n/* harmony default export */ const pivot_points_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "pivot_points_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "SMAPoint", "seriesTypes", "sma", "pointClass", "destroyExtraLabels", "point", "functionName", "props", "series", "pointArrayMap", "i", "length", "element", "destroy", "SMAIndicator", "merge", "extend", "defined", "isArray", "PivotPointsIndicator", "toYData", "P", "translate", "indicator", "apply", "points", "for<PERSON>ach", "value", "yAxis", "toPixels", "plotEndPoint", "xAxis", "endPoint", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "allPivotPoints", "pointArrayMapLength", "path", "position", "pointsLength", "push", "plotX", "plotY", "isNull", "pivotPoints", "concat", "drawDataLabels", "current<PERSON><PERSON><PERSON>", "pointMapping", "options", "dataLabels", "enabled", "k", "y", "pivotLine", "dataLabel", "getV<PERSON>ues", "params", "endTimestamp", "slicedXLen", "slicedX", "slicedY", "lastPP", "avg", "period", "xVal", "xData", "yVal", "yData", "yValLen", "placement", "algorithm", "PP", "slice", "getPivotAndHLC", "values", "close", "high", "Infinity", "low", "p", "Math", "max", "min", "standardPlacement", "diff", "camarillaPlacement", "fibonacciPlacement", "defaultOptions", "index", "marker", "enableMouseTracking", "format", "dataGrouping", "approximation", "nameBase", "pointVal<PERSON>ey", "destroyElements", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,qCAAsC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GAC9H,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,qCAAqC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAElHA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAWjL,IAAME,EAAW,AAACD,IAA2IE,WAAW,CAACC,GAAG,CAACX,SAAS,CAACY,UAAU,CASjM,SAASC,EAAmBC,CAAK,CAAEC,CAAY,EAC3C,IAAMC,EAAQF,EAAMG,MAAM,CAACC,aAAa,CACpCnB,EAAMoB,EAAIH,EAAMI,MAAM,CAE1B,IADA,AAACZ,IAA2IE,WAAW,CAACC,GAAG,CAACX,SAAS,CAACY,UAAU,CAACZ,SAAS,CAACe,EAAa,CAACb,IAAI,CAACY,GACvMK,KAGCL,CAAK,CAFTf,EAAO,YAAciB,CAAK,CAACG,EAAE,CAEd,EAAIL,CAAK,CAACf,EAAK,CAACsB,OAAO,EAClCP,CAAK,CAACf,EAAK,CAACuB,OAAO,GAEvBR,CAAK,CAACf,EAAK,CAAG,IAEtB,CAsCA,GAAM,CAAEY,IAAKY,CAAY,CAAE,CAAG,AAACf,IAA2IE,WAAW,CAE/K,CAAEc,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAEC,QAAAA,CAAO,CAAEC,QAAAA,CAAO,CAAE,CAAIrB,GAe7C,OAAMsB,UAA6BL,EAM/BM,QAAQf,CAAK,CAAE,CACX,MAAO,CAACA,EAAMgB,CAAC,CAAC,AACpB,CACAC,WAAY,CACR,IAAMC,EAAY,IAAI,CACtB,KAAK,CAACD,UAAUE,KAAK,CAACD,GACtBA,EAAUE,MAAM,CAACC,OAAO,CAAC,SAAUrB,CAAK,EACpCkB,EAAUd,aAAa,CAACiB,OAAO,CAAC,SAAUC,CAAK,EACvCV,EAAQZ,CAAK,CAACsB,EAAM,GACpBtB,CAAAA,CAAK,CAAC,OAASsB,EAAM,CAAIJ,EAAUK,KAAK,CAACC,QAAQ,CAACxB,CAAK,CAACsB,EAAM,CAAE,CAAA,EAAK,CAE7E,EACJ,GAIAJ,EAAUO,YAAY,CAAGP,EAAUQ,KAAK,CAACF,QAAQ,CAACN,EAAUS,QAAQ,CAAE,CAAA,EAC1E,CACAC,aAAaR,CAAM,CAAE,CACjB,IAAMF,EAAY,IAAI,CAAEW,EAAkB,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAAGC,EAAsBZ,EAAUd,aAAa,CAACE,MAAM,CACjIqB,EAAWT,EAAUO,YAAY,CAAEM,EAAO,EAAE,CAAEC,EAAUhC,EAAOiC,EAAeb,EAAOd,MAAM,CAAED,EACjG,KAAO4B,KAAgB,CAEnB,IAAK5B,EAAI,EADTL,EAAQoB,CAAM,CAACa,EAAa,CAChB5B,EAAIyB,EAAqBzB,IAE7BO,EAAQZ,CAAK,CADjBgC,EAAWd,EAAUd,aAAa,CAACC,EAAE,CACV,GACvBwB,CAAc,CAACxB,EAAE,CAAC6B,IAAI,CAAC,CAEnBC,MAAOnC,EAAMmC,KAAK,CAClBC,MAAOpC,CAAK,CAAC,OAASgC,EAAS,CAC/BK,OAAQ,CAAA,CACZ,EAAG,CAECF,MAAOR,EACPS,MAAOpC,CAAK,CAAC,OAASgC,EAAS,CAC/BK,OAAQ,CAAA,CACZ,EAAG,CAECF,MAAOR,EACPS,MAAO,KACPC,OAAQ,CAAA,CACZ,GAGRV,EAAW3B,EAAMmC,KAAK,AAC1B,CAIA,OAHAN,EAAeR,OAAO,CAAC,AAACiB,IACpBP,EAAOA,EAAKQ,MAAM,CAAC,KAAK,CAACX,aAAaxC,IAAI,CAAC8B,EAAWoB,GAC1D,GACOP,CACX,CAEAS,gBAAiB,CACb,IACIC,EAAcR,EAAcjC,EAAOK,EADjCa,EAAY,IAAI,CAAEwB,EAAexB,EAAUd,aAAa,AAE1Dc,CAAAA,EAAUyB,OAAO,CAACC,UAAU,CAACC,OAAO,GACpCZ,EAAef,EAAUE,MAAM,CAACd,MAAM,CAItCoC,EAAaH,MAAM,CAAC,CAAC,CAAA,EAAM,EAAElB,OAAO,CAAC,CAACW,EAAUc,KAE5C,IADAzC,EAAI4B,EACG5B,KACHL,EAAQkB,EAAUE,MAAM,CAACf,EAAE,CACtB2B,GAMDhC,EAAM+C,CAAC,CAAG/C,CAAK,CAACgC,EAAS,CACzBhC,EAAMgD,SAAS,CAAGhB,EAClBhC,EAAMoC,KAAK,CAAGpC,CAAK,CAAC,OAASgC,EAAS,CACtCS,EAAezC,CAAK,CAAC,YAAcgC,EAAS,CAExCc,GACA9C,CAAAA,CAAK,CAAC,YAAc0C,CAAY,CAACI,EAAI,EAAE,CAAC,CAAG9C,EAAMiD,SAAS,AAAD,EAEzD,AAACjD,EAAM4C,UAAU,EACjB5C,CAAAA,EAAM4C,UAAU,CAAG,EAAE,AAAD,EAExB5C,EAAM4C,UAAU,CAAC,EAAE,CAAG5C,EAAMiD,SAAS,CACjCR,EACIA,GAAgBA,EAAalC,OAAO,CAChCkC,EACA,MAnBZzC,CAAK,CAAC,YAAc0C,CAAY,CAACI,EAAI,EAAE,CAAC,CACpC9C,EAAMiD,SAAS,CAqB3B,KAAK,CAACT,eACDpD,IAAI,CAAC8B,EACd,GAER,CACAgC,UAAU/C,CAAM,CAAEgD,CAAM,CAAE,CACtB,IAGIC,EAAcC,EAAYC,EAASC,EAASC,EAAeC,EAAKpD,EAH9DqD,EAASP,EAAOO,MAAM,CAAEC,EAAOxD,EAAOyD,KAAK,CAAEC,EAAO1D,EAAO2D,KAAK,CAAEC,EAAUF,EAAOA,EAAKvD,MAAM,CAAG,EAAG0D,EAAY,IAAI,CAACb,EAAOc,SAAS,CAAG,YAAY,CAE1JC,EAAK,EAAE,CAAEN,EAAQ,EAAE,CAAEE,EAAQ,EAAE,CAG/B,GAAIH,CAAAA,CAAAA,EAAKrD,MAAM,CAAGoD,CAAK,GAClB7C,EAAQgD,CAAI,CAAC,EAAE,GAChBA,AAAmB,IAAnBA,CAAI,CAAC,EAAE,CAACvD,MAAM,EAGlB,IAAKD,EAAIqD,EAAS,EAAGrD,GAAK0D,EAAUL,EAAQrD,GAAKqD,EAC7CJ,EAAUK,EAAKQ,KAAK,CAAC9D,EAAIqD,EAAS,EAAGrD,GACrCkD,EAAUM,EAAKM,KAAK,CAAC9D,EAAIqD,EAAS,EAAGrD,GACrCgD,EAAaC,EAAQhD,MAAM,CAC3B8C,EAAeE,CAAO,CAACD,EAAa,EAAE,CAEtCI,EAAMO,EADE,IAAI,CAACI,cAAc,CAACb,IAE5BC,EAASU,EAAGhC,IAAI,CAAC,CAACkB,EAAa,CAC1Bb,MAAM,CAACkB,IACZG,EAAM1B,IAAI,CAACkB,GACXU,EAAM5B,IAAI,CAACgC,CAAE,CAACV,EAAS,EAAE,CAACW,KAAK,CAAC,IAQpC,OAFA,IAAI,CAACxC,QAAQ,CAAG2B,CAAO,CAAC,EAAE,CAAG,AAAEF,CAAAA,EAAeE,CAAO,CAAC,EAAE,AAAD,EACnDD,EAAcK,EACX,CACHW,OAAQH,EACRN,MAAOA,EACPE,MAAOA,CACX,EACJ,CACAM,eAAeC,CAAM,CAAE,CACnB,IAAMC,EAAQD,CAAM,CAACA,EAAO/D,MAAM,CAAG,EAAE,CAAC,EAAE,CACtCiE,EAAO,CAACC,IAAUC,EAAMD,IAM5B,OALAH,EAAOhD,OAAO,CAAC,SAAUqD,CAAC,EACtBH,EAAOI,KAAKC,GAAG,CAACL,EAAMG,CAAC,CAAC,EAAE,EAC1BD,EAAME,KAAKE,GAAG,CAACJ,EAAKC,CAAC,CAAC,EAAE,CAC5B,GAEO,CADO,AAACH,CAAAA,EAAOE,EAAMH,CAAI,EAAK,EACtBC,EAAME,EAAKH,EAAM,AACpC,CACAQ,kBAAkBT,CAAM,CAAE,CACtB,IAAMU,EAAOV,CAAM,CAAC,EAAE,CAAGA,CAAM,CAAC,EAAE,CAWlC,MAX0C,CACtC,KACA,KACAA,CAAM,CAAC,EAAE,CAAGU,EACZV,AAAY,EAAZA,CAAM,CAAC,EAAE,CAAOA,CAAM,CAAC,EAAE,CACzBA,CAAM,CAAC,EAAE,CACTA,AAAY,EAAZA,CAAM,CAAC,EAAE,CAAOA,CAAM,CAAC,EAAE,CACzBA,CAAM,CAAC,EAAE,CAAGU,EACZ,KACA,KACH,AAEL,CACAC,mBAAmBX,CAAM,CAAE,CACvB,IAAMU,EAAOV,CAAM,CAAC,EAAE,CAAGA,CAAM,CAAC,EAAE,CAWlC,MAX0C,CACtCA,CAAM,CAAC,EAAE,CAAGU,AAAO,IAAPA,EACZV,CAAM,CAAC,EAAE,CAAGU,AAAO,KAAPA,EACZV,CAAM,CAAC,EAAE,CAAGU,AAAO,OAAPA,EACZV,CAAM,CAAC,EAAE,CAAGU,AAAO,OAAPA,EACZV,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CAAGU,AAAO,OAAPA,EACZV,CAAM,CAAC,EAAE,CAAGU,AAAO,OAAPA,EACZV,CAAM,CAAC,EAAE,CAAGU,AAAO,KAAPA,EACZV,CAAM,CAAC,EAAE,CAAGU,AAAO,IAAPA,EACf,AAEL,CACAE,mBAAmBZ,CAAM,CAAE,CACvB,IAAMU,EAAOV,CAAM,CAAC,EAAE,CAAGA,CAAM,CAAC,EAAE,CAWlC,MAX0C,CACtC,KACAA,CAAM,CAAC,EAAE,CAAGU,EACZV,CAAM,CAAC,EAAE,CAAGU,AAAO,KAAPA,EACZV,CAAM,CAAC,EAAE,CAAGU,AAAO,KAAPA,EACZV,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CAAGU,AAAO,KAAPA,EACZV,CAAM,CAAC,EAAE,CAAGU,AAAO,KAAPA,EACZV,CAAM,CAAC,EAAE,CAAGU,EACZ,KACH,AAEL,CACJ,CAoBAjE,EAAqBoE,cAAc,CAAGxE,EAAMD,EAAayE,cAAc,CAAE,CAIrE/B,OAAQ,CACJgC,MAAO,KAAK,EACZzB,OAAQ,GAMRO,UAAW,UACf,EACAmB,OAAQ,CACJvC,QAAS,CAAA,CACb,EACAwC,oBAAqB,CAAA,EACrBzC,WAAY,CACRC,QAAS,CAAA,EACTyC,OAAQ,mBACZ,EACAC,aAAc,CACVC,cAAe,UACnB,CACJ,GACA7E,EAAOG,EAAqB5B,SAAS,CAAE,CACnCuG,SAAU,eACVrF,cAAe,CAAC,KAAM,KAAM,KAAM,KAAM,IAAK,KAAM,KAAM,KAAM,KAAK,CACpEsF,YAAa,IACb5F,WA7RJ,cAA+BH,EAM3BgG,iBAAkB,CACd5F,EAAmB,IAAI,CAAE,kBAC7B,CAEAS,SAAU,CACNT,EAAmB,IAAI,CAAE,kBAC7B,CACJ,CAiRA,GAMAL,IAA0IkG,kBAAkB,CAAC,cAAe9E,GA+B/I,IAAMxB,EAAqBE,IAG9C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}