{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/cmf\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * (c) 2010-2025 Highsoft AS\n * Author: <PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/cmf\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/cmf\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ cmf_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/CMF/CMFIndicator.js\n/* *\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Sebastian Domas\n *\n *  Chaikin Money Flow indicator for Highcharts Stock\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The CMF series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.cmf\n *\n * @augments Highcharts.Series\n */\nclass CMFIndicator extends SMAIndicator {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        this.nameBase = 'Chaikin Money Flow';\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Checks if the series and volumeSeries are accessible, number of\n     * points.x is longer than period, is series has OHLC data\n     * @private\n     * @param {Highcharts.CMFIndicator} this indicator to use.\n     * @return {boolean} True if series is valid and can be computed,\n     * otherwise false.\n     */\n    isValid() {\n        const chart = this.chart, options = this.options, series = this.linkedParent, volumeSeries = (this.volumeSeries ||\n            (this.volumeSeries =\n                chart.get(options.params.volumeSeriesID))), isSeriesOHLC = (series?.pointArrayMap?.length === 4);\n        /**\n         * @private\n         * @param {Highcharts.Series} serie to check length validity on.\n         * @return {boolean|undefined} true if length is valid.\n         */\n        function isLengthValid(serie) {\n            return serie.dataTable.rowCount >=\n                options.params.period;\n        }\n        return !!(series &&\n            volumeSeries &&\n            isLengthValid(series) &&\n            isLengthValid(volumeSeries) && isSeriesOHLC);\n    }\n    /**\n     * Returns indicator's data.\n     * @private\n     * @param {Highcharts.CMFIndicator} this indicator to use.\n     * @param {Highcharts.Series} series to calculate values from\n     * @param {Highcharts.CMFIndicatorParamsOptions} params to pass\n     * @return {boolean|Highcharts.IndicatorNullableValuesObject} Returns false if the\n     * indicator is not valid, otherwise returns Values object.\n     */\n    getValues(series, params) {\n        if (!this.isValid()) {\n            return;\n        }\n        return this.getMoneyFlow(series.xData, series.yData, this.volumeSeries.getColumn('y'), params.period);\n    }\n    /**\n     * @private\n     *\n     * @param {Array<number>} xData\n     * x timestamp values\n     *\n     * @param {Array<number>} seriesYData\n     * yData of basic series\n     *\n     * @param {Array<number>} volumeSeriesYData\n     * yData of volume series\n     *\n     * @param {number} period\n     * indicator's param\n     *\n     * @return {Highcharts.IndicatorNullableValuesObject}\n     * object containing computed money flow data\n     */\n    getMoneyFlow(xData, seriesYData, volumeSeriesYData, period) {\n        const len = seriesYData.length, moneyFlowVolume = [], moneyFlowXData = [], moneyFlowYData = [], values = [];\n        let i, point, nullIndex = -1, sumVolume = 0, sumMoneyFlowVolume = 0;\n        /**\n         * Calculates money flow volume, changes i, nullIndex vars from\n         * upper scope!\n         *\n         * @private\n         *\n         * @param {Array<number>} ohlc\n         * OHLC point\n         *\n         * @param {number} volume\n         * Volume point's y value\n         *\n         * @return {number|null}\n         * Volume * moneyFlowMultiplier\n         */\n        function getMoneyFlowVolume(ohlc, volume) {\n            const high = ohlc[1], low = ohlc[2], close = ohlc[3], isValid = volume !== null &&\n                high !== null &&\n                low !== null &&\n                close !== null &&\n                high !== low;\n            /**\n             * @private\n             * @param {number} h\n             * High value\n             * @param {number} l\n             * Low value\n             * @param {number} c\n             * Close value\n             * @return {number}\n             * Calculated multiplier for the point\n             */\n            function getMoneyFlowMultiplier(h, l, c) {\n                return ((c - l) - (h - c)) / (h - l);\n            }\n            return isValid ?\n                getMoneyFlowMultiplier(high, low, close) * volume :\n                ((nullIndex = i), null);\n        }\n        if (period > 0 && period <= len) {\n            for (i = 0; i < period; i++) {\n                moneyFlowVolume[i] = getMoneyFlowVolume(seriesYData[i], volumeSeriesYData[i]);\n                sumVolume += volumeSeriesYData[i];\n                sumMoneyFlowVolume += moneyFlowVolume[i];\n            }\n            moneyFlowXData.push(xData[i - 1]);\n            moneyFlowYData.push(i - nullIndex >= period && sumVolume !== 0 ?\n                sumMoneyFlowVolume / sumVolume :\n                null);\n            values.push([moneyFlowXData[0], moneyFlowYData[0]]);\n            for (; i < len; i++) {\n                moneyFlowVolume[i] = getMoneyFlowVolume(seriesYData[i], volumeSeriesYData[i]);\n                sumVolume -= volumeSeriesYData[i - period];\n                sumVolume += volumeSeriesYData[i];\n                sumMoneyFlowVolume -= moneyFlowVolume[i - period];\n                sumMoneyFlowVolume += moneyFlowVolume[i];\n                point = [\n                    xData[i],\n                    i - nullIndex >= period ?\n                        sumMoneyFlowVolume / sumVolume :\n                        null\n                ];\n                moneyFlowXData.push(point[0]);\n                moneyFlowYData.push(point[1]);\n                values.push([point[0], point[1]]);\n            }\n        }\n        return {\n            values: values,\n            xData: moneyFlowXData,\n            yData: moneyFlowYData\n        };\n    }\n}\n/**\n * Chaikin Money Flow indicator (cmf).\n *\n * @sample stock/indicators/cmf/\n *         Chaikin Money Flow indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @excluding    animationLimit\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/cmf\n * @optionparent plotOptions.cmf\n */\nCMFIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    /**\n     * @excluding index\n     */\n    params: {\n        index: void 0, // Unused index, do not inherit (#15362)\n        /**\n         * The id of another series to use its data as volume data for the\n         * indicator calculation.\n         */\n        volumeSeriesID: 'volume'\n    }\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('cmf', CMFIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const CMF_CMFIndicator = ((/* unused pure expression or super */ null && (CMFIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `CMF` series. If the [type](#series.cmf.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.cmf\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/cmf\n * @apioption series.cmf\n */\n''; // Adds doclet above to the transpiled file\n\n;// ./code/es-modules/masters/indicators/cmf.js\n\n\n\n\n/* harmony default export */ const cmf_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "cmf_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "sma", "SMAIndicator", "seriesTypes", "merge", "CMFIndicator", "constructor", "arguments", "nameBase", "<PERSON><PERSON><PERSON><PERSON>", "chart", "options", "series", "linkedParent", "volumeSeries", "params", "volumeSeriesID", "isSeriesOHLC", "pointArrayMap", "length", "is<PERSON>ength<PERSON><PERSON>d", "serie", "dataTable", "rowCount", "period", "getV<PERSON>ues", "getMoneyFlow", "xData", "yData", "getColumn", "seriesYData", "volumeSeriesYData", "len", "moneyFlowVolume", "moneyFlowXData", "moneyFlowYData", "values", "i", "point", "nullIndex", "sumVolume", "sumMoneyFlowVolume", "getMoneyFlowVolume", "ohlc", "volume", "high", "low", "close", "h", "c", "l", "push", "defaultOptions", "index", "registerSeriesType"], "mappings": "CAWA,AAXA;;;;;;;;;;CAUC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,4BAA6B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACrH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,4BAA4B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAEzGA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAiBjL,GAAM,CAAEE,IAAKC,CAAY,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAE/K,CAAEC,MAAAA,CAAK,CAAE,CAAIN,GAenB,OAAMO,UAAqBH,EACvBI,aAAc,CAMV,KAAK,IAAIC,WACT,IAAI,CAACC,QAAQ,CAAG,oBACpB,CAcAC,SAAU,CACN,IAAMC,EAAQ,IAAI,CAACA,KAAK,CAAEC,EAAU,IAAI,CAACA,OAAO,CAAEC,EAAS,IAAI,CAACC,YAAY,CAAEC,EAAgB,IAAI,CAACA,YAAY,EAC1G,CAAA,IAAI,CAACA,YAAY,CACdJ,EAAMrB,GAAG,CAACsB,EAAQI,MAAM,CAACC,cAAc,CAAA,EAAKC,EAAgBL,GAAQM,eAAeC,SAAW,EAMtG,SAASC,EAAcC,CAAK,EACxB,OAAOA,EAAMC,SAAS,CAACC,QAAQ,EAC3BZ,EAAQI,MAAM,CAACS,MAAM,AAC7B,CACA,MAAO,CAAC,CAAEZ,CAAAA,GACNE,GACAM,EAAcR,IACdQ,EAAcN,IAAiBG,CAAW,CAClD,CAUAQ,UAAUb,CAAM,CAAEG,CAAM,CAAE,CACtB,GAAK,IAAI,CAACN,OAAO,GAGjB,OAAO,IAAI,CAACiB,YAAY,CAACd,EAAOe,KAAK,CAAEf,EAAOgB,KAAK,CAAE,IAAI,CAACd,YAAY,CAACe,SAAS,CAAC,KAAMd,EAAOS,MAAM,CACxG,CAmBAE,aAAaC,CAAK,CAAEG,CAAW,CAAEC,CAAiB,CAAEP,CAAM,CAAE,CACxD,IAAMQ,EAAMF,EAAYX,MAAM,CAAEc,EAAkB,EAAE,CAAEC,EAAiB,EAAE,CAAEC,EAAiB,EAAE,CAAEC,EAAS,EAAE,CACvGC,EAAGC,EAAOC,EAAY,GAAIC,EAAY,EAAGC,EAAqB,EAgBlE,SAASC,EAAmBC,CAAI,CAAEC,CAAM,EACpC,IAAMC,EAAOF,CAAI,CAAC,EAAE,CAAEG,EAAMH,CAAI,CAAC,EAAE,CAAEI,EAAQJ,CAAI,CAAC,EAAE,CAmBpD,OAAOlC,AAnByDmC,AAAW,OAAXA,GAC5DC,AAAS,OAATA,GACAC,AAAQ,OAARA,GACAC,AAAU,OAAVA,GACAF,IAASC,EAaD,CAAA,AAG0BC,EAALD,EAHVE,CAAAA,AAGIH,EAAWE,CAHXE,CAAC,EAAMD,CAAAA,AAGPH,EAAMC,CAHKI,EAGSN,EAC1C,CAAA,AAACL,EAAYF,EAAI,IAAG,CAC7B,CACA,GAAIb,EAAS,GAAKA,GAAUQ,EAAK,CAC7B,IAAKK,EAAI,EAAGA,EAAIb,EAAQa,IACpBJ,CAAe,CAACI,EAAE,CAAGK,EAAmBZ,CAAW,CAACO,EAAE,CAAEN,CAAiB,CAACM,EAAE,EAC5EG,GAAaT,CAAiB,CAACM,EAAE,CACjCI,GAAsBR,CAAe,CAACI,EAAE,CAO5C,IALAH,EAAeiB,IAAI,CAACxB,CAAK,CAACU,EAAI,EAAE,EAChCF,EAAegB,IAAI,CAACd,EAAIE,GAAaf,GAAUgB,AAAc,IAAdA,EAC3CC,EAAqBD,EACrB,MACJJ,EAAOe,IAAI,CAAC,CAACjB,CAAc,CAAC,EAAE,CAAEC,CAAc,CAAC,EAAE,CAAC,EAC3CE,EAAIL,EAAKK,IACZJ,CAAe,CAACI,EAAE,CAAGK,EAAmBZ,CAAW,CAACO,EAAE,CAAEN,CAAiB,CAACM,EAAE,EAC5EG,GAAaT,CAAiB,CAACM,EAAIb,EAAO,CAC1CgB,GAAaT,CAAiB,CAACM,EAAE,CACjCI,GAAsBR,CAAe,CAACI,EAAIb,EAAO,CACjDiB,GAAsBR,CAAe,CAACI,EAAE,CACxCC,EAAQ,CACJX,CAAK,CAACU,EAAE,CACRA,EAAIE,GAAaf,EACbiB,EAAqBD,EACrB,KACP,CACDN,EAAeiB,IAAI,CAACb,CAAK,CAAC,EAAE,EAC5BH,EAAegB,IAAI,CAACb,CAAK,CAAC,EAAE,EAC5BF,EAAOe,IAAI,CAAC,CAACb,CAAK,CAAC,EAAE,CAAEA,CAAK,CAAC,EAAE,CAAC,CAExC,CACA,MAAO,CACHF,OAAQA,EACRT,MAAOO,EACPN,MAAOO,CACX,CACJ,CACJ,CAeA9B,EAAa+C,cAAc,CAAGhD,EAAMF,EAAakD,cAAc,CAAE,CAI7DrC,OAAQ,CACJsC,MAAO,KAAK,EAKZrC,eAAgB,QACpB,CACJ,GACAhB,IAA0IsD,kBAAkB,CAAC,MAAOjD,GA+BvI,IAAMT,EAAYE,IAGrC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}