{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/chaikin\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Wojciech Chmiel\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/chaikin\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/chaikin\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ chaikin_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/AD/ADIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n * */\n\n\nconst { sma: SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { error, extend, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The AD series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.ad\n *\n * @augments Highcharts.Series\n */\nclass ADIndicator extends SMAIndicator {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static populateAverage(xVal, yVal, yValVolume, i, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _period) {\n        const high = yVal[i][1], low = yVal[i][2], close = yVal[i][3], volume = yValVolume[i], adY = close === high && close === low || high === low ?\n            0 :\n            ((2 * close - low - high) / (high - low)) * volume, adX = xVal[i];\n        return [adX, adY];\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, volumeSeriesID = params.volumeSeriesID, volumeSeries = series.chart.get(volumeSeriesID), yValVolume = volumeSeries?.getColumn('y'), yValLen = yVal ? yVal.length : 0, AD = [], xData = [], yData = [];\n        let len, i, ADPoint;\n        if (xVal.length <= period &&\n            yValLen &&\n            yVal[0].length !== 4) {\n            return;\n        }\n        if (!volumeSeries) {\n            error('Series ' +\n                volumeSeriesID +\n                ' not found! Check `volumeSeriesID`.', true, series.chart);\n            return;\n        }\n        // When i = period <-- skip first N-points\n        // Calculate value one-by-one for each period in visible data\n        for (i = period; i < yValLen; i++) {\n            len = AD.length;\n            ADPoint = ADIndicator.populateAverage(xVal, yVal, yValVolume, i, period);\n            if (len > 0) {\n                ADPoint[1] += AD[len - 1][1];\n            }\n            AD.push(ADPoint);\n            xData.push(ADPoint[0]);\n            yData.push(ADPoint[1]);\n        }\n        return {\n            values: AD,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Accumulation Distribution (AD). This series requires `linkedTo` option to\n * be set.\n *\n * @sample stock/indicators/accumulation-distribution\n *         Accumulation/Distribution indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/accumulation-distribution\n * @optionparent plotOptions.ad\n */\nADIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    /**\n     * @excluding index\n     */\n    params: {\n        index: void 0, // Unused index, do not inherit (#15362)\n        /**\n         * The id of volume series which is mandatory.\n         * For example using OHLC data, volumeSeriesID='volume' means\n         * the indicator will be calculated using OHLC and volume values.\n         *\n         * @since 6.0.0\n         */\n        volumeSeriesID: 'volume'\n    }\n});\nextend(ADIndicator.prototype, {\n    nameComponents: false,\n    nameBase: 'Accumulation/Distribution'\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('ad', ADIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const AD_ADIndicator = (ADIndicator);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `AD` series. If the [type](#series.ad.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.ad\n * @since     6.0.0\n * @excluding dataParser, dataURL\n * @product   highstock\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/accumulation-distribution\n * @apioption series.ad\n */\n''; // Add doclet above to transpiled file\n\n;// ./code/es-modules/Stock/Indicators/Chaikin/ChaikinIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n // For historic reasons, AD is built into Chaikin\n\nconst { ema: EMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { correctFloat, extend: ChaikinIndicator_extend, merge: ChaikinIndicator_merge, error: ChaikinIndicator_error } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Chaikin series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.chaikin\n *\n * @augments Highcharts.Series\n */\nclass ChaikinIndicator extends EMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const periods = params.periods, period = params.period, \n        // 0- date, 1- Chaikin Oscillator\n        CHA = [], xData = [], yData = [];\n        let oscillator, i;\n        // Check if periods are correct\n        if (periods.length !== 2 || periods[1] <= periods[0]) {\n            ChaikinIndicator_error('Error: \"Chaikin requires two periods. Notice, first ' +\n                'period should be lower than the second one.\"');\n            return;\n        }\n        // Accumulation Distribution Line data\n        const ADL = AD_ADIndicator.prototype.getValues.call(this, series, {\n            volumeSeriesID: params.volumeSeriesID,\n            period: period\n        });\n        // Check if adl is calculated properly, if not skip\n        if (!ADL) {\n            return;\n        }\n        // Shorter Period EMA\n        const SPE = super.getValues.call(this, ADL, {\n            period: periods[0]\n        });\n        // Longer Period EMA\n        const LPE = super.getValues.call(this, ADL, {\n            period: periods[1]\n        });\n        // Check if ema is calculated properly, if not skip\n        if (!SPE || !LPE) {\n            return;\n        }\n        const periodsOffset = periods[1] - periods[0];\n        for (i = 0; i < LPE.yData.length; i++) {\n            oscillator = correctFloat(SPE.yData[i + periodsOffset] -\n                LPE.yData[i]);\n            CHA.push([LPE.xData[i], oscillator]);\n            xData.push(LPE.xData[i]);\n            yData.push(oscillator);\n        }\n        return {\n            values: CHA,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Chaikin Oscillator. This series requires the `linkedTo` option to\n * be set and should be loaded after the `stock/indicators/indicators.js`.\n *\n * @sample {highstock} stock/indicators/chaikin\n *         Chaikin Oscillator\n *\n * @extends      plotOptions.ema\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, joinBy, keys, navigatorOptions,\n *               pointInterval, pointIntervalUnit, pointPlacement,\n *               pointRange, pointStart, showInNavigator, stacking\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/chaikin\n * @optionparent plotOptions.chaikin\n */\nChaikinIndicator.defaultOptions = ChaikinIndicator_merge(EMAIndicator.defaultOptions, {\n    /**\n     * Parameters used in calculation of Chaikin Oscillator\n     * series points.\n     *\n     * @excluding index\n     */\n    params: {\n        index: void 0, // Unused index, do not inherit (#15362)\n        /**\n         * The id of volume series which is mandatory.\n         * For example using OHLC data, volumeSeriesID='volume' means\n         * the indicator will be calculated using OHLC and volume values.\n         */\n        volumeSeriesID: 'volume',\n        /**\n         * Parameter used indirectly for calculating the `AD` indicator.\n         * Decides about the number of data points that are taken\n         * into account for the indicator calculations.\n         */\n        period: 9,\n        /**\n         * Periods for Chaikin Oscillator calculations.\n         *\n         * @type    {Array<number>}\n         * @default [3, 10]\n         */\n        periods: [3, 10]\n    }\n});\nChaikinIndicator_extend(ChaikinIndicator.prototype, {\n    nameBase: 'Chaikin Osc',\n    nameComponents: ['periods']\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('chaikin', ChaikinIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Chaikin_ChaikinIndicator = ((/* unused pure expression or super */ null && (ChaikinIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `Chaikin Oscillator` series. If the [type](#series.chaikin.type)\n * option is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.chaikin\n * @since     7.0.0\n * @product   highstock\n * @excluding allAreas, colorAxis, dataParser, dataURL, joinBy, keys,\n *            navigatorOptions, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointRange, pointStart, stacking, showInNavigator\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/chaikin\n * @apioption series.chaikin\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/masters/indicators/chaikin.js\n\n\n\n\n/* harmony default export */ const chaikin_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "chaikin_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "sma", "SMAIndicator", "seriesTypes", "error", "extend", "merge", "ADIndicator", "populateAverage", "xVal", "yVal", "yValVolume", "i", "_period", "high", "low", "close", "volume", "adY", "getV<PERSON>ues", "series", "params", "len", "ADPoint", "period", "xData", "yData", "volumeSeriesID", "volumeSeries", "chart", "getColumn", "yValLen", "length", "AD", "push", "values", "defaultOptions", "index", "nameComponents", "nameBase", "registerSeriesType", "ema", "EMAIndicator", "correctFloat", "ChaikinIndicator_extend", "ChaikinIndicator_merge", "ChaikinIndicator_error", "ChaikinIndicator", "oscillator", "periods", "CHA", "ADL", "AD_ADIndicator", "SPE", "LPE", "periodsOffset"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,gCAAiC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACzH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,gCAAgC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAE7GA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAUjL,GAAM,CAAEE,IAAKC,CAAY,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAE/K,CAAEC,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAE,CAAIR,GAelC,OAAMS,UAAoBL,EAMtB,OAAOM,gBAAgBC,CAAI,CAAEC,CAAI,CAAEC,CAAU,CAAEC,CAAC,CAEhDC,CAAO,CAAE,CACL,IAAMC,EAAOJ,CAAI,CAACE,EAAE,CAAC,EAAE,CAAEG,EAAML,CAAI,CAACE,EAAE,CAAC,EAAE,CAAEI,EAAQN,CAAI,CAACE,EAAE,CAAC,EAAE,CAAEK,EAASN,CAAU,CAACC,EAAE,CAAEM,EAAMF,IAAUF,GAAQE,IAAUD,GAAOD,IAASC,EACrI,EACA,AAAE,CAAA,EAAIC,EAAQD,EAAMD,CAAG,EAAMA,CAAAA,EAAOC,CAAE,EAAME,EAChD,MAAO,CADuDR,CAAI,CAACG,EAAE,CACxDM,EAAI,AACrB,CAMAC,UAAUC,CAAM,CAAEC,CAAM,CAAE,CACtB,IACIC,EAAKV,EAAGW,EADNC,EAASH,EAAOG,MAAM,CAAEf,EAAOW,EAAOK,KAAK,CAAEf,EAAOU,EAAOM,KAAK,CAAEC,EAAiBN,EAAOM,cAAc,CAAEC,EAAeR,EAAOS,KAAK,CAACxC,GAAG,CAACsC,GAAiBhB,EAAaiB,GAAcE,UAAU,KAAMC,EAAUrB,EAAOA,EAAKsB,MAAM,CAAG,EAAGC,EAAK,EAAE,CAAER,EAAQ,EAAE,CAAEC,EAAQ,EAAE,CAE7Q,GAAIjB,CAAAA,CAAAA,EAAKuB,MAAM,EAAIR,CAAK,IACpBO,GACArB,AAAmB,IAAnBA,CAAI,CAAC,EAAE,CAACsB,MAAM,EAGlB,GAAI,CAACJ,EAAc,YACfxB,EAAM,UACFuB,EACA,sCAAuC,CAAA,EAAMP,EAAOS,KAAK,EAKjE,IAAKjB,EAAIY,EAAQZ,EAAImB,EAASnB,IAC1BU,EAAMW,EAAGD,MAAM,CACfT,EAAUhB,EAAYC,eAAe,CAACC,EAAMC,EAAMC,EAAYC,EAAGY,GAC7DF,EAAM,GACNC,CAAAA,CAAO,CAAC,EAAE,EAAIU,CAAE,CAACX,EAAM,EAAE,CAAC,EAAE,AAAD,EAE/BW,EAAGC,IAAI,CAACX,GACRE,EAAMS,IAAI,CAACX,CAAO,CAAC,EAAE,EACrBG,EAAMQ,IAAI,CAACX,CAAO,CAAC,EAAE,EAEzB,MAAO,CACHY,OAAQF,EACRR,MAAOA,EACPC,MAAOA,CACX,EACJ,CACJ,CAoBAnB,EAAY6B,cAAc,CAAG9B,EAAMJ,EAAakC,cAAc,CAAE,CAI5Df,OAAQ,CACJgB,MAAO,KAAK,EAQZV,eAAgB,QACpB,CACJ,GACAtB,EAAOE,EAAYf,SAAS,CAAE,CAC1B8C,eAAgB,CAAA,EAChBC,SAAU,2BACd,GACAvC,IAA0IwC,kBAAkB,CAAC,KAAMjC,GAqCnK,GAAM,CAAEkC,IAAKC,CAAY,CAAE,CAAG,AAAC1C,IAA2IG,WAAW,CAE/K,CAAEwC,aAAAA,CAAY,CAAEtC,OAAQuC,CAAuB,CAAEtC,MAAOuC,CAAsB,CAAEzC,MAAO0C,CAAsB,CAAE,CAAIhD,GAezH,OAAMiD,UAAyBL,EAM3BvB,UAAUC,CAAM,CAAEC,CAAM,CAAE,CACtB,IAGI2B,EAAYpC,EAHVqC,EAAU5B,EAAO4B,OAAO,CAAEzB,EAASH,EAAOG,MAAM,CAEtD0B,EAAM,EAAE,CAAEzB,EAAQ,EAAE,CAAEC,EAAQ,EAAE,CAGhC,GAAIuB,AAAmB,IAAnBA,EAAQjB,MAAM,EAAUiB,CAAO,CAAC,EAAE,EAAIA,CAAO,CAAC,EAAE,CAAE,YAClDH,EAAuB,oGAK3B,IAAMK,EAAMC,AAlEiC7C,EAkElBf,SAAS,CAAC2B,SAAS,CAACzB,IAAI,CAAC,IAAI,CAAE0B,EAAQ,CAC9DO,eAAgBN,EAAOM,cAAc,CACrCH,OAAQA,CACZ,GAEA,GAAI,CAAC2B,EACD,OAGJ,IAAME,EAAM,KAAK,CAAClC,UAAUzB,IAAI,CAAC,IAAI,CAAEyD,EAAK,CACxC3B,OAAQyB,CAAO,CAAC,EAAE,AACtB,GAEMK,EAAM,KAAK,CAACnC,UAAUzB,IAAI,CAAC,IAAI,CAAEyD,EAAK,CACxC3B,OAAQyB,CAAO,CAAC,EAAE,AACtB,GAEA,GAAI,CAACI,GAAO,CAACC,EACT,OAEJ,IAAMC,EAAgBN,CAAO,CAAC,EAAE,CAAGA,CAAO,CAAC,EAAE,CAC7C,IAAKrC,EAAI,EAAGA,EAAI0C,EAAI5B,KAAK,CAACM,MAAM,CAAEpB,IAC9BoC,EAAaL,EAAaU,EAAI3B,KAAK,CAACd,EAAI2C,EAAc,CAClDD,EAAI5B,KAAK,CAACd,EAAE,EAChBsC,EAAIhB,IAAI,CAAC,CAACoB,EAAI7B,KAAK,CAACb,EAAE,CAAEoC,EAAW,EACnCvB,EAAMS,IAAI,CAACoB,EAAI7B,KAAK,CAACb,EAAE,EACvBc,EAAMQ,IAAI,CAACc,GAEf,MAAO,CACHb,OAAQe,EACRzB,MAAOA,EACPC,MAAOA,CACX,CACJ,CACJ,CAuBAqB,EAAiBX,cAAc,CAAGS,EAAuBH,EAAaN,cAAc,CAAE,CAOlFf,OAAQ,CACJgB,MAAO,KAAK,EAMZV,eAAgB,SAMhBH,OAAQ,EAORyB,QAAS,CAAC,EAAG,GAAG,AACpB,CACJ,GACAL,EAAwBG,EAAiBvD,SAAS,CAAE,CAChD+C,SAAU,cACVD,eAAgB,CAAC,UAAU,AAC/B,GACAtC,IAA0IwC,kBAAkB,CAAC,UAAWO,GAiC3I,IAAMnD,EAAgBE,IAGzC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}