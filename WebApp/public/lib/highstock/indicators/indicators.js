!/**
 * Highstock JS v12.3.0 (2025-06-21)
 * @module highcharts/indicators/indicators
 * @requires highcharts
 * @requires highcharts/modules/stock
 *
 * Indicator series type for Highcharts Stock
 *
 * (c) 2010-2025 <PERSON><PERSON><PERSON>, <PERSON>
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.Chart,t._Highcharts.SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/indicators/indicators",["highcharts/highcharts"],function(t){return e(t,t.Chart,t.SeriesRegistry)}):"object"==typeof exports?exports["highcharts/indicators/indicators"]=e(t._Highcharts,t._Highcharts.Chart,t._Highcharts.SeriesRegistry):t.Highcharts=e(t.Highcharts,t.Highcharts.Chart,t.Highcharts.SeriesRegistry)}("undefined"==typeof window?this:window,(t,e,a)=>(()=>{"use strict";var i,s={512:t=>{t.exports=a},944:e=>{e.exports=t},960:t=>{t.exports=e}},n={};function r(t){var e=n[t];if(void 0!==e)return e.exports;var a=n[t]={exports:{}};return s[t](a,a.exports,r),a.exports}r.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return r.d(e,{a:e}),e},r.d=(t,e)=>{for(var a in e)r.o(e,a)&&!r.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:e[a]})},r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var o={};r.d(o,{default:()=>N});var l=r(944),h=r.n(l),p=r(960),d=r.n(p),u=r(512),c=r.n(u);let{line:f}=c().seriesTypes,{addEvent:m,fireEvent:x,error:y,extend:g,isArray:D,merge:P,pick:C}=h(),v=(t,e)=>{let a=[],i=t.pointArrayMap,s=e&&t.dataTable.modified||t.dataTable;if(!i)return t.getColumn("y",e);let n=i.map(a=>t.getColumn(a,e));for(let t=0;t<s.rowCount;t++){let e=i.map((e,a)=>n[a]?.[t]||0);a.push(e)}return a};class A extends f{destroy(){this.dataEventsToUnbind.forEach(function(t){t()}),super.destroy.apply(this,arguments)}getName(){let t=[],e=this.name;return e||((this.nameComponents||[]).forEach(function(e,a){t.push(this.options.params[e]+C(this.nameSuffixes[a],""))},this),e=(this.nameBase||this.type.toUpperCase())+(this.nameComponents?" ("+t.join(", ")+")":"")),e}getValues(t,e){let a=e.period,i=t.xData||[],s=t.yData,n=s.length,r=[],o=[],l=[],h,p=-1,d=0,u,c=0;if(!(i.length<a)){for(D(s[0])&&(p=e.index?e.index:0);d<a-1;)c+=p<0?s[d]:s[d][p],d++;for(h=d;h<n;h++)c+=p<0?s[h]:s[h][p],u=[i[h],c/a],r.push(u),o.push(u[0]),l.push(u[1]),c-=p<0?s[h-d]:s[h-d][p];return{values:r,xData:o,yData:l}}}init(t,e){let a=this;super.init.call(a,t,e);let i=m(d(),"afterLinkSeries",function({isUpdating:e}){if(e)return;let i=!!a.dataEventsToUnbind.length;if(!a.linkedParent)return y("Series "+a.options.linkedTo+" not found! Check `linkedTo`.",!1,t);if(!i&&(a.dataEventsToUnbind.push(m(a.linkedParent,"updatedData",function(){a.recalculateValues()})),a.calculateOn.xAxis&&a.dataEventsToUnbind.push(m(a.linkedParent.xAxis,a.calculateOn.xAxis,function(){a.recalculateValues()}))),"init"===a.calculateOn.chart)a.closestPointRange||a.recalculateValues();else if(!i){let t=m(a.chart,a.calculateOn.chart,function(){a.recalculateValues(),t()})}},{order:0});a.dataEventsToUnbind=[],a.eventsToUnbind.push(i)}recalculateValues(){let t=[],e=this.dataTable,a=this.points||[],i=this.dataTable.rowCount,s=!0,n,r,o,l,h=this.linkedParent.yData,p=this.linkedParent.processedYData;this.linkedParent.xData=this.linkedParent.getColumn("x"),this.linkedParent.yData=v(this.linkedParent),this.linkedParent.processedYData=v(this.linkedParent,!0);let d=this.linkedParent.options&&this.linkedParent.dataTable.rowCount&&this.getValues(this.linkedParent,this.options.params)||{values:[],xData:[],yData:[]};delete this.linkedParent.xData,this.linkedParent.yData=h,this.linkedParent.processedYData=p;let u=this.pointArrayMap||["y"],c={};if(d.yData.forEach(t=>{u.forEach((e,a)=>{let i=c[e]||[];i.push(D(t)?t[a]:t),c[e]||(c[e]=i)})}),i&&!this.hasGroupedData&&this.visible&&this.points)if(this.cropped){this.xAxis&&(o=this.xAxis.min,l=this.xAxis.max);let i=this.cropData(e,o,l),s=["x",...this.pointArrayMap||["y"]];for(let e=0;e<(i.modified?.rowCount||0);e++){let a=s.map(t=>this.getColumn(t)[e]||0);t.push(a)}let h=this.getColumn("x");n=d.xData.indexOf(h[0]),r=d.xData.indexOf(h[h.length-1]),-1===n&&r===d.xData.length-2&&t[0][0]===a[0].x&&t.shift(),this.updateData(t)}else(this.updateAllPoints||d.xData.length!==i-1&&d.xData.length!==i+1)&&(s=!1,this.updateData(d.values));s&&(e.setColumns({...c,x:d.xData}),this.options.data=d.values),this.calculateOn.xAxis&&this.getColumn("x",!0).length&&(this.isDirty=!0,this.redraw()),this.isDirtyData=!!this.linkedSeries.length,x(this,"updatedData")}processData(){let t=this.options.compareToMain,e=this.linkedParent;super.processData.apply(this,arguments),this.dataModify&&e&&e.dataModify&&e.dataModify.compareValue&&t&&(this.dataModify.compareValue=e.dataModify.compareValue)}}A.defaultOptions=P(f.defaultOptions,{name:void 0,tooltip:{valueDecimals:4},linkedTo:void 0,compareToMain:!1,params:{index:3,period:14}}),g(A.prototype,{calculateOn:{chart:"init"},hasDerivedData:!0,nameComponents:["period"],nameSuffixes:[],useCommonDataGrouping:!0}),c().registerSeriesType("sma",A);let{sma:k}=c().seriesTypes,{correctFloat:T,isArray:M,merge:O}=h();class b extends k{accumulatePeriodPoints(t,e,a){let i=0,s=0,n=0;for(;s<t;)i+=e<0?a[s]:a[s][e],s++;return i}calculateEma(t,e,a,i,s,n,r){let o=t[a-1],l=n<0?e[a-1]:e[a-1][n];return[o,void 0===s?r:T(l*i+s*(1-i))]}getValues(t,e){let a=e.period,i=t.xData,s=t.yData,n=s?s.length:0,r=2/(a+1),o=[],l=[],h=[],p,d,u,c=-1,f=0,m=0;if(!(n<a)){for(M(s[0])&&(c=e.index?e.index:0),m=this.accumulatePeriodPoints(a,c,s)/a,u=a;u<n+1;u++)d=this.calculateEma(i,s,u,r,p,c,m),o.push(d),l.push(d[0]),h.push(d[1]),p=d[1];return{values:o,xData:l,yData:h}}}}b.defaultOptions=O(k.defaultOptions,{params:{index:3,period:9}}),c().registerSeriesType("ema",b);let{sma:{prototype:E}}=c().seriesTypes,{defined:w,error:S,merge:V}=h();!function(t){let e=["bottomLine"],a=["top","bottom"],i=["top"];function s(t){return"plot"+t.charAt(0).toUpperCase()+t.slice(1)}function n(t,e){let a=[];return(t.pointArrayMap||[]).forEach(t=>{t!==e&&a.push(s(t))}),a}function r(){let t=this,e=t.pointValKey,a=t.linesApiNames,i=t.areaLinesNames,r=t.points,o=t.options,l=t.graph,h={options:{gapSize:o.gapSize}},p=[],d=n(t,e),u=r.length,c;if(d.forEach((t,e)=>{for(p[e]=[];u--;)c=r[u],p[e].push({x:c.x,plotX:c.plotX,plotY:c[t],isNull:!w(c[t])});u=r.length}),t.userOptions.fillColor&&i.length){let e=p[d.indexOf(s(i[0]))],a=1===i.length?r:p[d.indexOf(s(i[1]))],n=t.color;t.points=a,t.nextPoints=e,t.color=t.userOptions.fillColor,t.options=V(r,h),t.graph=t.area,t.fillGraph=!0,E.drawGraph.call(t),t.area=t.graph,delete t.nextPoints,delete t.fillGraph,t.color=n}a.forEach((e,a)=>{p[a]?(t.points=p[a],o[e]?t.options=V(o[e].styles,h):S('Error: "There is no '+e+' in DOCS options declared. Check if linesApiNames are consistent with your DOCS line names."'),t.graph=t["graph"+e],E.drawGraph.call(t),t["graph"+e]=t.graph):S('Error: "'+e+" doesn't have equivalent in pointArrayMap. To many elements in linesApiNames relative to pointArrayMap.\"")}),t.points=r,t.options=o,t.graph=l,E.drawGraph.call(t)}function o(t){let e,a=[],i=[];if(t=t||this.points,this.fillGraph&&this.nextPoints){if((e=E.getGraphPath.call(this,this.nextPoints))&&e.length){e[0][0]="L",a=E.getGraphPath.call(this,t),i=e.slice(0,a.length);for(let t=i.length-1;t>=0;t--)a.push(i[t])}}else a=E.getGraphPath.apply(this,arguments);return a}function l(t){let e=[];return(this.pointArrayMap||[]).forEach(a=>{e.push(t[a])}),e}function h(){let t=this.pointArrayMap,e=[],a;e=n(this),E.translate.apply(this,arguments),this.points.forEach(i=>{t.forEach((t,s)=>{a=i[t],this.dataModify&&(a=this.dataModify.modifyValue(a)),null!==a&&(i[e[s]]=this.yAxis.toPixels(a,!0))})})}t.compose=function(t){let s=t.prototype;return s.linesApiNames=s.linesApiNames||e.slice(),s.pointArrayMap=s.pointArrayMap||a.slice(),s.pointValKey=s.pointValKey||"top",s.areaLinesNames=s.areaLinesNames||i.slice(),s.drawGraph=r,s.getGraphPath=o,s.toYData=l,s.translate=h,t}}(i||(i={}));let G=i,H=h();H.MultipleLinesComposition=H.MultipleLinesComposition||G;let N=h();return o.default})());