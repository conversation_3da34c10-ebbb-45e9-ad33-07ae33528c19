{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/wma\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Kacper Madej\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/wma\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/wma\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ wma_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/WMA/WMAIndicator.js\n/* *\n *\n *  (c) 2010-2025 Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { isArray, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n// Utils:\n/**\n * @private\n */\nfunction accumulateAverage(points, xVal, yVal, i, index) {\n    const xValue = xVal[i], yValue = index < 0 ? yVal[i] : yVal[i][index];\n    points.push([xValue, yValue]);\n}\n/**\n * @private\n */\nfunction weightedSumArray(array, pLen) {\n    // The denominator is the sum of the number of days as a triangular number.\n    // If there are 5 days, the triangular numbers are 5, 4, 3, 2, and 1.\n    // The sum is 5 + 4 + 3 + 2 + 1 = 15.\n    const denominator = (pLen + 1) / 2 * pLen;\n    // Reduce VS loop => reduce\n    return array.reduce(function (prev, cur, i) {\n        return [null, prev[1] + cur[1] * (i + 1)];\n    })[1] / denominator;\n}\n/**\n * @private\n */\nfunction populateAverage(points, xVal, yVal, i) {\n    const pLen = points.length, wmaY = weightedSumArray(points, pLen), wmaX = xVal[i - 1];\n    points.shift(); // Remove point until range < period\n    return [wmaX, wmaY];\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The SMA series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.wma\n *\n * @augments Highcharts.Series\n */\nclass WMAIndicator extends SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, xValue = xVal[0], wma = [], xData = [], yData = [];\n        let range = 1, index = -1, i, wmaPoint, yValue = yVal[0];\n        if (xVal.length < period) {\n            return;\n        }\n        // Switch index for OHLC / Candlestick\n        if (isArray(yVal[0])) {\n            index = params.index;\n            yValue = yVal[0][index];\n        }\n        // Starting point\n        const points = [[xValue, yValue]];\n        // Accumulate first N-points\n        while (range !== period) {\n            accumulateAverage(points, xVal, yVal, range, index);\n            range++;\n        }\n        // Calculate value one-by-one for each period in visible data\n        for (i = range; i < yValLen; i++) {\n            wmaPoint = populateAverage(points, xVal, yVal, i);\n            wma.push(wmaPoint);\n            xData.push(wmaPoint[0]);\n            yData.push(wmaPoint[1]);\n            accumulateAverage(points, xVal, yVal, i, index);\n        }\n        wmaPoint = populateAverage(points, xVal, yVal, i);\n        wma.push(wmaPoint);\n        xData.push(wmaPoint[0]);\n        yData.push(wmaPoint[1]);\n        return {\n            values: wma,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Weighted moving average indicator (WMA). This series requires `linkedTo`\n * option to be set.\n *\n * @sample stock/indicators/wma\n *         Weighted moving average indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/wma\n * @optionparent plotOptions.wma\n */\nWMAIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    params: {\n        index: 3,\n        period: 9\n    }\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('wma', WMAIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const WMA_WMAIndicator = ((/* unused pure expression or super */ null && (WMAIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `WMA` series. If the [type](#series.wma.type) option is not specified, it\n * is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.wma\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/wma\n * @apioption series.wma\n */\n''; // Adds doclet above to the transpiled file\n\n;// ./code/es-modules/masters/indicators/wma.js\n\n\n\n\n/* harmony default export */ const wma_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "wma_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "sma", "SMAIndicator", "seriesTypes", "isArray", "merge", "accumulateAverage", "points", "xVal", "yVal", "i", "index", "xValue", "yValue", "push", "populateAverage", "pLen", "length", "wmaY", "array", "reduce", "prev", "cur", "wmaX", "shift", "WMAIndicator", "getV<PERSON>ues", "series", "params", "period", "xData", "yData", "yValLen", "wma", "range", "wmaPoint", "values", "defaultOptions", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,4BAA6B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACrH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,4BAA4B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAEzGA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAajL,GAAM,CAAEE,IAAKC,CAAY,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAE/K,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAE,CAAIP,IAU5B,SAASQ,EAAkBC,CAAM,CAAEC,CAAI,CAAEC,CAAI,CAAEC,CAAC,CAAEC,CAAK,EACnD,IAAMC,EAASJ,CAAI,CAACE,EAAE,CAAEG,EAASF,EAAQ,EAAIF,CAAI,CAACC,EAAE,CAAGD,CAAI,CAACC,EAAE,CAACC,EAAM,CACrEJ,EAAOO,IAAI,CAAC,CAACF,EAAQC,EAAO,CAChC,CAiBA,SAASE,EAAgBR,CAAM,CAAEC,CAAI,CAAEC,CAAI,CAAEC,CAAC,EAC1C,IAAMM,EAAOT,EAAOU,MAAM,CAAEC,EARrBC,AAQ6CZ,EARvCa,MAAM,CAAC,SAAUC,CAAI,CAAEC,CAAG,CAAEZ,CAAC,EACtC,MAAO,CAAC,KAAMW,CAAI,CAAC,EAAE,CAAGC,CAAG,CAAC,EAAE,CAAIZ,CAAAA,EAAI,CAAA,EAAG,AAC7C,EAAE,CAAC,EAAE,CAJe,CAAA,AAACM,CAAAA,AAUuCA,EAVhC,CAAA,EAAK,EAU2BA,CAVpB,EAU2BO,EAAOf,CAAI,CAACE,EAAI,EAAE,CAErF,OADAH,EAAOiB,KAAK,GACL,CAACD,EAAML,EAAK,AACvB,CAeA,MAAMO,UAAqBvB,EAMvBwB,UAAUC,CAAM,CAAEC,CAAM,CAAE,CACtB,IAAMC,EAASD,EAAOC,MAAM,CAAErB,EAAOmB,EAAOG,KAAK,CAAErB,EAAOkB,EAAOI,KAAK,CAAEC,EAAUvB,EAAOA,EAAKQ,MAAM,CAAG,EAAGL,EAASJ,CAAI,CAAC,EAAE,CAAEyB,EAAM,EAAE,CAAEH,EAAQ,EAAE,CAAEC,EAAQ,EAAE,CACxJG,EAAQ,EAAGvB,EAAQ,GAAID,EAAGyB,EAAUtB,EAASJ,CAAI,CAAC,EAAE,CACxD,GAAID,EAAKS,MAAM,CAAGY,EACd,OAGAzB,EAAQK,CAAI,CAAC,EAAE,IACfE,EAAQiB,EAAOjB,KAAK,CACpBE,EAASJ,CAAI,CAAC,EAAE,CAACE,EAAM,EAG3B,IAAMJ,EAAS,CAAC,CAACK,EAAQC,EAAO,CAAC,CAEjC,KAAOqB,IAAUL,GACbvB,EAAkBC,EAAQC,EAAMC,EAAMyB,EAAOvB,GAC7CuB,IAGJ,IAAKxB,EAAIwB,EAAOxB,EAAIsB,EAAStB,IAEzBuB,EAAInB,IAAI,CADRqB,EAAWpB,EAAgBR,EAAQC,EAAMC,EAAMC,IAE/CoB,EAAMhB,IAAI,CAACqB,CAAQ,CAAC,EAAE,EACtBJ,EAAMjB,IAAI,CAACqB,CAAQ,CAAC,EAAE,EACtB7B,EAAkBC,EAAQC,EAAMC,EAAMC,EAAGC,GAM7C,OAHAsB,EAAInB,IAAI,CADRqB,EAAWpB,EAAgBR,EAAQC,EAAMC,EAAMC,IAE/CoB,EAAMhB,IAAI,CAACqB,CAAQ,CAAC,EAAE,EACtBJ,EAAMjB,IAAI,CAACqB,CAAQ,CAAC,EAAE,EACf,CACHC,OAAQH,EACRH,MAAOA,EACPC,MAAOA,CACX,CACJ,CACJ,CAoBAN,EAAaY,cAAc,CAAGhC,EAAMH,EAAamC,cAAc,CAAE,CAC7DT,OAAQ,CACJjB,MAAO,EACPkB,OAAQ,CACZ,CACJ,GACA7B,IAA0IsC,kBAAkB,CAAC,MAAOb,GA+BvI,IAAM7B,EAAYE,IAGrC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}