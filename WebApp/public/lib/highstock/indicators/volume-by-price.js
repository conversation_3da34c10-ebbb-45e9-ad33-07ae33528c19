!/**
 * Highstock JS v12.3.0 (2025-06-21)
 * @module highcharts/indicators/volume-by-price
 * @requires highcharts
 * @requires highcharts/modules/stock
 *
 * Indicator series type for Highcharts Stock
 *
 * (c) 2010-2025 Pawe<PERSON>
 *
 * License: www.highcharts.com/license
 */function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(e._Highcharts,e._Highcharts.SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/indicators/volume-by-price",["highcharts/highcharts"],function(e){return t(e,e.SeriesRegistry)}):"object"==typeof exports?exports["highcharts/indicators/volume-by-price"]=t(e._Highcharts,e._Highcharts.SeriesRegistry):e.Highcharts=t(e.Highcharts,e.Highcharts.SeriesRegistry)}("undefined"==typeof window?this:window,(e,t)=>(()=>{"use strict";var i={512:e=>{e.exports=t},944:t=>{t.exports=e}},s={};function o(e){var t=s[e];if(void 0!==t)return t.exports;var a=s[e]={exports:{}};return i[e](a,a.exports,o),a.exports}o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},o.d=(e,t)=>{for(var i in t)o.o(t,i)&&!o.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var a={};o.d(a,{default:()=>A});var n=o(944),r=o.n(n),l=o(512),p=o.n(l);let{sma:{prototype:{pointClass:h}}}=p().seriesTypes,d=class extends h{destroy(){this.negativeGraphic&&(this.negativeGraphic=this.negativeGraphic.destroy()),super.destroy.apply(this,arguments)}},{animObject:u}=r(),{noop:m}=r(),{column:{prototype:c},sma:g}=p().seriesTypes,{addEvent:v,arrayMax:f,arrayMin:y,correctFloat:x,defined:D,error:S,extend:V,isArray:b,merge:w}=r(),P=Math.abs;class C extends g{init(e,t){let i=this;delete t.data,super.init.apply(i,arguments);let s=v(this.chart.constructor,"afterLinkSeries",function(){if(i.options){let t=i.options.params,s=i.linkedParent,o=e.get(t.volumeSeriesID);i.addCustomEvents(s,o)}s()},{order:1});return i}addCustomEvents(e,t){let i=this,s=()=>{i.chart.redraw(),i.setData([]),i.zoneStarts=[],i.zoneLinesSVG&&(i.zoneLinesSVG=i.zoneLinesSVG.destroy())};return i.dataEventsToUnbind.push(v(e,"remove",function(){s()})),t&&i.dataEventsToUnbind.push(v(t,"remove",function(){s()})),i}animate(e){let t=this,i=t.chart.inverted,s=t.group,o={};if(!e&&s){let e=i?t.yAxis.top:t.xAxis.left;i?(s["forceAnimate:translateY"]=!0,o.translateY=e):(s["forceAnimate:translateX"]=!0,o.translateX=e),s.animate(o,V(u(t.options.animation),{step:function(e,i){t.group.attr({scaleX:Math.max(.001,i.pos)})}}))}}drawPoints(){this.options.volumeDivision.enabled&&(this.posNegVolume(!0,!0),c.drawPoints.apply(this,arguments),this.posNegVolume(!1,!1)),c.drawPoints.apply(this,arguments)}posNegVolume(e,t){let i=t?["positive","negative"]:["negative","positive"],s=this.options.volumeDivision,o=this.points.length,a=[],n=[],r=0,l,p,h,d;for(e?(this.posWidths=a,this.negWidths=n):(a=this.posWidths,n=this.negWidths);r<o;r++)(d=this.points[r])[i[0]+"Graphic"]=d.graphic,d.graphic=d[i[1]+"Graphic"],e&&(l=d.shapeArgs.width,(h=(p=this.priceZones[r]).wholeVolumeData)?(a.push(l/h*p.positiveVolumeData),n.push(l/h*p.negativeVolumeData)):(a.push(0),n.push(0))),d.color=t?s.styles.positiveColor:s.styles.negativeColor,d.shapeArgs.width=t?this.posWidths[r]:this.negWidths[r],d.shapeArgs.x=t?d.shapeArgs.x:this.posWidths[r]}translate(){let e=this,t=e.options,i=e.chart,s=e.yAxis,o=s.min,a=e.options.zoneLines,n=e.priceZones,r=0,l,p,h,d,u,m,g,v,y,D;c.translate.apply(e);let S=e.points;S.length&&(g=t.pointPadding<.5?t.pointPadding:.1,l=f(e.volumeDataArray),p=i.plotWidth/2,v=i.plotTop,h=P(s.toPixels(o)-s.toPixels(o+e.rangeStep)),u=P(s.toPixels(o)-s.toPixels(o+e.rangeStep)),g&&(d=P(h*(1-2*g)),r=P((h-d)/2),h=P(d)),S.forEach(function(t,i){y=t.barX=t.plotX=0,D=t.plotY=s.toPixels(n[i].start)-v-(s.reversed?h-u:h)-r,t.pointWidth=m=x(p*n[i].wholeVolumeData/l),t.shapeArgs=e.crispCol.apply(e,[y,D,m,h]),t.volumeNeg=n[i].negativeVolumeData,t.volumePos=n[i].positiveVolumeData,t.volumeAll=n[i].wholeVolumeData}),a.enabled&&e.drawZones(i,s,e.zoneStarts,a.styles))}getExtremes(){let e,t=this.options.compare,i=this.options.cumulative;return this.options.compare?(this.options.compare=void 0,e=super.getExtremes(),this.options.compare=t):this.options.cumulative?(this.options.cumulative=!1,e=super.getExtremes(),this.options.cumulative=i):e=super.getExtremes(),e}getValues(e,t){let i=e.getColumn("x",!0),s=e.processedYData,o=this.chart,a=t.ranges,n=[],r=[],l=[],p=o.get(t.volumeSeriesID);if(!e.chart)return void S("Base series not found! In case it has been removed, add a new one.",!0,o);if(!p||!p.getColumn("x",!0).length){let e=p&&!p.getColumn("x",!0).length?" does not contain any data.":" not found! Check `volumeSeriesID`.";S("Series "+t.volumeSeriesID+e,!0,o);return}let h=b(s[0]);return h&&4!==s[0].length?void S("Type of "+e.name+" series is different than line, OHLC or candlestick.",!0,o):((this.priceZones=this.specifyZones(h,i,s,a,p)).forEach(function(e,t){n.push([e.x,e.end]),r.push(n[t][0]),l.push(n[t][1])}),{values:n,xData:r,yData:l})}specifyZones(e,t,i,s,o){let a=!!e&&function(e){let t=e.length,i=e[0][3],s=i,o=1,a;for(;o<t;o++)(a=e[o][3])<i&&(i=a),a>s&&(s=a);return{min:i,max:s}}(i),n=this.zoneStarts=[],r=[],l=a?a.min:y(i),p=a?a.max:f(i),h=0,d=1,u=this.linkedParent;if(!this.options.compareToMain&&u.dataModify&&(l=u.dataModify.modifyValue(l),p=u.dataModify.modifyValue(p)),!D(l)||!D(p))return this.points.length&&(this.setData([]),this.zoneStarts=[],this.zoneLinesSVG&&(this.zoneLinesSVG=this.zoneLinesSVG.destroy())),[];let m=this.rangeStep=x(p-l)/s;for(n.push(l);h<s-1;h++)n.push(x(n[h]+m));n.push(p);let c=n.length;for(;d<c;d++)r.push({index:d-1,x:t[0],start:n[d-1],end:n[d]});return this.volumePerZone(e,r,o,t,i)}volumePerZone(e,t,i,s,o){let a,n,r,l,p,h=this,d=i.getColumn("x",!0),u=i.getColumn("y",!0),m=t.length-1,c=o.length,g=u.length;return P(c-g)&&(s[0]!==d[0]&&u.unshift(0),s[c-1]!==d[g-1]&&u.push(0)),h.volumeDataArray=[],t.forEach(function(t){for(p=0,t.wholeVolumeData=0,t.positiveVolumeData=0,t.negativeVolumeData=0;p<c;p++){n=!1,r=!1,l=e?o[p][3]:o[p],a=p?e?o[p-1][3]:o[p-1]:l;let i=h.linkedParent;!h.options.compareToMain&&i.dataModify&&(l=i.dataModify.modifyValue(l),a=i.dataModify.modifyValue(a)),l<=t.start&&0===t.index&&(n=!0),l>=t.end&&t.index===m&&(r=!0),(l>t.start||n)&&(l<t.end||r)&&(t.wholeVolumeData+=u[p],a>l?t.negativeVolumeData+=u[p]:t.positiveVolumeData+=u[p])}h.volumeDataArray.push(t.wholeVolumeData)}),t}drawZones(e,t,i,s){let o=e.renderer,a=e.plotWidth,n=e.plotTop,r=this.zoneLinesSVG,l=[],p;i.forEach(function(i){p=t.toPixels(i)-n,l=l.concat(e.renderer.crispLine([["M",0,p],["L",a,p]],s.lineWidth))}),r?r.animate({d:l}):r=this.zoneLinesSVG=o.path(l).attr({"stroke-width":s.lineWidth,stroke:s.color,dashstyle:s.dashStyle,zIndex:this.group.zIndex+.1}).add(this.group)}}C.defaultOptions=w(g.defaultOptions,{params:{index:void 0,period:void 0,ranges:12,volumeSeriesID:"volume"},zoneLines:{enabled:!0,styles:{color:"#0A9AC9",dashStyle:"LongDash",lineWidth:1}},volumeDivision:{enabled:!0,styles:{positiveColor:"rgba(144, 237, 125, 0.8)",negativeColor:"rgba(244, 91, 91, 0.8)"}},animationLimit:1e3,enableMouseTracking:!1,pointPadding:0,zIndex:-1,crisp:!0,dataGrouping:{enabled:!1},dataLabels:{align:"left",allowOverlap:!0,enabled:!0,format:"P: {point.volumePos:.2f} | N: {point.volumeNeg:.2f}",padding:0,style:{fontSize:"0.5em"},verticalAlign:"top"}}),V(C.prototype,{nameBase:"Volume by Price",nameComponents:["ranges"],calculateOn:{chart:"render",xAxis:"afterSetExtremes"},pointClass:d,markerAttribs:m,drawGraph:m,getColumnMetrics:c.getColumnMetrics,crispCol:c.crispCol}),p().registerSeriesType("vbp",C);let A=r();return a.default})());