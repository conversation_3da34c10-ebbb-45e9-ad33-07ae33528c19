{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/obv\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 <PERSON><PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/obv\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/obv\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ obv_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/OBV/OBVIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { isNumber, error, extend, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The OBV series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.obv\n *\n * @augments Highcharts.Series\n */\nclass OBVIndicator extends SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const volumeSeries = series.chart.get(params.volumeSeriesID), xVal = series.xData, yVal = series.yData, OBV = [], xData = [], yData = [], hasOHLC = !isNumber(yVal[0]);\n        let OBVPoint = [], i = 1, previousOBV = 0, curentOBV = 0, previousClose = 0, curentClose = 0, volume;\n        // Checks if volume series exists.\n        if (volumeSeries) {\n            volume = volumeSeries.getColumn('y');\n            // Add first point and get close value.\n            OBVPoint = [xVal[0], previousOBV];\n            previousClose = hasOHLC ?\n                yVal[0][3] : yVal[0];\n            OBV.push(OBVPoint);\n            xData.push(xVal[0]);\n            yData.push(OBVPoint[1]);\n            for (i; i < yVal.length; i++) {\n                curentClose = hasOHLC ?\n                    yVal[i][3] : yVal[i];\n                if (curentClose > previousClose) { // Up\n                    curentOBV = previousOBV + volume[i];\n                }\n                else if (curentClose === previousClose) { // Constant\n                    curentOBV = previousOBV;\n                }\n                else { // Down\n                    curentOBV = previousOBV - volume[i];\n                }\n                // Add point.\n                OBVPoint = [xVal[i], curentOBV];\n                // Assign current as previous for next iteration.\n                previousOBV = curentOBV;\n                previousClose = curentClose;\n                OBV.push(OBVPoint);\n                xData.push(xVal[i]);\n                yData.push(OBVPoint[1]);\n            }\n        }\n        else {\n            error('Series ' +\n                params.volumeSeriesID +\n                ' not found! Check `volumeSeriesID`.', true, series.chart);\n            return;\n        }\n        return {\n            values: OBV,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * On-Balance Volume (OBV) technical indicator. This series\n * requires the `linkedTo` option to be set and should be loaded after\n * the `stock/indicators/indicators.js` file. Through the `volumeSeriesID`\n * there also should be linked the volume series.\n *\n * @sample stock/indicators/obv\n *         OBV indicator\n *\n * @extends      plotOptions.sma\n * @since 9.1.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/obv\n * @excluding    allAreas, colorAxis, joinBy, keys, navigatorOptions,\n *               pointInterval, pointIntervalUnit, pointPlacement,\n *               pointRange, pointStart, showInNavigator, stacking\n * @optionparent plotOptions.obv\n */\nOBVIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    marker: {\n        enabled: false\n    },\n    /**\n     * @excluding index, period\n     */\n    params: {\n        // Index and period are unchangeable, do not inherit (#15362)\n        index: void 0,\n        period: void 0,\n        /**\n         * The id of another series to use its data as volume data for the\n         * indicator calculation.\n         */\n        volumeSeriesID: 'volume'\n    },\n    tooltip: {\n        valueDecimals: 0\n    }\n});\nextend(OBVIndicator.prototype, {\n    nameComponents: void 0\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('obv', OBVIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const OBV_OBVIndicator = ((/* unused pure expression or super */ null && (OBVIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `OBV` series. If the [type](#series.obv.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.obv\n * @since 9.1.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/obv\n * @apioption series.obv\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/masters/indicators/obv.js\n\n\n\n\n/* harmony default export */ const obv_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "obv_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "sma", "SMAIndicator", "seriesTypes", "isNumber", "error", "extend", "merge", "OBVIndicator", "getV<PERSON>ues", "series", "params", "volumeSeries", "chart", "volumeSeriesID", "xVal", "xData", "yVal", "yData", "OBV", "hasOHLC", "OBVPoint", "i", "previousOBV", "curentOBV", "previousClose", "curentClose", "volume", "getColumn", "push", "length", "values", "defaultOptions", "marker", "enabled", "index", "period", "tooltip", "valueDecimals", "nameComponents", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,4BAA6B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACrH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,4BAA4B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAEzGA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAWjL,GAAM,CAAEE,IAAKC,CAAY,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAE/K,CAAEC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAE,CAAIT,GAe5C,OAAMU,UAAqBN,EAMvBO,UAAUC,CAAM,CAAEC,CAAM,CAAE,CACtB,IAAMC,EAAeF,EAAOG,KAAK,CAACxB,GAAG,CAACsB,EAAOG,cAAc,EAAGC,EAAOL,EAAOM,KAAK,CAAEC,EAAOP,EAAOQ,KAAK,CAAEC,EAAM,EAAE,CAAEH,EAAQ,EAAE,CAAEE,EAAQ,EAAE,CAAEE,EAAU,CAAChB,EAASa,CAAI,CAAC,EAAE,EACjKI,EAAW,EAAE,CAAEC,EAAI,EAAGC,EAAc,EAAGC,EAAY,EAAGC,EAAgB,EAAGC,EAAc,EAAGC,EAE9F,IAAIf,EA+BC,YACDP,EAAM,UACFM,EAAOG,cAAc,CACrB,sCAAuC,CAAA,EAAMJ,EAAOG,KAAK,EAzB7D,IARAc,EAASf,EAAagB,SAAS,CAAC,KAEhCP,EAAW,CAACN,CAAI,CAAC,EAAE,CAAEQ,EAAY,CACjCE,EAAgBL,EACZH,CAAI,CAAC,EAAE,CAAC,EAAE,CAAGA,CAAI,CAAC,EAAE,CACxBE,EAAIU,IAAI,CAACR,GACTL,EAAMa,IAAI,CAACd,CAAI,CAAC,EAAE,EAClBG,EAAMW,IAAI,CAACR,CAAQ,CAAC,EAAE,EACdC,EAAIL,EAAKa,MAAM,CAAER,IAIjBE,EADAE,AAFJA,CAAAA,EAAcN,EACVH,CAAI,CAACK,EAAE,CAAC,EAAE,CAAGL,CAAI,CAACK,EAAE,AAAD,EACLG,EACFF,EAAcI,CAAM,CAACL,EAAE,CAE9BI,IAAgBD,EACTF,EAGAA,EAAcI,CAAM,CAACL,EAAE,CAGvCD,EAAW,CAACN,CAAI,CAACO,EAAE,CAAEE,EAAU,CAE/BD,EAAcC,EACdC,EAAgBC,EAChBP,EAAIU,IAAI,CAACR,GACTL,EAAMa,IAAI,CAACd,CAAI,CAACO,EAAE,EAClBJ,EAAMW,IAAI,CAACR,CAAQ,CAAC,EAAE,EAS9B,MAAO,CACHU,OAAQZ,EACRH,MAAOA,EACPE,MAAOA,CACX,CACJ,CACJ,CAyBAV,EAAawB,cAAc,CAAGzB,EAAML,EAAa8B,cAAc,CAAE,CAC7DC,OAAQ,CACJC,QAAS,CAAA,CACb,EAIAvB,OAAQ,CAEJwB,MAAO,KAAK,EACZC,OAAQ,KAAK,EAKbtB,eAAgB,QACpB,EACAuB,QAAS,CACLC,cAAe,CACnB,CACJ,GACAhC,EAAOE,EAAahB,SAAS,CAAE,CAC3B+C,eAAgB,KAAK,CACzB,GACAvC,IAA0IwC,kBAAkB,CAAC,MAAOhC,GA+BvI,IAAMZ,EAAYE,IAGrC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}