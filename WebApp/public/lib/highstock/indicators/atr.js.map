{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/atr\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Sebastian <PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/atr\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/atr\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ atr_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/ATR/ATRIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { isArray, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n// Utils:\n/**\n * @private\n */\nfunction accumulateAverage(points, xVal, yVal, i) {\n    const xValue = xVal[i], yValue = yVal[i];\n    points.push([xValue, yValue]);\n}\n/**\n * @private\n */\nfunction getTR(currentPoint, prevPoint) {\n    const pointY = currentPoint, prevY = prevPoint, HL = pointY[1] - pointY[2], HCp = typeof prevY === 'undefined' ? 0 : Math.abs(pointY[1] - prevY[3]), LCp = typeof prevY === 'undefined' ? 0 : Math.abs(pointY[2] - prevY[3]), TR = Math.max(HL, HCp, LCp);\n    return TR;\n}\n/**\n * @private\n */\nfunction populateAverage(points, xVal, yVal, i, period, prevATR) {\n    const x = xVal[i - 1], TR = getTR(yVal[i - 1], yVal[i - 2]), y = (((prevATR * (period - 1)) + TR) / period);\n    return [x, y];\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The ATR series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.atr\n *\n * @augments Highcharts.Series\n */\nclass ATRIndicator extends SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, xValue = xVal[0], yValue = yVal[0], points = [[xValue, yValue]], ATR = [], xData = [], yData = [];\n        let point, i, prevATR = 0, range = 1, TR = 0;\n        if ((xVal.length <= period) ||\n            !isArray(yVal[0]) ||\n            yVal[0].length !== 4) {\n            return;\n        }\n        for (i = 1; i <= yValLen; i++) {\n            accumulateAverage(points, xVal, yVal, i);\n            if (period < range) {\n                point = populateAverage(points, xVal, yVal, i, period, prevATR);\n                prevATR = point[1];\n                ATR.push(point);\n                xData.push(point[0]);\n                yData.push(point[1]);\n            }\n            else if (period === range) {\n                prevATR = TR / (i - 1);\n                ATR.push([xVal[i - 1], prevATR]);\n                xData.push(xVal[i - 1]);\n                yData.push(prevATR);\n                range++;\n            }\n            else {\n                TR += getTR(yVal[i - 1], yVal[i - 2]);\n                range++;\n            }\n        }\n        return {\n            values: ATR,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Average true range indicator (ATR). This series requires `linkedTo`\n * option to be set.\n *\n * @sample stock/indicators/atr\n *         ATR indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/atr\n * @optionparent plotOptions.atr\n */\nATRIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    /**\n     * @excluding index\n     */\n    params: {\n        index: void 0 // Unused index, do not inherit (#15362)\n    }\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('atr', ATRIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const ATR_ATRIndicator = ((/* unused pure expression or super */ null && (ATRIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `ATR` series. If the [type](#series.atr.type) option is not specified, it\n * is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.atr\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/atr\n * @apioption series.atr\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/masters/indicators/atr.js\n\n\n\n\n/* harmony default export */ const atr_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "atr_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "sma", "SMAIndicator", "seriesTypes", "isArray", "merge", "getTR", "currentPoint", "prevPoint", "HL", "pointY", "Math", "max", "abs", "prevY", "ATRIndicator", "getV<PERSON>ues", "series", "params", "period", "xVal", "xData", "yVal", "yData", "yValLen", "length", "points", "ATR", "point", "i", "prevATR", "range", "TR", "xValue", "yValue", "push", "values", "defaultOptions", "index", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,4BAA6B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACrH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,4BAA4B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAEzGA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAWjL,GAAM,CAAEE,IAAKC,CAAY,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAE/K,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAE,CAAIP,IAiB5B,SAASQ,EAAMC,CAAY,CAAEC,CAAS,EAClC,IAAgDC,EAAKC,AAAtCH,CAA4C,CAAC,EAAE,CAAGG,AAAlDH,CAAwD,CAAC,EAAE,CAC1E,OADmOI,KAAKC,GAAG,CAACH,EAA1J,AAAiB,KAAA,IAA9DD,EAA4E,EAAIG,KAAKE,GAAG,CAACH,AAA/GH,CAAqH,CAAC,EAAE,CAAGO,AAArGN,CAA0G,CAAC,EAAE,EAAS,AAAiB,KAAA,IAAvIA,EAAqJ,EAAIG,KAAKE,GAAG,CAACH,AAAxLH,CAA8L,CAAC,EAAE,CAAGO,AAA9KN,CAAmL,CAAC,EAAE,EAE/N,CAsBA,MAAMO,UAAqBb,EAMvBc,UAAUC,CAAM,CAAEC,CAAM,CAAE,CACtB,IAAMC,EAASD,EAAOC,MAAM,CAAEC,EAAOH,EAAOI,KAAK,CAAEC,EAAOL,EAAOM,KAAK,CAAEC,EAAUF,EAAOA,EAAKG,MAAM,CAAG,EAAuCC,EAAS,CAAC,CAArCN,CAAI,CAAC,EAAE,CAAWE,CAAI,CAAC,EAAE,CAA4B,CAAC,CAAEK,EAAM,EAAE,CAAEN,EAAQ,EAAE,CAAEE,EAAQ,EAAE,CACvMK,EAAOC,EAAGC,EAAU,EAAGC,EAAQ,EAAGC,EAAK,EAC3C,GAAI,CAACZ,CAAAA,EAAKK,MAAM,EAAIN,CAAK,GACpBf,EAAQkB,CAAI,CAAC,EAAE,GAChBA,AAAmB,IAAnBA,CAAI,CAAC,EAAE,CAACG,MAAM,EAGlB,IAAKI,EAAI,EAAGA,GAAKL,EAASK,IAAK,KAhCdH,EAAQN,EAAME,EAAMO,EAAGV,EAAQW,EAdTD,EA+CGA,EA9C9C,IAAMI,EAASb,AA8CmBA,CA9Cf,CAACS,EAAE,CAAEK,EAASZ,AA8COA,CA9CH,CAACO,EAAE,CACxCH,AA6C0BA,EA7CnBS,IAAI,CAAC,CAACF,EAAQC,EAAO,EA8ChBf,EAASY,GAETD,EAAUF,CApCDF,EAmCeA,EAnCPN,EAmCeA,EAnCTE,EAmCeA,EAnCTO,EAmCeA,EAnCZV,EAmCeA,EAnCPW,EAmCeA,EAAvDF,EAjCL,CADGR,CAAI,CAACS,EAAI,EAAE,CAA6C,AAAC,CAAA,AAACC,EAAWX,CAAAA,EAAS,CAAA,EAA5Db,EAAMgB,CAAI,CAACO,EAAI,EAAE,CAAEP,CAAI,CAACO,EAAI,EAAE,CAAqC,EAAKV,EACvF,CAkCc,CAAC,EAAE,CAClBQ,EAAIQ,IAAI,CAACP,GACTP,EAAMc,IAAI,CAACP,CAAK,CAAC,EAAE,EACnBL,EAAMY,IAAI,CAACP,CAAK,CAAC,EAAE,IAEdT,IAAWY,GAChBD,EAAUE,EAAMH,CAAAA,EAAI,CAAA,EACpBF,EAAIQ,IAAI,CAAC,CAACf,CAAI,CAACS,EAAI,EAAE,CAAEC,EAAQ,EAC/BT,EAAMc,IAAI,CAACf,CAAI,CAACS,EAAI,EAAE,EACtBN,EAAMY,IAAI,CAACL,IAIXE,GAAM1B,EAAMgB,CAAI,CAACO,EAAI,EAAE,CAAEP,CAAI,CAACO,EAAI,EAAE,EACpCE,IAER,CACA,MAAO,CACHK,OAAQT,EACRN,MAAOA,EACPE,MAAOA,CACX,EACJ,CACJ,CAoBAR,EAAasB,cAAc,CAAGhC,EAAMH,EAAamC,cAAc,CAAE,CAI7DnB,OAAQ,CACJoB,MAAO,KAAK,CAChB,CACJ,GACAtC,IAA0IuC,kBAAkB,CAAC,MAAOxB,GA+BvI,IAAMnB,EAAYE,IAGrC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}