!/**
 * Highstock JS v12.3.0 (2025-06-21)
 * @module highcharts/indicators/indicators-all
 * @requires highcharts
 * @requires highcharts/modules/stock
 *
 * All technical indicators for Highcharts Stock
 *
 * (c) 2010-2025 Pawel Fus
 *
 * License: www.highcharts.com/license
 */function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(e._Highcharts,e._Highcharts.Chart,e._Highcharts.SeriesRegistry,e._Highcharts.dataGrouping.approximations,e._Highcharts.Color):"function"==typeof define&&define.amd?define("highcharts/indicators/indicators-all",["highcharts/highcharts"],function(e){return t(e,e.Chart,e.SeriesRegistry,e.dataGrouping,["approximations"],e.Color)}):"object"==typeof exports?exports["highcharts/indicators/indicators-all"]=t(e._Highcharts,e._Highcharts.Chart,e._Highcharts.SeriesRegistry,e._Highcharts.dataGrouping.approximations,e._Highcharts.Color):e.Highcharts=t(e.Highcharts,e.Highcharts.Chart,e.Highcharts.SeriesRegistry,e.Highcharts.dataGrouping.approximations,e.Highcharts.Color)}("undefined"==typeof window?this:window,(e,t,s,a,o)=>(()=>{"use strict";var i,r={512:e=>{e.exports=s},620:e=>{e.exports=o},944:t=>{t.exports=e},956:e=>{e.exports=a},960:e=>{e.exports=t}},n={};function l(e){var t=n[e];if(void 0!==t)return t.exports;var s=n[e]={exports:{}};return r[e](s,s.exports,l),s.exports}l.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return l.d(t,{a:t}),t},l.d=(e,t)=>{for(var s in t)l.o(t,s)&&!l.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},l.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var p={};l.d(p,{default:()=>a7});var u=l(944),h=l.n(u),d=l(960),c=l.n(d),m=l(512),g=l.n(m);let{line:y}=g().seriesTypes,{addEvent:f,fireEvent:x,error:D,extend:v,isArray:S,merge:A,pick:b}=h(),C=(e,t)=>{let s=[],a=e.pointArrayMap,o=t&&e.dataTable.modified||e.dataTable;if(!a)return e.getColumn("y",t);let i=a.map(s=>e.getColumn(s,t));for(let e=0;e<o.rowCount;e++){let t=a.map((t,s)=>i[s]?.[e]||0);s.push(t)}return s};class T extends y{destroy(){this.dataEventsToUnbind.forEach(function(e){e()}),super.destroy.apply(this,arguments)}getName(){let e=[],t=this.name;return t||((this.nameComponents||[]).forEach(function(t,s){e.push(this.options.params[t]+b(this.nameSuffixes[s],""))},this),t=(this.nameBase||this.type.toUpperCase())+(this.nameComponents?" ("+e.join(", ")+")":"")),t}getValues(e,t){let s=t.period,a=e.xData||[],o=e.yData,i=o.length,r=[],n=[],l=[],p,u=-1,h=0,d,c=0;if(!(a.length<s)){for(S(o[0])&&(u=t.index?t.index:0);h<s-1;)c+=u<0?o[h]:o[h][u],h++;for(p=h;p<i;p++)c+=u<0?o[p]:o[p][u],d=[a[p],c/s],r.push(d),n.push(d[0]),l.push(d[1]),c-=u<0?o[p-h]:o[p-h][u];return{values:r,xData:n,yData:l}}}init(e,t){let s=this;super.init.call(s,e,t);let a=f(c(),"afterLinkSeries",function({isUpdating:t}){if(t)return;let a=!!s.dataEventsToUnbind.length;if(!s.linkedParent)return D("Series "+s.options.linkedTo+" not found! Check `linkedTo`.",!1,e);if(!a&&(s.dataEventsToUnbind.push(f(s.linkedParent,"updatedData",function(){s.recalculateValues()})),s.calculateOn.xAxis&&s.dataEventsToUnbind.push(f(s.linkedParent.xAxis,s.calculateOn.xAxis,function(){s.recalculateValues()}))),"init"===s.calculateOn.chart)s.closestPointRange||s.recalculateValues();else if(!a){let e=f(s.chart,s.calculateOn.chart,function(){s.recalculateValues(),e()})}},{order:0});s.dataEventsToUnbind=[],s.eventsToUnbind.push(a)}recalculateValues(){let e=[],t=this.dataTable,s=this.points||[],a=this.dataTable.rowCount,o=!0,i,r,n,l,p=this.linkedParent.yData,u=this.linkedParent.processedYData;this.linkedParent.xData=this.linkedParent.getColumn("x"),this.linkedParent.yData=C(this.linkedParent),this.linkedParent.processedYData=C(this.linkedParent,!0);let h=this.linkedParent.options&&this.linkedParent.dataTable.rowCount&&this.getValues(this.linkedParent,this.options.params)||{values:[],xData:[],yData:[]};delete this.linkedParent.xData,this.linkedParent.yData=p,this.linkedParent.processedYData=u;let d=this.pointArrayMap||["y"],c={};if(h.yData.forEach(e=>{d.forEach((t,s)=>{let a=c[t]||[];a.push(S(e)?e[s]:e),c[t]||(c[t]=a)})}),a&&!this.hasGroupedData&&this.visible&&this.points)if(this.cropped){this.xAxis&&(n=this.xAxis.min,l=this.xAxis.max);let a=this.cropData(t,n,l),o=["x",...this.pointArrayMap||["y"]];for(let t=0;t<(a.modified?.rowCount||0);t++){let s=o.map(e=>this.getColumn(e)[t]||0);e.push(s)}let p=this.getColumn("x");i=h.xData.indexOf(p[0]),r=h.xData.indexOf(p[p.length-1]),-1===i&&r===h.xData.length-2&&e[0][0]===s[0].x&&e.shift(),this.updateData(e)}else(this.updateAllPoints||h.xData.length!==a-1&&h.xData.length!==a+1)&&(o=!1,this.updateData(h.values));o&&(t.setColumns({...c,x:h.xData}),this.options.data=h.values),this.calculateOn.xAxis&&this.getColumn("x",!0).length&&(this.isDirty=!0,this.redraw()),this.isDirtyData=!!this.linkedSeries.length,x(this,"updatedData")}processData(){let e=this.options.compareToMain,t=this.linkedParent;super.processData.apply(this,arguments),this.dataModify&&t&&t.dataModify&&t.dataModify.compareValue&&e&&(this.dataModify.compareValue=t.dataModify.compareValue)}}T.defaultOptions=A(y.defaultOptions,{name:void 0,tooltip:{valueDecimals:4},linkedTo:void 0,compareToMain:!1,params:{index:3,period:14}}),v(T.prototype,{calculateOn:{chart:"init"},hasDerivedData:!0,nameComponents:["period"],nameSuffixes:[],useCommonDataGrouping:!0}),g().registerSeriesType("sma",T);let{sma:P}=g().seriesTypes,{correctFloat:M,isArray:V,merge:L}=h();class k extends P{accumulatePeriodPoints(e,t,s){let a=0,o=0,i=0;for(;o<e;)a+=t<0?s[o]:s[o][t],o++;return a}calculateEma(e,t,s,a,o,i,r){let n=e[s-1],l=i<0?t[s-1]:t[s-1][i];return[n,void 0===o?r:M(l*a+o*(1-a))]}getValues(e,t){let s=t.period,a=e.xData,o=e.yData,i=o?o.length:0,r=2/(s+1),n=[],l=[],p=[],u,h,d,c=-1,m=0,g=0;if(!(i<s)){for(V(o[0])&&(c=t.index?t.index:0),g=this.accumulatePeriodPoints(s,c,o)/s,d=s;d<i+1;d++)h=this.calculateEma(a,o,d,r,u,c,g),n.push(h),l.push(h[0]),p.push(h[1]),u=h[1];return{values:n,xData:l,yData:p}}}}k.defaultOptions=L(P.defaultOptions,{params:{index:3,period:9}}),g().registerSeriesType("ema",k);let{sma:O}=g().seriesTypes,{error:w,extend:E,merge:I}=h();class N extends O{static populateAverage(e,t,s,a,o){let i=t[a][1],r=t[a][2],n=t[a][3],l=s[a],p=n===i&&n===r||i===r?0:(2*n-r-i)/(i-r)*l;return[e[a],p]}getValues(e,t){let s,a,o,i=t.period,r=e.xData,n=e.yData,l=t.volumeSeriesID,p=e.chart.get(l),u=p?.getColumn("y"),h=n?n.length:0,d=[],c=[],m=[];if(!(r.length<=i)||!h||4===n[0].length){if(!p)return void w("Series "+l+" not found! Check `volumeSeriesID`.",!0,e.chart);for(a=i;a<h;a++)s=d.length,o=N.populateAverage(r,n,u,a,i),s>0&&(o[1]+=d[s-1][1]),d.push(o),c.push(o[0]),m.push(o[1]);return{values:d,xData:c,yData:m}}}}N.defaultOptions=I(O.defaultOptions,{params:{index:void 0,volumeSeriesID:"volume"}}),E(N.prototype,{nameComponents:!1,nameBase:"Accumulation/Distribution"}),g().registerSeriesType("ad",N);let{noop:B}=h(),{column:{prototype:G},sma:W}=g().seriesTypes,{extend:z,merge:Y,correctFloat:F,isArray:X}=h();class R extends W{drawGraph(){let e,t=this.options,s=this.points,a=this.userOptions.color,o=t.greaterBarColor,i=t.lowerBarColor,r=s[0];if(!a&&r)for(e=1,r.color=o;e<s.length;e++)s[e].y>s[e-1].y?s[e].color=o:s[e].y<s[e-1].y?s[e].color=i:s[e].color=s[e-1].color}getValues(e){let t=e.xData||[],s=e.yData||[],a=s.length,o=[],i=[],r=[],n,l,p,u,h,d,c=0,m=0;if(!(t.length<=34)&&X(s[0])&&4===s[0].length){for(h=0;h<33;h++)u=(s[h][1]+s[h][2])/2,h>=29&&(m=F(m+u)),c=F(c+u);for(d=33;d<a;d++)m=F(m+(u=(s[d][1]+s[d][2])/2)),c=F(c+u),n=F(m/5-c/34),o.push([t[d],n]),i.push(t[d]),r.push(n),l=d+1-5,p=d+1-34,m=F(m-(s[l][1]+s[l][2])/2),c=F(c-(s[p][1]+s[p][2])/2);return{values:o,xData:i,yData:r}}}}R.defaultOptions=Y(W.defaultOptions,{params:{index:void 0,period:void 0},greaterBarColor:"#06b535",lowerBarColor:"#f21313",threshold:0,groupPadding:.2,pointPadding:.2,crisp:!1,states:{hover:{halo:{size:0}}}}),z(R.prototype,{nameBase:"AO",nameComponents:void 0,markerAttribs:B,getColumnMetrics:G.getColumnMetrics,crispCol:G.crispCol,translate:G.translate,drawPoints:G.drawPoints}),g().registerSeriesType("ao",R);let{sma:{prototype:K}}=g().seriesTypes,{defined:H,error:U,merge:Z}=h();!function(e){let t=["bottomLine"],s=["top","bottom"],a=["top"];function o(e){return"plot"+e.charAt(0).toUpperCase()+e.slice(1)}function i(e,t){let s=[];return(e.pointArrayMap||[]).forEach(e=>{e!==t&&s.push(o(e))}),s}function r(){let e=this,t=e.pointValKey,s=e.linesApiNames,a=e.areaLinesNames,r=e.points,n=e.options,l=e.graph,p={options:{gapSize:n.gapSize}},u=[],h=i(e,t),d=r.length,c;if(h.forEach((e,t)=>{for(u[t]=[];d--;)c=r[d],u[t].push({x:c.x,plotX:c.plotX,plotY:c[e],isNull:!H(c[e])});d=r.length}),e.userOptions.fillColor&&a.length){let t=u[h.indexOf(o(a[0]))],s=1===a.length?r:u[h.indexOf(o(a[1]))],i=e.color;e.points=s,e.nextPoints=t,e.color=e.userOptions.fillColor,e.options=Z(r,p),e.graph=e.area,e.fillGraph=!0,K.drawGraph.call(e),e.area=e.graph,delete e.nextPoints,delete e.fillGraph,e.color=i}s.forEach((t,s)=>{u[s]?(e.points=u[s],n[t]?e.options=Z(n[t].styles,p):U('Error: "There is no '+t+' in DOCS options declared. Check if linesApiNames are consistent with your DOCS line names."'),e.graph=e["graph"+t],K.drawGraph.call(e),e["graph"+t]=e.graph):U('Error: "'+t+" doesn't have equivalent in pointArrayMap. To many elements in linesApiNames relative to pointArrayMap.\"")}),e.points=r,e.options=n,e.graph=l,K.drawGraph.call(e)}function n(e){let t,s=[],a=[];if(e=e||this.points,this.fillGraph&&this.nextPoints){if((t=K.getGraphPath.call(this,this.nextPoints))&&t.length){t[0][0]="L",s=K.getGraphPath.call(this,e),a=t.slice(0,s.length);for(let e=a.length-1;e>=0;e--)s.push(a[e])}}else s=K.getGraphPath.apply(this,arguments);return s}function l(e){let t=[];return(this.pointArrayMap||[]).forEach(s=>{t.push(e[s])}),t}function p(){let e=this.pointArrayMap,t=[],s;t=i(this),K.translate.apply(this,arguments),this.points.forEach(a=>{e.forEach((e,o)=>{s=a[e],this.dataModify&&(s=this.dataModify.modifyValue(s)),null!==s&&(a[t[o]]=this.yAxis.toPixels(s,!0))})})}e.compose=function(e){let o=e.prototype;return o.linesApiNames=o.linesApiNames||t.slice(),o.pointArrayMap=o.pointArrayMap||s.slice(),o.pointValKey=o.pointValKey||"top",o.areaLinesNames=o.areaLinesNames||a.slice(),o.drawGraph=r,o.getGraphPath=n,o.toYData=l,o.translate=p,e}}(i||(i={}));let _=i,{sma:j}=g().seriesTypes,{extend:q,merge:$,pick:J}=h();function Q(e,t){let s=e[0],a=0,o;for(o=1;o<e.length;o++)("max"===t&&e[o]>=s||"min"===t&&e[o]<=s)&&(s=e[o],a=o);return a}class ee extends j{getValues(e,t){let s,a,o,i,r,n=t.period,l=e.xData,p=e.yData,u=p?p.length:0,h=[],d=[],c=[];for(i=n-1;i<u;i++)o=Q((r=p.slice(i-n+1,i+2)).map(function(e){return J(e[2],e)}),"min"),s=Q(r.map(function(e){return J(e[1],e)}),"max")/n*100,a=o/n*100,l[i+1]&&(h.push([l[i+1],s,a]),d.push(l[i+1]),c.push([s,a]));return{values:h,xData:d,yData:c}}}ee.defaultOptions=$(j.defaultOptions,{params:{index:void 0,period:25},marker:{enabled:!1},tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b><br/>Aroon Up: {point.y}<br/>Aroon Down: {point.aroonDown}<br/>'},aroonDown:{styles:{lineWidth:1,lineColor:void 0}},dataGrouping:{approximation:"averages"}}),q(ee.prototype,{areaLinesNames:[],linesApiNames:["aroonDown"],nameBase:"Aroon",pointArrayMap:["y","aroonDown"],pointValKey:"y"}),_.compose(ee),g().registerSeriesType("aroon",ee);let{aroon:et}=g().seriesTypes,{extend:es,merge:ea}=h();class eo extends et{getValues(e,t){let s,a,o=[],i=[],r=[],n=super.getValues.call(this,e,t);for(a=0;a<n.yData.length;a++)s=n.yData[a][0]-n.yData[a][1],o.push([n.xData[a],s]),i.push(n.xData[a]),r.push(s);return{values:o,xData:i,yData:r}}}eo.defaultOptions=ea(et.defaultOptions,{tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b>: {point.y}'}}),es(eo.prototype,{nameBase:"Aroon Oscillator",linesApiNames:[],pointArrayMap:["y"],pointValKey:"y"}),_.compose(et),g().registerSeriesType("aroonoscillator",eo);let{sma:ei}=g().seriesTypes,{isArray:er,merge:en}=h();function el(e,t){let s=e[1]-e[2];return Math.max(s,void 0===t?0:Math.abs(e[1]-t[3]),void 0===t?0:Math.abs(e[2]-t[3]))}class ep extends ei{getValues(e,t){let s=t.period,a=e.xData,o=e.yData,i=o?o.length:0,r=[[a[0],o[0]]],n=[],l=[],p=[],u,h,d=0,c=1,m=0;if(!(a.length<=s)&&er(o[0])&&4===o[0].length){for(h=1;h<=i;h++){var g,y,f,x,D,v,S=h;let e=a[S],t=o[S];r.push([e,t]),s<c?(d=(g=0,y=a,f=o,x=h,D=s,v=d,u=[y[x-1],(v*(D-1)+el(f[x-1],f[x-2]))/D])[1],n.push(u),l.push(u[0]),p.push(u[1])):(s===c?(d=m/(h-1),n.push([a[h-1],d]),l.push(a[h-1]),p.push(d)):m+=el(o[h-1],o[h-2]),c++)}return{values:n,xData:l,yData:p}}}}ep.defaultOptions=en(ei.defaultOptions,{params:{index:void 0}}),g().registerSeriesType("atr",ep);let{sma:eu}=g().seriesTypes,{extend:eh,isArray:ed,merge:ec}=h();class em extends eu{init(){g().seriesTypes.sma.prototype.init.apply(this,arguments),this.options=ec({topLine:{styles:{lineColor:this.color}},bottomLine:{styles:{lineColor:this.color}}},this.options)}getValues(e,t){let s,a,o,i,r,n,l,p,u,h=t.period,d=t.standardDeviation,c=[],m=[],y=e.xData,f=e.yData,x=f?f.length:0,D=[];if(y.length<h)return;let v=ed(f[0]);for(u=h;u<=x;u++)r=y.slice(u-h,u),n=f.slice(u-h,u),i=(p=g().seriesTypes.sma.prototype.getValues.call(this,{xData:r,yData:n},t)).xData[0],s=p.yData[0],l=function(e,t,s,a){let o=e.length,i=0,r=0,n,l=0;for(;i<o;i++)l+=(n=(s?e[i][t]:e[i])-a)*n;return Math.sqrt(l/=o-1)}(n,t.index,v,s),a=s+d*l,o=s-d*l,D.push([i,a,s,o]),c.push(i),m.push([a,s,o]);return{values:D,xData:c,yData:m}}}em.defaultOptions=ec(eu.defaultOptions,{params:{period:20,standardDeviation:2,index:3},bottomLine:{styles:{lineWidth:1,lineColor:void 0}},topLine:{styles:{lineWidth:1,lineColor:void 0}},tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b><br/>Top: {point.top}<br/>Middle: {point.middle}<br/>Bottom: {point.bottom}<br/>'},marker:{enabled:!1},dataGrouping:{approximation:"averages"}}),eh(em.prototype,{areaLinesNames:["top","bottom"],linesApiNames:["topLine","bottomLine"],nameComponents:["period","standardDeviation"],pointArrayMap:["top","middle","bottom"],pointValKey:"middle"}),_.compose(em),g().registerSeriesType("bb",em);let{sma:eg}=g().seriesTypes,{isArray:ey,merge:ef}=h();class ex extends eg{getValues(e,t){let s=t.period,a=e.xData,o=e.yData,i=o?o.length:0,r=[],n=[],l=[],p=[],u,h,d=[],c,m=1,g,y,f,x;if(!(a.length<=s)&&ey(o[0])&&4===o[0].length){for(;m<s;)h=o[m-1],r.push((h[1]+h[2]+h[3])/3),m++;for(x=s;x<=i;x++)y=((h=o[x-1])[1]+h[2]+h[3])/3,c=r.push(y),g=(d=r.slice(c-s)).reduce(function(e,t){return e+t},0)/s,f=function(e,t){let s=e.length,a=0,o;for(o=0;o<s;o++)a+=Math.abs(t-e[o]);return a}(d,g)/s,u=(y-g)/(.015*f),n.push([a[x-1],u]),l.push(a[x-1]),p.push(u);return{values:n,xData:l,yData:p}}}}ex.defaultOptions=ef(eg.defaultOptions,{params:{index:void 0}}),g().registerSeriesType("cci",ex);let{sma:eD}=g().seriesTypes,{merge:ev}=h();class eS extends eD{constructor(){super(...arguments),this.nameBase="Chaikin Money Flow"}isValid(){let e=this.chart,t=this.options,s=this.linkedParent,a=this.volumeSeries||(this.volumeSeries=e.get(t.params.volumeSeriesID)),o=s?.pointArrayMap?.length===4;function i(e){return e.dataTable.rowCount>=t.params.period}return!!(s&&a&&i(s)&&i(a)&&o)}getValues(e,t){if(this.isValid())return this.getMoneyFlow(e.xData,e.yData,this.volumeSeries.getColumn("y"),t.period)}getMoneyFlow(e,t,s,a){let o=t.length,i=[],r=[],n=[],l=[],p,u,h=-1,d=0,c=0;function m(e,t){let s=e[1],a=e[2],o=e[3];return null!==t&&null!==s&&null!==a&&null!==o&&s!==a?(o-a-(s-o))/(s-a)*t:(h=p,null)}if(a>0&&a<=o){for(p=0;p<a;p++)i[p]=m(t[p],s[p]),d+=s[p],c+=i[p];for(r.push(e[p-1]),n.push(p-h>=a&&0!==d?c/d:null),l.push([r[0],n[0]]);p<o;p++)i[p]=m(t[p],s[p]),d-=s[p-a],d+=s[p],c-=i[p-a],c+=i[p],u=[e[p],p-h>=a?c/d:null],r.push(u[0]),n.push(u[1]),l.push([u[0],u[1]])}return{values:l,xData:r,yData:n}}}eS.defaultOptions=ev(eD.defaultOptions,{params:{index:void 0,volumeSeriesID:"volume"}}),g().registerSeriesType("cmf",eS);let{sma:eA}=g().seriesTypes,{correctFloat:eb,extend:eC,isArray:eT,merge:eP}=h();class eM extends eA{calculateDM(e,t,s){let a,o=e[t][1],i=e[t][2],r=e[t-1][1],n=e[t-1][2];return eb(o-r>n-i?s?Math.max(o-r,0):0:s?0:Math.max(n-i,0))}calculateDI(e,t){return e/t*100}calculateDX(e,t){return eb(Math.abs(e-t)/Math.abs(e+t)*100)}smoothValues(e,t,s){return eb(e-e/s+t)}getTR(e,t){return eb(Math.max(e[1]-e[2],t?Math.abs(e[1]-t[3]):0,t?Math.abs(e[2]-t[3]):0))}getValues(e,t){let s=t.period,a=e.xData,o=e.yData,i=o?o.length:0,r=[],n=[],l=[];if(a.length<=s||!eT(o[0])||4!==o[0].length)return;let p=0,u=0,h=0,d;for(d=1;d<i;d++){let e,t,i,c,m,g,y,f,x;d<=s?(c=this.calculateDM(o,d,!0),m=this.calculateDM(o,d),g=this.getTR(o[d],o[d-1]),p+=c,u+=m,h+=g,d===s&&(y=this.calculateDI(p,h),f=this.calculateDI(u,h),x=this.calculateDX(p,u),r.push([a[d],x,y,f]),n.push(a[d]),l.push([x,y,f]))):(c=this.calculateDM(o,d,!0),m=this.calculateDM(o,d),g=this.getTR(o[d],o[d-1]),e=this.smoothValues(p,c,s),t=this.smoothValues(u,m,s),i=this.smoothValues(h,g,s),p=e,u=t,h=i,y=this.calculateDI(p,h),f=this.calculateDI(u,h),x=this.calculateDX(p,u),r.push([a[d],x,y,f]),n.push(a[d]),l.push([x,y,f]))}return{values:r,xData:n,yData:l}}}eM.defaultOptions=eP(eA.defaultOptions,{params:{index:void 0},marker:{enabled:!1},tooltip:{pointFormat:'<span style="color: {point.color}">●</span><b> {series.name}</b><br/><span style="color: {point.color}">DX</span>: {point.y}<br/><span style="color: {point.series.options.plusDILine.styles.lineColor}">+DI</span>: {point.plusDI}<br/><span style="color: {point.series.options.minusDILine.styles.lineColor}">-DI</span>: {point.minusDI}<br/>'},plusDILine:{styles:{lineWidth:1,lineColor:"#06b535"}},minusDILine:{styles:{lineWidth:1,lineColor:"#f21313"}},dataGrouping:{approximation:"averages"}}),eC(eM.prototype,{areaLinesNames:[],nameBase:"DMI",linesApiNames:["plusDILine","minusDILine"],pointArrayMap:["y","plusDI","minusDI"],parallelArrays:["x","y","plusDI","minusDI"],pointValKey:"y"}),_.compose(eM),g().registerSeriesType("dmi",eM);let{sma:eV}=g().seriesTypes,{extend:eL,merge:ek,correctFloat:eO,pick:ew}=h();function eE(e,t,s,a,o){let i=ew(t[s][a],t[s]);return o?eO(e-i):eO(e+i)}class eI extends eV{getValues(e,t){let s=t.period,a=t.index,o=Math.floor(s/2+1),i=s+o,r=e.xData||[],n=e.yData||[],l=n.length,p=[],u=[],h=[],d,c,m,g,y,f=0;if(!(r.length<=i)){for(g=0;g<s-1;g++)f=eE(f,n,g,a);for(y=0;y<=l-i;y++)c=y+s-1,m=y+i-1,f=eE(f,n,c,a),d=ew(n[m][a],n[m])-f/s,f=eE(f,n,y,a,!0),p.push([r[m],d]),u.push(r[m]),h.push(d);return{values:p,xData:u,yData:h}}}}eI.defaultOptions=ek(eV.defaultOptions,{params:{index:0,period:21}}),eL(eI.prototype,{nameBase:"DPO"}),g().registerSeriesType("dpo",eI);let{ema:eN}=g().seriesTypes,{correctFloat:eB,extend:eG,merge:eW,error:ez}=h();class eY extends eN{getValues(e,t){let s,a,o=t.periods,i=t.period,r=[],n=[],l=[];if(2!==o.length||o[1]<=o[0])return void ez('Error: "Chaikin requires two periods. Notice, first period should be lower than the second one."');let p=N.prototype.getValues.call(this,e,{volumeSeriesID:t.volumeSeriesID,period:i});if(!p)return;let u=super.getValues.call(this,p,{period:o[0]}),h=super.getValues.call(this,p,{period:o[1]});if(!u||!h)return;let d=o[1]-o[0];for(a=0;a<h.yData.length;a++)s=eB(u.yData[a+d]-h.yData[a]),r.push([h.xData[a],s]),n.push(h.xData[a]),l.push(s);return{values:r,xData:n,yData:l}}}eY.defaultOptions=eW(eN.defaultOptions,{params:{index:void 0,volumeSeriesID:"volume",period:9,periods:[3,10]}}),eG(eY.prototype,{nameBase:"Chaikin Osc",nameComponents:["periods"]}),g().registerSeriesType("chaikin",eY);let{sma:eF}=g().seriesTypes,{isNumber:eX,merge:eR}=h();class eK extends eF{getValues(e,t){let s=t.period,a=e.xData,o=e.yData,i=o?o.length:0,r=[],n=[],l=[],p,u=t.index,h;if(a.length<s)return;eX(o[0])?h=o:(u=Math.min(u,o[0].length-1),h=o.map(e=>e[u]));let d=0,c=0,m=0,g;for(let e=s;e>0;e--)h[e]>h[e-1]?c+=h[e]-h[e-1]:h[e]<h[e-1]&&(m+=h[e-1]-h[e]);for(g=c+m>0?100*(c-m)/(c+m):0,n.push(a[s]),l.push(g),r.push([a[s],g]),p=s+1;p<i;p++)d=Math.abs(h[p-s-1]-h[p-s]),h[p]>h[p-1]?c+=h[p]-h[p-1]:h[p]<h[p-1]&&(m+=h[p-1]-h[p]),h[p-s]>h[p-s-1]?c-=d:m-=d,g=c+m>0?100*(c-m)/(c+m):0,n.push(a[p]),l.push(g),r.push([a[p],g]);return{values:r,xData:n,yData:l}}}eK.defaultOptions=eR(eF.defaultOptions,{params:{period:20,index:3}}),g().registerSeriesType("cmo",eK);let{ema:eH}=g().seriesTypes,{correctFloat:eU,isArray:eZ,merge:e_}=h();class ej extends eH{getEMA(e,t,s,a,o,i){return super.calculateEma(i||[],e,void 0===o?1:o,this.EMApercent,t,void 0===a?-1:a,s)}getValues(e,t){let s=t.period,a=[],o=2*s,i=e.xData,r=e.yData,n=r?r.length:0,l=[],p=[],u=[],h=0,d=0,c,m,g,y,f=-1,x,D=0;if(this.EMApercent=2/(s+1),!(n<2*s-1)){for(eZ(r[0])&&(f=t.index?t.index:0),D=(h=super.accumulatePeriodPoints(s,f,r))/s,h=0,y=s;y<n+2;y++)y<n+1&&(d=this.getEMA(r,m,D,f,y)[1],a.push(d)),m=d,y<o?h+=d:(y===o&&(D=h/s),d=a[y-s-1],c=this.getEMA([d],g,D)[1],x=[i[y-2],eU(2*d-c)],l.push(x),p.push(x[0]),u.push(x[1]),g=c);return{values:l,xData:p,yData:u}}}}ej.defaultOptions=e_(eH.defaultOptions),g().registerSeriesType("dema",ej);let{ema:eq}=g().seriesTypes,{correctFloat:e$,isArray:eJ,merge:eQ}=h();class e0 extends eq{getEMA(e,t,s,a,o,i){return super.calculateEma(i||[],e,void 0===o?1:o,this.EMApercent,t,void 0===a?-1:a,s)}getTemaPoint(e,t,s,a){return[e[a-3],e$(3*s.level1-3*s.level2+s.level3)]}getValues(e,t){let s=t.period,a=2*s,o=3*s,i=e.xData,r=e.yData,n=r?r.length:0,l=[],p=[],u=[],h=[],d=[],c={},m=-1,g=0,y=0,f,x,D,v;if(this.EMApercent=2/(s+1),!(n<3*s-2)){for(eJ(r[0])&&(m=t.index?t.index:0),y=(g=super.accumulatePeriodPoints(s,m,r))/s,g=0,D=s;D<n+3;D++)D<n+1&&(c.level1=this.getEMA(r,f,y,m,D)[1],h.push(c.level1)),f=c.level1,D<a?g+=c.level1:(D===a&&(y=g/s,g=0),c.level1=h[D-s-1],c.level2=this.getEMA([c.level1],x,y)[1],d.push(c.level2),x=c.level2,D<o?g+=c.level2:(D===o&&(y=g/s),D===n+1&&(c.level1=h[D-s-1],c.level2=this.getEMA([c.level1],x,y)[1],d.push(c.level2)),c.level1=h[D-s-2],c.level2=d[D-2*s-1],c.level3=this.getEMA([c.level2],c.prevLevel3,y)[1],(v=this.getTemaPoint(i,o,c,D))&&(l.push(v),p.push(v[0]),u.push(v[1])),c.prevLevel3=c.level3));return{values:l,xData:p,yData:u}}}}e0.defaultOptions=eQ(eq.defaultOptions),g().registerSeriesType("tema",e0);let{tema:e1}=g().seriesTypes,{correctFloat:e2,merge:e3}=h();class e4 extends e1{getTemaPoint(e,t,s,a){if(a>t)return[e[a-3],0!==s.prevLevel3?e2(s.level3-s.prevLevel3)/s.prevLevel3*100:null]}}e4.defaultOptions=e3(e1.defaultOptions),g().registerSeriesType("trix",e4);let{ema:e5}=g().seriesTypes,{extend:e6,merge:e9,error:e8}=h();class e7 extends e5{getValues(e,t){let s,a,o=t.periods,i=t.index,r=[],n=[],l=[];if(2!==o.length||o[1]<=o[0])return void e8('Error: "APO requires two periods. Notice, first period should be lower than the second one."');let p=super.getValues.call(this,e,{index:i,period:o[0]}),u=super.getValues.call(this,e,{index:i,period:o[1]});if(!p||!u)return;let h=o[1]-o[0];for(a=0;a<u.yData.length;a++)s=p.yData[a+h]-u.yData[a],r.push([u.xData[a],s]),n.push(u.xData[a]),l.push(s);return{values:r,xData:n,yData:l}}}e7.defaultOptions=e9(e5.defaultOptions,{params:{period:void 0,periods:[10,20]}}),e6(e7.prototype,{nameBase:"APO",nameComponents:["periods"]}),g().registerSeriesType("apo",e7);var te=l(956),tt=l.n(te),ts=l(620);let{parse:ta}=l.n(ts)(),{sma:to}=g().seriesTypes,{defined:ti,extend:tr,isArray:tn,isNumber:tl,getClosestDistance:tp,merge:tu,objectEach:th}=h();function td(e){return{high:e.reduce(function(e,t){return Math.max(e,t[1])},-1/0),low:e.reduce(function(e,t){return Math.min(e,t[2])},1/0)}}function tc(e){let t=e.indicator;t.points=e.points,t.nextPoints=e.nextPoints,t.color=e.color,t.options=tu(e.options.senkouSpan.styles,e.gap),t.graph=e.graph,t.fillGraph=!0,g().seriesTypes.sma.prototype.drawGraph.call(t)}class tm extends to{constructor(){super(...arguments),this.data=[],this.options={},this.points=[],this.graphCollection=[]}init(){super.init.apply(this,arguments),this.options=tu({tenkanLine:{styles:{lineColor:this.color}},kijunLine:{styles:{lineColor:this.color}},chikouLine:{styles:{lineColor:this.color}},senkouSpanA:{styles:{lineColor:this.color,fill:ta(this.color).setOpacity(.5).get()}},senkouSpanB:{styles:{lineColor:this.color,fill:ta(this.color).setOpacity(.5).get()}},senkouSpan:{styles:{fill:ta(this.color).setOpacity(.2).get()}}},this.options)}toYData(e){return[e.tenkanSen,e.kijunSen,e.chikouSpan,e.senkouSpanA,e.senkouSpanB]}translate(){for(let e of(g().seriesTypes.sma.prototype.translate.apply(this),this.points))for(let t of this.pointArrayMap){let s=e[t];tl(s)&&(e["plot"+t]=this.yAxis.toPixels(s,!0),e.plotY=e["plot"+t],e.tooltipPos=[e.plotX,e["plot"+t]],e.isNull=!1)}}drawGraph(){let e=this,t=e.points,s=e.options,a=e.graph,o=e.color,i={options:{gapSize:s.gapSize}},r=e.pointArrayMap.length,n=[[],[],[],[],[],[]],l={tenkanLine:n[0],kijunLine:n[1],chikouLine:n[2],senkouSpanA:n[3],senkouSpanB:n[4],senkouSpan:n[5]},p=[],u=e.options.senkouSpan,h=u.color||u.styles.fill,d=u.negativeColor,c=[[],[]],m=[[],[]],y=t.length,f=0,x,D,v,S,A,b,C,T,P,M,V,L,k;for(e.ikhMap=l;y--;){for(v=0,D=t[y];v<r;v++)ti(D[x=e.pointArrayMap[v]])&&n[v].push({plotX:D.plotX,plotY:D["plot"+x],isNull:!1});if(d&&y!==t.length-1){let e=l.senkouSpanB.length-1,t=function(e,t,s,a){if(e&&t&&s&&a){let o=t.plotX-e.plotX,i=t.plotY-e.plotY,r=a.plotX-s.plotX,n=a.plotY-s.plotY,l=e.plotX-s.plotX,p=e.plotY-s.plotY,u=(-i*l+o*p)/(-r*i+o*n),h=(r*p-n*l)/(-r*i+o*n);if(u>=0&&u<=1&&h>=0&&h<=1)return{plotX:e.plotX+h*o,plotY:e.plotY+h*i}}}(l.senkouSpanA[e-1],l.senkouSpanA[e],l.senkouSpanB[e-1],l.senkouSpanB[e]);if(t){let s={plotX:t.plotX,plotY:t.plotY,isNull:!1,intersectPoint:!0};l.senkouSpanA.splice(e,0,s),l.senkouSpanB.splice(e,0,s),p.push(e)}}}if(th(l,(t,a)=>{s[a]&&"senkouSpan"!==a&&(e.points=n[f],e.options=tu(s[a].styles,i),e.graph=e["graph"+a],e.fillGraph=!1,e.color=o,g().seriesTypes.sma.prototype.drawGraph.call(e),e["graph"+a]=e.graph),f++}),e.graphCollection)for(let t of e.graphCollection)e[t].destroy(),delete e[t];if(e.graphCollection=[],d&&l.senkouSpanA[0]&&l.senkouSpanB[0]){for(p.unshift(0),p.push(l.senkouSpanA.length-1),L=0;L<p.length-1;L++)if(S=p[L],A=p[L+1],b=l.senkouSpanB.slice(S,A+1),C=l.senkouSpanA.slice(S,A+1),Math.floor(b.length/2)>=1){let e=Math.floor(b.length/2);if(b[e].plotY===C[e].plotY){for(k=0,T=0,P=0;k<b.length;k++)T+=b[k].plotY,P+=C[k].plotY;c[V=T>P?0:1]=c[V].concat(b),m[V]=m[V].concat(C)}else c[V=b[e].plotY>C[e].plotY?0:1]=c[V].concat(b),m[V]=m[V].concat(C)}else c[V=b[0].plotY>C[0].plotY?0:1]=c[V].concat(b),m[V]=m[V].concat(C);["graphsenkouSpanColor","graphsenkouSpanNegativeColor"].forEach(function(t,a){c[a].length&&m[a].length&&(M=0===a?h:d,tc({indicator:e,points:c[a],nextPoints:m[a],color:M,options:s,gap:i,graph:e[t]}),e[t]=e.graph,e.graphCollection.push(t))})}else tc({indicator:e,points:l.senkouSpanB,nextPoints:l.senkouSpanA,color:h,options:s,gap:i,graph:e.graphsenkouSpan}),e.graphsenkouSpan=e.graph;delete e.nextPoints,delete e.fillGraph,e.points=t,e.options=s,e.graph=a,e.color=o}getGraphPath(e){let t=[],s,a=[];if(e=e||this.points,this.fillGraph&&this.nextPoints){if((s=g().seriesTypes.sma.prototype.getGraphPath.call(this,this.nextPoints))&&s.length){s[0][0]="L",t=g().seriesTypes.sma.prototype.getGraphPath.call(this,e),a=s.slice(0,t.length);for(let e=a.length-1;e>=0;e--)t.push(a[e])}}else t=g().seriesTypes.sma.prototype.getGraphPath.apply(this,arguments);return t}getValues(e,t){let s,a,o,i,r,n,l,p,u,h,d=t.period,c=t.periodTenkan,m=t.periodSenkouSpanB,g=e.xData,y=e.yData,f=e.xAxis,x=y&&y.length||0,D=tp(f.series.map(e=>e.getColumn("x"))),v=[],S=[];if(g.length<=d||!tn(y[0])||4!==y[0].length)return;let A=g[0]-d*D;for(r=0;r<d;r++)S.push(A+r*D);for(r=0;r<x;r++)r>=c&&(n=((a=td(y.slice(r-c,r))).high+a.low)/2),r>=d&&(u=(n+(l=((o=td(y.slice(r-d,r))).high+o.low)/2))/2),r>=m&&(h=((i=td(y.slice(r-m,r))).high+i.low)/2),p=y[r][3],s=g[r],void 0===v[r]&&(v[r]=[]),void 0===v[r+d-1]&&(v[r+d-1]=[]),v[r+d-1][0]=n,v[r+d-1][1]=l,v[r+d-1][2]=void 0,void 0===v[r+1]&&(v[r+1]=[]),v[r+1][2]=p,r<=d&&(v[r+d-1][3]=void 0,v[r+d-1][4]=void 0),void 0===v[r+2*d-2]&&(v[r+2*d-2]=[]),v[r+2*d-2][3]=u,v[r+2*d-2][4]=h,S.push(s);for(r=1;r<=d;r++)S.push(s+r*D);return{values:v,xData:S,yData:v}}}tm.defaultOptions=tu(to.defaultOptions,{params:{index:void 0,period:26,periodTenkan:9,periodSenkouSpanB:52},marker:{enabled:!1},tooltip:{pointFormat:'<span style="color:{point.color}">●</span> <b> {series.name}</b><br/>TENKAN SEN: {point.tenkanSen:.3f}<br/>KIJUN SEN: {point.kijunSen:.3f}<br/>CHIKOU SPAN: {point.chikouSpan:.3f}<br/>SENKOU SPAN A: {point.senkouSpanA:.3f}<br/>SENKOU SPAN B: {point.senkouSpanB:.3f}<br/>'},tenkanLine:{styles:{lineWidth:1,lineColor:void 0}},kijunLine:{styles:{lineWidth:1,lineColor:void 0}},chikouLine:{styles:{lineWidth:1,lineColor:void 0}},senkouSpanA:{styles:{lineWidth:1,lineColor:void 0}},senkouSpanB:{styles:{lineWidth:1,lineColor:void 0}},senkouSpan:{styles:{fill:"rgba(255, 0, 0, 0.5)"}},dataGrouping:{approximation:"ichimoku-averages"}}),tr(tm.prototype,{pointArrayMap:["tenkanSen","kijunSen","chikouSpan","senkouSpanA","senkouSpanB"],pointValKey:"tenkanSen",nameComponents:["periodSenkouSpanB","period","periodTenkan"]}),tt()["ichimoku-averages"]=function(){let e,t=[];return[].forEach.call(arguments,function(s,a){t.push(tt().average(s)),e=!e&&void 0===t[a]}),e?void 0:t},g().registerSeriesType("ikh",tm);let{sma:tg}=g().seriesTypes,{correctFloat:ty,extend:tf,merge:tx}=h();class tD extends tg{init(){g().seriesTypes.sma.prototype.init.apply(this,arguments),this.options=tx({topLine:{styles:{lineColor:this.color}},bottomLine:{styles:{lineColor:this.color}}},this.options)}getValues(e,t){let s,a,o,i,r,n,l,p=t.period,u=t.periodATR,h=t.multiplierATR,d=t.index,c=e.yData,m=c?c.length:0,y=[],f=g().seriesTypes.ema.prototype.getValues(e,{period:p,index:d}),x=g().seriesTypes.atr.prototype.getValues(e,{period:u}),D=[],v=[];if(!(m<p)){for(l=p;l<=m;l++)r=f.values[l-p],n=x.values[l-u],i=r[0],a=ty(r[1]+h*n[1]),o=ty(r[1]-h*n[1]),s=r[1],y.push([i,a,s,o]),D.push(i),v.push([a,s,o]);return{values:y,xData:D,yData:v}}}}tD.defaultOptions=tx(tg.defaultOptions,{params:{index:0,period:20,periodATR:10,multiplierATR:2},bottomLine:{styles:{lineWidth:1,lineColor:void 0}},topLine:{styles:{lineWidth:1,lineColor:void 0}},tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b><br/>Upper Channel: {point.top}<br/>EMA({series.options.params.period}): {point.middle}<br/>Lower Channel: {point.bottom}<br/>'},marker:{enabled:!1},dataGrouping:{approximation:"averages"},lineWidth:1}),tf(tD.prototype,{nameBase:"Keltner Channels",areaLinesNames:["top","bottom"],nameComponents:["period","periodATR","multiplierATR"],linesApiNames:["topLine","bottomLine"],pointArrayMap:["top","middle","bottom"],pointValKey:"middle"}),_.compose(tD),g().registerSeriesType("keltnerchannels",tD);let{ema:tv,sma:tS}=g().seriesTypes,{correctFloat:tA,error:tb,extend:tC,isArray:tT,merge:tP}=h();class tM extends tS{calculateTrend(e,t){return e[t][1]+e[t][2]+e[t][3]>e[t-1][1]+e[t-1][2]+e[t-1][3]?1:-1}isValidData(e){let t=this.chart,s=this.options,a=this.linkedParent,o=tT(e)&&4===e.length,i=this.volumeSeries||(this.volumeSeries=t.get(s.params.volumeSeriesID));return i||tb("Series "+s.params.volumeSeriesID+" not found! Check `volumeSeriesID`.",!0,a.chart),!!([a,i].every(function(e){return e&&e.dataTable.rowCount>=s.params.slowAvgPeriod})&&o)}getCM(e,t,s,a,o){return tA(t+(s===a?e:o))}getDM(e,t){return tA(e-t)}getVolumeForce(e){let t=[],s=0,a,o=1,i=0,r=e[0][1]-e[0][2],n=0,l;for(;o<e.length;o++)l=this.calculateTrend(e,o),a=this.getDM(e[o][1],e[o][2]),s=this.getCM(i,a,l,n,r),t.push([this.volumeSeries.getColumn("y")[o]*l*Math.abs(2*(a/s-1))*100]),n=l,i=s,r=a;return t}getEMA(e,t,s,a,o,i,r){return tv.prototype.calculateEma(r||[],e,void 0===i?1:i,a,t,void 0===o?-1:o,s)}getSMA(e,t,s){return tv.prototype.accumulatePeriodPoints(e,t,s)/e}getValues(e,t){let s=[],a=e.xData,o=e.yData,i=[],r=[],n=[],l,p=0,u=0,h,d,c,m=null;if(!this.isValidData(o[0]))return;let g=this.getVolumeForce(o),y=this.getSMA(t.fastAvgPeriod,0,g),f=this.getSMA(t.slowAvgPeriod,0,g),x=2/(t.fastAvgPeriod+1),D=2/(t.slowAvgPeriod+1);for(;p<o.length;p++)p>=t.fastAvgPeriod&&(d=u=this.getEMA(g,d,y,x,0,p,a)[1]),p>=t.slowAvgPeriod&&(c=h=this.getEMA(g,c,f,D,0,p,a)[1],n.push(l=tA(u-h)),n.length>=t.signalPeriod&&(m=n.slice(-t.signalPeriod).reduce((e,t)=>e+t)/t.signalPeriod),s.push([a[p],l,m]),i.push(a[p]),r.push([l,m]));return{values:s,xData:i,yData:r}}}tM.defaultOptions=tP(tS.defaultOptions,{params:{fastAvgPeriod:34,slowAvgPeriod:55,signalPeriod:13,volumeSeriesID:"volume"},signalLine:{styles:{lineWidth:1,lineColor:"#ff0000"}},dataGrouping:{approximation:"averages"},tooltip:{pointFormat:'<span style="color: {point.color}">●</span><b> {series.name}</b><br/><span style="color: {point.color}">Klinger</span>: {point.y}<br/><span style="color: {point.series.options.signalLine.styles.lineColor}">Signal</span>: {point.signal}<br/>'}}),tC(tM.prototype,{areaLinesNames:[],linesApiNames:["signalLine"],nameBase:"Klinger",nameComponents:["fastAvgPeriod","slowAvgPeriod"],pointArrayMap:["y","signal"],parallelArrays:["x","y","signal"],pointValKey:"y"}),_.compose(tM),g().registerSeriesType("klinger",tM);let{noop:tV}=h(),{column:tL,sma:tk}=g().seriesTypes,{extend:tO,correctFloat:tw,defined:tE,merge:tI}=h();class tN extends tk{init(){g().seriesTypes.sma.prototype.init.apply(this,arguments);let e=this.color;this.options&&(tE(this.colorIndex)&&(this.options.signalLine&&this.options.signalLine.styles&&!this.options.signalLine.styles.lineColor&&(this.options.colorIndex=this.colorIndex+1,this.getCyclic("color",void 0,this.chart.options.colors),this.options.signalLine.styles.lineColor=this.color),this.options.macdLine&&this.options.macdLine.styles&&!this.options.macdLine.styles.lineColor&&(this.options.colorIndex=this.colorIndex+1,this.getCyclic("color",void 0,this.chart.options.colors),this.options.macdLine.styles.lineColor=this.color)),this.macdZones={zones:this.options.macdLine.zones,startIndex:0},this.signalZones={zones:this.macdZones.zones.concat(this.options.signalLine.zones),startIndex:this.macdZones.zones.length}),this.color=e}toYData(e){return[e.y,e.signal,e.MACD]}translate(){let e=this,t=["plotSignal","plotMACD"];h().seriesTypes.column.prototype.translate.apply(e),e.points.forEach(function(s){[s.signal,s.MACD].forEach(function(a,o){null!==a&&(s[t[o]]=e.yAxis.toPixels(a,!0))})})}destroy(){this.graph=null,this.graphmacd=this.graphmacd&&this.graphmacd.destroy(),this.graphsignal=this.graphsignal&&this.graphsignal.destroy(),g().seriesTypes.sma.prototype.destroy.apply(this,arguments)}drawGraph(){let e=this,t=e.points,s=e.options,a=e.zones,o={options:{gapSize:s.gapSize}},i=[[],[]],r,n=t.length;for(;n--;)tE((r=t[n]).plotMACD)&&i[0].push({plotX:r.plotX,plotY:r.plotMACD,isNull:!tE(r.plotMACD)}),tE(r.plotSignal)&&i[1].push({plotX:r.plotX,plotY:r.plotSignal,isNull:!tE(r.plotMACD)});["macd","signal"].forEach((t,a)=>{e.points=i[a],e.options=tI(s[`${t}Line`]?.styles||{},o),e.graph=e[`graph${t}`],e.zones=(e[`${t}Zones`].zones||[]).slice(e[`${t}Zones`].startIndex||0),g().seriesTypes.sma.prototype.drawGraph.call(e),e[`graph${t}`]=e.graph}),e.points=t,e.options=s,e.zones=a}applyZones(){let e=this.zones;this.zones=this.signalZones.zones,g().seriesTypes.sma.prototype.applyZones.call(this),this.graphmacd&&this.options.macdLine.zones.length&&this.graphmacd.hide(),this.zones=e}getValues(e,t){let s=t.longPeriod-t.shortPeriod,a=[],o=[],i=[],r,n,l,p=0,u=[];if(!(e.xData.length<t.longPeriod+t.signalPeriod)){for(l=0,r=g().seriesTypes.ema.prototype.getValues(e,{period:t.shortPeriod,index:t.index}),n=g().seriesTypes.ema.prototype.getValues(e,{period:t.longPeriod,index:t.index}),r=r.values,n=n.values;l<=r.length;l++)tE(n[l])&&tE(n[l][1])&&tE(r[l+s])&&tE(r[l+s][0])&&a.push([r[l+s][0],0,null,r[l+s][1]-n[l][1]]);for(l=0;l<a.length;l++)o.push(a[l][0]),i.push([0,null,a[l][3]]);for(l=0,u=(u=g().seriesTypes.ema.prototype.getValues({xData:o,yData:i},{period:t.signalPeriod,index:2})).values;l<a.length;l++)a[l][0]>=u[0][0]&&(a[l][2]=u[p][1],i[l]=[0,u[p][1],a[l][3]],null===a[l][3]?(a[l][1]=0,i[l][0]=0):(a[l][1]=tw(a[l][3]-u[p][1]),i[l][0]=tw(a[l][3]-u[p][1])),p++);return{values:a,xData:o,yData:i}}}}tN.defaultOptions=tI(tk.defaultOptions,{params:{shortPeriod:12,longPeriod:26,signalPeriod:9,period:26},signalLine:{zones:[],styles:{lineWidth:1,lineColor:void 0}},macdLine:{zones:[],styles:{lineWidth:1,lineColor:void 0}},threshold:0,groupPadding:.1,pointPadding:.1,crisp:!1,states:{hover:{halo:{size:0}}},tooltip:{pointFormat:'<span style="color:{point.color}">●</span> <b> {series.name}</b><br/>Value: {point.MACD}<br/>Signal: {point.signal}<br/>Histogram: {point.y}<br/>'},dataGrouping:{approximation:"averages"},minPointLength:0}),tO(tN.prototype,{nameComponents:["longPeriod","shortPeriod","signalPeriod"],pointArrayMap:["y","signal","MACD"],parallelArrays:["x","y","signal","MACD"],pointValKey:"y",markerAttribs:tV,getColumnMetrics:h().seriesTypes.column.prototype.getColumnMetrics,crispCol:h().seriesTypes.column.prototype.crispCol,drawPoints:h().seriesTypes.column.prototype.drawPoints}),g().registerSeriesType("macd",tN);let{sma:tB}=g().seriesTypes,{extend:tG,merge:tW,error:tz,isArray:tY}=h();function tF(e){return e.reduce(function(e,t){return e+t})}function tX(e){return(e[1]+e[2]+e[3])/3}class tR extends tB{getValues(e,t){let s=t.period,a=e.xData,o=e.yData,i=o?o.length:0,r=t.decimals,n=e.chart.get(t.volumeSeriesID),l=n?.getColumn("y")||[],p=[],u=[],h=[],d=[],c=[],m,g,y,f,x,D,v=!1,S=1;if(!n)return void tz("Series "+t.volumeSeriesID+" not found! Check `volumeSeriesID`.",!0,e.chart);if(!(a.length<=s)&&tY(o[0])&&4===o[0].length&&l){for(m=tX(o[S]);S<s+1;)g=m,v=(m=tX(o[S]))>=g,y=m*l[S],d.push(v?y:0),c.push(v?0:y),S++;for(D=S-1;D<i;D++){var A;D>S-1&&(d.shift(),c.shift(),g=m,v=(m=tX(o[D]))>g,y=m*l[D],d.push(v?y:0),c.push(v?0:y)),f=tF(c),A=100-100/(1+tF(d)/f),x=parseFloat(A.toFixed(r)),p.push([a[D],x]),u.push(a[D]),h.push(x)}return{values:p,xData:u,yData:h}}}}tR.defaultOptions=tW(tB.defaultOptions,{params:{index:void 0,volumeSeriesID:"volume",decimals:4}}),tG(tR.prototype,{nameBase:"Money Flow Index"}),g().registerSeriesType("mfi",tR);let{sma:tK}=g().seriesTypes,{extend:tH,isArray:tU,merge:tZ}=h();function t_(e,t,s,a,o){let i=t[s-1][o]-t[s-a-1][o];return[e[s-1],i]}class tj extends tK{getValues(e,t){let s,a,o=t.period,i=t.index,r=e.xData,n=e.yData,l=n?n.length:0,p=[],u=[],h=[];if(!(r.length<=o)&&tU(n[0])){for(s=o+1;s<l;s++)a=t_(r,n,s,o,i),p.push(a),u.push(a[0]),h.push(a[1]);return a=t_(r,n,s,o,i),p.push(a),u.push(a[0]),h.push(a[1]),{values:p,xData:u,yData:h}}}}tj.defaultOptions=tZ(tK.defaultOptions,{params:{index:3}}),tH(tj.prototype,{nameBase:"Momentum"}),g().registerSeriesType("momentum",tj);let{atr:tq}=g().seriesTypes,{merge:t$}=h();class tJ extends tq{getValues(e,t){let s=super.getValues.apply(this,arguments),a=s.values.length,o=e.yData,i=0,r=t.period-1;if(s){for(;i<a;i++)s.yData[i]=s.values[i][1]/o[r][3]*100,s.values[i][1]=s.yData[i],r++;return s}}}tJ.defaultOptions=t$(tq.defaultOptions,{tooltip:{valueSuffix:"%"}}),g().registerSeriesType("natr",tJ);let{sma:tQ}=g().seriesTypes,{isNumber:t0,error:t1,extend:t2,merge:t3}=h();class t4 extends tQ{getValues(e,t){let s=e.chart.get(t.volumeSeriesID),a=e.xData,o=e.yData,i=[],r=[],n=[],l=!t0(o[0]),p=[],u=1,h=0,d=0,c=0,m=0,g;if(!s)return void t1("Series "+t.volumeSeriesID+" not found! Check `volumeSeriesID`.",!0,e.chart);for(g=s.getColumn("y"),p=[a[0],h],c=l?o[0][3]:o[0],i.push(p),r.push(a[0]),n.push(p[1]);u<o.length;u++)d=(m=l?o[u][3]:o[u])>c?h+g[u]:m===c?h:h-g[u],p=[a[u],d],h=d,c=m,i.push(p),r.push(a[u]),n.push(p[1]);return{values:i,xData:r,yData:n}}}t4.defaultOptions=t3(tQ.defaultOptions,{marker:{enabled:!1},params:{index:void 0,period:void 0,volumeSeriesID:"volume"},tooltip:{valueDecimals:0}}),t2(t4.prototype,{nameComponents:void 0}),g().registerSeriesType("obv",t4);let t5=g().seriesTypes.sma.prototype.pointClass;function t6(e,t){let s=e.series.pointArrayMap,a,o=s.length;for(g().seriesTypes.sma.prototype.pointClass.prototype[t].call(e);o--;)e[a="dataLabel"+s[o]]&&e[a].element&&e[a].destroy(),e[a]=null}let{sma:t9}=g().seriesTypes,{merge:t8,extend:t7,defined:se,isArray:st}=h();class ss extends t9{toYData(e){return[e.P]}translate(){let e=this;super.translate.apply(e),e.points.forEach(function(t){e.pointArrayMap.forEach(function(s){se(t[s])&&(t["plot"+s]=e.yAxis.toPixels(t[s],!0))})}),e.plotEndPoint=e.xAxis.toPixels(e.endPoint,!0)}getGraphPath(e){let t=this,s=[[],[],[],[],[],[],[],[],[]],a=t.pointArrayMap.length,o=t.plotEndPoint,i=[],r,n,l=e.length,p;for(;l--;){for(p=0,n=e[l];p<a;p++)se(n[r=t.pointArrayMap[p]])&&s[p].push({plotX:n.plotX,plotY:n["plot"+r],isNull:!1},{plotX:o,plotY:n["plot"+r],isNull:!1},{plotX:o,plotY:null,isNull:!0});o=n.plotX}return s.forEach(e=>{i=i.concat(super.getGraphPath.call(t,e))}),i}drawDataLabels(){let e,t,s,a,o=this,i=o.pointArrayMap;o.options.dataLabels.enabled&&(t=o.points.length,i.concat([!1]).forEach((r,n)=>{for(a=t;a--;)s=o.points[a],r?(s.y=s[r],s.pivotLine=r,s.plotY=s["plot"+r],e=s["dataLabel"+r],n&&(s["dataLabel"+i[n-1]]=s.dataLabel),s.dataLabels||(s.dataLabels=[]),s.dataLabels[0]=s.dataLabel=e=e&&e.element?e:null):s["dataLabel"+i[n-1]]=s.dataLabel;super.drawDataLabels.call(o)}))}getValues(e,t){let s,a,o,i,r,n,l,p=t.period,u=e.xData,h=e.yData,d=h?h.length:0,c=this[t.algorithm+"Placement"],m=[],g=[],y=[];if(!(u.length<p)&&st(h[0])&&4===h[0].length){for(l=p+1;l<=d+p;l+=p)o=u.slice(l-p-1,l),i=h.slice(l-p-1,l),a=o.length,s=o[a-1],n=c(this.getPivotAndHLC(i)),r=m.push([s].concat(n)),g.push(s),y.push(m[r-1].slice(1));return this.endPoint=o[0]+(s-o[0])/a*p,{values:m,xData:g,yData:y}}}getPivotAndHLC(e){let t=e[e.length-1][3],s=-1/0,a=1/0;return e.forEach(function(e){s=Math.max(s,e[1]),a=Math.min(a,e[2])}),[(s+a+t)/3,s,a,t]}standardPlacement(e){let t=e[1]-e[2];return[null,null,e[0]+t,2*e[0]-e[2],e[0],2*e[0]-e[1],e[0]-t,null,null]}camarillaPlacement(e){let t=e[1]-e[2];return[e[3]+1.5*t,e[3]+1.25*t,e[3]+1.1666*t,e[3]+1.0833*t,e[0],e[3]-1.0833*t,e[3]-1.1666*t,e[3]-1.25*t,e[3]-1.5*t]}fibonacciPlacement(e){let t=e[1]-e[2];return[null,e[0]+t,e[0]+.618*t,e[0]+.382*t,e[0],e[0]-.382*t,e[0]-.618*t,e[0]-t,null]}}ss.defaultOptions=t8(t9.defaultOptions,{params:{index:void 0,period:28,algorithm:"standard"},marker:{enabled:!1},enableMouseTracking:!1,dataLabels:{enabled:!0,format:"{point.pivotLine}"},dataGrouping:{approximation:"averages"}}),t7(ss.prototype,{nameBase:"Pivot Points",pointArrayMap:["R4","R3","R2","R1","P","S1","S2","S3","S4"],pointValKey:"P",pointClass:class extends t5{destroyElements(){t6(this,"destroyElements")}destroy(){t6(this,"destroyElements")}}}),g().registerSeriesType("pivotpoints",ss);let{ema:sa}=g().seriesTypes,{correctFloat:so,extend:si,merge:sr,error:sn}=h();class sl extends sa{getValues(e,t){let s,a,o=t.periods,i=t.index,r=[],n=[],l=[];if(2!==o.length||o[1]<=o[0])return void sn('Error: "PPO requires two periods. Notice, first period should be lower than the second one."');let p=super.getValues.call(this,e,{index:i,period:o[0]}),u=super.getValues.call(this,e,{index:i,period:o[1]});if(!p||!u)return;let h=o[1]-o[0];for(a=0;a<u.yData.length;a++)s=so((p.yData[a+h]-u.yData[a])/u.yData[a]*100),r.push([u.xData[a],s]),n.push(u.xData[a]),l.push(s);return{values:r,xData:n,yData:l}}}sl.defaultOptions=sr(sa.defaultOptions,{params:{period:void 0,periods:[12,26]}}),si(sl.prototype,{nameBase:"PPO",nameComponents:["periods"]}),g().registerSeriesType("ppo",sl);let sp={getArrayExtremes:function(e,t,s){return e.reduce((e,a)=>[Math.min(e[0],a[t]),Math.max(e[1],a[s])],[Number.MAX_VALUE,-Number.MAX_VALUE])}},su={colors:["#2caffe","#544fc5","#00e272","#fe6a35","#6b8abc","#d568fb","#2ee0ca","#fa4b42","#feb56a","#91e8e1"]},{sma:sh}=g().seriesTypes,{merge:sd,extend:sc}=h();class sm extends sh{getValues(e,t){let s,a,o,i,r,n,l,p=t.period,u=e.xData,h=e.yData,d=h?h.length:0,c=[],m=[],g=[];if(!(d<p)){for(l=p;l<=d;l++)i=u[l-1],r=h.slice(l-p,l),s=((a=(n=sp.getArrayExtremes(r,2,1))[1])+(o=n[0]))/2,c.push([i,a,s,o]),m.push(i),g.push([a,s,o]);return{values:c,xData:m,yData:g}}}}sm.defaultOptions=sd(sh.defaultOptions,{params:{index:void 0,period:20},lineWidth:1,topLine:{styles:{lineColor:su.colors[2],lineWidth:1}},bottomLine:{styles:{lineColor:su.colors[8],lineWidth:1}},dataGrouping:{approximation:"averages"}}),sc(sm.prototype,{areaLinesNames:["top","bottom"],nameBase:"Price Channel",nameComponents:["period"],linesApiNames:["topLine","bottomLine"],pointArrayMap:["top","middle","bottom"],pointValKey:"middle"}),_.compose(sm),g().registerSeriesType("pc",sm);let{sma:sg}=g().seriesTypes,{extend:sy,isArray:sf,merge:sx}=h();class sD extends sg{init(){super.init.apply(this,arguments),this.options=sx({topLine:{styles:{lineColor:this.color}},bottomLine:{styles:{lineColor:this.color}}},this.options)}getValues(e,t){let s,a,o,i,r,n,l,p,u=t.period,h=t.topBand,d=t.bottomBand,c=e.xData,m=e.yData,g=m?m.length:0,y=[],f=[],x=[];if(!(c.length<u)&&sf(m[0])&&4===m[0].length){for(p=u;p<=g;p++)r=c.slice(p-u,p),n=m.slice(p-u,p),i=(l=super.getValues({xData:r,yData:n},t)).xData[0],a=(s=l.yData[0])*(1+h),o=s*(1-d),y.push([i,a,s,o]),f.push(i),x.push([a,s,o]);return{values:y,xData:f,yData:x}}}}sD.defaultOptions=sx(sg.defaultOptions,{marker:{enabled:!1},tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b><br/>Top: {point.top}<br/>Middle: {point.middle}<br/>Bottom: {point.bottom}<br/>'},params:{period:20,topBand:.1,bottomBand:.1},bottomLine:{styles:{lineWidth:1,lineColor:void 0}},topLine:{styles:{lineWidth:1}},dataGrouping:{approximation:"averages"}}),sy(sD.prototype,{areaLinesNames:["top","bottom"],linesApiNames:["topLine","bottomLine"],nameComponents:["period","topBand","bottomBand"],nameBase:"Price envelopes",pointArrayMap:["top","middle","bottom"],parallelArrays:["x","y","top","bottom"],pointValKey:"middle"}),_.compose(sD),g().registerSeriesType("priceenvelopes",sD);let{sma:sv}=g().seriesTypes,{merge:sS}=h();function sA(e,t){return parseFloat(e.toFixed(t))}class sb extends sv{constructor(){super(...arguments),this.nameComponents=void 0}getValues(e,t){let s=e.xData,a=e.yData,o=t.maxAccelerationFactor,i=t.increment,r=t.initialAccelerationFactor,n=t.decimals,l=t.index,p=[],u=[],h=[],d=t.initialAccelerationFactor,c,m=a[0][1],g,y,f,x=1,D,v,S,A,b=a[0][2],C,T,P,M;if(!(l>=a.length)){for(M=0;M<l;M++)m=Math.max(a[M][1],m),b=Math.min(a[M][2],sA(b,n));for(c=a[M][1]>b?1:-1,g=m-b,y=(d=t.initialAccelerationFactor)*g,p.push([s[l],b]),u.push(s[l]),h.push(sA(b,n)),M=l+1;M<a.length;M++)if(D=a[M-1][2],v=a[M-2][2],S=a[M-1][1],A=a[M-2][1],T=a[M][1],P=a[M][2],null!==v&&null!==A&&null!==D&&null!==S&&null!==T&&null!==P){var V,L,k,O,w,E,I,N,B,G,W,z,Y,F,X,R,K,H,U,Z,_;w=c,E=x,I=b,N=y,B=v,G=D,W=S,z=A,Y=m,b=w===E?1===w?I+N<Math.min(B,G)?I+N:Math.min(B,G):I+N>Math.max(z,W)?I+N:Math.max(z,W):Y,V=c,L=m,C=1===V?T>L?T:L:P<L?P:L,k=x,O=b,F=f=1===k&&P>O||-1===k&&T>O?1:-1,X=c,R=C,K=m,H=d,U=i,Z=o,_=r,y=(d=F===X?1===F&&R>K||-1===F&&R<K?H===Z?Z:sA(H+U,2):H:_)*(g=C-b),p.push([s[M],sA(b,n)]),u.push(s[M]),h.push(sA(b,n)),x=c,c=f,m=C}return{values:p,xData:u,yData:h}}}}sb.defaultOptions=sS(sv.defaultOptions,{lineWidth:0,marker:{enabled:!0},states:{hover:{lineWidthPlus:0}},params:{period:void 0,initialAccelerationFactor:.02,maxAccelerationFactor:.2,increment:.02,index:2,decimals:4}}),g().registerSeriesType("psar",sb);let{sma:sC}=g().seriesTypes,{isArray:sT,merge:sP,extend:sM}=h();class sV extends sC{getValues(e,t){let s=t.period,a=e.xData,o=e.yData,i=o?o.length:0,r=[],n=[],l=[],p,u=-1,h;if(!(a.length<=s)){for(sT(o[0])&&(u=t.index),p=s;p<i;p++)h=function(e,t,s,a,o){let i,r;return r=o<0?(i=t[s-a])?(t[s]-i)/i*100:null:(i=t[s-a][o])?(t[s][o]-i)/i*100:null,[e[s],r]}(a,o,p,s,u),r.push(h),n.push(h[0]),l.push(h[1]);return{values:r,xData:n,yData:l}}}}sV.defaultOptions=sP(sC.defaultOptions,{params:{index:3,period:9}}),sM(sV.prototype,{nameBase:"Rate of Change"}),g().registerSeriesType("roc",sV);let{sma:sL}=g().seriesTypes,{isNumber:sk,merge:sO}=h();function sw(e,t){return parseFloat(e.toFixed(t))}class sE extends sL{getValues(e,t){let s=t.period,a=e.xData,o=e.yData,i=o?o.length:0,r=t.decimals,n=[],l=[],p=[],u=0,h=0,d=t.index,c=1,m,g,y,f,x,D;if(!(a.length<s)){for(sk(o[0])?D=o:(d=Math.min(d,o[0].length-1),D=o.map(e=>e[d]));c<s;)(g=sw(D[c]-D[c-1],r))>0?u+=g:h+=Math.abs(g),c++;for(y=sw(u/(s-1),r),f=sw(h/(s-1),r),x=c;x<i;x++)(g=sw(D[x]-D[x-1],r))>0?(u=g,h=0):(u=0,h=Math.abs(g)),y=sw((y*(s-1)+u)/s,r),m=0===(f=sw((f*(s-1)+h)/s,r))?100:0===y?0:sw(100-100/(1+y/f),r),n.push([a[x],m]),l.push(a[x]),p.push(m);return{values:n,xData:l,yData:p}}}}sE.defaultOptions=sO(sL.defaultOptions,{params:{decimals:4,index:3}}),g().registerSeriesType("rsi",sE);let{sma:sI}=g().seriesTypes,{extend:sN,isArray:sB,merge:sG}=h();class sW extends sI{init(){super.init.apply(this,arguments),this.options=sG({smoothedLine:{styles:{lineColor:this.color}}},this.options)}getValues(e,t){let s=t.periods[0],a=t.periods[1],o=e.xData,i=e.yData,r=i?i.length:0,n=[],l=[],p=[],u,h,d,c=null,m,g;if(r<s||!sB(i[0])||4!==i[0].length)return;let y=!0,f=0;for(g=s-1;g<r;g++){if(u=i.slice(g-s+1,g+1),h=(m=sp.getArrayExtremes(u,2,1))[0],isNaN(d=(i[g][3]-h)/(m[1]-h)*100)&&y){f++;continue}y&&!isNaN(d)&&(y=!1);let e=l.push(o[g]);isNaN(d)?p.push([p[e-2]&&"number"==typeof p[e-2][0]?p[e-2][0]:null,null]):p.push([d,null]),g>=f+(s-1)+(a-1)&&(c=super.getValues({xData:l.slice(-a),yData:p.slice(-a)},{period:a}).yData[0]),n.push([o[g],d,c]),p[e-1][1]=c}return{values:n,xData:l,yData:p}}}sW.defaultOptions=sG(sI.defaultOptions,{params:{index:void 0,period:void 0,periods:[14,3]},marker:{enabled:!1},tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b><br/>%K: {point.y}<br/>%D: {point.smoothed}<br/>'},smoothedLine:{styles:{lineWidth:1,lineColor:void 0}},dataGrouping:{approximation:"averages"}}),sN(sW.prototype,{areaLinesNames:[],nameComponents:["periods"],nameBase:"Stochastic",pointArrayMap:["y","smoothed"],parallelArrays:["x","y","smoothed"],pointValKey:"y",linesApiNames:["smoothedLine"]}),_.compose(sW),g().registerSeriesType("stochastic",sW);let{sma:sz,stochastic:sY}=g().seriesTypes,{extend:sF,merge:sX}=h();class sR extends sY{getValues(e,t){let s=t.periods,a=super.getValues.call(this,e,t),o={values:[],xData:[],yData:[]};if(!a)return;o.xData=a.xData.slice(s[1]-1);let i=a.yData.slice(s[1]-1),r=sz.prototype.getValues.call(this,{xData:o.xData,yData:i},{index:1,period:s[2]});if(r){for(let e=0,t=o.xData.length;e<t;e++)o.yData[e]=[i[e][1],r.yData[e-s[2]+1]||null],o.values[e]=[o.xData[e],i[e][1],r.yData[e-s[2]+1]||null];return o}}}sR.defaultOptions=sX(sY.defaultOptions,{params:{periods:[14,3,3]}}),sF(sR.prototype,{nameBase:"Slow Stochastic"}),g().registerSeriesType("slowstochastic",sR);let{atr:sK,sma:sH}=g().seriesTypes,{addEvent:sU,correctFloat:sZ,isArray:s_,isNumber:sj,extend:sq,merge:s$,objectEach:sJ}=h();function sQ(e,t){return{index:t,close:e.getColumn("close")[t],x:e.getColumn("x")[t]}}class s0 extends sH{init(){let e=this;super.init.apply(e,arguments);let t=sU(this.chart.constructor,"afterLinkSeries",()=>{if(e.options){let t=e.options;t.cropThreshold=e.linkedParent.options.cropThreshold-(t.params.period-1)}t()},{order:1})}drawGraph(){let e=this,t=e.options,s=e.linkedParent,a=s.getColumn("x"),o=s?s.points:[],i=e.points,r=e.graph,n=o.length-i.length,l=n>0?n:0,p={options:{gapSize:t.gapSize}},u={top:[],bottom:[],intersect:[]},h={top:{styles:{lineWidth:t.lineWidth,lineColor:t.fallingTrendColor||t.color,dashStyle:t.dashStyle}},bottom:{styles:{lineWidth:t.lineWidth,lineColor:t.risingTrendColor||t.color,dashStyle:t.dashStyle}},intersect:t.changeTrendLine},d,c,m,g,y,f,x,D,v,S=i.length;for(;S--;)d=i[S],c=i[S-1],m=o[S-1+l],g=o[S-2+l],y=o[S+l],f=o[S+l+1],x=d.options.color,D={x:d.x,plotX:d.plotX,plotY:d.plotY,isNull:!1},!g&&m&&sj(a[m.index-1])&&(g=sQ(s,m.index-1)),!f&&y&&sj(a[y.index+1])&&(f=sQ(s,y.index+1)),!m&&g&&sj(a[g.index+1])?m=sQ(s,g.index+1):!m&&y&&sj(a[y.index-1])&&(m=sQ(s,y.index-1)),d&&m&&y&&g&&d.x!==m.x&&(d.x===y.x?(g=m,m=y):d.x===g.x?(m=g,g={close:s.getColumn("close")[m.index-1],x:a[m.index-1]}):f&&d.x===f.x&&(m=f,g=y)),c&&g&&m?(v={x:c.x,plotX:c.plotX,plotY:c.plotY,isNull:!1},d.y>=m.close&&c.y>=g.close?(d.color=x||t.fallingTrendColor||t.color,u.top.push(D)):d.y<m.close&&c.y<g.close?(d.color=x||t.risingTrendColor||t.color,u.bottom.push(D)):(u.intersect.push(D),u.intersect.push(v),u.intersect.push(s$(v,{isNull:!0})),d.y>=m.close&&c.y<g.close?(d.color=x||t.fallingTrendColor||t.color,c.color=x||t.risingTrendColor||t.color,u.top.push(D),u.top.push(s$(v,{isNull:!0}))):d.y<m.close&&c.y>=g.close&&(d.color=x||t.risingTrendColor||t.color,c.color=x||t.fallingTrendColor||t.color,u.bottom.push(D),u.bottom.push(s$(v,{isNull:!0}))))):m&&(d.y>=m.close?(d.color=x||t.fallingTrendColor||t.color,u.top.push(D)):(d.color=x||t.risingTrendColor||t.color,u.bottom.push(D)));sJ(u,function(t,s){e.points=t,e.options=s$(h[s].styles,p),e.graph=e["graph"+s+"Line"],sH.prototype.drawGraph.call(e),e["graph"+s+"Line"]=e.graph}),e.points=i,e.options=t,e.graph=r}getValues(e,t){let s=t.period,a=t.multiplier,o=e.xData,i=e.yData,r=[],n=[],l=[],p=0===s?0:s-1,u=[],h=[],d=[],c,m,g,y,f,x,D,v,S;if(!(o.length<=s)&&s_(i[0])&&4===i[0].length&&!(s<0)){for(S=0,d=sK.prototype.getValues.call(this,e,{period:s}).yData;S<d.length;S++)v=i[p+S],D=i[p+S-1]||[],y=u[S-1],f=h[S-1],x=l[S-1],0===S&&(y=f=x=0),c=sZ((v[1]+v[2])/2+a*d[S]),m=sZ((v[1]+v[2])/2-a*d[S]),c<y||D[3]>y?u[S]=c:u[S]=y,m>f||D[3]<f?h[S]=m:h[S]=f,x===y&&v[3]<u[S]||x===f&&v[3]<h[S]?g=u[S]:(x===y&&v[3]>u[S]||x===f&&v[3]>h[S])&&(g=h[S]),r.push([o[p+S],g]),n.push(o[p+S]),l.push(g);return{values:r,xData:n,yData:l}}}}s0.defaultOptions=s$(sH.defaultOptions,{params:{index:void 0,multiplier:3,period:10},risingTrendColor:"#06b535",fallingTrendColor:"#f21313",changeTrendLine:{styles:{lineWidth:1,lineColor:"#333333",dashStyle:"LongDash"}}}),sq(s0.prototype,{nameBase:"Supertrend",nameComponents:["multiplier","period"]}),g().registerSeriesType("supertrend",s0);let{sma:{prototype:{pointClass:s1}}}=g().seriesTypes,s2=class extends s1{destroy(){this.negativeGraphic&&(this.negativeGraphic=this.negativeGraphic.destroy()),super.destroy.apply(this,arguments)}},{animObject:s3}=h(),{noop:s4}=h(),{column:{prototype:s5},sma:s6}=g().seriesTypes,{addEvent:s9,arrayMax:s8,arrayMin:s7,correctFloat:ae,defined:at,error:as,extend:aa,isArray:ao,merge:ai}=h(),ar=Math.abs;class an extends s6{init(e,t){let s=this;delete t.data,super.init.apply(s,arguments);let a=s9(this.chart.constructor,"afterLinkSeries",function(){if(s.options){let t=s.options.params,a=s.linkedParent,o=e.get(t.volumeSeriesID);s.addCustomEvents(a,o)}a()},{order:1});return s}addCustomEvents(e,t){let s=this,a=()=>{s.chart.redraw(),s.setData([]),s.zoneStarts=[],s.zoneLinesSVG&&(s.zoneLinesSVG=s.zoneLinesSVG.destroy())};return s.dataEventsToUnbind.push(s9(e,"remove",function(){a()})),t&&s.dataEventsToUnbind.push(s9(t,"remove",function(){a()})),s}animate(e){let t=this,s=t.chart.inverted,a=t.group,o={};if(!e&&a){let e=s?t.yAxis.top:t.xAxis.left;s?(a["forceAnimate:translateY"]=!0,o.translateY=e):(a["forceAnimate:translateX"]=!0,o.translateX=e),a.animate(o,aa(s3(t.options.animation),{step:function(e,s){t.group.attr({scaleX:Math.max(.001,s.pos)})}}))}}drawPoints(){this.options.volumeDivision.enabled&&(this.posNegVolume(!0,!0),s5.drawPoints.apply(this,arguments),this.posNegVolume(!1,!1)),s5.drawPoints.apply(this,arguments)}posNegVolume(e,t){let s=t?["positive","negative"]:["negative","positive"],a=this.options.volumeDivision,o=this.points.length,i=[],r=[],n=0,l,p,u,h;for(e?(this.posWidths=i,this.negWidths=r):(i=this.posWidths,r=this.negWidths);n<o;n++)(h=this.points[n])[s[0]+"Graphic"]=h.graphic,h.graphic=h[s[1]+"Graphic"],e&&(l=h.shapeArgs.width,(u=(p=this.priceZones[n]).wholeVolumeData)?(i.push(l/u*p.positiveVolumeData),r.push(l/u*p.negativeVolumeData)):(i.push(0),r.push(0))),h.color=t?a.styles.positiveColor:a.styles.negativeColor,h.shapeArgs.width=t?this.posWidths[n]:this.negWidths[n],h.shapeArgs.x=t?h.shapeArgs.x:this.posWidths[n]}translate(){let e=this,t=e.options,s=e.chart,a=e.yAxis,o=a.min,i=e.options.zoneLines,r=e.priceZones,n=0,l,p,u,h,d,c,m,g,y,f;s5.translate.apply(e);let x=e.points;x.length&&(m=t.pointPadding<.5?t.pointPadding:.1,l=s8(e.volumeDataArray),p=s.plotWidth/2,g=s.plotTop,u=ar(a.toPixels(o)-a.toPixels(o+e.rangeStep)),d=ar(a.toPixels(o)-a.toPixels(o+e.rangeStep)),m&&(h=ar(u*(1-2*m)),n=ar((u-h)/2),u=ar(h)),x.forEach(function(t,s){y=t.barX=t.plotX=0,f=t.plotY=a.toPixels(r[s].start)-g-(a.reversed?u-d:u)-n,t.pointWidth=c=ae(p*r[s].wholeVolumeData/l),t.shapeArgs=e.crispCol.apply(e,[y,f,c,u]),t.volumeNeg=r[s].negativeVolumeData,t.volumePos=r[s].positiveVolumeData,t.volumeAll=r[s].wholeVolumeData}),i.enabled&&e.drawZones(s,a,e.zoneStarts,i.styles))}getExtremes(){let e,t=this.options.compare,s=this.options.cumulative;return this.options.compare?(this.options.compare=void 0,e=super.getExtremes(),this.options.compare=t):this.options.cumulative?(this.options.cumulative=!1,e=super.getExtremes(),this.options.cumulative=s):e=super.getExtremes(),e}getValues(e,t){let s=e.getColumn("x",!0),a=e.processedYData,o=this.chart,i=t.ranges,r=[],n=[],l=[],p=o.get(t.volumeSeriesID);if(!e.chart)return void as("Base series not found! In case it has been removed, add a new one.",!0,o);if(!p||!p.getColumn("x",!0).length){let e=p&&!p.getColumn("x",!0).length?" does not contain any data.":" not found! Check `volumeSeriesID`.";as("Series "+t.volumeSeriesID+e,!0,o);return}let u=ao(a[0]);return u&&4!==a[0].length?void as("Type of "+e.name+" series is different than line, OHLC or candlestick.",!0,o):((this.priceZones=this.specifyZones(u,s,a,i,p)).forEach(function(e,t){r.push([e.x,e.end]),n.push(r[t][0]),l.push(r[t][1])}),{values:r,xData:n,yData:l})}specifyZones(e,t,s,a,o){let i=!!e&&function(e){let t=e.length,s=e[0][3],a=s,o=1,i;for(;o<t;o++)(i=e[o][3])<s&&(s=i),i>a&&(a=i);return{min:s,max:a}}(s),r=this.zoneStarts=[],n=[],l=i?i.min:s7(s),p=i?i.max:s8(s),u=0,h=1,d=this.linkedParent;if(!this.options.compareToMain&&d.dataModify&&(l=d.dataModify.modifyValue(l),p=d.dataModify.modifyValue(p)),!at(l)||!at(p))return this.points.length&&(this.setData([]),this.zoneStarts=[],this.zoneLinesSVG&&(this.zoneLinesSVG=this.zoneLinesSVG.destroy())),[];let c=this.rangeStep=ae(p-l)/a;for(r.push(l);u<a-1;u++)r.push(ae(r[u]+c));r.push(p);let m=r.length;for(;h<m;h++)n.push({index:h-1,x:t[0],start:r[h-1],end:r[h]});return this.volumePerZone(e,n,o,t,s)}volumePerZone(e,t,s,a,o){let i,r,n,l,p,u=this,h=s.getColumn("x",!0),d=s.getColumn("y",!0),c=t.length-1,m=o.length,g=d.length;return ar(m-g)&&(a[0]!==h[0]&&d.unshift(0),a[m-1]!==h[g-1]&&d.push(0)),u.volumeDataArray=[],t.forEach(function(t){for(p=0,t.wholeVolumeData=0,t.positiveVolumeData=0,t.negativeVolumeData=0;p<m;p++){r=!1,n=!1,l=e?o[p][3]:o[p],i=p?e?o[p-1][3]:o[p-1]:l;let s=u.linkedParent;!u.options.compareToMain&&s.dataModify&&(l=s.dataModify.modifyValue(l),i=s.dataModify.modifyValue(i)),l<=t.start&&0===t.index&&(r=!0),l>=t.end&&t.index===c&&(n=!0),(l>t.start||r)&&(l<t.end||n)&&(t.wholeVolumeData+=d[p],i>l?t.negativeVolumeData+=d[p]:t.positiveVolumeData+=d[p])}u.volumeDataArray.push(t.wholeVolumeData)}),t}drawZones(e,t,s,a){let o=e.renderer,i=e.plotWidth,r=e.plotTop,n=this.zoneLinesSVG,l=[],p;s.forEach(function(s){p=t.toPixels(s)-r,l=l.concat(e.renderer.crispLine([["M",0,p],["L",i,p]],a.lineWidth))}),n?n.animate({d:l}):n=this.zoneLinesSVG=o.path(l).attr({"stroke-width":a.lineWidth,stroke:a.color,dashstyle:a.dashStyle,zIndex:this.group.zIndex+.1}).add(this.group)}}an.defaultOptions=ai(s6.defaultOptions,{params:{index:void 0,period:void 0,ranges:12,volumeSeriesID:"volume"},zoneLines:{enabled:!0,styles:{color:"#0A9AC9",dashStyle:"LongDash",lineWidth:1}},volumeDivision:{enabled:!0,styles:{positiveColor:"rgba(144, 237, 125, 0.8)",negativeColor:"rgba(244, 91, 91, 0.8)"}},animationLimit:1e3,enableMouseTracking:!1,pointPadding:0,zIndex:-1,crisp:!0,dataGrouping:{enabled:!1},dataLabels:{align:"left",allowOverlap:!0,enabled:!0,format:"P: {point.volumePos:.2f} | N: {point.volumeNeg:.2f}",padding:0,style:{fontSize:"0.5em"},verticalAlign:"top"}}),aa(an.prototype,{nameBase:"Volume by Price",nameComponents:["ranges"],calculateOn:{chart:"render",xAxis:"afterSetExtremes"},pointClass:s2,markerAttribs:s4,drawGraph:s4,getColumnMetrics:s5.getColumnMetrics,crispCol:s5.crispCol}),g().registerSeriesType("vbp",an);let{sma:al}=g().seriesTypes,{error:ap,isArray:au,merge:ah}=h();class ad extends al{getValues(e,t){let s=e.chart,a=e.xData,o=e.yData,i=t.period,r=!0,n;return(n=s.get(t.volumeSeriesID))?(au(o[0])||(r=!1),this.calculateVWAPValues(r,a,o,n,i)):void ap("Series "+t.volumeSeriesID+" not found! Check `volumeSeriesID`.",!0,s)}calculateVWAPValues(e,t,s,a,o){let i,r,n,l,p,u,h=a.getColumn("y"),d=h.length,c=t.length,m=[],g=[],y=[],f=[],x=[];for(p=0,i=c<=d?c:d,u=0;p<i;p++)r=(e?(s[p][1]+s[p][2]+s[p][3])/3:s[p])*h[p],n=u?m[p-1]+r:r,l=u?g[p-1]+h[p]:h[p],m.push(n),g.push(l),x.push([t[p],n/l]),y.push(x[p][0]),f.push(x[p][1]),++u===o&&(u=0);return{values:x,xData:y,yData:f}}}ad.defaultOptions=ah(al.defaultOptions,{params:{index:void 0,period:30,volumeSeriesID:"volume"}}),g().registerSeriesType("vwap",ad);let{sma:ac}=g().seriesTypes,{extend:am,isArray:ag,merge:ay}=h();class af extends ac{getValues(e,t){let s,a,o,i,r,n,l=t.period,p=e.xData,u=e.yData,h=u?u.length:0,d=[],c=[],m=[];if(!(p.length<l)&&ag(u[0])&&4===u[0].length){for(n=l-1;n<h;n++)s=u.slice(n-l+1,n+1),r=(a=sp.getArrayExtremes(s,2,1))[0],o=-(((i=a[1])-u[n][3])/(i-r)*100),p[n]&&(d.push([p[n],o]),c.push(p[n]),m.push(o));return{values:d,xData:c,yData:m}}}}af.defaultOptions=ay(ac.defaultOptions,{params:{index:void 0,period:14}}),am(af.prototype,{nameBase:"Williams %R"}),g().registerSeriesType("williamsr",af);let{sma:ax}=g().seriesTypes,{isArray:aD,merge:av}=h();function aS(e,t,s,a,o){let i=t[a],r=o<0?s[a]:s[a][o];e.push([i,r])}function aA(e,t,s,a){let o=e.length,i=e.reduce(function(e,t,s){return[null,e[1]+t[1]*(s+1)]})[1]/((o+1)/2*o),r=t[a-1];return e.shift(),[r,i]}class ab extends ax{getValues(e,t){let s=t.period,a=e.xData,o=e.yData,i=o?o.length:0,r=a[0],n=[],l=[],p=[],u=1,h=-1,d,c,m=o[0];if(a.length<s)return;aD(o[0])&&(h=t.index,m=o[0][h]);let g=[[r,m]];for(;u!==s;)aS(g,a,o,u,h),u++;for(d=u;d<i;d++)n.push(c=aA(g,a,o,d)),l.push(c[0]),p.push(c[1]),aS(g,a,o,d,h);return n.push(c=aA(g,a,o,d)),l.push(c[0]),p.push(c[1]),{values:n,xData:l,yData:p}}}ab.defaultOptions=av(ax.defaultOptions,{params:{index:3,period:9}}),g().registerSeriesType("wma",ab);let{sma:aC}=g().seriesTypes,{merge:aT,extend:aP}=h();class aM extends aC{getValues(e,t){let s=t.lowIndex,a=t.highIndex,o=t.deviation/100,i={low:1+o,high:1-o},r=e.xData,n=e.yData,l=n?n.length:0,p=[],u=[],h=[],d,c,m,g,y=!1,f=!1;if(!r||r.length<=1||l&&(void 0===n[0][s]||void 0===n[0][a]))return;let x=n[0][s],D=n[0][a];for(d=1;d<l;d++)n[d][s]<=D*i.high?(p.push([r[0],D]),m=[r[d],n[d][s]],g=!0,y=!0):n[d][a]>=x*i.low&&(p.push([r[0],x]),m=[r[d],n[d][a]],g=!1,y=!0),y&&(u.push(p[0][0]),h.push(p[0][1]),c=d++,d=l);for(d=c;d<l;d++)g?(n[d][s]<=m[1]&&(m=[r[d],n[d][s]]),n[d][a]>=m[1]*i.low&&(f=a)):(n[d][a]>=m[1]&&(m=[r[d],n[d][a]]),n[d][s]<=m[1]*i.high&&(f=s)),!1!==f&&(p.push(m),u.push(m[0]),h.push(m[1]),m=[r[d],n[d][f]],g=!g,f=!1);let v=p.length;return 0!==v&&p[v-1][0]<r[l-1]&&(p.push(m),u.push(m[0]),h.push(m[1])),{values:p,xData:u,yData:h}}}aM.defaultOptions=aT(aC.defaultOptions,{params:{index:void 0,period:void 0,lowIndex:2,highIndex:1,deviation:1}}),aP(aM.prototype,{nameComponents:["deviation"],nameSuffixes:["%"],nameBase:"Zig Zag"}),g().registerSeriesType("zigzag",aM);let{sma:aV}=g().seriesTypes,{isArray:aL,extend:ak,merge:aO}=h();class aw extends aV{getRegressionLineParameters(e,t){let s=this.options.params.index,a=function(e,t){return aL(e)?e[t]:e},o=e.reduce(function(e,t){return t+e},0),i=t.reduce(function(e,t){return a(t,s)+e},0),r=o/e.length,n=i/t.length,l,p,u=0,h=0;for(p=0;p<e.length;p++)u+=(l=e[p]-r)*(a(t[p],s)-n),h+=Math.pow(l,2);let d=h?u/h:0;return{slope:d,intercept:n-d*r}}getEndPointY(e,t){return e.slope*t+e.intercept}transformXData(e,t){let s=e[0];return e.map(function(e){return(e-s)/t})}findClosestDistance(e){let t,s,a;for(a=1;a<e.length-1;a++)(t=e[a]-e[a-1])>0&&(void 0===s||t<s)&&(s=t);return s}getValues(e,t){let s,a,o,i,r,n,l,p,u,h=e.xData,d=e.yData,c=t.period,m={xData:[],yData:[],values:[]},g=this.options.params.xAxisUnit||this.findClosestDistance(h);for(a=c-1;a<=h.length-1;a++)o=a-c+1,i=a+1,r=h[a],l=h.slice(o,i),p=d.slice(o,i),u=this.transformXData(l,g),s=this.getRegressionLineParameters(u,p),n=this.getEndPointY(s,u[u.length-1]),m.values.push({regressionLineParameters:s,x:r,y:n}),aL(m.xData)&&m.xData.push(r),aL(m.yData)&&m.yData.push(n);return m}}aw.defaultOptions=aO(aV.defaultOptions,{params:{xAxisUnit:null},tooltip:{valueDecimals:4}}),ak(aw.prototype,{nameBase:"Linear Regression Indicator"}),g().registerSeriesType("linearRegression",aw);let{linearRegression:aE}=g().seriesTypes,{extend:aI,merge:aN}=h();class aB extends aE{getEndPointY(e){return e.slope}}aB.defaultOptions=aN(aE.defaultOptions),aI(aB.prototype,{nameBase:"Linear Regression Slope Indicator"}),g().registerSeriesType("linearRegressionSlope",aB);let{linearRegression:aG}=g().seriesTypes,{extend:aW,merge:az}=h();class aY extends aG{getEndPointY(e){return e.intercept}}aY.defaultOptions=az(aG.defaultOptions),aW(aY.prototype,{nameBase:"Linear Regression Intercept Indicator"}),g().registerSeriesType("linearRegressionIntercept",aY);let{linearRegression:aF}=g().seriesTypes,{extend:aX,merge:aR}=h();class aK extends aF{slopeToAngle(e){return 180/Math.PI*Math.atan(e)}getEndPointY(e){return this.slopeToAngle(e.slope)}}aK.defaultOptions=aR(aF.defaultOptions,{tooltip:{pointFormat:'<span style="color:{point.color}">●</span>{series.name}: <b>{point.y}\xb0</b><br/>'}}),aX(aK.prototype,{nameBase:"Linear Regression Angle Indicator"}),g().registerSeriesType("linearRegressionAngle",aK);let{sma:aH}=g().seriesTypes,{correctFloat:aU,extend:aZ,merge:a_}=h();class aj extends aH{getValues(e,t){let s,a,o,i,r,n,l,p,u,h,d,c=t.period,m=t.factor,g=t.index,y=e.xData,f=e.yData,x=f?f.length:0,D=[],v=[],S=[],A=[],b=[];if(!(x<c)){for(d=0;d<=x;d++){if(d<x){var C,T;C=f[d][2],T=f[d][1],r=aU(T-C)/(aU(T+C)/2)*1e3*m,D.push(f[d][1]*aU(1+2*r)),v.push(f[d][2]*aU(1-2*r))}d>=c&&(u=y.slice(d-c,d),h=f.slice(d-c,d),l=super.getValues.call(this,{xData:u,yData:D.slice(d-c,d)},{period:c}),p=super.getValues.call(this,{xData:u,yData:v.slice(d-c,d)},{period:c}),i=(n=super.getValues.call(this,{xData:u,yData:h},{period:c,index:g})).xData[0],a=l.yData[0],o=p.yData[0],s=n.yData[0],S.push([i,a,s,o]),A.push(i),b.push([a,s,o]))}return{values:S,xData:A,yData:b}}}}aj.defaultOptions=a_(aH.defaultOptions,{params:{period:20,factor:.001,index:3},lineWidth:1,topLine:{styles:{lineWidth:1}},bottomLine:{styles:{lineWidth:1}},dataGrouping:{approximation:"averages"}}),aZ(aj.prototype,{areaLinesNames:["top","bottom"],linesApiNames:["topLine","bottomLine"],nameBase:"Acceleration Bands",nameComponents:["period","factor"],pointArrayMap:["top","middle","bottom"],pointValKey:"middle"}),_.compose(aj),g().registerSeriesType("abands",aj);let{sma:aq}=g().seriesTypes,{extend:a$,merge:aJ,isArray:aQ}=h();class a0 extends aq{constructor(){super(...arguments),this.updateAllPoints=!0}getValues(e,t){let s=e.xData,a=e.yData,o=[],i=[],r=[],n=[],l=t.index,p=0,u=0,h=0,d=0,c=0;for(let e=0;e<s.length;e++)(0===e||s[e]!==s[e-1])&&c++,o.push(c);for(let e=0;e<o.length;e++)h+=o[e],d+=aQ(a[e])?a[e][l]:a[e];let m=h/o.length,g=d/a.length;for(let e=0;e<o.length;e++){let t=aQ(a[e])?a[e][l]:a[e];p+=(o[e]-m)*(t-g),u+=Math.pow(o[e]-m,2)}for(let e=0;e<o.length;e++){if(s[e]===r[r.length-1])continue;let t=s[e],a=g+p/u*(o[e]-m);i.push([t,a]),r.push(t),n.push(a)}return{xData:r,yData:n,values:i}}}a0.defaultOptions=aJ(aq.defaultOptions,{params:{period:void 0,index:3}}),a$(a0.prototype,{nameBase:"Trendline",nameComponents:void 0}),g().registerSeriesType("trendline",a0);let{sma:a1}=g().seriesTypes,{correctFloat:a2,defined:a3,extend:a4,isArray:a5,merge:a6}=h();class a9 extends a1{init(){let e=arguments,t=e[1].params,s=t&&t.average?t.average:void 0;this.averageIndicator=g().seriesTypes[s]||a1,this.averageIndicator.prototype.init.apply(this,e)}calculateDisparityIndex(e,t){return a2(e-t)/t*100}getValues(e,t){let s=t.index,a=e.xData,o=e.yData,i=o?o.length:0,r=[],n=[],l=[],p=this.averageIndicator,u=a5(o[0]),h=p.prototype.getValues(e,t),d=h.yData,c=a.indexOf(h.xData[0]);if(d&&0!==d.length&&a3(s)&&!(o.length<=c)){for(let e=c;e<i;e++){let t=this.calculateDisparityIndex(u?o[e][s]:o[e],d[e-c]);r.push([a[e],t]),n.push(a[e]),l.push(t)}return{values:r,xData:n,yData:l}}}}a9.defaultOptions=a6(a1.defaultOptions,{params:{average:"sma",index:3},marker:{enabled:!1},dataGrouping:{approximation:"averages"}}),a4(a9.prototype,{nameBase:"Disparity Index",nameComponents:["period","average"]}),g().registerSeriesType("disparityindex",a9);let a8=h();a8.MultipleLinesComposition=a8.MultipleLinesComposition||_;let a7=h();return p.default})());