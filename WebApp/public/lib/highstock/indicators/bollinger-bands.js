!/**
 * Highstock JS v12.3.0 (2025-06-21)
 * @module highcharts/indicators/bollinger-bands
 * @requires highcharts
 * @requires highcharts/modules/stock
 *
 * Indicator series type for Highcharts Stock
 *
 * (c) 2010-2025 Paweł Fus
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/indicators/bollinger-bands",["highcharts/highcharts"],function(t){return e(t,t.SeriesRegistry)}):"object"==typeof exports?exports["highcharts/indicators/bollinger-bands"]=e(t._Highcharts,t._Highcharts.SeriesRegistry):t.Highcharts=e(t.Highcharts,t.Highcharts.SeriesRegistry)}("undefined"==typeof window?this:window,(t,e)=>(()=>{"use strict";var i,o={512:t=>{t.exports=e},944:e=>{e.exports=t}},r={};function a(t){var e=r[t];if(void 0!==e)return e.exports;var i=r[t]={exports:{}};return o[t](i,i.exports,a),i.exports}a.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return a.d(e,{a:e}),e},a.d=(t,e)=>{for(var i in e)a.o(e,i)&&!a.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},a.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var s={};a.d(s,{default:()=>A});var n=a(944),p=a.n(n),l=a(512),h=a.n(l);let{sma:{prototype:d}}=h().seriesTypes,{defined:c,error:f,merge:u}=p();!function(t){let e=["bottomLine"],i=["top","bottom"],o=["top"];function r(t){return"plot"+t.charAt(0).toUpperCase()+t.slice(1)}function a(t,e){let i=[];return(t.pointArrayMap||[]).forEach(t=>{t!==e&&i.push(r(t))}),i}function s(){let t=this,e=t.pointValKey,i=t.linesApiNames,o=t.areaLinesNames,s=t.points,n=t.options,p=t.graph,l={options:{gapSize:n.gapSize}},h=[],y=a(t,e),g=s.length,m;if(y.forEach((t,e)=>{for(h[e]=[];g--;)m=s[g],h[e].push({x:m.x,plotX:m.plotX,plotY:m[t],isNull:!c(m[t])});g=s.length}),t.userOptions.fillColor&&o.length){let e=h[y.indexOf(r(o[0]))],i=1===o.length?s:h[y.indexOf(r(o[1]))],a=t.color;t.points=i,t.nextPoints=e,t.color=t.userOptions.fillColor,t.options=u(s,l),t.graph=t.area,t.fillGraph=!0,d.drawGraph.call(t),t.area=t.graph,delete t.nextPoints,delete t.fillGraph,t.color=a}i.forEach((e,i)=>{h[i]?(t.points=h[i],n[e]?t.options=u(n[e].styles,l):f('Error: "There is no '+e+' in DOCS options declared. Check if linesApiNames are consistent with your DOCS line names."'),t.graph=t["graph"+e],d.drawGraph.call(t),t["graph"+e]=t.graph):f('Error: "'+e+" doesn't have equivalent in pointArrayMap. To many elements in linesApiNames relative to pointArrayMap.\"")}),t.points=s,t.options=n,t.graph=p,d.drawGraph.call(t)}function n(t){let e,i=[],o=[];if(t=t||this.points,this.fillGraph&&this.nextPoints){if((e=d.getGraphPath.call(this,this.nextPoints))&&e.length){e[0][0]="L",i=d.getGraphPath.call(this,t),o=e.slice(0,i.length);for(let t=o.length-1;t>=0;t--)i.push(o[t])}}else i=d.getGraphPath.apply(this,arguments);return i}function p(t){let e=[];return(this.pointArrayMap||[]).forEach(i=>{e.push(t[i])}),e}function l(){let t=this.pointArrayMap,e=[],i;e=a(this),d.translate.apply(this,arguments),this.points.forEach(o=>{t.forEach((t,r)=>{i=o[t],this.dataModify&&(i=this.dataModify.modifyValue(i)),null!==i&&(o[e[r]]=this.yAxis.toPixels(i,!0))})})}t.compose=function(t){let r=t.prototype;return r.linesApiNames=r.linesApiNames||e.slice(),r.pointArrayMap=r.pointArrayMap||i.slice(),r.pointValKey=r.pointValKey||"top",r.areaLinesNames=r.areaLinesNames||o.slice(),r.drawGraph=s,r.getGraphPath=n,r.toYData=p,r.translate=l,t}}(i||(i={}));let y=i,{sma:g}=h().seriesTypes,{extend:m,isArray:x,merge:b}=p();class v extends g{init(){h().seriesTypes.sma.prototype.init.apply(this,arguments),this.options=b({topLine:{styles:{lineColor:this.color}},bottomLine:{styles:{lineColor:this.color}}},this.options)}getValues(t,e){let i,o,r,a,s,n,p,l,d,c=e.period,f=e.standardDeviation,u=[],y=[],g=t.xData,m=t.yData,b=m?m.length:0,v=[];if(g.length<c)return;let A=x(m[0]);for(d=c;d<=b;d++)s=g.slice(d-c,d),n=m.slice(d-c,d),a=(l=h().seriesTypes.sma.prototype.getValues.call(this,{xData:s,yData:n},e)).xData[0],i=l.yData[0],p=function(t,e,i,o){let r=t.length,a=0,s=0,n,p=0;for(;a<r;a++)p+=(n=(i?t[a][e]:t[a])-o)*n;return Math.sqrt(p/=r-1)}(n,e.index,A,i),o=i+f*p,r=i-f*p,v.push([a,o,i,r]),u.push(a),y.push([o,i,r]);return{values:v,xData:u,yData:y}}}v.defaultOptions=b(g.defaultOptions,{params:{period:20,standardDeviation:2,index:3},bottomLine:{styles:{lineWidth:1,lineColor:void 0}},topLine:{styles:{lineWidth:1,lineColor:void 0}},tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b><br/>Top: {point.top}<br/>Middle: {point.middle}<br/>Bottom: {point.bottom}<br/>'},marker:{enabled:!1},dataGrouping:{approximation:"averages"}}),m(v.prototype,{areaLinesNames:["top","bottom"],linesApiNames:["topLine","bottomLine"],nameComponents:["period","standardDeviation"],pointArrayMap:["top","middle","bottom"],pointValKey:"middle"}),y.compose(v),h().registerSeriesType("bb",v);let A=p();return s.default})());