{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/supertrend\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Wojciech Chmiel\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/supertrend\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/supertrend\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ supertrend_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/Supertrend/SupertrendIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { atr: ATRIndicator, sma: SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { addEvent, correctFloat, isArray, isNumber, extend, merge, objectEach } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n// Utils:\n/**\n * @private\n */\nfunction createPointObj(mainSeries, index) {\n    return {\n        index,\n        close: mainSeries.getColumn('close')[index],\n        x: mainSeries.getColumn('x')[index]\n    };\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Supertrend series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.supertrend\n *\n * @augments Highcharts.Series\n */\nclass SupertrendIndicator extends SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    init() {\n        const indicator = this;\n        super.init.apply(indicator, arguments);\n        // Only after series are linked add some additional logic/properties.\n        const unbinder = addEvent(this.chart.constructor, 'afterLinkSeries', () => {\n            // Protection for a case where the indicator is being updated,\n            // for a brief moment the indicator is deleted.\n            if (indicator.options) {\n                const options = indicator.options, parentOptions = indicator.linkedParent.options;\n                // Indicator cropThreshold has to be equal linked series one\n                // reduced by period due to points comparison in drawGraph\n                // (#9787)\n                options.cropThreshold = (parentOptions.cropThreshold -\n                    (options.params.period - 1));\n            }\n            unbinder();\n        }, {\n            order: 1\n        });\n    }\n    drawGraph() {\n        const indicator = this, indicOptions = indicator.options, \n        // Series that indicator is linked to\n        mainSeries = indicator.linkedParent, mainXData = mainSeries.getColumn('x'), mainLinePoints = (mainSeries ? mainSeries.points : []), indicPoints = indicator.points, indicPath = indicator.graph, \n        // Points offset between lines\n        tempOffset = mainLinePoints.length - indicPoints.length, offset = tempOffset > 0 ? tempOffset : 0, \n        // @todo: fix when ichi-moku indicator is merged to master.\n        gappedExtend = {\n            options: {\n                gapSize: indicOptions.gapSize\n            }\n        }, \n        // Sorted supertrend points array\n        groupedPoints = {\n            top: [], // Rising trend line points\n            bottom: [], // Falling trend line points\n            intersect: [] // Change trend line points\n        }, \n        // Options for trend lines\n        supertrendLineOptions = {\n            top: {\n                styles: {\n                    lineWidth: indicOptions.lineWidth,\n                    lineColor: (indicOptions.fallingTrendColor ||\n                        indicOptions.color),\n                    dashStyle: indicOptions.dashStyle\n                }\n            },\n            bottom: {\n                styles: {\n                    lineWidth: indicOptions.lineWidth,\n                    lineColor: (indicOptions.risingTrendColor ||\n                        indicOptions.color),\n                    dashStyle: indicOptions.dashStyle\n                }\n            },\n            intersect: indicOptions.changeTrendLine\n        };\n        let // Supertrend line point\n        point, \n        // Supertrend line next point (has smaller x pos than point)\n        nextPoint, \n        // Main series points\n        mainPoint, nextMainPoint, \n        // Used when supertrend and main points are shifted\n        // relative to each other\n        prevMainPoint, prevPrevMainPoint, \n        // Used when particular point color is set\n        pointColor, \n        // Temporary points that fill groupedPoints array\n        newPoint, newNextPoint, indicPointsLen = indicPoints.length;\n        // Loop which sort supertrend points\n        while (indicPointsLen--) {\n            point = indicPoints[indicPointsLen];\n            nextPoint = indicPoints[indicPointsLen - 1];\n            mainPoint = mainLinePoints[indicPointsLen - 1 + offset];\n            nextMainPoint = mainLinePoints[indicPointsLen - 2 + offset];\n            prevMainPoint = mainLinePoints[indicPointsLen + offset];\n            prevPrevMainPoint = mainLinePoints[indicPointsLen + offset + 1];\n            pointColor = point.options.color;\n            newPoint = {\n                x: point.x,\n                plotX: point.plotX,\n                plotY: point.plotY,\n                isNull: false\n            };\n            // When mainPoint is the last one (left plot area edge)\n            // but supertrend has additional one\n            if (!nextMainPoint &&\n                mainPoint &&\n                isNumber(mainXData[mainPoint.index - 1])) {\n                nextMainPoint = createPointObj(mainSeries, mainPoint.index - 1);\n            }\n            // When prevMainPoint is the last one (right plot area edge)\n            // but supertrend has additional one (and points are shifted)\n            if (!prevPrevMainPoint &&\n                prevMainPoint &&\n                isNumber(mainXData[prevMainPoint.index + 1])) {\n                prevPrevMainPoint = createPointObj(mainSeries, prevMainPoint.index + 1);\n            }\n            // When points are shifted (right or left plot area edge)\n            if (!mainPoint &&\n                nextMainPoint &&\n                isNumber(mainXData[nextMainPoint.index + 1])) {\n                mainPoint = createPointObj(mainSeries, nextMainPoint.index + 1);\n            }\n            else if (!mainPoint &&\n                prevMainPoint &&\n                isNumber(mainXData[prevMainPoint.index - 1])) {\n                mainPoint = createPointObj(mainSeries, prevMainPoint.index - 1);\n            }\n            // Check if points are shifted relative to each other\n            if (point &&\n                mainPoint &&\n                prevMainPoint &&\n                nextMainPoint &&\n                point.x !== mainPoint.x) {\n                if (point.x === prevMainPoint.x) {\n                    nextMainPoint = mainPoint;\n                    mainPoint = prevMainPoint;\n                }\n                else if (point.x === nextMainPoint.x) {\n                    mainPoint = nextMainPoint;\n                    nextMainPoint = {\n                        close: mainSeries.getColumn('close')[mainPoint.index - 1],\n                        x: mainXData[mainPoint.index - 1]\n                    };\n                }\n                else if (prevPrevMainPoint && point.x === prevPrevMainPoint.x) {\n                    mainPoint = prevPrevMainPoint;\n                    nextMainPoint = prevMainPoint;\n                }\n            }\n            if (nextPoint && nextMainPoint && mainPoint) {\n                newNextPoint = {\n                    x: nextPoint.x,\n                    plotX: nextPoint.plotX,\n                    plotY: nextPoint.plotY,\n                    isNull: false\n                };\n                if (point.y >= mainPoint.close &&\n                    nextPoint.y >= nextMainPoint.close) {\n                    point.color = (pointColor || indicOptions.fallingTrendColor ||\n                        indicOptions.color);\n                    groupedPoints.top.push(newPoint);\n                }\n                else if (point.y < mainPoint.close &&\n                    nextPoint.y < nextMainPoint.close) {\n                    point.color = (pointColor || indicOptions.risingTrendColor ||\n                        indicOptions.color);\n                    groupedPoints.bottom.push(newPoint);\n                }\n                else {\n                    groupedPoints.intersect.push(newPoint);\n                    groupedPoints.intersect.push(newNextPoint);\n                    // Additional null point to make a gap in line\n                    groupedPoints.intersect.push(merge(newNextPoint, {\n                        isNull: true\n                    }));\n                    if (point.y >= mainPoint.close &&\n                        nextPoint.y < nextMainPoint.close) {\n                        point.color = (pointColor || indicOptions.fallingTrendColor ||\n                            indicOptions.color);\n                        nextPoint.color = (pointColor || indicOptions.risingTrendColor ||\n                            indicOptions.color);\n                        groupedPoints.top.push(newPoint);\n                        groupedPoints.top.push(merge(newNextPoint, {\n                            isNull: true\n                        }));\n                    }\n                    else if (point.y < mainPoint.close &&\n                        nextPoint.y >= nextMainPoint.close) {\n                        point.color = (pointColor || indicOptions.risingTrendColor ||\n                            indicOptions.color);\n                        nextPoint.color = (pointColor || indicOptions.fallingTrendColor ||\n                            indicOptions.color);\n                        groupedPoints.bottom.push(newPoint);\n                        groupedPoints.bottom.push(merge(newNextPoint, {\n                            isNull: true\n                        }));\n                    }\n                }\n            }\n            else if (mainPoint) {\n                if (point.y >= mainPoint.close) {\n                    point.color = (pointColor || indicOptions.fallingTrendColor ||\n                        indicOptions.color);\n                    groupedPoints.top.push(newPoint);\n                }\n                else {\n                    point.color = (pointColor || indicOptions.risingTrendColor ||\n                        indicOptions.color);\n                    groupedPoints.bottom.push(newPoint);\n                }\n            }\n        }\n        // Generate lines:\n        objectEach(groupedPoints, function (values, lineName) {\n            indicator.points = values;\n            indicator.options = merge(supertrendLineOptions[lineName].styles, gappedExtend);\n            indicator.graph = indicator['graph' + lineName + 'Line'];\n            SMAIndicator.prototype.drawGraph.call(indicator);\n            // Now save line\n            indicator['graph' + lineName + 'Line'] = indicator.graph;\n        });\n        // Restore options:\n        indicator.points = indicPoints;\n        indicator.options = indicOptions;\n        indicator.graph = indicPath;\n    }\n    // Supertrend (Multiplier, Period) Formula:\n    // BASIC UPPERBAND = (HIGH + LOW) / 2 + Multiplier * ATR(Period)\n    // BASIC LOWERBAND = (HIGH + LOW) / 2 - Multiplier * ATR(Period)\n    // FINAL UPPERBAND =\n    //     IF(\n    //      Current BASICUPPERBAND  < Previous FINAL UPPERBAND AND\n    //      Previous Close > Previous FINAL UPPERBAND\n    //     ) THEN (Current BASIC UPPERBAND)\n    //     ELSE (Previous FINALUPPERBAND)\n    // FINAL LOWERBAND =\n    //     IF(\n    //      Current BASIC LOWERBAND  > Previous FINAL LOWERBAND AND\n    //      Previous Close < Previous FINAL LOWERBAND\n    //     ) THEN (Current BASIC LOWERBAND)\n    //     ELSE (Previous FINAL LOWERBAND)\n    // SUPERTREND =\n    //     IF(\n    //      Previous Supertrend == Previous FINAL UPPERBAND AND\n    //      Current Close < Current FINAL UPPERBAND\n    //     ) THAN Current FINAL UPPERBAND\n    //     ELSE IF(\n    //      Previous Supertrend == Previous FINAL LOWERBAND AND\n    //      Current Close < Current FINAL LOWERBAND\n    //     ) THAN Current FINAL UPPERBAND\n    //     ELSE IF(\n    //      Previous Supertrend == Previous FINAL UPPERBAND AND\n    //      Current Close > Current FINAL UPPERBAND\n    //     ) THAN Current FINAL LOWERBAND\n    //     ELSE IF(\n    //      Previous Supertrend == Previous FINAL LOWERBAND AND\n    //      Current Close > Current FINAL LOWERBAND\n    //     ) THAN Current FINAL LOWERBAND\n    getValues(series, params) {\n        const period = params.period, multiplier = params.multiplier, xVal = series.xData, yVal = series.yData, \n        // 0- date, 1- Supertrend indicator\n        st = [], xData = [], yData = [], close = 3, low = 2, high = 1, periodsOffset = (period === 0) ? 0 : period - 1, finalUp = [], finalDown = [];\n        let atrData = [], basicUp, basicDown, supertrend, prevFinalUp, prevFinalDown, prevST, // Previous Supertrend\n        prevY, y, i;\n        if ((xVal.length <= period) || !isArray(yVal[0]) ||\n            yVal[0].length !== 4 || period < 0) {\n            return;\n        }\n        atrData = ATRIndicator.prototype.getValues.call(this, series, {\n            period: period\n        }).yData;\n        for (i = 0; i < atrData.length; i++) {\n            y = yVal[periodsOffset + i];\n            prevY = yVal[periodsOffset + i - 1] || [];\n            prevFinalUp = finalUp[i - 1];\n            prevFinalDown = finalDown[i - 1];\n            prevST = yData[i - 1];\n            if (i === 0) {\n                prevFinalUp = prevFinalDown = prevST = 0;\n            }\n            basicUp = correctFloat((y[high] + y[low]) / 2 + multiplier * atrData[i]);\n            basicDown = correctFloat((y[high] + y[low]) / 2 - multiplier * atrData[i]);\n            if ((basicUp < prevFinalUp) ||\n                (prevY[close] > prevFinalUp)) {\n                finalUp[i] = basicUp;\n            }\n            else {\n                finalUp[i] = prevFinalUp;\n            }\n            if ((basicDown > prevFinalDown) ||\n                (prevY[close] < prevFinalDown)) {\n                finalDown[i] = basicDown;\n            }\n            else {\n                finalDown[i] = prevFinalDown;\n            }\n            if (prevST === prevFinalUp && y[close] < finalUp[i] ||\n                prevST === prevFinalDown && y[close] < finalDown[i]) {\n                supertrend = finalUp[i];\n            }\n            else if (prevST === prevFinalUp && y[close] > finalUp[i] ||\n                prevST === prevFinalDown && y[close] > finalDown[i]) {\n                supertrend = finalDown[i];\n            }\n            st.push([xVal[periodsOffset + i], supertrend]);\n            xData.push(xVal[periodsOffset + i]);\n            yData.push(supertrend);\n        }\n        return {\n            values: st,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Supertrend indicator. This series requires the `linkedTo` option to be\n * set and should be loaded after the `stock/indicators/indicators.js` and\n * `stock/indicators/sma.js`.\n *\n * @sample {highstock} stock/indicators/supertrend\n *         Supertrend indicator\n *\n * @extends      plotOptions.sma\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, cropThreshold, negativeColor, colorAxis, joinBy,\n *               keys, navigatorOptions, pointInterval, pointIntervalUnit,\n *               pointPlacement, pointRange, pointStart, showInNavigator,\n *               stacking, threshold\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/supertrend\n * @optionparent plotOptions.supertrend\n */\nSupertrendIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    /**\n     * Parameters used in calculation of Supertrend indicator series points.\n     *\n     * @excluding index\n     */\n    params: {\n        index: void 0, // Unchangeable index, do not inherit (#15362)\n        /**\n         * Multiplier for Supertrend Indicator.\n         */\n        multiplier: 3,\n        /**\n         * The base period for indicator Supertrend Indicator calculations.\n         * This is the number of data points which are taken into account\n         * for the indicator calculations.\n         */\n        period: 10\n    },\n    /**\n     * Color of the Supertrend series line that is beneath the main series.\n     *\n     * @sample {highstock} stock/indicators/supertrend/\n     *         Example with risingTrendColor\n     *\n     * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     */\n    risingTrendColor: \"#06b535\" /* Palette.positiveColor */,\n    /**\n     * Color of the Supertrend series line that is above the main series.\n     *\n     * @sample {highstock} stock/indicators/supertrend/\n     *         Example with fallingTrendColor\n     *\n     * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     */\n    fallingTrendColor: \"#f21313\" /* Palette.negativeColor */,\n    /**\n     * The styles for the Supertrend line that intersect main series.\n     *\n     * @sample {highstock} stock/indicators/supertrend/\n     *         Example with changeTrendLine\n     */\n    changeTrendLine: {\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line.\n             *\n             * @type {Highcharts.ColorString}\n             */\n            lineColor: \"#333333\" /* Palette.neutralColor80 */,\n            /**\n             * The dash or dot style of the grid lines. For possible\n             * values, see\n             * [this demonstration](https://jsfiddle.net/gh/get/library/pure/highcharts/highcharts/tree/master/samples/highcharts/plotoptions/series-dashstyle-all/).\n             *\n             * @sample {highcharts} highcharts/yaxis/gridlinedashstyle/\n             *         Long dashes\n             * @sample {highstock} stock/xaxis/gridlinedashstyle/\n             *         Long dashes\n             *\n             * @type  {Highcharts.DashStyleValue}\n             * @since 7.0.0\n             */\n            dashStyle: 'LongDash'\n        }\n    }\n});\nextend(SupertrendIndicator.prototype, {\n    nameBase: 'Supertrend',\n    nameComponents: ['multiplier', 'period']\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('supertrend', SupertrendIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Supertrend_SupertrendIndicator = ((/* unused pure expression or super */ null && (SupertrendIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `Supertrend indicator` series. If the [type](#series.supertrend.type)\n * option is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.supertrend\n * @since     7.0.0\n * @product   highstock\n * @excluding allAreas, colorAxis, cropThreshold, data, dataParser, dataURL,\n *            joinBy, keys, navigatorOptions, negativeColor, pointInterval,\n *            pointIntervalUnit, pointPlacement, pointRange, pointStart,\n *            showInNavigator, stacking, threshold\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/supertrend\n * @apioption series.supertrend\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/masters/indicators/supertrend.js\n\n\n\n\n/* harmony default export */ const supertrend_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "supertrend_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "atr", "ATRIndicator", "sma", "SMAIndicator", "seriesTypes", "addEvent", "correctFloat", "isArray", "isNumber", "extend", "merge", "objectEach", "createPointObj", "mainSeries", "index", "close", "getColumn", "x", "SupertrendIndicator", "init", "indicator", "apply", "arguments", "unbinder", "chart", "constructor", "options", "cropThreshold", "parentOptions", "linkedParent", "params", "period", "order", "drawGraph", "indicOptions", "mainXData", "mainLinePoints", "points", "indicPoints", "indicPath", "graph", "tempOffset", "length", "offset", "gappedExtend", "gapSize", "groupedPoints", "top", "bottom", "intersect", "supertrendLineOptions", "styles", "lineWidth", "lineColor", "fallingTrendColor", "color", "dashStyle", "risingTrendColor", "changeTrendLine", "point", "nextPoint", "mainPoint", "nextMainPoint", "prevMainPoint", "prevPrevMainPoint", "pointColor", "newPoint", "newNextPoint", "indicPointsLen", "plotX", "plotY", "isNull", "y", "push", "values", "lineName", "getV<PERSON>ues", "series", "multiplier", "xVal", "xData", "yVal", "yData", "st", "periodsOffset", "finalUp", "finalDown", "atrData", "basicUp", "basicDown", "supertrend", "prevFinalUp", "prevFinalDown", "prevST", "prevY", "i", "defaultOptions", "nameBase", "nameComponents", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,mCAAoC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GAC5H,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,mCAAmC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAEhHA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAWjL,GAAM,CAAEE,IAAKC,CAAY,CAAEC,IAAKC,CAAY,CAAE,CAAG,AAACJ,IAA2IK,WAAW,CAElM,CAAEC,SAAAA,CAAQ,CAAEC,aAAAA,CAAY,CAAEC,QAAAA,CAAO,CAAEC,SAAAA,CAAQ,CAAEC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAEC,WAAAA,CAAU,CAAE,CAAId,IAUlF,SAASe,EAAeC,CAAU,CAAEC,CAAK,EACrC,MAAO,CACHA,MAAAA,EACAC,MAAOF,EAAWG,SAAS,CAAC,QAAQ,CAACF,EAAM,CAC3CG,EAAGJ,EAAWG,SAAS,CAAC,IAAI,CAACF,EAAM,AACvC,CACJ,CAeA,MAAMI,UAA4Bf,EAM9BgB,MAAO,CACH,IAAMC,EAAY,IAAI,CACtB,KAAK,CAACD,KAAKE,KAAK,CAACD,EAAWE,WAE5B,IAAMC,EAAWlB,EAAS,IAAI,CAACmB,KAAK,CAACC,WAAW,CAAE,kBAAmB,KAGjE,GAAIL,EAAUM,OAAO,CAAE,CACnB,IAAMA,EAAUN,EAAUM,OAAO,AAIjCA,CAAAA,EAAQC,aAAa,CAAIC,AAJ0BR,EAAUS,YAAY,CAACH,OAAO,CAI1CC,aAAa,CAC/CD,CAAAA,EAAQI,MAAM,CAACC,MAAM,CAAG,CAAA,CACjC,CACAR,GACJ,EAAG,CACCS,MAAO,CACX,EACJ,CACAC,WAAY,CACR,IAAMb,EAAY,IAAI,CAAEc,EAAed,EAAUM,OAAO,CAExDb,EAAaO,EAAUS,YAAY,CAAEM,EAAYtB,EAAWG,SAAS,CAAC,KAAMoB,EAAkBvB,EAAaA,EAAWwB,MAAM,CAAG,EAAE,CAAGC,EAAclB,EAAUiB,MAAM,CAAEE,EAAYnB,EAAUoB,KAAK,CAE/LC,EAAaL,EAAeM,MAAM,CAAGJ,EAAYI,MAAM,CAAEC,EAASF,EAAa,EAAIA,EAAa,EAEhGG,EAAe,CACXlB,QAAS,CACLmB,QAASX,EAAaW,OAAO,AACjC,CACJ,EAEAC,EAAgB,CACZC,IAAK,EAAE,CACPC,OAAQ,EAAE,CACVC,UAAW,EAAE,AACjB,EAEAC,EAAwB,CACpBH,IAAK,CACDI,OAAQ,CACJC,UAAWlB,EAAakB,SAAS,CACjCC,UAAYnB,EAAaoB,iBAAiB,EACtCpB,EAAaqB,KAAK,CACtBC,UAAWtB,EAAasB,SAAS,AACrC,CACJ,EACAR,OAAQ,CACJG,OAAQ,CACJC,UAAWlB,EAAakB,SAAS,CACjCC,UAAYnB,EAAauB,gBAAgB,EACrCvB,EAAaqB,KAAK,CACtBC,UAAWtB,EAAasB,SAAS,AACrC,CACJ,EACAP,UAAWf,EAAawB,eAAe,AAC3C,EAEAC,EAEAC,EAEAC,EAAWC,EAGXC,EAAeC,EAEfC,EAEAC,EAAUC,EAAcC,EAAiB9B,EAAYI,MAAM,CAE3D,KAAO0B,KACHT,EAAQrB,CAAW,CAAC8B,EAAe,CACnCR,EAAYtB,CAAW,CAAC8B,EAAiB,EAAE,CAC3CP,EAAYzB,CAAc,CAACgC,EAAiB,EAAIzB,EAAO,CACvDmB,EAAgB1B,CAAc,CAACgC,EAAiB,EAAIzB,EAAO,CAC3DoB,EAAgB3B,CAAc,CAACgC,EAAiBzB,EAAO,CACvDqB,EAAoB5B,CAAc,CAACgC,EAAiBzB,EAAS,EAAE,CAC/DsB,EAAaN,EAAMjC,OAAO,CAAC6B,KAAK,CAChCW,EAAW,CACPjD,EAAG0C,EAAM1C,CAAC,CACVoD,MAAOV,EAAMU,KAAK,CAClBC,MAAOX,EAAMW,KAAK,CAClBC,OAAQ,CAAA,CACZ,EAGI,CAACT,GACDD,GACArD,EAAS2B,CAAS,CAAC0B,EAAU/C,KAAK,CAAG,EAAE,GACvCgD,CAAAA,EAAgBlD,EAAeC,EAAYgD,EAAU/C,KAAK,CAAG,EAAC,EAI9D,CAACkD,GACDD,GACAvD,EAAS2B,CAAS,CAAC4B,EAAcjD,KAAK,CAAG,EAAE,GAC3CkD,CAAAA,EAAoBpD,EAAeC,EAAYkD,EAAcjD,KAAK,CAAG,EAAC,EAGtE,CAAC+C,GACDC,GACAtD,EAAS2B,CAAS,CAAC2B,EAAchD,KAAK,CAAG,EAAE,EAC3C+C,EAAYjD,EAAeC,EAAYiD,EAAchD,KAAK,CAAG,GAExD,CAAC+C,GACNE,GACAvD,EAAS2B,CAAS,CAAC4B,EAAcjD,KAAK,CAAG,EAAE,GAC3C+C,CAAAA,EAAYjD,EAAeC,EAAYkD,EAAcjD,KAAK,CAAG,EAAC,EAG9D6C,GACAE,GACAE,GACAD,GACAH,EAAM1C,CAAC,GAAK4C,EAAU5C,CAAC,GACnB0C,EAAM1C,CAAC,GAAK8C,EAAc9C,CAAC,EAC3B6C,EAAgBD,EAChBA,EAAYE,GAEPJ,EAAM1C,CAAC,GAAK6C,EAAc7C,CAAC,EAChC4C,EAAYC,EACZA,EAAgB,CACZ/C,MAAOF,EAAWG,SAAS,CAAC,QAAQ,CAAC6C,EAAU/C,KAAK,CAAG,EAAE,CACzDG,EAAGkB,CAAS,CAAC0B,EAAU/C,KAAK,CAAG,EAAE,AACrC,GAEKkD,GAAqBL,EAAM1C,CAAC,GAAK+C,EAAkB/C,CAAC,GACzD4C,EAAYG,EACZF,EAAgBC,IAGpBH,GAAaE,GAAiBD,GAC9BM,EAAe,CACXlD,EAAG2C,EAAU3C,CAAC,CACdoD,MAAOT,EAAUS,KAAK,CACtBC,MAAOV,EAAUU,KAAK,CACtBC,OAAQ,CAAA,CACZ,EACIZ,EAAMa,CAAC,EAAIX,EAAU9C,KAAK,EAC1B6C,EAAUY,CAAC,EAAIV,EAAc/C,KAAK,EAClC4C,EAAMJ,KAAK,CAAIU,GAAc/B,EAAaoB,iBAAiB,EACvDpB,EAAaqB,KAAK,CACtBT,EAAcC,GAAG,CAAC0B,IAAI,CAACP,IAElBP,EAAMa,CAAC,CAAGX,EAAU9C,KAAK,EAC9B6C,EAAUY,CAAC,CAAGV,EAAc/C,KAAK,EACjC4C,EAAMJ,KAAK,CAAIU,GAAc/B,EAAauB,gBAAgB,EACtDvB,EAAaqB,KAAK,CACtBT,EAAcE,MAAM,CAACyB,IAAI,CAACP,KAG1BpB,EAAcG,SAAS,CAACwB,IAAI,CAACP,GAC7BpB,EAAcG,SAAS,CAACwB,IAAI,CAACN,GAE7BrB,EAAcG,SAAS,CAACwB,IAAI,CAAC/D,EAAMyD,EAAc,CAC7CI,OAAQ,CAAA,CACZ,IACIZ,EAAMa,CAAC,EAAIX,EAAU9C,KAAK,EAC1B6C,EAAUY,CAAC,CAAGV,EAAc/C,KAAK,EACjC4C,EAAMJ,KAAK,CAAIU,GAAc/B,EAAaoB,iBAAiB,EACvDpB,EAAaqB,KAAK,CACtBK,EAAUL,KAAK,CAAIU,GAAc/B,EAAauB,gBAAgB,EAC1DvB,EAAaqB,KAAK,CACtBT,EAAcC,GAAG,CAAC0B,IAAI,CAACP,GACvBpB,EAAcC,GAAG,CAAC0B,IAAI,CAAC/D,EAAMyD,EAAc,CACvCI,OAAQ,CAAA,CACZ,KAEKZ,EAAMa,CAAC,CAAGX,EAAU9C,KAAK,EAC9B6C,EAAUY,CAAC,EAAIV,EAAc/C,KAAK,GAClC4C,EAAMJ,KAAK,CAAIU,GAAc/B,EAAauB,gBAAgB,EACtDvB,EAAaqB,KAAK,CACtBK,EAAUL,KAAK,CAAIU,GAAc/B,EAAaoB,iBAAiB,EAC3DpB,EAAaqB,KAAK,CACtBT,EAAcE,MAAM,CAACyB,IAAI,CAACP,GAC1BpB,EAAcE,MAAM,CAACyB,IAAI,CAAC/D,EAAMyD,EAAc,CAC1CI,OAAQ,CAAA,CACZ,OAIHV,IACDF,EAAMa,CAAC,EAAIX,EAAU9C,KAAK,EAC1B4C,EAAMJ,KAAK,CAAIU,GAAc/B,EAAaoB,iBAAiB,EACvDpB,EAAaqB,KAAK,CACtBT,EAAcC,GAAG,CAAC0B,IAAI,CAACP,KAGvBP,EAAMJ,KAAK,CAAIU,GAAc/B,EAAauB,gBAAgB,EACtDvB,EAAaqB,KAAK,CACtBT,EAAcE,MAAM,CAACyB,IAAI,CAACP,KAKtCvD,EAAWmC,EAAe,SAAU4B,CAAM,CAAEC,CAAQ,EAChDvD,EAAUiB,MAAM,CAAGqC,EACnBtD,EAAUM,OAAO,CAAGhB,EAAMwC,CAAqB,CAACyB,EAAS,CAACxB,MAAM,CAAEP,GAClExB,EAAUoB,KAAK,CAAGpB,CAAS,CAAC,QAAUuD,EAAW,OAAO,CACxDxE,EAAaZ,SAAS,CAAC0C,SAAS,CAACxC,IAAI,CAAC2B,GAEtCA,CAAS,CAAC,QAAUuD,EAAW,OAAO,CAAGvD,EAAUoB,KAAK,AAC5D,GAEApB,EAAUiB,MAAM,CAAGC,EACnBlB,EAAUM,OAAO,CAAGQ,EACpBd,EAAUoB,KAAK,CAAGD,CACtB,CAiCAqC,UAAUC,CAAM,CAAE/C,CAAM,CAAE,CACtB,IAAMC,EAASD,EAAOC,MAAM,CAAE+C,EAAahD,EAAOgD,UAAU,CAAEC,EAAOF,EAAOG,KAAK,CAAEC,EAAOJ,EAAOK,KAAK,CAEtGC,EAAK,EAAE,CAAEH,EAAQ,EAAE,CAAEE,EAAQ,EAAE,CAAgCE,EAAgB,AAACrD,AAAW,IAAXA,EAAgB,EAAIA,EAAS,EAAGsD,EAAU,EAAE,CAAEC,EAAY,EAAE,CACxIC,EAAU,EAAE,CAAEC,EAASC,EAAWC,EAAYC,EAAaC,EAAeC,EAC9EC,EAAOtB,EAAGuB,EACV,GAAI,CAAChB,CAAAA,EAAKrC,MAAM,EAAIX,CAAK,GAAOxB,EAAQ0E,CAAI,CAAC,EAAE,GAC3CA,AAAmB,IAAnBA,CAAI,CAAC,EAAE,CAACvC,MAAM,GAAUX,CAAAA,EAAS,CAAA,GAMrC,IAAKgE,EAAI,EAHTR,EAAUtF,EAAaV,SAAS,CAACqF,SAAS,CAACnF,IAAI,CAAC,IAAI,CAAEoF,EAAQ,CAC1D9C,OAAQA,CACZ,GAAGmD,KAAK,CACIa,EAAIR,EAAQ7C,MAAM,CAAEqD,IAC5BvB,EAAIS,CAAI,CAACG,EAAgBW,EAAE,CAC3BD,EAAQb,CAAI,CAACG,EAAgBW,EAAI,EAAE,EAAI,EAAE,CACzCJ,EAAcN,CAAO,CAACU,EAAI,EAAE,CAC5BH,EAAgBN,CAAS,CAACS,EAAI,EAAE,CAChCF,EAASX,CAAK,CAACa,EAAI,EAAE,CACjBA,AAAM,IAANA,GACAJ,CAAAA,EAAcC,EAAgBC,EAAS,CAAA,EAE3CL,EAAUlF,EAAa,AAACkE,CAAAA,CAAC,CAnB+B,EAmBzB,CAAGA,CAAC,CAnBW,EAmBN,AAAD,EAAK,EAAIM,EAAaS,CAAO,CAACQ,EAAE,EACvEN,EAAYnF,EAAa,AAACkE,CAAAA,CAAC,CApB6B,EAoBvB,CAAGA,CAAC,CApBS,EAoBJ,AAAD,EAAK,EAAIM,EAAaS,CAAO,CAACQ,EAAE,EACrE,AAACP,EAAUG,GACVG,CAAK,CAtB2B,EAsBpB,CAAGH,EAChBN,CAAO,CAACU,EAAE,CAAGP,EAGbH,CAAO,CAACU,EAAE,CAAGJ,EAEb,AAACF,EAAYG,GACZE,CAAK,CA7B2B,EA6BpB,CAAGF,EAChBN,CAAS,CAACS,EAAE,CAAGN,EAGfH,CAAS,CAACS,EAAE,CAAGH,EAEfC,IAAWF,GAAenB,CAAC,CAnCM,EAmCC,CAAGa,CAAO,CAACU,EAAE,EAC/CF,IAAWD,GAAiBpB,CAAC,CApCI,EAoCG,CAAGc,CAAS,CAACS,EAAE,CACnDL,EAAaL,CAAO,CAACU,EAAE,CAElBF,CAAAA,IAAWF,GAAenB,CAAC,CAvCC,EAuCM,CAAGa,CAAO,CAACU,EAAE,EACpDF,IAAWD,GAAiBpB,CAAC,CAxCI,EAwCG,CAAGc,CAAS,CAACS,EAAE,AAAD,GAClDL,CAAAA,EAAaJ,CAAS,CAACS,EAAE,AAAD,EAE5BZ,EAAGV,IAAI,CAAC,CAACM,CAAI,CAACK,EAAgBW,EAAE,CAAEL,EAAW,EAC7CV,EAAMP,IAAI,CAACM,CAAI,CAACK,EAAgBW,EAAE,EAClCb,EAAMT,IAAI,CAACiB,GAEf,MAAO,CACHhB,OAAQS,EACRH,MAAOA,EACPE,MAAOA,CACX,EACJ,CACJ,CAyBAhE,EAAoB8E,cAAc,CAAGtF,EAAMP,EAAa6F,cAAc,CAAE,CAMpElE,OAAQ,CACJhB,MAAO,KAAK,EAIZgE,WAAY,EAMZ/C,OAAQ,EACZ,EASA0B,iBAAkB,UASlBH,kBAAmB,UAOnBI,gBAAiB,CACbP,OAAQ,CAIJC,UAAW,EAMXC,UAAW,UAcXG,UAAW,UACf,CACJ,CACJ,GACA/C,EAAOS,EAAoB3B,SAAS,CAAE,CAClC0G,SAAU,aACVC,eAAgB,CAAC,aAAc,SAAS,AAC5C,GACAnG,IAA0IoG,kBAAkB,CAAC,aAAcjF,GAkC9I,IAAMvB,EAAmBE,IAG5C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}