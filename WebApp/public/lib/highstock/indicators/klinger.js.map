{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts Stock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/klinger\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 <PERSON><PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/klinger\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/klinger\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ klinger_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/MultipleLinesComposition.js\n/**\n *\n *  (c) 2010-2025 Wojciech Chmiel\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: { prototype: smaProto } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { defined, error, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\nvar MultipleLinesComposition;\n(function (MultipleLinesComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    /**\n     * Additional lines DOCS names. Elements of linesApiNames array should\n     * be consistent with DOCS line names defined in your implementation.\n     * Notice that linesApiNames should have decreased amount of elements\n     * relative to pointArrayMap (without pointValKey).\n     *\n     * @private\n     * @type {Array<string>}\n     */\n    const linesApiNames = ['bottomLine'];\n    /**\n     * Lines ids. Required to plot appropriate amount of lines.\n     * Notice that pointArrayMap should have more elements than\n     * linesApiNames, because it contains main line and additional lines ids.\n     * Also it should be consistent with amount of lines calculated in\n     * getValues method from your implementation.\n     *\n     * @private\n     * @type {Array<string>}\n     */\n    const pointArrayMap = ['top', 'bottom'];\n    /**\n     * Names of the lines, between which the area should be plotted.\n     * If the drawing of the area should\n     * be disabled for some indicators, leave this option as an empty array.\n     * Names should be the same as the names in the pointArrayMap.\n     *\n     * @private\n     * @type {Array<string>}\n     */\n    const areaLinesNames = ['top'];\n    /**\n     * Main line id.\n     *\n     * @private\n     * @type {string}\n     */\n    const pointValKey = 'top';\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Composition useful for all indicators that have more than one line.\n     * Compose it with your implementation where you will provide the\n     * `getValues` method appropriate to your indicator and `pointArrayMap`,\n     * `pointValKey`, `linesApiNames` properties. Notice that `pointArrayMap`\n     * should be consistent with the amount of lines calculated in the\n     * `getValues` method.\n     *\n     * @private\n     */\n    function compose(IndicatorClass) {\n        const proto = IndicatorClass.prototype;\n        proto.linesApiNames = (proto.linesApiNames ||\n            linesApiNames.slice());\n        proto.pointArrayMap = (proto.pointArrayMap ||\n            pointArrayMap.slice());\n        proto.pointValKey = (proto.pointValKey ||\n            pointValKey);\n        proto.areaLinesNames = (proto.areaLinesNames ||\n            areaLinesNames.slice());\n        proto.drawGraph = indicatorDrawGraph;\n        proto.getGraphPath = indicatorGetGraphPath;\n        proto.toYData = indicatorToYData;\n        proto.translate = indicatorTranslate;\n        return IndicatorClass;\n    }\n    MultipleLinesComposition.compose = compose;\n    /**\n     * Generate the API name of the line\n     *\n     * @private\n     * @param propertyName name of the line\n     */\n    function getLineName(propertyName) {\n        return ('plot' +\n            propertyName.charAt(0).toUpperCase() +\n            propertyName.slice(1));\n    }\n    /**\n     * Create translatedLines Collection based on pointArrayMap.\n     *\n     * @private\n     * @param {string} [excludedValue]\n     *        Main line id\n     * @return {Array<string>}\n     *         Returns translated lines names without excluded value.\n     */\n    function getTranslatedLinesNames(indicator, excludedValue) {\n        const translatedLines = [];\n        (indicator.pointArrayMap || []).forEach((propertyName) => {\n            if (propertyName !== excludedValue) {\n                translatedLines.push(getLineName(propertyName));\n            }\n        });\n        return translatedLines;\n    }\n    /**\n     * Draw main and additional lines.\n     *\n     * @private\n     */\n    function indicatorDrawGraph() {\n        const indicator = this, pointValKey = indicator.pointValKey, linesApiNames = indicator.linesApiNames, areaLinesNames = indicator.areaLinesNames, mainLinePoints = indicator.points, mainLineOptions = indicator.options, mainLinePath = indicator.graph, gappedExtend = {\n            options: {\n                gapSize: mainLineOptions.gapSize\n            }\n        }, \n        // Additional lines point place holders:\n        secondaryLines = [], secondaryLinesNames = getTranslatedLinesNames(indicator, pointValKey);\n        let pointsLength = mainLinePoints.length, point;\n        // Generate points for additional lines:\n        secondaryLinesNames.forEach((plotLine, index) => {\n            // Create additional lines point place holders\n            secondaryLines[index] = [];\n            while (pointsLength--) {\n                point = mainLinePoints[pointsLength];\n                secondaryLines[index].push({\n                    x: point.x,\n                    plotX: point.plotX,\n                    plotY: point[plotLine],\n                    isNull: !defined(point[plotLine])\n                });\n            }\n            pointsLength = mainLinePoints.length;\n        });\n        // Modify options and generate area fill:\n        if (indicator.userOptions.fillColor && areaLinesNames.length) {\n            const index = secondaryLinesNames.indexOf(getLineName(areaLinesNames[0])), secondLinePoints = secondaryLines[index], firstLinePoints = areaLinesNames.length === 1 ?\n                mainLinePoints :\n                secondaryLines[secondaryLinesNames.indexOf(getLineName(areaLinesNames[1]))], originalColor = indicator.color;\n            indicator.points = firstLinePoints;\n            indicator.nextPoints = secondLinePoints;\n            indicator.color = indicator.userOptions.fillColor;\n            indicator.options = merge(mainLinePoints, gappedExtend);\n            indicator.graph = indicator.area;\n            indicator.fillGraph = true;\n            smaProto.drawGraph.call(indicator);\n            indicator.area = indicator.graph;\n            // Clean temporary properties:\n            delete indicator.nextPoints;\n            delete indicator.fillGraph;\n            indicator.color = originalColor;\n        }\n        // Modify options and generate additional lines:\n        linesApiNames.forEach((lineName, i) => {\n            if (secondaryLines[i]) {\n                indicator.points = secondaryLines[i];\n                if (mainLineOptions[lineName]) {\n                    indicator.options = merge(mainLineOptions[lineName].styles, gappedExtend);\n                }\n                else {\n                    error('Error: \"There is no ' + lineName +\n                        ' in DOCS options declared. Check if linesApiNames' +\n                        ' are consistent with your DOCS line names.\"');\n                }\n                indicator.graph = indicator['graph' + lineName];\n                smaProto.drawGraph.call(indicator);\n                // Now save lines:\n                indicator['graph' + lineName] = indicator.graph;\n            }\n            else {\n                error('Error: \"' + lineName + ' doesn\\'t have equivalent ' +\n                    'in pointArrayMap. To many elements in linesApiNames ' +\n                    'relative to pointArrayMap.\"');\n            }\n        });\n        // Restore options and draw a main line:\n        indicator.points = mainLinePoints;\n        indicator.options = mainLineOptions;\n        indicator.graph = mainLinePath;\n        smaProto.drawGraph.call(indicator);\n    }\n    /**\n     * Create the path based on points provided as argument.\n     * If indicator.nextPoints option is defined, create the areaFill.\n     *\n     * @private\n     * @param points Points on which the path should be created\n     */\n    function indicatorGetGraphPath(points) {\n        let areaPath, path = [], higherAreaPath = [];\n        points = points || this.points;\n        // Render Span\n        if (this.fillGraph && this.nextPoints) {\n            areaPath = smaProto.getGraphPath.call(this, this.nextPoints);\n            if (areaPath && areaPath.length) {\n                areaPath[0][0] = 'L';\n                path = smaProto.getGraphPath.call(this, points);\n                higherAreaPath = areaPath.slice(0, path.length);\n                // Reverse points, so that the areaFill will start from the end:\n                for (let i = higherAreaPath.length - 1; i >= 0; i--) {\n                    path.push(higherAreaPath[i]);\n                }\n            }\n        }\n        else {\n            path = smaProto.getGraphPath.apply(this, arguments);\n        }\n        return path;\n    }\n    /**\n     * @private\n     * @param {Highcharts.Point} point\n     *        Indicator point\n     * @return {Array<number>}\n     *         Returns point Y value for all lines\n     */\n    function indicatorToYData(point) {\n        const pointColl = [];\n        (this.pointArrayMap || []).forEach((propertyName) => {\n            pointColl.push(point[propertyName]);\n        });\n        return pointColl;\n    }\n    /**\n     * Add lines plot pixel values.\n     *\n     * @private\n     */\n    function indicatorTranslate() {\n        const pointArrayMap = this.pointArrayMap;\n        let LinesNames = [], value;\n        LinesNames = getTranslatedLinesNames(this);\n        smaProto.translate.apply(this, arguments);\n        this.points.forEach((point) => {\n            pointArrayMap.forEach((propertyName, i) => {\n                value = point[propertyName];\n                // If the modifier, like for example compare exists,\n                // modified the original value by that method, #15867.\n                if (this.dataModify) {\n                    value = this.dataModify.modifyValue(value);\n                }\n                if (value !== null) {\n                    point[LinesNames[i]] = this.yAxis.toPixels(value, true);\n                }\n            });\n        });\n    }\n})(MultipleLinesComposition || (MultipleLinesComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Indicators_MultipleLinesComposition = (MultipleLinesComposition);\n\n;// ./code/es-modules/Stock/Indicators/Klinger/KlingerIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { ema: EMAIndicator, sma: SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { correctFloat, error: KlingerIndicator_error, extend, isArray, merge: KlingerIndicator_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Klinger oscillator series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.klinger\n *\n * @augments Highcharts.Series\n */\nclass KlingerIndicator extends SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    calculateTrend(yVal, i) {\n        const isUpward = yVal[i][1] + yVal[i][2] + yVal[i][3] >\n            yVal[i - 1][1] + yVal[i - 1][2] + yVal[i - 1][3];\n        return isUpward ? 1 : -1;\n    }\n    // Checks if the series and volumeSeries are accessible, number of\n    // points.x is longer than period, is series has OHLC data\n    isValidData(firstYVal) {\n        const chart = this.chart, options = this.options, series = this.linkedParent, isSeriesOHLC = isArray(firstYVal) &&\n            firstYVal.length === 4, volumeSeries = this.volumeSeries ||\n            (this.volumeSeries =\n                chart.get(options.params.volumeSeriesID));\n        if (!volumeSeries) {\n            KlingerIndicator_error('Series ' +\n                options.params.volumeSeriesID +\n                ' not found! Check `volumeSeriesID`.', true, series.chart);\n        }\n        const isLengthValid = [series, volumeSeries].every(function (series) {\n            return series && series.dataTable.rowCount >=\n                options.params.slowAvgPeriod;\n        });\n        return !!(isLengthValid && isSeriesOHLC);\n    }\n    getCM(previousCM, DM, trend, previousTrend, prevoiusDM) {\n        return correctFloat(DM + (trend === previousTrend ? previousCM : prevoiusDM));\n    }\n    getDM(high, low) {\n        return correctFloat(high - low);\n    }\n    getVolumeForce(yVal) {\n        const volumeForce = [];\n        let CM = 0, // Cumulative measurement\n        DM, // Daily measurement\n        force, i = 1, // Start from second point\n        previousCM = 0, previousDM = yVal[0][1] - yVal[0][2], // Initial DM\n        previousTrend = 0, trend;\n        for (i; i < yVal.length; i++) {\n            trend = this.calculateTrend(yVal, i);\n            DM = this.getDM(yVal[i][1], yVal[i][2]);\n            // For the first iteration when the previousTrend doesn't exist,\n            // previousCM doesn't exist either, but it doesn't matter becouse\n            // it's filltered out in the getCM method in else statement,\n            // (in this iteration, previousCM can be raplaced with the DM).\n            CM = this.getCM(previousCM, DM, trend, previousTrend, previousDM);\n            force = this.volumeSeries.getColumn('y')[i] *\n                trend * Math.abs(2 * ((DM / CM) - 1)) * 100;\n            volumeForce.push([force]);\n            // Before next iteration, assign the current as the previous.\n            previousTrend = trend;\n            previousCM = CM;\n            previousDM = DM;\n        }\n        return volumeForce;\n    }\n    getEMA(yVal, prevEMA, SMA, EMApercent, index, i, xVal) {\n        return EMAIndicator.prototype.calculateEma(xVal || [], yVal, typeof i === 'undefined' ? 1 : i, EMApercent, prevEMA, typeof index === 'undefined' ? -1 : index, SMA);\n    }\n    getSMA(period, index, values) {\n        return EMAIndicator.prototype\n            .accumulatePeriodPoints(period, index, values) / period;\n    }\n    getValues(series, params) {\n        const Klinger = [], xVal = series.xData, yVal = series.yData, xData = [], yData = [], calcSingal = [];\n        let KO, i = 0, fastEMA = 0, slowEMA, previousFastEMA = void 0, previousSlowEMA = void 0, signal = null;\n        // If the necessary conditions are not fulfilled, don't proceed.\n        if (!this.isValidData(yVal[0])) {\n            return;\n        }\n        // Calculate the Volume Force array.\n        const volumeForce = this.getVolumeForce(yVal);\n        // Calculate SMA for the first points.\n        const SMAFast = this.getSMA(params.fastAvgPeriod, 0, volumeForce), SMASlow = this.getSMA(params.slowAvgPeriod, 0, volumeForce);\n        // Calculate EMApercent for the first points.\n        const fastEMApercent = 2 / (params.fastAvgPeriod + 1), slowEMApercent = 2 / (params.slowAvgPeriod + 1);\n        // Calculate KO\n        for (i; i < yVal.length; i++) {\n            // Get EMA for fast period.\n            if (i >= params.fastAvgPeriod) {\n                fastEMA = this.getEMA(volumeForce, previousFastEMA, SMAFast, fastEMApercent, 0, i, xVal)[1];\n                previousFastEMA = fastEMA;\n            }\n            // Get EMA for slow period.\n            if (i >= params.slowAvgPeriod) {\n                slowEMA = this.getEMA(volumeForce, previousSlowEMA, SMASlow, slowEMApercent, 0, i, xVal)[1];\n                previousSlowEMA = slowEMA;\n                KO = correctFloat(fastEMA - slowEMA);\n                calcSingal.push(KO);\n                // Calculate signal SMA\n                if (calcSingal.length >= params.signalPeriod) {\n                    signal = calcSingal.slice(-params.signalPeriod)\n                        .reduce((prev, curr) => prev + curr) / params.signalPeriod;\n                }\n                Klinger.push([xVal[i], KO, signal]);\n                xData.push(xVal[i]);\n                yData.push([KO, signal]);\n            }\n        }\n        return {\n            values: Klinger,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Klinger oscillator. This series requires the `linkedTo` option to be set\n * and should be loaded after the `stock/indicators/indicators.js` file.\n *\n * @sample stock/indicators/klinger\n *         Klinger oscillator\n *\n * @extends      plotOptions.sma\n * @since 9.1.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/klinger\n * @optionparent plotOptions.klinger\n */\nKlingerIndicator.defaultOptions = KlingerIndicator_merge(SMAIndicator.defaultOptions, {\n    /**\n     * Parameters used in calculation of Klinger Oscillator.\n     *\n     * @excluding index, period\n     */\n    params: {\n        /**\n         * The fast period for indicator calculations.\n         */\n        fastAvgPeriod: 34,\n        /**\n         * The slow period for indicator calculations.\n         */\n        slowAvgPeriod: 55,\n        /**\n         * The base period for signal calculations.\n         */\n        signalPeriod: 13,\n        /**\n         * The id of another series to use its data as volume data for the\n         * indiator calculation.\n         */\n        volumeSeriesID: 'volume'\n    },\n    signalLine: {\n        /**\n         * Styles for a signal line.\n         */\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line. If not set, it's inherited from\n             * [plotOptions.klinger.color\n             * ](#plotOptions.klinger.color).\n             *\n             * @type {Highcharts.ColorString}\n             */\n            lineColor: '#ff0000'\n        }\n    },\n    dataGrouping: {\n        approximation: 'averages'\n    },\n    tooltip: {\n        pointFormat: '<span style=\"color: {point.color}\">\\u25CF</span>' +\n            '<b> {series.name}</b><br/>' +\n            '<span style=\"color: {point.color}\">Klinger</span>: ' +\n            '{point.y}<br/>' +\n            '<span style=\"color: ' +\n            '{point.series.options.signalLine.styles.lineColor}\">' +\n            'Signal</span>' +\n            ': {point.signal}<br/>'\n    }\n});\nextend(KlingerIndicator.prototype, {\n    areaLinesNames: [],\n    linesApiNames: ['signalLine'],\n    nameBase: 'Klinger',\n    nameComponents: ['fastAvgPeriod', 'slowAvgPeriod'],\n    pointArrayMap: ['y', 'signal'],\n    parallelArrays: ['x', 'y', 'signal'],\n    pointValKey: 'y'\n});\nIndicators_MultipleLinesComposition.compose(KlingerIndicator);\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('klinger', KlingerIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Klinger_KlingerIndicator = ((/* unused pure expression or super */ null && (KlingerIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A Klinger oscillator. If the [type](#series.klinger.type)\n * option is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.klinger\n * @since 9.1.0\n * @product   highstock\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/klinger\n * @apioption series.klinger\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/masters/indicators/klinger.js\n\n\n\n\n/* harmony default export */ const klinger_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "MultipleLinesComposition", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "klinger_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "sma", "sma<PERSON><PERSON><PERSON>", "seriesTypes", "defined", "error", "merge", "linesApiNames", "pointArrayMap", "areaLinesNames", "getLineName", "propertyName", "char<PERSON>t", "toUpperCase", "slice", "getTranslatedLinesNames", "indicator", "excludedValue", "translatedLines", "for<PERSON>ach", "push", "indicatorDrawGraph", "pointVal<PERSON>ey", "mainLinePoints", "points", "mainLineOptions", "options", "mainLinePath", "graph", "gappedExtend", "gapSize", "secondaryLines", "secondaryLinesNames", "pointsLength", "length", "point", "plotLine", "index", "x", "plotX", "plotY", "isNull", "userOptions", "fillColor", "secondLinePoints", "indexOf", "firstLinePoints", "originalColor", "color", "nextPoints", "area", "fillGraph", "drawGraph", "lineName", "i", "styles", "indicatorGetGraphPath", "areaPath", "path", "higherAreaPath", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "apply", "arguments", "indicatorToYData", "pointColl", "indicatorTranslate", "LinesNames", "value", "translate", "dataModify", "modifyValue", "yAxis", "toPixels", "compose", "IndicatorClass", "proto", "toYData", "Indicators_MultipleLinesComposition", "ema", "EMAIndicator", "SMAIndicator", "correctFloat", "KlingerIndicator_error", "extend", "isArray", "KlingerIndicator_merge", "KlingerIndicator", "calculateTrend", "yVal", "isUpward", "isValidData", "firstYVal", "chart", "series", "linkedParent", "isSeriesOHLC", "volumeSeries", "params", "volumeSeriesID", "is<PERSON>ength<PERSON><PERSON>d", "every", "dataTable", "rowCount", "slowAvgPeriod", "getCM", "previousCM", "DM", "trend", "previousTrend", "prevoiusDM", "getDM", "high", "low", "getVolumeForce", "volumeForce", "CM", "previousDM", "getColumn", "Math", "abs", "getEMA", "prevEMA", "SMA", "EMApercent", "xVal", "calculateEma", "getSMA", "period", "values", "accumulatePeriodPoints", "getV<PERSON>ues", "<PERSON><PERSON>", "xData", "yData", "calcSingal", "KO", "fastEMA", "slowEMA", "previousFastEMA", "previousSlowEMA", "signal", "SMAFast", "fastAvgPeriod", "SMASlow", "fastEMApercent", "slowEMApercent", "signalPeriod", "reduce", "prev", "curr", "defaultOptions", "signalLine", "lineWidth", "lineColor", "dataGrouping", "approximation", "tooltip", "pointFormat", "nameBase", "nameComponents", "parallelArrays", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,gCAAiC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACzH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,gCAAgC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAE7GA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IA0GNC,EA1GUC,EAAuB,CAE/B,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAab,OAAO,CAG5B,IAAIC,EAASS,CAAwB,CAACE,EAAS,CAAG,CAGjDZ,QAAS,CAAC,CACX,EAMA,OAHAS,CAAmB,CAACG,EAAS,CAACX,EAAQA,EAAOD,OAAO,CAAEW,GAG/CV,EAAOD,OAAO,AACtB,CAMCW,EAAoBI,CAAC,CAAG,AAACd,IACxB,IAAIe,EAASf,GAAUA,EAAOgB,UAAU,CACvC,IAAOhB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAU,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAAClB,EAASoB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACtB,EAASqB,IAC5EE,OAAOC,cAAc,CAACxB,EAASqB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAajL,GAAM,CAAEE,IAAK,CAAET,UAAWU,CAAQ,CAAE,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAE1L,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAE,CAAIR,KAOnC,AAAC,SAAU3B,CAAwB,EAoB/B,IAAMoC,EAAgB,CAAC,aAAa,CAW9BC,EAAgB,CAAC,MAAO,SAAS,CAUjCC,EAAiB,CAAC,MAAM,CA8C9B,SAASC,EAAYC,CAAY,EAC7B,MAAQ,OACJA,EAAaC,MAAM,CAAC,GAAGC,WAAW,GAClCF,EAAaG,KAAK,CAAC,EAC3B,CAUA,SAASC,EAAwBC,CAAS,CAAEC,CAAa,EACrD,IAAMC,EAAkB,EAAE,CAM1B,MALA,AAACF,CAAAA,EAAUR,aAAa,EAAI,EAAE,AAAD,EAAGW,OAAO,CAAC,AAACR,IACjCA,IAAiBM,GACjBC,EAAgBE,IAAI,CAACV,EAAYC,GAEzC,GACOO,CACX,CAMA,SAASG,IACL,IAAML,EAAY,IAAI,CAAEM,EAAcN,EAAUM,WAAW,CAAEf,EAAgBS,EAAUT,aAAa,CAAEE,EAAiBO,EAAUP,cAAc,CAAEc,EAAiBP,EAAUQ,MAAM,CAAEC,EAAkBT,EAAUU,OAAO,CAAEC,EAAeX,EAAUY,KAAK,CAAEC,EAAe,CACpQH,QAAS,CACLI,QAASL,EAAgBK,OAAO,AACpC,CACJ,EAEAC,EAAiB,EAAE,CAAEC,EAAsBjB,EAAwBC,EAAWM,GAC1EW,EAAeV,EAAeW,MAAM,CAAEC,EAiB1C,GAfAH,EAAoBb,OAAO,CAAC,CAACiB,EAAUC,KAGnC,IADAN,CAAc,CAACM,EAAM,CAAG,EAAE,CACnBJ,KACHE,EAAQZ,CAAc,CAACU,EAAa,CACpCF,CAAc,CAACM,EAAM,CAACjB,IAAI,CAAC,CACvBkB,EAAGH,EAAMG,CAAC,CACVC,MAAOJ,EAAMI,KAAK,CAClBC,MAAOL,CAAK,CAACC,EAAS,CACtBK,OAAQ,CAACrC,EAAQ+B,CAAK,CAACC,EAAS,CACpC,GAEJH,EAAeV,EAAeW,MAAM,AACxC,GAEIlB,EAAU0B,WAAW,CAACC,SAAS,EAAIlC,EAAeyB,MAAM,CAAE,CAC1D,IAA2EU,EAAmBb,CAAc,CAA9FC,EAAoBa,OAAO,CAACnC,EAAYD,CAAc,CAAC,EAAE,GAA4C,CAAEqC,EAAkBrC,AAA0B,IAA1BA,EAAeyB,MAAM,CACxJX,EACAQ,CAAc,CAACC,EAAoBa,OAAO,CAACnC,EAAYD,CAAc,CAAC,EAAE,GAAG,CAAEsC,EAAgB/B,EAAUgC,KAAK,AAChHhC,CAAAA,EAAUQ,MAAM,CAAGsB,EACnB9B,EAAUiC,UAAU,CAAGL,EACvB5B,EAAUgC,KAAK,CAAGhC,EAAU0B,WAAW,CAACC,SAAS,CACjD3B,EAAUU,OAAO,CAAGpB,EAAMiB,EAAgBM,GAC1Cb,EAAUY,KAAK,CAAGZ,EAAUkC,IAAI,CAChClC,EAAUmC,SAAS,CAAG,CAAA,EACtBjD,EAASkD,SAAS,CAAC1D,IAAI,CAACsB,GACxBA,EAAUkC,IAAI,CAAGlC,EAAUY,KAAK,CAEhC,OAAOZ,EAAUiC,UAAU,CAC3B,OAAOjC,EAAUmC,SAAS,CAC1BnC,EAAUgC,KAAK,CAAGD,CACtB,CAEAxC,EAAcY,OAAO,CAAC,CAACkC,EAAUC,KACzBvB,CAAc,CAACuB,EAAE,EACjBtC,EAAUQ,MAAM,CAAGO,CAAc,CAACuB,EAAE,CAChC7B,CAAe,CAAC4B,EAAS,CACzBrC,EAAUU,OAAO,CAAGpB,EAAMmB,CAAe,CAAC4B,EAAS,CAACE,MAAM,CAAE1B,GAG5DxB,EAAM,uBAAyBgD,EAAzB,gGAIVrC,EAAUY,KAAK,CAAGZ,CAAS,CAAC,QAAUqC,EAAS,CAC/CnD,EAASkD,SAAS,CAAC1D,IAAI,CAACsB,GAExBA,CAAS,CAAC,QAAUqC,EAAS,CAAGrC,EAAUY,KAAK,EAG/CvB,EAAM,WAAagD,EAAb,4GAId,GAEArC,EAAUQ,MAAM,CAAGD,EACnBP,EAAUU,OAAO,CAAGD,EACpBT,EAAUY,KAAK,CAAGD,EAClBzB,EAASkD,SAAS,CAAC1D,IAAI,CAACsB,EAC5B,CAQA,SAASwC,EAAsBhC,CAAM,EACjC,IAAIiC,EAAUC,EAAO,EAAE,CAAEC,EAAiB,EAAE,CAG5C,GAFAnC,EAASA,GAAU,IAAI,CAACA,MAAM,CAE1B,IAAI,CAAC2B,SAAS,EAAI,IAAI,CAACF,UAAU,CAEjC,CAAA,GAAIQ,AADJA,CAAAA,EAAWvD,EAAS0D,YAAY,CAAClE,IAAI,CAAC,IAAI,CAAE,IAAI,CAACuD,UAAU,CAAA,GAC3CQ,EAASvB,MAAM,CAAE,CAC7BuB,CAAQ,CAAC,EAAE,CAAC,EAAE,CAAG,IACjBC,EAAOxD,EAAS0D,YAAY,CAAClE,IAAI,CAAC,IAAI,CAAE8B,GACxCmC,EAAiBF,EAAS3C,KAAK,CAAC,EAAG4C,EAAKxB,MAAM,EAE9C,IAAK,IAAIoB,EAAIK,EAAezB,MAAM,CAAG,EAAGoB,GAAK,EAAGA,IAC5CI,EAAKtC,IAAI,CAACuC,CAAc,CAACL,EAAE,CAEnC,CAAA,MAGAI,EAAOxD,EAAS0D,YAAY,CAACC,KAAK,CAAC,IAAI,CAAEC,WAE7C,OAAOJ,CACX,CAQA,SAASK,EAAiB5B,CAAK,EAC3B,IAAM6B,EAAY,EAAE,CAIpB,MAHA,AAAC,CAAA,IAAI,CAACxD,aAAa,EAAI,EAAE,AAAD,EAAGW,OAAO,CAAC,AAACR,IAChCqD,EAAU5C,IAAI,CAACe,CAAK,CAACxB,EAAa,CACtC,GACOqD,CACX,CAMA,SAASC,IACL,IAAMzD,EAAgB,IAAI,CAACA,aAAa,CACpC0D,EAAa,EAAE,CAAEC,EACrBD,EAAanD,EAAwB,IAAI,EACzCb,EAASkE,SAAS,CAACP,KAAK,CAAC,IAAI,CAAEC,WAC/B,IAAI,CAACtC,MAAM,CAACL,OAAO,CAAC,AAACgB,IACjB3B,EAAcW,OAAO,CAAC,CAACR,EAAc2C,KACjCa,EAAQhC,CAAK,CAACxB,EAAa,CAGvB,IAAI,CAAC0D,UAAU,EACfF,CAAAA,EAAQ,IAAI,CAACE,UAAU,CAACC,WAAW,CAACH,EAAK,EAEzCA,AAAU,OAAVA,GACAhC,CAAAA,CAAK,CAAC+B,CAAU,CAACZ,EAAE,CAAC,CAAG,IAAI,CAACiB,KAAK,CAACC,QAAQ,CAACL,EAAO,CAAA,EAAI,CAE9D,EACJ,EACJ,CA3KAhG,EAAyBsG,OAAO,CAhBhC,SAAiBC,CAAc,EAC3B,IAAMC,EAAQD,EAAelF,SAAS,CAatC,OAZAmF,EAAMpE,aAAa,CAAIoE,EAAMpE,aAAa,EACtCA,EAAcO,KAAK,GACvB6D,EAAMnE,aAAa,CAAImE,EAAMnE,aAAa,EACtCA,EAAcM,KAAK,GACvB6D,EAAMrD,WAAW,CAAIqD,EAAMrD,WAAW,EAtBtB,MAwBhBqD,EAAMlE,cAAc,CAAIkE,EAAMlE,cAAc,EACxCA,EAAeK,KAAK,GACxB6D,EAAMvB,SAAS,CAAG/B,EAClBsD,EAAMf,YAAY,CAAGJ,EACrBmB,EAAMC,OAAO,CAAGb,EAChBY,EAAMP,SAAS,CAAGH,EACXS,CACX,CA6KJ,EAAGvG,GAA6BA,CAAAA,EAA2B,CAAC,CAAA,GAM/B,IAAM0G,EAAuC1G,EAapE,CAAE2G,IAAKC,CAAY,CAAE9E,IAAK+E,CAAY,CAAE,CAAG,AAAChF,IAA2IG,WAAW,CAElM,CAAE8E,aAAAA,CAAY,CAAE5E,MAAO6E,CAAsB,CAAEC,OAAAA,CAAM,CAAEC,QAAAA,CAAO,CAAE9E,MAAO+E,CAAsB,CAAE,CAAIvF,GAezG,OAAMwF,UAAyBN,EAM3BO,eAAeC,CAAI,CAAElC,CAAC,CAAE,CAGpB,OAAOmC,AAFUD,CAAI,CAAClC,EAAE,CAAC,EAAE,CAAGkC,CAAI,CAAClC,EAAE,CAAC,EAAE,CAAGkC,CAAI,CAAClC,EAAE,CAAC,EAAE,CACjDkC,CAAI,CAAClC,EAAI,EAAE,CAAC,EAAE,CAAGkC,CAAI,CAAClC,EAAI,EAAE,CAAC,EAAE,CAAGkC,CAAI,CAAClC,EAAI,EAAE,CAAC,EAAE,CAClC,EAAI,EAC1B,CAGAoC,YAAYC,CAAS,CAAE,CACnB,IAAMC,EAAQ,IAAI,CAACA,KAAK,CAAElE,EAAU,IAAI,CAACA,OAAO,CAAEmE,EAAS,IAAI,CAACC,YAAY,CAAEC,EAAeX,EAAQO,IACjGA,AAAqB,IAArBA,EAAUzD,MAAM,CAAQ8D,EAAe,IAAI,CAACA,YAAY,EACvD,CAAA,IAAI,CAACA,YAAY,CACdJ,EAAMvG,GAAG,CAACqC,EAAQuE,MAAM,CAACC,cAAc,CAAA,EAU/C,OATI,AAACF,GACDd,EAAuB,UACnBxD,EAAQuE,MAAM,CAACC,cAAc,CAC7B,sCAAuC,CAAA,EAAML,EAAOD,KAAK,EAM1D,CAAC,CAAEO,CAAAA,AAJY,CAACN,EAAQG,EAAa,CAACI,KAAK,CAAC,SAAUP,CAAM,EAC/D,OAAOA,GAAUA,EAAOQ,SAAS,CAACC,QAAQ,EACtC5E,EAAQuE,MAAM,CAACM,aAAa,AACpC,IAC2BR,CAAW,CAC1C,CACAS,MAAMC,CAAU,CAAEC,CAAE,CAAEC,CAAK,CAAEC,CAAa,CAAEC,CAAU,CAAE,CACpD,OAAO5B,EAAayB,EAAMC,CAAAA,IAAUC,EAAgBH,EAAaI,CAAS,EAC9E,CACAC,MAAMC,CAAI,CAAEC,CAAG,CAAE,CACb,OAAO/B,EAAa8B,EAAOC,EAC/B,CACAC,eAAezB,CAAI,CAAE,CACjB,IAAM0B,EAAc,EAAE,CAClBC,EAAK,EACTT,EACOpD,EAAI,EACXmD,EAAa,EAAGW,EAAa5B,CAAI,CAAC,EAAE,CAAC,EAAE,CAAGA,CAAI,CAAC,EAAE,CAAC,EAAE,CACpDoB,EAAgB,EAAGD,EACnB,KAAQrD,EAAIkC,EAAKtD,MAAM,CAAEoB,IACrBqD,EAAQ,IAAI,CAACpB,cAAc,CAACC,EAAMlC,GAClCoD,EAAK,IAAI,CAACI,KAAK,CAACtB,CAAI,CAAClC,EAAE,CAAC,EAAE,CAAEkC,CAAI,CAAClC,EAAE,CAAC,EAAE,EAKtC6D,EAAK,IAAI,CAACX,KAAK,CAACC,EAAYC,EAAIC,EAAOC,EAAeQ,GAGtDF,EAAY9F,IAAI,CAAC,CAFT,IAAI,CAAC4E,YAAY,CAACqB,SAAS,CAAC,IAAI,CAAC/D,EAAE,CACvCqD,EAAQW,KAAKC,GAAG,CAAC,EAAK,CAAA,AAACb,EAAKS,EAAM,CAAA,GAAM,IACpB,EAExBP,EAAgBD,EAChBF,EAAaU,EACbC,EAAaV,EAEjB,OAAOQ,CACX,CACAM,OAAOhC,CAAI,CAAEiC,CAAO,CAAEC,CAAG,CAAEC,CAAU,CAAEtF,CAAK,CAAEiB,CAAC,CAAEsE,CAAI,CAAE,CACnD,OAAO7C,EAAavF,SAAS,CAACqI,YAAY,CAACD,GAAQ,EAAE,CAAEpC,EAAM,AAAa,KAAA,IAANlC,EAAoB,EAAIA,EAAGqE,EAAYF,EAAS,AAAiB,KAAA,IAAVpF,EAAwB,GAAKA,EAAOqF,EACnK,CACAI,OAAOC,CAAM,CAAE1F,CAAK,CAAE2F,CAAM,CAAE,CAC1B,OAAOjD,EAAavF,SAAS,CACxByI,sBAAsB,CAACF,EAAQ1F,EAAO2F,GAAUD,CACzD,CACAG,UAAUrC,CAAM,CAAEI,CAAM,CAAE,CACtB,IAAMkC,EAAU,EAAE,CAAEP,EAAO/B,EAAOuC,KAAK,CAAE5C,EAAOK,EAAOwC,KAAK,CAAED,EAAQ,EAAE,CAAEC,EAAQ,EAAE,CAAEC,EAAa,EAAE,CACjGC,EAAIjF,EAAI,EAAGkF,EAAU,EAAGC,EAASC,EAA0BC,EAA0BC,EAAS,KAElG,GAAI,CAAC,IAAI,CAAClD,WAAW,CAACF,CAAI,CAAC,EAAE,EACzB,OAGJ,IAAM0B,EAAc,IAAI,CAACD,cAAc,CAACzB,GAElCqD,EAAU,IAAI,CAACf,MAAM,CAAC7B,EAAO6C,aAAa,CAAE,EAAG5B,GAAc6B,EAAU,IAAI,CAACjB,MAAM,CAAC7B,EAAOM,aAAa,CAAE,EAAGW,GAE5G8B,EAAiB,EAAK/C,CAAAA,EAAO6C,aAAa,CAAG,CAAA,EAAIG,EAAiB,EAAKhD,CAAAA,EAAOM,aAAa,CAAG,CAAA,EAEpG,KAAQjD,EAAIkC,EAAKtD,MAAM,CAAEoB,IAEjBA,GAAK2C,EAAO6C,aAAa,EAEzBJ,CAAAA,EADAF,EAAU,IAAI,CAAChB,MAAM,CAACN,EAAawB,EAAiBG,EAASG,EAAgB,EAAG1F,EAAGsE,EAAK,CAAC,EAAE,AACnE,EAGxBtE,GAAK2C,EAAOM,aAAa,GAEzBoC,EADAF,EAAU,IAAI,CAACjB,MAAM,CAACN,EAAayB,EAAiBI,EAASE,EAAgB,EAAG3F,EAAGsE,EAAK,CAAC,EAAE,CAG3FU,EAAWlH,IAAI,CADfmH,EAAKtD,EAAauD,EAAUC,IAGxBH,EAAWpG,MAAM,EAAI+D,EAAOiD,YAAY,EACxCN,CAAAA,EAASN,EAAWxH,KAAK,CAAC,CAACmF,EAAOiD,YAAY,EACzCC,MAAM,CAAC,CAACC,EAAMC,IAASD,EAAOC,GAAQpD,EAAOiD,YAAY,AAAD,EAEjEf,EAAQ/G,IAAI,CAAC,CAACwG,CAAI,CAACtE,EAAE,CAAEiF,EAAIK,EAAO,EAClCR,EAAMhH,IAAI,CAACwG,CAAI,CAACtE,EAAE,EAClB+E,EAAMjH,IAAI,CAAC,CAACmH,EAAIK,EAAO,GAG/B,MAAO,CACHZ,OAAQG,EACRC,MAAOA,EACPC,MAAOA,CACX,CACJ,CACJ,CAoBA/C,EAAiBgE,cAAc,CAAGjE,EAAuBL,EAAasE,cAAc,CAAE,CAMlFrD,OAAQ,CAIJ6C,cAAe,GAIfvC,cAAe,GAIf2C,aAAc,GAKdhD,eAAgB,QACpB,EACAqD,WAAY,CAIRhG,OAAQ,CAIJiG,UAAW,EAQXC,UAAW,SACf,CACJ,EACAC,aAAc,CACVC,cAAe,UACnB,EACAC,QAAS,CACLC,YAAa,kPAQjB,CACJ,GACA1E,EAAOG,EAAiB9F,SAAS,CAAE,CAC/BiB,eAAgB,EAAE,CAClBF,cAAe,CAAC,aAAa,CAC7BuJ,SAAU,UACVC,eAAgB,CAAC,gBAAiB,gBAAgB,CAClDvJ,cAAe,CAAC,IAAK,SAAS,CAC9BwJ,eAAgB,CAAC,IAAK,IAAK,SAAS,CACpC1I,YAAa,GACjB,GACAuD,EAAoCJ,OAAO,CAACa,GAC5CtF,IAA0IiK,kBAAkB,CAAC,UAAW3E,GA8B3I,IAAM1F,EAAgBE,IAGzC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}