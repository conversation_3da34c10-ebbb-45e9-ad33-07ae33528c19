!/**
 * Highstock JS v12.3.0 (2025-06-21)
 * @module highcharts/indicators/price-envelopes
 * @requires highcharts
 * @requires highcharts/modules/stock
 *
 * Indicator series type for Highcharts Stock
 *
 * (c) 2010-2025 Paweł Fus
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/indicators/price-envelopes",["highcharts/highcharts"],function(t){return e(t,t.SeriesRegistry)}):"object"==typeof exports?exports["highcharts/indicators/price-envelopes"]=e(t._Highcharts,t._Highcharts.SeriesRegistry):t.Highcharts=e(t.Highcharts,t.Highcharts.SeriesRegistry)}("undefined"==typeof window?this:window,(t,e)=>(()=>{"use strict";var o,i={512:t=>{t.exports=e},944:e=>{e.exports=t}},r={};function a(t){var e=r[t];if(void 0!==e)return e.exports;var o=r[t]={exports:{}};return i[t](o,o.exports,a),o.exports}a.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return a.d(e,{a:e}),e},a.d=(t,e)=>{for(var o in e)a.o(e,o)&&!a.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},a.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var s={};a.d(s,{default:()=>A});var n=a(944),p=a.n(n),l=a(512),h=a.n(l);let{sma:{prototype:c}}=h().seriesTypes,{defined:d,error:u,merge:f}=p();!function(t){let e=["bottomLine"],o=["top","bottom"],i=["top"];function r(t){return"plot"+t.charAt(0).toUpperCase()+t.slice(1)}function a(t,e){let o=[];return(t.pointArrayMap||[]).forEach(t=>{t!==e&&o.push(r(t))}),o}function s(){let t=this,e=t.pointValKey,o=t.linesApiNames,i=t.areaLinesNames,s=t.points,n=t.options,p=t.graph,l={options:{gapSize:n.gapSize}},h=[],y=a(t,e),m=s.length,g;if(y.forEach((t,e)=>{for(h[e]=[];m--;)g=s[m],h[e].push({x:g.x,plotX:g.plotX,plotY:g[t],isNull:!d(g[t])});m=s.length}),t.userOptions.fillColor&&i.length){let e=h[y.indexOf(r(i[0]))],o=1===i.length?s:h[y.indexOf(r(i[1]))],a=t.color;t.points=o,t.nextPoints=e,t.color=t.userOptions.fillColor,t.options=f(s,l),t.graph=t.area,t.fillGraph=!0,c.drawGraph.call(t),t.area=t.graph,delete t.nextPoints,delete t.fillGraph,t.color=a}o.forEach((e,o)=>{h[o]?(t.points=h[o],n[e]?t.options=f(n[e].styles,l):u('Error: "There is no '+e+' in DOCS options declared. Check if linesApiNames are consistent with your DOCS line names."'),t.graph=t["graph"+e],c.drawGraph.call(t),t["graph"+e]=t.graph):u('Error: "'+e+" doesn't have equivalent in pointArrayMap. To many elements in linesApiNames relative to pointArrayMap.\"")}),t.points=s,t.options=n,t.graph=p,c.drawGraph.call(t)}function n(t){let e,o=[],i=[];if(t=t||this.points,this.fillGraph&&this.nextPoints){if((e=c.getGraphPath.call(this,this.nextPoints))&&e.length){e[0][0]="L",o=c.getGraphPath.call(this,t),i=e.slice(0,o.length);for(let t=i.length-1;t>=0;t--)o.push(i[t])}}else o=c.getGraphPath.apply(this,arguments);return o}function p(t){let e=[];return(this.pointArrayMap||[]).forEach(o=>{e.push(t[o])}),e}function l(){let t=this.pointArrayMap,e=[],o;e=a(this),c.translate.apply(this,arguments),this.points.forEach(i=>{t.forEach((t,r)=>{o=i[t],this.dataModify&&(o=this.dataModify.modifyValue(o)),null!==o&&(i[e[r]]=this.yAxis.toPixels(o,!0))})})}t.compose=function(t){let r=t.prototype;return r.linesApiNames=r.linesApiNames||e.slice(),r.pointArrayMap=r.pointArrayMap||o.slice(),r.pointValKey=r.pointValKey||"top",r.areaLinesNames=r.areaLinesNames||i.slice(),r.drawGraph=s,r.getGraphPath=n,r.toYData=p,r.translate=l,t}}(o||(o={}));let y=o,{sma:m}=h().seriesTypes,{extend:g,isArray:x,merge:b}=p();class v extends m{init(){super.init.apply(this,arguments),this.options=b({topLine:{styles:{lineColor:this.color}},bottomLine:{styles:{lineColor:this.color}}},this.options)}getValues(t,e){let o,i,r,a,s,n,p,l,h=e.period,c=e.topBand,d=e.bottomBand,u=t.xData,f=t.yData,y=f?f.length:0,m=[],g=[],b=[];if(!(u.length<h)&&x(f[0])&&4===f[0].length){for(l=h;l<=y;l++)s=u.slice(l-h,l),n=f.slice(l-h,l),a=(p=super.getValues({xData:s,yData:n},e)).xData[0],i=(o=p.yData[0])*(1+c),r=o*(1-d),m.push([a,i,o,r]),g.push(a),b.push([i,o,r]);return{values:m,xData:g,yData:b}}}}v.defaultOptions=b(m.defaultOptions,{marker:{enabled:!1},tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b><br/>Top: {point.top}<br/>Middle: {point.middle}<br/>Bottom: {point.bottom}<br/>'},params:{period:20,topBand:.1,bottomBand:.1},bottomLine:{styles:{lineWidth:1,lineColor:void 0}},topLine:{styles:{lineWidth:1}},dataGrouping:{approximation:"averages"}}),g(v.prototype,{areaLinesNames:["top","bottom"],linesApiNames:["topLine","bottomLine"],nameComponents:["period","topBand","bottomBand"],nameBase:"Price envelopes",pointArrayMap:["top","middle","bottom"],parallelArrays:["x","y","top","bottom"],pointValKey:"middle"}),y.compose(v),h().registerSeriesType("priceenvelopes",v);let A=p();return s.default})());