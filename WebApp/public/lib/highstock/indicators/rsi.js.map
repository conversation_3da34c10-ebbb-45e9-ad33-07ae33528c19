{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/rsi\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Paweł Fus\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/rsi\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/rsi\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ rsi_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/RSI/RSIIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { isNumber, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n// Utils:\n/**\n *\n */\nfunction toFixed(a, n) {\n    return parseFloat(a.toFixed(n));\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The RSI series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.rsi\n *\n * @augments Highcharts.Series\n */\nclass RSIIndicator extends SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, decimals = params.decimals, \n        // RSI starts calculations from the second point\n        // Cause we need to calculate change between two points\n        RSI = [], xData = [], yData = [];\n        let gain = 0, loss = 0, index = params.index, range = 1, RSIPoint, change, avgGain, avgLoss, i, values;\n        if ((xVal.length < period)) {\n            return;\n        }\n        if (isNumber(yVal[0])) {\n            values = yVal;\n        }\n        else {\n            // In case of the situation, where the series type has data length\n            // longer then 4 (HLC, range), this ensures that we are not trying\n            // to reach the index out of bounds\n            index = Math.min(index, yVal[0].length - 1);\n            values = yVal\n                .map((value) => value[index]);\n        }\n        // Calculate changes for first N points\n        while (range < period) {\n            change = toFixed(values[range] - values[range - 1], decimals);\n            if (change > 0) {\n                gain += change;\n            }\n            else {\n                loss += Math.abs(change);\n            }\n            range++;\n        }\n        // Average for first n-1 points:\n        avgGain = toFixed(gain / (period - 1), decimals);\n        avgLoss = toFixed(loss / (period - 1), decimals);\n        for (i = range; i < yValLen; i++) {\n            change = toFixed(values[i] - values[i - 1], decimals);\n            if (change > 0) {\n                gain = change;\n                loss = 0;\n            }\n            else {\n                gain = 0;\n                loss = Math.abs(change);\n            }\n            // Calculate smoothed averages, RS, RSI values:\n            avgGain = toFixed((avgGain * (period - 1) + gain) / period, decimals);\n            avgLoss = toFixed((avgLoss * (period - 1) + loss) / period, decimals);\n            // If average-loss is equal zero, then by definition RSI is set\n            // to 100:\n            if (avgLoss === 0) {\n                RSIPoint = 100;\n                // If average-gain is equal zero, then by definition RSI is set\n                // to 0:\n            }\n            else if (avgGain === 0) {\n                RSIPoint = 0;\n            }\n            else {\n                RSIPoint = toFixed(100 - (100 / (1 + (avgGain / avgLoss))), decimals);\n            }\n            RSI.push([xVal[i], RSIPoint]);\n            xData.push(xVal[i]);\n            yData.push(RSIPoint);\n        }\n        return {\n            values: RSI,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Relative strength index (RSI) technical indicator. This series\n * requires the `linkedTo` option to be set and should be loaded after\n * the `stock/indicators/indicators.js` file.\n *\n * @sample stock/indicators/rsi\n *         RSI indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/rsi\n * @optionparent plotOptions.rsi\n */\nRSIIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    params: {\n        decimals: 4,\n        index: 3\n    }\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('rsi', RSIIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const RSI_RSIIndicator = ((/* unused pure expression or super */ null && (RSIIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `RSI` series. If the [type](#series.rsi.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.rsi\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/rsi\n * @apioption series.rsi\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/masters/indicators/rsi.js\n\n\n\n\n/* harmony default export */ const rsi_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "rsi_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "sma", "SMAIndicator", "seriesTypes", "isNumber", "merge", "toFixed", "parseFloat", "RSIIndicator", "getV<PERSON>ues", "series", "params", "period", "xVal", "xData", "yVal", "yData", "yValLen", "length", "decimals", "RSI", "gain", "loss", "index", "range", "RSIPoint", "change", "avgGain", "avgLoss", "i", "values", "Math", "min", "map", "value", "abs", "push", "defaultOptions", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,4BAA6B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACrH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,4BAA4B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAEzGA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAWjL,GAAM,CAAEE,IAAKC,CAAY,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAE/K,CAAEC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAE,CAAIP,IAU7B,SAASQ,EAAQxB,CAAC,CAAEJ,CAAC,EACjB,OAAO6B,WAAWzB,EAAEwB,OAAO,CAAC5B,GAChC,CAeA,MAAM8B,UAAqBN,EAMvBO,UAAUC,CAAM,CAAEC,CAAM,CAAE,CACtB,IAAMC,EAASD,EAAOC,MAAM,CAAEC,EAAOH,EAAOI,KAAK,CAAEC,EAAOL,EAAOM,KAAK,CAAEC,EAAUF,EAAOA,EAAKG,MAAM,CAAG,EAAGC,EAAWR,EAAOQ,QAAQ,CAGpIC,EAAM,EAAE,CAAEN,EAAQ,EAAE,CAAEE,EAAQ,EAAE,CAC5BK,EAAO,EAAGC,EAAO,EAAGC,EAAQZ,EAAOY,KAAK,CAAEC,EAAQ,EAAGC,EAAUC,EAAQC,EAASC,EAASC,EAAGC,EAChG,IAAKjB,CAAAA,EAAKK,MAAM,CAAGN,CAAK,GAexB,IAZIR,EAASW,CAAI,CAAC,EAAE,EAChBe,EAASf,GAMTQ,EAAQQ,KAAKC,GAAG,CAACT,EAAOR,CAAI,CAAC,EAAE,CAACG,MAAM,CAAG,GACzCY,EAASf,EACJkB,GAAG,CAAC,AAACC,GAAUA,CAAK,CAACX,EAAM,GAG7BC,EAAQZ,GAEPc,AADJA,CAAAA,EAASpB,EAAQwB,CAAM,CAACN,EAAM,CAAGM,CAAM,CAACN,EAAQ,EAAE,CAAEL,EAAQ,EAC/C,EACTE,GAAQK,EAGRJ,GAAQS,KAAKI,GAAG,CAACT,GAErBF,IAKJ,IAFAG,EAAUrB,EAAQe,EAAQT,CAAAA,EAAS,CAAA,EAAIO,GACvCS,EAAUtB,EAAQgB,EAAQV,CAAAA,EAAS,CAAA,EAAIO,GAClCU,EAAIL,EAAOK,EAAIZ,EAASY,IAErBH,AADJA,CAAAA,EAASpB,EAAQwB,CAAM,CAACD,EAAE,CAAGC,CAAM,CAACD,EAAI,EAAE,CAAEV,EAAQ,EACvC,GACTE,EAAOK,EACPJ,EAAO,IAGPD,EAAO,EACPC,EAAOS,KAAKI,GAAG,CAACT,IAGpBC,EAAUrB,EAAQ,AAACqB,CAAAA,EAAWf,CAAAA,EAAS,CAAA,EAAKS,CAAG,EAAKT,EAAQO,GAKxDM,EADAG,AAAY,IAHhBA,CAAAA,EAAUtB,EAAQ,AAACsB,CAAAA,EAAWhB,CAAAA,EAAS,CAAA,EAAKU,CAAG,EAAKV,EAAQO,EAAQ,EAIrD,IAINQ,AAAY,IAAZA,EACM,EAGArB,EAAQ,IAAO,IAAO,CAAA,EAAKqB,EAAUC,CAAO,EAAKT,GAEhEC,EAAIgB,IAAI,CAAC,CAACvB,CAAI,CAACgB,EAAE,CAAEJ,EAAS,EAC5BX,EAAMsB,IAAI,CAACvB,CAAI,CAACgB,EAAE,EAClBb,EAAMoB,IAAI,CAACX,GAEf,MAAO,CACHK,OAAQV,EACRN,MAAOA,EACPE,MAAOA,CACX,EACJ,CACJ,CAqBAR,EAAa6B,cAAc,CAAGhC,EAAMH,EAAamC,cAAc,CAAE,CAC7D1B,OAAQ,CACJQ,SAAU,EACVI,MAAO,CACX,CACJ,GACAvB,IAA0IsC,kBAAkB,CAAC,MAAO9B,GA+BvI,IAAMZ,EAAYE,IAGrC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}