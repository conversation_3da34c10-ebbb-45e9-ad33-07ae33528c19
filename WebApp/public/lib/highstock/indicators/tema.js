!/**
 * Highstock JS v12.3.0 (2025-06-21)
 * @module highcharts/indicators/tema
 * @requires highcharts
 * @requires highcharts/modules/stock
 *
 * Indicator series type for Highcharts Stock
 *
 * (c) 2010-2025 <PERSON><PERSON><PERSON>
 *
 * License: www.highcharts.com/license
 */function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(e._Highcharts,e._Highcharts.SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/indicators/tema",["highcharts/highcharts"],function(e){return t(e,e.SeriesRegistry)}):"object"==typeof exports?exports["highcharts/indicators/tema"]=t(e._Highcharts,e._Highcharts.SeriesRegistry):e.Highcharts=t(e.Highcharts,e.Highcharts.SeriesRegistry)}("undefined"==typeof window?this:window,(e,t)=>(()=>{"use strict";var r={512:e=>{e.exports=t},944:t=>{t.exports=e}},l={};function s(e){var t=l[e];if(void 0!==t)return t.exports;var i=l[e]={exports:{}};return r[e](i,i.exports,s),i.exports}s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},s.d=(e,t)=>{for(var r in t)s.o(t,r)&&!s.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var i={};s.d(i,{default:()=>g});var a=s(944),o=s.n(a),n=s(512),h=s.n(n);let{ema:v}=h().seriesTypes,{correctFloat:p,isArray:u,merge:c}=o();class d extends v{getEMA(e,t,r,l,s,i){return super.calculateEma(i||[],e,void 0===s?1:s,this.EMApercent,t,void 0===l?-1:l,r)}getTemaPoint(e,t,r,l){return[e[l-3],p(3*r.level1-3*r.level2+r.level3)]}getValues(e,t){let r=t.period,l=2*r,s=3*r,i=e.xData,a=e.yData,o=a?a.length:0,n=[],h=[],v=[],p=[],c=[],d={},g=-1,f=0,y=0,x,m,E,M;if(this.EMApercent=2/(r+1),!(o<3*r-2)){for(u(a[0])&&(g=t.index?t.index:0),y=(f=super.accumulatePeriodPoints(r,g,a))/r,f=0,E=r;E<o+3;E++)E<o+1&&(d.level1=this.getEMA(a,x,y,g,E)[1],p.push(d.level1)),x=d.level1,E<l?f+=d.level1:(E===l&&(y=f/r,f=0),d.level1=p[E-r-1],d.level2=this.getEMA([d.level1],m,y)[1],c.push(d.level2),m=d.level2,E<s?f+=d.level2:(E===s&&(y=f/r),E===o+1&&(d.level1=p[E-r-1],d.level2=this.getEMA([d.level1],m,y)[1],c.push(d.level2)),d.level1=p[E-r-2],d.level2=c[E-2*r-1],d.level3=this.getEMA([d.level2],d.prevLevel3,y)[1],(M=this.getTemaPoint(i,s,d,E))&&(n.push(M),h.push(M[0]),v.push(M[1])),d.prevLevel3=d.level3));return{values:n,xData:h,yData:v}}}}d.defaultOptions=c(v.defaultOptions),h().registerSeriesType("tema",d);let g=o();return i.default})());