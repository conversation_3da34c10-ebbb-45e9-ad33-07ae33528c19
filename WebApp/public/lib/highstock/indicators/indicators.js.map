{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/indicators\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 <PERSON><PERSON><PERSON>, <PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Chart\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/indicators\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"Chart\"],amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/indicators\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Chart\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Chart\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__960__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ }),\n\n/***/ 960:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__960__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ indicators_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Chart\"],\"commonjs\":[\"highcharts\",\"Chart\"],\"commonjs2\":[\"highcharts\",\"Chart\"],\"root\":[\"Highcharts\",\"Chart\"]}\nvar highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_ = __webpack_require__(960);\nvar highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default = /*#__PURE__*/__webpack_require__.n(highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/SMA/SMAIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { line: LineSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { addEvent, fireEvent, error, extend, isArray, merge, pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/**\n *\n * Return the parent series values in the legacy two-dimensional yData\n * format\n * @private\n */\nconst tableToMultiYData = (series, processed) => {\n    const yData = [], pointArrayMap = series.pointArrayMap, table = processed && series.dataTable.modified || series.dataTable;\n    if (!pointArrayMap) {\n        return series.getColumn('y', processed);\n    }\n    const columns = pointArrayMap.map((key) => series.getColumn(key, processed));\n    for (let i = 0; i < table.rowCount; i++) {\n        const values = pointArrayMap.map((key, colIndex) => columns[colIndex]?.[i] || 0);\n        yData.push(values);\n    }\n    return yData;\n};\n/* *\n *\n *  Class\n *\n * */\n/**\n * The SMA series type.\n *\n * @private\n */\nclass SMAIndicator extends LineSeries {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    destroy() {\n        this.dataEventsToUnbind.forEach(function (unbinder) {\n            unbinder();\n        });\n        super.destroy.apply(this, arguments);\n    }\n    /**\n     * @private\n     */\n    getName() {\n        const params = [];\n        let name = this.name;\n        if (!name) {\n            (this.nameComponents || []).forEach(function (component, index) {\n                params.push(this.options.params[component] +\n                    pick(this.nameSuffixes[index], ''));\n            }, this);\n            name = (this.nameBase || this.type.toUpperCase()) +\n                (this.nameComponents ? ' (' + params.join(', ') + ')' : '');\n        }\n        return name;\n    }\n    /**\n     * @private\n     */\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData || [], yVal = series.yData, yValLen = yVal.length, SMA = [], xData = [], yData = [];\n        let i, index = -1, range = 0, SMAPoint, sum = 0;\n        if (xVal.length < period) {\n            return;\n        }\n        // Switch index for OHLC / Candlestick / Arearange\n        if (isArray(yVal[0])) {\n            index = params.index ? params.index : 0;\n        }\n        // Accumulate first N-points\n        while (range < period - 1) {\n            sum += index < 0 ? yVal[range] : yVal[range][index];\n            range++;\n        }\n        // Calculate value one-by-one for each period in visible data\n        for (i = range; i < yValLen; i++) {\n            sum += index < 0 ? yVal[i] : yVal[i][index];\n            SMAPoint = [xVal[i], sum / period];\n            SMA.push(SMAPoint);\n            xData.push(SMAPoint[0]);\n            yData.push(SMAPoint[1]);\n            sum -= (index < 0 ?\n                yVal[i - range] :\n                yVal[i - range][index]);\n        }\n        return {\n            values: SMA,\n            xData: xData,\n            yData: yData\n        };\n    }\n    /**\n     * @private\n     */\n    init(chart, options) {\n        const indicator = this;\n        super.init.call(indicator, chart, options);\n        // Only after series are linked indicator can be processed.\n        const linkedSeriesUnbiner = addEvent((highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default()), 'afterLinkSeries', function ({ isUpdating }) {\n            // #18643 indicator shouldn't recalculate\n            // values while series updating.\n            if (isUpdating) {\n                return;\n            }\n            const hasEvents = !!indicator.dataEventsToUnbind.length;\n            if (indicator.linkedParent) {\n                if (!hasEvents) {\n                    // No matter which indicator, always recalculate after\n                    // updating the data.\n                    indicator.dataEventsToUnbind.push(addEvent(indicator.linkedParent, 'updatedData', function () {\n                        indicator.recalculateValues();\n                    }));\n                    // Some indicators (like VBP) requires an additional\n                    // event (afterSetExtremes) to properly show the data.\n                    if (indicator.calculateOn.xAxis) {\n                        indicator.dataEventsToUnbind.push(addEvent(indicator.linkedParent.xAxis, indicator.calculateOn.xAxis, function () {\n                            indicator.recalculateValues();\n                        }));\n                    }\n                }\n                // Most indicators are being calculated on chart's init.\n                if (indicator.calculateOn.chart === 'init') {\n                    // When closestPointRange is set, it is an indication\n                    // that `Series.processData` has run. If it hasn't we\n                    // need to `recalculateValues`.\n                    if (!indicator.closestPointRange) {\n                        indicator.recalculateValues();\n                    }\n                }\n                else if (!hasEvents) {\n                    // Some indicators (like VBP) has to recalculate their\n                    // values after other chart's events (render).\n                    const unbinder = addEvent(indicator.chart, indicator.calculateOn.chart, function () {\n                        indicator.recalculateValues();\n                        // Call this just once.\n                        unbinder();\n                    });\n                }\n            }\n            else {\n                return error('Series ' +\n                    indicator.options.linkedTo +\n                    ' not found! Check `linkedTo`.', false, chart);\n            }\n        }, {\n            order: 0\n        });\n        // Make sure we find series which is a base for an indicator\n        // chart.linkSeries();\n        indicator.dataEventsToUnbind = [];\n        indicator.eventsToUnbind.push(linkedSeriesUnbiner);\n    }\n    /**\n     * @private\n     */\n    recalculateValues() {\n        const croppedDataValues = [], indicator = this, table = this.dataTable, oldData = indicator.points || [], oldDataLength = indicator.dataTable.rowCount, emptySet = {\n            values: [],\n            xData: [],\n            yData: []\n        };\n        let overwriteData = true, oldFirstPointIndex, oldLastPointIndex, min, max;\n        // For the newer data table, temporarily set the parent series `yData`\n        // to the legacy format that is documented for custom indicators, and\n        // get the xData from the data table\n        const yData = indicator.linkedParent.yData, processedYData = indicator.linkedParent.processedYData;\n        indicator.linkedParent.xData = indicator.linkedParent\n            .getColumn('x');\n        indicator.linkedParent.yData = tableToMultiYData(indicator.linkedParent);\n        indicator.linkedParent.processedYData = tableToMultiYData(indicator.linkedParent, true);\n        // Updating an indicator with redraw=false may destroy data.\n        // If there will be a following update for the parent series,\n        // we will try to access Series object without any properties\n        // (except for prototyped ones). This is what happens\n        // for example when using Axis.setDataGrouping(). See #16670\n        const processedData = indicator.linkedParent.options &&\n            // #18176, #18177 indicators should work with empty dataset\n            indicator.linkedParent.dataTable.rowCount ?\n            (indicator.getValues(indicator.linkedParent, indicator.options.params) || emptySet) : emptySet;\n        // Reset\n        delete indicator.linkedParent.xData;\n        indicator.linkedParent.yData = yData;\n        indicator.linkedParent.processedYData = processedYData;\n        const pointArrayMap = indicator.pointArrayMap || ['y'], valueColumns = {};\n        // Split legacy twodimensional values into value columns\n        processedData.yData\n            .forEach((values) => {\n            pointArrayMap.forEach((key, index) => {\n                const column = valueColumns[key] || [];\n                column.push(isArray(values) ? values[index] : values);\n                if (!valueColumns[key]) {\n                    valueColumns[key] = column;\n                }\n            });\n        });\n        // We need to update points to reflect changes in all,\n        // x and y's, values. However, do it only for non-grouped\n        // data - grouping does it for us (#8572)\n        if (oldDataLength &&\n            !indicator.hasGroupedData &&\n            indicator.visible &&\n            indicator.points) {\n            // When data is cropped update only avaliable points (#9493)\n            if (indicator.cropped) {\n                if (indicator.xAxis) {\n                    min = indicator.xAxis.min;\n                    max = indicator.xAxis.max;\n                }\n                const croppedData = indicator.cropData(table, min, max);\n                const keys = ['x', ...(indicator.pointArrayMap || ['y'])];\n                for (let i = 0; i < (croppedData.modified?.rowCount || 0); i++) {\n                    const values = keys.map((key) => this.getColumn(key)[i] || 0);\n                    croppedDataValues.push(values);\n                }\n                const indicatorXData = indicator.getColumn('x');\n                oldFirstPointIndex = processedData.xData.indexOf(indicatorXData[0]);\n                oldLastPointIndex = processedData.xData.indexOf(indicatorXData[indicatorXData.length - 1]);\n                // Check if indicator points should be shifted (#8572)\n                if (oldFirstPointIndex === -1 &&\n                    oldLastPointIndex === processedData.xData.length - 2) {\n                    if (croppedDataValues[0][0] === oldData[0].x) {\n                        croppedDataValues.shift();\n                    }\n                }\n                indicator.updateData(croppedDataValues);\n            }\n            else if (indicator.updateAllPoints || // #18710\n                // Omit addPoint() and removePoint() cases\n                processedData.xData.length !== oldDataLength - 1 &&\n                    processedData.xData.length !== oldDataLength + 1) {\n                overwriteData = false;\n                indicator.updateData(processedData.values);\n            }\n        }\n        if (overwriteData) {\n            table.setColumns({\n                ...valueColumns,\n                x: processedData.xData\n            });\n            indicator.options.data = processedData.values;\n        }\n        if (indicator.calculateOn.xAxis &&\n            indicator.getColumn('x', true).length) {\n            indicator.isDirty = true;\n            indicator.redraw();\n        }\n        indicator.isDirtyData = !!indicator.linkedSeries.length;\n        fireEvent(indicator, 'updatedData'); // #18689\n    }\n    /**\n     * @private\n     */\n    processData() {\n        const series = this, compareToMain = series.options.compareToMain, linkedParent = series.linkedParent;\n        super.processData.apply(series, arguments);\n        if (series.dataModify &&\n            linkedParent &&\n            linkedParent.dataModify &&\n            linkedParent.dataModify.compareValue &&\n            compareToMain) {\n            series.dataModify.compareValue =\n                linkedParent.dataModify.compareValue;\n        }\n        return;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * The parameter allows setting line series type and use OHLC indicators.\n * Data in OHLC format is required.\n *\n * @sample {highstock} stock/indicators/use-ohlc-data\n *         Use OHLC data format to plot line chart\n *\n * @type      {boolean}\n * @product   highstock\n * @apioption plotOptions.line.useOhlcData\n */\n/**\n * Simple moving average indicator (SMA). This series requires `linkedTo`\n * option to be set.\n *\n * @sample stock/indicators/sma\n *         Simple moving average indicator\n *\n * @extends      plotOptions.line\n * @since        6.0.0\n * @excluding    allAreas, colorAxis, dragDrop, joinBy, keys,\n *               navigatorOptions, pointInterval, pointIntervalUnit,\n *               pointPlacement, pointRange, pointStart, showInNavigator,\n *               stacking, useOhlcData\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @optionparent plotOptions.sma\n */\nSMAIndicator.defaultOptions = merge(LineSeries.defaultOptions, {\n    /**\n     * The name of the series as shown in the legend, tooltip etc. If not\n     * set, it will be based on a technical indicator type and default\n     * params.\n     *\n     * @type {string}\n     */\n    name: void 0,\n    tooltip: {\n        /**\n         * Number of decimals in indicator series.\n         */\n        valueDecimals: 4\n    },\n    /**\n     * The main series ID that indicator will be based on. Required for this\n     * indicator.\n     *\n     * @type {string}\n     */\n    linkedTo: void 0,\n    /**\n     * Whether to compare indicator to the main series values\n     * or indicator values.\n     *\n     * @sample {highstock} stock/plotoptions/series-comparetomain/\n     *         Difference between comparing SMA values to the main series\n     *         and its own values.\n     *\n     * @type {boolean}\n     */\n    compareToMain: false,\n    /**\n     * Parameters used in calculation of regression series' points.\n     */\n    params: {\n        /**\n         * The point index which indicator calculations will base. For\n         * example using OHLC data, index=2 means the indicator will be\n         * calculated using Low values.\n         */\n        index: 3,\n        /**\n         * The base period for indicator calculations. This is the number of\n         * data points which are taken into account for the indicator\n         * calculations.\n         */\n        period: 14\n    }\n});\nextend(SMAIndicator.prototype, {\n    calculateOn: {\n        chart: 'init'\n    },\n    hasDerivedData: true,\n    nameComponents: ['period'],\n    nameSuffixes: [], // E.g. Zig Zag uses extra '%'' in the legend name\n    useCommonDataGrouping: true\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('sma', SMAIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const SMA_SMAIndicator = ((/* unused pure expression or super */ null && (SMAIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `SMA` series. If the [type](#series.sma.type) option is not specified, it\n * is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.sma\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL, useOhlcData\n * @requires  stock/indicators/indicators\n * @apioption series.sma\n */\n(''); // Adds doclet above to the transpiled file\n\n;// ./code/es-modules/Stock/Indicators/EMA/EMAIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: EMAIndicator_SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { correctFloat, isArray: EMAIndicator_isArray, merge: EMAIndicator_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The EMA series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.ema\n *\n * @augments Highcharts.Series\n */\nclass EMAIndicator extends EMAIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    accumulatePeriodPoints(period, index, yVal) {\n        let sum = 0, i = 0, y = 0;\n        while (i < period) {\n            y = index < 0 ? yVal[i] : yVal[i][index];\n            sum = sum + y;\n            i++;\n        }\n        return sum;\n    }\n    calculateEma(xVal, yVal, i, EMApercent, calEMA, index, SMA) {\n        const x = xVal[i - 1], yValue = index < 0 ?\n            yVal[i - 1] :\n            yVal[i - 1][index], y = typeof calEMA === 'undefined' ?\n            SMA : correctFloat((yValue * EMApercent) +\n            (calEMA * (1 - EMApercent)));\n        return [x, y];\n    }\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, EMApercent = 2 / (period + 1), EMA = [], xData = [], yData = [];\n        let calEMA, EMAPoint, i, index = -1, sum = 0, SMA = 0;\n        // Check period, if bigger than points length, skip\n        if (yValLen < period) {\n            return;\n        }\n        // Switch index for OHLC / Candlestick / Arearange\n        if (EMAIndicator_isArray(yVal[0])) {\n            index = params.index ? params.index : 0;\n        }\n        // Accumulate first N-points\n        sum = this.accumulatePeriodPoints(period, index, yVal);\n        // First point\n        SMA = sum / period;\n        // Calculate value one-by-one for each period in visible data\n        for (i = period; i < yValLen + 1; i++) {\n            EMAPoint = this.calculateEma(xVal, yVal, i, EMApercent, calEMA, index, SMA);\n            EMA.push(EMAPoint);\n            xData.push(EMAPoint[0]);\n            yData.push(EMAPoint[1]);\n            calEMA = EMAPoint[1];\n        }\n        return {\n            values: EMA,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Exponential moving average indicator (EMA). This series requires the\n * `linkedTo` option to be set.\n *\n * @sample stock/indicators/ema\n * Exponential moving average indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @optionparent plotOptions.ema\n */\nEMAIndicator.defaultOptions = EMAIndicator_merge(EMAIndicator_SMAIndicator.defaultOptions, {\n    params: {\n        /**\n         * The point index which indicator calculations will base. For\n         * example using OHLC data, index=2 means the indicator will be\n         * calculated using Low values.\n         *\n         * By default index value used to be set to 0. Since\n         * Highcharts Stock 7 by default index is set to 3\n         * which means that the ema indicator will be\n         * calculated using Close values.\n         */\n        index: 3,\n        period: 9 // @merge 14 in v6.2\n    }\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('ema', EMAIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const EMA_EMAIndicator = ((/* unused pure expression or super */ null && (EMAIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `EMA` series. If the [type](#series.ema.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.ema\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @apioption series.ema\n */\n''; // Adds doclet above to the transpiled file\n\n;// ./code/es-modules/Stock/Indicators/MultipleLinesComposition.js\n/**\n *\n *  (c) 2010-2025 Wojciech Chmiel\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: { prototype: smaProto } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { defined, error: MultipleLinesComposition_error, merge: MultipleLinesComposition_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\nvar MultipleLinesComposition;\n(function (MultipleLinesComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    /**\n     * Additional lines DOCS names. Elements of linesApiNames array should\n     * be consistent with DOCS line names defined in your implementation.\n     * Notice that linesApiNames should have decreased amount of elements\n     * relative to pointArrayMap (without pointValKey).\n     *\n     * @private\n     * @type {Array<string>}\n     */\n    const linesApiNames = ['bottomLine'];\n    /**\n     * Lines ids. Required to plot appropriate amount of lines.\n     * Notice that pointArrayMap should have more elements than\n     * linesApiNames, because it contains main line and additional lines ids.\n     * Also it should be consistent with amount of lines calculated in\n     * getValues method from your implementation.\n     *\n     * @private\n     * @type {Array<string>}\n     */\n    const pointArrayMap = ['top', 'bottom'];\n    /**\n     * Names of the lines, between which the area should be plotted.\n     * If the drawing of the area should\n     * be disabled for some indicators, leave this option as an empty array.\n     * Names should be the same as the names in the pointArrayMap.\n     *\n     * @private\n     * @type {Array<string>}\n     */\n    const areaLinesNames = ['top'];\n    /**\n     * Main line id.\n     *\n     * @private\n     * @type {string}\n     */\n    const pointValKey = 'top';\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Composition useful for all indicators that have more than one line.\n     * Compose it with your implementation where you will provide the\n     * `getValues` method appropriate to your indicator and `pointArrayMap`,\n     * `pointValKey`, `linesApiNames` properties. Notice that `pointArrayMap`\n     * should be consistent with the amount of lines calculated in the\n     * `getValues` method.\n     *\n     * @private\n     */\n    function compose(IndicatorClass) {\n        const proto = IndicatorClass.prototype;\n        proto.linesApiNames = (proto.linesApiNames ||\n            linesApiNames.slice());\n        proto.pointArrayMap = (proto.pointArrayMap ||\n            pointArrayMap.slice());\n        proto.pointValKey = (proto.pointValKey ||\n            pointValKey);\n        proto.areaLinesNames = (proto.areaLinesNames ||\n            areaLinesNames.slice());\n        proto.drawGraph = indicatorDrawGraph;\n        proto.getGraphPath = indicatorGetGraphPath;\n        proto.toYData = indicatorToYData;\n        proto.translate = indicatorTranslate;\n        return IndicatorClass;\n    }\n    MultipleLinesComposition.compose = compose;\n    /**\n     * Generate the API name of the line\n     *\n     * @private\n     * @param propertyName name of the line\n     */\n    function getLineName(propertyName) {\n        return ('plot' +\n            propertyName.charAt(0).toUpperCase() +\n            propertyName.slice(1));\n    }\n    /**\n     * Create translatedLines Collection based on pointArrayMap.\n     *\n     * @private\n     * @param {string} [excludedValue]\n     *        Main line id\n     * @return {Array<string>}\n     *         Returns translated lines names without excluded value.\n     */\n    function getTranslatedLinesNames(indicator, excludedValue) {\n        const translatedLines = [];\n        (indicator.pointArrayMap || []).forEach((propertyName) => {\n            if (propertyName !== excludedValue) {\n                translatedLines.push(getLineName(propertyName));\n            }\n        });\n        return translatedLines;\n    }\n    /**\n     * Draw main and additional lines.\n     *\n     * @private\n     */\n    function indicatorDrawGraph() {\n        const indicator = this, pointValKey = indicator.pointValKey, linesApiNames = indicator.linesApiNames, areaLinesNames = indicator.areaLinesNames, mainLinePoints = indicator.points, mainLineOptions = indicator.options, mainLinePath = indicator.graph, gappedExtend = {\n            options: {\n                gapSize: mainLineOptions.gapSize\n            }\n        }, \n        // Additional lines point place holders:\n        secondaryLines = [], secondaryLinesNames = getTranslatedLinesNames(indicator, pointValKey);\n        let pointsLength = mainLinePoints.length, point;\n        // Generate points for additional lines:\n        secondaryLinesNames.forEach((plotLine, index) => {\n            // Create additional lines point place holders\n            secondaryLines[index] = [];\n            while (pointsLength--) {\n                point = mainLinePoints[pointsLength];\n                secondaryLines[index].push({\n                    x: point.x,\n                    plotX: point.plotX,\n                    plotY: point[plotLine],\n                    isNull: !defined(point[plotLine])\n                });\n            }\n            pointsLength = mainLinePoints.length;\n        });\n        // Modify options and generate area fill:\n        if (indicator.userOptions.fillColor && areaLinesNames.length) {\n            const index = secondaryLinesNames.indexOf(getLineName(areaLinesNames[0])), secondLinePoints = secondaryLines[index], firstLinePoints = areaLinesNames.length === 1 ?\n                mainLinePoints :\n                secondaryLines[secondaryLinesNames.indexOf(getLineName(areaLinesNames[1]))], originalColor = indicator.color;\n            indicator.points = firstLinePoints;\n            indicator.nextPoints = secondLinePoints;\n            indicator.color = indicator.userOptions.fillColor;\n            indicator.options = MultipleLinesComposition_merge(mainLinePoints, gappedExtend);\n            indicator.graph = indicator.area;\n            indicator.fillGraph = true;\n            smaProto.drawGraph.call(indicator);\n            indicator.area = indicator.graph;\n            // Clean temporary properties:\n            delete indicator.nextPoints;\n            delete indicator.fillGraph;\n            indicator.color = originalColor;\n        }\n        // Modify options and generate additional lines:\n        linesApiNames.forEach((lineName, i) => {\n            if (secondaryLines[i]) {\n                indicator.points = secondaryLines[i];\n                if (mainLineOptions[lineName]) {\n                    indicator.options = MultipleLinesComposition_merge(mainLineOptions[lineName].styles, gappedExtend);\n                }\n                else {\n                    MultipleLinesComposition_error('Error: \"There is no ' + lineName +\n                        ' in DOCS options declared. Check if linesApiNames' +\n                        ' are consistent with your DOCS line names.\"');\n                }\n                indicator.graph = indicator['graph' + lineName];\n                smaProto.drawGraph.call(indicator);\n                // Now save lines:\n                indicator['graph' + lineName] = indicator.graph;\n            }\n            else {\n                MultipleLinesComposition_error('Error: \"' + lineName + ' doesn\\'t have equivalent ' +\n                    'in pointArrayMap. To many elements in linesApiNames ' +\n                    'relative to pointArrayMap.\"');\n            }\n        });\n        // Restore options and draw a main line:\n        indicator.points = mainLinePoints;\n        indicator.options = mainLineOptions;\n        indicator.graph = mainLinePath;\n        smaProto.drawGraph.call(indicator);\n    }\n    /**\n     * Create the path based on points provided as argument.\n     * If indicator.nextPoints option is defined, create the areaFill.\n     *\n     * @private\n     * @param points Points on which the path should be created\n     */\n    function indicatorGetGraphPath(points) {\n        let areaPath, path = [], higherAreaPath = [];\n        points = points || this.points;\n        // Render Span\n        if (this.fillGraph && this.nextPoints) {\n            areaPath = smaProto.getGraphPath.call(this, this.nextPoints);\n            if (areaPath && areaPath.length) {\n                areaPath[0][0] = 'L';\n                path = smaProto.getGraphPath.call(this, points);\n                higherAreaPath = areaPath.slice(0, path.length);\n                // Reverse points, so that the areaFill will start from the end:\n                for (let i = higherAreaPath.length - 1; i >= 0; i--) {\n                    path.push(higherAreaPath[i]);\n                }\n            }\n        }\n        else {\n            path = smaProto.getGraphPath.apply(this, arguments);\n        }\n        return path;\n    }\n    /**\n     * @private\n     * @param {Highcharts.Point} point\n     *        Indicator point\n     * @return {Array<number>}\n     *         Returns point Y value for all lines\n     */\n    function indicatorToYData(point) {\n        const pointColl = [];\n        (this.pointArrayMap || []).forEach((propertyName) => {\n            pointColl.push(point[propertyName]);\n        });\n        return pointColl;\n    }\n    /**\n     * Add lines plot pixel values.\n     *\n     * @private\n     */\n    function indicatorTranslate() {\n        const pointArrayMap = this.pointArrayMap;\n        let LinesNames = [], value;\n        LinesNames = getTranslatedLinesNames(this);\n        smaProto.translate.apply(this, arguments);\n        this.points.forEach((point) => {\n            pointArrayMap.forEach((propertyName, i) => {\n                value = point[propertyName];\n                // If the modifier, like for example compare exists,\n                // modified the original value by that method, #15867.\n                if (this.dataModify) {\n                    value = this.dataModify.modifyValue(value);\n                }\n                if (value !== null) {\n                    point[LinesNames[i]] = this.yAxis.toPixels(value, true);\n                }\n            });\n        });\n    }\n})(MultipleLinesComposition || (MultipleLinesComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Indicators_MultipleLinesComposition = (MultipleLinesComposition);\n\n;// ./code/es-modules/masters/indicators/indicators.js\n\n\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nG.MultipleLinesComposition =\n    G.MultipleLinesComposition || Indicators_MultipleLinesComposition;\n/* harmony default export */ const indicators_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__960__", "__WEBPACK_EXTERNAL_MODULE__512__", "MultipleLinesComposition", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "indicators_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_", "highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "line", "LineSeries", "seriesTypes", "addEvent", "fireEvent", "error", "extend", "isArray", "merge", "pick", "tableToMultiYData", "series", "processed", "yData", "pointArrayMap", "table", "dataTable", "modified", "getColumn", "columns", "map", "i", "rowCount", "values", "colIndex", "push", "SMAIndicator", "destroy", "dataEventsToUnbind", "for<PERSON>ach", "unbinder", "apply", "arguments", "getName", "params", "name", "nameComponents", "component", "index", "options", "nameSuffixes", "nameBase", "type", "toUpperCase", "join", "getV<PERSON>ues", "period", "xVal", "xData", "yVal", "yValLen", "length", "SMA", "range", "SMAPoint", "sum", "init", "chart", "indicator", "linkedSeriesUnbiner", "isUpdating", "hasEvents", "linkedParent", "linkedTo", "recalculateValues", "calculateOn", "xAxis", "closestPointRange", "order", "eventsToUnbind", "croppedDataValues", "oldData", "points", "oldDataLength", "overwriteData", "oldFirstPointIndex", "oldLastPointIndex", "min", "max", "processedYData", "processedData", "valueColumns", "column", "hasGroupedData", "visible", "cropped", "croppedData", "cropData", "keys", "indicatorXData", "indexOf", "x", "shift", "updateData", "updateAllPoints", "setColumns", "data", "isDirty", "redraw", "isDirtyData", "linkedSeries", "processData", "compareToMain", "dataModify", "compareValue", "defaultOptions", "tooltip", "valueDecimals", "hasDerivedData", "useCommonDataGrouping", "registerSeriesType", "sma", "EMAIndicator_SMAIndicator", "correctFloat", "EMAIndicator_isArray", "EMAIndicator_merge", "EMAIndicator", "accumulatePeriodPoints", "y", "calculateEma", "EMApercent", "calEMA", "yValue", "EMA", "EMAPoint", "sma<PERSON><PERSON><PERSON>", "defined", "MultipleLinesComposition_error", "MultipleLinesComposition_merge", "linesApiNames", "areaLinesNames", "getLineName", "propertyName", "char<PERSON>t", "slice", "getTranslatedLinesNames", "excludedValue", "translatedLines", "indicatorDrawGraph", "pointVal<PERSON>ey", "mainLinePoints", "mainLineOptions", "mainLinePath", "graph", "gappedExtend", "gapSize", "secondaryLines", "secondaryLinesNames", "pointsLength", "point", "plotLine", "plotX", "plotY", "isNull", "userOptions", "fillColor", "secondLinePoints", "firstLinePoints", "originalColor", "color", "nextPoints", "area", "fillGraph", "drawGraph", "lineName", "styles", "indicatorGetGraphPath", "areaPath", "path", "higherAreaPath", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "indicatorToYData", "pointColl", "indicatorTranslate", "LinesNames", "value", "translate", "modifyValue", "yAxis", "toPixels", "compose", "IndicatorClass", "proto", "toYData", "Indicators_MultipleLinesComposition", "G"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC1G,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,mCAAoC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,KAAQ,CAACA,EAAK,cAAiB,CAAE,GAC1I,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,mCAAmC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAE9IA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACpH,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,IACvG,AAAC,CAAA,KACP,aACA,IAgpBNC,EAhpBUC,EAAuB,CAE/B,IACC,AAACT,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,AAACL,IAERA,EAAOD,OAAO,CAAGO,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAad,OAAO,CAG5B,IAAIC,EAASU,CAAwB,CAACE,EAAS,CAAG,CAGjDb,QAAS,CAAC,CACX,EAMA,OAHAU,CAAmB,CAACG,EAAS,CAACZ,EAAQA,EAAOD,OAAO,CAAEY,GAG/CX,EAAOD,OAAO,AACtB,CAMCY,EAAoBI,CAAC,CAAG,AAACf,IACxB,IAAIgB,EAAShB,GAAUA,EAAOiB,UAAU,CACvC,IAAOjB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAW,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACnB,EAASqB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACvB,EAASsB,IAC5EE,OAAOC,cAAc,CAACzB,EAASsB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAA+FzB,EAAoB,KACnH0B,EAAmH1B,EAAoBI,CAAC,CAACqB,GAEzIE,EAAmI3B,EAAoB,KACvJ4B,EAAuJ5B,EAAoBI,CAAC,CAACuB,GAYjL,GAAM,CAAEE,KAAMC,CAAU,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAE9K,CAAEC,SAAAA,CAAQ,CAAEC,UAAAA,CAAS,CAAEC,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAE,CAAId,IAOhEe,EAAoB,CAACC,EAAQC,KAC/B,IAAMC,EAAQ,EAAE,CAAEC,EAAgBH,EAAOG,aAAa,CAAEC,EAAQH,GAAaD,EAAOK,SAAS,CAACC,QAAQ,EAAIN,EAAOK,SAAS,CAC1H,GAAI,CAACF,EACD,OAAOH,EAAOO,SAAS,CAAC,IAAKN,GAEjC,IAAMO,EAAUL,EAAcM,GAAG,CAAC,AAACvC,GAAQ8B,EAAOO,SAAS,CAACrC,EAAK+B,IACjE,IAAK,IAAIS,EAAI,EAAGA,EAAIN,EAAMO,QAAQ,CAAED,IAAK,CACrC,IAAME,EAAST,EAAcM,GAAG,CAAC,CAACvC,EAAK2C,IAAaL,CAAO,CAACK,EAAS,EAAE,CAACH,EAAE,EAAI,GAC9ER,EAAMY,IAAI,CAACF,EACf,CACA,OAAOV,CACX,CAWA,OAAMa,UAAqBzB,EASvB0B,SAAU,CACN,IAAI,CAACC,kBAAkB,CAACC,OAAO,CAAC,SAAUC,CAAQ,EAC9CA,GACJ,GACA,KAAK,CAACH,QAAQI,KAAK,CAAC,IAAI,CAAEC,UAC9B,CAIAC,SAAU,CACN,IAAMC,EAAS,EAAE,CACbC,EAAO,IAAI,CAACA,IAAI,CASpB,OARKA,IACD,AAAC,CAAA,IAAI,CAACC,cAAc,EAAI,EAAE,AAAD,EAAGP,OAAO,CAAC,SAAUQ,CAAS,CAAEC,CAAK,EAC1DJ,EAAOT,IAAI,CAAC,IAAI,CAACc,OAAO,CAACL,MAAM,CAACG,EAAU,CACtC5B,EAAK,IAAI,CAAC+B,YAAY,CAACF,EAAM,CAAE,IACvC,EAAG,IAAI,EACPH,EAAO,AAAC,CAAA,IAAI,CAACM,QAAQ,EAAI,IAAI,CAACC,IAAI,CAACC,WAAW,EAAC,EAC1C,CAAA,IAAI,CAACP,cAAc,CAAG,KAAOF,EAAOU,IAAI,CAAC,MAAQ,IAAM,EAAC,GAE1DT,CACX,CAIAU,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAMY,EAASZ,EAAOY,MAAM,CAAEC,EAAOpC,EAAOqC,KAAK,EAAI,EAAE,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAKE,MAAM,CAAEC,EAAM,EAAE,CAAEJ,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CACjIQ,EAAGiB,EAAQ,GAAIe,EAAQ,EAAGC,EAAUC,EAAM,EAC9C,IAAIR,CAAAA,EAAKI,MAAM,CAAGL,CAAK,GAQvB,IAJIvC,EAAQ0C,CAAI,CAAC,EAAE,GACfX,CAAAA,EAAQJ,EAAOI,KAAK,CAAGJ,EAAOI,KAAK,CAAG,CAAA,EAGnCe,EAAQP,EAAS,GACpBS,GAAOjB,EAAQ,EAAIW,CAAI,CAACI,EAAM,CAAGJ,CAAI,CAACI,EAAM,CAACf,EAAM,CACnDe,IAGJ,IAAKhC,EAAIgC,EAAOhC,EAAI6B,EAAS7B,IACzBkC,GAAOjB,EAAQ,EAAIW,CAAI,CAAC5B,EAAE,CAAG4B,CAAI,CAAC5B,EAAE,CAACiB,EAAM,CAC3CgB,EAAW,CAACP,CAAI,CAAC1B,EAAE,CAAEkC,EAAMT,EAAO,CAClCM,EAAI3B,IAAI,CAAC6B,GACTN,EAAMvB,IAAI,CAAC6B,CAAQ,CAAC,EAAE,EACtBzC,EAAMY,IAAI,CAAC6B,CAAQ,CAAC,EAAE,EACtBC,GAAQjB,EAAQ,EACZW,CAAI,CAAC5B,EAAIgC,EAAM,CACfJ,CAAI,CAAC5B,EAAIgC,EAAM,CAACf,EAAM,CAE9B,MAAO,CACHf,OAAQ6B,EACRJ,MAAOA,EACPnC,MAAOA,CACX,EACJ,CAIA2C,KAAKC,CAAK,CAAElB,CAAO,CAAE,CACjB,IAAMmB,EAAY,IAAI,CACtB,KAAK,CAACF,KAAKjE,IAAI,CAACmE,EAAWD,EAAOlB,GAElC,IAAMoB,EAAsBxD,EAAUN,IAAwG,kBAAmB,SAAU,CAAE+D,WAAAA,CAAU,CAAE,EAGrL,GAAIA,EACA,OAEJ,IAAMC,EAAY,CAAC,CAACH,EAAU9B,kBAAkB,CAACuB,MAAM,CACvD,IAAIO,EAAUI,YAAY,CAmCtB,OAAOzD,EAAM,UACTqD,EAAUnB,OAAO,CAACwB,QAAQ,CAC1B,gCAAiC,CAAA,EAAON,GArB5C,GAfI,CAACI,IAGDH,EAAU9B,kBAAkB,CAACH,IAAI,CAACtB,EAASuD,EAAUI,YAAY,CAAE,cAAe,WAC9EJ,EAAUM,iBAAiB,EAC/B,IAGIN,EAAUO,WAAW,CAACC,KAAK,EAC3BR,EAAU9B,kBAAkB,CAACH,IAAI,CAACtB,EAASuD,EAAUI,YAAY,CAACI,KAAK,CAAER,EAAUO,WAAW,CAACC,KAAK,CAAE,WAClGR,EAAUM,iBAAiB,EAC/B,KAIJN,AAAgC,SAAhCA,EAAUO,WAAW,CAACR,KAAK,CAIvB,AAACC,EAAUS,iBAAiB,EAC5BT,EAAUM,iBAAiB,QAG9B,GAAI,CAACH,EAAW,CAGjB,IAAM/B,EAAW3B,EAASuD,EAAUD,KAAK,CAAEC,EAAUO,WAAW,CAACR,KAAK,CAAE,WACpEC,EAAUM,iBAAiB,GAE3BlC,GACJ,EACJ,CAOR,EAAG,CACCsC,MAAO,CACX,EAGAV,CAAAA,EAAU9B,kBAAkB,CAAG,EAAE,CACjC8B,EAAUW,cAAc,CAAC5C,IAAI,CAACkC,EAClC,CAIAK,mBAAoB,CAChB,IAAMM,EAAoB,EAAE,CAAoBvD,EAAQ,IAAI,CAACC,SAAS,CAAEuD,EAAUb,AAAxC,IAAI,CAA8Cc,MAAM,EAAI,EAAE,CAAEC,EAAgBf,AAAhF,IAAI,CAAsF1C,SAAS,CAACM,QAAQ,CAKlJoD,EAAgB,CAAA,EAAMC,EAAoBC,EAAmBC,EAAKC,EAIhEjE,EAAQ6C,AAT4B,IAAI,CAStBI,YAAY,CAACjD,KAAK,CAAEkE,EAAiBrB,AATnB,IAAI,CASyBI,YAAY,CAACiB,cAAc,AAClGrB,CAV0C,IAAI,CAUpCI,YAAY,CAACd,KAAK,CAAGU,AAVW,IAAI,CAULI,YAAY,CAChD5C,SAAS,CAAC,KACfwC,AAZ0C,IAAI,CAYpCI,YAAY,CAACjD,KAAK,CAAGH,EAAkBgD,AAZP,IAAI,CAYaI,YAAY,EACvEJ,AAb0C,IAAI,CAapCI,YAAY,CAACiB,cAAc,CAAGrE,EAAkBgD,AAbhB,IAAI,CAasBI,YAAY,CAAE,CAAA,GAMlF,IAAMkB,EAAgBtB,AAnBoB,IAAI,CAmBdI,YAAY,CAACvB,OAAO,EAEhDmB,AArBsC,IAAI,CAqBhCI,YAAY,CAAC9C,SAAS,CAACM,QAAQ,EACxCoC,AAtBqC,IAAI,CAsB/Bb,SAAS,CAACa,AAtBiB,IAAI,CAsBXI,YAAY,CAAEJ,AAtBP,IAAI,CAsBanB,OAAO,CAACL,MAAM,GAtB0F,CAC/JX,OAAQ,EAAE,CACVyB,MAAO,EAAE,CACTnC,MAAO,EAAE,AACb,CAoBA,QAAO6C,AAxBmC,IAAI,CAwB7BI,YAAY,CAACd,KAAK,CACnCU,AAzB0C,IAAI,CAyBpCI,YAAY,CAACjD,KAAK,CAAGA,EAC/B6C,AA1B0C,IAAI,CA0BpCI,YAAY,CAACiB,cAAc,CAAGA,EACxC,IAAMjE,EAAgB4C,AA3BoB,IAAI,CA2Bd5C,aAAa,EAAI,CAAC,IAAI,CAAEmE,EAAe,CAAC,EAexE,GAbAD,EAAcnE,KAAK,CACdgB,OAAO,CAAC,AAACN,IACVT,EAAce,OAAO,CAAC,CAAChD,EAAKyD,KACxB,IAAM4C,EAASD,CAAY,CAACpG,EAAI,EAAI,EAAE,CACtCqG,EAAOzD,IAAI,CAAClB,EAAQgB,GAAUA,CAAM,CAACe,EAAM,CAAGf,GAC1C,AAAC0D,CAAY,CAACpG,EAAI,EAClBoG,CAAAA,CAAY,CAACpG,EAAI,CAAGqG,CAAK,CAEjC,EACJ,GAIIT,GACA,CAACf,AA3CqC,IAAI,CA2C/ByB,cAAc,EACzBzB,AA5CsC,IAAI,CA4ChC0B,OAAO,EACjB1B,AA7CsC,IAAI,CA6ChCc,MAAM,CAEhB,GAAId,AA/CkC,IAAI,CA+C5B2B,OAAO,CAAE,CACf3B,AAhD8B,IAAI,CAgDxBQ,KAAK,GACfW,EAAMnB,AAjDwB,IAAI,CAiDlBQ,KAAK,CAACW,GAAG,CACzBC,EAAMpB,AAlDwB,IAAI,CAkDlBQ,KAAK,CAACY,GAAG,EAE7B,IAAMQ,EAAc5B,AApDc,IAAI,CAoDR6B,QAAQ,CAACxE,EAAO8D,EAAKC,GAC7CU,EAAO,CAAC,OAAS9B,AArDW,IAAI,CAqDL5C,aAAa,EAAI,CAAC,IAAI,CAAE,CACzD,IAAK,IAAIO,EAAI,EAAGA,EAAKiE,CAAAA,EAAYrE,QAAQ,EAAEK,UAAY,CAAA,EAAID,IAAK,CAC5D,IAAME,EAASiE,EAAKpE,GAAG,CAAC,AAACvC,GAAQ,IAAI,CAACqC,SAAS,CAACrC,EAAI,CAACwC,EAAE,EAAI,GAC3DiD,EAAkB7C,IAAI,CAACF,EAC3B,CACA,IAAMkE,EAAiB/B,AA1DW,IAAI,CA0DLxC,SAAS,CAAC,KAC3CyD,EAAqBK,EAAchC,KAAK,CAAC0C,OAAO,CAACD,CAAc,CAAC,EAAE,EAClEb,EAAoBI,EAAchC,KAAK,CAAC0C,OAAO,CAACD,CAAc,CAACA,EAAetC,MAAM,CAAG,EAAE,EAErFwB,AAAuB,KAAvBA,GACAC,IAAsBI,EAAchC,KAAK,CAACG,MAAM,CAAG,GAC/CmB,CAAiB,CAAC,EAAE,CAAC,EAAE,GAAKC,CAAO,CAAC,EAAE,CAACoB,CAAC,EACxCrB,EAAkBsB,KAAK,GAG/BlC,AApEkC,IAAI,CAoE5BmC,UAAU,CAACvB,EACzB,KACSZ,CAAAA,AAtE6B,IAAI,CAsEvBoC,eAAe,EAE9Bd,EAAchC,KAAK,CAACG,MAAM,GAAKsB,EAAgB,GAC3CO,EAAchC,KAAK,CAACG,MAAM,GAAKsB,EAAgB,CAAA,IACnDC,EAAgB,CAAA,EAChBhB,AA3EkC,IAAI,CA2E5BmC,UAAU,CAACb,EAAczD,MAAM,GAG7CmD,IACA3D,EAAMgF,UAAU,CAAC,CACb,GAAGd,CAAY,CACfU,EAAGX,EAAchC,KAAK,AAC1B,GACAU,AAnFsC,IAAI,CAmFhCnB,OAAO,CAACyD,IAAI,CAAGhB,EAAczD,MAAM,EAE7CmC,AArFsC,IAAI,CAqFhCO,WAAW,CAACC,KAAK,EAC3BR,AAtFsC,IAAI,CAsFhCxC,SAAS,CAAC,IAAK,CAAA,GAAMiC,MAAM,GACrCO,AAvFsC,IAAI,CAuFhCuC,OAAO,CAAG,CAAA,EACpBvC,AAxFsC,IAAI,CAwFhCwC,MAAM,IAEpBxC,AA1F0C,IAAI,CA0FpCyC,WAAW,CAAG,CAAC,CAACzC,AA1FgB,IAAI,CA0FV0C,YAAY,CAACjD,MAAM,CACvD/C,EA3F0C,IAAI,CA2FzB,cACzB,CAIAiG,aAAc,CACV,IAAqBC,EAAgB3F,AAAtB,IAAI,CAAyB4B,OAAO,CAAC+D,aAAa,CAAExC,EAAenD,AAAnE,IAAI,CAAsEmD,YAAY,CACrG,KAAK,CAACuC,YAAYtE,KAAK,CADR,IAAI,CACaC,WAC5BrB,AAFW,IAAI,CAER4F,UAAU,EACjBzC,GACAA,EAAayC,UAAU,EACvBzC,EAAayC,UAAU,CAACC,YAAY,EACpCF,GACA3F,CAAAA,AAPW,IAAI,CAOR4F,UAAU,CAACC,YAAY,CAC1B1C,EAAayC,UAAU,CAACC,YAAY,AAAD,CAG/C,CACJ,CAkCA9E,EAAa+E,cAAc,CAAGjG,EAAMP,EAAWwG,cAAc,CAAE,CAQ3DtE,KAAM,KAAK,EACXuE,QAAS,CAILC,cAAe,CACnB,EAOA5C,SAAU,KAAK,EAWfuC,cAAe,CAAA,EAIfpE,OAAQ,CAMJI,MAAO,EAMPQ,OAAQ,EACZ,CACJ,GACAxC,EAAOoB,EAAarC,SAAS,CAAE,CAC3B4E,YAAa,CACTR,MAAO,MACX,EACAmD,eAAgB,CAAA,EAChBxE,eAAgB,CAAC,SAAS,CAC1BI,aAAc,EAAE,CAChBqE,sBAAuB,CAAA,CAC3B,GACA9G,IAA0I+G,kBAAkB,CAAC,MAAOpF,GAmCpK,GAAM,CAAEqF,IAAKC,CAAyB,CAAE,CAAG,AAACjH,IAA2IG,WAAW,CAE5L,CAAE+G,aAAAA,CAAY,CAAE1G,QAAS2G,CAAoB,CAAE1G,MAAO2G,CAAkB,CAAE,CAAIxH,GAepF,OAAMyH,UAAqBJ,EAMvBK,uBAAuBvE,CAAM,CAAER,CAAK,CAAEW,CAAI,CAAE,CACxC,IAAIM,EAAM,EAAGlC,EAAI,EAAGiG,EAAI,EACxB,KAAOjG,EAAIyB,GAEPS,GADIjB,EAAQ,EAAIW,CAAI,CAAC5B,EAAE,CAAG4B,CAAI,CAAC5B,EAAE,CAACiB,EAAM,CAExCjB,IAEJ,OAAOkC,CACX,CACAgE,aAAaxE,CAAI,CAAEE,CAAI,CAAE5B,CAAC,CAAEmG,CAAU,CAAEC,CAAM,CAAEnF,CAAK,CAAEc,CAAG,CAAE,CACxD,IAAMuC,EAAI5C,CAAI,CAAC1B,EAAI,EAAE,CAAEqG,EAASpF,EAAQ,EACpCW,CAAI,CAAC5B,EAAI,EAAE,CACX4B,CAAI,CAAC5B,EAAI,EAAE,CAACiB,EAAM,CAGtB,MAAO,CAACqD,EAHoB,AAAkB,KAAA,IAAX8B,EAC/BrE,EAAM6D,EAAa,AAACS,EAASF,EAC5BC,EAAU,CAAA,EAAID,CAAS,GACf,AACjB,CACA3E,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAMY,EAASZ,EAAOY,MAAM,CAAEC,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAOA,EAAKE,MAAM,CAAG,EAAGqE,EAAa,EAAK1E,CAAAA,EAAS,CAAA,EAAI6E,EAAM,EAAE,CAAE3E,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CACrK4G,EAAQG,EAAUvG,EAAGiB,EAAQ,GAAIiB,EAAM,EAAGH,EAAM,EAEpD,IAAIF,CAAAA,EAAUJ,CAAK,GAYnB,IARIoE,EAAqBjE,CAAI,CAAC,EAAE,GAC5BX,CAAAA,EAAQJ,EAAOI,KAAK,CAAGJ,EAAOI,KAAK,CAAG,CAAA,EAK1Cc,EAAMG,AAFA,IAAI,CAAC8D,sBAAsB,CAACvE,EAAQR,EAAOW,GAErCH,EAEPzB,EAAIyB,EAAQzB,EAAI6B,EAAU,EAAG7B,IAC9BuG,EAAW,IAAI,CAACL,YAAY,CAACxE,EAAME,EAAM5B,EAAGmG,EAAYC,EAAQnF,EAAOc,GACvEuE,EAAIlG,IAAI,CAACmG,GACT5E,EAAMvB,IAAI,CAACmG,CAAQ,CAAC,EAAE,EACtB/G,EAAMY,IAAI,CAACmG,CAAQ,CAAC,EAAE,EACtBH,EAASG,CAAQ,CAAC,EAAE,CAExB,MAAO,CACHrG,OAAQoG,EACR3E,MAAOA,EACPnC,MAAOA,CACX,EACJ,CACJ,CAmBAuG,EAAaX,cAAc,CAAGU,EAAmBH,EAA0BP,cAAc,CAAE,CACvFvE,OAAQ,CAWJI,MAAO,EACPQ,OAAQ,CACZ,CACJ,GACA/C,IAA0I+G,kBAAkB,CAAC,MAAOM,GAqCpK,GAAM,CAAEL,IAAK,CAAE1H,UAAWwI,CAAQ,CAAE,CAAE,CAAG,AAAC9H,IAA2IG,WAAW,CAE1L,CAAE4H,QAAAA,CAAO,CAAEzH,MAAO0H,CAA8B,CAAEvH,MAAOwH,CAA8B,CAAE,CAAIrI,KAOnG,AAAC,SAAU3B,CAAwB,EAoB/B,IAAMiK,EAAgB,CAAC,aAAa,CAW9BnH,EAAgB,CAAC,MAAO,SAAS,CAUjCoH,EAAiB,CAAC,MAAM,CA8C9B,SAASC,EAAYC,CAAY,EAC7B,MAAQ,OACJA,EAAaC,MAAM,CAAC,GAAG1F,WAAW,GAClCyF,EAAaE,KAAK,CAAC,EAC3B,CAUA,SAASC,EAAwB7E,CAAS,CAAE8E,CAAa,EACrD,IAAMC,EAAkB,EAAE,CAM1B,MALA,AAAC/E,CAAAA,EAAU5C,aAAa,EAAI,EAAE,AAAD,EAAGe,OAAO,CAAC,AAACuG,IACjCA,IAAiBI,GACjBC,EAAgBhH,IAAI,CAAC0G,EAAYC,GAEzC,GACOK,CACX,CAMA,SAASC,IACL,IAAMhF,EAAY,IAAI,CAAEiF,EAAcjF,EAAUiF,WAAW,CAAEV,EAAgBvE,EAAUuE,aAAa,CAAEC,EAAiBxE,EAAUwE,cAAc,CAAEU,EAAiBlF,EAAUc,MAAM,CAAEqE,EAAkBnF,EAAUnB,OAAO,CAAEuG,EAAepF,EAAUqF,KAAK,CAAEC,EAAe,CACpQzG,QAAS,CACL0G,QAASJ,EAAgBI,OAAO,AACpC,CACJ,EAEAC,EAAiB,EAAE,CAAEC,EAAsBZ,EAAwB7E,EAAWiF,GAC1ES,EAAeR,EAAezF,MAAM,CAAEkG,EAiB1C,GAfAF,EAAoBtH,OAAO,CAAC,CAACyH,EAAUhH,KAGnC,IADA4G,CAAc,CAAC5G,EAAM,CAAG,EAAE,CACnB8G,KACHC,EAAQT,CAAc,CAACQ,EAAa,CACpCF,CAAc,CAAC5G,EAAM,CAACb,IAAI,CAAC,CACvBkE,EAAG0D,EAAM1D,CAAC,CACV4D,MAAOF,EAAME,KAAK,CAClBC,MAAOH,CAAK,CAACC,EAAS,CACtBG,OAAQ,CAAC3B,EAAQuB,CAAK,CAACC,EAAS,CACpC,GAEJF,EAAeR,EAAezF,MAAM,AACxC,GAEIO,EAAUgG,WAAW,CAACC,SAAS,EAAIzB,EAAe/E,MAAM,CAAE,CAC1D,IAA2EyG,EAAmBV,CAAc,CAA9FC,EAAoBzD,OAAO,CAACyC,EAAYD,CAAc,CAAC,EAAE,GAA4C,CAAE2B,EAAkB3B,AAA0B,IAA1BA,EAAe/E,MAAM,CACxJyF,EACAM,CAAc,CAACC,EAAoBzD,OAAO,CAACyC,EAAYD,CAAc,CAAC,EAAE,GAAG,CAAE4B,EAAgBpG,EAAUqG,KAAK,AAChHrG,CAAAA,EAAUc,MAAM,CAAGqF,EACnBnG,EAAUsG,UAAU,CAAGJ,EACvBlG,EAAUqG,KAAK,CAAGrG,EAAUgG,WAAW,CAACC,SAAS,CACjDjG,EAAUnB,OAAO,CAAGyF,EAA+BY,EAAgBI,GACnEtF,EAAUqF,KAAK,CAAGrF,EAAUuG,IAAI,CAChCvG,EAAUwG,SAAS,CAAG,CAAA,EACtBrC,EAASsC,SAAS,CAAC5K,IAAI,CAACmE,GACxBA,EAAUuG,IAAI,CAAGvG,EAAUqF,KAAK,CAEhC,OAAOrF,EAAUsG,UAAU,CAC3B,OAAOtG,EAAUwG,SAAS,CAC1BxG,EAAUqG,KAAK,CAAGD,CACtB,CAEA7B,EAAcpG,OAAO,CAAC,CAACuI,EAAU/I,KACzB6H,CAAc,CAAC7H,EAAE,EACjBqC,EAAUc,MAAM,CAAG0E,CAAc,CAAC7H,EAAE,CAChCwH,CAAe,CAACuB,EAAS,CACzB1G,EAAUnB,OAAO,CAAGyF,EAA+Ba,CAAe,CAACuB,EAAS,CAACC,MAAM,CAAErB,GAGrFjB,EAA+B,uBAAyBqC,EAAzB,gGAInC1G,EAAUqF,KAAK,CAAGrF,CAAS,CAAC,QAAU0G,EAAS,CAC/CvC,EAASsC,SAAS,CAAC5K,IAAI,CAACmE,GAExBA,CAAS,CAAC,QAAU0G,EAAS,CAAG1G,EAAUqF,KAAK,EAG/ChB,EAA+B,WAAaqC,EAAb,4GAIvC,GAEA1G,EAAUc,MAAM,CAAGoE,EACnBlF,EAAUnB,OAAO,CAAGsG,EACpBnF,EAAUqF,KAAK,CAAGD,EAClBjB,EAASsC,SAAS,CAAC5K,IAAI,CAACmE,EAC5B,CAQA,SAAS4G,EAAsB9F,CAAM,EACjC,IAAI+F,EAAUC,EAAO,EAAE,CAAEC,EAAiB,EAAE,CAG5C,GAFAjG,EAASA,GAAU,IAAI,CAACA,MAAM,CAE1B,IAAI,CAAC0F,SAAS,EAAI,IAAI,CAACF,UAAU,CAEjC,CAAA,GAAIO,AADJA,CAAAA,EAAW1C,EAAS6C,YAAY,CAACnL,IAAI,CAAC,IAAI,CAAE,IAAI,CAACyK,UAAU,CAAA,GAC3CO,EAASpH,MAAM,CAAE,CAC7BoH,CAAQ,CAAC,EAAE,CAAC,EAAE,CAAG,IACjBC,EAAO3C,EAAS6C,YAAY,CAACnL,IAAI,CAAC,IAAI,CAAEiF,GACxCiG,EAAiBF,EAASjC,KAAK,CAAC,EAAGkC,EAAKrH,MAAM,EAE9C,IAAK,IAAI9B,EAAIoJ,EAAetH,MAAM,CAAG,EAAG9B,GAAK,EAAGA,IAC5CmJ,EAAK/I,IAAI,CAACgJ,CAAc,CAACpJ,EAAE,CAEnC,CAAA,MAGAmJ,EAAO3C,EAAS6C,YAAY,CAAC3I,KAAK,CAAC,IAAI,CAAEC,WAE7C,OAAOwI,CACX,CAQA,SAASG,EAAiBtB,CAAK,EAC3B,IAAMuB,EAAY,EAAE,CAIpB,MAHA,AAAC,CAAA,IAAI,CAAC9J,aAAa,EAAI,EAAE,AAAD,EAAGe,OAAO,CAAC,AAACuG,IAChCwC,EAAUnJ,IAAI,CAAC4H,CAAK,CAACjB,EAAa,CACtC,GACOwC,CACX,CAMA,SAASC,IACL,IAAM/J,EAAgB,IAAI,CAACA,aAAa,CACpCgK,EAAa,EAAE,CAAEC,EACrBD,EAAavC,EAAwB,IAAI,EACzCV,EAASmD,SAAS,CAACjJ,KAAK,CAAC,IAAI,CAAEC,WAC/B,IAAI,CAACwC,MAAM,CAAC3C,OAAO,CAAC,AAACwH,IACjBvI,EAAce,OAAO,CAAC,CAACuG,EAAc/G,KACjC0J,EAAQ1B,CAAK,CAACjB,EAAa,CAGvB,IAAI,CAAC7B,UAAU,EACfwE,CAAAA,EAAQ,IAAI,CAACxE,UAAU,CAAC0E,WAAW,CAACF,EAAK,EAEzCA,AAAU,OAAVA,GACA1B,CAAAA,CAAK,CAACyB,CAAU,CAACzJ,EAAE,CAAC,CAAG,IAAI,CAAC6J,KAAK,CAACC,QAAQ,CAACJ,EAAO,CAAA,EAAI,CAE9D,EACJ,EACJ,CA3KA/M,EAAyBoN,OAAO,CAhBhC,SAAiBC,CAAc,EAC3B,IAAMC,EAAQD,EAAehM,SAAS,CAatC,OAZAiM,EAAMrD,aAAa,CAAIqD,EAAMrD,aAAa,EACtCA,EAAcK,KAAK,GACvBgD,EAAMxK,aAAa,CAAIwK,EAAMxK,aAAa,EACtCA,EAAcwH,KAAK,GACvBgD,EAAM3C,WAAW,CAAI2C,EAAM3C,WAAW,EAtBtB,MAwBhB2C,EAAMpD,cAAc,CAAIoD,EAAMpD,cAAc,EACxCA,EAAeI,KAAK,GACxBgD,EAAMnB,SAAS,CAAGzB,EAClB4C,EAAMZ,YAAY,CAAGJ,EACrBgB,EAAMC,OAAO,CAAGZ,EAChBW,EAAMN,SAAS,CAAGH,EACXQ,CACX,CA6KJ,EAAGrN,GAA6BA,CAAAA,EAA2B,CAAC,CAAA,GAM/B,IAAMwN,EAAuCxN,EASpEyN,EAAK9L,GACX8L,CAAAA,EAAEzN,wBAAwB,CACtByN,EAAEzN,wBAAwB,EAAIwN,EACL,IAAM/L,EAAmBE,IAG5C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}