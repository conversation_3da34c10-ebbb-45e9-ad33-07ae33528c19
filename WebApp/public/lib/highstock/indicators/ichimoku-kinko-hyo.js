!/**
 * Highstock JS v12.3.0 (2025-06-21)
 * @module highcharts/indicators/ichimoku-kinko-hyo
 * @requires highcharts
 * @requires highcharts/modules/stock
 *
 * Indicator series type for Highcharts Stock
 *
 * (c) 2010-2025 Sebastian <PERSON>
 *
 * License: www.highcharts.com/license
 */function(o,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(o._Highcharts,o._Highcharts.dataGrouping.approximations,o._Highcharts.Color,o._Highcharts.SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/indicators/ichimoku-kinko-hyo",["highcharts/highcharts"],function(o){return t(o,o.dataGrouping,["approximations"],o.Color,o.SeriesRegistry)}):"object"==typeof exports?exports["highcharts/indicators/ichimoku-kinko-hyo"]=t(o._Highcharts,o._Highcharts.dataGrouping.approximations,o._Highcharts.Color,o._Highcharts.SeriesRegistry):o.Highcharts=t(o.Highcharts,o.Highcharts.dataGrouping.approximations,o.Highcharts.Color,o.Highcharts.SeriesRegistry)}("undefined"==typeof window?this:window,(o,t,e,n)=>(()=>{"use strict";var i={512:o=>{o.exports=n},620:o=>{o.exports=e},944:t=>{t.exports=o},956:o=>{o.exports=t}},s={};function p(o){var t=s[o];if(void 0!==t)return t.exports;var e=s[o]={exports:{}};return i[o](e,e.exports,p),e.exports}p.n=o=>{var t=o&&o.__esModule?()=>o.default:()=>o;return p.d(t,{a:t}),t},p.d=(o,t)=>{for(var e in t)p.o(t,e)&&!p.o(o,e)&&Object.defineProperty(o,e,{enumerable:!0,get:t[e]})},p.o=(o,t)=>Object.prototype.hasOwnProperty.call(o,t);var r={};p.d(r,{default:()=>G});var a=p(944),l=p.n(a),h=p(956),u=p.n(h),c=p(620),g=p.n(c),d=p(512),f=p.n(d);let{parse:k}=g(),{sma:S}=f().seriesTypes,{defined:y,extend:x,isArray:m,isNumber:v,getClosestDistance:C,merge:A,objectEach:Y}=l();function P(o){return{high:o.reduce(function(o,t){return Math.max(o,t[1])},-1/0),low:o.reduce(function(o,t){return Math.min(o,t[2])},1/0)}}function B(o){let t=o.indicator;t.points=o.points,t.nextPoints=o.nextPoints,t.color=o.color,t.options=A(o.options.senkouSpan.styles,o.gap),t.graph=o.graph,t.fillGraph=!0,f().seriesTypes.sma.prototype.drawGraph.call(t)}class b extends S{constructor(){super(...arguments),this.data=[],this.options={},this.points=[],this.graphCollection=[]}init(){super.init.apply(this,arguments),this.options=A({tenkanLine:{styles:{lineColor:this.color}},kijunLine:{styles:{lineColor:this.color}},chikouLine:{styles:{lineColor:this.color}},senkouSpanA:{styles:{lineColor:this.color,fill:k(this.color).setOpacity(.5).get()}},senkouSpanB:{styles:{lineColor:this.color,fill:k(this.color).setOpacity(.5).get()}},senkouSpan:{styles:{fill:k(this.color).setOpacity(.2).get()}}},this.options)}toYData(o){return[o.tenkanSen,o.kijunSen,o.chikouSpan,o.senkouSpanA,o.senkouSpanB]}translate(){for(let o of(f().seriesTypes.sma.prototype.translate.apply(this),this.points))for(let t of this.pointArrayMap){let e=o[t];v(e)&&(o["plot"+t]=this.yAxis.toPixels(e,!0),o.plotY=o["plot"+t],o.tooltipPos=[o.plotX,o["plot"+t]],o.isNull=!1)}}drawGraph(){let o=this,t=o.points,e=o.options,n=o.graph,i=o.color,s={options:{gapSize:e.gapSize}},p=o.pointArrayMap.length,r=[[],[],[],[],[],[]],a={tenkanLine:r[0],kijunLine:r[1],chikouLine:r[2],senkouSpanA:r[3],senkouSpanB:r[4],senkouSpan:r[5]},l=[],h=o.options.senkouSpan,u=h.color||h.styles.fill,c=h.negativeColor,g=[[],[]],d=[[],[]],k=t.length,S=0,x,m,v,C,P,b,G,H,N,X,w,T,j;for(o.ikhMap=a;k--;){for(v=0,m=t[k];v<p;v++)y(m[x=o.pointArrayMap[v]])&&r[v].push({plotX:m.plotX,plotY:m["plot"+x],isNull:!1});if(c&&k!==t.length-1){let o=a.senkouSpanB.length-1,t=function(o,t,e,n){if(o&&t&&e&&n){let i=t.plotX-o.plotX,s=t.plotY-o.plotY,p=n.plotX-e.plotX,r=n.plotY-e.plotY,a=o.plotX-e.plotX,l=o.plotY-e.plotY,h=(-s*a+i*l)/(-p*s+i*r),u=(p*l-r*a)/(-p*s+i*r);if(h>=0&&h<=1&&u>=0&&u<=1)return{plotX:o.plotX+u*i,plotY:o.plotY+u*s}}}(a.senkouSpanA[o-1],a.senkouSpanA[o],a.senkouSpanB[o-1],a.senkouSpanB[o]);if(t){let e={plotX:t.plotX,plotY:t.plotY,isNull:!1,intersectPoint:!0};a.senkouSpanA.splice(o,0,e),a.senkouSpanB.splice(o,0,e),l.push(o)}}}if(Y(a,(t,n)=>{e[n]&&"senkouSpan"!==n&&(o.points=r[S],o.options=A(e[n].styles,s),o.graph=o["graph"+n],o.fillGraph=!1,o.color=i,f().seriesTypes.sma.prototype.drawGraph.call(o),o["graph"+n]=o.graph),S++}),o.graphCollection)for(let t of o.graphCollection)o[t].destroy(),delete o[t];if(o.graphCollection=[],c&&a.senkouSpanA[0]&&a.senkouSpanB[0]){for(l.unshift(0),l.push(a.senkouSpanA.length-1),T=0;T<l.length-1;T++)if(C=l[T],P=l[T+1],b=a.senkouSpanB.slice(C,P+1),G=a.senkouSpanA.slice(C,P+1),Math.floor(b.length/2)>=1){let o=Math.floor(b.length/2);if(b[o].plotY===G[o].plotY){for(j=0,H=0,N=0;j<b.length;j++)H+=b[j].plotY,N+=G[j].plotY;g[w=H>N?0:1]=g[w].concat(b),d[w]=d[w].concat(G)}else g[w=b[o].plotY>G[o].plotY?0:1]=g[w].concat(b),d[w]=d[w].concat(G)}else g[w=b[0].plotY>G[0].plotY?0:1]=g[w].concat(b),d[w]=d[w].concat(G);["graphsenkouSpanColor","graphsenkouSpanNegativeColor"].forEach(function(t,n){g[n].length&&d[n].length&&(X=0===n?u:c,B({indicator:o,points:g[n],nextPoints:d[n],color:X,options:e,gap:s,graph:o[t]}),o[t]=o.graph,o.graphCollection.push(t))})}else B({indicator:o,points:a.senkouSpanB,nextPoints:a.senkouSpanA,color:u,options:e,gap:s,graph:o.graphsenkouSpan}),o.graphsenkouSpan=o.graph;delete o.nextPoints,delete o.fillGraph,o.points=t,o.options=e,o.graph=n,o.color=i}getGraphPath(o){let t=[],e,n=[];if(o=o||this.points,this.fillGraph&&this.nextPoints){if((e=f().seriesTypes.sma.prototype.getGraphPath.call(this,this.nextPoints))&&e.length){e[0][0]="L",t=f().seriesTypes.sma.prototype.getGraphPath.call(this,o),n=e.slice(0,t.length);for(let o=n.length-1;o>=0;o--)t.push(n[o])}}else t=f().seriesTypes.sma.prototype.getGraphPath.apply(this,arguments);return t}getValues(o,t){let e,n,i,s,p,r,a,l,h,u,c=t.period,g=t.periodTenkan,d=t.periodSenkouSpanB,f=o.xData,k=o.yData,S=o.xAxis,y=k&&k.length||0,x=C(S.series.map(o=>o.getColumn("x"))),v=[],A=[];if(f.length<=c||!m(k[0])||4!==k[0].length)return;let Y=f[0]-c*x;for(p=0;p<c;p++)A.push(Y+p*x);for(p=0;p<y;p++)p>=g&&(r=((n=P(k.slice(p-g,p))).high+n.low)/2),p>=c&&(h=(r+(a=((i=P(k.slice(p-c,p))).high+i.low)/2))/2),p>=d&&(u=((s=P(k.slice(p-d,p))).high+s.low)/2),l=k[p][3],e=f[p],void 0===v[p]&&(v[p]=[]),void 0===v[p+c-1]&&(v[p+c-1]=[]),v[p+c-1][0]=r,v[p+c-1][1]=a,v[p+c-1][2]=void 0,void 0===v[p+1]&&(v[p+1]=[]),v[p+1][2]=l,p<=c&&(v[p+c-1][3]=void 0,v[p+c-1][4]=void 0),void 0===v[p+2*c-2]&&(v[p+2*c-2]=[]),v[p+2*c-2][3]=h,v[p+2*c-2][4]=u,A.push(e);for(p=1;p<=c;p++)A.push(e+p*x);return{values:v,xData:A,yData:v}}}b.defaultOptions=A(S.defaultOptions,{params:{index:void 0,period:26,periodTenkan:9,periodSenkouSpanB:52},marker:{enabled:!1},tooltip:{pointFormat:'<span style="color:{point.color}">●</span> <b> {series.name}</b><br/>TENKAN SEN: {point.tenkanSen:.3f}<br/>KIJUN SEN: {point.kijunSen:.3f}<br/>CHIKOU SPAN: {point.chikouSpan:.3f}<br/>SENKOU SPAN A: {point.senkouSpanA:.3f}<br/>SENKOU SPAN B: {point.senkouSpanB:.3f}<br/>'},tenkanLine:{styles:{lineWidth:1,lineColor:void 0}},kijunLine:{styles:{lineWidth:1,lineColor:void 0}},chikouLine:{styles:{lineWidth:1,lineColor:void 0}},senkouSpanA:{styles:{lineWidth:1,lineColor:void 0}},senkouSpanB:{styles:{lineWidth:1,lineColor:void 0}},senkouSpan:{styles:{fill:"rgba(255, 0, 0, 0.5)"}},dataGrouping:{approximation:"ichimoku-averages"}}),x(b.prototype,{pointArrayMap:["tenkanSen","kijunSen","chikouSpan","senkouSpanA","senkouSpanB"],pointValKey:"tenkanSen",nameComponents:["periodSenkouSpanB","period","periodTenkan"]}),u()["ichimoku-averages"]=function(){let o,t=[];return[].forEach.call(arguments,function(e,n){t.push(u().average(e)),o=!o&&void 0===t[n]}),o?void 0:t},f().registerSeriesType("ikh",b);let G=l();return r.default})());