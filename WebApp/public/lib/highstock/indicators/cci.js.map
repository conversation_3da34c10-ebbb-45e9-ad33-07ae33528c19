{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/cci\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Sebastian <PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/cci\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/cci\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ cci_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/CCI/CCIIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n * */\n\n\nconst { sma: SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { isArray, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n// Utils:\n/**\n * @private\n */\nfunction sumArray(array) {\n    return array.reduce(function (prev, cur) {\n        return prev + cur;\n    }, 0);\n}\n/**\n * @private\n */\nfunction meanDeviation(arr, sma) {\n    const len = arr.length;\n    let sum = 0, i;\n    for (i = 0; i < len; i++) {\n        sum += Math.abs(sma - (arr[i]));\n    }\n    return sum;\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The CCI series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.cci\n *\n * @augments Highcharts.Series\n */\nclass CCIIndicator extends SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, TP = [], CCI = [], xData = [], yData = [];\n        let CCIPoint, p, periodTP = [], len, range = 1, smaTP, TPtemp, meanDev, i;\n        // CCI requires close value\n        if (xVal.length <= period ||\n            !isArray(yVal[0]) ||\n            yVal[0].length !== 4) {\n            return;\n        }\n        // Accumulate first N-points\n        while (range < period) {\n            p = yVal[range - 1];\n            TP.push((p[1] + p[2] + p[3]) / 3);\n            range++;\n        }\n        for (i = period; i <= yValLen; i++) {\n            p = yVal[i - 1];\n            TPtemp = (p[1] + p[2] + p[3]) / 3;\n            len = TP.push(TPtemp);\n            periodTP = TP.slice(len - period);\n            smaTP = sumArray(periodTP) / period;\n            meanDev = meanDeviation(periodTP, smaTP) / period;\n            CCIPoint = ((TPtemp - smaTP) / (0.015 * meanDev));\n            CCI.push([xVal[i - 1], CCIPoint]);\n            xData.push(xVal[i - 1]);\n            yData.push(CCIPoint);\n        }\n        return {\n            values: CCI,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Commodity Channel Index (CCI). This series requires `linkedTo` option to\n * be set.\n *\n * @sample stock/indicators/cci\n *         CCI indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/cci\n * @optionparent plotOptions.cci\n */\nCCIIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    /**\n     * @excluding index\n     */\n    params: {\n        index: void 0 // Unused index, do not inherit (#15362)\n    }\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('cci', CCIIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const CCI_CCIIndicator = ((/* unused pure expression or super */ null && (CCIIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `CCI` series. If the [type](#series.cci.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.cci\n * @since     6.0.0\n * @excluding dataParser, dataURL\n * @product   highstock\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/cci\n * @apioption series.cci\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/masters/indicators/cci.js\n\n\n\n\n/* harmony default export */ const cci_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "cci_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "sma", "SMAIndicator", "seriesTypes", "isArray", "merge", "CCIIndicator", "getV<PERSON>ues", "series", "params", "period", "xVal", "xData", "yVal", "yData", "yValLen", "length", "TP", "CCI", "CCIPoint", "p", "periodTP", "len", "range", "smaTP", "TPtemp", "meanDev", "i", "push", "sumArray", "array", "slice", "reduce", "prev", "cur", "meanDeviation", "arr", "sum", "Math", "abs", "values", "defaultOptions", "index", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,4BAA6B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACrH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,4BAA4B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAEzGA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAUjL,GAAM,CAAEE,IAAKC,CAAY,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAE/K,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAE,CAAIP,GAwC5B,OAAMQ,UAAqBJ,EAMvBK,UAAUC,CAAM,CAAEC,CAAM,CAAE,CACtB,IAAMC,EAASD,EAAOC,MAAM,CAAEC,EAAOH,EAAOI,KAAK,CAAEC,EAAOL,EAAOM,KAAK,CAAEC,EAAUF,EAAOA,EAAKG,MAAM,CAAG,EAAGC,EAAK,EAAE,CAAEC,EAAM,EAAE,CAAEN,EAAQ,EAAE,CAAEE,EAAQ,EAAE,CAC/IK,EAAUC,EAAGC,EAAW,EAAE,CAAEC,EAAKC,EAAQ,EAAGC,EAAOC,EAAQC,EAASC,EAExE,GAAIhB,CAAAA,CAAAA,EAAKK,MAAM,EAAIN,CAAK,GACnBN,EAAQS,CAAI,CAAC,EAAE,GAChBA,AAAmB,IAAnBA,CAAI,CAAC,EAAE,CAACG,MAAM,EAIlB,KAAOO,EAAQb,GACXU,EAAIP,CAAI,CAACU,EAAQ,EAAE,CACnBN,EAAGW,IAAI,CAAC,AAACR,CAAAA,CAAC,CAAC,EAAE,CAAGA,CAAC,CAAC,EAAE,CAAGA,CAAC,CAAC,EAAE,AAAD,EAAK,GAC/BG,IAEJ,IAAKI,EAAIjB,EAAQiB,GAAKZ,EAASY,IAE3BF,EAAS,AAACL,CAAAA,AADVA,CAAAA,EAAIP,CAAI,CAACc,EAAI,EAAE,AAAD,CACH,CAAC,EAAE,CAAGP,CAAC,CAAC,EAAE,CAAGA,CAAC,CAAC,EAAE,AAAD,EAAK,EAChCE,EAAML,EAAGW,IAAI,CAACH,GAEdD,EAAQK,AAvDTC,AAsDCT,CAAAA,EAAWJ,EAAGc,KAAK,CAACT,EAAMZ,EAAM,EAtD3BsB,MAAM,CAAC,SAAUC,CAAI,CAAEC,CAAG,EACnC,OAAOD,EAAOC,CAClB,EAAG,GAqDkCxB,EAC7BgB,EAAUS,AAjDtB,SAAuBC,CAAG,CAAEnC,CAAG,EAC3B,IAAMqB,EAAMc,EAAIpB,MAAM,CAClBqB,EAAM,EAAGV,EACb,IAAKA,EAAI,EAAGA,EAAIL,EAAKK,IACjBU,GAAOC,KAAKC,GAAG,CAACtC,EAAOmC,CAAG,CAACT,EAAE,EAEjC,OAAOU,CACX,EA0CoChB,EAAUG,GAASd,EAC3CS,EAAY,AAACM,CAAAA,EAASD,CAAI,EAAM,CAAA,KAAQE,CAAM,EAC9CR,EAAIU,IAAI,CAAC,CAACjB,CAAI,CAACgB,EAAI,EAAE,CAAER,EAAS,EAChCP,EAAMgB,IAAI,CAACjB,CAAI,CAACgB,EAAI,EAAE,EACtBb,EAAMc,IAAI,CAACT,GAEf,MAAO,CACHqB,OAAQtB,EACRN,MAAOA,EACPE,MAAOA,CACX,EACJ,CACJ,CAoBAR,EAAamC,cAAc,CAAGpC,EAAMH,EAAauC,cAAc,CAAE,CAI7DhC,OAAQ,CACJiC,MAAO,KAAK,CAChB,CACJ,GACA1C,IAA0I2C,kBAAkB,CAAC,MAAOrC,GA+BvI,IAAMV,EAAYE,IAGrC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}