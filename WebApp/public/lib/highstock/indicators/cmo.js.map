{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/cmo\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Pawel Lysy\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/cmo\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/cmo\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ cmo_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/CMO/CMOIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { isNumber, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The CMO series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.cmo\n *\n * @augments Highcharts.Series\n */\nclass CMOIndicator extends SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, CMO = [], xData = [], yData = [];\n        let i, index = params.index, values;\n        if (xVal.length < period) {\n            return;\n        }\n        if (isNumber(yVal[0])) {\n            values = yVal;\n        }\n        else {\n            // In case of the situation, where the series type has data length\n            // shorter then 4 (HLC, range), this ensures that we are not trying\n            // to reach the index out of bounds\n            index = Math.min(index, yVal[0].length - 1);\n            values = yVal.map((value) => value[index]);\n        }\n        let firstAddedSum = 0, sumOfHigherValues = 0, sumOfLowerValues = 0, y;\n        // Calculate first point, check if the first value\n        // was added to sum of higher/lower values, and what was the value.\n        for (let j = period; j > 0; j--) {\n            if (values[j] > values[j - 1]) {\n                sumOfHigherValues += values[j] - values[j - 1];\n            }\n            else if (values[j] < values[j - 1]) {\n                sumOfLowerValues += values[j - 1] - values[j];\n            }\n        }\n        // You might divide by 0 if all values are equal,\n        // so return 0 in this case.\n        y =\n            sumOfHigherValues + sumOfLowerValues > 0 ?\n                (100 * (sumOfHigherValues - sumOfLowerValues)) /\n                    (sumOfHigherValues + sumOfLowerValues) :\n                0;\n        xData.push(xVal[period]);\n        yData.push(y);\n        CMO.push([xVal[period], y]);\n        for (i = period + 1; i < yValLen; i++) {\n            firstAddedSum = Math.abs(values[i - period - 1] - values[i - period]);\n            if (values[i] > values[i - 1]) {\n                sumOfHigherValues += values[i] - values[i - 1];\n            }\n            else if (values[i] < values[i - 1]) {\n                sumOfLowerValues += values[i - 1] - values[i];\n            }\n            // Check, to which sum was the first value added to,\n            // and subtract this value from given sum.\n            if (values[i - period] > values[i - period - 1]) {\n                sumOfHigherValues -= firstAddedSum;\n            }\n            else {\n                sumOfLowerValues -= firstAddedSum;\n            }\n            // Same as above.\n            y =\n                sumOfHigherValues + sumOfLowerValues > 0 ?\n                    (100 * (sumOfHigherValues - sumOfLowerValues)) /\n                        (sumOfHigherValues + sumOfLowerValues) :\n                    0;\n            xData.push(xVal[i]);\n            yData.push(y);\n            CMO.push([xVal[i], y]);\n        }\n        return {\n            values: CMO,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Chande Momentum Oscilator (CMO) technical indicator. This series\n * requires the `linkedTo` option to be set and should be loaded after\n * the `stock/indicators/indicators.js` file.\n *\n * @sample stock/indicators/cmo\n *         CMO indicator\n *\n * @extends      plotOptions.sma\n * @since 9.1.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/cmo\n * @optionparent plotOptions.cmo\n */\nCMOIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    params: {\n        period: 20,\n        index: 3\n    }\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('cmo', CMOIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const CMO_CMOIndicator = ((/* unused pure expression or super */ null && (CMOIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `CMO` series. If the [type](#series.cmo.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.cmo\n * @since 9.1.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/cmo\n * @apioption series.cmo\n */\n(''); // To include the above in the js output\n\n;// ./code/es-modules/masters/indicators/cmo.js\n\n\n\n\n/* harmony default export */ const cmo_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "cmo_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "sma", "SMAIndicator", "seriesTypes", "isNumber", "merge", "CMOIndicator", "getV<PERSON>ues", "series", "params", "period", "xVal", "xData", "yVal", "yData", "yValLen", "length", "CMO", "i", "index", "values", "Math", "min", "map", "value", "firstAddedSum", "sumOfHigher<PERSON><PERSON><PERSON>", "sumOfLowerValues", "y", "j", "push", "abs", "defaultOptions", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,4BAA6B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACrH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,4BAA4B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAEzGA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAWjL,GAAM,CAAEE,IAAKC,CAAY,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAE/K,CAAEC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAE,CAAIP,GAe7B,OAAMQ,UAAqBJ,EAMvBK,UAAUC,CAAM,CAAEC,CAAM,CAAE,CACtB,IAAMC,EAASD,EAAOC,MAAM,CAAEC,EAAOH,EAAOI,KAAK,CAAEC,EAAOL,EAAOM,KAAK,CAAEC,EAAUF,EAAOA,EAAKG,MAAM,CAAG,EAAGC,EAAM,EAAE,CAAEL,EAAQ,EAAE,CAAEE,EAAQ,EAAE,CACtII,EAAGC,EAAQV,EAAOU,KAAK,CAAEC,EAC7B,GAAIT,EAAKK,MAAM,CAAGN,EACd,OAEAN,EAASS,CAAI,CAAC,EAAE,EAChBO,EAASP,GAMTM,EAAQE,KAAKC,GAAG,CAACH,EAAON,CAAI,CAAC,EAAE,CAACG,MAAM,CAAG,GACzCI,EAASP,EAAKU,GAAG,CAAC,AAACC,GAAUA,CAAK,CAACL,EAAM,GAE7C,IAAIM,EAAgB,EAAGC,EAAoB,EAAGC,EAAmB,EAAGC,EAGpE,IAAK,IAAIC,EAAInB,EAAQmB,EAAI,EAAGA,IACpBT,CAAM,CAACS,EAAE,CAAGT,CAAM,CAACS,EAAI,EAAE,CACzBH,GAAqBN,CAAM,CAACS,EAAE,CAAGT,CAAM,CAACS,EAAI,EAAE,CAEzCT,CAAM,CAACS,EAAE,CAAGT,CAAM,CAACS,EAAI,EAAE,EAC9BF,CAAAA,GAAoBP,CAAM,CAACS,EAAI,EAAE,CAAGT,CAAM,CAACS,EAAE,AAAD,EAapD,IARAD,EACIF,EAAoBC,EAAmB,EACnC,AAAC,IAAOD,CAAAA,EAAoBC,CAAe,EACtCD,CAAAA,EAAoBC,CAAe,EACxC,EACRf,EAAMkB,IAAI,CAACnB,CAAI,CAACD,EAAO,EACvBI,EAAMgB,IAAI,CAACF,GACXX,EAAIa,IAAI,CAAC,CAACnB,CAAI,CAACD,EAAO,CAAEkB,EAAE,EACrBV,EAAIR,EAAS,EAAGQ,EAAIH,EAASG,IAC9BO,EAAgBJ,KAAKU,GAAG,CAACX,CAAM,CAACF,EAAIR,EAAS,EAAE,CAAGU,CAAM,CAACF,EAAIR,EAAO,EAChEU,CAAM,CAACF,EAAE,CAAGE,CAAM,CAACF,EAAI,EAAE,CACzBQ,GAAqBN,CAAM,CAACF,EAAE,CAAGE,CAAM,CAACF,EAAI,EAAE,CAEzCE,CAAM,CAACF,EAAE,CAAGE,CAAM,CAACF,EAAI,EAAE,EAC9BS,CAAAA,GAAoBP,CAAM,CAACF,EAAI,EAAE,CAAGE,CAAM,CAACF,EAAE,AAAD,EAI5CE,CAAM,CAACF,EAAIR,EAAO,CAAGU,CAAM,CAACF,EAAIR,EAAS,EAAE,CAC3CgB,GAAqBD,EAGrBE,GAAoBF,EAGxBG,EACIF,EAAoBC,EAAmB,EACnC,AAAC,IAAOD,CAAAA,EAAoBC,CAAe,EACtCD,CAAAA,EAAoBC,CAAe,EACxC,EACRf,EAAMkB,IAAI,CAACnB,CAAI,CAACO,EAAE,EAClBJ,EAAMgB,IAAI,CAACF,GACXX,EAAIa,IAAI,CAAC,CAACnB,CAAI,CAACO,EAAE,CAAEU,EAAE,EAEzB,MAAO,CACHR,OAAQH,EACRL,MAAOA,EACPE,MAAOA,CACX,CACJ,CACJ,CAqBAR,EAAa0B,cAAc,CAAG3B,EAAMH,EAAa8B,cAAc,CAAE,CAC7DvB,OAAQ,CACJC,OAAQ,GACRS,MAAO,CACX,CACJ,GACAnB,IAA0IiC,kBAAkB,CAAC,MAAO3B,GA+BvI,IAAMV,EAAYE,IAGrC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}