!/**
 * Highstock JS v12.3.0 (2025-06-21)
 * @module highcharts/indicators/stochastic
 * @requires highcharts
 * @requires highcharts/modules/stock
 *
 * Indicator series type for Highcharts Stock
 *
 * (c) 2010-2025 Paweł Fus
 *
 * License: www.highcharts.com/license
 */function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(e._Highcharts,e._Highcharts.SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/indicators/stochastic",["highcharts/highcharts"],function(e){return t(e,e.SeriesRegistry)}):"object"==typeof exports?exports["highcharts/indicators/stochastic"]=t(e._Highcharts,e._Highcharts.SeriesRegistry):e.Highcharts=t(e.Highcharts,e.Highcharts.SeriesRegistry)}("undefined"==typeof window?this:window,(e,t)=>(()=>{"use strict";var i,r={512:e=>{e.exports=t},944:t=>{t.exports=e}},o={};function a(e){var t=o[e];if(void 0!==t)return t.exports;var i=o[e]={exports:{}};return r[e](i,i.exports,a),i.exports}a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var i in t)a.o(t,i)&&!a.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var s={};a.d(s,{default:()=>v});var n=a(944),p=a.n(n);let l={getArrayExtremes:function(e,t,i){return e.reduce((e,r)=>[Math.min(e[0],r[t]),Math.max(e[1],r[i])],[Number.MAX_VALUE,-Number.MAX_VALUE])}};var h=a(512),c=a.n(h);let{sma:{prototype:u}}=c().seriesTypes,{defined:d,error:f,merge:y}=p();!function(e){let t=["bottomLine"],i=["top","bottom"],r=["top"];function o(e){return"plot"+e.charAt(0).toUpperCase()+e.slice(1)}function a(e,t){let i=[];return(e.pointArrayMap||[]).forEach(e=>{e!==t&&i.push(o(e))}),i}function s(){let e=this,t=e.pointValKey,i=e.linesApiNames,r=e.areaLinesNames,s=e.points,n=e.options,p=e.graph,l={options:{gapSize:n.gapSize}},h=[],c=a(e,t),g=s.length,m;if(c.forEach((e,t)=>{for(h[t]=[];g--;)m=s[g],h[t].push({x:m.x,plotX:m.plotX,plotY:m[e],isNull:!d(m[e])});g=s.length}),e.userOptions.fillColor&&r.length){let t=h[c.indexOf(o(r[0]))],i=1===r.length?s:h[c.indexOf(o(r[1]))],a=e.color;e.points=i,e.nextPoints=t,e.color=e.userOptions.fillColor,e.options=y(s,l),e.graph=e.area,e.fillGraph=!0,u.drawGraph.call(e),e.area=e.graph,delete e.nextPoints,delete e.fillGraph,e.color=a}i.forEach((t,i)=>{h[i]?(e.points=h[i],n[t]?e.options=y(n[t].styles,l):f('Error: "There is no '+t+' in DOCS options declared. Check if linesApiNames are consistent with your DOCS line names."'),e.graph=e["graph"+t],u.drawGraph.call(e),e["graph"+t]=e.graph):f('Error: "'+t+" doesn't have equivalent in pointArrayMap. To many elements in linesApiNames relative to pointArrayMap.\"")}),e.points=s,e.options=n,e.graph=p,u.drawGraph.call(e)}function n(e){let t,i=[],r=[];if(e=e||this.points,this.fillGraph&&this.nextPoints){if((t=u.getGraphPath.call(this,this.nextPoints))&&t.length){t[0][0]="L",i=u.getGraphPath.call(this,e),r=t.slice(0,i.length);for(let e=r.length-1;e>=0;e--)i.push(r[e])}}else i=u.getGraphPath.apply(this,arguments);return i}function p(e){let t=[];return(this.pointArrayMap||[]).forEach(i=>{t.push(e[i])}),t}function l(){let e=this.pointArrayMap,t=[],i;t=a(this),u.translate.apply(this,arguments),this.points.forEach(r=>{e.forEach((e,o)=>{i=r[e],this.dataModify&&(i=this.dataModify.modifyValue(i)),null!==i&&(r[t[o]]=this.yAxis.toPixels(i,!0))})})}e.compose=function(e){let o=e.prototype;return o.linesApiNames=o.linesApiNames||t.slice(),o.pointArrayMap=o.pointArrayMap||i.slice(),o.pointValKey=o.pointValKey||"top",o.areaLinesNames=o.areaLinesNames||r.slice(),o.drawGraph=s,o.getGraphPath=n,o.toYData=p,o.translate=l,e}}(i||(i={}));let g=i,{sma:m}=c().seriesTypes,{extend:x,isArray:A,merge:N}=p();class b extends m{init(){super.init.apply(this,arguments),this.options=N({smoothedLine:{styles:{lineColor:this.color}}},this.options)}getValues(e,t){let i=t.periods[0],r=t.periods[1],o=e.xData,a=e.yData,s=a?a.length:0,n=[],p=[],h=[],c,u,d,f=null,y,g;if(s<i||!A(a[0])||4!==a[0].length)return;let m=!0,x=0;for(g=i-1;g<s;g++){if(c=a.slice(g-i+1,g+1),u=(y=l.getArrayExtremes(c,2,1))[0],isNaN(d=(a[g][3]-u)/(y[1]-u)*100)&&m){x++;continue}m&&!isNaN(d)&&(m=!1);let e=p.push(o[g]);isNaN(d)?h.push([h[e-2]&&"number"==typeof h[e-2][0]?h[e-2][0]:null,null]):h.push([d,null]),g>=x+(i-1)+(r-1)&&(f=super.getValues({xData:p.slice(-r),yData:h.slice(-r)},{period:r}).yData[0]),n.push([o[g],d,f]),h[e-1][1]=f}return{values:n,xData:p,yData:h}}}b.defaultOptions=N(m.defaultOptions,{params:{index:void 0,period:void 0,periods:[14,3]},marker:{enabled:!1},tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b><br/>%K: {point.y}<br/>%D: {point.smoothed}<br/>'},smoothedLine:{styles:{lineWidth:1,lineColor:void 0}},dataGrouping:{approximation:"averages"}}),x(b.prototype,{areaLinesNames:[],nameComponents:["periods"],nameBase:"Stochastic",pointArrayMap:["y","smoothed"],parallelArrays:["x","y","smoothed"],pointValKey:"y",linesApiNames:["smoothedLine"]}),g.compose(b),c().registerSeriesType("stochastic",b);let v=p();return s.default})());