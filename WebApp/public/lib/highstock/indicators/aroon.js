!/**
 * Highstock JS v12.3.0 (2025-06-21)
 * @module highcharts/indicators/aroon
 * @requires highcharts
 * @requires highcharts/modules/stock
 *
 * Indicator series type for Highcharts Stock
 *
 * (c) 2010-2025 Wojciech Chmiel
 *
 * License: www.highcharts.com/license
 */function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(e._Highcharts,e._Highcharts.SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/indicators/aroon",["highcharts/highcharts"],function(e){return t(e,e.SeriesRegistry)}):"object"==typeof exports?exports["highcharts/indicators/aroon"]=t(e._Highcharts,e._Highcharts.SeriesRegistry):e.Highcharts=t(e.Highcharts,e.Highcharts.SeriesRegistry)}("undefined"==typeof window?this:window,(e,t)=>(()=>{"use strict";var o,r={512:e=>{e.exports=t},944:t=>{t.exports=e}},a={};function i(e){var t=a[e];if(void 0!==t)return t.exports;var o=a[e]={exports:{}};return r[e](o,o.exports,i),o.exports}i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},i.d=(e,t)=>{for(var o in t)i.o(t,o)&&!i.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var n={};i.d(n,{default:()=>b});var s=i(944),p=i.n(s),l=i(512),h=i.n(l);let{sma:{prototype:c}}=h().seriesTypes,{defined:u,error:f,merge:d}=p();!function(e){let t=["bottomLine"],o=["top","bottom"],r=["top"];function a(e){return"plot"+e.charAt(0).toUpperCase()+e.slice(1)}function i(e,t){let o=[];return(e.pointArrayMap||[]).forEach(e=>{e!==t&&o.push(a(e))}),o}function n(){let e=this,t=e.pointValKey,o=e.linesApiNames,r=e.areaLinesNames,n=e.points,s=e.options,p=e.graph,l={options:{gapSize:s.gapSize}},h=[],y=i(e,t),g=n.length,m;if(y.forEach((e,t)=>{for(h[t]=[];g--;)m=n[g],h[t].push({x:m.x,plotX:m.plotX,plotY:m[e],isNull:!u(m[e])});g=n.length}),e.userOptions.fillColor&&r.length){let t=h[y.indexOf(a(r[0]))],o=1===r.length?n:h[y.indexOf(a(r[1]))],i=e.color;e.points=o,e.nextPoints=t,e.color=e.userOptions.fillColor,e.options=d(n,l),e.graph=e.area,e.fillGraph=!0,c.drawGraph.call(e),e.area=e.graph,delete e.nextPoints,delete e.fillGraph,e.color=i}o.forEach((t,o)=>{h[o]?(e.points=h[o],s[t]?e.options=d(s[t].styles,l):f('Error: "There is no '+t+' in DOCS options declared. Check if linesApiNames are consistent with your DOCS line names."'),e.graph=e["graph"+t],c.drawGraph.call(e),e["graph"+t]=e.graph):f('Error: "'+t+" doesn't have equivalent in pointArrayMap. To many elements in linesApiNames relative to pointArrayMap.\"")}),e.points=n,e.options=s,e.graph=p,c.drawGraph.call(e)}function s(e){let t,o=[],r=[];if(e=e||this.points,this.fillGraph&&this.nextPoints){if((t=c.getGraphPath.call(this,this.nextPoints))&&t.length){t[0][0]="L",o=c.getGraphPath.call(this,e),r=t.slice(0,o.length);for(let e=r.length-1;e>=0;e--)o.push(r[e])}}else o=c.getGraphPath.apply(this,arguments);return o}function p(e){let t=[];return(this.pointArrayMap||[]).forEach(o=>{t.push(e[o])}),t}function l(){let e=this.pointArrayMap,t=[],o;t=i(this),c.translate.apply(this,arguments),this.points.forEach(r=>{e.forEach((e,a)=>{o=r[e],this.dataModify&&(o=this.dataModify.modifyValue(o)),null!==o&&(r[t[a]]=this.yAxis.toPixels(o,!0))})})}e.compose=function(e){let a=e.prototype;return a.linesApiNames=a.linesApiNames||t.slice(),a.pointArrayMap=a.pointArrayMap||o.slice(),a.pointValKey=a.pointValKey||"top",a.areaLinesNames=a.areaLinesNames||r.slice(),a.drawGraph=n,a.getGraphPath=s,a.toYData=p,a.translate=l,e}}(o||(o={}));let y=o,{sma:g}=h().seriesTypes,{extend:m,merge:x,pick:A}=p();function v(e,t){let o=e[0],r=0,a;for(a=1;a<e.length;a++)("max"===t&&e[a]>=o||"min"===t&&e[a]<=o)&&(o=e[a],r=a);return r}class w extends g{getValues(e,t){let o,r,a,i,n,s=t.period,p=e.xData,l=e.yData,h=l?l.length:0,c=[],u=[],f=[];for(i=s-1;i<h;i++)a=v((n=l.slice(i-s+1,i+2)).map(function(e){return A(e[2],e)}),"min"),o=v(n.map(function(e){return A(e[1],e)}),"max")/s*100,r=a/s*100,p[i+1]&&(c.push([p[i+1],o,r]),u.push(p[i+1]),f.push([o,r]));return{values:c,xData:u,yData:f}}}w.defaultOptions=x(g.defaultOptions,{params:{index:void 0,period:25},marker:{enabled:!1},tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b><br/>Aroon Up: {point.y}<br/>Aroon Down: {point.aroonDown}<br/>'},aroonDown:{styles:{lineWidth:1,lineColor:void 0}},dataGrouping:{approximation:"averages"}}),m(w.prototype,{areaLinesNames:[],linesApiNames:["aroonDown"],nameBase:"Aroon",pointArrayMap:["y","aroonDown"],pointValKey:"y"}),y.compose(w),h().registerSeriesType("aroon",w);let b=p();return n.default})());