{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/vwap\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Paweł <PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/vwap\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/vwap\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ vwap_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/VWAP/VWAPIndicator.js\n/* *\n *\n *  (c) 2010-2025 Paweł Dalek\n *\n *  Volume Weighted Average Price (VWAP) indicator for Highcharts Stock\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { error, isArray, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Volume Weighted Average Price (VWAP) series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.vwap\n *\n * @augments Highcharts.Series\n */\nclass VWAPIndicator extends SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const indicator = this, chart = series.chart, xValues = series.xData, yValues = series.yData, period = params.period;\n        let isOHLC = true, volumeSeries;\n        // Checks if volume series exists\n        if (!(volumeSeries = (chart.get(params.volumeSeriesID)))) {\n            error('Series ' +\n                params.volumeSeriesID +\n                ' not found! Check `volumeSeriesID`.', true, chart);\n            return;\n        }\n        // Checks if series data fits the OHLC format\n        if (!(isArray(yValues[0]))) {\n            isOHLC = false;\n        }\n        return indicator.calculateVWAPValues(isOHLC, xValues, yValues, volumeSeries, period);\n    }\n    /**\n     * Main algorithm used to calculate Volume Weighted Average Price (VWAP)\n     * values\n     *\n     * @private\n     *\n     * @param {boolean} isOHLC\n     * Says if data has OHLC format\n     *\n     * @param {Array<number>} xValues\n     * Array of timestamps\n     *\n     * @param {Array<number|Array<number,number,number,number>>} yValues\n     * Array of yValues, can be an array of a four arrays (OHLC) or array of\n     * values (line)\n     *\n     * @param {Array<*>} volumeSeries\n     * Volume series\n     *\n     * @param {number} period\n     * Number of points to be calculated\n     *\n     * @return {Object}\n     * Object contains computed VWAP\n     **/\n    calculateVWAPValues(isOHLC, xValues, yValues, volumeSeries, period) {\n        const volumeValues = volumeSeries.getColumn('y'), volumeLength = volumeValues.length, pointsLength = xValues.length, cumulativePrice = [], cumulativeVolume = [], xData = [], yData = [], VWAP = [];\n        let commonLength, typicalPrice, cPrice, cVolume, i, j;\n        if (pointsLength <= volumeLength) {\n            commonLength = pointsLength;\n        }\n        else {\n            commonLength = volumeLength;\n        }\n        for (i = 0, j = 0; i < commonLength; i++) {\n            // Depending on whether series is OHLC or line type, price is\n            // average of the high, low and close or a simple value\n            typicalPrice = isOHLC ?\n                ((yValues[i][1] + yValues[i][2] +\n                    yValues[i][3]) / 3) :\n                yValues[i];\n            typicalPrice *= volumeValues[i];\n            cPrice = j ?\n                (cumulativePrice[i - 1] + typicalPrice) :\n                typicalPrice;\n            cVolume = j ?\n                (cumulativeVolume[i - 1] + volumeValues[i]) :\n                volumeValues[i];\n            cumulativePrice.push(cPrice);\n            cumulativeVolume.push(cVolume);\n            VWAP.push([xValues[i], (cPrice / cVolume)]);\n            xData.push(VWAP[i][0]);\n            yData.push(VWAP[i][1]);\n            j++;\n            if (j === period) {\n                j = 0;\n            }\n        }\n        return {\n            values: VWAP,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Volume Weighted Average Price indicator.\n *\n * This series requires `linkedTo` option to be set.\n *\n * @sample stock/indicators/vwap\n *         Volume Weighted Average Price indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/vwap\n * @optionparent plotOptions.vwap\n */\nVWAPIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    /**\n     * @excluding index\n     */\n    params: {\n        index: void 0, // Unchangeable index, do not inherit (#15362)\n        period: 30,\n        /**\n         * The id of volume series which is mandatory. For example using\n         * OHLC data, volumeSeriesID='volume' means the indicator will be\n         * calculated using OHLC and volume values.\n         */\n        volumeSeriesID: 'volume'\n    }\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('vwap', VWAPIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const VWAP_VWAPIndicator = ((/* unused pure expression or super */ null && (VWAPIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `Volume Weighted Average Price (VWAP)` series. If the\n * [type](#series.vwap.type) option is not specified, it is inherited from\n * [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.vwap\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/vwap\n * @apioption series.vwap\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/masters/indicators/vwap.js\n\n\n\n\n/* harmony default export */ const vwap_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "vwap_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "sma", "SMAIndicator", "seriesTypes", "error", "isArray", "merge", "VWAPIndicator", "getV<PERSON>ues", "series", "params", "chart", "xValues", "xData", "yV<PERSON><PERSON>", "yData", "period", "isOHLC", "volumeSeries", "volumeSeriesID", "indicator", "calculateVWAPValues", "common<PERSON><PERSON>th", "typicalPrice", "cPrice", "cVolume", "i", "j", "volumeValues", "getColumn", "volumeLength", "length", "pointsLength", "cumulativePrice", "cumulativeVolume", "VWAP", "push", "values", "defaultOptions", "index", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,6BAA8B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACtH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,6BAA6B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAE1GA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAejL,GAAM,CAAEE,IAAKC,CAAY,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAE/K,CAAEC,MAAAA,CAAK,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAE,CAAIR,GAenC,OAAMS,UAAsBL,EAMxBM,UAAUC,CAAM,CAAEC,CAAM,CAAE,CACtB,IAAwBC,EAAQF,EAAOE,KAAK,CAAEC,EAAUH,EAAOI,KAAK,CAAEC,EAAUL,EAAOM,KAAK,CAAEC,EAASN,EAAOM,MAAM,CAChHC,EAAS,CAAA,EAAMC,QAEnB,AAAMA,CAAAA,EAAgBP,EAAMtB,GAAG,CAACqB,EAAOS,cAAc,CAAC,GAOlD,AAAEd,EAAQS,CAAO,CAAC,EAAE,GACpBG,CAAAA,EAAS,CAAA,CAAI,EAEVG,AAbW,IAAI,CAaLC,mBAAmB,CAACJ,EAAQL,EAASE,EAASI,EAAcF,SATzEZ,EAAM,UACFM,EAAOS,cAAc,CACrB,sCAAuC,CAAA,EAAMR,EAQzD,CA0BAU,oBAAoBJ,CAAM,CAAEL,CAAO,CAAEE,CAAO,CAAEI,CAAY,CAAEF,CAAM,CAAE,CAChE,IACIM,EAAcC,EAAcC,EAAQC,EAASC,EAAGC,EAD9CC,EAAeV,EAAaW,SAAS,CAAC,KAAMC,EAAeF,EAAaG,MAAM,CAAEC,EAAepB,EAAQmB,MAAM,CAAEE,EAAkB,EAAE,CAAEC,EAAmB,EAAE,CAAErB,EAAQ,EAAE,CAAEE,EAAQ,EAAE,CAAEoB,EAAO,EAAE,CAQnM,IAAKT,EAAI,EALLJ,EADAU,GAAgBF,EACDE,EAGAF,EAEPH,EAAI,EAAGD,EAAIJ,EAAcI,IAOjCH,EAJeN,CAAAA,EACV,AAACH,CAAAA,CAAO,CAACY,EAAE,CAAC,EAAE,CAAGZ,CAAO,CAACY,EAAE,CAAC,EAAE,CAC3BZ,CAAO,CAACY,EAAE,CAAC,EAAE,AAAD,EAAK,EACrBZ,CAAO,CAACY,EAAE,AAAD,EACGE,CAAY,CAACF,EAAE,CAC/BF,EAASG,EACJM,CAAe,CAACP,EAAI,EAAE,CAAGH,EAC1BA,EACJE,EAAUE,EACLO,CAAgB,CAACR,EAAI,EAAE,CAAGE,CAAY,CAACF,EAAE,CAC1CE,CAAY,CAACF,EAAE,CACnBO,EAAgBG,IAAI,CAACZ,GACrBU,EAAiBE,IAAI,CAACX,GACtBU,EAAKC,IAAI,CAAC,CAACxB,CAAO,CAACc,EAAE,CAAGF,EAASC,EAAS,EAC1CZ,EAAMuB,IAAI,CAACD,CAAI,CAACT,EAAE,CAAC,EAAE,EACrBX,EAAMqB,IAAI,CAACD,CAAI,CAACT,EAAE,CAAC,EAAE,EAEjBC,EAAAA,IAAMX,GACNW,CAAAA,EAAI,CAAA,EAGZ,MAAO,CACHU,OAAQF,EACRtB,MAAOA,EACPE,MAAOA,CACX,CACJ,CACJ,CAqBAR,EAAc+B,cAAc,CAAGhC,EAAMJ,EAAaoC,cAAc,CAAE,CAI9D5B,OAAQ,CACJ6B,MAAO,KAAK,EACZvB,OAAQ,GAMRG,eAAgB,QACpB,CACJ,GACAnB,IAA0IwC,kBAAkB,CAAC,OAAQjC,GAgCxI,IAAMX,EAAaE,IAGtC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}