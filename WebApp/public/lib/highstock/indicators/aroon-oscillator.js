!/**
 * Highstock JS v12.3.0 (2025-06-21)
 * @module highcharts/indicators/aroon-oscillator
 * @requires highcharts
 * @requires highcharts/modules/stock
 *
 * Indicator series type for Highcharts Stock
 *
 * (c) 2010-2025 Wojciech Chmiel
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/indicators/aroon-oscillator",["highcharts/highcharts"],function(t){return e(t,t.SeriesRegistry)}):"object"==typeof exports?exports["highcharts/indicators/aroon-oscillator"]=e(t._Highcharts,t._Highcharts.SeriesRegistry):t.Highcharts=e(t.Highcharts,t.Highcharts.SeriesRegistry)}("undefined"==typeof window?this:window,(t,e)=>(()=>{"use strict";var r,a={512:t=>{t.exports=e},944:e=>{e.exports=t}},o={};function i(t){var e=o[t];if(void 0!==e)return e.exports;var r=o[t]={exports:{}};return a[t](r,r.exports,i),r.exports}i.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return i.d(e,{a:e}),e},i.d=(t,e)=>{for(var r in e)i.o(e,r)&&!i.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var s={};i.d(s,{default:()=>v});var n=i(944),p=i.n(n),l=i(512),h=i.n(l);let{sma:{prototype:c}}=h().seriesTypes,{defined:f,error:u,merge:y}=p();!function(t){let e=["bottomLine"],r=["top","bottom"],a=["top"];function o(t){return"plot"+t.charAt(0).toUpperCase()+t.slice(1)}function i(t,e){let r=[];return(t.pointArrayMap||[]).forEach(t=>{t!==e&&r.push(o(t))}),r}function s(){let t=this,e=t.pointValKey,r=t.linesApiNames,a=t.areaLinesNames,s=t.points,n=t.options,p=t.graph,l={options:{gapSize:n.gapSize}},h=[],g=i(t,e),d=s.length,m;if(g.forEach((t,e)=>{for(h[e]=[];d--;)m=s[d],h[e].push({x:m.x,plotX:m.plotX,plotY:m[t],isNull:!f(m[t])});d=s.length}),t.userOptions.fillColor&&a.length){let e=h[g.indexOf(o(a[0]))],r=1===a.length?s:h[g.indexOf(o(a[1]))],i=t.color;t.points=r,t.nextPoints=e,t.color=t.userOptions.fillColor,t.options=y(s,l),t.graph=t.area,t.fillGraph=!0,c.drawGraph.call(t),t.area=t.graph,delete t.nextPoints,delete t.fillGraph,t.color=i}r.forEach((e,r)=>{h[r]?(t.points=h[r],n[e]?t.options=y(n[e].styles,l):u('Error: "There is no '+e+' in DOCS options declared. Check if linesApiNames are consistent with your DOCS line names."'),t.graph=t["graph"+e],c.drawGraph.call(t),t["graph"+e]=t.graph):u('Error: "'+e+" doesn't have equivalent in pointArrayMap. To many elements in linesApiNames relative to pointArrayMap.\"")}),t.points=s,t.options=n,t.graph=p,c.drawGraph.call(t)}function n(t){let e,r=[],a=[];if(t=t||this.points,this.fillGraph&&this.nextPoints){if((e=c.getGraphPath.call(this,this.nextPoints))&&e.length){e[0][0]="L",r=c.getGraphPath.call(this,t),a=e.slice(0,r.length);for(let t=a.length-1;t>=0;t--)r.push(a[t])}}else r=c.getGraphPath.apply(this,arguments);return r}function p(t){let e=[];return(this.pointArrayMap||[]).forEach(r=>{e.push(t[r])}),e}function l(){let t=this.pointArrayMap,e=[],r;e=i(this),c.translate.apply(this,arguments),this.points.forEach(a=>{t.forEach((t,o)=>{r=a[t],this.dataModify&&(r=this.dataModify.modifyValue(r)),null!==r&&(a[e[o]]=this.yAxis.toPixels(r,!0))})})}t.compose=function(t){let o=t.prototype;return o.linesApiNames=o.linesApiNames||e.slice(),o.pointArrayMap=o.pointArrayMap||r.slice(),o.pointValKey=o.pointValKey||"top",o.areaLinesNames=o.areaLinesNames||a.slice(),o.drawGraph=s,o.getGraphPath=n,o.toYData=p,o.translate=l,t}}(r||(r={}));let g=r,{aroon:d}=h().seriesTypes,{extend:m,merge:x}=p();class A extends d{getValues(t,e){let r,a,o=[],i=[],s=[],n=super.getValues.call(this,t,e);for(a=0;a<n.yData.length;a++)r=n.yData[a][0]-n.yData[a][1],o.push([n.xData[a],r]),i.push(n.xData[a]),s.push(r);return{values:o,xData:i,yData:s}}}A.defaultOptions=x(d.defaultOptions,{tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b>: {point.y}'}}),m(A.prototype,{nameBase:"Aroon Oscillator",linesApiNames:[],pointArrayMap:["y"],pointValKey:"y"}),g.compose(d),h().registerSeriesType("aroonoscillator",A);let v=p();return s.default})());