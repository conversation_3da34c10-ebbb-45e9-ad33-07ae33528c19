{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/keltner-channels\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 <PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/keltner-channels\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/keltner-channels\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ keltner_channels_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/MultipleLinesComposition.js\n/**\n *\n *  (c) 2010-2025 Wojciech Chmiel\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: { prototype: smaProto } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { defined, error, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\nvar MultipleLinesComposition;\n(function (MultipleLinesComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    /**\n     * Additional lines DOCS names. Elements of linesApiNames array should\n     * be consistent with DOCS line names defined in your implementation.\n     * Notice that linesApiNames should have decreased amount of elements\n     * relative to pointArrayMap (without pointValKey).\n     *\n     * @private\n     * @type {Array<string>}\n     */\n    const linesApiNames = ['bottomLine'];\n    /**\n     * Lines ids. Required to plot appropriate amount of lines.\n     * Notice that pointArrayMap should have more elements than\n     * linesApiNames, because it contains main line and additional lines ids.\n     * Also it should be consistent with amount of lines calculated in\n     * getValues method from your implementation.\n     *\n     * @private\n     * @type {Array<string>}\n     */\n    const pointArrayMap = ['top', 'bottom'];\n    /**\n     * Names of the lines, between which the area should be plotted.\n     * If the drawing of the area should\n     * be disabled for some indicators, leave this option as an empty array.\n     * Names should be the same as the names in the pointArrayMap.\n     *\n     * @private\n     * @type {Array<string>}\n     */\n    const areaLinesNames = ['top'];\n    /**\n     * Main line id.\n     *\n     * @private\n     * @type {string}\n     */\n    const pointValKey = 'top';\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Composition useful for all indicators that have more than one line.\n     * Compose it with your implementation where you will provide the\n     * `getValues` method appropriate to your indicator and `pointArrayMap`,\n     * `pointValKey`, `linesApiNames` properties. Notice that `pointArrayMap`\n     * should be consistent with the amount of lines calculated in the\n     * `getValues` method.\n     *\n     * @private\n     */\n    function compose(IndicatorClass) {\n        const proto = IndicatorClass.prototype;\n        proto.linesApiNames = (proto.linesApiNames ||\n            linesApiNames.slice());\n        proto.pointArrayMap = (proto.pointArrayMap ||\n            pointArrayMap.slice());\n        proto.pointValKey = (proto.pointValKey ||\n            pointValKey);\n        proto.areaLinesNames = (proto.areaLinesNames ||\n            areaLinesNames.slice());\n        proto.drawGraph = indicatorDrawGraph;\n        proto.getGraphPath = indicatorGetGraphPath;\n        proto.toYData = indicatorToYData;\n        proto.translate = indicatorTranslate;\n        return IndicatorClass;\n    }\n    MultipleLinesComposition.compose = compose;\n    /**\n     * Generate the API name of the line\n     *\n     * @private\n     * @param propertyName name of the line\n     */\n    function getLineName(propertyName) {\n        return ('plot' +\n            propertyName.charAt(0).toUpperCase() +\n            propertyName.slice(1));\n    }\n    /**\n     * Create translatedLines Collection based on pointArrayMap.\n     *\n     * @private\n     * @param {string} [excludedValue]\n     *        Main line id\n     * @return {Array<string>}\n     *         Returns translated lines names without excluded value.\n     */\n    function getTranslatedLinesNames(indicator, excludedValue) {\n        const translatedLines = [];\n        (indicator.pointArrayMap || []).forEach((propertyName) => {\n            if (propertyName !== excludedValue) {\n                translatedLines.push(getLineName(propertyName));\n            }\n        });\n        return translatedLines;\n    }\n    /**\n     * Draw main and additional lines.\n     *\n     * @private\n     */\n    function indicatorDrawGraph() {\n        const indicator = this, pointValKey = indicator.pointValKey, linesApiNames = indicator.linesApiNames, areaLinesNames = indicator.areaLinesNames, mainLinePoints = indicator.points, mainLineOptions = indicator.options, mainLinePath = indicator.graph, gappedExtend = {\n            options: {\n                gapSize: mainLineOptions.gapSize\n            }\n        }, \n        // Additional lines point place holders:\n        secondaryLines = [], secondaryLinesNames = getTranslatedLinesNames(indicator, pointValKey);\n        let pointsLength = mainLinePoints.length, point;\n        // Generate points for additional lines:\n        secondaryLinesNames.forEach((plotLine, index) => {\n            // Create additional lines point place holders\n            secondaryLines[index] = [];\n            while (pointsLength--) {\n                point = mainLinePoints[pointsLength];\n                secondaryLines[index].push({\n                    x: point.x,\n                    plotX: point.plotX,\n                    plotY: point[plotLine],\n                    isNull: !defined(point[plotLine])\n                });\n            }\n            pointsLength = mainLinePoints.length;\n        });\n        // Modify options and generate area fill:\n        if (indicator.userOptions.fillColor && areaLinesNames.length) {\n            const index = secondaryLinesNames.indexOf(getLineName(areaLinesNames[0])), secondLinePoints = secondaryLines[index], firstLinePoints = areaLinesNames.length === 1 ?\n                mainLinePoints :\n                secondaryLines[secondaryLinesNames.indexOf(getLineName(areaLinesNames[1]))], originalColor = indicator.color;\n            indicator.points = firstLinePoints;\n            indicator.nextPoints = secondLinePoints;\n            indicator.color = indicator.userOptions.fillColor;\n            indicator.options = merge(mainLinePoints, gappedExtend);\n            indicator.graph = indicator.area;\n            indicator.fillGraph = true;\n            smaProto.drawGraph.call(indicator);\n            indicator.area = indicator.graph;\n            // Clean temporary properties:\n            delete indicator.nextPoints;\n            delete indicator.fillGraph;\n            indicator.color = originalColor;\n        }\n        // Modify options and generate additional lines:\n        linesApiNames.forEach((lineName, i) => {\n            if (secondaryLines[i]) {\n                indicator.points = secondaryLines[i];\n                if (mainLineOptions[lineName]) {\n                    indicator.options = merge(mainLineOptions[lineName].styles, gappedExtend);\n                }\n                else {\n                    error('Error: \"There is no ' + lineName +\n                        ' in DOCS options declared. Check if linesApiNames' +\n                        ' are consistent with your DOCS line names.\"');\n                }\n                indicator.graph = indicator['graph' + lineName];\n                smaProto.drawGraph.call(indicator);\n                // Now save lines:\n                indicator['graph' + lineName] = indicator.graph;\n            }\n            else {\n                error('Error: \"' + lineName + ' doesn\\'t have equivalent ' +\n                    'in pointArrayMap. To many elements in linesApiNames ' +\n                    'relative to pointArrayMap.\"');\n            }\n        });\n        // Restore options and draw a main line:\n        indicator.points = mainLinePoints;\n        indicator.options = mainLineOptions;\n        indicator.graph = mainLinePath;\n        smaProto.drawGraph.call(indicator);\n    }\n    /**\n     * Create the path based on points provided as argument.\n     * If indicator.nextPoints option is defined, create the areaFill.\n     *\n     * @private\n     * @param points Points on which the path should be created\n     */\n    function indicatorGetGraphPath(points) {\n        let areaPath, path = [], higherAreaPath = [];\n        points = points || this.points;\n        // Render Span\n        if (this.fillGraph && this.nextPoints) {\n            areaPath = smaProto.getGraphPath.call(this, this.nextPoints);\n            if (areaPath && areaPath.length) {\n                areaPath[0][0] = 'L';\n                path = smaProto.getGraphPath.call(this, points);\n                higherAreaPath = areaPath.slice(0, path.length);\n                // Reverse points, so that the areaFill will start from the end:\n                for (let i = higherAreaPath.length - 1; i >= 0; i--) {\n                    path.push(higherAreaPath[i]);\n                }\n            }\n        }\n        else {\n            path = smaProto.getGraphPath.apply(this, arguments);\n        }\n        return path;\n    }\n    /**\n     * @private\n     * @param {Highcharts.Point} point\n     *        Indicator point\n     * @return {Array<number>}\n     *         Returns point Y value for all lines\n     */\n    function indicatorToYData(point) {\n        const pointColl = [];\n        (this.pointArrayMap || []).forEach((propertyName) => {\n            pointColl.push(point[propertyName]);\n        });\n        return pointColl;\n    }\n    /**\n     * Add lines plot pixel values.\n     *\n     * @private\n     */\n    function indicatorTranslate() {\n        const pointArrayMap = this.pointArrayMap;\n        let LinesNames = [], value;\n        LinesNames = getTranslatedLinesNames(this);\n        smaProto.translate.apply(this, arguments);\n        this.points.forEach((point) => {\n            pointArrayMap.forEach((propertyName, i) => {\n                value = point[propertyName];\n                // If the modifier, like for example compare exists,\n                // modified the original value by that method, #15867.\n                if (this.dataModify) {\n                    value = this.dataModify.modifyValue(value);\n                }\n                if (value !== null) {\n                    point[LinesNames[i]] = this.yAxis.toPixels(value, true);\n                }\n            });\n        });\n    }\n})(MultipleLinesComposition || (MultipleLinesComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Indicators_MultipleLinesComposition = (MultipleLinesComposition);\n\n;// ./code/es-modules/Stock/Indicators/KeltnerChannels/KeltnerChannelsIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { sma: SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { correctFloat, extend, merge: KeltnerChannelsIndicator_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Keltner Channels series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.keltnerchannels\n *\n * @augments Highcharts.Series\n */\nclass KeltnerChannelsIndicator extends SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    init() {\n        highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().seriesTypes.sma.prototype.init.apply(this, arguments);\n        // Set default color for lines:\n        this.options = KeltnerChannelsIndicator_merge({\n            topLine: {\n                styles: {\n                    lineColor: this.color\n                }\n            },\n            bottomLine: {\n                styles: {\n                    lineColor: this.color\n                }\n            }\n        }, this.options);\n    }\n    getValues(series, params) {\n        const period = params.period, periodATR = params.periodATR, multiplierATR = params.multiplierATR, index = params.index, yVal = series.yData, yValLen = yVal ? yVal.length : 0, \n        // Keltner Channels array structure:\n        // 0-date, 1-top line, 2-middle line, 3-bottom line\n        KC = [], seriesEMA = highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().seriesTypes.ema.prototype.getValues(series, {\n            period: period,\n            index: index\n        }), seriesATR = highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().seriesTypes.atr.prototype.getValues(series, {\n            period: periodATR\n        }), xData = [], yData = [];\n        // Middle line, top line and bottom lineI\n        let ML, TL, BL, date, pointEMA, pointATR, i;\n        if (yValLen < period) {\n            return;\n        }\n        for (i = period; i <= yValLen; i++) {\n            pointEMA = seriesEMA.values[i - period];\n            pointATR = seriesATR.values[i - periodATR];\n            date = pointEMA[0];\n            TL = correctFloat(pointEMA[1] + (multiplierATR * pointATR[1]));\n            BL = correctFloat(pointEMA[1] - (multiplierATR * pointATR[1]));\n            ML = pointEMA[1];\n            KC.push([date, TL, ML, BL]);\n            xData.push(date);\n            yData.push([TL, ML, BL]);\n        }\n        return {\n            values: KC,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Keltner Channels. This series requires the `linkedTo` option to be set\n * and should be loaded after the `stock/indicators/indicators.js`,\n * `stock/indicators/atr.js`, and `stock/ema/.js`.\n *\n * @sample {highstock} stock/indicators/keltner-channels\n *         Keltner Channels\n *\n * @extends      plotOptions.sma\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, compare, compareBase, joinBy, keys,\n *               navigatorOptions, pointInterval, pointIntervalUnit,\n *               pointPlacement, pointRange, pointStart,showInNavigator,\n *               stacking\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/keltner-channels\n * @optionparent plotOptions.keltnerchannels\n */\nKeltnerChannelsIndicator.defaultOptions = KeltnerChannelsIndicator_merge(SMAIndicator.defaultOptions, {\n    /**\n     * Option for fill color between lines in Keltner Channels Indicator.\n     *\n     * @sample {highstock} stock/indicators/indicator-area-fill\n     *      Background fill between lines.\n     *\n     * @type {Highcharts.Color}\n     * @since 9.3.2\n     * @apioption plotOptions.keltnerchannels.fillColor\n     *\n     */\n    params: {\n        /**\n         * The point index which indicator calculations will base. For\n         * example using OHLC data, index=2 means the indicator will be\n         * calculated using Low values.\n         */\n        index: 0,\n        period: 20,\n        /**\n         * The ATR period.\n         */\n        periodATR: 10,\n        /**\n         * The ATR multiplier.\n         */\n        multiplierATR: 2\n    },\n    /**\n     * Bottom line options.\n     *\n     */\n    bottomLine: {\n        /**\n         * Styles for a bottom line.\n         *\n         */\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line. If not set, it's inherited from\n             * `plotOptions.keltnerchannels.color`\n             */\n            lineColor: void 0\n        }\n    },\n    /**\n     * Top line options.\n     *\n     * @extends plotOptions.keltnerchannels.bottomLine\n     */\n    topLine: {\n        styles: {\n            lineWidth: 1,\n            lineColor: void 0\n        }\n    },\n    tooltip: {\n        pointFormat: '<span style=\"color:{point.color}\">\\u25CF</span><b> {series.name}</b><br/>Upper Channel: {point.top}<br/>EMA({series.options.params.period}): {point.middle}<br/>Lower Channel: {point.bottom}<br/>'\n    },\n    marker: {\n        enabled: false\n    },\n    dataGrouping: {\n        approximation: 'averages'\n    },\n    lineWidth: 1\n});\nextend(KeltnerChannelsIndicator.prototype, {\n    nameBase: 'Keltner Channels',\n    areaLinesNames: ['top', 'bottom'],\n    nameComponents: ['period', 'periodATR', 'multiplierATR'],\n    linesApiNames: ['topLine', 'bottomLine'],\n    pointArrayMap: ['top', 'middle', 'bottom'],\n    pointValKey: 'middle'\n});\nIndicators_MultipleLinesComposition.compose(KeltnerChannelsIndicator);\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('keltnerchannels', KeltnerChannelsIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const KeltnerChannels_KeltnerChannelsIndicator = ((/* unused pure expression or super */ null && (KeltnerChannelsIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A Keltner Channels indicator. If the [type](#series.keltnerchannels.type)\n * option is not specified, it is inherited from[chart.type](#chart.type).\n *\n * @extends      series,plotOptions.keltnerchannels\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, compare, compareBase, dataParser, dataURL,\n *               joinBy, keys, navigatorOptions, pointInterval,\n *               pointIntervalUnit, pointPlacement, pointRange, pointStart,\n *               stacking, showInNavigator\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/keltner-channels\n * @apioption    series.keltnerchannels\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/masters/indicators/keltner-channels.js\n\n\n\n\n/* harmony default export */ const keltner_channels_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "MultipleLinesComposition", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "keltner_channels_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "sma", "sma<PERSON><PERSON><PERSON>", "seriesTypes", "defined", "error", "merge", "linesApiNames", "pointArrayMap", "areaLinesNames", "getLineName", "propertyName", "char<PERSON>t", "toUpperCase", "slice", "getTranslatedLinesNames", "indicator", "excludedValue", "translatedLines", "for<PERSON>ach", "push", "indicatorDrawGraph", "pointVal<PERSON>ey", "mainLinePoints", "points", "mainLineOptions", "options", "mainLinePath", "graph", "gappedExtend", "gapSize", "secondaryLines", "secondaryLinesNames", "pointsLength", "length", "point", "plotLine", "index", "x", "plotX", "plotY", "isNull", "userOptions", "fillColor", "secondLinePoints", "indexOf", "firstLinePoints", "originalColor", "color", "nextPoints", "area", "fillGraph", "drawGraph", "lineName", "i", "styles", "indicatorGetGraphPath", "areaPath", "path", "higherAreaPath", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "apply", "arguments", "indicatorToYData", "pointColl", "indicatorTranslate", "LinesNames", "value", "translate", "dataModify", "modifyValue", "yAxis", "toPixels", "compose", "IndicatorClass", "proto", "toYData", "Indicators_MultipleLinesComposition", "SMAIndicator", "correctFloat", "extend", "KeltnerChannelsIndicator_merge", "KeltnerChannelsIndicator", "init", "topLine", "lineColor", "bottomLine", "getV<PERSON>ues", "series", "params", "ML", "TL", "BL", "date", "pointEMA", "pointATR", "period", "periodATR", "multiplierATR", "yVal", "yData", "yValLen", "KC", "seriesEMA", "ema", "seriesATR", "atr", "xData", "values", "defaultOptions", "lineWidth", "tooltip", "pointFormat", "marker", "enabled", "dataGrouping", "approximation", "nameBase", "nameComponents", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,yCAA0C,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GAClI,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,yCAAyC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAEtHA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IA0GNC,EA1GUC,EAAuB,CAE/B,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAab,OAAO,CAG5B,IAAIC,EAASS,CAAwB,CAACE,EAAS,CAAG,CAGjDZ,QAAS,CAAC,CACX,EAMA,OAHAS,CAAmB,CAACG,EAAS,CAACX,EAAQA,EAAOD,OAAO,CAAEW,GAG/CV,EAAOD,OAAO,AACtB,CAMCW,EAAoBI,CAAC,CAAG,AAACd,IACxB,IAAIe,EAASf,GAAUA,EAAOgB,UAAU,CACvC,IAAOhB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAU,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAAClB,EAASoB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACtB,EAASqB,IAC5EE,OAAOC,cAAc,CAACxB,EAASqB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAajL,GAAM,CAAEE,IAAK,CAAET,UAAWU,CAAQ,CAAE,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAE1L,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAE,CAAIR,KAOnC,AAAC,SAAU3B,CAAwB,EAoB/B,IAAMoC,EAAgB,CAAC,aAAa,CAW9BC,EAAgB,CAAC,MAAO,SAAS,CAUjCC,EAAiB,CAAC,MAAM,CA8C9B,SAASC,EAAYC,CAAY,EAC7B,MAAQ,OACJA,EAAaC,MAAM,CAAC,GAAGC,WAAW,GAClCF,EAAaG,KAAK,CAAC,EAC3B,CAUA,SAASC,EAAwBC,CAAS,CAAEC,CAAa,EACrD,IAAMC,EAAkB,EAAE,CAM1B,MALA,AAACF,CAAAA,EAAUR,aAAa,EAAI,EAAE,AAAD,EAAGW,OAAO,CAAC,AAACR,IACjCA,IAAiBM,GACjBC,EAAgBE,IAAI,CAACV,EAAYC,GAEzC,GACOO,CACX,CAMA,SAASG,IACL,IAAML,EAAY,IAAI,CAAEM,EAAcN,EAAUM,WAAW,CAAEf,EAAgBS,EAAUT,aAAa,CAAEE,EAAiBO,EAAUP,cAAc,CAAEc,EAAiBP,EAAUQ,MAAM,CAAEC,EAAkBT,EAAUU,OAAO,CAAEC,EAAeX,EAAUY,KAAK,CAAEC,EAAe,CACpQH,QAAS,CACLI,QAASL,EAAgBK,OAAO,AACpC,CACJ,EAEAC,EAAiB,EAAE,CAAEC,EAAsBjB,EAAwBC,EAAWM,GAC1EW,EAAeV,EAAeW,MAAM,CAAEC,EAiB1C,GAfAH,EAAoBb,OAAO,CAAC,CAACiB,EAAUC,KAGnC,IADAN,CAAc,CAACM,EAAM,CAAG,EAAE,CACnBJ,KACHE,EAAQZ,CAAc,CAACU,EAAa,CACpCF,CAAc,CAACM,EAAM,CAACjB,IAAI,CAAC,CACvBkB,EAAGH,EAAMG,CAAC,CACVC,MAAOJ,EAAMI,KAAK,CAClBC,MAAOL,CAAK,CAACC,EAAS,CACtBK,OAAQ,CAACrC,EAAQ+B,CAAK,CAACC,EAAS,CACpC,GAEJH,EAAeV,EAAeW,MAAM,AACxC,GAEIlB,EAAU0B,WAAW,CAACC,SAAS,EAAIlC,EAAeyB,MAAM,CAAE,CAC1D,IAA2EU,EAAmBb,CAAc,CAA9FC,EAAoBa,OAAO,CAACnC,EAAYD,CAAc,CAAC,EAAE,GAA4C,CAAEqC,EAAkBrC,AAA0B,IAA1BA,EAAeyB,MAAM,CACxJX,EACAQ,CAAc,CAACC,EAAoBa,OAAO,CAACnC,EAAYD,CAAc,CAAC,EAAE,GAAG,CAAEsC,EAAgB/B,EAAUgC,KAAK,AAChHhC,CAAAA,EAAUQ,MAAM,CAAGsB,EACnB9B,EAAUiC,UAAU,CAAGL,EACvB5B,EAAUgC,KAAK,CAAGhC,EAAU0B,WAAW,CAACC,SAAS,CACjD3B,EAAUU,OAAO,CAAGpB,EAAMiB,EAAgBM,GAC1Cb,EAAUY,KAAK,CAAGZ,EAAUkC,IAAI,CAChClC,EAAUmC,SAAS,CAAG,CAAA,EACtBjD,EAASkD,SAAS,CAAC1D,IAAI,CAACsB,GACxBA,EAAUkC,IAAI,CAAGlC,EAAUY,KAAK,CAEhC,OAAOZ,EAAUiC,UAAU,CAC3B,OAAOjC,EAAUmC,SAAS,CAC1BnC,EAAUgC,KAAK,CAAGD,CACtB,CAEAxC,EAAcY,OAAO,CAAC,CAACkC,EAAUC,KACzBvB,CAAc,CAACuB,EAAE,EACjBtC,EAAUQ,MAAM,CAAGO,CAAc,CAACuB,EAAE,CAChC7B,CAAe,CAAC4B,EAAS,CACzBrC,EAAUU,OAAO,CAAGpB,EAAMmB,CAAe,CAAC4B,EAAS,CAACE,MAAM,CAAE1B,GAG5DxB,EAAM,uBAAyBgD,EAAzB,gGAIVrC,EAAUY,KAAK,CAAGZ,CAAS,CAAC,QAAUqC,EAAS,CAC/CnD,EAASkD,SAAS,CAAC1D,IAAI,CAACsB,GAExBA,CAAS,CAAC,QAAUqC,EAAS,CAAGrC,EAAUY,KAAK,EAG/CvB,EAAM,WAAagD,EAAb,4GAId,GAEArC,EAAUQ,MAAM,CAAGD,EACnBP,EAAUU,OAAO,CAAGD,EACpBT,EAAUY,KAAK,CAAGD,EAClBzB,EAASkD,SAAS,CAAC1D,IAAI,CAACsB,EAC5B,CAQA,SAASwC,EAAsBhC,CAAM,EACjC,IAAIiC,EAAUC,EAAO,EAAE,CAAEC,EAAiB,EAAE,CAG5C,GAFAnC,EAASA,GAAU,IAAI,CAACA,MAAM,CAE1B,IAAI,CAAC2B,SAAS,EAAI,IAAI,CAACF,UAAU,CAEjC,CAAA,GAAIQ,AADJA,CAAAA,EAAWvD,EAAS0D,YAAY,CAAClE,IAAI,CAAC,IAAI,CAAE,IAAI,CAACuD,UAAU,CAAA,GAC3CQ,EAASvB,MAAM,CAAE,CAC7BuB,CAAQ,CAAC,EAAE,CAAC,EAAE,CAAG,IACjBC,EAAOxD,EAAS0D,YAAY,CAAClE,IAAI,CAAC,IAAI,CAAE8B,GACxCmC,EAAiBF,EAAS3C,KAAK,CAAC,EAAG4C,EAAKxB,MAAM,EAE9C,IAAK,IAAIoB,EAAIK,EAAezB,MAAM,CAAG,EAAGoB,GAAK,EAAGA,IAC5CI,EAAKtC,IAAI,CAACuC,CAAc,CAACL,EAAE,CAEnC,CAAA,MAGAI,EAAOxD,EAAS0D,YAAY,CAACC,KAAK,CAAC,IAAI,CAAEC,WAE7C,OAAOJ,CACX,CAQA,SAASK,EAAiB5B,CAAK,EAC3B,IAAM6B,EAAY,EAAE,CAIpB,MAHA,AAAC,CAAA,IAAI,CAACxD,aAAa,EAAI,EAAE,AAAD,EAAGW,OAAO,CAAC,AAACR,IAChCqD,EAAU5C,IAAI,CAACe,CAAK,CAACxB,EAAa,CACtC,GACOqD,CACX,CAMA,SAASC,IACL,IAAMzD,EAAgB,IAAI,CAACA,aAAa,CACpC0D,EAAa,EAAE,CAAEC,EACrBD,EAAanD,EAAwB,IAAI,EACzCb,EAASkE,SAAS,CAACP,KAAK,CAAC,IAAI,CAAEC,WAC/B,IAAI,CAACtC,MAAM,CAACL,OAAO,CAAC,AAACgB,IACjB3B,EAAcW,OAAO,CAAC,CAACR,EAAc2C,KACjCa,EAAQhC,CAAK,CAACxB,EAAa,CAGvB,IAAI,CAAC0D,UAAU,EACfF,CAAAA,EAAQ,IAAI,CAACE,UAAU,CAACC,WAAW,CAACH,EAAK,EAEzCA,AAAU,OAAVA,GACAhC,CAAAA,CAAK,CAAC+B,CAAU,CAACZ,EAAE,CAAC,CAAG,IAAI,CAACiB,KAAK,CAACC,QAAQ,CAACL,EAAO,CAAA,EAAI,CAE9D,EACJ,EACJ,CA3KAhG,EAAyBsG,OAAO,CAhBhC,SAAiBC,CAAc,EAC3B,IAAMC,EAAQD,EAAelF,SAAS,CAatC,OAZAmF,EAAMpE,aAAa,CAAIoE,EAAMpE,aAAa,EACtCA,EAAcO,KAAK,GACvB6D,EAAMnE,aAAa,CAAImE,EAAMnE,aAAa,EACtCA,EAAcM,KAAK,GACvB6D,EAAMrD,WAAW,CAAIqD,EAAMrD,WAAW,EAtBtB,MAwBhBqD,EAAMlE,cAAc,CAAIkE,EAAMlE,cAAc,EACxCA,EAAeK,KAAK,GACxB6D,EAAMvB,SAAS,CAAG/B,EAClBsD,EAAMf,YAAY,CAAGJ,EACrBmB,EAAMC,OAAO,CAAGb,EAChBY,EAAMP,SAAS,CAAGH,EACXS,CACX,CA6KJ,EAAGvG,GAA6BA,CAAAA,EAA2B,CAAC,CAAA,GAM/B,IAAM0G,EAAuC1G,EAapE,CAAE8B,IAAK6E,CAAY,CAAE,CAAG,AAAC9E,IAA2IG,WAAW,CAE/K,CAAE4E,aAAAA,CAAY,CAAEC,OAAAA,CAAM,CAAE1E,MAAO2E,CAA8B,CAAE,CAAInF,GAezE,OAAMoF,UAAiCJ,EAMnCK,MAAO,CACHnF,IAA0IG,WAAW,CAACF,GAAG,CAACT,SAAS,CAAC2F,IAAI,CAACtB,KAAK,CAAC,IAAI,CAAEC,WAErL,IAAI,CAACpC,OAAO,CAAGuD,EAA+B,CAC1CG,QAAS,CACL7B,OAAQ,CACJ8B,UAAW,IAAI,CAACrC,KAAK,AACzB,CACJ,EACAsC,WAAY,CACR/B,OAAQ,CACJ8B,UAAW,IAAI,CAACrC,KAAK,AACzB,CACJ,CACJ,EAAG,IAAI,CAACtB,OAAO,CACnB,CACA6D,UAAUC,CAAM,CAAEC,CAAM,CAAE,CACtB,IAUIC,EAAIC,EAAIC,EAAIC,EAAMC,EAAUC,EAAUzC,EAVpC0C,EAASP,EAAOO,MAAM,CAAEC,EAAYR,EAAOQ,SAAS,CAAEC,EAAgBT,EAAOS,aAAa,CAAE7D,EAAQoD,EAAOpD,KAAK,CAAE8D,EAAOX,EAAOY,KAAK,CAAEC,EAAUF,EAAOA,EAAKjE,MAAM,CAAG,EAG5KoE,EAAK,EAAE,CAAEC,EAAYvG,IAA0IG,WAAW,CAACqG,GAAG,CAAChH,SAAS,CAAC+F,SAAS,CAACC,EAAQ,CACvMQ,OAAQA,EACR3D,MAAOA,CACX,GAAIoE,EAAYzG,IAA0IG,WAAW,CAACuG,GAAG,CAAClH,SAAS,CAAC+F,SAAS,CAACC,EAAQ,CAClMQ,OAAQC,CACZ,GAAIU,EAAQ,EAAE,CAAEP,EAAQ,EAAE,CAG1B,IAAIC,CAAAA,EAAUL,CAAK,GAGnB,IAAK1C,EAAI0C,EAAQ1C,GAAK+C,EAAS/C,IAC3BwC,EAAWS,EAAUK,MAAM,CAACtD,EAAI0C,EAAO,CACvCD,EAAWU,EAAUG,MAAM,CAACtD,EAAI2C,EAAU,CAC1CJ,EAAOC,CAAQ,CAAC,EAAE,CAClBH,EAAKZ,EAAae,CAAQ,CAAC,EAAE,CAAII,EAAgBH,CAAQ,CAAC,EAAE,EAC5DH,EAAKb,EAAae,CAAQ,CAAC,EAAE,CAAII,EAAgBH,CAAQ,CAAC,EAAE,EAC5DL,EAAKI,CAAQ,CAAC,EAAE,CAChBQ,EAAGlF,IAAI,CAAC,CAACyE,EAAMF,EAAID,EAAIE,EAAG,EAC1Be,EAAMvF,IAAI,CAACyE,GACXO,EAAMhF,IAAI,CAAC,CAACuE,EAAID,EAAIE,EAAG,EAE3B,MAAO,CACHgB,OAAQN,EACRK,MAAOA,EACPP,MAAOA,CACX,EACJ,CACJ,CAyBAlB,EAAyB2B,cAAc,CAAG5B,EAA+BH,EAAa+B,cAAc,CAAE,CAYlGpB,OAAQ,CAMJpD,MAAO,EACP2D,OAAQ,GAIRC,UAAW,GAIXC,cAAe,CACnB,EAKAZ,WAAY,CAKR/B,OAAQ,CAIJuD,UAAW,EAKXzB,UAAW,KAAK,CACpB,CACJ,EAMAD,QAAS,CACL7B,OAAQ,CACJuD,UAAW,EACXzB,UAAW,KAAK,CACpB,CACJ,EACA0B,QAAS,CACLC,YAAa,+LACjB,EACAC,OAAQ,CACJC,QAAS,CAAA,CACb,EACAC,aAAc,CACVC,cAAe,UACnB,EACAN,UAAW,CACf,GACA9B,EAAOE,EAAyB1F,SAAS,CAAE,CACvC6H,SAAU,mBACV5G,eAAgB,CAAC,MAAO,SAAS,CACjC6G,eAAgB,CAAC,SAAU,YAAa,gBAAgB,CACxD/G,cAAe,CAAC,UAAW,aAAa,CACxCC,cAAe,CAAC,MAAO,SAAU,SAAS,CAC1Cc,YAAa,QACjB,GACAuD,EAAoCJ,OAAO,CAACS,GAC5ClF,IAA0IuH,kBAAkB,CAAC,kBAAmBrC,GAkCnJ,IAAMtF,EAAyBE,IAGlD,OADYH,EAAoB,OAAU,AAE3C,CAAA"}