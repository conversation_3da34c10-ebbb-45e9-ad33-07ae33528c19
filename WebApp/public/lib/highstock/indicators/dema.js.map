{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/dema\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 <PERSON><PERSON><PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/dema\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/dema\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ dema_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/DEMA/DEMAIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { ema: EMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { correctFloat, isArray, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The DEMA series Type\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.dema\n *\n * @augments Highcharts.Series\n */\nclass DEMAIndicator extends EMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getEMA(yVal, prevEMA, SMA, index, i, xVal) {\n        return super.calculateEma(xVal || [], yVal, typeof i === 'undefined' ? 1 : i, this.EMApercent, prevEMA, typeof index === 'undefined' ? -1 : index, SMA);\n    }\n    getValues(series, params) {\n        const period = params.period, EMAvalues = [], doubledPeriod = 2 * period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, DEMA = [], xDataDema = [], yDataDema = [];\n        let accumulatePeriodPoints = 0, EMA = 0, \n        // EMA(EMA)\n        EMAlevel2, \n        // EMA of previous point\n        prevEMA, prevEMAlevel2, \n        // EMA values array\n        i, index = -1, DEMAPoint, SMA = 0;\n        this.EMApercent = (2 / (period + 1));\n        // Check period, if bigger than EMA points length, skip\n        if (yValLen < 2 * period - 1) {\n            return;\n        }\n        // Switch index for OHLC / Candlestick / Arearange\n        if (isArray(yVal[0])) {\n            index = params.index ? params.index : 0;\n        }\n        // Accumulate first N-points\n        accumulatePeriodPoints =\n            super.accumulatePeriodPoints(period, index, yVal);\n        // First point\n        SMA = accumulatePeriodPoints / period;\n        accumulatePeriodPoints = 0;\n        // Calculate value one-by-one for each period in visible data\n        for (i = period; i < yValLen + 2; i++) {\n            if (i < yValLen + 1) {\n                EMA = this.getEMA(yVal, prevEMA, SMA, index, i)[1];\n                EMAvalues.push(EMA);\n            }\n            prevEMA = EMA;\n            // Summing first period points for EMA(EMA)\n            if (i < doubledPeriod) {\n                accumulatePeriodPoints += EMA;\n            }\n            else {\n                // Calculate DEMA\n                // First DEMA point\n                if (i === doubledPeriod) {\n                    SMA = accumulatePeriodPoints / period;\n                }\n                EMA = EMAvalues[i - period - 1];\n                EMAlevel2 = this.getEMA([EMA], prevEMAlevel2, SMA)[1];\n                DEMAPoint = [\n                    xVal[i - 2],\n                    correctFloat(2 * EMA - EMAlevel2)\n                ];\n                DEMA.push(DEMAPoint);\n                xDataDema.push(DEMAPoint[0]);\n                yDataDema.push(DEMAPoint[1]);\n                prevEMAlevel2 = EMAlevel2;\n            }\n        }\n        return {\n            values: DEMA,\n            xData: xDataDema,\n            yData: yDataDema\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Double exponential moving average (DEMA) indicator. This series requires\n * `linkedTo` option to be set and should be loaded after the\n * `stock/indicators/indicators.js`.\n *\n * @sample {highstock} stock/indicators/dema\n *         DEMA indicator\n *\n * @extends      plotOptions.ema\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, compare, compareBase, joinBy, keys,\n *               navigatorOptions, pointInterval, pointIntervalUnit,\n *               pointPlacement, pointRange, pointStart, showInNavigator,\n *               stacking\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/dema\n * @optionparent plotOptions.dema\n */\nDEMAIndicator.defaultOptions = merge(EMAIndicator.defaultOptions);\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('dema', DEMAIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const DEMA_DEMAIndicator = ((/* unused pure expression or super */ null && (DEMAIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `DEMA` series. If the [type](#series.dema.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.dema\n * @since     7.0.0\n * @product   highstock\n * @excluding allAreas, colorAxis, compare, compareBase, dataParser, dataURL,\n *            joinBy, keys, navigatorOptions, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointRange, pointStart, showInNavigator, stacking\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/dema\n * @apioption series.dema\n */\n''; // Adds doclet above to the transpiled file\n\n;// ./code/es-modules/masters/indicators/dema.js\n\n\n\n\n/* harmony default export */ const dema_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "dema_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "ema", "EMAIndicator", "seriesTypes", "correctFloat", "isArray", "merge", "DEMAIndicator", "getEMA", "yVal", "prevEMA", "SMA", "index", "i", "xVal", "calculateEma", "EMApercent", "getV<PERSON>ues", "series", "params", "period", "EMAvalues", "doubledPeriod", "xData", "yData", "yValLen", "length", "DEMA", "xDataDema", "yDataDema", "accumulatePeriodPoints", "EMA", "EMAlevel2", "prevEMAlevel2", "DEMAPoint", "push", "values", "defaultOptions", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,6BAA8B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACtH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,6BAA6B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAE1GA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAWjL,GAAM,CAAEE,IAAKC,CAAY,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAE/K,CAAEC,aAAAA,CAAY,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAE,CAAIR,GAe1C,OAAMS,UAAsBL,EAMxBM,OAAOC,CAAI,CAAEC,CAAO,CAAEC,CAAG,CAAEC,CAAK,CAAEC,CAAC,CAAEC,CAAI,CAAE,CACvC,OAAO,KAAK,CAACC,aAAaD,GAAQ,EAAE,CAAEL,EAAM,AAAa,KAAA,IAANI,EAAoB,EAAIA,EAAG,IAAI,CAACG,UAAU,CAAEN,EAAS,AAAiB,KAAA,IAAVE,EAAwB,GAAKA,EAAOD,EACvJ,CACAM,UAAUC,CAAM,CAAEC,CAAM,CAAE,CACtB,IAAMC,EAASD,EAAOC,MAAM,CAAEC,EAAY,EAAE,CAAEC,EAAgB,EAAIF,EAAQN,EAAOI,EAAOK,KAAK,CAAEd,EAAOS,EAAOM,KAAK,CAAEC,EAAUhB,EAAOA,EAAKiB,MAAM,CAAG,EAAGC,EAAO,EAAE,CAAEC,EAAY,EAAE,CAAEC,EAAY,EAAE,CAC3LC,EAAyB,EAAGC,EAAM,EAEtCC,EAEAtB,EAASuB,EAETpB,EAAGD,EAAQ,GAAIsB,EAAWvB,EAAM,EAGhC,GAFA,IAAI,CAACK,UAAU,CAAI,EAAKI,CAAAA,EAAS,CAAA,GAE7BK,CAAAA,EAAU,EAAIL,EAAS,CAAA,GAc3B,IAVIf,EAAQI,CAAI,CAAC,EAAE,GACfG,CAAAA,EAAQO,EAAOP,KAAK,CAAGO,EAAOP,KAAK,CAAG,CAAA,EAM1CD,EAAMmB,AAHNA,CAAAA,EACI,KAAK,CAACA,uBAAuBV,EAAQR,EAAOH,EAAI,EAErBW,EAC/BU,EAAyB,EAEpBjB,EAAIO,EAAQP,EAAIY,EAAU,EAAGZ,IAC1BA,EAAIY,EAAU,IACdM,EAAM,IAAI,CAACvB,MAAM,CAACC,EAAMC,EAASC,EAAKC,EAAOC,EAAE,CAAC,EAAE,CAClDQ,EAAUc,IAAI,CAACJ,IAEnBrB,EAAUqB,EAENlB,EAAIS,EACJQ,GAA0BC,GAKtBlB,IAAMS,GACNX,CAAAA,EAAMmB,EAAyBV,CAAK,EAExCW,EAAMV,CAAS,CAACR,EAAIO,EAAS,EAAE,CAC/BY,EAAY,IAAI,CAACxB,MAAM,CAAC,CAACuB,EAAI,CAAEE,EAAetB,EAAI,CAAC,EAAE,CACrDuB,EAAY,CACRpB,CAAI,CAACD,EAAI,EAAE,CACXT,EAAa,EAAI2B,EAAMC,GAC1B,CACDL,EAAKQ,IAAI,CAACD,GACVN,EAAUO,IAAI,CAACD,CAAS,CAAC,EAAE,EAC3BL,EAAUM,IAAI,CAACD,CAAS,CAAC,EAAE,EAC3BD,EAAgBD,GAGxB,MAAO,CACHI,OAAQT,EACRJ,MAAOK,EACPJ,MAAOK,CACX,EACJ,CACJ,CAyBAtB,EAAc8B,cAAc,CAAG/B,EAAMJ,EAAamC,cAAc,EAChErC,IAA0IsC,kBAAkB,CAAC,OAAQ/B,GAiCxI,IAAMX,EAAaE,IAGtC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}