{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/mfi\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Money Flow Index indicator for Highcharts Stock\n *\n * (c) 2010-2025 <PERSON><PERSON><PERSON><PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/mfi\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/mfi\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ mfi_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/MFI/MFIIndicator.js\n/* *\n *\n *  Money Flow Index indicator for Highcharts Stock\n *\n *  (c) 2010-2025 Grzegorz Blachliński\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { extend, merge, error, isArray } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n// Utils:\n/**\n *\n */\nfunction sumArray(array) {\n    return array.reduce(function (prev, cur) {\n        return prev + cur;\n    });\n}\n/**\n *\n */\nfunction toFixed(a, n) {\n    return parseFloat(a.toFixed(n));\n}\n/**\n *\n */\nfunction calculateTypicalPrice(point) {\n    return (point[1] + point[2] + point[3]) / 3;\n}\n/**\n *\n */\nfunction calculateRawMoneyFlow(typicalPrice, volume) {\n    return typicalPrice * volume;\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The MFI series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.mfi\n *\n * @augments Highcharts.Series\n */\nclass MFIIndicator extends SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, decimals = params.decimals, volumeSeries = series.chart.get(params.volumeSeriesID), yValVolume = volumeSeries?.getColumn('y') || [], MFI = [], xData = [], yData = [], positiveMoneyFlow = [], negativeMoneyFlow = [];\n        let newTypicalPrice, oldTypicalPrice, rawMoneyFlow, negativeMoneyFlowSum, positiveMoneyFlowSum, moneyFlowRatio, MFIPoint, i, isUp = false, \n        // MFI starts calculations from the second point\n        // Cause we need to calculate change between two points\n        range = 1;\n        if (!volumeSeries) {\n            error('Series ' +\n                params.volumeSeriesID +\n                ' not found! Check `volumeSeriesID`.', true, series.chart);\n            return;\n        }\n        // MFI requires high low and close values\n        if ((xVal.length <= period) || !isArray(yVal[0]) ||\n            yVal[0].length !== 4 ||\n            !yValVolume) {\n            return;\n        }\n        // Calculate first typical price\n        newTypicalPrice = calculateTypicalPrice(yVal[range]);\n        // Accumulate first N-points\n        while (range < period + 1) {\n            // Calculate if up or down\n            oldTypicalPrice = newTypicalPrice;\n            newTypicalPrice = calculateTypicalPrice(yVal[range]);\n            isUp = newTypicalPrice >= oldTypicalPrice;\n            // Calculate raw money flow\n            rawMoneyFlow = calculateRawMoneyFlow(newTypicalPrice, yValVolume[range]);\n            // Add to array\n            positiveMoneyFlow.push(isUp ? rawMoneyFlow : 0);\n            negativeMoneyFlow.push(isUp ? 0 : rawMoneyFlow);\n            range++;\n        }\n        for (i = range - 1; i < yValLen; i++) {\n            if (i > range - 1) {\n                // Remove first point from array\n                positiveMoneyFlow.shift();\n                negativeMoneyFlow.shift();\n                // Calculate if up or down\n                oldTypicalPrice = newTypicalPrice;\n                newTypicalPrice = calculateTypicalPrice(yVal[i]);\n                isUp = newTypicalPrice > oldTypicalPrice;\n                // Calculate raw money flow\n                rawMoneyFlow = calculateRawMoneyFlow(newTypicalPrice, yValVolume[i]);\n                // Add to array\n                positiveMoneyFlow.push(isUp ? rawMoneyFlow : 0);\n                negativeMoneyFlow.push(isUp ? 0 : rawMoneyFlow);\n            }\n            // Calculate sum of negative and positive money flow:\n            negativeMoneyFlowSum = sumArray(negativeMoneyFlow);\n            positiveMoneyFlowSum = sumArray(positiveMoneyFlow);\n            moneyFlowRatio = positiveMoneyFlowSum / negativeMoneyFlowSum;\n            MFIPoint = toFixed(100 - (100 / (1 + moneyFlowRatio)), decimals);\n            MFI.push([xVal[i], MFIPoint]);\n            xData.push(xVal[i]);\n            yData.push(MFIPoint);\n        }\n        return {\n            values: MFI,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Money Flow Index. This series requires `linkedTo` option to be set and\n * should be loaded after the `stock/indicators/indicators.js` file.\n *\n * @sample stock/indicators/mfi\n *         Money Flow Index Indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/mfi\n * @optionparent plotOptions.mfi\n */\nMFIIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    /**\n     * @excluding index\n     */\n    params: {\n        index: void 0, // Unchangeable index, do not inherit (#15362)\n        /**\n         * The id of volume series which is mandatory.\n         * For example using OHLC data, volumeSeriesID='volume' means\n         * the indicator will be calculated using OHLC and volume values.\n         */\n        volumeSeriesID: 'volume',\n        /**\n         * Number of maximum decimals that are used in MFI calculations.\n         */\n        decimals: 4\n    }\n});\nextend(MFIIndicator.prototype, {\n    nameBase: 'Money Flow Index'\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('mfi', MFIIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const MFI_MFIIndicator = ((/* unused pure expression or super */ null && (MFIIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `MFI` series. If the [type](#series.mfi.type) option is not specified, it\n * is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.mfi\n * @since     6.0.0\n * @excluding dataParser, dataURL\n * @product   highstock\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/mfi\n * @apioption series.mfi\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/masters/indicators/mfi.js\n\n\n\n\n/* harmony default export */ const mfi_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "mfi_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "sma", "SMAIndicator", "seriesTypes", "extend", "merge", "error", "isArray", "sumArray", "array", "reduce", "prev", "cur", "calculateTypicalPrice", "point", "MFIIndicator", "getV<PERSON>ues", "series", "params", "period", "xVal", "xData", "yVal", "yData", "yValLen", "length", "decimals", "volumeSeries", "chart", "volumeSeriesID", "yValVolume", "getColumn", "MFI", "positiveMoneyFlow", "negativeMoneyFlow", "newTypicalPrice", "oldTypicalPrice", "rawMoneyFlow", "negativeMoneyFlowSum", "MFIPoint", "i", "isUp", "range", "typicalPrice", "push", "shift", "positiveMoneyFlowSum", "parseFloat", "toFixed", "values", "defaultOptions", "index", "nameBase", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,4BAA6B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACrH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,4BAA4B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAEzGA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAejL,GAAM,CAAEE,IAAKC,CAAY,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAE/K,CAAEC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAEC,QAAAA,CAAO,CAAE,CAAIT,IAU3C,SAASU,EAASC,CAAK,EACnB,OAAOA,EAAMC,MAAM,CAAC,SAAUC,CAAI,CAAEC,CAAG,EACnC,OAAOD,EAAOC,CAClB,EACJ,CAUA,SAASC,EAAsBC,CAAK,EAChC,MAAO,AAACA,CAAAA,CAAK,CAAC,EAAE,CAAGA,CAAK,CAAC,EAAE,CAAGA,CAAK,CAAC,EAAE,AAAD,EAAK,CAC9C,CAqBA,MAAMC,UAAqBb,EAMvBc,UAAUC,CAAM,CAAEC,CAAM,CAAE,CACtB,IAAMC,EAASD,EAAOC,MAAM,CAAEC,EAAOH,EAAOI,KAAK,CAAEC,EAAOL,EAAOM,KAAK,CAAEC,EAAUF,EAAOA,EAAKG,MAAM,CAAG,EAAGC,EAAWR,EAAOQ,QAAQ,CAAEC,EAAeV,EAAOW,KAAK,CAACvC,GAAG,CAAC6B,EAAOW,cAAc,EAAGC,EAAaH,GAAcI,UAAU,MAAQ,EAAE,CAAEC,EAAM,EAAE,CAAEX,EAAQ,EAAE,CAAEE,EAAQ,EAAE,CAAEU,EAAoB,EAAE,CAAEC,EAAoB,EAAE,CAC3TC,EAAiBC,EAAiBC,EAAcC,EAA4DC,EAAUC,EAAGC,EAAO,CAAA,EAGpIC,EAAQ,EACR,GAAI,CAACf,EAAc,YACfrB,EAAM,UACFY,EAAOW,cAAc,CACrB,sCAAuC,CAAA,EAAMZ,EAAOW,KAAK,EAIjE,GAAI,CAACR,CAAAA,EAAKK,MAAM,EAAIN,CAAK,GAAOZ,EAAQe,CAAI,CAAC,EAAE,GAC3CA,AAAmB,IAAnBA,CAAI,CAAC,EAAE,CAACG,MAAM,EACbK,GAML,IAFAK,EAAkBtB,EAAsBS,CAAI,CAACoB,EAAM,EAE5CA,EAAQvB,EAAS,GAEpBiB,EAAkBD,EAElBM,EAAON,AADPA,CAAAA,EAAkBtB,EAAsBS,CAAI,CAACoB,EAAM,CAAA,GACzBN,EAE1BC,EAjDDM,AAiDsCR,EAAiBL,CAAU,CAACY,EAAM,CAEvET,EAAkBW,IAAI,CAACH,EAAOJ,EAAe,GAC7CH,EAAkBU,IAAI,CAACH,EAAO,EAAIJ,GAClCK,IAEJ,IAAKF,EAAIE,EAAQ,EAAGF,EAAIhB,EAASgB,IAAK,KApE7B1D,CAqED0D,CAAAA,EAAIE,EAAQ,IAEZT,EAAkBY,KAAK,GACvBX,EAAkBW,KAAK,GAEvBT,EAAkBD,EAElBM,EAAON,AADPA,CAAAA,EAAkBtB,EAAsBS,CAAI,CAACkB,EAAE,CAAA,EACtBJ,EAEzBC,EAjELM,AAiE0CR,EAAiBL,CAAU,CAACU,EAAE,CAEnEP,EAAkBW,IAAI,CAACH,EAAOJ,EAAe,GAC7CH,EAAkBU,IAAI,CAACH,EAAO,EAAIJ,IAGtCC,EAAuB9B,EAAS0B,GApF3BpD,EAuFc,IAAO,IAAO,CAAA,EADhBgE,AADMtC,EAASyB,GACQK,CACU,EAAlDC,EAtFDQ,WAAWjE,EAAEkE,OAAO,CAsFoCtB,IACvDM,EAAIY,IAAI,CAAC,CAACxB,CAAI,CAACoB,EAAE,CAAED,EAAS,EAC5BlB,EAAMuB,IAAI,CAACxB,CAAI,CAACoB,EAAE,EAClBjB,EAAMqB,IAAI,CAACL,EACf,CACA,MAAO,CACHU,OAAQjB,EACRX,MAAOA,EACPE,MAAOA,CACX,EACJ,CACJ,CAoBAR,EAAamC,cAAc,CAAG7C,EAAMH,EAAagD,cAAc,CAAE,CAI7DhC,OAAQ,CACJiC,MAAO,KAAK,EAMZtB,eAAgB,SAIhBH,SAAU,CACd,CACJ,GACAtB,EAAOW,EAAavB,SAAS,CAAE,CAC3B4D,SAAU,kBACd,GACApD,IAA0IqD,kBAAkB,CAAC,MAAOtC,GA+BvI,IAAMnB,EAAYE,IAGrC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}