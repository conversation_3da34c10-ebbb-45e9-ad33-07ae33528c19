{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/ao\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Wojciech Chmiel\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/ao\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/ao\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ ao_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/AO/AOIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { noop } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { column: { prototype: columnProto }, sma: SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { extend, merge, correctFloat, isArray } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The AO series type\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.ao\n *\n * @augments Highcharts.Series\n */\nclass AOIndicator extends SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    drawGraph() {\n        const indicator = this, options = indicator.options, points = indicator.points, userColor = indicator.userOptions.color, positiveColor = options.greaterBarColor, negativeColor = options.lowerBarColor, firstPoint = points[0];\n        let i;\n        if (!userColor && firstPoint) {\n            firstPoint.color = positiveColor;\n            for (i = 1; i < points.length; i++) {\n                if (points[i].y > points[i - 1].y) {\n                    points[i].color = positiveColor;\n                }\n                else if (points[i].y < points[i - 1].y) {\n                    points[i].color = negativeColor;\n                }\n                else {\n                    points[i].color = points[i - 1].color;\n                }\n            }\n        }\n    }\n    getValues(series) {\n        const shortPeriod = 5, longPeriod = 34, xVal = series.xData || [], yVal = series.yData || [], yValLen = yVal.length, AO = [], // 0- date, 1- Awesome Oscillator\n        xData = [], yData = [], high = 1, low = 2;\n        let shortSMA, // Shorter Period SMA\n        longSMA, // Longer Period SMA\n        awesome, shortLastIndex, longLastIndex, price, i, j, longSum = 0, shortSum = 0;\n        if (xVal.length <= longPeriod ||\n            !isArray(yVal[0]) ||\n            yVal[0].length !== 4) {\n            return;\n        }\n        for (i = 0; i < longPeriod - 1; i++) {\n            price = (yVal[i][high] + yVal[i][low]) / 2;\n            if (i >= longPeriod - shortPeriod) {\n                shortSum = correctFloat(shortSum + price);\n            }\n            longSum = correctFloat(longSum + price);\n        }\n        for (j = longPeriod - 1; j < yValLen; j++) {\n            price = (yVal[j][high] + yVal[j][low]) / 2;\n            shortSum = correctFloat(shortSum + price);\n            longSum = correctFloat(longSum + price);\n            shortSMA = shortSum / shortPeriod;\n            longSMA = longSum / longPeriod;\n            awesome = correctFloat(shortSMA - longSMA);\n            AO.push([xVal[j], awesome]);\n            xData.push(xVal[j]);\n            yData.push(awesome);\n            shortLastIndex = j + 1 - shortPeriod;\n            longLastIndex = j + 1 - longPeriod;\n            shortSum = correctFloat(shortSum -\n                (yVal[shortLastIndex][high] +\n                    yVal[shortLastIndex][low]) / 2);\n            longSum = correctFloat(longSum -\n                (yVal[longLastIndex][high] +\n                    yVal[longLastIndex][low]) / 2);\n        }\n        return {\n            values: AO,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Awesome Oscillator. This series requires the `linkedTo` option to\n * be set and should be loaded after the `stock/indicators/indicators.js`\n *\n * @sample {highstock} stock/indicators/ao\n *         Awesome\n *\n * @extends      plotOptions.sma\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, joinBy, keys, navigatorOptions,\n *               params, pointInterval, pointIntervalUnit, pointPlacement,\n *               pointRange, pointStart, showInNavigator, stacking\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/ao\n * @optionparent plotOptions.ao\n */\nAOIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    params: {\n        // Index and period are unchangeable, do not inherit (#15362)\n        index: void 0,\n        period: void 0\n    },\n    /**\n     * Color of the Awesome oscillator series bar that is greater than the\n     * previous one. Note that if a `color` is defined, the `color`\n     * takes precedence and the `greaterBarColor` is ignored.\n     *\n     * @sample {highstock} stock/indicators/ao/\n     *         greaterBarColor\n     *\n     * @type  {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @since 7.0.0\n     */\n    greaterBarColor: \"#06b535\" /* Palette.positiveColor */,\n    /**\n     * Color of the Awesome oscillator series bar that is lower than the\n     * previous one. Note that if a `color` is defined, the `color`\n     * takes precedence and the `lowerBarColor` is ignored.\n     *\n     * @sample {highstock} stock/indicators/ao/\n     *         lowerBarColor\n     *\n     * @type  {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @since 7.0.0\n     */\n    lowerBarColor: \"#f21313\" /* Palette.negativeColor */,\n    threshold: 0,\n    groupPadding: 0.2,\n    pointPadding: 0.2,\n    crisp: false,\n    states: {\n        hover: {\n            halo: {\n                size: 0\n            }\n        }\n    }\n});\nextend(AOIndicator.prototype, {\n    nameBase: 'AO',\n    nameComponents: void 0,\n    // Columns support:\n    markerAttribs: noop,\n    getColumnMetrics: columnProto.getColumnMetrics,\n    crispCol: columnProto.crispCol,\n    translate: columnProto.translate,\n    drawPoints: columnProto.drawPoints\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('ao', AOIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const AO_AOIndicator = ((/* unused pure expression or super */ null && (AOIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * An `AO` series. If the [type](#series.ao.type)\n * option is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.ao\n * @since     7.0.0\n * @product   highstock\n * @excluding allAreas, colorAxis, dataParser, dataURL, joinBy, keys,\n *            navigatorOptions, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointRange, pointStart, showInNavigator, stacking\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/ao\n * @apioption series.ao\n */\n''; // For including the above in the doclets\n\n;// ./code/es-modules/masters/indicators/ao.js\n\n\n\n\n/* harmony default export */ const ao_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "ao_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "noop", "column", "columnProto", "sma", "SMAIndicator", "seriesTypes", "extend", "merge", "correctFloat", "isArray", "AOIndicator", "drawGraph", "i", "options", "indicator", "points", "userColor", "userOptions", "color", "positiveColor", "greaterBarColor", "negativeColor", "lowerBarColor", "firstPoint", "length", "y", "getV<PERSON>ues", "series", "xVal", "xData", "yVal", "yData", "yValLen", "AO", "awesome", "shortLastIndex", "longLastIndex", "price", "j", "longSum", "shortSum", "<PERSON>P<PERSON><PERSON>", "shortSMA", "push", "values", "defaultOptions", "params", "index", "period", "threshold", "groupPadding", "pointPadding", "crisp", "states", "hover", "halo", "size", "nameBase", "nameComponents", "markerAttribs", "getColumnMetrics", "crispCol", "translate", "drawPoints", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,2BAA4B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACpH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,2BAA2B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAExGA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAWjL,GAAM,CAAEE,KAAAA,CAAI,CAAE,CAAIH,IAEZ,CAAEI,OAAQ,CAAEV,UAAWW,CAAW,CAAE,CAAEC,IAAKC,CAAY,CAAE,CAAG,AAACL,IAA2IM,WAAW,CAEnN,CAAEC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAEC,aAAAA,CAAY,CAAEC,QAAAA,CAAO,CAAE,CAAIZ,GAelD,OAAMa,UAAoBN,EAMtBO,WAAY,CACR,IACIC,EADoBC,EAAUC,AAAhB,IAAI,CAAsBD,OAAO,CAAEE,EAASD,AAA5C,IAAI,CAAkDC,MAAM,CAAEC,EAAYF,AAA1E,IAAI,CAAgFG,WAAW,CAACC,KAAK,CAAEC,EAAgBN,EAAQO,eAAe,CAAEC,EAAgBR,EAAQS,aAAa,CAAEC,EAAaR,CAAM,CAAC,EAAE,CAE/N,GAAI,CAACC,GAAaO,EAEd,IAAKX,EAAI,EADTW,EAAWL,KAAK,CAAGC,EACPP,EAAIG,EAAOS,MAAM,CAAEZ,IACvBG,CAAM,CAACH,EAAE,CAACa,CAAC,CAAGV,CAAM,CAACH,EAAI,EAAE,CAACa,CAAC,CAC7BV,CAAM,CAACH,EAAE,CAACM,KAAK,CAAGC,EAEbJ,CAAM,CAACH,EAAE,CAACa,CAAC,CAAGV,CAAM,CAACH,EAAI,EAAE,CAACa,CAAC,CAClCV,CAAM,CAACH,EAAE,CAACM,KAAK,CAAGG,EAGlBN,CAAM,CAACH,EAAE,CAACM,KAAK,CAAGH,CAAM,CAACH,EAAI,EAAE,CAACM,KAAK,AAIrD,CACAQ,UAAUC,CAAM,CAAE,CACd,IAAwCC,EAAOD,EAAOE,KAAK,EAAI,EAAE,CAAEC,EAAOH,EAAOI,KAAK,EAAI,EAAE,CAAEC,EAAUF,EAAKN,MAAM,CAAES,EAAK,EAAE,CAC5HJ,EAAQ,EAAE,CAAEE,EAAQ,EAAE,CAGtBG,EAASC,EAAgBC,EAAeC,EAAOzB,EAAG0B,EAAGC,EAAU,EAAGC,EAAW,EAC7E,GAAIZ,CAAAA,CAAAA,EAAKJ,MAAM,EALqB,EAKR,GACvBf,EAAQqB,CAAI,CAAC,EAAE,GAChBA,AAAmB,IAAnBA,CAAI,CAAC,EAAE,CAACN,MAAM,EAGlB,IAAKZ,EAAI,EAAGA,EAAI6B,GAAgB7B,IAC5ByB,EAAQ,AAACP,CAAAA,CAAI,CAAClB,EAAE,CAVW,EAUL,CAAGkB,CAAI,CAAClB,EAAE,CAVI,EAUC,AAAD,EAAK,EACrCA,GAAK6B,IACLD,CAAAA,EAAWhC,EAAagC,EAAWH,EAAK,EAE5CE,EAAU/B,EAAa+B,EAAUF,GAErC,IAAKC,EAAIG,GAAgBH,EAAIN,EAASM,IAElCE,EAAWhC,EAAagC,EADxBH,CAAAA,EAAQ,AAACP,CAAAA,CAAI,CAACQ,EAAE,CAjBW,EAiBL,CAAGR,CAAI,CAACQ,EAAE,CAjBI,EAiBC,AAAD,EAAK,CAAA,GAEzCC,EAAU/B,EAAa+B,EAAUF,GAGjCH,EAAU1B,EAAakC,AAFZF,EArBK,EAsBND,EAtBsB,IAwBhCN,EAAGU,IAAI,CAAC,CAACf,CAAI,CAACU,EAAE,CAAEJ,EAAQ,EAC1BL,EAAMc,IAAI,CAACf,CAAI,CAACU,EAAE,EAClBP,EAAMY,IAAI,CAACT,GACXC,EAAiBG,EAAI,EA3BL,EA4BhBF,EAAgBE,EAAI,EA5BY,GA6BhCE,EAAWhC,EAAagC,EACpB,AAACV,CAAAA,CAAI,CAACK,EAAe,CA7BE,EA6BI,CACvBL,CAAI,CAACK,EAAe,CA9BQ,EA8BH,AAAD,EAAK,GACrCI,EAAU/B,EAAa+B,EACnB,AAACT,CAAAA,CAAI,CAACM,EAAc,CAhCG,EAgCG,CACtBN,CAAI,CAACM,EAAc,CAjCS,EAiCJ,AAAD,EAAK,GAExC,MAAO,CACHQ,OAAQX,EACRJ,MAAOA,EACPE,MAAOA,CACX,EACJ,CACJ,CAuBArB,EAAYmC,cAAc,CAAGtC,EAAMH,EAAayC,cAAc,CAAE,CAC5DC,OAAQ,CAEJC,MAAO,KAAK,EACZC,OAAQ,KAAK,CACjB,EAYA5B,gBAAiB,UAYjBE,cAAe,UACf2B,UAAW,EACXC,aAAc,GACdC,aAAc,GACdC,MAAO,CAAA,EACPC,OAAQ,CACJC,MAAO,CACHC,KAAM,CACFC,KAAM,CACV,CACJ,CACJ,CACJ,GACAlD,EAAOI,EAAYnB,SAAS,CAAE,CAC1BkE,SAAU,KACVC,eAAgB,KAAK,EAErBC,cAAe3D,EACf4D,iBAAkB1D,EAAY0D,gBAAgB,CAC9CC,SAAU3D,EAAY2D,QAAQ,CAC9BC,UAAW5D,EAAY4D,SAAS,CAChCC,WAAY7D,EAAY6D,UAAU,AACtC,GACAhE,IAA0IiE,kBAAkB,CAAC,KAAMtD,GAiCtI,IAAMf,EAAWE,IAGpC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}