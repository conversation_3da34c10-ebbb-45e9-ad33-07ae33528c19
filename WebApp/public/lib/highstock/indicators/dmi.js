!/**
 * Highstock JS v12.3.0 (2025-06-21)
 * @module highcharts/indicators/dmi
 * @requires highcharts
 * @requires highcharts/modules/stock
 *
 * Indicator series type for Highcharts Stock
 *
 * (c) 2010-2025 <PERSON><PERSON><PERSON>
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/indicators/dmi",["highcharts/highcharts"],function(t){return e(t,t.SeriesRegistry)}):"object"==typeof exports?exports["highcharts/indicators/dmi"]=e(t._Highcharts,t._Highcharts.SeriesRegistry):t.Highcharts=e(t.Highcharts,t.Highcharts.SeriesRegistry)}("undefined"==typeof window?this:window,(t,e)=>(()=>{"use strict";var a,s={512:t=>{t.exports=e},944:e=>{e.exports=t}},i={};function r(t){var e=i[t];if(void 0!==e)return e.exports;var a=i[t]={exports:{}};return s[t](a,a.exports,r),a.exports}r.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return r.d(e,{a:e}),e},r.d=(t,e)=>{for(var a in e)r.o(e,a)&&!r.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:e[a]})},r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var o={};r.d(o,{default:()=>I});var n=r(944),l=r.n(n),p=r(512),h=r.n(p);let{sma:{prototype:c}}=h().seriesTypes,{defined:u,error:f,merge:y}=l();!function(t){let e=["bottomLine"],a=["top","bottom"],s=["top"];function i(t){return"plot"+t.charAt(0).toUpperCase()+t.slice(1)}function r(t,e){let a=[];return(t.pointArrayMap||[]).forEach(t=>{t!==e&&a.push(i(t))}),a}function o(){let t=this,e=t.pointValKey,a=t.linesApiNames,s=t.areaLinesNames,o=t.points,n=t.options,l=t.graph,p={options:{gapSize:n.gapSize}},h=[],d=r(t,e),g=o.length,m;if(d.forEach((t,e)=>{for(h[e]=[];g--;)m=o[g],h[e].push({x:m.x,plotX:m.plotX,plotY:m[t],isNull:!u(m[t])});g=o.length}),t.userOptions.fillColor&&s.length){let e=h[d.indexOf(i(s[0]))],a=1===s.length?o:h[d.indexOf(i(s[1]))],r=t.color;t.points=a,t.nextPoints=e,t.color=t.userOptions.fillColor,t.options=y(o,p),t.graph=t.area,t.fillGraph=!0,c.drawGraph.call(t),t.area=t.graph,delete t.nextPoints,delete t.fillGraph,t.color=r}a.forEach((e,a)=>{h[a]?(t.points=h[a],n[e]?t.options=y(n[e].styles,p):f('Error: "There is no '+e+' in DOCS options declared. Check if linesApiNames are consistent with your DOCS line names."'),t.graph=t["graph"+e],c.drawGraph.call(t),t["graph"+e]=t.graph):f('Error: "'+e+" doesn't have equivalent in pointArrayMap. To many elements in linesApiNames relative to pointArrayMap.\"")}),t.points=o,t.options=n,t.graph=l,c.drawGraph.call(t)}function n(t){let e,a=[],s=[];if(t=t||this.points,this.fillGraph&&this.nextPoints){if((e=c.getGraphPath.call(this,this.nextPoints))&&e.length){e[0][0]="L",a=c.getGraphPath.call(this,t),s=e.slice(0,a.length);for(let t=s.length-1;t>=0;t--)a.push(s[t])}}else a=c.getGraphPath.apply(this,arguments);return a}function l(t){let e=[];return(this.pointArrayMap||[]).forEach(a=>{e.push(t[a])}),e}function p(){let t=this.pointArrayMap,e=[],a;e=r(this),c.translate.apply(this,arguments),this.points.forEach(s=>{t.forEach((t,i)=>{a=s[t],this.dataModify&&(a=this.dataModify.modifyValue(a)),null!==a&&(s[e[i]]=this.yAxis.toPixels(a,!0))})})}t.compose=function(t){let i=t.prototype;return i.linesApiNames=i.linesApiNames||e.slice(),i.pointArrayMap=i.pointArrayMap||a.slice(),i.pointValKey=i.pointValKey||"top",i.areaLinesNames=i.areaLinesNames||s.slice(),i.drawGraph=o,i.getGraphPath=n,i.toYData=l,i.translate=p,t}}(a||(a={}));let d=a,{sma:g}=h().seriesTypes,{correctFloat:m,extend:D,isArray:x,merge:M}=l();class b extends g{calculateDM(t,e,a){let s,i=t[e][1],r=t[e][2],o=t[e-1][1],n=t[e-1][2];return m(i-o>n-r?a?Math.max(i-o,0):0:a?0:Math.max(n-r,0))}calculateDI(t,e){return t/e*100}calculateDX(t,e){return m(Math.abs(t-e)/Math.abs(t+e)*100)}smoothValues(t,e,a){return m(t-t/a+e)}getTR(t,e){return m(Math.max(t[1]-t[2],e?Math.abs(t[1]-e[3]):0,e?Math.abs(t[2]-e[3]):0))}getValues(t,e){let a=e.period,s=t.xData,i=t.yData,r=i?i.length:0,o=[],n=[],l=[];if(s.length<=a||!x(i[0])||4!==i[0].length)return;let p=0,h=0,c=0,u;for(u=1;u<r;u++){let t,e,r,f,y,d,g,m,D;u<=a?(f=this.calculateDM(i,u,!0),y=this.calculateDM(i,u),d=this.getTR(i[u],i[u-1]),p+=f,h+=y,c+=d,u===a&&(g=this.calculateDI(p,c),m=this.calculateDI(h,c),D=this.calculateDX(p,h),o.push([s[u],D,g,m]),n.push(s[u]),l.push([D,g,m]))):(f=this.calculateDM(i,u,!0),y=this.calculateDM(i,u),d=this.getTR(i[u],i[u-1]),t=this.smoothValues(p,f,a),e=this.smoothValues(h,y,a),r=this.smoothValues(c,d,a),p=t,h=e,c=r,g=this.calculateDI(p,c),m=this.calculateDI(h,c),D=this.calculateDX(p,h),o.push([s[u],D,g,m]),n.push(s[u]),l.push([D,g,m]))}return{values:o,xData:n,yData:l}}}b.defaultOptions=M(g.defaultOptions,{params:{index:void 0},marker:{enabled:!1},tooltip:{pointFormat:'<span style="color: {point.color}">●</span><b> {series.name}</b><br/><span style="color: {point.color}">DX</span>: {point.y}<br/><span style="color: {point.series.options.plusDILine.styles.lineColor}">+DI</span>: {point.plusDI}<br/><span style="color: {point.series.options.minusDILine.styles.lineColor}">-DI</span>: {point.minusDI}<br/>'},plusDILine:{styles:{lineWidth:1,lineColor:"#06b535"}},minusDILine:{styles:{lineWidth:1,lineColor:"#f21313"}},dataGrouping:{approximation:"averages"}}),D(b.prototype,{areaLinesNames:[],nameBase:"DMI",linesApiNames:["plusDILine","minusDILine"],pointArrayMap:["y","plusDI","minusDI"],parallelArrays:["x","y","plusDI","minusDI"],pointValKey:"y"}),d.compose(b),h().registerSeriesType("dmi",b);let I=l();return o.default})());