{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/ichimoku-kinko-hyo\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Sebastian <PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"dataGrouping\"][\"approximations\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/ichimoku-kinko-hyo\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"dataGrouping\"],[\"approximations\"],amd1[\"Color\"],amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/ichimoku-kinko-hyo\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"dataGrouping\"][\"approximations\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"dataGrouping\"][\"approximations\"], root[\"Highcharts\"][\"Color\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__956__, __WEBPACK_EXTERNAL_MODULE__620__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 620:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__620__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ }),\n\n/***/ 956:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__956__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ ichimoku_kinko_hyo_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"dataGrouping\",\"approximations\"],\"commonjs\":[\"highcharts\",\"dataGrouping\",\"approximations\"],\"commonjs2\":[\"highcharts\",\"dataGrouping\",\"approximations\"],\"root\":[\"Highcharts\",\"dataGrouping\",\"approximations\"]}\nvar highcharts_dataGrouping_approximations_commonjs_highcharts_dataGrouping_approximations_commonjs2_highcharts_dataGrouping_approximations_root_Highcharts_dataGrouping_approximations_ = __webpack_require__(956);\nvar highcharts_dataGrouping_approximations_commonjs_highcharts_dataGrouping_approximations_commonjs2_highcharts_dataGrouping_approximations_root_Highcharts_dataGrouping_approximations_default = /*#__PURE__*/__webpack_require__.n(highcharts_dataGrouping_approximations_commonjs_highcharts_dataGrouping_approximations_commonjs2_highcharts_dataGrouping_approximations_root_Highcharts_dataGrouping_approximations_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Color\"],\"commonjs\":[\"highcharts\",\"Color\"],\"commonjs2\":[\"highcharts\",\"Color\"],\"root\":[\"Highcharts\",\"Color\"]}\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_ = __webpack_require__(620);\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default = /*#__PURE__*/__webpack_require__.n(highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/IKH/IKHIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { parse: color } = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default());\n\nconst { sma: SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { defined, extend, isArray, isNumber, getClosestDistance, merge, objectEach } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction maxHigh(arr) {\n    return arr.reduce(function (max, res) {\n        return Math.max(max, res[1]);\n    }, -Infinity);\n}\n/**\n * @private\n */\nfunction minLow(arr) {\n    return arr.reduce(function (min, res) {\n        return Math.min(min, res[2]);\n    }, Infinity);\n}\n/**\n * @private\n */\nfunction highlowLevel(arr) {\n    return {\n        high: maxHigh(arr),\n        low: minLow(arr)\n    };\n}\n/**\n * Check two lines intersection (line a1-a2 and b1-b2)\n * Source: https://en.wikipedia.org/wiki/Line%E2%80%93line_intersection\n * @private\n */\nfunction checkLineIntersection(a1, a2, b1, b2) {\n    if (a1 && a2 && b1 && b2) {\n        const saX = a2.plotX - a1.plotX, // Auxiliary section a2-a1 X\n        saY = a2.plotY - a1.plotY, // Auxiliary section a2-a1 Y\n        sbX = b2.plotX - b1.plotX, // Auxiliary section b2-b1 X\n        sbY = b2.plotY - b1.plotY, // Auxiliary section b2-b1 Y\n        sabX = a1.plotX - b1.plotX, // Auxiliary section a1-b1 X\n        sabY = a1.plotY - b1.plotY, // Auxiliary section a1-b1 Y\n        // First degree Bézier parameters\n        u = (-saY * sabX + saX * sabY) / (-sbX * saY + saX * sbY), t = (sbX * sabY - sbY * sabX) / (-sbX * saY + saX * sbY);\n        if (u >= 0 && u <= 1 && t >= 0 && t <= 1) {\n            return {\n                plotX: a1.plotX + t * saX,\n                plotY: a1.plotY + t * saY\n            };\n        }\n    }\n}\n/**\n * Parameter opt (indicator options object) include indicator, points,\n * nextPoints, color, options, gappedExtend and graph properties\n * @private\n */\nfunction drawSenkouSpan(opt) {\n    const indicator = opt.indicator;\n    indicator.points = opt.points;\n    indicator.nextPoints = opt.nextPoints;\n    indicator.color = opt.color;\n    indicator.options = merge(opt.options.senkouSpan.styles, opt.gap);\n    indicator.graph = opt.graph;\n    indicator.fillGraph = true;\n    highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().seriesTypes.sma.prototype.drawGraph.call(indicator);\n}\n/**\n * Data integrity in Ichimoku is different than default 'averages':\n * Point: [undefined, value, value, ...] is correct\n * Point: [undefined, undefined, undefined, ...] is incorrect\n * @private\n */\nfunction ichimokuAverages() {\n    const ret = [];\n    let isEmptyRange;\n    [].forEach.call(arguments, function (arr, i) {\n        ret.push(highcharts_dataGrouping_approximations_commonjs_highcharts_dataGrouping_approximations_commonjs2_highcharts_dataGrouping_approximations_root_Highcharts_dataGrouping_approximations_default().average(arr));\n        isEmptyRange = !isEmptyRange && typeof ret[i] === 'undefined';\n    });\n    // Return undefined when first elem. is undefined and let\n    // sum method handle null (#7377)\n    return isEmptyRange ? void 0 : ret;\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The IKH series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.ikh\n *\n * @augments Highcharts.Series\n */\nclass IKHIndicator extends SMAIndicator {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.data = [];\n        this.options = {};\n        this.points = [];\n        this.graphCollection = [];\n    }\n    /* *\n     *\n     * Functions\n     *\n     * */\n    init() {\n        super.init.apply(this, arguments);\n        // Set default color for lines:\n        this.options = merge({\n            tenkanLine: {\n                styles: {\n                    lineColor: this.color\n                }\n            },\n            kijunLine: {\n                styles: {\n                    lineColor: this.color\n                }\n            },\n            chikouLine: {\n                styles: {\n                    lineColor: this.color\n                }\n            },\n            senkouSpanA: {\n                styles: {\n                    lineColor: this.color,\n                    fill: color(this.color).setOpacity(0.5).get()\n                }\n            },\n            senkouSpanB: {\n                styles: {\n                    lineColor: this.color,\n                    fill: color(this.color).setOpacity(0.5).get()\n                }\n            },\n            senkouSpan: {\n                styles: {\n                    fill: color(this.color).setOpacity(0.2).get()\n                }\n            }\n        }, this.options);\n    }\n    toYData(point) {\n        return [\n            point.tenkanSen,\n            point.kijunSen,\n            point.chikouSpan,\n            point.senkouSpanA,\n            point.senkouSpanB\n        ];\n    }\n    translate() {\n        const indicator = this;\n        highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().seriesTypes.sma.prototype.translate.apply(indicator);\n        for (const point of indicator.points) {\n            for (const key of indicator.pointArrayMap) {\n                const pointValue = point[key];\n                if (isNumber(pointValue)) {\n                    point['plot' + key] = indicator.yAxis.toPixels(pointValue, true);\n                    // Add extra parameters for support tooltip in moved\n                    // lines\n                    point.plotY = point['plot' + key];\n                    point.tooltipPos = [\n                        point.plotX,\n                        point['plot' + key]\n                    ];\n                    point.isNull = false;\n                }\n            }\n        }\n    }\n    drawGraph() {\n        const indicator = this, mainLinePoints = indicator.points, mainLineOptions = indicator.options, mainLinePath = indicator.graph, mainColor = indicator.color, gappedExtend = {\n            options: {\n                gapSize: mainLineOptions.gapSize\n            }\n        }, pointArrayMapLength = indicator.pointArrayMap.length, allIchimokuPoints = [\n            [],\n            [],\n            [],\n            [],\n            [],\n            []\n        ], ikhMap = {\n            tenkanLine: allIchimokuPoints[0],\n            kijunLine: allIchimokuPoints[1],\n            chikouLine: allIchimokuPoints[2],\n            senkouSpanA: allIchimokuPoints[3],\n            senkouSpanB: allIchimokuPoints[4],\n            senkouSpan: allIchimokuPoints[5]\n        }, intersectIndexColl = [], senkouSpanOptions = indicator\n            .options.senkouSpan, color = senkouSpanOptions.color ||\n            senkouSpanOptions.styles.fill, negativeColor = senkouSpanOptions.negativeColor, \n        // Points to create color and negativeColor senkouSpan\n        points = [\n            [], // Points color\n            [] // Points negative color\n        ], \n        // For span, we need an access to the next points, used in\n        // getGraphPath()\n        nextPoints = [\n            [], // Next points color\n            [] // Next points negative color\n        ];\n        let pointsLength = mainLinePoints.length, lineIndex = 0, position, point, i, startIntersect, endIntersect, sectionPoints, sectionNextPoints, pointsPlotYSum, nextPointsPlotYSum, senkouSpanTempColor, concatArrIndex, j, k;\n        indicator.ikhMap = ikhMap;\n        // Generate points for all lines and spans lines:\n        while (pointsLength--) {\n            point = mainLinePoints[pointsLength];\n            for (i = 0; i < pointArrayMapLength; i++) {\n                position = indicator.pointArrayMap[i];\n                if (defined(point[position])) {\n                    allIchimokuPoints[i].push({\n                        plotX: point.plotX,\n                        plotY: point['plot' + position],\n                        isNull: false\n                    });\n                }\n            }\n            if (negativeColor && pointsLength !== mainLinePoints.length - 1) {\n                // Check if lines intersect\n                const index = ikhMap.senkouSpanB.length - 1, intersect = checkLineIntersection(ikhMap.senkouSpanA[index - 1], ikhMap.senkouSpanA[index], ikhMap.senkouSpanB[index - 1], ikhMap.senkouSpanB[index]);\n                if (intersect) {\n                    const intersectPointObj = {\n                        plotX: intersect.plotX,\n                        plotY: intersect.plotY,\n                        isNull: false,\n                        intersectPoint: true\n                    };\n                    // Add intersect point to ichimoku points collection\n                    // Create senkouSpan sections\n                    ikhMap.senkouSpanA.splice(index, 0, intersectPointObj);\n                    ikhMap.senkouSpanB.splice(index, 0, intersectPointObj);\n                    intersectIndexColl.push(index);\n                }\n            }\n        }\n        // Modify options and generate lines:\n        objectEach(ikhMap, (values, lineName) => {\n            if (mainLineOptions[lineName] &&\n                lineName !== 'senkouSpan') {\n                // First line is rendered by default option\n                indicator.points = allIchimokuPoints[lineIndex];\n                indicator.options = merge(mainLineOptions[lineName].styles, gappedExtend);\n                indicator.graph = indicator['graph' + lineName];\n                indicator.fillGraph = false;\n                indicator.color = mainColor;\n                highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().seriesTypes.sma.prototype.drawGraph.call(indicator);\n                // Now save line\n                indicator['graph' + lineName] = indicator.graph;\n            }\n            lineIndex++;\n        });\n        // Generate senkouSpan area:\n        // If graphCollection exist then remove svg\n        // element and indicator property\n        if (indicator.graphCollection) {\n            for (const graphName of indicator.graphCollection) {\n                indicator[graphName].destroy();\n                delete indicator[graphName];\n            }\n        }\n        // Clean graphCollection or initialize it\n        indicator.graphCollection = [];\n        // When user set negativeColor property\n        if (negativeColor && ikhMap.senkouSpanA[0] && ikhMap.senkouSpanB[0]) {\n            // Add first and last point to senkouSpan area sections\n            intersectIndexColl.unshift(0);\n            intersectIndexColl.push(ikhMap.senkouSpanA.length - 1);\n            // Populate points and nextPoints arrays\n            for (j = 0; j < intersectIndexColl.length - 1; j++) {\n                startIntersect = intersectIndexColl[j];\n                endIntersect = intersectIndexColl[j + 1];\n                sectionPoints = ikhMap.senkouSpanB.slice(startIntersect, endIntersect + 1);\n                sectionNextPoints = ikhMap.senkouSpanA.slice(startIntersect, endIntersect + 1);\n                // Add points to color or negativeColor arrays\n                // Check the middle point (if exist)\n                if (Math.floor(sectionPoints.length / 2) >= 1) {\n                    const x = Math.floor(sectionPoints.length / 2);\n                    // When middle points has equal values\n                    // Compare all points plotY value sum\n                    if (sectionPoints[x].plotY === sectionNextPoints[x].plotY) {\n                        pointsPlotYSum = 0;\n                        nextPointsPlotYSum = 0;\n                        for (k = 0; k < sectionPoints.length; k++) {\n                            pointsPlotYSum += sectionPoints[k].plotY;\n                            nextPointsPlotYSum += sectionNextPoints[k].plotY;\n                        }\n                        concatArrIndex =\n                            pointsPlotYSum > nextPointsPlotYSum ? 0 : 1;\n                        points[concatArrIndex] = points[concatArrIndex].concat(sectionPoints);\n                        nextPoints[concatArrIndex] = nextPoints[concatArrIndex].concat(sectionNextPoints);\n                    }\n                    else {\n                        // Compare middle point of the section\n                        concatArrIndex = (sectionPoints[x].plotY > sectionNextPoints[x].plotY) ? 0 : 1;\n                        points[concatArrIndex] = points[concatArrIndex].concat(sectionPoints);\n                        nextPoints[concatArrIndex] = nextPoints[concatArrIndex].concat(sectionNextPoints);\n                    }\n                }\n                else {\n                    // Compare first point of the section\n                    concatArrIndex = (sectionPoints[0].plotY > sectionNextPoints[0].plotY) ? 0 : 1;\n                    points[concatArrIndex] = points[concatArrIndex].concat(sectionPoints);\n                    nextPoints[concatArrIndex] = nextPoints[concatArrIndex].concat(sectionNextPoints);\n                }\n            }\n            // Render color and negativeColor paths\n            ['graphsenkouSpanColor', 'graphsenkouSpanNegativeColor'].forEach(function (areaName, i) {\n                if (points[i].length && nextPoints[i].length) {\n                    senkouSpanTempColor = i === 0 ? color : negativeColor;\n                    drawSenkouSpan({\n                        indicator: indicator,\n                        points: points[i],\n                        nextPoints: nextPoints[i],\n                        color: senkouSpanTempColor,\n                        options: mainLineOptions,\n                        gap: gappedExtend,\n                        graph: indicator[areaName]\n                    });\n                    // Now save line\n                    indicator[areaName] = indicator.graph;\n                    indicator.graphCollection.push(areaName);\n                }\n            });\n        }\n        else {\n            // When user set only senkouSpan style.fill property\n            drawSenkouSpan({\n                indicator: indicator,\n                points: ikhMap.senkouSpanB,\n                nextPoints: ikhMap.senkouSpanA,\n                color: color,\n                options: mainLineOptions,\n                gap: gappedExtend,\n                graph: indicator.graphsenkouSpan\n            });\n            // Now save line\n            indicator.graphsenkouSpan = indicator.graph;\n        }\n        // Clean temporary properties:\n        delete indicator.nextPoints;\n        delete indicator.fillGraph;\n        // Restore options and draw the Tenkan line:\n        indicator.points = mainLinePoints;\n        indicator.options = mainLineOptions;\n        indicator.graph = mainLinePath;\n        indicator.color = mainColor;\n    }\n    getGraphPath(points) {\n        const indicator = this;\n        let path = [], spanA, spanAarr = [];\n        points = points || this.points;\n        // Render Senkou Span\n        if (indicator.fillGraph && indicator.nextPoints) {\n            spanA = highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().seriesTypes.sma.prototype.getGraphPath.call(indicator, \n            // Reverse points, so Senkou Span A will start from the end:\n            indicator.nextPoints);\n            if (spanA && spanA.length) {\n                spanA[0][0] = 'L';\n                path = highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().seriesTypes.sma.prototype.getGraphPath\n                    .call(indicator, points);\n                spanAarr = spanA.slice(0, path.length);\n                for (let i = spanAarr.length - 1; i >= 0; i--) {\n                    path.push(spanAarr[i]);\n                }\n            }\n        }\n        else {\n            path = highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().seriesTypes.sma.prototype.getGraphPath\n                .apply(indicator, arguments);\n        }\n        return path;\n    }\n    getValues(series, params) {\n        const period = params.period, periodTenkan = params.periodTenkan, periodSenkouSpanB = params.periodSenkouSpanB, xVal = series.xData, yVal = series.yData, xAxis = series.xAxis, yValLen = (yVal && yVal.length) || 0, closestPointRange = getClosestDistance(xAxis.series.map((s) => s.getColumn('x'))), IKH = [], xData = [];\n        let date, slicedTSY, slicedKSY, slicedSSBY, pointTS, pointKS, pointSSB, i, TS, KS, CS, SSA, SSB;\n        // Ikh requires close value\n        if (xVal.length <= period ||\n            !isArray(yVal[0]) ||\n            yVal[0].length !== 4) {\n            return;\n        }\n        // Add timestamps at the beginning\n        const dateStart = xVal[0] - period * closestPointRange;\n        for (i = 0; i < period; i++) {\n            xData.push(dateStart + i * closestPointRange);\n        }\n        for (i = 0; i < yValLen; i++) {\n            // Tenkan Sen\n            if (i >= periodTenkan) {\n                slicedTSY = yVal.slice(i - periodTenkan, i);\n                pointTS = highlowLevel(slicedTSY);\n                TS = (pointTS.high + pointTS.low) / 2;\n            }\n            if (i >= period) {\n                slicedKSY = yVal.slice(i - period, i);\n                pointKS = highlowLevel(slicedKSY);\n                KS = (pointKS.high + pointKS.low) / 2;\n                SSA = (TS + KS) / 2;\n            }\n            if (i >= periodSenkouSpanB) {\n                slicedSSBY = yVal.slice(i - periodSenkouSpanB, i);\n                pointSSB = highlowLevel(slicedSSBY);\n                SSB = (pointSSB.high + pointSSB.low) / 2;\n            }\n            CS = yVal[i][3];\n            date = xVal[i];\n            if (typeof IKH[i] === 'undefined') {\n                IKH[i] = [];\n            }\n            if (typeof IKH[i + period - 1] === 'undefined') {\n                IKH[i + period - 1] = [];\n            }\n            IKH[i + period - 1][0] = TS;\n            IKH[i + period - 1][1] = KS;\n            IKH[i + period - 1][2] = void 0;\n            if (typeof IKH[i + 1] === 'undefined') {\n                IKH[i + 1] = [];\n            }\n            IKH[i + 1][2] = CS;\n            if (i <= period) {\n                IKH[i + period - 1][3] = void 0;\n                IKH[i + period - 1][4] = void 0;\n            }\n            if (typeof IKH[i + 2 * period - 2] === 'undefined') {\n                IKH[i + 2 * period - 2] = [];\n            }\n            IKH[i + 2 * period - 2][3] = SSA;\n            IKH[i + 2 * period - 2][4] = SSB;\n            xData.push(date);\n        }\n        // Add timestamps for further points\n        for (i = 1; i <= period; i++) {\n            xData.push(date + i * closestPointRange);\n        }\n        return {\n            values: IKH,\n            xData: xData,\n            yData: IKH\n        };\n    }\n}\n/**\n * Ichimoku Kinko Hyo (IKH). This series requires `linkedTo` option to be\n * set.\n *\n * @sample stock/indicators/ichimoku-kinko-hyo\n *         Ichimoku Kinko Hyo indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @excluding    allAreas, colorAxis, compare, compareBase, joinBy, keys,\n *               navigatorOptions, pointInterval, pointIntervalUnit,\n *               pointPlacement, pointRange, pointStart, showInNavigator,\n *               stacking\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/ichimoku-kinko-hyo\n * @optionparent plotOptions.ikh\n */\nIKHIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    /**\n     * @excluding index\n     */\n    params: {\n        index: void 0, // Unused index, do not inherit (#15362)\n        period: 26,\n        /**\n         * The base period for Tenkan calculations.\n         */\n        periodTenkan: 9,\n        /**\n         * The base period for Senkou Span B calculations\n         */\n        periodSenkouSpanB: 52\n    },\n    marker: {\n        enabled: false\n    },\n    tooltip: {\n        pointFormat: '<span style=\"color:{point.color}\">\\u25CF</span> <b> {series.name}</b><br/>' +\n            'TENKAN SEN: {point.tenkanSen:.3f}<br/>' +\n            'KIJUN SEN: {point.kijunSen:.3f}<br/>' +\n            'CHIKOU SPAN: {point.chikouSpan:.3f}<br/>' +\n            'SENKOU SPAN A: {point.senkouSpanA:.3f}<br/>' +\n            'SENKOU SPAN B: {point.senkouSpanB:.3f}<br/>'\n    },\n    /**\n     * The styles for Tenkan line\n     */\n    tenkanLine: {\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line.\n             *\n             * @type {Highcharts.ColorString}\n             */\n            lineColor: void 0\n        }\n    },\n    /**\n     * The styles for Kijun line\n     */\n    kijunLine: {\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line.\n             *\n             * @type {Highcharts.ColorString}\n             */\n            lineColor: void 0\n        }\n    },\n    /**\n     * The styles for Chikou line\n     */\n    chikouLine: {\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line.\n             *\n             * @type {Highcharts.ColorString}\n             */\n            lineColor: void 0\n        }\n    },\n    /**\n     * The styles for Senkou Span A line\n     */\n    senkouSpanA: {\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line.\n             *\n             * @type {Highcharts.ColorString}\n             */\n            lineColor: void 0\n        }\n    },\n    /**\n     * The styles for Senkou Span B line\n     */\n    senkouSpanB: {\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line.\n             *\n             * @type {Highcharts.ColorString}\n             */\n            lineColor: void 0\n        }\n    },\n    /**\n     * The styles for area between Senkou Span A and B.\n     */\n    senkouSpan: {\n        /**\n         * Color of the area between Senkou Span A and B,\n         * when Senkou Span A is above Senkou Span B. Note that if\n         * a `style.fill` is defined, the `color` takes precedence and\n         * the `style.fill` is ignored.\n         *\n         * @see [senkouSpan.styles.fill](#series.ikh.senkouSpan.styles.fill)\n         *\n         * @sample stock/indicators/ichimoku-kinko-hyo\n         *         Ichimoku Kinko Hyo color\n         *\n         * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         * @since     7.0.0\n         * @apioption plotOptions.ikh.senkouSpan.color\n         */\n        /**\n         * Color of the area between Senkou Span A and B,\n         * when Senkou Span A is under Senkou Span B.\n         *\n         * @sample stock/indicators/ikh-negative-color\n         *         Ichimoku Kinko Hyo negativeColor\n         *\n         * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         * @since     7.0.0\n         * @apioption plotOptions.ikh.senkouSpan.negativeColor\n         */\n        styles: {\n            /**\n             * Color of the area between Senkou Span A and B.\n             *\n             * @deprecated\n             * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n             */\n            fill: 'rgba(255, 0, 0, 0.5)'\n        }\n    },\n    dataGrouping: {\n        approximation: 'ichimoku-averages'\n    }\n});\nextend(IKHIndicator.prototype, {\n    pointArrayMap: [\n        'tenkanSen',\n        'kijunSen',\n        'chikouSpan',\n        'senkouSpanA',\n        'senkouSpanB'\n    ],\n    pointValKey: 'tenkanSen',\n    nameComponents: ['periodSenkouSpanB', 'period', 'periodTenkan']\n});\n/* *\n *\n *  Registry\n *\n * */\n(highcharts_dataGrouping_approximations_commonjs_highcharts_dataGrouping_approximations_commonjs2_highcharts_dataGrouping_approximations_root_Highcharts_dataGrouping_approximations_default())[\"ichimoku-averages\"] = ichimokuAverages;\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('ikh', IKHIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const IKH_IKHIndicator = ((/* unused pure expression or super */ null && (IKHIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `IKH` series. If the [type](#series.ikh.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.ikh\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/ichimoku-kinko-hyo\n * @apioption series.ikh\n */\n(''); // Add doclet above to transpiled file\n\n;// ./code/es-modules/masters/indicators/ichimoku-kinko-hyo.js\n\n\n\n\n/* harmony default export */ const ichimoku_kinko_hyo_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__956__", "__WEBPACK_EXTERNAL_MODULE__620__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "i<PERSON><PERSON><PERSON>_kinko_hyo_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_dataGrouping_approximations_commonjs_highcharts_dataGrouping_approximations_commonjs2_highcharts_dataGrouping_approximations_root_Highcharts_dataGrouping_approximations_", "highcharts_dataGrouping_approximations_commonjs_highcharts_dataGrouping_approximations_commonjs2_highcharts_dataGrouping_approximations_root_Highcharts_dataGrouping_approximations_default", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "parse", "color", "sma", "SMAIndicator", "seriesTypes", "defined", "extend", "isArray", "isNumber", "getClosestDistance", "merge", "objectEach", "highlowLevel", "arr", "high", "reduce", "max", "res", "Math", "Infinity", "low", "min", "drawSenkouSpan", "opt", "indicator", "points", "nextPoints", "options", "senkouSpan", "styles", "gap", "graph", "fillGraph", "drawGraph", "IKHIndicator", "constructor", "arguments", "data", "graphCollection", "init", "apply", "tenkanLine", "lineColor", "kijunLine", "chikouLine", "senkouSpanA", "fill", "setOpacity", "senkouSpanB", "toYData", "point", "tenkanSen", "kijunSen", "chikouSpan", "translate", "pointArrayMap", "pointValue", "yAxis", "toPixels", "plotY", "tooltipPos", "plotX", "isNull", "mainLinePoints", "mainLineOptions", "mainLinePath", "mainColor", "gappedExtend", "gapSize", "pointArrayMapLength", "length", "allIchimokuPoints", "ikhMap", "intersectIndexColl", "senkouSpanOptions", "negativeColor", "pointsLength", "lineIndex", "position", "i", "startIntersect", "endIntersect", "sectionPoints", "sectionNextPoints", "pointsPlotYSum", "nextPointsPlotYSum", "senkouSpanTempColor", "concatArrIndex", "j", "k", "push", "index", "intersect", "checkLineIntersection", "a1", "a2", "b1", "b2", "saX", "saY", "sbX", "sbY", "sabX", "sabY", "u", "t", "intersectPointObj", "intersectPoint", "splice", "values", "lineName", "graphName", "destroy", "unshift", "slice", "floor", "x", "concat", "for<PERSON>ach", "areaName", "graphsenkouSpan", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "path", "spanA", "spanAarr", "getV<PERSON>ues", "series", "params", "date", "pointTS", "pointKS", "pointSSB", "TS", "KS", "CS", "SSA", "SSB", "period", "periodTenkan", "periodSenkouSpanB", "xVal", "xData", "yVal", "yData", "xAxis", "yValLen", "closestPointRange", "map", "s", "getColumn", "IKH", "dateStart", "defaultOptions", "marker", "enabled", "tooltip", "pointFormat", "lineWidth", "dataGrouping", "approximation", "pointVal<PERSON>ey", "nameComponents", "isEmptyRange", "ret", "average", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,YAAe,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,cAAiB,EACjK,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,2CAA4C,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,YAAe,CAAC,CAAC,iBAAiB,CAACA,EAAK,KAAQ,CAACA,EAAK,cAAiB,CAAE,GAC1L,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,2CAA2C,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,YAAe,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAE7MA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,YAAe,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAC1K,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,EAAkCC,IACzI,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACT,IAERA,EAAOD,OAAO,CAAGS,CAEX,EAEA,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,AAACL,IAERA,EAAOD,OAAO,CAAGO,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAad,OAAO,CAG5B,IAAIC,EAASU,CAAwB,CAACE,EAAS,CAAG,CAGjDb,QAAS,CAAC,CACX,EAMA,OAHAU,CAAmB,CAACG,EAAS,CAACZ,EAAQA,EAAOD,OAAO,CAAEY,GAG/CX,EAAOD,OAAO,AACtB,CAMCY,EAAoBI,CAAC,CAAG,AAACf,IACxB,IAAIgB,EAAShB,GAAUA,EAAOiB,UAAU,CACvC,IAAOjB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAW,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACnB,EAASqB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACvB,EAASsB,IAC5EE,OAAOC,cAAc,CAACzB,EAASsB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAuLzB,EAAoB,KAC3M0B,EAA2M1B,EAAoBI,CAAC,CAACqB,GAEjOE,EAA+F3B,EAAoB,KACnH4B,EAAmH5B,EAAoBI,CAAC,CAACuB,GAEzIE,EAAmI7B,EAAoB,KACvJ8B,EAAuJ9B,EAAoBI,CAAC,CAACyB,GAYjL,GAAM,CAAEE,MAAOC,CAAK,CAAE,CAAIJ,IAEpB,CAAEK,IAAKC,CAAY,CAAE,CAAG,AAACJ,IAA2IK,WAAW,CAE/K,CAAEC,QAAAA,CAAO,CAAEC,OAAAA,CAAM,CAAEC,QAAAA,CAAO,CAAEC,SAAAA,CAAQ,CAAEC,mBAAAA,CAAkB,CAAEC,MAAAA,CAAK,CAAEC,WAAAA,CAAU,CAAE,CAAIlB,IAyBvF,SAASmB,EAAaC,CAAG,EACrB,MAAO,CACHC,KAjBGD,AAiBWA,EAjBPE,MAAM,CAAC,SAAUC,CAAG,CAAEC,CAAG,EAChC,OAAOC,KAAKF,GAAG,CAACA,EAAKC,CAAG,CAAC,EAAE,CAC/B,EAAG,CAACE,KAgBAC,IAVGP,AAUSA,EAVLE,MAAM,CAAC,SAAUM,CAAG,CAAEJ,CAAG,EAChC,OAAOC,KAAKG,GAAG,CAACA,EAAKJ,CAAG,CAAC,EAAE,CAC/B,EAAGE,IASH,CACJ,CA6BA,SAASG,EAAeC,CAAG,EACvB,IAAMC,EAAYD,EAAIC,SAAS,AAC/BA,CAAAA,EAAUC,MAAM,CAAGF,EAAIE,MAAM,CAC7BD,EAAUE,UAAU,CAAGH,EAAIG,UAAU,CACrCF,EAAUvB,KAAK,CAAGsB,EAAItB,KAAK,CAC3BuB,EAAUG,OAAO,CAAGjB,EAAMa,EAAII,OAAO,CAACC,UAAU,CAACC,MAAM,CAAEN,EAAIO,GAAG,EAChEN,EAAUO,KAAK,CAAGR,EAAIQ,KAAK,CAC3BP,EAAUQ,SAAS,CAAG,CAAA,EACtBjC,IAA0IK,WAAW,CAACF,GAAG,CAACf,SAAS,CAAC8C,SAAS,CAAC5C,IAAI,CAACmC,EACvL,CAgCA,MAAMU,UAAqB/B,EACvBgC,aAAc,CAMV,KAAK,IAAIC,WAMT,IAAI,CAACC,IAAI,CAAG,EAAE,CACd,IAAI,CAACV,OAAO,CAAG,CAAC,EAChB,IAAI,CAACF,MAAM,CAAG,EAAE,CAChB,IAAI,CAACa,eAAe,CAAG,EAAE,AAC7B,CAMAC,MAAO,CACH,KAAK,CAACA,KAAKC,KAAK,CAAC,IAAI,CAAEJ,WAEvB,IAAI,CAACT,OAAO,CAAGjB,EAAM,CACjB+B,WAAY,CACRZ,OAAQ,CACJa,UAAW,IAAI,CAACzC,KAAK,AACzB,CACJ,EACA0C,UAAW,CACPd,OAAQ,CACJa,UAAW,IAAI,CAACzC,KAAK,AACzB,CACJ,EACA2C,WAAY,CACRf,OAAQ,CACJa,UAAW,IAAI,CAACzC,KAAK,AACzB,CACJ,EACA4C,YAAa,CACThB,OAAQ,CACJa,UAAW,IAAI,CAACzC,KAAK,CACrB6C,KAAM7C,EAAM,IAAI,CAACA,KAAK,EAAE8C,UAAU,CAAC,IAAK/D,GAAG,EAC/C,CACJ,EACAgE,YAAa,CACTnB,OAAQ,CACJa,UAAW,IAAI,CAACzC,KAAK,CACrB6C,KAAM7C,EAAM,IAAI,CAACA,KAAK,EAAE8C,UAAU,CAAC,IAAK/D,GAAG,EAC/C,CACJ,EACA4C,WAAY,CACRC,OAAQ,CACJiB,KAAM7C,EAAM,IAAI,CAACA,KAAK,EAAE8C,UAAU,CAAC,IAAK/D,GAAG,EAC/C,CACJ,CACJ,EAAG,IAAI,CAAC2C,OAAO,CACnB,CACAsB,QAAQC,CAAK,CAAE,CACX,MAAO,CACHA,EAAMC,SAAS,CACfD,EAAME,QAAQ,CACdF,EAAMG,UAAU,CAChBH,EAAML,WAAW,CACjBK,EAAMF,WAAW,CACpB,AACL,CACAM,WAAY,CAGR,IAAK,IAAMJ,KADXnD,IAA0IK,WAAW,CAACF,GAAG,CAACf,SAAS,CAACmE,SAAS,CAACd,KAAK,CADjK,IAAI,EAEFhB,AAFF,IAAI,CAEQC,MAAM,EAChC,IAAK,IAAM9C,KAAO6C,AAHJ,IAAI,CAGU+B,aAAa,CAAE,CACvC,IAAMC,EAAaN,CAAK,CAACvE,EAAI,CACzB6B,EAASgD,KACTN,CAAK,CAAC,OAASvE,EAAI,CAAG6C,AANhB,IAAI,CAMsBiC,KAAK,CAACC,QAAQ,CAACF,EAAY,CAAA,GAG3DN,EAAMS,KAAK,CAAGT,CAAK,CAAC,OAASvE,EAAI,CACjCuE,EAAMU,UAAU,CAAG,CACfV,EAAMW,KAAK,CACXX,CAAK,CAAC,OAASvE,EAAI,CACtB,CACDuE,EAAMY,MAAM,CAAG,CAAA,EAEvB,CAER,CACA7B,WAAY,CACR,IAAMT,EAAY,IAAI,CAAEuC,EAAiBvC,EAAUC,MAAM,CAAEuC,EAAkBxC,EAAUG,OAAO,CAAEsC,EAAezC,EAAUO,KAAK,CAAEmC,EAAY1C,EAAUvB,KAAK,CAAEkE,EAAe,CACxKxC,QAAS,CACLyC,QAASJ,EAAgBI,OAAO,AACpC,CACJ,EAAGC,EAAsB7C,EAAU+B,aAAa,CAACe,MAAM,CAAEC,EAAoB,CACzE,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACL,CAAEC,EAAS,CACR/B,WAAY8B,CAAiB,CAAC,EAAE,CAChC5B,UAAW4B,CAAiB,CAAC,EAAE,CAC/B3B,WAAY2B,CAAiB,CAAC,EAAE,CAChC1B,YAAa0B,CAAiB,CAAC,EAAE,CACjCvB,YAAauB,CAAiB,CAAC,EAAE,CACjC3C,WAAY2C,CAAiB,CAAC,EAAE,AACpC,EAAGE,EAAqB,EAAE,CAAEC,EAAoBlD,EAC3CG,OAAO,CAACC,UAAU,CAAE3B,EAAQyE,EAAkBzE,KAAK,EACpDyE,EAAkB7C,MAAM,CAACiB,IAAI,CAAE6B,EAAgBD,EAAkBC,aAAa,CAElFlD,EAAS,CACL,EAAE,CACF,EAAE,CACL,CAGDC,EAAa,CACT,EAAE,CACF,EAAE,CACL,CACGkD,EAAeb,EAAeO,MAAM,CAAEO,EAAY,EAAGC,EAAU5B,EAAO6B,EAAGC,EAAgBC,EAAcC,EAAeC,EAAmBC,EAAgBC,EAAoBC,EAAqBC,EAAgBC,EAAGC,EAGzN,IAFAjE,EAAUgD,MAAM,CAAGA,EAEZI,KAAgB,CAEnB,IAAKG,EAAI,EADT7B,EAAQa,CAAc,CAACa,EAAa,CACxBG,EAAIV,EAAqBU,IAE7B1E,EAAQ6C,CAAK,CADjB4B,EAAWtD,EAAU+B,aAAa,CAACwB,EAAE,CACV,GACvBR,CAAiB,CAACQ,EAAE,CAACW,IAAI,CAAC,CACtB7B,MAAOX,EAAMW,KAAK,CAClBF,MAAOT,CAAK,CAAC,OAAS4B,EAAS,CAC/BhB,OAAQ,CAAA,CACZ,GAGR,GAAIa,GAAiBC,IAAiBb,EAAeO,MAAM,CAAG,EAAG,CAE7D,IAAMqB,EAAQnB,EAAOxB,WAAW,CAACsB,MAAM,CAAG,EAAGsB,EAAYC,AA5MzE,SAA+BC,CAAE,CAAEC,CAAE,CAAEC,CAAE,CAAEC,CAAE,EACzC,GAAIH,GAAMC,GAAMC,GAAMC,EAAI,CACtB,IAAMC,EAAMH,EAAGlC,KAAK,CAAGiC,EAAGjC,KAAK,CAC/BsC,EAAMJ,EAAGpC,KAAK,CAAGmC,EAAGnC,KAAK,CACzByC,EAAMH,EAAGpC,KAAK,CAAGmC,EAAGnC,KAAK,CACzBwC,EAAMJ,EAAGtC,KAAK,CAAGqC,EAAGrC,KAAK,CACzB2C,EAAOR,EAAGjC,KAAK,CAAGmC,EAAGnC,KAAK,CAC1B0C,EAAOT,EAAGnC,KAAK,CAAGqC,EAAGrC,KAAK,CAE1B6C,EAAI,AAAC,CAAA,CAACL,EAAMG,EAAOJ,EAAMK,CAAG,EAAM,CAAA,CAACH,EAAMD,EAAMD,EAAMG,CAAE,EAAII,EAAI,AAACL,CAAAA,EAAMG,EAAOF,EAAMC,CAAG,EAAM,CAAA,CAACF,EAAMD,EAAMD,EAAMG,CAAE,EACjH,GAAIG,GAAK,GAAKA,GAAK,GAAKC,GAAK,GAAKA,GAAK,EACnC,MAAO,CACH5C,MAAOiC,EAAGjC,KAAK,CAAG4C,EAAIP,EACtBvC,MAAOmC,EAAGnC,KAAK,CAAG8C,EAAIN,CAC1B,CAER,CACJ,EA2L+F3B,EAAO3B,WAAW,CAAC8C,EAAQ,EAAE,CAAEnB,EAAO3B,WAAW,CAAC8C,EAAM,CAAEnB,EAAOxB,WAAW,CAAC2C,EAAQ,EAAE,CAAEnB,EAAOxB,WAAW,CAAC2C,EAAM,EACjM,GAAIC,EAAW,CACX,IAAMc,EAAoB,CACtB7C,MAAO+B,EAAU/B,KAAK,CACtBF,MAAOiC,EAAUjC,KAAK,CACtBG,OAAQ,CAAA,EACR6C,eAAgB,CAAA,CACpB,EAGAnC,EAAO3B,WAAW,CAAC+D,MAAM,CAACjB,EAAO,EAAGe,GACpClC,EAAOxB,WAAW,CAAC4D,MAAM,CAACjB,EAAO,EAAGe,GACpCjC,EAAmBiB,IAAI,CAACC,EAC5B,CACJ,CACJ,CAoBA,GAlBAhF,EAAW6D,EAAQ,CAACqC,EAAQC,KACpB9C,CAAe,CAAC8C,EAAS,EACzBA,AAAa,eAAbA,IAEAtF,EAAUC,MAAM,CAAG8C,CAAiB,CAACM,EAAU,CAC/CrD,EAAUG,OAAO,CAAGjB,EAAMsD,CAAe,CAAC8C,EAAS,CAACjF,MAAM,CAAEsC,GAC5D3C,EAAUO,KAAK,CAAGP,CAAS,CAAC,QAAUsF,EAAS,CAC/CtF,EAAUQ,SAAS,CAAG,CAAA,EACtBR,EAAUvB,KAAK,CAAGiE,EAClBnE,IAA0IK,WAAW,CAACF,GAAG,CAACf,SAAS,CAAC8C,SAAS,CAAC5C,IAAI,CAACmC,GAEnLA,CAAS,CAAC,QAAUsF,EAAS,CAAGtF,EAAUO,KAAK,EAEnD8C,GACJ,GAIIrD,EAAUc,eAAe,CACzB,IAAK,IAAMyE,KAAavF,EAAUc,eAAe,CAC7Cd,CAAS,CAACuF,EAAU,CAACC,OAAO,GAC5B,OAAOxF,CAAS,CAACuF,EAAU,CAMnC,GAFAvF,EAAUc,eAAe,CAAG,EAAE,CAE1BqC,GAAiBH,EAAO3B,WAAW,CAAC,EAAE,EAAI2B,EAAOxB,WAAW,CAAC,EAAE,CAAE,CAKjE,IAHAyB,EAAmBwC,OAAO,CAAC,GAC3BxC,EAAmBiB,IAAI,CAAClB,EAAO3B,WAAW,CAACyB,MAAM,CAAG,GAE/CkB,EAAI,EAAGA,EAAIf,EAAmBH,MAAM,CAAG,EAAGkB,IAO3C,GANAR,EAAiBP,CAAkB,CAACe,EAAE,CACtCP,EAAeR,CAAkB,CAACe,EAAI,EAAE,CACxCN,EAAgBV,EAAOxB,WAAW,CAACkE,KAAK,CAAClC,EAAgBC,EAAe,GACxEE,EAAoBX,EAAO3B,WAAW,CAACqE,KAAK,CAAClC,EAAgBC,EAAe,GAGxE/D,KAAKiG,KAAK,CAACjC,EAAcZ,MAAM,CAAG,IAAM,EAAG,CAC3C,IAAM8C,EAAIlG,KAAKiG,KAAK,CAACjC,EAAcZ,MAAM,CAAG,GAG5C,GAAIY,CAAa,CAACkC,EAAE,CAACzD,KAAK,GAAKwB,CAAiB,CAACiC,EAAE,CAACzD,KAAK,CAAE,CAGvD,IAAK8B,EAAI,EAFTL,EAAiB,EACjBC,EAAqB,EACTI,EAAIP,EAAcZ,MAAM,CAAEmB,IAClCL,GAAkBF,CAAa,CAACO,EAAE,CAAC9B,KAAK,CACxC0B,GAAsBF,CAAiB,CAACM,EAAE,CAAC9B,KAAK,AAIpDlC,CAAAA,CAAM,CAFN8D,EACIH,EAAiBC,EAAqB,EAAI,EACxB,CAAG5D,CAAM,CAAC8D,EAAe,CAAC8B,MAAM,CAACnC,GACvDxD,CAAU,CAAC6D,EAAe,CAAG7D,CAAU,CAAC6D,EAAe,CAAC8B,MAAM,CAAClC,EACnE,MAII1D,CAAM,CADN8D,EAAiB,AAACL,CAAa,CAACkC,EAAE,CAACzD,KAAK,CAAGwB,CAAiB,CAACiC,EAAE,CAACzD,KAAK,CAAI,EAAI,EACvD,CAAGlC,CAAM,CAAC8D,EAAe,CAAC8B,MAAM,CAACnC,GACvDxD,CAAU,CAAC6D,EAAe,CAAG7D,CAAU,CAAC6D,EAAe,CAAC8B,MAAM,CAAClC,EAEvE,MAII1D,CAAM,CADN8D,EAAiB,AAACL,CAAa,CAAC,EAAE,CAACvB,KAAK,CAAGwB,CAAiB,CAAC,EAAE,CAACxB,KAAK,CAAI,EAAI,EACvD,CAAGlC,CAAM,CAAC8D,EAAe,CAAC8B,MAAM,CAACnC,GACvDxD,CAAU,CAAC6D,EAAe,CAAG7D,CAAU,CAAC6D,EAAe,CAAC8B,MAAM,CAAClC,GAIvE,CAAC,uBAAwB,+BAA+B,CAACmC,OAAO,CAAC,SAAUC,CAAQ,CAAExC,CAAC,EAC9EtD,CAAM,CAACsD,EAAE,CAACT,MAAM,EAAI5C,CAAU,CAACqD,EAAE,CAACT,MAAM,GACxCgB,EAAsBP,AAAM,IAANA,EAAU9E,EAAQ0E,EACxCrD,EAAe,CACXE,UAAWA,EACXC,OAAQA,CAAM,CAACsD,EAAE,CACjBrD,WAAYA,CAAU,CAACqD,EAAE,CACzB9E,MAAOqF,EACP3D,QAASqC,EACTlC,IAAKqC,EACLpC,MAAOP,CAAS,CAAC+F,EAAS,AAC9B,GAEA/F,CAAS,CAAC+F,EAAS,CAAG/F,EAAUO,KAAK,CACrCP,EAAUc,eAAe,CAACoD,IAAI,CAAC6B,GAEvC,EACJ,MAGIjG,EAAe,CACXE,UAAWA,EACXC,OAAQ+C,EAAOxB,WAAW,CAC1BtB,WAAY8C,EAAO3B,WAAW,CAC9B5C,MAAOA,EACP0B,QAASqC,EACTlC,IAAKqC,EACLpC,MAAOP,EAAUgG,eAAe,AACpC,GAEAhG,EAAUgG,eAAe,CAAGhG,EAAUO,KAAK,AAG/C,QAAOP,EAAUE,UAAU,CAC3B,OAAOF,EAAUQ,SAAS,CAE1BR,EAAUC,MAAM,CAAGsC,EACnBvC,EAAUG,OAAO,CAAGqC,EACpBxC,EAAUO,KAAK,CAAGkC,EAClBzC,EAAUvB,KAAK,CAAGiE,CACtB,CACAuD,aAAahG,CAAM,CAAE,CAEjB,IAAIiG,EAAO,EAAE,CAAEC,EAAOC,EAAW,EAAE,CAGnC,GAFAnG,EAASA,GAAU,IAAI,CAACA,MAAM,CAE1BD,AAJc,IAAI,CAIRQ,SAAS,EAAIR,AAJT,IAAI,CAIeE,UAAU,CAI3C,CAAA,GAAIiG,AAHJA,CAAAA,EAAQ5H,IAA0IK,WAAW,CAACF,GAAG,CAACf,SAAS,CAACsI,YAAY,CAACpI,IAAI,CAL/K,IAAI,CAOlBmC,AAPc,IAAI,CAORE,UAAU,CAAA,GACPiG,EAAMrD,MAAM,CAAE,CACvBqD,CAAK,CAAC,EAAE,CAAC,EAAE,CAAG,IACdD,EAAO3H,IAA0IK,WAAW,CAACF,GAAG,CAACf,SAAS,CAACsI,YAAY,CAClLpI,IAAI,CAXC,IAAI,CAWOoC,GACrBmG,EAAWD,EAAMT,KAAK,CAAC,EAAGQ,EAAKpD,MAAM,EACrC,IAAK,IAAIS,EAAI6C,EAAStD,MAAM,CAAG,EAAGS,GAAK,EAAGA,IACtC2C,EAAKhC,IAAI,CAACkC,CAAQ,CAAC7C,EAAE,CAE7B,CAAA,MAGA2C,EAAO3H,IAA0IK,WAAW,CAACF,GAAG,CAACf,SAAS,CAACsI,YAAY,CAClLjF,KAAK,CApBI,IAAI,CAoBIJ,WAE1B,OAAOsF,CACX,CACAG,UAAUC,CAAM,CAAEC,CAAM,CAAE,CACtB,IACIC,EAAwCC,EAASC,EAASC,EAAUpD,EAAGqD,EAAIC,EAAIC,EAAIC,EAAKC,EADtFC,EAASV,EAAOU,MAAM,CAAEC,EAAeX,EAAOW,YAAY,CAAEC,EAAoBZ,EAAOY,iBAAiB,CAAEC,EAAOd,EAAOe,KAAK,CAAEC,EAAOhB,EAAOiB,KAAK,CAAEC,EAAQlB,EAAOkB,KAAK,CAAEC,EAAU,AAACH,GAAQA,EAAKxE,MAAM,EAAK,EAAG4E,EAAoBzI,EAAmBuI,EAAMlB,MAAM,CAACqB,GAAG,CAAC,AAACC,GAAMA,EAAEC,SAAS,CAAC,OAAQC,EAAM,EAAE,CAAET,EAAQ,EAAE,CAG7T,GAAID,EAAKtE,MAAM,EAAImE,GACf,CAAClI,EAAQuI,CAAI,CAAC,EAAE,GAChBA,AAAmB,IAAnBA,CAAI,CAAC,EAAE,CAACxE,MAAM,CACd,OAGJ,IAAMiF,EAAYX,CAAI,CAAC,EAAE,CAAGH,EAASS,EACrC,IAAKnE,EAAI,EAAGA,EAAI0D,EAAQ1D,IACpB8D,EAAMnD,IAAI,CAAC6D,EAAYxE,EAAImE,GAE/B,IAAKnE,EAAI,EAAGA,EAAIkE,EAASlE,IAEjBA,GAAK2D,GAGLN,CAAAA,EAAK,AAACH,CAAAA,AADNA,CAAAA,EAAUrH,EADEkI,EAAK5B,KAAK,CAACnC,EAAI2D,EAAc3D,GACT,EAClBjE,IAAI,CAAGmH,EAAQ7G,GAAG,AAAD,EAAK,CAAA,EAEpC2D,GAAK0D,GAILF,CAAAA,EAAM,AAACH,CAAAA,EADPC,CAAAA,EAAK,AAACH,CAAAA,AADNA,CAAAA,EAAUtH,EADEkI,EAAK5B,KAAK,CAACnC,EAAI0D,EAAQ1D,GACH,EAClBjE,IAAI,CAAGoH,EAAQ9G,GAAG,AAAD,EAAK,CAAA,CACvB,EAAK,CAAA,EAElB2D,GAAK4D,GAGLH,CAAAA,EAAM,AAACL,CAAAA,AADPA,CAAAA,EAAWvH,EADEkI,EAAK5B,KAAK,CAACnC,EAAI4D,EAAmB5D,GACb,EAClBjE,IAAI,CAAGqH,EAAS/G,GAAG,AAAD,EAAK,CAAA,EAE3CkH,EAAKQ,CAAI,CAAC/D,EAAE,CAAC,EAAE,CACfiD,EAAOY,CAAI,CAAC7D,EAAE,CACV,AAAkB,KAAA,IAAXuE,CAAG,CAACvE,EAAE,EACbuE,CAAAA,CAAG,CAACvE,EAAE,CAAG,EAAE,AAAD,EAEV,AAA+B,KAAA,IAAxBuE,CAAG,CAACvE,EAAI0D,EAAS,EAAE,EAC1Ba,CAAAA,CAAG,CAACvE,EAAI0D,EAAS,EAAE,CAAG,EAAE,AAAD,EAE3Ba,CAAG,CAACvE,EAAI0D,EAAS,EAAE,CAAC,EAAE,CAAGL,EACzBkB,CAAG,CAACvE,EAAI0D,EAAS,EAAE,CAAC,EAAE,CAAGJ,EACzBiB,CAAG,CAACvE,EAAI0D,EAAS,EAAE,CAAC,EAAE,CAAG,KAAK,EAC1B,AAAsB,KAAA,IAAfa,CAAG,CAACvE,EAAI,EAAE,EACjBuE,CAAAA,CAAG,CAACvE,EAAI,EAAE,CAAG,EAAE,AAAD,EAElBuE,CAAG,CAACvE,EAAI,EAAE,CAAC,EAAE,CAAGuD,EACZvD,GAAK0D,IACLa,CAAG,CAACvE,EAAI0D,EAAS,EAAE,CAAC,EAAE,CAAG,KAAK,EAC9Ba,CAAG,CAACvE,EAAI0D,EAAS,EAAE,CAAC,EAAE,CAAG,KAAK,GAE9B,AAAmC,KAAA,IAA5Ba,CAAG,CAACvE,EAAI,EAAI0D,EAAS,EAAE,EAC9Ba,CAAAA,CAAG,CAACvE,EAAI,EAAI0D,EAAS,EAAE,CAAG,EAAE,AAAD,EAE/Ba,CAAG,CAACvE,EAAI,EAAI0D,EAAS,EAAE,CAAC,EAAE,CAAGF,EAC7Be,CAAG,CAACvE,EAAI,EAAI0D,EAAS,EAAE,CAAC,EAAE,CAAGD,EAC7BK,EAAMnD,IAAI,CAACsC,GAGf,IAAKjD,EAAI,EAAGA,GAAK0D,EAAQ1D,IACrB8D,EAAMnD,IAAI,CAACsC,EAAOjD,EAAImE,GAE1B,MAAO,CACHrC,OAAQyC,EACRT,MAAOA,EACPE,MAAOO,CACX,CACJ,CACJ,CAmBApH,EAAasH,cAAc,CAAG9I,EAAMP,EAAaqJ,cAAc,CAAE,CAI7DzB,OAAQ,CACJpC,MAAO,KAAK,EACZ8C,OAAQ,GAIRC,aAAc,EAIdC,kBAAmB,EACvB,EACAc,OAAQ,CACJC,QAAS,CAAA,CACb,EACAC,QAAS,CACLC,YAAa,+QAMjB,EAIAnH,WAAY,CACRZ,OAAQ,CAIJgI,UAAW,EAMXnH,UAAW,KAAK,CACpB,CACJ,EAIAC,UAAW,CACPd,OAAQ,CAIJgI,UAAW,EAMXnH,UAAW,KAAK,CACpB,CACJ,EAIAE,WAAY,CACRf,OAAQ,CAIJgI,UAAW,EAMXnH,UAAW,KAAK,CACpB,CACJ,EAIAG,YAAa,CACThB,OAAQ,CAIJgI,UAAW,EAMXnH,UAAW,KAAK,CACpB,CACJ,EAIAM,YAAa,CACTnB,OAAQ,CAIJgI,UAAW,EAMXnH,UAAW,KAAK,CACpB,CACJ,EAIAd,WAAY,CA2BRC,OAAQ,CAOJiB,KAAM,sBACV,CACJ,EACAgH,aAAc,CACVC,cAAe,mBACnB,CACJ,GACAzJ,EAAO4B,EAAa/C,SAAS,CAAE,CAC3BoE,cAAe,CACX,YACA,WACA,aACA,cACA,cACH,CACDyG,YAAa,YACbC,eAAgB,CAAC,oBAAqB,SAAU,eAAe,AACnE,GAMA,AAACtK,GAA8L,CAAC,oBAAoB,CAjkBpN,WACI,IACIuK,EADEC,EAAM,EAAE,CAQd,MANA,EAAE,CAAC7C,OAAO,CAACjI,IAAI,CAAC+C,UAAW,SAAUvB,CAAG,CAAEkE,CAAC,EACvCoF,EAAIzE,IAAI,CAAC/F,IAA8LyK,OAAO,CAACvJ,IAC/MqJ,EAAe,CAACA,GAAgB,AAAkB,KAAA,IAAXC,CAAG,CAACpF,EAAE,AACjD,GAGOmF,EAAe,KAAK,EAAIC,CACnC,EAwjBApK,IAA0IsK,kBAAkB,CAAC,MAAOnI,GA+BvI,IAAM3C,EAA2BE,IAGpD,OADYH,EAAoB,OAAU,AAE3C,CAAA"}