{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/accumulation-distribution\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Sebastian <PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/accumulation-distribution\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/accumulation-distribution\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ accumulation_distribution_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/AD/ADIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n * */\n\n\nconst { sma: SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { error, extend, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The AD series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.ad\n *\n * @augments Highcharts.Series\n */\nclass ADIndicator extends SMAIndicator {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static populateAverage(xVal, yVal, yValVolume, i, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _period) {\n        const high = yVal[i][1], low = yVal[i][2], close = yVal[i][3], volume = yValVolume[i], adY = close === high && close === low || high === low ?\n            0 :\n            ((2 * close - low - high) / (high - low)) * volume, adX = xVal[i];\n        return [adX, adY];\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, volumeSeriesID = params.volumeSeriesID, volumeSeries = series.chart.get(volumeSeriesID), yValVolume = volumeSeries?.getColumn('y'), yValLen = yVal ? yVal.length : 0, AD = [], xData = [], yData = [];\n        let len, i, ADPoint;\n        if (xVal.length <= period &&\n            yValLen &&\n            yVal[0].length !== 4) {\n            return;\n        }\n        if (!volumeSeries) {\n            error('Series ' +\n                volumeSeriesID +\n                ' not found! Check `volumeSeriesID`.', true, series.chart);\n            return;\n        }\n        // When i = period <-- skip first N-points\n        // Calculate value one-by-one for each period in visible data\n        for (i = period; i < yValLen; i++) {\n            len = AD.length;\n            ADPoint = ADIndicator.populateAverage(xVal, yVal, yValVolume, i, period);\n            if (len > 0) {\n                ADPoint[1] += AD[len - 1][1];\n            }\n            AD.push(ADPoint);\n            xData.push(ADPoint[0]);\n            yData.push(ADPoint[1]);\n        }\n        return {\n            values: AD,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Accumulation Distribution (AD). This series requires `linkedTo` option to\n * be set.\n *\n * @sample stock/indicators/accumulation-distribution\n *         Accumulation/Distribution indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/accumulation-distribution\n * @optionparent plotOptions.ad\n */\nADIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    /**\n     * @excluding index\n     */\n    params: {\n        index: void 0, // Unused index, do not inherit (#15362)\n        /**\n         * The id of volume series which is mandatory.\n         * For example using OHLC data, volumeSeriesID='volume' means\n         * the indicator will be calculated using OHLC and volume values.\n         *\n         * @since 6.0.0\n         */\n        volumeSeriesID: 'volume'\n    }\n});\nextend(ADIndicator.prototype, {\n    nameComponents: false,\n    nameBase: 'Accumulation/Distribution'\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('ad', ADIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const AD_ADIndicator = ((/* unused pure expression or super */ null && (ADIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `AD` series. If the [type](#series.ad.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.ad\n * @since     6.0.0\n * @excluding dataParser, dataURL\n * @product   highstock\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/accumulation-distribution\n * @apioption series.ad\n */\n''; // Add doclet above to transpiled file\n\n;// ./code/es-modules/masters/indicators/accumulation-distribution.js\n\n\n\n\n/* harmony default export */ const accumulation_distribution_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "accumulation_distribution_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "sma", "SMAIndicator", "seriesTypes", "error", "extend", "merge", "ADIndicator", "populateAverage", "xVal", "yVal", "yValVolume", "i", "_period", "high", "low", "close", "volume", "adY", "getV<PERSON>ues", "series", "params", "len", "ADPoint", "period", "xData", "yData", "volumeSeriesID", "volumeSeries", "chart", "getColumn", "yValLen", "length", "AD", "push", "values", "defaultOptions", "index", "nameComponents", "nameBase", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,kDAAmD,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GAC3I,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,kDAAkD,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAE/HA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAUjL,GAAM,CAAEE,IAAKC,CAAY,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAE/K,CAAEC,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAE,CAAIR,GAelC,OAAMS,UAAoBL,EAMtB,OAAOM,gBAAgBC,CAAI,CAAEC,CAAI,CAAEC,CAAU,CAAEC,CAAC,CAEhDC,CAAO,CAAE,CACL,IAAMC,EAAOJ,CAAI,CAACE,EAAE,CAAC,EAAE,CAAEG,EAAML,CAAI,CAACE,EAAE,CAAC,EAAE,CAAEI,EAAQN,CAAI,CAACE,EAAE,CAAC,EAAE,CAAEK,EAASN,CAAU,CAACC,EAAE,CAAEM,EAAMF,IAAUF,GAAQE,IAAUD,GAAOD,IAASC,EACrI,EACA,AAAE,CAAA,EAAIC,EAAQD,EAAMD,CAAG,EAAMA,CAAAA,EAAOC,CAAE,EAAME,EAChD,MAAO,CADuDR,CAAI,CAACG,EAAE,CACxDM,EAAI,AACrB,CAMAC,UAAUC,CAAM,CAAEC,CAAM,CAAE,CACtB,IACIC,EAAKV,EAAGW,EADNC,EAASH,EAAOG,MAAM,CAAEf,EAAOW,EAAOK,KAAK,CAAEf,EAAOU,EAAOM,KAAK,CAAEC,EAAiBN,EAAOM,cAAc,CAAEC,EAAeR,EAAOS,KAAK,CAACxC,GAAG,CAACsC,GAAiBhB,EAAaiB,GAAcE,UAAU,KAAMC,EAAUrB,EAAOA,EAAKsB,MAAM,CAAG,EAAGC,EAAK,EAAE,CAAER,EAAQ,EAAE,CAAEC,EAAQ,EAAE,CAE7Q,GAAIjB,CAAAA,CAAAA,EAAKuB,MAAM,EAAIR,CAAK,IACpBO,GACArB,AAAmB,IAAnBA,CAAI,CAAC,EAAE,CAACsB,MAAM,EAGlB,GAAI,CAACJ,EAAc,YACfxB,EAAM,UACFuB,EACA,sCAAuC,CAAA,EAAMP,EAAOS,KAAK,EAKjE,IAAKjB,EAAIY,EAAQZ,EAAImB,EAASnB,IAC1BU,EAAMW,EAAGD,MAAM,CACfT,EAAUhB,EAAYC,eAAe,CAACC,EAAMC,EAAMC,EAAYC,EAAGY,GAC7DF,EAAM,GACNC,CAAAA,CAAO,CAAC,EAAE,EAAIU,CAAE,CAACX,EAAM,EAAE,CAAC,EAAE,AAAD,EAE/BW,EAAGC,IAAI,CAACX,GACRE,EAAMS,IAAI,CAACX,CAAO,CAAC,EAAE,EACrBG,EAAMQ,IAAI,CAACX,CAAO,CAAC,EAAE,EAEzB,MAAO,CACHY,OAAQF,EACRR,MAAOA,EACPC,MAAOA,CACX,EACJ,CACJ,CAoBAnB,EAAY6B,cAAc,CAAG9B,EAAMJ,EAAakC,cAAc,CAAE,CAI5Df,OAAQ,CACJgB,MAAO,KAAK,EAQZV,eAAgB,QACpB,CACJ,GACAtB,EAAOE,EAAYf,SAAS,CAAE,CAC1B8C,eAAgB,CAAA,EAChBC,SAAU,2BACd,GACAvC,IAA0IwC,kBAAkB,CAAC,KAAMjC,GA+BtI,IAAMX,EAAkCE,IAG3D,OADYH,EAAoB,OAAU,AAE3C,CAAA"}