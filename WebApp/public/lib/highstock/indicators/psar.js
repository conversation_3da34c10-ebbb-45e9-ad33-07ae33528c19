!/**
 * Highstock JS v12.3.0 (2025-06-21)
 * @module highcharts/indicators/psar
 * @requires highcharts
 * @requires highcharts/modules/stock
 *
 * Parabolic SAR Indicator for Highcharts Stock
 *
 * (c) 2010-2025 <PERSON><PERSON><PERSON><PERSON>
 *
 * License: www.highcharts.com/license
 */function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(e._Highcharts,e._Highcharts.SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/indicators/psar",["highcharts/highcharts"],function(e){return t(e,e.SeriesRegistry)}):"object"==typeof exports?exports["highcharts/indicators/psar"]=t(e._Highcharts,e._Highcharts.SeriesRegistry):e.Highcharts=t(e.Highcharts,e.Highcharts.SeriesRegistry)}("undefined"==typeof window?this:window,(e,t)=>(()=>{"use strict";var r={512:e=>{e.exports=t},944:t=>{t.exports=e}},a={};function i(e){var t=a[e];if(void 0!==t)return t.exports;var n=a[e]={exports:{}};return r[e](n,n.exports,i),n.exports}i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},i.d=(e,t)=>{for(var r in t)i.o(t,r)&&!i.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var n={};i.d(n,{default:()=>f});var s=i(944),o=i.n(s),c=i(512),l=i.n(c);let{sma:h}=l().seriesTypes,{merge:u}=o();function p(e,t){return parseFloat(e.toFixed(t))}class d extends h{constructor(){super(...arguments),this.nameComponents=void 0}getValues(e,t){let r=e.xData,a=e.yData,i=t.maxAccelerationFactor,n=t.increment,s=t.initialAccelerationFactor,o=t.decimals,c=t.index,l=[],h=[],u=[],d=t.initialAccelerationFactor,f,m=a[0][1],x,g,y,v=1,F,b,H,M,A=a[0][2],_,j,w,O;if(!(c>=a.length)){for(O=0;O<c;O++)m=Math.max(a[O][1],m),A=Math.min(a[O][2],p(A,o));for(f=a[O][1]>A?1:-1,x=m-A,g=(d=t.initialAccelerationFactor)*x,l.push([r[c],A]),h.push(r[c]),u.push(p(A,o)),O=c+1;O<a.length;O++)if(F=a[O-1][2],b=a[O-2][2],H=a[O-1][1],M=a[O-2][1],j=a[O][1],w=a[O][2],null!==b&&null!==M&&null!==F&&null!==H&&null!==j&&null!==w){var S,D,R,P,T,W,k,C,V,q,z,B,E,G,I,J,K,L,N,Q,U;T=f,W=v,k=A,C=g,V=b,q=F,z=H,B=M,E=m,A=T===W?1===T?k+C<Math.min(V,q)?k+C:Math.min(V,q):k+C>Math.max(B,z)?k+C:Math.max(B,z):E,S=f,D=m,_=1===S?j>D?j:D:w<D?w:D,R=v,P=A,G=y=1===R&&w>P||-1===R&&j>P?1:-1,I=f,J=_,K=m,L=d,N=n,Q=i,U=s,g=(d=G===I?1===G&&J>K||-1===G&&J<K?L===Q?Q:p(L+N,2):L:U)*(x=_-A),l.push([r[O],p(A,o)]),h.push(r[O]),u.push(p(A,o)),v=f,f=y,m=_}return{values:l,xData:h,yData:u}}}}d.defaultOptions=u(h.defaultOptions,{lineWidth:0,marker:{enabled:!0},states:{hover:{lineWidthPlus:0}},params:{period:void 0,initialAccelerationFactor:.02,maxAccelerationFactor:.2,increment:.02,index:2,decimals:4}}),l().registerSeriesType("psar",d);let f=o();return n.default})());