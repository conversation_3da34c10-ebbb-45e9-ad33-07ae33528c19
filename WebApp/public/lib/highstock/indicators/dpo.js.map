{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/dpo\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Wojciech Chmiel\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/dpo\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/dpo\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ dpo_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/DPO/DPOIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: SMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { extend, merge, correctFloat, pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n// Utils:\n/**\n * @private\n */\nfunction accumulatePoints(sum, yVal, i, index, subtract) {\n    const price = pick(yVal[i][index], yVal[i]);\n    if (subtract) {\n        return correctFloat(sum - price);\n    }\n    return correctFloat(sum + price);\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The DPO series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.dpo\n *\n * @augments Highcharts.Series\n */\nclass DPOIndicator extends SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, index = params.index, offset = Math.floor(period / 2 + 1), range = period + offset, xVal = series.xData || [], yVal = series.yData || [], yValLen = yVal.length, \n        // 0- date, 1- Detrended Price Oscillator\n        DPO = [], xData = [], yData = [];\n        let oscillator, periodIndex, rangeIndex, price, i, j, sum = 0;\n        if (xVal.length <= range) {\n            return;\n        }\n        // Accumulate first N-points for SMA\n        for (i = 0; i < period - 1; i++) {\n            sum = accumulatePoints(sum, yVal, i, index);\n        }\n        // Detrended Price Oscillator formula:\n        // DPO = Price - Simple moving average [from (n / 2 + 1) days ago]\n        for (j = 0; j <= yValLen - range; j++) {\n            periodIndex = j + period - 1;\n            rangeIndex = j + range - 1;\n            // Adding the last period point\n            sum = accumulatePoints(sum, yVal, periodIndex, index);\n            price = pick(yVal[rangeIndex][index], yVal[rangeIndex]);\n            oscillator = price - sum / period;\n            // Subtracting the first period point\n            sum = accumulatePoints(sum, yVal, j, index, true);\n            DPO.push([xVal[rangeIndex], oscillator]);\n            xData.push(xVal[rangeIndex]);\n            yData.push(oscillator);\n        }\n        return {\n            values: DPO,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Detrended Price Oscillator. This series requires the `linkedTo` option to\n * be set and should be loaded after the `stock/indicators/indicators.js`.\n *\n * @sample {highstock} stock/indicators/dpo\n *         Detrended Price Oscillator\n *\n * @extends      plotOptions.sma\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, compare, compareBase, joinBy, keys,\n *               navigatorOptions, pointInterval, pointIntervalUnit,\n *               pointPlacement, pointRange, pointStart, showInNavigator,\n *               stacking\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/dpo\n * @optionparent plotOptions.dpo\n */\nDPOIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    /**\n     * Parameters used in calculation of Detrended Price Oscillator series\n     * points.\n     */\n    params: {\n        index: 0,\n        /**\n         * Period for Detrended Price Oscillator\n         */\n        period: 21\n    }\n});\nextend(DPOIndicator.prototype, {\n    nameBase: 'DPO'\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('dpo', DPOIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const DPO_DPOIndicator = ((/* unused pure expression or super */ null && (DPOIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A Detrended Price Oscillator. If the [type](#series.dpo.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.dpo\n * @since     7.0.0\n * @product   highstock\n * @excluding allAreas, colorAxis, compare, compareBase, dataParser, dataURL,\n *            joinBy, keys, navigatorOptions, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointRange, pointStart, showInNavigator, stacking\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/dpo\n * @apioption series.dpo\n */\n''; // To include the above in the js output'\n\n;// ./code/es-modules/masters/indicators/dpo.js\n\n\n\n\n/* harmony default export */ const dpo_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "dpo_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "sma", "SMAIndicator", "seriesTypes", "extend", "merge", "correctFloat", "pick", "accumulatePoints", "sum", "yVal", "i", "index", "subtract", "price", "DPOIndicator", "getV<PERSON>ues", "series", "params", "period", "offset", "Math", "floor", "range", "xVal", "xData", "yData", "yValLen", "length", "DPO", "oscillator", "periodIndex", "rangeIndex", "j", "push", "values", "defaultOptions", "nameBase", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,4BAA6B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACrH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,4BAA4B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAEzGA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAWjL,GAAM,CAAEE,IAAKC,CAAY,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAE/K,CAAEC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAEC,aAAAA,CAAY,CAAEC,KAAAA,CAAI,CAAE,CAAIT,IAU/C,SAASU,EAAiBC,CAAG,CAAEC,CAAI,CAAEC,CAAC,CAAEC,CAAK,CAAEC,CAAQ,EACnD,IAAMC,EAAQP,EAAKG,CAAI,CAACC,EAAE,CAACC,EAAM,CAAEF,CAAI,CAACC,EAAE,SAC1C,AAAIE,EACOP,EAAaG,EAAMK,GAEvBR,EAAaG,EAAMK,EAC9B,CAeA,MAAMC,UAAqBb,EAMvBc,UAAUC,CAAM,CAAEC,CAAM,CAAE,CACtB,IAAMC,EAASD,EAAOC,MAAM,CAAEP,EAAQM,EAAON,KAAK,CAAEQ,EAASC,KAAKC,KAAK,CAACH,EAAS,EAAI,GAAII,EAAQJ,EAASC,EAAQI,EAAOP,EAAOQ,KAAK,EAAI,EAAE,CAAEf,EAAOO,EAAOS,KAAK,EAAI,EAAE,CAAEC,EAAUjB,EAAKkB,MAAM,CAE7LC,EAAM,EAAE,CAAEJ,EAAQ,EAAE,CAAEC,EAAQ,EAAE,CAC5BI,EAAYC,EAAaC,EAAmBrB,EAAGsB,EAAGxB,EAAM,EAC5D,IAAIe,CAAAA,EAAKI,MAAM,EAAIL,CAAI,GAIvB,IAAKZ,EAAI,EAAGA,EAAIQ,EAAS,EAAGR,IACxBF,EAAMD,EAAiBC,EAAKC,EAAMC,EAAGC,GAIzC,IAAKqB,EAAI,EAAGA,GAAKN,EAAUJ,EAAOU,IAC9BF,EAAcE,EAAId,EAAS,EAC3Ba,EAAaC,EAAIV,EAAQ,EAEzBd,EAAMD,EAAiBC,EAAKC,EAAMqB,EAAanB,GAE/CkB,EAAahB,AADLP,EAAKG,CAAI,CAACsB,EAAW,CAACpB,EAAM,CAAEF,CAAI,CAACsB,EAAW,EACjCvB,EAAMU,EAE3BV,EAAMD,EAAiBC,EAAKC,EAAMuB,EAAGrB,EAAO,CAAA,GAC5CiB,EAAIK,IAAI,CAAC,CAACV,CAAI,CAACQ,EAAW,CAAEF,EAAW,EACvCL,EAAMS,IAAI,CAACV,CAAI,CAACQ,EAAW,EAC3BN,EAAMQ,IAAI,CAACJ,GAEf,MAAO,CACHK,OAAQN,EACRJ,MAAOA,EACPC,MAAOA,CACX,EACJ,CACJ,CAwBAX,EAAaqB,cAAc,CAAG/B,EAAMH,EAAakC,cAAc,CAAE,CAK7DlB,OAAQ,CACJN,MAAO,EAIPO,OAAQ,EACZ,CACJ,GACAf,EAAOW,EAAavB,SAAS,CAAE,CAC3B6C,SAAU,KACd,GACArC,IAA0IsC,kBAAkB,CAAC,MAAOvB,GAiCvI,IAAMnB,EAAYE,IAGrC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}