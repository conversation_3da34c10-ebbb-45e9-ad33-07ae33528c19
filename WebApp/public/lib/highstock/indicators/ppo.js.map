{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/indicators/ppo\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Wojciech Chmiel\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/ppo\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/ppo\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ ppo_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Indicators/PPO/PPOIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { ema: EMAIndicator } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { correctFloat, extend, merge, error } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The PPO series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.ppo\n *\n * @augments Highcharts.Series\n */\nclass PPOIndicator extends EMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const periods = params.periods, index = params.index, \n        // 0- date, 1- Percentage Price Oscillator\n        PPO = [], xData = [], yData = [];\n        let oscillator, i;\n        // Check if periods are correct\n        if (periods.length !== 2 || periods[1] <= periods[0]) {\n            error('Error: \"PPO requires two periods. Notice, first period ' +\n                'should be lower than the second one.\"');\n            return;\n        }\n        // Shorter Period EMA\n        const SPE = super.getValues.call(this, series, {\n            index: index,\n            period: periods[0]\n        });\n        // Longer Period EMA\n        const LPE = super.getValues.call(this, series, {\n            index: index,\n            period: periods[1]\n        });\n        // Check if ema is calculated properly, if not skip\n        if (!SPE || !LPE) {\n            return;\n        }\n        const periodsOffset = periods[1] - periods[0];\n        for (i = 0; i < LPE.yData.length; i++) {\n            oscillator = correctFloat((SPE.yData[i + periodsOffset] -\n                LPE.yData[i]) /\n                LPE.yData[i] *\n                100);\n            PPO.push([LPE.xData[i], oscillator]);\n            xData.push(LPE.xData[i]);\n            yData.push(oscillator);\n        }\n        return {\n            values: PPO,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Percentage Price Oscillator. This series requires the\n * `linkedTo` option to be set and should be loaded after the\n * `stock/indicators/indicators.js`.\n *\n * @sample {highstock} stock/indicators/ppo\n *         Percentage Price Oscillator\n *\n * @extends      plotOptions.ema\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, joinBy, keys, navigatorOptions,\n *               pointInterval, pointIntervalUnit, pointPlacement,\n *               pointRange, pointStart, showInNavigator, stacking\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/ppo\n * @optionparent plotOptions.ppo\n */\nPPOIndicator.defaultOptions = merge(EMAIndicator.defaultOptions, {\n    /**\n     * Parameters used in calculation of Percentage Price Oscillator series\n     * points.\n     *\n     * @excluding period\n     */\n    params: {\n        period: void 0, // Unchangeable period, do not inherit (#15362)\n        /**\n         * Periods for Percentage Price Oscillator calculations.\n         *\n         * @type    {Array<number>}\n         * @default [12, 26]\n         */\n        periods: [12, 26]\n    }\n});\nextend(PPOIndicator.prototype, {\n    nameBase: 'PPO',\n    nameComponents: ['periods']\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('ppo', PPOIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const PPO_PPOIndicator = ((/* unused pure expression or super */ null && (PPOIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `Percentage Price Oscillator` series. If the [type](#series.ppo.type)\n * option is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.ppo\n * @since     7.0.0\n * @product   highstock\n * @excluding allAreas, colorAxis, dataParser, dataURL, joinBy, keys,\n *            navigatorOptions, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointRange, pointStart, showInNavigator, stacking\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/ppo\n * @apioption series.ppo\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/masters/indicators/ppo.js\n\n\n\n\n/* harmony default export */ const ppo_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "ppo_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "ema", "EMAIndicator", "seriesTypes", "correctFloat", "extend", "merge", "error", "PPOIndicator", "getV<PERSON>ues", "series", "params", "oscillator", "i", "periods", "index", "PPO", "xData", "yData", "length", "SPE", "period", "LPE", "periodsOffset", "push", "values", "defaultOptions", "nameBase", "nameComponents", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,4BAA6B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACrH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,4BAA4B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAEzGA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAWjL,GAAM,CAAEE,IAAKC,CAAY,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAE/K,CAAEC,aAAAA,CAAY,CAAEC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAE,CAAIT,GAehD,OAAMU,UAAqBN,EAMvBO,UAAUC,CAAM,CAAEC,CAAM,CAAE,CACtB,IAGIC,EAAYC,EAHVC,EAAUH,EAAOG,OAAO,CAAEC,EAAQJ,EAAOI,KAAK,CAEpDC,EAAM,EAAE,CAAEC,EAAQ,EAAE,CAAEC,EAAQ,EAAE,CAGhC,GAAIJ,AAAmB,IAAnBA,EAAQK,MAAM,EAAUL,CAAO,CAAC,EAAE,EAAIA,CAAO,CAAC,EAAE,CAAE,YAClDP,EAAM,gGAKV,IAAMa,EAAM,KAAK,CAACX,UAAUf,IAAI,CAAC,IAAI,CAAEgB,EAAQ,CAC3CK,MAAOA,EACPM,OAAQP,CAAO,CAAC,EAAE,AACtB,GAEMQ,EAAM,KAAK,CAACb,UAAUf,IAAI,CAAC,IAAI,CAAEgB,EAAQ,CAC3CK,MAAOA,EACPM,OAAQP,CAAO,CAAC,EAAE,AACtB,GAEA,GAAI,CAACM,GAAO,CAACE,EACT,OAEJ,IAAMC,EAAgBT,CAAO,CAAC,EAAE,CAAGA,CAAO,CAAC,EAAE,CAC7C,IAAKD,EAAI,EAAGA,EAAIS,EAAIJ,KAAK,CAACC,MAAM,CAAEN,IAC9BD,EAAaR,EAAa,AAACgB,CAAAA,EAAIF,KAAK,CAACL,EAAIU,EAAc,CACnDD,EAAIJ,KAAK,CAACL,EAAE,AAAD,EACXS,EAAIJ,KAAK,CAACL,EAAE,CACZ,KACJG,EAAIQ,IAAI,CAAC,CAACF,EAAIL,KAAK,CAACJ,EAAE,CAAED,EAAW,EACnCK,EAAMO,IAAI,CAACF,EAAIL,KAAK,CAACJ,EAAE,EACvBK,EAAMM,IAAI,CAACZ,GAEf,MAAO,CACHa,OAAQT,EACRC,MAAOA,EACPC,MAAOA,CACX,CACJ,CACJ,CAwBAV,EAAakB,cAAc,CAAGpB,EAAMJ,EAAawB,cAAc,CAAE,CAO7Df,OAAQ,CACJU,OAAQ,KAAK,EAObP,QAAS,CAAC,GAAI,GAAG,AACrB,CACJ,GACAT,EAAOG,EAAahB,SAAS,CAAE,CAC3BmC,SAAU,MACVC,eAAgB,CAAC,UAAU,AAC/B,GACA5B,IAA0I6B,kBAAkB,CAAC,MAAOrB,GAiCvI,IAAMZ,EAAYE,IAGrC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}