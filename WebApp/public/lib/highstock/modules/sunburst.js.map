{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/sunburst\n * @requires highcharts\n *\n * (c) 2016-2025 Highsoft AS\n * Authors: <AUTHORS>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Templating\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"SVGElement\"], root[\"_Highcharts\"][\"Series\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/sunburst\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"Templating\"],amd1[\"Color\"],amd1[\"SeriesRegistry\"],amd1[\"SVGElement\"],amd1[\"Series\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/sunburst\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Templating\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"SVGElement\"], root[\"_Highcharts\"][\"Series\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Templating\"], root[\"Highcharts\"][\"Color\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"SVGElement\"], root[\"Highcharts\"][\"Series\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__984__, __WEBPACK_EXTERNAL_MODULE__620__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__28__, __WEBPACK_EXTERNAL_MODULE__820__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 28:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__28__;\n\n/***/ }),\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 620:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__620__;\n\n/***/ }),\n\n/***/ 820:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__820__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ }),\n\n/***/ 984:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__984__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ sunburst_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Extensions/Breadcrumbs/BreadcrumbsDefaults.js\n/* *\n *\n *  Highcharts Breadcrumbs module\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n/**\n * @optionparent lang\n */\nconst lang = {\n    /**\n     * @since   10.0.0\n     * @product highcharts highmaps\n     *\n     * @private\n     */\n    mainBreadcrumb: 'Main'\n};\n/**\n * Options for breadcrumbs. Breadcrumbs general options are defined in\n * `navigation.breadcrumbs`. Specific options for drilldown are set in\n * `drilldown.breadcrumbs` and for tree-like series traversing, in\n * `plotOptions[series].breadcrumbs`.\n *\n * @since        10.0.0\n * @product      highcharts\n * @optionparent navigation.breadcrumbs\n */\nconst options = {\n    /**\n     * A collection of attributes for the buttons. The object takes SVG\n     * attributes like `fill`, `stroke`, `stroke-width`, as well as `style`,\n     * a collection of CSS properties for the text.\n     *\n     * The object can also be extended with states, so you can set\n     * presentational options for `hover`, `select` or `disabled` button\n     * states.\n     *\n     * @sample {highcharts} highcharts/breadcrumbs/single-button\n     *         Themed, single button\n     *\n     * @type    {Highcharts.SVGAttributes}\n     * @since   10.0.0\n     * @product highcharts\n     */\n    buttonTheme: {\n        /** @ignore */\n        fill: 'none',\n        /** @ignore */\n        height: 18,\n        /** @ignore */\n        padding: 2,\n        /** @ignore */\n        'stroke-width': 0,\n        /** @ignore */\n        zIndex: 7,\n        /** @ignore */\n        states: {\n            select: {\n                fill: 'none'\n            }\n        },\n        style: {\n            color: \"#334eff\" /* Palette.highlightColor80 */\n        }\n    },\n    /**\n     * The default padding for each button and separator in each direction.\n     *\n     * @type  {number}\n     * @since 10.0.0\n     */\n    buttonSpacing: 5,\n    /**\n     * Fires when clicking on the breadcrumbs button. Two arguments are\n     * passed to the function. First breadcrumb button as an SVG element.\n     * Second is the breadcrumbs class, containing reference to the chart,\n     * series etc.\n     *\n     * ```js\n     * click: function(button, breadcrumbs) {\n     *   console.log(button);\n     * }\n     * ```\n     *\n     * Return false to stop default buttons click action.\n     *\n     * @type      {Highcharts.BreadcrumbsClickCallbackFunction}\n     * @since     10.0.0\n     * @apioption navigation.breadcrumbs.events.click\n     */\n    /**\n     * When the breadcrumbs are floating, the plot area will not move to\n     * make space for it. By default, the chart will not make space for the\n     * buttons. This property won't work when positioned in the middle.\n     *\n     * @sample highcharts/breadcrumbs/single-button\n     *         Floating button\n     *\n     * @type  {boolean}\n     * @since 10.0.0\n     */\n    floating: false,\n    /**\n     * A format string for the breadcrumbs button. Variables are enclosed by\n     * curly brackets. Available values are passed in the declared point\n     * options.\n     *\n     * @type      {string|undefined}\n     * @since 10.0.0\n     * @default   undefined\n     * @sample {highcharts} highcharts/breadcrumbs/format Display custom\n     *          values in breadcrumb button.\n     */\n    format: void 0,\n    /**\n     * Callback function to format the breadcrumb text from scratch.\n     *\n     * @type      {Highcharts.BreadcrumbsFormatterCallbackFunction}\n     * @since     10.0.0\n     * @default   undefined\n     * @apioption navigation.breadcrumbs.formatter\n     */\n    /**\n     * What box to align the button to. Can be either `plotBox` or\n     * `spacingBox`.\n     *\n     * @type    {Highcharts.ButtonRelativeToValue}\n     * @default plotBox\n     * @since   10.0.0\n     * @product highcharts highmaps\n     */\n    relativeTo: 'plotBox',\n    /**\n     * Whether to reverse the order of buttons. This is common in Arabic\n     * and Hebrew.\n     *\n     * @sample {highcharts} highcharts/breadcrumbs/rtl\n     *         Breadcrumbs in RTL\n     *\n     * @type  {boolean}\n     * @since 10.2.0\n     */\n    rtl: false,\n    /**\n     * Positioning for the button row. The breadcrumbs buttons will be\n     * aligned properly for the default chart layout (title,  subtitle,\n     * legend, range selector) for the custom chart layout set the position\n     * properties.\n     *\n     * @sample  {highcharts} highcharts/breadcrumbs/single-button\n     *          Single, right aligned button\n     *\n     * @type    {Highcharts.BreadcrumbsAlignOptions}\n     * @since   10.0.0\n     * @product highcharts highmaps\n     */\n    position: {\n        /**\n         * Horizontal alignment of the breadcrumbs buttons.\n         *\n         * @type {Highcharts.AlignValue}\n         */\n        align: 'left',\n        /**\n         * Vertical alignment of the breadcrumbs buttons.\n         *\n         * @type {Highcharts.VerticalAlignValue}\n         */\n        verticalAlign: 'top',\n        /**\n         * The X offset of the breadcrumbs button group.\n         *\n         * @type {number}\n         */\n        x: 0,\n        /**\n         * The Y offset of the breadcrumbs button group. When `undefined`,\n         * and `floating` is `false`, the `y` position is adapted so that\n         * the breadcrumbs are rendered outside the target area.\n         *\n         * @type {number|undefined}\n         */\n        y: void 0\n    },\n    /**\n     * Options object for Breadcrumbs separator.\n     *\n     * @since 10.0.0\n     */\n    separator: {\n        /**\n         * @type    {string}\n         * @since   10.0.0\n         * @product highcharts\n         */\n        text: '/',\n        /**\n         * CSS styles for the breadcrumbs separator.\n         *\n         * In styled mode, the breadcrumbs separators are styled by the\n         * `.highcharts-separator` rule with its different states.\n         *  @type  {Highcharts.CSSObject}\n         *  @since 10.0.0\n         */\n        style: {\n            color: \"#666666\" /* Palette.neutralColor60 */,\n            fontSize: '0.8em'\n        }\n    },\n    /**\n     * Show full path or only a single button.\n     *\n     * @sample {highcharts} highcharts/breadcrumbs/single-button\n     *         Single, styled button\n     *\n     * @type  {boolean}\n     * @since 10.0.0\n     */\n    showFullPath: true,\n    /**\n     * CSS styles for all breadcrumbs.\n     *\n     * In styled mode, the breadcrumbs buttons are styled by the\n     * `.highcharts-breadcrumbs-buttons .highcharts-button` rule with its\n     * different states.\n     *\n     * @type  {Highcharts.SVGAttributes}\n     * @since 10.0.0\n     */\n    style: {},\n    /**\n     * Whether to use HTML to render the breadcrumbs items texts.\n     *\n     * @type  {boolean}\n     * @since 10.0.0\n     */\n    useHTML: false,\n    /**\n     * The z index of the breadcrumbs group.\n     *\n     * @type  {number}\n     * @since 10.0.0\n     */\n    zIndex: 7\n};\n/* *\n *\n *  Default Export\n *\n * */\nconst BreadcrumbsDefaults = {\n    lang,\n    options\n};\n/* harmony default export */ const Breadcrumbs_BreadcrumbsDefaults = (BreadcrumbsDefaults);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Templating\"],\"commonjs\":[\"highcharts\",\"Templating\"],\"commonjs2\":[\"highcharts\",\"Templating\"],\"root\":[\"Highcharts\",\"Templating\"]}\nvar highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_ = __webpack_require__(984);\nvar highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default = /*#__PURE__*/__webpack_require__.n(highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_);\n;// ./code/es-modules/Extensions/Breadcrumbs/Breadcrumbs.js\n/* *\n *\n *  Highcharts Breadcrumbs module\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { format } = (highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default());\n\nconst { composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent, defined, extend, fireEvent, isString, merge, objectEach, pick, pushUnique } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Shift the drillUpButton to make the space for resetZoomButton, #8095.\n * @private\n */\nfunction onChartAfterShowResetZoom() {\n    const chart = this;\n    if (chart.breadcrumbs) {\n        const bbox = chart.resetZoomButton &&\n            chart.resetZoomButton.getBBox(), breadcrumbsOptions = chart.breadcrumbs.options;\n        if (bbox &&\n            breadcrumbsOptions.position.align === 'right' &&\n            breadcrumbsOptions.relativeTo === 'plotBox') {\n            chart.breadcrumbs.alignBreadcrumbsGroup(-bbox.width - breadcrumbsOptions.buttonSpacing);\n        }\n    }\n}\n/**\n * Remove resize/afterSetExtremes at chart destroy.\n * @private\n */\nfunction onChartDestroy() {\n    if (this.breadcrumbs) {\n        this.breadcrumbs.destroy();\n        this.breadcrumbs = void 0;\n    }\n}\n/**\n * Logic for making space for the buttons above the plot area\n * @private\n */\nfunction onChartGetMargins() {\n    const breadcrumbs = this.breadcrumbs;\n    if (breadcrumbs &&\n        !breadcrumbs.options.floating &&\n        breadcrumbs.level) {\n        const breadcrumbsOptions = breadcrumbs.options, buttonTheme = breadcrumbsOptions.buttonTheme, breadcrumbsHeight = ((buttonTheme.height || 0) +\n            2 * (buttonTheme.padding || 0) +\n            breadcrumbsOptions.buttonSpacing), verticalAlign = breadcrumbsOptions.position.verticalAlign;\n        if (verticalAlign === 'bottom') {\n            this.marginBottom = (this.marginBottom || 0) + breadcrumbsHeight;\n            breadcrumbs.yOffset = breadcrumbsHeight;\n        }\n        else if (verticalAlign !== 'middle') {\n            this.plotTop += breadcrumbsHeight;\n            breadcrumbs.yOffset = -breadcrumbsHeight;\n        }\n        else {\n            breadcrumbs.yOffset = void 0;\n        }\n    }\n}\n/**\n * @private\n */\nfunction onChartRedraw() {\n    this.breadcrumbs && this.breadcrumbs.redraw();\n}\n/**\n * After zooming out, shift the drillUpButton to the previous position, #8095.\n * @private\n */\nfunction onChartSelection(event) {\n    if (event.resetSelection === true &&\n        this.breadcrumbs) {\n        this.breadcrumbs.alignBreadcrumbsGroup();\n    }\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Breadcrumbs class\n *\n * @private\n * @class\n * @name Highcharts.Breadcrumbs\n *\n * @param {Highcharts.Chart} chart\n *        Chart object\n * @param {Highcharts.Options} userOptions\n *        User options\n */\nclass Breadcrumbs {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    static compose(ChartClass, highchartsDefaultOptions) {\n        if (pushUnique(composed, 'Breadcrumbs')) {\n            addEvent(ChartClass, 'destroy', onChartDestroy);\n            addEvent(ChartClass, 'afterShowResetZoom', onChartAfterShowResetZoom);\n            addEvent(ChartClass, 'getMargins', onChartGetMargins);\n            addEvent(ChartClass, 'redraw', onChartRedraw);\n            addEvent(ChartClass, 'selection', onChartSelection);\n            // Add language support.\n            extend(highchartsDefaultOptions.lang, Breadcrumbs_BreadcrumbsDefaults.lang);\n        }\n    }\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(chart, userOptions) {\n        this.elementList = {};\n        this.isDirty = true;\n        this.level = 0;\n        this.list = [];\n        const chartOptions = merge(chart.options.drilldown &&\n            chart.options.drilldown.drillUpButton, Breadcrumbs.defaultOptions, chart.options.navigation && chart.options.navigation.breadcrumbs, userOptions);\n        this.chart = chart;\n        this.options = chartOptions || {};\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Update Breadcrumbs properties, like level and list.\n     *\n     * @function Highcharts.Breadcrumbs#updateProperties\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    updateProperties(list) {\n        this.setList(list);\n        this.setLevel();\n        this.isDirty = true;\n    }\n    /**\n     * Set breadcrumbs list.\n     * @function Highcharts.Breadcrumbs#setList\n     *\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     * @param {Highcharts.BreadcrumbsOptions} list\n     *        Breadcrumbs list.\n     */\n    setList(list) {\n        this.list = list;\n    }\n    /**\n     * Calculate level on which chart currently is.\n     *\n     * @function Highcharts.Breadcrumbs#setLevel\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    setLevel() {\n        this.level = this.list.length && this.list.length - 1;\n    }\n    /**\n     * Get Breadcrumbs level\n     *\n     * @function Highcharts.Breadcrumbs#getLevel\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    getLevel() {\n        return this.level;\n    }\n    /**\n     * Default button text formatter.\n     *\n     * @function Highcharts.Breadcrumbs#getButtonText\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     * @param {Highcharts.Breadcrumbs} breadcrumb\n     *        Breadcrumb.\n     * @return {string}\n     *         Formatted text.\n     */\n    getButtonText(breadcrumb) {\n        const breadcrumbs = this, chart = breadcrumbs.chart, breadcrumbsOptions = breadcrumbs.options, lang = chart.options.lang, textFormat = pick(breadcrumbsOptions.format, breadcrumbsOptions.showFullPath ?\n            '{level.name}' : '← {level.name}'), defaultText = lang && pick(lang.drillUpText, lang.mainBreadcrumb);\n        let returnText = breadcrumbsOptions.formatter &&\n            breadcrumbsOptions.formatter(breadcrumb) ||\n            format(textFormat, { level: breadcrumb.levelOptions }, chart) || '';\n        if (((isString(returnText) &&\n            !returnText.length) ||\n            returnText === '← ') &&\n            defined(defaultText)) {\n            returnText = !breadcrumbsOptions.showFullPath ?\n                '← ' + defaultText :\n                defaultText;\n        }\n        return returnText;\n    }\n    /**\n     * Redraw.\n     *\n     * @function Highcharts.Breadcrumbs#redraw\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    redraw() {\n        if (this.isDirty) {\n            this.render();\n        }\n        if (this.group) {\n            this.group.align();\n        }\n        this.isDirty = false;\n    }\n    /**\n     * Create a group, then draw breadcrumbs together with the separators.\n     *\n     * @function Highcharts.Breadcrumbs#render\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    render() {\n        const breadcrumbs = this, chart = breadcrumbs.chart, breadcrumbsOptions = breadcrumbs.options;\n        // A main group for the breadcrumbs.\n        if (!breadcrumbs.group && breadcrumbsOptions) {\n            breadcrumbs.group = chart.renderer\n                .g('breadcrumbs-group')\n                .addClass('highcharts-no-tooltip highcharts-breadcrumbs')\n                .attr({\n                zIndex: breadcrumbsOptions.zIndex\n            })\n                .add();\n        }\n        // Draw breadcrumbs.\n        if (breadcrumbsOptions.showFullPath) {\n            this.renderFullPathButtons();\n        }\n        else {\n            this.renderSingleButton();\n        }\n        this.alignBreadcrumbsGroup();\n    }\n    /**\n     * Draw breadcrumbs together with the separators.\n     *\n     * @function Highcharts.Breadcrumbs#renderFullPathButtons\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    renderFullPathButtons() {\n        // Make sure that only one type of button is visible.\n        this.destroySingleButton();\n        this.resetElementListState();\n        this.updateListElements();\n        this.destroyListElements();\n    }\n    /**\n     * Render Single button - when showFullPath is not used. The button is\n     * similar to the old drillUpButton\n     *\n     * @function Highcharts.Breadcrumbs#renderSingleButton\n     * @param {Highcharts.Breadcrumbs} this Breadcrumbs class.\n     */\n    renderSingleButton() {\n        const breadcrumbs = this, chart = breadcrumbs.chart, list = breadcrumbs.list, breadcrumbsOptions = breadcrumbs.options, buttonSpacing = breadcrumbsOptions.buttonSpacing;\n        // Make sure that only one type of button is visible.\n        this.destroyListElements();\n        // Draw breadcrumbs. Initial position for calculating the breadcrumbs\n        // group.\n        const posX = breadcrumbs.group ?\n            breadcrumbs.group.getBBox().width :\n            buttonSpacing, posY = buttonSpacing;\n        const previousBreadcrumb = list[list.length - 2];\n        if (!chart.drillUpButton && (this.level > 0)) {\n            chart.drillUpButton = breadcrumbs.renderButton(previousBreadcrumb, posX, posY);\n        }\n        else if (chart.drillUpButton) {\n            if (this.level > 0) {\n                // Update button.\n                this.updateSingleButton();\n            }\n            else {\n                this.destroySingleButton();\n            }\n        }\n    }\n    /**\n     * Update group position based on align and it's width.\n     *\n     * @function Highcharts.Breadcrumbs#renderSingleButton\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    alignBreadcrumbsGroup(xOffset) {\n        const breadcrumbs = this;\n        if (breadcrumbs.group) {\n            const breadcrumbsOptions = breadcrumbs.options, buttonTheme = breadcrumbsOptions.buttonTheme, positionOptions = breadcrumbsOptions.position, alignTo = (breadcrumbsOptions.relativeTo === 'chart' ||\n                breadcrumbsOptions.relativeTo === 'spacingBox' ?\n                void 0 :\n                'plotBox'), bBox = breadcrumbs.group.getBBox(), additionalSpace = 2 * (buttonTheme.padding || 0) +\n                breadcrumbsOptions.buttonSpacing;\n            // Store positionOptions\n            positionOptions.width = bBox.width + additionalSpace;\n            positionOptions.height = bBox.height + additionalSpace;\n            const newPositions = merge(positionOptions);\n            // Add x offset if specified.\n            if (xOffset) {\n                newPositions.x += xOffset;\n            }\n            if (breadcrumbs.options.rtl) {\n                newPositions.x += positionOptions.width;\n            }\n            newPositions.y = pick(newPositions.y, this.yOffset, 0);\n            breadcrumbs.group.align(newPositions, true, alignTo);\n        }\n    }\n    /**\n     * Render a button.\n     *\n     * @function Highcharts.Breadcrumbs#renderButton\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     * @param {Highcharts.Breadcrumbs} breadcrumb\n     *        Current breadcrumb\n     * @param {Highcharts.Breadcrumbs} posX\n     *        Initial horizontal position\n     * @param {Highcharts.Breadcrumbs} posY\n     *        Initial vertical position\n     * @return {SVGElement|void}\n     *        Returns the SVG button\n     */\n    renderButton(breadcrumb, posX, posY) {\n        const breadcrumbs = this, chart = this.chart, breadcrumbsOptions = breadcrumbs.options, buttonTheme = merge(breadcrumbsOptions.buttonTheme);\n        const button = chart.renderer\n            .button(breadcrumbs.getButtonText(breadcrumb), posX, posY, function (e) {\n            // Extract events from button object and call\n            const buttonEvents = breadcrumbsOptions.events &&\n                breadcrumbsOptions.events.click;\n            let callDefaultEvent;\n            if (buttonEvents) {\n                callDefaultEvent = buttonEvents.call(breadcrumbs, e, breadcrumb);\n            }\n            // (difference in behaviour of showFullPath and drillUp)\n            if (callDefaultEvent !== false) {\n                // For single button we are not going to the button\n                // level, but the one level up\n                if (!breadcrumbsOptions.showFullPath) {\n                    e.newLevel = breadcrumbs.level - 1;\n                }\n                else {\n                    e.newLevel = breadcrumb.level;\n                }\n                fireEvent(breadcrumbs, 'up', e);\n            }\n        }, buttonTheme)\n            .addClass('highcharts-breadcrumbs-button')\n            .add(breadcrumbs.group);\n        if (!chart.styledMode) {\n            button.attr(breadcrumbsOptions.style);\n        }\n        return button;\n    }\n    /**\n     * Render a separator.\n     *\n     * @function Highcharts.Breadcrumbs#renderSeparator\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     * @param {Highcharts.Breadcrumbs} posX\n     *        Initial horizontal position\n     * @param {Highcharts.Breadcrumbs} posY\n     *        Initial vertical position\n     * @return {Highcharts.SVGElement}\n     *        Returns the SVG button\n     */\n    renderSeparator(posX, posY) {\n        const breadcrumbs = this, chart = this.chart, breadcrumbsOptions = breadcrumbs.options, separatorOptions = breadcrumbsOptions.separator;\n        const separator = chart.renderer\n            .label(separatorOptions.text, posX, posY, void 0, void 0, void 0, false)\n            .addClass('highcharts-breadcrumbs-separator')\n            .add(breadcrumbs.group);\n        if (!chart.styledMode) {\n            separator.css(separatorOptions.style);\n        }\n        return separator;\n    }\n    /**\n     * Update.\n     * @function Highcharts.Breadcrumbs#update\n     *\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     * @param {Highcharts.BreadcrumbsOptions} options\n     *        Breadcrumbs class.\n     * @param {boolean} redraw\n     *        Redraw flag\n     */\n    update(options) {\n        merge(true, this.options, options);\n        this.destroy();\n        this.isDirty = true;\n    }\n    /**\n     * Update button text when the showFullPath set to false.\n     * @function Highcharts.Breadcrumbs#updateSingleButton\n     *\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    updateSingleButton() {\n        const chart = this.chart, currentBreadcrumb = this.list[this.level - 1];\n        if (chart.drillUpButton) {\n            chart.drillUpButton.attr({\n                text: this.getButtonText(currentBreadcrumb)\n            });\n        }\n    }\n    /**\n     * Destroy the chosen breadcrumbs group\n     *\n     * @function Highcharts.Breadcrumbs#destroy\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    destroy() {\n        this.destroySingleButton();\n        // Destroy elements one by one. It's necessary because\n        // g().destroy() does not remove added HTML\n        this.destroyListElements(true);\n        // Then, destroy the group itself.\n        if (this.group) {\n            this.group.destroy();\n        }\n        this.group = void 0;\n    }\n    /**\n     * Destroy the elements' buttons and separators.\n     *\n     * @function Highcharts.Breadcrumbs#destroyListElements\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    destroyListElements(force) {\n        const elementList = this.elementList;\n        objectEach(elementList, (element, level) => {\n            if (force ||\n                !elementList[level].updated) {\n                element = elementList[level];\n                element.button && element.button.destroy();\n                element.separator && element.separator.destroy();\n                delete element.button;\n                delete element.separator;\n                delete elementList[level];\n            }\n        });\n        if (force) {\n            this.elementList = {};\n        }\n    }\n    /**\n     * Destroy the single button if exists.\n     *\n     * @function Highcharts.Breadcrumbs#destroySingleButton\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    destroySingleButton() {\n        if (this.chart.drillUpButton) {\n            this.chart.drillUpButton.destroy();\n            this.chart.drillUpButton = void 0;\n        }\n    }\n    /**\n     * Reset state for all buttons in elementList.\n     *\n     * @function Highcharts.Breadcrumbs#resetElementListState\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    resetElementListState() {\n        objectEach(this.elementList, (element) => {\n            element.updated = false;\n        });\n    }\n    /**\n     * Update rendered elements inside the elementList.\n     *\n     * @function Highcharts.Breadcrumbs#updateListElements\n     *\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    updateListElements() {\n        const breadcrumbs = this, elementList = breadcrumbs.elementList, buttonSpacing = breadcrumbs.options.buttonSpacing, posY = buttonSpacing, list = breadcrumbs.list, rtl = breadcrumbs.options.rtl, rtlFactor = rtl ? -1 : 1, updateXPosition = function (element, spacing) {\n            return rtlFactor * element.getBBox().width +\n                rtlFactor * spacing;\n        }, adjustToRTL = function (element, posX, posY) {\n            element.translate(posX - element.getBBox().width, posY);\n        };\n        // Initial position for calculating the breadcrumbs group.\n        let posX = breadcrumbs.group ?\n            updateXPosition(breadcrumbs.group, buttonSpacing) :\n            buttonSpacing, currentBreadcrumb, breadcrumb;\n        for (let i = 0, iEnd = list.length; i < iEnd; ++i) {\n            const isLast = i === iEnd - 1;\n            let button, separator;\n            breadcrumb = list[i];\n            if (elementList[breadcrumb.level]) {\n                currentBreadcrumb = elementList[breadcrumb.level];\n                button = currentBreadcrumb.button;\n                // Render a separator if it was not created before.\n                if (!currentBreadcrumb.separator &&\n                    !isLast) {\n                    // Add spacing for the next separator\n                    posX += rtlFactor * buttonSpacing;\n                    currentBreadcrumb.separator =\n                        breadcrumbs.renderSeparator(posX, posY);\n                    if (rtl) {\n                        adjustToRTL(currentBreadcrumb.separator, posX, posY);\n                    }\n                    posX += updateXPosition(currentBreadcrumb.separator, buttonSpacing);\n                }\n                else if (currentBreadcrumb.separator &&\n                    isLast) {\n                    currentBreadcrumb.separator.destroy();\n                    delete currentBreadcrumb.separator;\n                }\n                elementList[breadcrumb.level].updated = true;\n            }\n            else {\n                // Render a button.\n                button = breadcrumbs.renderButton(breadcrumb, posX, posY);\n                if (rtl) {\n                    adjustToRTL(button, posX, posY);\n                }\n                posX += updateXPosition(button, buttonSpacing);\n                // Render a separator.\n                if (!isLast) {\n                    separator = breadcrumbs.renderSeparator(posX, posY);\n                    if (rtl) {\n                        adjustToRTL(separator, posX, posY);\n                    }\n                    posX += updateXPosition(separator, buttonSpacing);\n                }\n                elementList[breadcrumb.level] = {\n                    button,\n                    separator,\n                    updated: true\n                };\n            }\n            if (button) {\n                button.setState(isLast ? 2 : 0);\n            }\n        }\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nBreadcrumbs.defaultOptions = Breadcrumbs_BreadcrumbsDefaults.options;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Breadcrumbs_Breadcrumbs = (Breadcrumbs);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Callback function to react on button clicks.\n *\n * @callback Highcharts.BreadcrumbsClickCallbackFunction\n *\n * @param {Highcharts.Event} event\n * Event.\n *\n * @param {Highcharts.BreadcrumbOptions} options\n * Breadcrumb options.\n *\n * @param {global.Event} e\n * Event arguments.\n */\n/**\n * Callback function to format the breadcrumb text from scratch.\n *\n * @callback Highcharts.BreadcrumbsFormatterCallbackFunction\n *\n * @param {Highcharts.BreadcrumbOptions} options\n * Breadcrumb options.\n *\n * @return {string}\n * Formatted text or false\n */\n/**\n * Options for the one breadcrumb.\n *\n * @interface Highcharts.BreadcrumbOptions\n */\n/**\n * Level connected to a specific breadcrumb.\n * @name Highcharts.BreadcrumbOptions#level\n * @type {number}\n */\n/**\n * Options for series or point connected to a specific breadcrumb.\n * @name Highcharts.BreadcrumbOptions#levelOptions\n * @type {SeriesOptions|PointOptionsObject}\n */\n/**\n * Options for aligning breadcrumbs group.\n *\n * @interface Highcharts.BreadcrumbsAlignOptions\n */\n/**\n * Align of a Breadcrumb group.\n * @default right\n * @name Highcharts.BreadcrumbsAlignOptions#align\n * @type {AlignValue}\n */\n/**\n * Vertical align of a Breadcrumb group.\n * @default top\n * @name Highcharts.BreadcrumbsAlignOptions#verticalAlign\n * @type {VerticalAlignValue}\n */\n/**\n * X offset of a Breadcrumbs group.\n * @name Highcharts.BreadcrumbsAlignOptions#x\n * @type {number}\n */\n/**\n * Y offset of a Breadcrumbs group.\n * @name Highcharts.BreadcrumbsAlignOptions#y\n * @type {number}\n */\n/**\n * Options for all breadcrumbs.\n *\n * @interface Highcharts.BreadcrumbsOptions\n */\n/**\n * Button theme.\n * @name Highcharts.BreadcrumbsOptions#buttonTheme\n * @type { SVGAttributes | undefined }\n */\n(''); // Keeps doclets above in JS file\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Color\"],\"commonjs\":[\"highcharts\",\"Color\"],\"commonjs2\":[\"highcharts\",\"Color\"],\"root\":[\"Highcharts\",\"Color\"]}\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_ = __webpack_require__(620);\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default = /*#__PURE__*/__webpack_require__.n(highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SVGElement\"],\"commonjs\":[\"highcharts\",\"SVGElement\"],\"commonjs2\":[\"highcharts\",\"SVGElement\"],\"root\":[\"Highcharts\",\"SVGElement\"]}\nvar highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_ = __webpack_require__(28);\nvar highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default = /*#__PURE__*/__webpack_require__.n(highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_);\n;// ./code/es-modules/Series/ColorMapComposition.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { column: { prototype: columnProto } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\n\nconst { addEvent: ColorMapComposition_addEvent, defined: ColorMapComposition_defined } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\nvar ColorMapComposition;\n(function (ColorMapComposition) {\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    ColorMapComposition.pointMembers = {\n        dataLabelOnNull: true,\n        moveToTopOnHover: true,\n        isValid: pointIsValid\n    };\n    ColorMapComposition.seriesMembers = {\n        colorKey: 'value',\n        axisTypes: ['xAxis', 'yAxis', 'colorAxis'],\n        parallelArrays: ['x', 'y', 'value'],\n        pointArrayMap: ['value'],\n        trackerGroups: ['group', 'markerGroup', 'dataLabelsGroup'],\n        colorAttribs: seriesColorAttribs,\n        pointAttribs: columnProto.pointAttribs\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    function compose(SeriesClass) {\n        const PointClass = SeriesClass.prototype.pointClass;\n        ColorMapComposition_addEvent(PointClass, 'afterSetState', onPointAfterSetState);\n        return SeriesClass;\n    }\n    ColorMapComposition.compose = compose;\n    /**\n     * Move points to the top of the z-index order when hovered.\n     * @private\n     */\n    function onPointAfterSetState(e) {\n        const point = this, series = point.series, renderer = series.chart.renderer;\n        if (point.moveToTopOnHover && point.graphic) {\n            if (!series.stateMarkerGraphic) {\n                // Create a `use` element and add it to the end of the group,\n                // which would make it appear on top of the other elements. This\n                // deals with z-index without reordering DOM elements (#13049).\n                series.stateMarkerGraphic = new (highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default())(renderer, 'use')\n                    .css({\n                    pointerEvents: 'none'\n                })\n                    .add(point.graphic.parentGroup);\n            }\n            if (e?.state === 'hover') {\n                // Give the graphic DOM element the same id as the Point\n                // instance\n                point.graphic.attr({\n                    id: this.id\n                });\n                series.stateMarkerGraphic.attr({\n                    href: `${renderer.url}#${this.id}`,\n                    visibility: 'visible'\n                });\n            }\n            else {\n                series.stateMarkerGraphic.attr({\n                    href: ''\n                });\n            }\n        }\n    }\n    /**\n     * Color points have a value option that determines whether or not it is\n     * a null point\n     * @private\n     */\n    function pointIsValid() {\n        return (this.value !== null &&\n            this.value !== Infinity &&\n            this.value !== -Infinity &&\n            // Undefined is allowed, but NaN is not (#17279)\n            (this.value === void 0 || !isNaN(this.value)));\n    }\n    /**\n     * Get the color attributes to apply on the graphic\n     * @private\n     * @function Highcharts.colorMapSeriesMixin.colorAttribs\n     * @param {Highcharts.Point} point\n     * @return {Highcharts.SVGAttributes}\n     *         The SVG attributes\n     */\n    function seriesColorAttribs(point) {\n        const ret = {};\n        if (ColorMapComposition_defined(point.color) &&\n            (!point.state || point.state === 'normal') // #15746\n        ) {\n            ret[this.colorProp || 'fill'] = point.color;\n        }\n        return ret;\n    }\n})(ColorMapComposition || (ColorMapComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Series_ColorMapComposition = (ColorMapComposition);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Series\"],\"commonjs\":[\"highcharts\",\"Series\"],\"commonjs2\":[\"highcharts\",\"Series\"],\"root\":[\"Highcharts\",\"Series\"]}\nvar highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_ = __webpack_require__(820);\nvar highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default = /*#__PURE__*/__webpack_require__.n(highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_);\n;// ./code/es-modules/Series/Treemap/TreemapAlgorithmGroup.js\n/* *\n *\n *  (c) 2014-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Class\n *\n * */\nclass TreemapAlgorithmGroup {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(h, w, d, p) {\n        this.height = h;\n        this.width = w;\n        this.plot = p;\n        this.direction = d;\n        this.startDirection = d;\n        this.total = 0;\n        this.nW = 0;\n        this.lW = 0;\n        this.nH = 0;\n        this.lH = 0;\n        this.elArr = [];\n        this.lP = {\n            total: 0,\n            lH: 0,\n            nH: 0,\n            lW: 0,\n            nW: 0,\n            nR: 0,\n            lR: 0,\n            aspectRatio: function (w, h) {\n                return Math.max((w / h), (h / w));\n            }\n        };\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    addElement(el) {\n        this.lP.total = this.elArr[this.elArr.length - 1];\n        this.total = this.total + el;\n        if (this.direction === 0) {\n            // Calculate last point old aspect ratio\n            this.lW = this.nW;\n            this.lP.lH = this.lP.total / this.lW;\n            this.lP.lR = this.lP.aspectRatio(this.lW, this.lP.lH);\n            // Calculate last point new aspect ratio\n            this.nW = this.total / this.height;\n            this.lP.nH = this.lP.total / this.nW;\n            this.lP.nR = this.lP.aspectRatio(this.nW, this.lP.nH);\n        }\n        else {\n            // Calculate last point old aspect ratio\n            this.lH = this.nH;\n            this.lP.lW = this.lP.total / this.lH;\n            this.lP.lR = this.lP.aspectRatio(this.lP.lW, this.lH);\n            // Calculate last point new aspect ratio\n            this.nH = this.total / this.width;\n            this.lP.nW = this.lP.total / this.nH;\n            this.lP.nR = this.lP.aspectRatio(this.lP.nW, this.nH);\n        }\n        this.elArr.push(el);\n    }\n    reset() {\n        this.nW = 0;\n        this.lW = 0;\n        this.elArr = [];\n        this.total = 0;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treemap_TreemapAlgorithmGroup = (TreemapAlgorithmGroup);\n\n;// ./code/es-modules/Series/Treemap/TreemapNode.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Class\n *\n * */\nclass TreemapNode {\n    constructor() {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.childrenTotal = 0;\n        this.visible = false;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    init(id, i, children, height, level, series, parent) {\n        this.id = id;\n        this.i = i;\n        this.children = children;\n        this.height = height;\n        this.level = level;\n        this.series = series;\n        this.parent = parent;\n        return this;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treemap_TreemapNode = (TreemapNode);\n\n;// ./code/es-modules/Series/DrawPointUtilities.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Handles the drawing of a component.\n * Can be used for any type of component that reserves the graphic property,\n * and provides a shouldDraw on its context.\n *\n * @private\n *\n * @todo add type checking.\n * @todo export this function to enable usage\n */\nfunction draw(point, params) {\n    const { animatableAttribs, onComplete, css, renderer } = params;\n    const animation = (point.series && point.series.chart.hasRendered) ?\n        // Chart-level animation on updates\n        void 0 :\n        // Series-level animation on new points\n        (point.series &&\n            point.series.options.animation);\n    let graphic = point.graphic;\n    params.attribs = {\n        ...params.attribs,\n        'class': point.getClassName()\n    } || {};\n    if ((point.shouldDraw())) {\n        if (!graphic) {\n            if (params.shapeType === 'text') {\n                graphic = renderer.text();\n            }\n            else if (params.shapeType === 'image') {\n                graphic = renderer.image(params.imageUrl || '')\n                    .attr(params.shapeArgs || {});\n            }\n            else {\n                graphic = renderer[params.shapeType](params.shapeArgs || {});\n            }\n            point.graphic = graphic;\n            graphic.add(params.group);\n        }\n        if (css) {\n            graphic.css(css);\n        }\n        graphic\n            .attr(params.attribs)\n            .animate(animatableAttribs, params.isNew ? false : animation, onComplete);\n    }\n    else if (graphic) {\n        const destroy = () => {\n            point.graphic = graphic = (graphic && graphic.destroy());\n            if (typeof onComplete === 'function') {\n                onComplete();\n            }\n        };\n        // Animate only runs complete callback if something was animated.\n        if (Object.keys(animatableAttribs).length) {\n            graphic.animate(animatableAttribs, void 0, () => destroy());\n        }\n        else {\n            destroy();\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst DrawPointUtilities = {\n    draw\n};\n/* harmony default export */ const Series_DrawPointUtilities = (DrawPointUtilities);\n\n;// ./code/es-modules/Series/Treemap/TreemapPoint.js\n/* *\n *\n *  (c) 2014-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { pie: { prototype: { pointClass: PiePoint } }, scatter: { prototype: { pointClass: ScatterPoint } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: TreemapPoint_extend, isNumber, pick: TreemapPoint_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass TreemapPoint extends ScatterPoint {\n    constructor() {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        super(...arguments);\n        this.groupedPointsAmount = 0;\n        this.shapeType = 'rect';\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    draw(params) {\n        Series_DrawPointUtilities.draw(this, params);\n    }\n    getClassName() {\n        const series = this.series, options = series.options;\n        let className = super.getClassName();\n        // Above the current level\n        if (this.node.level <= series.nodeMap[series.rootNode].level &&\n            this.node.children.length) {\n            className += ' highcharts-above-level';\n        }\n        else if (!this.node.isGroup &&\n            !this.node.isLeaf &&\n            !series.nodeMap[series.rootNode].isGroup &&\n            !TreemapPoint_pick(options.interactByLeaf, !options.allowTraversingTree)) {\n            className += ' highcharts-internal-node-interactive';\n        }\n        else if (!this.node.isGroup &&\n            !this.node.isLeaf &&\n            !series.nodeMap[series.rootNode].isGroup) {\n            className += ' highcharts-internal-node';\n        }\n        return className;\n    }\n    /**\n     * A tree point is valid if it has han id too, assume it may be a parent\n     * item.\n     *\n     * @private\n     * @function Highcharts.Point#isValid\n     */\n    isValid() {\n        return Boolean(this.id || isNumber(this.value));\n    }\n    setState(state) {\n        super.setState.apply(this, arguments);\n        // Graphic does not exist when point is not visible.\n        if (this.graphic) {\n            this.graphic.attr({\n                zIndex: state === 'hover' ? 1 : 0\n            });\n        }\n    }\n    shouldDraw() {\n        return isNumber(this.plotY) && this.y !== null;\n    }\n}\nTreemapPoint_extend(TreemapPoint.prototype, {\n    setVisible: PiePoint.prototype.setVisible\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treemap_TreemapPoint = (TreemapPoint);\n\n;// ./code/es-modules/Series/Treemap/TreemapSeriesDefaults.js\n/* *\n *\n *  (c) 2014-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { isString: TreemapSeriesDefaults_isString } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A treemap displays hierarchical data using nested rectangles. The data\n * can be laid out in varying ways depending on options.\n *\n * @sample highcharts/demo/treemap-large-dataset/\n *         Treemap\n *\n * @extends      plotOptions.scatter\n * @excluding    connectEnds, connectNulls, dataSorting, dragDrop, jitter, marker\n * @product      highcharts\n * @requires     modules/treemap\n * @optionparent plotOptions.treemap\n */\nconst TreemapSeriesDefaults = {\n    /**\n     * When enabled the user can click on a point which is a parent and\n     * zoom in on its children. Deprecated and replaced by\n     * [allowTraversingTree](#plotOptions.treemap.allowTraversingTree).\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-allowdrilltonode/\n     *         Enabled\n     *\n     * @deprecated\n     * @type      {boolean}\n     * @default   false\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.allowDrillToNode\n     */\n    /**\n     * When enabled the user can click on a point which is a parent and\n     * zoom in on its children.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-allowtraversingtree/\n     *         Enabled\n     * @sample {highcharts} highcharts/plotoptions/treemap-grouping-traversing/\n     *         Traversing to Grouped Points node\n     *\n     * @since     7.0.3\n     * @product   highcharts\n     */\n    allowTraversingTree: false,\n    animationLimit: 250,\n    /**\n     * The border radius for each treemap item.\n     */\n    borderRadius: 0,\n    /**\n     * Options for the breadcrumbs, the navigation at the top leading the\n     * way up through the traversed levels.\n     *\n     *\n     * @since 10.0.0\n     * @product   highcharts\n     * @extends   navigation.breadcrumbs\n     * @apioption plotOptions.treemap.breadcrumbs\n     */\n    /**\n     * When the series contains less points than the crop threshold, all\n     * points are drawn, event if the points fall outside the visible plot\n     * area at the current zoom. The advantage of drawing all points\n     * (including markers and columns), is that animation is performed on\n     * updates. On the other hand, when the series contains more points than\n     * the crop threshold, the series data is cropped to only contain points\n     * that fall within the plot area. The advantage of cropping away\n     * invisible points is to increase performance on large series.\n     *\n     * @type      {number}\n     * @default   300\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.cropThreshold\n     */\n    /**\n     * Fires on a request for change of root node for the tree, before the\n     * update is made. An event object is passed to the function, containing\n     * additional properties `newRootId`, `previousRootId`, `redraw` and\n     * `trigger`.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-events-setrootnode/\n     *         Alert update information on setRootNode event.\n     *\n     * @type {Function}\n     * @default undefined\n     * @since 7.0.3\n     * @product highcharts\n     * @apioption plotOptions.treemap.events.setRootNode\n     */\n    /**\n     * This option decides if the user can interact with the parent nodes\n     * or just the leaf nodes. When this option is undefined, it will be\n     * true by default. However when allowTraversingTree is true, then it\n     * will be false by default.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-interactbyleaf-false/\n     *         False\n     * @sample {highcharts} highcharts/plotoptions/treemap-interactbyleaf-true-and-allowtraversingtree/\n     *         InteractByLeaf and allowTraversingTree is true\n     *\n     * @type      {boolean}\n     * @since     4.1.2\n     * @product   highcharts\n     * @apioption plotOptions.treemap.interactByLeaf\n     */\n    /**\n     * The sort index of the point inside the treemap level.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-sortindex/\n     *         Sort by years\n     *\n     * @type      {number}\n     * @since     4.1.10\n     * @product   highcharts\n     * @apioption plotOptions.treemap.sortIndex\n     */\n    /**\n     * A series specific or series type specific color set to apply instead\n     * of the global [colors](#colors) when\n     * [colorByPoint](#plotOptions.treemap.colorByPoint) is true.\n     *\n     * @type      {Array<Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject>}\n     * @since     3.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.colors\n     */\n    /**\n     * Whether to display this series type or specific series item in the\n     * legend.\n     */\n    showInLegend: false,\n    /**\n     * @ignore-option\n     */\n    marker: void 0,\n    /**\n     * When using automatic point colors pulled from the `options.colors`\n     * collection, this option determines whether the chart should receive\n     * one color per series or one color per point.\n     *\n     * @see [series colors](#plotOptions.treemap.colors)\n     *\n     * @since     2.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.colorByPoint\n     */\n    colorByPoint: false,\n    /**\n     * @since 4.1.0\n     */\n    dataLabels: {\n        enabled: true,\n        formatter: function () {\n            const point = this && this.point ?\n                this.point :\n                {}, name = TreemapSeriesDefaults_isString(point.name) ? point.name : '';\n            return name;\n        },\n        /**\n         * Whether the data label should act as a group-level header. For leaf\n         * nodes, headers are not supported and the data label will be rendered\n         * inside.\n         *\n         * @sample {highcharts} highcharts/series-treemap/headers\n         *         Headers for parent nodes\n         *\n         * @since 12.2.0\n         */\n        headers: false,\n        inside: true,\n        padding: 2,\n        verticalAlign: 'middle',\n        style: {\n            textOverflow: 'ellipsis'\n        }\n    },\n    tooltip: {\n        headerFormat: '',\n        pointFormat: '<b>{point.name}</b>: {point.value}<br/>',\n        /**\n         * The HTML of the grouped point's nodes in the tooltip. Works only for\n         * Treemap series grouping and analogously to\n         * [pointFormat](#tooltip.pointFormat).\n         *\n         * The grouped nodes point tooltip can be also formatted using\n         * `tooltip.formatter` callback function and `point.isGroupNode` flag.\n         *\n         * @type      {string}\n         * @default   '+ {point.groupedPointsAmount} more...'\n         * @apioption tooltip.clusterFormat\n         */\n        clusterFormat: '+ {point.groupedPointsAmount} more...<br/>'\n    },\n    /**\n     * Whether to ignore hidden points when the layout algorithm runs.\n     * If `false`, hidden points will leave open spaces.\n     *\n     * @since 5.0.8\n     */\n    ignoreHiddenPoint: true,\n    /**\n     * This option decides which algorithm is used for setting position\n     * and dimensions of the points.\n     *\n     * @see [How to write your own algorithm](https://www.highcharts.com/docs/chart-and-series-types/treemap)\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-layoutalgorithm-sliceanddice/\n     *         SliceAndDice by default\n     * @sample {highcharts} highcharts/plotoptions/treemap-layoutalgorithm-stripes/\n     *         Stripes\n     * @sample {highcharts} highcharts/plotoptions/treemap-layoutalgorithm-squarified/\n     *         Squarified\n     * @sample {highcharts} highcharts/plotoptions/treemap-layoutalgorithm-strip/\n     *         Strip\n     *\n     * @since      4.1.0\n     * @validvalue [\"sliceAndDice\", \"stripes\", \"squarified\", \"strip\"]\n     */\n    layoutAlgorithm: 'sliceAndDice',\n    /**\n     * Defines which direction the layout algorithm will start drawing.\n     *\n     * @since       4.1.0\n     * @validvalue [\"vertical\", \"horizontal\"]\n     */\n    layoutStartingDirection: 'vertical',\n    /**\n     * Enabling this option will make the treemap alternate the drawing\n     * direction between vertical and horizontal. The next levels starting\n     * direction will always be the opposite of the previous.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-alternatestartingdirection-true/\n     *         Enabled\n     *\n     * @since 4.1.0\n     */\n    alternateStartingDirection: false,\n    /**\n     * Used together with the levels and allowTraversingTree options. When\n     * set to false the first level visible to be level one, which is\n     * dynamic when traversing the tree. Otherwise the level will be the\n     * same as the tree structure.\n     *\n     * @since 4.1.0\n     */\n    levelIsConstant: true,\n    /**\n     * Options for the button appearing when traversing down in a treemap.\n     *\n     * Since v9.3.3 the `traverseUpButton` is replaced by `breadcrumbs`.\n     *\n     * @deprecated\n     */\n    traverseUpButton: {\n        /**\n         * The position of the button.\n         */\n        position: {\n            /**\n             * Vertical alignment of the button.\n             *\n             * @type      {Highcharts.VerticalAlignValue}\n             * @default   top\n             * @product   highcharts\n             * @apioption plotOptions.treemap.traverseUpButton.position.verticalAlign\n             */\n            /**\n             * Horizontal alignment of the button.\n             *\n             * @type {Highcharts.AlignValue}\n             */\n            align: 'right',\n            /**\n             * Horizontal offset of the button.\n             */\n            x: -10,\n            /**\n             * Vertical offset of the button.\n             */\n            y: 10\n        }\n    },\n    /**\n     * Group padding for parent elements in terms of pixels. See also the\n     * `nodeSizeBy` option that controls how the leaf nodes' size is affected by\n     * the padding.\n     *\n     * @sample    {highcharts} highcharts/series-treemap/grouppadding/\n     *            Group padding\n     * @type      {number}\n     * @since 12.2.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.groupPadding\n     */\n    /**\n     * Set options on specific levels. Takes precedence over series options,\n     * but not point options.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-levels/\n     *         Styling dataLabels and borders\n     * @sample {highcharts} highcharts/demo/treemap-with-levels/\n     *         Different layoutAlgorithm\n     *\n     * @type      {Array<*>}\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels\n     */\n    /**\n     * Experimental. How to set the size of child nodes when a header or padding\n     * is present. When `leaf`, the group is expanded to make room for headers\n     * and padding in order to preserve the relative sizes between leaves. When\n     * `group`, the leaves are naïvely fit into the remaining area after the\n     * header and padding are subtracted.\n     *\n     * @sample    {highcharts} highcharts/series-treemap/nodesizeby/\n     *            Node sizing\n     * @since 12.2.0\n     * @type      {string}\n     * @validvalue [\"group\", \"leaf\"]\n     * @default   group\n     * @apioption plotOptions.treemap.nodeSizeBy\n     */\n    /**\n     * Can set a `borderColor` on all points which lies on the same level.\n     *\n     * @type      {Highcharts.ColorString}\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels.borderColor\n     */\n    /**\n     * Set the dash style of the border of all the point which lies on the\n     * level. See\n     * [plotOptions.scatter.dashStyle](#plotoptions.scatter.dashstyle)\n     * for possible options.\n     *\n     * @type      {Highcharts.DashStyleValue}\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels.borderDashStyle\n     */\n    /**\n     * Can set the borderWidth on all points which lies on the same level.\n     *\n     * @type      {number}\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels.borderWidth\n     */\n    /**\n     * Can set a color on all points which lies on the same level.\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels.color\n     */\n    /**\n     * A configuration object to define how the color of a child varies from\n     * the parent's color. The variation is distributed among the children\n     * of node. For example when setting brightness, the brightness change\n     * will range from the parent's original brightness on the first child,\n     * to the amount set in the `to` setting on the last node. This allows a\n     * gradient-like color scheme that sets children out from each other\n     * while highlighting the grouping on treemaps and sectors on sunburst\n     * charts.\n     *\n     * @sample highcharts/demo/sunburst/\n     *         Sunburst with color variation\n     *\n     * @sample highcharts/series-treegraph/color-variation\n     *         Treegraph nodes with color variation\n     *\n     * @since     6.0.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels.colorVariation\n     */\n    /**\n     * The key of a color variation. Currently supports `brightness` only.\n     *\n     * @type       {string}\n     * @since      6.0.0\n     * @product    highcharts\n     * @validvalue [\"brightness\"]\n     * @apioption  plotOptions.treemap.levels.colorVariation.key\n     */\n    /**\n     * The ending value of a color variation. The last sibling will receive\n     * this value.\n     *\n     * @type      {number}\n     * @since     6.0.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels.colorVariation.to\n     */\n    /**\n     * Can set the options of dataLabels on each point which lies on the\n     * level.\n     * [plotOptions.treemap.dataLabels](#plotOptions.treemap.dataLabels) for\n     * possible values.\n     *\n     * @extends   plotOptions.treemap.dataLabels\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels.dataLabels\n     */\n    /**\n     * Can set the layoutAlgorithm option on a specific level.\n     *\n     * @type       {string}\n     * @since      4.1.0\n     * @product    highcharts\n     * @validvalue [\"sliceAndDice\", \"stripes\", \"squarified\", \"strip\"]\n     * @apioption  plotOptions.treemap.levels.layoutAlgorithm\n     */\n    /**\n     * Can set the layoutStartingDirection option on a specific level.\n     *\n     * @type       {string}\n     * @since      4.1.0\n     * @product    highcharts\n     * @validvalue [\"vertical\", \"horizontal\"]\n     * @apioption  plotOptions.treemap.levels.layoutStartingDirection\n     */\n    /**\n     * Decides which level takes effect from the options set in the levels\n     * object.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-levels/\n     *         Styling of both levels\n     *\n     * @type      {number}\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels.level\n     */\n    // Presentational options\n    /**\n     * The color of the border surrounding each tree map item.\n     *\n     * @type {Highcharts.ColorString}\n     */\n    borderColor: \"#e6e6e6\" /* Palette.neutralColor10 */,\n    /**\n     * The width of the border surrounding each tree map item.\n     */\n    borderWidth: 1,\n    colorKey: 'colorValue',\n    /**\n     * The opacity of grouped points in treemap. When a point has children, the\n     * group point is covering the children, and is given this opacity. The\n     * visibility of the children is determined by the opacity.\n     *\n     * @since 4.2.4\n     */\n    opacity: 0.15,\n    /**\n     * A wrapper object for all the series options in specific states.\n     *\n     * @extends plotOptions.heatmap.states\n     */\n    states: {\n        /**\n         * Options for the hovered series\n         *\n         * @extends   plotOptions.heatmap.states.hover\n         * @excluding halo\n         */\n        hover: {\n            /**\n             * The border color for the hovered state.\n             */\n            borderColor: \"#999999\" /* Palette.neutralColor40 */,\n            /**\n             * Brightness for the hovered point. Defaults to 0 if the\n             * heatmap series is loaded first, otherwise 0.1.\n             *\n             * @type    {number}\n             * @default undefined\n             */\n            brightness: (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.heatmap ? 0 : 0.1,\n            /**\n             * @extends plotOptions.heatmap.states.hover.halo\n             */\n            halo: false,\n            /**\n             * The opacity of a point in treemap. When a point has children,\n             * the visibility of the children is determined by the opacity.\n             *\n             * @since 4.2.4\n             */\n            opacity: 0.75,\n            /**\n             * The shadow option for hovered state.\n             */\n            shadow: false\n        }\n    },\n    legendSymbol: 'rectangle',\n    /**\n     * This option enables automatic traversing to the last child level upon\n     * node interaction. This feature simplifies navigation by immediately\n     * focusing on the deepest layer of the data structure without intermediate\n     * steps.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-traverse-to-leaf/\n     *         Traverse to leaf enabled\n     *\n     * @since   11.4.4\n     *\n     * @product highcharts\n     */\n    traverseToLeaf: false,\n    /**\n     * An option to optimize treemap series rendering by grouping smaller leaf\n     * nodes below a certain square area threshold in pixels. If the square area\n     * of a point becomes smaller than the specified threshold, determined by\n     * the `pixelWidth` and/or `pixelHeight` options, then this point is moved\n     * into one group point per series.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-grouping-simple\n     *         Simple demo of Treemap grouping\n     * @sample {highcharts} highcharts/plotoptions/treemap-grouping-multiple-parents\n     *         Treemap grouping with multiple parents\n     * @sample {highcharts} highcharts/plotoptions/treemap-grouping-advanced\n     *         Advanced demo of Treemap grouping\n     *\n     * @since 12.1.0\n     *\n     * @excluding allowOverlap, animation, dataLabels, drillToCluster, events,\n     * layoutAlgorithm, marker, states, zones\n     *\n     * @product highcharts\n     */\n    cluster: {\n        /**\n         * An additional, individual class name for the grouped point's graphic\n         * representation.\n         *\n         * @type      string\n         * @product   highcharts\n         */\n        className: void 0,\n        /**\n         * Individual color for the grouped point. By default the color is\n         * pulled from the parent color.\n         *\n         * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         * @product   highcharts\n         */\n        color: void 0,\n        /**\n         * Enable or disable Treemap grouping.\n         *\n         * @type {boolean}\n         * @since 12.1.0\n         * @product highcharts\n         */\n        enabled: false,\n        /**\n         * The pixel threshold width of area, which is used in Treemap grouping.\n         *\n         * @type {number}\n         * @since 12.1.0\n         * @product highcharts\n         */\n        pixelWidth: void 0,\n        /**\n         * The pixel threshold height of area, which is used in Treemap\n         * grouping.\n         *\n         * @type {number}\n         * @since 12.1.0\n         * @product highcharts\n         */\n        pixelHeight: void 0,\n        /**\n         * The name of the point of grouped nodes shown in the tooltip,\n         * dataLabels, etc. By default it is set to '+ n', where n is number of\n         * grouped points.\n         *\n         * @type {string}\n         * @since 12.1.0\n         * @product highcharts\n         */\n        name: void 0,\n        /**\n         * A configuration property that specifies the factor by which the value\n         * and size of a grouped node are reduced. This can be particularly\n         * useful when a grouped node occupies a disproportionately large\n         * portion of the graph, ensuring better visual balance and readability.\n         *\n         * @type {number}\n         * @since 12.1.0\n         * @product highcharts\n         */\n        reductionFactor: void 0,\n        /**\n         * Defines the minimum number of child nodes required to create a group\n         * of small nodes.\n         *\n         * @type {number}\n         * @since 12.1.0\n         * @product highcharts\n         */\n        minimumClusterSize: 5,\n        layoutAlgorithm: {\n            distance: 0,\n            gridSize: 0,\n            kmeansThreshold: 0\n        },\n        marker: {\n            lineWidth: 0,\n            radius: 0\n        }\n    }\n};\n/**\n * A `treemap` series. If the [type](#series.treemap.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.treemap\n * @excluding dataParser, dataURL, stack, dataSorting\n * @product   highcharts\n * @requires  modules/treemap\n * @apioption series.treemap\n */\n/**\n * An array of data points for the series. For the `treemap` series\n * type, points can be given in the following ways:\n *\n * 1. An array of numerical values. In this case, the numerical values will be\n *    interpreted as `value` options. Example:\n *    ```js\n *    data: [0, 5, 3, 5]\n *    ```\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.treemap.turboThreshold),\n *    this option is not available.\n *    ```js\n *      data: [{\n *        value: 9,\n *        name: \"Point2\",\n *        color: \"#00FF00\"\n *      }, {\n *        value: 6,\n *        name: \"Point1\",\n *        color: \"#FF00FF\"\n *      }]\n *    ```\n *\n * @sample {highcharts} highcharts/chart/reflow-true/\n *         Numerical values\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<number|null|*>}\n * @extends   series.heatmap.data\n * @excluding x, y, pointPadding\n * @product   highcharts\n * @apioption series.treemap.data\n */\n/**\n * The value of the point, resulting in a relative area of the point\n * in the treemap.\n *\n * @type      {number|null}\n * @product   highcharts\n * @apioption series.treemap.data.value\n */\n/**\n * Serves a purpose only if a `colorAxis` object is defined in the chart\n * options. This value will decide which color the point gets from the\n * scale of the colorAxis.\n *\n * @type      {number}\n * @since     4.1.0\n * @product   highcharts\n * @apioption series.treemap.data.colorValue\n */\n/**\n * Only for treemap. Use this option to build a tree structure. The\n * value should be the id of the point which is the parent. If no points\n * has a matching id, or this option is undefined, then the parent will\n * be set to the root.\n *\n * @sample {highcharts} highcharts/point/parent/\n *         Point parent\n * @sample {highcharts} highcharts/demo/treemap-with-levels/\n *         Example where parent id is not matching\n *\n * @type      {string}\n * @since     4.1.0\n * @product   highcharts\n * @apioption series.treemap.data.parent\n */\n''; // Keeps doclets above detached\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treemap_TreemapSeriesDefaults = (TreemapSeriesDefaults);\n\n;// ./code/es-modules/Series/Treemap/TreemapUtilities.js\n/* *\n *\n *  (c) 2014-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Namespace\n *\n * */\nvar TreemapUtilities;\n(function (TreemapUtilities) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @todo find correct name for this function.\n     * @todo Similar to reduce, this function is likely redundant\n     */\n    function recursive(item, func, context) {\n        const next = func.call(context || this, item);\n        if (next !== false) {\n            recursive(next, func, context);\n        }\n    }\n    TreemapUtilities.recursive = recursive;\n})(TreemapUtilities || (TreemapUtilities = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treemap_TreemapUtilities = (TreemapUtilities);\n\n;// ./code/es-modules/Series/TreeUtilities.js\n/* *\n *\n *  (c) 2014-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { extend: TreeUtilities_extend, isArray, isNumber: TreeUtilities_isNumber, isObject, merge: TreeUtilities_merge, pick: TreeUtilities_pick, relativeLength } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/* eslint-disable valid-jsdoc */\n/**\n * @private\n */\nfunction getColor(node, options) {\n    const index = options.index, mapOptionsToLevel = options.mapOptionsToLevel, parentColor = options.parentColor, parentColorIndex = options.parentColorIndex, series = options.series, colors = options.colors, siblings = options.siblings, points = series.points, chartOptionsChart = series.chart.options.chart;\n    let getColorByPoint, point, level, colorByPoint, colorIndexByPoint, color, colorIndex;\n    /**\n     * @private\n     */\n    const variateColor = (color) => {\n        const colorVariation = level && level.colorVariation;\n        if (colorVariation &&\n            colorVariation.key === 'brightness' &&\n            index &&\n            siblings) {\n            return highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default().parse(color).brighten(colorVariation.to * (index / siblings)).get();\n        }\n        return color;\n    };\n    if (node) {\n        point = points[node.i];\n        level = mapOptionsToLevel[node.level] || {};\n        getColorByPoint = point && level.colorByPoint;\n        if (getColorByPoint) {\n            colorIndexByPoint = point.index % (colors ?\n                colors.length :\n                chartOptionsChart.colorCount);\n            colorByPoint = colors && colors[colorIndexByPoint];\n        }\n        // Select either point color, level color or inherited color.\n        if (!series.chart.styledMode) {\n            color = TreeUtilities_pick(point && point.options.color, level && level.color, colorByPoint, parentColor && variateColor(parentColor), series.color);\n        }\n        colorIndex = TreeUtilities_pick(point && point.options.colorIndex, level && level.colorIndex, colorIndexByPoint, parentColorIndex, options.colorIndex);\n    }\n    return {\n        color: color,\n        colorIndex: colorIndex\n    };\n}\n/**\n * Creates a map from level number to its given options.\n *\n * @private\n *\n * @param {Object} params\n * Object containing parameters.\n * - `defaults` Object containing default options. The default options are\n *   merged with the userOptions to get the final options for a specific\n *   level.\n * - `from` The lowest level number.\n * - `levels` User options from series.levels.\n * - `to` The highest level number.\n *\n * @return {Highcharts.Dictionary<object>|null}\n * Returns a map from level number to its given options.\n */\nfunction getLevelOptions(params) {\n    const result = {};\n    let defaults, converted, i, from, to, levels;\n    if (isObject(params)) {\n        from = TreeUtilities_isNumber(params.from) ? params.from : 1;\n        levels = params.levels;\n        converted = {};\n        defaults = isObject(params.defaults) ? params.defaults : {};\n        if (isArray(levels)) {\n            converted = levels.reduce((obj, item) => {\n                let level, levelIsConstant, options;\n                if (isObject(item) && TreeUtilities_isNumber(item.level)) {\n                    options = TreeUtilities_merge({}, item);\n                    levelIsConstant = TreeUtilities_pick(options.levelIsConstant, defaults.levelIsConstant);\n                    // Delete redundant properties.\n                    delete options.levelIsConstant;\n                    delete options.level;\n                    // Calculate which level these options apply to.\n                    level = item.level + (levelIsConstant ? 0 : from - 1);\n                    if (isObject(obj[level])) {\n                        TreeUtilities_merge(true, obj[level], options); // #16329\n                    }\n                    else {\n                        obj[level] = options;\n                    }\n                }\n                return obj;\n            }, {});\n        }\n        to = TreeUtilities_isNumber(params.to) ? params.to : 1;\n        for (i = 0; i <= to; i++) {\n            result[i] = TreeUtilities_merge({}, defaults, isObject(converted[i]) ? converted[i] : {});\n        }\n    }\n    return result;\n}\n/**\n * @private\n * @todo Combine buildTree and buildNode with setTreeValues\n * @todo Remove logic from Treemap and make it utilize this mixin.\n */\nfunction setTreeValues(tree, options) {\n    const before = options.before, idRoot = options.idRoot, mapIdToNode = options.mapIdToNode, nodeRoot = mapIdToNode[idRoot], levelIsConstant = (options.levelIsConstant !== false), points = options.points, point = points[tree.i], optionsPoint = point && point.options || {}, children = [];\n    let childrenTotal = 0;\n    tree.levelDynamic = tree.level - (levelIsConstant ? 0 : nodeRoot.level);\n    tree.name = TreeUtilities_pick(point && point.name, '');\n    tree.visible = (idRoot === tree.id ||\n        options.visible === true);\n    if (typeof before === 'function') {\n        tree = before(tree, options);\n    }\n    // First give the children some values\n    tree.children.forEach((child, i) => {\n        const newOptions = TreeUtilities_extend({}, options);\n        TreeUtilities_extend(newOptions, {\n            index: i,\n            siblings: tree.children.length,\n            visible: tree.visible\n        });\n        child = setTreeValues(child, newOptions);\n        children.push(child);\n        if (child.visible) {\n            childrenTotal += child.val;\n        }\n    });\n    // Set the values\n    const value = TreeUtilities_pick(optionsPoint.value, childrenTotal);\n    tree.visible = value >= 0 && (childrenTotal > 0 || tree.visible);\n    tree.children = children;\n    tree.childrenTotal = childrenTotal;\n    tree.isLeaf = tree.visible && !childrenTotal;\n    tree.val = value;\n    return tree;\n}\n/**\n * Update the rootId property on the series. Also makes sure that it is\n * accessible to exporting.\n *\n * @private\n *\n * @param {Object} series\n * The series to operate on.\n *\n * @return {string}\n * Returns the resulting rootId after update.\n */\nfunction updateRootId(series) {\n    let rootId, options;\n    if (isObject(series)) {\n        // Get the series options.\n        options = isObject(series.options) ? series.options : {};\n        // Calculate the rootId.\n        rootId = TreeUtilities_pick(series.rootNode, options.rootId, '');\n        // Set rootId on series.userOptions to pick it up in exporting.\n        if (isObject(series.userOptions)) {\n            series.userOptions.rootId = rootId;\n        }\n        // Set rootId on series to pick it up on next update.\n        series.rootNode = rootId;\n    }\n    return rootId;\n}\n/**\n * Get the node width, which relies on the plot width and the nodeDistance\n * option.\n *\n * @private\n */\nfunction getNodeWidth(series, columnCount) {\n    const { chart, options } = series, { nodeDistance = 0, nodeWidth = 0 } = options, { plotSizeX = 1 } = chart;\n    // Node width auto means they are evenly distributed along the width of\n    // the plot area\n    if (nodeWidth === 'auto') {\n        if (typeof nodeDistance === 'string' && /%$/.test(nodeDistance)) {\n            const fraction = parseFloat(nodeDistance) / 100, total = columnCount + fraction * (columnCount - 1);\n            return plotSizeX / total;\n        }\n        const nDistance = Number(nodeDistance);\n        return ((plotSizeX + nDistance) /\n            (columnCount || 1)) - nDistance;\n    }\n    return relativeLength(nodeWidth, plotSizeX);\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst TreeUtilities = {\n    getColor,\n    getLevelOptions,\n    getNodeWidth,\n    setTreeValues,\n    updateRootId\n};\n/* harmony default export */ const Series_TreeUtilities = (TreeUtilities);\n\n;// ./code/es-modules/Series/Treemap/TreemapSeries.js\n/* *\n *\n *  (c) 2014-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { parse: color } = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default());\n\n\nconst { composed: TreemapSeries_composed, noop } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nconst { column: ColumnSeries, scatter: ScatterSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\n\n\n\n\n\nconst { getColor: TreemapSeries_getColor, getLevelOptions: TreemapSeries_getLevelOptions, updateRootId: TreemapSeries_updateRootId } = Series_TreeUtilities;\n\nconst { addEvent: TreemapSeries_addEvent, arrayMax, clamp, correctFloat, crisp, defined: TreemapSeries_defined, error, extend: TreemapSeries_extend, fireEvent: TreemapSeries_fireEvent, isArray: TreemapSeries_isArray, isNumber: TreemapSeries_isNumber, isObject: TreemapSeries_isObject, isString: TreemapSeries_isString, merge: TreemapSeries_merge, pick: TreemapSeries_pick, pushUnique: TreemapSeries_pushUnique, splat, stableSort } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nhighcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default().keepProps.push('simulation', 'hadOutsideDataLabels');\n/* *\n *\n *  Constants\n *\n * */\nconst axisMax = 100;\n/* *\n *\n *  Variables\n *\n * */\nlet treemapAxisDefaultValues = false;\n/* *\n *\n *  Functions\n *\n * */\n/** @private */\nfunction onSeriesAfterBindAxes() {\n    const series = this, xAxis = series.xAxis, yAxis = series.yAxis;\n    let treeAxis;\n    if (xAxis && yAxis) {\n        if (series.is('treemap')) {\n            treeAxis = {\n                endOnTick: false,\n                gridLineWidth: 0,\n                lineWidth: 0,\n                min: 0,\n                minPadding: 0,\n                max: axisMax,\n                maxPadding: 0,\n                startOnTick: false,\n                title: void 0,\n                tickPositions: []\n            };\n            TreemapSeries_extend(yAxis.options, treeAxis);\n            TreemapSeries_extend(xAxis.options, treeAxis);\n            treemapAxisDefaultValues = true;\n        }\n        else if (treemapAxisDefaultValues) {\n            yAxis.setOptions(yAxis.userOptions);\n            xAxis.setOptions(xAxis.userOptions);\n            treemapAxisDefaultValues = false;\n        }\n    }\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.treemap\n *\n * @augments Highcharts.Series\n */\nclass TreemapSeries extends ScatterSeries {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        this.simulation = 0;\n        /* eslint-enable valid-jsdoc */\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static compose(SeriesClass) {\n        if (TreemapSeries_pushUnique(TreemapSeries_composed, 'TreemapSeries')) {\n            TreemapSeries_addEvent(SeriesClass, 'afterBindAxes', onSeriesAfterBindAxes);\n        }\n    }\n    /* *\n     *\n     *  Function\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    algorithmCalcPoints(directionChange, last, group, childrenArea) {\n        const plot = group.plot, end = group.elArr.length - 1;\n        let pX, pY, pW, pH, gW = group.lW, gH = group.lH, keep, i = 0;\n        if (last) {\n            gW = group.nW;\n            gH = group.nH;\n        }\n        else {\n            keep = group.elArr[end];\n        }\n        for (const p of group.elArr) {\n            if (last || (i < end)) {\n                if (group.direction === 0) {\n                    pX = plot.x;\n                    pY = plot.y;\n                    pW = gW;\n                    pH = p / pW;\n                }\n                else {\n                    pX = plot.x;\n                    pY = plot.y;\n                    pH = gH;\n                    pW = p / pH;\n                }\n                childrenArea.push({\n                    x: pX,\n                    y: pY,\n                    width: pW,\n                    height: correctFloat(pH)\n                });\n                if (group.direction === 0) {\n                    plot.y = plot.y + pH;\n                }\n                else {\n                    plot.x = plot.x + pW;\n                }\n            }\n            i = i + 1;\n        }\n        // Reset variables\n        group.reset();\n        if (group.direction === 0) {\n            group.width = group.width - gW;\n        }\n        else {\n            group.height = group.height - gH;\n        }\n        plot.y = plot.parent.y + (plot.parent.height - group.height);\n        plot.x = plot.parent.x + (plot.parent.width - group.width);\n        if (directionChange) {\n            group.direction = 1 - group.direction;\n        }\n        // If not last, then add uncalculated element\n        if (!last) {\n            group.addElement(keep);\n        }\n    }\n    algorithmFill(directionChange, parent, children) {\n        const childrenArea = [];\n        let pTot, direction = parent.direction, x = parent.x, y = parent.y, width = parent.width, height = parent.height, pX, pY, pW, pH;\n        for (const child of children) {\n            pTot =\n                (parent.width * parent.height) * (child.val / parent.val);\n            pX = x;\n            pY = y;\n            if (direction === 0) {\n                pH = height;\n                pW = pTot / pH;\n                width = width - pW;\n                x = x + pW;\n            }\n            else {\n                pW = width;\n                pH = pTot / pW;\n                height = height - pH;\n                y = y + pH;\n            }\n            childrenArea.push({\n                x: pX,\n                y: pY,\n                width: pW,\n                height: pH,\n                direction: 0,\n                val: 0\n            });\n            if (directionChange) {\n                direction = 1 - direction;\n            }\n        }\n        return childrenArea;\n    }\n    algorithmLowAspectRatio(directionChange, parent, children) {\n        const series = this, childrenArea = [], plot = {\n            x: parent.x,\n            y: parent.y,\n            parent: parent\n        }, direction = parent.direction, end = children.length - 1, group = new Treemap_TreemapAlgorithmGroup(parent.height, parent.width, direction, plot);\n        let pTot, i = 0;\n        // Loop through and calculate all areas\n        for (const child of children) {\n            pTot =\n                (parent.width * parent.height) * (child.val / parent.val);\n            group.addElement(pTot);\n            if (group.lP.nR > group.lP.lR) {\n                series.algorithmCalcPoints(directionChange, false, group, childrenArea, plot // @todo no supported\n                );\n            }\n            // If last child, then calculate all remaining areas\n            if (i === end) {\n                series.algorithmCalcPoints(directionChange, true, group, childrenArea, plot // @todo not supported\n                );\n            }\n            ++i;\n        }\n        return childrenArea;\n    }\n    /**\n     * Over the alignment method by setting z index.\n     * @private\n     */\n    alignDataLabel(point, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    dataLabel, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    labelOptions) {\n        ColumnSeries.prototype.alignDataLabel.apply(this, arguments);\n        if (point.dataLabel) {\n            // `point.node.zIndex` could be undefined (#6956)\n            point.dataLabel.attr({ zIndex: (point.node.zIndex || 0) + 1 });\n        }\n    }\n    applyTreeGrouping() {\n        const series = this, parentList = series.parentList || {}, { cluster } = series.options, minimumClusterSize = cluster?.minimumClusterSize || 5;\n        if (cluster?.enabled) {\n            const parentGroups = {};\n            const checkIfHide = (node) => {\n                if (node?.point?.shapeArgs) {\n                    const { width = 0, height = 0 } = node.point.shapeArgs, area = width * height;\n                    const { pixelWidth = 0, pixelHeight = 0 } = cluster, compareHeight = TreemapSeries_defined(pixelHeight), thresholdArea = pixelHeight ?\n                        pixelWidth * pixelHeight :\n                        pixelWidth * pixelWidth;\n                    if (width < pixelWidth ||\n                        height < (compareHeight ? pixelHeight : pixelWidth) ||\n                        area < thresholdArea) {\n                        if (!node.isGroup && TreemapSeries_defined(node.parent)) {\n                            if (!parentGroups[node.parent]) {\n                                parentGroups[node.parent] = [];\n                            }\n                            parentGroups[node.parent].push(node);\n                        }\n                    }\n                }\n                node?.children.forEach((child) => {\n                    checkIfHide(child);\n                });\n            };\n            checkIfHide(series.tree);\n            for (const parent in parentGroups) {\n                if (parentGroups[parent]) {\n                    if (parentGroups[parent].length > minimumClusterSize) {\n                        parentGroups[parent].forEach((node) => {\n                            const index = parentList[parent].indexOf(node.i);\n                            if (index !== -1) {\n                                parentList[parent].splice(index, 1);\n                                const id = `highcharts-grouped-treemap-points-${node.parent || 'root'}`;\n                                let groupPoint = series.points\n                                    .find((p) => p.id === id);\n                                if (!groupPoint) {\n                                    const PointClass = series.pointClass, pointIndex = series.points.length;\n                                    groupPoint = new PointClass(series, {\n                                        className: cluster.className,\n                                        color: cluster.color,\n                                        id,\n                                        index: pointIndex,\n                                        isGroup: true,\n                                        value: 0\n                                    });\n                                    TreemapSeries_extend(groupPoint, {\n                                        formatPrefix: 'cluster'\n                                    });\n                                    series.points.push(groupPoint);\n                                    parentList[parent].push(pointIndex);\n                                    parentList[id] = [];\n                                }\n                                const amount = groupPoint.groupedPointsAmount + 1, val = series.points[groupPoint.index]\n                                    .options.value || 0, name = cluster.name ||\n                                    `+ ${amount}`;\n                                // Update the point directly in points array to\n                                // prevent wrong instance update\n                                series.points[groupPoint.index]\n                                    .groupedPointsAmount = amount;\n                                series.points[groupPoint.index].options.value =\n                                    val + (node.point.value || 0);\n                                series.points[groupPoint.index].name = name;\n                                parentList[id].push(node.point.index);\n                            }\n                        });\n                    }\n                }\n            }\n            series.nodeMap = {};\n            series.nodeList = [];\n            series.parentList = parentList;\n            const tree = series.buildTree('', -1, 0, series.parentList);\n            series.translate(tree);\n        }\n    }\n    /**\n     * Recursive function which calculates the area for all children of a\n     * node.\n     *\n     * @private\n     * @function Highcharts.Series#calculateChildrenAreas\n     *\n     * @param {Object} parent\n     * The node which is parent to the children.\n     *\n     * @param {Object} area\n     * The rectangular area of the parent.\n     */\n    calculateChildrenAreas(parent, area) {\n        const series = this, options = series.options, mapOptionsToLevel = series.mapOptionsToLevel, level = mapOptionsToLevel[parent.level + 1], algorithm = TreemapSeries_pick((level?.layoutAlgorithm &&\n            series[level?.layoutAlgorithm] &&\n            level.layoutAlgorithm), options.layoutAlgorithm), alternate = options.alternateStartingDirection, \n        // Collect all children which should be included\n        children = parent.children.filter((n) => parent.isGroup || !n.ignore), groupPadding = level?.groupPadding ?? options.groupPadding ?? 0, rootNode = series.nodeMap[series.rootNode];\n        if (!algorithm) {\n            return;\n        }\n        let childrenValues = [], axisWidth = rootNode.pointValues?.width || 0, axisHeight = rootNode.pointValues?.height || 0;\n        if (level?.layoutStartingDirection) {\n            area.direction = level.layoutStartingDirection === 'vertical' ?\n                0 :\n                1;\n        }\n        childrenValues = series[algorithm](area, children);\n        let i = -1;\n        for (const child of children) {\n            const values = childrenValues[++i];\n            if (child === rootNode) {\n                axisWidth = axisWidth || values.width;\n                axisHeight = values.height;\n            }\n            const groupPaddingXValues = groupPadding / (series.xAxis.len / axisHeight), groupPaddingYValues = groupPadding / (series.yAxis.len / axisHeight);\n            child.values = TreemapSeries_merge(values, {\n                val: child.childrenTotal,\n                direction: (alternate ? 1 - area.direction : area.direction)\n            });\n            // Make room for outside data labels\n            if (child.children.length &&\n                child.point.dataLabels?.length) {\n                const dlHeight = arrayMax(child.point.dataLabels.map((dl) => dl.options\n                    ?.headers && dl.height || 0)) / (series.yAxis.len / axisHeight);\n                // Make room for data label unless the group is too small\n                if (dlHeight < child.values.height / 2) {\n                    child.values.y += dlHeight;\n                    child.values.height -= dlHeight;\n                }\n            }\n            if (groupPadding) {\n                const xPad = Math.min(groupPaddingXValues, child.values.width / 4), yPad = Math.min(groupPaddingYValues, child.values.height / 4);\n                child.values.x += xPad;\n                child.values.width -= 2 * xPad;\n                child.values.y += yPad;\n                child.values.height -= 2 * yPad;\n            }\n            child.pointValues = TreemapSeries_merge(values, {\n                x: (values.x / series.axisRatio),\n                // Flip y-values to avoid visual regression with csvCoord in\n                // Axis.translate at setPointValues. #12488\n                y: axisMax - values.y - values.height,\n                width: (values.width / series.axisRatio)\n            });\n            // If node has children, then call method recursively\n            if (child.children.length) {\n                series.calculateChildrenAreas(child, child.values);\n            }\n        }\n        const getChildrenRecursive = (node, result = [], getLeaves = true) => {\n            node.children.forEach((child) => {\n                if (getLeaves && child.isLeaf) {\n                    result.push(child.point);\n                }\n                else if (!getLeaves && !child.isLeaf) {\n                    result.push(child.point);\n                }\n                if (child.children.length) {\n                    getChildrenRecursive(child, result, getLeaves);\n                }\n            });\n            return result;\n        };\n        // Experimental block to make space for the outside data labels\n        if (options.nodeSizeBy === 'leaf' &&\n            parent === rootNode &&\n            this.hasOutsideDataLabels &&\n            // Sizing by leaf value is not possible if any of the groups have\n            // explicit values\n            !getChildrenRecursive(rootNode, void 0, false)\n                .some((point) => TreemapSeries_isNumber(point.options.value)) &&\n            !TreemapSeries_isNumber(rootNode.point?.options.value)) {\n            const leaves = getChildrenRecursive(rootNode), values = leaves.map((point) => point.options.value || 0), \n            // Areas in terms of axis units squared\n            areas = leaves.map(({ node: { pointValues } }) => (pointValues ?\n                pointValues.width * pointValues.height :\n                0)), valueSum = values.reduce((sum, value) => sum + value, 0), areaSum = areas.reduce((sum, value) => sum + value, 0), expectedAreaPerValue = areaSum / valueSum;\n            let minMiss = 0, maxMiss = 0;\n            leaves.forEach((point, i) => {\n                const areaPerValue = values[i] ? (areas[i] / values[i]) : 1, \n                // Less than 1 => rendered too small, greater than 1 =>\n                // rendered too big\n                fit = clamp(areaPerValue / expectedAreaPerValue, 0.8, 1.4);\n                let miss = 1 - fit;\n                if (point.value) {\n                    // Very small areas are more sensitive, and matter less to\n                    // the visual impression. Give them less weight.\n                    if (areas[i] < 20) {\n                        miss *= areas[i] / 20;\n                    }\n                    if (miss > maxMiss) {\n                        maxMiss = miss;\n                    }\n                    if (miss < minMiss) {\n                        minMiss = miss;\n                    }\n                    point.simulatedValue = (point.simulatedValue || point.value) / fit;\n                }\n            });\n            /* /\n            console.log('--- simulation',\n                this.simulation,\n                'worstMiss',\n                minMiss,\n                maxMiss\n            );\n            // */\n            if (\n            // An area error less than 5% is acceptable, the human ability\n            // to assess area size is not that accurate\n            (minMiss < -0.05 || maxMiss > 0.05) &&\n                // In case an eternal loop is brewing, pull the emergency brake\n                this.simulation < 10) {\n                this.simulation++;\n                this.setTreeValues(parent);\n                area.val = parent.val;\n                this.calculateChildrenAreas(parent, area);\n                // Simulation is settled, proceed to rendering. Reset the simulated\n                // values and set the tree values with real data.\n            }\n            else {\n                leaves.forEach((point) => {\n                    delete point.simulatedValue;\n                });\n                this.setTreeValues(parent);\n                this.simulation = 0;\n            }\n        }\n    }\n    /**\n     * Create level list.\n     * @private\n     */\n    createList(e) {\n        const chart = this.chart, breadcrumbs = chart.breadcrumbs, list = [];\n        if (breadcrumbs) {\n            let currentLevelNumber = 0;\n            list.push({\n                level: currentLevelNumber,\n                levelOptions: chart.series[0]\n            });\n            let node = e.target.nodeMap[e.newRootId];\n            const extraNodes = [];\n            // When the root node is set and has parent,\n            // recreate the path from the node tree.\n            while (node.parent || node.parent === '') {\n                extraNodes.push(node);\n                node = e.target.nodeMap[node.parent];\n            }\n            for (const node of extraNodes.reverse()) {\n                list.push({\n                    level: ++currentLevelNumber,\n                    levelOptions: node\n                });\n            }\n            // If the list has only first element, we should clear it\n            if (list.length <= 1) {\n                list.length = 0;\n            }\n        }\n        return list;\n    }\n    /**\n     * Extend drawDataLabels with logic to handle custom options related to\n     * the treemap series:\n     *\n     * - Points which is not a leaf node, has dataLabels disabled by\n     *   default.\n     *\n     * - Options set on series.levels is merged in.\n     *\n     * - Width of the dataLabel is set to match the width of the point\n     *   shape.\n     *\n     * @private\n     */\n    drawDataLabels() {\n        const series = this, mapOptionsToLevel = series.mapOptionsToLevel, points = series.points.filter(function (n) {\n            return n.node.visible || TreemapSeries_defined(n.dataLabel);\n        }), padding = splat(series.options.dataLabels || {})[0]?.padding, positionsAreSet = points.some((p) => TreemapSeries_isNumber(p.plotY));\n        for (const point of points) {\n            const style = {}, \n            // Set options to new object to avoid problems with scope\n            options = { style }, level = mapOptionsToLevel[point.node.level];\n            // If not a leaf, then label should be disabled as default\n            if (!point.node.isLeaf &&\n                !point.node.isGroup ||\n                (point.node.isGroup &&\n                    point.node.level <= series.nodeMap[series.rootNode].level)) {\n                options.enabled = false;\n            }\n            // If options for level exists, include them as well\n            if (level?.dataLabels) {\n                TreemapSeries_merge(true, options, splat(level.dataLabels)[0]);\n                series.hasDataLabels = () => true;\n            }\n            // Headers are always top-aligned. Leaf nodes no not support\n            // headers.\n            if (point.node.isLeaf) {\n                options.inside = true;\n            }\n            else if (options.headers) {\n                options.verticalAlign = 'top';\n            }\n            // Set dataLabel width to the width of the point shape minus the\n            // padding\n            if (point.shapeArgs && positionsAreSet) {\n                const { height = 0, width = 0 } = point.shapeArgs;\n                if (width > 32 && height > 16 && point.shouldDraw()) {\n                    const dataLabelWidth = width -\n                        2 * (options.padding || padding || 0);\n                    style.width = `${dataLabelWidth}px`;\n                    style.lineClamp ?? (style.lineClamp = Math.floor(height / 16));\n                    style.visibility = 'inherit';\n                    // Make the label box itself fill the width\n                    if (options.headers) {\n                        point.dataLabel?.attr({\n                            width: dataLabelWidth\n                        });\n                    }\n                    // Hide labels for shapes that are too small\n                }\n                else {\n                    style.width = `${width}px`;\n                    style.visibility = 'hidden';\n                }\n            }\n            // Merge custom options with point options\n            point.dlOptions = TreemapSeries_merge(options, point.options.dataLabels);\n        }\n        super.drawDataLabels(points);\n    }\n    /**\n     * Override drawPoints\n     * @private\n     */\n    drawPoints(points = this.points) {\n        const series = this, chart = series.chart, renderer = chart.renderer, styledMode = chart.styledMode, options = series.options, shadow = styledMode ? {} : options.shadow, borderRadius = options.borderRadius, withinAnimationLimit = chart.pointCount < options.animationLimit, allowTraversingTree = options.allowTraversingTree;\n        for (const point of points) {\n            const levelDynamic = point.node.levelDynamic, animatableAttribs = {}, attribs = {}, css = {}, groupKey = 'level-group-' + point.node.level, hasGraphic = !!point.graphic, shouldAnimate = withinAnimationLimit && hasGraphic, shapeArgs = point.shapeArgs;\n            // Don't bother with calculate styling if the point is not drawn\n            if (point.shouldDraw()) {\n                point.isInside = true;\n                if (borderRadius) {\n                    attribs.r = borderRadius;\n                }\n                TreemapSeries_merge(true, // Extend object\n                // Which object to extend\n                shouldAnimate ? animatableAttribs : attribs, \n                // Add shapeArgs to animate/attr if graphic exists\n                hasGraphic ? shapeArgs : {}, \n                // Add style attribs if !styleMode\n                styledMode ?\n                    {} :\n                    series.pointAttribs(point, point.selected ? 'select' : void 0));\n                // In styled mode apply point.color. Use CSS, otherwise the\n                // fill used in the style sheet will take precedence over\n                // the fill attribute.\n                if (series.colorAttribs && styledMode) {\n                    // Heatmap is loaded\n                    TreemapSeries_extend(css, series.colorAttribs(point));\n                }\n                if (!series[groupKey]) {\n                    series[groupKey] = renderer.g(groupKey)\n                        .attr({\n                        // @todo Set the zIndex based upon the number of\n                        // levels, instead of using 1000\n                        zIndex: 1000 - (levelDynamic || 0)\n                    })\n                        .add(series.group);\n                    series[groupKey].survive = true;\n                }\n            }\n            // Draw the point\n            point.draw({\n                animatableAttribs,\n                attribs,\n                css,\n                group: series[groupKey],\n                imageUrl: point.imageUrl,\n                renderer,\n                shadow,\n                shapeArgs,\n                shapeType: point.shapeType\n            });\n            // If setRootNode is allowed, set a point cursor on clickables &\n            // add drillId to point\n            if (allowTraversingTree && point.graphic) {\n                point.drillId = options.interactByLeaf ?\n                    series.drillToByLeaf(point) :\n                    series.drillToByGroup(point);\n            }\n        }\n    }\n    /**\n     * Finds the drill id for a parent node. Returns false if point should\n     * not have a click event.\n     * @private\n     */\n    drillToByGroup(point) {\n        return (!point.node.isLeaf || point.node.isGroup) ?\n            point.id : false;\n    }\n    /**\n     * Finds the drill id for a leaf node. Returns false if point should not\n     * have a click event\n     * @private\n     */\n    drillToByLeaf(point) {\n        const { traverseToLeaf } = point.series.options;\n        let drillId = false, nodeParent;\n        if ((point.node.parent !== this.rootNode) &&\n            point.node.isLeaf) {\n            if (traverseToLeaf) {\n                drillId = point.id;\n            }\n            else {\n                nodeParent = point.node;\n                while (!drillId) {\n                    if (typeof nodeParent.parent !== 'undefined') {\n                        nodeParent = this.nodeMap[nodeParent.parent];\n                    }\n                    if (nodeParent.parent === this.rootNode) {\n                        drillId = nodeParent.id;\n                    }\n                }\n            }\n        }\n        return drillId;\n    }\n    /**\n     * @todo remove this function at a suitable version.\n     * @private\n     */\n    drillToNode(id, redraw) {\n        error(32, false, void 0, { 'treemap.drillToNode': 'use treemap.setRootNode' });\n        this.setRootNode(id, redraw);\n    }\n    drillUp() {\n        const series = this, node = series.nodeMap[series.rootNode];\n        if (node && TreemapSeries_isString(node.parent)) {\n            series.setRootNode(node.parent, true, { trigger: 'traverseUpButton' });\n        }\n    }\n    getExtremes() {\n        // Get the extremes from the value data\n        const { dataMin, dataMax } = super.getExtremes(this.colorValueData);\n        this.valueMin = dataMin;\n        this.valueMax = dataMax;\n        // Get the extremes from the y data\n        return super.getExtremes();\n    }\n    /**\n     * Creates an object map from parent id to childrens index.\n     *\n     * @private\n     * @function Highcharts.Series#getListOfParents\n     *\n     * @param {Highcharts.SeriesTreemapDataOptions} [data]\n     *        List of points set in options.\n     *\n     * @param {Array<string>} [existingIds]\n     *        List of all point ids.\n     *\n     * @return {Object}\n     *         Map from parent id to children index in data.\n     */\n    getListOfParents(data, existingIds) {\n        const arr = TreemapSeries_isArray(data) ? data : [], ids = TreemapSeries_isArray(existingIds) ? existingIds : [], listOfParents = arr.reduce(function (prev, curr, i) {\n            const parent = TreemapSeries_pick(curr.parent, '');\n            if (typeof prev[parent] === 'undefined') {\n                prev[parent] = [];\n            }\n            prev[parent].push(i);\n            return prev;\n        }, {\n            '': [] // Root of tree\n        });\n        // If parent does not exist, hoist parent to root of tree.\n        for (const parent of Object.keys(listOfParents)) {\n            const children = listOfParents[parent];\n            if ((parent !== '') && (ids.indexOf(parent) === -1)) {\n                for (const child of children) {\n                    listOfParents[''].push(child);\n                }\n                delete listOfParents[parent];\n            }\n        }\n        return listOfParents;\n    }\n    /**\n     * Creates a tree structured object from the series points.\n     * @private\n     */\n    getTree() {\n        const series = this, allIds = this.data.map(function (d) {\n            return d.id;\n        });\n        series.parentList = series.getListOfParents(this.data, allIds);\n        series.nodeMap = {};\n        series.nodeList = [];\n        return series.buildTree('', -1, 0, series.parentList || {});\n    }\n    buildTree(id, index, level, list, parent) {\n        const series = this, children = [], point = series.points[index];\n        let height = 0, child;\n        // Actions\n        for (const i of (list[id] || [])) {\n            child = series.buildTree(series.points[i].id, i, level + 1, list, id);\n            height = Math.max(child.height + 1, height);\n            children.push(child);\n        }\n        const node = new series.NodeClass().init(id, index, children, height, level, series, parent);\n        for (const child of children) {\n            child.parentNode = node;\n        }\n        series.nodeMap[node.id] = node;\n        series.nodeList.push(node);\n        if (point) {\n            point.node = node;\n            node.point = point;\n        }\n        return node;\n    }\n    /**\n     * Define hasData function for non-cartesian series. Returns true if the\n     * series has points at all.\n     * @private\n     */\n    hasData() {\n        return !!this.dataTable.rowCount;\n    }\n    init(chart, options) {\n        const series = this, breadcrumbsOptions = TreemapSeries_merge(options.drillUpButton, options.breadcrumbs), setOptionsEvent = TreemapSeries_addEvent(series, 'setOptions', (event) => {\n            const options = event.userOptions;\n            // Deprecated options\n            if (TreemapSeries_defined(options.allowDrillToNode) &&\n                !TreemapSeries_defined(options.allowTraversingTree)) {\n                options.allowTraversingTree = options.allowDrillToNode;\n                delete options.allowDrillToNode;\n            }\n            if (TreemapSeries_defined(options.drillUpButton) &&\n                !TreemapSeries_defined(options.traverseUpButton)) {\n                options.traverseUpButton = options.drillUpButton;\n                delete options.drillUpButton;\n            }\n            // Check if we need to reserve space for headers\n            const dataLabels = splat(options.dataLabels || {});\n            options.levels?.forEach((level) => {\n                dataLabels.push.apply(dataLabels, splat(level.dataLabels || {}));\n            });\n            this.hasOutsideDataLabels = dataLabels.some((dl) => dl.headers);\n        });\n        super.init(chart, options);\n        // Treemap's opacity is a different option from other series\n        delete series.opacity;\n        // Handle deprecated options.\n        series.eventsToUnbind.push(setOptionsEvent);\n        if (series.options.allowTraversingTree) {\n            series.eventsToUnbind.push(TreemapSeries_addEvent(series, 'click', series.onClickDrillToNode));\n            series.eventsToUnbind.push(TreemapSeries_addEvent(series, 'setRootNode', function (e) {\n                const chart = series.chart;\n                if (chart.breadcrumbs) {\n                    // Create a list using the event after drilldown.\n                    chart.breadcrumbs.updateProperties(series.createList(e));\n                }\n            }));\n            series.eventsToUnbind.push(TreemapSeries_addEvent(series, 'update', \n            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n            function (e, redraw) {\n                const breadcrumbs = this.chart.breadcrumbs;\n                if (breadcrumbs && e.options.breadcrumbs) {\n                    breadcrumbs.update(e.options.breadcrumbs);\n                }\n                this.hadOutsideDataLabels = this.hasOutsideDataLabels;\n            }));\n            series.eventsToUnbind.push(TreemapSeries_addEvent(series, 'destroy', function destroyEvents(e) {\n                const chart = this.chart;\n                if (chart.breadcrumbs && !e.keepEventsForUpdate) {\n                    chart.breadcrumbs.destroy();\n                    chart.breadcrumbs = void 0;\n                }\n            }));\n        }\n        if (!chart.breadcrumbs) {\n            chart.breadcrumbs = new Breadcrumbs_Breadcrumbs(chart, breadcrumbsOptions);\n        }\n        series.eventsToUnbind.push(TreemapSeries_addEvent(chart.breadcrumbs, 'up', function (e) {\n            const drillUpsNumber = this.level - e.newLevel;\n            for (let i = 0; i < drillUpsNumber; i++) {\n                series.drillUp();\n            }\n        }));\n    }\n    /**\n     * Add drilling on the suitable points.\n     * @private\n     */\n    onClickDrillToNode(event) {\n        const series = this, point = event.point, drillId = point?.drillId;\n        // If a drill id is returned, add click event and cursor.\n        if (TreemapSeries_isString(drillId)) {\n            point.setState(''); // Remove hover\n            series.setRootNode(drillId, true, { trigger: 'click' });\n        }\n    }\n    /**\n     * Get presentational attributes\n     * @private\n     */\n    pointAttribs(point, state) {\n        const series = this, mapOptionsToLevel = (TreemapSeries_isObject(series.mapOptionsToLevel) ?\n            series.mapOptionsToLevel :\n            {}), level = point && mapOptionsToLevel[point.node.level] || {}, options = this.options, stateOptions = state && options.states && options.states[state] || {}, className = point?.getClassName() || '', \n        // Set attributes by precedence. Point trumps level trumps series.\n        // Stroke width uses pick because it can be 0.\n        attr = {\n            'stroke': (point && point.borderColor) ||\n                level.borderColor ||\n                stateOptions.borderColor ||\n                options.borderColor,\n            'stroke-width': TreemapSeries_pick(point && point.borderWidth, level.borderWidth, stateOptions.borderWidth, options.borderWidth),\n            'dashstyle': point?.borderDashStyle ||\n                level.borderDashStyle ||\n                stateOptions.borderDashStyle ||\n                options.borderDashStyle,\n            'fill': point?.color || this.color\n        };\n        // Hide levels above the current view\n        if (className.indexOf('highcharts-above-level') !== -1) {\n            attr.fill = 'none';\n            attr['stroke-width'] = 0;\n            // Nodes with children that accept interaction\n        }\n        else if (className.indexOf('highcharts-internal-node-interactive') !== -1) {\n            attr['fill-opacity'] = stateOptions.opacity ?? options.opacity ?? 1;\n            attr.cursor = 'pointer';\n            // Hide nodes that have children\n        }\n        else if (className.indexOf('highcharts-internal-node') !== -1) {\n            attr.fill = 'none';\n        }\n        else if (state && stateOptions.brightness) {\n            // Brighten and hoist the hover nodes\n            attr.fill = color(attr.fill)\n                .brighten(stateOptions.brightness)\n                .get();\n        }\n        return attr;\n    }\n    /**\n     * Set the node's color recursively, from the parent down.\n     * @private\n     */\n    setColorRecursive(node, parentColor, colorIndex, index, siblings) {\n        const series = this, chart = series?.chart, colors = chart?.options?.colors;\n        if (node) {\n            const colorInfo = TreemapSeries_getColor(node, {\n                colors: colors,\n                index: index,\n                mapOptionsToLevel: series.mapOptionsToLevel,\n                parentColor: parentColor,\n                parentColorIndex: colorIndex,\n                series: series,\n                siblings: siblings\n            }), point = series.points[node.i];\n            if (point) {\n                point.color = colorInfo.color;\n                point.colorIndex = colorInfo.colorIndex;\n            }\n            let i = -1;\n            // Do it all again with the children\n            for (const child of (node.children || [])) {\n                series.setColorRecursive(child, colorInfo.color, colorInfo.colorIndex, ++i, node.children.length);\n            }\n        }\n    }\n    setPointValues() {\n        const series = this;\n        const { points, xAxis, yAxis } = series;\n        const styledMode = series.chart.styledMode;\n        // Get the crisp correction in classic mode. For this to work in\n        // styled mode, we would need to first add the shape (without x,\n        // y, width and height), then read the rendered stroke width\n        // using point.graphic.strokeWidth(), then modify and apply the\n        // shapeArgs. This applies also to column series, but the\n        // downside is performance and code complexity.\n        const getStrokeWidth = (point) => (styledMode ?\n            0 :\n            (series.pointAttribs(point)['stroke-width'] || 0));\n        for (const point of points) {\n            const { pointValues: values, visible } = point.node;\n            // Points which is ignored, have no values.\n            if (values && visible) {\n                const { height, width, x, y } = values, strokeWidth = getStrokeWidth(point), xValue = xAxis.toPixels(x, true), x2Value = xAxis.toPixels(x + width, true), yValue = yAxis.toPixels(y, true), y2Value = yAxis.toPixels(y + height, true), \n                // If the edge of a rectangle is on the edge, make sure it\n                // stays within the plot area by adding or substracting half\n                // of the stroke width.\n                x1 = xValue === 0 ?\n                    strokeWidth / 2 :\n                    crisp(xAxis.toPixels(x, true), strokeWidth, true), x2 = x2Value === xAxis.len ?\n                    xAxis.len - strokeWidth / 2 :\n                    crisp(xAxis.toPixels(x + width, true), strokeWidth, true), y1 = yValue === yAxis.len ?\n                    yAxis.len - strokeWidth / 2 :\n                    crisp(yAxis.toPixels(y, true), strokeWidth, true), y2 = y2Value === 0 ?\n                    strokeWidth / 2 :\n                    crisp(yAxis.toPixels(y + height, true), strokeWidth, true);\n                // Set point values\n                const shapeArgs = {\n                    x: Math.min(x1, x2),\n                    y: Math.min(y1, y2),\n                    width: Math.abs(x2 - x1),\n                    height: Math.abs(y2 - y1)\n                };\n                point.plotX = shapeArgs.x + (shapeArgs.width / 2);\n                point.plotY = shapeArgs.y + (shapeArgs.height / 2);\n                point.shapeArgs = shapeArgs;\n            }\n            else {\n                // Reset visibility\n                delete point.plotX;\n                delete point.plotY;\n            }\n        }\n    }\n    /**\n     * Sets a new root node for the series.\n     *\n     * @private\n     * @function Highcharts.Series#setRootNode\n     *\n     * @param {string} id\n     * The id of the new root node.\n     *\n     * @param {boolean} [redraw=true]\n     * Whether to redraw the chart or not.\n     *\n     * @param {Object} [eventArguments]\n     * Arguments to be accessed in event handler.\n     *\n     * @param {string} [eventArguments.newRootId]\n     * Id of the new root.\n     *\n     * @param {string} [eventArguments.previousRootId]\n     * Id of the previous root.\n     *\n     * @param {boolean} [eventArguments.redraw]\n     * Whether to redraw the chart after.\n     *\n     * @param {Object} [eventArguments.series]\n     * The series to update the root of.\n     *\n     * @param {string} [eventArguments.trigger]\n     * The action which triggered the event. Undefined if the setRootNode is\n     * called directly.\n     *\n     * @emits Highcharts.Series#event:setRootNode\n     */\n    setRootNode(id, redraw, eventArguments) {\n        const series = this, eventArgs = TreemapSeries_extend({\n            newRootId: id,\n            previousRootId: series.rootNode,\n            redraw: TreemapSeries_pick(redraw, true),\n            series: series\n        }, eventArguments);\n        /**\n         * The default functionality of the setRootNode event.\n         *\n         * @private\n         * @param {Object} args The event arguments.\n         * @param {string} args.newRootId Id of the new root.\n         * @param {string} args.previousRootId Id of the previous root.\n         * @param {boolean} args.redraw Whether to redraw the chart after.\n         * @param {Object} args.series The series to update the root of.\n         * @param {string} [args.trigger=undefined] The action which\n         * triggered the event. Undefined if the setRootNode is called\n         * directly.\n             */\n        const defaultFn = function (args) {\n            const series = args.series;\n            // Store previous and new root ids on the series.\n            series.idPreviousRoot = args.previousRootId;\n            series.rootNode = args.newRootId;\n            // Redraw the chart\n            series.isDirty = true; // Force redraw\n            if (args.redraw) {\n                series.chart.redraw();\n            }\n        };\n        // Fire setRootNode event.\n        TreemapSeries_fireEvent(series, 'setRootNode', eventArgs, defaultFn);\n    }\n    /**\n     * Workaround for `inactive` state. Since `series.opacity` option is\n     * already reserved, don't use that state at all by disabling\n     * `inactiveOtherPoints` and not inheriting states by points.\n     * @private\n     */\n    setState(state) {\n        this.options.inactiveOtherPoints = true;\n        super.setState(state, false);\n        this.options.inactiveOtherPoints = false;\n    }\n    setTreeValues(tree) {\n        const series = this, options = series.options, idRoot = series.rootNode, mapIdToNode = series.nodeMap, nodeRoot = mapIdToNode[idRoot], levelIsConstant = (typeof options.levelIsConstant === 'boolean' ?\n            options.levelIsConstant :\n            true), children = [], point = series.points[tree.i];\n        // First give the children some values\n        let childrenTotal = 0;\n        for (let child of tree.children) {\n            child = series.setTreeValues(child);\n            children.push(child);\n            if (!child.ignore) {\n                childrenTotal += child.val;\n            }\n        }\n        // Sort the children\n        stableSort(children, (a, b) => ((a.sortIndex || 0) - (b.sortIndex || 0)));\n        // Set the values\n        let val = TreemapSeries_pick(point?.simulatedValue, point?.options.value, childrenTotal);\n        if (point) {\n            point.value = val;\n        }\n        if (point?.isGroup && options.cluster?.reductionFactor) {\n            val /= options.cluster.reductionFactor;\n        }\n        if (tree.parentNode?.point?.isGroup && series.rootNode !== tree.parent) {\n            tree.visible = false;\n        }\n        TreemapSeries_extend(tree, {\n            children: children,\n            childrenTotal: childrenTotal,\n            // Ignore this node if point is not visible\n            ignore: !(TreemapSeries_pick(point?.visible, true) && (val > 0)),\n            isLeaf: tree.visible && !childrenTotal,\n            isGroup: point?.isGroup,\n            levelDynamic: (tree.level - (levelIsConstant ? 0 : nodeRoot.level)),\n            name: TreemapSeries_pick(point?.name, ''),\n            sortIndex: TreemapSeries_pick(point?.sortIndex, -val),\n            val: val\n        });\n        return tree;\n    }\n    sliceAndDice(parent, children) {\n        return this.algorithmFill(true, parent, children);\n    }\n    squarified(parent, children) {\n        return this.algorithmLowAspectRatio(true, parent, children);\n    }\n    strip(parent, children) {\n        return this.algorithmLowAspectRatio(false, parent, children);\n    }\n    stripes(parent, children) {\n        return this.algorithmFill(false, parent, children);\n    }\n    translate(tree) {\n        const series = this, options = series.options, applyGrouping = !tree;\n        let // NOTE: updateRootId modifies series.\n        rootId = TreemapSeries_updateRootId(series), rootNode, pointValues, seriesArea, val;\n        if (!tree && !rootId.startsWith('highcharts-grouped-treemap-points-')) {\n            // Group points are removed, but not destroyed during generatePoints\n            (this.points || []).forEach((point) => {\n                if (point.isGroup) {\n                    point.destroy();\n                }\n            });\n            // Call prototype function\n            super.translate();\n            // @todo Only if series.isDirtyData is true\n            tree = series.getTree();\n        }\n        // Ensure `tree` and `series.tree` are synchronized\n        series.tree = tree = tree || series.tree;\n        rootNode = series.nodeMap[rootId];\n        if (rootId !== '' && !rootNode) {\n            series.setRootNode('', false);\n            rootId = series.rootNode;\n            rootNode = series.nodeMap[rootId];\n        }\n        if (!rootNode.point?.isGroup) {\n            series.mapOptionsToLevel = TreemapSeries_getLevelOptions({\n                from: rootNode.level + 1,\n                levels: options.levels,\n                to: tree.height,\n                defaults: {\n                    levelIsConstant: series.options.levelIsConstant,\n                    colorByPoint: options.colorByPoint\n                }\n            });\n        }\n        // Parents of the root node is by default visible\n        Treemap_TreemapUtilities.recursive(series.nodeMap[series.rootNode], (node) => {\n            const p = node.parent;\n            let next = false;\n            node.visible = true;\n            if (p || p === '') {\n                next = series.nodeMap[p];\n            }\n            return next;\n        });\n        // Children of the root node is by default visible\n        Treemap_TreemapUtilities.recursive(series.nodeMap[series.rootNode].children, (children) => {\n            let next = false;\n            for (const child of children) {\n                child.visible = true;\n                if (child.children.length) {\n                    next = (next || []).concat(child.children);\n                }\n            }\n            return next;\n        });\n        series.setTreeValues(tree);\n        // Calculate plotting values.\n        series.axisRatio = (series.xAxis.len / series.yAxis.len);\n        series.nodeMap[''].pointValues = pointValues = {\n            x: 0,\n            y: 0,\n            width: axisMax,\n            height: axisMax\n        };\n        series.nodeMap[''].values = seriesArea = TreemapSeries_merge(pointValues, {\n            width: (pointValues.width * series.axisRatio),\n            direction: (options.layoutStartingDirection === 'vertical' ? 0 : 1),\n            val: tree.val\n        });\n        // We need to pre-render the data labels in order to measure the height\n        // of data label group\n        if (this.hasOutsideDataLabels || this.hadOutsideDataLabels) {\n            this.drawDataLabels();\n        }\n        series.calculateChildrenAreas(tree, seriesArea);\n        // Logic for point colors\n        if (!series.colorAxis &&\n            !options.colorByPoint) {\n            series.setColorRecursive(series.tree);\n        }\n        // Update axis extremes according to the root node.\n        if (options.allowTraversingTree && rootNode.pointValues) {\n            val = rootNode.pointValues;\n            series.xAxis.setExtremes(val.x, val.x + val.width, false);\n            series.yAxis.setExtremes(val.y, val.y + val.height, false);\n            series.xAxis.setScale();\n            series.yAxis.setScale();\n        }\n        // Assign values to points.\n        series.setPointValues();\n        if (applyGrouping) {\n            series.applyTreeGrouping();\n        }\n    }\n}\nTreemapSeries.defaultOptions = TreemapSeries_merge(ScatterSeries.defaultOptions, Treemap_TreemapSeriesDefaults);\nTreemapSeries_extend(TreemapSeries.prototype, {\n    buildKDTree: noop,\n    colorAttribs: Series_ColorMapComposition.seriesMembers.colorAttribs,\n    colorKey: 'colorValue', // Point color option key\n    directTouch: true,\n    getExtremesFromAll: true,\n    getSymbol: noop,\n    optionalAxis: 'colorAxis',\n    parallelArrays: ['x', 'y', 'value', 'colorValue'],\n    pointArrayMap: ['value', 'colorValue'],\n    pointClass: Treemap_TreemapPoint,\n    NodeClass: Treemap_TreemapNode,\n    trackerGroups: ['group', 'dataLabelsGroup'],\n    utils: Treemap_TreemapUtilities\n});\nSeries_ColorMapComposition.compose(TreemapSeries);\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('treemap', TreemapSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treemap_TreemapSeries = ((/* unused pure expression or super */ null && (TreemapSeries)));\n\n;// ./code/es-modules/Series/CenteredUtilities.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { deg2rad } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nconst { fireEvent: CenteredUtilities_fireEvent, isNumber: CenteredUtilities_isNumber, pick: CenteredUtilities_pick, relativeLength: CenteredUtilities_relativeLength } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/**\n * @private\n */\nvar CenteredUtilities;\n(function (CenteredUtilities) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * Get the center of the pie based on the size and center options relative\n     * to the plot area. Borrowed by the polar and gauge series types.\n     *\n     * @private\n     * @function Highcharts.CenteredSeriesMixin.getCenter\n     */\n    function getCenter() {\n        const options = this.options, chart = this.chart, slicingRoom = 2 * (options.slicedOffset || 0), plotWidth = chart.plotWidth - 2 * slicingRoom, plotHeight = chart.plotHeight - 2 * slicingRoom, centerOption = options.center, smallestSize = Math.min(plotWidth, plotHeight), thickness = options.thickness;\n        let handleSlicingRoom, size = options.size, innerSize = options.innerSize || 0, i, value;\n        if (typeof size === 'string') {\n            size = parseFloat(size);\n        }\n        if (typeof innerSize === 'string') {\n            innerSize = parseFloat(innerSize);\n        }\n        const positions = [\n            CenteredUtilities_pick(centerOption?.[0], '50%'),\n            CenteredUtilities_pick(centerOption?.[1], '50%'),\n            // Prevent from negative values\n            CenteredUtilities_pick(size && size < 0 ? void 0 : options.size, '100%'),\n            CenteredUtilities_pick(innerSize && innerSize < 0 ? void 0 : options.innerSize || 0, '0%')\n        ];\n        // No need for inner size in angular (gauges) series but still required\n        // for pie series\n        if (chart.angular && !(this instanceof (highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default()))) {\n            positions[3] = 0;\n        }\n        for (i = 0; i < 4; ++i) {\n            value = positions[i];\n            handleSlicingRoom = i < 2 || (i === 2 && /%$/.test(value));\n            // I == 0: centerX, relative to width\n            // i == 1: centerY, relative to height\n            // i == 2: size, relative to smallestSize\n            // i == 3: innerSize, relative to size\n            positions[i] = CenteredUtilities_relativeLength(value, [plotWidth, plotHeight, smallestSize, positions[2]][i]) + (handleSlicingRoom ? slicingRoom : 0);\n        }\n        // Inner size cannot be larger than size (#3632)\n        if (positions[3] > positions[2]) {\n            positions[3] = positions[2];\n        }\n        // Thickness overrides innerSize, need to be less than pie size (#6647)\n        if (CenteredUtilities_isNumber(thickness) &&\n            thickness * 2 < positions[2] && thickness > 0) {\n            positions[3] = positions[2] - thickness * 2;\n        }\n        CenteredUtilities_fireEvent(this, 'afterGetCenter', { positions });\n        return positions;\n    }\n    CenteredUtilities.getCenter = getCenter;\n    /**\n     * GetStartAndEndRadians - Calculates start and end angles in radians.\n     * Used in series types such as pie and sunburst.\n     *\n     * @private\n     * @function Highcharts.CenteredSeriesMixin.getStartAndEndRadians\n     *\n     * @param {number} [start]\n     *        Start angle in degrees.\n     *\n     * @param {number} [end]\n     *        Start angle in degrees.\n     *\n     * @return {Highcharts.RadianAngles}\n     *         Returns an object containing start and end angles as radians.\n     */\n    function getStartAndEndRadians(start, end) {\n        const startAngle = CenteredUtilities_isNumber(start) ? start : 0, // Must be a number\n        endAngle = ((CenteredUtilities_isNumber(end) && // Must be a number\n            end > startAngle && // Must be larger than the start angle\n            // difference must be less than 360 degrees\n            (end - startAngle) < 360) ?\n            end :\n            startAngle + 360), correction = -90;\n        return {\n            start: deg2rad * (startAngle + correction),\n            end: deg2rad * (endAngle + correction)\n        };\n    }\n    CenteredUtilities.getStartAndEndRadians = getStartAndEndRadians;\n})(CenteredUtilities || (CenteredUtilities = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Series_CenteredUtilities = (CenteredUtilities);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * @private\n * @interface Highcharts.RadianAngles\n */ /**\n* @name Highcharts.RadianAngles#end\n* @type {number}\n*/ /**\n* @name Highcharts.RadianAngles#start\n* @type {number}\n*/\n''; // Keeps doclets above in JS file\n\n;// ./code/es-modules/Series/Sunburst/SunburstPoint.js\n/* *\n *\n *  This module implements sunburst charts in Highcharts.\n *\n *  (c) 2016-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { series: { prototype: { pointClass: Point } }, seriesTypes: { treemap: { prototype: { pointClass: SunburstPoint_TreemapPoint } } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { correctFloat: SunburstPoint_correctFloat, extend: SunburstPoint_extend, pInt } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass SunburstPoint extends SunburstPoint_TreemapPoint {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getDataLabelPath(label) {\n        const renderer = this.series.chart.renderer, shapeArgs = this.shapeExisting, r = shapeArgs.r + pInt(label.options?.distance || 0);\n        let start = shapeArgs.start, end = shapeArgs.end;\n        const angle = start + (end - start) / 2; // Arc middle value\n        let upperHalf = angle < 0 &&\n            angle > -Math.PI ||\n            angle > Math.PI, moreThanHalf;\n        // Check if point is a full circle\n        if (start === -Math.PI / 2 &&\n            SunburstPoint_correctFloat(end) === SunburstPoint_correctFloat(Math.PI * 1.5)) {\n            start = -Math.PI + Math.PI / 360;\n            end = -Math.PI / 360;\n            upperHalf = true;\n        }\n        // Check if dataLabels should be render in the upper half of the circle\n        if (end - start > Math.PI) {\n            upperHalf = false;\n            moreThanHalf = true;\n            // Close to the full circle, add some padding so that the SVG\n            // renderer treats it as separate points (#18884).\n            if ((end - start) > 2 * Math.PI - 0.01) {\n                start += 0.01;\n                end -= 0.01;\n            }\n        }\n        if (this.dataLabelPath) {\n            this.dataLabelPath = this.dataLabelPath.destroy();\n        }\n        // All times\n        this.dataLabelPath = renderer\n            .arc({\n            open: true,\n            longArc: moreThanHalf ? 1 : 0\n        })\n            .attr({\n            start: (upperHalf ? start : end),\n            end: (upperHalf ? end : start),\n            clockwise: +upperHalf,\n            x: shapeArgs.x,\n            y: shapeArgs.y,\n            r: (r + shapeArgs.innerR) / 2\n        })\n            .add(renderer.defs);\n        return this.dataLabelPath;\n    }\n    isValid() {\n        return true;\n    }\n}\nSunburstPoint_extend(SunburstPoint.prototype, {\n    getClassName: Point.prototype.getClassName,\n    haloPath: Point.prototype.haloPath,\n    setState: Point.prototype.setState\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sunburst_SunburstPoint = (SunburstPoint);\n\n;// ./code/es-modules/Series/Sunburst/SunburstUtilities.js\n/* *\n *\n *  This module implements sunburst charts in Highcharts.\n *\n *  (c) 2016-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { seriesTypes: { treemap: SunburstUtilities_TreemapSeries } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { isNumber: SunburstUtilities_isNumber, isObject: SunburstUtilities_isObject, merge: SunburstUtilities_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n * @function calculateLevelSizes\n *\n * @param {Object} levelOptions\n * Map of level to its options.\n *\n * @param {Highcharts.Dictionary<number>} params\n * Object containing number parameters `innerRadius` and `outerRadius`.\n *\n * @return {Highcharts.SunburstSeriesLevelsOptions|undefined}\n * Returns the modified options, or undefined.\n */\nfunction calculateLevelSizes(levelOptions, params) {\n    const p = SunburstUtilities_isObject(params) ? params : {};\n    let result, totalWeight = 0, diffRadius, levels, levelsNotIncluded, remainingSize, from, to;\n    if (SunburstUtilities_isObject(levelOptions)) {\n        result = SunburstUtilities_merge({}, levelOptions);\n        from = SunburstUtilities_isNumber(p.from) ? p.from : 0;\n        to = SunburstUtilities_isNumber(p.to) ? p.to : 0;\n        levels = range(from, to);\n        levelsNotIncluded = Object.keys(result).filter((key) => (levels.indexOf(+key) === -1));\n        diffRadius = remainingSize = SunburstUtilities_isNumber(p.diffRadius) ?\n            p.diffRadius : 0;\n        // Convert percentage to pixels.\n        // Calculate the remaining size to divide between \"weight\" levels.\n        // Calculate total weight to use in conversion from weight to\n        // pixels.\n        for (const level of levels) {\n            const options = result[level], unit = options.levelSize.unit, value = options.levelSize.value;\n            if (unit === 'weight') {\n                totalWeight += value;\n            }\n            else if (unit === 'percentage') {\n                options.levelSize = {\n                    unit: 'pixels',\n                    value: (value / 100) * diffRadius\n                };\n                remainingSize -= options.levelSize.value;\n            }\n            else if (unit === 'pixels') {\n                remainingSize -= value;\n            }\n        }\n        // Convert weight to pixels.\n        for (const level of levels) {\n            const options = result[level];\n            if (options.levelSize.unit === 'weight') {\n                const weight = options.levelSize.value;\n                result[level].levelSize = {\n                    unit: 'pixels',\n                    value: (weight / totalWeight) * remainingSize\n                };\n            }\n        }\n        // Set all levels not included in interval [from,to] to have 0\n        // pixels.\n        for (const level of levelsNotIncluded) {\n            result[level].levelSize = {\n                value: 0,\n                unit: 'pixels'\n            };\n        }\n    }\n    return result;\n}\n/**\n * @private\n */\nfunction getLevelFromAndTo({ level, height }) {\n    //  Never displays level below 1\n    const from = level > 0 ? level : 1;\n    const to = level + height;\n    return { from, to };\n}\n/**\n * TODO introduce step, which should default to 1.\n * @private\n */\nfunction range(from, to) {\n    const result = [];\n    if (SunburstUtilities_isNumber(from) && SunburstUtilities_isNumber(to) && from <= to) {\n        for (let i = from; i <= to; i++) {\n            result.push(i);\n        }\n    }\n    return result;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst SunburstUtilities = {\n    calculateLevelSizes,\n    getLevelFromAndTo,\n    range,\n    recursive: SunburstUtilities_TreemapSeries.prototype.utils.recursive\n};\n/* harmony default export */ const Sunburst_SunburstUtilities = (SunburstUtilities);\n\n;// ./code/es-modules/Series/Sunburst/SunburstNode.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n/* *\n *\n *  Class\n *\n * */\nclass SunburstNode extends Treemap_TreemapNode {\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sunburst_SunburstNode = (SunburstNode);\n\n;// ./code/es-modules/Series/Sunburst/SunburstSeriesDefaults.js\n/* *\n *\n *  This module implements sunburst charts in Highcharts.\n *\n *  (c) 2016-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A Sunburst displays hierarchical data, where a level in the hierarchy is\n * represented by a circle. The center represents the root node of the tree.\n * The visualization bears a resemblance to both treemap and pie charts.\n *\n * @sample highcharts/demo/sunburst\n *         Sunburst chart\n *\n * @extends      plotOptions.pie\n * @excluding    allAreas, clip, colorAxis, colorKey, compare, compareBase,\n *               dataGrouping, depth, dragDrop, endAngle, gapSize, gapUnit,\n *               ignoreHiddenPoint, innerSize, joinBy, legendType, linecap,\n *               minSize, navigatorOptions, pointRange\n * @product      highcharts\n * @requires     modules/sunburst\n * @optionparent plotOptions.sunburst\n *\n * @private\n */\nconst SunburstSeriesDefaults = {\n    /**\n     * Options for the breadcrumbs, the navigation at the top leading the\n     * way up through the traversed levels.\n     *\n     * @since 10.0.0\n     * @product   highcharts\n     * @extends   navigation.breadcrumbs\n     * @apioption plotOptions.sunburst.breadcrumbs\n     */\n    /**\n     * Set options on specific levels. Takes precedence over series options,\n     * but not point options.\n     *\n     * @sample highcharts/demo/sunburst\n     *         Sunburst chart\n     *\n     * @type      {Array<*>}\n     * @apioption plotOptions.sunburst.levels\n     */\n    /**\n     * Can set a `borderColor` on all points which lies on the same level.\n     *\n     * @type      {Highcharts.ColorString}\n     * @apioption plotOptions.sunburst.levels.borderColor\n     */\n    /**\n     * Can set a `borderWidth` on all points which lies on the same level.\n     *\n     * @type      {number}\n     * @apioption plotOptions.sunburst.levels.borderWidth\n     */\n    /**\n     * Can set a `borderDashStyle` on all points which lies on the same\n     * level.\n     *\n     * @type      {Highcharts.DashStyleValue}\n     * @apioption plotOptions.sunburst.levels.borderDashStyle\n     */\n    /**\n     * Can set a `color` on all points which lies on the same level.\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @apioption plotOptions.sunburst.levels.color\n     */\n    /**\n     * Determines whether the chart should receive one color per point based\n     * on this level.\n     *\n     * @type      {boolean}\n     * @apioption plotOptions.sunburst.levels.colorByPoint\n     */\n    /**\n     * Can set a `colorVariation` on all points which lies on the same\n     * level.\n     *\n     * @apioption plotOptions.sunburst.levels.colorVariation\n     */\n    /**\n     * The key of a color variation. Currently supports `brightness` only.\n     *\n     * @type      {string}\n     * @apioption plotOptions.sunburst.levels.colorVariation.key\n     */\n    /**\n     * The ending value of a color variation. The last sibling will receive\n     * this value.\n     *\n     * @type      {number}\n     * @apioption plotOptions.sunburst.levels.colorVariation.to\n     */\n    /**\n     * Can set `dataLabels` on all points which lies on the same level.\n     *\n     * @extends   plotOptions.sunburst.dataLabels\n     * @apioption plotOptions.sunburst.levels.dataLabels\n     */\n    /**\n     * Decides which level takes effect from the options set in the levels\n     * object.\n     *\n     * @sample highcharts/demo/sunburst\n     *         Sunburst chart\n     *\n     * @type      {number}\n     * @apioption plotOptions.sunburst.levels.level\n     */\n    /**\n     * Can set a `levelSize` on all points which lies on the same level.\n     *\n     * @type      {Object}\n     * @apioption plotOptions.sunburst.levels.levelSize\n     */\n    /**\n     * When enabled the user can click on a point which is a parent and\n     * zoom in on its children. Deprecated and replaced by\n     * [allowTraversingTree](#plotOptions.sunburst.allowTraversingTree).\n     *\n     * @deprecated\n     * @type      {boolean}\n     * @default   false\n     * @since     6.0.0\n     * @product   highcharts\n     * @apioption plotOptions.sunburst.allowDrillToNode\n     */\n    /**\n     * When enabled the user can click on a point which is a parent and\n     * zoom in on its children.\n     *\n     * @type      {boolean}\n     * @default   false\n     * @since     7.0.3\n     * @product   highcharts\n     * @apioption plotOptions.sunburst.allowTraversingTree\n     */\n    /**\n     * The center of the sunburst chart relative to the plot area. Can be\n     * percentages or pixel values.\n     *\n     * @sample {highcharts} highcharts/plotoptions/pie-center/\n     *         Centered at 100, 100\n     *\n     * @type    {Array<number|string>}\n     * @default [\"50%\", \"50%\"]\n     * @product highcharts\n     *\n     * @private\n     */\n    center: ['50%', '50%'],\n    /**\n     * @product highcharts\n     *\n     * @private\n     */\n    clip: false,\n    colorByPoint: false,\n    /**\n     * Disable inherited opacity from Treemap series.\n     *\n     * @ignore-option\n     *\n     * @private\n     */\n    opacity: 1,\n    /**\n     * @declare Highcharts.SeriesSunburstDataLabelsOptionsObject\n     *\n     * @private\n     */\n    dataLabels: {\n        allowOverlap: true,\n        defer: true,\n        /**\n         * Decides how the data label will be rotated relative to the\n         * perimeter of the sunburst. Valid values are `circular`, `auto`,\n         * `parallel` and `perpendicular`. When `circular`, the best fit\n         * will be computed for the point, so that the label is curved\n         * around the center when there is room for it, otherwise\n         * perpendicular. The legacy `auto` option works similar to\n         * `circular`, but instead of curving the labels they are tangent to\n         * the perimeter.\n         *\n         * The `rotation` option takes precedence over `rotationMode`.\n         *\n         * @type       {string}\n         * @sample {highcharts}\n         *         highcharts/plotoptions/sunburst-datalabels-rotationmode-circular/\n         *         Circular rotation mode\n         * @validvalue [\"auto\", \"perpendicular\", \"parallel\", \"circular\"]\n         * @since      6.0.0\n         */\n        rotationMode: 'circular',\n        style: {\n            /** @internal */\n            textOverflow: 'ellipsis'\n        }\n    },\n    /**\n     * Which point to use as a root in the visualization.\n     *\n     * @type {string}\n     *\n     * @private\n     */\n    rootId: void 0,\n    /**\n     * Used together with the levels and `allowDrillToNode` options. When\n     * set to false the first level visible when drilling is considered\n     * to be level one. Otherwise the level will be the same as the tree\n     * structure.\n     *\n     * @private\n     */\n    levelIsConstant: true,\n    /**\n     * Determines the width of the ring per level.\n     *\n     * @sample {highcharts} highcharts/plotoptions/sunburst-levelsize/\n     *         Sunburst with various sizes per level\n     *\n     * @since 6.0.5\n     *\n     * @private\n     */\n    levelSize: {\n        /**\n         * The value used for calculating the width of the ring. Its' affect\n         * is determined by `levelSize.unit`.\n         *\n         * @sample {highcharts} highcharts/plotoptions/sunburst-levelsize/\n         *         Sunburst with various sizes per level\n         */\n        value: 1,\n        /**\n         * How to interpret `levelSize.value`.\n         *\n         * - `percentage` gives a width relative to result of outer radius\n         *   minus inner radius.\n         *\n         * - `pixels` gives the ring a fixed width in pixels.\n         *\n         * - `weight` takes the remaining width after percentage and pixels,\n         *   and distributes it across all \"weighted\" levels. The value\n         *   relative to the sum of all weights determines the width.\n         *\n         * @sample {highcharts} highcharts/plotoptions/sunburst-levelsize/\n         *         Sunburst with various sizes per level\n         *\n         * @validvalue [\"percentage\", \"pixels\", \"weight\"]\n         */\n        unit: 'weight'\n    },\n    /**\n     * Options for the button appearing when traversing down in a sunburst.\n     * Since v9.3.3 the `traverseUpButton` is replaced by `breadcrumbs`.\n     *\n     * @extends   plotOptions.treemap.traverseUpButton\n     * @since     6.0.0\n     * @deprecated\n     * @apioption plotOptions.sunburst.traverseUpButton\n     *\n     */\n    /**\n     * If a point is sliced, moved out from the center, how many pixels\n     * should it be moved?.\n     *\n     * @sample highcharts/plotoptions/sunburst-sliced\n     *         Sliced sunburst\n     *\n     * @since 6.0.4\n     *\n     * @private\n     */\n    slicedOffset: 10\n};\n/**\n * A `sunburst` series. If the [type](#series.sunburst.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.sunburst\n * @excluding dataParser, dataURL, stack, dataSorting, boostThreshold,\n *            boostBlending\n * @product   highcharts\n * @requires  modules/sunburst\n * @apioption series.sunburst\n */\n/**\n * @type      {Array<number|null|*>}\n * @extends   series.treemap.data\n * @excluding x, y\n * @product   highcharts\n * @apioption series.sunburst.data\n */\n/**\n * @type      {Highcharts.SeriesSunburstDataLabelsOptionsObject|Array<Highcharts.SeriesSunburstDataLabelsOptionsObject>}\n * @product   highcharts\n * @apioption series.sunburst.data.dataLabels\n */\n/**\n * The value of the point, resulting in a relative area of the point\n * in the sunburst.\n *\n * @type      {number|null}\n * @since     6.0.0\n * @product   highcharts\n * @apioption series.sunburst.data.value\n */\n/**\n * Use this option to build a tree structure. The value should be the id of the\n * point which is the parent. If no points has a matching id, or this option is\n * undefined, then the parent will be set to the root.\n *\n * @type      {string}\n * @since     6.0.0\n * @product   highcharts\n * @apioption series.sunburst.data.parent\n */\n/**\n  * Whether to display a slice offset from the center. When a sunburst point is\n  * sliced, its children are also offset.\n  *\n  * @sample highcharts/plotoptions/sunburst-sliced\n  *         Sliced sunburst\n  *\n  * @type      {boolean}\n  * @default   false\n  * @since     6.0.4\n  * @product   highcharts\n  * @apioption series.sunburst.data.sliced\n  */\n''; // Detach doclets above\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sunburst_SunburstSeriesDefaults = (SunburstSeriesDefaults);\n\n;// ./code/es-modules/Extensions/TextPath.js\n/* *\n *\n *  Highcharts module with textPath functionality.\n *\n *  (c) 2009-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { deg2rad: TextPath_deg2rad } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nconst { addEvent: TextPath_addEvent, merge: TextPath_merge, uniqueKey, defined: TextPath_defined, extend: TextPath_extend } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/**\n * Set a text path for a `text` or `label` element, allowing the text to\n * flow along a path.\n *\n * In order to unset the path for an existing element, call `setTextPath`\n * with `{ enabled: false }` as the second argument.\n *\n * Text path support is not bundled into `highcharts.js`, and requires the\n * `modules/textpath.js` file. However, it is included in the script files of\n * those series types that use it by default\n *\n * @sample highcharts/members/renderer-textpath/ Text path demonstrated\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {Highcharts.SVGElement|undefined} path\n *        Path to follow. If undefined, it allows changing options for the\n *        existing path.\n *\n * @param {Highcharts.DataLabelsTextPathOptionsObject} textPathOptions\n *        Options.\n *\n * @return {Highcharts.SVGElement} Returns the SVGElement for chaining.\n */\nfunction setTextPath(path, textPathOptions) {\n    // Defaults\n    textPathOptions = TextPath_merge(true, {\n        enabled: true,\n        attributes: {\n            dy: -5,\n            startOffset: '50%',\n            textAnchor: 'middle'\n        }\n    }, textPathOptions);\n    const url = this.renderer.url, textWrapper = this.text || this, textPath = textWrapper.textPath, { attributes, enabled } = textPathOptions;\n    path = path || (textPath && textPath.path);\n    // Remove previously added event\n    if (textPath) {\n        textPath.undo();\n    }\n    if (path && enabled) {\n        const undo = TextPath_addEvent(textWrapper, 'afterModifyTree', (e) => {\n            if (path && enabled) {\n                // Set ID for the path\n                let textPathId = path.attr('id');\n                if (!textPathId) {\n                    path.attr('id', textPathId = uniqueKey());\n                }\n                // Set attributes for the <text>\n                const textAttribs = {\n                    // `dx`/`dy` options must by set on <text> (parent), the\n                    // rest should be set on <textPath>\n                    x: 0,\n                    y: 0\n                };\n                if (TextPath_defined(attributes.dx)) {\n                    textAttribs.dx = attributes.dx;\n                    delete attributes.dx;\n                }\n                if (TextPath_defined(attributes.dy)) {\n                    textAttribs.dy = attributes.dy;\n                    delete attributes.dy;\n                }\n                textWrapper.attr(textAttribs);\n                // Handle label properties\n                this.attr({ transform: '' });\n                if (this.box) {\n                    this.box = this.box.destroy();\n                }\n                // Wrap the nodes in a textPath\n                const children = e.nodes.slice(0);\n                e.nodes.length = 0;\n                e.nodes[0] = {\n                    tagName: 'textPath',\n                    attributes: TextPath_extend(attributes, {\n                        'text-anchor': attributes.textAnchor,\n                        href: `${url}#${textPathId}`\n                    }),\n                    children\n                };\n            }\n        });\n        // Set the reference\n        textWrapper.textPath = { path, undo };\n    }\n    else {\n        textWrapper.attr({ dx: 0, dy: 0 });\n        delete textWrapper.textPath;\n    }\n    if (this.added) {\n        // Rebuild text after added\n        textWrapper.textCache = '';\n        this.renderer.buildText(textWrapper);\n    }\n    return this;\n}\n/**\n * Attach a polygon to a bounding box if the element contains a textPath.\n *\n * @function Highcharts.SVGElement#setPolygon\n *\n * @param {any} event\n *        An event containing a bounding box object\n *\n * @return {Highcharts.BBoxObject} Returns the bounding box object.\n */\nfunction setPolygon(event) {\n    const bBox = event.bBox, tp = this.element?.querySelector('textPath');\n    if (tp) {\n        const polygon = [], { b, h } = this.renderer.fontMetrics(this.element), descender = h - b, lineCleanerRegex = new RegExp('(<tspan>|' +\n            '<tspan(?!\\\\sclass=\"highcharts-br\")[^>]*>|' +\n            '<\\\\/tspan>)', 'g'), lines = tp\n            .innerHTML\n            .replace(lineCleanerRegex, '')\n            .split(/<tspan class=\"highcharts-br\"[^>]*>/), numOfLines = lines.length;\n        // Calculate top and bottom coordinates for\n        // either the start or the end of a single\n        // character, and append it to the polygon.\n        const appendTopAndBottom = (charIndex, positionOfChar) => {\n            const { x, y } = positionOfChar, rotation = (tp.getRotationOfChar(charIndex) - 90) * TextPath_deg2rad, cosRot = Math.cos(rotation), sinRot = Math.sin(rotation);\n            return [\n                [\n                    x - descender * cosRot,\n                    y - descender * sinRot\n                ],\n                [\n                    x + b * cosRot,\n                    y + b * sinRot\n                ]\n            ];\n        };\n        for (let i = 0, lineIndex = 0; lineIndex < numOfLines; lineIndex++) {\n            const line = lines[lineIndex], lineLen = line.length;\n            for (let lineCharIndex = 0; lineCharIndex < lineLen; lineCharIndex += 5) {\n                try {\n                    const srcCharIndex = (i +\n                        lineCharIndex +\n                        lineIndex), [lower, upper] = appendTopAndBottom(srcCharIndex, tp.getStartPositionOfChar(srcCharIndex));\n                    if (lineCharIndex === 0) {\n                        polygon.push(upper);\n                        polygon.push(lower);\n                    }\n                    else {\n                        if (lineIndex === 0) {\n                            polygon.unshift(upper);\n                        }\n                        if (lineIndex === numOfLines - 1) {\n                            polygon.push(lower);\n                        }\n                    }\n                }\n                catch (e) {\n                    // Safari fails on getStartPositionOfChar even if the\n                    // character is within the `textContent.length`\n                    break;\n                }\n            }\n            i += lineLen - 1;\n            try {\n                const srcCharIndex = i + lineIndex, charPos = tp.getEndPositionOfChar(srcCharIndex), [lower, upper] = appendTopAndBottom(srcCharIndex, charPos);\n                polygon.unshift(upper);\n                polygon.unshift(lower);\n            }\n            catch (e) {\n                // Safari fails on getStartPositionOfChar even if the character\n                // is within the `textContent.length`\n                break;\n            }\n        }\n        // Close it\n        if (polygon.length) {\n            polygon.push(polygon[0].slice());\n        }\n        bBox.polygon = polygon;\n    }\n    return bBox;\n}\n/**\n * Draw text along a textPath for a dataLabel.\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {any} event\n *        An event containing label options\n *\n * @return {void}\n */\nfunction drawTextPath(event) {\n    const labelOptions = event.labelOptions, point = event.point, textPathOptions = (labelOptions[point.formatPrefix + 'TextPath'] ||\n        labelOptions.textPath);\n    if (textPathOptions && !labelOptions.useHTML) {\n        this.setTextPath(point.getDataLabelPath?.(this) || point.graphic, textPathOptions);\n        if (point.dataLabelPath &&\n            !textPathOptions.enabled) {\n            // Clean the DOM\n            point.dataLabelPath = (point.dataLabelPath.destroy());\n        }\n    }\n}\nfunction compose(SVGElementClass) {\n    TextPath_addEvent(SVGElementClass, 'afterGetBBox', setPolygon);\n    TextPath_addEvent(SVGElementClass, 'beforeAddingDataLabel', drawTextPath);\n    const svgElementProto = SVGElementClass.prototype;\n    if (!svgElementProto.setTextPath) {\n        svgElementProto.setTextPath = setTextPath;\n    }\n}\nconst TextPath = {\n    compose\n};\n/* harmony default export */ const Extensions_TextPath = (TextPath);\n\n;// ./code/es-modules/Series/Sunburst/SunburstSeries.js\n/* *\n *\n *  This module implements sunburst charts in Highcharts.\n *\n *  (c) 2016-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { getCenter, getStartAndEndRadians } = Series_CenteredUtilities;\n\nconst { noop: SunburstSeries_noop } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { column: SunburstSeries_ColumnSeries, treemap: SunburstSeries_TreemapSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\n\n\nconst { getColor: SunburstSeries_getColor, getLevelOptions: SunburstSeries_getLevelOptions, setTreeValues: SunburstSeries_setTreeValues, updateRootId: SunburstSeries_updateRootId } = Series_TreeUtilities;\n\n\n\nconst { defined: SunburstSeries_defined, error: SunburstSeries_error, extend: SunburstSeries_extend, fireEvent: SunburstSeries_fireEvent, isNumber: SunburstSeries_isNumber, isObject: SunburstSeries_isObject, isString: SunburstSeries_isString, merge: SunburstSeries_merge, splat: SunburstSeries_splat } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nExtensions_TextPath.compose((highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default()));\n/* *\n *\n *  Constants\n *\n * */\nconst rad2deg = 180 / Math.PI;\n/* *\n *\n *  Functions\n *\n * */\n/** @private */\nfunction isBoolean(x) {\n    return typeof x === 'boolean';\n}\n/**\n * Find a set of coordinates given a start coordinates, an angle, and a\n * distance.\n *\n * @private\n * @function getEndPoint\n *\n * @param {number} x\n *        Start coordinate x\n *\n * @param {number} y\n *        Start coordinate y\n *\n * @param {number} angle\n *        Angle in radians\n *\n * @param {number} distance\n *        Distance from start to end coordinates\n *\n * @return {Highcharts.SVGAttributes}\n *         Returns the end coordinates, x and y.\n */\nconst getEndPoint = function getEndPoint(x, y, angle, distance) {\n    return {\n        x: x + (Math.cos(angle) * distance),\n        y: y + (Math.sin(angle) * distance)\n    };\n};\n/** @private */\nfunction getDlOptions(params) {\n    // Set options to new object to avoid problems with scope\n    const point = params.point, shape = SunburstSeries_isObject(params.shapeArgs) ? params.shapeArgs : {}, { end = 0, radius = 0, start = 0 } = shape, optionsPoint = (SunburstSeries_isObject(params.optionsPoint) ?\n        params.optionsPoint.dataLabels :\n        {}), \n    // The splat was used because levels dataLabels\n    // options doesn't work as an array\n    optionsLevel = SunburstSeries_splat(SunburstSeries_isObject(params.level) ?\n        params.level.dataLabels :\n        {})[0], options = SunburstSeries_merge(optionsLevel, optionsPoint), style = options.style = options.style || {}, { innerArcLength = 0, outerArcLength = 0 } = point;\n    let rotationRad, rotation, rotationMode = options.rotationMode, width = SunburstSeries_defined(style.width) ?\n        parseInt(style.width || '0', 10) : void 0;\n    if (!SunburstSeries_isNumber(options.rotation)) {\n        if (rotationMode === 'auto' || rotationMode === 'circular') {\n            if (options.useHTML &&\n                rotationMode === 'circular') {\n                // Change rotationMode to 'auto' to avoid using text paths\n                // for HTML labels, see #18953\n                rotationMode = 'auto';\n            }\n            if (innerArcLength < 1 && outerArcLength > radius) {\n                rotationRad = 0;\n                // Trigger setTextPath function to get textOutline etc.\n                if (point.dataLabelPath && rotationMode === 'circular') {\n                    options.textPath = {\n                        enabled: true\n                    };\n                }\n                // If the slice is less than 180 degrees, set a reasonable width\n                // for fitting into the open slice (#22532)\n                if (end - start < Math.PI) {\n                    width = radius * 0.7;\n                }\n            }\n            else if (innerArcLength > 1 && outerArcLength > 1.5 * radius) {\n                if (rotationMode === 'circular') {\n                    options.textPath = {\n                        enabled: true,\n                        attributes: {\n                            dy: 5\n                        }\n                    };\n                }\n                else {\n                    rotationMode = 'parallel';\n                }\n            }\n            else {\n                // Trigger the destroyTextPath function\n                if (point.dataLabel?.textPath &&\n                    rotationMode === 'circular') {\n                    options.textPath = {\n                        enabled: false\n                    };\n                }\n                rotationMode = 'perpendicular';\n            }\n        }\n        if (rotationMode !== 'auto' && rotationMode !== 'circular') {\n            if (point.dataLabel?.textPath) {\n                options.textPath = {\n                    enabled: false\n                };\n            }\n            rotationRad = end - (end - start) / 2;\n        }\n        if (rotationMode === 'parallel') {\n            width = Math.min(radius * 2.5, (outerArcLength + innerArcLength) / 2);\n        }\n        else {\n            if (!SunburstSeries_defined(width) && radius) {\n                width = point.node.level === 1 ? 2 * radius : radius;\n            }\n        }\n        if (rotationMode === 'perpendicular') {\n            // 16 is the inferred line height. We don't know the real line\n            // yet because the label is not rendered. A better approach for this\n            // would be to hide the label from the `alignDataLabel` function\n            // when the actual line height is known.\n            const h = 16;\n            if (outerArcLength < h) {\n                width = 1;\n            }\n            else if (shape.radius) {\n                style.lineClamp = Math.floor(innerArcLength / h) || 1;\n                // When the slice is narrow (< 16px) in the inner end, compute a\n                // safe margin to avoid the label overlapping the border\n                // (#22532)\n                const safeMargin = innerArcLength < h ?\n                    radius * ((h - innerArcLength) /\n                        (outerArcLength - innerArcLength)) :\n                    0;\n                width = radius - safeMargin;\n            }\n        }\n        // Apply padding (#8515)\n        width = Math.max((width || 0) - 2 * (options.padding || 0), 1);\n        rotation = ((rotationRad || 0) * rad2deg) % 180;\n        if (rotationMode === 'parallel') {\n            rotation -= 90;\n        }\n        // Prevent text from rotating upside down\n        if (rotation > 90) {\n            rotation -= 180;\n        }\n        else if (rotation < -90) {\n            rotation += 180;\n        }\n        options.rotation = rotation;\n    }\n    if (options.textPath) {\n        if (point.shapeExisting.innerR === 0 &&\n            options.textPath.enabled) {\n            // Enable rotation to render text\n            options.rotation = 0;\n            // Center dataLabel - disable textPath\n            options.textPath.enabled = false;\n            // Setting width and padding\n            width = Math.max((point.shapeExisting.r * 2) -\n                2 * (options.padding || 0), 1);\n        }\n        else if (point.dlOptions?.textPath &&\n            !point.dlOptions.textPath.enabled &&\n            rotationMode === 'circular') {\n            // Bring dataLabel back if was a center dataLabel\n            options.textPath.enabled = true;\n        }\n        if (options.textPath.enabled) {\n            // Enable rotation to render text\n            options.rotation = 0;\n            // Setting width and padding\n            width = Math.max((outerArcLength + innerArcLength) / 2 -\n                2 * (options.padding || 0), 1);\n            style.whiteSpace = 'nowrap';\n        }\n    }\n    style.width = width + 'px';\n    return options;\n}\n/** @private */\nfunction getAnimation(shape, params) {\n    const point = params.point, radians = params.radians, innerR = params.innerR, idRoot = params.idRoot, idPreviousRoot = params.idPreviousRoot, shapeExisting = params.shapeExisting, shapeRoot = params.shapeRoot, shapePreviousRoot = params.shapePreviousRoot, visible = params.visible;\n    let from = {}, to = {\n        end: shape.end,\n        start: shape.start,\n        innerR: shape.innerR,\n        r: shape.r,\n        x: shape.x,\n        y: shape.y\n    };\n    if (visible) {\n        // Animate points in\n        if (!point.graphic && shapePreviousRoot) {\n            if (idRoot === point.id) {\n                from = {\n                    start: radians.start,\n                    end: radians.end\n                };\n            }\n            else {\n                from = (shapePreviousRoot.end <= shape.start) ? {\n                    start: radians.end,\n                    end: radians.end\n                } : {\n                    start: radians.start,\n                    end: radians.start\n                };\n            }\n            // Animate from center and outwards.\n            from.innerR = from.r = innerR;\n        }\n    }\n    else {\n        // Animate points out\n        if (point.graphic) {\n            if (idPreviousRoot === point.id) {\n                to = {\n                    innerR: innerR,\n                    r: innerR\n                };\n            }\n            else if (shapeRoot) {\n                to = (shapeRoot.end <= shapeExisting.start) ?\n                    {\n                        innerR: innerR,\n                        r: innerR,\n                        start: radians.end,\n                        end: radians.end\n                    } : {\n                    innerR: innerR,\n                    r: innerR,\n                    start: radians.start,\n                    end: radians.start\n                };\n            }\n        }\n    }\n    return {\n        from: from,\n        to: to\n    };\n}\n/** @private */\nfunction getDrillId(point, idRoot, mapIdToNode) {\n    const node = point.node;\n    let drillId, nodeRoot;\n    if (!node.isLeaf) {\n        // When it is the root node, the drillId should be set to parent.\n        if (idRoot === point.id) {\n            nodeRoot = mapIdToNode[idRoot];\n            drillId = nodeRoot.parent;\n        }\n        else {\n            drillId = point.id;\n        }\n    }\n    return drillId;\n}\n/** @private */\nfunction cbSetTreeValuesBefore(node, options) {\n    const mapIdToNode = options.mapIdToNode, parent = node.parent, nodeParent = parent ? mapIdToNode[parent] : void 0, series = options.series, chart = series.chart, points = series.points, point = points[node.i], colors = series.options.colors || chart && chart.options.colors, colorInfo = SunburstSeries_getColor(node, {\n        colors: colors,\n        colorIndex: series.colorIndex,\n        index: options.index,\n        mapOptionsToLevel: options.mapOptionsToLevel,\n        parentColor: nodeParent && nodeParent.color,\n        parentColorIndex: nodeParent && nodeParent.colorIndex,\n        series: options.series,\n        siblings: options.siblings\n    });\n    node.color = colorInfo.color;\n    node.colorIndex = colorInfo.colorIndex;\n    if (point) {\n        point.color = node.color;\n        point.colorIndex = node.colorIndex;\n        // Set slicing on node, but avoid slicing the top node.\n        node.sliced = (node.id !== options.idRoot) ? point.sliced : false;\n    }\n    return node;\n}\n/* *\n *\n *  Class\n *\n * */\nclass SunburstSeries extends SunburstSeries_TreemapSeries {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    alignDataLabel(point, dataLabel, labelOptions) {\n        if (labelOptions.textPath && labelOptions.textPath.enabled) {\n            return;\n        }\n        // In sunburst dataLabel may be placed, but this should be reset to\n        // make sure the dataLabel can be aligned to a new position (#21913)\n        dataLabel.placed = false;\n        return super.alignDataLabel.apply(this, arguments);\n    }\n    /**\n     * Animate the slices in. Similar to the animation of polar charts.\n     * @private\n     */\n    animate(init) {\n        const chart = this.chart, center = [\n            chart.plotWidth / 2,\n            chart.plotHeight / 2\n        ], plotLeft = chart.plotLeft, plotTop = chart.plotTop, group = this.group;\n        let attribs;\n        // Initialize the animation\n        if (init) {\n            // Scale down the group and place it in the center\n            attribs = {\n                translateX: center[0] + plotLeft,\n                translateY: center[1] + plotTop,\n                scaleX: 0.001, // #1499\n                scaleY: 0.001,\n                rotation: 10,\n                opacity: 0.01\n            };\n            group.attr(attribs);\n            // Run the animation\n        }\n        else {\n            attribs = {\n                translateX: plotLeft,\n                translateY: plotTop,\n                scaleX: 1,\n                scaleY: 1,\n                rotation: 0,\n                opacity: 1\n            };\n            group.animate(attribs, this.options.animation);\n        }\n    }\n    drawPoints() {\n        const series = this, mapOptionsToLevel = series.mapOptionsToLevel, shapeRoot = series.shapeRoot, group = series.group, hasRendered = series.hasRendered, idRoot = series.rootNode, idPreviousRoot = series.idPreviousRoot, nodeMap = series.nodeMap, nodePreviousRoot = nodeMap[idPreviousRoot], shapePreviousRoot = nodePreviousRoot && nodePreviousRoot.shapeArgs, points = series.points, radians = series.startAndEndRadians, chart = series.chart, optionsChart = chart && chart.options && chart.options.chart || {}, animation = (isBoolean(optionsChart.animation) ?\n            optionsChart.animation :\n            true), positions = series.center, center = {\n            x: positions[0],\n            y: positions[1]\n        }, innerR = positions[3] / 2, renderer = series.chart.renderer, hackDataLabelAnimation = !!(animation &&\n            hasRendered &&\n            idRoot !== idPreviousRoot &&\n            series.dataLabelsGroup);\n        let animateLabels, animateLabelsCalled = false, addedHack = false;\n        if (hackDataLabelAnimation) {\n            series.dataLabelsGroup.attr({ opacity: 0 });\n            animateLabels = function () {\n                const s = series;\n                animateLabelsCalled = true;\n                if (s.dataLabelsGroup) {\n                    s.dataLabelsGroup.animate({\n                        opacity: 1,\n                        visibility: 'inherit'\n                    });\n                }\n            };\n        }\n        for (const point of points) {\n            const node = point.node, level = mapOptionsToLevel[node.level], shapeExisting = (point.shapeExisting || {}), shape = node.shapeArgs || {}, visible = !!(node.visible && node.shapeArgs);\n            let animationInfo, onComplete;\n            // Border radius requires the border-radius.js module. Adding it\n            // here because the SunburstSeries is a mess and I can't find the\n            // regular shapeArgs. Usually shapeArgs are created in the series'\n            // `translate` function and then passed directly on to the renderer\n            // in the `drawPoints` function.\n            shape.borderRadius = series.options.borderRadius;\n            if (hasRendered && animation) {\n                animationInfo = getAnimation(shape, {\n                    center: center,\n                    point: point,\n                    radians: radians,\n                    innerR: innerR,\n                    idRoot: idRoot,\n                    idPreviousRoot: idPreviousRoot,\n                    shapeExisting: shapeExisting,\n                    shapeRoot: shapeRoot,\n                    shapePreviousRoot: shapePreviousRoot,\n                    visible: visible\n                });\n            }\n            else {\n                // When animation is disabled, attr is called from animation.\n                animationInfo = {\n                    to: shape,\n                    from: {}\n                };\n            }\n            SunburstSeries_extend(point, {\n                shapeExisting: shape, // Store for use in animation\n                tooltipPos: [shape.plotX, shape.plotY],\n                drillId: getDrillId(point, idRoot, nodeMap),\n                name: '' + (point.name || point.id || point.index),\n                plotX: shape.plotX, // Used for data label position\n                plotY: shape.plotY, // Used for data label position\n                value: node.val,\n                isInside: visible,\n                isNull: !visible // Used for dataLabels & point.draw\n            });\n            point.dlOptions = getDlOptions({\n                point: point,\n                level: level,\n                optionsPoint: point.options,\n                shapeArgs: shape\n            });\n            if (!addedHack && visible) {\n                addedHack = true;\n                onComplete = animateLabels;\n            }\n            point.draw({\n                animatableAttribs: animationInfo.to,\n                attribs: SunburstSeries_extend(animationInfo.from, (!chart.styledMode && series.pointAttribs(point, (point.selected && 'select')))),\n                onComplete: onComplete,\n                group: group,\n                renderer: renderer,\n                shapeType: 'arc',\n                shapeArgs: shape\n            });\n        }\n        // Draw data labels after points\n        // TODO draw labels one by one to avoid additional looping\n        if (hackDataLabelAnimation && addedHack) {\n            series.hasRendered = false;\n            series.options.dataLabels.defer = true;\n            SunburstSeries_ColumnSeries.prototype.drawDataLabels.call(series);\n            series.hasRendered = true;\n            // If animateLabels is called before labels were hidden, then call\n            // it again.\n            if (animateLabelsCalled) {\n                animateLabels();\n            }\n        }\n        else {\n            SunburstSeries_ColumnSeries.prototype.drawDataLabels.call(series);\n        }\n        series.idPreviousRoot = idRoot;\n    }\n    /**\n     * The layout algorithm for the levels.\n     * @private\n     */\n    layoutAlgorithm(parent, children, options) {\n        let startAngle = parent.start;\n        const range = parent.end - startAngle, total = parent.val, x = parent.x, y = parent.y, radius = ((options &&\n            SunburstSeries_isObject(options.levelSize) &&\n            SunburstSeries_isNumber(options.levelSize.value)) ?\n            options.levelSize.value :\n            0), innerRadius = parent.r, outerRadius = innerRadius + radius, slicedOffset = options && SunburstSeries_isNumber(options.slicedOffset) ?\n            options.slicedOffset :\n            0;\n        return (children || []).reduce((arr, child) => {\n            const percentage = (1 / total) * child.val, radians = percentage * range, radiansCenter = startAngle + (radians / 2), offsetPosition = getEndPoint(x, y, radiansCenter, slicedOffset), values = {\n                x: child.sliced ? offsetPosition.x : x,\n                y: child.sliced ? offsetPosition.y : y,\n                innerR: innerRadius,\n                r: outerRadius,\n                radius: radius,\n                start: startAngle,\n                end: startAngle + radians\n            };\n            arr.push(values);\n            startAngle = values.end;\n            return arr;\n        }, []);\n    }\n    setRootNode(id, redraw, eventArguments) {\n        const series = this;\n        if ( // If the target node is the only one at level 1, skip it. (#18658)\n        series.nodeMap[id].level === 1 &&\n            series.nodeList\n                .filter((node) => node.level === 1)\n                .length === 1) {\n            if (series.idPreviousRoot === '') {\n                return;\n            }\n            id = '';\n        }\n        super.setRootNode(id, redraw, eventArguments);\n    }\n    /**\n     * Set the shape arguments on the nodes. Recursive from root down.\n     * @private\n     */\n    setShapeArgs(parent, parentValues, mapOptionsToLevel) {\n        const level = parent.level + 1, options = mapOptionsToLevel[level], \n        // Collect all children which should be included\n        children = parent.children.filter(function (n) {\n            return n.visible;\n        }), twoPi = 6.28; // Two times Pi.\n        let childrenValues = [];\n        childrenValues = this.layoutAlgorithm(parentValues, children, options);\n        let i = -1;\n        for (const child of children) {\n            const values = childrenValues[++i], angle = values.start + ((values.end - values.start) / 2), radius = values.innerR + ((values.r - values.innerR) / 2), radians = (values.end - values.start), isCircle = (values.innerR === 0 && radians > twoPi), center = (isCircle ?\n                { x: values.x, y: values.y } :\n                getEndPoint(values.x, values.y, angle, radius)), val = (child.val ?\n                (child.childrenTotal > child.val ?\n                    child.childrenTotal :\n                    child.val) :\n                child.childrenTotal);\n            // The inner arc length is a convenience for data label filters.\n            if (this.points[child.i]) {\n                this.points[child.i].innerArcLength = radians * values.innerR;\n                this.points[child.i].outerArcLength = radians * values.r;\n            }\n            child.shapeArgs = SunburstSeries_merge(values, {\n                plotX: center.x,\n                plotY: center.y\n            });\n            child.values = SunburstSeries_merge(values, {\n                val: val\n            });\n            // If node has children, then call method recursively\n            if (child.children.length) {\n                this.setShapeArgs(child, child.values, mapOptionsToLevel);\n            }\n        }\n    }\n    translate() {\n        const series = this, options = series.options, positions = series.center = series.getCenter(), radians = series.startAndEndRadians = getStartAndEndRadians(options.startAngle, options.endAngle), innerRadius = positions[3] / 2, outerRadius = positions[2] / 2, diffRadius = outerRadius - innerRadius, \n        // NOTE: updateRootId modifies series.\n        rootId = SunburstSeries_updateRootId(series);\n        let mapIdToNode = series.nodeMap, mapOptionsToLevel, nodeRoot = mapIdToNode && mapIdToNode[rootId], nodeIds = {};\n        series.shapeRoot = nodeRoot && nodeRoot.shapeArgs;\n        series.generatePoints();\n        SunburstSeries_fireEvent(series, 'afterTranslate');\n        // @todo Only if series.isDirtyData is true\n        const tree = series.tree = series.getTree();\n        // Render traverseUpButton, after series.nodeMap i calculated.\n        mapIdToNode = series.nodeMap;\n        nodeRoot = mapIdToNode[rootId];\n        const idTop = SunburstSeries_isString(nodeRoot.parent) ? nodeRoot.parent : '', nodeTop = mapIdToNode[idTop], { from, to } = Sunburst_SunburstUtilities.getLevelFromAndTo(nodeRoot);\n        mapOptionsToLevel = SunburstSeries_getLevelOptions({\n            from,\n            levels: series.options.levels,\n            to,\n            defaults: {\n                colorByPoint: options.colorByPoint,\n                dataLabels: options.dataLabels,\n                levelIsConstant: options.levelIsConstant,\n                levelSize: options.levelSize,\n                slicedOffset: options.slicedOffset\n            }\n        });\n        // NOTE consider doing calculateLevelSizes in a callback to\n        // getLevelOptions\n        mapOptionsToLevel = Sunburst_SunburstUtilities.calculateLevelSizes(mapOptionsToLevel, {\n            diffRadius,\n            from,\n            to\n        });\n        // TODO Try to combine setTreeValues & setColorRecursive to avoid\n        //  unnecessary looping.\n        SunburstSeries_setTreeValues(tree, {\n            before: cbSetTreeValuesBefore,\n            idRoot: rootId,\n            levelIsConstant: options.levelIsConstant,\n            mapOptionsToLevel: mapOptionsToLevel,\n            mapIdToNode: mapIdToNode,\n            points: series.points,\n            series: series\n        });\n        const values = mapIdToNode[''].shapeArgs = {\n            end: radians.end,\n            r: innerRadius,\n            start: radians.start,\n            val: nodeRoot.val,\n            x: positions[0],\n            y: positions[1]\n        };\n        this.setShapeArgs(nodeTop, values, mapOptionsToLevel);\n        // Set mapOptionsToLevel on series for use in drawPoints.\n        series.mapOptionsToLevel = mapOptionsToLevel;\n        // #10669 - verify if all nodes have unique ids\n        for (const point of series.points) {\n            if (nodeIds[point.id]) {\n                SunburstSeries_error(31, false, series.chart);\n            }\n            // Map\n            nodeIds[point.id] = true;\n        }\n        // Reset object\n        nodeIds = {};\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nSunburstSeries.defaultOptions = SunburstSeries_merge(SunburstSeries_TreemapSeries.defaultOptions, Sunburst_SunburstSeriesDefaults);\nSunburstSeries_extend(SunburstSeries.prototype, {\n    axisTypes: [],\n    drawDataLabels: SunburstSeries_noop, // `drawDataLabels` is called in `drawPoints`\n    getCenter: getCenter,\n    isCartesian: false,\n    // Mark that the sunburst is supported by the series on point feature.\n    onPointSupported: true,\n    pointAttribs: SunburstSeries_ColumnSeries.prototype.pointAttribs,\n    pointClass: Sunburst_SunburstPoint,\n    NodeClass: Sunburst_SunburstNode,\n    utils: Sunburst_SunburstUtilities\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('sunburst', SunburstSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sunburst_SunburstSeries = ((/* unused pure expression or super */ null && (SunburstSeries)));\n\n;// ./code/es-modules/masters/modules/sunburst.js\n\n\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nG.Breadcrumbs = G.Breadcrumbs || Breadcrumbs_Breadcrumbs;\nG.Breadcrumbs.compose(G.Chart, G.defaultOptions);\n/* harmony default export */ const sunburst_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__984__", "__WEBPACK_EXTERNAL_MODULE__620__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__28__", "__WEBPACK_EXTERNAL_MODULE__820__", "ColorMapComposition", "TreemapUtilities", "CenteredUtilities", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "sunburst_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "Breadcrumbs_BreadcrumbsDefaults", "lang", "mainBreadcrumb", "options", "buttonTheme", "fill", "height", "padding", "zIndex", "states", "select", "style", "color", "buttonSpacing", "floating", "format", "relativeTo", "rtl", "position", "align", "verticalAlign", "x", "y", "separator", "text", "fontSize", "show<PERSON>ull<PERSON>ath", "useHTML", "highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_", "highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default", "composed", "addEvent", "defined", "extend", "fireEvent", "isString", "merge", "objectEach", "pick", "pushUnique", "onChartAfterShowResetZoom", "chart", "breadcrumbs", "bbox", "resetZoomButton", "getBBox", "breadcrumbsOptions", "alignBreadcrumbsGroup", "width", "onChartDestroy", "destroy", "onChart<PERSON><PERSON><PERSON><PERSON><PERSON>", "level", "breadcrumbsHeight", "marginBottom", "yOffset", "plotTop", "onChartRedraw", "redraw", "onChartSelection", "event", "resetSelection", "Breadcrumbs", "compose", "ChartClass", "highchartsDefaultOptions", "constructor", "userOptions", "elementList", "isDirty", "list", "chartOptions", "drilldown", "drillUpButton", "defaultOptions", "navigation", "updateProperties", "setList", "setLevel", "length", "getLevel", "getButtonText", "breadcrumb", "textFormat", "defaultText", "drillUpText", "returnText", "formatter", "levelOptions", "render", "group", "renderer", "g", "addClass", "attr", "add", "renderFullPathButtons", "renderSingleButton", "destroySingleButton", "resetElementListState", "updateListElements", "destroyListElements", "posX", "previousBreadcrumb", "renderButton", "updateSingleButton", "xOffset", "positionOptions", "alignTo", "bBox", "additionalSpace", "newPositions", "posY", "button", "e", "callDefaultEvent", "buttonEvents", "events", "click", "newLevel", "styledMode", "renderSeparator", "separatorOptions", "label", "css", "update", "currentBreadcrumb", "force", "element", "updated", "rtlFactor", "updateXPosition", "spacing", "adjustToRTL", "translate", "i", "iEnd", "isLast", "setState", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_", "highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default", "column", "columnProto", "seriesTypes", "ColorMapComposition_addEvent", "ColorMapComposition_defined", "onPointAfterSetState", "series", "point", "moveToTopOnHover", "graphic", "stateMarkerGraphic", "pointerEvents", "parentGroup", "state", "id", "href", "url", "visibility", "pointMembers", "dataLabelOnNull", "<PERSON><PERSON><PERSON><PERSON>", "value", "Infinity", "isNaN", "seriesMembers", "colorKey", "axisTypes", "parallelArrays", "pointArrayMap", "trackerGroups", "colorAttribs", "ret", "colorProp", "pointAttribs", "SeriesClass", "pointClass", "Series_ColorMapComposition", "highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_", "highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default", "Treemap_TreemapAlgorithmGroup", "h", "w", "p", "plot", "direction", "startDirection", "total", "nW", "lW", "nH", "lH", "el<PERSON>rr", "lP", "nR", "lR", "aspectRatio", "Math", "max", "addElement", "el", "push", "reset", "Treemap_TreemapNode", "childrenTotal", "visible", "init", "children", "parent", "Series_DrawPointUtilities", "draw", "params", "animatableAttribs", "onComplete", "animation", "hasRendered", "attribs", "getClassName", "shouldDraw", "shapeType", "image", "imageUrl", "shapeArgs", "animate", "isNew", "keys", "pie", "PiePoint", "scatter", "ScatterPoint", "TreemapPoint_extend", "isNumber", "TreemapPoint_pick", "TreemapPoint", "arguments", "groupedPointsAmount", "className", "node", "nodeMap", "rootNode", "isGroup", "<PERSON><PERSON><PERSON><PERSON>", "interactByLeaf", "allowTraversingTree", "Boolean", "apply", "plotY", "setVisible", "TreemapSeriesDefaults_isString", "TreemapSeriesDefaults", "animationLimit", "borderRadius", "showInLegend", "marker", "colorByPoint", "dataLabels", "enabled", "name", "headers", "inside", "textOverflow", "tooltip", "headerFormat", "pointFormat", "clusterFormat", "ignoreHiddenPoint", "layoutAlgorithm", "layoutStartingDirection", "alternateStartingDirection", "levelIsConstant", "traverseUpButton", "borderColor", "borderWidth", "opacity", "hover", "brightness", "heatmap", "halo", "shadow", "legendSymbol", "traverseToLeaf", "cluster", "pixelWidth", "pixelHeight", "reductionFactor", "minimumClusterSize", "distance", "gridSize", "kmeansT<PERSON><PERSON>old", "lineWidth", "radius", "recursive", "item", "func", "context", "next", "Treemap_TreemapUtilities", "TreeUtilities_extend", "isArray", "TreeUtilities_isNumber", "isObject", "TreeUtilities_merge", "TreeUtilities_pick", "<PERSON><PERSON><PERSON><PERSON>", "Series_TreeUtilities", "getColor", "colorIndexByPoint", "colorIndex", "index", "mapOptionsToLevel", "parentColor", "parentColorIndex", "colors", "siblings", "points", "chartOptionsChart", "colorCount", "variateColor", "colorVariation", "parse", "brighten", "to", "getLevelOptions", "defaults", "converted", "from", "levels", "result", "reduce", "getNodeWidth", "columnCount", "nodeDistance", "nodeWidth", "plotSizeX", "test", "fraction", "parseFloat", "nDistance", "Number", "setTreeValues", "tree", "before", "idRoot", "nodeRoot", "mapIdToNode", "optionsPoint", "levelDynamic", "for<PERSON>ach", "child", "newOptions", "val", "updateRootId", "rootId", "TreemapSeries_composed", "noop", "ColumnSeries", "ScatterSeries", "TreemapSeries_getColor", "TreemapSeries_getLevelOptions", "TreemapSeries_updateRootId", "TreemapSeries_addEvent", "arrayMax", "clamp", "correctFloat", "crisp", "TreemapSeries_defined", "error", "TreemapSeries_extend", "TreemapSeries_fireEvent", "TreemapSeries_isArray", "TreemapSeries_isNumber", "TreemapSeries_isObject", "TreemapSeries_isString", "TreemapSeries_merge", "TreemapSeries_pick", "TreemapSeries_pushUnique", "splat", "stableSort", "keepProps", "treemapAxisDefaultValues", "onSeriesAfterBindAxes", "treeAxis", "xAxis", "yAxis", "is", "endOnTick", "gridLineWidth", "min", "minPadding", "maxPadding", "startOnTick", "title", "tickPositions", "setOptions", "TreemapSeries", "simulation", "algorithmCalcPoints", "directionChange", "last", "children<PERSON>rea", "end", "pX", "pY", "pW", "pH", "gW", "gH", "keep", "algorithmFill", "pTot", "algorithmLowAspectRatio", "alignDataLabel", "dataLabel", "labelOptions", "applyTreeGrouping", "parentList", "parentGroups", "checkIfHide", "compareHeight", "thresholdArea", "area", "indexOf", "splice", "groupPoint", "find", "PointClass", "pointIndex", "formatPrefix", "amount", "nodeList", "buildTree", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "algorithm", "alternate", "filter", "ignore", "groupPadding", "children<PERSON><PERSON><PERSON>", "axisWidth", "pointV<PERSON>ues", "axisHeight", "values", "groupPaddingXValues", "len", "groupPaddingYValues", "dlH<PERSON>ght", "map", "dl", "xPad", "yPad", "axisRatio", "axisMax", "getChildrenRecursive", "getLeaves", "nodeSizeBy", "hasOutsideDataLabels", "some", "leaves", "areas", "valueSum", "sum", "expectedAreaPerValue", "areaSum", "minMiss", "max<PERSON><PERSON>", "fit", "areaPerValue", "miss", "simulatedValue", "createList", "currentLevelNumber", "target", "newRootId", "extraNodes", "reverse", "drawDataLabels", "positionsAreSet", "hasDataLabels", "dataLabelWidth", "lineClamp", "floor", "dlOptions", "drawPoints", "withinAnimationLimit", "pointCount", "groupKey", "hasGraphic", "shouldAnimate", "isInside", "r", "selected", "survive", "drillId", "drillToByLeaf", "drillToByGroup", "nodeParent", "drillToNode", "setRootNode", "drillUp", "trigger", "getExtremes", "dataMin", "dataMax", "colorValueData", "valueMin", "valueMax", "getListOfParents", "data", "existingIds", "arr", "ids", "listOfParents", "prev", "curr", "getTree", "allIds", "NodeClass", "parentNode", "hasData", "dataTable", "rowCount", "setOptionsEvent", "allowDrillToNode", "eventsToUnbind", "onClickDrillToNode", "hadOutsideDataLabels", "keepEventsForUpdate", "drillUpsNumber", "stateOptions", "borderDashStyle", "cursor", "setColorRecursive", "colorInfo", "setPointV<PERSON>ues", "getStrokeWidth", "strokeWidth", "xValue", "toPixels", "x2Value", "yValue", "y2Value", "x1", "x2", "y1", "y2", "abs", "plotX", "eventArguments", "previousRootId", "args", "idPreviousRoot", "inactiveOtherPoints", "b", "sortIndex", "sliceAndDice", "squarified", "strip", "stripes", "applyGrouping", "seriesArea", "startsWith", "concat", "colorAxis", "setExtremes", "setScale", "buildKDTree", "directTouch", "getExtremesFromAll", "getSymbol", "optionalAxis", "utils", "registerSeriesType", "deg2rad", "CenteredUtilities_fireEvent", "CenteredUtilities_isNumber", "CenteredUtilities_pick", "CenteredUtilities_relativeLength", "getCenter", "slicingRoom", "slicedOffset", "plot<PERSON>id<PERSON>", "plotHeight", "centerOption", "center", "smallestSize", "thickness", "handleSlicingRoom", "size", "innerSize", "positions", "angular", "getStartAndEndRadians", "start", "startAngle", "endAngle", "Series_CenteredUtilities", "Point", "treemap", "SunburstPoint_TreemapPoint", "SunburstPoint_correctFloat", "SunburstPoint_extend", "pInt", "SunburstPoint", "getDataLabelPath", "shapeExisting", "angle", "upperHalf", "PI", "moreThanHalf", "dataLabelPath", "arc", "open", "longArc", "clockwise", "innerR", "defs", "haloPath", "SunburstUtilities_TreemapSeries", "SunburstUtilities_isNumber", "SunburstUtilities_isObject", "SunburstUtilities_merge", "range", "SunburstUtilities", "calculateLevelSizes", "totalWeight", "diffRadius", "levelsNotIncluded", "remainingSize", "unit", "levelSize", "weight", "getLevelFromAndTo", "TextPath_deg2rad", "TextPath_addEvent", "TextPath_merge", "<PERSON><PERSON><PERSON>", "TextPath_defined", "TextPath_extend", "setTextPath", "path", "textPathOptions", "attributes", "dy", "startOffset", "textAnchor", "textWrapper", "textPath", "undo", "textPathId", "textAttribs", "dx", "transform", "box", "nodes", "slice", "tagName", "added", "textCache", "buildText", "setPolygon", "tp", "querySelector", "polygon", "fontMetrics", "descender", "lineCleanerRegex", "RegExp", "lines", "innerHTML", "replace", "split", "numOfLines", "appendTopAndBottom", "charIndex", "positionOfChar", "rotation", "getRotationOfChar", "cosRot", "cos", "sinRot", "sin", "lineIndex", "lineLen", "line", "lineCharIndex", "srcCharIndex", "lower", "upper", "getStartPositionOfChar", "unshift", "char<PERSON><PERSON>", "getEndPositionOfChar", "drawTextPath", "SunburstSeries_noop", "SunburstSeries_ColumnSeries", "SunburstSeries_TreemapSeries", "SunburstSeries_getColor", "SunburstSeries_getLevelOptions", "SunburstSeries_setTreeValues", "SunburstSeries_updateRootId", "SunburstSeries_defined", "SunburstSeries_error", "SunburstSeries_extend", "SunburstSeries_fireEvent", "SunburstSeries_isNumber", "SunburstSeries_isObject", "SunburstSeries_isString", "SunburstSeries_merge", "SunburstSeries_splat", "Extensions_TextPath", "SVGElementClass", "svgElementProto", "rad2deg", "getEndPoint", "cbSetTreeValuesBefore", "sliced", "SunburstSeries", "placed", "plotLeft", "translateX", "translateY", "scaleX", "scaleY", "shapeRoot", "nodePreviousRoot", "shapePreviousRoot", "radians", "startAndEndRadians", "optionsChart", "isBoolean", "hackDataLabelAnimation", "dataLabelsGroup", "animate<PERSON><PERSON><PERSON>", "animateLabelsCalled", "addedHack", "s", "animationInfo", "shape", "getAnimation", "tooltipPos", "getDrillId", "isNull", "getDlOptions", "innerArcLength", "outerArcLength", "rotationRad", "rotationMode", "parseInt", "whiteSpace", "defer", "innerRadius", "outerRadius", "percentage", "offsetPosition", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parentValues", "isCircle", "nodeIds", "generatePoints", "idTop", "nodeTop", "Sunburst_SunburstUtilities", "clip", "allowOverlap", "isCartesian", "onPointSupported", "G", "Chart"], "mappings": "CAUA,AAVA;;;;;;;;;CASC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,UAAa,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,UAAa,CAAEA,EAAK,WAAc,CAAC,MAAS,EAC/M,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,8BAA+B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,UAAa,CAACA,EAAK,KAAQ,CAACA,EAAK,cAAiB,CAACA,EAAK,UAAa,CAACA,EAAK,MAAS,CAAE,GAC1L,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,8BAA8B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,UAAa,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,UAAa,CAAEA,EAAK,WAAc,CAAC,MAAS,EAE9OA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,UAAa,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,UAAa,CAAEA,EAAK,UAAa,CAAC,MAAS,CACtN,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,EAAiCC,IAC5M,AAAC,CAAA,KACP,aACA,IA4jCNC,EAypCAC,EAg9CAC,EArqHUC,EAAuB,CAE/B,GACC,AAACd,IAERA,EAAOD,OAAO,CAAGU,CAEX,EAEA,IACC,AAACT,IAERA,EAAOD,OAAO,CAAGS,CAEX,EAEA,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGW,CAEX,EAEA,IACC,AAACV,IAERA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,AAACL,IAERA,EAAOD,OAAO,CAAGO,CAEX,CAEI,EAGIS,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAanB,OAAO,CAG5B,IAAIC,EAASe,CAAwB,CAACE,EAAS,CAAG,CAGjDlB,QAAS,CAAC,CACX,EAMA,OAHAe,CAAmB,CAACG,EAAS,CAACjB,EAAQA,EAAOD,OAAO,CAAEiB,GAG/ChB,EAAOD,OAAO,AACtB,CAMCiB,EAAoBI,CAAC,CAAG,AAACpB,IACxB,IAAIqB,EAASrB,GAAUA,EAAOsB,UAAU,CACvC,IAAOtB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAgB,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACxB,EAAS0B,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAAC5B,EAAS2B,IAC5EE,OAAOC,cAAc,CAAC9B,EAAS2B,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,EAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GA4QxF,IAAME,EAJP,CACxBC,KAnPS,CAOTC,eAAgB,MACpB,EA4OIC,QAjOY,CAiBZC,YAAa,CAETC,KAAM,OAENC,OAAQ,GAERC,QAAS,EAET,eAAgB,EAEhBC,OAAQ,EAERC,OAAQ,CACJC,OAAQ,CACJL,KAAM,MACV,CACJ,EACAM,MAAO,CACHC,MAAO,SACX,CACJ,EAOAC,cAAe,EA8BfC,SAAU,CAAA,EAYVC,OAAQ,KAAK,EAkBbC,WAAY,UAWZC,IAAK,CAAA,EAcLC,SAAU,CAMNC,MAAO,OAMPC,cAAe,MAMfC,EAAG,EAQHC,EAAG,KAAK,CACZ,EAMAC,UAAW,CAMPC,KAAM,IASNb,MAAO,CACHC,MAAO,UACPa,SAAU,OACd,CACJ,EAUAC,aAAc,CAAA,EAWdf,MAAO,CAAC,EAORgB,QAAS,CAAA,EAOTnB,OAAQ,CACZ,CASA,EAIA,IAAIoB,EAAmHrD,EAAoB,KAiB3I,GAAM,CAAEwC,OAAAA,CAAM,CAAE,CAAIc,AAhBuHtD,EAAoBI,CAAC,CAACiD,KAkB3J,CAAEE,SAAAA,CAAQ,CAAE,CAAI/B,IAEhB,CAAEgC,SAAAA,CAAQ,CAAEC,QAAAA,CAAO,CAAEC,OAAAA,CAAM,CAAEC,UAAAA,CAAS,CAAEC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,WAAAA,CAAU,CAAEC,KAAAA,CAAI,CAAEC,WAAAA,CAAU,CAAE,CAAIxC,IAUjG,SAASyC,IAEL,GAAIC,AADU,IAAI,CACRC,WAAW,CAAE,CACnB,IAAMC,EAAOF,AAFH,IAAI,CAEKG,eAAe,EAC9BH,AAHM,IAAI,CAGJG,eAAe,CAACC,OAAO,GAAIC,EAAqBL,AAHhD,IAAI,CAGkDC,WAAW,CAACvC,OAAO,AAC/EwC,CAAAA,GACAG,AAAsC,UAAtCA,EAAmB5B,QAAQ,CAACC,KAAK,EACjC2B,AAAkC,YAAlCA,EAAmB9B,UAAU,EAC7ByB,AAPM,IAAI,CAOJC,WAAW,CAACK,qBAAqB,CAAC,CAACJ,EAAKK,KAAK,CAAGF,EAAmBjC,aAAa,CAE9F,CACJ,CAKA,SAASoC,IACD,IAAI,CAACP,WAAW,GAChB,IAAI,CAACA,WAAW,CAACQ,OAAO,GACxB,IAAI,CAACR,WAAW,CAAG,KAAK,EAEhC,CAKA,SAASS,IACL,IAAMT,EAAc,IAAI,CAACA,WAAW,CACpC,GAAIA,GACA,CAACA,EAAYvC,OAAO,CAACW,QAAQ,EAC7B4B,EAAYU,KAAK,CAAE,CACnB,IAAMN,EAAqBJ,EAAYvC,OAAO,CAAEC,EAAc0C,EAAmB1C,WAAW,CAAEiD,EAAqB,AAACjD,CAAAA,EAAYE,MAAM,EAAI,CAAA,EACtI,EAAKF,CAAAA,EAAYG,OAAO,EAAI,CAAA,EAC5BuC,EAAmBjC,aAAa,CAAGO,EAAgB0B,EAAmB5B,QAAQ,CAACE,aAAa,AAC5FA,AAAkB,CAAA,WAAlBA,GACA,IAAI,CAACkC,YAAY,CAAG,AAAC,CAAA,IAAI,CAACA,YAAY,EAAI,CAAA,EAAKD,EAC/CX,EAAYa,OAAO,CAAGF,GAEjBjC,AAAkB,WAAlBA,GACL,IAAI,CAACoC,OAAO,EAAIH,EAChBX,EAAYa,OAAO,CAAG,CAACF,GAGvBX,EAAYa,OAAO,CAAG,KAAK,CAEnC,CACJ,CAIA,SAASE,IACL,IAAI,CAACf,WAAW,EAAI,IAAI,CAACA,WAAW,CAACgB,MAAM,EAC/C,CAKA,SAASC,EAAiBC,CAAK,EACvBA,AAAyB,CAAA,IAAzBA,EAAMC,cAAc,EACpB,IAAI,CAACnB,WAAW,EAChB,IAAI,CAACA,WAAW,CAACK,qBAAqB,EAE9C,CAkBA,MAAMe,EAMF,OAAOC,QAAQC,CAAU,CAAEC,CAAwB,CAAE,CAC7C1B,EAAWT,EAAU,iBACrBC,EAASiC,EAAY,UAAWf,GAChClB,EAASiC,EAAY,qBAAsBxB,GAC3CT,EAASiC,EAAY,aAAcb,GACnCpB,EAASiC,EAAY,SAAUP,GAC/B1B,EAASiC,EAAY,YAAaL,GAElC1B,EAAOgC,EAAyBhE,IAAI,CAAED,EAAgCC,IAAI,EAElF,CAMAiE,YAAYzB,CAAK,CAAE0B,CAAW,CAAE,CAC5B,IAAI,CAACC,WAAW,CAAG,CAAC,EACpB,IAAI,CAACC,OAAO,CAAG,CAAA,EACf,IAAI,CAACjB,KAAK,CAAG,EACb,IAAI,CAACkB,IAAI,CAAG,EAAE,CACd,IAAMC,EAAenC,EAAMK,EAAMtC,OAAO,CAACqE,SAAS,EAC9C/B,EAAMtC,OAAO,CAACqE,SAAS,CAACC,aAAa,CAAEX,EAAYY,cAAc,CAAEjC,EAAMtC,OAAO,CAACwE,UAAU,EAAIlC,EAAMtC,OAAO,CAACwE,UAAU,CAACjC,WAAW,CAAEyB,EACzI,CAAA,IAAI,CAAC1B,KAAK,CAAGA,EACb,IAAI,CAACtC,OAAO,CAAGoE,GAAgB,CAAC,CACpC,CAaAK,iBAAiBN,CAAI,CAAE,CACnB,IAAI,CAACO,OAAO,CAACP,GACb,IAAI,CAACQ,QAAQ,GACb,IAAI,CAACT,OAAO,CAAG,CAAA,CACnB,CAUAQ,QAAQP,CAAI,CAAE,CACV,IAAI,CAACA,IAAI,CAAGA,CAChB,CAQAQ,UAAW,CACP,IAAI,CAAC1B,KAAK,CAAG,IAAI,CAACkB,IAAI,CAACS,MAAM,EAAI,IAAI,CAACT,IAAI,CAACS,MAAM,CAAG,CACxD,CAQAC,UAAW,CACP,OAAO,IAAI,CAAC5B,KAAK,AACrB,CAYA6B,cAAcC,CAAU,CAAE,CACtB,IAA0BzC,EAAQC,AAAd,IAAI,CAAsBD,KAAK,CAAEK,EAAqBJ,AAAtD,IAAI,CAA8DvC,OAAO,CAAEF,EAAOwC,EAAMtC,OAAO,CAACF,IAAI,CAAEkF,EAAa7C,EAAKQ,EAAmB/B,MAAM,CAAE+B,EAAmBpB,YAAY,CAClM,eAAiB,kBAAmB0D,EAAcnF,GAAQqC,EAAKrC,EAAKoF,WAAW,CAAEpF,EAAKC,cAAc,EACpGoF,EAAaxC,EAAmByC,SAAS,EACzCzC,EAAmByC,SAAS,CAACL,IAC7BnE,EAAOoE,EAAY,CAAE/B,MAAO8B,EAAWM,YAAY,AAAC,EAAG/C,IAAU,GASrE,MARI,AAAC,CAAA,AAACN,EAASmD,IACX,CAACA,EAAWP,MAAM,EAClBO,AAAe,OAAfA,CAAkB,GAClBtD,EAAQoD,IACRE,CAAAA,EAAa,AAACxC,EAAmBpB,YAAY,CAEzC0D,EADA,KAAOA,CACG,EAEXE,CACX,CAQA5B,QAAS,CACD,IAAI,CAACW,OAAO,EACZ,IAAI,CAACoB,MAAM,GAEX,IAAI,CAACC,KAAK,EACV,IAAI,CAACA,KAAK,CAACvE,KAAK,GAEpB,IAAI,CAACkD,OAAO,CAAG,CAAA,CACnB,CAQAoB,QAAS,CACL,IAA0BhD,EAAQC,AAAd,IAAI,CAAsBD,KAAK,CAAEK,EAAqBJ,AAAtD,IAAI,CAA8DvC,OAAO,AAEzF,EAACuC,AAFe,IAAI,CAEPgD,KAAK,EAAI5C,GACtBJ,CAAAA,AAHgB,IAAI,CAGRgD,KAAK,CAAGjD,EAAMkD,QAAQ,CAC7BC,CAAC,CAAC,qBACFC,QAAQ,CAAC,gDACTC,IAAI,CAAC,CACNtF,OAAQsC,EAAmBtC,MAAM,AACrC,GACKuF,GAAG,EAAC,EAGTjD,EAAmBpB,YAAY,CAC/B,IAAI,CAACsE,qBAAqB,GAG1B,IAAI,CAACC,kBAAkB,GAE3B,IAAI,CAAClD,qBAAqB,EAC9B,CAQAiD,uBAAwB,CAEpB,IAAI,CAACE,mBAAmB,GACxB,IAAI,CAACC,qBAAqB,GAC1B,IAAI,CAACC,kBAAkB,GACvB,IAAI,CAACC,mBAAmB,EAC5B,CAQAJ,oBAAqB,CACjB,IAA0BxD,EAAQC,AAAd,IAAI,CAAsBD,KAAK,CAAE6B,EAAO5B,AAAxC,IAAI,CAAgD4B,IAAI,CAA4CzD,EAAgBiC,AAArCJ,AAA/E,IAAI,CAAuFvC,OAAO,CAAqCU,aAAa,CAExK,IAAI,CAACwF,mBAAmB,GAGxB,IAAMC,EAAO5D,AALO,IAAI,CAKCgD,KAAK,CAC1BhD,AANgB,IAAI,CAMRgD,KAAK,CAAC7C,OAAO,GAAGG,KAAK,CACjCnC,EACE0F,EAAqBjC,CAAI,CAACA,EAAKS,MAAM,CAAG,EAAE,AAC5C,EAACtC,EAAMgC,aAAa,EAAK,IAAI,CAACrB,KAAK,CAAG,EACtCX,EAAMgC,aAAa,CAAG/B,AAVN,IAAI,CAUc8D,YAAY,CAACD,EAAoBD,EAH7CzF,GAKjB4B,EAAMgC,aAAa,GACpB,IAAI,CAACrB,KAAK,CAAG,EAEb,IAAI,CAACqD,kBAAkB,GAGvB,IAAI,CAACP,mBAAmB,GAGpC,CAQAnD,sBAAsB2D,CAAO,CAAE,CAE3B,GAAIhE,AADgB,IAAI,CACRgD,KAAK,CAAE,CACnB,IAAM5C,EAAqBJ,AAFX,IAAI,CAEmBvC,OAAO,CAAEC,EAAc0C,EAAmB1C,WAAW,CAAEuG,EAAkB7D,EAAmB5B,QAAQ,CAAE0F,EAAW9D,AAAkC,UAAlCA,EAAmB9B,UAAU,EACjL8B,AAAkC,eAAlCA,EAAmB9B,UAAU,CAC7B,KAAK,EACL,UAAY6F,EAAOnE,AALP,IAAI,CAKegD,KAAK,CAAC7C,OAAO,GAAIiE,EAAkB,EAAK1G,CAAAA,EAAYG,OAAO,EAAI,CAAA,EAC9FuC,EAAmBjC,aAAa,AAEpC8F,CAAAA,EAAgB3D,KAAK,CAAG6D,EAAK7D,KAAK,CAAG8D,EACrCH,EAAgBrG,MAAM,CAAGuG,EAAKvG,MAAM,CAAGwG,EACvC,IAAMC,EAAe3E,EAAMuE,EAEvBD,CAAAA,GACAK,CAAAA,EAAa1F,CAAC,EAAIqF,CAAM,EAExBhE,AAfY,IAAI,CAeJvC,OAAO,CAACc,GAAG,EACvB8F,CAAAA,EAAa1F,CAAC,EAAIsF,EAAgB3D,KAAK,AAAD,EAE1C+D,EAAazF,CAAC,CAAGgB,EAAKyE,EAAazF,CAAC,CAAE,IAAI,CAACiC,OAAO,CAAE,GACpDb,AAnBgB,IAAI,CAmBRgD,KAAK,CAACvE,KAAK,CAAC4F,EAAc,CAAA,EAAMH,EAChD,CACJ,CAgBAJ,aAAatB,CAAU,CAAEoB,CAAI,CAAEU,CAAI,CAAE,CACjC,IAAMtE,EAAc,IAAI,CAAED,EAAQ,IAAI,CAACA,KAAK,CAAEK,EAAqBJ,EAAYvC,OAAO,CAAEC,EAAcgC,EAAMU,EAAmB1C,WAAW,EACpI6G,EAASxE,EAAMkD,QAAQ,CACxBsB,MAAM,CAACvE,EAAYuC,aAAa,CAACC,GAAaoB,EAAMU,EAAM,SAAUE,CAAC,EAEtE,IAEIC,EAFEC,EAAetE,EAAmBuE,MAAM,EAC1CvE,EAAmBuE,MAAM,CAACC,KAAK,AAE/BF,CAAAA,GACAD,CAAAA,EAAmBC,EAAazH,IAAI,CAAC+C,EAAawE,EAAGhC,EAAU,EAG1C,CAAA,IAArBiC,IAGKrE,EAAmBpB,YAAY,CAIhCwF,EAAEK,QAAQ,CAAGrC,EAAW9B,KAAK,CAH7B8D,EAAEK,QAAQ,CAAG7E,EAAYU,KAAK,CAAG,EAKrClB,EAAUQ,EAAa,KAAMwE,GAErC,EAAG9G,GACEyF,QAAQ,CAAC,iCACTE,GAAG,CAACrD,EAAYgD,KAAK,EAI1B,OAHI,AAACjD,EAAM+E,UAAU,EACjBP,EAAOnB,IAAI,CAAChD,EAAmBnC,KAAK,EAEjCsG,CACX,CAcAQ,gBAAgBnB,CAAI,CAAEU,CAAI,CAAE,CACxB,IAA0BvE,EAAQ,IAAI,CAACA,KAAK,CAA4CiF,EAAmB5E,AAAxCJ,AAA/C,IAAI,CAAuDvC,OAAO,CAAwCoB,SAAS,CACjIA,EAAYkB,EAAMkD,QAAQ,CAC3BgC,KAAK,CAACD,EAAiBlG,IAAI,CAAE8E,EAAMU,EAAM,KAAK,EAAG,KAAK,EAAG,KAAK,EAAG,CAAA,GACjEnB,QAAQ,CAAC,oCACTE,GAAG,CAACrD,AAJW,IAAI,CAIHgD,KAAK,EAI1B,OAHI,AAACjD,EAAM+E,UAAU,EACjBjG,EAAUqG,GAAG,CAACF,EAAiB/G,KAAK,EAEjCY,CACX,CAYAsG,OAAO1H,CAAO,CAAE,CACZiC,EAAM,CAAA,EAAM,IAAI,CAACjC,OAAO,CAAEA,GAC1B,IAAI,CAAC+C,OAAO,GACZ,IAAI,CAACmB,OAAO,CAAG,CAAA,CACnB,CAQAoC,oBAAqB,CACjB,IAAMhE,EAAQ,IAAI,CAACA,KAAK,CAAEqF,EAAoB,IAAI,CAACxD,IAAI,CAAC,IAAI,CAAClB,KAAK,CAAG,EAAE,AACnEX,CAAAA,EAAMgC,aAAa,EACnBhC,EAAMgC,aAAa,CAACqB,IAAI,CAAC,CACrBtE,KAAM,IAAI,CAACyD,aAAa,CAAC6C,EAC7B,EAER,CAQA5E,SAAU,CACN,IAAI,CAACgD,mBAAmB,GAGxB,IAAI,CAACG,mBAAmB,CAAC,CAAA,GAErB,IAAI,CAACX,KAAK,EACV,IAAI,CAACA,KAAK,CAACxC,OAAO,GAEtB,IAAI,CAACwC,KAAK,CAAG,KAAK,CACtB,CAQAW,oBAAoB0B,CAAK,CAAE,CACvB,IAAM3D,EAAc,IAAI,CAACA,WAAW,CACpC/B,EAAW+B,EAAa,CAAC4D,EAAS5E,KAC1B2E,CAAAA,GACA,CAAC3D,CAAW,CAAChB,EAAM,CAAC6E,OAAO,AAAD,IAE1BD,AADAA,CAAAA,EAAU5D,CAAW,CAAChB,EAAM,AAAD,EACnB6D,MAAM,EAAIe,EAAQf,MAAM,CAAC/D,OAAO,GACxC8E,EAAQzG,SAAS,EAAIyG,EAAQzG,SAAS,CAAC2B,OAAO,GAC9C,OAAO8E,EAAQf,MAAM,CACrB,OAAOe,EAAQzG,SAAS,CACxB,OAAO6C,CAAW,CAAChB,EAAM,CAEjC,GACI2E,GACA,CAAA,IAAI,CAAC3D,WAAW,CAAG,CAAC,CAAA,CAE5B,CAQA8B,qBAAsB,CACd,IAAI,CAACzD,KAAK,CAACgC,aAAa,GACxB,IAAI,CAAChC,KAAK,CAACgC,aAAa,CAACvB,OAAO,GAChC,IAAI,CAACT,KAAK,CAACgC,aAAa,CAAG,KAAK,EAExC,CAQA0B,uBAAwB,CACpB9D,EAAW,IAAI,CAAC+B,WAAW,CAAE,AAAC4D,IAC1BA,EAAQC,OAAO,CAAG,CAAA,CACtB,EACJ,CASA7B,oBAAqB,CACjB,IAA0BhC,EAAc1B,AAApB,IAAI,CAA4B0B,WAAW,CAAEvD,EAAgB6B,AAA7D,IAAI,CAAqEvC,OAAO,CAACU,aAAa,CAAwByD,EAAO5B,AAA7H,IAAI,CAAqI4B,IAAI,CAAErD,EAAMyB,AAArJ,IAAI,CAA6JvC,OAAO,CAACc,GAAG,CAAEiH,EAAYjH,EAAM,GAAK,EAAGkH,EAAkB,SAAUH,CAAO,CAAEI,CAAO,EACpQ,OAAOF,EAAYF,EAAQnF,OAAO,GAAGG,KAAK,CACtCkF,EAAYE,CACpB,EAAGC,EAAc,SAAUL,CAAO,CAAE1B,CAAI,CAAEU,CAAI,EAC1CgB,EAAQM,SAAS,CAAChC,EAAO0B,EAAQnF,OAAO,GAAGG,KAAK,CAAEgE,EACtD,EAEIV,EAAO5D,AAPS,IAAI,CAODgD,KAAK,CACxByC,EAAgBzF,AARA,IAAI,CAQQgD,KAAK,CAAE7E,GACnCA,EAAeiH,EAAmB5C,EACtC,IAAK,IAAIqD,EAAI,EAAGC,EAAOlE,EAAKS,MAAM,CAAEwD,EAAIC,EAAM,EAAED,EAAG,CAC/C,IACItB,EAAQ1F,EADNkH,EAASF,IAAMC,EAAO,CAGxBpE,CAAAA,CAAW,CAACc,AADhBA,CAAAA,EAAaZ,CAAI,CAACiE,EAAE,AAAD,EACQnF,KAAK,CAAC,EAE7B6D,EAASa,AADTA,CAAAA,EAAoB1D,CAAW,CAACc,EAAW9B,KAAK,CAAC,AAAD,EACrB6D,MAAM,CAE7B,AAACa,EAAkBvG,SAAS,EAC3BkH,EAUIX,EAAkBvG,SAAS,EAChCkH,IACAX,EAAkBvG,SAAS,CAAC2B,OAAO,GACnC,OAAO4E,EAAkBvG,SAAS,GAXlC+E,GAAQ4B,EAAYrH,EACpBiH,EAAkBvG,SAAS,CACvBmB,AAvBI,IAAI,CAuBI+E,eAAe,CAACnB,EAvB+EzF,GAwB3GI,GACAoH,EAAYP,EAAkBvG,SAAS,CAAE+E,EAzBkEzF,GA2B/GyF,GAAQ6B,EAAgBL,EAAkBvG,SAAS,CAAEV,IAOzDuD,CAAW,CAACc,EAAW9B,KAAK,CAAC,CAAC6E,OAAO,CAAG,CAAA,IAIxChB,EAASvE,AAtCG,IAAI,CAsCK8D,YAAY,CAACtB,EAAYoB,EAtCqEzF,GAuC/GI,GACAoH,EAAYpB,EAAQX,EAxC2FzF,GA0CnHyF,GAAQ6B,EAAgBlB,EAAQpG,GAE3B4H,IACDlH,EAAYmB,AA7CJ,IAAI,CA6CY+E,eAAe,CAACnB,EA7CuEzF,GA8C3GI,GACAoH,EAAY9G,EAAW+E,EA/CoFzF,GAiD/GyF,GAAQ6B,EAAgB5G,EAAWV,IAEvCuD,CAAW,CAACc,EAAW9B,KAAK,CAAC,CAAG,CAC5B6D,OAAAA,EACA1F,UAAAA,EACA0G,QAAS,CAAA,CACb,GAEAhB,GACAA,EAAOyB,QAAQ,CAACD,AAAS,IAATA,EAExB,CACJ,CACJ,CAMA3E,EAAYY,cAAc,CAAG1E,EAAgCG,OAAO,CA4FpE,IAAIwI,EAA+FpK,EAAoB,KACnHqK,EAAmHrK,EAAoBI,CAAC,CAACgK,GAEzIE,EAAmItK,EAAoB,KACvJuK,EAAuJvK,EAAoBI,CAAC,CAACkK,GAE7KE,EAAmHxK,EAAoB,IACvIyK,EAAuIzK,EAAoBI,CAAC,CAACoK,GAajK,GAAM,CAAEE,OAAQ,CAAExJ,UAAWyJ,CAAW,CAAE,CAAE,CAAG,AAACJ,IAA2IK,WAAW,CAGhM,CAAEpH,SAAUqH,CAA4B,CAAEpH,QAASqH,CAA2B,CAAE,CAAItJ,KAO1F,AAAC,SAAU7B,CAAmB,EAsC1B,SAASoL,EAAqBpC,CAAC,EAC3B,IAAoBqC,EAASC,AAAf,IAAI,CAAiBD,MAAM,CAAE5D,EAAW4D,EAAO9G,KAAK,CAACkD,QAAQ,AACvE6D,CADU,IAAI,CACRC,gBAAgB,EAAID,AADhB,IAAI,CACkBE,OAAO,GACnC,AAACH,EAAOI,kBAAkB,EAI1BJ,CAAAA,EAAOI,kBAAkB,CAAG,GAAKX,CAAAA,GAAwH,EAAGrD,EAAU,OACjKiC,GAAG,CAAC,CACLgC,cAAe,MACnB,GACK7D,GAAG,CAACyD,AAVH,IAAI,CAUKE,OAAO,CAACG,WAAW,CAAA,EAElC3C,GAAG4C,QAAU,SAGbN,AAfM,IAAI,CAeJE,OAAO,CAAC5D,IAAI,CAAC,CACfiE,GAAI,IAAI,CAACA,EAAE,AACf,GACAR,EAAOI,kBAAkB,CAAC7D,IAAI,CAAC,CAC3BkE,KAAM,CAAC,EAAErE,EAASsE,GAAG,CAAC,CAAC,EAAE,IAAI,CAACF,EAAE,CAAC,CAAC,CAClCG,WAAY,SAChB,IAGAX,EAAOI,kBAAkB,CAAC7D,IAAI,CAAC,CAC3BkE,KAAM,EACV,GAGZ,CA9DA9L,EAAoBiM,YAAY,CAAG,CAC/BC,gBAAiB,CAAA,EACjBX,iBAAkB,CAAA,EAClBY,QAiEJ,WACI,OAAQ,AAAe,OAAf,IAAI,CAACC,KAAK,EACd,IAAI,CAACA,KAAK,GAAKC,KACf,IAAI,CAACD,KAAK,GAAK,CAACC,KAEf,CAAA,AAAe,KAAK,IAApB,IAAI,CAACD,KAAK,EAAe,CAACE,MAAM,IAAI,CAACF,KAAK,CAAA,CACnD,CAtEA,EACApM,EAAoBuM,aAAa,CAAG,CAChCC,SAAU,QACVC,UAAW,CAAC,QAAS,QAAS,YAAY,CAC1CC,eAAgB,CAAC,IAAK,IAAK,QAAQ,CACnCC,cAAe,CAAC,QAAQ,CACxBC,cAAe,CAAC,QAAS,cAAe,kBAAkB,CAC1DC,aAwEJ,SAA4BvB,CAAK,EAC7B,IAAMwB,EAAM,CAAC,EAMb,OALI3B,EAA4BG,EAAM5I,KAAK,GACtC,CAAA,CAAC4I,EAAMM,KAAK,EAAIN,AAAgB,WAAhBA,EAAMM,KAAK,AAAY,GAExCkB,CAAAA,CAAG,CAAC,IAAI,CAACC,SAAS,EAAI,OAAO,CAAGzB,EAAM5I,KAAK,AAAD,EAEvCoK,CACX,EA/EIE,aAAchC,EAAYgC,YAAY,AAC1C,EAcAhN,EAAoB6F,OAAO,CAL3B,SAAiBoH,CAAW,EAGxB,OADA/B,EADmB+B,EAAY1L,SAAS,CAAC2L,UAAU,CACV,gBAAiB9B,GACnD6B,CACX,CAkEJ,EAAGjN,GAAwBA,CAAAA,EAAsB,CAAC,CAAA,GAMrB,IAAMmN,EAA8BnN,EAGjE,IAAIoN,EAAmG/M,EAAoB,KACvHgN,EAAuHhN,EAAoBI,CAAC,CAAC2M,GA4FpH,IAAME,EAzEnC,MAMItH,YAAYuH,CAAC,CAAEC,CAAC,CAAE5M,CAAC,CAAE6M,CAAC,CAAE,CACpB,IAAI,CAACrL,MAAM,CAAGmL,EACd,IAAI,CAACzI,KAAK,CAAG0I,EACb,IAAI,CAACE,IAAI,CAAGD,EACZ,IAAI,CAACE,SAAS,CAAG/M,EACjB,IAAI,CAACgN,cAAc,CAAGhN,EACtB,IAAI,CAACiN,KAAK,CAAG,EACb,IAAI,CAACC,EAAE,CAAG,EACV,IAAI,CAACC,EAAE,CAAG,EACV,IAAI,CAACC,EAAE,CAAG,EACV,IAAI,CAACC,EAAE,CAAG,EACV,IAAI,CAACC,KAAK,CAAG,EAAE,CACf,IAAI,CAACC,EAAE,CAAG,CACNN,MAAO,EACPI,GAAI,EACJD,GAAI,EACJD,GAAI,EACJD,GAAI,EACJM,GAAI,EACJC,GAAI,EACJC,YAAa,SAAUd,CAAC,CAAED,CAAC,EACvB,OAAOgB,KAAKC,GAAG,CAAEhB,EAAID,EAAKA,EAAIC,EAClC,CACJ,CACJ,CAMAiB,WAAWC,CAAE,CAAE,CACX,IAAI,CAACP,EAAE,CAACN,KAAK,CAAG,IAAI,CAACK,KAAK,CAAC,IAAI,CAACA,KAAK,CAACrH,MAAM,CAAG,EAAE,CACjD,IAAI,CAACgH,KAAK,CAAG,IAAI,CAACA,KAAK,CAAGa,EACtB,AAAmB,IAAnB,IAAI,CAACf,SAAS,EAEd,IAAI,CAACI,EAAE,CAAG,IAAI,CAACD,EAAE,CACjB,IAAI,CAACK,EAAE,CAACF,EAAE,CAAG,IAAI,CAACE,EAAE,CAACN,KAAK,CAAG,IAAI,CAACE,EAAE,CACpC,IAAI,CAACI,EAAE,CAACE,EAAE,CAAG,IAAI,CAACF,EAAE,CAACG,WAAW,CAAC,IAAI,CAACP,EAAE,CAAE,IAAI,CAACI,EAAE,CAACF,EAAE,EAEpD,IAAI,CAACH,EAAE,CAAG,IAAI,CAACD,KAAK,CAAG,IAAI,CAACzL,MAAM,CAClC,IAAI,CAAC+L,EAAE,CAACH,EAAE,CAAG,IAAI,CAACG,EAAE,CAACN,KAAK,CAAG,IAAI,CAACC,EAAE,CACpC,IAAI,CAACK,EAAE,CAACC,EAAE,CAAG,IAAI,CAACD,EAAE,CAACG,WAAW,CAAC,IAAI,CAACR,EAAE,CAAE,IAAI,CAACK,EAAE,CAACH,EAAE,IAIpD,IAAI,CAACC,EAAE,CAAG,IAAI,CAACD,EAAE,CACjB,IAAI,CAACG,EAAE,CAACJ,EAAE,CAAG,IAAI,CAACI,EAAE,CAACN,KAAK,CAAG,IAAI,CAACI,EAAE,CACpC,IAAI,CAACE,EAAE,CAACE,EAAE,CAAG,IAAI,CAACF,EAAE,CAACG,WAAW,CAAC,IAAI,CAACH,EAAE,CAACJ,EAAE,CAAE,IAAI,CAACE,EAAE,EAEpD,IAAI,CAACD,EAAE,CAAG,IAAI,CAACH,KAAK,CAAG,IAAI,CAAC/I,KAAK,CACjC,IAAI,CAACqJ,EAAE,CAACL,EAAE,CAAG,IAAI,CAACK,EAAE,CAACN,KAAK,CAAG,IAAI,CAACG,EAAE,CACpC,IAAI,CAACG,EAAE,CAACC,EAAE,CAAG,IAAI,CAACD,EAAE,CAACG,WAAW,CAAC,IAAI,CAACH,EAAE,CAACL,EAAE,CAAE,IAAI,CAACE,EAAE,GAExD,IAAI,CAACE,KAAK,CAACS,IAAI,CAACD,EACpB,CACAE,OAAQ,CACJ,IAAI,CAACd,EAAE,CAAG,EACV,IAAI,CAACC,EAAE,CAAG,EACV,IAAI,CAACG,KAAK,CAAG,EAAE,CACf,IAAI,CAACL,KAAK,CAAG,CACjB,CACJ,EAuDmCgB,EA/BnC,MACI7I,aAAc,CAMV,IAAI,CAAC8I,aAAa,CAAG,EACrB,IAAI,CAACC,OAAO,CAAG,CAAA,CACnB,CAMAC,KAAKnD,CAAE,CAAExB,CAAC,CAAE4E,CAAQ,CAAE7M,CAAM,CAAE8C,CAAK,CAAEmG,CAAM,CAAE6D,CAAM,CAAE,CAQjD,OAPA,IAAI,CAACrD,EAAE,CAAGA,EACV,IAAI,CAACxB,CAAC,CAAGA,EACT,IAAI,CAAC4E,QAAQ,CAAGA,EAChB,IAAI,CAAC7M,MAAM,CAAGA,EACd,IAAI,CAAC8C,KAAK,CAAGA,EACb,IAAI,CAACmG,MAAM,CAAGA,EACd,IAAI,CAAC6D,MAAM,CAAGA,EACP,IAAI,AACf,CACJ,EAyFmCC,EAHR,CACvBC,KAzDJ,SAAc9D,CAAK,CAAE+D,CAAM,EACvB,GAAM,CAAEC,kBAAAA,CAAiB,CAAEC,WAAAA,CAAU,CAAE7F,IAAAA,CAAG,CAAEjC,SAAAA,CAAQ,CAAE,CAAG4H,EACnDG,EAAY,AAAClE,EAAMD,MAAM,EAAIC,EAAMD,MAAM,CAAC9G,KAAK,CAACkL,WAAW,CAE7D,KAAK,EAEJnE,EAAMD,MAAM,EACTC,EAAMD,MAAM,CAACpJ,OAAO,CAACuN,SAAS,CAClChE,EAAUF,EAAME,OAAO,CAK3B,GAJA6D,EAAOK,OAAO,CAAG,CACb,GAAGL,EAAOK,OAAO,CACjB,MAASpE,EAAMqE,YAAY,EAC/B,EACKrE,EAAMsE,UAAU,GACZpE,IAWDF,EAAME,OAAO,CATTA,EADA6D,AAAqB,SAArBA,EAAOQ,SAAS,CACNpI,EAASnE,IAAI,GAElB+L,AAAqB,UAArBA,EAAOQ,SAAS,CACXpI,EAASqI,KAAK,CAACT,EAAOU,QAAQ,EAAI,IACvCnI,IAAI,CAACyH,EAAOW,SAAS,EAAI,CAAC,GAGrBvI,CAAQ,CAAC4H,EAAOQ,SAAS,CAAC,CAACR,EAAOW,SAAS,EAAI,CAAC,GAG9DxE,EAAQ3D,GAAG,CAACwH,EAAO7H,KAAK,GAExBkC,GACA8B,EAAQ9B,GAAG,CAACA,GAEhB8B,EACK5D,IAAI,CAACyH,EAAOK,OAAO,EACnBO,OAAO,CAACX,EAAmBD,CAAAA,EAAOa,KAAK,EAAWV,EAAWD,QAEjE,GAAI/D,EAAS,CACd,IAAMxG,EAAU,KACZsG,EAAME,OAAO,CAAGA,EAAWA,GAAWA,EAAQxG,OAAO,GACjD,AAAsB,YAAtB,OAAOuK,GACPA,GAER,CAEItO,CAAAA,OAAOkP,IAAI,CAACb,GAAmBzI,MAAM,CACrC2E,EAAQyE,OAAO,CAACX,EAAmB,KAAK,EAAG,IAAMtK,KAGjDA,GAER,CACJ,CAQA,EAkBM,CAAEoL,IAAK,CAAE7O,UAAW,CAAE2L,WAAYmD,CAAQ,CAAE,CAAE,CAAEC,QAAS,CAAE/O,UAAW,CAAE2L,WAAYqD,CAAY,CAAE,CAAE,CAAE,CAAG,AAAC3F,IAA2IK,WAAW,CAEhQ,CAAElH,OAAQyM,CAAmB,CAAEC,SAAAA,CAAQ,CAAErM,KAAMsM,CAAiB,CAAE,CAAI7O,GAM5E,OAAM8O,WAAqBJ,EACvBvK,aAAc,CAMV,KAAK,IAAI4K,WACT,IAAI,CAACC,mBAAmB,CAAG,EAC3B,IAAI,CAAChB,SAAS,CAAG,MACrB,CAMAT,KAAKC,CAAM,CAAE,CACTF,EAA0BC,IAAI,CAAC,IAAI,CAAEC,EACzC,CACAM,cAAe,CACX,IAAMtE,EAAS,IAAI,CAACA,MAAM,CAAEpJ,EAAUoJ,EAAOpJ,OAAO,CAChD6O,EAAY,KAAK,CAACnB,eAiBtB,OAfI,IAAI,CAACoB,IAAI,CAAC7L,KAAK,EAAImG,EAAO2F,OAAO,CAAC3F,EAAO4F,QAAQ,CAAC,CAAC/L,KAAK,EACxD,IAAI,CAAC6L,IAAI,CAAC9B,QAAQ,CAACpI,MAAM,CACzBiK,GAAa,0BAER,AAAC,IAAI,CAACC,IAAI,CAACG,OAAO,EACtB,IAAI,CAACH,IAAI,CAACI,MAAM,EAChB9F,EAAO2F,OAAO,CAAC3F,EAAO4F,QAAQ,CAAC,CAACC,OAAO,EACvCR,EAAkBzO,EAAQmP,cAAc,CAAE,CAACnP,EAAQoP,mBAAmB,EAGlE,AAAC,IAAI,CAACN,IAAI,CAACG,OAAO,EACtB,IAAI,CAACH,IAAI,CAACI,MAAM,EAChB9F,EAAO2F,OAAO,CAAC3F,EAAO4F,QAAQ,CAAC,CAACC,OAAO,EACxCJ,CAAAA,GAAa,2BAA0B,EALvCA,GAAa,wCAOVA,CACX,CAQA3E,SAAU,CACN,MAAOmF,CAAAA,CAAQ,CAAA,IAAI,CAACzF,EAAE,EAAI4E,EAAS,IAAI,CAACrE,KAAK,CAAA,CACjD,CACA5B,SAASoB,CAAK,CAAE,CACZ,KAAK,CAACpB,SAAS+G,KAAK,CAAC,IAAI,CAAEX,WAEvB,IAAI,CAACpF,OAAO,EACZ,IAAI,CAACA,OAAO,CAAC5D,IAAI,CAAC,CACdtF,OAAQsJ,CAAAA,CAAAA,AAAU,UAAVA,CAAgB,CAC5B,EAER,CACAgE,YAAa,CACT,OAAOa,EAAS,IAAI,CAACe,KAAK,GAAK,AAAW,OAAX,IAAI,CAACpO,CAAC,AACzC,CACJ,CACAoN,EAAoBG,GAAapP,SAAS,CAAE,CACxCkQ,WAAYpB,EAAS9O,SAAS,CAACkQ,UAAU,AAC7C,GAuBA,GAAM,CAAExN,SAAUyN,EAA8B,CAAE,CAAI7P,IAmBhD8P,GAAwB,CA4B1BN,oBAAqB,CAAA,EACrBO,eAAgB,IAIhBC,aAAc,EAmFdC,aAAc,CAAA,EAIdC,OAAQ,KAAK,EAYbC,aAAc,CAAA,EAIdC,WAAY,CACRC,QAAS,CAAA,EACT7K,UAAW,WACP,IAAMiE,EAAQ,IAAI,EAAI,IAAI,CAACA,KAAK,CAC5B,IAAI,CAACA,KAAK,CACV,CAAC,EACL,OADeoG,GAA+BpG,EAAM6G,IAAI,EAAI7G,EAAM6G,IAAI,CAAG,EAE7E,EAWAC,QAAS,CAAA,EACTC,OAAQ,CAAA,EACRhQ,QAAS,EACTa,cAAe,SACfT,MAAO,CACH6P,aAAc,UAClB,CACJ,EACAC,QAAS,CACLC,aAAc,GACdC,YAAa,0CAabC,cAAe,4CACnB,EAOAC,kBAAmB,CAAA,EAmBnBC,gBAAiB,eAOjBC,wBAAyB,WAWzBC,2BAA4B,CAAA,EAS5BC,gBAAiB,CAAA,EAQjBC,iBAAkB,CAIdhQ,SAAU,CAcNC,MAAO,QAIPE,EAAG,IAIHC,EAAG,EACP,CACJ,EAkKA6P,YAAa,UAIbC,YAAa,EACb1G,SAAU,aAQV2G,QAAS,IAMT5Q,OAAQ,CAOJ6Q,MAAO,CAIHH,YAAa,UAQbI,WAAY,AAAsK,IAAtK,AAACzI,IAA2IK,WAAW,CAACqI,OAAO,CAI3KC,KAAM,CAAA,EAONJ,QAAS,IAITK,OAAQ,CAAA,CACZ,CACJ,EACAC,aAAc,YAcdC,eAAgB,CAAA,EAsBhBC,QAAS,CAQL7C,UAAW,KAAK,EAQhBpO,MAAO,KAAK,EAQZwP,QAAS,CAAA,EAQT0B,WAAY,KAAK,EASjBC,YAAa,KAAK,EAUlB1B,KAAM,KAAK,EAWX2B,gBAAiB,KAAK,EAStBC,mBAAoB,EACpBnB,gBAAiB,CACboB,SAAU,EACVC,SAAU,EACVC,gBAAiB,CACrB,EACAnC,OAAQ,CACJoC,UAAW,EACXC,OAAQ,CACZ,CACJ,CACJ,CAmIInU,CACDA,CAAAA,GAAqBA,CAAAA,EAAmB,CAAC,CAAA,CAAC,EADxBoU,SAAS,CAN1B,SAASA,EAAUC,CAAI,CAAEC,CAAI,CAAEC,CAAO,EAClC,IAAMC,EAAOF,EAAK9S,IAAI,CAAC+S,GAAW,IAAI,CAAEF,EACpCG,AAAS,EAAA,IAATA,GACAJ,EAAUI,EAAMF,EAAMC,EAE9B,EAQyB,IAAME,GAA4BzU,EAiBzD,CAAE8D,OAAQ4Q,EAAoB,CAAEC,QAAAA,EAAO,CAAEnE,SAAUoE,EAAsB,CAAEC,SAAAA,EAAQ,CAAE5Q,MAAO6Q,EAAmB,CAAE3Q,KAAM4Q,EAAkB,CAAEC,eAAAA,EAAc,CAAE,CAAIpT,IAuMlIqT,GAPb,CAClBC,SAvLJ,SAAkBpE,CAAI,CAAE9O,CAAO,EAC3B,IACqBqJ,EAAOpG,EAAO8M,EAAcoD,EAAmB1S,EAAO2S,EADrEC,EAAQrT,EAAQqT,KAAK,CAAEC,EAAoBtT,EAAQsT,iBAAiB,CAAEC,EAAcvT,EAAQuT,WAAW,CAAEC,EAAmBxT,EAAQwT,gBAAgB,CAAEpK,EAASpJ,EAAQoJ,MAAM,CAAEqK,EAASzT,EAAQyT,MAAM,CAAEC,EAAW1T,EAAQ0T,QAAQ,CAAEC,EAASvK,EAAOuK,MAAM,CAAEC,EAAoBxK,EAAO9G,KAAK,CAACtC,OAAO,CAACsC,KAAK,CA+BjT,OAhBIwM,IACAzF,EAAQsK,CAAM,CAAC7E,EAAK1G,CAAC,CAAC,CACtBnF,EAAQqQ,CAAiB,CAACxE,EAAK7L,KAAK,CAAC,EAAI,CAAC,EACxBoG,GAASpG,EAAM8M,YAAY,GAEzCoD,EAAoB9J,EAAMgK,KAAK,CAAII,CAAAA,EAC/BA,EAAO7O,MAAM,CACbgP,EAAkBC,UAAU,AAAD,EAC/B9D,EAAe0D,GAAUA,CAAM,CAACN,EAAkB,EAGlD,AAAC/J,EAAO9G,KAAK,CAAC+E,UAAU,EACxB5G,CAAAA,EAAQsS,GAAmB1J,GAASA,EAAMrJ,OAAO,CAACS,KAAK,CAAEwC,GAASA,EAAMxC,KAAK,CAAEsP,EAAcwD,GAAeO,AAtB/F,CAAA,AAACrT,IAClB,IAAMsT,EAAiB9Q,GAASA,EAAM8Q,cAAc,QACpD,AAAIA,GACAA,AAAuB,eAAvBA,EAAejV,GAAG,EAClBuU,GACAK,EACOjL,IAAsGuL,KAAK,CAACvT,GAAOwT,QAAQ,CAACF,EAAeG,EAAE,CAAIb,CAAAA,EAAQK,CAAO,GAAIvU,GAAG,GAE3KsB,CACX,CAAA,EAaiI8S,GAAcnK,EAAO3I,KAAK,CAAA,EAEvJ2S,EAAaL,GAAmB1J,GAASA,EAAMrJ,OAAO,CAACoT,UAAU,CAAEnQ,GAASA,EAAMmQ,UAAU,CAAED,EAAmBK,EAAkBxT,EAAQoT,UAAU,GAElJ,CACH3S,MAAOA,EACP2S,WAAYA,CAChB,CACJ,EAoJIe,gBAlIJ,SAAyB/G,CAAM,EAC3B,IACIgH,EAAUC,EAAWjM,EAAGkM,EAAMJ,EAAIK,EADhCC,EAAS,CAAC,EAEhB,GAAI3B,GAASzF,GA2BT,IA1BAkH,EAAO1B,GAAuBxF,EAAOkH,IAAI,EAAIlH,EAAOkH,IAAI,CAAG,EAC3DC,EAASnH,EAAOmH,MAAM,CACtBF,EAAY,CAAC,EACbD,EAAWvB,GAASzF,EAAOgH,QAAQ,EAAIhH,EAAOgH,QAAQ,CAAG,CAAC,EACtDzB,GAAQ4B,IACRF,CAAAA,EAAYE,EAAOE,MAAM,CAAC,CAACrV,EAAKiT,KAC5B,IAAIpP,EAAO6N,EAAiB9Q,EAgB5B,OAfI6S,GAASR,IAASO,GAAuBP,EAAKpP,KAAK,IAEnD6N,EAAkBiC,GAAmB/S,AADrCA,CAAAA,EAAU8S,GAAoB,CAAC,EAAGT,EAAI,EACOvB,eAAe,CAAEsD,EAAStD,eAAe,EAEtF,OAAO9Q,EAAQ8Q,eAAe,CAC9B,OAAO9Q,EAAQiD,KAAK,CAGhB4P,GAASzT,CAAG,CADhB6D,EAAQoP,EAAKpP,KAAK,CAAI6N,CAAAA,EAAkB,EAAIwD,EAAO,CAAA,EAC5B,EACnBxB,GAAoB,CAAA,EAAM1T,CAAG,CAAC6D,EAAM,CAAEjD,GAGtCZ,CAAG,CAAC6D,EAAM,CAAGjD,GAGdZ,CACX,EAAG,CAAC,EAAC,EAET8U,EAAKtB,GAAuBxF,EAAO8G,EAAE,EAAI9G,EAAO8G,EAAE,CAAG,EAChD9L,EAAI,EAAGA,GAAK8L,EAAI9L,IACjBoM,CAAM,CAACpM,EAAE,CAAG0K,GAAoB,CAAC,EAAGsB,EAAUvB,GAASwB,CAAS,CAACjM,EAAE,EAAIiM,CAAS,CAACjM,EAAE,CAAG,CAAC,GAG/F,OAAOoM,CACX,EAgGIE,aAvBJ,SAAsBtL,CAAM,CAAEuL,CAAW,EACrC,GAAM,CAAErS,MAAAA,CAAK,CAAEtC,QAAAA,CAAO,CAAE,CAAGoJ,EAAQ,CAAEwL,aAAAA,EAAe,CAAC,CAAEC,UAAAA,EAAY,CAAC,CAAE,CAAG7U,EAAS,CAAE8U,UAAAA,EAAY,CAAC,CAAE,CAAGxS,EAGtG,GAAIuS,AAAc,SAAdA,EAAsB,CACtB,GAAI,AAAwB,UAAxB,OAAOD,GAA6B,KAAKG,IAAI,CAACH,GAE9C,OAAOE,EADkDH,CAAAA,EAAcK,AAAtDC,WAAWL,GAAgB,IAAuCD,CAAAA,EAAc,CAAA,CAAC,EAGtG,IAAMO,EAAYC,OAAOP,GACzB,MAAO,AAAEE,CAAAA,EAAYI,CAAQ,EACxBP,CAAAA,GAAe,CAAA,EAAMO,CAC9B,CACA,OAAOlC,GAAe6B,EAAWC,EACrC,EAUIM,cA3FJ,SAASA,EAAcC,CAAI,CAAErV,CAAO,EAChC,IAAMsV,EAAStV,EAAQsV,MAAM,CAAEC,EAASvV,EAAQuV,MAAM,CAAqCC,EAAWC,AAAhCzV,EAAQyV,WAAW,AAAwB,CAACF,EAAO,CAAEzE,EAAmB9Q,AAA4B,CAAA,IAA5BA,EAAQ8Q,eAAe,CAAsCzH,EAAQsK,AAAxB3T,EAAQ2T,MAAM,AAAgB,CAAC0B,EAAKjN,CAAC,CAAC,CAAEsN,EAAerM,GAASA,EAAMrJ,OAAO,EAAI,CAAC,EAAGgN,EAAW,EAAE,CACzRH,EAAgB,CACpBwI,CAAAA,EAAKM,YAAY,CAAGN,EAAKpS,KAAK,CAAI6N,CAAAA,EAAkB,EAAI0E,EAASvS,KAAK,AAAD,EACrEoS,EAAKnF,IAAI,CAAG6C,GAAmB1J,GAASA,EAAM6G,IAAI,CAAE,IACpDmF,EAAKvI,OAAO,CAAIyI,IAAWF,EAAKzL,EAAE,EAC9B5J,AAAoB,CAAA,IAApBA,EAAQ8M,OAAO,CACf,AAAkB,YAAlB,OAAOwI,GACPD,CAAAA,EAAOC,EAAOD,EAAMrV,EAAO,EAG/BqV,EAAKrI,QAAQ,CAAC4I,OAAO,CAAC,CAACC,EAAOzN,KAC1B,IAAM0N,EAAapD,GAAqB,CAAC,EAAG1S,GAC5C0S,GAAqBoD,EAAY,CAC7BzC,MAAOjL,EACPsL,SAAU2B,EAAKrI,QAAQ,CAACpI,MAAM,CAC9BkI,QAASuI,EAAKvI,OAAO,AACzB,GACA+I,EAAQT,EAAcS,EAAOC,GAC7B9I,EAASN,IAAI,CAACmJ,GACVA,EAAM/I,OAAO,EACbD,CAAAA,GAAiBgJ,EAAME,GAAG,AAAD,CAEjC,GAEA,IAAM5L,EAAQ4I,GAAmB2C,EAAavL,KAAK,CAAE0C,GAMrD,OALAwI,EAAKvI,OAAO,CAAG3C,GAAS,GAAM0C,CAAAA,EAAgB,GAAKwI,EAAKvI,OAAO,AAAD,EAC9DuI,EAAKrI,QAAQ,CAAGA,EAChBqI,EAAKxI,aAAa,CAAGA,EACrBwI,EAAKnG,MAAM,CAAGmG,EAAKvI,OAAO,EAAI,CAACD,EAC/BwI,EAAKU,GAAG,CAAG5L,EACJkL,CACX,EA4DIW,aA/CJ,SAAsB5M,CAAM,EACxB,IAAI6M,EAAQjW,EAaZ,OAZI6S,GAASzJ,KAETpJ,EAAU6S,GAASzJ,EAAOpJ,OAAO,EAAIoJ,EAAOpJ,OAAO,CAAG,CAAC,EAEvDiW,EAASlD,GAAmB3J,EAAO4F,QAAQ,CAAEhP,EAAQiW,MAAM,CAAE,IAEzDpD,GAASzJ,EAAOpF,WAAW,GAC3BoF,CAAAA,EAAOpF,WAAW,CAACiS,MAAM,CAAGA,CAAK,EAGrC7M,EAAO4F,QAAQ,CAAGiH,GAEfA,CACX,CAiCA,EAkBM,CAAEjC,MAAOvT,EAAK,CAAE,CAAIgI,IAGpB,CAAE9G,SAAUuU,EAAsB,CAAEC,KAAAA,EAAI,CAAE,CAAIvW,IAG9C,CAAEkJ,OAAQsN,EAAY,CAAE/H,QAASgI,EAAa,CAAE,CAAG,AAAC1N,IAA2IK,WAAW,CAO1M,CAAEkK,SAAUoD,EAAsB,CAAEnC,gBAAiBoC,EAA6B,CAAEP,aAAcQ,EAA0B,CAAE,CAAGvD,GAEjI,CAAErR,SAAU6U,EAAsB,CAAEC,SAAAA,EAAQ,CAAEC,MAAAA,EAAK,CAAEC,aAAAA,EAAY,CAAEC,MAAAA,EAAK,CAAEhV,QAASiV,EAAqB,CAAEC,MAAAA,EAAK,CAAEjV,OAAQkV,EAAoB,CAAEjV,UAAWkV,EAAuB,CAAEtE,QAASuE,EAAqB,CAAE1I,SAAU2I,EAAsB,CAAEtE,SAAUuE,EAAsB,CAAEpV,SAAUqV,EAAsB,CAAEpV,MAAOqV,EAAmB,CAAEnV,KAAMoV,EAAkB,CAAEnV,WAAYoV,EAAwB,CAAEC,MAAAA,EAAK,CAAEC,WAAAA,EAAU,CAAE,CAAI9X,IAClbwL,IAA0GuM,SAAS,CAACjL,IAAI,CAAC,aAAc,wBAYvI,IAAIkL,GAA2B,CAAA,EAO/B,SAASC,KACL,IACIC,EADiBC,EAAQ3O,AAAd,IAAI,CAAiB2O,KAAK,CAAEC,EAAQ5O,AAApC,IAAI,CAAuC4O,KAAK,CAE3DD,GAASC,IACL5O,AAHO,IAAI,CAGJ6O,EAAE,CAAC,YACVH,EAAW,CACPI,UAAW,CAAA,EACXC,cAAe,EACfjG,UAAW,EACXkG,IAAK,EACLC,WAAY,EACZ9L,IAxBA,IAyBA+L,WAAY,EACZC,YAAa,CAAA,EACbC,MAAO,KAAK,EACZC,cAAe,EAAE,AACrB,EACAzB,GAAqBgB,EAAMhY,OAAO,CAAE8X,GACpCd,GAAqBe,EAAM/X,OAAO,CAAE8X,GACpCF,GAA2B,CAAA,GAEtBA,KACLI,EAAMU,UAAU,CAACV,EAAMhU,WAAW,EAClC+T,EAAMW,UAAU,CAACX,EAAM/T,WAAW,EAClC4T,GAA2B,CAAA,GAGvC,CAaA,MAAMe,WAAsBtC,GACxBtS,aAAc,CAMV,KAAK,IAAI4K,WACT,IAAI,CAACiK,UAAU,CAAG,CAEtB,CAMA,OAAOhV,QAAQoH,CAAW,CAAE,CACpBwM,GAAyBtB,GAAwB,kBACjDO,GAAuBzL,EAAa,gBAAiB6M,GAE7D,CAOAgB,oBAAoBC,CAAe,CAAEC,CAAI,CAAExT,CAAK,CAAEyT,CAAY,CAAE,CAC5D,IAAMvN,EAAOlG,EAAMkG,IAAI,CAAEwN,EAAM1T,EAAM0G,KAAK,CAACrH,MAAM,CAAG,EAChDsU,EAAIC,EAAIC,EAAIC,EAAIC,EAAK/T,EAAMuG,EAAE,CAAEyN,EAAKhU,EAAMyG,EAAE,CAAEwN,EAAMpR,EAAI,EAQ5D,IAAK,IAAMoD,KAPPuN,GACAO,EAAK/T,EAAMsG,EAAE,CACb0N,EAAKhU,EAAMwG,EAAE,EAGbyN,EAAOjU,EAAM0G,KAAK,CAACgN,EAAI,CAEX1T,EAAM0G,KAAK,EACnB8M,CAAAA,GAAS3Q,EAAI6Q,CAAG,IACZ1T,AAAoB,IAApBA,EAAMmG,SAAS,EACfwN,EAAKzN,EAAKvK,CAAC,CACXiY,EAAK1N,EAAKtK,CAAC,CAEXkY,EAAK7N,EADL4N,CAAAA,EAAKE,CAAC,IAINJ,EAAKzN,EAAKvK,CAAC,CACXiY,EAAK1N,EAAKtK,CAAC,CAEXiY,EAAK5N,EADL6N,CAAAA,EAAKE,CAAC,GAGVP,EAAatM,IAAI,CAAC,CACdxL,EAAGgY,EACH/X,EAAGgY,EACHtW,MAAOuW,EACPjZ,OAAQyW,GAAayC,EACzB,GACI9T,AAAoB,IAApBA,EAAMmG,SAAS,CACfD,EAAKtK,CAAC,CAAGsK,EAAKtK,CAAC,CAAGkY,EAGlB5N,EAAKvK,CAAC,CAAGuK,EAAKvK,CAAC,CAAGkY,GAG1BhR,GAAQ,EAGZ7C,EAAMoH,KAAK,GACPpH,AAAoB,IAApBA,EAAMmG,SAAS,CACfnG,EAAM1C,KAAK,CAAG0C,EAAM1C,KAAK,CAAGyW,EAG5B/T,EAAMpF,MAAM,CAAGoF,EAAMpF,MAAM,CAAGoZ,EAElC9N,EAAKtK,CAAC,CAAGsK,EAAKwB,MAAM,CAAC9L,CAAC,CAAIsK,CAAAA,EAAKwB,MAAM,CAAC9M,MAAM,CAAGoF,EAAMpF,MAAM,AAAD,EAC1DsL,EAAKvK,CAAC,CAAGuK,EAAKwB,MAAM,CAAC/L,CAAC,CAAIuK,CAAAA,EAAKwB,MAAM,CAACpK,KAAK,CAAG0C,EAAM1C,KAAK,AAAD,EACpDiW,GACAvT,CAAAA,EAAMmG,SAAS,CAAG,EAAInG,EAAMmG,SAAS,AAAD,EAGpC,AAACqN,GACDxT,EAAMiH,UAAU,CAACgN,EAEzB,CACAC,cAAcX,CAAe,CAAE7L,CAAM,CAAED,CAAQ,CAAE,CAC7C,IAAMgM,EAAe,EAAE,CACnBU,EAAMhO,EAAYuB,EAAOvB,SAAS,CAAExK,EAAI+L,EAAO/L,CAAC,CAAEC,EAAI8L,EAAO9L,CAAC,CAAE0B,EAAQoK,EAAOpK,KAAK,CAAE1C,EAAS8M,EAAO9M,MAAM,CAAE+Y,EAAIC,EAAIC,EAAIC,EAC9H,IAAK,IAAMxD,KAAS7I,EAChB0M,EACI,AAACzM,EAAOpK,KAAK,CAAGoK,EAAO9M,MAAM,CAAK0V,CAAAA,EAAME,GAAG,CAAG9I,EAAO8I,GAAG,AAAD,EAC3DmD,EAAKhY,EACLiY,EAAKhY,EACDuK,AAAc,IAAdA,GAGA7I,GADAuW,EAAKM,EADLL,CAAAA,EAAKlZ,CAAK,EAGVe,GAAQkY,IAKRjZ,GADAkZ,EAAKK,EADLN,CAAAA,EAAKvW,CAAI,EAGT1B,GAAQkY,GAEZL,EAAatM,IAAI,CAAC,CACdxL,EAAGgY,EACH/X,EAAGgY,EACHtW,MAAOuW,EACPjZ,OAAQkZ,EACR3N,UAAW,EACXqK,IAAK,CACT,GACI+C,GACApN,CAAAA,EAAY,EAAIA,CAAQ,EAGhC,OAAOsN,CACX,CACAW,wBAAwBb,CAAe,CAAE7L,CAAM,CAAED,CAAQ,CAAE,CACvD,IAAqBgM,EAAe,EAAE,CAAEvN,EAAO,CAC3CvK,EAAG+L,EAAO/L,CAAC,CACXC,EAAG8L,EAAO9L,CAAC,CACX8L,OAAQA,CACZ,EAAGvB,EAAYuB,EAAOvB,SAAS,CAAEuN,EAAMjM,EAASpI,MAAM,CAAG,EAAGW,EAAQ,IAAI8F,EAA8B4B,EAAO9M,MAAM,CAAE8M,EAAOpK,KAAK,CAAE6I,EAAWD,GAC1IiO,EAAMtR,EAAI,EAEd,IAAK,IAAMyN,KAAS7I,EAChB0M,EACI,AAACzM,EAAOpK,KAAK,CAAGoK,EAAO9M,MAAM,CAAK0V,CAAAA,EAAME,GAAG,CAAG9I,EAAO8I,GAAG,AAAD,EAC3DxQ,EAAMiH,UAAU,CAACkN,GACbnU,EAAM2G,EAAE,CAACC,EAAE,CAAG5G,EAAM2G,EAAE,CAACE,EAAE,EACzBhD,AAZO,IAAI,CAYJyP,mBAAmB,CAACC,EAAiB,CAAA,EAAOvT,EAAOyT,EAAcvN,GAIxErD,IAAM6Q,GACN7P,AAjBO,IAAI,CAiBJyP,mBAAmB,CAACC,EAAiB,CAAA,EAAMvT,EAAOyT,EAAcvN,GAG3E,EAAErD,EAEN,OAAO4Q,CACX,CAKAY,eAAevQ,CAAK,CAEpBwQ,CAAS,CAETC,CAAY,CAAE,CACV1D,GAAa9W,SAAS,CAACsa,cAAc,CAACtK,KAAK,CAAC,IAAI,CAAEX,WAC9CtF,EAAMwQ,SAAS,EAEfxQ,EAAMwQ,SAAS,CAAClU,IAAI,CAAC,CAAEtF,OAAQ,AAACgJ,CAAAA,EAAMyF,IAAI,CAACzO,MAAM,EAAI,CAAA,EAAK,CAAE,EAEpE,CACA0Z,mBAAoB,CAChB,IAAM3Q,EAAS,IAAI,CAAE4Q,EAAa5Q,EAAO4Q,UAAU,EAAI,CAAC,EAAG,CAAEtI,QAAAA,CAAO,CAAE,CAAGtI,EAAOpJ,OAAO,CAAE8R,EAAqBJ,GAASI,oBAAsB,EAC7I,GAAIJ,GAASzB,QAAS,CAClB,IAAMgK,EAAe,CAAC,EAChBC,EAAc,AAACpL,IACjB,GAAIA,GAAMzF,OAAO0E,UAAW,CACxB,GAAM,CAAElL,MAAAA,EAAQ,CAAC,CAAE1C,OAAAA,EAAS,CAAC,CAAE,CAAG2O,EAAKzF,KAAK,CAAC0E,SAAS,CAChD,CAAE4D,WAAAA,EAAa,CAAC,CAAEC,YAAAA,EAAc,CAAC,CAAE,CAAGF,EAASyI,EAAgBrD,GAAsBlF,GAAcwI,EAAgBxI,EACrHD,EAAaC,EACbD,EAAaA,EACb9O,CAAAA,EAAQ8O,GACRxR,EAAUga,CAAAA,EAAgBvI,EAAcD,CAAS,GACjD0I,AAN2DxX,EAAQ1C,EAM5Dia,CAAY,GACf,CAACtL,EAAKG,OAAO,EAAI6H,GAAsBhI,EAAK7B,MAAM,IAC9C,AAACgN,CAAY,CAACnL,EAAK7B,MAAM,CAAC,EAC1BgN,CAAAA,CAAY,CAACnL,EAAK7B,MAAM,CAAC,CAAG,EAAE,AAAD,EAEjCgN,CAAY,CAACnL,EAAK7B,MAAM,CAAC,CAACP,IAAI,CAACoC,GAG3C,CACAA,GAAM9B,SAAS4I,QAAQ,AAACC,IACpBqE,EAAYrE,EAChB,EACJ,EAEA,IAAK,IAAM5I,KADXiN,EAAY9Q,EAAOiM,IAAI,EACF4E,EACbA,CAAY,CAAChN,EAAO,EAChBgN,CAAY,CAAChN,EAAO,CAACrI,MAAM,CAAGkN,GAC9BmI,CAAY,CAAChN,EAAO,CAAC2I,OAAO,CAAC,AAAC9G,IAC1B,IAAMuE,EAAQ2G,CAAU,CAAC/M,EAAO,CAACqN,OAAO,CAACxL,EAAK1G,CAAC,EAC/C,GAAIiL,AAAU,KAAVA,EAAc,CACd2G,CAAU,CAAC/M,EAAO,CAACsN,MAAM,CAAClH,EAAO,GACjC,IAAMzJ,EAAK,CAAC,kCAAkC,EAAEkF,EAAK7B,MAAM,EAAI,OAAO,CAAC,CACnEuN,EAAapR,EAAOuK,MAAM,CACzB8G,IAAI,CAAC,AAACjP,GAAMA,EAAE5B,EAAE,GAAKA,GAC1B,GAAI,CAAC4Q,EAAY,CACb,IAAME,EAAatR,EAAO6B,UAAU,CAAE0P,EAAavR,EAAOuK,MAAM,CAAC/O,MAAM,CASvEoS,GARAwD,EAAa,IAAIE,EAAWtR,EAAQ,CAChCyF,UAAW6C,EAAQ7C,SAAS,CAC5BpO,MAAOiR,EAAQjR,KAAK,CACpBmJ,GAAAA,EACAyJ,MAAOsH,EACP1L,QAAS,CAAA,EACT9E,MAAO,CACX,GACiC,CAC7ByQ,aAAc,SAClB,GACAxR,EAAOuK,MAAM,CAACjH,IAAI,CAAC8N,GACnBR,CAAU,CAAC/M,EAAO,CAACP,IAAI,CAACiO,GACxBX,CAAU,CAACpQ,EAAG,CAAG,EAAE,AACvB,CACA,IAAMiR,EAASL,EAAW5L,mBAAmB,CAAG,EAAGmH,EAAM3M,EAAOuK,MAAM,CAAC6G,EAAWnH,KAAK,CAAC,CACnFrT,OAAO,CAACmK,KAAK,EAAI,EAAG+F,EAAOwB,EAAQxB,IAAI,EACxC,CAAC,EAAE,EAAE2K,EAAO,CAAC,AAGjBzR,CAAAA,EAAOuK,MAAM,CAAC6G,EAAWnH,KAAK,CAAC,CAC1BzE,mBAAmB,CAAGiM,EAC3BzR,EAAOuK,MAAM,CAAC6G,EAAWnH,KAAK,CAAC,CAACrT,OAAO,CAACmK,KAAK,CACzC4L,EAAOjH,CAAAA,EAAKzF,KAAK,CAACc,KAAK,EAAI,CAAA,EAC/Bf,EAAOuK,MAAM,CAAC6G,EAAWnH,KAAK,CAAC,CAACnD,IAAI,CAAGA,EACvC8J,CAAU,CAACpQ,EAAG,CAAC8C,IAAI,CAACoC,EAAKzF,KAAK,CAACgK,KAAK,CACxC,CACJ,EAIZjK,CAAAA,EAAO2F,OAAO,CAAG,CAAC,EAClB3F,EAAO0R,QAAQ,CAAG,EAAE,CACpB1R,EAAO4Q,UAAU,CAAGA,EACpB,IAAM3E,EAAOjM,EAAO2R,SAAS,CAAC,GAAI,GAAI,EAAG3R,EAAO4Q,UAAU,EAC1D5Q,EAAOjB,SAAS,CAACkN,EACrB,CACJ,CAcA2F,uBAAuB/N,CAAM,CAAEoN,CAAI,CAAE,CACjC,IAAqBra,EAAUoJ,AAAhB,IAAI,CAAmBpJ,OAAO,CAAgDiD,EAAQqQ,AAAlClK,AAApD,IAAI,CAAuDkK,iBAAiB,AAA2B,CAACrG,EAAOhK,KAAK,CAAG,EAAE,CAAEgY,EAAY1D,GAAoBtU,GAAO0N,iBAC7KvH,AADW,IAAI,AACT,CAACnG,GAAO0N,gBAAgB,EAC9B1N,EAAM0N,eAAe,CAAG3Q,EAAQ2Q,eAAe,EAAGuK,EAAYlb,EAAQ6Q,0BAA0B,CAEpG7D,EAAWC,EAAOD,QAAQ,CAACmO,MAAM,CAAC,AAAC3c,GAAMyO,EAAOgC,OAAO,EAAI,CAACzQ,EAAE4c,MAAM,EAAGC,EAAepY,GAAOoY,cAAgBrb,EAAQqb,YAAY,EAAI,EAAGrM,EAAW5F,AAJpI,IAAI,CAIuI2F,OAAO,CAAC3F,AAJnJ,IAAI,CAIsJ4F,QAAQ,CAAC,CAClL,GAAI,CAACiM,EACD,OAEJ,IAAIK,EAAiB,EAAE,CAAEC,EAAYvM,EAASwM,WAAW,EAAE3Y,OAAS,EAAG4Y,EAAazM,EAASwM,WAAW,EAAErb,QAAU,CAChH8C,CAAAA,GAAO2N,yBACPyJ,CAAAA,EAAK3O,SAAS,CAAGzI,CAAAA,CAAAA,AAAkC,aAAlCA,EAAM2N,uBAAuB,AAAc,CAExD,EAER0K,EAAiBlS,AAdF,IAAI,AAcI,CAAC6R,EAAU,CAACZ,EAAMrN,GACzC,IAAI5E,EAAI,GACR,IAAK,IAAMyN,KAAS7I,EAAU,CAC1B,IAAM0O,EAASJ,CAAc,CAAC,EAAElT,EAAE,CAC9ByN,IAAU7G,IACVuM,EAAYA,GAAaG,EAAO7Y,KAAK,CACrC4Y,EAAaC,EAAOvb,MAAM,EAE9B,IAAMwb,EAAsBN,EAAgBjS,CAAAA,AAtBjC,IAAI,CAsBoC2O,KAAK,CAAC6D,GAAG,CAAGH,CAAS,EAAII,EAAsBR,EAAgBjS,CAAAA,AAtBvG,IAAI,CAsB0G4O,KAAK,CAAC4D,GAAG,CAAGH,CAAS,EAM9I,GALA5F,EAAM6F,MAAM,CAAGpE,GAAoBoE,EAAQ,CACvC3F,IAAKF,EAAMhJ,aAAa,CACxBnB,UAAYwP,EAAY,EAAIb,EAAK3O,SAAS,CAAG2O,EAAK3O,SAAS,AAC/D,GAEImK,EAAM7I,QAAQ,CAACpI,MAAM,EACrBiR,EAAMxM,KAAK,CAAC2G,UAAU,EAAEpL,OAAQ,CAChC,IAAMkX,EAAWpF,GAASb,EAAMxM,KAAK,CAAC2G,UAAU,CAAC+L,GAAG,CAAC,AAACC,GAAOA,EAAGhc,OAAO,EACjEmQ,SAAW6L,EAAG7b,MAAM,EAAI,IAAOiJ,CAAAA,AA/B9B,IAAI,CA+BiC4O,KAAK,CAAC4D,GAAG,CAAGH,CAAS,EAE7DK,EAAWjG,EAAM6F,MAAM,CAACvb,MAAM,CAAG,IACjC0V,EAAM6F,MAAM,CAACva,CAAC,EAAI2a,EAClBjG,EAAM6F,MAAM,CAACvb,MAAM,EAAI2b,EAE/B,CACA,GAAIT,EAAc,CACd,IAAMY,EAAO3P,KAAK8L,GAAG,CAACuD,EAAqB9F,EAAM6F,MAAM,CAAC7Y,KAAK,CAAG,GAAIqZ,EAAO5P,KAAK8L,GAAG,CAACyD,EAAqBhG,EAAM6F,MAAM,CAACvb,MAAM,CAAG,EAC/H0V,CAAAA,EAAM6F,MAAM,CAACxa,CAAC,EAAI+a,EAClBpG,EAAM6F,MAAM,CAAC7Y,KAAK,EAAI,EAAIoZ,EAC1BpG,EAAM6F,MAAM,CAACva,CAAC,EAAI+a,EAClBrG,EAAM6F,MAAM,CAACvb,MAAM,EAAI,EAAI+b,CAC/B,CACArG,EAAM2F,WAAW,CAAGlE,GAAoBoE,EAAQ,CAC5Cxa,EAAIwa,EAAOxa,CAAC,CAAGkI,AA9CR,IAAI,CA8CW+S,SAAS,CAG/Bhb,EAAGib,AA9VH,IA8VaV,EAAOva,CAAC,CAAGua,EAAOvb,MAAM,CACrC0C,MAAQ6Y,EAAO7Y,KAAK,CAAGuG,AAlDhB,IAAI,CAkDmB+S,SAAS,AAC3C,GAEItG,EAAM7I,QAAQ,CAACpI,MAAM,EACrBwE,AAtDO,IAAI,CAsDJ4R,sBAAsB,CAACnF,EAAOA,EAAM6F,MAAM,CAEzD,CACA,IAAMW,EAAuB,CAACvN,EAAM0F,EAAS,EAAE,CAAE8H,EAAY,CAAA,CAAI,IAC7DxN,EAAK9B,QAAQ,CAAC4I,OAAO,CAAC,AAACC,IACfyG,GAAazG,EAAM3G,MAAM,CACzBsF,EAAO9H,IAAI,CAACmJ,EAAMxM,KAAK,EAElB,AAACiT,GAAczG,EAAM3G,MAAM,EAChCsF,EAAO9H,IAAI,CAACmJ,EAAMxM,KAAK,EAEvBwM,EAAM7I,QAAQ,CAACpI,MAAM,EACrByX,EAAqBxG,EAAOrB,EAAQ8H,EAE5C,GACO9H,GAGX,GAAIxU,AAAuB,SAAvBA,EAAQuc,UAAU,EAClBtP,IAAW+B,GACX,IAAI,CAACwN,oBAAoB,EAGzB,CAACH,EAAqBrN,EAAU,KAAK,EAAG,CAAA,GACnCyN,IAAI,CAAC,AAACpT,GAAU8N,GAAuB9N,EAAMrJ,OAAO,CAACmK,KAAK,IAC/D,CAACgN,GAAuBnI,EAAS3F,KAAK,EAAErJ,QAAQmK,OAAQ,CACxD,IAAMuS,EAASL,EAAqBrN,GAAW0M,EAASgB,EAAOX,GAAG,CAAC,AAAC1S,GAAUA,EAAMrJ,OAAO,CAACmK,KAAK,EAAI,GAErGwS,EAAQD,EAAOX,GAAG,CAAC,CAAC,CAAEjN,KAAM,CAAE0M,YAAAA,CAAW,CAAE,CAAE,GAAMA,EAC/CA,EAAY3Y,KAAK,CAAG2Y,EAAYrb,MAAM,CACtC,GAAKyc,EAAWlB,EAAOjH,MAAM,CAAC,CAACoI,EAAK1S,IAAU0S,EAAM1S,EAAO,GAA4D2S,EAAuBC,AAArEJ,EAAMlI,MAAM,CAAC,CAACoI,EAAK1S,IAAU0S,EAAM1S,EAAO,GAAqCyS,EACxJI,EAAU,EAAGC,EAAU,EAC3BP,EAAO9G,OAAO,CAAC,CAACvM,EAAOjB,KACnB,IAGA8U,EAAMvG,GAAMwG,AAHSzB,CAAAA,CAAM,CAACtT,EAAE,CAAIuU,CAAK,CAACvU,EAAE,CAAGsT,CAAM,CAACtT,EAAE,CAAI,CAAA,EAG/B0U,EAAsB,GAAK,KAClDM,EAAO,EAAIF,CACX7T,CAAAA,EAAMc,KAAK,GAGPwS,CAAK,CAACvU,EAAE,CAAG,IACXgV,CAAAA,GAAQT,CAAK,CAACvU,EAAE,CAAG,EAAC,EAEpBgV,EAAOH,GACPA,CAAAA,EAAUG,CAAG,EAEbA,EAAOJ,GACPA,CAAAA,EAAUI,CAAG,EAEjB/T,EAAMgU,cAAc,CAAG,AAAChU,CAAAA,EAAMgU,cAAc,EAAIhU,EAAMc,KAAK,AAAD,EAAK+S,EAEvE,GAYA,AAACF,CAAAA,EAAU,MAASC,EAAU,GAAG,GAE7B,IAAI,CAACrE,UAAU,CAAG,IAClB,IAAI,CAACA,UAAU,GACf,IAAI,CAACxD,aAAa,CAACnI,GACnBoN,EAAKtE,GAAG,CAAG9I,EAAO8I,GAAG,CACrB,IAAI,CAACiF,sBAAsB,CAAC/N,EAAQoN,KAKpCqC,EAAO9G,OAAO,CAAC,AAACvM,IACZ,OAAOA,EAAMgU,cAAc,AAC/B,GACA,IAAI,CAACjI,aAAa,CAACnI,GACnB,IAAI,CAAC2L,UAAU,CAAG,EAE1B,CACJ,CAKA0E,WAAWvW,CAAC,CAAE,CACV,IAAMzE,EAAQ,IAAI,CAACA,KAAK,CAAEC,EAAcD,EAAMC,WAAW,CAAE4B,EAAO,EAAE,CACpE,GAAI5B,EAAa,CACb,IAAIgb,EAAqB,EACzBpZ,EAAKuI,IAAI,CAAC,CACNzJ,MAAOsa,EACPlY,aAAc/C,EAAM8G,MAAM,CAAC,EAAE,AACjC,GACA,IAAI0F,EAAO/H,EAAEyW,MAAM,CAACzO,OAAO,CAAChI,EAAE0W,SAAS,CAAC,CAClCC,EAAa,EAAE,CAGrB,KAAO5O,EAAK7B,MAAM,EAAI6B,AAAgB,KAAhBA,EAAK7B,MAAM,EAC7ByQ,EAAWhR,IAAI,CAACoC,GAChBA,EAAO/H,EAAEyW,MAAM,CAACzO,OAAO,CAACD,EAAK7B,MAAM,CAAC,CAExC,IAAK,IAAM6B,KAAQ4O,EAAWC,OAAO,GACjCxZ,EAAKuI,IAAI,CAAC,CACNzJ,MAAO,EAAEsa,EACTlY,aAAcyJ,CAClB,EAGA3K,CAAAA,EAAKS,MAAM,EAAI,GACfT,CAAAA,EAAKS,MAAM,CAAG,CAAA,CAEtB,CACA,OAAOT,CACX,CAeAyZ,gBAAiB,CACb,IAAqBtK,EAAoBlK,AAA1B,IAAI,CAA6BkK,iBAAiB,CAAEK,EAASvK,AAA7D,IAAI,CAAgEuK,MAAM,CAACwH,MAAM,CAAC,SAAU3c,CAAC,EACxG,OAAOA,EAAEsQ,IAAI,CAAChC,OAAO,EAAIgK,GAAsBtY,EAAEqb,SAAS,CAC9D,GAAIzZ,EAAUqX,GAAMrO,AAFL,IAAI,CAEQpJ,OAAO,CAACgQ,UAAU,EAAI,CAAC,EAAE,CAAC,EAAE,EAAE5P,QAASyd,EAAkBlK,EAAO8I,IAAI,CAAC,AAACjR,GAAM2L,GAAuB3L,EAAE+D,KAAK,GACrI,IAAK,IAAMlG,KAASsK,EAAQ,CACxB,IAAMnT,EAAQ,CAAC,EAEfR,EAAU,CAAEQ,MAAAA,CAAM,EAAGyC,EAAQqQ,CAAiB,CAACjK,EAAMyF,IAAI,CAAC7L,KAAK,CAAC,CAuBhE,GArBI,CAAA,CAACoG,EAAMyF,IAAI,CAACI,MAAM,EAClB,CAAC7F,EAAMyF,IAAI,CAACG,OAAO,EAClB5F,EAAMyF,IAAI,CAACG,OAAO,EACf5F,EAAMyF,IAAI,CAAC7L,KAAK,EAAImG,AAXjB,IAAI,CAWoB2F,OAAO,CAAC3F,AAXhC,IAAI,CAWmC4F,QAAQ,CAAC,CAAC/L,KAAK,GAC7DjD,CAAAA,EAAQiQ,OAAO,CAAG,CAAA,CAAI,EAGtBhN,GAAO+M,aACPsH,GAAoB,CAAA,EAAMtX,EAASyX,GAAMxU,EAAM+M,UAAU,CAAC,CAAC,EAAE,EAC7D5G,AAjBO,IAAI,CAiBJ0U,aAAa,CAAG,IAAM,CAAA,GAI7BzU,EAAMyF,IAAI,CAACI,MAAM,CACjBlP,EAAQoQ,MAAM,CAAG,CAAA,EAEZpQ,EAAQmQ,OAAO,EACpBnQ,CAAAA,EAAQiB,aAAa,CAAG,KAAI,EAI5BoI,EAAM0E,SAAS,EAAI8P,EAAiB,CACpC,GAAM,CAAE1d,OAAAA,EAAS,CAAC,CAAE0C,MAAAA,EAAQ,CAAC,CAAE,CAAGwG,EAAM0E,SAAS,CACjD,GAAIlL,EAAQ,IAAM1C,EAAS,IAAMkJ,EAAMsE,UAAU,GAAI,CACjD,IAAMoQ,EAAiBlb,EACnB,EAAK7C,CAAAA,EAAQI,OAAO,EAAIA,GAAW,CAAA,CACvCI,CAAAA,EAAMqC,KAAK,CAAG,CAAC,EAAEkb,EAAe,EAAE,CAAC,CACnCvd,EAAMwd,SAAS,EAAKxd,CAAAA,EAAMwd,SAAS,CAAG1R,KAAK2R,KAAK,CAAC9d,EAAS,GAAE,EAC5DK,EAAMuJ,UAAU,CAAG,UAEf/J,EAAQmQ,OAAO,EACf9G,EAAMwQ,SAAS,EAAElU,KAAK,CAClB9C,MAAOkb,CACX,EAGR,MAEIvd,EAAMqC,KAAK,CAAG,CAAC,EAAEA,EAAM,EAAE,CAAC,CAC1BrC,EAAMuJ,UAAU,CAAG,QAE3B,CAEAV,EAAM6U,SAAS,CAAG5G,GAAoBtX,EAASqJ,EAAMrJ,OAAO,CAACgQ,UAAU,CAC3E,CACA,KAAK,CAAC4N,eAAejK,EACzB,CAKAwK,WAAWxK,EAAS,IAAI,CAACA,MAAM,CAAE,CAC7B,IAAqBrR,EAAQ8G,AAAd,IAAI,CAAiB9G,KAAK,CAAEkD,EAAWlD,EAAMkD,QAAQ,CAAE6B,EAAa/E,EAAM+E,UAAU,CAAErH,EAAUoJ,AAAhG,IAAI,CAAmGpJ,OAAO,CAAEuR,EAASlK,EAAa,CAAC,EAAIrH,EAAQuR,MAAM,CAAE3B,EAAe5P,EAAQ4P,YAAY,CAAEwO,EAAuB9b,EAAM+b,UAAU,CAAGre,EAAQ2P,cAAc,CAAEP,EAAsBpP,EAAQoP,mBAAmB,CAClU,IAAK,IAAM/F,KAASsK,EAAQ,CACxB,IAAMgC,EAAetM,EAAMyF,IAAI,CAAC6G,YAAY,CAAEtI,EAAoB,CAAC,EAAGI,EAAU,CAAC,EAAGhG,EAAM,CAAC,EAAG6W,EAAW,eAAiBjV,EAAMyF,IAAI,CAAC7L,KAAK,CAAEsb,EAAa,CAAC,CAAClV,EAAME,OAAO,CAAEiV,EAAgBJ,GAAwBG,EAAYxQ,EAAY1E,EAAM0E,SAAS,CAErP1E,EAAMsE,UAAU,KAChBtE,EAAMoV,QAAQ,CAAG,CAAA,EACb7O,GACAnC,CAAAA,EAAQiR,CAAC,CAAG9O,CAAW,EAE3B0H,GAAoB,CAAA,EAEpBkH,EAAgBnR,EAAoBI,EAEpC8Q,EAAaxQ,EAAY,CAAC,EAE1B1G,EACI,CAAC,EACD+B,AAjBG,IAAI,CAiBA2B,YAAY,CAAC1B,EAAOA,EAAMsV,QAAQ,CAAG,SAAW,KAAK,IAI5DvV,AArBG,IAAI,CAqBAwB,YAAY,EAAIvD,GAEvB2P,GAAqBvP,EAAK2B,AAvBvB,IAAI,CAuB0BwB,YAAY,CAACvB,IAE7CD,AAzBE,IAAI,AAyBA,CAACkV,EAAS,GACjBlV,AA1BG,IAAI,AA0BD,CAACkV,EAAS,CAAG9Y,EAASC,CAAC,CAAC6Y,GACzB3Y,IAAI,CAAC,CAGNtF,OAAQ,IAAQsV,CAAAA,GAAgB,CAAA,CACpC,GACK/P,GAAG,CAACwD,AAhCN,IAAI,CAgCS7D,KAAK,EACrB6D,AAjCG,IAAI,AAiCD,CAACkV,EAAS,CAACM,OAAO,CAAG,CAAA,IAInCvV,EAAM8D,IAAI,CAAC,CACPE,kBAAAA,EACAI,QAAAA,EACAhG,IAAAA,EACAlC,MAAO6D,AAzCA,IAAI,AAyCE,CAACkV,EAAS,CACvBxQ,SAAUzE,EAAMyE,QAAQ,CACxBtI,SAAAA,EACA+L,OAAAA,EACAxD,UAAAA,EACAH,UAAWvE,EAAMuE,SAAS,AAC9B,GAGIwB,GAAuB/F,EAAME,OAAO,EACpCF,CAAAA,EAAMwV,OAAO,CAAG7e,EAAQmP,cAAc,CAClC/F,AApDG,IAAI,CAoDA0V,aAAa,CAACzV,GACrBD,AArDG,IAAI,CAqDA2V,cAAc,CAAC1V,EAAK,CAEvC,CACJ,CAMA0V,eAAe1V,CAAK,CAAE,CAClB,MAAO,AAAC,CAAA,CAACA,EAAMyF,IAAI,CAACI,MAAM,IAAI7F,EAAMyF,IAAI,CAACG,OAAO,AAAD,GAC3C5F,EAAMO,EAAE,AAChB,CAMAkV,cAAczV,CAAK,CAAE,CACjB,GAAM,CAAEoI,eAAAA,CAAc,CAAE,CAAGpI,EAAMD,MAAM,CAACpJ,OAAO,CAC3C6e,EAAU,CAAA,EAAOG,EACrB,GAAI,AAAC3V,EAAMyF,IAAI,CAAC7B,MAAM,GAAK,IAAI,CAAC+B,QAAQ,EACpC3F,EAAMyF,IAAI,CAACI,MAAM,CACjB,GAAIuC,EACAoN,EAAUxV,EAAMO,EAAE,MAIlB,IADAoV,EAAa3V,EAAMyF,IAAI,CAChB,CAAC+P,GACA,AAA6B,KAAA,IAAtBG,EAAW/R,MAAM,EACxB+R,CAAAA,EAAa,IAAI,CAACjQ,OAAO,CAACiQ,EAAW/R,MAAM,CAAC,AAAD,EAE3C+R,EAAW/R,MAAM,GAAK,IAAI,CAAC+B,QAAQ,EACnC6P,CAAAA,EAAUG,EAAWpV,EAAE,AAAD,EAKtC,OAAOiV,CACX,CAKAI,YAAYrV,CAAE,CAAErG,CAAM,CAAE,CACpBwT,GAAM,GAAI,CAAA,EAAO,KAAK,EAAG,CAAE,sBAAuB,yBAA0B,GAC5E,IAAI,CAACmI,WAAW,CAACtV,EAAIrG,EACzB,CACA4b,SAAU,CACN,IAAqBrQ,EAAO1F,AAAb,IAAI,CAAgB2F,OAAO,CAAC3F,AAA5B,IAAI,CAA+B4F,QAAQ,CAAC,AACvDF,CAAAA,GAAQuI,GAAuBvI,EAAK7B,MAAM,GAC1C7D,AAFW,IAAI,CAER8V,WAAW,CAACpQ,EAAK7B,MAAM,CAAE,CAAA,EAAM,CAAEmS,QAAS,kBAAmB,EAE5E,CACAC,aAAc,CAEV,GAAM,CAAEC,QAAAA,CAAO,CAAEC,QAAAA,CAAO,CAAE,CAAG,KAAK,CAACF,YAAY,IAAI,CAACG,cAAc,EAIlE,OAHA,IAAI,CAACC,QAAQ,CAAGH,EAChB,IAAI,CAACI,QAAQ,CAAGH,EAET,KAAK,CAACF,aACjB,CAgBAM,iBAAiBC,CAAI,CAAEC,CAAW,CAAE,CAChC,IAAMC,EAAM5I,GAAsB0I,GAAQA,EAAO,EAAE,CAAEG,EAAM7I,GAAsB2I,GAAeA,EAAc,EAAE,CAAEG,EAAgBF,EAAIrL,MAAM,CAAC,SAAUwL,CAAI,CAAEC,CAAI,CAAE9X,CAAC,EAChK,IAAM6E,EAASsK,GAAmB2I,EAAKjT,MAAM,CAAE,IAK/C,OAJI,AAAwB,KAAA,IAAjBgT,CAAI,CAAChT,EAAO,EACnBgT,CAAAA,CAAI,CAAChT,EAAO,CAAG,EAAE,AAAD,EAEpBgT,CAAI,CAAChT,EAAO,CAACP,IAAI,CAACtE,GACX6X,CACX,EAAG,CACC,GAAI,EAAE,AACV,GAEA,IAAK,IAAMhT,KAAUjO,OAAOkP,IAAI,CAAC8R,GAAgB,CAC7C,IAAMhT,EAAWgT,CAAa,CAAC/S,EAAO,CACtC,GAAI,AAAY,KAAXA,GAAmB8S,AAAwB,KAAxBA,EAAIzF,OAAO,CAACrN,GAAiB,CACjD,IAAK,IAAM4I,KAAS7I,EAChBgT,CAAa,CAAC,GAAG,CAACtT,IAAI,CAACmJ,EAE3B,QAAOmK,CAAa,CAAC/S,EAAO,AAChC,CACJ,CACA,OAAO+S,CACX,CAKAG,SAAU,CACN,IAAqBC,EAAS,IAAI,CAACR,IAAI,CAAC7D,GAAG,CAAC,SAAUpd,CAAC,EACnD,OAAOA,EAAEiL,EAAE,AACf,GAIA,OAHAR,AAHe,IAAI,CAGZ4Q,UAAU,CAAG5Q,AAHL,IAAI,CAGQuW,gBAAgB,CAAC,IAAI,CAACC,IAAI,CAAEQ,GACvDhX,AAJe,IAAI,CAIZ2F,OAAO,CAAG,CAAC,EAClB3F,AALe,IAAI,CAKZ0R,QAAQ,CAAG,EAAE,CACb1R,AANQ,IAAI,CAML2R,SAAS,CAAC,GAAI,GAAI,EAAG3R,AANpB,IAAI,CAMuB4Q,UAAU,EAAI,CAAC,EAC7D,CACAe,UAAUnR,CAAE,CAAEyJ,CAAK,CAAEpQ,CAAK,CAAEkB,CAAI,CAAE8I,CAAM,CAAE,CACtC,IAAqBD,EAAW,EAAE,CAAE3D,EAAQD,AAA7B,IAAI,CAAgCuK,MAAM,CAACN,EAAM,CAC5DlT,EAAS,EAAG0V,EAEhB,IAAK,IAAMzN,KAAMjE,CAAI,CAACyF,EAAG,EAAI,EAAE,CAE3BzJ,EAASmM,KAAKC,GAAG,CAACsJ,AADlBA,CAAAA,EAAQzM,AAJG,IAAI,CAIA2R,SAAS,CAAC3R,AAJd,IAAI,CAIiBuK,MAAM,CAACvL,EAAE,CAACwB,EAAE,CAAExB,EAAGnF,EAAQ,EAAGkB,EAAMyF,EAAE,EAC5CzJ,MAAM,CAAG,EAAGA,GACpC6M,EAASN,IAAI,CAACmJ,GAElB,IAAM/G,EAAO,IAAI1F,AARF,IAAI,CAQKiX,SAAS,GAAGtT,IAAI,CAACnD,EAAIyJ,EAAOrG,EAAU7M,EAAQ8C,EARvD,IAAI,CAQkEgK,GACrF,IAAK,IAAM4I,KAAS7I,EAChB6I,EAAMyK,UAAU,CAAGxR,EAQvB,OANA1F,AAZe,IAAI,CAYZ2F,OAAO,CAACD,EAAKlF,EAAE,CAAC,CAAGkF,EAC1B1F,AAbe,IAAI,CAaZ0R,QAAQ,CAACpO,IAAI,CAACoC,GACjBzF,IACAA,EAAMyF,IAAI,CAAGA,EACbA,EAAKzF,KAAK,CAAGA,GAEVyF,CACX,CAMAyR,SAAU,CACN,MAAO,CAAC,CAAC,IAAI,CAACC,SAAS,CAACC,QAAQ,AACpC,CACA1T,KAAKzK,CAAK,CAAEtC,CAAO,CAAE,CACjB,IAAMoJ,EAAS,IAAI,CAAEzG,EAAqB2U,GAAoBtX,EAAQsE,aAAa,CAAEtE,EAAQuC,WAAW,EAAGme,EAAkBjK,GAAuBrN,EAAQ,aAAc,AAAC3F,IACvK,IAAMzD,EAAUyD,EAAMO,WAAW,CAE7B8S,GAAsB9W,EAAQ2gB,gBAAgB,GAC9C,CAAC7J,GAAsB9W,EAAQoP,mBAAmB,IAClDpP,EAAQoP,mBAAmB,CAAGpP,EAAQ2gB,gBAAgB,CACtD,OAAO3gB,EAAQ2gB,gBAAgB,EAE/B7J,GAAsB9W,EAAQsE,aAAa,GAC3C,CAACwS,GAAsB9W,EAAQ+Q,gBAAgB,IAC/C/Q,EAAQ+Q,gBAAgB,CAAG/Q,EAAQsE,aAAa,CAChD,OAAOtE,EAAQsE,aAAa,EAGhC,IAAM0L,EAAayH,GAAMzX,EAAQgQ,UAAU,EAAI,CAAC,EAChDhQ,CAAAA,EAAQuU,MAAM,EAAEqB,QAAQ,AAAC3S,IACrB+M,EAAWtD,IAAI,CAAC4C,KAAK,CAACU,EAAYyH,GAAMxU,EAAM+M,UAAU,EAAI,CAAC,GACjE,GACA,IAAI,CAACwM,oBAAoB,CAAGxM,EAAWyM,IAAI,CAAC,AAACT,GAAOA,EAAG7L,OAAO,CAClE,GACA,KAAK,CAACpD,KAAKzK,EAAOtC,GAElB,OAAOoJ,EAAO8H,OAAO,CAErB9H,EAAOwX,cAAc,CAAClU,IAAI,CAACgU,GACvBtX,EAAOpJ,OAAO,CAACoP,mBAAmB,GAClChG,EAAOwX,cAAc,CAAClU,IAAI,CAAC+J,GAAuBrN,EAAQ,QAASA,EAAOyX,kBAAkB,GAC5FzX,EAAOwX,cAAc,CAAClU,IAAI,CAAC+J,GAAuBrN,EAAQ,cAAe,SAAUrC,CAAC,EAChF,IAAMzE,EAAQ8G,EAAO9G,KAAK,AACtBA,CAAAA,EAAMC,WAAW,EAEjBD,EAAMC,WAAW,CAACkC,gBAAgB,CAAC2E,EAAOkU,UAAU,CAACvW,GAE7D,IACAqC,EAAOwX,cAAc,CAAClU,IAAI,CAAC+J,GAAuBrN,EAAQ,SAE1D,SAAUrC,CAAC,CAAExD,CAAM,EACf,IAAMhB,EAAc,IAAI,CAACD,KAAK,CAACC,WAAW,AACtCA,CAAAA,GAAewE,EAAE/G,OAAO,CAACuC,WAAW,EACpCA,EAAYmF,MAAM,CAACX,EAAE/G,OAAO,CAACuC,WAAW,EAE5C,IAAI,CAACue,oBAAoB,CAAG,IAAI,CAACtE,oBAAoB,AACzD,IACApT,EAAOwX,cAAc,CAAClU,IAAI,CAAC+J,GAAuBrN,EAAQ,UAAW,SAAuBrC,CAAC,EACzF,IAAMzE,EAAQ,IAAI,CAACA,KAAK,AACpBA,CAAAA,EAAMC,WAAW,EAAI,CAACwE,EAAEga,mBAAmB,GAC3Cze,EAAMC,WAAW,CAACQ,OAAO,GACzBT,EAAMC,WAAW,CAAG,KAAK,EAEjC,KAEA,AAACD,EAAMC,WAAW,EAClBD,CAAAA,EAAMC,WAAW,CAAG,IAl0E8BoB,EAk0EFrB,EAAOK,EAAkB,EAE7EyG,EAAOwX,cAAc,CAAClU,IAAI,CAAC+J,GAAuBnU,EAAMC,WAAW,CAAE,KAAM,SAAUwE,CAAC,EAClF,IAAMia,EAAiB,IAAI,CAAC/d,KAAK,CAAG8D,EAAEK,QAAQ,CAC9C,IAAK,IAAIgB,EAAI,EAAGA,EAAI4Y,EAAgB5Y,IAChCgB,EAAO+V,OAAO,EAEtB,GACJ,CAKA0B,mBAAmBpd,CAAK,CAAE,CACtB,IAAqB4F,EAAQ5F,EAAM4F,KAAK,CAAEwV,EAAUxV,GAAOwV,QAEvDxH,GAAuBwH,KACvBxV,EAAMd,QAAQ,CAAC,IACfa,AAJW,IAAI,CAIR8V,WAAW,CAACL,EAAS,CAAA,EAAM,CAAEO,QAAS,OAAQ,GAE7D,CAKArU,aAAa1B,CAAK,CAAEM,CAAK,CAAE,CACvB,IAAqB2J,EAAqB8D,GAAuBhO,AAAlD,IAAI,CAAqDkK,iBAAiB,EACrFlK,AADW,IAAI,CACRkK,iBAAiB,CACxB,CAAC,EAAIrQ,EAAQoG,GAASiK,CAAiB,CAACjK,EAAMyF,IAAI,CAAC7L,KAAK,CAAC,EAAI,CAAC,EAAGjD,EAAU,IAAI,CAACA,OAAO,CAAEihB,EAAetX,GAAS3J,EAAQM,MAAM,EAAIN,EAAQM,MAAM,CAACqJ,EAAM,EAAI,CAAC,EAAGkF,EAAYxF,GAAOqE,gBAAkB,GAGzM/H,EAAO,CACH,OAAU,AAAC0D,GAASA,EAAM2H,WAAW,EACjC/N,EAAM+N,WAAW,EACjBiQ,EAAajQ,WAAW,EACxBhR,EAAQgR,WAAW,CACvB,eAAgBuG,GAAmBlO,GAASA,EAAM4H,WAAW,CAAEhO,EAAMgO,WAAW,CAAEgQ,EAAahQ,WAAW,CAAEjR,EAAQiR,WAAW,EAC/H,UAAa5H,GAAO6X,iBAChBje,EAAMie,eAAe,EACrBD,EAAaC,eAAe,EAC5BlhB,EAAQkhB,eAAe,CAC3B,KAAQ7X,GAAO5I,OAAS,IAAI,CAACA,KAAK,AACtC,EAqBA,OAnBIoO,AAAgD,KAAhDA,EAAUyL,OAAO,CAAC,2BAClB3U,EAAKzF,IAAI,CAAG,OACZyF,CAAI,CAAC,eAAe,CAAG,GAGlBkJ,AAA8D,KAA9DA,EAAUyL,OAAO,CAAC,yCACvB3U,CAAI,CAAC,eAAe,CAAGsb,EAAa/P,OAAO,EAAIlR,EAAQkR,OAAO,EAAI,EAClEvL,EAAKwb,MAAM,CAAG,WAGTtS,AAAkD,KAAlDA,EAAUyL,OAAO,CAAC,4BACvB3U,EAAKzF,IAAI,CAAG,OAEPyJ,GAASsX,EAAa7P,UAAU,EAErCzL,CAAAA,EAAKzF,IAAI,CAAGO,GAAMkF,EAAKzF,IAAI,EACtB+T,QAAQ,CAACgN,EAAa7P,UAAU,EAChCjS,GAAG,EAAC,EAENwG,CACX,CAKAyb,kBAAkBtS,CAAI,CAAEyE,CAAW,CAAEH,CAAU,CAAEC,CAAK,CAAEK,CAAQ,CAAE,CAC9D,IAAqBpR,EAAN,IAAI,EAAkBA,MAAOmR,EAASnR,GAAOtC,SAASyT,OACrE,GAAI3E,EAAM,CACN,IAAMuS,EAAY/K,GAAuBxH,EAAM,CAC3C2E,OAAQA,EACRJ,MAAOA,EACPC,kBAAmBlK,AALZ,IAAI,CAKekK,iBAAiB,CAC3CC,YAAaA,EACbC,iBAAkBJ,EAClBhK,OARO,IAAI,CASXsK,SAAUA,CACd,GAAIrK,EAAQD,AAVD,IAAI,CAUIuK,MAAM,CAAC7E,EAAK1G,CAAC,CAAC,CAC7BiB,IACAA,EAAM5I,KAAK,CAAG4gB,EAAU5gB,KAAK,CAC7B4I,EAAM+J,UAAU,CAAGiO,EAAUjO,UAAU,EAE3C,IAAIhL,EAAI,GAER,IAAK,IAAMyN,KAAU/G,EAAK9B,QAAQ,EAAI,EAAE,CACpC5D,AAlBO,IAAI,CAkBJgY,iBAAiB,CAACvL,EAAOwL,EAAU5gB,KAAK,CAAE4gB,EAAUjO,UAAU,CAAE,EAAEhL,EAAG0G,EAAK9B,QAAQ,CAACpI,MAAM,CAExG,CACJ,CACA0c,gBAAiB,CACb,IAAMlY,EAAS,IAAI,CACb,CAAEuK,OAAAA,CAAM,CAAEoE,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAE,CAAG5O,EAC3B/B,EAAa+B,EAAO9G,KAAK,CAAC+E,UAAU,CAOpCka,EAAiB,AAAClY,GAAWhC,EAC/B,EACC+B,EAAO2B,YAAY,CAAC1B,EAAM,CAAC,eAAe,EAAI,EACnD,IAAK,IAAMA,KAASsK,EAAQ,CACxB,GAAM,CAAE6H,YAAaE,CAAM,CAAE5O,QAAAA,CAAO,CAAE,CAAGzD,EAAMyF,IAAI,CAEnD,GAAI4M,GAAU5O,EAAS,CACnB,GAAM,CAAE3M,OAAAA,CAAM,CAAE0C,MAAAA,CAAK,CAAE3B,EAAAA,CAAC,CAAEC,EAAAA,CAAC,CAAE,CAAGua,EAAQ8F,EAAcD,EAAelY,GAAQoY,EAAS1J,EAAM2J,QAAQ,CAACxgB,EAAG,CAAA,GAAOygB,EAAU5J,EAAM2J,QAAQ,CAACxgB,EAAI2B,EAAO,CAAA,GAAO+e,EAAS5J,EAAM0J,QAAQ,CAACvgB,EAAG,CAAA,GAAO0gB,EAAU7J,EAAM0J,QAAQ,CAACvgB,EAAIhB,EAAQ,CAAA,GAIjO2hB,EAAKL,AAAW,IAAXA,EACDD,EAAc,EACd3K,GAAMkB,EAAM2J,QAAQ,CAACxgB,EAAG,CAAA,GAAOsgB,EAAa,CAAA,GAAOO,EAAKJ,IAAY5J,EAAM6D,GAAG,CAC7E7D,EAAM6D,GAAG,CAAG4F,EAAc,EAC1B3K,GAAMkB,EAAM2J,QAAQ,CAACxgB,EAAI2B,EAAO,CAAA,GAAO2e,EAAa,CAAA,GAAOQ,EAAKJ,IAAW5J,EAAM4D,GAAG,CACpF5D,EAAM4D,GAAG,CAAG4F,EAAc,EAC1B3K,GAAMmB,EAAM0J,QAAQ,CAACvgB,EAAG,CAAA,GAAOqgB,EAAa,CAAA,GAAOS,EAAKJ,AAAY,IAAZA,EACxDL,EAAc,EACd3K,GAAMmB,EAAM0J,QAAQ,CAACvgB,EAAIhB,EAAQ,CAAA,GAAOqhB,EAAa,CAAA,GAEnDzT,EAAY,CACd7M,EAAGoL,KAAK8L,GAAG,CAAC0J,EAAIC,GAChB5gB,EAAGmL,KAAK8L,GAAG,CAAC4J,EAAIC,GAChBpf,MAAOyJ,KAAK4V,GAAG,CAACH,EAAKD,GACrB3hB,OAAQmM,KAAK4V,GAAG,CAACD,EAAKD,EAC1B,CACA3Y,CAAAA,EAAM8Y,KAAK,CAAGpU,EAAU7M,CAAC,CAAI6M,EAAUlL,KAAK,CAAG,EAC/CwG,EAAMkG,KAAK,CAAGxB,EAAU5M,CAAC,CAAI4M,EAAU5N,MAAM,CAAG,EAChDkJ,EAAM0E,SAAS,CAAGA,CACtB,MAGI,OAAO1E,EAAM8Y,KAAK,CAClB,OAAO9Y,EAAMkG,KAAK,AAE1B,CACJ,CAkCA2P,YAAYtV,CAAE,CAAErG,CAAM,CAAE6e,CAAc,CAAE,CAgCpCnL,GA/Be,IAAI,CA+Ba,cA/BCD,GAAqB,CAClDyG,UAAW7T,EACXyY,eAAgBjZ,AAFL,IAAI,CAEQ4F,QAAQ,CAC/BzL,OAAQgU,GAAmBhU,EAAQ,CAAA,GACnC6F,OAJW,IAAI,AAKnB,EAAGgZ,GAce,SAAUE,CAAI,EAC5B,IAAMlZ,EAASkZ,EAAKlZ,MAAM,AAE1BA,CAAAA,EAAOmZ,cAAc,CAAGD,EAAKD,cAAc,CAC3CjZ,EAAO4F,QAAQ,CAAGsT,EAAK7E,SAAS,CAEhCrU,EAAOlF,OAAO,CAAG,CAAA,EACboe,EAAK/e,MAAM,EACX6F,EAAO9G,KAAK,CAACiB,MAAM,EAE3B,EAGJ,CAOAgF,SAASoB,CAAK,CAAE,CACZ,IAAI,CAAC3J,OAAO,CAACwiB,mBAAmB,CAAG,CAAA,EACnC,KAAK,CAACja,SAASoB,EAAO,CAAA,GACtB,IAAI,CAAC3J,OAAO,CAACwiB,mBAAmB,CAAG,CAAA,CACvC,CACApN,cAAcC,CAAI,CAAE,CAChB,IAAqBrV,EAAUoJ,AAAhB,IAAI,CAAmBpJ,OAAO,CAAEuV,EAASnM,AAAzC,IAAI,CAA4C4F,QAAQ,CAAgCwG,EAAWC,AAA3BrM,AAAxE,IAAI,CAA2E2F,OAAO,AAAwB,CAACwG,EAAO,CAAEzE,EAAmB,AAAmC,WAAnC,OAAO9Q,EAAQ8Q,eAAe,EACpL9Q,EAAQ8Q,eAAe,CAChB9D,EAAW,EAAE,CAAE3D,EAAQD,AAFnB,IAAI,CAEsBuK,MAAM,CAAC0B,EAAKjN,CAAC,CAAC,CAEnDyE,EAAgB,EACpB,IAAK,IAAIgJ,KAASR,EAAKrI,QAAQ,CAC3B6I,EAAQzM,AANG,IAAI,CAMAgM,aAAa,CAACS,GAC7B7I,EAASN,IAAI,CAACmJ,GACV,AAACA,EAAMuF,MAAM,EACbvO,CAAAA,GAAiBgJ,EAAME,GAAG,AAAD,EAIjC2B,GAAW1K,EAAU,CAACpO,EAAG6jB,IAAO,AAAC7jB,CAAAA,EAAE8jB,SAAS,EAAI,CAAA,EAAMD,CAAAA,EAAEC,SAAS,EAAI,CAAA,GAErE,IAAI3M,EAAMwB,GAAmBlO,GAAOgU,eAAgBhU,GAAOrJ,QAAQmK,MAAO0C,GAsB1E,OArBIxD,GACAA,CAAAA,EAAMc,KAAK,CAAG4L,CAAE,EAEhB1M,GAAO4F,SAAWjP,EAAQ0R,OAAO,EAAEG,iBACnCkE,CAAAA,GAAO/V,EAAQ0R,OAAO,CAACG,eAAe,AAAD,EAErCwD,EAAKiL,UAAU,EAAEjX,OAAO4F,SAAW7F,AAtBxB,IAAI,CAsB2B4F,QAAQ,GAAKqG,EAAKpI,MAAM,EAClEoI,CAAAA,EAAKvI,OAAO,CAAG,CAAA,CAAI,EAEvBkK,GAAqB3B,EAAM,CACvBrI,SAAUA,EACVH,cAAeA,EAEfuO,OAAQ,CAAE7D,CAAAA,GAAmBlO,GAAOyD,QAAS,CAAA,IAAUiJ,EAAM,CAAC,EAC9D7G,OAAQmG,EAAKvI,OAAO,EAAI,CAACD,EACzBoC,QAAS5F,GAAO4F,QAChB0G,aAAeN,EAAKpS,KAAK,CAAI6N,CAAAA,EAAkB,EAAI0E,EAASvS,KAAK,AAAD,EAChEiN,KAAMqH,GAAmBlO,GAAO6G,KAAM,IACtCwS,UAAWnL,GAAmBlO,GAAOqZ,UAAW,CAAC3M,GACjDA,IAAKA,CACT,GACOV,CACX,CACAsN,aAAa1V,CAAM,CAAED,CAAQ,CAAE,CAC3B,OAAO,IAAI,CAACyM,aAAa,CAAC,CAAA,EAAMxM,EAAQD,EAC5C,CACA4V,WAAW3V,CAAM,CAAED,CAAQ,CAAE,CACzB,OAAO,IAAI,CAAC2M,uBAAuB,CAAC,CAAA,EAAM1M,EAAQD,EACtD,CACA6V,MAAM5V,CAAM,CAAED,CAAQ,CAAE,CACpB,OAAO,IAAI,CAAC2M,uBAAuB,CAAC,CAAA,EAAO1M,EAAQD,EACvD,CACA8V,QAAQ7V,CAAM,CAAED,CAAQ,CAAE,CACtB,OAAO,IAAI,CAACyM,aAAa,CAAC,CAAA,EAAOxM,EAAQD,EAC7C,CACA7E,UAAUkN,CAAI,CAAE,CACZ,IAAMjM,EAAS,IAAI,CAAEpJ,EAAUoJ,EAAOpJ,OAAO,CAAE+iB,EAAgB,CAAC1N,EAEhEY,EAASO,GAA2BpN,GAAS4F,EAAUwM,EAAawH,EAAYjN,EAC3EV,GAASY,EAAOgN,UAAU,CAAC,wCAE5B,AAAC,CAAA,IAAI,CAACtP,MAAM,EAAI,EAAE,AAAD,EAAGiC,OAAO,CAAC,AAACvM,IACrBA,EAAM4F,OAAO,EACb5F,EAAMtG,OAAO,EAErB,GAEA,KAAK,CAACoF,YAENkN,EAAOjM,EAAO+W,OAAO,IAGzB/W,EAAOiM,IAAI,CAAGA,EAAOA,GAAQjM,EAAOiM,IAAI,CACxCrG,EAAW5F,EAAO2F,OAAO,CAACkH,EAAO,CAClB,KAAXA,GAAkBjH,IAClB5F,EAAO8V,WAAW,CAAC,GAAI,CAAA,GACvBjJ,EAAS7M,EAAO4F,QAAQ,CACxBA,EAAW5F,EAAO2F,OAAO,CAACkH,EAAO,EAEjC,AAACjH,EAAS3F,KAAK,EAAE4F,SACjB7F,CAAAA,EAAOkK,iBAAiB,CAAGiD,GAA8B,CACrDjC,KAAMtF,EAAS/L,KAAK,CAAG,EACvBsR,OAAQvU,EAAQuU,MAAM,CACtBL,GAAImB,EAAKlV,MAAM,CACfiU,SAAU,CACNtD,gBAAiB1H,EAAOpJ,OAAO,CAAC8Q,eAAe,CAC/Cf,aAAc/P,EAAQ+P,YAAY,AACtC,CACJ,EAAC,EAGL0C,GAAyBL,SAAS,CAAChJ,EAAO2F,OAAO,CAAC3F,EAAO4F,QAAQ,CAAC,CAAE,AAACF,IACjE,IAAMtD,EAAIsD,EAAK7B,MAAM,CACjBuF,EAAO,CAAA,EAKX,OAJA1D,EAAKhC,OAAO,CAAG,CAAA,EACXtB,CAAAA,GAAKA,AAAM,KAANA,CAAO,GACZgH,CAAAA,EAAOpJ,EAAO2F,OAAO,CAACvD,EAAE,AAAD,EAEpBgH,CACX,GAEAC,GAAyBL,SAAS,CAAChJ,EAAO2F,OAAO,CAAC3F,EAAO4F,QAAQ,CAAC,CAAChC,QAAQ,CAAE,AAACA,IAC1E,IAAIwF,EAAO,CAAA,EACX,IAAK,IAAMqD,KAAS7I,EAChB6I,EAAM/I,OAAO,CAAG,CAAA,EACZ+I,EAAM7I,QAAQ,CAACpI,MAAM,EACrB4N,CAAAA,EAAO,AAACA,CAAAA,GAAQ,EAAE,AAAD,EAAG0Q,MAAM,CAACrN,EAAM7I,QAAQ,CAAA,EAGjD,OAAOwF,CACX,GACApJ,EAAOgM,aAAa,CAACC,GAErBjM,EAAO+S,SAAS,CAAI/S,EAAO2O,KAAK,CAAC6D,GAAG,CAAGxS,EAAO4O,KAAK,CAAC4D,GAAG,CACvDxS,EAAO2F,OAAO,CAAC,GAAG,CAACyM,WAAW,CAAGA,EAAc,CAC3Cta,EAAG,EACHC,EAAG,EACH0B,MAtmCI,IAumCJ1C,OAvmCI,GAwmCR,EACAiJ,EAAO2F,OAAO,CAAC,GAAG,CAAC2M,MAAM,CAAGsH,EAAa1L,GAAoBkE,EAAa,CACtE3Y,MAAQ2Y,EAAY3Y,KAAK,CAAGuG,EAAO+S,SAAS,CAC5CzQ,UAAY1L,CAAAA,CAAAA,AAAoC,aAApCA,EAAQ4Q,uBAAuB,AAAc,EACzDmF,IAAKV,EAAKU,GAAG,AACjB,GAGI,CAAA,IAAI,CAACyG,oBAAoB,EAAI,IAAI,CAACsE,oBAAoB,AAAD,GACrD,IAAI,CAAClD,cAAc,GAEvBxU,EAAO4R,sBAAsB,CAAC3F,EAAM2N,GAEhC,AAAC5Z,EAAO+Z,SAAS,EAChBnjB,EAAQ+P,YAAY,EACrB3G,EAAOgY,iBAAiB,CAAChY,EAAOiM,IAAI,EAGpCrV,EAAQoP,mBAAmB,EAAIJ,EAASwM,WAAW,GACnDzF,EAAM/G,EAASwM,WAAW,CAC1BpS,EAAO2O,KAAK,CAACqL,WAAW,CAACrN,EAAI7U,CAAC,CAAE6U,EAAI7U,CAAC,CAAG6U,EAAIlT,KAAK,CAAE,CAAA,GACnDuG,EAAO4O,KAAK,CAACoL,WAAW,CAACrN,EAAI5U,CAAC,CAAE4U,EAAI5U,CAAC,CAAG4U,EAAI5V,MAAM,CAAE,CAAA,GACpDiJ,EAAO2O,KAAK,CAACsL,QAAQ,GACrBja,EAAO4O,KAAK,CAACqL,QAAQ,IAGzBja,EAAOkY,cAAc,GACjByB,GACA3Z,EAAO2Q,iBAAiB,EAEhC,CACJ,CACApB,GAAcpU,cAAc,CAAG+S,GAAoBjB,GAAc9R,cAAc,CAx7CXmL,IAy7CpEsH,GAAqB2B,GAAcrZ,SAAS,CAAE,CAC1CgkB,YAAanN,GACbvL,aAAcM,EAA2BZ,aAAa,CAACM,YAAY,CACnEL,SAAU,aACVgZ,YAAa,CAAA,EACbC,mBAAoB,CAAA,EACpBC,UAAWtN,GACXuN,aAAc,YACdjZ,eAAgB,CAAC,IAAK,IAAK,QAAS,aAAa,CACjDC,cAAe,CAAC,QAAS,aAAa,CACtCO,WA3pEuDyD,GA4pEvD2R,UAAWzT,EACXjC,cAAe,CAAC,QAAS,kBAAkB,CAC3CgZ,MAAOlR,EACX,GACAvH,EAA2BtH,OAAO,CAAC+U,IACnChQ,IAA0Iib,kBAAkB,CAAC,UAAWjL,IAoBxK,GAAM,CAAEkL,QAAAA,EAAO,CAAE,CAAIjkB,IAGf,CAAEmC,UAAW+hB,EAA2B,CAAEtV,SAAUuV,EAA0B,CAAE5hB,KAAM6hB,EAAsB,CAAEhR,eAAgBiR,EAAgC,CAAE,CAAIrkB,KAK1K,AAAC,SAAU3B,CAAiB,EA6DxBA,EAAkBimB,SAAS,CA1C3B,WACI,IAAMlkB,EAAU,IAAI,CAACA,OAAO,CAAEsC,EAAQ,IAAI,CAACA,KAAK,CAAE6hB,EAAc,EAAKnkB,CAAAA,EAAQokB,YAAY,EAAI,CAAA,EAAIC,EAAY/hB,EAAM+hB,SAAS,CAAG,EAAIF,EAAaG,EAAahiB,EAAMgiB,UAAU,CAAG,EAAIH,EAAaI,EAAevkB,EAAQwkB,MAAM,CAAEC,EAAenY,KAAK8L,GAAG,CAACiM,EAAWC,GAAaI,EAAY1kB,EAAQ0kB,SAAS,CACzSC,EAAmBC,EAAO5kB,EAAQ4kB,IAAI,CAAEC,EAAY7kB,EAAQ6kB,SAAS,EAAI,EAAGzc,EAAG+B,CAC/E,AAAgB,CAAA,UAAhB,OAAOya,GACPA,CAAAA,EAAO3P,WAAW2P,EAAI,EAEtB,AAAqB,UAArB,OAAOC,GACPA,CAAAA,EAAY5P,WAAW4P,EAAS,EAEpC,IAAMC,EAAY,CACdd,GAAuBO,GAAc,CAAC,EAAE,CAAE,OAC1CP,GAAuBO,GAAc,CAAC,EAAE,CAAE,OAE1CP,GAAuBY,GAAQA,EAAO,EAAI,KAAK,EAAI5kB,EAAQ4kB,IAAI,CAAE,QACjEZ,GAAuBa,GAAaA,EAAY,EAAI,KAAK,EAAI7kB,EAAQ6kB,SAAS,EAAI,EAAG,MACxF,CAMD,IAHIviB,CAAAA,EAAMyiB,OAAO,EAAM,IAAI,YAAa3Z,KACpC0Z,CAAAA,CAAS,CAAC,EAAE,CAAG,CAAA,EAEd1c,EAAI,EAAGA,EAAI,EAAG,EAAEA,EACjB+B,EAAQ2a,CAAS,CAAC1c,EAAE,CACpBuc,EAAoBvc,EAAI,GAAMA,AAAM,IAANA,GAAW,KAAK2M,IAAI,CAAC5K,GAKnD2a,CAAS,CAAC1c,EAAE,CAAG6b,GAAiC9Z,EAAO,CAACka,EAAWC,EAAYG,EAAcK,CAAS,CAAC,EAAE,CAAC,CAAC1c,EAAE,EAAKuc,CAAAA,EAAoBR,EAAc,CAAA,EAYxJ,OATIW,CAAS,CAAC,EAAE,CAAGA,CAAS,CAAC,EAAE,EAC3BA,CAAAA,CAAS,CAAC,EAAE,CAAGA,CAAS,CAAC,EAAE,AAAD,EAG1Bf,GAA2BW,IAC3BA,AAAY,EAAZA,EAAgBI,CAAS,CAAC,EAAE,EAAIJ,EAAY,GAC5CI,CAAAA,CAAS,CAAC,EAAE,CAAGA,CAAS,CAAC,EAAE,CAAGJ,AAAY,EAAZA,CAAY,EAE9CZ,GAA4B,IAAI,CAAE,iBAAkB,CAAEgB,UAAAA,CAAU,GACzDA,CACX,EA+BA7mB,EAAkB+mB,qBAAqB,CAbvC,SAA+BC,CAAK,CAAEhM,CAAG,EACrC,IAAMiM,EAAanB,GAA2BkB,GAASA,EAAQ,EAC/DE,EAAY,AAACpB,GAA2B9K,IACpCA,EAAMiM,GAEN,AAACjM,EAAMiM,EAAc,IACrBjM,EACAiM,EAAa,IACjB,MAAO,CACHD,MAAOpB,GAAWqB,CAAAA,EAFc,GAEQ,EACxCjM,IAAK4K,GAAWsB,CAAAA,EAHgB,GAGI,CACxC,CACJ,CAEJ,EAAGlnB,GAAsBA,CAAAA,EAAoB,CAAC,CAAA,GAMjB,IAAMmnB,GAA4BnnB,EAkCzD,CAAEmL,OAAQ,CAAE9J,UAAW,CAAE2L,WAAYoa,EAAK,CAAE,CAAE,CAAErc,YAAa,CAAEsc,QAAS,CAAEhmB,UAAW,CAAE2L,WAAYsa,EAA0B,CAAE,CAAE,CAAE,CAAE,CAAI5c,IAEzI,CAAEiO,aAAc4O,EAA0B,CAAE1jB,OAAQ2jB,EAAoB,CAAEC,KAAAA,EAAI,CAAE,CAAI9lB,GAM1F,OAAM+lB,WAAsBJ,GAMxBK,iBAAiBpe,CAAK,CAAE,CACpB,IAAMhC,EAAW,IAAI,CAAC4D,MAAM,CAAC9G,KAAK,CAACkD,QAAQ,CAAEuI,EAAY,IAAI,CAAC8X,aAAa,CAAEnH,EAAI3Q,EAAU2Q,CAAC,CAAGgH,GAAKle,EAAMxH,OAAO,EAAE+R,UAAY,GAC3HkT,EAAQlX,EAAUkX,KAAK,CAAEhM,EAAMlL,EAAUkL,GAAG,CAC1C6M,EAAQb,EAAQ,AAAChM,CAAAA,EAAMgM,CAAI,EAAK,EAClCc,EAAYD,EAAQ,GACpBA,EAAQ,CAACxZ,KAAK0Z,EAAE,EAChBF,EAAQxZ,KAAK0Z,EAAE,CAAEC,EAqCrB,OAnCIhB,IAAU,CAAC3Y,KAAK0Z,EAAE,CAAG,GACrBR,GAA2BvM,KAASuM,GAA2BlZ,AAAU,IAAVA,KAAK0Z,EAAE,IACtEf,EAAQ,CAAC3Y,KAAK0Z,EAAE,CAAG1Z,KAAK0Z,EAAE,CAAG,IAC7B/M,EAAM,CAAC3M,KAAK0Z,EAAE,CAAG,IACjBD,EAAY,CAAA,GAGZ9M,EAAMgM,EAAQ3Y,KAAK0Z,EAAE,GACrBD,EAAY,CAAA,EACZE,EAAe,CAAA,EAGVhN,EAAMgM,EAAS,EAAI3Y,KAAK0Z,EAAE,CAAG,MAC9Bf,GAAS,IACThM,GAAO,MAGX,IAAI,CAACiN,aAAa,EAClB,CAAA,IAAI,CAACA,aAAa,CAAG,IAAI,CAACA,aAAa,CAACnjB,OAAO,EAAC,EAGpD,IAAI,CAACmjB,aAAa,CAAG1gB,EAChB2gB,GAAG,CAAC,CACLC,KAAM,CAAA,EACNC,QAASJ,GAAAA,CACb,GACKtgB,IAAI,CAAC,CACNsf,MAAQc,EAAYd,EAAQhM,EAC5BA,IAAM8M,EAAY9M,EAAMgM,EACxBqB,UAAW,CAACP,EACZ7kB,EAAG6M,EAAU7M,CAAC,CACdC,EAAG4M,EAAU5M,CAAC,CACdud,EAAG,AAACA,CAAAA,EAAI3Q,EAAUwY,MAAM,AAAD,EAAK,CAChC,GACK3gB,GAAG,CAACJ,EAASghB,IAAI,EACf,IAAI,CAACN,aAAa,AAC7B,CACAhc,SAAU,CACN,MAAO,CAAA,CACX,CACJ,CACAub,GAAqBE,GAAcrmB,SAAS,CAAE,CAC1CoO,aAAc2X,GAAM/lB,SAAS,CAACoO,YAAY,CAC1C+Y,SAAUpB,GAAM/lB,SAAS,CAACmnB,QAAQ,CAClCle,SAAU8c,GAAM/lB,SAAS,CAACiJ,QAAQ,AACtC,GAwBA,GAAM,CAAES,YAAa,CAAEsc,QAASoB,EAA+B,CAAE,CAAE,CAAI/d,IAEjE,CAAE6F,SAAUmY,EAA0B,CAAE9T,SAAU+T,EAA0B,CAAE3kB,MAAO4kB,EAAuB,CAAE,CAAIjnB,IAqFxH,SAASknB,GAAMxS,CAAI,CAAEJ,CAAE,EACnB,IAAMM,EAAS,EAAE,CACjB,GAAImS,GAA2BrS,IAASqS,GAA2BzS,IAAOI,GAAQJ,EAC9E,IAAK,IAAI9L,EAAIkM,EAAMlM,GAAK8L,EAAI9L,IACxBoM,EAAO9H,IAAI,CAACtE,GAGpB,OAAOoM,CACX,CAMA,IAAMuS,GAAoB,CACtBC,oBAjFJ,SAA6B3hB,CAAY,CAAE+H,CAAM,EAC7C,IAAM5B,EAAIob,GAA2BxZ,GAAUA,EAAS,CAAC,EACrDoH,EAAQyS,EAAc,EAAGC,EAAY3S,EAAQ4S,EAAmBC,EACpE,GAAIR,GAA2BvhB,GAAe,CAY1C,IAAK,IAAMpC,KAXXuR,EAASqS,GAAwB,CAAC,EAAGxhB,GAGrCkP,EAASuS,GAFFH,GAA2Bnb,EAAE8I,IAAI,EAAI9I,EAAE8I,IAAI,CAAG,EAChDqS,GAA2Bnb,EAAE0I,EAAE,EAAI1I,EAAE0I,EAAE,CAAG,GAE/CiT,EAAoBnoB,OAAOkP,IAAI,CAACsG,GAAQ2G,MAAM,CAAC,AAACrc,GAASyV,AAAyB,KAAzBA,EAAO+F,OAAO,CAAC,CAACxb,IACzEooB,EAAaE,EAAgBT,GAA2Bnb,EAAE0b,UAAU,EAChE1b,EAAE0b,UAAU,CAAG,EAKC3S,GAAQ,CACxB,IAAMvU,EAAUwU,CAAM,CAACvR,EAAM,CAAEokB,EAAOrnB,EAAQsnB,SAAS,CAACD,IAAI,CAAEld,EAAQnK,EAAQsnB,SAAS,CAACnd,KAAK,AACzFkd,AAAS,CAAA,WAATA,EACAJ,GAAe9c,EAEVkd,AAAS,eAATA,GACLrnB,EAAQsnB,SAAS,CAAG,CAChBD,KAAM,SACNld,MAAO,AAACA,EAAQ,IAAO+c,CAC3B,EACAE,GAAiBpnB,EAAQsnB,SAAS,CAACnd,KAAK,EAEnCkd,AAAS,WAATA,GACLD,CAAAA,GAAiBjd,CAAI,CAE7B,CAEA,IAAK,IAAMlH,KAASsR,EAAQ,CACxB,IAAMvU,EAAUwU,CAAM,CAACvR,EAAM,CAC7B,GAAIjD,AAA2B,WAA3BA,EAAQsnB,SAAS,CAACD,IAAI,CAAe,CACrC,IAAME,EAASvnB,EAAQsnB,SAAS,CAACnd,KAAK,AACtCqK,CAAAA,CAAM,CAACvR,EAAM,CAACqkB,SAAS,CAAG,CACtBD,KAAM,SACNld,MAAO,AAACod,EAASN,EAAeG,CACpC,CACJ,CACJ,CAGA,IAAK,IAAMnkB,KAASkkB,EAChB3S,CAAM,CAACvR,EAAM,CAACqkB,SAAS,CAAG,CACtBnd,MAAO,EACPkd,KAAM,QACV,CAER,CACA,OAAO7S,CACX,EA8BIgT,kBA1BJ,SAA2B,CAAEvkB,MAAAA,CAAK,CAAE9C,OAAAA,CAAM,CAAE,EAIxC,MAAO,CAAEmU,KAFIrR,EAAQ,EAAIA,EAAQ,EAElBiR,GADJjR,EAAQ9C,CACD,CACtB,EAsBI2mB,MAAAA,GACA1U,UAAWsU,GAAgCpnB,SAAS,CAACqkB,KAAK,CAACvR,SAAS,AACxE,EAiZM,CAAEyR,QAAS4D,EAAgB,CAAE,CAAI7nB,IACjC,CAAEgC,SAAU8lB,EAAiB,CAAEzlB,MAAO0lB,EAAc,CAAEC,UAAAA,EAAS,CAAE/lB,QAASgmB,EAAgB,CAAE/lB,OAAQgmB,EAAe,CAAE,CAAIloB,IAyB/H,SAASmoB,GAAYC,CAAI,CAAEC,CAAe,EAEtCA,EAAkBN,GAAe,CAAA,EAAM,CACnC1X,QAAS,CAAA,EACTiY,WAAY,CACRC,GAAI,GACJC,YAAa,MACbC,WAAY,QAChB,CACJ,EAAGJ,GACH,IAAMne,EAAM,IAAI,CAACtE,QAAQ,CAACsE,GAAG,CAAEwe,EAAc,IAAI,CAACjnB,IAAI,EAAI,IAAI,CAAEknB,EAAWD,EAAYC,QAAQ,CAAE,CAAEL,WAAAA,CAAU,CAAEjY,QAAAA,CAAO,CAAE,CAAGgY,EAM3H,GALAD,EAAOA,GAASO,GAAYA,EAASP,IAAI,CAErCO,GACAA,EAASC,IAAI,GAEbR,GAAQ/X,EAAS,CACjB,IAAMuY,EAAOd,GAAkBY,EAAa,kBAAmB,AAACvhB,IAC5D,GAAIihB,GAAQ/X,EAAS,CAEjB,IAAIwY,EAAaT,EAAKriB,IAAI,CAAC,KACvB,CAAC8iB,GACDT,EAAKriB,IAAI,CAAC,KAAM8iB,EAAab,MAGjC,IAAMc,EAAc,CAGhBxnB,EAAG,EACHC,EAAG,CACP,EACI0mB,GAAiBK,EAAWS,EAAE,IAC9BD,EAAYC,EAAE,CAAGT,EAAWS,EAAE,CAC9B,OAAOT,EAAWS,EAAE,EAEpBd,GAAiBK,EAAWC,EAAE,IAC9BO,EAAYP,EAAE,CAAGD,EAAWC,EAAE,CAC9B,OAAOD,EAAWC,EAAE,EAExBG,EAAY3iB,IAAI,CAAC+iB,GAEjB,IAAI,CAAC/iB,IAAI,CAAC,CAAEijB,UAAW,EAAG,GACtB,IAAI,CAACC,GAAG,EACR,CAAA,IAAI,CAACA,GAAG,CAAG,IAAI,CAACA,GAAG,CAAC9lB,OAAO,EAAC,EAGhC,IAAMiK,EAAWjG,EAAE+hB,KAAK,CAACC,KAAK,CAAC,EAC/BhiB,CAAAA,EAAE+hB,KAAK,CAAClkB,MAAM,CAAG,EACjBmC,EAAE+hB,KAAK,CAAC,EAAE,CAAG,CACTE,QAAS,WACTd,WAAYJ,GAAgBI,EAAY,CACpC,cAAeA,EAAWG,UAAU,CACpCxe,KAAM,CAAC,EAAEC,EAAI,CAAC,EAAE2e,EAAW,CAAC,AAChC,GACAzb,SAAAA,CACJ,CACJ,CACJ,EAEAsb,CAAAA,EAAYC,QAAQ,CAAG,CAAEP,KAAAA,EAAMQ,KAAAA,CAAK,CACxC,MAEIF,EAAY3iB,IAAI,CAAC,CAAEgjB,GAAI,EAAGR,GAAI,CAAE,GAChC,OAAOG,EAAYC,QAAQ,CAO/B,OALI,IAAI,CAACU,KAAK,GAEVX,EAAYY,SAAS,CAAG,GACxB,IAAI,CAAC1jB,QAAQ,CAAC2jB,SAAS,CAACb,IAErB,IAAI,AACf,CAWA,SAASc,GAAW3lB,CAAK,EACrB,IAAMiD,EAAOjD,EAAMiD,IAAI,CAAE2iB,EAAK,IAAI,CAACxhB,OAAO,EAAEyhB,cAAc,YAC1D,GAAID,EAAI,CACJ,IAAME,EAAU,EAAE,CAAE,CAAE9G,EAAAA,CAAC,CAAEnX,EAAAA,CAAC,CAAE,CAAG,IAAI,CAAC9F,QAAQ,CAACgkB,WAAW,CAAC,IAAI,CAAC3hB,OAAO,EAAG4hB,EAAYne,EAAImX,EAAGiH,EAAmB,AAAIC,OAAO,gEAEtG,KAAMC,EAAQP,EAC5BQ,SAAS,CACTC,OAAO,CAACJ,EAAkB,IAC1BK,KAAK,CAAC,sCAAuCC,EAAaJ,EAAMhlB,MAAM,CAIrEqlB,EAAqB,CAACC,EAAWC,KACnC,GAAM,CAAEjpB,EAAAA,CAAC,CAAEC,EAAAA,CAAC,CAAE,CAAGgpB,EAAgBC,EAAW,AAACf,CAAAA,EAAGgB,iBAAiB,CAACH,GAAa,EAAC,EAAKzC,GAAkB6C,EAAShe,KAAKie,GAAG,CAACH,GAAWI,EAASle,KAAKme,GAAG,CAACL,GACtJ,MAAO,CACH,CACIlpB,EAAIuoB,EAAYa,EAChBnpB,EAAIsoB,EAAYe,EACnB,CACD,CACItpB,EAAIuhB,EAAI6H,EACRnpB,EAAIshB,EAAI+H,EACX,CACJ,AACL,EACA,IAAK,IAAIpiB,EAAI,EAAGsiB,EAAY,EAAGA,EAAYV,EAAYU,IAAa,CAChE,IAA+BC,EAAUC,AAA5BhB,CAAK,CAACc,EAAU,CAAiB9lB,MAAM,CACpD,IAAK,IAAIimB,EAAgB,EAAGA,EAAgBF,EAASE,GAAiB,EAClE,GAAI,CACA,IAAMC,EAAgB1iB,EAClByiB,EACAH,EAAY,CAACK,EAAOC,EAAM,CAAGf,EAAmBa,EAAczB,EAAG4B,sBAAsB,CAACH,GACxFD,AAAkB,CAAA,IAAlBA,GACAtB,EAAQ7c,IAAI,CAACse,GACbzB,EAAQ7c,IAAI,CAACqe,KAGTL,AAAc,IAAdA,GACAnB,EAAQ2B,OAAO,CAACF,GAEhBN,IAAcV,EAAa,GAC3BT,EAAQ7c,IAAI,CAACqe,GAGzB,CACA,MAAOhkB,EAAG,CAGN,KACJ,CAEJqB,GAAKuiB,EAAU,EACf,GAAI,CACA,IAAMG,EAAe1iB,EAAIsiB,EAAWS,EAAU9B,EAAG+B,oBAAoB,CAACN,GAAe,CAACC,EAAOC,EAAM,CAAGf,EAAmBa,EAAcK,GACvI5B,EAAQ2B,OAAO,CAACF,GAChBzB,EAAQ2B,OAAO,CAACH,EACpB,CACA,MAAOhkB,EAAG,CAGN,KACJ,CACJ,CAEIwiB,EAAQ3kB,MAAM,EACd2kB,EAAQ7c,IAAI,CAAC6c,CAAO,CAAC,EAAE,CAACR,KAAK,IAEjCriB,EAAK6iB,OAAO,CAAGA,CACnB,CACA,OAAO7iB,CACX,CAWA,SAAS2kB,GAAa5nB,CAAK,EACvB,IAAMqW,EAAerW,EAAMqW,YAAY,CAAEzQ,EAAQ5F,EAAM4F,KAAK,CAAE4e,EAAmBnO,CAAY,CAACzQ,EAAMuR,YAAY,CAAG,WAAW,EAC1Hd,EAAayO,QAAQ,CACrBN,GAAmB,CAACnO,EAAatY,OAAO,GACxC,IAAI,CAACumB,WAAW,CAAC1e,EAAMuc,gBAAgB,GAAG,IAAI,GAAKvc,EAAME,OAAO,CAAE0e,GAC9D5e,EAAM6c,aAAa,EACnB,CAAC+B,EAAgBhY,OAAO,EAExB5G,CAAAA,EAAM6c,aAAa,CAAI7c,EAAM6c,aAAa,CAACnjB,OAAO,EAAE,EAGhE,CA8BA,GAAM,CAAEmhB,UAAAA,EAAS,CAAEc,sBAAAA,EAAqB,CAAE,CAAGI,GAEvC,CAAEjP,KAAMmV,EAAmB,CAAE,CAAI1rB,IAEjC,CAAEkJ,OAAQyiB,EAA2B,CAAEjG,QAASkG,EAA4B,CAAE,CAAG,AAAC7iB,IAA2IK,WAAW,CAIxO,CAAEkK,SAAUuY,EAAuB,CAAEtX,gBAAiBuX,EAA8B,CAAEtW,cAAeuW,EAA4B,CAAE3V,aAAc4V,EAA2B,CAAE,CAAG3Y,GAIjL,CAAEpR,QAASgqB,EAAsB,CAAE9U,MAAO+U,EAAoB,CAAEhqB,OAAQiqB,EAAqB,CAAEhqB,UAAWiqB,EAAwB,CAAExd,SAAUyd,EAAuB,CAAEpZ,SAAUqZ,EAAuB,CAAElqB,SAAUmqB,EAAuB,CAAElqB,MAAOmqB,EAAoB,CAAE3U,MAAO4U,EAAoB,CAAE,CAAIzsB,IAGjT0sB,AApCiB,CAAA,CACb1oB,QATJ,SAAiB2oB,CAAe,EAC5B7E,GAAkB6E,EAAiB,eAAgBnD,IACnD1B,GAAkB6E,EAAiB,wBAAyBlB,IAC5D,IAAMmB,EAAkBD,EAAgBjtB,SAAS,AAC7C,AAACktB,CAAAA,EAAgBzE,WAAW,EAC5ByE,CAAAA,EAAgBzE,WAAW,CAAGA,EAAU,CAEhD,CAGA,CAAA,EAkCoBnkB,OAAO,CAAEiF,KAM7B,IAAM4jB,GAAU,IAAMngB,KAAK0Z,EAAE,CAgCvB0G,GAAc,SAAqBxrB,CAAC,CAAEC,CAAC,CAAE2kB,CAAK,CAAE/T,CAAQ,EAC1D,MAAO,CACH7Q,EAAGA,EAAKoL,KAAKie,GAAG,CAACzE,GAAS/T,EAC1B5Q,EAAGA,EAAKmL,KAAKme,GAAG,CAAC3E,GAAS/T,CAC9B,CACJ,EA6NA,SAAS4a,GAAsB7d,CAAI,CAAE9O,CAAO,EACxC,IAAMyV,EAAczV,EAAQyV,WAAW,CAAExI,EAAS6B,EAAK7B,MAAM,CAAE+R,EAAa/R,EAASwI,CAAW,CAACxI,EAAO,CAAG,KAAK,EAAG7D,EAASpJ,EAAQoJ,MAAM,CAAE9G,EAAQ8G,EAAO9G,KAAK,CAA0B+G,EAAQsK,AAAvBvK,EAAOuK,MAAM,AAAgB,CAAC7E,EAAK1G,CAAC,CAAC,CAAmEiZ,EAAYoK,GAAwB3c,EAAM,CACzT2E,OADuNrK,EAAOpJ,OAAO,CAACyT,MAAM,EAAInR,GAASA,EAAMtC,OAAO,CAACyT,MAAM,CAE7QL,WAAYhK,EAAOgK,UAAU,CAC7BC,MAAOrT,EAAQqT,KAAK,CACpBC,kBAAmBtT,EAAQsT,iBAAiB,CAC5CC,YAAayL,GAAcA,EAAWve,KAAK,CAC3C+S,iBAAkBwL,GAAcA,EAAW5L,UAAU,CACrDhK,OAAQpJ,EAAQoJ,MAAM,CACtBsK,SAAU1T,EAAQ0T,QAAQ,AAC9B,GASA,OARA5E,EAAKrO,KAAK,CAAG4gB,EAAU5gB,KAAK,CAC5BqO,EAAKsE,UAAU,CAAGiO,EAAUjO,UAAU,CAClC/J,IACAA,EAAM5I,KAAK,CAAGqO,EAAKrO,KAAK,CACxB4I,EAAM+J,UAAU,CAAGtE,EAAKsE,UAAU,CAElCtE,EAAK8d,MAAM,CAAG,AAAC9d,EAAKlF,EAAE,GAAK5J,EAAQuV,MAAM,EAAIlM,EAAMujB,MAAM,EAEtD9d,CACX,CAMA,MAAM+d,WAAuBrB,GAMzB5R,eAAevQ,CAAK,CAAEwQ,CAAS,CAAEC,CAAY,CAAE,CAC3C,GAAIA,CAAAA,EAAayO,QAAQ,GAAIzO,EAAayO,QAAQ,CAACtY,OAAO,CAM1D,OADA4J,EAAUiT,MAAM,CAAG,CAAA,EACZ,KAAK,CAAClT,eAAetK,KAAK,CAAC,IAAI,CAAEX,UAC5C,CAKAX,QAAQjB,CAAI,CAAE,CACV,IAIIU,EAJEnL,EAAQ,IAAI,CAACA,KAAK,CAAEkiB,EAAS,CAC/BliB,EAAM+hB,SAAS,CAAG,EAClB/hB,EAAMgiB,UAAU,CAAG,EACtB,CAAEyI,EAAWzqB,EAAMyqB,QAAQ,CAAE1pB,EAAUf,EAAMe,OAAO,CAAEkC,EAAQ,IAAI,CAACA,KAAK,CAGrEwH,GAEAU,EAAU,CACNuf,WAAYxI,CAAM,CAAC,EAAE,CAAGuI,EACxBE,WAAYzI,CAAM,CAAC,EAAE,CAAGnhB,EACxB6pB,OAAQ,KACRC,OAAQ,KACR/C,SAAU,GACVlZ,QAAS,GACb,EACA3L,EAAMI,IAAI,CAAC8H,KAIXA,EAAU,CACNuf,WAAYD,EACZE,WAAY5pB,EACZ6pB,OAAQ,EACRC,OAAQ,EACR/C,SAAU,EACVlZ,QAAS,CACb,EACA3L,EAAMyI,OAAO,CAACP,EAAS,IAAI,CAACzN,OAAO,CAACuN,SAAS,EAErD,CACA4Q,YAAa,CACT,IAAM/U,EAAS,IAAI,CAAEkK,EAAoBlK,EAAOkK,iBAAiB,CAAE8Z,EAAYhkB,EAAOgkB,SAAS,CAAE7nB,EAAQ6D,EAAO7D,KAAK,CAAEiI,EAAcpE,EAAOoE,WAAW,CAAE+H,EAASnM,EAAO4F,QAAQ,CAAEuT,EAAiBnZ,EAAOmZ,cAAc,CAAExT,EAAU3F,EAAO2F,OAAO,CAAEse,EAAmBte,CAAO,CAACwT,EAAe,CAAE+K,EAAoBD,GAAoBA,EAAiBtf,SAAS,CAAE4F,EAASvK,EAAOuK,MAAM,CAAE4Z,EAAUnkB,EAAOokB,kBAAkB,CAAElrB,EAAQ8G,EAAO9G,KAAK,CAAEmrB,EAAenrB,GAASA,EAAMtC,OAAO,EAAIsC,EAAMtC,OAAO,CAACsC,KAAK,EAAI,CAAC,EAAGiL,EAAamgB,AAxUzf,WAAb,OAwUghBD,EAAalgB,SAAS,EACriBkgB,EAAalgB,SAAS,CACfuX,EAAY1b,EAAOob,MAAM,CAAEA,EAAS,CAC3CtjB,EAAG4jB,CAAS,CAAC,EAAE,CACf3jB,EAAG2jB,CAAS,CAAC,EAAE,AACnB,EAAGyB,EAASzB,CAAS,CAAC,EAAE,CAAG,EAAGtf,EAAW4D,EAAO9G,KAAK,CAACkD,QAAQ,CAAEmoB,EAAyB,CAAC,CAAEpgB,CAAAA,GACxFC,GACA+H,IAAWgN,GACXnZ,EAAOwkB,eAAe,AAAD,EACrBC,EAAeC,EAAsB,CAAA,EAAOC,EAAY,CAAA,EAc5D,IAAK,IAAM1kB,KAbPskB,IACAvkB,EAAOwkB,eAAe,CAACjoB,IAAI,CAAC,CAAEuL,QAAS,CAAE,GACzC2c,EAAgB,WAEZC,EAAsB,CAAA,EAClBE,AAFM5kB,EAEJwkB,eAAe,EACjBI,AAHM5kB,EAGJwkB,eAAe,CAAC5f,OAAO,CAAC,CACtBkD,QAAS,EACTnH,WAAY,SAChB,EAER,GAEgB4J,GAAQ,CACxB,IACIsa,EAAe3gB,EADbwB,EAAOzF,EAAMyF,IAAI,CAAE7L,EAAQqQ,CAAiB,CAACxE,EAAK7L,KAAK,CAAC,CAAE4iB,EAAiBxc,EAAMwc,aAAa,EAAI,CAAC,EAAIqI,EAAQpf,EAAKf,SAAS,EAAI,CAAC,EAAGjB,EAAU,CAAC,CAAEgC,CAAAA,EAAKhC,OAAO,EAAIgC,EAAKf,SAAS,AAAD,CAOrLmgB,CAAAA,EAAMte,YAAY,CAAGxG,EAAOpJ,OAAO,CAAC4P,YAAY,CAE5Cqe,EADAzgB,GAAeD,EACC4gB,AA9LhC,SAAsBD,CAAK,CAAE9gB,CAAM,EAC/B,IAAM/D,EAAQ+D,EAAO/D,KAAK,CAAEkkB,EAAUngB,EAAOmgB,OAAO,CAAEhH,EAASnZ,EAAOmZ,MAAM,CAAEhR,EAASnI,EAAOmI,MAAM,CAAEgN,EAAiBnV,EAAOmV,cAAc,CAAEsD,EAAgBzY,EAAOyY,aAAa,CAAEuH,EAAYhgB,EAAOggB,SAAS,CAAEE,EAAoBlgB,EAAOkgB,iBAAiB,CAAExgB,EAAUM,EAAON,OAAO,CACpRwH,EAAO,CAAC,EAAGJ,EAAK,CAChB+E,IAAKiV,EAAMjV,GAAG,CACdgM,MAAOiJ,EAAMjJ,KAAK,CAClBsB,OAAQ2H,EAAM3H,MAAM,CACpB7H,EAAGwP,EAAMxP,CAAC,CACVxd,EAAGgtB,EAAMhtB,CAAC,CACVC,EAAG+sB,EAAM/sB,CAAC,AACd,EAgDA,OA/CI2L,EAEI,CAACzD,EAAME,OAAO,EAAI+jB,GAiBlBhZ,CAAAA,CAfIA,EADAiB,IAAWlM,EAAMO,EAAE,CACZ,CACHqb,MAAOsI,EAAQtI,KAAK,CACpBhM,IAAKsU,EAAQtU,GAAG,AACpB,EAGO,AAACqU,EAAkBrU,GAAG,EAAIiV,EAAMjJ,KAAK,CAAI,CAC5CA,MAAOsI,EAAQtU,GAAG,CAClBA,IAAKsU,EAAQtU,GAAG,AACpB,EAAI,CACAgM,MAAOsI,EAAQtI,KAAK,CACpBhM,IAAKsU,EAAQtI,KAAK,AACtB,GAGCsB,MAAM,CAAGjS,EAAKoK,CAAC,CAAG6H,CAAK,EAK5Bld,EAAME,OAAO,GACTgZ,IAAmBlZ,EAAMO,EAAE,CAC3BsK,EAAK,CACDqS,OAAQA,EACR7H,EAAG6H,CACP,EAEK6G,GACLlZ,CAAAA,EAAK,AAACkZ,EAAUnU,GAAG,EAAI4M,EAAcZ,KAAK,CACtC,CACIsB,OAAQA,EACR7H,EAAG6H,EACHtB,MAAOsI,EAAQtU,GAAG,CAClBA,IAAKsU,EAAQtU,GAAG,AACpB,EAAI,CACJsN,OAAQA,EACR7H,EAAG6H,EACHtB,MAAOsI,EAAQtI,KAAK,CACpBhM,IAAKsU,EAAQtI,KAAK,AACtB,CAAA,GAIL,CACH3Q,KAAMA,EACNJ,GAAIA,CACR,CACJ,EAiI6Cga,EAAO,CAChC1J,OAAQA,EACRnb,MAAOA,EACPkkB,QAASA,EACThH,OAAQA,EACRhR,OAAQA,EACRgN,eAAgBA,EAChBsD,cAAeA,EACfuH,UAAWA,EACXE,kBAAmBA,EACnBxgB,QAASA,CACb,GAIgB,CACZoH,GAAIga,EACJ5Z,KAAM,CAAC,CACX,EAEJyX,GAAsB1iB,EAAO,CACzBwc,cAAeqI,EACfE,WAAY,CAACF,EAAM/L,KAAK,CAAE+L,EAAM3e,KAAK,CAAC,CACtCsP,QAASwP,AAtJzB,SAAoBhlB,CAAK,CAAEkM,CAAM,CAAEE,CAAW,MAEtCoJ,EAWJ,OAVK/P,AAFQzF,EAAMyF,IAAI,CAEbI,MAAM,GAIR2P,EAFAtJ,IAAWlM,EAAMO,EAAE,CAET4L,AADCC,CAAW,CAACF,EAAO,CACXtI,MAAM,CAGf5D,EAAMO,EAAE,EAGnBiV,CACX,EAwIoCxV,EAAOkM,EAAQxG,GACnCmB,KAAM,GAAM7G,CAAAA,EAAM6G,IAAI,EAAI7G,EAAMO,EAAE,EAAIP,EAAMgK,KAAK,AAAD,EAChD8O,MAAO+L,EAAM/L,KAAK,CAClB5S,MAAO2e,EAAM3e,KAAK,CAClBpF,MAAO2E,EAAKiH,GAAG,CACf0I,SAAU3R,EACVwhB,OAAQ,CAACxhB,CACb,GACAzD,EAAM6U,SAAS,CAAGqQ,AAzW9B,SAAsBnhB,CAAM,EAExB,IAAM/D,EAAQ+D,EAAO/D,KAAK,CAAE6kB,EAAQhC,GAAwB9e,EAAOW,SAAS,EAAIX,EAAOW,SAAS,CAAG,CAAC,EAAG,CAAEkL,IAAAA,EAAM,CAAC,CAAE9G,OAAAA,EAAS,CAAC,CAAE8S,MAAAA,EAAQ,CAAC,CAAE,CAAGiJ,EAAOxY,EAAgBwW,GAAwB9e,EAAOsI,YAAY,EAC1MtI,EAAOsI,YAAY,CAAC1F,UAAU,CAC9B,CAAC,EAKOhQ,EAAUosB,GAFPC,GAAqBH,GAAwB9e,EAAOnK,KAAK,EACpEmK,EAAOnK,KAAK,CAAC+M,UAAU,CACvB,CAAC,EAAE,CAAC,EAAE,CAA+C0F,GAAelV,EAAQR,EAAQQ,KAAK,CAAGR,EAAQQ,KAAK,EAAI,CAAC,EAAG,CAAEguB,eAAAA,EAAiB,CAAC,CAAEC,eAAAA,EAAiB,CAAC,CAAE,CAAGplB,EAC9JqlB,EAAatE,EAAUuE,EAAe3uB,EAAQ2uB,YAAY,CAAE9rB,EAAQgpB,GAAuBrrB,EAAMqC,KAAK,EACtG+rB,SAASpuB,EAAMqC,KAAK,EAAI,IAAK,IAAM,KAAK,EA8H5C,OA7HKopB,GAAwBjsB,EAAQoqB,QAAQ,IACrCuE,CAAAA,AAAiB,SAAjBA,GAA2BA,AAAiB,aAAjBA,CAA0B,IACjD3uB,EAAQwB,OAAO,EACfmtB,AAAiB,aAAjBA,GAGAA,CAAAA,EAAe,MAAK,EAEpBH,EAAiB,GAAKC,EAAiBtc,GACvCuc,EAAc,EAEVrlB,EAAM6c,aAAa,EAAIyI,AAAiB,aAAjBA,GACvB3uB,CAAAA,EAAQuoB,QAAQ,CAAG,CACftY,QAAS,CAAA,CACb,CAAA,EAIAgJ,EAAMgM,EAAQ3Y,KAAK0Z,EAAE,EACrBnjB,CAAAA,EAAQsP,AAAS,GAATA,CAAW,GAGlBqc,EAAiB,GAAKC,EAAiB,IAAMtc,EAC9Cwc,AAAiB,aAAjBA,EACA3uB,EAAQuoB,QAAQ,CAAG,CACftY,QAAS,CAAA,EACTiY,WAAY,CACRC,GAAI,CACR,CACJ,EAGAwG,EAAe,YAKftlB,EAAMwQ,SAAS,EAAE0O,UACjBoG,AAAiB,aAAjBA,GACA3uB,CAAAA,EAAQuoB,QAAQ,CAAG,CACftY,QAAS,CAAA,CACb,CAAA,EAEJ0e,EAAe,kBAGF,SAAjBA,GAA2BA,AAAiB,aAAjBA,IACvBtlB,EAAMwQ,SAAS,EAAE0O,UACjBvoB,CAAAA,EAAQuoB,QAAQ,CAAG,CACftY,QAAS,CAAA,CACb,CAAA,EAEJye,EAAczV,EAAM,AAACA,CAAAA,EAAMgM,CAAI,EAAK,GAEpC0J,AAAiB,aAAjBA,EACA9rB,EAAQyJ,KAAK8L,GAAG,CAACjG,AAAS,IAATA,EAAc,AAACsc,CAAAA,EAAiBD,CAAa,EAAK,GAG/D,CAAC3C,GAAuBhpB,IAAUsP,GAClCtP,CAAAA,EAAQwG,AAAqB,IAArBA,EAAMyF,IAAI,CAAC7L,KAAK,CAAS,EAAIkP,EAASA,CAAK,EAGtC,kBAAjBwc,IAMIF,EADM,GAEN5rB,EAAQ,EAEHqrB,EAAM/b,MAAM,GACjB3R,EAAMwd,SAAS,CAAG1R,KAAK2R,KAAK,CAACuQ,EALvB,KAK8C,EAQpD3rB,EAAQsP,EAJWqc,CAAAA,EATb,GAUFrc,AAAW7G,CAAAA,AAVT,GAUakjB,CAAa,EACvBC,CAAAA,EAAiBD,CAAa,EADnCrc,EAEA,CAAA,IAKZtP,EAAQyJ,KAAKC,GAAG,CAAC,AAAC1J,CAAAA,GAAS,CAAA,EAAK,EAAK7C,CAAAA,EAAQI,OAAO,EAAI,CAAA,EAAI,GAC5DgqB,EAAW,AAAEsE,CAAAA,GAAe,CAAA,EAAKjC,GAAW,IACxCkC,AAAiB,aAAjBA,GACAvE,CAAAA,GAAY,EAAC,EAGbA,EAAW,GACXA,GAAY,IAEPA,EAAW,KAChBA,CAAAA,GAAY,GAAE,EAElBpqB,EAAQoqB,QAAQ,CAAGA,GAEnBpqB,EAAQuoB,QAAQ,GACZlf,AAA+B,IAA/BA,EAAMwc,aAAa,CAACU,MAAM,EAC1BvmB,EAAQuoB,QAAQ,CAACtY,OAAO,EAExBjQ,EAAQoqB,QAAQ,CAAG,EAEnBpqB,EAAQuoB,QAAQ,CAACtY,OAAO,CAAG,CAAA,EAE3BpN,EAAQyJ,KAAKC,GAAG,CAAC,AAAyB,EAAxBlD,EAAMwc,aAAa,CAACnH,CAAC,CACnC,EAAK1e,CAAAA,EAAQI,OAAO,EAAI,CAAA,EAAI,IAE3BiJ,EAAM6U,SAAS,EAAEqK,UACtB,CAAClf,EAAM6U,SAAS,CAACqK,QAAQ,CAACtY,OAAO,EACjC0e,AAAiB,aAAjBA,GAEA3uB,CAAAA,EAAQuoB,QAAQ,CAACtY,OAAO,CAAG,CAAA,CAAG,EAE9BjQ,EAAQuoB,QAAQ,CAACtY,OAAO,GAExBjQ,EAAQoqB,QAAQ,CAAG,EAEnBvnB,EAAQyJ,KAAKC,GAAG,CAAC,AAACkiB,CAAAA,EAAiBD,CAAa,EAAK,EACjD,EAAKxuB,CAAAA,EAAQI,OAAO,EAAI,CAAA,EAAI,GAChCI,EAAMquB,UAAU,CAAG,WAG3BruB,EAAMqC,KAAK,CAAGA,EAAQ,KACf7C,CACX,EA+N2C,CAC3BqJ,MAAOA,EACPpG,MAAOA,EACPyS,aAAcrM,EAAMrJ,OAAO,CAC3B+N,UAAWmgB,CACf,GACI,CAACH,GAAajhB,IACdihB,EAAY,CAAA,EACZzgB,EAAaugB,GAEjBxkB,EAAM8D,IAAI,CAAC,CACPE,kBAAmB4gB,EAAc/Z,EAAE,CACnCzG,QAASse,GAAsBkC,EAAc3Z,IAAI,CAAG,CAAChS,EAAM+E,UAAU,EAAI+B,EAAO2B,YAAY,CAAC1B,EAAQA,EAAMsV,QAAQ,EAAI,WACvHrR,WAAYA,EACZ/H,MAAOA,EACPC,SAAUA,EACVoI,UAAW,MACXG,UAAWmgB,CACf,EACJ,CAGIP,GAA0BI,GAC1B3kB,EAAOoE,WAAW,CAAG,CAAA,EACrBpE,EAAOpJ,OAAO,CAACgQ,UAAU,CAAC8e,KAAK,CAAG,CAAA,EAClCvD,GAA4BjsB,SAAS,CAACse,cAAc,CAACpe,IAAI,CAAC4J,GAC1DA,EAAOoE,WAAW,CAAG,CAAA,EAGjBsgB,GACAD,KAIJtC,GAA4BjsB,SAAS,CAACse,cAAc,CAACpe,IAAI,CAAC4J,GAE9DA,EAAOmZ,cAAc,CAAGhN,CAC5B,CAKA5E,gBAAgB1D,CAAM,CAAED,CAAQ,CAAEhN,CAAO,CAAE,CACvC,IAAIklB,EAAajY,EAAOgY,KAAK,CACvB6B,EAAQ7Z,EAAOgM,GAAG,CAAGiM,EAAYtZ,EAAQqB,EAAO8I,GAAG,CAAE7U,EAAI+L,EAAO/L,CAAC,CAAEC,EAAI8L,EAAO9L,CAAC,CAAEgR,EAAU,AAACnS,GAC9FksB,GAAwBlsB,EAAQsnB,SAAS,GACzC2E,GAAwBjsB,EAAQsnB,SAAS,CAACnd,KAAK,EAC/CnK,EAAQsnB,SAAS,CAACnd,KAAK,CACvB,EAAI4kB,EAAc9hB,EAAOyR,CAAC,CAAEsQ,EAAcD,EAAc5c,EAAQiS,EAAepkB,GAAWisB,GAAwBjsB,EAAQokB,YAAY,EACtIpkB,EAAQokB,YAAY,CACpB,EACJ,MAAO,AAACpX,CAAAA,GAAY,EAAE,AAAD,EAAGyH,MAAM,CAAC,CAACqL,EAAKjK,KACjC,IAA4C0X,EAAU0B,AAAlC,EAAIrjB,EAASiK,EAAME,GAAG,CAAyB+Q,EAAmDoI,EAAiBxC,GAAYxrB,EAAGC,EAA5D+jB,EAAcqI,EAAU,EAAsDnJ,GAAe1I,EAAS,CAC5Lxa,EAAG2U,EAAM+W,MAAM,CAAGsC,EAAehuB,CAAC,CAAGA,EACrCC,EAAG0U,EAAM+W,MAAM,CAAGsC,EAAe/tB,CAAC,CAAGA,EACrColB,OAAQwI,EACRrQ,EAAGsQ,EACH7c,OAAQA,EACR8S,MAAOC,EACPjM,IAAKiM,EAAaqI,CACtB,EAGA,OAFAzN,EAAIpT,IAAI,CAACgP,GACTwJ,EAAaxJ,EAAOzC,GAAG,CAChB6G,CACX,EAAG,EAAE,CACT,CACAZ,YAAYtV,CAAE,CAAErG,CAAM,CAAE6e,CAAc,CAAE,CAEpC,GACAhZ,AAA6B,IAA7BA,AAFe,IAAI,CAEZ2F,OAAO,CAACnF,EAAG,CAAC3G,KAAK,EACpBmG,AAEgB,IAFhBA,AAHW,IAAI,CAGR0R,QAAQ,CACVK,MAAM,CAAC,AAACrM,GAASA,AAAe,IAAfA,EAAK7L,KAAK,EAC3B2B,MAAM,CAAQ,CACnB,GAAIwE,AAA0B,KAA1BA,AANO,IAAI,CAMJmZ,cAAc,CACrB,OAEJ3Y,EAAK,EACT,CACA,KAAK,CAACsV,YAAYtV,EAAIrG,EAAQ6e,EAClC,CAKA+M,aAAaliB,CAAM,CAAEmiB,CAAY,CAAE9b,CAAiB,CAAE,CAClD,IAAgCtT,EAAUsT,CAAiB,CAA7CrG,EAAOhK,KAAK,CAAG,EAAqC,CAElE+J,EAAWC,EAAOD,QAAQ,CAACmO,MAAM,CAAC,SAAU3c,CAAC,EACzC,OAAOA,EAAEsO,OAAO,AACpB,GACIwO,EAAiB,EAAE,CACvBA,EAAiB,IAAI,CAAC3K,eAAe,CAACye,EAAcpiB,EAAUhN,GAC9D,IAAIoI,EAAI,GACR,IAAK,IAAMyN,KAAS7I,EAAU,CAC1B,IAAM0O,EAASJ,CAAc,CAAC,EAAElT,EAAE,CAAE0d,EAAQpK,EAAOuJ,KAAK,CAAI,AAACvJ,CAAAA,EAAOzC,GAAG,CAAGyC,EAAOuJ,KAAK,AAAD,EAAK,EAAI9S,EAASuJ,EAAO6K,MAAM,CAAI,AAAC7K,CAAAA,EAAOgD,CAAC,CAAGhD,EAAO6K,MAAM,AAAD,EAAK,EAAIgH,EAAW7R,EAAOzC,GAAG,CAAGyC,EAAOuJ,KAAK,CAAwDT,EAAU6K,AAAnD3T,AAAkB,IAAlBA,EAAO6K,MAAM,EAAUgH,EAL3N,KAMJ,CAAErsB,EAAGwa,EAAOxa,CAAC,CAAEC,EAAGua,EAAOva,CAAC,AAAC,EAC3BurB,GAAYhR,EAAOxa,CAAC,CAAEwa,EAAOva,CAAC,CAAE2kB,EAAO3T,GAAU4D,EAAOF,EAAME,GAAG,CAChEF,EAAMhJ,aAAa,CAAGgJ,EAAME,GAAG,CAC5BF,EAAMhJ,aAAa,CACnBgJ,EAAME,GAAG,CACbF,EAAMhJ,aAAa,AAEnB,CAAA,IAAI,CAAC8G,MAAM,CAACkC,EAAMzN,CAAC,CAAC,GACpB,IAAI,CAACuL,MAAM,CAACkC,EAAMzN,CAAC,CAAC,CAAComB,cAAc,CAAGjB,EAAU7R,EAAO6K,MAAM,CAC7D,IAAI,CAAC5S,MAAM,CAACkC,EAAMzN,CAAC,CAAC,CAACqmB,cAAc,CAAGlB,EAAU7R,EAAOgD,CAAC,EAE5D7I,EAAM9H,SAAS,CAAGqe,GAAqB1Q,EAAQ,CAC3CyG,MAAOqC,EAAOtjB,CAAC,CACfqO,MAAOiV,EAAOrjB,CAAC,AACnB,GACA0U,EAAM6F,MAAM,CAAG0Q,GAAqB1Q,EAAQ,CACxC3F,IAAKA,CACT,GAEIF,EAAM7I,QAAQ,CAACpI,MAAM,EACrB,IAAI,CAACuqB,YAAY,CAACtZ,EAAOA,EAAM6F,MAAM,CAAEpI,EAE/C,CACJ,CACAnL,WAAY,CACR,IAAqBnI,EAAUoJ,AAAhB,IAAI,CAAmBpJ,OAAO,CAAE8kB,EAAY1b,AAA5C,IAAI,CAA+Cob,MAAM,CAAGpb,AAA5D,IAAI,CAA+D8a,SAAS,GAAIqJ,EAAUnkB,AAA1F,IAAI,CAA6FokB,kBAAkB,CAAGxI,GAAsBhlB,EAAQklB,UAAU,CAAEllB,EAAQmlB,QAAQ,EAAG4J,EAAcjK,CAAS,CAAC,EAAE,CAAG,EAAGkK,EAAclK,CAAS,CAAC,EAAE,CAAG,EAE/P7O,EAAS2V,GAFM,IAAI,EAGfnW,EAAcrM,AAHH,IAAI,CAGM2F,OAAO,CAAEuE,EAAmBkC,EAAWC,GAAeA,CAAW,CAACQ,EAAO,CAAEqZ,EAAU,CAAC,CAC/GlmB,CAJe,IAAI,CAIZgkB,SAAS,CAAG5X,GAAYA,EAASzH,SAAS,CACjD3E,AALe,IAAI,CAKZmmB,cAAc,GACrBvD,GANe,IAAI,CAMc,kBAEjC,IAAM3W,EAAOjM,AARE,IAAI,CAQCiM,IAAI,CAAGjM,AARZ,IAAI,CAQe+W,OAAO,GAInCqP,EAAQrD,GAAwB3W,AADtCA,CAAAA,EAAWC,AADXA,CAAAA,EAAcrM,AAVC,IAAI,CAUE2F,OAAO,AAAD,CACL,CAACkH,EAAO,AAAD,EACkBhJ,MAAM,EAAIuI,EAASvI,MAAM,CAAG,GAAIwiB,EAAUha,CAAW,CAAC+Z,EAAM,CAAE,CAAElb,KAAAA,CAAI,CAAEJ,GAAAA,CAAE,CAAE,CAAGwb,AA/pCnE3I,GA+pC8FS,iBAAiB,CAAChS,GACzKlC,EAAoBoY,GAA+B,CAC/CpX,KAAAA,EACAC,OAAQnL,AAfG,IAAI,CAeApJ,OAAO,CAACuU,MAAM,CAC7BL,GAAAA,EACAE,SAAU,CACNrE,aAAc/P,EAAQ+P,YAAY,CAClCC,WAAYhQ,EAAQgQ,UAAU,CAC9Bc,gBAAiB9Q,EAAQ8Q,eAAe,CACxCwW,UAAWtnB,EAAQsnB,SAAS,CAC5BlD,aAAcpkB,EAAQokB,YAAY,AACtC,CACJ,GAGA9Q,EAAoBoc,AA9qCqC3I,GA8qCVC,mBAAmB,CAAC1T,EAAmB,CAClF4T,WA5B2Q8H,EAAcD,EA6BzRza,KAAAA,EACAJ,GAAAA,CACJ,GAGAyX,GAA6BtW,EAAM,CAC/BC,OAAQqX,GACRpX,OAAQU,EACRnF,gBAAiB9Q,EAAQ8Q,eAAe,CACxCwC,kBAAmBA,EACnBmC,YAAaA,EACb9B,OAAQvK,AAxCG,IAAI,CAwCAuK,MAAM,CACrBvK,OAzCW,IAAI,AA0CnB,GACA,IAAMsS,EAASjG,CAAW,CAAC,GAAG,CAAC1H,SAAS,CAAG,CACvCkL,IAAKsU,EAAQtU,GAAG,CAChByF,EAAGqQ,EACH9J,MAAOsI,EAAQtI,KAAK,CACpBlP,IAAKP,EAASO,GAAG,CACjB7U,EAAG4jB,CAAS,CAAC,EAAE,CACf3jB,EAAG2jB,CAAS,CAAC,EAAE,AACnB,EAKA,IAAK,IAAMzb,KAJX,IAAI,CAAC8lB,YAAY,CAACM,EAAS/T,EAAQpI,GAEnClK,AArDe,IAAI,CAqDZkK,iBAAiB,CAAGA,EAEPlK,AAvDL,IAAI,CAuDQuK,MAAM,EACzB2b,CAAO,CAACjmB,EAAMO,EAAE,CAAC,EACjBkiB,GAAqB,GAAI,CAAA,EAAO1iB,AAzDzB,IAAI,CAyD4B9G,KAAK,EAGhDgtB,CAAO,CAACjmB,EAAMO,EAAE,CAAC,CAAG,CAAA,CAI5B,CACJ,CAMAijB,GAAetoB,cAAc,CAAG6nB,GAAqBZ,GAA6BjnB,cAAc,CAvpCjE,CAgI3BigB,OAAQ,CAAC,MAAO,MAAM,CAMtBmL,KAAM,CAAA,EACN5f,aAAc,CAAA,EAQdmB,QAAS,EAMTlB,WAAY,CACR4f,aAAc,CAAA,EACdd,MAAO,CAAA,EAoBPH,aAAc,WACdnuB,MAAO,CAEH6P,aAAc,UAClB,CACJ,EAQA4F,OAAQ,KAAK,EASbnF,gBAAiB,CAAA,EAWjBwW,UAAW,CAQPnd,MAAO,EAkBPkd,KAAM,QACV,EAsBAjD,aAAc,EAClB,GA05BA2H,GAAsBc,GAAevtB,SAAS,CAAE,CAC5CkL,UAAW,EAAE,CACboT,eAAgB0N,GAChBpH,UAAWA,GACX2L,YAAa,CAAA,EAEbC,iBAAkB,CAAA,EAClB/kB,aAAcwgB,GAA4BjsB,SAAS,CAACyL,YAAY,CAChEE,WAh2CyD0a,GAi2CzDtF,UAjtCJ,cAA2BzT,EAC3B,EAitCI+W,MAruC6DoD,EAsuCjE,GACApe,IAA0Iib,kBAAkB,CAAC,WAAYiJ,IAezK,IAAMkD,GAAKnwB,GACXmwB,CAAAA,GAAEpsB,WAAW,CAAGosB,GAAEpsB,WAAW,EA9xIiCA,EA+xI9DosB,GAAEpsB,WAAW,CAACC,OAAO,CAACmsB,GAAEC,KAAK,CAAED,GAAExrB,cAAc,EAClB,IAAM7E,GAAiBE,IAG1C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}