{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/marker-clusters\n * @requires highcharts\n *\n * Marker clusters module for Highcharts\n *\n * (c) 2010-2025 Wojciech Chmiel\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/marker-clusters\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/marker-clusters\"] = factory(root[\"_Highcharts\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ marker_clusters_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Extensions/MarkerClusters/MarkerClusterDefaults.js\n/* *\n *\n *  Marker clusters module.\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  Author: Wojciech Chmiel\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Options for marker clusters, the concept of sampling the data\n * values into larger blocks in order to ease readability and\n * increase performance of the JavaScript charts.\n *\n * Note: marker clusters module is not working with `boost`\n * and `draggable-points` modules.\n *\n * The marker clusters feature requires the marker-clusters.js\n * file to be loaded, found in the modules directory of the download\n * package, or online at [code.highcharts.com/modules/marker-clusters.js\n * ](code.highcharts.com/modules/marker-clusters.js).\n *\n * @sample maps/marker-clusters/europe\n *         Maps marker clusters\n * @sample highcharts/marker-clusters/basic\n *         Scatter marker clusters\n * @sample maps/marker-clusters/optimized-kmeans\n *         Marker clusters with colorAxis\n *\n * @product      highcharts highmaps\n * @since 8.0.0\n * @optionparent plotOptions.scatter.cluster\n *\n * @private\n */\nconst cluster = {\n    /**\n     * Whether to enable the marker-clusters module.\n     *\n     * @sample maps/marker-clusters/basic\n     *         Maps marker clusters\n     * @sample highcharts/marker-clusters/basic\n     *         Scatter marker clusters\n     */\n    enabled: false,\n    /**\n     * When set to `false` prevent cluster overlapping - this option\n     * works only when `layoutAlgorithm.type = \"grid\"`.\n     *\n     * @sample highcharts/marker-clusters/grid\n     *         Prevent overlapping\n     */\n    allowOverlap: true,\n    /**\n     * Options for the cluster marker animation.\n     * @type    {boolean|Partial<Highcharts.AnimationOptionsObject>}\n     * @default { \"duration\": 500 }\n     */\n    animation: {\n        /** @ignore-option */\n        duration: 500\n    },\n    /**\n     * Zoom the plot area to the cluster points range when a cluster is clicked.\n     */\n    drillToCluster: true,\n    /**\n     * The minimum amount of points to be combined into a cluster.\n     * This value has to be greater or equal to 2.\n     *\n     * @sample highcharts/marker-clusters/basic\n     *         At least three points in the cluster\n     */\n    minimumClusterSize: 2,\n    /**\n     * Options for layout algorithm. Inside there\n     * are options to change the type of the algorithm, gridSize,\n     * distance or iterations.\n     */\n    layoutAlgorithm: {\n        /**\n         * Type of the algorithm used to combine points into a cluster.\n         * There are three available algorithms:\n         *\n         * 1) `grid` - grid-based clustering technique. Points are assigned\n         * to squares of set size depending on their position on the plot\n         * area. Points inside the grid square are combined into a cluster.\n         * The grid size can be controlled by `gridSize` property\n         * (grid size changes at certain zoom levels).\n         *\n         * 2) `kmeans` - based on K-Means clustering technique. In the\n         * first step, points are divided using the grid method (distance\n         * property is a grid size) to find the initial amount of clusters.\n         * Next, each point is classified by computing the distance between\n         * each cluster center and that point. When the closest cluster\n         * distance is lower than distance property set by a user the point\n         * is added to this cluster otherwise is classified as `noise`. The\n         * algorithm is repeated until each cluster center not change its\n         * previous position more than one pixel. This technique is more\n         * accurate but also more time consuming than the `grid` algorithm,\n         * especially for big datasets.\n         *\n         * 3) `optimizedKmeans` - based on K-Means clustering technique. This\n         * algorithm uses k-means algorithm only on the chart initialization\n         * or when chart extremes have greater range than on initialization.\n         * When a chart is redrawn the algorithm checks only clustered points\n         * distance from the cluster center and rebuild it when the point is\n         * spaced enough to be outside the cluster. It provides performance\n         * improvement and more stable clusters position yet can be used rather\n         * on small and sparse datasets.\n         *\n         * By default, the algorithm depends on visible quantity of points\n         * and `kmeansThreshold`. When there are more visible points than the\n         * `kmeansThreshold` the `grid` algorithm is used, otherwise `kmeans`.\n         *\n         * The custom clustering algorithm can be added by assigning a callback\n         * function as the type property. This function takes an array of\n         * `processedXData`, `processedYData`, `processedXData` indexes and\n         * `layoutAlgorithm` options as arguments and should return an object\n         * with grouped data.\n         *\n         * The algorithm should return an object like that:\n         * <pre>{\n         *  clusterId1: [{\n         *      x: 573,\n         *      y: 285,\n         *      index: 1 // point index in the data array\n         *  }, {\n         *      x: 521,\n         *      y: 197,\n         *      index: 2\n         *  }],\n         *  clusterId2: [{\n         *      ...\n         *  }]\n         *  ...\n         * }</pre>\n         *\n         * `clusterId` (example above - unique id of a cluster or noise)\n         * is an array of points belonging to a cluster. If the\n         * array has only one point or fewer points than set in\n         * `cluster.minimumClusterSize` it won't be combined into a cluster.\n         *\n         * @sample maps/marker-clusters/optimized-kmeans\n         *         Optimized K-Means algorithm\n         * @sample highcharts/marker-clusters/kmeans\n         *         K-Means algorithm\n         * @sample highcharts/marker-clusters/grid\n         *         Grid algorithm\n         * @sample maps/marker-clusters/custom-alg\n         *         Custom algorithm\n         *\n         * @type {string|Function}\n         * @see [cluster.minimumClusterSize](#plotOptions.scatter.cluster.minimumClusterSize)\n         * @apioption plotOptions.scatter.cluster.layoutAlgorithm.type\n         */\n        /**\n         * When `type` is set to the `grid`,\n         * `gridSize` is a size of a grid square element either as a number\n         * defining pixels, or a percentage defining a percentage\n         * of the plot area width.\n         *\n         * @type    {number|string}\n         */\n        gridSize: 50,\n        /**\n         * When `type` is set to `kmeans`,\n         * `iterations` are the number of iterations that this algorithm will be\n         * repeated to find clusters positions.\n         *\n         * @type    {number}\n         * @apioption plotOptions.scatter.cluster.layoutAlgorithm.iterations\n         */\n        /**\n         * When `type` is set to `kmeans`,\n         * `distance` is a maximum distance between point and cluster center\n         * so that this point will be inside the cluster. The distance\n         * is either a number defining pixels or a percentage\n         * defining a percentage of the plot area width.\n         *\n         * @type    {number|string}\n         */\n        distance: 40,\n        /**\n         * When `type` is set to `undefined` and there are more visible points\n         * than the kmeansThreshold the `grid` algorithm is used to find\n         * clusters, otherwise `kmeans`. It ensures good performance on\n         * large datasets and better clusters arrangement after the zoom.\n         */\n        kmeansThreshold: 100\n    },\n    /**\n     * Options for the cluster marker.\n     * @type      {Highcharts.PointMarkerOptionsObject}\n     * @extends   plotOptions.series.marker\n     * @excluding enabledThreshold, states\n     */\n    marker: {\n        /** @internal */\n        symbol: 'cluster',\n        /** @internal */\n        radius: 15,\n        /** @internal */\n        lineWidth: 0,\n        /** @internal */\n        lineColor: \"#ffffff\" /* Palette.backgroundColor */\n    },\n    /**\n     * Fires when the cluster point is clicked and `drillToCluster` is enabled.\n     * One parameter, `event`, is passed to the function. The default action\n     * is to zoom to the cluster points range. This can be prevented\n     * by calling `event.preventDefault()`.\n     *\n     * @type      {Highcharts.MarkerClusterDrillCallbackFunction}\n     * @product   highcharts highmaps\n     * @see [cluster.drillToCluster](#plotOptions.scatter.cluster.drillToCluster)\n     * @apioption plotOptions.scatter.cluster.events.drillToCluster\n     */\n    /**\n     * An array defining zones within marker clusters.\n     *\n     * In styled mode, the color zones are styled with the\n     * `.highcharts-cluster-zone-{n}` class, or custom\n     * classed from the `className`\n     * option.\n     *\n     * @sample highcharts/marker-clusters/basic\n     *         Marker clusters zones\n     * @sample maps/marker-clusters/custom-alg\n     *         Zones on maps\n     *\n     * @type      {Array<*>}\n     * @product   highcharts highmaps\n     * @apioption plotOptions.scatter.cluster.zones\n     */\n    /**\n     * Styled mode only. A custom class name for the zone.\n     *\n     * @sample highcharts/css/color-zones/\n     *         Zones styled by class name\n     *\n     * @type      {string}\n     * @apioption plotOptions.scatter.cluster.zones.className\n     */\n    /**\n     * Settings for the cluster marker belonging to the zone.\n     *\n     * @see [cluster.marker](#plotOptions.scatter.cluster.marker)\n     * @extends   plotOptions.scatter.cluster.marker\n     * @product   highcharts highmaps\n     * @apioption plotOptions.scatter.cluster.zones.marker\n     */\n    /**\n     * The value where the zone starts.\n     *\n     * @type      {number}\n     * @product   highcharts highmaps\n     * @apioption plotOptions.scatter.cluster.zones.from\n     */\n    /**\n     * The value where the zone ends.\n     *\n     * @type      {number}\n     * @product   highcharts highmaps\n     * @apioption plotOptions.scatter.cluster.zones.to\n     */\n    /**\n     * The fill color of the cluster marker in hover state. When\n     * `undefined`, the series' or point's fillColor for normal\n     * state is used.\n     *\n     * @type      {Highcharts.ColorType}\n     * @apioption plotOptions.scatter.cluster.states.hover.fillColor\n     */\n    /**\n     * Options for the cluster data labels.\n     * @type    {Highcharts.DataLabelsOptions}\n     */\n    dataLabels: {\n        /** @internal */\n        enabled: true,\n        /** @internal */\n        format: '{point.clusterPointsAmount}',\n        /** @internal */\n        verticalAlign: 'middle',\n        /** @internal */\n        align: 'center',\n        /** @internal */\n        style: {\n            color: 'contrast'\n        },\n        /** @internal */\n        inside: true\n    }\n};\nconst tooltip = {\n    /**\n     * The HTML of the cluster point's in the tooltip. Works only with\n     * marker-clusters module and analogously to\n     * [pointFormat](#tooltip.pointFormat).\n     *\n     * The cluster tooltip can be also formatted using\n     * `tooltip.formatter` callback function and `point.isCluster` flag.\n     *\n     * @sample highcharts/marker-clusters/grid\n     *         Format tooltip for cluster points.\n     *\n     * @sample maps/marker-clusters/europe/\n     *         Format tooltip for clusters using tooltip.formatter\n     *\n     * @type      {string}\n     * @default   Clustered points: {point.clusterPointsAmount}\n     * @apioption tooltip.clusterFormat\n     */\n    clusterFormat: '<span>Clustered points: ' +\n        '{point.clusterPointsAmount}</span><br/>'\n};\n/* *\n *\n *  Default Export\n *\n * */\nconst MarkerClusterDefaults = {\n    cluster,\n    tooltip\n};\n/* harmony default export */ const MarkerClusters_MarkerClusterDefaults = (MarkerClusterDefaults);\n\n;// ./code/es-modules/Data/ColumnUtils.js\n/* *\n *\n *  (c) 2020-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n/**\n * Utility functions for columns that can be either arrays or typed arrays.\n * @private\n */\nvar ColumnUtils;\n(function (ColumnUtils) {\n    /* *\n    *\n    *  Declarations\n    *\n    * */\n    /* *\n    *\n    * Functions\n    *\n    * */\n    /**\n     * Sets the length of the column array.\n     *\n     * @param {DataTable.Column} column\n     * Column to be modified.\n     *\n     * @param {number} length\n     * New length of the column.\n     *\n     * @param {boolean} asSubarray\n     * If column is a typed array, return a subarray instead of a new array. It\n     * is faster `O(1)`, but the entire buffer will be kept in memory until all\n     * views to it are destroyed. Default is `false`.\n     *\n     * @return {DataTable.Column}\n     * Modified column.\n     *\n     * @private\n     */\n    function setLength(column, length, asSubarray) {\n        if (Array.isArray(column)) {\n            column.length = length;\n            return column;\n        }\n        return column[asSubarray ? 'subarray' : 'slice'](0, length);\n    }\n    ColumnUtils.setLength = setLength;\n    /**\n     * Splices a column array.\n     *\n     * @param {DataTable.Column} column\n     * Column to be modified.\n     *\n     * @param {number} start\n     * Index at which to start changing the array.\n     *\n     * @param {number} deleteCount\n     * An integer indicating the number of old array elements to remove.\n     *\n     * @param {boolean} removedAsSubarray\n     * If column is a typed array, return a subarray instead of a new array. It\n     * is faster `O(1)`, but the entire buffer will be kept in memory until all\n     * views to it are destroyed. Default is `true`.\n     *\n     * @param {Array<number>|TypedArray} items\n     * The elements to add to the array, beginning at the start index. If you\n     * don't specify any elements, `splice()` will only remove elements from the\n     * array.\n     *\n     * @return {SpliceResult}\n     * Object containing removed elements and the modified column.\n     *\n     * @private\n     */\n    function splice(column, start, deleteCount, removedAsSubarray, items = []) {\n        if (Array.isArray(column)) {\n            if (!Array.isArray(items)) {\n                items = Array.from(items);\n            }\n            return {\n                removed: column.splice(start, deleteCount, ...items),\n                array: column\n            };\n        }\n        const Constructor = Object.getPrototypeOf(column)\n            .constructor;\n        const removed = column[removedAsSubarray ? 'subarray' : 'slice'](start, start + deleteCount);\n        const newLength = column.length - deleteCount + items.length;\n        const result = new Constructor(newLength);\n        result.set(column.subarray(0, start), 0);\n        result.set(items, start);\n        result.set(column.subarray(start + deleteCount), start + items.length);\n        return {\n            removed: removed,\n            array: result\n        };\n    }\n    ColumnUtils.splice = splice;\n})(ColumnUtils || (ColumnUtils = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Data_ColumnUtils = (ColumnUtils);\n\n;// ./code/es-modules/Data/DataTableCore.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Gøran Slettemark\n *  - Torstein Hønsi\n *\n * */\n\n\nconst { setLength, splice } = Data_ColumnUtils;\n\nconst { fireEvent, objectEach, uniqueKey } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * Class to manage columns and rows in a table structure. It provides methods\n * to add, remove, and manipulate columns and rows, as well as to retrieve data\n * from specific cells.\n *\n * @class\n * @name Highcharts.DataTable\n *\n * @param {Highcharts.DataTableOptions} [options]\n * Options to initialize the new DataTable instance.\n */\nclass DataTableCore {\n    /**\n     * Constructs an instance of the DataTable class.\n     *\n     * @example\n     * const dataTable = new Highcharts.DataTableCore({\n     *   columns: {\n     *     year: [2020, 2021, 2022, 2023],\n     *     cost: [11, 13, 12, 14],\n     *     revenue: [12, 15, 14, 18]\n     *   }\n     * });\n\n     *\n     * @param {Highcharts.DataTableOptions} [options]\n     * Options to initialize the new DataTable instance.\n     */\n    constructor(options = {}) {\n        /**\n         * Whether the ID was automatic generated or given in the constructor.\n         *\n         * @name Highcharts.DataTable#autoId\n         * @type {boolean}\n         */\n        this.autoId = !options.id;\n        this.columns = {};\n        /**\n         * ID of the table for identification purposes.\n         *\n         * @name Highcharts.DataTable#id\n         * @type {string}\n         */\n        this.id = (options.id || uniqueKey());\n        this.modified = this;\n        this.rowCount = 0;\n        this.versionTag = uniqueKey();\n        let rowCount = 0;\n        objectEach(options.columns || {}, (column, columnName) => {\n            this.columns[columnName] = column.slice();\n            rowCount = Math.max(rowCount, column.length);\n        });\n        this.applyRowCount(rowCount);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Applies a row count to the table by setting the `rowCount` property and\n     * adjusting the length of all columns.\n     *\n     * @private\n     * @param {number} rowCount The new row count.\n     */\n    applyRowCount(rowCount) {\n        this.rowCount = rowCount;\n        objectEach(this.columns, (column, columnName) => {\n            if (column.length !== rowCount) {\n                this.columns[columnName] = setLength(column, rowCount);\n            }\n        });\n    }\n    /**\n     * Delete rows. Simplified version of the full\n     * `DataTable.deleteRows` method.\n     *\n     * @param {number} rowIndex\n     * The start row index\n     *\n     * @param {number} [rowCount=1]\n     * The number of rows to delete\n     *\n     * @return {void}\n     *\n     * @emits #afterDeleteRows\n     */\n    deleteRows(rowIndex, rowCount = 1) {\n        if (rowCount > 0 && rowIndex < this.rowCount) {\n            let length = 0;\n            objectEach(this.columns, (column, columnName) => {\n                this.columns[columnName] =\n                    splice(column, rowIndex, rowCount).array;\n                length = column.length;\n            });\n            this.rowCount = length;\n        }\n        fireEvent(this, 'afterDeleteRows', { rowIndex, rowCount });\n        this.versionTag = uniqueKey();\n    }\n    /**\n     * Fetches the given column by the canonical column name. Simplified version\n     * of the full `DataTable.getRow` method, always returning by reference.\n     *\n     * @param {string} columnName\n     * Name of the column to get.\n     *\n     * @return {Highcharts.DataTableColumn|undefined}\n     * A copy of the column, or `undefined` if not found.\n     */\n    getColumn(columnName, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    asReference) {\n        return this.columns[columnName];\n    }\n    /**\n     * Retrieves all or the given columns. Simplified version of the full\n     * `DataTable.getColumns` method, always returning by reference.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names to retrieve.\n     *\n     * @return {Highcharts.DataTableColumnCollection}\n     * Collection of columns. If a requested column was not found, it is\n     * `undefined`.\n     */\n    getColumns(columnNames, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    asReference) {\n        return (columnNames || Object.keys(this.columns)).reduce((columns, columnName) => {\n            columns[columnName] = this.columns[columnName];\n            return columns;\n        }, {});\n    }\n    /**\n     * Retrieves the row at a given index.\n     *\n     * @param {number} rowIndex\n     * Row index to retrieve. First row has index 0.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names to retrieve.\n     *\n     * @return {Record<string, number|string|undefined>|undefined}\n     * Returns the row values, or `undefined` if not found.\n     */\n    getRow(rowIndex, columnNames) {\n        return (columnNames || Object.keys(this.columns)).map((key) => this.columns[key]?.[rowIndex]);\n    }\n    /**\n     * Sets cell values for a column. Will insert a new column, if not found.\n     *\n     * @param {string} columnName\n     * Column name to set.\n     *\n     * @param {Highcharts.DataTableColumn} [column]\n     * Values to set in the column.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to change. (Default: 0)\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setColumns\n     * @emits #afterSetColumns\n     */\n    setColumn(columnName, column = [], rowIndex = 0, eventDetail) {\n        this.setColumns({ [columnName]: column }, rowIndex, eventDetail);\n    }\n    /**\n     * Sets cell values for multiple columns. Will insert new columns, if not\n     * found. Simplified version of the full `DataTableCore.setColumns`, limited\n     * to full replacement of the columns (undefined `rowIndex`).\n     *\n     * @param {Highcharts.DataTableColumnCollection} columns\n     * Columns as a collection, where the keys are the column names.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to change. Ignored in the `DataTableCore`, as it\n     * always replaces the full column.\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setColumns\n     * @emits #afterSetColumns\n     */\n    setColumns(columns, rowIndex, eventDetail) {\n        let rowCount = this.rowCount;\n        objectEach(columns, (column, columnName) => {\n            this.columns[columnName] = column.slice();\n            rowCount = column.length;\n        });\n        this.applyRowCount(rowCount);\n        if (!eventDetail?.silent) {\n            fireEvent(this, 'afterSetColumns');\n            this.versionTag = uniqueKey();\n        }\n    }\n    /**\n     * Sets cell values of a row. Will insert a new row if no index was\n     * provided, or if the index is higher than the total number of table rows.\n     * A simplified version of the full `DateTable.setRow`, limited to objects.\n     *\n     * @param {Record<string, number|string|undefined>} row\n     * Cell values to set.\n     *\n     * @param {number} [rowIndex]\n     * Index of the row to set. Leave `undefined` to add as a new row.\n     *\n     * @param {boolean} [insert]\n     * Whether to insert the row at the given index, or to overwrite the row.\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #afterSetRows\n     */\n    setRow(row, rowIndex = this.rowCount, insert, eventDetail) {\n        const { columns } = this, indexRowCount = insert ? this.rowCount + 1 : rowIndex + 1;\n        objectEach(row, (cellValue, columnName) => {\n            let column = columns[columnName] ||\n                eventDetail?.addColumns !== false && new Array(indexRowCount);\n            if (column) {\n                if (insert) {\n                    column = splice(column, rowIndex, 0, true, [cellValue]).array;\n                }\n                else {\n                    column[rowIndex] = cellValue;\n                }\n                columns[columnName] = column;\n            }\n        });\n        if (indexRowCount > this.rowCount) {\n            this.applyRowCount(indexRowCount);\n        }\n        if (!eventDetail?.silent) {\n            fireEvent(this, 'afterSetRows');\n            this.versionTag = uniqueKey();\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Data_DataTableCore = (DataTableCore);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * A typed array.\n * @typedef {Int8Array|Uint8Array|Uint8ClampedArray|Int16Array|Uint16Array|Int32Array|Uint32Array|Float32Array|Float64Array} Highcharts.TypedArray\n * //**\n * A column of values in a data table.\n * @typedef {Array<boolean|null|number|string|undefined>|Highcharts.TypedArray} Highcharts.DataTableColumn\n */ /**\n* A collection of data table columns defined by a object where the key is the\n* column name and the value is an array of the column values.\n* @typedef {Record<string, Highcharts.DataTableColumn>} Highcharts.DataTableColumnCollection\n*/\n/**\n * Options for the `DataTable` or `DataTableCore` classes.\n * @interface Highcharts.DataTableOptions\n */ /**\n* The column options for the data table. The columns are defined by an object\n* where the key is the column ID and the value is an array of the column\n* values.\n*\n* @name Highcharts.DataTableOptions.columns\n* @type {Highcharts.DataTableColumnCollection|undefined}\n*/ /**\n* Custom ID to identify the new DataTable instance.\n*\n* @name Highcharts.DataTableOptions.id\n* @type {string|undefined}\n*/\n(''); // Keeps doclets above in JS file\n\n;// ./code/es-modules/Extensions/MarkerClusters/MarkerClusterScatter.js\n/* *\n *\n *  Marker clusters module.\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  Author: Wojciech Chmiel\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { animObject } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nconst { cluster: clusterDefaults } = MarkerClusters_MarkerClusterDefaults;\n\nconst { addEvent, defined, error, isArray, isFunction, isObject, isNumber, merge, objectEach: MarkerClusterScatter_objectEach, relativeLength, syncTimeout } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\nconst markerClusterAlgorithms = {\n    grid: function (dataX, dataY, dataIndexes, options) {\n        const series = this, grid = {}, gridOffset = series.getGridOffset(), scaledGridSize = series.getScaledGridSize(options);\n        let x, y, gridX, gridY, key, i;\n        for (i = 0; i < dataX.length; i++) {\n            const p = valuesToPixels(series, { x: dataX[i], y: dataY[i] });\n            x = p.x - gridOffset.plotLeft;\n            y = p.y - gridOffset.plotTop;\n            gridX = Math.floor(x / scaledGridSize);\n            gridY = Math.floor(y / scaledGridSize);\n            key = gridY + ':' + gridX;\n            grid[key] ?? (grid[key] = []);\n            grid[key].push({\n                dataIndex: dataIndexes[i],\n                x: dataX[i],\n                y: dataY[i]\n            });\n        }\n        return grid;\n    },\n    kmeans: function (dataX, dataY, dataIndexes, options) {\n        const series = this, clusters = [], noise = [], group = {}, pointMaxDistance = options.processedDistance ||\n            clusterDefaults.layoutAlgorithm.distance, iterations = options.iterations, \n        // Max pixel difference beetwen new and old cluster position.\n        maxClusterShift = 1;\n        let currentIteration = 0, repeat = true, pointX = 0, pointY = 0, tempPos, pointClusterDistance = [];\n        options.processedGridSize = options.processedDistance;\n        // Use grid method to get groupedData object.\n        const groupedData = series.markerClusterAlgorithms ?\n            series.markerClusterAlgorithms.grid.call(series, dataX, dataY, dataIndexes, options) : {};\n        // Find clusters amount and its start positions\n        // based on grid grouped data.\n        for (const key in groupedData) {\n            if (groupedData[key].length > 1) {\n                tempPos = getClusterPosition(groupedData[key]);\n                clusters.push({\n                    posX: tempPos.x,\n                    posY: tempPos.y,\n                    oldX: 0,\n                    oldY: 0,\n                    startPointsLen: groupedData[key].length,\n                    points: []\n                });\n            }\n        }\n        // Start kmeans iteration process.\n        while (repeat) {\n            for (const c of clusters) {\n                c.points.length = 0;\n            }\n            noise.length = 0;\n            for (let i = 0; i < dataX.length; i++) {\n                pointX = dataX[i];\n                pointY = dataY[i];\n                pointClusterDistance = series.getClusterDistancesFromPoint(clusters, pointX, pointY);\n                if (pointClusterDistance.length &&\n                    pointClusterDistance[0].distance < pointMaxDistance) {\n                    clusters[pointClusterDistance[0].clusterIndex].points.push({\n                        x: pointX,\n                        y: pointY,\n                        dataIndex: dataIndexes[i]\n                    });\n                }\n                else {\n                    noise.push({\n                        x: pointX,\n                        y: pointY,\n                        dataIndex: dataIndexes[i]\n                    });\n                }\n            }\n            // When cluster points array has only one point the\n            // point should be classified again.\n            for (let i = 0; i < clusters.length; i++) {\n                if (clusters[i].points.length === 1) {\n                    pointClusterDistance = series.getClusterDistancesFromPoint(clusters, clusters[i].points[0].x, clusters[i].points[0].y);\n                    if (pointClusterDistance[1].distance < pointMaxDistance) {\n                        // Add point to the next closest cluster.\n                        clusters[pointClusterDistance[1].clusterIndex].points\n                            .push(clusters[i].points[0]);\n                        // Clear points array.\n                        clusters[pointClusterDistance[0].clusterIndex]\n                            .points.length = 0;\n                    }\n                }\n            }\n            // Compute a new clusters position and check if it\n            // is different than the old one.\n            repeat = false;\n            for (let i = 0; i < clusters.length; i++) {\n                tempPos = getClusterPosition(clusters[i].points);\n                clusters[i].oldX = clusters[i].posX;\n                clusters[i].oldY = clusters[i].posY;\n                clusters[i].posX = tempPos.x;\n                clusters[i].posY = tempPos.y;\n                // Repeat the algorithm if at least one cluster\n                // is shifted more than maxClusterShift property.\n                if (clusters[i].posX > clusters[i].oldX + maxClusterShift ||\n                    clusters[i].posX < clusters[i].oldX - maxClusterShift ||\n                    clusters[i].posY > clusters[i].oldY + maxClusterShift ||\n                    clusters[i].posY < clusters[i].oldY - maxClusterShift) {\n                    repeat = true;\n                }\n            }\n            // If iterations property is set repeat the algorithm\n            // specified amount of times.\n            if (iterations) {\n                repeat = currentIteration < iterations - 1;\n            }\n            currentIteration++;\n        }\n        for (let i = 0, iEnd = clusters.length; i < iEnd; ++i) {\n            group['cluster' + i] = clusters[i].points;\n        }\n        for (let i = 0, iEnd = noise.length; i < iEnd; ++i) {\n            group['noise' + i] = [noise[i]];\n        }\n        return group;\n    },\n    optimizedKmeans: function (processedXData, processedYData, dataIndexes, options) {\n        const series = this, pointMaxDistance = options.processedDistance ||\n            clusterDefaults.layoutAlgorithm.gridSize, extremes = series.getRealExtremes(), clusterMarkerOptions = (series.options.cluster || {}).marker;\n        let distance, group = {}, offset, radius;\n        if (!series.markerClusterInfo || (series.initMaxX && series.initMaxX < extremes.maxX ||\n            series.initMinX && series.initMinX > extremes.minX ||\n            series.initMaxY && series.initMaxY < extremes.maxY ||\n            series.initMinY && series.initMinY > extremes.minY)) {\n            series.initMaxX = extremes.maxX;\n            series.initMinX = extremes.minX;\n            series.initMaxY = extremes.maxY;\n            series.initMinY = extremes.minY;\n            group = series.markerClusterAlgorithms ?\n                series.markerClusterAlgorithms.kmeans.call(series, processedXData, processedYData, dataIndexes, options) : {};\n            series.baseClusters = null;\n        }\n        else {\n            series.baseClusters ?? (series.baseClusters = {\n                clusters: series.markerClusterInfo.clusters,\n                noise: series.markerClusterInfo.noise\n            });\n            for (const cluster of series.baseClusters.clusters) {\n                cluster.pointsOutside = [];\n                cluster.pointsInside = [];\n                for (const dataPoint of cluster.data) {\n                    const dataPointPx = valuesToPixels(series, dataPoint), clusterPx = valuesToPixels(series, cluster);\n                    distance = Math.sqrt(Math.pow(dataPointPx.x - clusterPx.x, 2) +\n                        Math.pow(dataPointPx.y - clusterPx.y, 2));\n                    if (cluster.clusterZone?.marker?.radius) {\n                        radius = cluster.clusterZone.marker.radius;\n                    }\n                    else if (clusterMarkerOptions?.radius) {\n                        radius = clusterMarkerOptions.radius;\n                    }\n                    else {\n                        radius = clusterDefaults.marker.radius;\n                    }\n                    offset = pointMaxDistance - radius >= 0 ?\n                        pointMaxDistance - radius : radius;\n                    if (distance > radius + offset &&\n                        defined(cluster.pointsOutside)) {\n                        cluster.pointsOutside.push(dataPoint);\n                    }\n                    else if (defined(cluster.pointsInside)) {\n                        cluster.pointsInside.push(dataPoint);\n                    }\n                }\n                if (cluster.pointsInside.length) {\n                    group[cluster.id] = cluster.pointsInside;\n                }\n                let i = 0;\n                for (const p of cluster.pointsOutside) {\n                    group[cluster.id + '_noise' + i++] = [p];\n                }\n            }\n            for (const noise of series.baseClusters.noise) {\n                group[noise.id] = noise.data;\n            }\n        }\n        return group;\n    }\n};\n/* *\n *\n *  Variables\n *\n * */\nlet baseGeneratePoints, \n/**\n * Points that ids are included in the oldPointsStateId array are hidden\n * before animation. Other ones are destroyed.\n * @private\n */\noldPointsStateId = [], stateIdCounter = 0;\n/* *\n *\n *  Functions\n *\n * */\n/** @private */\nfunction compose(highchartsDefaultOptions, ScatterSeriesClass) {\n    const scatterProto = ScatterSeriesClass.prototype;\n    if (!scatterProto.markerClusterAlgorithms) {\n        baseGeneratePoints = scatterProto.generatePoints;\n        scatterProto.markerClusterAlgorithms = markerClusterAlgorithms;\n        scatterProto.animateClusterPoint = seriesAnimateClusterPoint;\n        scatterProto.destroyClusteredData = seriesDestroyClusteredData;\n        scatterProto.generatePoints = seriesGeneratePoints;\n        scatterProto.getClusterDistancesFromPoint =\n            seriesGetClusterDistancesFromPoint;\n        scatterProto.getClusteredData = seriesGetClusteredData;\n        scatterProto.getGridOffset = seriesGetGridOffset;\n        scatterProto.getPointsState = seriesGetPointsState;\n        scatterProto.getRealExtremes = seriesGetRealExtremes;\n        scatterProto.getScaledGridSize = seriesGetScaledGridSize;\n        scatterProto.hideClusteredData = seriesHideClusteredData;\n        scatterProto.isValidGroupedDataObject = seriesIsValidGroupedDataObject;\n        scatterProto.preventClusterCollisions = seriesPreventClusterCollisions;\n        // Destroy grouped data on series destroy.\n        addEvent(ScatterSeriesClass, 'destroy', scatterProto.destroyClusteredData);\n        if (highchartsDefaultOptions.plotOptions) {\n            highchartsDefaultOptions.plotOptions.series = merge(highchartsDefaultOptions.plotOptions.series, MarkerClusters_MarkerClusterDefaults);\n        }\n    }\n}\n/**\n * Util function.\n * @private\n */\nfunction destroyOldPoints(oldState) {\n    for (const key of Object.keys(oldState)) {\n        oldState[key].point?.destroy?.();\n    }\n}\n/**\n * Util function.\n * @private\n */\nfunction fadeInElement(elem, opacity, animation) {\n    elem.attr({ opacity }).animate({ opacity: 1 }, animation);\n}\n/**\n * Util function.\n * @private\n */\nfunction fadeInNewPointAndDestoryOld(newPointObj, oldPoints, animation, opacity) {\n    // Fade in new point.\n    fadeInStatePoint(newPointObj, opacity, animation, true, true);\n    // Destroy old animated points.\n    for (const p of oldPoints) {\n        p.point?.destroy?.();\n    }\n}\n/**\n * Util function.\n * @private\n */\nfunction fadeInStatePoint(stateObj, opacity, animation, fadeinGraphic, fadeinDataLabel) {\n    if (stateObj.point) {\n        if (fadeinGraphic && stateObj.point.graphic) {\n            stateObj.point.graphic.show();\n            fadeInElement(stateObj.point.graphic, opacity, animation);\n        }\n        if (fadeinDataLabel && stateObj.point.dataLabel) {\n            stateObj.point.dataLabel.show();\n            fadeInElement(stateObj.point.dataLabel, opacity, animation);\n        }\n    }\n}\n/**\n * Util function.\n * @private\n */\nfunction getClusterPosition(points) {\n    const pointsLen = points.length;\n    let sumX = 0, sumY = 0;\n    for (let i = 0; i < pointsLen; i++) {\n        sumX += points[i].x;\n        sumY += points[i].y;\n    }\n    return {\n        x: sumX / pointsLen,\n        y: sumY / pointsLen\n    };\n}\n/**\n * Util function.Prepare array with sorted data objects to be compared in\n * getPointsState method.\n * @private\n */\nfunction getDataState(clusteredData, stateDataLen) {\n    const state = [];\n    state.length = stateDataLen;\n    clusteredData.clusters.forEach(function (cluster) {\n        cluster.data.forEach(function (elem) {\n            state[elem.dataIndex] = elem;\n        });\n    });\n    clusteredData.noise.forEach(function (noise) {\n        state[noise.data[0].dataIndex] = noise.data[0];\n    });\n    return state;\n}\n/**\n * Util function. Generate unique stateId for a state element.\n * @private\n */\nfunction getStateId() {\n    return Math.random().toString(36).substring(2, 7) + '-' + stateIdCounter++;\n}\n/**\n * Util function.\n * @private\n */\nfunction hideStatePoint(stateObj, hideGraphic, hideDataLabel) {\n    if (stateObj.point) {\n        if (hideGraphic && stateObj.point.graphic) {\n            stateObj.point.graphic.hide();\n        }\n        if (hideDataLabel && stateObj.point.dataLabel) {\n            stateObj.point.dataLabel.hide();\n        }\n    }\n}\n/** @private */\nfunction onPointDrillToCluster(event) {\n    const point = event.point || event.target;\n    point.firePointEvent('drillToCluster', event, function (e) {\n        const point = e.point || e.target, series = point.series, { xAxis, yAxis, chart } = series, { inverted, mapView, pointer } = chart, drillToCluster = series.options.cluster?.drillToCluster;\n        if (drillToCluster && point.clusteredData) {\n            const sortedDataX = point.clusteredData\n                .map((data) => data.x)\n                .sort((a, b) => a - b), sortedDataY = point.clusteredData\n                .map((data) => data.y)\n                .sort((a, b) => a - b), minX = sortedDataX[0], maxX = sortedDataX[sortedDataX.length - 1], minY = sortedDataY[0], maxY = sortedDataY[sortedDataY.length - 1], offsetX = Math.abs((maxX - minX) * 0.1), offsetY = Math.abs((maxY - minY) * 0.1), x1 = Math.min(minX, maxX) - offsetX, x2 = Math.max(minX, maxX) + offsetX, y1 = Math.min(minY, maxY) - offsetY, y2 = Math.max(minY, maxY) + offsetY;\n            if (mapView) {\n                mapView.fitToBounds({ x1, x2, y1, y2 });\n            }\n            else if (xAxis && yAxis) {\n                let x1Px = xAxis.toPixels(x1), x2Px = xAxis.toPixels(x2), y1Px = yAxis.toPixels(y1), y2Px = yAxis.toPixels(y2);\n                if (inverted) {\n                    [x1Px, x2Px, y1Px, y2Px] = [y1Px, y2Px, x1Px, x2Px];\n                }\n                if (x1Px > x2Px) {\n                    [x1Px, x2Px] = [x2Px, x1Px];\n                }\n                if (y1Px > y2Px) {\n                    [y1Px, y2Px] = [y2Px, y1Px];\n                }\n                if (pointer) {\n                    pointer.zoomX = true;\n                    pointer.zoomY = true;\n                }\n                chart.transform({\n                    from: {\n                        x: x1Px,\n                        y: y1Px,\n                        width: x2Px - x1Px,\n                        height: y2Px - y1Px\n                    }\n                });\n            }\n        }\n    });\n}\n/**\n * Util function.\n * @private\n */\nfunction pixelsToValues(series, pos) {\n    const { chart, xAxis, yAxis } = series;\n    if (chart.mapView) {\n        return chart.mapView.pixelsToProjectedUnits(pos);\n    }\n    return {\n        x: xAxis ? xAxis.toValue(pos.x) : 0,\n        y: yAxis ? yAxis.toValue(pos.y) : 0\n    };\n}\n/** @private */\nfunction seriesAnimateClusterPoint(clusterObj) {\n    const series = this, chart = series.chart, mapView = chart.mapView, animation = animObject(series.options.cluster?.animation), animDuration = animation.duration || 500, pointsState = series.markerClusterInfo?.pointsState, newState = pointsState?.newState, oldState = pointsState?.oldState, oldPoints = [];\n    let parentId, oldPointObj, newPointObj, newPointBBox, offset = 0, newX = 0, newY = 0, isOldPointGrahic = false, isCbHandled = false;\n    if (oldState && newState) {\n        newPointObj = newState[clusterObj.stateId];\n        const newPos = valuesToPixels(series, newPointObj);\n        newX = newPos.x - (mapView ? 0 : chart.plotLeft);\n        newY = newPos.y - (mapView ? 0 : chart.plotTop);\n        // Point has one ancestor.\n        if (newPointObj.parentsId.length === 1) {\n            parentId = newState?.[clusterObj.stateId].parentsId[0];\n            oldPointObj = oldState[parentId];\n            // If old and new positions are the same do not animate.\n            if (newPointObj.point?.graphic &&\n                oldPointObj.point?.plotX &&\n                oldPointObj.point.plotY &&\n                (oldPointObj.point.plotX !== newPointObj.point.plotX ||\n                    oldPointObj.point.plotY !== newPointObj.point.plotY)) {\n                newPointBBox = newPointObj.point.graphic.getBBox();\n                // Marker image does not have the offset (#14342).\n                offset = newPointObj.point.graphic?.isImg ?\n                    0 : newPointBBox.width / 2;\n                newPointObj.point.graphic.attr({\n                    x: oldPointObj.point.plotX - offset,\n                    y: oldPointObj.point.plotY - offset\n                });\n                newPointObj.point.graphic.animate({\n                    x: newX - (newPointObj.point.graphic.radius || 0),\n                    y: newY - (newPointObj.point.graphic.radius || 0)\n                }, animation, function () {\n                    isCbHandled = true;\n                    // Destroy old point.\n                    oldPointObj.point?.destroy?.();\n                });\n                // Data label animation.\n                if (newPointObj.point.dataLabel?.alignAttr &&\n                    oldPointObj.point.dataLabel?.alignAttr) {\n                    newPointObj.point.dataLabel.attr({\n                        x: oldPointObj.point.dataLabel.alignAttr.x,\n                        y: oldPointObj.point.dataLabel.alignAttr.y\n                    });\n                    newPointObj.point.dataLabel.animate({\n                        x: newPointObj.point.dataLabel.alignAttr.x,\n                        y: newPointObj.point.dataLabel.alignAttr.y\n                    }, animation);\n                }\n            }\n        }\n        else if (newPointObj.parentsId.length === 0) {\n            // Point has no ancestors - new point.\n            // Hide new point.\n            hideStatePoint(newPointObj, true, true);\n            syncTimeout(function () {\n                // Fade in new point.\n                fadeInStatePoint(newPointObj, 0.1, animation, true, true);\n            }, animDuration / 2);\n        }\n        else {\n            // Point has many ancestors.\n            // Hide new point before animation.\n            hideStatePoint(newPointObj, true, true);\n            newPointObj.parentsId.forEach(function (elem) {\n                if (oldState?.[elem]) {\n                    oldPointObj = oldState[elem];\n                    oldPoints.push(oldPointObj);\n                    if (oldPointObj.point?.graphic) {\n                        isOldPointGrahic = true;\n                        oldPointObj.point.graphic.show();\n                        oldPointObj.point.graphic.animate({\n                            x: newX - (oldPointObj.point.graphic.radius || 0),\n                            y: newY - (oldPointObj.point.graphic.radius || 0),\n                            opacity: 0.4\n                        }, animation, function () {\n                            isCbHandled = true;\n                            fadeInNewPointAndDestoryOld(newPointObj, oldPoints, animation, 0.7);\n                        });\n                        if (oldPointObj.point.dataLabel &&\n                            oldPointObj.point.dataLabel.y !== -9999 &&\n                            newPointObj.point?.dataLabel?.alignAttr) {\n                            oldPointObj.point.dataLabel.show();\n                            oldPointObj.point.dataLabel.animate({\n                                x: newPointObj.point.dataLabel.alignAttr.x,\n                                y: newPointObj.point.dataLabel.alignAttr.y,\n                                opacity: 0.4\n                            }, animation);\n                        }\n                    }\n                }\n            });\n            // Make sure point is faded in.\n            syncTimeout(function () {\n                if (!isCbHandled) {\n                    fadeInNewPointAndDestoryOld(newPointObj, oldPoints, animation, 0.85);\n                }\n            }, animDuration);\n            if (!isOldPointGrahic) {\n                syncTimeout(function () {\n                    fadeInNewPointAndDestoryOld(newPointObj, oldPoints, animation, 0.1);\n                }, animDuration / 2);\n            }\n        }\n    }\n}\n/**\n * Destroy clustered data points.\n * @private\n */\nfunction seriesDestroyClusteredData() {\n    // Clear previous groups.\n    this.markerClusterSeriesData?.forEach((point) => {\n        point?.destroy?.();\n    });\n    this.markerClusterSeriesData = null;\n}\n/**\n * Override the generatePoints method by adding a reference to grouped data.\n * @private\n */\nfunction seriesGeneratePoints() {\n    const series = this, { chart } = series, mapView = chart.mapView, xData = series.getColumn('x'), yData = series.getColumn('y'), clusterOptions = series.options.cluster, realExtremes = series.getRealExtremes(), visibleXData = [], visibleYData = [], visibleDataIndexes = [];\n    let oldPointsState, oldDataLen, oldMarkerClusterInfo, kmeansThreshold, cropDataOffsetX, cropDataOffsetY, seriesMinX, seriesMaxX, seriesMinY, seriesMaxY, type, algorithm, clusteredData, groupedData, layoutAlgOptions, point;\n    // For map point series, we need to resolve lon, lat and geometry options\n    // and project them on the plane in order to get x and y. In the regular\n    // series flow, this is not done until the `translate` method because the\n    // resulting [x, y] position depends on inset positions in the MapView.\n    if (mapView && series.is('mappoint') && xData && yData) {\n        series.options.data?.forEach((p, i) => {\n            const xy = series.projectPoint(p);\n            if (xy) {\n                xData[i] = xy.x;\n                yData[i] = xy.y;\n            }\n        });\n    }\n    if (clusterOptions?.enabled &&\n        xData?.length &&\n        yData?.length &&\n        !chart.polar) {\n        type = clusterOptions.layoutAlgorithm.type;\n        layoutAlgOptions = clusterOptions.layoutAlgorithm;\n        // Get processed algorithm properties.\n        layoutAlgOptions.processedGridSize = relativeLength(layoutAlgOptions.gridSize ||\n            clusterDefaults.layoutAlgorithm.gridSize, chart.plotWidth);\n        layoutAlgOptions.processedDistance = relativeLength(layoutAlgOptions.distance ||\n            clusterDefaults.layoutAlgorithm.distance, chart.plotWidth);\n        kmeansThreshold = layoutAlgOptions.kmeansThreshold ||\n            clusterDefaults.layoutAlgorithm.kmeansThreshold;\n        // Offset to prevent cluster size changes.\n        const halfGrid = layoutAlgOptions.processedGridSize / 2, p1 = pixelsToValues(series, { x: 0, y: 0 }), p2 = pixelsToValues(series, { x: halfGrid, y: halfGrid });\n        cropDataOffsetX = Math.abs(p1.x - p2.x);\n        cropDataOffsetY = Math.abs(p1.y - p2.y);\n        // Get only visible data.\n        for (let i = 0; i < xData.length; i++) {\n            if (!series.dataMaxX) {\n                if (!defined(seriesMaxX) ||\n                    !defined(seriesMinX) ||\n                    !defined(seriesMaxY) ||\n                    !defined(seriesMinY)) {\n                    seriesMaxX = seriesMinX = xData[i];\n                    seriesMaxY = seriesMinY = yData[i];\n                }\n                else if (isNumber(yData[i]) &&\n                    isNumber(seriesMaxY) &&\n                    isNumber(seriesMinY)) {\n                    seriesMaxX = Math.max(xData[i], seriesMaxX);\n                    seriesMinX = Math.min(xData[i], seriesMinX);\n                    seriesMaxY = Math.max(yData[i] || seriesMaxY, seriesMaxY);\n                    seriesMinY = Math.min(yData[i] || seriesMinY, seriesMinY);\n                }\n            }\n            // Crop data to visible ones with appropriate offset to prevent\n            // cluster size changes on the edge of the plot area.\n            if (xData[i] >= (realExtremes.minX - cropDataOffsetX) &&\n                xData[i] <= (realExtremes.maxX + cropDataOffsetX) &&\n                (yData[i] || realExtremes.minY) >=\n                    (realExtremes.minY - cropDataOffsetY) &&\n                (yData[i] || realExtremes.maxY) <=\n                    (realExtremes.maxY + cropDataOffsetY)) {\n                visibleXData.push(xData[i]);\n                visibleYData.push(yData[i]);\n                visibleDataIndexes.push(i);\n            }\n        }\n        // Save data max values.\n        if (defined(seriesMaxX) && defined(seriesMinX) &&\n            isNumber(seriesMaxY) && isNumber(seriesMinY)) {\n            series.dataMaxX = seriesMaxX;\n            series.dataMinX = seriesMinX;\n            series.dataMaxY = seriesMaxY;\n            series.dataMinY = seriesMinY;\n        }\n        if (isFunction(type)) {\n            algorithm = type;\n        }\n        else if (series.markerClusterAlgorithms) {\n            if (type && series.markerClusterAlgorithms[type]) {\n                algorithm = series.markerClusterAlgorithms[type];\n            }\n            else {\n                algorithm = visibleXData.length < kmeansThreshold ?\n                    series.markerClusterAlgorithms.kmeans :\n                    series.markerClusterAlgorithms.grid;\n            }\n        }\n        else {\n            algorithm = () => false;\n        }\n        groupedData = algorithm.call(this, visibleXData, visibleYData, visibleDataIndexes, layoutAlgOptions);\n        clusteredData = groupedData ? series.getClusteredData(groupedData, clusterOptions) : groupedData;\n        // When animation is enabled get old points state.\n        if (clusterOptions.animation &&\n            series.markerClusterInfo?.pointsState?.oldState) {\n            // Destroy old points.\n            destroyOldPoints(series.markerClusterInfo.pointsState.oldState);\n            oldPointsState = series.markerClusterInfo.pointsState.newState;\n        }\n        else {\n            oldPointsState = {};\n        }\n        // Save points old state info.\n        oldDataLen = xData.length;\n        oldMarkerClusterInfo = series.markerClusterInfo;\n        if (clusteredData) {\n            series.dataTable.modified = new Data_DataTableCore({\n                columns: {\n                    x: clusteredData.groupedXData,\n                    y: clusteredData.groupedYData\n                }\n            });\n            series.hasGroupedData = true;\n            series.markerClusterInfo = clusteredData;\n            series.groupMap = clusteredData.groupMap;\n        }\n        baseGeneratePoints.apply(this);\n        if (clusteredData && series.markerClusterInfo) {\n            // Mark cluster points. Safe point reference in the cluster object.\n            series.markerClusterInfo.clusters?.forEach((cluster) => {\n                point = series.points[cluster.index];\n                point.isCluster = true;\n                point.clusteredData = cluster.data;\n                point.clusterPointsAmount = cluster.data.length;\n                cluster.point = point;\n                // Add zoom to cluster range.\n                addEvent(point, 'click', onPointDrillToCluster);\n            });\n            // Safe point reference in the noise object.\n            series.markerClusterInfo.noise?.forEach((noise) => {\n                noise.point = series.points[noise.index];\n            });\n            // When animation is enabled save points state.\n            if (clusterOptions.animation &&\n                series.markerClusterInfo) {\n                series.markerClusterInfo.pointsState = {\n                    oldState: oldPointsState,\n                    newState: series.getPointsState(clusteredData, oldMarkerClusterInfo, oldDataLen)\n                };\n            }\n            // Record grouped data in order to let it be destroyed the next time\n            // processData runs.\n            if (!clusterOptions.animation) {\n                this.destroyClusteredData();\n            }\n            else {\n                this.hideClusteredData();\n            }\n            this.markerClusterSeriesData =\n                this.hasGroupedData ? this.points : null;\n        }\n    }\n    else {\n        baseGeneratePoints.apply(this);\n    }\n}\n/** @private */\nfunction seriesGetClusterDistancesFromPoint(clusters, pointX, pointY) {\n    const pointClusterDistance = [];\n    for (let clusterIndex = 0; clusterIndex < clusters.length; clusterIndex++) {\n        const p1 = valuesToPixels(this, { x: pointX, y: pointY }), p2 = valuesToPixels(this, {\n            x: clusters[clusterIndex].posX,\n            y: clusters[clusterIndex].posY\n        }), distance = Math.sqrt(Math.pow(p1.x - p2.x, 2) +\n            Math.pow(p1.y - p2.y, 2));\n        pointClusterDistance.push({ clusterIndex, distance });\n    }\n    return pointClusterDistance.sort((a, b) => a.distance - b.distance);\n}\n/** @private */\nfunction seriesGetClusteredData(groupedData, options) {\n    const series = this, data = series.options.data, groupedXData = [], groupedYData = [], clusters = [], // Container for clusters.\n    noise = [], // Container for points not belonging to any cluster.\n    groupMap = [], \n    // Prevent minimumClusterSize lower than 2.\n    minimumClusterSize = Math.max(2, options.minimumClusterSize || 2);\n    let index = 0, stateId, point, points, pointUserOptions, pointsLen, marker, clusterPos, pointOptions, clusterTempPos, zoneOptions, clusterZone, clusterZoneClassName;\n    // Check if groupedData is valid when user uses a custom algorithm.\n    if (isFunction(options.layoutAlgorithm.type) &&\n        !series.isValidGroupedDataObject(groupedData)) {\n        error('Highcharts marker-clusters module: ' +\n            'The custom algorithm result is not valid!', false, series.chart);\n        return false;\n    }\n    for (const k in groupedData) {\n        if (groupedData[k].length >= minimumClusterSize) {\n            points = groupedData[k];\n            stateId = getStateId();\n            pointsLen = points.length;\n            // Get zone options for cluster.\n            if (options.zones) {\n                for (let i = 0; i < options.zones.length; i++) {\n                    if (pointsLen >= options.zones[i].from &&\n                        pointsLen <= options.zones[i].to) {\n                        clusterZone = options.zones[i];\n                        clusterZone.zoneIndex = i;\n                        zoneOptions = options.zones[i].marker;\n                        clusterZoneClassName = options.zones[i].className;\n                    }\n                }\n            }\n            clusterTempPos = getClusterPosition(points);\n            if (options.layoutAlgorithm.type === 'grid' &&\n                !options.allowOverlap) {\n                marker = series.options.marker || {};\n                clusterPos = series.preventClusterCollisions({\n                    x: clusterTempPos.x,\n                    y: clusterTempPos.y,\n                    key: k,\n                    groupedData: groupedData,\n                    gridSize: series.getScaledGridSize(options.layoutAlgorithm),\n                    defaultRadius: marker.radius || 3 + (marker.lineWidth || 0),\n                    clusterRadius: (zoneOptions && zoneOptions.radius) ?\n                        zoneOptions.radius :\n                        (options.marker || {}).radius ||\n                            clusterDefaults.marker.radius\n                });\n            }\n            else {\n                clusterPos = {\n                    x: clusterTempPos.x,\n                    y: clusterTempPos.y\n                };\n            }\n            for (let i = 0; i < pointsLen; i++) {\n                points[i].parentStateId = stateId;\n            }\n            clusters.push({\n                x: clusterPos.x,\n                y: clusterPos.y,\n                id: k,\n                stateId,\n                index,\n                data: points,\n                clusterZone,\n                clusterZoneClassName\n            });\n            groupedXData.push(clusterPos.x);\n            groupedYData.push(clusterPos.y);\n            groupMap.push({\n                options: {\n                    formatPrefix: 'cluster',\n                    dataLabels: options.dataLabels,\n                    marker: merge(options.marker, {\n                        states: options.states\n                    }, zoneOptions || {})\n                }\n            });\n            // Save cluster data points options.\n            if (data?.length) {\n                for (let i = 0; i < pointsLen; i++) {\n                    if (isObject(data[points[i].dataIndex])) {\n                        points[i].options = data[points[i].dataIndex];\n                    }\n                }\n            }\n            index++;\n            zoneOptions = null;\n        }\n        else {\n            for (let i = 0; i < groupedData[k].length; i++) {\n                // Points not belonging to any cluster.\n                point = groupedData[k][i];\n                stateId = getStateId();\n                pointOptions = null;\n                pointUserOptions = data?.[point.dataIndex];\n                groupedXData.push(point.x);\n                groupedYData.push(point.y);\n                point.parentStateId = stateId;\n                noise.push({\n                    x: point.x,\n                    y: point.y,\n                    id: k,\n                    stateId,\n                    index,\n                    data: groupedData[k]\n                });\n                if (pointUserOptions &&\n                    typeof pointUserOptions === 'object' &&\n                    !isArray(pointUserOptions)) {\n                    pointOptions = merge(pointUserOptions, { x: point.x, y: point.y });\n                }\n                else {\n                    pointOptions = {\n                        userOptions: pointUserOptions,\n                        x: point.x,\n                        y: point.y\n                    };\n                }\n                groupMap.push({ options: pointOptions });\n                index++;\n            }\n        }\n    }\n    return {\n        clusters,\n        noise,\n        groupedXData,\n        groupedYData,\n        groupMap\n    };\n}\n/** @private */\nfunction seriesGetGridOffset() {\n    const series = this, { chart, xAxis, yAxis } = series;\n    let plotLeft = 0, plotTop = 0;\n    if (xAxis && series.dataMinX && series.dataMaxX) {\n        plotLeft = xAxis.reversed ?\n            xAxis.toPixels(series.dataMaxX) : xAxis.toPixels(series.dataMinX);\n    }\n    else {\n        plotLeft = chart.plotLeft;\n    }\n    if (yAxis && series.dataMinY && series.dataMaxY) {\n        plotTop = yAxis.reversed ?\n            yAxis.toPixels(series.dataMinY) : yAxis.toPixels(series.dataMaxY);\n    }\n    else {\n        plotTop = chart.plotTop;\n    }\n    return { plotLeft, plotTop };\n}\n/**\n * Point state used when animation is enabled to compare and bind old points\n * with new ones.\n * @private\n */\nfunction seriesGetPointsState(clusteredData, oldMarkerClusterInfo, dataLength) {\n    const oldDataStateArr = oldMarkerClusterInfo ?\n        getDataState(oldMarkerClusterInfo, dataLength) : [], newDataStateArr = getDataState(clusteredData, dataLength), state = {};\n    // Clear global array before populate with new ids.\n    oldPointsStateId = [];\n    // Build points state structure.\n    clusteredData.clusters.forEach((cluster) => {\n        state[cluster.stateId] = {\n            x: cluster.x,\n            y: cluster.y,\n            id: cluster.stateId,\n            point: cluster.point,\n            parentsId: []\n        };\n    });\n    clusteredData.noise.forEach((noise) => {\n        state[noise.stateId] = {\n            x: noise.x,\n            y: noise.y,\n            id: noise.stateId,\n            point: noise.point,\n            parentsId: []\n        };\n    });\n    let newState, oldState;\n    // Bind new and old state.\n    for (let i = 0; i < newDataStateArr.length; i++) {\n        newState = newDataStateArr[i];\n        oldState = oldDataStateArr[i];\n        if (newState?.parentStateId &&\n            oldState?.parentStateId &&\n            state[newState.parentStateId]?.parentsId.indexOf(oldState.parentStateId) === -1) {\n            state[newState.parentStateId].parentsId.push(oldState.parentStateId);\n            if (oldPointsStateId.indexOf(oldState.parentStateId) === -1) {\n                oldPointsStateId.push(oldState.parentStateId);\n            }\n        }\n    }\n    return state;\n}\n/** @private */\nfunction seriesGetRealExtremes() {\n    const chart = this.chart, x = chart.mapView ? 0 : chart.plotLeft, y = chart.mapView ? 0 : chart.plotTop, p1 = pixelsToValues(this, {\n        x,\n        y\n    }), p2 = pixelsToValues(this, {\n        x: x + chart.plotWidth,\n        y: x + chart.plotHeight\n    }), realMinX = p1.x, realMaxX = p2.x, realMinY = p1.y, realMaxY = p2.y;\n    return {\n        minX: Math.min(realMinX, realMaxX),\n        maxX: Math.max(realMinX, realMaxX),\n        minY: Math.min(realMinY, realMaxY),\n        maxY: Math.max(realMinY, realMaxY)\n    };\n}\n/** @private */\nfunction seriesGetScaledGridSize(options) {\n    const series = this, xAxis = series.xAxis, mapView = series.chart.mapView, processedGridSize = options.processedGridSize ||\n        clusterDefaults.layoutAlgorithm.gridSize;\n    let search = true, k = 1, divider = 1;\n    if (!series.gridValueSize) {\n        if (mapView) {\n            series.gridValueSize = processedGridSize / mapView.getScale();\n        }\n        else {\n            series.gridValueSize = Math.abs(xAxis.toValue(processedGridSize) - xAxis.toValue(0));\n        }\n    }\n    const gridSize = mapView ?\n        series.gridValueSize * mapView.getScale() :\n        xAxis.toPixels(series.gridValueSize) - xAxis.toPixels(0);\n    const scale = +(processedGridSize / gridSize).toFixed(14);\n    // Find the level and its divider.\n    while (search && scale !== 1) {\n        const level = Math.pow(2, k);\n        if (scale > 0.75 && scale < 1.25) {\n            search = false;\n        }\n        else if (scale >= (1 / level) && scale < 2 * (1 / level)) {\n            search = false;\n            divider = level;\n        }\n        else if (scale <= level && scale > level / 2) {\n            search = false;\n            divider = 1 / level;\n        }\n        k++;\n    }\n    return (processedGridSize / divider) / scale;\n}\n/**\n * Hide clustered data points.\n * @private\n */\nfunction seriesHideClusteredData() {\n    const clusteredSeriesData = this.markerClusterSeriesData, oldState = this.markerClusterInfo?.pointsState?.oldState, oldPointsId = oldPointsStateId.map((elem) => oldState?.[elem].point?.id || '');\n    clusteredSeriesData?.forEach((point) => {\n        // If an old point is used in animation hide it, otherwise destroy.\n        if (point &&\n            oldPointsId.indexOf(point.id) !== -1) {\n            if (point.graphic) {\n                point.graphic.hide();\n            }\n            if (point.dataLabel) {\n                point.dataLabel.hide();\n            }\n        }\n        else {\n            point?.destroy?.();\n        }\n    });\n}\n/**\n * Check if user algorithm result is valid groupedDataObject.\n * @private\n */\nfunction seriesIsValidGroupedDataObject(groupedData) {\n    let result = false;\n    if (!isObject(groupedData)) {\n        return false;\n    }\n    MarkerClusterScatter_objectEach(groupedData, (elem) => {\n        result = true;\n        if (!isArray(elem) || !elem.length) {\n            result = false;\n            return;\n        }\n        for (let i = 0; i < elem.length; i++) {\n            if (!isObject(elem[i]) || (!elem[i].x || !elem[i].y)) {\n                result = false;\n                return;\n            }\n        }\n    });\n    return result;\n}\n/** @private */\nfunction seriesPreventClusterCollisions(props) {\n    const series = this, [gridY, gridX] = props.key.split(':').map(parseFloat), gridSize = props.gridSize, groupedData = props.groupedData, defaultRadius = props.defaultRadius, clusterRadius = props.clusterRadius, gridXPx = gridX * gridSize, gridYPx = gridY * gridSize, propsPx = valuesToPixels(series, props), gridsToCheckCollision = [], clusterMarkerOptions = series.options.cluster?.marker, zoneOptions = series.options.cluster?.zones, gridOffset = series.getGridOffset();\n    let xPixel = propsPx.x, yPixel = propsPx.y, pointsLen = 0, radius = 0, nextXPixel, nextYPixel, signX, signY, cornerGridX, cornerGridY, j, itemX, itemY, nextClusterPos, maxDist, keys;\n    // Distance to the grid start.\n    xPixel -= gridOffset.plotLeft;\n    yPixel -= gridOffset.plotTop;\n    for (let i = 1; i < 5; i++) {\n        signX = i % 2 ? -1 : 1;\n        signY = i < 3 ? -1 : 1;\n        cornerGridX = Math.floor((xPixel + signX * clusterRadius) / gridSize);\n        cornerGridY = Math.floor((yPixel + signY * clusterRadius) / gridSize);\n        keys = [\n            cornerGridY + ':' + cornerGridX,\n            cornerGridY + ':' + gridX,\n            gridY + ':' + cornerGridX\n        ];\n        for (j = 0; j < keys.length; j++) {\n            if (gridsToCheckCollision.indexOf(keys[j]) === -1 &&\n                keys[j] !== props.key) {\n                gridsToCheckCollision.push(keys[j]);\n            }\n        }\n    }\n    for (const item of gridsToCheckCollision) {\n        if (groupedData[item]) {\n            // Cluster or noise position is already computed.\n            if (!groupedData[item].posX) {\n                nextClusterPos = getClusterPosition(groupedData[item]);\n                groupedData[item].posX = nextClusterPos.x;\n                groupedData[item].posY = nextClusterPos.y;\n            }\n            const pos = valuesToPixels(series, {\n                x: groupedData[item].posX || 0,\n                y: groupedData[item].posY || 0\n            });\n            nextXPixel = pos.x - gridOffset.plotLeft;\n            nextYPixel = pos.y - gridOffset.plotTop;\n            [itemY, itemX] = item.split(':').map(parseFloat);\n            if (zoneOptions) {\n                pointsLen = groupedData[item].length;\n                for (let i = 0; i < zoneOptions.length; i++) {\n                    if (pointsLen >= zoneOptions[i].from &&\n                        pointsLen <= zoneOptions[i].to) {\n                        if (defined(zoneOptions[i].marker?.radius)) {\n                            radius = zoneOptions[i].marker.radius || 0;\n                        }\n                        else if (clusterMarkerOptions?.radius) {\n                            radius = clusterMarkerOptions.radius;\n                        }\n                        else {\n                            radius = clusterDefaults.marker.radius;\n                        }\n                    }\n                }\n            }\n            if (groupedData[item].length > 1 &&\n                radius === 0 &&\n                clusterMarkerOptions?.radius) {\n                radius = clusterMarkerOptions.radius;\n            }\n            else if (groupedData[item].length === 1) {\n                radius = defaultRadius;\n            }\n            maxDist = clusterRadius + radius;\n            radius = 0;\n            if (itemX !== gridX &&\n                Math.abs(xPixel - nextXPixel) < maxDist) {\n                xPixel = itemX - gridX < 0 ? gridXPx + clusterRadius :\n                    gridXPx + gridSize - clusterRadius;\n            }\n            if (itemY !== gridY &&\n                Math.abs(yPixel - nextYPixel) < maxDist) {\n                yPixel = itemY - gridY < 0 ? gridYPx + clusterRadius :\n                    gridYPx + gridSize - clusterRadius;\n            }\n        }\n    }\n    const pos = pixelsToValues(series, {\n        x: xPixel + gridOffset.plotLeft,\n        y: yPixel + gridOffset.plotTop\n    });\n    groupedData[props.key].posX = pos.x;\n    groupedData[props.key].posY = pos.y;\n    return pos;\n}\n/**\n * Util function.\n * @private\n */\nfunction valuesToPixels(series, pos) {\n    const { chart, xAxis, yAxis } = series;\n    if (chart.mapView) {\n        return chart.mapView.projectedUnitsToPixels(pos);\n    }\n    return {\n        x: xAxis ? xAxis.toPixels(pos.x) : 0,\n        y: yAxis ? yAxis.toPixels(pos.y) : 0\n    };\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst MarkerClusterScatter = {\n    compose\n};\n/* harmony default export */ const MarkerClusters_MarkerClusterScatter = (MarkerClusterScatter);\n\n;// ./code/es-modules/Extensions/MarkerClusters/MarkerClusters.js\n/* *\n *\n *  Marker clusters module.\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  Author: Wojciech Chmiel\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { animObject: MarkerClusters_animObject } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { defaultOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\n\nconst { addEvent: MarkerClusters_addEvent, defined: MarkerClusters_defined, error: MarkerClusters_error, isFunction: MarkerClusters_isFunction, merge: MarkerClusters_merge, pushUnique, syncTimeout: MarkerClusters_syncTimeout } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\n(defaultOptions.plotOptions || {}).series = MarkerClusters_merge((defaultOptions.plotOptions || {}).series, MarkerClusters_MarkerClusterDefaults);\n/* *\n *\n *  Functions\n *\n * */\n/** @private */\nfunction MarkerClusters_compose(AxisClass, ChartClass, highchartsDefaultOptions, SeriesClass) {\n    if (pushUnique(composed, 'MarkerClusters')) {\n        const PointClass = SeriesClass.prototype.pointClass, { scatter: ScatterSeries } = SeriesClass.types;\n        MarkerClusters_addEvent(AxisClass, 'setExtremes', onAxisSetExtremes);\n        MarkerClusters_addEvent(ChartClass, 'render', onChartRender);\n        MarkerClusters_addEvent(PointClass, 'drillToCluster', MarkerClusters_onPointDrillToCluster);\n        MarkerClusters_addEvent(PointClass, 'update', onPointUpdate);\n        MarkerClusters_addEvent(SeriesClass, 'afterRender', onSeriesAfterRender);\n        if (ScatterSeries) {\n            MarkerClusters_MarkerClusterScatter\n                .compose(highchartsDefaultOptions, ScatterSeries);\n        }\n    }\n}\n/**\n * Destroy the old tooltip after zoom.\n * @private\n */\nfunction onAxisSetExtremes() {\n    const chart = this.chart;\n    let animationDuration = 0;\n    for (const series of chart.series) {\n        if (series.markerClusterInfo) {\n            animationDuration = (MarkerClusters_animObject((series.options.cluster || {}).animation).duration ||\n                0);\n        }\n    }\n    MarkerClusters_syncTimeout(() => {\n        if (chart.tooltip) {\n            chart.tooltip.destroy();\n        }\n    }, animationDuration);\n}\n/**\n * Handle animation.\n * @private\n */\nfunction onChartRender() {\n    const chart = this;\n    for (const series of (chart.series || [])) {\n        if (series.markerClusterInfo) {\n            const options = series.options.cluster, pointsState = (series.markerClusterInfo || {}).pointsState, oldState = (pointsState || {}).oldState;\n            if ((options || {}).animation &&\n                series.markerClusterInfo &&\n                (series.chart.pointer?.pinchDown || []).length === 0 &&\n                ((series.xAxis || {}).eventArgs || {}).trigger !== 'pan' &&\n                oldState &&\n                Object.keys(oldState).length) {\n                for (const cluster of series.markerClusterInfo.clusters) {\n                    series.animateClusterPoint(cluster);\n                }\n                for (const noise of series.markerClusterInfo.noise) {\n                    series.animateClusterPoint(noise);\n                }\n            }\n        }\n    }\n}\n/** @private */\nfunction MarkerClusters_onPointDrillToCluster(event) {\n    const point = event.point || event.target, series = point.series, clusterOptions = series.options.cluster, onDrillToCluster = ((clusterOptions || {}).events || {}).drillToCluster;\n    if (MarkerClusters_isFunction(onDrillToCluster)) {\n        onDrillToCluster.call(this, event);\n    }\n}\n/**\n * Override point prototype to throw a warning when trying to update\n * clustered point.\n * @private\n */\nfunction onPointUpdate() {\n    const point = this;\n    if (point.dataGroup) {\n        MarkerClusters_error('Highcharts marker-clusters module: ' +\n            'Running `Point.update` when point belongs to clustered series' +\n            ' is not supported.', false, point.series.chart);\n        return false;\n    }\n}\n/**\n * Add classes, change mouse cursor.\n * @private\n */\nfunction onSeriesAfterRender() {\n    const series = this, clusterZoomEnabled = (series.options.cluster || {}).drillToCluster;\n    if (series.markerClusterInfo && series.markerClusterInfo.clusters) {\n        for (const cluster of series.markerClusterInfo.clusters) {\n            if (cluster.point && cluster.point.graphic) {\n                cluster.point.graphic.addClass('highcharts-cluster-point');\n                // Change cursor to pointer when drillToCluster is enabled.\n                if (clusterZoomEnabled && cluster.point) {\n                    cluster.point.graphic.css({\n                        cursor: 'pointer'\n                    });\n                    if (cluster.point.dataLabel) {\n                        cluster.point.dataLabel.css({\n                            cursor: 'pointer'\n                        });\n                    }\n                }\n                if (MarkerClusters_defined(cluster.clusterZone)) {\n                    cluster.point.graphic.addClass(cluster.clusterZoneClassName ||\n                        'highcharts-cluster-zone-' +\n                            cluster.clusterZone.zoneIndex);\n                }\n            }\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst MarkerClusters = {\n    compose: MarkerClusters_compose\n};\n/* harmony default export */ const MarkerClusters_MarkerClusters = (MarkerClusters);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Function callback when a cluster is clicked.\n *\n * @callback Highcharts.MarkerClusterDrillCallbackFunction\n *\n * @param {Highcharts.Point} this\n *        The point where the event occurred.\n *\n * @param {Highcharts.PointClickEventObject} event\n *        Event arguments.\n */\n''; // Keeps doclets above in JS file\n\n;// ./code/es-modules/Extensions/MarkerClusters/MarkerClusterSymbols.js\n/* *\n *\n *  Marker clusters module.\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  Author: Wojciech Chmiel\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Variables\n *\n * */\nlet symbols;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Cluster symbol.\n * @private\n */\nfunction MarkerClusterSymbols_cluster(x, y, width, height) {\n    const w = width / 2, h = height / 2, outerWidth = 1, space = 1, inner = symbols.arc(x + w, y + h, w - space * 4, h - space * 4, {\n        start: Math.PI * 0.5,\n        end: Math.PI * 2.5,\n        open: false\n    }), outer1 = symbols.arc(x + w, y + h, w - space * 3, h - space * 3, {\n        start: Math.PI * 0.5,\n        end: Math.PI * 2.5,\n        innerR: w - outerWidth * 2,\n        open: false\n    }), outer2 = symbols.arc(x + w, y + h, w - space, h - space, {\n        start: Math.PI * 0.5,\n        end: Math.PI * 2.5,\n        innerR: w,\n        open: false\n    });\n    return outer2.concat(outer1, inner);\n}\n/**\n * @private\n */\nfunction MarkerClusterSymbols_compose(SVGRendererClass) {\n    symbols = SVGRendererClass.prototype.symbols;\n    symbols.cluster = MarkerClusterSymbols_cluster;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst MarkerClusterSymbols = {\n    compose: MarkerClusterSymbols_compose\n};\n/* harmony default export */ const MarkerClusters_MarkerClusterSymbols = (MarkerClusterSymbols);\n\n;// ./code/es-modules/masters/modules/marker-clusters.js\n\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nMarkerClusters_MarkerClusters.compose(G.Axis, G.Chart, G.defaultOptions, G.Series);\nMarkerClusters_MarkerClusterSymbols.compose(G.SVGRenderer);\n/* harmony default export */ const marker_clusters_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "symbols", "Column<PERSON><PERSON><PERSON>", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "marker_clusters_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "MarkerClusters_MarkerClusterDefaults", "cluster", "enabled", "allowOverlap", "animation", "duration", "drillToCluster", "minimumClusterSize", "layoutAlgorithm", "gridSize", "distance", "kmeansT<PERSON><PERSON>old", "marker", "symbol", "radius", "lineWidth", "lineColor", "dataLabels", "format", "verticalAlign", "align", "style", "color", "inside", "tooltip", "clusterFormat", "<PERSON><PERSON><PERSON><PERSON>", "column", "length", "as<PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "splice", "start", "deleteCount", "removedAsSubarray", "items", "from", "removed", "array", "<PERSON><PERSON><PERSON><PERSON>", "getPrototypeOf", "constructor", "result", "set", "subarray", "fireEvent", "objectEach", "<PERSON><PERSON><PERSON>", "Data_DataTableCore", "options", "autoId", "id", "columns", "modified", "rowCount", "versionTag", "columnName", "slice", "Math", "max", "applyRowCount", "deleteRows", "rowIndex", "getColumn", "asReference", "getColumns", "columnNames", "keys", "reduce", "getRow", "map", "setColumn", "eventDetail", "setColumns", "silent", "setRow", "row", "insert", "indexRowCount", "cellValue", "addColumns", "animObject", "clusterDefaults", "addEvent", "defined", "error", "isFunction", "isObject", "isNumber", "merge", "MarkerClusterScatter_objectEach", "<PERSON><PERSON><PERSON><PERSON>", "syncTimeout", "markerClusterAlgorithms", "grid", "dataX", "dataY", "dataIndexes", "x", "y", "gridX", "i", "gridOffset", "series", "getGridOffset", "scaledGridSize", "getScaledGridSize", "p", "valuesToPixels", "plotLeft", "plotTop", "floor", "gridY", "push", "dataIndex", "kmeans", "clusters", "noise", "group", "pointMaxDistance", "processedDistance", "iterations", "currentIteration", "repeat", "pointX", "pointY", "tempPos", "pointClusterDistance", "processedGridSize", "groupedData", "getClusterPosition", "posX", "posY", "oldX", "oldY", "startPointsLen", "points", "c", "getClusterDistancesFromPoint", "clusterIndex", "iEnd", "optimizedKmeans", "processedXData", "processedYData", "extremes", "getRealExtremes", "clusterMarkerOptions", "offset", "markerClusterInfo", "initMaxX", "maxX", "initMinX", "minX", "initMaxY", "maxY", "initMinY", "minY", "baseClusters", "dataPoint", "pointsOutside", "pointsInside", "data", "dataPointPx", "clusterPx", "sqrt", "pow", "clusterZone", "baseGeneratePoints", "oldPointsStateId", "stateIdCounter", "fadeInElement", "elem", "opacity", "attr", "animate", "fadeInNewPointAndDestoryOld", "newPointObj", "oldPoints", "fadeInStatePoint", "point", "destroy", "stateObj", "fadeinGraphic", "fadeinDataLabel", "graphic", "show", "dataLabel", "pointsLen", "sumX", "sumY", "getDataState", "clusteredData", "stateDataLen", "state", "for<PERSON>ach", "getStateId", "random", "toString", "substring", "hideStatePoint", "hideGraphic", "hideDataLabel", "hide", "onPointDrillToCluster", "event", "target", "firePointEvent", "e", "xAxis", "yAxis", "chart", "inverted", "mapView", "pointer", "sortedDataX", "sort", "b", "sortedDataY", "offsetX", "abs", "offsetY", "x1", "min", "x2", "y1", "y2", "fitToBounds", "x1Px", "toPixels", "x2Px", "y1Px", "y2Px", "zoomX", "zoomY", "transform", "width", "height", "pixelsToValues", "pos", "pixelsToProjectedUnits", "toValue", "seriesAnimateClusterPoint", "clusterObj", "animDuration", "pointsState", "newState", "oldState", "oldPointObj", "newPointBBox", "newX", "newY", "isOldPointGrahic", "isCbHandled", "newPos", "stateId", "parentsId", "plotX", "plotY", "getBBox", "isImg", "alignAttr", "seriesDestroyClusteredData", "markerClusterSeriesData", "seriesGeneratePoints", "oldPointsState", "oldDataLen", "oldMarkerClusterInfo", "cropDataOffsetX", "cropDataOffsetY", "seriesMinX", "seriesMaxX", "seriesMinY", "seriesMaxY", "type", "algorithm", "layoutAlgOptions", "xData", "yData", "clusterOptions", "realExtremes", "visibleXData", "visibleYData", "visibleDataIndexes", "is", "xy", "projectPoint", "polar", "plot<PERSON>id<PERSON>", "halfGrid", "p1", "p2", "dataMaxX", "dataMinX", "dataMaxY", "dataMinY", "getClusteredData", "dataTable", "groupedXData", "groupedYData", "hasGroupedData", "groupMap", "apply", "index", "isCluster", "clusterPointsAmount", "getPointsState", "hideClusteredData", "destroyClusteredData", "seriesGetClusterDistancesFromPoint", "seriesGetClusteredData", "pointUserOptions", "clusterPos", "pointOptions", "clusterTempPos", "zoneOptions", "clusterZoneClassName", "isValidGroupedDataObject", "k", "zones", "to", "zoneIndex", "className", "preventClusterCollisions", "defaultRadius", "clusterRadius", "parentStateId", "formatPrefix", "states", "userOptions", "seriesGetGridOffset", "reversed", "seriesGetPointsState", "dataLength", "oldDataStateArr", "newDataStateArr", "indexOf", "seriesGetRealExtremes", "plotHeight", "realMinX", "realMaxX", "realMinY", "realMaxY", "seriesGetScaledGridSize", "search", "divider", "gridValueSize", "getScale", "scale", "toFixed", "level", "seriesHideClusteredData", "clusteredSeriesData", "oldPointsId", "seriesIsValidGroupedDataObject", "seriesPreventClusterCollisions", "props", "split", "parseFloat", "gridXPx", "gridYPx", "propsPx", "gridsToCheckCollision", "xPixel", "yPixel", "nextXPixel", "nextYPixel", "signX", "signY", "cornerGridX", "cornerGridY", "j", "itemX", "itemY", "nextClusterPos", "maxDist", "item", "projectedUnitsToPixels", "MarkerClusters_MarkerClusterScatter", "compose", "highchartsDefaultOptions", "ScatterSeriesClass", "scatterProto", "generatePoints", "animateClusterPoint", "plotOptions", "MarkerClusters_animObject", "defaultOptions", "composed", "MarkerClusters_addEvent", "MarkerClusters_defined", "MarkerClusters_error", "MarkerClusters_isFunction", "MarkerClusters_merge", "pushUnique", "MarkerClusters_syncTimeout", "onAxisSetExtremes", "animationDuration", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pinchDown", "eventArgs", "trigger", "MarkerClusters_onPointDrillToCluster", "onDrillToCluster", "events", "onPointUpdate", "dataGroup", "onSeriesAfterRender", "clusterZoomEnabled", "addClass", "css", "cursor", "MarkerClusterSymbols_cluster", "w", "h", "inner", "arc", "space", "PI", "end", "open", "outer1", "innerR", "outerWidth", "outer2", "concat", "G", "MarkerClusters_MarkerClusters", "AxisClass", "ChartClass", "SeriesClass", "PointClass", "pointClass", "scatter", "ScatterSeries", "types", "Axis", "Chart", "Series", "MarkerClusters_MarkerClusterSymbols", "SVGRendererClass", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "CAWA,AAXA;;;;;;;;;;CAUC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,EACrC,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,qCAAsC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAM,GACvG,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,qCAAqC,CAAGD,EAAQD,EAAK,WAAc,EAE3EA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CACjD,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,AAACC,GACnC,AAAC,CAAA,KACP,iBAklENC,EAjlEM,IAgbNC,EAhbUC,EAAuB,CAE/B,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAab,OAAO,CAG5B,IAAIC,EAASS,CAAwB,CAACE,EAAS,CAAG,CAGjDZ,QAAS,CAAC,CACX,EAMA,OAHAS,CAAmB,CAACG,EAAS,CAACX,EAAQA,EAAOD,OAAO,CAAEW,GAG/CV,EAAOD,OAAO,AACtB,CAMCW,EAAoBI,CAAC,CAAG,AAACd,IACxB,IAAIe,EAASf,GAAUA,EAAOgB,UAAU,CACvC,IAAOhB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAU,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAAClB,EAASoB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACtB,EAASqB,IAC5EE,OAAOC,cAAc,CAACxB,EAASqB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,EAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAkVxF,IAAME,EAJL,CAC1BC,QAhSY,CASZC,QAAS,CAAA,EAQTC,aAAc,CAAA,EAMdC,UAAW,CAEPC,SAAU,GACd,EAIAC,eAAgB,CAAA,EAQhBC,mBAAoB,EAMpBC,gBAAiB,CAqFbC,SAAU,GAkBVC,SAAU,GAOVC,gBAAiB,GACrB,EAOAC,OAAQ,CAEJC,OAAQ,UAERC,OAAQ,GAERC,UAAW,EAEXC,UAAW,SACf,EAwEAC,WAAY,CAERf,QAAS,CAAA,EAETgB,OAAQ,8BAERC,cAAe,SAEfC,MAAO,SAEPC,MAAO,CACHC,MAAO,UACX,EAEAC,OAAQ,CAAA,CACZ,CACJ,EA8BIC,QA7BY,CAmBZC,cAAe,iEAEnB,CASA,GAqBA,AAAC,SAAUrD,CAAW,EAqClBA,EAAYsD,SAAS,CAPrB,SAAmBC,CAAM,CAAEC,CAAM,CAAEC,CAAU,SACzC,AAAIC,MAAMC,OAAO,CAACJ,IACdA,EAAOC,MAAM,CAAGA,EACTD,GAEJA,CAAM,CAACE,EAAa,WAAa,QAAQ,CAAC,EAAGD,EACxD,EAoDAxD,EAAY4D,MAAM,CAvBlB,SAAgBL,CAAM,CAAEM,CAAK,CAAEC,CAAW,CAAEC,CAAiB,CAAEC,EAAQ,EAAE,EACrE,GAAIN,MAAMC,OAAO,CAACJ,GAId,OAHI,AAACG,MAAMC,OAAO,CAACK,IACfA,CAAAA,EAAQN,MAAMO,IAAI,CAACD,EAAK,EAErB,CACHE,QAASX,EAAOK,MAAM,CAACC,EAAOC,KAAgBE,GAC9CG,MAAOZ,CACX,EAEJ,IAAMa,EAAcrD,OAAOsD,cAAc,CAACd,GACrCe,WAAW,CACVJ,EAAUX,CAAM,CAACQ,EAAoB,WAAa,QAAQ,CAACF,EAAOA,EAAQC,GAE1ES,EAAS,IAAIH,EADDb,EAAOC,MAAM,CAAGM,EAAcE,EAAMR,MAAM,EAK5D,OAHAe,EAAOC,GAAG,CAACjB,EAAOkB,QAAQ,CAAC,EAAGZ,GAAQ,GACtCU,EAAOC,GAAG,CAACR,EAAOH,GAClBU,EAAOC,GAAG,CAACjB,EAAOkB,QAAQ,CAACZ,EAAQC,GAAcD,EAAQG,EAAMR,MAAM,EAC9D,CACHU,QAASA,EACTC,MAAOI,CACX,CACJ,CAEJ,EAAGvE,GAAgBA,CAAAA,EAAc,CAAC,CAAA,GAyBlC,GAAM,CAAEsD,UAAAA,CAAS,CAAEM,OAAAA,CAAM,CAAE,CAnB4B5D,EAqBjD,CAAE0E,UAAAA,CAAS,CAAEC,WAAAA,CAAU,CAAEC,UAAAA,CAAS,CAAE,CAAIjD,IA+PXkD,EA9OnC,MAiBIP,YAAYQ,EAAU,CAAC,CAAC,CAAE,CAOtB,IAAI,CAACC,MAAM,CAAG,CAACD,EAAQE,EAAE,CACzB,IAAI,CAACC,OAAO,CAAG,CAAC,EAOhB,IAAI,CAACD,EAAE,CAAIF,EAAQE,EAAE,EAAIJ,IACzB,IAAI,CAACM,QAAQ,CAAG,IAAI,CACpB,IAAI,CAACC,QAAQ,CAAG,EAChB,IAAI,CAACC,UAAU,CAAGR,IAClB,IAAIO,EAAW,EACfR,EAAWG,EAAQG,OAAO,EAAI,CAAC,EAAG,CAAC1B,EAAQ8B,KACvC,IAAI,CAACJ,OAAO,CAACI,EAAW,CAAG9B,EAAO+B,KAAK,GACvCH,EAAWI,KAAKC,GAAG,CAACL,EAAU5B,EAAOC,MAAM,CAC/C,GACA,IAAI,CAACiC,aAAa,CAACN,EACvB,CAaAM,cAAcN,CAAQ,CAAE,CACpB,IAAI,CAACA,QAAQ,CAAGA,EAChBR,EAAW,IAAI,CAACM,OAAO,CAAE,CAAC1B,EAAQ8B,KAC1B9B,EAAOC,MAAM,GAAK2B,GAClB,CAAA,IAAI,CAACF,OAAO,CAACI,EAAW,CAAG/B,EAAUC,EAAQ4B,EAAQ,CAE7D,EACJ,CAeAO,WAAWC,CAAQ,CAAER,EAAW,CAAC,CAAE,CAC/B,GAAIA,EAAW,GAAKQ,EAAW,IAAI,CAACR,QAAQ,CAAE,CAC1C,IAAI3B,EAAS,EACbmB,EAAW,IAAI,CAACM,OAAO,CAAE,CAAC1B,EAAQ8B,KAC9B,IAAI,CAACJ,OAAO,CAACI,EAAW,CACpBzB,EAAOL,EAAQoC,EAAUR,GAAUhB,KAAK,CAC5CX,EAASD,EAAOC,MAAM,AAC1B,GACA,IAAI,CAAC2B,QAAQ,CAAG3B,CACpB,CACAkB,EAAU,IAAI,CAAE,kBAAmB,CAAEiB,SAAAA,EAAUR,SAAAA,CAAS,GACxD,IAAI,CAACC,UAAU,CAAGR,GACtB,CAWAgB,UAAUP,CAAU,CAEpBQ,CAAW,CAAE,CACT,OAAO,IAAI,CAACZ,OAAO,CAACI,EAAW,AACnC,CAYAS,WAAWC,CAAW,CAEtBF,CAAW,CAAE,CACT,MAAO,AAACE,CAAAA,GAAehF,OAAOiF,IAAI,CAAC,IAAI,CAACf,OAAO,CAAA,EAAGgB,MAAM,CAAC,CAAChB,EAASI,KAC/DJ,CAAO,CAACI,EAAW,CAAG,IAAI,CAACJ,OAAO,CAACI,EAAW,CACvCJ,GACR,CAAC,EACR,CAaAiB,OAAOP,CAAQ,CAAEI,CAAW,CAAE,CAC1B,MAAO,AAACA,CAAAA,GAAehF,OAAOiF,IAAI,CAAC,IAAI,CAACf,OAAO,CAAA,EAAGkB,GAAG,CAAC,AAACtF,GAAQ,IAAI,CAACoE,OAAO,CAACpE,EAAI,EAAE,CAAC8E,EAAS,CAChG,CAmBAS,UAAUf,CAAU,CAAE9B,EAAS,EAAE,CAAEoC,EAAW,CAAC,CAAEU,CAAW,CAAE,CAC1D,IAAI,CAACC,UAAU,CAAC,CAAE,CAACjB,EAAW,CAAE9B,CAAO,EAAGoC,EAAUU,EACxD,CAmBAC,WAAWrB,CAAO,CAAEU,CAAQ,CAAEU,CAAW,CAAE,CACvC,IAAIlB,EAAW,IAAI,CAACA,QAAQ,CAC5BR,EAAWM,EAAS,CAAC1B,EAAQ8B,KACzB,IAAI,CAACJ,OAAO,CAACI,EAAW,CAAG9B,EAAO+B,KAAK,GACvCH,EAAW5B,EAAOC,MAAM,AAC5B,GACA,IAAI,CAACiC,aAAa,CAACN,GACdkB,GAAaE,SACd7B,EAAU,IAAI,CAAE,mBAChB,IAAI,CAACU,UAAU,CAAGR,IAE1B,CAoBA4B,OAAOC,CAAG,CAAEd,EAAW,IAAI,CAACR,QAAQ,CAAEuB,CAAM,CAAEL,CAAW,CAAE,CACvD,GAAM,CAAEpB,QAAAA,CAAO,CAAE,CAAG,IAAI,CAAE0B,EAAgBD,EAAS,IAAI,CAACvB,QAAQ,CAAG,EAAIQ,EAAW,EAClFhB,EAAW8B,EAAK,CAACG,EAAWvB,KACxB,IAAI9B,EAAS0B,CAAO,CAACI,EAAW,EAC5BgB,GAAaQ,aAAe,CAAA,GAAS,AAAInD,MAAMiD,GAC/CpD,IACImD,EACAnD,EAASK,EAAOL,EAAQoC,EAAU,EAAG,CAAA,EAAM,CAACiB,EAAU,EAAEzC,KAAK,CAG7DZ,CAAM,CAACoC,EAAS,CAAGiB,EAEvB3B,CAAO,CAACI,EAAW,CAAG9B,EAE9B,GACIoD,EAAgB,IAAI,CAACxB,QAAQ,EAC7B,IAAI,CAACM,aAAa,CAACkB,GAElBN,GAAaE,SACd7B,EAAU,IAAI,CAAE,gBAChB,IAAI,CAACU,UAAU,CAAGR,IAE1B,CACJ,EAyDM,CAAEkC,WAAAA,CAAU,CAAE,CAAInF,IAGlB,CAAEE,QAASkF,CAAe,CAAE,CAAGnF,EAE/B,CAAEoF,SAAAA,CAAQ,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAEvD,QAAAA,CAAO,CAAEwD,WAAAA,CAAU,CAAEC,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAE3C,WAAY4C,CAA+B,CAAEC,eAAAA,CAAc,CAAEC,YAAAA,CAAW,CAAE,CAAI9F,IAM1J+F,EAA0B,CAC5BC,KAAM,SAAUC,CAAK,CAAEC,CAAK,CAAEC,CAAW,CAAEhD,CAAO,EAC9C,IACIiD,EAAGC,EAAGC,EAAcpH,EAAKqH,EADRP,EAAO,CAAC,EAAGQ,EAAaC,AAA9B,IAAI,CAAiCC,aAAa,GAAIC,EAAiBF,AAAvE,IAAI,CAA0EG,iBAAiB,CAACzD,GAE/G,IAAKoD,EAAI,EAAGA,EAAIN,EAAMpE,MAAM,CAAE0E,IAAK,CAC/B,IAAMM,EAAIC,GAHC,IAAI,CAGkB,CAAEV,EAAGH,CAAK,CAACM,EAAE,CAAEF,EAAGH,CAAK,CAACK,EAAE,AAAC,GAC5DH,EAAIS,EAAET,CAAC,CAAGI,EAAWO,QAAQ,CAC7BV,EAAIQ,EAAER,CAAC,CAAGG,EAAWQ,OAAO,CAC5BV,EAAQ1C,KAAKqD,KAAK,CAACb,EAAIO,GAGvBX,CAAI,CADJ9G,EAAMgI,AADEtD,KAAKqD,KAAK,CAACZ,EAAIM,GACT,IAAML,EACX,EAAKN,CAAAA,CAAI,CAAC9G,EAAI,CAAG,EAAE,AAAD,EAC3B8G,CAAI,CAAC9G,EAAI,CAACiI,IAAI,CAAC,CACXC,UAAWjB,CAAW,CAACI,EAAE,CACzBH,EAAGH,CAAK,CAACM,EAAE,CACXF,EAAGH,CAAK,CAACK,EAAE,AACf,EACJ,CACA,OAAOP,CACX,EACAqB,OAAQ,SAAUpB,CAAK,CAAEC,CAAK,CAAEC,CAAW,CAAEhD,CAAO,EAChD,IAAqBmE,EAAW,EAAE,CAAEC,EAAQ,EAAE,CAAEC,EAAQ,CAAC,EAAGC,EAAmBtE,EAAQuE,iBAAiB,EACpGtC,EAAgB3E,eAAe,CAACE,QAAQ,CAAEgH,EAAaxE,EAAQwE,UAAU,CAGzEC,EAAmB,EAAGC,EAAS,CAAA,EAAMC,EAAS,EAAGC,EAAS,EAAGC,EAASC,EAAuB,EAAE,AACnG9E,CAAAA,EAAQ+E,iBAAiB,CAAG/E,EAAQuE,iBAAiB,CAErD,IAAMS,EAAc1B,AAPL,IAAI,CAOQV,uBAAuB,CAC9CU,AARW,IAAI,CAQRV,uBAAuB,CAACC,IAAI,CAACpG,IAAI,CAR7B,IAAI,CAQkCqG,EAAOC,EAAOC,EAAahD,GAAW,CAAC,EAG5F,IAAK,IAAMjE,KAAOiJ,EACVA,CAAW,CAACjJ,EAAI,CAAC2C,MAAM,CAAG,IAC1BmG,EAAUI,EAAmBD,CAAW,CAACjJ,EAAI,EAC7CoI,EAASH,IAAI,CAAC,CACVkB,KAAML,EAAQ5B,CAAC,CACfkC,KAAMN,EAAQ3B,CAAC,CACfkC,KAAM,EACNC,KAAM,EACNC,eAAgBN,CAAW,CAACjJ,EAAI,CAAC2C,MAAM,CACvC6G,OAAQ,EAAE,AACd,IAIR,KAAOb,GAAQ,CACX,IAAK,IAAMc,KAAKrB,EACZqB,EAAED,MAAM,CAAC7G,MAAM,CAAG,CAEtB0F,CAAAA,EAAM1F,MAAM,CAAG,EACf,IAAK,IAAI0E,EAAI,EAAGA,EAAIN,EAAMpE,MAAM,CAAE0E,IAC9BuB,EAAS7B,CAAK,CAACM,EAAE,CACjBwB,EAAS7B,CAAK,CAACK,EAAE,CAEb0B,AADJA,CAAAA,EAAuBxB,AAjChB,IAAI,CAiCmBmC,4BAA4B,CAACtB,EAAUQ,EAAQC,EAAM,EAC1DlG,MAAM,EAC3BoG,CAAoB,CAAC,EAAE,CAACtH,QAAQ,CAAG8G,EACnCH,CAAQ,CAACW,CAAoB,CAAC,EAAE,CAACY,YAAY,CAAC,CAACH,MAAM,CAACvB,IAAI,CAAC,CACvDf,EAAG0B,EACHzB,EAAG0B,EACHX,UAAWjB,CAAW,CAACI,EAAE,AAC7B,GAGAgB,EAAMJ,IAAI,CAAC,CACPf,EAAG0B,EACHzB,EAAG0B,EACHX,UAAWjB,CAAW,CAACI,EAAE,AAC7B,GAKR,IAAK,IAAIA,EAAI,EAAGA,EAAIe,EAASzF,MAAM,CAAE0E,IACC,IAA9Be,CAAQ,CAACf,EAAE,CAACmC,MAAM,CAAC7G,MAAM,EAErBoG,AADJA,CAAAA,EAAuBxB,AAtDpB,IAAI,CAsDuBmC,4BAA4B,CAACtB,EAAUA,CAAQ,CAACf,EAAE,CAACmC,MAAM,CAAC,EAAE,CAACtC,CAAC,CAAEkB,CAAQ,CAACf,EAAE,CAACmC,MAAM,CAAC,EAAE,CAACrC,CAAC,CAAA,CAC7F,CAAC,EAAE,CAAC1F,QAAQ,CAAG8G,IAEnCH,CAAQ,CAACW,CAAoB,CAAC,EAAE,CAACY,YAAY,CAAC,CAACH,MAAM,CAChDvB,IAAI,CAACG,CAAQ,CAACf,EAAE,CAACmC,MAAM,CAAC,EAAE,EAE/BpB,CAAQ,CAACW,CAAoB,CAAC,EAAE,CAACY,YAAY,CAAC,CACzCH,MAAM,CAAC7G,MAAM,CAAG,GAMjCgG,EAAS,CAAA,EACT,IAAK,IAAItB,EAAI,EAAGA,EAAIe,EAASzF,MAAM,CAAE0E,IACjCyB,EAAUI,EAAmBd,CAAQ,CAACf,EAAE,CAACmC,MAAM,EAC/CpB,CAAQ,CAACf,EAAE,CAACgC,IAAI,CAAGjB,CAAQ,CAACf,EAAE,CAAC8B,IAAI,CACnCf,CAAQ,CAACf,EAAE,CAACiC,IAAI,CAAGlB,CAAQ,CAACf,EAAE,CAAC+B,IAAI,CACnChB,CAAQ,CAACf,EAAE,CAAC8B,IAAI,CAAGL,EAAQ5B,CAAC,CAC5BkB,CAAQ,CAACf,EAAE,CAAC+B,IAAI,CAAGN,EAAQ3B,CAAC,CAGxBiB,CAAAA,CAAQ,CAACf,EAAE,CAAC8B,IAAI,CAAGf,CAAQ,CAACf,EAAE,CAACgC,IAAI,CAzE7B,GA0ENjB,CAAQ,CAACf,EAAE,CAAC8B,IAAI,CAAGf,CAAQ,CAACf,EAAE,CAACgC,IAAI,CA1E7B,GA2ENjB,CAAQ,CAACf,EAAE,CAAC+B,IAAI,CAAGhB,CAAQ,CAACf,EAAE,CAACiC,IAAI,CA3E7B,GA4ENlB,CAAQ,CAACf,EAAE,CAAC+B,IAAI,CAAGhB,CAAQ,CAACf,EAAE,CAACiC,IAAI,CA5E7B,CA4E8C,GACpDX,CAAAA,EAAS,CAAA,CAAG,CAKhBF,CAAAA,GACAE,CAAAA,EAASD,EAAmBD,EAAa,CAAA,EAE7CC,GACJ,CACA,IAAK,IAAIrB,EAAI,EAAGuC,EAAOxB,EAASzF,MAAM,CAAE0E,EAAIuC,EAAM,EAAEvC,EAChDiB,CAAK,CAAC,UAAYjB,EAAE,CAAGe,CAAQ,CAACf,EAAE,CAACmC,MAAM,CAE7C,IAAK,IAAInC,EAAI,EAAGuC,EAAOvB,EAAM1F,MAAM,CAAE0E,EAAIuC,EAAM,EAAEvC,EAC7CiB,CAAK,CAAC,QAAUjB,EAAE,CAAG,CAACgB,CAAK,CAAChB,EAAE,CAAC,CAEnC,OAAOiB,CACX,EACAuB,gBAAiB,SAAUC,CAAc,CAAEC,CAAc,CAAE9C,CAAW,CAAEhD,CAAO,EAC3E,IAAqBsE,EAAmBtE,EAAQuE,iBAAiB,EAC7DtC,EAAgB3E,eAAe,CAACC,QAAQ,CAAEwI,EAAWzC,AAD1C,IAAI,CAC6C0C,eAAe,GAAIC,EAAuB,AAAC3C,CAAAA,AAD5F,IAAI,CAC+FtD,OAAO,CAACjD,OAAO,EAAI,CAAC,CAAA,EAAGW,MAAM,CAC3IF,EAAU6G,EAAQ,CAAC,EAAG6B,EAAQtI,EAClC,GAAI,CAAC0F,AAHU,IAAI,CAGP6C,iBAAiB,EAAK7C,AAHnB,IAAI,CAGsB8C,QAAQ,EAAI9C,AAHtC,IAAI,CAGyC8C,QAAQ,CAAGL,EAASM,IAAI,EAChF/C,AAJW,IAAI,CAIRgD,QAAQ,EAAIhD,AAJR,IAAI,CAIWgD,QAAQ,CAAGP,EAASQ,IAAI,EAClDjD,AALW,IAAI,CAKRkD,QAAQ,EAAIlD,AALR,IAAI,CAKWkD,QAAQ,CAAGT,EAASU,IAAI,EAClDnD,AANW,IAAI,CAMRoD,QAAQ,EAAIpD,AANR,IAAI,CAMWoD,QAAQ,CAAGX,EAASY,IAAI,CAClDrD,AAPW,IAAI,CAOR8C,QAAQ,CAAGL,EAASM,IAAI,CAC/B/C,AARW,IAAI,CAQRgD,QAAQ,CAAGP,EAASQ,IAAI,CAC/BjD,AATW,IAAI,CASRkD,QAAQ,CAAGT,EAASU,IAAI,CAC/BnD,AAVW,IAAI,CAURoD,QAAQ,CAAGX,EAASY,IAAI,CAC/BtC,EAAQf,AAXG,IAAI,CAWAV,uBAAuB,CAClCU,AAZO,IAAI,CAYJV,uBAAuB,CAACsB,MAAM,CAACzH,IAAI,CAZnC,IAAI,CAYwCoJ,EAAgBC,EAAgB9C,EAAahD,GAAW,CAAC,EAChHsD,AAbW,IAAI,CAaRsD,YAAY,CAAG,SAErB,CAKD,IAAK,IAAM7J,KAJXuG,AAhBW,IAAI,CAgBRsD,YAAY,EAAKtD,CAAAA,AAhBb,IAAI,CAgBgBsD,YAAY,CAAG,CAC1CzC,SAAUb,AAjBH,IAAI,CAiBM6C,iBAAiB,CAAChC,QAAQ,CAC3CC,MAAOd,AAlBA,IAAI,CAkBG6C,iBAAiB,CAAC/B,KAAK,AACzC,CAAA,EACsBd,AApBX,IAAI,CAoBcsD,YAAY,CAACzC,QAAQ,EAAE,CAGhD,IAAK,IAAM0C,KAFX9J,EAAQ+J,aAAa,CAAG,EAAE,CAC1B/J,EAAQgK,YAAY,CAAG,EAAE,CACDhK,EAAQiK,IAAI,EAAE,CAClC,IAAMC,EAActD,GAxBjB,IAAI,CAwBoCkD,GAAYK,EAAYvD,GAxBhE,IAAI,CAwBmF5G,GAC1FS,EAAWiD,KAAK0G,IAAI,CAAC1G,KAAK2G,GAAG,CAACH,EAAYhE,CAAC,CAAGiE,EAAUjE,CAAC,CAAE,GACvDxC,KAAK2G,GAAG,CAACH,EAAY/D,CAAC,CAAGgE,EAAUhE,CAAC,CAAE,IAU1CgD,EAAS5B,GARL1G,EADAb,EAAQsK,WAAW,EAAE3J,QAAQE,OACpBb,EAAQsK,WAAW,CAAC3J,MAAM,CAACE,MAAM,CAErCqI,GAAsBrI,OAClBqI,EAAqBrI,MAAM,CAG3BqE,EAAgBvE,MAAM,CAACE,MAAM,GAEJ,EAClC0G,EAAmB1G,EAASA,EAC5BJ,EAAWI,EAASsI,GACpB/D,EAAQpF,EAAQ+J,aAAa,EAC7B/J,EAAQ+J,aAAa,CAAC9C,IAAI,CAAC6C,GAEtB1E,EAAQpF,EAAQgK,YAAY,GACjChK,EAAQgK,YAAY,CAAC/C,IAAI,CAAC6C,EAElC,CACI9J,EAAQgK,YAAY,CAACrI,MAAM,EAC3B2F,CAAAA,CAAK,CAACtH,EAAQmD,EAAE,CAAC,CAAGnD,EAAQgK,YAAY,AAAD,EAE3C,IAAI3D,EAAI,EACR,IAAK,IAAMM,KAAK3G,EAAQ+J,aAAa,CACjCzC,CAAK,CAACtH,EAAQmD,EAAE,CAAG,SAAWkD,IAAI,CAAG,CAACM,EAAE,AAEhD,CACA,IAAK,IAAMU,KAASd,AAtDT,IAAI,CAsDYsD,YAAY,CAACxC,KAAK,CACzCC,CAAK,CAACD,EAAMlE,EAAE,CAAC,CAAGkE,EAAM4C,IAAI,AAEpC,CACA,OAAO3C,CACX,CACJ,EAMIiD,EAMJC,EAAmB,EAAE,CAAEC,EAAiB,EA6CxC,SAASC,EAAcC,CAAI,CAAEC,CAAO,CAAEzK,CAAS,EAC3CwK,EAAKE,IAAI,CAAC,CAAED,QAAAA,CAAQ,GAAGE,OAAO,CAAC,CAAEF,QAAS,CAAE,EAAGzK,EACnD,CAKA,SAAS4K,EAA4BC,CAAW,CAAEC,CAAS,CAAE9K,CAAS,CAAEyK,CAAO,EAI3E,IAAK,IAAMjE,KAFXuE,EAAiBF,EAAaJ,EAASzK,EAAW,CAAA,EAAM,CAAA,GAExC8K,GACZtE,EAAEwE,KAAK,EAAEC,WAEjB,CAKA,SAASF,EAAiBG,CAAQ,CAAET,CAAO,CAAEzK,CAAS,CAAEmL,CAAa,CAAEC,CAAe,EAC9EF,EAASF,KAAK,GACVG,GAAiBD,EAASF,KAAK,CAACK,OAAO,GACvCH,EAASF,KAAK,CAACK,OAAO,CAACC,IAAI,GAC3Bf,EAAcW,EAASF,KAAK,CAACK,OAAO,CAAEZ,EAASzK,IAE/CoL,GAAmBF,EAASF,KAAK,CAACO,SAAS,GAC3CL,EAASF,KAAK,CAACO,SAAS,CAACD,IAAI,GAC7Bf,EAAcW,EAASF,KAAK,CAACO,SAAS,CAAEd,EAASzK,IAG7D,CAKA,SAAS+H,EAAmBM,CAAM,EAC9B,IAAMmD,EAAYnD,EAAO7G,MAAM,CAC3BiK,EAAO,EAAGC,EAAO,EACrB,IAAK,IAAIxF,EAAI,EAAGA,EAAIsF,EAAWtF,IAC3BuF,GAAQpD,CAAM,CAACnC,EAAE,CAACH,CAAC,CACnB2F,GAAQrD,CAAM,CAACnC,EAAE,CAACF,CAAC,CAEvB,MAAO,CACHD,EAAG0F,EAAOD,EACVxF,EAAG0F,EAAOF,CACd,CACJ,CAMA,SAASG,EAAaC,CAAa,CAAEC,CAAY,EAC7C,IAAMC,EAAQ,EAAE,CAUhB,OATAA,EAAMtK,MAAM,CAAGqK,EACfD,EAAc3E,QAAQ,CAAC8E,OAAO,CAAC,SAAUlM,CAAO,EAC5CA,EAAQiK,IAAI,CAACiC,OAAO,CAAC,SAAUvB,CAAI,EAC/BsB,CAAK,CAACtB,EAAKzD,SAAS,CAAC,CAAGyD,CAC5B,EACJ,GACAoB,EAAc1E,KAAK,CAAC6E,OAAO,CAAC,SAAU7E,CAAK,EACvC4E,CAAK,CAAC5E,EAAM4C,IAAI,CAAC,EAAE,CAAC/C,SAAS,CAAC,CAAGG,EAAM4C,IAAI,CAAC,EAAE,AAClD,GACOgC,CACX,CAKA,SAASE,IACL,OAAOzI,KAAK0I,MAAM,GAAGC,QAAQ,CAAC,IAAIC,SAAS,CAAC,EAAG,GAAK,IAAM7B,GAC9D,CAKA,SAAS8B,EAAelB,CAAQ,CAAEmB,CAAW,CAAEC,CAAa,EACpDpB,EAASF,KAAK,GACVqB,GAAenB,EAASF,KAAK,CAACK,OAAO,EACrCH,EAASF,KAAK,CAACK,OAAO,CAACkB,IAAI,GAE3BD,GAAiBpB,EAASF,KAAK,CAACO,SAAS,EACzCL,EAASF,KAAK,CAACO,SAAS,CAACgB,IAAI,GAGzC,CAEA,SAASC,EAAsBC,CAAK,EAEhCzB,AADcyB,CAAAA,EAAMzB,KAAK,EAAIyB,EAAMC,MAAM,AAAD,EAClCC,cAAc,CAAC,iBAAkBF,EAAO,SAAUG,CAAC,EACrD,IAAM5B,EAAQ4B,EAAE5B,KAAK,EAAI4B,EAAEF,MAAM,CAAEtG,EAAS4E,EAAM5E,MAAM,CAAE,CAAEyG,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAE,CAAG3G,EAAQ,CAAE4G,SAAAA,CAAQ,CAAEC,QAAAA,CAAO,CAAEC,QAAAA,CAAO,CAAE,CAAGH,EAC7H,GAAI7M,AADiJkG,EAAOtD,OAAO,CAACjD,OAAO,EAAEK,gBACvJ8K,EAAMY,aAAa,CAAE,CACvC,IAAMuB,EAAcnC,EAAMY,aAAa,CAClCzH,GAAG,CAAC,AAAC2F,GAASA,EAAK/D,CAAC,EACpBqH,IAAI,CAAC,CAACzO,EAAG0O,IAAM1O,EAAI0O,GAAIC,EAActC,EAAMY,aAAa,CACxDzH,GAAG,CAAC,AAAC2F,GAASA,EAAK9D,CAAC,EACpBoH,IAAI,CAAC,CAACzO,EAAG0O,IAAM1O,EAAI0O,GAAIhE,EAAO8D,CAAW,CAAC,EAAE,CAAEhE,EAAOgE,CAAW,CAACA,EAAY3L,MAAM,CAAG,EAAE,CAAEiI,EAAO6D,CAAW,CAAC,EAAE,CAAE/D,EAAO+D,CAAW,CAACA,EAAY9L,MAAM,CAAG,EAAE,CAAE+L,EAAUhK,KAAKiK,GAAG,CAAC,AAACrE,CAAAA,EAAOE,CAAG,EAAK,IAAMoE,EAAUlK,KAAKiK,GAAG,CAAC,AAACjE,CAAAA,EAAOE,CAAG,EAAK,IAAMiE,EAAKnK,KAAKoK,GAAG,CAACtE,EAAMF,GAAQoE,EAASK,EAAKrK,KAAKC,GAAG,CAAC6F,EAAMF,GAAQoE,EAASM,EAAKtK,KAAKoK,GAAG,CAAClE,EAAMF,GAAQkE,EAASK,EAAKvK,KAAKC,GAAG,CAACiG,EAAMF,GAAQkE,EAC/X,GAAIR,EACAA,EAAQc,WAAW,CAAC,CAAEL,GAAAA,EAAIE,GAAAA,EAAIC,GAAAA,EAAIC,GAAAA,CAAG,QAEpC,GAAIjB,GAASC,EAAO,CACrB,IAAIkB,EAAOnB,EAAMoB,QAAQ,CAACP,GAAKQ,EAAOrB,EAAMoB,QAAQ,CAACL,GAAKO,EAAOrB,EAAMmB,QAAQ,CAACJ,GAAKO,EAAOtB,EAAMmB,QAAQ,CAACH,EACvGd,CAAAA,GACA,CAAA,CAACgB,EAAME,EAAMC,EAAMC,EAAK,CAAG,CAACD,EAAMC,EAAMJ,EAAME,EAAK,AAAD,EAElDF,EAAOE,GACP,CAAA,CAACF,EAAME,EAAK,CAAG,CAACA,EAAMF,EAAK,AAAD,EAE1BG,EAAOC,GACP,CAAA,CAACD,EAAMC,EAAK,CAAG,CAACA,EAAMD,EAAK,AAAD,EAE1BjB,IACAA,EAAQmB,KAAK,CAAG,CAAA,EAChBnB,EAAQoB,KAAK,CAAG,CAAA,GAEpBvB,EAAMwB,SAAS,CAAC,CACZtM,KAAM,CACF8D,EAAGiI,EACHhI,EAAGmI,EACHK,MAAON,EAAOF,EACdS,OAAQL,EAAOD,CACnB,CACJ,EACJ,CACJ,CACJ,EACJ,CAKA,SAASO,EAAetI,CAAM,CAAEuI,CAAG,EAC/B,GAAM,CAAE5B,MAAAA,CAAK,CAAEF,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAE,CAAG1G,SAChC,AAAI2G,EAAME,OAAO,CACNF,EAAME,OAAO,CAAC2B,sBAAsB,CAACD,GAEzC,CACH5I,EAAG8G,EAAQA,EAAMgC,OAAO,CAACF,EAAI5I,CAAC,EAAI,EAClCC,EAAG8G,EAAQA,EAAM+B,OAAO,CAACF,EAAI3I,CAAC,EAAI,CACtC,CACJ,CAEA,SAAS8I,EAA0BC,CAAU,EACzC,IAAqBhC,EAAQ3G,AAAd,IAAI,CAAiB2G,KAAK,CAAEE,EAAUF,EAAME,OAAO,CAAEjN,EAAY8E,EAAWsB,AAA5E,IAAI,CAA+EtD,OAAO,CAACjD,OAAO,EAAEG,WAAYgP,EAAehP,EAAUC,QAAQ,EAAI,IAAKgP,EAAc7I,AAAxK,IAAI,CAA2K6C,iBAAiB,EAAEgG,YAAaC,EAAWD,GAAaC,SAAUC,EAAWF,GAAaE,SAAUrE,EAAY,EAAE,CAClSsE,EAAavE,EAAawE,EAAcrG,EAAS,EAAGsG,EAAO,EAAGC,EAAO,EAAGC,EAAmB,CAAA,EAAOC,EAAc,CAAA,EAC9H,GAAIN,GAAYD,EAAU,CAEtB,IAAMQ,EAASjJ,GAJJ,IAAI,CAGfoE,EAAcqE,CAAQ,CAACH,EAAWY,OAAO,CAAC,EAE1CL,EAAOI,EAAO3J,CAAC,CAAIkH,CAAAA,EAAU,EAAIF,EAAMrG,QAAQ,AAAD,EAC9C6I,EAAOG,EAAO1J,CAAC,CAAIiH,CAAAA,EAAU,EAAIF,EAAMpG,OAAO,AAAD,EAEzCkE,AAAiC,IAAjCA,EAAY+E,SAAS,CAACpO,MAAM,EAE5B4N,EAAcD,CAAQ,CADXD,GAAU,CAACH,EAAWY,OAAO,CAAC,CAACC,SAAS,CAAC,EAAE,CACtB,CAE5B/E,EAAYG,KAAK,EAAEK,SACnB+D,EAAYpE,KAAK,EAAE6E,OACnBT,EAAYpE,KAAK,CAAC8E,KAAK,EACtBV,CAAAA,EAAYpE,KAAK,CAAC6E,KAAK,GAAKhF,EAAYG,KAAK,CAAC6E,KAAK,EAChDT,EAAYpE,KAAK,CAAC8E,KAAK,GAAKjF,EAAYG,KAAK,CAAC8E,KAAK,AAAD,IACtDT,EAAexE,EAAYG,KAAK,CAACK,OAAO,CAAC0E,OAAO,GAEhD/G,EAAS6B,EAAYG,KAAK,CAACK,OAAO,EAAE2E,MAChC,EAAIX,EAAab,KAAK,CAAG,EAC7B3D,EAAYG,KAAK,CAACK,OAAO,CAACX,IAAI,CAAC,CAC3B3E,EAAGqJ,EAAYpE,KAAK,CAAC6E,KAAK,CAAG7G,EAC7BhD,EAAGoJ,EAAYpE,KAAK,CAAC8E,KAAK,CAAG9G,CACjC,GACA6B,EAAYG,KAAK,CAACK,OAAO,CAACV,OAAO,CAAC,CAC9B5E,EAAGuJ,EAAQzE,CAAAA,EAAYG,KAAK,CAACK,OAAO,CAAC3K,MAAM,EAAI,CAAA,EAC/CsF,EAAGuJ,EAAQ1E,CAAAA,EAAYG,KAAK,CAACK,OAAO,CAAC3K,MAAM,EAAI,CAAA,CACnD,EAAGV,EAAW,WACVyP,EAAc,CAAA,EAEdL,EAAYpE,KAAK,EAAEC,WACvB,GAEIJ,EAAYG,KAAK,CAACO,SAAS,EAAE0E,WAC7Bb,EAAYpE,KAAK,CAACO,SAAS,EAAE0E,YAC7BpF,EAAYG,KAAK,CAACO,SAAS,CAACb,IAAI,CAAC,CAC7B3E,EAAGqJ,EAAYpE,KAAK,CAACO,SAAS,CAAC0E,SAAS,CAAClK,CAAC,CAC1CC,EAAGoJ,EAAYpE,KAAK,CAACO,SAAS,CAAC0E,SAAS,CAACjK,CAAC,AAC9C,GACA6E,EAAYG,KAAK,CAACO,SAAS,CAACZ,OAAO,CAAC,CAChC5E,EAAG8E,EAAYG,KAAK,CAACO,SAAS,CAAC0E,SAAS,CAAClK,CAAC,CAC1CC,EAAG6E,EAAYG,KAAK,CAACO,SAAS,CAAC0E,SAAS,CAACjK,CAAC,AAC9C,EAAGhG,MAIN6K,AAAiC,IAAjCA,EAAY+E,SAAS,CAACpO,MAAM,EAGjC4K,EAAevB,EAAa,CAAA,EAAM,CAAA,GAClCpF,EAAY,WAERsF,EAAiBF,EAAa,GAAK7K,EAAW,CAAA,EAAM,CAAA,EACxD,EAAGgP,EAAe,KAKlB5C,EAAevB,EAAa,CAAA,EAAM,CAAA,GAClCA,EAAY+E,SAAS,CAAC7D,OAAO,CAAC,SAAUvB,CAAI,EACpC2E,GAAU,CAAC3E,EAAK,GAChB4E,EAAcD,CAAQ,CAAC3E,EAAK,CAC5BM,EAAUhE,IAAI,CAACsI,GACXA,EAAYpE,KAAK,EAAEK,UACnBmE,EAAmB,CAAA,EACnBJ,EAAYpE,KAAK,CAACK,OAAO,CAACC,IAAI,GAC9B8D,EAAYpE,KAAK,CAACK,OAAO,CAACV,OAAO,CAAC,CAC9B5E,EAAGuJ,EAAQF,CAAAA,EAAYpE,KAAK,CAACK,OAAO,CAAC3K,MAAM,EAAI,CAAA,EAC/CsF,EAAGuJ,EAAQH,CAAAA,EAAYpE,KAAK,CAACK,OAAO,CAAC3K,MAAM,EAAI,CAAA,EAC/C+J,QAAS,EACb,EAAGzK,EAAW,WACVyP,EAAc,CAAA,EACd7E,EAA4BC,EAAaC,EAAW9K,EAAW,GACnE,GACIoP,EAAYpE,KAAK,CAACO,SAAS,EAC3B6D,AAAkC,QAAlCA,EAAYpE,KAAK,CAACO,SAAS,CAACvF,CAAC,EAC7B6E,EAAYG,KAAK,EAAEO,WAAW0E,YAC9Bb,EAAYpE,KAAK,CAACO,SAAS,CAACD,IAAI,GAChC8D,EAAYpE,KAAK,CAACO,SAAS,CAACZ,OAAO,CAAC,CAChC5E,EAAG8E,EAAYG,KAAK,CAACO,SAAS,CAAC0E,SAAS,CAAClK,CAAC,CAC1CC,EAAG6E,EAAYG,KAAK,CAACO,SAAS,CAAC0E,SAAS,CAACjK,CAAC,CAC1CyE,QAAS,EACb,EAAGzK,KAInB,GAEAyF,EAAY,WACJ,AAACgK,GACD7E,EAA4BC,EAAaC,EAAW9K,EAAW,IAEvE,EAAGgP,GACC,AAACQ,GACD/J,EAAY,WACRmF,EAA4BC,EAAaC,EAAW9K,EAAW,GACnE,EAAGgP,EAAe,GAG9B,CACJ,CAKA,SAASkB,IAEL,IAAI,CAACC,uBAAuB,EAAEpE,QAAQ,AAACf,IACnCA,GAAOC,WACX,GACA,IAAI,CAACkF,uBAAuB,CAAG,IACnC,CAKA,SAASC,IACL,IACIC,EAAgBC,EAAYC,EAAsBhQ,EAAiBiQ,EAAiBC,EAAiBC,EAAYC,EAAYC,EAAYC,EAAYC,EAAMC,EAAWnF,EAAe9D,EAAakJ,EAAkBhG,EADlN5E,EAAS,IAAI,CAAE,CAAE2G,MAAAA,CAAK,CAAE,CAAG3G,EAAQ6G,EAAUF,EAAME,OAAO,CAAEgE,EAAQ7K,EAAOxC,SAAS,CAAC,KAAMsN,EAAQ9K,EAAOxC,SAAS,CAAC,KAAMuN,EAAiB/K,EAAOtD,OAAO,CAACjD,OAAO,CAAEuR,EAAehL,EAAO0C,eAAe,GAAIuI,EAAe,EAAE,CAAEC,EAAe,EAAE,CAAEC,EAAqB,EAAE,CAe/Q,GATItE,GAAW7G,EAAOoL,EAAE,CAAC,aAAeP,GAASC,GAC7C9K,EAAOtD,OAAO,CAACgH,IAAI,EAAEiC,QAAQ,CAACvF,EAAGN,KAC7B,IAAMuL,EAAKrL,EAAOsL,YAAY,CAAClL,GAC3BiL,IACAR,CAAK,CAAC/K,EAAE,CAAGuL,EAAG1L,CAAC,CACfmL,CAAK,CAAChL,EAAE,CAAGuL,EAAGzL,CAAC,CAEvB,GAEAmL,GAAgBrR,SAChBmR,GAAOzP,QACP0P,GAAO1P,QACP,CAACuL,EAAM4E,KAAK,CAAE,CACdb,EAAOK,EAAe/Q,eAAe,CAAC0Q,IAAI,CAG1CE,AAFAA,CAAAA,EAAmBG,EAAe/Q,eAAe,AAAD,EAE/ByH,iBAAiB,CAAGrC,EAAewL,EAAiB3Q,QAAQ,EACzE0E,EAAgB3E,eAAe,CAACC,QAAQ,CAAE0M,EAAM6E,SAAS,EAC7DZ,EAAiB3J,iBAAiB,CAAG7B,EAAewL,EAAiB1Q,QAAQ,EACzEyE,EAAgB3E,eAAe,CAACE,QAAQ,CAAEyM,EAAM6E,SAAS,EAC7DrR,EAAkByQ,EAAiBzQ,eAAe,EAC9CwE,EAAgB3E,eAAe,CAACG,eAAe,CAEnD,IAAMsR,EAAWb,EAAiBnJ,iBAAiB,CAAG,EAAGiK,EAAKpD,EAAetI,EAAQ,CAAEL,EAAG,EAAGC,EAAG,CAAE,GAAI+L,EAAKrD,EAAetI,EAAQ,CAAEL,EAAG8L,EAAU7L,EAAG6L,CAAS,GAC7JrB,EAAkBjN,KAAKiK,GAAG,CAACsE,EAAG/L,CAAC,CAAGgM,EAAGhM,CAAC,EACtC0K,EAAkBlN,KAAKiK,GAAG,CAACsE,EAAG9L,CAAC,CAAG+L,EAAG/L,CAAC,EAEtC,IAAK,IAAIE,EAAI,EAAGA,EAAI+K,EAAMzP,MAAM,CAAE0E,IAC1B,CAACE,EAAO4L,QAAQ,GACZ,AAAC/M,EAAQ0L,IACR1L,EAAQyL,IACRzL,EAAQ4L,IACR5L,EAAQ2L,GAIJvL,EAAS6L,CAAK,CAAChL,EAAE,GACtBb,EAASwL,IACTxL,EAASuL,KACTD,EAAapN,KAAKC,GAAG,CAACyN,CAAK,CAAC/K,EAAE,CAAEyK,GAChCD,EAAanN,KAAKoK,GAAG,CAACsD,CAAK,CAAC/K,EAAE,CAAEwK,GAChCG,EAAatN,KAAKC,GAAG,CAAC0N,CAAK,CAAChL,EAAE,EAAI2K,EAAYA,GAC9CD,EAAarN,KAAKoK,GAAG,CAACuD,CAAK,CAAChL,EAAE,EAAI0K,EAAYA,KAT9CD,EAAaD,EAAaO,CAAK,CAAC/K,EAAE,CAClC2K,EAAaD,EAAaM,CAAK,CAAChL,EAAE,GAatC+K,CAAK,CAAC/K,EAAE,EAAKkL,EAAa/H,IAAI,CAAGmH,GACjCS,CAAK,CAAC/K,EAAE,EAAKkL,EAAajI,IAAI,CAAGqH,GACjC,AAACU,CAAAA,CAAK,CAAChL,EAAE,EAAIkL,EAAa3H,IAAI,AAAD,GACxB2H,EAAa3H,IAAI,CAAGgH,GACzB,AAACS,CAAAA,CAAK,CAAChL,EAAE,EAAIkL,EAAa7H,IAAI,AAAD,GACxB6H,EAAa7H,IAAI,CAAGkH,IACzBY,EAAavK,IAAI,CAACmK,CAAK,CAAC/K,EAAE,EAC1BoL,EAAaxK,IAAI,CAACoK,CAAK,CAAChL,EAAE,EAC1BqL,EAAmBzK,IAAI,CAACZ,IA8BhC,GA1BIjB,EAAQ0L,IAAe1L,EAAQyL,IAC/BrL,EAASwL,IAAexL,EAASuL,KACjCxK,EAAO4L,QAAQ,CAAGrB,EAClBvK,EAAO6L,QAAQ,CAAGvB,EAClBtK,EAAO8L,QAAQ,CAAGrB,EAClBzK,EAAO+L,QAAQ,CAAGvB,GAmBtBhF,EAAgB9D,AADhBA,CAAAA,EAAciJ,CAhBV5L,EAAW2L,GACCA,EAEP1K,EAAOV,uBAAuB,CAC/BoL,GAAQ1K,EAAOV,uBAAuB,CAACoL,EAAK,CAChC1K,EAAOV,uBAAuB,CAACoL,EAAK,CAGpCO,EAAa7P,MAAM,CAAGjB,EAC9B6F,EAAOV,uBAAuB,CAACsB,MAAM,CACrCZ,EAAOV,uBAAuB,CAACC,IAAI,CAI/B,IAAM,CAAA,GAEEpG,IAAI,CAAC,IAAI,CAAE8R,EAAcC,EAAcC,EAAoBP,EAAgB,EACrE5K,EAAOgM,gBAAgB,CAACtK,EAAaqJ,GAAkBrJ,EAEjFqJ,EAAenR,SAAS,EACxBoG,EAAO6C,iBAAiB,EAAEgG,aAAaE,SAAU,KA1WnCA,EA4WG/I,EAAO6C,iBAAiB,CAACgG,WAAW,CAACE,QAAQ,CA3WtE,IAAK,IAAMtQ,KAAOE,OAAOiF,IAAI,CAACmL,GAC1BA,CAAQ,CAACtQ,EAAI,CAACmM,KAAK,EAAEC,YA2WjBoF,EAAiBjK,EAAO6C,iBAAiB,CAACgG,WAAW,CAACC,QAAQ,AAClE,MAEImB,EAAiB,CAAC,EAGtBC,EAAaW,EAAMzP,MAAM,CACzB+O,EAAuBnK,EAAO6C,iBAAiB,CAC3C2C,IACAxF,EAAOiM,SAAS,CAACnP,QAAQ,CAAG,IAAIL,EAAmB,CAC/CI,QAAS,CACL8C,EAAG6F,EAAc0G,YAAY,CAC7BtM,EAAG4F,EAAc2G,YAAY,AACjC,CACJ,GACAnM,EAAOoM,cAAc,CAAG,CAAA,EACxBpM,EAAO6C,iBAAiB,CAAG2C,EAC3BxF,EAAOqM,QAAQ,CAAG7G,EAAc6G,QAAQ,EAE5CrI,EAAmBsI,KAAK,CAAC,IAAI,EACzB9G,GAAiBxF,EAAO6C,iBAAiB,GAEzC7C,EAAO6C,iBAAiB,CAAChC,QAAQ,EAAE8E,QAAQ,AAAClM,IAExCmL,AADAA,CAAAA,EAAQ5E,EAAOiC,MAAM,CAACxI,EAAQ8S,KAAK,CAAC,AAAD,EAC7BC,SAAS,CAAG,CAAA,EAClB5H,EAAMY,aAAa,CAAG/L,EAAQiK,IAAI,CAClCkB,EAAM6H,mBAAmB,CAAGhT,EAAQiK,IAAI,CAACtI,MAAM,CAC/C3B,EAAQmL,KAAK,CAAGA,EAEhBhG,EAASgG,EAAO,QAASwB,EAC7B,GAEApG,EAAO6C,iBAAiB,CAAC/B,KAAK,EAAE6E,QAAQ,AAAC7E,IACrCA,EAAM8D,KAAK,CAAG5E,EAAOiC,MAAM,CAACnB,EAAMyL,KAAK,CAAC,AAC5C,GAEIxB,EAAenR,SAAS,EACxBoG,EAAO6C,iBAAiB,EACxB7C,CAAAA,EAAO6C,iBAAiB,CAACgG,WAAW,CAAG,CACnCE,SAAUkB,EACVnB,SAAU9I,EAAO0M,cAAc,CAAClH,EAAe2E,EAAsBD,EACzE,CAAA,EAICa,EAAenR,SAAS,CAIzB,IAAI,CAAC+S,iBAAiB,GAHtB,IAAI,CAACC,oBAAoB,GAK7B,IAAI,CAAC7C,uBAAuB,CACxB,IAAI,CAACqC,cAAc,CAAG,IAAI,CAACnK,MAAM,CAAG,KAEhD,MAEI+B,EAAmBsI,KAAK,CAAC,IAAI,CAErC,CAEA,SAASO,EAAmChM,CAAQ,CAAEQ,CAAM,CAAEC,CAAM,EAChE,IAAME,EAAuB,EAAE,CAC/B,IAAK,IAAIY,EAAe,EAAGA,EAAevB,EAASzF,MAAM,CAAEgH,IAAgB,CACvE,IAAMsJ,EAAKrL,GAAe,IAAI,CAAE,CAAEV,EAAG0B,EAAQzB,EAAG0B,CAAO,GAAIqK,EAAKtL,GAAe,IAAI,CAAE,CACjFV,EAAGkB,CAAQ,CAACuB,EAAa,CAACR,IAAI,CAC9BhC,EAAGiB,CAAQ,CAACuB,EAAa,CAACP,IAAI,AAClC,GAAI3H,EAAWiD,KAAK0G,IAAI,CAAC1G,KAAK2G,GAAG,CAAC4H,EAAG/L,CAAC,CAAGgM,EAAGhM,CAAC,CAAE,GAC3CxC,KAAK2G,GAAG,CAAC4H,EAAG9L,CAAC,CAAG+L,EAAG/L,CAAC,CAAE,IAC1B4B,EAAqBd,IAAI,CAAC,CAAE0B,aAAAA,EAAclI,SAAAA,CAAS,EACvD,CACA,OAAOsH,EAAqBwF,IAAI,CAAC,CAACzO,EAAG0O,IAAM1O,EAAE2B,QAAQ,CAAG+M,EAAE/M,QAAQ,CACtE,CAEA,SAAS4S,EAAuBpL,CAAW,CAAEhF,CAAO,EAChD,IAAqBgH,EAAO1D,AAAb,IAAI,CAAgBtD,OAAO,CAACgH,IAAI,CAAEwI,EAAe,EAAE,CAAEC,EAAe,EAAE,CAAEtL,EAAW,EAAE,CACpGC,EAAQ,EAAE,CACVuL,EAAW,EAAE,CAEbtS,EAAqBoD,KAAKC,GAAG,CAAC,EAAGV,EAAQ3C,kBAAkB,EAAI,GAC3DwS,EAAQ,EAAGhD,EAAS3E,EAAO3C,EAAQ8K,EAAkB3H,EAAWhL,EAAQ4S,EAAYC,EAAcC,EAAgBC,EAAapJ,EAAaqJ,EAEhJ,GAAIrO,EAAWrC,EAAQ1C,eAAe,CAAC0Q,IAAI,GACvC,CAAC1K,AARU,IAAI,CAQPqN,wBAAwB,CAAC3L,GAGjC,OAFA5C,EAAM,+EAC2C,CAAA,EAAOkB,AAV7C,IAAI,CAUgD2G,KAAK,EAC7D,CAAA,EAEX,IAAK,IAAM2G,KAAK5L,EACZ,GAAIA,CAAW,CAAC4L,EAAE,CAAClS,MAAM,EAAIrB,EAAoB,CAK7C,GAJAkI,EAASP,CAAW,CAAC4L,EAAE,CACvB/D,EAAU3D,IACVR,EAAYnD,EAAO7G,MAAM,CAErBsB,EAAQ6Q,KAAK,CACb,IAAK,IAAIzN,EAAI,EAAGA,EAAIpD,EAAQ6Q,KAAK,CAACnS,MAAM,CAAE0E,IAClCsF,GAAa1I,EAAQ6Q,KAAK,CAACzN,EAAE,CAACjE,IAAI,EAClCuJ,GAAa1I,EAAQ6Q,KAAK,CAACzN,EAAE,CAAC0N,EAAE,GAEhCzJ,AADAA,CAAAA,EAAcrH,EAAQ6Q,KAAK,CAACzN,EAAE,AAAD,EACjB2N,SAAS,CAAG3N,EACxBqN,EAAczQ,EAAQ6Q,KAAK,CAACzN,EAAE,CAAC1F,MAAM,CACrCgT,EAAuB1Q,EAAQ6Q,KAAK,CAACzN,EAAE,CAAC4N,SAAS,EAI7DR,EAAiBvL,EAAmBM,GAChCvF,AAAiC,SAAjCA,EAAQ1C,eAAe,CAAC0Q,IAAI,EAC3BhO,EAAQ/C,YAAY,CAgBrBqT,EAAa,CACTrN,EAAGuN,EAAevN,CAAC,CACnBC,EAAGsN,EAAetN,CAAC,AACvB,GAlBAxF,EAAS4F,AAjCN,IAAI,CAiCStD,OAAO,CAACtC,MAAM,EAAI,CAAC,EACnC4S,EAAahN,AAlCV,IAAI,CAkCa2N,wBAAwB,CAAC,CACzChO,EAAGuN,EAAevN,CAAC,CACnBC,EAAGsN,EAAetN,CAAC,CACnBnH,IAAK6U,EACL5L,YAAaA,EACbzH,SAAU+F,AAvCX,IAAI,CAuCcG,iBAAiB,CAACzD,EAAQ1C,eAAe,EAC1D4T,cAAexT,EAAOE,MAAM,EAAI,EAAKF,CAAAA,EAAOG,SAAS,EAAI,CAAA,EACzDsT,cAAe,AAACV,GAAeA,EAAY7S,MAAM,CAC7C6S,EAAY7S,MAAM,CAClB,AAACoC,CAAAA,EAAQtC,MAAM,EAAI,CAAC,CAAA,EAAGE,MAAM,EACzBqE,EAAgBvE,MAAM,CAACE,MAAM,AACzC,IAQJ,IAAK,IAAIwF,EAAI,EAAGA,EAAIsF,EAAWtF,IAC3BmC,CAAM,CAACnC,EAAE,CAACgO,aAAa,CAAGvE,EAwB9B,GAtBA1I,EAASH,IAAI,CAAC,CACVf,EAAGqN,EAAWrN,CAAC,CACfC,EAAGoN,EAAWpN,CAAC,CACfhD,GAAI0Q,EACJ/D,QAAAA,EACAgD,MAAAA,EACA7I,KAAMzB,EACN8B,YAAAA,EACAqJ,qBAAAA,CACJ,GACAlB,EAAaxL,IAAI,CAACsM,EAAWrN,CAAC,EAC9BwM,EAAazL,IAAI,CAACsM,EAAWpN,CAAC,EAC9ByM,EAAS3L,IAAI,CAAC,CACVhE,QAAS,CACLqR,aAAc,UACdtT,WAAYiC,EAAQjC,UAAU,CAC9BL,OAAQ8E,EAAMxC,EAAQtC,MAAM,CAAE,CAC1B4T,OAAQtR,EAAQsR,MAAM,AAC1B,EAAGb,GAAe,CAAC,EACvB,CACJ,GAEIzJ,GAAMtI,OACN,IAAK,IAAI0E,EAAI,EAAGA,EAAIsF,EAAWtF,IACvBd,EAAS0E,CAAI,CAACzB,CAAM,CAACnC,EAAE,CAACa,SAAS,CAAC,GAClCsB,CAAAA,CAAM,CAACnC,EAAE,CAACpD,OAAO,CAAGgH,CAAI,CAACzB,CAAM,CAACnC,EAAE,CAACa,SAAS,CAAC,AAAD,CAIxD4L,CAAAA,IACAY,EAAc,IAClB,MAEI,IAAK,IAAIrN,EAAI,EAAGA,EAAI4B,CAAW,CAAC4L,EAAE,CAAClS,MAAM,CAAE0E,IAEvC8E,EAAQlD,CAAW,CAAC4L,EAAE,CAACxN,EAAE,CACzByJ,EAAU3D,IACVqH,EAAe,KACfF,EAAmBrJ,GAAM,CAACkB,EAAMjE,SAAS,CAAC,CAC1CuL,EAAaxL,IAAI,CAACkE,EAAMjF,CAAC,EACzBwM,EAAazL,IAAI,CAACkE,EAAMhF,CAAC,EACzBgF,EAAMkJ,aAAa,CAAGvE,EACtBzI,EAAMJ,IAAI,CAAC,CACPf,EAAGiF,EAAMjF,CAAC,CACVC,EAAGgF,EAAMhF,CAAC,CACVhD,GAAI0Q,EACJ/D,QAAAA,EACAgD,MAAAA,EACA7I,KAAMhC,CAAW,CAAC4L,EAAE,AACxB,GAIIL,EAHAF,GACA,AAA4B,UAA5B,OAAOA,GACP,CAACxR,EAAQwR,GACM7N,EAAM6N,EAAkB,CAAEpN,EAAGiF,EAAMjF,CAAC,CAAEC,EAAGgF,EAAMhF,CAAC,AAAC,GAGjD,CACXqO,YAAalB,EACbpN,EAAGiF,EAAMjF,CAAC,CACVC,EAAGgF,EAAMhF,CAAC,AACd,EAEJyM,EAAS3L,IAAI,CAAC,CAAEhE,QAASuQ,CAAa,GACtCV,IAIZ,MAAO,CACH1L,SAAAA,EACAC,MAAAA,EACAoL,aAAAA,EACAC,aAAAA,EACAE,SAAAA,CACJ,CACJ,CAEA,SAAS6B,IACL,GAAqB,CAAEvH,MAAAA,CAAK,CAAEF,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAE,CAA7B,IAAI,CACfpG,EAAW,EAAGC,EAAU,EAe5B,OAASD,QAAQ,CAbbA,EADAmG,GAASzG,AAFE,IAAI,CAEC6L,QAAQ,EAAI7L,AAFjB,IAAI,CAEoB4L,QAAQ,CAChCnF,EAAM0H,QAAQ,CACrB1H,EAAMoB,QAAQ,CAAC7H,AAJR,IAAI,CAIW4L,QAAQ,EAAInF,EAAMoB,QAAQ,CAAC7H,AAJ1C,IAAI,CAI6C6L,QAAQ,EAGzDlF,EAAMrG,QAAQ,CASVC,OAAO,CAPtBmG,GAAS1G,AATE,IAAI,CASC+L,QAAQ,EAAI/L,AATjB,IAAI,CASoB8L,QAAQ,CACjCpF,EAAMyH,QAAQ,CACpBzH,EAAMmB,QAAQ,CAAC7H,AAXR,IAAI,CAWW+L,QAAQ,EAAIrF,EAAMmB,QAAQ,CAAC7H,AAX1C,IAAI,CAW6C8L,QAAQ,EAG1DnF,EAAMpG,OAAO,CAG/B,CAMA,SAAS6N,EAAqB5I,CAAa,CAAE2E,CAAoB,CAAEkE,CAAU,EACzE,IAuBIvF,EAAUC,EAvBRuF,EAAkBnE,EACpB5E,EAAa4E,EAAsBkE,GAAc,EAAE,CAAEE,EAAkBhJ,EAAaC,EAAe6I,GAAa3I,EAAQ,CAAC,EAE7HzB,EAAmB,EAAE,CAErBuB,EAAc3E,QAAQ,CAAC8E,OAAO,CAAC,AAAClM,IAC5BiM,CAAK,CAACjM,EAAQ8P,OAAO,CAAC,CAAG,CACrB5J,EAAGlG,EAAQkG,CAAC,CACZC,EAAGnG,EAAQmG,CAAC,CACZhD,GAAInD,EAAQ8P,OAAO,CACnB3E,MAAOnL,EAAQmL,KAAK,CACpB4E,UAAW,EAAE,AACjB,CACJ,GACAhE,EAAc1E,KAAK,CAAC6E,OAAO,CAAC,AAAC7E,IACzB4E,CAAK,CAAC5E,EAAMyI,OAAO,CAAC,CAAG,CACnB5J,EAAGmB,EAAMnB,CAAC,CACVC,EAAGkB,EAAMlB,CAAC,CACVhD,GAAIkE,EAAMyI,OAAO,CACjB3E,MAAO9D,EAAM8D,KAAK,CAClB4E,UAAW,EAAE,AACjB,CACJ,GAGA,IAAK,IAAI1J,EAAI,EAAGA,EAAIyO,EAAgBnT,MAAM,CAAE0E,IACxCgJ,EAAWyF,CAAe,CAACzO,EAAE,CAC7BiJ,EAAWuF,CAAe,CAACxO,EAAE,CACzBgJ,GAAUgF,eACV/E,GAAU+E,eACVpI,CAAK,CAACoD,EAASgF,aAAa,CAAC,EAAEtE,UAAUgF,QAAQzF,EAAS+E,aAAa,IAAM,KAC7EpI,CAAK,CAACoD,EAASgF,aAAa,CAAC,CAACtE,SAAS,CAAC9I,IAAI,CAACqI,EAAS+E,aAAa,EAC/D7J,AAAqD,KAArDA,EAAiBuK,OAAO,CAACzF,EAAS+E,aAAa,GAC/C7J,EAAiBvD,IAAI,CAACqI,EAAS+E,aAAa,GAIxD,OAAOpI,CACX,CAEA,SAAS+I,IACL,IAAM9H,EAAQ,IAAI,CAACA,KAAK,CAAEhH,EAAIgH,EAAME,OAAO,CAAG,EAAIF,EAAMrG,QAAQ,CAAyCoL,EAAKpD,EAAe,IAAI,CAAE,CAC/H3I,EAAAA,EACAC,EAFkE+G,EAAME,OAAO,CAAG,EAAIF,EAAMpG,OAAO,AAGvG,GAAIoL,EAAKrD,EAAe,IAAI,CAAE,CAC1B3I,EAAGA,EAAIgH,EAAM6E,SAAS,CACtB5L,EAAGD,EAAIgH,EAAM+H,UAAU,AAC3B,GAAIC,EAAWjD,EAAG/L,CAAC,CAAEiP,EAAWjD,EAAGhM,CAAC,CAAEkP,EAAWnD,EAAG9L,CAAC,CAAEkP,EAAWnD,EAAG/L,CAAC,CACtE,MAAO,CACHqD,KAAM9F,KAAKoK,GAAG,CAACoH,EAAUC,GACzB7L,KAAM5F,KAAKC,GAAG,CAACuR,EAAUC,GACzBvL,KAAMlG,KAAKoK,GAAG,CAACsH,EAAUC,GACzB3L,KAAMhG,KAAKC,GAAG,CAACyR,EAAUC,EAC7B,CACJ,CAEA,SAASC,EAAwBrS,CAAO,EACpC,IAAqB+J,EAAQzG,AAAd,IAAI,CAAiByG,KAAK,CAAEI,EAAU7G,AAAtC,IAAI,CAAyC2G,KAAK,CAACE,OAAO,CAAEpF,EAAoB/E,EAAQ+E,iBAAiB,EACpH9C,EAAgB3E,eAAe,CAACC,QAAQ,CACxC+U,EAAS,CAAA,EAAM1B,EAAI,EAAG2B,EAAU,CAC/BjP,CAHU,IAAI,CAGPkP,aAAa,GACjBrI,EACA7G,AALO,IAAI,CAKJkP,aAAa,CAAGzN,EAAoBoF,EAAQsI,QAAQ,GAG3DnP,AARO,IAAI,CAQJkP,aAAa,CAAG/R,KAAKiK,GAAG,CAACX,EAAMgC,OAAO,CAAChH,GAAqBgF,EAAMgC,OAAO,CAAC,KAMzF,IAAM2G,EAAQ,CAAC,AAAC3N,CAAAA,EAHCoF,CAAAA,EACb7G,AAZW,IAAI,CAYRkP,aAAa,CAAGrI,EAAQsI,QAAQ,GACvC1I,EAAMoB,QAAQ,CAAC7H,AAbJ,IAAI,CAaOkP,aAAa,EAAIzI,EAAMoB,QAAQ,CAAC,EAAC,CAChB,EAAGwH,OAAO,CAAC,IAEtD,KAAOL,GAAUI,AAAU,IAAVA,GAAa,CAC1B,IAAME,EAAQnS,KAAK2G,GAAG,CAAC,EAAGwJ,EACtB8B,CAAAA,EAAQ,KAAQA,EAAQ,KACxBJ,EAAS,CAAA,EAEJI,GAAU,EAAIE,GAAUF,EAAQ,AAAK,EAAIE,EAAT,GACrCN,EAAS,CAAA,EACTC,EAAUK,GAELF,GAASE,GAASF,EAAQE,EAAQ,IACvCN,EAAS,CAAA,EACTC,EAAU,EAAIK,GAElBhC,GACJ,CACA,OAAO,AAAC7L,EAAoBwN,EAAWG,CAC3C,CAKA,SAASG,IACL,IAAMC,EAAsB,IAAI,CAACzF,uBAAuB,CAAEhB,EAAW,IAAI,CAAClG,iBAAiB,EAAEgG,aAAaE,SAAU0G,EAAcxL,EAAiBlG,GAAG,CAAC,AAACqG,GAAS2E,GAAU,CAAC3E,EAAK,CAACQ,OAAOhI,IAAM,IAC/L4S,GAAqB7J,QAAQ,AAACf,IAEtBA,GACA6K,AAAkC,KAAlCA,EAAYjB,OAAO,CAAC5J,EAAMhI,EAAE,GACxBgI,EAAMK,OAAO,EACbL,EAAMK,OAAO,CAACkB,IAAI,GAElBvB,EAAMO,SAAS,EACfP,EAAMO,SAAS,CAACgB,IAAI,IAIxBvB,GAAOC,WAEf,EACJ,CAKA,SAAS6K,EAA+BhO,CAAW,EAC/C,IAAIvF,EAAS,CAAA,QACb,CAAI,CAAC6C,EAAS0C,KAGdvC,EAAgCuC,EAAa,AAAC0C,IAE1C,GADAjI,EAAS,CAAA,EACL,CAACZ,EAAQ6I,IAAS,CAACA,EAAKhJ,MAAM,CAAE,CAChCe,EAAS,CAAA,EACT,MACJ,CACA,IAAK,IAAI2D,EAAI,EAAGA,EAAIsE,EAAKhJ,MAAM,CAAE0E,IAC7B,GAAI,CAACd,EAASoF,CAAI,CAACtE,EAAE,GAAM,CAACsE,CAAI,CAACtE,EAAE,CAACH,CAAC,EAAI,CAACyE,CAAI,CAACtE,EAAE,CAACF,CAAC,CAAG,CAClDzD,EAAS,CAAA,EACT,MACJ,CAER,GACOA,EACX,CAEA,SAASwT,EAA+BC,CAAK,EACzC,GAAqB,CAACnP,EAAOZ,EAAM,CAAG+P,EAAMnX,GAAG,CAACoX,KAAK,CAAC,KAAK9R,GAAG,CAAC+R,YAAa7V,EAAW2V,EAAM3V,QAAQ,CAAEyH,EAAckO,EAAMlO,WAAW,CAAEkM,EAAgBgC,EAAMhC,aAAa,CAAEC,EAAgB+B,EAAM/B,aAAa,CAAEkC,EAAUlQ,EAAQ5F,EAAU+V,EAAUvP,EAAQxG,EAAUgW,EAAU5P,GAArQ,IAAI,CAAwRuP,GAAQM,EAAwB,EAAE,CAAEvN,EAAuB3C,AAAvV,IAAI,CAA0VtD,OAAO,CAACjD,OAAO,EAAEW,OAAQ+S,EAAcnN,AAArY,IAAI,CAAwYtD,OAAO,CAACjD,OAAO,EAAE8T,MAAOxN,EAAaC,AAAjb,IAAI,CAAobC,aAAa,GAChdkQ,EAASF,EAAQtQ,CAAC,CAAEyQ,EAASH,EAAQrQ,CAAC,CAAEwF,EAAY,EAAG9K,EAAS,EAAG+V,EAAYC,EAAYC,EAAOC,EAAOC,EAAaC,EAAaC,EAAGC,EAAOC,EAAOC,EAAgBC,EAASnT,EAEjLuS,GAAUpQ,EAAWO,QAAQ,CAC7B8P,GAAUrQ,EAAWQ,OAAO,CAC5B,IAAK,IAAIT,EAAI,EAAGA,EAAI,EAAGA,IAUnB,IAAK6Q,EAAI,EATTJ,EAAQzQ,EAAI,EAAI,GAAK,EACrB0Q,EAAQ1Q,EAAI,EAAI,GAAK,EACrB2Q,EAActT,KAAKqD,KAAK,CAAC,AAAC2P,CAAAA,EAASI,EAAQ1C,CAAY,EAAK5T,GAE5D2D,EAAO,CACH8S,AAFJA,CAAAA,EAAcvT,KAAKqD,KAAK,CAAC,AAAC4P,CAAAA,EAASI,EAAQ3C,CAAY,EAAK5T,EAAQ,EAElD,IAAMwW,EACpBC,EAAc,IAAM7Q,EACpBY,EAAQ,IAAMgQ,EACjB,CACWE,EAAI/S,EAAKxC,MAAM,CAAEuV,IACrBT,AAA2C,KAA3CA,EAAsB1B,OAAO,CAAC5Q,CAAI,CAAC+S,EAAE,GACrC/S,CAAI,CAAC+S,EAAE,GAAKf,EAAMnX,GAAG,EACrByX,EAAsBxP,IAAI,CAAC9C,CAAI,CAAC+S,EAAE,EAI9C,IAAK,IAAMK,KAAQd,EACf,GAAIxO,CAAW,CAACsP,EAAK,CAAE,CAEdtP,CAAW,CAACsP,EAAK,CAACpP,IAAI,GACvBkP,EAAiBnP,EAAmBD,CAAW,CAACsP,EAAK,EACrDtP,CAAW,CAACsP,EAAK,CAACpP,IAAI,CAAGkP,EAAenR,CAAC,CACzC+B,CAAW,CAACsP,EAAK,CAACnP,IAAI,CAAGiP,EAAelR,CAAC,EAE7C,IAAM2I,EAAMlI,GA9BL,IAAI,CA8BwB,CAC/BV,EAAG+B,CAAW,CAACsP,EAAK,CAACpP,IAAI,EAAI,EAC7BhC,EAAG8B,CAAW,CAACsP,EAAK,CAACnP,IAAI,EAAI,CACjC,GAIA,GAHAwO,EAAa9H,EAAI5I,CAAC,CAAGI,EAAWO,QAAQ,CACxCgQ,EAAa/H,EAAI3I,CAAC,CAAGG,EAAWQ,OAAO,CACvC,CAACsQ,EAAOD,EAAM,CAAGI,EAAKnB,KAAK,CAAC,KAAK9R,GAAG,CAAC+R,YACjC3C,EAAa,CACb/H,EAAY1D,CAAW,CAACsP,EAAK,CAAC5V,MAAM,CACpC,IAAK,IAAI0E,EAAI,EAAGA,EAAIqN,EAAY/R,MAAM,CAAE0E,IAChCsF,GAAa+H,CAAW,CAACrN,EAAE,CAACjE,IAAI,EAChCuJ,GAAa+H,CAAW,CAACrN,EAAE,CAAC0N,EAAE,GAE1BlT,EADAuE,EAAQsO,CAAW,CAACrN,EAAE,CAAC1F,MAAM,EAAEE,QACtB6S,CAAW,CAACrN,EAAE,CAAC1F,MAAM,CAACE,MAAM,EAAI,EAEpCqI,GAAsBrI,OAClBqI,EAAqBrI,MAAM,CAG3BqE,EAAgBvE,MAAM,CAACE,MAAM,CAItD,CACIoH,CAAW,CAACsP,EAAK,CAAC5V,MAAM,CAAG,GAC3Bd,AAAW,IAAXA,GACAqI,GAAsBrI,OACtBA,EAASqI,EAAqBrI,MAAM,CAE/BoH,AAA6B,IAA7BA,CAAW,CAACsP,EAAK,CAAC5V,MAAM,EAC7Bd,CAAAA,EAASsT,CAAY,EAEzBmD,EAAUlD,EAAgBvT,EAC1BA,EAAS,EACLsW,IAAU/Q,GACV1C,KAAKiK,GAAG,CAAC+I,EAASE,GAAcU,GAChCZ,CAAAA,EAASS,EAAQ/Q,EAAQ,EAAIkQ,EAAUlC,EACnCkC,EAAU9V,EAAW4T,CAAY,EAErCgD,IAAUpQ,GACVtD,KAAKiK,GAAG,CAACgJ,EAASE,GAAcS,GAChCX,CAAAA,EAASS,EAAQpQ,EAAQ,EAAIuP,EAAUnC,EACnCmC,EAAU/V,EAAW4T,CAAY,CAE7C,CAEJ,IAAMtF,EAAMD,EA5EG,IAAI,CA4EgB,CAC/B3I,EAAGwQ,EAASpQ,EAAWO,QAAQ,CAC/BV,EAAGwQ,EAASrQ,EAAWQ,OAAO,AAClC,GAGA,OAFAmB,CAAW,CAACkO,EAAMnX,GAAG,CAAC,CAACmJ,IAAI,CAAG2G,EAAI5I,CAAC,CACnC+B,CAAW,CAACkO,EAAMnX,GAAG,CAAC,CAACoJ,IAAI,CAAG0G,EAAI3I,CAAC,CAC5B2I,CACX,CAKA,SAASlI,GAAeL,CAAM,CAAEuI,CAAG,EAC/B,GAAM,CAAE5B,MAAAA,CAAK,CAAEF,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAE,CAAG1G,SAChC,AAAI2G,EAAME,OAAO,CACNF,EAAME,OAAO,CAACoK,sBAAsB,CAAC1I,GAEzC,CACH5I,EAAG8G,EAAQA,EAAMoB,QAAQ,CAACU,EAAI5I,CAAC,EAAI,EACnCC,EAAG8G,EAAQA,EAAMmB,QAAQ,CAACU,EAAI3I,CAAC,EAAI,CACvC,CACJ,CAS6B,IAAMsR,GAHN,CACzBC,QAn2BJ,SAAiBC,CAAwB,CAAEC,CAAkB,EACzD,IAAMC,EAAeD,EAAmBpY,SAAS,AAC7C,EAACqY,EAAahS,uBAAuB,GACrC0E,EAAqBsN,EAAaC,cAAc,CAChDD,EAAahS,uBAAuB,CAAGA,EACvCgS,EAAaE,mBAAmB,CAAG9I,EACnC4I,EAAa1E,oBAAoB,CAAG9C,EACpCwH,EAAaC,cAAc,CAAGvH,EAC9BsH,EAAanP,4BAA4B,CACrC0K,EACJyE,EAAatF,gBAAgB,CAAGc,EAChCwE,EAAarR,aAAa,CAAGiO,EAC7BoD,EAAa5E,cAAc,CAAG0B,EAC9BkD,EAAa5O,eAAe,CAAG+L,EAC/B6C,EAAanR,iBAAiB,CAAG4O,EACjCuC,EAAa3E,iBAAiB,CAAG4C,EACjC+B,EAAajE,wBAAwB,CAAGqC,EACxC4B,EAAa3D,wBAAwB,CAAGgC,EAExC/Q,EAASyS,EAAoB,UAAWC,EAAa1E,oBAAoB,EACrEwE,EAAyBK,WAAW,EACpCL,CAAAA,EAAyBK,WAAW,CAACzR,MAAM,CAAGd,EAAMkS,EAAyBK,WAAW,CAACzR,MAAM,CAAExG,EAAoC,EAGjJ,CA40BA,EAmBM,CAAEkF,WAAYgT,EAAyB,CAAE,CAAInY,IAE7C,CAAEoY,eAAAA,EAAc,CAAE,CAAIpY,IAEtB,CAAEqY,SAAAA,EAAQ,CAAE,CAAIrY,IAIhB,CAAEqF,SAAUiT,EAAuB,CAAEhT,QAASiT,EAAsB,CAAEhT,MAAOiT,EAAoB,CAAEhT,WAAYiT,EAAyB,CAAE9S,MAAO+S,EAAoB,CAAEC,WAAAA,EAAU,CAAE7S,YAAa8S,EAA0B,CAAE,CAAI5Y,IA+BtO,SAAS6Y,KACL,IAAMzL,EAAQ,IAAI,CAACA,KAAK,CACpB0L,EAAoB,EACxB,IAAK,IAAMrS,KAAU2G,EAAM3G,MAAM,CACzBA,EAAO6C,iBAAiB,EACxBwP,CAAAA,EAAqBX,GAA0B,AAAC1R,CAAAA,EAAOtD,OAAO,CAACjD,OAAO,EAAI,CAAC,CAAA,EAAGG,SAAS,EAAEC,QAAQ,EAC7F,CAAC,EAGbsY,GAA2B,KACnBxL,EAAM3L,OAAO,EACb2L,EAAM3L,OAAO,CAAC6J,OAAO,EAE7B,EAAGwN,EACP,CAKA,SAASC,KAEL,IAAK,IAAMtS,KAAW2G,AADR,IAAI,CACU3G,MAAM,EAAI,EAAE,CACpC,GAAIA,EAAO6C,iBAAiB,CAAE,CAC1B,IAAMnG,EAAUsD,EAAOtD,OAAO,CAACjD,OAAO,CAA8DsP,EAAW,AAACF,CAAAA,AAA1D,AAAC7I,CAAAA,EAAO6C,iBAAiB,EAAI,CAAC,CAAA,EAAGgG,WAAW,EAA6B,CAAC,CAAA,EAAGE,QAAQ,CAC3I,GAAI,AAACrM,CAAAA,GAAW,CAAC,CAAA,EAAG9C,SAAS,EACzBoG,EAAO6C,iBAAiB,EACxB,AAAmD,IAAnD,AAAC7C,CAAAA,EAAO2G,KAAK,CAACG,OAAO,EAAEyL,WAAa,EAAE,AAAD,EAAGnX,MAAM,EAC9C,AAAmD,QAAnD,AAAC,CAAA,AAAC4E,CAAAA,EAAOyG,KAAK,EAAI,CAAC,CAAA,EAAG+L,SAAS,EAAI,CAAC,CAAA,EAAGC,OAAO,EAC9C1J,GACApQ,OAAOiF,IAAI,CAACmL,GAAU3N,MAAM,CAAE,CAC9B,IAAK,IAAM3B,KAAWuG,EAAO6C,iBAAiB,CAAChC,QAAQ,CACnDb,EAAOwR,mBAAmB,CAAC/X,GAE/B,IAAK,IAAMqH,KAASd,EAAO6C,iBAAiB,CAAC/B,KAAK,CAC9Cd,EAAOwR,mBAAmB,CAAC1Q,EAEnC,CACJ,CAER,CAEA,SAAS4R,GAAqCrM,CAAK,EAC/C,IAA2GsM,EAAmB,AAAC,CAAA,AAAC5H,CAAAA,AAA7C/K,AAA/B4E,AAAtCyB,CAAAA,EAAMzB,KAAK,EAAIyB,EAAMC,MAAM,AAAD,EAAkBtG,MAAM,CAA0BtD,OAAO,CAACjD,OAAO,EAAyC,CAAC,CAAA,EAAGmZ,MAAM,EAAI,CAAC,CAAA,EAAG9Y,cAAc,AAC9KkY,CAAAA,GAA0BW,IAC1BA,EAAiBxZ,IAAI,CAAC,IAAI,CAAEkN,EAEpC,CAMA,SAASwM,KAEL,GAAIjO,AADU,IAAI,CACRkO,SAAS,CAIf,OAHAf,GAAqB,qHAEK,CAAA,EAAOnN,AAJvB,IAAI,CAIyB5E,MAAM,CAAC2G,KAAK,EAC5C,CAAA,CAEf,CAKA,SAASoM,KACL,IAAqBC,EAAqB,AAAChT,CAAAA,AAA5B,IAAI,CAA+BtD,OAAO,CAACjD,OAAO,EAAI,CAAC,CAAA,EAAGK,cAAc,CACvF,GAAIkG,AADW,IAAI,CACR6C,iBAAiB,EAAI7C,AADjB,IAAI,CACoB6C,iBAAiB,CAAChC,QAAQ,CAC7D,IAAK,IAAMpH,KAAWuG,AAFX,IAAI,CAEc6C,iBAAiB,CAAChC,QAAQ,CAC/CpH,EAAQmL,KAAK,EAAInL,EAAQmL,KAAK,CAACK,OAAO,GACtCxL,EAAQmL,KAAK,CAACK,OAAO,CAACgO,QAAQ,CAAC,4BAE3BD,GAAsBvZ,EAAQmL,KAAK,GACnCnL,EAAQmL,KAAK,CAACK,OAAO,CAACiO,GAAG,CAAC,CACtBC,OAAQ,SACZ,GACI1Z,EAAQmL,KAAK,CAACO,SAAS,EACvB1L,EAAQmL,KAAK,CAACO,SAAS,CAAC+N,GAAG,CAAC,CACxBC,OAAQ,SACZ,IAGJrB,GAAuBrY,EAAQsK,WAAW,GAC1CtK,EAAQmL,KAAK,CAACK,OAAO,CAACgO,QAAQ,CAACxZ,EAAQ2T,oBAAoB,EACvD,2BACI3T,EAAQsK,WAAW,CAAC0J,SAAS,EAKzD,CA0DA,SAAS2F,GAA6BzT,CAAC,CAAEC,CAAC,CAAEwI,CAAK,CAAEC,CAAM,EACrD,IAAMgL,EAAIjL,EAAQ,EAAGkL,EAAIjL,EAAS,EAA8BkL,EAAQ5b,EAAQ6b,GAAG,CAAC7T,EAAI0T,EAAGzT,EAAI0T,EAAGD,EAAII,EAAWH,EAAIG,EAAW,CAC5HhY,MAAO0B,AAAU,GAAVA,KAAKuW,EAAE,CACdC,IAAKxW,AAAU,IAAVA,KAAKuW,EAAE,CACZE,KAAM,CAAA,CACV,GAAIC,EAASlc,EAAQ6b,GAAG,CAAC7T,EAAI0T,EAAGzT,EAAI0T,EAAGD,EAAII,EAAWH,EAAIG,EAAW,CACjEhY,MAAO0B,AAAU,GAAVA,KAAKuW,EAAE,CACdC,IAAKxW,AAAU,IAAVA,KAAKuW,EAAE,CACZI,OAAQT,EAAIU,EACZH,KAAM,CAAA,CACV,GAMA,OAAOI,AANMrc,EAAQ6b,GAAG,CAAC7T,EAAI0T,EAAGzT,EAAI0T,EAAGD,EATsB,EASXC,EATW,EASA,CACzD7X,MAAO0B,AAAU,GAAVA,KAAKuW,EAAE,CACdC,IAAKxW,AAAU,IAAVA,KAAKuW,EAAE,CACZI,OAAQT,EACRO,KAAM,CAAA,CACV,GACcK,MAAM,CAACJ,EAAQN,EACjC,CA9LA,AAAC5B,CAAAA,GAAeF,WAAW,EAAI,CAAC,CAAA,EAAGzR,MAAM,CAAGiS,GAAqB,AAACN,CAAAA,GAAeF,WAAW,EAAI,CAAC,CAAA,EAAGzR,MAAM,CAAExG,GAsN5G,IAAM0a,GAAK3a,IACX4a,AA9FuB,CAAA,CACnBhD,QAnHJ,SAAgCiD,CAAS,CAAEC,CAAU,CAAEjD,CAAwB,CAAEkD,CAAW,EACxF,GAAIpC,GAAWN,GAAU,kBAAmB,CACxC,IAAM2C,EAAaD,EAAYrb,SAAS,CAACub,UAAU,CAAE,CAAEC,QAASC,CAAa,CAAE,CAAGJ,EAAYK,KAAK,CACnG9C,GAAwBuC,EAAW,cAAehC,IAClDP,GAAwBwC,EAAY,SAAU/B,IAC9CT,GAAwB0C,EAAY,iBAAkB7B,IACtDb,GAAwB0C,EAAY,SAAU1B,IAC9ChB,GAAwByC,EAAa,cAAevB,IAChD2B,GACAxD,GACKC,OAAO,CAACC,EAA0BsD,EAE/C,CACJ,CAuGA,CAAA,EA4F8BvD,OAAO,CAAC+C,GAAEU,IAAI,CAAEV,GAAEW,KAAK,CAAEX,GAAEvC,cAAc,CAAEuC,GAAEY,MAAM,EACjFC,AAb6B,CAAA,CACzB5D,QAVJ,SAAsC6D,CAAgB,EAElDrd,AADAA,CAAAA,EAAUqd,EAAiB/b,SAAS,CAACtB,OAAO,AAAD,EACnC8B,OAAO,CAAG2Z,EACtB,CAQA,CAAA,EAWoCjC,OAAO,CAAC+C,GAAEe,WAAW,EAC5B,IAAM5b,GAAwBE,IAGjD,OADYH,EAAoB,OAAU,AAE3C,CAAA"}