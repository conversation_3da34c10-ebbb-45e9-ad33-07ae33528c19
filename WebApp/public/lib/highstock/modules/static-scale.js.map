{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts Gantt JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/static-scale\n * @requires highcharts\n *\n * StaticScale\n *\n * (c) 2016-2025 <PERSON><PERSON>, Lars <PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/static-scale\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/static-scale\"] = factory(root[\"_Highcharts\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ static_scale_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Extensions/StaticScale.js\n/* *\n *\n *  (c) 2016-2025 Torstein Honsi, Lars Cabrera\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { addEvent, defined, isNumber, pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\n/** @private */\nfunction compose(AxisClass, ChartClass) {\n    const chartProto = ChartClass.prototype;\n    if (!chartProto.adjustHeight) {\n        addEvent(AxisClass, 'afterSetOptions', onAxisAfterSetOptions);\n        chartProto.adjustHeight = chartAdjustHeight;\n        addEvent(ChartClass, 'render', chartProto.adjustHeight);\n    }\n}\n/** @private */\nfunction onAxisAfterSetOptions() {\n    const chartOptions = this.chart.options.chart;\n    if (!this.horiz &&\n        isNumber(this.options.staticScale) &&\n        (!chartOptions.height ||\n            (chartOptions.scrollablePlotArea &&\n                chartOptions.scrollablePlotArea.minHeight))) {\n        this.staticScale = this.options.staticScale;\n    }\n}\n/** @private */\nfunction chartAdjustHeight() {\n    const chart = this;\n    if (chart.redrawTrigger !== 'adjustHeight') {\n        for (const axis of (chart.axes || [])) {\n            const chart = axis.chart, animate = !!chart.initiatedScale &&\n                chart.options.animation, staticScale = axis.options.staticScale;\n            if (axis.staticScale && defined(axis.min)) {\n                let height = pick(axis.brokenAxis && axis.brokenAxis.unitLength, axis.max + axis.tickInterval - axis.min) * staticScale;\n                // Minimum height is 1 x staticScale.\n                height = Math.max(height, staticScale);\n                const diff = height - chart.plotHeight;\n                if (!chart.scrollablePixelsY && Math.abs(diff) >= 1) {\n                    chart.plotHeight = height;\n                    chart.redrawTrigger = 'adjustHeight';\n                    chart.setSize(void 0, chart.chartHeight + diff, animate);\n                }\n                // Make sure clip rects have the right height before initial\n                // animation.\n                axis.series.forEach(function (series) {\n                    const clipRect = series.sharedClipKey &&\n                        chart.sharedClips[series.sharedClipKey];\n                    if (clipRect) {\n                        clipRect.attr(chart.inverted ? {\n                            width: chart.plotHeight\n                        } : {\n                            height: chart.plotHeight\n                        });\n                    }\n                });\n            }\n        }\n        this.initiatedScale = true;\n    }\n    this.redrawTrigger = null;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst StaticScale = {\n    compose\n};\n/* harmony default export */ const Extensions_StaticScale = (StaticScale);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * For vertical axes only. Setting the static scale ensures that each tick unit\n * is translated into a fixed pixel height. For example, setting the static\n * scale to 24 results in each Y axis category taking up 24 pixels, and the\n * height of the chart adjusts. Adding or removing items will make the chart\n * resize.\n *\n * @sample gantt/xrange-series/demo/\n *         X-range series with static scale\n *\n * @type      {number}\n * @default   50\n * @since     6.2.0\n * @product   gantt\n * @apioption yAxis.staticScale\n */\n''; // Keeps doclets above in JS file\n\n;// ./code/es-modules/masters/modules/static-scale.js\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nExtensions_StaticScale.compose(G.Axis, G.Chart);\n/* harmony default export */ const static_scale_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "static_scale_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "addEvent", "defined", "isNumber", "pick", "onAxisAfterSetOptions", "chartOptions", "chart", "options", "horiz", "staticScale", "height", "scrollablePlotArea", "minHeight", "chartAdjustHeight", "redrawTrigger", "axis", "axes", "animate", "initiatedScale", "animation", "min", "broken<PERSON><PERSON>s", "unitLength", "max", "tickInterval", "diff", "Math", "plotHeight", "scrollablePixelsY", "abs", "setSize", "chartHeight", "series", "for<PERSON>ach", "clipRect", "sharedClipKey", "sharedClips", "attr", "inverted", "width", "G", "Extensions_StaticScale", "compose", "AxisClass", "ChartClass", "chartProto", "adjustHeight", "Axis", "Chart"], "mappings": "CAWA,AAXA;;;;;;;;;;CAUC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,EACrC,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,kCAAmC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAM,GACpG,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,kCAAkC,CAAGD,EAAQD,EAAK,WAAc,EAExEA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CACjD,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,AAACC,GACnC,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIE,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaX,OAAO,CAG5B,IAAIC,EAASO,CAAwB,CAACE,EAAS,CAAG,CAGjDV,QAAS,CAAC,CACX,EAMA,OAHAO,CAAmB,CAACG,EAAS,CAACT,EAAQA,EAAOD,OAAO,CAAES,GAG/CR,EAAOD,OAAO,AACtB,CAMCS,EAAoBI,CAAC,CAAG,AAACZ,IACxB,IAAIa,EAASb,GAAUA,EAAOc,UAAU,CACvC,IAAOd,EAAO,OAAU,CACxB,IAAOA,EAER,OADAQ,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAAChB,EAASkB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACpB,EAASmB,IAC5EE,OAAOC,cAAc,CAACtB,EAASmB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAarH,GAAM,CAAEE,SAAAA,CAAQ,CAAEC,QAAAA,CAAO,CAAEC,SAAAA,CAAQ,CAAEC,KAAAA,CAAI,CAAE,CAAIJ,IAgB/C,SAASK,IACL,IAAMC,EAAe,IAAI,CAACC,KAAK,CAACC,OAAO,CAACD,KAAK,AACzC,EAAC,IAAI,CAACE,KAAK,EACXN,EAAS,IAAI,CAACK,OAAO,CAACE,WAAW,GAChC,CAAA,CAACJ,EAAaK,MAAM,EAChBL,EAAaM,kBAAkB,EAC5BN,EAAaM,kBAAkB,CAACC,SAAS,GACjD,CAAA,IAAI,CAACH,WAAW,CAAG,IAAI,CAACF,OAAO,CAACE,WAAW,AAAD,CAElD,CAEA,SAASI,IAEL,GAAIP,AAAwB,iBAAxBA,AADU,IAAI,CACRQ,aAAa,CAAqB,CACxC,IAAK,IAAMC,KAAST,AAFV,IAAI,CAEYU,IAAI,EAAI,EAAE,CAAG,CACnC,IAAMV,EAAQS,EAAKT,KAAK,CAAEW,EAAU,CAAC,CAACX,EAAMY,cAAc,EACtDZ,EAAMC,OAAO,CAACY,SAAS,CAAEV,EAAcM,EAAKR,OAAO,CAACE,WAAW,CACnE,GAAIM,EAAKN,WAAW,EAAIR,EAAQc,EAAKK,GAAG,EAAG,CACvC,IAAIV,EAASP,EAAKY,EAAKM,UAAU,EAAIN,EAAKM,UAAU,CAACC,UAAU,CAAEP,EAAKQ,GAAG,CAAGR,EAAKS,YAAY,CAAGT,EAAKK,GAAG,EAAIX,EAGtGgB,EAAOf,AADbA,CAAAA,EAASgB,KAAKH,GAAG,CAACb,EAAQD,EAAW,EACfH,EAAMqB,UAAU,AAClC,EAACrB,EAAMsB,iBAAiB,EAAIF,KAAKG,GAAG,CAACJ,IAAS,IAC9CnB,EAAMqB,UAAU,CAAGjB,EACnBJ,EAAMQ,aAAa,CAAG,eACtBR,EAAMwB,OAAO,CAAC,KAAK,EAAGxB,EAAMyB,WAAW,CAAGN,EAAMR,IAIpDF,EAAKiB,MAAM,CAACC,OAAO,CAAC,SAAUD,CAAM,EAChC,IAAME,EAAWF,EAAOG,aAAa,EACjC7B,EAAM8B,WAAW,CAACJ,EAAOG,aAAa,CAAC,AACvCD,CAAAA,GACAA,EAASG,IAAI,CAAC/B,EAAMgC,QAAQ,CAAG,CAC3BC,MAAOjC,EAAMqB,UAAU,AAC3B,EAAI,CACAjB,OAAQJ,EAAMqB,UAAU,AAC5B,EAER,EACJ,CACJ,CACA,IAAI,CAACT,cAAc,CAAG,CAAA,CAC1B,CACA,IAAI,CAACJ,aAAa,CAAG,IACzB,CAsCA,IAAM0B,EAAKzC,IACX0C,AAjCoB,CAAA,CAChBC,QA7DJ,SAAiBC,CAAS,CAAEC,CAAU,EAClC,IAAMC,EAAaD,EAAWnD,SAAS,AAClCoD,CAAAA,EAAWC,YAAY,GACxB9C,EAAS2C,EAAW,kBAAmBvC,GACvCyC,EAAWC,YAAY,CAAGjC,EAC1Bb,EAAS4C,EAAY,SAAUC,EAAWC,YAAY,EAE9D,CAuDA,CAAA,EA+BuBJ,OAAO,CAACF,EAAEO,IAAI,CAAEP,EAAEQ,KAAK,EACjB,IAAMnD,EAAqBE,IAG9C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}