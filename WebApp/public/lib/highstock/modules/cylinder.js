!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/cylinder
 * @requires highcharts
 * @requires highcharts/highcharts-3d
 *
 * Highcharts cylinder module
 *
 * (c) 2010-2025 Kacper Madej
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.Color,t._Highcharts.RendererRegistry,t._Highcharts.SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/modules/cylinder",["highcharts/highcharts"],function(t){return e(t,t.Color,t.RendererRegistry,t.SeriesRegistry)}):"object"==typeof exports?exports["highcharts/modules/cylinder"]=e(t._Highcharts,t._Highcharts.Color,t._Highcharts.RendererRegistry,t._Highcharts.SeriesRegistry):t.Highcharts=e(t.Highcharts,t.Highcharts.Color,t.Highcharts.RendererRegistry,t.Highcharts.SeriesRegistry)}("undefined"==typeof window?this:window,(t,e,r,s)=>(()=>{"use strict";var n={512:t=>{t.exports=s},608:t=>{t.exports=r},620:t=>{t.exports=e},944:e=>{e.exports=t}},o={};function i(t){var e=o[t];if(void 0!==e)return e.exports;var r=o[t]={exports:{}};return n[t](r,r.exports,i),r.exports}i.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return i.d(e,{a:e}),e},i.d=(t,e)=>{for(var r in e)i.o(e,r)&&!i.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var h={};i.d(h,{default:()=>W});var p=i(944),l=i.n(p);let{deg2rad:a}=l(),{pick:c}=l();function y(t,e,r,s){let n=e.options.chart.options3d,o=c(s,!!r&&e.inverted),i={x:e.plotWidth/2,y:e.plotHeight/2,z:n.depth/2,vd:c(n.depth,1)*c(n.viewDistance,0)},h=e.scale3d||1,p=a*n.beta*(o?-1:1),l=a*n.alpha*(o?-1:1),y={cosA:Math.cos(l),cosB:Math.cos(-p),sinA:Math.sin(l),sinB:Math.sin(-p)};return r||(i.x+=e.plotLeft,i.y+=e.plotTop),t.map(function(t){var e,r,s;let n=(e=(o?t.y:t.x)-i.x,r=(o?t.x:t.y)-i.y,s=(t.z||0)-i.z,{x:y.cosB*e-y.sinB*s,y:-y.sinA*y.sinB*e+y.cosA*r-y.cosB*y.sinA*s,z:y.cosA*y.sinB*e+y.sinA*r+y.cosA*y.cosB*s}),p=u(n,i,i.vd);return p.x=p.x*h+i.x,p.y=p.y*h+i.y,p.z=n.z*h+i.z,{x:o?p.y:p.x,y:o?p.x:p.y,z:p.z}})}function u(t,e,r){let s=r>0&&r<Number.POSITIVE_INFINITY?r/(t.z+e.z+r):1;return{x:t.x*s,y:t.y*s}}function d(t){let e=0,r,s;for(r=0;r<t.length;r++)s=(r+1)%t.length,e+=t[r].x*t[s].y-t[s].x*t[r].y;return e/2}var x=i(620),g=i.n(x),f=i(608),z=i.n(f);let{parse:C}=g(),{Element3D:b}=z().getRendererType().prototype,m=class extends b{constructor(){super(...arguments),this.parts=["top","bottom","front","back"],this.pathType="cylinder"}fillSetter(t){return this.singleSetterForParts("fill",null,{front:t,back:t,top:C(t).brighten(.1).get(),bottom:C(t).brighten(-.1).get()}),this.color=this.fill=t,this}},{charts:M,deg2rad:v}=l(),{perspective:H}={perspective:y,perspective3D:u,pointCameraDistance:function(t,e){let r=e.options.chart.options3d,s={x:e.plotWidth/2,y:e.plotHeight/2,z:c(r.depth,1)*c(r.viewDistance,0)+r.depth};return Math.sqrt(Math.pow(s.x-c(t.plotX,t.x),2)+Math.pow(s.y-c(t.plotY,t.y),2)+Math.pow(s.z-c(t.plotZ,t.z),2))},shapeArea:d,shapeArea3D:function(t,e,r){return d(y(t,e,r))}},{extend:R,pick:T}=l();function _(t){return!t.some(t=>"C"===t[0])}function w(t){return this.element3d("cylinder",t)}function B(t){let e=M[this.chartIndex],r=this.cuboidPath(t),s=!r.isTop,n=!r.isFront,o=this.getCylinderEnd(e,t),i=this.getCylinderEnd(e,t,!0);return{front:this.getCylinderFront(o,i),back:this.getCylinderBack(o,i),top:o,bottom:i,zIndexes:{top:3*!!s,bottom:3*!s,front:n?2:1,back:n?1:2,group:r.zIndexes.group}}}function A(t){let e=[["M",t[0].x,t[0].y]],r=t.length-2;for(let s=1;s<r;s+=3)e.push(["C",t[s].x,t[s].y,t[s+1].x,t[s+1].y,t[s+2].x,t[s+2].y]);return e}function P(t,e){let r=[];if(_(t)){let e=t[0],s=t[2];"M"===e[0]&&"L"===s[0]&&(r.push(["M",s[1],s[2]]),r.push(t[3]),r.push(["L",e[1],e[2]]))}else"C"===t[2][0]&&r.push(["M",t[2][5],t[2][6]]),r.push(t[3],t[4]);if(_(e)){let t=e[0];"M"===t[0]&&(r.push(["L",t[1],t[2]]),r.push(e[3]),r.push(e[2]))}else{let t=e[2],s=e[3],n=e[4];"C"===t[0]&&"C"===s[0]&&"C"===n[0]&&(r.push(["L",n[5],n[6]]),r.push(["C",n[3],n[4],n[1],n[2],s[5],s[6]]),r.push(["C",s[3],s[4],s[1],s[2],t[5],t[6]]))}return r.push(["Z"]),r}function I(t,e,r){let s,n,o,{width:i=0,height:h=0,alphaCorrection:p=0}=e,l=T(e.depth,i,0),a=Math.min(i,l)/2,c=v*(t.options.chart.options3d.beta-90+p),y=(e.y||0)+(r?h:0),u=.5519*a,d=i/2+(e.x||0),x=l/2+(e.z||0),g=[{x:0,y:y,z:a},{x:u,y:y,z:a},{x:a,y:y,z:u},{x:a,y:y,z:0},{x:a,y:y,z:-u},{x:u,y:y,z:-a},{x:0,y:y,z:-a},{x:-u,y:y,z:-a},{x:-a,y:y,z:-u},{x:-a,y:y,z:0},{x:-a,y:y,z:u},{x:-u,y:y,z:a},{x:0,y:y,z:a}],f=Math.cos(c),z=Math.sin(c);for(let t of g)n=t.x,o=t.z,t.x=n*f-o*z+d,t.z=o*f+n*z+x;let C=H(g,t,!0);return 2.5>Math.abs(C[3].y-C[9].y)&&2.5>Math.abs(C[0].y-C[6].y)?this.toLinePath([C[0],C[3],C[6],C[9]],!0):this.getCurvedPath(C)}function L(t,e){let r=t.slice(0,3);if(_(e)){let t=e[0];"M"===t[0]&&(r.push(e[2]),r.push(e[1]),r.push(["L",t[1],t[2]]))}else{let t=e[0],s=e[1],n=e[2];"M"===t[0]&&"C"===s[0]&&"C"===n[0]&&(r.push(["L",n[5],n[6]]),r.push(["C",n[3],n[4],n[1],n[2],s[5],s[6]]),r.push(["C",s[3],s[4],s[1],s[2],t[1],t[2]]))}return r.push(["Z"]),r}var S=i(512),k=i.n(S);let{column:{prototype:{pointClass:D}}}=k().seriesTypes,{extend:E}=l();class O extends D{}E(O.prototype,{shapeType:"cylinder"});let{column:j}=k().seriesTypes,{extend:F,merge:N}=l();class Z extends j{}Z.compose=function(t){let e=t.prototype;e.cylinder||(e.Element3D.types.cylinder=m,R(e,{cylinder:w,cylinderPath:B,getCurvedPath:A,getCylinderBack:P,getCylinderEnd:I,getCylinderFront:L}))},Z.defaultOptions=N(j.defaultOptions,{}),F(Z.prototype,{pointClass:O}),k().registerSeriesType("cylinder",Z),Z.compose(z().getRendererType());let W=l();return h.default})());