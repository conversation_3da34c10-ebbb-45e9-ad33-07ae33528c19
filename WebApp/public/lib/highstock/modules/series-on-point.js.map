{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/series-on-point\n * @requires highcharts\n *\n * Series on point module\n *\n * (c) 2010-2025 Highsoft AS\n * Author: <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>j\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Point\"], root[\"_Highcharts\"][\"Series\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"SVGRenderer\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/series-on-point\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"Point\"],amd1[\"Series\"],amd1[\"SeriesRegistry\"],amd1[\"SVGRenderer\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/series-on-point\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Point\"], root[\"_Highcharts\"][\"Series\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"SVGRenderer\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Point\"], root[\"Highcharts\"][\"Series\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"SVGRenderer\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__260__, __WEBPACK_EXTERNAL_MODULE__820__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__540__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 260:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__260__;\n\n/***/ }),\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 540:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__540__;\n\n/***/ }),\n\n/***/ 820:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__820__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ series_on_point_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Data/ColumnUtils.js\n/* *\n *\n *  (c) 2020-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n/**\n * Utility functions for columns that can be either arrays or typed arrays.\n * @private\n */\nvar ColumnUtils;\n(function (ColumnUtils) {\n    /* *\n    *\n    *  Declarations\n    *\n    * */\n    /* *\n    *\n    * Functions\n    *\n    * */\n    /**\n     * Sets the length of the column array.\n     *\n     * @param {DataTable.Column} column\n     * Column to be modified.\n     *\n     * @param {number} length\n     * New length of the column.\n     *\n     * @param {boolean} asSubarray\n     * If column is a typed array, return a subarray instead of a new array. It\n     * is faster `O(1)`, but the entire buffer will be kept in memory until all\n     * views to it are destroyed. Default is `false`.\n     *\n     * @return {DataTable.Column}\n     * Modified column.\n     *\n     * @private\n     */\n    function setLength(column, length, asSubarray) {\n        if (Array.isArray(column)) {\n            column.length = length;\n            return column;\n        }\n        return column[asSubarray ? 'subarray' : 'slice'](0, length);\n    }\n    ColumnUtils.setLength = setLength;\n    /**\n     * Splices a column array.\n     *\n     * @param {DataTable.Column} column\n     * Column to be modified.\n     *\n     * @param {number} start\n     * Index at which to start changing the array.\n     *\n     * @param {number} deleteCount\n     * An integer indicating the number of old array elements to remove.\n     *\n     * @param {boolean} removedAsSubarray\n     * If column is a typed array, return a subarray instead of a new array. It\n     * is faster `O(1)`, but the entire buffer will be kept in memory until all\n     * views to it are destroyed. Default is `true`.\n     *\n     * @param {Array<number>|TypedArray} items\n     * The elements to add to the array, beginning at the start index. If you\n     * don't specify any elements, `splice()` will only remove elements from the\n     * array.\n     *\n     * @return {SpliceResult}\n     * Object containing removed elements and the modified column.\n     *\n     * @private\n     */\n    function splice(column, start, deleteCount, removedAsSubarray, items = []) {\n        if (Array.isArray(column)) {\n            if (!Array.isArray(items)) {\n                items = Array.from(items);\n            }\n            return {\n                removed: column.splice(start, deleteCount, ...items),\n                array: column\n            };\n        }\n        const Constructor = Object.getPrototypeOf(column)\n            .constructor;\n        const removed = column[removedAsSubarray ? 'subarray' : 'slice'](start, start + deleteCount);\n        const newLength = column.length - deleteCount + items.length;\n        const result = new Constructor(newLength);\n        result.set(column.subarray(0, start), 0);\n        result.set(items, start);\n        result.set(column.subarray(start + deleteCount), start + items.length);\n        return {\n            removed: removed,\n            array: result\n        };\n    }\n    ColumnUtils.splice = splice;\n})(ColumnUtils || (ColumnUtils = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Data_ColumnUtils = (ColumnUtils);\n\n;// ./code/es-modules/Data/DataTableCore.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Gøran Slettemark\n *  - Torstein Hønsi\n *\n * */\n\n\nconst { setLength, splice } = Data_ColumnUtils;\n\nconst { fireEvent, objectEach, uniqueKey } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * Class to manage columns and rows in a table structure. It provides methods\n * to add, remove, and manipulate columns and rows, as well as to retrieve data\n * from specific cells.\n *\n * @class\n * @name Highcharts.DataTable\n *\n * @param {Highcharts.DataTableOptions} [options]\n * Options to initialize the new DataTable instance.\n */\nclass DataTableCore {\n    /**\n     * Constructs an instance of the DataTable class.\n     *\n     * @example\n     * const dataTable = new Highcharts.DataTableCore({\n     *   columns: {\n     *     year: [2020, 2021, 2022, 2023],\n     *     cost: [11, 13, 12, 14],\n     *     revenue: [12, 15, 14, 18]\n     *   }\n     * });\n\n     *\n     * @param {Highcharts.DataTableOptions} [options]\n     * Options to initialize the new DataTable instance.\n     */\n    constructor(options = {}) {\n        /**\n         * Whether the ID was automatic generated or given in the constructor.\n         *\n         * @name Highcharts.DataTable#autoId\n         * @type {boolean}\n         */\n        this.autoId = !options.id;\n        this.columns = {};\n        /**\n         * ID of the table for identification purposes.\n         *\n         * @name Highcharts.DataTable#id\n         * @type {string}\n         */\n        this.id = (options.id || uniqueKey());\n        this.modified = this;\n        this.rowCount = 0;\n        this.versionTag = uniqueKey();\n        let rowCount = 0;\n        objectEach(options.columns || {}, (column, columnName) => {\n            this.columns[columnName] = column.slice();\n            rowCount = Math.max(rowCount, column.length);\n        });\n        this.applyRowCount(rowCount);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Applies a row count to the table by setting the `rowCount` property and\n     * adjusting the length of all columns.\n     *\n     * @private\n     * @param {number} rowCount The new row count.\n     */\n    applyRowCount(rowCount) {\n        this.rowCount = rowCount;\n        objectEach(this.columns, (column, columnName) => {\n            if (column.length !== rowCount) {\n                this.columns[columnName] = setLength(column, rowCount);\n            }\n        });\n    }\n    /**\n     * Delete rows. Simplified version of the full\n     * `DataTable.deleteRows` method.\n     *\n     * @param {number} rowIndex\n     * The start row index\n     *\n     * @param {number} [rowCount=1]\n     * The number of rows to delete\n     *\n     * @return {void}\n     *\n     * @emits #afterDeleteRows\n     */\n    deleteRows(rowIndex, rowCount = 1) {\n        if (rowCount > 0 && rowIndex < this.rowCount) {\n            let length = 0;\n            objectEach(this.columns, (column, columnName) => {\n                this.columns[columnName] =\n                    splice(column, rowIndex, rowCount).array;\n                length = column.length;\n            });\n            this.rowCount = length;\n        }\n        fireEvent(this, 'afterDeleteRows', { rowIndex, rowCount });\n        this.versionTag = uniqueKey();\n    }\n    /**\n     * Fetches the given column by the canonical column name. Simplified version\n     * of the full `DataTable.getRow` method, always returning by reference.\n     *\n     * @param {string} columnName\n     * Name of the column to get.\n     *\n     * @return {Highcharts.DataTableColumn|undefined}\n     * A copy of the column, or `undefined` if not found.\n     */\n    getColumn(columnName, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    asReference) {\n        return this.columns[columnName];\n    }\n    /**\n     * Retrieves all or the given columns. Simplified version of the full\n     * `DataTable.getColumns` method, always returning by reference.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names to retrieve.\n     *\n     * @return {Highcharts.DataTableColumnCollection}\n     * Collection of columns. If a requested column was not found, it is\n     * `undefined`.\n     */\n    getColumns(columnNames, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    asReference) {\n        return (columnNames || Object.keys(this.columns)).reduce((columns, columnName) => {\n            columns[columnName] = this.columns[columnName];\n            return columns;\n        }, {});\n    }\n    /**\n     * Retrieves the row at a given index.\n     *\n     * @param {number} rowIndex\n     * Row index to retrieve. First row has index 0.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names to retrieve.\n     *\n     * @return {Record<string, number|string|undefined>|undefined}\n     * Returns the row values, or `undefined` if not found.\n     */\n    getRow(rowIndex, columnNames) {\n        return (columnNames || Object.keys(this.columns)).map((key) => this.columns[key]?.[rowIndex]);\n    }\n    /**\n     * Sets cell values for a column. Will insert a new column, if not found.\n     *\n     * @param {string} columnName\n     * Column name to set.\n     *\n     * @param {Highcharts.DataTableColumn} [column]\n     * Values to set in the column.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to change. (Default: 0)\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setColumns\n     * @emits #afterSetColumns\n     */\n    setColumn(columnName, column = [], rowIndex = 0, eventDetail) {\n        this.setColumns({ [columnName]: column }, rowIndex, eventDetail);\n    }\n    /**\n     * Sets cell values for multiple columns. Will insert new columns, if not\n     * found. Simplified version of the full `DataTableCore.setColumns`, limited\n     * to full replacement of the columns (undefined `rowIndex`).\n     *\n     * @param {Highcharts.DataTableColumnCollection} columns\n     * Columns as a collection, where the keys are the column names.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to change. Ignored in the `DataTableCore`, as it\n     * always replaces the full column.\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setColumns\n     * @emits #afterSetColumns\n     */\n    setColumns(columns, rowIndex, eventDetail) {\n        let rowCount = this.rowCount;\n        objectEach(columns, (column, columnName) => {\n            this.columns[columnName] = column.slice();\n            rowCount = column.length;\n        });\n        this.applyRowCount(rowCount);\n        if (!eventDetail?.silent) {\n            fireEvent(this, 'afterSetColumns');\n            this.versionTag = uniqueKey();\n        }\n    }\n    /**\n     * Sets cell values of a row. Will insert a new row if no index was\n     * provided, or if the index is higher than the total number of table rows.\n     * A simplified version of the full `DateTable.setRow`, limited to objects.\n     *\n     * @param {Record<string, number|string|undefined>} row\n     * Cell values to set.\n     *\n     * @param {number} [rowIndex]\n     * Index of the row to set. Leave `undefined` to add as a new row.\n     *\n     * @param {boolean} [insert]\n     * Whether to insert the row at the given index, or to overwrite the row.\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #afterSetRows\n     */\n    setRow(row, rowIndex = this.rowCount, insert, eventDetail) {\n        const { columns } = this, indexRowCount = insert ? this.rowCount + 1 : rowIndex + 1;\n        objectEach(row, (cellValue, columnName) => {\n            let column = columns[columnName] ||\n                eventDetail?.addColumns !== false && new Array(indexRowCount);\n            if (column) {\n                if (insert) {\n                    column = splice(column, rowIndex, 0, true, [cellValue]).array;\n                }\n                else {\n                    column[rowIndex] = cellValue;\n                }\n                columns[columnName] = column;\n            }\n        });\n        if (indexRowCount > this.rowCount) {\n            this.applyRowCount(indexRowCount);\n        }\n        if (!eventDetail?.silent) {\n            fireEvent(this, 'afterSetRows');\n            this.versionTag = uniqueKey();\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Data_DataTableCore = (DataTableCore);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * A typed array.\n * @typedef {Int8Array|Uint8Array|Uint8ClampedArray|Int16Array|Uint16Array|Int32Array|Uint32Array|Float32Array|Float64Array} Highcharts.TypedArray\n * //**\n * A column of values in a data table.\n * @typedef {Array<boolean|null|number|string|undefined>|Highcharts.TypedArray} Highcharts.DataTableColumn\n */ /**\n* A collection of data table columns defined by a object where the key is the\n* column name and the value is an array of the column values.\n* @typedef {Record<string, Highcharts.DataTableColumn>} Highcharts.DataTableColumnCollection\n*/\n/**\n * Options for the `DataTable` or `DataTableCore` classes.\n * @interface Highcharts.DataTableOptions\n */ /**\n* The column options for the data table. The columns are defined by an object\n* where the key is the column ID and the value is an array of the column\n* values.\n*\n* @name Highcharts.DataTableOptions.columns\n* @type {Highcharts.DataTableColumnCollection|undefined}\n*/ /**\n* Custom ID to identify the new DataTable instance.\n*\n* @name Highcharts.DataTableOptions.id\n* @type {string|undefined}\n*/\n(''); // Keeps doclets above in JS file\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Point\"],\"commonjs\":[\"highcharts\",\"Point\"],\"commonjs2\":[\"highcharts\",\"Point\"],\"root\":[\"Highcharts\",\"Point\"]}\nvar highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_ = __webpack_require__(260);\nvar highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default = /*#__PURE__*/__webpack_require__.n(highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Series\"],\"commonjs\":[\"highcharts\",\"Series\"],\"commonjs2\":[\"highcharts\",\"Series\"],\"root\":[\"Highcharts\",\"Series\"]}\nvar highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_ = __webpack_require__(820);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SVGRenderer\"],\"commonjs\":[\"highcharts\",\"SVGRenderer\"],\"commonjs2\":[\"highcharts\",\"SVGRenderer\"],\"root\":[\"Highcharts\",\"SVGRenderer\"]}\nvar highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_ = __webpack_require__(540);\nvar highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default = /*#__PURE__*/__webpack_require__.n(highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_);\n;// ./code/es-modules/Series/SeriesOnPointComposition.js\n/* *\n *\n *  (c) 2010-2025 Rafal Sebestjanski, Piotr Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\n\nconst { bubble } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\n\nconst { addEvent, defined, find, isNumber, pushUnique } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\nvar SeriesOnPointComposition;\n(function (SeriesOnPointComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Extends the series with a small addition.\n     *\n     * @private\n     *\n     * @param SeriesClass\n     * Series class to use.\n     *\n     * @param ChartClass\n     * Chart class to use.\n     */\n    function compose(SeriesClass, ChartClass) {\n        if (pushUnique(composed, 'SeriesOnPoint')) {\n            const { chartGetZData, seriesAfterInit, seriesAfterRender, seriesGetCenter, seriesShowOrHide, seriesTranslate } = Additions.prototype;\n            // We can mark support for pie series here because it's in the core.\n            // But all other series outside the core should be marked in its\n            // module. This is crucial when loading series-on-point before\n            // loading a module, e.g. sunburst.\n            // Supported series types:\n            // - pie\n            // - sunburst\n            SeriesClass.types.pie.prototype.onPointSupported = true;\n            addEvent(SeriesClass, 'afterInit', seriesAfterInit);\n            addEvent(SeriesClass, 'afterRender', seriesAfterRender);\n            addEvent(SeriesClass, 'afterGetCenter', seriesGetCenter);\n            addEvent(SeriesClass, 'hide', seriesShowOrHide);\n            addEvent(SeriesClass, 'show', seriesShowOrHide);\n            addEvent(SeriesClass, 'translate', seriesTranslate);\n            addEvent(ChartClass, 'beforeRender', chartGetZData);\n            addEvent(ChartClass, 'beforeRedraw', chartGetZData);\n        }\n        return SeriesClass;\n    }\n    SeriesOnPointComposition.compose = compose;\n    /* *\n     *\n     *  Classes\n     *\n     * */\n    /**\n     * @private\n     */\n    class Additions {\n        /* *\n         *\n         *  Constructors\n         *\n         * */\n        /**\n         * @private\n         */\n        constructor(series) {\n            /**\n             * @ignore\n             */\n            this.getColumn = bubble.prototype.getColumn;\n            /**\n             * @ignore\n             */\n            this.getRadii = bubble.prototype.getRadii;\n            /**\n             * @ignore\n             */\n            this.getRadius = bubble.prototype.getRadius;\n            /**\n             * @ignore\n             */\n            this.getPxExtremes = bubble.prototype.getPxExtremes;\n            /**\n             * @ignore\n             */\n            this.getZExtremes = bubble.prototype.getZExtremes;\n            this.chart = series.chart;\n            this.series = series;\n            this.options = series.options.onPoint;\n        }\n        /**\n         * Draw connector line that starts from the initial point's position\n         * and ends in the center of the series.\n         * @private\n         */\n        drawConnector() {\n            if (!this.connector) {\n                this.connector = this.series.chart.renderer.path()\n                    .addClass('highcharts-connector-seriesonpoint')\n                    .attr({\n                    zIndex: -1\n                })\n                    .add(this.series.markerGroup);\n            }\n            const attribs = this.getConnectorAttributes();\n            attribs && this.connector.animate(attribs);\n        }\n        /**\n         * Get connector line path and styles that connects series and point.\n         *\n         * @private\n         *\n         * @return {Highcharts.SVGAttributes} attribs - the path and styles.\n         */\n        getConnectorAttributes() {\n            const chart = this.series.chart, onPointOptions = this.options;\n            if (!onPointOptions) {\n                return;\n            }\n            const connectorOpts = onPointOptions.connectorOptions || {}, position = onPointOptions.position, connectedPoint = chart.get(onPointOptions.id);\n            if (!(connectedPoint instanceof (highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default())) ||\n                !position ||\n                !defined(connectedPoint.plotX) ||\n                !defined(connectedPoint.plotY)) {\n                return;\n            }\n            const xFrom = defined(position.x) ?\n                position.x :\n                connectedPoint.plotX, yFrom = defined(position.y) ?\n                position.y :\n                connectedPoint.plotY, xTo = xFrom + (position.offsetX || 0), yTo = yFrom + (position.offsetY || 0), width = connectorOpts.width || 1, color = connectorOpts.stroke || this.series.color, dashStyle = connectorOpts.dashstyle, attribs = {\n                d: highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default().prototype.crispLine([\n                    ['M', xFrom, yFrom],\n                    ['L', xTo, yTo]\n                ], width),\n                'stroke-width': width\n            };\n            if (!chart.styledMode) {\n                attribs.stroke = color;\n                attribs.dashstyle = dashStyle;\n            }\n            return attribs;\n        }\n        /**\n         * Initialize Series on point on series init.\n         *\n         * @ignore\n         */\n        seriesAfterInit() {\n            if (this.onPointSupported && this.options.onPoint) {\n                this.bubblePadding = true;\n                this.useMapGeometry = true;\n                this.onPoint = new Additions(this);\n            }\n        }\n        /**\n         * @ignore\n         */\n        seriesAfterRender() {\n            // Clear bubbleZExtremes to reset z calculations on update.\n            delete this.chart.bubbleZExtremes;\n            this.onPoint && this.onPoint.drawConnector();\n        }\n        /**\n         * Recalculate series.center (x, y and size).\n         *\n         * @ignore\n         */\n        seriesGetCenter(e) {\n            const onPointOptions = this.options.onPoint, center = e.positions;\n            if (onPointOptions) {\n                const connectedPoint = this.chart.get(onPointOptions.id);\n                if (connectedPoint instanceof (highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default()) &&\n                    defined(connectedPoint.plotX) &&\n                    defined(connectedPoint.plotY)) {\n                    center[0] = connectedPoint.plotX;\n                    center[1] = connectedPoint.plotY;\n                }\n                const position = onPointOptions.position;\n                if (position) {\n                    if (defined(position.x)) {\n                        center[0] = position.x;\n                    }\n                    if (defined(position.y)) {\n                        center[1] = position.y;\n                    }\n                    if (position.offsetX) {\n                        center[0] += position.offsetX;\n                    }\n                    if (position.offsetY) {\n                        center[1] += position.offsetY;\n                    }\n                }\n            }\n            // Get and set the size\n            const radius = this.radii && this.radii[this.index];\n            if (isNumber(radius)) {\n                center[2] = radius * 2;\n            }\n            e.positions = center;\n        }\n        /**\n         * @ignore\n         */\n        seriesShowOrHide() {\n            const allSeries = this.chart.series;\n            // When toggling a series visibility, loop through all points\n            this.points?.forEach((point) => {\n                // Find all series that are on toggled points\n                const series = find(allSeries, (series) => {\n                    const id = ((series.onPoint || {}).options || {}).id;\n                    if (!id) {\n                        return false;\n                    }\n                    return id === point.id;\n                });\n                // And also toggle series that are on toggled points. Redraw is\n                // not needed because it's fired later after showOrHide event\n                series && series.setVisible(!series.visible, false);\n            });\n        }\n        /**\n         * Calculate required radius (z data) before original translate.\n         *\n         * @ignore\n         * @function Highcharts.Series#translate\n         */\n        seriesTranslate() {\n            if (this.onPoint) {\n                this.onPoint.getRadii();\n                this.radii = this.onPoint.radii;\n            }\n        }\n        /**\n         * @ignore\n         */\n        chartGetZData() {\n            const zData = [];\n            this.series.forEach((series) => {\n                const onPointOpts = series.options.onPoint;\n                zData.push(onPointOpts?.z ?? null);\n            });\n            const dataTable = new Data_DataTableCore({\n                columns: {\n                    z: zData\n                }\n            });\n            this.series.forEach((series) => {\n                // Save z values of all the series\n                if (series.onPoint) {\n                    series.onPoint.dataTable = series.dataTable = dataTable;\n                }\n            });\n        }\n    }\n    SeriesOnPointComposition.Additions = Additions;\n})(SeriesOnPointComposition || (SeriesOnPointComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Series_SeriesOnPointComposition = (SeriesOnPointComposition);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Options for the _Series on point_ feature. Only `pie` and `sunburst` series\n * are supported at this moment.\n *\n * @sample      {highcharts} highcharts/series-on-point/series-on-point\n *              Series on point\n * @sample      {highmaps} maps/demo/map-pies\n *              Pies on a map\n * @requires    modules/series-on-point\n * @since 10.2.0\n * @type        {object}\n * @apioption   plotOptions.series.onPoint\n */\n/**\n * Options for the connector in the _Series on point_ feature.\n *\n * In styled mode, the connector can be styled with the\n * `.highcharts-connector-seriesonpoint` class name.\n *\n * @requires    modules/series-on-point\n * @since 10.2.0\n * @type        {Highcharts.SVGAttributes}\n * @apioption   plotOptions.series.onPoint.connectorOptions\n */\n/**\n * Color of the connector line. By default it's the series' color.\n *\n * @requires    modules/series-on-point\n * @since 10.2.0\n * @type        {string}\n * @apioption   plotOptions.series.onPoint.connectorOptions.stroke\n */\n/**\n * A name for the dash style to use for the connector.\n *\n * @requires    modules/series-on-point\n * @since 10.2.0\n * @type        {string}\n * @apioption   plotOptions.series.onPoint.connectorOptions.dashstyle\n */\n/**\n * Pixel width of the connector line.\n *\n * @default     1\n * @requires    modules/series-on-point\n * @type        {number}\n * @since 10.2.0\n * @apioption   plotOptions.series.onPoint.connectorOptions.width\n */\n/**\n * The `id` of the point that we connect the series to. Only points with a given\n * `plotX` and `plotY` values and map points are valid.\n *\n * @requires   modules/series-on-point\n * @since 10.2.0\n * @type       {string}\n * @apioption  plotOptions.series.onPoint.id\n */\n/**\n * Options allowing to set a position and an offset of the series in the\n * _Series on point_ feature.\n *\n * @requires    modules/series-on-point\n * @since 10.2.0\n * @type        {object}\n * @apioption   plotOptions.series.onPoint.position\n */\n/**\n * Series center offset from the original x position. If defined, the connector\n * line is drawn connecting original position with new position.\n *\n * @requires   modules/series-on-point\n * @since 10.2.0\n * @type       {number}\n * @apioption  plotOptions.series.onPoint.position.offsetX\n */\n/**\n * Series center offset from the original y position. If defined, the connector\n * line is drawn from original position to a new position.\n *\n * @requires   modules/series-on-point\n * @since 10.2.0\n * @type       {number}\n * @apioption  plotOptions.series.onPoint.position.offsetY\n */\n/**\n * X position of the series center. By default, the series is displayed on the\n * point that it is connected to.\n *\n * @requires   modules/series-on-point\n * @since 10.2.0\n * @type       {number}\n * @apioption  plotOptions.series.onPoint.position.x\n */\n/**\n * Y position of the series center. By default, the series is displayed on the\n * point that it is connected to.\n *\n * @requires   modules/series-on-point\n * @since 10.2.0\n * @type       {number}\n * @apioption  plotOptions.series.onPoint.position.y\n */\n''; // Keeps doclets above in transpiled file\n\n;// ./code/es-modules/masters/modules/series-on-point.js\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nSeries_SeriesOnPointComposition.compose(G.Series, G.Chart);\n/* harmony default export */ const series_on_point_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__260__", "__WEBPACK_EXTERNAL_MODULE__820__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__540__", "Column<PERSON><PERSON><PERSON>", "SeriesOnPointComposition", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "series_on_point_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "<PERSON><PERSON><PERSON><PERSON>", "column", "length", "as<PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "splice", "start", "deleteCount", "removedAsSubarray", "items", "from", "removed", "array", "<PERSON><PERSON><PERSON><PERSON>", "getPrototypeOf", "constructor", "result", "set", "subarray", "fireEvent", "objectEach", "<PERSON><PERSON><PERSON>", "Data_DataTableCore", "options", "autoId", "id", "columns", "modified", "rowCount", "versionTag", "columnName", "slice", "Math", "max", "applyRowCount", "deleteRows", "rowIndex", "getColumn", "asReference", "getColumns", "columnNames", "keys", "reduce", "getRow", "map", "setColumn", "eventDetail", "setColumns", "silent", "setRow", "row", "insert", "indexRowCount", "cellValue", "addColumns", "highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_", "highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_", "highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default", "composed", "bubble", "seriesTypes", "addEvent", "defined", "find", "isNumber", "pushUnique", "compose", "SeriesClass", "ChartClass", "chartGetZData", "seriesAfterInit", "seriesAfterRender", "seriesGetCenter", "seriesShowOrHide", "seriesTranslate", "Additions", "types", "pie", "onPointSupported", "series", "getRadii", "getRadius", "getPxExtremes", "getZExtremes", "chart", "onPoint", "drawConnector", "connector", "renderer", "path", "addClass", "attr", "zIndex", "add", "markerGroup", "attribs", "getConnectorAttributes", "animate", "onPointOptions", "connectorOpts", "connectorOptions", "position", "connectedPoint", "plotX", "plotY", "xFrom", "x", "yFrom", "y", "xTo", "offsetX", "yTo", "offsetY", "width", "color", "stroke", "dashStyle", "dashstyle", "crispLine", "styledMode", "bubblePadding", "useMapGeometry", "bubbleZExtremes", "e", "center", "positions", "radius", "radii", "index", "allSeries", "points", "for<PERSON>ach", "point", "setVisible", "visible", "zData", "onPointOpts", "push", "z", "dataTable", "Series_SeriesOnPointComposition", "G", "Series", "Chart"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,MAAS,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,WAAc,EAC7K,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,qCAAsC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,KAAQ,CAACA,EAAK,MAAS,CAACA,EAAK,cAAiB,CAACA,EAAK,WAAc,CAAE,GAC/K,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,qCAAqC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,MAAS,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,WAAc,EAEnNA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,MAAS,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,WAAc,CACrL,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,IAC3K,AAAC,CAAA,KACP,aACA,IAyHNC,EA4bAC,EArjBUC,EAAuB,CAE/B,IACC,AAACZ,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGS,CAEX,EAEA,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGU,CAEX,EAEA,IACC,AAACT,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIQ,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAajB,OAAO,CAG5B,IAAIC,EAASa,CAAwB,CAACE,EAAS,CAAG,CAGjDhB,QAAS,CAAC,CACX,EAMA,OAHAa,CAAmB,CAACG,EAAS,CAACf,EAAQA,EAAOD,OAAO,CAAEe,GAG/Cd,EAAOD,OAAO,AACtB,CAMCe,EAAoBI,CAAC,CAAG,AAAClB,IACxB,IAAImB,EAASnB,GAAUA,EAAOoB,UAAU,CACvC,IAAOpB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAc,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACtB,EAASwB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAAC1B,EAASyB,IAC5EE,OAAOC,cAAc,CAAC5B,EAASyB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,IAmBrH,AAAC,SAAU3B,CAAW,EAqClBA,EAAY6B,SAAS,CAPrB,SAAmBC,CAAM,CAAEC,CAAM,CAAEC,CAAU,SACzC,AAAIC,MAAMC,OAAO,CAACJ,IACdA,EAAOC,MAAM,CAAGA,EACTD,GAEJA,CAAM,CAACE,EAAa,WAAa,QAAQ,CAAC,EAAGD,EACxD,EAoDA/B,EAAYmC,MAAM,CAvBlB,SAAgBL,CAAM,CAAEM,CAAK,CAAEC,CAAW,CAAEC,CAAiB,CAAEC,EAAQ,EAAE,EACrE,GAAIN,MAAMC,OAAO,CAACJ,GAId,OAHI,AAACG,MAAMC,OAAO,CAACK,IACfA,CAAAA,EAAQN,MAAMO,IAAI,CAACD,EAAK,EAErB,CACHE,QAASX,EAAOK,MAAM,CAACC,EAAOC,KAAgBE,GAC9CG,MAAOZ,CACX,EAEJ,IAAMa,EAAc3B,OAAO4B,cAAc,CAACd,GACrCe,WAAW,CACVJ,EAAUX,CAAM,CAACQ,EAAoB,WAAa,QAAQ,CAACF,EAAOA,EAAQC,GAE1ES,EAAS,IAAIH,EADDb,EAAOC,MAAM,CAAGM,EAAcE,EAAMR,MAAM,EAK5D,OAHAe,EAAOC,GAAG,CAACjB,EAAOkB,QAAQ,CAAC,EAAGZ,GAAQ,GACtCU,EAAOC,GAAG,CAACR,EAAOH,GAClBU,EAAOC,GAAG,CAACjB,EAAOkB,QAAQ,CAACZ,EAAQC,GAAcD,EAAQG,EAAMR,MAAM,EAC9D,CACHU,QAASA,EACTC,MAAOI,CACX,CACJ,CAEJ,EAAG9C,GAAgBA,CAAAA,EAAc,CAAC,CAAA,GAyBlC,GAAM,CAAE6B,UAAAA,CAAS,CAAEM,OAAAA,CAAM,CAAE,CAnB4BnC,EAqBjD,CAAEiD,UAAAA,CAAS,CAAEC,WAAAA,CAAU,CAAEC,UAAAA,CAAS,CAAE,CAAIvB,IA+PXwB,EA9OnC,MAiBIP,YAAYQ,EAAU,CAAC,CAAC,CAAE,CAOtB,IAAI,CAACC,MAAM,CAAG,CAACD,EAAQE,EAAE,CACzB,IAAI,CAACC,OAAO,CAAG,CAAC,EAOhB,IAAI,CAACD,EAAE,CAAIF,EAAQE,EAAE,EAAIJ,IACzB,IAAI,CAACM,QAAQ,CAAG,IAAI,CACpB,IAAI,CAACC,QAAQ,CAAG,EAChB,IAAI,CAACC,UAAU,CAAGR,IAClB,IAAIO,EAAW,EACfR,EAAWG,EAAQG,OAAO,EAAI,CAAC,EAAG,CAAC1B,EAAQ8B,KACvC,IAAI,CAACJ,OAAO,CAACI,EAAW,CAAG9B,EAAO+B,KAAK,GACvCH,EAAWI,KAAKC,GAAG,CAACL,EAAU5B,EAAOC,MAAM,CAC/C,GACA,IAAI,CAACiC,aAAa,CAACN,EACvB,CAaAM,cAAcN,CAAQ,CAAE,CACpB,IAAI,CAACA,QAAQ,CAAGA,EAChBR,EAAW,IAAI,CAACM,OAAO,CAAE,CAAC1B,EAAQ8B,KAC1B9B,EAAOC,MAAM,GAAK2B,GAClB,CAAA,IAAI,CAACF,OAAO,CAACI,EAAW,CAAG/B,EAAUC,EAAQ4B,EAAQ,CAE7D,EACJ,CAeAO,WAAWC,CAAQ,CAAER,EAAW,CAAC,CAAE,CAC/B,GAAIA,EAAW,GAAKQ,EAAW,IAAI,CAACR,QAAQ,CAAE,CAC1C,IAAI3B,EAAS,EACbmB,EAAW,IAAI,CAACM,OAAO,CAAE,CAAC1B,EAAQ8B,KAC9B,IAAI,CAACJ,OAAO,CAACI,EAAW,CACpBzB,EAAOL,EAAQoC,EAAUR,GAAUhB,KAAK,CAC5CX,EAASD,EAAOC,MAAM,AAC1B,GACA,IAAI,CAAC2B,QAAQ,CAAG3B,CACpB,CACAkB,EAAU,IAAI,CAAE,kBAAmB,CAAEiB,SAAAA,EAAUR,SAAAA,CAAS,GACxD,IAAI,CAACC,UAAU,CAAGR,GACtB,CAWAgB,UAAUP,CAAU,CAEpBQ,CAAW,CAAE,CACT,OAAO,IAAI,CAACZ,OAAO,CAACI,EAAW,AACnC,CAYAS,WAAWC,CAAW,CAEtBF,CAAW,CAAE,CACT,MAAO,AAACE,CAAAA,GAAetD,OAAOuD,IAAI,CAAC,IAAI,CAACf,OAAO,CAAA,EAAGgB,MAAM,CAAC,CAAChB,EAASI,KAC/DJ,CAAO,CAACI,EAAW,CAAG,IAAI,CAACJ,OAAO,CAACI,EAAW,CACvCJ,GACR,CAAC,EACR,CAaAiB,OAAOP,CAAQ,CAAEI,CAAW,CAAE,CAC1B,MAAO,AAACA,CAAAA,GAAetD,OAAOuD,IAAI,CAAC,IAAI,CAACf,OAAO,CAAA,EAAGkB,GAAG,CAAC,AAAC5D,GAAQ,IAAI,CAAC0C,OAAO,CAAC1C,EAAI,EAAE,CAACoD,EAAS,CAChG,CAmBAS,UAAUf,CAAU,CAAE9B,EAAS,EAAE,CAAEoC,EAAW,CAAC,CAAEU,CAAW,CAAE,CAC1D,IAAI,CAACC,UAAU,CAAC,CAAE,CAACjB,EAAW,CAAE9B,CAAO,EAAGoC,EAAUU,EACxD,CAmBAC,WAAWrB,CAAO,CAAEU,CAAQ,CAAEU,CAAW,CAAE,CACvC,IAAIlB,EAAW,IAAI,CAACA,QAAQ,CAC5BR,EAAWM,EAAS,CAAC1B,EAAQ8B,KACzB,IAAI,CAACJ,OAAO,CAACI,EAAW,CAAG9B,EAAO+B,KAAK,GACvCH,EAAW5B,EAAOC,MAAM,AAC5B,GACA,IAAI,CAACiC,aAAa,CAACN,GACdkB,GAAaE,SACd7B,EAAU,IAAI,CAAE,mBAChB,IAAI,CAACU,UAAU,CAAGR,IAE1B,CAoBA4B,OAAOC,CAAG,CAAEd,EAAW,IAAI,CAACR,QAAQ,CAAEuB,CAAM,CAAEL,CAAW,CAAE,CACvD,GAAM,CAAEpB,QAAAA,CAAO,CAAE,CAAG,IAAI,CAAE0B,EAAgBD,EAAS,IAAI,CAACvB,QAAQ,CAAG,EAAIQ,EAAW,EAClFhB,EAAW8B,EAAK,CAACG,EAAWvB,KACxB,IAAI9B,EAAS0B,CAAO,CAACI,EAAW,EAC5BgB,GAAaQ,aAAe,CAAA,GAAS,AAAInD,MAAMiD,GAC/CpD,IACImD,EACAnD,EAASK,EAAOL,EAAQoC,EAAU,EAAG,CAAA,EAAM,CAACiB,EAAU,EAAEzC,KAAK,CAG7DZ,CAAM,CAACoC,EAAS,CAAGiB,EAEvB3B,CAAO,CAACI,EAAW,CAAG9B,EAE9B,GACIoD,EAAgB,IAAI,CAACxB,QAAQ,EAC7B,IAAI,CAACM,aAAa,CAACkB,GAElBN,GAAaE,SACd7B,EAAU,IAAI,CAAE,gBAChB,IAAI,CAACU,UAAU,CAAGR,IAE1B,CACJ,EA0CA,IAAIkC,EAA+FjF,EAAoB,KACnHkF,EAAmHlF,EAAoBI,CAAC,CAAC6E,GAEtCjF,EAAoB,KAE3H,IAAImF,EAAmInF,EAAoB,KACvJoF,EAAuJpF,EAAoBI,CAAC,CAAC+E,GAE7KE,EAAuHrF,EAAoB,KAC3IsF,EAA2ItF,EAAoBI,CAAC,CAACiF,GAcrK,GAAM,CAAEE,SAAAA,CAAQ,CAAE,CAAI/D,IAIhB,CAAEgE,OAAAA,CAAM,CAAE,CAAG,AAACJ,IAA2IK,WAAW,CAGpK,CAAEC,SAAAA,CAAQ,CAAEC,QAAAA,CAAO,CAAEC,KAAAA,CAAI,CAAEC,SAAAA,CAAQ,CAAEC,WAAAA,CAAU,CAAE,CAAItE,KAO3D,AAAC,SAAU3B,CAAwB,EA4C/BA,EAAyBkG,OAAO,CAtBhC,SAAiBC,CAAW,CAAEC,CAAU,EACpC,GAAIH,EAAWP,EAAU,iBAAkB,CACvC,GAAM,CAAEW,cAAAA,CAAa,CAAEC,gBAAAA,CAAe,CAAEC,kBAAAA,CAAiB,CAAEC,gBAAAA,CAAe,CAAEC,iBAAAA,CAAgB,CAAEC,gBAAAA,CAAe,CAAE,CAAGC,EAAUtF,SAAS,AAQrI8E,CAAAA,EAAYS,KAAK,CAACC,GAAG,CAACxF,SAAS,CAACyF,gBAAgB,CAAG,CAAA,EACnDjB,EAASM,EAAa,YAAaG,GACnCT,EAASM,EAAa,cAAeI,GACrCV,EAASM,EAAa,iBAAkBK,GACxCX,EAASM,EAAa,OAAQM,GAC9BZ,EAASM,EAAa,OAAQM,GAC9BZ,EAASM,EAAa,YAAaO,GACnCb,EAASO,EAAY,eAAgBC,GACrCR,EAASO,EAAY,eAAgBC,EACzC,CACA,OAAOF,CACX,CAUA,OAAMQ,EASF/D,YAAYmE,CAAM,CAAE,CAIhB,IAAI,CAAC7C,SAAS,CAAGyB,EAAOtE,SAAS,CAAC6C,SAAS,CAI3C,IAAI,CAAC8C,QAAQ,CAAGrB,EAAOtE,SAAS,CAAC2F,QAAQ,CAIzC,IAAI,CAACC,SAAS,CAAGtB,EAAOtE,SAAS,CAAC4F,SAAS,CAI3C,IAAI,CAACC,aAAa,CAAGvB,EAAOtE,SAAS,CAAC6F,aAAa,CAInD,IAAI,CAACC,YAAY,CAAGxB,EAAOtE,SAAS,CAAC8F,YAAY,CACjD,IAAI,CAACC,KAAK,CAAGL,EAAOK,KAAK,CACzB,IAAI,CAACL,MAAM,CAAGA,EACd,IAAI,CAAC3D,OAAO,CAAG2D,EAAO3D,OAAO,CAACiE,OAAO,AACzC,CAMAC,eAAgB,CACR,AAAC,IAAI,CAACC,SAAS,EACf,CAAA,IAAI,CAACA,SAAS,CAAG,IAAI,CAACR,MAAM,CAACK,KAAK,CAACI,QAAQ,CAACC,IAAI,GAC3CC,QAAQ,CAAC,sCACTC,IAAI,CAAC,CACNC,OAAQ,EACZ,GACKC,GAAG,CAAC,IAAI,CAACd,MAAM,CAACe,WAAW,CAAA,EAEpC,IAAMC,EAAU,IAAI,CAACC,sBAAsB,EAC3CD,CAAAA,GAAW,IAAI,CAACR,SAAS,CAACU,OAAO,CAACF,EACtC,CAQAC,wBAAyB,CACrB,IAAMZ,EAAQ,IAAI,CAACL,MAAM,CAACK,KAAK,CAAEc,EAAiB,IAAI,CAAC9E,OAAO,CAC9D,GAAI,CAAC8E,EACD,OAEJ,IAAMC,EAAgBD,EAAeE,gBAAgB,EAAI,CAAC,EAAGC,EAAWH,EAAeG,QAAQ,CAAEC,EAAiBlB,EAAMlG,GAAG,CAACgH,EAAe5E,EAAE,EAC7I,GAAI,CAAEgF,CAAAA,aAA2BjD,GAAqG,GAClI,CAACgD,GACD,CAACvC,EAAQwC,EAAeC,KAAK,GAC7B,CAACzC,EAAQwC,EAAeE,KAAK,EAC7B,OAEJ,IAAMC,EAAQ3C,EAAQuC,EAASK,CAAC,EAC5BL,EAASK,CAAC,CACVJ,EAAeC,KAAK,CAAEI,EAAQ7C,EAAQuC,EAASO,CAAC,EAChDP,EAASO,CAAC,CACVN,EAAeE,KAAK,CAAEK,EAAMJ,EAASJ,CAAAA,EAASS,OAAO,EAAI,CAAA,EAAIC,EAAMJ,EAASN,CAAAA,EAASW,OAAO,EAAI,CAAA,EAAIC,EAAQd,EAAcc,KAAK,EAAI,EAAGC,EAAQf,EAAcgB,MAAM,EAAI,IAAI,CAACpC,MAAM,CAACmC,KAAK,CAAEE,EAAYjB,EAAckB,SAAS,CAAEtB,EAAU,CACxOrH,EAAG+E,IAA8HpE,SAAS,CAACiI,SAAS,CAAC,CACjJ,CAAC,IAAKb,EAAOE,EAAM,CACnB,CAAC,IAAKE,EAAKE,EAAI,CAClB,CAAEE,GACH,eAAgBA,CACpB,EAKA,OAJK7B,EAAMmC,UAAU,GACjBxB,EAAQoB,MAAM,CAAGD,EACjBnB,EAAQsB,SAAS,CAAGD,GAEjBrB,CACX,CAMAzB,iBAAkB,CACV,IAAI,CAACQ,gBAAgB,EAAI,IAAI,CAAC1D,OAAO,CAACiE,OAAO,GAC7C,IAAI,CAACmC,aAAa,CAAG,CAAA,EACrB,IAAI,CAACC,cAAc,CAAG,CAAA,EACtB,IAAI,CAACpC,OAAO,CAAG,IAAIV,EAAU,IAAI,EAEzC,CAIAJ,mBAAoB,CAEhB,OAAO,IAAI,CAACa,KAAK,CAACsC,eAAe,CACjC,IAAI,CAACrC,OAAO,EAAI,IAAI,CAACA,OAAO,CAACC,aAAa,EAC9C,CAMAd,gBAAgBmD,CAAC,CAAE,CACf,IAAMzB,EAAiB,IAAI,CAAC9E,OAAO,CAACiE,OAAO,CAAEuC,EAASD,EAAEE,SAAS,CACjE,GAAI3B,EAAgB,CAChB,IAAMI,EAAiB,IAAI,CAAClB,KAAK,CAAClG,GAAG,CAACgH,EAAe5E,EAAE,EACnDgF,aAA2BjD,KAC3BS,EAAQwC,EAAeC,KAAK,GAC5BzC,EAAQwC,EAAeE,KAAK,IAC5BoB,CAAM,CAAC,EAAE,CAAGtB,EAAeC,KAAK,CAChCqB,CAAM,CAAC,EAAE,CAAGtB,EAAeE,KAAK,EAEpC,IAAMH,EAAWH,EAAeG,QAAQ,CACpCA,IACIvC,EAAQuC,EAASK,CAAC,GAClBkB,CAAAA,CAAM,CAAC,EAAE,CAAGvB,EAASK,CAAC,AAADA,EAErB5C,EAAQuC,EAASO,CAAC,GAClBgB,CAAAA,CAAM,CAAC,EAAE,CAAGvB,EAASO,CAAC,AAADA,EAErBP,EAASS,OAAO,EAChBc,CAAAA,CAAM,CAAC,EAAE,EAAIvB,EAASS,OAAO,AAAD,EAE5BT,EAASW,OAAO,EAChBY,CAAAA,CAAM,CAAC,EAAE,EAAIvB,EAASW,OAAO,AAAD,EAGxC,CAEA,IAAMc,EAAS,IAAI,CAACC,KAAK,EAAI,IAAI,CAACA,KAAK,CAAC,IAAI,CAACC,KAAK,CAAC,AAC/ChE,CAAAA,EAAS8D,IACTF,CAAAA,CAAM,CAAC,EAAE,CAAGE,AAAS,EAATA,CAAS,EAEzBH,EAAEE,SAAS,CAAGD,CAClB,CAIAnD,kBAAmB,CACf,IAAMwD,EAAY,IAAI,CAAC7C,KAAK,CAACL,MAAM,AAEnC,CAAA,IAAI,CAACmD,MAAM,EAAEC,QAAQ,AAACC,IAElB,IAAMrD,EAAShB,EAAKkE,EAAW,AAAClD,IAC5B,IAAMzD,EAAK,AAAC,CAAA,AAACyD,CAAAA,EAAOM,OAAO,EAAI,CAAC,CAAA,EAAGjE,OAAO,EAAI,CAAC,CAAA,EAAGE,EAAE,OACpD,CAAI,CAACA,GAGEA,IAAO8G,EAAM9G,EAAE,AAC1B,EAGAyD,CAAAA,GAAUA,EAAOsD,UAAU,CAAC,CAACtD,EAAOuD,OAAO,CAAE,CAAA,EACjD,EACJ,CAOA5D,iBAAkB,CACV,IAAI,CAACW,OAAO,GACZ,IAAI,CAACA,OAAO,CAACL,QAAQ,GACrB,IAAI,CAAC+C,KAAK,CAAG,IAAI,CAAC1C,OAAO,CAAC0C,KAAK,CAEvC,CAIA1D,eAAgB,CACZ,IAAMkE,EAAQ,EAAE,CAChB,IAAI,CAACxD,MAAM,CAACoD,OAAO,CAAC,AAACpD,IACjB,IAAMyD,EAAczD,EAAO3D,OAAO,CAACiE,OAAO,CAC1CkD,EAAME,IAAI,CAACD,GAAaE,GAAK,KACjC,GACA,IAAMC,EAAY,IAAIxH,EAAmB,CACrCI,QAAS,CACLmH,EAAGH,CACP,CACJ,GACA,IAAI,CAACxD,MAAM,CAACoD,OAAO,CAAC,AAACpD,IAEbA,EAAOM,OAAO,EACdN,CAAAA,EAAOM,OAAO,CAACsD,SAAS,CAAG5D,EAAO4D,SAAS,CAAGA,CAAQ,CAE9D,EACJ,CACJ,CACA3K,EAAyB2G,SAAS,CAAGA,CACzC,EAAG3G,GAA6BA,CAAAA,EAA2B,CAAC,CAAA,GAM/B,IAAM4K,EAAmC5K,EAoHhE6K,EAAKlJ,IACXiJ,EAAgC1E,OAAO,CAAC2E,EAAEC,MAAM,CAAED,EAAEE,KAAK,EAC5B,IAAMtJ,EAAwBE,IAGjD,OADYH,EAAoB,OAAU,AAE3C,CAAA"}