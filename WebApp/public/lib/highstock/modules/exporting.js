!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/exporting
 * @requires highcharts
 *
 * Exporting module
 *
 * (c) 2010-2025 Torstein Honsi
 *
 * License: www.highcharts.com/license
 */function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(e._Highcharts,e._Highcharts.AST,e._Highcharts.Chart):"function"==typeof define&&define.amd?define("highcharts/modules/exporting",["highcharts/highcharts"],function(e){return t(e,e.AST,e.Chart)}):"object"==typeof exports?exports["highcharts/modules/exporting"]=t(e._Highcharts,e._Highcharts.AST,e._Highcharts.Chart):e.Highcharts=t(e.Highcharts,e.Highcharts.AST,e.Highcharts.Chart)}("undefined"==typeof window?this:window,(e,t,n)=>(()=>{"use strict";var i,o,r={660:e=>{e.exports=t},944:t=>{t.exports=e},960:e=>{e.exports=n}},s={};function a(e){var t=s[e];if(void 0!==t)return t.exports;var n=s[e]={exports:{}};return r[e](n,n.exports,a),n.exports}a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var l={};a.d(l,{default:()=>eu});var c=a(944),h=a.n(c),p=a(660),d=a.n(p),u=a(960),g=a.n(u);!function(e){e.compose=function(e){return e.navigation||(e.navigation=new t(e)),e};class t{constructor(e){this.updates=[],this.chart=e}addUpdate(e){this.chart.navigation.updates.push(e)}update(e,t){this.updates.forEach(n=>{n.call(this.chart,e,t)})}}e.Additions=t}(i||(i={}));let f=i,{isSafari:m,win:x,win:{document:y}}=h(),{error:b}=h(),w=x.URL||x.webkitURL||x;function v(e){let t=e.replace(/filename=.*;/,"").match(/data:([^;]*)(;base64)?,([A-Z+\d\/]+)/i);if(t&&t.length>3&&x.atob&&x.ArrayBuffer&&x.Uint8Array&&x.Blob&&w.createObjectURL){let e=x.atob(t[3]),n=new x.ArrayBuffer(e.length),i=new x.Uint8Array(n);for(let t=0;t<i.length;++t)i[t]=e.charCodeAt(t);return w.createObjectURL(new x.Blob([i],{type:t[1]}))}}let{isTouchDevice:S}=h(),C={exporting:{allowTableSorting:!0,libURL:"https://code.highcharts.com/12.3.0/lib/",local:!0,type:"image/png",url:`https://export-svg.highcharts.com?v=${h().version}`,pdfFont:{normal:void 0,bold:void 0,bolditalic:void 0,italic:void 0},printMaxWidth:780,scale:2,buttons:{contextButton:{className:"highcharts-contextbutton",menuClassName:"highcharts-contextmenu",symbol:"menu",titleKey:"contextButtonTitle",menuItems:["viewFullscreen","printChart","separator","downloadPNG","downloadJPEG","downloadSVG"]}},menuItemDefinitions:{viewFullscreen:{textKey:"viewFullscreen",onclick:function(){this.fullscreen?.toggle()}},printChart:{textKey:"printChart",onclick:function(){this.exporting?.print()}},separator:{separator:!0},downloadPNG:{textKey:"downloadPNG",onclick:async function(){await this.exporting?.exportChart()}},downloadJPEG:{textKey:"downloadJPEG",onclick:async function(){await this.exporting?.exportChart({type:"image/jpeg"})}},downloadPDF:{textKey:"downloadPDF",onclick:async function(){await this.exporting?.exportChart({type:"application/pdf"})}},downloadSVG:{textKey:"downloadSVG",onclick:async function(){await this.exporting?.exportChart({type:"image/svg+xml"})}}}},lang:{viewFullscreen:"View in full screen",exitFullscreen:"Exit from full screen",printChart:"Print chart",downloadPNG:"Download PNG image",downloadJPEG:"Download JPEG image",downloadPDF:"Download PDF document",downloadSVG:"Download SVG vector image",contextButtonTitle:"Chart context menu"},navigation:{buttonOptions:{symbolSize:14,symbolX:14.5,symbolY:13.5,align:"right",buttonSpacing:5,height:28,y:-5,verticalAlign:"top",width:28,symbolFill:"#666666",symbolStroke:"#666666",symbolStrokeWidth:3,theme:{fill:"#ffffff",padding:5,stroke:"none","stroke-linecap":"round"}},menuStyle:{border:"none",borderRadius:"3px",background:"#ffffff",padding:"0.5em"},menuItemStyle:{background:"none",borderRadius:"3px",color:"#333333",padding:"0.5em",fontSize:S?"0.9em":"0.8em",transition:"background 250ms, color 250ms"},menuItemHoverStyle:{background:"#f2f2f2"}}};!function(e){let t=[];function n(e,t,n,i){return[["M",e,t+2.5],["L",e+n,t+2.5],["M",e,t+i/2+.5],["L",e+n,t+i/2+.5],["M",e,t+i-1.5],["L",e+n,t+i-1.5]]}function i(e,t,n,i){let o=i/3-2,r=[];return r.concat(this.circle(n-o,t,o,o),this.circle(n-o,t+o+4,o,o),this.circle(n-o,t+2*(o+4),o,o))}e.compose=function(e){if(-1===t.indexOf(e)){t.push(e);let o=e.prototype.symbols;o.menu=n,o.menuball=i.bind(o)}}}(o||(o={}));let E=o,{composed:O}=h(),{addEvent:T,fireEvent:k,pushUnique:F}=h();function R(){this.fullscreen=new N(this)}class N{static compose(e){F(O,"Fullscreen")&&T(e,"beforeRender",R)}constructor(e){this.chart=e,this.isOpen=!1;let t=e.renderTo;!this.browserProps&&("function"==typeof t.requestFullscreen?this.browserProps={fullscreenChange:"fullscreenchange",requestFullscreen:"requestFullscreen",exitFullscreen:"exitFullscreen"}:t.mozRequestFullScreen?this.browserProps={fullscreenChange:"mozfullscreenchange",requestFullscreen:"mozRequestFullScreen",exitFullscreen:"mozCancelFullScreen"}:t.webkitRequestFullScreen?this.browserProps={fullscreenChange:"webkitfullscreenchange",requestFullscreen:"webkitRequestFullScreen",exitFullscreen:"webkitExitFullscreen"}:t.msRequestFullscreen&&(this.browserProps={fullscreenChange:"MSFullscreenChange",requestFullscreen:"msRequestFullscreen",exitFullscreen:"msExitFullscreen"}))}close(){let e=this,t=e.chart,n=t.options.chart;k(t,"fullscreenClose",null,function(){e.isOpen&&e.browserProps&&t.container.ownerDocument instanceof Document&&t.container.ownerDocument[e.browserProps.exitFullscreen](),e.unbindFullscreenEvent&&(e.unbindFullscreenEvent=e.unbindFullscreenEvent()),t.setSize(e.origWidth,e.origHeight,!1),e.origWidth=void 0,e.origHeight=void 0,n.width=e.origWidthOption,n.height=e.origHeightOption,e.origWidthOption=void 0,e.origHeightOption=void 0,e.isOpen=!1,e.setButtonText()})}open(){let e=this,t=e.chart,n=t.options.chart;k(t,"fullscreenOpen",null,function(){if(n&&(e.origWidthOption=n.width,e.origHeightOption=n.height),e.origWidth=t.chartWidth,e.origHeight=t.chartHeight,e.browserProps){let n=T(t.container.ownerDocument,e.browserProps.fullscreenChange,function(){e.isOpen?(e.isOpen=!1,e.close()):(t.setSize(null,null,!1),e.isOpen=!0,e.setButtonText())}),i=T(t,"destroy",n);e.unbindFullscreenEvent=()=>{n(),i()};let o=t.renderTo[e.browserProps.requestFullscreen]();o&&o.catch(function(){alert("Full screen is not supported inside a frame.")})}})}setButtonText(){let e=this.chart,t=e.exporting?.divElements,n=e.options.exporting,i=n&&n.buttons&&n.buttons.contextButton.menuItems,o=e.options.lang;if(n&&n.menuItemDefinitions&&o&&o.exitFullscreen&&o.viewFullscreen&&i&&t){let e=t[i.indexOf("viewFullscreen")];e&&d().setElementHTML(e,this.isOpen?o.exitFullscreen:n.menuItemDefinitions.viewFullscreen.text||o.viewFullscreen)}}toggle(){this.isOpen?this.close():this.open()}}let{win:P}=h(),{discardElement:H,objectEach:L}=h(),M={ajax:function(e){let t={json:"application/json",xml:"application/xml",text:"text/plain",octet:"application/octet-stream"},n=new XMLHttpRequest;function i(t,n){e.error&&e.error(t,n)}if(!e.url)return!1;n.open((e.type||"get").toUpperCase(),e.url,!0),e.headers?.["Content-Type"]||n.setRequestHeader("Content-Type",t[e.dataType||"json"]||t.text),L(e.headers,function(e,t){n.setRequestHeader(t,e)}),e.responseType&&(n.responseType=e.responseType),n.onreadystatechange=function(){let t;if(4===n.readyState){if(200===n.status){if("blob"!==e.responseType&&(t=n.responseText,"json"===e.dataType))try{t=JSON.parse(t)}catch(e){if(e instanceof Error)return i(n,e)}return e.success?.(t,n)}i(n,n.responseText)}},e.data&&"string"!=typeof e.data&&(e.data=JSON.stringify(e.data)),n.send(e.data)},getJSON:function(e,t){M.ajax({url:e,success:t,dataType:"json",headers:{"Content-Type":"text/plain"}})},post:async function(e,t,n){let i=new P.FormData;L(t,function(e,t){i.append(t,e)}),i.append("b64","true");let o=await P.fetch(e,{method:"POST",body:i,...n});if(o.ok){let e=await o.text(),n=document.createElement("a");n.href=`data:${t.type};base64,${e}`,n.download=t.filename,n.click(),H(n)}}},{defaultOptions:D,setOptions:A}=h(),{downloadURL:U,getScript:I}={dataURLtoBlob:v,downloadURL:function(e,t){let n=x.navigator,i=y.createElement("a");if("string"!=typeof e&&!(e instanceof String)&&n.msSaveOrOpenBlob)return void n.msSaveOrOpenBlob(e,t);if(e=""+e,n.userAgent.length>1e3)throw Error("Input too long");let o=/Edge\/\d+/.test(n.userAgent);if((m&&"string"==typeof e&&0===e.indexOf("data:application/pdf")||o||e.length>2e6)&&!(e=v(e)||""))throw Error("Failed to convert to blob");if(void 0!==i.download)i.href=e,i.download=t,y.body.appendChild(i),i.click(),y.body.removeChild(i);else try{if(!x.open(e,"chart"))throw Error("Failed to open window")}catch{x.location.href=e}},getScript:function(e){return new Promise((t,n)=>{let i=y.getElementsByTagName("head")[0],o=y.createElement("script");o.type="text/javascript",o.src=e,o.onload=()=>{t()},o.onerror=()=>{n(b(`Error loading script ${e}`))},i.appendChild(o)})}},{composed:j,doc:B,isFirefox:G,isMS:V,isSafari:W,SVG_NS:$,win:q}=h(),{addEvent:z,clearTimeout:K,createElement:J,css:_,discardElement:X,error:Y,extend:Z,find:Q,fireEvent:ee,isObject:et,merge:en,objectEach:ei,pick:eo,pushUnique:er,removeEvent:es,splat:ea,uniqueKey:el}=h();d().allowedAttributes.push("data-z-index","fill-opacity","filter","preserveAspectRatio","rx","ry","stroke-dasharray","stroke-linejoin","stroke-opacity","text-anchor","transform","transform-origin","version","viewBox","visibility","xmlns","xmlns:xlink"),d().allowedTags.push("desc","clippath","fedropshadow","femorphology","g","image");let ec=q.URL||q.webkitURL||q;class eh{constructor(e,t){this.options={},this.chart=e,this.options=t,this.btnCount=0,this.buttonOffset=0,this.divElements=[],this.svgElements=[]}static hyphenate(e){return e.replace(/[A-Z]/g,function(e){return"-"+e.toLowerCase()})}static async imageToDataURL(e,t,n){let i=await eh.loadImage(e),o=B.createElement("canvas"),r=o?.getContext("2d");if(r)return o.height=i.height*t,o.width=i.width*t,r.drawImage(i,0,0,o.width,o.height),o.toDataURL(n);throw Error("No canvas found!")}static loadImage(e){return new Promise((t,n)=>{let i=new q.Image;i.crossOrigin="Anonymous",i.onload=()=>{setTimeout(()=>{t(i)},eh.loadEventDeferDelay)},i.onerror=e=>{n(e)},i.src=e})}static prepareImageOptions(e){let t=e?.type||"image/png",n=e?.libURL||D.exporting?.libURL;return{type:t,filename:(e?.filename||"chart")+"."+("image/svg+xml"===t?"svg":t.split("/")[1]),scale:e?.scale||1,libURL:n?.slice(-1)!=="/"?n+"/":n}}static sanitizeSVG(e,t){let n=e.indexOf("</svg>")+6,i=e.indexOf("<foreignObject")>-1,o=e.substr(n);return e=e.substr(0,n),i?e=e.replace(/(<(?:img|br).*?(?=\>))>/g,"$1 />"):o&&t?.exporting?.allowHTML&&(o='<foreignObject x="0" y="0" width="'+t.chart.width+'" height="'+t.chart.height+'"><body xmlns="http://www.w3.org/1999/xhtml">'+o.replace(/(<(?:img|br).*?(?=\>))>/g,"$1 />")+"</body></foreignObject>",e=e.replace("</svg>",o+"</svg>")),e=e.replace(/zIndex="[^"]+"/g,"").replace(/symbolName="[^"]+"/g,"").replace(/jQuery\d+="[^"]+"/g,"").replace(/url\(("|&quot;)(.*?)("|&quot;)\;?\)/g,"url($2)").replace(/url\([^#]+#/g,"url(#").replace(/<svg /,'<svg xmlns:xlink="http://www.w3.org/1999/xlink" ').replace(/ (NS\d+\:)?href=/g," xlink:href=").replace(/\n+/g," ").replace(/&nbsp;/g,"\xa0").replace(/&shy;/g,"\xad")}static svgToDataURL(e){let t=q.navigator.userAgent,n=t.indexOf("WebKit")>-1&&0>t.indexOf("Chrome");try{if(!n&&-1===e.indexOf("<foreignObject"))return ec.createObjectURL(new q.Blob([e],{type:"image/svg+xml;charset-utf-16"}))}catch{}return"data:image/svg+xml;charset=UTF-8,"+encodeURIComponent(e)}addButton(e){let t,n=this,i=n.chart,o=i.renderer,r=en(i.options.navigation?.buttonOptions,e),s=r.onclick,a=r.menuItems,l=r.symbolSize||12;if(!1===r.enabled||!r.theme)return;let c=i.styledMode?{}:r.theme,h=()=>{};s?h=function(e){e&&e.stopPropagation(),s.call(i,e)}:a&&(h=function(e){e&&e.stopPropagation(),n.contextMenu(p.menuClassName,a,p.translateX||0,p.translateY||0,p.width||0,p.height||0,p),p.setState(2)}),r.text&&r.symbol?c.paddingLeft=eo(c.paddingLeft,30):r.text||Z(c,{width:r.width,height:r.height,padding:0});let p=o.button(r.text||"",0,0,h,c,void 0,void 0,void 0,void 0,r.useHTML).addClass(e.className||"").attr({title:eo(i.options.lang[r._titleKey||r.titleKey],"")});p.menuClassName=e.menuClassName||"highcharts-menu-"+n.btnCount++,r.symbol&&(t=o.symbol(r.symbol,Math.round((r.symbolX||0)-l/2),Math.round((r.symbolY||0)-l/2),l,l,{width:l,height:l}).addClass("highcharts-button-symbol").attr({zIndex:1}).add(p),i.styledMode||t.attr({stroke:r.symbolStroke,fill:r.symbolFill,"stroke-width":r.symbolStrokeWidth||1})),p.add(n.group).align(Z(r,{width:p.width,x:eo(r.x,n.buttonOffset)}),!0,"spacingBox"),n.buttonOffset+=((p.width||0)+(r.buttonSpacing||0))*("right"===r.align?-1:1),n.svgElements.push(p,t)}afterPrint(){let e=this.chart;if(!this.printReverseInfo)return;let{childNodes:t,origDisplay:n,resetParams:i}=this.printReverseInfo;this.moveContainers(e.renderTo),[].forEach.call(t,function(e,t){1===e.nodeType&&(e.style.display=n[t]||"")}),this.isPrinting=!1,i&&e.setSize.apply(e,i),delete this.printReverseInfo,eh.printingChart=void 0,ee(e,"afterPrint")}beforePrint(){let e=this.chart,t=B.body,n=this.options.printMaxWidth,i={childNodes:t.childNodes,origDisplay:[],resetParams:void 0};this.isPrinting=!0,e.pointer?.reset(void 0,0),ee(e,"beforePrint"),n&&e.chartWidth>n&&(i.resetParams=[e.options.chart.width,void 0,!1],e.setSize(n,void 0,!1)),[].forEach.call(i.childNodes,function(e,t){1===e.nodeType&&(i.origDisplay[t]=e.style.display,e.style.display="none")}),this.moveContainers(t),this.printReverseInfo=i}contextMenu(e,t,n,i,o,r,s){let a=this,l=a.chart,c=l.options.navigation,h=l.chartWidth,p=l.chartHeight,u="cache-"+e,g=Math.max(o,r),f,m=l[u];m||(a.contextMenuEl=l[u]=m=J("div",{className:e},{position:"absolute",zIndex:1e3,padding:g+"px",pointerEvents:"auto",...l.renderer.style},l.scrollablePlotArea?.fixedDiv||l.container),f=J("ul",{className:"highcharts-menu"},l.styledMode?{}:{listStyle:"none",margin:0,padding:0},m),l.styledMode||_(f,Z({MozBoxShadow:"3px 3px 10px #0008",WebkitBoxShadow:"3px 3px 10px #0008",boxShadow:"3px 3px 10px #0008"},c?.menuStyle||{})),m.hideMenu=function(){_(m,{display:"none"}),s&&s.setState(0),l.exporting&&(l.exporting.openMenu=!1),_(l.renderTo,{overflow:"hidden"}),_(l.container,{overflow:"hidden"}),K(m.hideTimer),ee(l,"exportMenuHidden")},a.events?.push(z(m,"mouseleave",function(){m.hideTimer=q.setTimeout(m.hideMenu,500)}),z(m,"mouseenter",function(){K(m.hideTimer)}),z(B,"mouseup",function(t){l.pointer?.inClass(t.target,e)||m.hideMenu()}),z(m,"click",function(){l.exporting?.openMenu&&m.hideMenu()})),t.forEach(function(e){if("string"==typeof e&&a.options.menuItemDefinitions?.[e]&&(e=a.options.menuItemDefinitions[e]),et(e,!0)){let t;e.separator?t=J("hr",void 0,void 0,f):("viewData"===e.textKey&&a.isDataTableVisible&&(e.textKey="hideData"),t=J("li",{className:"highcharts-menu-item",onclick:function(t){t&&t.stopPropagation(),m.hideMenu(),"string"!=typeof e&&e.onclick&&e.onclick.apply(l,arguments)}},void 0,f),d().setElementHTML(t,e.text||l.options.lang[e.textKey]),l.styledMode||(t.onmouseover=function(){_(this,c?.menuItemHoverStyle||{})},t.onmouseout=function(){_(this,c?.menuItemStyle||{})},_(t,Z({cursor:"pointer"},c?.menuItemStyle||{})))),a.divElements.push(t)}}),a.divElements.push(f,m),a.menuHeight=m.offsetHeight,a.menuWidth=m.offsetWidth);let x={display:"block"};n+(a.menuWidth||0)>h?x.right=h-n-o-g+"px":x.left=n-g+"px",i+r+(a.menuHeight||0)>p&&s.alignOptions?.verticalAlign!=="top"?x.bottom=p-i-g+"px":x.top=i+r-g+"px",_(m,x),_(l.renderTo,{overflow:""}),_(l.container,{overflow:""}),l.exporting&&(l.exporting.openMenu=!0),ee(l,"exportMenuShown")}destroy(e){let t,n=e?e.target:this.chart,{divElements:i,events:o,svgElements:r}=this;r.forEach((e,i)=>{e&&(e.onclick=e.ontouchstart=null,n[t="cache-"+e.menuClassName]&&delete n[t],r[i]=e.destroy())}),r.length=0,this.group&&(this.group.destroy(),delete this.group),i.forEach(function(e,t){e&&(K(e.hideTimer),es(e,"mouseleave"),i[t]=e.onmouseout=e.onmouseover=e.ontouchstart=e.onclick=null,X(e))}),i.length=0,o&&(o.forEach(function(e){e()}),o.length=0)}async downloadSVG(e,t){let n,i={svg:e,exportingOptions:t,exporting:this};if(ee(eh.prototype,"downloadSVG",i),i.defaultPrevented)return;let{type:o,filename:r,scale:s,libURL:a}=eh.prepareImageOptions(t);if("application/pdf"===o)throw Error("Offline exporting logic for PDF type is not found.");if("image/svg+xml"===o){if(void 0!==q.MSBlobBuilder){let t=new q.MSBlobBuilder;t.append(e),n=t.getBlob("image/svg+xml")}else n=eh.svgToDataURL(e);U(n,r)}else{n=eh.svgToDataURL(e);try{eh.objectURLRevoke=!0;let e=await eh.imageToDataURL(n,s,o);U(e,r)}catch(c){if("No canvas found!"===c.message)throw c;if(e.length>1e8)throw Error("Input too long");let t=B.createElement("canvas"),n=t.getContext("2d"),i=e.match(/^<svg[^>]*\s{,1000}width\s{,1000}=\s{,1000}\"?(\d+)\"?[^>]*>/),l=e.match(/^<svg[^>]*\s{0,1000}height\s{,1000}=\s{,1000}\"?(\d+)\"?[^>]*>/);if(n&&i&&l){let c=i[1]*s,h=l[1]*s;t.width=c,t.height=h,q.canvg||(eh.objectURLRevoke=!0,await I(a+"canvg.js")),q.canvg.Canvg.fromString(n,e).start(),U(q.navigator.msSaveOrOpenBlob?t.msToBlob():t.toDataURL(o),r)}}finally{if(eh.objectURLRevoke)try{ec.revokeObjectURL(n)}catch{}}}}async exportChart(e,t){if((e=en(this.options,e)).local)await this.localExport(e,t||{});else{let n=this.getSVGForExport(e,t);e.url&&await M.post(e.url,{filename:e.filename?e.filename.replace(/\//g,"-"):this.getFilename(),type:e.type,width:e.width,scale:e.scale,svg:n},e.fetchOptions)}}async fallbackToServer(e,t){!1===e.fallbackToExportServer?e.error?e.error(e,t):Y(28,!0):"application/pdf"===e.type&&(e.local=!1,await this.exportChart(e))}getChartHTML(e){let t=this.chart;return e&&this.inlineStyles(),this.resolveCSSVariables(),t.container.innerHTML}getFilename(){let e=this.chart.userOptions.title?.text,t=this.options.filename;return t?t.replace(/\//g,"-"):("string"==typeof e&&(t=e.toLowerCase().replace(/<\/?[^>]+(>|$)/g,"").replace(/[\s_]+/g,"-").replace(/[^a-z\d\-]/g,"").replace(/^[\-]+/g,"").replace(/[\-]+/g,"-").substr(0,24).replace(/[\-]+$/g,"")),(!t||t.length<5)&&(t="chart"),t)}getSVG(e){let t=this.chart,n,i,o=en(t.options,e);o.plotOptions=en(t.userOptions.plotOptions,e?.plotOptions),o.time=en(t.userOptions.time,e?.time);let r=J("div",void 0,{position:"absolute",top:"-9999em",width:t.chartWidth+"px",height:t.chartHeight+"px"},B.body),s=t.renderTo.style.width,a=t.renderTo.style.height,l=o.exporting?.sourceWidth||o.chart.width||/px$/.test(s)&&parseInt(s,10)||(o.isGantt?800:600),c=o.exporting?.sourceHeight||o.chart.height||/px$/.test(a)&&parseInt(a,10)||400;Z(o.chart,{animation:!1,renderTo:r,forExport:!0,renderer:"SVGRenderer",width:l,height:c}),o.exporting&&(o.exporting.enabled=!1),delete o.data,o.series=[],t.series.forEach(function(e){(i=en(e.userOptions,{animation:!1,enableMouseTracking:!1,showCheckbox:!1,visible:e.visible})).isInternal||o?.series?.push(i)});let h={};t.axes.forEach(function(e){e.userOptions.internalKey||(e.userOptions.internalKey=el()),o&&!e.options.isInternal&&(h[e.coll]||(h[e.coll]=!0,o[e.coll]=[]),o[e.coll].push(en(e.userOptions,{visible:e.visible,type:e.type,uniqueNames:e.uniqueNames})))}),o.colorAxis=t.userOptions.colorAxis;let p=new t.constructor(o,t.callback);return e&&["xAxis","yAxis","series"].forEach(function(t){e[t]&&p.update({[t]:e[t]})}),t.axes.forEach(function(t){let n=Q(p.axes,e=>e.options.internalKey===t.userOptions.internalKey);if(n){let i=t.getExtremes(),o=ea(e?.[t.coll]||{})[0],r="min"in o?o.min:i.userMin,s="max"in o?o.max:i.userMax;(void 0!==r&&r!==n.min||void 0!==s&&s!==n.max)&&n.setExtremes(r??void 0,s??void 0,!0,!1)}}),n=p.exporting?.getChartHTML(t.styledMode||o.exporting?.applyStyleSheets)||"",ee(t,"getSVG",{chartCopy:p}),n=eh.sanitizeSVG(n,o),o=void 0,p.destroy(),X(r),n}getSVGForExport(e,t){let n=this.options;return this.getSVG(en({chart:{borderRadius:0}},n.chartOptions,t,{exporting:{sourceWidth:e?.sourceWidth||n.sourceWidth,sourceHeight:e?.sourceHeight||n.sourceHeight}}))}inlineStyles(){let e,t=eh.inlineDenylist,n=eh.inlineAllowlist,i={},o=J("iframe",void 0,{width:"1px",height:"1px",visibility:"hidden"},B.body),r=o.contentWindow?.document;r&&r.body.appendChild(r.createElementNS($,"svg")),!function o(s){let a,l,c,h,p,d,u={};if(r&&1===s.nodeType&&-1===eh.unstyledElements.indexOf(s.nodeName)){if(a=q.getComputedStyle(s,null),l="svg"===s.nodeName?{}:q.getComputedStyle(s.parentNode,null),!i[s.nodeName]){e=r.getElementsByTagName("svg")[0],c=r.createElementNS(s.namespaceURI,s.nodeName),e.appendChild(c);let t=q.getComputedStyle(c,null),n={};for(let e in t)e.length<1e3&&"string"==typeof t[e]&&!/^\d+$/.test(e)&&(n[e]=t[e]);i[s.nodeName]=n,"text"===s.nodeName&&delete i.text.fill,e.removeChild(c)}for(let e in a)(G||V||W||Object.hasOwnProperty.call(a,e))&&function(e,o){if(h=p=!1,n.length){for(d=n.length;d--&&!p;)p=n[d].test(o);h=!p}for("transform"===o&&"none"===e&&(h=!0),d=t.length;d--&&!h;){if(o.length>1e3)throw Error("Input too long");h=t[d].test(o)||"function"==typeof e}!h&&(l[o]!==e||"svg"===s.nodeName)&&i[s.nodeName][o]!==e&&(eh.inlineToAttributes&&-1===eh.inlineToAttributes.indexOf(o)?u[o]=e:e&&s.setAttribute(eh.hyphenate(o),e))}(a[e],e);if(_(s,u),"svg"===s.nodeName&&s.setAttribute("stroke-width","1px"),"text"===s.nodeName)return;[].forEach.call(s.children||s.childNodes,o)}}(this.chart.container.querySelector("svg")),e.parentNode.removeChild(e),o.parentNode.removeChild(o)}async localExport(e,t){let n=this.chart,i,o,r=null,s;if(V&&n.styledMode&&!eh.inlineAllowlist.length&&eh.inlineAllowlist.push(/^blockSize/,/^border/,/^caretColor/,/^color/,/^columnRule/,/^columnRuleColor/,/^cssFloat/,/^cursor/,/^fill$/,/^fillOpacity/,/^font/,/^inlineSize/,/^length/,/^lineHeight/,/^opacity/,/^outline/,/^parentRule/,/^rx$/,/^ry$/,/^stroke/,/^textAlign/,/^textAnchor/,/^textDecoration/,/^transform/,/^vectorEffect/,/^visibility/,/^x$/,/^y$/),V&&("application/pdf"===e.type||n.container.getElementsByTagName("image").length&&"image/svg+xml"!==e.type)||"application/pdf"===e.type&&[].some.call(n.container.getElementsByTagName("image"),function(e){let t=e.getAttribute("href");return""!==t&&"string"==typeof t&&0!==t.indexOf("data:")}))return void await this.fallbackToServer(e,Error("Image type not supported for this chart/browser."));let a=z(n,"getSVG",e=>{o=e.chartCopy.options,s=(i=e.chartCopy.container.cloneNode(!0))&&i.getElementsByTagName("image")||[]});try{let n;for(let n of(this.getSVGForExport(e,t),s?Array.from(s):[]))if(r=n.getAttributeNS("http://www.w3.org/1999/xlink","href")){eh.objectURLRevoke=!1;let t=await eh.imageToDataURL(r,e?.scale||1,e?.type||"image/png");n.setAttributeNS("http://www.w3.org/1999/xlink","href",t)}else n.parentNode.removeChild(n);let a=(n=i?.innerHTML,eh.sanitizeSVG(n||"",o));if(a.indexOf("<foreignObject")>-1&&"image/svg+xml"!==e.type&&(V||"application/pdf"===e.type))throw Error("Image type not supported for charts with embedded HTML");return await this.downloadSVG(a,Z({filename:this.getFilename()},e)),a}catch(t){await this.fallbackToServer(e,t)}finally{a()}}moveContainers(e){let t=this.chart,{scrollablePlotArea:n}=t;(n?[n.fixedDiv,n.scrollingContainer]:[t.container]).forEach(function(t){e.appendChild(t)})}print(){let e=this.chart;this.isPrinting||(eh.printingChart=e,W||this.beforePrint(),setTimeout(()=>{q.focus(),q.print(),W||setTimeout(()=>{e.exporting?.afterPrint()},1e3)},1))}render(){let e=this,{chart:t,options:n}=e,i=e?.isDirty||!e?.svgElements.length;e.buttonOffset=0,e.isDirty&&e.destroy(),i&&!1!==n.enabled&&(e.events=[],e.group||(e.group=t.renderer.g("exporting-group").attr({zIndex:3}).add()),ei(n?.buttons,function(t){e.addButton(t)}),e.isDirty=!1)}resolveCSSVariables(){Array.from(this.chart.container.querySelectorAll("*")).forEach(e=>{["color","fill","stop-color","stroke"].forEach(t=>{let n=e.getAttribute(t);n?.includes("var(")&&e.setAttribute(t,getComputedStyle(e).getPropertyValue(t));let i=e.style?.[t];i?.includes("var(")&&(e.style[t]=getComputedStyle(e).getPropertyValue(t))})})}update(e,t){this.isDirty=!0,en(!0,this.options,e),eo(t,!0)&&this.chart.redraw()}}eh.inlineAllowlist=[],eh.inlineDenylist=[/-/,/^(clipPath|cssText|d|height|width)$/,/^font$/,/[lL]ogical(Width|Height)$/,/^parentRule$/,/^(cssRules|ownerRules)$/,/perspective/,/TapHighlightColor/,/^transition/,/^length$/,/^\d+$/],eh.inlineToAttributes=["fill","stroke","strokeLinecap","strokeLinejoin","strokeWidth","textAnchor","x","y"],eh.loadEventDeferDelay=150*!!V,eh.unstyledElements=["clipPath","defs","desc"],function(e){function t(e){let t=e.exporting;t&&(t.render(),z(e,"redraw",function(){this.exporting?.render()}),z(e,"destroy",function(){this.exporting?.destroy()}))}function n(){let t=this;t.options.exporting&&(t.exporting=new e(t,t.options.exporting),f.compose(t).navigation.addUpdate((e,n)=>{t.exporting&&(t.exporting.isDirty=!0,en(!0,t.options.navigation,e),eo(n,!0)&&t.redraw())}))}function i({alignTo:e,key:t,textPxLength:n}){let i=this.options.exporting,{align:o,buttonSpacing:r=0,verticalAlign:s,width:a=0}=en(this.options.navigation?.buttonOptions,i?.buttons?.contextButton),l=e.width-n,c=a+r;(i?.enabled??!0)&&"title"===t&&"right"===o&&"top"===s&&l<2*c&&(l<c?e.width-=c:this.title?.alignValue!=="left"&&(e.x-=c-l/2))}e.compose=function(o,r){E.compose(r),N.compose(o),er(j,"Exporting")&&(Z(g().prototype,{exportChart:async function(e,t){await this.exporting?.exportChart(e,t)},getChartHTML:function(e){return this.exporting?.getChartHTML(e)},getFilename:function(){return this.exporting?.getFilename()},getSVG:function(e){return this.exporting?.getSVG(e)},print:function(){return this.exporting?.print()}}),o.prototype.callbacks.push(t),z(o,"afterInit",n),z(o,"layOutTitle",i),W&&q.matchMedia("print").addListener(function(t){e.printingChart&&(t.matches?e.printingChart.exporting?.beforePrint():e.printingChart.exporting?.afterPrint())}),A(C))}}(eh||(eh={}));let ep=eh,ed=h();ed.Exporting=ep,ed.HttpUtilities=ed.HttpUtilities||M,ed.ajax=ed.HttpUtilities.ajax,ed.getJSON=ed.HttpUtilities.getJSON,ed.post=ed.HttpUtilities.post,ep.compose(ed.Chart,ed.Renderer);let eu=h();return l.default})());