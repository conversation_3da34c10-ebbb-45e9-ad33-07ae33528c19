!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/lollipop
 * @requires highcharts
 *
 * (c) 2009-2025 <PERSON>, <PERSON><PERSON><PERSON>
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.SeriesRegistry,t._Highcharts.Series):"function"==typeof define&&define.amd?define("highcharts/modules/lollipop",["highcharts/highcharts"],function(t){return e(t,t.SeriesRegistry,t.Series)}):"object"==typeof exports?exports["highcharts/modules/lollipop"]=e(t._Highcharts,t._Highcharts.SeriesRegistry,t._Highcharts.Series):t.Highcharts=e(t.Highcharts,t.Highcharts.SeriesRegistry,t.Highcharts.Series)}("undefined"==typeof window?this:window,(t,e,r)=>(()=>{"use strict";var o={512:t=>{t.exports=e},820:t=>{t.exports=r},944:e=>{e.exports=t}},s={};function i(t){var e=s[t];if(void 0!==e)return e.exports;var r=s[t]={exports:{}};return o[t](r,r.exports,i),r.exports}i.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return i.d(e,{a:e}),e},i.d=(t,e)=>{for(var r in e)i.o(e,r)&&!i.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var a={};i.d(a,{default:()=>H});var n=i(944),p=i.n(n),l=i(512),d=i.n(l);let{series:{prototype:{pointClass:h}},seriesTypes:{scatter:{prototype:{pointClass:c}},dumbbell:{prototype:{pointClass:g}}}}=d(),{extend:u}=p();class y extends h{}u(y.prototype,{destroy:g.prototype.destroy,pointSetState:c.prototype.setState,setState:g.prototype.setState});var f=i(820),b=i.n(f);let{seriesTypes:{column:{prototype:x},dumbbell:{prototype:S},scatter:v}}=d(),{extend:C,merge:w}=p();class m extends b(){drawPoints(){let t=this.points.length,e=0,r;for(super.drawPoints.apply(this,arguments);e<t;)r=this.points[e],this.drawConnector(r),e++}translate(){for(let t of(x.translate.apply(this,arguments),this.points)){let{pointWidth:e,shapeArgs:r}=t;r?.x&&(r.x+=e/2,t.plotX=r.x||0)}}}m.defaultOptions=w(b().defaultOptions,{threshold:0,connectorWidth:1,groupPadding:.2,pointPadding:.1,states:{hover:{lineWidthPlus:0,connectorWidthPlus:1,halo:!1}},lineWidth:0,dataLabels:{align:void 0,verticalAlign:void 0},pointRange:1}),C(m.prototype,{alignDataLabel:x.alignDataLabel,crispCol:x.crispCol,drawConnector:S.drawConnector,drawDataLabels:x.drawDataLabels,getColumnMetrics:x.getColumnMetrics,getConnectorAttribs:S.getConnectorAttribs,pointClass:y}),d().registerSeriesType("lollipop",m);let H=p();return a.default})());