!/**
 * Highstock JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/full-screen
 * @requires highcharts
 *
 * Advanced Highcharts Stock tools
 *
 * (c) 2010-2025 Highsoft AS
 * Author: Torstein Honsi
 *
 * License: www.highcharts.com/license
 */function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(e._Highcharts,e._Highcharts.AST):"function"==typeof define&&define.amd?define("highcharts/modules/full-screen",["highcharts/highcharts"],function(e){return t(e,e.AST)}):"object"==typeof exports?exports["highcharts/modules/full-screen"]=t(e._Highcharts,e._Highcharts.AST):e.Highcharts=t(e.Highcharts,e.Highcharts.AST)}("undefined"==typeof window?this:window,(e,t)=>(()=>{"use strict";var n={660:e=>{e.exports=t},944:t=>{t.exports=e}},r={};function s(e){var t=r[e];if(void 0!==t)return t.exports;var i=r[e]={exports:{}};return n[e](i,i.exports,s),i.exports}s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},s.d=(e,t)=>{for(var n in t)s.o(t,n)&&!s.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var i={};s.d(i,{default:()=>w});var l=s(944),o=s.n(l),u=s(660),c=s.n(u);let{composed:h}=o(),{addEvent:p,fireEvent:a,pushUnique:f}=o();function g(){this.fullscreen=new d(this)}class d{static compose(e){f(h,"Fullscreen")&&p(e,"beforeRender",g)}constructor(e){this.chart=e,this.isOpen=!1;let t=e.renderTo;!this.browserProps&&("function"==typeof t.requestFullscreen?this.browserProps={fullscreenChange:"fullscreenchange",requestFullscreen:"requestFullscreen",exitFullscreen:"exitFullscreen"}:t.mozRequestFullScreen?this.browserProps={fullscreenChange:"mozfullscreenchange",requestFullscreen:"mozRequestFullScreen",exitFullscreen:"mozCancelFullScreen"}:t.webkitRequestFullScreen?this.browserProps={fullscreenChange:"webkitfullscreenchange",requestFullscreen:"webkitRequestFullScreen",exitFullscreen:"webkitExitFullscreen"}:t.msRequestFullscreen&&(this.browserProps={fullscreenChange:"MSFullscreenChange",requestFullscreen:"msRequestFullscreen",exitFullscreen:"msExitFullscreen"}))}close(){let e=this,t=e.chart,n=t.options.chart;a(t,"fullscreenClose",null,function(){e.isOpen&&e.browserProps&&t.container.ownerDocument instanceof Document&&t.container.ownerDocument[e.browserProps.exitFullscreen](),e.unbindFullscreenEvent&&(e.unbindFullscreenEvent=e.unbindFullscreenEvent()),t.setSize(e.origWidth,e.origHeight,!1),e.origWidth=void 0,e.origHeight=void 0,n.width=e.origWidthOption,n.height=e.origHeightOption,e.origWidthOption=void 0,e.origHeightOption=void 0,e.isOpen=!1,e.setButtonText()})}open(){let e=this,t=e.chart,n=t.options.chart;a(t,"fullscreenOpen",null,function(){if(n&&(e.origWidthOption=n.width,e.origHeightOption=n.height),e.origWidth=t.chartWidth,e.origHeight=t.chartHeight,e.browserProps){let n=p(t.container.ownerDocument,e.browserProps.fullscreenChange,function(){e.isOpen?(e.isOpen=!1,e.close()):(t.setSize(null,null,!1),e.isOpen=!0,e.setButtonText())}),r=p(t,"destroy",n);e.unbindFullscreenEvent=()=>{n(),r()};let s=t.renderTo[e.browserProps.requestFullscreen]();s&&s.catch(function(){alert("Full screen is not supported inside a frame.")})}})}setButtonText(){let e=this.chart,t=e.exporting?.divElements,n=e.options.exporting,r=n&&n.buttons&&n.buttons.contextButton.menuItems,s=e.options.lang;if(n&&n.menuItemDefinitions&&s&&s.exitFullscreen&&s.viewFullscreen&&r&&t){let e=t[r.indexOf("viewFullscreen")];e&&c().setElementHTML(e,this.isOpen?s.exitFullscreen:n.menuItemDefinitions.viewFullscreen.text||s.viewFullscreen)}}toggle(){this.isOpen?this.close():this.open()}}let F=o();F.Fullscreen=F.Fullscreen||d,F.Fullscreen.compose(F.Chart);let w=o();return i.default})());