{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/xrange\n * @requires highcharts\n *\n * X-range series\n *\n * (c) 2010-2025 <PERSON><PERSON>, Lars <PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/xrange\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"Color\"],amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/xrange\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Color\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__620__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 620:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__620__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ xrange_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Color\"],\"commonjs\":[\"highcharts\",\"Color\"],\"commonjs2\":[\"highcharts\",\"Color\"],\"root\":[\"Highcharts\",\"Color\"]}\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_ = __webpack_require__(620);\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default = /*#__PURE__*/__webpack_require__.n(highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/XRange/XRangeSeriesDefaults.js\n/* *\n *\n *  X-range series module\n *\n *  (c) 2010-2025 Torstein Honsi, Lars A. V. Cabrera\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { correctFloat, isNumber, isObject } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\n/**\n * The X-range series displays ranges on the X axis, typically time\n * intervals with a start and end date.\n *\n * @sample {highcharts} highcharts/demo/x-range/\n *         X-range\n * @sample {highcharts} highcharts/css/x-range/\n *         Styled mode X-range\n * @sample {highcharts} highcharts/chart/inverted-xrange/\n *         Inverted X-range\n *\n * @extends      plotOptions.column\n * @since        6.0.0\n * @product      highcharts highstock gantt\n * @excluding    boostThreshold, crisp, cropThreshold, depth, edgeColor,\n *               edgeWidth, findNearestPointBy, getExtremesFromAll,\n *               negativeColor, pointInterval, pointIntervalUnit,\n *               pointPlacement, pointRange, pointStart, softThreshold,\n *               stacking, threshold, data, dataSorting, boostBlending\n * @requires     modules/xrange\n * @optionparent plotOptions.xrange\n */\nconst XRangeSeriesDefaults = {\n    /**\n     * A partial fill for each point, typically used to visualize how much\n     * of a task is performed. The partial fill object can be set either on\n     * series or point level.\n     *\n     * @sample {highcharts} highcharts/demo/x-range\n     *         X-range with partial fill\n     *\n     * @product   highcharts highstock gantt\n     * @apioption plotOptions.xrange.partialFill\n     */\n    /**\n     * The fill color to be used for partial fills. Defaults to a darker\n     * shade of the point color.\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @product   highcharts highstock gantt\n     * @apioption plotOptions.xrange.partialFill.fill\n     */\n    /**\n     * A partial fill for each point, typically used to visualize how much\n     * of a task is performed. See [completed](series.gantt.data.completed).\n     *\n     * @sample gantt/demo/progress-indicator\n     *         Gantt with progress indicator\n     *\n     * @product   gantt\n     * @apioption plotOptions.gantt.partialFill\n     */\n    /**\n     * In an X-range series, this option makes all points of the same Y-axis\n     * category the same color.\n     */\n    colorByPoint: true,\n    dataLabels: {\n        formatter: function () {\n            let amount = this.partialFill;\n            if (isObject(amount)) {\n                amount = amount.amount;\n            }\n            if (isNumber(amount) && amount > 0) {\n                return correctFloat(amount * 100) + '%';\n            }\n        },\n        inside: true,\n        verticalAlign: 'middle',\n        style: {\n            whiteSpace: 'nowrap'\n        }\n    },\n    tooltip: {\n        headerFormat: '<span style=\"font-size: 0.8em\">{ucfirst point.x} - {point.x2}</span><br/>',\n        pointFormat: '<span style=\"color:{point.color}\">\\u25CF</span> {series.name}: <b>{point.yCategory}</b><br/>'\n    },\n    borderRadius: 3,\n    pointRange: 0\n};\n/* *\n *\n *  Export Default\n *\n * */\n/* harmony default export */ const XRange_XRangeSeriesDefaults = (XRangeSeriesDefaults);\n/* *\n *\n * API Options\n *\n * */\n/**\n * An `xrange` series. If the [type](#series.xrange.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.xrange\n * @excluding boostThreshold, crisp, cropThreshold, depth, edgeColor, edgeWidth,\n *            findNearestPointBy, getExtremesFromAll, negativeColor,\n *            pointInterval, pointIntervalUnit, pointPlacement, pointRange,\n *            pointStart, softThreshold, stacking, threshold, dataSorting,\n *            boostBlending\n * @product   highcharts highstock gantt\n * @requires  modules/xrange\n * @apioption series.xrange\n */\n/**\n * An array of data points for the series. For the `xrange` series type,\n * points can be given in the following ways:\n *\n * 1. An array of objects with named values. The objects are point configuration\n *    objects as seen below.\n *    ```js\n *    data: [{\n *        x: Date.UTC(2017, 0, 1),\n *        x2: Date.UTC(2017, 0, 3),\n *        name: \"Test\",\n *        y: 0,\n *        color: \"#00FF00\"\n *    }, {\n *        x: Date.UTC(2017, 0, 4),\n *        x2: Date.UTC(2017, 0, 5),\n *        name: \"Deploy\",\n *        y: 1,\n *        color: \"#FF0000\"\n *    }]\n *    ```\n *\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @declare   Highcharts.XrangePointOptionsObject\n * @type      {Array<*>}\n * @extends   series.line.data\n * @product   highcharts highstock gantt\n * @apioption series.xrange.data\n */\n/**\n * The starting X value of the range point.\n *\n * @sample {highcharts} highcharts/demo/x-range\n *         X-range\n *\n * @type      {number}\n * @product   highcharts highstock gantt\n * @apioption series.xrange.data.x\n */\n/**\n * The ending X value of the range point.\n *\n * @sample {highcharts} highcharts/demo/x-range\n *         X-range\n *\n * @type      {number}\n * @product   highcharts highstock gantt\n * @apioption series.xrange.data.x2\n */\n/**\n * The Y value of the range point.\n *\n * @sample {highcharts} highcharts/demo/x-range\n *         X-range\n *\n * @type      {number}\n * @product   highcharts highstock gantt\n * @apioption series.xrange.data.y\n */\n/**\n * A partial fill for each point, typically used to visualize how much of\n * a task is performed. The partial fill object can be set either on series\n * or point level.\n *\n * @sample {highcharts} highcharts/demo/x-range\n *         X-range with partial fill\n *\n * @declare   Highcharts.XrangePointPartialFillOptionsObject\n * @product   highcharts highstock gantt\n * @apioption series.xrange.data.partialFill\n */\n/**\n * The amount of the X-range point to be filled. Values can be 0-1 and are\n * converted to percentages in the default data label formatter.\n *\n * @type      {number}\n * @product   highcharts highstock gantt\n * @apioption series.xrange.data.partialFill.amount\n */\n/**\n * The fill color to be used for partial fills. Defaults to a darker shade\n * of the point color.\n *\n * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n * @product   highcharts highstock gantt\n * @apioption series.xrange.data.partialFill.fill\n */\n(''); // Adds doclets above to transpiled file\n\n;// ./code/es-modules/Series/XRange/XRangePoint.js\n/* *\n *\n *  X-range series module\n *\n *  (c) 2010-2025 Torstein Honsi, Lars A. V. Cabrera\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { column: { prototype: { pointClass: ColumnPoint } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { extend } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass XRangePoint extends ColumnPoint {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Return color of a point based on its category.\n     *\n     * @private\n     * @function getColorByCategory\n     *\n     * @param {object} series\n     *        The series which the point belongs to.\n     *\n     * @param {object} point\n     *        The point to calculate its color for.\n     *\n     * @return {object}\n     *         Returns an object containing the properties color and colorIndex.\n     */\n    static getColorByCategory(series, point) {\n        const colors = series.options.colors || series.chart.options.colors, colorCount = colors ?\n            colors.length :\n            series.chart.options.chart.colorCount, colorIndex = point.y % colorCount, color = colors?.[colorIndex];\n        return {\n            colorIndex: colorIndex,\n            color: color\n        };\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    resolveColor() {\n        const series = this.series;\n        if (series.options.colorByPoint && !this.options.color) {\n            const colorByPoint = XRangePoint.getColorByCategory(series, this);\n            if (!series.chart.styledMode) {\n                this.color = colorByPoint.color;\n            }\n            if (!this.options.colorIndex) {\n                this.colorIndex = colorByPoint.colorIndex;\n            }\n        }\n        else {\n            this.color = this.options.color || series.color;\n        }\n    }\n    /**\n     * Extend init to have y default to 0.\n     *\n     * @private\n     */\n    constructor(series, options) {\n        super(series, options);\n        if (!this.y) {\n            this.y = 0;\n        }\n    }\n    /**\n     * Extend applyOptions to handle time strings for x2\n     *\n     * @private\n     */\n    applyOptions(options, x) {\n        super.applyOptions(options, x);\n        this.x2 = this.series.chart.time.parse(this.x2);\n        this.isNull = !this.isValid?.();\n        return this;\n    }\n    /**\n     * @private\n     */\n    setState() {\n        super.setState.apply(this, arguments);\n        this.series.drawPoint(this, this.series.getAnimationVerb());\n    }\n    /**\n     * @private\n     */\n    isValid() {\n        return typeof this.x === 'number' &&\n            typeof this.x2 === 'number';\n    }\n}\nextend(XRangePoint.prototype, {\n    ttBelow: false,\n    tooltipDateKeys: ['x', 'x2']\n});\n/* *\n *\n *  Class Namespace\n *\n * */\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const XRange_XRangePoint = (XRangePoint);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * The ending X value of the range point.\n * @name Highcharts.Point#x2\n * @type {number|undefined}\n * @requires modules/xrange\n */\n/**\n * @interface Highcharts.PointOptionsObject in parts/Point.ts\n */ /**\n* The ending X value of the range point.\n* @name Highcharts.PointOptionsObject#x2\n* @type {number|undefined}\n* @requires modules/xrange\n*/\n(''); // Keeps doclets above in JS file\n\n;// ./code/es-modules/Series/XRange/XRangeSeries.js\n/* *\n *\n *  X-range series module\n *\n *  (c) 2010-2025 Torstein Honsi, Lars A. V. Cabrera\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { composed, noop } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { parse: color } = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default());\n\nconst { column: ColumnSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { addEvent, clamp, crisp, defined, extend: XRangeSeries_extend, find, isNumber: XRangeSeries_isNumber, isObject: XRangeSeries_isObject, merge, pick, pushUnique, relativeLength } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Max x2 should be considered in xAxis extremes\n * @private\n */\nfunction onAxisAfterGetSeriesExtremes() {\n    let dataMax, modMax;\n    if (this.isXAxis) {\n        dataMax = pick(this.dataMax, -Number.MAX_VALUE);\n        for (const series of this.series) {\n            const column = (series.dataTable.getColumn('x2', true) ||\n                series.dataTable.getColumn('end', true));\n            if (column) {\n                for (const val of column) {\n                    if (XRangeSeries_isNumber(val) && val > dataMax) {\n                        dataMax = val;\n                        modMax = true;\n                    }\n                }\n            }\n        }\n        if (modMax) {\n            this.dataMax = dataMax;\n        }\n    }\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.xrange\n *\n * @augments Highcharts.Series\n */\nclass XRangeSeries extends ColumnSeries {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static compose(AxisClass) {\n        if (pushUnique(composed, 'Series.XRange')) {\n            addEvent(AxisClass, 'afterGetSeriesExtremes', onAxisAfterGetSeriesExtremes);\n        }\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    init() {\n        super.init.apply(this, arguments);\n        this.options.stacking = void 0; // #13161\n    }\n    /**\n     * Borrow the column series metrics, but with swapped axes. This gives\n     * free access to features like groupPadding, grouping, pointWidth etc.\n     * @private\n     */\n    getColumnMetrics() {\n        const swapAxes = () => {\n            for (const series of this.chart.series) {\n                const xAxis = series.xAxis;\n                series.xAxis = series.yAxis;\n                series.yAxis = xAxis;\n            }\n        };\n        swapAxes();\n        const metrics = super.getColumnMetrics();\n        swapAxes();\n        return metrics;\n    }\n    /**\n     * Override cropData to show a point where x or x2 is outside visible range,\n     * but one of them is inside.\n     * @private\n     */\n    cropData(table, min, max) {\n        // Replace xData with x2Data to find the appropriate cropStart\n        const xData = table.getColumn('x') || [], x2Data = table.getColumn('x2');\n        table.setColumn('x', x2Data, void 0, { silent: true });\n        const croppedData = super.cropData(table, min, max);\n        // Re-insert the cropped xData\n        table.setColumn('x', xData.slice(croppedData.start, croppedData.end), void 0, { silent: true });\n        return croppedData;\n    }\n    /**\n     * Finds the index of an existing point that matches the given point\n     * options.\n     *\n     * @private\n     *\n     * @param {Highcharts.XRangePointOptions} options\n     *        The options of the point.\n     *\n     * @return {number|undefined}\n     *         Returns index of a matching point, or undefined if no match is\n     *         found.\n     */\n    findPointIndex(options) {\n        const { cropStart, points } = this;\n        const { id } = options;\n        let pointIndex;\n        if (id) {\n            const point = find(points, (point) => point.id === id);\n            pointIndex = point ? point.index : void 0;\n        }\n        if (typeof pointIndex === 'undefined') {\n            const point = find(points, (point) => (point.x === options.x &&\n                point.x2 === options.x2 &&\n                !point.touched));\n            pointIndex = point ? point.index : void 0;\n        }\n        // Reduce pointIndex if data is cropped\n        if (this.cropped &&\n            XRangeSeries_isNumber(pointIndex) &&\n            XRangeSeries_isNumber(cropStart) &&\n            pointIndex >= cropStart) {\n            pointIndex -= cropStart;\n        }\n        return pointIndex;\n    }\n    alignDataLabel(point) {\n        const oldPlotX = point.plotX;\n        point.plotX = pick(point.dlBox?.centerX, point.plotX);\n        if (point.dataLabel && point.shapeArgs?.width) {\n            point.dataLabel.css({\n                width: `${point.shapeArgs.width}px`\n            });\n        }\n        super.alignDataLabel.apply(this, arguments);\n        point.plotX = oldPlotX;\n    }\n    /**\n     * @private\n     */\n    translatePoint(point) {\n        const xAxis = this.xAxis, yAxis = this.yAxis, metrics = this.columnMetrics, options = this.options, minPointLength = options.minPointLength || 0, oldColWidth = (point.shapeArgs?.width || 0) / 2, seriesXOffset = this.pointXOffset = metrics.offset, posX = pick(point.x2, point.x + (point.len || 0)), borderRadius = options.borderRadius, plotTop = this.chart.plotTop, plotLeft = this.chart.plotLeft;\n        let plotX = point.plotX, plotX2 = xAxis.translate(posX, 0, 0, 0, 1);\n        const length = Math.abs(plotX2 - plotX), inverted = this.chart.inverted, borderWidth = pick(options.borderWidth, 1);\n        let widthDifference, partialFill, yOffset = metrics.offset, pointHeight = Math.round(metrics.width), dlLeft, dlRight, dlWidth, clipRectWidth;\n        if (minPointLength) {\n            widthDifference = minPointLength - length;\n            if (widthDifference < 0) {\n                widthDifference = 0;\n            }\n            plotX -= widthDifference / 2;\n            plotX2 += widthDifference / 2;\n        }\n        plotX = Math.max(plotX, -10);\n        plotX2 = clamp(plotX2, -10, xAxis.len + 10);\n        // Handle individual pointWidth\n        if (defined(point.options.pointWidth)) {\n            yOffset -= ((Math.ceil(point.options.pointWidth) - pointHeight) / 2);\n            pointHeight = Math.ceil(point.options.pointWidth);\n        }\n        // Apply pointPlacement to the Y axis\n        if (options.pointPlacement &&\n            XRangeSeries_isNumber(point.plotY) &&\n            yAxis.categories) {\n            point.plotY = yAxis.translate(point.y, 0, 1, 0, 1, options.pointPlacement);\n        }\n        const x = crisp(Math.min(plotX, plotX2), borderWidth), x2 = crisp(Math.max(plotX, plotX2), borderWidth), width = x2 - x;\n        const r = Math.min(relativeLength((typeof borderRadius === 'object' ?\n            borderRadius.radius :\n            borderRadius || 0), pointHeight), Math.min(width, pointHeight) / 2);\n        const shapeArgs = {\n            x,\n            y: crisp((point.plotY || 0) + yOffset, borderWidth),\n            width,\n            height: pointHeight,\n            r\n        };\n        point.shapeArgs = shapeArgs;\n        // Move tooltip to default position\n        if (!inverted) {\n            point.tooltipPos[0] -= oldColWidth +\n                seriesXOffset -\n                shapeArgs.width / 2;\n        }\n        else {\n            point.tooltipPos[1] += seriesXOffset +\n                oldColWidth;\n        }\n        // Align data labels inside the shape and inside the plot area\n        dlLeft = shapeArgs.x;\n        dlRight = dlLeft + shapeArgs.width;\n        if (dlLeft < 0 || dlRight > xAxis.len) {\n            dlLeft = clamp(dlLeft, 0, xAxis.len);\n            dlRight = clamp(dlRight, 0, xAxis.len);\n            dlWidth = dlRight - dlLeft;\n            point.dlBox = merge(shapeArgs, {\n                x: dlLeft,\n                width: dlRight - dlLeft,\n                centerX: dlWidth ? dlWidth / 2 : null\n            });\n        }\n        else {\n            point.dlBox = null;\n        }\n        // Tooltip position\n        const tooltipPos = point.tooltipPos;\n        const xIndex = !inverted ? 0 : 1;\n        const yIndex = !inverted ? 1 : 0;\n        const tooltipYOffset = (this.columnMetrics ?\n            this.columnMetrics.offset :\n            -metrics.width / 2);\n        // Centering tooltip position (#14147)\n        if (inverted) {\n            tooltipPos[xIndex] += shapeArgs.width / 2;\n        }\n        else {\n            tooltipPos[xIndex] = clamp(tooltipPos[xIndex] +\n                (xAxis.reversed ? -1 : 0) * shapeArgs.width, xAxis.left - plotLeft, xAxis.left + xAxis.len - plotLeft - 1);\n        }\n        tooltipPos[yIndex] = clamp(tooltipPos[yIndex] + ((inverted ? -1 : 1) * tooltipYOffset), yAxis.top - plotTop, yAxis.top + yAxis.len - plotTop - 1);\n        // Add a partShapeArgs to the point, based on the shapeArgs property\n        partialFill = point.partialFill;\n        if (partialFill) {\n            // Get the partial fill amount\n            if (XRangeSeries_isObject(partialFill)) {\n                partialFill = partialFill.amount;\n            }\n            // If it was not a number, assume 0\n            if (!XRangeSeries_isNumber(partialFill)) {\n                partialFill = 0;\n            }\n            point.partShapeArgs = merge(shapeArgs);\n            clipRectWidth = Math.max(Math.round(length * partialFill + point.plotX -\n                plotX), 0);\n            point.clipRectArgs = {\n                x: xAxis.reversed ? // #10717\n                    shapeArgs.x + length - clipRectWidth :\n                    shapeArgs.x,\n                y: shapeArgs.y,\n                width: clipRectWidth,\n                height: shapeArgs.height\n            };\n        }\n        // Add formatting keys for tooltip and data labels. Use 'category' as\n        // 'key' to ensure tooltip datetime formatting. Use 'name' only when\n        // 'category' is undefined.\n        point.key = point.category || point.name;\n        point.yCategory = yAxis.categories?.[point.y ?? -1];\n    }\n    /**\n     * @private\n     */\n    translate() {\n        super.translate.apply(this, arguments);\n        for (const point of this.points) {\n            this.translatePoint(point);\n        }\n    }\n    /**\n     * Draws a single point in the series. Needed for partial fill.\n     *\n     * This override turns point.graphic into a group containing the\n     * original graphic and an overlay displaying the partial fill.\n     *\n     * @private\n     *\n     * @param {Highcharts.Point} point\n     *        An instance of Point in the series.\n     *\n     * @param {\"animate\"|\"attr\"} verb\n     *        'animate' (animates changes) or 'attr' (sets options)\n     */\n    drawPoint(point, verb) {\n        const seriesOpts = this.options, renderer = this.chart.renderer, type = point.shapeType, shapeArgs = point.shapeArgs, partShapeArgs = point.partShapeArgs, clipRectArgs = point.clipRectArgs, pointState = point.state, stateOpts = (seriesOpts.states[pointState || 'normal'] ||\n            {}), pointStateVerb = typeof pointState === 'undefined' ?\n            'attr' : verb, pointAttr = this.pointAttribs(point, pointState), animation = pick(this.chart.options.chart.animation, stateOpts.animation);\n        let graphic = point.graphic, pfOptions = point.partialFill;\n        if (!point.isNull && point.visible !== false) {\n            // Original graphic\n            if (graphic) { // Update\n                graphic.rect[verb](shapeArgs);\n            }\n            else {\n                point.graphic = graphic = renderer.g('point')\n                    .addClass(point.getClassName())\n                    .add(point.group || this.group);\n                graphic.rect = renderer[type](merge(shapeArgs))\n                    .addClass(point.getClassName())\n                    .addClass('highcharts-partfill-original')\n                    .add(graphic);\n            }\n            // Partial fill graphic\n            if (partShapeArgs) {\n                if (graphic.partRect) {\n                    graphic.partRect[verb](merge(partShapeArgs));\n                    graphic.partialClipRect[verb](merge(clipRectArgs));\n                }\n                else {\n                    graphic.partialClipRect = renderer.clipRect(clipRectArgs.x, clipRectArgs.y, clipRectArgs.width, clipRectArgs.height);\n                    graphic.partRect =\n                        renderer[type](partShapeArgs)\n                            .addClass('highcharts-partfill-overlay')\n                            .add(graphic)\n                            .clip(graphic.partialClipRect);\n                }\n            }\n            // Presentational\n            if (!this.chart.styledMode) {\n                graphic\n                    .rect[verb](pointAttr, animation)\n                    .shadow(seriesOpts.shadow);\n                if (partShapeArgs) {\n                    // Ensure pfOptions is an object\n                    if (!XRangeSeries_isObject(pfOptions)) {\n                        pfOptions = {};\n                    }\n                    if (XRangeSeries_isObject(seriesOpts.partialFill)) {\n                        pfOptions = merge(seriesOpts.partialFill, pfOptions);\n                    }\n                    const fill = (pfOptions.fill ||\n                        color(pointAttr.fill).brighten(-0.3).get() ||\n                        color(point.color || this.color)\n                            .brighten(-0.3).get());\n                    pointAttr.fill = fill;\n                    graphic\n                        .partRect[pointStateVerb](pointAttr, animation)\n                        .shadow(seriesOpts.shadow);\n                }\n            }\n        }\n        else if (graphic) {\n            point.graphic = graphic.destroy(); // #1269\n        }\n    }\n    /**\n     * @private\n     */\n    drawPoints() {\n        const verb = this.getAnimationVerb();\n        // Draw the columns\n        for (const point of this.points) {\n            this.drawPoint(point, verb);\n        }\n    }\n    /**\n     * Returns \"animate\", or \"attr\" if the number of points is above the\n     * animation limit.\n     *\n     * @private\n     */\n    getAnimationVerb() {\n        return (this.chart.pointCount < (this.options.animationLimit || 250) ?\n            'animate' :\n            'attr');\n    }\n    /**\n     * @private\n     */\n    isPointInside(point) {\n        const shapeArgs = point.shapeArgs, plotX = point.plotX, plotY = point.plotY;\n        if (!shapeArgs) {\n            return super.isPointInside.apply(this, arguments);\n        }\n        const isInside = typeof plotX !== 'undefined' &&\n            typeof plotY !== 'undefined' &&\n            plotY >= 0 &&\n            plotY <= this.yAxis.len &&\n            (shapeArgs.x || 0) + (shapeArgs.width || 0) >= 0 &&\n            plotX <= this.xAxis.len;\n        return isInside;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nXRangeSeries.defaultOptions = merge(ColumnSeries.defaultOptions, XRange_XRangeSeriesDefaults);\nXRangeSeries_extend(XRangeSeries.prototype, {\n    pointClass: XRange_XRangePoint,\n    pointArrayMap: ['x2', 'y'],\n    getExtremesFromAll: true,\n    keysAffectYAxis: ['y'],\n    parallelArrays: ['x', 'x2', 'y'],\n    requireSorting: false,\n    type: 'xrange',\n    animate: (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).series.prototype.animate,\n    autoIncrement: noop,\n    buildKDTree: noop\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('xrange', XRangeSeries);\n/* *\n *\n * Default Export\n *\n * */\n/* harmony default export */ const XRange_XRangeSeries = (XRangeSeries);\n\n;// ./code/es-modules/masters/modules/xrange.js\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nXRange_XRangeSeries.compose(G.Axis);\n/* harmony default export */ const xrange_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__620__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "xrange_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "correctFloat", "isNumber", "isObject", "column", "pointClass", "ColumnPoint", "seriesTypes", "extend", "XRangePoint", "getColorByCategory", "series", "point", "colors", "options", "chart", "colorCount", "length", "colorIndex", "y", "color", "resolveColor", "colorByPoint", "styledMode", "constructor", "applyOptions", "x", "x2", "time", "parse", "isNull", "<PERSON><PERSON><PERSON><PERSON>", "setState", "apply", "arguments", "drawPoint", "getAnimationVerb", "ttBelow", "tooltipDateKeys", "composed", "noop", "ColumnSeries", "addEvent", "clamp", "crisp", "defined", "XRangeSeries_extend", "find", "XRangeSeries_isNumber", "XRangeSeries_isObject", "merge", "pick", "pushUnique", "<PERSON><PERSON><PERSON><PERSON>", "onAxisAfterGetSeriesExtremes", "dataMax", "modMax", "isXAxis", "Number", "MAX_VALUE", "dataTable", "getColumn", "val", "XRangeSeries", "compose", "AxisClass", "init", "stacking", "getColumnMetrics", "swapAxes", "xAxis", "yAxis", "metrics", "cropData", "table", "min", "max", "xData", "x2Data", "setColumn", "silent", "croppedData", "slice", "start", "end", "findPointIndex", "pointIndex", "cropStart", "points", "id", "index", "touched", "cropped", "alignDataLabel", "oldPlotX", "plotX", "dlBox", "centerX", "dataLabel", "shapeArgs", "width", "css", "translatePoint", "columnMetrics", "minP<PERSON><PERSON><PERSON>th", "oldColWidth", "seriesXOffset", "pointXOffset", "offset", "posX", "len", "borderRadius", "plotTop", "plotLeft", "plotX2", "translate", "Math", "abs", "inverted", "borderWidth", "widthDifference", "partialFill", "yOffset", "pointHeight", "round", "dlLeft", "dlRight", "dl<PERSON><PERSON><PERSON>", "clipRectWidth", "pointWidth", "ceil", "pointPlacement", "plotY", "categories", "r", "radius", "height", "tooltipPos", "xIndex", "yIndex", "tooltipYOffset", "reversed", "left", "top", "amount", "partShapeArgs", "clipRectArgs", "category", "name", "yCategory", "verb", "seriesOpts", "renderer", "type", "shapeType", "pointState", "state", "stateOpts", "states", "pointStateVerb", "pointAttr", "pointAttribs", "animation", "graphic", "pfOptions", "visible", "destroy", "rect", "g", "addClass", "getClassName", "add", "group", "partRect", "partialClipRect", "clipRect", "clip", "shadow", "fill", "brighten", "drawPoints", "pointCount", "animationLimit", "isPointInside", "defaultOptions", "dataLabels", "formatter", "inside", "verticalAlign", "style", "whiteSpace", "tooltip", "headerFormat", "pointFormat", "pointRange", "pointArrayMap", "getExtremesFromAll", "keysAffectYAxis", "parallelArrays", "requireSorting", "animate", "autoIncrement", "buildKDTree", "registerSeriesType", "G", "XRange_XRangeSeries", "Axis"], "mappings": "CAWA,AAXA;;;;;;;;;;CAUC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC1G,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,4BAA6B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,KAAQ,CAACA,EAAK,cAAiB,CAAE,GACnI,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,4BAA4B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAEvIA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACpH,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,IACvG,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAab,OAAO,CAG5B,IAAIC,EAASS,CAAwB,CAACE,EAAS,CAAG,CAGjDZ,QAAS,CAAC,CACX,EAMA,OAHAS,CAAmB,CAACG,EAAS,CAACX,EAAQA,EAAOD,OAAO,CAAEW,GAG/CV,EAAOD,OAAO,AACtB,CAMCW,EAAoBI,CAAC,CAAG,AAACd,IACxB,IAAIe,EAASf,GAAUA,EAAOgB,UAAU,CACvC,IAAOhB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAU,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAAClB,EAASoB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACtB,EAASqB,IAC5EE,OAAOC,cAAc,CAACxB,EAASqB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAA+FzB,EAAoB,KACnH0B,EAAmH1B,EAAoBI,CAAC,CAACqB,GAEzIE,EAAmI3B,EAAoB,KACvJ4B,EAAuJ5B,EAAoBI,CAAC,CAACuB,GAejL,GAAM,CAAEE,aAAAA,CAAY,CAAEC,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAE,CAAIP,IAwNxC,CAAEQ,OAAQ,CAAEd,UAAW,CAAEe,WAAYC,CAAW,CAAE,CAAE,CAAE,CAAG,AAACN,IAA2IO,WAAW,CAEhN,CAAEC,OAAAA,CAAM,CAAE,CAAIZ,GAMpB,OAAMa,UAAoBH,EAqBtB,OAAOI,mBAAmBC,CAAM,CAAEC,CAAK,CAAE,CACrC,IAAMC,EAASF,EAAOG,OAAO,CAACD,MAAM,EAAIF,EAAOI,KAAK,CAACD,OAAO,CAACD,MAAM,CAAEG,EAAaH,EAC9EA,EAAOI,MAAM,CACbN,EAAOI,KAAK,CAACD,OAAO,CAACC,KAAK,CAACC,UAAU,CAAEE,EAAaN,EAAMO,CAAC,CAAGH,EAAYI,EAAQP,GAAQ,CAACK,EAAW,CAC1G,MAAO,CACHA,WAAYA,EACZE,MAAOA,CACX,CACJ,CASAC,cAAe,CACX,IAAMV,EAAS,IAAI,CAACA,MAAM,CAC1B,GAAIA,EAAOG,OAAO,CAACQ,YAAY,EAAI,CAAC,IAAI,CAACR,OAAO,CAACM,KAAK,CAAE,CACpD,IAAME,EAAeb,EAAYC,kBAAkB,CAACC,EAAQ,IAAI,CAC5D,AAACA,CAAAA,EAAOI,KAAK,CAACQ,UAAU,EACxB,CAAA,IAAI,CAACH,KAAK,CAAGE,EAAaF,KAAK,AAAD,EAE9B,AAAC,IAAI,CAACN,OAAO,CAACI,UAAU,EACxB,CAAA,IAAI,CAACA,UAAU,CAAGI,EAAaJ,UAAU,AAAD,CAEhD,MAEI,IAAI,CAACE,KAAK,CAAG,IAAI,CAACN,OAAO,CAACM,KAAK,EAAIT,EAAOS,KAAK,AAEvD,CAMAI,YAAYb,CAAM,CAAEG,CAAO,CAAE,CACzB,KAAK,CAACH,EAAQG,GACV,AAAC,IAAI,CAACK,CAAC,EACP,CAAA,IAAI,CAACA,CAAC,CAAG,CAAA,CAEjB,CAMAM,aAAaX,CAAO,CAAEY,CAAC,CAAE,CAIrB,OAHA,KAAK,CAACD,aAAaX,EAASY,GAC5B,IAAI,CAACC,EAAE,CAAG,IAAI,CAAChB,MAAM,CAACI,KAAK,CAACa,IAAI,CAACC,KAAK,CAAC,IAAI,CAACF,EAAE,EAC9C,IAAI,CAACG,MAAM,CAAG,CAAC,IAAI,CAACC,OAAO,KACpB,IAAI,AACf,CAIAC,UAAW,CACP,KAAK,CAACA,SAASC,KAAK,CAAC,IAAI,CAAEC,WAC3B,IAAI,CAACvB,MAAM,CAACwB,SAAS,CAAC,IAAI,CAAE,IAAI,CAACxB,MAAM,CAACyB,gBAAgB,GAC5D,CAIAL,SAAU,CACN,MAAO,AAAkB,UAAlB,OAAO,IAAI,CAACL,CAAC,EAChB,AAAmB,UAAnB,OAAO,IAAI,CAACC,EAAE,AACtB,CACJ,CACAnB,EAAOC,EAAYnB,SAAS,CAAE,CAC1B+C,QAAS,CAAA,EACTC,gBAAiB,CAAC,IAAK,KAAK,AAChC,GA+CA,GAAM,CAAEC,SAAAA,CAAQ,CAAEC,KAAAA,CAAI,CAAE,CAAI5C,IAEtB,CAAEiC,MAAOT,CAAK,CAAE,CAAItB,IAEpB,CAAEM,OAAQqC,CAAY,CAAE,CAAG,AAACzC,IAA2IO,WAAW,CAElL,CAAEmC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAEC,QAAAA,CAAO,CAAErC,OAAQsC,CAAmB,CAAEC,KAAAA,CAAI,CAAE7C,SAAU8C,CAAqB,CAAE7C,SAAU8C,CAAqB,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAEC,WAAAA,CAAU,CAAEC,eAAAA,CAAc,CAAE,CAAIzD,IAY3L,SAAS0D,IACL,IAAIC,EAASC,EACb,GAAI,IAAI,CAACC,OAAO,CAAE,CAEd,IAAK,IAAM9C,KADX4C,EAAUJ,EAAK,IAAI,CAACI,OAAO,CAAE,CAACG,OAAOC,SAAS,EACzB,IAAI,CAAChD,MAAM,EAAE,CAC9B,IAAMP,EAAUO,EAAOiD,SAAS,CAACC,SAAS,CAAC,KAAM,CAAA,IAC7ClD,EAAOiD,SAAS,CAACC,SAAS,CAAC,MAAO,CAAA,GACtC,GAAIzD,EACA,IAAK,IAAM0D,KAAO1D,EACV4C,EAAsBc,IAAQA,EAAMP,IACpCA,EAAUO,EACVN,EAAS,CAAA,EAIzB,CACIA,GACA,CAAA,IAAI,CAACD,OAAO,CAAGA,CAAM,CAE7B,CACJ,CAaA,MAAMQ,UAAqBtB,EAMvB,OAAOuB,QAAQC,CAAS,CAAE,CAClBb,EAAWb,EAAU,kBACrBG,EAASuB,EAAW,yBAA0BX,EAEtD,CASAY,MAAO,CACH,KAAK,CAACA,KAAKjC,KAAK,CAAC,IAAI,CAAEC,WACvB,IAAI,CAACpB,OAAO,CAACqD,QAAQ,CAAG,KAAK,CACjC,CAMAC,kBAAmB,CACf,IAAMC,EAAW,KACb,IAAK,IAAM1D,KAAU,IAAI,CAACI,KAAK,CAACJ,MAAM,CAAE,CACpC,IAAM2D,EAAQ3D,EAAO2D,KAAK,AAC1B3D,CAAAA,EAAO2D,KAAK,CAAG3D,EAAO4D,KAAK,CAC3B5D,EAAO4D,KAAK,CAAGD,CACnB,CACJ,EACAD,IACA,IAAMG,EAAU,KAAK,CAACJ,mBAEtB,OADAC,IACOG,CACX,CAMAC,SAASC,CAAK,CAAEC,CAAG,CAAEC,CAAG,CAAE,CAEtB,IAAMC,EAAQH,EAAMb,SAAS,CAAC,MAAQ,EAAE,CAAEiB,EAASJ,EAAMb,SAAS,CAAC,MACnEa,EAAMK,SAAS,CAAC,IAAKD,EAAQ,KAAK,EAAG,CAAEE,OAAQ,CAAA,CAAK,GACpD,IAAMC,EAAc,KAAK,CAACR,SAASC,EAAOC,EAAKC,GAG/C,OADAF,EAAMK,SAAS,CAAC,IAAKF,EAAMK,KAAK,CAACD,EAAYE,KAAK,CAAEF,EAAYG,GAAG,EAAG,KAAK,EAAG,CAAEJ,OAAQ,CAAA,CAAK,GACtFC,CACX,CAcAI,eAAevE,CAAO,CAAE,CACpB,IAEIwE,EAFE,CAAEC,UAAAA,CAAS,CAAEC,OAAAA,CAAM,CAAE,CAAG,IAAI,CAC5B,CAAEC,GAAAA,CAAE,CAAE,CAAG3E,EAEf,GAAI2E,EAAI,CACJ,IAAM7E,EAAQmC,EAAKyC,EAAQ,AAAC5E,GAAUA,EAAM6E,EAAE,GAAKA,GACnDH,EAAa1E,EAAQA,EAAM8E,KAAK,CAAG,KAAK,CAC5C,CACA,GAAI,AAAsB,KAAA,IAAfJ,EAA4B,CACnC,IAAM1E,EAAQmC,EAAKyC,EAAQ,AAAC5E,GAAWA,EAAMc,CAAC,GAAKZ,EAAQY,CAAC,EACxDd,EAAMe,EAAE,GAAKb,EAAQa,EAAE,EACvB,CAACf,EAAM+E,OAAO,EAClBL,EAAa1E,EAAQA,EAAM8E,KAAK,CAAG,KAAK,CAC5C,CAQA,OANI,IAAI,CAACE,OAAO,EACZ5C,EAAsBsC,IACtBtC,EAAsBuC,IACtBD,GAAcC,GACdD,CAAAA,GAAcC,CAAQ,EAEnBD,CACX,CACAO,eAAejF,CAAK,CAAE,CAClB,IAAMkF,EAAWlF,EAAMmF,KAAK,AAC5BnF,CAAAA,EAAMmF,KAAK,CAAG5C,EAAKvC,EAAMoF,KAAK,EAAEC,QAASrF,EAAMmF,KAAK,EAChDnF,EAAMsF,SAAS,EAAItF,EAAMuF,SAAS,EAAEC,OACpCxF,EAAMsF,SAAS,CAACG,GAAG,CAAC,CAChBD,MAAO,CAAC,EAAExF,EAAMuF,SAAS,CAACC,KAAK,CAAC,EAAE,CAAC,AACvC,GAEJ,KAAK,CAACP,eAAe5D,KAAK,CAAC,IAAI,CAAEC,WACjCtB,EAAMmF,KAAK,CAAGD,CAClB,CAIAQ,eAAe1F,CAAK,CAAE,CAClB,IAAM0D,EAAQ,IAAI,CAACA,KAAK,CAAEC,EAAQ,IAAI,CAACA,KAAK,CAAEC,EAAU,IAAI,CAAC+B,aAAa,CAAEzF,EAAU,IAAI,CAACA,OAAO,CAAE0F,EAAiB1F,EAAQ0F,cAAc,EAAI,EAAGC,EAAc,AAAC7F,CAAAA,EAAMuF,SAAS,EAAEC,OAAS,CAAA,EAAK,EAAGM,EAAgB,IAAI,CAACC,YAAY,CAAGnC,EAAQoC,MAAM,CAAEC,EAAO1D,EAAKvC,EAAMe,EAAE,CAAEf,EAAMc,CAAC,CAAId,CAAAA,EAAMkG,GAAG,EAAI,CAAA,GAAKC,EAAejG,EAAQiG,YAAY,CAAEC,EAAU,IAAI,CAACjG,KAAK,CAACiG,OAAO,CAAEC,EAAW,IAAI,CAAClG,KAAK,CAACkG,QAAQ,CACvYlB,EAAQnF,EAAMmF,KAAK,CAAEmB,EAAS5C,EAAM6C,SAAS,CAACN,EAAM,EAAG,EAAG,EAAG,GAC3D5F,EAASmG,KAAKC,GAAG,CAACH,EAASnB,GAAQuB,EAAW,IAAI,CAACvG,KAAK,CAACuG,QAAQ,CAAEC,EAAcpE,EAAKrC,EAAQyG,WAAW,CAAE,GAC7GC,EAAiBC,EAAaC,EAAUlD,EAAQoC,MAAM,CAAEe,EAAcP,KAAKQ,KAAK,CAACpD,EAAQ4B,KAAK,EAAGyB,EAAQC,EAASC,EAASC,EAC3HxB,IAEIgB,AADJA,CAAAA,EAAkBhB,EAAiBvF,CAAK,EAClB,GAClBuG,CAAAA,EAAkB,CAAA,EAEtBzB,GAASyB,EAAkB,EAC3BN,GAAUM,EAAkB,GAEhCzB,EAAQqB,KAAKxC,GAAG,CAACmB,EAAO,KACxBmB,EAASvE,EAAMuE,EAAQ,IAAK5C,EAAMwC,GAAG,CAAG,IAEpCjE,EAAQjC,EAAME,OAAO,CAACmH,UAAU,IAChCP,GAAY,AAACN,CAAAA,KAAKc,IAAI,CAACtH,EAAME,OAAO,CAACmH,UAAU,EAAIN,CAAU,EAAK,EAClEA,EAAcP,KAAKc,IAAI,CAACtH,EAAME,OAAO,CAACmH,UAAU,GAGhDnH,EAAQqH,cAAc,EACtBnF,EAAsBpC,EAAMwH,KAAK,GACjC7D,EAAM8D,UAAU,EAChBzH,CAAAA,EAAMwH,KAAK,CAAG7D,EAAM4C,SAAS,CAACvG,EAAMO,CAAC,CAAE,EAAG,EAAG,EAAG,EAAGL,EAAQqH,cAAc,CAAA,EAE7E,IAAMzG,EAAIkB,EAAMwE,KAAKzC,GAAG,CAACoB,EAAOmB,GAASK,GAAgEnB,EAAQzE,AAArDiB,EAAMwE,KAAKxC,GAAG,CAACmB,EAAOmB,GAASK,GAA2B7F,EAChH4G,EAAIlB,KAAKzC,GAAG,CAACtB,EAAgB,AAAwB,UAAxB,OAAO0D,EACtCA,EAAawB,MAAM,CACnBxB,GAAgB,EAAIY,GAAcP,KAAKzC,GAAG,CAACyB,EAAOuB,GAAe,GAC/DxB,EAAY,CACdzE,EAAAA,EACAP,EAAGyB,EAAM,AAAChC,CAAAA,EAAMwH,KAAK,EAAI,CAAA,EAAKV,EAASH,GACvCnB,MAAAA,EACAoC,OAAQb,EACRW,EAAAA,CACJ,CACA1H,CAAAA,EAAMuF,SAAS,CAAGA,EAEbmB,EAMD1G,EAAM6H,UAAU,CAAC,EAAE,EAAI/B,EACnBD,EANJ7F,EAAM6H,UAAU,CAAC,EAAE,EAAIhC,EACnBC,EACAP,EAAUC,KAAK,CAAG,EAQ1B0B,EAAUD,AADVA,CAAAA,EAAS1B,EAAUzE,CAAC,AAADA,EACAyE,EAAUC,KAAK,CAC9ByB,EAAS,GAAKC,EAAUxD,EAAMwC,GAAG,EACjCe,EAASlF,EAAMkF,EAAQ,EAAGvD,EAAMwC,GAAG,EAEnCiB,EAAUD,AADVA,CAAAA,EAAUnF,EAAMmF,EAAS,EAAGxD,EAAMwC,GAAG,CAAA,EACjBe,EACpBjH,EAAMoF,KAAK,CAAG9C,EAAMiD,EAAW,CAC3BzE,EAAGmG,EACHzB,MAAO0B,EAAUD,EACjB5B,QAAS8B,EAAUA,EAAU,EAAI,IACrC,IAGAnH,EAAMoF,KAAK,CAAG,KAGlB,IAAMyC,EAAa7H,EAAM6H,UAAU,CAC7BC,EAAS,EAAA,CAACpB,EACVqB,EAAS,EAACrB,EACVsB,EAAkB,IAAI,CAACrC,aAAa,CACtC,IAAI,CAACA,aAAa,CAACK,MAAM,CACzB,CAACpC,EAAQ4B,KAAK,CAAG,EAEjBkB,EACAmB,CAAU,CAACC,EAAO,EAAIvC,EAAUC,KAAK,CAAG,EAGxCqC,CAAU,CAACC,EAAO,CAAG/F,EAAM8F,CAAU,CAACC,EAAO,CACzC,AAACpE,CAAAA,EAAMuE,QAAQ,CAAG,GAAK,CAAA,EAAK1C,EAAUC,KAAK,CAAE9B,EAAMwE,IAAI,CAAG7B,EAAU3C,EAAMwE,IAAI,CAAGxE,EAAMwC,GAAG,CAAGG,EAAW,GAEhHwB,CAAU,CAACE,EAAO,CAAGhG,EAAM8F,CAAU,CAACE,EAAO,CAAI,AAACrB,CAAAA,EAAW,GAAK,CAAA,EAAKsB,EAAiBrE,EAAMwE,GAAG,CAAG/B,EAASzC,EAAMwE,GAAG,CAAGxE,EAAMuC,GAAG,CAAGE,EAAU,GAE/IS,CAAAA,EAAc7G,EAAM6G,WAAW,AAAD,IAGtBxE,EAAsBwE,IACtBA,CAAAA,EAAcA,EAAYuB,MAAM,AAAD,EAG/B,AAAChG,EAAsByE,IACvBA,CAAAA,EAAc,CAAA,EAElB7G,EAAMqI,aAAa,CAAG/F,EAAMiD,GAC5B6B,EAAgBZ,KAAKxC,GAAG,CAACwC,KAAKQ,KAAK,CAAC3G,EAASwG,EAAc7G,EAAMmF,KAAK,CAClEA,GAAQ,GACZnF,EAAMsI,YAAY,CAAG,CACjBxH,EAAG4C,EAAMuE,QAAQ,CACb1C,EAAUzE,CAAC,CAAGT,EAAS+G,EACvB7B,EAAUzE,CAAC,CACfP,EAAGgF,EAAUhF,CAAC,CACdiF,MAAO4B,EACPQ,OAAQrC,EAAUqC,MAAM,AAC5B,GAKJ5H,EAAM9B,GAAG,CAAG8B,EAAMuI,QAAQ,EAAIvI,EAAMwI,IAAI,CACxCxI,EAAMyI,SAAS,CAAG9E,EAAM8D,UAAU,EAAE,CAACzH,EAAMO,CAAC,EAAI,GAAG,AACvD,CAIAgG,WAAY,CAER,IAAK,IAAMvG,KADX,KAAK,CAACuG,UAAUlF,KAAK,CAAC,IAAI,CAAEC,WACR,IAAI,CAACsD,MAAM,EAC3B,IAAI,CAACc,cAAc,CAAC1F,EAE5B,CAeAuB,UAAUvB,CAAK,CAAE0I,CAAI,CAAE,CACnB,IAAMC,EAAa,IAAI,CAACzI,OAAO,CAAE0I,EAAW,IAAI,CAACzI,KAAK,CAACyI,QAAQ,CAAEC,EAAO7I,EAAM8I,SAAS,CAAEvD,EAAYvF,EAAMuF,SAAS,CAAE8C,EAAgBrI,EAAMqI,aAAa,CAAEC,EAAetI,EAAMsI,YAAY,CAAES,EAAa/I,EAAMgJ,KAAK,CAAEC,EAAaN,EAAWO,MAAM,CAACH,GAAc,SAAS,EAC1Q,CAAC,EAAII,EAAiB,AAAsB,KAAA,IAAfJ,EAC7B,OAASL,EAAMU,EAAY,IAAI,CAACC,YAAY,CAACrJ,EAAO+I,GAAaO,EAAY/G,EAAK,IAAI,CAACpC,KAAK,CAACD,OAAO,CAACC,KAAK,CAACmJ,SAAS,CAAEL,EAAUK,SAAS,EACzIC,EAAUvJ,EAAMuJ,OAAO,CAAEC,EAAYxJ,EAAM6G,WAAW,CAC1D,GAAI,AAAC7G,EAAMkB,MAAM,EAAIlB,AAAkB,CAAA,IAAlBA,EAAMyJ,OAAO,CAqDzBF,GACLvJ,CAAAA,EAAMuJ,OAAO,CAAGA,EAAQG,OAAO,EAAC,OAxBhC,GA5BIH,EACAA,EAAQI,IAAI,CAACjB,EAAK,CAACnD,IAGnBvF,EAAMuJ,OAAO,CAAGA,EAAUX,EAASgB,CAAC,CAAC,SAChCC,QAAQ,CAAC7J,EAAM8J,YAAY,IAC3BC,GAAG,CAAC/J,EAAMgK,KAAK,EAAI,IAAI,CAACA,KAAK,EAClCT,EAAQI,IAAI,CAAGf,CAAQ,CAACC,EAAK,CAACvG,EAAMiD,IAC/BsE,QAAQ,CAAC7J,EAAM8J,YAAY,IAC3BD,QAAQ,CAAC,gCACTE,GAAG,CAACR,IAGTlB,IACIkB,EAAQU,QAAQ,EAChBV,EAAQU,QAAQ,CAACvB,EAAK,CAACpG,EAAM+F,IAC7BkB,EAAQW,eAAe,CAACxB,EAAK,CAACpG,EAAMgG,MAGpCiB,EAAQW,eAAe,CAAGtB,EAASuB,QAAQ,CAAC7B,EAAaxH,CAAC,CAAEwH,EAAa/H,CAAC,CAAE+H,EAAa9C,KAAK,CAAE8C,EAAaV,MAAM,EACnH2B,EAAQU,QAAQ,CACZrB,CAAQ,CAACC,EAAK,CAACR,GACVwB,QAAQ,CAAC,+BACTE,GAAG,CAACR,GACJa,IAAI,CAACb,EAAQW,eAAe,IAIzC,CAAC,IAAI,CAAC/J,KAAK,CAACQ,UAAU,GACtB4I,EACKI,IAAI,CAACjB,EAAK,CAACU,EAAWE,GACtBe,MAAM,CAAC1B,EAAW0B,MAAM,EACzBhC,GAAe,CAEX,AAAChG,EAAsBmH,IACvBA,CAAAA,EAAY,CAAC,CAAA,EAEbnH,EAAsBsG,EAAW9B,WAAW,GAC5C2C,CAAAA,EAAYlH,EAAMqG,EAAW9B,WAAW,CAAE2C,EAAS,EAEvD,IAAMc,EAAQd,EAAUc,IAAI,EACxB9J,EAAM4I,EAAUkB,IAAI,EAAEC,QAAQ,CAAC,KAAMhM,GAAG,IACxCiC,EAAMR,EAAMQ,KAAK,EAAI,IAAI,CAACA,KAAK,EAC1B+J,QAAQ,CAAC,KAAMhM,GAAG,EAC3B6K,CAAAA,EAAUkB,IAAI,CAAGA,EACjBf,EACKU,QAAQ,CAACd,EAAe,CAACC,EAAWE,GACpCe,MAAM,CAAC1B,EAAW0B,MAAM,CACjC,CAMZ,CAIAG,YAAa,CACT,IAAM9B,EAAO,IAAI,CAAClH,gBAAgB,GAElC,IAAK,IAAMxB,KAAS,IAAI,CAAC4E,MAAM,CAC3B,IAAI,CAACrD,SAAS,CAACvB,EAAO0I,EAE9B,CAOAlH,kBAAmB,CACf,OAAQ,IAAI,CAACrB,KAAK,CAACsK,UAAU,CAAI,CAAA,IAAI,CAACvK,OAAO,CAACwK,cAAc,EAAI,GAAE,EAC9D,UACA,MACR,CAIAC,cAAc3K,CAAK,CAAE,CACjB,IAAMuF,EAAYvF,EAAMuF,SAAS,CAAEJ,EAAQnF,EAAMmF,KAAK,CAAEqC,EAAQxH,EAAMwH,KAAK,QAC3E,AAAKjC,EAGY,AAAiB,KAAA,IAAVJ,GACpB,AAAiB,KAAA,IAAVqC,GACPA,GAAS,GACTA,GAAS,IAAI,CAAC7D,KAAK,CAACuC,GAAG,EACvB,AAACX,CAAAA,EAAUzE,CAAC,EAAI,CAAA,EAAMyE,CAAAA,EAAUC,KAAK,EAAI,CAAA,GAAM,GAC/CL,GAAS,IAAI,CAACzB,KAAK,CAACwC,GAAG,CAPhB,KAAK,CAACyE,cAActJ,KAAK,CAAC,IAAI,CAAEC,UAS/C,CACJ,CAMA6B,EAAayH,cAAc,CAAGtI,EAAMT,EAAa+I,cAAc,CAztBlC,CAkCzBlK,aAAc,CAAA,EACdmK,WAAY,CACRC,UAAW,WACP,IAAI1C,EAAS,IAAI,CAACvB,WAAW,CAI7B,GAHItH,EAAS6I,IACTA,CAAAA,EAASA,EAAOA,MAAM,AAAD,EAErB9I,EAAS8I,IAAWA,EAAS,EAC7B,OAAO/I,EAAa+I,AAAS,IAATA,GAAgB,GAE5C,EACA2C,OAAQ,CAAA,EACRC,cAAe,SACfC,MAAO,CACHC,WAAY,QAChB,CACJ,EACAC,QAAS,CACLC,aAAc,4EACdC,YAAa,yFACjB,EACAlF,aAAc,EACdmF,WAAY,CAChB,GAiqBApJ,EAAoBiB,EAAazE,SAAS,CAAE,CACxCe,WA/aqDI,EAgbrD0L,cAAe,CAAC,KAAM,IAAI,CAC1BC,mBAAoB,CAAA,EACpBC,gBAAiB,CAAC,IAAI,CACtBC,eAAgB,CAAC,IAAK,KAAM,IAAI,CAChCC,eAAgB,CAAA,EAChB9C,KAAM,SACN+C,QAAS,AAACxM,IAA2IW,MAAM,CAACrB,SAAS,CAACkN,OAAO,CAC7KC,cAAejK,EACfkK,YAAalK,CACjB,GACAxC,IAA0I2M,kBAAkB,CAAC,SAAU5I,GAavK,IAAM6I,EAAKhN,IACXiN,AAR0D9I,EAQtCC,OAAO,CAAC4I,EAAEE,IAAI,EACL,IAAMpN,EAAeE,IAGxC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}