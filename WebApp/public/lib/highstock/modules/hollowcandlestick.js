!/**
 * Highstock JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/hollowcandlestick
 * @requires highcharts
 * @requires highcharts/modules/stock
 *
 * Hollow Candlestick series type for Highcharts Stock
 *
 * (c) 2010-2025 <PERSON><PERSON>
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.SeriesRegistry,t._Highcharts.Axis):"function"==typeof define&&define.amd?define("highcharts/modules/hollowcandlestick",["highcharts/highcharts"],function(t){return e(t,t.SeriesRegistry,t.Axis)}):"object"==typeof exports?exports["highcharts/modules/hollowcandlestick"]=e(t._Highcharts,t._Highcharts.SeriesRegistry,t._Highcharts.Axis):t.Highcharts=e(t.Highcharts,t.Highcharts.SeriesRegistry,t.Highcharts.Axis)}("undefined"==typeof window?this:window,(t,e,i)=>(()=>{"use strict";var s={512:t=>{t.exports=e},532:t=>{t.exports=i},944:e=>{e.exports=t}},o={};function r(t){var e=o[t];if(void 0!==e)return e.exports;var i=o[t]={exports:{}};return s[t](i,i.exports,r),i.exports}r.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return r.d(e,{a:e}),e},r.d=(t,e)=>{for(var i in e)r.o(e,i)&&!r.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var l={};r.d(l,{default:()=>C});var n=r(944),a=r.n(n),h=r(512),p=r.n(h);let{seriesTypes:{candlestick:c}}=p();class u extends c.prototype.pointClass{getClassName(){let t=super.getClassName.apply(this),e=this.index,i=this.series.hollowCandlestickData[e];return i.isBullish||"up"!==i.trendDirection||(t+="-bearish-up"),t}}var d=r(532),g=r.n(d);let{seriesTypes:{candlestick:f}}=p(),{addEvent:w,merge:x}=a();class y extends f{constructor(){super(...arguments),this.hollowCandlestickData=[]}getPriceMovement(){let t,e=this.allGroupedTable||this.dataTable,i=e.rowCount,s=this.hollowCandlestickData;s.length=0;for(let o=0;o<i;o++){let i=e.getRow(o,this.pointArrayMap);s.push(this.isBullish(i,o?t:i)),t=i}}getLineColor(t){return"up"===t?this.options.upColor||"#06b535":this.options.color||"#f21313"}getPointFill(t){return t.isBullish?"transparent":"up"===t.trendDirection?this.options.upColor||"#06b535":this.options.color||"#f21313"}init(){super.init.apply(this,arguments),this.hollowCandlestickData=[]}isBullish(t,e){return{isBullish:(t[0]||0)<=(t[3]||0),trendDirection:(t[3]||0)<(e?.[3]||0)?"down":"up"}}pointAttribs(t,e){let i,s=super.pointAttribs.call(this,t,e),o=t.index,r=this.hollowCandlestickData[o];return s.fill=this.getPointFill(r)||s.fill,s.stroke=this.getLineColor(r.trendDirection)||s.stroke,e&&(s.fill=(i=this.options.states[e]).color||s.fill,s.stroke=i.lineColor||s.stroke,s["stroke-width"]=i.lineWidth||s["stroke-width"]),s}}y.defaultOptions=x(f.defaultOptions,{color:"#f21313",dataGrouping:{groupAll:!0,groupPixelWidth:10},lineColor:"#f21313",upColor:"#06b535",upLineColor:"#06b535"}),w(y,"updatedData",function(){this.hollowCandlestickData.length&&(this.hollowCandlestickData.length=0)}),w(g(),"postProcessData",function(){this.series.forEach(function(t){t.is("hollowcandlestick")&&t.getPriceMovement()})}),y.prototype.pointClass=u,p().registerSeriesType("hollowcandlestick",y);let C=a();return l.default})());