{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * Treegraph chart series type\n * @module highcharts/modules/treegraph\n * @requires highcharts\n * @requires highcharts/modules/treemap\n *\n *  (c) 2010-2025 Pawel Lysy Grzegorz Blachlinski\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"SVGRenderer\"], root[\"_Highcharts\"][\"Point\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"SVGElement\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/treegraph\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"],amd1[\"SVGRenderer\"],amd1[\"Point\"],amd1[\"Color\"],amd1[\"SVGElement\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/treegraph\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"SVGRenderer\"], root[\"_Highcharts\"][\"Point\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"SVGElement\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"SVGRenderer\"], root[\"Highcharts\"][\"Point\"], root[\"Highcharts\"][\"Color\"], root[\"Highcharts\"][\"SVGElement\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__540__, __WEBPACK_EXTERNAL_MODULE__260__, __WEBPACK_EXTERNAL_MODULE__620__, __WEBPACK_EXTERNAL_MODULE__28__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 28:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__28__;\n\n/***/ }),\n\n/***/ 260:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__260__;\n\n/***/ }),\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 540:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__540__;\n\n/***/ }),\n\n/***/ 620:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__620__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ treegraph_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Series/PathUtilities.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nconst getLinkPath = {\n    'default': getDefaultPath,\n    straight: getStraightPath,\n    curved: getCurvedPath\n};\n/**\n *\n */\nfunction getDefaultPath(pathParams) {\n    const { x1, y1, x2, y2, width = 0, inverted = false, radius, parentVisible } = pathParams;\n    const path = [\n        ['M', x1, y1],\n        ['L', x1, y1],\n        ['C', x1, y1, x1, y2, x1, y2],\n        ['L', x1, y2],\n        ['C', x1, y1, x1, y2, x1, y2],\n        ['L', x1, y2]\n    ];\n    return parentVisible ?\n        applyRadius([\n            ['M', x1, y1],\n            ['L', x1 + width * (inverted ? -0.5 : 0.5), y1],\n            ['L', x1 + width * (inverted ? -0.5 : 0.5), y2],\n            ['L', x2, y2]\n        ], radius) :\n        path;\n}\n/**\n *\n */\nfunction getStraightPath(pathParams) {\n    const { x1, y1, x2, y2, width = 0, inverted = false, parentVisible } = pathParams;\n    return parentVisible ? [\n        ['M', x1, y1],\n        ['L', x1 + width * (inverted ? -1 : 1), y2],\n        ['L', x2, y2]\n    ] : [\n        ['M', x1, y1],\n        ['L', x1, y2],\n        ['L', x1, y2]\n    ];\n}\n/**\n *\n */\nfunction getCurvedPath(pathParams) {\n    const { x1, y1, x2, y2, offset = 0, width = 0, inverted = false, parentVisible } = pathParams;\n    return parentVisible ?\n        [\n            ['M', x1, y1],\n            [\n                'C',\n                x1 + offset,\n                y1,\n                x1 - offset + width * (inverted ? -1 : 1),\n                y2,\n                x1 + width * (inverted ? -1 : 1),\n                y2\n            ],\n            ['L', x2, y2]\n        ] :\n        [\n            ['M', x1, y1],\n            ['C', x1, y1, x1, y2, x1, y2],\n            ['L', x2, y2]\n        ];\n}\n/**\n * General function to apply corner radius to a path\n * @private\n */\nfunction applyRadius(path, r) {\n    const d = [];\n    for (let i = 0; i < path.length; i++) {\n        const x = path[i][1];\n        const y = path[i][2];\n        if (typeof x === 'number' && typeof y === 'number') {\n            // MoveTo\n            if (i === 0) {\n                d.push(['M', x, y]);\n            }\n            else if (i === path.length - 1) {\n                d.push(['L', x, y]);\n                // CurveTo\n            }\n            else if (r) {\n                const prevSeg = path[i - 1];\n                const nextSeg = path[i + 1];\n                if (prevSeg && nextSeg) {\n                    const x1 = prevSeg[1], y1 = prevSeg[2], x2 = nextSeg[1], y2 = nextSeg[2];\n                    // Only apply to breaks\n                    if (typeof x1 === 'number' &&\n                        typeof x2 === 'number' &&\n                        typeof y1 === 'number' &&\n                        typeof y2 === 'number' &&\n                        x1 !== x2 &&\n                        y1 !== y2) {\n                        const directionX = x1 < x2 ? 1 : -1, directionY = y1 < y2 ? 1 : -1;\n                        d.push([\n                            'L',\n                            x - directionX * Math.min(Math.abs(x - x1), r),\n                            y - directionY * Math.min(Math.abs(y - y1), r)\n                        ], [\n                            'C',\n                            x,\n                            y,\n                            x,\n                            y,\n                            x + directionX * Math.min(Math.abs(x - x2), r),\n                            y + directionY * Math.min(Math.abs(y - y2), r)\n                        ]);\n                    }\n                }\n                // LineTo\n            }\n            else {\n                d.push(['L', x, y]);\n            }\n        }\n    }\n    return d;\n}\nconst PathUtilities = {\n    applyRadius,\n    getLinkPath\n};\n/* harmony default export */ const Series_PathUtilities = (PathUtilities);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SVGRenderer\"],\"commonjs\":[\"highcharts\",\"SVGRenderer\"],\"commonjs2\":[\"highcharts\",\"SVGRenderer\"],\"root\":[\"Highcharts\",\"SVGRenderer\"]}\nvar highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_ = __webpack_require__(540);\nvar highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default = /*#__PURE__*/__webpack_require__.n(highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_);\n;// ./code/es-modules/Series/Treegraph/TreegraphNode.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy Grzegorz Blachlinski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { seriesTypes: { treemap: { prototype: { NodeClass: TreemapNode } } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n */\nclass TreegraphNode extends TreemapNode {\n    constructor() {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        super(...arguments);\n        this.mod = 0;\n        this.shift = 0;\n        this.change = 0;\n        this.children = [];\n        this.preX = 0;\n        this.hidden = false;\n        this.wasVisited = false;\n        this.collapsed = false;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Get the next left node which is either first child or thread.\n     *\n     * @return {TreegraphNode|undefined}\n     *         Next left node child or thread.\n     */\n    nextLeft() {\n        return this.getLeftMostChild() || this.thread;\n    }\n    /**\n     * Get the next right node which is either last child or thread.\n     *\n     * @return {TreegraphNode|undefined}\n     *         Next right node child or thread.\n     */\n    nextRight() {\n        return this.getRightMostChild() || this.thread;\n    }\n    /**\n     * Return the left one of the greatest uncommon ancestors of a\n     * leftInternal node and it's right neighbor.\n     *\n     * @param {TreegraphNode} leftIntNode\n     * @param {TreegraphNode} defaultAncestor\n     * @return {TreegraphNode}\n     *         Left one of the greatest uncommon ancestors of a leftInternal\n     *         node and it's right neighbor.\n     *\n     */\n    getAncestor(leftIntNode, defaultAncestor) {\n        const leftAnc = leftIntNode.ancestor;\n        if (leftAnc.children[0] === this.children[0]) {\n            return leftIntNode.ancestor;\n        }\n        return defaultAncestor;\n    }\n    /**\n     * Get node's first sibling, which is not hidden.\n     *\n     * @return {TreegraphNode|undefined}\n     *         First sibling of the node which is not hidden or undefined, if it\n     *         does not exists.\n     */\n    getLeftMostSibling() {\n        const parent = this.getParent();\n        if (parent) {\n            for (const child of parent.children) {\n                if (child && child.point.visible) {\n                    return child;\n                }\n            }\n        }\n    }\n    /**\n     * Check if the node is a leaf (if it has any children).\n     *\n     * @return {boolean}\n     *         If the node has no visible children return true.\n     */\n    hasChildren() {\n        const children = this.children;\n        for (let i = 0; i < children.length; i++) {\n            if (children[i].point.visible) {\n                return true;\n            }\n        }\n        return false;\n    }\n    /**\n     * Get node's left sibling (if it exists).\n     *\n     * @return {TreegraphNode|undefined}\n     *         Left sibling of the node\n     */\n    getLeftSibling() {\n        const parent = this.getParent();\n        if (parent) {\n            const children = parent.children;\n            for (let i = this.relativeXPosition - 1; i >= 0; i--) {\n                if (children[i] && children[i].point.visible) {\n                    return children[i];\n                }\n            }\n        }\n    }\n    /**\n     * Get the node's first child (if it exists).\n     *\n     * @return {TreegraphNode|undefined}\n     *         Node's first child which isn't hidden.\n     */\n    getLeftMostChild() {\n        const children = this.children;\n        for (let i = 0; i < children.length; i++) {\n            if (children[i].point.visible) {\n                return children[i];\n            }\n        }\n    }\n    /**\n     * Get the node's last child (if it exists).\n     *\n     * @return {TreegraphNode|undefined}\n     *         Node's last child which isn't hidden.\n     */\n    getRightMostChild() {\n        const children = this.children;\n        for (let i = children.length - 1; i >= 0; i--) {\n            if (children[i].point.visible) {\n                return children[i];\n            }\n        }\n    }\n    /**\n     * Get the parent of current node or return undefined for root of the\n     * tree.\n     *\n     * @return {TreegraphNode|undefined}\n     *         Node's parent or undefined for root.\n     */\n    getParent() {\n        return this.parentNode;\n    }\n    /**\n     * Get node's first child which is not hidden.\n     *\n     * @return {TreegraphNode|undefined}\n     *         First child.\n     */\n    getFirstChild() {\n        const children = this.children;\n        for (let i = 0; i < children.length; i++) {\n            if (children[i].point.visible) {\n                return children[i];\n            }\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treegraph_TreegraphNode = (TreegraphNode);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Point\"],\"commonjs\":[\"highcharts\",\"Point\"],\"commonjs2\":[\"highcharts\",\"Point\"],\"root\":[\"Highcharts\",\"Point\"]}\nvar highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_ = __webpack_require__(260);\nvar highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default = /*#__PURE__*/__webpack_require__.n(highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_);\n;// ./code/es-modules/Series/Treegraph/TreegraphPoint.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy Grzegorz Blachlinski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { seriesTypes: { treemap: { prototype: { pointClass: TreemapPoint } } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { addEvent, fireEvent, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n */\nclass TreegraphPoint extends TreemapPoint {\n    constructor() {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        super(...arguments);\n        this.dataLabelOnHidden = true;\n        this.isLink = false;\n        this.setState = (highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default()).prototype.setState;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    draw() {\n        super.draw.apply(this, arguments);\n        // Run animation of hiding/showing of the point.\n        const graphic = this.graphic;\n        if (graphic) {\n            graphic.animate({\n                visibility: this.visible ? 'inherit' : 'hidden'\n            });\n        }\n        this.renderCollapseButton();\n    }\n    renderCollapseButton() {\n        const point = this, series = point.series, parentGroup = point.graphic && point.graphic.parentGroup, levelOptions = series.mapOptionsToLevel[point.node.level || 0] || {}, btnOptions = merge(series.options.collapseButton, levelOptions.collapseButton, point.options.collapseButton), { width, height, shape, style } = btnOptions, padding = 2, chart = this.series.chart, calculatedOpacity = (point.visible &&\n            (point.collapsed ||\n                !btnOptions.onlyOnHover ||\n                point.state === 'hover')) ? 1 : 0;\n        if (!point.shapeArgs) {\n            return;\n        }\n        this.collapseButtonOptions = btnOptions;\n        if (!point.collapseButton) {\n            if (!point.node.children.length || !btnOptions.enabled) {\n                return;\n            }\n            const { x, y } = this.getCollapseBtnPosition(btnOptions), fill = (btnOptions.fillColor ||\n                point.color ||\n                \"#cccccc\" /* Palette.neutralColor20 */);\n            point.collapseButton = chart.renderer\n                .label(point.collapsed ? '+' : '-', x, y, shape)\n                .attr({\n                height: height - 2 * padding,\n                width: width - 2 * padding,\n                padding: padding,\n                fill,\n                rotation: chart.inverted ? 90 : 0,\n                rotationOriginX: width / 2,\n                rotationOriginY: height / 2,\n                stroke: btnOptions.lineColor || \"#ffffff\" /* Palette.backgroundColor */,\n                'stroke-width': btnOptions.lineWidth,\n                'text-align': 'center',\n                align: 'center',\n                zIndex: 1,\n                opacity: calculatedOpacity,\n                visibility: point.visible ? 'inherit' : 'hidden'\n            })\n                .addClass('highcharts-tracker')\n                .addClass('highcharts-collapse-button')\n                .removeClass('highcharts-no-tooltip')\n                .css(merge({\n                color: typeof fill === 'string' ?\n                    chart.renderer.getContrast(fill) :\n                    \"#333333\" /* Palette.neutralColor80 */\n            }, style))\n                .add(parentGroup);\n            point.collapseButton.element.point = point;\n        }\n        else {\n            if (!point.node.children.length || !btnOptions.enabled) {\n                point.collapseButton.destroy();\n                delete point.collapseButton;\n            }\n            else {\n                const { x, y } = this.getCollapseBtnPosition(btnOptions);\n                point.collapseButton\n                    .attr({\n                    text: point.collapsed ? '+' : '-',\n                    rotation: chart.inverted ? 90 : 0,\n                    rotationOriginX: width / 2,\n                    rotationOriginY: height / 2,\n                    visibility: point.visible ? 'inherit' : 'hidden'\n                })\n                    .animate({\n                    x,\n                    y,\n                    opacity: calculatedOpacity\n                });\n            }\n        }\n    }\n    toggleCollapse(state) {\n        const series = this.series;\n        this.update({\n            collapsed: state ?? !this.collapsed\n        }, false, void 0, false);\n        fireEvent(series, 'toggleCollapse');\n        series.redraw();\n    }\n    destroy() {\n        if (this.collapseButton) {\n            this.collapseButton.destroy();\n            delete this.collapseButton;\n            this.collapseButton = void 0;\n        }\n        if (this.linkToParent) {\n            this.linkToParent.destroy();\n            delete this.linkToParent;\n        }\n        super.destroy.apply(this, arguments);\n    }\n    getCollapseBtnPosition(btnOptions) {\n        const point = this, chart = point.series.chart, inverted = chart.inverted, btnWidth = btnOptions.width, btnHeight = btnOptions.height, { x = 0, y = 0, width = 0, height = 0 } = point.shapeArgs || {};\n        return {\n            x: x +\n                btnOptions.x +\n                (inverted ? -btnHeight * 0.3 : width + btnWidth * -0.3),\n            y: y + height / 2 - btnHeight / 2 + btnOptions.y\n        };\n    }\n}\naddEvent(TreegraphPoint, 'mouseOut', function () {\n    const btn = this.collapseButton, btnOptions = this.collapseButtonOptions;\n    if (btn && btnOptions?.onlyOnHover && !this.collapsed) {\n        btn.animate({ opacity: 0 });\n    }\n});\naddEvent(TreegraphPoint, 'mouseOver', function () {\n    if (this.collapseButton && this.visible) {\n        this.collapseButton.animate({ opacity: 1 }, this.series.options.states?.hover?.animation);\n    }\n});\n// Handle showing and hiding of the points\naddEvent(TreegraphPoint, 'click', function () {\n    this.toggleCollapse();\n});\n/* *\n *\n *  Export Default\n *\n * */\n/* harmony default export */ const Treegraph_TreegraphPoint = (TreegraphPoint);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Color\"],\"commonjs\":[\"highcharts\",\"Color\"],\"commonjs2\":[\"highcharts\",\"Color\"],\"root\":[\"Highcharts\",\"Color\"]}\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_ = __webpack_require__(620);\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default = /*#__PURE__*/__webpack_require__.n(highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_);\n;// ./code/es-modules/Series/TreeUtilities.js\n/* *\n *\n *  (c) 2014-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { extend, isArray, isNumber, isObject, merge: TreeUtilities_merge, pick, relativeLength } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/* eslint-disable valid-jsdoc */\n/**\n * @private\n */\nfunction getColor(node, options) {\n    const index = options.index, mapOptionsToLevel = options.mapOptionsToLevel, parentColor = options.parentColor, parentColorIndex = options.parentColorIndex, series = options.series, colors = options.colors, siblings = options.siblings, points = series.points, chartOptionsChart = series.chart.options.chart;\n    let getColorByPoint, point, level, colorByPoint, colorIndexByPoint, color, colorIndex;\n    /**\n     * @private\n     */\n    const variateColor = (color) => {\n        const colorVariation = level && level.colorVariation;\n        if (colorVariation &&\n            colorVariation.key === 'brightness' &&\n            index &&\n            siblings) {\n            return highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default().parse(color).brighten(colorVariation.to * (index / siblings)).get();\n        }\n        return color;\n    };\n    if (node) {\n        point = points[node.i];\n        level = mapOptionsToLevel[node.level] || {};\n        getColorByPoint = point && level.colorByPoint;\n        if (getColorByPoint) {\n            colorIndexByPoint = point.index % (colors ?\n                colors.length :\n                chartOptionsChart.colorCount);\n            colorByPoint = colors && colors[colorIndexByPoint];\n        }\n        // Select either point color, level color or inherited color.\n        if (!series.chart.styledMode) {\n            color = pick(point && point.options.color, level && level.color, colorByPoint, parentColor && variateColor(parentColor), series.color);\n        }\n        colorIndex = pick(point && point.options.colorIndex, level && level.colorIndex, colorIndexByPoint, parentColorIndex, options.colorIndex);\n    }\n    return {\n        color: color,\n        colorIndex: colorIndex\n    };\n}\n/**\n * Creates a map from level number to its given options.\n *\n * @private\n *\n * @param {Object} params\n * Object containing parameters.\n * - `defaults` Object containing default options. The default options are\n *   merged with the userOptions to get the final options for a specific\n *   level.\n * - `from` The lowest level number.\n * - `levels` User options from series.levels.\n * - `to` The highest level number.\n *\n * @return {Highcharts.Dictionary<object>|null}\n * Returns a map from level number to its given options.\n */\nfunction getLevelOptions(params) {\n    const result = {};\n    let defaults, converted, i, from, to, levels;\n    if (isObject(params)) {\n        from = isNumber(params.from) ? params.from : 1;\n        levels = params.levels;\n        converted = {};\n        defaults = isObject(params.defaults) ? params.defaults : {};\n        if (isArray(levels)) {\n            converted = levels.reduce((obj, item) => {\n                let level, levelIsConstant, options;\n                if (isObject(item) && isNumber(item.level)) {\n                    options = TreeUtilities_merge({}, item);\n                    levelIsConstant = pick(options.levelIsConstant, defaults.levelIsConstant);\n                    // Delete redundant properties.\n                    delete options.levelIsConstant;\n                    delete options.level;\n                    // Calculate which level these options apply to.\n                    level = item.level + (levelIsConstant ? 0 : from - 1);\n                    if (isObject(obj[level])) {\n                        TreeUtilities_merge(true, obj[level], options); // #16329\n                    }\n                    else {\n                        obj[level] = options;\n                    }\n                }\n                return obj;\n            }, {});\n        }\n        to = isNumber(params.to) ? params.to : 1;\n        for (i = 0; i <= to; i++) {\n            result[i] = TreeUtilities_merge({}, defaults, isObject(converted[i]) ? converted[i] : {});\n        }\n    }\n    return result;\n}\n/**\n * @private\n * @todo Combine buildTree and buildNode with setTreeValues\n * @todo Remove logic from Treemap and make it utilize this mixin.\n */\nfunction setTreeValues(tree, options) {\n    const before = options.before, idRoot = options.idRoot, mapIdToNode = options.mapIdToNode, nodeRoot = mapIdToNode[idRoot], levelIsConstant = (options.levelIsConstant !== false), points = options.points, point = points[tree.i], optionsPoint = point && point.options || {}, children = [];\n    let childrenTotal = 0;\n    tree.levelDynamic = tree.level - (levelIsConstant ? 0 : nodeRoot.level);\n    tree.name = pick(point && point.name, '');\n    tree.visible = (idRoot === tree.id ||\n        options.visible === true);\n    if (typeof before === 'function') {\n        tree = before(tree, options);\n    }\n    // First give the children some values\n    tree.children.forEach((child, i) => {\n        const newOptions = extend({}, options);\n        extend(newOptions, {\n            index: i,\n            siblings: tree.children.length,\n            visible: tree.visible\n        });\n        child = setTreeValues(child, newOptions);\n        children.push(child);\n        if (child.visible) {\n            childrenTotal += child.val;\n        }\n    });\n    // Set the values\n    const value = pick(optionsPoint.value, childrenTotal);\n    tree.visible = value >= 0 && (childrenTotal > 0 || tree.visible);\n    tree.children = children;\n    tree.childrenTotal = childrenTotal;\n    tree.isLeaf = tree.visible && !childrenTotal;\n    tree.val = value;\n    return tree;\n}\n/**\n * Update the rootId property on the series. Also makes sure that it is\n * accessible to exporting.\n *\n * @private\n *\n * @param {Object} series\n * The series to operate on.\n *\n * @return {string}\n * Returns the resulting rootId after update.\n */\nfunction updateRootId(series) {\n    let rootId, options;\n    if (isObject(series)) {\n        // Get the series options.\n        options = isObject(series.options) ? series.options : {};\n        // Calculate the rootId.\n        rootId = pick(series.rootNode, options.rootId, '');\n        // Set rootId on series.userOptions to pick it up in exporting.\n        if (isObject(series.userOptions)) {\n            series.userOptions.rootId = rootId;\n        }\n        // Set rootId on series to pick it up on next update.\n        series.rootNode = rootId;\n    }\n    return rootId;\n}\n/**\n * Get the node width, which relies on the plot width and the nodeDistance\n * option.\n *\n * @private\n */\nfunction getNodeWidth(series, columnCount) {\n    const { chart, options } = series, { nodeDistance = 0, nodeWidth = 0 } = options, { plotSizeX = 1 } = chart;\n    // Node width auto means they are evenly distributed along the width of\n    // the plot area\n    if (nodeWidth === 'auto') {\n        if (typeof nodeDistance === 'string' && /%$/.test(nodeDistance)) {\n            const fraction = parseFloat(nodeDistance) / 100, total = columnCount + fraction * (columnCount - 1);\n            return plotSizeX / total;\n        }\n        const nDistance = Number(nodeDistance);\n        return ((plotSizeX + nDistance) /\n            (columnCount || 1)) - nDistance;\n    }\n    return relativeLength(nodeWidth, plotSizeX);\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst TreeUtilities = {\n    getColor,\n    getLevelOptions,\n    getNodeWidth,\n    setTreeValues,\n    updateRootId\n};\n/* harmony default export */ const Series_TreeUtilities = (TreeUtilities);\n\n;// ./code/es-modules/Series/Treegraph/TreegraphLink.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy Grzegorz Blachlinski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { pick: TreegraphLink_pick, extend: TreegraphLink_extend } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { seriesTypes: { column: { prototype: { pointClass: ColumnPoint } } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n */\nclass LinkPoint extends ColumnPoint {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(series, options, x, point) {\n        super(series, options, x);\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.dataLabelOnNull = true;\n        this.formatPrefix = 'link';\n        this.isLink = true;\n        this.node = {};\n        this.formatPrefix = 'link';\n        this.dataLabelOnNull = true;\n        if (point) {\n            this.fromNode = point.node.parentNode.point;\n            this.visible = point.visible;\n            this.toNode = point;\n            this.id = this.toNode.id + '-' + this.fromNode.id;\n        }\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    update(options, redraw, animation, runEvent) {\n        const oldOptions = {\n            id: this.id,\n            formatPrefix: this.formatPrefix\n        };\n        highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default().prototype.update.call(this, options, this.isLink ? false : redraw, // Hold the redraw for nodes\n        animation, runEvent);\n        this.visible = this.toNode.visible;\n        TreegraphLink_extend(this, oldOptions);\n        if (TreegraphLink_pick(redraw, true)) {\n            this.series.chart.redraw(animation);\n        }\n    }\n}\n/* *\n *\n *  Export Default\n *\n * */\n/* harmony default export */ const TreegraphLink = (LinkPoint);\n\n;// ./code/es-modules/Series/Treegraph/TreegraphLayout.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy Grzegorz Blachlinski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n */\nclass TreegraphLayout {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Create dummy node, which allows to manually set the level of the node.\n     *\n     * @param {TreegraphNode} parent\n     *        Parent node, to which the dummyNode should be connected.\n     * @param {TreegraphNode} child\n     *        Child node, which should be connected to dummyNode.\n     * @param {number} gapSize\n     *        Remaining gap size.\n     *\n     * @return {TreegraphNode}\n     *         DummyNode as a parent of nodes, which column changes.\n     */\n    static createDummyNode(parent, child, gapSize) {\n        // Initialise dummy node.\n        const dummyNode = new Treegraph_TreegraphNode();\n        dummyNode.id = parent.id + '-' + gapSize;\n        dummyNode.ancestor = parent;\n        // Add connection from new node to the previous points.\n        // First connection to itself.\n        dummyNode.children.push(child);\n        dummyNode.parent = parent.id;\n        dummyNode.parentNode = parent;\n        dummyNode.point = child.point;\n        dummyNode.level = child.level - gapSize;\n        dummyNode.relativeXPosition = child.relativeXPosition;\n        dummyNode.visible = child.visible;\n        // Then connection from parent to dummyNode.\n        parent.children[child.relativeXPosition] = dummyNode;\n        child.oldParentNode = parent;\n        child.relativeXPosition = 0;\n        // Then connection from child to dummyNode.\n        child.parentNode = dummyNode;\n        child.parent = dummyNode.id;\n        return dummyNode;\n    }\n    /**\n     * Walker algorithm of positioning the nodes in the treegraph improved by\n     * Buchheim to run in the linear time. Basic algorithm consists of post\n     * order traversal, which starts from going bottom up (first walk), and then\n     * pre order traversal top to bottom (second walk) where adding all of the\n     * modifiers is performed.\n     * link to the paper: http://dirk.jivas.de/papers/buchheim02improving.pdf\n     *\n     * @param {TreegraphSeries} series the Treegraph series\n     */\n    calculatePositions(series) {\n        const treeLayout = this;\n        const nodes = series.nodeList;\n        this.resetValues(nodes);\n        const root = series.tree;\n        if (root) {\n            treeLayout.calculateRelativeX(root, 0);\n            treeLayout.beforeLayout(nodes);\n            treeLayout.firstWalk(root);\n            treeLayout.secondWalk(root, -root.preX);\n            treeLayout.afterLayout(nodes);\n        }\n    }\n    /**\n     * Create dummyNodes as parents for nodes, which column is changed.\n     *\n     * @param {Array<TreegraphNode>} nodes\n     *        All of the nodes.\n     */\n    beforeLayout(nodes) {\n        for (const node of nodes) {\n            for (let child of node.children) {\n                // Support for children placed in distant columns.\n                if (child && child.level - node.level > 1) {\n                    // For further columns treat the nodes as a\n                    // single parent-child pairs till the column is achieved.\n                    let gapSize = child.level - node.level - 1;\n                    // Parent -> dummyNode -> child\n                    while (gapSize > 0) {\n                        child = TreegraphLayout.createDummyNode(node, child, gapSize);\n                        gapSize--;\n                    }\n                }\n            }\n        }\n    }\n    /**\n     * Reset the calculated values from the previous run.\n     * @param {TreegraphNode[]} nodes all of the nodes.\n     */\n    resetValues(nodes) {\n        for (const node of nodes) {\n            node.mod = 0;\n            node.ancestor = node;\n            node.shift = 0;\n            node.thread = void 0;\n            node.change = 0;\n            node.preX = 0;\n        }\n    }\n    /**\n     * Assigns the value to each node, which indicates, what is his sibling\n     * number.\n     *\n     * @param {TreegraphNode} node\n     *        Root node\n     * @param {number} index\n     *        Index to which the nodes position should be set\n     */\n    calculateRelativeX(node, index) {\n        const treeLayout = this, children = node.children;\n        for (let i = 0, iEnd = children.length; i < iEnd; ++i) {\n            treeLayout.calculateRelativeX(children[i], i);\n        }\n        node.relativeXPosition = index;\n    }\n    /**\n     * Recursive post order traversal of the tree, where the initial position\n     * of the nodes is calculated.\n     *\n     * @param {TreegraphNode} node\n     *        The node for which the position should be calculated.\n     */\n    firstWalk(node) {\n        const treeLayout = this, \n        // Arbitrary value used to position nodes in respect to each other.\n        siblingDistance = 1;\n        let leftSibling;\n        // If the node is a leaf, set it's position based on the left siblings.\n        if (!node.hasChildren()) {\n            leftSibling = node.getLeftSibling();\n            if (leftSibling) {\n                node.preX = leftSibling.preX + siblingDistance;\n                node.mod = node.preX;\n            }\n            else {\n                node.preX = 0;\n            }\n        }\n        else {\n            // If the node has children, perform the recursive first walk for\n            // its children, and then calculate its shift in the apportion\n            // function (most crucial part of the algorithm).\n            let defaultAncestor = node.getLeftMostChild();\n            for (const child of node.children) {\n                treeLayout.firstWalk(child);\n                defaultAncestor = treeLayout.apportion(child, defaultAncestor);\n            }\n            treeLayout.executeShifts(node);\n            const leftChild = node.getLeftMostChild(), rightChild = node.getRightMostChild(), \n            // Set the position of the parent as a middle point of its\n            // children and move it by the value of the leftSibling (if it\n            // exists).\n            midPoint = (leftChild.preX + rightChild.preX) / 2;\n            leftSibling = node.getLeftSibling();\n            if (leftSibling) {\n                node.preX = leftSibling.preX + siblingDistance;\n                node.mod = node.preX - midPoint;\n            }\n            else {\n                node.preX = midPoint;\n            }\n        }\n    }\n    /**\n     * Pre order traversal of the tree, which sets the final xPosition of the\n     * node as its preX value and sum of all if it's parents' modifiers.\n     *\n     * @param {TreegraphNode} node\n     *        The node, for which the final position should be calculated.\n     * @param {number} modSum\n     *        The sum of modifiers of all of the parents.\n     */\n    secondWalk(node, modSum) {\n        const treeLayout = this;\n        // When the chart is not inverted we want the tree to be positioned from\n        // left to right with root node close to the chart border, this is why\n        // x and y positions are switched.\n        node.yPosition = node.preX + modSum;\n        node.xPosition = node.level;\n        for (const child of node.children) {\n            treeLayout.secondWalk(child, modSum + node.mod);\n        }\n    }\n    /**\n     *  Shift all children of the current node from right to left.\n     *\n     * @param {TreegraphNode} node\n     *        The parent node.\n     */\n    executeShifts(node) {\n        let shift = 0, change = 0;\n        for (let i = node.children.length - 1; i >= 0; i--) {\n            const childNode = node.children[i];\n            childNode.preX += shift;\n            childNode.mod += shift;\n            change += childNode.change;\n            shift += childNode.shift + change;\n        }\n    }\n    /**\n     * The core of the algorithm. The new subtree is combined with the previous\n     * subtrees. Threads are used to traverse the inside and outside contours of\n     * the left and right subtree up to the highest common level. The vertecies\n     * are left(right)Int(Out)node where Int means internal and Out means\n     * outernal. For summing up the modifiers along the contour we use the\n     * `left(right)Int(Out)mod` variable. Whenever two nodes of the inside\n     * contours are in conflict we commute the left one of the greatest uncommon\n     * ancestors using the getAncestor function and we call the moveSubtree\n     * method to shift the subtree and prepare the shifts of smaller subtrees.\n     * Finally we add a new thread (if necessary) and we adjust ancestor of\n     * right outernal node or defaultAncestor.\n     *\n     * @param {TreegraphNode} node\n     * @param {TreegraphNode} defaultAncestor\n     *        The default ancestor of the passed node.\n     */\n    apportion(node, defaultAncestor) {\n        const treeLayout = this, leftSibling = node.getLeftSibling();\n        if (leftSibling) {\n            let rightIntNode = node, rightOutNode = node, leftIntNode = leftSibling, leftOutNode = rightIntNode.getLeftMostSibling(), rightIntMod = rightIntNode.mod, rightOutMod = rightOutNode.mod, leftIntMod = leftIntNode.mod, leftOutMod = leftOutNode.mod;\n            while (leftIntNode &&\n                leftIntNode.nextRight() &&\n                rightIntNode &&\n                rightIntNode.nextLeft()) {\n                leftIntNode = leftIntNode.nextRight();\n                leftOutNode = leftOutNode.nextLeft();\n                rightIntNode = rightIntNode.nextLeft();\n                rightOutNode = rightOutNode.nextRight();\n                rightOutNode.ancestor = node;\n                const siblingDistance = 1, shift = leftIntNode.preX +\n                    leftIntMod -\n                    (rightIntNode.preX + rightIntMod) +\n                    siblingDistance;\n                if (shift > 0) {\n                    treeLayout.moveSubtree(node.getAncestor(leftIntNode, defaultAncestor), node, shift);\n                    rightIntMod += shift;\n                    rightOutMod += shift;\n                }\n                leftIntMod += leftIntNode.mod;\n                rightIntMod += rightIntNode.mod;\n                leftOutMod += leftOutNode.mod;\n                rightOutMod += rightOutNode.mod;\n            }\n            if (leftIntNode &&\n                leftIntNode.nextRight() &&\n                !rightOutNode.nextRight()) {\n                rightOutNode.thread = leftIntNode.nextRight();\n                rightOutNode.mod += leftIntMod - rightOutMod;\n            }\n            if (rightIntNode &&\n                rightIntNode.nextLeft() &&\n                !leftOutNode.nextLeft()) {\n                leftOutNode.thread = rightIntNode.nextLeft();\n                leftOutNode.mod += rightIntMod - leftOutMod;\n            }\n            defaultAncestor = node;\n        }\n        return defaultAncestor;\n    }\n    /**\n     * Shifts the subtree from leftNode to rightNode.\n     *\n     * @param {TreegraphNode} leftNode\n     * @param {TreegraphNode} rightNode\n     * @param {number} shift\n     *        The value, by which the subtree should be moved.\n     */\n    moveSubtree(leftNode, rightNode, shift) {\n        const subtrees = rightNode.relativeXPosition - leftNode.relativeXPosition;\n        rightNode.change -= shift / subtrees;\n        rightNode.shift += shift;\n        rightNode.preX += shift;\n        rightNode.mod += shift;\n        leftNode.change += shift / subtrees;\n    }\n    /**\n     * Clear values created in a beforeLayout.\n     *\n     * @param {TreegraphNode[]} nodes\n     *        All of the nodes of the Treegraph Series.\n     */\n    afterLayout(nodes) {\n        for (const node of nodes) {\n            if (node.oldParentNode) {\n                // Restore default connections\n                node.relativeXPosition = node.parentNode.relativeXPosition;\n                node.parent = node.oldParentNode.parent;\n                node.parentNode = node.oldParentNode;\n                // Delete dummyNode\n                delete node.oldParentNode.children[node.relativeXPosition];\n                node.oldParentNode.children[node.relativeXPosition] = node;\n                node.oldParentNode = void 0;\n            }\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treegraph_TreegraphLayout = (TreegraphLayout);\n\n;// ./code/es-modules/Series/Treegraph/TreegraphSeriesDefaults.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy Grzegorz Blachlinski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n/**\n * A treegraph series is a diagram, which shows a relation between ancestors\n * and descendants with a clear parent - child relation.\n * The best examples of the dataStructures, which best reflect this chart\n * are e.g. genealogy tree or directory structure.\n *\n * TODO change back the demo path\n * @sample highcharts/demo/treegraph-chart\n *         Treegraph Chart\n *\n * @extends      plotOptions.treemap\n * @excluding    layoutAlgorithm, dashStyle, linecap, lineWidth,\n *               negativeColor, threshold, zones, zoneAxis, colorAxis,\n *               colorKey, compare, dataGrouping, endAngle, gapSize, gapUnit,\n *               ignoreHiddenPoint, innerSize, joinBy, legendType, linecap,\n *               minSize, navigatorOptions, pointRange, allowTraversingTree,\n *               alternateStartingDirection, borderRadius, breadcrumbs,\n *               interactByLeaf, layoutStartingDirection, levelIsConstant,\n *               lineWidth, negativeColor, nodes, sortIndex, zoneAxis,\n *               zones, cluster\n *\n * @product      highcharts\n * @since 10.3.0\n * @requires     modules/treemap\n * @requires     modules/treegraph\n * @optionparent plotOptions.treegraph\n */\nconst TreegraphSeriesDefaults = {\n    /**\n     * Flips the positions of the nodes of a treegraph along the\n     * horizontal axis (vertical if chart is inverted).\n     *\n     * @sample highcharts/series-treegraph/reversed-nodes\n     *         Treegraph series with reversed nodes.\n     *\n     * @type    {boolean}\n     * @default false\n     * @product highcharts\n     * @since 10.3.0\n     */\n    reversed: false,\n    /**\n     * @extends   plotOptions.series.marker\n     * @excluding enabled, enabledThreshold\n     */\n    marker: {\n        radius: 10,\n        lineWidth: 0,\n        symbol: 'circle',\n        fillOpacity: 1,\n        states: {}\n    },\n    link: {\n        /**\n         * Modifier of the shape of the curved link. Works best for\n         * values between 0 and 1, where 0 is a straight line, and 1 is\n         * a shape close to the default one.\n         *\n         * @type      {number}\n         * @default   0.5\n         * @product   highcharts\n         * @since 10.3.0\n         * @apioption series.treegraph.link.curveFactor\n         */\n        /**\n         * The color of the links between nodes.\n         *\n         * @type {Highcharts.ColorString}\n         * @private\n         */\n        color: \"#666666\" /* Palette.neutralColor60 */,\n        /**\n         * The line width of the links connecting nodes, in pixels.\n         * @type {number}\n         *\n         * @private\n         */\n        lineWidth: 1,\n        /**\n         * Radius for the rounded corners of the links between nodes.\n         * Works for `default` link type.\n         *\n         * @private\n         */\n        radius: 10,\n        cursor: 'default',\n        /**\n         * Type of the link shape.\n         *\n         * @sample   highcharts/series-treegraph/link-types\n         *           Different link types\n         *\n         * @type {'default' | 'curved' | 'straight'}\n         * @product highcharts\n         *\n         */\n        type: 'curved'\n    },\n    /**\n     * Options applied to collapse Button. The collape button is the\n     * small button which indicates, that the node is collapsable.\n     */\n    collapseButton: {\n        /**\n         * Whether the button should be visible only when the node is\n         * hovered. When set to true, the button is hidden for nodes,\n         * which are not collapsed, and shown for the collapsed ones.\n         */\n        onlyOnHover: true,\n        /**\n         * Whether the button should be visible.\n         */\n        enabled: true,\n        /**\n         * The line width of the button in pixels\n         */\n        lineWidth: 1,\n        /**\n         * Offset of the button in the x direction.\n         */\n        x: 0,\n        /**\n         * Offset of the button in the y direction.\n         */\n        y: 0,\n        /**\n         * Height of the button.\n         */\n        height: 18,\n        /**\n         * Width of the button.\n         */\n        width: 18,\n        /**\n         * The symbol of the collapse button.\n         */\n        shape: 'circle',\n        /**\n         * CSS styles for the collapse button.\n         *\n         * In styled mode, the collapse button style is given in the\n         * `.highcharts-collapse-button` class.\n         */\n        style: {\n            cursor: 'pointer',\n            fontWeight: 'bold',\n            fontSize: '1em'\n        }\n    },\n    /**\n     * Whether the treegraph series should fill the entire plot area in the X\n     * axis direction, even when there are collapsed points.\n     *\n     * @sample  highcharts/series-treegraph/fillspace\n     *          Fill space demonstrated\n     *\n     * @product highcharts\n     */\n    fillSpace: false,\n    /**\n     * @extends plotOptions.series.tooltip\n     * @excluding clusterFormat\n     */\n    tooltip: {\n        /**\n         * The HTML of the point's line in the tooltip. Variables are\n         * enclosed by curly brackets. Available variables are\n         * `point.id`,  `point.fromNode.id`, `point.toNode.id`,\n         * `series.name`, `series.color` and other properties on the\n         * same form. Furthermore, This can also be overridden for each\n         * series, which makes it a good hook for displaying units. In\n         * styled mode, the dot is colored by a class name rather than\n         * the point color.\n         *\n         * @type {string}\n         * @since 10.3.0\n         * @product highcharts\n         */\n        linkFormat: '{point.fromNode.id} \\u2192 {point.toNode.id}',\n        pointFormat: '{point.id}'\n        /**\n         * A callback function for formatting the HTML output for a\n         * single link in the tooltip. Like the `linkFormat` string,\n         * but with more flexibility.\n         *\n         * @type {Highcharts.FormatterCallbackFunction.<Highcharts.Point>}\n         * @apioption series.treegraph.tooltip.linkFormatter\n         *\n         */\n    },\n    /**\n     * Options for the data labels appearing on top of the nodes and\n     * links. For treegraph charts, data labels are visible for the\n     * nodes by default, but hidden for links. This is controlled by\n     * modifying the `nodeFormat`, and the `format` that applies to\n     * links and is an empty string by default.\n     *\n     * @declare Highcharts.SeriesTreegraphDataLabelsOptionsObject\n     */\n    dataLabels: {\n        defer: true,\n        /**\n         * Options for a _link_ label text which should follow link\n         * connection. Border and background are disabled for a label\n         * that follows a path.\n         *\n         * **Note:** Only SVG-based renderer supports this option.\n         * Setting `useHTML` to true will disable this option.\n         *\n         * @sample highcharts/series-treegraph/link-text-path\n         *         Treegraph series with link text path dataLabels.\n         *\n         * @extends plotOptions.treegraph.dataLabels.textPath\n         * @since 10.3.0\n         */\n        linkTextPath: {\n            attributes: {\n                startOffset: '50%'\n            }\n        },\n        enabled: true,\n        linkFormatter: () => '',\n        padding: 5,\n        style: {\n            textOverflow: 'none'\n        }\n    },\n    /**\n     * The distance between nodes in a tree graph in the longitudinal direction.\n     * The longitudinal direction means the direction that the chart flows - in\n     * a horizontal chart the distance is horizontal, in an inverted chart\n     * (vertical), the distance is vertical.\n     *\n     * If a number is given, it denotes pixels. If a percentage string is given,\n     * the distance is a percentage of the rendered node width. A `nodeDistance`\n     * of `100%` will render equal widths for the nodes and the gaps between\n     * them.\n     *\n     * This option applies only when the `nodeWidth` option is `auto`, making\n     * the node width respond to the number of columns.\n     *\n     * @since 11.4.0\n     * @sample highcharts/series-treegraph/node-distance\n     *         Node distance of 100% means equal to node width\n     * @type   {number|string}\n     */\n    nodeDistance: 30,\n    /**\n     * The pixel width of each node in a, or the height in case the chart is\n     * inverted. For tree graphs, the node width is only applied if the marker\n     * symbol is `rect`, otherwise the `marker` sizing options apply.\n     *\n     * Can be a number or a percentage string, or `auto`. If `auto`, the nodes\n     * are sized to fill up the plot area in the longitudinal direction,\n     * regardless of the number of levels.\n     *\n     * @since 11.4.0\n     * @see    [treegraph.nodeDistance](#nodeDistance)\n     * @sample highcharts/series-treegraph/node-distance\n     *         Node width is auto and combined with node distance\n     *\n     * @type {number|string}\n     */\n    nodeWidth: void 0\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treegraph_TreegraphSeriesDefaults = (TreegraphSeriesDefaults);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SVGElement\"],\"commonjs\":[\"highcharts\",\"SVGElement\"],\"commonjs2\":[\"highcharts\",\"SVGElement\"],\"root\":[\"Highcharts\",\"SVGElement\"]}\nvar highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_ = __webpack_require__(28);\nvar highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default = /*#__PURE__*/__webpack_require__.n(highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_);\n;// ./code/es-modules/Extensions/TextPath.js\n/* *\n *\n *  Highcharts module with textPath functionality.\n *\n *  (c) 2009-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { deg2rad } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nconst { addEvent: TextPath_addEvent, merge: TextPath_merge, uniqueKey, defined, extend: TextPath_extend } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/**\n * Set a text path for a `text` or `label` element, allowing the text to\n * flow along a path.\n *\n * In order to unset the path for an existing element, call `setTextPath`\n * with `{ enabled: false }` as the second argument.\n *\n * Text path support is not bundled into `highcharts.js`, and requires the\n * `modules/textpath.js` file. However, it is included in the script files of\n * those series types that use it by default\n *\n * @sample highcharts/members/renderer-textpath/ Text path demonstrated\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {Highcharts.SVGElement|undefined} path\n *        Path to follow. If undefined, it allows changing options for the\n *        existing path.\n *\n * @param {Highcharts.DataLabelsTextPathOptionsObject} textPathOptions\n *        Options.\n *\n * @return {Highcharts.SVGElement} Returns the SVGElement for chaining.\n */\nfunction setTextPath(path, textPathOptions) {\n    // Defaults\n    textPathOptions = TextPath_merge(true, {\n        enabled: true,\n        attributes: {\n            dy: -5,\n            startOffset: '50%',\n            textAnchor: 'middle'\n        }\n    }, textPathOptions);\n    const url = this.renderer.url, textWrapper = this.text || this, textPath = textWrapper.textPath, { attributes, enabled } = textPathOptions;\n    path = path || (textPath && textPath.path);\n    // Remove previously added event\n    if (textPath) {\n        textPath.undo();\n    }\n    if (path && enabled) {\n        const undo = TextPath_addEvent(textWrapper, 'afterModifyTree', (e) => {\n            if (path && enabled) {\n                // Set ID for the path\n                let textPathId = path.attr('id');\n                if (!textPathId) {\n                    path.attr('id', textPathId = uniqueKey());\n                }\n                // Set attributes for the <text>\n                const textAttribs = {\n                    // `dx`/`dy` options must by set on <text> (parent), the\n                    // rest should be set on <textPath>\n                    x: 0,\n                    y: 0\n                };\n                if (defined(attributes.dx)) {\n                    textAttribs.dx = attributes.dx;\n                    delete attributes.dx;\n                }\n                if (defined(attributes.dy)) {\n                    textAttribs.dy = attributes.dy;\n                    delete attributes.dy;\n                }\n                textWrapper.attr(textAttribs);\n                // Handle label properties\n                this.attr({ transform: '' });\n                if (this.box) {\n                    this.box = this.box.destroy();\n                }\n                // Wrap the nodes in a textPath\n                const children = e.nodes.slice(0);\n                e.nodes.length = 0;\n                e.nodes[0] = {\n                    tagName: 'textPath',\n                    attributes: TextPath_extend(attributes, {\n                        'text-anchor': attributes.textAnchor,\n                        href: `${url}#${textPathId}`\n                    }),\n                    children\n                };\n            }\n        });\n        // Set the reference\n        textWrapper.textPath = { path, undo };\n    }\n    else {\n        textWrapper.attr({ dx: 0, dy: 0 });\n        delete textWrapper.textPath;\n    }\n    if (this.added) {\n        // Rebuild text after added\n        textWrapper.textCache = '';\n        this.renderer.buildText(textWrapper);\n    }\n    return this;\n}\n/**\n * Attach a polygon to a bounding box if the element contains a textPath.\n *\n * @function Highcharts.SVGElement#setPolygon\n *\n * @param {any} event\n *        An event containing a bounding box object\n *\n * @return {Highcharts.BBoxObject} Returns the bounding box object.\n */\nfunction setPolygon(event) {\n    const bBox = event.bBox, tp = this.element?.querySelector('textPath');\n    if (tp) {\n        const polygon = [], { b, h } = this.renderer.fontMetrics(this.element), descender = h - b, lineCleanerRegex = new RegExp('(<tspan>|' +\n            '<tspan(?!\\\\sclass=\"highcharts-br\")[^>]*>|' +\n            '<\\\\/tspan>)', 'g'), lines = tp\n            .innerHTML\n            .replace(lineCleanerRegex, '')\n            .split(/<tspan class=\"highcharts-br\"[^>]*>/), numOfLines = lines.length;\n        // Calculate top and bottom coordinates for\n        // either the start or the end of a single\n        // character, and append it to the polygon.\n        const appendTopAndBottom = (charIndex, positionOfChar) => {\n            const { x, y } = positionOfChar, rotation = (tp.getRotationOfChar(charIndex) - 90) * deg2rad, cosRot = Math.cos(rotation), sinRot = Math.sin(rotation);\n            return [\n                [\n                    x - descender * cosRot,\n                    y - descender * sinRot\n                ],\n                [\n                    x + b * cosRot,\n                    y + b * sinRot\n                ]\n            ];\n        };\n        for (let i = 0, lineIndex = 0; lineIndex < numOfLines; lineIndex++) {\n            const line = lines[lineIndex], lineLen = line.length;\n            for (let lineCharIndex = 0; lineCharIndex < lineLen; lineCharIndex += 5) {\n                try {\n                    const srcCharIndex = (i +\n                        lineCharIndex +\n                        lineIndex), [lower, upper] = appendTopAndBottom(srcCharIndex, tp.getStartPositionOfChar(srcCharIndex));\n                    if (lineCharIndex === 0) {\n                        polygon.push(upper);\n                        polygon.push(lower);\n                    }\n                    else {\n                        if (lineIndex === 0) {\n                            polygon.unshift(upper);\n                        }\n                        if (lineIndex === numOfLines - 1) {\n                            polygon.push(lower);\n                        }\n                    }\n                }\n                catch (e) {\n                    // Safari fails on getStartPositionOfChar even if the\n                    // character is within the `textContent.length`\n                    break;\n                }\n            }\n            i += lineLen - 1;\n            try {\n                const srcCharIndex = i + lineIndex, charPos = tp.getEndPositionOfChar(srcCharIndex), [lower, upper] = appendTopAndBottom(srcCharIndex, charPos);\n                polygon.unshift(upper);\n                polygon.unshift(lower);\n            }\n            catch (e) {\n                // Safari fails on getStartPositionOfChar even if the character\n                // is within the `textContent.length`\n                break;\n            }\n        }\n        // Close it\n        if (polygon.length) {\n            polygon.push(polygon[0].slice());\n        }\n        bBox.polygon = polygon;\n    }\n    return bBox;\n}\n/**\n * Draw text along a textPath for a dataLabel.\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {any} event\n *        An event containing label options\n *\n * @return {void}\n */\nfunction drawTextPath(event) {\n    const labelOptions = event.labelOptions, point = event.point, textPathOptions = (labelOptions[point.formatPrefix + 'TextPath'] ||\n        labelOptions.textPath);\n    if (textPathOptions && !labelOptions.useHTML) {\n        this.setTextPath(point.getDataLabelPath?.(this) || point.graphic, textPathOptions);\n        if (point.dataLabelPath &&\n            !textPathOptions.enabled) {\n            // Clean the DOM\n            point.dataLabelPath = (point.dataLabelPath.destroy());\n        }\n    }\n}\nfunction compose(SVGElementClass) {\n    TextPath_addEvent(SVGElementClass, 'afterGetBBox', setPolygon);\n    TextPath_addEvent(SVGElementClass, 'beforeAddingDataLabel', drawTextPath);\n    const svgElementProto = SVGElementClass.prototype;\n    if (!svgElementProto.setTextPath) {\n        svgElementProto.setTextPath = setTextPath;\n    }\n}\nconst TextPath = {\n    compose\n};\n/* harmony default export */ const Extensions_TextPath = (TextPath);\n\n;// ./code/es-modules/Series/Treegraph/TreegraphSeries.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy Grzegorz Blachlinski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { getLinkPath: TreegraphSeries_getLinkPath } = Series_PathUtilities;\n\nconst { series: { prototype: seriesProto }, seriesTypes: { treemap: TreemapSeries, column: ColumnSeries } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { prototype: { symbols } } = (highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default());\n\n\n\nconst { getLevelOptions: TreegraphSeries_getLevelOptions, getNodeWidth: TreegraphSeries_getNodeWidth } = Series_TreeUtilities;\n\nconst { arrayMax, crisp, extend: TreegraphSeries_extend, merge: TreegraphSeries_merge, pick: TreegraphSeries_pick, relativeLength: TreegraphSeries_relativeLength, splat } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\n\n\n\nExtensions_TextPath.compose((highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default()));\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Treegraph series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.treegraph\n *\n * @augments Highcharts.Series\n */\nclass TreegraphSeries extends TreemapSeries {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        this.nodeList = [];\n        this.links = [];\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    init() {\n        super.init.apply(this, arguments);\n        this.layoutAlgorythm = new Treegraph_TreegraphLayout();\n        // Register the link data labels in the label collector for overlap\n        // detection.\n        const series = this, collectors = this.chart.labelCollectors, collectorFunc = function () {\n            const linkLabels = [];\n            // Check links for overlap\n            if (series.options.dataLabels &&\n                !splat(series.options.dataLabels)[0].allowOverlap) {\n                for (const link of (series.links || [])) {\n                    if (link.dataLabel) {\n                        linkLabels.push(link.dataLabel);\n                    }\n                }\n            }\n            return linkLabels;\n        };\n        // Only add the collector function if it is not present\n        if (!collectors.some((f) => f.name === 'collectorFunc')) {\n            collectors.push(collectorFunc);\n        }\n    }\n    /**\n     * Calculate `a` and `b` parameters of linear transformation, where\n     * `finalPosition = a * calculatedPosition + b`.\n     *\n     * @return {LayoutModifiers} `a` and `b` parameter for x and y direction.\n     */\n    getLayoutModifiers() {\n        const chart = this.chart, series = this, plotSizeX = chart.plotSizeX, plotSizeY = chart.plotSizeY, columnCount = arrayMax(this.points.map((p) => p.node.xPosition));\n        let minX = Infinity, maxX = -Infinity, minY = Infinity, maxY = -Infinity, maxXSize = 0, minXSize = 0, maxYSize = 0, minYSize = 0;\n        this.points.forEach((point) => {\n            // When fillSpace is on, stop the layout calculation when the hidden\n            // points are reached. (#19038)\n            if (this.options.fillSpace && !point.visible) {\n                return;\n            }\n            const node = point.node, level = series.mapOptionsToLevel[point.node.level] || {}, markerOptions = TreegraphSeries_merge(this.options.marker, level.marker, point.options.marker), nodeWidth = markerOptions.width ?? TreegraphSeries_getNodeWidth(this, columnCount), radius = TreegraphSeries_relativeLength(markerOptions.radius || 0, Math.min(plotSizeX, plotSizeY)), symbol = markerOptions.symbol, nodeSizeY = (symbol === 'circle' || !markerOptions.height) ?\n                radius * 2 :\n                TreegraphSeries_relativeLength(markerOptions.height, plotSizeY), nodeSizeX = symbol === 'circle' || !nodeWidth ?\n                radius * 2 :\n                TreegraphSeries_relativeLength(nodeWidth, plotSizeX);\n            node.nodeSizeX = nodeSizeX;\n            node.nodeSizeY = nodeSizeY;\n            let lineWidth;\n            if (node.xPosition <= minX) {\n                minX = node.xPosition;\n                lineWidth = markerOptions.lineWidth || 0;\n                minXSize = Math.max(nodeSizeX + lineWidth, minXSize);\n            }\n            if (node.xPosition >= maxX) {\n                maxX = node.xPosition;\n                lineWidth = markerOptions.lineWidth || 0;\n                maxXSize = Math.max(nodeSizeX + lineWidth, maxXSize);\n            }\n            if (node.yPosition <= minY) {\n                minY = node.yPosition;\n                lineWidth = markerOptions.lineWidth || 0;\n                minYSize = Math.max(nodeSizeY + lineWidth, minYSize);\n            }\n            if (node.yPosition >= maxY) {\n                maxY = node.yPosition;\n                lineWidth = markerOptions.lineWidth || 0;\n                maxYSize = Math.max(nodeSizeY + lineWidth, maxYSize);\n            }\n        });\n        // Calculate the values of linear transformation, which will later be\n        // applied as `nodePosition = a * x + b` for each direction.\n        const ay = maxY === minY ?\n            1 :\n            (plotSizeY - (minYSize + maxYSize) / 2) / (maxY - minY), by = maxY === minY ? plotSizeY / 2 : -ay * minY + minYSize / 2, ax = maxX === minX ?\n            1 :\n            (plotSizeX - (maxXSize + maxXSize) / 2) / (maxX - minX), bx = maxX === minX ? plotSizeX / 2 : -ax * minX + minXSize / 2;\n        return { ax, bx, ay, by };\n    }\n    getLinks() {\n        const series = this;\n        const links = [];\n        this.data.forEach((point) => {\n            const levelOptions = series.mapOptionsToLevel[point.node.level || 0] || {};\n            if (point.node.parent) {\n                const pointOptions = TreegraphSeries_merge(levelOptions, point.options);\n                if (!point.linkToParent || point.linkToParent.destroyed) {\n                    const link = new series.LinkClass(series, pointOptions, void 0, point);\n                    point.linkToParent = link;\n                }\n                else {\n                    // #19552\n                    point.collapsed = TreegraphSeries_pick(point.collapsed, (this.mapOptionsToLevel[point.node.level] || {}).collapsed);\n                    point.linkToParent.visible =\n                        point.linkToParent.toNode.visible;\n                }\n                point.linkToParent.index = links.push(point.linkToParent) - 1;\n            }\n            else {\n                if (point.linkToParent) {\n                    series.links.splice(point.linkToParent.index);\n                    point.linkToParent.destroy();\n                    delete point.linkToParent;\n                }\n            }\n        });\n        return links;\n    }\n    buildTree(id, index, level, list, parent) {\n        const point = this.points[index];\n        level = (point && point.level) || level;\n        return super.buildTree.call(this, id, index, level, list, parent);\n    }\n    markerAttribs() {\n        // The super Series.markerAttribs returns { width: NaN, height: NaN },\n        // so just disable this for now.\n        return {};\n    }\n    setCollapsedStatus(node, visibility) {\n        const point = node.point;\n        if (point) {\n            // Take the level options into account.\n            point.collapsed = TreegraphSeries_pick(point.collapsed, (this.mapOptionsToLevel[node.level] || {}).collapsed);\n            point.visible = visibility;\n            visibility = visibility === false ? false : !point.collapsed;\n        }\n        node.children.forEach((childNode) => {\n            this.setCollapsedStatus(childNode, visibility);\n        });\n    }\n    drawTracker() {\n        ColumnSeries.prototype.drawTracker.apply(this, arguments);\n        ColumnSeries.prototype.drawTracker.call(this, this.links);\n    }\n    /**\n     * Run pre-translation by generating the nodeColumns.\n     * @private\n     */\n    translate() {\n        const series = this, options = series.options;\n        // NOTE: updateRootId modifies series.\n        let rootId = Series_TreeUtilities.updateRootId(series), rootNode;\n        // Call prototype function\n        seriesProto.translate.call(series);\n        const tree = series.tree = series.getTree();\n        rootNode = series.nodeMap[rootId];\n        if (rootId !== '' && (!rootNode || !rootNode.children.length)) {\n            series.setRootNode('', false);\n            rootId = series.rootNode;\n            rootNode = series.nodeMap[rootId];\n        }\n        series.mapOptionsToLevel = TreegraphSeries_getLevelOptions({\n            from: rootNode.level + 1,\n            levels: options.levels,\n            to: tree.height,\n            defaults: {\n                levelIsConstant: series.options.levelIsConstant,\n                colorByPoint: options.colorByPoint\n            }\n        });\n        this.setCollapsedStatus(tree, true);\n        series.links = series.getLinks();\n        series.setTreeValues(tree);\n        this.layoutAlgorythm.calculatePositions(series);\n        series.layoutModifier = this.getLayoutModifiers();\n        this.points.forEach((point) => {\n            this.translateNode(point);\n        });\n        this.points.forEach((point) => {\n            if (point.linkToParent) {\n                this.translateLink(point.linkToParent);\n            }\n        });\n        if (!options.colorByPoint) {\n            series.setColorRecursive(series.tree);\n        }\n    }\n    translateLink(link) {\n        const fromNode = link.fromNode, toNode = link.toNode, linkWidth = this.options.link?.lineWidth || 0, factor = TreegraphSeries_pick(this.options.link?.curveFactor, 0.5), type = TreegraphSeries_pick(link.options.link?.type, this.options.link?.type, 'default');\n        if (fromNode.shapeArgs && toNode.shapeArgs) {\n            const fromNodeWidth = (fromNode.shapeArgs.width || 0), inverted = this.chart.inverted, y1 = crisp((fromNode.shapeArgs.y || 0) +\n                (fromNode.shapeArgs.height || 0) / 2, linkWidth), y2 = crisp((toNode.shapeArgs.y || 0) +\n                (toNode.shapeArgs.height || 0) / 2, linkWidth);\n            let x1 = crisp((fromNode.shapeArgs.x || 0) + fromNodeWidth, linkWidth), x2 = crisp(toNode.shapeArgs.x || 0, linkWidth);\n            if (inverted) {\n                x1 -= fromNodeWidth;\n                x2 += (toNode.shapeArgs.width || 0);\n            }\n            const diff = toNode.node.xPosition - fromNode.node.xPosition;\n            link.shapeType = 'path';\n            const fullWidth = Math.abs(x2 - x1) + fromNodeWidth, width = (fullWidth / diff) - fromNodeWidth, offset = width * factor * (inverted ? -1 : 1);\n            const xMiddle = crisp((x2 + x1) / 2, linkWidth);\n            link.plotX = xMiddle;\n            link.plotY = y2;\n            link.shapeArgs = {\n                d: TreegraphSeries_getLinkPath[type]({\n                    x1,\n                    y1,\n                    x2,\n                    y2,\n                    width,\n                    offset,\n                    inverted,\n                    parentVisible: toNode.visible,\n                    radius: this.options.link?.radius\n                })\n            };\n            link.dlBox = {\n                x: (x1 + x2) / 2,\n                y: (y1 + y2) / 2,\n                height: linkWidth,\n                width: 0\n            };\n            link.tooltipPos = inverted ? [\n                (this.chart.plotSizeY || 0) - link.dlBox.y,\n                (this.chart.plotSizeX || 0) - link.dlBox.x\n            ] : [\n                link.dlBox.x,\n                link.dlBox.y\n            ];\n        }\n    }\n    /**\n     * Private method responsible for adjusting the dataLabel options for each\n     * node-point individually.\n     */\n    drawNodeLabels(points) {\n        const series = this, mapOptionsToLevel = series.mapOptionsToLevel;\n        let options, level;\n        for (const point of points) {\n            level = mapOptionsToLevel[point.node.level];\n            // Set options to new object to avoid problems with scope\n            options = { style: {} };\n            // If options for level exists, include them as well\n            if (level && level.dataLabels) {\n                options = TreegraphSeries_merge(options, level.dataLabels);\n                series.hasDataLabels = () => true;\n            }\n            // Set dataLabel width to the width of the point shape.\n            if (point.shapeArgs &&\n                series.options.dataLabels) {\n                const css = {};\n                let { width = 0, height = 0 } = point.shapeArgs;\n                if (series.chart.inverted) {\n                    [width, height] = [height, width];\n                }\n                if (!splat(series.options.dataLabels)[0].style?.width) {\n                    css.width = `${width}px`;\n                }\n                if (!splat(series.options.dataLabels)[0].style?.lineClamp) {\n                    css.lineClamp = Math.floor(height / 16);\n                }\n                TreegraphSeries_extend(options.style, css);\n                point.dataLabel?.css(css);\n            }\n            // Merge custom options with point options\n            point.dlOptions = TreegraphSeries_merge(options, point.options.dataLabels);\n        }\n        seriesProto.drawDataLabels.call(this, points);\n    }\n    /**\n     * Override alignDataLabel so that position is always calculated and the\n     * label is faded in and out instead of hidden/shown when collapsing and\n     * expanding nodes.\n     */\n    alignDataLabel(point, dataLabel) {\n        const visible = point.visible;\n        // Force position calculation and visibility\n        point.visible = true;\n        super.alignDataLabel.apply(this, arguments);\n        // Fade in or out\n        dataLabel.animate({\n            opacity: visible === false ? 0 : 1\n        }, void 0, function () {\n            // Hide data labels that belong to hidden points (#18891)\n            visible || dataLabel.hide();\n        });\n        // Reset\n        point.visible = visible;\n    }\n    /**\n     * Treegraph has two separate collecions of nodes and lines,\n     * render dataLabels for both sets.\n     */\n    drawDataLabels() {\n        if (this.options.dataLabels) {\n            this.options.dataLabels = splat(this.options.dataLabels);\n            // Render node labels.\n            this.drawNodeLabels(this.points);\n            // Render link labels.\n            seriesProto.drawDataLabels.call(this, this.links);\n        }\n    }\n    destroy() {\n        // Links must also be destroyed.\n        if (this.links) {\n            for (const link of this.links) {\n                link.destroy();\n            }\n            this.links.length = 0;\n        }\n        return seriesProto.destroy.apply(this, arguments);\n    }\n    /**\n     * Return the presentational attributes.\n     * @private\n     */\n    pointAttribs(point, state) {\n        const series = this, levelOptions = point &&\n            series.mapOptionsToLevel[point.node.level || 0] || {}, options = point && point.options, stateOptions = (levelOptions.states &&\n            levelOptions.states[state]) ||\n            {};\n        if (point) {\n            point.options.marker = TreegraphSeries_merge(series.options.marker, levelOptions.marker, point.options.marker);\n        }\n        const linkColor = TreegraphSeries_pick(stateOptions && stateOptions.link && stateOptions.link.color, options && options.link && options.link.color, levelOptions && levelOptions.link && levelOptions.link.color, series.options.link && series.options.link.color), linkLineWidth = TreegraphSeries_pick(stateOptions && stateOptions.link &&\n            stateOptions.link.lineWidth, options && options.link && options.link.lineWidth, levelOptions && levelOptions.link &&\n            levelOptions.link.lineWidth, series.options.link && series.options.link.lineWidth), attribs = seriesProto.pointAttribs.call(series, point, state);\n        if (point) {\n            if (point.isLink) {\n                attribs.stroke = linkColor;\n                attribs['stroke-width'] = linkLineWidth;\n                delete attribs.fill;\n            }\n            if (!point.visible) {\n                attribs.opacity = 0;\n            }\n        }\n        return attribs;\n    }\n    drawPoints() {\n        TreemapSeries.prototype.drawPoints.apply(this, arguments);\n        ColumnSeries.prototype.drawPoints.call(this, this.links);\n    }\n    /**\n     * Run translation operations for one node.\n     * @private\n     */\n    translateNode(point) {\n        const chart = this.chart, node = point.node, plotSizeY = chart.plotSizeY, plotSizeX = chart.plotSizeX, \n        // Get the layout modifiers which are common for all nodes.\n        { ax, bx, ay, by } = this.layoutModifier, x = ax * node.xPosition + bx, y = ay * node.yPosition + by, level = this.mapOptionsToLevel[node.level] || {}, markerOptions = TreegraphSeries_merge(this.options.marker, level.marker, point.options.marker), symbol = markerOptions.symbol, height = node.nodeSizeY, width = node.nodeSizeX, reversed = this.options.reversed, nodeX = node.x = (chart.inverted ?\n            plotSizeX - width / 2 - x :\n            x - width / 2), nodeY = node.y = (!reversed ?\n            plotSizeY - y - height / 2 :\n            y - height / 2), borderRadius = TreegraphSeries_pick(point.options.borderRadius, level.borderRadius, this.options.borderRadius), symbolFn = symbols[symbol || 'circle'];\n        if (symbolFn === void 0) {\n            point.hasImage = true;\n            point.shapeType = 'image';\n            point.imageUrl = symbol.match(/^url\\((.*?)\\)$/)[1];\n        }\n        else {\n            point.shapeType = 'path';\n        }\n        if (!point.visible && point.linkToParent) {\n            const parentNode = point.linkToParent.fromNode;\n            if (parentNode) {\n                const parentShapeArgs = parentNode.shapeArgs || {}, { x = 0, y = 0, width = 0, height = 0 } = parentShapeArgs;\n                if (!point.shapeArgs) {\n                    point.shapeArgs = {};\n                }\n                if (!point.hasImage) {\n                    TreegraphSeries_extend(point.shapeArgs, {\n                        d: symbolFn(x, y, width, height, borderRadius ? { r: borderRadius } : void 0)\n                    });\n                }\n                TreegraphSeries_extend(point.shapeArgs, { x, y });\n                point.plotX = parentNode.plotX;\n                point.plotY = parentNode.plotY;\n            }\n        }\n        else {\n            point.plotX = nodeX;\n            point.plotY = nodeY;\n            point.shapeArgs = {\n                x: nodeX,\n                y: nodeY,\n                width,\n                height,\n                cursor: !point.node.isLeaf ? 'pointer' : 'default'\n            };\n            if (!point.hasImage) {\n                point.shapeArgs.d = symbolFn(nodeX, nodeY, width, height, borderRadius ? { r: borderRadius } : void 0);\n            }\n        }\n        // Set the anchor position for tooltip.\n        point.tooltipPos = chart.inverted ?\n            [plotSizeY - nodeY - height / 2, plotSizeX - nodeX - width / 2] :\n            [nodeX + width / 2, nodeY];\n    }\n}\nTreegraphSeries.defaultOptions = TreegraphSeries_merge(TreemapSeries.defaultOptions, Treegraph_TreegraphSeriesDefaults);\nTreegraphSeries_extend(TreegraphSeries.prototype, {\n    forceDL: true,\n    pointClass: Treegraph_TreegraphPoint,\n    NodeClass: Treegraph_TreegraphNode,\n    LinkClass: TreegraphLink,\n    isCartesian: false\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('treegraph', TreegraphSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treegraph_TreegraphSeries = ((/* unused pure expression or super */ null && (TreegraphSeries)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `treegraph` series. If the [type](#series.treegraph.type)\n * option is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.treegraph\n * @exclude   allowDrillToNode, boostBlending, boostThreshold, curveFactor,\n * centerInCategory, connectEnds, connectNulls, colorAxis, colorKey,\n * dataSorting, dragDrop, findNearestPointBy, getExtremesFromAll, groupPadding,\n * headers, layout, nodePadding, nodeSizeBy, pointInterval, pointIntervalUnit,\n * pointPlacement, pointStart, relativeXValue, softThreshold, stack, stacking,\n * step, traverseUpButton, xAxis, yAxis, zoneAxis, zones\n * @product   highcharts\n * @requires  modules/treemap\n * @requires  modules/treegraph\n * @apioption series.treegraph\n */\n/**\n * @extends   plotOptions.series.marker\n * @excluding enabled, enabledThreshold\n * @apioption series.treegraph.marker\n */\n/**\n * @type      {Highcharts.SeriesTreegraphDataLabelsOptionsObject|Array<Highcharts.SeriesTreegraphDataLabelsOptionsObject>}\n * @product   highcharts\n * @apioption series.treegraph.data.dataLabels\n */\n/**\n * @sample highcharts/series-treegraph/level-options\n *          Treegraph chart with level options applied\n *\n * @type      {Array<*>}\n * @excluding layoutStartingDirection, layoutAlgorithm\n * @apioption series.treegraph.levels\n */\n/**\n * Set collapsed status for nodes level-wise.\n * @type {boolean}\n * @apioption series.treegraph.levels.collapsed\n */\n/**\n * Set marker options for nodes at the level.\n * @extends   series.treegraph.marker\n * @apioption series.treegraph.levels.marker\n */\n/**\n * An array of data points for the series. For the `treegraph` series type,\n * points can be given in the following ways:\n *\n * 1. The array of arrays, with `keys` property, which defines how the fields in\n *     array should be interpreted\n *    ```js\n *       keys: ['id', 'parent'],\n *       data: [\n *           ['Category1'],\n *           ['Category1', 'Category2']\n *       ]\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the\n *    series' [turboThreshold](#series.area.turboThreshold),\n *    this option is not available.\n *    The data of the treegraph series needs to be formatted in such a way, that\n *    there are no circular dependencies on the nodes\n *\n *  ```js\n *     data: [{\n *         id: 'Category1'\n *     }, {\n *         id: 'Category1',\n *         parent: 'Category2',\n *     }]\n *  ```\n *\n * @type      {Array<*>}\n * @extends   series.treemap.data\n * @product   highcharts\n * @excluding outgoing, weight, value\n * @apioption series.treegraph.data\n */\n/**\n * Options used for button, which toggles the collapse status of the node.\n *\n *\n * @apioption series.treegraph.data.collapseButton\n */\n/**\n * If point's children should be initially hidden\n *\n * @sample highcharts/series-treegraph/level-options\n *          Treegraph chart with initially hidden children\n *\n * @type {boolean}\n * @apioption series.treegraph.data.collapsed\n */\n''; // Gets doclets above into transpiled version\n\n;// ./code/es-modules/masters/modules/treegraph.js\n\n\n\n\n/* harmony default export */ const treegraph_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__540__", "__WEBPACK_EXTERNAL_MODULE__260__", "__WEBPACK_EXTERNAL_MODULE__620__", "__WEBPACK_EXTERNAL_MODULE__28__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "treegraph_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "applyRadius", "path", "r", "i", "length", "x", "y", "push", "prevSeg", "nextSeg", "x1", "y1", "x2", "y2", "directionX", "directionY", "Math", "min", "abs", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_", "highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default", "seriesTypes", "treemap", "NodeClass", "TreemapNode", "Treegraph_TreegraphNode", "constructor", "arguments", "mod", "shift", "change", "children", "preX", "hidden", "wasVisited", "collapsed", "nextLeft", "getLeftMostChild", "thread", "nextRight", "getRightMostChild", "getAncestor", "leftIntNode", "defaultAncestor", "leftAnc", "ancestor", "getLeftMostSibling", "parent", "getParent", "child", "point", "visible", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getLeftSibling", "relativeXPosition", "parentNode", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_", "highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default", "pointClass", "TreemapPoint", "addEvent", "fireEvent", "merge", "TreegraphPoint", "dataLabelOnHidden", "isLink", "setState", "draw", "apply", "graphic", "animate", "visibility", "renderCollapseButton", "series", "parentGroup", "levelOptions", "mapOptionsToLevel", "node", "level", "btnOptions", "options", "collapseButton", "width", "height", "shape", "style", "chart", "calculatedOpacity", "onlyOnHover", "state", "shapeArgs", "collapseButtonOptions", "enabled", "getCollapseBtnPosition", "attr", "text", "rotation", "inverted", "rotationOriginX", "rotationOriginY", "opacity", "destroy", "fill", "fillColor", "color", "renderer", "label", "padding", "stroke", "lineColor", "lineWidth", "align", "zIndex", "addClass", "removeClass", "css", "getContrast", "add", "element", "toggleCollapse", "update", "redraw", "linkToParent", "btnWidth", "btnHeight", "btn", "states", "hover", "animation", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default", "extend", "isArray", "isNumber", "isObject", "TreeUtilities_merge", "pick", "<PERSON><PERSON><PERSON><PERSON>", "Series_TreeUtilities", "getColor", "colorByPoint", "colorIndexByPoint", "colorIndex", "index", "parentColor", "parentColorIndex", "colors", "siblings", "points", "chartOptionsChart", "colorCount", "styledMode", "variateColor", "colorVariation", "parse", "brighten", "to", "getLevelOptions", "params", "defaults", "converted", "from", "levels", "result", "reduce", "item", "levelIsConstant", "getNodeWidth", "columnCount", "nodeDistance", "nodeWidth", "plotSizeX", "test", "fraction", "parseFloat", "nDistance", "Number", "setTreeValues", "tree", "before", "idRoot", "nodeRoot", "mapIdToNode", "optionsPoint", "childrenTotal", "levelDynamic", "name", "id", "for<PERSON>ach", "newOptions", "val", "value", "<PERSON><PERSON><PERSON><PERSON>", "updateRootId", "rootId", "rootNode", "userOptions", "TreegraphLink_pick", "TreegraphLink_extend", "column", "ColumnPoint", "TreegraphLink", "dataLabelOnNull", "formatPrefix", "fromNode", "toNode", "runEvent", "oldOptions", "TreegraphLayout", "createDummyNode", "gapSize", "dummy<PERSON>ode", "oldParentNode", "calculatePositions", "nodes", "nodeList", "resetValues", "treeLayout", "calculateRelativeX", "beforeLayout", "firstWalk", "secondWalk", "afterLayout", "iEnd", "leftSibling", "apportion", "executeShifts", "leftChild", "<PERSON><PERSON><PERSON><PERSON>", "midPoint", "modSum", "yPosition", "xPosition", "childNode", "rightIntNode", "rightOutNode", "leftOutNode", "rightIntMod", "rightOutMod", "leftIntMod", "leftOutMod", "moveSubtree", "leftNode", "rightNode", "subtrees", "highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_", "highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default", "deg2rad", "TextPath_addEvent", "TextPath_merge", "<PERSON><PERSON><PERSON>", "defined", "TextPath_extend", "setTextPath", "textPathOptions", "attributes", "dy", "startOffset", "textAnchor", "url", "textWrapper", "textPath", "undo", "e", "textPathId", "textAttribs", "dx", "transform", "box", "slice", "tagName", "href", "added", "textCache", "buildText", "setPolygon", "event", "bBox", "tp", "querySelector", "polygon", "b", "h", "fontMetrics", "descender", "lineCleanerRegex", "RegExp", "lines", "innerHTML", "replace", "split", "numOfLines", "appendTopAndBottom", "charIndex", "positionOfChar", "getRotationOfChar", "cosRot", "cos", "sinRot", "sin", "lineIndex", "lineLen", "line", "lineCharIndex", "srcCharIndex", "lower", "upper", "getStartPositionOfChar", "unshift", "char<PERSON><PERSON>", "getEndPositionOfChar", "drawTextPath", "labelOptions", "useHTML", "getDataLabelPath", "dataLabelPath", "getLinkPath", "TreegraphSeries_getLinkPath", "pathParams", "radius", "parentVisible", "straight", "curved", "offset", "seriesProto", "TreemapSeries", "ColumnSeries", "symbols", "TreegraphSeries_getLevelOptions", "TreegraphSeries_getNodeWidth", "arrayMax", "crisp", "TreegraphSeries_extend", "TreegraphSeries_merge", "TreegraphSeries_pick", "TreegraphSeries_relativeLength", "splat", "Extensions_TextPath", "compose", "SVGElementClass", "svgElementProto", "TreegraphSeries", "links", "init", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "collectors", "labelCollectors", "some", "f", "linkLabels", "dataLabels", "allowOverlap", "link", "dataLabel", "getLayoutModifiers", "plotSizeY", "map", "p", "minX", "Infinity", "maxX", "minY", "maxY", "maxXSize", "minXSize", "maxYSize", "minYSize", "fillSpace", "markerOptions", "marker", "symbol", "nodeSizeY", "nodeSizeX", "max", "ay", "by", "ax", "bx", "getLinks", "data", "pointOptions", "destroyed", "LinkClass", "splice", "buildTree", "list", "markerAttribs", "setCollapsedStatus", "drawTracker", "translate", "getTree", "nodeMap", "setRootNode", "layoutModifier", "translateNode", "translateLink", "setColorRecursive", "linkWidth", "factor", "curveFactor", "type", "fromNodeWidth", "diff", "shapeType", "plotX", "plotY", "dlBox", "tooltipPos", "drawNodeLabels", "hasDataLabels", "lineClamp", "floor", "dlOptions", "drawDataLabels", "alignDataLabel", "hide", "pointAttribs", "stateOptions", "linkColor", "linkLineWidth", "attribs", "drawPoints", "reversed", "nodeX", "nodeY", "borderRadius", "symbolFn", "hasImage", "imageUrl", "match", "cursor", "defaultOptions", "fillOpacity", "fontWeight", "fontSize", "tooltip", "linkFormat", "pointFormat", "defer", "linkTextPath", "linkFormatter", "textOverflow", "forceDL", "isCartesian", "registerSeriesType"], "mappings": "CAWA,AAXA;;;;;;;;;;CAUC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,WAAc,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,UAAa,EAC/M,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,+BAAgC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAACA,EAAK,WAAc,CAACA,EAAK,KAAQ,CAACA,EAAK,KAAQ,CAACA,EAAK,UAAa,CAAE,GAC3L,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,+BAA+B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,WAAc,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,UAAa,EAE/OA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,WAAc,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,UAAa,CACtN,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,IAC7M,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,GACC,AAACX,IAERA,EAAOD,OAAO,CAAGW,CAEX,EAEA,IACC,AAACV,IAERA,EAAOD,OAAO,CAAGS,CAEX,EAEA,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGU,CAEX,EAEA,IACC,AAACT,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIO,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAahB,OAAO,CAG5B,IAAIC,EAASY,CAAwB,CAACE,EAAS,CAAG,CAGjDf,QAAS,CAAC,CACX,EAMA,OAHAY,CAAmB,CAACG,EAAS,CAACd,EAAQA,EAAOD,OAAO,CAAEc,GAG/Cb,EAAOD,OAAO,AACtB,CAMCc,EAAoBI,CAAC,CAAG,AAACjB,IACxB,IAAIkB,EAASlB,GAAUA,EAAOmB,UAAU,CACvC,IAAOnB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAa,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACrB,EAASuB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACzB,EAASwB,IAC5EE,OAAOC,cAAc,CAAC3B,EAASwB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,EAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAmFrH,SAASE,EAAYC,CAAI,CAAEC,CAAC,EACxB,IAAMpB,EAAI,EAAE,CACZ,IAAK,IAAIqB,EAAI,EAAGA,EAAIF,EAAKG,MAAM,CAAED,IAAK,CAClC,IAAME,EAAIJ,CAAI,CAACE,EAAE,CAAC,EAAE,CACdG,EAAIL,CAAI,CAACE,EAAE,CAAC,EAAE,CACpB,GAAI,AAAa,UAAb,OAAOE,GAAkB,AAAa,UAAb,OAAOC,EAEhC,GAAIH,AAAM,IAANA,EACArB,EAAEyB,IAAI,CAAC,CAAC,IAAKF,EAAGC,EAAE,OAEjB,GAAIH,IAAMF,EAAKG,MAAM,CAAG,EACzBtB,EAAEyB,IAAI,CAAC,CAAC,IAAKF,EAAGC,EAAE,OAGjB,GAAIJ,EAAG,CACR,IAAMM,EAAUP,CAAI,CAACE,EAAI,EAAE,CACrBM,EAAUR,CAAI,CAACE,EAAI,EAAE,CAC3B,GAAIK,GAAWC,EAAS,CACpB,IAAMC,EAAKF,CAAO,CAAC,EAAE,CAAEG,EAAKH,CAAO,CAAC,EAAE,CAAEI,EAAKH,CAAO,CAAC,EAAE,CAAEI,EAAKJ,CAAO,CAAC,EAAE,CAExE,GAAI,AAAc,UAAd,OAAOC,GACP,AAAc,UAAd,OAAOE,GACP,AAAc,UAAd,OAAOD,GACP,AAAc,UAAd,OAAOE,GACPH,IAAOE,GACPD,IAAOE,EAAI,CACX,IAAMC,EAAaJ,EAAKE,EAAK,EAAI,GAAIG,EAAaJ,EAAKE,EAAK,EAAI,GAChE/B,EAAEyB,IAAI,CAAC,CACH,IACAF,EAAIS,EAAaE,KAAKC,GAAG,CAACD,KAAKE,GAAG,CAACb,EAAIK,GAAKR,GAC5CI,EAAIS,EAAaC,KAAKC,GAAG,CAACD,KAAKE,GAAG,CAACZ,EAAIK,GAAKT,GAC/C,CAAE,CACC,IACAG,EACAC,EACAD,EACAC,EACAD,EAAIS,EAAaE,KAAKC,GAAG,CAACD,KAAKE,GAAG,CAACb,EAAIO,GAAKV,GAC5CI,EAAIS,EAAaC,KAAKC,GAAG,CAACD,KAAKE,GAAG,CAACZ,EAAIO,GAAKX,GAC/C,CACL,CACJ,CAEJ,MAEIpB,EAAEyB,IAAI,CAAC,CAAC,IAAKF,EAAGC,EAAE,CAG9B,CACA,OAAOxB,CACX,CAQA,IAAIqC,EAAmI5C,EAAoB,KACvJ6C,EAAuJ7C,EAAoBI,CAAC,CAACwC,GAE7KE,EAAuH9C,EAAoB,KAC3I+C,EAA2I/C,EAAoBI,CAAC,CAAC0C,GAarK,GAAM,CAAEE,YAAa,CAAEC,QAAS,CAAE/B,UAAW,CAAEgC,UAAWC,CAAW,CAAE,CAAE,CAAE,CAAE,CAAIN,IA+K9CO,EArKnC,cAA4BD,EACxBE,aAAc,CAMV,KAAK,IAAIC,WACT,IAAI,CAACC,GAAG,CAAG,EACX,IAAI,CAACC,KAAK,CAAG,EACb,IAAI,CAACC,MAAM,CAAG,EACd,IAAI,CAACC,QAAQ,CAAG,EAAE,CAClB,IAAI,CAACC,IAAI,CAAG,EACZ,IAAI,CAACC,MAAM,CAAG,CAAA,EACd,IAAI,CAACC,UAAU,CAAG,CAAA,EAClB,IAAI,CAACC,SAAS,CAAG,CAAA,CACrB,CAYAC,UAAW,CACP,OAAO,IAAI,CAACC,gBAAgB,IAAM,IAAI,CAACC,MAAM,AACjD,CAOAC,WAAY,CACR,OAAO,IAAI,CAACC,iBAAiB,IAAM,IAAI,CAACF,MAAM,AAClD,CAYAG,YAAYC,CAAW,CAAEC,CAAe,CAAE,QAEtC,AAAIC,AADYF,EAAYG,QAAQ,CACxBd,QAAQ,CAAC,EAAE,GAAK,IAAI,CAACA,QAAQ,CAAC,EAAE,CACjCW,EAAYG,QAAQ,CAExBF,CACX,CAQAG,oBAAqB,CACjB,IAAMC,EAAS,IAAI,CAACC,SAAS,GAC7B,GAAID,EACA,CAAA,IAAK,IAAME,KAASF,EAAOhB,QAAQ,CAC/B,GAAIkB,GAASA,EAAMC,KAAK,CAACC,OAAO,CAC5B,OAAOF,CAEf,CAER,CAOAG,aAAc,CACV,IAAMrB,EAAW,IAAI,CAACA,QAAQ,CAC9B,IAAK,IAAI9B,EAAI,EAAGA,EAAI8B,EAAS7B,MAAM,CAAED,IACjC,GAAI8B,CAAQ,CAAC9B,EAAE,CAACiD,KAAK,CAACC,OAAO,CACzB,MAAO,CAAA,EAGf,MAAO,CAAA,CACX,CAOAE,gBAAiB,CACb,IAAMN,EAAS,IAAI,CAACC,SAAS,GAC7B,GAAID,EAAQ,CACR,IAAMhB,EAAWgB,EAAOhB,QAAQ,CAChC,IAAK,IAAI9B,EAAI,IAAI,CAACqD,iBAAiB,CAAG,EAAGrD,GAAK,EAAGA,IAC7C,GAAI8B,CAAQ,CAAC9B,EAAE,EAAI8B,CAAQ,CAAC9B,EAAE,CAACiD,KAAK,CAACC,OAAO,CACxC,OAAOpB,CAAQ,CAAC9B,EAAE,AAG9B,CACJ,CAOAoC,kBAAmB,CACf,IAAMN,EAAW,IAAI,CAACA,QAAQ,CAC9B,IAAK,IAAI9B,EAAI,EAAGA,EAAI8B,EAAS7B,MAAM,CAAED,IACjC,GAAI8B,CAAQ,CAAC9B,EAAE,CAACiD,KAAK,CAACC,OAAO,CACzB,OAAOpB,CAAQ,CAAC9B,EAAE,AAG9B,CAOAuC,mBAAoB,CAChB,IAAMT,EAAW,IAAI,CAACA,QAAQ,CAC9B,IAAK,IAAI9B,EAAI8B,EAAS7B,MAAM,CAAG,EAAGD,GAAK,EAAGA,IACtC,GAAI8B,CAAQ,CAAC9B,EAAE,CAACiD,KAAK,CAACC,OAAO,CACzB,OAAOpB,CAAQ,CAAC9B,EAAE,AAG9B,CAQA+C,WAAY,CACR,OAAO,IAAI,CAACO,UAAU,AAC1B,CAOAC,eAAgB,CACZ,IAAMzB,EAAW,IAAI,CAACA,QAAQ,CAC9B,IAAK,IAAI9B,EAAI,EAAGA,EAAI8B,EAAS7B,MAAM,CAAED,IACjC,GAAI8B,CAAQ,CAAC9B,EAAE,CAACiD,KAAK,CAACC,OAAO,CACzB,OAAOpB,CAAQ,CAAC9B,EAAE,AAG9B,CACJ,EASA,IAAIwD,EAA+FpF,EAAoB,KACnHqF,EAAmHrF,EAAoBI,CAAC,CAACgF,GAc7I,GAAM,CAAEpC,YAAa,CAAEC,QAAS,CAAE/B,UAAW,CAAEoE,WAAYC,CAAY,CAAE,CAAE,CAAE,CAAE,CAAI1C,IAE7E,CAAE2C,SAAAA,CAAQ,CAAEC,UAAAA,CAAS,CAAEC,MAAAA,CAAK,CAAE,CAAIlE,GAUxC,OAAMmE,UAAuBJ,EACzBlC,aAAc,CAMV,KAAK,IAAIC,WACT,IAAI,CAACsC,iBAAiB,CAAG,CAAA,EACzB,IAAI,CAACC,MAAM,CAAG,CAAA,EACd,IAAI,CAACC,QAAQ,CAAG,AAACT,IAAuGnE,SAAS,CAAC4E,QAAQ,AAC9I,CAMAC,MAAO,CACH,KAAK,CAACA,KAAKC,KAAK,CAAC,IAAI,CAAE1C,WAEvB,IAAM2C,EAAU,IAAI,CAACA,OAAO,AACxBA,CAAAA,GACAA,EAAQC,OAAO,CAAC,CACZC,WAAY,IAAI,CAACrB,OAAO,CAAG,UAAY,QAC3C,GAEJ,IAAI,CAACsB,oBAAoB,EAC7B,CACAA,sBAAuB,CACnB,IAAoBC,EAASxB,AAAf,IAAI,CAAiBwB,MAAM,CAAEC,EAAczB,AAA3C,IAAI,CAA6CoB,OAAO,EAAIpB,AAA5D,IAAI,CAA8DoB,OAAO,CAACK,WAAW,CAAEC,EAAeF,EAAOG,iBAAiB,CAAC3B,AAA/H,IAAI,CAAiI4B,IAAI,CAACC,KAAK,EAAI,EAAE,EAAI,CAAC,EAAGC,EAAajB,EAAMW,EAAOO,OAAO,CAACC,cAAc,CAAEN,EAAaM,cAAc,CAAEhC,AAA5O,IAAI,CAA8O+B,OAAO,CAACC,cAAc,EAAG,CAAEC,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAE,CAAGN,EAAyBO,EAAQ,IAAI,CAACb,MAAM,CAACa,KAAK,CAAEC,EAAoB,AAACtC,AAAtX,IAAI,CAAwXC,OAAO,EAC5YD,CAAAA,AADS,IAAI,CACPf,SAAS,EACZ,CAAC6C,EAAWS,WAAW,EACvBvC,AAAgB,UAAhBA,AAHM,IAAI,CAGJwC,KAAK,AAAW,EAAM,EAAI,EACxC,GAAKxC,AAJS,IAAI,CAIPyC,SAAS,CAIpB,GADA,IAAI,CAACC,qBAAqB,CAAGZ,EACxB9B,AARS,IAAI,CAQPgC,cAAc,CAqCrB,GAAI,AAAChC,AA7CK,IAAI,CA6CH4B,IAAI,CAAC/C,QAAQ,CAAC7B,MAAM,EAAK8E,EAAWa,OAAO,CAIjD,CACD,GAAM,CAAE1F,EAAAA,CAAC,CAAEC,EAAAA,CAAC,CAAE,CAAG,IAAI,CAAC0F,sBAAsB,CAACd,GAC7C9B,AAnDM,IAAI,CAmDJgC,cAAc,CACfa,IAAI,CAAC,CACNC,KAAM9C,AArDJ,IAAI,CAqDMf,SAAS,CAAG,IAAM,IAC9B8D,SAAUV,AAAiB,KAAjBA,EAAMW,QAAQ,CACxBC,gBAAiBhB,EAAQ,EACzBiB,gBAAiBhB,EAAS,EAC1BZ,WAAYtB,AAzDV,IAAI,CAyDYC,OAAO,CAAG,UAAY,QAC5C,GACKoB,OAAO,CAAC,CACTpE,EAAAA,EACAC,EAAAA,EACAiG,QAASb,CACb,EACJ,MAlBItC,AA9CM,IAAI,CA8CJgC,cAAc,CAACoB,OAAO,GAC5B,OAAOpD,AA/CD,IAAI,CA+CGgC,cAAc,KAvCR,CACvB,GAAI,CAAChC,AATK,IAAI,CASH4B,IAAI,CAAC/C,QAAQ,CAAC7B,MAAM,EAAI,CAAC8E,EAAWa,OAAO,CAClD,OAEJ,GAAM,CAAE1F,EAAAA,CAAC,CAAEC,EAAAA,CAAC,CAAE,CAAG,IAAI,CAAC0F,sBAAsB,CAACd,GAAauB,EAAQvB,EAAWwB,SAAS,EAClFtD,AAbM,IAAI,CAaJuD,KAAK,EACX,SACJvD,CAfU,IAAI,CAeRgC,cAAc,CAAGK,EAAMmB,QAAQ,CAChCC,KAAK,CAACzD,AAhBD,IAAI,CAgBGf,SAAS,CAAG,IAAM,IAAKhC,EAAGC,EAAGiF,GACzCU,IAAI,CAAC,CACNX,OAAQA,EAAS,EACjBD,MAAOA,EAAQ,EACfyB,QApByU,EAqBzUL,KAAAA,EACAN,SAAUV,AAAiB,KAAjBA,EAAMW,QAAQ,CACxBC,gBAAiBhB,EAAQ,EACzBiB,gBAAiBhB,EAAS,EAC1ByB,OAAQ7B,EAAW8B,SAAS,EAAI,UAChC,eAAgB9B,EAAW+B,SAAS,CACpC,aAAc,SACdC,MAAO,SACPC,OAAQ,EACRZ,QAASb,EACThB,WAAYtB,AA/BN,IAAI,CA+BQC,OAAO,CAAG,UAAY,QAC5C,GACK+D,QAAQ,CAAC,sBACTA,QAAQ,CAAC,8BACTC,WAAW,CAAC,yBACZC,GAAG,CAACrD,EAAM,CACX0C,MAAO,AAAgB,UAAhB,OAAOF,EACVhB,EAAMmB,QAAQ,CAACW,WAAW,CAACd,GAC3B,SACR,EAAGjB,IACEgC,GAAG,CAAC3C,GACTzB,AA1CU,IAAI,CA0CRgC,cAAc,CAACqC,OAAO,CAACrE,KAAK,CA1CxB,IAAI,AA2ClB,CAuBJ,CACAsE,eAAe9B,CAAK,CAAE,CAClB,IAAMhB,EAAS,IAAI,CAACA,MAAM,CAC1B,IAAI,CAAC+C,MAAM,CAAC,CACRtF,UAAWuD,GAAS,CAAC,IAAI,CAACvD,SAAS,AACvC,EAAG,CAAA,EAAO,KAAK,EAAG,CAAA,GAClB2B,EAAUY,EAAQ,kBAClBA,EAAOgD,MAAM,EACjB,CACApB,SAAU,CACF,IAAI,CAACpB,cAAc,GACnB,IAAI,CAACA,cAAc,CAACoB,OAAO,GAC3B,OAAO,IAAI,CAACpB,cAAc,CAC1B,IAAI,CAACA,cAAc,CAAG,KAAK,GAE3B,IAAI,CAACyC,YAAY,GACjB,IAAI,CAACA,YAAY,CAACrB,OAAO,GACzB,OAAO,IAAI,CAACqB,YAAY,EAE5B,KAAK,CAACrB,QAAQjC,KAAK,CAAC,IAAI,CAAE1C,UAC9B,CACAmE,uBAAuBd,CAAU,CAAE,CAC/B,IAAgDkB,EAAWX,AAA/BrC,AAAd,IAAI,CAAgBwB,MAAM,CAACa,KAAK,CAAmBW,QAAQ,CAAE0B,EAAW5C,EAAWG,KAAK,CAAE0C,EAAY7C,EAAWI,MAAM,CAAE,CAAEjF,EAAAA,EAAI,CAAC,CAAEC,EAAAA,EAAI,CAAC,CAAE+E,MAAAA,EAAQ,CAAC,CAAEC,OAAAA,EAAS,CAAC,CAAE,CAAGlC,AAAnK,IAAI,CAAqKyC,SAAS,EAAI,CAAC,EACrM,MAAO,CACHxF,EAAGA,EACC6E,EAAW7E,CAAC,CACX+F,CAAAA,EAAW,CAAA,CAAA,AAAa,GAAZ2B,CAAc,EAAI1C,EAAQyC,AAAW,IAAXA,CAAc,EACzDxH,EAAGA,EAAIgF,EAAS,EAAIyC,EAAY,EAAI7C,EAAW5E,CAAC,AACpD,CACJ,CACJ,CACAyD,EAASG,EAAgB,WAAY,WACjC,IAAM8D,EAAM,IAAI,CAAC5C,cAAc,CAAEF,EAAa,IAAI,CAACY,qBAAqB,AACpEkC,CAAAA,GAAO9C,GAAYS,aAAe,CAAC,IAAI,CAACtD,SAAS,EACjD2F,EAAIvD,OAAO,CAAC,CAAE8B,QAAS,CAAE,EAEjC,GACAxC,EAASG,EAAgB,YAAa,WAC9B,IAAI,CAACkB,cAAc,EAAI,IAAI,CAAC/B,OAAO,EACnC,IAAI,CAAC+B,cAAc,CAACX,OAAO,CAAC,CAAE8B,QAAS,CAAE,EAAG,IAAI,CAAC3B,MAAM,CAACO,OAAO,CAAC8C,MAAM,EAAEC,OAAOC,UAEvF,GAEApE,EAASG,EAAgB,QAAS,WAC9B,IAAI,CAACwD,cAAc,EACvB,GASA,IAAIU,EAA+F7J,EAAoB,KACnH8J,EAAmH9J,EAAoBI,CAAC,CAACyJ,GAgB7I,GAAM,CAAEE,OAAAA,CAAM,CAAEC,QAAAA,CAAO,CAAEC,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAExE,MAAOyE,CAAmB,CAAEC,KAAAA,CAAI,CAAEC,eAAAA,CAAc,CAAE,CAAI7I,IAuMhE8I,EAPb,CAClBC,SAvLJ,SAAkB9D,CAAI,CAAEG,CAAO,EAC3B,IACqB/B,EAAO6B,EAAO8D,EAAcC,EAAmBrC,EAAOsC,EADrEC,EAAQ/D,EAAQ+D,KAAK,CAAEnE,EAAoBI,EAAQJ,iBAAiB,CAAEoE,EAAchE,EAAQgE,WAAW,CAAEC,EAAmBjE,EAAQiE,gBAAgB,CAAExE,EAASO,EAAQP,MAAM,CAAEyE,EAASlE,EAAQkE,MAAM,CAAEC,EAAWnE,EAAQmE,QAAQ,CAAEC,EAAS3E,EAAO2E,MAAM,CAAEC,EAAoB5E,EAAOa,KAAK,CAACN,OAAO,CAACM,KAAK,CA+BjT,OAhBIT,IACA5B,EAAQmG,CAAM,CAACvE,EAAK7E,CAAC,CAAC,CACtB8E,EAAQF,CAAiB,CAACC,EAAKC,KAAK,CAAC,EAAI,CAAC,EACxB7B,GAAS6B,EAAM8D,YAAY,GAEzCC,EAAoB5F,EAAM8F,KAAK,CAAIG,CAAAA,EAC/BA,EAAOjJ,MAAM,CACboJ,EAAkBC,UAAU,AAAD,EAC/BV,EAAeM,GAAUA,CAAM,CAACL,EAAkB,EAGlD,AAACpE,EAAOa,KAAK,CAACiE,UAAU,EACxB/C,CAAAA,EAAQgC,EAAKvF,GAASA,EAAM+B,OAAO,CAACwB,KAAK,CAAE1B,GAASA,EAAM0B,KAAK,CAAEoC,EAAcI,GAAeQ,AAtBjF,CAAA,AAAChD,IAClB,IAAMiD,EAAiB3E,GAASA,EAAM2E,cAAc,QACpD,AAAIA,GACAA,AAAuB,eAAvBA,EAAe3K,GAAG,EAClBiK,GACAI,EACOjB,IAAsGwB,KAAK,CAAClD,GAAOmD,QAAQ,CAACF,EAAeG,EAAE,CAAIb,CAAAA,EAAQI,CAAO,GAAIhK,GAAG,GAE3KqH,CACX,CAAA,EAamHwC,GAAcvE,EAAO+B,KAAK,CAAA,EAEzIsC,EAAaN,EAAKvF,GAASA,EAAM+B,OAAO,CAAC8D,UAAU,CAAEhE,GAASA,EAAMgE,UAAU,CAAED,EAAmBI,EAAkBjE,EAAQ8D,UAAU,GAEpI,CACHtC,MAAOA,EACPsC,WAAYA,CAChB,CACJ,EAoJIe,gBAlIJ,SAAyBC,CAAM,EAC3B,IACIC,EAAUC,EAAWhK,EAAGiK,EAAML,EAAIM,EADhCC,EAAS,CAAC,EAEhB,GAAI7B,EAASwB,GA2BT,IA1BAG,EAAO5B,EAASyB,EAAOG,IAAI,EAAIH,EAAOG,IAAI,CAAG,EAC7CC,EAASJ,EAAOI,MAAM,CACtBF,EAAY,CAAC,EACbD,EAAWzB,EAASwB,EAAOC,QAAQ,EAAID,EAAOC,QAAQ,CAAG,CAAC,EACtD3B,EAAQ8B,IACRF,CAAAA,EAAYE,EAAOE,MAAM,CAAC,CAAChL,EAAKiL,KAC5B,IAAIvF,EAAOwF,EAAiBtF,EAgB5B,OAfIsD,EAAS+B,IAAShC,EAASgC,EAAKvF,KAAK,IAErCwF,EAAkB9B,EAAKxD,AADvBA,CAAAA,EAAUuD,EAAoB,CAAC,EAAG8B,EAAI,EACPC,eAAe,CAAEP,EAASO,eAAe,EAExE,OAAOtF,EAAQsF,eAAe,CAC9B,OAAOtF,EAAQF,KAAK,CAGhBwD,EAASlJ,CAAG,CADhB0F,EAAQuF,EAAKvF,KAAK,CAAIwF,CAAAA,EAAkB,EAAIL,EAAO,CAAA,EAC5B,EACnB1B,EAAoB,CAAA,EAAMnJ,CAAG,CAAC0F,EAAM,CAAEE,GAGtC5F,CAAG,CAAC0F,EAAM,CAAGE,GAGd5F,CACX,EAAG,CAAC,EAAC,EAETwK,EAAKvB,EAASyB,EAAOF,EAAE,EAAIE,EAAOF,EAAE,CAAG,EAClC5J,EAAI,EAAGA,GAAK4J,EAAI5J,IACjBmK,CAAM,CAACnK,EAAE,CAAGuI,EAAoB,CAAC,EAAGwB,EAAUzB,EAAS0B,CAAS,CAAChK,EAAE,EAAIgK,CAAS,CAAChK,EAAE,CAAG,CAAC,GAG/F,OAAOmK,CACX,EAgGII,aAvBJ,SAAsB9F,CAAM,CAAE+F,CAAW,EACrC,GAAM,CAAElF,MAAAA,CAAK,CAAEN,QAAAA,CAAO,CAAE,CAAGP,EAAQ,CAAEgG,aAAAA,EAAe,CAAC,CAAEC,UAAAA,EAAY,CAAC,CAAE,CAAG1F,EAAS,CAAE2F,UAAAA,EAAY,CAAC,CAAE,CAAGrF,EAGtG,GAAIoF,AAAc,SAAdA,EAAsB,CACtB,GAAI,AAAwB,UAAxB,OAAOD,GAA6B,KAAKG,IAAI,CAACH,GAE9C,OAAOE,EADkDH,CAAAA,EAAcK,AAAtDC,WAAWL,GAAgB,IAAuCD,CAAAA,EAAc,CAAA,CAAC,EAGtG,IAAMO,EAAYC,OAAOP,GACzB,MAAO,AAAEE,CAAAA,EAAYI,CAAQ,EACxBP,CAAAA,GAAe,CAAA,EAAMO,CAC9B,CACA,OAAOtC,EAAeiC,EAAWC,EACrC,EAUIM,cA3FJ,SAASA,EAAcC,CAAI,CAAElG,CAAO,EAChC,IAAMmG,EAASnG,EAAQmG,MAAM,CAAEC,EAASpG,EAAQoG,MAAM,CAAqCC,EAAWC,AAAhCtG,EAAQsG,WAAW,AAAwB,CAACF,EAAO,CAAEd,EAAmBtF,AAA4B,CAAA,IAA5BA,EAAQsF,eAAe,CAAsCrH,EAAQmG,AAAxBpE,EAAQoE,MAAM,AAAgB,CAAC8B,EAAKlL,CAAC,CAAC,CAAEuL,EAAetI,GAASA,EAAM+B,OAAO,EAAI,CAAC,EAAGlD,EAAW,EAAE,CACzR0J,EAAgB,CACpBN,CAAAA,EAAKO,YAAY,CAAGP,EAAKpG,KAAK,CAAIwF,CAAAA,EAAkB,EAAIe,EAASvG,KAAK,AAAD,EACrEoG,EAAKQ,IAAI,CAAGlD,EAAKvF,GAASA,EAAMyI,IAAI,CAAE,IACtCR,EAAKhI,OAAO,CAAIkI,IAAWF,EAAKS,EAAE,EAC9B3G,AAAoB,CAAA,IAApBA,EAAQ9B,OAAO,CACf,AAAkB,YAAlB,OAAOiI,GACPD,CAAAA,EAAOC,EAAOD,EAAMlG,EAAO,EAG/BkG,EAAKpJ,QAAQ,CAAC8J,OAAO,CAAC,CAAC5I,EAAOhD,KAC1B,IAAM6L,EAAa1D,EAAO,CAAC,EAAGnD,GAC9BmD,EAAO0D,EAAY,CACf9C,MAAO/I,EACPmJ,SAAU+B,EAAKpJ,QAAQ,CAAC7B,MAAM,CAC9BiD,QAASgI,EAAKhI,OAAO,AACzB,GACAF,EAAQiI,EAAcjI,EAAO6I,GAC7B/J,EAAS1B,IAAI,CAAC4C,GACVA,EAAME,OAAO,EACbsI,CAAAA,GAAiBxI,EAAM8I,GAAG,AAAD,CAEjC,GAEA,IAAMC,EAAQvD,EAAK+C,EAAaQ,KAAK,CAAEP,GAMvC,OALAN,EAAKhI,OAAO,CAAG6I,GAAS,GAAMP,CAAAA,EAAgB,GAAKN,EAAKhI,OAAO,AAAD,EAC9DgI,EAAKpJ,QAAQ,CAAGA,EAChBoJ,EAAKM,aAAa,CAAGA,EACrBN,EAAKc,MAAM,CAAGd,EAAKhI,OAAO,EAAI,CAACsI,EAC/BN,EAAKY,GAAG,CAAGC,EACJb,CACX,EA4DIe,aA/CJ,SAAsBxH,CAAM,EACxB,IAAIyH,EAAQlH,EAaZ,OAZIsD,EAAS7D,KAETO,EAAUsD,EAAS7D,EAAOO,OAAO,EAAIP,EAAOO,OAAO,CAAG,CAAC,EAEvDkH,EAAS1D,EAAK/D,EAAO0H,QAAQ,CAAEnH,EAAQkH,MAAM,CAAE,IAE3C5D,EAAS7D,EAAO2H,WAAW,GAC3B3H,CAAAA,EAAO2H,WAAW,CAACF,MAAM,CAAGA,CAAK,EAGrCzH,EAAO0H,QAAQ,CAAGD,GAEfA,CACX,CAiCA,EAgBM,CAAE1D,KAAM6D,CAAkB,CAAElE,OAAQmE,CAAoB,CAAE,CAAI1M,IAE9D,CAAEwB,YAAa,CAAEmL,OAAQ,CAAEjN,UAAW,CAAEoE,WAAY8I,CAAW,CAAE,CAAE,CAAE,CAAE,CAAIvL,IA4D9CwL,EAlDnC,cAAwBD,EAMpB/K,YAAYgD,CAAM,CAAEO,CAAO,CAAE9E,CAAC,CAAE+C,CAAK,CAAE,CACnC,KAAK,CAACwB,EAAQO,EAAS9E,GAMvB,IAAI,CAACwM,eAAe,CAAG,CAAA,EACvB,IAAI,CAACC,YAAY,CAAG,OACpB,IAAI,CAAC1I,MAAM,CAAG,CAAA,EACd,IAAI,CAACY,IAAI,CAAG,CAAC,EACb,IAAI,CAAC8H,YAAY,CAAG,OACpB,IAAI,CAACD,eAAe,CAAG,CAAA,EACnBzJ,IACA,IAAI,CAAC2J,QAAQ,CAAG3J,EAAM4B,IAAI,CAACvB,UAAU,CAACL,KAAK,CAC3C,IAAI,CAACC,OAAO,CAAGD,EAAMC,OAAO,CAC5B,IAAI,CAAC2J,MAAM,CAAG5J,EACd,IAAI,CAAC0I,EAAE,CAAG,IAAI,CAACkB,MAAM,CAAClB,EAAE,CAAG,IAAM,IAAI,CAACiB,QAAQ,CAACjB,EAAE,CAEzD,CAMAnE,OAAOxC,CAAO,CAAEyC,CAAM,CAAEO,CAAS,CAAE8E,CAAQ,CAAE,CACzC,IAAMC,EAAa,CACfpB,GAAI,IAAI,CAACA,EAAE,CACXgB,aAAc,IAAI,CAACA,YAAY,AACnC,EACAlJ,IAAsGnE,SAAS,CAACkI,MAAM,CAAChI,IAAI,CAAC,IAAI,CAAEwF,EAAS,CAAA,IAAI,CAACf,MAAM,EAAWwD,EACjKO,EAAW8E,GACX,IAAI,CAAC5J,OAAO,CAAG,IAAI,CAAC2J,MAAM,CAAC3J,OAAO,CAClCoJ,EAAqB,IAAI,CAAES,GACvBV,EAAmB5E,EAAQ,CAAA,IAC3B,IAAI,CAAChD,MAAM,CAACa,KAAK,CAACmC,MAAM,CAACO,EAEjC,CACJ,CA6BA,OAAMgF,EAmBF,OAAOC,gBAAgBnK,CAAM,CAAEE,CAAK,CAAEkK,CAAO,CAAE,CAE3C,IAAMC,EAAY,IAAI3L,EAmBtB,OAlBA2L,EAAUxB,EAAE,CAAG7I,EAAO6I,EAAE,CAAG,IAAMuB,EACjCC,EAAUvK,QAAQ,CAAGE,EAGrBqK,EAAUrL,QAAQ,CAAC1B,IAAI,CAAC4C,GACxBmK,EAAUrK,MAAM,CAAGA,EAAO6I,EAAE,CAC5BwB,EAAU7J,UAAU,CAAGR,EACvBqK,EAAUlK,KAAK,CAAGD,EAAMC,KAAK,CAC7BkK,EAAUrI,KAAK,CAAG9B,EAAM8B,KAAK,CAAGoI,EAChCC,EAAU9J,iBAAiB,CAAGL,EAAMK,iBAAiB,CACrD8J,EAAUjK,OAAO,CAAGF,EAAME,OAAO,CAEjCJ,EAAOhB,QAAQ,CAACkB,EAAMK,iBAAiB,CAAC,CAAG8J,EAC3CnK,EAAMoK,aAAa,CAAGtK,EACtBE,EAAMK,iBAAiB,CAAG,EAE1BL,EAAMM,UAAU,CAAG6J,EACnBnK,EAAMF,MAAM,CAAGqK,EAAUxB,EAAE,CACpBwB,CACX,CAWAE,mBAAmB5I,CAAM,CAAE,CAEvB,IAAM6I,EAAQ7I,EAAO8I,QAAQ,CAC7B,IAAI,CAACC,WAAW,CAACF,GACjB,IAAMlQ,EAAOqH,EAAOyG,IAAI,CACpB9N,IACAqQ,AALe,IAAI,CAKRC,kBAAkB,CAACtQ,EAAM,GACpCqQ,AANe,IAAI,CAMRE,YAAY,CAACL,GACxBG,AAPe,IAAI,CAORG,SAAS,CAACxQ,GACrBqQ,AARe,IAAI,CAQRI,UAAU,CAACzQ,EAAM,CAACA,EAAK2E,IAAI,EACtC0L,AATe,IAAI,CASRK,WAAW,CAACR,GAE/B,CAOAK,aAAaL,CAAK,CAAE,CAChB,IAAK,IAAMzI,KAAQyI,EACf,IAAK,IAAItK,KAAS6B,EAAK/C,QAAQ,CAE3B,GAAIkB,GAASA,EAAM8B,KAAK,CAAGD,EAAKC,KAAK,CAAG,EAAG,CAGvC,IAAIoI,EAAUlK,EAAM8B,KAAK,CAAGD,EAAKC,KAAK,CAAG,EAEzC,KAAOoI,EAAU,GACblK,EAAQgK,EAAgBC,eAAe,CAACpI,EAAM7B,EAAOkK,GACrDA,GAER,CAGZ,CAKAM,YAAYF,CAAK,CAAE,CACf,IAAK,IAAMzI,KAAQyI,EACfzI,EAAKlD,GAAG,CAAG,EACXkD,EAAKjC,QAAQ,CAAGiC,EAChBA,EAAKjD,KAAK,CAAG,EACbiD,EAAKxC,MAAM,CAAG,KAAK,EACnBwC,EAAKhD,MAAM,CAAG,EACdgD,EAAK9C,IAAI,CAAG,CAEpB,CAUA2L,mBAAmB7I,CAAI,CAAEkE,CAAK,CAAE,CAC5B,IAAyBjH,EAAW+C,EAAK/C,QAAQ,CACjD,IAAK,IAAI9B,EAAI,EAAG+N,EAAOjM,EAAS7B,MAAM,CAAED,EAAI+N,EAAM,EAAE/N,EAChDyN,AAFe,IAAI,CAERC,kBAAkB,CAAC5L,CAAQ,CAAC9B,EAAE,CAAEA,EAE/C6E,CAAAA,EAAKxB,iBAAiB,CAAG0F,CAC7B,CAQA6E,UAAU/I,CAAI,CAAE,KAIRmJ,EAEJ,GAAKnJ,EAAK1B,WAAW,GAUhB,CAID,IAAIT,EAAkBmC,EAAKzC,gBAAgB,GAC3C,IAAK,IAAMY,KAAS6B,EAAK/C,QAAQ,CAC7B2L,AArBW,IAAI,CAqBJG,SAAS,CAAC5K,GACrBN,EAAkB+K,AAtBP,IAAI,CAsBcQ,SAAS,CAACjL,EAAON,GAElD+K,AAxBe,IAAI,CAwBRS,aAAa,CAACrJ,GACzB,IAAMsJ,EAAYtJ,EAAKzC,gBAAgB,GAAIgM,EAAavJ,EAAKtC,iBAAiB,GAI9E8L,EAAW,AAACF,CAAAA,EAAUpM,IAAI,CAAGqM,EAAWrM,IAAI,AAAD,EAAK,EAChDiM,CAAAA,EAAcnJ,EAAKzB,cAAc,EAAC,GAE9ByB,EAAK9C,IAAI,CAAGiM,EAAYjM,IAAI,CA9BlB,EA+BV8C,EAAKlD,GAAG,CAAGkD,EAAK9C,IAAI,CAAGsM,GAGvBxJ,EAAK9C,IAAI,CAAGsM,CAEpB,KAhCIL,CAAAA,EAAcnJ,EAAKzB,cAAc,EAAC,GAE9ByB,EAAK9C,IAAI,CAAGiM,EAAYjM,IAAI,CANlB,EAOV8C,EAAKlD,GAAG,CAAGkD,EAAK9C,IAAI,EAGpB8C,EAAK9C,IAAI,CAAG,CA2BxB,CAUA8L,WAAWhJ,CAAI,CAAEyJ,CAAM,CAAE,CAOrB,IAAK,IAAMtL,KAFX6B,EAAK0J,SAAS,CAAG1J,EAAK9C,IAAI,CAAGuM,EAC7BzJ,EAAK2J,SAAS,CAAG3J,EAAKC,KAAK,CACPD,EAAK/C,QAAQ,EAC7B2L,AAPe,IAAI,CAORI,UAAU,CAAC7K,EAAOsL,EAASzJ,EAAKlD,GAAG,CAEtD,CAOAuM,cAAcrJ,CAAI,CAAE,CAChB,IAAIjD,EAAQ,EAAGC,EAAS,EACxB,IAAK,IAAI7B,EAAI6E,EAAK/C,QAAQ,CAAC7B,MAAM,CAAG,EAAGD,GAAK,EAAGA,IAAK,CAChD,IAAMyO,EAAY5J,EAAK/C,QAAQ,CAAC9B,EAAE,AAClCyO,CAAAA,EAAU1M,IAAI,EAAIH,EAClB6M,EAAU9M,GAAG,EAAIC,EACjBC,GAAU4M,EAAU5M,MAAM,CAC1BD,GAAS6M,EAAU7M,KAAK,CAAGC,CAC/B,CACJ,CAkBAoM,UAAUpJ,CAAI,CAAEnC,CAAe,CAAE,CAC7B,IAAyBsL,EAAcnJ,EAAKzB,cAAc,GAC1D,GAAI4K,EAAa,CACb,IAAIU,EAAe7J,EAAM8J,EAAe9J,EAAMpC,EAAcuL,EAAaY,EAAcF,EAAa7L,kBAAkB,GAAIgM,EAAcH,EAAa/M,GAAG,CAAEmN,EAAcH,EAAahN,GAAG,CAAEoN,EAAatM,EAAYd,GAAG,CAAEqN,EAAaJ,EAAYjN,GAAG,CACpP,KAAOc,GACHA,EAAYH,SAAS,IACrBoM,GACAA,EAAavM,QAAQ,IAAI,CACzBM,EAAcA,EAAYH,SAAS,GACnCsM,EAAcA,EAAYzM,QAAQ,GAClCuM,EAAeA,EAAavM,QAAQ,GAEpCwM,AADAA,CAAAA,EAAeA,EAAarM,SAAS,EAAC,EACzBM,QAAQ,CAAGiC,EACxB,IAA2BjD,EAAQa,EAAYV,IAAI,CAC/CgN,EACCL,CAAAA,EAAa3M,IAAI,CAAG8M,CAAU,EAFX,EAIpBjN,EAAQ,IACR6L,AAjBO,IAAI,CAiBAwB,WAAW,CAACpK,EAAKrC,WAAW,CAACC,EAAaC,GAAkBmC,EAAMjD,GAC7EiN,GAAejN,EACfkN,GAAelN,GAEnBmN,GAActM,EAAYd,GAAG,CAC7BkN,GAAeH,EAAa/M,GAAG,CAC/BqN,GAAcJ,EAAYjN,GAAG,CAC7BmN,GAAeH,EAAahN,GAAG,AACnC,CACIc,GACAA,EAAYH,SAAS,IACrB,CAACqM,EAAarM,SAAS,KACvBqM,EAAatM,MAAM,CAAGI,EAAYH,SAAS,GAC3CqM,EAAahN,GAAG,EAAIoN,EAAaD,GAEjCJ,GACAA,EAAavM,QAAQ,IACrB,CAACyM,EAAYzM,QAAQ,KACrByM,EAAYvM,MAAM,CAAGqM,EAAavM,QAAQ,GAC1CyM,EAAYjN,GAAG,EAAIkN,EAAcG,GAErCtM,EAAkBmC,CACtB,CACA,OAAOnC,CACX,CASAuM,YAAYC,CAAQ,CAAEC,CAAS,CAAEvN,CAAK,CAAE,CACpC,IAAMwN,EAAWD,EAAU9L,iBAAiB,CAAG6L,EAAS7L,iBAAiB,AACzE8L,CAAAA,EAAUtN,MAAM,EAAID,EAAQwN,EAC5BD,EAAUvN,KAAK,EAAIA,EACnBuN,EAAUpN,IAAI,EAAIH,EAClBuN,EAAUxN,GAAG,EAAIC,EACjBsN,EAASrN,MAAM,EAAID,EAAQwN,CAC/B,CAOAtB,YAAYR,CAAK,CAAE,CACf,IAAK,IAAMzI,KAAQyI,EACXzI,EAAKuI,aAAa,GAElBvI,EAAKxB,iBAAiB,CAAGwB,EAAKvB,UAAU,CAACD,iBAAiB,CAC1DwB,EAAK/B,MAAM,CAAG+B,EAAKuI,aAAa,CAACtK,MAAM,CACvC+B,EAAKvB,UAAU,CAAGuB,EAAKuI,aAAa,CAEpC,OAAOvI,EAAKuI,aAAa,CAACtL,QAAQ,CAAC+C,EAAKxB,iBAAiB,CAAC,CAC1DwB,EAAKuI,aAAa,CAACtL,QAAQ,CAAC+C,EAAKxB,iBAAiB,CAAC,CAAGwB,EACtDA,EAAKuI,aAAa,CAAG,KAAK,EAGtC,CACJ,CAySA,IAAIiC,EAAmHjR,EAAoB,IACvIkR,EAAuIlR,EAAoBI,CAAC,CAAC6Q,GAgBjK,GAAM,CAAEE,QAAAA,CAAO,CAAE,CAAI3P,IACf,CAAEgE,SAAU4L,CAAiB,CAAE1L,MAAO2L,CAAc,CAAEC,UAAAA,CAAS,CAAEC,QAAAA,CAAO,CAAExH,OAAQyH,CAAe,CAAE,CAAIhQ,IAyB7G,SAASiQ,EAAY/P,CAAI,CAAEgQ,CAAe,EAEtCA,EAAkBL,EAAe,CAAA,EAAM,CACnC7J,QAAS,CAAA,EACTmK,WAAY,CACRC,GAAI,GACJC,YAAa,MACbC,WAAY,QAChB,CACJ,EAAGJ,GACH,IAAMK,EAAM,IAAI,CAAC1J,QAAQ,CAAC0J,GAAG,CAAEC,EAAc,IAAI,CAACrK,IAAI,EAAI,IAAI,CAAEsK,EAAWD,EAAYC,QAAQ,CAAE,CAAEN,WAAAA,CAAU,CAAEnK,QAAAA,CAAO,CAAE,CAAGkK,EAM3H,GALAhQ,EAAOA,GAASuQ,GAAYA,EAASvQ,IAAI,CAErCuQ,GACAA,EAASC,IAAI,GAEbxQ,GAAQ8F,EAAS,CACjB,IAAM0K,EAAOd,EAAkBY,EAAa,kBAAmB,AAACG,IAC5D,GAAIzQ,GAAQ8F,EAAS,CAEjB,IAAI4K,EAAa1Q,EAAKgG,IAAI,CAAC,KACvB,CAAC0K,GACD1Q,EAAKgG,IAAI,CAAC,KAAM0K,EAAad,KAGjC,IAAMe,EAAc,CAGhBvQ,EAAG,EACHC,EAAG,CACP,EACIwP,EAAQI,EAAWW,EAAE,IACrBD,EAAYC,EAAE,CAAGX,EAAWW,EAAE,CAC9B,OAAOX,EAAWW,EAAE,EAEpBf,EAAQI,EAAWC,EAAE,IACrBS,EAAYT,EAAE,CAAGD,EAAWC,EAAE,CAC9B,OAAOD,EAAWC,EAAE,EAExBI,EAAYtK,IAAI,CAAC2K,GAEjB,IAAI,CAAC3K,IAAI,CAAC,CAAE6K,UAAW,EAAG,GACtB,IAAI,CAACC,GAAG,EACR,CAAA,IAAI,CAACA,GAAG,CAAG,IAAI,CAACA,GAAG,CAACvK,OAAO,EAAC,EAGhC,IAAMvE,EAAWyO,EAAEjD,KAAK,CAACuD,KAAK,CAAC,EAC/BN,CAAAA,EAAEjD,KAAK,CAACrN,MAAM,CAAG,EACjBsQ,EAAEjD,KAAK,CAAC,EAAE,CAAG,CACTwD,QAAS,WACTf,WAAYH,EAAgBG,EAAY,CACpC,cAAeA,EAAWG,UAAU,CACpCa,KAAM,CAAC,EAAEZ,EAAI,CAAC,EAAEK,EAAW,CAAC,AAChC,GACA1O,SAAAA,CACJ,CACJ,CACJ,EAEAsO,CAAAA,EAAYC,QAAQ,CAAG,CAAEvQ,KAAAA,EAAMwQ,KAAAA,CAAK,CACxC,MAEIF,EAAYtK,IAAI,CAAC,CAAE4K,GAAI,EAAGV,GAAI,CAAE,GAChC,OAAOI,EAAYC,QAAQ,CAO/B,OALI,IAAI,CAACW,KAAK,GAEVZ,EAAYa,SAAS,CAAG,GACxB,IAAI,CAACxK,QAAQ,CAACyK,SAAS,CAACd,IAErB,IAAI,AACf,CAWA,SAASe,EAAWC,CAAK,EACrB,IAAMC,EAAOD,EAAMC,IAAI,CAAEC,EAAK,IAAI,CAAChK,OAAO,EAAEiK,cAAc,YAC1D,GAAID,EAAI,CACJ,IAAME,EAAU,EAAE,CAAE,CAAEC,EAAAA,CAAC,CAAEC,EAAAA,CAAC,CAAE,CAAG,IAAI,CAACjL,QAAQ,CAACkL,WAAW,CAAC,IAAI,CAACrK,OAAO,EAAGsK,EAAYF,EAAID,EAAGI,EAAmB,AAAIC,OAAO,gEAEtG,KAAMC,EAAQT,EAC5BU,SAAS,CACTC,OAAO,CAACJ,EAAkB,IAC1BK,KAAK,CAAC,sCAAuCC,EAAaJ,EAAM9R,MAAM,CAIrEmS,EAAqB,CAACC,EAAWC,KACnC,GAAM,CAAEpS,EAAAA,CAAC,CAAEC,EAAAA,CAAC,CAAE,CAAGmS,EAAgBtM,EAAW,AAACsL,CAAAA,EAAGiB,iBAAiB,CAACF,GAAa,EAAC,EAAK9C,EAASiD,EAAS3R,KAAK4R,GAAG,CAACzM,GAAW0M,EAAS7R,KAAK8R,GAAG,CAAC3M,GAC7I,MAAO,CACH,CACI9F,EAAI0R,EAAYY,EAChBrS,EAAIyR,EAAYc,EACnB,CACD,CACIxS,EAAIuR,EAAIe,EACRrS,EAAIsR,EAAIiB,EACX,CACJ,AACL,EACA,IAAK,IAAI1S,EAAI,EAAG4S,EAAY,EAAGA,EAAYT,EAAYS,IAAa,CAChE,IAA+BC,EAAUC,AAA5Bf,CAAK,CAACa,EAAU,CAAiB3S,MAAM,CACpD,IAAK,IAAI8S,EAAgB,EAAGA,EAAgBF,EAASE,GAAiB,EAClE,GAAI,CACA,IAAMC,EAAgBhT,EAClB+S,EACAH,EAAY,CAACK,EAAOC,EAAM,CAAGd,EAAmBY,EAAc1B,EAAG6B,sBAAsB,CAACH,GACxFD,AAAkB,CAAA,IAAlBA,GACAvB,EAAQpR,IAAI,CAAC8S,GACb1B,EAAQpR,IAAI,CAAC6S,KAGTL,AAAc,IAAdA,GACApB,EAAQ4B,OAAO,CAACF,GAEhBN,IAAcT,EAAa,GAC3BX,EAAQpR,IAAI,CAAC6S,GAGzB,CACA,MAAO1C,EAAG,CAGN,KACJ,CAEJvQ,GAAK6S,EAAU,EACf,GAAI,CACA,IAAMG,EAAehT,EAAI4S,EAAWS,EAAU/B,EAAGgC,oBAAoB,CAACN,GAAe,CAACC,EAAOC,EAAM,CAAGd,EAAmBY,EAAcK,GACvI7B,EAAQ4B,OAAO,CAACF,GAChB1B,EAAQ4B,OAAO,CAACH,EACpB,CACA,MAAO1C,EAAG,CAGN,KACJ,CACJ,CAEIiB,EAAQvR,MAAM,EACduR,EAAQpR,IAAI,CAACoR,CAAO,CAAC,EAAE,CAACX,KAAK,IAEjCQ,EAAKG,OAAO,CAAGA,CACnB,CACA,OAAOH,CACX,CAWA,SAASkC,EAAanC,CAAK,EACvB,IAAMoC,EAAepC,EAAMoC,YAAY,CAAEvQ,EAAQmO,EAAMnO,KAAK,CAAE6M,EAAmB0D,CAAY,CAACvQ,EAAM0J,YAAY,CAAG,WAAW,EAC1H6G,EAAanD,QAAQ,CACrBP,GAAmB,CAAC0D,EAAaC,OAAO,GACxC,IAAI,CAAC5D,WAAW,CAAC5M,EAAMyQ,gBAAgB,GAAG,IAAI,GAAKzQ,EAAMoB,OAAO,CAAEyL,GAC9D7M,EAAM0Q,aAAa,EACnB,CAAC7D,EAAgBlK,OAAO,EAExB3C,CAAAA,EAAM0Q,aAAa,CAAI1Q,EAAM0Q,aAAa,CAACtN,OAAO,EAAE,EAGhE,CA0BA,GAAM,CAAEuN,YAAaC,CAA2B,CAAE,CA5/C5B,CAClBhU,YAAAA,EACA+T,YA5HgB,CAChB,QAOJ,SAAwBE,CAAU,EAC9B,GAAM,CAAEvT,GAAAA,CAAE,CAAEC,GAAAA,CAAE,CAAEC,GAAAA,CAAE,CAAEC,GAAAA,CAAE,CAAEwE,MAAAA,EAAQ,CAAC,CAAEe,SAAAA,EAAW,CAAA,CAAK,CAAE8N,OAAAA,CAAM,CAAEC,cAAAA,CAAa,CAAE,CAAGF,EACzEhU,EAAO,CACT,CAAC,IAAKS,EAAIC,EAAG,CACb,CAAC,IAAKD,EAAIC,EAAG,CACb,CAAC,IAAKD,EAAIC,EAAID,EAAIG,EAAIH,EAAIG,EAAG,CAC7B,CAAC,IAAKH,EAAIG,EAAG,CACb,CAAC,IAAKH,EAAIC,EAAID,EAAIG,EAAIH,EAAIG,EAAG,CAC7B,CAAC,IAAKH,EAAIG,EAAG,CAChB,CACD,OAAOsT,EACHnU,EAAY,CACR,CAAC,IAAKU,EAAIC,EAAG,CACb,CAAC,IAAKD,EAAK2E,EAASe,CAAAA,EAAW,IAAO,EAAE,EAAIzF,EAAG,CAC/C,CAAC,IAAKD,EAAK2E,EAASe,CAAAA,EAAW,IAAO,EAAE,EAAIvF,EAAG,CAC/C,CAAC,IAAKD,EAAIC,EAAG,CAChB,CAAEqT,GACHjU,CACR,EAxBImU,SA4BJ,SAAyBH,CAAU,EAC/B,GAAM,CAAEvT,GAAAA,CAAE,CAAEC,GAAAA,CAAE,CAAEC,GAAAA,CAAE,CAAEC,GAAAA,CAAE,CAAEwE,MAAAA,EAAQ,CAAC,CAAEe,SAAAA,EAAW,CAAA,CAAK,CAAE+N,cAAAA,CAAa,CAAE,CAAGF,EACvE,OAAOE,EAAgB,CACnB,CAAC,IAAKzT,EAAIC,EAAG,CACb,CAAC,IAAKD,EAAK2E,EAASe,CAAAA,EAAW,GAAK,CAAA,EAAIvF,EAAG,CAC3C,CAAC,IAAKD,EAAIC,EAAG,CAChB,CAAG,CACA,CAAC,IAAKH,EAAIC,EAAG,CACb,CAAC,IAAKD,EAAIG,EAAG,CACb,CAAC,IAAKH,EAAIG,EAAG,CAChB,AACL,EAtCIwT,OA0CJ,SAAuBJ,CAAU,EAC7B,GAAM,CAAEvT,GAAAA,CAAE,CAAEC,GAAAA,CAAE,CAAEC,GAAAA,CAAE,CAAEC,GAAAA,CAAE,CAAEyT,OAAAA,EAAS,CAAC,CAAEjP,MAAAA,EAAQ,CAAC,CAAEe,SAAAA,EAAW,CAAA,CAAK,CAAE+N,cAAAA,CAAa,CAAE,CAAGF,EACnF,OAAOE,EACH,CACI,CAAC,IAAKzT,EAAIC,EAAG,CACb,CACI,IACAD,EAAK4T,EACL3T,EACAD,EAAK4T,EAASjP,EAASe,CAAAA,EAAW,GAAK,CAAA,EACvCvF,EACAH,EAAK2E,EAASe,CAAAA,EAAW,GAAK,CAAA,EAC9BvF,EACH,CACD,CAAC,IAAKD,EAAIC,EAAG,CAChB,CACD,CACI,CAAC,IAAKH,EAAIC,EAAG,CACb,CAAC,IAAKD,EAAIC,EAAID,EAAIG,EAAIH,EAAIG,EAAG,CAC7B,CAAC,IAAKD,EAAIC,EAAG,CAChB,AACT,CA9DA,CAyHA,EA2/CM,CAAE+D,OAAQ,CAAEnF,UAAW8U,CAAW,CAAE,CAAEhT,YAAa,CAAEC,QAASgT,EAAa,CAAE9H,OAAQ+H,EAAY,CAAE,CAAE,CAAIrT,IAEzG,CAAE3B,UAAW,CAAEiV,QAAAA,EAAO,CAAE,CAAE,CAAIpT,IAI9B,CAAE0I,gBAAiB2K,EAA+B,CAAEjK,aAAckK,EAA4B,CAAE,CAAG/L,EAEnG,CAAEgM,SAAAA,EAAQ,CAAEC,MAAAA,EAAK,CAAExM,OAAQyM,EAAsB,CAAE9Q,MAAO+Q,EAAqB,CAAErM,KAAMsM,EAAoB,CAAErM,eAAgBsM,EAA8B,CAAEC,MAAAA,EAAK,CAAE,CAAIpV,IAM9KqV,AAjCiB,CAAA,CACbC,QATJ,SAAiBC,CAAe,EAC5B3F,EAAkB2F,EAAiB,eAAgBhE,GACnD3B,EAAkB2F,EAAiB,wBAAyB5B,GAC5D,IAAM6B,EAAkBD,EAAgB7V,SAAS,AAC7C,AAAC8V,CAAAA,EAAgBvF,WAAW,EAC5BuF,CAAAA,EAAgBvF,WAAW,CAAGA,CAAU,CAEhD,CAGA,CAAA,EA+BoBqF,OAAO,CAAE5F,IAe7B,OAAM+F,WAAwBhB,GAC1B5S,aAAc,CAMV,KAAK,IAAIC,WACT,IAAI,CAAC6L,QAAQ,CAAG,EAAE,CAClB,IAAI,CAAC+H,KAAK,CAAG,EAAE,AACnB,CAMAC,MAAO,CACH,KAAK,CAACA,KAAKnR,KAAK,CAAC,IAAI,CAAE1C,WACvB,IAAI,CAAC8T,eAAe,CAAG,IAvkBiCxI,EA0kBxD,IAAMvI,EAAS,IAAI,CAAEgR,EAAa,IAAI,CAACnQ,KAAK,CAACoQ,eAAe,AAcxD,CAACD,EAAWE,IAAI,CAAC,AAACC,GAAMA,AAAW,kBAAXA,EAAElK,IAAI,GAC9B+J,EAAWrV,IAAI,CAf2D,WAC1E,IAAMyV,EAAa,EAAE,CAErB,GAAIpR,EAAOO,OAAO,CAAC8Q,UAAU,EACzB,CAACd,GAAMvQ,EAAOO,OAAO,CAAC8Q,UAAU,CAAC,CAAC,EAAE,CAACC,YAAY,CACjD,IAAK,IAAMC,KAASvR,EAAO6Q,KAAK,EAAI,EAAE,CAC9BU,EAAKC,SAAS,EACdJ,EAAWzV,IAAI,CAAC4V,EAAKC,SAAS,EAI1C,OAAOJ,CACX,EAKJ,CAOAK,oBAAqB,CACjB,IAAM5Q,EAAQ,IAAI,CAACA,KAAK,CAAEb,EAAS,IAAI,CAAEkG,EAAYrF,EAAMqF,SAAS,CAAEwL,EAAY7Q,EAAM6Q,SAAS,CAAE3L,EAAckK,GAAS,IAAI,CAACtL,MAAM,CAACgN,GAAG,CAAC,AAACC,GAAMA,EAAExR,IAAI,CAAC2J,SAAS,GAC7J8H,EAAOC,IAAUC,EAAO,CAACD,IAAUE,EAAOF,IAAUG,EAAO,CAACH,IAAUI,EAAW,EAAGC,EAAW,EAAGC,EAAW,EAAGC,EAAW,EAC/H,IAAI,CAAC1N,MAAM,CAACwC,OAAO,CAAC,AAAC3I,QAab6D,EAVJ,GAAI,IAAI,CAAC9B,OAAO,CAAC+R,SAAS,EAAI,CAAC9T,EAAMC,OAAO,CACxC,OAEJ,IAAM2B,EAAO5B,EAAM4B,IAAI,CAAEC,EAAQL,EAAOG,iBAAiB,CAAC3B,EAAM4B,IAAI,CAACC,KAAK,CAAC,EAAI,CAAC,EAAGkS,EAAgBnC,GAAsB,IAAI,CAAC7P,OAAO,CAACiS,MAAM,CAAEnS,EAAMmS,MAAM,CAAEhU,EAAM+B,OAAO,CAACiS,MAAM,EAAGvM,EAAYsM,EAAc9R,KAAK,EAAIuP,GAA6B,IAAI,CAAEjK,GAAcuJ,EAASgB,GAA+BiC,EAAcjD,MAAM,EAAI,EAAGlT,KAAKC,GAAG,CAAC6J,EAAWwL,IAAae,EAASF,EAAcE,MAAM,CAAEC,EAAY,AAACD,AAAW,WAAXA,GAAwBF,EAAc7R,MAAM,CAE/b4P,GAA+BiC,EAAc7R,MAAM,CAAEgR,GADrDpC,AAAS,EAATA,EACiEqD,EAAYF,AAAW,WAAXA,GAAwBxM,EAErGqK,GAA+BrK,EAAWC,GAD1CoJ,AAAS,EAATA,CAEJlP,CAAAA,EAAKuS,SAAS,CAAGA,EACjBvS,EAAKsS,SAAS,CAAGA,EAEbtS,EAAK2J,SAAS,EAAI8H,IAClBA,EAAOzR,EAAK2J,SAAS,CAErBoI,EAAW/V,KAAKwW,GAAG,CAACD,EADRJ,CAAAA,EAAclQ,SAAS,EAAI,CAAA,EACI8P,IAE3C/R,EAAK2J,SAAS,EAAIgI,IAClBA,EAAO3R,EAAK2J,SAAS,CAErBmI,EAAW9V,KAAKwW,GAAG,CAACD,EADRJ,CAAAA,EAAclQ,SAAS,EAAI,CAAA,EACI6P,IAE3C9R,EAAK0J,SAAS,EAAIkI,IAClBA,EAAO5R,EAAK0J,SAAS,CAErBuI,EAAWjW,KAAKwW,GAAG,CAACF,EADRH,CAAAA,EAAclQ,SAAS,EAAI,CAAA,EACIgQ,IAE3CjS,EAAK0J,SAAS,EAAImI,IAClBA,EAAO7R,EAAK0J,SAAS,CAErBsI,EAAWhW,KAAKwW,GAAG,CAACF,EADRH,CAAAA,EAAclQ,SAAS,EAAI,CAAA,EACI+P,GAEnD,GAGA,IAAMS,EAAKZ,IAASD,EAChB,EACA,AAACN,CAAAA,EAAY,AAACW,CAAAA,EAAWD,CAAO,EAAK,CAAA,EAAMH,CAAAA,EAAOD,CAAG,EAAIc,EAAKb,IAASD,EAAON,EAAY,EAAI,CAACmB,EAAKb,EAAOK,EAAW,EAAGU,EAAKhB,IAASF,EACvI,EACA,AAAC3L,CAAAA,EAAY,AAACgM,CAAAA,EAAWA,CAAO,EAAK,CAAA,EAAMH,CAAAA,EAAOF,CAAG,EAAImB,EAAKjB,IAASF,EAAO3L,EAAY,EAAI,CAAC6M,EAAKlB,EAAOM,EAAW,EAC1H,MAAO,CAAEY,GAAAA,EAAIC,GAAAA,EAAIH,GAAAA,EAAIC,GAAAA,CAAG,CAC5B,CACAG,UAAW,CACP,IAAMjT,EAAS,IAAI,CACb6Q,EAAQ,EAAE,CAyBhB,OAxBA,IAAI,CAACqC,IAAI,CAAC/L,OAAO,CAAC,AAAC3I,IACf,IAAM0B,EAAeF,EAAOG,iBAAiB,CAAC3B,EAAM4B,IAAI,CAACC,KAAK,EAAI,EAAE,EAAI,CAAC,EACzE,GAAI7B,EAAM4B,IAAI,CAAC/B,MAAM,CAAE,CACnB,IAAM8U,EAAe/C,GAAsBlQ,EAAc1B,EAAM+B,OAAO,EACtE,GAAI,CAAC/B,EAAMyE,YAAY,EAAIzE,EAAMyE,YAAY,CAACmQ,SAAS,CAAE,CACrD,IAAM7B,EAAO,IAAIvR,EAAOqT,SAAS,CAACrT,EAAQmT,EAAc,KAAK,EAAG3U,EAChEA,CAAAA,EAAMyE,YAAY,CAAGsO,CACzB,MAGI/S,EAAMf,SAAS,CAAG4S,GAAqB7R,EAAMf,SAAS,CAAE,AAAC,CAAA,IAAI,CAAC0C,iBAAiB,CAAC3B,EAAM4B,IAAI,CAACC,KAAK,CAAC,EAAI,CAAC,CAAA,EAAG5C,SAAS,EAClHe,EAAMyE,YAAY,CAACxE,OAAO,CACtBD,EAAMyE,YAAY,CAACmF,MAAM,CAAC3J,OAAO,AAEzCD,CAAAA,EAAMyE,YAAY,CAACqB,KAAK,CAAGuM,EAAMlV,IAAI,CAAC6C,EAAMyE,YAAY,EAAI,CAChE,MAEQzE,EAAMyE,YAAY,GAClBjD,EAAO6Q,KAAK,CAACyC,MAAM,CAAC9U,EAAMyE,YAAY,CAACqB,KAAK,EAC5C9F,EAAMyE,YAAY,CAACrB,OAAO,GAC1B,OAAOpD,EAAMyE,YAAY,CAGrC,GACO4N,CACX,CACA0C,UAAUrM,CAAE,CAAE5C,CAAK,CAAEjE,CAAK,CAAEmT,CAAI,CAAEnV,CAAM,CAAE,CACtC,IAAMG,EAAQ,IAAI,CAACmG,MAAM,CAACL,EAAM,CAEhC,OADAjE,EAAQ,AAAC7B,GAASA,EAAM6B,KAAK,EAAKA,EAC3B,KAAK,CAACkT,UAAUxY,IAAI,CAAC,IAAI,CAAEmM,EAAI5C,EAAOjE,EAAOmT,EAAMnV,EAC9D,CACAoV,eAAgB,CAGZ,MAAO,CAAC,CACZ,CACAC,mBAAmBtT,CAAI,CAAEN,CAAU,CAAE,CACjC,IAAMtB,EAAQ4B,EAAK5B,KAAK,CACpBA,IAEAA,EAAMf,SAAS,CAAG4S,GAAqB7R,EAAMf,SAAS,CAAE,AAAC,CAAA,IAAI,CAAC0C,iBAAiB,CAACC,EAAKC,KAAK,CAAC,EAAI,CAAC,CAAA,EAAG5C,SAAS,EAC5Ge,EAAMC,OAAO,CAAGqB,EAChBA,EAAaA,AAAe,CAAA,IAAfA,GAA+B,CAACtB,EAAMf,SAAS,EAEhE2C,EAAK/C,QAAQ,CAAC8J,OAAO,CAAC,AAAC6C,IACnB,IAAI,CAAC0J,kBAAkB,CAAC1J,EAAWlK,EACvC,EACJ,CACA6T,aAAc,CACV9D,GAAahV,SAAS,CAAC8Y,WAAW,CAAChU,KAAK,CAAC,IAAI,CAAE1C,WAC/C4S,GAAahV,SAAS,CAAC8Y,WAAW,CAAC5Y,IAAI,CAAC,IAAI,CAAE,IAAI,CAAC8V,KAAK,CAC5D,CAKA+C,WAAY,CACR,IAAqBrT,EAAUP,AAAhB,IAAI,CAAmBO,OAAO,CAEzCkH,EAASxD,EAAqBuD,YAAY,CAF/B,IAAI,EAEqCE,EAExDiI,EAAYiE,SAAS,CAAC7Y,IAAI,CAJX,IAAI,EAKnB,IAAM0L,EAAOzG,AALE,IAAI,CAKCyG,IAAI,CAAGzG,AALZ,IAAI,CAKe6T,OAAO,GACzCnM,EAAW1H,AANI,IAAI,CAMD8T,OAAO,CAACrM,EAAO,CAClB,KAAXA,GAAkB,AAACC,GAAaA,EAASrK,QAAQ,CAAC7B,MAAM,GACxDwE,AARW,IAAI,CAQR+T,WAAW,CAAC,GAAI,CAAA,GACvBtM,EAASzH,AATE,IAAI,CASC0H,QAAQ,CACxBA,EAAW1H,AAVA,IAAI,CAUG8T,OAAO,CAACrM,EAAO,EAErCzH,AAZe,IAAI,CAYZG,iBAAiB,CAAG4P,GAAgC,CACvDvK,KAAMkC,EAASrH,KAAK,CAAG,EACvBoF,OAAQlF,EAAQkF,MAAM,CACtBN,GAAIsB,EAAK/F,MAAM,CACf4E,SAAU,CACNO,gBAAiB7F,AAjBV,IAAI,CAiBaO,OAAO,CAACsF,eAAe,CAC/C1B,aAAc5D,EAAQ4D,YAAY,AACtC,CACJ,GACA,IAAI,CAACuP,kBAAkB,CAACjN,EAAM,CAAA,GAC9BzG,AAtBe,IAAI,CAsBZ6Q,KAAK,CAAG7Q,AAtBA,IAAI,CAsBGiT,QAAQ,GAC9BjT,AAvBe,IAAI,CAuBZwG,aAAa,CAACC,GACrB,IAAI,CAACsK,eAAe,CAACnI,kBAAkB,CAxBxB,IAAI,EAyBnB5I,AAzBe,IAAI,CAyBZgU,cAAc,CAAG,IAAI,CAACvC,kBAAkB,GAC/C,IAAI,CAAC9M,MAAM,CAACwC,OAAO,CAAC,AAAC3I,IACjB,IAAI,CAACyV,aAAa,CAACzV,EACvB,GACA,IAAI,CAACmG,MAAM,CAACwC,OAAO,CAAC,AAAC3I,IACbA,EAAMyE,YAAY,EAClB,IAAI,CAACiR,aAAa,CAAC1V,EAAMyE,YAAY,CAE7C,GACI,AAAC1C,EAAQ4D,YAAY,EACrBnE,AAnCW,IAAI,CAmCRmU,iBAAiB,CAACnU,AAnCd,IAAI,CAmCiByG,IAAI,CAE5C,CACAyN,cAAc3C,CAAI,CAAE,CAChB,IAAMpJ,EAAWoJ,EAAKpJ,QAAQ,CAAEC,EAASmJ,EAAKnJ,MAAM,CAAEgM,EAAY,IAAI,CAAC7T,OAAO,CAACgR,IAAI,EAAElP,WAAa,EAAGgS,EAAShE,GAAqB,IAAI,CAAC9P,OAAO,CAACgR,IAAI,EAAE+C,YAAa,IAAMC,EAAOlE,GAAqBkB,EAAKhR,OAAO,CAACgR,IAAI,EAAEgD,KAAM,IAAI,CAAChU,OAAO,CAACgR,IAAI,EAAEgD,KAAM,WACvP,GAAIpM,EAASlH,SAAS,EAAImH,EAAOnH,SAAS,CAAE,CACxC,IAAMuT,EAAiBrM,EAASlH,SAAS,CAACR,KAAK,EAAI,EAAIe,EAAW,IAAI,CAACX,KAAK,CAACW,QAAQ,CAAEzF,EAAKmU,GAAM,AAAC/H,CAAAA,EAASlH,SAAS,CAACvF,CAAC,EAAI,CAAA,EACvH,AAACyM,CAAAA,EAASlH,SAAS,CAACP,MAAM,EAAI,CAAA,EAAK,EAAG0T,GAAYnY,EAAKiU,GAAM,AAAC9H,CAAAA,EAAOnH,SAAS,CAACvF,CAAC,EAAI,CAAA,EACpF,AAAC0M,CAAAA,EAAOnH,SAAS,CAACP,MAAM,EAAI,CAAA,EAAK,EAAG0T,GACpCtY,EAAKoU,GAAM,AAAC/H,CAAAA,EAASlH,SAAS,CAACxF,CAAC,EAAI,CAAA,EAAK+Y,EAAeJ,GAAYpY,EAAKkU,GAAM9H,EAAOnH,SAAS,CAACxF,CAAC,EAAI,EAAG2Y,GACxG5S,IACA1F,GAAM0Y,EACNxY,GAAOoM,EAAOnH,SAAS,CAACR,KAAK,EAAI,GAErC,IAAMgU,EAAOrM,EAAOhI,IAAI,CAAC2J,SAAS,CAAG5B,EAAS/H,IAAI,CAAC2J,SAAS,AAC5DwH,CAAAA,EAAKmD,SAAS,CAAG,OACjB,IAAqDjU,EAAQ,AAA3CrE,CAAAA,KAAKE,GAAG,CAACN,EAAKF,GAAM0Y,CAAY,EAAwBC,EAAQD,CAElFjD,CAAAA,EAAKoD,KAAK,CADMzE,GAAM,AAAClU,CAAAA,EAAKF,CAAC,EAAK,EAAGsY,GAErC7C,EAAKqD,KAAK,CAAG3Y,EACbsV,EAAKtQ,SAAS,CAAG,CACb/G,EAAGkV,CAA2B,CAACmF,EAAK,CAAC,CACjCzY,GAAAA,EACAC,GAAAA,EACAC,GAAAA,EACAC,GAAAA,EACAwE,MAAAA,EACAiP,OAXkGjP,EAAQ4T,EAAU7S,CAAAA,EAAW,GAAK,CAAA,EAYpIA,SAAAA,EACA+N,cAAenH,EAAO3J,OAAO,CAC7B6Q,OAAQ,IAAI,CAAC/O,OAAO,CAACgR,IAAI,EAAEjC,MAC/B,EACJ,EACAiC,EAAKsD,KAAK,CAAG,CACTpZ,EAAG,AAACK,CAAAA,EAAKE,CAAC,EAAK,EACfN,EAAG,AAACK,CAAAA,EAAKE,CAAC,EAAK,EACfyE,OAAQ0T,EACR3T,MAAO,CACX,EACA8Q,EAAKuD,UAAU,CAAGtT,EAAW,CACzB,AAAC,CAAA,IAAI,CAACX,KAAK,CAAC6Q,SAAS,EAAI,CAAA,EAAKH,EAAKsD,KAAK,CAACnZ,CAAC,CAC1C,AAAC,CAAA,IAAI,CAACmF,KAAK,CAACqF,SAAS,EAAI,CAAA,EAAKqL,EAAKsD,KAAK,CAACpZ,CAAC,CAC7C,CAAG,CACA8V,EAAKsD,KAAK,CAACpZ,CAAC,CACZ8V,EAAKsD,KAAK,CAACnZ,CAAC,CACf,AACL,CACJ,CAKAqZ,eAAepQ,CAAM,CAAE,CACnB,IACIpE,EAASF,EADQF,EAAoBH,AAA1B,IAAI,CAA6BG,iBAAiB,CAEjE,IAAK,IAAM3B,KAASmG,EAAQ,CAUxB,GATAtE,EAAQF,CAAiB,CAAC3B,EAAM4B,IAAI,CAACC,KAAK,CAAC,CAE3CE,EAAU,CAAEK,MAAO,CAAC,CAAE,EAElBP,GAASA,EAAMgR,UAAU,GACzB9Q,EAAU6P,GAAsB7P,EAASF,EAAMgR,UAAU,EACzDrR,AATO,IAAI,CASJgV,aAAa,CAAG,IAAM,CAAA,GAG7BxW,EAAMyC,SAAS,EACfjB,AAbO,IAAI,CAaJO,OAAO,CAAC8Q,UAAU,CAAE,CAC3B,IAAM3O,EAAM,CAAC,EACT,CAAEjC,MAAAA,EAAQ,CAAC,CAAEC,OAAAA,EAAS,CAAC,CAAE,CAAGlC,EAAMyC,SAAS,AAC3CjB,CAhBG,IAAI,CAgBAa,KAAK,CAACW,QAAQ,EACrB,CAAA,CAACf,EAAOC,EAAO,CAAG,CAACA,EAAQD,EAAM,AAAD,EAEhC,AAAC8P,GAAMvQ,AAnBJ,IAAI,CAmBOO,OAAO,CAAC8Q,UAAU,CAAC,CAAC,EAAE,CAACzQ,KAAK,EAAEH,OAC5CiC,CAAAA,EAAIjC,KAAK,CAAG,CAAC,EAAEA,EAAM,EAAE,CAAC,AAAD,EAEvB,AAAC8P,GAAMvQ,AAtBJ,IAAI,CAsBOO,OAAO,CAAC8Q,UAAU,CAAC,CAAC,EAAE,CAACzQ,KAAK,EAAEqU,WAC5CvS,CAAAA,EAAIuS,SAAS,CAAG7Y,KAAK8Y,KAAK,CAACxU,EAAS,GAAE,EAE1CyP,GAAuB5P,EAAQK,KAAK,CAAE8B,GACtClE,EAAMgT,SAAS,EAAE9O,IAAIA,EACzB,CAEAlE,EAAM2W,SAAS,CAAG/E,GAAsB7P,EAAS/B,EAAM+B,OAAO,CAAC8Q,UAAU,CAC7E,CACA1B,EAAYyF,cAAc,CAACra,IAAI,CAAC,IAAI,CAAE4J,EAC1C,CAMA0Q,eAAe7W,CAAK,CAAEgT,CAAS,CAAE,CAC7B,IAAM/S,EAAUD,EAAMC,OAAO,AAE7BD,CAAAA,EAAMC,OAAO,CAAG,CAAA,EAChB,KAAK,CAAC4W,eAAe1V,KAAK,CAAC,IAAI,CAAE1C,WAEjCuU,EAAU3R,OAAO,CAAC,CACd8B,QAASlD,CAAAA,CAAAA,AAAY,CAAA,IAAZA,CAAgB,CAC7B,EAAG,KAAK,EAAG,WAEPA,GAAW+S,EAAU8D,IAAI,EAC7B,GAEA9W,EAAMC,OAAO,CAAGA,CACpB,CAKA2W,gBAAiB,CACT,IAAI,CAAC7U,OAAO,CAAC8Q,UAAU,GACvB,IAAI,CAAC9Q,OAAO,CAAC8Q,UAAU,CAAGd,GAAM,IAAI,CAAChQ,OAAO,CAAC8Q,UAAU,EAEvD,IAAI,CAAC0D,cAAc,CAAC,IAAI,CAACpQ,MAAM,EAE/BgL,EAAYyF,cAAc,CAACra,IAAI,CAAC,IAAI,CAAE,IAAI,CAAC8V,KAAK,EAExD,CACAjP,SAAU,CAEN,GAAI,IAAI,CAACiP,KAAK,CAAE,CACZ,IAAK,IAAMU,KAAQ,IAAI,CAACV,KAAK,CACzBU,EAAK3P,OAAO,EAEhB,CAAA,IAAI,CAACiP,KAAK,CAACrV,MAAM,CAAG,CACxB,CACA,OAAOmU,EAAY/N,OAAO,CAACjC,KAAK,CAAC,IAAI,CAAE1C,UAC3C,CAKAsY,aAAa/W,CAAK,CAAEwC,CAAK,CAAE,CACvB,IAAqBd,EAAe1B,GAChCwB,AADW,IAAI,CACRG,iBAAiB,CAAC3B,EAAM4B,IAAI,CAACC,KAAK,EAAI,EAAE,EAAI,CAAC,EAAGE,EAAU/B,GAASA,EAAM+B,OAAO,CAAEiV,EAAe,AAACtV,EAAamD,MAAM,EAC5HnD,EAAamD,MAAM,CAACrC,EAAM,EAC1B,CAAC,CACDxC,CAAAA,GACAA,CAAAA,EAAM+B,OAAO,CAACiS,MAAM,CAAGpC,GAAsBpQ,AALlC,IAAI,CAKqCO,OAAO,CAACiS,MAAM,CAAEtS,EAAasS,MAAM,CAAEhU,EAAM+B,OAAO,CAACiS,MAAM,CAAA,EAEjH,IAAMiD,EAAYpF,GAAqBmF,GAAgBA,EAAajE,IAAI,EAAIiE,EAAajE,IAAI,CAACxP,KAAK,CAAExB,GAAWA,EAAQgR,IAAI,EAAIhR,EAAQgR,IAAI,CAACxP,KAAK,CAAE7B,GAAgBA,EAAaqR,IAAI,EAAIrR,EAAaqR,IAAI,CAACxP,KAAK,CAAE/B,AAPnM,IAAI,CAOsMO,OAAO,CAACgR,IAAI,EAAIvR,AAP1N,IAAI,CAO6NO,OAAO,CAACgR,IAAI,CAACxP,KAAK,EAAG2T,EAAgBrF,GAAqBmF,GAAgBA,EAAajE,IAAI,EACvUiE,EAAajE,IAAI,CAAClP,SAAS,CAAE9B,GAAWA,EAAQgR,IAAI,EAAIhR,EAAQgR,IAAI,CAAClP,SAAS,CAAEnC,GAAgBA,EAAaqR,IAAI,EACjHrR,EAAaqR,IAAI,CAAClP,SAAS,CAAErC,AATlB,IAAI,CASqBO,OAAO,CAACgR,IAAI,EAAIvR,AATzC,IAAI,CAS4CO,OAAO,CAACgR,IAAI,CAAClP,SAAS,EAAGsT,EAAUhG,EAAY4F,YAAY,CAACxa,IAAI,CAThH,IAAI,CASqHyD,EAAOwC,GAW/I,OAVIxC,IACIA,EAAMgB,MAAM,GACZmW,EAAQxT,MAAM,CAAGsT,EACjBE,CAAO,CAAC,eAAe,CAAGD,EAC1B,OAAOC,EAAQ9T,IAAI,EAEnB,AAACrD,EAAMC,OAAO,EACdkX,CAAAA,EAAQhU,OAAO,CAAG,CAAA,GAGnBgU,CACX,CACAC,YAAa,CACThG,GAAc/U,SAAS,CAAC+a,UAAU,CAACjW,KAAK,CAAC,IAAI,CAAE1C,WAC/C4S,GAAahV,SAAS,CAAC+a,UAAU,CAAC7a,IAAI,CAAC,IAAI,CAAE,IAAI,CAAC8V,KAAK,CAC3D,CAKAoD,cAAczV,CAAK,CAAE,CACjB,IAAMqC,EAAQ,IAAI,CAACA,KAAK,CAAET,EAAO5B,EAAM4B,IAAI,CAAEsR,EAAY7Q,EAAM6Q,SAAS,CAAExL,EAAYrF,EAAMqF,SAAS,CAErG,CAAE6M,GAAAA,CAAE,CAAEC,GAAAA,CAAE,CAAEH,GAAAA,CAAE,CAAEC,GAAAA,CAAE,CAAE,CAAG,IAAI,CAACkB,cAAc,CAAEvY,EAAIsX,EAAK3S,EAAK2J,SAAS,CAAGiJ,EAAItX,EAAImX,EAAKzS,EAAK0J,SAAS,CAAGgJ,EAAIzS,EAAQ,IAAI,CAACF,iBAAiB,CAACC,EAAKC,KAAK,CAAC,EAAI,CAAC,EAAmGoS,EAASF,AAAzFnC,GAAsB,IAAI,CAAC7P,OAAO,CAACiS,MAAM,CAAEnS,EAAMmS,MAAM,CAAEhU,EAAM+B,OAAO,CAACiS,MAAM,EAA0BC,MAAM,CAAE/R,EAASN,EAAKsS,SAAS,CAAEjS,EAAQL,EAAKuS,SAAS,CAAEkD,EAAW,IAAI,CAACtV,OAAO,CAACsV,QAAQ,CAAEC,EAAQ1V,EAAK3E,CAAC,CAAIoF,EAAMW,QAAQ,CACtY0E,EAAYzF,EAAQ,EAAIhF,EACxBA,EAAIgF,EAAQ,EAAIsV,EAAQ3V,EAAK1E,CAAC,CAAI,AAACma,EAEnCna,EAAIgF,EAAS,EADbgR,EAAYhW,EAAIgF,EAAS,EACRsV,EAAe3F,GAAqB7R,EAAM+B,OAAO,CAACyV,YAAY,CAAE3V,EAAM2V,YAAY,CAAE,IAAI,CAACzV,OAAO,CAACyV,YAAY,EAAGC,EAAWnG,EAAO,CAAC2C,GAAU,SAAS,CAS3K,GARIwD,AAAa,KAAK,IAAlBA,GACAzX,EAAM0X,QAAQ,CAAG,CAAA,EACjB1X,EAAMkW,SAAS,CAAG,QAClBlW,EAAM2X,QAAQ,CAAG1D,EAAO2D,KAAK,CAAC,iBAAiB,CAAC,EAAE,EAGlD5X,EAAMkW,SAAS,CAAG,OAElB,CAAClW,EAAMC,OAAO,EAAID,EAAMyE,YAAY,CAAE,CACtC,IAAMpE,EAAaL,EAAMyE,YAAY,CAACkF,QAAQ,CAC9C,GAAItJ,EAAY,CACZ,GAAoD,CAAEpD,EAAAA,EAAI,CAAC,CAAEC,EAAAA,EAAI,CAAC,CAAE+E,MAAAA,EAAQ,CAAC,CAAEC,OAAAA,EAAS,CAAC,CAAE,CAAnE7B,EAAWoC,SAAS,EAAI,CAAC,CAC7C,AAACzC,CAAAA,EAAMyC,SAAS,EAChBzC,CAAAA,EAAMyC,SAAS,CAAG,CAAC,CAAA,EAEnB,AAACzC,EAAM0X,QAAQ,EACf/F,GAAuB3R,EAAMyC,SAAS,CAAE,CACpC/G,EAAG+b,EAASxa,EAAGC,EAAG+E,EAAOC,EAAQsV,EAAe,CAAE1a,EAAG0a,CAAa,EAAI,KAAK,EAC/E,GAEJ7F,GAAuB3R,EAAMyC,SAAS,CAAE,CAAExF,EAAAA,EAAGC,EAAAA,CAAE,GAC/C8C,EAAMmW,KAAK,CAAG9V,EAAW8V,KAAK,CAC9BnW,EAAMoW,KAAK,CAAG/V,EAAW+V,KAAK,AAClC,CACJ,MAEIpW,EAAMmW,KAAK,CAAGmB,EACdtX,EAAMoW,KAAK,CAAGmB,EACdvX,EAAMyC,SAAS,CAAG,CACdxF,EAAGqa,EACHpa,EAAGqa,EACHtV,MAAAA,EACAC,OAAAA,EACA2V,OAAQ,AAAC7X,EAAM4B,IAAI,CAACmH,MAAM,CAAe,UAAZ,SACjC,EACI,AAAC/I,EAAM0X,QAAQ,EACf1X,CAAAA,EAAMyC,SAAS,CAAC/G,CAAC,CAAG+b,EAASH,EAAOC,EAAOtV,EAAOC,EAAQsV,EAAe,CAAE1a,EAAG0a,CAAa,EAAI,KAAK,EAAC,CAI7GxX,CAAAA,EAAMsW,UAAU,CAAGjU,EAAMW,QAAQ,CAC7B,CAACkQ,EAAYqE,EAAQrV,EAAS,EAAGwF,EAAY4P,EAAQrV,EAAQ,EAAE,CAC/D,CAACqV,EAAQrV,EAAQ,EAAGsV,EAAM,AAClC,CACJ,CACAnF,GAAgB0F,cAAc,CAAGlG,GAAsBR,GAAc0G,cAAc,CA55BnD,CAa5BT,SAAU,CAAA,EAKVrD,OAAQ,CACJlD,OAAQ,GACRjN,UAAW,EACXoQ,OAAQ,SACR8D,YAAa,EACblT,OAAQ,CAAC,CACb,EACAkO,KAAM,CAkBFxP,MAAO,UAOPM,UAAW,EAOXiN,OAAQ,GACR+G,OAAQ,UAWR9B,KAAM,QACV,EAKA/T,eAAgB,CAMZO,YAAa,CAAA,EAIbI,QAAS,CAAA,EAITkB,UAAW,EAIX5G,EAAG,EAIHC,EAAG,EAIHgF,OAAQ,GAIRD,MAAO,GAIPE,MAAO,SAOPC,MAAO,CACHyV,OAAQ,UACRG,WAAY,OACZC,SAAU,KACd,CACJ,EAUAnE,UAAW,CAAA,EAKXoE,QAAS,CAeLC,WAAY,0CACZC,YAAa,YAUjB,EAUAvF,WAAY,CACRwF,MAAO,CAAA,EAePC,aAAc,CACVxL,WAAY,CACRE,YAAa,KACjB,CACJ,EACArK,QAAS,CAAA,EACT4V,cAAe,IAAM,GACrB7U,QAAS,EACTtB,MAAO,CACHoW,aAAc,MAClB,CACJ,EAoBAhR,aAAc,GAiBdC,UAAW,KAAK,CACpB,GAgrBAkK,GAAuBS,GAAgB/V,SAAS,CAAE,CAC9Coc,QAAS,CAAA,EACThY,WA3jD2DK,EA4jD3DzC,UAAWE,EACXsW,UAAWrL,EACXkP,YAAa,CAAA,CACjB,GACA1a,IAA0I2a,kBAAkB,CAAC,YAAavG,IAkH7I,IAAM3V,GAAkBE,IAG3C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}