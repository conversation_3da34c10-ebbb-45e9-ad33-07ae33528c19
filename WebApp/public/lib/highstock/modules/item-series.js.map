{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/item-series\n * @requires highcharts\n *\n * Item series type for Highcharts\n *\n * (c) 2019 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/item-series\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/item-series\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ item_series_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/Item/ItemPoint.js\n/* *\n *\n *  (c) 2019-2025 Torstein Honsi\n *\n *  Item series type for Highcharts\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { series: { prototype: { pointClass: Point } }, seriesTypes: { pie: { prototype: { pointClass: PiePoint } } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { extend } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass ItemPoint extends PiePoint {\n}\nextend(ItemPoint.prototype, {\n    haloPath: Point.prototype.haloPath\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Item_ItemPoint = (ItemPoint);\n\n;// ./code/es-modules/Core/Series/SeriesDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * General options for all series types.\n *\n * @optionparent plotOptions.series\n */\nconst seriesDefaults = {\n    // Base series options\n    /**\n     * The SVG value used for the `stroke-linecap` and `stroke-linejoin`\n     * of a line graph. Round means that lines are rounded in the ends and\n     * bends.\n     *\n     * @type       {Highcharts.SeriesLinecapValue}\n     * @default    round\n     * @since      3.0.7\n     * @apioption  plotOptions.line.linecap\n     */\n    /**\n     * Pixel width of the graph line.\n     *\n     * @see In styled mode, the line stroke-width can be set with the\n     *      `.highcharts-graph` class name.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-linewidth-general/\n     *         On all series\n     * @sample {highcharts} highcharts/plotoptions/series-linewidth-specific/\n     *         On one single series\n     *\n     * @product highcharts highstock\n     */\n    lineWidth: 2,\n    /**\n     * For some series, there is a limit that shuts down animation\n     * by default when the total number of points in the chart is too high.\n     * For example, for a column chart and its derivatives, animation does\n     * not run if there is more than 250 points totally. To disable this\n     * cap, set `animationLimit` to `Infinity`. This option works if animation\n     * is fired on individual points, not on a group of points like e.g. during\n     * the initial animation.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-animationlimit/\n     *         Animation limit on updating individual points\n     *\n     * @type      {number}\n     * @apioption plotOptions.series.animationLimit\n     */\n    /**\n     * Allow this series' points to be selected by clicking on the graphic\n     * (columns, point markers, pie slices, map areas etc).\n     *\n     * The selected points can be handled by point select and unselect\n     * events, or collectively by the [getSelectedPoints\n     * ](/class-reference/Highcharts.Chart#getSelectedPoints) function.\n     *\n     * And alternative way of selecting points is through dragging.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-allowpointselect-line/\n     *         Line\n     * @sample {highcharts} highcharts/plotoptions/series-allowpointselect-column/\n     *         Column\n     * @sample {highcharts} highcharts/plotoptions/series-allowpointselect-pie/\n     *         Pie\n     * @sample {highcharts} highcharts/chart/events-selection-points/\n     *         Select a range of points through a drag selection\n     * @sample {highmaps} maps/plotoptions/series-allowpointselect/\n     *         Map area\n     * @sample {highmaps} maps/plotoptions/mapbubble-allowpointselect/\n     *         Map bubble\n     *\n     * @since 1.2.0\n     *\n     * @private\n     */\n    allowPointSelect: false,\n    /**\n     * When true, each point or column edge is rounded to its nearest pixel\n     * in order to render sharp on screen. In some cases, when there are a\n     * lot of densely packed columns, this leads to visible difference\n     * in column widths or distance between columns. In these cases,\n     * setting `crisp` to `false` may look better, even though each column\n     * is rendered blurry.\n     *\n     * @sample {highcharts} highcharts/plotoptions/column-crisp-false/\n     *         Crisp is false\n     *\n     * @since   5.0.10\n     * @product highcharts highstock gantt\n     *\n     * @private\n     */\n    crisp: true,\n    /**\n     * If true, a checkbox is displayed next to the legend item to allow\n     * selecting the series. The state of the checkbox is determined by\n     * the `selected` option.\n     *\n     * @productdesc {highmaps}\n     * Note that if a `colorAxis` is defined, the color axis is represented\n     * in the legend, not the series.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-showcheckbox-true/\n     *         Show select box\n     *\n     * @since 1.2.0\n     *\n     * @private\n     */\n    showCheckbox: false,\n    /**\n     * Enable or disable the initial animation when a series is displayed.\n     * The animation can also be set as a configuration object. Please\n     * note that this option only applies to the initial animation of the\n     * series itself. For other animations, see [chart.animation](\n     * #chart.animation) and the animation parameter under the API methods.\n     * The following properties are supported:\n     *\n     * - `defer`: The animation delay time in milliseconds.\n     *\n     * - `duration`: The duration of the animation in milliseconds. (Defaults to\n     *   `1000`)\n     *\n     * - `easing`: Can be a string reference to an easing function set on\n     *   the `Math` object or a function. See the _Custom easing function_\n     *   demo below. (Defaults to `easeInOutSine`)\n     *\n     * Due to poor performance, animation is disabled in old IE browsers\n     * for several chart types.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-animation-disabled/\n     *         Animation disabled\n     * @sample {highcharts} highcharts/plotoptions/series-animation-slower/\n     *         Slower animation\n     * @sample {highcharts} highcharts/plotoptions/series-animation-easing/\n     *         Custom easing function\n     * @sample {highstock} stock/plotoptions/animation-slower/\n     *         Slower animation\n     * @sample {highstock} stock/plotoptions/animation-easing/\n     *         Custom easing function\n     * @sample {highmaps} maps/plotoptions/series-animation-true/\n     *         Animation enabled on map series\n     * @sample {highmaps} maps/plotoptions/mapbubble-animation-false/\n     *         Disabled on mapbubble series\n     *\n     * @type    {boolean|Highcharts.AnimationOptionsObject}\n     * @default {highcharts} true\n     * @default {highstock} true\n     * @default {highmaps} false\n     *\n     * @private\n     */\n    animation: {\n        /** @ignore-option */\n        duration: 1000\n    },\n    /**\n     * An additional class name to apply to the series' graphical elements.\n     * This option does not replace default class names of the graphical\n     * element. Changes to the series' color will also be reflected in a\n     * chart's legend and tooltip.\n     *\n     * @sample {highcharts} highcharts/css/point-series-classname\n     *         Series and point class name\n     *\n     * @type      {string}\n     * @since     5.0.0\n     * @apioption plotOptions.series.className\n     */\n    /**\n     * Disable this option to allow series rendering in the whole plotting\n     * area.\n     *\n     * **Note:** Clipping should be always enabled when\n     * [chart.zoomType](#chart.zoomType) is set\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-clip/\n     *         Disabled clipping\n     *\n     * @default   true\n     * @type      {boolean}\n     * @since     3.0.0\n     * @apioption plotOptions.series.clip\n     */\n    /**\n     * The main color of the series. In line type series it applies to the\n     * line and the point markers unless otherwise specified. In bar type\n     * series it applies to the bars unless a color is specified per point.\n     * The default value is pulled from the `options.colors` array.\n     *\n     * In styled mode, the color can be defined by the\n     * [colorIndex](#plotOptions.series.colorIndex) option. Also, the series\n     * color can be set with the `.highcharts-series`,\n     * `.highcharts-color-{n}`, `.highcharts-{type}-series` or\n     * `.highcharts-series-{n}` class, or individual classes given by the\n     * `className` option.\n     *\n     * @productdesc {highmaps}\n     * In maps, the series color is rarely used, as most choropleth maps use\n     * the color to denote the value of each point. The series color can\n     * however be used in a map with multiple series holding categorized\n     * data.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-color-general/\n     *         General plot option\n     * @sample {highcharts} highcharts/plotoptions/series-color-specific/\n     *         One specific series\n     * @sample {highcharts} highcharts/plotoptions/series-color-area/\n     *         Area color\n     * @sample {highcharts} highcharts/series/infographic/\n     *         Pattern fill\n     * @sample {highmaps} maps/demo/category-map/\n     *         Category map by multiple series\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @apioption plotOptions.series.color\n     */\n    /**\n     * Styled mode only. A specific color index to use for the series, so its\n     * graphic representations are given the class name `highcharts-color-{n}`.\n     *\n     * Since v11, CSS variables on the form `--highcharts-color-{n}` make\n     * changing the color scheme very convenient.\n     *\n     * @sample    {highcharts} highcharts/css/colorindex/ Series and point color\n     *            index\n     *\n     * @type      {number}\n     * @since     5.0.0\n     * @apioption plotOptions.series.colorIndex\n     */\n    /**\n     * Whether to connect a graph line across null points, or render a gap\n     * between the two points on either side of the null.\n     *\n     * In stacked area chart, if `connectNulls` is set to true,\n     * null points are interpreted as 0.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-connectnulls-false/\n     *         False by default\n     * @sample {highcharts} highcharts/plotoptions/series-connectnulls-true/\n     *         True\n     *\n     * @type      {boolean}\n     * @default   false\n     * @product   highcharts highstock\n     * @apioption plotOptions.series.connectNulls\n     */\n    /**\n     * You can set the cursor to \"pointer\" if you have click events attached\n     * to the series, to signal to the user that the points and lines can\n     * be clicked.\n     *\n     * In styled mode, the series cursor can be set with the same classes\n     * as listed under [series.color](#plotOptions.series.color).\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-cursor-line/\n     *         On line graph\n     * @sample {highcharts} highcharts/plotoptions/series-cursor-column/\n     *         On columns\n     * @sample {highcharts} highcharts/plotoptions/series-cursor-scatter/\n     *         On scatter markers\n     * @sample {highstock} stock/plotoptions/cursor/\n     *         Pointer on a line graph\n     * @sample {highmaps} maps/plotoptions/series-allowpointselect/\n     *         Map area\n     * @sample {highmaps} maps/plotoptions/mapbubble-allowpointselect/\n     *         Map bubble\n     *\n     * @type      {string|Highcharts.CursorValue}\n     * @apioption plotOptions.series.cursor\n     */\n    /**\n     * A reserved subspace to store options and values for customized\n     * functionality. Here you can add additional data for your own event\n     * callbacks and formatter callbacks.\n     *\n     * @sample {highcharts} highcharts/point/custom/\n     *         Point and series with custom data\n     *\n     * @type      {Highcharts.Dictionary<*>}\n     * @apioption plotOptions.series.custom\n     */\n    /**\n     * Name of the dash style to use for the graph, or for some series types\n     * the outline of each shape.\n     *\n     * In styled mode, the\n     * [stroke dash-array](https://jsfiddle.net/gh/get/library/pure/highcharts/highcharts/tree/master/samples/highcharts/css/series-dashstyle/)\n     * can be set with the same classes as listed under\n     * [series.color](#plotOptions.series.color).\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-dashstyle-all/\n     *         Possible values demonstrated\n     * @sample {highcharts} highcharts/plotoptions/series-dashstyle/\n     *         Chart suitable for printing in black and white\n     * @sample {highstock} highcharts/plotoptions/series-dashstyle-all/\n     *         Possible values demonstrated\n     * @sample {highmaps} highcharts/plotoptions/series-dashstyle-all/\n     *         Possible values demonstrated\n     * @sample {highmaps} maps/plotoptions/series-dashstyle/\n     *         Dotted borders on a map\n     *\n     * @type      {Highcharts.DashStyleValue}\n     * @default   Solid\n     * @since     2.1\n     * @apioption plotOptions.series.dashStyle\n     */\n    /**\n     * A description of the series to add to the screen reader information\n     * about the series.\n     *\n     * @type      {string}\n     * @since     5.0.0\n     * @requires  modules/accessibility\n     * @apioption plotOptions.series.description\n     */\n    /**\n     * Options for the series data sorting.\n     *\n     * @type      {Highcharts.DataSortingOptionsObject}\n     * @since     8.0.0\n     * @product   highcharts highstock\n     * @apioption plotOptions.series.dataSorting\n     */\n    /**\n     * Enable or disable data sorting for the series. Use [xAxis.reversed](\n     * #xAxis.reversed) to change the sorting order.\n     *\n     * @sample {highcharts} highcharts/datasorting/animation/\n     *         Data sorting in scatter-3d\n     * @sample {highcharts} highcharts/datasorting/labels-animation/\n     *         Axis labels animation\n     * @sample {highcharts} highcharts/datasorting/dependent-sorting/\n     *         Dependent series sorting\n     * @sample {highcharts} highcharts/datasorting/independent-sorting/\n     *         Independent series sorting\n     *\n     * @type      {boolean}\n     * @since     8.0.0\n     * @apioption plotOptions.series.dataSorting.enabled\n     */\n    /**\n     * Whether to allow matching points by name in an update. If this option\n     * is disabled, points will be matched by order.\n     *\n     * @sample {highcharts} highcharts/datasorting/match-by-name/\n     *         Enabled match by name\n     *\n     * @type      {boolean}\n     * @since     8.0.0\n     * @apioption plotOptions.series.dataSorting.matchByName\n     */\n    /**\n     * Determines what data value should be used to sort by.\n     *\n     * @sample {highcharts} highcharts/datasorting/sort-key/\n     *         Sort key as `z` value\n     *\n     * @type      {string}\n     * @since     8.0.0\n     * @default   y\n     * @apioption plotOptions.series.dataSorting.sortKey\n     */\n    /**\n     * Enable or disable the mouse tracking for a specific series. This\n     * includes point tooltips and click events on graphs and points. For\n     * large datasets it improves performance.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-enablemousetracking-false/\n     *         No mouse tracking\n     * @sample {highmaps} maps/plotoptions/series-enablemousetracking-false/\n     *         No mouse tracking\n     *\n     * @type      {boolean}\n     * @default   true\n     * @apioption plotOptions.series.enableMouseTracking\n     */\n    enableMouseTracking: true,\n    /**\n     * Whether to use the Y extremes of the total chart width or only the\n     * zoomed area when zooming in on parts of the X axis. By default, the\n     * Y axis adjusts to the min and max of the visible data. Cartesian\n     * series only.\n     *\n     * @type      {boolean}\n     * @default   false\n     * @since     4.1.6\n     * @product   highcharts highstock gantt\n     * @apioption plotOptions.series.getExtremesFromAll\n     */\n    /**\n     * Highlight only the hovered point and fade the remaining points.\n     *\n     * Scatter-type series require enabling the 'inactive' marker state and\n     * adjusting opacity. Note that this approach could affect performance\n     * with large datasets.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-inactiveotherpoints-enabled/\n     *         Chart with inactiveOtherPoints option enabled.\n     *\n     * @type      {boolean}\n     * @default   false\n     * @apioption plotOptions.series.inactiveOtherPoints\n     */\n    /**\n     * An array specifying which option maps to which key in the data point\n     * array. This makes it convenient to work with unstructured data arrays\n     * from different sources.\n     *\n     * @see [series.data](#series.line.data)\n     *\n     * @sample {highcharts|highstock} highcharts/series/data-keys/\n     *         An extended data array with keys\n     * @sample {highcharts|highstock} highcharts/series/data-nested-keys/\n     *         Nested keys used to access object properties\n     *\n     * @type      {Array<string>}\n     * @since     4.1.6\n     * @apioption plotOptions.series.keys\n     */\n    /**\n     * The line cap used for line ends and line joins on the graph.\n     *\n     * @sample highcharts/series-line/linecap/\n     *         Line cap comparison\n     *\n     * @type       {Highcharts.SeriesLinecapValue}\n     * @default    round\n     * @product    highcharts highstock\n     * @apioption  plotOptions.series.linecap\n     */\n    /**\n     * The [id](#series.id) of another series to link to. Additionally,\n     * the value can be \":previous\" to link to the previous series. When\n     * two series are linked, only the first one appears in the legend.\n     * Toggling the visibility of this also toggles the linked series.\n     *\n     * If master series uses data sorting and linked series does not have\n     * its own sorting definition, the linked series will be sorted in the\n     * same order as the master one.\n     *\n     * @sample {highcharts|highstock} highcharts/demo/arearange-line/\n     *         Linked series\n     *\n     * @type      {string}\n     * @since     3.0\n     * @product   highcharts highstock gantt\n     * @apioption plotOptions.series.linkedTo\n     */\n    /**\n     * Options for the corresponding navigator series if `showInNavigator`\n     * is `true` for this series. Available options are the same as any\n     * series, documented at [plotOptions](#plotOptions.series) and\n     * [series](#series).\n     *\n     * These options are merged with options in [navigator.series](\n     * #navigator.series), and will take precedence if the same option is\n     * defined both places.\n     *\n     * @see [navigator.series](#navigator.series)\n     *\n     * @type      {Highcharts.PlotSeriesOptions}\n     * @since     5.0.0\n     * @product   highstock\n     * @apioption plotOptions.series.navigatorOptions\n     */\n    /**\n     * The color for the parts of the graph or points that are below the\n     * [threshold](#plotOptions.series.threshold). Note that `zones` takes\n     * precedence over the negative color. Using `negativeColor` is\n     * equivalent to applying a zone with value of 0.\n     *\n     * @see In styled mode, a negative color is applied by setting this option\n     *      to `true` combined with the `.highcharts-negative` class name.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-negative-color/\n     *         Spline, area and column\n     * @sample {highcharts} highcharts/plotoptions/arearange-negativecolor/\n     *         Arearange\n     * @sample {highcharts} highcharts/css/series-negative-color/\n     *         Styled mode\n     * @sample {highstock} highcharts/plotoptions/series-negative-color/\n     *         Spline, area and column\n     * @sample {highstock} highcharts/plotoptions/arearange-negativecolor/\n     *         Arearange\n     * @sample {highmaps} highcharts/plotoptions/series-negative-color/\n     *         Spline, area and column\n     * @sample {highmaps} highcharts/plotoptions/arearange-negativecolor/\n     *         Arearange\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @since     3.0\n     * @apioption plotOptions.series.negativeColor\n     */\n    /**\n     * Whether or not data-points with the value of `null` should be interactive.\n     * When this is set to `true`, tooltips may highlight these points, and this\n     * option also enables keyboard navigation for such points. Format options\n     * for such points include [`nullFormat`](#tooltip.nullFormat) and [`nullFormater`](#tooltip.nullFormatter).\n     * Works for these series: `line`, `spline`, `area`, `area-spline`,\n     * `column`, `bar`, and* `timeline`.\n     *\n     * @sample {highcharts} highcharts/series/null-interaction/\n     *         Chart with interactive `null` points\n     *\n     * @sample {highcharts} highcharts/series-timeline/null-interaction/\n     *         Timeline series with `null` points\n     *\n     * @type      {boolean|undefined}\n     * @product   highcharts highstock\n     * @apioption plotOptions.series.nullInteraction\n     */\n    /**\n     * Same as\n     * [accessibility.point.descriptionFormat](#accessibility.point.descriptionFormat),\n     * but for an individual series. Overrides the chart wide configuration.\n     *\n     * @type      {Function}\n     * @since 11.1.0\n     * @apioption plotOptions.series.pointDescriptionFormat\n     */\n    /**\n     * Same as\n     * [accessibility.series.descriptionFormatter](#accessibility.series.descriptionFormatter),\n     * but for an individual series. Overrides the chart wide configuration.\n     *\n     * @type      {Function}\n     * @since     5.0.12\n     * @apioption plotOptions.series.pointDescriptionFormatter\n     */\n    /**\n     * If no x values are given for the points in a series, `pointInterval`\n     * defines the interval of the x values. For example, if a series\n     * contains one value every decade starting from year 0, set\n     * `pointInterval` to `10`. In true `datetime` axes, the `pointInterval`\n     * is set in milliseconds.\n     *\n     * It can be also be combined with `pointIntervalUnit` to draw irregular\n     * time intervals.\n     *\n     * If combined with `relativeXValue`, an x value can be set on each\n     * point, and the `pointInterval` is added x times to the `pointStart`\n     * setting.\n     *\n     * Please note that this options applies to the _series data_, not the\n     * interval of the axis ticks, which is independent.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-pointstart-datetime/\n     *         Datetime X axis\n     * @sample {highcharts} highcharts/plotoptions/series-relativexvalue/\n     *         Relative x value\n     * @sample {highstock} stock/plotoptions/pointinterval-pointstart/\n     *         Using pointStart and pointInterval\n     * @sample {highstock} stock/plotoptions/relativexvalue/\n     *         Relative x value\n     *\n     * @type      {number}\n     * @default   1\n     * @product   highcharts highstock gantt\n     * @apioption plotOptions.series.pointInterval\n     */\n    /**\n     * On datetime series, this allows for setting the\n     * [pointInterval](#plotOptions.series.pointInterval) to irregular time\n     * units, `day`, `month` and `year`. A day is usually the same as 24\n     * hours, but `pointIntervalUnit` also takes the DST crossover into\n     * consideration when dealing with local time. Combine this option with\n     * `pointInterval` to draw weeks, quarters, 6 months, 10 years etc.\n     *\n     * Please note that this options applies to the _series data_, not the\n     * interval of the axis ticks, which is independent.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-pointintervalunit/\n     *         One point a month\n     * @sample {highstock} highcharts/plotoptions/series-pointintervalunit/\n     *         One point a month\n     *\n     * @type       {string}\n     * @since      4.1.0\n     * @product    highcharts highstock gantt\n     * @validvalue [\"day\", \"month\", \"year\"]\n     * @apioption  plotOptions.series.pointIntervalUnit\n     */\n    /**\n     * Possible values: `\"on\"`, `\"between\"`, `number`.\n     *\n     * In a column chart, when pointPlacement is `\"on\"`, the point will not\n     * create any padding of the X axis. In a polar column chart this means\n     * that the first column points directly north. If the pointPlacement is\n     * `\"between\"`, the columns will be laid out between ticks. This is\n     * useful for example for visualising an amount between two points in\n     * time or in a certain sector of a polar chart.\n     *\n     * Since Highcharts 3.0.2, the point placement can also be numeric,\n     * where 0 is on the axis value, -0.5 is between this value and the\n     * previous, and 0.5 is between this value and the next. Unlike the\n     * textual options, numeric point placement options won't affect axis\n     * padding.\n     *\n     * Note that pointPlacement needs a [pointRange](\n     * #plotOptions.series.pointRange) to work. For column series this is\n     * computed, but for line-type series it needs to be set.\n     *\n     * For the `xrange` series type and gantt charts, if the Y axis is a\n     * category axis, the `pointPlacement` applies to the Y axis rather than\n     * the (typically datetime) X axis.\n     *\n     * Defaults to `undefined` in cartesian charts, `\"between\"` in polar\n     * charts.\n     *\n     * @see [xAxis.tickmarkPlacement](#xAxis.tickmarkPlacement)\n     *\n     * @sample {highcharts|highstock} highcharts/plotoptions/series-pointplacement-between/\n     *         Between in a column chart\n     * @sample {highcharts|highstock} highcharts/plotoptions/series-pointplacement-numeric/\n     *         Numeric placement for custom layout\n     * @sample {highcharts|highstock} maps/plotoptions/heatmap-pointplacement/\n     *         Placement in heatmap\n     *\n     * @type      {string|number}\n     * @since     2.3.0\n     * @product   highcharts highstock gantt\n     * @apioption plotOptions.series.pointPlacement\n     */\n    /**\n     * If no x values are given for the points in a series, `pointStart`\n     * defines on what value to start. For example, if a series contains one\n     * yearly value starting from 1945, set `pointStart` to 1945.\n     *\n     * The `pointStart` setting can be a number, or a datetime string that is\n     * parsed according to the `time.timezone` setting.\n     *\n     * If combined with `relativeXValue`, an x value can be set on each\n     * point. The x value from the point options is multiplied by\n     * `pointInterval` and added to `pointStart` to produce a modified x\n     * value.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-pointstart-linear/\n     *         Linear\n     * @sample {highcharts} highcharts/plotoptions/series-pointstart-datetime/\n     *         Datetime\n     * @sample {highcharts} highcharts/plotoptions/series-relativexvalue/\n     *         Relative x value\n     * @sample {highstock} stock/plotoptions/pointinterval-pointstart/\n     *         Using pointStart and pointInterval\n     * @sample {highstock} stock/plotoptions/relativexvalue/\n     *         Relative x value\n     *\n     * @type      {number|string}\n     * @default   0\n     * @product   highcharts highstock gantt\n     * @apioption plotOptions.series.pointStart\n     */\n    /**\n     * When true, X values in the data set are relative to the current\n     * `pointStart`, `pointInterval` and `pointIntervalUnit` settings. This\n     * allows compression of the data for datasets with irregular X values.\n     *\n     * The real X values are computed on the formula `f(x) = ax + b`, where\n     * `a` is the `pointInterval` (optionally with a time unit given by\n     * `pointIntervalUnit`), and `b` is the `pointStart`.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-relativexvalue/\n     *         Relative X value\n     * @sample {highstock} stock/plotoptions/relativexvalue/\n     *         Relative X value\n     *\n     * @type      {boolean}\n     * @default   false\n     * @product   highcharts highstock\n     * @apioption plotOptions.series.relativeXValue\n     */\n    /**\n     * Whether to select the series initially. If `showCheckbox` is true,\n     * the checkbox next to the series name in the legend will be checked\n     * for a selected series.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-selected/\n     *         One out of two series selected\n     *\n     * @type      {boolean}\n     * @default   false\n     * @since     1.2.0\n     * @apioption plotOptions.series.selected\n     */\n    /**\n     * Whether to apply a drop shadow to the graph line. Since 2.3 the\n     * shadow can be an object configuration containing `color`, `offsetX`,\n     * `offsetY`, `opacity` and `width`.\n     *\n     * Note that in some cases, like stacked columns or other dense layouts, the\n     * series may cast shadows on each other. In that case, the\n     * `chart.seriesGroupShadow` allows applying a common drop shadow to the\n     * whole series group.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-shadow/\n     *         Shadow enabled\n     *\n     * @type      {boolean|Highcharts.ShadowOptionsObject}\n     * @default   false\n     * @apioption plotOptions.series.shadow\n     */\n    /**\n     * Whether to display this particular series or series type in the\n     * legend. Standalone series are shown in legend by default, and linked\n     * series are not. Since v7.2.0 it is possible to show series that use\n     * colorAxis by setting this option to `true`.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-showinlegend/\n     *         One series in the legend, one hidden\n     *\n     * @type      {boolean}\n     * @apioption plotOptions.series.showInLegend\n     */\n    /**\n     * Whether or not to show the series in the navigator. Takes precedence\n     * over [navigator.baseSeries](#navigator.baseSeries) if defined.\n     *\n     * @type      {boolean}\n     * @since     5.0.0\n     * @product   highstock\n     * @apioption plotOptions.series.showInNavigator\n     */\n    /**\n     * If set to `true`, the accessibility module will skip past the points\n     * in this series for keyboard navigation.\n     *\n     * @type      {boolean}\n     * @since     5.0.12\n     * @apioption plotOptions.series.skipKeyboardNavigation\n     */\n    /**\n     * Whether to stack the values of each series on top of each other.\n     * Possible values are `undefined` to disable, `\"normal\"` to stack by\n     * value or `\"percent\"`.\n     *\n     * When stacking is enabled, data must be sorted\n     * in ascending X order.\n     *\n     * Some stacking options are related to specific series types. In the\n     * streamgraph series type, the stacking option is set to `\"stream\"`.\n     * The second one is `\"overlap\"`, which only applies to waterfall\n     * series.\n     *\n     * @see [yAxis.reversedStacks](#yAxis.reversedStacks)\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-stacking-line/\n     *         Line\n     * @sample {highcharts} highcharts/plotoptions/series-stacking-column/\n     *         Column\n     * @sample {highcharts} highcharts/plotoptions/series-stacking-bar/\n     *         Bar\n     * @sample {highcharts} highcharts/plotoptions/series-stacking-area/\n     *         Area\n     * @sample {highcharts} highcharts/plotoptions/series-stacking-percent-line/\n     *         Line\n     * @sample {highcharts} highcharts/plotoptions/series-stacking-percent-column/\n     *         Column\n     * @sample {highcharts} highcharts/plotoptions/series-stacking-percent-bar/\n     *         Bar\n     * @sample {highcharts} highcharts/plotoptions/series-stacking-percent-area/\n     *         Area\n     * @sample {highcharts} highcharts/plotoptions/series-waterfall-with-normal-stacking\n     *         Waterfall with normal stacking\n     * @sample {highcharts} highcharts/plotoptions/series-waterfall-with-overlap-stacking\n     *         Waterfall with overlap stacking\n     * @sample {highstock} stock/plotoptions/stacking/\n     *         Area\n     *\n     * @type       {string}\n     * @product    highcharts highstock\n     * @validvalue [\"normal\", \"overlap\", \"percent\", \"stream\"]\n     * @apioption  plotOptions.series.stacking\n     */\n    /**\n     * Whether to apply steps to the line. Possible values are `left`,\n     * `center` and `right`.\n     *\n     * @sample {highcharts} highcharts/plotoptions/line-step/\n     *         Different step line options\n     * @sample {highcharts} highcharts/plotoptions/area-step/\n     *         Stepped, stacked area\n     * @sample {highstock} stock/plotoptions/line-step/\n     *         Step line\n     *\n     * @type       {string}\n     * @since      1.2.5\n     * @product    highcharts highstock\n     * @validvalue [\"left\", \"center\", \"right\"]\n     * @apioption  plotOptions.series.step\n     */\n    /**\n     * The threshold, also called zero level or base level. For line type\n     * series this is only used in conjunction with\n     * [negativeColor](#plotOptions.series.negativeColor).\n     *\n     * @see [softThreshold](#plotOptions.series.softThreshold).\n     *\n     * @type      {number|null}\n     * @default   0\n     * @since     3.0\n     * @product   highcharts highstock\n     * @apioption plotOptions.series.threshold\n     */\n    /**\n     * Set the initial visibility of the series.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-visible/\n     *         Two series, one hidden and one visible\n     * @sample {highstock} stock/plotoptions/series-visibility/\n     *         Hidden series\n     *\n     * @type      {boolean}\n     * @default   true\n     * @apioption plotOptions.series.visible\n     */\n    /**\n     * Defines the Axis on which the zones are applied.\n     *\n     * @see [zones](#plotOptions.series.zones)\n     *\n     * @sample {highcharts} highcharts/series/color-zones-zoneaxis-x/\n     *         Zones on the X-Axis\n     * @sample {highstock} highcharts/series/color-zones-zoneaxis-x/\n     *         Zones on the X-Axis\n     *\n     * @type      {string}\n     * @default   y\n     * @since     4.1.0\n     * @product   highcharts highstock\n     * @apioption plotOptions.series.zoneAxis\n     */\n    /**\n     * General event handlers for the series items. These event hooks can\n     * also be attached to the series at run time using the\n     * `Highcharts.addEvent` function.\n     *\n     * @declare Highcharts.SeriesEventsOptionsObject\n     *\n     * @private\n     */\n    events: {},\n    /**\n     * Fires after the series has finished its initial animation, or in case\n     * animation is disabled, immediately as the series is displayed.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-events-afteranimate/\n     *         Show label after animate\n     * @sample {highstock} highcharts/plotoptions/series-events-afteranimate/\n     *         Show label after animate\n     *\n     * @type      {Highcharts.SeriesAfterAnimateCallbackFunction}\n     * @since     4.0\n     * @product   highcharts highstock gantt\n     * @context   Highcharts.Series\n     * @apioption plotOptions.series.events.afterAnimate\n     */\n    /**\n     * Fires when the checkbox next to the series' name in the legend is\n     * clicked. One parameter, `event`, is passed to the function. The state\n     * of the checkbox is found by `event.checked`. The checked item is\n     * found by `event.item`. Return `false` to prevent the default action\n     * which is to toggle the select state of the series.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-events-checkboxclick/\n     *         Alert checkbox status\n     *\n     * @type      {Highcharts.SeriesCheckboxClickCallbackFunction}\n     * @since     1.2.0\n     * @context   Highcharts.Series\n     * @apioption plotOptions.series.events.checkboxClick\n     */\n    /**\n     * Fires when the series is clicked. One parameter, `event`, is passed\n     * to the function, containing common event information. Additionally,\n     * `event.point` holds a pointer to the nearest point on the graph.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-events-click/\n     *         Alert click info\n     * @sample {highstock} stock/plotoptions/series-events-click/\n     *         Alert click info\n     * @sample {highmaps} maps/plotoptions/series-events-click/\n     *         Display click info in subtitle\n     *\n     * @type      {Highcharts.SeriesClickCallbackFunction}\n     * @context   Highcharts.Series\n     * @apioption plotOptions.series.events.click\n     */\n    /**\n     * Fires when the series is hidden after chart generation time, either\n     * by clicking the legend item or by calling `.hide()`.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-events-hide/\n     *         Alert when the series is hidden by clicking the legend item\n     *\n     * @type      {Highcharts.SeriesHideCallbackFunction}\n     * @since     1.2.0\n     * @context   Highcharts.Series\n     * @apioption plotOptions.series.events.hide\n     */\n    /**\n     * Fires when the legend item belonging to the series is clicked. One\n     * parameter, `event`, is passed to the function. The default action\n     * is to toggle the visibility of the series. This can be prevented\n     * by returning `false` or calling `event.preventDefault()`.\n     *\n     * **Note:** This option is deprecated in favor of\n     * [legend.events.itemClick](#legend.events.itemClick).\n     *\n     * @type       {Highcharts.SeriesLegendItemClickCallbackFunction}\n     * @deprecated 11.4.4\n     * @context    Highcharts.Series\n     * @apioption  plotOptions.series.events.legendItemClick\n     */\n    /**\n     * Fires when the mouse leaves the graph. One parameter, `event`, is\n     * passed to the function, containing common event information. If the\n     * [stickyTracking](#plotOptions.series) option is true, `mouseOut`\n     * doesn't happen before the mouse enters another graph or leaves the\n     * plot area.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-events-mouseover-sticky/\n     *         With sticky tracking by default\n     * @sample {highcharts} highcharts/plotoptions/series-events-mouseover-no-sticky/\n     *         Without sticky tracking\n     *\n     * @type      {Highcharts.SeriesMouseOutCallbackFunction}\n     * @context   Highcharts.Series\n     * @apioption plotOptions.series.events.mouseOut\n     */\n    /**\n     * Fires when the mouse enters the graph. One parameter, `event`, is\n     * passed to the function, containing common event information.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-events-mouseover-sticky/\n     *         With sticky tracking by default\n     * @sample {highcharts} highcharts/plotoptions/series-events-mouseover-no-sticky/\n     *         Without sticky tracking\n     *\n     * @type      {Highcharts.SeriesMouseOverCallbackFunction}\n     * @context   Highcharts.Series\n     * @apioption plotOptions.series.events.mouseOver\n     */\n    /**\n     * Fires when the series is shown after chart generation time, either\n     * by clicking the legend item or by calling `.show()`.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-events-show/\n     *         Alert when the series is shown by clicking the legend item.\n     *\n     * @type      {Highcharts.SeriesShowCallbackFunction}\n     * @since     1.2.0\n     * @context   Highcharts.Series\n     * @apioption plotOptions.series.events.show\n     */\n    /**\n     * Options for the point markers of line and scatter-like series. Properties\n     * like `fillColor`, `lineColor` and `lineWidth` define the visual\n     * appearance of the markers. The `symbol` option defines the shape. Other\n     * series types, like column series, don't have markers, but have visual\n     * options on the series level instead.\n     *\n     * In styled mode, the markers can be styled with the `.highcharts-point`,\n     * `.highcharts-point-hover` and `.highcharts-point-select` class names.\n     *\n     * @declare Highcharts.PointMarkerOptionsObject\n     *\n     * @sample {highmaps} maps/demo/mappoint-mapmarker\n     *         Using the mapmarker symbol for points\n     *\n     * @private\n     */\n    marker: {\n        /**\n         * Enable or disable the point marker. If `undefined`, the markers\n         * are hidden when the data is dense, and shown for more widespread\n         * data points.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-marker-enabled/\n         *         Disabled markers\n         * @sample {highcharts} highcharts/plotoptions/series-marker-enabled-false/\n         *         Disabled in normal state but enabled on hover\n         * @sample {highstock} stock/plotoptions/series-marker/\n         *         Enabled markers\n         *\n         * @type      {boolean}\n         * @default   {highcharts} undefined\n         * @default   {highstock} false\n         * @apioption plotOptions.series.marker.enabled\n         */\n        /**\n         * The threshold for how dense the point markers should be before\n         * they are hidden, given that `enabled` is not defined. The number\n         * indicates the horizontal distance between the two closest points\n         * in the series, as multiples of the `marker.radius`. In other\n         * words, the default value of 2 means points are hidden if\n         * overlapping horizontally.\n         *\n         * @sample highcharts/plotoptions/series-marker-enabledthreshold\n         *         A higher threshold\n         *\n         * @since 6.0.5\n         */\n        enabledThreshold: 2,\n        /**\n         * The fill color of the point marker. When `undefined`, the series'\n         * or point's color is used.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-marker-fillcolor/\n         *         White fill\n         *\n         * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         * @apioption plotOptions.series.marker.fillColor\n         */\n        /**\n         * Image markers only. Set the image width explicitly. When using\n         * this option, a `width` must also be set.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-marker-width-height/\n         *         Fixed width and height\n         * @sample {highstock} highcharts/plotoptions/series-marker-width-height/\n         *         Fixed width and height\n         *\n         * @type      {number}\n         * @since     4.0.4\n         * @apioption plotOptions.series.marker.height\n         */\n        /**\n         * The color of the point marker's outline. When `undefined`, the\n         * series' or point's color is used.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-marker-fillcolor/\n         *         Inherit from series color (undefined)\n         *\n         * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         */\n        lineColor: \"#ffffff\" /* Palette.backgroundColor */,\n        /**\n         * The width of the point marker's outline.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-marker-fillcolor/\n         *         2px blue marker\n         */\n        lineWidth: 0,\n        /**\n         * The radius of the point marker.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-marker-radius/\n         *         Bigger markers\n         *\n         * @default {highstock} 2\n         * @default {highcharts} 4\n         *\n         */\n        radius: 4,\n        /**\n         * A predefined shape or symbol for the marker. When undefined, the\n         * symbol is pulled from options.symbols. Other possible values are\n         * `'circle'`, `'square'`,`'diamond'`, `'triangle'` and\n         * `'triangle-down'`.\n         *\n         * Additionally, the URL to a graphic can be given on this form:\n         * `'url(graphic.png)'`. Note that for the image to be applied to\n         * exported charts, its URL needs to be accessible by the export\n         * server.\n         *\n         * Custom callbacks for symbol path generation can also be added to\n         * `Highcharts.SVGRenderer.prototype.symbols`. The callback is then\n         * used by its method name, as shown in the demo.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-marker-symbol/\n         *         Predefined, graphic and custom markers\n         * @sample {highstock} highcharts/plotoptions/series-marker-symbol/\n         *         Predefined, graphic and custom markers\n         * @sample {highmaps} maps/demo/mappoint-mapmarker\n         *         Using the mapmarker symbol for points\n         *\n         * @type      {string}\n         * @apioption plotOptions.series.marker.symbol\n         */\n        /**\n         * Image markers only. Set the image width explicitly. When using\n         * this option, a `height` must also be set.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-marker-width-height/\n         *         Fixed width and height\n         * @sample {highstock} highcharts/plotoptions/series-marker-width-height/\n         *         Fixed width and height\n         *\n         * @type      {number}\n         * @since     4.0.4\n         * @apioption plotOptions.series.marker.width\n         */\n        /**\n         * States for a single point marker.\n         *\n         * @declare Highcharts.PointStatesOptionsObject\n         */\n        states: {\n            /**\n             * The normal state of a single point marker. Currently only\n             * used for setting animation when returning to normal state\n             * from hover.\n             *\n             * @declare Highcharts.PointStatesNormalOptionsObject\n             */\n            normal: {\n                /**\n                 * Animation when returning to normal state after hovering.\n                 *\n                 * @type {boolean|Partial<Highcharts.AnimationOptionsObject>}\n                 */\n                animation: true\n            },\n            /**\n             * The hover state for a single point marker.\n             *\n             * @declare Highcharts.PointStatesHoverOptionsObject\n             */\n            hover: {\n                /**\n                 * Animation when hovering over the marker.\n                 *\n                 * @type {boolean|Partial<Highcharts.AnimationOptionsObject>}\n                 */\n                animation: {\n                    /** @internal */\n                    duration: 150\n                },\n                /**\n                 * Enable or disable the point marker.\n                 *\n                 * @sample {highcharts} highcharts/plotoptions/series-marker-states-hover-enabled/\n                 *         Disabled hover state\n                 */\n                enabled: true,\n                /**\n                 * The fill color of the marker in hover state. When\n                 * `undefined`, the series' or point's fillColor for normal\n                 * state is used.\n                 *\n                 * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n                 * @apioption plotOptions.series.marker.states.hover.fillColor\n                 */\n                /**\n                 * The color of the point marker's outline. When\n                 * `undefined`, the series' or point's lineColor for normal\n                 * state is used.\n                 *\n                 * @sample {highcharts} highcharts/plotoptions/series-marker-states-hover-linecolor/\n                 *         White fill color, black line color\n                 *\n                 * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n                 * @apioption plotOptions.series.marker.states.hover.lineColor\n                 */\n                /**\n                 * The width of the point marker's outline. When\n                 * `undefined`, the series' or point's lineWidth for normal\n                 * state is used.\n                 *\n                 * @sample {highcharts} highcharts/plotoptions/series-marker-states-hover-linewidth/\n                 *         3px line width\n                 *\n                 * @type      {number}\n                 * @apioption plotOptions.series.marker.states.hover.lineWidth\n                 */\n                /**\n                 * The radius of the point marker. In hover state, it\n                 * defaults to the normal state's radius + 2 as per the\n                 * [radiusPlus](#plotOptions.series.marker.states.hover.radiusPlus)\n                 * option.\n                 *\n                 * @sample {highcharts} highcharts/plotoptions/series-marker-states-hover-radius/\n                 *         10px radius\n                 *\n                 * @type      {number}\n                 * @apioption plotOptions.series.marker.states.hover.radius\n                 */\n                /**\n                 * The number of pixels to increase the radius of the\n                 * hovered point.\n                 *\n                 * @sample {highcharts} highcharts/plotoptions/series-states-hover-linewidthplus/\n                 *         5 pixels greater radius on hover\n                 * @sample {highstock} highcharts/plotoptions/series-states-hover-linewidthplus/\n                 *         5 pixels greater radius on hover\n                 *\n                 * @since 4.0.3\n                 */\n                radiusPlus: 2,\n                /**\n                 * The additional line width for a hovered point.\n                 *\n                 * @sample {highcharts} highcharts/plotoptions/series-states-hover-linewidthplus/\n                 *         2 pixels wider on hover\n                 * @sample {highstock} highcharts/plotoptions/series-states-hover-linewidthplus/\n                 *         2 pixels wider on hover\n                 *\n                 * @since 4.0.3\n                 */\n                lineWidthPlus: 1\n            },\n            /**\n             * The appearance of the point marker when selected. In order to\n             * allow a point to be selected, set the\n             * `series.allowPointSelect` option to true.\n             *\n             * @declare Highcharts.PointStatesSelectOptionsObject\n             */\n            select: {\n                /**\n                 * Enable or disable visible feedback for selection.\n                 *\n                 * @sample {highcharts} highcharts/plotoptions/series-marker-states-select-enabled/\n                 *         Disabled select state\n                 *\n                 * @type      {boolean}\n                 * @default   true\n                 * @apioption plotOptions.series.marker.states.select.enabled\n                 */\n                /**\n                 * The radius of the point marker. In hover state, it\n                 * defaults to the normal state's radius + 2.\n                 *\n                 * @sample {highcharts} highcharts/plotoptions/series-marker-states-select-radius/\n                 *         10px radius for selected points\n                 *\n                 * @type      {number}\n                 * @apioption plotOptions.series.marker.states.select.radius\n                 */\n                /**\n                 * The fill color of the point marker.\n                 *\n                 * @sample {highcharts} highcharts/plotoptions/series-marker-states-select-fillcolor/\n                 *         Solid red discs for selected points\n                 *\n                 * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n                 */\n                fillColor: \"#cccccc\" /* Palette.neutralColor20 */,\n                /**\n                 * The color of the point marker's outline. When\n                 * `undefined`, the series' or point's color is used.\n                 *\n                 * @sample {highcharts} highcharts/plotoptions/series-marker-states-select-linecolor/\n                 *         Red line color for selected points\n                 *\n                 * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n                 */\n                lineColor: \"#000000\" /* Palette.neutralColor100 */,\n                /**\n                 * The width of the point marker's outline.\n                 *\n                 * @sample {highcharts} highcharts/plotoptions/series-marker-states-select-linewidth/\n                 *         3px line width for selected points\n                 */\n                lineWidth: 2\n            }\n        }\n    },\n    /**\n     * Properties for each single point.\n     *\n     * @declare Highcharts.PlotSeriesPointOptions\n     *\n     * @private\n     */\n    point: {\n        /**\n         * Fires when a point is clicked. One parameter, `event`, is passed\n         * to the function, containing common event information.\n         *\n         * If the `series.allowPointSelect` option is true, the default\n         * action for the point's click event is to toggle the point's\n         * select state. Returning `false` cancels this action.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-point-events-click/\n         *         Click marker to alert values\n         * @sample {highcharts} highcharts/plotoptions/series-point-events-click-column/\n         *         Click column\n         * @sample {highcharts} highcharts/plotoptions/series-point-events-click-url/\n         *         Go to URL\n         * @sample {highmaps} maps/plotoptions/series-point-events-click/\n         *         Click marker to display values\n         * @sample {highmaps} maps/plotoptions/series-point-events-click-url/\n         *         Go to URL\n         *\n         * @type      {Highcharts.PointClickCallbackFunction}\n         * @context   Highcharts.Point\n         * @apioption plotOptions.series.point.events.click\n         */\n        /**\n         * Fires when the mouse leaves the area close to the point. One\n         * parameter, `event`, is passed to the function, containing common\n         * event information.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-point-events-mouseover/\n         *         Show values in the chart's corner on mouse over\n         *\n         * @type      {Highcharts.PointMouseOutCallbackFunction}\n         * @context   Highcharts.Point\n         * @apioption plotOptions.series.point.events.mouseOut\n         */\n        /**\n         * Fires when the mouse enters the area close to the point. One\n         * parameter, `event`, is passed to the function, containing common\n         * event information.\n         *\n         * Returning `false` cancels the default behavior, which is to show a\n         * tooltip for the point.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-point-events-mouseover/\n         *         Show values in the chart's corner on mouse over\n         *\n         * @type      {Highcharts.PointMouseOverCallbackFunction}\n         * @context   Highcharts.Point\n         * @apioption plotOptions.series.point.events.mouseOver\n         */\n        /**\n         * Fires when the point is removed using the `.remove()` method. One\n         * parameter, `event`, is passed to the function. Returning `false`\n         * cancels the operation.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-point-events-remove/\n         *         Remove point and confirm\n         *\n         * @type      {Highcharts.PointRemoveCallbackFunction}\n         * @since     1.2.0\n         * @context   Highcharts.Point\n         * @apioption plotOptions.series.point.events.remove\n         */\n        /**\n         * Fires when the point is selected either programmatically or\n         * following a click on the point. One parameter, `event`, is passed\n         * to the function. Returning `false` cancels the operation.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-point-events-select/\n         *         Report the last selected point\n         * @sample {highmaps} maps/plotoptions/series-allowpointselect/\n         *         Report select and unselect\n         *\n         * @type      {Highcharts.PointSelectCallbackFunction}\n         * @since     1.2.0\n         * @context   Highcharts.Point\n         * @apioption plotOptions.series.point.events.select\n         */\n        /**\n         * Fires when the point is unselected either programmatically or\n         * following a click on the point. One parameter, `event`, is passed\n         * to the function.\n         *  Returning `false` cancels the operation.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-point-events-unselect/\n         *         Report the last unselected point\n         * @sample {highmaps} maps/plotoptions/series-allowpointselect/\n         *         Report select and unselect\n         *\n         * @type      {Highcharts.PointUnselectCallbackFunction}\n         * @since     1.2.0\n         * @context   Highcharts.Point\n         * @apioption plotOptions.series.point.events.unselect\n         */\n        /**\n         * Fires when the point is updated programmatically through the\n         * `.update()` method. One parameter, `event`, is passed to the\n         * function. The new point options can be accessed through\n         * `event.options`. Returning `false` cancels the operation.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-point-events-update/\n         *         Confirm point updating\n         *\n         * @type      {Highcharts.PointUpdateCallbackFunction}\n         * @since     1.2.0\n         * @context   Highcharts.Point\n         * @apioption plotOptions.series.point.events.update\n         */\n        /**\n         * Events for each single point.\n         *\n         * @declare Highcharts.PointEventsOptionsObject\n         */\n        events: {}\n    },\n    /**\n     * Options for the series data labels, appearing next to each data\n     * point.\n     *\n     * Since v6.2.0, multiple data labels can be applied to each single\n     * point by defining them as an array of configs.\n     *\n     * In styled mode, the data labels can be styled with the\n     * `.highcharts-data-label-box` and `.highcharts-data-label` class names\n     * ([see example](https://www.highcharts.com/samples/highcharts/css/series-datalabels)).\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-datalabels-enabled\n     *         Data labels enabled\n     * @sample {highcharts} highcharts/plotoptions/series-datalabels-multiple\n     *         Multiple data labels on a bar series\n     * @sample {highcharts} highcharts/css/series-datalabels\n     *         Styled mode example\n     * @sample {highmaps} maps/demo/color-axis\n     *         Choropleth map with data labels\n     * @sample {highmaps} maps/demo/mappoint-datalabels-mapmarker\n     *         Using data labels as map markers\n     *\n     * @type    {*|Array<*>}\n     * @product highcharts highstock highmaps gantt\n     *\n     * @private\n     */\n    dataLabels: {\n        /**\n         * Enable or disable the initial animation when a series is displayed\n         * for the `dataLabels`. The animation can also be set as a\n         * configuration object. Please note that this option only applies to\n         * the initial animation.\n         *\n         * For other animations, see [chart.animation](#chart.animation) and the\n         * animation parameter under the API methods. The following properties\n         * are supported:\n         *\n         * - `defer`: The animation delay time in milliseconds.\n         *\n         * @sample {highcharts} highcharts/plotoptions/animation-defer/\n         *          Animation defer settings\n         *\n         * @type      {boolean|Partial<Highcharts.AnimationOptionsObject>}\n         * @since     8.2.0\n         * @apioption plotOptions.series.dataLabels.animation\n         */\n        animation: {},\n        /**\n         * The animation delay time in milliseconds. Set to `0` to render the\n         * data labels immediately. As `undefined` inherits defer time from the\n         * [series.animation.defer](#plotOptions.series.animation.defer).\n         *\n         * @type      {number}\n         * @since     8.2.0\n         * @apioption plotOptions.series.dataLabels.animation.defer\n         */\n        /**\n         * The alignment of the data label compared to the point. If `right`,\n         * the right side of the label should be touching the point. For points\n         * with an extent, like columns, the alignments also dictates how to\n         * align it inside the box, as given with the\n         * [inside](#plotOptions.column.dataLabels.inside) option. Can be one of\n         * `left`, `center` or `right`.\n         *\n         * @sample {highcharts}\n         *         highcharts/plotoptions/series-datalabels-align-left/ Left\n         *         aligned\n         * @sample {highcharts}\n         *         highcharts/plotoptions/bar-datalabels-align-inside-bar/ Data\n         *         labels inside the bar\n         *\n         * @type {Highcharts.AlignValue|null}\n         */\n        align: 'center',\n        /**\n         * Alignment method for data labels. If set to `plotEdges`, the labels\n         * are aligned within the plot area in the direction of the y-axis. So\n         * in a regular column chart, the labels are aligned vertically\n         * according to the `verticalAlign` setting. In a bar chart, which is\n         * inverted, the labels are aligned horizontally according to the\n         * `align` setting. Applies to cartesian series only.\n         *\n         * @sample {highcharts} highcharts/series-bar/datalabels-alignto/\n         *         Align to plot edges\n         *\n         * @type      {string}\n         * @since 11.4.2\n         * @apioption plotOptions.series.dataLabels.alignTo\n         */\n        /**\n         * Whether to allow data labels to overlap. To make the labels less\n         * sensitive for overlapping, the\n         * [dataLabels.padding](#plotOptions.series.dataLabels.padding)\n         * can be set to 0.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-datalabels-allowoverlap-false/\n         *         Don't allow overlap\n         *\n         * @type      {boolean}\n         * @default   false\n         * @since     4.1.0\n         * @apioption plotOptions.series.dataLabels.allowOverlap\n         */\n        /**\n         * The background color or gradient for the data label. Setting it to\n         * `auto` will use the point's color.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-datalabels-box/\n         *         Data labels box options\n         * @sample {highmaps} maps/plotoptions/series-datalabels-box/\n         *         Data labels box options\n         * @sample {highmaps} maps/demo/mappoint-datalabels-mapmarker\n         *         Data labels as map markers\n         *\n         * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         * @since     2.2.1\n         * @apioption plotOptions.series.dataLabels.backgroundColor\n         */\n        /**\n         * The border color for the data label. Setting it to `auto` will use\n         * the point's color. Defaults to `undefined`.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-datalabels-box/\n         *         Data labels box options\n         *\n         * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         * @since     2.2.1\n         * @apioption plotOptions.series.dataLabels.borderColor\n         */\n        /**\n         * The border radius in pixels for the data label.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-datalabels-box/\n         *         Data labels box options\n         * @sample {highmaps} maps/plotoptions/series-datalabels-box/\n         *         Data labels box options\n         *\n         * @type      {number}\n         * @default   0\n         * @since     2.2.1\n         * @apioption plotOptions.series.dataLabels.borderRadius\n         */\n        /**\n         * The border width in pixels for the data label.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-datalabels-box/\n         *         Data labels box options\n         *\n         * @type      {number}\n         * @default   0\n         * @since     2.2.1\n         * @apioption plotOptions.series.dataLabels.borderWidth\n         */\n        borderWidth: 0,\n        /**\n         * A class name for the data label. Particularly in styled mode,\n         * this can be used to give each series' or point's data label\n         * unique styling. In addition to this option, a default color class\n         * name is added so that we can give the labels a contrast text\n         * shadow.\n         *\n         * @sample {highcharts} highcharts/css/data-label-contrast/\n         *         Contrast text shadow\n         * @sample {highcharts} highcharts/css/series-datalabels/\n         *         Styling by CSS\n         *\n         * @type      {string}\n         * @since     5.0.0\n         * @apioption plotOptions.series.dataLabels.className\n         */\n        /**\n         * This options is deprecated.\n         * Use [style.color](#plotOptions.series.dataLabels.style) instead.\n         *\n         * The text color for the data labels. Defaults to `undefined`. For\n         * certain series types, like column or map, the data labels can be\n         * drawn inside the points. In this case the data label will be\n         * drawn with maximum contrast by default. Additionally, it will be\n         * given a `text-outline` style with the opposite color, to further\n         * increase the contrast. This can be overridden by setting the\n         * `text-outline` style to `none` in the `dataLabels.style` option.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-datalabels-color/\n         *         Red data labels\n         * @sample {highmaps} maps/demo/color-axis/\n         *         White data labels\n         *\n         * @see [style.color](#plotOptions.series.dataLabels.style)\n         *\n         * @type       {Highcharts.ColorType}\n         * @deprecated 10.3\n         * @apioption  plotOptions.series.dataLabels.color\n         */\n        /**\n         * Whether to hide data labels that are outside the plot area. By\n         * default, the data label is moved inside the plot area according\n         * to the\n         * [overflow](#plotOptions.series.dataLabels.overflow)\n         * option.\n         *\n         * @type      {boolean}\n         * @default   true\n         * @since     2.3.3\n         * @apioption plotOptions.series.dataLabels.crop\n         */\n        /**\n         * Whether to defer displaying the data labels until the initial\n         * series animation has finished. Setting to `false` renders the\n         * data label immediately. If set to `true` inherits the defer\n         * time set in [plotOptions.series.animation](#plotOptions.series.animation).\n         *\n         * @since     4.0.0\n         * @type      {boolean}\n         * @product   highcharts highstock gantt\n         */\n        defer: true,\n        /**\n         * Enable or disable the data labels.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-datalabels-enabled/\n         *         Data labels enabled\n         * @sample {highmaps} maps/demo/color-axis/\n         *         Data labels enabled\n         *\n         * @type      {boolean}\n         * @default   false\n         * @apioption plotOptions.series.dataLabels.enabled\n         */\n        /**\n         * A declarative filter to control of which data labels to display.\n         * The declarative filter is designed for use when callback\n         * functions are not available, like when the chart options require\n         * a pure JSON structure or for use with graphical editors. For\n         * programmatic control, use the `formatter` instead, and return\n         * `undefined` to disable a single data label.\n         *\n         * @example\n         * filter: {\n         *     property: 'percentage',\n         *     operator: '>',\n         *     value: 4\n         * }\n         *\n         * @sample {highcharts} highcharts/demo/pie-monochrome\n         *         Data labels filtered by percentage\n         *\n         * @declare   Highcharts.DataLabelsFilterOptionsObject\n         * @since     6.0.3\n         * @apioption plotOptions.series.dataLabels.filter\n         */\n        /**\n         * The operator to compare by. Can be one of `>`, `<`, `>=`, `<=`,\n         * `==`, `===`, `!=` and `!==`.\n         *\n         * @type       {string}\n         * @validvalue [\">\", \"<\", \">=\", \"<=\", \"==\", \"===\", \"!=\", \"!==\"]\n         * @apioption  plotOptions.series.dataLabels.filter.operator\n         */\n        /**\n         * The point property to filter by. Point options are passed\n         * directly to properties, additionally there are `y` value,\n         * `percentage` and others listed under {@link Highcharts.Point}\n         * members.\n         *\n         * @type      {string}\n         * @apioption plotOptions.series.dataLabels.filter.property\n         */\n        /**\n         * The value to compare against.\n         *\n         * @type      {number}\n         * @apioption plotOptions.series.dataLabels.filter.value\n         */\n        /**\n         * A\n         * [format string](https://www.highcharts.com/docs/chart-concepts/labels-and-string-formatting)\n         * for the data label. Available variables are the same as for\n         * `formatter`.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-datalabels-format/\n         *         Add a unit\n         * @sample {highcharts} highcharts/plotoptions/series-datalabels-format-subexpression/\n         *         Complex logic in the format string\n         * @sample {highmaps} maps/plotoptions/series-datalabels-format/\n         *         Formatted value in the data label\n         *\n         * @type      {string}\n         * @default   y\n         * @default   point.value\n         * @since     3.0\n         * @apioption plotOptions.series.dataLabels.format\n         */\n        // eslint-disable-next-line valid-jsdoc\n        /**\n         * Callback JavaScript function to format the data label. Note that if a\n         * `format` is defined, the format takes precedence and the formatter is\n         * ignored.\n         *\n         * @sample {highmaps} maps/plotoptions/series-datalabels-format/\n         *         Formatted value\n         *\n         * @type {Highcharts.DataLabelsFormatterCallbackFunction}\n         */\n        formatter: function () {\n            const { numberFormatter } = this.series.chart;\n            return typeof this.y !== 'number' ?\n                '' : numberFormatter(this.y, -1);\n        },\n        /**\n         * For points with an extent, like columns or map areas, whether to\n         * align the data label inside the box or to the actual value point.\n         * Defaults to `false` in most cases, `true` in stacked columns.\n         *\n         * @type      {boolean}\n         * @since     3.0\n         * @apioption plotOptions.series.dataLabels.inside\n         */\n        /**\n         * Format for points with the value of null. Works analogously to\n         * [format](#plotOptions.series.dataLabels.format). `nullFormat` can\n         * be applied only to series which support displaying null points.\n         * `heatmap` and `tilemap` supports `nullFormat` by default while the\n         * following series requires [#series.nullInteraction] set to `true`:\n         * `line`, `spline`, `area`, `area-spline`, `column`, `bar`, and\n         * `timeline`. Does not work with series that don't display null\n         * points, like `pie`.\n         *\n         * @sample {highcharts} highcharts/series/null-interaction/\n         *         Line chart with null interaction\n         * @sample {highcharts} highcharts/plotoptions/series-datalabels-nullformat/\n         *         Heatmap with null interaction\n         *\n         * @type      {boolean|string}\n         * @since     7.1.0\n         * @apioption plotOptions.series.dataLabels.nullFormat\n         */\n        /**\n         * Callback JavaScript function that defines formatting for points\n         * with the value of null. Works analogously to [formatter](#plotOptions.series.dataLabels.formatter).\n         * `nullFormatter` can be applied only to series which support\n         * displaying null points. `heatmap` and `tilemap` supports\n         * `nullFormatter` by default while the following series requires [#series.nullInteraction]\n         * set to `true`: `line`, `spline`, `area`, `area-spline`, `column`,\n         * `bar`, and `timeline`. Does not work with series that don't display\n         * null points, like `pie`.\n\n         * @sample {highcharts} highcharts/plotoptions/series-datalabels-nullformat/\n         *         Format data label for null points in heat map\n         *\n         * @type      {Highcharts.DataLabelsFormatterCallbackFunction}\n         * @since     7.1.0\n         * @apioption plotOptions.series.dataLabels.nullFormatter\n         */\n        /**\n         * How to handle data labels that flow outside the plot area. The\n         * default is `\"justify\"`, which aligns them inside the plot area.\n         * For columns and bars, this means it will be moved inside the bar.\n         * To display data labels outside the plot area, set `crop` to\n         * `false` and `overflow` to `\"allow\"`.\n         *\n         * @type       {Highcharts.DataLabelsOverflowValue}\n         * @default    justify\n         * @since      3.0.6\n         * @apioption  plotOptions.series.dataLabels.overflow\n         */\n        /**\n         * When either the `borderWidth` or the `backgroundColor` is set,\n         * this is the padding within the box.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-datalabels-box/\n         *         Data labels box options\n         * @sample {highmaps} maps/plotoptions/series-datalabels-box/\n         *         Data labels box options\n         *\n         * @since 2.2.1\n         */\n        padding: 5,\n        /**\n         * Aligns data labels relative to points. If `center` alignment is\n         * not possible, it defaults to `right`.\n         *\n         * @type      {Highcharts.AlignValue}\n         * @default   center\n         * @apioption plotOptions.series.dataLabels.position\n         */\n        /**\n         * Text rotation in degrees. Note that due to a more complex\n         * structure, backgrounds, borders and padding will be lost on a\n         * rotated data label.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-datalabels-rotation/\n         *         Vertical labels\n         *\n         * @type      {number}\n         * @default   0\n         * @apioption plotOptions.series.dataLabels.rotation\n         */\n        /**\n         * The shadow of the box. Works best with `borderWidth` or\n         * `backgroundColor`. Since 2.3 the shadow can be an object\n         * configuration containing `color`, `offsetX`, `offsetY`, `opacity`\n         * and `width`.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-datalabels-box/\n         *         Data labels box options\n         *\n         * @type      {boolean|Highcharts.ShadowOptionsObject}\n         * @default   false\n         * @since     2.2.1\n         * @apioption plotOptions.series.dataLabels.shadow\n         */\n        /**\n         * The name of a symbol to use for the border around the label.\n         * Symbols are predefined functions on the Renderer object.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-datalabels-shape/\n         *         A callout for annotations\n         *\n         * @type      {string}\n         * @default   square\n         * @since     4.1.2\n         * @apioption plotOptions.series.dataLabels.shape\n         */\n        /**\n         * Styles for the label. The default `color` setting is\n         * `\"contrast\"`, which is a pseudo color that Highcharts picks up\n         * and applies the maximum contrast to the underlying point item,\n         * for example the bar in a bar chart.\n         *\n         * The `textOutline` is a pseudo property that applies an outline of\n         * the given width with the given color, which by default is the\n         * maximum contrast to the text. So a bright text color will result\n         * in a black text outline for maximum readability on a mixed\n         * background. In some cases, especially with grayscale text, the\n         * text outline doesn't work well, in which cases it can be disabled\n         * by setting it to `\"none\"`. When `useHTML` is true, the\n         * `textOutline` will not be picked up. In this, case, the same\n         * effect can be acheived through the `text-shadow` CSS property.\n         *\n         * For some series types, where each point has an extent, like for\n         * example tree maps, the data label may overflow the point. There\n         * are two strategies for handling overflow. By default, the text\n         * will wrap to multiple lines. The other strategy is to set\n         * `style.textOverflow` to `ellipsis`, which will keep the text on\n         * one line plus it will break inside long words.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-datalabels-style/\n         *         Bold labels\n         * @sample {highcharts} highcharts/plotoptions/pie-datalabels-overflow/\n         *         Long labels truncated with an ellipsis in a pie\n         * @sample {highcharts} highcharts/plotoptions/pie-datalabels-overflow-wrap/\n         *         Long labels are wrapped in a pie\n         * @sample {highmaps} maps/demo/color-axis/\n         *         Bold labels\n         *\n         * @type      {Highcharts.CSSObject}\n         * @since     4.1.0\n         * @apioption plotOptions.series.dataLabels.style\n         */\n        style: {\n            /** @internal */\n            fontSize: '0.7em',\n            /** @internal */\n            fontWeight: 'bold',\n            /** @internal */\n            color: 'contrast',\n            /** @internal */\n            textOutline: '1px contrast'\n        },\n        /**\n         * Options for a label text which should follow marker's shape.\n         * Border and background are disabled for a label that follows a\n         * path.\n         *\n         * **Note:** Only SVG-based renderer supports this option. Setting\n         * `useHTML` to true will disable this option.\n         *\n         * @declare   Highcharts.DataLabelsTextPathOptionsObject\n         * @since     7.1.0\n         * @apioption plotOptions.series.dataLabels.textPath\n         */\n        /**\n         * Presentation attributes for the text path.\n         *\n         * @type      {Highcharts.SVGAttributes}\n         * @since     7.1.0\n         * @apioption plotOptions.series.dataLabels.textPath.attributes\n         */\n        /**\n         * Enable or disable `textPath` option for link's or marker's data\n         * labels.\n         *\n         * @type      {boolean}\n         * @since     7.1.0\n         * @apioption plotOptions.series.dataLabels.textPath.enabled\n         */\n        /**\n         * Whether to\n         * [use HTML](https://www.highcharts.com/docs/chart-concepts/labels-and-string-formatting#html)\n         * to render the labels.\n         *\n         * @type      {boolean}\n         * @default   false\n         * @apioption plotOptions.series.dataLabels.useHTML\n         */\n        /**\n         * The vertical alignment of a data label. Can be one of `top`,\n         * `middle` or `bottom`. The default value depends on the data, for\n         * instance in a column chart, the label is above positive values\n         * and below negative values.\n         *\n         * @type  {Highcharts.VerticalAlignValue|null}\n         * @since 2.3.3\n         */\n        verticalAlign: 'bottom',\n        /**\n         * The x position offset of the label relative to the point in\n         * pixels.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-datalabels-rotation/\n         *         Vertical and positioned\n         * @sample {highcharts} highcharts/plotoptions/bar-datalabels-align-inside-bar/\n         *         Data labels inside the bar\n         */\n        x: 0,\n        /**\n         * The z index of the data labels. Use a `zIndex` of 6 to display it above\n         * the series, or use a `zIndex` of 2 to display it behind the series.\n         *\n         * @type      {number}\n         * @default   6\n         * @since     2.3.5\n         * @apioption plotOptions.series.dataLabels.zIndex\n         */\n        /**\n         * The y position offset of the label relative to the point in\n         * pixels.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-datalabels-rotation/\n         *         Vertical and positioned\n         */\n        y: 0\n    },\n    /**\n     * When the series contains less points than the crop threshold, all\n     * points are drawn, even if the points fall outside the visible plot\n     * area at the current zoom. The advantage of drawing all points\n     * (including markers and columns), is that animation is performed on\n     * updates. On the other hand, when the series contains more points than\n     * the crop threshold, the series data is cropped to only contain points\n     * that fall within the plot area. The advantage of cropping away\n     * invisible points is to increase performance on large series.\n     *\n     * @since   2.2\n     * @product highcharts highstock\n     *\n     * @private\n     */\n    cropThreshold: 300,\n    /**\n     * Opacity of a series parts: line, fill (e.g. area) and dataLabels.\n     *\n     * @see [states.inactive.opacity](#plotOptions.series.states.inactive.opacity)\n     *\n     * @since 7.1.0\n     *\n     * @private\n     */\n    opacity: 1,\n    /**\n     * The width of each point on the x axis. For example in a column chart\n     * with one value each day, the pointRange would be 1 day (= 24 * 3600\n     * * 1000 milliseconds). This is normally computed automatically, but\n     * this option can be used to override the automatic value.\n     *\n     * @product highstock\n     *\n     * @private\n     */\n    pointRange: 0,\n    /**\n     * When this is true, the series will not cause the Y axis to cross\n     * the zero plane (or [threshold](#plotOptions.series.threshold) option)\n     * unless the data actually crosses the plane.\n     *\n     * For example, if `softThreshold` is `false`, a series of 0, 1, 2,\n     * 3 will make the Y axis show negative values according to the\n     * `minPadding` option. If `softThreshold` is `true`, the Y axis starts\n     * at 0.\n     *\n     * @since   4.1.9\n     * @product highcharts highstock\n     *\n     * @private\n     */\n    softThreshold: true,\n    /**\n     * @declare Highcharts.SeriesStatesOptionsObject\n     *\n     * @private\n     */\n    states: {\n        /**\n         * The normal state of a series, or for point items in column, pie\n         * and similar series. Currently only used for setting animation\n         * when returning to normal state from hover.\n         *\n         * @declare Highcharts.SeriesStatesNormalOptionsObject\n         */\n        normal: {\n            /**\n             * Animation when returning to normal state after hovering.\n             *\n                 * @type {boolean|Partial<Highcharts.AnimationOptionsObject>}\n             */\n            animation: true\n        },\n        /**\n         * Options for the hovered series. These settings override the\n         * normal state options when a series is moused over or touched.\n         *\n         * @declare Highcharts.SeriesStatesHoverOptionsObject\n         */\n        hover: {\n            /**\n             * Enable separate styles for the hovered series to visualize\n             * that the user hovers either the series itself or the legend.\n             *\n             * @sample {highcharts} highcharts/plotoptions/series-states-hover-enabled/\n             *         Line\n             * @sample {highcharts} highcharts/plotoptions/series-states-hover-enabled-column/\n             *         Column\n             * @sample {highcharts} highcharts/plotoptions/series-states-hover-enabled-pie/\n             *         Pie\n             *\n             * @type      {boolean}\n             * @default   true\n             * @since     1.2\n             * @apioption plotOptions.series.states.hover.enabled\n             */\n            /**\n             * Animation setting for hovering the graph in line-type series.\n             *\n             * @type {boolean|Partial<Highcharts.AnimationOptionsObject>}\n             * @since   5.0.8\n             * @product highcharts highstock\n             */\n            animation: {\n                /**\n                 * The duration of the hover animation in milliseconds. By\n                 * default the hover state animates quickly in, and slowly\n                 * back to normal.\n                 *\n                 * @internal\n                 */\n                duration: 150\n            },\n            /**\n             * Pixel width of the graph line. By default this property is\n             * undefined, and the `lineWidthPlus` property dictates how much\n             * to increase the linewidth from normal state.\n             *\n             * @sample {highcharts} highcharts/plotoptions/series-states-hover-linewidth/\n             *         5px line on hover\n             *\n             * @type      {number}\n             * @product   highcharts highstock\n             * @apioption plotOptions.series.states.hover.lineWidth\n             */\n            /**\n             * The additional line width for the graph of a hovered series.\n             *\n             * @sample {highcharts} highcharts/plotoptions/series-states-hover-linewidthplus/\n             *         5 pixels wider\n             * @sample {highstock} highcharts/plotoptions/series-states-hover-linewidthplus/\n             *         5 pixels wider\n             *\n             * @since   4.0.3\n             * @product highcharts highstock\n             */\n            lineWidthPlus: 1,\n            /**\n             * In Highcharts 1.0, the appearance of all markers belonging\n             * to the hovered series. For settings on the hover state of the\n             * individual point, see\n             * [marker.states.hover](#plotOptions.series.marker.states.hover).\n             *\n             * @deprecated\n             *\n             * @extends   plotOptions.series.marker\n             * @excluding states, symbol\n             * @product   highcharts highstock\n             */\n            marker: {\n            // `lineWidth: base + 1`,\n            // `radius: base + 1`\n            },\n            /**\n             * Options for the halo appearing around the hovered point in\n             * line-type series as well as outside the hovered slice in pie\n             * charts. By default the halo is filled by the current point or\n             * series color with an opacity of 0.25\\. The halo can be\n             * disabled by setting the `halo` option to `null`.\n             *\n             * In styled mode, the halo is styled with the\n             * `.highcharts-halo` class, with colors inherited from\n             * `.highcharts-color-{n}`.\n             *\n             * @sample {highcharts} highcharts/plotoptions/halo/\n             *         Halo options\n             * @sample {highstock} highcharts/plotoptions/halo/\n             *         Halo options\n             *\n             * @declare Highcharts.SeriesStatesHoverHaloOptionsObject\n             * @type    {null|*}\n             * @since   4.0\n             * @product highcharts highstock\n             */\n            halo: {\n                /**\n                 * A collection of SVG attributes to override the appearance\n                 * of the halo, for example `fill`, `stroke` and\n                 * `stroke-width`.\n                 *\n                 * @type      {Highcharts.SVGAttributes}\n                 * @since     4.0\n                 * @product   highcharts highstock\n                 * @apioption plotOptions.series.states.hover.halo.attributes\n                 */\n                /**\n                 * The pixel size of the halo. For point markers this is the\n                 * radius of the halo. For pie slices it is the width of the\n                 * halo outside the slice. For bubbles it defaults to 5 and\n                 * is the width of the halo outside the bubble.\n                 *\n                 * @since   4.0\n                 * @product highcharts highstock\n                 */\n                size: 10,\n                /**\n                 * Opacity for the halo unless a specific fill is overridden\n                 * using the `attributes` setting.\n                 *\n                 * @since   4.0\n                 * @product highcharts highstock\n                 */\n                opacity: 0.25\n            }\n        },\n        /**\n         * Specific options for point in selected states, after being\n         * selected by\n         * [allowPointSelect](#plotOptions.series.allowPointSelect)\n         * or programmatically.\n         *\n         * @sample maps/plotoptions/series-allowpointselect/\n         *         Allow point select demo\n         *\n         * @declare   Highcharts.SeriesStatesSelectOptionsObject\n         * @extends   plotOptions.series.states.hover\n         * @excluding brightness\n         */\n        select: {\n            animation: {\n                /** @internal */\n                duration: 0\n            }\n        },\n        /**\n         * The opposite state of a hover for series.\n         *\n         * @sample highcharts/plotoptions/series-states-inactive-disabled\n         *         Disabled inactive state\n         *\n         * @declare Highcharts.SeriesStatesInactiveOptionsObject\n         */\n        inactive: {\n            /**\n             * Enable or disable the inactive state for a series\n             *\n             * @sample highcharts/plotoptions/series-states-inactive-disabled\n             *         Disabled inactive state\n             *\n             * @type {boolean}\n             * @default true\n             * @apioption plotOptions.series.states.inactive.enabled\n             */\n            /**\n             * The animation for entering the inactive state.\n             *\n             * @type {boolean|Partial<Highcharts.AnimationOptionsObject>}\n             */\n            animation: {\n                /** @internal */\n                duration: 150\n            },\n            /**\n             * Opacity of series elements (dataLabels, line, area).\n             *\n             * @type {number}\n             */\n            opacity: 0.2\n        }\n    },\n    /**\n     * Sticky tracking of mouse events. When true, the `mouseOut` event on a\n     * series isn't triggered until the mouse moves over another series, or\n     * out of the plot area. When false, the `mouseOut` event on a series is\n     * triggered when the mouse leaves the area around the series' graph or\n     * markers. This also implies the tooltip when not shared. When\n     * `stickyTracking` is false and `tooltip.shared` is false, the tooltip\n     * will be hidden when moving the mouse between series. Defaults to true\n     * for line and area type series, but to false for columns, pies etc.\n     *\n     * **Note:** The boost module will force this option because of\n     * technical limitations.\n     *\n     * @sample {highcharts} highcharts/plotoptions/series-stickytracking-true/\n     *         True by default\n     * @sample {highcharts} highcharts/plotoptions/series-stickytracking-false/\n     *         False\n     *\n     * @default {highcharts} true\n     * @default {highstock} true\n     * @default {highmaps} false\n     * @since   2.0\n     *\n     * @private\n     */\n    stickyTracking: true,\n    /**\n     * A configuration object for the tooltip rendering of each single\n     * series. Properties are inherited from [tooltip](#tooltip), but only\n     * the following properties can be defined on a series level.\n     *\n     * @declare   Highcharts.SeriesTooltipOptionsObject\n     * @since     2.3\n     * @extends   tooltip\n     * @excluding animation, backgroundColor, borderColor, borderRadius,\n     *            borderWidth, className, crosshairs, enabled, fixed, formatter,\n     *            headerShape, hideDelay, outside, padding, positioner,\n     *            shadow, shape, shared, snap, split, stickOnContact,\n     *            style, useHTML\n     * @apioption plotOptions.series.tooltip\n     */\n    /**\n     * When a series contains a `data` array that is longer than this, the\n     * Series class looks for data configurations of plain numbers or arrays of\n     * numbers. The first and last valid points are checked. If found, the rest\n     * of the data is assumed to be the same. This saves expensive data checking\n     * and indexing in long series, and makes data-heavy charts render faster.\n     *\n     * Set it to `0` disable.\n     *\n     * Note:\n     * - In boost mode turbo threshold is forced. Only array of numbers or two\n     *   dimensional arrays are allowed.\n     * - In version 11.4.3 and earlier, if object configurations were passed\n     *   beyond the turbo threshold, a warning was logged in the console and the\n     *   data series didn't render.\n     *\n     * @since   2.2\n     * @product highcharts highstock gantt\n     *\n     * @private\n     */\n    turboThreshold: 1000,\n    /**\n     * An array defining zones within a series. Zones can be applied to the\n     * X axis, Y axis or Z axis for bubbles, according to the `zoneAxis`\n     * option. The zone definitions have to be in ascending order regarding\n     * to the value.\n     *\n     * In styled mode, the color zones are styled with the\n     * `.highcharts-zone-{n}` class, or custom classed from the `className`\n     * option\n     * ([view live demo](https://jsfiddle.net/gh/get/library/pure/highcharts/highcharts/tree/master/samples/highcharts/css/color-zones/)).\n     *\n     * @see [zoneAxis](#plotOptions.series.zoneAxis)\n     *\n     * @sample {highcharts} highcharts/series/color-zones-simple/\n     *         Color zones\n     * @sample {highstock} highcharts/series/color-zones-simple/\n     *         Color zones\n     *\n     * @declare   Highcharts.SeriesZonesOptionsObject\n     * @type      {Array<*>}\n     * @since     4.1.0\n     * @product   highcharts highstock\n     * @apioption plotOptions.series.zones\n     */\n    /**\n     * Styled mode only. A custom class name for the zone.\n     *\n     * @sample highcharts/css/color-zones/\n     *         Zones styled by class name\n     *\n     * @type      {string}\n     * @since     5.0.0\n     * @apioption plotOptions.series.zones.className\n     */\n    /**\n     * Defines the color of the series.\n     *\n     * @see [series color](#plotOptions.series.color)\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @since     4.1.0\n     * @product   highcharts highstock\n     * @apioption plotOptions.series.zones.color\n     */\n    /**\n     * A name for the dash style to use for the graph.\n     *\n     * @see [plotOptions.series.dashStyle](#plotOptions.series.dashStyle)\n     *\n     * @sample {highcharts|highstock} highcharts/series/color-zones-dashstyle-dot/\n     *         Dashed line indicates prognosis\n     *\n     * @type      {Highcharts.DashStyleValue}\n     * @since     4.1.0\n     * @product   highcharts highstock\n     * @apioption plotOptions.series.zones.dashStyle\n     */\n    /**\n     * Defines the fill color for the series (in area type series)\n     *\n     * @see [fillColor](#plotOptions.area.fillColor)\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @since     4.1.0\n     * @product   highcharts highstock\n     * @apioption plotOptions.series.zones.fillColor\n     */\n    /**\n     * The value up to where the zone extends, if undefined the zones\n     * stretches to the last value in the series.\n     *\n     * @type      {number}\n     * @since     4.1.0\n     * @product   highcharts highstock\n     * @apioption plotOptions.series.zones.value\n     */\n    /**\n     * When using dual or multiple color axes, this number defines which\n     * colorAxis the particular series is connected to. It refers to\n     * either the\n     * {@link #colorAxis.id|axis id}\n     * or the index of the axis in the colorAxis array, with 0 being the\n     * first. Set this option to false to prevent a series from connecting\n     * to the default color axis.\n     *\n     * Since v7.2.0 the option can also be an axis id or an axis index\n     * instead of a boolean flag.\n     *\n     * @sample highcharts/coloraxis/coloraxis-with-pie/\n     *         Color axis with pie series\n     * @sample highcharts/coloraxis/multiple-coloraxis/\n     *         Multiple color axis\n     *\n     * @type      {number|string|boolean}\n     * @default   0\n     * @product   highcharts highstock highmaps\n     * @apioption plotOptions.series.colorAxis\n     */\n    /**\n     * Determines what data value should be used to calculate point color\n     * if `colorAxis` is used. Requires to set `min` and `max` if some\n     * custom point property is used or if approximation for data grouping\n     * is set to `'sum'`.\n     *\n     * @sample highcharts/coloraxis/custom-color-key/\n     *         Custom color key\n     * @sample highcharts/coloraxis/color-key-with-stops/\n     *         Custom colorKey with color axis stops\n     * @sample highcharts/coloraxis/changed-default-color-key/\n     *         Changed default color key\n     *\n     * @type      {string}\n     * @default   y\n     * @since     7.2.0\n     * @product   highcharts highstock highmaps\n     * @apioption plotOptions.series.colorKey\n     */\n    /**\n     * What type of legend symbol to render for this series. Can be one of\n     * `areaMarker`, `lineMarker` or `rectangle`.\n     *\n     * @validvalue [\"areaMarker\", \"lineMarker\", \"rectangle\"]\n     *\n     * @sample {highcharts} highcharts/series/legend-symbol/\n     *         Change the legend symbol\n     *\n     * @type      {string}\n     * @default   rectangle\n     * @since     11.0.1\n     * @apioption plotOptions.series.legendSymbol\n     */\n    /**\n     * Defines the color of the legend symbol for this series. Defaults to\n     * undefined, in which case the series color is used. Does not work with\n     * styled mode.\n     *\n     * @sample {highcharts|highstock} highcharts/series/legend-symbol-color/\n     *         Change the legend symbol color\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @default   undefined\n     * @since 12.0.0\n     * @product   highcharts highstock highmaps\n     * @apioption plotOptions.series.legendSymbolColor\n     */\n    /**\n     * Determines whether the series should look for the nearest point\n     * in both dimensions or just the x-dimension when hovering the series.\n     * Defaults to `'xy'` for scatter series and `'x'` for most other\n     * series. If the data has duplicate x-values, it is recommended to\n     * set this to `'xy'` to allow hovering over all points.\n     *\n     * Applies only to series types using nearest neighbor search (not\n     * direct hover) for tooltip.\n     *\n     * @sample {highcharts} highcharts/series/findnearestpointby/\n     *         Different hover behaviors\n     * @sample {highstock} highcharts/series/findnearestpointby/\n     *         Different hover behaviors\n     * @sample {highmaps} highcharts/series/findnearestpointby/\n     *         Different hover behaviors\n     *\n     * @since      5.0.10\n     * @validvalue [\"x\", \"xy\"]\n     *\n     * @private\n     */\n    findNearestPointBy: 'x'\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const SeriesDefaults = (seriesDefaults);\n\n;// ./code/es-modules/Series/Item/ItemSeriesDefaults.js\n/* *\n *\n *  (c) 2019-2025 Torstein Honsi\n *\n *  Item series type for Highcharts\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  API Options\n *\n * */\n/**\n * An item chart is an infographic chart where a number of items are laid\n * out in either a rectangular or circular pattern. It can be used to\n * visualize counts within a group, or for the circular pattern, typically\n * a parliament.\n *\n * The circular layout has much in common with a pie chart. Many of the item\n * series options, like `center`, `size` and data label positioning, are\n * inherited from the pie series and don't apply for rectangular layouts.\n *\n * @sample       highcharts/demo/parliament-chart\n *               Parliament chart (circular item chart)\n * @sample       highcharts/series-item/rectangular\n *               Rectangular item chart\n * @sample       highcharts/series-item/symbols\n *               Infographic with symbols\n *\n * @extends      plotOptions.pie\n * @since        7.1.0\n * @product      highcharts\n * @excluding    borderColor, borderWidth, depth, linecap, shadow,\n *               slicedOffset\n * @requires     modules/item-series\n * @optionparent plotOptions.item\n */\nconst ItemSeriesDefaults = {\n    /**\n     * In circular view, the end angle of the item layout, in degrees where\n     * 0 is up.\n     *\n     * @sample highcharts/demo/parliament-chart\n     *         Parliament chart\n     * @type {undefined|number}\n     */\n    endAngle: void 0,\n    /**\n     * In circular view, the size of the inner diameter of the circle. Can\n     * be a percentage or pixel value. Percentages are relative to the outer\n     * perimeter. Pixel values are given as integers.\n     *\n     * If the `rows` option is set, it overrides the `innerSize` setting.\n     *\n     * @sample highcharts/demo/parliament-chart\n     *         Parliament chart\n     * @type {string|number}\n     */\n    innerSize: '40%',\n    /**\n     * The padding between the items, given in relative size where the size\n     * of the item is 1.\n     * @type {number}\n     */\n    itemPadding: 0.1,\n    /**\n     * The layout of the items in rectangular view. Can be either\n     * `horizontal` or `vertical`.\n     * @sample highcharts/series-item/symbols\n     *         Horizontal layout\n     * @type {string}\n     */\n    layout: 'vertical',\n    /**\n     * @extends plotOptions.series.marker\n     */\n    marker: merge(SeriesDefaults.marker, {\n        radius: null\n    }),\n    /**\n     * The number of rows to display in the rectangular or circular view. If\n     * the `innerSize` is set, it will be overridden by the `rows` setting.\n     *\n     * @sample highcharts/series-item/rows-columns\n     *         Fixed row count\n     * @type {number}\n     */\n    rows: void 0,\n    crisp: false,\n    showInLegend: true,\n    /**\n     * In circular view, the start angle of the item layout, in degrees\n     * where 0 is up.\n     *\n     * @sample highcharts/demo/parliament-chart\n     *         Parliament chart\n     * @type {undefined|number}\n     */\n    startAngle: void 0\n};\n/**\n * An `item` series. If the [type](#series.item.type) option is not specified,\n * it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.item\n * @excluding dataParser, dataURL, stack, xAxis, yAxis, dataSorting,\n *            boostThreshold, boostBlending\n * @product   highcharts\n * @requires  modules/item-series\n * @apioption series.item\n */\n/**\n * An array of data points for the series. For the `item` series type,\n * points can be given in the following ways:\n *\n * 1. An array of numerical values. In this case, the numerical values will be\n *    interpreted as `y` options. Example:\n *    ```js\n *    data: [0, 5, 3, 5]\n *    ```\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.item.turboThreshold),\n *    this option is not available.\n *    ```js\n *    data: [{\n *        y: 1,\n *        name: \"Point2\",\n *        color: \"#00FF00\"\n *    }, {\n *        y: 7,\n *        name: \"Point1\",\n *        color: \"#FF00FF\"\n *    }]\n *    ```\n *\n * @sample {highcharts} highcharts/chart/reflow-true/\n *         Numerical values\n * @sample {highcharts} highcharts/series/data-array-of-arrays/\n *         Arrays of numeric x and y\n * @sample {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *         Arrays of datetime x and y\n * @sample {highcharts} highcharts/series/data-array-of-name-value/\n *         Arrays of point.name and y\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<number|Array<string,(number|null)>|null|*>}\n * @extends   series.pie.data\n * @exclude   sliced\n * @product   highcharts\n * @apioption series.item.data\n */\n/**\n * The sequential index of the data point in the legend.\n *\n * @type      {number}\n * @product   highcharts\n * @apioption series.pie.data.legendIndex\n */\n/**\n * @excluding legendItemClick\n * @apioption series.item.events\n */\n''; // Keeps the doclets above separate\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Item_ItemSeriesDefaults = (ItemSeriesDefaults);\n\n;// ./code/es-modules/Series/Item/ItemSeries.js\n/* *\n *\n *  (c) 2019-2025 Torstein Honsi\n *\n *  Item series type for Highcharts\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { pie: PieSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { defined, extend: ItemSeries_extend, fireEvent, isNumber, merge: ItemSeries_merge, pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n// Inherits pie as the most tested non-cartesian series with individual point\n// legend, tooltips etc. Only downside is we need to re-enable marker options.\n/**\n * The item series type.\n *\n * @requires modules/item-series\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.item\n *\n * @augments Highcharts.seriesTypes.pie\n */\nclass ItemSeries extends PieSeries {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Fade in the whole chart.\n     * @private\n     */\n    animate(init) {\n        const group = this.group;\n        if (group) {\n            if (init) {\n                group.attr({\n                    opacity: 0\n                });\n            }\n            else {\n                group.animate({\n                    opacity: 1\n                }, this.options.animation);\n            }\n        }\n    }\n    drawDataLabels() {\n        if (this.center && this.slots) {\n            super.drawDataLabels();\n            // Or it's just a dot chart with no natural place to put the data labels\n        }\n        else {\n            for (const point of this.points) {\n                point.destroyElements({ dataLabel: 1 });\n            }\n        }\n    }\n    drawPoints() {\n        const series = this, options = this.options, renderer = series.chart.renderer, seriesMarkerOptions = options.marker, borderWidth = this.borderWidth, crisp = borderWidth % 2 ? 0.5 : 1, rows = this.getRows(), cols = Math.ceil(this.total / rows), cellWidth = this.chart.plotWidth / cols, cellHeight = this.chart.plotHeight / rows, itemSize = this.itemSize || Math.min(cellWidth, cellHeight);\n        let i = 0;\n        /* @todo: remove if not needed\n        this.slots.forEach(slot => {\n            this.chart.renderer.circle(slot.x, slot.y, 6)\n                .attr({\n                    fill: 'silver'\n                })\n                .add(this.group);\n        });\n        //*/\n        for (const point of series.points) {\n            const pointMarkerOptions = point.marker || {}, symbol = (pointMarkerOptions.symbol ||\n                seriesMarkerOptions.symbol), r = pick(pointMarkerOptions.radius, seriesMarkerOptions.radius), size = defined(r) ? 2 * r : itemSize, padding = size * options.itemPadding;\n            let attr, graphics, pointAttr, x, y, width, height;\n            point.graphics = graphics = point.graphics || [];\n            if (!series.chart.styledMode) {\n                pointAttr = series.pointAttribs(point, point.selected && 'select');\n            }\n            if (!point.isNull && point.visible) {\n                if (!point.graphic) {\n                    point.graphic = renderer.g('point')\n                        .add(series.group);\n                }\n                for (let val = 0; val < (point.y || 0); ++val) {\n                    // Semi-circle\n                    if (series.center && series.slots) {\n                        // Fill up the slots from left to right\n                        const slot = series.slots.shift();\n                        x = slot.x - itemSize / 2;\n                        y = slot.y - itemSize / 2;\n                    }\n                    else if (options.layout === 'horizontal') {\n                        x = cellWidth * (i % cols);\n                        y = cellHeight * Math.floor(i / cols);\n                    }\n                    else {\n                        x = cellWidth * Math.floor(i / rows);\n                        y = cellHeight * (i % rows);\n                    }\n                    x += padding;\n                    y += padding;\n                    width = Math.round(size - 2 * padding);\n                    height = width;\n                    if (series.options.crisp) {\n                        x = Math.round(x) - crisp;\n                        y = Math.round(y) + crisp;\n                    }\n                    attr = {\n                        x: x,\n                        y: y,\n                        width: width,\n                        height: height\n                    };\n                    if (typeof r !== 'undefined') {\n                        attr.r = r;\n                    }\n                    // Circles attributes update (#17257)\n                    if (pointAttr) {\n                        ItemSeries_extend(attr, pointAttr);\n                    }\n                    let graphic = graphics[val];\n                    if (graphic) {\n                        graphic.animate(attr);\n                    }\n                    else {\n                        graphic = renderer\n                            .symbol(symbol, void 0, void 0, void 0, void 0, {\n                            backgroundSize: 'within'\n                        })\n                            .attr(attr)\n                            .add(point.graphic);\n                    }\n                    graphic.isActive = true;\n                    graphics[val] = graphic;\n                    ++i;\n                }\n            }\n            for (let j = 0; j < graphics.length; j++) {\n                const graphic = graphics[j];\n                if (!graphic) {\n                    return;\n                }\n                if (!graphic.isActive) {\n                    graphic.destroy();\n                    graphics.splice(j, 1);\n                    j--; // Need to subtract 1 after splice, #19053\n                }\n                else {\n                    graphic.isActive = false;\n                }\n            }\n        }\n    }\n    getRows() {\n        const chart = this.chart, total = this.total || 0;\n        let rows = this.options.rows, cols, ratio;\n        // Get the row count that gives the most square cells\n        if (!rows) {\n            ratio = chart.plotWidth / chart.plotHeight;\n            rows = Math.sqrt(total);\n            if (ratio > 1) {\n                rows = Math.ceil(rows);\n                while (rows > 1) {\n                    cols = total / rows;\n                    if (cols / rows > ratio) {\n                        break;\n                    }\n                    rows--;\n                }\n            }\n            else {\n                rows = Math.floor(rows);\n                while (rows < total) {\n                    cols = total / rows;\n                    if (cols / rows < ratio) {\n                        break;\n                    }\n                    rows++;\n                }\n            }\n        }\n        return rows;\n    }\n    /**\n     * Get the semi-circular slots.\n     * @private\n     */\n    getSlots() {\n        const series = this, center = series.center, diameter = center[2], slots = series.slots = series.slots || [], fullAngle = (series.endAngleRad - series.startAngleRad), rowsOption = series.options.rows, isCircle = fullAngle % (2 * Math.PI) === 0, total = series.total || 0;\n        let innerSize = center[3], x, y, rowRadius, rowLength, colCount, increment, angle, col, itemSize = 0, rowCount, itemCount = Number.MAX_VALUE, finalItemCount, rows, testRows, \n        // How many rows (arcs) should be used\n        rowFraction = (diameter - innerSize) / diameter;\n        // Increase the itemSize until we find the best fit\n        while (itemCount > total + (rows && isCircle ? rows.length : 0)) {\n            finalItemCount = itemCount;\n            // Reset\n            slots.length = 0;\n            itemCount = 0;\n            // Now rows is the last successful run\n            rows = testRows;\n            testRows = [];\n            itemSize++;\n            // Total number of rows (arcs) from the center to the\n            // perimeter\n            rowCount = diameter / itemSize / 2;\n            if (rowsOption) {\n                innerSize = ((rowCount - rowsOption) / rowCount) * diameter;\n                if (innerSize >= 0) {\n                    rowCount = rowsOption;\n                    // If innerSize is negative, we are trying to set too\n                    // many rows in the rows option, so fall back to\n                    // treating it as innerSize 0\n                }\n                else {\n                    innerSize = 0;\n                    rowFraction = 1;\n                }\n            }\n            else {\n                rowCount = Math.floor(rowCount * rowFraction);\n            }\n            for (let row = rowCount; row > 0; row--) {\n                rowRadius = (innerSize + (row / rowCount) *\n                    (diameter - innerSize - itemSize)) / 2;\n                rowLength = fullAngle * rowRadius;\n                colCount = Math.ceil(rowLength / itemSize);\n                testRows.push({\n                    rowRadius: rowRadius,\n                    rowLength: rowLength,\n                    colCount: colCount\n                });\n                itemCount += colCount + 1;\n            }\n        }\n        if (!rows) {\n            return;\n        }\n        // We now have more slots than we have total items. Loop over\n        // the rows and remove the last slot until the count is correct.\n        // For each iteration we sort the last slot by the angle, and\n        // remove those with the highest angles.\n        let overshoot = finalItemCount - series.total -\n            (isCircle ? rows.length : 0);\n        /**\n         * @private\n         * @param {Highcharts.ItemRowContainerObject} item\n         * Wrapped object with angle and row\n         */\n        const cutOffRow = (item) => {\n            if (overshoot > 0) {\n                item.row.colCount--;\n                overshoot--;\n            }\n        };\n        while (overshoot > 0) {\n            rows\n                // Return a simplified representation of the angle of\n                // the last slot within each row.\n                .map((row) => ({\n                angle: row.colCount / row.rowLength,\n                row: row\n            }))\n                // Sort by the angles...\n                .sort((a, b) => (b.angle - a.angle))\n                // ...so that we can ignore the items with the lowest\n                // angles...\n                .slice(0, Math.min(overshoot, Math.ceil(rows.length / 2)))\n                // ...and remove the ones with the highest angles\n                .forEach(cutOffRow);\n        }\n        for (const row of rows) {\n            const rowRadius = row.rowRadius, colCount = row.colCount;\n            increment = colCount ? fullAngle / colCount : 0;\n            for (col = 0; col <= colCount; col += 1) {\n                angle = series.startAngleRad + col * increment;\n                x = center[0] + Math.cos(angle) * rowRadius;\n                y = center[1] + Math.sin(angle) * rowRadius;\n                slots.push({ x: x, y: y, angle: angle });\n            }\n        }\n        // Sort by angle\n        slots.sort((a, b) => (a.angle - b.angle));\n        series.itemSize = itemSize;\n        return slots;\n    }\n    translate(positions) {\n        // Initialize chart without setting data, #13379.\n        if (this.total === 0 && // Check if that is a (semi-)circle\n            isNumber(this.options.startAngle) &&\n            isNumber(this.options.endAngle)) {\n            this.center = this.getCenter();\n        }\n        if (!this.slots) {\n            this.slots = [];\n        }\n        if (isNumber(this.options.startAngle) &&\n            isNumber(this.options.endAngle)) {\n            super.translate(positions);\n            this.slots = this.getSlots();\n        }\n        else {\n            this.generatePoints();\n            fireEvent(this, 'afterTranslate');\n        }\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nItemSeries.defaultOptions = ItemSeries_merge(PieSeries.defaultOptions, Item_ItemSeriesDefaults);\nItemSeries_extend(ItemSeries.prototype, {\n    markerAttribs: void 0,\n    pointClass: Item_ItemPoint\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('item', ItemSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Item_ItemSeries = ((/* unused pure expression or super */ null && (ItemSeries)));\n\n;// ./code/es-modules/masters/modules/item-series.js\n\n\n\n\n/* harmony default export */ const item_series_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "item_series_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "series", "pointClass", "Point", "seriesTypes", "pie", "PiePoint", "extend", "ItemPoint", "haloPath", "merge", "ItemSeriesDefaults", "endAngle", "innerSize", "itemPadding", "layout", "marker", "SeriesDefaults", "lineWidth", "allowPointSelect", "crisp", "showCheckbox", "animation", "duration", "enableMouseTracking", "events", "enabledThreshold", "lineColor", "radius", "states", "normal", "hover", "enabled", "radiusPlus", "lineWidthPlus", "select", "fillColor", "point", "dataLabels", "align", "borderWidth", "defer", "formatter", "numberF<PERSON>atter", "chart", "y", "padding", "style", "fontSize", "fontWeight", "color", "textOutline", "verticalAlign", "x", "cropThreshold", "opacity", "pointRange", "softT<PERSON>eshold", "halo", "size", "inactive", "stickyTracking", "turboThreshold", "findNearestPointBy", "rows", "showInLegend", "startAngle", "PieSeries", "defined", "ItemSeries_extend", "fireEvent", "isNumber", "ItemSeries_merge", "pick", "ItemSeries", "animate", "init", "group", "attr", "options", "drawDataLabels", "center", "slots", "points", "destroyElements", "dataLabel", "drawPoints", "renderer", "seriesMarkerOptions", "getRows", "cols", "Math", "ceil", "total", "cellWidth", "plot<PERSON>id<PERSON>", "cellHeight", "plotHeight", "itemSize", "min", "i", "graphics", "pointAttr", "width", "pointMarkerOptions", "symbol", "r", "styledMode", "pointAttribs", "selected", "isNull", "visible", "graphic", "g", "add", "val", "slot", "shift", "floor", "round", "height", "backgroundSize", "isActive", "j", "length", "destroy", "splice", "ratio", "sqrt", "getSlots", "diameter", "fullAngle", "endAngleRad", "startAngleRad", "rowsOption", "isCircle", "PI", "rowRadius", "<PERSON><PERSON><PERSON><PERSON>", "col<PERSON>ount", "increment", "angle", "col", "rowCount", "itemCount", "Number", "MAX_VALUE", "finalItemCount", "testRows", "rowFraction", "row", "push", "overshoot", "cutOffRow", "item", "map", "sort", "b", "slice", "for<PERSON>ach", "cos", "sin", "translate", "positions", "getCenter", "generatePoints", "defaultOptions", "markerAttribs", "registerSeriesType"], "mappings": "CAWA,AAXA;;;;;;;;;;CAUC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,iCAAkC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GAC1H,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,iCAAiC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAE9GA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAejL,GAAM,CAAEE,OAAQ,CAAET,UAAW,CAAEU,WAAYC,CAAK,CAAE,CAAE,CAAEC,YAAa,CAAEC,IAAK,CAAEb,UAAW,CAAEU,WAAYI,CAAQ,CAAE,CAAE,CAAE,CAAE,CAAIN,IAEnH,CAAEO,OAAAA,CAAM,CAAE,CAAIT,GAMpB,OAAMU,UAAkBF,EACxB,CACAC,EAAOC,EAAUhB,SAAS,CAAE,CACxBiB,SAAUN,EAAMX,SAAS,CAACiB,QAAQ,AACtC,GAg6EA,GAAM,CAAEC,MAAAA,CAAK,CAAE,CAAIZ,IA+Bba,EAAqB,CASvBC,SAAU,KAAK,EAYfC,UAAW,MAMXC,YAAa,GAQbC,OAAQ,WAIRC,OAAQN,EAAMO,AAz8EK,CAyBnBC,UAAW,EA2CXC,iBAAkB,CAAA,EAiBlBC,MAAO,CAAA,EAiBPC,aAAc,CAAA,EA2CdC,UAAW,CAEPC,SAAU,GACd,EA+NAC,oBAAqB,CAAA,EAgdrBC,OAAQ,CAAC,EAmITT,OAAQ,CA+BJU,iBAAkB,EAiClBC,UAAW,UAOXT,UAAW,EAWXU,OAAQ,EA4CRC,OAAQ,CAQJC,OAAQ,CAMJR,UAAW,CAAA,CACf,EAMAS,MAAO,CAMHT,UAAW,CAEPC,SAAU,GACd,EAOAS,QAAS,CAAA,EAsDTC,WAAY,EAWZC,cAAe,CACnB,EAQAC,OAAQ,CA6BJC,UAAW,UAUXT,UAAW,UAOXT,UAAW,CACf,CACJ,CACJ,EAQAmB,MAAO,CAkHHZ,OAAQ,CAAC,CACb,EA4BAa,WAAY,CAoBRhB,UAAW,CAAC,EA2BZiB,MAAO,SAgFPC,YAAa,EA8DbC,MAAO,CAAA,EAwFPC,UAAW,WACP,GAAM,CAAEC,gBAAAA,CAAe,CAAE,CAAG,IAAI,CAAC1C,MAAM,CAAC2C,KAAK,CAC7C,MAAO,AAAkB,UAAlB,OAAO,IAAI,CAACC,CAAC,CAChB,GAAKF,EAAgB,IAAI,CAACE,CAAC,CAAE,GACrC,EAqEAC,QAAS,EAmFTC,MAAO,CAEHC,SAAU,QAEVC,WAAY,OAEZC,MAAO,WAEPC,YAAa,cACjB,EA8CAC,cAAe,SAUfC,EAAG,EAiBHR,EAAG,CACP,EAgBAS,cAAe,IAUfC,QAAS,EAWTC,WAAY,EAgBZC,cAAe,CAAA,EAMf5B,OAAQ,CAQJC,OAAQ,CAMJR,UAAW,CAAA,CACf,EAOAS,MAAO,CAwBHT,UAAW,CAQPC,SAAU,GACd,EAwBAW,cAAe,EAaflB,OAAQ,CAGR,EAsBA0C,KAAM,CAoBFC,KAAM,GAQNJ,QAAS,GACb,CACJ,EAcApB,OAAQ,CACJb,UAAW,CAEPC,SAAU,CACd,CACJ,EASAqC,SAAU,CAgBNtC,UAAW,CAEPC,SAAU,GACd,EAMAgC,QAAS,EACb,CACJ,EA0BAM,eAAgB,CAAA,EAqChBC,eAAgB,IAwKhBC,mBAAoB,GACxB,EA6FiC/C,MAAM,CAAE,CACjCY,OAAQ,IACZ,GASAoC,KAAM,KAAK,EACX5C,MAAO,CAAA,EACP6C,aAAc,CAAA,EASdC,WAAY,KAAK,CACrB,EA2FM,CAAE7D,IAAK8D,CAAS,CAAE,CAAG,AAACnE,IAA2II,WAAW,CAE5K,CAAEgE,QAAAA,CAAO,CAAE7D,OAAQ8D,CAAiB,CAAEC,UAAAA,CAAS,CAAEC,SAAAA,CAAQ,CAAE7D,MAAO8D,CAAgB,CAAEC,KAAAA,CAAI,CAAE,CAAI3E,GAmBpG,OAAM4E,UAAmBP,EAUrBQ,QAAQC,CAAI,CAAE,CACV,IAAMC,EAAQ,IAAI,CAACA,KAAK,CACpBA,IACID,EACAC,EAAMC,IAAI,CAAC,CACPvB,QAAS,CACb,GAGAsB,EAAMF,OAAO,CAAC,CACVpB,QAAS,CACb,EAAG,IAAI,CAACwB,OAAO,CAACzD,SAAS,EAGrC,CACA0D,gBAAiB,CACb,GAAI,IAAI,CAACC,MAAM,EAAI,IAAI,CAACC,KAAK,CACzB,KAAK,CAACF,sBAIN,IAAK,IAAM3C,KAAS,IAAI,CAAC8C,MAAM,CAC3B9C,EAAM+C,eAAe,CAAC,CAAEC,UAAW,CAAE,EAGjD,CACAC,YAAa,CACT,IAAqBP,EAAU,IAAI,CAACA,OAAO,CAAEQ,EAAWtF,AAAzC,IAAI,CAA4C2C,KAAK,CAAC2C,QAAQ,CAAEC,EAAsBT,EAAQ/D,MAAM,CAAkCI,EAAQoB,AAA1B,IAAI,CAACA,WAAW,CAAwB,EAAI,GAAM,EAAGwB,EAAO,IAAI,CAACyB,OAAO,GAAIC,EAAOC,KAAKC,IAAI,CAAC,IAAI,CAACC,KAAK,CAAG7B,GAAO8B,EAAY,IAAI,CAAClD,KAAK,CAACmD,SAAS,CAAGL,EAAMM,EAAa,IAAI,CAACpD,KAAK,CAACqD,UAAU,CAAGjC,EAAMkC,EAAW,IAAI,CAACA,QAAQ,EAAIP,KAAKQ,GAAG,CAACL,EAAWE,GACpXI,EAAI,EAUR,IAAK,IAAM/D,KAASpC,AAXL,IAAI,CAWQkF,MAAM,CAAE,CAC/B,IAEIL,EAAMuB,EAAUC,EAAWjD,EAAGR,EAAG0D,EAF/BC,EAAqBnE,EAAMrB,MAAM,EAAI,CAAC,EAAGyF,EAAUD,EAAmBC,MAAM,EAC9EjB,EAAoBiB,MAAM,CAAGC,EAAIjC,EAAK+B,EAAmB5E,MAAM,CAAE4D,EAAoB5D,MAAM,EAAG+B,EAAOS,EAAQsC,GAAK,EAAIA,EAAIR,EAAUpD,EAAUa,EAAOoB,EAAQjE,WAAW,CAM5K,GAJAuB,EAAMgE,QAAQ,CAAGA,EAAWhE,EAAMgE,QAAQ,EAAI,EAAE,CAC5C,AAACpG,AAhBM,IAAI,CAgBH2C,KAAK,CAAC+D,UAAU,EACxBL,CAAAA,EAAYrG,AAjBL,IAAI,CAiBQ2G,YAAY,CAACvE,EAAOA,EAAMwE,QAAQ,EAAI,SAAQ,EAEjE,CAACxE,EAAMyE,MAAM,EAAIzE,EAAM0E,OAAO,CAAE,CAC5B,AAAC1E,EAAM2E,OAAO,EACd3E,CAAAA,EAAM2E,OAAO,CAAGzB,EAAS0B,CAAC,CAAC,SACtBC,GAAG,CAACjH,AAtBN,IAAI,CAsBS4E,KAAK,CAAA,EAEzB,IAAK,IAAIsC,EAAM,EAAGA,EAAO9E,CAAAA,EAAMQ,CAAC,EAAI,CAAA,EAAI,EAAEsE,EAAK,CAE3C,GAAIlH,AA1BD,IAAI,CA0BIgF,MAAM,EAAIhF,AA1BlB,IAAI,CA0BqBiF,KAAK,CAAE,CAE/B,IAAMkC,EAAOnH,AA5Bd,IAAI,CA4BiBiF,KAAK,CAACmC,KAAK,GAC/BhE,EAAI+D,EAAK/D,CAAC,CAAG6C,EAAW,EACxBrD,EAAIuE,EAAKvE,CAAC,CAAGqD,EAAW,CAC5B,KACSnB,AAAmB,eAAnBA,EAAQhE,MAAM,EACnBsC,EAAIyC,AAAaM,EAAIV,EAAjBI,EACJjD,EAAImD,EAAaL,KAAK2B,KAAK,CAAClB,EAAIV,KAGhCrC,EAAIyC,EAAYH,KAAK2B,KAAK,CAAClB,EAAIpC,GAC/BnB,EAAImD,AAAcI,EAAIpC,EAAlBgC,GAER3C,GAAKP,EACLD,GAAKC,EACLyD,EAAQZ,KAAK4B,KAAK,CAAC5D,EAAO,EAAIb,GAE1B7C,AA5CD,IAAI,CA4CI8E,OAAO,CAAC3D,KAAK,GACpBiC,EAAIsC,KAAK4B,KAAK,CAAClE,GAAKjC,EACpByB,EAAI8C,KAAK4B,KAAK,CAAC1E,GAAKzB,GAExB0D,EAAO,CACHzB,EAAGA,EACHR,EAAGA,EACH0D,MAAOA,EACPiB,OATKjB,CAUT,EACI,AAAa,KAAA,IAANG,GACP5B,CAAAA,EAAK4B,CAAC,CAAGA,CAAAA,EAGTJ,GACAjC,EAAkBS,EAAMwB,GAE5B,IAAIU,EAAUX,CAAQ,CAACc,EAAI,CACvBH,EACAA,EAAQrC,OAAO,CAACG,GAGhBkC,EAAUzB,EACLkB,MAAM,CAACA,EAAQ,KAAK,EAAG,KAAK,EAAG,KAAK,EAAG,KAAK,EAAG,CAChDgB,eAAgB,QACpB,GACK3C,IAAI,CAACA,GACLoC,GAAG,CAAC7E,EAAM2E,OAAO,EAE1BA,EAAQU,QAAQ,CAAG,CAAA,EACnBrB,CAAQ,CAACc,EAAI,CAAGH,EAChB,EAAEZ,CACN,CACJ,CACA,IAAK,IAAIuB,EAAI,EAAGA,EAAItB,EAASuB,MAAM,CAAED,IAAK,CACtC,IAAMX,EAAUX,CAAQ,CAACsB,EAAE,CAC3B,GAAI,CAACX,EACD,MAECA,CAAAA,EAAQU,QAAQ,CAMjBV,EAAQU,QAAQ,CAAG,CAAA,GALnBV,EAAQa,OAAO,GACfxB,EAASyB,MAAM,CAACH,EAAG,GACnBA,IAKR,CACJ,CACJ,CACAlC,SAAU,CACN,IAAM7C,EAAQ,IAAI,CAACA,KAAK,CAAEiD,EAAQ,IAAI,CAACA,KAAK,EAAI,EAC5C7B,EAAO,IAAI,CAACe,OAAO,CAACf,IAAI,CAAE0B,EAAMqC,EAEpC,GAAI,CAAC/D,EAGD,GAFA+D,EAAQnF,EAAMmD,SAAS,CAAGnD,EAAMqD,UAAU,CAC1CjC,EAAO2B,KAAKqC,IAAI,CAACnC,GACbkC,EAAQ,EAER,IADA/D,EAAO2B,KAAKC,IAAI,CAAC5B,GAGb,AAFGA,EAAO,IAEN0B,CAAAA,AADGG,EAAQ7B,EACJA,EAAO+D,CAAI,GAGtB/D,SAKJ,IADAA,EAAO2B,KAAK2B,KAAK,CAACtD,GAGd,AAFGA,EAAO6B,IAENH,CAAAA,AADGG,EAAQ7B,EACJA,EAAO+D,CAAI,GAGtB/D,IAIZ,OAAOA,CACX,CAKAiE,UAAW,CACP,IAAqBhD,EAAShF,AAAf,IAAI,CAAkBgF,MAAM,CAAEiD,EAAWjD,CAAM,CAAC,EAAE,CAAEC,EAAQjF,AAA5D,IAAI,CAA+DiF,KAAK,CAAGjF,AAA3E,IAAI,CAA8EiF,KAAK,EAAI,EAAE,CAAEiD,EAAalI,AAA5G,IAAI,CAA+GmI,WAAW,CAAGnI,AAAjI,IAAI,CAAoIoI,aAAa,CAAGC,EAAarI,AAArK,IAAI,CAAwK8E,OAAO,CAACf,IAAI,CAAEuE,EAAWJ,EAAa,CAAA,EAAIxC,KAAK6C,EAAE,AAAD,GAAO,EAAG3C,EAAQ5F,AAA9O,IAAI,CAAiP4F,KAAK,EAAI,EACzQhF,EAAYoE,CAAM,CAAC,EAAE,CAAE5B,EAAGR,EAAG4F,EAAWC,EAAWC,EAAUC,EAAWC,EAAOC,EAAK5C,EAAW,EAAG6C,EAAUC,EAAYC,OAAOC,SAAS,CAAEC,EAAgBnF,EAAMoF,EAEpKC,EAAc,AAACnB,CAAAA,EAAWrH,CAAQ,EAAKqH,EAEvC,KAAOc,EAAYnD,EAAS7B,CAAAA,GAAQuE,EAAWvE,EAAK4D,MAAM,CAAG,CAAA,GAAI,CAC7DuB,EAAiBH,EAEjB9D,EAAM0C,MAAM,CAAG,EACfoB,EAAY,EAEZhF,EAAOoF,EACPA,EAAW,EAAE,CAIbL,EAAWb,IAAWhC,EAAW,EAC7BoC,EAEIzH,AADJA,CAAAA,EAAY,AAAEkI,CAAAA,EAAWT,CAAS,EAAKS,EAAYb,CAAO,GACzC,EACba,EAAWT,GAMXzH,EAAY,EACZwI,EAAc,GAIlBN,EAAWpD,KAAK2B,KAAK,CAACyB,EAAWM,GAErC,IAAK,IAAIC,EAAMP,EAAUO,EAAM,EAAGA,IAI9BX,EAAWhD,KAAKC,IAAI,CAAC8C,AADrBA,CAAAA,EAAYP,EAFZM,CAAAA,EAAY,AAAC5H,CAAAA,EAAY,AAACyI,EAAMP,EAC3Bb,CAAAA,EAAWrH,EAAYqF,CAAO,CAAC,EAAK,CAAA,CACT,EACCA,GACjCkD,EAASG,IAAI,CAAC,CACVd,UAAWA,EACXC,UAAWA,EACXC,SAAUA,CACd,GACAK,GAAaL,EAAW,CAEhC,CACA,GAAI,CAAC3E,EACD,OAMJ,IAAIwF,EAAYL,EAAiBlJ,AArDlB,IAAI,CAqDqB4F,KAAK,CACxC0C,CAAAA,EAAWvE,EAAK4D,MAAM,CAAG,CAAA,EAMxB6B,EAAY,AAACC,IACXF,EAAY,IACZE,EAAKJ,GAAG,CAACX,QAAQ,GACjBa,IAER,EACA,KAAOA,EAAY,GACfxF,EAGK2F,GAAG,CAAC,AAACL,GAAS,CAAA,CACfT,MAAOS,EAAIX,QAAQ,CAAGW,EAAIZ,SAAS,CACnCY,IAAKA,CACT,CAAA,GAEKM,IAAI,CAAC,CAAC9K,EAAG+K,IAAOA,EAAEhB,KAAK,CAAG/J,EAAE+J,KAAK,EAGjCiB,KAAK,CAAC,EAAGnE,KAAKQ,GAAG,CAACqD,EAAW7D,KAAKC,IAAI,CAAC5B,EAAK4D,MAAM,CAAG,KAErDmC,OAAO,CAACN,GAEjB,IAAK,IAAMH,KAAOtF,EAAM,CACpB,IAAMyE,EAAYa,EAAIb,SAAS,CAAEE,EAAWW,EAAIX,QAAQ,CAExD,IAAKG,EAAM,EADXF,EAAYD,EAAWR,EAAYQ,EAAW,EAChCG,GAAOH,EAAUG,GAAO,EAClCD,EAAQ5I,AAtFD,IAAI,CAsFIoI,aAAa,CAAGS,EAAMF,EACrCvF,EAAI4B,CAAM,CAAC,EAAE,CAAGU,KAAKqE,GAAG,CAACnB,GAASJ,EAClC5F,EAAIoC,CAAM,CAAC,EAAE,CAAGU,KAAKsE,GAAG,CAACpB,GAASJ,EAClCvD,EAAMqE,IAAI,CAAC,CAAElG,EAAGA,EAAGR,EAAGA,EAAGgG,MAAOA,CAAM,EAE9C,CAIA,OAFA3D,EAAM0E,IAAI,CAAC,CAAC9K,EAAG+K,IAAO/K,EAAE+J,KAAK,CAAGgB,EAAEhB,KAAK,EACvC5I,AA9Fe,IAAI,CA8FZiG,QAAQ,CAAGA,EACXhB,CACX,CACAgF,UAAUC,CAAS,CAAE,CAEb,AAAe,IAAf,IAAI,CAACtE,KAAK,EACVtB,EAAS,IAAI,CAACQ,OAAO,CAACb,UAAU,GAChCK,EAAS,IAAI,CAACQ,OAAO,CAACnE,QAAQ,GAC9B,CAAA,IAAI,CAACqE,MAAM,CAAG,IAAI,CAACmF,SAAS,EAAC,EAE7B,AAAC,IAAI,CAAClF,KAAK,EACX,CAAA,IAAI,CAACA,KAAK,CAAG,EAAE,AAAD,EAEdX,EAAS,IAAI,CAACQ,OAAO,CAACb,UAAU,GAChCK,EAAS,IAAI,CAACQ,OAAO,CAACnE,QAAQ,GAC9B,KAAK,CAACsJ,UAAUC,GAChB,IAAI,CAACjF,KAAK,CAAG,IAAI,CAAC+C,QAAQ,KAG1B,IAAI,CAACoC,cAAc,GACnB/F,EAAU,IAAI,CAAE,kBAExB,CACJ,CAMAI,EAAW4F,cAAc,CAAG9F,EAAiBL,EAAUmG,cAAc,CAxUP3J,GAyU9D0D,EAAkBK,EAAWlF,SAAS,CAAE,CACpC+K,cAAe,KAAK,EACpBrK,WA34FiDM,CA44FrD,GACAR,IAA0IwK,kBAAkB,CAAC,OAAQ9F,GAaxI,IAAM9E,EAAoBE,IAG7C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}