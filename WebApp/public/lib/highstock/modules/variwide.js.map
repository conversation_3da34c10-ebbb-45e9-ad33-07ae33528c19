{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/variwide\n * @requires highcharts\n *\n * Highcharts variwide module\n *\n * (c) 2010-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/variwide\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/variwide\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ variwide_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/Variwide/VariwideComposition.js\n/* *\n *\n *  Highcharts variwide module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent, pushUnique, wrap } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction compose(AxisClass, TickClass) {\n    if (pushUnique(composed, 'Variwide')) {\n        const tickProto = TickClass.prototype;\n        addEvent(AxisClass, 'afterDrawCrosshair', onAxisAfterDrawCrosshair);\n        addEvent(AxisClass, 'afterRender', onAxisAfterRender);\n        addEvent(TickClass, 'afterGetPosition', onTickAfterGetPosition);\n        tickProto.postTranslate = tickPostTranslate;\n        wrap(tickProto, 'getLabelPosition', wrapTickGetLabelPosition);\n    }\n}\n/**\n * Same width as the category (#8083)\n * @private\n */\nfunction onAxisAfterDrawCrosshair(e) {\n    if (this.variwide && this.cross) {\n        this.cross.attr('stroke-width', e.point?.crosshairWidth);\n    }\n}\n/**\n * On a vertical axis, apply anti-collision logic to the labels.\n * @private\n */\nfunction onAxisAfterRender() {\n    const axis = this;\n    if (this.variwide) {\n        this.chart.labelCollectors.push(function () {\n            return axis.tickPositions\n                .filter((pos) => !!axis.ticks[pos].label)\n                .map((pos, i) => {\n                const label = axis.ticks[pos].label;\n                label.labelrank = axis.zData[i];\n                return label;\n            });\n        });\n    }\n}\n/**\n * @private\n */\nfunction onTickAfterGetPosition(e) {\n    const axis = this.axis, xOrY = axis.horiz ? 'x' : 'y';\n    if (axis.variwide) {\n        this[xOrY + 'Orig'] = e.pos[xOrY];\n        this.postTranslate(e.pos, xOrY, this.pos);\n    }\n}\n/**\n * @private\n */\nfunction tickPostTranslate(xy, xOrY, index) {\n    const axis = this.axis;\n    let pos = xy[xOrY] - axis.pos;\n    if (!axis.horiz) {\n        pos = axis.len - pos;\n    }\n    pos = axis.series[0].postTranslate(index, pos);\n    if (!axis.horiz) {\n        pos = axis.len - pos;\n    }\n    xy[xOrY] = axis.pos + pos;\n}\n/**\n * @private\n */\nfunction wrapTickGetLabelPosition(proceed, _x, _y, _label, horiz, \n/* eslint-disable @typescript-eslint/no-unused-vars */\n_labelOptions, _tickmarkOffset, _index\n/* eslint-enable @typescript-eslint/no-unused-vars */\n) {\n    const args = Array.prototype.slice.call(arguments, 1), xOrY = horiz ? 'x' : 'y';\n    // Replace the x with the original x\n    if (this.axis.variwide &&\n        typeof this[xOrY + 'Orig'] === 'number') {\n        args[horiz ? 0 : 1] = this[xOrY + 'Orig'];\n    }\n    const xy = proceed.apply(this, args);\n    // Post-translate\n    if (this.axis.variwide && this.axis.categories) {\n        this.postTranslate(xy, xOrY, this.pos);\n    }\n    return xy;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst VariwideComposition = {\n    compose\n};\n/* harmony default export */ const Variwide_VariwideComposition = (VariwideComposition);\n\n;// ./code/es-modules/Series/Variwide/VariwidePoint.js\n/* *\n *\n *  Highcharts variwide module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { column: { prototype: { pointClass: ColumnPoint } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { isNumber } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass VariwidePoint extends ColumnPoint {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    isValid() {\n        return isNumber(this.y) && isNumber(this.z);\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Variwide_VariwidePoint = (VariwidePoint);\n\n;// ./code/es-modules/Series/Variwide/VariwideSeriesDefaults.js\n/* *\n *\n *  Highcharts variwide module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A variwide chart (related to marimekko chart) is a column chart with a\n * variable width expressing a third dimension.\n *\n * @sample {highcharts} highcharts/demo/variwide/\n *         Variwide chart\n * @sample {highcharts} highcharts/series-variwide/inverted/\n *         Inverted variwide chart\n * @sample {highcharts} highcharts/series-variwide/datetime/\n *         Variwide columns on a datetime axis\n *\n * @extends      plotOptions.column\n * @since        6.0.0\n * @product      highcharts\n * @excluding    boostThreshold, crisp, depth, edgeColor, edgeWidth,\n *               groupZPadding, boostBlending\n * @requires     modules/variwide\n * @optionparent plotOptions.variwide\n */\nconst VariwideSeriesDefaults = {\n    /**\n     * In a variwide chart, the point padding is 0 in order to express the\n     * horizontal stacking of items.\n     */\n    pointPadding: 0,\n    /**\n     * In a variwide chart, the group padding is 0 in order to express the\n     * horizontal stacking of items.\n     */\n    groupPadding: 0\n};\n/**\n * A `variwide` series. If the [type](#series.variwide.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.variwide\n * @excluding boostThreshold, boostBlending\n * @product   highcharts\n * @requires  modules/variwide\n * @apioption series.variwide\n */\n/**\n * An array of data points for the series. For the `variwide` series type,\n * points can be given in the following ways:\n *\n * 1. An array of arrays with 3 or 2 values. In this case, the values correspond\n *    to `x,y,z`. If the first value is a string, it is applied as the name of\n *    the point, and the `x` value is inferred. The `x` value can also be\n *    omitted, in which case the inner arrays should be of length 2. Then the\n *    `x` value is automatically calculated, either starting at 0 and\n *    incremented by 1, or from `pointStart` and `pointInterval` given in the\n *    series options.\n *    ```js\n *       data: [\n *           [0, 1, 2],\n *           [1, 5, 5],\n *           [2, 0, 2]\n *       ]\n *    ```\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.variwide.turboThreshold), this option is not\n *    available.\n *    ```js\n *       data: [{\n *           x: 1,\n *           y: 1,\n *           z: 1,\n *           name: \"Point2\",\n *           color: \"#00FF00\"\n *       }, {\n *           x: 1,\n *           y: 5,\n *           z: 4,\n *           name: \"Point1\",\n *           color: \"#FF00FF\"\n *       }]\n *    ```\n *\n * @sample {highcharts} highcharts/series/data-array-of-arrays/\n *         Arrays of numeric x and y\n * @sample {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *         Arrays of datetime x and y\n * @sample {highcharts} highcharts/series/data-array-of-name-value/\n *         Arrays of point.name and y\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<Array<(number|string),number>|Array<(number|string),number,number>|*>}\n * @extends   series.line.data\n * @excluding marker\n * @product   highcharts\n * @apioption series.variwide.data\n */\n/**\n * The relative width for each column. On a category axis, the widths are\n * distributed so they sum up to the X axis length. On linear and datetime axes,\n * the columns will be laid out from the X value and Z units along the axis.\n *\n * @type      {number}\n * @product   highcharts\n * @apioption series.variwide.data.z\n */\n''; // Adds doclets above to transpiled file\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Variwide_VariwideSeriesDefaults = (VariwideSeriesDefaults);\n\n;// ./code/es-modules/Series/Variwide/VariwideSeries.js\n/* *\n *\n *  Highcharts variwide module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { column: ColumnSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\n\n\n\nconst { addEvent: VariwideSeries_addEvent, arrayMin, arrayMax, crisp, extend, merge, pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.variwide\n *\n * @augments Highcharts.Series\n */\nclass VariwideSeries extends ColumnSeries {\n    /* *\n     *\n     * Functions\n     *\n     * */\n    processData(force) {\n        this.totalZ = 0;\n        this.relZ = [];\n        highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().seriesTypes.column.prototype.processData.call(this, force);\n        const zData = this.getColumn('z');\n        (this.xAxis.reversed ?\n            zData.slice().reverse() :\n            zData).forEach(function (z, i) {\n            this.relZ[i] = this.totalZ;\n            this.totalZ += z;\n        }, this);\n        if (this.xAxis.categories) {\n            this.xAxis.variwide = true;\n            this.xAxis.zData = zData; // Used for label rank\n        }\n        return;\n    }\n    /**\n     * Translate an x value inside a given category index into the distorted\n     * axis translation.\n     *\n     * @private\n     * @function Highcharts.Series#postTranslate\n     *\n     * @param {number} index\n     *        The category index\n     *\n     * @param {number} x\n     *        The X pixel position in undistorted axis pixels\n     *\n     * @param {Highcharts.Point} point\n     *        For crosshairWidth for every point\n     *\n     * @return {number}\n     *         Distorted X position\n     */\n    postTranslate(index, x, point) {\n        const axis = this.xAxis, relZ = this.relZ, i = axis.reversed ? relZ.length - index : index, goRight = axis.reversed ? -1 : 1, minPx = axis.toPixels(axis.reversed ?\n            (axis.dataMax || 0) + axis.pointRange :\n            (axis.dataMin || 0)), maxPx = axis.toPixels(axis.reversed ?\n            (axis.dataMin || 0) :\n            (axis.dataMax || 0) + axis.pointRange), len = Math.abs(maxPx - minPx), totalZ = this.totalZ, left = this.chart.inverted ?\n            maxPx - (this.chart.plotTop - goRight * axis.minPixelPadding) :\n            minPx - this.chart.plotLeft - goRight * axis.minPixelPadding, linearSlotLeft = i / relZ.length * len, linearSlotRight = (i + goRight) / relZ.length * len, slotLeft = (pick(relZ[i], totalZ) / totalZ) * len, slotRight = (pick(relZ[i + goRight], totalZ) / totalZ) * len, xInsideLinearSlot = (x - (left + linearSlotLeft));\n        // Set crosshairWidth for every point (#8173)\n        if (point) {\n            point.crosshairWidth = slotRight - slotLeft;\n        }\n        return left + slotLeft +\n            xInsideLinearSlot * (slotRight - slotLeft) /\n                (linearSlotRight - linearSlotLeft);\n    }\n    /* eslint-enable valid-jsdoc */\n    translate() {\n        // Temporarily disable crisping when computing original shapeArgs\n        this.crispOption = this.options.crisp;\n        this.options.crisp = false;\n        super.translate();\n        // Reset option\n        this.options.crisp = this.crispOption;\n    }\n    /**\n     * Function that corrects stack labels positions\n     * @private\n     */\n    correctStackLabels() {\n        const series = this, options = series.options, yAxis = series.yAxis;\n        let pointStack, pointWidth, stack, xValue;\n        for (const point of series.points) {\n            xValue = point.x;\n            pointWidth = point.shapeArgs.width;\n            stack = yAxis.stacking.stacks[(series.negStacks &&\n                point.y < (options.startFromThreshold ?\n                    0 :\n                    options.threshold) ?\n                '-' :\n                '') + series.stackKey];\n            if (stack) {\n                pointStack = stack[xValue];\n                if (pointStack && !point.isNull) {\n                    pointStack.setOffset(-(pointWidth / 2) || 0, pointWidth || 0, void 0, void 0, point.plotX, series.xAxis);\n                }\n            }\n        }\n    }\n    getXExtremes(xData) {\n        const max = arrayMax(xData), maxZ = this.getColumn('z')[xData.indexOf(max)];\n        return {\n            min: arrayMin(xData),\n            max: max + (this.xAxis.categories ? 0 : maxZ)\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nVariwideSeries.compose = Variwide_VariwideComposition.compose;\nVariwideSeries.defaultOptions = merge(ColumnSeries.defaultOptions, Variwide_VariwideSeriesDefaults);\n// Extend translation by distorting X position based on Z.\nVariwideSeries_addEvent(VariwideSeries, 'afterColumnTranslate', function () {\n    // Temporarily disable crisping when computing original shapeArgs\n    const xAxis = this.xAxis, inverted = this.chart.inverted;\n    let i = -1;\n    // Distort the points to reflect z dimension\n    for (const point of this.points) {\n        ++i;\n        const shapeArgs = point.shapeArgs || {}, { x = 0, width = 0 } = shapeArgs, { plotX = 0, tooltipPos, z = 0 } = point;\n        let left, right;\n        if (xAxis.variwide) {\n            left = this.postTranslate(i, x, point);\n            right = this.postTranslate(i, x + width);\n            // For linear or datetime axes, the variwide column should start with X\n            // and extend Z units, without modifying the axis.\n        }\n        else {\n            left = plotX;\n            right = xAxis.translate(point.x + z, false, false, false, true);\n        }\n        if (this.crispOption) {\n            left = crisp(left, this.borderWidth);\n            right = crisp(right, this.borderWidth);\n        }\n        shapeArgs.x = left;\n        shapeArgs.width = Math.max(right - left, 1);\n        // Crosshair position (#8083)\n        point.plotX = (left + right) / 2;\n        // Adjust the tooltip position\n        if (tooltipPos) {\n            if (!inverted) {\n                tooltipPos[0] = shapeArgs.x + shapeArgs.width / 2;\n            }\n            else {\n                tooltipPos[1] = xAxis.len - shapeArgs.x - shapeArgs.width / 2;\n            }\n        }\n    }\n    if (this.options.stacking) {\n        this.correctStackLabels();\n    }\n}, { order: 2 });\nextend(VariwideSeries.prototype, {\n    irregularWidths: true,\n    keysAffectYAxis: ['y'],\n    pointArrayMap: ['y', 'z'],\n    parallelArrays: ['x', 'y', 'z'],\n    pointClass: Variwide_VariwidePoint\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('variwide', VariwideSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Variwide_VariwideSeries = (VariwideSeries);\n\n;// ./code/es-modules/masters/modules/variwide.js\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nVariwide_VariwideSeries.compose(G.Axis, G.Tick);\n/* harmony default export */ const variwide_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "variwide_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "composed", "addEvent", "pushUnique", "wrap", "onAxisAfterDrawCrosshair", "e", "variwide", "cross", "attr", "point", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onAxisAfterRender", "axis", "chart", "labelCollectors", "push", "tickPositions", "filter", "pos", "ticks", "label", "map", "i", "labelrank", "zData", "onTickAfterGetPosition", "xOrY", "horiz", "postTranslate", "tickPostTranslate", "xy", "index", "len", "series", "wrapTickGetLabelPosition", "proceed", "_x", "_y", "_label", "_labelOptions", "_tickmarkOffset", "_index", "args", "Array", "slice", "arguments", "apply", "categories", "column", "pointClass", "ColumnPoint", "seriesTypes", "isNumber", "ColumnSeries", "VariwideSeries_addEvent", "arrayMin", "arrayMax", "crisp", "extend", "merge", "pick", "VariwideSeries", "processData", "force", "totalZ", "relZ", "getColumn", "xAxis", "reversed", "reverse", "for<PERSON>ach", "z", "x", "length", "goRight", "minPx", "toPixels", "dataMax", "pointRange", "dataMin", "maxPx", "Math", "abs", "left", "inverted", "plotTop", "minPixelPadding", "plotLeft", "linearSlotLeft", "linearSlotRight", "slotLeft", "slotRight", "xInsideLinearSlot", "translate", "crispOption", "options", "correct<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pointStack", "pointWidth", "stack", "xValue", "yAxis", "points", "shapeArgs", "width", "stacking", "stacks", "negStacks", "y", "startFromThreshold", "threshold", "<PERSON><PERSON><PERSON>", "isNull", "setOffset", "plotX", "getXExtremes", "xData", "max", "maxZ", "indexOf", "min", "compose", "AxisClass", "TickClass", "tick<PERSON>roto", "defaultOptions", "pointPadding", "groupPadding", "right", "tooltipPos", "borderWidth", "order", "irregularWidths", "keysAffectYAxis", "pointArrayMap", "parallelArrays", "<PERSON><PERSON><PERSON><PERSON>", "registerSeriesType", "G", "Variwide_VariwideSeries", "Axis", "Tick"], "mappings": "CAWA,AAXA;;;;;;;;;;CAUC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,8BAA+B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACvH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,8BAA8B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAE3GA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAejL,GAAM,CAAEE,SAAAA,CAAQ,CAAE,CAAIH,IAEhB,CAAEI,SAAAA,CAAQ,CAAEC,WAAAA,CAAU,CAAEC,KAAAA,CAAI,CAAE,CAAIN,IAuBxC,SAASO,EAAyBC,CAAC,EAC3B,IAAI,CAACC,QAAQ,EAAI,IAAI,CAACC,KAAK,EAC3B,IAAI,CAACA,KAAK,CAACC,IAAI,CAAC,eAAgBH,EAAEI,KAAK,EAAEC,eAEjD,CAKA,SAASC,IACL,IAAMC,EAAO,IAAI,AACb,CAAA,IAAI,CAACN,QAAQ,EACb,IAAI,CAACO,KAAK,CAACC,eAAe,CAACC,IAAI,CAAC,WAC5B,OAAOH,EAAKI,aAAa,CACpBC,MAAM,CAAC,AAACC,GAAQ,CAAC,CAACN,EAAKO,KAAK,CAACD,EAAI,CAACE,KAAK,EACvCC,GAAG,CAAC,CAACH,EAAKI,KACX,IAAMF,EAAQR,EAAKO,KAAK,CAACD,EAAI,CAACE,KAAK,CAEnC,OADAA,EAAMG,SAAS,CAAGX,EAAKY,KAAK,CAACF,EAAE,CACxBF,CACX,EACJ,EAER,CAIA,SAASK,EAAuBpB,CAAC,EAC7B,IAAMO,EAAO,IAAI,CAACA,IAAI,CAAEc,EAAOd,EAAKe,KAAK,CAAG,IAAM,GAC9Cf,CAAAA,EAAKN,QAAQ,GACb,IAAI,CAACoB,EAAO,OAAO,CAAGrB,EAAEa,GAAG,CAACQ,EAAK,CACjC,IAAI,CAACE,aAAa,CAACvB,EAAEa,GAAG,CAAEQ,EAAM,IAAI,CAACR,GAAG,EAEhD,CAIA,SAASW,EAAkBC,CAAE,CAAEJ,CAAI,CAAEK,CAAK,EACtC,IAAMnB,EAAO,IAAI,CAACA,IAAI,CAClBM,EAAMY,CAAE,CAACJ,EAAK,CAAGd,EAAKM,GAAG,AACzB,AAACN,CAAAA,EAAKe,KAAK,EACXT,CAAAA,EAAMN,EAAKoB,GAAG,CAAGd,CAAE,EAEvBA,EAAMN,EAAKqB,MAAM,CAAC,EAAE,CAACL,aAAa,CAACG,EAAOb,GACtC,AAACN,EAAKe,KAAK,EACXT,CAAAA,EAAMN,EAAKoB,GAAG,CAAGd,CAAE,EAEvBY,CAAE,CAACJ,EAAK,CAAGd,EAAKM,GAAG,CAAGA,CAC1B,CAIA,SAASgB,EAAyBC,CAAO,CAAEC,CAAE,CAAEC,CAAE,CAAEC,CAAM,CAAEX,CAAK,CAEhEY,CAAa,CAAEC,CAAe,CAAEC,CAAM,EAGlC,IAAMC,EAAOC,MAAMpD,SAAS,CAACqD,KAAK,CAACnD,IAAI,CAACoD,UAAW,GAAInB,EAAOC,EAAQ,IAAM,GAExE,CAAA,IAAI,CAACf,IAAI,CAACN,QAAQ,EAClB,AAA+B,UAA/B,OAAO,IAAI,CAACoB,EAAO,OAAO,EAC1BgB,CAAAA,CAAI,CAACf,EAAAA,EAAc,CAAG,IAAI,CAACD,EAAO,OAAO,AAAD,EAE5C,IAAMI,EAAKK,EAAQW,KAAK,CAAC,IAAI,CAAEJ,GAK/B,OAHI,IAAI,CAAC9B,IAAI,CAACN,QAAQ,EAAI,IAAI,CAACM,IAAI,CAACmC,UAAU,EAC1C,IAAI,CAACnB,aAAa,CAACE,EAAIJ,EAAM,IAAI,CAACR,GAAG,EAElCY,CACX,CAyBA,GAAM,CAAEkB,OAAQ,CAAEzD,UAAW,CAAE0D,WAAYC,CAAW,CAAE,CAAE,CAAE,CAAG,AAACnD,IAA2IoD,WAAW,CAEhN,CAAEC,SAAAA,CAAQ,CAAE,CAAIvD,IAwKhB,CAAEmD,OAAQK,CAAY,CAAE,CAAG,AAACtD,IAA2IoD,WAAW,CAKlL,CAAElD,SAAUqD,CAAuB,CAAEC,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAE,CAAI/D,GAa/F,OAAMgE,UAAuBR,EAMzBS,YAAYC,CAAK,CAAE,CACf,IAAI,CAACC,MAAM,CAAG,EACd,IAAI,CAACC,IAAI,CAAG,EAAE,CACdlE,IAA0IoD,WAAW,CAACH,MAAM,CAACzD,SAAS,CAACuE,WAAW,CAACrE,IAAI,CAAC,IAAI,CAAEsE,GAC9L,IAAMvC,EAAQ,IAAI,CAAC0C,SAAS,CAAC,KAC7B,AAAC,CAAA,IAAI,CAACC,KAAK,CAACC,QAAQ,CAChB5C,EAAMoB,KAAK,GAAGyB,OAAO,GACrB7C,CAAI,EAAG8C,OAAO,CAAC,SAAUC,CAAC,CAAEjD,CAAC,EAC7B,IAAI,CAAC2C,IAAI,CAAC3C,EAAE,CAAG,IAAI,CAAC0C,MAAM,CAC1B,IAAI,CAACA,MAAM,EAAIO,CACnB,EAAG,IAAI,EACH,IAAI,CAACJ,KAAK,CAACpB,UAAU,GACrB,IAAI,CAACoB,KAAK,CAAC7D,QAAQ,CAAG,CAAA,EACtB,IAAI,CAAC6D,KAAK,CAAC3C,KAAK,CAAGA,EAG3B,CAoBAI,cAAcG,CAAK,CAAEyC,CAAC,CAAE/D,CAAK,CAAE,CAC3B,IAAMG,EAAO,IAAI,CAACuD,KAAK,CAAEF,EAAO,IAAI,CAACA,IAAI,CAAE3C,EAAIV,EAAKwD,QAAQ,CAAGH,EAAKQ,MAAM,CAAG1C,EAAQA,EAAO2C,EAAU9D,EAAKwD,QAAQ,CAAG,GAAK,EAAGO,EAAQ/D,EAAKgE,QAAQ,CAAChE,EAAKwD,QAAQ,CAC7J,AAACxD,CAAAA,EAAKiE,OAAO,EAAI,CAAA,EAAKjE,EAAKkE,UAAU,CACpClE,EAAKmE,OAAO,EAAI,GAAKC,EAAQpE,EAAKgE,QAAQ,CAAChE,EAAKwD,QAAQ,CACxDxD,EAAKmE,OAAO,EAAI,EACjB,AAACnE,CAAAA,EAAKiE,OAAO,EAAI,CAAA,EAAKjE,EAAKkE,UAAU,EAAG9C,EAAMiD,KAAKC,GAAG,CAACF,EAAQL,GAAQX,EAAS,IAAI,CAACA,MAAM,CAAEmB,EAAO,IAAI,CAACtE,KAAK,CAACuE,QAAQ,CACvHJ,EAAS,CAAA,IAAI,CAACnE,KAAK,CAACwE,OAAO,CAAGX,EAAU9D,EAAK0E,eAAe,AAAD,EAC3DX,EAAQ,IAAI,CAAC9D,KAAK,CAAC0E,QAAQ,CAAGb,EAAU9D,EAAK0E,eAAe,CAAEE,EAAiBlE,EAAI2C,EAAKQ,MAAM,CAAGzC,EAAKyD,EAAkB,AAACnE,CAAAA,EAAIoD,CAAM,EAAKT,EAAKQ,MAAM,CAAGzC,EAAK0D,EAAW,AAAC9B,EAAKK,CAAI,CAAC3C,EAAE,CAAE0C,GAAUA,EAAUhC,EAAK2D,EAAY,AAAC/B,EAAKK,CAAI,CAAC3C,EAAIoD,EAAQ,CAAEV,GAAUA,EAAUhC,EAK3Q,OAHIvB,GACAA,CAAAA,EAAMC,cAAc,CAAGiF,EAAYD,CAAO,EAEvCP,EAAOO,EACVE,AANiSpB,CAAAA,EAAKW,CAAAA,EAAOK,CAAa,CAAC,EAMtSG,CAAAA,EAAYD,CAAO,EACnCD,CAAAA,EAAkBD,CAAa,CAC5C,CAEAK,WAAY,CAER,IAAI,CAACC,WAAW,CAAG,IAAI,CAACC,OAAO,CAACtC,KAAK,CACrC,IAAI,CAACsC,OAAO,CAACtC,KAAK,CAAG,CAAA,EACrB,KAAK,CAACoC,YAEN,IAAI,CAACE,OAAO,CAACtC,KAAK,CAAG,IAAI,CAACqC,WAAW,AACzC,CAKAE,oBAAqB,CACjB,IACIC,EAAYC,EAAYC,EAAOC,EADdL,EAAU9D,AAAhB,IAAI,CAAmB8D,OAAO,CAAEM,EAAQpE,AAAxC,IAAI,CAA2CoE,KAAK,CAEnE,IAAK,IAAM5F,KAASwB,AAFL,IAAI,CAEQqE,MAAM,CAC7BF,EAAS3F,EAAM+D,CAAC,CAChB0B,EAAazF,EAAM8F,SAAS,CAACC,KAAK,CAClCL,CAAAA,EAAQE,EAAMI,QAAQ,CAACC,MAAM,CAAC,AAACzE,CAAAA,AALpB,IAAI,CAKuB0E,SAAS,EAC3ClG,EAAMmG,CAAC,CAAIb,CAAAA,EAAQc,kBAAkB,CACjC,EACAd,EAAQe,SAAS,AAAD,EACpB,IACA,EAAC,EAAK7E,AAVC,IAAI,CAUE8E,QAAQ,CAAC,AAAD,GAGjBd,AADJA,CAAAA,EAAaE,CAAK,CAACC,EAAO,AAAD,GACP,CAAC3F,EAAMuG,MAAM,EAC3Bf,EAAWgB,SAAS,CAAC,CAAEf,CAAAA,EAAa,CAAA,GAAM,EAAGA,GAAc,EAAG,KAAK,EAAG,KAAK,EAAGzF,EAAMyG,KAAK,CAAEjF,AAdxF,IAAI,CAc2FkC,KAAK,CAIvH,CACAgD,aAAaC,CAAK,CAAE,CAChB,IAAMC,EAAM7D,EAAS4D,GAAQE,EAAO,IAAI,CAACpD,SAAS,CAAC,IAAI,CAACkD,EAAMG,OAAO,CAACF,GAAK,CAC3E,MAAO,CACHG,IAAKjE,EAAS6D,GACdC,IAAKA,EAAO,CAAA,IAAI,CAAClD,KAAK,CAACpB,UAAU,CAAG,EAAIuE,CAAG,CAC/C,CACJ,CACJ,CAMAzD,EAAe4D,OAAO,CA/YtB,SAAiBC,CAAS,CAAEC,CAAS,EACjC,GAAIzH,EAAWF,EAAU,YAAa,CAClC,IAAM4H,EAAYD,EAAUpI,SAAS,CACrCU,EAASyH,EAAW,qBAAsBtH,GAC1CH,EAASyH,EAAW,cAAe/G,GACnCV,EAAS0H,EAAW,mBAAoBlG,GACxCmG,EAAUhG,aAAa,CAAGC,EAC1B1B,EAAKyH,EAAW,mBAAoB1F,EACxC,CACJ,EAuYA2B,EAAegE,cAAc,CAAGlE,EAAMN,EAAawE,cAAc,CAvOlC,CAK3BC,aAAc,EAKdC,aAAc,CAClB,GA8NAzE,EAAwBO,EAAgB,uBAAwB,WAE5D,IAAMM,EAAQ,IAAI,CAACA,KAAK,CAAEiB,EAAW,IAAI,CAACvE,KAAK,CAACuE,QAAQ,CACpD9D,EAAI,GAER,IAAK,IAAMb,KAAS,IAAI,CAAC6F,MAAM,CAAE,KAGzBnB,EAAM6C,CAFV,GAAE1G,EACF,IAAMiF,EAAY9F,EAAM8F,SAAS,EAAI,CAAC,EAAG,CAAE/B,EAAAA,EAAI,CAAC,CAAEgC,MAAAA,EAAQ,CAAC,CAAE,CAAGD,EAAW,CAAEW,MAAAA,EAAQ,CAAC,CAAEe,WAAAA,CAAU,CAAE1D,EAAAA,EAAI,CAAC,CAAE,CAAG9D,CAE1G0D,CAAAA,EAAM7D,QAAQ,EACd6E,EAAO,IAAI,CAACvD,aAAa,CAACN,EAAGkD,EAAG/D,GAChCuH,EAAQ,IAAI,CAACpG,aAAa,CAACN,EAAGkD,EAAIgC,KAKlCrB,EAAO+B,EACPc,EAAQ7D,EAAM0B,SAAS,CAACpF,EAAM+D,CAAC,CAAGD,EAAG,CAAA,EAAO,CAAA,EAAO,CAAA,EAAO,CAAA,IAE1D,IAAI,CAACuB,WAAW,GAChBX,EAAO1B,EAAM0B,EAAM,IAAI,CAAC+C,WAAW,EACnCF,EAAQvE,EAAMuE,EAAO,IAAI,CAACE,WAAW,GAEzC3B,EAAU/B,CAAC,CAAGW,EACdoB,EAAUC,KAAK,CAAGvB,KAAKoC,GAAG,CAACW,EAAQ7C,EAAM,GAEzC1E,EAAMyG,KAAK,CAAG,AAAC/B,CAAAA,EAAO6C,CAAI,EAAK,EAE3BC,IACK7C,EAID6C,CAAU,CAAC,EAAE,CAAG9D,EAAMnC,GAAG,CAAGuE,EAAU/B,CAAC,CAAG+B,EAAUC,KAAK,CAAG,EAH5DyB,CAAU,CAAC,EAAE,CAAG1B,EAAU/B,CAAC,CAAG+B,EAAUC,KAAK,CAAG,EAM5D,CACI,IAAI,CAACT,OAAO,CAACU,QAAQ,EACrB,IAAI,CAACT,kBAAkB,EAE/B,EAAG,CAAEmC,MAAO,CAAE,GACdzE,EAAOG,EAAetE,SAAS,CAAE,CAC7B6I,gBAAiB,CAAA,EACjBC,gBAAiB,CAAC,IAAI,CACtBC,cAAe,CAAC,IAAK,IAAI,CACzBC,eAAgB,CAAC,IAAK,IAAK,IAAI,CAC/BtF,WA7UJ,cAA4BC,EAMxBsF,SAAU,CACN,OAAOpF,EAAS,IAAI,CAACwD,CAAC,GAAKxD,EAAS,IAAI,CAACmB,CAAC,CAC9C,CACJ,CAqUA,GACAxE,IAA0I0I,kBAAkB,CAAC,WAAY5E,GAazK,IAAM6E,EAAK7I,IACX8I,AAR8D9E,EAQtC4D,OAAO,CAACiB,EAAEE,IAAI,CAAEF,EAAEG,IAAI,EACjB,IAAMlJ,EAAiBE,IAG1C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}