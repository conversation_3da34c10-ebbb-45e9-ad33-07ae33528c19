{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/windbarb\n * @requires highcharts\n *\n * Wind barb series module\n *\n * (c) 2010-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"dataGrouping\"][\"approximations\"], root[\"_Highcharts\"][\"Series\"][\"types\"][\"column\"], root[\"_Highcharts\"][\"Series\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/windbarb\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"dataGrouping\"],[\"approximations\"],amd1[\"Series\"],[\"types\"],[\"column\"],amd1[\"Series\"],amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/windbarb\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"dataGrouping\"][\"approximations\"], root[\"_Highcharts\"][\"Series\"][\"types\"][\"column\"], root[\"_Highcharts\"][\"Series\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"dataGrouping\"][\"approximations\"], root[\"Highcharts\"][\"Series\"][\"types\"][\"column\"], root[\"Highcharts\"][\"Series\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__956__, __WEBPACK_EXTERNAL_MODULE__448__, __WEBPACK_EXTERNAL_MODULE__820__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 448:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__448__;\n\n/***/ }),\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 820:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__820__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ }),\n\n/***/ 956:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__956__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ windbarb_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"dataGrouping\",\"approximations\"],\"commonjs\":[\"highcharts\",\"dataGrouping\",\"approximations\"],\"commonjs2\":[\"highcharts\",\"dataGrouping\",\"approximations\"],\"root\":[\"Highcharts\",\"dataGrouping\",\"approximations\"]}\nvar highcharts_dataGrouping_approximations_commonjs_highcharts_dataGrouping_approximations_commonjs2_highcharts_dataGrouping_approximations_root_Highcharts_dataGrouping_approximations_ = __webpack_require__(956);\nvar highcharts_dataGrouping_approximations_commonjs_highcharts_dataGrouping_approximations_commonjs2_highcharts_dataGrouping_approximations_root_Highcharts_dataGrouping_approximations_default = /*#__PURE__*/__webpack_require__.n(highcharts_dataGrouping_approximations_commonjs_highcharts_dataGrouping_approximations_commonjs2_highcharts_dataGrouping_approximations_root_Highcharts_dataGrouping_approximations_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Series\",\"types\",\"column\"],\"commonjs\":[\"highcharts\",\"Series\",\"types\",\"column\"],\"commonjs2\":[\"highcharts\",\"Series\",\"types\",\"column\"],\"root\":[\"Highcharts\",\"Series\",\"types\",\"column\"]}\nvar highcharts_Series_types_column_commonjs_highcharts_Series_types_column_commonjs2_highcharts_Series_types_column_root_Highcharts_Series_types_column_ = __webpack_require__(448);\nvar highcharts_Series_types_column_commonjs_highcharts_Series_types_column_commonjs2_highcharts_Series_types_column_root_Highcharts_Series_types_column_default = /*#__PURE__*/__webpack_require__.n(highcharts_Series_types_column_commonjs_highcharts_Series_types_column_commonjs2_highcharts_Series_types_column_root_Highcharts_Series_types_column_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Series\"],\"commonjs\":[\"highcharts\",\"Series\"],\"commonjs2\":[\"highcharts\",\"Series\"],\"root\":[\"Highcharts\",\"Series\"]}\nvar highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_ = __webpack_require__(820);\nvar highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default = /*#__PURE__*/__webpack_require__.n(highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_);\n;// ./code/es-modules/Series/OnSeriesComposition.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nconst { prototype: columnProto } = (highcharts_Series_types_column_commonjs_highcharts_Series_types_column_commonjs2_highcharts_Series_types_column_root_Highcharts_Series_types_column_default());\n\nconst { prototype: seriesProto } = (highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default());\n\nconst { defined, pushUnique, stableSort } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\nvar OnSeriesComposition;\n(function (OnSeriesComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    function compose(SeriesClass) {\n        if (pushUnique(composed, 'OnSeries')) {\n            const seriesProto = SeriesClass.prototype;\n            seriesProto.getPlotBox = getPlotBox;\n            seriesProto.translate = translate;\n        }\n        return SeriesClass;\n    }\n    OnSeriesComposition.compose = compose;\n    /**\n     * Override getPlotBox. If the onSeries option is valid, return the plot box\n     * of the onSeries, otherwise proceed as usual.\n     *\n     * @private\n     */\n    function getPlotBox(name) {\n        return seriesProto.getPlotBox.call((this.options.onSeries &&\n            this.chart.get(this.options.onSeries)) || this, name);\n    }\n    OnSeriesComposition.getPlotBox = getPlotBox;\n    /**\n     * Extend the translate method by placing the point on the related series\n     *\n     * @private\n     */\n    function translate() {\n        columnProto.translate.apply(this);\n        const series = this, options = series.options, chart = series.chart, points = series.points, optionsOnSeries = options.onSeries, onSeries = (optionsOnSeries &&\n            chart.get(optionsOnSeries)), step = onSeries && onSeries.options.step, onData = (onSeries && onSeries.points), inverted = chart.inverted, xAxis = series.xAxis, yAxis = series.yAxis;\n        let cursor = points.length - 1, point, lastPoint, onKey = options.onKey || 'y', i = onData && onData.length, xOffset = 0, leftPoint, lastX, rightPoint, currentDataGrouping, distanceRatio;\n        // Relate to a master series\n        if (onSeries && onSeries.visible && i) {\n            xOffset = (onSeries.pointXOffset || 0) + (onSeries.barW || 0) / 2;\n            currentDataGrouping = onSeries.currentDataGrouping;\n            lastX = (onData[i - 1].x +\n                (currentDataGrouping ? currentDataGrouping.totalRange : 0)); // #2374\n            // sort the data points\n            stableSort(points, (a, b) => (a.x - b.x));\n            onKey = 'plot' + onKey[0].toUpperCase() + onKey.substr(1);\n            while (i-- && points[cursor]) {\n                leftPoint = onData[i];\n                point = points[cursor];\n                point.y = leftPoint.y;\n                if (leftPoint.x <= point.x &&\n                    typeof leftPoint[onKey] !== 'undefined') {\n                    if (point.x <= lastX) { // #803\n                        point.plotY = leftPoint[onKey];\n                        // Interpolate between points, #666\n                        if (leftPoint.x < point.x &&\n                            !step) {\n                            rightPoint = onData[i + 1];\n                            if (rightPoint &&\n                                typeof rightPoint[onKey] !== 'undefined') {\n                                // If the series is spline, calculate Y of the\n                                // point on the bezier line. #19264\n                                if (defined(point.plotX) &&\n                                    onSeries.is('spline')) {\n                                    leftPoint = leftPoint;\n                                    rightPoint = rightPoint;\n                                    const p0 = [\n                                        leftPoint.plotX || 0,\n                                        leftPoint.plotY || 0\n                                    ], p3 = [\n                                        rightPoint.plotX || 0,\n                                        rightPoint.plotY || 0\n                                    ], p1 = (leftPoint.controlPoints?.high ||\n                                        p0), p2 = (rightPoint.controlPoints?.low ||\n                                        p3), pixelThreshold = 0.25, maxIterations = 100, calculateCoord = (t, key) => (\n                                    // The parametric formula for the\n                                    // cubic Bezier curve.\n                                    Math.pow(1 - t, 3) * p0[key] +\n                                        3 * (1 - t) * (1 - t) * t *\n                                            p1[key] + 3 * (1 - t) * t * t *\n                                        p2[key] + t * t * t * p3[key]);\n                                    let tMin = 0, tMax = 1, t;\n                                    // Find `t` of the parametric function of\n                                    // the bezier curve for the given `plotX`.\n                                    for (let i = 0; i < maxIterations; i++) {\n                                        const tMid = (tMin + tMax) / 2;\n                                        const xMid = calculateCoord(tMid, 0);\n                                        if (xMid === null) {\n                                            break;\n                                        }\n                                        if (Math.abs(xMid - point.plotX) < pixelThreshold) {\n                                            t = tMid;\n                                            break;\n                                        }\n                                        if (xMid < point.plotX) {\n                                            tMin = tMid;\n                                        }\n                                        else {\n                                            tMax = tMid;\n                                        }\n                                    }\n                                    if (defined(t)) {\n                                        point.plotY =\n                                            calculateCoord(t, 1);\n                                        point.y =\n                                            yAxis.toValue(point.plotY, true);\n                                    }\n                                }\n                                else {\n                                    // The distance ratio, between 0 and 1\n                                    distanceRatio =\n                                        (point.x - leftPoint.x) /\n                                            (rightPoint.x - leftPoint.x);\n                                    point.plotY +=\n                                        distanceRatio *\n                                            // The plotY distance\n                                            (rightPoint[onKey] - leftPoint[onKey]);\n                                    point.y +=\n                                        distanceRatio *\n                                            (rightPoint.y - leftPoint.y);\n                                }\n                            }\n                        }\n                    }\n                    cursor--;\n                    i++; // Check again for points in the same x position\n                    if (cursor < 0) {\n                        break;\n                    }\n                }\n            }\n        }\n        // Add plotY position and handle stacking\n        points.forEach((point, i) => {\n            let stackIndex;\n            point.plotX += xOffset; // #2049\n            // Undefined plotY means the point is either on axis, outside series\n            // range or hidden series. If the series is outside the range of the\n            // x axis it should fall through with an undefined plotY, but then\n            // we must remove the shapeArgs (#847). For inverted charts, we need\n            // to calculate position anyway, because series.invertGroups is not\n            // defined\n            if (typeof point.plotY === 'undefined' || inverted) {\n                if (point.plotX >= 0 &&\n                    point.plotX <= xAxis.len) {\n                    // We're inside xAxis range\n                    if (inverted) {\n                        point.plotY = xAxis.translate(point.x, 0, 1, 0, 1);\n                        point.plotX = defined(point.y) ?\n                            yAxis.translate(point.y, 0, 0, 0, 1) :\n                            0;\n                    }\n                    else {\n                        point.plotY = (xAxis.opposite ? 0 : series.yAxis.len) +\n                            xAxis.offset; // For the windbarb demo\n                    }\n                }\n                else {\n                    point.shapeArgs = {}; // 847\n                }\n            }\n            // If multiple flags appear at the same x, order them into a stack\n            lastPoint = points[i - 1];\n            if (lastPoint && lastPoint.plotX === point.plotX) {\n                if (typeof lastPoint.stackIndex === 'undefined') {\n                    lastPoint.stackIndex = 0;\n                }\n                stackIndex = lastPoint.stackIndex + 1;\n            }\n            point.stackIndex = stackIndex; // #3639\n        });\n        this.onSeries = onSeries;\n    }\n    OnSeriesComposition.translate = translate;\n})(OnSeriesComposition || (OnSeriesComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Series_OnSeriesComposition = (OnSeriesComposition);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/Windbarb/WindbarbPoint.js\n/* *\n *\n *  Wind barb series module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { isNumber } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass WindbarbPoint extends (highcharts_Series_types_column_commonjs_highcharts_Series_types_column_commonjs2_highcharts_Series_types_column_root_Highcharts_Series_types_column_default()).prototype.pointClass {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    isValid() {\n        return isNumber(this.value) && this.value >= 0;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Windbarb_WindbarbPoint = (WindbarbPoint);\n\n;// ./code/es-modules/Series/Windbarb/WindbarbSeriesDefaults.js\n/* *\n *\n *  Wind barb series module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Wind barbs are a convenient way to represent wind speed and direction in\n * one graphical form. Wind direction is given by the stem direction, and\n * wind speed by the number and shape of barbs.\n *\n * @sample {highcharts|highstock} highcharts/demo/windbarb-series/\n *         Wind barb series\n *\n * @extends      plotOptions.column\n * @excluding    boostThreshold, marker, connectEnds, connectNulls,\n *               cropThreshold, dashStyle, dragDrop, gapSize, gapUnit,\n *               linecap, shadow, stacking, step, boostBlending\n * @since        6.0.0\n * @product      highcharts highstock\n * @requires     modules/windbarb\n * @optionparent plotOptions.windbarb\n */\nconst WindbarbSeriesDefaults = {\n    /**\n     * Data grouping options for the wind barbs. In Highcharts, this\n     * requires the `modules/datagrouping.js` module to be loaded. In\n     * Highcharts Stock, data grouping is included.\n     *\n     * @sample  highcharts/plotoptions/windbarb-datagrouping\n     *          Wind barb with data grouping\n     *\n     * @since   7.1.0\n     * @product highcharts highstock\n     */\n    dataGrouping: {\n        /**\n         * Whether to enable data grouping.\n         *\n         * @product highcharts highstock\n         */\n        enabled: true,\n        /**\n         * Approximation function for the data grouping. The default\n         * returns an average of wind speed and a vector average direction\n         * weighted by wind speed.\n         *\n         * @product highcharts highstock\n         *\n         * @type {string|Function}\n         */\n        approximation: 'windbarb',\n        /**\n         * The approximate data group width.\n         *\n         * @product highcharts highstock\n         */\n        groupPixelWidth: 30\n    },\n    /**\n     * The line width of the wind barb symbols.\n     */\n    lineWidth: 2,\n    /**\n     * The id of another series in the chart that the wind barbs are\n     * projected on. When `null`, the wind symbols are drawn on the X axis,\n     * but offset up or down by the `yOffset` setting.\n     *\n     * @sample {highcharts|highstock} highcharts/plotoptions/windbarb-onseries\n     *         Projected on area series\n     *\n     * @type {string|null}\n     */\n    onSeries: null,\n    states: {\n        hover: {\n            lineWidthPlus: 0\n        }\n    },\n    tooltip: {\n        /**\n         * The default point format for the wind barb tooltip. Note the\n         * `point.beaufort` property that refers to the Beaufort wind scale.\n         * The names can be internationalized by modifying\n         * `Highcharts.seriesTypes.windbarb.prototype.beaufortNames`.\n         */\n        pointFormat: '<span style=\"color:{point.color}\">\\u25CF</span> {series.name}: <b>{point.value}</b> ({point.beaufort})<br/>'\n    },\n    /**\n     * Pixel length of the stems.\n     */\n    vectorLength: 20,\n    /**\n     * @default   value\n     */\n    colorKey: 'value',\n    /**\n     * Vertical offset from the cartesian position, in pixels. The default\n     * value makes sure the symbols don't overlap the X axis when `onSeries`\n     * is `null`, and that they don't overlap the linked series when\n     * `onSeries` is given.\n     */\n    yOffset: -20,\n    /**\n     * Horizontal offset from the cartesian position, in pixels. When the\n     * chart is inverted, this option allows translation like\n     * [yOffset](#plotOptions.windbarb.yOffset) in non inverted charts.\n     *\n     * @since 6.1.0\n     */\n    xOffset: 0\n};\n/**\n * A `windbarb` series. If the [type](#series.windbarb.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.windbarb\n * @excluding dataParser, dataURL, boostThreshold, boostBlending\n * @product   highcharts highstock\n * @requires  modules/windbarb\n * @apioption series.windbarb\n */\n/**\n * An array of data points for the series. For the `windbarb` series type,\n * points can be given in the following ways:\n *\n * 1. An array of arrays with 3 values. In this case, the values correspond to\n *    `x,value,direction`. If the first value is a string, it is applied as the\n *    name of the point, and the `x` value is inferred.\n *    ```js\n *       data: [\n *           [Date.UTC(2017, 0, 1, 0), 3.3, 90],\n *           [Date.UTC(2017, 0, 1, 1), 12.1, 180],\n *           [Date.UTC(2017, 0, 1, 2), 11.1, 270]\n *       ]\n *    ```\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.area.turboThreshold), this option is not\n *    available.\n *    ```js\n *       data: [{\n *           x: Date.UTC(2017, 0, 1, 0),\n *           value: 12.1,\n *           direction: 90\n *       }, {\n *           x: Date.UTC(2017, 0, 1, 1),\n *           value: 11.1,\n *           direction: 270\n *       }]\n *    ```\n *\n * @sample {highcharts} highcharts/chart/reflow-true/\n *         Numerical values\n * @sample {highcharts} highcharts/series/data-array-of-arrays/\n *         Arrays of numeric x and y\n * @sample {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *         Arrays of datetime x and y\n * @sample {highcharts} highcharts/series/data-array-of-name-value/\n *         Arrays of point.name and y\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<Array<(number|string),number,number>|*>}\n * @extends   series.line.data\n * @product   highcharts highstock\n * @apioption series.windbarb.data\n */\n/**\n * The wind speed in meters per second.\n *\n * @type      {number|null}\n * @product   highcharts highstock\n * @apioption series.windbarb.data.value\n */\n/**\n * The wind direction in degrees, where 0 is north (pointing towards south).\n *\n * @type      {number}\n * @product   highcharts highstock\n * @apioption series.windbarb.data.direction\n */\n''; // Adds doclets above to transpiled file\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Windbarb_WindbarbSeriesDefaults = (WindbarbSeriesDefaults);\n\n;// ./code/es-modules/Series/Windbarb/WindbarbSeries.js\n/* *\n *\n *  Wind barb series module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { animObject } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\n\n\nconst { column: ColumnSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { extend, merge, pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Once off, register the windbarb approximation for data grouping. This can\n * be called anywhere (not necessarily in the translate function), but must\n * happen after the data grouping module is loaded and before the\n * wind barb series uses it.\n * @private\n */\nfunction registerApproximation() {\n    if (!(highcharts_dataGrouping_approximations_commonjs_highcharts_dataGrouping_approximations_commonjs2_highcharts_dataGrouping_approximations_root_Highcharts_dataGrouping_approximations_default()).windbarb) {\n        (highcharts_dataGrouping_approximations_commonjs_highcharts_dataGrouping_approximations_commonjs2_highcharts_dataGrouping_approximations_root_Highcharts_dataGrouping_approximations_default()).windbarb = (values, directions) => {\n            let vectorX = 0, vectorY = 0;\n            for (let i = 0, iEnd = values.length; i < iEnd; i++) {\n                vectorX += values[i] * Math.cos(directions[i] * (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).deg2rad);\n                vectorY += values[i] * Math.sin(directions[i] * (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).deg2rad);\n            }\n            return [\n                // Wind speed\n                values.reduce((sum, value) => (sum + value), 0) / values.length,\n                // Wind direction\n                Math.atan2(vectorY, vectorX) / (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).deg2rad\n            ];\n        };\n    }\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.windbarb\n *\n * @augments Highcharts.Series\n */\nclass WindbarbSeries extends ColumnSeries {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    init(chart, options) {\n        super.init(chart, options);\n    }\n    // Get presentational attributes.\n    pointAttribs(point, state) {\n        const options = this.options;\n        let stroke = point.color || this.color, strokeWidth = this.options.lineWidth;\n        if (state) {\n            stroke = options.states[state].color || stroke;\n            strokeWidth =\n                (options.states[state].lineWidth || strokeWidth) +\n                    (options.states[state].lineWidthPlus || 0);\n        }\n        return {\n            'stroke': stroke,\n            'stroke-width': strokeWidth\n        };\n    }\n    // Create a single wind arrow. It is later rotated around the zero\n    // centerpoint.\n    windArrow(point) {\n        const level = point.beaufortLevel, u = this.options.vectorLength / 20;\n        let knots = point.value * 1.943844, barbs, pos = -10;\n        if (point.isNull) {\n            return [];\n        }\n        if (level === 0) {\n            return this.chart.renderer.symbols.circle(-10 * u, -10 * u, 20 * u, 20 * u);\n        }\n        // The stem and the arrow head\n        const path = [\n            ['M', 0, 7 * u], // Base of arrow\n            ['L', -1.5 * u, 7 * u],\n            ['L', 0, 10 * u],\n            ['L', 1.5 * u, 7 * u],\n            ['L', 0, 7 * u],\n            ['L', 0, -10 * u] // Top\n        ];\n        // For each full 50 knots, add a pennant\n        barbs = (knots - knots % 50) / 50; // Pennants\n        if (barbs > 0) {\n            while (barbs--) {\n                path.push(pos === -10 ? ['L', 0, pos * u] : ['M', 0, pos * u], ['L', 5 * u, pos * u + 2], ['L', 0, pos * u + 4]);\n                // Substract from the rest and move position for next\n                knots -= 50;\n                pos += 7;\n            }\n        }\n        // For each full 10 knots, add a full barb\n        barbs = (knots - knots % 10) / 10;\n        if (barbs > 0) {\n            while (barbs--) {\n                path.push(pos === -10 ? ['L', 0, pos * u] : ['M', 0, pos * u], ['L', 7 * u, pos * u]);\n                knots -= 10;\n                pos += 3;\n            }\n        }\n        // For each full 5 knots, add a half barb\n        barbs = (knots - knots % 5) / 5; // Half barbs\n        if (barbs > 0) {\n            while (barbs--) {\n                path.push(pos === -10 ? ['L', 0, pos * u] : ['M', 0, pos * u], ['L', 4 * u, pos * u]);\n                knots -= 5;\n                pos += 3;\n            }\n        }\n        return path;\n    }\n    drawPoints() {\n        const chart = this.chart, yAxis = this.yAxis, inverted = chart.inverted, shapeOffset = this.options.vectorLength / 2;\n        for (const point of this.points) {\n            const plotX = point.plotX, plotY = point.plotY;\n            // Check if it's inside the plot area, but only for the X\n            // dimension.\n            if (this.options.clip === false ||\n                chart.isInsidePlot(plotX, 0)) {\n                // Create the graphic the first time\n                if (!point.graphic) {\n                    point.graphic = this.chart.renderer\n                        .path()\n                        .add(this.markerGroup)\n                        .addClass('highcharts-point ' +\n                        'highcharts-color-' +\n                        pick(point.colorIndex, point.series.colorIndex));\n                }\n                // Position the graphic\n                point.graphic\n                    .attr({\n                    d: this.windArrow(point),\n                    translateX: plotX + this.options.xOffset,\n                    translateY: plotY + this.options.yOffset,\n                    rotation: point.direction\n                });\n                if (!this.chart.styledMode) {\n                    point.graphic\n                        .attr(this.pointAttribs(point));\n                }\n            }\n            else if (point.graphic) {\n                point.graphic = point.graphic.destroy();\n            }\n            // Set the tooltip anchor position\n            point.tooltipPos = [\n                plotX + this.options.xOffset +\n                    (inverted && !this.onSeries ? shapeOffset : 0),\n                plotY + this.options.yOffset -\n                    (inverted ?\n                        0 :\n                        shapeOffset + yAxis.pos - chart.plotTop)\n            ]; // #6327\n        }\n    }\n    // Fade in the arrows on initializing series.\n    animate(init) {\n        if (init) {\n            this.markerGroup.attr({\n                opacity: 0.01\n            });\n        }\n        else {\n            this.markerGroup.animate({\n                opacity: 1\n            }, animObject(this.options.animation));\n        }\n    }\n    markerAttribs() {\n        return {};\n    }\n    getExtremes() {\n        return {};\n    }\n    shouldShowTooltip(plotX, plotY, options = {}) {\n        options.ignoreX = this.chart.inverted;\n        options.ignoreY = !options.ignoreX;\n        return super.shouldShowTooltip(plotX, plotY, options);\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nWindbarbSeries.defaultOptions = merge(ColumnSeries.defaultOptions, Windbarb_WindbarbSeriesDefaults);\nSeries_OnSeriesComposition.compose(WindbarbSeries);\nextend(WindbarbSeries.prototype, {\n    beaufortFloor: [\n        0, 0.3, 1.6, 3.4, 5.5, 8.0, 10.8, 13.9, 17.2, 20.8,\n        24.5, 28.5, 32.7\n    ], // @todo dictionary with names?\n    beaufortName: [\n        'Calm', 'Light air', 'Light breeze',\n        'Gentle breeze', 'Moderate breeze', 'Fresh breeze',\n        'Strong breeze', 'Near gale', 'Gale', 'Strong gale', 'Storm',\n        'Violent storm', 'Hurricane'\n    ],\n    invertible: false,\n    parallelArrays: ['x', 'value', 'direction'],\n    pointArrayMap: ['value', 'direction'],\n    pointClass: Windbarb_WindbarbPoint,\n    trackerGroups: ['markerGroup'],\n    translate: function () {\n        const beaufortFloor = this.beaufortFloor, beaufortName = this.beaufortName;\n        Series_OnSeriesComposition.translate.call(this);\n        for (const point of this.points) {\n            let level = 0;\n            // Find the beaufort level (zero based)\n            for (; level < beaufortFloor.length; level++) {\n                if (beaufortFloor[level] > point.value) {\n                    break;\n                }\n            }\n            point.beaufortLevel = level - 1;\n            point.beaufort = beaufortName[level - 1];\n        }\n    }\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('windbarb', WindbarbSeries);\nregisterApproximation();\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Windbarb_WindbarbSeries = ((/* unused pure expression or super */ null && (WindbarbSeries)));\n\n;// ./code/es-modules/masters/modules/windbarb.js\n\n\n\n\n/* harmony default export */ const windbarb_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__956__", "__WEBPACK_EXTERNAL_MODULE__448__", "__WEBPACK_EXTERNAL_MODULE__820__", "__WEBPACK_EXTERNAL_MODULE__512__", "OnSeriesComposition", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "windbarb_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_dataGrouping_approximations_commonjs_highcharts_dataGrouping_approximations_commonjs2_highcharts_dataGrouping_approximations_root_Highcharts_dataGrouping_approximations_", "highcharts_dataGrouping_approximations_commonjs_highcharts_dataGrouping_approximations_commonjs2_highcharts_dataGrouping_approximations_root_Highcharts_dataGrouping_approximations_default", "highcharts_Series_types_column_commonjs_highcharts_Series_types_column_commonjs2_highcharts_Series_types_column_root_Highcharts_Series_types_column_", "highcharts_Series_types_column_commonjs_highcharts_Series_types_column_commonjs2_highcharts_Series_types_column_root_Highcharts_Series_types_column_default", "highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_", "highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default", "composed", "columnProto", "seriesProto", "defined", "pushUnique", "stableSort", "getPlotBox", "name", "options", "onSeries", "chart", "translate", "apply", "series", "points", "optionsOnSeries", "step", "onData", "inverted", "xAxis", "yAxis", "cursor", "length", "point", "lastPoint", "onKey", "i", "xOffset", "leftPoint", "lastX", "rightPoint", "currentDataGrouping", "distanceRatio", "visible", "pointXOffset", "barW", "x", "totalRange", "b", "toUpperCase", "substr", "y", "plotY", "plotX", "is", "p0", "p3", "p1", "controlPoints", "high", "p2", "low", "calculateCoord", "t", "Math", "pow", "tMin", "tMax", "tMid", "xMid", "abs", "toValue", "for<PERSON>ach", "stackIndex", "len", "opposite", "offset", "shapeArgs", "compose", "SeriesClass", "Series_OnSeriesComposition", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "isNumber", "WindbarbPoint", "pointClass", "<PERSON><PERSON><PERSON><PERSON>", "value", "animObject", "column", "ColumnSeries", "seriesTypes", "extend", "merge", "pick", "WindbarbSeries", "init", "pointAttribs", "state", "stroke", "color", "strokeWidth", "lineWidth", "states", "lineWidthPlus", "windArrow", "level", "beaufortLevel", "u", "vectorLength", "knots", "barbs", "pos", "isNull", "renderer", "symbols", "circle", "path", "push", "drawPoints", "shapeOffset", "clip", "isInsidePlot", "graphic", "add", "markerGroup", "addClass", "colorIndex", "attr", "translateX", "translateY", "yOffset", "rotation", "direction", "styledMode", "destroy", "tooltipPos", "plotTop", "animate", "opacity", "animation", "markerAttribs", "getExtremes", "shouldShowTooltip", "ignoreX", "ignoreY", "defaultOptions", "dataGrouping", "enabled", "approximation", "groupPixelWidth", "hover", "tooltip", "pointFormat", "colorKey", "beaufortFloor", "beaufort<PERSON><PERSON>", "invertible", "parallelArrays", "pointArrayMap", "trackerGroups", "beaufort", "registerSeriesType", "windbarb", "values", "directions", "vectorX", "vectorY", "iEnd", "cos", "deg2rad", "sin", "reduce", "sum", "atan2"], "mappings": "CAWA,AAXA;;;;;;;;;;CAUC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,YAAe,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,MAAS,CAAC,KAAQ,CAAC,MAAS,CAAEA,EAAK,WAAc,CAAC,MAAS,CAAEA,EAAK,WAAc,CAAC,cAAiB,EACpN,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,8BAA+B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,YAAe,CAAC,CAAC,iBAAiB,CAACA,EAAK,MAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAACA,EAAK,MAAS,CAACA,EAAK,cAAiB,CAAE,GAClN,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,8BAA8B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,YAAe,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,MAAS,CAAC,KAAQ,CAAC,MAAS,CAAEA,EAAK,WAAc,CAAC,MAAS,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAEnPA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,YAAe,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,MAAS,CAAC,KAAQ,CAAC,MAAS,CAAEA,EAAK,UAAa,CAAC,MAAS,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAC5N,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,IAC3K,AAAC,CAAA,KACP,aACA,IAyINC,EAzIUC,EAAuB,CAE/B,IACC,AAACX,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGU,CAEX,EAEA,IACC,AAACT,IAERA,EAAOD,OAAO,CAAGS,CAEX,EAEA,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,AAACL,IAERA,EAAOD,OAAO,CAAGO,CAEX,CAEI,EAGIM,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAahB,OAAO,CAG5B,IAAIC,EAASY,CAAwB,CAACE,EAAS,CAAG,CAGjDf,QAAS,CAAC,CACX,EAMA,OAHAY,CAAmB,CAACG,EAAS,CAACd,EAAQA,EAAOD,OAAO,CAAEc,GAG/Cb,EAAOD,OAAO,AACtB,CAMCc,EAAoBI,CAAC,CAAG,AAACjB,IACxB,IAAIkB,EAASlB,GAAUA,EAAOmB,UAAU,CACvC,IAAOnB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAa,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACrB,EAASuB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACzB,EAASwB,IAC5EE,OAAOC,cAAc,CAAC3B,EAASwB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAuLzB,EAAoB,KAC3M0B,EAA2M1B,EAAoBI,CAAC,CAACqB,GAEjOE,EAAuJ3B,EAAoB,KAC3K4B,EAA2K5B,EAAoBI,CAAC,CAACuB,GAEjME,EAAmG7B,EAAoB,KACvH8B,EAAuH9B,EAAoBI,CAAC,CAACyB,GAcjJ,GAAM,CAAEE,SAAAA,CAAQ,CAAE,CAAIP,IAChB,CAAEN,UAAWc,CAAW,CAAE,CAAIJ,IAE9B,CAAEV,UAAWe,CAAW,CAAE,CAAIH,IAE9B,CAAEI,QAAAA,CAAO,CAAEC,WAAAA,CAAU,CAAEC,WAAAA,CAAU,CAAE,CAAIZ,KAO7C,AAAC,SAAU3B,CAAmB,EA6B1B,SAASwC,EAAWC,CAAI,EACpB,OAAOL,EAAYI,UAAU,CAACjB,IAAI,CAAC,AAAC,IAAI,CAACmB,OAAO,CAACC,QAAQ,EACrD,IAAI,CAACC,KAAK,CAAC1B,GAAG,CAAC,IAAI,CAACwB,OAAO,CAACC,QAAQ,GAAM,IAAI,CAAEF,EACxD,CAOA,SAASI,IACLV,EAAYU,SAAS,CAACC,KAAK,CAAC,IAAI,EAChC,IAAMC,EAAS,IAAI,CAAEL,EAAUK,EAAOL,OAAO,CAAEE,EAAQG,EAAOH,KAAK,CAAEI,EAASD,EAAOC,MAAM,CAAEC,EAAkBP,EAAQC,QAAQ,CAAEA,EAAYM,GACzIL,EAAM1B,GAAG,CAAC+B,GAAmBC,EAAOP,GAAYA,EAASD,OAAO,CAACQ,IAAI,CAAEC,EAAUR,GAAYA,EAASK,MAAM,CAAGI,EAAWR,EAAMQ,QAAQ,CAAEC,EAAQN,EAAOM,KAAK,CAAEC,EAAQP,EAAOO,KAAK,CACpLC,EAASP,EAAOQ,MAAM,CAAG,EAAGC,EAAOC,EAAWC,EAAQjB,EAAQiB,KAAK,EAAI,IAAKC,EAAIT,GAAUA,EAAOK,MAAM,CAAEK,EAAU,EAAGC,EAAWC,EAAOC,EAAYC,EAAqBC,EAE7K,GAAIvB,GAAYA,EAASwB,OAAO,EAAIP,EAQhC,CAAA,IAPAC,EAAU,AAAClB,CAAAA,EAASyB,YAAY,EAAI,CAAA,EAAK,AAACzB,CAAAA,EAAS0B,IAAI,EAAI,CAAA,EAAK,EAChEJ,EAAsBtB,EAASsB,mBAAmB,CAClDF,EAASZ,CAAM,CAACS,EAAI,EAAE,CAACU,CAAC,CACnBL,CAAAA,EAAsBA,EAAoBM,UAAU,CAAG,CAAA,EAE5DhC,EAAWS,EAAQ,CAACrC,EAAG6D,IAAO7D,EAAE2D,CAAC,CAAGE,EAAEF,CAAC,EACvCX,EAAQ,OAASA,CAAK,CAAC,EAAE,CAACc,WAAW,GAAKd,EAAMe,MAAM,CAAC,GAChDd,KAAOZ,CAAM,CAACO,EAAO,EAIxB,GAHAO,EAAYX,CAAM,CAACS,EAAE,CAErBH,AADAA,CAAAA,EAAQT,CAAM,CAACO,EAAO,AAAD,EACfoB,CAAC,CAAGb,EAAUa,CAAC,CACjBb,EAAUQ,CAAC,EAAIb,EAAMa,CAAC,EACtB,AAA4B,KAAA,IAArBR,CAAS,CAACH,EAAM,CAAkB,CACzC,GAAIF,EAAMa,CAAC,EAAIP,IACXN,EAAMmB,KAAK,CAAGd,CAAS,CAACH,EAAM,CAE1BG,EAAUQ,CAAC,CAAGb,EAAMa,CAAC,EACrB,CAACpB,GACDc,CAAAA,EAAab,CAAM,CAACS,EAAI,EAAE,AAAD,IAErB,AAA6B,KAAA,IAAtBI,CAAU,CAACL,EAAM,CAGxB,GAAItB,EAAQoB,EAAMoB,KAAK,GACnBlC,EAASmC,EAAE,CAAC,UAAW,CAGvB,IAAMC,EAAK,CACPjB,EAAUe,KAAK,EAAI,EACnBf,EAAUc,KAAK,EAAI,EACtB,CAAEI,EAAK,CACJhB,EAAWa,KAAK,EAAI,EACpBb,EAAWY,KAAK,EAAI,EACvB,CAAEK,EAAMnB,EAAUoB,aAAa,EAAEC,MAC9BJ,EAAKK,EAAMpB,EAAWkB,aAAa,EAAEG,KACrCL,EAAiDM,EAAiB,CAACC,EAAG1E,IAG1E2E,KAAKC,GAAG,CAAC,EAAIF,EAAG,GAAKR,CAAE,CAAClE,EAAI,CACxB,EAAK,CAAA,EAAI0E,CAAAA,EAAM,CAAA,EAAIA,CAAAA,EAAKA,EACpBN,CAAE,CAACpE,EAAI,CAAG,EAAK,CAAA,EAAI0E,CAAAA,EAAKA,EAAIA,EAChCH,CAAE,CAACvE,EAAI,CAAG0E,EAAIA,EAAIA,EAAIP,CAAE,CAACnE,EAAI,CAC7B6E,EAAO,EAAGC,EAAO,EAAGJ,EAGxB,IAAK,IAAI3B,EAAI,EAAGA,EAVgC,IAUbA,IAAK,CACpC,IAAMgC,EAAO,AAACF,CAAAA,EAAOC,CAAG,EAAK,EACvBE,EAAOP,EAAeM,EAAM,GAClC,GAAIC,AAAS,OAATA,EACA,MAEJ,GAAIL,AAhBkB,IAgBlBA,KAAKM,GAAG,CAACD,EAAOpC,EAAMoB,KAAK,EAAoB,CAC/CU,EAAIK,EACJ,KACJ,CACIC,EAAOpC,EAAMoB,KAAK,CAClBa,EAAOE,EAGPD,EAAOC,CAEf,CACIvD,EAAQkD,KACR9B,EAAMmB,KAAK,CACPU,EAAeC,EAAG,GACtB9B,EAAMkB,CAAC,CACHrB,EAAMyC,OAAO,CAACtC,EAAMmB,KAAK,CAAE,CAAA,GAEvC,MAGIV,EACI,AAACT,CAAAA,EAAMa,CAAC,CAAGR,EAAUQ,CAAC,AAADA,EAChBN,CAAAA,EAAWM,CAAC,CAAGR,EAAUQ,CAAC,AAADA,EAClCb,EAAMmB,KAAK,EACPV,EAEKF,CAAAA,CAAU,CAACL,EAAM,CAAGG,CAAS,CAACH,EAAM,AAAD,EAC5CF,EAAMkB,CAAC,EACHT,EACKF,CAAAA,EAAWW,CAAC,CAAGb,EAAUa,CAAC,AAADA,EAOlD,GAFApB,IACAK,IACIL,EAAS,EACT,KAER,CACJ,CAGJP,EAAOgD,OAAO,CAAC,CAACvC,EAAOG,KACnB,IAAIqC,CACJxC,CAAAA,EAAMoB,KAAK,EAAIhB,EAOX,CAAA,AAAuB,KAAA,IAAhBJ,EAAMmB,KAAK,EAAoBxB,CAAO,IACzCK,EAAMoB,KAAK,EAAI,GACfpB,EAAMoB,KAAK,EAAIxB,EAAM6C,GAAG,CAEpB9C,GACAK,EAAMmB,KAAK,CAAGvB,EAAMR,SAAS,CAACY,EAAMa,CAAC,CAAE,EAAG,EAAG,EAAG,GAChDb,EAAMoB,KAAK,CAAGxC,EAAQoB,EAAMkB,CAAC,EACzBrB,EAAMT,SAAS,CAACY,EAAMkB,CAAC,CAAE,EAAG,EAAG,EAAG,GAClC,GAGJlB,EAAMmB,KAAK,CAAG,AAACvB,CAAAA,EAAM8C,QAAQ,CAAG,EAAIpD,EAAOO,KAAK,CAAC4C,GAAG,AAAD,EAC/C7C,EAAM+C,MAAM,CAIpB3C,EAAM4C,SAAS,CAAG,CAAC,GAI3B3C,CAAAA,EAAYV,CAAM,CAACY,EAAI,EAAE,AAAD,GACPF,EAAUmB,KAAK,GAAKpB,EAAMoB,KAAK,GACxC,AAAgC,KAAA,IAAzBnB,EAAUuC,UAAU,EAC3BvC,CAAAA,EAAUuC,UAAU,CAAG,CAAA,EAE3BA,EAAavC,EAAUuC,UAAU,CAAG,GAExCxC,EAAMwC,UAAU,CAAGA,CACvB,GACA,IAAI,CAACtD,QAAQ,CAAGA,CACpB,CA7JA3C,EAAoBsG,OAAO,CAR3B,SAAiBC,CAAW,EACxB,GAAIjE,EAAWJ,EAAU,YAAa,CAClC,IAAME,EAAcmE,EAAYlF,SAAS,AACzCe,CAAAA,EAAYI,UAAU,CAAGA,EACzBJ,EAAYS,SAAS,CAAGA,CAC5B,CACA,OAAO0D,CACX,EAYAvG,EAAoBwC,UAAU,CAAGA,EAmJjCxC,EAAoB6C,SAAS,CAAGA,CACpC,EAAG7C,GAAwBA,CAAAA,EAAsB,CAAC,CAAA,GAMrB,IAAMwG,EAA8BxG,EAGjE,IAAIyG,EAAmItG,EAAoB,KACvJuG,EAAuJvG,EAAoBI,CAAC,CAACkG,GAejL,GAAM,CAAEE,SAAAA,CAAQ,CAAE,CAAIhF,GAMtB,OAAMiF,UAAsB,AAAC7E,IAA+JV,SAAS,CAACwF,UAAU,CAM5MC,SAAU,CACN,OAAOH,EAAS,IAAI,CAACI,KAAK,GAAK,IAAI,CAACA,KAAK,EAAI,CACjD,CACJ,CAkOA,GAAM,CAAEC,WAAAA,CAAU,CAAE,CAAIrF,IAKlB,CAAEsF,OAAQC,CAAY,CAAE,CAAG,AAACR,IAA2IS,WAAW,CAElL,CAAEC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAE,CAAI3F,GA4CjC,OAAM4F,UAAuBL,EAMzBM,KAAK5E,CAAK,CAAEF,CAAO,CAAE,CACjB,KAAK,CAAC8E,KAAK5E,EAAOF,EACtB,CAEA+E,aAAahE,CAAK,CAAEiE,CAAK,CAAE,CACvB,IAAMhF,EAAU,IAAI,CAACA,OAAO,CACxBiF,EAASlE,EAAMmE,KAAK,EAAI,IAAI,CAACA,KAAK,CAAEC,EAAc,IAAI,CAACnF,OAAO,CAACoF,SAAS,CAO5E,OANIJ,IACAC,EAASjF,EAAQqF,MAAM,CAACL,EAAM,CAACE,KAAK,EAAID,EACxCE,EACI,AAACnF,CAAAA,EAAQqF,MAAM,CAACL,EAAM,CAACI,SAAS,EAAID,CAAU,EACzCnF,CAAAA,EAAQqF,MAAM,CAACL,EAAM,CAACM,aAAa,EAAI,CAAA,GAE7C,CACH,OAAUL,EACV,eAAgBE,CACpB,CACJ,CAGAI,UAAUxE,CAAK,CAAE,CACb,IAAMyE,EAAQzE,EAAM0E,aAAa,CAAEC,EAAI,IAAI,CAAC1F,OAAO,CAAC2F,YAAY,CAAG,GAC/DC,EAAQ7E,AAAc,SAAdA,EAAMsD,KAAK,CAAawB,EAAOC,EAAM,IACjD,GAAI/E,EAAMgF,MAAM,CACZ,MAAO,EAAE,CAEb,GAAIP,AAAU,IAAVA,EACA,OAAO,IAAI,CAACtF,KAAK,CAAC8F,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,IAAMR,EAAG,IAAMA,EAAG,GAAKA,EAAG,GAAKA,GAG7E,IAAMS,EAAO,CACT,CAAC,IAAK,EAAG,EAAIT,EAAE,CACf,CAAC,IAAK,KAAOA,EAAG,EAAIA,EAAE,CACtB,CAAC,IAAK,EAAG,GAAKA,EAAE,CAChB,CAAC,IAAK,IAAMA,EAAG,EAAIA,EAAE,CACrB,CAAC,IAAK,EAAG,EAAIA,EAAE,CACf,CAAC,IAAK,EAAG,IAAMA,EAAE,CACpB,CAGD,GAAIG,AADJA,CAAAA,EAAQ,AAACD,CAAAA,EAAQA,EAAQ,EAAC,EAAK,EAAC,EACpB,EACR,KAAOC,KACHM,EAAKC,IAAI,CAACN,AAAQ,MAARA,EAAc,CAAC,IAAK,EAAGA,EAAMJ,EAAE,CAAG,CAAC,IAAK,EAAGI,EAAMJ,EAAE,CAAE,CAAC,IAAK,EAAIA,EAAGI,EAAMJ,EAAI,EAAE,CAAE,CAAC,IAAK,EAAGI,EAAMJ,EAAI,EAAE,EAE/GE,GAAS,GACTE,GAAO,EAKf,GAAID,AADJA,CAAAA,EAAQ,AAACD,CAAAA,EAAQA,EAAQ,EAAC,EAAK,EAAC,EACpB,EACR,KAAOC,KACHM,EAAKC,IAAI,CAACN,AAAQ,MAARA,EAAc,CAAC,IAAK,EAAGA,EAAMJ,EAAE,CAAG,CAAC,IAAK,EAAGI,EAAMJ,EAAE,CAAE,CAAC,IAAK,EAAIA,EAAGI,EAAMJ,EAAE,EACpFE,GAAS,GACTE,GAAO,EAKf,GAAID,AADJA,CAAAA,EAAQ,AAACD,CAAAA,EAAQA,EAAQ,CAAA,EAAK,CAAA,EAClB,EACR,KAAOC,KACHM,EAAKC,IAAI,CAACN,AAAQ,MAARA,EAAc,CAAC,IAAK,EAAGA,EAAMJ,EAAE,CAAG,CAAC,IAAK,EAAGI,EAAMJ,EAAE,CAAE,CAAC,IAAK,EAAIA,EAAGI,EAAMJ,EAAE,EACpFE,GAAS,EACTE,GAAO,EAGf,OAAOK,CACX,CACAE,YAAa,CACT,IAAMnG,EAAQ,IAAI,CAACA,KAAK,CAAEU,EAAQ,IAAI,CAACA,KAAK,CAAEF,EAAWR,EAAMQ,QAAQ,CAAE4F,EAAc,IAAI,CAACtG,OAAO,CAAC2F,YAAY,CAAG,EACnH,IAAK,IAAM5E,KAAS,IAAI,CAACT,MAAM,CAAE,CAC7B,IAAM6B,EAAQpB,EAAMoB,KAAK,CAAED,EAAQnB,EAAMmB,KAAK,AAG1C,AAAsB,EAAA,IAAtB,IAAI,CAAClC,OAAO,CAACuG,IAAI,EACjBrG,EAAMsG,YAAY,CAACrE,EAAO,IAEtB,AAACpB,EAAM0F,OAAO,EACd1F,CAAAA,EAAM0F,OAAO,CAAG,IAAI,CAACvG,KAAK,CAAC8F,QAAQ,CAC9BG,IAAI,GACJO,GAAG,CAAC,IAAI,CAACC,WAAW,EACpBC,QAAQ,CAAC,qCAEVhC,EAAK7D,EAAM8F,UAAU,CAAE9F,EAAMV,MAAM,CAACwG,UAAU,EAAC,EAGvD9F,EAAM0F,OAAO,CACRK,IAAI,CAAC,CACN9I,EAAG,IAAI,CAACuH,SAAS,CAACxE,GAClBgG,WAAY5E,EAAQ,IAAI,CAACnC,OAAO,CAACmB,OAAO,CACxC6F,WAAY9E,EAAQ,IAAI,CAAClC,OAAO,CAACiH,OAAO,CACxCC,SAAUnG,EAAMoG,SAAS,AAC7B,GACI,AAAC,IAAI,CAACjH,KAAK,CAACkH,UAAU,EACtBrG,EAAM0F,OAAO,CACRK,IAAI,CAAC,IAAI,CAAC/B,YAAY,CAAChE,KAG3BA,EAAM0F,OAAO,EAClB1F,CAAAA,EAAM0F,OAAO,CAAG1F,EAAM0F,OAAO,CAACY,OAAO,EAAC,EAG1CtG,EAAMuG,UAAU,CAAG,CACfnF,EAAQ,IAAI,CAACnC,OAAO,CAACmB,OAAO,CACvBT,CAAAA,GAAY,CAAC,IAAI,CAACT,QAAQ,CAAGqG,EAAc,CAAA,EAChDpE,EAAQ,IAAI,CAAClC,OAAO,CAACiH,OAAO,CACvBvG,CAAAA,EACG,EACA4F,EAAc1F,EAAMkF,GAAG,CAAG5F,EAAMqH,OAAO,AAAD,EACjD,AACL,CACJ,CAEAC,QAAQ1C,CAAI,CAAE,CACNA,EACA,IAAI,CAAC6B,WAAW,CAACG,IAAI,CAAC,CAClBW,QAAS,GACb,GAGA,IAAI,CAACd,WAAW,CAACa,OAAO,CAAC,CACrBC,QAAS,CACb,EAAGnD,EAAW,IAAI,CAACtE,OAAO,CAAC0H,SAAS,EAE5C,CACAC,eAAgB,CACZ,MAAO,CAAC,CACZ,CACAC,aAAc,CACV,MAAO,CAAC,CACZ,CACAC,kBAAkB1F,CAAK,CAAED,CAAK,CAAElC,EAAU,CAAC,CAAC,CAAE,CAG1C,OAFAA,EAAQ8H,OAAO,CAAG,IAAI,CAAC5H,KAAK,CAACQ,QAAQ,CACrCV,EAAQ+H,OAAO,CAAG,CAAC/H,EAAQ8H,OAAO,CAC3B,KAAK,CAACD,kBAAkB1F,EAAOD,EAAOlC,EACjD,CACJ,CAMA6E,EAAemD,cAAc,CAAGrD,EAAMH,EAAawD,cAAc,CA9XlC,CAY3BC,aAAc,CAMVC,QAAS,CAAA,EAUTC,cAAe,WAMfC,gBAAiB,EACrB,EAIAhD,UAAW,EAWXnF,SAAU,KACVoF,OAAQ,CACJgD,MAAO,CACH/C,cAAe,CACnB,CACJ,EACAgD,QAAS,CAOLC,YAAa,wGACjB,EAIA5C,aAAc,GAId6C,SAAU,QAOVvB,QAAS,IAQT9F,QAAS,CACb,GAuSA2C,EAA2BF,OAAO,CAACiB,GACnCH,EAAOG,EAAelG,SAAS,CAAE,CAC7B8J,cAAe,CACX,EAAG,GAAK,IAAK,IAAK,IAAK,EAAK,KAAM,KAAM,KAAM,KAC9C,KAAM,KAAM,KACf,CACDC,aAAc,CACV,OAAQ,YAAa,eACrB,gBAAiB,kBAAmB,eACpC,gBAAiB,YAAa,OAAQ,cAAe,QACrD,gBAAiB,YACpB,CACDC,WAAY,CAAA,EACZC,eAAgB,CAAC,IAAK,QAAS,YAAY,CAC3CC,cAAe,CAAC,QAAS,YAAY,CACrC1E,WAnbyDD,EAobzD4E,cAAe,CAAC,cAAc,CAC9B3I,UAAW,WACP,IAAMsI,EAAgB,IAAI,CAACA,aAAa,CAAEC,EAAe,IAAI,CAACA,YAAY,CAE1E,IAAK,IAAM3H,KADX+C,EAA2B3D,SAAS,CAACtB,IAAI,CAAC,IAAI,EAC1B,IAAI,CAACyB,MAAM,EAAE,CAC7B,IAAIkF,EAAQ,EAEZ,KAAOA,EAAQiD,EAAc3H,MAAM,GAC3B2H,CAAAA,CAAa,CAACjD,EAAM,CAAGzE,EAAMsD,KAAK,AAAD,EADJmB,KAKrCzE,EAAM0E,aAAa,CAAGD,EAAQ,EAC9BzE,EAAMgI,QAAQ,CAAGL,CAAY,CAAClD,EAAQ,EAAE,AAC5C,CACJ,CACJ,GACAxB,IAA0IgF,kBAAkB,CAAC,WAAYnE,GAlNjK,AAAC,AAAC1F,IAA+L8J,QAAQ,EACzM,CAAA,AAAC9J,IAA+L8J,QAAQ,CAAG,CAACC,EAAQC,KAChN,IAAIC,EAAU,EAAGC,EAAU,EAC3B,IAAK,IAAInI,EAAI,EAAGoI,EAAOJ,EAAOpI,MAAM,CAAEI,EAAIoI,EAAMpI,IAC5CkI,GAAWF,CAAM,CAAChI,EAAE,CAAG4B,KAAKyG,GAAG,CAACJ,CAAU,CAACjI,EAAE,CAAG,AAACjC,IAA+EuK,OAAO,EACvIH,GAAWH,CAAM,CAAChI,EAAE,CAAG4B,KAAK2G,GAAG,CAACN,CAAU,CAACjI,EAAE,CAAG,AAACjC,IAA+EuK,OAAO,EAE3I,MAAO,CAEHN,EAAOQ,MAAM,CAAC,CAACC,EAAKtF,IAAWsF,EAAMtF,EAAQ,GAAK6E,EAAOpI,MAAM,CAE/DgC,KAAK8G,KAAK,CAACP,EAASD,GAAW,AAACnK,IAA+EuK,OAAO,CACzH,AACL,CAAA,EAmNqB,IAAMzK,EAAiBE,IAG1C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}