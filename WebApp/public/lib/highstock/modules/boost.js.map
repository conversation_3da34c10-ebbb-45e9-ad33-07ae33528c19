{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/boost\n * @requires highcharts\n *\n * Boost module\n *\n * (c) 2010-2025 Highsoft AS\n * Author: Torstein Honsi\n *\n * License: www.highcharts.com/license\n *\n * */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Color\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/boost\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"Color\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/boost\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Color\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Color\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__620__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 620:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__620__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ boost_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Extensions/Boost/Boostables.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n// These are the series we allow boosting for.\nconst Boostables = [\n    'area',\n    'areaspline',\n    'arearange',\n    'column',\n    'columnrange',\n    'bar',\n    'line',\n    'scatter',\n    'heatmap',\n    'bubble',\n    'treemap'\n];\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Boost_Boostables = (Boostables);\n\n;// ./code/es-modules/Extensions/Boost/BoostableMap.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n// These are the series we allow boosting for.\nconst BoostableMap = {};\nBoost_Boostables.forEach((item) => {\n    BoostableMap[item] = true;\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Boost_BoostableMap = (BoostableMap);\n\n;// ./code/es-modules/Extensions/Boost/BoostChart.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent, pick, pushUnique } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction compose(ChartClass, wglMode) {\n    if (wglMode && pushUnique(composed, 'Boost.Chart')) {\n        ChartClass.prototype.callbacks.push(onChartCallback);\n    }\n    return ChartClass;\n}\n/**\n * Get the clip rectangle for a target, either a series or the chart.\n * For the chart, we need to consider the maximum extent of its Y axes,\n * in case of Highcharts Stock panes and navigator.\n *\n * @private\n * @function Highcharts.Chart#getBoostClipRect\n */\nfunction getBoostClipRect(chart, target) {\n    const navigator = chart.navigator;\n    let clipBox = {\n        x: chart.plotLeft,\n        y: chart.plotTop,\n        width: chart.plotWidth,\n        height: chart.plotHeight\n    };\n    if (navigator && chart.inverted) { // #17820, #20936\n        clipBox.width += navigator.top + navigator.height;\n        if (!navigator.opposite) {\n            clipBox.x = navigator.left;\n        }\n    }\n    else if (navigator && !chart.inverted) {\n        clipBox.height = navigator.top + navigator.height - chart.plotTop;\n    }\n    // Clipping of individual series (#11906, #19039).\n    if (target.is) {\n        const { xAxis, yAxis } = target;\n        clipBox = chart.getClipBox(target);\n        if (chart.inverted) {\n            const lateral = clipBox.width;\n            clipBox.width = clipBox.height;\n            clipBox.height = lateral;\n            clipBox.x = yAxis.pos;\n            clipBox.y = xAxis.pos;\n        }\n        else {\n            clipBox.x = xAxis.pos;\n            clipBox.y = yAxis.pos;\n        }\n    }\n    if (target === chart) {\n        const verticalAxes = chart.inverted ? chart.xAxis : chart.yAxis; // #14444\n        if (verticalAxes.length <= 1) {\n            clipBox.y = Math.min(verticalAxes[0].pos, clipBox.y);\n            clipBox.height = (verticalAxes[0].pos -\n                chart.plotTop +\n                verticalAxes[0].len);\n        }\n    }\n    return clipBox;\n}\n/**\n * Returns true if the chart is in series boost mode.\n * @private\n * @param {Highcharts.Chart} chart\n * Chart to check.\n * @return {boolean}\n * `true` if the chart is in series boost mode.\n */\nfunction isChartSeriesBoosting(chart) {\n    const allSeries = chart.series, boost = chart.boost = chart.boost || {}, boostOptions = chart.options.boost || {}, threshold = pick(boostOptions.seriesThreshold, 50);\n    if (allSeries.length >= threshold) {\n        return true;\n    }\n    if (allSeries.length === 1) {\n        return false;\n    }\n    let allowBoostForce = boostOptions.allowForce;\n    if (typeof allowBoostForce === 'undefined') {\n        allowBoostForce = true;\n        for (const axis of chart.xAxis) {\n            if (pick(axis.min, -Infinity) > pick(axis.dataMin, -Infinity) ||\n                pick(axis.max, Infinity) < pick(axis.dataMax, Infinity)) {\n                allowBoostForce = false;\n                break;\n            }\n        }\n    }\n    if (typeof boost.forceChartBoost !== 'undefined') {\n        if (allowBoostForce) {\n            return boost.forceChartBoost;\n        }\n        boost.forceChartBoost = void 0;\n    }\n    // If there are more than five series currently boosting,\n    // we should boost the whole chart to avoid running out of webgl contexts.\n    let canBoostCount = 0, needBoostCount = 0, seriesOptions;\n    for (const series of allSeries) {\n        seriesOptions = series.options;\n        // Don't count series with boostThreshold set to 0\n        // See #8950\n        // Also don't count if the series is hidden.\n        // See #9046\n        if (seriesOptions.boostThreshold === 0 ||\n            series.visible === false) {\n            continue;\n        }\n        // Don't count heatmap series as they are handled differently.\n        // In the future we should make the heatmap/treemap path compatible\n        // with forcing. See #9636.\n        if (series.type === 'heatmap') {\n            continue;\n        }\n        if (Boost_BoostableMap[series.type]) {\n            ++canBoostCount;\n        }\n        if (patientMax(series.getColumn('x', true), seriesOptions.data, \n        /// series.xData,\n        series.points) >= (seriesOptions.boostThreshold || Number.MAX_VALUE)) {\n            ++needBoostCount;\n        }\n    }\n    boost.forceChartBoost = allowBoostForce && ((\n    // Even when the series that need a boost are less than or equal\n    // to 5, force a chart boost when all series are to be boosted.\n    // See #18815\n    canBoostCount === allSeries.length &&\n        needBoostCount === canBoostCount) ||\n        needBoostCount > 5);\n    return boost.forceChartBoost;\n}\n/**\n * Take care of the canvas blitting\n * @private\n */\nfunction onChartCallback(chart) {\n    /**\n     * Convert chart-level canvas to image.\n     * @private\n     */\n    function canvasToSVG() {\n        if (chart.boost &&\n            chart.boost.wgl &&\n            isChartSeriesBoosting(chart)) {\n            chart.boost.wgl.render(chart);\n        }\n    }\n    /**\n     * Clear chart-level canvas.\n     * @private\n     */\n    function preRender() {\n        // Reset force state\n        chart.boost = chart.boost || {};\n        chart.boost.forceChartBoost = void 0;\n        chart.boosted = false;\n        // Clear the canvas\n        if (!chart.axes.some((axis) => axis.isPanning)) {\n            chart.boost.clear?.();\n        }\n        if (chart.boost.canvas &&\n            chart.boost.wgl &&\n            isChartSeriesBoosting(chart)) {\n            // Allocate\n            chart.boost.wgl.allocateBuffer(chart);\n        }\n        // See #6518 + #6739\n        if (chart.boost.markerGroup &&\n            chart.xAxis &&\n            chart.xAxis.length > 0 &&\n            chart.yAxis &&\n            chart.yAxis.length > 0) {\n            chart.boost.markerGroup.translate(chart.xAxis[0].pos, chart.yAxis[0].pos);\n        }\n    }\n    addEvent(chart, 'predraw', preRender);\n    // Use the load event rather than redraw, otherwise user load events will\n    // fire too early (#18755)\n    addEvent(chart, 'load', canvasToSVG, { order: -1 });\n    addEvent(chart, 'redraw', canvasToSVG);\n    let prevX = -1;\n    let prevY = -1;\n    addEvent(chart.pointer, 'afterGetHoverData', (e) => {\n        const series = e.hoverPoint?.series;\n        chart.boost = chart.boost || {};\n        if (chart.boost.markerGroup && series) {\n            const xAxis = chart.inverted ? series.yAxis : series.xAxis;\n            const yAxis = chart.inverted ? series.xAxis : series.yAxis;\n            if ((xAxis && xAxis.pos !== prevX) ||\n                (yAxis && yAxis.pos !== prevY)) {\n                // #21176: If the axis is changed, hide teh halo without\n                // animation  to prevent flickering of halos sharing the\n                // same marker group\n                chart.series.forEach((s) => {\n                    s.halo?.hide();\n                });\n                // #10464: Keep the marker group position in sync with the\n                // position of the hovered series axes since there is only\n                // one shared marker group when boosting.\n                chart.boost.markerGroup.translate(xAxis.pos, yAxis.pos);\n                prevX = xAxis.pos;\n                prevY = yAxis.pos;\n            }\n        }\n    });\n}\n/**\n * Tolerant max() function.\n *\n * @private\n * @param {...Array<Array<unknown>>} args\n * Max arguments\n * @return {number}\n * Max value\n */\nfunction patientMax(...args) {\n    let r = -Number.MAX_VALUE;\n    args.forEach((t) => {\n        if (typeof t !== 'undefined' &&\n            t !== null &&\n            typeof t.length !== 'undefined') {\n            if (t.length > 0) {\n                r = t.length;\n                return true;\n            }\n        }\n    });\n    return r;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst BoostChart = {\n    compose,\n    getBoostClipRect,\n    isChartSeriesBoosting\n};\n/* harmony default export */ const Boost_BoostChart = (BoostChart);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Color\"],\"commonjs\":[\"highcharts\",\"Color\"],\"commonjs2\":[\"highcharts\",\"Color\"],\"root\":[\"Highcharts\",\"Color\"]}\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_ = __webpack_require__(620);\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default = /*#__PURE__*/__webpack_require__.n(highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_);\n;// ./code/es-modules/Extensions/Boost/WGLDrawMode.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\nconst WGLDrawMode = {\n    'area': 'LINES',\n    'arearange': 'LINES',\n    'areaspline': 'LINES',\n    'column': 'LINES',\n    'columnrange': 'LINES',\n    'bar': 'LINES',\n    'line': 'LINE_STRIP',\n    'scatter': 'POINTS',\n    'heatmap': 'TRIANGLES',\n    'treemap': 'TRIANGLES',\n    'bubble': 'POINTS'\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Boost_WGLDrawMode = (WGLDrawMode);\n\n;// ./code/es-modules/Extensions/Boost/WGLShader.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { clamp, error, pick: WGLShader_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\nconst fragmentShader = [\n    /* eslint-disable max-len, @typescript-eslint/indent */\n    'precision highp float;',\n    'uniform vec4 fillColor;',\n    'varying highp vec2 position;',\n    'varying highp vec4 vColor;',\n    'uniform sampler2D uSampler;',\n    'uniform bool isCircle;',\n    'uniform bool hasColor;',\n    // 'vec4 toColor(float value, vec2 point) {',\n    //     'return vec4(0.0, 0.0, 0.0, 0.0);',\n    // '}',\n    'void main(void) {',\n    'vec4 col = fillColor;',\n    'vec4 tcol = texture2D(uSampler, gl_PointCoord.st);',\n    'if (hasColor) {',\n    'col = vColor;',\n    '}',\n    'if (isCircle) {',\n    'col *= tcol;',\n    'if (tcol.r < 0.0) {',\n    'discard;',\n    '} else {',\n    'gl_FragColor = col;',\n    '}',\n    '} else {',\n    'gl_FragColor = col;',\n    '}',\n    '}'\n    /* eslint-enable max-len, @typescript-eslint/indent */\n].join('\\n');\nconst vertexShader = [\n    /* eslint-disable max-len, @typescript-eslint/indent */\n    '#version 100',\n    '#define LN10 2.302585092994046',\n    'precision highp float;',\n    'attribute vec4 aVertexPosition;',\n    'attribute vec4 aColor;',\n    'varying highp vec2 position;',\n    'varying highp vec4 vColor;',\n    'uniform mat4 uPMatrix;',\n    'uniform float pSize;',\n    'uniform float translatedThreshold;',\n    'uniform bool hasThreshold;',\n    'uniform bool skipTranslation;',\n    'uniform float xAxisTrans;',\n    'uniform float xAxisMin;',\n    'uniform float xAxisMinPad;',\n    'uniform float xAxisPointRange;',\n    'uniform float xAxisLen;',\n    'uniform bool  xAxisPostTranslate;',\n    'uniform float xAxisOrdinalSlope;',\n    'uniform float xAxisOrdinalOffset;',\n    'uniform float xAxisPos;',\n    'uniform bool  xAxisCVSCoord;',\n    'uniform bool  xAxisIsLog;',\n    'uniform bool  xAxisReversed;',\n    'uniform float yAxisTrans;',\n    'uniform float yAxisMin;',\n    'uniform float yAxisMinPad;',\n    'uniform float yAxisPointRange;',\n    'uniform float yAxisLen;',\n    'uniform bool  yAxisPostTranslate;',\n    'uniform float yAxisOrdinalSlope;',\n    'uniform float yAxisOrdinalOffset;',\n    'uniform float yAxisPos;',\n    'uniform bool  yAxisCVSCoord;',\n    'uniform bool  yAxisIsLog;',\n    'uniform bool  yAxisReversed;',\n    'uniform bool  isBubble;',\n    'uniform bool  bubbleSizeByArea;',\n    'uniform float bubbleZMin;',\n    'uniform float bubbleZMax;',\n    'uniform float bubbleZThreshold;',\n    'uniform float bubbleMinSize;',\n    'uniform float bubbleMaxSize;',\n    'uniform bool  bubbleSizeAbs;',\n    'uniform bool  isInverted;',\n    'float bubbleRadius(){',\n    'float value = aVertexPosition.w;',\n    'float zMax = bubbleZMax;',\n    'float zMin = bubbleZMin;',\n    'float radius = 0.0;',\n    'float pos = 0.0;',\n    'float zRange = zMax - zMin;',\n    'if (bubbleSizeAbs){',\n    'value = value - bubbleZThreshold;',\n    'zMax = max(zMax - bubbleZThreshold, zMin - bubbleZThreshold);',\n    'zMin = 0.0;',\n    '}',\n    'if (value < zMin){',\n    'radius = bubbleZMin / 2.0 - 1.0;',\n    '} else {',\n    'pos = zRange > 0.0 ? (value - zMin) / zRange : 0.5;',\n    'if (bubbleSizeByArea && pos > 0.0){',\n    'pos = sqrt(pos);',\n    '}',\n    'radius = ceil(bubbleMinSize + pos * (bubbleMaxSize - bubbleMinSize)) / 2.0;',\n    '}',\n    'return radius * 2.0;',\n    '}',\n    'float translate(float val,',\n    'float pointPlacement,',\n    'float localA,',\n    'float localMin,',\n    'float minPixelPadding,',\n    'float pointRange,',\n    'float len,',\n    'bool  cvsCoord,',\n    'bool  isLog,',\n    'bool  reversed',\n    '){',\n    'float sign = 1.0;',\n    'float cvsOffset = 0.0;',\n    'if (cvsCoord) {',\n    'sign *= -1.0;',\n    'cvsOffset = len;',\n    '}',\n    'if (isLog) {',\n    'val = log(val) / LN10;',\n    '}',\n    'if (reversed) {',\n    'sign *= -1.0;',\n    'cvsOffset -= sign * len;',\n    '}',\n    'return sign * (val - localMin) * localA + cvsOffset + ',\n    '(sign * minPixelPadding);', // ' + localA * pointPlacement * pointRange;',\n    '}',\n    'float xToPixels(float value) {',\n    'if (skipTranslation){',\n    'return value;// + xAxisPos;',\n    '}',\n    'return translate(value, 0.0, xAxisTrans, xAxisMin, xAxisMinPad, xAxisPointRange, xAxisLen, xAxisCVSCoord, xAxisIsLog, xAxisReversed);// + xAxisPos;',\n    '}',\n    'float yToPixels(float value, float checkTreshold) {',\n    'float v;',\n    'if (skipTranslation){',\n    'v = value;// + yAxisPos;',\n    '} else {',\n    'v = translate(value, 0.0, yAxisTrans, yAxisMin, yAxisMinPad, yAxisPointRange, yAxisLen, yAxisCVSCoord, yAxisIsLog, yAxisReversed);// + yAxisPos;',\n    'if (v > yAxisLen) {',\n    'v = yAxisLen;',\n    '}',\n    '}',\n    'if (checkTreshold > 0.0 && hasThreshold) {',\n    'v = min(v, translatedThreshold);',\n    '}',\n    'return v;',\n    '}',\n    'void main(void) {',\n    'if (isBubble){',\n    'gl_PointSize = bubbleRadius();',\n    '} else {',\n    'gl_PointSize = pSize;',\n    '}',\n    // 'gl_PointSize = 10.0;',\n    'vColor = aColor;',\n    'if (skipTranslation && isInverted) {',\n    // If we get translated values from JS, just swap them (x, y)\n    'gl_Position = uPMatrix * vec4(aVertexPosition.y + yAxisPos, aVertexPosition.x + xAxisPos, 0.0, 1.0);',\n    '} else if (isInverted) {',\n    // But when calculating pixel positions directly,\n    // swap axes and values (x, y)\n    'gl_Position = uPMatrix * vec4(yToPixels(aVertexPosition.y, aVertexPosition.z) + yAxisPos, xToPixels(aVertexPosition.x) + xAxisPos, 0.0, 1.0);',\n    '} else {',\n    'gl_Position = uPMatrix * vec4(xToPixels(aVertexPosition.x) + xAxisPos, yToPixels(aVertexPosition.y, aVertexPosition.z) + yAxisPos, 0.0, 1.0);',\n    '}',\n    // 'gl_Position = uPMatrix * vec4(aVertexPosition.x, aVertexPosition.y, 0.0, 1.0);',\n    '}'\n    /* eslint-enable max-len, @typescript-eslint/indent */\n].join('\\n');\n/* *\n *\n *  Class\n *\n * */\n/* eslint-disable valid-jsdoc */\n/**\n * A static shader mimicing axis translation functions found in Core/Axis\n *\n * @private\n *\n * @param {WebGLContext} gl\n * the context in which the shader is active\n */\nclass WGLShader {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(gl) {\n        // Error stack\n        this.errors = [];\n        this.uLocations = {};\n        this.gl = gl;\n        if (gl && !this.createShader()) {\n            return void 0;\n        }\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Bind the shader.\n     * This makes the shader the active one until another one is bound,\n     * or until 0 is bound.\n     * @private\n     */\n    bind() {\n        if (this.gl && this.shaderProgram) {\n            this.gl.useProgram(this.shaderProgram);\n        }\n    }\n    /**\n     * Create the shader.\n     * Loads the shader program statically defined above\n     * @private\n     */\n    createShader() {\n        const v = this.stringToProgram(vertexShader, 'vertex'), f = this.stringToProgram(fragmentShader, 'fragment'), uloc = (n) => (this.gl.getUniformLocation(this.shaderProgram, n));\n        if (!v || !f) {\n            this.shaderProgram = false;\n            this.handleErrors();\n            return false;\n        }\n        this.shaderProgram = this.gl.createProgram();\n        this.gl.attachShader(this.shaderProgram, v);\n        this.gl.attachShader(this.shaderProgram, f);\n        this.gl.linkProgram(this.shaderProgram);\n        if (!this.gl.getProgramParameter(this.shaderProgram, this.gl.LINK_STATUS)) {\n            this.errors.push(this.gl.getProgramInfoLog(this.shaderProgram));\n            this.handleErrors();\n            this.shaderProgram = false;\n            return false;\n        }\n        this.gl.useProgram(this.shaderProgram);\n        this.gl.bindAttribLocation(this.shaderProgram, 0, 'aVertexPosition');\n        this.pUniform = uloc('uPMatrix');\n        this.psUniform = uloc('pSize');\n        this.fcUniform = uloc('fillColor');\n        this.isBubbleUniform = uloc('isBubble');\n        this.bubbleSizeAbsUniform = uloc('bubbleSizeAbs');\n        this.bubbleSizeAreaUniform = uloc('bubbleSizeByArea');\n        this.uSamplerUniform = uloc('uSampler');\n        this.skipTranslationUniform = uloc('skipTranslation');\n        this.isCircleUniform = uloc('isCircle');\n        this.isInverted = uloc('isInverted');\n        return true;\n    }\n    /**\n     * Handle errors accumulated in errors stack\n     * @private\n     */\n    handleErrors() {\n        if (this.errors.length) {\n            error('[highcharts boost] shader error - ' +\n                this.errors.join('\\n'));\n        }\n    }\n    /**\n     * String to shader program\n     * @private\n     * @param {string} str\n     * Program source\n     * @param {string} type\n     * Program type: either `vertex` or `fragment`\n     */\n    stringToProgram(str, type) {\n        const shader = this.gl.createShader(type === 'vertex' ? this.gl.VERTEX_SHADER : this.gl.FRAGMENT_SHADER);\n        this.gl.shaderSource(shader, str);\n        this.gl.compileShader(shader);\n        if (!this.gl.getShaderParameter(shader, this.gl.COMPILE_STATUS)) {\n            this.errors.push('when compiling ' +\n                type +\n                ' shader:\\n' +\n                this.gl.getShaderInfoLog(shader));\n            return false;\n        }\n        return shader;\n    }\n    /**\n     * Destroy the shader\n     * @private\n     */\n    destroy() {\n        if (this.gl && this.shaderProgram) {\n            this.gl.deleteProgram(this.shaderProgram);\n            this.shaderProgram = false;\n        }\n    }\n    fillColorUniform() {\n        return this.fcUniform;\n    }\n    /**\n     * Get the shader program handle\n     * @private\n     * @return {WebGLProgram}\n     * The handle for the program\n     */\n    getProgram() {\n        return this.shaderProgram;\n    }\n    pointSizeUniform() {\n        return this.psUniform;\n    }\n    perspectiveUniform() {\n        return this.pUniform;\n    }\n    /**\n     * Flush\n     * @private\n     */\n    reset() {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform1i(this.isBubbleUniform, 0);\n            this.gl.uniform1i(this.isCircleUniform, 0);\n        }\n    }\n    /**\n     * Set bubble uniforms\n     * @private\n     * @param {Highcharts.Series} series\n     * Series to use\n     */\n    setBubbleUniforms(series, zCalcMin, zCalcMax, pixelRatio = 1) {\n        const seriesOptions = series.options;\n        let zMin = Number.MAX_VALUE, zMax = -Number.MAX_VALUE;\n        if (this.gl && this.shaderProgram && series.is('bubble')) {\n            const pxSizes = series.getPxExtremes();\n            zMin = WGLShader_pick(seriesOptions.zMin, clamp(zCalcMin, seriesOptions.displayNegative === false ?\n                seriesOptions.zThreshold : -Number.MAX_VALUE, zMin));\n            zMax = WGLShader_pick(seriesOptions.zMax, Math.max(zMax, zCalcMax));\n            this.gl.uniform1i(this.isBubbleUniform, 1);\n            this.gl.uniform1i(this.isCircleUniform, 1);\n            this.gl.uniform1i(this.bubbleSizeAreaUniform, (series.options.sizeBy !== 'width'));\n            this.gl.uniform1i(this.bubbleSizeAbsUniform, series.options\n                .sizeByAbsoluteValue);\n            this.setUniform('bubbleMinSize', pxSizes.minPxSize * pixelRatio);\n            this.setUniform('bubbleMaxSize', pxSizes.maxPxSize * pixelRatio);\n            this.setUniform('bubbleZMin', zMin);\n            this.setUniform('bubbleZMax', zMax);\n            this.setUniform('bubbleZThreshold', series.options.zThreshold);\n        }\n    }\n    /**\n     * Set the Color uniform.\n     * @private\n     * @param {Array<number>} color\n     * Array with RGBA values.\n     */\n    setColor(color) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform4f(this.fcUniform, color[0] / 255.0, color[1] / 255.0, color[2] / 255.0, color[3]);\n        }\n    }\n    /**\n     * Enable/disable circle drawing\n     * @private\n     */\n    setDrawAsCircle(flag) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform1i(this.isCircleUniform, flag ? 1 : 0);\n        }\n    }\n    /**\n     * Set if inversion state\n     * @private\n     * @param {number} flag\n     * Inversion flag\n     */\n    setInverted(flag) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform1i(this.isInverted, flag);\n        }\n    }\n    /**\n     * Set the perspective matrix\n     * @private\n     * @param {Float32List} m\n     * Matrix 4 x 4\n     */\n    setPMatrix(m) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniformMatrix4fv(this.pUniform, false, m);\n        }\n    }\n    /**\n     * Set the point size.\n     * @private\n     * @param {number} p\n     * Point size\n     */\n    setPointSize(p) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform1f(this.psUniform, p);\n        }\n    }\n    /**\n     * Set skip translation\n     * @private\n     */\n    setSkipTranslation(flag) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform1i(this.skipTranslationUniform, flag === true ? 1 : 0);\n        }\n    }\n    /**\n     * Set the active texture\n     * @private\n     * @param {number} texture\n     * Texture to activate\n     */\n    setTexture(texture) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform1i(this.uSamplerUniform, texture);\n        }\n    }\n    /**\n     * Set a uniform value.\n     * This uses a hash map to cache uniform locations.\n     * @private\n     * @param {string} name\n     * Name of the uniform to set.\n     * @param {number} val\n     * Value to set\n     */\n    setUniform(name, val) {\n        if (this.gl && this.shaderProgram) {\n            const u = this.uLocations[name] = (this.uLocations[name] ||\n                this.gl.getUniformLocation(this.shaderProgram, name));\n            this.gl.uniform1f(u, val);\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Boost_WGLShader = (WGLShader);\n\n;// ./code/es-modules/Extensions/Boost/WGLVertexBuffer.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * Vertex Buffer abstraction.\n * A vertex buffer is a set of vertices which are passed to the GPU\n * in a single call.\n *\n * @private\n * @class\n * @name WGLVertexBuffer\n *\n * @param {WebGLContext} gl\n * Context in which to create the buffer.\n * @param {WGLShader} shader\n * Shader to use.\n */\nclass WGLVertexBuffer {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(gl, shader, dataComponents\n    /* , type */\n    ) {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.buffer = false;\n        this.iterator = 0;\n        this.preAllocated = false;\n        this.vertAttribute = false;\n        this.components = dataComponents || 2;\n        this.dataComponents = dataComponents;\n        this.gl = gl;\n        this.shader = shader;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Note about pre-allocated buffers:\n     *     - This is slower for charts with many series\n     * @private\n     */\n    allocate(size) {\n        this.iterator = -1;\n        this.preAllocated = new Float32Array(size * 4);\n    }\n    /**\n     * Bind the buffer\n     * @private\n     */\n    bind() {\n        if (!this.buffer) {\n            return false;\n        }\n        /// gl.bindAttribLocation(shader.program(), 0, 'aVertexPosition');\n        // gl.enableVertexAttribArray(vertAttribute);\n        // gl.bindBuffer(gl.ARRAY_BUFFER, buffer);\n        this.gl.vertexAttribPointer(this.vertAttribute, this.components, this.gl.FLOAT, false, 0, 0);\n        /// gl.enableVertexAttribArray(vertAttribute);\n    }\n    /**\n     * Build the buffer\n     * @private\n     * @param {Array<number>} dataIn\n     * Zero padded array of indices\n     * @param {string} attrib\n     * Name of the Attribute to bind the buffer to\n     * @param {number} dataComponents\n     * Number of components per. indice\n     */\n    build(dataIn, attrib, dataComponents) {\n        let farray;\n        this.data = dataIn || [];\n        if ((!this.data || this.data.length === 0) && !this.preAllocated) {\n            /// console.error('trying to render empty vbuffer');\n            this.destroy();\n            return false;\n        }\n        this.components = dataComponents || this.components;\n        if (this.buffer) {\n            this.gl.deleteBuffer(this.buffer);\n        }\n        if (!this.preAllocated) {\n            farray = new Float32Array(this.data);\n        }\n        this.buffer = this.gl.createBuffer();\n        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffer);\n        this.gl.bufferData(this.gl.ARRAY_BUFFER, this.preAllocated || farray, this.gl.STATIC_DRAW);\n        /// gl.bindAttribLocation(shader.program(), 0, 'aVertexPosition');\n        this.vertAttribute = this.gl\n            .getAttribLocation(this.shader.getProgram(), attrib);\n        this.gl.enableVertexAttribArray(this.vertAttribute);\n        // Trigger cleanup\n        farray = false;\n        return true;\n    }\n    /**\n     * @private\n     */\n    destroy() {\n        if (this.buffer) {\n            this.gl.deleteBuffer(this.buffer);\n            this.buffer = false;\n            this.vertAttribute = false;\n        }\n        this.iterator = 0;\n        this.components = this.dataComponents || 2;\n        this.data = [];\n    }\n    /**\n     * Adds data to the pre-allocated buffer.\n     * @private\n     * @param {number} x\n     * X data\n     * @param {number} y\n     * Y data\n     * @param {number} a\n     * A data\n     * @param {number} b\n     * B data\n     */\n    push(x, y, a, b) {\n        if (this.preAllocated) { // && iterator <= preAllocated.length - 4) {\n            this.preAllocated[++this.iterator] = x;\n            this.preAllocated[++this.iterator] = y;\n            this.preAllocated[++this.iterator] = a;\n            this.preAllocated[++this.iterator] = b;\n        }\n    }\n    /**\n     * Render the buffer\n     *\n     * @private\n     * @param {number} from\n     * Start indice.\n     * @param {number} to\n     * End indice.\n     * @param {WGLDrawModeValue} drawMode\n     * Draw mode.\n     */\n    render(from, to, drawMode) {\n        const length = this.preAllocated ?\n            this.preAllocated.length : this.data.length;\n        if (!this.buffer) {\n            return false;\n        }\n        if (!length) {\n            return false;\n        }\n        if (!from || from > length || from < 0) {\n            from = 0;\n        }\n        if (!to || to > length) {\n            to = length;\n        }\n        if (from >= to) {\n            return false;\n        }\n        drawMode = drawMode || 'POINTS';\n        this.gl.drawArrays(this.gl[drawMode], from / this.components, (to - from) / this.components);\n        return true;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Boost_WGLVertexBuffer = (WGLVertexBuffer);\n\n;// ./code/es-modules/Extensions/Boost/WGLRenderer.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { parse: color } = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default());\n\nconst { doc, win } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { isNumber, isObject, merge, objectEach, pick: WGLRenderer_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\n\n/* *\n *\n *  Constants\n *\n * */\n// Things to draw as \"rectangles\" (i.e lines)\nconst asBar = {\n    'column': true,\n    'columnrange': true,\n    'bar': true,\n    'area': true,\n    'areaspline': true,\n    'arearange': true\n};\nconst asCircle = {\n    'scatter': true,\n    'bubble': true\n};\nconst contexts = [\n    'webgl',\n    'experimental-webgl',\n    'moz-webgl',\n    'webkit-3d'\n];\n/* *\n *\n *  Class\n *\n * */\n/* eslint-disable valid-jsdoc */\n/**\n * Main renderer. Used to render series.\n *\n * Notes to self:\n * - May be able to build a point map by rendering to a separate canvas and\n *   encoding values in the color data.\n * - Need to figure out a way to transform the data quicker\n *\n * @private\n *\n * @param {Function} postRenderCallback\n */\nclass WGLRenderer {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Returns an orthographic perspective matrix\n     * @private\n     * @param {number} width\n     * the width of the viewport in pixels\n     * @param {number} height\n     * the height of the viewport in pixels\n     */\n    static orthoMatrix(width, height) {\n        const near = 0, far = 1;\n        return [\n            2 / width, 0, 0, 0,\n            0, -(2 / height), 0, 0,\n            0, 0, -2 / (far - near), 0,\n            -1, 1, -(far + near) / (far - near), 1\n        ];\n    }\n    /**\n     * @private\n     */\n    static seriesPointCount(series) {\n        let isStacked, xData, s;\n        if (series.boosted) {\n            isStacked = !!series.options.stacking;\n            xData = ((series.getColumn('x').length ?\n                series.getColumn('x') :\n                void 0) ||\n                series.options.xData ||\n                series.getColumn('x', true));\n            s = (isStacked ? series.data : (xData || series.options.data))\n                .length;\n            if (series.type === 'treemap') {\n                s *= 12;\n            }\n            else if (series.type === 'heatmap') {\n                s *= 6;\n            }\n            else if (asBar[series.type]) {\n                s *= 2;\n            }\n            return s;\n        }\n        return 0;\n    }\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(postRenderCallback) {\n        /**\n         * The data to render - array of coordinates.\n         * Repeating sequence of [x, y, checkThreshold, pointSize].\n         */\n        this.data = [];\n        // Height of our viewport in pixels\n        this.height = 0;\n        // Is it inited?\n        this.isInited = false;\n        // The marker data\n        this.markerData = [];\n        // The series stack\n        this.series = [];\n        // Texture handles\n        this.textureHandles = {};\n        // Width of our viewport in pixels\n        this.width = 0;\n        this.postRenderCallback = postRenderCallback;\n        this.settings = {\n            pointSize: 1,\n            lineWidth: 1,\n            fillColor: '#AA00AA',\n            useAlpha: true,\n            usePreallocated: false,\n            useGPUTranslations: false,\n            debug: {\n                timeRendering: false,\n                timeSeriesProcessing: false,\n                timeSetup: false,\n                timeBufferCopy: false,\n                timeKDTree: false,\n                showSkipSummary: false\n            }\n        };\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    getPixelRatio() {\n        return this.settings.pixelRatio || win.devicePixelRatio || 1;\n    }\n    /**\n     * @private\n     */\n    setOptions(options) {\n        // The pixelRatio defaults to 1. This is an antipattern, we should\n        // refactor the Boost options to include an object of default options as\n        // base for the merge, like other components.\n        if (!('pixelRatio' in options)) {\n            options.pixelRatio = 1;\n        }\n        merge(true, this.settings, options);\n    }\n    /**\n     * Allocate a float buffer to fit all series\n     * @private\n     */\n    allocateBuffer(chart) {\n        const vbuffer = this.vbuffer;\n        let s = 0;\n        if (!this.settings.usePreallocated) {\n            return;\n        }\n        chart.series.forEach((series) => {\n            if (series.boosted) {\n                s += WGLRenderer.seriesPointCount(series);\n            }\n        });\n        vbuffer && vbuffer.allocate(s);\n    }\n    /**\n     * @private\n     */\n    allocateBufferForSingleSeries(series) {\n        const vbuffer = this.vbuffer;\n        let s = 0;\n        if (!this.settings.usePreallocated) {\n            return;\n        }\n        if (series.boosted) {\n            s = WGLRenderer.seriesPointCount(series);\n        }\n        vbuffer && vbuffer.allocate(s);\n    }\n    /**\n     * Clear the depth and color buffer\n     * @private\n     */\n    clear() {\n        const gl = this.gl;\n        gl && gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);\n    }\n    /**\n     * Push data for a single series\n     * This calculates additional vertices and transforms the data to be\n     * aligned correctly in memory\n     * @private\n     */\n    pushSeriesData(series, inst) {\n        const data = this.data, settings = this.settings, vbuffer = this.vbuffer, isRange = (series.pointArrayMap &&\n            series.pointArrayMap.join(',') === 'low,high'), { chart, options, sorted, xAxis, yAxis } = series, isStacked = !!options.stacking, rawData = options.data, xExtremes = series.xAxis.getExtremes(), \n        // Taking into account the offset of the min point #19497\n        xMin = xExtremes.min - (series.xAxis.minPointOffset || 0), xMax = xExtremes.max + (series.xAxis.minPointOffset || 0), yExtremes = series.yAxis.getExtremes(), yMin = yExtremes.min - (series.yAxis.minPointOffset || 0), yMax = yExtremes.max + (series.yAxis.minPointOffset || 0), xData = (series.getColumn('x').length ? series.getColumn('x') : void 0) || options.xData || series.getColumn('x', true), yData = (series.getColumn('y').length ? series.getColumn('y') : void 0) || options.yData || series.getColumn('y', true), zData = (series.getColumn('z').length ? series.getColumn('z') : void 0) || options.zData || series.getColumn('z', true), useRaw = !xData || xData.length === 0, \n        /// threshold = options.threshold,\n        // yBottom = chart.yAxis[0].getThreshold(threshold),\n        // hasThreshold = isNumber(threshold),\n        // colorByPoint = series.options.colorByPoint,\n        // This is required for color by point, so make sure this is\n        // uncommented if enabling that\n        // colorIndex = 0,\n        // Required for color axis support\n        // caxis,\n        connectNulls = options.connectNulls, \n        // For some reason eslint/TypeScript don't pick up that this is\n        // actually used: --- bre1470: it is never read, just set\n        // maxVal: (number|undefined), // eslint-disable-line no-unused-vars\n        points = series.points || false, sdata = isStacked ? series.data : (xData || rawData), closestLeft = { x: Number.MAX_VALUE, y: 0 }, closestRight = { x: -Number.MAX_VALUE, y: 0 }, cullXThreshold = 1, cullYThreshold = 1, chartDestroyed = typeof chart.index === 'undefined', drawAsBar = asBar[series.type], zoneAxis = options.zoneAxis || 'y', zones = options.zones || false, threshold = options.threshold, pixelRatio = this.getPixelRatio();\n        let plotWidth = series.chart.plotWidth, lastX = false, lastY = false, minVal, scolor, \n        //\n        skipped = 0, hadPoints = false, \n        // The following are used in the builder while loop\n        x, y, d, z, i = -1, px = false, nx = false, low, nextInside = false, prevInside = false, pcolor = false, isXInside = false, isYInside = true, firstPoint = true, zoneColors, zoneDefColor = false, gapSize = false, vlen = 0;\n        if (options.boostData && options.boostData.length > 0) {\n            return;\n        }\n        if (options.gapSize) {\n            gapSize = options.gapUnit !== 'value' ?\n                options.gapSize * series.closestPointRange :\n                options.gapSize;\n        }\n        if (zones) {\n            zoneColors = [];\n            zones.forEach((zone, i) => {\n                if (zone.color) {\n                    const zoneColor = color(zone.color).rgba;\n                    zoneColor[0] /= 255.0;\n                    zoneColor[1] /= 255.0;\n                    zoneColor[2] /= 255.0;\n                    zoneColors[i] = zoneColor;\n                    if (!zoneDefColor && typeof zone.value === 'undefined') {\n                        zoneDefColor = zoneColor;\n                    }\n                }\n            });\n            if (!zoneDefColor) {\n                const seriesColor = ((series.pointAttribs && series.pointAttribs().fill) ||\n                    series.color);\n                zoneDefColor = color(seriesColor).rgba;\n                zoneDefColor[0] /= 255.0;\n                zoneDefColor[1] /= 255.0;\n                zoneDefColor[2] /= 255.0;\n            }\n        }\n        if (chart.inverted) {\n            plotWidth = series.chart.plotHeight;\n        }\n        series.closestPointRangePx = Number.MAX_VALUE;\n        /**\n         * Push color to color buffer - need to do this per vertex.\n         * @private\n         */\n        const pushColor = (color) => {\n            if (color) {\n                inst.colorData.push(color[0]);\n                inst.colorData.push(color[1]);\n                inst.colorData.push(color[2]);\n                inst.colorData.push(color[3]);\n            }\n        };\n        /**\n         * Push a vertice to the data buffer.\n         * @private\n         */\n        const vertice = (x, y, checkTreshold, pointSize = 1, color) => {\n            pushColor(color);\n            // Correct for pixel ratio\n            if (pixelRatio !== 1 && (!settings.useGPUTranslations ||\n                inst.skipTranslation)) {\n                x *= pixelRatio;\n                y *= pixelRatio;\n                pointSize *= pixelRatio;\n            }\n            if (settings.usePreallocated && vbuffer) {\n                vbuffer.push(x, y, checkTreshold ? 1 : 0, pointSize);\n                vlen += 4;\n            }\n            else {\n                data.push(x);\n                data.push(y);\n                data.push(checkTreshold ? pixelRatio : 0);\n                data.push(pointSize);\n            }\n        };\n        /**\n         * @private\n         */\n        const closeSegment = () => {\n            if (inst.segments.length) {\n                inst.segments[inst.segments.length - 1].to = data.length || vlen;\n            }\n        };\n        /**\n         * Create a new segment for the current set.\n         * @private\n         */\n        const beginSegment = () => {\n            // Insert a segment on the series.\n            // A segment is just a start indice.\n            // When adding a segment, if one exists from before, it should\n            // set the previous segment's end\n            if (inst.segments.length &&\n                inst.segments[inst.segments.length - 1].from === (data.length || vlen)) {\n                return;\n            }\n            closeSegment();\n            inst.segments.push({\n                from: data.length || vlen\n            });\n        };\n        /**\n         * Push a rectangle to the data buffer.\n         * @private\n         */\n        const pushRect = (x, y, w, h, color) => {\n            pushColor(color);\n            vertice(x + w, y);\n            pushColor(color);\n            vertice(x, y);\n            pushColor(color);\n            vertice(x, y + h);\n            pushColor(color);\n            vertice(x, y + h);\n            pushColor(color);\n            vertice(x + w, y + h);\n            pushColor(color);\n            vertice(x + w, y);\n        };\n        // Create the first segment\n        beginSegment();\n        // Special case for point shapes\n        if (points && points.length > 0) {\n            // If we're doing points, we assume that the points are already\n            // translated, so we skip the shader translation.\n            inst.skipTranslation = true;\n            // Force triangle draw mode\n            inst.drawMode = 'TRIANGLES';\n            // We don't have a z component in the shader, so we need to sort.\n            if (points[0].node && points[0].node.levelDynamic) {\n                points.sort((a, b) => {\n                    if (a.node) {\n                        if (a.node.levelDynamic >\n                            b.node.levelDynamic) {\n                            return 1;\n                        }\n                        if (a.node.levelDynamic <\n                            b.node.levelDynamic) {\n                            return -1;\n                        }\n                    }\n                    return 0;\n                });\n            }\n            points.forEach((point) => {\n                const plotY = point.plotY;\n                let swidth, pointAttr;\n                if (typeof plotY !== 'undefined' &&\n                    !isNaN(plotY) &&\n                    point.y !== null &&\n                    point.shapeArgs) {\n                    let { x = 0, y = 0, width = 0, height = 0 } = point.shapeArgs;\n                    pointAttr = chart.styledMode ?\n                        point.series\n                            .colorAttribs(point) :\n                        pointAttr = point.series.pointAttribs(point);\n                    swidth = pointAttr['stroke-width'] || 0;\n                    // Handle point colors\n                    pcolor = color(pointAttr.fill).rgba;\n                    pcolor[0] /= 255.0;\n                    pcolor[1] /= 255.0;\n                    pcolor[2] /= 255.0;\n                    // So there are two ways of doing this. Either we can\n                    // create a rectangle of two triangles, or we can do a\n                    // point and use point size. Latter is faster, but\n                    // only supports squares. So we're doing triangles.\n                    // We could also use one color per. vertice to get\n                    // better color interpolation.\n                    // If there's stroking, we do an additional rect\n                    if (series.is('treemap')) {\n                        swidth = swidth || 1;\n                        scolor = color(pointAttr.stroke).rgba;\n                        scolor[0] /= 255.0;\n                        scolor[1] /= 255.0;\n                        scolor[2] /= 255.0;\n                        pushRect(x, y, width, height, scolor);\n                        swidth /= 2;\n                    }\n                    // } else {\n                    //     swidth = 0;\n                    // }\n                    // Fixes issues with inverted heatmaps (see #6981). The root\n                    // cause is that the coordinate system is flipped. In other\n                    // words, instead of [0,0] being top-left, it's\n                    // bottom-right. This causes a vertical and horizontal flip\n                    // in the resulting image, making it rotated 180 degrees.\n                    if (series.is('heatmap') && chart.inverted) {\n                        x = xAxis.len - x;\n                        y = yAxis.len - y;\n                        width = -width;\n                        height = -height;\n                    }\n                    pushRect(x + swidth, y + swidth, width - (swidth * 2), height - (swidth * 2), pcolor);\n                }\n            });\n            closeSegment();\n            return;\n        }\n        // Extract color axis\n        // (chart.axes || []).forEach((a): void => {\n        //     if (H.ColorAxis && a instanceof H.ColorAxis) {\n        //         caxis = a;\n        //     }\n        // });\n        while (i < sdata.length - 1) {\n            d = sdata[++i];\n            if (typeof d === 'undefined') {\n                continue;\n            }\n            /// px = x = y = z = nx = low = false;\n            // chartDestroyed = typeof chart.index === 'undefined';\n            // nextInside = prevInside = pcolor = isXInside = isYInside = false;\n            // drawAsBar = asBar[series.type];\n            if (chartDestroyed) {\n                break;\n            }\n            // Uncomment this to enable color by point.\n            // This currently left disabled as the charts look really ugly\n            // when enabled and there's a lot of points.\n            // Leaving in for the future (tm).\n            // if (colorByPoint) {\n            //     colorIndex = ++colorIndex %\n            //         series.chart.options.colors.length;\n            //     pcolor = toRGBAFast(series.chart.options.colors[colorIndex]);\n            //     pcolor[0] /= 255.0;\n            //     pcolor[1] /= 255.0;\n            //     pcolor[2] /= 255.0;\n            // }\n            // Handle the point.color option (#5999)\n            const pointOptions = rawData && rawData[i];\n            if (!useRaw && isObject(pointOptions, true)) {\n                if (pointOptions.color) {\n                    pcolor = color(pointOptions.color).rgba;\n                    pcolor[0] /= 255.0;\n                    pcolor[1] /= 255.0;\n                    pcolor[2] /= 255.0;\n                }\n            }\n            if (useRaw) {\n                x = d[0];\n                y = d[1];\n                if (sdata[i + 1]) {\n                    nx = sdata[i + 1][0];\n                }\n                if (sdata[i - 1]) {\n                    px = sdata[i - 1][0];\n                }\n                if (d.length >= 3) {\n                    z = d[2];\n                    if (d[2] > inst.zMax) {\n                        inst.zMax = d[2];\n                    }\n                    if (d[2] < inst.zMin) {\n                        inst.zMin = d[2];\n                    }\n                }\n            }\n            else {\n                x = d;\n                y = yData?.[i];\n                if (sdata[i + 1]) {\n                    nx = sdata[i + 1];\n                }\n                if (sdata[i - 1]) {\n                    px = sdata[i - 1];\n                }\n                if (zData && zData.length) {\n                    z = zData[i];\n                    if (zData[i] > inst.zMax) {\n                        inst.zMax = zData[i];\n                    }\n                    if (zData[i] < inst.zMin) {\n                        inst.zMin = zData[i];\n                    }\n                }\n            }\n            if (!connectNulls && (x === null || y === null)) {\n                beginSegment();\n                continue;\n            }\n            if (nx && nx >= xMin && nx <= xMax) {\n                nextInside = true;\n            }\n            if (px && px >= xMin && px <= xMax) {\n                prevInside = true;\n            }\n            if (isRange) {\n                if (useRaw) {\n                    y = d.slice(1, 3);\n                }\n                low = series.getColumn('low', true)?.[i];\n                y = series.getColumn('high', true)?.[i] || 0;\n            }\n            else if (isStacked) {\n                x = d.x;\n                y = d.stackY;\n                low = y - d.y;\n            }\n            if (yMin !== null &&\n                typeof yMin !== 'undefined' &&\n                yMax !== null &&\n                typeof yMax !== 'undefined') {\n                isYInside = y >= yMin && y <= yMax;\n            }\n            // Do not render points outside the zoomed range (#19701)\n            if (!sorted && !isYInside) {\n                continue;\n            }\n            if (x > xMax && closestRight.x < xMax) {\n                closestRight.x = x;\n                closestRight.y = y;\n            }\n            if (x < xMin && closestLeft.x > xMin) {\n                closestLeft.x = x;\n                closestLeft.y = y;\n            }\n            if (y === null && connectNulls) {\n                continue;\n            }\n            // Cull points outside the extremes\n            // Continue if `sdata` has only one point as `nextInside` asserts\n            // whether the next point exists and will thus be false. (#22194)\n            if (y === null || (!isYInside && sdata.length > 1 &&\n                !nextInside && !prevInside)) {\n                beginSegment();\n                continue;\n            }\n            // The first point before and first after extremes should be\n            // rendered (#9962, 19701)\n            // Make sure series with a single point are rendered (#21897)\n            if (sorted && ((nx >= xMin || x >= xMin) &&\n                (px <= xMax || x <= xMax)) ||\n                !sorted && ((x >= xMin) && (x <= xMax))) {\n                isXInside = true;\n            }\n            if (!isXInside && !nextInside && !prevInside) {\n                continue;\n            }\n            if (gapSize && x - px > gapSize) {\n                beginSegment();\n            }\n            // Note: Boost requires that zones are sorted!\n            if (zones) {\n                let zoneColor;\n                zones.some((// eslint-disable-line no-loop-func\n                zone, i) => {\n                    const last = zones[i - 1];\n                    if (zoneAxis === 'x') {\n                        if (typeof zone.value !== 'undefined' &&\n                            x <= zone.value) {\n                            if (zoneColors[i] &&\n                                (!last || x >= last.value)) {\n                                zoneColor = zoneColors[i];\n                            }\n                            return true;\n                        }\n                        return false;\n                    }\n                    if (typeof zone.value !== 'undefined' && y <= zone.value) {\n                        if (zoneColors[i] &&\n                            (!last || y >= last.value)) {\n                            zoneColor = zoneColors[i];\n                        }\n                        return true;\n                    }\n                    return false;\n                });\n                pcolor = zoneColor || zoneDefColor || pcolor;\n            }\n            // Skip translations - temporary floating point fix\n            if (!settings.useGPUTranslations) {\n                inst.skipTranslation = true;\n                x = xAxis.toPixels(x, true);\n                y = yAxis.toPixels(y, true);\n                // Make sure we're not drawing outside of the chart area.\n                // See #6594. Update: this is no longer required as far as I\n                // can tell. Leaving in for git blame in case there are edge\n                // cases I've not found. Having this in breaks #10246.\n                // if (y > plotHeight) {\n                // y = plotHeight;\n                // }\n                if (x > plotWidth) {\n                    // If this is rendered as a point, just skip drawing it\n                    // entirely, as we're not dependant on lineTo'ing to it.\n                    // See #8197\n                    if (inst.drawMode === 'POINTS') {\n                        continue;\n                    }\n                    // Having this here will clamp markers and make the angle\n                    // of the last line wrong. See 9166.\n                    // x = plotWidth;\n                }\n            }\n            // No markers on out of bounds things.\n            // Out of bound things are shown if and only if the next\n            // or previous point is inside the rect.\n            if (inst.hasMarkers && isXInside) {\n                /// x = Highcharts.correctFloat(\n                //     Math.min(Math.max(-1e5, xAxis.translate(\n                //         x,\n                //         0,\n                //         0,\n                //         0,\n                //         1,\n                //         0.5,\n                //         false\n                //     )), 1e5)\n                // );\n                if (lastX !== false) {\n                    series.closestPointRangePx = Math.min(series.closestPointRangePx, Math.abs(x - lastX));\n                }\n            }\n            // If the last _drawn_ point is closer to this point than the\n            // threshold, skip it. Shaves off 20-100ms in processing.\n            if (!settings.useGPUTranslations &&\n                !settings.usePreallocated &&\n                (lastX && Math.abs(x - lastX) < cullXThreshold) &&\n                (lastY && Math.abs(y - lastY) < cullYThreshold)) {\n                if (settings.debug.showSkipSummary) {\n                    ++skipped;\n                }\n                continue;\n            }\n            if (drawAsBar) {\n                minVal = low || 0;\n                if (low === false || typeof low === 'undefined') {\n                    if (y < 0) {\n                        minVal = y;\n                    }\n                    else {\n                        minVal = 0;\n                    }\n                }\n                if ((!isRange && !isStacked) ||\n                    yAxis.logarithmic // #16850\n                ) {\n                    minVal = Math.max(threshold === null ? yMin : threshold, // #5268\n                    yMin); // #8731\n                }\n                if (!settings.useGPUTranslations) {\n                    minVal = yAxis.toPixels(minVal, true);\n                }\n                // Need to add an extra point here\n                vertice(x, minVal, 0, 0, pcolor);\n            }\n            // Do step line if enabled.\n            // Draws an additional point at the old Y at the new X.\n            // See #6976.\n            if (options.step && !firstPoint) {\n                vertice(x, lastY, 0, 2, pcolor);\n            }\n            vertice(x, y, 0, series.type === 'bubble' ? (z || 1) : 2, pcolor);\n            // Uncomment this to support color axis.\n            // if (caxis) {\n            //     pcolor = color(caxis.toColor(y)).rgba;\n            //     inst.colorData.push(color[0] / 255.0);\n            //     inst.colorData.push(color[1] / 255.0);\n            //     inst.colorData.push(color[2] / 255.0);\n            //     inst.colorData.push(color[3]);\n            // }\n            lastX = x;\n            lastY = y;\n            hadPoints = true;\n            firstPoint = false;\n        }\n        if (settings.debug.showSkipSummary) {\n            console.log('skipped points:', skipped); // eslint-disable-line no-console\n        }\n        const pushSupplementPoint = (point, atStart) => {\n            if (!settings.useGPUTranslations) {\n                inst.skipTranslation = true;\n                point.x = xAxis.toPixels(point.x, true);\n                point.y = yAxis.toPixels(point.y, true);\n            }\n            // We should only do this for lines, and we should ignore markers\n            // since there's no point here that would have a marker.\n            if (atStart) {\n                this.data = [point.x, point.y, 0, 2].concat(this.data);\n                return;\n            }\n            vertice(point.x, point.y, 0, 2);\n        };\n        if (!hadPoints &&\n            connectNulls !== false &&\n            series.drawMode === 'line_strip') {\n            if (closestLeft.x < Number.MAX_VALUE) {\n                // We actually need to push this *before* the complete buffer.\n                pushSupplementPoint(closestLeft, true);\n            }\n            if (closestRight.x > -Number.MAX_VALUE) {\n                pushSupplementPoint(closestRight);\n            }\n        }\n        closeSegment();\n    }\n    /**\n     * Push a series to the renderer\n     * If we render the series immediately, we don't have to loop later\n     * @private\n     * @param {Highchart.Series} s\n     * The series to push.\n     */\n    pushSeries(s) {\n        const markerData = this.markerData, series = this.series, settings = this.settings;\n        if (series.length > 0) {\n            if (series[series.length - 1].hasMarkers) {\n                series[series.length - 1].markerTo = markerData.length;\n            }\n        }\n        if (settings.debug.timeSeriesProcessing) {\n            console.time('building ' + s.type + ' series'); // eslint-disable-line no-console\n        }\n        const obj = {\n            segments: [],\n            markerFrom: markerData.length,\n            // Push RGBA values to this array to use per. point coloring.\n            // It should be 0-padded, so each component should be pushed in\n            // succession.\n            colorData: [],\n            series: s,\n            zMin: Number.MAX_VALUE,\n            zMax: -Number.MAX_VALUE,\n            hasMarkers: s.options.marker ?\n                s.options.marker.enabled !== false :\n                false,\n            showMarkers: true,\n            drawMode: Boost_WGLDrawMode[s.type] || 'LINE_STRIP'\n        };\n        if (s.index >= series.length) {\n            series.push(obj);\n        }\n        else {\n            series[s.index] = obj;\n        }\n        // Add the series data to our buffer(s)\n        this.pushSeriesData(s, obj);\n        if (settings.debug.timeSeriesProcessing) {\n            console.timeEnd('building ' + s.type + ' series'); // eslint-disable-line no-console\n        }\n    }\n    /**\n     * Flush the renderer.\n     * This removes pushed series and vertices.\n     * Should be called after clearing and before rendering\n     * @private\n     */\n    flush() {\n        const vbuffer = this.vbuffer;\n        this.data = [];\n        this.markerData = [];\n        this.series = [];\n        if (vbuffer) {\n            vbuffer.destroy();\n        }\n    }\n    /**\n     * Pass x-axis to shader\n     * @private\n     * @param {Highcharts.Axis} axis\n     * The x-axis.\n     */\n    setXAxis(axis) {\n        const shader = this.shader;\n        if (!shader) {\n            return;\n        }\n        const pixelRatio = this.getPixelRatio();\n        shader.setUniform('xAxisTrans', axis.transA * pixelRatio);\n        shader.setUniform('xAxisMin', axis.min);\n        shader.setUniform('xAxisMinPad', axis.minPixelPadding * pixelRatio);\n        shader.setUniform('xAxisPointRange', axis.pointRange);\n        shader.setUniform('xAxisLen', axis.len * pixelRatio);\n        shader.setUniform('xAxisPos', axis.pos * pixelRatio);\n        shader.setUniform('xAxisCVSCoord', (!axis.horiz));\n        shader.setUniform('xAxisIsLog', (!!axis.logarithmic));\n        shader.setUniform('xAxisReversed', (!!axis.reversed));\n    }\n    /**\n     * Pass y-axis to shader\n     * @private\n     * @param {Highcharts.Axis} axis\n     * The y-axis.\n     */\n    setYAxis(axis) {\n        const shader = this.shader;\n        if (!shader) {\n            return;\n        }\n        const pixelRatio = this.getPixelRatio();\n        shader.setUniform('yAxisTrans', axis.transA * pixelRatio);\n        shader.setUniform('yAxisMin', axis.min);\n        shader.setUniform('yAxisMinPad', axis.minPixelPadding * pixelRatio);\n        shader.setUniform('yAxisPointRange', axis.pointRange);\n        shader.setUniform('yAxisLen', axis.len * pixelRatio);\n        shader.setUniform('yAxisPos', axis.pos * pixelRatio);\n        shader.setUniform('yAxisCVSCoord', (!axis.horiz));\n        shader.setUniform('yAxisIsLog', (!!axis.logarithmic));\n        shader.setUniform('yAxisReversed', (!!axis.reversed));\n    }\n    /**\n     * Set the translation threshold\n     * @private\n     * @param {boolean} has\n     * Has threshold flag.\n     * @param {numbe} translation\n     * The threshold.\n     */\n    setThreshold(has, translation) {\n        const shader = this.shader;\n        if (!shader) {\n            return;\n        }\n        shader.setUniform('hasThreshold', has);\n        shader.setUniform('translatedThreshold', translation);\n    }\n    /**\n     * Render the data\n     * This renders all pushed series.\n     * @private\n     */\n    renderChart(chart) {\n        const gl = this.gl, settings = this.settings, shader = this.shader, vbuffer = this.vbuffer;\n        const pixelRatio = this.getPixelRatio();\n        if (chart) {\n            this.width = chart.chartWidth * pixelRatio;\n            this.height = chart.chartHeight * pixelRatio;\n        }\n        else {\n            return false;\n        }\n        const height = this.height, width = this.width;\n        if (!gl || !shader || !width || !height) {\n            return false;\n        }\n        if (settings.debug.timeRendering) {\n            console.time('gl rendering'); // eslint-disable-line no-console\n        }\n        gl.canvas.width = width;\n        gl.canvas.height = height;\n        shader.bind();\n        gl.viewport(0, 0, width, height);\n        shader.setPMatrix(WGLRenderer.orthoMatrix(width, height));\n        if (settings.lineWidth > 1 && !(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isMS) {\n            gl.lineWidth(settings.lineWidth);\n        }\n        if (vbuffer) {\n            vbuffer.build(this.data, 'aVertexPosition', 4);\n            vbuffer.bind();\n        }\n        shader.setInverted(chart.inverted);\n        // Render the series\n        this.series.forEach((s, si) => {\n            const options = s.series.options, shapeOptions = options.marker, lineWidth = (typeof options.lineWidth !== 'undefined' ?\n                options.lineWidth :\n                1), threshold = options.threshold, hasThreshold = isNumber(threshold), yBottom = s.series.yAxis.getThreshold(threshold), translatedThreshold = yBottom, showMarkers = WGLRenderer_pick(options.marker ? options.marker.enabled : null, s.series.xAxis.isRadial ? true : null, s.series.closestPointRangePx >\n                2 * ((options.marker ?\n                    options.marker.radius :\n                    10) || 10)), shapeTexture = this.textureHandles[(shapeOptions && shapeOptions.symbol) ||\n                s.series.symbol] || this.textureHandles.circle;\n            let sindex, cbuffer, fillColor, scolor = [];\n            if (s.segments.length === 0 ||\n                s.segments[0].from === s.segments[0].to) {\n                return;\n            }\n            if (shapeTexture.isReady) {\n                gl.bindTexture(gl.TEXTURE_2D, shapeTexture.handle);\n                shader.setTexture(shapeTexture.handle);\n            }\n            if (chart.styledMode) {\n                if (s.series.markerGroup === s.series.chart.boost?.markerGroup) {\n                    // Create a temporary markerGroup to get the fill color\n                    delete s.series.markerGroup;\n                    s.series.markerGroup = s.series.plotGroup('markerGroup', 'markers', 'visible', 1, chart.seriesGroup).addClass('highcharts-tracker');\n                    fillColor = s.series.markerGroup.getStyle('fill');\n                    s.series.markerGroup.destroy();\n                    s.series.markerGroup = s.series.chart.boost?.markerGroup;\n                }\n                else {\n                    fillColor = s.series.markerGroup?.getStyle('fill');\n                }\n            }\n            else {\n                fillColor =\n                    (s.drawMode === 'POINTS' && // #14260\n                        s.series.pointAttribs &&\n                        s.series.pointAttribs().fill) ||\n                        s.series.color;\n                if (options.colorByPoint) {\n                    fillColor = s.series.chart.options.colors[si];\n                }\n            }\n            if (s.series.fillOpacity && options.fillOpacity) {\n                fillColor = new (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default())(fillColor).setOpacity(WGLRenderer_pick(options.fillOpacity, 1.0)).get();\n            }\n            scolor = color(fillColor).rgba;\n            if (!settings.useAlpha) {\n                scolor[3] = 1.0;\n            }\n            // Blending\n            if (options.boostBlending === 'add') {\n                gl.blendFunc(gl.SRC_ALPHA, gl.ONE);\n                gl.blendEquation(gl.FUNC_ADD);\n            }\n            else if (options.boostBlending === 'mult' ||\n                options.boostBlending === 'multiply') {\n                gl.blendFunc(gl.DST_COLOR, gl.ZERO);\n            }\n            else if (options.boostBlending === 'darken') {\n                gl.blendFunc(gl.ONE, gl.ONE);\n                gl.blendEquation(gl.FUNC_MIN);\n            }\n            else {\n                /// gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);\n                // gl.blendEquation(gl.FUNC_ADD);\n                gl.blendFuncSeparate(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA, gl.ONE, gl.ONE_MINUS_SRC_ALPHA);\n            }\n            shader.reset();\n            // If there are entries in the colorData buffer, build and bind it.\n            if (s.colorData.length > 0) {\n                shader.setUniform('hasColor', 1);\n                cbuffer = new Boost_WGLVertexBuffer(gl, shader);\n                cbuffer.build(\n                // The color array attribute for vertex is assigned from 0,\n                // so it needs to be shifted to be applied to further\n                // segments. #18858\n                Array(s.segments[0].from).concat(s.colorData), 'aColor', 4);\n                cbuffer.bind();\n            }\n            else {\n                // Set the hasColor uniform to false (0) when the series\n                // contains no colorData buffer points. #18858\n                shader.setUniform('hasColor', 0);\n                // #15869, a buffer with fewer points might already be bound by\n                // a different series/chart causing out of range errors\n                gl.disableVertexAttribArray(gl.getAttribLocation(shader.getProgram(), 'aColor'));\n            }\n            // Set series specific uniforms\n            shader.setColor(scolor);\n            this.setXAxis(s.series.xAxis);\n            this.setYAxis(s.series.yAxis);\n            this.setThreshold(hasThreshold, translatedThreshold);\n            if (s.drawMode === 'POINTS') {\n                shader.setPointSize(WGLRenderer_pick(options.marker && options.marker.radius, 0.5) * 2 * pixelRatio);\n            }\n            // If set to true, the toPixels translations in the shader\n            // is skipped, i.e it's assumed that the value is a pixel coord.\n            shader.setSkipTranslation(s.skipTranslation);\n            if (s.series.type === 'bubble') {\n                shader.setBubbleUniforms(s.series, s.zMin, s.zMax, pixelRatio);\n            }\n            shader.setDrawAsCircle(asCircle[s.series.type] || false);\n            if (!vbuffer) {\n                return;\n            }\n            // Do the actual rendering\n            // If the line width is < 0, skip rendering of the lines. See #7833.\n            if (lineWidth > 0 || s.drawMode !== 'LINE_STRIP') {\n                for (sindex = 0; sindex < s.segments.length; sindex++) {\n                    vbuffer.render(s.segments[sindex].from, s.segments[sindex].to, s.drawMode);\n                }\n            }\n            if (s.hasMarkers && showMarkers) {\n                shader.setPointSize(WGLRenderer_pick(options.marker && options.marker.radius, 5) * 2 * pixelRatio);\n                shader.setDrawAsCircle(true);\n                for (sindex = 0; sindex < s.segments.length; sindex++) {\n                    vbuffer.render(s.segments[sindex].from, s.segments[sindex].to, 'POINTS');\n                }\n            }\n        });\n        if (settings.debug.timeRendering) {\n            console.timeEnd('gl rendering'); // eslint-disable-line no-console\n        }\n        if (this.postRenderCallback) {\n            this.postRenderCallback(this);\n        }\n        this.flush();\n    }\n    /**\n     * Render the data when ready\n     * @private\n     */\n    render(chart) {\n        this.clear();\n        if (chart.renderer.forExport) {\n            return this.renderChart(chart);\n        }\n        if (this.isInited) {\n            this.renderChart(chart);\n        }\n        else {\n            setTimeout(() => {\n                this.render(chart);\n            }, 1);\n        }\n    }\n    /**\n     * Set the viewport size in pixels\n     * Creates an orthographic perspective matrix and applies it.\n     * @private\n     */\n    setSize(width, height) {\n        const shader = this.shader;\n        // Skip if there's no change, or if we have no valid shader\n        if (!shader || (this.width === width && this.height === height)) {\n            return;\n        }\n        this.width = width;\n        this.height = height;\n        shader.bind();\n        shader.setPMatrix(WGLRenderer.orthoMatrix(width, height));\n    }\n    /**\n     * Init OpenGL\n     * @private\n     */\n    init(canvas, noFlush) {\n        const settings = this.settings;\n        this.isInited = false;\n        if (!canvas) {\n            return false;\n        }\n        if (settings.debug.timeSetup) {\n            console.time('gl setup'); // eslint-disable-line no-console\n        }\n        for (let i = 0; i < contexts.length; ++i) {\n            this.gl = canvas.getContext(contexts[i], {\n            //    /premultipliedAlpha: false\n            });\n            if (this.gl) {\n                break;\n            }\n        }\n        const gl = this.gl;\n        if (gl) {\n            if (!noFlush) {\n                this.flush();\n            }\n        }\n        else {\n            return false;\n        }\n        gl.enable(gl.BLEND);\n        /// gl.blendFunc(gl.SRC_ALPHA, gl.ONE);\n        gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);\n        gl.disable(gl.DEPTH_TEST);\n        /// gl.depthMask(gl.FALSE);\n        gl.depthFunc(gl.LESS);\n        const shader = this.shader = new Boost_WGLShader(gl);\n        if (!shader) {\n            // We need to abort, there's no shader context\n            return false;\n        }\n        this.vbuffer = new Boost_WGLVertexBuffer(gl, shader);\n        const createTexture = (name, fn) => {\n            const props = {\n                isReady: false,\n                texture: doc.createElement('canvas'),\n                handle: gl.createTexture()\n            }, ctx = props.texture.getContext('2d');\n            this.textureHandles[name] = props;\n            props.texture.width = 512;\n            props.texture.height = 512;\n            ctx.mozImageSmoothingEnabled = false;\n            ctx.webkitImageSmoothingEnabled = false;\n            ctx.msImageSmoothingEnabled = false;\n            ctx.imageSmoothingEnabled = false;\n            ctx.strokeStyle = 'rgba(255, 255, 255, 0)';\n            ctx.fillStyle = '#FFF';\n            fn(ctx);\n            try {\n                gl.activeTexture(gl.TEXTURE0);\n                gl.bindTexture(gl.TEXTURE_2D, props.handle);\n                /// gl.pixelStorei(gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL, true);\n                gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, props.texture);\n                gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);\n                gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);\n                gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);\n                gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);\n                /// gl.generateMipmap(gl.TEXTURE_2D);\n                gl.bindTexture(gl.TEXTURE_2D, null);\n                props.isReady = true;\n            }\n            catch (e) {\n                // Silent error\n            }\n        };\n        // Circle shape\n        createTexture('circle', (ctx) => {\n            ctx.beginPath();\n            ctx.arc(256, 256, 256, 0, 2 * Math.PI);\n            ctx.stroke();\n            ctx.fill();\n        });\n        // Square shape\n        createTexture('square', (ctx) => {\n            ctx.fillRect(0, 0, 512, 512);\n        });\n        // Diamond shape\n        createTexture('diamond', (ctx) => {\n            ctx.beginPath();\n            ctx.moveTo(256, 0);\n            ctx.lineTo(512, 256);\n            ctx.lineTo(256, 512);\n            ctx.lineTo(0, 256);\n            ctx.lineTo(256, 0);\n            ctx.fill();\n        });\n        // Triangle shape\n        createTexture('triangle', (ctx) => {\n            ctx.beginPath();\n            ctx.moveTo(0, 512);\n            ctx.lineTo(256, 0);\n            ctx.lineTo(512, 512);\n            ctx.lineTo(0, 512);\n            ctx.fill();\n        });\n        // Triangle shape (rotated)\n        createTexture('triangle-down', (ctx) => {\n            ctx.beginPath();\n            ctx.moveTo(0, 0);\n            ctx.lineTo(256, 512);\n            ctx.lineTo(512, 0);\n            ctx.lineTo(0, 0);\n            ctx.fill();\n        });\n        this.isInited = true;\n        if (settings.debug.timeSetup) {\n            console.timeEnd('gl setup'); // eslint-disable-line no-console\n        }\n        return true;\n    }\n    /**\n     * @private\n     * @todo use it\n     */\n    destroy() {\n        const gl = this.gl, shader = this.shader, vbuffer = this.vbuffer;\n        this.flush();\n        if (vbuffer) {\n            vbuffer.destroy();\n        }\n        if (shader) {\n            shader.destroy();\n        }\n        if (gl) {\n            objectEach(this.textureHandles, (texture) => {\n                if (texture.handle) {\n                    gl.deleteTexture(texture.handle);\n                }\n            });\n            gl.canvas.width = 1;\n            gl.canvas.height = 1;\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Boost_WGLRenderer = (WGLRenderer);\n\n;// ./code/es-modules/Data/ColumnUtils.js\n/* *\n *\n *  (c) 2020-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n/**\n * Utility functions for columns that can be either arrays or typed arrays.\n * @private\n */\nvar ColumnUtils;\n(function (ColumnUtils) {\n    /* *\n    *\n    *  Declarations\n    *\n    * */\n    /* *\n    *\n    * Functions\n    *\n    * */\n    /**\n     * Sets the length of the column array.\n     *\n     * @param {DataTable.Column} column\n     * Column to be modified.\n     *\n     * @param {number} length\n     * New length of the column.\n     *\n     * @param {boolean} asSubarray\n     * If column is a typed array, return a subarray instead of a new array. It\n     * is faster `O(1)`, but the entire buffer will be kept in memory until all\n     * views to it are destroyed. Default is `false`.\n     *\n     * @return {DataTable.Column}\n     * Modified column.\n     *\n     * @private\n     */\n    function setLength(column, length, asSubarray) {\n        if (Array.isArray(column)) {\n            column.length = length;\n            return column;\n        }\n        return column[asSubarray ? 'subarray' : 'slice'](0, length);\n    }\n    ColumnUtils.setLength = setLength;\n    /**\n     * Splices a column array.\n     *\n     * @param {DataTable.Column} column\n     * Column to be modified.\n     *\n     * @param {number} start\n     * Index at which to start changing the array.\n     *\n     * @param {number} deleteCount\n     * An integer indicating the number of old array elements to remove.\n     *\n     * @param {boolean} removedAsSubarray\n     * If column is a typed array, return a subarray instead of a new array. It\n     * is faster `O(1)`, but the entire buffer will be kept in memory until all\n     * views to it are destroyed. Default is `true`.\n     *\n     * @param {Array<number>|TypedArray} items\n     * The elements to add to the array, beginning at the start index. If you\n     * don't specify any elements, `splice()` will only remove elements from the\n     * array.\n     *\n     * @return {SpliceResult}\n     * Object containing removed elements and the modified column.\n     *\n     * @private\n     */\n    function splice(column, start, deleteCount, removedAsSubarray, items = []) {\n        if (Array.isArray(column)) {\n            if (!Array.isArray(items)) {\n                items = Array.from(items);\n            }\n            return {\n                removed: column.splice(start, deleteCount, ...items),\n                array: column\n            };\n        }\n        const Constructor = Object.getPrototypeOf(column)\n            .constructor;\n        const removed = column[removedAsSubarray ? 'subarray' : 'slice'](start, start + deleteCount);\n        const newLength = column.length - deleteCount + items.length;\n        const result = new Constructor(newLength);\n        result.set(column.subarray(0, start), 0);\n        result.set(items, start);\n        result.set(column.subarray(start + deleteCount), start + items.length);\n        return {\n            removed: removed,\n            array: result\n        };\n    }\n    ColumnUtils.splice = splice;\n})(ColumnUtils || (ColumnUtils = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Data_ColumnUtils = (ColumnUtils);\n\n;// ./code/es-modules/Data/DataTableCore.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Gøran Slettemark\n *  - Torstein Hønsi\n *\n * */\n\n\nconst { setLength, splice } = Data_ColumnUtils;\n\nconst { fireEvent, objectEach: DataTableCore_objectEach, uniqueKey } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * Class to manage columns and rows in a table structure. It provides methods\n * to add, remove, and manipulate columns and rows, as well as to retrieve data\n * from specific cells.\n *\n * @class\n * @name Highcharts.DataTable\n *\n * @param {Highcharts.DataTableOptions} [options]\n * Options to initialize the new DataTable instance.\n */\nclass DataTableCore {\n    /**\n     * Constructs an instance of the DataTable class.\n     *\n     * @example\n     * const dataTable = new Highcharts.DataTableCore({\n     *   columns: {\n     *     year: [2020, 2021, 2022, 2023],\n     *     cost: [11, 13, 12, 14],\n     *     revenue: [12, 15, 14, 18]\n     *   }\n     * });\n\n     *\n     * @param {Highcharts.DataTableOptions} [options]\n     * Options to initialize the new DataTable instance.\n     */\n    constructor(options = {}) {\n        /**\n         * Whether the ID was automatic generated or given in the constructor.\n         *\n         * @name Highcharts.DataTable#autoId\n         * @type {boolean}\n         */\n        this.autoId = !options.id;\n        this.columns = {};\n        /**\n         * ID of the table for identification purposes.\n         *\n         * @name Highcharts.DataTable#id\n         * @type {string}\n         */\n        this.id = (options.id || uniqueKey());\n        this.modified = this;\n        this.rowCount = 0;\n        this.versionTag = uniqueKey();\n        let rowCount = 0;\n        DataTableCore_objectEach(options.columns || {}, (column, columnName) => {\n            this.columns[columnName] = column.slice();\n            rowCount = Math.max(rowCount, column.length);\n        });\n        this.applyRowCount(rowCount);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Applies a row count to the table by setting the `rowCount` property and\n     * adjusting the length of all columns.\n     *\n     * @private\n     * @param {number} rowCount The new row count.\n     */\n    applyRowCount(rowCount) {\n        this.rowCount = rowCount;\n        DataTableCore_objectEach(this.columns, (column, columnName) => {\n            if (column.length !== rowCount) {\n                this.columns[columnName] = setLength(column, rowCount);\n            }\n        });\n    }\n    /**\n     * Delete rows. Simplified version of the full\n     * `DataTable.deleteRows` method.\n     *\n     * @param {number} rowIndex\n     * The start row index\n     *\n     * @param {number} [rowCount=1]\n     * The number of rows to delete\n     *\n     * @return {void}\n     *\n     * @emits #afterDeleteRows\n     */\n    deleteRows(rowIndex, rowCount = 1) {\n        if (rowCount > 0 && rowIndex < this.rowCount) {\n            let length = 0;\n            DataTableCore_objectEach(this.columns, (column, columnName) => {\n                this.columns[columnName] =\n                    splice(column, rowIndex, rowCount).array;\n                length = column.length;\n            });\n            this.rowCount = length;\n        }\n        fireEvent(this, 'afterDeleteRows', { rowIndex, rowCount });\n        this.versionTag = uniqueKey();\n    }\n    /**\n     * Fetches the given column by the canonical column name. Simplified version\n     * of the full `DataTable.getRow` method, always returning by reference.\n     *\n     * @param {string} columnName\n     * Name of the column to get.\n     *\n     * @return {Highcharts.DataTableColumn|undefined}\n     * A copy of the column, or `undefined` if not found.\n     */\n    getColumn(columnName, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    asReference) {\n        return this.columns[columnName];\n    }\n    /**\n     * Retrieves all or the given columns. Simplified version of the full\n     * `DataTable.getColumns` method, always returning by reference.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names to retrieve.\n     *\n     * @return {Highcharts.DataTableColumnCollection}\n     * Collection of columns. If a requested column was not found, it is\n     * `undefined`.\n     */\n    getColumns(columnNames, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    asReference) {\n        return (columnNames || Object.keys(this.columns)).reduce((columns, columnName) => {\n            columns[columnName] = this.columns[columnName];\n            return columns;\n        }, {});\n    }\n    /**\n     * Retrieves the row at a given index.\n     *\n     * @param {number} rowIndex\n     * Row index to retrieve. First row has index 0.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names to retrieve.\n     *\n     * @return {Record<string, number|string|undefined>|undefined}\n     * Returns the row values, or `undefined` if not found.\n     */\n    getRow(rowIndex, columnNames) {\n        return (columnNames || Object.keys(this.columns)).map((key) => this.columns[key]?.[rowIndex]);\n    }\n    /**\n     * Sets cell values for a column. Will insert a new column, if not found.\n     *\n     * @param {string} columnName\n     * Column name to set.\n     *\n     * @param {Highcharts.DataTableColumn} [column]\n     * Values to set in the column.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to change. (Default: 0)\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setColumns\n     * @emits #afterSetColumns\n     */\n    setColumn(columnName, column = [], rowIndex = 0, eventDetail) {\n        this.setColumns({ [columnName]: column }, rowIndex, eventDetail);\n    }\n    /**\n     * Sets cell values for multiple columns. Will insert new columns, if not\n     * found. Simplified version of the full `DataTableCore.setColumns`, limited\n     * to full replacement of the columns (undefined `rowIndex`).\n     *\n     * @param {Highcharts.DataTableColumnCollection} columns\n     * Columns as a collection, where the keys are the column names.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to change. Ignored in the `DataTableCore`, as it\n     * always replaces the full column.\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setColumns\n     * @emits #afterSetColumns\n     */\n    setColumns(columns, rowIndex, eventDetail) {\n        let rowCount = this.rowCount;\n        DataTableCore_objectEach(columns, (column, columnName) => {\n            this.columns[columnName] = column.slice();\n            rowCount = column.length;\n        });\n        this.applyRowCount(rowCount);\n        if (!eventDetail?.silent) {\n            fireEvent(this, 'afterSetColumns');\n            this.versionTag = uniqueKey();\n        }\n    }\n    /**\n     * Sets cell values of a row. Will insert a new row if no index was\n     * provided, or if the index is higher than the total number of table rows.\n     * A simplified version of the full `DateTable.setRow`, limited to objects.\n     *\n     * @param {Record<string, number|string|undefined>} row\n     * Cell values to set.\n     *\n     * @param {number} [rowIndex]\n     * Index of the row to set. Leave `undefined` to add as a new row.\n     *\n     * @param {boolean} [insert]\n     * Whether to insert the row at the given index, or to overwrite the row.\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #afterSetRows\n     */\n    setRow(row, rowIndex = this.rowCount, insert, eventDetail) {\n        const { columns } = this, indexRowCount = insert ? this.rowCount + 1 : rowIndex + 1;\n        DataTableCore_objectEach(row, (cellValue, columnName) => {\n            let column = columns[columnName] ||\n                eventDetail?.addColumns !== false && new Array(indexRowCount);\n            if (column) {\n                if (insert) {\n                    column = splice(column, rowIndex, 0, true, [cellValue]).array;\n                }\n                else {\n                    column[rowIndex] = cellValue;\n                }\n                columns[columnName] = column;\n            }\n        });\n        if (indexRowCount > this.rowCount) {\n            this.applyRowCount(indexRowCount);\n        }\n        if (!eventDetail?.silent) {\n            fireEvent(this, 'afterSetRows');\n            this.versionTag = uniqueKey();\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Data_DataTableCore = (DataTableCore);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * A typed array.\n * @typedef {Int8Array|Uint8Array|Uint8ClampedArray|Int16Array|Uint16Array|Int32Array|Uint32Array|Float32Array|Float64Array} Highcharts.TypedArray\n * //**\n * A column of values in a data table.\n * @typedef {Array<boolean|null|number|string|undefined>|Highcharts.TypedArray} Highcharts.DataTableColumn\n */ /**\n* A collection of data table columns defined by a object where the key is the\n* column name and the value is an array of the column values.\n* @typedef {Record<string, Highcharts.DataTableColumn>} Highcharts.DataTableColumnCollection\n*/\n/**\n * Options for the `DataTable` or `DataTableCore` classes.\n * @interface Highcharts.DataTableOptions\n */ /**\n* The column options for the data table. The columns are defined by an object\n* where the key is the column ID and the value is an array of the column\n* values.\n*\n* @name Highcharts.DataTableOptions.columns\n* @type {Highcharts.DataTableColumnCollection|undefined}\n*/ /**\n* Custom ID to identify the new DataTable instance.\n*\n* @name Highcharts.DataTableOptions.id\n* @type {string|undefined}\n*/\n(''); // Keeps doclets above in JS file\n\n;// ./code/es-modules/Extensions/Boost/BoostSeries.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { getBoostClipRect: BoostSeries_getBoostClipRect, isChartSeriesBoosting: BoostSeries_isChartSeriesBoosting } = Boost_BoostChart;\n\nconst { getOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { composed: BoostSeries_composed, doc: BoostSeries_doc, noop, win: BoostSeries_win } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent: BoostSeries_addEvent, destroyObjectProperties, error: BoostSeries_error, extend, fireEvent: BoostSeries_fireEvent, isArray, isNumber: BoostSeries_isNumber, pick: BoostSeries_pick, pushUnique: BoostSeries_pushUnique, wrap, defined } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\n/* *\n *\n *  Constants\n *\n * */\nconst CHUNK_SIZE = 3000;\n/* *\n *\n *  Variables\n *\n * */\nlet index, mainCanvas;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction allocateIfNotSeriesBoosting(renderer, series) {\n    const boost = series.boost;\n    if (renderer &&\n        boost &&\n        boost.target &&\n        boost.canvas &&\n        !BoostSeries_isChartSeriesBoosting(series.chart)) {\n        renderer.allocateBufferForSingleSeries(series);\n    }\n}\n/**\n * Return true if ths boost.enabled option is true\n *\n * @private\n * @param {Highcharts.Chart} chart\n * The chart\n * @return {boolean}\n * True, if boost is enabled.\n */\nfunction boostEnabled(chart) {\n    return BoostSeries_pick((chart &&\n        chart.options &&\n        chart.options.boost &&\n        chart.options.boost.enabled), true);\n}\n/**\n * @private\n */\nfunction BoostSeries_compose(SeriesClass, seriesTypes, PointClass, wglMode) {\n    if (BoostSeries_pushUnique(BoostSeries_composed, 'Boost.Series')) {\n        const plotOptions = getOptions().plotOptions, seriesProto = SeriesClass.prototype;\n        BoostSeries_addEvent(SeriesClass, 'destroy', onSeriesDestroy);\n        BoostSeries_addEvent(SeriesClass, 'hide', onSeriesHide);\n        if (wglMode) {\n            seriesProto.renderCanvas = seriesRenderCanvas;\n        }\n        wrap(seriesProto, 'getExtremes', wrapSeriesGetExtremes);\n        wrap(seriesProto, 'processData', wrapSeriesProcessData);\n        wrap(seriesProto, 'searchPoint', wrapSeriesSearchPoint);\n        [\n            'translate',\n            'generatePoints',\n            'drawTracker',\n            'drawPoints',\n            'render'\n        ].forEach((method) => wrapSeriesFunctions(seriesProto, seriesTypes, method));\n        wrap(PointClass.prototype, 'firePointEvent', function (proceed, type, e) {\n            if (type === 'click' && this.series.boosted) {\n                const point = e.point;\n                if ((point.dist || point.distX) >= (point.series.options.marker?.radius ?? 10)) {\n                    return;\n                }\n            }\n            return proceed.apply(this, [].slice.call(arguments, 1));\n        });\n        // Set default options\n        Boost_Boostables.forEach((type) => {\n            const typePlotOptions = plotOptions[type];\n            if (typePlotOptions) {\n                typePlotOptions.boostThreshold = 5000;\n                typePlotOptions.boostData = [];\n                seriesTypes[type].prototype.fillOpacity = true;\n            }\n        });\n        if (wglMode) {\n            const { area: AreaSeries, areaspline: AreaSplineSeries, bubble: BubbleSeries, column: ColumnSeries, heatmap: HeatmapSeries, scatter: ScatterSeries, treemap: TreemapSeries } = seriesTypes;\n            if (AreaSeries) {\n                extend(AreaSeries.prototype, {\n                    fill: true,\n                    fillOpacity: true,\n                    sampling: true\n                });\n            }\n            if (AreaSplineSeries) {\n                extend(AreaSplineSeries.prototype, {\n                    fill: true,\n                    fillOpacity: true,\n                    sampling: true\n                });\n            }\n            if (BubbleSeries) {\n                const bubbleProto = BubbleSeries.prototype;\n                // By default, the bubble series does not use the KD-tree, so\n                // force it to.\n                delete bubbleProto.buildKDTree;\n                // SeriesTypes.bubble.prototype.directTouch = false;\n                // Needed for markers to work correctly\n                wrap(bubbleProto, 'markerAttribs', function (proceed) {\n                    if (this.boosted) {\n                        return false;\n                    }\n                    return proceed.apply(this, [].slice.call(arguments, 1));\n                });\n            }\n            if (ColumnSeries) {\n                extend(ColumnSeries.prototype, {\n                    fill: true,\n                    sampling: true\n                });\n            }\n            if (ScatterSeries) {\n                ScatterSeries.prototype.fill = true;\n            }\n            // We need to handle heatmaps separately, since we can't perform the\n            // size/color calculations in the shader easily.\n            // @todo This likely needs future optimization.\n            [HeatmapSeries, TreemapSeries].forEach((SC) => {\n                if (SC) {\n                    wrap(SC.prototype, 'drawPoints', wrapSeriesDrawPoints);\n                }\n            });\n        }\n    }\n    return SeriesClass;\n}\n/**\n * Create a canvas + context and attach it to the target\n *\n * @private\n * @function createAndAttachRenderer\n *\n * @param {Highcharts.Chart} chart\n * the chart\n *\n * @param {Highcharts.Series} series\n * the series\n *\n * @return {Highcharts.BoostGLRenderer}\n * the canvas renderer\n */\nfunction createAndAttachRenderer(chart, series) {\n    const ChartClass = chart.constructor, targetGroup = chart.seriesGroup || series.group, alpha = 1;\n    let width = chart.chartWidth, height = chart.chartHeight, target = chart, foSupported = typeof SVGForeignObjectElement !== 'undefined', hasClickHandler = false;\n    if (BoostSeries_isChartSeriesBoosting(chart)) {\n        target = chart;\n    }\n    else {\n        target = series;\n        hasClickHandler = Boolean(series.options.events?.click ||\n            series.options.point?.events?.click);\n    }\n    const boost = target.boost =\n        target.boost ||\n            {};\n    // Support for foreignObject is flimsy as best.\n    // IE does not support it, and Chrome has a bug which messes up\n    // the canvas draw order.\n    // As such, we force the Image fallback for now, but leaving the\n    // actual Canvas path in-place in case this changes in the future.\n    foSupported = false;\n    if (!mainCanvas) {\n        mainCanvas = BoostSeries_doc.createElement('canvas');\n    }\n    if (!boost.target) {\n        boost.canvas = mainCanvas;\n        // Fall back to image tag if foreignObject isn't supported,\n        // or if we're exporting.\n        if (chart.renderer.forExport || !foSupported) {\n            target.renderTarget = boost.target = chart.renderer.image('', 0, 0, width, height)\n                .addClass('highcharts-boost-canvas')\n                .add(targetGroup);\n            boost.clear = function () {\n                boost.target.attr({\n                    // Insert a blank pixel (#17182)\n                    /* eslint-disable-next-line max-len*/\n                    href: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII='\n                });\n            };\n            boost.copy = function () {\n                boost.resize();\n                boost.target.attr({\n                    href: boost.canvas.toDataURL('image/png')\n                });\n            };\n        }\n        else {\n            boost.targetFo = chart.renderer\n                .createElement('foreignObject')\n                .add(targetGroup);\n            target.renderTarget = boost.target =\n                BoostSeries_doc.createElement('canvas');\n            boost.targetCtx = boost.target.getContext('2d');\n            boost.targetFo.element.appendChild(boost.target);\n            boost.clear = function () {\n                boost.target.width = boost.canvas.width;\n                boost.target.height = boost.canvas.height;\n            };\n            boost.copy = function () {\n                boost.target.width = boost.canvas.width;\n                boost.target.height = boost.canvas.height;\n                boost.targetCtx.drawImage(boost.canvas, 0, 0);\n            };\n        }\n        boost.resize = function () {\n            width = chart.chartWidth;\n            height = chart.chartHeight;\n            (boost.targetFo || boost.target)\n                .attr({\n                x: 0,\n                y: 0,\n                width,\n                height\n            })\n                .css({\n                pointerEvents: hasClickHandler ? void 0 : 'none',\n                mixedBlendMode: 'normal',\n                opacity: alpha\n            })\n                .addClass(hasClickHandler ? 'highcharts-tracker' : '');\n            if (target instanceof ChartClass) {\n                target.boost?.markerGroup?.translate(chart.plotLeft, chart.plotTop);\n            }\n        };\n        boost.clipRect = chart.renderer.clipRect();\n        (boost.targetFo || boost.target)\n            .attr({\n            // Set the z index of the boost target to that of the last\n            // series using it. This logic is not perfect, as it will not\n            // handle interleaved series with boost enabled or disabled. But\n            // it will cover the most common use case of one or more\n            // successive boosted or non-boosted series (#9819).\n            zIndex: series.options.zIndex\n        });\n        if (target instanceof ChartClass) {\n            target.boost.markerGroup = target.renderer\n                .g()\n                .add(targetGroup)\n                .translate(series.xAxis.pos, series.yAxis.pos);\n        }\n    }\n    boost.canvas.width = width;\n    boost.canvas.height = height;\n    if (boost.clipRect) {\n        const box = BoostSeries_getBoostClipRect(chart, target), \n        // When using panes, the image itself must be clipped. When not\n        // using panes, it is better to clip the target group, because then\n        // we preserve clipping on touch- and mousewheel zoom preview.\n        clippedElement = (box.width === chart.clipBox.width &&\n            box.height === chart.clipBox.height) ? targetGroup :\n            (boost.targetFo || boost.target);\n        boost.clipRect.attr(box);\n        clippedElement?.clip(boost.clipRect);\n    }\n    boost.resize();\n    boost.clear();\n    if (!boost.wgl) {\n        boost.wgl = new Boost_WGLRenderer((wgl) => {\n            if (wgl.settings.debug.timeBufferCopy) {\n                console.time('buffer copy'); // eslint-disable-line no-console\n            }\n            boost.copy();\n            if (wgl.settings.debug.timeBufferCopy) {\n                console.timeEnd('buffer copy'); // eslint-disable-line no-console\n            }\n        });\n        if (!boost.wgl.init(boost.canvas)) {\n            // The OGL renderer couldn't be inited. This likely means a shader\n            // error as we wouldn't get to this point if there was no WebGL\n            // support.\n            BoostSeries_error('[highcharts boost] - unable to init WebGL renderer');\n        }\n        boost.wgl.setOptions(chart.options.boost || {});\n        if (target instanceof ChartClass) {\n            boost.wgl.allocateBuffer(chart);\n        }\n    }\n    boost.wgl.setSize(width, height);\n    return boost.wgl;\n}\n/**\n * If implemented in the core, parts of this can probably be\n * shared with other similar methods in Highcharts.\n * @private\n * @function Highcharts.Series#destroyGraphics\n */\nfunction destroyGraphics(series) {\n    const points = series.points;\n    if (points) {\n        let point, i;\n        for (i = 0; i < points.length; i = i + 1) {\n            point = points[i];\n            if (point && point.destroyElements) {\n                point.destroyElements(); // #7557\n            }\n        }\n    }\n    ['graph', 'area', 'tracker'].forEach((prop) => {\n        const seriesProp = series[prop];\n        if (seriesProp) {\n            series[prop] = seriesProp.destroy();\n        }\n    });\n    for (const zone of series.zones) {\n        destroyObjectProperties(zone, void 0, true);\n    }\n}\n/**\n * An \"async\" foreach loop. Uses a setTimeout to keep the loop from blocking the\n * UI thread.\n *\n * @private\n * @param {Array<unknown>} arr\n * The array to loop through.\n * @param {Function} fn\n * The callback to call for each item.\n * @param {Function} finalFunc\n * The callback to call when done.\n * @param {number} [chunkSize]\n * The number of iterations per timeout.\n * @param {number} [i]\n * The current index.\n * @param {boolean} [noTimeout]\n * Set to true to skip timeouts.\n */\nfunction eachAsync(arr, fn, finalFunc, chunkSize, i, noTimeout) {\n    i = i || 0;\n    chunkSize = chunkSize || CHUNK_SIZE;\n    const threshold = i + chunkSize;\n    let proceed = true;\n    while (proceed && i < threshold && i < arr.length) {\n        proceed = fn(arr[i], i);\n        ++i;\n    }\n    if (proceed) {\n        if (i < arr.length) {\n            if (noTimeout) {\n                eachAsync(arr, fn, finalFunc, chunkSize, i, noTimeout);\n            }\n            else if (BoostSeries_win.requestAnimationFrame) {\n                // If available, do requestAnimationFrame - shaves off a few ms\n                BoostSeries_win.requestAnimationFrame(function () {\n                    eachAsync(arr, fn, finalFunc, chunkSize, i);\n                });\n            }\n            else {\n                setTimeout(eachAsync, 0, arr, fn, finalFunc, chunkSize, i);\n            }\n        }\n        else if (finalFunc) {\n            finalFunc();\n        }\n    }\n}\n/**\n * Enter boost mode and apply boost-specific properties.\n * @private\n * @function Highcharts.Series#enterBoost\n */\nfunction enterBoost(series) {\n    series.boost = series.boost || {\n        // Faster than a series bind:\n        getPoint: ((bp) => getPoint(series, bp))\n    };\n    const alteredByBoost = series.boost.altered = [];\n    // Save the original values, including whether it was an own\n    // property or inherited from the prototype.\n    ['allowDG', 'directTouch', 'stickyTracking'].forEach((prop) => {\n        alteredByBoost.push({\n            prop: prop,\n            val: series[prop],\n            own: Object.hasOwnProperty.call(series, prop)\n        });\n    });\n    series.allowDG = false;\n    series.directTouch = false;\n    series.stickyTracking = true;\n    // Prevent animation when zooming in on boosted series(#13421).\n    series.finishedAnimating = true;\n    // Hide series label if any\n    if (series.labelBySeries) {\n        series.labelBySeries = series.labelBySeries.destroy();\n    }\n    // Destroy existing points after zoom out\n    if (series.is('scatter') &&\n        !series.is('treemap') &&\n        series.data.length) {\n        for (const point of series.data) {\n            point?.destroy?.();\n        }\n        series.data.length = 0;\n        series.points.length = 0;\n        delete series.processedData;\n    }\n}\n/**\n * Exit from boost mode and restore non-boost properties.\n * @private\n * @function Highcharts.Series#exitBoost\n */\nfunction exitBoost(series) {\n    const boost = series.boost, chart = series.chart, chartBoost = chart.boost;\n    if (chartBoost?.markerGroup) {\n        chartBoost.markerGroup.destroy();\n        chartBoost.markerGroup = void 0;\n        for (const s of chart.series) {\n            s.markerGroup = void 0;\n            s.markerGroup = s.plotGroup('markerGroup', 'markers', 'visible', 1, chart.seriesGroup).addClass('highcharts-tracker');\n        }\n    }\n    // Reset instance properties and/or delete instance properties and go back\n    // to prototype\n    if (boost) {\n        (boost.altered || []).forEach((setting) => {\n            if (setting.own) {\n                series[setting.prop] = setting.val;\n            }\n            else {\n                // Revert to prototype\n                delete series[setting.prop];\n            }\n        });\n        // Clear previous run\n        if (boost.clear) {\n            boost.clear();\n        }\n    }\n    // #21106, clean up boost clipping on the series groups.\n    (chart.seriesGroup || series.group)?.clip();\n}\n/**\n * @private\n * @function Highcharts.Series#hasExtremes\n */\nfunction hasExtremes(series, checkX) {\n    const options = series.options, dataLength = series.dataTable.modified.rowCount, xAxis = series.xAxis && series.xAxis.options, yAxis = series.yAxis && series.yAxis.options, colorAxis = series.colorAxis && series.colorAxis.options;\n    return dataLength > (options.boostThreshold || Number.MAX_VALUE) &&\n        // Defined yAxis extremes\n        BoostSeries_isNumber(yAxis.min) &&\n        BoostSeries_isNumber(yAxis.max) &&\n        // Defined (and required) xAxis extremes\n        (!checkX ||\n            (BoostSeries_isNumber(xAxis.min) && BoostSeries_isNumber(xAxis.max))) &&\n        // Defined (e.g. heatmap) colorAxis extremes\n        (!colorAxis ||\n            (BoostSeries_isNumber(colorAxis.min) && BoostSeries_isNumber(colorAxis.max)));\n}\n/**\n * Used multiple times. In processData first on this.options.data, the second\n * time it runs the check again after processedXData is built.\n * If the data is going to be grouped, the series shouldn't be boosted.\n * @private\n */\nconst getSeriesBoosting = (series, data) => {\n    // Check if will be grouped.\n    if (series.forceCrop) {\n        return false;\n    }\n    return (BoostSeries_isChartSeriesBoosting(series.chart) ||\n        ((data ? data.length : 0) >=\n            (series.options.boostThreshold || Number.MAX_VALUE)));\n};\n/**\n * Extend series.destroy to also remove the fake k-d-tree points (#5137).\n * Normally this is handled by Series.destroy that calls Point.destroy,\n * but the fake search points are not registered like that.\n * @private\n */\nfunction onSeriesDestroy() {\n    const series = this, chart = series.chart;\n    if (chart.boost &&\n        chart.boost.markerGroup === series.markerGroup) {\n        series.markerGroup = void 0;\n    }\n    if (chart.hoverPoints) {\n        chart.hoverPoints = chart.hoverPoints.filter(function (point) {\n            return point.series === series;\n        });\n    }\n    if (chart.hoverPoint && chart.hoverPoint.series === series) {\n        chart.hoverPoint = void 0;\n    }\n}\n/**\n * @private\n */\nfunction onSeriesHide() {\n    const boost = this.boost;\n    if (boost && boost.canvas && boost.target) {\n        if (boost.wgl) {\n            boost.wgl.clear();\n        }\n        if (boost.clear) {\n            boost.clear();\n        }\n    }\n}\n/**\n * Performs the actual render if the renderer is\n * attached to the series.\n * @private\n */\nfunction renderIfNotSeriesBoosting(series) {\n    const boost = series.boost;\n    if (boost &&\n        boost.canvas &&\n        boost.target &&\n        boost.wgl &&\n        !BoostSeries_isChartSeriesBoosting(series.chart)) {\n        boost.wgl.render(series.chart);\n    }\n}\n/**\n * Return a full Point object based on the index.\n * The boost module uses stripped point objects for performance reasons.\n * @private\n * @param {object|Highcharts.Point} boostPoint\n *        A stripped-down point object\n * @return {Highcharts.Point}\n *         A Point object as per https://api.highcharts.com/highcharts#Point\n */\nfunction getPoint(series, boostPoint) {\n    const seriesOptions = series.options, xAxis = series.xAxis, PointClass = series.pointClass;\n    if (boostPoint instanceof PointClass) {\n        return boostPoint;\n    }\n    const isScatter = series.is('scatter'), xData = ((isScatter && series.getColumn('x', true).length ?\n        series.getColumn('x', true) :\n        void 0) ||\n        (series.getColumn('x').length ? series.getColumn('x') : void 0) ||\n        seriesOptions.xData ||\n        series.getColumn('x', true) ||\n        false), yData = (series.getColumn('y', true) ||\n        seriesOptions.yData ||\n        false), point = new PointClass(series, (isScatter && xData && yData) ?\n        [xData[boostPoint.i], yData[boostPoint.i]] :\n        (isArray(series.options.data) ? series.options.data : [])[boostPoint.i], xData ? xData[boostPoint.i] : void 0);\n    point.category = BoostSeries_pick(xAxis.categories ?\n        xAxis.categories[point.x] :\n        point.x, // @todo simplify\n    point.x);\n    point.key = point.name ?? point.category;\n    point.dist = boostPoint.dist;\n    point.distX = boostPoint.distX;\n    point.plotX = boostPoint.plotX;\n    point.plotY = boostPoint.plotY;\n    point.index = boostPoint.i;\n    point.percentage = boostPoint.percentage;\n    point.isInside = series.isPointInside(point);\n    return point;\n}\n/**\n * @private\n */\nfunction scatterProcessData(force) {\n    const series = this, { options, xAxis, yAxis } = series;\n    // Process only on changes\n    if (!series.isDirty &&\n        !xAxis.isDirty &&\n        !yAxis.isDirty &&\n        !force) {\n        return false;\n    }\n    // Required to get tick-based zoom ranges that take options into account\n    // like `minPadding`, `maxPadding`, `startOnTick`, `endOnTick`.\n    series.yAxis.setTickInterval();\n    const boostThreshold = options.boostThreshold || 0, cropThreshold = options.cropThreshold, xData = series.getColumn('x'), xExtremes = xAxis.getExtremes(), xMax = xExtremes.max ?? Number.MAX_VALUE, xMin = xExtremes.min ?? -Number.MAX_VALUE, yData = series.getColumn('y'), yExtremes = yAxis.getExtremes(), yMax = yExtremes.max ?? Number.MAX_VALUE, yMin = yExtremes.min ?? -Number.MAX_VALUE;\n    // Skip processing in non-boost zoom\n    if (!series.boosted &&\n        xAxis.old &&\n        yAxis.old &&\n        xMin >= (xAxis.old.min ?? -Number.MAX_VALUE) &&\n        xMax <= (xAxis.old.max ?? Number.MAX_VALUE) &&\n        yMin >= (yAxis.old.min ?? -Number.MAX_VALUE) &&\n        yMax <= (yAxis.old.max ?? Number.MAX_VALUE)) {\n        series.dataTable.modified.setColumns({\n            x: xData,\n            y: yData\n        });\n        return true;\n    }\n    // Without thresholds just assign data\n    const dataLength = series.dataTable.rowCount;\n    if (!boostThreshold ||\n        dataLength < boostThreshold ||\n        (cropThreshold &&\n            !series.forceCrop &&\n            !series.getExtremesFromAll &&\n            !options.getExtremesFromAll &&\n            dataLength < cropThreshold)) {\n        series.dataTable.modified.setColumns({\n            x: xData,\n            y: yData\n        });\n        return true;\n    }\n    // Filter unsorted scatter data for ranges\n    const processedData = [], processedXData = [], processedYData = [], xRangeNeeded = !(BoostSeries_isNumber(xExtremes.max) || BoostSeries_isNumber(xExtremes.min)), yRangeNeeded = !(BoostSeries_isNumber(yExtremes.max) || BoostSeries_isNumber(yExtremes.min));\n    let cropped = false, x, xDataMax = xData[0], xDataMin = xData[0], y, yDataMax = yData?.[0], yDataMin = yData?.[0];\n    for (let i = 0, iEnd = xData.length; i < iEnd; ++i) {\n        x = xData[i];\n        y = yData?.[i];\n        if (x >= xMin && x <= xMax &&\n            y >= yMin && y <= yMax) {\n            processedData.push({ x, y });\n            processedXData.push(x);\n            processedYData.push(y);\n            if (xRangeNeeded) {\n                xDataMax = Math.max(xDataMax, x);\n                xDataMin = Math.min(xDataMin, x);\n            }\n            if (yRangeNeeded) {\n                yDataMax = Math.max(yDataMax, y);\n                yDataMin = Math.min(yDataMin, y);\n            }\n        }\n        else {\n            cropped = true;\n        }\n    }\n    if (xRangeNeeded) {\n        xAxis.dataMax = Math.max(xDataMax, xAxis.dataMax || 0);\n        xAxis.dataMin = Math.min(xDataMin, xAxis.dataMin || 0);\n    }\n    if (yRangeNeeded) {\n        yAxis.dataMax = Math.max(yDataMax, yAxis.dataMax || 0);\n        yAxis.dataMin = Math.min(yDataMin, yAxis.dataMin || 0);\n    }\n    // Set properties as base processData\n    series.cropped = cropped;\n    series.cropStart = 0;\n    // For boosted points rendering\n    if (cropped && series.dataTable.modified === series.dataTable) {\n        // Calling setColumns with cropped data must be done on a new instance\n        // to avoid modification of the original (complete) data\n        series.dataTable.modified = new Data_DataTableCore();\n    }\n    series.dataTable.modified.setColumns({\n        x: processedXData,\n        y: processedYData\n    });\n    if (!getSeriesBoosting(series, processedXData)) {\n        series.processedData = processedData; // For un-boosted points rendering\n    }\n    return true;\n}\n/**\n * @private\n * @function Highcharts.Series#renderCanvas\n */\nfunction seriesRenderCanvas() {\n    const options = this.options || {}, chart = this.chart, chartBoost = chart.boost, seriesBoost = this.boost, xAxis = this.xAxis, yAxis = this.yAxis, xData = options.xData || this.getColumn('x', true), yData = options.yData || this.getColumn('y', true), lowData = this.getColumn('low', true), highData = this.getColumn('high', true), rawData = this.processedData || options.data, xExtremes = xAxis.getExtremes(), \n    // Taking into account the offset of the min point #19497\n    xMin = xExtremes.min - (xAxis.minPointOffset || 0), xMax = xExtremes.max + (xAxis.minPointOffset || 0), yExtremes = yAxis.getExtremes(), yMin = yExtremes.min - (yAxis.minPointOffset || 0), yMax = yExtremes.max + (yAxis.minPointOffset || 0), pointTaken = {}, sampling = !!this.sampling, enableMouseTracking = options.enableMouseTracking, threshold = options.threshold, isRange = this.pointArrayMap &&\n        this.pointArrayMap.join(',') === 'low,high', isStacked = !!options.stacking, cropStart = this.cropStart || 0, requireSorting = this.requireSorting, useRaw = !xData, compareX = options.findNearestPointBy === 'x', xDataFull = ((this.getColumn('x').length ?\n        this.getColumn('x') :\n        void 0) ||\n        this.options.xData ||\n        this.getColumn('x', true)), lineWidth = BoostSeries_pick(options.lineWidth, 1), nullYSubstitute = options.nullInteraction && yMin, tooltip = chart.tooltip;\n    let renderer = false, lastClientX, yBottom = yAxis.getThreshold(threshold), minVal, maxVal, minI, maxI;\n    // Clear mock points and tooltip after zoom (#20330)\n    if (!this.boosted) {\n        return;\n    }\n    this.points?.forEach((point) => {\n        point?.destroyElements?.();\n    });\n    this.points = [];\n    if (tooltip && !tooltip.isHidden) {\n        const isSeriesHovered = chart.hoverPoint?.series === this ||\n            chart.hoverPoints?.some((point) => point.series === this);\n        if (isSeriesHovered) {\n            chart.hoverPoint = chart.hoverPoints = void 0;\n            tooltip.hide(0);\n        }\n    }\n    else if (chart.hoverPoints) {\n        chart.hoverPoints = chart.hoverPoints.filter((point) => point.series !== this);\n    }\n    // When touch-zooming or mouse-panning, re-rendering the canvas would not\n    // perform fast enough. Instead, let the axes redraw, but not the series.\n    // The series is scale-translated in an event handler for an approximate\n    // preview.\n    if (xAxis.isPanning || yAxis.isPanning) {\n        return;\n    }\n    // Get or create the renderer\n    renderer = createAndAttachRenderer(chart, this);\n    chart.boosted = true;\n    if (!this.visible) {\n        return;\n    }\n    // If we are zooming out from SVG mode, destroy the graphics\n    if (this.points || this.graph) {\n        destroyGraphics(this);\n    }\n    // If we're rendering per. series we should create the marker groups\n    // as usual.\n    if (!BoostSeries_isChartSeriesBoosting(chart)) {\n        // If all series were boosting, but are not anymore\n        // restore private markerGroup\n        if (this.markerGroup === chartBoost?.markerGroup) {\n            this.markerGroup = void 0;\n        }\n        this.markerGroup = this.plotGroup('markerGroup', 'markers', 'visible', 1, chart.seriesGroup).addClass('highcharts-tracker');\n    }\n    else {\n        // If series has a private markerGroup, remove that\n        // and use common markerGroup\n        if (this.markerGroup &&\n            this.markerGroup !== chartBoost?.markerGroup) {\n            this.markerGroup.destroy();\n        }\n        // Use a single group for the markers\n        this.markerGroup = chartBoost?.markerGroup;\n        // When switching from chart boosting mode, destroy redundant\n        // series boosting targets\n        if (seriesBoost && seriesBoost.target) {\n            this.renderTarget =\n                seriesBoost.target =\n                    seriesBoost.target.destroy();\n        }\n    }\n    const points = this.points = [], addKDPoint = (clientX, plotY, i, percentage) => {\n        const x = xDataFull ? xDataFull[cropStart + i] : false, pushPoint = (plotX) => {\n            if (chart.inverted) {\n                plotX = xAxis.len - plotX;\n                plotY = yAxis.len - plotY;\n            }\n            points.push({\n                destroy: noop,\n                x: x,\n                clientX: plotX,\n                plotX: plotX,\n                plotY: plotY,\n                i: cropStart + i,\n                percentage: percentage\n            });\n        };\n        // We need to do ceil on the clientX to make things\n        // snap to pixel values. The renderer will frequently\n        // draw stuff on \"sub-pixels\".\n        clientX = Math.ceil(clientX);\n        // Shaves off about 60ms compared to repeated concatenation\n        index = compareX ? clientX : clientX + ',' + plotY;\n        // The k-d tree requires series points.\n        // Reduce the amount of points, since the time to build the\n        // tree increases exponentially.\n        if (enableMouseTracking) {\n            if (!pointTaken[index]) {\n                pointTaken[index] = true;\n                pushPoint(clientX);\n            }\n            else if (x === xDataFull[xDataFull.length - 1]) {\n                // If the last point is on the same pixel as the last\n                // tracked point, swap them. (#18856)\n                points.length--;\n                pushPoint(clientX);\n            }\n        }\n    };\n    // Do not start building while drawing\n    this.buildKDTree = noop;\n    BoostSeries_fireEvent(this, 'renderCanvas');\n    if (this.is('line') &&\n        lineWidth > 1 &&\n        seriesBoost?.target &&\n        chartBoost &&\n        !chartBoost.lineWidthFilter) {\n        chartBoost.lineWidthFilter = chart.renderer.definition({\n            tagName: 'filter',\n            children: [\n                {\n                    tagName: 'feMorphology',\n                    attributes: {\n                        operator: 'dilate',\n                        radius: 0.25 * lineWidth\n                    }\n                }\n            ],\n            attributes: { id: 'linewidth' }\n        });\n        seriesBoost.target.attr({\n            filter: 'url(#linewidth)'\n        });\n    }\n    if (renderer) {\n        allocateIfNotSeriesBoosting(renderer, this);\n        renderer.pushSeries(this);\n        // Perform the actual renderer if we're on series level\n        renderIfNotSeriesBoosting(this);\n    }\n    /**\n     * This builds the KD-tree\n     * @private\n     */\n    function processPoint(d, i) {\n        const chartDestroyed = typeof chart.index === 'undefined';\n        let x, y, clientX, plotY, percentage, low = false, isYInside = true;\n        if (!defined(d)) {\n            return true;\n        }\n        if (!chartDestroyed) {\n            if (useRaw) {\n                x = d[0];\n                y = d[1];\n            }\n            else {\n                x = d;\n                y = yData[i] ?? nullYSubstitute ?? null;\n            }\n            // Resolve low and high for range series\n            if (isRange) {\n                if (useRaw) {\n                    y = d.slice(1, 3);\n                }\n                low = lowData[i];\n                y = highData[i];\n            }\n            else if (isStacked) {\n                x = d.x;\n                y = d.stackY;\n                low = y - d.y;\n                percentage = d.percentage;\n            }\n            // Optimize for scatter zooming\n            if (!requireSorting) {\n                isYInside = (y || 0) >= yMin && y <= yMax;\n            }\n            if (y !== null && x >= xMin && x <= xMax && isYInside) {\n                clientX = xAxis.toPixels(x, true);\n                if (sampling) {\n                    if (typeof minI === 'undefined' ||\n                        clientX === lastClientX) {\n                        if (!isRange) {\n                            low = y;\n                        }\n                        if (typeof maxI === 'undefined' ||\n                            y > maxVal) {\n                            maxVal = y;\n                            maxI = i;\n                        }\n                        if (typeof minI === 'undefined' ||\n                            low < minVal) {\n                            minVal = low;\n                            minI = i;\n                        }\n                    }\n                    // Add points and reset\n                    if (!compareX || clientX !== lastClientX) {\n                        // `maxI` is number too:\n                        if (typeof minI !== 'undefined') {\n                            plotY =\n                                yAxis.toPixels(maxVal, true);\n                            yBottom =\n                                yAxis.toPixels(minVal, true);\n                            addKDPoint(clientX, plotY, maxI, percentage);\n                            if (yBottom !== plotY) {\n                                addKDPoint(clientX, yBottom, minI, percentage);\n                            }\n                        }\n                        minI = maxI = void 0;\n                        lastClientX = clientX;\n                    }\n                }\n                else {\n                    plotY = Math.ceil(yAxis.toPixels(y, true));\n                    addKDPoint(clientX, plotY, i, percentage);\n                }\n            }\n        }\n        return !chartDestroyed;\n    }\n    /**\n     * @private\n     */\n    const boostOptions = renderer.settings, doneProcessing = () => {\n        BoostSeries_fireEvent(this, 'renderedCanvas');\n        // Go back to prototype, ready to build\n        delete this.buildKDTree;\n        // Check that options exist, as async processing\n        // could mean the series is removed at this point (#19895)\n        if (this.options) {\n            this.buildKDTree();\n        }\n        if (boostOptions.debug.timeKDTree) {\n            console.timeEnd('kd tree building'); // eslint-disable-line no-console\n        }\n    };\n    // Loop over the points to build the k-d tree - skip this if\n    // exporting\n    if (!chart.renderer.forExport) {\n        if (boostOptions.debug.timeKDTree) {\n            console.time('kd tree building'); // eslint-disable-line no-console\n        }\n        eachAsync(isStacked ?\n            this.data.slice(cropStart) :\n            (xData || rawData), processPoint, doneProcessing);\n    }\n}\n/**\n * Used for treemap|heatmap.drawPoints\n * @private\n */\nfunction wrapSeriesDrawPoints(proceed) {\n    let enabled = true;\n    if (this.chart.options && this.chart.options.boost) {\n        enabled = typeof this.chart.options.boost.enabled === 'undefined' ?\n            true :\n            this.chart.options.boost.enabled;\n    }\n    if (!enabled || !this.boosted) {\n        return proceed.call(this);\n    }\n    this.chart.boosted = true;\n    // Make sure we have a valid OGL context\n    const renderer = createAndAttachRenderer(this.chart, this);\n    if (renderer) {\n        allocateIfNotSeriesBoosting(renderer, this);\n        renderer.pushSeries(this);\n    }\n    renderIfNotSeriesBoosting(this);\n}\n/**\n * Override a bunch of methods the same way. If the number of points is\n * below the threshold, run the original method. If not, check for a\n * canvas version or do nothing.\n *\n * Note that we're not overriding any of these for heatmaps.\n */\nfunction wrapSeriesFunctions(seriesProto, seriesTypes, method) {\n    /**\n     * @private\n     */\n    function branch(proceed) {\n        const letItPass = this.options.stacking &&\n            (method === 'translate' || method === 'generatePoints');\n        if (!this.boosted ||\n            letItPass ||\n            !boostEnabled(this.chart) ||\n            this.type === 'heatmap' ||\n            this.type === 'treemap' ||\n            !Boost_BoostableMap[this.type] ||\n            this.options.boostThreshold === 0) {\n            proceed.call(this);\n            // Run canvas version of method, like renderCanvas(), if it exists\n        }\n        else if (method === 'render' && this.renderCanvas) {\n            this.renderCanvas();\n        }\n    }\n    wrap(seriesProto, method, branch);\n    // Special case for some types, when translate method is already wrapped\n    if (method === 'translate') {\n        for (const type of [\n            'column',\n            'arearange',\n            'columnrange',\n            'heatmap',\n            'treemap'\n        ]) {\n            if (seriesTypes[type]) {\n                wrap(seriesTypes[type].prototype, method, branch);\n            }\n        }\n    }\n}\n/**\n * Do not compute extremes when min and max are set. If we use this in the\n * core, we can add the hook to hasExtremes to the methods directly.\n * @private\n */\nfunction wrapSeriesGetExtremes(proceed) {\n    if (this.boosted) {\n        if (hasExtremes(this)) {\n            return {};\n        }\n        if (this.xAxis.isPanning || this.yAxis.isPanning) {\n            // Do not re-compute the extremes during panning, because looping\n            // the data is expensive. The `this` contains the `dataMin` and\n            // `dataMax` to use.\n            return this;\n        }\n    }\n    return proceed.apply(this, [].slice.call(arguments, 1));\n}\n/**\n * If the series is a heatmap or treemap, or if the series is not boosting\n * do the default behaviour. Otherwise, process if the series has no\n * extremes.\n * @private\n */\nfunction wrapSeriesProcessData(proceed) {\n    let dataToMeasure = this.options.data;\n    if (boostEnabled(this.chart) && Boost_BoostableMap[this.type]) {\n        const series = this, \n        // Flag for code that should run for ScatterSeries and its\n        // subclasses, apart from the enlisted exceptions.\n        isScatter = series.is('scatter') &&\n            !series.is('bubble') &&\n            !series.is('treemap') &&\n            !series.is('heatmap');\n        // If there are no extremes given in the options, we also need to\n        // process the data to read the data extremes. If this is a heatmap,\n        // do default behaviour.\n        if (\n        // First pass with options.data:\n        !getSeriesBoosting(series, dataToMeasure) ||\n            isScatter ||\n            series.is('treemap') ||\n            // Use processedYData for the stack (#7481):\n            series.options.stacking ||\n            !hasExtremes(series, true)) {\n            // Do nothing until the panning stops\n            if (series.boosted && (series.xAxis?.isPanning || series.yAxis?.isPanning)) {\n                return;\n            }\n            // Extra check for zoomed scatter data\n            if (isScatter && series.yAxis.type !== 'treegrid') {\n                scatterProcessData.call(series, arguments[1]);\n            }\n            else {\n                proceed.apply(series, [].slice.call(arguments, 1));\n            }\n            dataToMeasure = series.getColumn('x', true);\n        }\n        // Set the isBoosting flag, second pass with processedXData to\n        // see if we have zoomed.\n        series.boosted = getSeriesBoosting(series, dataToMeasure);\n        // Enter or exit boost mode\n        if (series.boosted) {\n            // Force turbo-mode:\n            let firstPoint;\n            if (series.options.data?.length) {\n                firstPoint = series.getFirstValidPoint(series.options.data);\n                if (!BoostSeries_isNumber(firstPoint) &&\n                    !isArray(firstPoint) &&\n                    !series.is('treemap')) {\n                    BoostSeries_error(12, false, series.chart);\n                }\n            }\n            enterBoost(series);\n        }\n        else {\n            exitBoost(series);\n        }\n        // The series type is not boostable\n    }\n    else {\n        proceed.apply(this, [].slice.call(arguments, 1));\n    }\n}\n/**\n * Return a point instance from the k-d-tree\n * @private\n */\nfunction wrapSeriesSearchPoint(proceed) {\n    const result = proceed.apply(this, [].slice.call(arguments, 1));\n    if (this.boost && result) {\n        return this.boost.getPoint(result);\n    }\n    return result;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst BoostSeries = {\n    compose: BoostSeries_compose,\n    destroyGraphics,\n    eachAsync,\n    getPoint\n};\n/* harmony default export */ const Boost_BoostSeries = (BoostSeries);\n\n;// ./code/es-modules/Extensions/Boost/NamedColors.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n// Register color names since GL can't render those directly.\n// TODO: When supporting modern syntax, make this a named export\nconst defaultHTMLColorMap = {\n    aliceblue: '#f0f8ff',\n    antiquewhite: '#faebd7',\n    aqua: '#00ffff',\n    aquamarine: '#7fffd4',\n    azure: '#f0ffff',\n    beige: '#f5f5dc',\n    bisque: '#ffe4c4',\n    blanchedalmond: '#ffebcd',\n    blue: '#0000ff',\n    blueviolet: '#8a2be2',\n    brown: '#a52a2a',\n    burlywood: '#deb887',\n    cadetblue: '#5f9ea0',\n    chartreuse: '#7fff00',\n    chocolate: '#d2691e',\n    coral: '#ff7f50',\n    cornflowerblue: '#6495ed',\n    cornsilk: '#fff8dc',\n    crimson: '#dc143c',\n    cyan: '#00ffff',\n    darkblue: '#00008b',\n    darkcyan: '#008b8b',\n    darkgoldenrod: '#b8860b',\n    darkgray: '#a9a9a9',\n    darkgreen: '#006400',\n    darkkhaki: '#bdb76b',\n    darkmagenta: '#8b008b',\n    darkolivegreen: '#556b2f',\n    darkorange: '#ff8c00',\n    darkorchid: '#9932cc',\n    darkred: '#8b0000',\n    darksalmon: '#e9967a',\n    darkseagreen: '#8fbc8f',\n    darkslateblue: '#483d8b',\n    darkslategray: '#2f4f4f',\n    darkturquoise: '#00ced1',\n    darkviolet: '#9400d3',\n    deeppink: '#ff1493',\n    deepskyblue: '#00bfff',\n    dimgray: '#696969',\n    dodgerblue: '#1e90ff',\n    feldspar: '#d19275',\n    firebrick: '#b22222',\n    floralwhite: '#fffaf0',\n    forestgreen: '#228b22',\n    fuchsia: '#ff00ff',\n    gainsboro: '#dcdcdc',\n    ghostwhite: '#f8f8ff',\n    gold: '#ffd700',\n    goldenrod: '#daa520',\n    gray: '#808080',\n    grey: '#808080',\n    green: '#008000',\n    greenyellow: '#adff2f',\n    honeydew: '#f0fff0',\n    hotpink: '#ff69b4',\n    indianred: '#cd5c5c',\n    indigo: '#4b0082',\n    ivory: '#fffff0',\n    khaki: '#f0e68c',\n    lavender: '#e6e6fa',\n    lavenderblush: '#fff0f5',\n    lawngreen: '#7cfc00',\n    lemonchiffon: '#fffacd',\n    lightblue: '#add8e6',\n    lightcoral: '#f08080',\n    lightcyan: '#e0ffff',\n    lightgoldenrodyellow: '#fafad2',\n    lightgrey: '#d3d3d3',\n    lightgreen: '#90ee90',\n    lightpink: '#ffb6c1',\n    lightsalmon: '#ffa07a',\n    lightseagreen: '#20b2aa',\n    lightskyblue: '#87cefa',\n    lightslateblue: '#8470ff',\n    lightslategray: '#778899',\n    lightsteelblue: '#b0c4de',\n    lightyellow: '#ffffe0',\n    lime: '#00ff00',\n    limegreen: '#32cd32',\n    linen: '#faf0e6',\n    magenta: '#ff00ff',\n    maroon: '#800000',\n    mediumaquamarine: '#66cdaa',\n    mediumblue: '#0000cd',\n    mediumorchid: '#ba55d3',\n    mediumpurple: '#9370d8',\n    mediumseagreen: '#3cb371',\n    mediumslateblue: '#7b68ee',\n    mediumspringgreen: '#00fa9a',\n    mediumturquoise: '#48d1cc',\n    mediumvioletred: '#c71585',\n    midnightblue: '#191970',\n    mintcream: '#f5fffa',\n    mistyrose: '#ffe4e1',\n    moccasin: '#ffe4b5',\n    navajowhite: '#ffdead',\n    navy: '#000080',\n    oldlace: '#fdf5e6',\n    olive: '#808000',\n    olivedrab: '#6b8e23',\n    orange: '#ffa500',\n    orangered: '#ff4500',\n    orchid: '#da70d6',\n    palegoldenrod: '#eee8aa',\n    palegreen: '#98fb98',\n    paleturquoise: '#afeeee',\n    palevioletred: '#d87093',\n    papayawhip: '#ffefd5',\n    peachpuff: '#ffdab9',\n    peru: '#cd853f',\n    pink: '#ffc0cb',\n    plum: '#dda0dd',\n    powderblue: '#b0e0e6',\n    purple: '#800080',\n    red: '#ff0000',\n    rosybrown: '#bc8f8f',\n    royalblue: '#4169e1',\n    saddlebrown: '#8b4513',\n    salmon: '#fa8072',\n    sandybrown: '#f4a460',\n    seagreen: '#2e8b57',\n    seashell: '#fff5ee',\n    sienna: '#a0522d',\n    silver: '#c0c0c0',\n    skyblue: '#87ceeb',\n    slateblue: '#6a5acd',\n    slategray: '#708090',\n    snow: '#fffafa',\n    springgreen: '#00ff7f',\n    steelblue: '#4682b4',\n    tan: '#d2b48c',\n    teal: '#008080',\n    thistle: '#d8bfd8',\n    tomato: '#ff6347',\n    turquoise: '#40e0d0',\n    violet: '#ee82ee',\n    violetred: '#d02090',\n    wheat: '#f5deb3',\n    whitesmoke: '#f5f5f5',\n    yellow: '#ffff00',\n    yellowgreen: '#9acd32'\n};\n/* *\n *\n *  Default Export\n *\n * */\nconst namedColors = {\n    defaultHTMLColorMap\n};\n/* harmony default export */ const NamedColors = (namedColors);\n\n;// ./code/es-modules/Extensions/Boost/Boost.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { doc: Boost_doc, win: Boost_win } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nconst { addEvent: Boost_addEvent, error: Boost_error } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\nconst Boost_contexts = [\n    'webgl',\n    'experimental-webgl',\n    'moz-webgl',\n    'webkit-3d'\n];\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction Boost_compose(ChartClass, AxisClass, SeriesClass, seriesTypes, PointClass, ColorClass) {\n    const wglMode = hasWebGLSupport();\n    if (!wglMode) {\n        if (typeof (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).initCanvasBoost !== 'undefined') {\n            // Fallback to canvas boost\n            highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().initCanvasBoost();\n        }\n        else {\n            Boost_error(26);\n        }\n    }\n    if (ColorClass && !ColorClass.names.lightgoldenrodyellow) {\n        ColorClass.names = {\n            ...ColorClass.names,\n            ...NamedColors.defaultHTMLColorMap\n        };\n    }\n    // WebGL support is alright, and we're good to go.\n    Boost_BoostChart.compose(ChartClass, wglMode);\n    Boost_BoostSeries.compose(SeriesClass, seriesTypes, PointClass, wglMode);\n    // Handle zooming by touch/pinch or mouse wheel. Assume that boosted charts\n    // are too slow for a live preview while dragging. Instead, just scale the\n    // div while `isPanning`.\n    Boost_addEvent(AxisClass, 'setExtremes', function (e) {\n        // Render targets can be either chart-wide or series-specific\n        const renderTargets = [this.chart, ...this.series]\n            .map((item) => item.renderTarget)\n            .filter(Boolean);\n        for (const renderTarget of renderTargets) {\n            const { horiz, pos } = this, scaleKey = horiz ? 'scaleX' : 'scaleY', translateKey = horiz ? 'translateX' : 'translateY', lastScale = renderTarget?.[scaleKey] ?? 1;\n            let scale = 1, translate = 0, opacity = 1, filter = 'none';\n            if (this.isPanning) {\n                scale = (e.scale ?? 1) * lastScale;\n                translate = (renderTarget?.[translateKey] || 0) -\n                    scale * (e.move || 0) +\n                    lastScale * pos -\n                    scale * pos;\n                opacity = 0.7;\n                filter = 'blur(3px)';\n            }\n            renderTarget\n                ?.attr({\n                [scaleKey]: scale,\n                [translateKey]: translate\n            })\n                .css({\n                transition: '250ms filter, 250ms opacity',\n                filter,\n                opacity\n            });\n        }\n    });\n}\n/**\n * Returns true if the current browser supports WebGL.\n *\n * @requires modules/boost\n *\n * @function Highcharts.hasWebGLSupport\n *\n * @return {boolean}\n * `true` if the browser supports WebGL.\n */\nfunction hasWebGLSupport() {\n    let canvas, gl = false;\n    if (typeof Boost_win.WebGLRenderingContext !== 'undefined') {\n        canvas = Boost_doc.createElement('canvas');\n        for (let i = 0; i < Boost_contexts.length; ++i) {\n            try {\n                gl = canvas.getContext(Boost_contexts[i]);\n                if (typeof gl !== 'undefined' && gl !== null) {\n                    return true;\n                }\n            }\n            catch (e) {\n                // Silent error\n            }\n        }\n    }\n    return false;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst Boost = {\n    compose: Boost_compose,\n    hasWebGLSupport\n};\n/* harmony default export */ const Boost_Boost = (Boost);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Options for the Boost module. The Boost module allows certain series types\n * to be rendered by WebGL instead of the default SVG. This allows hundreds of\n * thousands of data points to be rendered in milliseconds. In addition to the\n * WebGL rendering it saves time by skipping processing and inspection of the\n * data wherever possible. This introduces some limitations to what features are\n * available in boost mode. See [the docs](\n * https://www.highcharts.com/docs/advanced-chart-features/boost-module) for\n * details.\n *\n * In addition to the global `boost` option, each series has a\n * [boostThreshold](#plotOptions.series.boostThreshold) that defines when the\n * boost should kick in.\n *\n * Requires the `modules/boost.js` module.\n *\n * @sample {highstock} highcharts/boost/line-series-heavy-stock\n *         Stock chart\n * @sample {highstock} highcharts/boost/line-series-heavy-dynamic\n *         Dynamic stock chart\n * @sample highcharts/boost/line\n *         Line chart\n * @sample highcharts/boost/line-series-heavy\n *         Line chart with hundreds of series\n * @sample highcharts/boost/scatter\n *         Scatter chart\n * @sample highcharts/boost/area\n *         Area chart\n * @sample highcharts/boost/arearange\n *         Area range chart\n * @sample highcharts/boost/column\n *         Column chart\n * @sample highcharts/boost/columnrange\n *         Column range chart\n * @sample highcharts/boost/bubble\n *         Bubble chart\n * @sample highcharts/boost/heatmap\n *         Heat map\n * @sample highcharts/boost/treemap\n *         Tree map\n *\n * @product   highcharts highstock\n * @requires  modules/boost\n * @apioption boost\n */\n/**\n * The chart will be boosted, if one of the series crosses its threshold and all\n * the series in the chart can be boosted.\n *\n * @type      {boolean}\n * @default   true\n * @apioption boost.allowForce\n */\n/**\n * Enable or disable boost on a chart.\n *\n * @type      {boolean}\n * @default   true\n * @apioption boost.enabled\n */\n/**\n * Debugging options for boost.\n * Useful for benchmarking, and general timing.\n *\n * @apioption boost.debug\n */\n/**\n * Time the series rendering.\n *\n * This outputs the time spent on actual rendering in the console when\n * set to true.\n *\n * @type      {boolean}\n * @default   false\n * @apioption boost.debug.timeRendering\n */\n/**\n * Time the series processing.\n *\n * This outputs the time spent on transforming the series data to\n * vertex buffers when set to true.\n *\n * @type      {boolean}\n * @default   false\n * @apioption boost.debug.timeSeriesProcessing\n */\n/**\n * Time the WebGL setup.\n *\n * This outputs the time spent on setting up the WebGL context,\n * creating shaders, and textures.\n *\n * @type      {boolean}\n * @default   false\n * @apioption boost.debug.timeSetup\n */\n/**\n * Time the building of the k-d tree.\n *\n * This outputs the time spent building the k-d tree used for\n * markers etc.\n *\n * Note that the k-d tree is built async, and runs post-rendering.\n * Following, it does not affect the performance of the rendering itself.\n *\n * @type      {boolean}\n * @default   false\n * @apioption boost.debug.timeKDTree\n */\n/**\n * Show the number of points skipped through culling.\n *\n * When set to true, the number of points skipped in series processing\n * is outputted. Points are skipped if they are closer than 1 pixel from\n * each other.\n *\n * @type      {boolean}\n * @default   false\n * @apioption boost.debug.showSkipSummary\n */\n/**\n * Time the WebGL to SVG buffer copy\n *\n * After rendering, the result is copied to an image which is injected\n * into the SVG.\n *\n * If this property is set to true, the time it takes for the buffer copy\n * to complete is outputted.\n *\n * @type      {boolean}\n * @default   false\n * @apioption boost.debug.timeBufferCopy\n */\n/**\n * The pixel ratio for the WebGL content. If 0, the `window.devicePixelRatio` is\n * used. This ensures sharp graphics on high DPI displays like Apple's Retina,\n * as well as when a page is zoomed.\n *\n * The default is left at 1 for now, as this is a new feature that has the\n * potential to break existing setups. Over time, when it has been battle\n * tested, the intention is to set it to 0 by default.\n *\n * Another use case for this option is to set it to 2 in order to make exported\n * and upscaled charts render sharp.\n *\n * One limitation when using the `pixelRatio` is that the line width of graphs\n * is scaled down. Since the Boost module currently can only render 1px line\n * widths, it is scaled down to a thin 0.5 pixels on a Retina display.\n *\n * @sample    highcharts/boost/line-devicepixelratio\n *            Enable the `devicePixelRatio`\n * @sample    highcharts/boost/line-export-pixelratio\n *            Sharper graphics in export\n *\n * @type      {number}\n * @since 10.0.0\n * @default   1\n * @apioption boost.pixelRatio\n */\n/**\n * Set the series threshold for when the boost should kick in globally.\n *\n * Setting to e.g. 20 will cause the whole chart to enter boost mode\n * if there are 20 or more series active. When the chart is in boost mode,\n * every series in it will be rendered to a common canvas. This offers\n * a significant speed improvement in charts with a very high\n * amount of series.\n *\n * @type      {number}\n * @default   50\n * @apioption boost.seriesThreshold\n */\n/**\n * Enable or disable GPU translations. GPU translations are faster than doing\n * the translation in JavaScript.\n *\n * This option may cause rendering issues with certain datasets.\n * Namely, if your dataset has large numbers with small increments (such as\n * timestamps), it won't work correctly. This is due to floating point\n * precision.\n *\n * @type      {boolean}\n * @default   false\n * @apioption boost.useGPUTranslations\n */\n/**\n * Enable or disable pre-allocation of vertex buffers.\n *\n * Enabling this will make it so that the binary data arrays required for\n * storing the series data will be allocated prior to transforming the data\n * to a WebGL-compatible format.\n *\n * This saves a copy operation on the order of O(n) and so is significantly more\n * performant. However, this is currently an experimental option, and may cause\n * visual artifacts with some datasets.\n *\n * As such, care should be taken when using this setting to make sure that\n * it doesn't cause any rendering glitches with the given use-case.\n *\n * @type      {boolean}\n * @default   false\n * @apioption boost.usePreallocated\n */\n/**\n * Set the point threshold for when a series should enter boost mode.\n *\n * Setting it to e.g. 2000 will cause the series to enter boost mode when there\n * are 2000 or more points in the series.\n *\n * To disable boosting on the series, set the `boostThreshold` to 0. Setting it\n * to 1 will force boosting.\n *\n * Note that the [cropThreshold](plotOptions.series.cropThreshold) also affects\n * this setting. When zooming in on a series that has fewer points than the\n * `cropThreshold`, all points are rendered although outside the visible plot\n * area, and the `boostThreshold` won't take effect.\n *\n * @type      {number}\n * @default   5000\n * @requires  modules/boost\n * @apioption plotOptions.series.boostThreshold\n */\n/**\n * Sets the color blending in the boost module.\n *\n * @type       {string}\n * @default    undefined\n * @validvalue [\"add\", \"multiply\", \"darken\"]\n * @requires   modules/boost\n * @apioption  plotOptions.series.boostBlending\n */\n''; // Adds doclets above to transpiled file\n\n;// ./code/es-modules/masters/modules/boost.js\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nG.hasWebGLSupport = Boost_Boost.hasWebGLSupport;\nBoost_Boost.compose(G.Chart, G.Axis, G.Series, G.seriesTypes, G.Point, G.Color);\n/* harmony default export */ const boost_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__620__", "index", "mainCanvas", "Column<PERSON><PERSON><PERSON>", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "boost_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "Boost_Boostables", "BoostableMap", "for<PERSON>ach", "item", "composed", "addEvent", "pick", "pushUnique", "isChartSeriesBoosting", "chart", "allSeries", "series", "boost", "boostOptions", "options", "threshold", "seriesThreshold", "length", "allowBoostForce", "allowForce", "axis", "xAxis", "min", "Infinity", "dataMin", "max", "dataMax", "forceChartBoost", "canBoostCount", "needBoostCount", "seriesOptions", "boostThreshold", "visible", "type", "Boost_BoostableMap", "patientMax", "args", "r", "Number", "MAX_VALUE", "t", "getColumn", "data", "points", "onChartCallback", "canvasToSVG", "wgl", "render", "boosted", "axes", "some", "isPanning", "clear", "canvas", "allocate<PERSON><PERSON><PERSON>", "markerGroup", "yAxis", "translate", "pos", "order", "prevX", "prevY", "pointer", "e", "hoverPoint", "inverted", "s", "halo", "hide", "<PERSON><PERSON><PERSON>", "compose", "ChartClass", "wglMode", "callbacks", "push", "getBoostClipRect", "target", "navigator", "clipBox", "x", "plotLeft", "y", "plotTop", "width", "plot<PERSON>id<PERSON>", "height", "plotHeight", "top", "opposite", "left", "is", "getClipBox", "lateral", "verticalAxes", "Math", "len", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default", "Boost_WGLDrawMode", "clamp", "error", "WG<PERSON><PERSON><PERSON>_pick", "Boost_WGLShader", "constructor", "gl", "errors", "uLocations", "createShader", "bind", "shaderProgram", "useProgram", "v", "stringToProgram", "f", "uloc", "getUniformLocation", "createProgram", "<PERSON><PERSON><PERSON><PERSON>", "linkProgram", "getProgramParameter", "LINK_STATUS", "bindAttribLocation", "pUniform", "psUniform", "fcUniform", "isBubbleUniform", "bubbleSizeAbsUniform", "bubbleSizeAreaUniform", "uSamplerUniform", "skipTranslationUniform", "isCircleUniform", "isInverted", "getProgramInfoLog", "handleErrors", "join", "str", "shader", "VERTEX_SHADER", "FRAGMENT_SHADER", "shaderSource", "compileShader", "getShaderParameter", "COMPILE_STATUS", "getShaderInfoLog", "destroy", "deleteProgram", "fillColorUniform", "getProgram", "pointSizeUniform", "perspectiveUniform", "reset", "uniform1i", "setBubbleUniforms", "zCalcMin", "zCalcMax", "pixelRatio", "zMin", "zMax", "pxSizes", "getPxExtremes", "displayNegative", "zThreshold", "sizeBy", "sizeByAbsoluteValue", "setUniform", "minPxSize", "maxPxSize", "setColor", "color", "uniform4f", "setDrawAsCircle", "flag", "setInverted", "setPMatrix", "m", "uniformMatrix4fv", "setPointSize", "p", "uniform1f", "setSkipTranslation", "setTexture", "texture", "name", "val", "u", "Boost_WGLVertexBuffer", "dataComponents", "buffer", "iterator", "preAllocated", "vertAttribute", "components", "allocate", "size", "Float32Array", "vertexAttribPointer", "FLOAT", "build", "dataIn", "attrib", "farray", "deleteBuffer", "createBuffer", "<PERSON><PERSON><PERSON><PERSON>", "ARRAY_BUFFER", "bufferData", "STATIC_DRAW", "getAttribLocation", "enableVertexAttribArray", "b", "from", "to", "drawMode", "drawArrays", "parse", "doc", "win", "isNumber", "isObject", "merge", "objectEach", "WG<PERSON>enderer_pick", "asBar", "asCircle", "contexts", "WG<PERSON><PERSON><PERSON>", "orthoMatrix", "seriesPointCount", "isStacked", "xData", "stacking", "postRender<PERSON>allback", "isInited", "markerData", "textureHandles", "settings", "pointSize", "lineWidth", "fillColor", "useAlpha", "usePreallocated", "useGPUTranslations", "debug", "timeRendering", "timeSeriesProcessing", "timeSetup", "timeBufferCopy", "timeKDTree", "showSkipSummary", "getPixelRatio", "devicePixelRatio", "setOptions", "vbuffer", "allocateBufferForSingleSeries", "COLOR_BUFFER_BIT", "DEPTH_BUFFER_BIT", "pushSeriesData", "inst", "isRange", "pointArrayMap", "sorted", "rawData", "xExtremes", "getExtremes", "xMin", "minPointOffset", "xMax", "yExtremes", "yMin", "yMax", "yData", "zData", "useRaw", "connectNulls", "sdata", "closestLeft", "closestRight", "chartDestroyed", "drawAsBar", "zoneAxis", "zones", "lastX", "lastY", "minVal", "scolor", "skipped", "hadPoints", "z", "i", "px", "nx", "low", "nextInside", "prevInside", "pcolor", "isXInside", "isYInside", "firstPoint", "zoneColors", "zoneDefColor", "gapSize", "vlen", "boostData", "gapUnit", "closestPointRange", "zone", "zoneColor", "rgba", "value", "pointAttribs", "fill", "closestPointRangePx", "pushColor", "colorData", "vertice", "checkTreshold", "skipTranslation", "closeSegment", "segments", "beginSegment", "pushRect", "w", "h", "node", "levelDynamic", "sort", "point", "swidth", "pointAttr", "plotY", "isNaN", "shapeArgs", "styledMode", "colorAttribs", "stroke", "pointOptions", "slice", "stackY", "last", "toPixels", "hasMark<PERSON>", "abs", "logarithmic", "step", "console", "log", "pushSupplementPoint", "atStart", "concat", "pushSeries", "markerTo", "time", "markerFrom", "marker", "enabled", "showMarkers", "timeEnd", "flush", "setXAxis", "transA", "minPixelPadding", "pointRange", "horiz", "reversed", "setYAxis", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "has", "translation", "<PERSON><PERSON><PERSON>", "chartWidth", "chartHeight", "viewport", "isMS", "si", "shapeOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yBottom", "get<PERSON><PERSON><PERSON>old", "isRadial", "radius", "shapeTexture", "symbol", "circle", "sindex", "cbuffer", "isReady", "bindTexture", "TEXTURE_2D", "handle", "plotGroup", "seriesGroup", "addClass", "getStyle", "colorByPoint", "colors", "fillOpacity", "setOpacity", "boostBlending", "blendFunc", "SRC_ALPHA", "ONE", "blendEquation", "FUNC_ADD", "DST_COLOR", "ZERO", "FUNC_MIN", "blendFuncSeparate", "ONE_MINUS_SRC_ALPHA", "Array", "disableVertexAttribArray", "renderer", "forExport", "setTimeout", "setSize", "init", "noFlush", "getContext", "enable", "BLEND", "disable", "DEPTH_TEST", "depthFunc", "LESS", "createTexture", "fn", "props", "createElement", "ctx", "mozImageSmoothingEnabled", "webkitImageSmoothingEnabled", "msImageSmoothingEnabled", "imageSmoothingEnabled", "strokeStyle", "fillStyle", "activeTexture", "TEXTURE0", "texImage2D", "RGBA", "UNSIGNED_BYTE", "texParameteri", "TEXTURE_WRAP_S", "CLAMP_TO_EDGE", "TEXTURE_WRAP_T", "TEXTURE_MAG_FILTER", "LINEAR", "TEXTURE_MIN_FILTER", "beginPath", "arc", "PI", "fillRect", "moveTo", "lineTo", "deleteTexture", "<PERSON><PERSON><PERSON><PERSON>", "column", "as<PERSON><PERSON><PERSON><PERSON>", "isArray", "splice", "start", "deleteCount", "removedAsSubarray", "items", "removed", "array", "<PERSON><PERSON><PERSON><PERSON>", "getPrototypeOf", "result", "set", "subarray", "fireEvent", "DataTableCore_objectEach", "<PERSON><PERSON><PERSON>", "Data_DataTableCore", "autoId", "id", "columns", "modified", "rowCount", "versionTag", "columnName", "applyRowCount", "deleteRows", "rowIndex", "asReference", "getColumns", "columnNames", "keys", "reduce", "getRow", "map", "setColumn", "eventDetail", "setColumns", "silent", "setRow", "row", "insert", "indexRowCount", "cellValue", "addColumns", "BoostSeries_getBoostClipRect", "BoostSeries_isChartSeriesBoosting", "getOptions", "BoostSeries_composed", "BoostSeries_doc", "noop", "BoostSeries_win", "BoostSeries_addEvent", "destroyObjectProperties", "BoostSeries_error", "extend", "BoostSeries_fireEvent", "BoostSeries_isNumber", "BoostSeries_pick", "BoostSeries_pushUnique", "wrap", "defined", "allocateIfNotSeriesBoosting", "boostEnabled", "createAndAttachRenderer", "targetGroup", "group", "foSupported", "SVGForeignObjectElement", "hasClickHandler", "Boolean", "events", "click", "renderTarget", "image", "add", "attr", "href", "copy", "resize", "toDataURL", "targetFo", "targetCtx", "element", "append<PERSON><PERSON><PERSON>", "drawImage", "css", "pointerEvents", "mixedBlendMode", "opacity", "clipRect", "zIndex", "g", "box", "clippedElement", "clip", "destroyGraphics", "destroyElements", "seriesProp", "eachAsync", "arr", "finalFunc", "chunkSize", "noTimeout", "proceed", "requestAnimationFrame", "hasExtremes", "checkX", "dataLength", "dataTable", "colorAxis", "getSeriesBoosting", "forceCrop", "onSeriesDestroy", "hoverPoints", "filter", "onSeriesHide", "renderIfNotSeriesBoosting", "getPoint", "boostPoint", "PointClass", "pointClass", "isScatter", "category", "categories", "dist", "distX", "plotX", "percentage", "isInside", "isPointInside", "scatterProcessData", "force", "isDirty", "setTickInterval", "cropThreshold", "old", "getExtremesFromAll", "processedData", "processedXData", "processedYData", "xRangeNeeded", "y<PERSON>ange<PERSON><PERSON>ed", "cropped", "xDataMax", "xDataMin", "yDataMax", "yDataMin", "iEnd", "cropStart", "seriesRenderCanvas", "chartBoost", "seriesBoost", "lowData", "highData", "pointTaken", "sampling", "enableMouseTracking", "requireSorting", "compareX", "findNearestPointBy", "xDataFull", "nullYSubstitute", "nullInteraction", "tooltip", "lastClientX", "maxVal", "minI", "maxI", "isHidden", "graph", "addKDPoint", "clientX", "pushPoint", "ceil", "buildKDTree", "lineWidthFilter", "tagName", "children", "attributes", "operator", "wrapSeriesDrawPoints", "wrapSeriesGetExtremes", "apply", "arguments", "wrapSeriesProcessData", "dataToMeasure", "getFirstValidPoint", "bp", "alteredByBoost", "altered", "own", "allowDG", "directTouch", "stickyTracking", "finishedAnimating", "labelBySeries", "exitBoost", "setting", "wrapSeriesSearchPoint", "Boost_BoostSeries", "SeriesClass", "seriesTypes", "plotOptions", "seriesProto", "renderCanvas", "method", "wrapSeriesFunctions", "branch", "letItPass", "typePlotOptions", "area", "AreaSeries", "areaspline", "AreaSplineSeries", "bubble", "BubbleSeries", "ColumnSeries", "heatmap", "HeatmapSeries", "scatter", "ScatterSeries", "treemap", "TreemapSeries", "bubbleProto", "SC", "NamedColors", "defaultHTMLColorMap", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "dodgerblue", "feldspar", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "grey", "green", "greenyellow", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "<PERSON><PERSON>rey", "lightgreen", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslateblue", "lightslategray", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "violetred", "wheat", "whitesmoke", "yellow", "yellowgreen", "Boost_doc", "Boost_win", "Boost_addEvent", "Boost_error", "Boost_contexts", "hasWebGLSupport", "WebGLRenderingContext", "Boost_Boost", "AxisClass", "ColorClass", "initCanvasBoost", "names", "scaleKey", "<PERSON><PERSON><PERSON>", "lastScale", "scale", "move", "transition", "G", "Chart", "Axis", "Series", "Point", "Color"], "mappings": "CAaA,AAbA;;;;;;;;;;;;GAYG,EACF,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,KAAQ,EACnE,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,2BAA4B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,KAAQ,CAAE,GAC3G,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,2BAA2B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,KAAQ,EAE/FA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAC9E,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,iBAkuFNC,EAAOC,EAjuFD,IAsyENC,EAtyEUC,EAAuB,CAE/B,IACC,AAACV,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIM,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaf,OAAO,CAG5B,IAAIC,EAASW,CAAwB,CAACE,EAAS,CAAG,CAGjDd,QAAS,CAAC,CACX,EAMA,OAHAW,CAAmB,CAACG,EAAS,CAACb,EAAQA,EAAOD,OAAO,CAAEa,GAG/CZ,EAAOD,OAAO,AACtB,CAMCa,EAAoBI,CAAC,CAAG,AAAChB,IACxB,IAAIiB,EAASjB,GAAUA,EAAOkB,UAAU,CACvC,IAAOlB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAY,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACpB,EAASsB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACxB,EAASuB,IAC5EE,OAAOC,cAAc,CAAC1B,EAASuB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,EAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAsCxF,IAAME,EAlBhB,CACf,OACA,aACA,YACA,SACA,cACA,MACA,OACA,UACA,UACA,SACA,UACH,CAiCKC,EAAe,CAAC,EACtBD,EAAiBE,OAAO,CAAC,AAACC,IACtBF,CAAY,CAACE,EAAK,CAAG,CAAA,CACzB,GAuBA,GAAM,CAAEC,SAAAA,CAAQ,CAAE,CAAIL,IAEhB,CAAEM,SAAAA,CAAQ,CAAEC,KAAAA,CAAI,CAAEC,WAAAA,CAAU,CAAE,CAAIR,IA2ExC,SAASS,EAAsBC,CAAK,EAChC,IAAMC,EAAYD,EAAME,MAAM,CAAEC,EAAQH,EAAMG,KAAK,CAAGH,EAAMG,KAAK,EAAI,CAAC,EAAGC,EAAeJ,EAAMK,OAAO,CAACF,KAAK,EAAI,CAAC,EAAGG,EAAYT,EAAKO,EAAaG,eAAe,CAAE,IAClK,GAAIN,EAAUO,MAAM,EAAIF,EACpB,MAAO,CAAA,EAEX,GAAIL,AAAqB,IAArBA,EAAUO,MAAM,CAChB,MAAO,CAAA,EAEX,IAAIC,EAAkBL,EAAaM,UAAU,CAC7C,GAAI,AAA2B,KAAA,IAApBD,EAEP,CAAA,IAAK,IAAME,KADXF,EAAkB,CAAA,EACCT,EAAMY,KAAK,EAC1B,GAAIf,EAAKc,EAAKE,GAAG,CAAE,CAACC,KAAYjB,EAAKc,EAAKI,OAAO,CAAE,CAACD,MAChDjB,EAAKc,EAAKK,GAAG,CAAEF,KAAYjB,EAAKc,EAAKM,OAAO,CAAEH,KAAW,CACzDL,EAAkB,CAAA,EAClB,KACJ,CACJ,CAEJ,GAAI,AAAiC,KAAA,IAA1BN,EAAMe,eAAe,CAAkB,CAC9C,GAAIT,EACA,OAAON,EAAMe,eAAe,AAEhCf,CAAAA,EAAMe,eAAe,CAAG,KAAK,CACjC,CAGA,IAAIC,EAAgB,EAAGC,EAAiB,EAAGC,EAC3C,IAAK,IAAMnB,KAAUD,EAMoB,IAAjCoB,AALJA,CAAAA,EAAgBnB,EAAOG,OAAO,AAAD,EAKXiB,cAAc,EAC5BpB,AAAmB,CAAA,IAAnBA,EAAOqB,OAAO,EAME,YAAhBrB,EAAOsB,IAAI,GAGXC,AA1I6CjC,CA0I3B,CAACU,EAAOsB,IAAI,CAAC,EAC/B,EAAEL,EAEFO,AAmGZ,SAAoB,GAAGC,CAAI,EACvB,IAAIC,EAAI,CAACC,OAAOC,SAAS,CAWzB,OAVAH,EAAKlC,OAAO,CAAC,AAACsC,IACV,GAAI,MAAOA,GAEP,AAAoB,KAAA,IAAbA,EAAEvB,MAAM,EACXuB,EAAEvB,MAAM,CAAG,EAEX,OADAoB,EAAIG,EAAEvB,MAAM,CACL,CAAA,CAGnB,GACOoB,CACX,EAhHuB1B,EAAO8B,SAAS,CAAC,IAAK,CAAA,GAAOX,EAAcY,IAAI,CAE9D/B,EAAOgC,MAAM,GAAMb,CAAAA,EAAcC,cAAc,EAAIO,OAAOC,SAAS,AAAD,GAC9D,EAAEV,GAUV,OAPAjB,EAAMe,eAAe,CAAGT,GAAoB,CAAA,AAI5CU,IAAkBlB,EAAUO,MAAM,EAC9BY,IAAmBD,GACnBC,EAAiB,CAAA,EACdjB,EAAMe,eAAe,AAChC,CAKA,SAASiB,EAAgBnC,CAAK,EAK1B,SAASoC,IACDpC,EAAMG,KAAK,EACXH,EAAMG,KAAK,CAACkC,GAAG,EACftC,EAAsBC,IACtBA,EAAMG,KAAK,CAACkC,GAAG,CAACC,MAAM,CAACtC,EAE/B,CA6BAJ,EAASI,EAAO,UAxBhB,WAEIA,EAAMG,KAAK,CAAGH,EAAMG,KAAK,EAAI,CAAC,EAC9BH,EAAMG,KAAK,CAACe,eAAe,CAAG,KAAK,EACnClB,EAAMuC,OAAO,CAAG,CAAA,EAEZ,AAACvC,EAAMwC,IAAI,CAACC,IAAI,CAAC,AAAC9B,GAASA,EAAK+B,SAAS,GACzC1C,EAAMG,KAAK,CAACwC,KAAK,KAEjB3C,EAAMG,KAAK,CAACyC,MAAM,EAClB5C,EAAMG,KAAK,CAACkC,GAAG,EACftC,EAAsBC,IAEtBA,EAAMG,KAAK,CAACkC,GAAG,CAACQ,cAAc,CAAC7C,GAG/BA,EAAMG,KAAK,CAAC2C,WAAW,EACvB9C,EAAMY,KAAK,EACXZ,EAAMY,KAAK,CAACJ,MAAM,CAAG,GACrBR,EAAM+C,KAAK,EACX/C,EAAM+C,KAAK,CAACvC,MAAM,CAAG,GACrBR,EAAMG,KAAK,CAAC2C,WAAW,CAACE,SAAS,CAAChD,EAAMY,KAAK,CAAC,EAAE,CAACqC,GAAG,CAAEjD,EAAM+C,KAAK,CAAC,EAAE,CAACE,GAAG,CAEhF,GAIArD,EAASI,EAAO,OAAQoC,EAAa,CAAEc,MAAO,EAAG,GACjDtD,EAASI,EAAO,SAAUoC,GAC1B,IAAIe,EAAQ,GACRC,EAAQ,GACZxD,EAASI,EAAMqD,OAAO,CAAE,oBAAqB,AAACC,IAC1C,IAAMpD,EAASoD,EAAEC,UAAU,EAAErD,OAE7B,GADAF,EAAMG,KAAK,CAAGH,EAAMG,KAAK,EAAI,CAAC,EAC1BH,EAAMG,KAAK,CAAC2C,WAAW,EAAI5C,EAAQ,CACnC,IAAMU,EAAQZ,EAAMwD,QAAQ,CAAGtD,EAAO6C,KAAK,CAAG7C,EAAOU,KAAK,CACpDmC,EAAQ/C,EAAMwD,QAAQ,CAAGtD,EAAOU,KAAK,CAAGV,EAAO6C,KAAK,CACtD,CAAA,AAACnC,GAASA,EAAMqC,GAAG,GAAKE,GACvBJ,GAASA,EAAME,GAAG,GAAKG,CAAK,IAI7BpD,EAAME,MAAM,CAACT,OAAO,CAAC,AAACgE,IAClBA,EAAEC,IAAI,EAAEC,MACZ,GAIA3D,EAAMG,KAAK,CAAC2C,WAAW,CAACE,SAAS,CAACpC,EAAMqC,GAAG,CAAEF,EAAME,GAAG,EACtDE,EAAQvC,EAAMqC,GAAG,CACjBG,EAAQL,EAAME,GAAG,CAEzB,CACJ,EACJ,CAkC6B,IAAMW,EALhB,CACfC,QAxOJ,SAAiBC,CAAU,CAAEC,CAAO,EAIhC,OAHIA,GAAWjE,EAAWH,EAAU,gBAChCmE,EAAW9E,SAAS,CAACgF,SAAS,CAACC,IAAI,CAAC9B,GAEjC2B,CACX,EAoOII,iBA3NJ,SAA0BlE,CAAK,CAAEmE,CAAM,EACnC,IAAMC,EAAYpE,EAAMoE,SAAS,CAC7BC,EAAU,CACVC,EAAGtE,EAAMuE,QAAQ,CACjBC,EAAGxE,EAAMyE,OAAO,CAChBC,MAAO1E,EAAM2E,SAAS,CACtBC,OAAQ5E,EAAM6E,UAAU,AAC5B,EAWA,GAVIT,GAAapE,EAAMwD,QAAQ,EAC3Ba,EAAQK,KAAK,EAAIN,EAAUU,GAAG,CAAGV,EAAUQ,MAAM,CAC7C,AAACR,EAAUW,QAAQ,EACnBV,CAAAA,EAAQC,CAAC,CAAGF,EAAUY,IAAI,AAAD,GAGxBZ,GAAa,CAACpE,EAAMwD,QAAQ,EACjCa,CAAAA,EAAQO,MAAM,CAAGR,EAAUU,GAAG,CAAGV,EAAUQ,MAAM,CAAG5E,EAAMyE,OAAO,AAAD,EAGhEN,EAAOc,EAAE,CAAE,CACX,GAAM,CAAErE,MAAAA,CAAK,CAAEmC,MAAAA,CAAK,CAAE,CAAGoB,EAEzB,GADAE,EAAUrE,EAAMkF,UAAU,CAACf,GACvBnE,EAAMwD,QAAQ,CAAE,CAChB,IAAM2B,EAAUd,EAAQK,KAAK,AAC7BL,CAAAA,EAAQK,KAAK,CAAGL,EAAQO,MAAM,CAC9BP,EAAQO,MAAM,CAAGO,EACjBd,EAAQC,CAAC,CAAGvB,EAAME,GAAG,CACrBoB,EAAQG,CAAC,CAAG5D,EAAMqC,GAAG,AACzB,MAEIoB,EAAQC,CAAC,CAAG1D,EAAMqC,GAAG,CACrBoB,EAAQG,CAAC,CAAGzB,EAAME,GAAG,AAE7B,CACA,GAAIkB,IAAWnE,EAAO,CAClB,IAAMoF,EAAepF,EAAMwD,QAAQ,CAAGxD,EAAMY,KAAK,CAAGZ,EAAM+C,KAAK,AAC3DqC,CAAAA,EAAa5E,MAAM,EAAI,IACvB6D,EAAQG,CAAC,CAAGa,KAAKxE,GAAG,CAACuE,CAAY,CAAC,EAAE,CAACnC,GAAG,CAAEoB,EAAQG,CAAC,EACnDH,EAAQO,MAAM,CAAIQ,CAAY,CAAC,EAAE,CAACnC,GAAG,CACjCjD,EAAMyE,OAAO,CACbW,CAAY,CAAC,EAAE,CAACE,GAAG,CAE/B,CACA,OAAOjB,CACX,EAiLItE,sBAAAA,CACJ,EAIA,IAAIwF,EAA+FzH,EAAoB,KACnH0H,EAAmH1H,EAAoBI,CAAC,CAACqH,GAqChH,IAAME,EAlBf,CAChB,KAAQ,QACR,UAAa,QACb,WAAc,QACd,OAAU,QACV,YAAe,QACf,IAAO,QACP,KAAQ,aACR,QAAW,SACX,QAAW,YACX,QAAW,YACX,OAAU,QACd,EAsBM,CAAEC,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAE9F,KAAM+F,CAAc,CAAE,CAAItG,IAgcbuG,EAjQnC,MAMIC,YAAYC,CAAE,CAAE,CAKZ,GAHA,IAAI,CAACC,MAAM,CAAG,EAAE,CAChB,IAAI,CAACC,UAAU,CAAG,CAAC,EACnB,IAAI,CAACF,EAAE,CAAGA,EACNA,GAAM,CAAC,IAAI,CAACG,YAAY,GACxB,MAER,CAYAC,MAAO,CACC,IAAI,CAACJ,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAACM,UAAU,CAAC,IAAI,CAACD,aAAa,CAE7C,CAMAF,cAAe,CACX,IAAMI,EAAI,IAAI,CAACC,eAAe,CA/LjB,ygHA+LgC,UAAWC,EAAI,IAAI,CAACD,eAAe,CA9NjE,+bA8NkF,YAAaE,EAAO,AAACvI,GAAO,IAAI,CAAC6H,EAAE,CAACW,kBAAkB,CAAC,IAAI,CAACN,aAAa,CAAElI,UAC5K,AAAI,AAACoI,GAAME,GAKX,IAAI,CAACJ,aAAa,CAAG,IAAI,CAACL,EAAE,CAACY,aAAa,GAC1C,IAAI,CAACZ,EAAE,CAACa,YAAY,CAAC,IAAI,CAACR,aAAa,CAAEE,GACzC,IAAI,CAACP,EAAE,CAACa,YAAY,CAAC,IAAI,CAACR,aAAa,CAAEI,GACzC,IAAI,CAACT,EAAE,CAACc,WAAW,CAAC,IAAI,CAACT,aAAa,EACjC,IAAI,CAACL,EAAE,CAACe,mBAAmB,CAAC,IAAI,CAACV,aAAa,CAAE,IAAI,CAACL,EAAE,CAACgB,WAAW,IAMxE,IAAI,CAAChB,EAAE,CAACM,UAAU,CAAC,IAAI,CAACD,aAAa,EACrC,IAAI,CAACL,EAAE,CAACiB,kBAAkB,CAAC,IAAI,CAACZ,aAAa,CAAE,EAAG,mBAClD,IAAI,CAACa,QAAQ,CAAGR,EAAK,YACrB,IAAI,CAACS,SAAS,CAAGT,EAAK,SACtB,IAAI,CAACU,SAAS,CAAGV,EAAK,aACtB,IAAI,CAACW,eAAe,CAAGX,EAAK,YAC5B,IAAI,CAACY,oBAAoB,CAAGZ,EAAK,iBACjC,IAAI,CAACa,qBAAqB,CAAGb,EAAK,oBAClC,IAAI,CAACc,eAAe,CAAGd,EAAK,YAC5B,IAAI,CAACe,sBAAsB,CAAGf,EAAK,mBACnC,IAAI,CAACgB,eAAe,CAAGhB,EAAK,YAC5B,IAAI,CAACiB,UAAU,CAAGjB,EAAK,cAChB,CAAA,IAjBH,IAAI,CAACT,MAAM,CAAC/B,IAAI,CAAC,IAAI,CAAC8B,EAAE,CAAC4B,iBAAiB,CAAC,IAAI,CAACvB,aAAa,GAC7D,IAAI,CAACwB,YAAY,GACjB,IAAI,CAACxB,aAAa,CAAG,CAAA,EACd,CAAA,IAZP,IAAI,CAACA,aAAa,CAAG,CAAA,EACrB,IAAI,CAACwB,YAAY,GACV,CAAA,EAyBf,CAKAA,cAAe,CACP,IAAI,CAAC5B,MAAM,CAACxF,MAAM,EAClBmF,EAAM,qCACF,IAAI,CAACK,MAAM,CAAC6B,IAAI,CAAC,MAE7B,CASAtB,gBAAgBuB,CAAG,CAAEtG,CAAI,CAAE,CACvB,IAAMuG,EAAS,IAAI,CAAChC,EAAE,CAACG,YAAY,CAAC1E,AAAS,WAATA,EAAoB,IAAI,CAACuE,EAAE,CAACiC,aAAa,CAAG,IAAI,CAACjC,EAAE,CAACkC,eAAe,QAGvG,CAFA,IAAI,CAAClC,EAAE,CAACmC,YAAY,CAACH,EAAQD,GAC7B,IAAI,CAAC/B,EAAE,CAACoC,aAAa,CAACJ,GACjB,IAAI,CAAChC,EAAE,CAACqC,kBAAkB,CAACL,EAAQ,IAAI,CAAChC,EAAE,CAACsC,cAAc,GAOvDN,GANH,IAAI,CAAC/B,MAAM,CAAC/B,IAAI,CAAC,kBACbzC,EACA,aACA,IAAI,CAACuE,EAAE,CAACuC,gBAAgB,CAACP,IACtB,CAAA,EAGf,CAKAQ,SAAU,CACF,IAAI,CAACxC,EAAE,EAAI,IAAI,CAACK,aAAa,GAC7B,IAAI,CAACL,EAAE,CAACyC,aAAa,CAAC,IAAI,CAACpC,aAAa,EACxC,IAAI,CAACA,aAAa,CAAG,CAAA,EAE7B,CACAqC,kBAAmB,CACf,OAAO,IAAI,CAACtB,SAAS,AACzB,CAOAuB,YAAa,CACT,OAAO,IAAI,CAACtC,aAAa,AAC7B,CACAuC,kBAAmB,CACf,OAAO,IAAI,CAACzB,SAAS,AACzB,CACA0B,oBAAqB,CACjB,OAAO,IAAI,CAAC3B,QAAQ,AACxB,CAKA4B,OAAQ,CACA,IAAI,CAAC9C,EAAE,EAAI,IAAI,CAACK,aAAa,GAC7B,IAAI,CAACL,EAAE,CAAC+C,SAAS,CAAC,IAAI,CAAC1B,eAAe,CAAE,GACxC,IAAI,CAACrB,EAAE,CAAC+C,SAAS,CAAC,IAAI,CAACrB,eAAe,CAAE,GAEhD,CAOAsB,kBAAkB7I,CAAM,CAAE8I,CAAQ,CAAEC,CAAQ,CAAEC,EAAa,CAAC,CAAE,CAC1D,IAAM7H,EAAgBnB,EAAOG,OAAO,CAChC8I,EAAOtH,OAAOC,SAAS,CAAEsH,EAAO,CAACvH,OAAOC,SAAS,CACrD,GAAI,IAAI,CAACiE,EAAE,EAAI,IAAI,CAACK,aAAa,EAAIlG,EAAO+E,EAAE,CAAC,UAAW,CACtD,IAAMoE,EAAUnJ,EAAOoJ,aAAa,GACpCH,EAAOvD,EAAevE,EAAc8H,IAAI,CAAEzD,EAAMsD,EAAU3H,AAAkC,CAAA,IAAlCA,EAAckI,eAAe,CACnFlI,EAAcmI,UAAU,CAAG,CAAC3H,OAAOC,SAAS,CAAEqH,IAClDC,EAAOxD,EAAevE,EAAc+H,IAAI,CAAE/D,KAAKrE,GAAG,CAACoI,EAAMH,IACzD,IAAI,CAAClD,EAAE,CAAC+C,SAAS,CAAC,IAAI,CAAC1B,eAAe,CAAE,GACxC,IAAI,CAACrB,EAAE,CAAC+C,SAAS,CAAC,IAAI,CAACrB,eAAe,CAAE,GACxC,IAAI,CAAC1B,EAAE,CAAC+C,SAAS,CAAC,IAAI,CAACxB,qBAAqB,CAAGpH,AAA0B,UAA1BA,EAAOG,OAAO,CAACoJ,MAAM,EACpE,IAAI,CAAC1D,EAAE,CAAC+C,SAAS,CAAC,IAAI,CAACzB,oBAAoB,CAAEnH,EAAOG,OAAO,CACtDqJ,mBAAmB,EACxB,IAAI,CAACC,UAAU,CAAC,gBAAiBN,EAAQO,SAAS,CAAGV,GACrD,IAAI,CAACS,UAAU,CAAC,gBAAiBN,EAAQQ,SAAS,CAAGX,GACrD,IAAI,CAACS,UAAU,CAAC,aAAcR,GAC9B,IAAI,CAACQ,UAAU,CAAC,aAAcP,GAC9B,IAAI,CAACO,UAAU,CAAC,mBAAoBzJ,EAAOG,OAAO,CAACmJ,UAAU,CACjE,CACJ,CAOAM,SAASC,CAAK,CAAE,CACR,IAAI,CAAChE,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAACiE,SAAS,CAAC,IAAI,CAAC7C,SAAS,CAAE4C,CAAK,CAAC,EAAE,CAAG,IAAOA,CAAK,CAAC,EAAE,CAAG,IAAOA,CAAK,CAAC,EAAE,CAAG,IAAOA,CAAK,CAAC,EAAE,CAExG,CAKAE,gBAAgBC,CAAI,CAAE,CACd,IAAI,CAACnE,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAAC+C,SAAS,CAAC,IAAI,CAACrB,eAAe,CAAEyC,GAAAA,EAEhD,CAOAC,YAAYD,CAAI,CAAE,CACV,IAAI,CAACnE,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAAC+C,SAAS,CAAC,IAAI,CAACpB,UAAU,CAAEwC,EAE3C,CAOAE,WAAWC,CAAC,CAAE,CACN,IAAI,CAACtE,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAACuE,gBAAgB,CAAC,IAAI,CAACrD,QAAQ,CAAE,CAAA,EAAOoD,EAEvD,CAOAE,aAAaC,CAAC,CAAE,CACR,IAAI,CAACzE,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAAC0E,SAAS,CAAC,IAAI,CAACvD,SAAS,CAAEsD,EAE1C,CAKAE,mBAAmBR,CAAI,CAAE,CACjB,IAAI,CAACnE,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAAC+C,SAAS,CAAC,IAAI,CAACtB,sBAAsB,CAAE0C,CAAAA,CAAAA,AAAS,CAAA,IAATA,CAAY,EAEnE,CAOAS,WAAWC,CAAO,CAAE,CACZ,IAAI,CAAC7E,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAAC+C,SAAS,CAAC,IAAI,CAACvB,eAAe,CAAEqD,EAEhD,CAUAjB,WAAWkB,CAAI,CAAEC,CAAG,CAAE,CAClB,GAAI,IAAI,CAAC/E,EAAE,EAAI,IAAI,CAACK,aAAa,CAAE,CAC/B,IAAM2E,EAAI,IAAI,CAAC9E,UAAU,CAAC4E,EAAK,CAAI,IAAI,CAAC5E,UAAU,CAAC4E,EAAK,EACpD,IAAI,CAAC9E,EAAE,CAACW,kBAAkB,CAAC,IAAI,CAACN,aAAa,CAAEyE,GACnD,IAAI,CAAC9E,EAAE,CAAC0E,SAAS,CAACM,EAAGD,EACzB,CACJ,CACJ,EAuMmCE,EA/JnC,MAMIlF,YAAYC,CAAE,CAAEgC,CAAM,CAAEkD,CAAc,CAEpC,CAME,IAAI,CAACC,MAAM,CAAG,CAAA,EACd,IAAI,CAACC,QAAQ,CAAG,EAChB,IAAI,CAACC,YAAY,CAAG,CAAA,EACpB,IAAI,CAACC,aAAa,CAAG,CAAA,EACrB,IAAI,CAACC,UAAU,CAAGL,GAAkB,EACpC,IAAI,CAACA,cAAc,CAAGA,EACtB,IAAI,CAAClF,EAAE,CAAGA,EACV,IAAI,CAACgC,MAAM,CAAGA,CAClB,CAWAwD,SAASC,CAAI,CAAE,CACX,IAAI,CAACL,QAAQ,CAAG,GAChB,IAAI,CAACC,YAAY,CAAG,IAAIK,aAAaD,AAAO,EAAPA,EACzC,CAKArF,MAAO,CACH,GAAI,CAAC,IAAI,CAAC+E,MAAM,CACZ,MAAO,CAAA,EAKX,IAAI,CAACnF,EAAE,CAAC2F,mBAAmB,CAAC,IAAI,CAACL,aAAa,CAAE,IAAI,CAACC,UAAU,CAAE,IAAI,CAACvF,EAAE,CAAC4F,KAAK,CAAE,CAAA,EAAO,EAAG,EAE9F,CAWAC,MAAMC,CAAM,CAAEC,CAAM,CAAEb,CAAc,CAAE,CAClC,IAAIc,QAEJ,CADA,IAAI,CAAC9J,IAAI,CAAG4J,GAAU,EAAE,CACpB,AAAE,IAAI,CAAC5J,IAAI,EAAI,AAAqB,IAArB,IAAI,CAACA,IAAI,CAACzB,MAAM,EAAY,IAAI,CAAC4K,YAAY,GAKhE,IAAI,CAACE,UAAU,CAAGL,GAAkB,IAAI,CAACK,UAAU,CAC/C,IAAI,CAACJ,MAAM,EACX,IAAI,CAACnF,EAAE,CAACiG,YAAY,CAAC,IAAI,CAACd,MAAM,EAEhC,AAAC,IAAI,CAACE,YAAY,EAClBW,CAAAA,EAAS,IAAIN,aAAa,IAAI,CAACxJ,IAAI,CAAA,EAEvC,IAAI,CAACiJ,MAAM,CAAG,IAAI,CAACnF,EAAE,CAACkG,YAAY,GAClC,IAAI,CAAClG,EAAE,CAACmG,UAAU,CAAC,IAAI,CAACnG,EAAE,CAACoG,YAAY,CAAE,IAAI,CAACjB,MAAM,EACpD,IAAI,CAACnF,EAAE,CAACqG,UAAU,CAAC,IAAI,CAACrG,EAAE,CAACoG,YAAY,CAAE,IAAI,CAACf,YAAY,EAAIW,EAAQ,IAAI,CAAChG,EAAE,CAACsG,WAAW,EAEzF,IAAI,CAAChB,aAAa,CAAG,IAAI,CAACtF,EAAE,CACvBuG,iBAAiB,CAAC,IAAI,CAACvE,MAAM,CAACW,UAAU,GAAIoD,GACjD,IAAI,CAAC/F,EAAE,CAACwG,uBAAuB,CAAC,IAAI,CAAClB,aAAa,EAElDU,EAAS,CAAA,EACF,CAAA,IAnBH,IAAI,CAACxD,OAAO,GACL,CAAA,EAmBf,CAIAA,SAAU,CACF,IAAI,CAAC2C,MAAM,GACX,IAAI,CAACnF,EAAE,CAACiG,YAAY,CAAC,IAAI,CAACd,MAAM,EAChC,IAAI,CAACA,MAAM,CAAG,CAAA,EACd,IAAI,CAACG,aAAa,CAAG,CAAA,GAEzB,IAAI,CAACF,QAAQ,CAAG,EAChB,IAAI,CAACG,UAAU,CAAG,IAAI,CAACL,cAAc,EAAI,EACzC,IAAI,CAAChJ,IAAI,CAAG,EAAE,AAClB,CAaAgC,KAAKK,CAAC,CAAEE,CAAC,CAAElG,CAAC,CAAEkO,CAAC,CAAE,CACT,IAAI,CAACpB,YAAY,GACjB,IAAI,CAACA,YAAY,CAAC,EAAE,IAAI,CAACD,QAAQ,CAAC,CAAG7G,EACrC,IAAI,CAAC8G,YAAY,CAAC,EAAE,IAAI,CAACD,QAAQ,CAAC,CAAG3G,EACrC,IAAI,CAAC4G,YAAY,CAAC,EAAE,IAAI,CAACD,QAAQ,CAAC,CAAG7M,EACrC,IAAI,CAAC8M,YAAY,CAAC,EAAE,IAAI,CAACD,QAAQ,CAAC,CAAGqB,EAE7C,CAYAlK,OAAOmK,CAAI,CAAEC,CAAE,CAAEC,CAAQ,CAAE,CACvB,IAAMnM,EAAS,IAAI,CAAC4K,YAAY,CAC5B,IAAI,CAACA,YAAY,CAAC5K,MAAM,CAAG,IAAI,CAACyB,IAAI,CAACzB,MAAM,OAC/C,CAAI,CAAC,IAAI,CAAC0K,MAAM,GAGZ,CAAC1K,IAGD,CAAA,CAACiM,GAAQA,EAAOjM,GAAUiM,EAAO,CAAA,GACjCA,CAAAA,EAAO,CAAA,EAEP,CAAA,CAACC,GAAMA,EAAKlM,CAAK,GACjBkM,CAAAA,EAAKlM,CAAK,GAEViM,CAAAA,GAAQC,CAAC,IAGbC,EAAWA,GAAY,SACvB,IAAI,CAAC5G,EAAE,CAAC6G,UAAU,CAAC,IAAI,CAAC7G,EAAE,CAAC4G,EAAS,CAAEF,EAAO,IAAI,CAACnB,UAAU,CAAE,AAACoB,CAAAA,EAAKD,CAAG,EAAK,IAAI,CAACnB,UAAU,EACpF,CAAA,GACX,CACJ,EAsBM,CAAEuB,MAAO9C,CAAK,CAAE,CAAIvE,IAEpB,CAAEsH,IAAAA,CAAG,CAAEC,IAAAA,CAAG,CAAE,CAAIzN,IAEhB,CAAE0N,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,WAAAA,CAAU,CAAEtN,KAAMuN,CAAgB,CAAE,CAAI9N,IAUrE+N,EAAQ,CACV,OAAU,CAAA,EACV,YAAe,CAAA,EACf,IAAO,CAAA,EACP,KAAQ,CAAA,EACR,WAAc,CAAA,EACd,UAAa,CAAA,CACjB,EACMC,EAAW,CACb,QAAW,CAAA,EACX,OAAU,CAAA,CACd,EACMC,EAAW,CACb,QACA,qBACA,YACA,YACH,AAmBD,OAAMC,EAcF,OAAOC,YAAY/I,CAAK,CAAEE,CAAM,CAAE,CAE9B,MAAO,CACH,EAAIF,EAAO,EAAG,EAAG,EACjB,EAAG,CAAE,CAAA,EAAIE,CAAK,EAAI,EAAG,EACrB,EAAG,EAAG,GAAmB,EACzB,GAAI,EAAG,GAA8B,EACxC,AACL,CAIA,OAAO8I,iBAAiBxN,CAAM,CAAE,CAC5B,IAAIyN,EAAWC,EAAOnK,SACtB,AAAIvD,EAAOqC,OAAO,EACdoL,EAAY,CAAC,CAACzN,EAAOG,OAAO,CAACwN,QAAQ,CACrCD,EAAS,AAAC1N,CAAAA,EAAO8B,SAAS,CAAC,KAAKxB,MAAM,CAClCN,EAAO8B,SAAS,CAAC,KACjB,KAAK,CAAA,GACL9B,EAAOG,OAAO,CAACuN,KAAK,EACpB1N,EAAO8B,SAAS,CAAC,IAAK,CAAA,GAC1ByB,EAAI,AAACkK,CAAAA,EAAYzN,EAAO+B,IAAI,CAAI2L,GAAS1N,EAAOG,OAAO,CAAC4B,IAAI,EACvDzB,MAAM,CACPN,AAAgB,YAAhBA,EAAOsB,IAAI,CACXiC,GAAK,GAEAvD,AAAgB,YAAhBA,EAAOsB,IAAI,CAChBiC,GAAK,EAEA4J,CAAK,CAACnN,EAAOsB,IAAI,CAAC,EACvBiC,CAAAA,GAAK,CAAA,EAEFA,GAEJ,CACX,CAMAqC,YAAYgI,CAAkB,CAAE,CAK5B,IAAI,CAAC7L,IAAI,CAAG,EAAE,CAEd,IAAI,CAAC2C,MAAM,CAAG,EAEd,IAAI,CAACmJ,QAAQ,CAAG,CAAA,EAEhB,IAAI,CAACC,UAAU,CAAG,EAAE,CAEpB,IAAI,CAAC9N,MAAM,CAAG,EAAE,CAEhB,IAAI,CAAC+N,cAAc,CAAG,CAAC,EAEvB,IAAI,CAACvJ,KAAK,CAAG,EACb,IAAI,CAACoJ,kBAAkB,CAAGA,EAC1B,IAAI,CAACI,QAAQ,CAAG,CACZC,UAAW,EACXC,UAAW,EACXC,UAAW,UACXC,SAAU,CAAA,EACVC,gBAAiB,CAAA,EACjBC,mBAAoB,CAAA,EACpBC,MAAO,CACHC,cAAe,CAAA,EACfC,qBAAsB,CAAA,EACtBC,UAAW,CAAA,EACXC,eAAgB,CAAA,EAChBC,WAAY,CAAA,EACZC,gBAAiB,CAAA,CACrB,CACJ,CACJ,CASAC,eAAgB,CACZ,OAAO,IAAI,CAACd,QAAQ,CAAChF,UAAU,EAAI6D,EAAIkC,gBAAgB,EAAI,CAC/D,CAIAC,WAAW7O,CAAO,CAAE,CAIZ,AAAE,eAAgBA,GAClBA,CAAAA,EAAQ6I,UAAU,CAAG,CAAA,EAEzBgE,EAAM,CAAA,EAAM,IAAI,CAACgB,QAAQ,CAAE7N,EAC/B,CAKAwC,eAAe7C,CAAK,CAAE,CAClB,IAAMmP,EAAU,IAAI,CAACA,OAAO,CACxB1L,EAAI,CACH,CAAA,IAAI,CAACyK,QAAQ,CAACK,eAAe,GAGlCvO,EAAME,MAAM,CAACT,OAAO,CAAC,AAACS,IACdA,EAAOqC,OAAO,EACdkB,CAAAA,GAAK+J,EAAYE,gBAAgB,CAACxN,EAAM,CAEhD,GACAiP,GAAWA,EAAQ5D,QAAQ,CAAC9H,GAChC,CAIA2L,8BAA8BlP,CAAM,CAAE,CAClC,IAAMiP,EAAU,IAAI,CAACA,OAAO,CACxB1L,EAAI,CACH,CAAA,IAAI,CAACyK,QAAQ,CAACK,eAAe,GAG9BrO,EAAOqC,OAAO,EACdkB,CAAAA,EAAI+J,EAAYE,gBAAgB,CAACxN,EAAM,EAE3CiP,GAAWA,EAAQ5D,QAAQ,CAAC9H,GAChC,CAKAd,OAAQ,CACJ,IAAMoD,EAAK,IAAI,CAACA,EAAE,AAClBA,CAAAA,GAAMA,EAAGpD,KAAK,CAACoD,EAAGsJ,gBAAgB,CAAGtJ,EAAGuJ,gBAAgB,CAC5D,CAOAC,eAAerP,CAAM,CAAEsP,CAAI,CAAE,CACzB,IAAMvN,EAAO,IAAI,CAACA,IAAI,CAAEiM,EAAW,IAAI,CAACA,QAAQ,CAAEiB,EAAU,IAAI,CAACA,OAAO,CAAEM,EAAWvP,EAAOwP,aAAa,EACrGxP,AAAmC,aAAnCA,EAAOwP,aAAa,CAAC7H,IAAI,CAAC,KAAsB,CAAE7H,MAAAA,CAAK,CAAEK,QAAAA,CAAO,CAAEsP,OAAAA,CAAM,CAAE/O,MAAAA,CAAK,CAAEmC,MAAAA,CAAK,CAAE,CAAG7C,EAAQyN,EAAY,CAAC,CAACtN,EAAQwN,QAAQ,CAAE+B,EAAUvP,EAAQ4B,IAAI,CAAE4N,EAAY3P,EAAOU,KAAK,CAACkP,WAAW,GAEnMC,EAAOF,EAAUhP,GAAG,CAAIX,CAAAA,EAAOU,KAAK,CAACoP,cAAc,EAAI,CAAA,EAAIC,EAAOJ,EAAU7O,GAAG,CAAId,CAAAA,EAAOU,KAAK,CAACoP,cAAc,EAAI,CAAA,EAAIE,EAAYhQ,EAAO6C,KAAK,CAAC+M,WAAW,GAAIK,EAAOD,EAAUrP,GAAG,CAAIX,CAAAA,EAAO6C,KAAK,CAACiN,cAAc,EAAI,CAAA,EAAII,EAAOF,EAAUlP,GAAG,CAAId,CAAAA,EAAO6C,KAAK,CAACiN,cAAc,EAAI,CAAA,EAAIpC,EAAQ,AAAC1N,CAAAA,EAAO8B,SAAS,CAAC,KAAKxB,MAAM,CAAGN,EAAO8B,SAAS,CAAC,KAAO,KAAK,CAAA,GAAM3B,EAAQuN,KAAK,EAAI1N,EAAO8B,SAAS,CAAC,IAAK,CAAA,GAAOqO,EAAQ,AAACnQ,CAAAA,EAAO8B,SAAS,CAAC,KAAKxB,MAAM,CAAGN,EAAO8B,SAAS,CAAC,KAAO,KAAK,CAAA,GAAM3B,EAAQgQ,KAAK,EAAInQ,EAAO8B,SAAS,CAAC,IAAK,CAAA,GAAOsO,EAAQ,AAACpQ,CAAAA,EAAO8B,SAAS,CAAC,KAAKxB,MAAM,CAAGN,EAAO8B,SAAS,CAAC,KAAO,KAAK,CAAA,GAAM3B,EAAQiQ,KAAK,EAAIpQ,EAAO8B,SAAS,CAAC,IAAK,CAAA,GAAOuO,EAAS,CAAC3C,GAASA,AAAiB,IAAjBA,EAAMpN,MAAM,CAU9pBgQ,EAAenQ,EAAQmQ,YAAY,CAInCtO,EAAShC,EAAOgC,MAAM,EAAI,CAAA,EAAOuO,EAAQ9C,EAAYzN,EAAO+B,IAAI,CAAI2L,GAASgC,EAAUc,EAAc,CAAEpM,EAAGzC,OAAOC,SAAS,CAAE0C,EAAG,CAAE,EAAGmM,EAAe,CAAErM,EAAG,CAACzC,OAAOC,SAAS,CAAE0C,EAAG,CAAE,EAA2CoM,EAAiB,AAAuB,KAAA,IAAhB5Q,EAAMvC,KAAK,CAAkBoT,EAAYxD,CAAK,CAACnN,EAAOsB,IAAI,CAAC,CAAEsP,EAAWzQ,EAAQyQ,QAAQ,EAAI,IAAKC,EAAQ1Q,EAAQ0Q,KAAK,EAAI,CAAA,EAAOzQ,EAAYD,EAAQC,SAAS,CAAE4I,EAAa,IAAI,CAAC8F,aAAa,GAC9arK,EAAYzE,EAAOF,KAAK,CAAC2E,SAAS,CAAEqM,EAAQ,CAAA,EAAOC,EAAQ,CAAA,EAAOC,EAAQC,EAE9EC,EAAU,EAAGC,EAAY,CAAA,EAEzB/M,EAAGE,EAAGnG,EAAGiT,EAAGC,EAAI,GAAIC,EAAK,CAAA,EAAOC,EAAK,CAAA,EAAOC,EAAKC,EAAa,CAAA,EAAOC,EAAa,CAAA,EAAOC,GAAS,CAAA,EAAOC,GAAY,CAAA,EAAOC,GAAY,CAAA,EAAMC,GAAa,CAAA,EAAMC,GAAYC,GAAe,CAAA,EAAOC,GAAU,CAAA,EAAOC,GAAO,EAC3N,GAAI/R,EAAQgS,SAAS,EAAIhS,EAAQgS,SAAS,CAAC7R,MAAM,CAAG,EAChD,MAEAH,CAAAA,EAAQ8R,OAAO,EACfA,CAAAA,GAAU9R,AAAoB,UAApBA,EAAQiS,OAAO,CACrBjS,EAAQ8R,OAAO,CAAGjS,EAAOqS,iBAAiB,CAC1ClS,EAAQ8R,OAAO,AAAD,EAElBpB,IACAkB,GAAa,EAAE,CACflB,EAAMtR,OAAO,CAAC,CAAC+S,EAAMjB,KACjB,GAAIiB,EAAKzI,KAAK,CAAE,CACZ,IAAM0I,EAAY1I,EAAMyI,EAAKzI,KAAK,EAAE2I,IAAI,AACxCD,CAAAA,CAAS,CAAC,EAAE,EAAI,IAChBA,CAAS,CAAC,EAAE,EAAI,IAChBA,CAAS,CAAC,EAAE,EAAI,IAChBR,EAAU,CAACV,EAAE,CAAGkB,EACZ,AAACP,IAAgB,AAAsB,KAAA,IAAfM,EAAKG,KAAK,EAClCT,CAAAA,GAAeO,CAAQ,CAE/B,CACJ,GACKP,KAGDA,GAAenI,EAFM,AAAC7J,EAAO0S,YAAY,EAAI1S,EAAO0S,YAAY,GAAGC,IAAI,EACnE3S,EAAO6J,KAAK,EACkB2I,IAAI,CACtCR,EAAY,CAAC,EAAE,EAAI,IACnBA,EAAY,CAAC,EAAE,EAAI,IACnBA,EAAY,CAAC,EAAE,EAAI,MAGvBlS,EAAMwD,QAAQ,EACdmB,CAAAA,EAAYzE,EAAOF,KAAK,CAAC6E,UAAU,AAAD,EAEtC3E,EAAO4S,mBAAmB,CAAGjR,OAAOC,SAAS,CAK7C,IAAMiR,GAAY,AAAChJ,IACXA,IACAyF,EAAKwD,SAAS,CAAC/O,IAAI,CAAC8F,CAAK,CAAC,EAAE,EAC5ByF,EAAKwD,SAAS,CAAC/O,IAAI,CAAC8F,CAAK,CAAC,EAAE,EAC5ByF,EAAKwD,SAAS,CAAC/O,IAAI,CAAC8F,CAAK,CAAC,EAAE,EAC5ByF,EAAKwD,SAAS,CAAC/O,IAAI,CAAC8F,CAAK,CAAC,EAAE,EAEpC,EAKMkJ,GAAU,CAAC3O,EAAGE,EAAG0O,EAAe/E,EAAY,CAAC,CAAEpE,KACjDgJ,GAAUhJ,GAES,IAAfb,GAAqB,CAAA,CAACgF,EAASM,kBAAkB,EACjDgB,EAAK2D,eAAe,AAAD,IACnB7O,GAAK4E,EACL1E,GAAK0E,EACLiF,GAAajF,GAEbgF,EAASK,eAAe,EAAIY,GAC5BA,EAAQlL,IAAI,CAACK,EAAGE,EAAG0O,GAAAA,EAAuB/E,GAC1CiE,IAAQ,IAGRnQ,EAAKgC,IAAI,CAACK,GACVrC,EAAKgC,IAAI,CAACO,GACVvC,EAAKgC,IAAI,CAACiP,EAAgBhK,EAAa,GACvCjH,EAAKgC,IAAI,CAACkK,GAElB,EAIMiF,GAAe,KACb5D,EAAK6D,QAAQ,CAAC7S,MAAM,EACpBgP,CAAAA,EAAK6D,QAAQ,CAAC7D,EAAK6D,QAAQ,CAAC7S,MAAM,CAAG,EAAE,CAACkM,EAAE,CAAGzK,EAAKzB,MAAM,EAAI4R,EAAG,CAEvE,EAKMkB,GAAe,KAKb9D,EAAK6D,QAAQ,CAAC7S,MAAM,EACpBgP,EAAK6D,QAAQ,CAAC7D,EAAK6D,QAAQ,CAAC7S,MAAM,CAAG,EAAE,CAACiM,IAAI,GAAMxK,CAAAA,EAAKzB,MAAM,EAAI4R,EAAG,IAGxEgB,KACA5D,EAAK6D,QAAQ,CAACpP,IAAI,CAAC,CACfwI,KAAMxK,EAAKzB,MAAM,EAAI4R,EACzB,GACJ,EAKMmB,GAAW,CAACjP,EAAGE,EAAGgP,EAAGC,EAAG1J,KAC1BgJ,GAAUhJ,GACVkJ,GAAQ3O,EAAIkP,EAAGhP,GACfuO,GAAUhJ,GACVkJ,GAAQ3O,EAAGE,GACXuO,GAAUhJ,GACVkJ,GAAQ3O,EAAGE,EAAIiP,GACfV,GAAUhJ,GACVkJ,GAAQ3O,EAAGE,EAAIiP,GACfV,GAAUhJ,GACVkJ,GAAQ3O,EAAIkP,EAAGhP,EAAIiP,GACnBV,GAAUhJ,GACVkJ,GAAQ3O,EAAIkP,EAAGhP,EACnB,EAIA,GAFA8O,KAEIpR,GAAUA,EAAO1B,MAAM,CAAG,EAAG,CAG7BgP,EAAK2D,eAAe,CAAG,CAAA,EAEvB3D,EAAK7C,QAAQ,CAAG,YAEZzK,CAAM,CAAC,EAAE,CAACwR,IAAI,EAAIxR,CAAM,CAAC,EAAE,CAACwR,IAAI,CAACC,YAAY,EAC7CzR,EAAO0R,IAAI,CAAC,CAACtV,EAAGkO,KACZ,GAAIlO,EAAEoV,IAAI,CAAE,CACR,GAAIpV,EAAEoV,IAAI,CAACC,YAAY,CACnBnH,EAAEkH,IAAI,CAACC,YAAY,CACnB,OAAO,EAEX,GAAIrV,EAAEoV,IAAI,CAACC,YAAY,CACnBnH,EAAEkH,IAAI,CAACC,YAAY,CACnB,OAAO,EAEf,CACA,OAAO,CACX,GAEJzR,EAAOzC,OAAO,CAAC,AAACoU,IACZ,IACIC,EAAQC,EADNC,EAAQH,EAAMG,KAAK,CAEzB,GAAI,AAAiB,KAAA,IAAVA,GACP,CAACC,MAAMD,IACPH,AAAY,OAAZA,EAAMrP,CAAC,EACPqP,EAAMK,SAAS,CAAE,CACjB,GAAI,CAAE5P,EAAAA,EAAI,CAAC,CAAEE,EAAAA,EAAI,CAAC,CAAEE,MAAAA,EAAQ,CAAC,CAAEE,OAAAA,EAAS,CAAC,CAAE,CAAGiP,EAAMK,SAAS,CAK7DJ,EAASC,AAJTA,CAAAA,EAAY/T,EAAMmU,UAAU,CACxBN,EAAM3T,MAAM,CACPkU,YAAY,CAACP,GAClBE,EAAYF,EAAM3T,MAAM,CAAC0S,YAAY,CAACiB,EAAK,CAC7B,CAAC,eAAe,EAAI,EAEtChC,GAAS9H,EAAMgK,EAAUlB,IAAI,EAAEH,IAAI,CACnCb,EAAM,CAAC,EAAE,EAAI,IACbA,EAAM,CAAC,EAAE,EAAI,IACbA,EAAM,CAAC,EAAE,EAAI,IAQT3R,EAAO+E,EAAE,CAAC,aACV6O,EAASA,GAAU,EACnB3C,EAASpH,EAAMgK,EAAUM,MAAM,EAAE3B,IAAI,CACrCvB,CAAM,CAAC,EAAE,EAAI,IACbA,CAAM,CAAC,EAAE,EAAI,IACbA,CAAM,CAAC,EAAE,EAAI,IACboC,GAASjP,EAAGE,EAAGE,EAAOE,EAAQuM,GAC9B2C,GAAU,GAUV5T,EAAO+E,EAAE,CAAC,YAAcjF,EAAMwD,QAAQ,GACtCc,EAAI1D,EAAM0E,GAAG,CAAGhB,EAChBE,EAAIzB,EAAMuC,GAAG,CAAGd,EAChBE,EAAQ,CAACA,EACTE,EAAS,CAACA,GAEd2O,GAASjP,EAAIwP,EAAQtP,EAAIsP,EAAQpP,EAASoP,AAAS,EAATA,EAAalP,EAAUkP,AAAS,EAATA,EAAajC,GAClF,CACJ,GACAuB,KACA,MACJ,CAOA,KAAO7B,EAAId,EAAMjQ,MAAM,CAAG,GAAG,CAEzB,GAAI,AAAa,KAAA,IADjBnC,CAAAA,EAAIoS,CAAK,CAAC,EAAEc,EAAE,AAAD,EAET,SAMJ,GAAIX,EACA,MAeJ,IAAM0D,EAAe1E,GAAWA,CAAO,CAAC2B,EAAE,CA+C1C,GA9CI,CAAChB,GAAUtD,EAASqH,EAAc,CAAA,IAC9BA,EAAavK,KAAK,GAClB8H,GAAS9H,EAAMuK,EAAavK,KAAK,EAAE2I,IAAI,CACvCb,EAAM,CAAC,EAAE,EAAI,IACbA,EAAM,CAAC,EAAE,EAAI,IACbA,EAAM,CAAC,EAAE,EAAI,KAGjBtB,GACAjM,EAAIjG,CAAC,CAAC,EAAE,CACRmG,EAAInG,CAAC,CAAC,EAAE,CACJoS,CAAK,CAACc,EAAI,EAAE,EACZE,CAAAA,EAAKhB,CAAK,CAACc,EAAI,EAAE,CAAC,EAAE,AAAD,EAEnBd,CAAK,CAACc,EAAI,EAAE,EACZC,CAAAA,EAAKf,CAAK,CAACc,EAAI,EAAE,CAAC,EAAE,AAAD,EAEnBlT,EAAEmC,MAAM,EAAI,IACZ8Q,EAAIjT,CAAC,CAAC,EAAE,CACJA,CAAC,CAAC,EAAE,CAAGmR,EAAKpG,IAAI,EAChBoG,CAAAA,EAAKpG,IAAI,CAAG/K,CAAC,CAAC,EAAE,AAAD,EAEfA,CAAC,CAAC,EAAE,CAAGmR,EAAKrG,IAAI,EAChBqG,CAAAA,EAAKrG,IAAI,CAAG9K,CAAC,CAAC,EAAE,AAAD,KAKvBiG,EAAIjG,EACJmG,EAAI6L,GAAO,CAACkB,EAAE,CACVd,CAAK,CAACc,EAAI,EAAE,EACZE,CAAAA,EAAKhB,CAAK,CAACc,EAAI,EAAE,AAAD,EAEhBd,CAAK,CAACc,EAAI,EAAE,EACZC,CAAAA,EAAKf,CAAK,CAACc,EAAI,EAAE,AAAD,EAEhBjB,GAASA,EAAM9P,MAAM,GACrB8Q,EAAIhB,CAAK,CAACiB,EAAE,CACRjB,CAAK,CAACiB,EAAE,CAAG/B,EAAKpG,IAAI,EACpBoG,CAAAA,EAAKpG,IAAI,CAAGkH,CAAK,CAACiB,EAAE,AAAD,EAEnBjB,CAAK,CAACiB,EAAE,CAAG/B,EAAKrG,IAAI,EACpBqG,CAAAA,EAAKrG,IAAI,CAAGmH,CAAK,CAACiB,EAAE,AAAD,IAI3B,CAACf,GAAiBlM,CAAAA,AAAM,OAANA,GAAcE,AAAM,OAANA,CAAS,EAAI,CAC7C8O,KACA,QACJ,CA0BA,GAzBI7B,GAAMA,GAAM1B,GAAQ0B,GAAMxB,GAC1B0B,CAAAA,EAAa,CAAA,CAAG,EAEhBH,GAAMA,GAAMzB,GAAQyB,GAAMvB,GAC1B2B,CAAAA,EAAa,CAAA,CAAG,EAEhBnC,GACIc,GACA/L,CAAAA,EAAInG,EAAEkW,KAAK,CAAC,EAAG,EAAC,EAEpB7C,EAAMxR,EAAO8B,SAAS,CAAC,MAAO,CAAA,IAAO,CAACuP,EAAE,CACxC/M,EAAItE,EAAO8B,SAAS,CAAC,OAAQ,CAAA,IAAO,CAACuP,EAAE,EAAI,GAEtC5D,IACLrJ,EAAIjG,EAAEiG,CAAC,CAEPoN,EAAMlN,AADNA,CAAAA,EAAInG,EAAEmW,MAAM,AAAD,EACDnW,EAAEmG,CAAC,EAEb2L,MAAAA,GAGA,MADAC,GAEA2B,CAAAA,GAAYvN,GAAK2L,GAAQ3L,GAAK4L,CAAG,EAGjC,CAACT,GAAU,CAACoC,KAGZzN,EAAI2L,GAAQU,EAAarM,CAAC,CAAG2L,IAC7BU,EAAarM,CAAC,CAAGA,EACjBqM,EAAanM,CAAC,CAAGA,GAEjBF,EAAIyL,GAAQW,EAAYpM,CAAC,CAAGyL,IAC5BW,EAAYpM,CAAC,CAAGA,EAChBoM,EAAYlM,CAAC,CAAGA,GAEhBA,AAAM,OAANA,GAAcgM,GAVd,SAgBJ,GAAIhM,AAAM,OAANA,GAAe,CAACuN,IAAatB,EAAMjQ,MAAM,CAAG,GAC5C,CAACmR,GAAc,CAACC,EAAa,CAC7B0B,KACA,QACJ,CASA,GALI3D,CAAAA,GAAY8B,CAAAA,GAAM1B,GAAQzL,GAAKyL,CAAG,GACjCyB,CAAAA,GAAMvB,GAAQ3L,GAAK2L,CAAG,GACvB,CAACN,GAAYrL,GAAKyL,GAAUzL,GAAK2L,CAAK,GACtC6B,CAAAA,GAAY,CAAA,CAAG,EAEf,AAACA,IAAcH,GAAeC,GAOlC,GAJIO,IAAW7N,EAAIkN,EAAKW,IACpBmB,KAGAvC,EAAO,CACP,IAAI0B,EACJ1B,EAAMtO,IAAI,CAAC,CACX+P,EAAMjB,KACF,IAAMkD,EAAO1D,CAAK,CAACQ,EAAI,EAAE,OACzB,AAAIT,AAAa,MAAbA,EACA,AAA0B,KAAA,IAAf0B,EAAKG,KAAK,EACjBrO,GAAKkO,EAAKG,KAAK,GACXV,EAAU,CAACV,EAAE,EACZ,CAAA,CAACkD,GAAQnQ,GAAKmQ,EAAK9B,KAAK,AAAD,GACxBF,CAAAA,EAAYR,EAAU,CAACV,EAAE,AAAD,EAErB,CAAA,GAIf,AAA0B,KAAA,IAAfiB,EAAKG,KAAK,EAAoBnO,GAAKgO,EAAKG,KAAK,GAChDV,EAAU,CAACV,EAAE,EACZ,CAAA,CAACkD,GAAQjQ,GAAKiQ,EAAK9B,KAAK,AAAD,GACxBF,CAAAA,EAAYR,EAAU,CAACV,EAAE,AAAD,EAErB,CAAA,EAGf,GACAM,GAASY,GAAaP,IAAgBL,EAC1C,CAEA,GAAI,AAAC3D,EAASM,kBAAkB,GAC5BgB,EAAK2D,eAAe,CAAG,CAAA,EACvB7O,EAAI1D,EAAM8T,QAAQ,CAACpQ,EAAG,CAAA,GACtBE,EAAIzB,EAAM2R,QAAQ,CAAClQ,EAAG,CAAA,GAQlBF,CAAAA,CAAAA,EAAIK,CAAQ,GAIR6K,AAAkB,WAAlBA,EAAK7C,QAAQ,GA6BzB,GAlBI6C,EAAKmF,UAAU,EAAI7C,IAYfd,AAAU,CAAA,IAAVA,GACA9Q,CAAAA,EAAO4S,mBAAmB,CAAGzN,KAAKxE,GAAG,CAACX,EAAO4S,mBAAmB,CAAEzN,KAAKuP,GAAG,CAACtQ,EAAI0M,GAAM,EAKzF,CAAC9C,EAASM,kBAAkB,EAC5B,CAACN,EAASK,eAAe,EACxByC,GAAS3L,AAlakL,EAkalLA,KAAKuP,GAAG,CAACtQ,EAAI0M,IACtBC,GAAS5L,AAnasM,EAmatMA,KAAKuP,GAAG,CAACpQ,EAAIyM,GAA0B,CAC7C/C,EAASO,KAAK,CAACM,eAAe,EAC9B,EAAEqC,EAEN,QACJ,CACIP,IACAK,EAASQ,GAAO,EACZA,CAAAA,AAAQ,CAAA,IAARA,GAAiB,AAAe,KAAA,IAARA,CAAkB,IAEtCR,EADA1M,EAAI,EACKA,EAGA,GAGb,AAAC,CAAA,AAACiL,GAAY9B,CAAQ,IACtB5K,EAAM8R,WAAW,EAEjB3D,CAAAA,EAAS7L,KAAKrE,GAAG,CAACV,AAAc,OAAdA,EAAqB6P,EAAO7P,EAC9C6P,EAAI,EAEJ,AAACjC,EAASM,kBAAkB,EAC5B0C,CAAAA,EAASnO,EAAM2R,QAAQ,CAACxD,EAAQ,CAAA,EAAI,EAGxC+B,GAAQ3O,EAAG4M,EAAQ,EAAG,EAAGW,KAKzBxR,EAAQyU,IAAI,EAAI,CAAC9C,IACjBiB,GAAQ3O,EAAG2M,EAAO,EAAG,EAAGY,IAE5BoB,GAAQ3O,EAAGE,EAAG,EAAGtE,AAAgB,WAAhBA,EAAOsB,IAAI,CAAiB8P,GAAK,EAAK,EAAGO,IAS1Db,EAAQ1M,EACR2M,EAAQzM,EACR6M,EAAY,CAAA,EACZW,GAAa,CAAA,GACjB,CACI9D,EAASO,KAAK,CAACM,eAAe,EAC9BgG,QAAQC,GAAG,CAAC,kBAAmB5D,GAEnC,IAAM6D,GAAsB,CAACpB,EAAOqB,KAQhC,GAPKhH,EAASM,kBAAkB,GAC5BgB,EAAK2D,eAAe,CAAG,CAAA,EACvBU,EAAMvP,CAAC,CAAG1D,EAAM8T,QAAQ,CAACb,EAAMvP,CAAC,CAAE,CAAA,GAClCuP,EAAMrP,CAAC,CAAGzB,EAAM2R,QAAQ,CAACb,EAAMrP,CAAC,CAAE,CAAA,IAIlC0Q,EAAS,CACT,IAAI,CAACjT,IAAI,CAAG,CAAC4R,EAAMvP,CAAC,CAAEuP,EAAMrP,CAAC,CAAE,EAAG,EAAE,CAAC2Q,MAAM,CAAC,IAAI,CAAClT,IAAI,EACrD,MACJ,CACAgR,GAAQY,EAAMvP,CAAC,CAAEuP,EAAMrP,CAAC,CAAE,EAAG,EACjC,CACI,EAAC6M,GACDb,AAAiB,CAAA,IAAjBA,GACAtQ,AAAoB,eAApBA,EAAOyM,QAAQ,GACX+D,EAAYpM,CAAC,CAAGzC,OAAOC,SAAS,EAEhCmT,GAAoBvE,EAAa,CAAA,GAEjCC,EAAarM,CAAC,CAAG,CAACzC,OAAOC,SAAS,EAClCmT,GAAoBtE,IAG5ByC,IACJ,CAQAgC,WAAW3R,CAAC,CAAE,CACV,IAAMuK,EAAa,IAAI,CAACA,UAAU,CAAE9N,EAAS,IAAI,CAACA,MAAM,CAAEgO,EAAW,IAAI,CAACA,QAAQ,AAC9EhO,CAAAA,EAAOM,MAAM,CAAG,GACZN,CAAM,CAACA,EAAOM,MAAM,CAAG,EAAE,CAACmU,UAAU,EACpCzU,CAAAA,CAAM,CAACA,EAAOM,MAAM,CAAG,EAAE,CAAC6U,QAAQ,CAAGrH,EAAWxN,MAAM,AAAD,EAGzD0N,EAASO,KAAK,CAACE,oBAAoB,EACnCoG,QAAQO,IAAI,CAAC,YAAc7R,EAAEjC,IAAI,CAAG,WAExC,IAAM1C,EAAM,CACRuU,SAAU,EAAE,CACZkC,WAAYvH,EAAWxN,MAAM,CAI7BwS,UAAW,EAAE,CACb9S,OAAQuD,EACR0F,KAAMtH,OAAOC,SAAS,CACtBsH,KAAM,CAACvH,OAAOC,SAAS,CACvB6S,WAAYlR,EAAAA,EAAEpD,OAAO,CAACmV,MAAM,EACxB/R,AAA6B,CAAA,IAA7BA,EAAEpD,OAAO,CAACmV,MAAM,CAACC,OAAO,CAE5BC,YAAa,CAAA,EACb/I,SAAUlH,CAAiB,CAAChC,EAAEjC,IAAI,CAAC,EAAI,YAC3C,CACIiC,CAAAA,EAAEhG,KAAK,EAAIyC,EAAOM,MAAM,CACxBN,EAAO+D,IAAI,CAACnF,GAGZoB,CAAM,CAACuD,EAAEhG,KAAK,CAAC,CAAGqB,EAGtB,IAAI,CAACyQ,cAAc,CAAC9L,EAAG3E,GACnBoP,EAASO,KAAK,CAACE,oBAAoB,EACnCoG,QAAQY,OAAO,CAAC,YAAclS,EAAEjC,IAAI,CAAG,UAE/C,CAOAoU,OAAQ,CACJ,IAAMzG,EAAU,IAAI,CAACA,OAAO,AAC5B,CAAA,IAAI,CAAClN,IAAI,CAAG,EAAE,CACd,IAAI,CAAC+L,UAAU,CAAG,EAAE,CACpB,IAAI,CAAC9N,MAAM,CAAG,EAAE,CACZiP,GACAA,EAAQ5G,OAAO,EAEvB,CAOAsN,SAASlV,CAAI,CAAE,CACX,IAAMoH,EAAS,IAAI,CAACA,MAAM,CAC1B,GAAI,CAACA,EACD,OAEJ,IAAMmB,EAAa,IAAI,CAAC8F,aAAa,GACrCjH,EAAO4B,UAAU,CAAC,aAAchJ,EAAKmV,MAAM,CAAG5M,GAC9CnB,EAAO4B,UAAU,CAAC,WAAYhJ,EAAKE,GAAG,EACtCkH,EAAO4B,UAAU,CAAC,cAAehJ,EAAKoV,eAAe,CAAG7M,GACxDnB,EAAO4B,UAAU,CAAC,kBAAmBhJ,EAAKqV,UAAU,EACpDjO,EAAO4B,UAAU,CAAC,WAAYhJ,EAAK2E,GAAG,CAAG4D,GACzCnB,EAAO4B,UAAU,CAAC,WAAYhJ,EAAKsC,GAAG,CAAGiG,GACzCnB,EAAO4B,UAAU,CAAC,gBAAkB,CAAChJ,EAAKsV,KAAK,EAC/ClO,EAAO4B,UAAU,CAAC,aAAe,CAAC,CAAChJ,EAAKkU,WAAW,EACnD9M,EAAO4B,UAAU,CAAC,gBAAkB,CAAC,CAAChJ,EAAKuV,QAAQ,CACvD,CAOAC,SAASxV,CAAI,CAAE,CACX,IAAMoH,EAAS,IAAI,CAACA,MAAM,CAC1B,GAAI,CAACA,EACD,OAEJ,IAAMmB,EAAa,IAAI,CAAC8F,aAAa,GACrCjH,EAAO4B,UAAU,CAAC,aAAchJ,EAAKmV,MAAM,CAAG5M,GAC9CnB,EAAO4B,UAAU,CAAC,WAAYhJ,EAAKE,GAAG,EACtCkH,EAAO4B,UAAU,CAAC,cAAehJ,EAAKoV,eAAe,CAAG7M,GACxDnB,EAAO4B,UAAU,CAAC,kBAAmBhJ,EAAKqV,UAAU,EACpDjO,EAAO4B,UAAU,CAAC,WAAYhJ,EAAK2E,GAAG,CAAG4D,GACzCnB,EAAO4B,UAAU,CAAC,WAAYhJ,EAAKsC,GAAG,CAAGiG,GACzCnB,EAAO4B,UAAU,CAAC,gBAAkB,CAAChJ,EAAKsV,KAAK,EAC/ClO,EAAO4B,UAAU,CAAC,aAAe,CAAC,CAAChJ,EAAKkU,WAAW,EACnD9M,EAAO4B,UAAU,CAAC,gBAAkB,CAAC,CAAChJ,EAAKuV,QAAQ,CACvD,CASAE,aAAaC,CAAG,CAAEC,CAAW,CAAE,CAC3B,IAAMvO,EAAS,IAAI,CAACA,MAAM,CACrBA,IAGLA,EAAO4B,UAAU,CAAC,eAAgB0M,GAClCtO,EAAO4B,UAAU,CAAC,sBAAuB2M,GAC7C,CAMAC,YAAYvW,CAAK,CAAE,CACf,IAAM+F,EAAK,IAAI,CAACA,EAAE,CAAEmI,EAAW,IAAI,CAACA,QAAQ,CAAEnG,EAAS,IAAI,CAACA,MAAM,CAAEoH,EAAU,IAAI,CAACA,OAAO,CACpFjG,EAAa,IAAI,CAAC8F,aAAa,GACrC,IAAIhP,EAKA,MAAO,CAAA,CAJP,CAAA,IAAI,CAAC0E,KAAK,CAAG1E,EAAMwW,UAAU,CAAGtN,EAChC,IAAI,CAACtE,MAAM,CAAG5E,EAAMyW,WAAW,CAAGvN,EAKtC,IAAMtE,EAAS,IAAI,CAACA,MAAM,CAAEF,EAAQ,IAAI,CAACA,KAAK,CAC9C,GAAI,CAACqB,GAAM,CAACgC,GAAU,CAACrD,GAAS,CAACE,EAC7B,MAAO,CAAA,CAEPsJ,CAAAA,EAASO,KAAK,CAACC,aAAa,EAC5BqG,QAAQO,IAAI,CAAC,gBAEjBvP,EAAGnD,MAAM,CAAC8B,KAAK,CAAGA,EAClBqB,EAAGnD,MAAM,CAACgC,MAAM,CAAGA,EACnBmD,EAAO5B,IAAI,GACXJ,EAAG2Q,QAAQ,CAAC,EAAG,EAAGhS,EAAOE,GACzBmD,EAAOqC,UAAU,CAACoD,EAAYC,WAAW,CAAC/I,EAAOE,IAC7CsJ,EAASE,SAAS,CAAG,GAAK,CAAC,AAAC9O,IAA+EqX,IAAI,EAC/G5Q,EAAGqI,SAAS,CAACF,EAASE,SAAS,EAE/Be,IACAA,EAAQvD,KAAK,CAAC,IAAI,CAAC3J,IAAI,CAAE,kBAAmB,GAC5CkN,EAAQhJ,IAAI,IAEhB4B,EAAOoC,WAAW,CAACnK,EAAMwD,QAAQ,EAEjC,IAAI,CAACtD,MAAM,CAACT,OAAO,CAAC,CAACgE,EAAGmT,KACpB,IAAMvW,EAAUoD,EAAEvD,MAAM,CAACG,OAAO,CAAEwW,EAAexW,EAAQmV,MAAM,CAAEpH,EAAa,AAA6B,KAAA,IAAtB/N,EAAQ+N,SAAS,CAClG/N,EAAQ+N,SAAS,CACjB,EAAI9N,EAAYD,EAAQC,SAAS,CAAEwW,EAAe9J,EAAS1M,GAAYyW,EAAUtT,EAAEvD,MAAM,CAAC6C,KAAK,CAACiU,YAAY,CAAC1W,GAA2CoV,EAActI,EAAiB/M,EAAQmV,MAAM,CAAGnV,EAAQmV,MAAM,CAACC,OAAO,CAAG,KAAMhS,EAAAA,EAAEvD,MAAM,CAACU,KAAK,CAACqW,QAAQ,EAAU,KAAMxT,EAAEvD,MAAM,CAAC4S,mBAAmB,CAC1S,EAAK,CAAA,AAACzS,CAAAA,EAAQmV,MAAM,CAChBnV,EAAQmV,MAAM,CAAC0B,MAAM,CACrB,EAAC,GAAM,EAAC,GAAKC,EAAe,IAAI,CAAClJ,cAAc,CAAC,AAAC4I,GAAgBA,EAAaO,MAAM,EACxF3T,EAAEvD,MAAM,CAACkX,MAAM,CAAC,EAAI,IAAI,CAACnJ,cAAc,CAACoJ,MAAM,CAC9CC,EAAQC,EAASlJ,EAAW8C,EAAS,EAAE,CAC3C,GAAI1N,AAAsB,IAAtBA,EAAE4P,QAAQ,CAAC7S,MAAM,EACjBiD,EAAE4P,QAAQ,CAAC,EAAE,CAAC5G,IAAI,GAAKhJ,EAAE4P,QAAQ,CAAC,EAAE,CAAC3G,EAAE,GAGvCyK,EAAaK,OAAO,GACpBzR,EAAG0R,WAAW,CAAC1R,EAAG2R,UAAU,CAAEP,EAAaQ,MAAM,EACjD5P,EAAO4C,UAAU,CAACwM,EAAaQ,MAAM,GAErC3X,EAAMmU,UAAU,CACZ1Q,EAAEvD,MAAM,CAAC4C,WAAW,GAAKW,EAAEvD,MAAM,CAACF,KAAK,CAACG,KAAK,EAAE2C,aAE/C,OAAOW,EAAEvD,MAAM,CAAC4C,WAAW,CAC3BW,EAAEvD,MAAM,CAAC4C,WAAW,CAAGW,EAAEvD,MAAM,CAAC0X,SAAS,CAAC,cAAe,UAAW,UAAW,EAAG5X,EAAM6X,WAAW,EAAEC,QAAQ,CAAC,sBAC9GzJ,EAAY5K,EAAEvD,MAAM,CAAC4C,WAAW,CAACiV,QAAQ,CAAC,QAC1CtU,EAAEvD,MAAM,CAAC4C,WAAW,CAACyF,OAAO,GAC5B9E,EAAEvD,MAAM,CAAC4C,WAAW,CAAGW,EAAEvD,MAAM,CAACF,KAAK,CAACG,KAAK,EAAE2C,aAG7CuL,EAAY5K,EAAEvD,MAAM,CAAC4C,WAAW,EAAEiV,SAAS,SAI/C1J,EACI,AAAgB,WAAf5K,EAAEkJ,QAAQ,EACPlJ,EAAEvD,MAAM,CAAC0S,YAAY,EACrBnP,EAAEvD,MAAM,CAAC0S,YAAY,GAAGC,IAAI,EAC5BpP,EAAEvD,MAAM,CAAC6J,KAAK,CAClB1J,EAAQ2X,YAAY,EACpB3J,CAAAA,EAAY5K,EAAEvD,MAAM,CAACF,KAAK,CAACK,OAAO,CAAC4X,MAAM,CAACrB,EAAG,AAAD,GAGhDnT,EAAEvD,MAAM,CAACgY,WAAW,EAAI7X,EAAQ6X,WAAW,EAC3C7J,CAAAA,EAAY,GAAK7I,CAAAA,GAAoG,EAAG6I,GAAW8J,UAAU,CAAC/K,EAAiB/M,EAAQ6X,WAAW,CAAE,IAAMrZ,GAAG,EAAC,EAElMsS,EAASpH,EAAMsE,GAAWqE,IAAI,CAC1B,AAACxE,EAASI,QAAQ,EAClB6C,CAAAA,CAAM,CAAC,EAAE,CAAG,CAAE,EAGd9Q,AAA0B,QAA1BA,EAAQ+X,aAAa,EACrBrS,EAAGsS,SAAS,CAACtS,EAAGuS,SAAS,CAAEvS,EAAGwS,GAAG,EACjCxS,EAAGyS,aAAa,CAACzS,EAAG0S,QAAQ,GAEvBpY,AAA0B,SAA1BA,EAAQ+X,aAAa,EAC1B/X,AAA0B,aAA1BA,EAAQ+X,aAAa,CACrBrS,EAAGsS,SAAS,CAACtS,EAAG2S,SAAS,CAAE3S,EAAG4S,IAAI,EAE7BtY,AAA0B,WAA1BA,EAAQ+X,aAAa,EAC1BrS,EAAGsS,SAAS,CAACtS,EAAGwS,GAAG,CAAExS,EAAGwS,GAAG,EAC3BxS,EAAGyS,aAAa,CAACzS,EAAG6S,QAAQ,GAK5B7S,EAAG8S,iBAAiB,CAAC9S,EAAGuS,SAAS,CAAEvS,EAAG+S,mBAAmB,CAAE/S,EAAGwS,GAAG,CAAExS,EAAG+S,mBAAmB,EAE7F/Q,EAAOc,KAAK,GAERpF,EAAEuP,SAAS,CAACxS,MAAM,CAAG,GACrBuH,EAAO4B,UAAU,CAAC,WAAY,GAE9B4N,AADAA,CAAAA,EAAU,IAAIvM,EAAsBjF,EAAIgC,EAAM,EACtC6D,KAAK,CAIbmN,MAAMtV,EAAE4P,QAAQ,CAAC,EAAE,CAAC5G,IAAI,EAAE0I,MAAM,CAAC1R,EAAEuP,SAAS,EAAG,SAAU,GACzDuE,EAAQpR,IAAI,KAKZ4B,EAAO4B,UAAU,CAAC,WAAY,GAG9B5D,EAAGiT,wBAAwB,CAACjT,EAAGuG,iBAAiB,CAACvE,EAAOW,UAAU,GAAI,YAG1EX,EAAO+B,QAAQ,CAACqH,GAChB,IAAI,CAAC0E,QAAQ,CAACpS,EAAEvD,MAAM,CAACU,KAAK,EAC5B,IAAI,CAACuV,QAAQ,CAAC1S,EAAEvD,MAAM,CAAC6C,KAAK,EAC5B,IAAI,CAACqT,YAAY,CAACU,EAtFiIC,GAuF/ItT,AAAe,WAAfA,EAAEkJ,QAAQ,EACV5E,EAAOwC,YAAY,CAAC6C,AAAiE,EAAjEA,EAAiB/M,EAAQmV,MAAM,EAAInV,EAAQmV,MAAM,CAAC0B,MAAM,CAAE,IAAWhO,GAI7FnB,EAAO2C,kBAAkB,CAACjH,EAAE0P,eAAe,EACvC1P,AAAkB,WAAlBA,EAAEvD,MAAM,CAACsB,IAAI,EACbuG,EAAOgB,iBAAiB,CAACtF,EAAEvD,MAAM,CAAEuD,EAAE0F,IAAI,CAAE1F,EAAE2F,IAAI,CAAEF,GAEvDnB,EAAOkC,eAAe,CAACqD,CAAQ,CAAC7J,EAAEvD,MAAM,CAACsB,IAAI,CAAC,EAAI,CAAA,GAC7C2N,IAKL,GAAIf,EAAY,GAAK3K,AAAe,eAAfA,EAAEkJ,QAAQ,CAC3B,IAAK2K,EAAS,EAAGA,EAAS7T,EAAE4P,QAAQ,CAAC7S,MAAM,CAAE8W,IACzCnI,EAAQ7M,MAAM,CAACmB,EAAE4P,QAAQ,CAACiE,EAAO,CAAC7K,IAAI,CAAEhJ,EAAE4P,QAAQ,CAACiE,EAAO,CAAC5K,EAAE,CAAEjJ,EAAEkJ,QAAQ,EAGjF,GAAIlJ,EAAEkR,UAAU,EAAIe,EAGhB,IAFA3N,EAAOwC,YAAY,CAAC6C,AAA+D,EAA/DA,EAAiB/M,EAAQmV,MAAM,EAAInV,EAAQmV,MAAM,CAAC0B,MAAM,CAAE,GAAShO,GACvFnB,EAAOkC,eAAe,CAAC,CAAA,GAClBqN,EAAS,EAAGA,EAAS7T,EAAE4P,QAAQ,CAAC7S,MAAM,CAAE8W,IACzCnI,EAAQ7M,MAAM,CAACmB,EAAE4P,QAAQ,CAACiE,EAAO,CAAC7K,IAAI,CAAEhJ,EAAE4P,QAAQ,CAACiE,EAAO,CAAC5K,EAAE,CAAE,UAG3E,GACIwB,EAASO,KAAK,CAACC,aAAa,EAC5BqG,QAAQY,OAAO,CAAC,gBAEhB,IAAI,CAAC7H,kBAAkB,EACvB,IAAI,CAACA,kBAAkB,CAAC,IAAI,EAEhC,IAAI,CAAC8H,KAAK,EACd,CAKAtT,OAAOtC,CAAK,CAAE,CAEV,GADA,IAAI,CAAC2C,KAAK,GACN3C,EAAMiZ,QAAQ,CAACC,SAAS,CACxB,OAAO,IAAI,CAAC3C,WAAW,CAACvW,EAExB,CAAA,IAAI,CAAC+N,QAAQ,CACb,IAAI,CAACwI,WAAW,CAACvW,GAGjBmZ,WAAW,KACP,IAAI,CAAC7W,MAAM,CAACtC,EAChB,EAAG,EAEX,CAMAoZ,QAAQ1U,CAAK,CAAEE,CAAM,CAAE,CACnB,IAAMmD,EAAS,IAAI,CAACA,MAAM,CAErBA,GAAW,CAAA,IAAI,CAACrD,KAAK,GAAKA,GAAS,IAAI,CAACE,MAAM,GAAKA,CAAK,IAG7D,IAAI,CAACF,KAAK,CAAGA,EACb,IAAI,CAACE,MAAM,CAAGA,EACdmD,EAAO5B,IAAI,GACX4B,EAAOqC,UAAU,CAACoD,EAAYC,WAAW,CAAC/I,EAAOE,IACrD,CAKAyU,KAAKzW,CAAM,CAAE0W,CAAO,CAAE,CAClB,IAAMpL,EAAW,IAAI,CAACA,QAAQ,CAE9B,GADA,IAAI,CAACH,QAAQ,CAAG,CAAA,EACZ,CAACnL,EACD,MAAO,CAAA,CAEPsL,CAAAA,EAASO,KAAK,CAACG,SAAS,EACxBmG,QAAQO,IAAI,CAAC,YAEjB,IAAK,IAAI/D,EAAI,EAAGA,EAAIhE,EAAS/M,MAAM,GAC/B,IAAI,CAACuF,EAAE,CAAGnD,EAAO2W,UAAU,CAAChM,CAAQ,CAACgE,EAAE,CAAE,CAEzC,IACI,IAAI,CAACxL,EAAE,EAJsB,EAAEwL,GAQvC,IAAMxL,EAAK,IAAI,CAACA,EAAE,CAClB,IAAIA,EAMA,MAAO,CAAA,CALH,CAACuT,GACD,IAAI,CAAC1D,KAAK,GAMlB7P,EAAGyT,MAAM,CAACzT,EAAG0T,KAAK,EAElB1T,EAAGsS,SAAS,CAACtS,EAAGuS,SAAS,CAAEvS,EAAG+S,mBAAmB,EACjD/S,EAAG2T,OAAO,CAAC3T,EAAG4T,UAAU,EAExB5T,EAAG6T,SAAS,CAAC7T,EAAG8T,IAAI,EACpB,IAAM9R,EAAS,IAAI,CAACA,MAAM,CAAG,IAAIlC,EAAgBE,GACjD,GAAI,CAACgC,EAED,MAAO,CAAA,CAEX,CAAA,IAAI,CAACoH,OAAO,CAAG,IAAInE,EAAsBjF,EAAIgC,GAC7C,IAAM+R,EAAgB,CAACjP,EAAMkP,KACzB,IAAMC,EAAQ,CACVxC,QAAS,CAAA,EACT5M,QAASkC,EAAImN,aAAa,CAAC,UAC3BtC,OAAQ5R,EAAG+T,aAAa,EAC5B,EAAGI,EAAMF,EAAMpP,OAAO,CAAC2O,UAAU,CAAC,KAClC,CAAA,IAAI,CAACtL,cAAc,CAACpD,EAAK,CAAGmP,EAC5BA,EAAMpP,OAAO,CAAClG,KAAK,CAAG,IACtBsV,EAAMpP,OAAO,CAAChG,MAAM,CAAG,IACvBsV,EAAIC,wBAAwB,CAAG,CAAA,EAC/BD,EAAIE,2BAA2B,CAAG,CAAA,EAClCF,EAAIG,uBAAuB,CAAG,CAAA,EAC9BH,EAAII,qBAAqB,CAAG,CAAA,EAC5BJ,EAAIK,WAAW,CAAG,yBAClBL,EAAIM,SAAS,CAAG,OAChBT,EAAGG,GACH,GAAI,CACAnU,EAAG0U,aAAa,CAAC1U,EAAG2U,QAAQ,EAC5B3U,EAAG0R,WAAW,CAAC1R,EAAG2R,UAAU,CAAEsC,EAAMrC,MAAM,EAE1C5R,EAAG4U,UAAU,CAAC5U,EAAG2R,UAAU,CAAE,EAAG3R,EAAG6U,IAAI,CAAE7U,EAAG6U,IAAI,CAAE7U,EAAG8U,aAAa,CAAEb,EAAMpP,OAAO,EACjF7E,EAAG+U,aAAa,CAAC/U,EAAG2R,UAAU,CAAE3R,EAAGgV,cAAc,CAAEhV,EAAGiV,aAAa,EACnEjV,EAAG+U,aAAa,CAAC/U,EAAG2R,UAAU,CAAE3R,EAAGkV,cAAc,CAAElV,EAAGiV,aAAa,EACnEjV,EAAG+U,aAAa,CAAC/U,EAAG2R,UAAU,CAAE3R,EAAGmV,kBAAkB,CAAEnV,EAAGoV,MAAM,EAChEpV,EAAG+U,aAAa,CAAC/U,EAAG2R,UAAU,CAAE3R,EAAGqV,kBAAkB,CAAErV,EAAGoV,MAAM,EAEhEpV,EAAG0R,WAAW,CAAC1R,EAAG2R,UAAU,CAAE,MAC9BsC,EAAMxC,OAAO,CAAG,CAAA,CACpB,CACA,MAAOlU,EAAG,CAEV,CACJ,EA4CA,OA1CAwW,EAAc,SAAU,AAACI,IACrBA,EAAImB,SAAS,GACbnB,EAAIoB,GAAG,CAAC,IAAK,IAAK,IAAK,EAAG,EAAIjW,KAAKkW,EAAE,EACrCrB,EAAI7F,MAAM,GACV6F,EAAIrH,IAAI,EACZ,GAEAiH,EAAc,SAAU,AAACI,IACrBA,EAAIsB,QAAQ,CAAC,EAAG,EAAG,IAAK,IAC5B,GAEA1B,EAAc,UAAW,AAACI,IACtBA,EAAImB,SAAS,GACbnB,EAAIuB,MAAM,CAAC,IAAK,GAChBvB,EAAIwB,MAAM,CAAC,IAAK,KAChBxB,EAAIwB,MAAM,CAAC,IAAK,KAChBxB,EAAIwB,MAAM,CAAC,EAAG,KACdxB,EAAIwB,MAAM,CAAC,IAAK,GAChBxB,EAAIrH,IAAI,EACZ,GAEAiH,EAAc,WAAY,AAACI,IACvBA,EAAImB,SAAS,GACbnB,EAAIuB,MAAM,CAAC,EAAG,KACdvB,EAAIwB,MAAM,CAAC,IAAK,GAChBxB,EAAIwB,MAAM,CAAC,IAAK,KAChBxB,EAAIwB,MAAM,CAAC,EAAG,KACdxB,EAAIrH,IAAI,EACZ,GAEAiH,EAAc,gBAAiB,AAACI,IAC5BA,EAAImB,SAAS,GACbnB,EAAIuB,MAAM,CAAC,EAAG,GACdvB,EAAIwB,MAAM,CAAC,IAAK,KAChBxB,EAAIwB,MAAM,CAAC,IAAK,GAChBxB,EAAIwB,MAAM,CAAC,EAAG,GACdxB,EAAIrH,IAAI,EACZ,GACA,IAAI,CAAC9E,QAAQ,CAAG,CAAA,EACZG,EAASO,KAAK,CAACG,SAAS,EACxBmG,QAAQY,OAAO,CAAC,YAEb,CAAA,CACX,CAKApN,SAAU,CACN,IAAMxC,EAAK,IAAI,CAACA,EAAE,CAAEgC,EAAS,IAAI,CAACA,MAAM,CAAEoH,EAAU,IAAI,CAACA,OAAO,CAChE,IAAI,CAACyG,KAAK,GACNzG,GACAA,EAAQ5G,OAAO,GAEfR,GACAA,EAAOQ,OAAO,GAEdxC,IACAoH,EAAW,IAAI,CAACc,cAAc,CAAE,AAACrD,IACzBA,EAAQ+M,MAAM,EACd5R,EAAG4V,aAAa,CAAC/Q,EAAQ+M,MAAM,CAEvC,GACA5R,EAAGnD,MAAM,CAAC8B,KAAK,CAAG,EAClBqB,EAAGnD,MAAM,CAACgC,MAAM,CAAG,EAE3B,CACJ,EA0BA,AAAC,SAAUjH,CAAW,EAqClBA,EAAYie,SAAS,CAPrB,SAAmBC,CAAM,CAAErb,CAAM,CAAEsb,CAAU,SACzC,AAAI/C,MAAMgD,OAAO,CAACF,IACdA,EAAOrb,MAAM,CAAGA,EACTqb,GAEJA,CAAM,CAACC,EAAa,WAAa,QAAQ,CAAC,EAAGtb,EACxD,EAoDA7C,EAAYqe,MAAM,CAvBlB,SAAgBH,CAAM,CAAEI,CAAK,CAAEC,CAAW,CAAEC,CAAiB,CAAEC,EAAQ,EAAE,EACrE,GAAIrD,MAAMgD,OAAO,CAACF,GAId,OAHI,AAAC9C,MAAMgD,OAAO,CAACK,IACfA,CAAAA,EAAQrD,MAAMtM,IAAI,CAAC2P,EAAK,EAErB,CACHC,QAASR,EAAOG,MAAM,CAACC,EAAOC,KAAgBE,GAC9CE,MAAOT,CACX,EAEJ,IAAMU,EAAc7d,OAAO8d,cAAc,CAACX,GACrC/V,WAAW,CACVuW,EAAUR,CAAM,CAACM,EAAoB,WAAa,QAAQ,CAACF,EAAOA,EAAQC,GAE1EO,EAAS,IAAIF,EADDV,EAAOrb,MAAM,CAAG0b,EAAcE,EAAM5b,MAAM,EAK5D,OAHAic,EAAOC,GAAG,CAACb,EAAOc,QAAQ,CAAC,EAAGV,GAAQ,GACtCQ,EAAOC,GAAG,CAACN,EAAOH,GAClBQ,EAAOC,GAAG,CAACb,EAAOc,QAAQ,CAACV,EAAQC,GAAcD,EAAQG,EAAM5b,MAAM,EAC9D,CACH6b,QAASA,EACTC,MAAOG,CACX,CACJ,CAEJ,EAAG9e,GAAgBA,CAAAA,EAAc,CAAC,CAAA,GAyBlC,GAAM,CAAEie,UAAAA,CAAS,CAAEI,OAAAA,CAAM,CAAE,CAnB4Bre,EAqBjD,CAAEif,UAAAA,CAAS,CAAEzP,WAAY0P,CAAwB,CAAEC,UAAAA,CAAS,CAAE,CAAIxd,IA+PrCyd,EA9OnC,MAiBIjX,YAAYzF,EAAU,CAAC,CAAC,CAAE,CAOtB,IAAI,CAAC2c,MAAM,CAAG,CAAC3c,EAAQ4c,EAAE,CACzB,IAAI,CAACC,OAAO,CAAG,CAAC,EAOhB,IAAI,CAACD,EAAE,CAAI5c,EAAQ4c,EAAE,EAAIH,IACzB,IAAI,CAACK,QAAQ,CAAG,IAAI,CACpB,IAAI,CAACC,QAAQ,CAAG,EAChB,IAAI,CAACC,UAAU,CAAGP,IAClB,IAAIM,EAAW,EACfP,EAAyBxc,EAAQ6c,OAAO,EAAI,CAAC,EAAG,CAACrB,EAAQyB,KACrD,IAAI,CAACJ,OAAO,CAACI,EAAW,CAAGzB,EAAOtH,KAAK,GACvC6I,EAAW/X,KAAKrE,GAAG,CAACoc,EAAUvB,EAAOrb,MAAM,CAC/C,GACA,IAAI,CAAC+c,aAAa,CAACH,EACvB,CAaAG,cAAcH,CAAQ,CAAE,CACpB,IAAI,CAACA,QAAQ,CAAGA,EAChBP,EAAyB,IAAI,CAACK,OAAO,CAAE,CAACrB,EAAQyB,KACxCzB,EAAOrb,MAAM,GAAK4c,GAClB,CAAA,IAAI,CAACF,OAAO,CAACI,EAAW,CAAG1B,EAAUC,EAAQuB,EAAQ,CAE7D,EACJ,CAeAI,WAAWC,CAAQ,CAAEL,EAAW,CAAC,CAAE,CAC/B,GAAIA,EAAW,GAAKK,EAAW,IAAI,CAACL,QAAQ,CAAE,CAC1C,IAAI5c,EAAS,EACbqc,EAAyB,IAAI,CAACK,OAAO,CAAE,CAACrB,EAAQyB,KAC5C,IAAI,CAACJ,OAAO,CAACI,EAAW,CACpBtB,EAAOH,EAAQ4B,EAAUL,GAAUd,KAAK,CAC5C9b,EAASqb,EAAOrb,MAAM,AAC1B,GACA,IAAI,CAAC4c,QAAQ,CAAG5c,CACpB,CACAoc,EAAU,IAAI,CAAE,kBAAmB,CAAEa,SAAAA,EAAUL,SAAAA,CAAS,GACxD,IAAI,CAACC,UAAU,CAAGP,GACtB,CAWA9a,UAAUsb,CAAU,CAEpBI,CAAW,CAAE,CACT,OAAO,IAAI,CAACR,OAAO,CAACI,EAAW,AACnC,CAYAK,WAAWC,CAAW,CAEtBF,CAAW,CAAE,CACT,MAAO,AAACE,CAAAA,GAAelf,OAAOmf,IAAI,CAAC,IAAI,CAACX,OAAO,CAAA,EAAGY,MAAM,CAAC,CAACZ,EAASI,KAC/DJ,CAAO,CAACI,EAAW,CAAG,IAAI,CAACJ,OAAO,CAACI,EAAW,CACvCJ,GACR,CAAC,EACR,CAaAa,OAAON,CAAQ,CAAEG,CAAW,CAAE,CAC1B,MAAO,AAACA,CAAAA,GAAelf,OAAOmf,IAAI,CAAC,IAAI,CAACX,OAAO,CAAA,EAAGc,GAAG,CAAC,AAACxf,GAAQ,IAAI,CAAC0e,OAAO,CAAC1e,EAAI,EAAE,CAACif,EAAS,CAChG,CAmBAQ,UAAUX,CAAU,CAAEzB,EAAS,EAAE,CAAE4B,EAAW,CAAC,CAAES,CAAW,CAAE,CAC1D,IAAI,CAACC,UAAU,CAAC,CAAE,CAACb,EAAW,CAAEzB,CAAO,EAAG4B,EAAUS,EACxD,CAmBAC,WAAWjB,CAAO,CAAEO,CAAQ,CAAES,CAAW,CAAE,CACvC,IAAId,EAAW,IAAI,CAACA,QAAQ,CAC5BP,EAAyBK,EAAS,CAACrB,EAAQyB,KACvC,IAAI,CAACJ,OAAO,CAACI,EAAW,CAAGzB,EAAOtH,KAAK,GACvC6I,EAAWvB,EAAOrb,MAAM,AAC5B,GACA,IAAI,CAAC+c,aAAa,CAACH,GACdc,GAAaE,SACdxB,EAAU,IAAI,CAAE,mBAChB,IAAI,CAACS,UAAU,CAAGP,IAE1B,CAoBAuB,OAAOC,CAAG,CAAEb,EAAW,IAAI,CAACL,QAAQ,CAAEmB,CAAM,CAAEL,CAAW,CAAE,CACvD,GAAM,CAAEhB,QAAAA,CAAO,CAAE,CAAG,IAAI,CAAEsB,EAAgBD,EAAS,IAAI,CAACnB,QAAQ,CAAG,EAAIK,EAAW,EAClFZ,EAAyByB,EAAK,CAACG,EAAWnB,KACtC,IAAIzB,EAASqB,CAAO,CAACI,EAAW,EAC5BY,GAAaQ,aAAe,CAAA,GAAS,AAAI3F,MAAMyF,GAC/C3C,IACI0C,EACA1C,EAASG,EAAOH,EAAQ4B,EAAU,EAAG,CAAA,EAAM,CAACgB,EAAU,EAAEnC,KAAK,CAG7DT,CAAM,CAAC4B,EAAS,CAAGgB,EAEvBvB,CAAO,CAACI,EAAW,CAAGzB,EAE9B,GACI2C,EAAgB,IAAI,CAACpB,QAAQ,EAC7B,IAAI,CAACG,aAAa,CAACiB,GAElBN,GAAaE,SACdxB,EAAU,IAAI,CAAE,gBAChB,IAAI,CAACS,UAAU,CAAGP,IAE1B,CACJ,EAyDM,CAAE5Y,iBAAkBya,CAA4B,CAAE5e,sBAAuB6e,CAAiC,CAAE,CAAGhb,EAE/G,CAAEib,WAAAA,CAAU,CAAE,CAAIvf,IAElB,CAAEK,SAAUmf,CAAoB,CAAEhS,IAAKiS,CAAe,CAAEC,KAAAA,CAAI,CAAEjS,IAAKkS,CAAe,CAAE,CAAI3f,IAExF,CAAEM,SAAUsf,CAAoB,CAAEC,wBAAAA,EAAuB,CAAExZ,MAAOyZ,EAAiB,CAAEC,OAAAA,EAAM,CAAEzC,UAAW0C,EAAqB,CAAEvD,QAAAA,EAAO,CAAE/O,SAAUuS,EAAoB,CAAE1f,KAAM2f,EAAgB,CAAE1f,WAAY2f,EAAsB,CAAEC,KAAAA,EAAI,CAAEC,QAAAA,EAAO,CAAE,CAAIrgB,IAuB7P,SAASsgB,GAA4B3G,CAAQ,CAAE/Y,CAAM,EACjD,IAAMC,EAAQD,EAAOC,KAAK,AACtB8Y,CAAAA,GACA9Y,GACAA,EAAMgE,MAAM,EACZhE,EAAMyC,MAAM,EACZ,CAACgc,EAAkC1e,EAAOF,KAAK,GAC/CiZ,EAAS7J,6BAA6B,CAAClP,EAE/C,CAUA,SAAS2f,GAAa7f,CAAK,EACvB,OAAOwf,GAAkBxf,GACrBA,EAAMK,OAAO,EACbL,EAAMK,OAAO,CAACF,KAAK,EACnBH,EAAMK,OAAO,CAACF,KAAK,CAACsV,OAAO,CAAG,CAAA,EACtC,CA0GA,SAASqK,GAAwB9f,CAAK,CAAEE,CAAM,EAC1C,IAAM4D,EAAa9D,EAAM8F,WAAW,CAAEia,EAAc/f,EAAM6X,WAAW,EAAI3X,EAAO8f,KAAK,CACjFtb,EAAQ1E,EAAMwW,UAAU,CAAE5R,EAAS5E,EAAMyW,WAAW,CAAEtS,EAASnE,EAAOigB,EAAc,AAAmC,aAAnC,OAAOC,wBAAyCC,EAAkB,CAAA,EACtJvB,EAAkC5e,GAClCmE,EAASnE,GAGTmE,EAASjE,EACTigB,EAAkBC,CAAAA,CAAQlgB,CAAAA,EAAOG,OAAO,CAACggB,MAAM,EAAEC,OAC7CpgB,EAAOG,OAAO,CAACwT,KAAK,EAAEwM,QAAQC,KAAI,GAE1C,IAAMngB,EAAQgE,EAAOhE,KAAK,CACtBgE,EAAOhE,KAAK,EACR,CAAC,EAyFT,GAnFA8f,EAAc,CAAA,EACV,AAACviB,GACDA,CAAAA,EAAaqhB,EAAgB9E,aAAa,CAAC,SAAQ,EAEnD,CAAC9Z,EAAMgE,MAAM,GACbhE,EAAMyC,MAAM,CAAGlF,EAGXsC,EAAMiZ,QAAQ,CAACC,SAAS,EAAI,CAAC+G,GAC7B9b,EAAOoc,YAAY,CAAGpgB,EAAMgE,MAAM,CAAGnE,EAAMiZ,QAAQ,CAACuH,KAAK,CAAC,GAAI,EAAG,EAAG9b,EAAOE,GACtEkT,QAAQ,CAAC,2BACT2I,GAAG,CAACV,GACT5f,EAAMwC,KAAK,CAAG,WACVxC,EAAMgE,MAAM,CAACuc,IAAI,CAAC,CAGdC,KAAM,oHACV,EACJ,EACAxgB,EAAMygB,IAAI,CAAG,WACTzgB,EAAM0gB,MAAM,GACZ1gB,EAAMgE,MAAM,CAACuc,IAAI,CAAC,CACdC,KAAMxgB,EAAMyC,MAAM,CAACke,SAAS,CAAC,YACjC,EACJ,IAGA3gB,EAAM4gB,QAAQ,CAAG/gB,EAAMiZ,QAAQ,CAC1BgB,aAAa,CAAC,iBACdwG,GAAG,CAACV,GACT5b,EAAOoc,YAAY,CAAGpgB,EAAMgE,MAAM,CAC9B4a,EAAgB9E,aAAa,CAAC,UAClC9Z,EAAM6gB,SAAS,CAAG7gB,EAAMgE,MAAM,CAACoV,UAAU,CAAC,MAC1CpZ,EAAM4gB,QAAQ,CAACE,OAAO,CAACC,WAAW,CAAC/gB,EAAMgE,MAAM,EAC/ChE,EAAMwC,KAAK,CAAG,WACVxC,EAAMgE,MAAM,CAACO,KAAK,CAAGvE,EAAMyC,MAAM,CAAC8B,KAAK,CACvCvE,EAAMgE,MAAM,CAACS,MAAM,CAAGzE,EAAMyC,MAAM,CAACgC,MAAM,AAC7C,EACAzE,EAAMygB,IAAI,CAAG,WACTzgB,EAAMgE,MAAM,CAACO,KAAK,CAAGvE,EAAMyC,MAAM,CAAC8B,KAAK,CACvCvE,EAAMgE,MAAM,CAACS,MAAM,CAAGzE,EAAMyC,MAAM,CAACgC,MAAM,CACzCzE,EAAM6gB,SAAS,CAACG,SAAS,CAAChhB,EAAMyC,MAAM,CAAE,EAAG,EAC/C,GAEJzC,EAAM0gB,MAAM,CAAG,WACXnc,EAAQ1E,EAAMwW,UAAU,CACxB5R,EAAS5E,EAAMyW,WAAW,CAC1B,AAACtW,CAAAA,EAAM4gB,QAAQ,EAAI5gB,EAAMgE,MAAM,AAAD,EACzBuc,IAAI,CAAC,CACNpc,EAAG,EACHE,EAAG,EACHE,MAAAA,EACAE,OAAAA,CACJ,GACKwc,GAAG,CAAC,CACLC,cAAelB,EAAkB,KAAK,EAAI,OAC1CmB,eAAgB,SAChBC,QA3EmF,CA4EvF,GACKzJ,QAAQ,CAACqI,EAAkB,qBAAuB,IACnDhc,aAAkBL,GAClBK,EAAOhE,KAAK,EAAE2C,aAAaE,UAAUhD,EAAMuE,QAAQ,CAAEvE,EAAMyE,OAAO,CAE1E,EACAtE,EAAMqhB,QAAQ,CAAGxhB,EAAMiZ,QAAQ,CAACuI,QAAQ,GACxC,AAACrhB,CAAAA,EAAM4gB,QAAQ,EAAI5gB,EAAMgE,MAAM,AAAD,EACzBuc,IAAI,CAAC,CAMNe,OAAQvhB,EAAOG,OAAO,CAACohB,MAAM,AACjC,GACItd,aAAkBL,GAClBK,CAAAA,EAAOhE,KAAK,CAAC2C,WAAW,CAAGqB,EAAO8U,QAAQ,CACrCyI,CAAC,GACDjB,GAAG,CAACV,GACJ/c,SAAS,CAAC9C,EAAOU,KAAK,CAACqC,GAAG,CAAE/C,EAAO6C,KAAK,CAACE,GAAG,CAAA,GAGzD9C,EAAMyC,MAAM,CAAC8B,KAAK,CAAGA,EACrBvE,EAAMyC,MAAM,CAACgC,MAAM,CAAGA,EAClBzE,EAAMqhB,QAAQ,CAAE,CAChB,IAAMG,EAAMhD,EAA6B3e,EAAOmE,GAIhDyd,EAAiB,AAACD,EAAIjd,KAAK,GAAK1E,EAAMqE,OAAO,CAACK,KAAK,EAC/Cid,EAAI/c,MAAM,GAAK5E,EAAMqE,OAAO,CAACO,MAAM,CAAImb,EACtC5f,EAAM4gB,QAAQ,EAAI5gB,EAAMgE,MAAM,CACnChE,EAAMqhB,QAAQ,CAACd,IAAI,CAACiB,GACpBC,GAAgBC,KAAK1hB,EAAMqhB,QAAQ,CACvC,CAyBA,OAxBArhB,EAAM0gB,MAAM,GACZ1gB,EAAMwC,KAAK,GACP,CAACxC,EAAMkC,GAAG,GACVlC,EAAMkC,GAAG,CAAG,IA7sBoCmL,EA6sBd,AAACnL,IAC3BA,EAAI6L,QAAQ,CAACO,KAAK,CAACI,cAAc,EACjCkG,QAAQO,IAAI,CAAC,eAEjBnV,EAAMygB,IAAI,GACNve,EAAI6L,QAAQ,CAACO,KAAK,CAACI,cAAc,EACjCkG,QAAQY,OAAO,CAAC,cAExB,GACI,AAACxV,EAAMkC,GAAG,CAACgX,IAAI,CAAClZ,EAAMyC,MAAM,GAI5Bwc,GAAkB,sDAEtBjf,EAAMkC,GAAG,CAAC6M,UAAU,CAAClP,EAAMK,OAAO,CAACF,KAAK,EAAI,CAAC,GACzCgE,aAAkBL,GAClB3D,EAAMkC,GAAG,CAACQ,cAAc,CAAC7C,IAGjCG,EAAMkC,GAAG,CAAC+W,OAAO,CAAC1U,EAAOE,GAClBzE,EAAMkC,GAAG,AACpB,CAOA,SAASyf,GAAgB5hB,CAAM,EAC3B,IAAMgC,EAAShC,EAAOgC,MAAM,CAC5B,GAAIA,EAAQ,CACR,IAAI2R,EAAOtC,EACX,IAAKA,EAAI,EAAGA,EAAIrP,EAAO1B,MAAM,CAAE+Q,GAAQ,EAE/BsC,AADJA,CAAAA,EAAQ3R,CAAM,CAACqP,EAAE,AAAD,GACHsC,EAAMkO,eAAe,EAC9BlO,EAAMkO,eAAe,EAGjC,CAOA,IAAK,IAAMvP,KANX,CAAC,QAAS,OAAQ,UAAU,CAAC/S,OAAO,CAAC,AAACV,IAClC,IAAMijB,EAAa9hB,CAAM,CAACnB,EAAK,AAC3BijB,CAAAA,GACA9hB,CAAAA,CAAM,CAACnB,EAAK,CAAGijB,EAAWzZ,OAAO,EAAC,CAE1C,GACmBrI,EAAO6Q,KAAK,EAC3BoO,GAAwB3M,EAAM,KAAK,EAAG,CAAA,EAE9C,CAmBA,SAASyP,GAAUC,CAAG,CAAEnI,CAAE,CAAEoI,CAAS,CAAEC,CAAS,CAAE7Q,CAAC,CAAE8Q,CAAS,EAG1D,IAAM/hB,EAAYiR,AAFlBA,CAAAA,EAAIA,GAAK,CAAA,EACT6Q,CAAAA,EAAYA,GA3UG,GA2UmB,EAE9BE,EAAU,CAAA,EACd,KAAOA,GAAW/Q,EAAIjR,GAAaiR,EAAI2Q,EAAI1hB,MAAM,EAC7C8hB,EAAUvI,EAAGmI,CAAG,CAAC3Q,EAAE,CAAEA,GACrB,EAAEA,EAEF+Q,IACI/Q,EAAI2Q,EAAI1hB,MAAM,CACV6hB,EACAJ,GAAUC,EAAKnI,EAAIoI,EAAWC,EAAW7Q,EAAG8Q,GAEvCpD,EAAgBsD,qBAAqB,CAE1CtD,EAAgBsD,qBAAqB,CAAC,WAClCN,GAAUC,EAAKnI,EAAIoI,EAAWC,EAAW7Q,EAC7C,GAGA4H,WAAW8I,GAAW,EAAGC,EAAKnI,EAAIoI,EAAWC,EAAW7Q,GAGvD4Q,GACLA,IAGZ,CAiFA,SAASK,GAAYtiB,CAAM,CAAEuiB,CAAM,EAC/B,IAAMpiB,EAAUH,EAAOG,OAAO,CAAEqiB,EAAaxiB,EAAOyiB,SAAS,CAACxF,QAAQ,CAACC,QAAQ,CAAExc,EAAQV,EAAOU,KAAK,EAAIV,EAAOU,KAAK,CAACP,OAAO,CAAE0C,EAAQ7C,EAAO6C,KAAK,EAAI7C,EAAO6C,KAAK,CAAC1C,OAAO,CAAEuiB,EAAY1iB,EAAO0iB,SAAS,EAAI1iB,EAAO0iB,SAAS,CAACviB,OAAO,CACrO,OAAOqiB,EAAcriB,CAAAA,EAAQiB,cAAc,EAAIO,OAAOC,SAAS,AAAD,GAE1Dyd,GAAqBxc,EAAMlC,GAAG,GAC9B0e,GAAqBxc,EAAM/B,GAAG,GAE7B,CAAA,CAACyhB,GACGlD,GAAqB3e,EAAMC,GAAG,GAAK0e,GAAqB3e,EAAMI,GAAG,CAAC,GAEtE,CAAA,CAAC4hB,GACGrD,GAAqBqD,EAAU/hB,GAAG,GAAK0e,GAAqBqD,EAAU5hB,GAAG,CAAC,CACvF,CAOA,IAAM6hB,GAAoB,CAAC3iB,EAAQ+B,IAE/B,CAAI/B,EAAO4iB,SAAS,EAGZlE,CAAAA,EAAkC1e,EAAOF,KAAK,GACjD,AAACiC,CAAAA,EAAOA,EAAKzB,MAAM,CAAG,CAAA,GAClBN,CAAAA,EAAOG,OAAO,CAACiB,cAAc,EAAIO,OAAOC,SAAS,AAAD,CAAE,EAQ/D,SAASihB,KACL,IAAM7iB,EAAS,IAAI,CAAEF,EAAQE,EAAOF,KAAK,AACrCA,CAAAA,EAAMG,KAAK,EACXH,EAAMG,KAAK,CAAC2C,WAAW,GAAK5C,EAAO4C,WAAW,EAC9C5C,CAAAA,EAAO4C,WAAW,CAAG,KAAK,CAAA,EAE1B9C,EAAMgjB,WAAW,EACjBhjB,CAAAA,EAAMgjB,WAAW,CAAGhjB,EAAMgjB,WAAW,CAACC,MAAM,CAAC,SAAUpP,CAAK,EACxD,OAAOA,EAAM3T,MAAM,GAAKA,CAC5B,EAAC,EAEDF,EAAMuD,UAAU,EAAIvD,EAAMuD,UAAU,CAACrD,MAAM,GAAKA,GAChDF,CAAAA,EAAMuD,UAAU,CAAG,KAAK,CAAA,CAEhC,CAIA,SAAS2f,KACL,IAAM/iB,EAAQ,IAAI,CAACA,KAAK,CACpBA,GAASA,EAAMyC,MAAM,EAAIzC,EAAMgE,MAAM,GACjChE,EAAMkC,GAAG,EACTlC,EAAMkC,GAAG,CAACM,KAAK,GAEfxC,EAAMwC,KAAK,EACXxC,EAAMwC,KAAK,GAGvB,CAMA,SAASwgB,GAA0BjjB,CAAM,EACrC,IAAMC,EAAQD,EAAOC,KAAK,AACtBA,CAAAA,GACAA,EAAMyC,MAAM,EACZzC,EAAMgE,MAAM,EACZhE,EAAMkC,GAAG,EACT,CAACuc,EAAkC1e,EAAOF,KAAK,GAC/CG,EAAMkC,GAAG,CAACC,MAAM,CAACpC,EAAOF,KAAK,CAErC,CAUA,SAASojB,GAASljB,CAAM,CAAEmjB,CAAU,EAChC,IAAMhiB,EAAgBnB,EAAOG,OAAO,CAAEO,EAAQV,EAAOU,KAAK,CAAE0iB,EAAapjB,EAAOqjB,UAAU,CAC1F,GAAIF,aAAsBC,EACtB,OAAOD,EAEX,IAAMG,EAAYtjB,EAAO+E,EAAE,CAAC,WAAY2I,EAAS,AAAC4V,CAAAA,GAAatjB,EAAO8B,SAAS,CAAC,IAAK,CAAA,GAAMxB,MAAM,CAC7FN,EAAO8B,SAAS,CAAC,IAAK,CAAA,GACtB,KAAK,CAAA,GACJ9B,CAAAA,EAAO8B,SAAS,CAAC,KAAKxB,MAAM,CAAGN,EAAO8B,SAAS,CAAC,KAAO,KAAK,CAAA,GAC7DX,EAAcuM,KAAK,EACnB1N,EAAO8B,SAAS,CAAC,IAAK,CAAA,IACtB,CAAA,EAAQqO,EAASnQ,EAAO8B,SAAS,CAAC,IAAK,CAAA,IACvCX,EAAcgP,KAAK,EACnB,CAAA,EAAQwD,EAAQ,IAAIyP,EAAWpjB,EAAQ,AAACsjB,GAAa5V,GAASyC,EAC9D,CAACzC,CAAK,CAACyV,EAAW9R,CAAC,CAAC,CAAElB,CAAK,CAACgT,EAAW9R,CAAC,CAAC,CAAC,CAC1C,AAACwK,CAAAA,GAAQ7b,EAAOG,OAAO,CAAC4B,IAAI,EAAI/B,EAAOG,OAAO,CAAC4B,IAAI,CAAG,EAAE,AAAD,CAAE,CAACohB,EAAW9R,CAAC,CAAC,CAAE3D,EAAQA,CAAK,CAACyV,EAAW9R,CAAC,CAAC,CAAG,KAAK,GAahH,OAZAsC,EAAM4P,QAAQ,CAAGjE,GAAiB5e,EAAM8iB,UAAU,CAC9C9iB,EAAM8iB,UAAU,CAAC7P,EAAMvP,CAAC,CAAC,CACzBuP,EAAMvP,CAAC,CACXuP,EAAMvP,CAAC,EACPuP,EAAMrV,GAAG,CAAGqV,EAAMhJ,IAAI,EAAIgJ,EAAM4P,QAAQ,CACxC5P,EAAM8P,IAAI,CAAGN,EAAWM,IAAI,CAC5B9P,EAAM+P,KAAK,CAAGP,EAAWO,KAAK,CAC9B/P,EAAMgQ,KAAK,CAAGR,EAAWQ,KAAK,CAC9BhQ,EAAMG,KAAK,CAAGqP,EAAWrP,KAAK,CAC9BH,EAAMpW,KAAK,CAAG4lB,EAAW9R,CAAC,CAC1BsC,EAAMiQ,UAAU,CAAGT,EAAWS,UAAU,CACxCjQ,EAAMkQ,QAAQ,CAAG7jB,EAAO8jB,aAAa,CAACnQ,GAC/BA,CACX,CAIA,SAASoQ,GAAmBC,CAAK,EAC7B,GAAqB,CAAE7jB,QAAAA,CAAO,CAAEO,MAAAA,CAAK,CAAEmC,MAAAA,CAAK,CAAE,CAA/B,IAAI,CAEnB,GAAI,CAAC7C,AAFU,IAAI,CAEPikB,OAAO,EACf,CAACvjB,EAAMujB,OAAO,EACd,CAACphB,EAAMohB,OAAO,EACd,CAACD,EACD,MAAO,CAAA,EAIXhkB,AAVe,IAAI,CAUZ6C,KAAK,CAACqhB,eAAe,GAC5B,IAAM9iB,EAAiBjB,EAAQiB,cAAc,EAAI,EAAG+iB,EAAgBhkB,EAAQgkB,aAAa,CAAEzW,EAAQ1N,AAXpF,IAAI,CAWuF8B,SAAS,CAAC,KAAM6N,EAAYjP,EAAMkP,WAAW,GAAIG,EAAOJ,EAAU7O,GAAG,EAAIa,OAAOC,SAAS,CAAEiO,EAAOF,EAAUhP,GAAG,EAAI,CAACgB,OAAOC,SAAS,CAAEuO,EAAQnQ,AAXzO,IAAI,CAW4O8B,SAAS,CAAC,KAAMkO,EAAYnN,EAAM+M,WAAW,GAAIM,EAAOF,EAAUlP,GAAG,EAAIa,OAAOC,SAAS,CAAEqO,EAAOD,EAAUrP,GAAG,EAAI,CAACgB,OAAOC,SAAS,CAEnY,GAAI,CAAC5B,AAbU,IAAI,CAaPqC,OAAO,EACf3B,EAAM0jB,GAAG,EACTvhB,EAAMuhB,GAAG,EACTvU,GAASnP,CAAAA,EAAM0jB,GAAG,CAACzjB,GAAG,EAAI,CAACgB,OAAOC,SAAS,AAAD,GAC1CmO,GAASrP,CAAAA,EAAM0jB,GAAG,CAACtjB,GAAG,EAAIa,OAAOC,SAAS,AAAD,GACzCqO,GAASpN,CAAAA,EAAMuhB,GAAG,CAACzjB,GAAG,EAAI,CAACgB,OAAOC,SAAS,AAAD,GAC1CsO,GAASrN,CAAAA,EAAMuhB,GAAG,CAACtjB,GAAG,EAAIa,OAAOC,SAAS,AAAD,EAKzC,OAJA5B,AApBW,IAAI,CAoBRyiB,SAAS,CAACxF,QAAQ,CAACgB,UAAU,CAAC,CACjC7Z,EAAGsJ,EACHpJ,EAAG6L,CACP,GACO,CAAA,EAGX,IAAMqS,EAAaxiB,AA3BJ,IAAI,CA2BOyiB,SAAS,CAACvF,QAAQ,CAC5C,GAAI,CAAC9b,GACDohB,EAAaphB,GACZ+iB,GACG,CAACnkB,AA/BM,IAAI,CA+BH4iB,SAAS,EACjB,CAAC5iB,AAhCM,IAAI,CAgCHqkB,kBAAkB,EAC1B,CAAClkB,EAAQkkB,kBAAkB,EAC3B7B,EAAa2B,EAKjB,OAJAnkB,AAnCW,IAAI,CAmCRyiB,SAAS,CAACxF,QAAQ,CAACgB,UAAU,CAAC,CACjC7Z,EAAGsJ,EACHpJ,EAAG6L,CACP,GACO,CAAA,EAGX,IAAMmU,EAAgB,EAAE,CAAEC,EAAiB,EAAE,CAAEC,EAAiB,EAAE,CAAEC,EAAe,CAAEpF,CAAAA,GAAqB1P,EAAU7O,GAAG,GAAKue,GAAqB1P,EAAUhP,GAAG,CAAA,EAAI+jB,EAAe,CAAErF,CAAAA,GAAqBrP,EAAUlP,GAAG,GAAKue,GAAqBrP,EAAUrP,GAAG,CAAA,EACxPgkB,EAAU,CAAA,EAAOvgB,EAAGwgB,EAAWlX,CAAK,CAAC,EAAE,CAAEmX,EAAWnX,CAAK,CAAC,EAAE,CAAEpJ,EAAGwgB,EAAW3U,GAAO,CAAC,EAAE,CAAE4U,EAAW5U,GAAO,CAAC,EAAE,CACjH,IAAK,IAAIkB,EAAI,EAAG2T,EAAOtX,EAAMpN,MAAM,CAAE+Q,EAAI2T,EAAM,EAAE3T,EAC7CjN,EAAIsJ,CAAK,CAAC2D,EAAE,CACZ/M,EAAI6L,GAAO,CAACkB,EAAE,CACVjN,GAAKyL,GAAQzL,GAAK2L,GAClBzL,GAAK2L,GAAQ3L,GAAK4L,GAClBoU,EAAcvgB,IAAI,CAAC,CAAEK,EAAAA,EAAGE,EAAAA,CAAE,GAC1BigB,EAAexgB,IAAI,CAACK,GACpBogB,EAAezgB,IAAI,CAACO,GAChBmgB,IACAG,EAAWzf,KAAKrE,GAAG,CAAC8jB,EAAUxgB,GAC9BygB,EAAW1f,KAAKxE,GAAG,CAACkkB,EAAUzgB,IAE9BsgB,IACAI,EAAW3f,KAAKrE,GAAG,CAACgkB,EAAUxgB,GAC9BygB,EAAW5f,KAAKxE,GAAG,CAACokB,EAAUzgB,KAIlCqgB,EAAU,CAAA,EA2BlB,OAxBIF,IACA/jB,EAAMK,OAAO,CAAGoE,KAAKrE,GAAG,CAAC8jB,EAAUlkB,EAAMK,OAAO,EAAI,GACpDL,EAAMG,OAAO,CAAGsE,KAAKxE,GAAG,CAACkkB,EAAUnkB,EAAMG,OAAO,EAAI,IAEpD6jB,IACA7hB,EAAM9B,OAAO,CAAGoE,KAAKrE,GAAG,CAACgkB,EAAUjiB,EAAM9B,OAAO,EAAI,GACpD8B,EAAMhC,OAAO,CAAGsE,KAAKxE,GAAG,CAACokB,EAAUliB,EAAMhC,OAAO,EAAI,IAGxDb,AA1Ee,IAAI,CA0EZ2kB,OAAO,CAAGA,EACjB3kB,AA3Ee,IAAI,CA2EZilB,SAAS,CAAG,EAEfN,GAAW3kB,AA7EA,IAAI,CA6EGyiB,SAAS,CAACxF,QAAQ,GAAKjd,AA7E9B,IAAI,CA6EiCyiB,SAAS,EAGzDziB,CAAAA,AAhFW,IAAI,CAgFRyiB,SAAS,CAACxF,QAAQ,CAAG,IAAIJ,CAAmB,EAEvD7c,AAlFe,IAAI,CAkFZyiB,SAAS,CAACxF,QAAQ,CAACgB,UAAU,CAAC,CACjC7Z,EAAGmgB,EACHjgB,EAAGkgB,CACP,GACI,AAAC7B,GAtFU,IAAI,CAsFY4B,IAC3BvkB,CAAAA,AAvFW,IAAI,CAuFRskB,aAAa,CAAGA,CAAY,EAEhC,CAAA,CACX,CAKA,SAASY,KACL,IAAM/kB,EAAU,IAAI,CAACA,OAAO,EAAI,CAAC,EAAGL,EAAQ,IAAI,CAACA,KAAK,CAAEqlB,EAAarlB,EAAMG,KAAK,CAAEmlB,EAAc,IAAI,CAACnlB,KAAK,CAAES,EAAQ,IAAI,CAACA,KAAK,CAAEmC,EAAQ,IAAI,CAACA,KAAK,CAAE6K,EAAQvN,EAAQuN,KAAK,EAAI,IAAI,CAAC5L,SAAS,CAAC,IAAK,CAAA,GAAOqO,EAAQhQ,EAAQgQ,KAAK,EAAI,IAAI,CAACrO,SAAS,CAAC,IAAK,CAAA,GAAOujB,EAAU,IAAI,CAACvjB,SAAS,CAAC,MAAO,CAAA,GAAOwjB,EAAW,IAAI,CAACxjB,SAAS,CAAC,OAAQ,CAAA,GAAO4N,EAAU,IAAI,CAAC4U,aAAa,EAAInkB,EAAQ4B,IAAI,CAAE4N,EAAYjP,EAAMkP,WAAW,GAEvZC,EAAOF,EAAUhP,GAAG,CAAID,CAAAA,EAAMoP,cAAc,EAAI,CAAA,EAAIC,EAAOJ,EAAU7O,GAAG,CAAIJ,CAAAA,EAAMoP,cAAc,EAAI,CAAA,EAAIE,EAAYnN,EAAM+M,WAAW,GAAIK,EAAOD,EAAUrP,GAAG,CAAIkC,CAAAA,EAAMiN,cAAc,EAAI,CAAA,EAAII,EAAOF,EAAUlP,GAAG,CAAI+B,CAAAA,EAAMiN,cAAc,EAAI,CAAA,EAAIyV,EAAa,CAAC,EAAGC,EAAW,CAAC,CAAC,IAAI,CAACA,QAAQ,CAAEC,EAAsBtlB,EAAQslB,mBAAmB,CAAErlB,EAAYD,EAAQC,SAAS,CAAEmP,EAAU,IAAI,CAACC,aAAa,EACxY,AAAiC,aAAjC,IAAI,CAACA,aAAa,CAAC7H,IAAI,CAAC,KAAqB8F,EAAY,CAAC,CAACtN,EAAQwN,QAAQ,CAAEsX,EAAY,IAAI,CAACA,SAAS,EAAI,EAAGS,EAAiB,IAAI,CAACA,cAAc,CAAErV,EAAS,CAAC3C,EAAOiY,EAAWxlB,AAA+B,MAA/BA,EAAQylB,kBAAkB,CAAUC,EAAa,AAAC,CAAA,IAAI,CAAC/jB,SAAS,CAAC,KAAKxB,MAAM,CAC5P,IAAI,CAACwB,SAAS,CAAC,KACf,KAAK,CAAA,GACL,IAAI,CAAC3B,OAAO,CAACuN,KAAK,EAClB,IAAI,CAAC5L,SAAS,CAAC,IAAK,CAAA,GAAQoM,EAAYoR,GAAiBnf,EAAQ+N,SAAS,CAAE,GAAI4X,EAAkB3lB,EAAQ4lB,eAAe,EAAI9V,EAAM+V,EAAUlmB,EAAMkmB,OAAO,CAC1JjN,EAAW,CAAA,EAAOkN,EAAapP,EAAUhU,EAAMiU,YAAY,CAAC1W,GAAY4Q,EAAQkV,EAAQC,EAAMC,EAElG,GAAI,CAAC,IAAI,CAAC/jB,OAAO,GAGjB,IAAI,CAACL,MAAM,EAAEzC,QAAQ,AAACoU,IAClBA,GAAOkO,mBACX,GACA,IAAI,CAAC7f,MAAM,CAAG,EAAE,CACZgkB,GAAW,CAACA,EAAQK,QAAQ,CACJvmB,CAAAA,EAAMuD,UAAU,EAAErD,SAAW,IAAI,EACrDF,EAAMgjB,WAAW,EAAEvgB,KAAK,AAACoR,GAAUA,EAAM3T,MAAM,GAAK,IAAI,CAAA,IAExDF,EAAMuD,UAAU,CAAGvD,EAAMgjB,WAAW,CAAG,KAAK,EAC5CkD,EAAQviB,IAAI,CAAC,IAGZ3D,EAAMgjB,WAAW,EACtBhjB,CAAAA,EAAMgjB,WAAW,CAAGhjB,EAAMgjB,WAAW,CAACC,MAAM,CAAC,AAACpP,GAAUA,EAAM3T,MAAM,GAAK,IAAI,CAAA,EAM7EU,EAAM8B,SAAS,EAAIK,EAAML,SAAS,IAItCuW,EAAW6G,GAAwB9f,EAAO,IAAI,EAC9CA,EAAMuC,OAAO,CAAG,CAAA,EACZ,CAAC,IAAI,CAAChB,OAAO,EA3Bb,MA+BA,CAAA,CAAA,IAAI,CAACW,MAAM,EAAI,IAAI,CAACskB,KAAK,AAAD,GACxB1E,GAAgB,IAAI,EAInBlD,EAAkC5e,IAW/B,IAAI,CAAC8C,WAAW,EAChB,IAAI,CAACA,WAAW,GAAKuiB,GAAYviB,aACjC,IAAI,CAACA,WAAW,CAACyF,OAAO,GAG5B,IAAI,CAACzF,WAAW,CAAGuiB,GAAYviB,YAG3BwiB,GAAeA,EAAYnhB,MAAM,EACjC,CAAA,IAAI,CAACoc,YAAY,CACb+E,EAAYnhB,MAAM,CACdmhB,EAAYnhB,MAAM,CAACoE,OAAO,EAAC,IAnBnC,IAAI,CAACzF,WAAW,GAAKuiB,GAAYviB,aACjC,CAAA,IAAI,CAACA,WAAW,CAAG,KAAK,CAAA,EAE5B,IAAI,CAACA,WAAW,CAAG,IAAI,CAAC8U,SAAS,CAAC,cAAe,UAAW,UAAW,EAAG5X,EAAM6X,WAAW,EAAEC,QAAQ,CAAC,uBAmB1G,IAAM5V,EAAS,IAAI,CAACA,MAAM,CAAG,EAAE,CAAEukB,EAAa,CAACC,EAAS1S,EAAOzC,EAAGuS,KAC9D,IAAMxf,EAAIyhB,EAAAA,GAAYA,CAAS,CAACZ,EAAY5T,EAAE,CAAUoV,EAAY,AAAC9C,IAC7D7jB,EAAMwD,QAAQ,GACdqgB,EAAQjjB,EAAM0E,GAAG,CAAGue,EACpB7P,EAAQjR,EAAMuC,GAAG,CAAG0O,GAExB9R,EAAO+B,IAAI,CAAC,CACRsE,QAASyW,EACT1a,EAAGA,EACHoiB,QAAS7C,EACTA,MAAOA,EACP7P,MAAOA,EACPzC,EAAG4T,EAAY5T,EACfuS,WAAYA,CAChB,EACJ,EAIA4C,EAAUrhB,KAAKuhB,IAAI,CAACF,GAEpBjpB,EAAQooB,EAAWa,EAAUA,EAAU,IAAM1S,EAIzC2R,IACKF,CAAU,CAAChoB,EAAM,CAIb6G,IAAMyhB,CAAS,CAACA,EAAUvlB,MAAM,CAAG,EAAE,GAG1C0B,EAAO1B,MAAM,GACbmmB,EAAUD,KAPVjB,CAAU,CAAChoB,EAAM,CAAG,CAAA,EACpBkpB,EAAUD,IAStB,CAEA,CAAA,IAAI,CAACG,WAAW,CAAG7H,EACnBM,GAAsB,IAAI,CAAE,gBACxB,IAAI,CAACra,EAAE,CAAC,SACRmJ,EAAY,GACZkX,GAAanhB,QACbkhB,GACA,CAACA,EAAWyB,eAAe,GAC3BzB,EAAWyB,eAAe,CAAG9mB,EAAMiZ,QAAQ,CAAC1a,UAAU,CAAC,CACnDwoB,QAAS,SACTC,SAAU,CACN,CACID,QAAS,eACTE,WAAY,CACRC,SAAU,SACVhQ,OAAQ,IAAO9I,CACnB,CACJ,EACH,CACD6Y,WAAY,CAAEhK,GAAI,WAAY,CAClC,GACAqI,EAAYnhB,MAAM,CAACuc,IAAI,CAAC,CACpBuC,OAAQ,iBACZ,IAEAhK,IACA2G,GAA4B3G,EAAU,IAAI,EAC1CA,EAAS7D,UAAU,CAAC,IAAI,EAExB+N,GAA0B,IAAI,GAsFlC,IAAM/iB,EAAe6Y,EAAS/K,QAAQ,AAejClO,CAAAA,EAAMiZ,QAAQ,CAACC,SAAS,GACrB9Y,EAAaqO,KAAK,CAACK,UAAU,EAC7BiG,QAAQO,IAAI,CAAC,oBAEjB2M,GAAUtU,EACN,IAAI,CAAC1L,IAAI,CAACsS,KAAK,CAAC4Q,GACfvX,GAASgC,EArGlB,SAAsBvR,CAAC,CAAEkT,CAAC,EACtB,IAAMX,EAAiB,AAAuB,KAAA,IAAhB5Q,EAAMvC,KAAK,CACrC6G,EAAGE,EAAGkiB,EAAS1S,EAAO8P,EAAYpS,EAAM,CAAA,EAAOK,EAAY,CAAA,QAC/D,CAAK4N,GAAQthB,KAGT,CAACuS,IACGL,GACAjM,EAAIjG,CAAC,CAAC,EAAE,CACRmG,EAAInG,CAAC,CAAC,EAAE,GAGRiG,EAAIjG,EACJmG,EAAI6L,CAAK,CAACkB,EAAE,EAAIyU,GAAmB,MAGnCvW,GACIc,GACA/L,CAAAA,EAAInG,EAAEkW,KAAK,CAAC,EAAG,EAAC,EAEpB7C,EAAM6T,CAAO,CAAChU,EAAE,CAChB/M,EAAIghB,CAAQ,CAACjU,EAAE,EAEV5D,IACLrJ,EAAIjG,EAAEiG,CAAC,CAEPoN,EAAMlN,AADNA,CAAAA,EAAInG,EAAEmW,MAAM,AAAD,EACDnW,EAAEmG,CAAC,CACbsf,EAAazlB,EAAEylB,UAAU,EAGzB,AAAC8B,GACD7T,CAAAA,EAAY,AAACvN,CAAAA,GAAK,CAAA,GAAM2L,GAAQ3L,GAAK4L,CAAG,EAElC,OAAN5L,GAAcF,GAAKyL,GAAQzL,GAAK2L,GAAQ8B,IACxC2U,EAAU9lB,EAAM8T,QAAQ,CAACpQ,EAAG,CAAA,GACxBohB,GACI,CAAA,AAAgB,KAAA,IAATW,GACPK,IAAYP,CAAU,IAClB,AAAC1W,GACDiC,CAAAA,EAAMlN,CAAAA,EAEN,CAAA,AAAgB,KAAA,IAAT8hB,GACP9hB,EAAI4hB,CAAK,IACTA,EAAS5hB,EACT8hB,EAAO/U,GAEP,CAAA,AAAgB,KAAA,IAAT8U,GACP3U,EAAMR,CAAK,IACXA,EAASQ,EACT2U,EAAO9U,IAIVsU,GAAYa,IAAYP,IAEL,KAAA,IAATE,IACPrS,EACIjR,EAAM2R,QAAQ,CAAC0R,EAAQ,CAAA,GAC3BrP,EACIhU,EAAM2R,QAAQ,CAACxD,EAAQ,CAAA,GAC3BuV,EAAWC,EAAS1S,EAAOsS,EAAMxC,GAC7B/M,IAAY/C,GACZyS,EAAWC,EAAS3P,EAASsP,EAAMvC,IAG3CuC,EAAOC,EAAO,KAAK,EACnBH,EAAcO,IAKlBD,EAAWC,EADX1S,EAAQ3O,KAAKuhB,IAAI,CAAC7jB,EAAM2R,QAAQ,CAAClQ,EAAG,CAAA,IACT+M,EAAGuS,KAInC,CAAClT,EACZ,EAIyD,KACrD0O,GAAsB,IAAI,CAAE,kBAE5B,OAAO,IAAI,CAACuH,WAAW,CAGnB,IAAI,CAACxmB,OAAO,EACZ,IAAI,CAACwmB,WAAW,GAEhBzmB,EAAaqO,KAAK,CAACK,UAAU,EAC7BiG,QAAQY,OAAO,CAAC,mBAExB,GAWJ,CAKA,SAASwR,GAAqB7E,CAAO,EACjC,IAAI7M,EAAU,CAAA,EAMd,GALI,IAAI,CAACzV,KAAK,CAACK,OAAO,EAAI,IAAI,CAACL,KAAK,CAACK,OAAO,CAACF,KAAK,EAC9CsV,CAAAA,EAAU,AAA4C,KAAA,IAArC,IAAI,CAACzV,KAAK,CAACK,OAAO,CAACF,KAAK,CAACsV,OAAO,EAE7C,IAAI,CAACzV,KAAK,CAACK,OAAO,CAACF,KAAK,CAACsV,OAAO,AAAD,EAEnC,CAACA,GAAW,CAAC,IAAI,CAAClT,OAAO,CACzB,OAAO+f,EAAQpjB,IAAI,CAAC,IAAI,CAE5B,CAAA,IAAI,CAACc,KAAK,CAACuC,OAAO,CAAG,CAAA,EAErB,IAAM0W,EAAW6G,GAAwB,IAAI,CAAC9f,KAAK,CAAE,IAAI,EACrDiZ,IACA2G,GAA4B3G,EAAU,IAAI,EAC1CA,EAAS7D,UAAU,CAAC,IAAI,GAE5B+N,GAA0B,IAAI,CAClC,CAkDA,SAASiE,GAAsB9E,CAAO,EAClC,GAAI,IAAI,CAAC/f,OAAO,CAAE,CACd,GAAIigB,GAAY,IAAI,EAChB,MAAO,CAAC,EAEZ,GAAI,IAAI,CAAC5hB,KAAK,CAAC8B,SAAS,EAAI,IAAI,CAACK,KAAK,CAACL,SAAS,CAI5C,OAAO,IAAI,AAEnB,CACA,OAAO4f,EAAQ+E,KAAK,CAAC,IAAI,CAAE,EAAE,CAAC9S,KAAK,CAACrV,IAAI,CAACooB,UAAW,GACxD,CAOA,SAASC,GAAsBjF,CAAO,EAClC,IAAIkF,EAAgB,IAAI,CAACnnB,OAAO,CAAC4B,IAAI,CACrC,GAAI4d,GAAa,IAAI,CAAC7f,KAAK,GAAKyB,AAriHqBjC,CAqiHH,CAAC,IAAI,CAACgC,IAAI,CAAC,CAAE,CAC3D,IAGAgiB,EAAYtjB,AAHG,IAAI,CAGA+E,EAAE,CAAC,YAClB,CAAC/E,AAJU,IAAI,CAIP+E,EAAE,CAAC,WACX,CAAC/E,AALU,IAAI,CAKP+E,EAAE,CAAC,YACX,CAAC/E,AANU,IAAI,CAMP+E,EAAE,CAAC,WAIf,GAEA,CAAC4d,GAZc,IAAI,CAYQ2E,IACvBhE,GACAtjB,AAdW,IAAI,CAcR+E,EAAE,CAAC,YAEV/E,AAhBW,IAAI,CAgBRG,OAAO,CAACwN,QAAQ,EACvB,CAAC2U,GAjBU,IAAI,CAiBM,CAAA,GAAO,CAE5B,GAAItiB,AAnBO,IAAI,CAmBJqC,OAAO,EAAKrC,CAAAA,AAnBZ,IAAI,CAmBeU,KAAK,EAAE8B,WAAaxC,AAnBvC,IAAI,CAmB0C6C,KAAK,EAAEL,SAAQ,EACpE,MAGA8gB,CAAAA,GAAatjB,AAAsB,aAAtBA,AAvBN,IAAI,CAuBS6C,KAAK,CAACvB,IAAI,CAC9ByiB,GAAmB/kB,IAAI,CAxBhB,IAAI,CAwBqBooB,SAAS,CAAC,EAAE,EAG5ChF,EAAQ+E,KAAK,CA3BN,IAAI,CA2BW,EAAE,CAAC9S,KAAK,CAACrV,IAAI,CAACooB,UAAW,IAEnDE,EAAgBtnB,AA7BL,IAAI,CA6BQ8B,SAAS,CAAC,IAAK,CAAA,EAC1C,CAKA,GAFA9B,AAjCe,IAAI,CAiCZqC,OAAO,CAAGsgB,GAjCF,IAAI,CAiCwB2E,GAEvCtnB,AAnCW,IAAI,CAmCRqC,OAAO,CAAE,KAEZyP,CACA9R,CAtCO,IAAI,CAsCJG,OAAO,CAAC4B,IAAI,EAAEzB,QAEjB,CAAA,AAAC+e,GADLvN,EAAa9R,AAvCN,IAAI,CAuCSunB,kBAAkB,CAACvnB,AAvChC,IAAI,CAuCmCG,OAAO,CAAC4B,IAAI,IAErD8Z,GAAQ/J,IACR9R,AA1CE,IAAI,CA0CC+E,EAAE,CAAC,YACXma,GAAkB,GAAI,CAAA,EAAOlf,AA3C1B,IAAI,CA2C6BF,KAAK,CADrB,MAtqBpBE,EA4nBG,IAAI,AA3nBvBA,CAAAA,EAAOC,KAAK,CAAGD,EAAOC,KAAK,EAAI,CAE3BijB,SAAW,AAACsE,GAAOtE,GAASljB,EAAQwnB,EACxC,EACA,IAAMC,EAAiBznB,EAAOC,KAAK,CAACynB,OAAO,CAAG,EAAE,CAoBhD,GAjBA,CAAC,UAAW,cAAe,iBAAiB,CAACnoB,OAAO,CAAC,AAACV,IAClD4oB,EAAe1jB,IAAI,CAAC,CAChBlF,KAAMA,EACN+L,IAAK5K,CAAM,CAACnB,EAAK,CACjB8oB,IAAKnpB,OAAOO,cAAc,CAACC,IAAI,CAACgB,EAAQnB,EAC5C,EACJ,GACAmB,EAAO4nB,OAAO,CAAG,CAAA,EACjB5nB,EAAO6nB,WAAW,CAAG,CAAA,EACrB7nB,EAAO8nB,cAAc,CAAG,CAAA,EAExB9nB,EAAO+nB,iBAAiB,CAAG,CAAA,EAEvB/nB,EAAOgoB,aAAa,EACpBhoB,CAAAA,EAAOgoB,aAAa,CAAGhoB,EAAOgoB,aAAa,CAAC3f,OAAO,EAAC,EAGpDrI,EAAO+E,EAAE,CAAC,YACV,CAAC/E,EAAO+E,EAAE,CAAC,YACX/E,EAAO+B,IAAI,CAACzB,MAAM,CAAE,CACpB,IAAK,IAAMqT,KAAS3T,EAAO+B,IAAI,CAC3B4R,GAAOtL,WAEXrI,CAAAA,EAAO+B,IAAI,CAACzB,MAAM,CAAG,EACrBN,EAAOgC,MAAM,CAAC1B,MAAM,CAAG,EACvB,OAAON,EAAOskB,aAAa,AAC/B,CAyoBI,MAEI2D,AApoBZ,SAAmBjoB,CAAM,EACrB,IAAMC,EAAQD,EAAOC,KAAK,CAAEH,EAAQE,EAAOF,KAAK,CAAEqlB,EAAarlB,EAAMG,KAAK,CAC1E,GAAIklB,GAAYviB,YAGZ,IAAK,IAAMW,KAFX4hB,EAAWviB,WAAW,CAACyF,OAAO,GAC9B8c,EAAWviB,WAAW,CAAG,KAAK,EACd9C,EAAME,MAAM,EACxBuD,EAAEX,WAAW,CAAG,KAAK,EACrBW,EAAEX,WAAW,CAAGW,EAAEmU,SAAS,CAAC,cAAe,UAAW,UAAW,EAAG5X,EAAM6X,WAAW,EAAEC,QAAQ,CAAC,sBAKpG3X,IACA,AAACA,CAAAA,EAAMynB,OAAO,EAAI,EAAE,AAAD,EAAGnoB,OAAO,CAAC,AAAC2oB,IACvBA,EAAQP,GAAG,CACX3nB,CAAM,CAACkoB,EAAQrpB,IAAI,CAAC,CAAGqpB,EAAQtd,GAAG,CAIlC,OAAO5K,CAAM,CAACkoB,EAAQrpB,IAAI,CAAC,AAEnC,GAEIoB,EAAMwC,KAAK,EACXxC,EAAMwC,KAAK,IAIlB3C,CAAAA,EAAM6X,WAAW,EAAI3X,EAAO8f,KAAK,AAAD,GAAI6B,MACzC,EAsjBuB,IAAI,CAoDvB,MAEIS,EAAQ+E,KAAK,CAAC,IAAI,CAAE,EAAE,CAAC9S,KAAK,CAACrV,IAAI,CAACooB,UAAW,GAErD,CAKA,SAASe,GAAsB/F,CAAO,EAClC,IAAM7F,EAAS6F,EAAQ+E,KAAK,CAAC,IAAI,CAAE,EAAE,CAAC9S,KAAK,CAACrV,IAAI,CAACooB,UAAW,WAC5D,AAAI,IAAI,CAACnnB,KAAK,EAAIsc,EACP,IAAI,CAACtc,KAAK,CAACijB,QAAQ,CAAC3G,GAExBA,CACX,CAY6B,IAAM6L,GANf,CAChBzkB,QAtgCJ,SAA6B0kB,CAAW,CAAEC,CAAW,CAAElF,CAAU,CAAEvf,CAAO,EACtE,GAAI0b,GAAuBX,EAAsB,gBAAiB,CAC9D,IAAM2J,EAAc5J,IAAa4J,WAAW,CAAEC,EAAcH,EAAYvpB,SAAS,CAkCjF,GAjCAkgB,EAAqBqJ,EAAa,UAAWxF,IAC7C7D,EAAqBqJ,EAAa,OAAQrF,IACtCnf,GACA2kB,CAAAA,EAAYC,YAAY,CAAGvD,EAAiB,EAEhD1F,GAAKgJ,EAAa,cAAetB,IACjC1H,GAAKgJ,EAAa,cAAenB,IACjC7H,GAAKgJ,EAAa,cAAeL,IACjC,CACI,YACA,iBACA,cACA,aACA,SACH,CAAC5oB,OAAO,CAAC,AAACmpB,GAAWC,AA02B9B,CAAA,SAA6BH,CAAW,CAAEF,CAAW,CAAEI,CAAM,EAIzD,SAASE,EAAOxG,CAAO,EACnB,IAAMyG,EAAY,IAAI,CAAC1oB,OAAO,CAACwN,QAAQ,EAClC+a,CAAAA,AAAW,cAAXA,GAA0BA,AAAW,mBAAXA,CAA0B,CACrD,AAAC,CAAA,IAAI,CAACrmB,OAAO,GACbwmB,GACClJ,GAAa,IAAI,CAAC7f,KAAK,GACxB,AAAc,YAAd,IAAI,CAACwB,IAAI,EACT,AAAc,YAAd,IAAI,CAACA,IAAI,EACRC,AAj/G4CjC,CAi/G1B,CAAC,IAAI,CAACgC,IAAI,CAAC,EAC9B,AAAgC,IAAhC,IAAI,CAACnB,OAAO,CAACiB,cAAc,CAItBsnB,AAAW,WAAXA,GAAuB,IAAI,CAACD,YAAY,EAC7C,IAAI,CAACA,YAAY,GAJjBrG,EAAQpjB,IAAI,CAAC,IAAI,CAMzB,CAGA,GAFAwgB,GAAKgJ,EAAaE,EAAQE,GAEtBF,AAAW,cAAXA,EACA,IAAK,IAAMpnB,IAAQ,CACf,SACA,YACA,cACA,UACA,UACH,CACOgnB,CAAW,CAAChnB,EAAK,EACjBke,GAAK8I,CAAW,CAAChnB,EAAK,CAACxC,SAAS,CAAE4pB,EAAQE,EAI1D,CAAA,EA94BkDJ,EAAaF,EAAaI,IACpElJ,GAAK4D,EAAWtkB,SAAS,CAAE,iBAAkB,SAAUsjB,CAAO,CAAE9gB,CAAI,CAAE8B,CAAC,EACnE,GAAI9B,AAAS,UAATA,GAAoB,IAAI,CAACtB,MAAM,CAACqC,OAAO,CAAE,CACzC,IAAMsR,EAAQvQ,EAAEuQ,KAAK,CACrB,GAAI,AAACA,CAAAA,EAAM8P,IAAI,EAAI9P,EAAM+P,KAAK,AAAD,GAAO/P,CAAAA,EAAM3T,MAAM,CAACG,OAAO,CAACmV,MAAM,EAAE0B,QAAU,EAAC,EACxE,MAER,CACA,OAAOoL,EAAQ+E,KAAK,CAAC,IAAI,CAAE,EAAE,CAAC9S,KAAK,CAACrV,IAAI,CAACooB,UAAW,GACxD,GAEA/nB,EAAiBE,OAAO,CAAC,AAAC+B,IACtB,IAAMwnB,EAAkBP,CAAW,CAACjnB,EAAK,CACrCwnB,IACAA,EAAgB1nB,cAAc,CAAG,IACjC0nB,EAAgB3W,SAAS,CAAG,EAAE,CAC9BmW,CAAW,CAAChnB,EAAK,CAACxC,SAAS,CAACkZ,WAAW,CAAG,CAAA,EAElD,GACInU,EAAS,CACT,GAAM,CAAEklB,KAAMC,CAAU,CAAEC,WAAYC,CAAgB,CAAEC,OAAQC,CAAY,CAAEzN,OAAQ0N,CAAY,CAAEC,QAASC,CAAa,CAAEC,QAASC,CAAa,CAAEC,QAASC,CAAa,CAAE,CAAGrB,EAe/K,GAdIU,GACA7J,GAAO6J,EAAWlqB,SAAS,CAAE,CACzB6T,KAAM,CAAA,EACNqF,YAAa,CAAA,EACbwN,SAAU,CAAA,CACd,GAEA0D,GACA/J,GAAO+J,EAAiBpqB,SAAS,CAAE,CAC/B6T,KAAM,CAAA,EACNqF,YAAa,CAAA,EACbwN,SAAU,CAAA,CACd,GAEA4D,EAAc,CACd,IAAMQ,EAAcR,EAAatqB,SAAS,AAG1C,QAAO8qB,EAAYjD,WAAW,CAG9BnH,GAAKoK,EAAa,gBAAiB,SAAUxH,CAAO,QAChD,CAAI,IAAI,CAAC/f,OAAO,EAGT+f,EAAQ+E,KAAK,CAAC,IAAI,CAAE,EAAE,CAAC9S,KAAK,CAACrV,IAAI,CAACooB,UAAW,GACxD,EACJ,CACIiC,GACAlK,GAAOkK,EAAavqB,SAAS,CAAE,CAC3B6T,KAAM,CAAA,EACN6S,SAAU,CAAA,CACd,GAEAiE,GACAA,CAAAA,EAAc3qB,SAAS,CAAC6T,IAAI,CAAG,CAAA,CAAG,EAKtC,CAAC4W,EAAeI,EAAc,CAACpqB,OAAO,CAAC,AAACsqB,IAChCA,GACArK,GAAKqK,EAAG/qB,SAAS,CAAE,aAAcmoB,GAEzC,EACJ,CACJ,CACA,OAAOoB,CACX,EAi7BIzG,gBAAAA,GACAG,UAAAA,GACAmB,SAAAA,EACJ,EA+KmC4G,GAHf,CAChBC,oBAtJwB,CACxBC,UAAW,UACXC,aAAc,UACdC,KAAM,UACNC,WAAY,UACZC,MAAO,UACPC,MAAO,UACPC,OAAQ,UACRC,eAAgB,UAChBC,KAAM,UACNC,WAAY,UACZC,MAAO,UACPC,UAAW,UACXC,UAAW,UACXC,WAAY,UACZC,UAAW,UACXC,MAAO,UACPC,eAAgB,UAChBC,SAAU,UACVC,QAAS,UACTC,KAAM,UACNC,SAAU,UACVC,SAAU,UACVC,cAAe,UACfC,SAAU,UACVC,UAAW,UACXC,UAAW,UACXC,YAAa,UACbC,eAAgB,UAChBC,WAAY,UACZC,WAAY,UACZC,QAAS,UACTC,WAAY,UACZC,aAAc,UACdC,cAAe,UACfC,cAAe,UACfC,cAAe,UACfC,WAAY,UACZC,SAAU,UACVC,YAAa,UACbC,QAAS,UACTC,WAAY,UACZC,SAAU,UACVC,UAAW,UACXC,YAAa,UACbC,YAAa,UACbC,QAAS,UACTC,UAAW,UACXC,WAAY,UACZC,KAAM,UACNC,UAAW,UACXC,KAAM,UACNC,KAAM,UACNC,MAAO,UACPC,YAAa,UACbC,SAAU,UACVC,QAAS,UACTC,UAAW,UACXC,OAAQ,UACRC,MAAO,UACPC,MAAO,UACPC,SAAU,UACVC,cAAe,UACfC,UAAW,UACXC,aAAc,UACdC,UAAW,UACXC,WAAY,UACZC,UAAW,UACXC,qBAAsB,UACtBC,UAAW,UACXC,WAAY,UACZC,UAAW,UACXC,YAAa,UACbC,cAAe,UACfC,aAAc,UACdC,eAAgB,UAChBC,eAAgB,UAChBC,eAAgB,UAChBC,YAAa,UACbC,KAAM,UACNC,UAAW,UACXC,MAAO,UACPC,QAAS,UACTC,OAAQ,UACRC,iBAAkB,UAClBC,WAAY,UACZC,aAAc,UACdC,aAAc,UACdC,eAAgB,UAChBC,gBAAiB,UACjBC,kBAAmB,UACnBC,gBAAiB,UACjBC,gBAAiB,UACjBC,aAAc,UACdC,UAAW,UACXC,UAAW,UACXC,SAAU,UACVC,YAAa,UACbC,KAAM,UACNC,QAAS,UACTC,MAAO,UACPC,UAAW,UACXC,OAAQ,UACRC,UAAW,UACXC,OAAQ,UACRC,cAAe,UACfC,UAAW,UACXC,cAAe,UACfC,cAAe,UACfC,WAAY,UACZC,UAAW,UACXC,KAAM,UACNC,KAAM,UACNC,KAAM,UACNC,WAAY,UACZC,OAAQ,UACRC,IAAK,UACLC,UAAW,UACXC,UAAW,UACXC,YAAa,UACbC,OAAQ,UACRC,WAAY,UACZC,SAAU,UACVC,SAAU,UACVC,OAAQ,UACRC,OAAQ,UACRC,QAAS,UACTC,UAAW,UACXC,UAAW,UACXC,KAAM,UACNC,YAAa,UACbC,UAAW,UACXC,IAAK,UACLC,KAAM,UACNC,QAAS,UACTC,OAAQ,UACRC,UAAW,UACXC,OAAQ,UACRC,UAAW,UACXC,MAAO,UACPC,WAAY,UACZC,OAAQ,UACRC,YAAa,SACjB,CAQA,EAmBM,CAAEjmB,IAAKkmB,EAAS,CAAEjmB,IAAKkmB,EAAS,CAAE,CAAI3zB,IAGtC,CAAEM,SAAUszB,EAAc,CAAEvtB,MAAOwtB,EAAW,CAAE,CAAI7zB,IAMpD8zB,GAAiB,CACnB,QACA,qBACA,YACA,YACH,CAwED,SAASC,KACL,IAAIzwB,EAAQmD,EAAK,CAAA,EACjB,GAAI,AAA2C,KAAA,IAApCktB,GAAUK,qBAAqB,CAAkB,CACxD1wB,EAASowB,GAAU/Y,aAAa,CAAC,UACjC,IAAK,IAAI1I,EAAI,EAAGA,EAAI6hB,GAAe5yB,MAAM,CAAE,EAAE+Q,EACzC,GAAI,CAEA,GAAI,MADC3O,EAAO2W,UAAU,CAAC6Z,EAAc,CAAC7hB,EAAE,EAEpC,MAAO,CAAA,CAEf,CACA,MAAOjO,EAAG,CAEV,CAER,CACA,MAAO,CAAA,CACX,CAU6B,IAAMiwB,GAJrB,CACV1vB,QAvFJ,SAAuBC,CAAU,CAAE0vB,CAAS,CAAEjL,CAAW,CAAEC,CAAW,CAAElF,CAAU,CAAEmQ,CAAU,EAC1F,IAAM1vB,EAAUsvB,KACXtvB,IACG,AAA2G,KAAA,IAApG,AAACzE,IAA+Eo0B,eAAe,CAEtGp0B,IAA8Eo0B,eAAe,GAG7FP,GAAY,KAGhBM,GAAc,CAACA,EAAWE,KAAK,CAACtF,oBAAoB,EACpDoF,CAAAA,EAAWE,KAAK,CAAG,CACf,GAAGF,EAAWE,KAAK,CACnB,GAAG3J,GAAYC,mBAAmB,AACtC,CAAA,EAGJrmB,EAAiBC,OAAO,CAACC,EAAYC,GACrCukB,GAAkBzkB,OAAO,CAAC0kB,EAAaC,EAAalF,EAAYvf,GAIhEmvB,GAAeM,EAAW,cAAe,SAAUlwB,CAAC,EAKhD,IAAK,IAAMid,IAHW,CAAC,IAAI,CAACvgB,KAAK,IAAK,IAAI,CAACE,MAAM,CAAC,CAC7C8d,GAAG,CAAC,AAACte,GAASA,EAAK6gB,YAAY,EAC/B0C,MAAM,CAAC7C,SAC8B,CACtC,GAAM,CAAEnK,MAAAA,CAAK,CAAEhT,IAAAA,CAAG,CAAE,CAAG,IAAI,CAAE2wB,EAAW3d,EAAQ,SAAW,SAAU4d,EAAe5d,EAAQ,aAAe,aAAc6d,EAAYvT,GAAc,CAACqT,EAAS,EAAI,EAC7JG,EAAQ,EAAG/wB,EAAY,EAAGue,EAAU,EAAG0B,EAAS,MAChD,CAAA,IAAI,CAACvgB,SAAS,GACdqxB,EAAQ,AAACzwB,CAAAA,EAAEywB,KAAK,EAAI,CAAA,EAAKD,EACzB9wB,EAAY,AAACud,CAAAA,GAAc,CAACsT,EAAa,EAAI,CAAA,EACzCE,EAASzwB,CAAAA,EAAE0wB,IAAI,EAAI,CAAA,EACnBF,EAAY7wB,EACZ8wB,EAAQ9wB,EACZse,EAAU,GACV0B,EAAS,aAEb1C,GACMG,KAAK,CACP,CAACkT,EAAS,CAAEG,EACZ,CAACF,EAAa,CAAE7wB,CACpB,GACKoe,IAAI,CACL6S,WAAY,8BACZhR,OAAAA,EACA1B,QAAAA,CACJ,EACJ,CACJ,EACJ,EAoCI8R,gBAAAA,EACJ,EAqPMa,GAAK50B,GACX40B,CAAAA,GAAEb,eAAe,CAAGE,GAAYF,eAAe,CAC/CE,GAAY1vB,OAAO,CAACqwB,GAAEC,KAAK,CAAED,GAAEE,IAAI,CAAEF,GAAEG,MAAM,CAAEH,GAAE1L,WAAW,CAAE0L,GAAEI,KAAK,CAAEJ,GAAEK,KAAK,EACjD,IAAMn1B,GAAcE,IAGvC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}