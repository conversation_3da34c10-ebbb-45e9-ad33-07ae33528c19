!/**
 * Highstock JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/pointandfigure
 * @requires highcharts
 * @requires highcharts/modules/stock
 *
 * Point and figure series type for Highcharts Stock
 *
 * (c) 2010-2025 <PERSON><PERSON><PERSON>
 *
 * License: www.highcharts.com/license
 */function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(e._Highcharts,e._Highcharts.SeriesRegistry,e._Highcharts.RendererRegistry):"function"==typeof define&&define.amd?define("highcharts/modules/pointandfigure",["highcharts/highcharts"],function(e){return t(e,e.SeriesRegistry,e.RendererRegistry)}):"object"==typeof exports?exports["highcharts/modules/pointandfigure"]=t(e._Highcharts,e._Highcharts.SeriesRegistry,e._Highcharts.RendererRegistry):e.Highcharts=t(e.Highcharts,e.Highcharts.SeriesRegistry,e.Highcharts.RendererRegistry)}("undefined"==typeof window?this:window,(e,t,r)=>(()=>{"use strict";var s,o={512:e=>{e.exports=t},608:e=>{e.exports=r},944:t=>{t.exports=e}},i={};function n(e){var t=i[e];if(void 0!==t)return t.exports;var r=i[e]={exports:{}};return o[e](r,r.exports,n),r.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var a={};n.d(a,{default:()=>w});var h=n(944),l=n.n(h),p=n(512),u=n.n(p);let{seriesTypes:{scatter:{prototype:{pointClass:d}}}}=u(),c=class extends d{resolveMarker(){let e=this.series.options;this.marker=this.options.marker=this.upTrend?e.markerUp:e.marker,this.color=this.options.marker.lineColor}resolveColor(){super.resolveColor(),this.resolveMarker()}getClassName(){return super.getClassName.call(this)+(this.upTrend?" highcharts-point-up":" highcharts-point-down")}};var g=n(608),f=n.n(g);!function(e){let t=[];function r(e,t,r,s){return[["M",e,t],["L",e+r,t+s],["M",e+r,t],["L",e,t+s],["Z"]]}e.compose=function(e){-1===t.indexOf(e)&&(t.push(e),e.prototype.symbols.cross=r);let s=f().getRendererType();t.indexOf(s)&&t.push(s)}}(s||(s={}));let m=s,{composed:y}=l(),{scatter:x,column:{prototype:b}}=u().seriesTypes,{extend:k,merge:C,pushUnique:R,isNumber:v,relativeLength:M}=l();class H extends x{constructor(){super(...arguments),this.allowDG=!1}static compose(e){R(y,"pointandfigure")&&m.compose(e)}init(){super.init.apply(this,arguments),this.pnfDataGroups=[]}getProcessedData(){let e;if(!this.pnfDataGroups)return{modified:this.dataTable.modified,cropped:!1,cropStart:0,closestPointRange:1};let t=this.dataTable.modified,r=this.options,s=this.getColumn("x",!0),o=this.getColumn("y",!0),i=r.boxSize,n=v(i)?i:M(i,o[0]),a=this.pnfDataGroups,h=n*r.reversalAmount;function l(e,t,r){let s=a[a.length-1],o=t?1:-1,i=Math.floor(o*(e-r)/n);for(let e=1;e<=i;e++){let t=r+n*e*o;s.y.push(t)}}if(this.calculatedBoxSize=n,this.isDirtyData||0===a.length){this.pnfDataGroups.length=0;for(let t=0;t<o.length;t++){let r=s[t],i=o[t],h=o[0];if(i-h>=n){e=!0,a.push({x:r,y:[i],upTrend:e});break}if(h-i>=n){e=!1,a.push({x:r,y:[i],upTrend:e});break}}o.forEach((t,r)=>{let o=s[r],i=function(e){let t=e[e.length-1].y;return t[t.length-1]}(a);e&&(t-i>=n&&l(t,e,i),i-t>=h&&(e=!1,a.push({x:o,y:[],upTrend:e}),l(t,e,i))),!e&&(i-t>=n&&l(t,e,i),t-i>=h&&(e=!0,a.push({x:o,y:[],upTrend:e}),l(t,e,i)))})}let p=[],u=[],d=[];return a.forEach(e=>{let t=e.x,r=e.upTrend;e.y.forEach(e=>{u.push(t),d.push(e),p.push({x:t,y:e,upTrend:r})})}),t.setColumn("x",u),t.setColumn("y",d),this.pnfDataGroups=a,this.processedData=p,{modified:t,cropped:!1,cropStart:0,closestPointRange:1}}markerAttribs(e){let t=this.options,r={},s=e.pos();return r.width=this.markerWidth,r.height=this.markerHeight,s&&r.width&&r.height&&(r.x=s[0]-Math.round(r.width)/2,r.y=s[1]-Math.round(r.height)/2),t.crisp&&r.x&&(r.x=Math.floor(r.x)),r}translate(){let e=this.getColumnMetrics(),t=this.calculatedBoxSize;this.markerWidth=e.width+e.paddedWidth+e.offset,this.markerHeight=this.yAxis.toPixels(0)-this.yAxis.toPixels(t),super.translate()}}H.defaultOptions=C(x.defaultOptions,{boxSize:"1%",reversalAmount:3,tooltip:{pointFormat:'<span style="color:{point.color}">●</span> <b> {series.name}</b><br/>Close: {point.y:.2f}<br/>',headerFormat:""},turboThreshold:0,groupPadding:.2,pointPadding:.1,pointRange:null,dataGrouping:{enabled:!1},markerUp:{symbol:"cross",lineColor:"#00FF00",lineWidth:2},marker:{symbol:"circle",fillColor:"transparent",lineColor:"#FF0000",lineWidth:2},legendSymbol:"lineMarker"}),k(H.prototype,{takeOrdinalPosition:!0,pnfDataGroups:[],getColumnMetrics:b.getColumnMetrics,pointClass:c,sorted:!0}),u().registerSeriesType("pointandfigure",H);let S=l();H.compose(S.Renderer);let w=l();return a.default})());