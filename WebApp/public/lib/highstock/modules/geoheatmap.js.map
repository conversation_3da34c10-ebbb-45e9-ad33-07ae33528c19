{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/geoheatmap\n * @requires highcharts\n *\n * (c) 2009-2025\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/geoheatmap\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/geoheatmap\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ geoheatmap_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/GeoHeatmap/GeoHeatmapPoint.js\n/* *\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { map: { prototype: { pointClass: MapPoint } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\nconst { isNumber } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass GeoHeatmapPoint extends MapPoint {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * @private\n     */\n    applyOptions(options, x) {\n        const point = super.applyOptions.call(this, options, x), { lat, lon } = point.options;\n        if (isNumber(lon) && isNumber(lat)) {\n            const { colsize = 1, rowsize = 1 } = this.series.options, x1 = lon - colsize / 2, y1 = lat - rowsize / 2;\n            point.geometry = point.options.geometry = {\n                type: 'Polygon',\n                // A rectangle centered in lon/lat\n                coordinates: [\n                    [\n                        [x1, y1],\n                        [x1 + colsize, y1],\n                        [x1 + colsize, y1 + rowsize],\n                        [x1, y1 + rowsize],\n                        [x1, y1]\n                    ]\n                ]\n            };\n        }\n        return point;\n        /* eslint-enable valid-jsdoc */\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const GeoHeatmap_GeoHeatmapPoint = (GeoHeatmapPoint);\n\n;// ./code/es-modules/Series/InterpolationUtilities.js\n/* *\n *\n *  (c) 2010-2025 Hubert Kozik\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { doc } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { defined, pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Find color of point based on color axis.\n *\n * @function Highcharts.colorFromPoint\n *\n * @param {number | null} value\n *        Value to find corresponding color on the color axis.\n *\n * @param {Highcharts.Point} point\n *        Point to find it's color from color axis.\n *\n * @return {number[]}\n *        Color in RGBa array.\n */\nfunction colorFromPoint(value, point) {\n    const colorAxis = point.series.colorAxis;\n    if (colorAxis) {\n        const rgba = (colorAxis.toColor(value || 0, point)\n            .split(')')[0]\n            .split('(')[1]\n            .split(',')\n            .map((s) => pick(parseFloat(s), parseInt(s, 10))));\n        rgba[3] = pick(rgba[3], 1.0) * 255;\n        if (!defined(value) || !point.visible) {\n            rgba[3] = 0;\n        }\n        return rgba;\n    }\n    return [0, 0, 0, 0];\n}\n/**\n * Method responsible for creating a canvas for interpolation image.\n * @private\n */\nfunction getContext(series) {\n    const { canvas, context } = series;\n    if (canvas && context) {\n        context.clearRect(0, 0, canvas.width, canvas.height);\n    }\n    else {\n        series.canvas = doc.createElement('canvas');\n        series.context = series.canvas.getContext('2d', {\n            willReadFrequently: true\n        }) || void 0;\n        return series.context;\n    }\n    return context;\n}\nconst InterpolationUtilities = {\n    colorFromPoint,\n    getContext\n};\n/* harmony default export */ const Series_InterpolationUtilities = (InterpolationUtilities);\n\n;// ./code/es-modules/Series/GeoHeatmap/GeoHeatmapSeries.js\n/* *\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { animObject, stop } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nconst { noop } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { colorFromPoint: GeoHeatmapSeries_colorFromPoint, getContext: GeoHeatmapSeries_getContext } = Series_InterpolationUtilities;\n\nconst { seriesTypes: { map: MapSeries } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { addEvent, error, extend, isNumber: GeoHeatmapSeries_isNumber, isObject, merge, pick: GeoHeatmapSeries_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/**\n * Normalize longitute value to -180:180 range.\n * @private\n */\nfunction normalizeLonValue(lon) {\n    return lon - Math.floor((lon + 180) / 360) * 360;\n}\n/**\n * Get proper point's position for PixelData array.\n * @private\n */\nfunction scaledPointPos(lon, lat, canvasWidth, canvasHeight, colsize, rowsize) {\n    return Math.ceil((canvasWidth * (canvasHeight - 1 - (lat + 90) / rowsize)) +\n        ((lon + 180) / colsize));\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Geo Heatmap series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.geoheatmap\n *\n * @augments Highcharts.Series\n */\nclass GeoHeatmapSeries extends MapSeries {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        this.isDirtyCanvas = true;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * For updated colsize and rowsize options\n     * @private\n     */\n    update() {\n        const series = this;\n        series.options = merge(series.options, arguments[0]);\n        if (series.getInterpolation().enabled) {\n            series.isDirtyCanvas = true;\n            series.points.forEach((point) => {\n                if (point.graphic) {\n                    point.graphic.destroy();\n                    delete point.graphic;\n                }\n            });\n        }\n        super.update.apply(series, arguments);\n    }\n    /**\n     * Override translate method to not fire if not needed.\n     * @private\n     */\n    translate() {\n        if (this.getInterpolation().enabled &&\n            this.image &&\n            !this.isDirty &&\n            !this.isDirtyData) {\n            return;\n        }\n        super.translate.apply(this, arguments);\n    }\n    /**\n     * Create the extended object out of the boolean\n     * @private\n     */\n    getInterpolation() {\n        if (!isObject(this.options.interpolation)) {\n            return {\n                blur: 1,\n                enabled: this.options.interpolation\n            };\n        }\n        return this.options.interpolation;\n    }\n    /**\n     * Overriding drawPoints original method to apply new features.\n     * @private\n     */\n    drawPoints() {\n        const series = this, chart = series.chart, mapView = chart.mapView, seriesOptions = series.options;\n        if (series.getInterpolation().enabled && mapView && series.bounds) {\n            const ctx = series.context || GeoHeatmapSeries_getContext(series), { canvas, colorAxis, image, chart, points } = series, [colsize, rowsize] = [\n                GeoHeatmapSeries_pick(seriesOptions.colsize, 1),\n                GeoHeatmapSeries_pick(seriesOptions.rowsize, 1)\n            ], \n            // Calculate dimensions based on series bounds\n            topLeft = mapView.projectedUnitsToPixels({\n                x: series.bounds.x1,\n                y: series.bounds.y2\n            }), bottomRight = mapView.projectedUnitsToPixels({\n                x: series.bounds.x2,\n                y: series.bounds.y1\n            });\n            if (canvas && ctx && colorAxis && topLeft && bottomRight) {\n                const { x, y } = topLeft, width = bottomRight.x - x, height = bottomRight.y - y, dimensions = {\n                    x,\n                    y,\n                    width,\n                    height\n                };\n                if (\n                // Do not calculate new canvas if not necessary\n                series.isDirtyCanvas ||\n                    // Calculate new canvas if data is dirty\n                    series.isDirtyData ||\n                    // Always calculate new canvas for Orthographic projection\n                    mapView.projection.options.name === 'Orthographic') {\n                    const canvasWidth = canvas.width = ~~(360 / colsize) + 1, canvasHeight = canvas.height = ~~(180 / rowsize) + 1, canvasArea = canvasWidth * canvasHeight, pixelData = new Uint8ClampedArray(canvasArea * 4), \n                    // Guess if we have to round lon/lat with this data\n                    { lat = 0, lon = 0 } = points[0].options, unEvenLon = lon % rowsize !== 0, unEvenLat = lat % colsize !== 0, getAdjustedLon = (unEvenLon ?\n                        (lon) => (Math.round(lon / rowsize) * rowsize) :\n                        (lon) => lon), getAdjustedLat = (unEvenLat ?\n                        (lat) => (Math.round(lat / colsize) * colsize) :\n                        (lat) => lat), pointsLen = points.length;\n                    if (unEvenLon || unEvenLat) {\n                        error('Highcharts Warning: For best performance,' +\n                            ' lon/lat datapoints should spaced by a single ' +\n                            'colsize/rowsize', false, series.chart, {\n                            colsize: String(colsize),\n                            rowsize: String(rowsize)\n                        });\n                    }\n                    // Needed for tooltip\n                    series.directTouch = false;\n                    series.isDirtyCanvas = true;\n                    // First pixelData represents the geo coordinates\n                    for (let i = 0; i < pointsLen; i++) {\n                        const p = points[i], { lon, lat } = p.options;\n                        if (GeoHeatmapSeries_isNumber(lon) && GeoHeatmapSeries_isNumber(lat)) {\n                            pixelData.set(GeoHeatmapSeries_colorFromPoint(p.value, p), scaledPointPos(getAdjustedLon(lon), getAdjustedLat(lat), canvasWidth, canvasHeight, colsize, rowsize) * 4);\n                        }\n                    }\n                    const blur = series.getInterpolation().blur, blurFactor = blur === 0 ? 1 : blur * 11, upscaledWidth = ~~(canvasWidth * blurFactor), upscaledHeight = ~~(canvasHeight * blurFactor), projectedWidth = ~~width, projectedHeight = ~~height, img = new ImageData(pixelData, canvasWidth, canvasHeight);\n                    canvas.width = upscaledWidth;\n                    canvas.height = upscaledHeight;\n                    // Next step is to upscale pixelData to big image to get\n                    // the blur on the interpolation\n                    ctx.putImageData(img, 0, 0);\n                    // Now we have an unscaled version of our ImageData\n                    // let's make the compositing mode to 'copy' so that\n                    // our next drawing op erases whatever was there\n                    // previously just like putImageData would have done\n                    ctx.globalCompositeOperation = 'copy';\n                    // Now we can draw ourself over ourself\n                    ctx.drawImage(canvas, 0, 0, img.width, img.height, // Grab the ImageData\n                    0, 0, upscaledWidth, upscaledHeight // Scale it\n                    );\n                    // Add projection to upscaled ImageData\n                    const projectedPixelData = this.getProjectedImageData(mapView, projectedWidth, projectedHeight, ctx.getImageData(0, 0, upscaledWidth, upscaledHeight), canvas, x, y);\n                    canvas.width = projectedWidth;\n                    canvas.height = projectedHeight;\n                    ctx.putImageData(new ImageData(projectedPixelData, projectedWidth, projectedHeight), 0, 0);\n                }\n                if (image) {\n                    if (chart.renderer.globalAnimation && chart.hasRendered) {\n                        const startX = Number(image.attr('x')), startY = Number(image.attr('y')), startWidth = Number(image.attr('width')), startHeight = Number(image.attr('height'));\n                        const step = (now, fx) => {\n                            const pos = fx.pos;\n                            image.attr({\n                                x: (startX + (x - startX) * pos),\n                                y: (startY + (y - startY) * pos),\n                                width: (startWidth + (width - startWidth) * pos),\n                                height: (startHeight + (height - startHeight) * pos)\n                            });\n                        };\n                        const animOptions = merge(animObject(chart.renderer.globalAnimation)), userStep = animOptions.step;\n                        animOptions.step =\n                            function () {\n                                if (userStep) {\n                                    userStep.apply(this, arguments);\n                                }\n                                step.apply(this, arguments);\n                            };\n                        image\n                            .attr(merge({ animator: 0 }, series.isDirtyCanvas ? {\n                            href: canvas.toDataURL('image/png', 1)\n                        } : void 0))\n                            .animate({ animator: 1 }, animOptions);\n                        // When dragging or first rendering, animation is off\n                    }\n                    else {\n                        stop(image);\n                        image.attr(merge(dimensions, series.isDirtyCanvas ? {\n                            href: canvas.toDataURL('image/png', 1)\n                        } : void 0));\n                    }\n                }\n                else {\n                    series.image = chart.renderer.image(canvas.toDataURL('image/png', 1))\n                        .attr(dimensions)\n                        .add(series.group);\n                }\n                series.isDirtyCanvas = false;\n            }\n        }\n        else {\n            super.drawPoints.apply(series, arguments);\n        }\n    }\n    /**\n     * Project ImageData to actual mapView projection used on a chart.\n     * @private\n     */\n    getProjectedImageData(mapView, projectedWidth, projectedHeight, cartesianImageData, canvas, horizontalShift, verticalShift) {\n        const projectedPixelData = new Uint8ClampedArray(projectedWidth * projectedHeight * 4), lambda = GeoHeatmapSeries_pick(mapView.projection.options.rotation?.[0], 0), widthFactor = canvas.width / 360, heightFactor = -1 * canvas.height / 180;\n        let y = -1;\n        // For each pixel on the map plane, find the map\n        // coordinate and get the color value\n        for (let i = 0; i < projectedPixelData.length; i += 4) {\n            const x = (i / 4) % projectedWidth;\n            if (x === 0) {\n                y++;\n            }\n            const projectedCoords = mapView.pixelsToLonLat({\n                x: horizontalShift + x,\n                y: verticalShift + y\n            });\n            if (projectedCoords) {\n                // Normalize lon values\n                if (projectedCoords.lon > -180 - lambda &&\n                    projectedCoords.lon < 180 - lambda) {\n                    projectedCoords.lon =\n                        normalizeLonValue(projectedCoords.lon);\n                }\n                const projected = [\n                    projectedCoords.lon,\n                    projectedCoords.lat\n                ], cvs2PixelX = projected[0] * widthFactor + canvas.width / 2, cvs2PixelY = projected[1] * heightFactor +\n                    canvas.height / 2;\n                if (cvs2PixelX >= 0 &&\n                    cvs2PixelX <= canvas.width &&\n                    cvs2PixelY >= 0 &&\n                    cvs2PixelY <= canvas.height) {\n                    const redPos = (\n                    // Rows\n                    Math.floor(cvs2PixelY) *\n                        canvas.width * 4 +\n                        // Columns\n                        Math.round(cvs2PixelX) * 4);\n                    projectedPixelData[i] =\n                        cartesianImageData.data[redPos];\n                    projectedPixelData[i + 1] =\n                        cartesianImageData.data[redPos + 1];\n                    projectedPixelData[i + 2] =\n                        cartesianImageData.data[redPos + 2];\n                    projectedPixelData[i + 3] =\n                        cartesianImageData.data[redPos + 3];\n                }\n            }\n        }\n        return projectedPixelData;\n    }\n    searchPoint(e, compareX) {\n        const series = this, chart = this.chart, mapView = chart.mapView;\n        if (mapView &&\n            series.bounds &&\n            series.image &&\n            chart.tooltip &&\n            chart.tooltip.options.enabled) {\n            if (\n            // If user drags map do not build k-d-tree\n            !chart.pointer.hasDragged &&\n                // If user zooms in/out map do not build k-d-tree\n                (+series.image.attr('animator') <= 0.01 ||\n                    +series.image.attr('animator') >= 0.99)) {\n                const topLeft = mapView.projectedUnitsToPixels({\n                    x: series.bounds.x1,\n                    y: series.bounds.y2\n                }), bottomRight = mapView.projectedUnitsToPixels({\n                    x: series.bounds.x2,\n                    y: series.bounds.y1\n                });\n                chart.pointer.normalize(e);\n                if (e.lon && e.lat &&\n                    topLeft && bottomRight &&\n                    e.chartX - chart.plotLeft > topLeft.x &&\n                    e.chartX - chart.plotLeft < bottomRight.x &&\n                    e.chartY - chart.plotTop > topLeft.y &&\n                    e.chartY - chart.plotTop < bottomRight.y) {\n                    return this.searchKDTree({\n                        clientX: e.chartX,\n                        lon: normalizeLonValue(e.lon),\n                        lat: e.lat\n                    }, compareX, e);\n                }\n            }\n            else {\n                chart.tooltip.destroy();\n            }\n        }\n    }\n}\n/**\n * A `geoheatmap` series is a variety of heatmap series, composed into\n * the map projection, where the units are expressed in the latitude\n * and longitude, and individual values contained in a matrix are\n * represented as colors.\n *\n * @sample maps/demo/geoheatmap-europe/\n *         GeoHeatmap Chart with interpolation on Europe map\n * @sample maps/series-geoheatmap/geoheatmap-equalearth/\n *         GeoHeatmap Chart on the Equal Earth Projection\n *\n * @extends      plotOptions.map\n * @since        11.0.0\n * @product      highmaps\n * @excluding    allAreas, dragDrop, findNearestPointBy, geometry, joinBy,\n * negativeColor, onPoint, stickyTracking\n * @requires     modules/geoheatmap\n * @optionparent plotOptions.geoheatmap\n */\nGeoHeatmapSeries.defaultOptions = merge(MapSeries.defaultOptions, {\n    nullColor: 'transparent',\n    tooltip: {\n        pointFormat: 'Lat: {point.lat}, Lon: {point.lon}, Value: {point.value}<br/>'\n    },\n    /**\n     * The border width of each geoheatmap tile.\n     *\n     * In styled mode, the border stroke width is given in the\n     * `.highcharts-point` class.\n     *\n     * @sample maps/demo/geoheatmap-orthographic/\n     *         borderWidth set to 1 to create a grid\n     *\n     * @type      {number|null}\n     * @default   0\n     * @product   highmaps\n     * @apioption plotOptions.geoheatmap.borderWidth\n     */\n    borderWidth: 0,\n    /**\n     * The column size - how many longitude units each column in the\n     * geoheatmap should span.\n     *\n     * @sample maps/demo/geoheatmap-europe/\n     *         1 by default, set to 5\n     *\n     * @product   highmaps\n     * @apioption plotOptions.geoheatmap.colsize\n     */\n    colsize: 1,\n    /**\n     * The main color of the series. In heat maps this color is rarely\n     * used, as we mostly use the color to denote the value of each\n     * point. Unless options are set in the [colorAxis](#colorAxis), the\n     * default value is pulled from the [options.colors](#colors) array.\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @product   highmaps\n     * @apioption plotOptions.geoheatmap.color\n     */\n    /**\n     * The rowsize size - how many latitude units each row in the\n     * geoheatmap should span.\n     *\n     * @sample maps/demo/geoheatmap-europe/\n     *         1 by default, set to 5\n     *\n     * @product   highmaps\n     * @apioption plotOptions.geoheatmap.rowsize\n     */\n    rowsize: 1,\n    stickyTracking: true,\n    /**\n     * Make the geoheatmap render its data points as an interpolated\n     * image. It can be used to show a Temperature Map-like charts.\n     *\n     * @sample maps/demo/geoheatmap-earth-statistics\n     *         Advanced demo of GeoHeatmap interpolation with multiple\n     *         datasets\n     *\n     * @type      {boolean|Highcharts.InterpolationOptionsObject}\n     * @since     11.2.0\n     * @product   highmaps\n     */\n    interpolation: {\n        /**\n         * Enable or disable the interpolation of the geoheatmap series.\n         *\n         * @since 11.2.0\n         */\n        enabled: false,\n        /**\n         * Represents how much blur should be added to the interpolated\n         * image. Works best in the range of 0-1, all higher values\n         * would need a lot more performance of the machine to calculate\n         * more detailed interpolation.\n         *\n         *  * **Note:** Useful, if the data is spread into wide range of\n         *  longitude and latitude values.\n         *\n         * @sample maps/series-geoheatmap/turkey-fire-areas\n         *         Simple demo of GeoHeatmap interpolation\n         *\n         * @since  11.2.0\n         */\n        blur: 1\n    }\n});\naddEvent(GeoHeatmapSeries, 'afterDataClassLegendClick', function () {\n    this.isDirtyCanvas = true;\n    this.drawPoints();\n});\nextend(GeoHeatmapSeries.prototype, {\n    type: 'geoheatmap',\n    applyJitter: noop,\n    pointClass: GeoHeatmap_GeoHeatmapPoint,\n    pointArrayMap: ['lon', 'lat', 'value'],\n    kdAxisArray: ['lon', 'lat'] // Search k-d-tree by lon/lat values\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('geoheatmap', GeoHeatmapSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const GeoHeatmap_GeoHeatmapSeries = ((/* unused pure expression or super */ null && (GeoHeatmapSeries)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `geoheatmap` series. If the [type](#series.map.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.geoheatmap\n * @excluding allAreas, dataParser, dataURL, dragDrop, findNearestPointBy,\n *            joinBy, marker, mapData, negativeColor, onPoint, shadow,\n *            stickyTracking\n * @product   highmaps\n * @apioption series.geoheatmap\n */\n/**\n * An array of data points for the series. For the `geoheatmap` series\n * type, points can be given in the following ways:\n *\n * 1.  An array of arrays with 3 or 2 values. In this case, the values\n * correspond to `lon,lat,value`. The `value` refers to the color on the `colorAxis`.\n *\n *  ```js\n *     data: [\n *         [51.50, -0.12, 7],\n *         [54.59, -5.93, 4],\n *         [55.8, -4.25, 3]\n *     ]\n *  ```\n *\n * 2.  An array of objects with named values. The following snippet shows only a\n * few settings, see the complete options set below. If the total number of data\n * points exceeds the series' [turboThreshold](#series.heatmap.turboThreshold),\n * this option is not available.\n *\n *  ```js\n *     data: [{\n *         lat: 51.50,\n *         lon: -0.12,\n *         value: 7,\n *         name: \"London\"\n *     }, {\n *         lat: 54.59,\n *         lon: -5.93,\n *         value: 4,\n *         name: \"Belfast\"\n *     }]\n *  ```\n *\n * @sample maps/demo/geoheatmap-europe/\n *         GeoHeatmap Chart with interpolation on Europe map\n * @sample maps/series-geoheatmap/geoheatmap-equalearth/\n *         GeoHeatmap Chart on the Equal Earth Projection\n *\n * @type      {Array<Array<number>|*>}\n * @extends   series.map.data\n * @product   highmaps\n * @apioption series.geoheatmap.data\n */\n/**\n * Individual color for the point. By default the color is either used\n * to denote the value, or pulled from the global `colors` array.\n *\n * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n * @product   highmaps\n * @apioption series.geoheatmap.data.color\n */\n/**\n * The value of the point, resulting in a color controlled by options\n * as set in the [colorAxis](#colorAxis) configuration.\n *\n * @type      {number|null}\n * @product   highmaps\n * @apioption series.geoheatmap.data.value\n */\n/**\n * Detailed options for interpolation object.\n *\n * @interface Highcharts.InterpolationOptionsObject\n */ /**\n*  Enable or disable the interpolation.\n*\n* @name Highcharts.InterpolationOptionsObject#enabled\n* @type {boolean}\n*/ /**\n* Represents how much blur should be added to the interpolated\n* image. Works best in the range of 0-1, all higher values\n* would need a lot more performance of the machine to calculate\n* more detailed interpolation.\n*\n* @name Highcharts.InterpolationOptionsObject#blur\n* @type {number}\n*/\n''; // Adds doclets above to the transpiled file\n\n;// ./code/es-modules/masters/modules/geoheatmap.js\n\n\n\n\n/* harmony default export */ const geoheatmap_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "geoheatmap_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "map", "pointClass", "MapPoint", "seriesTypes", "isNumber", "GeoHeatmap_GeoHeatmapPoint", "applyOptions", "options", "x", "point", "lat", "lon", "colsize", "rowsize", "series", "x1", "y1", "geometry", "type", "coordinates", "doc", "defined", "pick", "animObject", "stop", "noop", "colorFromPoint", "GeoHeatmapSeries_colorFromPoint", "getContext", "GeoHeatmapSeries_getContext", "value", "colorAxis", "rgba", "toColor", "split", "s", "parseFloat", "parseInt", "visible", "canvas", "context", "clearRect", "width", "height", "createElement", "willReadFrequently", "MapSeries", "addEvent", "error", "extend", "GeoHeatmapSeries_isNumber", "isObject", "merge", "GeoHeatmapSeries_pick", "normalizeLonValue", "Math", "floor", "GeoHeatmapSeries", "constructor", "arguments", "isDirtyCanvas", "update", "getInterpolation", "enabled", "points", "for<PERSON>ach", "graphic", "destroy", "apply", "translate", "image", "isDirty", "isDirtyData", "interpolation", "blur", "drawPoints", "mapView", "chart", "seriesOptions", "bounds", "ctx", "topLeft", "projectedUnitsToPixels", "y", "y2", "bottomRight", "x2", "dimensions", "projection", "name", "canvasWidth", "canvasHeight", "pixelData", "Uint8ClampedArray", "canvasArea", "unEvenLon", "unEvenLat", "getAdjustedLon", "round", "getAdjustedLat", "pointsLen", "length", "String", "directTouch", "i", "p", "set", "scaledPointPos", "ceil", "blurFactor", "upscaledWidth", "upscaledHeight", "projectedWidth", "projectedHeight", "img", "ImageData", "putImageData", "globalCompositeOperation", "drawImage", "projectedPixelData", "getProjectedImageData", "getImageData", "renderer", "globalAnimation", "hasRendered", "startX", "Number", "attr", "startY", "startWidth", "startHeight", "step", "now", "fx", "pos", "animOptions", "userStep", "animator", "href", "toDataURL", "animate", "add", "group", "cartesianImageData", "horizontalShift", "verticalShift", "lambda", "rotation", "widthFactor", "heightFactor", "projectedCoords", "pixelsToLonLat", "projected", "cvs2PixelX", "cvs2PixelY", "redPos", "data", "searchPoint", "e", "compareX", "tooltip", "pointer", "hasDragged", "normalize", "chartX", "plotLeft", "chartY", "plotTop", "searchKDTree", "clientX", "defaultOptions", "nullColor", "pointFormat", "borderWidth", "stickyTracking", "applyJitter", "pointArrayMap", "kdAxisArray", "registerSeriesType"], "mappings": "CASA,AATA;;;;;;;;CAQC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,gCAAiC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACzH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,gCAAgC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAE7GA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAgBjL,GAAM,CAAEE,IAAK,CAAET,UAAW,CAAEU,WAAYC,CAAQ,CAAE,CAAE,CAAE,CAAG,AAACH,IAA2II,WAAW,CAC1M,CAAEC,SAAAA,CAAQ,CAAE,CAAIP,IA2CaQ,EArCnC,cAA8BH,EAU1BI,aAAaC,CAAO,CAAEC,CAAC,CAAE,CACrB,IAAMC,EAAQ,KAAK,CAACH,aAAab,IAAI,CAAC,IAAI,CAAEc,EAASC,GAAI,CAAEE,IAAAA,CAAG,CAAEC,IAAAA,CAAG,CAAE,CAAGF,EAAMF,OAAO,CACrF,GAAIH,EAASO,IAAQP,EAASM,GAAM,CAChC,GAAM,CAAEE,QAAAA,EAAU,CAAC,CAAEC,QAAAA,EAAU,CAAC,CAAE,CAAG,IAAI,CAACC,MAAM,CAACP,OAAO,CAAEQ,EAAKJ,EAAMC,EAAU,EAAGI,EAAKN,EAAMG,EAAU,CACvGJ,CAAAA,EAAMQ,QAAQ,CAAGR,EAAMF,OAAO,CAACU,QAAQ,CAAG,CACtCC,KAAM,UAENC,YAAa,CACT,CACI,CAACJ,EAAIC,EAAG,CACR,CAACD,EAAKH,EAASI,EAAG,CAClB,CAACD,EAAKH,EAASI,EAAKH,EAAQ,CAC5B,CAACE,EAAIC,EAAKH,EAAQ,CAClB,CAACE,EAAIC,EAAG,CACX,CACJ,AACL,CACJ,CACA,OAAOP,CAEX,CACJ,EAoBM,CAAEW,IAAAA,CAAG,CAAE,CAAIvB,IAEX,CAAEwB,QAAAA,CAAO,CAAEC,KAAAA,CAAI,CAAE,CAAIzB,IA0ErB,CAAE0B,WAAAA,CAAU,CAAEC,KAAAA,CAAI,CAAE,CAAI3B,IAGxB,CAAE4B,KAAAA,CAAI,CAAE,CAAI5B,IAEZ,CAAE6B,eAAgBC,CAA+B,CAAEC,WAAYC,CAA2B,CAAE,CAzBnE,CAC3BH,eAnCJ,SAAwBI,CAAK,CAAErB,CAAK,EAChC,IAAMsB,EAAYtB,EAAMK,MAAM,CAACiB,SAAS,CACxC,GAAIA,EAAW,CACX,IAAMC,EAAQD,EAAUE,OAAO,CAACH,GAAS,EAAGrB,GACvCyB,KAAK,CAAC,IAAI,CAAC,EAAE,CACbA,KAAK,CAAC,IAAI,CAAC,EAAE,CACbA,KAAK,CAAC,KACNlC,GAAG,CAAC,AAACmC,GAAMb,EAAKc,WAAWD,GAAIE,SAASF,EAAG,MAKhD,OAJAH,CAAI,CAAC,EAAE,CAAGV,AAAqB,IAArBA,EAAKU,CAAI,CAAC,EAAE,CAAE,GACpB,AAACX,EAAQS,IAAWrB,EAAM6B,OAAO,EACjCN,CAAAA,CAAI,CAAC,EAAE,CAAG,CAAA,EAEPA,CACX,CACA,MAAO,CAAC,EAAG,EAAG,EAAG,EAAE,AACvB,EAqBIJ,WAhBJ,SAAoBd,CAAM,EACtB,GAAM,CAAEyB,OAAAA,CAAM,CAAEC,QAAAA,CAAO,CAAE,CAAG1B,SAC5B,AAAIyB,GAAUC,GACVA,EAAQC,SAAS,CAAC,EAAG,EAAGF,EAAOG,KAAK,CAAEH,EAAOI,MAAM,EAShDH,IANH1B,EAAOyB,MAAM,CAAGnB,EAAIwB,aAAa,CAAC,UAClC9B,EAAO0B,OAAO,CAAG1B,EAAOyB,MAAM,CAACX,UAAU,CAAC,KAAM,CAC5CiB,mBAAoB,CAAA,CACxB,IAAM,KAAK,EACJ/B,EAAO0B,OAAO,CAG7B,CAIA,EAwBM,CAAErC,YAAa,CAAEH,IAAK8C,CAAS,CAAE,CAAE,CAAI/C,IAEvC,CAAEgD,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAE7C,SAAU8C,CAAyB,CAAEC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAE9B,KAAM+B,CAAqB,CAAE,CAAIxD,IAKxH,SAASyD,EAAkB3C,CAAG,EAC1B,OAAOA,EAAM4C,AAAgC,IAAhCA,KAAKC,KAAK,CAAC,AAAC7C,CAAAA,EAAM,GAAE,EAAK,IAC1C,CAuBA,MAAM8C,UAAyBX,EAC3BY,aAAc,CAMV,KAAK,IAAIC,WACT,IAAI,CAACC,aAAa,CAAG,CAAA,CACzB,CAWAC,QAAS,CAEL/C,AADe,IAAI,CACZP,OAAO,CAAG6C,EAAMtC,AADR,IAAI,CACWP,OAAO,CAAEoD,SAAS,CAAC,EAAE,EAC/C7C,AAFW,IAAI,CAERgD,gBAAgB,GAAGC,OAAO,GACjCjD,AAHW,IAAI,CAGR8C,aAAa,CAAG,CAAA,EACvB9C,AAJW,IAAI,CAIRkD,MAAM,CAACC,OAAO,CAAC,AAACxD,IACfA,EAAMyD,OAAO,GACbzD,EAAMyD,OAAO,CAACC,OAAO,GACrB,OAAO1D,EAAMyD,OAAO,CAE5B,IAEJ,KAAK,CAACL,OAAOO,KAAK,CAXH,IAAI,CAWQT,UAC/B,CAKAU,WAAY,CACJ,CAAA,CAAA,IAAI,CAACP,gBAAgB,GAAGC,OAAO,GAC/B,IAAI,CAACO,KAAK,EACT,IAAI,CAACC,OAAO,EACZ,IAAI,CAACC,WAAW,AAAD,GAGpB,KAAK,CAACH,UAAUD,KAAK,CAAC,IAAI,CAAET,UAChC,CAKAG,kBAAmB,QACf,AAAKX,EAAS,IAAI,CAAC5C,OAAO,CAACkE,aAAa,EAMjC,IAAI,CAAClE,OAAO,CAACkE,aAAa,CALtB,CACHC,KAAM,EACNX,QAAS,IAAI,CAACxD,OAAO,CAACkE,aAAa,AACvC,CAGR,CAKAE,YAAa,CACT,IAA2CC,EAAUC,AAAxB/D,AAAd,IAAI,CAAiB+D,KAAK,CAAkBD,OAAO,CAAEE,EAAgBhE,AAArE,IAAI,CAAwEP,OAAO,CAClG,GAAIO,AADW,IAAI,CACRgD,gBAAgB,GAAGC,OAAO,EAAIa,GAAW9D,AADrC,IAAI,CACwCiE,MAAM,CAAE,CAC/D,IAAMC,EAAMlE,AAFD,IAAI,CAEI0B,OAAO,EAAIX,EAFnB,IAAI,EAEoD,CAAEU,OAAAA,CAAM,CAAER,UAAAA,CAAS,CAAEuC,MAAAA,CAAK,CAAEO,MAAAA,CAAK,CAAEb,OAAAA,CAAM,CAAE,CAFnG,IAAI,CAE0G,CAACpD,EAASC,EAAQ,CAAG,CAC1IwC,EAAsByB,EAAclE,OAAO,CAAE,GAC7CyC,EAAsByB,EAAcjE,OAAO,CAAE,GAChD,CAEDoE,EAAUL,EAAQM,sBAAsB,CAAC,CACrC1E,EAAGM,AARI,IAAI,CAQDiE,MAAM,CAAChE,EAAE,CACnBoE,EAAGrE,AATI,IAAI,CASDiE,MAAM,CAACK,EAAE,AACvB,GAAIC,EAAcT,EAAQM,sBAAsB,CAAC,CAC7C1E,EAAGM,AAXI,IAAI,CAWDiE,MAAM,CAACO,EAAE,CACnBH,EAAGrE,AAZI,IAAI,CAYDiE,MAAM,CAAC/D,EAAE,AACvB,GACA,GAAIuB,GAAUyC,GAAOjD,GAAakD,GAAWI,EAAa,CACtD,GAAM,CAAE7E,EAAAA,CAAC,CAAE2E,EAAAA,CAAC,CAAE,CAAGF,EAASvC,EAAQ2C,EAAY7E,CAAC,CAAGA,EAAGmC,EAAS0C,EAAYF,CAAC,CAAGA,EAAGI,EAAa,CAC1F/E,EAAAA,EACA2E,EAAAA,EACAzC,MAAAA,EACAC,OAAAA,CACJ,EACA,GAEA7B,AAvBO,IAAI,CAuBJ8C,aAAa,EAEhB9C,AAzBG,IAAI,CAyBA0D,WAAW,EAElBI,AAAoC,iBAApCA,EAAQY,UAAU,CAACjF,OAAO,CAACkF,IAAI,CAAqB,CACpD,IAAMC,EAAcnD,EAAOG,KAAK,CAAG,CAAC,CAAE,CAAA,IAAM9B,CAAM,EAAK,EAAG+E,EAAepD,EAAOI,MAAM,CAAG,CAAC,CAAE,CAAA,IAAM9B,CAAM,EAAK,EAA4C+E,EAAY,IAAIC,kBAAkBC,AAA9DJ,EAAcC,EAA6D,GAExM,CAAEjF,IAAAA,EAAM,CAAC,CAAEC,IAAAA,EAAM,CAAC,CAAE,CAAGqD,CAAM,CAAC,EAAE,CAACzD,OAAO,CAAEwF,EAAYpF,EAAME,GAAY,EAAGmF,EAAYtF,EAAME,GAAY,EAAGqF,EAAkBF,EAC1H,AAACpF,GAAS4C,KAAK2C,KAAK,CAACvF,EAAME,GAAWA,EACtC,AAACF,GAAQA,EAAMwF,EAAkBH,EACjC,AAACtF,GAAS6C,KAAK2C,KAAK,CAACxF,EAAME,GAAWA,EACtC,AAACF,GAAQA,EAAM0F,EAAYpC,EAAOqC,MAAM,AACxCN,CAAAA,CAAAA,GAAaC,CAAQ,GACrBhD,EAAM,yGAEiB,CAAA,EAAOlC,AAtC/B,IAAI,CAsCkC+D,KAAK,CAAE,CACxCjE,QAAS0F,OAAO1F,GAChBC,QAASyF,OAAOzF,EACpB,GAGJC,AA5CG,IAAI,CA4CAyF,WAAW,CAAG,CAAA,EACrBzF,AA7CG,IAAI,CA6CA8C,aAAa,CAAG,CAAA,EAEvB,IAAK,IAAI4C,EAAI,EAAGA,EAAIJ,EAAWI,IAAK,KAlIhC7F,EAmIA,IAAM8F,EAAIzC,CAAM,CAACwC,EAAE,CAAE,CAAE7F,IAAAA,CAAG,CAAED,IAAAA,CAAG,CAAE,CAAG+F,EAAElG,OAAO,AACzC2C,CAAAA,EAA0BvC,IAAQuC,EAA0BxC,IAC5DkF,EAAUc,GAAG,CAAC/E,EAAgC8E,EAAE3E,KAAK,CAAE2E,GAAIE,AAAwG,GArIvKhG,EAqI8EsF,EAAetF,GApI1G4C,KAAKqD,IAAI,CAAC,AAoI2HlB,EApI3GC,CAAAA,AAoIwHA,EApIzG,EAAI,AAACjF,CAAAA,AAoIkEyF,EAAezF,GApI3E,EAAC,EAoIoHG,CApIzG,EAClE,AAACF,CAAAA,EAAM,GAAE,EAmIyJC,IAEvJ,CACA,IAAM8D,EAAO5D,AArDV,IAAI,CAqDagD,gBAAgB,GAAGY,IAAI,CAAEmC,EAAanC,AAAS,IAATA,EAAa,EAAIA,AAAO,GAAPA,EAAWoC,EAAgB,CAAC,CAAEpB,CAAAA,EAAcmB,CAAS,EAAIE,EAAiB,CAAC,CAAEpB,CAAAA,EAAekB,CAAS,EAAIG,EAAiB,CAAC,CAACtE,EAAOuE,EAAkB,CAAC,CAACtE,EAAQuE,EAAM,IAAIC,UAAUvB,EAAWF,EAAaC,EACtRpD,CAAAA,EAAOG,KAAK,CAAGoE,EACfvE,EAAOI,MAAM,CAAGoE,EAGhB/B,EAAIoC,YAAY,CAACF,EAAK,EAAG,GAKzBlC,EAAIqC,wBAAwB,CAAG,OAE/BrC,EAAIsC,SAAS,CAAC/E,EAAQ,EAAG,EAAG2E,EAAIxE,KAAK,CAAEwE,EAAIvE,MAAM,CACjD,EAAG,EAAGmE,EAAeC,GAGrB,IAAMQ,EAAqB,IAAI,CAACC,qBAAqB,CAAC5C,EAASoC,EAAgBC,EAAiBjC,EAAIyC,YAAY,CAAC,EAAG,EAAGX,EAAeC,GAAiBxE,EAAQ/B,EAAG2E,EAClK5C,CAAAA,EAAOG,KAAK,CAAGsE,EACfzE,EAAOI,MAAM,CAAGsE,EAChBjC,EAAIoC,YAAY,CAAC,IAAID,UAAUI,EAAoBP,EAAgBC,GAAkB,EAAG,EAC5F,CACA,GAAI3C,EACA,GAAIO,EAAM6C,QAAQ,CAACC,eAAe,EAAI9C,EAAM+C,WAAW,CAAE,CACrD,IAAMC,EAASC,OAAOxD,EAAMyD,IAAI,CAAC,MAAOC,EAASF,OAAOxD,EAAMyD,IAAI,CAAC,MAAOE,EAAaH,OAAOxD,EAAMyD,IAAI,CAAC,UAAWG,EAAcJ,OAAOxD,EAAMyD,IAAI,CAAC,WAC9II,EAAO,CAACC,EAAKC,KACf,IAAMC,EAAMD,EAAGC,GAAG,CAClBhE,EAAMyD,IAAI,CAAC,CACPvH,EAAIqH,EAAS,AAACrH,CAAAA,EAAIqH,CAAK,EAAKS,EAC5BnD,EAAI6C,EAAS,AAAC7C,CAAAA,EAAI6C,CAAK,EAAKM,EAC5B5F,MAAQuF,EAAa,AAACvF,CAAAA,EAAQuF,CAAS,EAAKK,EAC5C3F,OAASuF,EAAc,AAACvF,CAAAA,EAASuF,CAAU,EAAKI,CACpD,EACJ,EACMC,EAAcnF,EAAM7B,EAAWsD,EAAM6C,QAAQ,CAACC,eAAe,GAAIa,EAAWD,EAAYJ,IAAI,AAClGI,CAAAA,EAAYJ,IAAI,CACZ,WACQK,GACAA,EAASpE,KAAK,CAAC,IAAI,CAAET,WAEzBwE,EAAK/D,KAAK,CAAC,IAAI,CAAET,UACrB,EACJW,EACKyD,IAAI,CAAC3E,EAAM,CAAEqF,SAAU,CAAE,EAAG3H,AA/FlC,IAAI,CA+FqC8C,aAAa,CAAG,CACpD8E,KAAMnG,EAAOoG,SAAS,CAAC,YAAa,EACxC,EAAI,KAAK,IACJC,OAAO,CAAC,CAAEH,SAAU,CAAE,EAAGF,EAElC,MAEI/G,EAAK8C,GACLA,EAAMyD,IAAI,CAAC3E,EAAMmC,EAAYzE,AAvG9B,IAAI,CAuGiC8C,aAAa,CAAG,CAChD8E,KAAMnG,EAAOoG,SAAS,CAAC,YAAa,EACxC,EAAI,KAAK,SAIb7H,AA7GG,IAAI,CA6GAwD,KAAK,CAAGO,EAAM6C,QAAQ,CAACpD,KAAK,CAAC/B,EAAOoG,SAAS,CAAC,YAAa,IAC7DZ,IAAI,CAACxC,GACLsD,GAAG,CAAC/H,AA/GN,IAAI,CA+GSgI,KAAK,CAEzBhI,CAjHO,IAAI,CAiHJ8C,aAAa,CAAG,CAAA,CAC3B,CACJ,MAEI,KAAK,CAACe,WAAWP,KAAK,CArHX,IAAI,CAqHgBT,UAEvC,CAKA6D,sBAAsB5C,CAAO,CAAEoC,CAAc,CAAEC,CAAe,CAAE8B,CAAkB,CAAExG,CAAM,CAAEyG,CAAe,CAAEC,CAAa,CAAE,CACxH,IAAM1B,EAAqB,IAAI1B,kBAAkBmB,EAAiBC,EAAkB,GAAIiC,EAAS7F,EAAsBuB,EAAQY,UAAU,CAACjF,OAAO,CAAC4I,QAAQ,EAAE,CAAC,EAAE,CAAE,GAAIC,EAAc7G,EAAOG,KAAK,CAAG,IAAK2G,EAAe,GAAK9G,EAAOI,MAAM,CAAG,IACvOwC,EAAI,GAGR,IAAK,IAAIqB,EAAI,EAAGA,EAAIe,EAAmBlB,MAAM,CAAEG,GAAK,EAAG,CACnD,IAAMhG,EAAI,AAACgG,EAAI,EAAKQ,CAChBxG,AAAM,CAAA,IAANA,GACA2E,IAEJ,IAAMmE,EAAkB1E,EAAQ2E,cAAc,CAAC,CAC3C/I,EAAGwI,EAAkBxI,EACrB2E,EAAG8D,EAAgB9D,CACvB,GACA,GAAImE,EAAiB,CAEbA,EAAgB3I,GAAG,CAAG,KAAOuI,GAC7BI,EAAgB3I,GAAG,CAAG,IAAMuI,GAC5BI,CAAAA,EAAgB3I,GAAG,CACf2C,EAAkBgG,EAAgB3I,GAAG,CAAA,EAE7C,IAAM6I,EAAY,CACdF,EAAgB3I,GAAG,CACnB2I,EAAgB5I,GAAG,CACtB,CAAE+I,EAAaD,CAAS,CAAC,EAAE,CAAGJ,EAAc7G,EAAOG,KAAK,CAAG,EAAGgH,EAAaF,CAAS,CAAC,EAAE,CAAGH,EACvF9G,EAAOI,MAAM,CAAG,EACpB,GAAI8G,GAAc,GACdA,GAAclH,EAAOG,KAAK,EAC1BgH,GAAc,GACdA,GAAcnH,EAAOI,MAAM,CAAE,CAC7B,IAAMgH,EAENpG,KAAKC,KAAK,CAACkG,GACPnH,EAAOG,KAAK,CAAG,EAEfa,AAAyB,EAAzBA,KAAK2C,KAAK,CAACuD,EACflC,CAAAA,CAAkB,CAACf,EAAE,CACjBuC,EAAmBa,IAAI,CAACD,EAAO,CACnCpC,CAAkB,CAACf,EAAI,EAAE,CACrBuC,EAAmBa,IAAI,CAACD,EAAS,EAAE,CACvCpC,CAAkB,CAACf,EAAI,EAAE,CACrBuC,EAAmBa,IAAI,CAACD,EAAS,EAAE,CACvCpC,CAAkB,CAACf,EAAI,EAAE,CACrBuC,EAAmBa,IAAI,CAACD,EAAS,EAAE,AAC3C,CACJ,CACJ,CACA,OAAOpC,CACX,CACAsC,YAAYC,CAAC,CAAEC,CAAQ,CAAE,CACrB,IAAqBlF,EAAQ,IAAI,CAACA,KAAK,CAAED,EAAUC,EAAMD,OAAO,CAChE,GAAIA,GACA9D,AAFW,IAAI,CAERiE,MAAM,EACbjE,AAHW,IAAI,CAGRwD,KAAK,EACZO,EAAMmF,OAAO,EACbnF,EAAMmF,OAAO,CAACzJ,OAAO,CAACwD,OAAO,CAC7B,GAEA,CAACc,EAAMoF,OAAO,CAACC,UAAU,EAEpB,CAAA,AAAkC,KAAlC,CAACpJ,AAVK,IAAI,CAUFwD,KAAK,CAACyD,IAAI,CAAC,aAChB,CAACjH,AAXE,IAAI,CAWCwD,KAAK,CAACyD,IAAI,CAAC,aAAe,GAAG,EAAI,CAC7C,IAAM9C,EAAUL,EAAQM,sBAAsB,CAAC,CAC3C1E,EAAGM,AAbA,IAAI,CAaGiE,MAAM,CAAChE,EAAE,CACnBoE,EAAGrE,AAdA,IAAI,CAcGiE,MAAM,CAACK,EAAE,AACvB,GAAIC,EAAcT,EAAQM,sBAAsB,CAAC,CAC7C1E,EAAGM,AAhBA,IAAI,CAgBGiE,MAAM,CAACO,EAAE,CACnBH,EAAGrE,AAjBA,IAAI,CAiBGiE,MAAM,CAAC/D,EAAE,AACvB,GAEA,GADA6D,EAAMoF,OAAO,CAACE,SAAS,CAACL,GACpBA,EAAEnJ,GAAG,EAAImJ,EAAEpJ,GAAG,EACduE,GAAWI,GACXyE,EAAEM,MAAM,CAAGvF,EAAMwF,QAAQ,CAAGpF,EAAQzE,CAAC,EACrCsJ,EAAEM,MAAM,CAAGvF,EAAMwF,QAAQ,CAAGhF,EAAY7E,CAAC,EACzCsJ,EAAEQ,MAAM,CAAGzF,EAAM0F,OAAO,CAAGtF,EAAQE,CAAC,EACpC2E,EAAEQ,MAAM,CAAGzF,EAAM0F,OAAO,CAAGlF,EAAYF,CAAC,CACxC,OAAO,IAAI,CAACqF,YAAY,CAAC,CACrBC,QAASX,EAAEM,MAAM,CACjBzJ,IAAK2C,EAAkBwG,EAAEnJ,GAAG,EAC5BD,IAAKoJ,EAAEpJ,GAAG,AACd,EAAGqJ,EAAUD,EAErB,MAEIjF,EAAMmF,OAAO,CAAC7F,OAAO,EAGjC,CACJ,CAoBAV,EAAiBiH,cAAc,CAAGtH,EAAMN,EAAU4H,cAAc,CAAE,CAC9DC,UAAW,cACXX,QAAS,CACLY,YAAa,+DACjB,EAeAC,YAAa,EAWbjK,QAAS,EAqBTC,QAAS,EACTiK,eAAgB,CAAA,EAahBrG,cAAe,CAMXV,QAAS,CAAA,EAeTW,KAAM,CACV,CACJ,GACA3B,EAASU,EAAkB,4BAA6B,WACpD,IAAI,CAACG,aAAa,CAAG,CAAA,EACrB,IAAI,CAACe,UAAU,EACnB,GACA1B,EAAOQ,EAAiBlE,SAAS,CAAE,CAC/B2B,KAAM,aACN6J,YAAatJ,EACbxB,WAAYI,EACZ2K,cAAe,CAAC,MAAO,MAAO,QAAQ,CACtCC,YAAa,CAAC,MAAO,MAAM,AAC/B,GACAlL,IAA0ImL,kBAAkB,CAAC,aAAczH,GA4G9I,IAAM9D,EAAmBE,IAG5C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}