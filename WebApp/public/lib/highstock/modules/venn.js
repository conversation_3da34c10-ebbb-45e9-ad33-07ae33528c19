!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/venn
 * @requires highcharts
 *
 * (c) 2017-2025 Highsoft AS
 * Authors: <AUTHORS>
 *
 * License: www.highcharts.com/license
 */function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(e._Highcharts,e._Highcharts.Color,e._Highcharts.SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/modules/venn",["highcharts/highcharts"],function(e){return t(e,e.Color,e.SeriesRegistry)}):"object"==typeof exports?exports["highcharts/modules/venn"]=t(e._Highcharts,e._Highcharts.Color,e._Highcharts.SeriesRegistry):e.Highcharts=t(e.Highcharts,e.Highcharts.Color,e.Highcharts.SeriesRegistry)}("undefined"==typeof window?this:window,(e,t,r)=>(()=>{"use strict";var n,i,s={512:e=>{e.exports=r},620:e=>{e.exports=t},944:t=>{t.exports=e}},o={};function l(e){var t=o[e];if(void 0!==t)return t.exports;var r=o[e]={exports:{}};return s[e](r,r.exports,l),r.exports}l.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return l.d(t,{a:t}),t},l.d=(e,t)=>{for(var r in t)l.o(t,r)&&!l.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},l.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var a={};l.d(a,{default:()=>eo});var c=l(944),u=l.n(c),f=l(620),h=l.n(f);!function(e){e.getCenterOfPoints=function(e){let t=e.reduce((e,t)=>(e.x+=t.x,e.y+=t.y,e),{x:0,y:0});return{x:t.x/e.length,y:t.y/e.length}},e.getDistanceBetweenPoints=function(e,t){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))},e.getAngleBetweenPoints=function(e,t){return Math.atan2(t.x-e.x,t.y-e.y)},e.pointInPolygon=function({x:e,y:t},r){let n=r.length,i,s,o=!1;for(i=0,s=n-1;i<n;s=i++){let[n,l]=r[i],[a,c]=r[s];l>t!=c>t&&e<(a-n)*(t-l)/(c-l)+n&&(o=!o)}return o}}(n||(n={}));let p=n,{getAngleBetweenPoints:d,getCenterOfPoints:g,getDistanceBetweenPoints:y}=p;!function(e){function t(e,t){let r=Math.pow(10,t);return Math.round(e*r)/r}function r(e){if(e<=0)throw Error("radius of circle must be a positive number.");return Math.PI*e*e}function n(e,t){return e*e*Math.acos(1-t/e)-(e-t)*Math.sqrt(t*(2*e-t))}function i(e,r){let n=y(e,r),i=e.r,s=r.r,o=[];if(n<i+s&&n>Math.abs(i-s)){let l=i*i,a=(l-s*s+n*n)/(2*n),c=Math.sqrt(l-a*a),u=e.x,f=r.x,h=e.y,p=r.y,d=u+a*(f-u)/n,g=h+a*(p-h)/n,y=-(c/n*(p-h)),x=-(c/n*(f-u));o=[{x:t(d+y,14),y:t(g-x,14)},{x:t(d-y,14),y:t(g+x,14)}]}return o}function s(e){return e.reduce((e,t,r,n)=>{let s=n.slice(r+1).reduce((e,n,s)=>{let o=[r,s+r+1];return e.concat(i(t,n).map(e=>(e.indexes=o,e)))},[]);return e.concat(s)},[])}function o(e,t){return y(e,t)<=t.r+1e-10}function l(e,t){return!t.some(function(t){return!o(e,t)})}function a(e){return s(e).filter(function(t){return l(t,e)})}e.round=t,e.getAreaOfCircle=r,e.getCircularSegmentArea=n,e.getOverlapBetweenCircles=function(e,i,s){let o=0;if(s<e+i){if(s<=Math.abs(i-e))o=r(e<i?e:i);else{let t=(e*e-i*i+s*s)/(2*s);o=n(e,e-t)+n(i,i-(s-t))}o=t(o,14)}return o},e.getCircleCircleIntersection=i,e.getCirclesIntersectionPoints=s,e.isCircle1CompletelyOverlappingCircle2=function(e,t){return y(e,t)+t.r<e.r+1e-10},e.isPointInsideCircle=o,e.isPointInsideAllCircles=l,e.isPointOutsideAllCircles=function(e,t){return!t.some(function(t){return o(e,t)})},e.getCirclesIntersectionPolygon=a,e.getAreaOfIntersectionBetweenCircles=function(e){let t=a(e),r;if(t.length>1){let n=g(t),i=(t=t.map(function(e){return e.angle=d(n,e),e}).sort(function(e,t){return t.angle-e.angle}))[t.length-1],s=t.reduce(function(t,r){let{startPoint:n}=t,i=g([n,r]),s=r.indexes.filter(function(e){return n.indexes.indexOf(e)>-1}).reduce(function(t,s){let o=e[s],l=d(o,r),a=d(o,n),c=a-l+(a<l?2*Math.PI:0),u=a-c/2,f=y(i,{x:o.x+o.r*Math.sin(u),y:o.y+o.r*Math.cos(u)}),{r:h}=o;return f>2*h&&(f=2*h),(!t||t.width>f)&&(t={r:h,largeArc:+(f>h),width:f,x:r.x,y:r.y}),t},null);if(s){let{r:e}=s;t.arcs.push(["A",e,e,0,s.largeArc,1,s.x,s.y]),t.startPoint=r}return t},{startPoint:i,arcs:[]}).arcs;0===s.length||1===s.length||(s.unshift(["M",i.x,i.y]),r={center:n,d:s})}return r}}(i||(i={}));let x=i,m={draw:function(e,t){let{animatableAttribs:r,onComplete:n,css:i,renderer:s}=t,o=e.series&&e.series.chart.hasRendered?void 0:e.series&&e.series.options.animation,l=e.graphic;if(t.attribs={...t.attribs,class:e.getClassName()},e.shouldDraw())l||(e.graphic=l="text"===t.shapeType?s.text():"image"===t.shapeType?s.image(t.imageUrl||"").attr(t.shapeArgs||{}):s[t.shapeType](t.shapeArgs||{}),l.add(t.group)),i&&l.css(i),l.attr(t.attribs).animate(r,!t.isNew&&o,n);else if(l){let t=()=>{e.graphic=l=l&&l.destroy(),"function"==typeof n&&n()};Object.keys(r).length?l.animate(r,void 0,()=>t()):t()}}};var b=l(512),v=l.n(b);let{scatter:{prototype:{pointClass:M}}}=v().seriesTypes,{isNumber:A}=u(),{getAreaOfCircle:C,getCircleCircleIntersection:O,getOverlapBetweenCircles:w,isPointInsideAllCircles:P,isPointInsideCircle:E,isPointOutsideAllCircles:L}=x,{getDistanceBetweenPoints:j}=p,{extend:S,isArray:_,isNumber:I,isObject:N,isString:T}=u();function V(e){let t={};return e.filter(e=>2===e.sets.length).forEach(e=>{e.sets.forEach((r,n,i)=>{N(t[r])||(t[r]={totalOverlap:0,overlapping:{}}),t[r]={totalOverlap:(t[r].totalOverlap||0)+e.value,overlapping:{...t[r].overlapping||{},[i[1-n]]:e.value}}})}),e.filter(D).forEach(e=>{let r=t[e.sets[0]];S(e,r)}),e}function H(e,t,r,n,i){let s=e(t),o=e(r),l=i||100,a=n||1e-10,c=r-t,u,f,h=1;if(t>=r)throw Error("a must be smaller than b.");if(s*o>0)throw Error("f(a) and f(b) must have opposite signs.");if(0===s)u=t;else if(0===o)u=r;else for(;h++<=l&&0!==f&&c>a;)c=(r-t)/2,s*(f=e(u=t+c))>0?t=u:r=u;return u}function B(e){let t=e.slice(0,-1),r=t.length,n=[],i=(e,t)=>(e.sum+=t[e.i],e);for(let e=0;e<r;e++)n[e]=t.reduce(i,{sum:0,i:e}).sum/r;return n}function X(e,t,r){let n,i=e+t;return r<=0?i:C(e<t?e:t)<=r?0:H(n=>r-w(e,t,n),0,i)}function D(e){return _(e.sets)&&1===e.sets.length}function k(e){let t={};return N(e)&&I(e.value)&&e.value>-1&&_(e.sets)&&e.sets.length>0&&!e.sets.some(function(e){let r=!1;return!t[e]&&T(e)?t[e]=!0:r=!0,r})}function U(e,t){return t.reduce(function(t,r){let n=0;if(r.sets.length>1){let t=r.value-function(e){let t=0;if(2===e.length){let r=e[0],n=e[1];t=w(r.r,n.r,j(r,n))}return t}(r.sets.map(function(t){return e[t]}));n=Math.round(t*t*1e11)/1e11}return t+n},0)}function F(e,t){return void 0!==t.totalOverlap&&void 0!==e.totalOverlap?t.totalOverlap-e.totalOverlap:NaN}let R={geometry:p,geometryCircles:x,addOverlapToSets:V,getCentroid:B,getDistanceBetweenCirclesByOverlap:X,getLabelWidth:function(e,t,r){let n=t.reduce((e,t)=>Math.min(t.r,e),1/0),i=r.filter(t=>!E(e,t)),s=function(r,n){return H(s=>{let o={x:e.x+n*s,y:e.y};return-(r-s)+(P(o,t)&&L(o,i)?0:Number.MAX_VALUE)},0,r)};return 2*Math.min(s(n,-1),s(n,1))},getMarginFromCircles:function(e,t,r){let n=t.reduce((t,r)=>{let n=r.r-j(e,r);return n<=t?n:t},Number.MAX_VALUE);return r.reduce((t,r)=>{let n=j(e,r)-r.r;return n<=t?n:t},n)},isSet:D,layoutGreedyVenn:function(e){let t=[],r={};e.filter(e=>1===e.sets.length).forEach(e=>{r[e.sets[0]]=e.circle={x:Number.MAX_VALUE,y:Number.MAX_VALUE,r:Math.sqrt(e.value/Math.PI)}});let n=(e,r)=>{let n=e.circle;n&&(n.x=r.x,n.y=r.y),t.push(e)};V(e);let i=e.filter(D).sort(F);n(i.shift(),{x:0,y:0});let s=e.filter(e=>2===e.sets.length);for(let e of i){let i=e.circle;if(!i)continue;let o=i.r,l=e.overlapping;n(e,t.reduce((e,n,a)=>{let c=n.circle;if(!c||!l)return e;let u=l[n.sets[0]],f=X(o,c.r,u),h=[{x:c.x+f,y:c.y},{x:c.x-f,y:c.y},{x:c.x,y:c.y+f},{x:c.x,y:c.y-f}];for(let e of t.slice(a+1)){let t=e.circle,r=l[e.sets[0]];if(!t)continue;let n=X(o,t.r,r);h=h.concat(O({x:c.x,y:c.y,r:f},{x:t.x,y:t.y,r:n}))}for(let t of h){i.x=t.x,i.y=t.y;let n=U(r,s);n<e.loss&&(e.loss=n,e.coordinates=t)}return e},{loss:Number.MAX_VALUE,coordinates:void 0}).coordinates)}return r},loss:U,nelderMead:function(e,t){let r=function(e,t){return e.fx-t.fx},n=(e,t,r,n)=>t.map((t,i)=>e*t+r*n[i]),i=(t,r)=>(r.fx=e(r),t[t.length-1]=r,t),s=t=>{let r=t[0];return t.map(t=>{let i=n(.5,r,.5,t);return i.fx=e(i),i})},o=(t,r,i,s)=>{let o=n(i,t,s,r);return o.fx=e(o),o},l=(t=>{let r=t.length,n=Array(r+1);n[0]=t,n[0].fx=e(t);for(let i=0;i<r;++i){let r=t.slice();r[i]=r[i]?1.05*r[i]:.001,r.fx=e(r),n[i+1]=r}return n})(t);for(let e=0;e<100;e++){l.sort(r);let e=l[l.length-1],t=B(l),n=o(t,e,2,-1);if(n.fx<l[0].fx){let r=o(t,e,3,-2);l=i(l,r.fx<n.fx?r:n)}else if(n.fx>=l[l.length-2].fx){let r;l=n.fx>e.fx?(r=o(t,e,.5,.5)).fx<e.fx?i(l,r):s(l):(r=o(t,e,1.5,-.5)).fx<n.fx?i(l,r):s(l)}else l=i(l,n)}return l[0]},processVennData:function(e,t){let r=_(e)?e:[],n=r.reduce(function(e,t){var r;return t.sets&&k(r=t)&&D(r)&&r.value>0&&-1===e.indexOf(t.sets[0])&&e.push(t.sets[0]),e},[]).sort(),i=r.reduce(function(e,r){return r.sets&&k(r)&&!r.sets.some(function(e){return -1===n.indexOf(e)})&&(e[r.sets.sort().join(t)]={sets:r.sets,value:r.value||0}),e},{});return n.reduce(function(e,r,n,i){return i.slice(n+1).forEach(function(n){e.push(r+t+n)}),e},[]).forEach(function(e){if(!i[e]){let r={sets:e.split(t),value:0};i[e]=r}}),Object.keys(i).map(function(e){return i[e]})},sortByTotalOverlap:F},{animObject:W}=u(),{parse:q}=h(),{getAreaOfIntersectionBetweenCircles:Y,getCirclesIntersectionPolygon:G,isCircle1CompletelyOverlappingCircle2:z,isPointInsideAllCircles:J,isPointOutsideAllCircles:K}=x,{getCenterOfPoints:Q}=p,{scatter:Z}=v().seriesTypes,{addEvent:$,extend:ee,isArray:et,isNumber:er,isObject:en,merge:ei}=u();class es extends Z{static getLabelPosition(e,t){let r=e.reduce((r,n)=>{let i=n.r/2;return[{x:n.x,y:n.y},{x:n.x+i,y:n.y},{x:n.x-i,y:n.y},{x:n.x,y:n.y+i},{x:n.x,y:n.y-i}].reduce((r,n)=>{let i=R.getMarginFromCircles(n,e,t);return r.margin<i&&(r.point=n,r.margin=i),r},r)},{point:void 0,margin:-Number.MAX_VALUE}).point,n=R.nelderMead(r=>-R.getMarginFromCircles({x:r[0],y:r[1]},e,t),[r.x,r.y]);return J(r={x:n[0],y:n[1]},e)&&K(r,t)||(r=e.length>1?Q(G(e)):{x:e[0].x,y:e[0].y}),r}static getLabelValues(e,t){let r=e.sets,n=t.reduce((e,t)=>{let n=r.indexOf(t.sets[0])>-1;return t.circle&&e[n?"internal":"external"].push(t.circle),e},{internal:[],external:[]});n.external=n.external.filter(e=>n.internal.some(t=>!z(e,t)));let i=es.getLabelPosition(n.internal,n.external),s=R.getLabelWidth(i,n.internal,n.external);return{position:i,width:s}}static layout(e){let t={},r={};if(e.length>0){let n=R.layoutGreedyVenn(e),i=e.filter(R.isSet);for(let s of e){let e=s.sets,o=e.join(),l=R.isSet(s)?n[o]:Y(e.map(e=>n[e]));l&&(t[o]=l,r[o]=es.getLabelValues(s,i))}}return{mapOfIdToShape:t,mapOfIdToLabelValues:r}}static getScale(e,t,r){let n=r.bottom-r.top,i=r.right-r.left,s=(r.right+r.left)/2,o=(r.top+r.bottom)/2,l=Math.min(i>0?1/i*e:1,n>0?1/n*t:1);return{scale:l,centerX:e/2-s*l,centerY:t/2-o*l}}static updateFieldBoundaries(e,t){let r=t.x-t.r,n=t.x+t.r,i=t.y+t.r,s=t.y-t.r;return(!er(e.left)||e.left>r)&&(e.left=r),(!er(e.right)||e.right<n)&&(e.right=n),(!er(e.top)||e.top>s)&&(e.top=s),(!er(e.bottom)||e.bottom<i)&&(e.bottom=i),e}animate(e){if(!e){let e=W(this.options.animation);for(let t of this.points){let r=t.shapeArgs;if(t.graphic&&r){let n={},i={};r.d?n.opacity=.001:(n.r=0,i.r=r.r),t.graphic.attr(n).animate(i,e),r.d&&setTimeout(()=>{t?.graphic?.animate({opacity:1})},e.duration)}}}}drawPoints(){let e=this.chart,t=this.group,r=this.points||[],n=e.renderer;for(let i of r){let r={zIndex:et(i.sets)?i.sets.length:0},s=i.shapeArgs;e.styledMode||ee(r,this.pointAttribs(i,i.state)),m.draw(i,{isNew:!i.graphic,animatableAttribs:s,attribs:r,group:t,renderer:n,shapeType:s?.d?"path":"circle"})}}init(){Z.prototype.init.apply(this,arguments),delete this.opacity}pointAttribs(e,t){let r=this.options||{},n=e?.options||{},i=t&&r.states[t]||{},s=ei(r,{color:e?.color},n,i);return{fill:q(s.color).brighten(s.brightness).get(),opacity:s.opacity,stroke:s.borderColor,"stroke-width":s.borderWidth,dashstyle:s.borderDashStyle}}translate(){let e=this.chart;this.dataTable.modified=this.dataTable,this.generatePoints();let t=R.processVennData(this.options.data,es.splitter),{mapOfIdToShape:r,mapOfIdToLabelValues:n}=es.layout(t),i=Object.keys(r).filter(e=>{let t=r[e];return t&&er(t.r)}).reduce((e,t)=>es.updateFieldBoundaries(e,r[t]),{top:0,bottom:0,left:0,right:0}),s=es.getScale(e.plotWidth,e.plotHeight,i),o=s.scale,l=s.centerX,a=s.centerY;for(let e of this.points){let t=et(e.sets)?e.sets:[],i=t.join(),s=r[i],c=n[i]||{},u=e.options?.dataLabels,f,h=c.width,p=c.position;if(s){if(s.r)f={x:l+s.x*o,y:a+s.y*o,r:s.r*o};else if(s.d){let e=s.d;e.forEach(e=>{"M"===e[0]?(e[1]=l+e[1]*o,e[2]=a+e[2]*o):"A"===e[0]&&(e[1]=e[1]*o,e[2]=e[2]*o,e[6]=l+e[6]*o,e[7]=a+e[7]*o)}),f={d:e}}p?(p.x=l+p.x*o,p.y=a+p.y*o):p={},er(h)&&(h=Math.round(h*o))}e.shapeArgs=f,p&&f&&(e.plotX=p.x,e.plotY=p.y),h&&f&&(e.dlOptions=ei(!0,{style:{width:h}},en(u,!0)?u:void 0)),e.name=e.options.name||t.join("∩")}}}es.splitter="highcharts-split",es.defaultOptions=ei(Z.defaultOptions,{borderColor:"#cccccc",borderDashStyle:"solid",borderWidth:1,brighten:0,clip:!1,colorByPoint:!0,dataLabels:{enabled:!0,verticalAlign:"middle",formatter:function(){return this.point.name}},inactiveOtherPoints:!0,marker:!1,opacity:.75,showInLegend:!1,legendType:"point",states:{hover:{opacity:1,borderColor:"#333333"},select:{color:"#cccccc",borderColor:"#000000",animation:!1},inactive:{opacity:.075}},tooltip:{pointFormat:"{point.name}: {point.value}"},legendSymbol:"rectangle"}),ee(es.prototype,{axisTypes:[],directTouch:!0,isCartesian:!1,pointArrayMap:["value"],pointClass:class extends M{isValid(){return A(this.value)}shouldDraw(){return!!this.shapeArgs}},utils:R}),$(es,"afterSetOptions",function(e){let t=e.options.states||{};if(this.is("venn"))for(let e of Object.keys(t))t[e].halo=!1}),v().registerSeriesType("venn",es);let eo=u();return a.default})());