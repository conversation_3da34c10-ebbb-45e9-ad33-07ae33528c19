{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highmaps JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/tilemap\n * @requires highcharts\n * @requires highcharts/modules/map\n *\n * Tilemap module\n *\n * (c) 2010-2025 Highsoft AS\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"Color\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/tilemap\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"],amd1[\"Color\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/tilemap\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"Color\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"Color\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__620__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 620:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__620__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ tilemap_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Color\"],\"commonjs\":[\"highcharts\",\"Color\"],\"commonjs2\":[\"highcharts\",\"Color\"],\"root\":[\"Highcharts\",\"Color\"]}\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_ = __webpack_require__(620);\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default = /*#__PURE__*/__webpack_require__.n(highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_);\n;// ./code/es-modules/Core/Axis/Color/ColorAxisComposition.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { parse: color } = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default());\n\nconst { addEvent, extend, merge, pick, splat } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\nvar ColorAxisComposition;\n(function (ColorAxisComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Variables\n     *\n     * */\n    let ColorAxisConstructor;\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    function compose(ColorAxisClass, ChartClass, FxClass, LegendClass, SeriesClass) {\n        const chartProto = ChartClass.prototype, fxProto = FxClass.prototype, seriesProto = SeriesClass.prototype;\n        if (!chartProto.collectionsWithUpdate.includes('colorAxis')) {\n            ColorAxisConstructor = ColorAxisClass;\n            chartProto.collectionsWithUpdate.push('colorAxis');\n            chartProto.collectionsWithInit.colorAxis = [\n                chartProto.addColorAxis\n            ];\n            addEvent(ChartClass, 'afterCreateAxes', onChartAfterCreateAxes);\n            wrapChartCreateAxis(ChartClass);\n            fxProto.fillSetter = wrapFxFillSetter;\n            fxProto.strokeSetter = wrapFxStrokeSetter;\n            addEvent(LegendClass, 'afterGetAllItems', onLegendAfterGetAllItems);\n            addEvent(LegendClass, 'afterColorizeItem', onLegendAfterColorizeItem);\n            addEvent(LegendClass, 'afterUpdate', onLegendAfterUpdate);\n            extend(seriesProto, {\n                optionalAxis: 'colorAxis',\n                translateColors: seriesTranslateColors\n            });\n            extend(seriesProto.pointClass.prototype, {\n                setVisible: pointSetVisible\n            });\n            addEvent(SeriesClass, 'afterTranslate', onSeriesAfterTranslate, { order: 1 });\n            addEvent(SeriesClass, 'bindAxes', onSeriesBindAxes);\n        }\n    }\n    ColorAxisComposition.compose = compose;\n    /**\n     * Extend the chart createAxes method to also make the color axis.\n     * @private\n     */\n    function onChartAfterCreateAxes() {\n        const { userOptions } = this;\n        this.colorAxis = [];\n        // If a `colorAxis` config is present in the user options (not in a\n        // theme), instanciate it.\n        if (userOptions.colorAxis) {\n            userOptions.colorAxis = splat(userOptions.colorAxis);\n            userOptions.colorAxis.map((axisOptions) => (new ColorAxisConstructor(this, axisOptions)));\n        }\n    }\n    /**\n     * Add the color axis. This also removes the axis' own series to prevent\n     * them from showing up individually.\n     * @private\n     */\n    function onLegendAfterGetAllItems(e) {\n        const colorAxes = this.chart.colorAxis || [], destroyItem = (item) => {\n            const i = e.allItems.indexOf(item);\n            if (i !== -1) {\n                // #15436\n                this.destroyItem(e.allItems[i]);\n                e.allItems.splice(i, 1);\n            }\n        };\n        let colorAxisItems = [], options, i;\n        colorAxes.forEach(function (colorAxis) {\n            options = colorAxis.options;\n            if (options?.showInLegend) {\n                // Data classes\n                if (options.dataClasses && options.visible) {\n                    colorAxisItems = colorAxisItems.concat(colorAxis.getDataClassLegendSymbols());\n                    // Gradient legend\n                }\n                else if (options.visible) {\n                    // Add this axis on top\n                    colorAxisItems.push(colorAxis);\n                }\n                // If dataClasses are defined or showInLegend option is not set\n                // to true, do not add color axis' series to legend.\n                colorAxis.series.forEach(function (series) {\n                    if (!series.options.showInLegend || options.dataClasses) {\n                        if (series.options.legendType === 'point') {\n                            series.points.forEach(function (point) {\n                                destroyItem(point);\n                            });\n                        }\n                        else {\n                            destroyItem(series);\n                        }\n                    }\n                });\n            }\n        });\n        i = colorAxisItems.length;\n        while (i--) {\n            e.allItems.unshift(colorAxisItems[i]);\n        }\n    }\n    /**\n     * @private\n     */\n    function onLegendAfterColorizeItem(e) {\n        if (e.visible && e.item.legendColor) {\n            e.item.legendItem.symbol.attr({\n                fill: e.item.legendColor\n            });\n        }\n    }\n    /**\n     * Updates in the legend need to be reflected in the color axis. (#6888)\n     * @private\n     */\n    function onLegendAfterUpdate(e) {\n        this.chart.colorAxis?.forEach((colorAxis) => {\n            colorAxis.update({}, e.redraw);\n        });\n    }\n    /**\n     * Calculate and set colors for points.\n     * @private\n     */\n    function onSeriesAfterTranslate() {\n        if (this.chart.colorAxis?.length ||\n            this.colorAttribs) {\n            this.translateColors();\n        }\n    }\n    /**\n     * Add colorAxis to series axisTypes.\n     * @private\n     */\n    function onSeriesBindAxes() {\n        const axisTypes = this.axisTypes;\n        if (!axisTypes) {\n            this.axisTypes = ['colorAxis'];\n        }\n        else if (axisTypes.indexOf('colorAxis') === -1) {\n            axisTypes.push('colorAxis');\n        }\n    }\n    /**\n     * Set the visibility of a single point\n     * @private\n     * @function Highcharts.colorPointMixin.setVisible\n     * @param {boolean} visible\n     */\n    function pointSetVisible(vis) {\n        const point = this, method = vis ? 'show' : 'hide';\n        point.visible = point.options.visible = Boolean(vis);\n        // Show and hide associated elements\n        ['graphic', 'dataLabel'].forEach(function (key) {\n            if (point[key]) {\n                point[key][method]();\n            }\n        });\n        this.series.buildKDTree(); // Rebuild kdtree #13195\n    }\n    ColorAxisComposition.pointSetVisible = pointSetVisible;\n    /**\n     * In choropleth maps, the color is a result of the value, so this needs\n     * translation too\n     * @private\n     * @function Highcharts.colorSeriesMixin.translateColors\n     */\n    function seriesTranslateColors() {\n        const series = this, points = this.getPointsCollection(), // #17945\n        nullColor = this.options.nullColor, colorAxis = this.colorAxis, colorKey = this.colorKey;\n        points.forEach((point) => {\n            const value = point.getNestedProperty(colorKey), color = point.options.color || (point.isNull || point.value === null ?\n                nullColor :\n                (colorAxis && typeof value !== 'undefined') ?\n                    colorAxis.toColor(value, point) :\n                    point.color || series.color);\n            if (color && point.color !== color) {\n                point.color = color;\n                if (series.options.legendType === 'point' &&\n                    point.legendItem &&\n                    point.legendItem.label) {\n                    series.chart.legend.colorizeItem(point, point.visible);\n                }\n            }\n        });\n    }\n    /**\n     * @private\n     */\n    function wrapChartCreateAxis(ChartClass) {\n        const superCreateAxis = ChartClass.prototype.createAxis;\n        ChartClass.prototype.createAxis = function (type, options) {\n            const chart = this;\n            if (type !== 'colorAxis') {\n                return superCreateAxis.apply(chart, arguments);\n            }\n            const axis = new ColorAxisConstructor(chart, merge(options.axis, {\n                index: chart[type].length,\n                isX: false\n            }));\n            chart.isDirtyLegend = true;\n            // Clear before 'bindAxes' (#11924)\n            chart.axes.forEach((axis) => {\n                axis.series = [];\n            });\n            chart.series.forEach((series) => {\n                series.bindAxes();\n                series.isDirtyData = true;\n            });\n            if (pick(options.redraw, true)) {\n                chart.redraw(options.animation);\n            }\n            return axis;\n        };\n    }\n    /**\n     * Handle animation of the color attributes directly.\n     * @private\n     */\n    function wrapFxFillSetter() {\n        this.elem.attr('fill', color(this.start).tweenTo(color(this.end), this.pos), void 0, true);\n    }\n    /**\n     * Handle animation of the color attributes directly.\n     * @private\n     */\n    function wrapFxStrokeSetter() {\n        this.elem.attr('stroke', color(this.start).tweenTo(color(this.end), this.pos), void 0, true);\n    }\n})(ColorAxisComposition || (ColorAxisComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Color_ColorAxisComposition = (ColorAxisComposition);\n\n;// ./code/es-modules/Series/Tilemap/TilemapPoint.js\n/* *\n *\n *  Tilemaps module\n *\n *  (c) 2010-2025 Highsoft AS\n *  Author: Øystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { series: { prototype: { pointClass: Point } }, seriesTypes: { heatmap: { prototype: { pointClass: HeatmapPoint } } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { extend: TilemapPoint_extend } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass TilemapPoint extends HeatmapPoint {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     * @function Highcharts.Point#haloPath\n     */\n    haloPath() {\n        return this.series.tileShape.haloPath.apply(this, arguments);\n    }\n}\nTilemapPoint_extend(TilemapPoint.prototype, {\n    setState: Point.prototype.setState,\n    setVisible: Color_ColorAxisComposition.pointSetVisible\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Tilemap_TilemapPoint = (TilemapPoint);\n\n;// ./code/es-modules/Series/Tilemap/TilemapSeriesDefaults.js\n/* *\n *\n *  Tilemaps module\n *\n *  (c) 2010-2025 Highsoft AS\n *  Author: Øystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A tilemap series is a type of heatmap where the tile shapes are\n * configurable.\n *\n * @sample highcharts/demo/honeycomb-usa/\n *         Honeycomb tilemap, USA\n * @sample maps/plotoptions/honeycomb-brazil/\n *         Honeycomb tilemap, Brazil\n * @sample maps/plotoptions/honeycomb-china/\n *         Honeycomb tilemap, China\n * @sample maps/plotoptions/honeycomb-europe/\n *         Honeycomb tilemap, Europe\n * @sample maps/demo/circlemap-africa/\n *         Circlemap tilemap, Africa\n * @sample maps/demo/diamondmap\n *         Diamondmap tilemap\n *\n * @extends      plotOptions.heatmap\n * @since        6.0.0\n * @excluding    jitter, joinBy, shadow, allAreas, mapData, marker, data,\n *               dataSorting, boostThreshold, boostBlending\n * @product      highcharts highmaps\n * @requires     modules/tilemap\n * @optionparent plotOptions.tilemap\n */\nconst TilemapSeriesDefaults = {\n    // Remove marker from tilemap default options, as it was before\n    // heatmap refactoring.\n    marker: null,\n    states: {\n        hover: {\n            halo: {\n                enabled: true,\n                size: 2,\n                opacity: 0.5,\n                attributes: {\n                    zIndex: 3\n                }\n            }\n        }\n    },\n    /**\n     * The padding between points in the tilemap.\n     *\n     * @sample maps/plotoptions/tilemap-pointpadding\n     *         Point padding on tiles\n     */\n    pointPadding: 2,\n    /**\n     * The column size - how many X axis units each column in the tilemap\n     * should span. Works as in [Heatmaps](#plotOptions.heatmap.colsize).\n     *\n     * @sample {highcharts} maps/demo/heatmap/\n     *         One day\n     * @sample {highmaps} maps/demo/heatmap/\n     *         One day\n     *\n     * @type      {number}\n     * @default   1\n     * @product   highcharts highmaps\n     * @apioption plotOptions.tilemap.colsize\n     */\n    /**\n     * The row size - how many Y axis units each tilemap row should span.\n     * Analogous to [colsize](#plotOptions.tilemap.colsize).\n     *\n     * @sample {highcharts} maps/demo/heatmap/\n     *         1 by default\n     * @sample {highmaps} maps/demo/heatmap/\n     *         1 by default\n     *\n     * @type      {number}\n     * @default   1\n     * @product   highcharts highmaps\n     * @apioption plotOptions.tilemap.rowsize\n     */\n    /**\n     * The shape of the tiles in the tilemap. Possible values are `hexagon`,\n     * `circle`, `diamond`, and `square`.\n     *\n     * @sample maps/demo/circlemap-africa\n     *         Circular tile shapes\n     * @sample maps/demo/diamondmap\n     *         Diamond tile shapes\n     *\n     * @type {Highcharts.TilemapShapeValue}\n     */\n    tileShape: 'hexagon'\n};\n/**\n * A `tilemap` series. If the [type](#series.tilemap.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.tilemap\n * @excluding allAreas, dataParser, dataURL, joinBy, mapData, marker,\n *            pointRange, shadow, stack, dataSorting, boostThreshold,\n *            boostBlending\n * @product   highcharts highmaps\n * @requires  modules/tilemap\n * @apioption series.tilemap\n */\n/**\n * An array of data points for the series. For the `tilemap` series\n * type, points can be given in the following ways:\n *\n * 1. An array of arrays with 3 or 2 values. In this case, the values correspond\n *    to `x,y,value`. If the first value is a string, it is applied as the name\n *    of the point, and the `x` value is inferred. The `x` value can also be\n *    omitted, in which case the inner arrays should be of length 2\\. Then the\n *    `x` value is automatically calculated, either starting at 0 and\n *    incremented by 1, or from `pointStart` and `pointInterval` given in the\n *    series options.\n *    ```js\n *    data: [\n *        [0, 9, 7],\n *        [1, 10, 4],\n *        [2, 6, 3]\n *    ]\n *    ```\n *\n * 2. An array of objects with named values. The objects are point configuration\n *    objects as seen below. If the total number of data points exceeds the\n *    series' [turboThreshold](#series.tilemap.turboThreshold), this option is\n *    not available.\n *    ```js\n *    data: [{\n *        x: 1,\n *        y: 3,\n *        value: 10,\n *        name: \"Point2\",\n *        color: \"#00FF00\"\n *    }, {\n *        x: 1,\n *        y: 7,\n *        value: 10,\n *        name: \"Point1\",\n *        color: \"#FF00FF\"\n *    }]\n *    ```\n *\n * Note that for some [tileShapes](#plotOptions.tilemap.tileShape) the grid\n * coordinates are offset.\n *\n * @sample maps/series/tilemap-gridoffset\n *         Offset grid coordinates\n * @sample {highcharts} highcharts/series/data-array-of-arrays/\n *         Arrays of numeric x and y\n * @sample {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *         Arrays of datetime x and y\n * @sample {highcharts} highcharts/series/data-array-of-name-value/\n *         Arrays of point.name and y\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<Array<(number|string),number>|Array<(number|string),number,number>|*>}\n * @extends   series.heatmap.data\n * @excluding marker\n * @product   highcharts highmaps\n * @apioption series.tilemap.data\n */\n/**\n * The color of the point. In tilemaps the point color is rarely set\n * explicitly, as we use the color to denote the `value`. Options for\n * this are set in the [colorAxis](#colorAxis) configuration.\n *\n * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n * @product   highcharts highmaps\n * @apioption series.tilemap.data.color\n */\n/**\n * The x coordinate of the point.\n *\n * Note that for some [tileShapes](#plotOptions.tilemap.tileShape) the grid\n * coordinates are offset.\n *\n * @sample maps/series/tilemap-gridoffset\n *         Offset grid coordinates\n *\n * @type      {number}\n * @product   highcharts highmaps\n * @apioption series.tilemap.data.x\n */\n/**\n * The y coordinate of the point.\n *\n * Note that for some [tileShapes](#plotOptions.tilemap.tileShape) the grid\n * coordinates are offset.\n *\n * @sample maps/series/tilemap-gridoffset\n *         Offset grid coordinates\n *\n * @type      {number}\n * @product   highcharts highmaps\n * @apioption series.tilemap.data.y\n */\n''; // Keeps doclets above detached\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Tilemap_TilemapSeriesDefaults = (TilemapSeriesDefaults);\n\n;// ./code/es-modules/Series/Tilemap/TilemapShapes.js\n/* *\n *\n *  Tilemaps module\n *\n *  (c) 2010-2025 Highsoft AS\n *  Author: Øystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { noop } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { heatmap: HeatmapSeries, scatter: ScatterSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { clamp, pick: TilemapShapes_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Utility func to get padding definition from tile size division\n * @private\n */\nfunction tilePaddingFromTileSize(series, xDiv, yDiv) {\n    const options = series.options;\n    return {\n        xPad: (options.colsize || 1) / -xDiv,\n        yPad: (options.rowsize || 1) / -yDiv\n    };\n}\n/* *\n *\n *  Registry\n *\n * */\n/**\n * Map of shape types.\n * @private\n */\nconst TilemapShapes = {\n    // Hexagon shape type.\n    hexagon: {\n        alignDataLabel: ScatterSeries.prototype.alignDataLabel,\n        getSeriesPadding: function (series) {\n            return tilePaddingFromTileSize(series, 3, 2);\n        },\n        haloPath: function (size) {\n            if (!size) {\n                return [];\n            }\n            const hexagon = this.tileEdges;\n            return [\n                ['M', hexagon.x2 - size, hexagon.y1 + size],\n                ['L', hexagon.x3 + size, hexagon.y1 + size],\n                ['L', hexagon.x4 + size * 1.5, hexagon.y2],\n                ['L', hexagon.x3 + size, hexagon.y3 - size],\n                ['L', hexagon.x2 - size, hexagon.y3 - size],\n                ['L', hexagon.x1 - size * 1.5, hexagon.y2],\n                ['Z']\n            ];\n        },\n        translate: function () {\n            const series = this, options = series.options, xAxis = series.xAxis, yAxis = series.yAxis, seriesPointPadding = options.pointPadding || 0, xPad = (options.colsize || 1) / 3, yPad = (options.rowsize || 1) / 2;\n            let yShift;\n            series.generatePoints();\n            for (const point of series.points) {\n                let x1 = clamp(Math.floor(xAxis.len -\n                    xAxis.translate(point.x - xPad * 2, 0, 1, 0, 1)), -xAxis.len, 2 * xAxis.len), x2 = clamp(Math.floor(xAxis.len -\n                    xAxis.translate(point.x - xPad, 0, 1, 0, 1)), -xAxis.len, 2 * xAxis.len), x3 = clamp(Math.floor(xAxis.len -\n                    xAxis.translate(point.x + xPad, 0, 1, 0, 1)), -xAxis.len, 2 * xAxis.len), x4 = clamp(Math.floor(xAxis.len -\n                    xAxis.translate(point.x + xPad * 2, 0, 1, 0, 1)), -xAxis.len, 2 * xAxis.len), y1 = clamp(Math.floor(yAxis.translate(point.y - yPad, 0, 1, 0, 1)), -yAxis.len, 2 * yAxis.len), y2 = clamp(Math.floor(yAxis.translate(point.y, 0, 1, 0, 1)), -yAxis.len, 2 * yAxis.len), y3 = clamp(Math.floor(yAxis.translate(point.y + yPad, 0, 1, 0, 1)), -yAxis.len, 2 * yAxis.len);\n                const pointPadding = point.pointPadding ?? seriesPointPadding, \n                // We calculate the point padding of the midpoints to\n                // preserve the angles of the shape.\n                midPointPadding = pointPadding *\n                    Math.abs(x2 - x1) / Math.abs(y3 - y2), xMidPadding = xAxis.reversed ?\n                    -midPointPadding : midPointPadding, xPointPadding = xAxis.reversed ?\n                    -pointPadding : pointPadding, yPointPadding = yAxis.reversed ?\n                    -pointPadding : pointPadding;\n                // Shift y-values for every second grid column\n                if (point.x % 2) {\n                    yShift = yShift || Math.round(Math.abs(y3 - y1) / 2) *\n                        // We have to reverse the shift for reversed y-axes\n                        (yAxis.reversed ? -1 : 1);\n                    y1 += yShift;\n                    y2 += yShift;\n                    y3 += yShift;\n                }\n                // Set plotX and plotY for use in K-D-Tree and more\n                point.plotX = point.clientX = (x2 + x3) / 2;\n                point.plotY = y2;\n                // Apply point padding to translated coordinates\n                x1 += xMidPadding + xPointPadding;\n                x2 += xPointPadding;\n                x3 -= xPointPadding;\n                x4 -= xMidPadding + xPointPadding;\n                y1 -= yPointPadding;\n                y3 += yPointPadding;\n                // Store points for halo creation\n                point.tileEdges = {\n                    x1: x1, x2: x2, x3: x3, x4: x4, y1: y1, y2: y2, y3: y3\n                };\n                // Finally set the shape for this point\n                point.shapeType = 'path';\n                point.shapeArgs = {\n                    d: [\n                        ['M', x2, y1],\n                        ['L', x3, y1],\n                        ['L', x4, y2],\n                        ['L', x3, y3],\n                        ['L', x2, y3],\n                        ['L', x1, y2],\n                        ['Z']\n                    ]\n                };\n            }\n            series.translateColors();\n        }\n    },\n    // Diamond shape type.\n    diamond: {\n        alignDataLabel: ScatterSeries.prototype.alignDataLabel,\n        getSeriesPadding: function (series) {\n            return tilePaddingFromTileSize(series, 2, 2);\n        },\n        haloPath: function (size) {\n            if (!size) {\n                return [];\n            }\n            const diamond = this.tileEdges;\n            return [\n                ['M', diamond.x2, diamond.y1 + size],\n                ['L', diamond.x3 + size, diamond.y2],\n                ['L', diamond.x2, diamond.y3 - size],\n                ['L', diamond.x1 - size, diamond.y2],\n                ['Z']\n            ];\n        },\n        translate: function () {\n            const series = this, options = series.options, xAxis = series.xAxis, yAxis = series.yAxis, seriesPointPadding = options.pointPadding || 0, xPad = (options.colsize || 1), yPad = (options.rowsize || 1) / 2;\n            let yShift;\n            series.generatePoints();\n            for (const point of series.points) {\n                let x1 = clamp(Math.round(xAxis.len -\n                    xAxis.translate(point.x - xPad, 0, 1, 0, 0)), -xAxis.len, 2 * xAxis.len), x3 = clamp(Math.round(xAxis.len -\n                    xAxis.translate(point.x + xPad, 0, 1, 0, 0)), -xAxis.len, 2 * xAxis.len), y1 = clamp(Math.round(yAxis.translate(point.y - yPad, 0, 1, 0, 0)), -yAxis.len, 2 * yAxis.len), y2 = clamp(Math.round(yAxis.translate(point.y, 0, 1, 0, 0)), -yAxis.len, 2 * yAxis.len), y3 = clamp(Math.round(yAxis.translate(point.y + yPad, 0, 1, 0, 0)), -yAxis.len, 2 * yAxis.len);\n                const x2 = clamp(Math.round(xAxis.len -\n                    xAxis.translate(point.x, 0, 1, 0, 0)), -xAxis.len, 2 * xAxis.len), pointPadding = TilemapShapes_pick(point.pointPadding, seriesPointPadding), \n                // We calculate the point padding of the midpoints to\n                // preserve the angles of the shape.\n                midPointPadding = pointPadding *\n                    Math.abs(x2 - x1) / Math.abs(y3 - y2), xPointPadding = xAxis.reversed ?\n                    -midPointPadding : midPointPadding, yPointPadding = yAxis.reversed ?\n                    -pointPadding : pointPadding;\n                // Shift y-values for every second grid column\n                // We have to reverse the shift for reversed y-axes\n                if (point.x % 2) {\n                    yShift = Math.abs(y3 - y1) / 2 * (yAxis.reversed ? -1 : 1);\n                    y1 += yShift;\n                    y2 += yShift;\n                    y3 += yShift;\n                }\n                // Set plotX and plotY for use in K-D-Tree and more\n                point.plotX = point.clientX = x2;\n                point.plotY = y2;\n                // Apply point padding to translated coordinates\n                x1 += xPointPadding;\n                x3 -= xPointPadding;\n                y1 -= yPointPadding;\n                y3 += yPointPadding;\n                // Store points for halo creation\n                point.tileEdges = {\n                    x1: x1, x2: x2, x3: x3, y1: y1, y2: y2, y3: y3\n                };\n                // Set this point's shape parameters\n                point.shapeType = 'path';\n                point.shapeArgs = {\n                    d: [\n                        ['M', x2, y1],\n                        ['L', x3, y2],\n                        ['L', x2, y3],\n                        ['L', x1, y2],\n                        ['Z']\n                    ]\n                };\n            }\n            series.translateColors();\n        }\n    },\n    // Circle shape type.\n    circle: {\n        alignDataLabel: ScatterSeries.prototype.alignDataLabel,\n        getSeriesPadding: function (series) {\n            return tilePaddingFromTileSize(series, 2, 2);\n        },\n        haloPath: function (size) {\n            return ScatterSeries.prototype.pointClass.prototype.haloPath\n                .call(this, size + (size && this.radius));\n        },\n        translate: function () {\n            const series = this, options = series.options, xAxis = series.xAxis, yAxis = series.yAxis, seriesPointPadding = options.pointPadding || 0, yRadius = (options.rowsize || 1) / 2, colsize = (options.colsize || 1);\n            let colsizePx, yRadiusPx, xRadiusPx, radius, forceNextRadiusCompute = false;\n            series.generatePoints();\n            for (const point of series.points) {\n                const x = clamp(Math.round(xAxis.len -\n                    xAxis.translate(point.x, 0, 1, 0, 0)), -xAxis.len, 2 * xAxis.len);\n                let pointPadding = seriesPointPadding, hasPerPointPadding = false, y = clamp(Math.round(yAxis.translate(point.y, 0, 1, 0, 0)), -yAxis.len, 2 * yAxis.len);\n                // If there is point padding defined on a single point, add it\n                if (typeof point.pointPadding !== 'undefined') {\n                    pointPadding = point.pointPadding;\n                    hasPerPointPadding = true;\n                    forceNextRadiusCompute = true;\n                }\n                // Find radius if not found already.\n                // Use the smallest one (x vs y) to avoid overlap.\n                // Note that the radius will be recomputed for each series.\n                // Ideal (max) x radius is dependent on y radius:\n                /*\n                                * (circle 2)\n\n                                        * (circle 3)\n                                        |    yRadiusPx\n                    (circle 1)    *-------|\n                                 colsizePx\n\n                    The distance between circle 1 and 3 (and circle 2 and 3) is\n                    2r, which is the hypotenuse of the triangle created by\n                    colsizePx and yRadiusPx. If the distance between circle 2\n                    and circle 1 is less than 2r, we use half of that distance\n                    instead (yRadiusPx).\n                */\n                if (!radius || forceNextRadiusCompute) {\n                    colsizePx = Math.abs(clamp(Math.floor(xAxis.len -\n                        xAxis.translate(point.x + colsize, 0, 1, 0, 0)), -xAxis.len, 2 * xAxis.len) - x);\n                    yRadiusPx = Math.abs(clamp(Math.floor(yAxis.translate(point.y + yRadius, 0, 1, 0, 0)), -yAxis.len, 2 * yAxis.len) - y);\n                    xRadiusPx = Math.floor(Math.sqrt((colsizePx * colsizePx + yRadiusPx * yRadiusPx)) / 2);\n                    radius = Math.min(colsizePx, xRadiusPx, yRadiusPx) - pointPadding;\n                    // If we have per point padding we need to always compute\n                    // the radius for this point and the next. If we used to\n                    // have per point padding but don't anymore, don't force\n                    // compute next radius.\n                    if (forceNextRadiusCompute && !hasPerPointPadding) {\n                        forceNextRadiusCompute = false;\n                    }\n                }\n                // Shift y-values for every second grid column.\n                // Note that we always use the optimal y axis radius for this.\n                // Also note: We have to reverse the shift for reversed y-axes.\n                if (point.x % 2) {\n                    y += yRadiusPx * (yAxis.reversed ? -1 : 1);\n                }\n                // Set plotX and plotY for use in K-D-Tree and more\n                point.plotX = point.clientX = x;\n                point.plotY = y;\n                // Save radius for halo\n                point.radius = radius;\n                // Set this point's shape parameters\n                point.shapeType = 'circle';\n                point.shapeArgs = {\n                    x: x,\n                    y: y,\n                    r: radius\n                };\n            }\n            series.translateColors();\n        }\n    },\n    // Square shape type.\n    square: {\n        alignDataLabel: HeatmapSeries.prototype.alignDataLabel,\n        translate: HeatmapSeries.prototype.translate,\n        getSeriesPadding: noop,\n        haloPath: HeatmapSeries.prototype.pointClass.prototype.haloPath\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Tilemap_TilemapShapes = (TilemapShapes);\n\n;// ./code/es-modules/Series/Tilemap/TilemapSeries.js\n/* *\n *\n *  Tilemaps module\n *\n *  (c) 2010-2025 Highsoft AS\n *  Author: Øystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { composed, noop: TilemapSeries_noop } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { column: ColumnSeries, heatmap: TilemapSeries_HeatmapSeries, scatter: TilemapSeries_ScatterSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\n\n\n\nconst { addEvent: TilemapSeries_addEvent, extend: TilemapSeries_extend, merge: TilemapSeries_merge, pushUnique } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Extension to add pixel padding for series. Uses getSeriesPixelPadding on each\n * series and adds the largest padding required. If no series has this function\n * defined, we add nothing.\n * @private\n */\nfunction onAxisAfterSetAxisTranslation() {\n    if (this.recomputingForTilemap || this.coll === 'colorAxis') {\n        return;\n    }\n    const axis = this, \n    // Find which series' padding to use\n    seriesPadding = axis.series\n        .map(function (series) {\n        return series.getSeriesPixelPadding &&\n            series.getSeriesPixelPadding(axis);\n    })\n        .reduce(function (a, b) {\n        return (a && a.padding) > (b && b.padding) ?\n            a :\n            b;\n    }, void 0) ||\n        {\n            padding: 0,\n            axisLengthFactor: 1\n        }, lengthPadding = Math.round(seriesPadding.padding * seriesPadding.axisLengthFactor);\n    // Don't waste time on this if we're not adding extra padding\n    if (seriesPadding.padding) {\n        // Recompute translation with new axis length now (minus padding)\n        axis.len -= lengthPadding;\n        axis.recomputingForTilemap = true;\n        axis.setAxisTranslation();\n        delete axis.recomputingForTilemap;\n        axis.minPixelPadding += seriesPadding.padding;\n        axis.len += lengthPadding;\n    }\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.tilemap\n *\n * @augments Highcharts.Series\n */\nclass TilemapSeries extends TilemapSeries_HeatmapSeries {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static compose(AxisClass) {\n        if (pushUnique(composed, 'TilemapSeries')) {\n            TilemapSeries_addEvent(AxisClass, 'afterSetAxisTranslation', onAxisAfterSetAxisTranslation);\n        }\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Use the shape's defined data label alignment function.\n     * @private\n     */\n    alignDataLabel() {\n        return this.tileShape.alignDataLabel.apply(this, arguments);\n    }\n    drawPoints() {\n        // In styled mode, use CSS, otherwise the fill used in the style\n        // sheet will take precedence over the fill attribute.\n        ColumnSeries.prototype.drawPoints.call(this);\n        for (const point of this.points) {\n            if (point.graphic) {\n                point.graphic[this.chart.styledMode ? 'css' : 'animate'](this.colorAttribs(point));\n            }\n        }\n    }\n    /**\n     * Get metrics for padding of axis for this series.\n     * @private\n     */\n    getSeriesPixelPadding(axis) {\n        const isX = axis.isXAxis, padding = this.tileShape.getSeriesPadding(this);\n        // If the shape type does not require padding, return no-op padding\n        if (!padding) {\n            return {\n                padding: 0,\n                axisLengthFactor: 1\n            };\n        }\n        // Use translate to compute how far outside the points we\n        // draw, and use this difference as padding.\n        const coord1 = Math.round(axis.translate(isX ?\n            padding.xPad * 2 :\n            padding.yPad, 0, 1, 0, 1));\n        const coord2 = Math.round(axis.translate(isX ? padding.xPad : 0, 0, 1, 0, 1));\n        return {\n            padding: (axis.single ? // If there is only one tick adjust padding #18647\n                Math.abs(coord1 - coord2) / 2 :\n                Math.abs(coord1 - coord2)) || 0,\n            // Offset the yAxis length to compensate for shift. Setting the\n            // length factor to 2 would add the same margin to max as min.\n            // Now we only add a slight bit of the min margin to max, as we\n            // don't actually draw outside the max bounds. For the xAxis we\n            // draw outside on both sides so we add the same margin to min\n            // and max.\n            axisLengthFactor: isX ? 2 : 1.1\n        };\n    }\n    /**\n     * Set tile shape object on series.\n     * @private\n     */\n    setOptions() {\n        // Call original function\n        const ret = super.setOptions.apply(this, arguments);\n        this.tileShape = Tilemap_TilemapShapes[ret.tileShape];\n        return ret;\n    }\n    /**\n     * Use translate from tileShape.\n     * @private\n     */\n    translate() {\n        return this.tileShape.translate.apply(this, arguments);\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nTilemapSeries.defaultOptions = TilemapSeries_merge(TilemapSeries_HeatmapSeries.defaultOptions, Tilemap_TilemapSeriesDefaults);\nTilemapSeries_extend(TilemapSeries.prototype, {\n    // Revert the noop on getSymbol.\n    getSymbol: TilemapSeries_noop,\n    // Use drawPoints, markerAttribs, pointAttribs methods from the old\n    // heatmap implementation.\n    // TODO: Consider standardizing heatmap and tilemap into more\n    // consistent form.\n    markerAttribs: TilemapSeries_ScatterSeries.prototype.markerAttribs,\n    pointAttribs: ColumnSeries.prototype.pointAttribs,\n    pointClass: Tilemap_TilemapPoint\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('tilemap', TilemapSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Tilemap_TilemapSeries = (TilemapSeries);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * @typedef {\"circle\"|\"diamond\"|\"hexagon\"|\"square\"} Highcharts.TilemapShapeValue\n */\n''; // Keeps doclets above in JS file\n\n;// ./code/es-modules/masters/modules/tilemap.js\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nTilemap_TilemapSeries.compose(G.Axis);\n/* harmony default export */ const tilemap_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__620__", "ColorAxisComposition", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "tilemap_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_", "parse", "color", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default", "addEvent", "extend", "merge", "pick", "splat", "ColorAxisConstructor", "onChartAfterCreateAxes", "userOptions", "colorAxis", "map", "axisOptions", "onLegendAfterGetAllItems", "e", "colorAxes", "chart", "destroyItem", "item", "i", "allItems", "indexOf", "splice", "colorAxisItems", "options", "for<PERSON>ach", "showInLegend", "dataClasses", "visible", "concat", "getDataClassLegendSymbols", "push", "series", "legendType", "points", "point", "length", "unshift", "onLegendAfterColorizeItem", "legendColor", "legendItem", "symbol", "attr", "fill", "onLegendAfterUpdate", "update", "redraw", "onSeriesAfterTranslate", "colorAttribs", "translateColors", "onSeriesBindAxes", "axisTypes", "pointSetVisible", "vis", "method", "Boolean", "buildKDTree", "seriesTranslateColors", "getPointsCollection", "nullColor", "colorKey", "value", "getNestedProperty", "isNull", "toColor", "label", "legend", "colorizeItem", "wrapFxFillSetter", "elem", "start", "tweenTo", "end", "pos", "wrapFxStrokeSetter", "compose", "ColorAxisClass", "ChartClass", "FxClass", "LegendClass", "SeriesClass", "chartProto", "fxProto", "seriesProto", "collectionsWithUpdate", "includes", "collectionsWithInit", "addColorAxis", "wrapChartCreateAxis", "superCreateAxis", "createAxis", "type", "apply", "arguments", "axis", "index", "isX", "isDirtyLegend", "axes", "bindAxes", "isDirtyData", "animation", "fillSetter", "strokeSetter", "optionalAxis", "pointClass", "setVisible", "order", "Color_ColorAxisComposition", "Point", "seriesTypes", "heatmap", "HeatmapPoint", "TilemapPoint_extend", "TilemapPoint", "haloPath", "tileShape", "setState", "noop", "HeatmapSeries", "scatter", "ScatterSeries", "clamp", "TilemapShapes_pick", "tilePaddingFromTileSize", "xDiv", "yDiv", "xPad", "colsize", "yPad", "rowsize", "TilemapShapes", "hexagon", "alignDataLabel", "getSeriesPadding", "size", "tileEdges", "x2", "y1", "x3", "x4", "y2", "y3", "x1", "translate", "yShift", "xAxis", "yAxis", "seriesPointPadding", "pointPadding", "generatePoints", "Math", "floor", "len", "x", "y", "midPointPadding", "abs", "xMidPadding", "reversed", "xPointPadding", "yPointPadding", "round", "plotX", "clientX", "plotY", "shapeType", "shapeArgs", "diamond", "circle", "radius", "yRadius", "colsizePx", "yRadiusPx", "xRadiusPx", "forceNextRadiusCompute", "hasPerPointPadding", "sqrt", "min", "r", "square", "composed", "TilemapSeries_noop", "column", "ColumnSeries", "TilemapSeries_HeatmapSeries", "TilemapSeries_ScatterSeries", "TilemapSeries_addEvent", "TilemapSeries_extend", "TilemapSeries_merge", "pushUnique", "onAxisAfterSetAxisTranslation", "recomputingForTilemap", "coll", "seriesPadding", "getSeriesPixelPadding", "reduce", "b", "padding", "axisLengthFactor", "lengthPadding", "setAxisTranslation", "minPixelPadding", "TilemapSeries", "AxisClass", "drawPoints", "graphic", "styledMode", "isXAxis", "coord1", "coord2", "single", "setOptions", "ret", "Tilemap_TilemapShapes", "defaultOptions", "marker", "states", "hover", "halo", "enabled", "opacity", "attributes", "zIndex", "getSymbol", "markerAttribs", "pointAttribs", "registerSeriesType", "G", "Tilemap_TilemapSeries", "Axis"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,KAAQ,EAC1G,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,6BAA8B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAACA,EAAK,KAAQ,CAAE,GACpI,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,6BAA6B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,KAAQ,EAExIA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,KAAQ,CACpH,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,IACvG,AAAC,CAAA,KACP,aACA,IAoHNC,EApHUC,EAAuB,CAE/B,IACC,AAACT,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIK,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAad,OAAO,CAG5B,IAAIC,EAASU,CAAwB,CAACE,EAAS,CAAG,CAGjDb,QAAS,CAAC,CACX,EAMA,OAHAU,CAAmB,CAACG,EAAS,CAACZ,EAAQA,EAAOD,OAAO,CAAEY,GAG/CX,EAAOD,OAAO,AACtB,CAMCY,EAAoBI,CAAC,CAAG,AAACf,IACxB,IAAIgB,EAAShB,GAAUA,EAAOiB,UAAU,CACvC,IAAOjB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAW,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACnB,EAASqB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACvB,EAASsB,IAC5EE,OAAOC,cAAc,CAACzB,EAASsB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAE7KE,EAA+F3B,EAAoB,KAcvH,GAAM,CAAE4B,MAAOC,CAAK,CAAE,CAAIC,AAb6F9B,EAAoBI,CAAC,CAACuB,KAevI,CAAEI,SAAAA,CAAQ,CAAEC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAEC,MAAAA,CAAK,CAAE,CAAIX,KAOlD,AAAC,SAAU3B,CAAoB,EAW3B,IAAIuC,EAwCJ,SAASC,IACL,GAAM,CAAEC,YAAAA,CAAW,CAAE,CAAG,IAAI,AAC5B,CAAA,IAAI,CAACC,SAAS,CAAG,EAAE,CAGfD,EAAYC,SAAS,GACrBD,EAAYC,SAAS,CAAGJ,EAAMG,EAAYC,SAAS,EACnDD,EAAYC,SAAS,CAACC,GAAG,CAAC,AAACC,GAAiB,IAAIL,EAAqB,IAAI,CAAEK,IAEnF,CAMA,SAASC,EAAyBC,CAAC,EAC/B,IAAMC,EAAY,IAAI,CAACC,KAAK,CAACN,SAAS,EAAI,EAAE,CAAEO,EAAc,AAACC,IACzD,IAAMC,EAAIL,EAAEM,QAAQ,CAACC,OAAO,CAACH,EACnB,CAAA,KAANC,IAEA,IAAI,CAACF,WAAW,CAACH,EAAEM,QAAQ,CAACD,EAAE,EAC9BL,EAAEM,QAAQ,CAACE,MAAM,CAACH,EAAG,GAE7B,EACII,EAAiB,EAAE,CAAEC,EAASL,EA8BlC,IA7BAJ,EAAUU,OAAO,CAAC,SAAUf,CAAS,EACjCc,EAAUd,EAAUc,OAAO,CACvBA,GAASE,eAELF,EAAQG,WAAW,EAAIH,EAAQI,OAAO,CACtCL,EAAiBA,EAAeM,MAAM,CAACnB,EAAUoB,yBAAyB,IAGrEN,EAAQI,OAAO,EAEpBL,EAAeQ,IAAI,CAACrB,GAIxBA,EAAUsB,MAAM,CAACP,OAAO,CAAC,SAAUO,CAAM,EACjC,CAAA,CAACA,EAAOR,OAAO,CAACE,YAAY,EAAIF,EAAQG,WAAW,AAAD,IAC9CK,AAA8B,UAA9BA,EAAOR,OAAO,CAACS,UAAU,CACzBD,EAAOE,MAAM,CAACT,OAAO,CAAC,SAAUU,CAAK,EACjClB,EAAYkB,EAChB,GAGAlB,EAAYe,GAGxB,GAER,GACAb,EAAII,EAAea,MAAM,CAClBjB,KACHL,EAAEM,QAAQ,CAACiB,OAAO,CAACd,CAAc,CAACJ,EAAE,CAE5C,CAIA,SAASmB,EAA0BxB,CAAC,EAC5BA,EAAEc,OAAO,EAAId,EAAEI,IAAI,CAACqB,WAAW,EAC/BzB,EAAEI,IAAI,CAACsB,UAAU,CAACC,MAAM,CAACC,IAAI,CAAC,CAC1BC,KAAM7B,EAAEI,IAAI,CAACqB,WAAW,AAC5B,EAER,CAKA,SAASK,EAAoB9B,CAAC,EAC1B,IAAI,CAACE,KAAK,CAACN,SAAS,EAAEe,QAAQ,AAACf,IAC3BA,EAAUmC,MAAM,CAAC,CAAC,EAAG/B,EAAEgC,MAAM,CACjC,EACJ,CAKA,SAASC,IACD,CAAA,IAAI,CAAC/B,KAAK,CAACN,SAAS,EAAE0B,QACtB,IAAI,CAACY,YAAY,AAAD,GAChB,IAAI,CAACC,eAAe,EAE5B,CAKA,SAASC,IACL,IAAMC,EAAY,IAAI,CAACA,SAAS,CAC3BA,EAGIA,AAAmC,KAAnCA,EAAU9B,OAAO,CAAC,cACvB8B,EAAUpB,IAAI,CAAC,aAHf,IAAI,CAACoB,SAAS,CAAG,CAAC,YAAY,AAKtC,CAOA,SAASC,EAAgBC,CAAG,EACxB,IAAMlB,EAAQ,IAAI,CAAEmB,EAASD,EAAM,OAAS,MAC5ClB,CAAAA,EAAMP,OAAO,CAAGO,EAAMX,OAAO,CAACI,OAAO,CAAG2B,CAAAA,CAAQF,EAEhD,CAAC,UAAW,YAAY,CAAC5B,OAAO,CAAC,SAAU5C,CAAG,EACtCsD,CAAK,CAACtD,EAAI,EACVsD,CAAK,CAACtD,EAAI,CAACyE,EAAO,EAE1B,GACA,IAAI,CAACtB,MAAM,CAACwB,WAAW,EAC3B,CAQA,SAASC,IACL,IAAMzB,EAAS,IAAI,CAAEE,EAAS,IAAI,CAACwB,mBAAmB,GACtDC,EAAY,IAAI,CAACnC,OAAO,CAACmC,SAAS,CAAEjD,EAAY,IAAI,CAACA,SAAS,CAAEkD,EAAW,IAAI,CAACA,QAAQ,CACxF1B,EAAOT,OAAO,CAAC,AAACU,IACZ,IAAM0B,EAAQ1B,EAAM2B,iBAAiB,CAACF,GAAW5D,EAAQmC,EAAMX,OAAO,CAACxB,KAAK,EAAKmC,CAAAA,EAAM4B,MAAM,EAAI5B,AAAgB,OAAhBA,EAAM0B,KAAK,CACxGF,EACA,AAACjD,GAAa,AAAiB,KAAA,IAAVmD,EACjBnD,EAAUsD,OAAO,CAACH,EAAO1B,GACzBA,EAAMnC,KAAK,EAAIgC,EAAOhC,KAAK,AAAD,EAC9BA,GAASmC,EAAMnC,KAAK,GAAKA,IACzBmC,EAAMnC,KAAK,CAAGA,EACVgC,AAA8B,UAA9BA,EAAOR,OAAO,CAACS,UAAU,EACzBE,EAAMK,UAAU,EAChBL,EAAMK,UAAU,CAACyB,KAAK,EACtBjC,EAAOhB,KAAK,CAACkD,MAAM,CAACC,YAAY,CAAChC,EAAOA,EAAMP,OAAO,EAGjE,EACJ,CAkCA,SAASwC,IACL,IAAI,CAACC,IAAI,CAAC3B,IAAI,CAAC,OAAQ1C,EAAM,IAAI,CAACsE,KAAK,EAAEC,OAAO,CAACvE,EAAM,IAAI,CAACwE,GAAG,EAAG,IAAI,CAACC,GAAG,EAAG,KAAK,EAAG,CAAA,EACzF,CAKA,SAASC,IACL,IAAI,CAACL,IAAI,CAAC3B,IAAI,CAAC,SAAU1C,EAAM,IAAI,CAACsE,KAAK,EAAEC,OAAO,CAACvE,EAAM,IAAI,CAACwE,GAAG,EAAG,IAAI,CAACC,GAAG,EAAG,KAAK,EAAG,CAAA,EAC3F,CA9LAzG,EAAqB2G,OAAO,CA1B5B,SAAiBC,CAAc,CAAEC,CAAU,CAAEC,CAAO,CAAEC,CAAW,CAAEC,CAAW,EAC1E,IAAMC,EAAaJ,EAAWxF,SAAS,CAAE6F,EAAUJ,EAAQzF,SAAS,CAAE8F,EAAcH,EAAY3F,SAAS,CACpG4F,EAAWG,qBAAqB,CAACC,QAAQ,CAAC,eAC3C9E,EAAuBqE,EACvBK,EAAWG,qBAAqB,CAACrD,IAAI,CAAC,aACtCkD,EAAWK,mBAAmB,CAAC5E,SAAS,CAAG,CACvCuE,EAAWM,YAAY,CAC1B,CACDrF,EAAS2E,EAAY,kBAAmBrE,GACxCgF,AAwKR,SAA6BX,CAAU,EACnC,IAAMY,EAAkBZ,EAAWxF,SAAS,CAACqG,UAAU,AACvDb,CAAAA,EAAWxF,SAAS,CAACqG,UAAU,CAAG,SAAUC,CAAI,CAAEnE,CAAO,EAErD,GAAImE,AAAS,cAATA,EACA,OAAOF,EAAgBG,KAAK,CAFlB,IAAI,CAEsBC,WAExC,IAAMC,EAAO,IAAIvF,EAJH,IAAI,CAI2BH,EAAMoB,EAAQsE,IAAI,CAAE,CAC7DC,MAAO/E,AALG,IAAI,AAKF,CAAC2E,EAAK,CAACvD,MAAM,CACzB4D,IAAK,CAAA,CACT,IAaA,OAZAhF,AARc,IAAI,CAQZiF,aAAa,CAAG,CAAA,EAEtBjF,AAVc,IAAI,CAUZkF,IAAI,CAACzE,OAAO,CAAC,AAACqE,IAChBA,EAAK9D,MAAM,CAAG,EAAE,AACpB,GACAhB,AAbc,IAAI,CAaZgB,MAAM,CAACP,OAAO,CAAC,AAACO,IAClBA,EAAOmE,QAAQ,GACfnE,EAAOoE,WAAW,CAAG,CAAA,CACzB,GACI/F,EAAKmB,EAAQsB,MAAM,CAAE,CAAA,IACrB9B,AAlBU,IAAI,CAkBR8B,MAAM,CAACtB,EAAQ6E,SAAS,EAE3BP,CACX,CACJ,EAjM4BjB,GACpBK,EAAQoB,UAAU,CAAGlC,EACrBc,EAAQqB,YAAY,CAAG7B,EACvBxE,EAAS6E,EAAa,mBAAoBlE,GAC1CX,EAAS6E,EAAa,oBAAqBzC,GAC3CpC,EAAS6E,EAAa,cAAenC,GACrCzC,EAAOgF,EAAa,CAChBqB,aAAc,YACdvD,gBAAiBQ,CACrB,GACAtD,EAAOgF,EAAYsB,UAAU,CAACpH,SAAS,CAAE,CACrCqH,WAAYtD,CAChB,GACAlD,EAAS8E,EAAa,iBAAkBjC,EAAwB,CAAE4D,MAAO,CAAE,GAC3EzG,EAAS8E,EAAa,WAAY9B,GAE1C,EA2HAlF,EAAqBoF,eAAe,CAAGA,CAqE3C,EAAGpF,GAAyBA,CAAAA,EAAuB,CAAC,CAAA,GAMvB,IAAM4I,EAA8B5I,EAkB3D,CAAEgE,OAAQ,CAAE3C,UAAW,CAAEoH,WAAYI,CAAK,CAAE,CAAE,CAAEC,YAAa,CAAEC,QAAS,CAAE1H,UAAW,CAAEoH,WAAYO,CAAY,CAAE,CAAE,CAAE,CAAE,CAAInH,IAE3H,CAAEM,OAAQ8G,CAAmB,CAAE,CAAItH,GAMzC,OAAMuH,UAAqBF,EAUvBG,UAAW,CACP,OAAO,IAAI,CAACnF,MAAM,CAACoF,SAAS,CAACD,QAAQ,CAACvB,KAAK,CAAC,IAAI,CAAEC,UACtD,CACJ,CACAoB,EAAoBC,EAAa7H,SAAS,CAAE,CACxCgI,SAAUR,EAAMxH,SAAS,CAACgI,QAAQ,CAClCX,WAAYE,EAA2BxD,eAAe,AAC1D,GAqPA,GAAM,CAAEkE,KAAAA,CAAI,CAAE,CAAI3H,IAEZ,CAAEoH,QAASQ,CAAa,CAAEC,QAASC,CAAa,CAAE,CAAG,AAAC5H,IAA2IiH,WAAW,CAE5M,CAAEY,MAAAA,CAAK,CAAErH,KAAMsH,CAAkB,CAAE,CAAIhI,IAU7C,SAASiI,EAAwB5F,CAAM,CAAE6F,CAAI,CAAEC,CAAI,EAC/C,IAAMtG,EAAUQ,EAAOR,OAAO,CAC9B,MAAO,CACHuG,KAAM,CAAA,CAAA,AAACvG,CAAAA,EAAQwG,OAAO,EAAI,CAAA,EAAMH,CAAG,EACnCI,KAAM,CAAA,CAAA,AAACzG,CAAAA,EAAQ0G,OAAO,EAAI,CAAA,EAAMJ,CAAG,CACvC,CACJ,CAUA,IAAMK,EAAgB,CAElBC,QAAS,CACLC,eAAgBZ,EAAcpI,SAAS,CAACgJ,cAAc,CACtDC,iBAAkB,SAAUtG,CAAM,EAC9B,OAAO4F,EAAwB5F,EAAQ,EAAG,EAC9C,EACAmF,SAAU,SAAUoB,CAAI,EACpB,GAAI,CAACA,EACD,MAAO,EAAE,CAEb,IAAMH,EAAU,IAAI,CAACI,SAAS,CAC9B,MAAO,CACH,CAAC,IAAKJ,EAAQK,EAAE,CAAGF,EAAMH,EAAQM,EAAE,CAAGH,EAAK,CAC3C,CAAC,IAAKH,EAAQO,EAAE,CAAGJ,EAAMH,EAAQM,EAAE,CAAGH,EAAK,CAC3C,CAAC,IAAKH,EAAQQ,EAAE,CAAGL,AAAO,IAAPA,EAAYH,EAAQS,EAAE,CAAC,CAC1C,CAAC,IAAKT,EAAQO,EAAE,CAAGJ,EAAMH,EAAQU,EAAE,CAAGP,EAAK,CAC3C,CAAC,IAAKH,EAAQK,EAAE,CAAGF,EAAMH,EAAQU,EAAE,CAAGP,EAAK,CAC3C,CAAC,IAAKH,EAAQW,EAAE,CAAGR,AAAO,IAAPA,EAAYH,EAAQS,EAAE,CAAC,CAC1C,CAAC,IAAI,CACR,AACL,EACAG,UAAW,WACP,IACIC,EADiBzH,EAAUQ,AAAhB,IAAI,CAAmBR,OAAO,CAAE0H,EAAQlH,AAAxC,IAAI,CAA2CkH,KAAK,CAAEC,EAAQnH,AAA9D,IAAI,CAAiEmH,KAAK,CAAEC,EAAqB5H,EAAQ6H,YAAY,EAAI,EAAGtB,EAAO,AAACvG,CAAAA,EAAQwG,OAAO,EAAI,CAAA,EAAK,EAAGC,EAAO,AAACzG,CAAAA,EAAQ0G,OAAO,EAAI,CAAA,EAAK,EAG9M,IAAK,IAAM/F,KADXH,AAFe,IAAI,CAEZsH,cAAc,GACDtH,AAHL,IAAI,CAGQE,MAAM,EAAE,CAC/B,IAAI6G,EAAKrB,EAAM6B,KAAKC,KAAK,CAACN,EAAMO,GAAG,CAC/BP,EAAMF,SAAS,CAAC7G,EAAMuH,CAAC,CAAG3B,AAAO,EAAPA,EAAU,EAAG,EAAG,EAAG,IAAK,CAACmB,EAAMO,GAAG,CAAE,EAAIP,EAAMO,GAAG,EAAGhB,EAAKf,EAAM6B,KAAKC,KAAK,CAACN,EAAMO,GAAG,CAC7GP,EAAMF,SAAS,CAAC7G,EAAMuH,CAAC,CAAG3B,EAAM,EAAG,EAAG,EAAG,IAAK,CAACmB,EAAMO,GAAG,CAAE,EAAIP,EAAMO,GAAG,EAAGd,EAAKjB,EAAM6B,KAAKC,KAAK,CAACN,EAAMO,GAAG,CACzGP,EAAMF,SAAS,CAAC7G,EAAMuH,CAAC,CAAG3B,EAAM,EAAG,EAAG,EAAG,IAAK,CAACmB,EAAMO,GAAG,CAAE,EAAIP,EAAMO,GAAG,EAAGb,EAAKlB,EAAM6B,KAAKC,KAAK,CAACN,EAAMO,GAAG,CACzGP,EAAMF,SAAS,CAAC7G,EAAMuH,CAAC,CAAG3B,AAAO,EAAPA,EAAU,EAAG,EAAG,EAAG,IAAK,CAACmB,EAAMO,GAAG,CAAE,EAAIP,EAAMO,GAAG,EAAGf,EAAKhB,EAAM6B,KAAKC,KAAK,CAACL,EAAMH,SAAS,CAAC7G,EAAMwH,CAAC,CAAG1B,EAAM,EAAG,EAAG,EAAG,IAAK,CAACkB,EAAMM,GAAG,CAAE,EAAIN,EAAMM,GAAG,EAAGZ,EAAKnB,EAAM6B,KAAKC,KAAK,CAACL,EAAMH,SAAS,CAAC7G,EAAMwH,CAAC,CAAE,EAAG,EAAG,EAAG,IAAK,CAACR,EAAMM,GAAG,CAAE,EAAIN,EAAMM,GAAG,EAAGX,EAAKpB,EAAM6B,KAAKC,KAAK,CAACL,EAAMH,SAAS,CAAC7G,EAAMwH,CAAC,CAAG1B,EAAM,EAAG,EAAG,EAAG,IAAK,CAACkB,EAAMM,GAAG,CAAE,EAAIN,EAAMM,GAAG,EAClWJ,EAAelH,EAAMkH,YAAY,EAAID,EAG3CQ,EAAkBP,EACdE,KAAKM,GAAG,CAACpB,EAAKM,GAAMQ,KAAKM,GAAG,CAACf,EAAKD,GAAKiB,EAAcZ,EAAMa,QAAQ,CACnE,CAACH,EAAkBA,EAAiBI,EAAgBd,EAAMa,QAAQ,CAClE,CAACV,EAAeA,EAAcY,EAAgBd,EAAMY,QAAQ,CAC5D,CAACV,EAAeA,CAEhBlH,CAAAA,EAAMuH,CAAC,CAAG,IACVT,EAASA,GAAUM,KAAKW,KAAK,CAACX,KAAKM,GAAG,CAACf,EAAKJ,GAAM,GAE7CS,CAAAA,EAAMY,QAAQ,CAAG,GAAK,CAAA,EAC3BrB,GAAMO,EACNJ,GAAMI,EACNH,GAAMG,GAGV9G,EAAMgI,KAAK,CAAGhI,EAAMiI,OAAO,CAAG,AAAC3B,CAAAA,EAAKE,CAAC,EAAK,EAC1CxG,EAAMkI,KAAK,CAAGxB,EASd1G,EAAMqG,SAAS,CAAG,CACdO,GARJA,GAAMe,EAAcE,EAQRvB,GAPZA,GAAMuB,EAOcrB,GANpBA,GAAMqB,EAMsBpB,GAL5BA,GAAMkB,EAAcE,EAKgBtB,GAJpCA,GAAMuB,EAIsCpB,GAAIA,EAAIC,GAHpDA,GAAMmB,CAIN,EAEA9H,EAAMmI,SAAS,CAAG,OAClBnI,EAAMoI,SAAS,CAAG,CACd7L,EAAG,CACC,CAAC,IAAK+J,EAAIC,EAAG,CACb,CAAC,IAAKC,EAAID,EAAG,CACb,CAAC,IAAKE,EAAIC,EAAG,CACb,CAAC,IAAKF,EAAIG,EAAG,CACb,CAAC,IAAKL,EAAIK,EAAG,CACb,CAAC,IAAKC,EAAIF,EAAG,CACb,CAAC,IAAI,CACR,AACL,CACJ,CACA7G,AAtDe,IAAI,CAsDZiB,eAAe,EAC1B,CACJ,EAEAuH,QAAS,CACLnC,eAAgBZ,EAAcpI,SAAS,CAACgJ,cAAc,CACtDC,iBAAkB,SAAUtG,CAAM,EAC9B,OAAO4F,EAAwB5F,EAAQ,EAAG,EAC9C,EACAmF,SAAU,SAAUoB,CAAI,EACpB,GAAI,CAACA,EACD,MAAO,EAAE,CAEb,IAAMiC,EAAU,IAAI,CAAChC,SAAS,CAC9B,MAAO,CACH,CAAC,IAAKgC,EAAQ/B,EAAE,CAAE+B,EAAQ9B,EAAE,CAAGH,EAAK,CACpC,CAAC,IAAKiC,EAAQ7B,EAAE,CAAGJ,EAAMiC,EAAQ3B,EAAE,CAAC,CACpC,CAAC,IAAK2B,EAAQ/B,EAAE,CAAE+B,EAAQ1B,EAAE,CAAGP,EAAK,CACpC,CAAC,IAAKiC,EAAQzB,EAAE,CAAGR,EAAMiC,EAAQ3B,EAAE,CAAC,CACpC,CAAC,IAAI,CACR,AACL,EACAG,UAAW,WACP,IACIC,EADiBzH,EAAUQ,AAAhB,IAAI,CAAmBR,OAAO,CAAE0H,EAAQlH,AAAxC,IAAI,CAA2CkH,KAAK,CAAEC,EAAQnH,AAA9D,IAAI,CAAiEmH,KAAK,CAAEC,EAAqB5H,EAAQ6H,YAAY,EAAI,EAAGtB,EAAQvG,EAAQwG,OAAO,EAAI,EAAIC,EAAO,AAACzG,CAAAA,EAAQ0G,OAAO,EAAI,CAAA,EAAK,EAG1M,IAAK,IAAM/F,KADXH,AAFe,IAAI,CAEZsH,cAAc,GACDtH,AAHL,IAAI,CAGQE,MAAM,EAAE,CAC/B,IAAI6G,EAAKrB,EAAM6B,KAAKW,KAAK,CAAChB,EAAMO,GAAG,CAC/BP,EAAMF,SAAS,CAAC7G,EAAMuH,CAAC,CAAG3B,EAAM,EAAG,EAAG,EAAG,IAAK,CAACmB,EAAMO,GAAG,CAAE,EAAIP,EAAMO,GAAG,EAAGd,EAAKjB,EAAM6B,KAAKW,KAAK,CAAChB,EAAMO,GAAG,CACzGP,EAAMF,SAAS,CAAC7G,EAAMuH,CAAC,CAAG3B,EAAM,EAAG,EAAG,EAAG,IAAK,CAACmB,EAAMO,GAAG,CAAE,EAAIP,EAAMO,GAAG,EAAGf,EAAKhB,EAAM6B,KAAKW,KAAK,CAACf,EAAMH,SAAS,CAAC7G,EAAMwH,CAAC,CAAG1B,EAAM,EAAG,EAAG,EAAG,IAAK,CAACkB,EAAMM,GAAG,CAAE,EAAIN,EAAMM,GAAG,EAAGZ,EAAKnB,EAAM6B,KAAKW,KAAK,CAACf,EAAMH,SAAS,CAAC7G,EAAMwH,CAAC,CAAE,EAAG,EAAG,EAAG,IAAK,CAACR,EAAMM,GAAG,CAAE,EAAIN,EAAMM,GAAG,EAAGX,EAAKpB,EAAM6B,KAAKW,KAAK,CAACf,EAAMH,SAAS,CAAC7G,EAAMwH,CAAC,CAAG1B,EAAM,EAAG,EAAG,EAAG,IAAK,CAACkB,EAAMM,GAAG,CAAE,EAAIN,EAAMM,GAAG,EAC9VhB,EAAKf,EAAM6B,KAAKW,KAAK,CAAChB,EAAMO,GAAG,CACjCP,EAAMF,SAAS,CAAC7G,EAAMuH,CAAC,CAAE,EAAG,EAAG,EAAG,IAAK,CAACR,EAAMO,GAAG,CAAE,EAAIP,EAAMO,GAAG,EAAGJ,EAAe1B,EAAmBxF,EAAMkH,YAAY,CAAED,GAG7HQ,EAAkBP,EACdE,KAAKM,GAAG,CAACpB,EAAKM,GAAMQ,KAAKM,GAAG,CAACf,EAAKD,GAAKmB,EAAgBd,EAAMa,QAAQ,CACrE,CAACH,EAAkBA,EAAiBK,EAAgBd,EAAMY,QAAQ,CAClE,CAACV,EAAeA,CAGhBlH,CAAAA,EAAMuH,CAAC,CAAG,IACVT,EAASM,KAAKM,GAAG,CAACf,EAAKJ,GAAM,EAAKS,CAAAA,EAAMY,QAAQ,CAAG,GAAK,CAAA,EACxDrB,GAAMO,EACNJ,GAAMI,EACNH,GAAMG,GAGV9G,EAAMgI,KAAK,CAAGhI,EAAMiI,OAAO,CAAG3B,EAC9BtG,EAAMkI,KAAK,CAAGxB,EAOd1G,EAAMqG,SAAS,CAAG,CACdO,GANJA,GAAMiB,EAMMvB,GAAIA,EAAIE,GALpBA,GAAMqB,EAKsBtB,GAJ5BA,GAAMuB,EAI8BpB,GAAIA,EAAIC,GAH5CA,GAAMmB,CAIN,EAEA9H,EAAMmI,SAAS,CAAG,OAClBnI,EAAMoI,SAAS,CAAG,CACd7L,EAAG,CACC,CAAC,IAAK+J,EAAIC,EAAG,CACb,CAAC,IAAKC,EAAIE,EAAG,CACb,CAAC,IAAKJ,EAAIK,EAAG,CACb,CAAC,IAAKC,EAAIF,EAAG,CACb,CAAC,IAAI,CACR,AACL,CACJ,CACA7G,AA/Ce,IAAI,CA+CZiB,eAAe,EAC1B,CACJ,EAEAwH,OAAQ,CACJpC,eAAgBZ,EAAcpI,SAAS,CAACgJ,cAAc,CACtDC,iBAAkB,SAAUtG,CAAM,EAC9B,OAAO4F,EAAwB5F,EAAQ,EAAG,EAC9C,EACAmF,SAAU,SAAUoB,CAAI,EACpB,OAAOd,EAAcpI,SAAS,CAACoH,UAAU,CAACpH,SAAS,CAAC8H,QAAQ,CACvD5H,IAAI,CAAC,IAAI,CAAEgJ,EAAQA,CAAAA,GAAQ,IAAI,CAACmC,MAAM,AAAD,EAC9C,EACA1B,UAAW,WACP,IAAqBxH,EAAUQ,AAAhB,IAAI,CAAmBR,OAAO,CAAE0H,EAAQlH,AAAxC,IAAI,CAA2CkH,KAAK,CAAEC,EAAQnH,AAA9D,IAAI,CAAiEmH,KAAK,CAAEC,EAAqB5H,EAAQ6H,YAAY,EAAI,EAAGsB,EAAU,AAACnJ,CAAAA,EAAQ0G,OAAO,EAAI,CAAA,EAAK,EAAGF,EAAWxG,EAAQwG,OAAO,EAAI,EAC3M4C,EAAWC,EAAWC,EAAWJ,EAAQK,EAAyB,CAAA,EAEtE,IAAK,IAAM5I,KADXH,AAFe,IAAI,CAEZsH,cAAc,GACDtH,AAHL,IAAI,CAGQE,MAAM,EAAE,CAC/B,IAAMwH,EAAIhC,EAAM6B,KAAKW,KAAK,CAAChB,EAAMO,GAAG,CAChCP,EAAMF,SAAS,CAAC7G,EAAMuH,CAAC,CAAE,EAAG,EAAG,EAAG,IAAK,CAACR,EAAMO,GAAG,CAAE,EAAIP,EAAMO,GAAG,EAChEJ,EAAeD,EAAoB4B,EAAqB,CAAA,EAAOrB,EAAIjC,EAAM6B,KAAKW,KAAK,CAACf,EAAMH,SAAS,CAAC7G,EAAMwH,CAAC,CAAE,EAAG,EAAG,EAAG,IAAK,CAACR,EAAMM,GAAG,CAAE,EAAIN,EAAMM,GAAG,CAEtH,MAAA,IAAvBtH,EAAMkH,YAAY,GACzBA,EAAelH,EAAMkH,YAAY,CACjC2B,EAAqB,CAAA,EACrBD,EAAyB,CAAA,GAoBzB,CAAA,CAACL,GAAUK,CAAqB,IAIhCD,EAAYvB,KAAKC,KAAK,CAACD,KAAK0B,IAAI,CAAEL,AAHlCA,CAAAA,EAAYrB,KAAKM,GAAG,CAACnC,EAAM6B,KAAKC,KAAK,CAACN,EAAMO,GAAG,CAC3CP,EAAMF,SAAS,CAAC7G,EAAMuH,CAAC,CAAG1B,EAAS,EAAG,EAAG,EAAG,IAAK,CAACkB,EAAMO,GAAG,CAAE,EAAIP,EAAMO,GAAG,EAAIC,EAAC,EAErCkB,EAAYC,AAD1DA,CAAAA,EAAYtB,KAAKM,GAAG,CAACnC,EAAM6B,KAAKC,KAAK,CAACL,EAAMH,SAAS,CAAC7G,EAAMwH,CAAC,CAAGgB,EAAS,EAAG,EAAG,EAAG,IAAK,CAACxB,EAAMM,GAAG,CAAE,EAAIN,EAAMM,GAAG,EAAIE,EAAC,EAC/CkB,GAAc,GACpFH,EAASnB,KAAK2B,GAAG,CAACN,EAAWE,EAAWD,GAAaxB,EAKjD0B,GAA0B,CAACC,GAC3BD,CAAAA,EAAyB,CAAA,CAAI,GAMjC5I,EAAMuH,CAAC,CAAG,GACVC,CAAAA,GAAKkB,EAAa1B,CAAAA,EAAMY,QAAQ,CAAG,GAAK,CAAA,CAAC,EAG7C5H,EAAMgI,KAAK,CAAGhI,EAAMiI,OAAO,CAAGV,EAC9BvH,EAAMkI,KAAK,CAAGV,EAEdxH,EAAMuI,MAAM,CAAGA,EAEfvI,EAAMmI,SAAS,CAAG,SAClBnI,EAAMoI,SAAS,CAAG,CACdb,EAAGA,EACHC,EAAGA,EACHwB,EAAGT,CACP,CACJ,CACA1I,AAhEe,IAAI,CAgEZiB,eAAe,EAC1B,CACJ,EAEAmI,OAAQ,CACJ/C,eAAgBd,EAAclI,SAAS,CAACgJ,cAAc,CACtDW,UAAWzB,EAAclI,SAAS,CAAC2J,SAAS,CAC5CV,iBAAkBhB,EAClBH,SAAUI,EAAclI,SAAS,CAACoH,UAAU,CAACpH,SAAS,CAAC8H,QAAQ,AACnE,CACJ,EAuBM,CAAEkE,SAAAA,CAAQ,CAAE/D,KAAMgE,CAAkB,CAAE,CAAI3L,IAE1C,CAAE4L,OAAQC,CAAY,CAAEzE,QAAS0E,CAA2B,CAAEjE,QAASkE,CAA2B,CAAE,CAAG,AAAC7L,IAA2IiH,WAAW,CAK9P,CAAE5G,SAAUyL,CAAsB,CAAExL,OAAQyL,CAAoB,CAAExL,MAAOyL,CAAmB,CAAEC,WAAAA,CAAU,CAAE,CAAInM,IAYpH,SAASoM,IACL,GAAI,IAAI,CAACC,qBAAqB,EAAI,AAAc,cAAd,IAAI,CAACC,IAAI,CACvC,OAEJ,IAAMnG,EAAO,IAAI,CAEjBoG,EAAgBpG,EAAK9D,MAAM,CACtBrB,GAAG,CAAC,SAAUqB,CAAM,EACrB,OAAOA,EAAOmK,qBAAqB,EAC/BnK,EAAOmK,qBAAqB,CAACrG,EACrC,GACKsG,MAAM,CAAC,SAAUzN,CAAC,CAAE0N,CAAC,EACtB,MAAO,AAAC1N,CAAAA,GAAKA,EAAE2N,OAAO,AAAD,EAAMD,CAAAA,GAAKA,EAAEC,OAAO,AAAD,EACpC3N,EACA0N,CACR,EAAG,KAAK,IACJ,CACIC,QAAS,EACTC,iBAAkB,CACtB,EAAGC,EAAgBjD,KAAKW,KAAK,CAACgC,EAAcI,OAAO,CAAGJ,EAAcK,gBAAgB,CAEpFL,CAAAA,EAAcI,OAAO,GAErBxG,EAAK2D,GAAG,EAAI+C,EACZ1G,EAAKkG,qBAAqB,CAAG,CAAA,EAC7BlG,EAAK2G,kBAAkB,GACvB,OAAO3G,EAAKkG,qBAAqB,CACjClG,EAAK4G,eAAe,EAAIR,EAAcI,OAAO,CAC7CxG,EAAK2D,GAAG,EAAI+C,EAEpB,CAaA,MAAMG,UAAsBlB,EAMxB,OAAO9G,QAAQiI,CAAS,CAAE,CAClBd,EAAWT,EAAU,kBACrBM,EAAuBiB,EAAW,0BAA2Bb,EAErE,CAUA1D,gBAAiB,CACb,OAAO,IAAI,CAACjB,SAAS,CAACiB,cAAc,CAACzC,KAAK,CAAC,IAAI,CAAEC,UACrD,CACAgH,YAAa,CAIT,IAAK,IAAM1K,KADXqJ,EAAanM,SAAS,CAACwN,UAAU,CAACtN,IAAI,CAAC,IAAI,EACvB,IAAI,CAAC2C,MAAM,EACvBC,EAAM2K,OAAO,EACb3K,EAAM2K,OAAO,CAAC,IAAI,CAAC9L,KAAK,CAAC+L,UAAU,CAAG,MAAQ,UAAU,CAAC,IAAI,CAAC/J,YAAY,CAACb,GAGvF,CAKAgK,sBAAsBrG,CAAI,CAAE,CACxB,IAAME,EAAMF,EAAKkH,OAAO,CAAEV,EAAU,IAAI,CAAClF,SAAS,CAACkB,gBAAgB,CAAC,IAAI,EAExE,GAAI,CAACgE,EACD,MAAO,CACHA,QAAS,EACTC,iBAAkB,CACtB,EAIJ,IAAMU,EAAS1D,KAAKW,KAAK,CAACpE,EAAKkD,SAAS,CAAChD,EACrCsG,AAAe,EAAfA,EAAQvE,IAAI,CACZuE,EAAQrE,IAAI,CAAE,EAAG,EAAG,EAAG,IACrBiF,EAAS3D,KAAKW,KAAK,CAACpE,EAAKkD,SAAS,CAAChD,EAAMsG,EAAQvE,IAAI,CAAG,EAAG,EAAG,EAAG,EAAG,IAC1E,MAAO,CACHuE,QAAS,AAACxG,CAAAA,EAAKqH,MAAM,CACjB5D,KAAKM,GAAG,CAACoD,EAASC,GAAU,EAC5B3D,KAAKM,GAAG,CAACoD,EAASC,EAAM,GAAM,EAOlCX,iBAAkBvG,EAAM,EAAI,GAChC,CACJ,CAKAoH,YAAa,CAET,IAAMC,EAAM,KAAK,CAACD,WAAWxH,KAAK,CAAC,IAAI,CAAEC,WAEzC,OADA,IAAI,CAACuB,SAAS,CAAGkG,AAvJmCnF,CAuJd,CAACkF,EAAIjG,SAAS,CAAC,CAC9CiG,CACX,CAKArE,WAAY,CACR,OAAO,IAAI,CAAC5B,SAAS,CAAC4B,SAAS,CAACpD,KAAK,CAAC,IAAI,CAAEC,UAChD,CACJ,CAMA8G,EAAcY,cAAc,CAAG1B,EAAoBJ,EAA4B8B,cAAc,CAvnB/D,CAG1BC,OAAQ,KACRC,OAAQ,CACJC,MAAO,CACHC,KAAM,CACFC,QAAS,CAAA,EACTrF,KAAM,EACNsF,QAAS,GACTC,WAAY,CACRC,OAAQ,CACZ,CACJ,CACJ,CACJ,EAOA1E,aAAc,EAwCdjC,UAAW,SACf,GAyjBAwE,EAAqBe,EAActN,SAAS,CAAE,CAE1C2O,UAAW1C,EAKX2C,cAAevC,EAA4BrM,SAAS,CAAC4O,aAAa,CAClEC,aAAc1C,EAAanM,SAAS,CAAC6O,YAAY,CACjDzH,WA/qBuDS,CAgrB3D,GACArH,IAA0IsO,kBAAkB,CAAC,UAAWxB,GAsBxK,IAAMyB,EAAKzO,IACX0O,AAjB4D1B,EAiBtChI,OAAO,CAACyJ,EAAEE,IAAI,EACP,IAAM7O,EAAgBE,IAGzC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}