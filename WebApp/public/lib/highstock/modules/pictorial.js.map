{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/pictorial\n * @requires highcharts\n *\n * Pictorial graph series type for Highcharts\n *\n * (c) 2010-2025 <PERSON>stein Honsi, Magdalena Gut\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Series\"][\"types\"][\"column\"], root[\"_Highcharts\"][\"Chart\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"Series\"], root[\"_Highcharts\"][\"StackItem\"], root[\"_Highcharts\"][\"SVGRenderer\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/pictorial\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"Series\"],[\"types\"],[\"column\"],amd1[\"Chart\"],amd1[\"SeriesRegistry\"],amd1[\"Series\"],amd1[\"StackItem\"],amd1[\"SVGRenderer\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/pictorial\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Series\"][\"types\"][\"column\"], root[\"_Highcharts\"][\"Chart\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"Series\"], root[\"_Highcharts\"][\"StackItem\"], root[\"_Highcharts\"][\"SVGRenderer\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Series\"][\"types\"][\"column\"], root[\"Highcharts\"][\"Chart\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"Series\"], root[\"Highcharts\"][\"StackItem\"], root[\"Highcharts\"][\"SVGRenderer\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__448__, __WEBPACK_EXTERNAL_MODULE__960__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__820__, __WEBPACK_EXTERNAL_MODULE__184__, __WEBPACK_EXTERNAL_MODULE__540__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 184:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__184__;\n\n/***/ }),\n\n/***/ 448:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__448__;\n\n/***/ }),\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 540:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__540__;\n\n/***/ }),\n\n/***/ 820:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__820__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ }),\n\n/***/ 960:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__960__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ pictorial_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Series\",\"types\",\"column\"],\"commonjs\":[\"highcharts\",\"Series\",\"types\",\"column\"],\"commonjs2\":[\"highcharts\",\"Series\",\"types\",\"column\"],\"root\":[\"Highcharts\",\"Series\",\"types\",\"column\"]}\nvar highcharts_Series_types_column_commonjs_highcharts_Series_types_column_commonjs2_highcharts_Series_types_column_root_Highcharts_Series_types_column_ = __webpack_require__(448);\n;// ./code/es-modules/Extensions/PatternFill.js\n/* *\n *\n *  Module for using patterns or images as point fills.\n *\n *  (c) 2010-2025 Highsoft AS\n *  Author: Torstein Hønsi, Øystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { animObject } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { getOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent, defined, erase, extend, merge, pick, removeEvent, wrap } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\nconst patterns = createPatterns();\n/* *\n *\n *  Functions\n *\n * */\n/** @private */\nfunction compose(ChartClass, SeriesClass, SVGRendererClass) {\n    const PointClass = SeriesClass.prototype.pointClass, pointProto = PointClass.prototype;\n    if (!pointProto.calculatePatternDimensions) {\n        addEvent(ChartClass, 'endResize', onChartEndResize);\n        addEvent(ChartClass, 'redraw', onChartRedraw);\n        extend(pointProto, {\n            calculatePatternDimensions: pointCalculatePatternDimensions\n        });\n        addEvent(PointClass, 'afterInit', onPointAfterInit);\n        addEvent(SeriesClass, 'render', onSeriesRender);\n        wrap(SeriesClass.prototype, 'getColor', wrapSeriesGetColor);\n        // Pattern scale corrections\n        addEvent(SeriesClass, 'afterRender', onPatternScaleCorrection);\n        addEvent(SeriesClass, 'mapZoomComplete', onPatternScaleCorrection);\n        extend(SVGRendererClass.prototype, {\n            addPattern: rendererAddPattern\n        });\n        addEvent(SVGRendererClass, 'complexColor', onRendererComplexColor);\n    }\n}\n/**\n * Add the predefined patterns.\n * @private\n */\nfunction createPatterns() {\n    const patterns = [], colors = getOptions().colors;\n    // Start with subtle patterns\n    let i = 0;\n    for (const pattern of [\n        'M 0 0 L 5 5 M 4.5 -0.5 L 5.5 0.5 M -0.5 4.5 L 0.5 5.5',\n        'M 0 5 L 5 0 M -0.5 0.5 L 0.5 -0.5 M 4.5 5.5 L 5.5 4.5',\n        'M 2 0 L 2 5 M 4 0 L 4 5',\n        'M 0 2 L 5 2 M 0 4 L 5 4',\n        'M 0 1.5 L 2.5 1.5 L 2.5 0 M 2.5 5 L 2.5 3.5 L 5 3.5'\n    ]) {\n        patterns.push({\n            path: pattern,\n            color: colors[i++],\n            width: 5,\n            height: 5,\n            patternTransform: 'scale(1.4 1.4)'\n        });\n    }\n    // Then add the more drastic ones\n    i = 5;\n    for (const pattern of [\n        'M 0 0 L 5 10 L 10 0',\n        'M 3 3 L 8 3 L 8 8 L 3 8 Z',\n        'M 5 5 m -4 0 a 4 4 0 1 1 8 0 a 4 4 0 1 1 -8 0',\n        'M 0 0 L 10 10 M 9 -1 L 11 1 M -1 9 L 1 11',\n        'M 0 10 L 10 0 M -1 1 L 1 -1 M 9 11 L 11 9'\n    ]) {\n        patterns.push({\n            path: pattern,\n            color: colors[i],\n            width: 10,\n            height: 10\n        });\n        i = i + 5;\n    }\n    return patterns;\n}\n/**\n * Utility function to compute a hash value from an object. Modified Java\n * String.hashCode implementation in JS. Use the preSeed parameter to add an\n * additional seeding step.\n *\n * @private\n * @function hashFromObject\n *\n * @param {Object} obj\n *        The javascript object to compute the hash from.\n *\n * @param {boolean} [preSeed=false]\n *        Add an optional preSeed stage.\n *\n * @return {string}\n *         The computed hash.\n */\nfunction hashFromObject(obj, preSeed) {\n    const str = JSON.stringify(obj), strLen = str.length || 0;\n    let hash = 0, i = 0, char, seedStep;\n    if (preSeed) {\n        seedStep = Math.max(Math.floor(strLen / 500), 1);\n        for (let a = 0; a < strLen; a += seedStep) {\n            hash += str.charCodeAt(a);\n        }\n        hash = hash & hash;\n    }\n    for (; i < strLen; ++i) {\n        char = str.charCodeAt(i);\n        hash = ((hash << 5) - hash) + char;\n        hash = hash & hash;\n    }\n    return hash.toString(16).replace('-', '1');\n}\n/**\n * When animation is used, we have to recalculate pattern dimensions after\n * resize, as the bounding boxes are not available until then.\n * @private\n */\nfunction onChartEndResize() {\n    if (this.renderer &&\n        (this.renderer.defIds || []).filter((id) => (id &&\n            id.indexOf &&\n            id.indexOf('highcharts-pattern-') === 0)).length) {\n        // We have non-default patterns to fix. Find them by looping through\n        // all points.\n        for (const series of this.series) {\n            if (series.visible) {\n                for (const point of series.points) {\n                    const colorOptions = point.options && point.options.color;\n                    if (colorOptions &&\n                        colorOptions.pattern) {\n                        colorOptions.pattern\n                            ._width = 'defer';\n                        colorOptions.pattern\n                            ._height = 'defer';\n                    }\n                }\n            }\n        }\n        // Redraw without animation\n        this.redraw(false);\n    }\n}\n/**\n * Add a garbage collector to delete old patterns with autogenerated hashes that\n * are no longer being referenced.\n * @private\n */\nfunction onChartRedraw() {\n    const usedIds = {}, renderer = this.renderer, \n    // Get the autocomputed patterns - these are the ones we might delete\n    patterns = (renderer.defIds || []).filter((pattern) => (pattern.indexOf &&\n        pattern.indexOf('highcharts-pattern-') === 0));\n    if (patterns.length) {\n        // Look through the DOM for usage of the patterns. This can be points,\n        // series, tooltips etc.\n        [].forEach.call(this.renderTo.querySelectorAll('[color^=\"url(\"], [fill^=\"url(\"], [stroke^=\"url(\"]'), (node) => {\n            const id = node.getAttribute('fill') ||\n                node.getAttribute('color') ||\n                node.getAttribute('stroke');\n            if (id) {\n                const sanitizedId = id\n                    .replace(renderer.url, '')\n                    .replace('url(#', '')\n                    .replace(')', '');\n                usedIds[sanitizedId] = true;\n            }\n        });\n        // Loop through the patterns that exist and see if they are used\n        for (const id of patterns) {\n            if (!usedIds[id]) {\n                // Remove id from used id list\n                erase(renderer.defIds, id);\n                // Remove pattern element\n                if (renderer.patternElements[id]) {\n                    renderer.patternElements[id].destroy();\n                    delete renderer.patternElements[id];\n                }\n            }\n        }\n    }\n}\n/**\n * Merge series color options to points.\n * @private\n */\nfunction onPointAfterInit() {\n    const point = this, colorOptions = point.options.color;\n    // Only do this if we have defined a specific color on this point. Otherwise\n    // we will end up trying to re-add the series color for each point.\n    if (colorOptions && colorOptions.pattern) {\n        // Move path definition to object, allows for merge with series path\n        // definition\n        if (typeof colorOptions.pattern.path === 'string') {\n            colorOptions.pattern.path = {\n                d: colorOptions.pattern.path\n            };\n        }\n        // Merge with series options\n        point.color = point.options.color = merge(point.series.options.color, colorOptions);\n    }\n}\n/**\n * Add functionality to SVG renderer to handle patterns as complex colors.\n * @private\n */\nfunction onRendererComplexColor(args) {\n    const color = args.args[0], prop = args.args[1], element = args.args[2], chartIndex = (this.chartIndex || 0);\n    let pattern = color.pattern, value = \"#333333\" /* Palette.neutralColor80 */;\n    // Handle patternIndex\n    if (typeof color.patternIndex !== 'undefined' && patterns) {\n        pattern = patterns[color.patternIndex];\n    }\n    // Skip and call default if there is no pattern\n    if (!pattern) {\n        return true;\n    }\n    // We have a pattern.\n    if (pattern.image ||\n        typeof pattern.path === 'string' ||\n        pattern.path && pattern.path.d) {\n        // Real pattern. Add it and set the color value to be a reference.\n        // Force Hash-based IDs for legend items, as they are drawn before\n        // point render, meaning they are drawn before autocalculated image\n        // width/heights. We don't want them to highjack the width/height for\n        // this ID if it is defined by users.\n        let forceHashId = element.parentNode &&\n            element.parentNode.getAttribute('class');\n        forceHashId = forceHashId &&\n            forceHashId.indexOf('highcharts-legend') > -1;\n        // If we don't have a width/height yet, handle it. Try faking a point\n        // and running the algorithm again.\n        if (pattern._width === 'defer' || pattern._height === 'defer') {\n            pointCalculatePatternDimensions.call({ graphic: { element: element } }, pattern);\n        }\n        // If we don't have an explicit ID, compute a hash from the\n        // definition and use that as the ID. This ensures that points with\n        // the same pattern definition reuse existing pattern elements by\n        // default. We combine two hashes, the second with an additional\n        // preSeed algorithm, to minimize collision probability.\n        if (forceHashId || !pattern.id) {\n            // Make a copy so we don't accidentally edit options when setting ID\n            pattern = merge({}, pattern);\n            pattern.id = 'highcharts-pattern-' + chartIndex + '-' +\n                hashFromObject(pattern) + hashFromObject(pattern, true);\n        }\n        // Add it. This function does nothing if an element with this ID\n        // already exists.\n        this.addPattern(pattern, !this.forExport && pick(pattern.animation, this.globalAnimation, { duration: 100 }));\n        value = `url(${this.url}#${pattern.id + (this.forExport ? '-export' : '')})`;\n    }\n    else {\n        // Not a full pattern definition, just add color\n        value = pattern.color || value;\n    }\n    // Set the fill/stroke prop on the element\n    element.setAttribute(prop, value);\n    // Allow the color to be concatenated into tooltips formatters etc.\n    color.toString = function () {\n        return value;\n    };\n    // Skip default handler\n    return false;\n}\n/**\n * Calculate pattern dimensions on points that have their own pattern.\n * @private\n */\nfunction onSeriesRender() {\n    const isResizing = this.chart.isResizing;\n    if (this.isDirtyData || isResizing || !this.chart.hasRendered) {\n        for (const point of this.points) {\n            const colorOptions = point.options && point.options.color;\n            if (colorOptions &&\n                colorOptions.pattern) {\n                // For most points we want to recalculate the dimensions on\n                // render, where we have the shape args and bbox. But if we\n                // are resizing and don't have the shape args, defer it, since\n                // the bounding box is still not resized.\n                if (isResizing &&\n                    !(point.shapeArgs &&\n                        point.shapeArgs.width &&\n                        point.shapeArgs.height)) {\n                    colorOptions\n                        .pattern._width = 'defer';\n                    colorOptions\n                        .pattern._height = 'defer';\n                }\n                else {\n                    point.calculatePatternDimensions(colorOptions.pattern);\n                }\n            }\n        }\n    }\n}\n/**\n * Set dimensions on pattern from point. This function will set internal\n * pattern._width/_height properties if width and height are not both already\n * set. We only do this on image patterns. The _width/_height properties are set\n * to the size of the bounding box of the point, optionally taking aspect ratio\n * into account. If only one of width or height are supplied as options, the\n * undefined option is calculated as above.\n *\n * @private\n * @function Highcharts.Point#calculatePatternDimensions\n *\n * @param {Highcharts.PatternOptionsObject} pattern\n *        The pattern to set dimensions on.\n *\n * @return {void}\n *\n * @requires modules/pattern-fill\n */\nfunction pointCalculatePatternDimensions(pattern) {\n    if (pattern.width && pattern.height) {\n        return;\n    }\n    const bBox = this.graphic && (this.graphic.getBBox &&\n        this.graphic.getBBox(true) ||\n        this.graphic.element &&\n            this.graphic.element.getBBox()) || {}, shapeArgs = this.shapeArgs;\n    // Prefer using shapeArgs, as it is animation agnostic\n    if (shapeArgs) {\n        bBox.width = shapeArgs.width || bBox.width;\n        bBox.height = shapeArgs.height || bBox.height;\n        bBox.x = shapeArgs.x || bBox.x;\n        bBox.y = shapeArgs.y || bBox.y;\n    }\n    // For images we stretch to bounding box\n    if (pattern.image) {\n        // If we do not have a bounding box at this point, simply add a defer\n        // key and pick this up in the fillSetter handler, where the bounding\n        // box should exist.\n        if (!bBox.width || !bBox.height) {\n            pattern._width = 'defer';\n            pattern._height = 'defer';\n            // Mark the pattern to be flipped later if upside down (#16810)\n            const scaleY = this.series.chart.mapView &&\n                this.series.chart.mapView.getSVGTransform().scaleY;\n            if (defined(scaleY) && scaleY < 0) {\n                pattern._inverted = true;\n            }\n            return;\n        }\n        // Handle aspect ratio filling\n        if (pattern.aspectRatio) {\n            bBox.aspectRatio = bBox.width / bBox.height;\n            if (pattern.aspectRatio > bBox.aspectRatio) {\n                // Height of bBox will determine width\n                bBox.aspectWidth = bBox.height * pattern.aspectRatio;\n            }\n            else {\n                // Width of bBox will determine height\n                bBox.aspectHeight = bBox.width / pattern.aspectRatio;\n            }\n        }\n        // We set the width/height on internal properties to differentiate\n        // between the options set by a user and by this function.\n        pattern._width = pattern.width ||\n            Math.ceil(bBox.aspectWidth || bBox.width);\n        pattern._height = pattern.height ||\n            Math.ceil(bBox.aspectHeight || bBox.height);\n    }\n    // Set x/y accordingly, centering if using aspect ratio, otherwise adjusting\n    // so bounding box corner is 0,0 of pattern.\n    if (!pattern.width) {\n        pattern._x = pattern.x || 0;\n        pattern._x += bBox.x - Math.round(bBox.aspectWidth ?\n            Math.abs(bBox.aspectWidth - bBox.width) / 2 :\n            0);\n    }\n    if (!pattern.height) {\n        pattern._y = pattern.y || 0;\n        pattern._y += bBox.y - Math.round(bBox.aspectHeight ?\n            Math.abs(bBox.aspectHeight - bBox.height) / 2 :\n            0);\n    }\n}\n/**\n * Add a pattern to the renderer.\n *\n * @private\n * @function Highcharts.SVGRenderer#addPattern\n *\n * @param {Highcharts.PatternObject} options\n * The pattern options.\n *\n * @param {boolean|Partial<Highcharts.AnimationOptionsObject>} [animation]\n * The animation options.\n *\n * @return {Highcharts.SVGElement|undefined}\n * The added pattern. Undefined if the pattern already exists.\n *\n * @requires modules/pattern-fill\n */\nfunction rendererAddPattern(options, animation) {\n    const animate = pick(animation, true), animationOptions = animObject(animate), color = options.color || \"#333333\" /* Palette.neutralColor80 */, defaultSize = 32, height = options.height ||\n        (typeof options._height === 'number' ? options._height : 0) ||\n        defaultSize, rect = (fill) => this\n        .rect(0, 0, width, height)\n        .attr({ fill })\n        .add(pattern), width = options.width ||\n        (typeof options._width === 'number' ? options._width : 0) ||\n        defaultSize;\n    let attribs, id = options.id, path;\n    if (!id) {\n        this.idCounter = this.idCounter || 0;\n        id = ('highcharts-pattern-' +\n            this.idCounter +\n            '-' +\n            (this.chartIndex || 0));\n        ++this.idCounter;\n    }\n    if (this.forExport) {\n        id += '-export';\n    }\n    // Do nothing if ID already exists\n    this.defIds = this.defIds || [];\n    if (this.defIds.indexOf(id) > -1) {\n        return;\n    }\n    // Store ID in list to avoid duplicates\n    this.defIds.push(id);\n    // Calculate pattern element attributes\n    const attrs = {\n        id: id,\n        patternUnits: 'userSpaceOnUse',\n        patternContentUnits: options.patternContentUnits || 'userSpaceOnUse',\n        width: width,\n        height: height,\n        x: options._x || options.x || 0,\n        y: options._y || options.y || 0\n    };\n    if (options._inverted) {\n        attrs.patternTransform = 'scale(1, -1)'; // (#16810)\n        if (options.patternTransform) {\n            options.patternTransform += ' scale(1, -1)';\n        }\n    }\n    if (options.patternTransform) {\n        attrs.patternTransform = options.patternTransform;\n    }\n    const pattern = this.createElement('pattern').attr(attrs).add(this.defs);\n    // Set id on the SVGRenderer object\n    pattern.id = id;\n    // Use an SVG path for the pattern\n    if (options.path) {\n        path = highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().isObject(options.path) ?\n            options.path :\n            { d: options.path };\n        // The background\n        if (options.backgroundColor) {\n            rect(options.backgroundColor);\n        }\n        // The pattern\n        attribs = {\n            'd': path.d\n        };\n        if (!this.styledMode) {\n            attribs.stroke = path.stroke || color;\n            attribs['stroke-width'] = pick(path.strokeWidth, 2);\n            attribs.fill = path.fill || 'none';\n        }\n        if (path.transform) {\n            attribs.transform = path.transform;\n        }\n        this.createElement('path').attr(attribs).add(pattern);\n        pattern.color = color;\n        // Image pattern\n    }\n    else if (options.image) {\n        if (animate) {\n            this.image(options.image, 0, 0, width, height, function () {\n                // Onload\n                this.animate({\n                    opacity: pick(options.opacity, 1)\n                }, animationOptions);\n                removeEvent(this.element, 'load');\n            }).attr({ opacity: 0 }).add(pattern);\n        }\n        else {\n            this.image(options.image, 0, 0, width, height).add(pattern);\n        }\n    }\n    // For non-animated patterns, set opacity now\n    if (!(options.image && animate) && typeof options.opacity !== 'undefined') {\n        [].forEach.call(pattern.element.childNodes, (child) => {\n            child.setAttribute('opacity', options.opacity);\n        });\n    }\n    // Store for future reference\n    this.patternElements = this.patternElements || {};\n    this.patternElements[id] = pattern;\n    return pattern;\n}\n/**\n * Make sure we have a series color.\n * @private\n */\nfunction wrapSeriesGetColor(proceed) {\n    const oldColor = this.options.color;\n    // Temporarily remove color options to get defaults\n    if (oldColor &&\n        oldColor.pattern &&\n        !oldColor.pattern.color) {\n        delete this.options.color;\n        // Get default\n        proceed.apply(this, [].slice.call(arguments, 1));\n        // Replace with old, but add default color\n        oldColor.pattern.color =\n            this.color;\n        this.color = this.options.color = oldColor;\n    }\n    else {\n        // We have a color, no need to do anything special\n        proceed.apply(this, [].slice.call(arguments, 1));\n    }\n}\n/**\n * Scale patterns inversely to the series it's used in.\n * Maintains a visual (1,1) scale regardless of size.\n * @private\n */\nfunction onPatternScaleCorrection() {\n    const series = this;\n    // If not a series used in a map chart, skip it.\n    if (!series.chart?.mapView) {\n        return;\n    }\n    const chart = series.chart, renderer = chart.renderer, patterns = renderer.patternElements;\n    // Only scale if we have patterns to scale.\n    if (renderer.defIds?.length && patterns) {\n        // Filter for points which have patterns that don't use images assigned\n        // and has a group scale available.\n        series.points.filter(function (p) {\n            const point = p;\n            // No graphic we can fetch id from, filter out this point.\n            if (!point.graphic) {\n                return false;\n            }\n            return (point.graphic.element.hasAttribute('fill') ||\n                point.graphic.element.hasAttribute('color') ||\n                point.graphic.element.hasAttribute('stroke')) &&\n                !point.options.color?.pattern?.image &&\n                !!point.group?.scaleX &&\n                !!point.group?.scaleY;\n        })\n            // Map up pattern id's and their scales.\n            .map(function (p) {\n            const point = p;\n            // Parse the id from the graphic element of the point.\n            const id = (point.graphic?.element.getAttribute('fill') ||\n                point.graphic?.element.getAttribute('color') ||\n                point.graphic?.element.getAttribute('stroke') || '')\n                .replace(renderer.url, '')\n                .replace('url(#', '')\n                .replace(')', '');\n            return {\n                id,\n                x: point.group?.scaleX || 1,\n                y: point.group?.scaleY || 1\n            };\n        })\n            // Filter out colors and other non-patterns, as well as duplicates.\n            .filter(function (pointInfo, index, arr) {\n            return pointInfo.id !== '' &&\n                pointInfo.id.indexOf('highcharts-pattern-') !== -1 &&\n                !arr.some(function (otherInfo, otherIndex) {\n                    return otherInfo.id === pointInfo.id && otherIndex < index;\n                });\n        })\n            .forEach(function (pointInfo) {\n            const id = pointInfo.id;\n            patterns[id].scaleX = 1 / pointInfo.x;\n            patterns[id].scaleY = 1 / pointInfo.y;\n            patterns[id].updateTransform('patternTransform');\n        });\n    }\n}\n/* *\n *\n *  Export\n *\n * */\nconst PatternFill = {\n    compose,\n    patterns\n};\n/* harmony default export */ const Extensions_PatternFill = (PatternFill);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Pattern options\n *\n * @interface Highcharts.PatternOptionsObject\n */ /**\n* Background color for the pattern if a `path` is set (not images).\n* @name Highcharts.PatternOptionsObject#backgroundColor\n* @type {Highcharts.ColorString|undefined}\n*/ /**\n* URL to an image to use as the pattern.\n* @name Highcharts.PatternOptionsObject#image\n* @type {string|undefined}\n*/ /**\n* Width of the pattern. For images this is automatically set to the width of\n* the element bounding box if not supplied. For non-image patterns the default\n* is 32px. Note that automatic resizing of image patterns to fill a bounding\n* box dynamically is only supported for patterns with an automatically\n* calculated ID.\n* @name Highcharts.PatternOptionsObject#width\n* @type {number|undefined}\n*/ /**\n* Analogous to pattern.width.\n* @name Highcharts.PatternOptionsObject#height\n* @type {number|undefined}\n*/ /**\n* For automatically calculated width and height on images, it is possible to\n* set an aspect ratio. The image will be zoomed to fill the bounding box,\n* maintaining the aspect ratio defined.\n* @name Highcharts.PatternOptionsObject#aspectRatio\n* @type {number|undefined}\n*/ /**\n* Horizontal offset of the pattern. Defaults to 0.\n* @name Highcharts.PatternOptionsObject#x\n* @type {number|undefined}\n*/ /**\n* Vertical offset of the pattern. Defaults to 0.\n* @name Highcharts.PatternOptionsObject#y\n* @type {number|undefined}\n*/ /**\n* Either an SVG path as string, or an object. As an object, supply the path\n* string in the `path.d` property. Other supported properties are standard SVG\n* attributes like `path.stroke` and `path.fill`. If a path is supplied for the\n* pattern, the `image` property is ignored.\n* @name Highcharts.PatternOptionsObject#path\n* @type {string|Highcharts.SVGAttributes|undefined}\n*/ /**\n* SVG `patternTransform` to apply to the entire pattern.\n* @name Highcharts.PatternOptionsObject#patternTransform\n* @type {string|undefined}\n* @see [patternTransform demo](https://jsfiddle.net/gh/get/library/pure/highcharts/highcharts/tree/master/samples/highcharts/series/pattern-fill-transform)\n*/ /**\n* Pattern color, used as default path stroke.\n* @name Highcharts.PatternOptionsObject#color\n* @type {Highcharts.ColorString|undefined}\n*/ /**\n* Opacity of the pattern as a float value from 0 to 1.\n* @name Highcharts.PatternOptionsObject#opacity\n* @type {number|undefined}\n*/ /**\n* ID to assign to the pattern. This is automatically computed if not added, and\n* identical patterns are reused. To refer to an existing pattern for a\n* Highcharts color, use `color: \"url(#pattern-id)\"`.\n* @name Highcharts.PatternOptionsObject#id\n* @type {string|undefined}\n*/\n/**\n * Holds a pattern definition.\n *\n * @sample highcharts/series/pattern-fill-area/\n *         Define a custom path pattern\n * @sample highcharts/series/pattern-fill-pie/\n *         Default patterns and a custom image pattern\n * @sample maps/demo/pattern-fill-map/\n *         Custom images on map\n *\n * @example\n * // Pattern used as a color option\n * color: {\n *     pattern: {\n *            path: {\n *                 d: 'M 3 3 L 8 3 L 8 8 Z',\n *                fill: '#102045'\n *            },\n *            width: 12,\n *            height: 12,\n *            color: '#907000',\n *            opacity: 0.5\n *     }\n * }\n *\n * @interface Highcharts.PatternObject\n */ /**\n* Pattern options\n* @name Highcharts.PatternObject#pattern\n* @type {Highcharts.PatternOptionsObject}\n*/ /**\n* Animation options for the image pattern loading.\n* @name Highcharts.PatternObject#animation\n* @type {boolean|Partial<Highcharts.AnimationOptionsObject>|undefined}\n*/ /**\n* Optionally an index referencing which pattern to use. Highcharts adds\n* 10 default patterns to the `Highcharts.patterns` array. Additional\n* pattern definitions can be pushed to this array if desired. This option\n* is an index into this array.\n* @name Highcharts.PatternObject#patternIndex\n* @type {number|undefined}\n*/\n''; // Keeps doclets above in transpiled file\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Chart\"],\"commonjs\":[\"highcharts\",\"Chart\"],\"commonjs2\":[\"highcharts\",\"Chart\"],\"root\":[\"Highcharts\",\"Chart\"]}\nvar highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_ = __webpack_require__(960);\nvar highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default = /*#__PURE__*/__webpack_require__.n(highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/Pictorial/PictorialUtilities.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi, Magdalena Gut\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nconst { defined: PictorialUtilities_defined } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/**\n *\n */\nfunction rescalePatternFill(element, stackHeight, width, height, borderWidth = 1) {\n    const fill = element && element.attr('fill'), match = fill && fill.match(/url\\(([^)]+)\\)/);\n    if (match) {\n        const patternPath = document.querySelector(`${match[1]} path`);\n        if (patternPath) {\n            let bBox = patternPath.getBBox();\n            // Firefox (v108/Mac) is unable to detect the bounding box within\n            // defs. Without this block, the pictorial is not rendered.\n            if (bBox.width === 0) {\n                const parent = patternPath.parentElement;\n                // Temporarily append it to the root\n                element.renderer.box.appendChild(patternPath);\n                bBox = patternPath.getBBox();\n                parent.appendChild(patternPath);\n            }\n            let scaleX = 1 / (bBox.width + borderWidth);\n            const scaleY = stackHeight / height / bBox.height, aspectRatio = bBox.width / bBox.height, pointAspectRatio = width / stackHeight, x = -bBox.width / 2;\n            if (aspectRatio < pointAspectRatio) {\n                scaleX = scaleX * aspectRatio / pointAspectRatio;\n            }\n            patternPath.setAttribute('stroke-width', borderWidth / (width * scaleX));\n            patternPath.setAttribute('transform', 'translate(0.5, 0)' +\n                `scale(${scaleX} ${scaleY}) ` +\n                `translate(${x + borderWidth * scaleX / 2}, ${-bBox.y})`);\n        }\n    }\n}\n/**\n *\n */\nfunction getStackMetrics(yAxis, shape) {\n    let height = yAxis.len, y = 0;\n    if (shape && PictorialUtilities_defined(shape.max)) {\n        y = yAxis.toPixels(shape.max, true);\n        height = yAxis.len - y;\n    }\n    return {\n        height,\n        y\n    };\n}\n/**\n *\n */\nfunction invertShadowGroup(shadowGroup, yAxis) {\n    const inverted = yAxis.chart.inverted;\n    if (inverted) {\n        shadowGroup.attr({\n            rotation: inverted ? 90 : 0,\n            scaleX: inverted ? -1 : 1\n        });\n    }\n}\n/* harmony default export */ const PictorialUtilities = ({ rescalePatternFill, invertShadowGroup, getStackMetrics });\n\n;// ./code/es-modules/Series/Pictorial/PictorialPoint.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi, Magdalena Gut\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst ColumnPoint = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.column.prototype.pointClass;\nconst { rescalePatternFill: PictorialPoint_rescalePatternFill, getStackMetrics: PictorialPoint_getStackMetrics } = PictorialUtilities;\n/* *\n *\n *  Class\n *\n * */\nclass PictorialPoint extends ColumnPoint {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    setState() {\n        const point = this;\n        super.setState.apply(point, arguments);\n        const series = point.series, paths = series.options.paths;\n        if (point.graphic && point.shapeArgs && paths) {\n            const shape = paths[point.index %\n                paths.length];\n            PictorialPoint_rescalePatternFill(point.graphic, PictorialPoint_getStackMetrics(series.yAxis, shape).height, point.shapeArgs.width || 0, point.shapeArgs.height || Infinity, point.series.options.borderWidth || 0);\n        }\n    }\n}\n/* *\n *\n *  Export Default\n *\n * */\n/* harmony default export */ const Pictorial_PictorialPoint = (PictorialPoint);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Series\"],\"commonjs\":[\"highcharts\",\"Series\"],\"commonjs2\":[\"highcharts\",\"Series\"],\"root\":[\"Highcharts\",\"Series\"]}\nvar highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_ = __webpack_require__(820);\nvar highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default = /*#__PURE__*/__webpack_require__.n(highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"StackItem\"],\"commonjs\":[\"highcharts\",\"StackItem\"],\"commonjs2\":[\"highcharts\",\"StackItem\"],\"root\":[\"Highcharts\",\"StackItem\"]}\nvar highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_ = __webpack_require__(184);\nvar highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_default = /*#__PURE__*/__webpack_require__.n(highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SVGRenderer\"],\"commonjs\":[\"highcharts\",\"SVGRenderer\"],\"commonjs2\":[\"highcharts\",\"SVGRenderer\"],\"root\":[\"Highcharts\",\"SVGRenderer\"]}\nvar highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_ = __webpack_require__(540);\nvar highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default = /*#__PURE__*/__webpack_require__.n(highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_);\n;// ./code/es-modules/Series/Pictorial/PictorialSeries.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi, Magdalena Gut\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\n\n\n\n\n\n\n\n\n\n\nconst ColumnSeries = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.column;\nExtensions_PatternFill.compose((highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default()), (highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default()), (highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default()));\nconst { animObject: PictorialSeries_animObject } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nconst { getStackMetrics: PictorialSeries_getStackMetrics, invertShadowGroup: PictorialSeries_invertShadowGroup, rescalePatternFill: PictorialSeries_rescalePatternFill } = PictorialUtilities;\nconst { addEvent: PictorialSeries_addEvent, defined: PictorialSeries_defined, merge: PictorialSeries_merge, objectEach, pick: PictorialSeries_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The pictorial series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.pictorial\n *\n * @augments Highcharts.Series\n */\nclass PictorialSeries extends ColumnSeries {\n    /* *\n     *\n     * Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * Animate in the series. Called internally twice. First with the `init`\n     * parameter set to true, which sets up the initial state of the\n     * animation. Then when ready, it is called with the `init` parameter\n     * undefined, in order to perform the actual animation.\n     *\n     * @function Highcharts.Series#animate\n     *\n     * @param {boolean} [init]\n     * Initialize the animation.\n     */\n    animate(init) {\n        const { chart, group } = this, animation = PictorialSeries_animObject(this.options.animation), \n        // The key for temporary animation clips\n        animationClipKey = [\n            this.getSharedClipKey(),\n            animation.duration,\n            animation.easing,\n            animation.defer\n        ].join(',');\n        let animationClipRect = chart.sharedClips[animationClipKey];\n        // Initialize the animation. Set up the clipping rectangle.\n        if (init && group) {\n            const clipBox = chart.getClipBox(this);\n            // Create temporary animation clips\n            if (!animationClipRect) {\n                clipBox.y = clipBox.height;\n                clipBox.height = 0;\n                animationClipRect = chart.renderer.clipRect(clipBox);\n                chart.sharedClips[animationClipKey] = animationClipRect;\n            }\n            group.clip(animationClipRect);\n            // Run the animation\n        }\n        else if (animationClipRect &&\n            // Only first series in this pane\n            !animationClipRect.hasClass('highcharts-animating')) {\n            const finalBox = chart.getClipBox(this);\n            animationClipRect\n                .addClass('highcharts-animating')\n                .animate(finalBox, animation);\n        }\n    }\n    animateDrilldown() { }\n    animateDrillupFrom() { }\n    pointAttribs(point) {\n        const pointAttribs = super.pointAttribs.apply(this, arguments), seriesOptions = this.options, series = this, paths = seriesOptions.paths;\n        if (point && point.shapeArgs && paths) {\n            const shape = paths[point.index % paths.length], { y, height } = PictorialSeries_getStackMetrics(series.yAxis, shape), pathDef = shape.definition;\n            // New pattern, replace\n            if (pathDef !== point.pathDef) {\n                point.pathDef = pathDef;\n                pointAttribs.fill = {\n                    pattern: {\n                        path: {\n                            d: pathDef,\n                            fill: pointAttribs.fill,\n                            strokeWidth: pointAttribs['stroke-width'],\n                            stroke: pointAttribs.stroke\n                        },\n                        x: point.shapeArgs.x,\n                        y: y,\n                        width: point.shapeArgs.width || 0,\n                        height: height,\n                        patternContentUnits: 'objectBoundingBox',\n                        backgroundColor: 'none',\n                        color: '#ff0000'\n                    }\n                };\n            }\n            else if (point.pathDef && point.graphic) {\n                delete pointAttribs.fill;\n            }\n        }\n        delete pointAttribs.stroke;\n        delete pointAttribs.strokeWidth;\n        return pointAttribs;\n    }\n    /**\n     * Make sure that path.max is also considered when calculating dataMax.\n     */\n    getExtremes() {\n        const extremes = super.getExtremes.apply(this, arguments), series = this, paths = series.options.paths;\n        if (paths) {\n            paths.forEach(function (path) {\n                if (PictorialSeries_defined(path.max) &&\n                    PictorialSeries_defined(extremes.dataMax) &&\n                    path.max > extremes.dataMax) {\n                    extremes.dataMax = path.max;\n                }\n            });\n        }\n        return extremes;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nPictorialSeries.defaultOptions = PictorialSeries_merge(ColumnSeries.defaultOptions, \n/**\n * A pictorial chart uses vector images to represents the data.\n * The shape of the data point is taken from the path parameter.\n *\n * @sample       {highcharts} highcharts/demo/pictorial/\n *               Pictorial chart\n *\n * @extends      plotOptions.column\n * @since 11.0.0\n * @product      highcharts\n * @excluding    allAreas, borderRadius,\n *               centerInCategory, colorAxis, colorKey, connectEnds,\n *               connectNulls, crisp, compare, compareBase, dataSorting,\n *               dashStyle, dataAsColumns, linecap, lineWidth, shadow,\n *               onPoint\n * @requires     modules/pictorial\n * @optionparent plotOptions.pictorial\n */\n{\n    borderWidth: 0\n});\n/* *\n *\n *  Events\n *\n * */\nPictorialSeries_addEvent(PictorialSeries, 'afterRender', function () {\n    const series = this, paths = series.options.paths, fillUrlMatcher = /url\\(([^)]+)\\)/;\n    series.points.forEach(function (point) {\n        if (point.graphic && point.shapeArgs && paths) {\n            const shape = paths[point.index % paths.length], fill = point.graphic.attr('fill'), match = fill && fill.match(fillUrlMatcher), { y, height } = PictorialSeries_getStackMetrics(series.yAxis, shape);\n            if (match && series.chart.renderer.patternElements) {\n                const currentPattern = series.chart.renderer.patternElements[match[1].slice(1)];\n                if (currentPattern) {\n                    currentPattern.animate({\n                        x: point.shapeArgs.x,\n                        y: y,\n                        width: point.shapeArgs.width || 0,\n                        height: height\n                    });\n                }\n            }\n            PictorialSeries_rescalePatternFill(point.graphic, PictorialSeries_getStackMetrics(series.yAxis, shape).height, point.shapeArgs.width || 0, point.shapeArgs.height || Infinity, series.options.borderWidth || 0);\n        }\n    });\n});\n/**\n *\n */\nfunction renderStackShadow(stack) {\n    // Get first pictorial series\n    const stackKeys = Object\n        .keys(stack.points)\n        .filter((p) => p.split(',').length > 1), allSeries = stack.axis.chart.series, seriesIndexes = stackKeys.map((key) => parseFloat(key.split(',')[0]));\n    let seriesIndex = -1;\n    seriesIndexes.forEach((index) => {\n        if (allSeries[index] && allSeries[index].visible) {\n            seriesIndex = index;\n        }\n    });\n    const series = stack.axis.chart.series[seriesIndex];\n    if (series &&\n        series.is('pictorial') &&\n        stack.axis.hasData() &&\n        series.xAxis.hasData()) {\n        const xAxis = series.xAxis, options = stack.axis.options, chart = stack.axis.chart, stackShadow = stack.shadow, xCenter = xAxis.toPixels(stack.x, true), x = chart.inverted ? xAxis.len - xCenter : xCenter, paths = series.options.paths || [], index = stack.x % paths.length, shape = paths[index], width = series.getColumnMetrics &&\n            series.getColumnMetrics().width, { height, y } = PictorialSeries_getStackMetrics(series.yAxis, shape), shadowOptions = options.stackShadow, strokeWidth = PictorialSeries_pick(shadowOptions && shadowOptions.borderWidth, series.options.borderWidth, 1);\n        if (!stackShadow &&\n            shadowOptions &&\n            shadowOptions.enabled &&\n            shape) {\n            if (!stack.shadowGroup) {\n                stack.shadowGroup = chart.renderer.g('shadow-group')\n                    .add();\n            }\n            stack.shadowGroup.attr({\n                translateX: chart.inverted ?\n                    stack.axis.pos : xAxis.pos,\n                translateY: chart.inverted ?\n                    xAxis.pos : stack.axis.pos\n            });\n            stack.shadow = chart.renderer.rect(x, y, width, height)\n                .attr({\n                fill: {\n                    pattern: {\n                        path: {\n                            d: shape.definition,\n                            fill: shadowOptions.color ||\n                                '#dedede',\n                            strokeWidth: strokeWidth,\n                            stroke: shadowOptions.borderColor ||\n                                'transparent'\n                        },\n                        x: x,\n                        y: y,\n                        width: width,\n                        height: height,\n                        patternContentUnits: 'objectBoundingBox',\n                        backgroundColor: 'none',\n                        color: '#dedede'\n                    }\n                }\n            })\n                .add(stack.shadowGroup);\n            PictorialSeries_invertShadowGroup(stack.shadowGroup, stack.axis);\n            PictorialSeries_rescalePatternFill(stack.shadow, height, width, height, strokeWidth);\n            stack.setOffset(series.pointXOffset || 0, series.barW || 0);\n        }\n        else if (stackShadow && stack.shadowGroup) {\n            stackShadow.animate({\n                x,\n                y,\n                width,\n                height\n            });\n            const fillUrlMatcher = /url\\(([^)]+)\\)/, fill = stackShadow.attr('fill'), match = fill && fill.match(fillUrlMatcher);\n            if (match && chart.renderer.patternElements) {\n                chart.renderer.patternElements[match[1].slice(1)]\n                    .animate({\n                    x,\n                    y,\n                    width,\n                    height\n                });\n            }\n            stack.shadowGroup.animate({\n                translateX: chart.inverted ?\n                    stack.axis.pos : xAxis.pos,\n                translateY: chart.inverted ?\n                    xAxis.pos : stack.axis.pos\n            });\n            PictorialSeries_invertShadowGroup(stack.shadowGroup, stack.axis);\n            PictorialSeries_rescalePatternFill(stackShadow, height, width, height, strokeWidth);\n            stack.setOffset(series.pointXOffset || 0, series.barW || 0);\n        }\n    }\n    else if (stack.shadow && stack.shadowGroup) {\n        stack.shadow.destroy();\n        stack.shadow = void 0;\n        stack.shadowGroup.destroy();\n        stack.shadowGroup = void 0;\n    }\n}\n/**\n *\n */\nfunction forEachStack(chart, callback) {\n    if (chart.axes) {\n        chart.axes.forEach(function (axis) {\n            if (!axis.stacking) {\n                return;\n            }\n            const stacks = axis.stacking.stacks;\n            // Render each stack total\n            objectEach(stacks, function (type) {\n                objectEach(type, function (stack) {\n                    callback(stack);\n                });\n            });\n        });\n    }\n}\nPictorialSeries_addEvent((highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default()), 'render', function () {\n    forEachStack(this, renderStackShadow);\n});\nPictorialSeries_addEvent((highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_default()), 'afterSetOffset', function (e) {\n    if (this.shadow) {\n        const { chart, len } = this.axis, { xOffset, width } = e, translateX = chart.inverted ? xOffset - chart.xAxis[0].len : xOffset, translateY = chart.inverted ? -len : 0;\n        this.shadow.attr({\n            translateX,\n            translateY\n        });\n        this.shadow.animate({ width });\n    }\n});\n/**\n *\n */\nfunction destroyAllStackShadows(chart) {\n    forEachStack(chart, function (stack) {\n        if (stack.shadow && stack.shadowGroup) {\n            stack.shadow.destroy();\n            stack.shadowGroup.destroy();\n            delete stack.shadow;\n            delete stack.shadowGroup;\n        }\n    });\n}\n// This is a workaround due to no implementation of the animation drilldown.\nPictorialSeries_addEvent((highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default()), 'afterDrilldown', function () {\n    destroyAllStackShadows(this);\n});\nPictorialSeries_addEvent((highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default()), 'afterDrillUp', function () {\n    destroyAllStackShadows(this);\n});\nPictorialSeries.prototype.pointClass = Pictorial_PictorialPoint;\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('pictorial', PictorialSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Pictorial_PictorialSeries = ((/* unused pure expression or super */ null && (PictorialSeries)));\n/* *\n *\n * API Options\n *\n * */\n/**\n * A `pictorial` series. If the [type](#series.pictorial.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.pictorial\n * @since 11.0.0\n * @product   highcharts\n * @excluding dataParser, borderRadius, boostBlending, boostThreshold,\n *            borderColor, borderWidth, centerInCategory, connectEnds,\n *            connectNulls, crisp, colorKey, dataURL, dataAsColumns, depth,\n *            dragDrop, edgeColor, edgeWidth, linecap, lineWidth,  marker,\n *            dataSorting, dashStyle, onPoint, relativeXValue, shadow, zoneAxis,\n *            zones\n * @requires  modules/pictorial\n * @apioption series.pictorial\n */\n/**\n * An array of data points for the series. For the `pictorial` series type,\n * points can be given in the following ways:\n *\n * 1. An array of arrays with 2 values. In this case, the values correspond\n *    to `x,y`. If the first value is a string, it is applied as the name\n *    of the point, and the `x` value is inferred. The `x` value can also be\n *    omitted, in which case the inner arrays should be of length 2. Then the\n *    `x` value is automatically calculated, either starting at 0 and\n *    incremented by 1, or from `pointStart` and `pointInterval` given in the\n *    series options.\n *    ```js\n *    data: [\n *        [0, 40],\n *        [1, 50],\n *        [2, 60]\n *    ]\n *    ```\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.pictorial.turboThreshold), this option is not\n *    available.\n *    ```js\n *    data: [{\n *        x: 0,\n *        y: 40,\n *        name: \"Point1\",\n *        color: \"#00FF00\"\n *    }, {\n *        x: 1,\n *        y: 60,\n *        name: \"Point2\",\n *        color: \"#FF00FF\"\n *    }]\n *    ```\n *\n * @type      {Array<Array<(number|string),number>|Array<(number|string),number,number>|*>}\n * @extends   series.column.data\n *\n * @sample {highcharts} highcharts/demo/pictorial/\n *         Pictorial chart\n * @sample {highcharts} highcharts/demo/pictorial-stackshadow/\n *         Pictorial stackShadow option\n * @sample {highcharts} highcharts/series-pictorial/paths-max/\n *         Pictorial max option\n *\n * @excluding borderColor, borderWidth, dashStyle, dragDrop\n * @since 11.0.0\n * @product   highcharts\n * @apioption series.pictorial.data\n */\n/**\n * The paths include options describing the series image. For further details on\n * preparing the SVG image, please refer to the [pictorial\n * documentation](https://www.highcharts.com/docs/chart-and-series-types/pictorial).\n *\n * @declare   Highcharts.SeriesPictorialPathsOptionsObject\n * @type      {Array<*>}\n *\n * @sample    {highcharts} highcharts/demo/pictorial/\n *            Pictorial chart\n *\n * @since     11.0.0\n * @product   highcharts\n * @apioption series.pictorial.paths\n */\n/**\n * The definition defines a path to be drawn. It corresponds `d` SVG attribute.\n *\n * @type      {string}\n *\n * @sample    {highcharts} highcharts/demo/pictorial/\n *            Pictorial chart\n *\n * @product   highcharts\n * @apioption series.pictorial.paths.definition\n */\n/**\n * The max option determines height of the image. It is the ratio of\n * `yAxis.max` to the `paths.max`.\n *\n * @sample {highcharts} highcharts/series-pictorial/paths-max\n *         Pictorial max option\n *\n * @type      {number}\n * @default   yAxis.max\n * @product   highcharts\n * @apioption series.pictorial.paths.max\n */\n/**\n * Relevant only for pictorial series. The `stackShadow` forms the background of\n * stacked points. Requires `series.stacking` to be defined.\n *\n * @sample {highcharts} highcharts/demo/pictorial-stackshadow/ Pictorial\n *         stackShadow option\n *\n * @declare   Highcharts.YAxisOptions\n * @type      {*}\n * @since 11.0.0\n * @product   highcharts\n * @requires  modules/pictorial\n * @apioption yAxis.stackShadow\n */\n/**\n * The color of the `stackShadow` border.\n *\n * @declare   Highcharts.YAxisOptions\n * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n * @default   transparent\n * @product   highcharts\n * @requires  modules/pictorial\n * @apioption yAxis.stackShadow.borderColor\n */\n/**\n * The width of the `stackShadow` border.\n *\n * @declare   Highcharts.YAxisOptions\n * @type      {number}\n * @default   0\n * @product   highcharts\n * @requires  modules/pictorial\n * @apioption yAxis.stackShadow.borderWidth\n */\n/**\n * The color of the `stackShadow`.\n *\n * @declare   Highcharts.YAxisOptions\n * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n * @default   #dedede\n * @product   highcharts\n * @requires  modules/pictorial\n * @apioption yAxis.stackShadow.color\n */\n/**\n * Enable or disable `stackShadow`.\n *\n * @declare   Highcharts.YAxisOptions\n * @type      {boolean}\n * @default   undefined\n * @product   highcharts\n * @requires  modules/pictorial\n * @apioption yAxis.stackShadow.enabled\n */\n''; // Adds doclets above to transpiled file\n\n;// ./code/es-modules/masters/modules/pictorial.js\n\n\n\n\n/* harmony default export */ const pictorial_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__448__", "__WEBPACK_EXTERNAL_MODULE__960__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__820__", "__WEBPACK_EXTERNAL_MODULE__184__", "__WEBPACK_EXTERNAL_MODULE__540__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "pictorial_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "animObject", "getOptions", "addEvent", "defined", "erase", "extend", "merge", "pick", "removeEvent", "wrap", "patterns", "createPatterns", "colors", "i", "pattern", "push", "path", "color", "width", "height", "patternTransform", "hashFromObject", "preSeed", "str", "JSON", "stringify", "strLen", "length", "hash", "seedStep", "Math", "max", "floor", "charCodeAt", "toString", "replace", "onChartEndResize", "renderer", "defIds", "filter", "id", "indexOf", "series", "visible", "point", "points", "colorOptions", "options", "_width", "_height", "redraw", "onChartRedraw", "usedIds", "for<PERSON>ach", "renderTo", "querySelectorAll", "node", "getAttribute", "url", "patternElements", "destroy", "onPointAfterInit", "onRendererComplexColor", "args", "element", "chartIndex", "value", "patternIndex", "image", "forceHashId", "parentNode", "pointCalculatePatternDimensions", "graphic", "addPattern", "forExport", "animation", "globalAnimation", "duration", "setAttribute", "onSeriesRender", "isResizing", "chart", "isDirtyData", "hasRendered", "shapeArgs", "calculatePatternDimensions", "bBox", "getBBox", "x", "y", "scaleY", "mapView", "getSVGTransform", "_inverted", "aspectRatio", "aspectWidth", "aspectHeight", "ceil", "_x", "round", "abs", "_y", "rendererAddPattern", "animate", "animationOptions", "attribs", "idCounter", "attrs", "patternUnits", "patternContentUnits", "createElement", "attr", "add", "defs", "fill", "isObject", "backgroundColor", "rect", "styledMode", "stroke", "strokeWidth", "transform", "opacity", "childNodes", "child", "wrapSeriesGetColor", "proceed", "oldColor", "apply", "slice", "arguments", "onPatternScaleCorrection", "p", "hasAttribute", "group", "scaleX", "map", "pointInfo", "index", "arr", "some", "otherInfo", "otherIndex", "updateTransform", "highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_", "highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "PictorialUtilities_defined", "PictorialUtilities", "rescalePatternFill", "stackHeight", "borderWidth", "match", "patternPath", "document", "querySelector", "parent", "parentElement", "box", "append<PERSON><PERSON><PERSON>", "pointAspectRatio", "invertShadowGroup", "shadowGroup", "yAxis", "inverted", "rotation", "getStackMetrics", "shape", "len", "toPixels", "ColumnPoint", "seriesTypes", "column", "pointClass", "PictorialPoint_rescalePatternFill", "PictorialPoint_getStackMetrics", "Pictorial_PictorialPoint", "setState", "paths", "Infinity", "highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_", "highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default", "highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_", "highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_default", "highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_", "highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default", "ColumnSeries", "Extensions_PatternFill", "compose", "ChartClass", "SeriesClass", "SVGRendererClass", "PointClass", "pointProto", "PictorialSeries_animObject", "PictorialSeries_getStackMetrics", "PictorialSeries_invertShadowGroup", "PictorialSeries_rescalePatternFill", "PictorialSeries_addEvent", "PictorialSeries_defined", "PictorialSeries_merge", "objectEach", "PictorialSeries_pick", "PictorialSeries", "init", "animationClipKey", "getSharedClipKey", "easing", "defer", "join", "animationClipRect", "sharedClips", "clipBox", "getClipBox", "clipRect", "clip", "hasClass", "finalBox", "addClass", "animateDrilldown", "animateDrillupFrom", "pointAttribs", "seriesOptions", "pathDef", "getExtremes", "extremes", "dataMax", "renderStackShadow", "stack", "stackKeys", "keys", "split", "allSeries", "axis", "seriesIndexes", "parseFloat", "seriesIndex", "is", "hasData", "xAxis", "stackShadow", "shadow", "xCenter", "getColumnMetrics", "shadowOptions", "enabled", "g", "translateX", "pos", "translateY", "borderColor", "setOffset", "pointXOffset", "barW", "forEachStack", "callback", "axes", "stacking", "stacks", "type", "destroyAllStackShadows", "defaultOptions", "fill<PERSON>rl<PERSON><PERSON><PERSON>", "currentPattern", "e", "xOffset", "registerSeriesType"], "mappings": "CAWA,AAXA;;;;;;;;;;CAUC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,MAAS,CAAC,KAAQ,CAAC,MAAS,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,MAAS,CAAEA,EAAK,WAAc,CAAC,SAAY,CAAEA,EAAK,WAAc,CAAC,WAAc,EACjQ,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,+BAAgC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,MAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAACA,EAAK,KAAQ,CAACA,EAAK,cAAiB,CAACA,EAAK,MAAS,CAACA,EAAK,SAAY,CAACA,EAAK,WAAc,CAAE,GAC/N,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,+BAA+B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,MAAS,CAAC,KAAQ,CAAC,MAAS,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,MAAS,CAAEA,EAAK,WAAc,CAAC,SAAY,CAAEA,EAAK,WAAc,CAAC,WAAc,EAEjSA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,MAAS,CAAC,KAAQ,CAAC,MAAS,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,MAAS,CAAEA,EAAK,UAAa,CAAC,SAAY,CAAEA,EAAK,UAAa,CAAC,WAAc,CACvQ,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,IAC/O,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACZ,IAERA,EAAOD,OAAO,CAAGW,CAEX,EAEA,IACC,AAACV,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGS,CAEX,EAEA,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGY,CAEX,EAEA,IACC,AAACX,IAERA,EAAOD,OAAO,CAAGU,CAEX,EAEA,IACC,AAACT,IAERA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,AAACL,IAERA,EAAOD,OAAO,CAAGQ,CAEX,CAEI,EAGIM,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAajB,OAAO,CAG5B,IAAIC,EAASa,CAAwB,CAACE,EAAS,CAAG,CAGjDhB,QAAS,CAAC,CACX,EAMA,OAHAa,CAAmB,CAACG,EAAS,CAACf,EAAQA,EAAOD,OAAO,CAAEe,GAG/Cd,EAAOD,OAAO,AACtB,CAMCe,EAAoBI,CAAC,CAAG,AAAClB,IACxB,IAAImB,EAASnB,GAAUA,EAAOoB,UAAU,CACvC,IAAOpB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAc,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACtB,EAASwB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAAC1B,EAASyB,IAC5EE,OAAOC,cAAc,CAAC5B,EAASyB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,EAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEsCvB,EAAoB,KAgB/K,GAAM,CAAEyB,WAAAA,CAAU,CAAE,CAAID,IAElB,CAAEE,WAAAA,CAAU,CAAE,CAAIF,IAElB,CAAEG,SAAAA,CAAQ,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAEC,YAAAA,CAAW,CAAEC,KAAAA,CAAI,CAAE,CAAIV,IAMxEW,EAAWC,AA+BjB,WACI,IAAMD,EAAW,EAAE,CAAEE,EAASX,IAAaW,MAAM,CAE7CC,EAAI,EACR,IAAK,IAAMC,IAAW,CAClB,wDACA,wDACA,0BACA,0BACA,sDACH,CACGJ,EAASK,IAAI,CAAC,CACVC,KAAMF,EACNG,MAAOL,CAAM,CAACC,IAAI,CAClBK,MAAO,EACPC,OAAQ,EACRC,iBAAkB,gBACtB,GAIJ,IAAK,IAAMN,KADXD,EAAI,EACkB,CAClB,sBACA,4BACA,gDACA,4CACA,4CACH,EACGH,EAASK,IAAI,CAAC,CACVC,KAAMF,EACNG,MAAOL,CAAM,CAACC,EAAE,CAChBK,MAAO,GACPC,OAAQ,EACZ,GACAN,GAAQ,EAEZ,OAAOH,CACX,IAkBA,SAASW,EAAe9B,CAAG,CAAE+B,CAAO,EAChC,IAAMC,EAAMC,KAAKC,SAAS,CAAClC,GAAMmC,EAASH,EAAII,MAAM,EAAI,EACpDC,EAAO,EAAGf,EAAI,EAASgB,EAC3B,GAAIP,EAAS,CACTO,EAAWC,KAAKC,GAAG,CAACD,KAAKE,KAAK,CAACN,EAAS,KAAM,GAC9C,IAAK,IAAI3C,EAAI,EAAGA,EAAI2C,EAAQ3C,GAAK8C,EAC7BD,GAAQL,EAAIU,UAAU,CAAClD,GAE3B6C,GAAcA,CAClB,CACA,KAAOf,EAAIa,EAAQ,EAAEb,EAEjBe,EAAO,AAAEA,CAAAA,GAAQ,CAAA,EAAKA,EADfL,EAAIU,UAAU,CAACpB,GAEtBe,GAAcA,EAElB,OAAOA,EAAKM,QAAQ,CAAC,IAAIC,OAAO,CAAC,IAAK,IAC1C,CAMA,SAASC,IACL,GAAI,IAAI,CAACC,QAAQ,EACb,AAAC,CAAA,IAAI,CAACA,QAAQ,CAACC,MAAM,EAAI,EAAE,AAAD,EAAGC,MAAM,CAAC,AAACC,GAAQA,GACzCA,EAAGC,OAAO,EACVD,AAAsC,IAAtCA,EAAGC,OAAO,CAAC,wBAA+Bd,MAAM,CAAE,CAGtD,IAAK,IAAMe,KAAU,IAAI,CAACA,MAAM,CAC5B,GAAIA,EAAOC,OAAO,CACd,IAAK,IAAMC,KAASF,EAAOG,MAAM,CAAE,CAC/B,IAAMC,EAAeF,EAAMG,OAAO,EAAIH,EAAMG,OAAO,CAAC9B,KAAK,CACrD6B,GACAA,EAAahC,OAAO,GACpBgC,EAAahC,OAAO,CACfkC,MAAM,CAAG,QACdF,EAAahC,OAAO,CACfmC,OAAO,CAAG,QAEvB,CAIR,IAAI,CAACC,MAAM,CAAC,CAAA,EAChB,CACJ,CAMA,SAASC,IACL,IAAMC,EAAU,CAAC,EAAGf,EAAW,IAAI,CAACA,QAAQ,CAE5C3B,EAAW,AAAC2B,CAAAA,EAASC,MAAM,EAAI,EAAE,AAAD,EAAGC,MAAM,CAAC,AAACzB,GAAaA,EAAQ2B,OAAO,EACnE3B,AAA2C,IAA3CA,EAAQ2B,OAAO,CAAC,wBACpB,GAAI/B,EAASiB,MAAM,CAgBf,IAAK,IAAMa,KAbX,EAAE,CAACa,OAAO,CAAC1D,IAAI,CAAC,IAAI,CAAC2D,QAAQ,CAACC,gBAAgB,CAAC,qDAAsD,AAACC,IAClG,IAAMhB,EAAKgB,EAAKC,YAAY,CAAC,SACzBD,EAAKC,YAAY,CAAC,UAClBD,EAAKC,YAAY,CAAC,UAClBjB,GAKAY,CAAAA,CAAO,CAJaZ,EACfL,OAAO,CAACE,EAASqB,GAAG,CAAE,IACtBvB,OAAO,CAAC,QAAS,IACjBA,OAAO,CAAC,IAAK,IACE,CAAG,CAAA,CAAG,CAElC,GAEiBzB,GACT,CAAC0C,CAAO,CAACZ,EAAG,GAEZpC,EAAMiC,EAASC,MAAM,CAAEE,GAEnBH,EAASsB,eAAe,CAACnB,EAAG,GAC5BH,EAASsB,eAAe,CAACnB,EAAG,CAACoB,OAAO,GACpC,OAAOvB,EAASsB,eAAe,CAACnB,EAAG,EAKvD,CAKA,SAASqB,IACL,IAAoBf,EAAeF,AAArB,IAAI,CAAuBG,OAAO,CAAC9B,KAAK,CAGlD6B,GAAgBA,EAAahC,OAAO,GAGhC,AAAqC,UAArC,OAAOgC,EAAahC,OAAO,CAACE,IAAI,EAChC8B,CAAAA,EAAahC,OAAO,CAACE,IAAI,CAAG,CACxBlC,EAAGgE,EAAahC,OAAO,CAACE,IAAI,AAChC,CAAA,EAGJ4B,AAZU,IAAI,CAYR3B,KAAK,CAAG2B,AAZJ,IAAI,CAYMG,OAAO,CAAC9B,KAAK,CAAGX,EAAMsC,AAZhC,IAAI,CAYkCF,MAAM,CAACK,OAAO,CAAC9B,KAAK,CAAE6B,GAE9E,CAKA,SAASgB,EAAuBC,CAAI,EAChC,IAAM9C,EAAQ8C,EAAKA,IAAI,CAAC,EAAE,CAAEvE,EAAOuE,EAAKA,IAAI,CAAC,EAAE,CAAEC,EAAUD,EAAKA,IAAI,CAAC,EAAE,CAAEE,EAAc,IAAI,CAACA,UAAU,EAAI,EACtGnD,EAAUG,EAAMH,OAAO,CAAEoD,EAAQ,UAMrC,GAJI,AAA8B,KAAA,IAAvBjD,EAAMkD,YAAY,EAAoBzD,GAC7CI,CAAAA,EAAUJ,CAAQ,CAACO,EAAMkD,YAAY,CAAC,AAAD,EAGrC,CAACrD,EACD,MAAO,CAAA,EAGX,GAAIA,EAAQsD,KAAK,EACb,AAAwB,UAAxB,OAAOtD,EAAQE,IAAI,EACnBF,EAAQE,IAAI,EAAIF,EAAQE,IAAI,CAAClC,CAAC,CAAE,CAMhC,IAAIuF,EAAcL,EAAQM,UAAU,EAChCN,EAAQM,UAAU,CAACb,YAAY,CAAC,SACpCY,EAAcA,GACVA,EAAY5B,OAAO,CAAC,qBAAuB,GAG3C3B,CAAAA,AAAmB,UAAnBA,EAAQkC,MAAM,EAAgBlC,AAAoB,UAApBA,EAAQmC,OAAO,AAAW,GACxDsB,EAAgC5E,IAAI,CAAC,CAAE6E,QAAS,CAAER,QAASA,CAAQ,CAAE,EAAGlD,GAOxEuD,CAAAA,GAAe,CAACvD,EAAQ0B,EAAE,AAAD,GAGzB1B,CAAAA,AADAA,CAAAA,EAAUR,EAAM,CAAC,EAAGQ,EAAO,EACnB0B,EAAE,CAAG,sBAAwByB,EAAa,IAC9C5C,EAAeP,GAAWO,EAAeP,EAAS,CAAA,EAAI,EAI9D,IAAI,CAAC2D,UAAU,CAAC3D,EAAS,CAAC,IAAI,CAAC4D,SAAS,EAAInE,EAAKO,EAAQ6D,SAAS,CAAE,IAAI,CAACC,eAAe,CAAE,CAAEC,SAAU,GAAI,IAC1GX,EAAQ,CAAC,IAAI,EAAE,IAAI,CAACR,GAAG,CAAC,CAAC,EAAE5C,EAAQ0B,EAAE,CAAI,CAAA,IAAI,CAACkC,SAAS,CAAG,UAAY,EAAC,EAAG,CAAC,CAAC,AAChF,MAGIR,EAAQpD,EAAQG,KAAK,EAAIiD,EAS7B,OANAF,EAAQc,YAAY,CAACtF,EAAM0E,GAE3BjD,EAAMiB,QAAQ,CAAG,WACb,OAAOgC,CACX,EAEO,CAAA,CACX,CAKA,SAASa,IACL,IAAMC,EAAa,IAAI,CAACC,KAAK,CAACD,UAAU,CACxC,GAAI,IAAI,CAACE,WAAW,EAAIF,GAAc,CAAC,IAAI,CAACC,KAAK,CAACE,WAAW,CACzD,IAAK,IAAMvC,KAAS,IAAI,CAACC,MAAM,CAAE,CAC7B,IAAMC,EAAeF,EAAMG,OAAO,EAAIH,EAAMG,OAAO,CAAC9B,KAAK,CACrD6B,GACAA,EAAahC,OAAO,GAKhBkE,GACA,CAAEpC,CAAAA,EAAMwC,SAAS,EACbxC,EAAMwC,SAAS,CAAClE,KAAK,EACrB0B,EAAMwC,SAAS,CAACjE,MAAM,AAAD,GACzB2B,EACKhC,OAAO,CAACkC,MAAM,CAAG,QACtBF,EACKhC,OAAO,CAACmC,OAAO,CAAG,SAGvBL,EAAMyC,0BAA0B,CAACvC,EAAahC,OAAO,EAGjE,CAER,CAmBA,SAASyD,EAAgCzD,CAAO,EAC5C,GAAIA,EAAQI,KAAK,EAAIJ,EAAQK,MAAM,CAC/B,OAEJ,IAAMmE,EAAO,IAAI,CAACd,OAAO,EAAK,CAAA,IAAI,CAACA,OAAO,CAACe,OAAO,EAC9C,IAAI,CAACf,OAAO,CAACe,OAAO,CAAC,CAAA,IACrB,IAAI,CAACf,OAAO,CAACR,OAAO,EAChB,IAAI,CAACQ,OAAO,CAACR,OAAO,CAACuB,OAAO,EAAC,GAAM,CAAC,EAAGH,EAAY,IAAI,CAACA,SAAS,CASzE,GAPIA,IACAE,EAAKpE,KAAK,CAAGkE,EAAUlE,KAAK,EAAIoE,EAAKpE,KAAK,CAC1CoE,EAAKnE,MAAM,CAAGiE,EAAUjE,MAAM,EAAImE,EAAKnE,MAAM,CAC7CmE,EAAKE,CAAC,CAAGJ,EAAUI,CAAC,EAAIF,EAAKE,CAAC,CAC9BF,EAAKG,CAAC,CAAGL,EAAUK,CAAC,EAAIH,EAAKG,CAAC,EAG9B3E,EAAQsD,KAAK,CAAE,CAIf,GAAI,CAACkB,EAAKpE,KAAK,EAAI,CAACoE,EAAKnE,MAAM,CAAE,CAC7BL,EAAQkC,MAAM,CAAG,QACjBlC,EAAQmC,OAAO,CAAG,QAElB,IAAMyC,EAAS,IAAI,CAAChD,MAAM,CAACuC,KAAK,CAACU,OAAO,EACpC,IAAI,CAACjD,MAAM,CAACuC,KAAK,CAACU,OAAO,CAACC,eAAe,GAAGF,MAAM,AAClDvF,CAAAA,EAAQuF,IAAWA,EAAS,GAC5B5E,CAAAA,EAAQ+E,SAAS,CAAG,CAAA,CAAG,EAE3B,MACJ,CAEI/E,EAAQgF,WAAW,GACnBR,EAAKQ,WAAW,CAAGR,EAAKpE,KAAK,CAAGoE,EAAKnE,MAAM,CACvCL,EAAQgF,WAAW,CAAGR,EAAKQ,WAAW,CAEtCR,EAAKS,WAAW,CAAGT,EAAKnE,MAAM,CAAGL,EAAQgF,WAAW,CAIpDR,EAAKU,YAAY,CAAGV,EAAKpE,KAAK,CAAGJ,EAAQgF,WAAW,EAK5DhF,EAAQkC,MAAM,CAAGlC,EAAQI,KAAK,EAC1BY,KAAKmE,IAAI,CAACX,EAAKS,WAAW,EAAIT,EAAKpE,KAAK,EAC5CJ,EAAQmC,OAAO,CAAGnC,EAAQK,MAAM,EAC5BW,KAAKmE,IAAI,CAACX,EAAKU,YAAY,EAAIV,EAAKnE,MAAM,CAClD,CAGKL,EAAQI,KAAK,GACdJ,EAAQoF,EAAE,CAAGpF,EAAQ0E,CAAC,EAAI,EAC1B1E,EAAQoF,EAAE,EAAIZ,EAAKE,CAAC,CAAG1D,KAAKqE,KAAK,CAACb,EAAKS,WAAW,CAC9CjE,KAAKsE,GAAG,CAACd,EAAKS,WAAW,CAAGT,EAAKpE,KAAK,EAAI,EAC1C,IAEHJ,EAAQK,MAAM,GACfL,EAAQuF,EAAE,CAAGvF,EAAQ2E,CAAC,EAAI,EAC1B3E,EAAQuF,EAAE,EAAIf,EAAKG,CAAC,CAAG3D,KAAKqE,KAAK,CAACb,EAAKU,YAAY,CAC/ClE,KAAKsE,GAAG,CAACd,EAAKU,YAAY,CAAGV,EAAKnE,MAAM,EAAI,EAC5C,GAEZ,CAkBA,SAASmF,EAAmBvD,CAAO,CAAE4B,CAAS,EAC1C,IAAM4B,EAAUhG,EAAKoE,EAAW,CAAA,GAAO6B,EAAmBxG,EAAWuG,GAAUtF,EAAQ8B,EAAQ9B,KAAK,EAAI,UAA0DE,EAAS4B,EAAQ5B,MAAM,EACpL,CAAA,AAA2B,UAA3B,OAAO4B,EAAQE,OAAO,CAAgBF,EAAQE,OAAO,CAAG,CAAA,GADiG,GAK3I/B,EAAQ6B,EAAQ7B,KAAK,EACnC,CAAA,AAA0B,UAA1B,OAAO6B,EAAQC,MAAM,CAAgBD,EAAQC,MAAM,CAAG,CAAA,GANmG,GAQ1JyD,EAASjE,EAAKO,EAAQP,EAAE,CAAExB,EAc9B,GAbI,CAACwB,IACD,IAAI,CAACkE,SAAS,CAAG,IAAI,CAACA,SAAS,EAAI,EACnClE,EAAM,sBACF,IAAI,CAACkE,SAAS,CACd,IACC,CAAA,IAAI,CAACzC,UAAU,EAAI,CAAA,EACxB,EAAE,IAAI,CAACyC,SAAS,EAEhB,IAAI,CAAChC,SAAS,EACdlC,CAAAA,GAAM,SAAQ,EAGlB,IAAI,CAACF,MAAM,CAAG,IAAI,CAACA,MAAM,EAAI,EAAE,CAC3B,IAAI,CAACA,MAAM,CAACG,OAAO,CAACD,GAAM,GAC1B,OAGJ,IAAI,CAACF,MAAM,CAACvB,IAAI,CAACyB,GAEjB,IAAMmE,EAAQ,CACVnE,GAAIA,EACJoE,aAAc,iBACdC,oBAAqB9D,EAAQ8D,mBAAmB,EAAI,iBACpD3F,MAAOA,EACPC,OAAQA,EACRqE,EAAGzC,EAAQmD,EAAE,EAAInD,EAAQyC,CAAC,EAAI,EAC9BC,EAAG1C,EAAQsD,EAAE,EAAItD,EAAQ0C,CAAC,EAAI,CAClC,CACI1C,CAAAA,EAAQ8C,SAAS,GACjBc,EAAMvF,gBAAgB,CAAG,eACrB2B,EAAQ3B,gBAAgB,EACxB2B,CAAAA,EAAQ3B,gBAAgB,EAAI,eAAc,GAG9C2B,EAAQ3B,gBAAgB,EACxBuF,CAAAA,EAAMvF,gBAAgB,CAAG2B,EAAQ3B,gBAAgB,AAAD,EAEpD,IAAMN,EAAU,IAAI,CAACgG,aAAa,CAAC,WAAWC,IAAI,CAACJ,GAAOK,GAAG,CAAC,IAAI,CAACC,IAAI,EAIvE,GAFAnG,EAAQ0B,EAAE,CAAGA,EAETO,EAAQ/B,IAAI,CAAE,KAhDOkG,EAiDrBlG,EAAOjB,IAA8EoH,QAAQ,CAACpE,EAAQ/B,IAAI,EACtG+B,EAAQ/B,IAAI,CACZ,CAAElC,EAAGiE,EAAQ/B,IAAI,AAAC,EAElB+B,EAAQqE,eAAe,GArDNF,EAsDZnE,EAAQqE,eAAe,CAtDF,IAAI,CACjCC,IAAI,CAAC,EAAG,EAAGnG,EAAOC,GAClB4F,IAAI,CAAC,CAAEG,KAAAA,CAAK,GACZF,GAAG,CAAClG,IAsDL2F,EAAU,CACN,EAAKzF,EAAKlC,CAAC,AACf,EACK,IAAI,CAACwI,UAAU,GAChBb,EAAQc,MAAM,CAAGvG,EAAKuG,MAAM,EAAItG,EAChCwF,CAAO,CAAC,eAAe,CAAGlG,EAAKS,EAAKwG,WAAW,CAAE,GACjDf,EAAQS,IAAI,CAAGlG,EAAKkG,IAAI,EAAI,QAE5BlG,EAAKyG,SAAS,EACdhB,CAAAA,EAAQgB,SAAS,CAAGzG,EAAKyG,SAAS,AAAD,EAErC,IAAI,CAACX,aAAa,CAAC,QAAQC,IAAI,CAACN,GAASO,GAAG,CAAClG,GAC7CA,EAAQG,KAAK,CAAGA,CAEpB,MACS8B,EAAQqB,KAAK,GACdmC,EACA,IAAI,CAACnC,KAAK,CAACrB,EAAQqB,KAAK,CAAE,EAAG,EAAGlD,EAAOC,EAAQ,WAE3C,IAAI,CAACoF,OAAO,CAAC,CACTmB,QAASnH,EAAKwC,EAAQ2E,OAAO,CAAE,EACnC,EAAGlB,GACHhG,EAAY,IAAI,CAACwD,OAAO,CAAE,OAC9B,GAAG+C,IAAI,CAAC,CAAEW,QAAS,CAAE,GAAGV,GAAG,CAAClG,GAG5B,IAAI,CAACsD,KAAK,CAACrB,EAAQqB,KAAK,CAAE,EAAG,EAAGlD,EAAOC,GAAQ6F,GAAG,CAAClG,IAY3D,OARI,AAAEiC,EAAQqB,KAAK,EAAImC,GAAY,AAA2B,KAAA,IAApBxD,EAAQ2E,OAAO,EACrD,EAAE,CAACrE,OAAO,CAAC1D,IAAI,CAACmB,EAAQkD,OAAO,CAAC2D,UAAU,CAAE,AAACC,IACzCA,EAAM9C,YAAY,CAAC,UAAW/B,EAAQ2E,OAAO,CACjD,GAGJ,IAAI,CAAC/D,eAAe,CAAG,IAAI,CAACA,eAAe,EAAI,CAAC,EAChD,IAAI,CAACA,eAAe,CAACnB,EAAG,CAAG1B,EACpBA,CACX,CAKA,SAAS+G,EAAmBC,CAAO,EAC/B,IAAMC,EAAW,IAAI,CAAChF,OAAO,CAAC9B,KAAK,AAE/B8G,CAAAA,GACAA,EAASjH,OAAO,EAChB,CAACiH,EAASjH,OAAO,CAACG,KAAK,EACvB,OAAO,IAAI,CAAC8B,OAAO,CAAC9B,KAAK,CAEzB6G,EAAQE,KAAK,CAAC,IAAI,CAAE,EAAE,CAACC,KAAK,CAACtI,IAAI,CAACuI,UAAW,IAE7CH,EAASjH,OAAO,CAACG,KAAK,CAClB,IAAI,CAACA,KAAK,CACd,IAAI,CAACA,KAAK,CAAG,IAAI,CAAC8B,OAAO,CAAC9B,KAAK,CAAG8G,GAIlCD,EAAQE,KAAK,CAAC,IAAI,CAAE,EAAE,CAACC,KAAK,CAACtI,IAAI,CAACuI,UAAW,GAErD,CAMA,SAASC,IAGL,GAAI,CAACzF,AAFU,IAAI,CAEPuC,KAAK,EAAEU,QACf,OAEJ,IAA4BtD,EAAW4C,AAAzBvC,AALC,IAAI,CAKEuC,KAAK,CAAmB5C,QAAQ,CAAE3B,EAAW2B,EAASsB,eAAe,AAEtFtB,CAAAA,EAASC,MAAM,EAAEX,QAAUjB,GAG3BgC,AAVW,IAAI,CAURG,MAAM,CAACN,MAAM,CAAC,SAAU6F,CAAC,QAG5B,CAAI,CAACxF,AAFSwF,EAEH5D,OAAO,EAGX,AAAC5B,CAAAA,AALMwF,EAKA5D,OAAO,CAACR,OAAO,CAACqE,YAAY,CAAC,SACvCzF,AANUwF,EAMJ5D,OAAO,CAACR,OAAO,CAACqE,YAAY,CAAC,UACnCzF,AAPUwF,EAOJ5D,OAAO,CAACR,OAAO,CAACqE,YAAY,CAAC,SAAQ,GAC3C,CAACzF,AARSwF,EAQHrF,OAAO,CAAC9B,KAAK,EAAEH,SAASsD,OAC/B,CAAC,CAACxB,AATQwF,EASFE,KAAK,EAAEC,QACf,CAAC,CAAC3F,AAVQwF,EAUFE,KAAK,EAAE5C,MACvB,GAEK8C,GAAG,CAAC,SAAUJ,CAAC,EAShB,MAAO,CACH5F,GAPO,AAACI,CAAAA,AAFEwF,EAEI5D,OAAO,EAAER,QAAQP,aAAa,SAC5Cb,AAHUwF,EAGJ5D,OAAO,EAAER,QAAQP,aAAa,UACpCb,AAJUwF,EAIJ5D,OAAO,EAAER,QAAQP,aAAa,WAAa,EAAC,EACjDtB,OAAO,CAACE,EAASqB,GAAG,CAAE,IACtBvB,OAAO,CAAC,QAAS,IACjBA,OAAO,CAAC,IAAK,IAGdqD,EAAG5C,AAVOwF,EAUDE,KAAK,EAAEC,QAAU,EAC1B9C,EAAG7C,AAXOwF,EAWDE,KAAK,EAAE5C,QAAU,CAC9B,CACJ,GAEKnD,MAAM,CAAC,SAAUkG,CAAS,CAAEC,CAAK,CAAEC,CAAG,EACvC,MAAOF,AAAiB,KAAjBA,EAAUjG,EAAE,EACfiG,AAAgD,KAAhDA,EAAUjG,EAAE,CAACC,OAAO,CAAC,wBACrB,CAACkG,EAAIC,IAAI,CAAC,SAAUC,CAAS,CAAEC,CAAU,EACrC,OAAOD,EAAUrG,EAAE,GAAKiG,EAAUjG,EAAE,EAAIsG,EAAaJ,CACzD,EACR,GACKrF,OAAO,CAAC,SAAUoF,CAAS,EAC5B,IAAMjG,EAAKiG,EAAUjG,EAAE,AACvB9B,CAAAA,CAAQ,CAAC8B,EAAG,CAAC+F,MAAM,CAAG,EAAIE,EAAUjD,CAAC,CACrC9E,CAAQ,CAAC8B,EAAG,CAACkD,MAAM,CAAG,EAAI+C,EAAUhD,CAAC,CACrC/E,CAAQ,CAAC8B,EAAG,CAACuG,eAAe,CAAC,mBACjC,EAER,CA8HA,IAAIC,EAA+FzK,EAAoB,KACnH0K,EAAmH1K,EAAoBI,CAAC,CAACqK,GAEzIE,EAAmI3K,EAAoB,KACvJ4K,EAAuJ5K,EAAoBI,CAAC,CAACuK,GAYjL,GAAM,CAAE/I,QAASiJ,CAA0B,CAAE,CAAIrJ,IAyDdsJ,EAAsB,CAAEC,mBArD3D,SAA4BtF,CAAO,CAAEuF,CAAW,CAAErI,CAAK,CAAEC,CAAM,CAAEqI,EAAc,CAAC,EAC5E,IAAMtC,EAAOlD,GAAWA,EAAQ+C,IAAI,CAAC,QAAS0C,EAAQvC,GAAQA,EAAKuC,KAAK,CAAC,kBACzE,GAAIA,EAAO,CACP,IAAMC,EAAcC,SAASC,aAAa,CAAC,CAAC,EAAEH,CAAK,CAAC,EAAE,CAAC,KAAK,CAAC,EAC7D,GAAIC,EAAa,CACb,IAAIpE,EAAOoE,EAAYnE,OAAO,GAG9B,GAAID,AAAe,IAAfA,EAAKpE,KAAK,CAAQ,CAClB,IAAM2I,EAASH,EAAYI,aAAa,CAExC9F,EAAQ3B,QAAQ,CAAC0H,GAAG,CAACC,WAAW,CAACN,GACjCpE,EAAOoE,EAAYnE,OAAO,GAC1BsE,EAAOG,WAAW,CAACN,EACvB,CACA,IAAInB,EAAS,EAAKjD,CAAAA,EAAKpE,KAAK,CAAGsI,CAAU,EACnC9D,EAAS6D,EAAcpI,EAASmE,EAAKnE,MAAM,CAAE2E,EAAcR,EAAKpE,KAAK,CAAGoE,EAAKnE,MAAM,CAAE8I,EAAmB/I,EAAQqI,EAAa/D,EAAI,CAACF,EAAKpE,KAAK,CAAG,CACjJ4E,CAAAA,EAAcmE,GACd1B,CAAAA,EAASA,EAASzC,EAAcmE,CAAe,EAEnDP,EAAY5E,YAAY,CAAC,eAAgB0E,EAAetI,CAAAA,EAAQqH,CAAK,GACrEmB,EAAY5E,YAAY,CAAC,YACrB,CAAC,uBAAM,EAAEyD,EAAO,CAAC,EAAE7C,EAClB,YAAU,EAAEF,EAAIgE,EAAcjB,EAAS,EAAE,EAAE,EAAE,CAACjD,EAAKG,CAAC,CAAC,CAAC,CAD1B,CAErC,CACJ,CACJ,EA2B+EyE,kBAT/E,SAA2BC,CAAW,CAAEC,CAAK,EACzC,IAAMC,EAAWD,EAAMnF,KAAK,CAACoF,QAAQ,AACjCA,CAAAA,GACAF,EAAYpD,IAAI,CAAC,CACbuD,SAAUD,AAAW,KAAXA,EACV9B,OAAQ8B,EAAW,GAAK,CAC5B,EAER,EACkGE,gBAvBlG,SAAyBH,CAAK,CAAEI,CAAK,EACjC,IAAIrJ,EAASiJ,EAAMK,GAAG,CAAEhF,EAAI,EAK5B,OAJI+E,GAASpB,EAA2BoB,EAAMzI,GAAG,IAC7C0D,EAAI2E,EAAMM,QAAQ,CAACF,EAAMzI,GAAG,CAAE,CAAA,GAC9BZ,EAASiJ,EAAMK,GAAG,CAAGhF,GAElB,CACHtE,OAAAA,EACAsE,EAAAA,CACJ,CACJ,CAakH,EAc5GkF,EAAc,AAACxB,IAA2IyB,WAAW,CAACC,MAAM,CAACpL,SAAS,CAACqL,UAAU,CACjM,CAAExB,mBAAoByB,CAAiC,CAAER,gBAAiBS,CAA8B,CAAE,CAAG3B,EA4BhF4B,EAtBnC,cAA6BN,EAMzBO,UAAW,CAEP,KAAK,CAACA,SAASlD,KAAK,CADN,IAAI,CACUE,WAC5B,IAAMxF,EAASE,AAFD,IAAI,CAEGF,MAAM,CAAEyI,EAAQzI,EAAOK,OAAO,CAACoI,KAAK,CACzD,GAAIvI,AAHU,IAAI,CAGR4B,OAAO,EAAI5B,AAHP,IAAI,CAGSwC,SAAS,EAAI+F,EAAO,CAC3C,IAAMX,EAAQW,CAAK,CAACvI,AAJV,IAAI,CAIY8F,KAAK,CAC3ByC,EAAMxJ,MAAM,CAAC,CACjBoJ,EAAkCnI,AANxB,IAAI,CAM0B4B,OAAO,CAAEwG,EAA+BtI,EAAO0H,KAAK,CAAEI,GAAOrJ,MAAM,CAAEyB,AANnG,IAAI,CAMqGwC,SAAS,CAAClE,KAAK,EAAI,EAAG0B,AAN/H,IAAI,CAMiIwC,SAAS,CAACjE,MAAM,EAAIiK,IAAUxI,AANnK,IAAI,CAMqKF,MAAM,CAACK,OAAO,CAACyG,WAAW,EAAI,EACrN,CACJ,CACJ,EASA,IAAI6B,EAAmG9M,EAAoB,KACvH+M,EAAuH/M,EAAoBI,CAAC,CAAC0M,GAE7IE,EAA+GhN,EAAoB,KACnIiN,EAAmIjN,EAAoBI,CAAC,CAAC4M,GAEzJE,EAAuHlN,EAAoB,KAC3ImN,EAA2InN,EAAoBI,CAAC,CAAC8M,GA4BrK,IAAME,EAAe,AAACxC,IAA2IyB,WAAW,CAACC,MAAM,CACnLe,AAnRoB,CAAA,CAChBC,QAxjBJ,SAAiBC,CAAU,CAAEC,CAAW,CAAEC,CAAgB,EACtD,IAAMC,EAAaF,EAAYtM,SAAS,CAACqL,UAAU,CAAEoB,EAAaD,EAAWxM,SAAS,AACjFyM,CAAAA,EAAW7G,0BAA0B,GACtCnF,EAAS4L,EAAY,YAAa1J,GAClClC,EAAS4L,EAAY,SAAU3I,GAC/B9C,EAAO6L,EAAY,CACf7G,2BAA4Bd,CAChC,GACArE,EAAS+L,EAAY,YAAapI,GAClC3D,EAAS6L,EAAa,SAAUhH,GAChCtE,EAAKsL,EAAYtM,SAAS,CAAE,WAAYoI,GAExC3H,EAAS6L,EAAa,cAAe5D,GACrCjI,EAAS6L,EAAa,kBAAmB5D,GACzC9H,EAAO2L,EAAiBvM,SAAS,CAAE,CAC/BgF,WAAY6B,CAChB,GACApG,EAAS8L,EAAkB,eAAgBlI,GAEnD,EAsiBIpD,SAAAA,CACJ,CAAA,EAgRuBmL,OAAO,CAAE5C,IAAyGqC,IAA6GI,KACtP,GAAM,CAAE1L,WAAYmM,CAA0B,CAAE,CAAIpM,IAC9C,CAAEwK,gBAAiB6B,CAA+B,CAAElC,kBAAmBmC,CAAiC,CAAE/C,mBAAoBgD,EAAkC,CAAE,CAAGjD,EACrK,CAAEnJ,SAAUqM,EAAwB,CAAEpM,QAASqM,EAAuB,CAAElM,MAAOmM,EAAqB,CAAEC,WAAAA,EAAU,CAAEnM,KAAMoM,EAAoB,CAAE,CAAI5M,GAexJ,OAAM6M,WAAwBjB,EAkB1BpF,QAAQsG,CAAI,CAAE,CACV,GAAM,CAAE5H,MAAAA,CAAK,CAAEqD,MAAAA,CAAK,CAAE,CAAG,IAAI,CAAE3D,EAAYwH,EAA2B,IAAI,CAACpJ,OAAO,CAAC4B,SAAS,EAE5FmI,EAAmB,CACf,IAAI,CAACC,gBAAgB,GACrBpI,EAAUE,QAAQ,CAClBF,EAAUqI,MAAM,CAChBrI,EAAUsI,KAAK,CAClB,CAACC,IAAI,CAAC,KACHC,EAAoBlI,EAAMmI,WAAW,CAACN,EAAiB,CAE3D,GAAID,GAAQvE,EAAO,CACf,IAAM+E,EAAUpI,EAAMqI,UAAU,CAAC,IAAI,EAEhCH,IACDE,EAAQ5H,CAAC,CAAG4H,EAAQlM,MAAM,CAC1BkM,EAAQlM,MAAM,CAAG,EACjBgM,EAAoBlI,EAAM5C,QAAQ,CAACkL,QAAQ,CAACF,GAC5CpI,EAAMmI,WAAW,CAACN,EAAiB,CAAGK,GAE1C7E,EAAMkF,IAAI,CAACL,EAEf,MACK,GAAIA,GAEL,CAACA,EAAkBM,QAAQ,CAAC,wBAAyB,CACrD,IAAMC,EAAWzI,EAAMqI,UAAU,CAAC,IAAI,EACtCH,EACKQ,QAAQ,CAAC,wBACTpH,OAAO,CAACmH,EAAU/I,EAC3B,CACJ,CACAiJ,kBAAmB,CAAE,CACrBC,oBAAqB,CAAE,CACvBC,aAAalL,CAAK,CAAE,CAChB,IAAMkL,EAAe,KAAK,CAACA,aAAa9F,KAAK,CAAC,IAAI,CAAEE,WAAyDiD,EAAQ4C,AAArC,IAAI,CAAChL,OAAO,CAAuCoI,KAAK,CACxI,GAAIvI,GAASA,EAAMwC,SAAS,EAAI+F,EAAO,CACnC,IAAMX,EAAQW,CAAK,CAACvI,EAAM8F,KAAK,CAAGyC,EAAMxJ,MAAM,CAAC,CAAE,CAAE8D,EAAAA,CAAC,CAAEtE,OAAAA,CAAM,CAAE,CAAGiL,EAAgC1J,AAFE,IAAI,CAEC0H,KAAK,CAAEI,GAAQwD,EAAUxD,EAAMxL,UAAU,AAE7IgP,CAAAA,IAAYpL,EAAMoL,OAAO,EACzBpL,EAAMoL,OAAO,CAAGA,EAChBF,EAAa5G,IAAI,CAAG,CAChBpG,QAAS,CACLE,KAAM,CACFlC,EAAGkP,EACH9G,KAAM4G,EAAa5G,IAAI,CACvBM,YAAasG,CAAY,CAAC,eAAe,CACzCvG,OAAQuG,EAAavG,MAAM,AAC/B,EACA/B,EAAG5C,EAAMwC,SAAS,CAACI,CAAC,CACpBC,EAAGA,EACHvE,MAAO0B,EAAMwC,SAAS,CAAClE,KAAK,EAAI,EAChCC,OAAQA,EACR0F,oBAAqB,oBACrBO,gBAAiB,OACjBnG,MAAO,SACX,CACJ,GAEK2B,EAAMoL,OAAO,EAAIpL,EAAM4B,OAAO,EACnC,OAAOsJ,EAAa5G,IAAI,AAEhC,CAGA,OAFA,OAAO4G,EAAavG,MAAM,CAC1B,OAAOuG,EAAatG,WAAW,CACxBsG,CACX,CAIAG,aAAc,CACV,IAAMC,EAAW,KAAK,CAACD,YAAYjG,KAAK,CAAC,IAAI,CAAEE,WAA2BiD,EAAQzI,AAAd,IAAI,CAAiBK,OAAO,CAACoI,KAAK,CAUtG,OATIA,GACAA,EAAM9H,OAAO,CAAC,SAAUrC,CAAI,EACpBwL,GAAwBxL,EAAKe,GAAG,GAChCyK,GAAwB0B,EAASC,OAAO,GACxCnN,EAAKe,GAAG,CAAGmM,EAASC,OAAO,EAC3BD,CAAAA,EAASC,OAAO,CAAGnN,EAAKe,GAAG,AAAD,CAElC,GAEGmM,CACX,CACJ,CAwDA,SAASE,GAAkBC,CAAK,EAE5B,IAAMC,EAAYnP,OACboP,IAAI,CAACF,EAAMxL,MAAM,EACjBN,MAAM,CAAC,AAAC6F,GAAMA,EAAEoG,KAAK,CAAC,KAAK7M,MAAM,CAAG,GAAI8M,EAAYJ,EAAMK,IAAI,CAACzJ,KAAK,CAACvC,MAAM,CAAEiM,EAAgBL,EAAU9F,GAAG,CAAC,AAACvJ,GAAQ2P,WAAW3P,EAAIuP,KAAK,CAAC,IAAI,CAAC,EAAE,GACjJK,EAAc,GAClBF,EAActL,OAAO,CAAC,AAACqF,IACf+F,CAAS,CAAC/F,EAAM,EAAI+F,CAAS,CAAC/F,EAAM,CAAC/F,OAAO,EAC5CkM,CAAAA,EAAcnG,CAAI,CAE1B,GACA,IAAMhG,EAAS2L,EAAMK,IAAI,CAACzJ,KAAK,CAACvC,MAAM,CAACmM,EAAY,CACnD,GAAInM,GACAA,EAAOoM,EAAE,CAAC,cACVT,EAAMK,IAAI,CAACK,OAAO,IAClBrM,EAAOsM,KAAK,CAACD,OAAO,GAAI,CACxB,IAAMC,EAAQtM,EAAOsM,KAAK,CAAEjM,EAAUsL,EAAMK,IAAI,CAAC3L,OAAO,CAAEkC,EAAQoJ,EAAMK,IAAI,CAACzJ,KAAK,CAAEgK,EAAcZ,EAAMa,MAAM,CAAEC,EAAUH,EAAMtE,QAAQ,CAAC2D,EAAM7I,CAAC,CAAE,CAAA,GAAOA,EAAIP,EAAMoF,QAAQ,CAAG2E,EAAMvE,GAAG,CAAG0E,EAAUA,EAAShE,EAAQzI,EAAOK,OAAO,CAACoI,KAAK,EAAI,EAAE,CAAEzC,EAAQ2F,EAAM7I,CAAC,CAAG2F,EAAMxJ,MAAM,CAAE6I,EAAQW,CAAK,CAACzC,EAAM,CAAExH,EAAQwB,EAAO0M,gBAAgB,EAClU1M,EAAO0M,gBAAgB,GAAGlO,KAAK,CAAE,CAAEC,OAAAA,CAAM,CAAEsE,EAAAA,CAAC,CAAE,CAAG2G,EAAgC1J,EAAO0H,KAAK,CAAEI,GAAQ6E,EAAgBtM,EAAQkM,WAAW,CAAEzH,EAAcmF,GAAqB0C,GAAiBA,EAAc7F,WAAW,CAAE9G,EAAOK,OAAO,CAACyG,WAAW,CAAE,GAC3P,GAAI,CAACyF,GACDI,GACAA,EAAcC,OAAO,EACrB9E,EACI,AAAC6D,EAAMlE,WAAW,EAClBkE,CAAAA,EAAMlE,WAAW,CAAGlF,EAAM5C,QAAQ,CAACkN,CAAC,CAAC,gBAChCvI,GAAG,EAAC,EAEbqH,EAAMlE,WAAW,CAACpD,IAAI,CAAC,CACnByI,WAAYvK,EAAMoF,QAAQ,CACtBgE,EAAMK,IAAI,CAACe,GAAG,CAAGT,EAAMS,GAAG,CAC9BC,WAAYzK,EAAMoF,QAAQ,CACtB2E,EAAMS,GAAG,CAAGpB,EAAMK,IAAI,CAACe,GAAG,AAClC,GACApB,EAAMa,MAAM,CAAGjK,EAAM5C,QAAQ,CAACgF,IAAI,CAAC7B,EAAGC,EAAGvE,EAAOC,GAC3C4F,IAAI,CAAC,CACNG,KAAM,CACFpG,QAAS,CACLE,KAAM,CACFlC,EAAG0L,EAAMxL,UAAU,CACnBkI,KAAMmI,EAAcpO,KAAK,EACrB,UACJuG,YAAaA,EACbD,OAAQ8H,EAAcM,WAAW,EAC7B,aACR,EACAnK,EAAGA,EACHC,EAAGA,EACHvE,MAAOA,EACPC,OAAQA,EACR0F,oBAAqB,oBACrBO,gBAAiB,OACjBnG,MAAO,SACX,CACJ,CACJ,GACK+F,GAAG,CAACqH,EAAMlE,WAAW,EAC1BkC,EAAkCgC,EAAMlE,WAAW,CAAEkE,EAAMK,IAAI,EAC/DpC,GAAmC+B,EAAMa,MAAM,CAAE/N,EAAQD,EAAOC,EAAQqG,GACxE6G,EAAMuB,SAAS,CAAClN,EAAOmN,YAAY,EAAI,EAAGnN,EAAOoN,IAAI,EAAI,QAExD,GAAIb,GAAeZ,EAAMlE,WAAW,CAAE,CACvC8E,EAAY1I,OAAO,CAAC,CAChBf,EAAAA,EACAC,EAAAA,EACAvE,MAAAA,EACAC,OAAAA,CACJ,GACA,IAAyC+F,EAAO+H,EAAYlI,IAAI,CAAC,QAAS0C,EAAQvC,GAAQA,EAAKuC,KAAK,CAA7E,iBACnBA,CAAAA,GAASxE,EAAM5C,QAAQ,CAACsB,eAAe,EACvCsB,EAAM5C,QAAQ,CAACsB,eAAe,CAAC8F,CAAK,CAAC,EAAE,CAACxB,KAAK,CAAC,GAAG,CAC5C1B,OAAO,CAAC,CACTf,EAAAA,EACAC,EAAAA,EACAvE,MAAAA,EACAC,OAAAA,CACJ,GAEJkN,EAAMlE,WAAW,CAAC5D,OAAO,CAAC,CACtBiJ,WAAYvK,EAAMoF,QAAQ,CACtBgE,EAAMK,IAAI,CAACe,GAAG,CAAGT,EAAMS,GAAG,CAC9BC,WAAYzK,EAAMoF,QAAQ,CACtB2E,EAAMS,GAAG,CAAGpB,EAAMK,IAAI,CAACe,GAAG,AAClC,GACApD,EAAkCgC,EAAMlE,WAAW,CAAEkE,EAAMK,IAAI,EAC/DpC,GAAmC2C,EAAa9N,EAAQD,EAAOC,EAAQqG,GACvE6G,EAAMuB,SAAS,CAAClN,EAAOmN,YAAY,EAAI,EAAGnN,EAAOoN,IAAI,EAAI,EAC7D,CACJ,MACSzB,EAAMa,MAAM,EAAIb,EAAMlE,WAAW,GACtCkE,EAAMa,MAAM,CAACtL,OAAO,GACpByK,EAAMa,MAAM,CAAG,KAAK,EACpBb,EAAMlE,WAAW,CAACvG,OAAO,GACzByK,EAAMlE,WAAW,CAAG,KAAK,EAEjC,CAIA,SAAS4F,GAAa9K,CAAK,CAAE+K,CAAQ,EAC7B/K,EAAMgL,IAAI,EACVhL,EAAMgL,IAAI,CAAC5M,OAAO,CAAC,SAAUqL,CAAI,EACxBA,EAAKwB,QAAQ,EAKlBxD,GAFegC,EAAKwB,QAAQ,CAACC,MAAM,CAEhB,SAAUC,CAAI,EAC7B1D,GAAW0D,EAAM,SAAU/B,CAAK,EAC5B2B,EAAS3B,EACb,EACJ,EACJ,EAER,CAiBA,SAASgC,GAAuBpL,CAAK,EACjC8K,GAAa9K,EAAO,SAAUoJ,CAAK,EAC3BA,EAAMa,MAAM,EAAIb,EAAMlE,WAAW,GACjCkE,EAAMa,MAAM,CAACtL,OAAO,GACpByK,EAAMlE,WAAW,CAACvG,OAAO,GACzB,OAAOyK,EAAMa,MAAM,CACnB,OAAOb,EAAMlE,WAAW,CAEhC,EACJ,CA5LAyC,GAAgB0D,cAAc,CAAG7D,GAAsBd,EAAa2E,cAAc,CAmBlF,CACI9G,YAAa,CACjB,GAMA+C,GAAyBK,GAAiB,cAAe,WACrD,IAAMlK,EAAS,IAAI,CAAEyI,EAAQzI,EAAOK,OAAO,CAACoI,KAAK,CAAEoF,EAAiB,iBACpE7N,EAAOG,MAAM,CAACQ,OAAO,CAAC,SAAUT,CAAK,EACjC,GAAIA,EAAM4B,OAAO,EAAI5B,EAAMwC,SAAS,EAAI+F,EAAO,CAC3C,IAAMX,EAAQW,CAAK,CAACvI,EAAM8F,KAAK,CAAGyC,EAAMxJ,MAAM,CAAC,CAAEuF,EAAOtE,EAAM4B,OAAO,CAACuC,IAAI,CAAC,QAAS0C,EAAQvC,GAAQA,EAAKuC,KAAK,CAAC8G,GAAiB,CAAE9K,EAAAA,CAAC,CAAEtE,OAAAA,CAAM,CAAE,CAAGiL,EAAgC1J,EAAO0H,KAAK,CAAEI,GAC9L,GAAIf,GAAS/G,EAAOuC,KAAK,CAAC5C,QAAQ,CAACsB,eAAe,CAAE,CAChD,IAAM6M,EAAiB9N,EAAOuC,KAAK,CAAC5C,QAAQ,CAACsB,eAAe,CAAC8F,CAAK,CAAC,EAAE,CAACxB,KAAK,CAAC,GAAG,AAC3EuI,CAAAA,GACAA,EAAejK,OAAO,CAAC,CACnBf,EAAG5C,EAAMwC,SAAS,CAACI,CAAC,CACpBC,EAAGA,EACHvE,MAAO0B,EAAMwC,SAAS,CAAClE,KAAK,EAAI,EAChCC,OAAQA,CACZ,EAER,CACAmL,GAAmC1J,EAAM4B,OAAO,CAAE4H,EAAgC1J,EAAO0H,KAAK,CAAEI,GAAOrJ,MAAM,CAAEyB,EAAMwC,SAAS,CAAClE,KAAK,EAAI,EAAG0B,EAAMwC,SAAS,CAACjE,MAAM,EAAIiK,IAAU1I,EAAOK,OAAO,CAACyG,WAAW,EAAI,EACjN,CACJ,EACJ,GAqHA+C,GAA0BtD,IAAwG,SAAU,WACxI8G,GAAa,IAAI,CAAE3B,GACvB,GACA7B,GAA0Bf,IAAwH,iBAAkB,SAAUiF,CAAC,EAC3K,GAAI,IAAI,CAACvB,MAAM,CAAE,CACb,GAAM,CAAEjK,MAAAA,CAAK,CAAEwF,IAAAA,CAAG,CAAE,CAAG,IAAI,CAACiE,IAAI,CAAE,CAAEgC,QAAAA,CAAO,CAAExP,MAAAA,CAAK,CAAE,CAAGuP,EAAGjB,EAAavK,EAAMoF,QAAQ,CAAGqG,EAAUzL,EAAM+J,KAAK,CAAC,EAAE,CAACvE,GAAG,CAAGiG,EAAShB,EAAazK,EAAMoF,QAAQ,CAAG,CAACI,EAAM,EACrK,IAAI,CAACyE,MAAM,CAACnI,IAAI,CAAC,CACbyI,WAAAA,EACAE,WAAAA,CACJ,GACA,IAAI,CAACR,MAAM,CAAC3I,OAAO,CAAC,CAAErF,MAAAA,CAAM,EAChC,CACJ,GAeAqL,GAA0BtD,IAAwG,iBAAkB,WAChJoH,GAAuB,IAAI,CAC/B,GACA9D,GAA0BtD,IAAwG,eAAgB,WAC9IoH,GAAuB,IAAI,CAC/B,GACAzD,GAAgBnN,SAAS,CAACqL,UAAU,CAAGG,EACvC9B,IAA0IwH,kBAAkB,CAAC,YAAa/D,IAoL7I,IAAM/M,GAAkBE,IAG3C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}