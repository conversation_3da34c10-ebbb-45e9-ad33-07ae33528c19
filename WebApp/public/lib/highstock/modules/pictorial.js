!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/pictorial
 * @requires highcharts
 *
 * Pictorial graph series type for Highcharts
 *
 * (c) 2010-2025 <PERSON>stein <PERSON>si, Magdalena Gut
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.Series.types.column,t._Highcharts.Chart,t._Highcharts.SeriesRegistry,t._Highcharts.Series,t._Highcharts.StackItem,t._Highcharts.SVGRenderer):"function"==typeof define&&define.amd?define("highcharts/modules/pictorial",["highcharts/highcharts"],function(t){return e(t,t.Series,["types"],["column"],t.Chart,t.SeriesRegistry,t.Series,t.StackItem,t.SVGRenderer)}):"object"==typeof exports?exports["highcharts/modules/pictorial"]=e(t._Highcharts,t._Highcharts.Series.types.column,t._Highcharts.Chart,t._Highcharts.SeriesRegistry,t._Highcharts.Series,t._Highcharts.StackItem,t._Highcharts.SVGRenderer):t.Highcharts=e(t.Highcharts,t.Highcharts.Series.types.column,t.Highcharts.Chart,t.Highcharts.SeriesRegistry,t.Highcharts.Series,t.Highcharts.StackItem,t.Highcharts.SVGRenderer)}("undefined"==typeof window?this:window,(t,e,r,i,s,a,o)=>(()=>{"use strict";var h={184:t=>{t.exports=a},448:t=>{t.exports=e},512:t=>{t.exports=i},540:t=>{t.exports=o},820:t=>{t.exports=s},944:e=>{e.exports=t},960:t=>{t.exports=r}},n={};function l(t){var e=n[t];if(void 0!==e)return e.exports;var r=n[t]={exports:{}};return h[t](r,r.exports,l),r.exports}l.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return l.d(e,{a:e}),e},l.d=(t,e)=>{for(var r in e)l.o(e,r)&&!l.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},l.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var p={};l.d(p,{default:()=>tp});var d=l(944),c=l.n(d);l(448);let{animObject:f}=c(),{getOptions:g}=c(),{addEvent:u,defined:m,erase:x,extend:w,merge:y,pick:b,removeEvent:A,wrap:M}=c(),S=function(){let t=[],e=g().colors,r=0;for(let i of["M 0 0 L 5 5 M 4.5 -0.5 L 5.5 0.5 M -0.5 4.5 L 0.5 5.5","M 0 5 L 5 0 M -0.5 0.5 L 0.5 -0.5 M 4.5 5.5 L 5.5 4.5","M 2 0 L 2 5 M 4 0 L 4 5","M 0 2 L 5 2 M 0 4 L 5 4","M 0 1.5 L 2.5 1.5 L 2.5 0 M 2.5 5 L 2.5 3.5 L 5 3.5"])t.push({path:i,color:e[r++],width:5,height:5,patternTransform:"scale(1.4 1.4)"});for(let i of(r=5,["M 0 0 L 5 10 L 10 0","M 3 3 L 8 3 L 8 8 L 3 8 Z","M 5 5 m -4 0 a 4 4 0 1 1 8 0 a 4 4 0 1 1 -8 0","M 0 0 L 10 10 M 9 -1 L 11 1 M -1 9 L 1 11","M 0 10 L 10 0 M -1 1 L 1 -1 M 9 11 L 11 9"]))t.push({path:i,color:e[r],width:10,height:10}),r+=5;return t}();function _(t,e){let r=JSON.stringify(t),i=r.length||0,s=0,a=0,o;if(e){o=Math.max(Math.floor(i/500),1);for(let t=0;t<i;t+=o)s+=r.charCodeAt(t);s&=s}for(;a<i;++a)s=(s<<5)-s+r.charCodeAt(a),s&=s;return s.toString(16).replace("-","1")}function C(){if(this.renderer&&(this.renderer.defIds||[]).filter(t=>t&&t.indexOf&&0===t.indexOf("highcharts-pattern-")).length){for(let t of this.series)if(t.visible)for(let e of t.points){let t=e.options&&e.options.color;t&&t.pattern&&(t.pattern._width="defer",t.pattern._height="defer")}this.redraw(!1)}}function k(){let t={},e=this.renderer,r=(e.defIds||[]).filter(t=>t.indexOf&&0===t.indexOf("highcharts-pattern-"));if(r.length)for(let i of([].forEach.call(this.renderTo.querySelectorAll('[color^="url("], [fill^="url("], [stroke^="url("]'),r=>{let i=r.getAttribute("fill")||r.getAttribute("color")||r.getAttribute("stroke");i&&(t[i.replace(e.url,"").replace("url(#","").replace(")","")]=!0)}),r))!t[i]&&(x(e.defIds,i),e.patternElements[i]&&(e.patternElements[i].destroy(),delete e.patternElements[i]))}function v(){let t=this.options.color;t&&t.pattern&&("string"==typeof t.pattern.path&&(t.pattern.path={d:t.pattern.path}),this.color=this.options.color=y(this.series.options.color,t))}function E(t){let e=t.args[0],r=t.args[1],i=t.args[2],s=this.chartIndex||0,a=e.pattern,o="#333333";if(void 0!==e.patternIndex&&S&&(a=S[e.patternIndex]),!a)return!0;if(a.image||"string"==typeof a.path||a.path&&a.path.d){let t=i.parentNode&&i.parentNode.getAttribute("class");t=t&&t.indexOf("highcharts-legend")>-1,("defer"===a._width||"defer"===a._height)&&L.call({graphic:{element:i}},a),(t||!a.id)&&((a=y({},a)).id="highcharts-pattern-"+s+"-"+_(a)+_(a,!0)),this.addPattern(a,!this.forExport&&b(a.animation,this.globalAnimation,{duration:100})),o=`url(${this.url}#${a.id+(this.forExport?"-export":"")})`}else o=a.color||o;return i.setAttribute(r,o),e.toString=function(){return o},!1}function H(){let t=this.chart.isResizing;if(this.isDirtyData||t||!this.chart.hasRendered)for(let e of this.points){let r=e.options&&e.options.color;r&&r.pattern&&(t&&!(e.shapeArgs&&e.shapeArgs.width&&e.shapeArgs.height)?(r.pattern._width="defer",r.pattern._height="defer"):e.calculatePatternDimensions(r.pattern))}}function L(t){if(t.width&&t.height)return;let e=this.graphic&&(this.graphic.getBBox&&this.graphic.getBBox(!0)||this.graphic.element&&this.graphic.element.getBBox())||{},r=this.shapeArgs;if(r&&(e.width=r.width||e.width,e.height=r.height||e.height,e.x=r.x||e.x,e.y=r.y||e.y),t.image){if(!e.width||!e.height){t._width="defer",t._height="defer";let e=this.series.chart.mapView&&this.series.chart.mapView.getSVGTransform().scaleY;m(e)&&e<0&&(t._inverted=!0);return}t.aspectRatio&&(e.aspectRatio=e.width/e.height,t.aspectRatio>e.aspectRatio?e.aspectWidth=e.height*t.aspectRatio:e.aspectHeight=e.width/t.aspectRatio),t._width=t.width||Math.ceil(e.aspectWidth||e.width),t._height=t.height||Math.ceil(e.aspectHeight||e.height)}t.width||(t._x=t.x||0,t._x+=e.x-Math.round(e.aspectWidth?Math.abs(e.aspectWidth-e.width)/2:0)),t.height||(t._y=t.y||0,t._y+=e.y-Math.round(e.aspectHeight?Math.abs(e.aspectHeight-e.height)/2:0))}function O(t,e){let r=b(e,!0),i=f(r),s=t.color||"#333333",a=t.height||("number"==typeof t._height?t._height:0)||32,o=t.width||("number"==typeof t._width?t._width:0)||32,h,n=t.id,l;if(!n&&(this.idCounter=this.idCounter||0,n="highcharts-pattern-"+this.idCounter+"-"+(this.chartIndex||0),++this.idCounter),this.forExport&&(n+="-export"),this.defIds=this.defIds||[],this.defIds.indexOf(n)>-1)return;this.defIds.push(n);let p={id:n,patternUnits:"userSpaceOnUse",patternContentUnits:t.patternContentUnits||"userSpaceOnUse",width:o,height:a,x:t._x||t.x||0,y:t._y||t.y||0};t._inverted&&(p.patternTransform="scale(1, -1)",t.patternTransform&&(t.patternTransform+=" scale(1, -1)")),t.patternTransform&&(p.patternTransform=t.patternTransform);let d=this.createElement("pattern").attr(p).add(this.defs);if(d.id=n,t.path){let e;l=c().isObject(t.path)?t.path:{d:t.path},t.backgroundColor&&(e=t.backgroundColor,this.rect(0,0,o,a).attr({fill:e}).add(d)),h={d:l.d},this.styledMode||(h.stroke=l.stroke||s,h["stroke-width"]=b(l.strokeWidth,2),h.fill=l.fill||"none"),l.transform&&(h.transform=l.transform),this.createElement("path").attr(h).add(d),d.color=s}else t.image&&(r?this.image(t.image,0,0,o,a,function(){this.animate({opacity:b(t.opacity,1)},i),A(this.element,"load")}).attr({opacity:0}).add(d):this.image(t.image,0,0,o,a).add(d));return t.image&&r||void 0===t.opacity||[].forEach.call(d.element.childNodes,e=>{e.setAttribute("opacity",t.opacity)}),this.patternElements=this.patternElements||{},this.patternElements[n]=d,d}function G(t){let e=this.options.color;e&&e.pattern&&!e.pattern.color?(delete this.options.color,t.apply(this,[].slice.call(arguments,1)),e.pattern.color=this.color,this.color=this.options.color=e):t.apply(this,[].slice.call(arguments,1))}function R(){if(!this.chart?.mapView)return;let t=this.chart.renderer,e=t.patternElements;t.defIds?.length&&e&&this.points.filter(function(t){return!!t.graphic&&(t.graphic.element.hasAttribute("fill")||t.graphic.element.hasAttribute("color")||t.graphic.element.hasAttribute("stroke"))&&!t.options.color?.pattern?.image&&!!t.group?.scaleX&&!!t.group?.scaleY}).map(function(e){return{id:(e.graphic?.element.getAttribute("fill")||e.graphic?.element.getAttribute("color")||e.graphic?.element.getAttribute("stroke")||"").replace(t.url,"").replace("url(#","").replace(")",""),x:e.group?.scaleX||1,y:e.group?.scaleY||1}}).filter(function(t,e,r){return""!==t.id&&-1!==t.id.indexOf("highcharts-pattern-")&&!r.some(function(r,i){return r.id===t.id&&i<e})}).forEach(function(t){let r=t.id;e[r].scaleX=1/t.x,e[r].scaleY=1/t.y,e[r].updateTransform("patternTransform")})}var I=l(960),B=l.n(I),W=l(512),D=l.n(W);let{defined:T}=c(),P={rescalePatternFill:function(t,e,r,i,s=1){let a=t&&t.attr("fill"),o=a&&a.match(/url\(([^)]+)\)/);if(o){let a=document.querySelector(`${o[1]} path`);if(a){let o=a.getBBox();if(0===o.width){let e=a.parentElement;t.renderer.box.appendChild(a),o=a.getBBox(),e.appendChild(a)}let h=1/(o.width+s),n=e/i/o.height,l=o.width/o.height,p=r/e,d=-o.width/2;l<p&&(h=h*l/p),a.setAttribute("stroke-width",s/(r*h)),a.setAttribute("transform",`translate(0.5, 0)scale(${h} ${n}) translate(${d+s*h/2}, ${-o.y})`)}}},invertShadowGroup:function(t,e){let r=e.chart.inverted;r&&t.attr({rotation:90*!!r,scaleX:r?-1:1})},getStackMetrics:function(t,e){let r=t.len,i=0;return e&&T(e.max)&&(i=t.toPixels(e.max,!0),r=t.len-i),{height:r,y:i}}},j=D().seriesTypes.column.prototype.pointClass,{rescalePatternFill:U,getStackMetrics:V}=P,X=class extends j{setState(){super.setState.apply(this,arguments);let t=this.series,e=t.options.paths;if(this.graphic&&this.shapeArgs&&e){let r=e[this.index%e.length];U(this.graphic,V(t.yAxis,r).height,this.shapeArgs.width||0,this.shapeArgs.height||1/0,this.series.options.borderWidth||0)}}};var $=l(820),Y=l.n($),F=l(184),N=l.n(F),q=l(540),z=l.n(q);let Z=D().seriesTypes.column;({compose:function(t,e,r){let i=e.prototype.pointClass,s=i.prototype;s.calculatePatternDimensions||(u(t,"endResize",C),u(t,"redraw",k),w(s,{calculatePatternDimensions:L}),u(i,"afterInit",v),u(e,"render",H),M(e.prototype,"getColor",G),u(e,"afterRender",R),u(e,"mapZoomComplete",R),w(r.prototype,{addPattern:O}),u(r,"complexColor",E))},patterns:S}).compose(B(),Y(),z());let{animObject:J}=c(),{getStackMetrics:K,invertShadowGroup:Q,rescalePatternFill:tt}=P,{addEvent:te,defined:tr,merge:ti,objectEach:ts,pick:ta}=c();class to extends Z{animate(t){let{chart:e,group:r}=this,i=J(this.options.animation),s=[this.getSharedClipKey(),i.duration,i.easing,i.defer].join(","),a=e.sharedClips[s];if(t&&r){let t=e.getClipBox(this);a||(t.y=t.height,t.height=0,a=e.renderer.clipRect(t),e.sharedClips[s]=a),r.clip(a)}else if(a&&!a.hasClass("highcharts-animating")){let t=e.getClipBox(this);a.addClass("highcharts-animating").animate(t,i)}}animateDrilldown(){}animateDrillupFrom(){}pointAttribs(t){let e=super.pointAttribs.apply(this,arguments),r=this.options.paths;if(t&&t.shapeArgs&&r){let i=r[t.index%r.length],{y:s,height:a}=K(this.yAxis,i),o=i.definition;o!==t.pathDef?(t.pathDef=o,e.fill={pattern:{path:{d:o,fill:e.fill,strokeWidth:e["stroke-width"],stroke:e.stroke},x:t.shapeArgs.x,y:s,width:t.shapeArgs.width||0,height:a,patternContentUnits:"objectBoundingBox",backgroundColor:"none",color:"#ff0000"}}):t.pathDef&&t.graphic&&delete e.fill}return delete e.stroke,delete e.strokeWidth,e}getExtremes(){let t=super.getExtremes.apply(this,arguments),e=this.options.paths;return e&&e.forEach(function(e){tr(e.max)&&tr(t.dataMax)&&e.max>t.dataMax&&(t.dataMax=e.max)}),t}}function th(t){let e=Object.keys(t.points).filter(t=>t.split(",").length>1),r=t.axis.chart.series,i=e.map(t=>parseFloat(t.split(",")[0])),s=-1;i.forEach(t=>{r[t]&&r[t].visible&&(s=t)});let a=t.axis.chart.series[s];if(a&&a.is("pictorial")&&t.axis.hasData()&&a.xAxis.hasData()){let e=a.xAxis,r=t.axis.options,i=t.axis.chart,s=t.shadow,o=e.toPixels(t.x,!0),h=i.inverted?e.len-o:o,n=a.options.paths||[],l=t.x%n.length,p=n[l],d=a.getColumnMetrics&&a.getColumnMetrics().width,{height:c,y:f}=K(a.yAxis,p),g=r.stackShadow,u=ta(g&&g.borderWidth,a.options.borderWidth,1);if(!s&&g&&g.enabled&&p)t.shadowGroup||(t.shadowGroup=i.renderer.g("shadow-group").add()),t.shadowGroup.attr({translateX:i.inverted?t.axis.pos:e.pos,translateY:i.inverted?e.pos:t.axis.pos}),t.shadow=i.renderer.rect(h,f,d,c).attr({fill:{pattern:{path:{d:p.definition,fill:g.color||"#dedede",strokeWidth:u,stroke:g.borderColor||"transparent"},x:h,y:f,width:d,height:c,patternContentUnits:"objectBoundingBox",backgroundColor:"none",color:"#dedede"}}}).add(t.shadowGroup),Q(t.shadowGroup,t.axis),tt(t.shadow,c,d,c,u),t.setOffset(a.pointXOffset||0,a.barW||0);else if(s&&t.shadowGroup){s.animate({x:h,y:f,width:d,height:c});let r=s.attr("fill"),o=r&&r.match(/url\(([^)]+)\)/);o&&i.renderer.patternElements&&i.renderer.patternElements[o[1].slice(1)].animate({x:h,y:f,width:d,height:c}),t.shadowGroup.animate({translateX:i.inverted?t.axis.pos:e.pos,translateY:i.inverted?e.pos:t.axis.pos}),Q(t.shadowGroup,t.axis),tt(s,c,d,c,u),t.setOffset(a.pointXOffset||0,a.barW||0)}}else t.shadow&&t.shadowGroup&&(t.shadow.destroy(),t.shadow=void 0,t.shadowGroup.destroy(),t.shadowGroup=void 0)}function tn(t,e){t.axes&&t.axes.forEach(function(t){t.stacking&&ts(t.stacking.stacks,function(t){ts(t,function(t){e(t)})})})}function tl(t){tn(t,function(t){t.shadow&&t.shadowGroup&&(t.shadow.destroy(),t.shadowGroup.destroy(),delete t.shadow,delete t.shadowGroup)})}to.defaultOptions=ti(Z.defaultOptions,{borderWidth:0}),te(to,"afterRender",function(){let t=this,e=t.options.paths,r=/url\(([^)]+)\)/;t.points.forEach(function(i){if(i.graphic&&i.shapeArgs&&e){let s=e[i.index%e.length],a=i.graphic.attr("fill"),o=a&&a.match(r),{y:h,height:n}=K(t.yAxis,s);if(o&&t.chart.renderer.patternElements){let e=t.chart.renderer.patternElements[o[1].slice(1)];e&&e.animate({x:i.shapeArgs.x,y:h,width:i.shapeArgs.width||0,height:n})}tt(i.graphic,K(t.yAxis,s).height,i.shapeArgs.width||0,i.shapeArgs.height||1/0,t.options.borderWidth||0)}})}),te(B(),"render",function(){tn(this,th)}),te(N(),"afterSetOffset",function(t){if(this.shadow){let{chart:e,len:r}=this.axis,{xOffset:i,width:s}=t,a=e.inverted?i-e.xAxis[0].len:i,o=e.inverted?-r:0;this.shadow.attr({translateX:a,translateY:o}),this.shadow.animate({width:s})}}),te(B(),"afterDrilldown",function(){tl(this)}),te(B(),"afterDrillUp",function(){tl(this)}),to.prototype.pointClass=X,D().registerSeriesType("pictorial",to);let tp=c();return p.default})());