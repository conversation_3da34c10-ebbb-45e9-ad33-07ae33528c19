{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/exporting\n * @requires highcharts\n *\n * Exporting module\n *\n * (c) 2010-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"AST\"], root[\"_Highcharts\"][\"Chart\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/exporting\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"AST\"],amd1[\"Chart\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/exporting\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"AST\"], root[\"_Highcharts\"][\"Chart\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"AST\"], root[\"Highcharts\"][\"Chart\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__660__, __WEBPACK_EXTERNAL_MODULE__960__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 660:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__660__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ }),\n\n/***/ 960:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__960__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ exporting_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"AST\"],\"commonjs\":[\"highcharts\",\"AST\"],\"commonjs2\":[\"highcharts\",\"AST\"],\"root\":[\"Highcharts\",\"AST\"]}\nvar highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_ = __webpack_require__(660);\nvar highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default = /*#__PURE__*/__webpack_require__.n(highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Chart\"],\"commonjs\":[\"highcharts\",\"Chart\"],\"commonjs2\":[\"highcharts\",\"Chart\"],\"root\":[\"Highcharts\",\"Chart\"]}\nvar highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_ = __webpack_require__(960);\nvar highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default = /*#__PURE__*/__webpack_require__.n(highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_);\n;// ./code/es-modules/Core/Chart/ChartNavigationComposition.js\n/**\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Composition\n *\n * */\nvar ChartNavigationComposition;\n(function (ChartNavigationComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * @private\n     */\n    function compose(chart) {\n        if (!chart.navigation) {\n            chart.navigation = new Additions(chart);\n        }\n        return chart;\n    }\n    ChartNavigationComposition.compose = compose;\n    /* *\n     *\n     *  Class\n     *\n     * */\n    /**\n     * Initializes `chart.navigation` object which delegates `update()` methods\n     * to all other common classes (used in exporting and navigationBindings).\n     * @private\n     */\n    class Additions {\n        /* *\n         *\n         *  Constructor\n         *\n         * */\n        constructor(chart) {\n            this.updates = [];\n            this.chart = chart;\n        }\n        /* *\n         *\n         *  Functions\n         *\n         * */\n        /**\n         * Registers an `update()` method in the `chart.navigation` object.\n         *\n         * @private\n         * @param {UpdateFunction} updateFn\n         * The `update()` method that will be called in `chart.update()`.\n         */\n        addUpdate(updateFn) {\n            this.chart.navigation.updates.push(updateFn);\n        }\n        /**\n         * @private\n         */\n        update(options, redraw) {\n            this.updates.forEach((updateFn) => {\n                updateFn.call(this.chart, options, redraw);\n            });\n        }\n    }\n    ChartNavigationComposition.Additions = Additions;\n})(ChartNavigationComposition || (ChartNavigationComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Chart_ChartNavigationComposition = (ChartNavigationComposition);\n\n;// ./code/es-modules/Extensions/DownloadURL.js\n/* *\n *\n *  (c) 2015-2025 Oystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Mixin for downloading content in the browser\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\nconst { isSafari, win, win: { document: doc } } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { error } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\nconst domurl = win.URL || win.webkitURL || win;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Convert base64 dataURL to Blob if supported, otherwise returns undefined.\n *\n * @private\n * @function Highcharts.dataURLtoBlob\n *\n * @param {string} dataURL\n * URL to convert.\n *\n * @return {string | undefined}\n * Blob.\n */\nfunction dataURLtoBlob(dataURL) {\n    const parts = dataURL\n        .replace(/filename=.*;/, '')\n        .match(/data:([^;]*)(;base64)?,([A-Z+\\d\\/]+)/i);\n    if (parts &&\n        parts.length > 3 &&\n        (win.atob) &&\n        win.ArrayBuffer &&\n        win.Uint8Array &&\n        win.Blob &&\n        (domurl.createObjectURL)) {\n        // Try to convert data URL to Blob\n        const binStr = win.atob(parts[3]), buf = new win.ArrayBuffer(binStr.length), binary = new win.Uint8Array(buf);\n        for (let i = 0; i < binary.length; ++i) {\n            binary[i] = binStr.charCodeAt(i);\n        }\n        return domurl\n            .createObjectURL(new win.Blob([binary], { 'type': parts[1] }));\n    }\n}\n/**\n * Download a data URL in the browser. Can also take a blob as first param.\n *\n * @private\n * @function Highcharts.downloadURL\n *\n * @param {string | global.URL} dataURL\n * The dataURL/Blob to download.\n * @param {string} filename\n * The name of the resulting file (w/extension).\n */\nfunction downloadURL(dataURL, filename) {\n    const nav = win.navigator, a = doc.createElement('a');\n    // IE specific blob implementation\n    // Don't use for normal dataURLs\n    if (typeof dataURL !== 'string' &&\n        !(dataURL instanceof String) &&\n        nav.msSaveOrOpenBlob) {\n        nav.msSaveOrOpenBlob(dataURL, filename);\n        return;\n    }\n    dataURL = '' + dataURL;\n    if (nav.userAgent.length > 1000 /* RegexLimits.shortLimit */) {\n        throw new Error('Input too long');\n    }\n    const // Some browsers have limitations for data URL lengths. Try to convert\n    // to Blob or fall back. Edge always needs that blob.\n    isOldEdgeBrowser = /Edge\\/\\d+/.test(nav.userAgent), \n    // Safari on iOS needs Blob in order to download PDF\n    safariBlob = (isSafari &&\n        typeof dataURL === 'string' &&\n        dataURL.indexOf('data:application/pdf') === 0);\n    if (safariBlob || isOldEdgeBrowser || dataURL.length > 2000000) {\n        dataURL = dataURLtoBlob(dataURL) || '';\n        if (!dataURL) {\n            throw new Error('Failed to convert to blob');\n        }\n    }\n    // Try HTML5 download attr if supported\n    if (typeof a.download !== 'undefined') {\n        a.href = dataURL;\n        a.download = filename; // HTML5 download attribute\n        doc.body.appendChild(a);\n        a.click();\n        doc.body.removeChild(a);\n    }\n    else {\n        // No download attr, just opening data URI\n        try {\n            if (!win.open(dataURL, 'chart')) {\n                throw new Error('Failed to open window');\n            }\n        }\n        catch {\n            // If window.open failed, try location.href\n            win.location.href = dataURL;\n        }\n    }\n}\n/**\n * Asynchronously downloads a script from a provided location.\n *\n * @private\n * @function Highcharts.getScript\n *\n * @param {string} scriptLocation\n * The location for the script to fetch.\n */\nfunction getScript(scriptLocation) {\n    return new Promise((resolve, reject) => {\n        const head = doc.getElementsByTagName('head')[0], script = doc.createElement('script');\n        // Set type and location for the script\n        script.type = 'text/javascript';\n        script.src = scriptLocation;\n        // Resolve in case of a succesful script fetching\n        script.onload = () => {\n            resolve();\n        };\n        // Reject in case of fail\n        script.onerror = () => {\n            reject(error(`Error loading script ${scriptLocation}`));\n        };\n        // Append the newly created script\n        head.appendChild(script);\n    });\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst DownloadURL = {\n    dataURLtoBlob,\n    downloadURL,\n    getScript\n};\n/* harmony default export */ const Extensions_DownloadURL = (DownloadURL);\n\n;// ./code/es-modules/Extensions/Exporting/ExportingDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { isTouchDevice } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  API Options\n *\n * */\n// Add the export related options\n/**\n * Options for the exporting module. For an overview on the matter, see\n * [the docs](https://www.highcharts.com/docs/export-module/export-module-overview) and\n * read our [Fair Usage Policy](https://www.highcharts.com/docs/export-module/privacy-disclaimer-export).\n *\n * @requires     modules/exporting\n * @optionparent exporting\n */\nconst exporting = {\n    /**\n     * Experimental setting to allow HTML inside the chart (added through\n     * the `useHTML` options), directly in the exported image. This allows\n     * you to preserve complicated HTML structures like tables or bi-directional\n     * text in exported charts.\n     *\n     * Disclaimer: The HTML is rendered in a `foreignObject` tag in the\n     * generated SVG. The official export server is based on PhantomJS,\n     * which supports this, but other SVG clients, like Batik, does not\n     * support it. This also applies to downloaded SVG that you want to\n     * open in a desktop client.\n     *\n     * @type      {boolean}\n     * @default   false\n     * @since     4.1.8\n     * @apioption exporting.allowHTML\n     */\n    /**\n     * Allows the end user to sort the data table by clicking on column headers.\n     *\n     * @since     10.3.3\n     * @apioption exporting.allowTableSorting\n     */\n    allowTableSorting: true,\n    /**\n     * Allow exporting a chart retaining any user-applied CSS.\n     *\n     * Note that this is is default behavior in [styledMode](#chart.styledMode).\n     *\n     * @see [styledMode](#chart.styledMode)\n     *\n     * @sample {highcharts} highcharts/exporting/apply-stylesheets/\n     *\n     * @type      {boolean}\n     * @default   false\n     * @since     12.0.0\n     * @apioption exporting.applyStyleSheets\n     */\n    /**\n     * Additional chart options to be merged into the chart before exporting to\n     * an image format. This does not apply to printing the chart via the export\n     * menu.\n     *\n     * For example, a common use case is to add data labels to improve\n     * readability of the exported chart, or to add a printer-friendly color\n     * scheme to exported PDFs.\n     *\n     * @sample {highcharts} highcharts/exporting/chartoptions-data-labels/\n     *         Added data labels\n     * @sample {highstock} highcharts/exporting/chartoptions-data-labels/\n     *         Added data labels\n     *\n     * @type      {Highcharts.Options}\n     * @apioption exporting.chartOptions\n     */\n    /**\n     * Whether to enable the exporting module. Disabling the module will\n     * hide the context button, but API methods will still be available.\n     *\n     * @sample {highcharts} highcharts/exporting/enabled-false/\n     *         Exporting module is loaded but disabled\n     * @sample {highstock} highcharts/exporting/enabled-false/\n     *         Exporting module is loaded but disabled\n     *\n     * @type      {boolean}\n     * @default   true\n     * @since     2.0\n     * @apioption exporting.enabled\n     */\n    /**\n     * Function to call if the offline-exporting module fails to export\n     * a chart on the client side, and [fallbackToExportServer](\n     * #exporting.fallbackToExportServer) is disabled. If left undefined, an\n     * exception is thrown instead. Receives two parameters, the exporting\n     * options, and the error from the module.\n     *\n     * @see [fallbackToExportServer](#exporting.fallbackToExportServer)\n     *\n     * @type      {Highcharts.ExportingErrorCallbackFunction}\n     * @since     5.0.0\n     * @requires  modules/exporting\n     * @requires  modules/offline-exporting\n     * @apioption exporting.error\n     */\n    /**\n     * Whether or not to fall back to the export server if the offline-exporting\n     * module is unable to export the chart on the client side. This happens for\n     * certain browsers, and certain features (e.g.\n     * [allowHTML](#exporting.allowHTML)), depending on the image type exporting\n     * to. For very complex charts, it is possible that export can fail in\n     * browsers that don't support Blob objects, due to data URL length limits.\n     * It is recommended to define the [exporting.error](#exporting.error)\n     * handler if disabling fallback, in order to notify users in case export\n     * fails.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @since     4.1.8\n     * @requires  modules/exporting\n     * @requires  modules/offline-exporting\n     * @apioption exporting.fallbackToExportServer\n     */\n    /**\n     * The filename, without extension, to use for the exported chart.\n     *\n     * @sample {highcharts} highcharts/exporting/filename/\n     *         Custom file name\n     * @sample {highstock} highcharts/exporting/filename/\n     *         Custom file name\n     *\n     * @type      {string}\n     * @default   chart\n     * @since     2.0\n     * @apioption exporting.filename\n     */\n    /**\n     * Highcharts v11.2.0 and older. An object containing additional key value\n     * data for the POST form that sends the SVG to the export server. For\n     * example, a `target` can be set to make sure the generated image is\n     * received in another frame, or a custom `enctype` or `encoding` can be\n     * set.\n     *\n     * With Highcharts v11.3.0, the `fetch` API replaced the old HTML form. To\n     * modify the request, now use [fetchOptions](#exporting.fetchOptions)\n     * instead.\n     *\n     * @deprecated\n     * @type      {Highcharts.HTMLAttributes}\n     * @since     3.0.8\n     * @apioption exporting.formAttributes\n     */\n    /**\n     * Options for the fetch request used when sending the SVG to the export\n     * server.\n     *\n     * See [MDN](https://developer.mozilla.org/en-US/docs/Web/API/fetch)\n     * for more information\n     *\n     * @type      {Object}\n     * @since     11.3.0\n     * @apioption exporting.fetchOptions\n     */\n    /**\n     * Path where Highcharts will look for export module dependencies to\n     * load on demand if they don't already exist on `window`. Should currently\n     * point to location of [CanVG](https://github.com/canvg/canvg) library,\n     * [jsPDF](https://github.com/parallax/jsPDF) and\n     * [svg2pdf.js](https://github.com/yWorks/svg2pdf.js), required for client\n     * side export in certain browsers.\n     *\n     * @type      {string}\n     * @default   https://code.highcharts.com/{version}/lib\n     * @since     5.0.0\n     * @apioption exporting.libURL\n     */\n    libURL: 'https://code.highcharts.com/12.3.0/lib/',\n    /**\n     * Whether the chart should be exported using the browser's built-in\n     * capabilities, allowing offline exports without requiring access to the\n     * Highcharts export server, or sent directly to the export server for\n     * processing and downloading.\n     *\n     * This option is different from `exporting.fallbackToExportServer`, which\n     * controls whether the export server should be used as a fallback only if\n     * the local export fails. In contrast, `exporting.local` explicitly defines\n     * which export method to use.\n     *\n     * @see [fallbackToExportServer](#exporting.fallbackToExportServer)\n     *\n     * @type      {boolean}\n     * @default   true\n     * @since 12.3.0\n     * @requires  modules/exporting\n     * @apioption exporting.local\n     */\n    local: true,\n    /**\n     * Analogous to [sourceWidth](#exporting.sourceWidth).\n     *\n     * @type      {number}\n     * @since     3.0\n     * @apioption exporting.sourceHeight\n     */\n    /**\n     * The width of the original chart when exported, unless an explicit\n     * [chart.width](#chart.width) is set, or a pixel width is set on the\n     * container. The width exported raster image is then multiplied by\n     * [scale](#exporting.scale).\n     *\n     * @sample {highcharts} highcharts/exporting/sourcewidth/\n     *         Source size demo\n     * @sample {highstock} highcharts/exporting/sourcewidth/\n     *         Source size demo\n     * @sample {highmaps} maps/exporting/sourcewidth/\n     *         Source size demo\n     *\n     * @type      {number}\n     * @since     3.0\n     * @apioption exporting.sourceWidth\n     */\n    /**\n     * The pixel width of charts exported to PNG or JPG. As of Highcharts\n     * 3.0, the default pixel width is a function of the [chart.width](\n     * #chart.width) or [exporting.sourceWidth](#exporting.sourceWidth) and the\n     * [exporting.scale](#exporting.scale).\n     *\n     * @sample {highcharts} highcharts/exporting/width/\n     *         Export to 200px wide images\n     * @sample {highstock} highcharts/exporting/width/\n     *         Export to 200px wide images\n     *\n     * @type      {number}\n     * @since     2.0\n     * @apioption exporting.width\n     */\n    /**\n     * Default MIME type for exporting if `chart.exportChart()` is called\n     * without specifying a `type` option. Possible values are `image/png`,\n     *  `image/jpeg`, `application/pdf` and `image/svg+xml`.\n     *\n     * @type  {Highcharts.ExportingMimeTypeValue}\n     * @since 2.0\n     */\n    type: 'image/png',\n    /**\n     * The URL for the server module converting the SVG string to an image\n     * format. By default this points to Highchart's free web service.\n     *\n     * @since 2.0\n     */\n    url: `https://export-svg.highcharts.com?v=${(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).version}`,\n    /**\n     * Settings for a custom font for the exported PDF, when using the\n     * `offline-exporting` module. This is used for languages containing\n     * non-ASCII characters, like Chinese, Russian, Japanese etc.\n     *\n     * As described in the [jsPDF\n     * docs](https://github.com/parallax/jsPDF#use-of-unicode-characters--utf-8),\n     * the 14 standard fonts in PDF are limited to the ASCII-codepage.\n     * Therefore, in order to support other text in the exported PDF, one or\n     * more TTF font files have to be passed on to the exporting module.\n     *\n     * See more in [the\n     * docs](https://www.highcharts.com/docs/export-module/client-side-export).\n     *\n     * @sample {highcharts} highcharts/exporting/offline-download-pdffont/\n     *         Download PDF in a language containing non-Latin characters.\n     *\n     * @since    10.0.0\n     * @requires modules/offline-exporting\n     */\n    pdfFont: {\n        /**\n         * The TTF font file for normal `font-style`. If font variations like\n         * `bold` or `italic` are not defined, the `normal` font will be used\n         * for those too.\n         *\n         * @type string | undefined\n         */\n        normal: void 0,\n        /**\n         * The TTF font file for bold text.\n         *\n         * @type string | undefined\n         */\n        bold: void 0,\n        /**\n         * The TTF font file for bold and italic text.\n         *\n         * @type string | undefined\n         */\n        bolditalic: void 0,\n        /**\n         * The TTF font file for italic text.\n         *\n         * @type string | undefined\n         */\n        italic: void 0\n    },\n    /**\n     * When printing the chart from the menu item in the burger menu, if\n     * the on-screen chart exceeds this width, it is resized. After printing\n     * or cancelled, it is restored. The default width makes the chart\n     * fit into typical paper format. Note that this does not affect the\n     * chart when printing the web page as a whole.\n     *\n     * @since 4.2.5\n     */\n    printMaxWidth: 780,\n    /**\n     * Defines the scale or zoom factor for the exported image compared\n     * to the on-screen display. While for instance a 600px wide chart\n     * may look good on a website, it will look bad in print. The default\n     * scale of 2 makes this chart export to a 1200px PNG or JPG.\n     *\n     * @see [chart.width](#chart.width)\n     * @see [exporting.sourceWidth](#exporting.sourceWidth)\n     *\n     * @sample {highcharts} highcharts/exporting/scale/\n     *         Scale demonstrated\n     * @sample {highstock} highcharts/exporting/scale/\n     *         Scale demonstrated\n     * @sample {highmaps} maps/exporting/scale/\n     *         Scale demonstrated\n     *\n     * @since 3.0\n     */\n    scale: 2,\n    /**\n     * Options for the export related buttons, print and export. In addition\n     * to the default buttons listed here, custom buttons can be added.\n     * See [navigation.buttonOptions](#navigation.buttonOptions) for general\n     * options.\n     *\n     * @type     {Highcharts.Dictionary<*>}\n     * @requires modules/exporting\n     */\n    buttons: {\n        /**\n         * Options for the export button.\n         *\n         * In styled mode, export button styles can be applied with the\n         * `.highcharts-contextbutton` class.\n         *\n         * @declare  Highcharts.ExportingButtonsOptionsObject\n         * @extends  navigation.buttonOptions\n         * @requires modules/exporting\n         */\n        contextButton: {\n            /**\n             * A click handler callback to use on the button directly instead of\n             * the popup menu.\n             *\n             * @sample highcharts/exporting/buttons-contextbutton-onclick/\n             *         Skip the menu and export the chart directly\n             *\n             * @type      {Function}\n             * @since     2.0\n             * @apioption exporting.buttons.contextButton.onclick\n             */\n            /**\n             * See [navigation.buttonOptions.symbolFill](\n             * #navigation.buttonOptions.symbolFill).\n             *\n             * @type      {Highcharts.ColorString}\n             * @default   #666666\n             * @since     2.0\n             * @apioption exporting.buttons.contextButton.symbolFill\n             */\n            /**\n             * The horizontal position of the button relative to the `align`\n             * option.\n             *\n             * @type      {number}\n             * @default   -10\n             * @since     2.0\n             * @apioption exporting.buttons.contextButton.x\n             */\n            /**\n             * The class name of the context button.\n             */\n            className: 'highcharts-contextbutton',\n            /**\n             * The class name of the menu appearing from the button.\n             */\n            menuClassName: 'highcharts-contextmenu',\n            /**\n             * The symbol for the button. Points to a definition function in\n             * the `Highcharts.Renderer.symbols` collection. The default\n             * `menu` function is part of the exporting module. Possible\n             * values are \"circle\", \"square\", \"diamond\", \"triangle\",\n             * \"triangle-down\", \"menu\", \"menuball\" or custom shape.\n             *\n             * @sample highcharts/exporting/buttons-contextbutton-symbol/\n             *         Use a circle for symbol\n             * @sample highcharts/exporting/buttons-contextbutton-symbol-custom/\n             *         Custom shape as symbol\n             *\n             * @type  {Highcharts.SymbolKeyValue | \"menu\" | \"menuball\" | string}\n             * @since 2.0\n             */\n            symbol: 'menu',\n            /**\n             * The key to a [lang](#lang) option setting that is used for the\n             * button's title tooltip. When the key is `contextButtonTitle`, it\n             * refers to [lang.contextButtonTitle](#lang.contextButtonTitle)\n             * that defaults to \"Chart context menu\".\n             *\n             * @since 6.1.4\n             */\n            titleKey: 'contextButtonTitle',\n            /**\n             * A collection of strings pointing to config options for the menu\n             * items. The config options are defined in the\n             * `menuItemDefinitions` option.\n             *\n             * By default, there is the \"View in full screen\" and \"Print\" menu\n             * items, plus one menu item for each of the available export types.\n             *\n             * @sample highcharts/exporting/menuitemdefinitions/\n             *         Menu item definitions\n             * @sample highcharts/exporting/menuitemdefinitions-webp/\n             *         Adding a custom menu item for WebP export\n             *\n             * @type    {Array<string>}\n             * @default [\"viewFullscreen\", \"printChart\", \"separator\", \"downloadPNG\", \"downloadJPEG\", \"downloadSVG\"]\n             * @since   2.0\n             */\n            menuItems: [\n                'viewFullscreen',\n                'printChart',\n                'separator',\n                'downloadPNG',\n                'downloadJPEG',\n                'downloadSVG'\n            ]\n        }\n    },\n    /**\n     * An object consisting of definitions for the menu items in the context\n     * menu. Each key value pair has a `key` that is referenced in the\n     * [menuItems](#exporting.buttons.contextButton.menuItems) setting,\n     * and a `value`, which is an object with the following properties:\n     *\n     * - **onclick:** The click handler for the menu item\n     *\n     * - **text:** The text for the menu item\n     *\n     * - **textKey:** If internationalization is required, the key to a language\n     *   string\n     *\n     * Custom text for the \"exitFullScreen\" can be set only in lang options\n     * (it is not a separate button).\n     *\n     * @sample highcharts/exporting/menuitemdefinitions/\n     *         Menu item definitions\n     * @sample highcharts/exporting/menuitemdefinitions-webp/\n     *         Adding a custom menu item for WebP export\n     *\n     *\n     * @type    {Highcharts.Dictionary<Highcharts.ExportingMenuObject>}\n     * @default {\"viewFullscreen\": {}, \"printChart\": {}, \"separator\": {}, \"downloadPNG\": {}, \"downloadJPEG\": {}, \"downloadPDF\": {}, \"downloadSVG\": {}}\n     * @since   5.0.13\n     */\n    menuItemDefinitions: {\n        /**\n         * @ignore\n         */\n        viewFullscreen: {\n            textKey: 'viewFullscreen',\n            onclick: function () {\n                this.fullscreen?.toggle();\n            }\n        },\n        /**\n         * @ignore\n         */\n        printChart: {\n            textKey: 'printChart',\n            onclick: function () {\n                this.exporting?.print();\n            }\n        },\n        /**\n         * @ignore\n         */\n        separator: {\n            separator: true\n        },\n        /**\n         * @ignore\n         */\n        downloadPNG: {\n            textKey: 'downloadPNG',\n            onclick: async function () {\n                await this.exporting?.exportChart();\n            }\n        },\n        /**\n         * @ignore\n         */\n        downloadJPEG: {\n            textKey: 'downloadJPEG',\n            onclick: async function () {\n                await this.exporting?.exportChart({\n                    type: 'image/jpeg'\n                });\n            }\n        },\n        /**\n         * @ignore\n         */\n        downloadPDF: {\n            textKey: 'downloadPDF',\n            onclick: async function () {\n                await this.exporting?.exportChart({\n                    type: 'application/pdf'\n                });\n            }\n        },\n        /**\n         * @ignore\n         */\n        downloadSVG: {\n            textKey: 'downloadSVG',\n            onclick: async function () {\n                await this.exporting?.exportChart({\n                    type: 'image/svg+xml'\n                });\n            }\n        }\n    }\n};\n// Add language\n/**\n * @optionparent lang\n */\nconst lang = {\n    /**\n     * Exporting module only. The text for the menu item to view the chart\n     * in full screen.\n     *\n     * @since 8.0.1\n     */\n    viewFullscreen: 'View in full screen',\n    /**\n     * Exporting module only. The text for the menu item to exit the chart\n     * from full screen.\n     *\n     * @since 8.0.1\n     */\n    exitFullscreen: 'Exit from full screen',\n    /**\n     * Exporting module only. The text for the menu item to print the chart.\n     *\n     * @since    3.0.1\n     * @requires modules/exporting\n     */\n    printChart: 'Print chart',\n    /**\n     * Exporting module only. The text for the PNG download menu item.\n     *\n     * @since    2.0\n     * @requires modules/exporting\n     */\n    downloadPNG: 'Download PNG image',\n    /**\n     * Exporting module only. The text for the JPEG download menu item.\n     *\n     * @since    2.0\n     * @requires modules/exporting\n     */\n    downloadJPEG: 'Download JPEG image',\n    /**\n     * Exporting module only. The text for the PDF download menu item.\n     *\n     * @since    2.0\n     * @requires modules/exporting\n     */\n    downloadPDF: 'Download PDF document',\n    /**\n     * Exporting module only. The text for the SVG download menu item.\n     *\n     * @since    2.0\n     * @requires modules/exporting\n     */\n    downloadSVG: 'Download SVG vector image',\n    /**\n     * Exporting module menu. The tooltip title for the context menu holding\n     * print and export menu items.\n     *\n     * @since    3.0\n     * @requires modules/exporting\n     */\n    contextButtonTitle: 'Chart context menu'\n};\n/**\n * A collection of options for buttons and menus appearing in the exporting\n * module or in Stock Tools.\n *\n * @requires     modules/exporting\n * @optionparent navigation\n */\nconst navigation = {\n    /**\n     * A collection of options for buttons appearing in the exporting\n     * module.\n     *\n     * In styled mode, the buttons are styled with the\n     * `.highcharts-contextbutton` and `.highcharts-button-symbol` classes.\n     *\n     * @requires modules/exporting\n     */\n    buttonOptions: {\n        /**\n         * Whether to enable buttons.\n         *\n         * @sample highcharts/navigation/buttonoptions-enabled/\n         *         Exporting module loaded but buttons disabled\n         *\n         * @type      {boolean}\n         * @default   true\n         * @since     2.0\n         * @apioption navigation.buttonOptions.enabled\n         */\n        /**\n         * The pixel size of the symbol on the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        symbolSize: 14,\n        /**\n         * The x position of the center of the symbol inside the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        symbolX: 14.5,\n        /**\n         * The y position of the center of the symbol inside the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        symbolY: 13.5,\n        /**\n         * Alignment for the buttons.\n         *\n         * @sample highcharts/navigation/buttonoptions-align/\n         *         Center aligned\n         *\n         * @type  {Highcharts.AlignValue}\n         * @since 2.0\n         */\n        align: 'right',\n        /**\n         * The pixel spacing between buttons, and between the context button and\n         * the title.\n         *\n         * @sample highcharts/title/widthadjust\n         *         Adjust the spacing when using text button\n         *\n         * @since 2.0\n         */\n        buttonSpacing: 5,\n        /**\n         * Pixel height of the buttons.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        height: 28,\n        /**\n         * A text string to add to the individual button.\n         *\n         * @sample highcharts/exporting/buttons-text/\n         *         Full text button\n         * @sample highcharts/exporting/buttons-text-usehtml/\n         *         Icon using CSS font in text\n         * @sample highcharts/exporting/buttons-text-symbol/\n         *         Combined symbol and text\n         *\n         * @type      {string}\n         * @default   null\n         * @since     3.0\n         * @apioption navigation.buttonOptions.text\n         */\n        /**\n         * Whether to use HTML for rendering the button. HTML allows for things\n         * like inline CSS or image-based icons.\n         *\n         * @sample highcharts/exporting/buttons-text-usehtml/\n         *         Icon using CSS font in text\n         *\n         * @type      boolean\n         * @default   false\n         * @since     10.3.0\n         * @apioption navigation.buttonOptions.useHTML\n         */\n        /**\n         * The vertical offset of the button's position relative to its\n         * `verticalAlign`. By default adjusted for the chart title alignment.\n         *\n         * @sample highcharts/navigation/buttonoptions-verticalalign/\n         *         Buttons at lower right\n         *\n         * @since     2.0\n         * @apioption navigation.buttonOptions.y\n         */\n        y: -5,\n        /**\n         * The vertical alignment of the buttons. Can be one of `\"top\"`,\n         * `\"middle\"` or `\"bottom\"`.\n         *\n         * @sample highcharts/navigation/buttonoptions-verticalalign/\n         *         Buttons at lower right\n         *\n         * @type  {Highcharts.VerticalAlignValue}\n         * @since 2.0\n         */\n        verticalAlign: 'top',\n        /**\n         * The pixel width of the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        width: 28,\n        /**\n         * Fill color for the symbol within the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-symbolfill/\n         *         Blue symbol stroke for one of the buttons\n         *\n         * @type  {Highcharts.ColorString | Highcharts.GradientColorObject | Highcharts.PatternObject}\n         * @since 2.0\n         */\n        symbolFill: \"#666666\" /* Palette.neutralColor60 */,\n        /**\n         * The color of the symbol's stroke or line.\n         *\n         * @sample highcharts/navigation/buttonoptions-symbolstroke/\n         *         Blue symbol stroke\n         *\n         * @type  {Highcharts.ColorString}\n         * @since 2.0\n         */\n        symbolStroke: \"#666666\" /* Palette.neutralColor60 */,\n        /**\n         * The pixel stroke width of the symbol on the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        symbolStrokeWidth: 3,\n        /**\n         * A configuration object for the button theme. The object accepts\n         * SVG properties like `stroke-width`, `stroke` and `fill`.\n         * Tri-state button styles are supported by the `states.hover` and\n         * `states.select` objects.\n         *\n         * @sample highcharts/navigation/buttonoptions-theme/\n         *         Theming the buttons\n         *\n         * @requires modules/exporting\n         *\n         * @since 3.0\n         */\n        theme: {\n            /**\n             * The default fill exists only to capture hover events.\n             *\n             * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n             */\n            fill: \"#ffffff\" /* Palette.backgroundColor */,\n            /**\n             * Padding for the button.\n             */\n            padding: 5,\n            /**\n             * Default stroke for the buttons.\n             *\n             * @type {Highcharts.ColorString}\n             */\n            stroke: 'none',\n            /**\n             * Default stroke linecap for the buttons.\n             */\n            'stroke-linecap': 'round'\n        }\n    },\n    /**\n     * CSS styles for the popup menu appearing by default when the export\n     * icon is clicked. This menu is rendered in HTML.\n     *\n     * @see In styled mode, the menu is styled with the `.highcharts-menu`\n     *      class.\n     *\n     * @sample highcharts/navigation/menustyle/\n     *         Light gray menu background\n     *\n     * @type    {Highcharts.CSSObject}\n     * @default {\"background\": \"#ffffff\", \"borderRadius\": \"3px\", \"padding\": \"0.5em\"}\n     * @since   2.0\n     */\n    menuStyle: {\n        /** @ignore-option */\n        border: 'none',\n        /** @ignore-option */\n        borderRadius: '3px',\n        /** @ignore-option */\n        background: \"#ffffff\" /* Palette.backgroundColor */,\n        /** @ignore-option */\n        padding: '0.5em'\n    },\n    /**\n     * CSS styles for the individual items within the popup menu appearing\n     * by default when the export icon is clicked. The menu items are\n     * rendered in HTML. Font size defaults to `11px` on desktop and `14px`\n     * on touch devices.\n     *\n     * @see In styled mode, the menu items are styled with the\n     *      `.highcharts-menu-item` class.\n     *\n     * @sample {highcharts} highcharts/navigation/menuitemstyle/\n     *         Add a grey stripe to the left\n     *\n     * @type    {Highcharts.CSSObject}\n     * @default {\"padding\": \"0.5em\", \"color\": \"#333333\", \"background\": \"none\", \"borderRadius\": \"3px\", \"fontSize\": \"0.8em\", \"transition\": \"background 250ms, color 250ms\"}\n     * @since   2.0\n     */\n    menuItemStyle: {\n        /** @ignore-option */\n        background: 'none',\n        /** @ignore-option */\n        borderRadius: '3px',\n        /** @ignore-option */\n        color: \"#333333\" /* Palette.neutralColor80 */,\n        /** @ignore-option */\n        padding: '0.5em',\n        /** @ignore-option */\n        fontSize: isTouchDevice ? '0.9em' : '0.8em',\n        /** @ignore-option */\n        transition: 'background 250ms, color 250ms'\n    },\n    /**\n     * CSS styles for the hover state of the individual items within the\n     * popup menu appearing by default when the export icon is clicked. The\n     * menu items are rendered in HTML.\n     *\n     * @see In styled mode, the menu items are styled with the\n     *      `.highcharts-menu-item` class.\n     *\n     * @sample highcharts/navigation/menuitemhoverstyle/\n     *         Bold text on hover\n     *\n     * @type    {Highcharts.CSSObject}\n     * @default {\"background\": \"#f2f2f2\" }\n     * @since   2.0\n     */\n    menuItemHoverStyle: {\n        /** @ignore-option */\n        background: \"#f2f2f2\" /* Palette.neutralColor5 */\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\nconst ExportingDefaults = {\n    exporting,\n    lang,\n    navigation\n};\n/* harmony default export */ const Exporting_ExportingDefaults = (ExportingDefaults);\n\n;// ./code/es-modules/Extensions/Exporting/ExportingSymbols.js\n/* *\n *\n *  Exporting module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Composition\n *\n * */\nvar ExportingSymbols;\n(function (ExportingSymbols) {\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    const modifiedClasses = [];\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * @private\n     */\n    function compose(SVGRendererClass) {\n        if (modifiedClasses.indexOf(SVGRendererClass) === -1) {\n            modifiedClasses.push(SVGRendererClass);\n            const symbols = SVGRendererClass.prototype.symbols;\n            symbols.menu = menu;\n            symbols.menuball = menuball.bind(symbols);\n        }\n    }\n    ExportingSymbols.compose = compose;\n    /**\n     * @private\n     */\n    function menu(x, y, width, height) {\n        const arr = [\n            ['M', x, y + 2.5],\n            ['L', x + width, y + 2.5],\n            ['M', x, y + height / 2 + 0.5],\n            ['L', x + width, y + height / 2 + 0.5],\n            ['M', x, y + height - 1.5],\n            ['L', x + width, y + height - 1.5]\n        ];\n        return arr;\n    }\n    /**\n     * @private\n     */\n    function menuball(x, y, width, height) {\n        const h = (height / 3) - 2;\n        let path = [];\n        path = path.concat(this.circle(width - h, y, h, h), this.circle(width - h, y + h + 4, h, h), this.circle(width - h, y + 2 * (h + 4), h, h));\n        return path;\n    }\n})(ExportingSymbols || (ExportingSymbols = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Exporting_ExportingSymbols = (ExportingSymbols);\n\n;// ./code/es-modules/Extensions/Exporting/Fullscreen.js\n/* *\n *\n *  (c) 2009-2025 Rafal Sebestjanski\n *\n *  Full screen for Highcharts\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n/**\n * The module allows user to enable display chart in full screen mode.\n * Used in StockTools too.\n * Based on default solutions in browsers.\n */\n\n\n\nconst { composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent, fireEvent, pushUnique } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction onChartBeforeRender() {\n    /**\n     * @name Highcharts.Chart#fullscreen\n     * @type {Highcharts.Fullscreen}\n     * @requires modules/full-screen\n     */\n    this.fullscreen = new Fullscreen(this);\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * Handles displaying chart's container in the fullscreen mode.\n *\n * **Note**: Fullscreen is not supported on iPhone due to iOS limitations.\n *\n * @class\n * @name Highcharts.Fullscreen\n *\n * @requires modules/exporting\n */\nclass Fullscreen {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Prepares the chart class to support fullscreen.\n     *\n     * @param {typeof_Highcharts.Chart} ChartClass\n     * The chart class to decorate with fullscreen support.\n     */\n    static compose(ChartClass) {\n        if (pushUnique(composed, 'Fullscreen')) {\n            // Initialize fullscreen\n            addEvent(ChartClass, 'beforeRender', onChartBeforeRender);\n        }\n    }\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    constructor(chart) {\n        /**\n         * Chart managed by the fullscreen controller.\n         * @name Highcharts.Fullscreen#chart\n         * @type {Highcharts.Chart}\n         */\n        this.chart = chart;\n        /**\n         * The flag is set to `true` when the chart is displayed in\n         * the fullscreen mode.\n         *\n         * @name Highcharts.Fullscreen#isOpen\n         * @type {boolean | undefined}\n         * @since 8.0.1\n         */\n        this.isOpen = false;\n        const container = chart.renderTo;\n        // Hold event and methods available only for a current browser.\n        if (!this.browserProps) {\n            if (typeof container.requestFullscreen === 'function') {\n                this.browserProps = {\n                    fullscreenChange: 'fullscreenchange',\n                    requestFullscreen: 'requestFullscreen',\n                    exitFullscreen: 'exitFullscreen'\n                };\n            }\n            else if (container.mozRequestFullScreen) {\n                this.browserProps = {\n                    fullscreenChange: 'mozfullscreenchange',\n                    requestFullscreen: 'mozRequestFullScreen',\n                    exitFullscreen: 'mozCancelFullScreen'\n                };\n            }\n            else if (container.webkitRequestFullScreen) {\n                this.browserProps = {\n                    fullscreenChange: 'webkitfullscreenchange',\n                    requestFullscreen: 'webkitRequestFullScreen',\n                    exitFullscreen: 'webkitExitFullscreen'\n                };\n            }\n            else if (container.msRequestFullscreen) {\n                this.browserProps = {\n                    fullscreenChange: 'MSFullscreenChange',\n                    requestFullscreen: 'msRequestFullscreen',\n                    exitFullscreen: 'msExitFullscreen'\n                };\n            }\n        }\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Stops displaying the chart in fullscreen mode.\n     * Exporting module required.\n     *\n     * @since       8.0.1\n     *\n     * @function    Highcharts.Fullscreen#close\n     * @return      {void}\n     * @requires    modules/full-screen\n     */\n    close() {\n        const fullscreen = this, chart = fullscreen.chart, optionsChart = chart.options.chart;\n        fireEvent(chart, 'fullscreenClose', null, function () {\n            // Don't fire exitFullscreen() when user exited\n            // using 'Escape' button.\n            if (fullscreen.isOpen &&\n                fullscreen.browserProps &&\n                chart.container.ownerDocument instanceof Document) {\n                chart.container.ownerDocument[fullscreen.browserProps.exitFullscreen]();\n            }\n            // Unbind event as it's necessary only before exiting\n            // from fullscreen.\n            if (fullscreen.unbindFullscreenEvent) {\n                fullscreen.unbindFullscreenEvent = fullscreen\n                    .unbindFullscreenEvent();\n            }\n            chart.setSize(fullscreen.origWidth, fullscreen.origHeight, false);\n            fullscreen.origWidth = void 0;\n            fullscreen.origHeight = void 0;\n            optionsChart.width = fullscreen.origWidthOption;\n            optionsChart.height = fullscreen.origHeightOption;\n            fullscreen.origWidthOption = void 0;\n            fullscreen.origHeightOption = void 0;\n            fullscreen.isOpen = false;\n            fullscreen.setButtonText();\n        });\n    }\n    /**\n     * Displays the chart in fullscreen mode.\n     * When fired customly by user before exporting context button is created,\n     * button's text will not be replaced - it's on the user side.\n     * Exporting module required.\n     *\n     * @since       8.0.1\n     *\n     * @function Highcharts.Fullscreen#open\n     * @return      {void}\n     * @requires    modules/full-screen\n     */\n    open() {\n        const fullscreen = this, chart = fullscreen.chart, optionsChart = chart.options.chart;\n        fireEvent(chart, 'fullscreenOpen', null, function () {\n            if (optionsChart) {\n                fullscreen.origWidthOption = optionsChart.width;\n                fullscreen.origHeightOption = optionsChart.height;\n            }\n            fullscreen.origWidth = chart.chartWidth;\n            fullscreen.origHeight = chart.chartHeight;\n            // Handle exitFullscreen() method when user clicks 'Escape' button.\n            if (fullscreen.browserProps) {\n                const unbindChange = addEvent(chart.container.ownerDocument, // Chart's document\n                fullscreen.browserProps.fullscreenChange, function () {\n                    // Handle lack of async of browser's\n                    // fullScreenChange event.\n                    if (fullscreen.isOpen) {\n                        fullscreen.isOpen = false;\n                        fullscreen.close();\n                    }\n                    else {\n                        chart.setSize(null, null, false);\n                        fullscreen.isOpen = true;\n                        fullscreen.setButtonText();\n                    }\n                });\n                const unbindDestroy = addEvent(chart, 'destroy', unbindChange);\n                fullscreen.unbindFullscreenEvent = () => {\n                    unbindChange();\n                    unbindDestroy();\n                };\n                const promise = chart.renderTo[fullscreen.browserProps.requestFullscreen]();\n                if (promise) {\n                    promise['catch'](function () {\n                        alert(// eslint-disable-line no-alert\n                        'Full screen is not supported inside a frame.');\n                    });\n                }\n            }\n        });\n    }\n    /**\n     * Replaces the exporting context button's text when toogling the\n     * fullscreen mode.\n     *\n     * @private\n     *\n     * @since 8.0.1\n     *\n     * @requires modules/full-screen\n     */\n    setButtonText() {\n        const chart = this.chart, exportDivElements = chart.exporting?.divElements, exportingOptions = chart.options.exporting, menuItems = (exportingOptions &&\n            exportingOptions.buttons &&\n            exportingOptions.buttons.contextButton.menuItems), lang = chart.options.lang;\n        if (exportingOptions &&\n            exportingOptions.menuItemDefinitions &&\n            lang &&\n            lang.exitFullscreen &&\n            lang.viewFullscreen &&\n            menuItems &&\n            exportDivElements) {\n            const exportDivElement = exportDivElements[menuItems.indexOf('viewFullscreen')];\n            if (exportDivElement) {\n                highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default().setElementHTML(exportDivElement, !this.isOpen ?\n                    (exportingOptions.menuItemDefinitions.viewFullscreen\n                        .text ||\n                        lang.viewFullscreen) : lang.exitFullscreen);\n            }\n        }\n    }\n    /**\n     * Toggles displaying the chart in fullscreen mode.\n     * By default, when the exporting module is enabled, a context button with\n     * a drop down menu in the upper right corner accesses this function.\n     * Exporting module required.\n     *\n     * @since 8.0.1\n     *\n     * @sample      highcharts/members/chart-togglefullscreen/\n     *              Toggle fullscreen mode from a HTML button\n     *\n     * @function Highcharts.Fullscreen#toggle\n     * @requires    modules/full-screen\n     */\n    toggle() {\n        const fullscreen = this;\n        if (!fullscreen.isOpen) {\n            fullscreen.open();\n        }\n        else {\n            fullscreen.close();\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Exporting_Fullscreen = (Fullscreen);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Gets fired when closing the fullscreen\n *\n * @callback Highcharts.FullScreenfullscreenCloseCallbackFunction\n *\n * @param {Highcharts.Chart} chart\n *        The chart on which the event occurred.\n *\n * @param {global.Event} event\n *        The event that occurred.\n */\n/**\n * Gets fired when opening the fullscreen\n *\n * @callback Highcharts.FullScreenfullscreenOpenCallbackFunction\n *\n * @param {Highcharts.Chart} chart\n *        The chart on which the event occurred.\n *\n * @param {global.Event} event\n *        The event that occurred.\n */\n(''); // Keeps doclets above separated from following code\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Fires when a fullscreen is closed through the context menu item,\n * or a fullscreen is closed on the `Escape` button click,\n * or the `Chart.fullscreen.close` method.\n *\n * @sample highcharts/chart/events-fullscreen\n *         Title size change on fullscreen open\n *\n * @type      {Highcharts.FullScreenfullscreenCloseCallbackFunction}\n * @since     10.1.0\n * @context   Highcharts.Chart\n * @requires  modules/full-screen\n * @apioption chart.events.fullscreenClose\n */\n/**\n * Fires when a fullscreen is opened through the context menu item,\n * or the `Chart.fullscreen.open` method.\n *\n * @sample highcharts/chart/events-fullscreen\n *         Title size change on fullscreen open\n *\n * @type      {Highcharts.FullScreenfullscreenOpenCallbackFunction}\n * @since     10.1.0\n * @context   Highcharts.Chart\n * @requires  modules/full-screen\n * @apioption chart.events.fullscreenOpen\n */\n(''); // Keeps doclets above in transpiled file\n\n;// ./code/es-modules/Core/HttpUtilities.js\n/* *\n *\n *  (c) 2010-2025 Christer Vasseng, Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { win: HttpUtilities_win } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { discardElement, objectEach } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Perform an Ajax call.\n *\n * @function Highcharts.ajax\n *\n * @param {Highcharts.AjaxSettingsObject} settings\n * The Ajax settings to use.\n *\n * @return {false | undefined}\n * Returns false, if error occurred.\n */\nfunction ajax(settings) {\n    const headers = {\n        json: 'application/json',\n        xml: 'application/xml',\n        text: 'text/plain',\n        octet: 'application/octet-stream'\n    }, r = new XMLHttpRequest();\n    /**\n     * Private error handler.\n     *\n     * @private\n     *\n     * @param {XMLHttpRequest} xhr\n     * Internal request object.\n     * @param {string | Error} err\n     * Occurred error.\n     */\n    function handleError(xhr, err) {\n        if (settings.error) {\n            settings.error(xhr, err);\n        }\n        else {\n            // @todo Maybe emit a highcharts error event here\n        }\n    }\n    if (!settings.url) {\n        return false;\n    }\n    r.open((settings.type || 'get').toUpperCase(), settings.url, true);\n    if (!settings.headers?.['Content-Type']) {\n        r.setRequestHeader('Content-Type', headers[settings.dataType || 'json'] || headers.text);\n    }\n    objectEach(settings.headers, function (val, key) {\n        r.setRequestHeader(key, val);\n    });\n    if (settings.responseType) {\n        r.responseType = settings.responseType;\n    }\n    // @todo lacking timeout handling\n    r.onreadystatechange = function () {\n        let res;\n        if (r.readyState === 4) {\n            if (r.status === 200) {\n                if (settings.responseType !== 'blob') {\n                    res = r.responseText;\n                    if (settings.dataType === 'json') {\n                        try {\n                            res = JSON.parse(res);\n                        }\n                        catch (e) {\n                            if (e instanceof Error) {\n                                return handleError(r, e);\n                            }\n                        }\n                    }\n                }\n                return settings.success?.(res, r);\n            }\n            handleError(r, r.responseText);\n        }\n    };\n    if (settings.data && typeof settings.data !== 'string') {\n        settings.data = JSON.stringify(settings.data);\n    }\n    r.send(settings.data);\n}\n/**\n * Get a JSON resource over XHR, also supporting CORS without preflight.\n *\n * @function Highcharts.getJSON\n *\n * @param {string} url\n * The URL to load.\n * @param {Function} success\n * The success callback. For error handling, use the `Highcharts.ajax` function\n * instead.\n */\nfunction getJSON(url, success) {\n    HttpUtilities.ajax({\n        url: url,\n        success: success,\n        dataType: 'json',\n        headers: {\n            // Override the Content-Type to avoid preflight problems with CORS\n            // in the Highcharts demos\n            'Content-Type': 'text/plain'\n        }\n    });\n}\n/**\n * The post utility.\n *\n * @private\n * @function Highcharts.post\n *\n * @param {string} url\n * Post URL.\n * @param {Object} data\n * Post data.\n * @param {RequestInit} [fetchOptions]\n * Additional attributes for the post request.\n */\nasync function post(url, data, fetchOptions) {\n    // Prepare a form to send the data\n    const formData = new HttpUtilities_win.FormData();\n    // Add the data to the form\n    objectEach(data, function (value, name) {\n        formData.append(name, value);\n    });\n    formData.append('b64', 'true');\n    // Send the POST\n    const response = await HttpUtilities_win.fetch(url, {\n        method: 'POST',\n        body: formData,\n        ...fetchOptions\n    });\n    // Check the response\n    if (response.ok) {\n        // Get the text from the response\n        const text = await response.text();\n        // Prepare self-click link with the Base64 representation\n        const link = document.createElement('a');\n        link.href = `data:${data.type};base64,${text}`;\n        link.download = data.filename;\n        link.click();\n        // Remove the link\n        discardElement(link);\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst HttpUtilities = {\n    ajax,\n    getJSON,\n    post\n};\n/* harmony default export */ const Core_HttpUtilities = (HttpUtilities);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * @interface Highcharts.AjaxSettingsObject\n */ /**\n* The payload to send.\n*\n* @name Highcharts.AjaxSettingsObject#data\n* @type {string | Highcharts.Dictionary<any> | undefined}\n*/ /**\n* The data type expected.\n*\n* @name Highcharts.AjaxSettingsObject#dataType\n* @type {\"json\" | \"xml\" | \"text\" | \"octet\" | undefined}\n*/ /**\n* Function to call on error.\n*\n* @name Highcharts.AjaxSettingsObject#error\n* @type {Function | undefined}\n*/ /**\n* The headers; keyed on header name.\n*\n* @name Highcharts.AjaxSettingsObject#headers\n* @type {Highcharts.Dictionary<string> | undefined}\n*/ /**\n* Function to call on success.\n*\n* @name Highcharts.AjaxSettingsObject#success\n* @type {Function | undefined}\n*/ /**\n* The HTTP method to use. For example GET or POST.\n*\n* @name Highcharts.AjaxSettingsObject#type\n* @type {string | undefined}\n*/ /**\n* The URL to call.\n*\n* @name Highcharts.AjaxSettingsObject#url\n* @type {string}\n*/\n(''); // Keeps doclets above in JS file\n\n;// ./code/es-modules/Extensions/Exporting/Exporting.js\n/* *\n *\n *  Exporting module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\nconst { defaultOptions, setOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { downloadURL: Exporting_downloadURL, getScript: Exporting_getScript } = Extensions_DownloadURL;\n\n\n\n\nconst { composed: Exporting_composed, doc: Exporting_doc, isFirefox, isMS, isSafari: Exporting_isSafari, SVG_NS, win: Exporting_win } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nconst { addEvent: Exporting_addEvent, clearTimeout: Exporting_clearTimeout, createElement, css, discardElement: Exporting_discardElement, error: Exporting_error, extend, find, fireEvent: Exporting_fireEvent, isObject, merge, objectEach: Exporting_objectEach, pick, pushUnique: Exporting_pushUnique, removeEvent, splat, uniqueKey } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nhighcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default().allowedAttributes.push('data-z-index', 'fill-opacity', 'filter', 'preserveAspectRatio', 'rx', 'ry', 'stroke-dasharray', 'stroke-linejoin', 'stroke-opacity', 'text-anchor', 'transform', 'transform-origin', 'version', 'viewBox', 'visibility', 'xmlns', 'xmlns:xlink');\nhighcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default().allowedTags.push('desc', 'clippath', 'fedropshadow', 'femorphology', 'g', 'image');\n/* *\n *\n *  Constants\n *\n * */\nconst Exporting_domurl = Exporting_win.URL || Exporting_win.webkitURL || Exporting_win;\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Exporting class provides methods for exporting charts to images. If the\n * exporting module is loaded, this class is instantiated on the chart and\n * available through the `chart.exporting` property. Read more about the\n * [exporting module](https://www.highcharts.com/docs/export-module-overview).\n *\n * @class\n * @name Highcharts.Exporting\n *\n * @param {Highcharts.Chart} chart\n * The chart instance.\n *\n */\nclass Exporting {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(chart, options) {\n        this.options = {};\n        this.chart = chart;\n        this.options = options;\n        this.btnCount = 0;\n        this.buttonOffset = 0;\n        this.divElements = [];\n        this.svgElements = [];\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Make hyphenated property names out of camelCase.\n     *\n     * @private\n     * @static\n     * @function Highcharts.Exporting#hyphenate\n     *\n     * @param {string} property\n     * Property name in camelCase.\n     *\n     * @return {string}\n     * Hyphenated property name.\n     *\n     * @requires modules/exporting\n     */\n    static hyphenate(property) {\n        return property.replace(/[A-Z]/g, function (match) {\n            return '-' + match.toLowerCase();\n        });\n    }\n    /**\n     * Get data:URL from image URL.\n     *\n     * @private\n     * @static\n     * @async\n     * @function Highcharts.Exporting#imageToDataURL\n     *\n     * @param {string} imageURL\n     * The address or URL of the image.\n     * @param {number} scale\n     * The scale of the image.\n     * @param {string} imageType\n     * The export type of the image.\n     *\n     * @requires modules/exporting\n     */\n    static async imageToDataURL(imageURL, scale, imageType) {\n        // First, wait for the image to be loaded\n        const img = await Exporting.loadImage(imageURL), canvas = Exporting_doc.createElement('canvas'), ctx = canvas?.getContext('2d');\n        if (!ctx) {\n            throw new Error('No canvas found!');\n        }\n        else {\n            canvas.height = img.height * scale;\n            canvas.width = img.width * scale;\n            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\n            // Now we try to get the contents of the canvas\n            return canvas.toDataURL(imageType);\n        }\n    }\n    /**\n     * Loads an image from the provided URL.\n     *\n     * @private\n     * @static\n     * @function Highcharts.Exporting#loadImage\n     *\n     * @param {string} imageURL\n     * The address or URL of the image.\n     *\n     * @return {Promise<HTMLImageElement>}\n     * Returns a Promise that resolves with the loaded HTMLImageElement.\n     *\n     * @requires modules/exporting\n     */\n    static loadImage(imageURL) {\n        return new Promise((resolve, reject) => {\n            // Create an image\n            const image = new Exporting_win.Image();\n            // Must be set prior to loading image source\n            image.crossOrigin = 'Anonymous';\n            // Return the image in case of success\n            image.onload = () => {\n                // IE bug where image is not always ready despite load event\n                setTimeout(() => {\n                    resolve(image);\n                }, Exporting.loadEventDeferDelay);\n            };\n            // Reject in case of fail\n            image.onerror = (error) => {\n                reject(error);\n            };\n            // Provide the image URL\n            image.src = imageURL;\n        });\n    }\n    /**\n     * Prepares and returns the image export options with default values where\n     * necessary.\n     *\n     * @private\n     * @static\n     * @function Highcharts.Exporting#prepareImageOptions\n     *\n     * @param {Highcharts.ExportingOptions} exportingOptions\n     * The exporting options.\n     *\n     * @return {Exporting.ImageOptions}\n     * The finalized image export options with ensured values.\n     *\n     * @requires modules/exporting\n     */\n    static prepareImageOptions(exportingOptions) {\n        const type = exportingOptions?.type || 'image/png', libURL = (exportingOptions?.libURL ||\n            defaultOptions.exporting?.libURL);\n        return {\n            type,\n            filename: ((exportingOptions?.filename || 'chart') +\n                '.' +\n                (type === 'image/svg+xml' ? 'svg' : type.split('/')[1])),\n            scale: exportingOptions?.scale || 1,\n            // Allow libURL to end with or without fordward slash\n            libURL: libURL?.slice(-1) !== '/' ? libURL + '/' : libURL\n        };\n    }\n    /**\n     * A collection of fixes on the produced SVG to account for expand\n     * properties and browser bugs. Returns a cleaned SVG.\n     *\n     * @private\n     * @static\n     * @function Highcharts.Exporting#sanitizeSVG\n     *\n     * @param {string} svg\n     * SVG code to sanitize.\n     * @param {Highcharts.Options} options\n     * Chart options to apply.\n     *\n     * @return {string}\n     * Sanitized SVG code.\n     *\n     * @requires modules/exporting\n     */\n    static sanitizeSVG(svg, options) {\n        const split = svg.indexOf('</svg>') + 6, useForeignObject = svg.indexOf('<foreignObject') > -1;\n        let html = svg.substr(split);\n        // Remove any HTML added to the container after the SVG (#894, #9087)\n        svg = svg.substr(0, split);\n        if (useForeignObject) {\n            // Some tags needs to be closed in xhtml (#13726)\n            svg = svg.replace(/(<(?:img|br).*?(?=\\>))>/g, '$1 />');\n            // Move HTML into a foreignObject\n        }\n        else if (html && options?.exporting?.allowHTML) {\n            html = '<foreignObject x=\"0\" y=\"0\" ' +\n                'width=\"' + options.chart.width + '\" ' +\n                'height=\"' + options.chart.height + '\">' +\n                '<body xmlns=\"http://www.w3.org/1999/xhtml\">' +\n                // Some tags needs to be closed in xhtml (#13726)\n                html.replace(/(<(?:img|br).*?(?=\\>))>/g, '$1 />') +\n                '</body>' +\n                '</foreignObject>';\n            svg = svg.replace('</svg>', html + '</svg>');\n        }\n        svg = svg\n            .replace(/zIndex=\"[^\"]+\"/g, '')\n            .replace(/symbolName=\"[^\"]+\"/g, '')\n            .replace(/jQuery\\d+=\"[^\"]+\"/g, '')\n            .replace(/url\\((\"|&quot;)(.*?)(\"|&quot;)\\;?\\)/g, 'url($2)')\n            .replace(/url\\([^#]+#/g, 'url(#')\n            .replace(/<svg /, '<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" ')\n            .replace(/ (NS\\d+\\:)?href=/g, ' xlink:href=') // #3567\n            .replace(/\\n+/g, ' ')\n            // Replace HTML entities, issue #347\n            .replace(/&nbsp;/g, '\\u00A0') // No-break space\n            .replace(/&shy;/g, '\\u00AD'); // Soft hyphen\n        return svg;\n    }\n    /**\n     * Get blob URL from SVG code. Falls back to normal data URI.\n     *\n     * @private\n     * @static\n     * @function Highcharts.Exporting#svgToDataURL\n     *\n     * @param {string} svg\n     * SVG to get the URL from.\n     *\n     * @return {string}\n     * The data URL.\n     *\n     * @requires modules/exporting\n     */\n    static svgToDataURL(svg) {\n        // Webkit and not chrome\n        const userAgent = Exporting_win.navigator.userAgent;\n        const webKit = (userAgent.indexOf('WebKit') > -1 &&\n            userAgent.indexOf('Chrome') < 0);\n        try {\n            // Safari requires data URI since it doesn't allow navigation to\n            // blob URLs. ForeignObjects also don't work well in Blobs in Chrome\n            // (#14780).\n            if (!webKit && svg.indexOf('<foreignObject') === -1) {\n                return Exporting_domurl.createObjectURL(new Exporting_win.Blob([svg], {\n                    type: 'image/svg+xml;charset-utf-16'\n                }));\n            }\n        }\n        catch {\n            // Ignore\n        }\n        return 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(svg);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Add the export button to the chart, with options.\n     *\n     * @private\n     * @function Highcharts.Exporting#addButton\n     *\n     * @param {Highcharts.ExportingButtonOptions} options\n     * The exporting button options object.\n     *\n     * @requires modules/exporting\n     */\n    addButton(options) {\n        const exporting = this, chart = exporting.chart, renderer = chart.renderer, btnOptions = merge(chart.options.navigation?.buttonOptions, options), onclick = btnOptions.onclick, menuItems = btnOptions.menuItems, symbolSize = btnOptions.symbolSize || 12;\n        let symbol;\n        if (btnOptions.enabled === false || !btnOptions.theme) {\n            return;\n        }\n        const theme = chart.styledMode ? {} : btnOptions.theme;\n        let callback = (() => { });\n        if (onclick) {\n            callback = function (e) {\n                if (e) {\n                    e.stopPropagation();\n                }\n                onclick.call(chart, e);\n            };\n        }\n        else if (menuItems) {\n            callback = function (e) {\n                // Consistent with onclick call (#3495)\n                if (e) {\n                    e.stopPropagation();\n                }\n                exporting.contextMenu(button.menuClassName, menuItems, button.translateX || 0, button.translateY || 0, button.width || 0, button.height || 0, button);\n                button.setState(2);\n            };\n        }\n        if (btnOptions.text && btnOptions.symbol) {\n            theme.paddingLeft = pick(theme.paddingLeft, 30);\n        }\n        else if (!btnOptions.text) {\n            extend(theme, {\n                width: btnOptions.width,\n                height: btnOptions.height,\n                padding: 0\n            });\n        }\n        const button = renderer\n            .button(btnOptions.text || '', 0, 0, callback, theme, void 0, void 0, void 0, void 0, btnOptions.useHTML)\n            .addClass(options.className || '')\n            .attr({\n            title: pick(chart.options.lang[(btnOptions._titleKey ||\n                btnOptions.titleKey)], '')\n        });\n        button.menuClassName = (options.menuClassName ||\n            'highcharts-menu-' + exporting.btnCount++);\n        if (btnOptions.symbol) {\n            symbol = renderer\n                .symbol(btnOptions.symbol, Math.round((btnOptions.symbolX || 0) - (symbolSize / 2)), Math.round((btnOptions.symbolY || 0) - (symbolSize / 2)), symbolSize, symbolSize, \n            // If symbol is an image, scale it (#7957)\n            {\n                width: symbolSize,\n                height: symbolSize\n            })\n                .addClass('highcharts-button-symbol')\n                .attr({\n                zIndex: 1\n            })\n                .add(button);\n            if (!chart.styledMode) {\n                symbol.attr({\n                    stroke: btnOptions.symbolStroke,\n                    fill: btnOptions.symbolFill,\n                    'stroke-width': btnOptions.symbolStrokeWidth || 1\n                });\n            }\n        }\n        button\n            .add(exporting.group)\n            .align(extend(btnOptions, {\n            width: button.width,\n            x: pick(btnOptions.x, exporting.buttonOffset) // #1654\n        }), true, 'spacingBox');\n        exporting.buttonOffset += (((button.width || 0) + (btnOptions.buttonSpacing || 0)) *\n            (btnOptions.align === 'right' ? -1 : 1));\n        exporting.svgElements.push(button, symbol);\n    }\n    /**\n     * Clean up after printing a chart.\n     *\n     * @private\n     * @function Highcharts.Exporting#afterPrint\n     *\n     * @emits Highcharts.Chart#event:afterPrint\n     *\n     * @requires modules/exporting\n     */\n    afterPrint() {\n        const chart = this.chart;\n        if (!this.printReverseInfo) {\n            return void 0;\n        }\n        const { childNodes, origDisplay, resetParams } = this.printReverseInfo;\n        // Put the chart back in\n        this.moveContainers(chart.renderTo);\n        // Restore all body content\n        [].forEach.call(childNodes, function (node, i) {\n            if (node.nodeType === 1) {\n                node.style.display = (origDisplay[i] || '');\n            }\n        });\n        this.isPrinting = false;\n        // Reset printMaxWidth\n        if (resetParams) {\n            chart.setSize.apply(chart, resetParams);\n        }\n        delete this.printReverseInfo;\n        Exporting.printingChart = void 0;\n        Exporting_fireEvent(chart, 'afterPrint');\n    }\n    /**\n     * Prepare chart and document before printing a chart.\n     *\n     * @private\n     * @function Highcharts.Exporting#beforePrint\n     *\n     * @emits Highcharts.Chart#event:beforePrint\n     *\n     * @requires modules/exporting\n     */\n    beforePrint() {\n        const chart = this.chart, body = Exporting_doc.body, printMaxWidth = this.options.printMaxWidth, printReverseInfo = {\n            childNodes: body.childNodes,\n            origDisplay: [],\n            resetParams: void 0\n        };\n        this.isPrinting = true;\n        chart.pointer?.reset(void 0, 0);\n        Exporting_fireEvent(chart, 'beforePrint');\n        // Handle printMaxWidth\n        if (printMaxWidth && chart.chartWidth > printMaxWidth) {\n            printReverseInfo.resetParams = [\n                chart.options.chart.width,\n                void 0,\n                false\n            ];\n            chart.setSize(printMaxWidth, void 0, false);\n        }\n        // Hide all body content\n        [].forEach.call(printReverseInfo.childNodes, function (node, i) {\n            if (node.nodeType === 1) {\n                printReverseInfo.origDisplay[i] = node.style.display;\n                node.style.display = 'none';\n            }\n        });\n        // Pull out the chart\n        this.moveContainers(body);\n        // Storage details for undo action after printing\n        this.printReverseInfo = printReverseInfo;\n    }\n    /**\n     * Display a popup menu for choosing the export type.\n     *\n     * @private\n     * @function Highcharts.Exporting#contextMenu\n     *\n     * @param {string} className\n     * An identifier for the menu.\n     * @param {Array<(string | Highcharts.ExportingMenuObject)>} items\n     * A collection with text and onclicks for the items.\n     * @param {number} x\n     * The x position of the opener button.\n     * @param {number} y\n     * The y position of the opener button.\n     * @param {number} width\n     * The width of the opener button.\n     * @param {number} height\n     * The height of the opener button.\n     * @param {SVGElement} button\n     * The SVG button element.\n     *\n     * @emits Highcharts.Chart#event:exportMenuHidden\n     * @emits Highcharts.Chart#event:exportMenuShown\n     *\n     * @requires modules/exporting\n     */\n    contextMenu(className, items, x, y, width, height, button) {\n        const exporting = this, chart = exporting.chart, navOptions = chart.options.navigation, chartWidth = chart.chartWidth, chartHeight = chart.chartHeight, cacheName = 'cache-' + className, \n        // For mouse leave detection\n        menuPadding = Math.max(width, height);\n        let innerMenu, menu = chart[cacheName];\n        // Create the menu only the first time\n        if (!menu) {\n            // Create a HTML element above the SVG\n            exporting.contextMenuEl = chart[cacheName] = menu =\n                createElement('div', {\n                    className: className\n                }, {\n                    position: 'absolute',\n                    zIndex: 1000,\n                    padding: menuPadding + 'px',\n                    pointerEvents: 'auto',\n                    ...chart.renderer.style\n                }, chart.scrollablePlotArea?.fixedDiv || chart.container);\n            innerMenu = createElement('ul', { className: 'highcharts-menu' }, chart.styledMode ? {} : {\n                listStyle: 'none',\n                margin: 0,\n                padding: 0\n            }, menu);\n            // Presentational CSS\n            if (!chart.styledMode) {\n                css(innerMenu, extend({\n                    MozBoxShadow: '3px 3px 10px #0008',\n                    WebkitBoxShadow: '3px 3px 10px #0008',\n                    boxShadow: '3px 3px 10px #0008'\n                }, navOptions?.menuStyle || {}));\n            }\n            // Hide on mouse out\n            menu.hideMenu = function () {\n                css(menu, { display: 'none' });\n                if (button) {\n                    button.setState(0);\n                }\n                if (chart.exporting) {\n                    chart.exporting.openMenu = false;\n                }\n                // #10361, #9998\n                css(chart.renderTo, { overflow: 'hidden' });\n                css(chart.container, { overflow: 'hidden' });\n                Exporting_clearTimeout(menu.hideTimer);\n                Exporting_fireEvent(chart, 'exportMenuHidden');\n            };\n            // Hide the menu some time after mouse leave (#1357)\n            exporting.events?.push(Exporting_addEvent(menu, 'mouseleave', function () {\n                menu.hideTimer = Exporting_win.setTimeout(menu.hideMenu, 500);\n            }), Exporting_addEvent(menu, 'mouseenter', function () {\n                Exporting_clearTimeout(menu.hideTimer);\n            }), \n            // Hide it on clicking or touching outside the menu (#2258,\n            // #2335, #2407)\n            Exporting_addEvent(Exporting_doc, 'mouseup', function (e) {\n                if (!chart.pointer?.inClass(e.target, className)) {\n                    menu.hideMenu();\n                }\n            }), Exporting_addEvent(menu, 'click', function () {\n                if (chart.exporting?.openMenu) {\n                    menu.hideMenu();\n                }\n            }));\n            // Create the items\n            items.forEach(function (item) {\n                if (typeof item === 'string') {\n                    if (exporting.options.menuItemDefinitions?.[item]) {\n                        item = exporting.options.menuItemDefinitions[item];\n                    }\n                }\n                if (isObject(item, true)) {\n                    let element;\n                    if (item.separator) {\n                        element = createElement('hr', void 0, void 0, innerMenu);\n                    }\n                    else {\n                        // When chart initialized with the table, wrong button\n                        // text displayed, #14352.\n                        if (item.textKey === 'viewData' &&\n                            exporting.isDataTableVisible) {\n                            item.textKey = 'hideData';\n                        }\n                        element = createElement('li', {\n                            className: 'highcharts-menu-item',\n                            onclick: function (e) {\n                                if (e) { // IE7\n                                    e.stopPropagation();\n                                }\n                                menu.hideMenu();\n                                if (typeof item !== 'string' && item.onclick) {\n                                    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n                                    item.onclick.apply(chart, arguments);\n                                }\n                            }\n                        }, void 0, innerMenu);\n                        highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default().setElementHTML(element, item.text || chart.options.lang[item.textKey]);\n                        if (!chart.styledMode) {\n                            element.onmouseover = function () {\n                                css(this, navOptions?.menuItemHoverStyle || {});\n                            };\n                            element.onmouseout = function () {\n                                css(this, navOptions?.menuItemStyle || {});\n                            };\n                            css(element, extend({\n                                cursor: 'pointer'\n                            }, navOptions?.menuItemStyle || {}));\n                        }\n                    }\n                    // Keep references to menu divs to be able to destroy them\n                    exporting.divElements.push(element);\n                }\n            });\n            // Keep references to menu and innerMenu div to be able to destroy\n            // them\n            exporting.divElements.push(innerMenu, menu);\n            exporting.menuHeight = menu.offsetHeight;\n            exporting.menuWidth = menu.offsetWidth;\n        }\n        const menuStyle = { display: 'block' };\n        // If outside right, right align it\n        if (x + (exporting.menuWidth || 0) > chartWidth) {\n            menuStyle.right = (chartWidth - x - width - menuPadding) + 'px';\n        }\n        else {\n            menuStyle.left = (x - menuPadding) + 'px';\n        }\n        // If outside bottom, bottom align it\n        if (y + height + (exporting.menuHeight || 0) >\n            chartHeight &&\n            button.alignOptions?.verticalAlign !== 'top') {\n            menuStyle.bottom = (chartHeight - y - menuPadding) + 'px';\n        }\n        else {\n            menuStyle.top = (y + height - menuPadding) + 'px';\n        }\n        css(menu, menuStyle);\n        // #10361, #9998\n        css(chart.renderTo, { overflow: '' });\n        css(chart.container, { overflow: '' });\n        if (chart.exporting) {\n            chart.exporting.openMenu = true;\n        }\n        Exporting_fireEvent(chart, 'exportMenuShown');\n    }\n    /**\n     * Destroy the export buttons.\n     *\n     * @private\n     * @function Highcharts.Exporting#destroy\n     *\n     * @param {global.Event} [e]\n     * Event object.\n     *\n     * @requires modules/exporting\n     */\n    destroy(e) {\n        const exporting = this, chart = e ? e.target : exporting.chart, { divElements, events, svgElements } = exporting;\n        let cacheName;\n        // Destroy the extra buttons added\n        svgElements.forEach((elem, i) => {\n            // Destroy and null the svg elements\n            if (elem) { // #1822\n                elem.onclick = elem.ontouchstart = null;\n                cacheName = 'cache-' + elem.menuClassName;\n                if (chart[cacheName]) {\n                    delete chart[cacheName];\n                }\n                svgElements[i] = elem.destroy();\n            }\n        });\n        svgElements.length = 0;\n        // Destroy the exporting group\n        if (exporting.group) {\n            exporting.group.destroy();\n            delete exporting.group;\n        }\n        // Destroy the divs for the menu\n        divElements.forEach(function (elem, i) {\n            if (elem) {\n                // Remove the event handler\n                Exporting_clearTimeout(elem.hideTimer); // #5427\n                removeEvent(elem, 'mouseleave');\n                // Remove inline events\n                divElements[i] =\n                    elem.onmouseout =\n                        elem.onmouseover =\n                            elem.ontouchstart =\n                                elem.onclick = null;\n                // Destroy the div by moving to garbage bin\n                Exporting_discardElement(elem);\n            }\n        });\n        divElements.length = 0;\n        if (events) {\n            events.forEach(function (unbind) {\n                unbind();\n            });\n            events.length = 0;\n        }\n    }\n    /**\n     * Get data URL to an image of an SVG and call download on its options\n     * object:\n     *\n     * - **filename:** Name of resulting downloaded file without extension.\n     * Default is based on the chart title.\n     * - **type:** File type of resulting download. Default is `image/png`.\n     * - **scale:** Scaling factor of downloaded image compared to source.\n     * Default is `2`.\n     * - **libURL:** URL pointing to location of dependency scripts to download\n     * on demand. Default is the exporting.libURL option of the global\n     * Highcharts options pointing to our server.\n     *\n     * @async\n     * @private\n     * @function Highcharts.Exporting#downloadSVG\n     *\n     * @param {string} svg\n     * The generated SVG.\n     * @param {Highcharts.ExportingOptions} exportingOptions\n     * The exporting options.\n     *\n     * @requires modules/exporting\n     */\n    // eslint-disable-next-line @typescript-eslint/require-await\n    async downloadSVG(svg, exportingOptions) {\n        const eventArgs = {\n            svg,\n            exportingOptions,\n            exporting: this\n        };\n        // Fire a custom event before the export starts\n        Exporting_fireEvent(Exporting.prototype, 'downloadSVG', eventArgs);\n        // If the event was prevented, do not proceed with the export\n        if (eventArgs.defaultPrevented) {\n            return;\n        }\n        // Get the final image options\n        const { type, filename, scale, libURL } = Exporting.prepareImageOptions(exportingOptions);\n        let svgURL;\n        // Initiate download depending on file type\n        if (type === 'application/pdf') {\n            // Error in case of offline-exporting module is not loaded\n            throw new Error('Offline exporting logic for PDF type is not found.');\n        }\n        else if (type === 'image/svg+xml') {\n            // SVG download. In this case, we want to use Microsoft specific\n            // Blob if available\n            if (typeof Exporting_win.MSBlobBuilder !== 'undefined') {\n                const blob = new Exporting_win.MSBlobBuilder();\n                blob.append(svg);\n                svgURL = blob.getBlob('image/svg+xml');\n            }\n            else {\n                svgURL = Exporting.svgToDataURL(svg);\n            }\n            // Download the chart\n            Exporting_downloadURL(svgURL, filename);\n        }\n        else {\n            // PNG/JPEG download - create bitmap from SVG\n            svgURL = Exporting.svgToDataURL(svg);\n            try {\n                Exporting.objectURLRevoke = true;\n                // First, try to get PNG by rendering on canvas\n                const dataURL = await Exporting.imageToDataURL(svgURL, scale, type);\n                Exporting_downloadURL(dataURL, filename);\n            }\n            catch (error) {\n                // No need for the below logic to run in case no canvas is\n                // found\n                if (error.message === 'No canvas found!') {\n                    throw error;\n                }\n                // Or in case of exceeding the input length\n                if (svg.length > 100000000 /* RegexLimits.svgLimit */) {\n                    throw new Error('Input too long');\n                }\n                // Failed due to tainted canvas\n                // Create new and untainted canvas\n                const canvas = Exporting_doc.createElement('canvas'), ctx = canvas.getContext('2d'), matchedImageWidth = svg.match(\n                // eslint-disable-next-line max-len\n                /^<svg[^>]*\\s{,1000}width\\s{,1000}=\\s{,1000}\\\"?(\\d+)\\\"?[^>]*>/), matchedImageHeight = svg.match(\n                // eslint-disable-next-line max-len\n                /^<svg[^>]*\\s{0,1000}height\\s{,1000}=\\s{,1000}\\\"?(\\d+)\\\"?[^>]*>/);\n                if (ctx &&\n                    matchedImageWidth &&\n                    matchedImageHeight) {\n                    const imageWidth = +matchedImageWidth[1] * scale, imageHeight = +matchedImageHeight[1] * scale, downloadWithCanVG = () => {\n                        const v = Exporting_win.canvg.Canvg.fromString(ctx, svg);\n                        v.start();\n                        Exporting_downloadURL(Exporting_win.navigator.msSaveOrOpenBlob ?\n                            canvas.msToBlob() :\n                            canvas.toDataURL(type), filename);\n                    };\n                    canvas.width = imageWidth;\n                    canvas.height = imageHeight;\n                    // Must load canVG first if not found. Don't destroy the\n                    // object URL yet since we are doing things\n                    // asynchronously\n                    if (!Exporting_win.canvg) {\n                        Exporting.objectURLRevoke = true;\n                        await Exporting_getScript(libURL + 'canvg.js');\n                    }\n                    // Use loaded canvg\n                    downloadWithCanVG();\n                }\n            }\n            finally {\n                if (Exporting.objectURLRevoke) {\n                    try {\n                        Exporting_domurl.revokeObjectURL(svgURL);\n                    }\n                    catch {\n                        // Ignore\n                    }\n                }\n            }\n        }\n    }\n    /**\n     * Submit an SVG version of the chart along with some parameters for local\n     * conversion (PNG, JPEG, and SVG) or conversion on a server (PDF).\n     *\n     * @sample highcharts/members/chart-exportchart/\n     * Export with no options\n     * @sample highcharts/members/chart-exportchart-filename/\n     * PDF type and custom filename\n     * @sample highcharts/exporting/menuitemdefinitions-webp/\n     * Export to WebP\n     * @sample highcharts/members/chart-exportchart-custom-background/\n     * Different chart background in export\n     * @sample stock/members/chart-exportchart/\n     * Export with Highcharts Stock\n     *\n     * @async\n     * @function Highcharts.Exporting#exportChart\n     *\n     * @param {Highcharts.ExportingOptions} [exportingOptions]\n     * Exporting options in addition to those defined in\n     * [exporting](https://api.highcharts.com/highcharts/exporting).\n     * @param {Highcharts.Options} [chartOptions]\n     * Additional chart options for the exported chart. For example a different\n     * background color can be added here, or `dataLabels` for export only.\n     *\n     * @requires modules/exporting\n     */\n    async exportChart(exportingOptions, chartOptions) {\n        // Merge the options\n        exportingOptions = merge(this.options, exportingOptions);\n        // If local if expected\n        if (exportingOptions.local) {\n            // Trigger the local export logic\n            await this.localExport(exportingOptions, chartOptions || {});\n        }\n        else {\n            // Get the SVG representation\n            const svg = this.getSVGForExport(exportingOptions, chartOptions);\n            // Do the post\n            if (exportingOptions.url) {\n                await Core_HttpUtilities.post(exportingOptions.url, {\n                    filename: exportingOptions.filename ?\n                        exportingOptions.filename.replace(/\\//g, '-') :\n                        this.getFilename(),\n                    type: exportingOptions.type,\n                    width: exportingOptions.width,\n                    scale: exportingOptions.scale,\n                    svg\n                }, exportingOptions.fetchOptions);\n            }\n        }\n    }\n    /**\n     * Handles the fallback to the export server when a local export fails.\n     *\n     * @private\n     * @async\n     * @function Highcharts.Exporting#fallbackToServer\n     *\n     * @param {Highcharts.ExportingOptions} exportingOptions\n     * The exporting options.\n     * @param {Error} err\n     * The error that caused the local export to fail.\n     *\n     * @return {Promise<void>}\n     * A promise that resolves when the fallback process is complete.\n     *\n     * @requires modules/exporting\n     */\n    async fallbackToServer(exportingOptions, err) {\n        if (exportingOptions.fallbackToExportServer === false) {\n            if (exportingOptions.error) {\n                exportingOptions.error(exportingOptions, err);\n            }\n            else {\n                // Fallback disabled\n                Exporting_error(28, true);\n            }\n        }\n        else if (exportingOptions.type === 'application/pdf') {\n            // The local must be false to fallback to server for PDF export\n            exportingOptions.local = false;\n            // Allow fallbacking to server only for PDFs that failed locally\n            await this.exportChart(exportingOptions);\n        }\n    }\n    /**\n     * Return the unfiltered innerHTML of the chart container. Used as hook for\n     * plugins. In styled mode, it also takes care of inlining CSS style rules.\n     *\n     * @see Chart#getSVG\n     *\n     * @function Highcharts.Exporting#getChartHTML\n     *\n     * @param {boolean} [applyStyleSheets]\n     * whether or not to apply the style sheets.\n     *\n     * @return {string}\n     * The unfiltered SVG of the chart.\n     *\n     * @requires modules/exporting\n     */\n    getChartHTML(applyStyleSheets) {\n        const chart = this.chart;\n        if (applyStyleSheets) {\n            this.inlineStyles();\n        }\n        this.resolveCSSVariables();\n        return chart.container.innerHTML;\n    }\n    /**\n     * Get the default file name used for exported charts. By default it creates\n     * a file name based on the chart title.\n     *\n     * @function Highcharts.Exporting#getFilename\n     *\n     * @return {string}\n     * A file name without extension.\n     *\n     * @requires modules/exporting\n     */\n    getFilename() {\n        const titleText = this.chart.userOptions.title?.text;\n        let filename = this.options.filename;\n        if (filename) {\n            return filename.replace(/\\//g, '-');\n        }\n        if (typeof titleText === 'string') {\n            filename = titleText\n                .toLowerCase()\n                .replace(/<\\/?[^>]+(>|$)/g, '') // Strip HTML tags\n                .replace(/[\\s_]+/g, '-')\n                .replace(/[^a-z\\d\\-]/g, '') // Preserve only latin\n                .replace(/^[\\-]+/g, '') // Dashes in the start\n                .replace(/[\\-]+/g, '-') // Dashes in a row\n                .substr(0, 24)\n                .replace(/[\\-]+$/g, ''); // Dashes in the end;\n        }\n        if (!filename || filename.length < 5) {\n            filename = 'chart';\n        }\n        return filename;\n    }\n    /**\n     * Return an SVG representation of the chart.\n     *\n     * @sample highcharts/members/chart-getsvg/\n     * View the SVG from a button\n     *\n     * @function Highcharts.Exporting#getSVG\n     *\n     * @param {Highcharts.Options} [chartOptions]\n     * Additional chart options for the generated SVG representation. For\n     * collections like `xAxis`, `yAxis` or `series`, the additional options is\n     * either merged in to the original item of the same `id`, or to the first\n     * item if a common id is not found.\n     *\n     * @return {string}\n     * The SVG representation of the rendered chart.\n     *\n     * @emits Highcharts.Chart#event:getSVG\n     *\n     * @requires modules/exporting\n     */\n    getSVG(chartOptions) {\n        const chart = this.chart;\n        let svg, seriesOptions, \n        // Copy the options and add extra options\n        options = merge(chart.options, chartOptions);\n        // Use userOptions to make the options chain in series right (#3881)\n        options.plotOptions = merge(chart.userOptions.plotOptions, chartOptions?.plotOptions);\n        // ... and likewise with time, avoid that undefined time properties are\n        // merged over legacy global time options\n        options.time = merge(chart.userOptions.time, chartOptions?.time);\n        // Create a sandbox where a new chart will be generated\n        const sandbox = createElement('div', void 0, {\n            position: 'absolute',\n            top: '-9999em',\n            width: chart.chartWidth + 'px',\n            height: chart.chartHeight + 'px'\n        }, Exporting_doc.body);\n        // Get the source size\n        const cssWidth = chart.renderTo.style.width, cssHeight = chart.renderTo.style.height, sourceWidth = options.exporting?.sourceWidth ||\n            options.chart.width ||\n            (/px$/.test(cssWidth) && parseInt(cssWidth, 10)) ||\n            (options.isGantt ? 800 : 600), sourceHeight = options.exporting?.sourceHeight ||\n            options.chart.height ||\n            (/px$/.test(cssHeight) && parseInt(cssHeight, 10)) ||\n            400;\n        // Override some options\n        extend(options.chart, {\n            animation: false,\n            renderTo: sandbox,\n            forExport: true,\n            renderer: 'SVGRenderer',\n            width: sourceWidth,\n            height: sourceHeight\n        });\n        if (options.exporting) {\n            options.exporting.enabled = false; // Hide buttons in print\n        }\n        delete options.data; // #3004\n        // Prepare for replicating the chart\n        options.series = [];\n        chart.series.forEach(function (serie) {\n            seriesOptions = merge(serie.userOptions, {\n                animation: false, // Turn off animation\n                enableMouseTracking: false,\n                showCheckbox: false,\n                visible: serie.visible\n            });\n            // Used for the navigator series that has its own option set\n            if (!seriesOptions.isInternal) {\n                options?.series?.push(seriesOptions);\n            }\n        });\n        const colls = {};\n        chart.axes.forEach(function (axis) {\n            // Assign an internal key to ensure a one-to-one mapping (#5924)\n            if (!axis.userOptions.internalKey) { // #6444\n                axis.userOptions.internalKey = uniqueKey();\n            }\n            if (options && !axis.options.isInternal) {\n                if (!colls[axis.coll]) {\n                    colls[axis.coll] = true;\n                    options[axis.coll] = [];\n                }\n                options[axis.coll].push(merge(axis.userOptions, {\n                    visible: axis.visible,\n                    // Force some options that could have be set directly on\n                    // the axis while missing in the userOptions or options.\n                    type: axis.type,\n                    uniqueNames: axis.uniqueNames\n                }));\n            }\n        });\n        // Make sure the `colorAxis` object of the `defaultOptions` isn't used\n        // in the chart copy's user options, because a color axis should only be\n        // added when the user actually applies it.\n        options.colorAxis = chart.userOptions.colorAxis;\n        // Generate the chart copy\n        const chartCopy = new chart.constructor(options, chart.callback);\n        // Axis options and series options  (#2022, #3900, #5982)\n        if (chartOptions) {\n            ['xAxis', 'yAxis', 'series'].forEach(function (coll) {\n                if (chartOptions[coll]) {\n                    chartCopy.update({\n                        [coll]: chartOptions[coll]\n                    });\n                }\n            });\n        }\n        // Reflect axis extremes in the export (#5924)\n        chart.axes.forEach(function (axis) {\n            const axisCopy = find(chartCopy.axes, (copy) => copy.options.internalKey === axis.userOptions.internalKey);\n            if (axisCopy) {\n                const extremes = axis.getExtremes(), \n                // Make sure min and max overrides in the\n                // `exporting.chartOptions.xAxis` settings are reflected.\n                // These should override user-set extremes via zooming,\n                // scrollbar etc (#7873).\n                exportOverride = splat(chartOptions?.[axis.coll] || {})[0], userMin = 'min' in exportOverride ?\n                    exportOverride.min :\n                    extremes.userMin, userMax = 'max' in exportOverride ?\n                    exportOverride.max :\n                    extremes.userMax;\n                if (((typeof userMin !== 'undefined' &&\n                    userMin !== axisCopy.min) || (typeof userMax !== 'undefined' &&\n                    userMax !== axisCopy.max))) {\n                    axisCopy.setExtremes(userMin ?? void 0, userMax ?? void 0, true, false);\n                }\n            }\n        });\n        // Get the SVG from the container's innerHTML\n        svg = chartCopy.exporting?.getChartHTML(chart.styledMode ||\n            options.exporting?.applyStyleSheets) || '';\n        Exporting_fireEvent(chart, 'getSVG', { chartCopy: chartCopy });\n        svg = Exporting.sanitizeSVG(svg, options);\n        // Free up memory\n        options = void 0;\n        chartCopy.destroy();\n        Exporting_discardElement(sandbox);\n        return svg;\n    }\n    /**\n     * Gets the SVG for export using the getSVG function with additional\n     * options.\n     *\n     * @private\n     * @function Highcharts.Exporting#getSVGForExport\n     *\n     * @param {Highcharts.ExportingOptions} [exportingOptions]\n     * The exporting options.\n     * @param {Highcharts.Options} [chartOptions]\n     * Additional chart options for the exported chart.\n     *\n     * @return {string}\n     * The SVG representation of the rendered chart.\n     *\n     * @requires modules/exporting\n     */\n    getSVGForExport(exportingOptions, chartOptions) {\n        const currentExportingOptions = this.options;\n        return this.getSVG(merge({ chart: { borderRadius: 0 } }, currentExportingOptions.chartOptions, chartOptions, {\n            exporting: {\n                sourceWidth: (exportingOptions?.sourceWidth ||\n                    currentExportingOptions.sourceWidth),\n                sourceHeight: (exportingOptions?.sourceHeight ||\n                    currentExportingOptions.sourceHeight)\n            }\n        }));\n    }\n    /**\n     * Analyze inherited styles from stylesheets and add them inline.\n     *\n     * @private\n     * @function Highcharts.Exporting#inlineStyles\n     *\n     * @todo What are the border styles for text about? In general, text has a\n     * lot of properties.\n     *\n     * @todo Make it work with IE9 and IE10.\n     *\n     * @requires modules/exporting\n     */\n    inlineStyles() {\n        const denylist = Exporting.inlineDenylist, allowlist = Exporting.inlineAllowlist, // For IE\n        defaultStyles = {};\n        let dummySVG;\n        // Create an iframe where we read default styles without pollution from\n        // this body\n        const iframe = createElement('iframe', void 0, {\n            width: '1px',\n            height: '1px',\n            visibility: 'hidden'\n        }, Exporting_doc.body);\n        const iframeDoc = iframe.contentWindow?.document;\n        if (iframeDoc) {\n            iframeDoc.body.appendChild(iframeDoc.createElementNS(SVG_NS, 'svg'));\n        }\n        /**\n         * Call this on all elements and recurse to children.\n         *\n         * @private\n         * @function recurse\n         *\n         * @param {Highcharts.HTMLDOMElement | Highcharts.SVGSVGElement} node\n         * Element child.\n         */\n        function recurse(node) {\n            const filteredStyles = {};\n            let styles, parentStyles, dummy, denylisted, allowlisted, i;\n            /**\n             * Check computed styles and whether they are in the allow/denylist\n             * for styles or attributes.\n             *\n             * @private\n             * @function filterStyles\n             *\n             * @param {string | number | Highcharts.GradientColor | Highcharts.PatternObject | undefined} val\n             * Style value.\n             * @param {string} prop\n             * Style property name.\n             */\n            function filterStyles(val, prop) {\n                // Check against allowlist & denylist\n                denylisted = allowlisted = false;\n                if (allowlist.length) {\n                    // Styled mode in IE has a allowlist instead. Exclude all\n                    // props not in this list.\n                    i = allowlist.length;\n                    while (i-- && !allowlisted) {\n                        allowlisted = allowlist[i].test(prop);\n                    }\n                    denylisted = !allowlisted;\n                }\n                // Explicitly remove empty transforms\n                if (prop === 'transform' && val === 'none') {\n                    denylisted = true;\n                }\n                i = denylist.length;\n                while (i-- && !denylisted) {\n                    if (prop.length > 1000 /* RegexLimits.shortLimit */) {\n                        throw new Error('Input too long');\n                    }\n                    denylisted = (denylist[i].test(prop) ||\n                        typeof val === 'function');\n                }\n                if (!denylisted) {\n                    // If parent node has the same style, it gets inherited, no\n                    // need to inline it. Top-level props should be diffed\n                    // against parent (#7687).\n                    if ((parentStyles[prop] !== val ||\n                        node.nodeName === 'svg') &&\n                        (defaultStyles[node.nodeName])[prop] !== val) {\n                        // Attributes\n                        if (!Exporting.inlineToAttributes ||\n                            Exporting.inlineToAttributes.indexOf(prop) !== -1) {\n                            if (val) {\n                                node.setAttribute(Exporting.hyphenate(prop), val);\n                            }\n                            // Styles\n                        }\n                        else {\n                            filteredStyles[prop] = val;\n                        }\n                    }\n                }\n            }\n            if (iframeDoc &&\n                node.nodeType === 1 &&\n                Exporting.unstyledElements.indexOf(node.nodeName) === -1) {\n                styles =\n                    Exporting_win.getComputedStyle(node, null);\n                parentStyles = node.nodeName === 'svg' ?\n                    {} :\n                    Exporting_win.getComputedStyle(node.parentNode, null);\n                // Get default styles from the browser so that we don't have to\n                // add these\n                if (!defaultStyles[node.nodeName]) {\n                    /*\n                    If (!dummySVG) {\n                        dummySVG = doc.createElementNS(H.SVG_NS, 'svg');\n                        dummySVG.setAttribute('version', '1.1');\n                        doc.body.appendChild(dummySVG);\n                    }\n                    */\n                    dummySVG =\n                        iframeDoc.getElementsByTagName('svg')[0];\n                    dummy = iframeDoc.createElementNS(node.namespaceURI, node.nodeName);\n                    dummySVG.appendChild(dummy);\n                    // Get the defaults into a standard object (simple merge\n                    // won't do)\n                    const s = Exporting_win.getComputedStyle(dummy, null), defaults = {};\n                    for (const key in s) {\n                        if (key.length < 1000 /* RegexLimits.shortLimit */ &&\n                            typeof s[key] === 'string' &&\n                            !/^\\d+$/.test(key)) {\n                            defaults[key] = s[key];\n                        }\n                    }\n                    defaultStyles[node.nodeName] = defaults;\n                    // Remove default fill, otherwise text disappears when\n                    // exported\n                    if (node.nodeName === 'text') {\n                        delete defaultStyles.text.fill;\n                    }\n                    dummySVG.removeChild(dummy);\n                }\n                // Loop through all styles and add them inline if they are ok\n                for (const p in styles) {\n                    if (\n                    // Some browsers put lots of styles on the prototype...\n                    isFirefox ||\n                        isMS ||\n                        Exporting_isSafari || // #16902\n                        // ... Chrome puts them on the instance\n                        Object.hasOwnProperty.call(styles, p)) {\n                        filterStyles(styles[p], p);\n                    }\n                }\n                // Apply styles\n                css(node, filteredStyles);\n                // Set default stroke width (needed at least for IE)\n                if (node.nodeName === 'svg') {\n                    node.setAttribute('stroke-width', '1px');\n                }\n                if (node.nodeName === 'text') {\n                    return;\n                }\n                // Recurse\n                [].forEach.call(node.children || node.childNodes, recurse);\n            }\n        }\n        /**\n         * Remove the dummy objects used to get defaults.\n         *\n         * @private\n         * @function tearDown\n         */\n        function tearDown() {\n            dummySVG.parentNode.removeChild(dummySVG);\n            // Remove trash from DOM that stayed after each exporting\n            iframe.parentNode.removeChild(iframe);\n        }\n        recurse(this.chart.container.querySelector('svg'));\n        tearDown();\n    }\n    /**\n     * Get SVG of chart prepared for client side export. This converts embedded\n     * images in the SVG to data URIs. It requires the regular exporting module.\n     * The options and chartOptions arguments are passed to the getSVGForExport\n     * function.\n     *\n     * @private\n     * @async\n     * @function Highcharts.Exporting#localExport\n     *\n     * @param {Highcharts.ExportingOptions} exportingOptions\n     * The exporting options.\n     * @param {Highcharts.Options} chartOptions\n     * Additional chart options for the exported chart.\n     *\n     * @return {Promise<string>}\n     * The sanitized SVG.\n     *\n     * @requires modules/exporting\n     */\n    async localExport(exportingOptions, chartOptions) {\n        const chart = this.chart, exporting = this, \n        // After grabbing the SVG of the chart's copy container we need\n        // to do sanitation on the SVG\n        sanitize = (svg) => Exporting.sanitizeSVG(svg || '', chartCopyOptions), \n        // Return true if the SVG contains images with external data.\n        // With the boost module there are `image` elements with encoded\n        // PNGs, these are supported by svg2pdf and should pass (#10243)\n        hasExternalImages = function () {\n            return [].some.call(chart.container.getElementsByTagName('image'), function (image) {\n                const href = image.getAttribute('href');\n                return (href !== '' &&\n                    typeof href === 'string' &&\n                    href.indexOf('data:') !== 0);\n            });\n        };\n        let chartCopyContainer, chartCopyOptions, href = null, images;\n        // If we are on IE and in styled mode, add an allowlist to the\n        // renderer for inline styles that we want to pass through. There\n        // are so many styles by default in IE that we don't want to\n        // denylist them all\n        if (isMS && chart.styledMode && !Exporting.inlineAllowlist.length) {\n            Exporting.inlineAllowlist.push(/^blockSize/, /^border/, /^caretColor/, /^color/, /^columnRule/, /^columnRuleColor/, /^cssFloat/, /^cursor/, /^fill$/, /^fillOpacity/, /^font/, /^inlineSize/, /^length/, /^lineHeight/, /^opacity/, /^outline/, /^parentRule/, /^rx$/, /^ry$/, /^stroke/, /^textAlign/, /^textAnchor/, /^textDecoration/, /^transform/, /^vectorEffect/, /^visibility/, /^x$/, /^y$/);\n        }\n        // Always fall back on:\n        // - MS browsers: Embedded images JPEG/PNG, or any PDF\n        // - Embedded images and PDF\n        if ((isMS &&\n            (exportingOptions.type === 'application/pdf' ||\n                chart.container.getElementsByTagName('image').length &&\n                    exportingOptions.type !== 'image/svg+xml')) || (exportingOptions.type === 'application/pdf' &&\n            hasExternalImages())) {\n            await this.fallbackToServer(exportingOptions, new Error('Image type not supported for this chart/browser.'));\n            return;\n        }\n        // Hook into getSVG to get a copy of the chart copy's container (#8273)\n        const unbindGetSVG = Exporting_addEvent(chart, 'getSVG', (e) => {\n            chartCopyOptions = e.chartCopy.options;\n            chartCopyContainer =\n                e.chartCopy.container.cloneNode(true);\n            images = chartCopyContainer && chartCopyContainer\n                .getElementsByTagName('image') || [];\n        });\n        try {\n            // Trigger hook to get chart copy\n            this.getSVGForExport(exportingOptions, chartOptions);\n            // Get the static array\n            const imagesArray = images ? Array.from(images) : [];\n            // Go through the images we want to embed\n            for (const image of imagesArray) {\n                href = image.getAttributeNS('http://www.w3.org/1999/xlink', 'href');\n                if (href) {\n                    Exporting.objectURLRevoke = false;\n                    const dataURL = await Exporting.imageToDataURL(href, exportingOptions?.scale || 1, exportingOptions?.type || 'image/png');\n                    // Change image href in chart copy\n                    image.setAttributeNS('http://www.w3.org/1999/xlink', 'href', dataURL);\n                    // Hidden, boosted series have blank href (#10243)\n                }\n                else {\n                    image.parentNode.removeChild(image);\n                }\n            }\n            // Sanitize the SVG\n            const sanitizedSVG = sanitize(chartCopyContainer?.innerHTML);\n            // Use SVG of chart copy. If SVG contains foreignObjects PDF fails\n            // in all browsers and all exports except SVG will fail in IE, as\n            // both CanVG and svg2pdf choke on this. Gracefully fall back.\n            if (sanitizedSVG.indexOf('<foreignObject') > -1 &&\n                exportingOptions.type !== 'image/svg+xml' &&\n                (isMS ||\n                    exportingOptions.type === 'application/pdf')) {\n                throw new Error('Image type not supported for charts with embedded HTML');\n            }\n            else {\n                // Trigger SVG download\n                await exporting.downloadSVG(sanitizedSVG, extend({ filename: exporting.getFilename() }, exportingOptions));\n            }\n            // Return the sanitized SVG\n            return sanitizedSVG;\n        }\n        catch (error) {\n            await this.fallbackToServer(exportingOptions, error);\n        }\n        finally {\n            // Clean up\n            unbindGetSVG();\n        }\n    }\n    /**\n     * Move the chart container(s) to another div.\n     *\n     * @private\n     * @function Highcharts.Exporting#moveContainers\n     *\n     * @param {Highcharts.HTMLDOMElement} moveTo\n     * Move target.\n     *\n     * @requires modules/exporting\n     */\n    moveContainers(moveTo) {\n        const chart = this.chart, { scrollablePlotArea } = chart;\n        (\n        // When scrollablePlotArea is active (#9533)\n        scrollablePlotArea ?\n            [\n                scrollablePlotArea.fixedDiv,\n                scrollablePlotArea.scrollingContainer\n            ] :\n            [chart.container]).forEach(function (div) {\n            moveTo.appendChild(div);\n        });\n    }\n    /**\n     * Clears away other elements in the page and prints the chart as it is\n     * displayed. By default, when the exporting module is enabled, a context\n     * button with a drop down menu in the upper right corner accesses this\n     * function.\n     *\n     * @sample highcharts/members/chart-print/\n     * Print from a HTML button\n     *\n     * @function Highcharts.Exporting#print\n     *\n     * @emits Highcharts.Chart#event:beforePrint\n     * @emits Highcharts.Chart#event:afterPrint\n     *\n     * @requires modules/exporting\n     */\n    print() {\n        const chart = this.chart;\n        // Block the button while in printing mode\n        if (this.isPrinting) {\n            return;\n        }\n        Exporting.printingChart = chart;\n        if (!Exporting_isSafari) {\n            this.beforePrint();\n        }\n        // Give the browser time to draw WebGL content, an issue that randomly\n        // appears (at least) in Chrome ~67 on the Mac (#8708).\n        setTimeout(() => {\n            Exporting_win.focus(); // #1510\n            Exporting_win.print();\n            // Allow the browser to prepare before reverting\n            if (!Exporting_isSafari) {\n                setTimeout(() => {\n                    chart.exporting?.afterPrint();\n                }, 1000);\n            }\n        }, 1);\n    }\n    /**\n     * Add the buttons on chart load.\n     *\n     * @private\n     * @function Highcharts.Exporting#render\n     *\n     * @requires modules/exporting\n     */\n    render() {\n        const exporting = this, { chart, options } = exporting, isDirty = exporting?.isDirty || !exporting?.svgElements.length;\n        exporting.buttonOffset = 0;\n        if (exporting.isDirty) {\n            exporting.destroy();\n        }\n        if (isDirty && options.enabled !== false) {\n            exporting.events = [];\n            exporting.group || (exporting.group = chart.renderer.g('exporting-group').attr({\n                zIndex: 3 // #4955, // #8392\n            }).add());\n            Exporting_objectEach(options?.buttons, function (button) {\n                exporting.addButton(button);\n            });\n            exporting.isDirty = false;\n        }\n    }\n    /**\n     * Resolve CSS variables into hex colors.\n     *\n     * @private\n     * @function Highcharts.Exporting#resolveCSSVariables\n     *\n     * @requires modules/exporting\n     */\n    resolveCSSVariables() {\n        Array.from(this.chart.container.querySelectorAll('*')).forEach((element) => {\n            ['color', 'fill', 'stop-color', 'stroke'].forEach((prop) => {\n                const attrValue = element.getAttribute(prop);\n                if (attrValue?.includes('var(')) {\n                    element.setAttribute(prop, getComputedStyle(element).getPropertyValue(prop));\n                }\n                const styleValue = element.style?.[prop];\n                if (styleValue?.includes('var(')) {\n                    element.style[prop] =\n                        getComputedStyle(element).getPropertyValue(prop);\n                }\n            });\n        });\n    }\n    /**\n     * Updates the exporting object with the provided exporting options.\n     *\n     * @private\n     * @function Highcharts.Exporting#update\n     *\n     * @param {Highcharts.ExportingOptions} exportingOptions\n     * The exporting options to update with.\n     * @param {boolean} [redraw=true]\n     * Whether to redraw or not.\n     *\n     * @requires modules/exporting\n     */\n    update(exportingOptions, redraw) {\n        this.isDirty = true;\n        merge(true, this.options, exportingOptions);\n        if (pick(redraw, true)) {\n            this.chart.redraw();\n        }\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nExporting.inlineAllowlist = [];\n// These CSS properties are not inlined. Remember camelCase.\nExporting.inlineDenylist = [\n    /-/, // In Firefox, both hyphened and camelCased names are listed\n    /^(clipPath|cssText|d|height|width)$/, // Full words\n    /^font$/, // More specific props are set\n    /[lL]ogical(Width|Height)$/,\n    /^parentRule$/,\n    /^(cssRules|ownerRules)$/, // #19516 read-only properties\n    /perspective/,\n    /TapHighlightColor/,\n    /^transition/,\n    /^length$/, // #7700\n    /^\\d+$/ // #17538\n];\n// These ones are translated to attributes rather than styles\nExporting.inlineToAttributes = [\n    'fill',\n    'stroke',\n    'strokeLinecap',\n    'strokeLinejoin',\n    'strokeWidth',\n    'textAnchor',\n    'x',\n    'y'\n];\n// Milliseconds to defer image load event handlers to offset IE bug\nExporting.loadEventDeferDelay = isMS ? 150 : 0;\nExporting.unstyledElements = [\n    'clipPath',\n    'defs',\n    'desc'\n];\n/* *\n *\n *  Class Namespace\n *\n * */\n(function (Exporting) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Composition function.\n     *\n     * @private\n     * @function Highcharts.Exporting#compose\n     *\n     * @param {ChartClass} ChartClass\n     * Chart class.\n     * @param {SVGRendererClass} SVGRendererClass\n     * SVGRenderer class.\n     *\n     * @requires modules/exporting\n     */\n    function compose(ChartClass, SVGRendererClass) {\n        Exporting_ExportingSymbols.compose(SVGRendererClass);\n        Exporting_Fullscreen.compose(ChartClass);\n        // Check the composition registry for the Exporting\n        if (!Exporting_pushUnique(Exporting_composed, 'Exporting')) {\n            return;\n        }\n        // Adding wrappers for the deprecated functions\n        extend((highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default()).prototype, {\n            exportChart: async function (exportingOptions, chartOptions) {\n                await this.exporting?.exportChart(exportingOptions, chartOptions);\n                return;\n            },\n            getChartHTML: function (applyStyleSheets) {\n                return this.exporting?.getChartHTML(applyStyleSheets);\n            },\n            getFilename: function () {\n                return this.exporting?.getFilename();\n            },\n            getSVG: function (chartOptions) {\n                return this.exporting?.getSVG(chartOptions);\n            },\n            print: function () {\n                return this.exporting?.print();\n            }\n        });\n        ChartClass.prototype.callbacks.push(chartCallback);\n        Exporting_addEvent(ChartClass, 'afterInit', onChartAfterInit);\n        Exporting_addEvent(ChartClass, 'layOutTitle', onChartLayOutTitle);\n        if (Exporting_isSafari) {\n            Exporting_win.matchMedia('print').addListener(function (mqlEvent) {\n                if (!Exporting.printingChart) {\n                    return void 0;\n                }\n                if (mqlEvent.matches) {\n                    Exporting.printingChart.exporting?.beforePrint();\n                }\n                else {\n                    Exporting.printingChart.exporting?.afterPrint();\n                }\n            });\n        }\n        // Update with defaults of the exporting module\n        setOptions(Exporting_ExportingDefaults);\n    }\n    Exporting.compose = compose;\n    /**\n     * Function that is added to the callbacks array that runs on chart load.\n     *\n     * @private\n     * @function Highcharts#chartCallback\n     *\n     * @param {Highcharts.Chart} chart\n     * The chart instance.\n     *\n     * @requires modules/exporting\n     */\n    function chartCallback(chart) {\n        const exporting = chart.exporting;\n        if (exporting) {\n            exporting.render();\n            // Add the exporting buttons on each chart redraw\n            Exporting_addEvent(chart, 'redraw', function () {\n                this.exporting?.render();\n            });\n            // Destroy the export elements at chart destroy\n            Exporting_addEvent(chart, 'destroy', function () {\n                this.exporting?.destroy();\n            });\n        }\n        // Uncomment this to see a button directly below the chart, for quick\n        // testing of export\n        // let button, viewImage, viewSource;\n        // if (!chart.renderer.forExport) {\n        //     viewImage = function (): void {\n        //         const div = doc.createElement('div');\n        //         div.innerHTML = chart.exporting?.getSVGForExport() || '';\n        //         chart.renderTo.parentNode.appendChild(div);\n        //     };\n        //     viewSource = function (): void {\n        //         const pre = doc.createElement('pre');\n        //         pre.innerHTML = chart.exporting?.getSVGForExport()\n        //             .replace(/</g, '\\n&lt;')\n        //             .replace(/>/g, '&gt;') || '';\n        //         chart.renderTo.parentNode.appendChild(pre);\n        //     };\n        //     viewImage();\n        //     // View SVG Image\n        //     button = doc.createElement('button');\n        //     button.innerHTML = 'View SVG Image';\n        //     chart.renderTo.parentNode.appendChild(button);\n        //     button.onclick = viewImage;\n        //     // View SVG Source\n        //     button = doc.createElement('button');\n        //     button.innerHTML = 'View SVG Source';\n        //     chart.renderTo.parentNode.appendChild(button);\n        //     button.onclick = viewSource;\n        // }\n    }\n    /**\n     * Add update methods to handle chart.update and chart.exporting.update and\n     * chart.navigation.update. These must be added to the chart instance rather\n     * than the Chart prototype in order to use the chart instance inside the\n     * update function.\n     *\n     * @private\n     * @function Highcharts#onChartAfterInit\n     *\n     * @requires modules/exporting\n     */\n    function onChartAfterInit() {\n        const chart = this;\n        // Create the exporting instance\n        if (chart.options.exporting) {\n            /**\n             * Exporting object.\n             *\n             * @name Highcharts.Chart#exporting\n             * @type {Highcharts.Exporting}\n             */\n            chart.exporting = new Exporting(chart, chart.options.exporting);\n            // Register update() method for navigation. Cannot be set the same\n            // way as for exporting, because navigation options are shared with\n            // bindings which has separate update() logic.\n            Chart_ChartNavigationComposition\n                .compose(chart).navigation\n                .addUpdate((options, redraw) => {\n                if (chart.exporting) {\n                    chart.exporting.isDirty = true;\n                    merge(true, chart.options.navigation, options);\n                    if (pick(redraw, true)) {\n                        chart.redraw();\n                    }\n                }\n            });\n        }\n    }\n    /**\n     * On layout of titles (title, subtitle and caption), adjust the `alignTo`\n     * box to avoid the context menu button.\n     *\n     * @private\n     * @function Highcharts#onChartLayOutTitle\n     *\n     * @requires modules/exporting\n     */\n    function onChartLayOutTitle({ alignTo, key, textPxLength }) {\n        const exportingOptions = this.options.exporting, { align, buttonSpacing = 0, verticalAlign, width = 0 } = merge(this.options.navigation?.buttonOptions, exportingOptions?.buttons?.contextButton), space = alignTo.width - textPxLength, widthAdjust = width + buttonSpacing;\n        if ((exportingOptions?.enabled ?? true) &&\n            key === 'title' &&\n            align === 'right' &&\n            verticalAlign === 'top') {\n            if (space < 2 * widthAdjust) {\n                if (space < widthAdjust) {\n                    alignTo.width -= widthAdjust;\n                }\n                else if (this.title?.alignValue !== 'left') {\n                    alignTo.x -= widthAdjust - space / 2;\n                }\n            }\n        }\n    }\n})(Exporting || (Exporting = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Exporting_Exporting = (Exporting);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Gets fired after a chart is printed through the context menu item or the\n * Chart.print method.\n *\n * @callback Highcharts.ExportingAfterPrintCallbackFunction\n *\n * @param {Highcharts.Chart} this\n * The chart on which the event occurred.\n * @param {global.Event} event\n * The event that occurred.\n */\n/**\n * Gets fired before a chart is printed through the context menu item or the\n * Chart.print method.\n *\n * @callback Highcharts.ExportingBeforePrintCallbackFunction\n *\n * @param {Highcharts.Chart} this\n * The chart on which the event occurred.\n * @param {global.Event} event\n * The event that occurred.\n */\n/**\n * Function to call if the offline-exporting module fails to export a chart on\n * the client side.\n *\n * @callback Highcharts.ExportingErrorCallbackFunction\n *\n * @param {Highcharts.ExportingOptions} options\n * The exporting options.\n * @param {global.Error} err\n * The error from the module.\n */\n/**\n * Definition for a menu item in the context menu.\n *\n * @interface Highcharts.ExportingMenuObject\n */ /**\n* The text for the menu item.\n*\n* @name Highcharts.ExportingMenuObject#text\n* @type {string | undefined}\n*/ /**\n* If internationalization is required, the key to a language string.\n*\n* @name Highcharts.ExportingMenuObject#textKey\n* @type {string | undefined}\n*/ /**\n* The click handler for the menu item.\n*\n* @name Highcharts.ExportingMenuObject#onclick\n* @type {Highcharts.EventCallbackFunction<Highcharts.Chart> | undefined}\n*/ /**\n* Indicates a separator line instead of an item.\n*\n* @name Highcharts.ExportingMenuObject#separator\n* @type {boolean | undefined}\n*/\n/**\n * Possible MIME types for exporting.\n *\n * @typedef {\"image/png\" | \"image/jpeg\" | \"application/pdf\" | \"image/svg+xml\"} Highcharts.ExportingMimeTypeValue\n */\n(''); // Keeps doclets above in transpiled file\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Fires after a chart is printed through the context menu item or the\n * `Chart.print` method.\n *\n * @sample highcharts/chart/events-beforeprint-afterprint/\n * Rescale the chart to print\n *\n * @type {Highcharts.ExportingAfterPrintCallbackFunction}\n * @since 4.1.0\n * @context Highcharts.Chart\n * @requires modules/exporting\n * @apioption chart.events.afterPrint\n */\n/**\n * Fires before a chart is printed through the context menu item or\n * the `Chart.print` method.\n *\n * @sample highcharts/chart/events-beforeprint-afterprint/\n * Rescale the chart to print\n *\n * @type {Highcharts.ExportingBeforePrintCallbackFunction}\n * @since 4.1.0\n * @context Highcharts.Chart\n * @requires modules/exporting\n * @apioption chart.events.beforePrint\n */\n(''); // Keeps doclets above in transpiled file\n\n;// ./code/es-modules/masters/modules/exporting.js\n\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n// Class\nG.Exporting = Exporting_Exporting;\n// Compatibility\nG.HttpUtilities = G.HttpUtilities || Core_HttpUtilities;\nG.ajax = G.HttpUtilities.ajax;\nG.getJSON = G.HttpUtilities.getJSON;\nG.post = G.HttpUtilities.post;\n// Compose\nExporting_Exporting.compose(G.Chart, G.Renderer);\n/* harmony default export */ const exporting_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__660__", "__WEBPACK_EXTERNAL_MODULE__960__", "ChartNavigationComposition", "ExportingSymbols", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "exporting_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_", "highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default", "highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_", "highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default", "compose", "chart", "navigation", "Additions", "constructor", "updates", "addUpdate", "updateFn", "push", "update", "options", "redraw", "for<PERSON>ach", "Chart_ChartNavigationComposition", "<PERSON><PERSON><PERSON><PERSON>", "win", "document", "doc", "error", "domurl", "URL", "webkitURL", "dataURLtoBlob", "dataURL", "parts", "replace", "match", "length", "atob", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Uint8Array", "Blob", "createObjectURL", "binStr", "buf", "binary", "i", "charCodeAt", "isTouchDevice", "Exporting_ExportingDefaults", "exporting", "allowTableSorting", "libURL", "local", "type", "url", "version", "pdfFont", "normal", "bold", "bolditalic", "italic", "printMaxWidth", "scale", "buttons", "contextButton", "className", "menuClassName", "symbol", "<PERSON><PERSON><PERSON>", "menuItems", "menuItemDefinitions", "viewFullscreen", "<PERSON><PERSON><PERSON>", "onclick", "fullscreen", "toggle", "printChart", "print", "separator", "downloadPNG", "exportChart", "downloadJPEG", "downloadPDF", "downloadSVG", "lang", "exitFullscreen", "contextButtonTitle", "buttonOptions", "symbolSize", "symbolX", "symbolY", "align", "buttonSpacing", "height", "y", "verticalAlign", "width", "symbolFill", "symbolStroke", "symbolStrokeWidth", "theme", "fill", "padding", "stroke", "menuStyle", "border", "borderRadius", "background", "menuItemStyle", "color", "fontSize", "transition", "menuItemHoverStyle", "modifiedClasses", "menu", "x", "menuball", "h", "path", "concat", "circle", "SVGRendererClass", "indexOf", "symbols", "bind", "Exporting_ExportingSymbols", "composed", "addEvent", "fireEvent", "pushUnique", "onChartBeforeRender", "Fullscreen", "ChartClass", "isOpen", "container", "renderTo", "browserProps", "requestFullscreen", "fullscreenChange", "mozRequestFullScreen", "webkitRequestFullScreen", "msRequestFullscreen", "close", "optionsChart", "ownerDocument", "Document", "unbindFullscreenEvent", "setSize", "origWidth", "origHeight", "origWidthOption", "origHeightOption", "setButtonText", "open", "chartWidth", "chartHeight", "unbind<PERSON>hange", "unbind<PERSON><PERSON><PERSON>", "promise", "alert", "exportDivElements", "divElements", "exportingOptions", "exportDivElement", "setElementHTML", "text", "HttpUtilities_win", "discardElement", "objectEach", "HttpUtilities", "ajax", "settings", "headers", "json", "xml", "octet", "r", "XMLHttpRequest", "handleError", "xhr", "err", "toUpperCase", "setRequestHeader", "dataType", "val", "responseType", "onreadystatechange", "res", "readyState", "status", "responseText", "JSON", "parse", "e", "Error", "success", "data", "stringify", "send", "getJSON", "post", "fetchOptions", "formData", "FormData", "value", "name", "append", "response", "fetch", "method", "body", "ok", "link", "createElement", "href", "download", "filename", "click", "defaultOptions", "setOptions", "downloadURL", "Exporting_downloadURL", "getScript", "Exporting_getScript", "nav", "navigator", "String", "msSaveOrOpenBlob", "userAgent", "isOldEdgeBrowser", "test", "safariBlob", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "location", "scriptLocation", "Promise", "resolve", "reject", "head", "getElementsByTagName", "script", "src", "onload", "onerror", "Exporting_composed", "Exporting_doc", "isFirefox", "isMS", "Exporting_isSafari", "SVG_NS", "Exporting_win", "Exporting_addEvent", "clearTimeout", "Exporting_clearTimeout", "css", "Exporting_discardElement", "Exporting_error", "extend", "find", "Exporting_fireEvent", "isObject", "merge", "Exporting_objectEach", "pick", "Exporting_pushUnique", "removeEvent", "splat", "<PERSON><PERSON><PERSON>", "allowedAttributes", "allowedTags", "Exporting_domurl", "Exporting", "btnCount", "buttonOffset", "svgElements", "hyphenate", "property", "toLowerCase", "imageToDataURL", "imageURL", "imageType", "img", "loadImage", "canvas", "ctx", "getContext", "drawImage", "toDataURL", "image", "Image", "crossOrigin", "setTimeout", "loadEventDeferDelay", "prepareImageOptions", "split", "slice", "sanitizeSVG", "svg", "useForeignObject", "html", "substr", "allowHTML", "svgToDataURL", "webKit", "encodeURIComponent", "addButton", "renderer", "btnOptions", "enabled", "styledMode", "callback", "stopPropagation", "contextMenu", "button", "translateX", "translateY", "setState", "paddingLeft", "useHTML", "addClass", "attr", "title", "_title<PERSON>ey", "Math", "round", "zIndex", "add", "group", "after<PERSON><PERSON>t", "printReverseInfo", "childNodes", "origDisplay", "resetParams", "moveContainers", "node", "nodeType", "style", "display", "isPrinting", "apply", "printingChart", "beforePrint", "pointer", "reset", "items", "navOptions", "cacheName", "menuPadding", "max", "innerMenu", "contextMenuEl", "position", "pointerEvents", "scrollablePlotArea", "fixedDiv", "listStyle", "margin", "MozBoxShadow", "WebkitBoxShadow", "boxShadow", "hideMenu", "openMenu", "overflow", "hide<PERSON><PERSON>r", "events", "inClass", "target", "item", "element", "isDataTableVisible", "arguments", "on<PERSON><PERSON>ver", "onmouseout", "cursor", "menuHeight", "offsetHeight", "menuWidth", "offsetWidth", "right", "left", "alignOptions", "bottom", "top", "destroy", "elem", "ontouchstart", "unbind", "svgURL", "eventArgs", "defaultPrevented", "MSBlobBuilder", "blob", "getBlob", "objectURLRevoke", "message", "matchedImageWidth", "matchedImageHeight", "imageWidth", "imageHeight", "canvg", "v", "Canvg", "fromString", "start", "msToBlob", "revokeObjectURL", "chartOptions", "localExport", "getSVGForExport", "Core_HttpUtilities", "getFilename", "fallbackToServer", "fallbackToExportServer", "getChartHTML", "applyStyleSheets", "inlineStyles", "resolveCSSVariables", "innerHTML", "titleText", "userOptions", "getSVG", "seriesOptions", "plotOptions", "time", "sandbox", "cssWidth", "cssHeight", "sourceWidth", "parseInt", "isGantt", "sourceHeight", "animation", "forExport", "series", "serie", "enableMouseTracking", "showCheckbox", "visible", "isInternal", "colls", "axes", "axis", "internalKey", "coll", "uniqueNames", "colorAxis", "chartCopy", "axisCopy", "copy", "extremes", "getExtremes", "exportOverride", "userMin", "min", "userMax", "setExtremes", "currentExportingOptions", "dummySVG", "denylist", "inlineDenylist", "allowlist", "inlineAllowlist", "defaultStyles", "iframe", "visibility", "iframeDoc", "contentWindow", "createElementNS", "recurse", "styles", "parentStyles", "dummy", "denylisted", "allowlisted", "filteredStyles", "unstyledElements", "nodeName", "getComputedStyle", "parentNode", "namespaceURI", "s", "defaults", "p", "filterStyles", "inlineToAttributes", "setAttribute", "children", "querySelector", "chartCopyContainer", "chartCopyOptions", "images", "some", "getAttribute", "unbindGetSVG", "cloneNode", "Array", "from", "getAttributeNS", "setAttributeNS", "sanitizedSVG", "moveTo", "scrollingContainer", "div", "focus", "render", "isDirty", "g", "querySelectorAll", "attrValue", "includes", "getPropertyValue", "styleValue", "chartCallback", "onChartAfterInit", "onChartLayOutTitle", "alignTo", "textPxLength", "space", "widthAdjust", "alignValue", "Exporting_Fullscreen", "callbacks", "matchMedia", "addListener", "mqlEvent", "matches", "Exporting_Exporting", "G", "Chart", "<PERSON><PERSON><PERSON>"], "mappings": "CAWA,AAXA;;;;;;;;;;CAUC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,GAAM,CAAEA,EAAK,WAAc,CAAC,KAAQ,EAC/F,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,+BAAgC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,GAAM,CAACA,EAAK,KAAQ,CAAE,GAC3H,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,+BAA+B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,GAAM,CAAEA,EAAK,WAAc,CAAC,KAAQ,EAE/HA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,GAAM,CAAEA,EAAK,UAAa,CAAC,KAAQ,CACzG,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,IACvG,AAAC,CAAA,KACP,aACA,IAgHNC,EAmoCAC,EAnvCUC,EAAuB,CAE/B,IACC,AAACV,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,AAACL,IAERA,EAAOD,OAAO,CAAGQ,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaf,OAAO,CAG5B,IAAIC,EAASW,CAAwB,CAACE,EAAS,CAAG,CAGjDd,QAAS,CAAC,CACX,EAMA,OAHAW,CAAmB,CAACG,EAAS,CAACb,EAAQA,EAAOD,OAAO,CAAEa,GAG/CZ,EAAOD,OAAO,AACtB,CAMCa,EAAoBI,CAAC,CAAG,AAAChB,IACxB,IAAIiB,EAASjB,GAAUA,EAAOkB,UAAU,CACvC,IAAOlB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAY,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACpB,EAASsB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACxB,EAASuB,IAC5EE,OAAOC,cAAc,CAAC1B,EAASuB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,EAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAuFzB,EAAoB,KAC3G0B,EAA2G1B,EAAoBI,CAAC,CAACqB,GAEjIE,EAA+F3B,EAAoB,KACnH4B,EAAmH5B,EAAoBI,CAAC,CAACuB,IAkB7I,AAAC,SAAU/B,CAA0B,EAqBjCA,EAA2BiC,OAAO,CANlC,SAAiBC,CAAK,EAIlB,OAHI,AAACA,EAAMC,UAAU,EACjBD,CAAAA,EAAMC,UAAU,CAAG,IAAIC,EAAUF,EAAK,EAEnCA,CACX,CAYA,OAAME,EAMFC,YAAYH,CAAK,CAAE,CACf,IAAI,CAACI,OAAO,CAAG,EAAE,CACjB,IAAI,CAACJ,KAAK,CAAGA,CACjB,CAaAK,UAAUC,CAAQ,CAAE,CAChB,IAAI,CAACN,KAAK,CAACC,UAAU,CAACG,OAAO,CAACG,IAAI,CAACD,EACvC,CAIAE,OAAOC,CAAO,CAAEC,CAAM,CAAE,CACpB,IAAI,CAACN,OAAO,CAACO,OAAO,CAAC,AAACL,IAClBA,EAAShB,IAAI,CAAC,IAAI,CAACU,KAAK,CAAES,EAASC,EACvC,EACJ,CACJ,CACA5C,EAA2BoC,SAAS,CAAGA,CAC3C,EAAGpC,GAA+BA,CAAAA,EAA6B,CAAC,CAAA,GAMnC,IAAM8C,EAAoC9C,EAqBjE,CAAE+C,SAAAA,CAAQ,CAAEC,IAAAA,CAAG,CAAEA,IAAK,CAAEC,SAAUC,CAAG,CAAE,CAAE,CAAItB,IAE7C,CAAEuB,MAAAA,CAAK,CAAE,CAAIvB,IAMbwB,EAASJ,EAAIK,GAAG,EAAIL,EAAIM,SAAS,EAAIN,EAkB3C,SAASO,EAAcC,CAAO,EAC1B,IAAMC,EAAQD,EACTE,OAAO,CAAC,eAAgB,IACxBC,KAAK,CAAC,yCACX,GAAIF,GACAA,EAAMG,MAAM,CAAG,GACdZ,EAAIa,IAAI,EACTb,EAAIc,WAAW,EACfd,EAAIe,UAAU,EACdf,EAAIgB,IAAI,EACPZ,EAAOa,eAAe,CAAG,CAE1B,IAAMC,EAASlB,EAAIa,IAAI,CAACJ,CAAK,CAAC,EAAE,EAAGU,EAAM,IAAInB,EAAIc,WAAW,CAACI,EAAON,MAAM,EAAGQ,EAAS,IAAIpB,EAAIe,UAAU,CAACI,GACzG,IAAK,IAAIE,EAAI,EAAGA,EAAID,EAAOR,MAAM,CAAE,EAAES,EACjCD,CAAM,CAACC,EAAE,CAAGH,EAAOI,UAAU,CAACD,GAElC,OAAOjB,EACFa,eAAe,CAAC,IAAIjB,EAAIgB,IAAI,CAAC,CAACI,EAAO,CAAE,CAAE,KAAQX,CAAK,CAAC,EAAE,AAAC,GACnE,CACJ,CA+GA,GAAM,CAAEc,cAAAA,CAAa,CAAE,CAAI3C,IAo3BQ4C,EALT,CACtBC,UAj2Bc,CAwBdC,kBAAmB,CAAA,EAoInBC,OAAQ,0CAoBRC,MAAO,CAAA,EAgDPC,KAAM,YAONC,IAAK,CAAC,oCAAoC,EAAE,AAAClD,IAA+EmD,OAAO,CAAC,CAAC,CAqBrIC,QAAS,CAQLC,OAAQ,KAAK,EAMbC,KAAM,KAAK,EAMXC,WAAY,KAAK,EAMjBC,OAAQ,KAAK,CACjB,EAUAC,cAAe,IAmBfC,MAAO,EAUPC,QAAS,CAWLC,cAAe,CAiCXC,UAAW,2BAIXC,cAAe,yBAgBfC,OAAQ,OASRC,SAAU,qBAkBVC,UAAW,CACP,iBACA,aACA,YACA,cACA,eACA,cACH,AACL,CACJ,EA2BAC,oBAAqB,CAIjBC,eAAgB,CACZC,QAAS,iBACTC,QAAS,WACL,IAAI,CAACC,UAAU,EAAEC,QACrB,CACJ,EAIAC,WAAY,CACRJ,QAAS,aACTC,QAAS,WACL,IAAI,CAACxB,SAAS,EAAE4B,OACpB,CACJ,EAIAC,UAAW,CACPA,UAAW,CAAA,CACf,EAIAC,YAAa,CACTP,QAAS,cACTC,QAAS,iBACL,MAAM,IAAI,CAACxB,SAAS,EAAE+B,aAC1B,CACJ,EAIAC,aAAc,CACVT,QAAS,eACTC,QAAS,iBACL,MAAM,IAAI,CAACxB,SAAS,EAAE+B,YAAY,CAC9B3B,KAAM,YACV,EACJ,CACJ,EAIA6B,YAAa,CACTV,QAAS,cACTC,QAAS,iBACL,MAAM,IAAI,CAACxB,SAAS,EAAE+B,YAAY,CAC9B3B,KAAM,iBACV,EACJ,CACJ,EAIA8B,YAAa,CACTX,QAAS,cACTC,QAAS,iBACL,MAAM,IAAI,CAACxB,SAAS,EAAE+B,YAAY,CAC9B3B,KAAM,eACV,EACJ,CACJ,CACJ,CACJ,EAiWI+B,KA5VS,CAOTb,eAAgB,sBAOhBc,eAAgB,wBAOhBT,WAAY,cAOZG,YAAa,qBAObE,aAAc,sBAOdC,YAAa,wBAObC,YAAa,4BAQbG,mBAAoB,oBACxB,EAmSI3E,WA3Re,CAUf4E,cAAe,CAoBXC,WAAY,GASZC,QAAS,KASTC,QAAS,KAUTC,MAAO,QAUPC,cAAe,EASfC,OAAQ,GAsCRC,EAAG,GAWHC,cAAe,MASfC,MAAO,GAUPC,WAAY,UAUZC,aAAc,UASdC,kBAAmB,EAcnBC,MAAO,CAMHC,KAAM,UAINC,QAAS,EAMTC,OAAQ,OAIR,iBAAkB,OACtB,CACJ,EAeAC,UAAW,CAEPC,OAAQ,OAERC,aAAc,MAEdC,WAAY,UAEZL,QAAS,OACb,EAiBAM,cAAe,CAEXD,WAAY,OAEZD,aAAc,MAEdG,MAAO,UAEPP,QAAS,QAETQ,SAAU/D,EAAgB,QAAU,QAEpCgE,WAAY,+BAChB,EAgBAC,mBAAoB,CAEhBL,WAAY,SAChB,CACJ,CAUA,GAsBA,AAAC,SAAUlI,CAAgB,EAMvB,IAAMwI,EAAkB,EAAE,CAsB1B,SAASC,EAAKC,CAAC,CAAErB,CAAC,CAAEE,CAAK,CAAEH,CAAM,EAS7B,MARY,CACR,CAAC,IAAKsB,EAAGrB,EAAI,IAAI,CACjB,CAAC,IAAKqB,EAAInB,EAAOF,EAAI,IAAI,CACzB,CAAC,IAAKqB,EAAGrB,EAAID,EAAS,EAAI,GAAI,CAC9B,CAAC,IAAKsB,EAAInB,EAAOF,EAAID,EAAS,EAAI,GAAI,CACtC,CAAC,IAAKsB,EAAGrB,EAAID,EAAS,IAAI,CAC1B,CAAC,IAAKsB,EAAInB,EAAOF,EAAID,EAAS,IAAI,CACrC,AAEL,CAIA,SAASuB,EAASD,CAAC,CAAErB,CAAC,CAAEE,CAAK,CAAEH,CAAM,EACjC,IAAMwB,EAAI,AAACxB,EAAS,EAAK,EACrByB,EAAO,EAAE,CAEb,OADOA,EAAKC,MAAM,CAAC,IAAI,CAACC,MAAM,CAACxB,EAAQqB,EAAGvB,EAAGuB,EAAGA,GAAI,IAAI,CAACG,MAAM,CAACxB,EAAQqB,EAAGvB,EAAIuB,EAAI,EAAGA,EAAGA,GAAI,IAAI,CAACG,MAAM,CAACxB,EAAQqB,EAAGvB,EAAI,EAAKuB,CAAAA,EAAI,CAAA,EAAIA,EAAGA,GAE5I,CAvBA5I,EAAiBgC,OAAO,CARxB,SAAiBgH,CAAgB,EAC7B,GAAIR,AAA8C,KAA9CA,EAAgBS,OAAO,CAACD,GAA0B,CAClDR,EAAgBhG,IAAI,CAACwG,GACrB,IAAME,EAAUF,EAAiB3H,SAAS,CAAC6H,OAAO,AAClDA,CAAAA,EAAQT,IAAI,CAAGA,EACfS,EAAQP,QAAQ,CAAGA,EAASQ,IAAI,CAACD,EACrC,CACJ,CAyBJ,EAAGlJ,GAAqBA,CAAAA,EAAmB,CAAC,CAAA,GAMf,IAAMoJ,EAA8BpJ,EAsB3D,CAAEqJ,SAAAA,CAAQ,CAAE,CAAI1H,IAEhB,CAAE2H,SAAAA,CAAQ,CAAEC,UAAAA,CAAS,CAAEC,WAAAA,CAAU,CAAE,CAAI7H,IAS7C,SAAS8H,IAML,IAAI,CAACxD,UAAU,CAAG,IAAIyD,EAAW,IAAI,CACzC,CAgBA,MAAMA,EAYF,OAAO1H,QAAQ2H,CAAU,CAAE,CACnBH,EAAWH,EAAU,eAErBC,EAASK,EAAY,eAAgBF,EAE7C,CAMArH,YAAYH,CAAK,CAAE,CAMf,IAAI,CAACA,KAAK,CAAGA,EASb,IAAI,CAAC2H,MAAM,CAAG,CAAA,EACd,IAAMC,EAAY5H,EAAM6H,QAAQ,AAE5B,EAAC,IAAI,CAACC,YAAY,GACd,AAAuC,YAAvC,OAAOF,EAAUG,iBAAiB,CAClC,IAAI,CAACD,YAAY,CAAG,CAChBE,iBAAkB,mBAClBD,kBAAmB,oBACnBpD,eAAgB,gBACpB,EAEKiD,EAAUK,oBAAoB,CACnC,IAAI,CAACH,YAAY,CAAG,CAChBE,iBAAkB,sBAClBD,kBAAmB,uBACnBpD,eAAgB,qBACpB,EAEKiD,EAAUM,uBAAuB,CACtC,IAAI,CAACJ,YAAY,CAAG,CAChBE,iBAAkB,yBAClBD,kBAAmB,0BACnBpD,eAAgB,sBACpB,EAEKiD,EAAUO,mBAAmB,EAClC,CAAA,IAAI,CAACL,YAAY,CAAG,CAChBE,iBAAkB,qBAClBD,kBAAmB,sBACnBpD,eAAgB,kBACpB,CAAA,EAGZ,CAgBAyD,OAAQ,CACJ,IAAMpE,EAAa,IAAI,CAAEhE,EAAQgE,EAAWhE,KAAK,CAAEqI,EAAerI,EAAMS,OAAO,CAACT,KAAK,CACrFsH,EAAUtH,EAAO,kBAAmB,KAAM,WAGlCgE,EAAW2D,MAAM,EACjB3D,EAAW8D,YAAY,EACvB9H,EAAM4H,SAAS,CAACU,aAAa,YAAYC,UACzCvI,EAAM4H,SAAS,CAACU,aAAa,CAACtE,EAAW8D,YAAY,CAACnD,cAAc,CAAC,GAIrEX,EAAWwE,qBAAqB,EAChCxE,CAAAA,EAAWwE,qBAAqB,CAAGxE,EAC9BwE,qBAAqB,EAAC,EAE/BxI,EAAMyI,OAAO,CAACzE,EAAW0E,SAAS,CAAE1E,EAAW2E,UAAU,CAAE,CAAA,GAC3D3E,EAAW0E,SAAS,CAAG,KAAK,EAC5B1E,EAAW2E,UAAU,CAAG,KAAK,EAC7BN,EAAa/C,KAAK,CAAGtB,EAAW4E,eAAe,CAC/CP,EAAalD,MAAM,CAAGnB,EAAW6E,gBAAgB,CACjD7E,EAAW4E,eAAe,CAAG,KAAK,EAClC5E,EAAW6E,gBAAgB,CAAG,KAAK,EACnC7E,EAAW2D,MAAM,CAAG,CAAA,EACpB3D,EAAW8E,aAAa,EAC5B,EACJ,CAaAC,MAAO,CACH,IAAM/E,EAAa,IAAI,CAAEhE,EAAQgE,EAAWhE,KAAK,CAAEqI,EAAerI,EAAMS,OAAO,CAACT,KAAK,CACrFsH,EAAUtH,EAAO,iBAAkB,KAAM,WAQrC,GAPIqI,IACArE,EAAW4E,eAAe,CAAGP,EAAa/C,KAAK,CAC/CtB,EAAW6E,gBAAgB,CAAGR,EAAalD,MAAM,EAErDnB,EAAW0E,SAAS,CAAG1I,EAAMgJ,UAAU,CACvChF,EAAW2E,UAAU,CAAG3I,EAAMiJ,WAAW,CAErCjF,EAAW8D,YAAY,CAAE,CACzB,IAAMoB,EAAe7B,EAASrH,EAAM4H,SAAS,CAACU,aAAa,CAC3DtE,EAAW8D,YAAY,CAACE,gBAAgB,CAAE,WAGlChE,EAAW2D,MAAM,EACjB3D,EAAW2D,MAAM,CAAG,CAAA,EACpB3D,EAAWoE,KAAK,KAGhBpI,EAAMyI,OAAO,CAAC,KAAM,KAAM,CAAA,GAC1BzE,EAAW2D,MAAM,CAAG,CAAA,EACpB3D,EAAW8E,aAAa,GAEhC,GACMK,EAAgB9B,EAASrH,EAAO,UAAWkJ,EACjDlF,CAAAA,EAAWwE,qBAAqB,CAAG,KAC/BU,IACAC,GACJ,EACA,IAAMC,EAAUpJ,EAAM6H,QAAQ,CAAC7D,EAAW8D,YAAY,CAACC,iBAAiB,CAAC,EACrEqB,CAAAA,GACAA,EAAQ,KAAQ,CAAC,WACbC,MACA,+CACJ,EAER,CACJ,EACJ,CAWAP,eAAgB,CACZ,IAAM9I,EAAQ,IAAI,CAACA,KAAK,CAAEsJ,EAAoBtJ,EAAMuC,SAAS,EAAEgH,YAAaC,EAAmBxJ,EAAMS,OAAO,CAAC8B,SAAS,CAAEoB,EAAa6F,GACjIA,EAAiBnG,OAAO,EACxBmG,EAAiBnG,OAAO,CAACC,aAAa,CAACK,SAAS,CAAGe,EAAO1E,EAAMS,OAAO,CAACiE,IAAI,CAChF,GAAI8E,GACAA,EAAiB5F,mBAAmB,EACpCc,GACAA,EAAKC,cAAc,EACnBD,EAAKb,cAAc,EACnBF,GACA2F,EAAmB,CACnB,IAAMG,EAAmBH,CAAiB,CAAC3F,EAAUqD,OAAO,CAAC,kBAAkB,AAC3EyC,CAAAA,GACA7J,IAA8F8J,cAAc,CAACD,EAAkB,AAAC,IAAI,CAAC9B,MAAM,CAG5GjD,EAAKC,cAAc,CAF7C6E,EAAiB5F,mBAAmB,CAACC,cAAc,CAC/C8F,IAAI,EACLjF,EAAKb,cAAc,CAEnC,CACJ,CAeAI,QAAS,CAEAD,AADc,IAAI,CACP2D,MAAM,CAIlB3D,AALe,IAAI,CAKRoE,KAAK,GAHhBpE,AAFe,IAAI,CAER+E,IAAI,EAKvB,CACJ,CAiFA,GAAM,CAAEjI,IAAK8I,CAAiB,CAAE,CAAIlK,IAE9B,CAAEmK,eAAAA,CAAc,CAAEC,WAAAA,CAAU,CAAE,CAAIpK,IAuJlCqK,EAAgB,CAClBC,KAvIJ,SAAcC,CAAQ,EAClB,IAAMC,EAAU,CACZC,KAAM,mBACNC,IAAK,kBACLT,KAAM,aACNU,MAAO,0BACX,EAAGC,EAAI,IAAIC,eAWX,SAASC,EAAYC,CAAG,CAAEC,CAAG,EACrBT,EAAShJ,KAAK,EACdgJ,EAAShJ,KAAK,CAACwJ,EAAKC,EAK5B,CACA,GAAI,CAACT,EAASrH,GAAG,CACb,MAAO,CAAA,EAEX0H,EAAEvB,IAAI,CAAC,AAACkB,CAAAA,EAAStH,IAAI,EAAI,KAAI,EAAGgI,WAAW,GAAIV,EAASrH,GAAG,CAAE,CAAA,GACzD,AAACqH,EAASC,OAAO,EAAE,CAAC,eAAe,EACnCI,EAAEM,gBAAgB,CAAC,eAAgBV,CAAO,CAACD,EAASY,QAAQ,EAAI,OAAO,EAAIX,EAAQP,IAAI,EAE3FG,EAAWG,EAASC,OAAO,CAAE,SAAUY,CAAG,CAAElM,CAAG,EAC3C0L,EAAEM,gBAAgB,CAAChM,EAAKkM,EAC5B,GACIb,EAASc,YAAY,EACrBT,CAAAA,EAAES,YAAY,CAAGd,EAASc,YAAY,AAAD,EAGzCT,EAAEU,kBAAkB,CAAG,WACnB,IAAIC,EACJ,GAAIX,AAAiB,IAAjBA,EAAEY,UAAU,CAAQ,CACpB,GAAIZ,AAAa,MAAbA,EAAEa,MAAM,CAAU,CAClB,GAAIlB,AAA0B,SAA1BA,EAASc,YAAY,GACrBE,EAAMX,EAAEc,YAAY,CAChBnB,AAAsB,SAAtBA,EAASY,QAAQ,EACjB,GAAI,CACAI,EAAMI,KAAKC,KAAK,CAACL,EACrB,CACA,MAAOM,EAAG,CACN,GAAIA,aAAaC,MACb,OAAOhB,EAAYF,EAAGiB,EAE9B,CAGR,OAAOtB,EAASwB,OAAO,GAAGR,EAAKX,EACnC,CACAE,EAAYF,EAAGA,EAAEc,YAAY,CACjC,CACJ,EACInB,EAASyB,IAAI,EAAI,AAAyB,UAAzB,OAAOzB,EAASyB,IAAI,EACrCzB,CAAAA,EAASyB,IAAI,CAAGL,KAAKM,SAAS,CAAC1B,EAASyB,IAAI,CAAA,EAEhDpB,EAAEsB,IAAI,CAAC3B,EAASyB,IAAI,CACxB,EAuEIG,QA3DJ,SAAiBjJ,CAAG,CAAE6I,CAAO,EACzB1B,EAAcC,IAAI,CAAC,CACfpH,IAAKA,EACL6I,QAASA,EACTZ,SAAU,OACVX,QAAS,CAGL,eAAgB,YACpB,CACJ,EACJ,EAiDI4B,KAnCJ,eAAoBlJ,CAAG,CAAE8I,CAAI,CAAEK,CAAY,EAEvC,IAAMC,EAAW,IAAIpC,EAAkBqC,QAAQ,CAE/CnC,EAAW4B,EAAM,SAAUQ,CAAK,CAAEC,CAAI,EAClCH,EAASI,MAAM,CAACD,EAAMD,EAC1B,GACAF,EAASI,MAAM,CAAC,MAAO,QAEvB,IAAMC,EAAW,MAAMzC,EAAkB0C,KAAK,CAAC1J,EAAK,CAChD2J,OAAQ,OACRC,KAAMR,EACN,GAAGD,CAAY,AACnB,GAEA,GAAIM,EAASI,EAAE,CAAE,CAEb,IAAM9C,EAAO,MAAM0C,EAAS1C,IAAI,GAE1B+C,EAAO3L,SAAS4L,aAAa,CAAC,IACpCD,CAAAA,EAAKE,IAAI,CAAG,CAAC,KAAK,EAAElB,EAAK/I,IAAI,CAAC,QAAQ,EAAEgH,EAAK,CAAC,CAC9C+C,EAAKG,QAAQ,CAAGnB,EAAKoB,QAAQ,CAC7BJ,EAAKK,KAAK,GAEVlD,EAAe6C,EACnB,CACJ,CAUA,EAgEM,CAAEM,eAAAA,CAAc,CAAEC,WAAAA,CAAU,CAAE,CAAIvN,IAElC,CAAEwN,YAAaC,CAAqB,CAAEC,UAAWC,CAAmB,CAAE,CArhDxD,CAChBhM,cAAAA,EACA6L,YAlFJ,SAAqB5L,CAAO,CAAEwL,CAAQ,EAClC,IAAMQ,EAAMxM,EAAIyM,SAAS,CAAE7O,EAAIsC,EAAI2L,aAAa,CAAC,KAGjD,GAAI,AAAmB,UAAnB,OAAOrL,GACP,CAAEA,CAAAA,aAAmBkM,MAAK,GAC1BF,EAAIG,gBAAgB,CAAE,YACtBH,EAAIG,gBAAgB,CAACnM,EAASwL,GAIlC,GADAxL,EAAU,GAAKA,EACXgM,EAAII,SAAS,CAAChM,MAAM,CAAG,IACvB,MAAM,AAAI8J,MAAM,kBAEpB,IAEAmC,EAAmB,YAAYC,IAAI,CAACN,EAAII,SAAS,EAKjD,GAAIG,CAAAA,AAHUhN,GACV,AAAmB,UAAnB,OAAOS,GACPA,AAA4C,IAA5CA,EAAQ0F,OAAO,CAAC,yBACF2G,GAAoBrM,EAAQI,MAAM,CAAG,GAAM,GAErD,CADJJ,CAAAA,EAAUD,EAAcC,IAAY,EAAC,EAEjC,MAAM,AAAIkK,MAAM,6BAIxB,GAAI,AAAsB,KAAA,IAAf9M,EAAEmO,QAAQ,CACjBnO,EAAEkO,IAAI,CAAGtL,EACT5C,EAAEmO,QAAQ,CAAGC,EACb9L,EAAIwL,IAAI,CAACsB,WAAW,CAACpP,GACrBA,EAAEqO,KAAK,GACP/L,EAAIwL,IAAI,CAACuB,WAAW,CAACrP,QAIrB,GAAI,CACA,GAAI,CAACoC,EAAIiI,IAAI,CAACzH,EAAS,SACnB,MAAM,AAAIkK,MAAM,wBAExB,CACA,KAAM,CAEF1K,EAAIkN,QAAQ,CAACpB,IAAI,CAAGtL,CACxB,CAER,EAoCI8L,UA1BJ,SAAmBa,CAAc,EAC7B,OAAO,IAAIC,QAAQ,CAACC,EAASC,KACzB,IAAMC,EAAOrN,EAAIsN,oBAAoB,CAAC,OAAO,CAAC,EAAE,CAAEC,EAASvN,EAAI2L,aAAa,CAAC,SAE7E4B,CAAAA,EAAO5L,IAAI,CAAG,kBACd4L,EAAOC,GAAG,CAAGP,EAEbM,EAAOE,MAAM,CAAG,KACZN,GACJ,EAEAI,EAAOG,OAAO,CAAG,KACbN,EAAOnN,EAAM,CAAC,qBAAqB,EAAEgN,EAAe,CAAC,EACzD,EAEAI,EAAKP,WAAW,CAACS,EACrB,EACJ,CAUA,EAshDM,CAAEnH,SAAUuH,CAAkB,CAAE3N,IAAK4N,CAAa,CAAEC,UAAAA,CAAS,CAAEC,KAAAA,CAAI,CAAEjO,SAAUkO,CAAkB,CAAEC,OAAAA,CAAM,CAAElO,IAAKmO,CAAa,CAAE,CAAIvP,IAGnI,CAAE2H,SAAU6H,CAAkB,CAAEC,aAAcC,CAAsB,CAAEzC,cAAAA,CAAa,CAAE0C,IAAAA,CAAG,CAAExF,eAAgByF,CAAwB,CAAErO,MAAOsO,CAAe,CAAEC,OAAAA,CAAM,CAAEC,KAAAA,CAAI,CAAEnI,UAAWoI,EAAmB,CAAEC,SAAAA,EAAQ,CAAEC,MAAAA,EAAK,CAAE9F,WAAY+F,EAAoB,CAAEC,KAAAA,EAAI,CAAEvI,WAAYwI,EAAoB,CAAEC,YAAAA,EAAW,CAAEC,MAAAA,EAAK,CAAEC,UAAAA,EAAS,CAAE,CAAIxQ,IAC9UE,IAA8FuQ,iBAAiB,CAAC5P,IAAI,CAAC,eAAgB,eAAgB,SAAU,sBAAuB,KAAM,KAAM,mBAAoB,kBAAmB,iBAAkB,cAAe,YAAa,mBAAoB,UAAW,UAAW,aAAc,QAAS,eACxVX,IAA8FwQ,WAAW,CAAC7P,IAAI,CAAC,OAAQ,WAAY,eAAgB,eAAgB,IAAK,SAMxK,IAAM8P,GAAmBpB,EAAc9N,GAAG,EAAI8N,EAAc7N,SAAS,EAAI6N,CAmBzE,OAAMqB,GAMFnQ,YAAYH,CAAK,CAAES,CAAO,CAAE,CACxB,IAAI,CAACA,OAAO,CAAG,CAAC,EAChB,IAAI,CAACT,KAAK,CAAGA,EACb,IAAI,CAACS,OAAO,CAAGA,EACf,IAAI,CAAC8P,QAAQ,CAAG,EAChB,IAAI,CAACC,YAAY,CAAG,EACpB,IAAI,CAACjH,WAAW,CAAG,EAAE,CACrB,IAAI,CAACkH,WAAW,CAAG,EAAE,AACzB,CAqBA,OAAOC,UAAUC,CAAQ,CAAE,CACvB,OAAOA,EAASnP,OAAO,CAAC,SAAU,SAAUC,CAAK,EAC7C,MAAO,IAAMA,EAAMmP,WAAW,EAClC,EACJ,CAkBA,aAAaC,eAAeC,CAAQ,CAAE1N,CAAK,CAAE2N,CAAS,CAAE,CAEpD,IAAMC,EAAM,MAAMV,GAAUW,SAAS,CAACH,GAAWI,EAAStC,EAAcjC,aAAa,CAAC,UAAWwE,EAAMD,GAAQE,WAAW,MAC1H,GAAKD,EAQD,OAJAD,EAAO/L,MAAM,CAAG6L,EAAI7L,MAAM,CAAG/B,EAC7B8N,EAAO5L,KAAK,CAAG0L,EAAI1L,KAAK,CAAGlC,EAC3B+N,EAAIE,SAAS,CAACL,EAAK,EAAG,EAAGE,EAAO5L,KAAK,CAAE4L,EAAO/L,MAAM,EAE7C+L,EAAOI,SAAS,CAACP,EAPxB,OAAM,AAAIvF,MAAM,mBASxB,CAgBA,OAAOyF,UAAUH,CAAQ,CAAE,CACvB,OAAO,IAAI5C,QAAQ,CAACC,EAASC,KAEzB,IAAMmD,EAAQ,IAAItC,EAAcuC,KAAK,AAErCD,CAAAA,EAAME,WAAW,CAAG,YAEpBF,EAAM9C,MAAM,CAAG,KAEXiD,WAAW,KACPvD,EAAQoD,EACZ,EAAGjB,GAAUqB,mBAAmB,CACpC,EAEAJ,EAAM7C,OAAO,CAAG,AAACzN,IACbmN,EAAOnN,EACX,EAEAsQ,EAAM/C,GAAG,CAAGsC,CAChB,EACJ,CAiBA,OAAOc,oBAAoBpI,CAAgB,CAAE,CACzC,IAAM7G,EAAO6G,GAAkB7G,MAAQ,YAAaF,EAAU+G,GAAkB/G,QAC5EuK,EAAezK,SAAS,EAAEE,OAC9B,MAAO,CACHE,KAAAA,EACAmK,SAAW,AAACtD,CAAAA,GAAkBsD,UAAY,OAAM,EAC5C,IACCnK,CAAAA,AAAS,kBAATA,EAA2B,MAAQA,EAAKkP,KAAK,CAAC,IAAI,CAAC,EAAE,AAAD,EACzDzO,MAAOoG,GAAkBpG,OAAS,EAElCX,OAAQA,GAAQqP,MAAM,MAAQ,IAAMrP,EAAS,IAAMA,CACvD,CACJ,CAmBA,OAAOsP,YAAYC,CAAG,CAAEvR,CAAO,CAAE,CAC7B,IAAMoR,EAAQG,EAAIhL,OAAO,CAAC,UAAY,EAAGiL,EAAmBD,EAAIhL,OAAO,CAAC,kBAAoB,GACxFkL,EAAOF,EAAIG,MAAM,CAACN,GA+BtB,OA7BAG,EAAMA,EAAIG,MAAM,CAAC,EAAGN,GAChBI,EAEAD,EAAMA,EAAIxQ,OAAO,CAAC,2BAA4B,SAGzC0Q,GAAQzR,GAAS8B,WAAW6P,YACjCF,EAAO,qCACSzR,EAAQT,KAAK,CAACsF,KAAK,CAD5B,aAEU7E,EAAQT,KAAK,CAACmF,MAAM,CAF9B,gDAKH+M,EAAK1Q,OAAO,CAAC,2BAA4B,SALtC,0BAQPwQ,EAAMA,EAAIxQ,OAAO,CAAC,SAAU0Q,EAAO,WAEvCF,EAAMA,EACDxQ,OAAO,CAAC,kBAAmB,IAC3BA,OAAO,CAAC,sBAAuB,IAC/BA,OAAO,CAAC,qBAAsB,IAC9BA,OAAO,CAAC,uCAAwC,WAChDA,OAAO,CAAC,eAAgB,SACxBA,OAAO,CAAC,QAAS,oDACjBA,OAAO,CAAC,oBAAqB,gBAC7BA,OAAO,CAAC,OAAQ,KAEhBA,OAAO,CAAC,UAAW,QACnBA,OAAO,CAAC,SAAU,OAE3B,CAgBA,OAAO6Q,aAAaL,CAAG,CAAE,CAErB,IAAMtE,EAAYuB,EAAc1B,SAAS,CAACG,SAAS,CAC7C4E,EAAU5E,EAAU1G,OAAO,CAAC,UAAY,IAC1C0G,AAA8B,EAA9BA,EAAU1G,OAAO,CAAC,UACtB,GAAI,CAIA,GAAI,CAACsL,GAAUN,AAAkC,KAAlCA,EAAIhL,OAAO,CAAC,kBACvB,OAAOqJ,GAAiBtO,eAAe,CAAC,IAAIkN,EAAcnN,IAAI,CAAC,CAACkQ,EAAI,CAAE,CAClErP,KAAM,8BACV,GAER,CACA,KAAM,CAEN,CACA,MAAO,oCAAsC4P,mBAAmBP,EACpE,CAiBAQ,UAAU/R,CAAO,CAAE,CACf,IACIgD,EADElB,EAAY,IAAI,CAAEvC,EAAQuC,EAAUvC,KAAK,CAAEyS,EAAWzS,EAAMyS,QAAQ,CAAEC,EAAa9C,GAAM5P,EAAMS,OAAO,CAACR,UAAU,EAAE4E,cAAepE,GAAUsD,EAAU2O,EAAW3O,OAAO,CAAEJ,EAAY+O,EAAW/O,SAAS,CAAEmB,EAAa4N,EAAW5N,UAAU,EAAI,GAExP,GAAI4N,AAAuB,CAAA,IAAvBA,EAAWC,OAAO,EAAc,CAACD,EAAWhN,KAAK,CACjD,OAEJ,IAAMA,EAAQ1F,EAAM4S,UAAU,CAAG,CAAC,EAAIF,EAAWhN,KAAK,CAClDmN,EAAY,KAAQ,EACpB9O,EACA8O,EAAW,SAAUtH,CAAC,EACdA,GACAA,EAAEuH,eAAe,GAErB/O,EAAQzE,IAAI,CAACU,EAAOuL,EACxB,EAEK5H,GACLkP,CAAAA,EAAW,SAAUtH,CAAC,EAEdA,GACAA,EAAEuH,eAAe,GAErBvQ,EAAUwQ,WAAW,CAACC,EAAOxP,aAAa,CAAEG,EAAWqP,EAAOC,UAAU,EAAI,EAAGD,EAAOE,UAAU,EAAI,EAAGF,EAAO1N,KAAK,EAAI,EAAG0N,EAAO7N,MAAM,EAAI,EAAG6N,GAC9IA,EAAOG,QAAQ,CAAC,EACpB,CAAA,EAEAT,EAAW/I,IAAI,EAAI+I,EAAWjP,MAAM,CACpCiC,EAAM0N,WAAW,CAAGtD,GAAKpK,EAAM0N,WAAW,CAAE,IAEvC,AAACV,EAAW/I,IAAI,EACrB6F,EAAO9J,EAAO,CACVJ,MAAOoN,EAAWpN,KAAK,CACvBH,OAAQuN,EAAWvN,MAAM,CACzBS,QAAS,CACb,GAEJ,IAAMoN,EAASP,EACVO,MAAM,CAACN,EAAW/I,IAAI,EAAI,GAAI,EAAG,EAAGkJ,EAAUnN,EAAO,KAAK,EAAG,KAAK,EAAG,KAAK,EAAG,KAAK,EAAGgN,EAAWW,OAAO,EACvGC,QAAQ,CAAC7S,EAAQ8C,SAAS,EAAI,IAC9BgQ,IAAI,CAAC,CACNC,MAAO1D,GAAK9P,EAAMS,OAAO,CAACiE,IAAI,CAAEgO,EAAWe,SAAS,EAChDf,EAAWhP,QAAQ,CAAE,CAAE,GAC/B,EACAsP,CAAAA,EAAOxP,aAAa,CAAI/C,EAAQ+C,aAAa,EACzC,mBAAqBjB,EAAUgO,QAAQ,GACvCmC,EAAWjP,MAAM,GACjBA,EAASgP,EACJhP,MAAM,CAACiP,EAAWjP,MAAM,CAAEiQ,KAAKC,KAAK,CAAC,AAACjB,CAAAA,EAAW3N,OAAO,EAAI,CAAA,EAAMD,EAAa,GAAK4O,KAAKC,KAAK,CAAC,AAACjB,CAAAA,EAAW1N,OAAO,EAAI,CAAA,EAAMF,EAAa,GAAKA,EAAYA,EAE/J,CACIQ,MAAOR,EACPK,OAAQL,CACZ,GACKwO,QAAQ,CAAC,4BACTC,IAAI,CAAC,CACNK,OAAQ,CACZ,GACKC,GAAG,CAACb,GACL,AAAChT,EAAM4S,UAAU,EACjBnP,EAAO8P,IAAI,CAAC,CACR1N,OAAQ6M,EAAWlN,YAAY,CAC/BG,KAAM+M,EAAWnN,UAAU,CAC3B,eAAgBmN,EAAWjN,iBAAiB,EAAI,CACpD,IAGRuN,EACKa,GAAG,CAACtR,EAAUuR,KAAK,EACnB7O,KAAK,CAACuK,EAAOkD,EAAY,CAC1BpN,MAAO0N,EAAO1N,KAAK,CACnBmB,EAAGqJ,GAAK4C,EAAWjM,CAAC,CAAElE,EAAUiO,YAAY,CAChD,GAAI,CAAA,EAAM,cACVjO,EAAUiO,YAAY,EAAK,AAAC,CAAA,AAACwC,CAAAA,EAAO1N,KAAK,EAAI,CAAA,EAAMoN,CAAAA,EAAWxN,aAAa,EAAI,CAAA,CAAC,EAC3EwN,CAAAA,AAAqB,UAArBA,EAAWzN,KAAK,CAAe,GAAK,CAAA,EACzC1C,EAAUkO,WAAW,CAAClQ,IAAI,CAACyS,EAAQvP,EACvC,CAWAsQ,YAAa,CACT,IAAM/T,EAAQ,IAAI,CAACA,KAAK,CACxB,GAAI,CAAC,IAAI,CAACgU,gBAAgB,CACtB,OAEJ,GAAM,CAAEC,WAAAA,CAAU,CAAEC,YAAAA,CAAW,CAAEC,YAAAA,CAAW,CAAE,CAAG,IAAI,CAACH,gBAAgB,CAEtE,IAAI,CAACI,cAAc,CAACpU,EAAM6H,QAAQ,EAElC,EAAE,CAAClH,OAAO,CAACrB,IAAI,CAAC2U,EAAY,SAAUI,CAAI,CAAElS,CAAC,EACrCkS,AAAkB,IAAlBA,EAAKC,QAAQ,EACbD,CAAAA,EAAKE,KAAK,CAACC,OAAO,CAAIN,CAAW,CAAC/R,EAAE,EAAI,EAAE,CAElD,GACA,IAAI,CAACsS,UAAU,CAAG,CAAA,EAEdN,GACAnU,EAAMyI,OAAO,CAACiM,KAAK,CAAC1U,EAAOmU,GAE/B,OAAO,IAAI,CAACH,gBAAgB,CAC5B1D,GAAUqE,aAAa,CAAG,KAAK,EAC/BjF,GAAoB1P,EAAO,aAC/B,CAWA4U,aAAc,CACV,IAAM5U,EAAQ,IAAI,CAACA,KAAK,CAAEwM,EAAOoC,EAAcpC,IAAI,CAAErJ,EAAgB,IAAI,CAAC1C,OAAO,CAAC0C,aAAa,CAAE6Q,EAAmB,CAChHC,WAAYzH,EAAKyH,UAAU,CAC3BC,YAAa,EAAE,CACfC,YAAa,KAAK,CACtB,CACA,CAAA,IAAI,CAACM,UAAU,CAAG,CAAA,EAClBzU,EAAM6U,OAAO,EAAEC,MAAM,KAAK,EAAG,GAC7BpF,GAAoB1P,EAAO,eAEvBmD,GAAiBnD,EAAMgJ,UAAU,CAAG7F,IACpC6Q,EAAiBG,WAAW,CAAG,CAC3BnU,EAAMS,OAAO,CAACT,KAAK,CAACsF,KAAK,CACzB,KAAK,EACL,CAAA,EACH,CACDtF,EAAMyI,OAAO,CAACtF,EAAe,KAAK,EAAG,CAAA,IAGzC,EAAE,CAACxC,OAAO,CAACrB,IAAI,CAAC0U,EAAiBC,UAAU,CAAE,SAAUI,CAAI,CAAElS,CAAC,EACpC,IAAlBkS,EAAKC,QAAQ,GACbN,EAAiBE,WAAW,CAAC/R,EAAE,CAAGkS,EAAKE,KAAK,CAACC,OAAO,CACpDH,EAAKE,KAAK,CAACC,OAAO,CAAG,OAE7B,GAEA,IAAI,CAACJ,cAAc,CAAC5H,GAEpB,IAAI,CAACwH,gBAAgB,CAAGA,CAC5B,CA2BAjB,YAAYxP,CAAS,CAAEwR,CAAK,CAAEtO,CAAC,CAAErB,CAAC,CAAEE,CAAK,CAAEH,CAAM,CAAE6N,CAAM,CAAE,CACvD,IAAMzQ,EAAY,IAAI,CAAEvC,EAAQuC,EAAUvC,KAAK,CAAEgV,EAAahV,EAAMS,OAAO,CAACR,UAAU,CAAE+I,EAAahJ,EAAMgJ,UAAU,CAAEC,EAAcjJ,EAAMiJ,WAAW,CAAEgM,EAAY,SAAW1R,EAE/K2R,EAAcxB,KAAKyB,GAAG,CAAC7P,EAAOH,GAC1BiQ,EAAW5O,EAAOxG,CAAK,CAACiV,EAAU,CAEjCzO,IAEDjE,EAAU8S,aAAa,CAAGrV,CAAK,CAACiV,EAAU,CAAGzO,EACzCmG,EAAc,MAAO,CACjBpJ,UAAWA,CACf,EAAG,CACC+R,SAAU,WACV1B,OAAQ,IACRhO,QAASsP,EAAc,KACvBK,cAAe,OACf,GAAGvV,EAAMyS,QAAQ,CAAC8B,KAAK,AAC3B,EAAGvU,EAAMwV,kBAAkB,EAAEC,UAAYzV,EAAM4H,SAAS,EAC5DwN,EAAYzI,EAAc,KAAM,CAAEpJ,UAAW,iBAAkB,EAAGvD,EAAM4S,UAAU,CAAG,CAAC,EAAI,CACtF8C,UAAW,OACXC,OAAQ,EACR/P,QAAS,CACb,EAAGY,GAEC,AAACxG,EAAM4S,UAAU,EACjBvD,EAAI+F,EAAW5F,EAAO,CAClBoG,aAAc,qBACdC,gBAAiB,qBACjBC,UAAW,oBACf,EAAGd,GAAYlP,WAAa,CAAC,IAGjCU,EAAKuP,QAAQ,CAAG,WACZ1G,EAAI7I,EAAM,CAAEgO,QAAS,MAAO,GACxBxB,GACAA,EAAOG,QAAQ,CAAC,GAEhBnT,EAAMuC,SAAS,EACfvC,CAAAA,EAAMuC,SAAS,CAACyT,QAAQ,CAAG,CAAA,CAAI,EAGnC3G,EAAIrP,EAAM6H,QAAQ,CAAE,CAAEoO,SAAU,QAAS,GACzC5G,EAAIrP,EAAM4H,SAAS,CAAE,CAAEqO,SAAU,QAAS,GAC1C7G,EAAuB5I,EAAK0P,SAAS,EACrCxG,GAAoB1P,EAAO,mBAC/B,EAEAuC,EAAU4T,MAAM,EAAE5V,KAAK2O,EAAmB1I,EAAM,aAAc,WAC1DA,EAAK0P,SAAS,CAAGjH,EAAcyC,UAAU,CAAClL,EAAKuP,QAAQ,CAAE,IAC7D,GAAI7G,EAAmB1I,EAAM,aAAc,WACvC4I,EAAuB5I,EAAK0P,SAAS,CACzC,GAGAhH,EAAmBN,EAAe,UAAW,SAAUrD,CAAC,EAChD,AAACvL,EAAM6U,OAAO,EAAEuB,QAAQ7K,EAAE8K,MAAM,CAAE9S,IAClCiD,EAAKuP,QAAQ,EAErB,GAAI7G,EAAmB1I,EAAM,QAAS,WAC9BxG,EAAMuC,SAAS,EAAEyT,UACjBxP,EAAKuP,QAAQ,EAErB,IAEAhB,EAAMpU,OAAO,CAAC,SAAU2V,CAAI,EAMxB,GALI,AAAgB,UAAhB,OAAOA,GACH/T,EAAU9B,OAAO,CAACmD,mBAAmB,EAAE,CAAC0S,EAAK,EAC7CA,CAAAA,EAAO/T,EAAU9B,OAAO,CAACmD,mBAAmB,CAAC0S,EAAK,AAAD,EAGrD3G,GAAS2G,EAAM,CAAA,GAAO,CACtB,IAAIC,CACAD,CAAAA,EAAKlS,SAAS,CACdmS,EAAU5J,EAAc,KAAM,KAAK,EAAG,KAAK,EAAGyI,IAK1CkB,AAAiB,aAAjBA,EAAKxS,OAAO,EACZvB,EAAUiU,kBAAkB,EAC5BF,CAAAA,EAAKxS,OAAO,CAAG,UAAS,EAE5ByS,EAAU5J,EAAc,KAAM,CAC1BpJ,UAAW,uBACXQ,QAAS,SAAUwH,CAAC,EACZA,GACAA,EAAEuH,eAAe,GAErBtM,EAAKuP,QAAQ,GACT,AAAgB,UAAhB,OAAOO,GAAqBA,EAAKvS,OAAO,EAExCuS,EAAKvS,OAAO,CAAC2Q,KAAK,CAAC1U,EAAOyW,UAElC,CACJ,EAAG,KAAK,EAAGrB,GACXxV,IAA8F8J,cAAc,CAAC6M,EAASD,EAAK3M,IAAI,EAAI3J,EAAMS,OAAO,CAACiE,IAAI,CAAC4R,EAAKxS,OAAO,CAAC,EAC9J9D,EAAM4S,UAAU,GACjB2D,EAAQG,WAAW,CAAG,WAClBrH,EAAI,IAAI,CAAE2F,GAAY1O,oBAAsB,CAAC,EACjD,EACAiQ,EAAQI,UAAU,CAAG,WACjBtH,EAAI,IAAI,CAAE2F,GAAY9O,eAAiB,CAAC,EAC5C,EACAmJ,EAAIkH,EAAS/G,EAAO,CAChBoH,OAAQ,SACZ,EAAG5B,GAAY9O,eAAiB,CAAC,MAIzC3D,EAAUgH,WAAW,CAAChJ,IAAI,CAACgW,EAC/B,CACJ,GAGAhU,EAAUgH,WAAW,CAAChJ,IAAI,CAAC6U,EAAW5O,GACtCjE,EAAUsU,UAAU,CAAGrQ,EAAKsQ,YAAY,CACxCvU,EAAUwU,SAAS,CAAGvQ,EAAKwQ,WAAW,EAE1C,IAAMlR,EAAY,CAAE0O,QAAS,OAAQ,CAEjC/N,CAAAA,EAAKlE,CAAAA,EAAUwU,SAAS,EAAI,CAAA,EAAK/N,EACjClD,EAAUmR,KAAK,CAAG,AAACjO,EAAavC,EAAInB,EAAQ4P,EAAe,KAG3DpP,EAAUoR,IAAI,CAAG,AAACzQ,EAAIyO,EAAe,KAGrC9P,EAAID,EAAU5C,CAAAA,EAAUsU,UAAU,EAAI,CAAA,EACtC5N,GACA+J,EAAOmE,YAAY,EAAE9R,gBAAkB,MACvCS,EAAUsR,MAAM,CAAG,AAACnO,EAAc7D,EAAI8P,EAAe,KAGrDpP,EAAUuR,GAAG,CAAG,AAACjS,EAAID,EAAS+P,EAAe,KAEjD7F,EAAI7I,EAAMV,GAEVuJ,EAAIrP,EAAM6H,QAAQ,CAAE,CAAEoO,SAAU,EAAG,GACnC5G,EAAIrP,EAAM4H,SAAS,CAAE,CAAEqO,SAAU,EAAG,GAChCjW,EAAMuC,SAAS,EACfvC,CAAAA,EAAMuC,SAAS,CAACyT,QAAQ,CAAG,CAAA,CAAG,EAElCtG,GAAoB1P,EAAO,kBAC/B,CAYAsX,QAAQ/L,CAAC,CAAE,CACP,IACI0J,EADoBjV,EAAQuL,EAAIA,EAAE8K,MAAM,CAAG9T,AAA7B,IAAI,CAAmCvC,KAAK,CAAE,CAAEuJ,YAAAA,CAAW,CAAE4M,OAAAA,CAAM,CAAE1F,YAAAA,CAAW,CAAE,CAAlF,IAAI,CAGtBA,EAAY9P,OAAO,CAAC,CAAC4W,EAAMpV,KAEnBoV,IACAA,EAAKxT,OAAO,CAAGwT,EAAKC,YAAY,CAAG,KAE/BxX,CAAK,CADTiV,EAAY,SAAWsC,EAAK/T,aAAa,CACrB,EAChB,OAAOxD,CAAK,CAACiV,EAAU,CAE3BxE,CAAW,CAACtO,EAAE,CAAGoV,EAAKD,OAAO,GAErC,GACA7G,EAAY/O,MAAM,CAAG,EAEjBa,AAhBc,IAAI,CAgBRuR,KAAK,GACfvR,AAjBc,IAAI,CAiBRuR,KAAK,CAACwD,OAAO,GACvB,OAAO/U,AAlBO,IAAI,CAkBDuR,KAAK,EAG1BvK,EAAY5I,OAAO,CAAC,SAAU4W,CAAI,CAAEpV,CAAC,EAC7BoV,IAEAnI,EAAuBmI,EAAKrB,SAAS,EACrClG,GAAYuH,EAAM,cAElBhO,CAAW,CAACpH,EAAE,CACVoV,EAAKZ,UAAU,CACXY,EAAKb,WAAW,CACZa,EAAKC,YAAY,CACbD,EAAKxT,OAAO,CAAG,KAE/BuL,EAAyBiI,GAEjC,GACAhO,EAAY7H,MAAM,CAAG,EACjByU,IACAA,EAAOxV,OAAO,CAAC,SAAU8W,CAAM,EAC3BA,GACJ,GACAtB,EAAOzU,MAAM,CAAG,EAExB,CA0BA,MAAM+C,YAAYuN,CAAG,CAAExI,CAAgB,CAAE,CACrC,IAaIkO,EAbEC,EAAY,CACd3F,IAAAA,EACAxI,iBAAAA,EACAjH,UAAW,IAAI,AACnB,EAIA,GAFAmN,GAAoBY,GAAUlR,SAAS,CAAE,cAAeuY,GAEpDA,EAAUC,gBAAgB,CAC1B,OAGJ,GAAM,CAAEjV,KAAAA,CAAI,CAAEmK,SAAAA,CAAQ,CAAE1J,MAAAA,CAAK,CAAEX,OAAAA,CAAM,CAAE,CAAG6N,GAAUsB,mBAAmB,CAACpI,GAGxE,GAAI7G,AAAS,oBAATA,EAEA,MAAM,AAAI6I,MAAM,sDAEf,GAAI7I,AAAS,kBAATA,EAA0B,CAG/B,GAAI,AAAuC,KAAA,IAAhCsM,EAAc4I,aAAa,CAAkB,CACpD,IAAMC,EAAO,IAAI7I,EAAc4I,aAAa,CAC5CC,EAAK1L,MAAM,CAAC4F,GACZ0F,EAASI,EAAKC,OAAO,CAAC,gBAC1B,MAEIL,EAASpH,GAAU+B,YAAY,CAACL,GAGpC7E,EAAsBuK,EAAQ5K,EAClC,KACK,CAED4K,EAASpH,GAAU+B,YAAY,CAACL,GAChC,GAAI,CACA1B,GAAU0H,eAAe,CAAG,CAAA,EAE5B,IAAM1W,EAAU,MAAMgP,GAAUO,cAAc,CAAC6G,EAAQtU,EAAOT,GAC9DwK,EAAsB7L,EAASwL,EACnC,CACA,MAAO7L,EAAO,CAGV,GAAIA,AAAkB,qBAAlBA,EAAMgX,OAAO,CACb,MAAMhX,EAGV,GAAI+Q,EAAItQ,MAAM,CAAG,IACb,MAAM,AAAI8J,MAAM,kBAIpB,IAAM0F,EAAStC,EAAcjC,aAAa,CAAC,UAAWwE,EAAMD,EAAOE,UAAU,CAAC,MAAO8G,EAAoBlG,EAAIvQ,KAAK,CAElH,gEAAiE0W,EAAqBnG,EAAIvQ,KAAK,CAE/F,kEACA,GAAI0P,GACA+G,GACAC,EAAoB,CACpB,IAAMC,EAAa,AAACF,CAAiB,CAAC,EAAE,CAAG9U,EAAOiV,EAAc,AAACF,CAAkB,CAAC,EAAE,CAAG/U,CAOzF8N,CAAAA,EAAO5L,KAAK,CAAG8S,EACflH,EAAO/L,MAAM,CAAGkT,EAIXpJ,EAAcqJ,KAAK,GACpBhI,GAAU0H,eAAe,CAAG,CAAA,EAC5B,MAAM3K,EAAoB5K,EAAS,aAZnC8V,AADUtJ,EAAcqJ,KAAK,CAACE,KAAK,CAACC,UAAU,CAACtH,EAAKa,GAClD0G,KAAK,GACPvL,EAAsB8B,EAAc1B,SAAS,CAACE,gBAAgB,CAC1DyD,EAAOyH,QAAQ,GACfzH,EAAOI,SAAS,CAAC3O,GAAOmK,EAapC,CACJ,QACQ,CACJ,GAAIwD,GAAU0H,eAAe,CACzB,GAAI,CACA3H,GAAiBuI,eAAe,CAAClB,EACrC,CACA,KAAM,CAEN,CAER,CACJ,CACJ,CA4BA,MAAMpT,YAAYkF,CAAgB,CAAEqP,CAAY,CAAE,CAI9C,GAAIrP,AAFJA,CAAAA,EAAmBoG,GAAM,IAAI,CAACnP,OAAO,CAAE+I,EAAgB,EAElC9G,KAAK,CAEtB,MAAM,IAAI,CAACoW,WAAW,CAACtP,EAAkBqP,GAAgB,CAAC,OAEzD,CAED,IAAM7G,EAAM,IAAI,CAAC+G,eAAe,CAACvP,EAAkBqP,EAE/CrP,CAAAA,EAAiB5G,GAAG,EACpB,MAAMoW,AA32BmCjP,EA22BhB+B,IAAI,CAACtC,EAAiB5G,GAAG,CAAE,CAChDkK,SAAUtD,EAAiBsD,QAAQ,CAC/BtD,EAAiBsD,QAAQ,CAACtL,OAAO,CAAC,MAAO,KACzC,IAAI,CAACyX,WAAW,GACpBtW,KAAM6G,EAAiB7G,IAAI,CAC3B2C,MAAOkE,EAAiBlE,KAAK,CAC7BlC,MAAOoG,EAAiBpG,KAAK,CAC7B4O,IAAAA,CACJ,EAAGxI,EAAiBuC,YAAY,CAExC,CACJ,CAkBA,MAAMmN,iBAAiB1P,CAAgB,CAAEkB,CAAG,CAAE,CACtClB,AAA4C,CAAA,IAA5CA,EAAiB2P,sBAAsB,CACnC3P,EAAiBvI,KAAK,CACtBuI,EAAiBvI,KAAK,CAACuI,EAAkBkB,GAIzC6E,EAAgB,GAAI,CAAA,GAGO,oBAA1B/F,EAAiB7G,IAAI,GAE1B6G,EAAiB9G,KAAK,CAAG,CAAA,EAEzB,MAAM,IAAI,CAAC4B,WAAW,CAACkF,GAE/B,CAiBA4P,aAAaC,CAAgB,CAAE,CAC3B,IAAMrZ,EAAQ,IAAI,CAACA,KAAK,CAKxB,OAJIqZ,GACA,IAAI,CAACC,YAAY,GAErB,IAAI,CAACC,mBAAmB,GACjBvZ,EAAM4H,SAAS,CAAC4R,SAAS,AACpC,CAYAP,aAAc,CACV,IAAMQ,EAAY,IAAI,CAACzZ,KAAK,CAAC0Z,WAAW,CAAClG,KAAK,EAAE7J,KAC5CmD,EAAW,IAAI,CAACrM,OAAO,CAACqM,QAAQ,QACpC,AAAIA,EACOA,EAAStL,OAAO,CAAC,MAAO,MAE/B,AAAqB,UAArB,OAAOiY,GACP3M,CAAAA,EAAW2M,EACN7I,WAAW,GACXpP,OAAO,CAAC,kBAAmB,IAC3BA,OAAO,CAAC,UAAW,KACnBA,OAAO,CAAC,cAAe,IACvBA,OAAO,CAAC,UAAW,IACnBA,OAAO,CAAC,SAAU,KAClB2Q,MAAM,CAAC,EAAG,IACV3Q,OAAO,CAAC,UAAW,GAAE,EAE1B,CAAA,CAACsL,GAAYA,EAASpL,MAAM,CAAG,CAAA,GAC/BoL,CAAAA,EAAW,OAAM,EAEdA,EACX,CAsBA6M,OAAOd,CAAY,CAAE,CACjB,IAAM7Y,EAAQ,IAAI,CAACA,KAAK,CACpBgS,EAAK4H,EAETnZ,EAAUmP,GAAM5P,EAAMS,OAAO,CAAEoY,EAE/BpY,CAAAA,EAAQoZ,WAAW,CAAGjK,GAAM5P,EAAM0Z,WAAW,CAACG,WAAW,CAAEhB,GAAcgB,aAGzEpZ,EAAQqZ,IAAI,CAAGlK,GAAM5P,EAAM0Z,WAAW,CAACI,IAAI,CAAEjB,GAAciB,MAE3D,IAAMC,EAAUpN,EAAc,MAAO,KAAK,EAAG,CACzC2I,SAAU,WACV+B,IAAK,UACL/R,MAAOtF,EAAMgJ,UAAU,CAAG,KAC1B7D,OAAQnF,EAAMiJ,WAAW,CAAG,IAChC,EAAG2F,EAAcpC,IAAI,EAEfwN,EAAWha,EAAM6H,QAAQ,CAAC0M,KAAK,CAACjP,KAAK,CAAE2U,EAAYja,EAAM6H,QAAQ,CAAC0M,KAAK,CAACpP,MAAM,CAAE+U,EAAczZ,EAAQ8B,SAAS,EAAE2X,aACnHzZ,EAAQT,KAAK,CAACsF,KAAK,EAClB,MAAMsI,IAAI,CAACoM,IAAaG,SAASH,EAAU,KAC3CvZ,CAAAA,EAAQ2Z,OAAO,CAAG,IAAM,GAAE,EAAIC,EAAe5Z,EAAQ8B,SAAS,EAAE8X,cACjE5Z,EAAQT,KAAK,CAACmF,MAAM,EACnB,MAAMyI,IAAI,CAACqM,IAAcE,SAASF,EAAW,KAC9C,IAEJzK,EAAO/O,EAAQT,KAAK,CAAE,CAClBsa,UAAW,CAAA,EACXzS,SAAUkS,EACVQ,UAAW,CAAA,EACX9H,SAAU,cACVnN,MAAO4U,EACP/U,OAAQkV,CACZ,GACI5Z,EAAQ8B,SAAS,EACjB9B,CAAAA,EAAQ8B,SAAS,CAACoQ,OAAO,CAAG,CAAA,CAAI,EAEpC,OAAOlS,EAAQiL,IAAI,CAEnBjL,EAAQ+Z,MAAM,CAAG,EAAE,CACnBxa,EAAMwa,MAAM,CAAC7Z,OAAO,CAAC,SAAU8Z,CAAK,EAQ5B,AAACb,AAPLA,CAAAA,EAAgBhK,GAAM6K,EAAMf,WAAW,CAAE,CACrCY,UAAW,CAAA,EACXI,oBAAqB,CAAA,EACrBC,aAAc,CAAA,EACdC,QAASH,EAAMG,OAAO,AAC1B,EAAC,EAEkBC,UAAU,EACzBpa,GAAS+Z,QAAQja,KAAKqZ,EAE9B,GACA,IAAMkB,EAAQ,CAAC,EACf9a,EAAM+a,IAAI,CAACpa,OAAO,CAAC,SAAUqa,CAAI,EAEzB,AAACA,EAAKtB,WAAW,CAACuB,WAAW,EAC7BD,CAAAA,EAAKtB,WAAW,CAACuB,WAAW,CAAG/K,IAAU,EAEzCzP,GAAW,CAACua,EAAKva,OAAO,CAACoa,UAAU,GAC9BC,CAAK,CAACE,EAAKE,IAAI,CAAC,GACjBJ,CAAK,CAACE,EAAKE,IAAI,CAAC,CAAG,CAAA,EACnBza,CAAO,CAACua,EAAKE,IAAI,CAAC,CAAG,EAAE,EAE3Bza,CAAO,CAACua,EAAKE,IAAI,CAAC,CAAC3a,IAAI,CAACqP,GAAMoL,EAAKtB,WAAW,CAAE,CAC5CkB,QAASI,EAAKJ,OAAO,CAGrBjY,KAAMqY,EAAKrY,IAAI,CACfwY,YAAaH,EAAKG,WAAW,AACjC,IAER,GAIA1a,EAAQ2a,SAAS,CAAGpb,EAAM0Z,WAAW,CAAC0B,SAAS,CAE/C,IAAMC,EAAY,IAAIrb,EAAMG,WAAW,CAACM,EAAST,EAAM6S,QAAQ,EAyC/D,OAvCIgG,GACA,CAAC,QAAS,QAAS,SAAS,CAAClY,OAAO,CAAC,SAAUua,CAAI,EAC3CrC,CAAY,CAACqC,EAAK,EAClBG,EAAU7a,MAAM,CAAC,CACb,CAAC0a,EAAK,CAAErC,CAAY,CAACqC,EAAK,AAC9B,EAER,GAGJlb,EAAM+a,IAAI,CAACpa,OAAO,CAAC,SAAUqa,CAAI,EAC7B,IAAMM,EAAW7L,EAAK4L,EAAUN,IAAI,CAAE,AAACQ,GAASA,EAAK9a,OAAO,CAACwa,WAAW,GAAKD,EAAKtB,WAAW,CAACuB,WAAW,EACzG,GAAIK,EAAU,CACV,IAAME,EAAWR,EAAKS,WAAW,GAKjCC,EAAiBzL,GAAM4I,GAAc,CAACmC,EAAKE,IAAI,CAAC,EAAI,CAAC,EAAE,CAAC,EAAE,CAAES,EAAU,QAASD,EAC3EA,EAAeE,GAAG,CAClBJ,EAASG,OAAO,CAAEE,EAAU,QAASH,EACrCA,EAAevG,GAAG,CAClBqG,EAASK,OAAO,AACf,CAAA,CAAA,AAAoB,KAAA,IAAZF,GACTA,IAAYL,EAASM,GAAG,EAAM,AAAmB,KAAA,IAAZC,GACrCA,IAAYP,EAASnG,GAAG,GACxBmG,EAASQ,WAAW,CAACH,GAAW,KAAK,EAAGE,GAAW,KAAK,EAAG,CAAA,EAAM,CAAA,EAEzE,CACJ,GAEA7J,EAAMqJ,EAAU9Y,SAAS,EAAE6W,aAAapZ,EAAM4S,UAAU,EACpDnS,EAAQ8B,SAAS,EAAE8W,mBAAqB,GAC5C3J,GAAoB1P,EAAO,SAAU,CAAEqb,UAAWA,CAAU,GAC5DrJ,EAAM1B,GAAUyB,WAAW,CAACC,EAAKvR,GAEjCA,EAAU,KAAK,EACf4a,EAAU/D,OAAO,GACjBhI,EAAyByK,GAClB/H,CACX,CAkBA+G,gBAAgBvP,CAAgB,CAAEqP,CAAY,CAAE,CAC5C,IAAMkD,EAA0B,IAAI,CAACtb,OAAO,CAC5C,OAAO,IAAI,CAACkZ,MAAM,CAAC/J,GAAM,CAAE5P,MAAO,CAAEgG,aAAc,CAAE,CAAE,EAAG+V,EAAwBlD,YAAY,CAAEA,EAAc,CACzGtW,UAAW,CACP2X,YAAc1Q,GAAkB0Q,aAC5B6B,EAAwB7B,WAAW,CACvCG,aAAe7Q,GAAkB6Q,cAC7B0B,EAAwB1B,YAAY,AAC5C,CACJ,GACJ,CAcAf,cAAe,CACX,IAEI0C,EAFEC,EAAW3L,GAAU4L,cAAc,CAAEC,EAAY7L,GAAU8L,eAAe,CAChFC,EAAgB,CAAC,EAIXC,EAAS3P,EAAc,SAAU,KAAK,EAAG,CAC3CrH,MAAO,MACPH,OAAQ,MACRoX,WAAY,QAChB,EAAG3N,EAAcpC,IAAI,EACfgQ,EAAYF,EAAOG,aAAa,EAAE1b,QACpCyb,CAAAA,GACAA,EAAUhQ,IAAI,CAACsB,WAAW,CAAC0O,EAAUE,eAAe,CAAC1N,EAAQ,SAmJjE2N,AAxIA,SAASA,EAAQtI,CAAI,EACjB,IACIuI,EAAQC,EAAcC,EAAOC,EAAYC,EAAa7a,EADpD8a,EAAiB,CAAC,EA2DxB,GAAIT,GACAnI,AAAkB,IAAlBA,EAAKC,QAAQ,EACbhE,AAAsD,KAAtDA,GAAU4M,gBAAgB,CAAClW,OAAO,CAACqN,EAAK8I,QAAQ,EAAU,CAQ1D,GAPAP,EACI3N,EAAcmO,gBAAgB,CAAC/I,EAAM,MACzCwI,EAAexI,AAAkB,QAAlBA,EAAK8I,QAAQ,CACxB,CAAC,EACDlO,EAAcmO,gBAAgB,CAAC/I,EAAKgJ,UAAU,CAAE,MAGhD,CAAChB,CAAa,CAAChI,EAAK8I,QAAQ,CAAC,CAAE,CAQ/BnB,EACIQ,EAAUlO,oBAAoB,CAAC,MAAM,CAAC,EAAE,CAC5CwO,EAAQN,EAAUE,eAAe,CAACrI,EAAKiJ,YAAY,CAAEjJ,EAAK8I,QAAQ,EAClEnB,EAASlO,WAAW,CAACgP,GAGrB,IAAMS,EAAItO,EAAcmO,gBAAgB,CAACN,EAAO,MAAOU,EAAW,CAAC,EACnE,IAAK,IAAM5e,KAAO2e,EACV3e,EAAI8C,MAAM,CAAG,KACb,AAAkB,UAAlB,OAAO6b,CAAC,CAAC3e,EAAI,EACb,CAAC,QAAQgP,IAAI,CAAChP,IACd4e,CAAAA,CAAQ,CAAC5e,EAAI,CAAG2e,CAAC,CAAC3e,EAAI,AAAD,CAG7Byd,CAAAA,CAAa,CAAChI,EAAK8I,QAAQ,CAAC,CAAGK,EAG3BnJ,AAAkB,SAAlBA,EAAK8I,QAAQ,EACb,OAAOd,EAAc1S,IAAI,CAAChE,IAAI,CAElCqW,EAASjO,WAAW,CAAC+O,EACzB,CAEA,IAAK,IAAMW,KAAKb,EAGZ/N,CAAAA,GACIC,GACAC,GAEAjQ,OAAOO,cAAc,CAACC,IAAI,CAACsd,EAAQa,EAAC,GACpCC,AA9FZ,SAAsB5S,CAAG,CAAE3L,CAAI,EAG3B,GADA4d,EAAaC,EAAc,CAAA,EACvBb,EAAUza,MAAM,CAAE,CAIlB,IADAS,EAAIga,EAAUza,MAAM,CACbS,KAAO,CAAC6a,GACXA,EAAcb,CAAS,CAACha,EAAE,CAACyL,IAAI,CAACzO,GAEpC4d,EAAa,CAACC,CAClB,CAMA,IAJI7d,AAAS,cAATA,GAAwB2L,AAAQ,SAARA,GACxBiS,CAAAA,EAAa,CAAA,CAAG,EAEpB5a,EAAI8Z,EAASva,MAAM,CACZS,KAAO,CAAC4a,GAAY,CACvB,GAAI5d,EAAKuC,MAAM,CAAG,IACd,MAAM,AAAI8J,MAAM,kBAEpBuR,EAAcd,CAAQ,CAAC9Z,EAAE,CAACyL,IAAI,CAACzO,IAC3B,AAAe,YAAf,OAAO2L,CACf,CACI,CAACiS,GAIIF,CAAAA,CAAY,CAAC1d,EAAK,GAAK2L,GACxBuJ,AAAkB,QAAlBA,EAAK8I,QAAQ,AAAS,GACtB,AAACd,CAAa,CAAChI,EAAK8I,QAAQ,CAAC,AAAC,CAAChe,EAAK,GAAK2L,IAErC,AAACwF,GAAUqN,kBAAkB,EAC7BrN,AAA+C,KAA/CA,GAAUqN,kBAAkB,CAAC3W,OAAO,CAAC7H,GAOrC8d,CAAc,CAAC9d,EAAK,CAAG2L,EANnBA,GACAuJ,EAAKuJ,YAAY,CAACtN,GAAUI,SAAS,CAACvR,GAAO2L,GASjE,EAkDyB8R,CAAM,CAACa,EAAE,CAAEA,GAShC,GALApO,EAAIgF,EAAM4I,GAEN5I,AAAkB,QAAlBA,EAAK8I,QAAQ,EACb9I,EAAKuJ,YAAY,CAAC,eAAgB,OAElCvJ,AAAkB,SAAlBA,EAAK8I,QAAQ,CACb,OAGJ,EAAE,CAACxc,OAAO,CAACrB,IAAI,CAAC+U,EAAKwJ,QAAQ,EAAIxJ,EAAKJ,UAAU,CAAE0I,EACtD,CACJ,EAYQ,IAAI,CAAC3c,KAAK,CAAC4H,SAAS,CAACkW,aAAa,CAAC,QAJvC9B,EAASqB,UAAU,CAACtP,WAAW,CAACiO,GAEhCM,EAAOe,UAAU,CAACtP,WAAW,CAACuO,EAItC,CAqBA,MAAMxD,YAAYtP,CAAgB,CAAEqP,CAAY,CAAE,CAC9C,IAAM7Y,EAAQ,IAAI,CAACA,KAAK,CAepB+d,EAAoBC,EAAkBpR,EAAO,KAAMqR,EAWvD,GANInP,GAAQ9O,EAAM4S,UAAU,EAAI,CAACtC,GAAU8L,eAAe,CAAC1a,MAAM,EAC7D4O,GAAU8L,eAAe,CAAC7b,IAAI,CAAC,aAAc,UAAW,cAAe,SAAU,cAAe,mBAAoB,YAAa,UAAW,SAAU,eAAgB,QAAS,cAAe,UAAW,cAAe,WAAY,WAAY,cAAe,OAAQ,OAAQ,UAAW,aAAc,cAAe,kBAAmB,aAAc,gBAAiB,cAAe,MAAO,OAK/X,AAACuO,GACAtF,CAAAA,AAA0B,oBAA1BA,EAAiB7G,IAAI,EAClB3C,EAAM4H,SAAS,CAAC0G,oBAAoB,CAAC,SAAS5M,MAAM,EAChD8H,AAA0B,kBAA1BA,EAAiB7G,IAAI,AAAmB,GAAQ6G,AAA0B,oBAA1BA,EAAiB7G,IAAI,EArBtE,EAAE,CAACub,IAAI,CAAC5e,IAAI,CAACU,EAAM4H,SAAS,CAAC0G,oBAAoB,CAAC,SAAU,SAAUiD,CAAK,EAC9E,IAAM3E,EAAO2E,EAAM4M,YAAY,CAAC,QAChC,MAAQvR,AAAS,KAATA,GACJ,AAAgB,UAAhB,OAAOA,GACPA,AAA0B,IAA1BA,EAAK5F,OAAO,CAAC,QACrB,GAiBsB,YACtB,MAAM,IAAI,CAACkS,gBAAgB,CAAC1P,EAAkB,AAAIgC,MAAM,qDAI5D,IAAM4S,EAAelP,EAAmBlP,EAAO,SAAU,AAACuL,IACtDyS,EAAmBzS,EAAE8P,SAAS,CAAC5a,OAAO,CAGtCwd,EAASF,AAFTA,CAAAA,EACIxS,EAAE8P,SAAS,CAACzT,SAAS,CAACyW,SAAS,CAAC,CAAA,EAAI,GACTN,EAC1BzP,oBAAoB,CAAC,UAAY,EAAE,AAC5C,GACA,GAAI,KAvCQ0D,EA6CR,IAAK,IAAMT,KAJX,IAAI,CAACwH,eAAe,CAACvP,EAAkBqP,GAEnBoF,EAASK,MAAMC,IAAI,CAACN,GAAU,EAAE,EAIhD,GADArR,EAAO2E,EAAMiN,cAAc,CAAC,+BAAgC,QAClD,CACNlO,GAAU0H,eAAe,CAAG,CAAA,EAC5B,IAAM1W,EAAU,MAAMgP,GAAUO,cAAc,CAACjE,EAAMpD,GAAkBpG,OAAS,EAAGoG,GAAkB7G,MAAQ,aAE7G4O,EAAMkN,cAAc,CAAC,+BAAgC,OAAQnd,EAEjE,MAEIiQ,EAAM8L,UAAU,CAACtP,WAAW,CAACwD,GAIrC,IAAMmN,GA3DE1M,EA2DsB+L,GAAoBvE,UA3DlClJ,GAAUyB,WAAW,CAACC,GAAO,GAAIgM,IA+DjD,GAAIU,EAAa1X,OAAO,CAAC,kBAAoB,IACzCwC,AAA0B,kBAA1BA,EAAiB7G,IAAI,EACpBmM,CAAAA,GACGtF,AAA0B,oBAA1BA,EAAiB7G,IAAI,AAAqB,EAC9C,MAAM,AAAI6I,MAAM,0DAOpB,OAHI,MAAMjJ,AA1EwB,IAAI,CA0ElBkC,WAAW,CAACia,EAAclP,EAAO,CAAE1C,SAAUvK,AA1E/B,IAAI,CA0EqC0W,WAAW,EAAG,EAAGzP,IAGrFkV,CACX,CACA,MAAOzd,EAAO,CACV,MAAM,IAAI,CAACiY,gBAAgB,CAAC1P,EAAkBvI,EAClD,QACQ,CAEJmd,GACJ,CACJ,CAYAhK,eAAeuK,CAAM,CAAE,CACnB,IAAM3e,EAAQ,IAAI,CAACA,KAAK,CAAE,CAAEwV,mBAAAA,CAAkB,CAAE,CAAGxV,EACnD,AAEAwV,CAAAA,EACI,CACIA,EAAmBC,QAAQ,CAC3BD,EAAmBoJ,kBAAkB,CACxC,CACD,CAAC5e,EAAM4H,SAAS,CAAC,AAAD,EAAGjH,OAAO,CAAC,SAAUke,CAAG,EACxCF,EAAO7Q,WAAW,CAAC+Q,EACvB,EACJ,CAiBA1a,OAAQ,CACJ,IAAMnE,EAAQ,IAAI,CAACA,KAAK,AAEpB,CAAA,IAAI,CAACyU,UAAU,GAGnBnE,GAAUqE,aAAa,CAAG3U,EACtB,AAAC+O,GACD,IAAI,CAAC6F,WAAW,GAIpBlD,WAAW,KACPzC,EAAc6P,KAAK,GACnB7P,EAAc9K,KAAK,GAEf,AAAC4K,GACD2C,WAAW,KACP1R,EAAMuC,SAAS,EAAEwR,YACrB,EAAG,IAEX,EAAG,GACP,CASAgL,QAAS,CACL,IAAMxc,EAAY,IAAI,CAAE,CAAEvC,MAAAA,CAAK,CAAES,QAAAA,CAAO,CAAE,CAAG8B,EAAWyc,EAAUzc,GAAWyc,SAAW,CAACzc,GAAWkO,YAAY/O,MAChHa,CAAAA,EAAUiO,YAAY,CAAG,EACrBjO,EAAUyc,OAAO,EACjBzc,EAAU+U,OAAO,GAEjB0H,GAAWve,AAAoB,CAAA,IAApBA,EAAQkS,OAAO,GAC1BpQ,EAAU4T,MAAM,CAAG,EAAE,CACrB5T,EAAUuR,KAAK,EAAKvR,CAAAA,EAAUuR,KAAK,CAAG9T,EAAMyS,QAAQ,CAACwM,CAAC,CAAC,mBAAmB1L,IAAI,CAAC,CAC3EK,OAAQ,CACZ,GAAGC,GAAG,EAAC,EACPhE,GAAqBpP,GAAS4C,QAAS,SAAU2P,CAAM,EACnDzQ,EAAUiQ,SAAS,CAACQ,EACxB,GACAzQ,EAAUyc,OAAO,CAAG,CAAA,EAE5B,CASAzF,qBAAsB,CAClB+E,MAAMC,IAAI,CAAC,IAAI,CAACve,KAAK,CAAC4H,SAAS,CAACsX,gBAAgB,CAAC,MAAMve,OAAO,CAAC,AAAC4V,IAC5D,CAAC,QAAS,OAAQ,aAAc,SAAS,CAAC5V,OAAO,CAAC,AAACxB,IAC/C,IAAMggB,EAAY5I,EAAQ4H,YAAY,CAAChf,EACnCggB,CAAAA,GAAWC,SAAS,SACpB7I,EAAQqH,YAAY,CAACze,EAAMie,iBAAiB7G,GAAS8I,gBAAgB,CAAClgB,IAE1E,IAAMmgB,EAAa/I,EAAQhC,KAAK,EAAE,CAACpV,EAAK,AACpCmgB,CAAAA,GAAYF,SAAS,SACrB7I,CAAAA,EAAQhC,KAAK,CAACpV,EAAK,CACfie,iBAAiB7G,GAAS8I,gBAAgB,CAAClgB,EAAI,CAE3D,EACJ,EACJ,CAcAqB,OAAOgJ,CAAgB,CAAE9I,CAAM,CAAE,CAC7B,IAAI,CAACse,OAAO,CAAG,CAAA,EACfpP,GAAM,CAAA,EAAM,IAAI,CAACnP,OAAO,CAAE+I,GACtBsG,GAAKpP,EAAQ,CAAA,IACb,IAAI,CAACV,KAAK,CAACU,MAAM,EAEzB,CACJ,CAMA4P,GAAU8L,eAAe,CAAG,EAAE,CAE9B9L,GAAU4L,cAAc,CAAG,CACvB,IACA,sCACA,SACA,4BACA,eACA,0BACA,cACA,oBACA,cACA,WACA,QACH,CAED5L,GAAUqN,kBAAkB,CAAG,CAC3B,OACA,SACA,gBACA,iBACA,cACA,aACA,IACA,IACH,CAEDrN,GAAUqB,mBAAmB,CAAG7C,AAAO,MAAPA,EAChCwB,GAAU4M,gBAAgB,CAAG,CACzB,WACA,OACA,OACH,CAMD,AAAC,SAAU5M,CAAS,EAiFhB,SAASiP,EAAcvf,CAAK,EACxB,IAAMuC,EAAYvC,EAAMuC,SAAS,CAC7BA,IACAA,EAAUwc,MAAM,GAEhB7P,EAAmBlP,EAAO,SAAU,WAChC,IAAI,CAACuC,SAAS,EAAEwc,QACpB,GAEA7P,EAAmBlP,EAAO,UAAW,WACjC,IAAI,CAACuC,SAAS,EAAE+U,SACpB,GA8BR,CAYA,SAASkI,IACL,IAAMxf,EAAQ,IAAI,AAEdA,CAAAA,EAAMS,OAAO,CAAC8B,SAAS,GAOvBvC,EAAMuC,SAAS,CAAG,IAAI+N,EAAUtQ,EAAOA,EAAMS,OAAO,CAAC8B,SAAS,EAI9D3B,EACKb,OAAO,CAACC,GAAOC,UAAU,CACzBI,SAAS,CAAC,CAACI,EAASC,KACjBV,EAAMuC,SAAS,GACfvC,EAAMuC,SAAS,CAACyc,OAAO,CAAG,CAAA,EAC1BpP,GAAM,CAAA,EAAM5P,EAAMS,OAAO,CAACR,UAAU,CAAEQ,GAClCqP,GAAKpP,EAAQ,CAAA,IACbV,EAAMU,MAAM,GAGxB,GAER,CAUA,SAAS+e,EAAmB,CAAEC,QAAAA,CAAO,CAAE9gB,IAAAA,CAAG,CAAE+gB,aAAAA,CAAY,CAAE,EACtD,IAAMnW,EAAmB,IAAI,CAAC/I,OAAO,CAAC8B,SAAS,CAAE,CAAE0C,MAAAA,CAAK,CAAEC,cAAAA,EAAgB,CAAC,CAAEG,cAAAA,CAAa,CAAEC,MAAAA,EAAQ,CAAC,CAAE,CAAGsK,GAAM,IAAI,CAACnP,OAAO,CAACR,UAAU,EAAE4E,cAAe2E,GAAkBnG,SAASC,eAAgBsc,EAAQF,EAAQpa,KAAK,CAAGqa,EAAcE,EAAcva,EAAQJ,EAC1PsE,CAAAA,GAAkBmJ,SAAW,CAAA,CAAG,GACjC/T,AAAQ,UAARA,GACAqG,AAAU,UAAVA,GACAI,AAAkB,QAAlBA,GACIua,EAAQ,EAAIC,IACRD,EAAQC,EACRH,EAAQpa,KAAK,EAAIua,EAEZ,IAAI,CAACrM,KAAK,EAAEsM,aAAe,QAChCJ,CAAAA,EAAQjZ,CAAC,EAAIoZ,EAAcD,EAAQ,CAAA,EAInD,CApHAtP,EAAUvQ,OAAO,CA7CjB,SAAiB2H,CAAU,CAAEX,CAAgB,EACzCI,EAA2BpH,OAAO,CAACgH,GACnCgZ,AAz0DmDtY,EAy0D9B1H,OAAO,CAAC2H,GAExBqI,GAAqBpB,EAAoB,eAI9Ca,EAAO,AAAC1P,IAAuGV,SAAS,CAAE,CACtHkF,YAAa,eAAgBkF,CAAgB,CAAEqP,CAAY,EACvD,MAAM,IAAI,CAACtW,SAAS,EAAE+B,YAAYkF,EAAkBqP,EAExD,EACAO,aAAc,SAAUC,CAAgB,EACpC,OAAO,IAAI,CAAC9W,SAAS,EAAE6W,aAAaC,EACxC,EACAJ,YAAa,WACT,OAAO,IAAI,CAAC1W,SAAS,EAAE0W,aAC3B,EACAU,OAAQ,SAAUd,CAAY,EAC1B,OAAO,IAAI,CAACtW,SAAS,EAAEoX,OAAOd,EAClC,EACA1U,MAAO,WACH,OAAO,IAAI,CAAC5B,SAAS,EAAE4B,OAC3B,CACJ,GACAuD,EAAWtI,SAAS,CAAC4gB,SAAS,CAACzf,IAAI,CAACgf,GACpCrQ,EAAmBxH,EAAY,YAAa8X,GAC5CtQ,EAAmBxH,EAAY,cAAe+X,GAC1C1Q,GACAE,EAAcgR,UAAU,CAAC,SAASC,WAAW,CAAC,SAAUC,CAAQ,EACvD7P,EAAUqE,aAAa,GAGxBwL,EAASC,OAAO,CAChB9P,EAAUqE,aAAa,CAACpS,SAAS,EAAEqS,cAGnCtE,EAAUqE,aAAa,CAACpS,SAAS,EAAEwR,aAE3C,GAGJ9G,EAAW3K,GACf,CAsHJ,EAAGgO,IAAcA,CAAAA,GAAY,CAAC,CAAA,GAMD,IAAM+P,GAAuB/P,GA6GpDgQ,GAAK5gB,GAEX4gB,CAAAA,GAAEhQ,SAAS,CAAG+P,GAEdC,GAAEvW,aAAa,CAAGuW,GAAEvW,aAAa,EAv3DwBA,EAw3DzDuW,GAAEtW,IAAI,CAAGsW,GAAEvW,aAAa,CAACC,IAAI,CAC7BsW,GAAEzU,OAAO,CAAGyU,GAAEvW,aAAa,CAAC8B,OAAO,CACnCyU,GAAExU,IAAI,CAAGwU,GAAEvW,aAAa,CAAC+B,IAAI,CAE7BuU,GAAoBtgB,OAAO,CAACugB,GAAEC,KAAK,CAAED,GAAEE,QAAQ,EAClB,IAAMhhB,GAAkBE,IAG3C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}