{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/parallel-coordinates\n * @requires highcharts\n *\n * Support for parallel coordinates in Highcharts\n *\n * (c) 2010-2025 Pawel Fus\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Templating\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/parallel-coordinates\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"Templating\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/parallel-coordinates\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Templating\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Templating\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__984__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ }),\n\n/***/ 984:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__984__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ parallel_coordinates_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Extensions/ParallelCoordinates/ParallelCoordinatesDefaults.js\n/* *\n *\n *  Parallel coordinates module\n *\n *  (c) 2010-2025 Pawel Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * @optionparent chart\n */\nconst chartDefaults = {\n    /**\n     * Flag to render charts as a parallel coordinates plot. In a parallel\n     * coordinates plot (||-coords) by default all required yAxes are generated\n     * and the legend is disabled. This feature requires\n     * `modules/parallel-coordinates.js`.\n     *\n     * @sample {highcharts} /highcharts/demo/parallel-coordinates/\n     *         Parallel coordinates demo\n     * @sample {highcharts} highcharts/parallel-coordinates/polar/\n     *         Star plot, multivariate data in a polar chart\n     *\n     * @since    6.0.0\n     * @product  highcharts\n     * @requires modules/parallel-coordinates\n     */\n    parallelCoordinates: false,\n    /**\n     * Common options for all yAxes rendered in a parallel coordinates plot.\n     * This feature requires `modules/parallel-coordinates.js`.\n     *\n     * The default options are:\n     * ```js\n     * parallelAxes: {\n     *    lineWidth: 1,       // classic mode only\n     *    gridlinesWidth: 0,  // classic mode only\n     *    title: {\n     *        text: '',\n     *        reserveSpace: false\n     *    },\n     *    labels: {\n     *        x: 0,\n     *        y: 0,\n     *        align: 'center',\n     *        reserveSpace: false\n     *    },\n     *    offset: 0\n     * }\n     * ```\n     *\n     * @sample {highcharts} highcharts/parallel-coordinates/parallelaxes/\n     *         Set the same tickAmount for all yAxes\n     *\n     * @extends   yAxis\n     * @since     6.0.0\n     * @product   highcharts\n     * @excluding alternateGridColor, breaks, id, gridLineColor,\n     *            gridLineDashStyle, gridLineWidth, minorGridLineColor,\n     *            minorGridLineDashStyle, minorGridLineWidth, plotBands,\n     *            plotLines, angle, gridLineInterpolation, maxColor, maxZoom,\n     *            minColor, scrollbar, stackLabels, stops,\n     * @requires  modules/parallel-coordinates\n     */\n    parallelAxes: {\n        lineWidth: 1,\n        /**\n         * Titles for yAxes are taken from\n         * [xAxis.categories](#xAxis.categories). All options for `xAxis.labels`\n         * applies to parallel coordinates titles. For example, to style\n         * categories, use [xAxis.labels.style](#xAxis.labels.style).\n         *\n         * @excluding align, enabled, margin, offset, position3d, reserveSpace,\n         *            rotation, skew3d, style, text, useHTML, x, y\n         */\n        title: {\n            text: '',\n            reserveSpace: false\n        },\n        labels: {\n            x: 0,\n            y: 4,\n            align: 'center',\n            reserveSpace: false\n        },\n        offset: 0\n    }\n};\nconst xAxisDefaults = {\n    lineWidth: 0,\n    tickLength: 0,\n    opposite: true,\n    type: 'category'\n};\n/**\n * Parallel coordinates only. Format that will be used for point.y\n * and available in [tooltip.pointFormat](#tooltip.pointFormat) as\n * `{point.formattedValue}`. If not set, `{point.formattedValue}`\n * will use other options, in this order:\n *\n * 1. [yAxis.labels.format](#yAxis.labels.format) will be used if\n *    set\n *\n * 2. If yAxis is a category, then category name will be displayed\n *\n * 3. If yAxis is a datetime, then value will use the same format as\n *    yAxis labels\n *\n * 4. If yAxis is linear/logarithmic type, then simple value will be\n *    used\n *\n * @sample {highcharts}\n *         /highcharts/parallel-coordinates/tooltipvalueformat/\n *         Different tooltipValueFormats's\n *\n * @type      {string}\n * @default   undefined\n * @since     6.0.0\n * @product   highcharts\n * @requires  modules/parallel-coordinates\n * @apioption yAxis.tooltipValueFormat\n */\n''; // Keeps doclets above separate in JS file\n/* *\n *\n *  Default Options\n *\n * */\nconst ParallelCoordinatesDefaults = {\n    chart: chartDefaults,\n    xAxis: xAxisDefaults\n};\n/* harmony default export */ const ParallelCoordinates_ParallelCoordinatesDefaults = (ParallelCoordinatesDefaults);\n\n;// ./code/es-modules/Extensions/ParallelCoordinates/ParallelAxis.js\n/* *\n *\n *  Parallel coordinates module\n *\n *  (c) 2010-2025 Pawel Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { addEvent, arrayMax, arrayMin, isNumber, merge, pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * Support for parallel axes.\n * @private\n * @class\n */\nclass ParallelAxisAdditions {\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    constructor(axis) {\n        this.axis = axis;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Set predefined left+width and top+height (inverted) for yAxes.\n     * This method modifies options param.\n     *\n     * @private\n     *\n     * @param  {Array<string>} axisPosition\n     * ['left', 'width', 'height', 'top'] or ['top', 'height', 'width', 'left']\n     * for an inverted chart.\n     *\n     * @param  {Highcharts.AxisOptions} options\n     * Axis options.\n     */\n    setPosition(axisPosition, options) {\n        const parallel = this, axis = parallel.axis, chart = axis.chart, fraction = ((parallel.position || 0) + 0.5) /\n            (chart.parallelInfo.counter + 1);\n        if (chart.polar) {\n            options.angle = 360 * fraction;\n        }\n        else {\n            options[axisPosition[0]] = 100 * fraction + '%';\n            axis[axisPosition[1]] = options[axisPosition[1]] = 0;\n            // In case of chart.update(inverted), remove old options:\n            axis[axisPosition[2]] = options[axisPosition[2]] = null;\n            axis[axisPosition[3]] = options[axisPosition[3]] = null;\n        }\n    }\n}\n/* *\n *\n *  Composition\n *\n * */\nvar ParallelAxis;\n(function (ParallelAxis) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Adds support for parallel axes.\n     * @private\n     */\n    function compose(AxisClass) {\n        if (!AxisClass.keepProps.includes('parallel')) {\n            const axisCompo = AxisClass;\n            // On update, keep parallel additions.\n            AxisClass.keepProps.push('parallel');\n            addEvent(axisCompo, 'init', onInit);\n            addEvent(axisCompo, 'afterSetOptions', onAfterSetOptions);\n            addEvent(axisCompo, 'getSeriesExtremes', onGetSeriesExtremes);\n        }\n    }\n    ParallelAxis.compose = compose;\n    /**\n     * Update default options with predefined for a parallel coords.\n     * @private\n     */\n    function onAfterSetOptions(e) {\n        const axis = this, chart = axis.chart, parallelCoordinates = axis.parallelCoordinates;\n        let axisPosition = [\n            'left', 'width', 'height', 'top'\n        ];\n        if (chart.hasParallelCoordinates) {\n            if (chart.inverted) {\n                axisPosition = axisPosition.reverse();\n            }\n            if (axis.isXAxis) {\n                axis.options = merge(axis.options, ParallelCoordinates_ParallelCoordinatesDefaults.xAxis, e.userOptions);\n            }\n            else {\n                const axisIndex = chart.yAxis.indexOf(axis); // #13608\n                axis.options = merge(axis.options, axis.chart.options.chart.parallelAxes, e.userOptions);\n                parallelCoordinates.position = pick(parallelCoordinates.position, axisIndex >= 0 ? axisIndex : chart.yAxis.length);\n                parallelCoordinates.setPosition(axisPosition, axis.options);\n            }\n        }\n    }\n    /**\n     * Each axis should gather extremes from points on a particular position in\n     * series.data. Not like the default one, which gathers extremes from all\n     * series bind to this axis. Consider using series.points instead of\n     * series.yData.\n     * @private\n     */\n    function onGetSeriesExtremes(e) {\n        const axis = this;\n        const chart = axis.chart;\n        const parallelCoordinates = axis.parallelCoordinates;\n        if (!parallelCoordinates) {\n            return;\n        }\n        if (chart && chart.hasParallelCoordinates && !axis.isXAxis) {\n            const index = parallelCoordinates.position;\n            let currentPoints = [];\n            axis.series.forEach(function (series) {\n                if (series.visible && isNumber(index)) {\n                    currentPoints = (series.pointArrayMap || ['y'])\n                        .reduce((currentPoints, key) => [\n                        ...currentPoints,\n                        series.getColumn(key)?.[index] ?? null\n                    ], currentPoints);\n                }\n            });\n            currentPoints = currentPoints.filter(isNumber);\n            axis.dataMin = arrayMin(currentPoints);\n            axis.dataMax = arrayMax(currentPoints);\n            e.preventDefault();\n        }\n    }\n    /**\n     * Add parallel addition\n     * @private\n     */\n    function onInit() {\n        const axis = this;\n        if (!axis.parallelCoordinates) {\n            axis.parallelCoordinates = new ParallelAxisAdditions(axis);\n        }\n    }\n})(ParallelAxis || (ParallelAxis = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const ParallelCoordinates_ParallelAxis = (ParallelAxis);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Templating\"],\"commonjs\":[\"highcharts\",\"Templating\"],\"commonjs2\":[\"highcharts\",\"Templating\"],\"root\":[\"Highcharts\",\"Templating\"]}\nvar highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_ = __webpack_require__(984);\nvar highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default = /*#__PURE__*/__webpack_require__.n(highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_);\n;// ./code/es-modules/Extensions/ParallelCoordinates/ParallelSeries.js\n/* *\n *\n *  Parallel coordinates module\n *\n *  (c) 2010-2025 Pawel Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { format } = (highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default());\n\nconst { addEvent: ParallelSeries_addEvent, defined, erase, extend, insertItem, isArray, isNumber: ParallelSeries_isNumber, pushUnique } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\nvar ParallelSeries;\n(function (ParallelSeries) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /** @private */\n    function compose(SeriesClass) {\n        if (pushUnique(composed, 'ParallelSeries')) {\n            const CompoClass = SeriesClass;\n            ParallelSeries_addEvent(CompoClass, 'afterTranslate', onSeriesAfterTranslate, { order: 1 });\n            ParallelSeries_addEvent(CompoClass, 'bindAxes', onSeriesBindAxes);\n            ParallelSeries_addEvent(CompoClass, 'destroy', onSeriesDestroy);\n            ParallelSeries_addEvent(SeriesClass, 'afterGeneratePoints', onSeriesAfterGeneratePoints);\n        }\n    }\n    ParallelSeries.compose = compose;\n    /**\n     * Translate each point using corresponding yAxis.\n     * @private\n     */\n    function onSeriesAfterTranslate() {\n        const series = this, chart = this.chart, points = series.points, dataLength = points && points.length;\n        let closestPointRangePx = Number.MAX_VALUE, lastPlotX, point;\n        if (this.chart.hasParallelCoordinates) {\n            for (let i = 0; i < dataLength; i++) {\n                point = points[i];\n                if (defined(point.y)) {\n                    if (chart.polar) {\n                        point.plotX = chart.yAxis[i].angleRad || 0;\n                    }\n                    else if (chart.inverted) {\n                        point.plotX = (chart.plotHeight -\n                            chart.yAxis[i].top +\n                            chart.plotTop);\n                    }\n                    else {\n                        point.plotX = chart.yAxis[i].left - chart.plotLeft;\n                    }\n                    point.clientX = point.plotX;\n                    point.plotY = chart.yAxis[i]\n                        .translate(point.y, false, true, void 0, true);\n                    // Range series (#15752)\n                    if (ParallelSeries_isNumber(point.high)) {\n                        point.plotHigh = chart.yAxis[i].translate(point.high, false, true, void 0, true);\n                    }\n                    if (typeof lastPlotX !== 'undefined') {\n                        closestPointRangePx = Math.min(closestPointRangePx, Math.abs(point.plotX - lastPlotX));\n                    }\n                    lastPlotX = point.plotX;\n                    point.isInside = chart.isInsidePlot(point.plotX, point.plotY, { inverted: chart.inverted });\n                }\n                else {\n                    point.isNull = true;\n                }\n            }\n            this.closestPointRangePx = closestPointRangePx;\n        }\n    }\n    /**\n     * Bind each series to each yAxis. yAxis needs a reference to all series to\n     * calculate extremes.\n     * @private\n     */\n    function onSeriesBindAxes(e) {\n        const series = this, chart = series.chart;\n        if (chart.hasParallelCoordinates) {\n            const series = this;\n            for (const axis of chart.axes) {\n                insertItem(series, axis.series);\n                axis.isDirty = true;\n            }\n            series.xAxis = chart.xAxis[0];\n            series.yAxis = chart.yAxis[0];\n            e.preventDefault();\n        }\n    }\n    /**\n     * On destroy, we need to remove series from each `axis.series`.\n     * @private\n     */\n    function onSeriesDestroy() {\n        const series = this, chart = series.chart;\n        if (chart.hasParallelCoordinates) {\n            for (const axis of (chart.axes || [])) {\n                if (axis && axis.series) {\n                    erase(axis.series, series);\n                    axis.isDirty = axis.forceRedraw = true;\n                }\n            }\n        }\n    }\n    /**\n     * @private\n     */\n    function onSeriesAfterGeneratePoints() {\n        const chart = this.chart;\n        if (chart?.hasParallelCoordinates) {\n            for (const point of this.points) {\n                const yAxis = chart.yAxis[point.x || 0], yAxisOptions = yAxis.options, labelFormat = yAxisOptions.tooltipValueFormat ??\n                    yAxisOptions.labels.format;\n                let formattedValue;\n                if (labelFormat) {\n                    formattedValue = format(labelFormat, extend(point, { value: point.y }), chart);\n                }\n                else if (yAxis.dateTime) {\n                    formattedValue = chart.time.dateFormat(chart.time.resolveDTLFormat(yAxisOptions.dateTimeLabelFormats?.[yAxis.tickPositions.info?.unitName || 'year'] || '').main, point.y ?? void 0);\n                }\n                else if (isArray(yAxisOptions.categories)) {\n                    formattedValue = yAxisOptions.categories[point.y ?? -1];\n                }\n                else {\n                    formattedValue = String(point.y ?? '');\n                }\n                point.formattedValue = formattedValue;\n            }\n        }\n    }\n})(ParallelSeries || (ParallelSeries = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const ParallelCoordinates_ParallelSeries = (ParallelSeries);\n\n;// ./code/es-modules/Extensions/ParallelCoordinates/ParallelCoordinates.js\n/* *\n *\n *  Parallel coordinates module\n *\n *  (c) 2010-2025 Pawel Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\nconst { addEvent: ParallelCoordinates_addEvent, defined: ParallelCoordinates_defined, merge: ParallelCoordinates_merge, splat } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass ChartAdditions {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(chart) {\n        this.chart = chart;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Define how many parellel axes we have according to the longest dataset.\n     * This is quite heavy - loop over all series and check series.data.length\n     * Consider:\n     *\n     * - make this an option, so user needs to set this to get better\n     *   performance\n     *\n     * - check only first series for number of points and assume the rest is the\n     *   same\n     *\n     * @private\n     * @function Highcharts.Chart#setParallelInfo\n     * @param {Highcharts.Options} options\n     * User options\n     * @requires modules/parallel-coordinates\n     */\n    setParallelInfo(options) {\n        const chart = (this.chart ||\n            this), seriesOptions = options.series;\n        chart.parallelInfo = {\n            counter: 0\n        };\n        for (const series of seriesOptions) {\n            if (series.data) {\n                chart.parallelInfo.counter = Math.max(chart.parallelInfo.counter, series.data.length - 1);\n            }\n        }\n    }\n}\n/* *\n *\n *  Composition\n *\n * */\nvar ParallelCoordinates;\n(function (ParallelCoordinates) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /** @private */\n    function compose(AxisClass, ChartClass, highchartsDefaultOptions, SeriesClass) {\n        ParallelCoordinates_ParallelAxis.compose(AxisClass);\n        ParallelCoordinates_ParallelSeries.compose(SeriesClass);\n        const ChartCompo = ChartClass, addsProto = ChartAdditions.prototype, chartProto = ChartCompo.prototype;\n        if (!chartProto.setParallelInfo) {\n            chartProto.setParallelInfo = addsProto.setParallelInfo;\n            ParallelCoordinates_addEvent(ChartCompo, 'init', onChartInit);\n            ParallelCoordinates_addEvent(ChartCompo, 'update', onChartUpdate);\n            ParallelCoordinates_merge(true, highchartsDefaultOptions.chart, ParallelCoordinates_ParallelCoordinatesDefaults.chart);\n        }\n    }\n    ParallelCoordinates.compose = compose;\n    /**\n     * Initialize parallelCoordinates\n     * @private\n     */\n    function onChartInit(e) {\n        const chart = this, options = e.args[0], defaultYAxis = splat(options.yAxis || {}), newYAxes = [];\n        let yAxisLength = defaultYAxis.length;\n        /**\n         * Flag used in parallel coordinates plot to check if chart has\n         * ||-coords (parallel coords).\n         *\n         * @requires modules/parallel-coordinates\n         *\n         * @name Highcharts.Chart#hasParallelCoordinates\n         * @type {boolean}\n         */\n        chart.hasParallelCoordinates = options.chart &&\n            options.chart.parallelCoordinates;\n        if (chart.hasParallelCoordinates) {\n            chart.setParallelInfo(options);\n            // Push empty yAxes in case user did not define them:\n            for (; yAxisLength <= chart.parallelInfo.counter; yAxisLength++) {\n                newYAxes.push({});\n            }\n            if (!options.legend) {\n                options.legend = {};\n            }\n            if (options.legend &&\n                typeof options.legend.enabled === 'undefined') {\n                options.legend.enabled = false;\n            }\n            ParallelCoordinates_merge(true, options, \n            // Disable boost\n            {\n                boost: {\n                    seriesThreshold: Number.MAX_VALUE\n                },\n                plotOptions: {\n                    series: {\n                        boostThreshold: Number.MAX_VALUE\n                    }\n                }\n            });\n            options.yAxis = defaultYAxis.concat(newYAxes);\n            options.xAxis = ParallelCoordinates_merge(ParallelCoordinates_ParallelCoordinatesDefaults.xAxis, // Docs\n            splat(options.xAxis || {})[0]);\n        }\n    }\n    /**\n     * Initialize parallelCoordinates\n     * @private\n     */\n    function onChartUpdate(e) {\n        const chart = this, options = e.options;\n        if (options.chart) {\n            if (ParallelCoordinates_defined(options.chart.parallelCoordinates)) {\n                chart.hasParallelCoordinates =\n                    options.chart.parallelCoordinates;\n            }\n            chart.options.chart.parallelAxes = ParallelCoordinates_merge(chart.options.chart.parallelAxes, options.chart.parallelAxes);\n        }\n        if (chart.hasParallelCoordinates) {\n            // (#10081)\n            if (options.series) {\n                chart.setParallelInfo(options);\n            }\n            for (const axis of chart.yAxis) {\n                axis.update({}, false);\n            }\n        }\n    }\n})(ParallelCoordinates || (ParallelCoordinates = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const ParallelCoordinates_ParallelCoordinates = (ParallelCoordinates);\n\n;// ./code/es-modules/masters/modules/parallel-coordinates.js\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nParallelCoordinates_ParallelCoordinates.compose(G.Axis, G.Chart, G.defaultOptions, G.Series);\n/* harmony default export */ const parallel_coordinates_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__984__", "ParallelAxis", "ParallelSeries", "ParallelCoordinates", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "parallel_coordinates_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "ParallelCoordinates_ParallelCoordinatesDefaults", "chart", "parallelCoordinates", "parallelAxes", "lineWidth", "title", "text", "reserveSpace", "labels", "x", "y", "align", "offset", "xAxis", "tick<PERSON><PERSON>th", "opposite", "type", "addEvent", "arrayMax", "arrayMin", "isNumber", "merge", "pick", "ParallelAxisAdditions", "constructor", "axis", "setPosition", "axisPosition", "options", "parallel", "fraction", "position", "parallelInfo", "counter", "polar", "angle", "onAfterSetOptions", "e", "hasParallelCoordinates", "inverted", "reverse", "isXAxis", "userOptions", "axisIndex", "yAxis", "indexOf", "length", "onGetSeriesExtremes", "index", "currentPoints", "series", "for<PERSON>ach", "visible", "pointArrayMap", "reduce", "getColumn", "filter", "dataMin", "dataMax", "preventDefault", "onInit", "compose", "AxisClass", "keepProps", "includes", "push", "ParallelCoordinates_ParallelAxis", "highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_", "highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default", "composed", "format", "ParallelSeries_addEvent", "defined", "erase", "extend", "insertItem", "isArray", "ParallelSeries_isNumber", "pushUnique", "onSeriesAfterTranslate", "points", "dataLength", "closestPointRangePx", "Number", "MAX_VALUE", "lastPlotX", "point", "i", "plotX", "angleRad", "plotHeight", "top", "plotTop", "left", "plotLeft", "clientX", "plotY", "translate", "high", "plotHigh", "Math", "min", "abs", "isInside", "isInsidePlot", "isNull", "onSeriesBindAxes", "axes", "isDirty", "onSeriesDestroy", "forceRedraw", "onSeriesAfterGeneratePoints", "formattedValue", "yAxisOptions", "labelFormat", "tooltipValueFormat", "value", "dateTime", "time", "dateFormat", "resolveDTLFormat", "dateTimeLabelFormats", "tickPositions", "info", "unitName", "main", "categories", "String", "SeriesClass", "order", "ParallelCoordinates_ParallelSeries", "ParallelCoordinates_addEvent", "ParallelCoordinates_defined", "ParallelCoordinates_merge", "splat", "ChartAdditions", "setParallelInfo", "seriesOptions", "data", "max", "onChartInit", "args", "defaultYAxis", "newYAxes", "yAxis<PERSON><PERSON>th", "legend", "enabled", "boost", "seriesThreshold", "plotOptions", "boostThreshold", "concat", "onChartUpdate", "update", "ChartClass", "highchartsDefaultOptions", "addsProto", "chartProto", "ChartCompo", "ParallelCoordinates_ParallelCoordinates", "G", "Axis", "Chart", "defaultOptions", "Series"], "mappings": "CAWA,AAXA;;;;;;;;;;CAUC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,UAAa,EACxE,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,0CAA2C,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,UAAa,CAAE,GAC/H,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,0CAA0C,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,UAAa,EAEnHA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,UAAa,CACnF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IA4SNC,EAgIAC,EA4MAC,EAxnBUC,EAAuB,CAE/B,IACC,AAACV,IAERA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,AAACL,IAERA,EAAOD,OAAO,CAAGO,CAEX,CAEI,EAGIK,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaf,OAAO,CAG5B,IAAIC,EAASW,CAAwB,CAACE,EAAS,CAAG,CAGjDd,QAAS,CAAC,CACX,EAMA,OAHAW,CAAmB,CAACG,EAAS,CAACb,EAAQA,EAAOD,OAAO,CAAEa,GAG/CZ,EAAOD,OAAO,AACtB,CAMCa,EAAoBI,CAAC,CAAG,AAAChB,IACxB,IAAIiB,EAASjB,GAAUA,EAAOkB,UAAU,CACvC,IAAOlB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAY,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACpB,EAASsB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACxB,EAASuB,IAC5EE,OAAOC,cAAc,CAAC1B,EAASuB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GA+IxF,IAAME,EAJC,CAChCC,MAtHkB,CAgBlBC,oBAAqB,CAAA,EAqCrBC,aAAc,CACVC,UAAW,EAUXC,MAAO,CACHC,KAAM,GACNC,aAAc,CAAA,CAClB,EACAC,OAAQ,CACJC,EAAG,EACHC,EAAG,EACHC,MAAO,SACPJ,aAAc,CAAA,CAClB,EACAK,OAAQ,CACZ,CACJ,EA2CIC,MA1CkB,CAClBT,UAAW,EACXU,WAAY,EACZC,SAAU,CAAA,EACVC,KAAM,UACV,CAsCA,EAkBM,CAAEC,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAE,CAAIvB,GAWjE,OAAMwB,EAMFC,YAAYC,CAAI,CAAE,CACd,IAAI,CAACA,IAAI,CAAGA,CAChB,CAmBAC,YAAYC,CAAY,CAAEC,CAAO,CAAE,CAC/B,IAAuBH,EAAOI,AAAb,IAAI,CAAkBJ,IAAI,CAAExB,EAAQwB,EAAKxB,KAAK,CAAE6B,EAAW,AAAC,CAAA,AAACD,CAAAA,AAA7D,IAAI,CAAkEE,QAAQ,EAAI,CAAA,EAAK,EAAE,EACrG9B,CAAAA,EAAM+B,YAAY,CAACC,OAAO,CAAG,CAAA,CAC9BhC,CAAAA,EAAMiC,KAAK,CACXN,EAAQO,KAAK,CAAG,IAAML,GAGtBF,CAAO,CAACD,CAAY,CAAC,EAAE,CAAC,CAAG,IAAMG,EAAW,IAC5CL,CAAI,CAACE,CAAY,CAAC,EAAE,CAAC,CAAGC,CAAO,CAACD,CAAY,CAAC,EAAE,CAAC,CAAG,EAEnDF,CAAI,CAACE,CAAY,CAAC,EAAE,CAAC,CAAGC,CAAO,CAACD,CAAY,CAAC,EAAE,CAAC,CAAG,KACnDF,CAAI,CAACE,CAAY,CAAC,EAAE,CAAC,CAAGC,CAAO,CAACD,CAAY,CAAC,EAAE,CAAC,CAAG,KAE3D,CACJ,EAOA,AAAC,SAAUzD,CAAY,EA8BnB,SAASkE,EAAkBC,CAAC,EACxB,IAAmBpC,EAAQwB,AAAd,IAAI,CAAexB,KAAK,CAAEC,EAAsBuB,AAAhD,IAAI,CAAiDvB,mBAAmB,CACjFyB,EAAe,CACf,OAAQ,QAAS,SAAU,MAC9B,CACD,GAAI1B,EAAMqC,sBAAsB,CAI5B,GAHIrC,EAAMsC,QAAQ,EACdZ,CAAAA,EAAeA,EAAaa,OAAO,EAAC,EAEpCf,AARK,IAAI,CAQJgB,OAAO,CACZhB,AATK,IAAI,CASJG,OAAO,CAAGP,EAAMI,AAThB,IAAI,CASiBG,OAAO,CAAE5B,EAAgDa,KAAK,CAAEwB,EAAEK,WAAW,MAEtG,CACD,IAAMC,EAAY1C,EAAM2C,KAAK,CAACC,OAAO,CAZhC,IAAI,CAaTpB,CAbK,IAAI,CAaJG,OAAO,CAAGP,EAAMI,AAbhB,IAAI,CAaiBG,OAAO,CAAEH,AAb9B,IAAI,CAa+BxB,KAAK,CAAC2B,OAAO,CAAC3B,KAAK,CAACE,YAAY,CAAEkC,EAAEK,WAAW,EACvFxC,EAAoB6B,QAAQ,CAAGT,EAAKpB,EAAoB6B,QAAQ,CAAEY,GAAa,EAAIA,EAAY1C,EAAM2C,KAAK,CAACE,MAAM,EACjH5C,EAAoBwB,WAAW,CAACC,EAAcF,AAfzC,IAAI,CAe0CG,OAAO,CAC9D,CAER,CAQA,SAASmB,EAAoBV,CAAC,EAE1B,IAAMpC,EAAQwB,AADD,IAAI,CACExB,KAAK,CAClBC,EAAsBuB,AAFf,IAAI,CAEgBvB,mBAAmB,CACpD,GAAKA,GAGDD,GAASA,EAAMqC,sBAAsB,EAAI,CAACb,AANjC,IAAI,CAMkCgB,OAAO,CAAE,CACxD,IAAMO,EAAQ9C,EAAoB6B,QAAQ,CACtCkB,EAAgB,EAAE,CACtBxB,AATS,IAAI,CASRyB,MAAM,CAACC,OAAO,CAAC,SAAUD,CAAM,EAC5BA,EAAOE,OAAO,EAAIhC,EAAS4B,IAC3BC,CAAAA,EAAgB,AAACC,CAAAA,EAAOG,aAAa,EAAI,CAAC,IAAI,AAAD,EACxCC,MAAM,CAAC,CAACL,EAAehE,IAAQ,IAC7BgE,EACHC,EAAOK,SAAS,CAACtE,IAAM,CAAC+D,EAAM,EAAI,KACrC,CAAEC,EAAa,CAExB,GACAA,EAAgBA,EAAcO,MAAM,CAACpC,GACrCK,AAnBS,IAAI,CAmBRgC,OAAO,CAAGtC,EAAS8B,GACxBxB,AApBS,IAAI,CAoBRiC,OAAO,CAAGxC,EAAS+B,GACxBZ,EAAEsB,cAAc,EACpB,CACJ,CAKA,SAASC,IAED,AAACnC,AADQ,IAAI,CACPvB,mBAAmB,EACzBuB,CAAAA,AAFS,IAAI,CAERvB,mBAAmB,CAAG,IAAIqB,EAFtB,IAAI,CAE4C,CAEjE,CAlEArD,EAAa2F,OAAO,CAVpB,SAAiBC,CAAS,EACjBA,EAAUC,SAAS,CAACC,QAAQ,CAAC,cAG9BF,EAAUC,SAAS,CAACE,IAAI,CAAC,YACzBhD,EAHkB6C,EAGE,OAAQF,GAC5B3C,EAJkB6C,EAIE,kBAAmB1B,GACvCnB,EALkB6C,EAKE,oBAAqBf,GAEjD,CAoEJ,EAAG7E,GAAiBA,CAAAA,EAAe,CAAC,CAAA,GAMP,IAAMgG,EAAoChG,EAGvE,IAAIiG,EAAmH5F,EAAoB,KACvI6F,EAAuI7F,EAAoBI,CAAC,CAACwF,GAejK,GAAM,CAAEE,SAAAA,CAAQ,CAAE,CAAItE,IAEhB,CAAEuE,OAAAA,CAAM,CAAE,CAAIF,IAEd,CAAEnD,SAAUsD,CAAuB,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAEC,WAAAA,CAAU,CAAEC,QAAAA,CAAO,CAAExD,SAAUyD,CAAuB,CAAEC,WAAAA,CAAU,CAAE,CAAI/E,KAO3I,AAAC,SAAU5B,CAAc,EA0BrB,SAAS4G,IACL,IAAqB9E,EAAQ,IAAI,CAACA,KAAK,CAAE+E,EAAS9B,AAAnC,IAAI,CAAsC8B,MAAM,CAAEC,EAAaD,GAAUA,EAAOlC,MAAM,CACjGoC,EAAsBC,OAAOC,SAAS,CAAEC,EAAWC,EACvD,GAAI,IAAI,CAACrF,KAAK,CAACqC,sBAAsB,CAAE,CACnC,IAAK,IAAIiD,EAAI,EAAGA,EAAIN,EAAYM,IAExBf,EAAQc,AADZA,CAAAA,EAAQN,CAAM,CAACO,EAAE,AAAD,EACE7E,CAAC,GACXT,EAAMiC,KAAK,CACXoD,EAAME,KAAK,CAAGvF,EAAM2C,KAAK,CAAC2C,EAAE,CAACE,QAAQ,EAAI,EAEpCxF,EAAMsC,QAAQ,CACnB+C,EAAME,KAAK,CAAIvF,EAAMyF,UAAU,CAC3BzF,EAAM2C,KAAK,CAAC2C,EAAE,CAACI,GAAG,CAClB1F,EAAM2F,OAAO,CAGjBN,EAAME,KAAK,CAAGvF,EAAM2C,KAAK,CAAC2C,EAAE,CAACM,IAAI,CAAG5F,EAAM6F,QAAQ,CAEtDR,EAAMS,OAAO,CAAGT,EAAME,KAAK,CAC3BF,EAAMU,KAAK,CAAG/F,EAAM2C,KAAK,CAAC2C,EAAE,CACvBU,SAAS,CAACX,EAAM5E,CAAC,CAAE,CAAA,EAAO,CAAA,EAAM,KAAK,EAAG,CAAA,GAEzCmE,EAAwBS,EAAMY,IAAI,GAClCZ,CAAAA,EAAMa,QAAQ,CAAGlG,EAAM2C,KAAK,CAAC2C,EAAE,CAACU,SAAS,CAACX,EAAMY,IAAI,CAAE,CAAA,EAAO,CAAA,EAAM,KAAK,EAAG,CAAA,EAAI,EAE/E,AAAqB,KAAA,IAAdb,GACPH,CAAAA,EAAsBkB,KAAKC,GAAG,CAACnB,EAAqBkB,KAAKE,GAAG,CAAChB,EAAME,KAAK,CAAGH,GAAU,EAEzFA,EAAYC,EAAME,KAAK,CACvBF,EAAMiB,QAAQ,CAAGtG,EAAMuG,YAAY,CAAClB,EAAME,KAAK,CAAEF,EAAMU,KAAK,CAAE,CAAEzD,SAAUtC,EAAMsC,QAAQ,AAAC,IAGzF+C,EAAMmB,MAAM,CAAG,CAAA,CAGvB,CAAA,IAAI,CAACvB,mBAAmB,CAAGA,CAC/B,CACJ,CAMA,SAASwB,EAAiBrE,CAAC,EACvB,IAAqBpC,EAAQiD,AAAd,IAAI,CAAiBjD,KAAK,CACzC,GAAIA,EAAMqC,sBAAsB,CAAE,CAE9B,IAAK,IAAMb,KAAQxB,EAAM0G,IAAI,CACzBhC,EAFW,IAAI,CAEIlD,EAAKyB,MAAM,EAC9BzB,EAAKmF,OAAO,CAAG,CAAA,CAEnB1D,CALe,IAAI,CAKZrC,KAAK,CAAGZ,EAAMY,KAAK,CAAC,EAAE,CAC7BqC,AANe,IAAI,CAMZN,KAAK,CAAG3C,EAAM2C,KAAK,CAAC,EAAE,CAC7BP,EAAEsB,cAAc,EACpB,CACJ,CAKA,SAASkD,IACL,IAAqB5G,EAAQiD,AAAd,IAAI,CAAiBjD,KAAK,CACzC,GAAIA,EAAMqC,sBAAsB,CAC5B,IAAK,IAAMb,KAASxB,EAAM0G,IAAI,EAAI,EAAE,CAC5BlF,GAAQA,EAAKyB,MAAM,GACnBuB,EAAMhD,EAAKyB,MAAM,CAJd,IAAI,EAKPzB,EAAKmF,OAAO,CAAGnF,EAAKqF,WAAW,CAAG,CAAA,EAIlD,CAIA,SAASC,IACL,IAAM9G,EAAQ,IAAI,CAACA,KAAK,CACxB,GAAIA,GAAOqC,uBACP,IAAK,IAAMgD,KAAS,IAAI,CAACN,MAAM,CAAE,CAC7B,IAEIgC,EAFEpE,EAAQ3C,EAAM2C,KAAK,CAAC0C,EAAM7E,CAAC,EAAI,EAAE,CAAEwG,EAAerE,EAAMhB,OAAO,CAAEsF,EAAcD,EAAaE,kBAAkB,EAChHF,EAAazG,MAAM,CAAC8D,MAAM,CAG1B0C,EADAE,EACiB5C,EAAO4C,EAAaxC,EAAOY,EAAO,CAAE8B,MAAO9B,EAAM5E,CAAC,AAAC,GAAIT,GAEnE2C,EAAMyE,QAAQ,CACFpH,EAAMqH,IAAI,CAACC,UAAU,CAACtH,EAAMqH,IAAI,CAACE,gBAAgB,CAACP,EAAaQ,oBAAoB,EAAE,CAAC7E,EAAM8E,aAAa,CAACC,IAAI,EAAEC,UAAY,OAAO,EAAI,IAAIC,IAAI,CAAEvC,EAAM5E,CAAC,EAAI,KAAK,GAE7KkE,EAAQqC,EAAaa,UAAU,EACnBb,EAAaa,UAAU,CAACxC,EAAM5E,CAAC,EAAI,GAAG,CAGtCqH,OAAOzC,EAAM5E,CAAC,EAAI,IAEvC4E,EAAM0B,cAAc,CAAGA,CAC3B,CAER,CArGA7I,EAAe0F,OAAO,CATtB,SAAiBmE,CAAW,EACpBlD,EAAWT,EAAU,oBAErBE,EADmByD,EACiB,iBAAkBjD,EAAwB,CAAEkD,MAAO,CAAE,GACzF1D,EAFmByD,EAEiB,WAAYtB,GAChDnC,EAHmByD,EAGiB,UAAWnB,GAC/CtC,EAAwByD,EAAa,sBAAuBjB,GAEpE,CAuGJ,EAAG5I,GAAmBA,CAAAA,EAAiB,CAAC,CAAA,GAMX,IAAM+J,EAAsC/J,EAmBnE,CAAE8C,SAAUkH,CAA4B,CAAE3D,QAAS4D,CAA2B,CAAE/G,MAAOgH,CAAyB,CAAEC,MAAAA,CAAK,CAAE,CAAIvI,GAMnI,OAAMwI,EAMF/G,YAAYvB,CAAK,CAAE,CACf,IAAI,CAACA,KAAK,CAAGA,CACjB,CAuBAuI,gBAAgB5G,CAAO,CAAE,CACrB,IAAM3B,EAAS,IAAI,CAACA,KAAK,EACrB,IAAI,CAAGwI,EAAgB7G,EAAQsB,MAAM,CAIzC,IAAK,IAAMA,KAHXjD,EAAM+B,YAAY,CAAG,CACjBC,QAAS,CACb,EACqBwG,GACbvF,EAAOwF,IAAI,EACXzI,CAAAA,EAAM+B,YAAY,CAACC,OAAO,CAAGmE,KAAKuC,GAAG,CAAC1I,EAAM+B,YAAY,CAACC,OAAO,CAAEiB,EAAOwF,IAAI,CAAC5F,MAAM,CAAG,EAAC,CAGpG,CACJ,EAOA,AAAC,SAAU1E,CAAmB,EA4B1B,SAASwK,EAAYvG,CAAC,EAClB,IAAoBT,EAAUS,EAAEwG,IAAI,CAAC,EAAE,CAAEC,EAAeR,EAAM1G,EAAQgB,KAAK,EAAI,CAAC,GAAImG,EAAW,EAAE,CAC7FC,EAAcF,EAAahG,MAAM,CAYrC,GAFA7C,AAXc,IAAI,CAWZqC,sBAAsB,CAAGV,EAAQ3B,KAAK,EACxC2B,EAAQ3B,KAAK,CAACC,mBAAmB,CACjCD,AAbU,IAAI,CAaRqC,sBAAsB,CAAE,CAG9B,IAFArC,AAdU,IAAI,CAcRuI,eAAe,CAAC5G,GAEfoH,GAAe/I,AAhBZ,IAAI,CAgBc+B,YAAY,CAACC,OAAO,CAAE+G,IAC9CD,EAAS9E,IAAI,CAAC,CAAC,EAEf,AAACrC,CAAAA,EAAQqH,MAAM,EACfrH,CAAAA,EAAQqH,MAAM,CAAG,CAAC,CAAA,EAElBrH,EAAQqH,MAAM,EACd,AAAkC,KAAA,IAA3BrH,EAAQqH,MAAM,CAACC,OAAO,EAC7BtH,CAAAA,EAAQqH,MAAM,CAACC,OAAO,CAAG,CAAA,CAAI,EAEjCb,EAA0B,CAAA,EAAMzG,EAEhC,CACIuH,MAAO,CACHC,gBAAiBjE,OAAOC,SAAS,AACrC,EACAiE,YAAa,CACTnG,OAAQ,CACJoG,eAAgBnE,OAAOC,SAAS,AACpC,CACJ,CACJ,GACAxD,EAAQgB,KAAK,CAAGkG,EAAaS,MAAM,CAACR,GACpCnH,EAAQf,KAAK,CAAGwH,EAA0BrI,EAAgDa,KAAK,CAC/FyH,EAAM1G,EAAQf,KAAK,EAAI,CAAC,EAAE,CAAC,EAAE,CACjC,CACJ,CAKA,SAAS2I,EAAcnH,CAAC,EACpB,IAAoBT,EAAUS,EAAET,OAAO,CAQvC,GAPIA,EAAQ3B,KAAK,GACTmI,EAA4BxG,EAAQ3B,KAAK,CAACC,mBAAmB,GAC7DD,CAAAA,AAHM,IAAI,CAGJqC,sBAAsB,CACxBV,EAAQ3B,KAAK,CAACC,mBAAmB,AAAD,EAExCD,AANU,IAAI,CAMR2B,OAAO,CAAC3B,KAAK,CAACE,YAAY,CAAGkI,EAA0BpI,AANnD,IAAI,CAMqD2B,OAAO,CAAC3B,KAAK,CAACE,YAAY,CAAEyB,EAAQ3B,KAAK,CAACE,YAAY,GAEzHF,AARU,IAAI,CAQRqC,sBAAsB,CAK5B,IAAK,IAAMb,KAHPG,EAAQsB,MAAM,EACdjD,AAXM,IAAI,CAWJuI,eAAe,CAAC5G,GAEP3B,AAbT,IAAI,CAaW2C,KAAK,EAC1BnB,EAAKgI,MAAM,CAAC,CAAC,EAAG,CAAA,EAG5B,CAvEArL,EAAoByF,OAAO,CAX3B,SAAiBC,CAAS,CAAE4F,CAAU,CAAEC,CAAwB,CAAE3B,CAAW,EACzE9D,EAAiCL,OAAO,CAACC,GACzCoE,EAAmCrE,OAAO,CAACmE,GAC3C,IAA+B4B,EAAYrB,EAAe9I,SAAS,CAAEoK,EAAaC,AAA/DJ,EAA0EjK,SAAS,AACjGoK,CAAAA,EAAWrB,eAAe,GAC3BqB,EAAWrB,eAAe,CAAGoB,EAAUpB,eAAe,CACtDL,EAHeuB,EAG0B,OAAQd,GACjDT,EAJeuB,EAI0B,SAAUF,GACnDnB,EAA0B,CAAA,EAAMsB,EAAyB1J,KAAK,CAAED,EAAgDC,KAAK,EAE7H,CAyEJ,EAAG7B,GAAwBA,CAAAA,EAAsB,CAAC,CAAA,GAMrB,IAAM2L,EAA2C3L,EAOxE4L,EAAKjK,IACXgK,EAAwClG,OAAO,CAACmG,EAAEC,IAAI,CAAED,EAAEE,KAAK,CAAEF,EAAEG,cAAc,CAAEH,EAAEI,MAAM,EAC9D,IAAMvK,EAA6BE,IAGtD,OADYH,EAAoB,OAAU,AAE3C,CAAA"}