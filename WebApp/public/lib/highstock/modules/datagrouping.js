!/**
 * Highstock JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/datagrouping
 * @requires highcharts
 *
 * Data grouping module
 *
 * (c) 2010-2025 Torstein Hønsi
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.SeriesRegistry,t._Highcharts.Templating):"function"==typeof define&&define.amd?define("highcharts/modules/datagrouping",["highcharts/highcharts"],function(t){return e(t,t.SeriesRegistry,t.Templating)}):"object"==typeof exports?exports["highcharts/modules/datagrouping"]=e(t._Highcharts,t._Highcharts.SeriesRegistry,t._Highcharts.Templating):t.Highcharts=e(t.Highcharts,t.Highcharts.SeriesRegistry,t.Highcharts.Templating)}("undefined"==typeof window?this:window,(t,e,i)=>(()=>{"use strict";let o;var a,n,r={512:t=>{t.exports=e},944:e=>{e.exports=t},984:t=>{t.exports=i}},s={};function l(t){var e=s[t];if(void 0!==e)return e.exports;var i=s[t]={exports:{}};return r[t](i,i.exports,l),i.exports}l.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return l.d(e,{a:e}),e},l.d=(t,e)=>{for(var i in e)l.o(e,i)&&!l.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},l.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var u={};l.d(u,{default:()=>ty});var h=l(944),p=l.n(h);let d={},{arrayMax:c,arrayMin:g,correctFloat:m,extend:f,isNumber:y}=p();function x(t){let e=t.length,i=G(t);return y(i)&&e&&(i=m(i/e)),i}function G(t){let e=t.length,i;if(!e&&t.hasNulls)i=null;else if(e)for(i=0;e--;)i+=t[e];return i}let b={average:x,averages:function(){let t=[];return[].forEach.call(arguments,function(e){t.push(x(e))}),void 0===t[0]?void 0:t},close:function(t){return t.length?t[t.length-1]:t.hasNulls?null:void 0},high:function(t){return t.length?c(t):t.hasNulls?null:void 0},hlc:function(t,e,i){if(t=d.high(t),e=d.low(e),i=d.close(i),y(t)||y(e)||y(i))return[t,e,i]},low:function(t){return t.length?g(t):t.hasNulls?null:void 0},ohlc:function(t,e,i,o){if(t=d.open(t),e=d.high(e),i=d.low(i),o=d.close(o),y(t)||y(e)||y(i)||y(o))return[t,e,i,o]},open:function(t){return t.length?t[0]:t.hasNulls?null:void 0},range:function(t,e){return(t=d.low(t),e=d.high(e),y(t)||y(e))?[t,e]:null===t&&null===e?null:void 0},sum:G};f(d,b);let v={common:{groupPixelWidth:2,dateTimeLabelFormats:{millisecond:["%[AebHMSL]","%[AebHMSL]","-%[HMSL]"],second:["%[AebHMS]","%[AebHMS]","-%[HMS]"],minute:["%[AebHM]","%[AebHM]","-%[HM]"],hour:["%[AebHM]","%[AebHM]","-%[HM]"],day:["%[AebY]","%[Aeb]","-%[AebY]"],week:["%v %[AebY]","%[Aeb]","-%[AebY]"],month:["%[BY]","%[B]","-%[BY]"],year:["%Y","%Y","-%Y"]}},seriesSpecific:{line:{},spline:{},area:{},areaspline:{},arearange:{},column:{groupPixelWidth:10},columnrange:{groupPixelWidth:10},candlestick:{groupPixelWidth:10},ohlc:{groupPixelWidth:5},hlc:{groupPixelWidth:5},heikinashi:{groupPixelWidth:10}},units:[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1]],["week",[1]],["month",[1,3,6]],["year",null]]},{addEvent:M,extend:T,merge:A,pick:D}=p();function w(t){let e=this,i=e.series;i.forEach(function(t){t.groupPixelWidth=void 0}),i.forEach(function(i){i.groupPixelWidth=e.getGroupPixelWidth&&e.getGroupPixelWidth(),i.groupPixelWidth&&(i.hasProcessed=!0),i.applyGrouping(!!t.hasExtremesChanged)})}function C(){let t=this.series,e=t.length,i=0,o=!1,a,n;for(;e--;)(n=t[e].options.dataGrouping)&&(i=Math.max(i,D(n.groupPixelWidth,v.common.groupPixelWidth)),a=(t[e].dataTable.modified||t[e].dataTable).rowCount,(t[e].groupPixelWidth||a>this.chart.plotSizeX/i||a&&n.forced)&&(o=!0));return o?i:0}function S(){this.series.forEach(function(t){t.hasProcessed=!1})}function P(t,e){let i;if(e=D(e,!0),t||(t={forced:!1,units:null}),this instanceof o)for(i=this.series.length;i--;)this.series[i].update({dataGrouping:t},!1);else this.chart.options.series.forEach(function(e){e.dataGrouping="boolean"==typeof t?t:A(t,e.dataGrouping)});this.ordinal&&(this.ordinal.slope=void 0),e&&this.chart.redraw()}let k={compose:function(t){o=t;let e=t.prototype;e.applyGrouping||(M(t,"afterSetScale",S),M(t,"postProcessData",w),T(e,{applyGrouping:w,getGroupPixelWidth:C,setDataGrouping:P}))}};!function(t){t.setLength=function(t,e,i){return Array.isArray(t)?(t.length=e,t):t[i?"subarray":"slice"](0,e)},t.splice=function(t,e,i,o,a=[]){if(Array.isArray(t))return Array.isArray(a)||(a=Array.from(a)),{removed:t.splice(e,i,...a),array:t};let n=Object.getPrototypeOf(t).constructor,r=t[o?"subarray":"slice"](e,e+i),s=new n(t.length-i+a.length);return s.set(t.subarray(0,e),0),s.set(a,e),s.set(t.subarray(e+i),e+a.length),{removed:r,array:s}}}(a||(a={}));let{setLength:H,splice:R}=a,{fireEvent:O,objectEach:F,uniqueKey:W}=p(),E=class{constructor(t={}){this.autoId=!t.id,this.columns={},this.id=t.id||W(),this.modified=this,this.rowCount=0,this.versionTag=W();let e=0;F(t.columns||{},(t,i)=>{this.columns[i]=t.slice(),e=Math.max(e,t.length)}),this.applyRowCount(e)}applyRowCount(t){this.rowCount=t,F(this.columns,(e,i)=>{e.length!==t&&(this.columns[i]=H(e,t))})}deleteRows(t,e=1){if(e>0&&t<this.rowCount){let i=0;F(this.columns,(o,a)=>{this.columns[a]=R(o,t,e).array,i=o.length}),this.rowCount=i}O(this,"afterDeleteRows",{rowIndex:t,rowCount:e}),this.versionTag=W()}getColumn(t,e){return this.columns[t]}getColumns(t,e){return(t||Object.keys(this.columns)).reduce((t,e)=>(t[e]=this.columns[e],t),{})}getRow(t,e){return(e||Object.keys(this.columns)).map(e=>this.columns[e]?.[t])}setColumn(t,e=[],i=0,o){this.setColumns({[t]:e},i,o)}setColumns(t,e,i){let o=this.rowCount;F(t,(t,e)=>{this.columns[e]=t.slice(),o=t.length}),this.applyRowCount(o),i?.silent||(O(this,"afterSetColumns"),this.versionTag=W())}setRow(t,e=this.rowCount,i,o){let{columns:a}=this,n=i?this.rowCount+1:e+1;F(t,(t,r)=>{let s=a[r]||o?.addColumns!==!1&&Array(n);s&&(i?s=R(s,e,0,!0,[t]).array:s[e]=t,a[r]=s)}),n>this.rowCount&&this.applyRowCount(n),o?.silent||(O(this,"afterSetRows"),this.versionTag=W())}},{addEvent:N,getMagnitude:I,normalizeTickInterval:L,timeUnits:j}=p();!function(t){function e(){return this.chart.time.getTimeTicks.apply(this.chart.time,arguments)}function i(){if("datetime"!==this.type){this.dateTime=void 0;return}this.dateTime||(this.dateTime=new o(this))}t.compose=function(t){return t.keepProps.includes("dateTime")||(t.keepProps.push("dateTime"),t.prototype.getTimeTicks=e,N(t,"afterSetType",i)),t};class o{constructor(t){this.axis=t}normalizeTimeTickInterval(t,e){let i=e||[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1,2]],["week",[1,2]],["month",[1,2,3,4,6]],["year",null]],o=i[i.length-1],a=j[o[0]],n=o[1],r;for(r=0;r<i.length&&(a=j[(o=i[r])[0]],n=o[1],!i[r+1]||!(t<=(a*n[n.length-1]+j[i[r+1][0]])/2));r++);a===j.year&&t<5*a&&(n=[1,2,5]);let s=L(t/a,n,"year"===o[0]?Math.max(I(t/a),1):1);return{unitRange:a,count:s,unitName:o[0]}}getXDateFormat(t,e){let{axis:i}=this,o=i.chart.time;return i.closestPointRange?o.getDateFormat(i.closestPointRange,t,i.options.startOfWeek,e)||o.resolveDTLFormat(e.year).main:o.resolveDTLFormat(e.day).main}}t.Additions=o}(n||(n={}));let Y=n;var _=l(512);let{series:{prototype:z}}=l.n(_)(),{addEvent:X,defined:q,error:B,extend:J,isNumber:K,merge:Q,pick:U,splat:V}=p(),Z=z.generatePoints;function $(t){var e,i,o;let a,n,r=this.chart,s=this.options.dataGrouping,l=!1!==this.allowDG&&s&&U(s.enabled,r.options.isStock),u=this.reserveSpace(),h=this.currentDataGrouping,p,d,c=!1;l&&!this.requireSorting&&(this.requireSorting=c=!0);let g=!1==(e=this,i=t,!(e.isCartesian&&!e.isDirty&&!e.xAxis.isDirty&&!e.yAxis.isDirty&&!i))||!l;if(c&&(this.requireSorting=!1),g)return;this.destroyGroupedData();let m=s.groupAll?this.dataTable:this.dataTable.modified||this.dataTable,f=this.getColumn("x",!s.groupAll),y=r.plotSizeX,x=this.xAxis,G=x.getExtremes(),b=x.options.ordinal,M=this.groupPixelWidth;if(M&&f&&m.rowCount&&y&&K(G.min)){n=!0,this.isDirty=!0,this.points=null;let t=G.min,e=G.max,i=b&&x.ordinal&&x.ordinal.getGroupIntervalFactor(t,e,this)||1,l=M*(e-t)/y*i,h=x.getTimeTicks(Y.Additions.prototype.normalizeTimeTickInterval(l,s.units||v.units),Math.min(t,f[0]),Math.max(e,f[f.length-1]),x.options.startOfWeek,f,this.closestPointRange),c=z.groupData.apply(this,[m,h,s.approximation]),g=c.modified,T=g.getColumn("x",!0),A=0;for(s?.smoothed&&g.rowCount&&(s.firstAnchor="firstPoint",s.anchor="middle",s.lastAnchor="lastPoint",B(32,!1,r,{"dataGrouping.smoothed":"use dataGrouping.anchor"})),a=1;a<h.length;a++)h.info.segmentStarts&&-1!==h.info.segmentStarts.indexOf(a)||(A=Math.max(h[a]-h[a-1],A));(p=h.info).gapSize=A,this.closestPointRange=h.info.totalRange,this.groupMap=c.groupMap,this.currentDataGrouping=p,function(t,e,i){let o=t.options.dataGrouping,a=t.currentDataGrouping&&t.currentDataGrouping.gapSize,n=t.getColumn("x");if(!(o&&n.length&&a&&t.groupMap))return;let r=e.length-1,s=o.anchor,l=o.firstAnchor,u=o.lastAnchor,h=e.length-1,p=0;if(l&&n[0]>=e[0]){let i;p++;let o=t.groupMap[0].start,r=t.groupMap[0].length;K(o)&&K(r)&&(i=o+(r-1)),e[0]=({start:e[0],middle:e[0]+.5*a,end:e[0]+a,firstPoint:n[0],lastPoint:i&&n[i]})[l]}if(r>0&&u&&a&&e[r]>=i-a){h--;let i=t.groupMap[t.groupMap.length-1].start;e[r]=({start:e[r],middle:e[r]+.5*a,end:e[r]+a,firstPoint:i&&n[i],lastPoint:n[n.length-1]})[u]}if(s&&"start"!==s){let t=a*({middle:.5,end:1})[s];for(;h>=p;)e[h]+=t,h--}}(this,T||[],e),u&&T&&(q((o=T)[0])&&K(x.min)&&K(x.dataMin)&&o[0]<x.min&&((!q(x.options.min)&&x.min<=x.dataMin||x.min===x.dataMin)&&(x.min=Math.min(o[0],x.min)),x.dataMin=Math.min(o[0],x.dataMin)),q(o[o.length-1])&&K(x.max)&&K(x.dataMax)&&o[o.length-1]>x.max&&((!q(x.options.max)&&K(x.dataMax)&&x.max>=x.dataMax||x.max===x.dataMax)&&(x.max=Math.max(o[o.length-1],x.max)),x.dataMax=Math.max(o[o.length-1],x.dataMax))),s.groupAll&&(this.allGroupedTable=g,T=(g=(d=this.cropData(g,x.min||0,x.max||0)).modified).getColumn("x"),this.cropStart=d.start),this.dataTable.modified=g}else this.groupMap=void 0,this.currentDataGrouping=void 0;this.hasGroupedData=n,this.preventGraphAnimation=(h&&h.totalRange)!==(p&&p.totalRange)}function tt(){this.groupedData&&(this.groupedData.forEach(function(t,e){t&&(this.groupedData[e]=t.destroy?t.destroy():null)},this),this.groupedData.length=0,delete this.allGroupedTable)}function te(){Z.apply(this),this.destroyGroupedData(),this.groupedData=this.hasGroupedData?this.points:null}function ti(){return this.is("arearange")?"range":this.is("ohlc")?"ohlc":this.is("hlc")?"hlc":this.is("column")||this.options.cumulative?"sum":"average"}function to(t,e,i){let o=t.getColumn("x",!0)||[],a=t.getColumn("y",!0),n=this,r=n.data,s=n.options&&n.options.data,l=[],u=new E,h=[],p=t.rowCount,c=!!a,g=[],m=n.pointArrayMap,f=m&&m.length,y=["x"].concat(m||["y"]),x=(m||["y"]).map(()=>[]),G=this.options.dataGrouping&&this.options.dataGrouping.groupAll,b,v,M,T=0,A=0,D="function"==typeof i?i:i&&d[i]?d[i]:d[n.getDGApproximation&&n.getDGApproximation()||"average"];if(f){let t=m.length;for(;t--;)g.push([])}else g.push([]);let w=f||1;for(let t=0;t<=p;t++)if(!(o[t]<e[0])){for(;void 0!==e[T+1]&&o[t]>=e[T+1]||t===p;){if(b=e[T],n.dataGroupInfo={start:G?A:n.cropStart+A,length:g[0].length,groupStart:b},M=D.apply(n,g),n.pointClass&&!q(n.dataGroupInfo.options)&&(n.dataGroupInfo.options=Q(n.pointClass.prototype.optionsToObject.call({series:n},n.options.data[n.cropStart+A])),y.forEach(function(t){delete n.dataGroupInfo.options[t]})),void 0!==M){l.push(b);let t=V(M);for(let e=0;e<t.length;e++)x[e].push(t[e]);h.push(n.dataGroupInfo)}A=t;for(let t=0;t<w;t++)g[t].length=0,g[t].hasNulls=!1;if(T+=1,t===p)break}if(t===p)break;if(m){let e,i=G?t:n.cropStart+t,o=r&&r[i]||n.pointClass.prototype.applyOptions.apply({series:n},[s[i]]);for(let t=0;t<f;t++)K(e=o[m[t]])?g[t].push(e):null===e&&(g[t].hasNulls=!0)}else K(v=c?a[t]:null)?g[0].push(v):null===v&&(g[0].hasNulls=!0)}let C={x:l};return(m||["y"]).forEach((t,e)=>{C[t]=x[e]}),u.setColumns(C),{groupMap:h,modified:u}}function ta(t){let e=t.options,i=this.type,o=this.chart.options.plotOptions,a=this.useCommonDataGrouping&&v.common,n=v.seriesSpecific,r=p().defaultOptions.plotOptions[i].dataGrouping;if(o&&(n[i]||a)){let t=this.chart.rangeSelector;r||(r=Q(v.common,n[i])),e.dataGrouping=Q(a,r,o.series&&o.series.dataGrouping,o[i].dataGrouping,this.userOptions.dataGrouping,!e.isInternal&&t&&K(t.selected)&&t.buttonOptions[t.selected].dataGrouping)}}let tn={compose:function(t){let e=t.prototype;e.applyGrouping||(X(t.prototype.pointClass,"update",function(){if(this.dataGroup)return B(24,!1,this.series.chart),!1}),X(t,"afterSetOptions",ta),X(t,"destroy",tt),J(e,{applyGrouping:$,destroyGroupedData:tt,generatePoints:te,getDGApproximation:ti,groupData:to}))},groupData:to};var tr=l(984);let{format:ts}=l.n(tr)(),{composed:tl}=p(),{addEvent:tu,extend:th,isNumber:tp,pick:td,pushUnique:tc}=p();function tg(t){let e=this.chart,i=e.time,o=t.point,a=o.series,n=a.options,r=a.tooltipOptions,s=n.dataGrouping,l=a.xAxis,u=r.xDateFormat||"",h,p,d,c,g,m=r[t.isFooter?"footerFormat":"headerFormat"];if(l&&"datetime"===l.options.type&&s&&tp(o.key)){p=a.currentDataGrouping,d=s.dateTimeLabelFormats||v.common.dateTimeLabelFormats,p?(c=d[p.unitName],1===p.count?u=c[0]:(u=c[1],h=c[2])):!u&&d&&l.dateTime&&(u=l.dateTime.getXDateFormat(o.x,r.dateTimeLabelFormats));let n=td(a.groupMap?.[o.index].groupStart,o.key),f=n+(p?.totalRange||0)-1;g=i.dateFormat(u,n),h&&(g+=i.dateFormat(h,f)),a.chart.styledMode&&(m=this.styledModeFormat(m)),t.text=ts(m,{point:th(o,{key:g}),series:a},e),t.preventDefault()}}let tm={compose:function(t,e,i){k.compose(t),tn.compose(e),i&&tc(tl,"DataGrouping")&&tu(i,"headerFormatter",tg)},groupData:tn.groupData},tf=p();tf.dataGrouping=tf.dataGrouping||{},tf.dataGrouping.approximationDefaults=tf.dataGrouping.approximationDefaults||b,tf.dataGrouping.approximations=tf.dataGrouping.approximations||d,tm.compose(tf.Axis,tf.Series,tf.Tooltip);let ty=p();return u.default})());