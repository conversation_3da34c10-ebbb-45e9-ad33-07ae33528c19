!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/data-tools
 * @requires highcharts
 *
 * Highcharts
 *
 * (c) 2010-2025 Highsoft AS
 *
 * License: www.highcharts.com/license
 */function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(e._Highcharts):"function"==typeof define&&define.amd?define("highcharts/modules/data-tools",["highcharts/highcharts"],function(e){return t(e)}):"object"==typeof exports?exports["highcharts/modules/data-tools"]=t(e._Highcharts):e.Highcharts=t(e.Highcharts)}("undefined"==typeof window?this:window,e=>(()=>{"use strict";var t,r={944:t=>{t.exports=e}},s={};function i(e){var t=s[e];if(void 0!==t)return t.exports;var n=s[e]={exports:{}};return r[e](n,n.exports,i),n.exports}i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},i.d=(e,t)=>{for(var r in t)i.o(t,r)&&!i.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var n={};i.d(n,{default:()=>tp});var o=i(944),l=i.n(o);let{addEvent:a,fireEvent:u,merge:h}=l();class f{benchmark(e,t){let r=[],s=this,i=()=>{s.modifyTable(e),s.emit({type:"afterBenchmarkIteration"})},{iterations:n}=h({iterations:1},t);s.on("afterBenchmarkIteration",()=>{if(r.length===n)return void s.emit({type:"afterBenchmark",results:r});i()});let o={startTime:0,endTime:0};return s.on("modify",()=>{o.startTime=window.performance.now()}),s.on("afterModify",()=>{o.endTime=window.performance.now(),r.push(o.endTime-o.startTime)}),i(),r}emit(e){u(this,e.type,e)}modify(e,t){let r=this;return new Promise((s,i)=>{e.modified===e&&(e.modified=e.clone(!1,t));try{s(r.modifyTable(e,t))}catch(s){r.emit({type:"error",detail:t,table:e}),i(s)}})}modifyCell(e,t,r,s,i){return this.modifyTable(e)}modifyColumns(e,t,r,s){return this.modifyTable(e)}modifyRows(e,t,r,s){return this.modifyTable(e)}on(e,t){return a(this,e,t)}}!function(e){e.types={},e.registerType=function(t,r){return!!t&&!e.types[t]&&!!(e.types[t]=r)}}(f||(f={}));let m=f;!function(e){e.setLength=function(e,t,r){return Array.isArray(e)?(e.length=t,e):e[r?"subarray":"slice"](0,t)},e.splice=function(e,t,r,s,i=[]){if(Array.isArray(e))return Array.isArray(i)||(i=Array.from(i)),{removed:e.splice(t,r,...i),array:e};let n=Object.getPrototypeOf(e).constructor,o=e[s?"subarray":"slice"](t,t+r),l=new n(e.length-r+i.length);return l.set(e.subarray(0,t),0),l.set(i,t),l.set(e.subarray(t+r),t+i.length),{removed:o,array:l}}}(t||(t={}));let c=t,{setLength:d,splice:g}=c,{fireEvent:p,objectEach:y,uniqueKey:b}=l(),w=class{constructor(e={}){this.autoId=!e.id,this.columns={},this.id=e.id||b(),this.modified=this,this.rowCount=0,this.versionTag=b();let t=0;y(e.columns||{},(e,r)=>{this.columns[r]=e.slice(),t=Math.max(t,e.length)}),this.applyRowCount(t)}applyRowCount(e){this.rowCount=e,y(this.columns,(t,r)=>{t.length!==e&&(this.columns[r]=d(t,e))})}deleteRows(e,t=1){if(t>0&&e<this.rowCount){let r=0;y(this.columns,(s,i)=>{this.columns[i]=g(s,e,t).array,r=s.length}),this.rowCount=r}p(this,"afterDeleteRows",{rowIndex:e,rowCount:t}),this.versionTag=b()}getColumn(e,t){return this.columns[e]}getColumns(e,t){return(e||Object.keys(this.columns)).reduce((e,t)=>(e[t]=this.columns[t],e),{})}getRow(e,t){return(t||Object.keys(this.columns)).map(t=>this.columns[t]?.[e])}setColumn(e,t=[],r=0,s){this.setColumns({[e]:t},r,s)}setColumns(e,t,r){let s=this.rowCount;y(e,(e,t)=>{this.columns[t]=e.slice(),s=e.length}),this.applyRowCount(s),r?.silent||(p(this,"afterSetColumns"),this.versionTag=b())}setRow(e,t=this.rowCount,r,s){let{columns:i}=this,n=r?this.rowCount+1:t+1;y(e,(e,o)=>{let l=i[o]||s?.addColumns!==!1&&Array(n);l&&(r?l=g(l,t,0,!0,[e]).array:l[t]=e,i[o]=l)}),n>this.rowCount&&this.applyRowCount(n),s?.silent||(p(this,"afterSetRows"),this.versionTag=b())}},{addEvent:C,defined:R,extend:N,fireEvent:T,isNumber:v,uniqueKey:O}=l();class A extends w{static isNull(e){if(e===A.NULL)return!0;if(e instanceof Array){if(!e.length)return!1;for(let t=0,r=e.length;t<r;++t)if(null!==e[t])return!1}else{let t=Object.keys(e);if(!t.length)return!1;for(let r=0,s=t.length;r<s;++r)if(null!==e[t[r]])return!1}return!0}constructor(e={}){super(e),this.modified=this}clone(e,t){let r={};this.emit({type:"cloneTable",detail:t}),e||(r.columns=this.columns),this.autoId||(r.id=this.id);let s=new A(r);return e||(s.versionTag=this.versionTag,s.originalRowIndexes=this.originalRowIndexes,s.localRowIndexes=this.localRowIndexes),this.emit({type:"afterCloneTable",detail:t,tableClone:s}),s}deleteColumns(e,t){let r=this.columns,s={},i={},n=this.modifier,o=this.rowCount;if((e=e||Object.keys(r)).length){this.emit({type:"deleteColumns",columnNames:e,detail:t});for(let t=0,n=e.length,l,a;t<n;++t)(l=r[a=e[t]])&&(s[a]=l,i[a]=Array(o)),delete r[a];return Object.keys(r).length||(this.rowCount=0,this.deleteRowIndexReferences()),n&&n.modifyColumns(this,i,0,t),this.emit({type:"afterDeleteColumns",columns:s,columnNames:e,detail:t}),s}}deleteRowIndexReferences(){delete this.originalRowIndexes,delete this.localRowIndexes}deleteRows(e,t=1,r){let s=[],i=[],n=this.modifier;if(this.emit({type:"deleteRows",detail:r,rowCount:t,rowIndex:e||0}),void 0===e&&(e=0,t=this.rowCount),t>0&&e<this.rowCount){let r=this.columns,n=Object.keys(r);for(let o=0,l=n.length,a,u,h;o<l;++o){a=r[h=n[o]];let f=c.splice(a,e,t);u=f.removed,r[h]=a=f.array,o||(this.rowCount=a.length);for(let e=0,t=u.length;e<t;++e)s[e]=s[e]||[],s[e][o]=u[e];i.push(Array(l))}}return n&&n.modifyRows(this,i,e||0,r),this.emit({type:"afterDeleteRows",detail:r,rowCount:t,rowIndex:e||0,rows:s}),s}emit(e){["afterDeleteColumns","afterDeleteRows","afterSetCell","afterSetColumns","afterSetRows"].includes(e.type)&&(this.versionTag=O()),T(this,e.type,e)}getCell(e,t){let r=this.columns[e];if(r)return r[t]}getCellAsBoolean(e,t){let r=this.columns[e];return!!(r&&r[t])}getCellAsNumber(e,t,r){let s=this.columns[e],i=s&&s[t];switch(typeof i){case"boolean":return+!!i;case"number":return isNaN(i)&&!r?null:i}return isNaN(i=parseFloat(`${i??""}`))&&!r?null:i}getCellAsString(e,t){let r=this.columns[e];return`${r&&r[t]}`}getColumn(e,t){return this.getColumns([e],t)[e]}getColumnAsNumbers(e,t){let r=this.columns[e],s=[];if(r){let i=r.length;if(t)for(let t=0;t<i;++t)s.push(this.getCellAsNumber(e,t,!0));else{for(let e=0,t;e<i;++e){if("number"==typeof(t=r[e]))return r.slice();if(null!=t)break}for(let t=0;t<i;++t)s.push(this.getCellAsNumber(e,t))}}return s}getColumnNames(){return Object.keys(this.columns)}getColumns(e,t,r){let s=this.columns,i={};e=e||Object.keys(s);for(let n=0,o=e.length,l,a;n<o;++n)(l=s[a=e[n]])&&(t?i[a]=l:r&&!Array.isArray(l)?i[a]=Array.from(l):i[a]=l.slice());return i}getLocalRowIndex(e){let{localRowIndexes:t}=this;return t?t[e]:e}getModifier(){return this.modifier}getOriginalRowIndex(e){let{originalRowIndexes:t}=this;return t?t[e]:e}getRow(e,t){return this.getRows(e,1,t)[0]}getRowCount(){return this.rowCount}getRowIndexBy(e,t,r){let s=this.columns[e];if(s){let e=-1;if(Array.isArray(s)?e=s.indexOf(t,r):v(t)&&(e=s.indexOf(t,r)),-1!==e)return e}}getRowObject(e,t){return this.getRowObjects(e,1,t)[0]}getRowObjects(e=0,t=this.rowCount-e,r){let s=this.columns,i=Array(t);r=r||Object.keys(s);for(let n=e,o=0,l=Math.min(this.rowCount,e+t),a,u;n<l;++n,++o)for(let e of(u=i[o]={},r))a=s[e],u[e]=a?a[n]:void 0;return i}getRows(e=0,t=this.rowCount-e,r){let s=this.columns,i=Array(t);r=r||Object.keys(s);for(let n=e,o=0,l=Math.min(this.rowCount,e+t),a,u;n<l;++n,++o)for(let e of(u=i[o]=[],r))a=s[e],u.push(a?a[n]:void 0);return i}getVersionTag(){return this.versionTag}hasColumns(e){let t=this.columns;for(let r=0,s=e.length;r<s;++r)if(!t[e[r]])return!1;return!0}hasRowWith(e,t){let r=this.columns[e];return Array.isArray(r)?-1!==r.indexOf(t):!!(R(t)&&Number.isFinite(t))&&-1!==r.indexOf(+t)}on(e,t){return C(this,e,t)}renameColumn(e,t){let r=this.columns;return!!r[e]&&(e!==t&&(r[t]=r[e],delete r[e]),!0)}setCell(e,t,r,s){let i=this.columns,n=this.modifier,o=i[e];o&&o[t]===r||(this.emit({type:"setCell",cellValue:r,columnName:e,detail:s,rowIndex:t}),o||(o=i[e]=Array(this.rowCount)),t>=this.rowCount&&(this.rowCount=t+1),o[t]=r,n&&n.modifyCell(this,e,t,r),this.emit({type:"afterSetCell",cellValue:r,columnName:e,detail:s,rowIndex:t}))}setColumns(e,t,r,s){let i=this.columns,n=this.modifier,o=Object.keys(e),l=this.rowCount;if(this.emit({type:"setColumns",columns:e,columnNames:o,detail:r,rowIndex:t}),R(t)||s){for(let r=0,n=o.length,a,u,h,f;r<n;++r){a=e[h=o[r]],f=Object.getPrototypeOf((u=i[h])&&s?u:a).constructor,u?f===Array?Array.isArray(u)||(u=Array.from(u)):u.length<l&&(u=new f(l)).set(i[h]):u=new f(l),i[h]=u;for(let e=t||0,r=a.length;e<r;++e)u[e]=a[e];l=Math.max(l,a.length)}this.applyRowCount(l)}else super.setColumns(e,t,N(r,{silent:!0}));n&&n.modifyColumns(this,e,t||0),this.emit({type:"afterSetColumns",columns:e,columnNames:o,detail:r,rowIndex:t})}setModifier(e,t){let r,s=this;return s.emit({type:"setModifier",detail:t,modifier:e,modified:s.modified}),s.modified=s,s.modifier=e,(e?e.modify(s):Promise.resolve(s)).then(r=>(r.emit({type:"afterSetModifier",detail:t,modifier:e,modified:r.modified}),r)).catch(t=>{throw s.emit({type:"setModifierError",error:t,modifier:e,modified:s.modified}),t})}setOriginalRowIndexes(e,t=!1){if(this.originalRowIndexes=e,t)return;let r=this.localRowIndexes=[];for(let t=0,s=e.length,i;t<s;++t)R(i=e[t])&&(r[i]=t)}setRow(e,t,r,s){this.setRows([e],t,r,s)}setRows(e,t=this.rowCount,r,s){let i=this.columns,n=Object.keys(i),o=this.modifier,l=e.length;this.emit({type:"setRows",detail:s,rowCount:l,rowIndex:t,rows:e});for(let s=0,o=t,a;s<l;++s,++o)if((a=e[s])===A.NULL)for(let e=0,t=n.length;e<t;++e){let t=i[n[e]];r?i[n[e]]=c.splice(t,o,0,!0,[null]).array:t[o]=null}else if(a instanceof Array)for(let e=0,t=n.length;e<t;++e)i[n[e]][o]=a[e];else super.setRow(a,o,void 0,{silent:!0});let a=r?l+e.length:t+l;if(a>this.rowCount){this.rowCount=a;for(let e=0,t=n.length;e<t;++e){let t=n[e];i[t]=c.setLength(i[t],a)}}o&&o.modifyRows(this,e,t),this.emit({type:"afterSetRows",detail:s,rowCount:l,rowIndex:t,rows:e})}}A.NULL={},A.version="1.0.0";let{addEvent:x,fireEvent:F,merge:M,pick:E}=l();class I{constructor(e={},t=[]){this.dataTables={},this.loaded=!1,this.metadata=e.metadata||{columns:{}};let r=0;if(t?.length>0)for(let e=0,s=t.length;e<s;++e){let s=t[e],i=s?.key;this.dataTables[i??r]=new A(s),!i&&r++}else this.dataTables[0]=new A(e.dataTable)}get polling(){return!!this._polling}get table(){return this.getTable()}describeColumn(e,t){let r=this.metadata.columns;r[e]=M(r[e]||{},t)}describeColumns(e){let t,r=Object.keys(e);for(;"string"==typeof(t=r.pop());)this.describeColumn(t,e[t])}emit(e){F(this,e.type,e)}getColumnOrder(e){let t=this.metadata.columns,r=Object.keys(t||{});if(r.length)return r.sort((e,r)=>E(t[e].index,0)-E(t[r].index,0))}getTable(e){return e?this.dataTables[e]:Object.values(this.dataTables)[0]}getSortedColumns(e){return this.table.getColumns(this.getColumnOrder(e))}load(){return F(this,"afterLoad",{table:this.table}),Promise.resolve(this)}on(e,t){return x(this,e,t)}save(){return F(this,"saveError",{table:this.table}),Promise.reject(Error("Not implemented"))}setColumnOrder(e){for(let t=0,r=e.length;t<r;++t)this.describeColumn(e[t],{index:t})}async setModifierOptions(e,t){for(let[r,s]of Object.entries(this.dataTables)){let i=t?.find(e=>e.key===r),n=M(i?.dataModifier,e),o=n&&m.types[n.type];await s.setModifier(o?new o(n):void 0)}return this}startPolling(e=1e3){let t=this,r=t.dataTables;this.pollingController=new AbortController,window.clearTimeout(t._polling),t._polling=window.setTimeout(()=>t.load().catch(e=>t.emit({type:"loadError",error:e,tables:r})).then(()=>{t._polling&&t.startPolling(e)}),e)}stopPolling(){this.polling&&(this?.pollingController?.abort(),window.clearTimeout(this._polling),delete this._polling)}whatIs(e){return this.metadata.columns[e]}initConverters(e,t,r){let s=0;for(let[i,n]of Object.entries(this.dataTables)){let o=t(i,n);r(o,e),n.deleteColumns(),n.setColumns(o.getTable().getColumns()),0===s&&(this.converter=o),s++}}}!function(e){e.types={},e.registerType=function(t,r){return!!t&&!e.types[t]&&!!(e.types[t]=r)}}(I||(I={}));let P=I,{addEvent:D,fireEvent:j,isNumber:L,merge:S}=l();class k{constructor(e){this.dateFormats={"YYYY/mm/dd":{regex:/^(\d{4})([\-\.\/])(\d{1,2})\2(\d{1,2})$/,parser:function(e){return e?Date.UTC(+e[1],e[3]-1,+e[4]):NaN}},"dd/mm/YYYY":{regex:/^(\d{1,2})([\-\.\/])(\d{1,2})\2(\d{4})$/,parser:function(e){return e?Date.UTC(+e[4],e[3]-1,+e[1]):NaN},alternative:"mm/dd/YYYY"},"mm/dd/YYYY":{regex:/^(\d{1,2})([\-\.\/])(\d{1,2})\2(\d{4})$/,parser:function(e){return e?Date.UTC(+e[4],e[1]-1,+e[3]):NaN}},"dd/mm/YY":{regex:/^(\d{1,2})([\-\.\/])(\d{1,2})\2(\d{2})$/,parser:function(e){let t=new Date;if(!e)return NaN;let r=+e[4];return r>t.getFullYear()-2e3?r+=1900:r+=2e3,Date.UTC(r,e[3]-1,+e[1])},alternative:"mm/dd/YY"},"mm/dd/YY":{regex:/^(\d{1,2})([\-\.\/])(\d{1,2})\2(\d{2})$/,parser:function(e){return e?Date.UTC(+e[4]+2e3,e[1]-1,+e[3]):NaN}}};let t=S(k.defaultOptions,e),r=t.decimalPoint;("."===r||","===r)&&(r="."===r?"\\.":",",this.decimalRegExp=RegExp("^(-?[0-9]+)"+r+"([0-9]+)$")),this.options=t}asBoolean(e){return"boolean"==typeof e?e:"string"==typeof e?""!==e&&"0"!==e&&"false"!==e:!!this.asNumber(e)}asDate(e){let t;if("string"==typeof e)t=this.parseDate(e);else if("number"==typeof e)t=e;else{if(e instanceof Date)return e;t=this.parseDate(this.asString(e))}return new Date(t)}asGuessedType(e){return({number:this.asNumber,Date:this.asDate,string:this.asString})[this.guessType(e)].call(this,e)}asNumber(e){if("number"==typeof e)return e;if("boolean"==typeof e)return+!!e;if("string"==typeof e){let t=this.decimalRegExp;if(e.indexOf(" ")>-1&&(e=e.replace(/\s+/g,"")),t){if(!t.test(e))return NaN;e=e.replace(t,"$1.$2")}return parseFloat(e)}return e instanceof Date?e.getDate():e?e.getRowCount():NaN}asString(e){return""+e}deduceDateFormat(e,t,r){let s=[],i=[],n="YYYY/mm/dd",o,l=[],a=0,u=!1,h,f;for((!t||t>e.length)&&(t=e.length);a<t;a++)if(void 0!==e[a]&&e[a]&&e[a].length)for(f=0,o=e[a].trim().replace(/[\-\.\/]/g," ").split(" "),l=["","",""];f<o.length;f++)f<l.length&&(h=parseInt(o[f],10))&&(i[f]=!i[f]||i[f]<h?h:i[f],void 0!==s[f]?s[f]!==h&&(s[f]=!1):s[f]=h,h>31?h<100?l[f]="YY":l[f]="YYYY":h>12&&h<=31?(l[f]="dd",u=!0):l[f].length||(l[f]="mm"));if(u){for(f=0;f<s.length;f++)!1!==s[f]?i[f]>12&&"YY"!==l[f]&&"YYYY"!==l[f]&&(l[f]="YY"):i[f]>12&&"mm"===l[f]&&(l[f]="dd");3===l.length&&"dd"===l[1]&&"dd"===l[2]&&(l[2]="YY"),n=l.join("/")}return r&&(this.options.dateFormat=n),n}emit(e){j(this,e.type,e)}export(e,t){throw this.emit({type:"exportError",columns:[],headers:[]}),Error("Not implemented")}getTable(){throw Error("Not implemented")}guessType(e){let t="string";if("string"==typeof e){let r=this.trim(`${e}`),s=this.decimalRegExp,i=this.trim(r,!0);s&&(i=s.test(i)?i.replace(s,"$1.$2"):"");let n=parseFloat(i);+i===n?e=n:t=L(this.parseDate(e))?"Date":"string"}return"number"==typeof e&&(t=e>31536e6?"Date":"number"),t}on(e,t){return D(this,e,t)}parse(e){throw this.emit({type:"parseError",columns:[],headers:[]}),Error("Not implemented")}parseDate(e,t){let r=this.options,s=t||r.dateFormat,i=NaN,n,o,l;if(r.parseDate)i=r.parseDate(e);else{if(s)(o=this.dateFormats[s])||(o=this.dateFormats["YYYY/mm/dd"]),(l=e.match(o.regex))&&(i=o.parser(l));else for(n in this.dateFormats)if(o=this.dateFormats[n],l=e.match(o.regex)){s=n,i=o.parser(l);break}!l&&("object"==typeof(l=Date.parse(e))&&null!==l&&l.getTime?i=l.getTime()-6e4*l.getTimezoneOffset():L(l)&&(i=l-6e4*new Date(l).getTimezoneOffset(),-1===e.indexOf("2001")&&2001===new Date(i).getFullYear()&&(i=NaN)))}return i}trim(e,t){return"string"==typeof e&&(e=e.replace(/^\s+|\s+$/g,""),t&&/^[\d\s]+$/.test(e)&&(e=e.replace(/\s/g,""))),e}}k.defaultOptions={dateFormat:"",alternativeFormat:"",startColumn:0,endColumn:Number.MAX_VALUE,startRow:0,endRow:Number.MAX_VALUE,firstRowAsNames:!0,switchRowsAndColumns:!1},function(e){e.types={},e.registerType=function(t,r){return!!t&&!e.types[t]&&!!(e.types[t]=r)},e.getTableFromColumns=function(e=[],t=[]){let r=new A;for(let s=0,i=Math.max(t.length,e.length);s<i;++s)r.setColumn(t[s]||`${s}`,e[s]);return r}}(k||(k={}));let Y=k;class V{constructor(e={}){this.emittingRegister=[],this.listenerMap={},this.stateMap=e}addListener(e,t,r){let s=this.listenerMap[e]=this.listenerMap[e]||{};return(s[t]=s[t]||[]).push(r),this}buildEmittingTag(e){return("position"===e.cursor.type?[e.table.id,e.cursor.column,e.cursor.row,e.cursor.state,e.cursor.type]:[e.table.id,e.cursor.columns,e.cursor.firstRow,e.cursor.lastRow,e.cursor.state,e.cursor.type]).join("\0")}emitCursor(e,t,r,s){let i=e.id,n=t.state,o=this.listenerMap[i]&&this.listenerMap[i][n];if(o){let n=this.stateMap[i]=this.stateMap[i]??{},l=n[t.state]||[];s&&(l.length||(n[t.state]=l),-1===V.getIndex(t,l)&&l.push(t));let a={cursor:t,cursors:l,table:e};r&&(a.event=r);let u=this.emittingRegister,h=this.buildEmittingTag(a);if(u.indexOf(h)>=0)return this;try{this.emittingRegister.push(h);for(let e=0,t=o.length;e<t;++e)o[e].call(this,a)}finally{let e=this.emittingRegister.indexOf(h);e>=0&&this.emittingRegister.splice(e,1)}}return this}remitCursor(e,t){let r=this.stateMap[e]&&this.stateMap[e][t.state];if(r){let e=V.getIndex(t,r);e>=0&&r.splice(e,1)}return this}removeListener(e,t,r){let s=this.listenerMap[e]&&this.listenerMap[e][t];if(s){let e=s.indexOf(r);e>=0&&s.splice(e,1)}return this}}V.version="1.0.0",function(e){function t(e,t){if("range"===e.type)return e;let r={type:"range",firstRow:e.row??(t&&t.firstRow)??0,lastRow:e.row??(t&&t.lastRow)??Number.MAX_VALUE,state:e.state};return void 0!==e.column&&(r.columns=[e.column]),r}e.getIndex=function(e,t){if("position"===e.type){for(let r,s=0,i=t.length;s<i;++s)if("position"===(r=t[s]).type&&r.state===e.state&&r.column===e.column&&r.row===e.row)return s}else{let r=JSON.stringify(e.columns);for(let s,i=0,n=t.length;i<n;++i)if("range"===(s=t[i]).type&&s.state===e.state&&s.firstRow===e.firstRow&&s.lastRow===e.lastRow&&JSON.stringify(s.columns)===r)return i}return -1},e.isEqual=function(e,t){return"position"===e.type&&"position"===t.type?e.column===t.column&&e.row===t.row&&e.state===t.state:"range"===e.type&&"range"===t.type&&e.firstRow===t.firstRow&&e.lastRow===t.lastRow&&JSON.stringify(e.columns)===JSON.stringify(t.columns)},e.isInRange=function(e,r){"position"===r.type&&(r=t(r)),"position"===e.type&&(e=t(e,r));let s=e.columns,i=r.columns;return e.firstRow>=r.firstRow&&e.lastRow<=r.lastRow&&(!s||!i||s.every(e=>i.indexOf(e)>=0))},e.toPositions=function(e){if("position"===e.type)return[e];let t=e.columns||[],r=[],s=e.state;for(let i=e.firstRow,n=e.lastRow;i<n;++i){if(!t.length){r.push({type:"position",row:i,state:s});continue}for(let e=0,n=t.length;e<n;++e)r.push({type:"position",column:t[e],row:i,state:s})}return r},e.toRange=t}(V||(V={}));let $=V,U={connectors:[]};class H{constructor(e=U){e.connectors=e.connectors||[],this.connectors={},this.options=e,this.waiting={}}emit(e){l().fireEvent(this,e.type,e)}getConnector(e){let t=this.connectors[e];if(t?.loaded)return Promise.resolve(t);let r=this.waiting[e];if(!r){r=this.waiting[e]=[];let t=this.getConnectorOptions(e);if(!t)throw Error(`Connector '${e}' not found.`);this.loadConnector(t).then(t=>{delete this.waiting[e];for(let e=0,s=r.length;e<s;++e)r[e][0](t)}).catch(t=>{delete this.waiting[e];for(let e=0,s=r.length;e<s;++e)r[e][1](t)})}return new Promise((e,t)=>{r.push([e,t])})}getConnectorIds(){let e=this.options.connectors,t=[];for(let r=0,s=e.length;r<s;++r)t.push(e[r].id);return t}getConnectorOptions(e){let t=this.options.connectors;for(let r=0,s=t.length;r<s;++r)if(t[r].id===e)return t[r]}getConnectorTable(e){return this.getConnector(e).then(e=>e.table)}isNewConnector(e){return!this.connectors[e]}loadConnector(e){return new Promise((t,r)=>{this.emit({type:"load",options:e});let s=P.types[e.type];if(!s)throw Error(`Connector type not found. (${e.type})`);let i=this.connectors[e.id]=new s(e.options,e.dataTables);i.load().then(({converter:r,dataTables:s})=>{i.dataTables=s,i.converter=r,i.loaded=!0,this.emit({type:"afterLoad",options:e}),t(i)}).catch(r)})}cancelPendingRequests(){let{connectors:e}=this;for(let t of Object.keys(e))e[t].stopPolling()}on(e,t){return l().addEvent(this,e,t)}setConnectorOptions(e){let t=this.options.connectors,r=this.connectors;this.emit({type:"setConnectorOptions",options:e});for(let r=0,s=t.length;r<s;++r)if(t[r].id===e.id){t.splice(r,1);break}r[e.id]&&(r[e.id].stopPolling(),delete r[e.id]),t.push(e),this.emit({type:"afterSetConnectorOptions",options:e})}}H.version="1.0.0";let _=/^(?:FALSE|TRUE)/,B=/^[+\-]?\d+(?:\.\d+)?(?:e[+\-]\d+)?/,G=/^[+\-]?\d+(?:,\d+)?(?:e[+\-]\d+)?/,Z=/^([A-Z][A-Z\d\.]*)\(/,J=/^(?:[+\-*\/^<=>]|<=|=>)/,X=/^(\$?[A-Z]+)(\$?\d+)\:(\$?[A-Z]+)(\$?\d+)/,K=/^R(\d*|\[\d+\])C(\d*|\[\d+\])\:R(\d*|\[\d+\])C(\d*|\[\d+\])/,q=/^(\$?[A-Z]+)(\$?\d+)(?![\:C])/,z=/^R(\d*|\[\d+\])C(\d*|\[\d+\])(?!\:)/;function Q(e){let t=0;for(let r=0,s=e.length,i,n=1;r<s;++r){if("("===(i=e[r])){t||(n=r+1),++t;continue}if(")"===i&&!--t)return e.substring(n,r)}if(t>0){let e=Error("Incomplete parantheses.");throw e.name="FormulaParseError",e}return""}function W(e){let t=-1;for(let r=0,s=e.length,i,n=!1;r<s;++r){if("\\"===(i=e[r])){n=!n;continue}if(n){n=!1;continue}if('"'===i)if(!(t<0))return e.substring(t+1,r);else t=r}let r=Error("Incomplete string.");throw r.name="FormulaParseError",r}function ee(e,t){let r;if(r=e.match(K)){let e=""===r[2]||"["===r[2][0],t=""===r[1]||"["===r[1][0],s=""===r[4]||"["===r[4][0],i=""===r[3]||"["===r[3][0],n={type:"range",beginColumn:e?parseInt(r[2].substring(1,-1)||"0",10):parseInt(r[2],10)-1,beginRow:t?parseInt(r[1].substring(1,-1)||"0",10):parseInt(r[1],10)-1,endColumn:s?parseInt(r[4].substring(1,-1)||"0",10):parseInt(r[4],10)-1,endRow:i?parseInt(r[3].substring(1,-1)||"0",10):parseInt(r[3],10)-1};return e&&(n.beginColumnRelative=!0),t&&(n.beginRowRelative=!0),s&&(n.endColumnRelative=!0),i&&(n.endRowRelative=!0),n}if(r=e.match(X)){let e="$"!==r[1][0],t="$"!==r[2][0],s="$"!==r[3][0],i="$"!==r[4][0],n={type:"range",beginColumn:er(e?r[1]:r[1].substring(1))-1,beginRow:parseInt(t?r[2]:r[2].substring(1),10)-1,endColumn:er(s?r[3]:r[3].substring(1))-1,endRow:parseInt(i?r[4]:r[4].substring(1),10)-1};return e&&(n.beginColumnRelative=!0),t&&(n.beginRowRelative=!0),s&&(n.endColumnRelative=!0),i&&(n.endRowRelative=!0),n}let s=et(e,t);return 1===s.length&&"string"!=typeof s[0]?s[0]:s}function et(e,t){let r=t?G:B,s=[],i,n=("="===e[0]?e.substring(1):e).trim();for(;n;){if(i=n.match(z)){let e=""===i[2]||"["===i[2][0],t=""===i[1]||"["===i[1][0],r={type:"reference",column:e?parseInt(i[2].substring(1,-1)||"0",10):parseInt(i[2],10)-1,row:t?parseInt(i[1].substring(1,-1)||"0",10):parseInt(i[1],10)-1};e&&(r.columnRelative=!0),t&&(r.rowRelative=!0),s.push(r),n=n.substring(i[0].length).trim();continue}if(i=n.match(q)){let e="$"!==i[1][0],t="$"!==i[2][0],r={type:"reference",column:er(e?i[1]:i[1].substring(1))-1,row:parseInt(t?i[2]:i[2].substring(1),10)-1};e&&(r.columnRelative=!0),t&&(r.rowRelative=!0),s.push(r),n=n.substring(i[0].length).trim();continue}if(i=n.match(J)){s.push(i[0]),n=n.substring(i[0].length).trim();continue}if(i=n.match(_)){s.push("TRUE"===i[0]),n=n.substring(i[0].length).trim();continue}if(i=n.match(r)){s.push(parseFloat(i[0])),n=n.substring(i[0].length).trim();continue}if('"'===n[0]){let e=W(n);s.push(e.substring(1,-1)),n=n.substring(e.length+2).trim();continue}if(i=n.match(Z)){let e=Q(n=n.substring(i[1].length).trim());s.push({type:"function",name:i[1],args:function(e,t){let r=[],s=t?";":",",i=0,n="";for(let o=0,l=e.length,a;o<l;++o)if((a=e[o])===s&&!i&&n)r.push(ee(n,t)),n="";else if('"'!==a||i||n)" "!==a&&(n+=a,"("===a?++i:")"===a&&--i);else{let t=W(e.substring(o));r.push(t),o+=t.length+1}return!i&&n&&r.push(ee(n,t)),r}(e,t)}),n=n.substring(e.length+2).trim();continue}if("("===n[0]){let e=Q(n);if(e){s.push(et(e,t)),n=n.substring(e.length+2).trim();continue}}let o=e.length-n.length,l=Error("Unexpected character `"+e.substring(o,o+1)+"` at position "+(o+1)+". (`..."+e.substring(o-5,o+6)+"...`)");throw l.name="FormulaParseError",l}return s}function er(e){let t=0;for(let r=0,s=e.length,i,n=e.length-1;r<s;++r)(i=e.charCodeAt(r))>=65&&i<=90&&(t+=(i-64)*Math.pow(26,n)),--n;return t}let es={parseFormula:et},ei=["+","-","*","/","^","=","<","<=",">",">="],en={isFormula:function(e){return e instanceof Array},isFunction:function(e){return"object"==typeof e&&!(e instanceof Array)&&"function"===e.type},isOperator:function(e){return"string"==typeof e&&ei.indexOf(e)>=0},isRange:function(e){return"object"==typeof e&&!(e instanceof Array)&&"range"===e.type},isReference:function(e){return"object"==typeof e&&!(e instanceof Array)&&"reference"===e.type},isValue:function(e){return"boolean"==typeof e||"number"==typeof e||"string"==typeof e}},{isFormula:eo,isFunction:el,isOperator:ea,isRange:eu,isReference:eh,isValue:ef}=en,em=/ */,ec=Number.MAX_VALUE/1.000000000001,ed=Number.MAX_VALUE/1.000000000002,eg=Number.MAX_VALUE,ep={"^":3,"*":2,"/":2,"+":1,"-":1,"=":0,"<":0,"<=":0,">":0,">=":0},ey={},eb=/^[A-Z][A-Z\.]*$/;function ew(e){switch(typeof e){case"boolean":return e?eg:ec;case"string":return ed;case"number":return e;default:return NaN}}function eC(e){return"string"==typeof e?e.toLowerCase().replace(em,"\0"):e}function eR(e){switch(typeof e){case"boolean":return+!!e;case"string":return parseFloat(e.replace(",","."));case"number":return e;default:return NaN}}function eN(e,t,r){let s;switch(e){case"=":return eC(t)===eC(r);case"<":if(typeof t==typeof r)return eC(t)<eC(r);return ew(t)<ew(r);case"<=":if(typeof t==typeof r)return eC(t)<=eC(r);return ew(t)<=ew(r);case">":if(typeof t==typeof r)return eC(t)>eC(r);return ew(t)>ew(r);case">=":if(typeof t==typeof r)return eC(t)>=eC(r);return ew(t)>=ew(r)}switch(t=eR(t),r=eR(r),e){case"+":s=t+r;break;case"-":s=t-r;break;case"*":s=t*r;break;case"/":s=t/r;break;case"^":s=Math.pow(t,r);break;default:return NaN}return s%1?Math.round(1e9*s)/1e9:s}function eT(e,t){return ef(e)?e:eu(e)?t&&ev(e,t)||[]:el(e)?ex(e,t):eA(eo(e)?e:[e],t)}function ev(e,t){let r=t.getColumnNames().slice(e.beginColumn,e.endColumn+1),s=[];for(let i=0,n=r.length,o;i<n;++i){let n=t.getColumn(r[i],!0)||[];for(let l=e.beginRow,a=e.endRow+1;l<a;++l)"string"==typeof(o=n[l])&&"="===o[0]&&t!==t.modified&&(o=t.modified.getCell(r[i],l)),s.push(ef(o)?o:NaN)}return s}function eO(e,t){let r=t.getColumnNames()[e.column];if(r){let s=t.getCell(r,e.row);if("string"==typeof s&&"="===s[0]&&t!==t.modified){let s=t.modified.getCell(r,e.row);return ef(s)?s:NaN}return ef(s)?s:NaN}return NaN}function eA(e,t){let r;for(let s=0,i=e.length,n,o,l,a;s<i;++s){if(ea(n=e[s])){o=n;continue}if(ef(n)?a=n:eo(n)?a=eA(e,t):el(n)?a=ef(l=ex(n,t))?l:NaN:eh(n)&&(a=t&&eO(n,t)),void 0!==a){if(void 0===r)r=o?eN(o,0,a):a;else{if(!o)return NaN;let t=e[s+1];ea(t)&&ep[t]>ep[o]&&(a=eN(t,a,eA(e.slice(s+2))),s=i),r=eN(o,r,a)}o=void 0,a=void 0}}return ef(r)?r:NaN}function ex(e,t,r){let s=ey[e.name];if(s)try{return s(e.args,t)}catch{return NaN}let i=Error(`Function "${e.name}" not found.`);throw i.name="FormulaProcessError",i}let eF={asNumber:eR,getArgumentValue:eT,getArgumentsValues:function(e,t){let r=[];for(let s=0,i=e.length;s<i;++s)r.push(eT(e[s],t));return r},getRangeValues:ev,getReferenceValue:eO,processFormula:eA,processorFunctions:ey,registerProcessorFunction:function(e,t){return eb.test(e)&&!ey[e]&&!!(ey[e]=t)},translateReferences:function e(t,r=0,s=0){for(let i=0,n=t.length,o;i<n;++i)(o=t[i])instanceof Array?e(o,r,s):el(o)?e(o.args,r,s):eu(o)?(o.beginColumnRelative&&(o.beginColumn+=r),o.beginRowRelative&&(o.beginRow+=s),o.endColumnRelative&&(o.endColumn+=r),o.endRowRelative&&(o.endRow+=s)):eh(o)&&(o.columnRelative&&(o.column+=r),o.rowRelative&&(o.row+=s));return t}},{getArgumentValue:eM}=eF;eF.registerProcessorFunction("ABS",function(e,t){let r=eM(e[0],t);switch(typeof r){case"number":return Math.abs(r);case"object":{let e=[];for(let t=0,s=r.length,i;t<s;++t){if("number"!=typeof(i=r[t]))return NaN;e.push(Math.abs(i))}return e}default:return NaN}});let{getArgumentValue:eE}=eF;eF.registerProcessorFunction("AND",function e(t,r){for(let s=0,i=t.length,n;s<i;++s)if(!(n=eE(t[s],r))||"object"==typeof n&&!e(n,r))return!1;return!0});let{getArgumentsValues:eI}=eF;eF.registerProcessorFunction("AVERAGE",function(e,t){let r=eI(e,t),s=0,i=0;for(let e=0,t=r.length,n;e<t;++e)switch(typeof(n=r[e])){case"number":isNaN(n)||(++s,i+=n);break;case"object":for(let e=0,t=n.length,r;e<t;++e)"number"!=typeof(r=n[e])||isNaN(r)||(++s,i+=r)}return s?i/s:0});let{getArgumentValue:eP}=eF;eF.registerProcessorFunction("AVERAGEA",function(e,t){let r=0,s=0;for(let i=0,n=e.length,o;i<n;++i)switch(typeof(o=eP(e[i],t))){case"boolean":++r,s+=+!!o;continue;case"number":isNaN(o)||(++r,s+=o);continue;case"string":++r;continue;default:for(let e=0,t=o.length,i;e<t;++e)switch(typeof(i=o[e])){case"boolean":++r,s+=+!!i;continue;case"number":isNaN(i)||(++r,s+=i);continue;case"string":++r;continue}continue}return r?s/r:0}),eF.registerProcessorFunction("COUNT",function e(t,r){let s=eF.getArgumentsValues(t,r),i=0;for(let t=0,n=s.length,o;t<n;++t)switch(typeof(o=s[t])){case"number":!isNaN(o)&&++i;break;case"object":i+=e(o,r)}return i}),eF.registerProcessorFunction("COUNTA",function e(t,r){let s=eF.getArgumentsValues(t,r),i=0;for(let t=0,n=s.length,o;t<n;++t){switch(typeof(o=s[t])){case"number":if(isNaN(o))continue;break;case"object":i+=e(o,r);continue;case"string":if(!o)continue}++i}return i});let{getArgumentValue:eD}=eF;eF.registerProcessorFunction("IF",function(e,t){return eD(e[0],t)?eD(e[1],t):eD(e[2],t)});let{getArgumentValue:ej}=eF;eF.registerProcessorFunction("ISNA",function(e,t){let r=ej(e[0],t);return"number"!=typeof r||isNaN(r)});let{getArgumentsValues:eL}=eF;eF.registerProcessorFunction("MAX",function e(t,r){let s=eL(t,r),i=Number.NEGATIVE_INFINITY;for(let t=0,r=s.length,n;t<r;++t)switch(typeof(n=s[t])){case"number":n>i&&(i=n);break;case"object":(n=e(n))>i&&(i=n)}return isFinite(i)?i:0}),eF.registerProcessorFunction("MEDIAN",function(e,t){let r=[],s=eF.getArgumentsValues(e,t);for(let e=0,t=s.length,i;e<t;++e)switch(typeof(i=s[e])){case"number":isNaN(i)||r.push(i);break;case"object":for(let e=0,t=i.length,s;e<t;++e)"number"!=typeof(s=i[e])||isNaN(s)||r.push(s)}let i=r.length;if(!i)return NaN;let n=Math.floor(i/2);return i%2?r[n]:(r[n-1]+r[n])/2});let{getArgumentsValues:eS}=eF;eF.registerProcessorFunction("MIN",function e(t,r){let s=eS(t,r),i=Number.POSITIVE_INFINITY;for(let t=0,r=s.length,n;t<r;++t)switch(typeof(n=s[t])){case"number":n<i&&(i=n);break;case"object":(n=e(n))<i&&(i=n)}return isFinite(i)?i:0});let{getArgumentValue:ek}=eF;function eY(e,t){let r={},s=eF.getArgumentsValues(e,t);for(let e=0,t=s.length,i;e<t;++e)switch(typeof(i=s[e])){case"number":isNaN(i)||(r[i]=(r[i]||0)+1);break;case"object":for(let e=0,t=i.length,s;e<t;++e)"number"!=typeof(s=i[e])||isNaN(s)||(r[s]=(r[s]||0)+1)}return r}function eV(e,t){let r=eY(e,t),s=Object.keys(r);if(!s.length)return NaN;let i=parseFloat(s[0]),n=r[s[0]];for(let e=1,t=s.length,o,l,a;e<t;++e)n<(a=r[o=s[e]])?(i=parseFloat(o),n=a):n===a&&i>(l=parseFloat(o))&&(i=l,n=a);return n>1?i:NaN}eF.registerProcessorFunction("MOD",function(e,t){let r=ek(e[0],t),s=ek(e[1],t);return("object"==typeof r&&(r=r[0]),"object"==typeof s&&(s=s[0]),"number"!=typeof r||"number"!=typeof s||0===s)?NaN:r%s}),eF.registerProcessorFunction("MODE",eV),eF.registerProcessorFunction("MODE.MULT",function(e,t){let r=eY(e,t),s=Object.keys(r);if(!s.length)return NaN;let i=[parseFloat(s[0])],n=r[s[0]];for(let e=1,t=s.length,o,l;e<t;++e)n<(l=r[o=s[e]])?(i=[parseFloat(o)],n=l):n===l&&i.push(parseFloat(o));return n>1?i:NaN}),eF.registerProcessorFunction("MODE.SNGL",eV);let{getArgumentValue:e$}=eF;eF.registerProcessorFunction("NOT",function(e,t){let r=e$(e[0],t);switch("object"==typeof r&&(r=r[0]),typeof r){case"boolean":case"number":return!r}return NaN});let{getArgumentValue:eU}=eF;eF.registerProcessorFunction("OR",function e(t,r){for(let s=0,i=t.length,n;s<i;++s)if("object"==typeof(n=eU(t[s],r))){if(e(n,r))return!0}else if(n)return!0;return!1});let{getArgumentsValues:eH}=eF;eF.registerProcessorFunction("PRODUCT",function e(t,r){let s=eH(t,r),i=1,n=!1;for(let t=0,o=s.length,l;t<o;++t)switch(typeof(l=s[t])){case"number":isNaN(l)||(n=!0,i*=l);break;case"object":n=!0,i*=e(l,r)}return n?i:0}),eF.registerProcessorFunction("SUM",function e(t,r){let s=eF.getArgumentsValues(t,r),i=0;for(let t=0,n=s.length,o;t<n;++t)switch(typeof(o=s[t])){case"number":isNaN(o)||(i+=o);break;case"object":i+=e(o,r)}return i});let{getArgumentValue:e_}=eF;eF.registerProcessorFunction("XOR",function(e,t){for(let r=0,s=e.length,i,n;r<s;++r)switch(typeof(n=e_(e[r],t))){case"boolean":case"number":if(void 0===i)i=!!n;else if(!!n!==i)return!0;break;case"object":for(let e=0,t=n.length,r;e<t;++e)switch(typeof(r=n[e])){case"boolean":case"number":if(void 0===i)i=!!r;else if(!!r!==i)return!0}}return!1});let eB={...es,...eF,...en},{merge:eG}=l();class eZ extends Y{constructor(e){let t=eG(eZ.defaultOptions,e);super(t),this.columns=[],this.headers=[],this.dataTypes=[],this.options=t}export(e,t=this.options){let{useLocalDecimalPoint:r,lineDelimiter:s}=t,i=!1!==this.options.firstRowAsNames,{decimalPoint:n,itemDelimiter:o}=t;n||(n=","!==o&&r?1.1.toLocaleString()[1]:"."),o||(o=","===n?";":",");let l=e.getSortedColumns(t.usePresentationOrder),a=Object.keys(l),u=[],h=a.length,f=[];i&&u.push(a.map(e=>`"${e}"`).join(o));for(let t=0;t<h;t++){let r,s=a[t],i=l[s],m=i.length,c=e.whatIs(s);c&&(r=c.dataType);for(let e=0;e<m;e++){let s=i[e];if(f[e]||(f[e]=[]),"string"===r?s='"'+s+'"':"number"==typeof s?s=String(s).replace(".",n):"string"==typeof s&&(s=`"${s}"`),f[e][t]=s,t===h-1){let r=t;for(;f[e].length>2&&void 0===f[e][r];)f[e].pop(),r--;u.push(f[e].join(o))}}}return u.join(s)}parse(e,t){let r=this.dataTypes,s=eG(this.options,e),{beforeParse:i,lineDelimiter:n,firstRowAsNames:o,itemDelimiter:l}=s,a,u=0,{csv:h,startRow:f,endRow:m}=s,c;if(this.columns=[],this.emit({type:"parse",columns:this.columns,detail:t,headers:this.headers}),h&&i&&(h=i(h)),h){if(a=h.replace(/\r\n|\r/g,"\n").split(n||"\n"),(!f||f<0)&&(f=0),(!m||m>=a.length)&&(m=a.length-1),l||(this.guessedItemDelimiter=this.guessDelimiter(a)),o){let e=a[0].split(l||this.guessedItemDelimiter||",");for(let t=0;t<e.length;t++)e[t]=e[t].trim().replace(/^["']|["']$/g,"");this.headers=e,f++}let e=0;for(u=f;u<=m;u++)"#"===a[u][0]?e++:this.parseCSVRow(a[u],u-f-e);r.length&&r[0].length&&"date"===r[0][1]&&!this.options.dateFormat&&this.deduceDateFormat(this.columns[0],null,!0);for(let e=0,t=this.columns.length;e<t;++e){c=this.columns[e];for(let t=0,r=c.length;t<r;++t)if(c[t]&&"string"==typeof c[t]){let r=this.asGuessedType(c[t]);r instanceof Date&&(r=r.getTime()),this.columns[e][t]=r}}}this.emit({type:"afterParse",columns:this.columns,detail:t,headers:this.headers})}parseCSVRow(e,t){let r=this,s=r.columns||[],i=r.dataTypes,{startColumn:n,endColumn:o}=r.options,l=r.options.itemDelimiter||r.guessedItemDelimiter,{decimalPoint:a}=r.options;a&&a!==l||(a=r.guessedDecimalPoint||".");let u=0,h="",f="",m=0,c=0,d=t=>{h=e[t]},g=e=>{i.length<c+1&&i.push([e]),i[c][i[c].length-1]!==e&&i[c].push(e)},p=()=>{if(n>m||m>o){++m,f="";return}if("string"==typeof f?!isNaN(parseFloat(f))&&isFinite(f)?(f=parseFloat(f),g("number")):isNaN(Date.parse(f))?g("string"):(f=f.replace(/\//g,"-"),g("date")):g("number"),s.length<c+1&&s.push([]),"number"!=typeof f&&"number"!==r.guessType(f)&&a){let e=f;f=f.replace(a,"."),"number"!==r.guessType(f)&&(f=e)}s[c][t]=f,f="",++c,++m};if(e.trim().length&&"#"!==e.trim()[0]){for(;u<e.length;u++){if(d(u),"#"===h&&!/^#[A-F\d]{3,3}|[A-F\d]{6,6}/i.test(e.substring(u)))return void p();if('"'===h)for(d(++u);u<e.length&&'"'!==h;)f+=h,d(++u);else h===l?p():f+=h}p()}}guessDelimiter(e){let t=0,r=0,s,i={",":0,";":0,"	":0},n=e.length;for(let s=0;s<n;s++){let n=!1,o,l,a,u="";if(s>13)break;let h=e[s];for(let e=0;e<h.length&&(o=h[e],l=h[e+1],a=h[e-1],"#"!==o);e++){if('"'===o)if(n){if('"'!==a&&'"'!==l){for(;" "===l&&e<h.length;)l=h[++e];void 0!==i[l]&&i[l]++,n=!1}}else n=!0;else void 0!==i[o]?(isNaN(Date.parse(u=u.trim()))?(isNaN(Number(u))||!isFinite(Number(u)))&&i[o]++:i[o]++,u=""):u+=o;","===o&&r++,"."===o&&t++}}return i[";"]>i[","]?s=";":(i[","]>i[";"],s=","),t>r?this.guessedDecimalPoint=".":this.guessedDecimalPoint=",",s}getTable(){return Y.getTableFromColumns(this.columns,this.headers)}}eZ.defaultOptions={...Y.defaultOptions,lineDelimiter:"\n"},Y.registerType("CSV",eZ);let{merge:eJ,defined:eX}=l();class eK extends P{constructor(e,t){let r=eJ(eK.defaultOptions,e);super(r,t),this.options=eX(t)?eJ(r,{dataTables:t}):r,r.enablePolling&&this.startPolling(1e3*Math.max(r.dataRefreshRate||0,1))}load(e){let t=this,r=t.dataTables,{csv:s,csvURL:i,dataModifier:n,dataTables:o}=t.options;return t.emit({type:"load",csv:s,detail:e,tables:r}),Promise.resolve(i?fetch(i,{signal:t?.pollingController?.signal}).then(e=>e.text()):s||"").then(e=>(e&&this.initConverters(e,e=>{let t=this.options,r=o?.find(t=>t.key===e),s={dataTableKey:e,firstRowAsNames:r?.firstRowAsNames??t.firstRowAsNames,beforeParse:r?.beforeParse??t.beforeParse};return new eZ(eJ(this.options,s))},(e,t)=>{e.parse({csv:t})}),t.setModifierOptions(n,o).then(()=>e))).then(s=>(t.emit({type:"afterLoad",csv:s,detail:e,tables:r}),t)).catch(s=>{throw t.emit({type:"loadError",detail:e,error:s,tables:r}),s})}}eK.defaultOptions={csv:"",csvURL:"",enablePolling:!1,dataRefreshRate:1,firstRowAsNames:!0},P.registerType("CSV",eK);let{error:eq,isArray:ez,merge:eQ,objectEach:eW}=l();class e0 extends Y{constructor(e){let t=eQ(e0.defaultOptions,e);super(t),this.columns=[],this.headers=[],this.options=t,this.table=new A}parse(e,t){let{beforeParse:r,orientation:s,firstRowAsNames:i,columnNames:n}=e=eQ(this.options,e),o=e.data;if(o){if(this.columns=[],this.emit({type:"parse",columns:this.columns,detail:t,headers:this.headers}),r&&(o=r(o)),o=o.slice(),"columns"===s)for(let e=0,t=o.length;e<t;e++){let t=o[e];if(!(t instanceof Array))return;this.headers instanceof Array?(i?this.headers.push(`${t.shift()}`):n&&n instanceof Array&&this.headers.push(n[e]),this.table.setColumn(this.headers[e]||e.toString(),t)):eq("JSONConverter: Invalid `columnNames` option.",!1)}else if("rows"===s){i?this.headers=o.shift():n&&(this.headers=n);for(let e=0,t=o.length;e<t;e++){let t=o[e];if(ez(t))for(let e=0,r=t.length;e<r;e++)this.columns.length<e+1&&this.columns.push([]),this.columns[e].push(t[e]),this.headers instanceof Array?this.table.setColumn(this.headers[e]||e.toString(),this.columns[e]):eq("JSONConverter: Invalid `columnNames` option.",!1);else{let r=this.headers;if(r&&!(r instanceof Array)){let e={};eW(r,(r,s)=>{e[s]=r.reduce((e,t)=>e[t],t)}),t=e}this.table.setRows([t],e)}}}this.emit({type:"afterParse",columns:this.columns,detail:t,headers:this.headers})}}getTable(){return this.table}}e0.defaultOptions={...Y.defaultOptions,data:[],orientation:"rows"},Y.registerType("JSON",e0);let{merge:e1,defined:e2}=l();class e3 extends P{constructor(e,t){let r=e1(e3.defaultOptions,e);super(r,t),this.options=e2(t)?e1(r,{dataTables:t}):r,r.enablePolling&&this.startPolling(1e3*Math.max(r.dataRefreshRate||0,1))}load(e){let t=this,r=t.dataTables,{data:s,dataUrl:i,dataModifier:n,dataTables:o}=t.options;return t.emit({type:"load",data:s,detail:e,tables:r}),Promise.resolve(i?fetch(i,{signal:t?.pollingController?.signal}).then(e=>e.json()).catch(s=>{t.emit({type:"loadError",detail:e,error:s,tables:r}),console.warn(`Unable to fetch data from ${i}.`)}):s||[]).then(e=>(e&&this.initConverters(e,e=>{let t=this.options,r=o?.find(t=>t.key===e),s={dataTableKey:e,columnNames:r?.columnNames??t.columnNames,firstRowAsNames:r?.firstRowAsNames??t.firstRowAsNames,orientation:r?.orientation??t.orientation,beforeParse:r?.beforeParse??t.beforeParse};return new e0(e1(this.options,s))},(e,t)=>{e.parse({data:t})}),t.setModifierOptions(n,o).then(()=>e))).then(s=>(t.emit({type:"afterLoad",data:s,detail:e,tables:r}),t)).catch(s=>{throw t.emit({type:"loadError",detail:e,error:s,tables:r}),s})}}e3.defaultOptions={data:[],enablePolling:!1,dataRefreshRate:0,firstRowAsNames:!0,orientation:"rows"},P.registerType("JSON",e3);let{merge:e4,uniqueKey:e6}=l();class e9 extends Y{constructor(e){let t=e4(e9.defaultOptions,e);super(t),this.columns=[],this.header=[],this.options=t}parse(e,t){let r,s=e4(this.options,e),i=(s.json?.values||[]).map(e=>e.slice());if(0===i.length)return!1;this.header=[],this.columns=[],this.emit({type:"parse",columns:this.columns,detail:t,headers:this.header});let{beforeParse:n,json:o}=s;n&&o&&(i=n(o.values)),this.columns=i;for(let e=0,t=i.length;e<t;e++){r=i[e],this.header[e]=s.firstRowAsNames?`${r.shift()}`:e6();for(let t=0,s=r.length;t<s;++t)if(r[t]&&"string"==typeof r[t]){let s=this.asGuessedType(r[t]);s instanceof Date&&(s=s.getTime()),this.columns[e][t]=s}}this.emit({type:"afterParse",columns:this.columns,detail:t,headers:this.header})}getTable(){return Y.getTableFromColumns(this.columns,this.header)}}e9.defaultOptions={...Y.defaultOptions},Y.registerType("GoogleSheets",e9);let{merge:e5,pick:e7,defined:e8}=l();class te extends P{constructor(e,t){let r=e5(te.defaultOptions,e);super(r,t),this.options=e8(t)?e5(r,{dataTables:t}):r}load(e){let t=this,r=t.dataTables,{dataModifier:s,dataRefreshRate:i,enablePolling:n,googleAPIKey:o,googleSpreadsheetKey:l,dataTables:a}=t.options,u=te.buildFetchURL(o,l,t.options);if(t.emit({type:"load",detail:e,tables:r,url:u}),!URL.canParse(u))throw Error("Invalid URL: "+u);return fetch(u,{signal:t?.pollingController?.signal}).then(e=>e.json()).then(e=>{if("object"==typeof e&&e&&"object"==typeof e.error&&e.error&&"number"==typeof e.error.code&&"string"==typeof e.error.message&&"string"==typeof e.error.status)throw Error(e.error.message);return this.initConverters(e,e=>{let t=this.options,r=a?.find(t=>t.key===e),s={dataTableKey:e,firstRowAsNames:r?.firstRowAsNames??t.firstRowAsNames,beforeParse:r?.beforeParse??t.beforeParse};return new e9(e5(this.options,s))},(e,t)=>{e.parse({json:t})}),t.setModifierOptions(s,a)}).then(()=>(t.emit({type:"afterLoad",detail:e,tables:r,url:u}),n&&setTimeout(()=>t.load(),1e3*Math.max(i||0,1)),t)).catch(s=>{throw t.emit({type:"loadError",detail:e,error:s,tables:r}),s})}}te.defaultOptions={googleAPIKey:"",googleSpreadsheetKey:"",enablePolling:!1,dataRefreshRate:2,firstRowAsNames:!0},function(e){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZ";function r(e={}){let{endColumn:s,endRow:i,googleSpreadsheetRange:n,startColumn:o,startRow:l}=e;return n||(t[o||0]||"A")+(Math.max(l||0,0)+1)+":"+(t[e7(s,25)]||"Z")+(i?Math.max(i,0):"Z")}e.buildFetchURL=function(e,t,s={}){let i=new URL(`https://sheets.googleapis.com/v4/spreadsheets/${t}/values/`),n=s.onlyColumnNames?"A1:Z1":r(s);i.pathname+=n;let o=i.searchParams;return o.set("alt","json"),s.onlyColumnNames||(o.set("dateTimeRenderOption","FORMATTED_STRING"),o.set("majorDimension","COLUMNS"),o.set("valueRenderOption","UNFORMATTED_VALUE")),o.set("prettyPrint","false"),o.set("key",e),i.href},e.buildQueryRange=r}(te||(te={})),P.registerType("GoogleSheets",te);let{merge:tt}=l();class tr extends Y{constructor(e){let t=tt(tr.defaultOptions,e);super(t),this.columns=[],this.headers=[],this.options=t,t.tableElement&&(this.tableElement=t.tableElement,this.tableElementID=t.tableElement.id)}export(e,t=this.options){let r=!1!==t.firstRowAsNames,s=t.useMultiLevelHeaders,i=e.getSortedColumns(t.usePresentationOrder),n=Object.keys(i),o=[],l=n.length,a=[],u="";if(r){let e=[];if(s){for(let t of n){let r=i[t];Array.isArray(r)||(r=Array.from(r));let s=(r.shift()||"").toString();i[t]=r,e.push(s)}u=this.getTableHeaderHTML(n,e,t)}else u=this.getTableHeaderHTML(void 0,n,t)}for(let e=0;e<l;e++){let t=i[n[e]],r=t.length;for(let s=0;s<r;s++){let r=t[s];a[s]||(a[s]=[]),"string"!=typeof r&&"number"!=typeof r&&void 0!==r&&(r=(r||"").toString()),a[s][e]=this.getCellHTMLFromValue(e?"td":"th",null,e?"":'scope="row"',r),e===l-1&&o.push("<tr>"+a[s].join("")+"</tr>")}}let h="";return t.tableCaption&&(h='<caption class="highcharts-table-caption">'+t.tableCaption+"</caption>"),"<table>"+h+u+"<tbody>"+o.join("")+"</tbody></table>"}getCellHTMLFromValue(e,t,r,s,i){let n=s,o="text"+(t?" "+t:"");return"number"==typeof n?(n=n.toString(),","===i&&(n=n.replace(".",i)),o="number"):s||(n="",o="empty"),"<"+e+(r?" "+r:"")+' class="'+o+'">'+n+"</"+e+">"}getTableHeaderHTML(e=[],t=[],r=this.options){let{useMultiLevelHeaders:s,useRowspanHeaders:i}=r,n="<thead>",o=0,l=t&&t.length,a,u=0,h;if(s&&e&&t&&!function(e,t){let r=e.length;if(t.length!==r)return!1;for(;--r;)if(e[r]!==t[r])return!1;return!0}(e,t)){for(n+="<tr>";o<l;++o)(a=e[o])===e[o+1]?++u:u?(n+=this.getCellHTMLFromValue("th","highcharts-table-topheading",'scope="col" colspan="'+(u+1)+'"',a),u=0):(a===t[o]?i?(h=2,delete t[o]):(h=1,t[o]=""):h=1,n+=this.getCellHTMLFromValue("th","highcharts-table-topheading",'scope="col"'+(h>1?' valign="top" rowspan="'+h+'"':""),a));n+="</tr>"}if(t){for(n+="<tr>",o=0,l=t.length;o<l;++o)void 0!==t[o]&&(n+=this.getCellHTMLFromValue("th",null,'scope="col"',t[o]));n+="</tr>"}return n+"</thead>"}parse(e,t){let r=[],s=[],i=tt(this.options,e),{endRow:n,startColumn:o,endColumn:l,firstRowAsNames:a}=i,u=i.tableElement||this.tableElement;if(!(u instanceof HTMLElement))return void this.emit({type:"parseError",columns:r,detail:t,headers:s,error:"Not a valid HTML Table"});this.tableElement=u,this.tableElementID=u.id,this.emit({type:"parse",columns:this.columns,detail:t,headers:this.headers});let h=u.getElementsByTagName("tr"),f=h.length,m=0,c,{startRow:d}=i;if(a&&f){let e=h[0].children,t=e.length;for(let r=o;r<t&&!(r>l);r++)("TD"===(c=e[r]).tagName||"TH"===c.tagName)&&s.push(c.innerHTML);d++}for(;m<f;){if(m>=d&&m<=n){let e=h[m].children,t=e.length,s=0;for(;s<t;){let t=s-o,i=r[t];if(("TD"===(c=e[s]).tagName||"TH"===c.tagName)&&s>=o&&s<=l){r[t]||(r[t]=[]);let e=this.asGuessedType(c.innerHTML);e instanceof Date&&(e=e.getTime()),r[t][m-d]=e;let s=1;for(;m-d>=s&&void 0===i[m-d-s];)i[m-d-s]=null,s++}s++}}m++}this.columns=r,this.headers=s,this.emit({type:"afterParse",columns:r,detail:t,headers:s})}getTable(){return Y.getTableFromColumns(this.columns,this.headers)}}tr.defaultOptions={...Y.defaultOptions,useRowspanHeaders:!0,useMultiLevelHeaders:!0},Y.registerType("HTMLTable",tr);let{win:ts}=l(),{merge:ti}=l();class tn extends P{constructor(e){let t=ti(tn.defaultOptions,e);super(t),this.converter=new tr(t),this.options=t}load(e){let t,r=this,s=r.converter,i=r.table,{dataModifier:n,table:o}=r.options;if(r.emit({type:"load",detail:e,tables:{table:i},tableElement:r.tableElement}),"string"==typeof o?(r.tableID=o,t=ts.document.getElementById(o)):r.tableID=(t=o).id,r.tableElement=t||void 0,!r.tableElement){let t="HTML table not provided, or element with ID not found";return r.emit({type:"loadError",detail:e,error:t,tables:{table:i}}),Promise.reject(Error(t))}return s.parse(ti({tableElement:r.tableElement},r.options),e),i.deleteColumns(),i.setColumns(s.getTable().getColumns()),r.setModifierOptions(n).then(()=>(r.emit({type:"afterLoad",detail:e,tables:{table:i},tableElement:r.tableElement}),r))}}tn.defaultOptions={table:""},P.registerType("HTMLTable",tn);let{merge:to}=l();class tl extends m{constructor(e,...t){super(),this.chain=t,this.options=to(tl.defaultOptions,e);let r=this.options.chain||[];for(let e=0,s=r.length,i,n;e<s;++e)(i=r[e]).type&&(n=m.types[i.type])&&t.push(new n(i))}add(e,t){this.emit({type:"addModifier",detail:t,modifier:e}),this.chain.push(e),this.emit({type:"addModifier",detail:t,modifier:e})}clear(e){this.emit({type:"clearChain",detail:e}),this.chain.length=0,this.emit({type:"afterClearChain",detail:e})}async modify(e,t){let r=this.options.reverse?this.chain.slice().reverse():this.chain.slice();e.modified===e&&(e.modified=e.clone(!1,t));let s=e;for(let i=0,n=r.length;i<n;++i){try{await r[i].modify(s,t)}catch(r){throw this.emit({type:"error",detail:t,table:e}),r}s=s.modified}return e.modified=s,e}modifyCell(e,t,r,s,i){let n=this.options.reverse?this.chain.reverse():this.chain;if(n.length){let o=e.clone();for(let e=0,l=n.length;e<l;++e)n[e].modifyCell(o,t,r,s,i),o=o.modified;e.modified=o}return e}modifyColumns(e,t,r,s){let i=this.options.reverse?this.chain.reverse():this.chain.slice();if(i.length){let n=e.clone();for(let e=0,o=i.length;e<o;++e)i[e].modifyColumns(n,t,r,s),n=n.modified;e.modified=n}return e}modifyRows(e,t,r,s){let i=this.options.reverse?this.chain.reverse():this.chain.slice();if(i.length){let n=e.clone();for(let e=0,o=i.length;e<o;++e)i[e].modifyRows(n,t,r,s),n=n.modified;e.modified=n}return e}modifyTable(e,t){this.emit({type:"modify",detail:t,table:e});let r=this.options.reverse?this.chain.reverse():this.chain.slice(),s=e.modified;for(let e=0,i=r.length;e<i;++e)s=r[e].modifyTable(s,t).modified;return e.modified=s,this.emit({type:"afterModify",detail:t,table:e}),e}remove(e,t){let r=this.chain;this.emit({type:"removeModifier",detail:t,modifier:e}),r.splice(r.indexOf(e),1),this.emit({type:"afterRemoveModifier",detail:t,modifier:e})}}tl.defaultOptions={type:"Chain"},m.registerType("Chain",tl);let{merge:ta}=l();class tu extends m{constructor(e){super(),this.options=ta(tu.defaultOptions,e)}modifyCell(e,t,r,s,i){let n=e.modified,o=n.getRowIndexBy("columnNames",t);return void 0===o?n.setColumns(this.modifyTable(e.clone()).getColumns(),void 0,i):n.setCell(`${r}`,o,s,i),e}modifyColumns(e,t,r,s){let i=e.modified,n=i.getColumn("columnNames")||[],o=e.getColumnNames(),l=e.getRowCount()!==n.length;if(!l){for(let e=0,t=o.length;e<t;++e)if(o[e]!==n[e]){l=!0;break}}if(l)return this.modifyTable(e,s);o=Object.keys(t);for(let e=0,n=o.length,l,a,u;e<n;++e){l=t[a=o[e]],u=i.getRowIndexBy("columnNames",a)||i.getRowCount();for(let e=0,t=r,n=l.length;e<n;++e,++t)i.setCell(`${t}`,u,l[e],s)}return e}modifyRows(e,t,r,s){let i=e.getColumnNames(),n=e.modified,o=n.getColumn("columnNames")||[],l=e.getRowCount()!==o.length;if(!l){for(let e=0,t=i.length;e<t;++e)if(i[e]!==o[e]){l=!0;break}}if(l)return this.modifyTable(e,s);for(let e=0,o=r,l=t.length,a;e<l;++e,++o)if((a=t[e])instanceof Array)n.setColumn(`${o}`,a);else for(let e=0,t=i.length;e<t;++e)n.setCell(`${o}`,e,a[i[e]],s);return e}modifyTable(e,t){this.emit({type:"modify",detail:t,table:e});let r=e.modified;if(e.hasColumns(["columnNames"])){let t=(e.deleteColumns(["columnNames"])||{}).columnNames||[],s={},i=[];for(let e=0,r=t.length;e<r;++e)i.push(""+t[e]);for(let t=0,r=e.getRowCount(),n;t<r;++t)(n=e.getRow(t))&&(s[i[t]]=n);r.deleteColumns(),r.setColumns(s)}else{let t={};for(let r=0,s=e.getRowCount(),i;r<s;++r)(i=e.getRow(r))&&(t[`${r}`]=i);t.columnNames=e.getColumnNames(),r.deleteColumns(),r.setColumns(t)}return this.emit({type:"afterModify",detail:t,table:e}),e}}tu.defaultOptions={type:"Invert"},m.registerType("Invert",tu);class th extends m{constructor(e){super(),this.options={...th.defaultOptions,...e}}modifyTable(e,t){this.emit({type:"modify",detail:t,table:e});let r=this.options.alternativeSeparators,s=this.options.formulaColumns||e.getColumnNames(),i=e.modified;for(let t=0,r=s.length,n;t<r;++t)n=s[t],s.indexOf(n)>=0&&i.setColumn(n,this.processColumn(e,n));let n=this.options.columnFormulas||[];for(let t=0,s=n.length,o,l;t<s;++t)o=n[t],l=es.parseFormula(o.formula,r),i.setColumn(o.column,this.processColumnFormula(l,e,o.rowStart,o.rowEnd));return this.emit({type:"afterModify",detail:t,table:e}),e}processColumn(e,t,r=0){let s=this.options.alternativeSeparators,i=(e.getColumn(t,!0)||[]).slice(r>0?r:0);for(let t=0,r=i.length,n=[],o;t<r;++t)if("string"==typeof(o=i[t])&&"="===o[0])try{n=""===o?n:es.parseFormula(o.substring(1),s),i[t]=eF.processFormula(n,e)}catch{i[t]=NaN}return i}processColumnFormula(e,t,r=0,s=t.getRowCount()){r=r>=0?r:0,s=s>=0?s:t.getRowCount()+s;let i=[],n=t.modified;for(let t=0,o=s-r;t<o;++t)try{i[t]=eF.processFormula(e,n)}catch{i[t]=NaN}finally{e=eF.translateReferences(e,0,1)}return i}}th.defaultOptions={type:"Math",alternativeSeparators:!1},m.registerType("Math",th);let{merge:tf}=l();class tm extends m{constructor(e){super(),this.options=tf(tm.defaultOptions,e)}modifyTable(e,t){this.emit({type:"modify",detail:t,table:e});let r=[],{additive:s,ranges:i,strict:n}=this.options;if(i.length){let t=e.modified,o=e.getColumns(),l=[];for(let e=0,a=i.length,u,h;e<a;++e)if(u=i[e],!n||typeof u.minValue==typeof u.maxValue){e>0&&!s&&(t.deleteRows(),t.setRows(l),t.setOriginalRowIndexes(r,!0),o=t.getColumns(),l=[],r=[]),h=o[u.column]||[];for(let e=0,t=h.length;e<t;++e)switch(typeof h[e]){default:continue;case"boolean":case"number":case"string":}}t.deleteRows(),t.setRows(l),t.setOriginalRowIndexes(r)}return this.emit({type:"afterModify",detail:t,table:e}),e}}tm.defaultOptions={type:"Range",ranges:[]},m.registerType("Range",tm);let{merge:tc}=l();class td extends m{static ascending(e,t){return(e||0)<(t||0)?-1:+((e||0)>(t||0))}static descending(e,t){return(t||0)<(e||0)?-1:+((t||0)>(e||0))}constructor(e){super(),this.options=tc(td.defaultOptions,e)}getRowReferences(e){let t=e.getRows(),r=[];for(let e=0,s=t.length;e<s;++e)r.push({index:e,row:t[e]});return r}modifyCell(e,t,r,s,i){let{orderByColumn:n,orderInColumn:o}=this.options;return t===n&&(o?(e.modified.setCell(t,r,s),e.modified.setColumn(o,this.modifyTable(new A({columns:e.getColumns([n,o])})).modified.getColumn(o))):this.modifyTable(e,i)),e}modifyColumns(e,t,r,s){let{orderByColumn:i,orderInColumn:n}=this.options,o=Object.keys(t);return o.indexOf(i)>-1&&(n&&t[o[0]].length?(e.modified.setColumns(t,r),e.modified.setColumn(n,this.modifyTable(new A({columns:e.getColumns([i,n])})).modified.getColumn(n))):this.modifyTable(e,s)),e}modifyRows(e,t,r,s){let{orderByColumn:i,orderInColumn:n}=this.options;return n&&t.length?(e.modified.setRows(t,r),e.modified.setColumn(n,this.modifyTable(new A({columns:e.getColumns([i,n])})).modified.getColumn(n))):this.modifyTable(e,s),e}modifyTable(e,t){this.emit({type:"modify",detail:t,table:e});let r=e.getColumnNames(),s=e.getRowCount(),i=this.getRowReferences(e),{direction:n,orderByColumn:o,orderInColumn:l}=this.options,a="asc"===n?td.ascending:td.descending,u=r.indexOf(o),h=e.modified;if(-1!==u&&i.sort((e,t)=>a(e.row[u],t.row[u])),l){let e=[];for(let t=0;t<s;++t)e[i[t].index]=t;h.setColumns({[l]:e})}else{let e,t=[],r=[];for(let n=0;n<s;++n)e=i[n],t.push(h.getOriginalRowIndex(e.index)),r.push(e.row);h.setRows(r,0),h.setOriginalRowIndexes(t)}return this.emit({type:"afterModify",detail:t,table:e}),e}}td.defaultOptions={type:"Sort",direction:"desc",orderByColumn:"y"},m.registerType("Sort",td);let tg=l();tg.DataConnector=tg.DataConnector||P,tg.DataConverter=tg.DataConverter||Y,tg.DataCursor=tg.DataCursor||$,tg.DataModifier=tg.DataModifier||m,tg.DataPool=tg.DataPool||H,tg.DataTable=tg.DataTable||A,tg.Formula=tg.Formula||eB;let tp=l();return n.default})());