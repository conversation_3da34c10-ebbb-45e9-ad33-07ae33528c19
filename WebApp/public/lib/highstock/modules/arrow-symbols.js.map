{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/arrow-symbols\n * @requires highcharts\n *\n * Arrow Symbols\n *\n * (c) 2017-2025 Lars A. V. Cabrera\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/arrow-symbols\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/arrow-symbols\"] = factory(root[\"_Highcharts\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ arrow_symbols_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Extensions/ArrowSymbols.js\n/* *\n *\n *  (c) 2017 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Creates an arrow symbol. Like a triangle, except not filled.\n * ```\n *                   o\n *             o\n *       o\n * o\n *       o\n *             o\n *                   o\n * ```\n *\n * @private\n * @function\n *\n * @param {number} x\n *        x position of the arrow\n *\n * @param {number} y\n *        y position of the arrow\n *\n * @param {number} w\n *        width of the arrow\n *\n * @param {number} h\n *        height of the arrow\n *\n * @return {Highcharts.SVGPathArray}\n *         Path array\n */\nfunction arrow(x, y, w, h) {\n    return [\n        ['M', x, y + h / 2],\n        ['L', x + w, y],\n        ['L', x, y + h / 2],\n        ['L', x + w, y + h]\n    ];\n}\n/**\n * Creates a half-width arrow symbol. Like a triangle, except not filled.\n * ```\n *       o\n *    o\n * o\n *    o\n *       o\n * ```\n *\n * @private\n * @function\n *\n * @param {number} x\n *        x position of the arrow\n *\n * @param {number} y\n *        y position of the arrow\n *\n * @param {number} w\n *        width of the arrow\n *\n * @param {number} h\n *        height of the arrow\n *\n * @return {Highcharts.SVGPathArray}\n *         Path array\n */\nfunction arrowHalf(x, y, w, h) {\n    return arrow(x, y, w / 2, h);\n}\n/**\n * @private\n */\nfunction compose(SVGRendererClass) {\n    const symbols = SVGRendererClass.prototype.symbols;\n    symbols.arrow = arrow;\n    symbols['arrow-filled'] = triangleLeft;\n    symbols['arrow-filled-half'] = triangleLeftHalf;\n    symbols['arrow-half'] = arrowHalf;\n    symbols['triangle-left'] = triangleLeft;\n    symbols['triangle-left-half'] = triangleLeftHalf;\n}\n/**\n * Creates a left-oriented triangle.\n * ```\n *             o\n *       ooooooo\n * ooooooooooooo\n *       ooooooo\n *             o\n * ```\n *\n * @private\n * @function\n *\n * @param {number} x\n *        x position of the triangle\n *\n * @param {number} y\n *        y position of the triangle\n *\n * @param {number} w\n *        width of the triangle\n *\n * @param {number} h\n *        height of the triangle\n *\n * @return {Highcharts.SVGPathArray}\n *         Path array\n */\nfunction triangleLeft(x, y, w, h) {\n    return [\n        ['M', x + w, y],\n        ['L', x, y + h / 2],\n        ['L', x + w, y + h],\n        ['Z']\n    ];\n}\n/**\n * Creates a half-width, left-oriented triangle.\n * ```\n *       o\n *    oooo\n * ooooooo\n *    oooo\n *       o\n * ```\n *\n * @private\n * @function\n *\n * @param {number} x\n *        x position of the triangle\n *\n * @param {number} y\n *        y position of the triangle\n *\n * @param {number} w\n *        width of the triangle\n *\n * @param {number} h\n *        height of the triangle\n *\n * @return {Highcharts.SVGPathArray}\n *         Path array\n */\nfunction triangleLeftHalf(x, y, w, h) {\n    return triangleLeft(x, y, w / 2, h);\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst ArrowSymbols = {\n    compose\n};\n/* harmony default export */ const Extensions_ArrowSymbols = (ArrowSymbols);\n\n;// ./code/es-modules/masters/modules/arrow-symbols.js\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nExtensions_ArrowSymbols.compose(G.SVGRenderer);\n/* harmony default export */ const arrow_symbols_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "arrow_symbols_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "arrow", "x", "y", "w", "h", "arrow<PERSON>alf", "triangleLeft", "triangleLeftHalf", "Extensions_ArrowSymbols", "compose", "SVGRendererClass", "symbols", "G", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "CAWA,AAXA;;;;;;;;;;CAUC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,EACrC,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,mCAAoC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAM,GACrG,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,mCAAmC,CAAGD,EAAQD,EAAK,WAAc,EAEzEA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CACjD,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,AAACC,GACnC,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIE,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaX,OAAO,CAG5B,IAAIC,EAASO,CAAwB,CAACE,EAAS,CAAG,CAGjDV,QAAS,CAAC,CACX,EAMA,OAHAO,CAAmB,CAACG,EAAS,CAACT,EAAQA,EAAOD,OAAO,CAAES,GAG/CR,EAAOD,OAAO,AACtB,CAMCS,EAAoBI,CAAC,CAAG,AAACZ,IACxB,IAAIa,EAASb,GAAUA,EAAOc,UAAU,CACvC,IAAOd,EAAO,OAAU,CACxB,IAAOA,EAER,OADAQ,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAAChB,EAASkB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACpB,EAASmB,IAC5EE,OAAOC,cAAc,CAACtB,EAASmB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAgDrH,SAASE,EAAMC,CAAC,CAAEC,CAAC,CAAEC,CAAC,CAAEC,CAAC,EACrB,MAAO,CACH,CAAC,IAAKH,EAAGC,EAAIE,EAAI,EAAE,CACnB,CAAC,IAAKH,EAAIE,EAAGD,EAAE,CACf,CAAC,IAAKD,EAAGC,EAAIE,EAAI,EAAE,CACnB,CAAC,IAAKH,EAAIE,EAAGD,EAAIE,EAAE,CACtB,AACL,CA6BA,SAASC,EAAUJ,CAAC,CAAEC,CAAC,CAAEC,CAAC,CAAEC,CAAC,EACzB,OAAOJ,EAAMC,EAAGC,EAAGC,EAAI,EAAGC,EAC9B,CAyCA,SAASE,EAAaL,CAAC,CAAEC,CAAC,CAAEC,CAAC,CAAEC,CAAC,EAC5B,MAAO,CACH,CAAC,IAAKH,EAAIE,EAAGD,EAAE,CACf,CAAC,IAAKD,EAAGC,EAAIE,EAAI,EAAE,CACnB,CAAC,IAAKH,EAAIE,EAAGD,EAAIE,EAAE,CACnB,CAAC,IAAI,CACR,AACL,CA6BA,SAASG,EAAiBN,CAAC,CAAEC,CAAC,CAAEC,CAAC,CAAEC,CAAC,EAChC,OAAOE,EAAaL,EAAGC,EAAGC,EAAI,EAAGC,EACrC,CAiBAI,AAXqB,CAAA,CACjBC,QAlFJ,SAAiBC,CAAgB,EAC7B,IAAMC,EAAUD,EAAiBjB,SAAS,CAACkB,OAAO,AAClDA,CAAAA,EAAQX,KAAK,CAAGA,EAChBW,CAAO,CAAC,eAAe,CAAGL,EAC1BK,CAAO,CAAC,oBAAoB,CAAGJ,EAC/BI,CAAO,CAAC,aAAa,CAAGN,EACxBM,CAAO,CAAC,gBAAgB,CAAGL,EAC3BK,CAAO,CAAC,qBAAqB,CAAGJ,CACpC,CA2EA,CAAA,EASwBE,OAAO,CAACG,AADrBb,IACuBc,WAAW,EAChB,IAAMhB,EAAsBE,IAG/C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}