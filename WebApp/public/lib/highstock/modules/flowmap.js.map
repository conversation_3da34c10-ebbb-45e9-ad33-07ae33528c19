{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/flowmap\n * @requires highcharts\n *\n * (c) 2009-2025\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/flowmap\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/flowmap\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ flowmap_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/FlowMap/FlowMapPoint.js\n/* *\n *\n *  (c) 2010-2025 Askel Eirik Johansson, Piotr Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { seriesTypes: { mapline: { prototype: { pointClass: MapLinePoint } } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { pick, isString, isNumber } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass FlowMapPoint extends MapLinePoint {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    isValid() {\n        let valid = !!(this.options.to && this.options.from);\n        [this.options.to, this.options.from]\n            .forEach(function (toOrFrom) {\n            valid = !!(valid && (toOrFrom && (isString(toOrFrom) || ( // Point id or has lat/lon coords\n            isNumber(pick(toOrFrom[0], toOrFrom.lat)) &&\n                isNumber(pick(toOrFrom[1], toOrFrom.lon))))));\n        });\n        return valid;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const FlowMap_FlowMapPoint = (FlowMapPoint);\n\n;// ./code/es-modules/Series/FlowMap/FlowMapSeries.js\n/* *\n *\n *  (c) 2010-2025 Askel Eirik Johansson, Piotr Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { series: { prototype: { pointClass: Point } }, seriesTypes: { column: ColumnSeries, map: MapSeries, mapline: MapLineSeries } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { addEvent, arrayMax, arrayMin, defined, extend, isArray, merge, pick: FlowMapSeries_pick, relativeLength } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/**\n * The flowmap series type\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.flowmap\n *\n * @augments Highcharts.Series\n */\nclass FlowMapSeries extends MapLineSeries {\n    /* *\n     *\n     *  Static Function\n     *\n     * */\n    /**\n     * Get vector length.\n     * @private\n     */\n    static getLength(x, y) {\n        return Math.sqrt(x * x + y * y);\n    }\n    /**\n     * Return a normalized vector.\n     * @private\n     */\n    static normalize(x, y) {\n        const length = this.getLength(x, y);\n        return [x / length, y / length];\n    }\n    /**\n     * Return an SVGPath for markerEnd.\n     * @private\n     */\n    static markerEndPath(lCorner, rCorner, topCorner, options) {\n        const width = relativeLength(options.width || 0, this.getLength(rCorner[0] - lCorner[0], rCorner[1] - lCorner[1]));\n        const type = options.markerType || 'arrow', [edgeX, edgeY] = this.normalize(rCorner[0] - lCorner[0], rCorner[1] - lCorner[1]);\n        const path = [];\n        // For arrow head calculation.\n        if (type === 'arrow') {\n            // Left side of arrow head.\n            let [x, y] = lCorner;\n            x -= edgeX * width;\n            y -= edgeY * width;\n            path.push(['L', x, y]);\n            // Tip of arrow head.\n            path.push(['L', topCorner[0], topCorner[1]]);\n            // Right side of arrow head.\n            [x, y] = rCorner;\n            x += edgeX * width;\n            y += edgeY * width;\n            path.push(['L', x, y]);\n        }\n        // For mushroom head calculation.\n        if (type === 'mushroom') {\n            let [xLeft, yLeft] = lCorner, [xRight, yRight] = rCorner;\n            const [xTop, yTop] = topCorner, xMid = (xRight - xLeft) / 2 + xLeft, yMid = (yRight - yLeft) / 2 + yLeft, \n            // Control point for curve.\n            xControl = (xTop - xMid) * 2 + xMid, yControl = (yTop - yMid) * 2 + yMid;\n            // Left side of arrow head.\n            xLeft -= edgeX * width;\n            yLeft -= edgeY * width;\n            path.push(['L', xLeft, yLeft]);\n            // Right side of arrow head.\n            xRight += edgeX * width;\n            yRight += edgeY * width;\n            // Curve from left to right.\n            path.push(['Q', xControl, yControl, xRight, yRight]);\n        }\n        return path;\n    }\n    /**\n     *\n     *  Functions\n     *\n     */\n    /**\n     * Animate the flowmap point one by one from 'fromPoint'.\n     *\n     * @private\n     * @function Highcharts.seriesTypes.flowmap#animate\n     *\n     * @param {boolean} init\n     *        Whether to initialize the animation or run it\n     */\n    animate(init) {\n        const series = this, points = series.points;\n        if (!init) { // Run the animation\n            points.forEach((point) => {\n                if (point.shapeArgs &&\n                    isArray(point.shapeArgs.d) &&\n                    point.shapeArgs.d.length) {\n                    const path = point.shapeArgs.d, x = path[0][1], y = path[0][2];\n                    // To animate SVG path the initial path array needs to be\n                    // same as target, but element should be visible, so we\n                    // insert array elements with start (M) values\n                    if (x && y) {\n                        const start = [];\n                        for (let i = 0; i < path.length; i++) {\n                            // Added any when merging master into another branch\n                            // :((. The spread looks correct, but TS complains\n                            // about possible number in the first position,\n                            // which is the segment type.\n                            start.push([...path[i]]);\n                            for (let j = 1; j < path[i].length; j++) {\n                                start[i][j] = j % 2 ? x : y;\n                            }\n                        }\n                        if (point.graphic) {\n                            point.graphic.attr({ d: start });\n                            point.graphic.animate({ d: path });\n                        }\n                    }\n                }\n            });\n        }\n    }\n    /**\n     * Get the actual width of a link either as a mapped weight between\n     * `minWidth` and `maxWidth` or a specified width.\n     * @private\n     */\n    getLinkWidth(point) {\n        const width = this.options.width, weight = point.options.weight || this.options.weight;\n        point.options.weight = weight;\n        if (width && !weight) {\n            return width;\n        }\n        const smallestWeight = this.smallestWeight, greatestWeight = this.greatestWeight;\n        if (!defined(weight) || !smallestWeight || !greatestWeight) {\n            return 0;\n        }\n        const minWidthLimit = this.options.minWidth, maxWidthLimit = this.options.maxWidth;\n        return (weight - smallestWeight) * (maxWidthLimit - minWidthLimit) /\n            ((greatestWeight - smallestWeight) || 1) + minWidthLimit;\n    }\n    /**\n     * Automatically calculate the optimal curve based on a reference point.\n     * @private\n     */\n    autoCurve(fromX, fromY, toX, toY, centerX, centerY) {\n        const linkV = {\n            x: (toX - fromX),\n            y: (toY - fromY)\n        }, half = {\n            x: (toX - fromX) / 2 + fromX,\n            y: (toY - fromY) / 2 + fromY\n        }, centerV = {\n            x: half.x - centerX,\n            y: half.y - centerY\n        };\n        // Dot product and determinant\n        const dot = linkV.x * centerV.x + linkV.y * centerV.y, det = linkV.x * centerV.y - linkV.y * centerV.x;\n        // Calculate the angle and base the curveFactor on it.\n        let angle = Math.atan2(det, dot), angleDeg = angle * 180 / Math.PI;\n        if (angleDeg < 0) {\n            angleDeg = 360 + angleDeg;\n        }\n        angle = angleDeg * Math.PI / 180;\n        // A more subtle result.\n        return -Math.sin(angle) * 0.7;\n    }\n    /**\n     * Get point attributes.\n     * @private\n     */\n    pointAttribs(point, state) {\n        const attrs = MapSeries.prototype.pointAttribs.call(this, point, state);\n        attrs.fill = FlowMapSeries_pick(point.options.fillColor, point.options.color, this.options.fillColor === 'none' ? null : this.options.fillColor, this.color);\n        attrs['fill-opacity'] = FlowMapSeries_pick(point.options.fillOpacity, this.options.fillOpacity);\n        attrs['stroke-width'] = FlowMapSeries_pick(point.options.lineWidth, this.options.lineWidth, 1);\n        if (point.options.opacity) {\n            attrs.opacity = point.options.opacity;\n        }\n        return attrs;\n    }\n    /**\n     * Draw shapeArgs based on from/to options. Run translation operations. We\n     * need two loops: first loop to calculate data, like smallest/greatest\n     * weights and centerOfPoints, which needs the calculated positions, second\n     * loop for calculating shapes of points based on previous calculations.\n     * @private\n     */\n    translate() {\n        if (this.chart.hasRendered && (this.isDirtyData || !this.hasRendered)) {\n            this.processData();\n            this.generatePoints();\n        }\n        const weights = [];\n        let averageX = 0, averageY = 0;\n        this.points.forEach((point) => {\n            const chart = this.chart, mapView = chart.mapView, options = point.options, dirtySeries = () => {\n                point.series.isDirty = true;\n            }, getPointXY = (pointId) => {\n                const foundPoint = chart.get(pointId);\n                // Connect to the linked parent point (in mappoint) to\n                // trigger series redraw for the linked point (in flow).\n                if ((foundPoint instanceof Point) &&\n                    foundPoint.plotX &&\n                    foundPoint.plotY) {\n                    // After linked point update flowmap point should\n                    // be also updated\n                    addEvent(foundPoint, 'update', dirtySeries);\n                    return {\n                        x: foundPoint.plotX,\n                        y: foundPoint.plotY\n                    };\n                }\n            }, getLonLatXY = (lonLat) => {\n                if (isArray(lonLat)) {\n                    return {\n                        lon: lonLat[0],\n                        lat: lonLat[1]\n                    };\n                }\n                return lonLat;\n            };\n            let fromPos, toPos;\n            if (typeof options.from === 'string') {\n                fromPos = getPointXY(options.from);\n            }\n            else if (typeof options.from === 'object' && mapView) {\n                fromPos = mapView.lonLatToPixels(getLonLatXY(options.from));\n            }\n            if (typeof options.to === 'string') {\n                toPos = getPointXY(options.to);\n            }\n            else if (typeof options.to === 'object' && mapView) {\n                toPos = mapView.lonLatToPixels(getLonLatXY(options.to));\n            }\n            // Save original point location.\n            point.fromPos = fromPos;\n            point.toPos = toPos;\n            if (fromPos && toPos) {\n                averageX += (fromPos.x + toPos.x) / 2;\n                averageY += (fromPos.y + toPos.y) / 2;\n            }\n            if (FlowMapSeries_pick(point.options.weight, this.options.weight)) {\n                weights.push(FlowMapSeries_pick(point.options.weight, this.options.weight));\n            }\n        });\n        this.smallestWeight = arrayMin(weights);\n        this.greatestWeight = arrayMax(weights);\n        this.centerOfPoints = {\n            x: averageX / this.points.length,\n            y: averageY / this.points.length\n        };\n        this.points.forEach((point) => {\n            // Don't draw point if weight is not valid.\n            if (!this.getLinkWidth(point)) {\n                point.shapeArgs = {\n                    d: []\n                };\n                return;\n            }\n            if (point.fromPos) {\n                point.plotX = point.fromPos.x;\n                point.plotY = point.fromPos.y;\n            }\n            // Calculate point shape\n            point.shapeType = 'path';\n            point.shapeArgs = this.getPointShapeArgs(point);\n            // When updating point from null to normal value, set a real color\n            // (don't keep nullColor).\n            point.color = FlowMapSeries_pick(point.options.color, point.series.color);\n        });\n    }\n    getPointShapeArgs(point) {\n        const { fromPos, toPos } = point;\n        if (!fromPos || !toPos) {\n            return {};\n        }\n        const finalWidth = this.getLinkWidth(point) / 2, pointOptions = point.options, markerEndOptions = merge(this.options.markerEnd, pointOptions.markerEnd), growTowards = FlowMapSeries_pick(pointOptions.growTowards, this.options.growTowards), fromX = fromPos.x || 0, fromY = fromPos.y || 0;\n        let toX = toPos.x || 0, toY = toPos.y || 0, curveFactor = FlowMapSeries_pick(pointOptions.curveFactor, this.options.curveFactor), offset = markerEndOptions && markerEndOptions.enabled &&\n            markerEndOptions.height || 0;\n        if (!defined(curveFactor)) { // Automate the curveFactor value.\n            curveFactor = this.autoCurve(fromX, fromY, toX, toY, this.centerOfPoints.x, this.centerOfPoints.y);\n        }\n        // An offset makes room for arrows if they are specified.\n        if (offset) {\n            // Prepare offset if it's a percentage by converting to number.\n            offset = relativeLength(offset, finalWidth * 4);\n            // Vector between the points.\n            let dX = toX - fromX, dY = toY - fromY;\n            // Vector is halved.\n            dX *= 0.5;\n            dY *= 0.5;\n            // Vector points exactly between the points.\n            const mX = fromX + dX, mY = fromY + dY;\n            // Rotating the halfway distance by 90 anti-clockwise.\n            // We can then use this to create an arc.\n            const tmp = dX;\n            dX = dY;\n            dY = -tmp;\n            // Calculate the arc strength.\n            const arcPointX = (mX + dX * curveFactor), arcPointY = (mY + dY * curveFactor);\n            let [offsetX, offsetY] = FlowMapSeries.normalize(arcPointX - toX, arcPointY - toY);\n            offsetX *= offset;\n            offsetY *= offset;\n            toX += offsetX;\n            toY += offsetY;\n        }\n        // Vector between the points.\n        let dX = toX - fromX, dY = toY - fromY;\n        // Vector is halved.\n        dX *= 0.5;\n        dY *= 0.5;\n        // Vector points exactly between the points.\n        const mX = fromX + dX, mY = fromY + dY;\n        // Rotating the halfway distance by 90 anti-clockwise.\n        // We can then use this to create an arc.\n        let tmp = dX;\n        dX = dY;\n        dY = -tmp;\n        // Weight vector calculation for the middle of the curve.\n        let [wX, wY] = FlowMapSeries.normalize(dX, dY);\n        // The `fineTune` prevents an obvious mismatch along the curve.\n        const fineTune = 1 + Math.sqrt(curveFactor * curveFactor) * 0.25;\n        wX *= finalWidth * fineTune;\n        wY *= finalWidth * fineTune;\n        // Calculate the arc strength.\n        const arcPointX = (mX + dX * curveFactor), arcPointY = (mY + dY * curveFactor);\n        // Calculate edge vectors in the from-point.\n        let [fromXToArc, fromYToArc] = FlowMapSeries.normalize(arcPointX - fromX, arcPointY - fromY);\n        tmp = fromXToArc;\n        fromXToArc = fromYToArc;\n        fromYToArc = -tmp;\n        fromXToArc *= finalWidth;\n        fromYToArc *= finalWidth;\n        // Calculate edge vectors in the to-point.\n        let [toXToArc, toYToArc] = FlowMapSeries.normalize(arcPointX - toX, arcPointY - toY);\n        tmp = toXToArc;\n        toXToArc = -toYToArc;\n        toYToArc = tmp;\n        toXToArc *= finalWidth;\n        toYToArc *= finalWidth;\n        // Shrink the starting edge and middle thickness to make it grow\n        // towards the end.\n        if (growTowards) {\n            fromXToArc /= finalWidth;\n            fromYToArc /= finalWidth;\n            wX /= 4;\n            wY /= 4;\n        }\n        const shapeArgs = {\n            d: [[\n                    'M',\n                    fromX - fromXToArc,\n                    fromY - fromYToArc\n                ], [\n                    'Q',\n                    arcPointX - wX,\n                    arcPointY - wY,\n                    toX - toXToArc,\n                    toY - toYToArc\n                ], [\n                    'L',\n                    toX + toXToArc,\n                    toY + toYToArc\n                ], [\n                    'Q',\n                    arcPointX + wX,\n                    arcPointY + wY,\n                    fromX + fromXToArc,\n                    fromY + fromYToArc\n                ], [\n                    'Z'\n                ]]\n        };\n        if (markerEndOptions && markerEndOptions.enabled && shapeArgs.d) {\n            const marker = FlowMapSeries.markerEndPath([toX - toXToArc, toY - toYToArc], [toX + toXToArc, toY + toYToArc], [toPos.x, toPos.y], markerEndOptions);\n            shapeArgs.d.splice(2, 0, ...marker);\n        }\n        // Objects converted to string to be used in tooltip.\n        const fromPoint = point.options.from, toPoint = point.options.to, fromLat = fromPoint.lat, fromLon = fromPoint.lon, toLat = toPoint.lat, toLon = toPoint.lon;\n        if (fromLat && fromLon) {\n            point.options.from = `${+fromLat}, ${+fromLon}`;\n        }\n        if (toLat && toLon) {\n            point.options.to = `${+toLat}, ${+toLon}`;\n        }\n        return shapeArgs;\n    }\n}\n/* *\n *\n *  Static properties\n *\n * */\n/**\n * A flowmap series is a series laid out on top of a map series allowing to\n * display route paths (e.g. flight or ship routes) or flows on a map. It\n * creates a link between two points on a map chart.\n *\n * @since 11.0.0\n * @extends      plotOptions.mapline\n * @excluding    affectsMapView, allAreas, allowPointSelect, boostBlending,\n * boostThreshold, borderColor, borderWidth, dashStyle, dataLabels,\n * dragDrop, joinBy, mapData, negativeColor, onPoint, shadow, showCheckbox\n * @product      highmaps\n * @requires     modules/flowmap\n * @optionparent plotOptions.flowmap\n */\nFlowMapSeries.defaultOptions = merge(MapLineSeries.defaultOptions, {\n    animation: true,\n    /**\n     * The `curveFactor` option for all links. Value higher than 0 will\n     * curve the link clockwise. A negative value will curve it counter\n     * clockwise. If the value is 0 the link will be a straight line. By\n     * default undefined curveFactor get an automatic curve.\n     *\n     * @sample {highmaps} maps/series-flowmap/curve-factor Setting different\n     *         values for curveFactor\n     *\n     * @type      {number}\n     * @default   undefined\n     * @apioption plotOptions.flowmap.curveFactor\n     */\n    dataLabels: {\n        enabled: false\n    },\n    /**\n     * The fill color of all the links. If not set, the series color will be\n     * used with the opacity set in\n     * [fillOpacity](#plotOptions.flowmap.fillOpacity).\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @apioption plotOptions.flowmap.fillColor\n     */\n    /**\n     * The opacity of the color fill for all links.\n     *\n     * @type   {number}\n     * @sample {highmaps} maps/series-flowmap/fill-opacity\n     *         Setting different values for fillOpacity\n     */\n    fillOpacity: 0.5,\n    /**\n     * The [id](#series.id) of another series to link to. Additionally, the\n     * value can be \":previous\" to link to the previous series. When two\n     * series are linked, only the first one appears in the legend. Toggling\n     * the visibility of this also toggles the linked series, which is\n     * necessary for operations such as zoom or updates on the flowmap\n     * series.\n     *\n     * @type      {string}\n     * @apioption plotOptions.flowmap.linkedTo\n     */\n    /**\n     * A `markerEnd` creates an arrow symbol indicating the direction of\n     * flow at the destination. Specifying a `markerEnd` here will create\n     * one for each link.\n     *\n     * @declare Highcharts.SeriesFlowMapSeriesOptionsObject\n     */\n    markerEnd: {\n        /**\n         * Enable or disable the `markerEnd`.\n         *\n         * @type   {boolean}\n         * @sample {highmaps} maps/series-flowmap/marker-end\n         *         Setting different markerType for markerEnd\n         */\n        enabled: true,\n        /**\n         * Height of the `markerEnd`. Can be a number in pixels or a\n         * percentage based on the weight of the link.\n         *\n         * @type  {number|string}\n         */\n        height: '40%',\n        /**\n         * Width of the `markerEnd`. Can be a number in pixels or a\n         * percentage based on the weight of the link.\n         *\n         * @type  {number|string}\n         */\n        width: '40%',\n        /**\n         * Change the shape of the `markerEnd`.\n         * Can be `arrow` or `mushroom`.\n         *\n         * @type {string}\n         */\n        markerType: 'arrow'\n    },\n    /**\n     * If no weight has previously been specified, this will set the width\n     * of all the links without being compared to and scaled according to\n     * other weights.\n     *\n     * @type  {number}\n     */\n    width: 1,\n    /**\n     * Maximum width of a link expressed in pixels. The weight of a link is\n     * mapped between `maxWidth` and `minWidth`.\n     *\n     * @type  {number}\n     */\n    maxWidth: 25,\n    /**\n     * Minimum width of a link expressed in pixels. The weight of a link is\n     * mapped between `maxWidth` and `minWidth`.\n     *\n     * @type  {number}\n     */\n    minWidth: 5,\n    /**\n     * Specify the `lineWidth` of the links if they are not specified.\n     *\n     * @type  {number}\n     */\n    lineWidth: void 0,\n    /**\n     * The opacity of all the links. Affects the opacity for the entire\n     * link, including stroke. See also\n     * [fillOpacity](#plotOptions.flowmap.fillOpacity), that affects the\n     * opacity of only the fill color.\n     *\n     * @apioption plotOptions.flowmap.opacity\n     */\n    /**\n     * The weight for all links with unspecified weights. The weight of a\n     * link determines its thickness compared to other links.\n     *\n     * @sample {highmaps} maps/series-flowmap/ship-route/ Example ship route\n     *\n     * @type      {number}\n     * @product   highmaps\n     * @apioption plotOptions.flowmap.weight\n     */\n    tooltip: {\n        /**\n         * The HTML for the flowmaps' route description in the tooltip. It\n         * consists of the `headerFormat` and `pointFormat`, which can be\n         * edited. Variables are enclosed by curly brackets. Available\n         * variables are `series.name`, `point.options.from`,\n         * `point.options.to`, `point.options.weight` and other properties in the\n         * same form.\n         *\n         * @product   highmaps\n         */\n        headerFormat: '<span style=\"font-size: 0.8em\">{series.name}</span><br/>',\n        pointFormat: '{point.options.from} \\u2192 {point.options.to}: <b>{point.options.weight}</b>'\n    }\n});\nextend(FlowMapSeries.prototype, {\n    pointClass: FlowMap_FlowMapPoint,\n    pointArrayMap: ['from', 'to', 'weight'],\n    drawPoints: ColumnSeries.prototype.drawPoints,\n    dataColumnKeys: ColumnSeries.prototype.dataColumnKeys,\n    // Make it work on zoom or pan.\n    useMapGeometry: true\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('flowmap', FlowMapSeries);\n/* *\n *\n *  Default export\n *\n * */\n/* harmony default export */ const FlowMap_FlowMapSeries = ((/* unused pure expression or super */ null && (FlowMapSeries)));\n/* *\n *\n *  API options\n *\n * */\n/**\n * A `flowmap` series. If the [type](#series.flowmap.type) option\n * is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.flowmap\n * @excluding affectsMapView, allAreas, allowPointSelect, boostBlending,\n * boostThreshold, borderColor, borderWidth, dashStyle, dataLabels, dragDrop,\n * joinBy, mapData, negativeColor, onPoint, shadow, showCheckbox\n * @product   highmaps\n * @apioption series.flowmap\n */\n/**\n * An array of data points for the series. For the `flowmap` series\n * type, points can be given in the following ways:\n *\n * 1.  An array of arrays with options as values. In this case,\n *     the values correspond to `from, to, weight`. Example:\n *     ```js\n *     data: [\n *         ['Point 1', 'Point 2', 4]\n *     ]\n *     ```\n *\n * 2.  An array of objects with named values. The following snippet shows only a\n *     few settings, see the complete options set below.\n *\n *     ```js\n *     data: [{\n *         from: 'Point 1',\n *         to: 'Point 2',\n *         curveFactor: 0.4,\n *         weight: 5,\n *         growTowards: true,\n *         markerEnd: {\n *             enabled: true,\n *             height: 15,\n *             width: 8\n *         }\n *     }]\n *     ```\n *\n * 3.   For objects with named values, instead of using the `mappoint` `id`,\n *      you can use `[longitude, latitude]` arrays.\n *\n *      ```js\n *      data: [{\n *          from: [longitude, latitude],\n *          to: [longitude, latitude]\n *      }]\n *      ```\n *\n * @type      {Array<number|null|*>}\n * @apioption series.flowmap.data\n */\n/**\n * A `curveFactor` with a higher value than 0 will curve the link clockwise.\n * A negative value will curve the link counter clockwise.\n * If the value is 0 the link will be straight.\n *\n * @sample {highmaps} maps/series-flowmap/ship-route/\n *         Example ship route\n *\n * @type      {number}\n * @apioption series.flowmap.data.curveFactor\n */\n/**\n * The fill color of an individual link.\n *\n * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n * @apioption series.flowmap.data.fillColor\n */\n/**\n * ID referencing a map point holding coordinates of the link origin or\n * coordinates in terms of array of `[longitude, latitude]` or object with `lon`\n * and `lat` properties.\n *\n * @sample {highmaps} maps/series-flowmap/from-to-lon-lat\n *         Flowmap point using lonlat coordinates\n * @sample {highmaps} maps/series-flowmap/flight-routes\n *         Highmaps basic flight routes demo\n *\n * @type      {string|Highcharts.LonLatArray|Highcharts.MapLonLatObject}\n * @apioption series.flowmap.data.from\n */\n/**\n * ID referencing a map point holding coordinates of the link origin or\n * coordinates in terms of array of `[longitude, latitude]` or object with `lon`\n * and `lat` properties.\n *\n * @sample {highmaps} maps/series-flowmap/from-to-lon-lat\n *         Flowmap point using lonlat coordinates\n * @sample {highmaps} maps/series-flowmap/flight-routes\n *         Highmaps basic flight routes demo\n *\n * @type      {string|Highcharts.LonLatArray|Highcharts.MapLonLatObject}\n * @apioption series.flowmap.data.to\n */\n/**\n * The opacity of the link color fill.\n *\n * @type      {number}\n * @apioption series.flowmap.data.fillOpacity\n */\n/**\n * If set to `true`, the line will grow towards its end.\n *\n * @sample {highmaps} maps/series-flowmap/ship-route/\n *         Example ship route\n *\n * @type      {boolean}\n * @apioption series.flowmap.data.growTowards\n */\n/**\n * Specifying a `markerEnd` here will create an arrow symbol\n * indicating the direction of flow at the destination of one individual link.\n * If one has been previously specified at the higher level option it will be\n * overridden for the current link.\n *\n * @sample {highmaps} maps/series-flowmap/ship-route/\n *         Example ship route\n *\n * @type      {*|null}\n * @apioption series.flowmap.data.markerEnd\n */\n/**\n * Enable or disable the `markerEnd`.\n *\n * @type      {boolean}\n * @apioption series.flowmap.data.markerEnd.enabled\n */\n/**\n * Height of the `markerEnd`. Can be a number in pixels\n * or a percentage based on the weight of the link.\n *\n * @type      {number|string}\n * @apioption series.flowmap.data.markerEnd.height\n */\n/**\n * Width of the `markerEnd`. Can be a number in pixels\n * or a percentage based on the weight of the link.\n *\n * @type      {number|string}\n * @apioption series.flowmap.data.markerEnd.width\n */\n/**\n * Change the shape of the `markerEnd`. Can be `arrow` or `mushroom`.\n *\n * @type      {string}\n * @apioption series.flowmap.data.markerEnd.markerType\n */\n/**\n * The opacity of an individual link.\n *\n * @type      {number}\n * @apioption series.flowmap.data.opacity\n */\n/**\n * The weight of a link determines its thickness compared to\n * other links.\n *\n * @sample {highmaps} maps/series-flowmap/ship-route/\n *         Example ship route\n *\n * @type      {number}\n * @apioption series.flowmap.data.weight\n */\n/**\n * Specify the `lineWidth` of the link.\n *\n * @type  {number}\n * @apioption series.flowmap.data.lineWidth\n */\n''; // Adds doclets above to transpiled file\n\n;// ./code/es-modules/masters/modules/flowmap.js\n\n\n\n\n/* harmony default export */ const flowmap_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "flowmap_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "seriesTypes", "mapline", "pointClass", "MapLinePoint", "pick", "isString", "isNumber", "FlowMap_FlowMapPoint", "<PERSON><PERSON><PERSON><PERSON>", "valid", "options", "to", "from", "for<PERSON>ach", "toOrFrom", "lat", "lon", "series", "Point", "column", "ColumnSeries", "map", "MapSeries", "MapLineSeries", "addEvent", "arrayMax", "arrayMin", "defined", "extend", "isArray", "merge", "FlowMapSeries_pick", "<PERSON><PERSON><PERSON><PERSON>", "FlowMapSeries", "<PERSON><PERSON><PERSON><PERSON>", "x", "y", "Math", "sqrt", "normalize", "length", "marker<PERSON>ndPath", "<PERSON><PERSON><PERSON><PERSON>", "r<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "width", "type", "markerType", "edgeX", "edgeY", "path", "push", "xLeft", "yLeft", "xRight", "yRight", "xTop", "yTop", "xMid", "yMid", "animate", "init", "points", "point", "shapeArgs", "start", "i", "j", "graphic", "attr", "getLinkWidth", "weight", "smallestWeight", "greatestWeight", "minWidthLimit", "min<PERSON><PERSON><PERSON>", "maxWidthLimit", "max<PERSON><PERSON><PERSON>", "autoCurve", "fromX", "fromY", "toX", "toY", "centerX", "centerY", "linkV", "half", "centerV", "dot", "angle", "atan2", "angleDeg", "PI", "sin", "pointAttribs", "state", "attrs", "fill", "fillColor", "color", "fillOpacity", "lineWidth", "opacity", "translate", "chart", "hasRendered", "isDirtyData", "processData", "generatePoints", "weights", "averageX", "averageY", "fromPos", "toPos", "mapView", "dirtySeries", "isDirty", "getPointXY", "pointId", "foundPoint", "plotX", "plotY", "getLonLatXY", "lonLat", "lonLatToPixels", "centerOfPoints", "shapeType", "getPointShapeArgs", "finalWidth", "pointOptions", "markerEndOptions", "markerEnd", "growTowards", "curveFactor", "offset", "enabled", "height", "dX", "dY", "mX", "mY", "tmp", "arcPointX", "arcPointY", "offsetX", "offsetY", "wX", "wY", "fineTune", "fromXToArc", "fromYToArc", "toXToArc", "toYToArc", "marker", "splice", "fromPoint", "toPoint", "fromLat", "fromLon", "toLat", "toLon", "defaultOptions", "animation", "dataLabels", "tooltip", "headerFormat", "pointFormat", "pointArrayMap", "drawPoints", "dataColumnKeys", "useMapGeometry", "registerSeriesType"], "mappings": "CASA,AATA;;;;;;;;CAQC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,6BAA8B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACtH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,6BAA6B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAE1GA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAajL,GAAM,CAAEE,YAAa,CAAEC,QAAS,CAAEV,UAAW,CAAEW,WAAYC,CAAY,CAAE,CAAE,CAAE,CAAE,CAAIJ,IAE7E,CAAEK,KAAAA,CAAI,CAAEC,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAE,CAAIT,IA+BHU,EAzBnC,cAA2BJ,EASvBK,SAAU,CACN,IAAIC,EAAQ,CAAC,CAAE,CAAA,IAAI,CAACC,OAAO,CAACC,EAAE,EAAI,IAAI,CAACD,OAAO,CAACE,IAAI,AAAD,EAOlD,MANA,CAAC,IAAI,CAACF,OAAO,CAACC,EAAE,CAAE,IAAI,CAACD,OAAO,CAACE,IAAI,CAAC,CAC/BC,OAAO,CAAC,SAAUC,CAAQ,EAC3BL,EAAQ,CAAC,CAAEA,CAAAA,GAAUK,GAAaT,CAAAA,EAASS,IAC3CR,EAASF,EAAKU,CAAQ,CAAC,EAAE,CAAEA,EAASC,GAAG,IACnCT,EAASF,EAAKU,CAAQ,CAAC,EAAE,CAAEA,EAASE,GAAG,EAAE,CAAE,CACnD,GACOP,CACX,CACJ,EAqBM,CAAEQ,OAAQ,CAAE1B,UAAW,CAAEW,WAAYgB,CAAK,CAAE,CAAE,CAAElB,YAAa,CAAEmB,OAAQC,CAAY,CAAEC,IAAKC,CAAS,CAAErB,QAASsB,CAAa,CAAE,CAAE,CAAIxB,IAEnI,CAAEyB,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAEC,QAAAA,CAAO,CAAEC,OAAAA,CAAM,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAE1B,KAAM2B,CAAkB,CAAEC,eAAAA,CAAc,CAAE,CAAInC,GAUrH,OAAMoC,UAAsBV,EAUxB,OAAOW,UAAUC,CAAC,CAAEC,CAAC,CAAE,CACnB,OAAOC,KAAKC,IAAI,CAACH,EAAIA,EAAIC,EAAIA,EACjC,CAKA,OAAOG,UAAUJ,CAAC,CAAEC,CAAC,CAAE,CACnB,IAAMI,EAAS,IAAI,CAACN,SAAS,CAACC,EAAGC,GACjC,MAAO,CAACD,EAAIK,EAAQJ,EAAII,EAAO,AACnC,CAKA,OAAOC,cAAcC,CAAO,CAAEC,CAAO,CAAEC,CAAS,CAAElC,CAAO,CAAE,CACvD,IAAMmC,EAAQb,EAAetB,EAAQmC,KAAK,EAAI,EAAG,IAAI,CAACX,SAAS,CAACS,CAAO,CAAC,EAAE,CAAGD,CAAO,CAAC,EAAE,CAAEC,CAAO,CAAC,EAAE,CAAGD,CAAO,CAAC,EAAE,GAC1GI,EAAOpC,EAAQqC,UAAU,EAAI,QAAS,CAACC,EAAOC,EAAM,CAAG,IAAI,CAACV,SAAS,CAACI,CAAO,CAAC,EAAE,CAAGD,CAAO,CAAC,EAAE,CAAEC,CAAO,CAAC,EAAE,CAAGD,CAAO,CAAC,EAAE,EACtHQ,EAAO,EAAE,CAEf,GAAIJ,AAAS,UAATA,EAAkB,CAElB,GAAI,CAACX,EAAGC,EAAE,CAAGM,EACbP,GAAKa,EAAQH,EACbT,GAAKa,EAAQJ,EACbK,EAAKC,IAAI,CAAC,CAAC,IAAKhB,EAAGC,EAAE,EAErBc,EAAKC,IAAI,CAAC,CAAC,IAAKP,CAAS,CAAC,EAAE,CAAEA,CAAS,CAAC,EAAE,CAAC,EAE3C,CAACT,EAAGC,EAAE,CAAGO,EACTR,GAAKa,EAAQH,EACbT,GAAKa,EAAQJ,EACbK,EAAKC,IAAI,CAAC,CAAC,IAAKhB,EAAGC,EAAE,CACzB,CAEA,GAAIU,AAAS,aAATA,EAAqB,CACrB,GAAI,CAACM,EAAOC,EAAM,CAAGX,EAAS,CAACY,EAAQC,EAAO,CAAGZ,EAC3C,CAACa,EAAMC,EAAK,CAAGb,EAAWc,EAAO,AAACJ,CAAAA,EAASF,CAAI,EAAK,EAAIA,EAAOO,EAAO,AAACJ,CAAAA,EAASF,CAAI,EAAK,EAAIA,EAInGD,GAASJ,EAAQH,EACjBQ,GAASJ,EAAQJ,EACjBK,EAAKC,IAAI,CAAC,CAAC,IAAKC,EAAOC,EAAM,EAE7BC,GAAUN,EAAQH,EAClBU,GAAUN,EAAQJ,EAElBK,EAAKC,IAAI,CAAC,CAAC,IATA,AAACK,CAAAA,EAAOE,CAAG,EAAK,EAAIA,EAAiB,AAACD,CAAAA,EAAOE,CAAG,EAAK,EAAIA,EAShCL,EAAQC,EAAO,CACvD,CACA,OAAOL,CACX,CAeAU,QAAQC,CAAI,CAAE,CACV,IAAqBC,EAAS7C,AAAf,IAAI,CAAkB6C,MAAM,AACvC,CAACD,GACDC,EAAOjD,OAAO,CAAC,AAACkD,IACZ,GAAIA,EAAMC,SAAS,EACfnC,EAAQkC,EAAMC,SAAS,CAACpF,CAAC,GACzBmF,EAAMC,SAAS,CAACpF,CAAC,CAAC4D,MAAM,CAAE,CAC1B,IAAMU,EAAOa,EAAMC,SAAS,CAACpF,CAAC,CAAEuD,EAAIe,CAAI,CAAC,EAAE,CAAC,EAAE,CAAEd,EAAIc,CAAI,CAAC,EAAE,CAAC,EAAE,CAI9D,GAAIf,GAAKC,EAAG,CACR,IAAM6B,EAAQ,EAAE,CAChB,IAAK,IAAIC,EAAI,EAAGA,EAAIhB,EAAKV,MAAM,CAAE0B,IAAK,CAKlCD,EAAMd,IAAI,CAAC,IAAID,CAAI,CAACgB,EAAE,CAAC,EACvB,IAAK,IAAIC,EAAI,EAAGA,EAAIjB,CAAI,CAACgB,EAAE,CAAC1B,MAAM,CAAE2B,IAChCF,CAAK,CAACC,EAAE,CAACC,EAAE,CAAGA,EAAI,EAAIhC,EAAIC,CAElC,CACI2B,EAAMK,OAAO,GACbL,EAAMK,OAAO,CAACC,IAAI,CAAC,CAAEzF,EAAGqF,CAAM,GAC9BF,EAAMK,OAAO,CAACR,OAAO,CAAC,CAAEhF,EAAGsE,CAAK,GAExC,CACJ,CACJ,EAER,CAMAoB,aAAaP,CAAK,CAAE,CAChB,IAAMlB,EAAQ,IAAI,CAACnC,OAAO,CAACmC,KAAK,CAAE0B,EAASR,EAAMrD,OAAO,CAAC6D,MAAM,EAAI,IAAI,CAAC7D,OAAO,CAAC6D,MAAM,CAEtF,GADAR,EAAMrD,OAAO,CAAC6D,MAAM,CAAGA,EACnB1B,GAAS,CAAC0B,EACV,OAAO1B,EAEX,IAAM2B,EAAiB,IAAI,CAACA,cAAc,CAAEC,EAAiB,IAAI,CAACA,cAAc,CAChF,GAAI,CAAC9C,EAAQ4C,IAAW,CAACC,GAAkB,CAACC,EACxC,OAAO,EAEX,IAAMC,EAAgB,IAAI,CAAChE,OAAO,CAACiE,QAAQ,CAC3C,MAAO,AAACJ,CAAAA,EAASC,CAAa,EAAMI,CAAAA,AADyB,IAAI,CAAClE,OAAO,CAACmE,QAAQ,CAC9BH,CAAY,EAC3D,CAAA,AAACD,EAAiBD,GAAmB,CAAA,EAAKE,CACnD,CAKAI,UAAUC,CAAK,CAAEC,CAAK,CAAEC,CAAG,CAAEC,CAAG,CAAEC,CAAO,CAAEC,CAAO,CAAE,CAChD,IAAMC,EAAQ,CACVlD,EAAI8C,EAAMF,EACV3C,EAAI8C,EAAMF,CACd,EAAGM,EAAO,CACNnD,EAAG,AAAC8C,CAAAA,EAAMF,CAAI,EAAK,EAAIA,EACvB3C,EAAG,AAAC8C,CAAAA,EAAMF,CAAI,EAAK,EAAIA,CAC3B,EAAGO,EAAU,CACTpD,EAAGmD,EAAKnD,CAAC,CAAGgD,EACZ/C,EAAGkD,EAAKlD,CAAC,CAAGgD,CAChB,EAEMI,EAAMH,EAAMlD,CAAC,CAAGoD,EAAQpD,CAAC,CAAGkD,EAAMjD,CAAC,CAAGmD,EAAQnD,CAAC,CAEjDqD,EAAQpD,KAAKqD,KAAK,CAFuCL,EAAMlD,CAAC,CAAGoD,EAAQnD,CAAC,CAAGiD,EAAMjD,CAAC,CAAGmD,EAAQpD,CAAC,CAE1EqD,GAAMG,EAAWF,AAAQ,IAARA,EAAcpD,KAAKuD,EAAE,CAMlE,OALID,EAAW,GACXA,CAAAA,EAAW,IAAMA,CAAO,EAIrB,CAAA,CAAA,AAAmB,GAAlBtD,KAAKwD,GAAG,CAFhBJ,EAAQE,EAAWtD,KAAKuD,EAAE,CAAG,IAED,CAChC,CAKAE,aAAa/B,CAAK,CAAEgC,CAAK,CAAE,CACvB,IAAMC,EAAQ1E,EAAU/B,SAAS,CAACuG,YAAY,CAACrG,IAAI,CAAC,IAAI,CAAEsE,EAAOgC,GAOjE,OANAC,EAAMC,IAAI,CAAGlE,EAAmBgC,EAAMrD,OAAO,CAACwF,SAAS,CAAEnC,EAAMrD,OAAO,CAACyF,KAAK,CAAE,AAA2B,SAA3B,IAAI,CAACzF,OAAO,CAACwF,SAAS,CAAc,KAAO,IAAI,CAACxF,OAAO,CAACwF,SAAS,CAAE,IAAI,CAACC,KAAK,EAC3JH,CAAK,CAAC,eAAe,CAAGjE,EAAmBgC,EAAMrD,OAAO,CAAC0F,WAAW,CAAE,IAAI,CAAC1F,OAAO,CAAC0F,WAAW,EAC9FJ,CAAK,CAAC,eAAe,CAAGjE,EAAmBgC,EAAMrD,OAAO,CAAC2F,SAAS,CAAE,IAAI,CAAC3F,OAAO,CAAC2F,SAAS,CAAE,GACxFtC,EAAMrD,OAAO,CAAC4F,OAAO,EACrBN,CAAAA,EAAMM,OAAO,CAAGvC,EAAMrD,OAAO,CAAC4F,OAAO,AAAD,EAEjCN,CACX,CAQAO,WAAY,CACJ,IAAI,CAACC,KAAK,CAACC,WAAW,EAAK,CAAA,IAAI,CAACC,WAAW,EAAI,CAAC,IAAI,CAACD,WAAW,AAAD,IAC/D,IAAI,CAACE,WAAW,GAChB,IAAI,CAACC,cAAc,IAEvB,IAAMC,EAAU,EAAE,CACdC,EAAW,EAAGC,EAAW,EAC7B,IAAI,CAACjD,MAAM,CAACjD,OAAO,CAAC,AAACkD,IACjB,IA0BIiD,EAASC,EA1BPT,EAAQ,IAAI,CAACA,KAAK,CAAEU,EAAUV,EAAMU,OAAO,CAAExG,EAAUqD,EAAMrD,OAAO,CAAEyG,EAAc,KACtFpD,EAAM9C,MAAM,CAACmG,OAAO,CAAG,CAAA,CAC3B,EAAGC,EAAa,AAACC,IACb,IAAMC,EAAaf,EAAMpH,GAAG,CAACkI,GAG7B,GAAI,AAACC,aAAsBrG,GACvBqG,EAAWC,KAAK,EAChBD,EAAWE,KAAK,CAIhB,OADAjG,EAAS+F,EAAY,SAAUJ,GACxB,CACHhF,EAAGoF,EAAWC,KAAK,CACnBpF,EAAGmF,EAAWE,KAAK,AACvB,CAER,EAAGC,EAAc,AAACC,GACd,AAAI9F,EAAQ8F,GACD,CACH3G,IAAK2G,CAAM,CAAC,EAAE,CACd5G,IAAK4G,CAAM,CAAC,EAAE,AAClB,EAEGA,CAGP,AAAwB,CAAA,UAAxB,OAAOjH,EAAQE,IAAI,CACnBoG,EAAUK,EAAW3G,EAAQE,IAAI,EAE5B,AAAwB,UAAxB,OAAOF,EAAQE,IAAI,EAAiBsG,GACzCF,CAAAA,EAAUE,EAAQU,cAAc,CAACF,EAAYhH,EAAQE,IAAI,EAAC,EAE1D,AAAsB,UAAtB,OAAOF,EAAQC,EAAE,CACjBsG,EAAQI,EAAW3G,EAAQC,EAAE,EAExB,AAAsB,UAAtB,OAAOD,EAAQC,EAAE,EAAiBuG,GACvCD,CAAAA,EAAQC,EAAQU,cAAc,CAACF,EAAYhH,EAAQC,EAAE,EAAC,EAG1DoD,EAAMiD,OAAO,CAAGA,EAChBjD,EAAMkD,KAAK,CAAGA,EACVD,GAAWC,IACXH,GAAY,AAACE,CAAAA,EAAQ7E,CAAC,CAAG8E,EAAM9E,CAAC,AAADA,EAAK,EACpC4E,GAAY,AAACC,CAAAA,EAAQ5E,CAAC,CAAG6E,EAAM7E,CAAC,AAADA,EAAK,GAEpCL,EAAmBgC,EAAMrD,OAAO,CAAC6D,MAAM,CAAE,IAAI,CAAC7D,OAAO,CAAC6D,MAAM,GAC5DsC,EAAQ1D,IAAI,CAACpB,EAAmBgC,EAAMrD,OAAO,CAAC6D,MAAM,CAAE,IAAI,CAAC7D,OAAO,CAAC6D,MAAM,EAEjF,GACA,IAAI,CAACC,cAAc,CAAG9C,EAASmF,GAC/B,IAAI,CAACpC,cAAc,CAAGhD,EAASoF,GAC/B,IAAI,CAACgB,cAAc,CAAG,CAClB1F,EAAG2E,EAAW,IAAI,CAAChD,MAAM,CAACtB,MAAM,CAChCJ,EAAG2E,EAAW,IAAI,CAACjD,MAAM,CAACtB,MAAM,AACpC,EACA,IAAI,CAACsB,MAAM,CAACjD,OAAO,CAAC,AAACkD,IAEjB,GAAI,CAAC,IAAI,CAACO,YAAY,CAACP,GAAQ,CAC3BA,EAAMC,SAAS,CAAG,CACdpF,EAAG,EAAE,AACT,EACA,MACJ,CACImF,EAAMiD,OAAO,GACbjD,EAAMyD,KAAK,CAAGzD,EAAMiD,OAAO,CAAC7E,CAAC,CAC7B4B,EAAM0D,KAAK,CAAG1D,EAAMiD,OAAO,CAAC5E,CAAC,EAGjC2B,EAAM+D,SAAS,CAAG,OAClB/D,EAAMC,SAAS,CAAG,IAAI,CAAC+D,iBAAiB,CAAChE,GAGzCA,EAAMoC,KAAK,CAAGpE,EAAmBgC,EAAMrD,OAAO,CAACyF,KAAK,CAAEpC,EAAM9C,MAAM,CAACkF,KAAK,CAC5E,EACJ,CACA4B,kBAAkBhE,CAAK,CAAE,CACrB,GAAM,CAAEiD,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAE,CAAGlD,EAC3B,GAAI,CAACiD,GAAW,CAACC,EACb,MAAO,CAAC,EAEZ,IAAMe,EAAa,IAAI,CAAC1D,YAAY,CAACP,GAAS,EAAGkE,EAAelE,EAAMrD,OAAO,CAAEwH,EAAmBpG,EAAM,IAAI,CAACpB,OAAO,CAACyH,SAAS,CAAEF,EAAaE,SAAS,EAAGC,EAAcrG,EAAmBkG,EAAaG,WAAW,CAAE,IAAI,CAAC1H,OAAO,CAAC0H,WAAW,EAAGrD,EAAQiC,EAAQ7E,CAAC,EAAI,EAAG6C,EAAQgC,EAAQ5E,CAAC,EAAI,EACxR6C,EAAMgC,EAAM9E,CAAC,EAAI,EAAG+C,EAAM+B,EAAM7E,CAAC,EAAI,EAAGiG,EAActG,EAAmBkG,EAAaI,WAAW,CAAE,IAAI,CAAC3H,OAAO,CAAC2H,WAAW,EAAGC,EAASJ,GAAoBA,EAAiBK,OAAO,EACnLL,EAAiBM,MAAM,EAAI,EAK/B,GAJI,AAAC7G,EAAQ0G,IACTA,CAAAA,EAAc,IAAI,CAACvD,SAAS,CAACC,EAAOC,EAAOC,EAAKC,EAAK,IAAI,CAAC2C,cAAc,CAAC1F,CAAC,CAAE,IAAI,CAAC0F,cAAc,CAACzF,CAAC,CAAA,EAGjGkG,EAAQ,CAERA,EAAStG,EAAesG,EAAQN,AAAa,EAAbA,GAEhC,IAAIS,EAAKxD,EAAMF,EAAO2D,EAAKxD,EAAMF,EAK3B2D,EAAK5D,EAHX0D,CAAAA,GAAM,EAAE,EAGeG,EAAK5D,EAF5B0D,CAAAA,GAAM,EAAE,EAKFG,EAAMJ,EAINK,EAAaH,EAAKF,AAHxBA,CAAAA,EAAKC,CAAC,EAGuBL,EAAcU,EAAaH,EAAKF,AAF7DA,CAAAA,EAAK,CAACG,CAAE,EAE0DR,EAC9D,CAACW,EAASC,EAAQ,CAAGhH,EAAcM,SAAS,CAACuG,EAAY7D,EAAK8D,EAAY7D,GAC9E8D,GAAWV,EACXW,GAAWX,EACXrD,GAAO+D,EACP9D,GAAO+D,CACX,CAEA,IAAIR,EAAKxD,EAAMF,EAAO2D,EAAKxD,EAAMF,EAK3B2D,EAAK5D,EAHX0D,CAAAA,GAAM,EAAE,EAGeG,EAAK5D,EAF5B0D,CAAAA,GAAM,EAAE,EAKJG,EAAMJ,EACVA,EAAKC,EACLA,EAAK,CAACG,EAEN,GAAI,CAACK,EAAIC,EAAG,CAAGlH,EAAcM,SAAS,CAACkG,EAAIC,GAErCU,EAAW,EAAI/G,AAAuC,IAAvCA,KAAKC,IAAI,CAAC+F,EAAcA,GAC7Ca,GAAMlB,EAAaoB,EACnBD,GAAMnB,EAAaoB,EAEnB,IAAMN,EAAaH,EAAKF,EAAKJ,EAAcU,EAAaH,EAAKF,EAAKL,EAE9D,CAACgB,EAAYC,EAAW,CAAGrH,EAAcM,SAAS,CAACuG,EAAY/D,EAAOgE,EAAY/D,GACtF6D,EAAMQ,EACNA,EAAaC,EACbA,EAAa,CAACT,EACdQ,GAAcrB,EACdsB,GAActB,EAEd,GAAI,CAACuB,EAAUC,EAAS,CAAGvH,EAAcM,SAAS,CAACuG,EAAY7D,EAAK8D,EAAY7D,GAChF2D,EAAMU,EACNA,EAAW,CAACC,EACZA,EAAWX,EACXU,GAAYvB,EACZwB,GAAYxB,EAGRI,IACAiB,GAAcrB,EACdsB,GAActB,EACdkB,GAAM,EACNC,GAAM,GAEV,IAAMnF,EAAY,CACdpF,EAAG,CAAC,CACI,IACAmG,EAAQsE,EACRrE,EAAQsE,EACX,CAAE,CACC,IACAR,EAAYI,EACZH,EAAYI,EACZlE,EAAMsE,EACNrE,EAAMsE,EACT,CAAE,CACC,IACAvE,EAAMsE,EACNrE,EAAMsE,EACT,CAAE,CACC,IACAV,EAAYI,EACZH,EAAYI,EACZpE,EAAQsE,EACRrE,EAAQsE,EACX,CAAE,CACC,IACH,CAAC,AACV,EACA,GAAIpB,GAAoBA,EAAiBK,OAAO,EAAIvE,EAAUpF,CAAC,CAAE,CAC7D,IAAM6K,EAASxH,EAAcQ,aAAa,CAAC,CAACwC,EAAMsE,EAAUrE,EAAMsE,EAAS,CAAE,CAACvE,EAAMsE,EAAUrE,EAAMsE,EAAS,CAAE,CAACvC,EAAM9E,CAAC,CAAE8E,EAAM7E,CAAC,CAAC,CAAE8F,GACnIlE,EAAUpF,CAAC,CAAC8K,MAAM,CAAC,EAAG,KAAMD,EAChC,CAEA,IAAME,EAAY5F,EAAMrD,OAAO,CAACE,IAAI,CAAEgJ,EAAU7F,EAAMrD,OAAO,CAACC,EAAE,CAAEkJ,EAAUF,EAAU5I,GAAG,CAAE+I,EAAUH,EAAU3I,GAAG,CAAE+I,EAAQH,EAAQ7I,GAAG,CAAEiJ,EAAQJ,EAAQ5I,GAAG,CAO5J,OANI6I,GAAWC,GACX/F,CAAAA,EAAMrD,OAAO,CAACE,IAAI,CAAG,CAAC,EAAE,CAACiJ,EAAQ,EAAE,EAAE,CAACC,EAAQ,CAAC,AAAD,EAE9CC,GAASC,GACTjG,CAAAA,EAAMrD,OAAO,CAACC,EAAE,CAAG,CAAC,EAAE,CAACoJ,EAAM,EAAE,EAAE,CAACC,EAAM,CAAC,AAAD,EAErChG,CACX,CACJ,CAoBA/B,EAAcgI,cAAc,CAAGnI,EAAMP,EAAc0I,cAAc,CAAE,CAC/DC,UAAW,CAAA,EAcXC,WAAY,CACR5B,QAAS,CAAA,CACb,EAgBAnC,YAAa,GAmBb+B,UAAW,CAQPI,QAAS,CAAA,EAOTC,OAAQ,MAOR3F,MAAO,MAOPE,WAAY,OAChB,EAQAF,MAAO,EAOPgC,SAAU,GAOVF,SAAU,EAMV0B,UAAW,KAAK,EAmBhB+D,QAAS,CAWLC,aAAc,2DACdC,YAAa,0EACjB,CACJ,GACA1I,EAAOK,EAAc1C,SAAS,CAAE,CAC5BW,WAAYK,EACZgK,cAAe,CAAC,OAAQ,KAAM,SAAS,CACvCC,WAAYpJ,EAAa7B,SAAS,CAACiL,UAAU,CAC7CC,eAAgBrJ,EAAa7B,SAAS,CAACkL,cAAc,CAErDC,eAAgB,CAAA,CACpB,GACA3K,IAA0I4K,kBAAkB,CAAC,UAAW1I,GA+L3I,IAAMtC,EAAgBE,IAGzC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}