{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/pointandfigure\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Point and figure series type for Highcharts Stock\n *\n * (c) 2010-2025 <PERSON><PERSON><PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"RendererRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/pointandfigure\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"],amd1[\"RendererRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/pointandfigure\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"RendererRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"RendererRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__608__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 608:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__608__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ pointandfigure_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/PointAndFigure/PointAndFigurePoint.js\n/* *\n *\n *  (c) 2010-2025 Kamil Musialowski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n*\n*  Imports\n*\n* */\n\nconst { seriesTypes: { scatter: { prototype: { pointClass: ScatterPoint } } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n */\nclass PointAndFigurePoint extends ScatterPoint {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    resolveMarker() {\n        const seriesOptions = this.series.options;\n        this.marker = this.options.marker =\n            this.upTrend ? seriesOptions.markerUp : seriesOptions.marker;\n        this.color = this.options.marker.lineColor;\n    }\n    resolveColor() {\n        super.resolveColor();\n        this.resolveMarker();\n    }\n    /**\n     * Extend the parent method by adding up or down to the class name.\n     * @private\n     * @function Highcharts.seriesTypes.pointandfigure#getClassName\n     */\n    getClassName() {\n        return super.getClassName.call(this) +\n            (this.upTrend ?\n                ' highcharts-point-up' :\n                ' highcharts-point-down');\n    }\n}\n/* *\n *\n *  Export Default\n *\n * */\n/* harmony default export */ const PointAndFigure_PointAndFigurePoint = (PointAndFigurePoint);\n\n;// ./code/es-modules/Series/PointAndFigure/PointAndFigureSeriesDefaults.js\n/* *\n *\n *  (c) 2010-2025 Kamil Musialowski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * The Point and Figure series represents changes in stock price movements,\n * without focusing on the time and volume. Each data point is created when the\n * `boxSize` criteria is met. Opposite column of points gets created only when\n * the `reversalAmount` threshold is met.\n *\n * @sample stock/demo/pointandfigure/\n *         Point and Figure series\n *\n * @extends      plotOptions.scatter\n * @product      highstock\n * @excluding    boostBlending, boostThreshold, compare, compareBase,\n *               compareStart, cumulative, cumulativeStart, dataGrouping,\n *               dataGrouping, dragDrop\n * @requires     modules/pointandfigure\n * @optionparent plotOptions.pointandfigure\n */\nconst PointAndFigureSeriesDefaults = {\n    boxSize: '1%',\n    reversalAmount: 3,\n    tooltip: {\n        pointFormat: '<span style=\"color:{point.color}\">\\u25CF</span> ' +\n            '<b> {series.name}</b><br/>' +\n            'Close: {point.y:.2f}<br/>',\n        headerFormat: ''\n    },\n    turboThreshold: 0,\n    groupPadding: 0.2,\n    pointPadding: 0.1,\n    pointRange: null,\n    dataGrouping: {\n        enabled: false\n    },\n    markerUp: {\n        symbol: 'cross',\n        lineColor: '#00FF00',\n        lineWidth: 2\n    },\n    marker: {\n        symbol: 'circle',\n        fillColor: 'transparent',\n        lineColor: '#FF0000',\n        lineWidth: 2\n    },\n    legendSymbol: 'lineMarker'\n};\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `pointandfigure` series. If the [type](#series.pointandfigure.type)\n * option is not specified, it is inherited from [chart.type](\n * #chart.type).\n *\n * @type      {*}\n * @extends   series,plotOptions.pointandfigure\n * @product   highstock\n * @excluding boostBlending, boostThreshold, compare, compareBase,\n *            compareStart, cumulative, cumulativeStart, dataGrouping,\n *            dataGrouping, dragDrop\n * @requires  modules/pointandfigure\n * @apioption series.pointandfigure\n */\n/**\n * An array of data points for the series. For the `pointandfigure` series\n * type, points can be given in the following way:\n *\n * 1. An array of arrays with 2 values. In this case, the values correspond\n *    to `x, y`. Y values are parsed under the hood to create\n *    point and figure format data points.\n *    ```js\n *    data: [\n *        [1665408600000, 140.42],\n *        [1665495000000, 138.98],\n *        [1665581400000, 138.34]\n *    ]\n *    ```\n * 2. An array of objects with named values `{x, y}`.\n *    ```js\n *    data: [\n *        {x: 1665408600000, y: 140.42},\n *        {x: 1665495000000, y: 138.98},\n *        {x: 1665581400000, y: 138.34}\n *    ]\n *    ```\n *\n * @type      {Array<Array<number,number>|*>}\n * @extends   series.scatter.data\n * @product   highstock\n * @apioption series.pointandfigure.data\n */\n/**\n * Price increment that determines if a new point should be added to the column.\n *\n *\n * @type      {string|number}\n * @since 12.0.0\n * @product   highstock\n * @apioption plotOptions.pointandfigure.boxSize\n */\n/**\n * Threshold that should be met to create a new column in opposite direction.\n *\n *\n * @type      {number}\n * @since 12.0.0\n * @product   highstock\n * @apioption plotOptions.pointandfigure.reversalAmount\n */\n/**\n * Marker options for the up direction column, inherited from `series.marker`\n * options.\n *\n * @extends   plotOptions.series.marker\n * @product   highstock\n * @apioption plotOptions.pointandfigure.markerUp\n */\n''; // Keeps doclets above detached\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const PointAndFigure_PointAndFigureSeriesDefaults = (PointAndFigureSeriesDefaults);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"RendererRegistry\"],\"commonjs\":[\"highcharts\",\"RendererRegistry\"],\"commonjs2\":[\"highcharts\",\"RendererRegistry\"],\"root\":[\"Highcharts\",\"RendererRegistry\"]}\nvar highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_ = __webpack_require__(608);\nvar highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_);\n;// ./code/es-modules/Series/PointAndFigure/PointAndFigureSymbols.js\n/* *\n *\n *  Imports\n *\n * */\n\n/* *\n *\n *  Composition\n *\n * */\nvar PointAndFigureSymbols;\n(function (PointAndFigureSymbols) {\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    const modifiedMembers = [];\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * @private\n     */\n    function compose(SVGRendererClass) {\n        if (modifiedMembers.indexOf(SVGRendererClass) === -1) {\n            modifiedMembers.push(SVGRendererClass);\n            const symbols = SVGRendererClass.prototype.symbols;\n            symbols.cross = cross;\n        }\n        const RendererClass = highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default().getRendererType();\n        // The symbol callbacks are generated on the SVGRenderer object in all\n        // browsers.\n        if (modifiedMembers.indexOf(RendererClass)) {\n            modifiedMembers.push(RendererClass);\n        }\n    }\n    PointAndFigureSymbols.compose = compose;\n    /**\n     *\n     */\n    function cross(x, y, w, h) {\n        return [\n            ['M', x, y],\n            ['L', x + w, y + h],\n            ['M', x + w, y],\n            ['L', x, y + h],\n            ['Z']\n        ];\n    }\n})(PointAndFigureSymbols || (PointAndFigureSymbols = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const PointAndFigure_PointAndFigureSymbols = (PointAndFigureSymbols);\n\n;// ./code/es-modules/Series/PointAndFigure/PointAndFigureSeries.js\n/* *\n *\n *  (c) 2010-2025 Kamil Musialowski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *  Imports\n *\n * */\n\n\n\n\n\n\nconst { composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nconst { scatter: ScatterSeries, column: { prototype: columnProto } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\nconst { extend, merge, pushUnique, isNumber, relativeLength } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Declarations\n *\n * */\n/* *\n *\n *  Functions\n *\n * */\n/* *\n *\n *  Class\n *\n * */\n/**\n * The series type\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.pointandfigure\n *\n * @augments Highcharts.Series\n */\nclass PointAndFigureSeries extends ScatterSeries {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n        * */\n        super(...arguments);\n        this.allowDG = false;\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static compose(SVGRendererClass) {\n        if (pushUnique(composed, 'pointandfigure')) {\n            PointAndFigure_PointAndFigureSymbols.compose(SVGRendererClass);\n        }\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    init() {\n        super.init.apply(this, arguments);\n        this.pnfDataGroups = [];\n    }\n    getProcessedData() {\n        if (!this.pnfDataGroups) {\n            return {\n                modified: this.dataTable.modified,\n                cropped: false,\n                cropStart: 0,\n                closestPointRange: 1\n            };\n        }\n        const series = this, modified = this.dataTable.modified, options = series.options, xData = series.getColumn('x', true), yData = series.getColumn('y', true), boxSize = options.boxSize, calculatedBoxSize = isNumber(boxSize) ?\n            boxSize : relativeLength(boxSize, yData[0]), pnfDataGroups = series.pnfDataGroups, reversal = calculatedBoxSize * options.reversalAmount;\n        series.calculatedBoxSize = calculatedBoxSize;\n        let upTrend;\n        /**\n         * Get the Y value of last data point, from the last PNF group.\n         * @private\n         * @function Highcharts.seriesTypes.pointandfigure#getLastPoint\n         */\n        function getLastPoint(pnfDataGroups) {\n            const y = pnfDataGroups[pnfDataGroups.length - 1].y;\n            return y[y.length - 1];\n        }\n        /**\n         * Push new data point to the last PNF group.\n         * @private\n         * @function Highcharts.seriesTypes.pointandfigure#pushNewPoint\n         */\n        function pushNewPoint(y, upTrend, lastPoint) {\n            const currPointGroup = pnfDataGroups[pnfDataGroups.length - 1], flipFactor = upTrend ? 1 : -1, times = Math.floor(flipFactor * (y - lastPoint) / calculatedBoxSize);\n            for (let i = 1; i <= times; i++) {\n                const newPoint = lastPoint + flipFactor * (calculatedBoxSize * i);\n                currPointGroup.y.push(newPoint);\n            }\n        }\n        if (this.isDirtyData || pnfDataGroups.length === 0) {\n            this.pnfDataGroups.length = 0;\n            // Get first point and determine its symbol and trend\n            for (let i = 0; i < yData.length; i++) {\n                const x = xData[i], close = yData[i], firstPoint = yData[0];\n                if (close - firstPoint >= calculatedBoxSize) {\n                    upTrend = true;\n                    pnfDataGroups.push({ x, y: [close], upTrend });\n                    break;\n                }\n                if (firstPoint - close >= calculatedBoxSize) {\n                    upTrend = false;\n                    pnfDataGroups.push({ x, y: [close], upTrend });\n                    break;\n                }\n            }\n            yData.forEach((close, i) => {\n                const x = xData[i], lastPoint = getLastPoint(pnfDataGroups);\n                if (upTrend) {\n                    // Add point going UP\n                    if (close - lastPoint >= calculatedBoxSize) {\n                        pushNewPoint(close, upTrend, lastPoint);\n                    }\n                    if (lastPoint - close >= reversal) { // Handle reversal\n                        upTrend = false;\n                        pnfDataGroups.push({ x, y: [], upTrend });\n                        pushNewPoint(close, upTrend, lastPoint);\n                    }\n                }\n                if (!upTrend) {\n                    // Add point going DOWN\n                    if (lastPoint - close >= calculatedBoxSize) {\n                        pushNewPoint(close, upTrend, lastPoint);\n                    }\n                    if (close - lastPoint >= reversal) { // Handle reversal\n                        upTrend = true;\n                        pnfDataGroups.push({ x, y: [], upTrend });\n                        pushNewPoint(close, upTrend, lastPoint);\n                    }\n                }\n            });\n        }\n        // Process the pnfDataGroups to HC series format\n        const finalData = [];\n        const processedXData = [];\n        const processedYData = [];\n        pnfDataGroups.forEach((point) => {\n            const x = point.x, upTrend = point.upTrend;\n            point.y.forEach((y) => {\n                processedXData.push(x);\n                processedYData.push(y);\n                finalData.push({\n                    x,\n                    y,\n                    upTrend\n                });\n            });\n        });\n        modified.setColumn('x', processedXData);\n        modified.setColumn('y', processedYData);\n        series.pnfDataGroups = pnfDataGroups;\n        series.processedData = finalData;\n        return {\n            modified,\n            cropped: false,\n            cropStart: 0,\n            closestPointRange: 1\n        };\n    }\n    markerAttribs(point) {\n        const series = this, options = series.options, attribs = {}, pos = point.pos();\n        attribs.width = series.markerWidth;\n        attribs.height = series.markerHeight;\n        if (pos && attribs.width && attribs.height) {\n            attribs.x = pos[0] - Math.round(attribs.width) / 2;\n            attribs.y = pos[1] - Math.round(attribs.height) / 2;\n        }\n        if (options.crisp && attribs.x) {\n            // Math.floor for #1843:\n            attribs.x = Math.floor(attribs.x);\n        }\n        return attribs;\n    }\n    translate() {\n        const metrics = this.getColumnMetrics(), calculatedBoxSize = this.calculatedBoxSize;\n        this.markerWidth = metrics.width + metrics.paddedWidth + metrics.offset;\n        this.markerHeight =\n            this.yAxis.toPixels(0) - this.yAxis.toPixels(calculatedBoxSize);\n        super.translate();\n    }\n}\nPointAndFigureSeries.defaultOptions = merge(ScatterSeries.defaultOptions, PointAndFigure_PointAndFigureSeriesDefaults);\nextend(PointAndFigureSeries.prototype, {\n    takeOrdinalPosition: true,\n    pnfDataGroups: [],\n    getColumnMetrics: columnProto.getColumnMetrics,\n    pointClass: PointAndFigure_PointAndFigurePoint,\n    sorted: true\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('pointandfigure', PointAndFigureSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const PointAndFigure_PointAndFigureSeries = (PointAndFigureSeries);\n\n;// ./code/es-modules/masters/modules/pointandfigure.js\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nPointAndFigure_PointAndFigureSeries.compose(G.Renderer);\n/* harmony default export */ const pointandfigure_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__608__", "PointAndFigureSymbols", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "pointandfigure_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "seriesTypes", "scatter", "pointClass", "ScatterPoint", "PointAndFigure_PointAndFigurePoint", "<PERSON><PERSON><PERSON><PERSON>", "seriesOptions", "series", "options", "marker", "upTrend", "markerUp", "color", "lineColor", "resolveColor", "getClassName", "highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_", "highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default", "modifiedMembers", "cross", "x", "y", "w", "h", "compose", "SVGRendererClass", "indexOf", "push", "symbols", "RendererClass", "getRendererType", "PointAndFigure_PointAndFigureSymbols", "composed", "ScatterSeries", "column", "columnProto", "extend", "merge", "pushUnique", "isNumber", "<PERSON><PERSON><PERSON><PERSON>", "PointAndFigureSeries", "constructor", "arguments", "allowDG", "init", "apply", "pnfDataGroups", "getProcessedData", "modified", "dataTable", "cropped", "cropStart", "closestPointRange", "xData", "getColumn", "yData", "boxSize", "calculatedBoxSize", "reversal", "reversalAmount", "pushNewPoint", "lastPoint", "currPointGroup", "length", "flipFactor", "times", "Math", "floor", "i", "newPoint", "isDirtyData", "close", "firstPoint", "for<PERSON>ach", "getLastPoint", "finalData", "processedXData", "processedYData", "point", "setColumn", "processedData", "markerAttribs", "attribs", "pos", "width", "marker<PERSON>id<PERSON>", "height", "markerHeight", "round", "crisp", "translate", "metrics", "getColumnMetrics", "<PERSON><PERSON><PERSON><PERSON>", "offset", "yAxis", "toPixels", "defaultOptions", "tooltip", "pointFormat", "headerFormat", "turboThreshold", "groupPadding", "pointPadding", "pointRange", "dataGrouping", "enabled", "symbol", "lineWidth", "fillColor", "legendSymbol", "takeOrdinalPosition", "sorted", "registerSeriesType", "G", "PointAndFigure_PointAndFigureSeries", "<PERSON><PERSON><PERSON>"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,gBAAmB,EACrH,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,oCAAqC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAACA,EAAK,gBAAmB,CAAE,GACtJ,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,oCAAoC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,gBAAmB,EAE1JA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,gBAAmB,CAC/H,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,IACvG,AAAC,CAAA,KACP,aACA,IAyTNC,EAzTUC,EAAuB,CAE/B,IACC,AAACT,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIK,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAad,OAAO,CAG5B,IAAIC,EAASU,CAAwB,CAACE,EAAS,CAAG,CAGjDb,QAAS,CAAC,CACX,EAMA,OAHAU,CAAmB,CAACG,EAAS,CAACZ,EAAQA,EAAOD,OAAO,CAAEY,GAG/CX,EAAOD,OAAO,AACtB,CAMCY,EAAoBI,CAAC,CAAG,AAACf,IACxB,IAAIgB,EAAShB,GAAUA,EAAOiB,UAAU,CACvC,IAAOjB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAW,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACnB,EAASqB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACvB,EAASsB,IAC5EE,OAAOC,cAAc,CAACzB,EAASsB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAkBjL,GAAM,CAAEE,YAAa,CAAEC,QAAS,CAAEV,UAAW,CAAEW,WAAYC,CAAY,CAAE,CAAE,CAAE,CAAE,CAAIJ,IA2ChDK,EAjCnC,cAAkCD,EAM9BE,eAAgB,CACZ,IAAMC,EAAgB,IAAI,CAACC,MAAM,CAACC,OAAO,AACzC,CAAA,IAAI,CAACC,MAAM,CAAG,IAAI,CAACD,OAAO,CAACC,MAAM,CAC7B,IAAI,CAACC,OAAO,CAAGJ,EAAcK,QAAQ,CAAGL,EAAcG,MAAM,CAChE,IAAI,CAACG,KAAK,CAAG,IAAI,CAACJ,OAAO,CAACC,MAAM,CAACI,SAAS,AAC9C,CACAC,cAAe,CACX,KAAK,CAACA,eACN,IAAI,CAACT,aAAa,EACtB,CAMAU,cAAe,CACX,OAAO,KAAK,CAACA,aAAatB,IAAI,CAAC,IAAI,EAC9B,CAAA,IAAI,CAACiB,OAAO,CACT,uBACA,wBAAuB,CACnC,CACJ,EAwJA,IAAIM,EAA2I3C,EAAoB,KAC/J4C,EAA+J5C,EAAoBI,CAAC,CAACuC,IAczL,AAAC,SAAU9C,CAAqB,EAM5B,IAAMgD,EAAkB,EAAE,CA2B1B,SAASC,EAAMC,CAAC,CAAEC,CAAC,CAAEC,CAAC,CAAEC,CAAC,EACrB,MAAO,CACH,CAAC,IAAKH,EAAGC,EAAE,CACX,CAAC,IAAKD,EAAIE,EAAGD,EAAIE,EAAE,CACnB,CAAC,IAAKH,EAAIE,EAAGD,EAAE,CACf,CAAC,IAAKD,EAAGC,EAAIE,EAAE,CACf,CAAC,IAAI,CACR,AACL,CAZArD,EAAsBsD,OAAO,CAb7B,SAAiBC,CAAgB,EACqB,KAA9CP,EAAgBQ,OAAO,CAACD,KACxBP,EAAgBS,IAAI,CAACF,GAErBG,AADgBH,EAAiBlC,SAAS,CAACqC,OAAO,CAC1CT,KAAK,CAAGA,GAEpB,IAAMU,EAAgBZ,IAAkJa,eAAe,EAGnLZ,CAAAA,EAAgBQ,OAAO,CAACG,IACxBX,EAAgBS,IAAI,CAACE,EAE7B,CAcJ,EAAG3D,GAA0BA,CAAAA,EAAwB,CAAC,CAAA,GAMzB,IAAM6D,EAAwC7D,EAuBrE,CAAE8D,SAAAA,CAAQ,CAAE,CAAInC,IAChB,CAAEI,QAASgC,CAAa,CAAEC,OAAQ,CAAE3C,UAAW4C,CAAW,CAAE,CAAE,CAAG,AAACpC,IAA2IC,WAAW,CACxN,CAAEoC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAEC,WAAAA,CAAU,CAAEC,SAAAA,CAAQ,CAAEC,eAAAA,CAAc,CAAE,CAAI3C,GAyBjE,OAAM4C,UAA6BR,EAC/BS,aAAc,CAMV,KAAK,IAAIC,WACT,IAAI,CAACC,OAAO,CAAG,CAAA,CACnB,CAMA,OAAOpB,QAAQC,CAAgB,CAAE,CACzBa,EAAWN,EAAU,mBACrBD,EAAqCP,OAAO,CAACC,EAErD,CAMAoB,MAAO,CACH,KAAK,CAACA,KAAKC,KAAK,CAAC,IAAI,CAAEH,WACvB,IAAI,CAACI,aAAa,CAAG,EAAE,AAC3B,CACAC,kBAAmB,KAYXtC,EAXJ,GAAI,CAAC,IAAI,CAACqC,aAAa,CACnB,MAAO,CACHE,SAAU,IAAI,CAACC,SAAS,CAACD,QAAQ,CACjCE,QAAS,CAAA,EACTC,UAAW,EACXC,kBAAmB,CACvB,EAEJ,IAAqBJ,EAAW,IAAI,CAACC,SAAS,CAACD,QAAQ,CAAEzC,EAAUD,AAApD,IAAI,CAAuDC,OAAO,CAAE8C,EAAQ/C,AAA5E,IAAI,CAA+EgD,SAAS,CAAC,IAAK,CAAA,GAAOC,EAAQjD,AAAjH,IAAI,CAAoHgD,SAAS,CAAC,IAAK,CAAA,GAAOE,EAAUjD,EAAQiD,OAAO,CAAEC,EAAoBnB,EAASkB,GACjNA,EAAUjB,EAAeiB,EAASD,CAAK,CAAC,EAAE,EAAGT,EAAgBxC,AADlD,IAAI,CACqDwC,aAAa,CAAEY,EAAWD,EAAoBlD,EAAQoD,cAAc,CAiB5I,SAASC,EAAaxC,CAAC,CAAEX,CAAO,CAAEoD,CAAS,EACvC,IAAMC,EAAiBhB,CAAa,CAACA,EAAciB,MAAM,CAAG,EAAE,CAAEC,EAAavD,EAAU,EAAI,GAAIwD,EAAQC,KAAKC,KAAK,CAACH,EAAc5C,CAAAA,EAAIyC,CAAQ,EAAKJ,GACjJ,IAAK,IAAIW,EAAI,EAAGA,GAAKH,EAAOG,IAAK,CAC7B,IAAMC,EAAWR,EAAYG,AAAcP,EAAoBW,EAAlCJ,EAC7BF,EAAe1C,CAAC,CAACM,IAAI,CAAC2C,EAC1B,CACJ,CACA,GAvBA/D,AAFe,IAAI,CAEZmD,iBAAiB,CAAGA,EAuBvB,IAAI,CAACa,WAAW,EAAIxB,AAAyB,IAAzBA,EAAciB,MAAM,CAAQ,CAChD,IAAI,CAACjB,aAAa,CAACiB,MAAM,CAAG,EAE5B,IAAK,IAAIK,EAAI,EAAGA,EAAIb,EAAMQ,MAAM,CAAEK,IAAK,CACnC,IAAMjD,EAAIkC,CAAK,CAACe,EAAE,CAAEG,EAAQhB,CAAK,CAACa,EAAE,CAAEI,EAAajB,CAAK,CAAC,EAAE,CAC3D,GAAIgB,EAAQC,GAAcf,EAAmB,CACzChD,EAAU,CAAA,EACVqC,EAAcpB,IAAI,CAAC,CAAEP,EAAAA,EAAGC,EAAG,CAACmD,EAAM,CAAE9D,QAAAA,CAAQ,GAC5C,KACJ,CACA,GAAI+D,EAAaD,GAASd,EAAmB,CACzChD,EAAU,CAAA,EACVqC,EAAcpB,IAAI,CAAC,CAAEP,EAAAA,EAAGC,EAAG,CAACmD,EAAM,CAAE9D,QAAAA,CAAQ,GAC5C,KACJ,CACJ,CACA8C,EAAMkB,OAAO,CAAC,CAACF,EAAOH,KAClB,IAAMjD,EAAIkC,CAAK,CAACe,EAAE,CAAEP,EAAYa,AAjCxC,SAAsB5B,CAAa,EAC/B,IAAM1B,EAAI0B,CAAa,CAACA,EAAciB,MAAM,CAAG,EAAE,CAAC3C,CAAC,CACnD,OAAOA,CAAC,CAACA,EAAE2C,MAAM,CAAG,EAAE,AAC1B,EA8BqDjB,GACzCrC,IAEI8D,EAAQV,GAAaJ,GACrBG,EAAaW,EAAO9D,EAASoD,GAE7BA,EAAYU,GAASb,IACrBjD,EAAU,CAAA,EACVqC,EAAcpB,IAAI,CAAC,CAAEP,EAAAA,EAAGC,EAAG,EAAE,CAAEX,QAAAA,CAAQ,GACvCmD,EAAaW,EAAO9D,EAASoD,KAGjC,CAACpD,IAEGoD,EAAYU,GAASd,GACrBG,EAAaW,EAAO9D,EAASoD,GAE7BU,EAAQV,GAAaH,IACrBjD,EAAU,CAAA,EACVqC,EAAcpB,IAAI,CAAC,CAAEP,EAAAA,EAAGC,EAAG,EAAE,CAAEX,QAAAA,CAAQ,GACvCmD,EAAaW,EAAO9D,EAASoD,IAGzC,EACJ,CAEA,IAAMc,EAAY,EAAE,CACdC,EAAiB,EAAE,CACnBC,EAAiB,EAAE,CAiBzB,OAhBA/B,EAAc2B,OAAO,CAAC,AAACK,IACnB,IAAM3D,EAAI2D,EAAM3D,CAAC,CAAEV,EAAUqE,EAAMrE,OAAO,CAC1CqE,EAAM1D,CAAC,CAACqD,OAAO,CAAC,AAACrD,IACbwD,EAAelD,IAAI,CAACP,GACpB0D,EAAenD,IAAI,CAACN,GACpBuD,EAAUjD,IAAI,CAAC,CACXP,EAAAA,EACAC,EAAAA,EACAX,QAAAA,CACJ,EACJ,EACJ,GACAuC,EAAS+B,SAAS,CAAC,IAAKH,GACxB5B,EAAS+B,SAAS,CAAC,IAAKF,GACxBvE,AArFe,IAAI,CAqFZwC,aAAa,CAAGA,EACvBxC,AAtFe,IAAI,CAsFZ0E,aAAa,CAAGL,EAChB,CACH3B,SAAAA,EACAE,QAAS,CAAA,EACTC,UAAW,EACXC,kBAAmB,CACvB,CACJ,CACA6B,cAAcH,CAAK,CAAE,CACjB,IAAqBvE,EAAUD,AAAhB,IAAI,CAAmBC,OAAO,CAAE2E,EAAU,CAAC,EAAGC,EAAML,EAAMK,GAAG,GAW5E,OAVAD,EAAQE,KAAK,CAAG9E,AADD,IAAI,CACI+E,WAAW,CAClCH,EAAQI,MAAM,CAAGhF,AAFF,IAAI,CAEKiF,YAAY,CAChCJ,GAAOD,EAAQE,KAAK,EAAIF,EAAQI,MAAM,GACtCJ,EAAQ/D,CAAC,CAAGgE,CAAG,CAAC,EAAE,CAAGjB,KAAKsB,KAAK,CAACN,EAAQE,KAAK,EAAI,EACjDF,EAAQ9D,CAAC,CAAG+D,CAAG,CAAC,EAAE,CAAGjB,KAAKsB,KAAK,CAACN,EAAQI,MAAM,EAAI,GAElD/E,EAAQkF,KAAK,EAAIP,EAAQ/D,CAAC,EAE1B+D,CAAAA,EAAQ/D,CAAC,CAAG+C,KAAKC,KAAK,CAACe,EAAQ/D,CAAC,CAAA,EAE7B+D,CACX,CACAQ,WAAY,CACR,IAAMC,EAAU,IAAI,CAACC,gBAAgB,GAAInC,EAAoB,IAAI,CAACA,iBAAiB,AACnF,CAAA,IAAI,CAAC4B,WAAW,CAAGM,EAAQP,KAAK,CAAGO,EAAQE,WAAW,CAAGF,EAAQG,MAAM,CACvE,IAAI,CAACP,YAAY,CACb,IAAI,CAACQ,KAAK,CAACC,QAAQ,CAAC,GAAK,IAAI,CAACD,KAAK,CAACC,QAAQ,CAACvC,GACjD,KAAK,CAACiC,WACV,CACJ,CACAlD,EAAqByD,cAAc,CAAG7D,EAAMJ,EAAciE,cAAc,CA1XnC,CACjCzC,QAAS,KACTG,eAAgB,EAChBuC,QAAS,CACLC,YAAa,iGAGbC,aAAc,EAClB,EACAC,eAAgB,EAChBC,aAAc,GACdC,aAAc,GACdC,WAAY,KACZC,aAAc,CACVC,QAAS,CAAA,CACb,EACAhG,SAAU,CACNiG,OAAQ,QACR/F,UAAW,UACXgG,UAAW,CACf,EACApG,OAAQ,CACJmG,OAAQ,SACRE,UAAW,cACXjG,UAAW,UACXgG,UAAW,CACf,EACAE,aAAc,YAClB,GA+VA3E,EAAOK,EAAqBlD,SAAS,CAAE,CACnCyH,oBAAqB,CAAA,EACrBjE,cAAe,EAAE,CACjB8C,iBAAkB1D,EAAY0D,gBAAgB,CAC9C3F,WAAYE,EACZ6G,OAAQ,CAAA,CACZ,GACAlH,IAA0ImH,kBAAkB,CAAC,iBAAkBzE,GAa/K,IAAM0E,EAAKtH,IACXuH,AAR0E3E,EAQtCjB,OAAO,CAAC2F,EAAEE,QAAQ,EACzB,IAAM1H,EAAuBE,IAGhD,OADYH,EAAoB,OAAU,AAE3C,CAAA"}