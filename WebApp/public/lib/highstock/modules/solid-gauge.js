!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/solid-gauge
 * @requires highcharts
 * @requires highcharts/highcharts-more
 *
 * Solid angular gauge module
 *
 * (c) 2010-2025 Torstein Honsi
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.SeriesRegistry,t._Highcharts.Color):"function"==typeof define&&define.amd?define("highcharts/modules/solid-gauge",["highcharts/highcharts"],function(t){return e(t,t.SeriesRegistry,t.Color)}):"object"==typeof exports?exports["highcharts/modules/solid-gauge"]=e(t._Highcharts,t._Highcharts.SeriesRegistry,t._Highcharts.Color):t.Highcharts=e(t.Highcharts,t.Highcharts.SeriesRegistry,t.Highcharts.Color)}("undefined"==typeof window?this:window,(t,e,o)=>(()=>{"use strict";var s,r={512:t=>{t.exports=e},620:t=>{t.exports=o},944:e=>{e.exports=t}},i={};function a(t){var e=i[t];if(void 0!==e)return e.exports;var o=i[t]={exports:{}};return r[t](o,o.exports,a),o.exports}a.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return a.d(e,{a:e}),e},a.d=(t,e)=>{for(var o in e)a.o(e,o)&&!a.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},a.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var n={};a.d(n,{default:()=>N});var l=a(944),d=a.n(l);let{defaultOptions:h}=d(),{noop:c}=d(),{addEvent:g,extend:p,isObject:u,merge:f,relativeLength:m}=d(),y={radius:0,scope:"stack",where:void 0};function x(t,e){return u(t)||(t={radius:t||0}),f(y,e,t)}let C={optionsToObject:x};var R=a(512),v=a.n(R),A=a(620);let{parse:b}=a.n(A)(),{merge:w}=d();!function(t){t.initDataClasses=function(t){let e=this.chart,o=this.legendItem=this.legendItem||{},s=this.options,r=t.dataClasses||[],i,a,n=e.options.chart.colorCount,l=0,d;this.dataClasses=a=[],o.labels=[];for(let t=0,o=r.length;t<o;++t)i=w(i=r[t]),a.push(i),(e.styledMode||!i.color)&&("category"===s.dataClassColor?(e.styledMode||(n=(d=e.options.colors||[]).length,i.color=d[l]),i.colorIndex=l,++l===n&&(l=0)):i.color=b(s.minColor).tweenTo(b(s.maxColor),o<2?.5:t/(o-1)))},t.initStops=function(){let t=this.options,e=this.stops=t.stops||[[0,t.minColor||""],[1,t.maxColor||""]];for(let t=0,o=e.length;t<o;++t)e[t].color=b(e[t][1])},t.normalizedValue=function(t){let e=this.max||0,o=this.min||0;return this.logarithmic&&(t=this.logarithmic.log2lin(t)),1-(e-t)/(e-o||1)},t.toColor=function(t,e){let o,s,r,i,a,n,l=this.dataClasses,d=this.stops;if(l){for(n=l.length;n--;)if(s=(a=l[n]).from,r=a.to,(void 0===s||t>=s)&&(void 0===r||t<=r)){i=a.color,e&&(e.dataClass=n,e.colorIndex=a.colorIndex);break}}else{for(o=this.normalizedValue(t),n=d.length;n--&&!(o>d[n][0]););s=d[n]||d[n+1],o=1-((r=d[n+1]||s)[0]-o)/(r[0]-s[0]||1),i=s.color.tweenTo(r.color,o)}return i}}(s||(s={}));let M=s,{extend:H}=d(),I={init:function(t){H(t,M)}},{gauge:_,pie:j}=v().seriesTypes,{clamp:P,extend:S,isNumber:k,merge:O,pick:T,pInt:z}=d();class D extends _{translate(){let t=this.yAxis;I.init(t),!t.dataClasses&&t.options.dataClasses&&t.initDataClasses(t.options),t.initStops(),_.prototype.translate.call(this)}drawPoints(){let t,e=this.yAxis,o=e.center,s=this.options,r=this.chart.renderer,i=s.overshoot,a=s.rounded&&void 0===s.borderRadius,n=k(i)?i/180*Math.PI:0;for(let i of(k(s.threshold)&&(t=e.startAngleRad+e.translate(s.threshold,void 0,void 0,void 0,!0)),this.thresholdAngleRad=T(t,e.startAngleRad),this.points))if(!i.isNull){let t=z(T(i.options.radius,s.radius,100))*o[2]/200,l=z(T(i.options.innerRadius,s.innerRadius,60))*o[2]/200,d=Math.min(e.startAngleRad,e.endAngleRad),h=Math.max(e.startAngleRad,e.endAngleRad),c=i.graphic,g=e.startAngleRad+e.translate(i.y,void 0,void 0,void 0,!0),p,u,f=e.toColor(i.y,i),m=i.getClassName();"none"===f&&(f=i.color||this.color||"none"),"none"!==f&&(i.color=f),g=P(g,d-n,h+n),!1===s.wrap&&(g=P(g,d,h));let y=a?(t-l)/2/t:0,x=Math.min(g,this.thresholdAngleRad)-y,R=Math.max(g,this.thresholdAngleRad)+y;R-x>2*Math.PI&&(R=x+2*Math.PI);let v=a?"50%":0;s.borderRadius&&(v=C.optionsToObject(s.borderRadius).radius),i.shapeArgs=p={x:o[0],y:o[1],r:t,innerR:l,start:x,end:R,borderRadius:v},i.startR=t,c?(u=p.d,c.animate(S({fill:f},p)),u&&(p.d=u)):i.graphic=c=r.arc(p).attr({fill:f,"sweep-flag":0}).add(this.group),this.chart.styledMode?this.yAxis?.stops&&(m=m.replace(/highcharts-color-\d/gm,"").trim()):("square"!==s.linecap&&c.attr({"stroke-linecap":"round","stroke-linejoin":"round"}),c.attr({stroke:s.borderColor||"none","stroke-width":s.borderWidth||0})),c&&c.addClass(m)}}animate(t){t||(this.startAngleRad=this.thresholdAngleRad,j.prototype.animate.call(this,t))}}D.defaultOptions=O(_.defaultOptions,{colorByPoint:!0,dataLabels:{y:0}}),v().registerSeriesType("solidgauge",D);let N=d();return n.default})());