{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/no-data-to-display\n * @requires highcharts\n *\n * Plugin for displaying a message when there is no data visible in chart.\n *\n * (c) 2010-2025 Highsoft AS\n * Author: <PERSON><PERSON><PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"AST\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/no-data-to-display\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"AST\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/no-data-to-display\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"AST\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"AST\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__660__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 660:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__660__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ no_data_to_display_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"AST\"],\"commonjs\":[\"highcharts\",\"AST\"],\"commonjs2\":[\"highcharts\",\"AST\"],\"root\":[\"Highcharts\",\"AST\"]}\nvar highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_ = __webpack_require__(660);\nvar highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default = /*#__PURE__*/__webpack_require__.n(highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_);\n;// ./code/es-modules/Extensions/NoDataToDisplay/NoDataDefaults.js\n/* *\n *\n *  Plugin for displaying a message when there is no data visible in chart.\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Oystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * @optionparent lang\n */\nconst lang = {\n    /**\n     * The text to display when the chart contains no data.\n     *\n     * @see [noData](#noData)\n     *\n     * @sample highcharts/no-data-to-display/no-data-line\n     *         No-data text\n     *\n     * @since    3.0.8\n     * @product  highcharts highstock\n     * @requires modules/no-data-to-display\n     */\n    noData: 'No data to display'\n};\n/**\n * Options for displaying a message like \"No data to display\".\n * This feature requires the file no-data-to-display.js to be loaded in the\n * page. The actual text to display is set in the lang.noData option.\n *\n * @sample highcharts/no-data-to-display/no-data-line\n *         Line chart with no-data module\n * @sample highcharts/no-data-to-display/no-data-pie\n *         Pie chart with no-data module\n *\n * @product      highcharts highstock gantt\n * @requires     modules/no-data-to-display\n * @optionparent noData\n */\nconst noData = {\n    /**\n     * An object of additional SVG attributes for the no-data label.\n     *\n     * @type      {Highcharts.SVGAttributes}\n     * @since     3.0.8\n     * @product   highcharts highstock gantt\n     * @apioption noData.attr\n     */\n    attr: {\n        zIndex: 1\n    },\n    /**\n     * Whether to insert the label as HTML, or as pseudo-HTML rendered with\n     * SVG.\n     *\n     * @type      {boolean}\n     * @default   false\n     * @since     4.1.10\n     * @product   highcharts highstock gantt\n     * @apioption noData.useHTML\n     */\n    /**\n     * The position of the no-data label, relative to the plot area.\n     *\n     * @type  {Highcharts.AlignObject}\n     * @since 3.0.8\n     */\n    position: {\n        /**\n         * Horizontal offset of the label, in pixels.\n         */\n        x: 0,\n        /**\n         * Vertical offset of the label, in pixels.\n         */\n        y: 0,\n        /**\n         * Horizontal alignment of the label.\n         *\n         * @type {Highcharts.AlignValue}\n         */\n        align: 'center',\n        /**\n         * Vertical alignment of the label.\n         *\n         * @type {Highcharts.VerticalAlignValue}\n         */\n        verticalAlign: 'middle'\n    },\n    /**\n     * CSS styles for the no-data label.\n     *\n     * @sample highcharts/no-data-to-display/no-data-line\n     *         Styled no-data text\n     *\n     * @type {Highcharts.CSSObject}\n     */\n    style: {\n        /** @ignore */\n        fontWeight: 'bold',\n        /** @ignore */\n        fontSize: '0.8em',\n        /** @ignore */\n        color: \"#666666\" /* Palette.neutralColor60 */\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\nconst NoDataDefaults = {\n    lang,\n    noData\n};\n/* harmony default export */ const NoDataToDisplay_NoDataDefaults = (NoDataDefaults);\n\n;// ./code/es-modules/Extensions/NoDataToDisplay/NoDataToDisplay.js\n/* *\n *\n *  Plugin for displaying a message when there is no data visible in chart.\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Oystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { addEvent, extend, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Returns true if there are data points within the plot area now.\n *\n * @private\n * @function Highcharts.Chart#hasData\n * @return {boolean|undefined}\n * True, if there are data points.\n * @requires modules/no-data-to-display\n */\nfunction chartHasData() {\n    const chart = this, series = chart.series || [];\n    let i = series.length;\n    while (i--) {\n        if (series[i].hasData() && !series[i].options.isInternal) {\n            return true;\n        }\n    }\n    return chart.loadingShown; // #4588\n}\n/**\n * Hide no-data message.\n *\n * @private\n * @function Highcharts.Chart#hideNoData\n * @return {void}\n * @requires modules/no-data-to-display\n */\nfunction chartHideNoData() {\n    const chart = this;\n    if (chart.noDataLabel) {\n        chart.noDataLabel = chart.noDataLabel.destroy();\n    }\n}\n/**\n * Display a no-data message.\n * @private\n * @function Highcharts.Chart#showNoData\n * @param {string} [str]\n * An optional message to show in place of the default one\n * @return {void}\n * @requires modules/no-data-to-display\n */\nfunction chartShowNoData(str) {\n    const chart = this, options = chart.options, text = str || (options && options.lang.noData) || '', noDataOptions = options && (options.noData || {});\n    if (chart.renderer) { // Meaning chart is not destroyed\n        if (!chart.noDataLabel) {\n            chart.noDataLabel = chart.renderer\n                .label(text, 0, 0, void 0, void 0, void 0, noDataOptions.useHTML, void 0, 'no-data')\n                .add();\n        }\n        if (!chart.styledMode) {\n            chart.noDataLabel\n                .attr(highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default().filterUserAttributes(noDataOptions.attr || {}))\n                .css(noDataOptions.style || {});\n        }\n        chart.noDataLabel.align(extend(chart.noDataLabel.getBBox(), noDataOptions.position || {}), false, 'plotBox');\n    }\n}\n/** @private */\nfunction compose(ChartClass, highchartsDefaultOptions) {\n    const chartProto = ChartClass.prototype;\n    if (!chartProto.showNoData) {\n        chartProto.hasData = chartHasData;\n        chartProto.hideNoData = chartHideNoData;\n        chartProto.showNoData = chartShowNoData;\n        addEvent(ChartClass, 'render', onChartRender);\n        merge(true, highchartsDefaultOptions, NoDataToDisplay_NoDataDefaults);\n    }\n}\n/**\n * Add event listener to handle automatic show or hide no-data message.\n * @private\n */\nfunction onChartRender() {\n    const chart = this;\n    if (chart.hasData()) {\n        chart.hideNoData();\n    }\n    else {\n        chart.showNoData();\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst NoDataToDisplay = {\n    compose\n};\n/* harmony default export */ const NoDataToDisplay_NoDataToDisplay = (NoDataToDisplay);\n\n;// ./code/es-modules/masters/modules/no-data-to-display.js\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nNoDataToDisplay_NoDataToDisplay.compose(G.Chart, G.defaultOptions);\n/* harmony default export */ const no_data_to_display_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__660__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "no_data_to_display_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_", "highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default", "NoDataToDisplay_NoDataDefaults", "lang", "noData", "attr", "zIndex", "position", "x", "y", "align", "verticalAlign", "style", "fontWeight", "fontSize", "color", "addEvent", "extend", "merge", "chartHasData", "series", "chart", "i", "length", "hasData", "options", "isInternal", "loadingShown", "chartHideNoData", "noDataLabel", "destroy", "chartShowNoData", "str", "text", "noDataOptions", "renderer", "label", "useHTML", "add", "styledMode", "filterUserAttributes", "css", "getBBox", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hideNoData", "showNoData", "G", "NoDataToDisplay_NoDataToDisplay", "compose", "ChartClass", "highchartsDefaultOptions", "chartProto", "Chart", "defaultOptions"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,GAAM,EACjE,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,wCAAyC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,GAAM,CAAE,GACtH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,wCAAwC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,GAAM,EAE1GA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,GAAM,CAC5E,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAuFzB,EAAoB,KAC3G0B,EAA2G1B,EAAoBI,CAAC,CAACqB,GAiIxG,IAAME,EAJZ,CACnBC,KAtGS,CAaTC,OAAQ,oBACZ,EAyFIA,OA1EW,CASXC,KAAM,CACFC,OAAQ,CACZ,EAiBAC,SAAU,CAINC,EAAG,EAIHC,EAAG,EAMHC,MAAO,SAMPC,cAAe,QACnB,EASAC,MAAO,CAEHC,WAAY,OAEZC,SAAU,QAEVC,MAAO,SACX,CACJ,CASA,EAqBM,CAAEC,SAAAA,CAAQ,CAAEC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAE,CAAInB,IAerC,SAASoB,IACL,IAAoBC,EAASC,AAAf,IAAI,CAAiBD,MAAM,EAAI,EAAE,CAC3CE,EAAIF,EAAOG,MAAM,CACrB,KAAOD,KACH,GAAIF,CAAM,CAACE,EAAE,CAACE,OAAO,IAAM,CAACJ,CAAM,CAACE,EAAE,CAACG,OAAO,CAACC,UAAU,CACpD,MAAO,CAAA,EAGf,OAAOL,AAPO,IAAI,CAOLM,YAAY,AAC7B,CASA,SAASC,IAEDP,AADU,IAAI,CACRQ,WAAW,EACjBR,CAAAA,AAFU,IAAI,CAERQ,WAAW,CAAGR,AAFV,IAAI,CAEYQ,WAAW,CAACC,OAAO,EAAC,CAEtD,CAUA,SAASC,EAAgBC,CAAG,EACxB,IAAoBP,EAAUJ,AAAhB,IAAI,CAAkBI,OAAO,CAAEQ,EAAOD,GAAQP,GAAWA,EAAQtB,IAAI,CAACC,MAAM,EAAK,GAAI8B,EAAgBT,GAAYA,CAAAA,EAAQrB,MAAM,EAAI,CAAC,CAAA,CAC9IiB,CADU,IAAI,CACRc,QAAQ,GACV,AAACd,AAFK,IAAI,CAEHQ,WAAW,EAClBR,CAAAA,AAHM,IAAI,CAGJQ,WAAW,CAAGR,AAHd,IAAI,CAGgBc,QAAQ,CAC7BC,KAAK,CAACH,EAAM,EAAG,EAAG,KAAK,EAAG,KAAK,EAAG,KAAK,EAAGC,EAAcG,OAAO,CAAE,KAAK,EAAG,WACzEC,GAAG,EAAC,EAET,AAACjB,AAPK,IAAI,CAOHkB,UAAU,EACjBlB,AARM,IAAI,CAQJQ,WAAW,CACZxB,IAAI,CAACJ,IAA8FuC,oBAAoB,CAACN,EAAc7B,IAAI,EAAI,CAAC,IAC/IoC,GAAG,CAACP,EAActB,KAAK,EAAI,CAAC,GAErCS,AAZU,IAAI,CAYRQ,WAAW,CAACnB,KAAK,CAACO,EAAOI,AAZrB,IAAI,CAYuBQ,WAAW,CAACa,OAAO,GAAIR,EAAc3B,QAAQ,EAAI,CAAC,GAAI,CAAA,EAAO,WAE1G,CAgBA,SAASoC,IAEDtB,AADU,IAAI,CACRG,OAAO,GACbH,AAFU,IAAI,CAERuB,UAAU,GAGhBvB,AALU,IAAI,CAKRwB,UAAU,EAExB,CAgBA,IAAMC,EAAK/C,IACXgD,AAXwB,CAAA,CACpBC,QA7BJ,SAAiBC,CAAU,CAAEC,CAAwB,EACjD,IAAMC,EAAaF,EAAWxD,SAAS,AAClC0D,CAAAA,EAAWN,UAAU,GACtBM,EAAW3B,OAAO,CAAGL,EACrBgC,EAAWP,UAAU,CAAGhB,EACxBuB,EAAWN,UAAU,CAAGd,EACxBf,EAASiC,EAAY,SAAUN,GAC/BzB,EAAM,CAAA,EAAMgC,EAA0BhD,GAE9C,CAqBA,CAAA,EASgC8C,OAAO,CAACF,EAAEM,KAAK,CAAEN,EAAEO,cAAc,EACpC,IAAMxD,EAA2BE,IAGpD,OADYH,EAAoB,OAAU,AAE3C,CAAA"}