{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/renko\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Renko series type for Highcharts Stock\n *\n * (c) 2010-2025 Pawel Lysy\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"Series\"][\"types\"][\"column\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/renko\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"],amd1[\"Series\"],[\"types\"],[\"column\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/renko\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"Series\"][\"types\"][\"column\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"Series\"][\"types\"][\"column\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__448__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 448:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__448__;\n\n/***/ }),\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ renko_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/Renko/RenkoPoint.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { column: { prototype: { pointClass: ColumnPoint } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n/* *\n *\n *  Class\n *\n * */\nclass RenkoPoint extends ColumnPoint {\n    getClassName() {\n        return (super.getClassName.call(this) +\n            (this.upTrend ? ' highcharts-point-up' : ' highcharts-point-down'));\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Renko_RenkoPoint = (RenkoPoint);\n\n;// ./code/es-modules/Series/Renko/RenkoSeriesDefaults.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A Renko series is a style of financial chart used to describe price\n * movements over time. It displays open, high, low and close values per\n * data point.\n *\n * @sample stock/demo/renko/\n *         Renko series\n *\n *\n * @sample stock/series-renko/renko-vs-heikinashi-vs-candlestick\n *         Renko series\n *\n * @extends      plotOptions.column\n * @excluding boost, boostBlending, boostThreshold, centerInCategory,\n * cumulative, cumulativeStart, dashStyle, dragDrop, dataSorting, edgeColor,\n * stacking, getExtremesFromAll, clip, colorByPoint, compare, compareBase,\n * compareStart, compareTo, dataGrouping, edgeWidth, lineColor, linkedTo,\n * pointPadding, pointPlacement, pointRange, pointStart, pointWidth\n * @product      highstock\n * @requires     modules/renko\n * @optionparent plotOptions.renko\n */\nconst RenkoDefaults = {\n    /**\n     * The size of the individual box, representing a point. Can be set in yAxis\n     * value, or percent value of the first point e.g. if first point's value is\n     * 200, and box size is set to `20%`, the box will be 40, so the new point\n     * will be drawn when the next value changes for more than 40.\n     */\n    boxSize: 4,\n    groupPadding: 0,\n    pointPadding: 0,\n    downColor: '#ff0000',\n    navigatorOptions: {\n        type: 'renko'\n    },\n    fillColor: 'transparent',\n    borderWidth: 2,\n    lineWidth: 0,\n    stickyTracking: true,\n    borderRadius: {\n        where: 'all'\n    },\n    tooltip: {\n        pointFormat: '<span style=\"color:{point.color}\">\\u25CF</span> {series.name}: <b>{point.low:.2f} - {point.y:.2f}</b><br/>'\n    }\n};\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `renko` series. If the [type](#series.renko.type)\n * option is not specified, it is inherited from [chart.type](\n * #chart.type).\n *\n * @type      {*}\n * @extends   series,plotOptions.renko\n * @product   highstock\n * @excluding boost, compare, compareStart, connectNulls, cumulative,\n * cumulativeStart, dataGrouping, dataParser, dataSorting, dataURL,\n * dragDrop, marker, step\n * @requires  modules/renko\n * @apioption series.renko\n */\n/**\n * An array of data points for the series. For the `renko` series\n * type, points can be given in the following ways:\n *\n * 1. An array of arrays with 1 or 2 values correspond to `x,close`. If the\n * first value is a string, it is applied as the name of the point, and the\n * `x` value is inferred. The `x` value can also be omitted, in which case\n * the inner arrays should be of length 4. Then the `x` value is\n * automatically calculated, either starting at 0 and incremented by 1, or\n * from `pointStart` and `pointInterval` given in the series options.\n *    ```js\n *    data: [\n *        [0, 7],\n *        [1, 1],\n *        [2, 3]\n *    ]\n *    ```\n *\n * 2. An array of objects with named values. With renko series, the data\n * does not directly correspond to the points in the series. the reason\n * is that the points are calculated based on the trends and boxSize.\n * Setting options for individual point is impossible.\n *\n *    ```js\n *    data: [{\n *        x: 1,\n *        y: 6\n *    }, {\n *        x: 1,\n *        y: 7,\n *    }]\n *    ```\n *\n * @type      {Array<Array<number,number>|*>}\n * @extends series.column.data\n * @product highstock\n * @apioption series.renko.data\n */\n(''); // Adds doclets above to transpiled\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const RenkoSeriesDefaults = (RenkoDefaults);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Series\",\"types\",\"column\"],\"commonjs\":[\"highcharts\",\"Series\",\"types\",\"column\"],\"commonjs2\":[\"highcharts\",\"Series\",\"types\",\"column\"],\"root\":[\"Highcharts\",\"Series\",\"types\",\"column\"]}\nvar highcharts_Series_types_column_commonjs_highcharts_Series_types_column_commonjs2_highcharts_Series_types_column_root_Highcharts_Series_types_column_ = __webpack_require__(448);\nvar highcharts_Series_types_column_commonjs_highcharts_Series_types_column_commonjs2_highcharts_Series_types_column_root_Highcharts_Series_types_column_default = /*#__PURE__*/__webpack_require__.n(highcharts_Series_types_column_commonjs_highcharts_Series_types_column_commonjs2_highcharts_Series_types_column_root_Highcharts_Series_types_column_);\n;// ./code/es-modules/Series/Renko/RenkoSeries.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\n\nconst { extend, merge, relativeLength, isNumber } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The renko series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.renko\n *\n * @augments Highcharts.seriesTypes.column\n */\nclass RenkoSeries extends (highcharts_Series_types_column_commonjs_highcharts_Series_types_column_commonjs2_highcharts_Series_types_column_root_Highcharts_Series_types_column_default()) {\n    constructor() {\n        super(...arguments);\n        this.hasDerivedData = true;\n        this.allowDG = false;\n        /* *\n         *\n         *  Functions\n         *\n         * */\n    }\n    init() {\n        super.init.apply(this, arguments);\n        this.renkoData = [];\n    }\n    setData(data, redraw, animation) {\n        this.renkoData = [];\n        super.setData(data, redraw, animation, false);\n    }\n    getXExtremes(xData) {\n        this.processData();\n        xData = this.getColumn('x', true);\n        return {\n            min: xData[0],\n            max: xData[xData.length - 1]\n        };\n    }\n    getProcessedData() {\n        const modified = this.dataTable.modified;\n        const processedXData = [];\n        const processedYData = [];\n        const processedLowData = [];\n        const xData = this.getColumn('x', true);\n        const yData = this.getColumn('y', true);\n        if (!this.renkoData || this.renkoData.length > 0) {\n            return {\n                modified,\n                closestPointRange: 1,\n                cropped: false,\n                cropStart: 0\n            };\n        }\n        const boxSize = this.options.boxSize;\n        const change = isNumber(boxSize) ? boxSize : relativeLength(boxSize, yData[0]);\n        const renkoData = [], length = xData.length;\n        let prevTrend = 0;\n        let prevPrice = yData[0];\n        for (let i = 1; i < length; i++) {\n            const currentChange = yData[i] - yData[i - 1];\n            if (currentChange > change) {\n                // Uptrend\n                if (prevTrend === 2) {\n                    prevPrice += change;\n                }\n                for (let j = 0; j < currentChange / change; j++) {\n                    renkoData.push({\n                        x: xData[i] + j,\n                        low: prevPrice,\n                        y: prevPrice + change,\n                        color: this.options.color,\n                        upTrend: true\n                    });\n                    prevPrice += change;\n                }\n                prevTrend = 1;\n            }\n            else if (Math.abs(currentChange) > change) {\n                if (prevTrend === 1) {\n                    prevPrice -= change;\n                }\n                // Downtrend\n                for (let j = 0; j < Math.abs(currentChange) / change; j++) {\n                    renkoData.push({\n                        x: xData[i] + j,\n                        low: prevPrice - change,\n                        y: prevPrice,\n                        color: this.options.downColor,\n                        upTrend: false\n                    });\n                    prevPrice -= change;\n                }\n                prevTrend = 2;\n            }\n        }\n        this.renkoData = renkoData;\n        for (const point of renkoData) {\n            processedXData.push(point.x);\n            processedYData.push(point.y);\n            processedLowData.push(point.low);\n        }\n        this.processedData = renkoData;\n        modified.setColumn('x', processedXData);\n        modified.setColumn('y', processedYData);\n        modified.setColumn('low', processedLowData);\n        return {\n            modified,\n            cropped: false,\n            cropStart: 0,\n            closestPointRange: 1\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nRenkoSeries.defaultOptions = merge((highcharts_Series_types_column_commonjs_highcharts_Series_types_column_commonjs2_highcharts_Series_types_column_root_Highcharts_Series_types_column_default()).defaultOptions, RenkoSeriesDefaults);\nextend(RenkoSeries.prototype, {\n    pointClass: Renko_RenkoPoint\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('renko', RenkoSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Renko_RenkoSeries = ((/* unused pure expression or super */ null && (RenkoSeries)));\n\n;// ./code/es-modules/masters/modules/renko.js\n\n\n\n\n/* harmony default export */ const renko_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__448__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "renko_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "column", "pointClass", "ColumnPoint", "seriesTypes", "highcharts_Series_types_column_commonjs_highcharts_Series_types_column_commonjs2_highcharts_Series_types_column_root_Highcharts_Series_types_column_", "highcharts_Series_types_column_commonjs_highcharts_Series_types_column_commonjs2_highcharts_Series_types_column_root_Highcharts_Series_types_column_default", "extend", "merge", "<PERSON><PERSON><PERSON><PERSON>", "isNumber", "RenkoSeries", "constructor", "arguments", "hasDerivedData", "allowDG", "init", "apply", "renko<PERSON><PERSON>", "setData", "data", "redraw", "animation", "getXExtremes", "xData", "processData", "min", "getColumn", "max", "length", "getProcessedData", "modified", "dataTable", "processedXData", "processedYData", "processedLowData", "yData", "closestPointRange", "cropped", "cropStart", "boxSize", "options", "change", "prevTrend", "prevPrice", "i", "currentChange", "j", "push", "x", "low", "y", "color", "upTrend", "Math", "abs", "downColor", "point", "processedData", "setColumn", "defaultOptions", "groupPadding", "pointPadding", "navigatorOptions", "type", "fillColor", "borderWidth", "lineWidth", "stickyTracking", "borderRadius", "where", "tooltip", "pointFormat", "getClassName", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,MAAS,CAAC,KAAQ,CAAC,MAAS,EAC9H,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,2BAA4B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAACA,EAAK,MAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAE,GACxJ,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,2BAA2B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,MAAS,CAAC,KAAQ,CAAC,MAAS,EAE1JA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,MAAS,CAAC,KAAQ,CAAC,MAAS,CACxI,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,IACvG,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAab,OAAO,CAG5B,IAAIC,EAASS,CAAwB,CAACE,EAAS,CAAG,CAGjDZ,QAAS,CAAC,CACX,EAMA,OAHAS,CAAmB,CAACG,EAAS,CAACX,EAAQA,EAAOD,OAAO,CAAEW,GAG/CV,EAAOD,OAAO,AACtB,CAMCW,EAAoBI,CAAC,CAAG,AAACd,IACxB,IAAIe,EAASf,GAAUA,EAAOgB,UAAU,CACvC,IAAOhB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAU,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAAClB,EAASoB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACtB,EAASqB,IAC5EE,OAAOC,cAAc,CAACxB,EAASqB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAajL,GAAM,CAAEE,OAAQ,CAAET,UAAW,CAAEU,WAAYC,CAAW,CAAE,CAAE,CAAE,CAAG,AAACH,IAA2II,WAAW,CAoJtN,IAAIC,EAAuJ/B,EAAoB,KAC3KgC,EAA2KhC,EAAoBI,CAAC,CAAC2B,GAiBrM,GAAM,CAAEE,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAEC,eAAAA,CAAc,CAAEC,SAAAA,CAAQ,CAAE,CAAIZ,GAerD,OAAMa,UAAqBL,IACvBM,aAAc,CACV,KAAK,IAAIC,WACT,IAAI,CAACC,cAAc,CAAG,CAAA,EACtB,IAAI,CAACC,OAAO,CAAG,CAAA,CAMnB,CACAC,MAAO,CACH,KAAK,CAACA,KAAKC,KAAK,CAAC,IAAI,CAAEJ,WACvB,IAAI,CAACK,SAAS,CAAG,EAAE,AACvB,CACAC,QAAQC,CAAI,CAAEC,CAAM,CAAEC,CAAS,CAAE,CAC7B,IAAI,CAACJ,SAAS,CAAG,EAAE,CACnB,KAAK,CAACC,QAAQC,EAAMC,EAAQC,EAAW,CAAA,EAC3C,CACAC,aAAaC,CAAK,CAAE,CAGhB,OAFA,IAAI,CAACC,WAAW,GAET,CACHC,IAAKF,AAFTA,CAAAA,EAAQ,IAAI,CAACG,SAAS,CAAC,IAAK,CAAA,EAAI,CAElB,CAAC,EAAE,CACbC,IAAKJ,CAAK,CAACA,EAAMK,MAAM,CAAG,EAAE,AAChC,CACJ,CACAC,kBAAmB,CACf,IAAMC,EAAW,IAAI,CAACC,SAAS,CAACD,QAAQ,CAClCE,EAAiB,EAAE,CACnBC,EAAiB,EAAE,CACnBC,EAAmB,EAAE,CACrBX,EAAQ,IAAI,CAACG,SAAS,CAAC,IAAK,CAAA,GAC5BS,EAAQ,IAAI,CAACT,SAAS,CAAC,IAAK,CAAA,GAClC,GAAI,CAAC,IAAI,CAACT,SAAS,EAAI,IAAI,CAACA,SAAS,CAACW,MAAM,CAAG,EAC3C,MAAO,CACHE,SAAAA,EACAM,kBAAmB,EACnBC,QAAS,CAAA,EACTC,UAAW,CACf,EAEJ,IAAMC,EAAU,IAAI,CAACC,OAAO,CAACD,OAAO,CAC9BE,EAAShC,EAAS8B,GAAWA,EAAU/B,EAAe+B,EAASJ,CAAK,CAAC,EAAE,EACvElB,EAAY,EAAE,CAAEW,EAASL,EAAMK,MAAM,CACvCc,EAAY,EACZC,EAAYR,CAAK,CAAC,EAAE,CACxB,IAAK,IAAIS,EAAI,EAAGA,EAAIhB,EAAQgB,IAAK,CAC7B,IAAMC,EAAgBV,CAAK,CAACS,EAAE,CAAGT,CAAK,CAACS,EAAI,EAAE,CAC7C,GAAIC,EAAgBJ,EAAQ,CAEpBC,AAAc,IAAdA,GACAC,CAAAA,GAAaF,CAAK,EAEtB,IAAK,IAAIK,EAAI,EAAGA,EAAID,EAAgBJ,EAAQK,IACxC7B,EAAU8B,IAAI,CAAC,CACXC,EAAGzB,CAAK,CAACqB,EAAE,CAAGE,EACdG,IAAKN,EACLO,EAAGP,EAAYF,EACfU,MAAO,IAAI,CAACX,OAAO,CAACW,KAAK,CACzBC,QAAS,CAAA,CACb,GACAT,GAAaF,EAEjBC,EAAY,CAChB,MACK,GAAIW,KAAKC,GAAG,CAACT,GAAiBJ,EAAQ,CACnCC,AAAc,IAAdA,GACAC,CAAAA,GAAaF,CAAK,EAGtB,IAAK,IAAIK,EAAI,EAAGA,EAAIO,KAAKC,GAAG,CAACT,GAAiBJ,EAAQK,IAClD7B,EAAU8B,IAAI,CAAC,CACXC,EAAGzB,CAAK,CAACqB,EAAE,CAAGE,EACdG,IAAKN,EAAYF,EACjBS,EAAGP,EACHQ,MAAO,IAAI,CAACX,OAAO,CAACe,SAAS,CAC7BH,QAAS,CAAA,CACb,GACAT,GAAaF,EAEjBC,EAAY,CAChB,CACJ,CAEA,IAAK,IAAMc,KADX,IAAI,CAACvC,SAAS,CAAGA,EACGA,GAChBe,EAAee,IAAI,CAACS,EAAMR,CAAC,EAC3Bf,EAAec,IAAI,CAACS,EAAMN,CAAC,EAC3BhB,EAAiBa,IAAI,CAACS,EAAMP,GAAG,EAMnC,OAJA,IAAI,CAACQ,aAAa,CAAGxC,EACrBa,EAAS4B,SAAS,CAAC,IAAK1B,GACxBF,EAAS4B,SAAS,CAAC,IAAKzB,GACxBH,EAAS4B,SAAS,CAAC,MAAOxB,GACnB,CACHJ,SAAAA,EACAO,QAAS,CAAA,EACTC,UAAW,EACXF,kBAAmB,CACvB,CACJ,CACJ,CAMA1B,EAAYiD,cAAc,CAAGpD,EAAM,AAACF,IAA+JsD,cAAc,CAvO3L,CAOlBpB,QAAS,EACTqB,aAAc,EACdC,aAAc,EACdN,UAAW,UACXO,iBAAkB,CACdC,KAAM,OACV,EACAC,UAAW,cACXC,YAAa,EACbC,UAAW,EACXC,eAAgB,CAAA,EAChBC,aAAc,CACVC,MAAO,KACX,EACAC,QAAS,CACLC,YAAa,uGACjB,CACJ,GAgNAjE,EAAOI,EAAYnB,SAAS,CAAE,CAC1BU,WA5RJ,cAAyBC,EACrBsE,cAAe,CACX,OAAQ,KAAK,CAACA,aAAa/E,IAAI,CAAC,IAAI,EAC/B,CAAA,IAAI,CAAC2D,OAAO,CAAG,uBAAyB,wBAAuB,CACxE,CACJ,CAwRA,GACArD,IAA0I0E,kBAAkB,CAAC,QAAS/D,GAazI,IAAMf,EAAcE,IAGvC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}