!/**
 * Highstock JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/stock-tools
 * @requires highcharts
 * @requires highcharts/modules/stock
 *
 * Advanced Highcharts Stock tools
 *
 * (c) 2010-2025 Highsoft AS
 * Author: Torstein Honsi
 *
 * License: www.highcharts.com/license
 */function(t,i){"object"==typeof exports&&"object"==typeof module?module.exports=i(t._Highcharts,t._Highcharts.Templating,t._Highcharts.Series,t._Highcharts.AST):"function"==typeof define&&define.amd?define("highcharts/modules/stock-tools",["highcharts/highcharts"],function(t){return i(t,t.Templating,t.Series,t.AST)}):"object"==typeof exports?exports["highcharts/modules/stock-tools"]=i(t._Highcharts,t._Highcharts.Templating,t._Highcharts.Series,t._Highcharts.AST):t.Highcharts=i(t.Highcharts,t.Highcharts.Templating,t.Highcharts.Series,t.Highcharts.AST)}("undefined"==typeof window?this:window,(t,i,e,s)=>(()=>{"use strict";var n,o={660:t=>{t.exports=s},820:t=>{t.exports=e},944:i=>{i.exports=t},984:t=>{t.exports=i}},a={};function r(t){var i=a[t];if(void 0!==i)return i.exports;var e=a[t]={exports:{}};return o[t](e,e.exports,r),e.exports}r.n=t=>{var i=t&&t.__esModule?()=>t.default:()=>t;return r.d(i,{a:i}),i},r.d=(t,i)=>{for(var e in i)r.o(i,e)&&!r.o(t,e)&&Object.defineProperty(t,e,{enumerable:!0,get:i[e]})},r.o=(t,i)=>Object.prototype.hasOwnProperty.call(t,i);var l={};r.d(l,{default:()=>t6});var h=r(944),c=r.n(h);!function(t){t.compose=function(t){return t.navigation||(t.navigation=new i(t)),t};class i{constructor(t){this.updates=[],this.chart=t}addUpdate(t){this.chart.navigation.updates.push(t)}update(t,i){this.updates.forEach(e=>{e.call(this.chart,t,i)})}}t.Additions=i}(n||(n={}));let p=n;var d=r(984),g=r.n(d);let{defined:u,isNumber:m,pick:y}=c(),v={backgroundColor:"string",borderColor:"string",borderRadius:"string",color:"string",fill:"string",fontSize:"string",labels:"string",name:"string",stroke:"string",title:"string"},f={annotationsFieldsTypes:v,getAssignedAxis:function(t){return t.filter(t=>{let i=t.axis.getExtremes(),e=i.min,s=i.max,n=y(t.axis.minPointOffset,0);return m(e)&&m(s)&&t.value>=e-n&&t.value<=s+n&&!t.axis.options.isInternal})[0]},getFieldType:function(t,i){let e=v[t],s=typeof i;return u(e)&&(s=e),({string:"text",number:"number",boolean:"checkbox"})[s]}},{getAssignedAxis:x}=f,{isNumber:b,merge:A}=c(),k={lang:{navigation:{popup:{simpleShapes:"Simple shapes",lines:"Lines",circle:"Circle",ellipse:"Ellipse",rectangle:"Rectangle",label:"Label",shapeOptions:"Shape options",typeOptions:"Details",fill:"Fill",format:"Text",strokeWidth:"Line width",stroke:"Line color",title:"Title",name:"Name",labelOptions:"Label options",labels:"Labels",backgroundColor:"Background color",backgroundColors:"Background colors",borderColor:"Border color",borderRadius:"Border radius",borderWidth:"Border width",style:"Style",padding:"Padding",fontSize:"Font size",color:"Color",height:"Height",shapes:"Shape options"}}},navigation:{bindingsClassName:"highcharts-bindings-container",bindings:{circleAnnotation:{className:"highcharts-circle-annotation",start:function(t){let i=this.chart.pointer?.getCoordinates(t),e=i&&x(i.xAxis),s=i&&x(i.yAxis),n=this.chart.options.navigation;if(e&&s)return this.chart.addAnnotation(A({langKey:"circle",type:"basicAnnotation",shapes:[{type:"circle",point:{x:e.value,y:s.value,xAxis:e.axis.index,yAxis:s.axis.index},r:5}]},n.annotationsOptions,n.bindings.circleAnnotation.annotationsOptions))},steps:[function(t,i){let e,s=i.options.shapes,n=s&&s[0]&&s[0].point||{};if(b(n.xAxis)&&b(n.yAxis)){let i=this.chart.inverted,s=this.chart.xAxis[n.xAxis].toPixels(n.x),o=this.chart.yAxis[n.yAxis].toPixels(n.y);e=Math.max(Math.sqrt(Math.pow(i?o-t.chartX:s-t.chartX,2)+Math.pow(i?s-t.chartY:o-t.chartY,2)),5)}i.update({shapes:[{r:e}]})}]},ellipseAnnotation:{className:"highcharts-ellipse-annotation",start:function(t){let i=this.chart.pointer?.getCoordinates(t),e=i&&x(i.xAxis),s=i&&x(i.yAxis),n=this.chart.options.navigation;if(e&&s)return this.chart.addAnnotation(A({langKey:"ellipse",type:"basicAnnotation",shapes:[{type:"ellipse",xAxis:e.axis.index,yAxis:s.axis.index,points:[{x:e.value,y:s.value},{x:e.value,y:s.value}],ry:1}]},n.annotationsOptions,n.bindings.ellipseAnnotation.annotationsOptions))},steps:[function(t,i){let e=i.shapes[0],s=e.getAbsolutePosition(e.points[1]);e.translatePoint(t.chartX-s.x,t.chartY-s.y,1),e.redraw(!1)},function(t,i){let e=i.shapes[0],s=e.getAbsolutePosition(e.points[0]),n=e.getAbsolutePosition(e.points[1]),o=e.getDistanceFromLine(s,n,t.chartX,t.chartY),a=e.getYAxis(),r=Math.abs(a.toValue(0)-a.toValue(o));e.setYRadius(r),e.redraw(!1)}]},rectangleAnnotation:{className:"highcharts-rectangle-annotation",start:function(t){let i=this.chart.pointer?.getCoordinates(t),e=i&&x(i.xAxis),s=i&&x(i.yAxis);if(!e||!s)return;let n=e.value,o=s.value,a=e.axis.index,r=s.axis.index,l=this.chart.options.navigation;return this.chart.addAnnotation(A({langKey:"rectangle",type:"basicAnnotation",shapes:[{type:"path",points:[{xAxis:a,yAxis:r,x:n,y:o},{xAxis:a,yAxis:r,x:n,y:o},{xAxis:a,yAxis:r,x:n,y:o},{xAxis:a,yAxis:r,x:n,y:o},{command:"Z"}]}]},l.annotationsOptions,l.bindings.rectangleAnnotation.annotationsOptions))},steps:[function(t,i){let e=i.options.shapes,s=e&&e[0]&&e[0].points||[],n=this.chart.pointer?.getCoordinates(t),o=n&&x(n.xAxis),a=n&&x(n.yAxis);if(o&&a){let t=o.value,e=a.value;s[1].x=t,s[2].x=t,s[2].y=e,s[3].y=e,i.update({shapes:[{points:s}]})}}]},labelAnnotation:{className:"highcharts-label-annotation",start:function(t){let i=this.chart.pointer?.getCoordinates(t),e=i&&x(i.xAxis),s=i&&x(i.yAxis),n=this.chart.options.navigation;if(e&&s)return this.chart.addAnnotation(A({langKey:"label",type:"basicAnnotation",labelOptions:{format:"{y:.2f}",overflow:"none",crop:!0},labels:[{point:{xAxis:e.axis.index,yAxis:s.axis.index,x:e.value,y:s.value}}]},n.annotationsOptions,n.bindings.labelAnnotation.annotationsOptions))}}},events:{},annotationsOptions:{animation:{defer:0}}}},{setOptions:C}=c(),{format:w}=g(),{composed:N,doc:O,win:L}=c(),{getAssignedAxis:T,getFieldType:B}=f,{addEvent:E,attr:S,defined:I,fireEvent:z,isArray:P,isFunction:H,isNumber:Y,isObject:W,merge:R,objectEach:M,pick:X,pushUnique:U}=c();function D(){this.chart.navigationBindings&&this.chart.navigationBindings.deselectAnnotation()}function F(){this.navigationBindings&&this.navigationBindings.destroy()}function K(){let t=this.options;t&&t.navigation&&t.navigation.bindings&&(this.navigationBindings=new G(this,t.navigation),this.navigationBindings.initEvents(),this.navigationBindings.initUpdate())}function V(){let t=this.navigationBindings,i="highcharts-disabled-btn";if(this&&t){let e=!1;if(this.series.forEach(t=>{!t.options.isInternal&&t.visible&&(e=!0)}),this.navigationBindings&&this.navigationBindings.container&&this.navigationBindings.container[0]){let s=this.navigationBindings.container[0];M(t.boundClassNames,(t,n)=>{let o=s.querySelectorAll("."+n);if(o)for(let s=0;s<o.length;s++){let n=o[s],a=n.className;"normal"===t.noDataState||e?-1!==a.indexOf(i)&&n.classList.remove(i):-1===a.indexOf(i)&&(n.className+=" "+i)}})}}}function q(){this.deselectAnnotation()}function Z(){this.selectedButtonElement=null}function _(t){let i,e,s=t.prototype.defaultOptions.events&&t.prototype.defaultOptions.events.click;function n(t){let i=this,e=i.chart.navigationBindings,n=e.activeAnnotation;s&&s.call(i,t),n!==i?(e.deselectAnnotation(),e.activeAnnotation=i,i.setControlPointsVisibility(!0),z(e,"showPopup",{annotation:i,formType:"annotation-toolbar",options:e.annotationToFields(i),onSubmit:function(t){if("remove"===t.actionType)e.activeAnnotation=!1,e.chart.removeAnnotation(i);else{let s={};e.fieldsToOptions(t.fields,s),e.deselectAnnotation();let n=s.typeOptions;"measure"===i.options.type&&(n.crosshairY.enabled=0!==n.crosshairY.strokeWidth,n.crosshairX.enabled=0!==n.crosshairX.strokeWidth),i.update(s)}}})):z(e,"closePopup"),t.activeAnnotation=!0}R(!0,t.prototype.defaultOptions.events,{click:n,touchstart:function(t){i=t.touches[0].clientX,e=t.touches[0].clientY},touchend:function(t){i&&Math.sqrt(Math.pow(i-t.changedTouches[0].clientX,2)+Math.pow(e-t.changedTouches[0].clientY,2))>=4||n.call(this,t)}})}class G{static compose(t,i){U(N,"NavigationBindings")&&(E(t,"remove",D),_(t),M(t.types,t=>{_(t)}),E(i,"destroy",F),E(i,"load",K),E(i,"render",V),E(G,"closePopup",q),E(G,"deselectButton",Z),C(k))}constructor(t,i){this.boundClassNames=void 0,this.chart=t,this.options=i,this.eventsToUnbind=[],this.container=this.chart.container.getElementsByClassName(this.options.bindingsClassName||""),this.container.length||(this.container=O.getElementsByClassName(this.options.bindingsClassName||""))}getCoords(t){let i=this.chart.pointer?.getCoordinates(t);return[i&&T(i.xAxis),i&&T(i.yAxis)]}initEvents(){let t=this,i=t.chart,e=t.container,s=t.options;t.boundClassNames={},M(s.bindings||{},i=>{t.boundClassNames[i.className]=i}),[].forEach.call(e,i=>{t.eventsToUnbind.push(E(i,"click",e=>{let s=t.getButtonEvents(i,e);s&&!s.button.classList.contains("highcharts-disabled-btn")&&t.bindingsButtonClick(s.button,s.events,e)}))}),M(s.events||{},(i,e)=>{H(i)&&t.eventsToUnbind.push(E(t,e,i,{passive:!1}))}),t.eventsToUnbind.push(E(i.container,"click",function(e){!i.cancelClick&&i.isInsidePlot(e.chartX-i.plotLeft,e.chartY-i.plotTop,{visiblePlotOnly:!0})&&t.bindingsChartClick(this,e)})),t.eventsToUnbind.push(E(i.container,c().isTouchDevice?"touchmove":"mousemove",function(i){t.bindingsContainerMouseMove(this,i)},c().isTouchDevice?{passive:!1}:void 0))}initUpdate(){let t=this;p.compose(this.chart).navigation.addUpdate(i=>{t.update(i)})}bindingsButtonClick(t,i,e){let s=this.chart,n=s.renderer.boxWrapper,o=!0;this.selectedButtonElement&&(this.selectedButtonElement.classList===t.classList&&(o=!1),z(this,"deselectButton",{button:this.selectedButtonElement}),this.nextEvent&&(this.currentUserDetails&&"annotations"===this.currentUserDetails.coll&&s.removeAnnotation(this.currentUserDetails),this.mouseMoveEvent=this.nextEvent=!1)),o?(this.selectedButton=i,this.selectedButtonElement=t,z(this,"selectButton",{button:t}),i.init&&i.init.call(this,t,e),(i.start||i.steps)&&s.renderer.boxWrapper.addClass("highcharts-draw-mode")):(s.stockTools&&t.classList.remove("highcharts-active"),n.removeClass("highcharts-draw-mode"),this.nextEvent=!1,this.mouseMoveEvent=!1,this.selectedButton=null)}bindingsChartClick(t,i){t=this.chart;let e=this.activeAnnotation,s=this.selectedButton,n=t.renderer.boxWrapper;e&&(e.cancelClick||i.activeAnnotation||!i.target.parentNode||function(t,i){let e=L.Element.prototype,s=e.matches||e.msMatchesSelector||e.webkitMatchesSelector,n=null;if(e.closest)n=e.closest.call(t,i);else do{if(s.call(t,i))return t;t=t.parentElement||t.parentNode}while(null!==t&&1===t.nodeType);return n}(i.target,".highcharts-popup")?e.cancelClick&&setTimeout(()=>{e.cancelClick=!1},0):z(this,"closePopup")),s&&s.start&&(this.nextEvent?(this.nextEvent(i,this.currentUserDetails),this.steps&&(this.stepIndex++,s.steps[this.stepIndex]?this.mouseMoveEvent=this.nextEvent=s.steps[this.stepIndex]:(z(this,"deselectButton",{button:this.selectedButtonElement}),n.removeClass("highcharts-draw-mode"),s.end&&s.end.call(this,i,this.currentUserDetails),this.nextEvent=!1,this.mouseMoveEvent=!1,this.selectedButton=null))):(this.currentUserDetails=s.start.call(this,i),this.currentUserDetails&&s.steps?(this.stepIndex=0,this.steps=!0,this.mouseMoveEvent=this.nextEvent=s.steps[this.stepIndex]):(z(this,"deselectButton",{button:this.selectedButtonElement}),n.removeClass("highcharts-draw-mode"),this.steps=!1,this.selectedButton=null,s.end&&s.end.call(this,i,this.currentUserDetails))))}bindingsContainerMouseMove(t,i){this.mouseMoveEvent&&this.mouseMoveEvent(i,this.currentUserDetails)}fieldsToOptions(t,i){return M(t,(t,e)=>{let s=parseFloat(t),n=e.split("."),o=n.length-1;if(!Y(s)||t.match(/px|em/g)||e.match(/format/g)||(t=s),"undefined"!==t){let e=i;n.forEach((i,s)=>{if("__proto__"!==i&&"constructor"!==i){let a=X(n[s+1],"");o===s?e[i]=t:(e[i]||(e[i]=a.match(/\d/g)?[]:{}),e=e[i])}})}}),i}deselectAnnotation(){this.activeAnnotation&&(this.activeAnnotation.setControlPointsVisibility(!1),this.activeAnnotation=!1)}annotationToFields(t){let i=t.options,e=G.annotationsEditable,s=e.nestedOptions,n=X(i.type,i.shapes&&i.shapes[0]&&i.shapes[0].type,i.labels&&i.labels[0]&&i.labels[0].type,"label"),o=G.annotationsNonEditable[i.langKey]||[],a={langKey:i.langKey,type:n};function r(i,e,n,a,l){let h;n&&I(i)&&-1===o.indexOf(e)&&((n.indexOf&&n.indexOf(e))>=0||n[e]||!0===n)&&(P(i)?(a[e]=[],i.forEach((t,i)=>{W(t)?(a[e][i]={},M(t,(t,n)=>{r(t,n,s[e],a[e][i],e)})):r(t,0,s[e],a[e],e)})):W(i)?(h={},P(a)?(a.push(h),h[e]={},h=h[e]):a[e]=h,M(i,(t,i)=>{r(t,i,0===e?n:s[e],h,e)})):"format"===e?a[e]=[w(i,t.labels[0].points[0]).toString(),"text"]:P(a)?a.push([i,B(l,i)]):a[e]=[i,B(e,i)])}return M(i,(t,o)=>{"typeOptions"===o?(a[o]={},M(i[o],(t,i)=>{r(t,i,s,a[o],i)})):r(t,o,e[n],a,o)}),a}getClickedClassNames(t,i){let e=i.target,s=[],n;for(;e&&e.tagName&&((n=S(e,"class"))&&(s=s.concat(n.split(" ").map(t=>[t,e]))),(e=e.parentNode)!==t););return s}getButtonEvents(t,i){let e,s=this;return this.getClickedClassNames(t,i).forEach(t=>{s.boundClassNames[t[0]]&&!e&&(e={events:s.boundClassNames[t[0]],button:t[1]})}),e}update(t){this.options=R(!0,this.options,t),this.removeEvents(),this.initEvents()}removeEvents(){this.eventsToUnbind.forEach(t=>t())}destroy(){this.removeEvents()}}G.annotationsEditable={nestedOptions:{labelOptions:["style","format","backgroundColor"],labels:["style"],label:["style"],style:["fontSize","color"],background:["fill","strokeWidth","stroke"],innerBackground:["fill","strokeWidth","stroke"],outerBackground:["fill","strokeWidth","stroke"],shapeOptions:["fill","strokeWidth","stroke"],shapes:["fill","strokeWidth","stroke"],line:["strokeWidth","stroke"],backgroundColors:[!0],connector:["fill","strokeWidth","stroke"],crosshairX:["strokeWidth","stroke"],crosshairY:["strokeWidth","stroke"]},circle:["shapes"],ellipse:["shapes"],verticalLine:[],label:["labelOptions"],measure:["background","crosshairY","crosshairX"],fibonacci:[],tunnel:["background","line","height"],pitchfork:["innerBackground","outerBackground"],rect:["shapes"],crookedLine:[],basicAnnotation:["shapes","labelOptions"]},G.annotationsNonEditable={rectangle:["crosshairX","crosshairY","labelOptions"],ellipse:["labelOptions"],circle:["labelOptions"]};var j=r(820),J=r.n(j);let{getOptions:Q}=c(),{getAssignedAxis:$,getFieldType:tt}=f,{defined:ti,fireEvent:te,isNumber:ts,uniqueKey:tn}=c(),to=["apo","ad","aroon","aroonoscillator","atr","ao","cci","chaikin","cmf","cmo","disparityindex","dmi","dpo","linearRegressionAngle","linearRegressionIntercept","linearRegressionSlope","klinger","macd","mfi","momentum","natr","obv","ppo","roc","rsi","slowstochastic","stochastic","trix","williamsr"],ta=["ad","cmf","klinger","mfi","obv","vbp","vwap"];function tr(t,i){let e=i.pointer?.getCoordinates(t),s,n,o=Number.MAX_VALUE,a;if(i.navigationBindings&&e&&(s=$(e.xAxis),n=$(e.yAxis)),!s||!n)return;let r=s.value,l=n.value;if(n.axis.series.forEach(i=>{if(i.points){let e=i.searchPoint(t,!0);e&&o>Math.abs(e.x-r)&&(o=Math.abs(e.x-r),a=e)}}),a&&a.x&&a.y)return{x:a.x,y:a.y,below:l<a.y,series:a.series,xAxis:a.series.xAxis.index||0,yAxis:a.series.yAxis.index||0}}let tl={indicatorsWithAxes:to,indicatorsWithVolume:ta,addFlagFromForm:function(t){return function(i){let e=this,s=e.chart,n=s.stockTools,o=tr(i,s);if(!o)return;let a={x:o.x,y:o.y},r={type:"flags",onSeries:o.series.id,shape:t,data:[a],xAxis:o.xAxis,yAxis:o.yAxis,point:{events:{click:function(){let t=this,i=t.options;te(e,"showPopup",{point:t,formType:"annotation-toolbar",options:{langKey:"flags",type:"flags",title:[i.title,tt("title",i.title)],name:[i.name,tt("name",i.name)]},onSubmit:function(i){"remove"===i.actionType?t.remove():t.update(e.fieldsToOptions(i.fields,{}))}})}}}};n&&n.guiEnabled||s.addSeries(r),te(e,"showPopup",{formType:"flag",options:{langKey:"flags",type:"flags",title:["A",tt("label","A")],name:["Flag A",tt("label","Flag A")]},onSubmit:function(t){e.fieldsToOptions(t.fields,r.data[0]),s.addSeries(r)}})}},attractToPoint:tr,getAssignedAxis:$,isNotNavigatorYAxis:function(t){return"highcharts-navigator-yaxis"!==t.userOptions.className},isPriceIndicatorEnabled:function(t){return t.some(t=>t.lastVisiblePrice||t.lastPrice)},manageIndicators:function(t){let i,e,s,n,o=this.chart,a={linkedTo:t.linkedTo,type:t.type};if("edit"===t.actionType)this.fieldsToOptions(t.fields,a),(n=o.get(t.seriesId))&&n.update(a,!1);else if("remove"===t.actionType){if((n=o.get(t.seriesId))&&(i=n.yAxis,n.linkedSeries&&n.linkedSeries.forEach(t=>{t.remove(!1)}),n.remove(!1),to.indexOf(n.type)>=0)){let t={height:i.options.height,top:i.options.top};i.remove(!1),this.resizeYAxes(t)}}else a.id=tn(),this.fieldsToOptions(t.fields,a),e=o.get(a.linkedTo),s=Q().plotOptions,void 0!==e&&e instanceof J()&&"sum"===e.getDGApproximation()&&!ti(s&&s[a.type]&&s.dataGrouping&&s.dataGrouping.approximation)&&(a.dataGrouping={approximation:"sum"}),to.indexOf(t.type)>=0?(a.yAxis=(i=o.addAxis({id:tn(),offset:0,opposite:!0,title:{text:""},tickPixelInterval:40,showLastLabel:!1,labels:{align:"left",y:-2}},!1,!1)).options.id,this.resizeYAxes()):a.yAxis=o.get(t.linkedTo).options.yAxis,ta.indexOf(t.type)>=0&&(a.params.volumeSeriesID=o.series.filter(function(t){return"column"===t.options.type})[0].options.id),o.addSeries(a,!1);te(this,"deselectButton",{button:this.selectedButtonElement}),o.redraw()},shallowArraysEqual:function(t,i){if(!ti(t)||!ti(i)||t.length!==i.length)return!1;for(let e=0;e<t.length;e++)if(t[e]!==i[e])return!1;return!0},updateHeight:function(t,i){let e=i.options.typeOptions,s=ts(e.yAxis)&&this.chart.yAxis[e.yAxis];s&&e.points&&i.update({typeOptions:{height:s.toValue(t[s.horiz?"chartX":"chartY"])-(e.points[1].y||0)}})},updateNthPoint:function(t){return function(i,e){let s=e.options.typeOptions,n=ts(s.xAxis)&&this.chart.xAxis[s.xAxis],o=ts(s.yAxis)&&this.chart.yAxis[s.yAxis];n&&o&&(s.points.forEach((e,s)=>{s>=t&&(e.x=n.toValue(i[n.horiz?"chartX":"chartY"]),e.y=o.toValue(i[o.horiz?"chartX":"chartY"]))}),e.update({typeOptions:{points:s.points}}))}},updateRectSize:function(t,i){let e=i.chart,s=i.options.typeOptions,n=ts(s.xAxis)&&e.xAxis[s.xAxis],o=ts(s.yAxis)&&e.yAxis[s.yAxis];if(n&&o){let a=n.toValue(t[n.horiz?"chartX":"chartY"]),r=o.toValue(t[o.horiz?"chartX":"chartY"]),l=a-s.point.x,h=s.point.y-r;i.update({typeOptions:{background:{width:e.inverted?h:l,height:e.inverted?l:h}}})}}},{addFlagFromForm:th,attractToPoint:tc,isNotNavigatorYAxis:tp,isPriceIndicatorEnabled:td,manageIndicators:tg,updateHeight:tu,updateNthPoint:tm,updateRectSize:ty}=tl,{fireEvent:tv,merge:tf}=c(),tx={segment:{className:"highcharts-segment",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=this.chart.options.navigation,n=tf({langKey:"segment",type:"crookedLine",typeOptions:{xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:i.value,y:e.value},{x:i.value,y:e.value}]}},s.annotationsOptions,s.bindings?.segment.annotationsOptions);return this.chart.addAnnotation(n)},steps:[tm(1)]},arrowSegment:{className:"highcharts-arrow-segment",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=this.chart.options.navigation,n=tf({langKey:"arrowSegment",type:"crookedLine",typeOptions:{line:{markerEnd:"arrow"},xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:i.value,y:e.value},{x:i.value,y:e.value}]}},s.annotationsOptions,s.bindings?.arrowSegment.annotationsOptions);return this.chart.addAnnotation(n)},steps:[tm(1)]},ray:{className:"highcharts-ray",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=this.chart.options.navigation,n=tf({langKey:"ray",type:"infinityLine",typeOptions:{type:"ray",xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:i.value,y:e.value},{x:i.value,y:e.value}]}},s.annotationsOptions,s.bindings?.ray.annotationsOptions);return this.chart.addAnnotation(n)},steps:[tm(1)]},arrowRay:{className:"highcharts-arrow-ray",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=this.chart.options.navigation,n=tf({langKey:"arrowRay",type:"infinityLine",typeOptions:{type:"ray",line:{markerEnd:"arrow"},xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:i.value,y:e.value},{x:i.value,y:e.value}]}},s.annotationsOptions,s.bindings?.arrowRay.annotationsOptions);return this.chart.addAnnotation(n)},steps:[tm(1)]},infinityLine:{className:"highcharts-infinity-line",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=this.chart.options.navigation,n=tf({langKey:"infinityLine",type:"infinityLine",typeOptions:{type:"line",xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:i.value,y:e.value},{x:i.value,y:e.value}]}},s.annotationsOptions,s.bindings?.infinityLine.annotationsOptions);return this.chart.addAnnotation(n)},steps:[tm(1)]},arrowInfinityLine:{className:"highcharts-arrow-infinity-line",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=this.chart.options.navigation,n=tf({langKey:"arrowInfinityLine",type:"infinityLine",typeOptions:{type:"line",line:{markerEnd:"arrow"},xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:i.value,y:e.value},{x:i.value,y:e.value}]}},s.annotationsOptions,s.bindings?.arrowInfinityLine.annotationsOptions);return this.chart.addAnnotation(n)},steps:[tm(1)]},horizontalLine:{className:"highcharts-horizontal-line",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=this.chart.options.navigation,n=tf({langKey:"horizontalLine",type:"infinityLine",draggable:"y",typeOptions:{type:"horizontalLine",xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:i.value,y:e.value}]}},s.annotationsOptions,s.bindings?.horizontalLine.annotationsOptions);this.chart.addAnnotation(n)}},verticalLine:{className:"highcharts-vertical-line",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=this.chart.options.navigation,n=tf({langKey:"verticalLine",type:"infinityLine",draggable:"x",typeOptions:{type:"verticalLine",xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:i.value,y:e.value}]}},s.annotationsOptions,s.bindings?.verticalLine.annotationsOptions);this.chart.addAnnotation(n)}},crooked3:{className:"highcharts-crooked3",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=i.value,n=e.value,o=this.chart.options.navigation,a=tf({langKey:"crooked3",type:"crookedLine",typeOptions:{xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:s,y:n},{x:s,y:n},{x:s,y:n}]}},o.annotationsOptions,o.bindings?.crooked3.annotationsOptions);return this.chart.addAnnotation(a)},steps:[tm(1),tm(2)]},crooked5:{className:"highcharts-crooked5",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=i.value,n=e.value,o=this.chart.options.navigation,a=tf({langKey:"crooked5",type:"crookedLine",typeOptions:{xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:s,y:n},{x:s,y:n},{x:s,y:n},{x:s,y:n},{x:s,y:n}]}},o.annotationsOptions,o.bindings?.crooked5.annotationsOptions);return this.chart.addAnnotation(a)},steps:[tm(1),tm(2),tm(3),tm(4)]},elliott3:{className:"highcharts-elliott3",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=i.value,n=e.value,o=this.chart.options.navigation,a=tf({langKey:"elliott3",type:"elliottWave",typeOptions:{xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:s,y:n},{x:s,y:n},{x:s,y:n},{x:s,y:n}]}},o.annotationsOptions,o.bindings?.elliott3.annotationsOptions);return this.chart.addAnnotation(a)},steps:[tm(1),tm(2),tm(3)]},elliott5:{className:"highcharts-elliott5",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=i.value,n=e.value,o=this.chart.options.navigation,a=tf({langKey:"elliott5",type:"elliottWave",typeOptions:{xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:s,y:n},{x:s,y:n},{x:s,y:n},{x:s,y:n},{x:s,y:n},{x:s,y:n}]}},o.annotationsOptions,o.bindings?.elliott5.annotationsOptions);return this.chart.addAnnotation(a)},steps:[tm(1),tm(2),tm(3),tm(4),tm(5)]},measureX:{className:"highcharts-measure-x",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=i.value,n=e.value,o=this.chart.options.navigation,a=tf({langKey:"measure",type:"measure",typeOptions:{selectType:"x",xAxis:i.axis.index,yAxis:e.axis.index,point:{x:s,y:n},crosshairX:{strokeWidth:1},crosshairY:{enabled:!1,strokeWidth:0},background:{width:0,height:0}}},o.annotationsOptions,o.bindings?.measureX.annotationsOptions);return this.chart.addAnnotation(a)},steps:[ty]},measureY:{className:"highcharts-measure-y",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=i.value,n=e.value,o=this.chart.options.navigation,a=tf({langKey:"measure",type:"measure",typeOptions:{selectType:"y",xAxis:i.axis.index,yAxis:e.axis.index,point:{x:s,y:n},crosshairX:{enabled:!1,strokeWidth:0},crosshairY:{strokeWidth:1},background:{width:0,height:0,strokeWidth:0}}},o.annotationsOptions,o.bindings?.measureY.annotationsOptions);return this.chart.addAnnotation(a)},steps:[ty]},measureXY:{className:"highcharts-measure-xy",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=i.value,n=e.value,o=this.chart.options.navigation,a=tf({langKey:"measure",type:"measure",typeOptions:{selectType:"xy",xAxis:i.axis.index,yAxis:e.axis.index,point:{x:s,y:n},background:{width:0,height:0,strokeWidth:0},crosshairX:{strokeWidth:1},crosshairY:{strokeWidth:1}}},o.annotationsOptions,o.bindings?.measureXY.annotationsOptions);return this.chart.addAnnotation(a)},steps:[ty]},fibonacci:{className:"highcharts-fibonacci",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=i.value,n=e.value,o=this.chart.options.navigation,a=tf({langKey:"fibonacci",type:"fibonacci",typeOptions:{xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:s,y:n},{x:s,y:n}]}},o.annotationsOptions,o.bindings?.fibonacci.annotationsOptions);return this.chart.addAnnotation(a)},steps:[tm(1),tu]},parallelChannel:{className:"highcharts-parallel-channel",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=i.value,n=e.value,o=this.chart.options.navigation,a=tf({langKey:"parallelChannel",type:"tunnel",typeOptions:{xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:s,y:n},{x:s,y:n}]}},o.annotationsOptions,o.bindings?.parallelChannel.annotationsOptions);return this.chart.addAnnotation(a)},steps:[tm(1),tu]},pitchfork:{className:"highcharts-pitchfork",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=i.value,n=e.value,o=this.chart.options.navigation,a=tf({langKey:"pitchfork",type:"pitchfork",typeOptions:{xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:i.value,y:e.value,controlPoint:{style:{fill:"#f21313"}}},{x:s,y:n},{x:s,y:n}]}},o.annotationsOptions,o.bindings?.pitchfork.annotationsOptions);return this.chart.addAnnotation(a)},steps:[tm(1),tm(2)]},verticalCounter:{className:"highcharts-vertical-counter",start:function(t){let i=tc(t,this.chart);if(!i)return;this.verticalCounter=this.verticalCounter||0;let e=this.chart.options.navigation,s=tf({langKey:"verticalCounter",type:"verticalLine",typeOptions:{point:{x:i.x,y:i.y,xAxis:i.xAxis,yAxis:i.yAxis},label:{offset:i.below?40:-40,text:this.verticalCounter.toString()}}},e.annotationsOptions,e.bindings?.verticalCounter.annotationsOptions),n=this.chart.addAnnotation(s);this.verticalCounter++,n.options.events.click.call(n,{})}},timeCycles:{className:"highcharts-time-cycles",start:function(t){let i=tc(t,this.chart);if(!i)return;let e=this.chart.options.navigation,s=tf({langKey:"timeCycles",type:"timeCycles",typeOptions:{xAxis:i.xAxis,yAxis:i.yAxis,points:[{x:i.x},{x:i.x}]}},e.annotationsOptions,e.bindings?.timeCycles.annotationsOptions),n=this.chart.addAnnotation(s);return n.options.events.click.call(n,{}),n},steps:[tm(1)]},verticalLabel:{className:"highcharts-vertical-label",start:function(t){let i=tc(t,this.chart);if(!i)return;let e=this.chart.options.navigation,s=tf({langKey:"verticalLabel",type:"verticalLine",typeOptions:{point:{x:i.x,y:i.y,xAxis:i.xAxis,yAxis:i.yAxis},label:{offset:i.below?40:-40}}},e.annotationsOptions,e.bindings?.verticalLabel.annotationsOptions),n=this.chart.addAnnotation(s);n.options.events.click.call(n,{})}},verticalArrow:{className:"highcharts-vertical-arrow",start:function(t){let i=tc(t,this.chart);if(!i)return;let e=this.chart.options.navigation,s=tf({langKey:"verticalArrow",type:"verticalLine",typeOptions:{point:{x:i.x,y:i.y,xAxis:i.xAxis,yAxis:i.yAxis},label:{offset:i.below?40:-40,format:" "},connector:{fill:"none",stroke:i.below?"#f21313":"#06b535"}}},e.annotationsOptions,e.bindings?.verticalArrow.annotationsOptions),n=this.chart.addAnnotation(s);n.options.events.click.call(n,{})}},fibonacciTimeZones:{className:"highcharts-fibonacci-time-zones",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=this.chart.options.navigation,n=tf({type:"fibonacciTimeZones",langKey:"fibonacciTimeZones",typeOptions:{xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:i.value}]}},s.annotationsOptions,s.bindings?.fibonacciTimeZones.annotationsOptions);return this.chart.addAnnotation(n)},steps:[function(t,i){let e=i.options.typeOptions.points,s=e&&e[0].x,[n,o]=this.getCoords(t);n&&o&&i.update({typeOptions:{xAxis:n.axis.index,yAxis:o.axis.index,points:[{x:s},{x:n.value}]}})}]},flagCirclepin:{className:"highcharts-flag-circlepin",start:th("circlepin")},flagDiamondpin:{className:"highcharts-flag-diamondpin",start:th("flag")},flagSquarepin:{className:"highcharts-flag-squarepin",start:th("squarepin")},flagSimplepin:{className:"highcharts-flag-simplepin",start:th("nopin")},zoomX:{className:"highcharts-zoom-x",init:function(t){this.chart.update({chart:{zooming:{type:"x"}}}),tv(this,"deselectButton",{button:t})}},zoomY:{className:"highcharts-zoom-y",init:function(t){this.chart.update({chart:{zooming:{type:"y"}}}),tv(this,"deselectButton",{button:t})}},zoomXY:{className:"highcharts-zoom-xy",init:function(t){this.chart.update({chart:{zooming:{type:"xy"}}}),tv(this,"deselectButton",{button:t})}},seriesTypeLine:{className:"highcharts-series-type-line",init:function(t){this.chart.series[0].update({type:"line",useOhlcData:!0}),tv(this,"deselectButton",{button:t})}},seriesTypeOhlc:{className:"highcharts-series-type-ohlc",init:function(t){this.chart.series[0].update({type:"ohlc"}),tv(this,"deselectButton",{button:t})}},seriesTypeCandlestick:{className:"highcharts-series-type-candlestick",init:function(t){this.chart.series[0].update({type:"candlestick"}),tv(this,"deselectButton",{button:t})}},seriesTypeHeikinAshi:{className:"highcharts-series-type-heikinashi",init:function(t){this.chart.series[0].update({type:"heikinashi"}),tv(this,"deselectButton",{button:t})}},seriesTypeHLC:{className:"highcharts-series-type-hlc",init:function(t){this.chart.series[0].update({type:"hlc",useOhlcData:!0}),tv(this,"deselectButton",{button:t})}},seriesTypeHollowCandlestick:{className:"highcharts-series-type-hollowcandlestick",init:function(t){this.chart.series[0].update({type:"hollowcandlestick"}),tv(this,"deselectButton",{button:t})}},fullScreen:{className:"highcharts-full-screen",noDataState:"normal",init:function(t){this.chart.fullscreen&&this.chart.fullscreen.toggle(),tv(this,"deselectButton",{button:t})}},currentPriceIndicator:{className:"highcharts-current-price-indicator",init:function(t){let i=this.chart,e=i.series,s=i.stockTools,n=td(i.series);s&&s.guiEnabled&&(e.forEach(function(t){t.update({lastPrice:{enabled:!n},lastVisiblePrice:{enabled:!n,label:{enabled:!0}}},!1)}),i.redraw()),tv(this,"deselectButton",{button:t})}},indicators:{className:"highcharts-indicators",init:function(){let t=this;tv(t,"showPopup",{formType:"indicators",options:{},onSubmit:function(i){tg.call(t,i)}})}},toggleAnnotations:{className:"highcharts-toggle-annotations",init:function(t){let i=this.chart,e=i.stockTools,s=e.getIconsURL();this.toggledAnnotations=!this.toggledAnnotations,(i.annotations||[]).forEach(function(t){t.setVisibility(!this.toggledAnnotations)},this),e&&e.guiEnabled&&(this.toggledAnnotations?t.firstChild.style["background-image"]='url("'+s+'annotations-hidden.svg")':t.firstChild.style["background-image"]='url("'+s+'annotations-visible.svg")'),tv(this,"deselectButton",{button:t})}},saveChart:{className:"highcharts-save-chart",noDataState:"normal",init:function(t){let i=this.chart,e=[],s=[],n=[],o=[];i.annotations.forEach(function(t,i){e[i]=t.userOptions}),i.series.forEach(function(t){t.is("sma")?s.push(t.userOptions):"flags"===t.type&&n.push(t.userOptions)}),i.yAxis.forEach(function(t){tp(t)&&o.push(t.options)}),c().win.localStorage.setItem("highcharts-chart",JSON.stringify({annotations:e,indicators:s,flags:n,yAxes:o})),tv(this,"deselectButton",{button:t})}}},tb={lang:{stockTools:{gui:{simpleShapes:"Simple shapes",lines:"Lines",crookedLines:"Crooked lines",measure:"Measure",advanced:"Advanced",toggleAnnotations:"Toggle annotations",verticalLabels:"Vertical labels",flags:"Flags",zoomChange:"Zoom change",typeChange:"Type change",saveChart:"Save chart",indicators:"Indicators",currentPriceIndicator:"Current Price Indicators",zoomX:"Zoom X",zoomY:"Zoom Y",zoomXY:"Zooom XY",fullScreen:"Fullscreen",typeOHLC:"OHLC",typeLine:"Line",typeCandlestick:"Candlestick",typeHLC:"HLC",typeHollowCandlestick:"Hollow Candlestick",typeHeikinAshi:"Heikin Ashi",circle:"Circle",ellipse:"Ellipse",label:"Label",rectangle:"Rectangle",flagCirclepin:"Flag circle",flagDiamondpin:"Flag diamond",flagSquarepin:"Flag square",flagSimplepin:"Flag simple",measureXY:"Measure XY",measureX:"Measure X",measureY:"Measure Y",segment:"Segment",arrowSegment:"Arrow segment",ray:"Ray",arrowRay:"Arrow ray",line:"Line",arrowInfinityLine:"Arrow line",horizontalLine:"Horizontal line",verticalLine:"Vertical line",infinityLine:"Infinity line",crooked3:"Crooked 3 line",crooked5:"Crooked 5 line",elliott3:"Elliott 3 line",elliott5:"Elliott 5 line",verticalCounter:"Vertical counter",verticalLabel:"Vertical label",verticalArrow:"Vertical arrow",fibonacci:"Fibonacci",fibonacciTimeZones:"Fibonacci Time Zones",pitchfork:"Pitchfork",parallelChannel:"Parallel channel",timeCycles:"Time Cycles"}},navigation:{popup:{circle:"Circle",ellipse:"Ellipse",rectangle:"Rectangle",label:"Label",segment:"Segment",arrowSegment:"Arrow segment",ray:"Ray",arrowRay:"Arrow ray",line:"Line",arrowInfinityLine:"Arrow line",horizontalLine:"Horizontal line",verticalLine:"Vertical line",crooked3:"Crooked 3 line",crooked5:"Crooked 5 line",elliott3:"Elliott 3 line",elliott5:"Elliott 5 line",verticalCounter:"Vertical counter",verticalLabel:"Vertical label",verticalArrow:"Vertical arrow",fibonacci:"Fibonacci",fibonacciTimeZones:"Fibonacci Time Zones",pitchfork:"Pitchfork",parallelChannel:"Parallel channel",infinityLine:"Infinity line",measure:"Measure",measureXY:"Measure XY",measureX:"Measure X",measureY:"Measure Y",timeCycles:"Time Cycles",flags:"Flags",addButton:"Add",saveButton:"Save",editButton:"Edit",removeButton:"Remove",series:"Series",volume:"Volume",connector:"Connector",innerBackground:"Inner background",outerBackground:"Outer background",crosshairX:"Crosshair X",crosshairY:"Crosshair Y",tunnel:"Tunnel",background:"Background",noFilterMatch:"No match",searchIndicators:"Search Indicators",clearFilter:"✕ clear filter",index:"Index",period:"Period",periods:"Periods",standardDeviation:"Standard deviation",periodTenkan:"Tenkan period",periodSenkouSpanB:"Senkou Span B period",periodATR:"ATR period",multiplierATR:"ATR multiplier",shortPeriod:"Short period",longPeriod:"Long period",signalPeriod:"Signal period",decimals:"Decimals",algorithm:"Algorithm",topBand:"Top band",bottomBand:"Bottom band",initialAccelerationFactor:"Initial acceleration factor",maxAccelerationFactor:"Max acceleration factor",increment:"Increment",multiplier:"Multiplier",ranges:"Ranges",highIndex:"High index",lowIndex:"Low index",deviation:"Deviation",xAxisUnit:"x-axis unit",factor:"Factor",fastAvgPeriod:"Fast average period",slowAvgPeriod:"Slow average period",average:"Average",indicatorAliases:{abands:["Acceleration Bands"],bb:["Bollinger Bands"],dema:["Double Exponential Moving Average"],ema:["Exponential Moving Average"],ikh:["Ichimoku Kinko Hyo"],keltnerchannels:["Keltner Channels"],linearRegression:["Linear Regression"],pivotpoints:["Pivot Points"],pc:["Price Channel"],priceenvelopes:["Price Envelopes"],psar:["Parabolic SAR"],sma:["Simple Moving Average"],supertrend:["Super Trend"],tema:["Triple Exponential Moving Average"],vbp:["Volume by Price"],vwap:["Volume Weighted Moving Average"],wma:["Weighted Moving Average"],zigzag:["Zig Zag"],apo:["Absolute price indicator"],ad:["Accumulation/Distribution"],aroon:["Aroon"],aroonoscillator:["Aroon oscillator"],atr:["Average True Range"],ao:["Awesome oscillator"],cci:["Commodity Channel Index"],chaikin:["Chaikin"],cmf:["Chaikin Money Flow"],cmo:["Chande Momentum Oscillator"],disparityindex:["Disparity Index"],dmi:["Directional Movement Index"],dpo:["Detrended price oscillator"],klinger:["Klinger Oscillator"],linearRegressionAngle:["Linear Regression Angle"],linearRegressionIntercept:["Linear Regression Intercept"],linearRegressionSlope:["Linear Regression Slope"],macd:["Moving Average Convergence Divergence"],mfi:["Money Flow Index"],momentum:["Momentum"],natr:["Normalized Average True Range"],obv:["On-Balance Volume"],ppo:["Percentage Price oscillator"],roc:["Rate of Change"],rsi:["Relative Strength Index"],slowstochastic:["Slow Stochastic"],stochastic:["Stochastic"],trix:["TRIX"],williamsr:["Williams %R"]}}}},stockTools:{gui:{enabled:!0,className:"highcharts-bindings-wrapper",toolbarClassName:"stocktools-toolbar",buttons:["indicators","separator","simpleShapes","lines","crookedLines","measure","advanced","toggleAnnotations","separator","verticalLabels","flags","separator","zoomChange","fullScreen","typeChange","separator","currentPriceIndicator","saveChart"],definitions:{separator:{elementType:"span",symbol:"separator.svg"},simpleShapes:{items:["label","circle","ellipse","rectangle"],circle:{symbol:"circle.svg"},ellipse:{symbol:"ellipse.svg"},rectangle:{symbol:"rectangle.svg"},label:{symbol:"label.svg"}},flags:{items:["flagCirclepin","flagDiamondpin","flagSquarepin","flagSimplepin"],flagSimplepin:{symbol:"flag-basic.svg"},flagDiamondpin:{symbol:"flag-diamond.svg"},flagSquarepin:{symbol:"flag-trapeze.svg"},flagCirclepin:{symbol:"flag-elipse.svg"}},lines:{items:["segment","arrowSegment","ray","arrowRay","line","arrowInfinityLine","horizontalLine","verticalLine"],segment:{symbol:"segment.svg"},arrowSegment:{symbol:"arrow-segment.svg"},ray:{symbol:"ray.svg"},arrowRay:{symbol:"arrow-ray.svg"},line:{symbol:"line.svg"},arrowInfinityLine:{symbol:"arrow-line.svg"},verticalLine:{symbol:"vertical-line.svg"},horizontalLine:{symbol:"horizontal-line.svg"}},crookedLines:{items:["elliott3","elliott5","crooked3","crooked5"],crooked3:{symbol:"crooked-3.svg"},crooked5:{symbol:"crooked-5.svg"},elliott3:{symbol:"elliott-3.svg"},elliott5:{symbol:"elliott-5.svg"}},verticalLabels:{items:["verticalCounter","verticalLabel","verticalArrow"],verticalCounter:{symbol:"vertical-counter.svg"},verticalLabel:{symbol:"vertical-label.svg"},verticalArrow:{symbol:"vertical-arrow.svg"}},advanced:{items:["fibonacci","fibonacciTimeZones","pitchfork","parallelChannel","timeCycles"],pitchfork:{symbol:"pitchfork.svg"},fibonacci:{symbol:"fibonacci.svg"},fibonacciTimeZones:{symbol:"fibonacci-timezone.svg"},parallelChannel:{symbol:"parallel-channel.svg"},timeCycles:{symbol:"time-cycles.svg"}},measure:{items:["measureXY","measureX","measureY"],measureX:{symbol:"measure-x.svg"},measureY:{symbol:"measure-y.svg"},measureXY:{symbol:"measure-xy.svg"}},toggleAnnotations:{symbol:"annotations-visible.svg"},currentPriceIndicator:{symbol:"current-price-show.svg"},indicators:{symbol:"indicators.svg"},zoomChange:{items:["zoomX","zoomY","zoomXY"],zoomX:{symbol:"zoom-x.svg"},zoomY:{symbol:"zoom-y.svg"},zoomXY:{symbol:"zoom-xy.svg"}},typeChange:{items:["typeOHLC","typeLine","typeCandlestick","typeHollowCandlestick","typeHLC","typeHeikinAshi"],typeOHLC:{symbol:"series-ohlc.svg"},typeLine:{symbol:"series-line.svg"},typeCandlestick:{symbol:"series-candlestick.svg"},typeHLC:{symbol:"series-hlc.svg"},typeHeikinAshi:{symbol:"series-heikin-ashi.svg"},typeHollowCandlestick:{symbol:"series-hollow-candlestick.svg"}},fullScreen:{symbol:"fullscreen.svg"},saveChart:{symbol:"save-chart.svg"}},visible:!0}}},{setOptions:tA}=c(),{getAssignedAxis:tk}=f,{isNotNavigatorYAxis:tC,isPriceIndicatorEnabled:tw}=tl,{correctFloat:tN,defined:tO,isNumber:tL,pick:tT}=c();function tB(t,i,e,s){let n=0,o,a,r;function l(t){return tO(t)&&!tL(t)&&t.match("%")}return s&&(r=tN(parseFloat(s.top)/100),a=tN(parseFloat(s.height)/100)),{positions:t.map((s,h)=>{let c=tN(l(s.options.height)?parseFloat(s.options.height)/100:s.height/i),p=tN(l(s.options.top)?parseFloat(s.options.top)/100:(s.top-s.chart.plotTop)/i);return a?(p>r&&(p-=a),n=Math.max(n,(p||0)+(c||0))):(tL(c)||(c=t[h-1].series.every(t=>t.is("sma"))?o:e/100),tL(p)||(p=n),o=c,n=tN(Math.max(n,(p||0)+(c||0)))),{height:100*c,top:100*p}}),allAxesHeight:n}}function tE(t){let i=[];return t.forEach(function(e,s){let n=t[s+1];n?i[s]={enabled:!0,controlledAxis:{next:[tT(n.options.id,n.index)]}}:i[s]={enabled:!1}}),i}function tS(t,i,e,s){return t.forEach(function(n,o){let a=t[o-1];n.top=a?tN(a.height+a.top):0,e&&(n.height=tN(n.height+s*i))}),t}function tI(t){let i=this.chart,e=i.yAxis.filter(tC),s=i.plotHeight,{positions:n,allAxesHeight:o}=this.getYAxisPositions(e,s,20,t),a=this.getYAxisResizers(e);!t&&o<=tN(1)?n[n.length-1]={height:20,top:tN(100*o-20)}:n.forEach(function(t){t.height=t.height/(100*o)*100,t.top=t.top/(100*o)*100}),n.forEach(function(t,i){e[i].update({height:t.height+"%",top:t.top+"%",resize:a[i],offset:0},!1)})}var tz=r(660),tP=r.n(tz);let{addEvent:tH,createElement:tY,css:tW,defined:tR,fireEvent:tM,getStyle:tX,isArray:tU,merge:tD,pick:tF}=c(),{shallowArraysEqual:tK}=tl;class tV{constructor(t,i,e){this.width=0,this.isDirty=!1,this.chart=e,this.options=t,this.lang=i,this.iconsURL=this.getIconsURL(),this.guiEnabled=t.enabled,this.visible=tF(t.visible,!0),this.guiClassName=t.className,this.toolbarClassName=t.toolbarClassName,this.eventsToUnbind=[],this.guiEnabled&&(this.createContainer(),this.createButtons(),this.showHideNavigation()),tM(this,"afterInit")}createButtons(){let t=this.lang,i=this.options,e=this.toolbar,s=i.buttons,n=i.definitions,o=e.childNodes;this.buttonList=s,s.forEach(i=>{let s=this.addButton(e,n,i,t);this.eventsToUnbind.push(tH(s.buttonWrapper,"click",()=>this.eraseActiveButtons(o,s.buttonWrapper))),tU(n[i].items)&&this.addSubmenu(s,n[i])})}addSubmenu(t,i){let e=t.submenuArrow,s=t.buttonWrapper,n=tX(s,"width"),o=this.wrapper,a=this.listWrapper,r=this.toolbar.childNodes,l=this.submenu=tY("ul",{className:"highcharts-submenu-wrapper"},void 0,s);this.addSubmenuItems(s,i),this.eventsToUnbind.push(tH(e,"click",t=>{if(t.stopPropagation(),this.eraseActiveButtons(r,s),s.className.indexOf("highcharts-current")>=0)a.style.width=a.startWidth+"px",s.classList.remove("highcharts-current"),l.style.display="none";else{l.style.display="block";let t=l.offsetHeight-s.offsetHeight-3;l.offsetHeight+s.offsetTop>o.offsetHeight&&s.offsetTop>t||(t=0),tW(l,{top:-t+"px",left:n+3+"px"}),s.className+=" highcharts-current",a.startWidth=o.offsetWidth,a.style.width=a.startWidth+tX(a,"padding-left")+l.offsetWidth+3+"px"}}))}addSubmenuItems(t,i){let e,s=this,n=this.submenu,o=this.lang,a=this.listWrapper;i.items.forEach(r=>{e=this.addButton(n,i,r,o),this.eventsToUnbind.push(tH(e.mainButton,"click",function(){s.switchSymbol(this,t,!0),a.style.width=a.startWidth+"px",n.style.display="none"}))});let r=n.querySelectorAll("li > .highcharts-menu-item-btn")[0];this.switchSymbol(r,!1)}eraseActiveButtons(t,i,e){[].forEach.call(t,t=>{t!==i&&(t.classList.remove("highcharts-current"),t.classList.remove("highcharts-active"),(e=t.querySelectorAll(".highcharts-submenu-wrapper")).length>0&&(e[0].style.display="none"))})}addButton(t,i,e,s={}){let n=i[e],o=n.items,a=tV.prototype.classMapping,r=n.className||"",l=tY("li",{className:tF(a[e],"")+" "+r,title:s[e]||e},void 0,t),h=tY(n.elementType||"button",{className:"highcharts-menu-item-btn"},void 0,l);if(o&&o.length){let t=tY("button",{className:"highcharts-submenu-item-arrow highcharts-arrow-right"},void 0,l);return t.style.backgroundImage="url("+this.iconsURL+"arrow-bottom.svg)",{buttonWrapper:l,mainButton:h,submenuArrow:t}}return h.style.backgroundImage="url("+this.iconsURL+n.symbol+")",{buttonWrapper:l,mainButton:h}}addNavigation(){let t=this.wrapper;this.arrowWrapper=tY("div",{className:"highcharts-arrow-wrapper"}),this.arrowUp=tY("div",{className:"highcharts-arrow-up"},void 0,this.arrowWrapper),this.arrowUp.style.backgroundImage="url("+this.iconsURL+"arrow-right.svg)",this.arrowDown=tY("div",{className:"highcharts-arrow-down"},void 0,this.arrowWrapper),this.arrowDown.style.backgroundImage="url("+this.iconsURL+"arrow-right.svg)",t.insertBefore(this.arrowWrapper,t.childNodes[0]),this.scrollButtons()}scrollButtons(){let t=this.wrapper,i=this.toolbar,e=.1*t.offsetHeight,s=0;this.eventsToUnbind.push(tH(this.arrowUp,"click",()=>{s>0&&(s-=e,i.style.marginTop=-s+"px")})),this.eventsToUnbind.push(tH(this.arrowDown,"click",()=>{t.offsetHeight+s<=i.offsetHeight+e&&(s+=e,i.style.marginTop=-s+"px")}))}createContainer(){let t,i,e=this.chart,s=this.options,n=e.container,o=e.options.navigation,a=o?.bindingsClassName,r=this,l=this.wrapper=tY("div",{className:"highcharts-stocktools-wrapper "+s.className+" "+a});n.appendChild(l),this.showHideBtn=tY("div",{className:"highcharts-toggle-toolbar highcharts-arrow-left"},void 0,l),this.eventsToUnbind.push(tH(this.showHideBtn,"click",()=>{this.update({gui:{visible:!r.visible}})})),["mousedown","mousemove","click","touchstart"].forEach(t=>{tH(l,t,t=>t.stopPropagation())}),tH(l,"mouseover",t=>e.pointer?.onContainerMouseLeave(t)),this.toolbar=i=tY("ul",{className:"highcharts-stocktools-toolbar "+s.toolbarClassName}),this.listWrapper=t=tY("div",{className:"highcharts-menu-wrapper"}),l.insertBefore(t,l.childNodes[0]),t.insertBefore(i,t.childNodes[0]),this.showHideToolbar(),this.addNavigation()}showHideNavigation(){this.visible&&this.toolbar.offsetHeight>this.wrapper.offsetHeight-50?this.arrowWrapper.style.display="block":(this.toolbar.style.marginTop="0px",this.arrowWrapper.style.display="none")}showHideToolbar(){let t=this.wrapper,i=this.listWrapper,e=this.submenu,s=this.showHideBtn,n=this.visible;s.style.backgroundImage="url("+this.iconsURL+"arrow-right.svg)",n?(t.style.height="100%",i.classList.remove("highcharts-hide"),s.classList.remove("highcharts-arrow-right"),s.style.top=tX(i,"padding-top")+"px",s.style.left=t.offsetWidth+tX(i,"padding-left")+"px"):(e&&(e.style.display="none"),s.style.left="0px",n=this.visible=!1,i.classList.add("highcharts-hide"),s.classList.add("highcharts-arrow-right"),t.style.height=s.offsetHeight+"px")}switchSymbol(t,i){let e=t.parentNode,s=e.className,n=e.parentNode.parentNode;!(s.indexOf("highcharts-disabled-btn")>-1)&&(n.className="",s&&n.classList.add(s.trim()),n.querySelectorAll(".highcharts-menu-item-btn")[0].style.backgroundImage=t.style.backgroundImage,i&&this.toggleButtonActiveClass(n))}toggleButtonActiveClass(t){let i=t.classList;i.contains("highcharts-active")?i.remove("highcharts-active"):i.add("highcharts-active")}unselectAllButtons(t){let i=t.parentNode.querySelectorAll(".highcharts-active");[].forEach.call(i,i=>{i!==t&&i.classList.remove("highcharts-active")})}update(t,i){this.isDirty=!!t.gui.definitions,tD(!0,this.chart.options.stockTools,t),tD(!0,this.options,t.gui),this.visible=tF(this.options.visible&&this.options.enabled,!0),this.chart.navigationBindings&&this.chart.navigationBindings.update(),this.chart.isDirtyBox=!0,tF(i,!0)&&this.chart.redraw()}destroy(){let t=this.wrapper,i=t&&t.parentNode;this.eventsToUnbind.forEach(t=>t()),i&&i.removeChild(t)}redraw(){if(this.options.enabled!==this.guiEnabled)this.handleGuiEnabledChange();else{if(!this.guiEnabled)return;this.updateClassNames(),this.updateButtons(),this.updateVisibility(),this.showHideNavigation(),this.showHideToolbar()}}handleGuiEnabledChange(){!1===this.options.enabled&&(this.destroy(),this.visible=!1),!0===this.options.enabled&&(this.createContainer(),this.createButtons()),this.guiEnabled=this.options.enabled}updateClassNames(){this.options.className!==this.guiClassName&&(this.guiClassName&&this.wrapper.classList.remove(this.guiClassName),this.options.className&&this.wrapper.classList.add(this.options.className),this.guiClassName=this.options.className),this.options.toolbarClassName!==this.toolbarClassName&&(this.toolbarClassName&&this.toolbar.classList.remove(this.toolbarClassName),this.options.toolbarClassName&&this.toolbar.classList.add(this.options.toolbarClassName),this.toolbarClassName=this.options.toolbarClassName)}updateButtons(){(!tK(this.options.buttons,this.buttonList)||this.isDirty)&&(this.toolbar.innerHTML=tP().emptyHTML,this.createButtons())}updateVisibility(){tR(this.options.visible)&&(this.visible=this.options.visible)}getIconsURL(){return this.chart.options.navigation.iconsURL||this.options.iconsURL||"https://code.highcharts.com/12.3.0/gfx/stock-icons/"}}tV.prototype.classMapping={circle:"highcharts-circle-annotation",ellipse:"highcharts-ellipse-annotation",rectangle:"highcharts-rectangle-annotation",label:"highcharts-label-annotation",segment:"highcharts-segment",arrowSegment:"highcharts-arrow-segment",ray:"highcharts-ray",arrowRay:"highcharts-arrow-ray",line:"highcharts-infinity-line",arrowInfinityLine:"highcharts-arrow-infinity-line",verticalLine:"highcharts-vertical-line",horizontalLine:"highcharts-horizontal-line",crooked3:"highcharts-crooked3",crooked5:"highcharts-crooked5",elliott3:"highcharts-elliott3",elliott5:"highcharts-elliott5",pitchfork:"highcharts-pitchfork",fibonacci:"highcharts-fibonacci",fibonacciTimeZones:"highcharts-fibonacci-time-zones",parallelChannel:"highcharts-parallel-channel",measureX:"highcharts-measure-x",measureY:"highcharts-measure-y",measureXY:"highcharts-measure-xy",timeCycles:"highcharts-time-cycles",verticalCounter:"highcharts-vertical-counter",verticalLabel:"highcharts-vertical-label",verticalArrow:"highcharts-vertical-arrow",currentPriceIndicator:"highcharts-current-price-indicator",indicators:"highcharts-indicators",flagCirclepin:"highcharts-flag-circlepin",flagDiamondpin:"highcharts-flag-diamondpin",flagSquarepin:"highcharts-flag-squarepin",flagSimplepin:"highcharts-flag-simplepin",zoomX:"highcharts-zoom-x",zoomY:"highcharts-zoom-y",zoomXY:"highcharts-zoom-xy",typeLine:"highcharts-series-type-line",typeOHLC:"highcharts-series-type-ohlc",typeHLC:"highcharts-series-type-hlc",typeCandlestick:"highcharts-series-type-candlestick",typeHollowCandlestick:"highcharts-series-type-hollowcandlestick",typeHeikinAshi:"highcharts-series-type-heikinashi",fullScreen:"highcharts-full-screen",toggleAnnotations:"highcharts-toggle-annotations",saveChart:"highcharts-save-chart",separator:"highcharts-separator"};let{setOptions:tq}=c(),{addEvent:tZ,getStyle:t_,merge:tG,pick:tj}=c();function tJ(t){let i=this.options,e=i.lang,s=tG(i.stockTools&&i.stockTools.gui,t&&t.gui),n=e&&e.stockTools&&e.stockTools.gui;this.stockTools=new tV(s,n,this),this.stockTools.guiEnabled&&(this.isDirtyBox=!0)}function tQ(){this.setStockTools()}function t$(){this.stockTools&&(this.stockTools.redraw(),function(t){if(t.stockTools?.guiEnabled){let i=t.options.chart,e=t.stockTools.listWrapper,s=e&&(e.startWidth+t_(e,"padding-left")+t_(e,"padding-right")||e.offsetWidth);t.stockTools.width=s;let n=!1;if(s<t.plotWidth){let e=tj(i.spacingLeft,i.spacing&&i.spacing[3],0)+s,o=e-t.spacingBox.x;t.spacingBox.x=e,t.spacingBox.width-=o,n=!0}else 0===s&&(n=!0);s!==t.stockTools.prevOffsetWidth&&(t.stockTools.prevOffsetWidth=s,n&&(t.isDirtyLegend=!0))}}(this))}function t0(){this.stockTools&&this.stockTools.destroy()}function t1(){let t=this.stockTools?.visible&&this.stockTools.guiEnabled?this.stockTools.width:0;t&&t<this.plotWidth&&(this.plotLeft+=t,this.spacing[3]+=t)}function t3(){let t=this.stockTools,i=t&&t.toolbar&&t.toolbar.querySelector(".highcharts-current-price-indicator");t&&this.navigationBindings&&this.options.series&&i&&(this.navigationBindings.utils?.isPriceIndicatorEnabled?.(this.series)?i.firstChild.style["background-image"]='url("'+t.getIconsURL()+'current-price-hide.svg")':i.firstChild.style["background-image"]='url("'+t.getIconsURL()+'current-price-show.svg")')}function t5(t){let i=this.chart.stockTools;if(i&&i.guiEnabled){let i=t.button;i.parentNode.className.indexOf("highcharts-submenu-wrapper")>=0&&(i=i.parentNode.parentNode),i.classList.remove("highcharts-active")}}function t2(t){let i=this.chart.stockTools;if(i&&i.guiEnabled){let e=t.button;i.unselectAllButtons(t.button),e.parentNode.className.indexOf("highcharts-submenu-wrapper")>=0&&(e=e.parentNode.parentNode),i.toggleButtonActiveClass(e)}}let t4=c();t4.NavigationBindings=t4.NavigationBindings||G,t4.Toolbar=tV,({compose:function(t){let i=t.prototype;i.utils?.manageIndicators||(i.getYAxisPositions=tB,i.getYAxisResizers=tE,i.recalculateYAxisPositions=tS,i.resizeYAxes=tI,i.utils=i.utils||{},i.utils.indicatorsWithAxes=tl.indicatorsWithAxes,i.utils.indicatorsWithVolume=tl.indicatorsWithVolume,i.utils.getAssignedAxis=tk,i.utils.isPriceIndicatorEnabled=tw,i.utils.manageIndicators=tl.manageIndicators,tA(tb),tA({navigation:{bindings:tx}}))}}).compose(t4.NavigationBindings),({compose:function(t,i){let e=t.prototype;e.setStockTools||(tZ(t,"afterGetContainer",tQ),tZ(t,"beforeRedraw",t$),tZ(t,"beforeRender",t$),tZ(t,"destroy",t0),tZ(t,"getMargins",t1,{order:0}),tZ(t,"render",t3),e.setStockTools=tJ,tZ(i,"deselectButton",t5),tZ(i,"selectButton",t2),tq(tb))}}).compose(t4.Chart,t4.NavigationBindings);let t6=c();return l.default})());