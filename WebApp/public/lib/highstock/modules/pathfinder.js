!/**
 * Highcharts Gantt JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/pathfinder
 * @requires highcharts
 *
 * Pathfinder
 *
 * (c) 2016-2025 Øystein Moseng
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.Point):"function"==typeof define&&define.amd?define("highcharts/modules/pathfinder",["highcharts/highcharts"],function(t){return e(t,t.Point)}):"object"==typeof exports?exports["highcharts/modules/pathfinder"]=e(t._Highcharts,t._Highcharts.Point):t.Highcharts=e(t.Highcharts,t.Highcharts.Point)}("undefined"==typeof window?this:window,(t,e)=>(()=>{"use strict";var n,i={260:t=>{t.exports=e},944:e=>{e.exports=t}},r={};function a(t){var e=r[t];if(void 0!==e)return e.exports;var n=r[t]={exports:{}};return i[t](n,n.exports,a),n.exports}a.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return a.d(e,{a:e}),e},a.d=(t,e)=>{for(var n in e)a.o(e,n)&&!a.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},a.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var o={};a.d(o,{default:()=>F});var s=a(944),h=a.n(s);let{defined:c,error:l,merge:x,objectEach:M}=h(),d=h().deg2rad,y=Math.max,p=Math.min,f=class{constructor(t,e,n){this.init(t,e,n)}init(t,e,n){this.fromPoint=t,this.toPoint=e,this.options=n,this.chart=t.series.chart,this.pathfinder=this.chart.pathfinder}renderPath(t,e){let n=this.chart,i=n.styledMode,r=this.pathfinder,a={},o=this.graphics&&this.graphics.path;r.group||(r.group=n.renderer.g().addClass("highcharts-pathfinder-group").attr({zIndex:-1}).add(n.seriesGroup)),r.group.translate(n.plotLeft,n.plotTop),!(o&&o.renderer)&&(o=n.renderer.path().add(r.group),i||o.attr({opacity:0})),o.attr(e),a.d=t,i||(a.opacity=1),o.animate(a),this.graphics=this.graphics||{},this.graphics.path=o}addMarker(t,e,n){let i,r,a,o,s,h,c,l=this.fromPoint.series.chart,x=l.pathfinder,M=l.renderer,y="start"===t?this.fromPoint:this.toPoint,p=y.getPathfinderAnchorPoint(e);e.enabled&&((c="start"===t?n[1]:n[n.length-2])&&"M"===c[0]||"L"===c[0])&&(h={x:c[1],y:c[2]},r=y.getRadiansToVector(h,p),i=y.getMarkerVector(r,e.radius,p),e.width&&e.height?(o=e.width,s=e.height):o=s=2*e.radius,this.graphics=this.graphics||{},a={x:i.x-o/2,y:i.y-s/2,width:o,height:s,rotation:-r/d,rotationOriginX:i.x,rotationOriginY:i.y},this.graphics[t]?this.graphics[t].animate(a):(this.graphics[t]=M.symbol(e.symbol).addClass("highcharts-point-connecting-path-"+t+"-marker highcharts-color-"+this.fromPoint.colorIndex).attr(a).add(x.group),M.styledMode||this.graphics[t].attr({fill:e.color||this.fromPoint.color,stroke:e.lineColor,"stroke-width":e.lineWidth,opacity:0}).animate({opacity:1},y.series.options.animation)))}getPath(t){let e=this.pathfinder,n=this.chart,i=e.algorithms[t.type],r=e.chartObstacles;return"function"!=typeof i?(l('"'+t.type+'" is not a Pathfinder algorithm.'),{path:[],obstacles:[]}):(i.requiresObstacles&&!r&&(r=e.chartObstacles=e.getChartObstacles(t),n.options.connectors.algorithmMargin=t.algorithmMargin,e.chartObstacleMetrics=e.getObstacleMetrics(r)),i(this.fromPoint.getPathfinderAnchorPoint(t.startMarker),this.toPoint.getPathfinderAnchorPoint(t.endMarker),x({chartObstacles:r,lineObstacles:e.lineObstacles||[],obstacleMetrics:e.chartObstacleMetrics,hardBounds:{xMin:0,xMax:n.plotWidth,yMin:0,yMax:n.plotHeight},obstacleOptions:{margin:t.algorithmMargin},startDirectionX:e.getAlgorithmStartDirection(t.startMarker)},t)))}render(){let t=this.fromPoint,e=t.series,n=e.chart,i=n.pathfinder,r={},a=x(n.options.connectors,e.options.connectors,t.options.connectors,this.options);!n.styledMode&&(r.stroke=a.lineColor||t.color,r["stroke-width"]=a.lineWidth,a.dashStyle&&(r.dashstyle=a.dashStyle)),r.class="highcharts-point-connecting-path highcharts-color-"+t.colorIndex,c((a=x(r,a)).marker.radius)||(a.marker.radius=p(y(Math.ceil((a.algorithmMargin||8)/2)-1,1),5));let o=this.getPath(a),s=o.path;o.obstacles&&(i.lineObstacles=i.lineObstacles||[],i.lineObstacles=i.lineObstacles.concat(o.obstacles)),this.renderPath(s,r),this.addMarker("start",x(a.marker,a.startMarker),s),this.addMarker("end",x(a.marker,a.endMarker),s)}destroy(){this.graphics&&(M(this.graphics,function(t){t.destroy()}),delete this.graphics)}},u={applyRadius:function(t,e){let n=[];for(let i=0;i<t.length;i++){let r=t[i][1],a=t[i][2];if("number"==typeof r&&"number"==typeof a)if(0===i)n.push(["M",r,a]);else if(i===t.length-1)n.push(["L",r,a]);else if(e){let o=t[i-1],s=t[i+1];if(o&&s){let t=o[1],i=o[2],h=s[1],c=s[2];if("number"==typeof t&&"number"==typeof h&&"number"==typeof i&&"number"==typeof c&&t!==h&&i!==c){let o=t<h?1:-1,s=i<c?1:-1;n.push(["L",r-o*Math.min(Math.abs(r-t),e),a-s*Math.min(Math.abs(a-i),e)],["C",r,a,r,a,r+o*Math.min(Math.abs(r-h),e),a+s*Math.min(Math.abs(a-c),e)])}}}else n.push(["L",r,a])}return n}},{pick:g}=h(),{min:m,max:b,abs:P}=Math;function v(t,e,n){let i=e-1e-7,r=n||0,a=t.length-1,o,s;for(;r<=a;)if((s=i-t[o=a+r>>1].xMin)>0)r=o+1;else{if(!(s<0))return o;a=o-1}return r>0?r-1:0}function O(t,e){let n=v(t,e.x+1)+1;for(;n--;){var i;if(t[n].xMax>=e.x&&(i=t[n],e.x<=i.xMax&&e.x>=i.xMin&&e.y<=i.yMax&&e.y>=i.yMin))return n}return -1}function k(t){let e=[];if(t.length){e.push(["M",t[0].start.x,t[0].start.y]);for(let n=0;n<t.length;++n)e.push(["L",t[n].end.x,t[n].end.y])}return e}function w(t,e){t.yMin=b(t.yMin,e.yMin),t.yMax=m(t.yMax,e.yMax),t.xMin=b(t.xMin,e.xMin),t.xMax=m(t.xMax,e.xMax)}let A=function(t,e,n){let i=[],r=n.chartObstacles,a=O(r,t),o=O(r,e),s,h=g(n.startDirectionX,P(e.x-t.x)>P(e.y-t.y))?"x":"y",c,l,x,M;function d(t,e,n,i,r){let a={x:t.x,y:t.y};return a[e]=n[i||e]+(r||0),a}function y(t,e,n){let i=P(e[n]-t[n+"Min"])>P(e[n]-t[n+"Max"]);return d(e,n,t,n+(i?"Max":"Min"),i?1:-1)}o>-1?(s={start:l=y(r[o],e,h),end:e},M=l):M=e,a>-1&&(l=y(c=r[a],t,h),i.push({start:t,end:l}),l[h]>=t[h]==l[h]>=M[h]&&(x=t[h="y"===h?"x":"y"]<e[h],i.push({start:l,end:d(l,h,c,h+(x?"Max":"Min"),x?1:-1)}),h="y"===h?"x":"y"));let p=i.length?i[i.length-1].end:t;l=d(p,h,M),i.push({start:p,end:l});let f=d(l,h="y"===h?"x":"y",M);return i.push({start:l,end:f}),i.push(s),{path:u.applyRadius(k(i),n.radius),obstacles:i}};function I(t,e,n){let i=g(n.startDirectionX,P(e.x-t.x)>P(e.y-t.y)),r=i?"x":"y",a=[],o=n.obstacleMetrics,s=m(t.x,e.x)-o.maxWidth-10,h=b(t.x,e.x)+o.maxWidth+10,c=m(t.y,e.y)-o.maxHeight-10,l=b(t.y,e.y)+o.maxHeight+10,x,M,d,y=!1,p=n.chartObstacles,f=v(p,h),u=v(p,s);function A(t,e,n){let i,r,a,o,s=t.x<e.x?1:-1;t.x<e.x?(i=t,r=e):(i=e,r=t),t.y<e.y?(o=t,a=e):(o=e,a=t);let h=s<0?m(v(p,r.x),p.length-1):0;for(;p[h]&&(s>0&&p[h].xMin<=r.x||s<0&&p[h].xMax>=i.x);){if(p[h].xMin<=r.x&&p[h].xMax>=i.x&&p[h].yMin<=a.y&&p[h].yMax>=o.y){if(n)return{y:t.y,x:t.x<e.x?p[h].xMin-1:p[h].xMax+1,obstacle:p[h]};return{x:t.x,y:t.y<e.y?p[h].yMin-1:p[h].yMax+1,obstacle:p[h]}}h+=s}return e}function I(t,e,n,i,r){let a=r.soft,o=r.hard,s=i?"x":"y",h={x:e.x,y:e.y},c={x:e.x,y:e.y},l=t[s+"Max"]>=a[s+"Max"],x=t[s+"Min"]<=a[s+"Min"],M=t[s+"Max"]>=o[s+"Max"],d=t[s+"Min"]<=o[s+"Min"],y=P(t[s+"Min"]-e[s]),p=P(t[s+"Max"]-e[s]),f=10>P(y-p)?e[s]<n[s]:p<y;c[s]=t[s+"Min"],h[s]=t[s+"Max"];let u=A(e,c,i)[s]!==c[s],g=A(e,h,i)[s]!==h[s];return f=u?!g||f:!g&&f,f=x?!l||f:!l&&f,f=d?!M||f:!M&&f}for((f=O(p=p.slice(u,f+1),e))>-1&&(d=function(t,e,i){let r=m(t.xMax-e.x,e.x-t.xMin)<m(t.yMax-e.y,e.y-t.yMin),a=I(t,e,i,r,{soft:n.hardBounds,hard:n.hardBounds});return r?{y:e.y,x:t[a?"xMax":"xMin"]+(a?1:-1)}:{x:e.x,y:t[a?"yMax":"yMin"]+(a?1:-1)}}(p[f],e,t),a.push({end:e,start:d}),e=d);(f=O(p,e))>-1;)M=e[r]-t[r]<0,(d={x:e.x,y:e.y})[r]=p[f][M?r+"Max":r+"Min"]+(M?1:-1),a.push({end:e,start:d}),e=d;return{path:k(x=(x=function t(e,i,r){let a,o,x,M,d,f,u;if(e.x===i.x&&e.y===i.y)return[];let g=r?"x":"y",P=n.obstacleOptions.margin,v={soft:{xMin:s,xMax:h,yMin:c,yMax:l},hard:n.hardBounds};return(d=O(p,e))>-1?(M=I(d=p[d],e,i,r,v),w(d,n.hardBounds),u=r?{y:e.y,x:d[M?"xMax":"xMin"]+(M?1:-1)}:{x:e.x,y:d[M?"yMax":"yMin"]+(M?1:-1)},(f=O(p,u))>-1&&(w(f=p[f],n.hardBounds),u[g]=M?b(d[g+"Max"]-P+1,(f[g+"Min"]+d[g+"Max"])/2):m(d[g+"Min"]+P-1,(f[g+"Max"]+d[g+"Min"])/2),e.x===u.x&&e.y===u.y?(y&&(u[g]=M?b(d[g+"Max"],f[g+"Max"])+1:m(d[g+"Min"],f[g+"Min"])-1),y=!y):y=!1),o=[{start:e,end:u}]):(a=A(e,{x:r?i.x:e.x,y:r?e.y:i.y},r),o=[{start:e,end:{x:a.x,y:a.y}}],a[r?"x":"y"]!==i[r?"x":"y"]&&(M=I(a.obstacle,a,i,!r,v),w(a.obstacle,n.hardBounds),x={x:r?a.x:a.obstacle[M?"xMax":"xMin"]+(M?1:-1),y:r?a.obstacle[M?"yMax":"yMin"]+(M?1:-1):a.y},r=!r,o=o.concat(t({x:a.x,y:a.y},x,r)))),o=o.concat(t(o[o.length-1].end,i,!r))}(t,e,i)).concat(a.reverse())),obstacles:x}}A.requiresObstacles=!0,I.requiresObstacles=!0;let R={connectors:{type:"straight",radius:0,lineWidth:1,marker:{enabled:!1,align:"center",verticalAlign:"middle",inside:!1,lineWidth:1},startMarker:{symbol:"diamond"},endMarker:{symbol:"arrow-filled"}}},{setOptions:L}=h(),{defined:B,error:E,merge:H}=h();function C(t){let e=t.shapeArgs;if(e)return{xMin:e.x||0,xMax:(e.x||0)+(e.width||0),yMin:e.y||0,yMax:(e.y||0)+(e.height||0)};let n=t.graphic&&t.graphic.getBBox();return n?{xMin:t.plotX-n.width/2,xMax:t.plotX+n.width/2,yMin:t.plotY-n.height/2,yMax:t.plotY+n.height/2}:null}!function(t){function e(t){let e,n,i=C(this);switch(t.align){case"right":e="xMax";break;case"left":e="xMin"}switch(t.verticalAlign){case"top":n="yMin";break;case"bottom":n="yMax"}return{x:e?i[e]:(i.xMin+i.xMax)/2,y:n?i[n]:(i.yMin+i.yMax)/2}}function n(t,e){let n;return!B(e)&&(n=C(this))&&(e={x:(n.xMin+n.xMax)/2,y:(n.yMin+n.yMax)/2}),Math.atan2(e.y-t.y,t.x-e.x)}function i(t,e,n){let i=2*Math.PI,r=C(this),a=r.xMax-r.xMin,o=r.yMax-r.yMin,s=Math.atan2(o,a),h=a/2,c=o/2,l=r.xMin+h,x=r.yMin+c,M={x:l,y:x},d=t,y=1,p=!1,f=1,u=1;for(;d<-Math.PI;)d+=i;for(;d>Math.PI;)d-=i;return y=Math.tan(d),d>-s&&d<=s?(u=-1,p=!0):d>s&&d<=Math.PI-s?u=-1:d>Math.PI-s||d<=-(Math.PI-s)?(f=-1,p=!0):f=-1,p?(M.x+=f*h,M.y+=u*h*y):(M.x+=o/(2*y)*f,M.y+=u*c),n.x!==l&&(M.x=n.x),n.y!==x&&(M.y=n.y),{x:M.x+e*Math.cos(d),y:M.y-e*Math.sin(d)}}t.compose=function(t,r,a){let o=a.prototype;o.getPathfinderAnchorPoint||(t.prototype.callbacks.push(function(t){!1!==t.options.connectors.enabled&&((t.options.pathfinder||t.series.reduce(function(t,e){return e.options&&H(!0,e.options.connectors=e.options.connectors||{},e.options.pathfinder),t||e.options&&e.options.pathfinder},!1))&&(H(!0,t.options.connectors=t.options.connectors||{},t.options.pathfinder),E('WARNING: Pathfinder options have been renamed. Use "chart.connectors" or "series.connectors" instead.')),this.pathfinder=new r(this),this.pathfinder.update(!0))}),o.getMarkerVector=i,o.getPathfinderAnchorPoint=e,o.getRadiansToVector=n,L(R))}}(n||(n={}));let W=n;var X=a(260),_=a.n(X);let{addEvent:j,defined:D,pick:S,splat:V}=h(),Y=Math.max,q=Math.min;class G{static compose(t,e){W.compose(t,G,e)}constructor(t){this.init(t)}init(t){this.chart=t,this.connections=[],j(t,"redraw",function(){this.pathfinder.update()})}update(t){let e=this.chart,n=this,i=n.connections;n.connections=[],e.series.forEach(function(t){t.visible&&!t.options.isInternal&&t.points.forEach(function(t){let i,r=t.options;r&&r.dependency&&(r.connect=r.dependency);let a=t.options?.connect?V(t.options.connect):[];t.visible&&!1!==t.isInside&&a.forEach(r=>{let a="string"==typeof r?r:r.to;a&&(i=e.get(a)),i instanceof _()&&i.series.visible&&i.visible&&!1!==i.isInside&&n.connections.push(new f(t,i,"string"==typeof r?{}:r))})})});for(let t=0,e,r,a=i.length,o=n.connections.length;t<a;++t){r=!1;let a=i[t];for(e=0;e<o;++e){let t=n.connections[e];if((a.options&&a.options.type)===(t.options&&t.options.type)&&a.fromPoint===t.fromPoint&&a.toPoint===t.toPoint){t.graphics=a.graphics,r=!0;break}}r||a.destroy()}delete this.chartObstacles,delete this.lineObstacles,n.renderConnections(t)}renderConnections(t){t?this.chart.series.forEach(function(t){let e=function(){let e=t.chart.pathfinder;(e&&e.connections||[]).forEach(function(e){e.fromPoint&&e.fromPoint.series===t&&e.render()}),t.pathfinderRemoveRenderEvent&&(t.pathfinderRemoveRenderEvent(),delete t.pathfinderRemoveRenderEvent)};!1===t.options.animation?e():t.pathfinderRemoveRenderEvent=j(t,"afterAnimate",e)}):this.connections.forEach(function(t){t.render()})}getChartObstacles(t){let e=this.chart.series,n=S(t.algorithmMargin,0),i=[],r;for(let t=0,r=e.length;t<r;++t)if(e[t].visible&&!e[t].options.isInternal)for(let r=0,a=e[t].points.length,o,s;r<a;++r)(s=e[t].points[r]).visible&&(o=function(t){let e=t.shapeArgs;if(e)return{xMin:e.x||0,xMax:(e.x||0)+(e.width||0),yMin:e.y||0,yMax:(e.y||0)+(e.height||0)};let n=t.graphic&&t.graphic.getBBox();return n?{xMin:t.plotX-n.width/2,xMax:t.plotX+n.width/2,yMin:t.plotY-n.height/2,yMax:t.plotY+n.height/2}:null}(s))&&i.push({xMin:o.xMin-n,xMax:o.xMax+n,yMin:o.yMin-n,yMax:o.yMax+n});return i=i.sort(function(t,e){return t.xMin-e.xMin}),D(t.algorithmMargin)||(r=t.algorithmMargin=function(t){let e,n=t.length,i=[];for(let r=0;r<n;++r)for(let a=r+1;a<n;++a)(e=function t(e,n,i){let r=S(i,10),a=e.yMax+r>n.yMin-r&&e.yMin-r<n.yMax+r,o=e.xMax+r>n.xMin-r&&e.xMin-r<n.xMax+r,s=a?e.xMin>n.xMax?e.xMin-n.xMax:n.xMin-e.xMax:1/0,h=o?e.yMin>n.yMax?e.yMin-n.yMax:n.yMin-e.yMax:1/0;return o&&a?r?t(e,n,Math.floor(r/2)):1/0:q(s,h)}(t[r],t[a]))<80&&i.push(e);return i.push(80),Y(Math.floor(i.sort(function(t,e){return t-e})[Math.floor(i.length/10)]/2-1),1)}(i),i.forEach(function(t){t.xMin-=r,t.xMax+=r,t.yMin-=r,t.yMax+=r})),i}getObstacleMetrics(t){let e=0,n=0,i,r,a=t.length;for(;a--;)i=t[a].xMax-t[a].xMin,r=t[a].yMax-t[a].yMin,e<i&&(e=i),n<r&&(n=r);return{maxHeight:n,maxWidth:e}}getAlgorithmStartDirection(t){let e="left"!==t.align&&"right"!==t.align,n="top"!==t.verticalAlign&&"bottom"!==t.verticalAlign;return e?!!n&&void 0:!!n||void 0}}function T(t,e,n,i){return[["M",t,e+i/2],["L",t+n,e],["L",t,e+i/2],["L",t+n,e+i]]}function N(t,e,n,i){return T(t,e,n/2,i)}function z(t,e,n,i){return[["M",t+n,e],["L",t,e+i/2],["L",t+n,e+i],["Z"]]}function U(t,e,n,i){return z(t,e,n/2,i)}G.prototype.algorithms={fastAvoid:I,straight:function(t,e){return{path:[["M",t.x,t.y],["L",e.x,e.y]],obstacles:[{start:t,end:e}]}},simpleConnect:A};let Z=h();Z.Pathfinder=Z.Pathfinder||G,({compose:function(t){let e=t.prototype.symbols;e.arrow=T,e["arrow-filled"]=z,e["arrow-filled-half"]=U,e["arrow-half"]=N,e["triangle-left"]=z,e["triangle-left-half"]=U}}).compose(Z.SVGRenderer),Z.Pathfinder.compose(Z.Chart,Z.Point);let F=h();return o.default})());