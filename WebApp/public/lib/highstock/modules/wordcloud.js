!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/wordcloud
 * @requires highcharts
 *
 * (c) 2016-2025 Highsoft AS
 * Authors: <AUTHORS>
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/modules/wordcloud",["highcharts/highcharts"],function(t){return e(t,t.SeriesRegistry)}):"object"==typeof exports?exports["highcharts/modules/wordcloud"]=e(t._Highcharts,t._Highcharts.SeriesRegistry):t.Highcharts=e(t.Highcharts,t.Highcharts.SeriesRegistry)}("undefined"==typeof window?this:window,(t,e)=>(()=>{"use strict";var i={512:t=>{t.exports=e},944:e=>{e.exports=t}},o={};function a(t){var e=o[t];if(void 0!==e)return e.exports;var n=o[t]={exports:{}};return i[t](n,n.exports,a),n.exports}a.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return a.d(e,{a:e}),e},a.d=(t,e)=>{for(var i in e)a.o(e,i)&&!a.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},a.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var n={};a.d(n,{default:()=>Z});var r=a(944),l=a.n(r);let s={draw:function(t,e){let{animatableAttribs:i,onComplete:o,css:a,renderer:n}=e,r=t.series&&t.series.chart.hasRendered?void 0:t.series&&t.series.options.animation,l=t.graphic;if(e.attribs={...e.attribs,class:t.getClassName()},t.shouldDraw())l||(t.graphic=l="text"===e.shapeType?n.text():"image"===e.shapeType?n.image(e.imageUrl||"").attr(e.shapeArgs||{}):n[e.shapeType](e.shapeArgs||{}),l.add(e.group)),a&&l.css(a),l.attr(e.attribs).animate(i,!e.isNew&&r,o);else if(l){let e=()=>{t.graphic=l=l&&l.destroy(),"function"==typeof o&&o()};Object.keys(i).length?l.animate(i,void 0,()=>e()):e()}}};var h=a(512),d=a.n(h);let{column:{prototype:{pointClass:p}}}=d().seriesTypes,{extend:u}=l();class g extends p{isValid(){return!0}}u(g.prototype,{weight:1});let{deg2rad:c}=l(),{extend:m,find:f,isNumber:x,isObject:y,merge:b}=l();function M(t,e){return!(e.left>t.right||e.right<t.left||e.top>t.bottom||e.bottom<t.top)}function w(t){let e,i=t.axes||[];return i.length||(i=[],(e=t.concat([t[0]])).reduce((t,e)=>{let o=function(t,e){let i=e[0]-t[0],o=e[1]-t[1];return[[-o,i],[o,-i]]}(t,e)[0];return f(i,t=>t[0]===o[0]&&t[1]===o[1])||i.push(o),e}),t.axes=i),i}function S(t,e){let i=t.map(t=>{let i=t[0],o=t[1];return i*e[0]+o*e[1]});return{min:Math.min.apply(this,i),max:Math.max.apply(this,i)}}function F(t,e){let i=w(t),o=w(e);return!f(i.concat(o),i=>(function(t,e,i){let o=S(e,t),a=S(i,t);return!!(a.min>o.max||a.max<o.min)})(i,t,e))}function P(t,e){let i=4*t,o=Math.ceil((Math.sqrt(i)-1)/2),a=2*o+1,n=Math.pow(a,2),r=!1;return a-=1,t<=1e4&&("boolean"==typeof r&&i>=n-a&&(r={x:o-(n-i),y:-o}),n-=a,"boolean"==typeof r&&i>=n-a&&(r={x:-o,y:-o+(n-i)}),n-=a,"boolean"==typeof r&&(r=i>=n-a?{x:-o+(n-i),y:o}:{x:o,y:o-(n-i-a)}),r.x*=5,r.y*=5),r}function A(t,e){let i=Math.pow(10,x(e)?e:14);return Math.round(t*i)/i}function X(t,e){let i=t[0],o=t[1],a=-(c*e),n=Math.cos(a),r=Math.sin(a);return[A(i*n-o*r),A(i*r+o*n)]}function v(t,e,i){let o=X([t[0]-e[0],t[1]-e[1]],i);return[o[0]+e[0],o[1]+e[1]]}let{noop:T}=l(),{column:z}=d().seriesTypes,{extend:Y,isArray:H,isNumber:R,isObject:B,merge:C}=l(),{archimedeanSpiral:W,extendPlayingField:_,getBoundingBoxFromPolygon:N,getPlayingField:O,getPolygon:D,getRandomPosition:j,getRotation:E,getScale:L,getSpiral:U,intersectionTesting:V,isPolygonsColliding:q,rectangularSpiral:k,rotate2DToOrigin:I,rotate2DToPoint:G,squareSpiral:J,updateFieldBoundaries:K}={archimedeanSpiral:function(t,e){let i=e.field,o=i.width*i.width+i.height*i.height,a=.8*t,n=!1;return t<=1e4&&(Math.min(Math.abs((n={x:a*Math.cos(a),y:a*Math.sin(a)}).x),Math.abs(n.y))<o||(n=!1)),n},extendPlayingField:function(t,e){let i,o,a,n,r,l;return y(t)&&y(e)?(i=e.bottom-e.top,o=e.right-e.left,r=o*(a=t.ratioX)>i*(n=t.ratioY)?o:i,l=b(t,{width:t.width+r*a*2,height:t.height+r*n*2})):l=t,l},getBoundingBoxFromPolygon:function(t){return t.reduce(function(t,e){let i=e[0],o=e[1];return t.left=Math.min(i,t.left),t.right=Math.max(i,t.right),t.bottom=Math.max(o,t.bottom),t.top=Math.min(o,t.top),t},{left:Number.MAX_VALUE,right:-Number.MAX_VALUE,bottom:-Number.MAX_VALUE,top:Number.MAX_VALUE})},getPlayingField:function(t,e,i){let o=i.reduce(function(t,e){let i=e.dimensions,o=Math.max(i.width,i.height);return t.maxHeight=Math.max(t.maxHeight,i.height),t.maxWidth=Math.max(t.maxWidth,i.width),t.area+=o*o,t},{maxHeight:0,maxWidth:0,area:0}),a=Math.max(o.maxHeight,o.maxWidth,.85*Math.sqrt(o.area)),n=t>e?t/e:1,r=e>t?e/t:1;return{width:a*n,height:a*r,ratioX:n,ratioY:r}},getPolygon:function(t,e,i,o,a){let n=[t,e],r=t-i/2,l=t+i/2,s=e-o/2,h=e+o/2;return[[r,s],[l,s],[l,h],[r,h]].map(function(t){return v(t,n,-a)})},getRandomPosition:function(t){return Math.round(t*(Math.random()+.5)/2)},getRotation:function(t,e,i,o){let a=!1,n;return x(t)&&x(e)&&x(i)&&x(o)&&t>0&&e>-1&&o>i&&(n=(o-i)/(t-1||1),a=i+e%t*n),a},getScale:function(t,e,i){let o=2*Math.max(Math.abs(i.top),Math.abs(i.bottom)),a=2*Math.max(Math.abs(i.left),Math.abs(i.right));return Math.min(a>0?1/a*t:1,o>0?1/o*e:1)},getSpiral:function(t,e){let i=[];for(let o=1;o<1e4;o++)i.push(t(o,e));return t=>t<=1e4&&i[t-1]},intersectionTesting:function(t,e){let i=e.placed,o=e.field,a=e.rectangle,n=e.polygon,r=e.spiral,l=t.rect=m({},a),s=1,h={x:0,y:0};for(t.polygon=n,t.rotation=e.rotation;!1!==h&&(function(t,e){let i=t.rect,o=t.polygon,a=t.lastCollidedWith,n=function(e){let a=M(i,e.rect);return a&&(t.rotation%90||e.rotation%90)&&(a=F(o,e.polygon)),a},r=!1;return a&&((r=n(a))||delete t.lastCollidedWith),r||(r=!!f(e,function(e){let i=n(e);return i&&(t.lastCollidedWith=e),i})),r}(t,i)||function(t,e){let i={left:-(e.width/2),right:e.width/2,top:-(e.height/2),bottom:e.height/2};return!(i.left<t.left&&i.right>t.right&&i.top<t.top&&i.bottom>t.bottom)}(l,o));)y(h=r(s))&&(l.left=a.left+h.x,l.right=a.right+h.x,l.top=a.top+h.y,l.bottom=a.bottom+h.y,t.polygon=function(t,e,i){return i.map(function(i){return[i[0]+t,i[1]+e]})}(h.x,h.y,n)),s++;return h},isPolygonsColliding:F,isRectanglesIntersecting:M,rectangularSpiral:function(t,e){let i=P(t,e),o=e.field;return i&&(i.x*=o.ratioX,i.y*=o.ratioY),i},rotate2DToOrigin:X,rotate2DToPoint:v,squareSpiral:P,updateFieldBoundaries:function(t,e){return(!x(t.left)||t.left>e.left)&&(t.left=e.left),(!x(t.right)||t.right<e.right)&&(t.right=e.right),(!x(t.top)||t.top>e.top)&&(t.top=e.top),(!x(t.bottom)||t.bottom<e.bottom)&&(t.bottom=e.bottom),t}};class Q extends z{pointAttribs(t,e){let i=l().seriesTypes.column.prototype.pointAttribs.call(this,t,e);return delete i.stroke,delete i["stroke-width"],i}deriveFontSize(t,e,i){let o=R(t)?t:0,a=R(e)?e:1;return Math.floor(Math.max(R(i)?i:1,o*a))}drawPoints(){if(this.zooming||this.defaultScale&&this.group.scaleX!==this.defaultScale)return;let t=this.hasRendered,e=this.xAxis,i=this.yAxis,o=this.chart,a=this.group,n=this.options,r=n.animation,l=n.allowExtendPlayingField,h=o.renderer,d=[],p=this.placementStrategy[n.placementStrategy],u=n.rotation,g=this.points.map(function(t){return t.weight}),c=Math.max.apply(null,g),m=this.points.concat().sort((t,e)=>e.weight-t.weight),f=h.text().add(a),x;for(let t of(this.group.attr({scaleX:1,scaleY:1}),m)){let e=1/c*t.weight,i=Y({fontSize:this.deriveFontSize(e,n.maxFontSize,n.minFontSize)+"px"},n.style);f.css(i).attr({x:0,y:0,text:t.name});let o=f.getBBox(!0);t.dimensions={height:o.height,width:o.width}}x=O(e.len,i.len,m);let y=U(this.spirals[n.spiral],{field:x});for(let e of m){let i=1/c*e.weight,o=Y({fontSize:this.deriveFontSize(i,n.maxFontSize,n.minFontSize)+"px"},n.style),g=p(e,{data:m,field:x,placed:d,rotation:u}),f=Y(this.pointAttribs(e,e.selected&&"select"),{align:"center","alignment-baseline":"middle","dominant-baseline":"middle",x:g.x,y:g.y,text:e.name,rotation:R(g.rotation)?g.rotation:void 0}),b=D(g.x,g.y,e.dimensions.width,e.dimensions.height,g.rotation),M=N(b),w=V(e,{rectangle:M,polygon:b,field:x,placed:d,spiral:y,rotation:g.rotation}),S;!w&&l&&(x=_(x,M),w=V(e,{rectangle:M,polygon:b,field:x,placed:d,spiral:y,rotation:g.rotation})),B(w)?(f.x=(f.x||0)+w.x,f.y=(f.y||0)+w.y,M.left+=w.x,M.right+=w.x,M.top+=w.y,M.bottom+=w.y,x=K(x,M),d.push(e),e.isNull=!1,e.isInside=!0):e.isNull=!0,r&&(S={x:f.x,y:f.y},t?(delete f.x,delete f.y):(f.x=0,f.y=0)),s.draw(e,{animatableAttribs:S,attribs:f,css:o,group:a,renderer:h,shapeArgs:void 0,shapeType:"text"})}f=f.destroy(),this.defaultScale=L(e.len,i.len,x),this.field=x,this.group.attr({scaleX:this.defaultScale,scaleY:this.defaultScale})}hasData(){return B(this)&&!0===this.visible&&H(this.points)&&this.points.length>0}getPlotBox(t){let{chart:e,group:i,zooming:o}=this,{plotSizeX:a=0,plotSizeY:n=0,inverted:r}=e,l=this[r?"yAxis":"xAxis"],s=this[r?"xAxis":"yAxis"],h=l?l.len:e.plotWidth,d=s?s.len:e.plotHeight,p=l?l.left:e.plotLeft,u=s?s.top:e.plotTop,g=this.field,c=0,m=0,f=p+h/2,x=u+d/2,y=f,b=x,M=this.defaultScale||1,w=0,S=0;if(g&&(w=2*Math.max(Math.abs(g.top),Math.abs(g.bottom)),S=2*Math.max(Math.abs(g.left),Math.abs(g.right))),r&&([S,w]=[w,S]),i&&o){let e=p+Math.max(S*(M=Math.max(o.scale,this.defaultScale||1)),h)/2,r=u+Math.max(w*M,d)/2,l=M-(i.scaleX||1);c=l*((a-S)/2+o.zoomX*a-h/2),m=l*((n-w)/2+o.zoomY*n-d/2),"series"===t&&(o.x=Math.max(0,Math.min(1-o.width,o.x+o.panX/o.scale)),c+=o.panX*a,o.panX=0,o.y=Math.max(0,Math.min(1-o.height,o.y+o.panY/o.scale)),m+=o.panY*n,o.panY=0),R(i.translateX)&&R(i.translateY)&&(y=i.translateX,b=i.translateY),f=y-c,(x=b-m)>r?x=r:x<2*u+d-r&&(x=2*u+d-r),f>e?f=e:f<2*p+h-e&&(f=2*p+h-e)}return{translateX:f,translateY:x,scaleX:M,scaleY:M}}}Q.defaultOptions=C(z.defaultOptions,{allowExtendPlayingField:!0,animation:{duration:500},borderWidth:0,clip:!1,colorByPoint:!0,cropThreshold:1/0,minFontSize:1,maxFontSize:25,placementStrategy:"center",rotation:{from:0,orientations:2,to:90},showInLegend:!1,spiral:"rectangular",style:{fontFamily:"sans-serif",fontWeight:"900",whiteSpace:"nowrap"},tooltip:{followPointer:!0,pointFormat:'<span style="color:{point.color}">●</span> {series.name}: <b>{point.weight}</b><br/>'}}),Y(Q.prototype,{animate:T,animateDrilldown:T,animateDrillupFrom:T,isCartesian:!1,pointClass:g,setClip:T,placementStrategy:{random:function(t,e){let i=e.field,o=e.rotation;return{x:j(i.width)-i.width/2,y:j(i.height)-i.height/2,rotation:E(o.orientations,t.index,o.from,o.to)}},center:function(t,e){let i=e.rotation;return{x:0,y:0,rotation:E(i.orientations,t.index,i.from,i.to)}}},pointArrayMap:["weight"],spirals:{archimedean:W,rectangular:k,square:J},utils:{extendPlayingField:_,getRotation:E,isPolygonsColliding:q,rotate2DToOrigin:I,rotate2DToPoint:G}}),d().registerSeriesType("wordcloud",Q);let Z=l();return n.default})());