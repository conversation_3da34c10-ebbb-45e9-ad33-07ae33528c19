!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/export-data
 * @requires highcharts
 * @requires highcharts/modules/exporting
 *
 * Export data module
 *
 * (c) 2010-2025 Torstein Honsi
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.AST,t._Highcharts.Chart):"function"==typeof define&&define.amd?define("highcharts/modules/export-data",["highcharts/highcharts"],function(t){return e(t,t.AST,t.Chart)}):"object"==typeof exports?exports["highcharts/modules/export-data"]=e(t._Highcharts,t._Highcharts.AST,t._Highcharts.Chart):t.Highcharts=e(t.Highcharts,t.Highcharts.AST,t.Highcharts.Chart)}("undefined"==typeof window?this:window,(t,e,a)=>(()=>{"use strict";var o,n={660:t=>{t.exports=e},944:e=>{e.exports=t},960:t=>{t.exports=a}},i={};function r(t){var e=i[t];if(void 0!==e)return e.exports;var a=i[t]={exports:{}};return n[t](a,a.exports,r),a.exports}r.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return r.d(e,{a:e}),e},r.d=(t,e)=>{for(var a in e)r.o(e,a)&&!r.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:e[a]})},r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var l={};r.d(l,{default:()=>I});var s=r(944),h=r.n(s);let{isSafari:c,win:d,win:{document:p}}=h(),{error:g}=h(),u=d.URL||d.webkitURL||d;function f(t){let e=t.replace(/filename=.*;/,"").match(/data:([^;]*)(;base64)?,([A-Z+\d\/]+)/i);if(e&&e.length>3&&d.atob&&d.ArrayBuffer&&d.Uint8Array&&d.Blob&&u.createObjectURL){let t=d.atob(e[3]),a=new d.ArrayBuffer(t.length),o=new d.Uint8Array(a);for(let e=0;e<o.length;++e)o[e]=t.charCodeAt(e);return u.createObjectURL(new d.Blob([o],{type:e[1]}))}}let m={dataURLtoBlob:f,downloadURL:function(t,e){let a=d.navigator,o=p.createElement("a");if("string"!=typeof t&&!(t instanceof String)&&a.msSaveOrOpenBlob)return void a.msSaveOrOpenBlob(t,e);if(t=""+t,a.userAgent.length>1e3)throw Error("Input too long");let n=/Edge\/\d+/.test(a.userAgent);if((c&&"string"==typeof t&&0===t.indexOf("data:application/pdf")||n||t.length>2e6)&&!(t=f(t)||""))throw Error("Failed to convert to blob");if(void 0!==o.download)o.href=t,o.download=e,p.body.appendChild(o),o.click(),p.body.removeChild(o);else try{if(!d.open(t,"chart"))throw Error("Failed to open window")}catch{d.location.href=t}},getScript:function(t){return new Promise((e,a)=>{let o=p.getElementsByTagName("head")[0],n=p.createElement("script");n.type="text/javascript",n.src=t,n.onload=()=>{e()},n.onerror=()=>{a(g(`Error loading script ${t}`))},o.appendChild(n)})}};var x=r(660),b=r.n(x),y=r(960),w=r.n(y);let T={exporting:{csv:{annotations:{itemDelimiter:"; ",join:!1},columnHeaderFormatter:null,dateFormat:"%Y-%m-%d %H:%M:%S",decimalPoint:null,itemDelimiter:null,lineDelimiter:"\n"},menuItemDefinitions:{downloadCSV:{textKey:"downloadCSV",onclick:function(){this.exporting?.downloadCSV()}},downloadXLS:{textKey:"downloadXLS",onclick:function(){this.exporting?.downloadXLS()}},viewData:{textKey:"viewData",onclick:function(){this.exporting?.wrapLoading(this.exporting.toggleDataTable)}}},showTable:!1,useMultiLevelHeaders:!0,useRowspanHeaders:!0,showExportInProgress:!0},lang:{downloadCSV:"Download CSV",downloadXLS:"Download XLS",exportData:{annotationHeader:"Annotations",categoryHeader:"Category",categoryDatetimeHeader:"DateTime"},viewData:"View data table",hideData:"Hide data table",exportInProgress:"Exporting..."}},{getOptions:D,setOptions:v}=h(),{downloadURL:S}=m,{composed:L,doc:E,win:C}=h(),{addEvent:A,defined:H,extend:R,find:V,fireEvent:k,isNumber:O,pick:N,pushUnique:B}=h();!function(t){function e(){this.wrapLoading(()=>{let t=this.getCSV(!0);S(o(t,"text/csv")||"data:text/csv,\uFEFF"+encodeURIComponent(t),this.getFilename()+".csv")})}function a(){this.wrapLoading(()=>{let t='<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head>\x3c!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>Ark1</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--\x3e<style>td{border:none;font-family: Calibri, sans-serif;} .number{mso-number-format:"0.00";} .text{ mso-number-format:"@";}</style><meta name=ProgId content=Excel.Sheet><meta charset=UTF-8></head><body>'+this.getTable(!0)+"</body></html>";S(o(t,"application/vnd.ms-excel")||"data:application/vnd.ms-excel;base64,"+C.btoa(unescape(encodeURIComponent(t))),this.getFilename()+".xls")})}function o(t,e){let a=C.navigator,o=C.URL||C.webkitURL||C;try{if(a.msSaveOrOpenBlob&&C.MSBlobBuilder){let e=new C.MSBlobBuilder;return e.append(t),e.getBlob("image/svg+xml")}return o.createObjectURL(new C.Blob(["\uFEFF"+t],{type:e}))}catch(t){}}function n(t){let e="",a=this.getDataRows(),o=this.options?.csv,n=N(o?.decimalPoint,o?.itemDelimiter!==","&&t?1.1.toLocaleString()[1]:"."),i=N(o?.itemDelimiter,","===n?";":","),r=o?.lineDelimiter;return a.forEach((t,o)=>{let l="",s=t.length;for(;s--;)"string"==typeof(l=t[s])&&(l=`"${l}"`),"number"==typeof l&&"."!==n&&(l=l.toString().replace(".",n)),t[s]=l;t.length=a.length?a[0].length:0,e+=t.join(i),o<a.length-1&&(e+=r)}),e}function i(t){let e,a,o=this.chart,n=o.hasParallelCoordinates,i=o.time,r=this.options?.csv||{},l=o.xAxis,s={},h=[],c=[],d=[],p=o.options.lang.exportData,g=p?.categoryHeader,u=p?.categoryDatetimeHeader,f=function(e,a,o){if(r.columnHeaderFormatter){let t=r.columnHeaderFormatter(e,a,o);if(!1!==t)return t}return!e&&g?g:!e.bindAxes&&u&&g?e.options.title&&e.options.title.text||(e.dateTime?u:g):t?{columnTitle:((o||0)>1?a:e.name)||"",topLevelColumnTitle:e.name}:e.name+((o||0)>1?" ("+a+")":"")},m=function(t,e,a){let o={},n={};return e.forEach(function(e){let i=(t.keyToAxis&&t.keyToAxis[e]||e)+"Axis",r=O(a)?t.chart[i][a]:t[i];o[e]=r&&r.categories||[],n[e]=r&&r.dateTime}),{categoryMap:o,dateTimeValueAxisMap:n}},x=function(t,e){let a=t.pointArrayMap||["y"];return t.data.some(t=>void 0!==t.y&&t.name)&&e&&!e.categories&&"name"!==t.exportKey?["x",...a]:a},b=[],y,w,T,D=0,v,S;for(v in o.series.forEach(function(e){let a=e.options.keys,o=e.xAxis,h=a||x(e,o),p=h.length,g=!e.requireSorting&&{},u=l.indexOf(o),y=m(e,h),w,v;if(!1!==e.options.includeInDataExport&&!e.options.isInternal&&!1!==e.visible){for(V(b,function(t){return t[0]===u})||b.push([u,D]),v=0;v<p;)T=f(e,h[v],h.length),d.push(T.columnTitle||T),t&&c.push(T.topLevelColumnTitle||T),v++;w={chart:e.chart,autoIncrement:e.autoIncrement,options:e.options,pointArrayMap:e.pointArrayMap,index:e.index},e.options.data?.forEach(function(t,a){let l,c,d,f={series:w};n&&(y=m(e,h,a)),e.pointClass.prototype.applyOptions.apply(f,[t]);let x=e.data[a]&&e.data[a].name;if(l=(f.x??"")+","+x,v=0,(!o||"name"===e.exportKey||!n&&o&&o.hasNames&&x)&&(l=x),g&&(g[l]&&(l+="|"+a),g[l]=!0),s[l]){let t=`${l},${s[l].pointers[e.index]}`,a=l;s[l].pointers[e.index]&&(s[t]||(s[t]=[],s[t].xValues=[],s[t].pointers=[]),l=t),s[a].pointers[e.index]+=1}else{s[l]=[],s[l].xValues=[];let t=[];for(let a=0;a<e.chart.series.length;a++)t[a]=0;s[l].pointers=t,s[l].pointers[e.index]=1}for(s[l].x=f.x,s[l].name=x,s[l].xValues[u]=f.x;v<p;)c=h[v],d=e.pointClass.prototype.getNestedProperty.apply(f,[c]),s[l][D+v]=N(y.categoryMap[c][d],y.dateTimeValueAxisMap[c]?i.dateFormat(r.dateFormat,d):null,d),v++}),D+=v}}),s)Object.hasOwnProperty.call(s,v)&&h.push(s[v]);for(w=t?[c,d]:[d],D=b.length;D--;)e=b[D][0],a=b[D][1],y=l[e],h.sort(function(t,a){return t.xValues[e]-a.xValues[e]}),S=f(y),w[0].splice(a,0,S),t&&w[1]&&w[1].splice(a,0,S),h.forEach(function(t){let e=t.name;y&&!H(e)&&(y.dateTime?(t.x instanceof Date&&(t.x=t.x.getTime()),e=i.dateFormat(r.dateFormat,t.x)):e=y.categories?N(y.names[t.x],y.categories[t.x],t.x):t.x),t.splice(a,0,e)});return k(o,"exportData",{dataRows:w=w.concat(h)}),w}function r(t){let e=t=>{if(!t.tagName||"#text"===t.tagName)return t.textContent||"";let a=t.attributes,o=`<${t.tagName}`;return a&&Object.keys(a).forEach(t=>{let e=a[t];o+=` ${t}="${e}"`}),o+=">",o+=t.textContent||"",(t.children||[]).forEach(t=>{o+=e(t)}),o+=`</${t.tagName}>`};return e(this.getTableAST(t))}function l(t){let e=0,a=[],o=this,n=o.chart,i=n.options,r=t?1.1.toLocaleString()[1]:".",l=N(o.options.useMultiLevelHeaders,!0),s=o.getDataRows(l),h=l?s.shift():null,c=s.shift(),d=function(t,e){let a=t.length;if(e.length!==a)return!1;for(;a--;)if(t[a]!==e[a])return!1;return!0},p=function(t,e,a,o){let n=N(o,""),i="highcharts-text"+(e?" "+e:"");return"number"==typeof n?(n=n.toString(),","===r&&(n=n.replace(".",r)),i="highcharts-number"):o||(i="highcharts-empty"),{tagName:t,attributes:a=R({class:i},a),textContent:n}},{tableCaption:g}=o.options||{};!1!==g&&a.push({tagName:"caption",attributes:{class:"highcharts-table-caption"},textContent:"string"==typeof g?g:i.title?.text||i.lang.chartTitle});for(let t=0,a=s.length;t<a;++t)s[t].length>e&&(e=s[t].length);a.push(function(t,e,a){let n=[],i=0,r=a||e&&e.length,s,h=0,c;if(l&&t&&e&&!d(t,e)){let a=[];for(;i<r;++i)if((s=t[i])===t[i+1])++h;else if(h)a.push(p("th","highcharts-table-topheading",{scope:"col",colspan:h+1},s)),h=0;else{s===e[i]?o.options.useRowspanHeaders?(c=2,delete e[i]):(c=1,e[i]=""):c=1;let t=p("th","highcharts-table-topheading",{scope:"col"},s);c>1&&t.attributes&&(t.attributes.valign="top",t.attributes.rowspan=c),a.push(t)}n.push({tagName:"tr",children:a})}if(e){let t=[];for(i=0,r=e.length;i<r;++i)void 0!==e[i]&&t.push(p("th",null,{scope:"col"},e[i]));n.push({tagName:"tr",children:t})}return{tagName:"thead",children:n}}(h,c||[],Math.max(e,c?.length||0)));let u=[];s.forEach(function(t){let a=[];for(let o=0;o<e;o++)a.push(p(o?"td":"th",null,o?{}:{scope:"row"},t[o]));u.push({tagName:"tr",children:a})}),a.push({tagName:"tbody",children:u});let f={tree:{tagName:"table",id:`highcharts-data-table-${n.index}`,children:a}};return k(n,"afterGetTableAST",f),f.tree}function s(){this.toggleDataTable(!1)}function h(t){let e=this.chart,a=(t=N(t,!this.isDataTableVisible))&&!this.dataTableDiv;if(a&&(this.dataTableDiv=E.createElement("div"),this.dataTableDiv.className="highcharts-data-table",e.renderTo.parentNode.insertBefore(this.dataTableDiv,e.renderTo.nextSibling)),this.dataTableDiv){let o=this.dataTableDiv.style,n=o.display;o.display=t?"block":"none",t?(this.dataTableDiv.innerHTML=b().emptyHTML,new(b())([this.getTableAST()]).addToDOM(this.dataTableDiv),k(e,"afterViewData",{element:this.dataTableDiv,wasHidden:a||n!==o.display})):k(e,"afterHideData")}this.isDataTableVisible=t;let o=this.divElements,n=this.options,i=n.buttons?.contextButton.menuItems,r=e.options.lang;if(n&&n.menuItemDefinitions&&r&&r.viewData&&r.hideData&&i&&o){let t=o[i.indexOf("viewData")];t&&b().setElementHTML(t,this.isDataTableVisible?r.hideData:r.viewData)}}function c(){this.toggleDataTable(!0)}function d(t){let e=this.chart,a=!!this.options.showExportInProgress,o=C.requestAnimationFrame||setTimeout;o(()=>{a&&e.showLoading(e.options.lang.exportInProgress),o(()=>{try{t.call(this)}finally{a&&e.hideLoading()}})})}function p(){let t=this.exporting,e=t?.dataTableDiv,a=(t,e)=>t.children[e].textContent,o=(t,e)=>(o,n)=>{let i,r;return i=a(e?o:n,t),r=a(e?n:o,t),""===i||""===r||isNaN(i)||isNaN(r)?i.toString().localeCompare(r):i-r};if(e&&t.options.allowTableSorting){let a=e.querySelector("thead tr");a&&a.childNodes.forEach(a=>{let n=e.querySelector("tbody");a.addEventListener("click",function(){let i=[...e.querySelectorAll("tr:not(thead tr)")],r=[...a.parentNode.children];t&&(i.sort(o(r.indexOf(a),t.ascendingOrderInTable=!t.ascendingOrderInTable)).forEach(t=>{n?.appendChild(t)}),r.forEach(t=>{["highcharts-sort-ascending","highcharts-sort-descending"].forEach(e=>{t.classList.contains(e)&&t.classList.remove(e)})}),a.classList.add(t.ascendingOrderInTable?"highcharts-sort-ascending":"highcharts-sort-descending"))})})}}function g(){this.options?.exporting?.showTable&&!this.options.chart.forExport&&this.exporting?.viewData()}function u(){this.exporting?.dataTableDiv?.remove()}t.compose=function(t,o,f){if(!B(L,"ExportData"))return;R(w().prototype,{downloadCSV:function(){return this.exporting?.downloadCSV()},downloadXLS:function(){return this.exporting?.downloadXLS()},getCSV:function(t){return this.exporting?.getCSV(t)},getDataRows:function(t){return this.exporting?.getDataRows(t)},getTable:function(t){return this.exporting?.getTable(t)},getTableAST:function(t){return this.exporting?.getTableAST(t)},hideData:function(){return this.exporting?.hideData()},toggleDataTable:function(t){return this.exporting?.toggleDataTable(t)},viewData:function(){return this.exporting?.viewData()}});let m=o.prototype;if(!m.downloadCSV){A(t,"afterViewData",p),A(t,"render",g),A(t,"destroy",u),m.downloadCSV=e,m.downloadXLS=a,m.getCSV=n,m.getDataRows=i,m.getTable=r,m.getTableAST=l,m.hideData=s,m.toggleDataTable=h,m.wrapLoading=d,m.viewData=c,v(T);let o=D().exporting?.buttons?.contextButton?.menuItems;o&&o.push("separator","downloadCSV","downloadXLS","viewData");let{arearange:x,gantt:b,map:y,mapbubble:w,treemap:S,xrange:L}=f.types;x&&(x.prototype.keyToAxis={low:"y",high:"y"}),b&&(b.prototype.exportKey="name",b.prototype.keyToAxis={start:"x",end:"x"}),y&&(y.prototype.exportKey="name"),w&&(w.prototype.exportKey="name"),S&&(S.prototype.exportKey="name"),L&&(L.prototype.keyToAxis={x2:"x"})}}}(o||(o={}));let F=o,U=h();U.dataURLtoBlob=U.dataURLtoBlob||m.dataURLtoBlob,U.downloadURL=U.downloadURL||m.downloadURL,F.compose(U.Chart,U.Exporting,U.Series);let I=h();return l.default})());