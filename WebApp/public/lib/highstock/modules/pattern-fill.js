!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/pattern-fill
 * @requires highcharts
 *
 * Module for adding patterns and images as point fills.
 *
 * (c) 2010-2025 Highsoft AS
 * Author: <PERSON><PERSON>, Øystein Moseng
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts):"function"==typeof define&&define.amd?define("highcharts/modules/pattern-fill",["highcharts/highcharts"],function(t){return e(t)}):"object"==typeof exports?exports["highcharts/modules/pattern-fill"]=e(t._Highcharts):t.Highcharts=e(t.Highcharts)}("undefined"==typeof window?this:window,t=>(()=>{"use strict";var e={944:e=>{e.exports=t}},r={};function i(t){var a=r[t];if(void 0!==a)return a.exports;var o=r[t]={exports:{}};return e[t](o,o.exports,i),o.exports}i.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return i.d(e,{a:e}),e},i.d=(t,e)=>{for(var r in e)i.o(e,r)&&!i.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var a={};i.d(a,{default:()=>R});var o=i(944),n=i.n(o);let{animObject:h}=n(),{getOptions:s}=n(),{addEvent:l,defined:p,erase:c,extend:d,merge:f,pick:g,removeEvent:u,wrap:m}=n(),x=function(){let t=[],e=s().colors,r=0;for(let i of["M 0 0 L 5 5 M 4.5 -0.5 L 5.5 0.5 M -0.5 4.5 L 0.5 5.5","M 0 5 L 5 0 M -0.5 0.5 L 0.5 -0.5 M 4.5 5.5 L 5.5 4.5","M 2 0 L 2 5 M 4 0 L 4 5","M 0 2 L 5 2 M 0 4 L 5 4","M 0 1.5 L 2.5 1.5 L 2.5 0 M 2.5 5 L 2.5 3.5 L 5 3.5"])t.push({path:i,color:e[r++],width:5,height:5,patternTransform:"scale(1.4 1.4)"});for(let i of(r=5,["M 0 0 L 5 10 L 10 0","M 3 3 L 8 3 L 8 8 L 3 8 Z","M 5 5 m -4 0 a 4 4 0 1 1 8 0 a 4 4 0 1 1 -8 0","M 0 0 L 10 10 M 9 -1 L 11 1 M -1 9 L 1 11","M 0 10 L 10 0 M -1 1 L 1 -1 M 9 11 L 11 9"]))t.push({path:i,color:e[r],width:10,height:10}),r+=5;return t}();function y(t,e){let r=JSON.stringify(t),i=r.length||0,a=0,o=0,n;if(e){n=Math.max(Math.floor(i/500),1);for(let t=0;t<i;t+=n)a+=r.charCodeAt(t);a&=a}for(;o<i;++o)a=(a<<5)-a+r.charCodeAt(o),a&=a;return a.toString(16).replace("-","1")}function w(){if(this.renderer&&(this.renderer.defIds||[]).filter(t=>t&&t.indexOf&&0===t.indexOf("highcharts-pattern-")).length){for(let t of this.series)if(t.visible)for(let e of t.points){let t=e.options&&e.options.color;t&&t.pattern&&(t.pattern._width="defer",t.pattern._height="defer")}this.redraw(!1)}}function M(){let t={},e=this.renderer,r=(e.defIds||[]).filter(t=>t.indexOf&&0===t.indexOf("highcharts-pattern-"));if(r.length)for(let i of([].forEach.call(this.renderTo.querySelectorAll('[color^="url("], [fill^="url("], [stroke^="url("]'),r=>{let i=r.getAttribute("fill")||r.getAttribute("color")||r.getAttribute("stroke");i&&(t[i.replace(e.url,"").replace("url(#","").replace(")","")]=!0)}),r))!t[i]&&(c(e.defIds,i),e.patternElements[i]&&(e.patternElements[i].destroy(),delete e.patternElements[i]))}function b(){let t=this.options.color;t&&t.pattern&&("string"==typeof t.pattern.path&&(t.pattern.path={d:t.pattern.path}),this.color=this.options.color=f(this.series.options.color,t))}function _(t){let e=t.args[0],r=t.args[1],i=t.args[2],a=this.chartIndex||0,o=e.pattern,n="#333333";if(void 0!==e.patternIndex&&x&&(o=x[e.patternIndex]),!o)return!0;if(o.image||"string"==typeof o.path||o.path&&o.path.d){let t=i.parentNode&&i.parentNode.getAttribute("class");t=t&&t.indexOf("highcharts-legend")>-1,("defer"===o._width||"defer"===o._height)&&A.call({graphic:{element:i}},o),(t||!o.id)&&((o=f({},o)).id="highcharts-pattern-"+a+"-"+y(o)+y(o,!0)),this.addPattern(o,!this.forExport&&g(o.animation,this.globalAnimation,{duration:100})),n=`url(${this.url}#${o.id+(this.forExport?"-export":"")})`}else n=o.color||n;return i.setAttribute(r,n),e.toString=function(){return n},!1}function L(){let t=this.chart.isResizing;if(this.isDirtyData||t||!this.chart.hasRendered)for(let e of this.points){let r=e.options&&e.options.color;r&&r.pattern&&(t&&!(e.shapeArgs&&e.shapeArgs.width&&e.shapeArgs.height)?(r.pattern._width="defer",r.pattern._height="defer"):e.calculatePatternDimensions(r.pattern))}}function A(t){if(t.width&&t.height)return;let e=this.graphic&&(this.graphic.getBBox&&this.graphic.getBBox(!0)||this.graphic.element&&this.graphic.element.getBBox())||{},r=this.shapeArgs;if(r&&(e.width=r.width||e.width,e.height=r.height||e.height,e.x=r.x||e.x,e.y=r.y||e.y),t.image){if(!e.width||!e.height){t._width="defer",t._height="defer";let e=this.series.chart.mapView&&this.series.chart.mapView.getSVGTransform().scaleY;p(e)&&e<0&&(t._inverted=!0);return}t.aspectRatio&&(e.aspectRatio=e.width/e.height,t.aspectRatio>e.aspectRatio?e.aspectWidth=e.height*t.aspectRatio:e.aspectHeight=e.width/t.aspectRatio),t._width=t.width||Math.ceil(e.aspectWidth||e.width),t._height=t.height||Math.ceil(e.aspectHeight||e.height)}t.width||(t._x=t.x||0,t._x+=e.x-Math.round(e.aspectWidth?Math.abs(e.aspectWidth-e.width)/2:0)),t.height||(t._y=t.y||0,t._y+=e.y-Math.round(e.aspectHeight?Math.abs(e.aspectHeight-e.height)/2:0))}function C(t,e){let r=g(e,!0),i=h(r),a=t.color||"#333333",o=t.height||("number"==typeof t._height?t._height:0)||32,s=t.width||("number"==typeof t._width?t._width:0)||32,l,p=t.id,c;if(!p&&(this.idCounter=this.idCounter||0,p="highcharts-pattern-"+this.idCounter+"-"+(this.chartIndex||0),++this.idCounter),this.forExport&&(p+="-export"),this.defIds=this.defIds||[],this.defIds.indexOf(p)>-1)return;this.defIds.push(p);let d={id:p,patternUnits:"userSpaceOnUse",patternContentUnits:t.patternContentUnits||"userSpaceOnUse",width:s,height:o,x:t._x||t.x||0,y:t._y||t.y||0};t._inverted&&(d.patternTransform="scale(1, -1)",t.patternTransform&&(t.patternTransform+=" scale(1, -1)")),t.patternTransform&&(d.patternTransform=t.patternTransform);let f=this.createElement("pattern").attr(d).add(this.defs);if(f.id=p,t.path){let e;c=n().isObject(t.path)?t.path:{d:t.path},t.backgroundColor&&(e=t.backgroundColor,this.rect(0,0,s,o).attr({fill:e}).add(f)),l={d:c.d},this.styledMode||(l.stroke=c.stroke||a,l["stroke-width"]=g(c.strokeWidth,2),l.fill=c.fill||"none"),c.transform&&(l.transform=c.transform),this.createElement("path").attr(l).add(f),f.color=a}else t.image&&(r?this.image(t.image,0,0,s,o,function(){this.animate({opacity:g(t.opacity,1)},i),u(this.element,"load")}).attr({opacity:0}).add(f):this.image(t.image,0,0,s,o).add(f));return t.image&&r||void 0===t.opacity||[].forEach.call(f.element.childNodes,e=>{e.setAttribute("opacity",t.opacity)}),this.patternElements=this.patternElements||{},this.patternElements[p]=f,f}function E(t){let e=this.options.color;e&&e.pattern&&!e.pattern.color?(delete this.options.color,t.apply(this,[].slice.call(arguments,1)),e.pattern.color=this.color,this.color=this.options.color=e):t.apply(this,[].slice.call(arguments,1))}function O(){if(!this.chart?.mapView)return;let t=this.chart.renderer,e=t.patternElements;t.defIds?.length&&e&&this.points.filter(function(t){return!!t.graphic&&(t.graphic.element.hasAttribute("fill")||t.graphic.element.hasAttribute("color")||t.graphic.element.hasAttribute("stroke"))&&!t.options.color?.pattern?.image&&!!t.group?.scaleX&&!!t.group?.scaleY}).map(function(e){return{id:(e.graphic?.element.getAttribute("fill")||e.graphic?.element.getAttribute("color")||e.graphic?.element.getAttribute("stroke")||"").replace(t.url,"").replace("url(#","").replace(")",""),x:e.group?.scaleX||1,y:e.group?.scaleY||1}}).filter(function(t,e,r){return""!==t.id&&-1!==t.id.indexOf("highcharts-pattern-")&&!r.some(function(r,i){return r.id===t.id&&i<e})}).forEach(function(t){let r=t.id;e[r].scaleX=1/t.x,e[r].scaleY=1/t.y,e[r].updateTransform("patternTransform")})}let v={compose:function(t,e,r){let i=e.prototype.pointClass,a=i.prototype;a.calculatePatternDimensions||(l(t,"endResize",w),l(t,"redraw",M),d(a,{calculatePatternDimensions:A}),l(i,"afterInit",b),l(e,"render",L),m(e.prototype,"getColor",E),l(e,"afterRender",O),l(e,"mapZoomComplete",O),d(r.prototype,{addPattern:C}),l(r,"complexColor",_))},patterns:x},I=n();I.patterns=v.patterns,v.compose(I.Chart,I.Series,I.SVGRenderer);let R=n();return a.default})());