!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/color-axis
 * @requires highcharts
 *
 * ColorAxis module
 *
 * (c) 2012-2025 Pawe<PERSON>
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.Axis,t._Highcharts.Color,t._Highcharts.LegendSymbol,t._Highcharts.SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/modules/coloraxis",["highcharts/highcharts"],function(t){return e(t,t.Axis,t.Color,t.LegendSymbol,t.SeriesRegistry)}):"object"==typeof exports?exports["highcharts/modules/coloraxis"]=e(t._Highcharts,t._Highcharts.Axis,t._Highcharts.Color,t._Highcharts.LegendSymbol,t._Highcharts.SeriesRegistry):t.Highcharts=e(t.Highcharts,t.Highcharts.Axis,t.Highcharts.Color,t.Highcharts.LegendSymbol,t.Highcharts.SeriesRegistry)}("undefined"==typeof window?this:window,(t,e,s,i,o)=>(()=>{"use strict";var r,l,a={500:t=>{t.exports=i},512:t=>{t.exports=o},532:t=>{t.exports=e},620:t=>{t.exports=s},944:e=>{e.exports=t}},h={};function n(t){var e=h[t];if(void 0!==e)return e.exports;var s=h[t]={exports:{}};return a[t](s,s.exports,n),s.exports}n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},n.d=(t,e)=>{for(var s in e)n.o(e,s)&&!n.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:e[s]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var d={};n.d(d,{default:()=>N});var c=n(944),p=n.n(c),g=n(532),f=n.n(g),u=n(620),m=n.n(u);let{parse:x}=m(),{addEvent:y,extend:b,merge:C,pick:v,splat:A}=p();!function(t){let e;function s(){let{userOptions:t}=this;this.colorAxis=[],t.colorAxis&&(t.colorAxis=A(t.colorAxis),t.colorAxis.map(t=>new e(this,t)))}function i(t){let e=this.chart.colorAxis||[],s=e=>{let s=t.allItems.indexOf(e);-1!==s&&(this.destroyItem(t.allItems[s]),t.allItems.splice(s,1))},i=[],o,r;for(e.forEach(function(t){o=t.options,o?.showInLegend&&(o.dataClasses&&o.visible?i=i.concat(t.getDataClassLegendSymbols()):o.visible&&i.push(t),t.series.forEach(function(t){(!t.options.showInLegend||o.dataClasses)&&("point"===t.options.legendType?t.points.forEach(function(t){s(t)}):s(t))}))}),r=i.length;r--;)t.allItems.unshift(i[r])}function o(t){t.visible&&t.item.legendColor&&t.item.legendItem.symbol.attr({fill:t.item.legendColor})}function r(t){this.chart.colorAxis?.forEach(e=>{e.update({},t.redraw)})}function l(){(this.chart.colorAxis?.length||this.colorAttribs)&&this.translateColors()}function a(){let t=this.axisTypes;t?-1===t.indexOf("colorAxis")&&t.push("colorAxis"):this.axisTypes=["colorAxis"]}function h(t){let e=this,s=t?"show":"hide";e.visible=e.options.visible=!!t,["graphic","dataLabel"].forEach(function(t){e[t]&&e[t][s]()}),this.series.buildKDTree()}function n(){let t=this,e=this.getPointsCollection(),s=this.options.nullColor,i=this.colorAxis,o=this.colorKey;e.forEach(e=>{let r=e.getNestedProperty(o),l=e.options.color||(e.isNull||null===e.value?s:i&&void 0!==r?i.toColor(r,e):e.color||t.color);l&&e.color!==l&&(e.color=l,"point"===t.options.legendType&&e.legendItem&&e.legendItem.label&&t.chart.legend.colorizeItem(e,e.visible))})}function d(){this.elem.attr("fill",x(this.start).tweenTo(x(this.end),this.pos),void 0,!0)}function c(){this.elem.attr("stroke",x(this.start).tweenTo(x(this.end),this.pos),void 0,!0)}t.compose=function(t,p,g,f,u){let m=p.prototype,x=g.prototype,A=u.prototype;m.collectionsWithUpdate.includes("colorAxis")||(e=t,m.collectionsWithUpdate.push("colorAxis"),m.collectionsWithInit.colorAxis=[m.addColorAxis],y(p,"afterCreateAxes",s),function(t){let s=t.prototype.createAxis;t.prototype.createAxis=function(t,i){if("colorAxis"!==t)return s.apply(this,arguments);let o=new e(this,C(i.axis,{index:this[t].length,isX:!1}));return this.isDirtyLegend=!0,this.axes.forEach(t=>{t.series=[]}),this.series.forEach(t=>{t.bindAxes(),t.isDirtyData=!0}),v(i.redraw,!0)&&this.redraw(i.animation),o}}(p),x.fillSetter=d,x.strokeSetter=c,y(f,"afterGetAllItems",i),y(f,"afterColorizeItem",o),y(f,"afterUpdate",r),b(A,{optionalAxis:"colorAxis",translateColors:n}),b(A.pointClass.prototype,{setVisible:h}),y(u,"afterTranslate",l,{order:1}),y(u,"bindAxes",a))},t.pointSetVisible=h}(r||(r={}));let I=r,{parse:L}=m(),{merge:w}=p();!function(t){t.initDataClasses=function(t){let e=this.chart,s=this.legendItem=this.legendItem||{},i=this.options,o=t.dataClasses||[],r,l,a=e.options.chart.colorCount,h=0,n;this.dataClasses=l=[],s.labels=[];for(let t=0,s=o.length;t<s;++t)r=w(r=o[t]),l.push(r),(e.styledMode||!r.color)&&("category"===i.dataClassColor?(e.styledMode||(a=(n=e.options.colors||[]).length,r.color=n[h]),r.colorIndex=h,++h===a&&(h=0)):r.color=L(i.minColor).tweenTo(L(i.maxColor),s<2?.5:t/(s-1)))},t.initStops=function(){let t=this.options,e=this.stops=t.stops||[[0,t.minColor||""],[1,t.maxColor||""]];for(let t=0,s=e.length;t<s;++t)e[t].color=L(e[t][1])},t.normalizedValue=function(t){let e=this.max||0,s=this.min||0;return this.logarithmic&&(t=this.logarithmic.log2lin(t)),1-(e-t)/(e-s||1)},t.toColor=function(t,e){let s,i,o,r,l,a,h=this.dataClasses,n=this.stops;if(h){for(a=h.length;a--;)if(i=(l=h[a]).from,o=l.to,(void 0===i||t>=i)&&(void 0===o||t<=o)){r=l.color,e&&(e.dataClass=a,e.colorIndex=l.colorIndex);break}}else{for(s=this.normalizedValue(t),a=n.length;a--&&!(s>n[a][0]););i=n[a]||n[a+1],s=1-((o=n[a+1]||i)[0]-s)/(o[0]-i[0]||1),r=i.color.tweenTo(o.color,s)}return r}}(l||(l={}));let S=l;var M=n(500),P=n.n(M),D=n(512),H=n.n(D);let{defaultOptions:k}=p(),{series:z}=H(),{defined:E,extend:O,fireEvent:V,isArray:T,isNumber:_,merge:W,pick:j,relativeLength:K}=p();k.colorAxis=W(k.xAxis,{lineWidth:0,minPadding:0,maxPadding:0,gridLineColor:"#ffffff",gridLineWidth:1,tickPixelInterval:72,startOnTick:!0,endOnTick:!0,offset:0,marker:{animation:{duration:50},width:.01,color:"#999999"},labels:{distance:8,overflow:"justify",rotation:0},minColor:"#e6e9ff",maxColor:"#0022ff",tickLength:5,showInLegend:!0});class R extends f(){static compose(t,e,s,i){I.compose(R,t,e,s,i)}constructor(t,e){super(t,e),this.coll="colorAxis",this.visible=!0,this.init(t,e)}init(t,e){let s=t.options.legend||{},i=e.layout?"vertical"!==e.layout:"vertical"!==s.layout;this.side=e.side||i?2:1,this.reversed=e.reversed||!i,this.opposite=!i,super.init(t,e,"colorAxis"),this.userOptions=e,T(t.userOptions.colorAxis)&&(t.userOptions.colorAxis[this.index]=e),e.dataClasses&&this.initDataClasses(e),this.initStops(),this.horiz=i,this.zoomEnabled=!1}hasData(){return!!(this.tickPositions||[]).length}setTickPositions(){if(!this.dataClasses)return super.setTickPositions()}setOptions(t){let e=W(k.colorAxis,t,{showEmpty:!1,title:null,visible:this.chart.options.legend.enabled&&!1!==t.visible});super.setOptions(e),this.options.crosshair=this.options.marker}setAxisSize(){let t=this.chart,e=this.legendItem?.symbol,{width:s,height:i}=this.getSize();e&&(this.left=+e.attr("x"),this.top=+e.attr("y"),this.width=s=+e.attr("width"),this.height=i=+e.attr("height"),this.right=t.chartWidth-this.left-s,this.bottom=t.chartHeight-this.top-i,this.pos=this.horiz?this.left:this.top),this.len=(this.horiz?s:i)||R.defaultLegendLength}getOffset(){let t=this.legendItem?.group,e=this.chart.axisOffset[this.side];if(t){this.axisParent=t,super.getOffset();let s=this.chart.legend;s.allItems.forEach(function(t){t instanceof R&&t.drawLegendSymbol(s,t)}),s.render(),this.chart.getMargins(!0),this.chart.series.some(t=>t.isDrilling)||(this.isDirty=!0),this.added||(this.added=!0,this.labelLeft=0,this.labelRight=this.width),this.chart.axisOffset[this.side]=e}}setLegendColor(){let t=this.horiz,e=this.reversed,s=+!!e,i=+!e,o=t?[s,0,i,0]:[0,i,0,s];this.legendColor={linearGradient:{x1:o[0],y1:o[1],x2:o[2],y2:o[3]},stops:this.stops}}drawLegendSymbol(t,e){let s=e.legendItem||{},i=t.padding,o=t.options,r=this.options.labels,l=j(o.itemDistance,10),a=this.horiz,{width:h,height:n}=this.getSize(),d=j(o.labelPadding,a?16:30);this.setLegendColor(),s.symbol||(s.symbol=this.chart.renderer.symbol("roundedRect").attr({r:o.symbolRadius??3,zIndex:1}).add(s.group)),s.symbol.attr({x:0,y:(t.baseline||0)-11,width:h,height:n}),s.labelWidth=h+i+(a?l:j(r.x,r.distance)+(this.maxLabelLength||0)),s.labelHeight=n+i+(a?d:0)}setState(t){this.series.forEach(function(e){e.setState(t)})}setVisible(){}getSeriesExtremes(){let t=this.series,e,s,i,o,r=t.length;for(this.dataMin=1/0,this.dataMax=-1/0;r--;){for(let l of(s=(o=t[r]).colorKey=j(o.options.colorKey,o.colorKey,o.pointValKey,o.zoneAxis,"y"),i=o[s+"Min"]&&o[s+"Max"],[s,"value","y"]))if((e=o.getColumn(l)).length)break;if(i)o.minColorValue=o[s+"Min"],o.maxColorValue=o[s+"Max"];else{let t=z.prototype.getExtremes.call(o,e);o.minColorValue=t.dataMin,o.maxColorValue=t.dataMax}E(o.minColorValue)&&E(o.maxColorValue)&&(this.dataMin=Math.min(this.dataMin,o.minColorValue),this.dataMax=Math.max(this.dataMax,o.maxColorValue)),i||z.prototype.applyExtremes.call(o)}}drawCrosshair(t,e){let s,i=this.legendItem||{},o=e?.plotX,r=e?.plotY,l=this.pos,a=this.len;e&&((s=this.toPixels(e.getNestedProperty(e.series.colorKey)))<l?s=l-2:s>l+a&&(s=l+a+2),e.plotX=s,e.plotY=this.len-s,super.drawCrosshair(t,e),e.plotX=o,e.plotY=r,this.cross&&!this.cross.addedToColorAxis&&i.group&&(this.cross.addClass("highcharts-coloraxis-marker").add(i.group),this.cross.addedToColorAxis=!0,this.chart.styledMode||"object"!=typeof this.crosshair||this.cross.attr({fill:this.crosshair.color})))}getPlotLinePath(t){let e=this.left,s=t.translatedValue,i=this.top;return _(s)?this.horiz?[["M",s-4,i-6],["L",s+4,i-6],["L",s,i],["Z"]]:[["M",e,s],["L",e-6,s+6],["L",e-6,s-6],["Z"]]:super.getPlotLinePath(t)}update(t,e){let s=this.chart.legend;this.series.forEach(t=>{t.isDirtyData=!0}),(t.dataClasses&&s.allItems||this.dataClasses)&&this.destroyItems(),super.update(t,e),this.legendItem?.label&&(this.setLegendColor(),s.colorizeItem(this,!0))}destroyItems(){let t=this.chart,e=this.legendItem||{};if(e.label)t.legend.destroyItem(this);else if(e.labels)for(let s of e.labels)t.legend.destroyItem(s);t.isDirtyLegend=!0}destroy(){this.chart.isDirtyLegend=!0,this.destroyItems(),super.destroy(...[].slice.call(arguments))}remove(t){this.destroyItems(),super.remove(t)}getDataClassLegendSymbols(){let t,e=this,s=e.chart,i=e.legendItem&&e.legendItem.labels||[],o=s.options.legend,r=j(o.valueDecimals,-1),l=j(o.valueSuffix,""),a=t=>e.series.reduce((e,s)=>(e.push(...s.points.filter(e=>e.dataClass===t)),e),[]);return i.length||e.dataClasses.forEach((o,h)=>{let n=o.from,d=o.to,{numberFormatter:c}=s,p=!0;t="",void 0===n?t="< ":void 0===d&&(t="> "),void 0!==n&&(t+=c(n,r)+l),void 0!==n&&void 0!==d&&(t+=" - "),void 0!==d&&(t+=c(d,r)+l),i.push(O({chart:s,name:t,options:{},drawLegendSymbol:P().rectangle,visible:!0,isDataClass:!0,setState:t=>{for(let e of a(h))e.setState(t)},setVisible:function(){this.visible=p=e.visible=!p;let t=[];for(let e of a(h))e.setVisible(p),e.hiddenInDataClass=!p,-1===t.indexOf(e.series)&&t.push(e.series);s.legend.colorizeItem(this,p),t.forEach(t=>{V(t,"afterDataClassLegendClick")})}},o))}),i}getSize(){let{chart:t,horiz:e}=this,{height:s,width:i}=this.options,{legend:o}=t.options;return{width:j(E(i)?K(i,t.chartWidth):void 0,o?.symbolWidth,e?R.defaultLegendLength:12),height:j(E(s)?K(s,t.chartHeight):void 0,o?.symbolHeight,e?12:R.defaultLegendLength)}}}R.defaultLegendLength=200,R.keepProps=["legendItem"],O(R.prototype,S),Array.prototype.push.apply(f().keepProps,R.keepProps);let X=p();X.ColorAxis=X.ColorAxis||R,X.ColorAxis.compose(X.Chart,X.Fx,X.Legend,X.Series);let N=p();return d.default})());