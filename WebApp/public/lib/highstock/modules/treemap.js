!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/treemap
 * @requires highcharts
 *
 * (c) 2014-2025 Highsoft AS
 * Authors: <AUTHORS>
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.Templating,t._Highcharts.Color,t._Highcharts.SeriesRegistry,t._Highcharts.SVGElement,t._Highcharts.Series):"function"==typeof define&&define.amd?define("highcharts/modules/treemap",["highcharts/highcharts"],function(t){return e(t,t.Templating,t.Color,t.SeriesRegistry,t.SVGElement,t.Series)}):"object"==typeof exports?exports["highcharts/modules/treemap"]=e(t._Highcharts,t._Highcharts.Templating,t._Highcharts.Color,t._Highcharts.SeriesRegistry,t._Highcharts.SVGElement,t._Highcharts.Series):t.Highcharts=e(t.Highcharts,t.Highcharts.Templating,t.Highcharts.Color,t.Highcharts.SeriesRegistry,t.Highcharts.SVGElement,t.Highcharts.Series)}("undefined"==typeof window?this:window,(t,e,i,s,o,r)=>(()=>{"use strict";var l,a,n={28:t=>{t.exports=o},512:t=>{t.exports=s},620:t=>{t.exports=i},820:t=>{t.exports=r},944:e=>{e.exports=t},984:t=>{t.exports=e}},h={};function d(t){var e=h[t];if(void 0!==e)return e.exports;var i=h[t]={exports:{}};return n[t](i,i.exports,d),i.exports}d.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return d.d(e,{a:e}),e},d.d=(t,e)=>{for(var i in e)d.o(e,i)&&!d.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},d.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var p={};d.d(p,{default:()=>tk});var u=d(944),c=d.n(u);let g={lang:{mainBreadcrumb:"Main"},options:{buttonTheme:{fill:"none",height:18,padding:2,"stroke-width":0,zIndex:7,states:{select:{fill:"none"}},style:{color:"#334eff"}},buttonSpacing:5,floating:!1,format:void 0,relativeTo:"plotBox",rtl:!1,position:{align:"left",verticalAlign:"top",x:0,y:void 0},separator:{text:"/",style:{color:"#666666",fontSize:"0.8em"}},showFullPath:!0,style:{},useHTML:!1,zIndex:7}};var v=d(984);let{format:m}=d.n(v)(),{composed:b}=c(),{addEvent:f,defined:y,extend:x,fireEvent:w,isString:T,merge:L,objectEach:B,pick:A,pushUnique:P}=c();function S(){if(this.breadcrumbs){let t=this.resetZoomButton&&this.resetZoomButton.getBBox(),e=this.breadcrumbs.options;t&&"right"===e.position.align&&"plotBox"===e.relativeTo&&this.breadcrumbs.alignBreadcrumbsGroup(-t.width-e.buttonSpacing)}}function C(){this.breadcrumbs&&(this.breadcrumbs.destroy(),this.breadcrumbs=void 0)}function O(){let t=this.breadcrumbs;if(t&&!t.options.floating&&t.level){let e=t.options,i=e.buttonTheme,s=(i.height||0)+2*(i.padding||0)+e.buttonSpacing,o=e.position.verticalAlign;"bottom"===o?(this.marginBottom=(this.marginBottom||0)+s,t.yOffset=s):"middle"!==o?(this.plotTop+=s,t.yOffset=-s):t.yOffset=void 0}}function N(){this.breadcrumbs&&this.breadcrumbs.redraw()}function M(t){!0===t.resetSelection&&this.breadcrumbs&&this.breadcrumbs.alignBreadcrumbsGroup()}class R{static compose(t,e){P(b,"Breadcrumbs")&&(f(t,"destroy",C),f(t,"afterShowResetZoom",S),f(t,"getMargins",O),f(t,"redraw",N),f(t,"selection",M),x(e.lang,g.lang))}constructor(t,e){this.elementList={},this.isDirty=!0,this.level=0,this.list=[];let i=L(t.options.drilldown&&t.options.drilldown.drillUpButton,R.defaultOptions,t.options.navigation&&t.options.navigation.breadcrumbs,e);this.chart=t,this.options=i||{}}updateProperties(t){this.setList(t),this.setLevel(),this.isDirty=!0}setList(t){this.list=t}setLevel(){this.level=this.list.length&&this.list.length-1}getLevel(){return this.level}getButtonText(t){let e=this.chart,i=this.options,s=e.options.lang,o=A(i.format,i.showFullPath?"{level.name}":"← {level.name}"),r=s&&A(s.drillUpText,s.mainBreadcrumb),l=i.formatter&&i.formatter(t)||m(o,{level:t.levelOptions},e)||"";return(T(l)&&!l.length||"← "===l)&&y(r)&&(l=i.showFullPath?r:"← "+r),l}redraw(){this.isDirty&&this.render(),this.group&&this.group.align(),this.isDirty=!1}render(){let t=this.chart,e=this.options;!this.group&&e&&(this.group=t.renderer.g("breadcrumbs-group").addClass("highcharts-no-tooltip highcharts-breadcrumbs").attr({zIndex:e.zIndex}).add()),e.showFullPath?this.renderFullPathButtons():this.renderSingleButton(),this.alignBreadcrumbsGroup()}renderFullPathButtons(){this.destroySingleButton(),this.resetElementListState(),this.updateListElements(),this.destroyListElements()}renderSingleButton(){let t=this.chart,e=this.list,i=this.options.buttonSpacing;this.destroyListElements();let s=this.group?this.group.getBBox().width:i,o=e[e.length-2];!t.drillUpButton&&this.level>0?t.drillUpButton=this.renderButton(o,s,i):t.drillUpButton&&(this.level>0?this.updateSingleButton():this.destroySingleButton())}alignBreadcrumbsGroup(t){if(this.group){let e=this.options,i=e.buttonTheme,s=e.position,o="chart"===e.relativeTo||"spacingBox"===e.relativeTo?void 0:"plotBox",r=this.group.getBBox(),l=2*(i.padding||0)+e.buttonSpacing;s.width=r.width+l,s.height=r.height+l;let a=L(s);t&&(a.x+=t),this.options.rtl&&(a.x+=s.width),a.y=A(a.y,this.yOffset,0),this.group.align(a,!0,o)}}renderButton(t,e,i){let s=this,o=this.chart,r=s.options,l=L(r.buttonTheme),a=o.renderer.button(s.getButtonText(t),e,i,function(e){let i,o=r.events&&r.events.click;o&&(i=o.call(s,e,t)),!1!==i&&(r.showFullPath?e.newLevel=t.level:e.newLevel=s.level-1,w(s,"up",e))},l).addClass("highcharts-breadcrumbs-button").add(s.group);return o.styledMode||a.attr(r.style),a}renderSeparator(t,e){let i=this.chart,s=this.options.separator,o=i.renderer.label(s.text,t,e,void 0,void 0,void 0,!1).addClass("highcharts-breadcrumbs-separator").add(this.group);return i.styledMode||o.css(s.style),o}update(t){L(!0,this.options,t),this.destroy(),this.isDirty=!0}updateSingleButton(){let t=this.chart,e=this.list[this.level-1];t.drillUpButton&&t.drillUpButton.attr({text:this.getButtonText(e)})}destroy(){this.destroySingleButton(),this.destroyListElements(!0),this.group&&this.group.destroy(),this.group=void 0}destroyListElements(t){let e=this.elementList;B(e,(i,s)=>{(t||!e[s].updated)&&((i=e[s]).button&&i.button.destroy(),i.separator&&i.separator.destroy(),delete i.button,delete i.separator,delete e[s])}),t&&(this.elementList={})}destroySingleButton(){this.chart.drillUpButton&&(this.chart.drillUpButton.destroy(),this.chart.drillUpButton=void 0)}resetElementListState(){B(this.elementList,t=>{t.updated=!1})}updateListElements(){let t=this.elementList,e=this.options.buttonSpacing,i=this.list,s=this.options.rtl,o=s?-1:1,r=function(t,e){return o*t.getBBox().width+o*e},l=function(t,e,i){t.translate(e-t.getBBox().width,i)},a=this.group?r(this.group,e):e,n,h;for(let d=0,p=i.length;d<p;++d){let u,c,g=d===p-1;t[(h=i[d]).level]?(u=(n=t[h.level]).button,n.separator||g?n.separator&&g&&(n.separator.destroy(),delete n.separator):(a+=o*e,n.separator=this.renderSeparator(a,e),s&&l(n.separator,a,e),a+=r(n.separator,e)),t[h.level].updated=!0):(u=this.renderButton(h,a,e),s&&l(u,a,e),a+=r(u,e),g||(c=this.renderSeparator(a,e),s&&l(c,a,e),a+=r(c,e)),t[h.level]={button:u,separator:c,updated:!0}),u&&u.setState(2*!!g)}}}R.defaultOptions=g.options;var D=d(620),I=d.n(D),H=d(512),G=d.n(H),E=d(28),V=d.n(E);let{column:{prototype:k}}=G().seriesTypes,{addEvent:U,defined:W}=c();!function(t){function e(t){let e=this.series,i=e.chart.renderer;this.moveToTopOnHover&&this.graphic&&(e.stateMarkerGraphic||(e.stateMarkerGraphic=new(V())(i,"use").css({pointerEvents:"none"}).add(this.graphic.parentGroup)),t?.state==="hover"?(this.graphic.attr({id:this.id}),e.stateMarkerGraphic.attr({href:`${i.url}#${this.id}`,visibility:"visible"})):e.stateMarkerGraphic.attr({href:""}))}t.pointMembers={dataLabelOnNull:!0,moveToTopOnHover:!0,isValid:function(){return null!==this.value&&this.value!==1/0&&this.value!==-1/0&&(void 0===this.value||!isNaN(this.value))}},t.seriesMembers={colorKey:"value",axisTypes:["xAxis","yAxis","colorAxis"],parallelArrays:["x","y","value"],pointArrayMap:["value"],trackerGroups:["group","markerGroup","dataLabelsGroup"],colorAttribs:function(t){let e={};return W(t.color)&&(!t.state||"normal"===t.state)&&(e[this.colorProp||"fill"]=t.color),e},pointAttribs:k.pointAttribs},t.compose=function(t){return U(t.prototype.pointClass,"afterSetState",e),t}}(l||(l={}));let F=l;var _=d(820),z=d.n(_);let j=class{constructor(t,e,i,s){this.height=t,this.width=e,this.plot=s,this.direction=i,this.startDirection=i,this.total=0,this.nW=0,this.lW=0,this.nH=0,this.lH=0,this.elArr=[],this.lP={total:0,lH:0,nH:0,lW:0,nW:0,nR:0,lR:0,aspectRatio:function(t,e){return Math.max(t/e,e/t)}}}addElement(t){this.lP.total=this.elArr[this.elArr.length-1],this.total=this.total+t,0===this.direction?(this.lW=this.nW,this.lP.lH=this.lP.total/this.lW,this.lP.lR=this.lP.aspectRatio(this.lW,this.lP.lH),this.nW=this.total/this.height,this.lP.nH=this.lP.total/this.nW,this.lP.nR=this.lP.aspectRatio(this.nW,this.lP.nH)):(this.lH=this.nH,this.lP.lW=this.lP.total/this.lH,this.lP.lR=this.lP.aspectRatio(this.lP.lW,this.lH),this.nH=this.total/this.width,this.lP.nW=this.lP.total/this.nH,this.lP.nR=this.lP.aspectRatio(this.lP.nW,this.nH)),this.elArr.push(t)}reset(){this.nW=0,this.lW=0,this.elArr=[],this.total=0}},$={draw:function(t,e){let{animatableAttribs:i,onComplete:s,css:o,renderer:r}=e,l=t.series&&t.series.chart.hasRendered?void 0:t.series&&t.series.options.animation,a=t.graphic;if(e.attribs={...e.attribs,class:t.getClassName()},t.shouldDraw())a||(t.graphic=a="text"===e.shapeType?r.text():"image"===e.shapeType?r.image(e.imageUrl||"").attr(e.shapeArgs||{}):r[e.shapeType](e.shapeArgs||{}),a.add(e.group)),o&&a.css(o),a.attr(e.attribs).animate(i,!e.isNew&&l,s);else if(a){let e=()=>{t.graphic=a=a&&a.destroy(),"function"==typeof s&&s()};Object.keys(i).length?a.animate(i,void 0,()=>e()):e()}}},{pie:{prototype:{pointClass:K}},scatter:{prototype:{pointClass:Y}}}=G().seriesTypes,{extend:Z,isNumber:q,pick:X}=c();class J extends Y{constructor(){super(...arguments),this.groupedPointsAmount=0,this.shapeType="rect"}draw(t){$.draw(this,t)}getClassName(){let t=this.series,e=t.options,i=super.getClassName();return this.node.level<=t.nodeMap[t.rootNode].level&&this.node.children.length?i+=" highcharts-above-level":this.node.isGroup||this.node.isLeaf||t.nodeMap[t.rootNode].isGroup||X(e.interactByLeaf,!e.allowTraversingTree)?this.node.isGroup||this.node.isLeaf||t.nodeMap[t.rootNode].isGroup||(i+=" highcharts-internal-node"):i+=" highcharts-internal-node-interactive",i}isValid(){return!!(this.id||q(this.value))}setState(t){super.setState.apply(this,arguments),this.graphic&&this.graphic.attr({zIndex:+("hover"===t)})}shouldDraw(){return q(this.plotY)&&null!==this.y}}Z(J.prototype,{setVisible:K.prototype.setVisible});let{isString:Q}=c(),tt={allowTraversingTree:!1,animationLimit:250,borderRadius:0,showInLegend:!1,marker:void 0,colorByPoint:!1,dataLabels:{enabled:!0,formatter:function(){let t=this&&this.point?this.point:{};return Q(t.name)?t.name:""},headers:!1,inside:!0,padding:2,verticalAlign:"middle",style:{textOverflow:"ellipsis"}},tooltip:{headerFormat:"",pointFormat:"<b>{point.name}</b>: {point.value}<br/>",clusterFormat:"+ {point.groupedPointsAmount} more...<br/>"},ignoreHiddenPoint:!0,layoutAlgorithm:"sliceAndDice",layoutStartingDirection:"vertical",alternateStartingDirection:!1,levelIsConstant:!0,traverseUpButton:{position:{align:"right",x:-10,y:10}},borderColor:"#e6e6e6",borderWidth:1,colorKey:"colorValue",opacity:.15,states:{hover:{borderColor:"#999999",brightness:.1*!G().seriesTypes.heatmap,halo:!1,opacity:.75,shadow:!1}},legendSymbol:"rectangle",traverseToLeaf:!1,cluster:{className:void 0,color:void 0,enabled:!1,pixelWidth:void 0,pixelHeight:void 0,name:void 0,reductionFactor:void 0,minimumClusterSize:5,layoutAlgorithm:{distance:0,gridSize:0,kmeansThreshold:0},marker:{lineWidth:0,radius:0}}};(a||(a={})).recursive=function t(e,i,s){let o=i.call(s||this,e);!1!==o&&t(o,i,s)};let te=a,{extend:ti,isArray:ts,isNumber:to,isObject:tr,merge:tl,pick:ta,relativeLength:tn}=c(),{parse:th}=I(),{composed:td,noop:tp}=c(),{column:tu,scatter:tc}=G().seriesTypes,{getColor:tg,getLevelOptions:tv,updateRootId:tm}={getColor:function(t,e){let i,s,o,r,l,a,n=e.index,h=e.mapOptionsToLevel,d=e.parentColor,p=e.parentColorIndex,u=e.series,c=e.colors,g=e.siblings,v=u.points,m=u.chart.options.chart;return t&&(i=v[t.i],s=h[t.level]||{},i&&s.colorByPoint&&(r=i.index%(c?c.length:m.colorCount),o=c&&c[r]),u.chart.styledMode||(l=ta(i&&i.options.color,s&&s.color,o,d&&(t=>{let e=s&&s.colorVariation;return e&&"brightness"===e.key&&n&&g?I().parse(t).brighten(e.to*(n/g)).get():t})(d),u.color)),a=ta(i&&i.options.colorIndex,s&&s.colorIndex,r,p,e.colorIndex)),{color:l,colorIndex:a}},getLevelOptions:function(t){let e,i,s,o,r,l,a={};if(tr(t))for(o=to(t.from)?t.from:1,l=t.levels,i={},e=tr(t.defaults)?t.defaults:{},ts(l)&&(i=l.reduce((t,i)=>{let s,r,l;return tr(i)&&to(i.level)&&(r=ta((l=tl({},i)).levelIsConstant,e.levelIsConstant),delete l.levelIsConstant,delete l.level,tr(t[s=i.level+(r?0:o-1)])?tl(!0,t[s],l):t[s]=l),t},{})),r=to(t.to)?t.to:1,s=0;s<=r;s++)a[s]=tl({},e,tr(i[s])?i[s]:{});return a},getNodeWidth:function(t,e){let{chart:i,options:s}=t,{nodeDistance:o=0,nodeWidth:r=0}=s,{plotSizeX:l=1}=i;if("auto"===r){if("string"==typeof o&&/%$/.test(o))return l/(e+parseFloat(o)/100*(e-1));let t=Number(o);return(l+t)/(e||1)-t}return tn(r,l)},setTreeValues:function t(e,i){let s=i.before,o=i.idRoot,r=i.mapIdToNode[o],l=!1!==i.levelIsConstant,a=i.points[e.i],n=a&&a.options||{},h=[],d=0;e.levelDynamic=e.level-(l?0:r.level),e.name=ta(a&&a.name,""),e.visible=o===e.id||!0===i.visible,"function"==typeof s&&(e=s(e,i)),e.children.forEach((s,o)=>{let r=ti({},i);ti(r,{index:o,siblings:e.children.length,visible:e.visible}),s=t(s,r),h.push(s),s.visible&&(d+=s.val)});let p=ta(n.value,d);return e.visible=p>=0&&(d>0||e.visible),e.children=h,e.childrenTotal=d,e.isLeaf=e.visible&&!d,e.val=p,e},updateRootId:function(t){let e,i;return tr(t)&&(i=tr(t.options)?t.options:{},e=ta(t.rootNode,i.rootId,""),tr(t.userOptions)&&(t.userOptions.rootId=e),t.rootNode=e),e}},{addEvent:tb,arrayMax:tf,clamp:ty,correctFloat:tx,crisp:tw,defined:tT,error:tL,extend:tB,fireEvent:tA,isArray:tP,isNumber:tS,isObject:tC,isString:tO,merge:tN,pick:tM,pushUnique:tR,splat:tD,stableSort:tI}=c();z().keepProps.push("simulation","hadOutsideDataLabels");let tH=!1;function tG(){let t,e=this.xAxis,i=this.yAxis;e&&i&&(this.is("treemap")?(t={endOnTick:!1,gridLineWidth:0,lineWidth:0,min:0,minPadding:0,max:100,maxPadding:0,startOnTick:!1,title:void 0,tickPositions:[]},tB(i.options,t),tB(e.options,t),tH=!0):tH&&(i.setOptions(i.userOptions),e.setOptions(e.userOptions),tH=!1))}class tE extends tc{constructor(){super(...arguments),this.simulation=0}static compose(t){tR(td,"TreemapSeries")&&tb(t,"afterBindAxes",tG)}algorithmCalcPoints(t,e,i,s){let o=i.plot,r=i.elArr.length-1,l,a,n,h,d=i.lW,p=i.lH,u,c=0;for(let t of(e?(d=i.nW,p=i.nH):u=i.elArr[r],i.elArr))(e||c<r)&&(0===i.direction?(l=o.x,a=o.y,h=t/(n=d)):(l=o.x,a=o.y,n=t/(h=p)),s.push({x:l,y:a,width:n,height:tx(h)}),0===i.direction?o.y=o.y+h:o.x=o.x+n),c+=1;i.reset(),0===i.direction?i.width=i.width-d:i.height=i.height-p,o.y=o.parent.y+(o.parent.height-i.height),o.x=o.parent.x+(o.parent.width-i.width),t&&(i.direction=1-i.direction),e||i.addElement(u)}algorithmFill(t,e,i){let s=[],o,r=e.direction,l=e.x,a=e.y,n=e.width,h=e.height,d,p,u,c;for(let g of i)o=e.width*e.height*(g.val/e.val),d=l,p=a,0===r?(n-=u=o/(c=h),l+=u):(h-=c=o/(u=n),a+=c),s.push({x:d,y:p,width:u,height:c,direction:0,val:0}),t&&(r=1-r);return s}algorithmLowAspectRatio(t,e,i){let s=[],o={x:e.x,y:e.y,parent:e},r=e.direction,l=i.length-1,a=new j(e.height,e.width,r,o),n,h=0;for(let r of i)n=e.width*e.height*(r.val/e.val),a.addElement(n),a.lP.nR>a.lP.lR&&this.algorithmCalcPoints(t,!1,a,s,o),h===l&&this.algorithmCalcPoints(t,!0,a,s,o),++h;return s}alignDataLabel(t,e,i){tu.prototype.alignDataLabel.apply(this,arguments),t.dataLabel&&t.dataLabel.attr({zIndex:(t.node.zIndex||0)+1})}applyTreeGrouping(){let t=this,e=t.parentList||{},{cluster:i}=t.options,s=i?.minimumClusterSize||5;if(i?.enabled){let o={},r=t=>{if(t?.point?.shapeArgs){let{width:e=0,height:s=0}=t.point.shapeArgs,{pixelWidth:r=0,pixelHeight:l=0}=i,a=tT(l),n=l?r*l:r*r;(e<r||s<(a?l:r)||e*s<n)&&!t.isGroup&&tT(t.parent)&&(o[t.parent]||(o[t.parent]=[]),o[t.parent].push(t))}t?.children.forEach(t=>{r(t)})};for(let l in r(t.tree),o)o[l]&&o[l].length>s&&o[l].forEach(s=>{let o=e[l].indexOf(s.i);if(-1!==o){e[l].splice(o,1);let r=`highcharts-grouped-treemap-points-${s.parent||"root"}`,a=t.points.find(t=>t.id===r);if(!a){let s=t.pointClass,o=t.points.length;tB(a=new s(t,{className:i.className,color:i.color,id:r,index:o,isGroup:!0,value:0}),{formatPrefix:"cluster"}),t.points.push(a),e[l].push(o),e[r]=[]}let n=a.groupedPointsAmount+1,h=t.points[a.index].options.value||0,d=i.name||`+ ${n}`;t.points[a.index].groupedPointsAmount=n,t.points[a.index].options.value=h+(s.point.value||0),t.points[a.index].name=d,e[r].push(s.point.index)}});t.nodeMap={},t.nodeList=[],t.parentList=e;let l=t.buildTree("",-1,0,t.parentList);t.translate(l)}}calculateChildrenAreas(t,e){let i=this.options,s=this.mapOptionsToLevel[t.level+1],o=tM(s?.layoutAlgorithm&&this[s?.layoutAlgorithm]&&s.layoutAlgorithm,i.layoutAlgorithm),r=i.alternateStartingDirection,l=t.children.filter(e=>t.isGroup||!e.ignore),a=s?.groupPadding??i.groupPadding??0,n=this.nodeMap[this.rootNode];if(!o)return;let h=[],d=n.pointValues?.width||0,p=n.pointValues?.height||0;s?.layoutStartingDirection&&(e.direction=+("vertical"!==s.layoutStartingDirection)),h=this[o](e,l);let u=-1;for(let t of l){let i=h[++u];t===n&&(d=d||i.width,p=i.height);let s=a/(this.xAxis.len/p),o=a/(this.yAxis.len/p);if(t.values=tN(i,{val:t.childrenTotal,direction:r?1-e.direction:e.direction}),t.children.length&&t.point.dataLabels?.length){let e=tf(t.point.dataLabels.map(t=>t.options?.headers&&t.height||0))/(this.yAxis.len/p);e<t.values.height/2&&(t.values.y+=e,t.values.height-=e)}if(a){let e=Math.min(s,t.values.width/4),i=Math.min(o,t.values.height/4);t.values.x+=e,t.values.width-=2*e,t.values.y+=i,t.values.height-=2*i}t.pointValues=tN(i,{x:i.x/this.axisRatio,y:100-i.y-i.height,width:i.width/this.axisRatio}),t.children.length&&this.calculateChildrenAreas(t,t.values)}let c=(t,e=[],i=!0)=>(t.children.forEach(t=>{i&&t.isLeaf?e.push(t.point):i||t.isLeaf||e.push(t.point),t.children.length&&c(t,e,i)}),e);if("leaf"===i.nodeSizeBy&&t===n&&this.hasOutsideDataLabels&&!c(n,void 0,!1).some(t=>tS(t.options.value))&&!tS(n.point?.options.value)){let i=c(n),s=i.map(t=>t.options.value||0),o=i.map(({node:{pointValues:t}})=>t?t.width*t.height:0),r=s.reduce((t,e)=>t+e,0),l=o.reduce((t,e)=>t+e,0)/r,a=0,h=0;i.forEach((t,e)=>{let i=ty((s[e]?o[e]/s[e]:1)/l,.8,1.4),r=1-i;t.value&&(o[e]<20&&(r*=o[e]/20),r>h&&(h=r),r<a&&(a=r),t.simulatedValue=(t.simulatedValue||t.value)/i)}),(a<-.05||h>.05)&&this.simulation<10?(this.simulation++,this.setTreeValues(t),e.val=t.val,this.calculateChildrenAreas(t,e)):(i.forEach(t=>{delete t.simulatedValue}),this.setTreeValues(t),this.simulation=0)}}createList(t){let e=this.chart,i=e.breadcrumbs,s=[];if(i){let i=0;s.push({level:i,levelOptions:e.series[0]});let o=t.target.nodeMap[t.newRootId],r=[];for(;o.parent||""===o.parent;)r.push(o),o=t.target.nodeMap[o.parent];for(let t of r.reverse())s.push({level:++i,levelOptions:t});s.length<=1&&(s.length=0)}return s}drawDataLabels(){let t=this.mapOptionsToLevel,e=this.points.filter(function(t){return t.node.visible||tT(t.dataLabel)}),i=tD(this.options.dataLabels||{})[0]?.padding,s=e.some(t=>tS(t.plotY));for(let o of e){let e={},r={style:e},l=t[o.node.level];if((!o.node.isLeaf&&!o.node.isGroup||o.node.isGroup&&o.node.level<=this.nodeMap[this.rootNode].level)&&(r.enabled=!1),l?.dataLabels&&(tN(!0,r,tD(l.dataLabels)[0]),this.hasDataLabels=()=>!0),o.node.isLeaf?r.inside=!0:r.headers&&(r.verticalAlign="top"),o.shapeArgs&&s){let{height:t=0,width:s=0}=o.shapeArgs;if(s>32&&t>16&&o.shouldDraw()){let l=s-2*(r.padding||i||0);e.width=`${l}px`,e.lineClamp??(e.lineClamp=Math.floor(t/16)),e.visibility="inherit",r.headers&&o.dataLabel?.attr({width:l})}else e.width=`${s}px`,e.visibility="hidden"}o.dlOptions=tN(r,o.options.dataLabels)}super.drawDataLabels(e)}drawPoints(t=this.points){let e=this.chart,i=e.renderer,s=e.styledMode,o=this.options,r=s?{}:o.shadow,l=o.borderRadius,a=e.pointCount<o.animationLimit,n=o.allowTraversingTree;for(let e of t){let t=e.node.levelDynamic,h={},d={},p={},u="level-group-"+e.node.level,c=!!e.graphic,g=a&&c,v=e.shapeArgs;e.shouldDraw()&&(e.isInside=!0,l&&(d.r=l),tN(!0,g?h:d,c?v:{},s?{}:this.pointAttribs(e,e.selected?"select":void 0)),this.colorAttribs&&s&&tB(p,this.colorAttribs(e)),this[u]||(this[u]=i.g(u).attr({zIndex:1e3-(t||0)}).add(this.group),this[u].survive=!0)),e.draw({animatableAttribs:h,attribs:d,css:p,group:this[u],imageUrl:e.imageUrl,renderer:i,shadow:r,shapeArgs:v,shapeType:e.shapeType}),n&&e.graphic&&(e.drillId=o.interactByLeaf?this.drillToByLeaf(e):this.drillToByGroup(e))}}drillToByGroup(t){return(!t.node.isLeaf||!!t.node.isGroup)&&t.id}drillToByLeaf(t){let{traverseToLeaf:e}=t.series.options,i=!1,s;if(t.node.parent!==this.rootNode&&t.node.isLeaf)if(e)i=t.id;else for(s=t.node;!i;)void 0!==s.parent&&(s=this.nodeMap[s.parent]),s.parent===this.rootNode&&(i=s.id);return i}drillToNode(t,e){tL(32,!1,void 0,{"treemap.drillToNode":"use treemap.setRootNode"}),this.setRootNode(t,e)}drillUp(){let t=this.nodeMap[this.rootNode];t&&tO(t.parent)&&this.setRootNode(t.parent,!0,{trigger:"traverseUpButton"})}getExtremes(){let{dataMin:t,dataMax:e}=super.getExtremes(this.colorValueData);return this.valueMin=t,this.valueMax=e,super.getExtremes()}getListOfParents(t,e){let i=tP(t)?t:[],s=tP(e)?e:[],o=i.reduce(function(t,e,i){let s=tM(e.parent,"");return void 0===t[s]&&(t[s]=[]),t[s].push(i),t},{"":[]});for(let t of Object.keys(o)){let e=o[t];if(""!==t&&-1===s.indexOf(t)){for(let t of e)o[""].push(t);delete o[t]}}return o}getTree(){let t=this.data.map(function(t){return t.id});return this.parentList=this.getListOfParents(this.data,t),this.nodeMap={},this.nodeList=[],this.buildTree("",-1,0,this.parentList||{})}buildTree(t,e,i,s,o){let r=[],l=this.points[e],a=0,n;for(let e of s[t]||[])a=Math.max((n=this.buildTree(this.points[e].id,e,i+1,s,t)).height+1,a),r.push(n);let h=new this.NodeClass().init(t,e,r,a,i,this,o);for(let t of r)t.parentNode=h;return this.nodeMap[h.id]=h,this.nodeList.push(h),l&&(l.node=h,h.point=l),h}hasData(){return!!this.dataTable.rowCount}init(t,e){let i=this,s=tN(e.drillUpButton,e.breadcrumbs),o=tb(i,"setOptions",t=>{let e=t.userOptions;tT(e.allowDrillToNode)&&!tT(e.allowTraversingTree)&&(e.allowTraversingTree=e.allowDrillToNode,delete e.allowDrillToNode),tT(e.drillUpButton)&&!tT(e.traverseUpButton)&&(e.traverseUpButton=e.drillUpButton,delete e.drillUpButton);let i=tD(e.dataLabels||{});e.levels?.forEach(t=>{i.push.apply(i,tD(t.dataLabels||{}))}),this.hasOutsideDataLabels=i.some(t=>t.headers)});super.init(t,e),delete i.opacity,i.eventsToUnbind.push(o),i.options.allowTraversingTree&&(i.eventsToUnbind.push(tb(i,"click",i.onClickDrillToNode)),i.eventsToUnbind.push(tb(i,"setRootNode",function(t){let e=i.chart;e.breadcrumbs&&e.breadcrumbs.updateProperties(i.createList(t))})),i.eventsToUnbind.push(tb(i,"update",function(t,e){let i=this.chart.breadcrumbs;i&&t.options.breadcrumbs&&i.update(t.options.breadcrumbs),this.hadOutsideDataLabels=this.hasOutsideDataLabels})),i.eventsToUnbind.push(tb(i,"destroy",function(t){let e=this.chart;e.breadcrumbs&&!t.keepEventsForUpdate&&(e.breadcrumbs.destroy(),e.breadcrumbs=void 0)}))),t.breadcrumbs||(t.breadcrumbs=new R(t,s)),i.eventsToUnbind.push(tb(t.breadcrumbs,"up",function(t){let e=this.level-t.newLevel;for(let t=0;t<e;t++)i.drillUp()}))}onClickDrillToNode(t){let e=t.point,i=e?.drillId;tO(i)&&(e.setState(""),this.setRootNode(i,!0,{trigger:"click"}))}pointAttribs(t,e){let i=tC(this.mapOptionsToLevel)?this.mapOptionsToLevel:{},s=t&&i[t.node.level]||{},o=this.options,r=e&&o.states&&o.states[e]||{},l=t?.getClassName()||"",a={stroke:t&&t.borderColor||s.borderColor||r.borderColor||o.borderColor,"stroke-width":tM(t&&t.borderWidth,s.borderWidth,r.borderWidth,o.borderWidth),dashstyle:t?.borderDashStyle||s.borderDashStyle||r.borderDashStyle||o.borderDashStyle,fill:t?.color||this.color};return -1!==l.indexOf("highcharts-above-level")?(a.fill="none",a["stroke-width"]=0):-1!==l.indexOf("highcharts-internal-node-interactive")?(a["fill-opacity"]=r.opacity??o.opacity??1,a.cursor="pointer"):-1!==l.indexOf("highcharts-internal-node")?a.fill="none":e&&r.brightness&&(a.fill=th(a.fill).brighten(r.brightness).get()),a}setColorRecursive(t,e,i,s,o){let r=this?.chart,l=r?.options?.colors;if(t){let r=tg(t,{colors:l,index:s,mapOptionsToLevel:this.mapOptionsToLevel,parentColor:e,parentColorIndex:i,series:this,siblings:o}),a=this.points[t.i];a&&(a.color=r.color,a.colorIndex=r.colorIndex);let n=-1;for(let e of t.children||[])this.setColorRecursive(e,r.color,r.colorIndex,++n,t.children.length)}}setPointValues(){let t=this,{points:e,xAxis:i,yAxis:s}=t,o=t.chart.styledMode,r=e=>o?0:t.pointAttribs(e)["stroke-width"]||0;for(let t of e){let{pointValues:e,visible:o}=t.node;if(e&&o){let{height:o,width:l,x:a,y:n}=e,h=r(t),d=i.toPixels(a,!0),p=i.toPixels(a+l,!0),u=s.toPixels(n,!0),c=s.toPixels(n+o,!0),g=0===d?h/2:tw(i.toPixels(a,!0),h,!0),v=p===i.len?i.len-h/2:tw(i.toPixels(a+l,!0),h,!0),m=u===s.len?s.len-h/2:tw(s.toPixels(n,!0),h,!0),b=0===c?h/2:tw(s.toPixels(n+o,!0),h,!0),f={x:Math.min(g,v),y:Math.min(m,b),width:Math.abs(v-g),height:Math.abs(b-m)};t.plotX=f.x+f.width/2,t.plotY=f.y+f.height/2,t.shapeArgs=f}else delete t.plotX,delete t.plotY}}setRootNode(t,e,i){tA(this,"setRootNode",tB({newRootId:t,previousRootId:this.rootNode,redraw:tM(e,!0),series:this},i),function(t){let e=t.series;e.idPreviousRoot=t.previousRootId,e.rootNode=t.newRootId,e.isDirty=!0,t.redraw&&e.chart.redraw()})}setState(t){this.options.inactiveOtherPoints=!0,super.setState(t,!1),this.options.inactiveOtherPoints=!1}setTreeValues(t){let e=this.options,i=this.rootNode,s=this.nodeMap[i],o="boolean"!=typeof e.levelIsConstant||e.levelIsConstant,r=[],l=this.points[t.i],a=0;for(let e of t.children)e=this.setTreeValues(e),r.push(e),e.ignore||(a+=e.val);tI(r,(t,e)=>(t.sortIndex||0)-(e.sortIndex||0));let n=tM(l?.simulatedValue,l?.options.value,a);return l&&(l.value=n),l?.isGroup&&e.cluster?.reductionFactor&&(n/=e.cluster.reductionFactor),t.parentNode?.point?.isGroup&&this.rootNode!==t.parent&&(t.visible=!1),tB(t,{children:r,childrenTotal:a,ignore:!(tM(l?.visible,!0)&&n>0),isLeaf:t.visible&&!a,isGroup:l?.isGroup,levelDynamic:t.level-(o?0:s.level),name:tM(l?.name,""),sortIndex:tM(l?.sortIndex,-n),val:n}),t}sliceAndDice(t,e){return this.algorithmFill(!0,t,e)}squarified(t,e){return this.algorithmLowAspectRatio(!0,t,e)}strip(t,e){return this.algorithmLowAspectRatio(!1,t,e)}stripes(t,e){return this.algorithmFill(!1,t,e)}translate(t){let e=this,i=e.options,s=!t,o=tm(e),r,l,a,n;t||o.startsWith("highcharts-grouped-treemap-points-")||((this.points||[]).forEach(t=>{t.isGroup&&t.destroy()}),super.translate(),t=e.getTree()),e.tree=t=t||e.tree,r=e.nodeMap[o],""===o||r||(e.setRootNode("",!1),o=e.rootNode,r=e.nodeMap[o]),r.point?.isGroup||(e.mapOptionsToLevel=tv({from:r.level+1,levels:i.levels,to:t.height,defaults:{levelIsConstant:e.options.levelIsConstant,colorByPoint:i.colorByPoint}})),te.recursive(e.nodeMap[e.rootNode],t=>{let i=t.parent,s=!1;return t.visible=!0,(i||""===i)&&(s=e.nodeMap[i]),s}),te.recursive(e.nodeMap[e.rootNode].children,t=>{let e=!1;for(let i of t)i.visible=!0,i.children.length&&(e=(e||[]).concat(i.children));return e}),e.setTreeValues(t),e.axisRatio=e.xAxis.len/e.yAxis.len,e.nodeMap[""].pointValues=l={x:0,y:0,width:100,height:100},e.nodeMap[""].values=a=tN(l,{width:l.width*e.axisRatio,direction:+("vertical"!==i.layoutStartingDirection),val:t.val}),(this.hasOutsideDataLabels||this.hadOutsideDataLabels)&&this.drawDataLabels(),e.calculateChildrenAreas(t,a),e.colorAxis||i.colorByPoint||e.setColorRecursive(e.tree),i.allowTraversingTree&&r.pointValues&&(n=r.pointValues,e.xAxis.setExtremes(n.x,n.x+n.width,!1),e.yAxis.setExtremes(n.y,n.y+n.height,!1),e.xAxis.setScale(),e.yAxis.setScale()),e.setPointValues(),s&&e.applyTreeGrouping()}}tE.defaultOptions=tN(tc.defaultOptions,tt),tB(tE.prototype,{buildKDTree:tp,colorAttribs:F.seriesMembers.colorAttribs,colorKey:"colorValue",directTouch:!0,getExtremesFromAll:!0,getSymbol:tp,optionalAxis:"colorAxis",parallelArrays:["x","y","value","colorValue"],pointArrayMap:["value","colorValue"],pointClass:J,NodeClass:class{constructor(){this.childrenTotal=0,this.visible=!1}init(t,e,i,s,o,r,l){return this.id=t,this.i=e,this.children=i,this.height=s,this.level=o,this.series=r,this.parent=l,this}},trackerGroups:["group","dataLabelsGroup"],utils:te}),F.compose(tE),G().registerSeriesType("treemap",tE);let tV=c();tV.Breadcrumbs=tV.Breadcrumbs||R,tV.Breadcrumbs.compose(tV.Chart,tV.defaultOptions),tE.compose(tV.Series);let tk=c();return p.default})());