{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/timeline\n * @requires highcharts\n *\n * Timeline series\n *\n * (c) 2010-2025 Highsoft AS\n * Author: <PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"Point\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/timeline\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"],amd1[\"Point\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/timeline\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"Point\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"Point\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__260__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 260:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__260__;\n\n/***/ }),\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ timeline_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Point\"],\"commonjs\":[\"highcharts\",\"Point\"],\"commonjs2\":[\"highcharts\",\"Point\"],\"root\":[\"Highcharts\",\"Point\"]}\nvar highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_ = __webpack_require__(260);\nvar highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default = /*#__PURE__*/__webpack_require__.n(highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_);\n;// ./code/es-modules/Series/Timeline/TimelinePoint.js\n/* *\n *\n *  Timeline Series.\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Daniel Studencki\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { line: { prototype: { pointClass: LinePoint } }, pie: { prototype: { pointClass: PiePoint } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { defined, isNumber, merge, objectEach, pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass TimelinePoint extends LinePoint {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    alignConnector() {\n        const point = this, series = point.series, dataLabel = point.dataLabel, connector = dataLabel.connector, dlOptions = (dataLabel.options || {}), connectorWidth = dlOptions.connectorWidth || 0, chart = point.series.chart, bBox = connector.getBBox(), plotPos = {\n            x: bBox.x + (dataLabel.translateX || 0),\n            y: bBox.y + (dataLabel.translateY || 0)\n        };\n        // Include a half of connector width in order to run animation,\n        // when connectors are aligned to the plot area edge.\n        if (chart.inverted) {\n            plotPos.y -= connectorWidth / 2;\n        }\n        else {\n            plotPos.x += connectorWidth / 2;\n        }\n        const isVisible = chart.isInsidePlot(plotPos.x, plotPos.y);\n        connector[isVisible ? 'animate' : 'attr']({\n            d: point.getConnectorPath()\n        });\n        connector.addClass('highcharts-color-' + point.colorIndex);\n        if (!series.chart.styledMode) {\n            connector.attr({\n                stroke: dlOptions.connectorColor || point.color,\n                'stroke-width': dlOptions.connectorWidth,\n                opacity: dataLabel[defined(dataLabel.newOpacity) ? 'newOpacity' : 'opacity']\n            });\n        }\n    }\n    drawConnector() {\n        const point = this, { dataLabel, series } = point;\n        if (dataLabel) {\n            if (!dataLabel.connector) {\n                dataLabel.connector = series.chart.renderer\n                    .path(point.getConnectorPath())\n                    .attr({\n                    zIndex: -1\n                })\n                    .add(dataLabel);\n            }\n            if (point.series.chart.isInsidePlot(// #10507\n            dataLabel.x || 0, dataLabel.y || 0)) {\n                point.alignConnector();\n            }\n        }\n    }\n    getConnectorPath() {\n        const { plotX = 0, plotY = 0, series, dataLabel } = this, chart = series.chart, xAxisLen = series.xAxis.len, inverted = chart.inverted, direction = inverted ? 'x2' : 'y2';\n        if (dataLabel) {\n            const targetDLPos = dataLabel.targetPosition, negativeDistance = ((dataLabel.alignAttr || dataLabel)[direction[0]] <\n                series.yAxis.len / 2);\n            let coords = {\n                x1: plotX,\n                y1: plotY,\n                x2: plotX,\n                y2: isNumber(targetDLPos.y) ? targetDLPos.y : dataLabel.y\n            };\n            // Recalculate coords when the chart is inverted.\n            if (inverted) {\n                coords = {\n                    x1: plotY,\n                    y1: xAxisLen - plotX,\n                    x2: targetDLPos.x || dataLabel.x,\n                    y2: xAxisLen - plotX\n                };\n            }\n            // Subtract data label width or height from expected coordinate so\n            // that the connector would start from the appropriate edge.\n            if (negativeDistance) {\n                coords[direction] += dataLabel[inverted ? 'width' : 'height'] || 0;\n            }\n            // Change coordinates so that they will be relative to data label.\n            objectEach(coords, (_coord, i) => {\n                coords[i] -= (dataLabel.alignAttr || dataLabel)[i[0]];\n            });\n            return chart.renderer.crispLine([\n                ['M', coords.x1, coords.y1],\n                ['L', coords.x2, coords.y2]\n            ], dataLabel.options?.connectorWidth || 0);\n        }\n        return [];\n    }\n    constructor(series, options) {\n        super(series, options);\n        this.name ?? (this.name = \n        // If options is null, we are dealing with a null point\n        ((options && options.y !== null) ||\n            !series.options.nullInteraction) &&\n            'Event' ||\n            'Null');\n        this.y = 1;\n    }\n    isValid() {\n        return (this.options.y !== null ||\n            this.series.options.nullInteraction ||\n            true);\n    }\n    setState() {\n        const proceed = super.setState;\n        // Prevent triggering the setState method on null points.\n        if (!this.isNull || this.series.options.nullInteraction) {\n            proceed.apply(this, arguments);\n        }\n    }\n    setVisible(visible, redraw) {\n        const point = this, series = point.series;\n        redraw = pick(redraw, series.options.ignoreHiddenPoint);\n        PiePoint.prototype.setVisible.call(point, visible, false);\n        // Process new data\n        series.processData();\n        if (redraw) {\n            series.chart.redraw();\n        }\n    }\n    applyOptions(options, x) {\n        const isNull = (this.isNull ||\n            options === null ||\n            options.y === null), series = this.series;\n        if (!x && !options?.x) {\n            if (isNumber(this.x)) {\n                x = this.x;\n            }\n            else if (isNumber(series?.xIncrement) || NaN) {\n                x = series.xIncrement || 0;\n                series.autoIncrement();\n            }\n        }\n        options = highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default().prototype.optionsToObject.call(this, options ?? ((series.options.nullInteraction && { y: 0 }) ||\n            null));\n        const p = super.applyOptions(options, x);\n        this.userDLOptions = merge(this.userDLOptions, options.dataLabels);\n        p.isNull = isNull;\n        return p;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Timeline_TimelinePoint = (TimelinePoint);\n\n;// ./code/es-modules/Series/Timeline/TimelineSeriesDefaults.js\n/* *\n *\n *  Timeline Series.\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Daniel Studencki\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * The timeline series presents given events along a drawn line.\n *\n * @sample highcharts/series-timeline/alternate-labels\n *         Timeline series\n * @sample highcharts/series-timeline/inverted\n *         Inverted timeline\n * @sample highcharts/series-timeline/datetime-axis\n *         With true datetime axis\n *\n * @extends      plotOptions.line\n * @excluding    animationLimit, boostThreshold, connectEnds, connectNulls,\n *               cropThreshold, dashStyle, findNearestPointBy,\n *               getExtremesFromAll, negativeColor, pointInterval,\n *               pointIntervalUnit, pointPlacement, pointStart,\n *               softThreshold, stacking, step, threshold, turboThreshold,\n *               zoneAxis, zones, dataSorting, boostBlending\n * @product      highcharts\n * @since        7.0.0\n * @requires     modules/timeline\n * @optionparent plotOptions.timeline\n */\nconst TimelineSeriesDefaults = {\n    colorByPoint: true,\n    stickyTracking: false,\n    ignoreHiddenPoint: true,\n    /**\n     * @ignore\n     */\n    legendType: 'point',\n    /**\n     * Pixel width of the graph line.\n     */\n    lineWidth: 4,\n    tooltip: {\n        headerFormat: '<span style=\"color:{point.color}\">\\u25CF</span> ' +\n            '<span style=\"font-size: 0.8em\"> {point.key}</span><br/>',\n        pointFormat: '{point.description}'\n    },\n    states: {\n        hover: {\n            lineWidthPlus: 0\n        }\n    },\n    /**\n     * @declare Highcharts.TimelineDataLabelsOptionsObject\n     */\n    dataLabels: {\n        enabled: true,\n        allowOverlap: true,\n        /**\n         * Whether to position data labels alternately. For example, if\n         * [distance](#plotOptions.timeline.dataLabels.distance)\n         * is set equal to `100`, then data labels will be positioned\n         * alternately (on both sides of the point) at a distance of 100px.\n         *\n         * @sample {highcharts} highcharts/series-timeline/alternate-disabled\n         *         Alternate disabled\n         */\n        alternate: true,\n        backgroundColor: \"#ffffff\" /* Palette.backgroundColor */,\n        borderWidth: 1,\n        borderColor: \"#999999\" /* Palette.neutralColor40 */,\n        borderRadius: 3,\n        color: \"#333333\" /* Palette.neutralColor80 */,\n        /**\n         * The color of the line connecting the data label to the point.\n         * The default color is the same as the point's color.\n         *\n         * In styled mode, the connector stroke is given in the\n         * `.highcharts-data-label-connector` class.\n         *\n         * @sample {highcharts} highcharts/series-timeline/connector-styles\n         *         Custom connector width and color\n         *\n         * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         * @apioption plotOptions.timeline.dataLabels.connectorColor\n         */\n        /**\n         * The width of the line connecting the data label to the point.\n         *\n         * In styled mode, the connector stroke width is given in the\n         * `.highcharts-data-label-connector` class.\n         *\n         * @sample {highcharts} highcharts/series-timeline/connector-styles\n         *         Custom connector width and color\n         */\n        connectorWidth: 1,\n        /**\n         * A pixel value defining the distance between the data label and\n         * the point. Negative numbers puts the label on top of the point in a\n         * non-inverted chart. Defaults to 100 for horizontal and 20 for\n         * vertical timeline (`chart.inverted: true`).\n         */\n        distance: void 0,\n        // eslint-disable-next-line jsdoc/require-description\n        /**\n         * @default function () {\n         *   let format;\n         *\n         *   if (!this.series.chart.styledMode) {\n         *       format = '<span style=\"color:' + this.point.color +\n         *           '\">● </span>';\n         *   } else {\n         *       format = '<span class=\"highcharts-color-' +\n         *          this.point.colorIndex + '\">● </span>';\n         *   }\n         *   format += '<span>' + (this.key || '') + '</span><br/>' +\n         *       (this.point.label || '');\n         *   return format;\n         * }\n         */\n        formatter: function () {\n            let format;\n            if (!this.series.chart.styledMode) {\n                format = '<span style=\"color:' + this.point.color +\n                    '\">● </span>';\n            }\n            else {\n                format = '<span class=\"highcharts-color-' +\n                    this.point.colorIndex + '\">● </span>';\n            }\n            format += '<span class=\"highcharts-strong\">' +\n                (this.key || '') + '</span><br/>' +\n                (this.label || '');\n            return format;\n        },\n        style: {\n            /** @internal */\n            textOutline: 'none',\n            /** @internal */\n            fontWeight: 'normal',\n            /** @internal */\n            fontSize: '0.8em',\n            /** @internal */\n            textAlign: 'left'\n        },\n        /**\n         * Shadow options for the data label.\n         *\n         * @type {boolean|Highcharts.CSSObject}\n         */\n        shadow: false,\n        /**\n         * @type      {number}\n         * @apioption plotOptions.timeline.dataLabels.width\n         */\n        verticalAlign: 'middle'\n    },\n    marker: {\n        enabledThreshold: 0,\n        symbol: 'square',\n        radius: 6,\n        lineWidth: 2,\n        height: 15\n    },\n    showInLegend: false,\n    colorKey: 'x',\n    legendSymbol: 'rectangle'\n};\n/**\n * The `timeline` series. If the [type](#series.timeline.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.timeline\n * @excluding animationLimit, boostThreshold, connectEnds, connectNulls,\n *            cropThreshold, dashStyle, dataParser, dataURL, findNearestPointBy,\n *            getExtremesFromAll, negativeColor, pointInterval,\n *            pointIntervalUnit, pointPlacement, pointStart, softThreshold,\n *            stacking, stack, step, threshold, turboThreshold, zoneAxis, zones,\n *            dataSorting, boostBlending\n * @product   highcharts\n * @requires  modules/timeline\n * @apioption series.timeline\n */\n/**\n * An array of data points for the series. For the `timeline` series type,\n * points can be given with three general parameters, `name`, `label`,\n * and `description`:\n *\n * Example:\n *\n * ```js\n * series: [{\n *    type: 'timeline',\n *    data: [{\n *        name: 'Jan 2018',\n *        label: 'Some event label',\n *        description: 'Description to show in tooltip'\n *    }]\n * }]\n * ```\n * If all points additionally have the `x` values, and xAxis type is set to\n * `datetime`, then events are laid out on a true time axis, where their\n * placement reflects the actual time between them.\n *\n * @sample {highcharts} highcharts/series-timeline/alternate-labels\n *         Alternate labels\n * @sample {highcharts} highcharts/series-timeline/datetime-axis\n *         Real time intervals\n *\n * @type      {Array<*>}\n * @extends   series.line.data\n * @excluding marker, y\n * @product   highcharts\n * @apioption series.timeline.data\n */\n/**\n * The name of event.\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.timeline.data.name\n */\n/**\n * The label of event.\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.timeline.data.label\n */\n/**\n * The description of event. This description will be shown in tooltip.\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.timeline.data.description\n */\n''; // Adds doclets above to transpiled file\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Timeline_TimelineSeriesDefaults = (TimelineSeriesDefaults);\n\n;// ./code/es-modules/Series/Timeline/TimelineSeries.js\n/* *\n *\n *  Timeline Series.\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Daniel Studencki\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { column: ColumnSeries, line: LineSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\n\n\nconst { addEvent, arrayMax, arrayMin, defined: TimelineSeries_defined, extend, merge: TimelineSeries_merge, pick: TimelineSeries_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The timeline series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.timeline\n *\n * @augments Highcharts.Series\n */\nclass TimelineSeries extends LineSeries {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    alignDataLabel(point, dataLabel, _options, _alignTo) {\n        const series = this, isInverted = series.chart.inverted, visiblePoints = series.visibilityMap.filter((point) => !!point), visiblePointsCount = series.visiblePointsCount || 0, pointIndex = visiblePoints.indexOf(point), isFirstOrLast = (!pointIndex || pointIndex === visiblePointsCount - 1), dataLabelsOptions = series.options.dataLabels, userDLOptions = point.userDLOptions || {}, \n        // Define multiplier which is used to calculate data label\n        // width. If data labels are alternate, they have two times more\n        // space to adapt (excepting first and last ones, which has only\n        // one and half), than in case of placing all data labels side\n        // by side.\n        multiplier = dataLabelsOptions.alternate ?\n            (isFirstOrLast ? 1.5 : 2) :\n            1, availableSpace = Math.floor(series.xAxis.len / visiblePointsCount), pad = dataLabel.padding;\n        let distance, targetDLWidth, styles;\n        // Adjust data label width to the currently available space.\n        if (point.visible) {\n            distance = Math.abs(userDLOptions.x || point.options.dataLabels.x);\n            if (isInverted) {\n                targetDLWidth = ((distance - pad) * 2 - ((point.itemHeight || 0) / 2));\n                styles = {\n                    width: TimelineSeries_pick(dataLabelsOptions.style?.width, `${series.yAxis.len * 0.4}px`),\n                    // Apply ellipsis when data label height is exceeded.\n                    textOverflow: (dataLabel.width || 0) / targetDLWidth *\n                        (dataLabel.height || 0) / 2 > availableSpace *\n                        multiplier ?\n                        'ellipsis' : 'none'\n                };\n            }\n            else {\n                styles = {\n                    width: (userDLOptions.width ||\n                        dataLabelsOptions.width ||\n                        availableSpace * multiplier - (pad * 2)) + 'px'\n                };\n            }\n            dataLabel.css(styles);\n            if (!series.chart.styledMode) {\n                dataLabel.shadow(dataLabelsOptions.shadow);\n            }\n        }\n        super.alignDataLabel.apply(series, arguments);\n    }\n    bindAxes() {\n        const series = this;\n        super.bindAxes();\n        // Initially set the linked xAxis type to category.\n        if (!series.xAxis.userOptions.type) {\n            series.xAxis.categories = series.xAxis.hasNames = true;\n        }\n    }\n    distributeDL() {\n        const series = this, dataLabelsOptions = series.options.dataLabels, inverted = series.chart.inverted;\n        let visibilityIndex = 1;\n        if (dataLabelsOptions) {\n            const distance = TimelineSeries_pick(dataLabelsOptions.distance, inverted ? 20 : 100);\n            for (const point of series.points) {\n                const defaults = {\n                    [inverted ? 'x' : 'y']: dataLabelsOptions.alternate && visibilityIndex % 2 ?\n                        -distance : distance\n                };\n                if (inverted) {\n                    defaults.align = (dataLabelsOptions.alternate && visibilityIndex % 2) ? 'right' : 'left';\n                }\n                point.options.dataLabels = TimelineSeries_merge(defaults, point.userDLOptions);\n                visibilityIndex++;\n            }\n        }\n    }\n    generatePoints() {\n        super.generatePoints();\n        const series = this, points = series.points, pointsLen = points.length, xData = series.getColumn('x');\n        for (let i = 0, iEnd = pointsLen; i < iEnd; ++i) {\n            const x = xData[i];\n            points[i].applyOptions({ x: x }, x);\n        }\n    }\n    getVisibilityMap() {\n        const series = this, nullInteraction = series.options.nullInteraction, map = ((series.data.length ? series.data : series.options.data) || []).map((point) => (point &&\n            point.visible !== false &&\n            (!point.isNull || nullInteraction) ?\n            point :\n            false));\n        return map;\n    }\n    getXExtremes(xData) {\n        const series = this, filteredData = xData.filter((_x, i) => (series.points[i].isValid() &&\n            series.points[i].visible));\n        return {\n            min: arrayMin(filteredData),\n            max: arrayMax(filteredData)\n        };\n    }\n    init() {\n        const series = this;\n        super.init.apply(series, arguments);\n        series.eventsToUnbind.push(addEvent(series, 'afterTranslate', function () {\n            let lastPlotX, closestPointRangePx = Number.MAX_VALUE;\n            for (const point of series.points) {\n                // Set the isInside parameter basing also on the real point\n                // visibility, in order to avoid showing hidden points\n                // in drawPoints method.\n                point.isInside = point.isInside && point.visible;\n                // New way of calculating closestPointRangePx value, which\n                // respects the real point visibility is needed.\n                if (point.visible && (!point.isNull ||\n                    series.options.nullInteraction)) {\n                    if (TimelineSeries_defined(lastPlotX)) {\n                        closestPointRangePx = Math.min(closestPointRangePx, Math.abs(point.plotX - lastPlotX));\n                    }\n                    lastPlotX = point.plotX;\n                }\n            }\n            series.closestPointRangePx = closestPointRangePx;\n        }));\n        // Distribute data labels before rendering them. Distribution is\n        // based on the 'dataLabels.distance' and 'dataLabels.alternate'\n        // property.\n        series.eventsToUnbind.push(addEvent(series, 'drawDataLabels', function () {\n            // Distribute data labels basing on defined algorithm.\n            series.distributeDL(); // @todo use this scope for series\n        }));\n        series.eventsToUnbind.push(addEvent(series, 'afterDrawDataLabels', function () {\n            let dataLabel; // @todo use this scope for series\n            // Draw or align connector for each point.\n            for (const point of series.points) {\n                dataLabel = point.dataLabel;\n                if (dataLabel) {\n                    // Within this wrap method is necessary to save the\n                    // current animation params, because the data label\n                    // target position (after animation) is needed to align\n                    // connectors.\n                    dataLabel.animate = function (params) {\n                        if (this.targetPosition) {\n                            this.targetPosition = params;\n                        }\n                        return this.renderer.Element.prototype\n                            .animate.apply(this, arguments);\n                    };\n                    // Initialize the targetPosition field within data label\n                    // object. It's necessary because there is need to know\n                    // expected position of specific data label, when\n                    // aligning connectors. This field is overridden inside\n                    // of SVGElement.animate() wrapped method.\n                    if (!dataLabel.targetPosition) {\n                        dataLabel.targetPosition = {};\n                    }\n                    point.drawConnector();\n                }\n            }\n        }));\n        series.eventsToUnbind.push(addEvent(series.chart, 'afterHideOverlappingLabel', function () {\n            for (const p of series.points) {\n                if (p.dataLabel &&\n                    p.dataLabel.connector &&\n                    p.dataLabel.oldOpacity !== p.dataLabel.newOpacity) {\n                    p.alignConnector();\n                }\n            }\n        }));\n    }\n    markerAttribs(point, state) {\n        const series = this, seriesMarkerOptions = series.options.marker, pointMarkerOptions = point.marker || {}, symbol = (pointMarkerOptions.symbol || seriesMarkerOptions.symbol), width = TimelineSeries_pick(pointMarkerOptions.width, seriesMarkerOptions.width, series.closestPointRangePx), height = TimelineSeries_pick(pointMarkerOptions.height, seriesMarkerOptions.height);\n        let seriesStateOptions, pointStateOptions, radius = 0;\n        // Call default markerAttribs method, when the xAxis type\n        // is set to datetime.\n        if (series.xAxis.dateTime) {\n            return super.markerAttribs(point, state);\n        }\n        // Handle hover and select states\n        if (state) {\n            seriesStateOptions =\n                seriesMarkerOptions.states[state] || {};\n            pointStateOptions = pointMarkerOptions.states &&\n                pointMarkerOptions.states[state] || {};\n            radius = TimelineSeries_pick(pointStateOptions.radius, seriesStateOptions.radius, radius + (seriesStateOptions.radiusPlus || 0));\n        }\n        point.hasImage = (symbol && symbol.indexOf('url') === 0);\n        const attribs = {\n            x: Math.floor(point.plotX) - (width / 2) - (radius / 2),\n            y: point.plotY - (height / 2) - (radius / 2),\n            width: width + radius,\n            height: height + radius\n        };\n        return (series.chart.inverted) ? {\n            y: (attribs.x && attribs.width) &&\n                series.xAxis.len - attribs.x - attribs.width,\n            x: attribs.y && attribs.y,\n            width: attribs.height,\n            height: attribs.width\n        } : attribs;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nTimelineSeries.defaultOptions = TimelineSeries_merge(LineSeries.defaultOptions, Timeline_TimelineSeriesDefaults);\n// Add series-specific properties after data is already processed, #17890\naddEvent(TimelineSeries, 'afterProcessData', function () {\n    const series = this, xData = series.getColumn('x');\n    let visiblePoints = 0;\n    series.visibilityMap = series.getVisibilityMap();\n    // Calculate currently visible points.\n    for (const point of series.visibilityMap) {\n        if (point) {\n            visiblePoints++;\n        }\n    }\n    series.visiblePointsCount = visiblePoints;\n    this.dataTable.setColumn('y', new Array(xData.length).fill(1));\n});\nextend(TimelineSeries.prototype, {\n    // Use a group of trackers from TrackerMixin\n    drawTracker: ColumnSeries.prototype.drawTracker,\n    pointClass: Timeline_TimelinePoint,\n    trackerGroups: ['markerGroup', 'dataLabelsGroup']\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('timeline', TimelineSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Timeline_TimelineSeries = ((/* unused pure expression or super */ null && (TimelineSeries)));\n\n;// ./code/es-modules/masters/modules/timeline.js\n\n\n\n\n/* harmony default export */ const timeline_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__260__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "timeline_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_", "highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default", "line", "pointClass", "LinePoint", "pie", "PiePoint", "seriesTypes", "defined", "isNumber", "merge", "objectEach", "pick", "Timeline_TimelinePoint", "alignConnector", "series", "point", "dataLabel", "connector", "dlOptions", "options", "connectorWidth", "chart", "bBox", "getBBox", "plotPos", "x", "translateX", "y", "translateY", "inverted", "isVisible", "isInsidePlot", "getConnectorPath", "addClass", "colorIndex", "styledMode", "attr", "stroke", "connectorColor", "color", "opacity", "newOpacity", "drawConnector", "renderer", "path", "zIndex", "add", "plotX", "plotY", "xAxisLen", "xAxis", "len", "direction", "targetDLPos", "targetPosition", "negativeDistance", "alignAttr", "yAxis", "coords", "x1", "y1", "x2", "y2", "_coord", "i", "crispLine", "constructor", "name", "nullInteraction", "<PERSON><PERSON><PERSON><PERSON>", "setState", "proceed", "isNull", "apply", "arguments", "setVisible", "visible", "redraw", "ignoreHiddenPoint", "processData", "applyOptions", "xIncrement", "autoIncrement", "optionsToObject", "p", "userDLOptions", "dataLabels", "column", "ColumnSeries", "LineSeries", "addEvent", "arrayMax", "arrayMin", "TimelineSeries_defined", "extend", "TimelineSeries_merge", "TimelineSeries_pick", "TimelineSeries", "alignDataLabel", "_options", "_alignTo", "distance", "targetDLWidth", "styles", "isInverted", "visiblePoints", "visibilityMap", "filter", "visiblePointsCount", "pointIndex", "indexOf", "dataLabelsOptions", "multiplier", "alternate", "isFirstOrLast", "availableSpace", "Math", "floor", "pad", "padding", "abs", "itemHeight", "width", "style", "textOverflow", "height", "css", "shadow", "bindAxes", "userOptions", "type", "categories", "hasNames", "distributeDL", "visibilityIndex", "points", "defaults", "align", "generatePoints", "pointsLen", "length", "xData", "getColumn", "getVisibilityMap", "data", "map", "getXExtremes", "filteredData", "_x", "min", "max", "init", "eventsToUnbind", "push", "lastPlotX", "closestPointRangePx", "Number", "MAX_VALUE", "isInside", "animate", "params", "Element", "oldOpacity", "markerAttribs", "state", "seriesMarkerOptions", "marker", "pointMarkerOptions", "symbol", "seriesStateOptions", "radius", "dateTime", "states", "pointStateOptions", "radiusPlus", "hasImage", "attribs", "defaultOptions", "colorByPoint", "stickyTracking", "legendType", "lineWidth", "tooltip", "headerFormat", "pointFormat", "hover", "lineWidthPlus", "enabled", "allowOverlap", "backgroundColor", "borderWidth", "borderColor", "borderRadius", "formatter", "format", "label", "textOutline", "fontWeight", "fontSize", "textAlign", "verticalAlign", "enabledThreshold", "showInLegend", "colorKey", "legendSymbol", "dataTable", "setColumn", "Array", "fill", "drawTracker", "trackerGroups", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,KAAQ,EAC1G,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,8BAA+B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAACA,EAAK,KAAQ,CAAE,GACrI,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,8BAA8B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,KAAQ,EAEzIA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,KAAQ,CACpH,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,IACvG,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAab,OAAO,CAG5B,IAAIC,EAASS,CAAwB,CAACE,EAAS,CAAG,CAGjDZ,QAAS,CAAC,CACX,EAMA,OAHAS,CAAmB,CAACG,EAAS,CAACX,EAAQA,EAAOD,OAAO,CAAEW,GAG/CV,EAAOD,OAAO,AACtB,CAMCW,EAAoBI,CAAC,CAAG,AAACd,IACxB,IAAIe,EAASf,GAAUA,EAAOgB,UAAU,CACvC,IAAOhB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAU,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAAClB,EAASoB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACtB,EAASqB,IAC5EE,OAAOC,cAAc,CAACxB,EAASqB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAE7KE,EAA+F3B,EAAoB,KACnH4B,EAAmH5B,EAAoBI,CAAC,CAACuB,GAkB7I,GAAM,CAAEE,KAAM,CAAEX,UAAW,CAAEY,WAAYC,CAAS,CAAE,CAAE,CAAEC,IAAK,CAAEd,UAAW,CAAEY,WAAYG,CAAQ,CAAE,CAAE,CAAE,CAAG,AAACP,IAA2IQ,WAAW,CAE1P,CAAEC,QAAAA,CAAO,CAAEC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,WAAAA,CAAU,CAAEC,KAAAA,CAAI,CAAE,CAAIf,IAqJrBgB,EA/InC,cAA4BT,EAMxBU,gBAAiB,CACb,IAAoBC,EAASC,AAAf,IAAI,CAAiBD,MAAM,CAAEE,EAAYD,AAAzC,IAAI,CAA2CC,SAAS,CAAEC,EAAYD,EAAUC,SAAS,CAAEC,EAAaF,EAAUG,OAAO,EAAI,CAAC,EAAIC,EAAiBF,EAAUE,cAAc,EAAI,EAAGC,EAAQN,AAA1L,IAAI,CAA4LD,MAAM,CAACO,KAAK,CAAEC,EAAOL,EAAUM,OAAO,GAAIC,EAAU,CAC9PC,EAAGH,EAAKG,CAAC,CAAIT,CAAAA,EAAUU,UAAU,EAAI,CAAA,EACrCC,EAAGL,EAAKK,CAAC,CAAIX,CAAAA,EAAUY,UAAU,EAAI,CAAA,CACzC,CAGIP,CAAAA,EAAMQ,QAAQ,CACdL,EAAQG,CAAC,EAAIP,EAAiB,EAG9BI,EAAQC,CAAC,EAAIL,EAAiB,EAGlCH,CAAS,CAACa,AADQT,EAAMU,YAAY,CAACP,EAAQC,CAAC,CAAED,EAAQG,CAAC,EACnC,UAAY,OAAO,CAAC,CACtChD,EAAGoC,AAdO,IAAI,CAcLiB,gBAAgB,EAC7B,GACAf,EAAUgB,QAAQ,CAAC,oBAAsBlB,AAhB3B,IAAI,CAgB6BmB,UAAU,EACrD,AAACpB,EAAOO,KAAK,CAACc,UAAU,EACxBlB,EAAUmB,IAAI,CAAC,CACXC,OAAQnB,EAAUoB,cAAc,EAAIvB,AAnB9B,IAAI,CAmBgCwB,KAAK,CAC/C,eAAgBrB,EAAUE,cAAc,CACxCoB,QAASxB,CAAS,CAACT,EAAQS,EAAUyB,UAAU,EAAI,aAAe,UAAU,AAChF,EAER,CACAC,eAAgB,CACZ,GAAoB,CAAE1B,UAAAA,CAAS,CAAEF,OAAAA,CAAM,CAAE,CAA3B,IAAI,CACdE,IACI,AAACA,EAAUC,SAAS,EACpBD,CAAAA,EAAUC,SAAS,CAAGH,EAAOO,KAAK,CAACsB,QAAQ,CACtCC,IAAI,CAAC7B,AAJJ,IAAI,CAIMiB,gBAAgB,IAC3BI,IAAI,CAAC,CACNS,OAAQ,EACZ,GACKC,GAAG,CAAC9B,EAAS,EAElBD,AAVM,IAAI,CAUJD,MAAM,CAACO,KAAK,CAACU,YAAY,CACnCf,EAAUS,CAAC,EAAI,EAAGT,EAAUW,CAAC,EAAI,IAC7BZ,AAZM,IAAI,CAYJF,cAAc,GAGhC,CACAmB,kBAAmB,CACf,GAAM,CAAEe,MAAAA,EAAQ,CAAC,CAAEC,MAAAA,EAAQ,CAAC,CAAElC,OAAAA,CAAM,CAAEE,UAAAA,CAAS,CAAE,CAAG,IAAI,CAAEK,EAAQP,EAAOO,KAAK,CAAE4B,EAAWnC,EAAOoC,KAAK,CAACC,GAAG,CAAEtB,EAAWR,EAAMQ,QAAQ,CAAEuB,EAAYvB,EAAW,KAAO,KACtK,GAAIb,EAAW,CACX,IAAMqC,EAAcrC,EAAUsC,cAAc,CAAEC,EAAoB,AAACvC,CAAAA,EAAUwC,SAAS,EAAIxC,CAAQ,CAAE,CAACoC,CAAS,CAAC,EAAE,CAAC,CAC9GtC,EAAO2C,KAAK,CAACN,GAAG,CAAG,EACnBO,EAAS,CACTC,GAAIZ,EACJa,GAAIZ,EACJa,GAAId,EACJe,GAAItD,EAAS6C,EAAY1B,CAAC,EAAI0B,EAAY1B,CAAC,CAAGX,EAAUW,CAAC,AAC7D,EAmBA,OAjBIE,GACA6B,CAAAA,EAAS,CACLC,GAAIX,EACJY,GAAIX,EAAWF,EACfc,GAAIR,EAAY5B,CAAC,EAAIT,EAAUS,CAAC,CAChCqC,GAAIb,EAAWF,CACnB,CAAA,EAIAQ,GACAG,CAAAA,CAAM,CAACN,EAAU,EAAIpC,CAAS,CAACa,EAAW,QAAU,SAAS,EAAI,CAAA,EAGrEnB,EAAWgD,EAAQ,CAACK,EAAQC,KACxBN,CAAM,CAACM,EAAE,EAAI,AAAChD,CAAAA,EAAUwC,SAAS,EAAIxC,CAAQ,CAAE,CAACgD,CAAC,CAAC,EAAE,CAAC,AACzD,GACO3C,EAAMsB,QAAQ,CAACsB,SAAS,CAAC,CAC5B,CAAC,IAAKP,EAAOC,EAAE,CAAED,EAAOE,EAAE,CAAC,CAC3B,CAAC,IAAKF,EAAOG,EAAE,CAAEH,EAAOI,EAAE,CAAC,CAC9B,CAAE9C,EAAUG,OAAO,EAAEC,gBAAkB,EAC5C,CACA,MAAO,EAAE,AACb,CACA8C,YAAYpD,CAAM,CAAEK,CAAO,CAAE,CACzB,KAAK,CAACL,EAAQK,GACd,IAAI,CAACgD,IAAI,EAAK,CAAA,IAAI,CAACA,IAAI,CAEvB,AAAC,CAAA,AAAChD,GAAWA,AAAc,OAAdA,EAAQQ,CAAC,EAClB,CAACb,EAAOK,OAAO,CAACiD,eAAe,AAAD,GAC9B,SACA,MAAK,EACT,IAAI,CAACzC,CAAC,CAAG,CACb,CACA0C,SAAU,CACN,OAAQ,AAAmB,OAAnB,IAAI,CAAClD,OAAO,CAACQ,CAAC,EAClB,IAAI,CAACb,MAAM,CAACK,OAAO,CAACiD,eAAe,EACnC,CAAA,CACR,CACAE,UAAW,CACP,IAAMC,EAAU,KAAK,CAACD,QAElB,CAAA,CAAA,CAAC,IAAI,CAACE,MAAM,EAAI,IAAI,CAAC1D,MAAM,CAACK,OAAO,CAACiD,eAAe,AAAD,GAClDG,EAAQE,KAAK,CAAC,IAAI,CAAEC,UAE5B,CACAC,WAAWC,CAAO,CAAEC,CAAM,CAAE,CACxB,IAAoB/D,EAASC,AAAf,IAAI,CAAiBD,MAAM,CACzC+D,EAASlE,EAAKkE,EAAQ/D,EAAOK,OAAO,CAAC2D,iBAAiB,EACtDzE,EAASf,SAAS,CAACqF,UAAU,CAACnF,IAAI,CAFpB,IAAI,CAEwBoF,EAAS,CAAA,GAEnD9D,EAAOiE,WAAW,GACdF,GACA/D,EAAOO,KAAK,CAACwD,MAAM,EAE3B,CACAG,aAAa7D,CAAO,CAAEM,CAAC,CAAE,CACrB,IAAM+C,EAAU,IAAI,CAACA,MAAM,EACvBrD,AAAY,OAAZA,GACAA,AAAc,OAAdA,EAAQQ,CAAC,CAAYb,EAAS,IAAI,CAACA,MAAM,CACxCW,GAAMN,GAASM,IACZjB,EAAS,IAAI,CAACiB,CAAC,EACfA,EAAI,IAAI,CAACA,CAAC,CAELjB,EAASM,GAAQmE,cACtBxD,EAAIX,EAAOmE,UAAU,EAAI,EACzBnE,EAAOoE,aAAa,KAG5B/D,EAAUnB,IAAsGV,SAAS,CAAC6F,eAAe,CAAC3F,IAAI,CAAC,IAAI,CAAE2B,GAAY,CAAA,AAACL,EAAOK,OAAO,CAACiD,eAAe,EAAI,CAAEzC,EAAG,CAAE,GACvM,IAAG,GACP,IAAMyD,EAAI,KAAK,CAACJ,aAAa7D,EAASM,GAGtC,OAFA,IAAI,CAAC4D,aAAa,CAAG5E,EAAM,IAAI,CAAC4E,aAAa,CAAElE,EAAQmE,UAAU,EACjEF,EAAEZ,MAAM,CAAGA,EACJY,CACX,CACJ,EAwRM,CAAEG,OAAQC,CAAY,CAAEvF,KAAMwF,CAAU,CAAE,CAAG,AAAC3F,IAA2IQ,WAAW,CAIpM,CAAEoF,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAErF,QAASsF,CAAsB,CAAEC,OAAAA,CAAM,CAAErF,MAAOsF,CAAoB,CAAEpF,KAAMqF,CAAmB,CAAE,CAAIpG,GAe3I,OAAMqG,UAAuBR,EAMzBS,eAAenF,CAAK,CAAEC,CAAS,CAAEmF,CAAQ,CAAEC,CAAQ,CAAE,CACjD,IASIC,EAAUC,EAAeC,EATRC,EAAa1F,AAAnB,IAAI,CAAsBO,KAAK,CAACQ,QAAQ,CAAE4E,EAAgB3F,AAA1D,IAAI,CAA6D4F,aAAa,CAACC,MAAM,CAAC,AAAC5F,GAAU,CAAC,CAACA,GAAQ6F,EAAqB9F,AAAhI,IAAI,CAAmI8F,kBAAkB,EAAI,EAAGC,EAAaJ,EAAcK,OAAO,CAAC/F,GAAgFgG,EAAoBjG,AAAvS,IAAI,CAA0SK,OAAO,CAACmE,UAAU,CAAED,EAAgBtE,EAAMsE,aAAa,EAAI,CAAC,EAMzX2B,EAAaD,EAAkBE,SAAS,CACnCC,AAPsO,AAACL,GAAcA,IAAeD,EAAqB,EAOnQ,EAAN,IACjB,EAAGO,EAAiBC,KAAKC,KAAK,CAACvG,AARpB,IAAI,CAQuBoC,KAAK,CAACC,GAAG,CAAGyD,GAAqBU,EAAMtG,EAAUuG,OAAO,AAG9FxG,CAAAA,EAAM6D,OAAO,GACbyB,EAAWe,KAAKI,GAAG,CAACnC,EAAc5D,CAAC,EAAIV,EAAMI,OAAO,CAACmE,UAAU,CAAC7D,CAAC,EAC7D+E,GACAF,EAAiB,AAACD,CAAAA,EAAWiB,CAAE,EAAK,EAAK,AAACvG,CAAAA,EAAM0G,UAAU,EAAI,CAAA,EAAK,EACnElB,EAAS,CACLmB,MAAO1B,EAAoBe,EAAkBY,KAAK,EAAED,MAAO,CAAC,EAAE5G,AAAmB,GAAnBA,AAhB3D,IAAI,CAgB8D2C,KAAK,CAACN,GAAG,CAAO,EAAE,CAAC,EAExFyE,aAAc,AAAC5G,CAAAA,EAAU0G,KAAK,EAAI,CAAA,EAAKpB,EAClCtF,CAAAA,EAAU6G,MAAM,EAAI,CAAA,EAAK,EAAIV,EAC9BH,EACA,WAAa,MACrB,GAGAT,EAAS,CACLmB,MAAO,AAACrC,CAAAA,EAAcqC,KAAK,EACvBX,EAAkBW,KAAK,EACvBP,EAAiBH,EAAcM,AAAM,EAANA,CAAO,EAAK,IACnD,EAEJtG,EAAU8G,GAAG,CAACvB,GACV,AAACzF,AAhCM,IAAI,CAgCHO,KAAK,CAACc,UAAU,EACxBnB,EAAU+G,MAAM,CAAChB,EAAkBgB,MAAM,GAGjD,KAAK,CAAC7B,eAAezB,KAAK,CApCX,IAAI,CAoCgBC,UACvC,CACAsD,UAAW,CAEP,KAAK,CAACA,WAEF,AAAClH,AAHU,IAAI,CAGPoC,KAAK,CAAC+E,WAAW,CAACC,IAAI,EAC9BpH,CAAAA,AAJW,IAAI,CAIRoC,KAAK,CAACiF,UAAU,CAAGrH,AAJf,IAAI,CAIkBoC,KAAK,CAACkF,QAAQ,CAAG,CAAA,CAAG,CAE7D,CACAC,cAAe,CACX,IAAqBtB,EAAoBjG,AAA1B,IAAI,CAA6BK,OAAO,CAACmE,UAAU,CAAEzD,EAAWf,AAAhE,IAAI,CAAmEO,KAAK,CAACQ,QAAQ,CAChGyG,EAAkB,EACtB,GAAIvB,EAAmB,CACnB,IAAMV,EAAWL,EAAoBe,EAAkBV,QAAQ,CAAExE,EAAW,GAAK,KACjF,IAAK,IAAMd,KAASD,AAJT,IAAI,CAIYyH,MAAM,CAAE,CAC/B,IAAMC,EAAW,CACb,CAAC3G,EAAW,IAAM,IAAI,CAAEkF,EAAkBE,SAAS,EAAIqB,EAAkB,EACrE,CAACjC,EAAWA,CACpB,CACIxE,CAAAA,GACA2G,CAAAA,EAASC,KAAK,CAAG,AAAC1B,EAAkBE,SAAS,EAAIqB,EAAkB,EAAK,QAAU,MAAK,EAE3FvH,EAAMI,OAAO,CAACmE,UAAU,CAAGS,EAAqByC,EAAUzH,EAAMsE,aAAa,EAC7EiD,GACJ,CACJ,CACJ,CACAI,gBAAiB,CACb,KAAK,CAACA,iBACN,IAAqBH,EAASzH,AAAf,IAAI,CAAkByH,MAAM,CAAEI,EAAYJ,EAAOK,MAAM,CAAEC,EAAQ/H,AAAjE,IAAI,CAAoEgI,SAAS,CAAC,KACjG,IAAK,IAAI9E,EAAI,EAAqBA,EAAX2E,EAAqB,EAAE3E,EAAG,CAC7C,IAAMvC,EAAIoH,CAAK,CAAC7E,EAAE,CAClBuE,CAAM,CAACvE,EAAE,CAACgB,YAAY,CAAC,CAAEvD,EAAGA,CAAE,EAAGA,EACrC,CACJ,CACAsH,kBAAmB,CACf,IAAqB3E,EAAkBtD,AAAxB,IAAI,CAA2BK,OAAO,CAACiD,eAAe,CAKrE,MAL6E,AAAC,CAAA,AAACtD,CAAAA,AAAhE,IAAI,CAAmEkI,IAAI,CAACJ,MAAM,CAAG9H,AAArF,IAAI,CAAwFkI,IAAI,CAAGlI,AAAnG,IAAI,CAAsGK,OAAO,CAAC6H,IAAI,AAAD,GAAM,EAAE,AAAD,EAAGC,GAAG,CAAC,AAAClI,GAAWA,EAAAA,GAC1JA,AAAkB,CAAA,IAAlBA,EAAM6D,OAAO,EACZ,CAAA,CAAC7D,EAAMyD,MAAM,IAAIJ,CAAc,GAChCrD,EAGR,CACAmI,aAAaL,CAAK,CAAE,CAChB,IAAM/H,EAAS,IAAI,CAAEqI,EAAeN,EAAMlC,MAAM,CAAC,CAACyC,EAAIpF,IAAOlD,EAAOyH,MAAM,CAACvE,EAAE,CAACK,OAAO,IACjFvD,EAAOyH,MAAM,CAACvE,EAAE,CAACY,OAAO,EAC5B,MAAO,CACHyE,IAAKzD,EAASuD,GACdG,IAAK3D,EAASwD,EAClB,CACJ,CACAI,MAAO,CACH,IAAMzI,EAAS,IAAI,CACnB,KAAK,CAACyI,KAAK9E,KAAK,CAAC3D,EAAQ4D,WACzB5D,EAAO0I,cAAc,CAACC,IAAI,CAAC/D,EAAS5E,EAAQ,iBAAkB,WAC1D,IAAI4I,EAAWC,EAAsBC,OAAOC,SAAS,CACrD,IAAK,IAAM9I,KAASD,EAAOyH,MAAM,CAI7BxH,EAAM+I,QAAQ,CAAG/I,EAAM+I,QAAQ,EAAI/I,EAAM6D,OAAO,CAG5C7D,EAAM6D,OAAO,EAAK,CAAA,CAAC7D,EAAMyD,MAAM,EAC/B1D,EAAOK,OAAO,CAACiD,eAAe,AAAD,IACzByB,EAAuB6D,IACvBC,CAAAA,EAAsBvC,KAAKiC,GAAG,CAACM,EAAqBvC,KAAKI,GAAG,CAACzG,EAAMgC,KAAK,CAAG2G,GAAU,EAEzFA,EAAY3I,EAAMgC,KAAK,CAG/BjC,CAAAA,EAAO6I,mBAAmB,CAAGA,CACjC,IAIA7I,EAAO0I,cAAc,CAACC,IAAI,CAAC/D,EAAS5E,EAAQ,iBAAkB,WAE1DA,EAAOuH,YAAY,EACvB,IACAvH,EAAO0I,cAAc,CAACC,IAAI,CAAC/D,EAAS5E,EAAQ,sBAAuB,WAC/D,IAAIE,EAEJ,IAAK,IAAMD,KAASD,EAAOyH,MAAM,CAC7BvH,CAAAA,EAAYD,EAAMC,SAAS,AAAD,IAMtBA,EAAU+I,OAAO,CAAG,SAAUC,CAAM,EAIhC,OAHI,IAAI,CAAC1G,cAAc,EACnB,CAAA,IAAI,CAACA,cAAc,CAAG0G,CAAK,EAExB,IAAI,CAACrH,QAAQ,CAACsH,OAAO,CAAC3K,SAAS,CACjCyK,OAAO,CAACtF,KAAK,CAAC,IAAI,CAAEC,UAC7B,EAMI,AAAC1D,EAAUsC,cAAc,EACzBtC,CAAAA,EAAUsC,cAAc,CAAG,CAAC,CAAA,EAEhCvC,EAAM2B,aAAa,GAG/B,IACA5B,EAAO0I,cAAc,CAACC,IAAI,CAAC/D,EAAS5E,EAAOO,KAAK,CAAE,4BAA6B,WAC3E,IAAK,IAAM+D,KAAKtE,EAAOyH,MAAM,CACrBnD,EAAEpE,SAAS,EACXoE,EAAEpE,SAAS,CAACC,SAAS,EACrBmE,EAAEpE,SAAS,CAACkJ,UAAU,GAAK9E,EAAEpE,SAAS,CAACyB,UAAU,EACjD2C,EAAEvE,cAAc,EAG5B,GACJ,CACAsJ,cAAcpJ,CAAK,CAAEqJ,CAAK,CAAE,CACxB,IAAqBC,EAAsBvJ,AAA5B,IAAI,CAA+BK,OAAO,CAACmJ,MAAM,CAAEC,EAAqBxJ,EAAMuJ,MAAM,EAAI,CAAC,EAAGE,EAAUD,EAAmBC,MAAM,EAAIH,EAAoBG,MAAM,CAAG9C,EAAQ1B,EAAoBuE,EAAmB7C,KAAK,CAAE2C,EAAoB3C,KAAK,CAAE5G,AAAjP,IAAI,CAAoP6I,mBAAmB,EAAG9B,EAAS7B,EAAoBuE,EAAmB1C,MAAM,CAAEwC,EAAoBxC,MAAM,EAC3W4C,EAAuCC,EAAS,EAGpD,GAAI5J,AAJW,IAAI,CAIRoC,KAAK,CAACyH,QAAQ,CACrB,OAAO,KAAK,CAACR,cAAcpJ,EAAOqJ,GAGlCA,IACAK,EACIJ,EAAoBO,MAAM,CAACR,EAAM,EAAI,CAAC,EAG1CM,EAAS1E,EAAoB6E,AAFTN,CAAAA,EAAmBK,MAAM,EACzCL,EAAmBK,MAAM,CAACR,EAAM,EAAI,CAAC,CAAA,EACMM,MAAM,CAAED,EAAmBC,MAAM,CAAEA,EAAUD,CAAAA,EAAmBK,UAAU,EAAI,CAAA,IAEjI/J,EAAMgK,QAAQ,CAAIP,GAAUA,AAA0B,IAA1BA,EAAO1D,OAAO,CAAC,OAC3C,IAAMkE,EAAU,CACZvJ,EAAG2F,KAAKC,KAAK,CAACtG,EAAMgC,KAAK,EAAK2E,EAAQ,EAAMgD,EAAS,EACrD/I,EAAGZ,EAAMiC,KAAK,CAAI6E,EAAS,EAAM6C,EAAS,EAC1ChD,MAAOA,EAAQgD,EACf7C,OAAQA,EAAS6C,CACrB,EACA,OAAO,AAAC5J,AAtBO,IAAI,CAsBJO,KAAK,CAACQ,QAAQ,CAAI,CAC7BF,EAAG,AAACqJ,EAAQvJ,CAAC,EAAIuJ,EAAQtD,KAAK,EAC1B5G,AAxBO,IAAI,CAwBJoC,KAAK,CAACC,GAAG,CAAG6H,EAAQvJ,CAAC,CAAGuJ,EAAQtD,KAAK,CAChDjG,EAAGuJ,EAAQrJ,CAAC,EAAIqJ,EAAQrJ,CAAC,CACzB+F,MAAOsD,EAAQnD,MAAM,CACrBA,OAAQmD,EAAQtD,KAAK,AACzB,EAAIsD,CACR,CACJ,CAMA/E,EAAegF,cAAc,CAAGlF,EAAqBN,EAAWwF,cAAc,CAjc/C,CAC3BC,aAAc,CAAA,EACdC,eAAgB,CAAA,EAChBrG,kBAAmB,CAAA,EAInBsG,WAAY,QAIZC,UAAW,EACXC,QAAS,CACLC,aAAc,qGAEdC,YAAa,qBACjB,EACAZ,OAAQ,CACJa,MAAO,CACHC,cAAe,CACnB,CACJ,EAIApG,WAAY,CACRqG,QAAS,CAAA,EACTC,aAAc,CAAA,EAUd3E,UAAW,CAAA,EACX4E,gBAAiB,UACjBC,YAAa,EACbC,YAAa,UACbC,aAAc,EACdzJ,MAAO,UAuBPnB,eAAgB,EAOhBiF,SAAU,KAAK,EAkBf4F,UAAW,WACP,IAAIC,EAYJ,OAXK,IAAI,CAACpL,MAAM,CAACO,KAAK,CAACc,UAAU,CAKpB,iCACL,IAAI,CAACpB,KAAK,CAACmB,UAAU,CAAG,cALnB,sBAAwB,IAAI,CAACnB,KAAK,CAACwB,KAAK,CAC7C,eAME,CAAA,mCACL,CAAA,IAAI,CAACzD,GAAG,EAAI,EAAC,EAAK,cAAa,EAC/B,CAAA,IAAI,CAACqN,KAAK,EAAI,EAAC,CAExB,EACAxE,MAAO,CAEHyE,YAAa,OAEbC,WAAY,SAEZC,SAAU,QAEVC,UAAW,MACf,EAMAxE,OAAQ,CAAA,EAKRyE,cAAe,QACnB,EACAlC,OAAQ,CACJmC,iBAAkB,EAClBjC,OAAQ,SACRE,OAAQ,EACRW,UAAW,EACXxD,OAAQ,EACZ,EACA6E,aAAc,CAAA,EACdC,SAAU,IACVC,aAAc,WAClB,GA0TAlH,EAASO,EAAgB,mBAAoB,WACzC,IAAqB4C,EAAQ/H,AAAd,IAAI,CAAiBgI,SAAS,CAAC,KAC1CrC,EAAgB,EAGpB,IAAK,IAAM1F,KAFXD,AAFe,IAAI,CAEZ4F,aAAa,CAAG5F,AAFR,IAAI,CAEWiI,gBAAgB,GAE1BjI,AAJL,IAAI,CAIQ4F,aAAa,EAChC3F,GACA0F,GAGR3F,CATe,IAAI,CASZ8F,kBAAkB,CAAGH,EAC5B,IAAI,CAACoG,SAAS,CAACC,SAAS,CAAC,IAAK,AAAIC,MAAMlE,EAAMD,MAAM,EAAEoE,IAAI,CAAC,GAC/D,GACAlH,EAAOG,EAAe3G,SAAS,CAAE,CAE7B2N,YAAazH,EAAalG,SAAS,CAAC2N,WAAW,CAC/C/M,WAAYU,EACZsM,cAAe,CAAC,cAAe,kBAAkB,AACrD,GACApN,IAA0IqN,kBAAkB,CAAC,WAAYlH,GAa5I,IAAMvG,EAAiBE,IAG1C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}