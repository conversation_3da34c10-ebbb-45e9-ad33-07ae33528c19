{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/vector\n * @requires highcharts\n *\n * Vector plot series module\n *\n * (c) 2010-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/vector\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/vector\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ vector_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/Vector/VectorSeriesDefaults.js\n/* *\n *\n *  Vector plot series module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A vector plot is a type of cartesian chart where each point has an X and\n * Y position, a length and a direction. Vectors are drawn as arrows.\n *\n * @sample {highcharts|highstock} highcharts/demo/vector-plot/\n *         Vector pot\n *\n * @since        6.0.0\n * @extends      plotOptions.scatter\n * @excluding    boostThreshold, marker, connectEnds, connectNulls,\n *               cropThreshold, dashStyle, dragDrop, gapSize, gapUnit,\n *               dataGrouping, linecap, shadow, stacking, step, jitter,\n *               boostBlending\n * @product      highcharts highstock\n * @requires     modules/vector\n * @optionparent plotOptions.vector\n */\nconst VectorSeriesDefaults = {\n    /**\n     * The line width for each vector arrow.\n     */\n    lineWidth: 2,\n    marker: void 0,\n    /**\n     * What part of the vector it should be rotated around. Can be one of\n     * `start`, `center` and `end`. When `start`, the vectors will start\n     * from the given [x, y] position, and when `end` the vectors will end\n     * in the [x, y] position.\n     *\n     * @sample highcharts/plotoptions/vector-rotationorigin-start/\n     *         Rotate from start\n     *\n     * @validvalue [\"start\", \"center\", \"end\"]\n     */\n    rotationOrigin: 'center',\n    states: {\n        hover: {\n            /**\n             * Additonal line width for the vector errors when they are\n             * hovered.\n             */\n            lineWidthPlus: 1\n        }\n    },\n    tooltip: {\n        /**\n         * @default [{point.x}, {point.y}] Length: {point.length} Direction: {point.direction}°\n         */\n        pointFormat: '<b>[{point.x}, {point.y}]</b><br/>Length: <b>{point.length}</b><br/>Direction: <b>{point.direction}\\u00B0</b><br/>'\n    },\n    /**\n     * Maximum length of the arrows in the vector plot. The individual arrow\n     * length is computed between 0 and this value.\n     */\n    vectorLength: 20\n};\n/**\n * A `vector` series. If the [type](#series.vector.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.vector\n * @excluding dataParser, dataURL, boostThreshold, boostBlending\n * @product   highcharts highstock\n * @requires  modules/vector\n * @apioption series.vector\n */\n/**\n * An array of data points for the series. For the `vector` series type,\n * points can be given in the following ways:\n *\n * 1. An array of arrays with 4 values. In this case, the values correspond to\n *    to `x,y,length,direction`. If the first value is a string, it is applied\n *    as the name of the point, and the `x` value is inferred.\n *    ```js\n *    data: [\n *        [0, 0, 10, 90],\n *        [0, 1, 5, 180],\n *        [1, 1, 2, 270]\n *    ]\n *    ```\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.area.turboThreshold), this option is not\n *    available.\n *    ```js\n *    data: [{\n *        x: 0,\n *        y: 0,\n *        name: \"Point2\",\n *        length: 10,\n *        direction: 90\n *    }, {\n *        x: 1,\n *        y: 1,\n *        name: \"Point1\",\n *        direction: 270\n *    }]\n *    ```\n *\n * @sample {highcharts} highcharts/series/data-array-of-arrays/\n *         Arrays of numeric x and y\n * @sample {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *         Arrays of datetime x and y\n * @sample {highcharts} highcharts/series/data-array-of-name-value/\n *         Arrays of point.name and y\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<Array<(number|string),number,number,number>|*>}\n * @extends   series.line.data\n * @product   highcharts highstock\n * @apioption series.vector.data\n */\n/**\n * The length of the vector. The rendered length will relate to the\n * `vectorLength` setting.\n *\n * @type      {number}\n * @product   highcharts highstock\n * @apioption series.vector.data.length\n */\n/**\n * The vector direction in degrees, where 0 is north (pointing towards south).\n *\n * @type      {number}\n * @product   highcharts highstock\n * @apioption series.vector.data.direction\n */\n''; // Adds doclets above to the transpiled file\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Vector_VectorSeriesDefaults = (VectorSeriesDefaults);\n\n;// ./code/es-modules/Series/Vector/VectorSeries.js\n/* *\n *\n *  Vector plot series module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { animObject } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nconst { series: Series, seriesTypes: { scatter: ScatterSeries } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { arrayMax, extend, merge, pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * The vector series class.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.vector\n *\n * @augments Highcharts.seriesTypes.scatter\n */\nclass VectorSeries extends ScatterSeries {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Fade in the arrows on initializing series.\n     * @private\n     */\n    animate(init) {\n        if (init) {\n            this.markerGroup.attr({\n                opacity: 0.01\n            });\n        }\n        else {\n            this.markerGroup.animate({\n                opacity: 1\n            }, animObject(this.options.animation));\n        }\n    }\n    /**\n     * Create a single arrow. It is later rotated around the zero\n     * centerpoint.\n     * @private\n     */\n    arrow(point) {\n        const fraction = point.length / this.lengthMax, u = fraction * this.options.vectorLength / 20, o = {\n            start: 10 * u,\n            center: 0,\n            end: -10 * u\n        }[this.options.rotationOrigin] || 0, \n        // The stem and the arrow head. Draw the arrow first with rotation\n        // 0, which is the arrow pointing down (vector from north to south).\n        path = [\n            ['M', 0, 7 * u + o], // Base of arrow\n            ['L', -1.5 * u, 7 * u + o],\n            ['L', 0, 10 * u + o],\n            ['L', 1.5 * u, 7 * u + o],\n            ['L', 0, 7 * u + o],\n            ['L', 0, -10 * u + o] // Top\n        ];\n        return path;\n    }\n    /*\n    DrawLegendSymbol: function (legend, item) {\n        let options = legend.options,\n            symbolHeight = legend.symbolHeight,\n            square = options.squareSymbol,\n            symbolWidth = square ? symbolHeight : legend.symbolWidth,\n            path = this.arrow.call({\n                lengthMax: 1,\n                options: {\n                    vectorLength: symbolWidth\n                }\n            }, {\n                length: 1\n            });\n        legendItem.line = this.chart.renderer.path(path)\n        .addClass('highcharts-point')\n        .attr({\n            zIndex: 3,\n            translateY: symbolWidth / 2,\n            rotation: 270,\n            'stroke-width': 1,\n            'stroke': 'black'\n        }).add(item.legendItem.group);\n    },\n    */\n    /**\n     * @private\n     */\n    drawPoints() {\n        const chart = this.chart;\n        for (const point of this.points) {\n            const plotX = point.plotX, plotY = point.plotY;\n            if (this.options.clip === false ||\n                chart.isInsidePlot(plotX, plotY, { inverted: chart.inverted })) {\n                if (!point.graphic) {\n                    point.graphic = this.chart.renderer\n                        .path()\n                        .add(this.markerGroup)\n                        .addClass('highcharts-point ' +\n                        'highcharts-color-' +\n                        pick(point.colorIndex, point.series.colorIndex));\n                }\n                point.graphic\n                    .attr({\n                    d: this.arrow(point),\n                    translateX: plotX,\n                    translateY: plotY,\n                    rotation: point.direction\n                });\n                if (!this.chart.styledMode) {\n                    point.graphic\n                        .attr(this.pointAttribs(point));\n                }\n            }\n            else if (point.graphic) {\n                point.graphic = point.graphic.destroy();\n            }\n        }\n    }\n    /**\n     * Get presentational attributes.\n     * @private\n     */\n    pointAttribs(point, state) {\n        const options = this.options;\n        let stroke = point?.color || this.color, strokeWidth = this.options.lineWidth;\n        if (state) {\n            stroke = options.states[state].color || stroke;\n            strokeWidth =\n                (options.states[state].lineWidth || strokeWidth) +\n                    (options.states[state].lineWidthPlus || 0);\n        }\n        return {\n            'stroke': stroke,\n            'stroke-width': strokeWidth\n        };\n    }\n    /**\n     * @private\n     */\n    translate() {\n        Series.prototype.translate.call(this);\n        this.lengthMax = arrayMax(this.getColumn('length'));\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nVectorSeries.defaultOptions = merge(ScatterSeries.defaultOptions, Vector_VectorSeriesDefaults);\nextend(VectorSeries.prototype, {\n    /**\n     * @ignore\n     * @deprecated\n     */\n    drawGraph: (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).noop,\n    /**\n     * @ignore\n     * @deprecated\n     */\n    getSymbol: (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).noop,\n    /**\n     * @ignore\n     * @deprecated\n     */\n    markerAttribs: (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).noop,\n    parallelArrays: ['x', 'y', 'length', 'direction'],\n    pointArrayMap: ['y', 'length', 'direction']\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('vector', VectorSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Vector_VectorSeries = ((/* unused pure expression or super */ null && (VectorSeries)));\n\n;// ./code/es-modules/masters/modules/vector.js\n\n\n\n\n/* harmony default export */ const vector_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "vector_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "animObject", "series", "Series", "seriesTypes", "scatter", "ScatterSeries", "arrayMax", "extend", "merge", "pick", "VectorSeries", "animate", "init", "markerGroup", "attr", "opacity", "options", "animation", "arrow", "point", "u", "fraction", "length", "lengthMax", "vectorLength", "start", "center", "end", "<PERSON><PERSON><PERSON><PERSON>", "drawPoints", "chart", "points", "plotX", "plotY", "clip", "isInsidePlot", "inverted", "graphic", "renderer", "path", "add", "addClass", "colorIndex", "translateX", "translateY", "rotation", "direction", "styledMode", "pointAttribs", "destroy", "state", "stroke", "color", "strokeWidth", "lineWidth", "states", "lineWidthPlus", "translate", "getColumn", "defaultOptions", "marker", "hover", "tooltip", "pointFormat", "drawGraph", "noop", "getSymbol", "markerAttribs", "parallelArrays", "pointArrayMap", "registerSeriesType"], "mappings": "CAWA,AAXA;;;;;;;;;;CAUC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,4BAA6B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACrH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,4BAA4B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAEzGA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GA2KjL,GAAM,CAAEE,WAAAA,CAAU,CAAE,CAAIH,IAGlB,CAAEI,OAAQC,CAAM,CAAEC,YAAa,CAAEC,QAASC,CAAa,CAAE,CAAE,CAAIN,IAE/D,CAAEO,SAAAA,CAAQ,CAAEC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAE,CAAIZ,GAgB3C,OAAMa,UAAqBL,EAUvBM,QAAQC,CAAI,CAAE,CACNA,EACA,IAAI,CAACC,WAAW,CAACC,IAAI,CAAC,CAClBC,QAAS,GACb,GAGA,IAAI,CAACF,WAAW,CAACF,OAAO,CAAC,CACrBI,QAAS,CACb,EAAGf,EAAW,IAAI,CAACgB,OAAO,CAACC,SAAS,EAE5C,CAMAC,MAAMC,CAAK,CAAE,CACT,IAAgDC,EAAIC,AAAnCF,EAAMG,MAAM,CAAG,IAAI,CAACC,SAAS,CAAiB,IAAI,CAACP,OAAO,CAACQ,YAAY,CAAG,GAAIxC,EAAI,CAC/FyC,MAAO,GAAKL,EACZM,OAAQ,EACRC,IAAK,IAAMP,CACf,CAAC,CAAC,IAAI,CAACJ,OAAO,CAACY,cAAc,CAAC,EAAI,EAWlC,MARO,CACH,CAAC,IAAK,EAAG,EAAIR,EAAIpC,EAAE,CACnB,CAAC,IAAK,KAAOoC,EAAG,EAAIA,EAAIpC,EAAE,CAC1B,CAAC,IAAK,EAAG,GAAKoC,EAAIpC,EAAE,CACpB,CAAC,IAAK,IAAMoC,EAAG,EAAIA,EAAIpC,EAAE,CACzB,CAAC,IAAK,EAAG,EAAIoC,EAAIpC,EAAE,CACnB,CAAC,IAAK,EAAG,IAAMoC,EAAIpC,EAAE,CACxB,AAEL,CA6BA6C,YAAa,CACT,IAAMC,EAAQ,IAAI,CAACA,KAAK,CACxB,IAAK,IAAMX,KAAS,IAAI,CAACY,MAAM,CAAE,CAC7B,IAAMC,EAAQb,EAAMa,KAAK,CAAEC,EAAQd,EAAMc,KAAK,AAC1C,AAAsB,EAAA,IAAtB,IAAI,CAACjB,OAAO,CAACkB,IAAI,EACjBJ,EAAMK,YAAY,CAACH,EAAOC,EAAO,CAAEG,SAAUN,EAAMM,QAAQ,AAAC,IACxD,AAACjB,EAAMkB,OAAO,EACdlB,CAAAA,EAAMkB,OAAO,CAAG,IAAI,CAACP,KAAK,CAACQ,QAAQ,CAC9BC,IAAI,GACJC,GAAG,CAAC,IAAI,CAAC3B,WAAW,EACpB4B,QAAQ,CAAC,qCAEVhC,EAAKU,EAAMuB,UAAU,CAAEvB,EAAMlB,MAAM,CAACyC,UAAU,EAAC,EAEvDvB,EAAMkB,OAAO,CACRvB,IAAI,CAAC,CACNlC,EAAG,IAAI,CAACsC,KAAK,CAACC,GACdwB,WAAYX,EACZY,WAAYX,EACZY,SAAU1B,EAAM2B,SAAS,AAC7B,GACI,AAAC,IAAI,CAAChB,KAAK,CAACiB,UAAU,EACtB5B,EAAMkB,OAAO,CACRvB,IAAI,CAAC,IAAI,CAACkC,YAAY,CAAC7B,KAG3BA,EAAMkB,OAAO,EAClBlB,CAAAA,EAAMkB,OAAO,CAAGlB,EAAMkB,OAAO,CAACY,OAAO,EAAC,CAE9C,CACJ,CAKAD,aAAa7B,CAAK,CAAE+B,CAAK,CAAE,CACvB,IAAMlC,EAAU,IAAI,CAACA,OAAO,CACxBmC,EAAShC,GAAOiC,OAAS,IAAI,CAACA,KAAK,CAAEC,EAAc,IAAI,CAACrC,OAAO,CAACsC,SAAS,CAO7E,OANIJ,IACAC,EAASnC,EAAQuC,MAAM,CAACL,EAAM,CAACE,KAAK,EAAID,EACxCE,EACI,AAACrC,CAAAA,EAAQuC,MAAM,CAACL,EAAM,CAACI,SAAS,EAAID,CAAU,EACzCrC,CAAAA,EAAQuC,MAAM,CAACL,EAAM,CAACM,aAAa,EAAI,CAAA,GAE7C,CACH,OAAUL,EACV,eAAgBE,CACpB,CACJ,CAIAI,WAAY,CACRvD,EAAOX,SAAS,CAACkE,SAAS,CAAChE,IAAI,CAAC,IAAI,EACpC,IAAI,CAAC8B,SAAS,CAAGjB,EAAS,IAAI,CAACoD,SAAS,CAAC,UAC7C,CACJ,CAMAhD,EAAaiD,cAAc,CAAGnD,EAAMH,EAAcsD,cAAc,CAnSnC,CAIzBL,UAAW,EACXM,OAAQ,KAAK,EAYbhC,eAAgB,SAChB2B,OAAQ,CACJM,MAAO,CAKHL,cAAe,CACnB,CACJ,EACAM,QAAS,CAILC,YAAa,kHACjB,EAKAvC,aAAc,EAClB,GA8PAjB,EAAOG,EAAanB,SAAS,CAAE,CAK3ByE,UAAW,AAACnE,IAA+EoE,IAAI,CAK/FC,UAAW,AAACrE,IAA+EoE,IAAI,CAK/FE,cAAe,AAACtE,IAA+EoE,IAAI,CACnGG,eAAgB,CAAC,IAAK,IAAK,SAAU,YAAY,CACjDC,cAAe,CAAC,IAAK,SAAU,YAAY,AAC/C,GACAtE,IAA0IuE,kBAAkB,CAAC,SAAU5D,GAa1I,IAAMf,EAAeE,IAGxC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}