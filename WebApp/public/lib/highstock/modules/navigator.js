!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/navigator
 * @requires highcharts
 *
 * Standalone navigator module
 *
 * (c) 2009-2025 Mateusz Bernacik
 *
 * License: www.highcharts.com/license
 */function(t,i){"object"==typeof exports&&"object"==typeof module?module.exports=i(t._Highcharts,t._Highcharts.Chart,t._Highcharts.Axis,t._Highcharts.Color,t._Highcharts.SeriesRegistry,t._Highcharts.RendererRegistry,t._Highcharts.SVGRenderer):"function"==typeof define&&define.amd?define("highcharts/modules/navigator",["highcharts/highcharts"],function(t){return i(t,t.Chart,t.Axis,t.Color,t.SeriesRegistry,t.RendererRegistry,t.SVGRenderer)}):"object"==typeof exports?exports["highcharts/modules/navigator"]=i(t._Highcharts,t._Highcharts.Chart,t._Highcharts.Axis,t._Highcharts.Color,t._Highcharts.SeriesRegistry,t._Highcharts.RendererRegistry,t._Highcharts.SVGRenderer):t.Highcharts=i(t.Highcharts,t.Highcharts.Chart,t.Highcharts.Axis,t.Highcharts.Color,t.Highcharts.SeriesRegistry,t.Highcharts.RendererRegistry,t.Highcharts.SVGRenderer)}("undefined"==typeof window?this:window,(t,i,e,s,a,r,o)=>(()=>{"use strict";let n;var h,l={512:t=>{t.exports=a},532:t=>{t.exports=e},540:t=>{t.exports=o},608:t=>{t.exports=r},620:t=>{t.exports=s},944:i=>{i.exports=t},960:t=>{t.exports=i}},d={};function c(t){var i=d[t];if(void 0!==i)return i.exports;var e=d[t]={exports:{}};return l[t](e,e.exports,c),e.exports}c.n=t=>{var i=t&&t.__esModule?()=>t.default:()=>t;return c.d(i,{a:i}),i},c.d=(t,i)=>{for(var e in i)c.o(i,e)&&!c.o(t,e)&&Object.defineProperty(t,e,{enumerable:!0,get:i[e]})},c.o=(t,i)=>Object.prototype.hasOwnProperty.call(t,i);var g={};c.d(g,{default:()=>t7});var p=c(944),x=c.n(p),u=c(960),m=c.n(u),v=c(532),b=c.n(v);let{isTouchDevice:f}=x(),{addEvent:M,merge:A,pick:y}=x(),E=[];function k(){this.navigator&&this.navigator.setBaseSeries(null,!1)}function w(){let t,i,e,s=this.legend,a=this.navigator;if(a){t=s&&s.options,i=a.xAxis,e=a.yAxis;let{scrollbarHeight:r,scrollButtonSize:o}=a;this.inverted?(a.left=a.opposite?this.chartWidth-r-a.height:this.spacing[3]+r,a.top=this.plotTop+o):(a.left=y(i.left,this.plotLeft+o),a.top=a.navigatorOptions.top||this.chartHeight-a.height-r-(this.scrollbar?.options.margin||0)-this.spacing[2]-(this.rangeSelector&&this.extraBottomMargin?this.rangeSelector.getHeight():0)-(t&&"bottom"===t.verticalAlign&&"proximate"!==t.layout&&t.enabled&&!t.floating?s.legendHeight+y(t.margin,10):0)-(this.titleOffset?this.titleOffset[2]:0)),i&&e&&(this.inverted?i.options.left=e.options.left=a.left:i.options.top=e.options.top=a.top,i.setAxisSize(),e.setAxisSize())}}function S(t){!this.navigator&&!this.scroller&&(this.options.navigator.enabled||this.options.scrollbar.enabled)&&(this.scroller=this.navigator=new n(this),y(t.redraw,!0)&&this.redraw(t.animation))}function O(){let t=this.options;(t.navigator.enabled||t.scrollbar.enabled)&&(this.scroller=this.navigator=new n(this))}function z(){let t=this.options,i=t.navigator,e=t.rangeSelector;if((i&&i.enabled||e&&e.enabled)&&(!f&&"x"===this.zooming.type||f&&"x"===this.zooming.pinchType))return!1}function R(t){let i=t.navigator;if(i&&t.xAxis[0]){let e=t.xAxis[0].getExtremes();i.render(e.min,e.max)}}function D(t){let i=t.options.navigator||{},e=t.options.scrollbar||{};!this.navigator&&!this.scroller&&(i.enabled||e.enabled)&&(A(!0,this.options.navigator,i),A(!0,this.options.scrollbar,e),delete t.options.navigator,delete t.options.scrollbar)}let T={compose:function(t,i){if(x().pushUnique(E,t)){let e=t.prototype;n=i,e.callbacks.push(R),M(t,"afterAddSeries",k),M(t,"afterSetChartSize",w),M(t,"afterUpdate",S),M(t,"beforeRender",O),M(t,"beforeShowResetZoom",z),M(t,"update",D)}}},{isTouchDevice:B}=x(),{addEvent:C,correctFloat:H,defined:L,isNumber:W,pick:P}=x();function U(){this.navigatorAxis||(this.navigatorAxis=new I(this))}function N(t){let i,e=this.chart,s=e.options,a=s.navigator,r=this.navigatorAxis,o=e.zooming.pinchType,n=s.rangeSelector,h=e.zooming.type;if(this.isXAxis&&(a?.enabled||n?.enabled)){if("y"===h&&"zoom"===t.trigger)i=!1;else if(("zoom"===t.trigger&&"xy"===h||B&&"xy"===o)&&this.options.range){let i=r.previousZoom;L(t.min)?r.previousZoom=[this.min,this.max]:i&&(t.min=i[0],t.max=i[1],r.previousZoom=void 0)}}void 0!==i&&t.preventDefault()}class I{static compose(t){t.keepProps.includes("navigatorAxis")||(t.keepProps.push("navigatorAxis"),C(t,"init",U),C(t,"setExtremes",N))}constructor(t){this.axis=t}destroy(){this.axis=void 0}toFixedRange(t,i,e,s){let a=this.axis,r=(a.pointRange||0)/2,o=P(e,a.translate(t,!0,!a.horiz)),n=P(s,a.translate(i,!0,!a.horiz));return L(e)||(o=H(o+r)),L(s)||(n=H(n-r)),W(o)&&W(n)||(o=n=void 0),{min:o,max:n}}}var X=c(620),_=c.n(X),Y=c(512),G=c.n(Y);let{parse:F}=_(),{seriesTypes:V}=G(),j={height:40,margin:22,maskInside:!0,handles:{width:7,borderRadius:0,height:15,symbols:["navigator-handle","navigator-handle"],enabled:!0,lineWidth:1,backgroundColor:"#f2f2f2",borderColor:"#999999"},maskFill:F("#667aff").setOpacity(.3).get(),outlineColor:"#999999",outlineWidth:1,series:{type:void 0===V.areaspline?"line":"areaspline",fillOpacity:.05,lineWidth:1,compare:null,sonification:{enabled:!1},dataGrouping:{approximation:"average",enabled:!0,groupPixelWidth:2,firstAnchor:"firstPoint",anchor:"middle",lastAnchor:"lastPoint",units:[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1,2,3,4]],["week",[1,2,3]],["month",[1,3,6]],["year",null]]},dataLabels:{enabled:!1,zIndex:2},id:"highcharts-navigator-series",className:"highcharts-navigator-series",lineColor:null,marker:{enabled:!1},threshold:null},xAxis:{className:"highcharts-navigator-xaxis",tickLength:0,lineWidth:0,gridLineColor:"#e6e6e6",id:"navigator-x-axis",gridLineWidth:1,tickPixelInterval:200,labels:{align:"left",style:{color:"#000000",fontSize:"0.7em",opacity:.6,textOutline:"2px contrast"},x:3,y:-4},crosshair:!1},yAxis:{className:"highcharts-navigator-yaxis",gridLineWidth:0,startOnTick:!1,endOnTick:!1,minPadding:.1,id:"navigator-y-axis",maxPadding:.1,labels:{enabled:!1},crosshair:!1,title:{text:void 0},tickLength:0,tickWidth:0}},{defined:Z,isNumber:q,pick:J}=x(),K={rect:function(t,i,e,s,a){return a?.r?function(t,i,e,s,a){let r=a?.r||0;return[["M",t+r,i],["L",t+e-r,i],["A",r,r,0,0,1,t+e,i+r],["L",t+e,i+s-r],["A",r,r,0,0,1,t+e-r,i+s],["L",t+r,i+s],["A",r,r,0,0,1,t,i+s-r],["L",t,i+r],["A",r,r,0,0,1,t+r,i],["Z"]]}(t,i,e,s,a):[["M",t,i],["L",t+e,i],["L",t+e,i+s],["L",t,i+s],["Z"]]}},{relativeLength:Q}=x(),$={"navigator-handle":function(t,i,e,s,a={}){let r=a.width?a.width/2:e,o=Q(a.borderRadius||0,Math.min(2*r,s));return[["M",-1.5,(s=a.height||s)/2-3.5],["L",-1.5,s/2+4.5],["M",.5,s/2-3.5],["L",.5,s/2+4.5],...K.rect(-r-1,.5,2*r+1,s,{r:o})]}};var tt=c(608),ti=c.n(tt);let{defined:te}=x(),{defaultOptions:ts}=x(),{composed:ta}=x(),{getRendererType:tr}=ti(),{setFixedRange:to}={setFixedRange:function(t){let i=this.xAxis[0];te(i.dataMax)&&te(i.dataMin)&&t?this.fixedRange=Math.min(t,i.dataMax-i.dataMin):this.fixedRange=t}},{addEvent:tn,extend:th,pushUnique:tl}=x();function td(){this.chart.navigator&&!this.options.isInternal&&this.chart.navigator.setBaseSeries(null,!1)}let tc={compose:function(t,i,e){I.compose(i),tl(ta,"Navigator")&&(t.prototype.setFixedRange=to,th(tr().prototype.symbols,$),th(ts,{navigator:j}),tn(e,"afterUpdate",td))}},{composed:tg}=x(),{addEvent:tp,defined:tx,pick:tu,pushUnique:tm}=x();!function(t){let i;function e(t){let i=tu(t.options?.min,t.min),e=tu(t.options?.max,t.max);return{axisMin:i,axisMax:e,scrollMin:tx(t.dataMin)?Math.min(i,t.min,t.dataMin,tu(t.threshold,1/0)):i,scrollMax:tx(t.dataMax)?Math.max(e,t.max,t.dataMax,tu(t.threshold,-1/0)):e}}function s(){let t=this.scrollbar,i=t&&!t.options.opposite,e=this.horiz?2:i?3:1;t&&(this.chart.scrollbarsOffsets=[0,0],this.chart.axisOffset[e]+=t.size+(t.options.margin||0))}function a(){let t=this;t.options?.scrollbar?.enabled&&(t.options.scrollbar.vertical=!t.horiz,t.options.startOnTick=t.options.endOnTick=!1,t.scrollbar=new i(t.chart.renderer,t.options.scrollbar,t.chart),tp(t.scrollbar,"changed",function(i){let s,a,{axisMin:r,axisMax:o,scrollMin:n,scrollMax:h}=e(t),l=h-n;if(tx(r)&&tx(o))if(t.horiz&&!t.reversed||!t.horiz&&t.reversed?(s=n+l*this.to,a=n+l*this.from):(s=n+l*(1-this.from),a=n+l*(1-this.to)),this.shouldUpdateExtremes(i.DOMType)){let e="mousemove"!==i.DOMType&&"touchmove"!==i.DOMType&&void 0;t.setExtremes(a,s,!0,e,i)}else this.setRange(this.from,this.to)}))}function r(){let t,i,s,{scrollMin:a,scrollMax:r}=e(this),o=this.scrollbar,n=this.axisTitleMargin+(this.titleOffset||0),h=this.chart.scrollbarsOffsets,l=this.options.margin||0;if(o&&h){if(this.horiz)this.opposite||(h[1]+=n),o.position(this.left,this.top+this.height+2+h[1]-(this.opposite?l:0),this.width,this.height),this.opposite||(h[1]+=l),t=1;else{let i;this.opposite&&(h[0]+=n),i=o.options.opposite?this.left+this.width+2+h[0]-(this.opposite?0:l):this.opposite?0:l,o.position(i,this.top,this.width,this.height),this.opposite&&(h[0]+=l),t=0}if(h[t]+=o.size+(o.options.margin||0),isNaN(a)||isNaN(r)||!tx(this.min)||!tx(this.max)||this.dataMin===this.dataMax)o.setRange(0,1);else if(this.min===this.max){let t=this.pointRange/(this.dataMax+1);i=t*this.min,s=t*(this.max+1),o.setRange(i,s)}else i=(this.min-a)/(r-a),s=(this.max-a)/(r-a),this.horiz&&!this.reversed||!this.horiz&&this.reversed?o.setRange(i,s):o.setRange(1-s,1-i)}}t.compose=function(t,e){tm(tg,"Axis.Scrollbar")&&(i=e,tp(t,"afterGetOffset",s),tp(t,"afterInit",a),tp(t,"afterRender",r))}}(h||(h={}));let tv=h,tb={height:10,barBorderRadius:5,buttonBorderRadius:0,buttonsEnabled:!1,liveRedraw:void 0,margin:void 0,minWidth:6,opposite:!0,step:.2,zIndex:3,barBackgroundColor:"#cccccc",barBorderWidth:0,barBorderColor:"#cccccc",buttonArrowColor:"#333333",buttonBackgroundColor:"#e6e6e6",buttonBorderColor:"#cccccc",buttonBorderWidth:1,rifleColor:"none",trackBackgroundColor:"rgba(255, 255, 255, 0.001)",trackBorderColor:"#cccccc",trackBorderRadius:5,trackBorderWidth:1},{defaultOptions:tf}=x(),{composed:tM}=x(),{addEvent:tA,correctFloat:ty,crisp:tE,defined:tk,destroyObjectProperties:tw,extend:tS,fireEvent:tO,merge:tz,pick:tR,pushUnique:tD,removeEvent:tT}=x();class tB{static compose(t){tv.compose(t,tB),tD(tM,"Scrollbar")&&tS(tf,{scrollbar:tb})}static swapXY(t,i){return i&&t.forEach(t=>{let i,e=t.length;for(let s=0;s<e;s+=2)"number"==typeof(i=t[s+1])&&(t[s+1]=t[s+2],t[s+2]=i)}),t}constructor(t,i,e){this._events=[],this.chartX=0,this.chartY=0,this.from=0,this.scrollbarButtons=[],this.scrollbarLeft=0,this.scrollbarStrokeWidth=1,this.scrollbarTop=0,this.size=0,this.to=0,this.trackBorderWidth=1,this.x=0,this.y=0,this.init(t,i,e)}addEvents(){let t=this.options.inverted?[1,0]:[0,1],i=this.scrollbarButtons,e=this.scrollbarGroup.element,s=this.track.element,a=this.mouseDownHandler.bind(this),r=this.mouseMoveHandler.bind(this),o=this.mouseUpHandler.bind(this),n=[[i[t[0]].element,"click",this.buttonToMinClick.bind(this)],[i[t[1]].element,"click",this.buttonToMaxClick.bind(this)],[s,"click",this.trackClick.bind(this)],[e,"mousedown",a],[e.ownerDocument,"mousemove",r],[e.ownerDocument,"mouseup",o],[e,"touchstart",a],[e.ownerDocument,"touchmove",r],[e.ownerDocument,"touchend",o]];n.forEach(function(t){tA.apply(null,t)}),this._events=n}buttonToMaxClick(t){let i=(this.to-this.from)*tR(this.options.step,.2);this.updatePosition(this.from+i,this.to+i),tO(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})}buttonToMinClick(t){let i=ty(this.to-this.from)*tR(this.options.step,.2);this.updatePosition(ty(this.from-i),ty(this.to-i)),tO(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})}cursorToScrollbarPosition(t){let i=this.options,e=i.minWidth>this.calculatedWidth?i.minWidth:0;return{chartX:(t.chartX-this.x-this.xOffset)/(this.barWidth-e),chartY:(t.chartY-this.y-this.yOffset)/(this.barWidth-e)}}destroy(){let t=this,i=t.chart.scroller;t.removeEvents(),["track","scrollbarRifles","scrollbar","scrollbarGroup","group"].forEach(function(i){t[i]&&t[i].destroy&&(t[i]=t[i].destroy())}),i&&t===i.scrollbar&&(i.scrollbar=null,tw(i.scrollbarButtons))}drawScrollbarButton(t){let i=this.renderer,e=this.scrollbarButtons,s=this.options,a=this.size,r=i.g().add(this.group);if(e.push(r),s.buttonsEnabled){let o=i.rect().addClass("highcharts-scrollbar-button").add(r);this.chart.styledMode||o.attr({stroke:s.buttonBorderColor,"stroke-width":s.buttonBorderWidth,fill:s.buttonBackgroundColor}),o.attr(o.crisp({x:-.5,y:-.5,width:a,height:a,r:s.buttonBorderRadius},o.strokeWidth()));let n=i.path(tB.swapXY([["M",a/2+(t?-1:1),a/2-3],["L",a/2+(t?-1:1),a/2+3],["L",a/2+(t?2:-2),a/2]],s.vertical)).addClass("highcharts-scrollbar-arrow").add(e[t]);this.chart.styledMode||n.attr({fill:s.buttonArrowColor})}}init(t,i,e){this.scrollbarButtons=[],this.renderer=t,this.userOptions=i,this.options=tz(tb,tf.scrollbar,i),this.options.margin=tR(this.options.margin,10),this.chart=e,this.size=tR(this.options.size,this.options.height),i.enabled&&(this.render(),this.addEvents())}mouseDownHandler(t){let i=this.chart.pointer?.normalize(t)||t,e=this.cursorToScrollbarPosition(i);this.chartX=e.chartX,this.chartY=e.chartY,this.initPositions=[this.from,this.to],this.grabbedCenter=!0}mouseMoveHandler(t){let i,e=this.chart.pointer?.normalize(t)||t,s=this.options.vertical?"chartY":"chartX",a=this.initPositions||[];this.grabbedCenter&&(!t.touches||0!==t.touches[0][s])&&(i=this.cursorToScrollbarPosition(e)[s]-this[s],this.hasDragged=!0,this.updatePosition(a[0]+i,a[1]+i),this.hasDragged&&tO(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMType:t.type,DOMEvent:t}))}mouseUpHandler(t){this.hasDragged&&tO(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMType:t.type,DOMEvent:t}),this.grabbedCenter=this.hasDragged=this.chartX=this.chartY=null}position(t,i,e,s){let{buttonsEnabled:a,margin:r=0,vertical:o}=this.options,n=this.rendered?"animate":"attr",h=s,l=0;this.group.show(),this.x=t,this.y=i+this.trackBorderWidth,this.width=e,this.height=s,this.xOffset=h,this.yOffset=l,o?(this.width=this.yOffset=e=l=this.size,this.xOffset=h=0,this.yOffset=l=a?this.size:0,this.barWidth=s-(a?2*e:0),this.x=t+=r):(this.height=s=this.size,this.xOffset=h=a?this.size:0,this.barWidth=e-(a?2*s:0),this.y=this.y+r),this.group[n]({translateX:t,translateY:this.y}),this.track[n]({width:e,height:s}),this.scrollbarButtons[1][n]({translateX:o?0:e-h,translateY:o?s-l:0})}removeEvents(){this._events.forEach(function(t){tT.apply(null,t)}),this._events.length=0}render(){let t=this.renderer,i=this.options,e=this.size,s=this.chart.styledMode,a=t.g("scrollbar").attr({zIndex:i.zIndex}).hide().add();this.group=a,this.track=t.rect().addClass("highcharts-scrollbar-track").attr({r:i.trackBorderRadius||0,height:e,width:e}).add(a),s||this.track.attr({fill:i.trackBackgroundColor,stroke:i.trackBorderColor,"stroke-width":i.trackBorderWidth});let r=this.trackBorderWidth=this.track.strokeWidth();this.track.attr({x:-tE(0,r),y:-tE(0,r)}),this.scrollbarGroup=t.g().add(a),this.scrollbar=t.rect().addClass("highcharts-scrollbar-thumb").attr({height:e-r,width:e-r,r:i.barBorderRadius||0}).add(this.scrollbarGroup),this.scrollbarRifles=t.path(tB.swapXY([["M",-3,e/4],["L",-3,2*e/3],["M",0,e/4],["L",0,2*e/3],["M",3,e/4],["L",3,2*e/3]],i.vertical)).addClass("highcharts-scrollbar-rifles").add(this.scrollbarGroup),s||(this.scrollbar.attr({fill:i.barBackgroundColor,stroke:i.barBorderColor,"stroke-width":i.barBorderWidth}),this.scrollbarRifles.attr({stroke:i.rifleColor,"stroke-width":1})),this.scrollbarStrokeWidth=this.scrollbar.strokeWidth(),this.scrollbarGroup.translate(-tE(0,this.scrollbarStrokeWidth),-tE(0,this.scrollbarStrokeWidth)),this.drawScrollbarButton(0),this.drawScrollbarButton(1)}setRange(t,i){let e,s,a=this.options,r=a.vertical,o=a.minWidth,n=this.barWidth,h=!this.rendered||this.hasDragged||this.chart.navigator&&this.chart.navigator.hasDragged?"attr":"animate";if(!tk(n))return;let l=n*Math.min(i,1);e=Math.ceil(n*(t=Math.max(t,0))),this.calculatedWidth=s=ty(l-e),s<o&&(e=(n-o+s)*t,s=o);let d=Math.floor(e+this.xOffset+this.yOffset),c=s/2-.5;this.from=t,this.to=i,r?(this.scrollbarGroup[h]({translateY:d}),this.scrollbar[h]({height:s}),this.scrollbarRifles[h]({translateY:c}),this.scrollbarTop=d,this.scrollbarLeft=0):(this.scrollbarGroup[h]({translateX:d}),this.scrollbar[h]({width:s}),this.scrollbarRifles[h]({translateX:c}),this.scrollbarLeft=d,this.scrollbarTop=0),s<=12?this.scrollbarRifles.hide():this.scrollbarRifles.show(),!1===a.showFull&&(t<=0&&i>=1?this.group.hide():this.group.show()),this.rendered=!0}shouldUpdateExtremes(t){return tR(this.options.liveRedraw,x().svg&&!x().isTouchDevice&&!this.chart.boosted)||"mouseup"===t||"touchend"===t||!tk(t)}trackClick(t){let i=this.chart.pointer?.normalize(t)||t,e=this.to-this.from,s=this.y+this.scrollbarTop,a=this.x+this.scrollbarLeft;this.options.vertical&&i.chartY>s||!this.options.vertical&&i.chartX>a?this.updatePosition(this.from+e,this.to+e):this.updatePosition(this.from-e,this.to-e),tO(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})}update(t){this.destroy(),this.init(this.chart.renderer,tz(!0,this.options,t),this.chart)}updatePosition(t,i){i>1&&(t=ty(1-ty(i-t)),i=1),t<0&&(i=ty(i-t),t=0),this.from=t,this.to=i}}tB.defaultOptions=tb;var tC=c(540),tH=c.n(tC);let{defaultOptions:tL}=x(),{isTouchDevice:tW}=x(),{prototype:{symbols:tP}}=tH(),{addEvent:tU,clamp:tN,correctFloat:tI,defined:tX,destroyObjectProperties:t_,erase:tY,extend:tG,find:tF,fireEvent:tV,isArray:tj,isNumber:tZ,merge:tq,pick:tJ,removeEvent:tK,splat:tQ}=x();function t$(t,...i){let e=[].filter.call(i,tZ);if(e.length)return Math[t].apply(0,e)}class t0{static compose(t,i,e){T.compose(t,t0),tc.compose(t,i,e)}constructor(t){this.isDirty=!1,this.scrollbarHeight=0,this.init(t)}drawHandle(t,i,e,s){let a=this.navigatorOptions.handles.height;this.handles[i][s](e?{translateX:Math.round(this.left+this.height/2),translateY:Math.round(this.top+parseInt(t,10)+.5-a)}:{translateX:Math.round(this.left+parseInt(t,10)),translateY:Math.round(this.top+this.height/2-a/2-1)})}drawOutline(t,i,e,s){let a=this.navigatorOptions.maskInside,r=this.outline.strokeWidth(),o=r/2,n=r%2/2,h=this.scrollButtonSize,l=this.size,d=this.top,c=this.height,g=d-o,p=d+c,x=this.left,u,m;e?(u=d+i+n,i=d+t+n,m=[["M",x+c,d-h-n],["L",x+c,u],["L",x,u],["M",x,i],["L",x+c,i],["L",x+c,d+l+h]],a&&m.push(["M",x+c,u-o],["L",x+c,i+o])):(x-=h,t+=x+h-n,i+=x+h-n,m=[["M",x,g],["L",t,g],["L",t,p],["M",i,p],["L",i,g],["L",x+l+2*h,g]],a&&m.push(["M",t-o,g],["L",i+o,g])),this.outline[s]({d:m})}drawMasks(t,i,e,s){let a,r,o,n,h=this.left,l=this.top,d=this.height;e?(o=[h,h,h],n=[l,l+t,l+i],r=[d,d,d],a=[t,i-t,this.size-i]):(o=[h,h+t,h+i],n=[l,l,l],r=[t,i-t,this.size-i],a=[d,d,d]),this.shades.forEach((t,i)=>{t[s]({x:o[i],y:n[i],width:r[i],height:a[i]})})}renderElements(){let t=this,i=t.navigatorOptions,e=i.maskInside,s=t.chart,a=s.inverted,r=s.renderer,o={cursor:a?"ns-resize":"ew-resize"},n=t.navigatorGroup??(t.navigatorGroup=r.g("navigator").attr({zIndex:8,visibility:"hidden"}).add());if([!e,e,!e].forEach((e,a)=>{let h=t.shades[a]??(t.shades[a]=r.rect().addClass("highcharts-navigator-mask"+(1===a?"-inside":"-outside")).add(n));s.styledMode||(h.attr({fill:e?i.maskFill:"rgba(0,0,0,0)"}),1===a&&h.css(o))}),t.outline||(t.outline=r.path().addClass("highcharts-navigator-outline").add(n)),s.styledMode||t.outline.attr({"stroke-width":i.outlineWidth,stroke:i.outlineColor}),i.handles?.enabled){let e=i.handles,{height:a,width:h}=e;[0,1].forEach(i=>{let l=e.symbols[i];if(t.handles[i]&&t.handles[i].symbolUrl===l){if(!t.handles[i].isImg&&t.handles[i].symbolName!==l){let e=tP[l].call(tP,-h/2-1,0,h,a);t.handles[i].attr({d:e}),t.handles[i].symbolName=l}}else t.handles[i]?.destroy(),t.handles[i]=r.symbol(l,-h/2-1,0,h,a,e),t.handles[i].attr({zIndex:7-i}).addClass("highcharts-navigator-handle highcharts-navigator-handle-"+["left","right"][i]).add(n),t.addMouseEvents();s.inverted&&t.handles[i].attr({rotation:90,rotationOriginX:Math.floor(-h/2),rotationOriginY:(a+h)/2}),s.styledMode||t.handles[i].attr({fill:e.backgroundColor,stroke:e.borderColor,"stroke-width":e.lineWidth,width:e.width,height:e.height,x:-h/2-1,y:0}).css(o)})}}update(t,i=!1){let e=this.chart,s=e.options.chart.inverted!==e.scrollbar?.options.vertical;if(tq(!0,e.options.navigator,t),this.navigatorOptions=e.options.navigator||{},this.setOpposite(),tX(t.enabled)||s)return this.destroy(),this.navigatorEnabled=t.enabled||this.navigatorEnabled,this.init(e);if(this.navigatorEnabled&&(this.isDirty=!0,!1===t.adaptToUpdatedData&&this.baseSeries.forEach(t=>{tK(t,"updatedData",this.updatedDataHandler)},this),t.adaptToUpdatedData&&this.baseSeries.forEach(t=>{t.eventsToUnbind.push(tU(t,"updatedData",this.updatedDataHandler))},this),(t.series||t.baseSeries)&&this.setBaseSeries(void 0,!1),t.height||t.xAxis||t.yAxis)){this.height=t.height??this.height;let i=this.getXAxisOffsets();this.xAxis.update({...t.xAxis,offsets:i,[e.inverted?"width":"height"]:this.height,[e.inverted?"height":"width"]:void 0},!1),this.yAxis.update({...t.yAxis,[e.inverted?"width":"height"]:this.height},!1)}i&&e.redraw()}render(t,i,e,s){let a=this.chart,r=this.xAxis,o=r.pointRange||0,n=r.navigatorAxis.fake?a.xAxis[0]:r,h=this.navigatorEnabled,l=this.rendered,d=a.inverted,c=a.xAxis[0].minRange,g=a.xAxis[0].options.maxRange,p=this.scrollButtonSize,x,u,m,v=this.scrollbarHeight,b,f;if(this.hasDragged&&!tX(e))return;if(this.isDirty&&this.renderElements(),t=tI(t-o/2),i=tI(i+o/2),!tZ(t)||!tZ(i))if(!l)return;else e=0,s=tJ(r.width,n.width);this.left=tJ(r.left,a.plotLeft+p+(d?a.plotWidth:0));let M=this.size=b=tJ(r.len,(d?a.plotHeight:a.plotWidth)-2*p);x=d?v:b+2*p,e=tJ(e,r.toPixels(t,!0)),s=tJ(s,r.toPixels(i,!0)),tZ(e)&&Math.abs(e)!==1/0||(e=0,s=x);let A=r.toValue(e,!0),y=r.toValue(s,!0),E=Math.abs(tI(y-A));E<c?this.grabbedLeft?e=r.toPixels(y-c-o,!0):this.grabbedRight&&(s=r.toPixels(A+c+o,!0)):tX(g)&&tI(E-o)>g&&(this.grabbedLeft?e=r.toPixels(y-g-o,!0):this.grabbedRight&&(s=r.toPixels(A+g+o,!0))),this.zoomedMax=tN(Math.max(e,s),0,M),this.zoomedMin=tN(this.fixedWidth?this.zoomedMax-this.fixedWidth:Math.min(e,s),0,M),this.range=this.zoomedMax-this.zoomedMin,M=Math.round(this.zoomedMax);let k=Math.round(this.zoomedMin);h&&(this.navigatorGroup.attr({visibility:"inherit"}),f=l&&!this.hasDragged?"animate":"attr",this.drawMasks(k,M,d,f),this.drawOutline(k,M,d,f),this.navigatorOptions.handles.enabled&&(this.drawHandle(k,0,d,f),this.drawHandle(M,1,d,f))),this.scrollbar&&(d?(m=this.top-p,u=this.left-v+(h||!n.opposite?0:(n.titleOffset||0)+n.axisTitleMargin),v=b+2*p):(m=this.top+(h?this.height:-v),u=this.left-p),this.scrollbar.position(u,m,x,v),this.scrollbar.setRange(this.zoomedMin/(b||1),this.zoomedMax/(b||1))),this.rendered=!0,this.isDirty=!1,tV(this,"afterRender")}addMouseEvents(){let t=this,i=t.chart,e=i.container,s=[],a,r;t.mouseMoveHandler=a=function(i){t.onMouseMove(i)},t.mouseUpHandler=r=function(i){t.onMouseUp(i)},(s=t.getPartsEvents("mousedown")).push(tU(i.renderTo,"mousemove",a),tU(e.ownerDocument,"mouseup",r),tU(i.renderTo,"touchmove",a),tU(e.ownerDocument,"touchend",r)),s.concat(t.getPartsEvents("touchstart")),t.eventsToUnbind=s,t.series&&t.series[0]&&s.push(tU(t.series[0].xAxis,"foundExtremes",function(){i.navigator.modifyNavigatorAxisExtremes()}))}getPartsEvents(t){let i=this,e=[];return["shades","handles"].forEach(function(s){i[s].forEach(function(a,r){e.push(tU(a.element,t,function(t){i[s+"Mousedown"](t,r)}))})}),e}shadesMousedown(t,i){t=this.chart.pointer?.normalize(t)||t;let e=this.chart,s=this.xAxis,a=this.zoomedMin,r=this.size,o=this.range,n=this.left,h=t.chartX,l,d,c,g;e.inverted&&(h=t.chartY,n=this.top),1===i?(this.grabbedCenter=h,this.fixedWidth=o,this.dragOffset=h-a):(g=h-n-o/2,0===i?g=Math.max(0,g):2===i&&g+o>=r&&(g=r-o,this.reversedExtremes?(g-=o,d=this.getUnionExtremes().dataMin):l=this.getUnionExtremes().dataMax),g!==a&&(this.fixedWidth=o,tX((c=s.navigatorAxis.toFixedRange(g,g+o,d,l)).min)&&tV(this,"setRange",{min:Math.min(c.min,c.max),max:Math.max(c.min,c.max),redraw:!0,eventArguments:{trigger:"navigator"}})))}handlesMousedown(t,i){t=this.chart.pointer?.normalize(t)||t;let e=this.chart,s=e.xAxis[0],a=this.reversedExtremes;0===i?(this.grabbedLeft=!0,this.otherHandlePos=this.zoomedMax,this.fixedExtreme=a?s.min:s.max):(this.grabbedRight=!0,this.otherHandlePos=this.zoomedMin,this.fixedExtreme=a?s.max:s.min),e.setFixedRange(void 0)}onMouseMove(t){let i=this,e=i.chart,s=i.navigatorSize,a=i.range,r=i.dragOffset,o=e.inverted,n=i.left,h;(!t.touches||0!==t.touches[0].pageX)&&(h=(t=e.pointer?.normalize(t)||t).chartX,o&&(n=i.top,h=t.chartY),i.grabbedLeft?(i.hasDragged=!0,i.render(0,0,h-n,i.otherHandlePos)):i.grabbedRight?(i.hasDragged=!0,i.render(0,0,i.otherHandlePos,h-n)):i.grabbedCenter&&(i.hasDragged=!0,h<r?h=r:h>s+r-a&&(h=s+r-a),i.render(0,0,h-r,h-r+a)),i.hasDragged&&i.scrollbar&&tJ(i.scrollbar.options.liveRedraw,!tW&&!this.chart.boosted)&&(t.DOMType=t.type,setTimeout(function(){i.onMouseUp(t)},0)))}onMouseUp(t){let i,e,s,a,r,o,n=this.chart,h=this.xAxis,l=this.scrollbar,d=t.DOMEvent||t,c=n.inverted,g=this.rendered&&!this.hasDragged?"animate":"attr";(this.hasDragged&&(!l||!l.hasDragged)||"scrollbar"===t.trigger)&&(s=this.getUnionExtremes(),this.zoomedMin===this.otherHandlePos?a=this.fixedExtreme:this.zoomedMax===this.otherHandlePos&&(r=this.fixedExtreme),this.zoomedMax===this.size&&(r=this.reversedExtremes?s.dataMin:s.dataMax),0===this.zoomedMin&&(a=this.reversedExtremes?s.dataMax:s.dataMin),tX((o=h.navigatorAxis.toFixedRange(this.zoomedMin,this.zoomedMax,a,r)).min)&&tV(this,"setRange",{min:Math.min(o.min,o.max),max:Math.max(o.min,o.max),redraw:!0,animation:!this.hasDragged&&null,eventArguments:{trigger:"navigator",triggerOp:"navigator-drag",DOMEvent:d}})),"mousemove"!==t.DOMType&&"touchmove"!==t.DOMType&&(this.grabbedLeft=this.grabbedRight=this.grabbedCenter=this.fixedWidth=this.fixedExtreme=this.otherHandlePos=this.hasDragged=this.dragOffset=null),this.navigatorEnabled&&tZ(this.zoomedMin)&&tZ(this.zoomedMax)&&(e=Math.round(this.zoomedMin),i=Math.round(this.zoomedMax),this.shades&&this.drawMasks(e,i,c,g),this.outline&&this.drawOutline(e,i,c,g),this.navigatorOptions.handles.enabled&&Object.keys(this.handles).length===this.handles.length&&(this.drawHandle(e,0,c,g),this.drawHandle(i,1,c,g)))}removeEvents(){this.eventsToUnbind&&(this.eventsToUnbind.forEach(function(t){t()}),this.eventsToUnbind=void 0),this.removeBaseSeriesEvents()}removeBaseSeriesEvents(){let t=this.baseSeries||[];this.navigatorEnabled&&t[0]&&(!1!==this.navigatorOptions.adaptToUpdatedData&&t.forEach(function(t){tK(t,"updatedData",this.updatedDataHandler)},this),t[0].xAxis&&tK(t[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes))}getXAxisOffsets(){return this.chart.inverted?[this.scrollButtonSize,0,-this.scrollButtonSize,0]:[0,-this.scrollButtonSize,0,this.scrollButtonSize]}init(t){let i=t.options,e=i.navigator||{},s=e.enabled,a=i.scrollbar||{},r=a.enabled,o=s&&e.height||0,n=r&&a.height||0,h=a.buttonsEnabled&&n||0;this.handles=[],this.shades=[],this.chart=t,this.setBaseSeries(),this.height=o,this.scrollbarHeight=n,this.scrollButtonSize=h,this.scrollbarEnabled=r,this.navigatorEnabled=s,this.navigatorOptions=e,this.scrollbarOptions=a,this.setOpposite();let l=this,d=l.baseSeries,c=t.xAxis.length,g=t.yAxis.length,p=d&&d[0]&&d[0].xAxis||t.xAxis[0]||{options:{}};if(t.isDirtyBox=!0,l.navigatorEnabled){let i=this.getXAxisOffsets();l.xAxis=new(b())(t,tq({breaks:p.options.breaks,ordinal:p.options.ordinal,overscroll:p.options.overscroll},e.xAxis,{type:"datetime",yAxis:e.yAxis?.id,index:c,isInternal:!0,offset:0,keepOrdinalPadding:!0,startOnTick:!1,endOnTick:!1,minPadding:p.options.ordinal?0:p.options.minPadding,maxPadding:p.options.ordinal?0:p.options.maxPadding,zoomEnabled:!1},t.inverted?{offsets:i,width:o}:{offsets:i,height:o}),"xAxis"),l.yAxis=new(b())(t,tq(e.yAxis,{alignTicks:!1,offset:0,index:g,isInternal:!0,reversed:tJ(e.yAxis&&e.yAxis.reversed,t.yAxis[0]&&t.yAxis[0].reversed,!1),zoomEnabled:!1},t.inverted?{width:o}:{height:o}),"yAxis"),d||e.series.data?l.updateNavigatorSeries(!1):0===t.series.length&&(l.unbindRedraw=tU(t,"beforeRedraw",function(){t.series.length>0&&!l.series&&(l.setBaseSeries(),l.unbindRedraw())})),l.reversedExtremes=t.inverted&&!l.xAxis.reversed||!t.inverted&&l.xAxis.reversed,l.renderElements(),l.addMouseEvents()}else l.xAxis={chart:t,navigatorAxis:{fake:!0},translate:function(i,e){let s=t.xAxis[0],a=s.getExtremes(),r=s.len-2*h,o=t$("min",s.options.min,a.dataMin),n=t$("max",s.options.max,a.dataMax)-o;return e?i*n/r+o:r*(i-o)/n},toPixels:function(t){return this.translate(t)},toValue:function(t){return this.translate(t,!0)}},l.xAxis.navigatorAxis.axis=l.xAxis,l.xAxis.navigatorAxis.toFixedRange=I.prototype.toFixedRange.bind(l.xAxis.navigatorAxis);if(t.options.scrollbar?.enabled){let i=tq(t.options.scrollbar,{vertical:t.inverted});tZ(i.margin)||(i.margin=t.inverted?-3:3),t.scrollbar=l.scrollbar=new tB(t.renderer,i,t),tU(l.scrollbar,"changed",function(t){let i=l.size,e=i*this.to,s=i*this.from;l.hasDragged=l.scrollbar.hasDragged,l.render(0,0,s,e),this.shouldUpdateExtremes(t.DOMType)&&setTimeout(function(){l.onMouseUp(t)})})}l.addBaseSeriesEvents(),l.addChartEvents()}setOpposite(){let t=this.navigatorOptions,i=this.navigatorEnabled,e=this.chart;this.opposite=tJ(t.opposite,!!(!i&&e.inverted))}getUnionExtremes(t){let i,e=this.chart.xAxis[0],s=this.chart.time,a=this.xAxis,r=a.options,o=e.options;return t&&null===e.dataMin||(i={dataMin:tJ(s.parse(r?.min),t$("min",s.parse(o.min),e.dataMin,a.dataMin,a.min)),dataMax:tJ(s.parse(r?.max),t$("max",s.parse(o.max),e.dataMax,a.dataMax,a.max))}),i}setBaseSeries(t,i){let e=this.chart,s=this.baseSeries=[];t=t||e.options&&e.options.navigator.baseSeries||(e.series.length?tF(e.series,t=>!t.options.isInternal).index:0),(e.series||[]).forEach((i,e)=>{!i.options.isInternal&&(i.options.showInNavigator||(e===t||i.options.id===t)&&!1!==i.options.showInNavigator)&&s.push(i)}),this.xAxis&&!this.xAxis.navigatorAxis.fake&&this.updateNavigatorSeries(!0,i)}updateNavigatorSeries(t,i){let e=this,s=e.chart,a=e.baseSeries,r={enableMouseTracking:!1,index:null,linkedTo:null,group:"nav",padXAxis:!1,xAxis:this.navigatorOptions.xAxis?.id,yAxis:this.navigatorOptions.yAxis?.id,showInLegend:!1,stacking:void 0,isInternal:!0,states:{inactive:{opacity:1}}},o=e.series=(e.series||[]).filter(t=>{let i=t.baseSeries;return!(0>a.indexOf(i))||(i&&(tK(i,"updatedData",e.updatedDataHandler),delete i.navigatorSeries),t.chart&&t.destroy(),!1)}),n,h,l=e.navigatorOptions.series,d;a&&a.length&&a.forEach(t=>{let c=t.navigatorSeries,g=tG({color:t.color,visible:t.visible},tj(l)?tL.navigator.series:l);if(c&&!1===e.navigatorOptions.adaptToUpdatedData)return;r.name="Navigator "+a.length,d=(n=t.options||{}).navigatorOptions||{},g.dataLabels=tQ(g.dataLabels),(h=tq(n,r,g,d)).pointRange=tJ(g.pointRange,d.pointRange,tL.plotOptions[h.type||"line"].pointRange);let p=d.data||g.data;e.hasNavigatorData=e.hasNavigatorData||!!p,h.data=p||n.data?.slice(0),c&&c.options?c.update(h,i):(t.navigatorSeries=s.initSeries(h),s.setSortedData(),t.navigatorSeries.baseSeries=t,o.push(t.navigatorSeries))}),(l.data&&!(a&&a.length)||tj(l))&&(e.hasNavigatorData=!1,(l=tQ(l)).forEach((t,i)=>{r.name="Navigator "+(o.length+1),(h=tq(tL.navigator.series,{color:s.series[i]&&!s.series[i].options.isInternal&&s.series[i].color||s.options.colors[i]||s.options.colors[0]},r,t)).data=t.data,h.data&&(e.hasNavigatorData=!0,o.push(s.initSeries(h)))})),t&&this.addBaseSeriesEvents()}addBaseSeriesEvents(){let t=this,i=t.baseSeries||[];i[0]&&i[0].xAxis&&i[0].eventsToUnbind.push(tU(i[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes)),i.forEach(e=>{e.eventsToUnbind.push(tU(e,"show",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!0,!1)})),e.eventsToUnbind.push(tU(e,"hide",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!1,!1)})),!1!==this.navigatorOptions.adaptToUpdatedData&&e.xAxis&&e.eventsToUnbind.push(tU(e,"updatedData",this.updatedDataHandler)),e.eventsToUnbind.push(tU(e,"remove",function(){i&&tY(i,e),this.navigatorSeries&&t.series&&(tY(t.series,this.navigatorSeries),tX(this.navigatorSeries.options)&&this.navigatorSeries.remove(!1),delete this.navigatorSeries)}))})}getBaseSeriesMin(t){return this.baseSeries.reduce(function(t,i){return Math.min(t,i.getColumn("x")[0]??t)},t)}modifyNavigatorAxisExtremes(){let t=this.xAxis;if(void 0!==t.getExtremes){let i=this.getUnionExtremes(!0);i&&(i.dataMin!==t.min||i.dataMax!==t.max)&&(t.min=i.dataMin,t.max=i.dataMax)}}modifyBaseAxisExtremes(){let t,i,e=this.chart.navigator,s=this.getExtremes(),a=s.min,r=s.max,o=s.dataMin,n=s.dataMax,h=r-a,l=e.stickToMin,d=e.stickToMax,c=tJ(this.ordinal?.convertOverscroll(this.options.overscroll),0),g=e.series&&e.series[0],p=!!this.setExtremes;!(this.eventArgs&&"rangeSelectorButton"===this.eventArgs.trigger)&&(l&&(t=(i=o)+h),d&&(t=n+c,l||(i=Math.max(o,t-h,e.getBaseSeriesMin(g&&g.xData?g.xData[0]:-Number.MAX_VALUE)))),p&&(l||d)&&tZ(i)&&(this.min=this.userMin=i,this.max=this.userMax=t)),e.stickToMin=e.stickToMax=null}updatedDataHandler(){let t=this.chart.navigator,i=this.navigatorSeries,e=t.reversedExtremes?0===Math.round(t.zoomedMin):Math.round(t.zoomedMax)>=Math.round(t.size);t.stickToMax=tJ(this.chart.options.navigator&&this.chart.options.navigator.stickToMax,e),t.stickToMin=t.shouldStickToMin(this,t),i&&!t.hasNavigatorData&&(i.options.pointStart=this.getColumn("x")[0],i.setData(this.options.data,!1,null,!1))}shouldStickToMin(t,i){let e=i.getBaseSeriesMin(t.getColumn("x")[0]),s=t.xAxis,a=s.max,r=s.min,o=s.options.range,n=!0;return!!(tZ(a)&&tZ(r))&&(o&&a-e>0?a-e<o:r<=e)}addChartEvents(){this.eventsToUnbind||(this.eventsToUnbind=[]),this.eventsToUnbind.push(tU(this.chart,"redraw",function(){let t=this.navigator,i=t&&(t.baseSeries&&t.baseSeries[0]&&t.baseSeries[0].xAxis||this.xAxis[0]);i&&t.render(i.min,i.max)}),tU(this.chart,"getMargins",function(){let t=this.navigator,i=t.opposite?"plotTop":"marginBottom";this.inverted&&(i=t.opposite?"marginRight":"plotLeft"),this[i]=(this[i]||0)+(t.navigatorEnabled||!this.inverted?t.height+(this.scrollbar?.options.margin||0)+t.scrollbarHeight:0)+(t.navigatorOptions.margin||0)}),tU(t0,"setRange",function(t){this.chart.xAxis[0].setExtremes(t.min,t.max,t.redraw,t.animation,t.eventArguments)}))}destroy(){this.removeEvents(),this.xAxis&&(tY(this.chart.xAxis,this.xAxis),tY(this.chart.axes,this.xAxis)),this.yAxis&&(tY(this.chart.yAxis,this.yAxis),tY(this.chart.axes,this.yAxis)),(this.series||[]).forEach(t=>{t.destroy&&t.destroy()}),["series","xAxis","yAxis","shades","outline","scrollbarTrack","scrollbarRifles","scrollbarGroup","scrollbar","navigatorGroup","rendered"].forEach(t=>{this[t]&&this[t].destroy&&this[t].destroy(),this[t]=null}),[this.handles].forEach(t=>{t_(t)}),this.baseSeries.forEach(t=>{t.navigatorSeries=void 0}),this.navigatorEnabled=!1}}let t1={chart:{height:70,margin:[0,5,0,5]},exporting:{enabled:!1},legend:{enabled:!1},navigator:{enabled:!1},plotOptions:{series:{states:{hover:{enabled:!1}},marker:{enabled:!1}}},scrollbar:{enabled:!1},title:{text:""},tooltip:{enabled:!1},xAxis:{visible:!1},yAxis:{height:0,visible:!1}},{merge:t2,addEvent:t5,fireEvent:t3,pick:t6}=x();class t9{static navigator(t,i){let e=new t9(t,i);return x().navigators?x().navigators.push(e):x().navigators=[e],e}constructor(t,i){this.boundAxes=[],this.userOptions=i,this.chartOptions=t2(x().getOptions(),t1,i.chart,{navigator:i}),this.chartOptions.chart&&i.height&&(this.chartOptions.chart.height=i.height);let e=new(m())(t,this.chartOptions);e.options=t2(e.options,{navigator:{enabled:!0},scrollbar:{enabled:!0}}),this.chartOptions.navigator&&this.chartOptions.scrollbar&&(this.chartOptions.navigator.enabled=!0,this.chartOptions.scrollbar.enabled=!0),this.navigator=new t0(e),e.navigator=this.navigator,this.initNavigator()}bind(t,i=!0){let e=this,s=t instanceof m()?t.xAxis[0]:t;if(!(s instanceof b()))return;let{min:a,max:r}=this.navigator.xAxis,o=[];if(i){let t=t5(s,"setExtremes",t=>{("pan"===t.trigger||"zoom"===t.trigger||"mousewheel"===t.trigger)&&e.setRange(t.min,t.max,!0,"pan"!==t.trigger&&"mousewheel"!==t.trigger,{trigger:s})});o.push(t)}let n=t5(this.navigator,"setRange",t=>{s.setExtremes(t.min,t.max,t.redraw,t.animation)});o.push(n);let h=this.boundAxes.filter(function(t){return t.axis===s})[0];h||(h={axis:s,callbacks:[]},this.boundAxes.push(h)),h.callbacks=o,s.series.forEach(t=>{t.options.showInNavigator&&e.addSeries(t.options)}),s.setExtremes(a,r),t5(s,"destroy",t=>{t.keepEvents||this.unbind(s)})}unbind(t){if(!t){this.boundAxes.forEach(({callbacks:t})=>{t.forEach(t=>t())}),this.boundAxes.length=0;return}let i=t instanceof b()?t:t.xAxis[0];for(let t=this.boundAxes.length-1;t>=0;t--)this.boundAxes[t].axis===i&&(this.boundAxes[t].callbacks.forEach(t=>t()),this.boundAxes.splice(t,1))}destroy(){this.boundAxes.forEach(({callbacks:t})=>{t.forEach(t=>t())}),this.boundAxes.length=0,this.navigator.destroy(),this.navigator.chart.destroy()}update(t,i){this.chartOptions=t2(this.chartOptions,t.height&&{chart:{height:t.height}},t.chart,{navigator:t}),this.navigator.chart.update(this.chartOptions,i)}redraw(){this.navigator.chart.redraw()}addSeries(t){this.navigator.chart.addSeries(t2(t,{showInNavigator:t6(t.showInNavigator,!0)})),this.navigator.setBaseSeries()}initNavigator(){let t=this.navigator;t.top=1,t.xAxis.setScale(),t.yAxis.setScale(),t.xAxis.render(),t.yAxis.render(),t.series?.forEach(t=>{t.translate(),t.render(),t.redraw()});let{min:i,max:e}=this.getInitialExtremes();t.chart.xAxis[0].userMin=i,t.chart.xAxis[0].userMax=e,t.render(i,e)}getRange(){let{min:t,max:i}=this.navigator.chart.xAxis[0].getExtremes(),{userMin:e,userMax:s,min:a,max:r}=this.navigator.xAxis.getExtremes();return{min:t6(t,a),max:t6(i,r),dataMin:a,dataMax:r,userMin:e,userMax:s}}setRange(t,i,e,s,a){t3(this.navigator,"setRange",{min:t,max:i,redraw:e,animation:s,eventArguments:t2(a,{trigger:"navigator"})})}getInitialExtremes(){let{min:t,max:i}=this.navigator.xAxis.getExtremes();return{min:t,max:i}}}let t4=x();t4.StandaloneNavigator=t4.StandaloneNavigator||t9,t4.navigator=t4.StandaloneNavigator.navigator,tc.compose(t4.Chart,t4.Axis,t4.Series);let t7=x();return g.default})());