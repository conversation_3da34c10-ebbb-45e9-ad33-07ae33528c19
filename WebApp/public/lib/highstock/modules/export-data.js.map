{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/export-data\n * @requires highcharts\n * @requires highcharts/modules/exporting\n *\n * Export data module\n *\n * (c) 2010-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"AST\"], root[\"_Highcharts\"][\"Chart\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/export-data\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"AST\"],amd1[\"Chart\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/export-data\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"AST\"], root[\"_Highcharts\"][\"Chart\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"AST\"], root[\"Highcharts\"][\"Chart\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__660__, __WEBPACK_EXTERNAL_MODULE__960__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 660:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__660__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ }),\n\n/***/ 960:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__960__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ export_data_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Extensions/DownloadURL.js\n/* *\n *\n *  (c) 2015-2025 Oystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Mixin for downloading content in the browser\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\nconst { isSafari, win, win: { document: doc } } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { error } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\nconst domurl = win.URL || win.webkitURL || win;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Convert base64 dataURL to Blob if supported, otherwise returns undefined.\n *\n * @private\n * @function Highcharts.dataURLtoBlob\n *\n * @param {string} dataURL\n * URL to convert.\n *\n * @return {string | undefined}\n * Blob.\n */\nfunction dataURLtoBlob(dataURL) {\n    const parts = dataURL\n        .replace(/filename=.*;/, '')\n        .match(/data:([^;]*)(;base64)?,([A-Z+\\d\\/]+)/i);\n    if (parts &&\n        parts.length > 3 &&\n        (win.atob) &&\n        win.ArrayBuffer &&\n        win.Uint8Array &&\n        win.Blob &&\n        (domurl.createObjectURL)) {\n        // Try to convert data URL to Blob\n        const binStr = win.atob(parts[3]), buf = new win.ArrayBuffer(binStr.length), binary = new win.Uint8Array(buf);\n        for (let i = 0; i < binary.length; ++i) {\n            binary[i] = binStr.charCodeAt(i);\n        }\n        return domurl\n            .createObjectURL(new win.Blob([binary], { 'type': parts[1] }));\n    }\n}\n/**\n * Download a data URL in the browser. Can also take a blob as first param.\n *\n * @private\n * @function Highcharts.downloadURL\n *\n * @param {string | global.URL} dataURL\n * The dataURL/Blob to download.\n * @param {string} filename\n * The name of the resulting file (w/extension).\n */\nfunction downloadURL(dataURL, filename) {\n    const nav = win.navigator, a = doc.createElement('a');\n    // IE specific blob implementation\n    // Don't use for normal dataURLs\n    if (typeof dataURL !== 'string' &&\n        !(dataURL instanceof String) &&\n        nav.msSaveOrOpenBlob) {\n        nav.msSaveOrOpenBlob(dataURL, filename);\n        return;\n    }\n    dataURL = '' + dataURL;\n    if (nav.userAgent.length > 1000 /* RegexLimits.shortLimit */) {\n        throw new Error('Input too long');\n    }\n    const // Some browsers have limitations for data URL lengths. Try to convert\n    // to Blob or fall back. Edge always needs that blob.\n    isOldEdgeBrowser = /Edge\\/\\d+/.test(nav.userAgent), \n    // Safari on iOS needs Blob in order to download PDF\n    safariBlob = (isSafari &&\n        typeof dataURL === 'string' &&\n        dataURL.indexOf('data:application/pdf') === 0);\n    if (safariBlob || isOldEdgeBrowser || dataURL.length > 2000000) {\n        dataURL = dataURLtoBlob(dataURL) || '';\n        if (!dataURL) {\n            throw new Error('Failed to convert to blob');\n        }\n    }\n    // Try HTML5 download attr if supported\n    if (typeof a.download !== 'undefined') {\n        a.href = dataURL;\n        a.download = filename; // HTML5 download attribute\n        doc.body.appendChild(a);\n        a.click();\n        doc.body.removeChild(a);\n    }\n    else {\n        // No download attr, just opening data URI\n        try {\n            if (!win.open(dataURL, 'chart')) {\n                throw new Error('Failed to open window');\n            }\n        }\n        catch {\n            // If window.open failed, try location.href\n            win.location.href = dataURL;\n        }\n    }\n}\n/**\n * Asynchronously downloads a script from a provided location.\n *\n * @private\n * @function Highcharts.getScript\n *\n * @param {string} scriptLocation\n * The location for the script to fetch.\n */\nfunction getScript(scriptLocation) {\n    return new Promise((resolve, reject) => {\n        const head = doc.getElementsByTagName('head')[0], script = doc.createElement('script');\n        // Set type and location for the script\n        script.type = 'text/javascript';\n        script.src = scriptLocation;\n        // Resolve in case of a succesful script fetching\n        script.onload = () => {\n            resolve();\n        };\n        // Reject in case of fail\n        script.onerror = () => {\n            reject(error(`Error loading script ${scriptLocation}`));\n        };\n        // Append the newly created script\n        head.appendChild(script);\n    });\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst DownloadURL = {\n    dataURLtoBlob,\n    downloadURL,\n    getScript\n};\n/* harmony default export */ const Extensions_DownloadURL = (DownloadURL);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"AST\"],\"commonjs\":[\"highcharts\",\"AST\"],\"commonjs2\":[\"highcharts\",\"AST\"],\"root\":[\"Highcharts\",\"AST\"]}\nvar highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_ = __webpack_require__(660);\nvar highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default = /*#__PURE__*/__webpack_require__.n(highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Chart\"],\"commonjs\":[\"highcharts\",\"Chart\"],\"commonjs2\":[\"highcharts\",\"Chart\"],\"root\":[\"Highcharts\",\"Chart\"]}\nvar highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_ = __webpack_require__(960);\nvar highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default = /*#__PURE__*/__webpack_require__.n(highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_);\n;// ./code/es-modules/Extensions/ExportData/ExportDataDefaults.js\n/* *\n *\n *  Experimental data export module for Highcharts\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n/**\n * @optionparent exporting\n * @private\n */\nconst exporting = {\n    /**\n     * Caption for the data table. Same as chart title by default. Set to\n     * `false` to disable.\n     *\n     * @sample highcharts/export-data/multilevel-table\n     *         Multiple table headers\n     *\n     * @type      {boolean | string}\n     * @since     6.0.4\n     * @requires  modules/export-data\n     * @apioption exporting.tableCaption\n     */\n    /**\n     * Options for exporting data to CSV or ExCel, or displaying the data\n     * in a HTML table or a JavaScript structure.\n     *\n     * This module adds data export options to the export menu and provides\n     * functions like `Exporting.getCSV`, `Exporting.getTable`,\n     * `Exporting.getDataRows` and `Exporting.viewData`.\n     *\n     * The XLS converter is limited and only creates a HTML string that is\n     * passed for download, which works but creates a warning before\n     * opening. The workaround for this is to use a third party XLSX\n     * converter, as demonstrated in the sample below.\n     *\n     * @sample  highcharts/export-data/categorized/ Categorized data\n     * @sample  highcharts/export-data/stock-timeaxis/ Highcharts Stock time axis\n     * @sample  highcharts/export-data/xlsx/\n     *          Using a third party XLSX converter\n     *\n     * @since    6.0.0\n     * @requires modules/export-data\n     */\n    csv: {\n        /**\n         *\n         * Options for annotations in the export-data table.\n         *\n         * @since    8.2.0\n         * @requires modules/export-data\n         * @requires modules/annotations\n         *\n         *\n         */\n        annotations: {\n            /**\n            * The way to mark the separator for annotations\n            * combined in one export-data table cell.\n            *\n            * @since    8.2.0\n            * @requires modules/annotations\n            */\n            itemDelimiter: '; ',\n            /**\n            * When several labels are assigned to a specific point,\n            * they will be displayed in one field in the table.\n            *\n            * @sample highcharts/export-data/join-annotations/\n            *         Concatenate point annotations with itemDelimiter set.\n            *\n            * @since    8.2.0\n            * @requires modules/annotations\n            */\n            join: false\n        },\n        /**\n         * Formatter callback for the column headers. Parameters are:\n         * - `item` - The series or axis object)\n         * - `key` -  The point key, for example y or z\n         * - `keyLength` - The amount of value keys for this item, for\n         *   example a range series has the keys `low` and `high` so the\n         *   key length is 2.\n         *\n         * If [useMultiLevelHeaders](#exporting.useMultiLevelHeaders) is\n         * true, columnHeaderFormatter by default returns an object with\n         * columnTitle and topLevelColumnTitle for each key. Columns with\n         * the same topLevelColumnTitle have their titles merged into a\n         * single cell with colspan for table/Excel export.\n         *\n         * If `useMultiLevelHeaders` is false, or for CSV export, it returns\n         * the series name, followed by the key if there is more than one\n         * key.\n         *\n         * For the axis it returns the axis title or \"Category\" or\n         * \"DateTime\" by default.\n         *\n         * Return `false` to use Highcharts' proposed header.\n         *\n         * @sample highcharts/export-data/multilevel-table\n         *         Multiple table headers\n         *\n         * @type {Function | null}\n         */\n        columnHeaderFormatter: null,\n        /**\n         * Which date format to use for exported dates on a datetime X axis.\n         * See `Highcharts.dateFormat`.\n         */\n        dateFormat: '%Y-%m-%d %H:%M:%S',\n        /**\n         * Which decimal point to use for exported CSV. Defaults to the same\n         * as the browser locale, typically `.` (English) or `,` (German,\n         * French etc).\n         *\n         * @type  {string | null}\n         * @since 6.0.4\n         */\n        decimalPoint: null,\n        /**\n         * The item delimiter in the exported data. Use `;` for direct\n         * exporting to Excel. Defaults to a best guess based on the browser\n         * locale. If the locale _decimal point_ is `,`, the `itemDelimiter`\n         * defaults to `;`, otherwise the `itemDelimiter` defaults to `,`.\n         *\n         * @type {string | null}\n         */\n        itemDelimiter: null,\n        /**\n         * The line delimiter in the exported data, defaults to a newline.\n         */\n        lineDelimiter: '\\n'\n    },\n    /**\n     * An object consisting of definitions for the menu items in the context\n     * menu. Each key value pair has a `key` that is referenced in the\n     * [menuItems](#exporting.buttons.contextButton.menuItems) setting,\n     * and a `value`, which is an object with the following properties:\n     *\n     * - **onclick:** The click handler for the menu item\n     *\n     * - **text:** The text for the menu item\n     *\n     * - **textKey:** If internationalization is required, the key to a language\n     *   string\n     *\n     * Custom text for the \"exitFullScreen\" can be set only in lang options\n     * (it is not a separate button).\n     *\n     * @sample highcharts/exporting/menuitemdefinitions/\n     *         Menu item definitions\n     * @sample highcharts/exporting/menuitemdefinitions-webp/\n     *         Adding a custom menu item for WebP export\n     *\n     * @type     {Highcharts.Dictionary<Highcharts.ExportingMenuObject>}\n     * @default  {\"downloadCSV\": {}, \"downloadXLS\": {}, \"viewData\": {}}\n     * @requires modules/export-data\n     */\n    menuItemDefinitions: {\n        /**\n         * @ignore\n         */\n        downloadCSV: {\n            textKey: 'downloadCSV',\n            onclick: function () {\n                this.exporting?.downloadCSV();\n            }\n        },\n        /**\n         * @ignore\n         */\n        downloadXLS: {\n            textKey: 'downloadXLS',\n            onclick: function () {\n                this.exporting?.downloadXLS();\n            }\n        },\n        /**\n         * @ignore\n         */\n        viewData: {\n            textKey: 'viewData',\n            onclick: function () {\n                this.exporting?.wrapLoading(this.exporting.toggleDataTable);\n            }\n        }\n    },\n    /**\n     * Show a HTML table below the chart with the chart's current data.\n     *\n     * @sample highcharts/export-data/showtable/\n     *         Show the table\n     * @sample highcharts/studies/exporting-table-html\n     *         Experiment with putting the table inside the subtitle to\n     *         allow exporting it.\n     *\n     * @since    6.0.0\n     * @requires modules/export-data\n     */\n    showTable: false,\n    /**\n     * Use multi level headers in data table. If [csv.columnHeaderFormatter\n     * ](#exporting.csv.columnHeaderFormatter) is defined, it has to return\n     * objects in order for multi level headers to work.\n     *\n     * @sample highcharts/export-data/multilevel-table\n     *         Multiple table headers\n     *\n     * @since    6.0.4\n     * @requires modules/export-data\n     */\n    useMultiLevelHeaders: true,\n    /**\n     * If using multi level table headers, use rowspans for headers that\n     * have only one level.\n     *\n     * @sample highcharts/export-data/multilevel-table\n     *         Multiple table headers\n     *\n     * @since    6.0.4\n     * @requires modules/export-data\n     */\n    useRowspanHeaders: true,\n    /**\n     * Display a message when export is in progress.\n     * Uses [Chart.setLoading()](/class-reference/Highcharts.Chart#setLoading)\n     *\n     * The message can be altered by changing [](#lang.exporting.exportInProgress)\n     *\n     * @since    11.3.0\n     * @requires modules/export-data\n     */\n    showExportInProgress: true\n};\n/**\n * @optionparent lang\n * @private\n */\nconst lang = {\n    /**\n     * The text for the menu item.\n     *\n     * @since    6.0.0\n     * @requires modules/export-data\n     */\n    downloadCSV: 'Download CSV',\n    /**\n     * The text for the menu item.\n     *\n     * @since    6.0.0\n     * @requires modules/export-data\n     */\n    downloadXLS: 'Download XLS',\n    /**\n     * The text for exported table.\n     *\n     * @since    8.1.0\n     * @requires modules/export-data\n     */\n    exportData: {\n        /**\n         * The annotation column title.\n         */\n        annotationHeader: 'Annotations',\n        /**\n         * The category column title.\n         */\n        categoryHeader: 'Category',\n        /**\n         * The category column title when axis type set to \"datetime\".\n         */\n        categoryDatetimeHeader: 'DateTime'\n    },\n    /**\n     * The text for the menu item.\n     *\n     * @since    6.0.0\n     * @requires modules/export-data\n     */\n    viewData: 'View data table',\n    /**\n     * The text for the menu item.\n     *\n     * @since    8.2.0\n     * @requires modules/export-data\n     */\n    hideData: 'Hide data table',\n    /**\n     * Text to show when export is in progress.\n     *\n     * @since    11.3.0\n     * @requires modules/export-data\n     */\n    exportInProgress: 'Exporting...'\n};\n/* *\n *\n *  Default Export\n *\n * */\nconst ExportDataDefaults = {\n    exporting,\n    lang\n};\n/* harmony default export */ const ExportData_ExportDataDefaults = (ExportDataDefaults);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Callback that fires while exporting data. This allows the modification of\n * data rows before processed into the final format.\n *\n * @type      {Highcharts.ExportDataCallbackFunction}\n * @context   Highcharts.Chart\n * @requires  modules/export-data\n * @apioption chart.events.exportData\n */\n/**\n * When set to `false` will prevent the series data from being included in\n * any form of data export.\n *\n * Since version 6.0.0 until 7.1.0 the option was existing undocumented\n * as `includeInCSVExport`.\n *\n * @type      {boolean}\n * @since     7.1.0\n * @requires  modules/export-data\n * @apioption plotOptions.series.includeInDataExport\n */\n(''); // Keep doclets above in JS file\n\n;// ./code/es-modules/Extensions/ExportData/ExportData.js\n/* *\n *\n *  Experimental data export module for Highcharts\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n// @todo\n// - Set up systematic tests for all series types, paired with tests of the data\n//   module importing the same data.\n\n\n\n\nconst { getOptions, setOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { downloadURL: ExportData_downloadURL } = Extensions_DownloadURL;\n\n\nconst { composed, doc: ExportData_doc, win: ExportData_win } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent, defined, extend, find, fireEvent, isNumber, pick, pushUnique } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\nvar ExportData;\n(function (ExportData) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Composition function.\n     *\n     * @private\n     * @function Highcharts.Exporting#compose\n     *\n     * @param {ChartClass} ChartClass\n     * Chart class.\n     * @param {ExportingClass} ExportingClass\n     * Exporting class.\n     * @param {SeriesClass} SeriesClass\n     * Series class.\n     *\n     * @requires modules/exporting\n     * @requires modules/export-data\n     */\n    function compose(ChartClass, ExportingClass, SeriesClass) {\n        // Check the composition registry for the Exporting\n        if (!pushUnique(composed, 'ExportData')) {\n            return;\n        }\n        // Adding wrappers for the deprecated functions\n        extend((highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default()).prototype, {\n            downloadCSV: function () {\n                return this.exporting?.downloadCSV();\n            },\n            downloadXLS: function () {\n                return this.exporting?.downloadXLS();\n            },\n            getCSV: function (useLocalDecimalPoint) {\n                return this.exporting?.getCSV(useLocalDecimalPoint);\n            },\n            getDataRows: function (multiLevelHeaders) {\n                return this.exporting?.getDataRows(multiLevelHeaders);\n            },\n            getTable: function (useLocalDecimalPoint) {\n                return this.exporting?.getTable(useLocalDecimalPoint);\n            },\n            getTableAST: function (useLocalDecimalPoint) {\n                return this.exporting?.getTableAST(useLocalDecimalPoint);\n            },\n            hideData: function () {\n                return this.exporting?.hideData();\n            },\n            toggleDataTable: function (show) {\n                return this.exporting?.toggleDataTable(show);\n            },\n            viewData: function () {\n                return this.exporting?.viewData();\n            }\n        });\n        const exportingProto = ExportingClass.prototype;\n        if (!exportingProto.downloadCSV) {\n            // Add an event listener to handle the showTable option\n            addEvent(ChartClass, 'afterViewData', onChartAfterViewData);\n            addEvent(ChartClass, 'render', onChartRenderer);\n            addEvent(ChartClass, 'destroy', onChartDestroy);\n            // Adding functions to the Exporting prototype\n            exportingProto.downloadCSV = downloadCSV;\n            exportingProto.downloadXLS = downloadXLS;\n            exportingProto.getCSV = getCSV;\n            exportingProto.getDataRows = getDataRows;\n            exportingProto.getTable = getTable;\n            exportingProto.getTableAST = getTableAST;\n            exportingProto.hideData = hideData;\n            exportingProto.toggleDataTable = toggleDataTable;\n            exportingProto.wrapLoading = wrapLoading;\n            exportingProto.viewData = viewData;\n            // Update with defaults of the export data module\n            setOptions(ExportData_ExportDataDefaults);\n            // Additionaly, extend the menuItems with the export data variants\n            const menuItems = getOptions().exporting?.buttons?.contextButton?.menuItems;\n            menuItems && menuItems.push('separator', 'downloadCSV', 'downloadXLS', 'viewData');\n            const { arearange: AreaRangeSeries, gantt: GanttSeries, map: MapSeries, mapbubble: MapBubbleSeries, treemap: TreemapSeries, xrange: XRangeSeries } = SeriesClass.types;\n            if (AreaRangeSeries) {\n                AreaRangeSeries.prototype.keyToAxis = {\n                    low: 'y',\n                    high: 'y'\n                };\n            }\n            if (GanttSeries) {\n                GanttSeries.prototype.exportKey = 'name';\n                GanttSeries.prototype.keyToAxis = {\n                    start: 'x',\n                    end: 'x'\n                };\n            }\n            if (MapSeries) {\n                MapSeries.prototype.exportKey = 'name';\n            }\n            if (MapBubbleSeries) {\n                MapBubbleSeries.prototype.exportKey = 'name';\n            }\n            if (TreemapSeries) {\n                TreemapSeries.prototype.exportKey = 'name';\n            }\n            if (XRangeSeries) {\n                XRangeSeries.prototype.keyToAxis = {\n                    x2: 'x'\n                };\n            }\n        }\n    }\n    ExportData.compose = compose;\n    /**\n     * Generates a data URL of CSV for local download in the browser. This is\n     * the default action for a click on the 'Download CSV' button.\n     *\n     * See {@link Highcharts.Exporting#getCSV} to get the CSV data itself.\n     *\n     * @function Highcharts.Exporting#downloadCSV\n     *\n     * @requires modules/exporting\n     * @requires modules/export-data\n     */\n    function downloadCSV() {\n        this.wrapLoading(() => {\n            const csv = this.getCSV(true);\n            ExportData_downloadURL(getBlobFromContent(csv, 'text/csv') ||\n                'data:text/csv,\\uFEFF' + encodeURIComponent(csv), this.getFilename() + '.csv');\n        });\n    }\n    /**\n     * Generates a data URL of an XLS document for local download in the\n     * browser. This is the default action for a click on the 'Download XLS'\n     * button.\n     *\n     * See {@link Highcharts.Exporting#getTable} to get the table data itself.\n     *\n     * @function Highcharts.Exporting#downloadXLS\n     *\n     * @requires modules/exporting\n     * @requires modules/export-data\n     */\n    function downloadXLS() {\n        this.wrapLoading(() => {\n            const uri = 'data:application/vnd.ms-excel;base64,', template = '<html xmlns:o=\"urn:schemas-microsoft-com:office:office\" ' +\n                'xmlns:x=\"urn:schemas-microsoft-com:office:excel\" ' +\n                'xmlns=\"http://www.w3.org/TR/REC-html40\">' +\n                '<head><!--[if gte mso 9]><xml><x:ExcelWorkbook>' +\n                '<x:ExcelWorksheets><x:ExcelWorksheet>' +\n                '<x:Name>Ark1</x:Name>' +\n                '<x:WorksheetOptions><x:DisplayGridlines/>' +\n                '</x:WorksheetOptions>' +\n                '</x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook>' +\n                '</xml><![endif]-->' +\n                '<style>td{border:none;font-family: Calibri, sans-serif;} ' +\n                '.number{mso-number-format:\"0.00\";} ' +\n                '.text{ mso-number-format:\"\\@\";}</style>' +\n                '<meta name=ProgId content=Excel.Sheet>' +\n                '<meta charset=UTF-8>' +\n                '</head><body>' +\n                this.getTable(true) +\n                '</body></html>', base64 = function (s) {\n                return ExportData_win.btoa(unescape(encodeURIComponent(s))); // #50\n            };\n            ExportData_downloadURL(getBlobFromContent(template, 'application/vnd.ms-excel') ||\n                uri + base64(template), this.getFilename() + '.xls');\n        });\n    }\n    /**\n     * Get a blob object from content, if blob is supported.\n     *\n     * @private\n     * @function Highcharts.getBlobFromContent\n     *\n     * @param {string} content\n     * The content to create the blob from.\n     * @param {string} type\n     * The type of the content.\n     *\n     * @return {string | undefined}\n     * The blob object, or undefined if not supported.\n     *\n     * @requires modules/exporting\n     * @requires modules/export-data\n     */\n    function getBlobFromContent(content, type) {\n        const nav = ExportData_win.navigator, domurl = ExportData_win.URL || ExportData_win.webkitURL || ExportData_win;\n        try {\n            // MS specific\n            if ((nav.msSaveOrOpenBlob) && ExportData_win.MSBlobBuilder) {\n                const blob = new ExportData_win.MSBlobBuilder();\n                blob.append(content);\n                return blob.getBlob('image/svg+xml');\n            }\n            return domurl.createObjectURL(new ExportData_win.Blob(['\\uFEFF' + content], // #7084\n            { type: type }));\n        }\n        catch (e) {\n            // Ignore\n        }\n    }\n    /**\n     * Returns the current chart data as a CSV string.\n     *\n     * @function Highcharts.Exporting#getCSV\n     *\n     * @param {boolean} [useLocalDecimalPoint]\n     * Whether to use the local decimal point as detected from the browser. This\n     * makes it easier to export data to Excel in the same locale as the user\n     * is.\n     *\n     * @return {string}\n     * CSV representation of the data.\n     *\n     * @requires modules/exporting\n     * @requires modules/export-data\n     */\n    function getCSV(useLocalDecimalPoint) {\n        let csv = '';\n        const rows = this.getDataRows(), csvOptions = this.options?.csv, decimalPoint = pick(csvOptions?.decimalPoint, csvOptions?.itemDelimiter !== ',' && useLocalDecimalPoint ?\n            (1.1).toLocaleString()[1] :\n            '.'), \n        // Use ';' for direct to Excel\n        itemDelimiter = pick(csvOptions?.itemDelimiter, decimalPoint === ',' ? ';' : ','), \n        // '\\n' isn't working with the js csv data extraction\n        lineDelimiter = csvOptions?.lineDelimiter;\n        // Transform the rows to CSV\n        rows.forEach((row, i) => {\n            let val = '', j = row.length;\n            while (j--) {\n                val = row[j];\n                if (typeof val === 'string') {\n                    val = `\"${val}\"`;\n                }\n                if (typeof val === 'number') {\n                    if (decimalPoint !== '.') {\n                        val = val.toString().replace('.', decimalPoint);\n                    }\n                }\n                row[j] = val;\n            }\n            // The first row is the header - it defines the number of columns.\n            // Empty columns between not-empty cells are covered in the\n            // getDataRows method.\n            // Now add empty values only to the end of the row so all rows have\n            // the same number of columns, #17186\n            row.length = rows.length ? rows[0].length : 0;\n            // Add the values\n            csv += row.join(itemDelimiter);\n            // Add the line delimiter\n            if (i < rows.length - 1) {\n                csv += lineDelimiter;\n            }\n        });\n        return csv;\n    }\n    /**\n     * Returns a two-dimensional array containing the current chart data.\n     *\n     * @function Highcharts.Exporting#getDataRows\n     *\n     * @param {boolean} [multiLevelHeaders]\n     * Use multilevel headers for the rows by default. Adds an extra row with\n     * top level headers. If a custom columnHeaderFormatter is defined, this can\n     * override the behavior.\n     *\n     * @return {Array<Array<(number | string)>>}\n     * The current chart data\n     *\n     * @emits Highcharts.Chart#event:exportData\n     *\n     * @requires modules/exporting\n     * @requires modules/export-data\n     */\n    function getDataRows(multiLevelHeaders) {\n        const chart = this.chart, hasParallelCoords = chart.hasParallelCoordinates, time = chart.time, csvOptions = this.options?.csv || {}, xAxes = chart.xAxis, rows = {}, rowArr = [], topLevelColumnTitles = [], columnTitles = [], langOptions = chart.options.lang, exportDataOptions = langOptions.exportData, categoryHeader = exportDataOptions?.categoryHeader, categoryDatetimeHeader = exportDataOptions?.categoryDatetimeHeader, \n        // Options\n        columnHeaderFormatter = function (item, key, keyLength) {\n            if (csvOptions.columnHeaderFormatter) {\n                const s = csvOptions.columnHeaderFormatter(item, key, keyLength);\n                if (s !== false) {\n                    return s;\n                }\n            }\n            if (!item && categoryHeader) {\n                return categoryHeader;\n            }\n            if (!item.bindAxes &&\n                categoryDatetimeHeader &&\n                categoryHeader) {\n                return (item.options.title &&\n                    item.options.title.text) || (item.dateTime ?\n                    categoryDatetimeHeader :\n                    categoryHeader);\n            }\n            if (multiLevelHeaders) {\n                return {\n                    columnTitle: ((keyLength || 0) > 1 ?\n                        key :\n                        item.name) || '',\n                    topLevelColumnTitle: item.name\n                };\n            }\n            return item.name + ((keyLength || 0) > 1 ? ' (' + key + ')' : '');\n        }, \n        // Map the categories for value axes\n        getCategoryAndDateTimeMap = function (series, pointArrayMap, pIdx) {\n            const categoryMap = {}, dateTimeValueAxisMap = {};\n            pointArrayMap.forEach(function (prop) {\n                const axisName = ((series.keyToAxis && series.keyToAxis[prop]) ||\n                    prop) + 'Axis', \n                // Points in parallel coordinates refers to all yAxis\n                // not only `series.yAxis`\n                axis = isNumber(pIdx) ?\n                    series.chart[axisName][pIdx] :\n                    series[axisName];\n                categoryMap[prop] = (axis && axis.categories) || [];\n                dateTimeValueAxisMap[prop] = (axis && axis.dateTime);\n            });\n            return {\n                categoryMap: categoryMap,\n                dateTimeValueAxisMap: dateTimeValueAxisMap\n            };\n        }, \n        // Create point array depends if xAxis is category\n        // or point.name is defined #13293\n        getPointArray = function (series, xAxis) {\n            const pointArrayMap = series.pointArrayMap || ['y'], namedPoints = series.data.some((d) => (typeof d.y !== 'undefined') && d.name);\n            // If there are points with a name, we also want the x value in\n            // the table\n            if (namedPoints &&\n                xAxis &&\n                !xAxis.categories &&\n                series.exportKey !== 'name') {\n                return ['x', ...pointArrayMap];\n            }\n            return pointArrayMap;\n        }, xAxisIndices = [];\n        let xAxis, dataRows, columnTitleObj, i = 0, // Loop the series and index values\n        x, xTitle;\n        chart.series.forEach(function (series) {\n            const keys = series.options.keys, xAxis = series.xAxis, pointArrayMap = keys || getPointArray(series, xAxis), valueCount = pointArrayMap.length, xTaken = !series.requireSorting && {}, xAxisIndex = xAxes.indexOf(xAxis);\n            let categoryAndDatetimeMap = getCategoryAndDateTimeMap(series, pointArrayMap), mockSeries, j;\n            if (series.options.includeInDataExport !== false &&\n                !series.options.isInternal &&\n                series.visible !== false // #55\n            ) {\n                // Build a lookup for X axis index and the position of the first\n                // series that belongs to that X axis. Includes -1 for non-axis\n                // series types like pies.\n                if (!find(xAxisIndices, function (index) {\n                    return index[0] === xAxisIndex;\n                })) {\n                    xAxisIndices.push([xAxisIndex, i]);\n                }\n                // Compute the column headers and top level headers, usually the\n                // same as series names\n                j = 0;\n                while (j < valueCount) {\n                    columnTitleObj = columnHeaderFormatter(series, pointArrayMap[j], pointArrayMap.length);\n                    columnTitles.push(columnTitleObj.columnTitle ||\n                        columnTitleObj);\n                    if (multiLevelHeaders) {\n                        topLevelColumnTitles.push(columnTitleObj.topLevelColumnTitle ||\n                            columnTitleObj);\n                    }\n                    j++;\n                }\n                mockSeries = {\n                    chart: series.chart,\n                    autoIncrement: series.autoIncrement,\n                    options: series.options,\n                    pointArrayMap: series.pointArrayMap,\n                    index: series.index\n                };\n                // Export directly from options.data because we need the\n                // uncropped data (#7913), and we need to support Boost (#7026).\n                series.options.data?.forEach(function eachData(options, pIdx) {\n                    const mockPoint = { series: mockSeries };\n                    let key, prop, val;\n                    // In parallel coordinates chart, each data point is\n                    // connected to a separate yAxis, conform this\n                    if (hasParallelCoords) {\n                        categoryAndDatetimeMap = getCategoryAndDateTimeMap(series, pointArrayMap, pIdx);\n                    }\n                    series.pointClass.prototype.applyOptions.apply(mockPoint, [options]);\n                    const name = series.data[pIdx] && series.data[pIdx].name;\n                    key = (mockPoint.x ?? '') + ',' + name;\n                    j = 0;\n                    // Pies, funnels, geo maps etc. use point name in X row\n                    if (!xAxis ||\n                        series.exportKey === 'name' ||\n                        (!hasParallelCoords && xAxis && xAxis.hasNames) && name) {\n                        key = name;\n                    }\n                    if (xTaken) {\n                        if (xTaken[key]) {\n                            key += '|' + pIdx;\n                        }\n                        xTaken[key] = true;\n                    }\n                    if (!rows[key]) {\n                        rows[key] = [];\n                        rows[key].xValues = [];\n                        // ES5 replacement for Array.from / fill.\n                        const arr = [];\n                        for (let i = 0; i < series.chart.series.length; i++) {\n                            arr[i] = 0;\n                        }\n                        // Create pointers array, holding information how many\n                        // duplicates of specific x occurs in each series.\n                        // Used for creating rows with duplicates.\n                        rows[key].pointers = arr;\n                        rows[key].pointers[series.index] = 1;\n                    }\n                    else {\n                        // Handle duplicates (points with the same x), by\n                        // creating extra rows based on pointers for better\n                        // performance.\n                        // eslint-disable-next-line @typescript-eslint/restrict-template-expressions\n                        const modifiedKey = `${key},${rows[key].pointers[series.index]}`, originalKey = key;\n                        if (rows[key].pointers[series.index]) {\n                            if (!rows[modifiedKey]) {\n                                rows[modifiedKey] = [];\n                                rows[modifiedKey].xValues = [];\n                                rows[modifiedKey].pointers = [];\n                            }\n                            key = modifiedKey;\n                        }\n                        rows[originalKey].pointers[series.index] += 1;\n                    }\n                    rows[key].x = mockPoint.x;\n                    rows[key].name = name;\n                    rows[key].xValues[xAxisIndex] = mockPoint.x;\n                    while (j < valueCount) {\n                        prop = pointArrayMap[j]; // `y`, `z` etc\n                        val =\n                            series.pointClass.prototype.getNestedProperty.apply(mockPoint, [prop]);\n                        // Allow values from nested properties (#20470)\n                        rows[key][i + j] = pick(\n                        // Y axis category if present\n                        categoryAndDatetimeMap.categoryMap[prop][val], \n                        // Datetime yAxis\n                        categoryAndDatetimeMap.dateTimeValueAxisMap[prop] ?\n                            time.dateFormat(csvOptions.dateFormat, val) :\n                            null, \n                        // Linear/log yAxis\n                        val);\n                        j++;\n                    }\n                });\n                i = i + j;\n            }\n        });\n        // Make a sortable array\n        for (x in rows) {\n            if (Object.hasOwnProperty.call(rows, x)) {\n                rowArr.push(rows[x]);\n            }\n        }\n        let xAxisIndex, column;\n        // Add computed column headers and top level headers to final row set\n        dataRows = multiLevelHeaders ? [topLevelColumnTitles, columnTitles] :\n            [columnTitles];\n        i = xAxisIndices.length;\n        while (i--) { // Start from end to splice in\n            xAxisIndex = xAxisIndices[i][0];\n            column = xAxisIndices[i][1];\n            xAxis = xAxes[xAxisIndex];\n            // Sort it by X values\n            rowArr.sort(function (// eslint-disable-line no-loop-func\n            a, b) {\n                return a.xValues[xAxisIndex] - b.xValues[xAxisIndex];\n            });\n            // Add header row\n            xTitle = columnHeaderFormatter(xAxis);\n            dataRows[0].splice(column, 0, xTitle);\n            if (multiLevelHeaders && dataRows[1]) {\n                // If using multi level headers, we just added top level header.\n                // Also add for sub level\n                dataRows[1].splice(column, 0, xTitle);\n            }\n            // Add the category column\n            rowArr.forEach(function (// eslint-disable-line no-loop-func\n            row) {\n                let category = row.name;\n                if (xAxis && !defined(category)) {\n                    if (xAxis.dateTime) {\n                        if (row.x instanceof Date) {\n                            row.x = row.x.getTime();\n                        }\n                        category = time.dateFormat(csvOptions.dateFormat, row.x);\n                    }\n                    else if (xAxis.categories) {\n                        category = pick(xAxis.names[row.x], xAxis.categories[row.x], row.x);\n                    }\n                    else {\n                        category = row.x;\n                    }\n                }\n                // Add the X/date/category\n                row.splice(column, 0, category);\n            });\n        }\n        dataRows = dataRows.concat(rowArr);\n        fireEvent(chart, 'exportData', { dataRows: dataRows });\n        return dataRows;\n    }\n    /**\n     * Build a HTML table with the chart's current data.\n     *\n     * @sample highcharts/export-data/viewdata/\n     * View the data from the export menu\n     *\n     * @function Highcharts.Exporting#getTable\n     *\n     * @param {boolean} [useLocalDecimalPoint]\n     * Whether to use the local decimal point as detected from the browser. This\n     * makes it easier to export data to Excel in the same locale as the user\n     * is.\n     *\n     * @return {string}\n     * HTML representation of the data.\n     *\n     * @emits Highcharts.Chart#event:afterGetTable\n     *\n     * @requires modules/exporting\n     * @requires modules/export-data\n     */\n    function getTable(useLocalDecimalPoint) {\n        const serialize = (node) => {\n            if (!node.tagName || node.tagName === '#text') {\n                // Text node\n                return node.textContent || '';\n            }\n            const attributes = node.attributes;\n            let html = `<${node.tagName}`;\n            if (attributes) {\n                Object.keys(attributes)\n                    .forEach((key) => {\n                    const value = attributes[key];\n                    // eslint-disable-next-line @typescript-eslint/restrict-template-expressions\n                    html += ` ${key}=\"${value}\"`;\n                });\n            }\n            html += '>';\n            html += node.textContent || '';\n            (node.children || []).forEach((child) => {\n                html += serialize(child);\n            });\n            html += `</${node.tagName}>`;\n            return html;\n        };\n        const tree = this.getTableAST(useLocalDecimalPoint);\n        return serialize(tree);\n    }\n    /**\n     * Get the AST of a HTML table representing the chart data.\n     *\n     * @private\n     * @function Highcharts.Exporting#getTableAST\n     *\n     * @param {boolean} [useLocalDecimalPoint]\n     * Whether to use the local decimal point as detected from the browser. This\n     * makes it easier to export data to Excel in the same locale as the user\n     * is.\n     *\n     * @return {Highcharts.ASTNode}\n     * The abstract syntax tree\n     *\n     * @requires modules/exporting\n     * @requires modules/export-data\n     */\n    function getTableAST(useLocalDecimalPoint) {\n        let rowLength = 0;\n        const treeChildren = [], exporting = this, chart = exporting.chart, options = chart.options, decimalPoint = useLocalDecimalPoint ? (1.1).toLocaleString()[1] : '.', useMultiLevelHeaders = pick(exporting.options.useMultiLevelHeaders, true), rows = exporting.getDataRows(useMultiLevelHeaders), topHeaders = useMultiLevelHeaders ? rows.shift() : null, subHeaders = rows.shift(), \n        // Compare two rows for equality\n        isRowEqual = function (row1, row2) {\n            let i = row1.length;\n            if (row2.length === i) {\n                while (i--) {\n                    if (row1[i] !== row2[i]) {\n                        return false;\n                    }\n                }\n            }\n            else {\n                return false;\n            }\n            return true;\n        }, \n        // Get table cell HTML from value\n        getCellHTMLFromValue = function (tagName, classes, attributes, value) {\n            let textContent = pick(value, ''), className = 'highcharts-text' + (classes ? ' ' + classes : '');\n            // Convert to string if number\n            if (typeof textContent === 'number') {\n                textContent = textContent.toString();\n                if (decimalPoint === ',') {\n                    textContent = textContent.replace('.', decimalPoint);\n                }\n                className = 'highcharts-number';\n            }\n            else if (!value) {\n                className = 'highcharts-empty';\n            }\n            attributes = extend({ 'class': className }, attributes);\n            return {\n                tagName,\n                attributes,\n                textContent\n            };\n        }, \n        // Get table header markup from row data\n        getTableHeaderHTML = function (topheaders, subheaders, rowLength) {\n            const theadChildren = [];\n            let i = 0, len = rowLength || subheaders && subheaders.length, next, cur, curColspan = 0, rowspan;\n            // Clean up multiple table headers. Chart.getDataRows() returns\n            // two levels of headers when using multilevel, not merged. We\n            // need to merge identical headers, remove redundant headers,\n            // and keep it all marked up nicely.\n            if (useMultiLevelHeaders &&\n                topheaders &&\n                subheaders &&\n                !isRowEqual(topheaders, subheaders)) {\n                const trChildren = [];\n                for (; i < len; ++i) {\n                    cur = topheaders[i];\n                    next = topheaders[i + 1];\n                    if (cur === next) {\n                        ++curColspan;\n                    }\n                    else if (curColspan) {\n                        // Ended colspan\n                        // Add cur to HTML with colspan.\n                        trChildren.push(getCellHTMLFromValue('th', 'highcharts-table-topheading', {\n                            scope: 'col',\n                            colspan: curColspan + 1\n                        }, cur));\n                        curColspan = 0;\n                    }\n                    else {\n                        // Cur is standalone. If it is same as sublevel,\n                        // remove sublevel and add just toplevel.\n                        if (cur === subheaders[i]) {\n                            if (exporting.options.useRowspanHeaders) {\n                                rowspan = 2;\n                                delete subheaders[i];\n                            }\n                            else {\n                                rowspan = 1;\n                                subheaders[i] = '';\n                            }\n                        }\n                        else {\n                            rowspan = 1;\n                        }\n                        const cell = getCellHTMLFromValue('th', 'highcharts-table-topheading', { scope: 'col' }, cur);\n                        if (rowspan > 1 && cell.attributes) {\n                            cell.attributes.valign = 'top';\n                            cell.attributes.rowspan = rowspan;\n                        }\n                        trChildren.push(cell);\n                    }\n                }\n                theadChildren.push({\n                    tagName: 'tr',\n                    children: trChildren\n                });\n            }\n            // Add the subheaders (the only headers if not using\n            // multilevels)\n            if (subheaders) {\n                const trChildren = [];\n                for (i = 0, len = subheaders.length; i < len; ++i) {\n                    if (typeof subheaders[i] !== 'undefined') {\n                        trChildren.push(getCellHTMLFromValue('th', null, { scope: 'col' }, subheaders[i]));\n                    }\n                }\n                theadChildren.push({\n                    tagName: 'tr',\n                    children: trChildren\n                });\n            }\n            return {\n                tagName: 'thead',\n                children: theadChildren\n            };\n        };\n        // Add table caption\n        const { tableCaption } = exporting.options || {};\n        if (tableCaption !== false) {\n            treeChildren.push({\n                tagName: 'caption',\n                attributes: {\n                    'class': 'highcharts-table-caption'\n                },\n                textContent: typeof tableCaption === 'string' ?\n                    tableCaption :\n                    options.title?.text || options.lang.chartTitle\n            });\n        }\n        // Find longest row\n        for (let i = 0, len = rows.length; i < len; ++i) {\n            if (rows[i].length > rowLength) {\n                rowLength = rows[i].length;\n            }\n        }\n        // Add header\n        treeChildren.push(getTableHeaderHTML(topHeaders, subHeaders || [], Math.max(rowLength, subHeaders?.length || 0)));\n        // Transform the rows to HTML\n        const trs = [];\n        rows.forEach(function (row) {\n            const trChildren = [];\n            for (let j = 0; j < rowLength; j++) {\n                // Make first column a header too. Especially important for\n                // category axes, but also might make sense for datetime? Should\n                // await user feedback on this.\n                trChildren.push(getCellHTMLFromValue(j ? 'td' : 'th', null, j ? {} : { scope: 'row' }, row[j]));\n            }\n            trs.push({\n                tagName: 'tr',\n                children: trChildren\n            });\n        });\n        treeChildren.push({\n            tagName: 'tbody',\n            children: trs\n        });\n        const e = {\n            tree: {\n                tagName: 'table',\n                id: `highcharts-data-table-${chart.index}`,\n                children: treeChildren\n            }\n        };\n        fireEvent(chart, 'afterGetTableAST', e);\n        return e.tree;\n    }\n    /**\n     * Hide the data table when visible.\n     *\n     * @function Highcharts.Exporting#hideData\n     *\n     * @requires modules/exporting\n     * @requires modules/export-data\n     */\n    function hideData() {\n        this.toggleDataTable(false);\n    }\n    /**\n     * Toggle showing data table.\n     *\n     * @private\n     * @function Highcharts.Exporting#hideData\n     *\n     * @param {boolean} [show]\n     * Whether to show data table or not.\n     *\n     * @requires modules/exporting\n     * @requires modules/export-data\n     */\n    function toggleDataTable(show) {\n        const chart = this.chart, \n        // Create the div\n        createContainer = (show = pick(show, !this.isDataTableVisible)) &&\n            !this.dataTableDiv;\n        if (createContainer) {\n            this.dataTableDiv = ExportData_doc.createElement('div');\n            this.dataTableDiv.className = 'highcharts-data-table';\n            // Insert after the chart container\n            chart.renderTo.parentNode.insertBefore(this.dataTableDiv, chart.renderTo.nextSibling);\n        }\n        // Toggle the visibility\n        if (this.dataTableDiv) {\n            const style = this.dataTableDiv.style, oldDisplay = style.display;\n            style.display = show ? 'block' : 'none';\n            // Generate the data table\n            if (show) {\n                this.dataTableDiv.innerHTML = (highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default()).emptyHTML;\n                const ast = new (highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default())([this.getTableAST()]);\n                ast.addToDOM(this.dataTableDiv);\n                fireEvent(chart, 'afterViewData', {\n                    element: this.dataTableDiv,\n                    wasHidden: createContainer || oldDisplay !== style.display\n                });\n            }\n            else {\n                fireEvent(chart, 'afterHideData');\n            }\n        }\n        // Set the flag\n        this.isDataTableVisible = show;\n        // Change the menu item text\n        const exportDivElements = this.divElements, options = this.options, menuItems = options.buttons?.contextButton.menuItems, lang = chart.options.lang;\n        if (options &&\n            options.menuItemDefinitions &&\n            lang &&\n            lang.viewData &&\n            lang.hideData &&\n            menuItems &&\n            exportDivElements) {\n            const exportDivElement = exportDivElements[menuItems.indexOf('viewData')];\n            if (exportDivElement) {\n                highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default().setElementHTML(exportDivElement, this.isDataTableVisible ? lang.hideData : lang.viewData);\n            }\n        }\n    }\n    /**\n     * View the data in a table below the chart.\n     *\n     * @function Highcharts.Exporting#viewData\n     *\n     * @emits Highcharts.Chart#event:afterViewData\n     *\n     * @requires modules/exporting\n     * @requires modules/export-data\n     */\n    function viewData() {\n        this.toggleDataTable(true);\n    }\n    /**\n     * Wrapper function for the download functions, which handles showing and\n     * hiding the loading message\n     *\n     * @private\n     *\n     * @requires modules/exporting\n     * @requires modules/export-data\n     */\n    function wrapLoading(fn) {\n        const chart = this.chart, showMessage = Boolean(this.options.showExportInProgress);\n        // Prefer requestAnimationFrame if available\n        const timeoutFn = ExportData_win.requestAnimationFrame || setTimeout;\n        // Outer timeout avoids menu freezing on click\n        timeoutFn(() => {\n            showMessage &&\n                chart.showLoading(chart.options.lang.exportInProgress);\n            timeoutFn(() => {\n                try {\n                    fn.call(this);\n                }\n                finally {\n                    showMessage && chart.hideLoading();\n                }\n            });\n        });\n    }\n    /**\n     * Function that runs on the chart's 'afterViewData' event.\n     *\n     * @private\n     * @function Highcharts.Chart#onChartAfterViewData\n     *\n     * @requires modules/exporting\n     * @requires modules/export-data\n     */\n    function onChartAfterViewData() {\n        const exporting = this.exporting, dataTableDiv = exporting?.dataTableDiv, getCellValue = (tr, index) => tr.children[index].textContent, comparer = (index, ascending) => (a, b) => {\n            const sort = (v1, v2) => (v1 !== '' && v2 !== '' && !isNaN(v1) && !isNaN(v2) ?\n                v1 - v2 :\n                v1.toString().localeCompare(v2));\n            return sort(getCellValue(ascending ? a : b, index), getCellValue(ascending ? b : a, index));\n        };\n        if (dataTableDiv && exporting.options.allowTableSorting) {\n            const row = dataTableDiv.querySelector('thead tr');\n            if (row) {\n                row.childNodes.forEach((th) => {\n                    const tableBody = dataTableDiv.querySelector('tbody');\n                    th.addEventListener('click', function () {\n                        const rows = [...dataTableDiv.querySelectorAll('tr:not(thead tr)')], headers = [...th.parentNode.children];\n                        if (exporting) {\n                            rows.sort(comparer(headers.indexOf(th), exporting.ascendingOrderInTable =\n                                !exporting.ascendingOrderInTable)).forEach((tr) => {\n                                tableBody?.appendChild(tr);\n                            });\n                            headers.forEach((th) => {\n                                [\n                                    'highcharts-sort-ascending',\n                                    'highcharts-sort-descending'\n                                ].forEach((className) => {\n                                    if (th.classList.contains(className)) {\n                                        th.classList.remove(className);\n                                    }\n                                });\n                            });\n                            th.classList.add(exporting.ascendingOrderInTable ?\n                                'highcharts-sort-ascending' :\n                                'highcharts-sort-descending');\n                        }\n                    });\n                });\n            }\n        }\n    }\n    /**\n     * Function that runs on the chart's 'render' event. Handle the showTable\n     * option.\n     *\n     * @private\n     * @function Highcharts.Chart#onChartRenderer\n     *\n     * @requires modules/exporting\n     * @requires modules/export-data\n     */\n    function onChartRenderer() {\n        if (this.options?.exporting?.showTable &&\n            !this.options.chart.forExport) {\n            this.exporting?.viewData();\n        }\n    }\n    /**\n     * Function that runs on the chart's 'destroy' event. Handle cleaning up the\n     * dataTableDiv element.\n     *\n     * @private\n     * @function Highcharts.Chart#onChartDestroy\n     *\n     * @requires modules/exporting\n     * @requires modules/export-data\n     */\n    function onChartDestroy() {\n        this.exporting?.dataTableDiv?.remove();\n    }\n})(ExportData || (ExportData = {}));\n/* *\n *\n * Default Export\n *\n * */\n/* harmony default export */ const ExportData_ExportData = (ExportData);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Function callback to execute while data rows are processed for exporting.\n * This allows the modification of data rows before processed into the final\n * format.\n *\n * @callback Highcharts.ExportDataCallbackFunction\n * @extends Highcharts.EventCallbackFunction<Highcharts.Chart>\n *\n * @param {Highcharts.Chart} this\n * Chart context where the event occurred.\n *\n * @param {Highcharts.ExportDataEventObject} event\n * Event object with data rows that can be modified.\n */\n/**\n * Contains information about the export data event.\n *\n * @interface Highcharts.ExportDataEventObject\n */ /**\n* Contains the data rows for the current export task and can be modified.\n* @name Highcharts.ExportDataEventObject#dataRows\n* @type {Array<Array<string>>}\n*/\n(''); // Keeps doclets above in JS file\n\n;// ./code/es-modules/masters/modules/export-data.js\n\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n// Compatibility\nG.dataURLtoBlob = G.dataURLtoBlob || Extensions_DownloadURL.dataURLtoBlob;\nG.downloadURL = G.downloadURL || Extensions_DownloadURL.downloadURL;\n// Compose\nExportData_ExportData.compose(G.Chart, G.Exporting, G.Series);\n/* harmony default export */ const export_data_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__660__", "__WEBPACK_EXTERNAL_MODULE__960__", "ExportData", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "export_data_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "<PERSON><PERSON><PERSON><PERSON>", "win", "document", "doc", "error", "domurl", "URL", "webkitURL", "dataURLtoBlob", "dataURL", "parts", "replace", "match", "length", "atob", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Uint8Array", "Blob", "createObjectURL", "binStr", "buf", "binary", "i", "charCodeAt", "Extensions_DownloadURL", "downloadURL", "filename", "nav", "navigator", "createElement", "String", "msSaveOrOpenBlob", "userAgent", "Error", "isOldEdgeBrowser", "test", "safariBlob", "indexOf", "download", "href", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "open", "location", "getScript", "scriptLocation", "Promise", "resolve", "reject", "head", "getElementsByTagName", "script", "type", "src", "onload", "onerror", "highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_", "highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default", "highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_", "highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default", "ExportData_ExportDataDefaults", "exporting", "csv", "annotations", "itemDelimiter", "join", "columnHeaderFormatter", "dateFormat", "decimalPoint", "lineDelimiter", "menuItemDefinitions", "downloadCSV", "<PERSON><PERSON><PERSON>", "onclick", "downloadXLS", "viewData", "wrapLoading", "toggleDataTable", "showTable", "useMultiLevelHeaders", "useRowspanHeaders", "showExportInProgress", "lang", "exportData", "annotationHeader", "categoryHeader", "categoryDatetimeHeader", "hideData", "exportInProgress", "getOptions", "setOptions", "ExportData_downloadURL", "composed", "ExportData_doc", "ExportData_win", "addEvent", "defined", "extend", "find", "fireEvent", "isNumber", "pick", "pushUnique", "getCSV", "getBlobFromContent", "encodeURIComponent", "getFilename", "template", "getTable", "uri", "btoa", "unescape", "content", "MSBlobBuilder", "blob", "append", "getBlob", "e", "useLocalDecimalPoint", "rows", "getDataRows", "csvOptions", "options", "toLocaleString", "for<PERSON>ach", "row", "val", "j", "toString", "multiLevelHeaders", "xAxisIndex", "column", "chart", "hasParallelCoords", "hasParallelCoordinates", "time", "xAxes", "xAxis", "rowArr", "topLevelColumnTitles", "columnTitles", "exportDataOptions", "langOptions", "item", "<PERSON><PERSON><PERSON><PERSON>", "s", "bindAxes", "title", "text", "dateTime", "columnTitle", "name", "topLevelColumnTitle", "getCategoryAndDateTimeMap", "series", "pointArrayMap", "pIdx", "categoryMap", "dateTimeValueAxisMap", "axisName", "keyToAxis", "axis", "categories", "getPointArray", "namedPoints", "data", "some", "y", "exportKey", "xAxisIndices", "dataRows", "columnTitleObj", "x", "xTitle", "keys", "valueCount", "xTaken", "requireSorting", "categoryAndDatetimeMap", "mockSeries", "includeInDataExport", "isInternal", "visible", "index", "push", "autoIncrement", "mockPoint", "pointClass", "applyOptions", "apply", "hasNames", "modifiedKey", "pointers", "original<PERSON>ey", "xValues", "arr", "getNestedProperty", "sort", "b", "splice", "category", "Date", "getTime", "names", "concat", "serialize", "node", "tagName", "textContent", "attributes", "html", "value", "children", "child", "getTableAST", "<PERSON><PERSON><PERSON><PERSON>", "tree<PERSON><PERSON><PERSON>n", "topHeaders", "shift", "subHeaders", "isRowEqual", "row1", "row2", "getCellHTMLFromValue", "classes", "className", "tableCaption", "chartTitle", "len", "getTableHeaderHTML", "topheaders", "subheaders", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cur", "cur<PERSON><PERSON><PERSON>", "rowspan", "tr<PERSON><PERSON><PERSON><PERSON>", "scope", "colspan", "cell", "valign", "Math", "max", "trs", "tree", "id", "show", "createContainer", "isDataTableVisible", "dataTableDiv", "renderTo", "parentNode", "insertBefore", "nextS<PERSON>ling", "style", "oldDisplay", "display", "innerHTML", "emptyHTML", "ast", "addToDOM", "element", "wasH<PERSON>den", "exportDivElements", "divElements", "menuItems", "buttons", "contextButton", "exportDivElement", "setElementHTML", "fn", "showMessage", "Boolean", "timeoutFn", "requestAnimationFrame", "setTimeout", "showLoading", "hideLoading", "onChartAfterViewData", "getCellValue", "tr", "comparer", "ascending", "v1", "v2", "isNaN", "localeCompare", "allowTableSorting", "querySelector", "childNodes", "th", "tableBody", "addEventListener", "querySelectorAll", "headers", "ascendingOrderInTable", "classList", "contains", "remove", "add", "on<PERSON><PERSON><PERSON><PERSON><PERSON>", "forExport", "onChartDestroy", "compose", "ChartClass", "ExportingClass", "SeriesClass", "exportingProto", "arearange", "AreaRangeSeries", "gantt", "GanttSeries", "map", "MapSeries", "mapbubble", "MapBubbleSeries", "treemap", "TreemapSeries", "xrange", "XRangeSeries", "types", "low", "high", "start", "end", "x2", "ExportData_ExportData", "G", "Chart", "Exporting", "Series"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,GAAM,CAAEA,EAAK,WAAc,CAAC,KAAQ,EAC/F,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,iCAAkC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,GAAM,CAACA,EAAK,KAAQ,CAAE,GAC7H,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,iCAAiC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,GAAM,CAAEA,EAAK,WAAc,CAAC,KAAQ,EAEjIA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,GAAM,CAAEA,EAAK,UAAa,CAAC,KAAQ,CACzG,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,IACvG,AAAC,CAAA,KACP,aACA,IA4nBNC,EA5nBUC,EAAuB,CAE/B,IACC,AAACT,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,AAACL,IAERA,EAAOD,OAAO,CAAGQ,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAad,OAAO,CAG5B,IAAIC,EAASU,CAAwB,CAACE,EAAS,CAAG,CAGjDb,QAAS,CAAC,CACX,EAMA,OAHAU,CAAmB,CAACG,EAAS,CAACZ,EAAQA,EAAOD,OAAO,CAAEY,GAG/CX,EAAOD,OAAO,AACtB,CAMCY,EAAoBI,CAAC,CAAG,AAACf,IACxB,IAAIgB,EAAShB,GAAUA,EAAOiB,UAAU,CACvC,IAAOjB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAW,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACnB,EAASqB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACvB,EAASsB,IAC5EE,OAAOC,cAAc,CAACzB,EAASsB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAoBrH,GAAM,CAAEE,SAAAA,CAAQ,CAAEC,IAAAA,CAAG,CAAEA,IAAK,CAAEC,SAAUC,CAAG,CAAE,CAAE,CAAIJ,IAE7C,CAAEK,MAAAA,CAAK,CAAE,CAAIL,IAMbM,EAASJ,EAAIK,GAAG,EAAIL,EAAIM,SAAS,EAAIN,EAkB3C,SAASO,EAAcC,CAAO,EAC1B,IAAMC,EAAQD,EACTE,OAAO,CAAC,eAAgB,IACxBC,KAAK,CAAC,yCACX,GAAIF,GACAA,EAAMG,MAAM,CAAG,GACdZ,EAAIa,IAAI,EACTb,EAAIc,WAAW,EACfd,EAAIe,UAAU,EACdf,EAAIgB,IAAI,EACPZ,EAAOa,eAAe,CAAG,CAE1B,IAAMC,EAASlB,EAAIa,IAAI,CAACJ,CAAK,CAAC,EAAE,EAAGU,EAAM,IAAInB,EAAIc,WAAW,CAACI,EAAON,MAAM,EAAGQ,EAAS,IAAIpB,EAAIe,UAAU,CAACI,GACzG,IAAK,IAAIE,EAAI,EAAGA,EAAID,EAAOR,MAAM,CAAE,EAAES,EACjCD,CAAM,CAACC,EAAE,CAAGH,EAAOI,UAAU,CAACD,GAElC,OAAOjB,EACFa,eAAe,CAAC,IAAIjB,EAAIgB,IAAI,CAAC,CAACI,EAAO,CAAE,CAAE,KAAQX,CAAK,CAAC,EAAE,AAAC,GACnE,CACJ,CAiG6B,IAAMc,EALf,CAChBhB,cAAAA,EACAiB,YAlFJ,SAAqBhB,CAAO,CAAEiB,CAAQ,EAClC,IAAMC,EAAM1B,EAAI2B,SAAS,CAAE7C,EAAIoB,EAAI0B,aAAa,CAAC,KAGjD,GAAI,AAAmB,UAAnB,OAAOpB,GACP,CAAEA,CAAAA,aAAmBqB,MAAK,GAC1BH,EAAII,gBAAgB,CAAE,YACtBJ,EAAII,gBAAgB,CAACtB,EAASiB,GAIlC,GADAjB,EAAU,GAAKA,EACXkB,EAAIK,SAAS,CAACnB,MAAM,CAAG,IACvB,MAAM,AAAIoB,MAAM,kBAEpB,IAEAC,EAAmB,YAAYC,IAAI,CAACR,EAAIK,SAAS,EAKjD,GAAII,CAAAA,AAHUpC,GACV,AAAmB,UAAnB,OAAOS,GACPA,AAA4C,IAA5CA,EAAQ4B,OAAO,CAAC,yBACFH,GAAoBzB,EAAQI,MAAM,CAAG,GAAM,GAErD,CADJJ,CAAAA,EAAUD,EAAcC,IAAY,EAAC,EAEjC,MAAM,AAAIwB,MAAM,6BAIxB,GAAI,AAAsB,KAAA,IAAflD,EAAEuD,QAAQ,CACjBvD,EAAEwD,IAAI,CAAG9B,EACT1B,EAAEuD,QAAQ,CAAGZ,EACbvB,EAAIqC,IAAI,CAACC,WAAW,CAAC1D,GACrBA,EAAE2D,KAAK,GACPvC,EAAIqC,IAAI,CAACG,WAAW,CAAC5D,QAIrB,GAAI,CACA,GAAI,CAACkB,EAAI2C,IAAI,CAACnC,EAAS,SACnB,MAAM,AAAIwB,MAAM,wBAExB,CACA,KAAM,CAEFhC,EAAI4C,QAAQ,CAACN,IAAI,CAAG9B,CACxB,CAER,EAoCIqC,UA1BJ,SAAmBC,CAAc,EAC7B,OAAO,IAAIC,QAAQ,CAACC,EAASC,KACzB,IAAMC,EAAOhD,EAAIiD,oBAAoB,CAAC,OAAO,CAAC,EAAE,CAAEC,EAASlD,EAAI0B,aAAa,CAAC,SAE7EwB,CAAAA,EAAOC,IAAI,CAAG,kBACdD,EAAOE,GAAG,CAAGR,EAEbM,EAAOG,MAAM,CAAG,KACZP,GACJ,EAEAI,EAAOI,OAAO,CAAG,KACbP,EAAO9C,EAAM,CAAC,qBAAqB,EAAE2C,EAAe,CAAC,EACzD,EAEAI,EAAKV,WAAW,CAACY,EACrB,EACJ,CAUA,EAIA,IAAIK,EAAuFnF,EAAoB,KAC3GoF,EAA2GpF,EAAoBI,CAAC,CAAC+E,GAEjIE,EAA+FrF,EAAoB,KACnHsF,EAAmHtF,EAAoBI,CAAC,CAACiF,GA6ThH,IAAME,EAJR,CACvBC,UAnSc,CAkCdC,IAAK,CAWDC,YAAa,CAQTC,cAAe,KAWfC,KAAM,CAAA,CACV,EA6BAC,sBAAuB,KAKvBC,WAAY,oBASZC,aAAc,KASdJ,cAAe,KAIfK,cAAe,IACnB,EA0BAC,oBAAqB,CAIjBC,YAAa,CACTC,QAAS,cACTC,QAAS,WACL,IAAI,CAACZ,SAAS,EAAEU,aACpB,CACJ,EAIAG,YAAa,CACTF,QAAS,cACTC,QAAS,WACL,IAAI,CAACZ,SAAS,EAAEa,aACpB,CACJ,EAIAC,SAAU,CACNH,QAAS,WACTC,QAAS,WACL,IAAI,CAACZ,SAAS,EAAEe,YAAY,IAAI,CAACf,SAAS,CAACgB,eAAe,CAC9D,CACJ,CACJ,EAaAC,UAAW,CAAA,EAYXC,qBAAsB,CAAA,EAWtBC,kBAAmB,CAAA,EAUnBC,qBAAsB,CAAA,CAC1B,EAqEIC,KAhES,CAOTX,YAAa,eAObG,YAAa,eAObS,WAAY,CAIRC,iBAAkB,cAIlBC,eAAgB,WAIhBC,uBAAwB,UAC5B,EAOAX,SAAU,kBAOVY,SAAU,kBAOVC,iBAAkB,cACtB,CASA,EAiDM,CAAEC,WAAAA,CAAU,CAAEC,WAAAA,CAAU,CAAE,CAAI7F,IAE9B,CAAE0B,YAAaoE,CAAsB,CAAE,CAAGrE,EAG1C,CAAEsE,SAAAA,CAAQ,CAAE3F,IAAK4F,CAAc,CAAE9F,IAAK+F,CAAc,CAAE,CAAIjG,IAE1D,CAAEkG,SAAAA,CAAQ,CAAEC,QAAAA,CAAO,CAAEC,OAAAA,CAAM,CAAEC,KAAAA,CAAI,CAAEC,UAAAA,CAAS,CAAEC,SAAAA,CAAQ,CAAEC,KAAAA,CAAI,CAAEC,WAAAA,CAAU,CAAE,CAAIzG,KAOpF,AAAC,SAAU3B,CAAU,EA8HjB,SAASqG,IACL,IAAI,CAACK,WAAW,CAAC,KACb,IAAMd,EAAM,IAAI,CAACyC,MAAM,CAAC,CAAA,GACxBZ,EAAuBa,EAAmB1C,EAAK,aAC3C,uBAAyB2C,mBAAmB3C,GAAM,IAAI,CAAC4C,WAAW,GAAK,OAC/E,EACJ,CAaA,SAAShC,IACL,IAAI,CAACE,WAAW,CAAC,KACb,IAAqD+B,EAAW,qlBAgB5D,IAAI,CAACC,QAAQ,CAAC,CAAA,GACd,iBAGJjB,EAAuBa,EAAmBG,EAAU,6BAChDE,AArBQ,wCAkBDf,EAAegB,IAAI,CAACC,SAASN,mBAGvBE,KAAW,IAAI,CAACD,WAAW,GAAK,OACrD,EACJ,CAkBA,SAASF,EAAmBQ,CAAO,CAAE5D,CAAI,EACrC,IAAM3B,EAAMqE,EAAepE,SAAS,CAAEvB,EAAS2F,EAAe1F,GAAG,EAAI0F,EAAezF,SAAS,EAAIyF,EACjG,GAAI,CAEA,GAAI,AAACrE,EAAII,gBAAgB,EAAKiE,EAAemB,aAAa,CAAE,CACxD,IAAMC,EAAO,IAAIpB,EAAemB,aAAa,CAE7C,OADAC,EAAKC,MAAM,CAACH,GACLE,EAAKE,OAAO,CAAC,gBACxB,CACA,OAAOjH,EAAOa,eAAe,CAAC,IAAI8E,EAAe/E,IAAI,CAAC,CAAC,SAAWiG,EAAQ,CAC1E,CAAE5D,KAAMA,CAAK,GACjB,CACA,MAAOiE,EAAG,CAEV,CACJ,CAiBA,SAASd,EAAOe,CAAoB,EAChC,IAAIxD,EAAM,GACJyD,EAAO,IAAI,CAACC,WAAW,GAAIC,EAAa,IAAI,CAACC,OAAO,EAAE5D,IAAKM,EAAeiC,EAAKoB,GAAYrD,aAAcqD,GAAYzD,gBAAkB,KAAOsD,EAChJ,AAAC,IAAKK,cAAc,EAAE,CAAC,EAAE,CACzB,KAEJ3D,EAAgBqC,EAAKoB,GAAYzD,cAAeI,AAAiB,MAAjBA,EAAuB,IAAM,KAE7EC,EAAgBoD,GAAYpD,cA6B5B,OA3BAkD,EAAKK,OAAO,CAAC,CAACC,EAAKzG,KACf,IAAI0G,EAAM,GAAIC,EAAIF,EAAIlH,MAAM,CAC5B,KAAOoH,KAEC,AAAe,UAAf,MADJD,CAAAA,EAAMD,CAAG,CAACE,EAAE,AAAD,GAEPD,CAAAA,EAAM,CAAC,CAAC,EAAEA,EAAI,CAAC,CAAC,AAAD,EAEf,AAAe,UAAf,OAAOA,GACH1D,AAAiB,MAAjBA,GACA0D,CAAAA,EAAMA,EAAIE,QAAQ,GAAGvH,OAAO,CAAC,IAAK2D,EAAY,EAGtDyD,CAAG,CAACE,EAAE,CAAGD,CAObD,CAAAA,EAAIlH,MAAM,CAAG4G,EAAK5G,MAAM,CAAG4G,CAAI,CAAC,EAAE,CAAC5G,MAAM,CAAG,EAE5CmD,GAAO+D,EAAI5D,IAAI,CAACD,GAEZ5C,EAAImG,EAAK5G,MAAM,CAAG,GAClBmD,CAAAA,GAAOO,CAAY,CAE3B,GACOP,CACX,CAmBA,SAAS0D,EAAYS,CAAiB,EAClC,IAyLIC,EAAYC,EAzLVC,EAAQ,IAAI,CAACA,KAAK,CAAEC,EAAoBD,EAAME,sBAAsB,CAAEC,EAAOH,EAAMG,IAAI,CAAEd,EAAa,IAAI,CAACC,OAAO,EAAE5D,KAAO,CAAC,EAAG0E,EAAQJ,EAAMK,KAAK,CAAElB,EAAO,CAAC,EAAGmB,EAAS,EAAE,CAAEC,EAAuB,EAAE,CAAEC,EAAe,EAAE,CAAoCC,EAAoBC,AAAxCV,EAAMV,OAAO,CAACxC,IAAI,CAAkCC,UAAU,CAAEE,EAAiBwD,GAAmBxD,eAAgBC,EAAyBuD,GAAmBvD,uBAE9YpB,EAAwB,SAAU6E,CAAI,CAAEhK,CAAG,CAAEiK,CAAS,EAClD,GAAIvB,EAAWvD,qBAAqB,CAAE,CAClC,IAAM+E,EAAIxB,EAAWvD,qBAAqB,CAAC6E,EAAMhK,EAAKiK,GACtD,GAAIC,AAAM,CAAA,IAANA,EACA,OAAOA,CAEf,OACA,AAAI,CAACF,GAAQ1D,EACFA,EAEP,CAAC0D,EAAKG,QAAQ,EACd5D,GACAD,EACO,AAAC0D,EAAKrB,OAAO,CAACyB,KAAK,EACtBJ,EAAKrB,OAAO,CAACyB,KAAK,CAACC,IAAI,EAAML,CAAAA,EAAKM,QAAQ,CAC1C/D,EACAD,CAAa,EAEjB4C,EACO,CACHqB,YAAa,AAAC,CAAA,AAACN,CAAAA,GAAa,CAAA,EAAK,EAC7BjK,EACAgK,EAAKQ,IAAI,AAAD,GAAM,GAClBC,oBAAqBT,EAAKQ,IAAI,AAClC,EAEGR,EAAKQ,IAAI,CAAI,CAAA,AAACP,CAAAA,GAAa,CAAA,EAAK,EAAI,KAAOjK,EAAM,IAAM,EAAC,CACnE,EAEA0K,EAA4B,SAAUC,CAAM,CAAEC,CAAa,CAAEC,CAAI,EAC7D,IAAMC,EAAc,CAAC,EAAGC,EAAuB,CAAC,EAYhD,OAXAH,EAAc/B,OAAO,CAAC,SAAUtI,CAAI,EAChC,IAAMyK,EAAW,AAAC,CAAA,AAACL,EAAOM,SAAS,EAAIN,EAAOM,SAAS,CAAC1K,EAAK,EACzDA,CAAG,EAAK,OAGZ2K,EAAO7D,EAASwD,GACZF,EAAOtB,KAAK,CAAC2B,EAAS,CAACH,EAAK,CAC5BF,CAAM,CAACK,EAAS,AACpBF,CAAAA,CAAW,CAACvK,EAAK,CAAG,AAAC2K,GAAQA,EAAKC,UAAU,EAAK,EAAE,CACnDJ,CAAoB,CAACxK,EAAK,CAAI2K,GAAQA,EAAKZ,QAAQ,AACvD,GACO,CACHQ,YAAaA,EACbC,qBAAsBA,CAC1B,CACJ,EAGAK,EAAgB,SAAUT,CAAM,CAAEjB,CAAK,EACnC,IAAMkB,EAAgBD,EAAOC,aAAa,EAAI,CAAC,IAAI,QAGnD,AAAIS,AAH+DV,EAAOW,IAAI,CAACC,IAAI,CAAC,AAAC1L,GAAM,AAAgB,KAAA,IAARA,EAAE2L,CAAC,EAAqB3L,EAAE2K,IAAI,GAI7Hd,GACA,CAACA,EAAMyB,UAAU,EACjBR,AAAqB,SAArBA,EAAOc,SAAS,CACT,CAAC,OAAQb,EAAc,CAE3BA,CACX,EAAGc,EAAe,EAAE,CAChBhC,EAAOiC,EAAUC,EAAgBvJ,EAAI,EACzCwJ,EAAGC,EAoHH,IAAKD,KAnHLxC,EAAMsB,MAAM,CAAC9B,OAAO,CAAC,SAAU8B,CAAM,EACjC,IAAMoB,EAAOpB,EAAOhC,OAAO,CAACoD,IAAI,CAAErC,EAAQiB,EAAOjB,KAAK,CAAEkB,EAAgBmB,GAAQX,EAAcT,EAAQjB,GAAQsC,EAAapB,EAAchJ,MAAM,CAAEqK,EAAS,CAACtB,EAAOuB,cAAc,EAAI,CAAC,EAAG/C,EAAaM,EAAMrG,OAAO,CAACsG,GAC/MyC,EAAyBzB,EAA0BC,EAAQC,GAAgBwB,EAAYpD,EAC3F,GAAI2B,AAAuC,CAAA,IAAvCA,EAAOhC,OAAO,CAAC0D,mBAAmB,EAClC,CAAC1B,EAAOhC,OAAO,CAAC2D,UAAU,EAC1B3B,AAAmB,CAAA,IAAnBA,EAAO4B,OAAO,CAChB,CAYE,IARI,AAACpF,EAAKuE,EAAc,SAAUc,CAAK,EACnC,OAAOA,CAAK,CAAC,EAAE,GAAKrD,CACxB,IACIuC,EAAae,IAAI,CAAC,CAACtD,EAAY9G,EAAE,EAIrC2G,EAAI,EACGA,EAAIgD,GACPJ,EAAiBzG,EAAsBwF,EAAQC,CAAa,CAAC5B,EAAE,CAAE4B,EAAchJ,MAAM,EACrFiI,EAAa4C,IAAI,CAACb,EAAerB,WAAW,EACxCqB,GACA1C,GACAU,EAAqB6C,IAAI,CAACb,EAAenB,mBAAmB,EACxDmB,GAER5C,IAEJoD,EAAa,CACT/C,MAAOsB,EAAOtB,KAAK,CACnBqD,cAAe/B,EAAO+B,aAAa,CACnC/D,QAASgC,EAAOhC,OAAO,CACvBiC,cAAeD,EAAOC,aAAa,CACnC4B,MAAO7B,EAAO6B,KAAK,AACvB,EAGA7B,EAAOhC,OAAO,CAAC2C,IAAI,EAAEzC,QAAQ,SAAkBF,CAAO,CAAEkC,CAAI,EACxD,IACI7K,EAAKO,EAAMwI,EADT4D,EAAY,CAAEhC,OAAQyB,CAAW,CAInC9C,CAAAA,GACA6C,CAAAA,EAAyBzB,EAA0BC,EAAQC,EAAeC,EAAI,EAElFF,EAAOiC,UAAU,CAACpM,SAAS,CAACqM,YAAY,CAACC,KAAK,CAACH,EAAW,CAAChE,EAAQ,EACnE,IAAM6B,EAAOG,EAAOW,IAAI,CAACT,EAAK,EAAIF,EAAOW,IAAI,CAACT,EAAK,CAACL,IAAI,CAexD,GAdAxK,EAAM,AAAC2M,CAAAA,EAAUd,CAAC,EAAI,EAAC,EAAK,IAAMrB,EAClCxB,EAAI,EAEA,CAAA,CAACU,GACDiB,AAAqB,SAArBA,EAAOc,SAAS,EAChB,AAAC,CAACnC,GAAqBI,GAASA,EAAMqD,QAAQ,EAAKvC,CAAG,GACtDxK,CAAAA,EAAMwK,CAAG,EAETyB,IACIA,CAAM,CAACjM,EAAI,EACXA,CAAAA,GAAO,IAAM6K,CAAG,EAEpBoB,CAAM,CAACjM,EAAI,CAAG,CAAA,GAEbwI,CAAI,CAACxI,EAAI,CAcT,CAKD,IAAMgN,EAAc,CAAC,EAAEhN,EAAI,CAAC,EAAEwI,CAAI,CAACxI,EAAI,CAACiN,QAAQ,CAACtC,EAAO6B,KAAK,CAAC,CAAC,CAAC,CAAEU,EAAclN,CAC5EwI,CAAAA,CAAI,CAACxI,EAAI,CAACiN,QAAQ,CAACtC,EAAO6B,KAAK,CAAC,GAC3BhE,CAAI,CAACwE,EAAY,GAClBxE,CAAI,CAACwE,EAAY,CAAG,EAAE,CACtBxE,CAAI,CAACwE,EAAY,CAACG,OAAO,CAAG,EAAE,CAC9B3E,CAAI,CAACwE,EAAY,CAACC,QAAQ,CAAG,EAAE,EAEnCjN,EAAMgN,GAEVxE,CAAI,CAAC0E,EAAY,CAACD,QAAQ,CAACtC,EAAO6B,KAAK,CAAC,EAAI,CAChD,KA7BgB,CACZhE,CAAI,CAACxI,EAAI,CAAG,EAAE,CACdwI,CAAI,CAACxI,EAAI,CAACmN,OAAO,CAAG,EAAE,CAEtB,IAAMC,EAAM,EAAE,CACd,IAAK,IAAI/K,EAAI,EAAGA,EAAIsI,EAAOtB,KAAK,CAACsB,MAAM,CAAC/I,MAAM,CAAES,IAC5C+K,CAAG,CAAC/K,EAAE,CAAG,CAKbmG,CAAAA,CAAI,CAACxI,EAAI,CAACiN,QAAQ,CAAGG,EACrB5E,CAAI,CAACxI,EAAI,CAACiN,QAAQ,CAACtC,EAAO6B,KAAK,CAAC,CAAG,CACvC,CAoBA,IAHAhE,CAAI,CAACxI,EAAI,CAAC6L,CAAC,CAAGc,EAAUd,CAAC,CACzBrD,CAAI,CAACxI,EAAI,CAACwK,IAAI,CAAGA,EACjBhC,CAAI,CAACxI,EAAI,CAACmN,OAAO,CAAChE,EAAW,CAAGwD,EAAUd,CAAC,CACpC7C,EAAIgD,GACPzL,EAAOqK,CAAa,CAAC5B,EAAE,CACvBD,EACI4B,EAAOiC,UAAU,CAACpM,SAAS,CAAC6M,iBAAiB,CAACP,KAAK,CAACH,EAAW,CAACpM,EAAK,EAEzEiI,CAAI,CAACxI,EAAI,CAACqC,EAAI2G,EAAE,CAAG1B,EAEnB6E,EAAuBrB,WAAW,CAACvK,EAAK,CAACwI,EAAI,CAE7CoD,EAAuBpB,oBAAoB,CAACxK,EAAK,CAC7CiJ,EAAKpE,UAAU,CAACsD,EAAWtD,UAAU,CAAE2D,GACvC,KAEJA,GACAC,GAER,GACA3G,GAAQ2G,CACZ,CACJ,GAEUR,EACFtI,OAAOO,cAAc,CAACC,IAAI,CAAC8H,EAAMqD,IACjClC,EAAO8C,IAAI,CAACjE,CAAI,CAACqD,EAAE,EAQ3B,IAHAF,EAAWzC,EAAoB,CAACU,EAAsBC,EAAa,CAC/D,CAACA,EAAa,CAClBxH,EAAIqJ,EAAa9J,MAAM,CAChBS,KACH8G,EAAauC,CAAY,CAACrJ,EAAE,CAAC,EAAE,CAC/B+G,EAASsC,CAAY,CAACrJ,EAAE,CAAC,EAAE,CAC3BqH,EAAQD,CAAK,CAACN,EAAW,CAEzBQ,EAAO2D,IAAI,CAAC,SACZxN,CAAC,CAAEyN,CAAC,EACA,OAAOzN,EAAEqN,OAAO,CAAChE,EAAW,CAAGoE,EAAEJ,OAAO,CAAChE,EAAW,AACxD,GAEA2C,EAAS3G,EAAsBuE,GAC/BiC,CAAQ,CAAC,EAAE,CAAC6B,MAAM,CAACpE,EAAQ,EAAG0C,GAC1B5C,GAAqByC,CAAQ,CAAC,EAAE,EAGhCA,CAAQ,CAAC,EAAE,CAAC6B,MAAM,CAACpE,EAAQ,EAAG0C,GAGlCnC,EAAOd,OAAO,CAAC,SACfC,CAAG,EACC,IAAI2E,EAAW3E,EAAI0B,IAAI,CACnBd,GAAS,CAACzC,EAAQwG,KACd/D,EAAMY,QAAQ,EACVxB,EAAI+C,CAAC,YAAY6B,MACjB5E,CAAAA,EAAI+C,CAAC,CAAG/C,EAAI+C,CAAC,CAAC8B,OAAO,EAAC,EAE1BF,EAAWjE,EAAKpE,UAAU,CAACsD,EAAWtD,UAAU,CAAE0D,EAAI+C,CAAC,GAGvD4B,EADK/D,EAAMyB,UAAU,CACV7D,EAAKoC,EAAMkE,KAAK,CAAC9E,EAAI+C,CAAC,CAAC,CAAEnC,EAAMyB,UAAU,CAACrC,EAAI+C,CAAC,CAAC,CAAE/C,EAAI+C,CAAC,EAGvD/C,EAAI+C,CAAC,EAIxB/C,EAAI0E,MAAM,CAACpE,EAAQ,EAAGqE,EAC1B,GAIJ,OADArG,EAAUiC,EAAO,aAAc,CAAEsC,SADjCA,EAAWA,EAASkC,MAAM,CAAClE,EACyB,GAC7CgC,CACX,CAsBA,SAAS9D,EAASU,CAAoB,EAClC,IAAMuF,EAAY,AAACC,IACf,GAAI,CAACA,EAAKC,OAAO,EAAID,AAAiB,UAAjBA,EAAKC,OAAO,CAE7B,OAAOD,EAAKE,WAAW,EAAI,GAE/B,IAAMC,EAAaH,EAAKG,UAAU,CAC9BC,EAAO,CAAC,CAAC,EAAEJ,EAAKC,OAAO,CAAC,CAAC,CAe7B,OAdIE,GACAhO,OAAO6L,IAAI,CAACmC,GACPrF,OAAO,CAAC,AAAC7I,IACV,IAAMoO,EAAQF,CAAU,CAAClO,EAAI,CAE7BmO,GAAQ,CAAC,CAAC,EAAEnO,EAAI,EAAE,EAAEoO,EAAM,CAAC,CAAC,AAChC,GAEJD,GAAQ,IACRA,GAAQJ,EAAKE,WAAW,EAAI,GAC5B,AAACF,CAAAA,EAAKM,QAAQ,EAAI,EAAE,AAAD,EAAGxF,OAAO,CAAC,AAACyF,IAC3BH,GAAQL,EAAUQ,EACtB,GACAH,GAAQ,CAAC,EAAE,EAAEJ,EAAKC,OAAO,CAAC,CAAC,CAAC,AAEhC,EAEA,OAAOF,EADM,IAAI,CAACS,WAAW,CAAChG,GAElC,CAkBA,SAASgG,EAAYhG,CAAoB,EACrC,IAAIiG,EAAY,EACVC,EAAe,EAAE,CAAE3J,EAAY,IAAI,CAAEuE,EAAQvE,EAAUuE,KAAK,CAAEV,EAAUU,EAAMV,OAAO,CAAEtD,EAAekD,EAAuB,AAAC,IAAKK,cAAc,EAAE,CAAC,EAAE,CAAG,IAAK5C,EAAuBsB,EAAKxC,EAAU6D,OAAO,CAAC3C,oBAAoB,CAAE,CAAA,GAAOwC,EAAO1D,EAAU2D,WAAW,CAACzC,GAAuB0I,EAAa1I,EAAuBwC,EAAKmG,KAAK,GAAK,KAAMC,EAAapG,EAAKmG,KAAK,GAEnXE,EAAa,SAAUC,CAAI,CAAEC,CAAI,EAC7B,IAAI1M,EAAIyM,EAAKlN,MAAM,CACnB,GAAImN,EAAKnN,MAAM,GAAKS,EAQhB,MAAO,CAAA,EAPP,KAAOA,KACH,GAAIyM,CAAI,CAACzM,EAAE,GAAK0M,CAAI,CAAC1M,EAAE,CACnB,MAAO,CAAA,EAOnB,MAAO,CAAA,CACX,EAEA2M,EAAuB,SAAUhB,CAAO,CAAEiB,CAAO,CAAEf,CAAU,CAAEE,CAAK,EAChE,IAAIH,EAAc3G,EAAK8G,EAAO,IAAKc,EAAY,kBAAqBD,CAAAA,EAAU,IAAMA,EAAU,EAAC,EAa/F,MAXI,AAAuB,UAAvB,OAAOhB,GACPA,EAAcA,EAAYhF,QAAQ,GAC9B5D,AAAiB,MAAjBA,GACA4I,CAAAA,EAAcA,EAAYvM,OAAO,CAAC,IAAK2D,EAAY,EAEvD6J,EAAY,qBAEP,AAACd,GACNc,CAAAA,EAAY,kBAAiB,EAG1B,CACHlB,QAAAA,EACAE,WAHJA,EAAahH,EAAO,CAAE,MAASgI,CAAU,EAAGhB,GAIxCD,YAAAA,CACJ,CACJ,EA8EM,CAAEkB,aAAAA,CAAY,CAAE,CAAGrK,EAAU6D,OAAO,EAAI,CAAC,CAC3CwG,AAAiB,EAAA,IAAjBA,GACAV,EAAahC,IAAI,CAAC,CACduB,QAAS,UACTE,WAAY,CACR,MAAS,0BACb,EACAD,YAAa,AAAwB,UAAxB,OAAOkB,EAChBA,EACAxG,EAAQyB,KAAK,EAAEC,MAAQ1B,EAAQxC,IAAI,CAACiJ,UAAU,AACtD,GAGJ,IAAK,IAAI/M,EAAI,EAAGgN,EAAM7G,EAAK5G,MAAM,CAAES,EAAIgN,EAAK,EAAEhN,EACtCmG,CAAI,CAACnG,EAAE,CAACT,MAAM,CAAG4M,GACjBA,CAAAA,EAAYhG,CAAI,CAACnG,EAAE,CAACT,MAAM,AAAD,EAIjC6M,EAAahC,IAAI,CAAC6C,AA/FG,SAAUC,CAAU,CAAEC,CAAU,CAAEhB,CAAS,EAC5D,IAAMiB,EAAgB,EAAE,CACpBpN,EAAI,EAAGgN,EAAMb,GAAagB,GAAcA,EAAW5N,MAAM,CAAQ8N,EAAKC,EAAa,EAAGC,EAK1F,GAAI5J,GACAuJ,GACAC,GACA,CAACX,EAAWU,EAAYC,GAAa,CACrC,IAAMK,EAAa,EAAE,CACrB,KAAOxN,EAAIgN,EAAK,EAAEhN,EAGd,GAFAqN,CAAAA,EAAMH,CAAU,CAAClN,EAAE,AAAD,IACXkN,CAAU,CAAClN,EAAI,EAAE,CAEpB,EAAEsN,OAED,GAAIA,EAGLE,EAAWpD,IAAI,CAACuC,EAAqB,KAAM,8BAA+B,CACtEc,MAAO,MACPC,QAASJ,EAAa,CAC1B,EAAGD,IACHC,EAAa,MAEZ,CAGGD,IAAQF,CAAU,CAACnN,EAAE,CACjByC,EAAU6D,OAAO,CAAC1C,iBAAiB,EACnC2J,EAAU,EACV,OAAOJ,CAAU,CAACnN,EAAE,GAGpBuN,EAAU,EACVJ,CAAU,CAACnN,EAAE,CAAG,IAIpBuN,EAAU,EAEd,IAAMI,EAAOhB,EAAqB,KAAM,8BAA+B,CAAEc,MAAO,KAAM,EAAGJ,GACrFE,EAAU,GAAKI,EAAK9B,UAAU,GAC9B8B,EAAK9B,UAAU,CAAC+B,MAAM,CAAG,MACzBD,EAAK9B,UAAU,CAAC0B,OAAO,CAAGA,GAE9BC,EAAWpD,IAAI,CAACuD,EACpB,CAEJP,EAAchD,IAAI,CAAC,CACfuB,QAAS,KACTK,SAAUwB,CACd,EACJ,CAGA,GAAIL,EAAY,CACZ,IAAMK,EAAa,EAAE,CACrB,IAAKxN,EAAI,EAAGgN,EAAMG,EAAW5N,MAAM,CAAES,EAAIgN,EAAK,EAAEhN,EACxC,AAAyB,KAAA,IAAlBmN,CAAU,CAACnN,EAAE,EACpBwN,EAAWpD,IAAI,CAACuC,EAAqB,KAAM,KAAM,CAAEc,MAAO,KAAM,EAAGN,CAAU,CAACnN,EAAE,GAGxFoN,EAAchD,IAAI,CAAC,CACfuB,QAAS,KACTK,SAAUwB,CACd,EACJ,CACA,MAAO,CACH7B,QAAS,QACTK,SAAUoB,CACd,CACJ,EAqBqCf,EAAYE,GAAc,EAAE,CAAEsB,KAAKC,GAAG,CAAC3B,EAAWI,GAAYhN,QAAU,KAE7G,IAAMwO,EAAM,EAAE,CACd5H,EAAKK,OAAO,CAAC,SAAUC,CAAG,EACtB,IAAM+G,EAAa,EAAE,CACrB,IAAK,IAAI7G,EAAI,EAAGA,EAAIwF,EAAWxF,IAI3B6G,EAAWpD,IAAI,CAACuC,EAAqBhG,EAAI,KAAO,KAAM,KAAMA,EAAI,CAAC,EAAI,CAAE8G,MAAO,KAAM,EAAGhH,CAAG,CAACE,EAAE,GAEjGoH,EAAI3D,IAAI,CAAC,CACLuB,QAAS,KACTK,SAAUwB,CACd,EACJ,GACApB,EAAahC,IAAI,CAAC,CACduB,QAAS,QACTK,SAAU+B,CACd,GACA,IAAM9H,EAAI,CACN+H,KAAM,CACFrC,QAAS,QACTsC,GAAI,CAAC,sBAAsB,EAAEjH,EAAMmD,KAAK,CAAC,CAAC,CAC1C6B,SAAUI,CACd,CACJ,EAEA,OADArH,EAAUiC,EAAO,mBAAoBf,GAC9BA,EAAE+H,IAAI,AACjB,CASA,SAAS7J,IACL,IAAI,CAACV,eAAe,CAAC,CAAA,EACzB,CAaA,SAASA,EAAgByK,CAAI,EACzB,IAAMlH,EAAQ,IAAI,CAACA,KAAK,CAExBmH,EAAkB,AAACD,CAAAA,EAAOjJ,EAAKiJ,EAAM,CAAC,IAAI,CAACE,kBAAkB,CAAA,GACzD,CAAC,IAAI,CAACC,YAAY,CAQtB,GAPIF,IACA,IAAI,CAACE,YAAY,CAAG5J,EAAelE,aAAa,CAAC,OACjD,IAAI,CAAC8N,YAAY,CAACxB,SAAS,CAAG,wBAE9B7F,EAAMsH,QAAQ,CAACC,UAAU,CAACC,YAAY,CAAC,IAAI,CAACH,YAAY,CAAErH,EAAMsH,QAAQ,CAACG,WAAW,GAGpF,IAAI,CAACJ,YAAY,CAAE,CACnB,IAAMK,EAAQ,IAAI,CAACL,YAAY,CAACK,KAAK,CAAEC,EAAaD,EAAME,OAAO,AACjEF,CAAAA,EAAME,OAAO,CAAGV,EAAO,QAAU,OAE7BA,GACA,IAAI,CAACG,YAAY,CAACQ,SAAS,CAAG,AAACxM,IAA+FyM,SAAS,CAEvIC,AADY,GAAK1M,CAAAA,GAA4F,EAAG,CAAC,IAAI,CAAC6J,WAAW,GAAG,EAChI8C,QAAQ,CAAC,IAAI,CAACX,YAAY,EAC9BtJ,EAAUiC,EAAO,gBAAiB,CAC9BiI,QAAS,IAAI,CAACZ,YAAY,CAC1Ba,UAAWf,GAAmBQ,IAAeD,EAAME,OAAO,AAC9D,IAGA7J,EAAUiC,EAAO,gBAEzB,CAEA,IAAI,CAACoH,kBAAkB,CAAGF,EAE1B,IAAMiB,EAAoB,IAAI,CAACC,WAAW,CAAE9I,EAAU,IAAI,CAACA,OAAO,CAAE+I,EAAY/I,EAAQgJ,OAAO,EAAEC,cAAcF,UAAWvL,EAAOkD,EAAMV,OAAO,CAACxC,IAAI,CACnJ,GAAIwC,GACAA,EAAQpD,mBAAmB,EAC3BY,GACAA,EAAKP,QAAQ,EACbO,EAAKK,QAAQ,EACbkL,GACAF,EAAmB,CACnB,IAAMK,EAAmBL,CAAiB,CAACE,EAAUtO,OAAO,CAAC,YAAY,AACrEyO,CAAAA,GACAnN,IAA8FoN,cAAc,CAACD,EAAkB,IAAI,CAACpB,kBAAkB,CAAGtK,EAAKK,QAAQ,CAAGL,EAAKP,QAAQ,CAE9L,CACJ,CAWA,SAASA,IACL,IAAI,CAACE,eAAe,CAAC,CAAA,EACzB,CAUA,SAASD,EAAYkM,CAAE,EACnB,IAAM1I,EAAQ,IAAI,CAACA,KAAK,CAAE2I,EAAcC,CAAAA,CAAQ,IAAI,CAACtJ,OAAO,CAACzC,oBAAoB,CAE3EgM,EAAYnL,EAAeoL,qBAAqB,EAAIC,WAE1DF,EAAU,KACNF,GACI3I,EAAMgJ,WAAW,CAAChJ,EAAMV,OAAO,CAACxC,IAAI,CAACM,gBAAgB,EACzDyL,EAAU,KACN,GAAI,CACAH,EAAGrR,IAAI,CAAC,IAAI,CAChB,QACQ,CACJsR,GAAe3I,EAAMiJ,WAAW,EACpC,CACJ,EACJ,EACJ,CAUA,SAASC,IACL,IAAMzN,EAAY,IAAI,CAACA,SAAS,CAAE4L,EAAe5L,GAAW4L,aAAc8B,EAAe,CAACC,EAAIjG,IAAUiG,EAAGpE,QAAQ,CAAC7B,EAAM,CAACyB,WAAW,CAAEyE,EAAW,CAAClG,EAAOmG,IAAc,CAAC7S,EAAGyN,SAC3JqF,EAAIC,EAGlB,OAHcD,EAGFJ,EAAaG,EAAY7S,EAAIyN,EAAGf,GAH1BqG,EAGkCL,EAAaG,EAAYpF,EAAIzN,EAAG0M,GAH1DoG,AAAO,KAAPA,GAAaC,AAAO,KAAPA,GAAcC,MAAMF,IAAQE,MAAMD,GAErED,EAAG3J,QAAQ,GAAG8J,aAAa,CAACF,GAD5BD,EAAKC,CAGb,EACA,GAAInC,GAAgB5L,EAAU6D,OAAO,CAACqK,iBAAiB,CAAE,CACrD,IAAMlK,EAAM4H,EAAauC,aAAa,CAAC,WACnCnK,CAAAA,GACAA,EAAIoK,UAAU,CAACrK,OAAO,CAAC,AAACsK,IACpB,IAAMC,EAAY1C,EAAauC,aAAa,CAAC,SAC7CE,EAAGE,gBAAgB,CAAC,QAAS,WACzB,IAAM7K,EAAO,IAAIkI,EAAa4C,gBAAgB,CAAC,oBAAoB,CAAEC,EAAU,IAAIJ,EAAGvC,UAAU,CAACvC,QAAQ,CAAC,CACtGvJ,IACA0D,EAAK8E,IAAI,CAACoF,EAASa,EAAQnQ,OAAO,CAAC+P,GAAKrO,EAAU0O,qBAAqB,CACnE,CAAC1O,EAAU0O,qBAAqB,GAAG3K,OAAO,CAAC,AAAC4J,IAC5CW,GAAW5P,YAAYiP,EAC3B,GACAc,EAAQ1K,OAAO,CAAC,AAACsK,IACb,CACI,4BACA,6BACH,CAACtK,OAAO,CAAC,AAACqG,IACHiE,EAAGM,SAAS,CAACC,QAAQ,CAACxE,IACtBiE,EAAGM,SAAS,CAACE,MAAM,CAACzE,EAE5B,EACJ,GACAiE,EAAGM,SAAS,CAACG,GAAG,CAAC9O,EAAU0O,qBAAqB,CAC5C,4BACA,8BAEZ,EACJ,EAER,CACJ,CAWA,SAASK,IACD,IAAI,CAAClL,OAAO,EAAE7D,WAAWiB,WACzB,CAAC,IAAI,CAAC4C,OAAO,CAACU,KAAK,CAACyK,SAAS,EAC7B,IAAI,CAAChP,SAAS,EAAEc,UAExB,CAWA,SAASmO,IACL,IAAI,CAACjP,SAAS,EAAE4L,cAAciD,QAClC,CA3yBAxU,EAAW6U,OAAO,CAvFlB,SAAiBC,CAAU,CAAEC,CAAc,CAAEC,CAAW,EAEpD,GAAI,CAAC5M,EAAWV,EAAU,cACtB,OAGJK,EAAO,AAACtC,IAAuGpE,SAAS,CAAE,CACtHgF,YAAa,WACT,OAAO,IAAI,CAACV,SAAS,EAAEU,aAC3B,EACAG,YAAa,WACT,OAAO,IAAI,CAACb,SAAS,EAAEa,aAC3B,EACA6B,OAAQ,SAAUe,CAAoB,EAClC,OAAO,IAAI,CAACzD,SAAS,EAAE0C,OAAOe,EAClC,EACAE,YAAa,SAAUS,CAAiB,EACpC,OAAO,IAAI,CAACpE,SAAS,EAAE2D,YAAYS,EACvC,EACArB,SAAU,SAAUU,CAAoB,EACpC,OAAO,IAAI,CAACzD,SAAS,EAAE+C,SAASU,EACpC,EACAgG,YAAa,SAAUhG,CAAoB,EACvC,OAAO,IAAI,CAACzD,SAAS,EAAEyJ,YAAYhG,EACvC,EACA/B,SAAU,WACN,OAAO,IAAI,CAAC1B,SAAS,EAAE0B,UAC3B,EACAV,gBAAiB,SAAUyK,CAAI,EAC3B,OAAO,IAAI,CAACzL,SAAS,EAAEgB,gBAAgByK,EAC3C,EACA3K,SAAU,WACN,OAAO,IAAI,CAACd,SAAS,EAAEc,UAC3B,CACJ,GACA,IAAMwO,EAAiBF,EAAe1T,SAAS,CAC/C,GAAI,CAAC4T,EAAe5O,WAAW,CAAE,CAE7BwB,EAASiN,EAAY,gBAAiB1B,GACtCvL,EAASiN,EAAY,SAAUJ,GAC/B7M,EAASiN,EAAY,UAAWF,GAEhCK,EAAe5O,WAAW,CAAGA,EAC7B4O,EAAezO,WAAW,CAAGA,EAC7ByO,EAAe5M,MAAM,CAAGA,EACxB4M,EAAe3L,WAAW,CAAGA,EAC7B2L,EAAevM,QAAQ,CAAGA,EAC1BuM,EAAe7F,WAAW,CAAGA,EAC7B6F,EAAe5N,QAAQ,CAAGA,EAC1B4N,EAAetO,eAAe,CAAGA,EACjCsO,EAAevO,WAAW,CAAGA,EAC7BuO,EAAexO,QAAQ,CAAGA,EAE1Be,EAAW9B,GAEX,IAAM6M,EAAYhL,IAAa5B,SAAS,EAAE6M,SAASC,eAAeF,SAClEA,CAAAA,GAAaA,EAAUjF,IAAI,CAAC,YAAa,cAAe,cAAe,YACvE,GAAM,CAAE4H,UAAWC,CAAe,CAAEC,MAAOC,CAAW,CAAEC,IAAKC,CAAS,CAAEC,UAAWC,CAAe,CAAEC,QAASC,CAAa,CAAEC,OAAQC,CAAY,CAAE,CAAGb,EAAYc,KAAK,AAClKX,CAAAA,GACAA,CAAAA,EAAgB9T,SAAS,CAACyK,SAAS,CAAG,CAClCiK,IAAK,IACLC,KAAM,GACV,CAAA,EAEAX,IACAA,EAAYhU,SAAS,CAACiL,SAAS,CAAG,OAClC+I,EAAYhU,SAAS,CAACyK,SAAS,CAAG,CAC9BmK,MAAO,IACPC,IAAK,GACT,GAEAX,GACAA,CAAAA,EAAUlU,SAAS,CAACiL,SAAS,CAAG,MAAK,EAErCmJ,GACAA,CAAAA,EAAgBpU,SAAS,CAACiL,SAAS,CAAG,MAAK,EAE3CqJ,GACAA,CAAAA,EAActU,SAAS,CAACiL,SAAS,CAAG,MAAK,EAEzCuJ,GACAA,CAAAA,EAAaxU,SAAS,CAACyK,SAAS,CAAG,CAC/BqK,GAAI,GACR,CAAA,CAER,CACJ,CA6yBJ,EAAGnW,GAAeA,CAAAA,EAAa,CAAC,CAAA,GAMH,IAAMoW,EAAyBpW,EAqCtDqW,EAAK1U,GAEX0U,CAAAA,EAAEjU,aAAa,CAAGiU,EAAEjU,aAAa,EAAIgB,EAAuBhB,aAAa,CACzEiU,EAAEhT,WAAW,CAAGgT,EAAEhT,WAAW,EAAID,EAAuBC,WAAW,CAEnE+S,EAAsBvB,OAAO,CAACwB,EAAEC,KAAK,CAAED,EAAEE,SAAS,CAAEF,EAAEG,MAAM,EAC/B,IAAM/U,EAAoBE,IAG7C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}