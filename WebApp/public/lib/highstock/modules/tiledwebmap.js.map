{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/tiledwebmap\n * @requires highcharts\n *\n * (c) 2009-2025\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/tiledwebmap\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/tiledwebmap\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ tiledwebmap_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Maps/TilesProviders/OpenStreetMap.js\n/* *\n * OpenStreetMap provider, used for tile map services\n * */\n\n/* *\n *\n *  Class\n *\n * */\nclass OpenStreetMap {\n    constructor() {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.defaultCredits = ('Map data &copy2023' +\n            ' <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a>');\n        this.initialProjectionName = 'WebMercator';\n        this.subdomains = ['a', 'b', 'c'];\n        this.themes = {\n            Standard: {\n                url: 'https://tile.openstreetmap.org/{zoom}/{x}/{y}.png',\n                minZoom: 0,\n                maxZoom: 19\n            },\n            Hot: {\n                url: 'https://{s}.tile.openstreetmap.fr/hot/{z}/{x}/{y}.png',\n                minZoom: 0,\n                maxZoom: 19\n            },\n            OpenTopoMap: {\n                url: 'https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png',\n                minZoom: 0,\n                maxZoom: 17,\n                credits: `Map data: &copy; <a href=\"https://www.openstreetmap.org/copyright\">\n                OpenStreetMap</a> contributors, <a href=\"https://viewfinderpanoramas.org\">SRTM</a> \n                | Map style: &copy; <a href=\"https://opentopomap.org\">OpenTopoMap</a> \n                (<a href=\"https://creativecommons.org/licenses/by-sa/3.0/\">CC-BY-SA</a>)`\n            }\n        };\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const TilesProviders_OpenStreetMap = (OpenStreetMap);\n\n;// ./code/es-modules/Maps/TilesProviders/Stamen.js\n/* *\n * Stamen provider, used for tile map services\n * */\n\n/* *\n *\n *  Class\n *\n * */\nclass Stamen {\n    constructor() {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.defaultCredits = ('&copy; Map tiles by <a href=\"https://stamen.com\">Stamen Design</a>,' +\n            ' under <a href=\"https://creativecommons.org/licenses/by/3.0\">CC BY' +\n            ' 3.0</a>. Data by <a href=\"https://openstreetmap.org\">OpenStreetMap' +\n            '</a>, under <a href=\"https://www.openstreetmap.org/copyright\">ODbL</a>');\n        this.initialProjectionName = 'WebMercator';\n        this.subdomains = ['a', 'b', 'c', 'd'];\n        this.themes = {\n            Toner: {\n                url: 'https://stamen-tiles-{s}.a.ssl.fastly.net/toner/{z}/{x}/{y}.png',\n                minZoom: 0,\n                maxZoom: 20\n            },\n            TonerBackground: {\n                url: 'https://stamen-tiles-{s}.a.ssl.fastly.net/toner-background/{z}/{x}/{y}.png',\n                minZoom: 0,\n                maxZoom: 20\n            },\n            TonerLite: {\n                url: 'https://stamen-tiles-{s}.a.ssl.fastly.net/toner-lite/{z}/{x}/{y}.png',\n                minZoom: 0,\n                maxZoom: 20\n            },\n            Terrain: {\n                url: 'https://stamen-tiles-{s}.a.ssl.fastly.net/terrain/{z}/{x}/{y}.png',\n                minZoom: 0,\n                maxZoom: 18\n            },\n            TerrainBackground: {\n                url: 'https://stamen-tiles-{s}.a.ssl.fastly.net/terrain-background/{z}/{x}/{y}.png',\n                minZoom: 0,\n                maxZoom: 18\n            },\n            Watercolor: {\n                url: 'https://stamen-tiles-{s}.a.ssl.fastly.net/watercolor/{z}/{x}/{y}.png',\n                minZoom: 1,\n                maxZoom: 16,\n                credits: ('&copy Map tiles by <a href=\"https://stamen.com\">Stamen' +\n                    ' Design</a>, under <a href=\"https://creativecommons.org/' +\n                    'licenses/by/3.0\">CC BY 3.0</a>. Data by <a href=\"https://' +\n                    'openstreetmap.org\">OpenStreetMap</a>, under <a href=' +\n                    '\"https://creativecommons.org/licenses/by-sa/3.0\">CC BY SA</a>')\n            }\n        };\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const TilesProviders_Stamen = (Stamen);\n\n;// ./code/es-modules/Maps/TilesProviders/LimaLabs.js\n/* *\n * LimaLabs provider, used for tile map services\n * */\n\n/* *\n *\n *  Class\n *\n * */\nclass LimaLabs {\n    constructor() {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.defaultCredits = ('Map data &copy;2023' +\n            ' <a href=\"https://maps.lima-labs.com/\">LimaLabs</a>');\n        this.initialProjectionName = 'WebMercator';\n        this.requiresApiKey = true;\n        this.themes = {\n            Standard: {\n                url: 'https://cdn.lima-labs.com/{zoom}/{x}/{y}.png?api={apikey}',\n                minZoom: 0,\n                maxZoom: 20\n            }\n        };\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const TilesProviders_LimaLabs = (LimaLabs);\n\n;// ./code/es-modules/Maps/TilesProviders/Thunderforest.js\n/* *\n * Thunderforest provider, used for tile map services\n * */\n\n/* *\n *\n *  Class\n *\n * */\nclass Thunderforest {\n    constructor() {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.defaultCredits = ('Maps &copy <a href=\"https://www.thunderforest.com\">Thunderforest</a>' +\n            ', Data &copy; <a href=\"https://www.openstreetmap.org/copyright\">' +\n            'OpenStreetMap contributors</a>');\n        this.initialProjectionName = 'WebMercator';\n        this.requiresApiKey = true;\n        this.subdomains = ['a', 'b', 'c'];\n        this.themes = {\n            OpenCycleMap: {\n                url: 'https://{s}.tile.thunderforest.com/cycle/{z}/{x}/{y}.png?apikey={apikey}',\n                minZoom: 0,\n                maxZoom: 22\n            },\n            Transport: {\n                url: 'https://{s}.tile.thunderforest.com/transport/{z}/{x}/{y}.png?apikey={apikey}',\n                minZoom: 0,\n                maxZoom: 22\n            },\n            TransportDark: {\n                url: 'https://{s}.tile.thunderforest.com/transport-dark/{z}/{x}/{y}.png?apikey={apikey}',\n                minZoom: 0,\n                maxZoom: 22\n            },\n            SpinalMap: {\n                url: 'https://{s}.tile.thunderforest.com/spinal-map/{z}/{x}/{y}.png?apikey={apikey}',\n                minZoom: 0,\n                maxZoom: 22\n            },\n            Landscape: {\n                url: 'https://{s}.tile.thunderforest.com/landscape/{z}/{x}/{y}.png?apikey={apikey}',\n                minZoom: 0,\n                maxZoom: 22\n            },\n            Outdoors: {\n                url: 'https://{s}.tile.thunderforest.com/outdoors/{z}/{x}/{y}.png?apikey={apikey}',\n                minZoom: 0,\n                maxZoom: 22\n            },\n            Pioneer: {\n                url: 'https://{s}.tile.thunderforest.com/pioneer/{z}/{x}/{y}.png?apikey={apikey}',\n                minZoom: 0,\n                maxZoom: 22\n            },\n            MobileAtlas: {\n                url: 'https://{s}.tile.thunderforest.com/mobile-atlas/{z}/{x}/{y}.png?apikey={apikey}',\n                minZoom: 0,\n                maxZoom: 22\n            },\n            Neighbourhood: {\n                url: 'https://{s}.tile.thunderforest.com/neighbourhood/{z}/{x}/{y}.png?apikey={apikey}',\n                minZoom: 0,\n                maxZoom: 22\n            }\n        };\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const TilesProviders_Thunderforest = (Thunderforest);\n\n;// ./code/es-modules/Maps/TilesProviders/Esri.js\n/* *\n * Esri provider, used for tile map services\n * */\n\n/* *\n *\n *  Class\n *\n * */\nclass Esri {\n    constructor() {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.defaultCredits = ('Tiles &copy; Esri &mdash; Source: Esri, DeLorme, NAVTEQ, USGS, ' +\n            ' Intermap, iPC, NRCAN, Esri Japan, METI, Esri China (Hong Kong),' +\n            ' Esri (Thailand), TomTom, 2012');\n        this.initialProjectionName = 'WebMercator';\n        this.themes = {\n            WorldStreetMap: {\n                url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Street_Map/MapServer/tile/{z}/{y}/{x}',\n                minZoom: 0,\n                maxZoom: 20\n            },\n            DeLorme: {\n                url: 'https://server.arcgisonline.com/ArcGIS/rest/services/Specialty/DeLorme_World_Base_Map/MapServer/tile/{z}/{y}/{x}',\n                minZoom: 1,\n                maxZoom: 11,\n                credits: 'Tiles &copy; Esri &mdash; Copyright: &copy;2012 DeLorme'\n            },\n            WorldTopoMap: {\n                url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Topo_Map/MapServer/tile/{z}/{y}/{x}',\n                minZoom: 0,\n                maxZoom: 20,\n                credits: ('Tiles &copy; Esri &mdash; Esri, DeLorme, NAVTEQ, TomTom,' +\n                    ' Intermap, iPC, USGS, FAO, NPS, NRCAN, GeoBase, Kadaster NL,' +\n                    ' Ordnance Survey, Esri Japan, METI, Esri China (Hong Kong),' +\n                    ' and the GIS User Community')\n            },\n            WorldImagery: {\n                url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',\n                minZoom: 0,\n                maxZoom: 20,\n                credits: ('Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS,' +\n                    ' AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP,' +\n                    ' and the GIS User Community')\n            },\n            WorldTerrain: {\n                url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Terrain_Base/MapServer/tile/{z}/{y}/{x}',\n                minZoom: 0,\n                maxZoom: 13,\n                credits: ('Tiles &copy; Esri &mdash; Source: USGS, Esri, TANA, DeLorme,' +\n                    ' and NPS')\n            },\n            WorldShadedRelief: {\n                url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Shaded_Relief/MapServer/tile/{z}/{y}/{x}',\n                minZoom: 0,\n                maxZoom: 13,\n                credits: 'Tiles &copy; Esri &mdash; Source: Esri'\n            },\n            WorldPhysical: {\n                url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Physical_Map/MapServer/tile/{z}/{y}/{x}',\n                minZoom: 0,\n                maxZoom: 8,\n                credits: 'Tiles &copy; Esri &mdash; Source: US National Park Service'\n            },\n            NatGeoWorldMap: {\n                url: 'https://server.arcgisonline.com/ArcGIS/rest/services/NatGeo_World_Map/MapServer/tile/{z}/{y}/{x}',\n                minZoom: 0,\n                maxZoom: 16,\n                credits: ('Tiles &copy; Esri &mdash; National Geographic, Esri,' +\n                    ' DeLorme, NAVTEQ, UNEP-WCMC, USGS, NASA, ESA, METI, NRCAN,' +\n                    ' GEBCO, NOAA, iPC')\n            },\n            WorldGrayCanvas: {\n                url: 'https://server.arcgisonline.com/ArcGIS/rest/services/Canvas/World_Light_Gray_Base/MapServer/tile/{z}/{y}/{x}',\n                minZoom: 0,\n                maxZoom: 16,\n                credits: 'Tiles &copy; Esri &mdash; Esri, DeLorme, NAVTEQ'\n            }\n        };\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const TilesProviders_Esri = (Esri);\n\n;// ./code/es-modules/Maps/TilesProviders/USGS.js\n/* *\n * USGS provider, used for tile map services\n * */\n\n/* *\n *\n *  Class\n *\n * */\nclass USGS {\n    constructor() {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.defaultCredits = ('Tiles courtesy of the <a href=\"https://usgs.gov/\">U.S. Geological' +\n            'Survey</a>');\n        this.initialProjectionName = 'WebMercator';\n        this.themes = {\n            USTopo: {\n                url: 'https://basemap.nationalmap.gov/arcgis/rest/services/USGSTopo/MapServer/tile/{z}/{y}/{x}',\n                minZoom: 0,\n                maxZoom: 20\n            },\n            USImagery: {\n                url: 'https://basemap.nationalmap.gov/arcgis/rest/services/USGSImageryOnly/MapServer/tile/{z}/{y}/{x}',\n                minZoom: 0,\n                maxZoom: 20\n            },\n            USImageryTopo: {\n                url: 'https://basemap.nationalmap.gov/arcgis/rest/services/USGSImageryTopo/MapServer/tile/{z}/{y}/{x}',\n                minZoom: 0,\n                maxZoom: 20\n            }\n        };\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const TilesProviders_USGS = (USGS);\n\n;// ./code/es-modules/Maps/TilesProviders/TilesProviderRegistry.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\n\n\n\n\n\n/* *\n *\n *  Constants\n *\n * */\nconst tilesProviderRegistry = {\n    Esri: TilesProviders_Esri,\n    LimaLabs: TilesProviders_LimaLabs,\n    OpenStreetMap: TilesProviders_OpenStreetMap,\n    Stamen: TilesProviders_Stamen,\n    Thunderforest: TilesProviders_Thunderforest,\n    USGS: TilesProviders_USGS\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const TilesProviderRegistry = (tilesProviderRegistry);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/TiledWebMap/TiledWebMapSeriesDefaults.js\n/* *\n *\n *  (c) 2010-2025 Hubert Kozik, Kamil Musiałowski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A tiledwebmap series allows user to display dynamically joined individual\n * images (tiles) and join them together to create a map.\n *\n * @sample maps/series-tiledwebmap/simple-demo-norway\n *         Simple demo of data for Norway on TiledWebMap\n * @sample maps/series-tiledwebmap/only-twm\n *         OpenStreetMap demo\n *\n * @extends      plotOptions.map\n * @excluding    affectsMapView, allAreas, allowPointSelect, animation,\n * animationLimit, boostBlending, boostThreshold, borderColor, borderWidth,\n * clip, color, colorAxis, colorByPoint, colorIndex, colorKey, colors,\n * cursor, dashStyle, dataLabels, dataParser, dataURL, dragDrop,\n * enableMouseTracking, findNearestPointBy, joinBy, keys, marker,\n * negativeColor, nullColor, nullInteraction, onPoint, point,\n * pointDescriptionFormatter, selected, shadow, showCheckbox,\n * sonification, stickyTracking, tooltip, type\n * @product      highmaps\n * @optionparent plotOptions.tiledwebmap\n */\nconst TiledWebMapSeriesDefaults = {\n    states: {\n        inactive: {\n            enabled: false\n        }\n    }\n};\n/* *\n *\n *  API options\n *\n * */\n/**\n * A `tiledwebmap` series. The [type](#series.tiledwebmap.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @sample maps/series-tiledwebmap/simple-demo-norway\n *         Simple demo of data for Norway on TiledWebMap\n * @sample maps/series-tiledwebmap/only-twm\n *         OpenStreetMap demo\n *\n * @extends   series,plotOptions.tiledwebmap\n * @excluding affectsMapView, allAreas, allowPointSelect, animation,\n * animationLimit, boostBlending, boostThreshold, borderColor, borderWidth,\n * clip, color, colorAxis, colorByPoint, colorIndex, colorKey, colors, cursor,\n * dashStyle, dataLabels, dataParser, dataURL, dragDrop, enableMouseTracking,\n * findNearestPointBy, joinBy, keys, marker, negativeColor, nullColor,\n * nullInteraction, onPoint, point, pointDescriptionFormatter, selected, shadow,\n * showCheckbox, stickyTracking, tooltip, type\n * @product   highmaps\n * @apioption series.tiledwebmap\n */\n/**\n * Provider options for the series.\n *\n * @sample maps/series-tiledwebmap/human-anatomy\n *         Human Anatomy Explorer - Custom TiledWebMap Provider\n *\n * @since 11.1.0\n * @product   highmaps\n * @apioption plotOptions.tiledwebmap.provider\n */\n/**\n * Provider type to pull data (tiles) from.\n *\n * @sample maps/series-tiledwebmap/basic-configuration\n *         Basic configuration for TiledWebMap\n *\n * @type      {string}\n * @since 11.1.0\n * @product   highmaps\n * @apioption plotOptions.tiledwebmap.provider.type\n */\n/**\n * Set a tiles theme. Check the [providers documentation](https://www.highcharts.com/docs/maps/tiledwebmap)\n * for official list of available themes.\n *\n * @sample maps/series-tiledwebmap/europe-timezones\n *         Imagery basemap for Europe\n * @sample maps/series-tiledwebmap/hiking-trail\n *         Topo basemap and MapLine\n *\n * @type      {string}\n * @since 11.1.0\n * @product   highmaps\n * @apioption plotOptions.tiledwebmap.provider.theme\n */\n/**\n * Subdomain required by each provider. Check the [providers documentation](https://www.highcharts.com/docs/maps/tiledwebmap)\n * for available subdomains.\n *\n * @sample maps/series-tiledwebmap/basic-configuration\n *         Basic configuration for TiledWebMap\n *\n * @type      {string}\n * @since 11.1.0\n * @product   highmaps\n * @apioption plotOptions.tiledwebmap.provider.subdomain\n */\n/**\n * API key for providers that require using one.\n *\n * @type      {string}\n * @since 11.1.0\n * @product   highmaps\n * @apioption plotOptions.tiledwebmap.provider.apiKey\n */\n/**\n * Custom URL for providers not specified in [providers type](#series.\n * tiledwebmap.provider.type). Available variables to use in URL are: `{x}`,\n * `{y}`, `{z}` or `{zoom}`. Remember to always specify a projection, when\n * using a custom URL.\n *\n * @sample maps/series-tiledwebmap/custom-url\n *         Custom URL with projection in TiledWebMap configuration\n *\n * @type      {string}\n * @since 11.1.0\n * @product   highmaps\n * @apioption plotOptions.tiledwebmap.provider.url\n */\n''; // Keeps doclets above detached\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const TiledWebMap_TiledWebMapSeriesDefaults = (TiledWebMapSeriesDefaults);\n\n;// ./code/es-modules/Series/TiledWebMap/TiledWebMapSeries.js\n/* *\n *\n *  (c) 2010-2025 Hubert Kozik, Kamil Musiałowski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { map: MapSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\n\n\nconst { addEvent, defined, error, merge, pick, pushUnique } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/** @private */\nfunction onRecommendMapView(e) {\n    const { geoBounds, chart } = e, twm = (chart.options.series || []).filter((s) => s.type === 'tiledwebmap')[0];\n    if (twm && twm.provider && twm.provider.type && !twm.provider.url) {\n        const ProviderDefinition = TilesProviderRegistry[twm.provider.type];\n        if (!defined(ProviderDefinition)) {\n            error('Highcharts warning: Tiles Provider not defined in the ' +\n                'Provider Registry.', false);\n        }\n        else {\n            const def = new ProviderDefinition(), { initialProjectionName: providerProjectionName } = def;\n            if (geoBounds) {\n                const { x1, y1, x2, y2 } = geoBounds;\n                this.recommendedMapView = {\n                    projection: {\n                        name: providerProjectionName,\n                        parallels: [y1, y2],\n                        rotation: [-(x1 + x2) / 2]\n                    }\n                };\n            }\n            else {\n                this.recommendedMapView = {\n                    projection: {\n                        name: providerProjectionName\n                    },\n                    minZoom: 0\n                };\n            }\n            return false;\n        }\n    }\n    return true;\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The series type\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.tiledwebmap\n *\n * @augments Highcharts.Series\n */\nclass TiledWebMapSeries extends MapSeries {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        this.redrawTiles = false;\n        this.isAnimating = false;\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static compose(MapViewClass) {\n        if (pushUnique(composed, 'TiledWebMapSeries')) {\n            addEvent(MapViewClass, 'onRecommendMapView', onRecommendMapView);\n        }\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Convert map coordinates in longitude/latitude to tile\n     * @private\n     * @param  {Highcharts.MapLonLatObject} lonLat\n     *         The map coordinates\n     * @return {Highcharts.PositionObject}\n     *         Array of x and y positions of the tile\n     */\n    lonLatToTile(lonLat, zoom) {\n        const { lon, lat } = lonLat, xTile = Math.floor((lon + 180) / 360 * Math.pow(2, zoom)), yTile = Math.floor((1 - Math.log(Math.tan(lat * Math.PI / 180) +\n            1 / Math.cos(lat * Math.PI / 180)) / Math.PI) /\n            2 * Math.pow(2, zoom));\n        return { x: xTile, y: yTile };\n    }\n    /**\n     * Convert tile to map coordinates in longitude/latitude\n     * @private\n     * @param  xTile\n     *         Position x of the tile\n     * @param  yTile\n     *         Position y of the tile\n     * @param  zTile\n     *         Zoom of the tile\n     * @return {Highcharts.MapLonLatObject}\n     *         The map coordinates\n     */\n    tileToLonLat(xTile, yTile, zTile) {\n        const lon = xTile / Math.pow(2, zTile) * 360 - 180, n = Math.PI - 2 * Math.PI * yTile / Math.pow(2, zTile), lat = (180 /\n            Math.PI * Math.atan(0.5 * (Math.exp(n) - Math.exp(-n))));\n        return { lon, lat };\n    }\n    drawPoints() {\n        const chart = this.chart, mapView = chart.mapView;\n        if (!mapView) {\n            return;\n        }\n        const tiles = (this.tiles = this.tiles || {}), transformGroups = (this.transformGroups = this.transformGroups || []), series = this, options = this.options, provider = options.provider, { zoom } = mapView, lambda = pick((mapView.projection.options.rotation &&\n            mapView.projection.options.rotation[0]), 0), worldSize = 400.979322, tileSize = 256, duration = chart.renderer.forExport ? 0 : 200, animateTiles = (duration) => {\n            for (const zoomKey of Object.keys(tiles)) {\n                if ((parseFloat(zoomKey) === (mapView.zoom < 0 ? 0 :\n                    Math.floor(mapView.zoom))) ||\n                    (series.minZoom &&\n                        (mapView.zoom < 0 ? 0 :\n                            Math.floor(mapView.zoom)) < series.minZoom &&\n                        parseFloat(zoomKey) === series.minZoom) ||\n                    (series.maxZoom &&\n                        (mapView.zoom < 0 ? 0 :\n                            Math.floor(mapView.zoom)) > series.maxZoom &&\n                        parseFloat(zoomKey) === series.maxZoom)) {\n                    Object\n                        .keys(tiles[zoomKey].tiles)\n                        .forEach((key, i) => {\n                        tiles[zoomKey].tiles[key].animate({\n                            opacity: 1\n                        }, {\n                            duration: duration\n                        }, () => {\n                            if (i === Object.keys(tiles[zoomKey].tiles)\n                                .length - 1) {\n                                tiles[zoomKey].isActive = true;\n                            }\n                        });\n                    });\n                }\n                else {\n                    Object\n                        .keys(tiles[zoomKey].tiles)\n                        .forEach((key, i) => {\n                        tiles[zoomKey].tiles[key].animate({\n                            opacity: 0\n                        }, {\n                            duration: duration,\n                            defer: duration / 2\n                        }, () => {\n                            tiles[zoomKey].tiles[key].destroy();\n                            delete tiles[zoomKey].tiles[key];\n                            if (i === Object.keys(tiles[zoomKey].tiles)\n                                .length - 1) {\n                                tiles[zoomKey].isActive = false;\n                                tiles[zoomKey].loaded = false;\n                            }\n                        });\n                    });\n                }\n            }\n        };\n        let zoomFloor = zoom < 0 ? 0 : Math.floor(zoom), maxTile = Math.pow(2, zoomFloor), scale = ((tileSize / worldSize) * Math.pow(2, zoom)) /\n            ((tileSize / worldSize) * Math.pow(2, zoomFloor)), scaledTileSize = scale * 256;\n        if (provider && (provider.type || provider.url)) {\n            if (provider.type && !provider.url) {\n                const ProviderDefinition = TilesProviderRegistry[provider.type];\n                if (!defined(ProviderDefinition)) {\n                    error('Highcharts warning: Tiles Provider \\'' +\n                        provider.type + '\\' not defined in the Provider' +\n                        'Registry.', false);\n                    return;\n                }\n                const def = new ProviderDefinition(), providerProjection = def.initialProjectionName;\n                let theme, subdomain = '';\n                if (provider.theme && defined(def.themes[provider.theme])) {\n                    theme = def.themes[provider.theme];\n                }\n                else {\n                    // If nothing set take first theme\n                    const firstTheme = Object.keys(def.themes)[0];\n                    theme = def.themes[firstTheme];\n                    error('Highcharts warning: The Tiles Provider\\'s Theme \\'' +\n                        provider.theme + '\\' is not defined in the Provider ' +\n                        'definition - falling back to \\'' + firstTheme + '\\'.', false);\n                }\n                if (provider.subdomain &&\n                    def.subdomains &&\n                    def.subdomains.indexOf(provider.subdomain) !== -1) {\n                    subdomain = provider.subdomain;\n                }\n                else if (defined(def.subdomains) &&\n                    // Do not show warning if no subdomain in URL\n                    theme.url.indexOf('{s}') !== -1) {\n                    subdomain = pick(def.subdomains && def.subdomains[0], '');\n                    error('Highcharts warning: The Tiles Provider\\'s Subdomain ' +\n                        '\\'' + provider.subdomain + '\\' is not defined in ' +\n                        'the Provider definition - falling back to \\'' +\n                        subdomain + '\\'.', false);\n                }\n                if (def.requiresApiKey) {\n                    if (provider.apiKey) {\n                        theme.url =\n                            theme.url.replace('{apikey}', provider.apiKey);\n                    }\n                    else {\n                        error('Highcharts warning: The Tiles Provider requires ' +\n                            'API Key to use tiles, use provider.apiKey to ' +\n                            'provide a token.', false);\n                        theme.url = theme.url.replace('?apikey={apikey}', '');\n                    }\n                }\n                provider.url = theme.url\n                    .replace('{s}', subdomain);\n                this.minZoom = theme.minZoom;\n                this.maxZoom = theme.maxZoom;\n                // Add as credits.text, to prevent changing the default mapText\n                const creditsText = pick(chart.userOptions.credits && chart.userOptions.credits.text, 'Highcharts.com ' + pick(theme.credits, def.defaultCredits));\n                if (chart.credits) {\n                    chart.credits.update({\n                        text: creditsText\n                    });\n                }\n                else {\n                    chart.addCredits({\n                        text: creditsText,\n                        style: pick(chart.options.credits?.style, {})\n                    });\n                }\n                if (mapView.projection.options.name !== providerProjection) {\n                    error('Highcharts warning: The set projection is different ' +\n                        'than supported by Tiles Provider.', false);\n                }\n            }\n            else {\n                if (!mapView.projection.options.name) {\n                    error('Highcharts warning: The set projection is different ' +\n                        'than supported by Tiles Provider.', false);\n                }\n            }\n            // If zoom is smaller/higher than supported by provider\n            if (defined(this.minZoom) && zoomFloor < this.minZoom) {\n                zoomFloor = this.minZoom;\n                maxTile = Math.pow(2, zoomFloor);\n                scale = ((tileSize / worldSize) * Math.pow(2, zoom)) /\n                    ((tileSize / worldSize) * Math.pow(2, zoomFloor));\n                scaledTileSize = scale * 256;\n            }\n            else if (defined(this.maxZoom) && zoomFloor > this.maxZoom) {\n                zoomFloor = this.maxZoom;\n                maxTile = Math.pow(2, zoomFloor);\n                scale = ((tileSize / worldSize) * Math.pow(2, zoom)) /\n                    ((tileSize / worldSize) * Math.pow(2, zoomFloor));\n                scaledTileSize = scale * 256;\n            }\n            if (mapView.projection && mapView.projection.def) {\n                // Always true for tile maps\n                mapView.projection.hasCoordinates = true;\n                if (!transformGroups[zoomFloor]) {\n                    transformGroups[zoomFloor] =\n                        chart.renderer.g().add(this.group);\n                }\n                const replaceVariables = (url, x, y, zoom) => url\n                    .replace('{x}', x.toString())\n                    .replace('{y}', y.toString())\n                    .replace('{zoom}', zoom.toString())\n                    .replace('{z}', zoom.toString());\n                const addTile = (x, y, givenZoom, translateX, translateY) => {\n                    const modX = x % maxTile, modY = y % maxTile, tileX = modX < 0 ? modX + maxTile : modX, tileY = modY < 0 ? modY + maxTile : modY;\n                    if (!tiles[`${givenZoom}`].tiles[`${x},${y}`]) {\n                        if (provider.url) {\n                            const url = replaceVariables(provider.url, tileX, tileY, givenZoom);\n                            tiles[givenZoom].loaded = false;\n                            tiles[`${givenZoom}`].tiles[`${x},${y}`] =\n                                chart.renderer.image(url, (x * scaledTileSize) - translateX, (y * scaledTileSize) - translateY, scaledTileSize, scaledTileSize)\n                                    .attr({\n                                    zIndex: 2,\n                                    opacity: 0\n                                })\n                                    .on('load', function () {\n                                    if (provider.onload) {\n                                        provider.onload.apply(this);\n                                    }\n                                    if ((givenZoom ===\n                                        (mapView.zoom < 0 ? 0 :\n                                            Math.floor(mapView.zoom))) ||\n                                        givenZoom === series.minZoom) {\n                                        tiles[`${givenZoom}`]\n                                            .actualTilesCount++;\n                                        // If last tile\n                                        if (tiles[`${givenZoom}`]\n                                            .howManyTiles ===\n                                            tiles[`${givenZoom}`]\n                                                .actualTilesCount) {\n                                            tiles[givenZoom].loaded = true;\n                                            // Fade-in new tiles if there is\n                                            // no other animation\n                                            if (!series.isAnimating) {\n                                                series.redrawTiles = false;\n                                                animateTiles(duration);\n                                            }\n                                            else {\n                                                series.redrawTiles = true;\n                                            }\n                                            tiles[`${givenZoom}`]\n                                                .actualTilesCount = 0;\n                                        }\n                                    }\n                                })\n                                    .add(transformGroups[givenZoom]);\n                            tiles[`${givenZoom}`].tiles[`${x},${y}`].posX = x;\n                            tiles[`${givenZoom}`].tiles[`${x},${y}`].posY = y;\n                            tiles[`${givenZoom}`].tiles[`${x},${y}`]\n                                .originalURL = url;\n                        }\n                    }\n                };\n                // Calculate topLeft and bottomRight corners without normalize\n                const topLeftUnits = mapView.pixelsToProjectedUnits({\n                    x: 0,\n                    y: 0\n                }), topLeftArr = mapView.projection.def.inverse([topLeftUnits.x, topLeftUnits.y]), topLeft = {\n                    lon: topLeftArr[0] - lambda,\n                    lat: topLeftArr[1]\n                }, bottomRightUnits = mapView.pixelsToProjectedUnits({\n                    x: chart.plotWidth,\n                    y: chart.plotHeight\n                }), bottomRightArr = mapView.projection.def.inverse([bottomRightUnits.x, bottomRightUnits.y]), bottomRight = {\n                    lon: bottomRightArr[0] - lambda,\n                    lat: bottomRightArr[1]\n                };\n                // Do not support vertical looping\n                if (topLeft.lat > mapView.projection.maxLatitude ||\n                    bottomRight.lat < -1 * mapView.projection.maxLatitude) {\n                    topLeft.lat = mapView.projection.maxLatitude;\n                    bottomRight.lat = -1 * mapView.projection.maxLatitude;\n                }\n                const startPos = this.lonLatToTile(topLeft, zoomFloor), endPos = this.lonLatToTile(bottomRight, zoomFloor);\n                // Calculate group translations based on first loaded tile\n                const firstTileLonLat = this.tileToLonLat(startPos.x, startPos.y, zoomFloor), units = mapView.projection.def.forward([\n                    firstTileLonLat.lon + lambda,\n                    firstTileLonLat.lat\n                ]), firstTilePx = mapView.projectedUnitsToPixels({\n                    x: units[0], y: units[1]\n                }), translateX = (startPos.x * scaledTileSize - firstTilePx.x), translateY = (startPos.y * scaledTileSize - firstTilePx.y);\n                if (!tiles[`${zoomFloor}`]) {\n                    tiles[`${zoomFloor}`] = {\n                        tiles: {},\n                        isActive: false,\n                        howManyTiles: 0,\n                        actualTilesCount: 0,\n                        loaded: false\n                    };\n                }\n                tiles[`${zoomFloor}`].howManyTiles =\n                    (endPos.x - startPos.x + 1) * (endPos.y - startPos.y + 1);\n                tiles[`${zoomFloor}`].actualTilesCount = 0;\n                for (let x = startPos.x; x <= endPos.x; x++) {\n                    for (let y = startPos.y; y <= endPos.y; y++) {\n                        addTile(x, y, zoomFloor, translateX, translateY);\n                    }\n                }\n            }\n            for (const zoomKey of Object.keys(tiles)) {\n                for (const key of Object.keys(tiles[zoomKey].tiles)) {\n                    if (mapView.projection && mapView.projection.def) {\n                        // Calculate group translations based on first loaded\n                        // tile\n                        const scale = ((tileSize / worldSize) *\n                            Math.pow(2, zoom)) / ((tileSize / worldSize) *\n                            Math.pow(2, parseFloat(zoomKey))), scaledTileSize = scale * 256, firstTile = tiles[zoomKey].tiles[Object.keys(tiles[zoomKey].tiles)[0]], { posX, posY } = tiles[zoomKey].tiles[key];\n                        if (defined(posX) &&\n                            defined(posY) &&\n                            defined(firstTile.posX) &&\n                            defined(firstTile.posY)) {\n                            const firstTileLonLat = this.tileToLonLat(firstTile.posX, firstTile.posY, parseFloat(zoomKey)), units = mapView.projection.def.forward([\n                                firstTileLonLat.lon + lambda,\n                                firstTileLonLat.lat\n                            ]), firstTilePx = mapView.projectedUnitsToPixels({\n                                x: units[0], y: units[1]\n                            }), tilesOffsetX = (firstTile.posX * scaledTileSize) -\n                                firstTilePx.x, tilesOffsetY = (firstTile.posY * scaledTileSize) -\n                                firstTilePx.y;\n                            if (chart.renderer.globalAnimation &&\n                                chart.hasRendered) {\n                                const startX = Number(tiles[zoomKey].tiles[key].attr('x')), startY = Number(tiles[zoomKey].tiles[key].attr('y')), startWidth = Number(tiles[zoomKey].tiles[key].attr('width')), startHeight = Number(tiles[zoomKey].tiles[key].attr('height'));\n                                const step = (now, fx) => {\n                                    tiles[zoomKey].tiles[key].attr({\n                                        x: (startX + (((posX * scaledTileSize) -\n                                            tilesOffsetX - startX) * fx.pos)),\n                                        y: (startY + (((posY * scaledTileSize) -\n                                            tilesOffsetY - startY) * fx.pos)),\n                                        width: (startWidth + ((Math.ceil(scaledTileSize) + 1 -\n                                            startWidth) * fx.pos)),\n                                        height: (startHeight + ((Math.ceil(scaledTileSize) + 1 -\n                                            startHeight) * fx.pos))\n                                    });\n                                };\n                                series.isAnimating = true;\n                                tiles[zoomKey].tiles[key]\n                                    .attr({ animator: 0 })\n                                    .animate({ animator: 1 }, { step }, function () {\n                                    series.isAnimating = false;\n                                    // If animate ended after loading\n                                    // the tiles\n                                    if (series.redrawTiles) {\n                                        series.redrawTiles = false;\n                                        animateTiles(duration);\n                                    }\n                                });\n                                // When dragging or first rendering,\n                                // animation is off\n                            }\n                            else {\n                                // Animate tiles if something broke\n                                if (series.redrawTiles ||\n                                    parseFloat(zoomKey) !== zoomFloor ||\n                                    ((tiles[zoomKey].isActive ||\n                                        parseFloat(zoomKey) === zoomFloor) &&\n                                        Object.keys(tiles[zoomKey].tiles)\n                                            .map((key) => tiles[zoomKey].tiles[key])\n                                            .some((tile) => tile.opacity === 0))) {\n                                    series.redrawTiles = false;\n                                    animateTiles(duration);\n                                }\n                                tiles[zoomKey].tiles[key].attr({\n                                    x: (posX * scaledTileSize) - tilesOffsetX,\n                                    y: (posY * scaledTileSize) - tilesOffsetY,\n                                    width: Math.ceil(scaledTileSize) + 1,\n                                    height: Math.ceil(scaledTileSize) + 1\n                                });\n                            }\n                        }\n                    }\n                }\n            }\n        }\n        else {\n            error('Highcharts warning: Tiles Provider not defined in the ' +\n                'Provider Registry.', false);\n        }\n    }\n    update() {\n        const series = this, { transformGroups } = series, chart = this.chart, mapView = chart.mapView, options = arguments[0], { provider } = options;\n        if (transformGroups) {\n            transformGroups.forEach((group) => {\n                if (Object.keys(group).length !== 0) {\n                    group.destroy();\n                }\n            });\n            this.transformGroups = [];\n        }\n        if (mapView &&\n            !defined(chart.userOptions.mapView?.projection) &&\n            provider &&\n            provider.type) {\n            const ProviderDefinition = TilesProviderRegistry[provider.type];\n            if (ProviderDefinition) {\n                const def = new ProviderDefinition(), { initialProjectionName: providerProjectionName } = def;\n                mapView.update({\n                    projection: {\n                        name: providerProjectionName\n                    }\n                });\n            }\n        }\n        super.update.apply(series, arguments);\n    }\n}\nTiledWebMapSeries.defaultOptions = merge(MapSeries.defaultOptions, TiledWebMap_TiledWebMapSeriesDefaults);\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('tiledwebmap', TiledWebMapSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const TiledWebMap_TiledWebMapSeries = (TiledWebMapSeries);\n\n;// ./code/es-modules/masters/modules/tiledwebmap.js\n\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nG.TilesProviderRegistry = G.TilesProviderRegistry || TilesProviderRegistry;\nTiledWebMap_TiledWebMapSeries.compose(G.MapView);\n/* harmony default export */ const tiledwebmap_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "tiledwebmap_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "TilesProviderRegistry", "<PERSON><PERSON><PERSON>", "constructor", "defaultCredits", "initialProjectionName", "themes", "WorldStreetMap", "url", "minZoom", "max<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "credits", "WorldTopoMap", "WorldImagery", "WorldTerrain", "WorldShadedRelief", "WorldPhysical", "NatGeoWorldMap", "WorldGrayCanvas", "LimaLabs", "requiresApi<PERSON>ey", "Standard", "OpenStreetMap", "subdomains", "Hot", "OpenTopoMap", "Stamen", "Toner", "TonerBackground", "TonerLite", "Terrain", "TerrainBackground", "Watercolor", "Thunderforest", "OpenCycleMap", "Transport", "TransportDark", "SpinalMap", "Landscape", "Outdoors", "Pioneer", "MobileAtlas", "Neighbourhood", "USGS", "USTopo", "USImagery", "USImageryTopo", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "composed", "map", "MapSeries", "seriesTypes", "addEvent", "defined", "error", "merge", "pick", "pushUnique", "onRecommendMapView", "e", "geoBounds", "chart", "twm", "options", "series", "filter", "s", "type", "provider", "ProviderDefinition", "providerProjectionName", "x1", "y1", "x2", "y2", "recommendedMapView", "projection", "name", "parallels", "rotation", "TiledWebMapSeries", "arguments", "redrawTiles", "isAnimating", "compose", "MapViewClass", "lonLatToTile", "lonLat", "zoom", "lon", "lat", "x", "Math", "floor", "pow", "y", "log", "tan", "PI", "cos", "tileToLonLat", "xTile", "yTile", "zTile", "atan", "exp", "drawPoints", "mapView", "tiles", "transformGroups", "lambda", "duration", "renderer", "forExport", "animateTiles", "zoomKey", "keys", "parseFloat", "for<PERSON>ach", "i", "animate", "opacity", "length", "isActive", "defer", "destroy", "loaded", "zoomFloor", "maxTile", "scale", "tileSize", "scaledTileSize", "def", "providerProjection", "theme", "subdomain", "firstTheme", "indexOf", "<PERSON><PERSON><PERSON><PERSON>", "replace", "creditsText", "userOptions", "text", "update", "addCredits", "style", "hasCoordinates", "g", "add", "group", "replaceVariables", "toString", "addTile", "given<PERSON><PERSON>", "translateX", "translateY", "modX", "modY", "tileX", "tileY", "image", "attr", "zIndex", "on", "onload", "apply", "actualTilesCount", "howManyTiles", "posX", "posY", "originalURL", "topLeftUnits", "pixelsToProjectedUnits", "topLeftArr", "inverse", "topLeft", "bottomRightUnits", "plot<PERSON>id<PERSON>", "plotHeight", "bottomRightArr", "bottomRight", "maxLatitude", "startPos", "endPos", "firstTileLonLat", "units", "forward", "firstTilePx", "projectedUnitsToPixels", "firstTile", "tilesOffsetX", "tilesOffsetY", "globalAnimation", "hasRendered", "startX", "Number", "startY", "startWidth", "startHeight", "step", "now", "fx", "pos", "width", "ceil", "height", "animator", "some", "tile", "defaultOptions", "states", "inactive", "enabled", "registerSeriesType", "G", "TiledWebMap_TiledWebMapSeries", "MapView"], "mappings": "CASA,AATA;;;;;;;;CAQC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,iCAAkC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GAC1H,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,iCAAiC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAE9GA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GA4ZxF,IAAME,EAbL,CAC1BC,KAzJJ,MACIC,aAAc,CAMV,IAAI,CAACC,cAAc,CAAI,gKAGvB,IAAI,CAACC,qBAAqB,CAAG,cAC7B,IAAI,CAACC,MAAM,CAAG,CACVC,eAAgB,CACZC,IAAK,mGACLC,QAAS,EACTC,QAAS,EACb,EACAC,QAAS,CACLH,IAAK,mHACLC,QAAS,EACTC,QAAS,GACTE,QAAS,yDACb,EACAC,aAAc,CACVL,IAAK,iGACLC,QAAS,EACTC,QAAS,GACTE,QAAU,4MAId,EACAE,aAAc,CACVN,IAAK,gGACLC,QAAS,EACTC,QAAS,GACTE,QAAU,+IAGd,EACAG,aAAc,CACVP,IAAK,qGACLC,QAAS,EACTC,QAAS,GACTE,QAAU,sEAEd,EACAI,kBAAmB,CACfR,IAAK,sGACLC,QAAS,EACTC,QAAS,GACTE,QAAS,wCACb,EACAK,cAAe,CACXT,IAAK,qGACLC,QAAS,EACTC,QAAS,EACTE,QAAS,4DACb,EACAM,eAAgB,CACZV,IAAK,mGACLC,QAAS,EACTC,QAAS,GACTE,QAAU,iIAGd,EACAO,gBAAiB,CACbX,IAAK,+GACLC,QAAS,EACTC,QAAS,GACTE,QAAS,iDACb,CACJ,CACJ,CACJ,EA+EIQ,SA9QJ,MACIjB,aAAc,CAMV,IAAI,CAACC,cAAc,CAAI,yEAEvB,IAAI,CAACC,qBAAqB,CAAG,cAC7B,IAAI,CAACgB,cAAc,CAAG,CAAA,EACtB,IAAI,CAACf,MAAM,CAAG,CACVgB,SAAU,CACNd,IAAK,4DACLC,QAAS,EACTC,QAAS,EACb,CACJ,CACJ,CACJ,EA4PIa,cAvYJ,MACIpB,aAAc,CAMV,IAAI,CAACC,cAAc,CAAI,yFAEvB,IAAI,CAACC,qBAAqB,CAAG,cAC7B,IAAI,CAACmB,UAAU,CAAG,CAAC,IAAK,IAAK,IAAI,CACjC,IAAI,CAAClB,MAAM,CAAG,CACVgB,SAAU,CACNd,IAAK,oDACLC,QAAS,EACTC,QAAS,EACb,EACAe,IAAK,CACDjB,IAAK,wDACLC,QAAS,EACTC,QAAS,EACb,EACAgB,YAAa,CACTlB,IAAK,mDACLC,QAAS,EACTC,QAAS,GACTE,QAAS,CAAC;AAC1B;AACA;AACA,wFAAwF,CAAC,AAC7E,CACJ,CACJ,CACJ,EAuWIe,OArVJ,MACIxB,aAAc,CAMV,IAAI,CAACC,cAAc,CAAI,iRAIvB,IAAI,CAACC,qBAAqB,CAAG,cAC7B,IAAI,CAACmB,UAAU,CAAG,CAAC,IAAK,IAAK,IAAK,IAAI,CACtC,IAAI,CAAClB,MAAM,CAAG,CACVsB,MAAO,CACHpB,IAAK,kEACLC,QAAS,EACTC,QAAS,EACb,EACAmB,gBAAiB,CACbrB,IAAK,6EACLC,QAAS,EACTC,QAAS,EACb,EACAoB,UAAW,CACPtB,IAAK,uEACLC,QAAS,EACTC,QAAS,EACb,EACAqB,QAAS,CACLvB,IAAK,oEACLC,QAAS,EACTC,QAAS,EACb,EACAsB,kBAAmB,CACfxB,IAAK,+EACLC,QAAS,EACTC,QAAS,EACb,EACAuB,WAAY,CACRzB,IAAK,uEACLC,QAAS,EACTC,QAAS,GACTE,QAAU,0RAKd,CACJ,CACJ,CACJ,EAmSIsB,cA5OJ,MACI/B,aAAc,CAMV,IAAI,CAACC,cAAc,CAAI,qKAGvB,IAAI,CAACC,qBAAqB,CAAG,cAC7B,IAAI,CAACgB,cAAc,CAAG,CAAA,EACtB,IAAI,CAACG,UAAU,CAAG,CAAC,IAAK,IAAK,IAAI,CACjC,IAAI,CAAClB,MAAM,CAAG,CACV6B,aAAc,CACV3B,IAAK,2EACLC,QAAS,EACTC,QAAS,EACb,EACA0B,UAAW,CACP5B,IAAK,+EACLC,QAAS,EACTC,QAAS,EACb,EACA2B,cAAe,CACX7B,IAAK,oFACLC,QAAS,EACTC,QAAS,EACb,EACA4B,UAAW,CACP9B,IAAK,gFACLC,QAAS,EACTC,QAAS,EACb,EACA6B,UAAW,CACP/B,IAAK,+EACLC,QAAS,EACTC,QAAS,EACb,EACA8B,SAAU,CACNhC,IAAK,8EACLC,QAAS,EACTC,QAAS,EACb,EACA+B,QAAS,CACLjC,IAAK,6EACLC,QAAS,EACTC,QAAS,EACb,EACAgC,YAAa,CACTlC,IAAK,kFACLC,QAAS,EACTC,QAAS,EACb,EACAiC,cAAe,CACXnC,IAAK,mFACLC,QAAS,EACTC,QAAS,EACb,CACJ,CACJ,CACJ,EAgLIkC,KAjEJ,MACIzC,aAAc,CAMV,IAAI,CAACC,cAAc,CAAI,8EAEvB,IAAI,CAACC,qBAAqB,CAAG,cAC7B,IAAI,CAACC,MAAM,CAAG,CACVuC,OAAQ,CACJrC,IAAK,2FACLC,QAAS,EACTC,QAAS,EACb,EACAoC,UAAW,CACPtC,IAAK,kGACLC,QAAS,EACTC,QAAS,EACb,EACAqC,cAAe,CACXvC,IAAK,kGACLC,QAAS,EACTC,QAAS,EACb,CACJ,CACJ,CACJ,CAsCA,EASA,IAAIsC,EAAmIxE,EAAoB,KACvJyE,EAAuJzE,EAAoBI,CAAC,CAACoE,GA+JjL,GAAM,CAAEE,SAAAA,CAAQ,CAAE,CAAIlD,IAEhB,CAAEmD,IAAKC,CAAS,CAAE,CAAG,AAACH,IAA2II,WAAW,CAI5K,CAAEC,SAAAA,CAAQ,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAEC,WAAAA,CAAU,CAAE,CAAI3D,IAO/D,SAAS4D,EAAmBC,CAAC,EACzB,GAAM,CAAEC,UAAAA,CAAS,CAAEC,MAAAA,CAAK,CAAE,CAAGF,EAAGG,EAAM,AAACD,CAAAA,EAAME,OAAO,CAACC,MAAM,EAAI,EAAE,AAAD,EAAGC,MAAM,CAAC,AAACC,GAAMA,AAAW,gBAAXA,EAAEC,IAAI,CAAmB,CAAC,EAAE,CAC7G,GAAIL,GAAOA,EAAIM,QAAQ,EAAIN,EAAIM,QAAQ,CAACD,IAAI,EAAI,CAACL,EAAIM,QAAQ,CAAC9D,GAAG,CAAE,CAC/D,IAAM+D,EAAqBtE,CAAqB,CAAC+D,EAAIM,QAAQ,CAACD,IAAI,CAAC,CACnE,GAAKd,EAAQgB,GAIR,CACD,GAAsC,CAAElE,sBAAuBmE,CAAsB,CAAE,CAA3E,IAAID,EAChB,GAAIT,EAAW,CACX,GAAM,CAAEW,GAAAA,CAAE,CAAEC,GAAAA,CAAE,CAAEC,GAAAA,CAAE,CAAEC,GAAAA,CAAE,CAAE,CAAGd,CAC3B,CAAA,IAAI,CAACe,kBAAkB,CAAG,CACtBC,WAAY,CACRC,KAAMP,EACNQ,UAAW,CAACN,EAAIE,EAAG,CACnBK,SAAU,CAAC,CAAER,CAAAA,EAAKE,CAAC,EAAK,EAAE,AAC9B,CACJ,CACJ,MAEI,IAAI,CAACE,kBAAkB,CAAG,CACtBC,WAAY,CACRC,KAAMP,CACV,EACA/D,QAAS,CACb,EAEJ,MAAO,CAAA,CACX,CAxBI+C,EAAM,2EACoB,CAAA,EAwBlC,CACA,MAAO,CAAA,CACX,CAeA,MAAM0B,UAA0B9B,EAC5BjD,aAAc,CAMV,KAAK,IAAIgF,WACT,IAAI,CAACC,WAAW,CAAG,CAAA,EACnB,IAAI,CAACC,WAAW,CAAG,CAAA,CACvB,CAMA,OAAOC,QAAQC,CAAY,CAAE,CACrB5B,EAAWT,EAAU,sBACrBI,EAASiC,EAAc,qBAAsB3B,EAErD,CAcA4B,aAAaC,CAAM,CAAEC,CAAI,CAAE,CACvB,GAAM,CAAEC,IAAAA,CAAG,CAAEC,IAAAA,CAAG,CAAE,CAAGH,EAGrB,MAAO,CAAEI,EAH4BC,KAAKC,KAAK,CAAC,AAACJ,CAAAA,EAAM,GAAE,EAAK,IAAMG,KAAKE,GAAG,CAAC,EAAGN,IAG7DO,EAH6EH,KAAKC,KAAK,CAAC,AAAC,CAAA,EAAID,KAAKI,GAAG,CAACJ,KAAKK,GAAG,CAACP,EAAME,KAAKM,EAAE,CAAG,KAC9I,EAAIN,KAAKO,GAAG,CAACT,EAAME,KAAKM,EAAE,CAAG,MAAQN,KAAKM,EAAE,AAAD,EAC3C,EAAIN,KAAKE,GAAG,CAAC,EAAGN,GACQ,CAChC,CAaAY,aAAaC,CAAK,CAAEC,CAAK,CAAEC,CAAK,CAAE,CAC9B,IAAMd,EAAMY,EAAQT,KAAKE,GAAG,CAAC,EAAGS,GAAS,IAAM,IAAK7H,EAAIkH,KAAKM,EAAE,CAAG,EAAIN,KAAKM,EAAE,CAAGI,EAAQV,KAAKE,GAAG,CAAC,EAAGS,GAEpG,MAAO,CAAEd,IAAAA,EAAKC,IAFqG,IAC/GE,KAAKM,EAAE,CAAGN,KAAKY,IAAI,CAAC,GAAOZ,CAAAA,KAAKa,GAAG,CAAC/H,GAAKkH,KAAKa,GAAG,CAAC,CAAC/H,EAAC,EACtC,CACtB,CACAgI,YAAa,CACT,IAAM7C,EAAQ,IAAI,CAACA,KAAK,CAAE8C,EAAU9C,EAAM8C,OAAO,CACjD,GAAI,CAACA,EACD,OAEJ,IAAMC,EAAS,IAAI,CAACA,KAAK,CAAG,IAAI,CAACA,KAAK,EAAI,CAAC,EAAIC,EAAmB,IAAI,CAACA,eAAe,CAAG,IAAI,CAACA,eAAe,EAAI,EAAE,CAAG7C,EAAS,IAAI,CAA0BI,EAAWL,AAAzB,IAAI,CAACA,OAAO,CAAqBK,QAAQ,CAAE,CAAEoB,KAAAA,CAAI,CAAE,CAAGmB,EAASG,EAAStD,EAAMmD,EAAQ/B,UAAU,CAACb,OAAO,CAACgB,QAAQ,EAC5P4B,EAAQ/B,UAAU,CAACb,OAAO,CAACgB,QAAQ,CAAC,EAAE,CAAG,GAA4CgC,EAAWlD,AAA+B,KAA/BA,EAAMmD,QAAQ,CAACC,SAAS,CAAYC,EAAe,AAACH,IACpJ,IAAK,IAAMI,KAAWjI,OAAOkI,IAAI,CAACR,GAC1B,AAACS,WAAWF,KAAcR,CAAAA,EAAQnB,IAAI,CAAG,EAAI,EAC7CI,KAAKC,KAAK,CAACc,EAAQnB,IAAI,CAAA,GACtBxB,EAAOzD,OAAO,EACX,AAACoG,CAAAA,EAAQnB,IAAI,CAAG,EAAI,EAChBI,KAAKC,KAAK,CAACc,EAAQnB,IAAI,CAAA,EAAKxB,EAAOzD,OAAO,EAC9C8G,WAAWF,KAAanD,EAAOzD,OAAO,EACzCyD,EAAOxD,OAAO,EACX,AAACmG,CAAAA,EAAQnB,IAAI,CAAG,EAAI,EAChBI,KAAKC,KAAK,CAACc,EAAQnB,IAAI,CAAA,EAAKxB,EAAOxD,OAAO,EAC9C6G,WAAWF,KAAanD,EAAOxD,OAAO,CAC1CtB,OACKkI,IAAI,CAACR,CAAK,CAACO,EAAQ,CAACP,KAAK,EACzBU,OAAO,CAAC,CAACtI,EAAKuI,KACfX,CAAK,CAACO,EAAQ,CAACP,KAAK,CAAC5H,EAAI,CAACwI,OAAO,CAAC,CAC9BC,QAAS,CACb,EAAG,CACCV,SAAUA,CACd,EAAG,KACKQ,IAAMrI,OAAOkI,IAAI,CAACR,CAAK,CAACO,EAAQ,CAACP,KAAK,EACrCc,MAAM,CAAG,GACVd,CAAAA,CAAK,CAACO,EAAQ,CAACQ,QAAQ,CAAG,CAAA,CAAG,CAErC,EACJ,GAGAzI,OACKkI,IAAI,CAACR,CAAK,CAACO,EAAQ,CAACP,KAAK,EACzBU,OAAO,CAAC,CAACtI,EAAKuI,KACfX,CAAK,CAACO,EAAQ,CAACP,KAAK,CAAC5H,EAAI,CAACwI,OAAO,CAAC,CAC9BC,QAAS,CACb,EAAG,CACCV,SAAUA,EACVa,MAAOb,EAAW,CACtB,EAAG,KACCH,CAAK,CAACO,EAAQ,CAACP,KAAK,CAAC5H,EAAI,CAAC6I,OAAO,GACjC,OAAOjB,CAAK,CAACO,EAAQ,CAACP,KAAK,CAAC5H,EAAI,CAC5BuI,IAAMrI,OAAOkI,IAAI,CAACR,CAAK,CAACO,EAAQ,CAACP,KAAK,EACrCc,MAAM,CAAG,IACVd,CAAK,CAACO,EAAQ,CAACQ,QAAQ,CAAG,CAAA,EAC1Bf,CAAK,CAACO,EAAQ,CAACW,MAAM,CAAG,CAAA,EAEhC,EACJ,EAGZ,EACIC,EAAYvC,EAAO,EAAI,EAAII,KAAKC,KAAK,CAACL,GAAOwC,EAAUpC,KAAKE,GAAG,CAAC,EAAGiC,GAAYE,EAAQ,AAAEC,iBAAwBtC,KAAKE,GAAG,CAAC,EAAGN,GAC5H,CAAA,AAAC0C,iBAAwBtC,KAAKE,GAAG,CAAC,EAAGiC,EAAS,EAAII,EAAiBF,AAAQ,IAARA,EACxE,GAAI7D,GAAaA,CAAAA,EAASD,IAAI,EAAIC,EAAS9D,GAAG,AAAD,EAAI,CAC7C,GAAI8D,EAASD,IAAI,EAAI,CAACC,EAAS9D,GAAG,CAAE,CAChC,IAAM+D,EAAqBtE,CAAqB,CAACqE,EAASD,IAAI,CAAC,CAC/D,GAAI,CAACd,EAAQgB,GAAqB,YAC9Bf,EAAM,uCACFc,EAASD,IAAI,CADX,yCAEW,CAAA,GAGrB,IAAMiE,EAAM,IAAI/D,EAAsBgE,EAAqBD,EAAIjI,qBAAqB,CAChFmI,EAAOC,EAAY,GACvB,GAAInE,EAASkE,KAAK,EAAIjF,EAAQ+E,EAAIhI,MAAM,CAACgE,EAASkE,KAAK,CAAC,EACpDA,EAAQF,EAAIhI,MAAM,CAACgE,EAASkE,KAAK,CAAC,KAEjC,CAED,IAAME,EAAatJ,OAAOkI,IAAI,CAACgB,EAAIhI,MAAM,CAAC,CAAC,EAAE,CAC7CkI,EAAQF,EAAIhI,MAAM,CAACoI,EAAW,CAC9BlF,EAAM,mDACFc,EAASkE,KAAK,CADZ,kEAEkCE,EAAa,KAAO,CAAA,EAChE,CACIpE,EAASmE,SAAS,EAClBH,EAAI9G,UAAU,EACd8G,AAA+C,KAA/CA,EAAI9G,UAAU,CAACmH,OAAO,CAACrE,EAASmE,SAAS,EACzCA,EAAYnE,EAASmE,SAAS,CAEzBlF,EAAQ+E,EAAI9G,UAAU,GAE3BgH,AAA6B,KAA7BA,EAAMhI,GAAG,CAACmI,OAAO,CAAC,SAClBF,EAAY/E,EAAK4E,EAAI9G,UAAU,EAAI8G,EAAI9G,UAAU,CAAC,EAAE,CAAE,IACtDgC,EAAM,uDACKc,EAASmE,SAAS,CADvB,kEAGFA,EAAY,KAAO,CAAA,IAEvBH,EAAIjH,cAAc,GACdiD,EAASsE,MAAM,CACfJ,EAAMhI,GAAG,CACLgI,EAAMhI,GAAG,CAACqI,OAAO,CAAC,WAAYvE,EAASsE,MAAM,GAGjDpF,EAAM,gHAEkB,CAAA,GACxBgF,EAAMhI,GAAG,CAAGgI,EAAMhI,GAAG,CAACqI,OAAO,CAAC,mBAAoB,MAG1DvE,EAAS9D,GAAG,CAAGgI,EAAMhI,GAAG,CACnBqI,OAAO,CAAC,MAAOJ,GACpB,IAAI,CAAChI,OAAO,CAAG+H,EAAM/H,OAAO,CAC5B,IAAI,CAACC,OAAO,CAAG8H,EAAM9H,OAAO,CAE5B,IAAMoI,EAAcpF,EAAKK,EAAMgF,WAAW,CAACnI,OAAO,EAAImD,EAAMgF,WAAW,CAACnI,OAAO,CAACoI,IAAI,CAAE,kBAAoBtF,EAAK8E,EAAM5H,OAAO,CAAE0H,EAAIlI,cAAc,EAC5I2D,CAAAA,EAAMnD,OAAO,CACbmD,EAAMnD,OAAO,CAACqI,MAAM,CAAC,CACjBD,KAAMF,CACV,GAGA/E,EAAMmF,UAAU,CAAC,CACbF,KAAMF,EACNK,MAAOzF,EAAKK,EAAME,OAAO,CAACrD,OAAO,EAAEuI,MAAO,CAAC,EAC/C,GAEAtC,EAAQ/B,UAAU,CAACb,OAAO,CAACc,IAAI,GAAKwD,GACpC/E,EAAM,wFACmC,CAAA,EAEjD,MAEQ,AAACqD,EAAQ/B,UAAU,CAACb,OAAO,CAACc,IAAI,EAChCvB,EAAM,wFACmC,CAAA,GAkBjD,GAdID,EAAQ,IAAI,CAAC9C,OAAO,GAAKwH,EAAY,IAAI,CAACxH,OAAO,EAEjDyH,EAAUpC,KAAKE,GAAG,CAAC,EADnBiC,EAAY,IAAI,CAACxH,OAAO,EAIxB4H,EAAiBF,AAAQ,IAFzBA,CAAAA,EAAQ,AAAEC,iBAAwBtC,KAAKE,GAAG,CAAC,EAAGN,GACzC,CAAA,AAAC0C,iBAAwBtC,KAAKE,GAAG,CAAC,EAAGiC,EAAS,CAAC,GAG/C1E,EAAQ,IAAI,CAAC7C,OAAO,GAAKuH,EAAY,IAAI,CAACvH,OAAO,GAEtDwH,EAAUpC,KAAKE,GAAG,CAAC,EADnBiC,EAAY,IAAI,CAACvH,OAAO,EAIxB2H,EAAiBF,AAAQ,IAFzBA,CAAAA,EAAQ,AAAEC,iBAAwBtC,KAAKE,GAAG,CAAC,EAAGN,GACzC,CAAA,AAAC0C,iBAAwBtC,KAAKE,GAAG,CAAC,EAAGiC,EAAS,CAAC,GAGpDpB,EAAQ/B,UAAU,EAAI+B,EAAQ/B,UAAU,CAACwD,GAAG,CAAE,CAE9CzB,EAAQ/B,UAAU,CAACsE,cAAc,CAAG,CAAA,EAChC,AAACrC,CAAe,CAACkB,EAAU,EAC3BlB,CAAAA,CAAe,CAACkB,EAAU,CACtBlE,EAAMmD,QAAQ,CAACmC,CAAC,GAAGC,GAAG,CAAC,IAAI,CAACC,KAAK,CAAA,EAEzC,IAAMC,EAAmB,CAAChJ,EAAKqF,EAAGI,EAAGP,IAASlF,EACzCqI,OAAO,CAAC,MAAOhD,EAAE4D,QAAQ,IACzBZ,OAAO,CAAC,MAAO5C,EAAEwD,QAAQ,IACzBZ,OAAO,CAAC,SAAUnD,EAAK+D,QAAQ,IAC/BZ,OAAO,CAAC,MAAOnD,EAAK+D,QAAQ,IAC3BC,EAAU,CAAC7D,EAAGI,EAAG0D,EAAWC,EAAYC,KAC1C,IAAMC,EAAOjE,EAAIqC,EAAS6B,EAAO9D,EAAIiC,EAAS8B,EAAQF,EAAO,EAAIA,EAAO5B,EAAU4B,EAAMG,EAAQF,EAAO,EAAIA,EAAO7B,EAAU6B,EAC5H,GAAI,CAACjD,CAAK,CAAC,CAAC,EAAE6C,EAAU,CAAC,CAAC,CAAC7C,KAAK,CAAC,CAAC,EAAEjB,EAAE,CAAC,EAAEI,EAAE,CAAC,CAAC,EACrC3B,EAAS9D,GAAG,CAAE,CACd,IAAMA,EAAMgJ,EAAiBlF,EAAS9D,GAAG,CAAEwJ,EAAOC,EAAON,EACzD7C,CAAAA,CAAK,CAAC6C,EAAU,CAAC3B,MAAM,CAAG,CAAA,EAC1BlB,CAAK,CAAC,CAAC,EAAE6C,EAAU,CAAC,CAAC,CAAC7C,KAAK,CAAC,CAAC,EAAEjB,EAAE,CAAC,EAAEI,EAAE,CAAC,CAAC,CACpClC,EAAMmD,QAAQ,CAACgD,KAAK,CAAC1J,EAAK,AAACqF,EAAIwC,EAAkBuB,EAAY,AAAC3D,EAAIoC,EAAkBwB,EAAYxB,EAAgBA,GAC3G8B,IAAI,CAAC,CACNC,OAAQ,EACRzC,QAAS,CACb,GACK0C,EAAE,CAAC,OAAQ,WACR/F,EAASgG,MAAM,EACfhG,EAASgG,MAAM,CAACC,KAAK,CAAC,IAAI,EAE1B,CAAA,AAACZ,IACA9C,CAAAA,EAAQnB,IAAI,CAAG,EAAI,EAChBI,KAAKC,KAAK,CAACc,EAAQnB,IAAI,CAAA,GAC3BiE,IAAczF,EAAOzD,OAAO,AAAD,IAC3BqG,CAAK,CAAC,CAAC,EAAE6C,EAAU,CAAC,CAAC,CAChBa,gBAAgB,GAEjB1D,CAAK,CAAC,CAAC,EAAE6C,EAAU,CAAC,CAAC,CACpBc,YAAY,GACb3D,CAAK,CAAC,CAAC,EAAE6C,EAAU,CAAC,CAAC,CAChBa,gBAAgB,GACrB1D,CAAK,CAAC6C,EAAU,CAAC3B,MAAM,CAAG,CAAA,EAGrB9D,EAAOmB,WAAW,CAKnBnB,EAAOkB,WAAW,CAAG,CAAA,GAJrBlB,EAAOkB,WAAW,CAAG,CAAA,EACrBgC,EAAaH,IAKjBH,CAAK,CAAC,CAAC,EAAE6C,EAAU,CAAC,CAAC,CAChBa,gBAAgB,CAAG,GAGpC,GACKlB,GAAG,CAACvC,CAAe,CAAC4C,EAAU,EACvC7C,CAAK,CAAC,CAAC,EAAE6C,EAAU,CAAC,CAAC,CAAC7C,KAAK,CAAC,CAAC,EAAEjB,EAAE,CAAC,EAAEI,EAAE,CAAC,CAAC,CAACyE,IAAI,CAAG7E,EAChDiB,CAAK,CAAC,CAAC,EAAE6C,EAAU,CAAC,CAAC,CAAC7C,KAAK,CAAC,CAAC,EAAEjB,EAAE,CAAC,EAAEI,EAAE,CAAC,CAAC,CAAC0E,IAAI,CAAG1E,EAChDa,CAAK,CAAC,CAAC,EAAE6C,EAAU,CAAC,CAAC,CAAC7C,KAAK,CAAC,CAAC,EAAEjB,EAAE,CAAC,EAAEI,EAAE,CAAC,CAAC,CACnC2E,WAAW,CAAGpK,CACvB,CAER,EAEMqK,EAAehE,EAAQiE,sBAAsB,CAAC,CAChDjF,EAAG,EACHI,EAAG,CACP,GAAI8E,EAAalE,EAAQ/B,UAAU,CAACwD,GAAG,CAAC0C,OAAO,CAAC,CAACH,EAAahF,CAAC,CAAEgF,EAAa5E,CAAC,CAAC,EAAGgF,EAAU,CACzFtF,IAAKoF,CAAU,CAAC,EAAE,CAAG/D,EACrBpB,IAAKmF,CAAU,CAAC,EAAE,AACtB,EAAGG,EAAmBrE,EAAQiE,sBAAsB,CAAC,CACjDjF,EAAG9B,EAAMoH,SAAS,CAClBlF,EAAGlC,EAAMqH,UAAU,AACvB,GAAIC,EAAiBxE,EAAQ/B,UAAU,CAACwD,GAAG,CAAC0C,OAAO,CAAC,CAACE,EAAiBrF,CAAC,CAAEqF,EAAiBjF,CAAC,CAAC,EAAGqF,EAAc,CACzG3F,IAAK0F,CAAc,CAAC,EAAE,CAAGrE,EACzBpB,IAAKyF,CAAc,CAAC,EAAE,AAC1B,EAEIJ,CAAAA,EAAQrF,GAAG,CAAGiB,EAAQ/B,UAAU,CAACyG,WAAW,EAC5CD,EAAY1F,GAAG,CAAG,GAAKiB,EAAQ/B,UAAU,CAACyG,WAAW,AAAD,IACpDN,EAAQrF,GAAG,CAAGiB,EAAQ/B,UAAU,CAACyG,WAAW,CAC5CD,EAAY1F,GAAG,CAAG,GAAKiB,EAAQ/B,UAAU,CAACyG,WAAW,EAEzD,IAAMC,EAAW,IAAI,CAAChG,YAAY,CAACyF,EAAShD,GAAYwD,EAAS,IAAI,CAACjG,YAAY,CAAC8F,EAAarD,GAE1FyD,EAAkB,IAAI,CAACpF,YAAY,CAACkF,EAAS3F,CAAC,CAAE2F,EAASvF,CAAC,CAAEgC,GAAY0D,EAAQ9E,EAAQ/B,UAAU,CAACwD,GAAG,CAACsD,OAAO,CAAC,CACjHF,EAAgB/F,GAAG,CAAGqB,EACtB0E,EAAgB9F,GAAG,CACtB,EAAGiG,EAAchF,EAAQiF,sBAAsB,CAAC,CAC7CjG,EAAG8F,CAAK,CAAC,EAAE,CAAE1F,EAAG0F,CAAK,CAAC,EAAE,AAC5B,GAAI/B,EAAc4B,EAAS3F,CAAC,CAAGwC,EAAiBwD,EAAYhG,CAAC,CAAGgE,EAAc2B,EAASvF,CAAC,CAAGoC,EAAiBwD,EAAY5F,CAAC,AACrH,AAACa,CAAAA,CAAK,CAAC,CAAC,EAAEmB,EAAU,CAAC,CAAC,EACtBnB,CAAAA,CAAK,CAAC,CAAC,EAAEmB,EAAU,CAAC,CAAC,CAAG,CACpBnB,MAAO,CAAC,EACRe,SAAU,CAAA,EACV4C,aAAc,EACdD,iBAAkB,EAClBxC,OAAQ,CAAA,CACZ,CAAA,EAEJlB,CAAK,CAAC,CAAC,EAAEmB,EAAU,CAAC,CAAC,CAACwC,YAAY,CAC9B,AAACgB,CAAAA,EAAO5F,CAAC,CAAG2F,EAAS3F,CAAC,CAAG,CAAA,EAAM4F,CAAAA,EAAOxF,CAAC,CAAGuF,EAASvF,CAAC,CAAG,CAAA,EAC3Da,CAAK,CAAC,CAAC,EAAEmB,EAAU,CAAC,CAAC,CAACuC,gBAAgB,CAAG,EACzC,IAAK,IAAI3E,EAAI2F,EAAS3F,CAAC,CAAEA,GAAK4F,EAAO5F,CAAC,CAAEA,IACpC,IAAK,IAAII,EAAIuF,EAASvF,CAAC,CAAEA,GAAKwF,EAAOxF,CAAC,CAAEA,IACpCyD,EAAQ7D,EAAGI,EAAGgC,EAAW2B,EAAYC,EAGjD,CACA,IAAK,IAAMxC,KAAWjI,OAAOkI,IAAI,CAACR,GAC9B,IAAK,IAAM5H,KAAOE,OAAOkI,IAAI,CAACR,CAAK,CAACO,EAAQ,CAACP,KAAK,EAC9C,GAAID,EAAQ/B,UAAU,EAAI+B,EAAQ/B,UAAU,CAACwD,GAAG,CAAE,CAG9C,IAEuCD,EAAiBF,AAAQ,IAFlD,CAAA,AAAEC,iBACZtC,KAAKE,GAAG,CAAC,EAAGN,GAAU,CAAA,AAAC0C,iBACvBtC,KAAKE,GAAG,CAAC,EAAGuB,WAAWF,GAAQ,CAAC,EAAiC0E,EAAYjF,CAAK,CAACO,EAAQ,CAACP,KAAK,CAAC1H,OAAOkI,IAAI,CAACR,CAAK,CAACO,EAAQ,CAACP,KAAK,CAAC,CAAC,EAAE,CAAC,CAAE,CAAE4D,KAAAA,CAAI,CAAEC,KAAAA,CAAI,CAAE,CAAG7D,CAAK,CAACO,EAAQ,CAACP,KAAK,CAAC5H,EAAI,CACvL,GAAIqE,EAAQmH,IACRnH,EAAQoH,IACRpH,EAAQwI,EAAUrB,IAAI,GACtBnH,EAAQwI,EAAUpB,IAAI,EAAG,CACzB,IAAMe,EAAkB,IAAI,CAACpF,YAAY,CAACyF,EAAUrB,IAAI,CAAEqB,EAAUpB,IAAI,CAAEpD,WAAWF,IAAWsE,EAAQ9E,EAAQ/B,UAAU,CAACwD,GAAG,CAACsD,OAAO,CAAC,CACnIF,EAAgB/F,GAAG,CAAGqB,EACtB0E,EAAgB9F,GAAG,CACtB,EAAGiG,EAAchF,EAAQiF,sBAAsB,CAAC,CAC7CjG,EAAG8F,CAAK,CAAC,EAAE,CAAE1F,EAAG0F,CAAK,CAAC,EAAE,AAC5B,GAAIK,EAAe,AAACD,EAAUrB,IAAI,CAAGrC,EACjCwD,EAAYhG,CAAC,CAAEoG,EAAe,AAACF,EAAUpB,IAAI,CAAGtC,EAChDwD,EAAY5F,CAAC,CACjB,GAAIlC,EAAMmD,QAAQ,CAACgF,eAAe,EAC9BnI,EAAMoI,WAAW,CAAE,CACnB,IAAMC,EAASC,OAAOvF,CAAK,CAACO,EAAQ,CAACP,KAAK,CAAC5H,EAAI,CAACiL,IAAI,CAAC,MAAOmC,EAASD,OAAOvF,CAAK,CAACO,EAAQ,CAACP,KAAK,CAAC5H,EAAI,CAACiL,IAAI,CAAC,MAAOoC,EAAaF,OAAOvF,CAAK,CAACO,EAAQ,CAACP,KAAK,CAAC5H,EAAI,CAACiL,IAAI,CAAC,UAAWqC,EAAcH,OAAOvF,CAAK,CAACO,EAAQ,CAACP,KAAK,CAAC5H,EAAI,CAACiL,IAAI,CAAC,WAC9NsC,EAAO,CAACC,EAAKC,KACf7F,CAAK,CAACO,EAAQ,CAACP,KAAK,CAAC5H,EAAI,CAACiL,IAAI,CAAC,CAC3BtE,EAAIuG,EAAU,AAAC,CAAA,AAAC1B,EAAOrC,EACnB2D,EAAeI,CAAK,EAAKO,EAAGC,GAAG,CACnC3G,EAAIqG,EAAU,AAAC,CAAA,AAAC3B,EAAOtC,EACnB4D,EAAeK,CAAK,EAAKK,EAAGC,GAAG,CACnCC,MAAQN,EAAc,AAACzG,CAAAA,KAAKgH,IAAI,CAACzE,GAAkB,EAC/CkE,CAAS,EAAKI,EAAGC,GAAG,CACxBG,OAASP,EAAe,AAAC1G,CAAAA,KAAKgH,IAAI,CAACzE,GAAkB,EACjDmE,CAAU,EAAKG,EAAGC,GAAG,AAC7B,EACJ,CACA1I,CAAAA,EAAOmB,WAAW,CAAG,CAAA,EACrByB,CAAK,CAACO,EAAQ,CAACP,KAAK,CAAC5H,EAAI,CACpBiL,IAAI,CAAC,CAAE6C,SAAU,CAAE,GACnBtF,OAAO,CAAC,CAAEsF,SAAU,CAAE,EAAG,CAAEP,KAAAA,CAAK,EAAG,WACpCvI,EAAOmB,WAAW,CAAG,CAAA,EAGjBnB,EAAOkB,WAAW,GAClBlB,EAAOkB,WAAW,CAAG,CAAA,EACrBgC,EAAaH,GAErB,EAGJ,KAGQ/C,CAAAA,EAAOkB,WAAW,EAClBmC,WAAWF,KAAaY,GACvB,AAACnB,CAAAA,CAAK,CAACO,EAAQ,CAACQ,QAAQ,EACrBN,WAAWF,KAAaY,CAAQ,GAChC7I,OAAOkI,IAAI,CAACR,CAAK,CAACO,EAAQ,CAACP,KAAK,EAC3B3D,GAAG,CAAC,AAACjE,GAAQ4H,CAAK,CAACO,EAAQ,CAACP,KAAK,CAAC5H,EAAI,EACtC+N,IAAI,CAAC,AAACC,GAASA,AAAiB,IAAjBA,EAAKvF,OAAO,CAAO,IAC3CzD,EAAOkB,WAAW,CAAG,CAAA,EACrBgC,EAAaH,IAEjBH,CAAK,CAACO,EAAQ,CAACP,KAAK,CAAC5H,EAAI,CAACiL,IAAI,CAAC,CAC3BtE,EAAG,AAAC6E,EAAOrC,EAAkB2D,EAC7B/F,EAAG,AAAC0E,EAAOtC,EAAkB4D,EAC7BY,MAAO/G,KAAKgH,IAAI,CAACzE,GAAkB,EACnC0E,OAAQjH,KAAKgH,IAAI,CAACzE,GAAkB,CACxC,EAER,CACJ,CAGZ,MAEI7E,EAAM,2EACoB,CAAA,EAElC,CACAyF,QAAS,CACL,GAAqB,CAAElC,gBAAAA,CAAe,CAAE,CAAzB,IAAI,CAAgChD,EAAQ,IAAI,CAACA,KAAK,CAAE8C,EAAU9C,EAAM8C,OAAO,CAAE5C,EAAUkB,SAAS,CAAC,EAAE,CAAE,CAAEb,SAAAA,CAAQ,CAAE,CAAGL,EASvI,GARI8C,IACAA,EAAgBS,OAAO,CAAC,AAAC+B,IACjBnK,AAA8B,IAA9BA,OAAOkI,IAAI,CAACiC,GAAO3B,MAAM,EACzB2B,EAAMxB,OAAO,EAErB,GACA,IAAI,CAAChB,eAAe,CAAG,EAAE,EAEzBF,GACA,CAACtD,EAAQQ,EAAMgF,WAAW,CAAClC,OAAO,EAAE/B,aACpCR,GACAA,EAASD,IAAI,CAAE,CACf,IAAME,EAAqBtE,CAAqB,CAACqE,EAASD,IAAI,CAAC,CAC/D,GAAIE,EAAoB,CACpB,GAAsC,CAAElE,sBAAuBmE,CAAsB,CAAE,CAA3E,IAAID,EAChBsC,EAAQoC,MAAM,CAAC,CACXnE,WAAY,CACRC,KAAMP,CACV,CACJ,EACJ,CACJ,CACA,KAAK,CAACyE,OAAOsB,KAAK,CAvBH,IAAI,CAuBQpF,UAC/B,CACJ,CACAD,EAAkBiI,cAAc,CAAG1J,EAAML,EAAU+J,cAAc,CAxlB/B,CAC9BC,OAAQ,CACJC,SAAU,CACNC,QAAS,CAAA,CACb,CACJ,CACJ,GAmlBArK,IAA0IsK,kBAAkB,CAAC,cAAerI,GAc5K,IAAMsI,EAAKxN,GACXwN,CAAAA,EAAEvN,qBAAqB,CAAGuN,EAAEvN,qBAAqB,EAAIA,EACrDwN,AAVoEvI,EAUtCI,OAAO,CAACkI,EAAEE,OAAO,EAClB,IAAM5N,EAAoBE,IAG7C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}