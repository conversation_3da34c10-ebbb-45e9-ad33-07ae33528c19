!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/series-on-point
 * @requires highcharts
 *
 * Series on point module
 *
 * (c) 2010-2025 Highsoft AS
 * Author: <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.Point,t._Highcharts.Series,t._Highcharts.SeriesRegistry,t._Highcharts.SVGRenderer):"function"==typeof define&&define.amd?define("highcharts/modules/series-on-point",["highcharts/highcharts"],function(t){return e(t,t.Point,t.Series,t.SeriesRegistry,t.SVGRenderer)}):"object"==typeof exports?exports["highcharts/modules/series-on-point"]=e(t._Highcharts,t._Highcharts.Point,t._Highcharts.Series,t._Highcharts.SeriesRegistry,t._Highcharts.SVGRenderer):t.Highcharts=e(t.Highcharts,t.Highcharts.Point,t.Highcharts.Series,t.Highcharts.SeriesRegistry,t.Highcharts.SVGRenderer)}("undefined"==typeof window?this:window,(t,e,s,i,r)=>(()=>{"use strict";var o,n,h={260:t=>{t.exports=e},512:t=>{t.exports=i},540:t=>{t.exports=r},820:t=>{t.exports=s},944:e=>{e.exports=t}},a={};function l(t){var e=a[t];if(void 0!==e)return e.exports;var s=a[t]={exports:{}};return h[t](s,s.exports,l),s.exports}l.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return l.d(e,{a:e}),e},l.d=(t,e)=>{for(var s in e)l.o(e,s)&&!l.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:e[s]})},l.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var c={};l.d(c,{default:()=>X});var u=l(944),p=l.n(u);!function(t){t.setLength=function(t,e,s){return Array.isArray(t)?(t.length=e,t):t[s?"subarray":"slice"](0,e)},t.splice=function(t,e,s,i,r=[]){if(Array.isArray(t))return Array.isArray(r)||(r=Array.from(r)),{removed:t.splice(e,s,...r),array:t};let o=Object.getPrototypeOf(t).constructor,n=t[i?"subarray":"slice"](e,e+s),h=new o(t.length-s+r.length);return h.set(t.subarray(0,e),0),h.set(r,e),h.set(t.subarray(e+s),e+r.length),{removed:n,array:h}}}(o||(o={}));let{setLength:d,splice:f}=o,{fireEvent:g,objectEach:y,uniqueKey:m}=p(),w=class{constructor(t={}){this.autoId=!t.id,this.columns={},this.id=t.id||m(),this.modified=this,this.rowCount=0,this.versionTag=m();let e=0;y(t.columns||{},(t,s)=>{this.columns[s]=t.slice(),e=Math.max(e,t.length)}),this.applyRowCount(e)}applyRowCount(t){this.rowCount=t,y(this.columns,(e,s)=>{e.length!==t&&(this.columns[s]=d(e,t))})}deleteRows(t,e=1){if(e>0&&t<this.rowCount){let s=0;y(this.columns,(i,r)=>{this.columns[r]=f(i,t,e).array,s=i.length}),this.rowCount=s}g(this,"afterDeleteRows",{rowIndex:t,rowCount:e}),this.versionTag=m()}getColumn(t,e){return this.columns[t]}getColumns(t,e){return(t||Object.keys(this.columns)).reduce((t,e)=>(t[e]=this.columns[e],t),{})}getRow(t,e){return(e||Object.keys(this.columns)).map(e=>this.columns[e]?.[t])}setColumn(t,e=[],s=0,i){this.setColumns({[t]:e},s,i)}setColumns(t,e,s){let i=this.rowCount;y(t,(t,e)=>{this.columns[e]=t.slice(),i=t.length}),this.applyRowCount(i),s?.silent||(g(this,"afterSetColumns"),this.versionTag=m())}setRow(t,e=this.rowCount,s,i){let{columns:r}=this,o=s?this.rowCount+1:e+1;y(t,(t,n)=>{let h=r[n]||i?.addColumns!==!1&&Array(o);h&&(s?h=f(h,e,0,!0,[t]).array:h[e]=t,r[n]=h)}),o>this.rowCount&&this.applyRowCount(o),i?.silent||(g(this,"afterSetRows"),this.versionTag=m())}};var C=l(260),b=l.n(C);l(820);var x=l(512),P=l.n(x),R=l(540),S=l.n(R);let{composed:v}=p(),{bubble:H}=P().seriesTypes,{addEvent:A,defined:_,find:O,isNumber:G,pushUnique:j}=p();!function(t){t.compose=function(t,s){if(j(v,"SeriesOnPoint")){let{chartGetZData:i,seriesAfterInit:r,seriesAfterRender:o,seriesGetCenter:n,seriesShowOrHide:h,seriesTranslate:a}=e.prototype;t.types.pie.prototype.onPointSupported=!0,A(t,"afterInit",r),A(t,"afterRender",o),A(t,"afterGetCenter",n),A(t,"hide",h),A(t,"show",h),A(t,"translate",a),A(s,"beforeRender",i),A(s,"beforeRedraw",i)}return t};class e{constructor(t){this.getColumn=H.prototype.getColumn,this.getRadii=H.prototype.getRadii,this.getRadius=H.prototype.getRadius,this.getPxExtremes=H.prototype.getPxExtremes,this.getZExtremes=H.prototype.getZExtremes,this.chart=t.chart,this.series=t,this.options=t.options.onPoint}drawConnector(){this.connector||(this.connector=this.series.chart.renderer.path().addClass("highcharts-connector-seriesonpoint").attr({zIndex:-1}).add(this.series.markerGroup));let t=this.getConnectorAttributes();t&&this.connector.animate(t)}getConnectorAttributes(){let t=this.series.chart,e=this.options;if(!e)return;let s=e.connectorOptions||{},i=e.position,r=t.get(e.id);if(!(r instanceof b())||!i||!_(r.plotX)||!_(r.plotY))return;let o=_(i.x)?i.x:r.plotX,n=_(i.y)?i.y:r.plotY,h=o+(i.offsetX||0),a=n+(i.offsetY||0),l=s.width||1,c=s.stroke||this.series.color,u=s.dashstyle,p={d:S().prototype.crispLine([["M",o,n],["L",h,a]],l),"stroke-width":l};return t.styledMode||(p.stroke=c,p.dashstyle=u),p}seriesAfterInit(){this.onPointSupported&&this.options.onPoint&&(this.bubblePadding=!0,this.useMapGeometry=!0,this.onPoint=new e(this))}seriesAfterRender(){delete this.chart.bubbleZExtremes,this.onPoint&&this.onPoint.drawConnector()}seriesGetCenter(t){let e=this.options.onPoint,s=t.positions;if(e){let t=this.chart.get(e.id);t instanceof b()&&_(t.plotX)&&_(t.plotY)&&(s[0]=t.plotX,s[1]=t.plotY);let i=e.position;i&&(_(i.x)&&(s[0]=i.x),_(i.y)&&(s[1]=i.y),i.offsetX&&(s[0]+=i.offsetX),i.offsetY&&(s[1]+=i.offsetY))}let i=this.radii&&this.radii[this.index];G(i)&&(s[2]=2*i),t.positions=s}seriesShowOrHide(){let t=this.chart.series;this.points?.forEach(e=>{let s=O(t,t=>{let s=((t.onPoint||{}).options||{}).id;return!!s&&s===e.id});s&&s.setVisible(!s.visible,!1)})}seriesTranslate(){this.onPoint&&(this.onPoint.getRadii(),this.radii=this.onPoint.radii)}chartGetZData(){let t=[];this.series.forEach(e=>{let s=e.options.onPoint;t.push(s?.z??null)});let e=new w({columns:{z:t}});this.series.forEach(t=>{t.onPoint&&(t.onPoint.dataTable=t.dataTable=e)})}}t.Additions=e}(n||(n={}));let E=n,T=p();E.compose(T.Series,T.Chart);let X=p();return c.default})());