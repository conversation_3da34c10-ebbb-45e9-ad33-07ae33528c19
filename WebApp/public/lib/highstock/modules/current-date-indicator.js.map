{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts Gantt JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/current-date-indicator\n * @requires highcharts\n *\n * CurrentDateIndicator\n *\n * (c) 2010-2025 Lars A. <PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/current-date-indicator\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/current-date-indicator\"] = factory(root[\"_Highcharts\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ current_date_indicator_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Extensions/CurrentDateIndication.js\n/* *\n *\n *  (c) 2016-2025 Highsoft AS\n *\n *  Author: Lars A. V. Cabrera\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent, merge, pushUnique, wrap } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\n/**\n * Show an indicator on the axis for the current date and time. Can be a\n * boolean or a configuration object similar to\n * [xAxis.plotLines](#xAxis.plotLines).\n *\n * @sample gantt/current-date-indicator/demo\n *         Current date indicator enabled\n * @sample gantt/current-date-indicator/object-config\n *         Current date indicator with custom options\n *\n * @declare   Highcharts.CurrentDateIndicatorOptions\n * @type      {boolean|CurrentDateIndicatorOptions}\n * @default   true\n * @extends   xAxis.plotLines\n * @excluding value\n * @product   gantt\n * @apioption xAxis.currentDateIndicator\n */\nconst defaultOptions = {\n    color: \"#ccd3ff\" /* Palette.highlightColor20 */,\n    width: 2,\n    /**\n     * @declare Highcharts.AxisCurrentDateIndicatorLabelOptions\n     */\n    label: {\n        /**\n         * Format of the label. This options is passed as the first argument to\n         * [dateFormat](/class-reference/Highcharts.Time#dateFormat) function.\n         *\n         * @type      {string|Intl.DateTimeFormatOptions}\n         * @product   gantt\n         * @apioption xAxis.currentDateIndicator.label.format\n         */\n        format: '%[abdYHM]',\n        formatter: function (value, format) {\n            return this.axis.chart.time.dateFormat(format || '', value, true);\n        },\n        rotation: 0,\n        /**\n         * @type {Highcharts.CSSObject}\n         */\n        style: {\n            /** @internal */\n            fontSize: '0.7em'\n        }\n    }\n};\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction compose(AxisClass, PlotLineOrBandClass) {\n    if (pushUnique(composed, 'CurrentDateIndication')) {\n        addEvent(AxisClass, 'afterSetOptions', onAxisAfterSetOptions);\n        addEvent(PlotLineOrBandClass, 'render', onPlotLineOrBandRender);\n        wrap(PlotLineOrBandClass.prototype, 'getLabelText', wrapPlotLineOrBandGetLabelText);\n    }\n}\n/**\n * @private\n */\nfunction onAxisAfterSetOptions() {\n    const options = this.options, cdiOptions = options.currentDateIndicator;\n    if (cdiOptions) {\n        const plotLineOptions = typeof cdiOptions === 'object' ?\n            merge(defaultOptions, cdiOptions) :\n            merge(defaultOptions);\n        plotLineOptions.value = Date.now();\n        plotLineOptions.className = 'highcharts-current-date-indicator';\n        if (!options.plotLines) {\n            options.plotLines = [];\n        }\n        options.plotLines.push(plotLineOptions);\n    }\n}\n/**\n * @private\n */\nfunction onPlotLineOrBandRender() {\n    // If the label already exists, update its text\n    if (this.label) {\n        this.label.attr({\n            text: this.getLabelText(this.options.label)\n        });\n    }\n}\n/**\n * @private\n */\nfunction wrapPlotLineOrBandGetLabelText(defaultMethod, defaultLabelOptions) {\n    const options = this.options;\n    if (options &&\n        options.className &&\n        options.className.indexOf('highcharts-current-date-indicator') !== -1 &&\n        options.label &&\n        typeof options.label.formatter === 'function') {\n        options.value = Date.now();\n        return options.label.formatter\n            .call(this, options.value, options.label.format);\n    }\n    return defaultMethod.call(this, defaultLabelOptions);\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst CurrentDateIndication = {\n    compose\n};\n/* harmony default export */ const Extensions_CurrentDateIndication = (CurrentDateIndication);\n\n;// ./code/es-modules/masters/modules/current-date-indicator.js\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nExtensions_CurrentDateIndication.compose(G.Axis, G.PlotLineOrBand);\n/* harmony default export */ const current_date_indicator_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "current_date_indicator_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "composed", "addEvent", "merge", "pushUnique", "wrap", "defaultOptions", "color", "width", "label", "format", "formatter", "value", "axis", "chart", "time", "dateFormat", "rotation", "style", "fontSize", "onAxisAfterSetOptions", "options", "cdiOptions", "currentDateIndicator", "plotLineOptions", "Date", "now", "className", "plotLines", "push", "onPlotLineOrBandRender", "attr", "text", "getLabelText", "wrapPlotLineOrBandGetLabelText", "defaultMethod", "defaultLabelOptions", "indexOf", "G", "Extensions_CurrentDateIndication", "compose", "AxisClass", "PlotLineOrBandClass", "Axis", "PlotLineOrBand"], "mappings": "CAWA,AAXA;;;;;;;;;;CAUC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,EACrC,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,4CAA6C,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAM,GAC9G,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,4CAA4C,CAAGD,EAAQD,EAAK,WAAc,EAElFA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CACjD,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,AAACC,GACnC,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIE,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaX,OAAO,CAG5B,IAAIC,EAASO,CAAwB,CAACE,EAAS,CAAG,CAGjDV,QAAS,CAAC,CACX,EAMA,OAHAO,CAAmB,CAACG,EAAS,CAACT,EAAQA,EAAOD,OAAO,CAAES,GAG/CR,EAAOD,OAAO,AACtB,CAMCS,EAAoBI,CAAC,CAAG,AAACZ,IACxB,IAAIa,EAASb,GAAUA,EAAOc,UAAU,CACvC,IAAOd,EAAO,OAAU,CACxB,IAAOA,EAER,OADAQ,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAAChB,EAASkB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACpB,EAASmB,IAC5EE,OAAOC,cAAc,CAACtB,EAASmB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAerH,GAAM,CAAEE,SAAAA,CAAQ,CAAE,CAAID,IAEhB,CAAEE,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,WAAAA,CAAU,CAAEC,KAAAA,CAAI,CAAE,CAAIL,IAwBzCM,EAAiB,CACnBC,MAAO,UACPC,MAAO,EAIPC,MAAO,CASHC,OAAQ,YACRC,UAAW,SAAUC,CAAK,CAAEF,CAAM,EAC9B,OAAO,IAAI,CAACG,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,UAAU,CAACN,GAAU,GAAIE,EAAO,CAAA,EAChE,EACAK,SAAU,EAIVC,MAAO,CAEHC,SAAU,OACd,CACJ,CACJ,EAmBA,SAASC,IACL,IAAMC,EAAU,IAAI,CAACA,OAAO,CAAEC,EAAaD,EAAQE,oBAAoB,CACvE,GAAID,EAAY,CACZ,IAAME,EAAkB,AAAsB,UAAtB,OAAOF,EAC3BnB,EAAMG,EAAgBgB,GACtBnB,EAAMG,EACVkB,CAAAA,EAAgBZ,KAAK,CAAGa,KAAKC,GAAG,GAChCF,EAAgBG,SAAS,CAAG,oCACxB,AAACN,EAAQO,SAAS,EAClBP,CAAAA,EAAQO,SAAS,CAAG,EAAE,AAAD,EAEzBP,EAAQO,SAAS,CAACC,IAAI,CAACL,EAC3B,CACJ,CAIA,SAASM,IAED,IAAI,CAACrB,KAAK,EACV,IAAI,CAACA,KAAK,CAACsB,IAAI,CAAC,CACZC,KAAM,IAAI,CAACC,YAAY,CAAC,IAAI,CAACZ,OAAO,CAACZ,KAAK,CAC9C,EAER,CAIA,SAASyB,EAA+BC,CAAa,CAAEC,CAAmB,EACtE,IAAMf,EAAU,IAAI,CAACA,OAAO,QAC5B,AAAIA,GACAA,EAAQM,SAAS,EACjBN,AAAmE,KAAnEA,EAAQM,SAAS,CAACU,OAAO,CAAC,sCAC1BhB,EAAQZ,KAAK,EACb,AAAmC,YAAnC,OAAOY,EAAQZ,KAAK,CAACE,SAAS,EAC9BU,EAAQT,KAAK,CAAGa,KAAKC,GAAG,GACjBL,EAAQZ,KAAK,CAACE,SAAS,CACzBf,IAAI,CAAC,IAAI,CAAEyB,EAAQT,KAAK,CAAES,EAAQZ,KAAK,CAACC,MAAM,GAEhDyB,EAAcvC,IAAI,CAAC,IAAI,CAAEwC,EACpC,CAgBA,IAAME,EAAKtC,IACXuC,AAX8B,CAAA,CAC1BC,QAzDJ,SAAiBC,CAAS,CAAEC,CAAmB,EACvCtC,EAAWH,EAAU,2BACrBC,EAASuC,EAAW,kBAAmBrB,GACvClB,EAASwC,EAAqB,SAAUZ,GACxCzB,EAAKqC,EAAoBhD,SAAS,CAAE,eAAgBwC,GAE5D,CAoDA,CAAA,EASiCM,OAAO,CAACF,EAAEK,IAAI,CAAEL,EAAEM,cAAc,EACpC,IAAM9C,EAA+BE,IAGxD,OADYH,EAAoB,OAAU,AAE3C,CAAA"}