{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * Organization chart series type\n * @module highcharts/modules/organization\n * @requires highcharts\n * @requires highcharts/modules/sankey\n *\n * (c) 2019-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"SVGElement\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/organization\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"],amd1[\"SVGElement\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/organization\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"SVGElement\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"SVGElement\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__28__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 28:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__28__;\n\n/***/ }),\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ organization_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/Organization/OrganizationPoint.js\n/* *\n *\n *  Organization chart module\n *\n *  (c) 2018-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sankey: { prototype: { pointClass: SankeyPointClass } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { defined, find, pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Get columns offset including all sibling and cousins etc.\n * @private\n */\nfunction getOffset(node) {\n    let offset = node.linksFrom.length;\n    node.linksFrom.forEach((link) => {\n        if (link.id === link.toNode.linksTo[0].id) {\n            // Node has children, that hangs directly from it:\n            offset += getOffset(link.toNode);\n        }\n        else {\n            // If the node hangs from multiple parents, and this is not\n            // the last one, ignore it:\n            offset--;\n        }\n    });\n    return offset;\n}\n/* *\n *\n *  Class\n *\n * */\nclass OrganizationPoint extends SankeyPointClass {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    constructor(series, options, x) {\n        super(series, options, x);\n        if (!this.isNode) {\n            this.dataLabelOnNull = true;\n            this.formatPrefix = 'link';\n        }\n    }\n    /**\n     * All nodes in an org chart are equal width.\n     * @private\n     */\n    getSum() {\n        return 1;\n    }\n    /**\n     * Set node.column for hanging layout\n     * @private\n     */\n    setNodeColumn() {\n        super.setNodeColumn();\n        const node = this, fromNode = node.getFromNode().fromNode;\n        // Hanging layout\n        if (\n        // Not defined by user\n        !defined(node.options.column) &&\n            // Has links to\n            node.linksTo.length !== 0 &&\n            // And parent uses hanging layout\n            fromNode &&\n            fromNode.options.layout === 'hanging') {\n            let i = -1, link;\n            // Default all children of the hanging node\n            // to have hanging layout\n            node.options.layout = pick(node.options.layout, 'hanging');\n            node.hangsFrom = fromNode;\n            find(fromNode.linksFrom, (link, index) => {\n                const found = link.toNode === node;\n                if (found) {\n                    i = index;\n                }\n                return found;\n            });\n            // For all siblings' children (recursively)\n            // increase the column offset to prevent overlapping\n            for (let j = 0; j < fromNode.linksFrom.length; ++j) {\n                link = fromNode.linksFrom[j];\n                if (link.toNode.id === node.id) {\n                    // Break\n                    j = fromNode.linksFrom.length;\n                }\n                else {\n                    i += getOffset(link.toNode);\n                }\n            }\n            node.column = (node.column || 0) + i;\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Organization_OrganizationPoint = (OrganizationPoint);\n\n;// ./code/es-modules/Series/Organization/OrganizationSeriesDefaults.js\n/* *\n *\n *  Organization chart module\n *\n *  (c) 2018-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * An organization chart is a diagram that shows the structure of an\n * organization and the relationships and relative ranks of its parts and\n * positions.\n *\n * @sample       highcharts/demo/organization-chart/\n *               Organization chart\n * @sample       highcharts/series-organization/horizontal/\n *               Horizontal organization chart\n * @sample       highcharts/series-organization/borderless\n *               Borderless design\n * @sample       highcharts/series-organization/center-layout\n *               Centered layout\n *\n * @extends      plotOptions.sankey\n * @excluding    allowPointSelect, curveFactor, dataSorting\n * @since        7.1.0\n * @product      highcharts\n * @requires     modules/organization\n * @optionparent plotOptions.organization\n */\nconst OrganizationSeriesDefaults = {\n    /**\n     * The border color of the node cards.\n     *\n     * @type {Highcharts.ColorString}\n     */\n    borderColor: \"#666666\" /* Palette.neutralColor60 */,\n    /**\n     * The border radius of the node cards.\n     *\n     * @private\n     */\n    borderRadius: 3,\n    /**\n     * Radius for the rounded corners of the links between nodes. This\n     * option is now deprecated, and moved to\n     * [link.radius](#plotOptions.organization.link.radius).\n     *\n     * @sample   highcharts/series-organization/link-options\n     *           Square links\n     *\n     * @deprecated\n     * @apioption series.organization.linkRadius\n     */\n    /**\n     * Link Styling options\n     * @since 10.3.0\n     * @product highcharts\n     */\n    link: {\n        /**\n         * Modifier of the shape of the curved link. Works best for values\n         * between 0 and 1, where 0 is a straight line, and 1 is a shape\n         * close to the default one.\n         *\n         * @default 0.5\n         * @type {number}\n         * @since 10.3.0\n         * @product highcharts\n         * @apioption series.organization.link.offset\n         */\n        /**\n         * The color of the links between nodes.\n         *\n         * @type {Highcharts.ColorString}\n         */\n        color: \"#666666\" /* Palette.neutralColor60 */,\n        /**\n         * The line width of the links connecting nodes, in pixels.\n         *\n         * @sample   highcharts/series-organization/link-options\n         *           Square links\n         */\n        lineWidth: 1,\n        /**\n         * Radius for the rounded corners of the links between nodes.\n         * Works for `default` link type.\n         *\n         * @sample   highcharts/series-organization/link-options\n         *           Square links\n         */\n        radius: 10,\n        /**\n         * Type of the link shape.\n         *\n         * @sample   highcharts/series-organization/different-link-types\n         *           Different link types\n         *\n         * @declare Highcharts.OrganizationLinkTypeValue\n         * @type {'default' | 'curved' | 'straight'}\n         * @default 'default'\n         * @product highcharts\n         */\n        type: 'default'\n    },\n    borderWidth: 1,\n    /**\n     * @declare Highcharts.SeriesOrganizationDataLabelsOptionsObject\n     *\n     * @private\n     */\n    dataLabels: {\n        /* eslint-disable valid-jsdoc */\n        /**\n         * A callback for defining the format for _nodes_ in the\n         * organization chart. The `nodeFormat` option takes precedence\n         * over `nodeFormatter`.\n         *\n         * In an organization chart, the `nodeFormatter` is a quite complex\n         * function of the available options, striving for a good default\n         * layout of cards with or without images. In organization chart,\n         * the data labels come with `useHTML` set to true, meaning they\n         * will be rendered as true HTML above the SVG.\n         *\n         * @sample highcharts/series-organization/datalabels-nodeformatter\n         *         Modify the default label format output\n         *\n         * @type  {Highcharts.SeriesSankeyDataLabelsFormatterCallbackFunction}\n         * @since 6.0.2\n         */\n        nodeFormatter: function () {\n            const outerStyle = {\n                width: '100%',\n                height: '100%',\n                display: 'flex',\n                'flex-direction': 'row',\n                'align-items': 'center',\n                'justify-content': 'center'\n            }, imageStyle = {\n                'max-height': '100%',\n                'border-radius': '50%'\n            }, innerStyle = {\n                width: '100%',\n                padding: 0,\n                'text-align': 'center',\n                'white-space': 'normal'\n            }, nameStyle = {\n                margin: 0\n            }, titleStyle = {\n                margin: 0\n            }, descriptionStyle = {\n                opacity: 0.75,\n                margin: '5px'\n            };\n            // eslint-disable-next-line valid-jsdoc\n            /**\n             * @private\n             */\n            function styleAttr(style) {\n                return Object.keys(style).reduce(function (str, key) {\n                    return str + key + ':' + style[key] + ';';\n                }, 'style=\"') + '\"';\n            }\n            const { description, image, title } = this.point;\n            if (image) {\n                imageStyle['max-width'] = '30%';\n                innerStyle.width = '70%';\n            }\n            // PhantomJS doesn't support flex, roll back to absolute\n            // positioning\n            if (this.series.chart.renderer.forExport) {\n                outerStyle.display = 'block';\n                innerStyle.position = 'absolute';\n                innerStyle.left = image ? '30%' : 0;\n                innerStyle.top = 0;\n            }\n            let html = '<div ' + styleAttr(outerStyle) + '>';\n            if (image) {\n                html += '<img src=\"' + image + '\" ' +\n                    styleAttr(imageStyle) + '>';\n            }\n            html += '<div ' + styleAttr(innerStyle) + '>';\n            if (this.point.name) {\n                html += '<h4 ' + styleAttr(nameStyle) + '>' +\n                    this.point.name + '</h4>';\n            }\n            if (title) {\n                html += '<p ' + styleAttr(titleStyle) + '>' +\n                    (title || '') + '</p>';\n            }\n            if (description) {\n                html += '<p ' + styleAttr(descriptionStyle) + '>' +\n                    description + '</p>';\n            }\n            html += '</div>' +\n                '</div>';\n            return html;\n        },\n        /* eslint-enable valid-jsdoc */\n        style: {\n            /** @internal */\n            fontWeight: 'normal',\n            /** @internal */\n            fontSize: '0.9em',\n            /** @internal */\n            textAlign: 'left'\n        },\n        useHTML: true,\n        linkTextPath: {\n            attributes: {\n                startOffset: '95%',\n                textAnchor: 'end'\n            }\n        }\n    },\n    /**\n     * The indentation in pixels of hanging nodes, nodes which parent has\n     * [layout](#series.organization.nodes.layout) set to `hanging`.\n     *\n     * @private\n     */\n    hangingIndent: 20,\n    /**\n     * Defines the indentation of a `hanging` layout parent's children.\n     * Possible options:\n     *\n     * - `inherit` (default): Only the first child adds the indentation,\n     * children of a child with indentation inherit the indentation.\n     * - `cumulative`: All children of a child with indentation add its\n     * own indent. The option may cause overlapping of nodes.\n     * Then use `shrink` option:\n     * - `shrink`: Nodes shrink by the\n     * [hangingIndent](#plotOptions.organization.hangingIndent)\n     * value until they reach the\n     * [minNodeLength](#plotOptions.organization.minNodeLength).\n     *\n     * @sample highcharts/series-organization/hanging-cumulative\n     *         Every indent increases the indentation\n     *\n     * @sample highcharts/series-organization/hanging-shrink\n     *         Every indent decreases the nodes' width\n     *\n     * @type {Highcharts.OrganizationHangingIndentTranslationValue}\n     * @since 10.0.0\n     * @default inherit\n     *\n     * @private\n     */\n    hangingIndentTranslation: 'inherit',\n    /**\n     * Whether links connecting hanging nodes should be drawn on the left\n     * or right side. Useful for RTL layouts.\n     * **Note:** Only effects inverted charts (vertical layout).\n     *\n     * @sample highcharts/series-organization/hanging-side\n     *         Nodes hanging from right side.\n     *\n     * @type {'left'|'right'}\n     * @since 11.3.0\n     * @default 'left'\n     */\n    hangingSide: 'left',\n    /**\n     *\n     * The color of the links between nodes. This option is moved to\n     * [link.color](#plotOptions.organization.link.color).\n     *\n     * @type {Highcharts.ColorString}\n     * @deprecated\n     * @apioption series.organization.linkColor\n     * @private\n     */\n    /**\n     * The line width of the links connecting nodes, in pixels. This option\n     * is now deprecated and moved to the\n     * [link.radius](#plotOptions.organization.link.lineWidth).\n     *\n     * @sample   highcharts/series-organization/link-options\n     *           Square links\n     *\n     * @deprecated\n     * @apioption series.organization.linkLineWidth\n     * @private\n     */\n    /**\n     * In a horizontal chart, the minimum width of the **hanging** nodes\n     * only, in pixels. In a vertical chart, the minimum height of the\n     * **haning** nodes only, in pixels too.\n     *\n     * Note: Used only when\n     * [hangingIndentTranslation](#plotOptions.organization.hangingIndentTranslation)\n     * is set to `shrink`.\n     *\n     * @see [nodeWidth](#plotOptions.organization.nodeWidth)\n     *\n     * @private\n     */\n    minNodeLength: 10,\n    /**\n     * In a horizontal chart, the width of the nodes in pixels. Note that\n     * most organization charts are inverted (vertical), so the name of this\n     * option is counterintuitive.\n     *\n     * @see [minNodeLength](#plotOptions.organization.minNodeLength)\n     *\n     * @private\n     */\n    nodeWidth: 50,\n    tooltip: {\n        nodeFormat: '{point.name}<br>{point.title}<br>{point.description}'\n    }\n};\n/**\n * An `organization` series. If the [type](#series.organization.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.organization\n * @exclude   dataSorting, boostThreshold, boostBlending\n * @product   highcharts\n * @requires  modules/sankey\n * @requires  modules/organization\n * @apioption series.organization\n */\n/**\n * @type      {Highcharts.SeriesOrganizationDataLabelsOptionsObject|Array<Highcharts.SeriesOrganizationDataLabelsOptionsObject>}\n * @product   highcharts\n * @apioption series.organization.data.dataLabels\n */\n/**\n * A collection of options for the individual nodes. The nodes in an org chart\n * are auto-generated instances of `Highcharts.Point`, but options can be\n * applied here and linked by the `id`.\n *\n * @extends   series.sankey.nodes\n * @type      {Array<*>}\n * @product   highcharts\n * @apioption series.organization.nodes\n */\n/**\n * Individual data label for each node. The options are the same as\n * the ones for [series.organization.dataLabels](#series.organization.dataLabels).\n *\n * @type    {Highcharts.SeriesOrganizationDataLabelsOptionsObject|Array<Highcharts.SeriesOrganizationDataLabelsOptionsObject>}\n *\n * @apioption series.organization.nodes.dataLabels\n */\n/**\n * The job description for the node card, will be inserted by the default\n * `dataLabel.nodeFormatter`.\n *\n * @sample highcharts/demo/organization-chart\n *         Org chart with job descriptions\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.organization.nodes.description\n */\n/**\n * An image for the node card, will be inserted by the default\n * `dataLabel.nodeFormatter`.\n *\n * @sample highcharts/demo/organization-chart\n *         Org chart with images\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.organization.nodes.image\n */\n/**\n * The format string specifying what to show for *links* in the\n * organization chart.\n *\n * Best to use with [`linkTextPath`](#series.organization.dataLabels.linkTextPath) enabled.\n *\n * @sample highcharts/series-organization/link-labels\n *         Organization chart with link labels\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.organization.dataLabels.linkFormat\n * @since 11.0.0\n */\n/**\n * Callback to format data labels for _links_ in the\n * organization chart. The `linkFormat` option takes\n * precedence over the `linkFormatter`.\n *\n * @type      {OrganizationDataLabelsFormatterCallbackFunction}\n * @product   highcharts\n * @apioption series.organization.dataLabels.linkFormatter\n * @since 11.0.0\n */\n/**\n * Options for a _link_ label text which should follow link\n * connection.\n *\n * @sample highcharts/series-organization/link-labels\n *         Organization chart with link labels\n *\n * @type { DataLabelTextPathOptions }\n * @product highcharts\n * @apioption series.organization.dataLabels.linkTextPath\n * @since 11.0.0\n */\n/**\n * Layout for the node's children. If `hanging`, this node's children will hang\n * below their parent, allowing a tighter packing of nodes in the diagram.\n *\n * Note: Since version 10.0.0, the `hanging` layout is set by default for\n * children of a parent using `hanging` layout.\n *\n * @sample highcharts/demo/organization-chart\n *         Hanging layout\n *\n * @type      {Highcharts.SeriesOrganizationNodesLayoutValue}\n * @default   normal\n * @product   highcharts\n * @apioption series.organization.nodes.layout\n */\n/**\n * The job title for the node card, will be inserted by the default\n * `dataLabel.nodeFormatter`.\n *\n * @sample highcharts/demo/organization-chart\n *         Org chart with job titles\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.organization.nodes.title\n */\n/**\n * An array of data points for the series. For the `organization` series\n * type, points can be given in the following way:\n *\n * An array of objects with named values. The following snippet shows only a\n * few settings, see the complete options set below. If the total number of data\n * points exceeds the series' [turboThreshold](#series.area.turboThreshold),\n * this option is not available.\n *\n *  ```js\n *     data: [{\n *         from: 'Category1',\n *         to: 'Category2',\n *         weight: 2\n *     }, {\n *         from: 'Category1',\n *         to: 'Category3',\n *         weight: 5\n *     }]\n *  ```\n *\n * @type      {Array<*>}\n * @extends   series.sankey.data\n * @product   highcharts\n * @apioption series.organization.data\n */\n''; // Keeps doclets above in JS file\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Organization_OrganizationSeriesDefaults = (OrganizationSeriesDefaults);\n\n;// ./code/es-modules/Series/PathUtilities.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nconst getLinkPath = {\n    'default': getDefaultPath,\n    straight: getStraightPath,\n    curved: getCurvedPath\n};\n/**\n *\n */\nfunction getDefaultPath(pathParams) {\n    const { x1, y1, x2, y2, width = 0, inverted = false, radius, parentVisible } = pathParams;\n    const path = [\n        ['M', x1, y1],\n        ['L', x1, y1],\n        ['C', x1, y1, x1, y2, x1, y2],\n        ['L', x1, y2],\n        ['C', x1, y1, x1, y2, x1, y2],\n        ['L', x1, y2]\n    ];\n    return parentVisible ?\n        applyRadius([\n            ['M', x1, y1],\n            ['L', x1 + width * (inverted ? -0.5 : 0.5), y1],\n            ['L', x1 + width * (inverted ? -0.5 : 0.5), y2],\n            ['L', x2, y2]\n        ], radius) :\n        path;\n}\n/**\n *\n */\nfunction getStraightPath(pathParams) {\n    const { x1, y1, x2, y2, width = 0, inverted = false, parentVisible } = pathParams;\n    return parentVisible ? [\n        ['M', x1, y1],\n        ['L', x1 + width * (inverted ? -1 : 1), y2],\n        ['L', x2, y2]\n    ] : [\n        ['M', x1, y1],\n        ['L', x1, y2],\n        ['L', x1, y2]\n    ];\n}\n/**\n *\n */\nfunction getCurvedPath(pathParams) {\n    const { x1, y1, x2, y2, offset = 0, width = 0, inverted = false, parentVisible } = pathParams;\n    return parentVisible ?\n        [\n            ['M', x1, y1],\n            [\n                'C',\n                x1 + offset,\n                y1,\n                x1 - offset + width * (inverted ? -1 : 1),\n                y2,\n                x1 + width * (inverted ? -1 : 1),\n                y2\n            ],\n            ['L', x2, y2]\n        ] :\n        [\n            ['M', x1, y1],\n            ['C', x1, y1, x1, y2, x1, y2],\n            ['L', x2, y2]\n        ];\n}\n/**\n * General function to apply corner radius to a path\n * @private\n */\nfunction applyRadius(path, r) {\n    const d = [];\n    for (let i = 0; i < path.length; i++) {\n        const x = path[i][1];\n        const y = path[i][2];\n        if (typeof x === 'number' && typeof y === 'number') {\n            // MoveTo\n            if (i === 0) {\n                d.push(['M', x, y]);\n            }\n            else if (i === path.length - 1) {\n                d.push(['L', x, y]);\n                // CurveTo\n            }\n            else if (r) {\n                const prevSeg = path[i - 1];\n                const nextSeg = path[i + 1];\n                if (prevSeg && nextSeg) {\n                    const x1 = prevSeg[1], y1 = prevSeg[2], x2 = nextSeg[1], y2 = nextSeg[2];\n                    // Only apply to breaks\n                    if (typeof x1 === 'number' &&\n                        typeof x2 === 'number' &&\n                        typeof y1 === 'number' &&\n                        typeof y2 === 'number' &&\n                        x1 !== x2 &&\n                        y1 !== y2) {\n                        const directionX = x1 < x2 ? 1 : -1, directionY = y1 < y2 ? 1 : -1;\n                        d.push([\n                            'L',\n                            x - directionX * Math.min(Math.abs(x - x1), r),\n                            y - directionY * Math.min(Math.abs(y - y1), r)\n                        ], [\n                            'C',\n                            x,\n                            y,\n                            x,\n                            y,\n                            x + directionX * Math.min(Math.abs(x - x2), r),\n                            y + directionY * Math.min(Math.abs(y - y2), r)\n                        ]);\n                    }\n                }\n                // LineTo\n            }\n            else {\n                d.push(['L', x, y]);\n            }\n        }\n    }\n    return d;\n}\nconst PathUtilities = {\n    applyRadius,\n    getLinkPath\n};\n/* harmony default export */ const Series_PathUtilities = (PathUtilities);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SVGElement\"],\"commonjs\":[\"highcharts\",\"SVGElement\"],\"commonjs2\":[\"highcharts\",\"SVGElement\"],\"root\":[\"Highcharts\",\"SVGElement\"]}\nvar highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_ = __webpack_require__(28);\nvar highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default = /*#__PURE__*/__webpack_require__.n(highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_);\n;// ./code/es-modules/Extensions/TextPath.js\n/* *\n *\n *  Highcharts module with textPath functionality.\n *\n *  (c) 2009-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { deg2rad } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nconst { addEvent, merge, uniqueKey, defined: TextPath_defined, extend } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/**\n * Set a text path for a `text` or `label` element, allowing the text to\n * flow along a path.\n *\n * In order to unset the path for an existing element, call `setTextPath`\n * with `{ enabled: false }` as the second argument.\n *\n * Text path support is not bundled into `highcharts.js`, and requires the\n * `modules/textpath.js` file. However, it is included in the script files of\n * those series types that use it by default\n *\n * @sample highcharts/members/renderer-textpath/ Text path demonstrated\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {Highcharts.SVGElement|undefined} path\n *        Path to follow. If undefined, it allows changing options for the\n *        existing path.\n *\n * @param {Highcharts.DataLabelsTextPathOptionsObject} textPathOptions\n *        Options.\n *\n * @return {Highcharts.SVGElement} Returns the SVGElement for chaining.\n */\nfunction setTextPath(path, textPathOptions) {\n    // Defaults\n    textPathOptions = merge(true, {\n        enabled: true,\n        attributes: {\n            dy: -5,\n            startOffset: '50%',\n            textAnchor: 'middle'\n        }\n    }, textPathOptions);\n    const url = this.renderer.url, textWrapper = this.text || this, textPath = textWrapper.textPath, { attributes, enabled } = textPathOptions;\n    path = path || (textPath && textPath.path);\n    // Remove previously added event\n    if (textPath) {\n        textPath.undo();\n    }\n    if (path && enabled) {\n        const undo = addEvent(textWrapper, 'afterModifyTree', (e) => {\n            if (path && enabled) {\n                // Set ID for the path\n                let textPathId = path.attr('id');\n                if (!textPathId) {\n                    path.attr('id', textPathId = uniqueKey());\n                }\n                // Set attributes for the <text>\n                const textAttribs = {\n                    // `dx`/`dy` options must by set on <text> (parent), the\n                    // rest should be set on <textPath>\n                    x: 0,\n                    y: 0\n                };\n                if (TextPath_defined(attributes.dx)) {\n                    textAttribs.dx = attributes.dx;\n                    delete attributes.dx;\n                }\n                if (TextPath_defined(attributes.dy)) {\n                    textAttribs.dy = attributes.dy;\n                    delete attributes.dy;\n                }\n                textWrapper.attr(textAttribs);\n                // Handle label properties\n                this.attr({ transform: '' });\n                if (this.box) {\n                    this.box = this.box.destroy();\n                }\n                // Wrap the nodes in a textPath\n                const children = e.nodes.slice(0);\n                e.nodes.length = 0;\n                e.nodes[0] = {\n                    tagName: 'textPath',\n                    attributes: extend(attributes, {\n                        'text-anchor': attributes.textAnchor,\n                        href: `${url}#${textPathId}`\n                    }),\n                    children\n                };\n            }\n        });\n        // Set the reference\n        textWrapper.textPath = { path, undo };\n    }\n    else {\n        textWrapper.attr({ dx: 0, dy: 0 });\n        delete textWrapper.textPath;\n    }\n    if (this.added) {\n        // Rebuild text after added\n        textWrapper.textCache = '';\n        this.renderer.buildText(textWrapper);\n    }\n    return this;\n}\n/**\n * Attach a polygon to a bounding box if the element contains a textPath.\n *\n * @function Highcharts.SVGElement#setPolygon\n *\n * @param {any} event\n *        An event containing a bounding box object\n *\n * @return {Highcharts.BBoxObject} Returns the bounding box object.\n */\nfunction setPolygon(event) {\n    const bBox = event.bBox, tp = this.element?.querySelector('textPath');\n    if (tp) {\n        const polygon = [], { b, h } = this.renderer.fontMetrics(this.element), descender = h - b, lineCleanerRegex = new RegExp('(<tspan>|' +\n            '<tspan(?!\\\\sclass=\"highcharts-br\")[^>]*>|' +\n            '<\\\\/tspan>)', 'g'), lines = tp\n            .innerHTML\n            .replace(lineCleanerRegex, '')\n            .split(/<tspan class=\"highcharts-br\"[^>]*>/), numOfLines = lines.length;\n        // Calculate top and bottom coordinates for\n        // either the start or the end of a single\n        // character, and append it to the polygon.\n        const appendTopAndBottom = (charIndex, positionOfChar) => {\n            const { x, y } = positionOfChar, rotation = (tp.getRotationOfChar(charIndex) - 90) * deg2rad, cosRot = Math.cos(rotation), sinRot = Math.sin(rotation);\n            return [\n                [\n                    x - descender * cosRot,\n                    y - descender * sinRot\n                ],\n                [\n                    x + b * cosRot,\n                    y + b * sinRot\n                ]\n            ];\n        };\n        for (let i = 0, lineIndex = 0; lineIndex < numOfLines; lineIndex++) {\n            const line = lines[lineIndex], lineLen = line.length;\n            for (let lineCharIndex = 0; lineCharIndex < lineLen; lineCharIndex += 5) {\n                try {\n                    const srcCharIndex = (i +\n                        lineCharIndex +\n                        lineIndex), [lower, upper] = appendTopAndBottom(srcCharIndex, tp.getStartPositionOfChar(srcCharIndex));\n                    if (lineCharIndex === 0) {\n                        polygon.push(upper);\n                        polygon.push(lower);\n                    }\n                    else {\n                        if (lineIndex === 0) {\n                            polygon.unshift(upper);\n                        }\n                        if (lineIndex === numOfLines - 1) {\n                            polygon.push(lower);\n                        }\n                    }\n                }\n                catch (e) {\n                    // Safari fails on getStartPositionOfChar even if the\n                    // character is within the `textContent.length`\n                    break;\n                }\n            }\n            i += lineLen - 1;\n            try {\n                const srcCharIndex = i + lineIndex, charPos = tp.getEndPositionOfChar(srcCharIndex), [lower, upper] = appendTopAndBottom(srcCharIndex, charPos);\n                polygon.unshift(upper);\n                polygon.unshift(lower);\n            }\n            catch (e) {\n                // Safari fails on getStartPositionOfChar even if the character\n                // is within the `textContent.length`\n                break;\n            }\n        }\n        // Close it\n        if (polygon.length) {\n            polygon.push(polygon[0].slice());\n        }\n        bBox.polygon = polygon;\n    }\n    return bBox;\n}\n/**\n * Draw text along a textPath for a dataLabel.\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {any} event\n *        An event containing label options\n *\n * @return {void}\n */\nfunction drawTextPath(event) {\n    const labelOptions = event.labelOptions, point = event.point, textPathOptions = (labelOptions[point.formatPrefix + 'TextPath'] ||\n        labelOptions.textPath);\n    if (textPathOptions && !labelOptions.useHTML) {\n        this.setTextPath(point.getDataLabelPath?.(this) || point.graphic, textPathOptions);\n        if (point.dataLabelPath &&\n            !textPathOptions.enabled) {\n            // Clean the DOM\n            point.dataLabelPath = (point.dataLabelPath.destroy());\n        }\n    }\n}\nfunction compose(SVGElementClass) {\n    addEvent(SVGElementClass, 'afterGetBBox', setPolygon);\n    addEvent(SVGElementClass, 'beforeAddingDataLabel', drawTextPath);\n    const svgElementProto = SVGElementClass.prototype;\n    if (!svgElementProto.setTextPath) {\n        svgElementProto.setTextPath = setTextPath;\n    }\n}\nconst TextPath = {\n    compose\n};\n/* harmony default export */ const Extensions_TextPath = (TextPath);\n\n;// ./code/es-modules/Series/Organization/OrganizationSeries.js\n/* *\n *\n *  Organization chart module\n *\n *  (c) 2018-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\nconst { sankey: SankeySeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { css, crisp, extend: OrganizationSeries_extend, isNumber, merge: OrganizationSeries_merge, pick: OrganizationSeries_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nExtensions_TextPath.compose((highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default()));\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.organization\n *\n * @augments Highcharts.seriesTypes.sankey\n */\nclass OrganizationSeries extends SankeySeries {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    alignDataLabel(point, dataLabel, options) {\n        // Align the data label to the point graphic\n        const shapeArgs = point.shapeArgs, text = dataLabel.text;\n        if (options.useHTML && shapeArgs) {\n            const padjust = (this.options.borderWidth +\n                2 * this.options.dataLabels.padding);\n            let width = shapeArgs.width || 0, height = shapeArgs.height || 0;\n            if (this.chart.inverted) {\n                width = height;\n                height = shapeArgs.width || 0;\n            }\n            height -= padjust;\n            width -= padjust;\n            text.foreignObject?.attr({\n                x: 0,\n                y: 0,\n                width,\n                height\n            });\n            // When foreign object, the parent node is the body. When parallel\n            // HTML, it is the surrounding div emulating `g`\n            css(text.element.parentNode, {\n                width: width + 'px',\n                height: height + 'px'\n            });\n            // Set properties for the span emulating `text`\n            css(text.element, {\n                left: 0,\n                top: 0,\n                width: '100%',\n                height: '100%',\n                overflow: 'hidden'\n            });\n            // The getBBox function is used in `alignDataLabel` to align\n            // inside the box\n            dataLabel.getBBox = () => ({ width, height, x: 0, y: 0 });\n            // Overwrite dataLabel dimensions (#13100).\n            dataLabel.width = width;\n            dataLabel.height = height;\n        }\n        super.alignDataLabel.apply(this, arguments);\n    }\n    createNode(id) {\n        const node = super.createNode.call(this, id);\n        // All nodes in an org chart are equal width\n        node.getSum = () => 1;\n        return node;\n    }\n    pointAttribs(point, state) {\n        const series = this, attribs = SankeySeries.prototype.pointAttribs.call(series, point, state), level = point.isNode ? point.level : point.fromNode.level, levelOptions = series.mapOptionsToLevel[level || 0] || {}, options = point.options, stateOptions = (levelOptions.states &&\n            levelOptions.states[state]) ||\n            {}, borderRadius = OrganizationSeries_pick(stateOptions.borderRadius, options.borderRadius, levelOptions.borderRadius, series.options.borderRadius), linkColor = OrganizationSeries_pick(stateOptions.linkColor, options.linkColor, levelOptions.linkColor, series.options.linkColor, stateOptions.link && stateOptions.link.color, options.link && options.link.color, levelOptions.link && levelOptions.link.color, series.options.link && series.options.link.color), linkLineWidth = OrganizationSeries_pick(stateOptions.linkLineWidth, options.linkLineWidth, levelOptions.linkLineWidth, series.options.linkLineWidth, stateOptions.link && stateOptions.link.lineWidth, options.link && options.link.lineWidth, levelOptions.link && levelOptions.link.lineWidth, series.options.link && series.options.link.lineWidth), linkOpacity = OrganizationSeries_pick(stateOptions.linkOpacity, options.linkOpacity, levelOptions.linkOpacity, series.options.linkOpacity, stateOptions.link && stateOptions.link.linkOpacity, options.link && options.link.linkOpacity, levelOptions.link && levelOptions.link.linkOpacity, series.options.link && series.options.link.linkOpacity);\n        if (!point.isNode) {\n            attribs.stroke = linkColor;\n            attribs['stroke-width'] = linkLineWidth;\n            attribs.opacity = linkOpacity;\n            delete attribs.fill;\n        }\n        else {\n            if (isNumber(borderRadius)) {\n                attribs.r = borderRadius;\n            }\n        }\n        return attribs;\n    }\n    translateLink(point) {\n        const chart = this.chart, options = this.options, fromNode = point.fromNode, toNode = point.toNode, linkWidth = OrganizationSeries_pick(options.linkLineWidth, options.link.lineWidth, 0), factor = OrganizationSeries_pick(options.link.offset, 0.5), type = OrganizationSeries_pick(point.options.link && point.options.link.type, options.link.type);\n        if (fromNode.shapeArgs && toNode.shapeArgs) {\n            const hangingIndent = options.hangingIndent, hangingRight = options.hangingSide === 'right', toOffset = toNode.options.offset, percentOffset = /%$/.test(toOffset) && parseInt(toOffset, 10), inverted = chart.inverted;\n            let x1 = crisp((fromNode.shapeArgs.x || 0) +\n                (fromNode.shapeArgs.width || 0), linkWidth), y1 = crisp((fromNode.shapeArgs.y || 0) +\n                (fromNode.shapeArgs.height || 0) / 2, linkWidth), x2 = crisp(toNode.shapeArgs.x || 0, linkWidth), y2 = crisp((toNode.shapeArgs.y || 0) +\n                (toNode.shapeArgs.height || 0) / 2, linkWidth), xMiddle;\n            if (inverted) {\n                x1 -= (fromNode.shapeArgs.width || 0);\n                x2 += (toNode.shapeArgs.width || 0);\n            }\n            xMiddle = this.colDistance ?\n                crisp(x2 +\n                    ((inverted ? 1 : -1) *\n                        (this.colDistance - this.nodeWidth)) /\n                        2, linkWidth) :\n                crisp((x2 + x1) / 2, linkWidth);\n            // Put the link on the side of the node when an offset is given. HR\n            // node in the main demo.\n            if (percentOffset &&\n                (percentOffset >= 50 || percentOffset <= -50)) {\n                xMiddle = x2 = crisp(x2 + (inverted ? -0.5 : 0.5) *\n                    (toNode.shapeArgs.width || 0), linkWidth);\n                y2 = toNode.shapeArgs.y || 0;\n                if (percentOffset > 0) {\n                    y2 += toNode.shapeArgs.height || 0;\n                }\n            }\n            if (toNode.hangsFrom === fromNode) {\n                if (chart.inverted) {\n                    y1 = !hangingRight ?\n                        crisp((fromNode.shapeArgs.y || 0) +\n                            (fromNode.shapeArgs.height || 0) -\n                            hangingIndent / 2, linkWidth) :\n                        crisp((fromNode.shapeArgs.y || 0) + hangingIndent / 2, linkWidth);\n                    y2 = !hangingRight ? ((toNode.shapeArgs.y || 0) +\n                        (toNode.shapeArgs.height || 0)) : (toNode.shapeArgs.y || 0) + hangingIndent / 2;\n                }\n                else {\n                    y1 = crisp((fromNode.shapeArgs.y || 0) + hangingIndent / 2, linkWidth);\n                }\n                xMiddle = x2 = crisp((toNode.shapeArgs.x || 0) +\n                    (toNode.shapeArgs.width || 0) / 2, linkWidth);\n            }\n            point.plotX = xMiddle;\n            point.plotY = (y1 + y2) / 2;\n            point.shapeType = 'path';\n            if (type === 'straight') {\n                point.shapeArgs = {\n                    d: [\n                        ['M', x1, y1],\n                        ['L', x2, y2]\n                    ]\n                };\n            }\n            else if (type === 'curved') {\n                const offset = Math.abs(x2 - x1) * factor * (inverted ? -1 : 1);\n                point.shapeArgs = {\n                    d: [\n                        ['M', x1, y1],\n                        ['C', x1 + offset, y1, x2 - offset, y2, x2, y2]\n                    ]\n                };\n            }\n            else {\n                point.shapeArgs = {\n                    d: Series_PathUtilities.applyRadius([\n                        ['M', x1, y1],\n                        ['L', xMiddle, y1],\n                        ['L', xMiddle, y2],\n                        ['L', x2, y2]\n                    ], OrganizationSeries_pick(options.linkRadius, options.link.radius))\n                };\n            }\n            point.dlBox = {\n                x: (x1 + x2) / 2,\n                y: (y1 + y2) / 2,\n                height: linkWidth,\n                width: 0\n            };\n        }\n    }\n    translateNode(node, column) {\n        super.translateNode(node, column);\n        const chart = this.chart, options = this.options, sum = node.getSum(), translationFactor = this.translationFactor, nodeHeight = Math.max(Math.round(sum * translationFactor), options.minLinkWidth || 0), hangingRight = options.hangingSide === 'right', indent = options.hangingIndent || 0, indentLogic = options.hangingIndentTranslation, minLength = options.minNodeLength || 10, nodeWidth = Math.round(this.nodeWidth), shapeArgs = node.shapeArgs, sign = chart.inverted ? -1 : 1;\n        let parentNode = node.hangsFrom;\n        if (parentNode) {\n            if (indentLogic === 'cumulative') {\n                // Move to the right:\n                shapeArgs.height -= indent;\n                // If hanging right, first indent is handled by shrinking.\n                if (chart.inverted && !hangingRight) {\n                    shapeArgs.y -= sign * indent;\n                }\n                while (parentNode) {\n                    // Hanging right is the same direction as non-inverted.\n                    shapeArgs.y += (hangingRight ? 1 : sign) * indent;\n                    parentNode = parentNode.hangsFrom;\n                }\n            }\n            else if (indentLogic === 'shrink') {\n                // Resize the node:\n                while (parentNode &&\n                    shapeArgs.height > indent + minLength) {\n                    shapeArgs.height -= indent;\n                    // Fixes nodes not dropping in non-inverted charts.\n                    // Hanging right is the same as non-inverted.\n                    if (!chart.inverted || hangingRight) {\n                        shapeArgs.y += indent;\n                    }\n                    parentNode = parentNode.hangsFrom;\n                }\n            }\n            else {\n                // Option indentLogic === \"inherit\"\n                // Do nothing (v9.3.2 and prev versions):\n                shapeArgs.height -= indent;\n                if (!chart.inverted || hangingRight) {\n                    shapeArgs.y += indent;\n                }\n            }\n        }\n        node.nodeHeight = chart.inverted ?\n            shapeArgs.width :\n            shapeArgs.height;\n        // Calculate shape args correctly to align nodes to center (#19946)\n        if (node.shapeArgs && !node.hangsFrom) {\n            node.shapeArgs = OrganizationSeries_merge(node.shapeArgs, {\n                x: (node.shapeArgs.x || 0) + (nodeWidth / 2) -\n                    ((node.shapeArgs.width || 0) / 2),\n                y: (node.shapeArgs.y || 0) + (nodeHeight / 2) -\n                    ((node.shapeArgs.height || 0) / 2)\n            });\n        }\n    }\n    drawDataLabels() {\n        const dlOptions = this.options.dataLabels;\n        if (dlOptions.linkTextPath && dlOptions.linkTextPath.enabled) {\n            for (const link of this.points) {\n                link.options.dataLabels = OrganizationSeries_merge(link.options.dataLabels, { useHTML: false });\n            }\n        }\n        super.drawDataLabels();\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nOrganizationSeries.defaultOptions = OrganizationSeries_merge(SankeySeries.defaultOptions, Organization_OrganizationSeriesDefaults);\nOrganizationSeries_extend(OrganizationSeries.prototype, {\n    pointClass: Organization_OrganizationPoint\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('organization', OrganizationSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Organization_OrganizationSeries = ((/* unused pure expression or super */ null && (OrganizationSeries)));\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Layout value for the child nodes in an organization chart. If `hanging`, this\n * node's children will hang below their parent, allowing a tighter packing of\n * nodes in the diagram.\n *\n * @typedef {\"normal\"|\"hanging\"} Highcharts.SeriesOrganizationNodesLayoutValue\n */\n/**\n * Indent translation value for the child nodes in an organization chart, when\n * parent has `hanging` layout. Option can shrink nodes (for tight charts),\n * translate children to the left, or render nodes directly under the parent.\n *\n * @typedef {\"inherit\"|\"cumulative\"|\"shrink\"} Highcharts.OrganizationHangingIndentTranslationValue\n */\n''; // Detach doclets above\n\n;// ./code/es-modules/masters/modules/organization.js\n\n\n\n\n/* harmony default export */ const organization_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__28__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "organization_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "sankey", "pointClass", "SankeyPointClass", "seriesTypes", "defined", "find", "pick", "Organization_OrganizationPoint", "constructor", "series", "options", "x", "isNode", "dataLabelOnNull", "formatPrefix", "getSum", "setNodeColumn", "node", "fromNode", "getFromNode", "column", "linksTo", "length", "layout", "i", "link", "hangsFrom", "linksFrom", "index", "found", "toNode", "j", "id", "getOffset", "offset", "for<PERSON>ach", "Series_PathUtilities", "applyRadius", "path", "r", "y", "push", "prevSeg", "nextSeg", "x1", "y1", "x2", "y2", "directionX", "directionY", "Math", "min", "abs", "highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_", "highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default", "deg2rad", "addEvent", "merge", "<PERSON><PERSON><PERSON>", "TextPath_defined", "extend", "setTextPath", "textPathOptions", "enabled", "attributes", "dy", "startOffset", "textAnchor", "url", "renderer", "textWrapper", "text", "textPath", "undo", "e", "textPathId", "attr", "textAttribs", "dx", "transform", "box", "destroy", "children", "nodes", "slice", "tagName", "href", "added", "textCache", "buildText", "setPolygon", "event", "bBox", "tp", "element", "querySelector", "polygon", "b", "h", "fontMetrics", "descender", "lineCleanerRegex", "RegExp", "lines", "innerHTML", "replace", "split", "numOfLines", "appendTopAndBottom", "charIndex", "positionOfChar", "rotation", "getRotationOfChar", "cosRot", "cos", "sinRot", "sin", "lineIndex", "lineLen", "line", "lineCharIndex", "srcCharIndex", "lower", "upper", "getStartPositionOfChar", "unshift", "char<PERSON><PERSON>", "getEndPositionOfChar", "drawTextPath", "labelOptions", "point", "useHTML", "getDataLabelPath", "graphic", "dataLabelPath", "SankeySeries", "css", "crisp", "OrganizationSeries_extend", "isNumber", "OrganizationSeries_merge", "OrganizationSeries_pick", "Extensions_TextPath", "compose", "SVGElementClass", "svgElementProto", "OrganizationSeries", "alignDataLabel", "dataLabel", "shapeArgs", "padjust", "borderWidth", "dataLabels", "padding", "width", "height", "chart", "inverted", "foreignObject", "parentNode", "left", "top", "overflow", "getBBox", "apply", "arguments", "createNode", "pointAttribs", "state", "attribs", "level", "levelOptions", "mapOptionsToLevel", "stateOptions", "states", "borderRadius", "linkColor", "color", "linkLineWidth", "lineWidth", "linkOpacity", "stroke", "opacity", "fill", "translateLink", "linkWidth", "factor", "type", "hangingIndent", "hangingRight", "hangingSide", "toOffset", "percentOffset", "test", "parseInt", "xMiddle", "colDistance", "nodeWidth", "plotX", "plotY", "shapeType", "linkRadius", "radius", "dlBox", "translateNode", "nodeHeight", "max", "round", "sum", "translationFactor", "minLinkWidth", "indent", "indentLogic", "hangingIndentTranslation", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sign", "drawDataLabels", "dlOptions", "linkTextPath", "points", "defaultOptions", "borderColor", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outerStyle", "display", "imageStyle", "innerStyle", "styleAttr", "style", "keys", "reduce", "str", "description", "image", "title", "forExport", "position", "html", "name", "margin", "fontWeight", "fontSize", "textAlign", "tooltip", "nodeFormat", "registerSeriesType"], "mappings": "CAWA,AAXA;;;;;;;;;;CAUC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,UAAa,EAC/G,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,kCAAmC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAACA,EAAK,UAAa,CAAE,GAC9I,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,kCAAkC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,UAAa,EAElJA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,UAAa,CACzH,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,IACvG,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,GACC,AAACR,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAab,OAAO,CAG5B,IAAIC,EAASS,CAAwB,CAACE,EAAS,CAAG,CAGjDZ,QAAS,CAAC,CACX,EAMA,OAHAS,CAAmB,CAACG,EAAS,CAACX,EAAQA,EAAOD,OAAO,CAAEW,GAG/CV,EAAOD,OAAO,AACtB,CAMCW,EAAoBI,CAAC,CAAG,AAACd,IACxB,IAAIe,EAASf,GAAUA,EAAOgB,UAAU,CACvC,IAAOhB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAU,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAAClB,EAASoB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACtB,EAASqB,IAC5EE,OAAOC,cAAc,CAACxB,EAASqB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAejL,GAAM,CAAEE,OAAQ,CAAET,UAAW,CAAEU,WAAYC,CAAgB,CAAE,CAAE,CAAE,CAAG,AAACH,IAA2II,WAAW,CAErN,CAAEC,QAAAA,CAAO,CAAEC,KAAAA,CAAI,CAAEC,KAAAA,CAAI,CAAE,CAAIT,IAmGEU,EArEnC,cAAgCL,EAM5BM,YAAYC,CAAM,CAAEC,CAAO,CAAEC,CAAC,CAAE,CAC5B,KAAK,CAACF,EAAQC,EAASC,GAClB,IAAI,CAACC,MAAM,GACZ,IAAI,CAACC,eAAe,CAAG,CAAA,EACvB,IAAI,CAACC,YAAY,CAAG,OAE5B,CAKAC,QAAS,CACL,OAAO,CACX,CAKAC,eAAgB,CACZ,KAAK,CAACA,gBACN,IAAMC,EAAO,IAAI,CAAEC,EAAWD,EAAKE,WAAW,GAAGD,QAAQ,CAEzD,GAEA,CAACd,EAAQa,EAAKP,OAAO,CAACU,MAAM,GAExBH,AAAwB,IAAxBA,EAAKI,OAAO,CAACC,MAAM,EAEnBJ,GACAA,AAA4B,YAA5BA,EAASR,OAAO,CAACa,MAAM,CAAgB,CACvC,IAAIC,EAAI,GAAIC,CAGZR,CAAAA,EAAKP,OAAO,CAACa,MAAM,CAAGjB,EAAKW,EAAKP,OAAO,CAACa,MAAM,CAAE,WAChDN,EAAKS,SAAS,CAAGR,EACjBb,EAAKa,EAASS,SAAS,CAAE,CAACF,EAAMG,KAC5B,IAAMC,EAAQJ,EAAKK,MAAM,GAAKb,EAI9B,OAHIY,GACAL,CAAAA,EAAII,CAAI,EAELC,CACX,GAGA,IAAK,IAAIE,EAAI,EAAGA,EAAIb,EAASS,SAAS,CAACL,MAAM,CAAE,EAAES,EAEzCN,AADJA,CAAAA,EAAOP,EAASS,SAAS,CAACI,EAAE,AAAD,EAClBD,MAAM,CAACE,EAAE,GAAKf,EAAKe,EAAE,CAE1BD,EAAIb,EAASS,SAAS,CAACL,MAAM,CAG7BE,GAAKS,AA7EzB,SAASA,EAAUhB,CAAI,EACnB,IAAIiB,EAASjB,EAAKU,SAAS,CAACL,MAAM,CAYlC,OAXAL,EAAKU,SAAS,CAACQ,OAAO,CAAC,AAACV,IAChBA,EAAKO,EAAE,GAAKP,EAAKK,MAAM,CAACT,OAAO,CAAC,EAAE,CAACW,EAAE,CAErCE,GAAUD,EAAUR,EAAKK,MAAM,EAK/BI,GAER,GACOA,CACX,EA+DmCT,EAAKK,MAAM,CAGlCb,CAAAA,EAAKG,MAAM,CAAG,AAACH,CAAAA,EAAKG,MAAM,EAAI,CAAA,EAAKI,CACvC,CACJ,CACJ,EA0mBmCY,EAJb,CAClBC,YApDJ,SAAqBC,CAAI,CAAEC,CAAC,EACxB,IAAM3D,EAAI,EAAE,CACZ,IAAK,IAAI4C,EAAI,EAAGA,EAAIc,EAAKhB,MAAM,CAAEE,IAAK,CAClC,IAAMb,EAAI2B,CAAI,CAACd,EAAE,CAAC,EAAE,CACdgB,EAAIF,CAAI,CAACd,EAAE,CAAC,EAAE,CACpB,GAAI,AAAa,UAAb,OAAOb,GAAkB,AAAa,UAAb,OAAO6B,EAEhC,GAAIhB,AAAM,IAANA,EACA5C,EAAE6D,IAAI,CAAC,CAAC,IAAK9B,EAAG6B,EAAE,OAEjB,GAAIhB,IAAMc,EAAKhB,MAAM,CAAG,EACzB1C,EAAE6D,IAAI,CAAC,CAAC,IAAK9B,EAAG6B,EAAE,OAGjB,GAAID,EAAG,CACR,IAAMG,EAAUJ,CAAI,CAACd,EAAI,EAAE,CACrBmB,EAAUL,CAAI,CAACd,EAAI,EAAE,CAC3B,GAAIkB,GAAWC,EAAS,CACpB,IAAMC,EAAKF,CAAO,CAAC,EAAE,CAAEG,EAAKH,CAAO,CAAC,EAAE,CAAEI,EAAKH,CAAO,CAAC,EAAE,CAAEI,EAAKJ,CAAO,CAAC,EAAE,CAExE,GAAI,AAAc,UAAd,OAAOC,GACP,AAAc,UAAd,OAAOE,GACP,AAAc,UAAd,OAAOD,GACP,AAAc,UAAd,OAAOE,GACPH,IAAOE,GACPD,IAAOE,EAAI,CACX,IAAMC,EAAaJ,EAAKE,EAAK,EAAI,GAAIG,EAAaJ,EAAKE,EAAK,EAAI,GAChEnE,EAAE6D,IAAI,CAAC,CACH,IACA9B,EAAIqC,EAAaE,KAAKC,GAAG,CAACD,KAAKE,GAAG,CAACzC,EAAIiC,GAAKL,GAC5CC,EAAIS,EAAaC,KAAKC,GAAG,CAACD,KAAKE,GAAG,CAACZ,EAAIK,GAAKN,GAC/C,CAAE,CACC,IACA5B,EACA6B,EACA7B,EACA6B,EACA7B,EAAIqC,EAAaE,KAAKC,GAAG,CAACD,KAAKE,GAAG,CAACzC,EAAImC,GAAKP,GAC5CC,EAAIS,EAAaC,KAAKC,GAAG,CAACD,KAAKE,GAAG,CAACZ,EAAIO,GAAKR,GAC/C,CACL,CACJ,CAEJ,MAEI3D,EAAE6D,IAAI,CAAC,CAAC,IAAK9B,EAAG6B,EAAE,CAG9B,CACA,OAAO5D,CACX,CAIA,EAIA,IAAIyE,EAAmHhF,EAAoB,IACvIiF,EAAuIjF,EAAoBI,CAAC,CAAC4E,GAgBjK,GAAM,CAAEE,QAAAA,CAAO,CAAE,CAAI1D,IACf,CAAE2D,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,UAAAA,CAAS,CAAEtD,QAASuD,CAAgB,CAAEC,OAAAA,CAAM,CAAE,CAAI/D,IAyB3E,SAASgE,EAAYvB,CAAI,CAAEwB,CAAe,EAEtCA,EAAkBL,EAAM,CAAA,EAAM,CAC1BM,QAAS,CAAA,EACTC,WAAY,CACRC,GAAI,GACJC,YAAa,MACbC,WAAY,QAChB,CACJ,EAAGL,GACH,IAAMM,EAAM,IAAI,CAACC,QAAQ,CAACD,GAAG,CAAEE,EAAc,IAAI,CAACC,IAAI,EAAI,IAAI,CAAEC,EAAWF,EAAYE,QAAQ,CAAE,CAAER,WAAAA,CAAU,CAAED,QAAAA,CAAO,CAAE,CAAGD,EAM3H,GALAxB,EAAOA,GAASkC,GAAYA,EAASlC,IAAI,CAErCkC,GACAA,EAASC,IAAI,GAEbnC,GAAQyB,EAAS,CACjB,IAAMU,EAAOjB,EAASc,EAAa,kBAAmB,AAACI,IACnD,GAAIpC,GAAQyB,EAAS,CAEjB,IAAIY,EAAarC,EAAKsC,IAAI,CAAC,KACvB,CAACD,GACDrC,EAAKsC,IAAI,CAAC,KAAMD,EAAajB,KAGjC,IAAMmB,EAAc,CAGhBlE,EAAG,EACH6B,EAAG,CACP,EACImB,EAAiBK,EAAWc,EAAE,IAC9BD,EAAYC,EAAE,CAAGd,EAAWc,EAAE,CAC9B,OAAOd,EAAWc,EAAE,EAEpBnB,EAAiBK,EAAWC,EAAE,IAC9BY,EAAYZ,EAAE,CAAGD,EAAWC,EAAE,CAC9B,OAAOD,EAAWC,EAAE,EAExBK,EAAYM,IAAI,CAACC,GAEjB,IAAI,CAACD,IAAI,CAAC,CAAEG,UAAW,EAAG,GACtB,IAAI,CAACC,GAAG,EACR,CAAA,IAAI,CAACA,GAAG,CAAG,IAAI,CAACA,GAAG,CAACC,OAAO,EAAC,EAGhC,IAAMC,EAAWR,EAAES,KAAK,CAACC,KAAK,CAAC,EAC/BV,CAAAA,EAAES,KAAK,CAAC7D,MAAM,CAAG,EACjBoD,EAAES,KAAK,CAAC,EAAE,CAAG,CACTE,QAAS,WACTrB,WAAYJ,EAAOI,EAAY,CAC3B,cAAeA,EAAWG,UAAU,CACpCmB,KAAM,CAAC,EAAElB,EAAI,CAAC,EAAEO,EAAW,CAAC,AAChC,GACAO,SAAAA,CACJ,CACJ,CACJ,EAEAZ,CAAAA,EAAYE,QAAQ,CAAG,CAAElC,KAAAA,EAAMmC,KAAAA,CAAK,CACxC,MAEIH,EAAYM,IAAI,CAAC,CAAEE,GAAI,EAAGb,GAAI,CAAE,GAChC,OAAOK,EAAYE,QAAQ,CAO/B,OALI,IAAI,CAACe,KAAK,GAEVjB,EAAYkB,SAAS,CAAG,GACxB,IAAI,CAACnB,QAAQ,CAACoB,SAAS,CAACnB,IAErB,IAAI,AACf,CAWA,SAASoB,EAAWC,CAAK,EACrB,IAAMC,EAAOD,EAAMC,IAAI,CAAEC,EAAK,IAAI,CAACC,OAAO,EAAEC,cAAc,YAC1D,GAAIF,EAAI,CACJ,IAAMG,EAAU,EAAE,CAAE,CAAEC,EAAAA,CAAC,CAAEC,EAAAA,CAAC,CAAE,CAAG,IAAI,CAAC7B,QAAQ,CAAC8B,WAAW,CAAC,IAAI,CAACL,OAAO,EAAGM,EAAYF,EAAID,EAAGI,EAAmB,AAAIC,OAAO,gEAEtG,KAAMC,EAAQV,EAC5BW,SAAS,CACTC,OAAO,CAACJ,EAAkB,IAC1BK,KAAK,CAAC,sCAAuCC,EAAaJ,EAAMjF,MAAM,CAIrEsF,EAAqB,CAACC,EAAWC,KACnC,GAAM,CAAEnG,EAAAA,CAAC,CAAE6B,EAAAA,CAAC,CAAE,CAAGsE,EAAgBC,EAAW,AAAClB,CAAAA,EAAGmB,iBAAiB,CAACH,GAAa,EAAC,EAAKtD,EAAS0D,EAAS/D,KAAKgE,GAAG,CAACH,GAAWI,EAASjE,KAAKkE,GAAG,CAACL,GAC7I,MAAO,CACH,CACIpG,EAAIyF,EAAYa,EAChBzE,EAAI4D,EAAYe,EACnB,CACD,CACIxG,EAAIsF,EAAIgB,EACRzE,EAAIyD,EAAIkB,EACX,CACJ,AACL,EACA,IAAK,IAAI3F,EAAI,EAAG6F,EAAY,EAAGA,EAAYV,EAAYU,IAAa,CAChE,IAA+BC,EAAUC,AAA5BhB,CAAK,CAACc,EAAU,CAAiB/F,MAAM,CACpD,IAAK,IAAIkG,EAAgB,EAAGA,EAAgBF,EAASE,GAAiB,EAClE,GAAI,CACA,IAAMC,EAAgBjG,EAClBgG,EACAH,EAAY,CAACK,EAAOC,EAAM,CAAGf,EAAmBa,EAAc5B,EAAG+B,sBAAsB,CAACH,GACxFD,AAAkB,CAAA,IAAlBA,GACAxB,EAAQvD,IAAI,CAACkF,GACb3B,EAAQvD,IAAI,CAACiF,KAGTL,AAAc,IAAdA,GACArB,EAAQ6B,OAAO,CAACF,GAEhBN,IAAcV,EAAa,GAC3BX,EAAQvD,IAAI,CAACiF,GAGzB,CACA,MAAOhD,EAAG,CAGN,KACJ,CAEJlD,GAAK8F,EAAU,EACf,GAAI,CACA,IAAMG,EAAejG,EAAI6F,EAAWS,EAAUjC,EAAGkC,oBAAoB,CAACN,GAAe,CAACC,EAAOC,EAAM,CAAGf,EAAmBa,EAAcK,GACvI9B,EAAQ6B,OAAO,CAACF,GAChB3B,EAAQ6B,OAAO,CAACH,EACpB,CACA,MAAOhD,EAAG,CAGN,KACJ,CACJ,CAEIsB,EAAQ1E,MAAM,EACd0E,EAAQvD,IAAI,CAACuD,CAAO,CAAC,EAAE,CAACZ,KAAK,IAEjCQ,EAAKI,OAAO,CAAGA,CACnB,CACA,OAAOJ,CACX,CAWA,SAASoC,EAAarC,CAAK,EACvB,IAAMsC,EAAetC,EAAMsC,YAAY,CAAEC,EAAQvC,EAAMuC,KAAK,CAAEpE,EAAmBmE,CAAY,CAACC,EAAMpH,YAAY,CAAG,WAAW,EAC1HmH,EAAazD,QAAQ,CACrBV,GAAmB,CAACmE,EAAaE,OAAO,GACxC,IAAI,CAACtE,WAAW,CAACqE,EAAME,gBAAgB,GAAG,IAAI,GAAKF,EAAMG,OAAO,CAAEvE,GAC9DoE,EAAMI,aAAa,EACnB,CAACxE,EAAgBC,OAAO,EAExBmE,CAAAA,EAAMI,aAAa,CAAIJ,EAAMI,aAAa,CAACrD,OAAO,EAAE,EAGhE,CA+BA,GAAM,CAAEjF,OAAQuI,CAAY,CAAE,CAAG,AAACxI,IAA2II,WAAW,CAElL,CAAEqI,IAAAA,CAAG,CAAEC,MAAAA,CAAK,CAAE7E,OAAQ8E,CAAyB,CAAEC,SAAAA,CAAQ,CAAElF,MAAOmF,CAAwB,CAAEtI,KAAMuI,CAAuB,CAAE,CAAIhJ,IAGrIiJ,AA3BiB,CAAA,CACbC,QATJ,SAAiBC,CAAe,EAC5BxF,EAASwF,EAAiB,eAAgBtD,GAC1ClC,EAASwF,EAAiB,wBAAyBhB,GACnD,IAAMiB,EAAkBD,EAAgBzJ,SAAS,AAC7C,AAAC0J,CAAAA,EAAgBpF,WAAW,EAC5BoF,CAAAA,EAAgBpF,WAAW,CAAGA,CAAU,CAEhD,CAGA,CAAA,EAyBoBkF,OAAO,CAAEzF,IAa7B,OAAM4F,UAA2BX,EAM7BY,eAAejB,CAAK,CAAEkB,CAAS,CAAE1I,CAAO,CAAE,CAEtC,IAAM2I,EAAYnB,EAAMmB,SAAS,CAAE9E,EAAO6E,EAAU7E,IAAI,CACxD,GAAI7D,EAAQyH,OAAO,EAAIkB,EAAW,CAC9B,IAAMC,EAAW,IAAI,CAAC5I,OAAO,CAAC6I,WAAW,CACrC,EAAI,IAAI,CAAC7I,OAAO,CAAC8I,UAAU,CAACC,OAAO,CACnCC,EAAQL,EAAUK,KAAK,EAAI,EAAGC,EAASN,EAAUM,MAAM,EAAI,CAC3D,CAAA,IAAI,CAACC,KAAK,CAACC,QAAQ,GACnBH,EAAQC,EACRA,EAASN,EAAUK,KAAK,EAAI,GAEhCC,GAAUL,EACVI,GAASJ,EACT/E,EAAKuF,aAAa,EAAElF,KAAK,CACrBjE,EAAG,EACH6B,EAAG,EACHkH,MAAAA,EACAC,OAAAA,CACJ,GAGAnB,EAAIjE,EAAKuB,OAAO,CAACiE,UAAU,CAAE,CACzBL,MAAOA,EAAQ,KACfC,OAAQA,EAAS,IACrB,GAEAnB,EAAIjE,EAAKuB,OAAO,CAAE,CACdkE,KAAM,EACNC,IAAK,EACLP,MAAO,OACPC,OAAQ,OACRO,SAAU,QACd,GAGAd,EAAUe,OAAO,CAAG,IAAO,CAAA,CAAET,MAAAA,EAAOC,OAAAA,EAAQhJ,EAAG,EAAG6B,EAAG,CAAE,CAAA,EAEvD4G,EAAUM,KAAK,CAAGA,EAClBN,EAAUO,MAAM,CAAGA,CACvB,CACA,KAAK,CAACR,eAAeiB,KAAK,CAAC,IAAI,CAAEC,UACrC,CACAC,WAAWtI,CAAE,CAAE,CACX,IAAMf,EAAO,KAAK,CAACqJ,WAAW7K,IAAI,CAAC,IAAI,CAAEuC,GAGzC,OADAf,EAAKF,MAAM,CAAG,IAAM,EACbE,CACX,CACAsJ,aAAarC,CAAK,CAAEsC,CAAK,CAAE,CACvB,IAAqBC,EAAUlC,EAAahJ,SAAS,CAACgL,YAAY,CAAC9K,IAAI,CAAxD,IAAI,CAA6DyI,EAAOsC,GAAQE,EAAQxC,EAAMtH,MAAM,CAAGsH,EAAMwC,KAAK,CAAGxC,EAAMhH,QAAQ,CAACwJ,KAAK,CAAEC,EAAelK,AAA1J,IAAI,CAA6JmK,iBAAiB,CAACF,GAAS,EAAE,EAAI,CAAC,EAAGhK,EAAUwH,EAAMxH,OAAO,CAAEmK,EAAe,AAACF,EAAaG,MAAM,EAC7QH,EAAaG,MAAM,CAACN,EAAM,EAC1B,CAAC,EAAGO,EAAelC,EAAwBgC,EAAaE,YAAY,CAAErK,EAAQqK,YAAY,CAAEJ,EAAaI,YAAY,CAAEtK,AAF5G,IAAI,CAE+GC,OAAO,CAACqK,YAAY,EAAGC,EAAYnC,EAAwBgC,EAAaG,SAAS,CAAEtK,EAAQsK,SAAS,CAAEL,EAAaK,SAAS,CAAEvK,AAFjP,IAAI,CAEoPC,OAAO,CAACsK,SAAS,CAAEH,EAAapJ,IAAI,EAAIoJ,EAAapJ,IAAI,CAACwJ,KAAK,CAAEvK,EAAQe,IAAI,EAAIf,EAAQe,IAAI,CAACwJ,KAAK,CAAEN,EAAalJ,IAAI,EAAIkJ,EAAalJ,IAAI,CAACwJ,KAAK,CAAExK,AAF3Y,IAAI,CAE8YC,OAAO,CAACe,IAAI,EAAIhB,AAFla,IAAI,CAEqaC,OAAO,CAACe,IAAI,CAACwJ,KAAK,EAAGC,EAAgBrC,EAAwBgC,EAAaK,aAAa,CAAExK,EAAQwK,aAAa,CAAEP,EAAaO,aAAa,CAAEzK,AAFrjB,IAAI,CAEwjBC,OAAO,CAACwK,aAAa,CAAEL,EAAapJ,IAAI,EAAIoJ,EAAapJ,IAAI,CAAC0J,SAAS,CAAEzK,EAAQe,IAAI,EAAIf,EAAQe,IAAI,CAAC0J,SAAS,CAAER,EAAalJ,IAAI,EAAIkJ,EAAalJ,IAAI,CAAC0J,SAAS,CAAE1K,AAF/tB,IAAI,CAEkuBC,OAAO,CAACe,IAAI,EAAIhB,AAFtvB,IAAI,CAEyvBC,OAAO,CAACe,IAAI,CAAC0J,SAAS,EAAGC,EAAcvC,EAAwBgC,EAAaO,WAAW,CAAE1K,EAAQ0K,WAAW,CAAET,EAAaS,WAAW,CAAE3K,AAFr4B,IAAI,CAEw4BC,OAAO,CAAC0K,WAAW,CAAEP,EAAapJ,IAAI,EAAIoJ,EAAapJ,IAAI,CAAC2J,WAAW,CAAE1K,EAAQe,IAAI,EAAIf,EAAQe,IAAI,CAAC2J,WAAW,CAAET,EAAalJ,IAAI,EAAIkJ,EAAalJ,IAAI,CAAC2J,WAAW,CAAE3K,AAFnjC,IAAI,CAEsjCC,OAAO,CAACe,IAAI,EAAIhB,AAF1kC,IAAI,CAE6kCC,OAAO,CAACe,IAAI,CAAC2J,WAAW,EAYxnC,OAXKlD,EAAMtH,MAAM,CAOT+H,EAASoC,IACTN,CAAAA,EAAQlI,CAAC,CAAGwI,CAAW,GAP3BN,EAAQY,MAAM,CAAGL,EACjBP,CAAO,CAAC,eAAe,CAAGS,EAC1BT,EAAQa,OAAO,CAAGF,EAClB,OAAOX,EAAQc,IAAI,EAOhBd,CACX,CACAe,cAActD,CAAK,CAAE,CACjB,IAAM0B,EAAQ,IAAI,CAACA,KAAK,CAAElJ,EAAU,IAAI,CAACA,OAAO,CAAEQ,EAAWgH,EAAMhH,QAAQ,CAAEY,EAASoG,EAAMpG,MAAM,CAAE2J,EAAY5C,EAAwBnI,EAAQwK,aAAa,CAAExK,EAAQe,IAAI,CAAC0J,SAAS,CAAE,GAAIO,EAAS7C,EAAwBnI,EAAQe,IAAI,CAACS,MAAM,CAAE,IAAMyJ,EAAO9C,EAAwBX,EAAMxH,OAAO,CAACe,IAAI,EAAIyG,EAAMxH,OAAO,CAACe,IAAI,CAACkK,IAAI,CAAEjL,EAAQe,IAAI,CAACkK,IAAI,EACtV,GAAIzK,EAASmI,SAAS,EAAIvH,EAAOuH,SAAS,CAAE,CACxC,IAAMuC,EAAgBlL,EAAQkL,aAAa,CAAEC,EAAenL,AAAwB,UAAxBA,EAAQoL,WAAW,CAAcC,EAAWjK,EAAOpB,OAAO,CAACwB,MAAM,CAAE8J,EAAgB,KAAKC,IAAI,CAACF,IAAaG,SAASH,EAAU,IAAKlC,EAAWD,EAAMC,QAAQ,CACnNjH,EAAK6F,EAAM,AAACvH,CAAAA,EAASmI,SAAS,CAAC1I,CAAC,EAAI,CAAA,EACnCO,CAAAA,EAASmI,SAAS,CAACK,KAAK,EAAI,CAAA,EAAI+B,GAAY5I,EAAK4F,EAAM,AAACvH,CAAAA,EAASmI,SAAS,CAAC7G,CAAC,EAAI,CAAA,EACjF,AAACtB,CAAAA,EAASmI,SAAS,CAACM,MAAM,EAAI,CAAA,EAAK,EAAG8B,GAAY3I,EAAK2F,EAAM3G,EAAOuH,SAAS,CAAC1I,CAAC,EAAI,EAAG8K,GAAY1I,EAAK0F,EAAM,AAAC3G,CAAAA,EAAOuH,SAAS,CAAC7G,CAAC,EAAI,CAAA,EACpI,AAACV,CAAAA,EAAOuH,SAAS,CAACM,MAAM,EAAI,CAAA,EAAK,EAAG8B,GAAYU,EAyCpD,GAxCItC,IACAjH,GAAO1B,EAASmI,SAAS,CAACK,KAAK,EAAI,EACnC5G,GAAOhB,EAAOuH,SAAS,CAACK,KAAK,EAAI,GAErCyC,EAAU,IAAI,CAACC,WAAW,CACtB3D,EAAM3F,EACF,AAAE+G,CAAAA,EAAW,EAAI,EAAC,EACb,CAAA,IAAI,CAACuC,WAAW,CAAG,IAAI,CAACC,SAAS,AAAD,EACjC,EAAGZ,GACXhD,EAAM,AAAC3F,CAAAA,EAAKF,CAAC,EAAK,EAAG6I,GAGrBO,GACCA,CAAAA,GAAiB,IAAMA,GAAiB,GAAE,IAC3CG,EAAUrJ,EAAK2F,EAAM3F,EAAK,AAAC+G,CAAAA,EAAW,IAAO,EAAE,EAC1C/H,CAAAA,EAAOuH,SAAS,CAACK,KAAK,EAAI,CAAA,EAAI+B,GACnC1I,EAAKjB,EAAOuH,SAAS,CAAC7G,CAAC,EAAI,EACvBwJ,EAAgB,GAChBjJ,CAAAA,GAAMjB,EAAOuH,SAAS,CAACM,MAAM,EAAI,CAAA,GAGrC7H,EAAOJ,SAAS,GAAKR,IACjB0I,EAAMC,QAAQ,EACdhH,EAAK,AAACgJ,EAIFpD,EAAM,AAACvH,CAAAA,EAASmI,SAAS,CAAC7G,CAAC,EAAI,CAAA,EAAKoJ,EAAgB,EAAGH,GAHvDhD,EAAM,AAACvH,CAAAA,EAASmI,SAAS,CAAC7G,CAAC,EAAI,CAAA,EAC1BtB,CAAAA,EAASmI,SAAS,CAACM,MAAM,EAAI,CAAA,EAC9BiC,EAAgB,EAAGH,GAE3B1I,EAAK,AAAC8I,EACgC,AAAC/J,CAAAA,EAAOuH,SAAS,CAAC7G,CAAC,EAAI,CAAA,EAAKoJ,EAAgB,EAD5D,AAAC9J,CAAAA,EAAOuH,SAAS,CAAC7G,CAAC,EAAI,CAAA,EACxCV,CAAAA,EAAOuH,SAAS,CAACM,MAAM,EAAI,CAAA,GAGhC9G,EAAK4F,EAAM,AAACvH,CAAAA,EAASmI,SAAS,CAAC7G,CAAC,EAAI,CAAA,EAAKoJ,EAAgB,EAAGH,GAEhEU,EAAUrJ,EAAK2F,EAAM,AAAC3G,CAAAA,EAAOuH,SAAS,CAAC1I,CAAC,EAAI,CAAA,EACxC,AAACmB,CAAAA,EAAOuH,SAAS,CAACK,KAAK,EAAI,CAAA,EAAK,EAAG+B,IAE3CvD,EAAMoE,KAAK,CAAGH,EACdjE,EAAMqE,KAAK,CAAG,AAAC1J,CAAAA,EAAKE,CAAC,EAAK,EAC1BmF,EAAMsE,SAAS,CAAG,OACdb,AAAS,aAATA,EACAzD,EAAMmB,SAAS,CAAG,CACdzK,EAAG,CACC,CAAC,IAAKgE,EAAIC,EAAG,CACb,CAAC,IAAKC,EAAIC,EAAG,CAChB,AACL,OAEC,GAAI4I,AAAS,WAATA,EAAmB,CACxB,IAAMzJ,EAASgB,KAAKE,GAAG,CAACN,EAAKF,GAAM8I,EAAU7B,CAAAA,EAAW,GAAK,CAAA,CAC7D3B,CAAAA,EAAMmB,SAAS,CAAG,CACdzK,EAAG,CACC,CAAC,IAAKgE,EAAIC,EAAG,CACb,CAAC,IAAKD,EAAKV,EAAQW,EAAIC,EAAKZ,EAAQa,EAAID,EAAIC,EAAG,CAClD,AACL,CACJ,MAEImF,EAAMmB,SAAS,CAAG,CACdzK,EAAGwD,EAAqBC,WAAW,CAAC,CAChC,CAAC,IAAKO,EAAIC,EAAG,CACb,CAAC,IAAKsJ,EAAStJ,EAAG,CAClB,CAAC,IAAKsJ,EAASpJ,EAAG,CAClB,CAAC,IAAKD,EAAIC,EAAG,CAChB,CAAE8F,EAAwBnI,EAAQ+L,UAAU,CAAE/L,EAAQe,IAAI,CAACiL,MAAM,EACtE,CAEJxE,CAAAA,EAAMyE,KAAK,CAAG,CACVhM,EAAG,AAACiC,CAAAA,EAAKE,CAAC,EAAK,EACfN,EAAG,AAACK,CAAAA,EAAKE,CAAC,EAAK,EACf4G,OAAQ8B,EACR/B,MAAO,CACX,CACJ,CACJ,CACAkD,cAAc3L,CAAI,CAAEG,CAAM,CAAE,CACxB,KAAK,CAACwL,cAAc3L,EAAMG,GAC1B,IAAMwI,EAAQ,IAAI,CAACA,KAAK,CAAElJ,EAAU,IAAI,CAACA,OAAO,CAAmEmM,EAAa3J,KAAK4J,GAAG,CAAC5J,KAAK6J,KAAK,CAACC,AAA5F/L,EAAKF,MAAM,GAAwB,IAAI,CAACkM,iBAAiB,EAA6DvM,EAAQwM,YAAY,EAAI,GAAIrB,EAAenL,AAAwB,UAAxBA,EAAQoL,WAAW,CAAcqB,EAASzM,EAAQkL,aAAa,EAAI,EAAGwB,EAAc1M,EAAQ2M,wBAAwB,CAAEC,EAAY5M,EAAQ6M,aAAa,EAAI,GAAIlB,EAAYnJ,KAAK6J,KAAK,CAAC,IAAI,CAACV,SAAS,EAAGhD,EAAYpI,EAAKoI,SAAS,CAAEmE,EAAO5D,EAAMC,QAAQ,CAAG,GAAK,EACrdE,EAAa9I,EAAKS,SAAS,CAC/B,GAAIqI,EACA,GAAIqD,AAAgB,eAAhBA,EAOA,IALA/D,EAAUM,MAAM,EAAIwD,EAEhBvD,EAAMC,QAAQ,EAAI,CAACgC,GACnBxC,CAAAA,EAAU7G,CAAC,EAAIgL,EAAOL,CAAK,EAExBpD,GAEHV,EAAU7G,CAAC,EAAI,AAACqJ,CAAAA,EAAe,EAAI2B,CAAG,EAAKL,EAC3CpD,EAAaA,EAAWrI,SAAS,MAGpC,GAAI0L,AAAgB,WAAhBA,EAEL,KAAOrD,GACHV,EAAUM,MAAM,CAAGwD,EAASG,GAC5BjE,EAAUM,MAAM,EAAIwD,EAGhB,CAAA,CAACvD,EAAMC,QAAQ,EAAIgC,CAAW,GAC9BxC,CAAAA,EAAU7G,CAAC,EAAI2K,CAAK,EAExBpD,EAAaA,EAAWrI,SAAS,MAMrC2H,EAAUM,MAAM,EAAIwD,EAChB,CAAA,CAACvD,EAAMC,QAAQ,EAAIgC,CAAW,GAC9BxC,CAAAA,EAAU7G,CAAC,EAAI2K,CAAK,CAIhClM,CAAAA,EAAK4L,UAAU,CAAGjD,EAAMC,QAAQ,CAC5BR,EAAUK,KAAK,CACfL,EAAUM,MAAM,CAEhB1I,EAAKoI,SAAS,EAAI,CAACpI,EAAKS,SAAS,EACjCT,CAAAA,EAAKoI,SAAS,CAAGT,EAAyB3H,EAAKoI,SAAS,CAAE,CACtD1I,EAAG,AAACM,CAAAA,EAAKoI,SAAS,CAAC1I,CAAC,EAAI,CAAA,EAAM0L,EAAY,EACrC,AAACpL,CAAAA,EAAKoI,SAAS,CAACK,KAAK,EAAI,CAAA,EAAK,EACnClH,EAAG,AAACvB,CAAAA,EAAKoI,SAAS,CAAC7G,CAAC,EAAI,CAAA,EAAMqK,EAAa,EACtC,AAAC5L,CAAAA,EAAKoI,SAAS,CAACM,MAAM,EAAI,CAAA,EAAK,CACxC,EAAC,CAET,CACA8D,gBAAiB,CACb,IAAMC,EAAY,IAAI,CAAChN,OAAO,CAAC8I,UAAU,CACzC,GAAIkE,EAAUC,YAAY,EAAID,EAAUC,YAAY,CAAC5J,OAAO,CACxD,IAAK,IAAMtC,KAAQ,IAAI,CAACmM,MAAM,CAC1BnM,EAAKf,OAAO,CAAC8I,UAAU,CAAGZ,EAAyBnH,EAAKf,OAAO,CAAC8I,UAAU,CAAE,CAAErB,QAAS,CAAA,CAAM,GAGrG,KAAK,CAACsF,gBACV,CACJ,CAMAvE,EAAmB2E,cAAc,CAAGjF,EAAyBL,EAAasF,cAAc,CAtiCrD,CAM/BC,YAAa,UAMb/C,aAAc,EAiBdtJ,KAAM,CAiBFwJ,MAAO,UAOPE,UAAW,EAQXuB,OAAQ,GAYRf,KAAM,SACV,EACApC,YAAa,EAMbC,WAAY,CAmBRuE,cAAe,WACX,IAAMC,EAAa,CACftE,MAAO,OACPC,OAAQ,OACRsE,QAAS,OACT,iBAAkB,MAClB,cAAe,SACf,kBAAmB,QACvB,EAAGC,EAAa,CACZ,aAAc,OACd,gBAAiB,KACrB,EAAGC,EAAa,CACZzE,MAAO,OACPD,QAAS,EACT,aAAc,SACd,cAAe,QACnB,EAYA,SAAS2E,EAAUC,CAAK,EACpB,OAAOpP,OAAOqP,IAAI,CAACD,GAAOE,MAAM,CAAC,SAAUC,CAAG,CAAEzP,CAAG,EAC/C,OAAOyP,EAAMzP,EAAM,IAAMsP,CAAK,CAACtP,EAAI,CAAG,GAC1C,EAAG,WAAa,GACpB,CACA,GAAM,CAAE0P,YAAAA,CAAW,CAAEC,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAE,CAAG,IAAI,CAACzG,KAAK,CAC5CwG,IACAR,CAAU,CAAC,YAAY,CAAG,MAC1BC,EAAWzE,KAAK,CAAG,OAInB,IAAI,CAACjJ,MAAM,CAACmJ,KAAK,CAACvF,QAAQ,CAACuK,SAAS,GACpCZ,EAAWC,OAAO,CAAG,QACrBE,EAAWU,QAAQ,CAAG,WACtBV,EAAWnE,IAAI,CAAG0E,EAAQ,MAAQ,EAClCP,EAAWlE,GAAG,CAAG,GAErB,IAAI6E,EAAO,QAAUV,EAAUJ,GAAc,IAoB7C,OAnBIU,GACAI,CAAAA,GAAQ,aAAeJ,EAAQ,KAC3BN,EAAUF,GAAc,GAAE,EAElCY,GAAQ,QAAUV,EAAUD,GAAc,IACtC,IAAI,CAACjG,KAAK,CAAC6G,IAAI,EACfD,CAAAA,GAAQ,OAASV,EArCN,CACXY,OAAQ,CACZ,GAmC4C,IACpC,IAAI,CAAC9G,KAAK,CAAC6G,IAAI,CAAG,OAAM,EAE5BJ,GACAG,CAAAA,GAAQ,MAAQV,EAvCJ,CACZY,OAAQ,CACZ,GAqC4C,IACnCL,CAAAA,GAAS,EAAC,EAAK,MAAK,EAEzBF,GACAK,CAAAA,GAAQ,MAAQV,EAzCE,CAClB9C,QAAS,IACT0D,OAAQ,KACZ,GAsCkD,IAC1CP,EAAc,MAAK,EAE3BK,GAAQ,cAGZ,EAEAT,MAAO,CAEHY,WAAY,SAEZC,SAAU,QAEVC,UAAW,MACf,EACAhH,QAAS,CAAA,EACTwF,aAAc,CACV3J,WAAY,CACRE,YAAa,MACbC,WAAY,KAChB,CACJ,CACJ,EAOAyH,cAAe,GA2BfyB,yBAA0B,UAa1BvB,YAAa,OAoCbyB,cAAe,GAUflB,UAAW,GACX+C,QAAS,CACLC,WAAY,sDAChB,CACJ,GA8wBA3G,EAA0BQ,EAAmB3J,SAAS,CAAE,CACpDU,WAAYM,CAChB,GACAR,IAA0IuP,kBAAkB,CAAC,eAAgBpG,GAiChJ,IAAMvJ,EAAqBE,IAG9C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}