{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/series-label\n * @requires highcharts\n *\n * (c) 2009-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Templating\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/series-label\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"Templating\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/series-label\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Templating\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Templating\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__984__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ }),\n\n/***/ 984:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__984__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ series_label_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Templating\"],\"commonjs\":[\"highcharts\",\"Templating\"],\"commonjs2\":[\"highcharts\",\"Templating\"],\"root\":[\"Highcharts\",\"Templating\"]}\nvar highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_ = __webpack_require__(984);\nvar highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default = /*#__PURE__*/__webpack_require__.n(highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_);\n;// ./code/es-modules/Extensions/SeriesLabel/SeriesLabelDefaults.js\n/* *\n *\n *  (c) 2009-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n/**\n * Series labels are placed as close to the series as possible in a\n * natural way, seeking to avoid other series. The goal of this\n * feature is to make the chart more easily readable, like if a\n * human designer placed the labels in the optimal position.\n *\n * The series labels currently work with series types having a\n * `graph` or an `area`.\n *\n * @sample highcharts/series-label/line-chart\n *         Line chart\n * @sample highcharts/demo/streamgraph\n *         Stream graph\n * @sample highcharts/series-label/stock-chart\n *         Stock chart\n *\n * @declare  Highcharts.SeriesLabelOptionsObject\n * @since    6.0.0\n * @product  highcharts highstock gantt\n * @requires modules/series-label\n * @optionparent plotOptions.series.label\n */\nconst SeriesLabelDefaults = {\n    /**\n     * Enable the series label per series.\n     */\n    enabled: true,\n    /**\n     * Allow labels to be placed distant to the graph if necessary,\n     * and draw a connector line to the graph. Setting this option\n     * to true may decrease the performance significantly, since the\n     * algorithm with systematically search for open spaces in the\n     * whole plot area. Visually, it may also result in a more\n     * cluttered chart, though more of the series will be labeled.\n     */\n    connectorAllowed: false,\n    /**\n     * If the label is closer than this to a neighbour graph, draw a\n     * connector.\n     */\n    connectorNeighbourDistance: 24,\n    /**\n     * A format string for the label, with support for a subset of\n     * HTML. Variables are enclosed by curly brackets. Available\n     * variables are `name`, `options.xxx`, `color` and other\n     * members from the `series` object. Use this option also to set\n     * a static text for the label.\n     *\n     * @type string\n     * @since 8.1.0\n     */\n    format: void 0,\n    /**\n     * Callback function to format each of the series' labels. The\n     * `this` keyword refers to the series object. By default the\n     * `formatter` is undefined and the `series.name` is rendered.\n     *\n     * @type {Highcharts.FormatterCallbackFunction<Series>}\n     * @since 8.1.0\n     */\n    formatter: void 0,\n    /**\n     * For area-like series, allow the font size to vary so that\n     * small areas get a smaller font size. The default applies this\n     * effect to area-like series but not line-like series.\n     *\n     * @sample highcharts/demo/streamgraph\n     *         Min and max font size on a streamgraph\n     * @type   {number|null}\n     */\n    minFontSize: null,\n    /**\n     * For area-like series, allow the font size to vary so that\n     * small areas get a smaller font size. The default applies this\n     * effect to area-like series but not line-like series.\n     *\n     * @sample highcharts/demo/streamgraph\n     *         Min and max font size on a streamgraph\n     *\n     * @type   {number|null}\n     */\n    maxFontSize: null,\n    /**\n     * Draw the label on the area of an area series. By default it\n     * is drawn on the area. Set it to `false` to draw it next to\n     * the graph instead.\n     *\n     * @type {boolean|null}\n     */\n    onArea: null,\n    /**\n     * Styles for the series label. The color defaults to the series\n     * color, or a contrast color if `onArea`.\n     *\n     * @type {Highcharts.CSSObject}\n     */\n    style: {\n        /**\n         * @type {number|string}\n         */\n        fontSize: '0.8em',\n        /** @internal */\n        fontWeight: 'bold'\n    },\n    /**\n     * Whether to use HTML to render the series label.\n     */\n    useHTML: false,\n    /**\n     * An array of boxes to avoid when laying out the labels. Each\n     * item has a `left`, `right`, `top` and `bottom` property.\n     *\n     * @type {Array<Highcharts.LabelIntersectBoxObject>}\n     */\n    boxesToAvoid: []\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const SeriesLabel_SeriesLabelDefaults = (SeriesLabelDefaults);\n\n;// ./code/es-modules/Extensions/SeriesLabel/SeriesLabelUtilities.js\n/* *\n *\n *  (c) 2009-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Counter-clockwise, part of the fast line intersection logic.\n *\n * @private\n * @function ccw\n */\nfunction ccw(x1, y1, x2, y2, x3, y3) {\n    const cw = ((y3 - y1) * (x2 - x1)) - ((y2 - y1) * (x3 - x1));\n    return cw > 0 ? true : !(cw < 0);\n}\n/**\n * Detect if two lines intersect.\n *\n * @private\n * @function intersectLine\n */\nfunction intersectLine(x1, y1, x2, y2, x3, y3, x4, y4) {\n    return ccw(x1, y1, x3, y3, x4, y4) !== ccw(x2, y2, x3, y3, x4, y4) &&\n        ccw(x1, y1, x2, y2, x3, y3) !== ccw(x1, y1, x2, y2, x4, y4);\n}\n/**\n * Detect if a box intersects with a line.\n *\n * @private\n * @function boxIntersectLine\n */\nfunction boxIntersectLine(x, y, w, h, x1, y1, x2, y2) {\n    return (intersectLine(x, y, x + w, y, x1, y1, x2, y2) || // Top of label\n        intersectLine(x + w, y, x + w, y + h, x1, y1, x2, y2) || // Right\n        intersectLine(x, y + h, x + w, y + h, x1, y1, x2, y2) || // Bottom\n        intersectLine(x, y, x, y + h, x1, y1, x2, y2) // Left of label\n    );\n}\n/**\n * @private\n */\nfunction intersectRect(r1, r2) {\n    return !(r2.left > r1.right ||\n        r2.right < r1.left ||\n        r2.top > r1.bottom ||\n        r2.bottom < r1.top);\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst SeriesLabelUtilities = {\n    boxIntersectLine,\n    intersectRect\n};\n/* harmony default export */ const SeriesLabel_SeriesLabelUtilities = (SeriesLabelUtilities);\n\n;// ./code/es-modules/Extensions/SeriesLabel/SeriesLabel.js\n/* *\n *\n *  (c) 2009-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n/*\n * Highcharts module to place labels next to a series in a natural position.\n *\n * TODO:\n * - add column support (box collision detection, boxesToAvoid logic)\n * - add more options (connector, format, formatter)\n *\n * https://jsfiddle.net/highcharts/L2u9rpwr/\n * https://jsfiddle.net/highcharts/y5A37/\n * https://jsfiddle.net/highcharts/264Nm/\n * https://jsfiddle.net/highcharts/y5A37/\n */\n\n\nconst { animObject } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { format } = (highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default());\n\nconst { setOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nconst { boxIntersectLine: SeriesLabel_boxIntersectLine, intersectRect: SeriesLabel_intersectRect } = SeriesLabel_SeriesLabelUtilities;\n\nconst { addEvent, extend, fireEvent, isNumber, pick, pushUnique, syncTimeout } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\nconst labelDistance = 3;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Check whether a proposed label position is clear of other elements.\n * @private\n */\nfunction checkClearPoint(series, x, y, bBox, checkDistance) {\n    const chart = series.chart, seriesLabelOptions = series.options.label || {}, onArea = pick(seriesLabelOptions.onArea, !!series.area), findDistanceToOthers = (onArea || seriesLabelOptions.connectorAllowed), leastDistance = 16, boxesToAvoid = chart.boxesToAvoid;\n    let distToOthersSquared = Number.MAX_VALUE, // Distance to other graphs\n    distToPointSquared = Number.MAX_VALUE, dist, connectorPoint, withinRange, xDist, yDist, i, j;\n    /**\n     * Get the weight in order to determine the ideal position. Larger distance\n     * to other series gives more weight. Smaller distance to the actual point\n     * (connector points only) gives more weight.\n     * @private\n     */\n    function getWeight(distToOthersSquared, distToPointSquared) {\n        return distToOthersSquared - distToPointSquared;\n    }\n    // First check for collision with existing labels\n    for (i = 0; boxesToAvoid && i < boxesToAvoid.length; i += 1) {\n        if (SeriesLabel_intersectRect(boxesToAvoid[i], {\n            left: x,\n            right: x + bBox.width,\n            top: y,\n            bottom: y + bBox.height\n        })) {\n            return false;\n        }\n    }\n    // For each position, check if the lines around the label intersect with any\n    // of the graphs.\n    for (i = 0; i < chart.series.length; i += 1) {\n        const serie = chart.series[i], points = serie.interpolatedPoints && [...serie.interpolatedPoints];\n        if (serie.visible && points) {\n            // Avoid the sides of the plot area\n            const stepY = chart.plotHeight / 10;\n            for (let chartY = chart.plotTop; chartY <= chart.plotTop + chart.plotHeight; chartY += stepY) {\n                points.unshift({\n                    chartX: chart.plotLeft,\n                    chartY\n                });\n                points.push({\n                    chartX: chart.plotLeft + chart.plotWidth,\n                    chartY\n                });\n            }\n            for (j = 1; j < points.length; j += 1) {\n                if (\n                // To avoid processing, only check intersection if the X\n                // values are close to the box.\n                points[j].chartX >= x - leastDistance &&\n                    points[j - 1].chartX <= x + bBox.width +\n                        leastDistance\n                /* @todo condition above is not the same as below\n                (\n                    points[j].chartX >=\n                    (x - leastDistance)\n                ) && (\n                    points[j - 1].chartX <=\n                    (x + bBox.width + leastDistance)\n                ) */\n                ) {\n                    // If any of the box sides intersect with the line, return.\n                    if (SeriesLabel_boxIntersectLine(x, y, bBox.width, bBox.height, points[j - 1].chartX, points[j - 1].chartY, points[j].chartX, points[j].chartY)) {\n                        return false;\n                    }\n                    // But if it is too far away (a padded box doesn't\n                    // intersect), also return.\n                    if (series === serie && !withinRange && checkDistance) {\n                        withinRange = SeriesLabel_boxIntersectLine(x - leastDistance, y - leastDistance, bBox.width + 2 * leastDistance, bBox.height + 2 * leastDistance, points[j - 1].chartX, points[j - 1].chartY, points[j].chartX, points[j].chartY);\n                    }\n                }\n                // Find the squared distance from the center of the label. On\n                // area series, avoid its own graph.\n                if ((findDistanceToOthers || withinRange) &&\n                    (series !== serie || onArea)) {\n                    xDist = x + bBox.width / 2 - points[j].chartX;\n                    yDist = y + bBox.height / 2 - points[j].chartY;\n                    distToOthersSquared = Math.min(distToOthersSquared, xDist * xDist + yDist * yDist);\n                }\n            }\n            // Do we need a connector?\n            if (!onArea &&\n                findDistanceToOthers &&\n                series === serie &&\n                ((checkDistance && !withinRange) ||\n                    distToOthersSquared < Math.pow(seriesLabelOptions.connectorNeighbourDistance || 1, 2))) {\n                for (j = 1; j < points.length; j += 1) {\n                    dist = Math.min((Math.pow(x + bBox.width / 2 - points[j].chartX, 2) +\n                        Math.pow(y + bBox.height / 2 - points[j].chartY, 2)), (Math.pow(x - points[j].chartX, 2) +\n                        Math.pow(y - points[j].chartY, 2)), (Math.pow(x + bBox.width - points[j].chartX, 2) +\n                        Math.pow(y - points[j].chartY, 2)), (Math.pow(x + bBox.width - points[j].chartX, 2) +\n                        Math.pow(y + bBox.height - points[j].chartY, 2)), (Math.pow(x - points[j].chartX, 2) +\n                        Math.pow(y + bBox.height - points[j].chartY, 2)));\n                    if (dist < distToPointSquared) {\n                        distToPointSquared = dist;\n                        connectorPoint = points[j];\n                    }\n                }\n                withinRange = true;\n            }\n        }\n    }\n    return !checkDistance || withinRange ? {\n        x,\n        y,\n        weight: getWeight(distToOthersSquared, connectorPoint ? distToPointSquared : 0),\n        connectorPoint\n    } : false;\n}\n/**\n * @private\n */\nfunction compose(ChartClass, SVGRendererClass) {\n    if (pushUnique(composed, 'SeriesLabel')) {\n        // Leave both events, we handle animation differently (#9815)\n        addEvent(ChartClass, 'load', onChartRedraw);\n        addEvent(ChartClass, 'redraw', onChartRedraw);\n        SVGRendererClass.prototype.symbols.connector = symbolConnector;\n        setOptions({ plotOptions: { series: { label: SeriesLabel_SeriesLabelDefaults } } });\n    }\n}\n/**\n * The main initialize method that runs on chart level after initialization and\n * redraw. It runs in  a timeout to prevent locking, and loops over all series,\n * taking all series and labels into account when placing the labels.\n *\n * @private\n * @function Highcharts.Chart#drawSeriesLabels\n */\nfunction drawSeriesLabels(chart) {\n    // Console.time('drawSeriesLabels');\n    chart.boxesToAvoid = [];\n    const labelSeries = chart.labelSeries || [], boxesToAvoid = chart.boxesToAvoid;\n    // Avoid data labels\n    chart.series.forEach((s) => (s.points || []).forEach((p) => (p.dataLabels || []).forEach((label) => {\n        const { width, height } = label.getBBox(), left = (label.translateX || 0) + (s.xAxis ? s.xAxis.pos : s.chart.plotLeft), top = (label.translateY || 0) + (s.yAxis ? s.yAxis.pos : s.chart.plotTop);\n        boxesToAvoid.push({\n            left,\n            top,\n            right: left + width,\n            bottom: top + height\n        });\n    })));\n    // Build the interpolated points\n    labelSeries.forEach(function (series) {\n        const seriesLabelOptions = series.options.label || {};\n        series.interpolatedPoints = getPointsOnGraph(series);\n        boxesToAvoid.push(...(seriesLabelOptions.boxesToAvoid || []));\n    });\n    chart.series.forEach(function (series) {\n        const labelOptions = series.options.label;\n        if (!labelOptions || (!series.xAxis && !series.yAxis)) {\n            return;\n        }\n        const colorClass = ('highcharts-color-' + pick(series.colorIndex, 'none')), isNew = !series.labelBySeries, minFontSize = labelOptions.minFontSize, maxFontSize = labelOptions.maxFontSize, inverted = chart.inverted, paneLeft = (inverted ? series.yAxis.pos : series.xAxis.pos), paneTop = (inverted ? series.xAxis.pos : series.yAxis.pos), paneWidth = chart.inverted ? series.yAxis.len : series.xAxis.len, paneHeight = chart.inverted ? series.xAxis.len : series.yAxis.len, points = series.interpolatedPoints, onArea = pick(labelOptions.onArea, !!series.area), results = [], xData = series.getColumn('x');\n        let bBox, x, y, clearPoint, i, best, label = series.labelBySeries, dataExtremes, areaMin, areaMax;\n        // Stay within the area data bounds (#10038)\n        if (onArea && !inverted) {\n            dataExtremes = [\n                series.xAxis.toPixels(xData[0]),\n                series.xAxis.toPixels(xData[xData.length - 1])\n            ];\n            areaMin = Math.min.apply(Math, dataExtremes);\n            areaMax = Math.max.apply(Math, dataExtremes);\n        }\n        /**\n         * @private\n         */\n        function insidePane(x, y, bBox) {\n            const leftBound = Math.max(paneLeft, pick(areaMin, -Infinity)), rightBound = Math.min(paneLeft + paneWidth, pick(areaMax, Infinity));\n            return (x > leftBound &&\n                x <= rightBound - bBox.width &&\n                y >= paneTop &&\n                y <= paneTop + paneHeight - bBox.height);\n        }\n        /**\n         * @private\n         */\n        function destroyLabel() {\n            if (label) {\n                series.labelBySeries = label.destroy();\n            }\n        }\n        if (series.visible && !series.boosted && points) {\n            if (!label) {\n                let labelText = series.name;\n                if (typeof labelOptions.format === 'string') {\n                    labelText = format(labelOptions.format, series, chart);\n                }\n                else if (labelOptions.formatter) {\n                    labelText = labelOptions.formatter.call(series);\n                }\n                series.labelBySeries = label = chart.renderer\n                    .label(labelText, 0, 0, 'connector', 0, 0, labelOptions.useHTML)\n                    .addClass('highcharts-series-label ' +\n                    'highcharts-series-label-' + series.index + ' ' +\n                    (series.options.className || '') + ' ' +\n                    colorClass);\n                if (!chart.renderer.styledMode) {\n                    const color = typeof series.color === 'string' ?\n                        series.color : \"#666666\" /* Palette.neutralColor60 */;\n                    label.css(extend({\n                        color: onArea ?\n                            chart.renderer.getContrast(color) :\n                            color\n                    }, labelOptions.style || {}));\n                    label.attr({\n                        opacity: chart.renderer.forExport ? 1 : 0,\n                        stroke: series.color,\n                        'stroke-width': 1\n                    });\n                }\n                // Adapt label sizes to the sum of the data\n                if (minFontSize && maxFontSize) {\n                    label.css({\n                        fontSize: labelFontSize(series, minFontSize, maxFontSize)\n                    });\n                }\n                label\n                    .attr({\n                    padding: 0,\n                    zIndex: 3\n                })\n                    .add();\n            }\n            bBox = label.getBBox();\n            bBox.width = Math.round(bBox.width);\n            // Ideal positions are centered above or below a point on right side\n            // of chart\n            for (i = points.length - 1; i > 0; i -= 1) {\n                if (onArea) {\n                    // Centered\n                    x = (points[i].chartCenterX ?? points[i].chartX) -\n                        bBox.width / 2;\n                    y = (points[i].chartCenterY ?? points[i].chartY) -\n                        bBox.height / 2;\n                    if (insidePane(x, y, bBox)) {\n                        best = checkClearPoint(series, x, y, bBox);\n                    }\n                    if (best) {\n                        results.push(best);\n                    }\n                }\n                else {\n                    // Right - up\n                    x = points[i].chartX + labelDistance;\n                    y = points[i].chartY - bBox.height - labelDistance;\n                    if (insidePane(x, y, bBox)) {\n                        best = checkClearPoint(series, x, y, bBox, true);\n                    }\n                    if (best) {\n                        results.push(best);\n                    }\n                    // Right - down\n                    x = points[i].chartX + labelDistance;\n                    y = points[i].chartY + labelDistance;\n                    if (insidePane(x, y, bBox)) {\n                        best = checkClearPoint(series, x, y, bBox, true);\n                    }\n                    if (best) {\n                        results.push(best);\n                    }\n                    // Left - down\n                    x = points[i].chartX - bBox.width - labelDistance;\n                    y = points[i].chartY + labelDistance;\n                    if (insidePane(x, y, bBox)) {\n                        best = checkClearPoint(series, x, y, bBox, true);\n                    }\n                    if (best) {\n                        results.push(best);\n                    }\n                    // Left - up\n                    x = points[i].chartX - bBox.width - labelDistance;\n                    y = points[i].chartY - bBox.height - labelDistance;\n                    if (insidePane(x, y, bBox)) {\n                        best = checkClearPoint(series, x, y, bBox, true);\n                    }\n                    if (best) {\n                        results.push(best);\n                    }\n                }\n            }\n            // Brute force, try all positions on the chart in a 16x16 grid\n            if (labelOptions.connectorAllowed && !results.length && !onArea) {\n                for (x = paneLeft + paneWidth - bBox.width; x >= paneLeft; x -= 16) {\n                    for (y = paneTop; y < paneTop + paneHeight - bBox.height; y += 16) {\n                        clearPoint = checkClearPoint(series, x, y, bBox, true);\n                        if (clearPoint) {\n                            results.push(clearPoint);\n                        }\n                    }\n                }\n            }\n            if (results.length) {\n                results.sort((a, b) => b.weight - a.weight);\n                best = results[0];\n                (chart.boxesToAvoid || []).push({\n                    left: best.x,\n                    right: best.x + bBox.width,\n                    top: best.y,\n                    bottom: best.y + bBox.height\n                });\n                // Move it if needed\n                const dist = Math.sqrt(Math.pow(Math.abs(best.x - (label.x || 0)), 2) +\n                    Math.pow(Math.abs(best.y - (label.y || 0)), 2));\n                if (dist && series.labelBySeries) {\n                    // Move fast and fade in - pure animation movement is\n                    // distractive...\n                    let attr = {\n                        opacity: chart.renderer.forExport ? 1 : 0,\n                        x: best.x,\n                        y: best.y\n                    }, anim = {\n                        opacity: 1\n                    };\n                    // ... unless we're just moving a short distance\n                    if (dist <= 10) {\n                        anim = {\n                            x: attr.x,\n                            y: attr.y\n                        };\n                        attr = {};\n                    }\n                    // Default initial animation to a fraction of the series\n                    // animation (#9396)\n                    let animationOptions;\n                    if (isNew) {\n                        animationOptions = animObject(series.options.animation);\n                        animationOptions.duration *= 0.2;\n                    }\n                    series.labelBySeries\n                        .attr(extend(attr, {\n                        anchorX: best.connectorPoint &&\n                            (best.connectorPoint.plotX || 0) + paneLeft,\n                        anchorY: best.connectorPoint &&\n                            (best.connectorPoint.plotY || 0) + paneTop\n                    }))\n                        .animate(anim, animationOptions);\n                    // Record closest point to stick to for sync redraw\n                    series.options.kdNow = true;\n                    series.buildKDTree();\n                    const closest = series.searchPoint({\n                        chartX: best.x,\n                        chartY: best.y\n                    }, true);\n                    if (closest) {\n                        label.closest = [\n                            closest,\n                            best.x - (closest.plotX || 0),\n                            best.y - (closest.plotY || 0)\n                        ];\n                    }\n                }\n            }\n            else {\n                destroyLabel();\n            }\n        }\n        else {\n            destroyLabel();\n        }\n    });\n    fireEvent(chart, 'afterDrawSeriesLabels');\n    // Console.timeEnd('drawSeriesLabels');\n}\n/**\n * Points to avoid. In addition to actual data points, the label should avoid\n * interpolated positions.\n *\n * @private\n * @function Highcharts.Series#getPointsOnGraph\n */\nfunction getPointsOnGraph(series) {\n    if (!series.xAxis && !series.yAxis) {\n        return;\n    }\n    const distance = 16, points = series.points, interpolated = [], graph = series.graph || series.area, node = graph && graph.element, inverted = series.chart.inverted, xAxis = series.xAxis, yAxis = series.yAxis, paneLeft = inverted ? yAxis.pos : xAxis.pos, paneTop = inverted ? xAxis.pos : yAxis.pos, paneHeight = inverted ? xAxis.len : yAxis.len, paneWidth = inverted ? yAxis.len : xAxis.len, seriesLabelOptions = series.options.label || {}, onArea = pick(seriesLabelOptions.onArea, !!series.area), translatedThreshold = yAxis.getThreshold(series.options.threshold), grid = {}, chartCenterKey = inverted ? 'chartCenterX' : 'chartCenterY';\n    let i, deltaX, deltaY, delta, len, n, j;\n    /**\n     * Push the point to the interpolated points, but only if that position in\n     * the grid has not been occupied. As a performance optimization, we divide\n     * the plot area into a grid and only add one point per series (#9815).\n     * @private\n     */\n    function pushDiscrete(point) {\n        const cellSize = 8, key = Math.round((point.plotX || 0) / cellSize) + ',' +\n            Math.round((point.plotY || 0) / cellSize);\n        if (!grid[key]) {\n            grid[key] = 1;\n            interpolated.push(point);\n        }\n    }\n    // For splines, get the point at length (possible caveat: peaks are not\n    // correctly detected)\n    if (series.getPointSpline &&\n        node &&\n        node.getPointAtLength &&\n        !onArea &&\n        // Not performing well on complex series, node.getPointAtLength is too\n        // heavy (#9815)\n        points.length < (series.chart.plotSizeX || 0) / distance) {\n        // If it is animating towards a path definition, use that briefly, and\n        // reset\n        const d = graph.toD && graph.attr('d');\n        if (graph.toD) {\n            graph.attr({ d: graph.toD });\n        }\n        len = node.getTotalLength();\n        for (i = 0; i < len; i += distance) {\n            const domPoint = node.getPointAtLength(i), plotX = inverted ? paneWidth - domPoint.y : domPoint.x, plotY = inverted ? paneHeight - domPoint.x : domPoint.y;\n            pushDiscrete({\n                chartX: paneLeft + plotX,\n                chartY: paneTop + plotY,\n                plotX,\n                plotY\n            });\n        }\n        if (d) {\n            graph.attr({ d });\n        }\n        // Last point\n        const point = points[points.length - 1], pos = point.pos();\n        pushDiscrete({\n            chartX: paneLeft + (pos?.[0] || 0),\n            chartY: paneTop + (pos?.[1] || 0)\n        });\n        // Interpolate\n    }\n    else {\n        len = points.length;\n        let last;\n        for (i = 0; i < len; i += 1) {\n            const point = points[i], [plotX, plotY] = point.pos() || [], { plotHigh } = point;\n            if (isNumber(plotX) && isNumber(plotY)) {\n                const ctlPoint = {\n                    plotX,\n                    plotY,\n                    // Absolute coordinates so we can compare different panes\n                    chartX: paneLeft + plotX,\n                    chartY: paneTop + plotY\n                };\n                if (onArea) {\n                    // Vertically centered inside area\n                    if (plotHigh) {\n                        ctlPoint.plotY = plotHigh;\n                        ctlPoint.chartY = paneTop + plotHigh;\n                    }\n                    if (inverted) {\n                        ctlPoint.chartCenterX = paneLeft + paneWidth - ((plotHigh ? plotHigh : point.plotY || 0) +\n                            pick(point.yBottom, translatedThreshold)) / 2;\n                    }\n                    else {\n                        ctlPoint.chartCenterY = paneTop + ((plotHigh ? plotHigh : plotY) +\n                            pick(point.yBottom, translatedThreshold)) / 2;\n                    }\n                }\n                // Add interpolated points\n                if (last) {\n                    deltaX = Math.abs(ctlPoint.chartX - last.chartX);\n                    deltaY = Math.abs(ctlPoint.chartY - last.chartY);\n                    delta = Math.max(deltaX, deltaY);\n                    if (delta > distance && delta < 999) {\n                        n = Math.ceil(delta / distance);\n                        for (j = 1; j < n; j += 1) {\n                            pushDiscrete({\n                                chartX: last.chartX +\n                                    (ctlPoint.chartX - last.chartX) * (j / n),\n                                chartY: last.chartY +\n                                    (ctlPoint.chartY - last.chartY) * (j / n),\n                                [chartCenterKey]: (last[chartCenterKey] || 0) +\n                                    ((ctlPoint[chartCenterKey] || 0) -\n                                        (last[chartCenterKey] || 0)) * (j / n),\n                                plotX: (last.plotX || 0) +\n                                    (plotX - (last.plotX || 0)) * (j / n),\n                                plotY: (last.plotY || 0) +\n                                    (plotY - (last.plotY || 0)) * (j / n)\n                            });\n                        }\n                    }\n                }\n                // Add the real point in order to find positive and negative\n                // peaks\n                pushDiscrete(ctlPoint);\n                last = ctlPoint;\n            }\n        }\n    }\n    // Get the bounding box so we can do a quick check first if the bounding\n    // boxes overlap.\n    /*\n    interpolated.bBox = node.getBBox();\n    interpolated.bBox.x += paneLeft;\n    interpolated.bBox.y += paneTop;\n    */\n    return interpolated;\n}\n/**\n * Overridable function to return series-specific font sizes for the labels. By\n * default it returns bigger font sizes for series with the greater sum of y\n * values.\n * @private\n */\nfunction labelFontSize(series, minFontSize, maxFontSize) {\n    return minFontSize + (((series.sum || 0) / (series.chart.labelSeriesMaxSum || 0)) *\n        (maxFontSize - minFontSize)) + 'px';\n}\n/**\n * Prepare drawing series labels.\n * @private\n */\nfunction onChartRedraw(e) {\n    if (this.renderer) {\n        const chart = this;\n        let delay = animObject(chart.renderer.globalAnimation).duration;\n        chart.labelSeries = [];\n        chart.labelSeriesMaxSum = 0;\n        if (chart.seriesLabelTimer) {\n            highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().clearTimeout(chart.seriesLabelTimer);\n        }\n        // Which series should have labels\n        chart.series.forEach(function (series) {\n            const seriesLabelOptions = series.options.label || {}, label = series.labelBySeries, closest = label && label.closest, yData = series.getColumn('y');\n            if (seriesLabelOptions.enabled &&\n                series.visible &&\n                (series.graph || series.area) &&\n                !series.boosted &&\n                chart.labelSeries) {\n                chart.labelSeries.push(series);\n                if (seriesLabelOptions.minFontSize &&\n                    seriesLabelOptions.maxFontSize &&\n                    yData.length) {\n                    series.sum = yData.reduce((pv, cv) => (pv || 0) + (cv || 0), 0);\n                    chart.labelSeriesMaxSum = Math.max(chart.labelSeriesMaxSum || 0, series.sum || 0);\n                }\n                // The labels are processing heavy, wait until the animation is\n                // done\n                if (e.type === 'load') {\n                    delay = Math.max(delay, animObject(series.options.animation).duration);\n                }\n                // Keep the position updated to the axis while redrawing\n                if (closest) {\n                    if (typeof closest[0].plotX !== 'undefined') {\n                        label.animate({\n                            x: closest[0].plotX + closest[1],\n                            y: closest[0].plotY + closest[2]\n                        });\n                    }\n                    else {\n                        label.attr({ opacity: 0 });\n                    }\n                }\n            }\n        });\n        chart.seriesLabelTimer = syncTimeout(function () {\n            if (chart.series && chart.labelSeries) { // #7931, chart destroyed\n                drawSeriesLabels(chart);\n            }\n        }, chart.renderer.forExport || !delay ? 0 : delay);\n    }\n}\n/**\n * General symbol definition for labels with connector.\n * @private\n */\nfunction symbolConnector(x, y, w, h, options) {\n    const anchorX = options && options.anchorX, anchorY = options && options.anchorY;\n    let path, yOffset, lateral = w / 2;\n    if (isNumber(anchorX) && isNumber(anchorY)) {\n        path = [['M', anchorX, anchorY]];\n        // Prefer 45 deg connectors\n        yOffset = y - anchorY;\n        if (yOffset < 0) {\n            yOffset = -h - yOffset;\n        }\n        if (yOffset < w) {\n            lateral = anchorX < x + (w / 2) ? yOffset : w - yOffset;\n        }\n        // Anchor below label\n        if (anchorY > y + h) {\n            path.push(['L', x + lateral, y + h]);\n            // Anchor above label\n        }\n        else if (anchorY < y) {\n            path.push(['L', x + lateral, y]);\n            // Anchor left of label\n        }\n        else if (anchorX < x) {\n            path.push(['L', x, y + h / 2]);\n            // Anchor right of label\n        }\n        else if (anchorX > x + w) {\n            path.push(['L', x + w, y + h / 2]);\n        }\n    }\n    return path || [];\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst SeriesLabel = {\n    compose\n};\n/* harmony default export */ const SeriesLabel_SeriesLabel = (SeriesLabel);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Containing the position of a box that should be avoided by labels.\n *\n * @interface Highcharts.LabelIntersectBoxObject\n */ /**\n* @name Highcharts.LabelIntersectBoxObject#bottom\n* @type {number}\n*/ /**\n* @name Highcharts.LabelIntersectBoxObject#left\n* @type {number}\n*/ /**\n* @name Highcharts.LabelIntersectBoxObject#right\n* @type {number}\n*/ /**\n* @name Highcharts.LabelIntersectBoxObject#top\n* @type {number}\n*/\n(''); // Keeps doclets above in JS file\n\n;// ./code/es-modules/masters/modules/series-label.js\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nSeriesLabel_SeriesLabel.compose(G.Chart, G.SVGRenderer);\n/* harmony default export */ const series_label_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__984__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "series_label_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_", "highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default", "SeriesLabel_SeriesLabelDefaults", "enabled", "connectorAllowed", "connectorNeighbourDistance", "format", "formatter", "minFontSize", "maxFontSize", "onArea", "style", "fontSize", "fontWeight", "useHTML", "boxesToAvoid", "ccw", "x1", "y1", "x2", "y2", "x3", "y3", "cw", "intersectLine", "x4", "y4", "animObject", "setOptions", "composed", "boxIntersectLine", "SeriesLabel_boxIntersectLine", "intersectRect", "SeriesLabel_intersectRect", "x", "y", "w", "h", "r1", "r2", "left", "right", "top", "bottom", "addEvent", "extend", "fireEvent", "isNumber", "pick", "pushUnique", "syncTimeout", "checkClearPoint", "series", "bBox", "checkDistance", "chart", "seriesLabelOptions", "options", "label", "area", "findDistanceToOthers", "distToOthersSquared", "Number", "MAX_VALUE", "distToPointSquared", "dist", "connectorPoint", "withinRange", "xDist", "yDist", "i", "j", "length", "width", "height", "serie", "points", "interpolatedPoints", "visible", "stepY", "plotHeight", "chartY", "plotTop", "unshift", "chartX", "plotLeft", "push", "plot<PERSON>id<PERSON>", "Math", "min", "pow", "weight", "onChartRedraw", "e", "renderer", "delay", "globalAnimation", "duration", "labelSeries", "labelSeriesMaxSum", "seriesLabelTimer", "clearTimeout", "for<PERSON>ach", "labelBySeries", "closest", "yData", "getColumn", "graph", "boosted", "sum", "reduce", "pv", "cv", "max", "type", "animation", "plotX", "animate", "plotY", "attr", "opacity", "drawSeriesLabels", "s", "p", "dataLabels", "getBBox", "translateX", "xAxis", "pos", "translateY", "yAxis", "getPointsOnGraph", "delta", "len", "interpolated", "node", "element", "inverted", "paneLeft", "paneTop", "paneHeight", "paneWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "get<PERSON><PERSON><PERSON>old", "threshold", "grid", "chartCenterKey", "pushDiscrete", "point", "round", "getPointSpline", "getPointAtLength", "plotSizeX", "toD", "getTotalLength", "domPoint", "last", "plotHigh", "ctlPoint", "chartCenterX", "yBottom", "chartCenterY", "abs", "ceil", "labelOptions", "colorClass", "colorIndex", "isNew", "results", "xData", "clearPoint", "best", "dataExtremes", "areaMin", "areaMax", "insidePane", "leftBound", "Infinity", "rightBound", "destroyLabel", "destroy", "toPixels", "apply", "labelText", "name", "addClass", "index", "className", "styledMode", "color", "css", "getContrast", "forExport", "stroke", "padding", "zIndex", "add", "sort", "b", "sqrt", "animationOptions", "anim", "anchorX", "anchorY", "kdNow", "buildKDTree", "searchPoint", "symbolConnector", "path", "yOffset", "lateral", "G", "SeriesLabel_SeriesLabel", "compose", "ChartClass", "SVGRendererClass", "symbols", "connector", "plotOptions", "Chart", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "CASA,AATA;;;;;;;;CAQC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,UAAa,EACxE,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,kCAAmC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,UAAa,CAAE,GACvH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,kCAAkC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,UAAa,EAE3GA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,UAAa,CACnF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,AAACL,IAERA,EAAOD,OAAO,CAAGO,CAEX,CAEI,EAGIE,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmHzB,EAAoB,KACvI0B,EAAuI1B,EAAoBI,CAAC,CAACqB,GA0IpI,IAAME,EAnGP,CAIxBC,QAAS,CAAA,EASTC,iBAAkB,CAAA,EAKlBC,2BAA4B,GAW5BC,OAAQ,KAAK,EASbC,UAAW,KAAK,EAUhBC,YAAa,KAWbC,YAAa,KAQbC,OAAQ,KAORC,MAAO,CAIHC,SAAU,QAEVC,WAAY,MAChB,EAIAC,QAAS,CAAA,EAOTC,aAAc,EAAE,AACpB,EA8BA,SAASC,EAAIC,CAAE,CAAEC,CAAE,CAAEC,CAAE,CAAEC,CAAE,CAAEC,CAAE,CAAEC,CAAE,EAC/B,IAAMC,EAAK,AAAED,CAAAA,EAAKJ,CAAC,EAAMC,CAAAA,EAAKF,CAAC,EAAO,AAACG,CAAAA,EAAKF,CAAC,EAAMG,CAAAA,EAAKJ,CAAC,EACzD,OAAOM,EAAK,GAAW,CAAEA,CAAAA,EAAK,CAAA,CAClC,CAOA,SAASC,EAAcP,CAAE,CAAEC,CAAE,CAAEC,CAAE,CAAEC,CAAE,CAAEC,CAAE,CAAEC,CAAE,CAAEG,CAAE,CAAEC,CAAE,EACjD,OAAOV,EAAIC,EAAIC,EAAIG,EAAIC,EAAIG,EAAIC,KAAQV,EAAIG,EAAIC,EAAIC,EAAIC,EAAIG,EAAIC,IAC3DV,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,KAAQN,EAAIC,EAAIC,EAAIC,EAAIC,EAAIK,EAAIC,EAChE,CA0DA,GAAM,CAAEC,WAAAA,CAAU,CAAE,CAAI5B,IAElB,CAAEO,OAAAA,CAAM,CAAE,CAAIL,IAEd,CAAE2B,WAAAA,CAAU,CAAE,CAAI7B,IAElB,CAAE8B,SAAAA,CAAQ,CAAE,CAAI9B,IAGhB,CAAE+B,iBAAkBC,CAA4B,CAAEC,cAAeC,CAAyB,CAAE,CAvCrE,CACzBH,iBAtBJ,SAA0BI,CAAC,CAAEC,CAAC,CAAEC,CAAC,CAAEC,CAAC,CAAEpB,CAAE,CAAEC,CAAE,CAAEC,CAAE,CAAEC,CAAE,EAChD,OAAQI,EAAcU,EAAGC,EAAGD,EAAIE,EAAGD,EAAGlB,EAAIC,EAAIC,EAAIC,IAC9CI,EAAcU,EAAIE,EAAGD,EAAGD,EAAIE,EAAGD,EAAIE,EAAGpB,EAAIC,EAAIC,EAAIC,IAClDI,EAAcU,EAAGC,EAAIE,EAAGH,EAAIE,EAAGD,EAAIE,EAAGpB,EAAIC,EAAIC,EAAIC,IAClDI,EAAcU,EAAGC,EAAGD,EAAGC,EAAIE,EAAGpB,EAAIC,EAAIC,EAAIC,EAElD,EAiBIY,cAbJ,SAAuBM,CAAE,CAAEC,CAAE,EACzB,MAAO,CAAEA,CAAAA,EAAGC,IAAI,CAAGF,EAAGG,KAAK,EACvBF,EAAGE,KAAK,CAAGH,EAAGE,IAAI,EAClBD,EAAGG,GAAG,CAAGJ,EAAGK,MAAM,EAClBJ,EAAGI,MAAM,CAAGL,EAAGI,GAAG,AAAD,CACzB,CASA,EAsCM,CAAEE,SAAAA,CAAQ,CAAEC,OAAAA,CAAM,CAAEC,UAAAA,CAAS,CAAEC,SAAAA,CAAQ,CAAEC,KAAAA,CAAI,CAAEC,WAAAA,CAAU,CAAEC,YAAAA,CAAW,CAAE,CAAInD,IAgBlF,SAASoD,EAAgBC,CAAM,CAAElB,CAAC,CAAEC,CAAC,CAAEkB,CAAI,CAAEC,CAAa,EACtD,IAAMC,EAAQH,EAAOG,KAAK,CAAEC,EAAqBJ,EAAOK,OAAO,CAACC,KAAK,EAAI,CAAC,EAAGhD,EAASsC,EAAKQ,EAAmB9C,MAAM,CAAE,CAAC,CAAC0C,EAAOO,IAAI,EAAGC,EAAwBlD,GAAU8C,EAAmBpD,gBAAgB,CAAuBW,EAAewC,EAAMxC,YAAY,CAC/P8C,EAAsBC,OAAOC,SAAS,CAC1CC,EAAqBF,OAAOC,SAAS,CAAEE,EAAMC,EAAgBC,EAAaC,EAAOC,EAAOC,EAAGC,EAW3F,IAAKD,EAAI,EAAGvD,GAAgBuD,EAAIvD,EAAayD,MAAM,CAAEF,GAAK,EACtD,GAAIrC,EAA0BlB,CAAY,CAACuD,EAAE,CAAE,CAC3C9B,KAAMN,EACNO,MAAOP,EAAImB,EAAKoB,KAAK,CACrB/B,IAAKP,EACLQ,OAAQR,EAAIkB,EAAKqB,MAAM,AAC3B,GACI,MAAO,CAAA,EAKf,IAAKJ,EAAI,EAAGA,EAAIf,EAAMH,MAAM,CAACoB,MAAM,CAAEF,GAAK,EAAG,CACzC,IAAMK,EAAQpB,EAAMH,MAAM,CAACkB,EAAE,CAAEM,EAASD,EAAME,kBAAkB,EAAI,IAAIF,EAAME,kBAAkB,CAAC,CACjG,GAAIF,EAAMG,OAAO,EAAIF,EAAQ,CAEzB,IAAMG,EAAQxB,EAAMyB,UAAU,CAAG,GACjC,IAAK,IAAIC,EAAS1B,EAAM2B,OAAO,CAAED,GAAU1B,EAAM2B,OAAO,CAAG3B,EAAMyB,UAAU,CAAEC,GAAUF,EACnFH,EAAOO,OAAO,CAAC,CACXC,OAAQ7B,EAAM8B,QAAQ,CACtBJ,OAAAA,CACJ,GACAL,EAAOU,IAAI,CAAC,CACRF,OAAQ7B,EAAM8B,QAAQ,CAAG9B,EAAMgC,SAAS,CACxCN,OAAAA,CACJ,GAEJ,IAAKV,EAAI,EAAGA,EAAIK,EAAOJ,MAAM,CAAED,GAAK,EAAG,CACnC,GAGAK,CAAM,CAACL,EAAE,CAACa,MAAM,EAAIlD,EA5C8L,IA6C9M0C,CAAM,CAACL,EAAI,EAAE,CAACa,MAAM,EAAIlD,EAAImB,EAAKoB,KAAK,CA7CwK,GAuDhN,CAEE,GAAI1C,EAA6BG,EAAGC,EAAGkB,EAAKoB,KAAK,CAAEpB,EAAKqB,MAAM,CAAEE,CAAM,CAACL,EAAI,EAAE,CAACa,MAAM,CAAER,CAAM,CAACL,EAAI,EAAE,CAACU,MAAM,CAAEL,CAAM,CAACL,EAAE,CAACa,MAAM,CAAER,CAAM,CAACL,EAAE,CAACU,MAAM,EAC1I,MAAO,CAAA,CAIP7B,CAAAA,IAAWuB,GAAS,CAACR,GAAeb,GACpCa,CAAAA,EAAcpC,EAA6BG,EA/D+J,GA+D5IC,EA/D4I,GA+DzHkB,EAAKoB,KAAK,CAAG,GAAmBpB,EAAKqB,MAAM,CAAG,GAAmBE,CAAM,CAACL,EAAI,EAAE,CAACa,MAAM,CAAER,CAAM,CAACL,EAAI,EAAE,CAACU,MAAM,CAAEL,CAAM,CAACL,EAAE,CAACa,MAAM,CAAER,CAAM,CAACL,EAAE,CAACU,MAAM,CAAA,CAExO,CAGKrB,CAAAA,GAAwBO,CAAU,GAClCf,CAAAA,IAAWuB,GAASjE,CAAK,IAG1BmD,EAAsB2B,KAAKC,GAAG,CAAC5B,EAAqBO,AAFpDA,CAAAA,EAAQlC,EAAImB,EAAKoB,KAAK,CAAG,EAAIG,CAAM,CAACL,EAAE,CAACa,MAAM,AAAD,EAEgBhB,EAAQC,AADpEA,CAAAA,EAAQlC,EAAIkB,EAAKqB,MAAM,CAAG,EAAIE,CAAM,CAACL,EAAE,CAACU,MAAM,AAAD,EAC+BZ,GAEpF,CAEA,GAAI,CAAC3D,GACDkD,GACAR,IAAWuB,GACV,CAAA,AAACrB,GAAiB,CAACa,GAChBN,EAAsB2B,KAAKE,GAAG,CAAClC,EAAmBnD,0BAA0B,EAAI,EAAG,EAAC,EAAI,CAC5F,IAAKkE,EAAI,EAAGA,EAAIK,EAAOJ,MAAM,CAAED,GAAK,EAChCN,CAAAA,EAAOuB,KAAKC,GAAG,CAAED,KAAKE,GAAG,CAACxD,EAAImB,EAAKoB,KAAK,CAAG,EAAIG,CAAM,CAACL,EAAE,CAACa,MAAM,CAAE,GAC7DI,KAAKE,GAAG,CAACvD,EAAIkB,EAAKqB,MAAM,CAAG,EAAIE,CAAM,CAACL,EAAE,CAACU,MAAM,CAAE,GAAMO,KAAKE,GAAG,CAACxD,EAAI0C,CAAM,CAACL,EAAE,CAACa,MAAM,CAAE,GACtFI,KAAKE,GAAG,CAACvD,EAAIyC,CAAM,CAACL,EAAE,CAACU,MAAM,CAAE,GAAMO,KAAKE,GAAG,CAACxD,EAAImB,EAAKoB,KAAK,CAAGG,CAAM,CAACL,EAAE,CAACa,MAAM,CAAE,GACjFI,KAAKE,GAAG,CAACvD,EAAIyC,CAAM,CAACL,EAAE,CAACU,MAAM,CAAE,GAAMO,KAAKE,GAAG,CAACxD,EAAImB,EAAKoB,KAAK,CAAGG,CAAM,CAACL,EAAE,CAACa,MAAM,CAAE,GACjFI,KAAKE,GAAG,CAACvD,EAAIkB,EAAKqB,MAAM,CAAGE,CAAM,CAACL,EAAE,CAACU,MAAM,CAAE,GAAMO,KAAKE,GAAG,CAACxD,EAAI0C,CAAM,CAACL,EAAE,CAACa,MAAM,CAAE,GAClFI,KAAKE,GAAG,CAACvD,EAAIkB,EAAKqB,MAAM,CAAGE,CAAM,CAACL,EAAE,CAACU,MAAM,CAAE,GAAG,EACzCjB,IACPA,EAAqBC,EACrBC,EAAiBU,CAAM,CAACL,EAAE,EAGlCJ,EAAc,CAAA,CAClB,CACJ,CACJ,CACA,MAAO,CAAA,CAACb,KAAiBa,CAAU,GAAI,CACnCjC,EAAAA,EACAC,EAAAA,EACAwD,OA1FO9B,AA0FWA,EAAqBK,CAAAA,EAAiBF,EAAqB,CAAA,EAC7EE,eAAAA,CACJ,CACJ,CAkZA,SAAS0B,EAAcC,CAAC,EACpB,GAAI,IAAI,CAACC,QAAQ,CAAE,CACf,IAAMvC,EAAQ,IAAI,CACdwC,EAAQpE,EAAW4B,EAAMuC,QAAQ,CAACE,eAAe,EAAEC,QAAQ,AAC/D1C,CAAAA,EAAM2C,WAAW,CAAG,EAAE,CACtB3C,EAAM4C,iBAAiB,CAAG,EACtB5C,EAAM6C,gBAAgB,EACtBrG,IAA8EsG,YAAY,CAAC9C,EAAM6C,gBAAgB,EAGrH7C,EAAMH,MAAM,CAACkD,OAAO,CAAC,SAAUlD,CAAM,EACjC,IAAMI,EAAqBJ,EAAOK,OAAO,CAACC,KAAK,EAAI,CAAC,EAAGA,EAAQN,EAAOmD,aAAa,CAAEC,EAAU9C,GAASA,EAAM8C,OAAO,CAAEC,EAAQrD,EAAOsD,SAAS,CAAC,IAC5IlD,CAAAA,EAAmBrD,OAAO,EAC1BiD,EAAO0B,OAAO,EACb1B,CAAAA,EAAOuD,KAAK,EAAIvD,EAAOO,IAAI,AAAD,GAC3B,CAACP,EAAOwD,OAAO,EACfrD,EAAM2C,WAAW,GACjB3C,EAAM2C,WAAW,CAACZ,IAAI,CAAClC,GACnBI,EAAmBhD,WAAW,EAC9BgD,EAAmB/C,WAAW,EAC9BgG,EAAMjC,MAAM,GACZpB,EAAOyD,GAAG,CAAGJ,EAAMK,MAAM,CAAC,CAACC,EAAIC,IAAO,AAACD,CAAAA,GAAM,CAAA,EAAMC,CAAAA,GAAM,CAAA,EAAI,GAC7DzD,EAAM4C,iBAAiB,CAAGX,KAAKyB,GAAG,CAAC1D,EAAM4C,iBAAiB,EAAI,EAAG/C,EAAOyD,GAAG,EAAI,IAI/EhB,AAAW,SAAXA,EAAEqB,IAAI,EACNnB,CAAAA,EAAQP,KAAKyB,GAAG,CAAClB,EAAOpE,EAAWyB,EAAOK,OAAO,CAAC0D,SAAS,EAAElB,QAAQ,CAAA,EAGrEO,IACI,AAA4B,KAAA,IAArBA,CAAO,CAAC,EAAE,CAACY,KAAK,CACvB1D,EAAM2D,OAAO,CAAC,CACVnF,EAAGsE,CAAO,CAAC,EAAE,CAACY,KAAK,CAAGZ,CAAO,CAAC,EAAE,CAChCrE,EAAGqE,CAAO,CAAC,EAAE,CAACc,KAAK,CAAGd,CAAO,CAAC,EAAE,AACpC,GAGA9C,EAAM6D,IAAI,CAAC,CAAEC,QAAS,CAAE,IAIxC,GACAjE,EAAM6C,gBAAgB,CAAGlD,EAAY,WAC7BK,EAAMH,MAAM,EAAIG,EAAM2C,WAAW,EACjCuB,AA1ahB,SAA0BlE,CAAK,EAE3BA,EAAMxC,YAAY,CAAG,EAAE,CACvB,IAAMmF,EAAc3C,EAAM2C,WAAW,EAAI,EAAE,CAAEnF,EAAewC,EAAMxC,YAAY,CAE9EwC,EAAMH,MAAM,CAACkD,OAAO,CAAC,AAACoB,GAAM,AAACA,CAAAA,EAAE9C,MAAM,EAAI,EAAE,AAAD,EAAG0B,OAAO,CAAC,AAACqB,GAAM,AAACA,CAAAA,EAAEC,UAAU,EAAI,EAAE,AAAD,EAAGtB,OAAO,CAAC,AAAC5C,IACtF,GAAM,CAAEe,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAE,CAAGhB,EAAMmE,OAAO,GAAIrF,EAAO,AAACkB,CAAAA,EAAMoE,UAAU,EAAI,CAAA,EAAMJ,CAAAA,EAAEK,KAAK,CAAGL,EAAEK,KAAK,CAACC,GAAG,CAAGN,EAAEnE,KAAK,CAAC8B,QAAQ,AAAD,EAAI3C,EAAM,AAACgB,CAAAA,EAAMuE,UAAU,EAAI,CAAA,EAAMP,CAAAA,EAAEQ,KAAK,CAAGR,EAAEQ,KAAK,CAACF,GAAG,CAAGN,EAAEnE,KAAK,CAAC2B,OAAO,AAAD,EAC/LnE,EAAauE,IAAI,CAAC,CACd9C,KAAAA,EACAE,IAAAA,EACAD,MAAOD,EAAOiC,EACd9B,OAAQD,EAAMgC,CAClB,EACJ,KAEAwB,EAAYI,OAAO,CAAC,SAAUlD,CAAM,EAChC,IAAMI,EAAqBJ,EAAOK,OAAO,CAACC,KAAK,EAAI,CAAC,CACpDN,CAAAA,EAAOyB,kBAAkB,CAAGsD,AAkOpC,SAA0B/E,CAAM,MAKxBkB,EAAmB8D,EAAOC,EAAK1J,EAAG4F,EAJtC,GAAI,CAACnB,EAAO2E,KAAK,EAAI,CAAC3E,EAAO8E,KAAK,CAC9B,OAEJ,IAAqBtD,EAASxB,EAAOwB,MAAM,CAAE0D,EAAe,EAAE,CAAE3B,EAAQvD,EAAOuD,KAAK,EAAIvD,EAAOO,IAAI,CAAE4E,EAAO5B,GAASA,EAAM6B,OAAO,CAAEC,EAAWrF,EAAOG,KAAK,CAACkF,QAAQ,CAAEV,EAAQ3E,EAAO2E,KAAK,CAAEG,EAAQ9E,EAAO8E,KAAK,CAAEQ,EAAWD,EAAWP,EAAMF,GAAG,CAAGD,EAAMC,GAAG,CAAEW,EAAUF,EAAWV,EAAMC,GAAG,CAAGE,EAAMF,GAAG,CAAEY,EAAaH,EAAWV,EAAMM,GAAG,CAAGH,EAAMG,GAAG,CAAEQ,EAAYJ,EAAWP,EAAMG,GAAG,CAAGN,EAAMM,GAAG,CAAmD3H,EAASsC,EAAKQ,AAA1CJ,CAAAA,EAAOK,OAAO,CAACC,KAAK,EAAI,CAAC,CAAA,EAAoChD,MAAM,CAAE,CAAC,CAAC0C,EAAOO,IAAI,EAAGmF,EAAsBZ,EAAMa,YAAY,CAAC3F,EAAOK,OAAO,CAACuF,SAAS,EAAGC,EAAO,CAAC,EAAGC,EAAiBT,EAAW,eAAiB,eAQ9mB,SAASU,EAAaC,CAAK,EACvB,IAAoBnK,EAAMuG,KAAK6D,KAAK,CAAC,AAACD,CAAAA,EAAMhC,KAAK,EAAI,CAAA,EAApC,GAAqD,IAClE5B,KAAK6D,KAAK,CAAC,AAACD,CAAAA,EAAM9B,KAAK,EAAI,CAAA,EADd,EAEZ2B,CAAAA,CAAI,CAAChK,EAAI,GACVgK,CAAI,CAAChK,EAAI,CAAG,EACZqJ,EAAahD,IAAI,CAAC8D,GAE1B,CAGA,GAAIhG,EAAOkG,cAAc,EACrBf,GACAA,EAAKgB,gBAAgB,EACrB,CAAC7I,GAGDkE,EAAOJ,MAAM,CAAG,AAACpB,CAAAA,EAAOG,KAAK,CAACiG,SAAS,EAAI,CAAA,EAxB9B,GAwB6C,CAG1D,IAAM1K,EAAI6H,EAAM8C,GAAG,EAAI9C,EAAMY,IAAI,CAAC,KAKlC,IAJIZ,EAAM8C,GAAG,EACT9C,EAAMY,IAAI,CAAC,CAAEzI,EAAG6H,EAAM8C,GAAG,AAAC,GAE9BpB,EAAME,EAAKmB,cAAc,GACpBpF,EAAI,EAAGA,EAAI+D,EAAK/D,GAhCR,GAgCuB,CAChC,IAAMqF,EAAWpB,EAAKgB,gBAAgB,CAACjF,GAAI8C,EAAQqB,EAAWI,EAAYc,EAASxH,CAAC,CAAGwH,EAASzH,CAAC,CAAEoF,EAAQmB,EAAWG,EAAae,EAASzH,CAAC,CAAGyH,EAASxH,CAAC,CAC1JgH,EAAa,CACT/D,OAAQsD,EAAWtB,EACnBnC,OAAQ0D,EAAUrB,EAClBF,MAAAA,EACAE,MAAAA,CACJ,EACJ,CACIxI,GACA6H,EAAMY,IAAI,CAAC,CAAEzI,EAAAA,CAAE,GAGnB,IAAyCkJ,EAAMoB,AAAjCxE,CAAM,CAACA,EAAOJ,MAAM,CAAG,EAAE,CAAcwD,GAAG,GACxDmB,EAAa,CACT/D,OAAQsD,EAAYV,CAAAA,GAAK,CAAC,EAAE,EAAI,CAAA,EAChC/C,OAAQ0D,EAAWX,CAAAA,GAAK,CAAC,EAAE,EAAI,CAAA,CACnC,EAEJ,KACK,KAEG4B,EACJ,IAAKtF,EAAI,EAFT+D,EAAMzD,EAAOJ,MAAM,CAEPF,EAAI+D,EAAK/D,GAAK,EAAG,CACzB,IAAM8E,EAAQxE,CAAM,CAACN,EAAE,CAAE,CAAC8C,EAAOE,EAAM,CAAG8B,EAAMpB,GAAG,IAAM,EAAE,CAAE,CAAE6B,SAAAA,CAAQ,CAAE,CAAGT,EAC5E,GAAIrG,EAASqE,IAAUrE,EAASuE,GAAQ,CACpC,IAAMwC,EAAW,CACb1C,MAAAA,EACAE,MAAAA,EAEAlC,OAAQsD,EAAWtB,EACnBnC,OAAQ0D,EAAUrB,CACtB,EAiBA,GAhBI5G,IAEImJ,IACAC,EAASxC,KAAK,CAAGuC,EACjBC,EAAS7E,MAAM,CAAG0D,EAAUkB,GAE5BpB,EACAqB,EAASC,YAAY,CAAGrB,EAAWG,EAAY,AAAC,CAAA,AAACgB,CAAAA,GAAsBT,EAAM9B,KAAK,EAAI,CAAA,EAClFtE,EAAKoG,EAAMY,OAAO,CAAElB,EAAmB,EAAK,EAGhDgB,EAASG,YAAY,CAAGtB,EAAU,AAAC,CAAA,AAACkB,CAAAA,GAAsBvC,CAAI,EAC1DtE,EAAKoG,EAAMY,OAAO,CAAElB,EAAmB,EAAK,GAIpDc,GAIIxB,AADJA,CAAAA,EAAQ5C,KAAKyB,GAAG,CAFPzB,KAAK0E,GAAG,CAACJ,EAAS1E,MAAM,CAAGwE,EAAKxE,MAAM,EACtCI,KAAK0E,GAAG,CAACJ,EAAS7E,MAAM,CAAG2E,EAAK3E,MAAM,EAChB,EApF9B,IAqFuBmD,EAAQ,IAE5B,IAAK7D,EAAI,EADT5F,EAAI6G,KAAK2E,IAAI,CAAC/B,EAtFjB,IAuFe7D,EAAI5F,EAAG4F,GAAK,EACpB4E,EAAa,CACT/D,OAAQwE,EAAKxE,MAAM,CACf,AAAC0E,CAAAA,EAAS1E,MAAM,CAAGwE,EAAKxE,MAAM,AAAD,EAAMb,CAAAA,EAAI5F,CAAAA,EAC3CsG,OAAQ2E,EAAK3E,MAAM,CACf,AAAC6E,CAAAA,EAAS7E,MAAM,CAAG2E,EAAK3E,MAAM,AAAD,EAAMV,CAAAA,EAAI5F,CAAAA,EAC3C,CAACuK,EAAe,CAAE,AAACU,CAAAA,CAAI,CAACV,EAAe,EAAI,CAAA,EACvC,AAAC,CAAA,AAACY,CAAAA,CAAQ,CAACZ,EAAe,EAAI,CAAA,EACzBU,CAAAA,CAAI,CAACV,EAAe,EAAI,CAAA,CAAC,EAAM3E,CAAAA,EAAI5F,CAAAA,EAC5CyI,MAAO,AAACwC,CAAAA,EAAKxC,KAAK,EAAI,CAAA,EAClB,AAACA,CAAAA,EAASwC,CAAAA,EAAKxC,KAAK,EAAI,CAAA,CAAC,EAAM7C,CAAAA,EAAI5F,CAAAA,EACvC2I,MAAO,AAACsC,CAAAA,EAAKtC,KAAK,EAAI,CAAA,EAClB,AAACA,CAAAA,EAASsC,CAAAA,EAAKtC,KAAK,EAAI,CAAA,CAAC,EAAM/C,CAAAA,EAAI5F,CAAAA,CAC3C,GAMZwK,EAAaW,GACbF,EAAOE,CACX,CACJ,CACJ,CAQA,OAAOxB,CACX,EA7VqDlF,GAC7CrC,EAAauE,IAAI,IAAK9B,EAAmBzC,YAAY,EAAI,EAAE,CAC/D,GACAwC,EAAMH,MAAM,CAACkD,OAAO,CAAC,SAAUlD,CAAM,EACjC,IAAMgH,EAAehH,EAAOK,OAAO,CAACC,KAAK,CACzC,GAAI,CAAC0G,GAAiB,CAAChH,EAAO2E,KAAK,EAAI,CAAC3E,EAAO8E,KAAK,CAChD,OAEJ,IAAMmC,EAAc,oBAAsBrH,EAAKI,EAAOkH,UAAU,CAAE,QAAUC,EAAQ,CAACnH,EAAOmD,aAAa,CAAE/F,EAAc4J,EAAa5J,WAAW,CAAEC,EAAc2J,EAAa3J,WAAW,CAAEgI,EAAWlF,EAAMkF,QAAQ,CAAEC,EAAYD,EAAWrF,EAAO8E,KAAK,CAACF,GAAG,CAAG5E,EAAO2E,KAAK,CAACC,GAAG,CAAGW,EAAWF,EAAWrF,EAAO2E,KAAK,CAACC,GAAG,CAAG5E,EAAO8E,KAAK,CAACF,GAAG,CAAGa,EAAYtF,EAAMkF,QAAQ,CAAGrF,EAAO8E,KAAK,CAACG,GAAG,CAAGjF,EAAO2E,KAAK,CAACM,GAAG,CAAEO,EAAarF,EAAMkF,QAAQ,CAAGrF,EAAO2E,KAAK,CAACM,GAAG,CAAGjF,EAAO8E,KAAK,CAACG,GAAG,CAAEzD,EAASxB,EAAOyB,kBAAkB,CAAEnE,EAASsC,EAAKoH,EAAa1J,MAAM,CAAE,CAAC,CAAC0C,EAAOO,IAAI,EAAG6G,EAAU,EAAE,CAAEC,EAAQrH,EAAOsD,SAAS,CAAC,KAC9kBrD,EAAMnB,EAAGC,EAAGuI,EAAYpG,EAAGqG,EAAMjH,EAAQN,EAAOmD,aAAa,CAAEqE,EAAcC,EAASC,EAa1F,SAASC,EAAW7I,CAAC,CAAEC,CAAC,CAAEkB,CAAI,EAC1B,IAAM2H,EAAYxF,KAAKyB,GAAG,CAACyB,EAAU1F,EAAK6H,EAAS,CAACI,MAAYC,EAAa1F,KAAKC,GAAG,CAACiD,EAAWG,EAAW7F,EAAK8H,EAASG,MAC1H,OAAQ/I,EAAI8I,GACR9I,GAAKgJ,EAAa7H,EAAKoB,KAAK,EAC5BtC,GAAKwG,GACLxG,GAAKwG,EAAUC,EAAavF,EAAKqB,MAAM,AAC/C,CAIA,SAASyG,IACDzH,GACAN,CAAAA,EAAOmD,aAAa,CAAG7C,EAAM0H,OAAO,EAAC,CAE7C,CACA,GA1BI1K,GAAU,CAAC+H,IACXmC,EAAe,CACXxH,EAAO2E,KAAK,CAACsD,QAAQ,CAACZ,CAAK,CAAC,EAAE,EAC9BrH,EAAO2E,KAAK,CAACsD,QAAQ,CAACZ,CAAK,CAACA,EAAMjG,MAAM,CAAG,EAAE,EAChD,CACDqG,EAAUrF,KAAKC,GAAG,CAAC6F,KAAK,CAAC9F,KAAMoF,GAC/BE,EAAUtF,KAAKyB,GAAG,CAACqE,KAAK,CAAC9F,KAAMoF,IAoB/BxH,EAAO0B,OAAO,EAAI,CAAC1B,EAAOwD,OAAO,EAAIhC,EAAQ,CAC7C,GAAI,CAAClB,EAAO,KA8TDN,EAAQ5C,EAAaC,EA7T5B,IAAI8K,EAAYnI,EAAOoI,IAAI,CAa3B,GAZI,AAA+B,UAA/B,OAAOpB,EAAa9J,MAAM,CAC1BiL,EAAYjL,EAAO8J,EAAa9J,MAAM,CAAE8C,EAAQG,GAE3C6G,EAAa7J,SAAS,EAC3BgL,CAAAA,EAAYnB,EAAa7J,SAAS,CAACZ,IAAI,CAACyD,EAAM,EAElDA,EAAOmD,aAAa,CAAG7C,EAAQH,EAAMuC,QAAQ,CACxCpC,KAAK,CAAC6H,EAAW,EAAG,EAAG,YAAa,EAAG,EAAGnB,EAAatJ,OAAO,EAC9D2K,QAAQ,CAAC,mDACmBrI,EAAOsI,KAAK,CAAG,IAC3CtI,CAAAA,EAAOK,OAAO,CAACkI,SAAS,EAAI,EAAC,EAAK,IACnCtB,GACA,CAAC9G,EAAMuC,QAAQ,CAAC8F,UAAU,CAAE,CAC5B,IAAMC,EAAQ,AAAwB,UAAxB,OAAOzI,EAAOyI,KAAK,CAC7BzI,EAAOyI,KAAK,CAAG,UACnBnI,EAAMoI,GAAG,CAACjJ,EAAO,CACbgJ,MAAOnL,EACH6C,EAAMuC,QAAQ,CAACiG,WAAW,CAACF,GAC3BA,CACR,EAAGzB,EAAazJ,KAAK,EAAI,CAAC,IAC1B+C,EAAM6D,IAAI,CAAC,CACPC,QAASjE,GAAAA,EAAMuC,QAAQ,CAACkG,SAAS,CACjCC,OAAQ7I,EAAOyI,KAAK,CACpB,eAAgB,CACpB,EACJ,CAEIrL,GAAeC,GACfiD,EAAMoI,GAAG,CAAC,CACNlL,QAAQ,EA+RTwC,EA/RyBA,EA+RjB5C,EA/RyBA,EA+RZC,EA/RyBA,EAgS1DD,EAAe,AAAE4C,CAAAA,EAAOyD,GAAG,EAAI,CAAA,EAAMzD,CAAAA,EAAOG,KAAK,CAAC4C,iBAAiB,EAAI,CAAA,EACzE1F,CAAAA,EAAcD,CAAU,EAAM,KAhSnB,GAEJkD,EACK6D,IAAI,CAAC,CACN2E,QAAS,EACTC,OAAQ,CACZ,GACKC,GAAG,EACZ,CAKA,IAHA/I,AADAA,CAAAA,EAAOK,EAAMmE,OAAO,EAAC,EAChBpD,KAAK,CAAGe,KAAK6D,KAAK,CAAChG,EAAKoB,KAAK,EAG7BH,EAAIM,EAAOJ,MAAM,CAAG,EAAGF,EAAI,EAAGA,GAAK,EAChC5D,EAMIqK,EAJJ7I,EAAI,AAAC0C,CAAAA,CAAM,CAACN,EAAE,CAACyF,YAAY,EAAInF,CAAM,CAACN,EAAE,CAACc,MAAM,AAAD,EAC1C/B,EAAKoB,KAAK,CAAG,EACjBtC,EAAI,AAACyC,CAAAA,CAAM,CAACN,EAAE,CAAC2F,YAAY,EAAIrF,CAAM,CAACN,EAAE,CAACW,MAAM,AAAD,EAC1C5B,EAAKqB,MAAM,CAAG,EACGrB,IACjBsH,CAAAA,EAAOxH,EAAgBC,EAAQlB,EAAGC,EAAGkB,EAAI,GAUzC0H,EAFJ7I,EAAI0C,CAAM,CAACN,EAAE,CAACc,MAAM,CA3PlB,EA4PFjD,EAAIyC,CAAM,CAACN,EAAE,CAACW,MAAM,CAAG5B,EAAKqB,MAAM,CA5PhC,EA6PmBrB,IACjBsH,CAAAA,EAAOxH,EAAgBC,EAAQlB,EAAGC,EAAGkB,EAAM,CAAA,EAAI,EAE/CsH,GACAH,EAAQlF,IAAI,CAACqF,GAKbI,EAFJ7I,EAAI0C,CAAM,CAACN,EAAE,CAACc,MAAM,CApQlB,EAqQFjD,EAAIyC,CAAM,CAACN,EAAE,CAACW,MAAM,CArQlB,EAsQmB5B,IACjBsH,CAAAA,EAAOxH,EAAgBC,EAAQlB,EAAGC,EAAGkB,EAAM,CAAA,EAAI,EAE/CsH,GACAH,EAAQlF,IAAI,CAACqF,GAKbI,EAFJ7I,EAAI0C,CAAM,CAACN,EAAE,CAACc,MAAM,CAAG/B,EAAKoB,KAAK,CA7Q/B,EA8QFtC,EAAIyC,CAAM,CAACN,EAAE,CAACW,MAAM,CA9QlB,EA+QmB5B,IACjBsH,CAAAA,EAAOxH,EAAgBC,EAAQlB,EAAGC,EAAGkB,EAAM,CAAA,EAAI,EAE/CsH,GACAH,EAAQlF,IAAI,CAACqF,GAKbI,EAFJ7I,EAAI0C,CAAM,CAACN,EAAE,CAACc,MAAM,CAAG/B,EAAKoB,KAAK,CAtR/B,EAuRFtC,EAAIyC,CAAM,CAACN,EAAE,CAACW,MAAM,CAAG5B,EAAKqB,MAAM,CAvRhC,EAwRmBrB,IACjBsH,CAAAA,EAAOxH,EAAgBC,EAAQlB,EAAGC,EAAGkB,EAAM,CAAA,EAAI,GAE/CsH,GACAH,EAAQlF,IAAI,CAACqF,GAKzB,GAAIP,EAAahK,gBAAgB,EAAI,CAACoK,EAAQhG,MAAM,EAAI,CAAC9D,EACrD,IAAKwB,EAAIwG,EAAWG,EAAYxF,EAAKoB,KAAK,CAAEvC,GAAKwG,EAAUxG,GAAK,GAC5D,IAAKC,EAAIwG,EAASxG,EAAIwG,EAAUC,EAAavF,EAAKqB,MAAM,CAAEvC,GAAK,GAEvDuI,AADJA,CAAAA,EAAavH,EAAgBC,EAAQlB,EAAGC,EAAGkB,EAAM,CAAA,EAAI,GAEjDmH,EAAQlF,IAAI,CAACoF,GAK7B,GAAIF,EAAQhG,MAAM,CAAE,CAChBgG,EAAQ6B,IAAI,CAAC,CAACtN,EAAGuN,IAAMA,EAAE3G,MAAM,CAAG5G,EAAE4G,MAAM,EAC1CgF,EAAOH,CAAO,CAAC,EAAE,CACjB,AAACjH,CAAAA,EAAMxC,YAAY,EAAI,EAAE,AAAD,EAAGuE,IAAI,CAAC,CAC5B9C,KAAMmI,EAAKzI,CAAC,CACZO,MAAOkI,EAAKzI,CAAC,CAAGmB,EAAKoB,KAAK,CAC1B/B,IAAKiI,EAAKxI,CAAC,CACXQ,OAAQgI,EAAKxI,CAAC,CAAGkB,EAAKqB,MAAM,AAChC,GAEA,IAAMT,EAAOuB,KAAK+G,IAAI,CAAC/G,KAAKE,GAAG,CAACF,KAAK0E,GAAG,CAACS,EAAKzI,CAAC,CAAIwB,CAAAA,EAAMxB,CAAC,EAAI,CAAA,GAAK,GAC/DsD,KAAKE,GAAG,CAACF,KAAK0E,GAAG,CAACS,EAAKxI,CAAC,CAAIuB,CAAAA,EAAMvB,CAAC,EAAI,CAAA,GAAK,IAChD,GAAI8B,GAAQb,EAAOmD,aAAa,CAAE,CAG9B,IAiBIiG,EAjBAjF,EAAO,CACPC,QAASjE,GAAAA,EAAMuC,QAAQ,CAACkG,SAAS,CACjC9J,EAAGyI,EAAKzI,CAAC,CACTC,EAAGwI,EAAKxI,CAAC,AACb,EAAGsK,EAAO,CACNjF,QAAS,CACb,EAEIvD,GAAQ,KACRwI,EAAO,CACHvK,EAAGqF,EAAKrF,CAAC,CACTC,EAAGoF,EAAKpF,CAAC,AACb,EACAoF,EAAO,CAAC,GAKRgD,IACAiC,EAAmB7K,EAAWyB,EAAOK,OAAO,CAAC0D,SAAS,EACtDqF,EAAiBvG,QAAQ,EAAI,IAEjC7C,EAAOmD,aAAa,CACfgB,IAAI,CAAC1E,EAAO0E,EAAM,CACnBmF,QAAS/B,EAAKzG,cAAc,EACxB,AAACyG,CAAAA,EAAKzG,cAAc,CAACkD,KAAK,EAAI,CAAA,EAAKsB,EACvCiE,QAAShC,EAAKzG,cAAc,EACxB,AAACyG,CAAAA,EAAKzG,cAAc,CAACoD,KAAK,EAAI,CAAA,EAAKqB,CAC3C,IACKtB,OAAO,CAACoF,EAAMD,GAEnBpJ,EAAOK,OAAO,CAACmJ,KAAK,CAAG,CAAA,EACvBxJ,EAAOyJ,WAAW,GAClB,IAAMrG,EAAUpD,EAAO0J,WAAW,CAAC,CAC/B1H,OAAQuF,EAAKzI,CAAC,CACd+C,OAAQ0F,EAAKxI,CAAC,AAClB,EAAG,CAAA,EACCqE,CAAAA,GACA9C,CAAAA,EAAM8C,OAAO,CAAG,CACZA,EACAmE,EAAKzI,CAAC,CAAIsE,CAAAA,EAAQY,KAAK,EAAI,CAAA,EAC3BuD,EAAKxI,CAAC,CAAIqE,CAAAA,EAAQc,KAAK,EAAI,CAAA,EAC9B,AAAD,CAER,CACJ,MAEI6D,GAER,MAEIA,GAER,GACArI,EAAUS,EAAO,wBAErB,EA+LiCA,EAEzB,EAAGA,EAAMuC,QAAQ,CAACkG,SAAS,EAAI,CAACjG,EAAQ,EAAIA,EAChD,CACJ,CAKA,SAASgH,EAAgB7K,CAAC,CAAEC,CAAC,CAAEC,CAAC,CAAEC,CAAC,CAAEoB,CAAO,EACxC,IAAMiJ,EAAUjJ,GAAWA,EAAQiJ,OAAO,CAAEC,EAAUlJ,GAAWA,EAAQkJ,OAAO,CAC5EK,EAAMC,EAASC,EAAU9K,EAAI,EA4BjC,OA3BIW,EAAS2J,IAAY3J,EAAS4J,KAC9BK,EAAO,CAAC,CAAC,IAAKN,EAASC,EAAQ,CAAC,CAG5BM,AADJA,CAAAA,EAAU9K,EAAIwK,CAAM,EACN,GACVM,CAAAA,EAAU,CAAC5K,EAAI4K,CAAM,EAErBA,EAAU7K,GACV8K,CAAAA,EAAUR,EAAUxK,EAAKE,EAAI,EAAK6K,EAAU7K,EAAI6K,CAAM,EAGtDN,EAAUxK,EAAIE,EACd2K,EAAK1H,IAAI,CAAC,CAAC,IAAKpD,EAAIgL,EAAS/K,EAAIE,EAAE,EAG9BsK,EAAUxK,EACf6K,EAAK1H,IAAI,CAAC,CAAC,IAAKpD,EAAIgL,EAAS/K,EAAE,EAG1BuK,EAAUxK,EACf8K,EAAK1H,IAAI,CAAC,CAAC,IAAKpD,EAAGC,EAAIE,EAAI,EAAE,EAGxBqK,EAAUxK,EAAIE,GACnB4K,EAAK1H,IAAI,CAAC,CAAC,IAAKpD,EAAIE,EAAGD,EAAIE,EAAI,EAAE,GAGlC2K,GAAQ,EAAE,AACrB,CAuCA,IAAMG,EAAKpN,IACXqN,AAlCoB,CAAA,CAChBC,QA1eJ,SAAiBC,CAAU,CAAEC,CAAgB,EACrCtK,EAAWpB,EAAU,iBAErBe,EAAS0K,EAAY,OAAQ1H,GAC7BhD,EAAS0K,EAAY,SAAU1H,GAC/B2H,EAAiB9N,SAAS,CAAC+N,OAAO,CAACC,SAAS,CAAGV,EAC/CnL,EAAW,CAAE8L,YAAa,CAAEtK,OAAQ,CAAEM,MAAOxD,CAAgC,CAAE,CAAE,GAEzF,CAmeA,CAAA,EAgCwBmN,OAAO,CAACF,EAAEQ,KAAK,CAAER,EAAES,WAAW,EACzB,IAAM/N,EAAqBE,IAG9C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}