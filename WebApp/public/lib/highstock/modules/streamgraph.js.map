{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/streamgraph\n * @requires highcharts\n *\n * Streamgraph module\n *\n * (c) 2010-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/streamgraph\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/streamgraph\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ streamgraph_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/Streamgraph/StreamgraphSeriesDefaults.js\n/* *\n *\n *  Streamgraph module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A streamgraph is a type of stacked area graph which is displaced around a\n * central axis, resulting in a flowing, organic shape.\n *\n * @sample {highcharts|highstock} highcharts/demo/streamgraph/\n *         Streamgraph\n *\n * @extends      plotOptions.areaspline\n * @since        6.0.0\n * @product      highcharts highstock\n * @requires     modules/streamgraph\n * @optionparent plotOptions.streamgraph\n */\nconst StreamgraphSeriesDefaults = {\n    /**\n     * @see [fillColor](#plotOptions.streamgraph.fillColor)\n     * @see [fillOpacity](#plotOptions.streamgraph.fillOpacity)\n     *\n     * @apioption plotOptions.streamgraph.color\n     */\n    /**\n     * @see [color](#plotOptions.streamgraph.color)\n     * @see [fillOpacity](#plotOptions.streamgraph.fillOpacity)\n     *\n     * @apioption plotOptions.streamgraph.fillColor\n     */\n    /**\n     * @see [color](#plotOptions.streamgraph.color)\n     * @see [fillColor](#plotOptions.streamgraph.fillColor)\n     *\n     * @apioption plotOptions.streamgraph.fillOpacity\n     */\n    fillOpacity: 1,\n    lineWidth: 0,\n    marker: {\n        enabled: false\n    },\n    stacking: 'stream'\n};\n/**\n * A `streamgraph` series. If the [type](#series.streamgraph.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.streamgraph\n * @excluding dataParser, dataURL, step, boostThreshold, boostBlending\n * @product   highcharts highstock\n * @requires  modules/streamgraph\n * @apioption series.streamgraph\n */\n/**\n * @see [fillColor](#series.streamgraph.fillColor)\n * @see [fillOpacity](#series.streamgraph.fillOpacity)\n *\n * @apioption series.streamgraph.color\n */\n/**\n * An array of data points for the series. For the `streamgraph` series type,\n * points can be given in the following ways:\n *\n * 1. An array of numerical values. In this case, the numerical values will be\n *    interpreted as `y` options. The `x` values will be automatically\n *    calculated, either starting at 0 and incremented by 1, or from\n *    `pointStart` and `pointInterval` given in the series options. If the axis\n *    has categories, these will be used. Example:\n *    ```js\n *    data: [0, 5, 3, 5]\n *    ```\n *\n * 2. An array of arrays with 2 values. In this case, the values correspond to\n *    `x,y`. If the first value is a string, it is applied as the name of the\n *    point, and the `x` value is inferred.\n *    ```js\n *        data: [\n *            [0, 9],\n *            [1, 7],\n *            [2, 6]\n *        ]\n *    ```\n *\n * 3. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.area.turboThreshold),\n *    this option is not available.\n *    ```js\n *        data: [{\n *            x: 1,\n *            y: 9,\n *            name: \"Point2\",\n *            color: \"#00FF00\"\n *        }, {\n *            x: 1,\n *            y: 6,\n *            name: \"Point1\",\n *            color: \"#FF00FF\"\n *        }]\n *    ```\n *\n * @sample {highcharts} highcharts/chart/reflow-true/\n *         Numerical values\n * @sample {highcharts} highcharts/series/data-array-of-arrays/\n *         Arrays of numeric x and y\n * @sample {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *         Arrays of datetime x and y\n * @sample {highcharts} highcharts/series/data-array-of-name-value/\n *         Arrays of point.name and y\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<number|Array<(number|string),(number|null)>|null|*>}\n * @extends   series.line.data\n * @product   highcharts highstock\n * @apioption series.streamgraph.data\n */\n/**\n * @see [color](#series.streamgraph.color)\n * @see [fillOpacity](#series.streamgraph.fillOpacity)\n *\n * @apioption series.streamgraph.fillColor\n */\n/**\n * @see [color](#series.streamgraph.color)\n * @see [fillColor](#series.streamgraph.fillColor)\n *\n * @type      {number}\n * @default   1\n * @apioption series.streamgraph.fillOpacity\n */\n''; // Keeps doclets above separate\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Streamgraph_StreamgraphSeriesDefaults = (StreamgraphSeriesDefaults);\n\n;// ./code/es-modules/Series/Streamgraph/StreamgraphSeries.js\n/* *\n *\n *  Streamgraph module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { areaspline: AreaSplineSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\n\nconst { addEvent, merge, extend } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/**\n * Streamgraph series type\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.streamgraph\n *\n * @augments Highcharts.Series\n */\nclass StreamgraphSeries extends AreaSplineSeries {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    // Modifier function for stream stacks. It simply moves the point up or\n    // down in order to center the full stack vertically.\n    streamStacker(pointExtremes, stack, i) {\n        // Y bottom value\n        pointExtremes[0] -= stack.total / 2;\n        // Y value\n        pointExtremes[1] -= stack.total / 2;\n        // Record the Y data for use when getting axis extremes. Register only\n        // the max. This is picked up in the `afterGetExtremes` event, and the\n        // dataMin property is reflected.\n        if (this.stackedYData) {\n            this.stackedYData[i] = Math.max.apply(0, pointExtremes);\n        }\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nStreamgraphSeries.defaultOptions = merge(AreaSplineSeries.defaultOptions, Streamgraph_StreamgraphSeriesDefaults);\n// Reflect the dataMin property, as only dataMax is registered above\naddEvent(StreamgraphSeries, 'afterGetExtremes', (e) => {\n    e.dataExtremes.dataMin = -e.dataExtremes.dataMax;\n});\nextend(StreamgraphSeries.prototype, {\n    negStacks: false\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('streamgraph', StreamgraphSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Streamgraph_StreamgraphSeries = ((/* unused pure expression or super */ null && (StreamgraphSeries)));\n\n;// ./code/es-modules/masters/modules/streamgraph.js\n\n\n\n\n/* harmony default export */ const streamgraph_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "streamgraph_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "areaspline", "AreaSplineSeries", "seriesTypes", "addEvent", "merge", "extend", "StreamgraphSeries", "streamStacker", "pointExtremes", "stack", "i", "total", "stackedYData", "Math", "max", "apply", "defaultOptions", "fillOpacity", "lineWidth", "marker", "enabled", "stacking", "e", "dataExtremes", "dataMin", "dataMax", "negStacks", "registerSeriesType"], "mappings": "CAWA,AAXA;;;;;;;;;;CAUC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,iCAAkC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GAC1H,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,iCAAiC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAE9GA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAyKjL,GAAM,CAAEE,WAAYC,CAAgB,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAG1L,CAAEC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAE,CAAIR,GAUrC,OAAMS,UAA0BL,EAQ5BM,cAAcC,CAAa,CAAEC,CAAK,CAAEC,CAAC,CAAE,CAEnCF,CAAa,CAAC,EAAE,EAAIC,EAAME,KAAK,CAAG,EAElCH,CAAa,CAAC,EAAE,EAAIC,EAAME,KAAK,CAAG,EAI9B,IAAI,CAACC,YAAY,EACjB,CAAA,IAAI,CAACA,YAAY,CAACF,EAAE,CAAGG,KAAKC,GAAG,CAACC,KAAK,CAAC,EAAGP,EAAa,CAE9D,CACJ,CAMAF,EAAkBU,cAAc,CAAGZ,EAAMH,EAAiBe,cAAc,CAhLtC,CAmB9BC,YAAa,EACbC,UAAW,EACXC,OAAQ,CACJC,QAAS,CAAA,CACb,EACAC,SAAU,QACd,GAyJAlB,EAASG,EAAmB,mBAAoB,AAACgB,IAC7CA,EAAEC,YAAY,CAACC,OAAO,CAAG,CAACF,EAAEC,YAAY,CAACE,OAAO,AACpD,GACApB,EAAOC,EAAkBf,SAAS,CAAE,CAChCmC,UAAW,CAAA,CACf,GACA3B,IAA0I4B,kBAAkB,CAAC,cAAerB,GAa/I,IAAMX,EAAoBE,IAG7C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}