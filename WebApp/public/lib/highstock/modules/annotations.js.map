{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/annotations\n * @requires highcharts\n *\n * Annotations module\n *\n * (c) 2009-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"Templating\"], root[\"_Highcharts\"][\"AST\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/annotations\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"],amd1[\"Templating\"],amd1[\"AST\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/annotations\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"Templating\"], root[\"_Highcharts\"][\"AST\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"Templating\"], root[\"Highcharts\"][\"AST\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__984__, __WEBPACK_EXTERNAL_MODULE__660__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 660:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__660__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ }),\n\n/***/ 984:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__984__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ annotations_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Extensions/Annotations/AnnotationChart.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { addEvent, erase, find, fireEvent, isArray, isObject, pick, wrap } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Add an annotation to the chart after render time.\n *\n * @sample highcharts/annotations/add-annotation/\n *         Add annotation\n *\n * @function Highcharts.Chart#addAnnotation\n *\n * @param  {Highcharts.AnnotationsOptions} options\n *         The annotation options for the new, detailed annotation.\n *\n * @param {boolean} [redraw]\n *\n * @return {Highcharts.Annotation}\n *         The newly generated annotation.\n */\nfunction chartAddAnnotation(userOptions, redraw) {\n    const annotation = this.initAnnotation(userOptions);\n    this.options.annotations.push(annotation.options);\n    if (pick(redraw, true)) {\n        annotation.redraw();\n        annotation.graphic.attr({\n            opacity: 1\n        });\n    }\n    return annotation;\n}\n/**\n * @private\n */\nfunction chartCallback() {\n    const chart = this;\n    chart.plotBoxClip = this.renderer.clipRect(this.plotBox);\n    chart.controlPointsGroup = chart.renderer\n        .g('control-points')\n        .attr({ zIndex: 99 })\n        .clip(chart.plotBoxClip)\n        .add();\n    chart.options.annotations.forEach((annotationOptions, i) => {\n        if (\n        // Verify that it has not been previously added in a responsive rule\n        !chart.annotations.some((annotation) => annotation.options === annotationOptions)) {\n            const annotation = chart.initAnnotation(annotationOptions);\n            chart.options.annotations[i] = annotation.options;\n        }\n    });\n    chart.drawAnnotations();\n    addEvent(chart, 'redraw', chart.drawAnnotations);\n    addEvent(chart, 'destroy', function () {\n        chart.plotBoxClip.destroy();\n        chart.controlPointsGroup.destroy();\n    });\n    addEvent(chart, 'exportData', function (event) {\n        const annotations = chart.annotations, csvColumnHeaderFormatter = ((this.options.exporting &&\n            this.options.exporting.csv) ||\n            {}).columnHeaderFormatter, \n        // If second row doesn't have xValues\n        // then it is a title row thus multiple level header is in use.\n        multiLevelHeaders = !event.dataRows[1].xValues, annotationHeader = (chart.options.lang &&\n            chart.options.lang.exportData &&\n            chart.options.lang.exportData.annotationHeader), columnHeaderFormatter = function (index) {\n            let s;\n            if (csvColumnHeaderFormatter) {\n                s = csvColumnHeaderFormatter(index);\n                if (s !== false) {\n                    return s;\n                }\n            }\n            s = annotationHeader + ' ' + index;\n            if (multiLevelHeaders) {\n                return {\n                    columnTitle: s,\n                    topLevelColumnTitle: s\n                };\n            }\n            return s;\n        }, startRowLength = event.dataRows[0].length, annotationSeparator = (chart.options.exporting &&\n            chart.options.exporting.csv &&\n            chart.options.exporting.csv.annotations &&\n            chart.options.exporting.csv.annotations.itemDelimiter), joinAnnotations = (chart.options.exporting &&\n            chart.options.exporting.csv &&\n            chart.options.exporting.csv.annotations &&\n            chart.options.exporting.csv.annotations.join);\n        annotations.forEach((annotation) => {\n            if (annotation.options.labelOptions &&\n                annotation.options.labelOptions.includeInDataExport) {\n                annotation.labels.forEach((label) => {\n                    if (label.options.text) {\n                        const annotationText = label.options.text;\n                        label.points.forEach((points) => {\n                            const annotationX = points.x, xAxisIndex = points.series.xAxis ?\n                                points.series.xAxis.index :\n                                -1;\n                            let wasAdded = false;\n                            // Annotation not connected to any xAxis -\n                            // add new row.\n                            if (xAxisIndex === -1) {\n                                const n = event.dataRows[0].length, newRow = new Array(n);\n                                for (let i = 0; i < n; ++i) {\n                                    newRow[i] = '';\n                                }\n                                newRow.push(annotationText);\n                                newRow.xValues = [];\n                                newRow.xValues[xAxisIndex] = annotationX;\n                                event.dataRows.push(newRow);\n                                wasAdded = true;\n                            }\n                            // Annotation placed on a exported data point\n                            // - add new column\n                            if (!wasAdded) {\n                                event.dataRows.forEach((row) => {\n                                    if (!wasAdded &&\n                                        row.xValues &&\n                                        xAxisIndex !== void 0 &&\n                                        annotationX === row.xValues[xAxisIndex]) {\n                                        if (joinAnnotations &&\n                                            row.length > startRowLength) {\n                                            row[row.length - 1] += (annotationSeparator +\n                                                annotationText);\n                                        }\n                                        else {\n                                            row.push(annotationText);\n                                        }\n                                        wasAdded = true;\n                                    }\n                                });\n                            }\n                            // Annotation not placed on any exported data point,\n                            // but connected to the xAxis - add new row\n                            if (!wasAdded) {\n                                const n = event.dataRows[0].length, newRow = new Array(n);\n                                for (let i = 0; i < n; ++i) {\n                                    newRow[i] = '';\n                                }\n                                newRow[0] = annotationX;\n                                newRow.push(annotationText);\n                                newRow.xValues = [];\n                                if (xAxisIndex !== void 0) {\n                                    newRow.xValues[xAxisIndex] = annotationX;\n                                }\n                                event.dataRows.push(newRow);\n                            }\n                        });\n                    }\n                });\n            }\n        });\n        let maxRowLen = 0;\n        event.dataRows.forEach((row) => {\n            maxRowLen = Math.max(maxRowLen, row.length);\n        });\n        const newRows = maxRowLen - event.dataRows[0].length;\n        for (let i = 0; i < newRows; i++) {\n            const header = columnHeaderFormatter(i + 1);\n            if (multiLevelHeaders) {\n                event.dataRows[0].push(header.topLevelColumnTitle);\n                event.dataRows[1].push(header.columnTitle);\n            }\n            else {\n                event.dataRows[0].push(header);\n            }\n        }\n    });\n}\n/**\n * @private\n */\nfunction chartDrawAnnotations() {\n    this.plotBoxClip.attr(this.plotBox);\n    this.annotations.forEach((annotation) => {\n        annotation.redraw();\n        annotation.graphic.animate({\n            opacity: 1\n        }, annotation.animationConfig);\n    });\n}\n/**\n * Remove an annotation from the chart.\n *\n * @function Highcharts.Chart#removeAnnotation\n *\n * @param {number|string|Highcharts.Annotation} idOrAnnotation\n *        The annotation's id or direct annotation object.\n */\nfunction chartRemoveAnnotation(idOrAnnotation) {\n    const annotations = this.annotations, annotation = (idOrAnnotation.coll === 'annotations') ?\n        idOrAnnotation :\n        find(annotations, function (annotation) {\n            return annotation.options.id === idOrAnnotation;\n        });\n    if (annotation) {\n        fireEvent(annotation, 'remove');\n        erase(this.options.annotations, annotation.options);\n        erase(annotations, annotation);\n        annotation.destroy();\n    }\n}\n/**\n * Create lookups initially\n * @private\n */\nfunction onChartAfterInit() {\n    const chart = this, annotationsOption = this.options.annotations, annotationsUserOption = this.userOptions.annotations;\n    chart.annotations = [];\n    if (!isArray(this.options.annotations)) {\n        this.options.annotations = [];\n    }\n    if (isObject(annotationsUserOption, true) &&\n        isObject(annotationsOption, true)) {\n        this.options.annotations.push(annotationsOption);\n    }\n}\n/**\n * @private\n */\nfunction wrapPointerOnContainerMouseDown(proceed) {\n    if (!this.chart.hasDraggedAnnotation) {\n        proceed.apply(this, Array.prototype.slice.call(arguments, 1));\n    }\n}\n/* *\n *\n *  Composition\n *\n * */\n/**\n * @private\n */\nvar AnnotationChart;\n(function (AnnotationChart) {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    function compose(AnnotationClass, ChartClass, PointerClass) {\n        const chartProto = ChartClass.prototype;\n        if (!chartProto.addAnnotation) {\n            const pointerProto = PointerClass.prototype;\n            addEvent(ChartClass, 'afterInit', onChartAfterInit);\n            chartProto.addAnnotation = chartAddAnnotation;\n            chartProto.callbacks.push(chartCallback);\n            chartProto.collectionsWithInit.annotations = [chartAddAnnotation];\n            chartProto.collectionsWithUpdate.push('annotations');\n            chartProto.drawAnnotations = chartDrawAnnotations;\n            chartProto.removeAnnotation = chartRemoveAnnotation;\n            chartProto.initAnnotation = function chartInitAnnotation(userOptions) {\n                const Constructor = (AnnotationClass.types[userOptions.type] ||\n                    AnnotationClass), annotation = new Constructor(this, userOptions);\n                this.annotations.push(annotation);\n                return annotation;\n            };\n            wrap(pointerProto, 'onContainerMouseDown', wrapPointerOnContainerMouseDown);\n        }\n    }\n    AnnotationChart.compose = compose;\n})(AnnotationChart || (AnnotationChart = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Annotations_AnnotationChart = (AnnotationChart);\n\n;// ./code/es-modules/Extensions/Annotations/AnnotationDefaults.js\n/* *\n *\n *  Imports\n *\n * */\n\nconst { defined } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A collection of annotations to add to the chart. The basic annotation allows\n * adding custom labels or shapes. The items can be tied to points, axis\n * coordinates or chart pixel coordinates.\n *\n * General options for all annotations can be set using the\n * `Highcharts.setOptions` function. In this case only single objects are\n * supported, because it alters the defaults for all items. For initialization\n * in the chart constructors however, arrays of annotations are supported.\n *\n * See more in the [general docs](https://www.highcharts.com/docs/advanced-chart-features/annotations).\n *\n * @sample highcharts/annotations/basic/ Basic annotations\n * @sample highcharts/demo/annotations/ Annotated chart\n * @sample highcharts/css/annotations Styled mode\n * @sample highcharts/annotations-advanced/controllable Controllable items\n * @sample {highstock} stock/annotations/fibonacci-retracements\n *         Custom annotation, Fibonacci retracement\n * @sample highcharts/annotations/shape/\n *         Themed crooked line annotation\n *\n * @type         {Array<*>}\n * @since        6.0.0\n * @requires     modules/annotations\n * @optionparent annotations\n */\nconst AnnotationDefaults = {\n    /**\n     * Sets an ID for an annotation. Can be user later when\n     * removing an annotation in [Chart#removeAnnotation(id)](\n     * /class-reference/Highcharts.Chart#removeAnnotation) method.\n     *\n     * @type      {number|string}\n     * @apioption annotations.id\n     */\n    /**\n     * For advanced annotations, this option defines the type of annotation. Can\n     * be one of the keys listed under the [types option](#annotations.types).\n     *\n     * @sample    highcharts/annotations-advanced/crooked-line\n     *            Crooked line annotation\n     * @requires  modules/annotations-advanced\n     * @product   highstock\n     * @type      {string}\n     * @apioption annotations.type\n     */\n    /**\n     * Whether the annotation is visible.\n     *\n     * @sample highcharts/annotations/visible/\n     *         Set annotation visibility\n     */\n    visible: true,\n    /**\n     * Enable or disable the initial animation when a series is\n     * displayed for the `annotation`. The animation can also be set\n     * as a configuration object. Please note that this option only\n     * applies to the initial animation.\n     * For other animations, see [chart.animation](#chart.animation)\n     * and the animation parameter under the API methods.\n     * The following properties are supported:\n     *\n     * - `defer`: The animation delay time in milliseconds.\n     *\n     * @sample {highcharts} highcharts/annotations/defer/\n     *          Animation defer settings\n     * @type {boolean|Partial<Highcharts.AnimationOptionsObject>}\n     * @since 8.2.0\n     */\n    animation: {},\n    /**\n     * Whether to hide the part of the annotation\n     * that is outside the plot area.\n     *\n     * @sample highcharts/annotations/label-crop-overflow/\n     *         Crop line annotation\n     * @type  {boolean}\n     * @since 9.3.0\n     */\n    crop: true,\n    /**\n     * The animation delay time in milliseconds.\n     * Set to `0` renders annotation immediately.\n     * As `undefined` inherits defer time from the [series.animation.defer](#plotOptions.series.animation.defer).\n     *\n     * @type      {number}\n     * @since 8.2.0\n     * @apioption annotations.animation.defer\n     */\n    /**\n     * Allow an annotation to be draggable by a user. Possible\n     * values are `'x'`, `'xy'`, `'y'` and `''` (disabled).\n     *\n     * @sample highcharts/annotations/draggable/\n     *         Annotations draggable: 'xy'\n     *\n     * @type {Highcharts.AnnotationDraggableValue}\n     */\n    draggable: 'xy',\n    /**\n     * Options for annotation's labels. Each label inherits options\n     * from the labelOptions object. An option from the labelOptions\n     * can be overwritten by config for a specific label.\n     *\n     * @requires modules/annotations\n     */\n    labelOptions: {\n        /**\n         * The alignment of the annotation's label. If right,\n         * the right side of the label should be touching the point.\n         *\n         * @sample highcharts/annotations/label-position/\n         *         Set labels position\n         *\n         * @type {Highcharts.AlignValue}\n         */\n        align: 'center',\n        /**\n         * Whether to allow the annotation's labels to overlap.\n         * To make the labels less sensitive for overlapping,\n         * the can be set to 0.\n         *\n         * @sample highcharts/annotations/tooltip-like/\n         *         Hide overlapping labels\n         */\n        allowOverlap: false,\n        /**\n         * The background color or gradient for the annotation's\n         * label.\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         *\n         * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         */\n        backgroundColor: 'rgba(0, 0, 0, 0.75)',\n        /**\n         * The border color for the annotation's label.\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         *\n         * @type {Highcharts.ColorString}\n         */\n        borderColor: \"#000000\" /* Palette.neutralColor100 */,\n        /**\n         * The border radius in pixels for the annotation's label.\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         */\n        borderRadius: 3,\n        /**\n         * The border width in pixels for the annotation's label\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         */\n        borderWidth: 1,\n        /**\n         * A class name for styling by CSS.\n         *\n         * @sample highcharts/css/annotations\n         *         Styled mode annotations\n         *\n         * @since 6.0.5\n         */\n        className: 'highcharts-no-tooltip',\n        /**\n         * Whether to hide the annotation's label\n         * that is outside the plot area.\n         *\n         * @sample highcharts/annotations/label-crop-overflow/\n         *         Crop or justify labels\n         */\n        crop: false,\n        /**\n         * The label's pixel distance from the point.\n         *\n         * @sample highcharts/annotations/label-position/\n         *         Set labels position\n         *\n         * @type      {number}\n         * @apioption annotations.labelOptions.distance\n         */\n        /**\n         * A\n         * [format](https://www.highcharts.com/docs/chart-concepts/labels-and-string-formatting)\n         * string for the data label.\n         *\n         * @see [plotOptions.series.dataLabels.format](plotOptions.series.dataLabels.format.html)\n         *\n         * @sample highcharts/annotations/label-text/\n         *         Set labels text\n         *\n         * @type      {string}\n         * @apioption annotations.labelOptions.format\n         */\n        /**\n         * Alias for the format option.\n         *\n         * @see [format](annotations.labelOptions.format.html)\n         *\n         * @sample highcharts/annotations/label-text/\n         *         Set labels text\n         *\n         * @type      {string}\n         * @apioption annotations.labelOptions.text\n         */\n        /**\n         * Callback JavaScript function to format the annotation's\n         * label. Note that if a `format` or `text` are defined,\n         * the format or text take precedence and the formatter is\n         * ignored. `This` refers to a point object.\n         *\n         * @sample highcharts/annotations/label-text/\n         *         Set labels text\n         *\n         * @type    {Highcharts.FormatterCallbackFunction<Highcharts.Point>}\n         * @default function () { return defined(this.y) ? this.y : 'Annotation label'; }\n         */\n        formatter: function () {\n            return defined(this.y) ? '' + this.y : 'Annotation label';\n        },\n        /**\n         * Whether the annotation is visible in the exported data\n         * table.\n         *\n         * @sample highcharts/annotations/include-in-data-export/\n         *         Do not include in the data export\n         *\n         * @since 8.2.0\n         * @requires modules/export-data\n         */\n        includeInDataExport: true,\n        /**\n         * How to handle the annotation's label that flow outside\n         * the plot area. The justify option aligns the label inside\n         * the plot area.\n         *\n         * @sample highcharts/annotations/label-crop-overflow/\n         *         Crop or justify labels\n         *\n         * @validvalue [\"allow\", \"justify\"]\n         */\n        overflow: 'justify',\n        /**\n         * When either the borderWidth or the backgroundColor is\n         * set, this is the padding within the box.\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         */\n        padding: 5,\n        /**\n         * The shadow of the box. The shadow can be an object\n         * configuration containing `color`, `offsetX`, `offsetY`,\n         * `opacity` and `width`.\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         *\n         * @type {boolean|Highcharts.ShadowOptionsObject}\n         */\n        shadow: false,\n        /**\n         * The name of a symbol to use for the border around the\n         * label. Symbols are predefined functions on the Renderer\n         * object.\n         *\n         * @sample highcharts/annotations/shapes/\n         *         Available shapes for labels\n         */\n        shape: 'callout',\n        /**\n         * Styles for the annotation's label.\n         *\n         * @see [plotOptions.series.dataLabels.style](plotOptions.series.dataLabels.style.html)\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         *\n         * @type {Highcharts.CSSObject}\n         */\n        style: {\n            /** @ignore */\n            fontSize: '0.7em',\n            /** @ignore */\n            fontWeight: 'normal',\n            /** @ignore */\n            color: 'contrast'\n        },\n        /**\n         * Whether to [use HTML](https://www.highcharts.com/docs/chart-concepts/labels-and-string-formatting#html)\n         * to render the annotation's label.\n         */\n        useHTML: false,\n        /**\n         * The vertical alignment of the annotation's label.\n         *\n         * @sample highcharts/annotations/label-position/\n         *         Set labels position\n         *\n         * @type {Highcharts.VerticalAlignValue}\n         */\n        verticalAlign: 'bottom',\n        /**\n         * The x position offset of the label relative to the point.\n         * Note that if a `distance` is defined, the distance takes\n         * precedence over `x` and `y` options.\n         *\n         * @sample highcharts/annotations/label-position/\n         *         Set labels position\n         */\n        x: 0,\n        /**\n         * The y position offset of the label relative to the point.\n         * Note that if a `distance` is defined, the distance takes\n         * precedence over `x` and `y` options.\n         *\n         * @sample highcharts/annotations/label-position/\n         *         Set labels position\n         */\n        y: -16\n    },\n    /**\n     * An array of labels for the annotation. For options that apply\n     * to multiple labels, they can be added to the\n     * [labelOptions](annotations.labelOptions.html).\n     *\n     * @type      {Array<*>}\n     * @extends   annotations.labelOptions\n     * @apioption annotations.labels\n     */\n    /**\n     * This option defines the point to which the label will be\n     * connected. It can be either the point which exists in the\n     * series - it is referenced by the point's id - or a new point\n     * with defined x, y properties and optionally axes.\n     *\n     * @sample highcharts/annotations/mock-point/\n     *         Attach annotation to a mock point\n     * @sample highcharts/annotations/mock-points/\n     *         Attach annotation to a mock point with different ways\n     *\n     * @declare   Highcharts.AnnotationMockPointOptionsObject\n     * @type      {\n     *               string|\n     *               Highcharts.AnnotationMockPointOptionsObject|\n     *               Highcharts.AnnotationMockPointFunction\n     *            }\n     * @requires  modules/annotations\n     * @apioption annotations.labels.point\n     */\n    /**\n     * An array of shapes for the annotation. For options that apply\n     * to multiple shapes, then can be added to the\n     * [shapeOptions](annotations.shapeOptions.html).\n     *\n     * @type      {Array<*>}\n     * @extends   annotations.shapeOptions\n     * @apioption annotations.shapes\n     */\n    /**\n     * This option defines the point to which the shape will be\n     * connected. It can be either the point which exists in the\n     * series - it is referenced by the point's id - or a new point\n     * with defined x, y properties and optionally axes.\n     *\n     * @sample highcharts/annotations/mock-points/\n     *         Attach annotation to a mock point with different ways\n     *\n     * @declare   Highcharts.AnnotationMockPointOptionsObject\n     * @type      {\n     *               string|\n     *               Highcharts.AnnotationMockPointOptionsObject|\n     *               Highcharts.AnnotationMockPointFunction\n     *            }\n     * @extends   annotations.labels.point\n     * @requires  modules/annotations\n     * @apioption annotations.shapes.point\n     */\n    /**\n     * An array of points for the shape\n     * or a callback function that returns that shape point.\n     *\n     * This option is available\n     * for shapes which can use multiple points such as path. A\n     * point can be either a point object or a point's id.\n     *\n     * @see [annotations.shapes.point](annotations.shapes.point.html)\n     *\n     * @type      {Array<Highcharts.AnnotationShapePointOptions>}\n     * @extends   annotations.labels.point\n     * @apioption annotations.shapes.points\n     */\n    /**\n     * The URL for an image to use as the annotation shape. Note,\n     * type has to be set to `'image'`.\n     *\n     * @see [annotations.shapes.type](annotations.shapes.type)\n     * @sample highcharts/annotations/shape-src/\n     *         Define a marker image url for annotations\n     *\n     * @type      {string}\n     * @apioption annotations.shapes.src\n     */\n    /**\n     * Id of the marker which will be drawn at the final vertex of\n     * the path. Custom markers can be defined in defs property.\n     *\n     * @see [defs.markers](defs.markers.html)\n     *\n     * @sample highcharts/annotations/custom-markers/\n     *         Define a custom marker for annotations\n     *\n     * @type      {string}\n     * @apioption annotations.shapes.markerEnd\n     */\n    /**\n     * Id of the marker which will be drawn at the first vertex of\n     * the path. Custom markers can be defined in defs property.\n     *\n     * @see [defs.markers](defs.markers.html)\n     *\n     * @sample {highcharts} highcharts/annotations/custom-markers/\n     *         Define a custom marker for annotations\n     *\n     * @type      {string}\n     * @apioption annotations.shapes.markerStart\n     */\n    /**\n     * Options for annotation's shapes. Each shape inherits options\n     * from the shapeOptions object. An option from the shapeOptions\n     * can be overwritten by config for a specific shape.\n     *\n     * @requires  modules/annotations\n     */\n    shapeOptions: {\n        /**\n         *\n         * The radius of the shape in y direction.\n         * Used for the ellipse.\n         *\n         * @sample highcharts/annotations/ellipse/\n         *         Ellipse annotation\n         *\n         * @type      {number}\n         * @apioption annotations.shapeOptions.ry\n         **/\n        /**\n         *\n         * The xAxis index to which the points should be attached.\n         * Used for the ellipse.\n         *\n         * @type      {number}\n         * @apioption annotations.shapeOptions.xAxis\n         **/\n        /**\n         * The yAxis index to which the points should be attached.\n         * Used for the ellipse.\n         *\n         * @type      {number}\n         * @apioption annotations.shapeOptions.yAxis\n         **/\n        /**\n         * The width of the shape.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         *\n         * @type      {number}\n         * @apioption annotations.shapeOptions.width\n         **/\n        /**\n         * The height of the shape.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         *\n         * @type      {number}\n         * @apioption annotations.shapeOptions.height\n         */\n        /**\n         * The type of the shape.\n         * Available options are circle, rect and ellipse.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         *\n         * @sample highcharts/annotations/ellipse/\n         *         Ellipse annotation\n         *\n         * @type      {string}\n         * @default   rect\n         * @apioption annotations.shapeOptions.type\n         */\n        /**\n         * The URL for an image to use as the annotation shape.\n         * Note, type has to be set to `'image'`.\n         *\n         * @see [annotations.shapeOptions.type](annotations.shapeOptions.type)\n         * @sample highcharts/annotations/shape-src/\n         *         Define a marker image url for annotations\n         *\n         * @type      {string}\n         * @apioption annotations.shapeOptions.src\n         */\n        /**\n         * Name of the dash style to use for the shape's stroke.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-dashstyle-all/\n         *         Possible values demonstrated\n         *\n         * @type      {Highcharts.DashStyleValue}\n         * @apioption annotations.shapeOptions.dashStyle\n         */\n        /**\n         * The color of the shape's stroke.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         *\n         * @type {Highcharts.ColorString}\n         */\n        stroke: 'rgba(0, 0, 0, 0.75)',\n        /**\n         * The pixel stroke width of the shape.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         */\n        strokeWidth: 1,\n        /**\n         * The color of the shape's fill.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         *\n         * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         */\n        fill: 'rgba(0, 0, 0, 0.75)',\n        /**\n         * The radius of the shape.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         */\n        r: 0,\n        /**\n         * Defines additional snapping area around an annotation\n         * making this annotation to focus. Defined in pixels.\n         */\n        snap: 2\n    },\n    /**\n     * Options for annotation's control points. Each control point\n     * inherits options from controlPointOptions object.\n     * Options from the controlPointOptions can be overwritten\n     * by options in a specific control point.\n     *\n     * @declare  Highcharts.AnnotationControlPointOptionsObject\n     * @requires modules/annotations\n     */\n    controlPointOptions: {\n        /**\n         * @type      {Highcharts.AnnotationControlPointPositionerFunction}\n         * @apioption annotations.controlPointOptions.positioner\n         */\n        /**\n         * @type {Highcharts.Dictionary<Function>}\n         */\n        events: {},\n        /**\n         * @type {Highcharts.SVGAttributes}\n         */\n        style: {\n            cursor: 'pointer',\n            fill: \"#ffffff\" /* Palette.backgroundColor */,\n            stroke: \"#000000\" /* Palette.neutralColor100 */,\n            'stroke-width': 2\n        },\n        height: 10,\n        symbol: 'circle',\n        visible: false,\n        width: 10\n    },\n    /**\n     * Event callback when annotation is added to the chart.\n     *\n     * @type      {Highcharts.EventCallbackFunction<Highcharts.Annotation>}\n     * @since     7.1.0\n     * @apioption annotations.events.add\n     */\n    /**\n     * Event callback when annotation is updated (e.g. drag and\n     * dropped or resized by control points).\n     *\n     * @type      {Highcharts.EventCallbackFunction<Highcharts.Annotation>}\n     * @since     7.1.0\n     * @apioption annotations.events.afterUpdate\n     */\n    /**\n     * Fires when the annotation is clicked.\n     *\n     * @type      {Highcharts.EventCallbackFunction<Highcharts.Annotation>}\n     * @since     7.1.0\n     * @apioption annotations.events.click\n     */\n    /**\n     * Fires when the annotation is dragged.\n     *\n     * @type      {Highcharts.EventCallbackFunction<Highcharts.Annotation>}\n     * @apioption annotations.events.drag\n     */\n    /**\n     * Event callback when annotation is removed from the chart.\n     *\n     * @type      {Highcharts.EventCallbackFunction<Highcharts.Annotation>}\n     * @since     7.1.0\n     * @apioption annotations.events.remove\n     */\n    /**\n     * Events available in annotations.\n     *\n     * @requires modules/annotations\n     */\n    events: {},\n    /**\n     * Option override for specific advanced annotation types. This collection\n     * is intended for general theming using `Highcharts.setOptions()`.\n     *\n     * @sample   highcharts/annotations/shape/\n     *           Themed crooked line annotation\n     * @product highstock\n     * @requires modules/annotations-advanced\n     */\n    types: {},\n    /**\n     * The Z index of the annotation.\n     */\n    zIndex: 6\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Annotations_AnnotationDefaults = (AnnotationDefaults);\n\n;// ./code/es-modules/Extensions/Annotations/EventEmitter.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { doc, isTouchDevice } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent: EventEmitter_addEvent, fireEvent: EventEmitter_fireEvent, objectEach, pick: EventEmitter_pick, removeEvent } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n */\nclass EventEmitter {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Add emitter events.\n     * @private\n     */\n    addEvents() {\n        const emitter = this, addMouseDownEvent = function (element) {\n            EventEmitter_addEvent(element, isTouchDevice ? 'touchstart' : 'mousedown', (e) => {\n                emitter.onMouseDown(e);\n            }, { passive: false });\n        };\n        addMouseDownEvent(this.graphic.element);\n        (emitter.labels || []).forEach((label) => {\n            if (label.options.useHTML &&\n                label.graphic.text &&\n                !label.graphic.text.foreignObject) {\n                // Mousedown event bound to HTML element (#13070).\n                addMouseDownEvent(label.graphic.text.element);\n            }\n        });\n        objectEach(emitter.options.events, (event, type) => {\n            const eventHandler = function (e) {\n                if (type !== 'click' || !emitter.cancelClick) {\n                    event.call(emitter, emitter.chart.pointer?.normalize(e), emitter.target);\n                }\n            };\n            if ((emitter.nonDOMEvents || []).indexOf(type) === -1) {\n                EventEmitter_addEvent(emitter.graphic.element, type, eventHandler, { passive: false });\n                if (emitter.graphic.div) {\n                    EventEmitter_addEvent(emitter.graphic.div, type, eventHandler, { passive: false });\n                }\n            }\n            else {\n                EventEmitter_addEvent(emitter, type, eventHandler, { passive: false });\n            }\n        });\n        if (emitter.options.draggable) {\n            EventEmitter_addEvent(emitter, 'drag', emitter.onDrag);\n            if (!emitter.graphic.renderer.styledMode) {\n                const cssPointer = {\n                    cursor: {\n                        x: 'ew-resize',\n                        y: 'ns-resize',\n                        xy: 'move'\n                    }[emitter.options.draggable]\n                };\n                emitter.graphic.css(cssPointer);\n                (emitter.labels || []).forEach((label) => {\n                    if (label.options.useHTML &&\n                        label.graphic.text &&\n                        !label.graphic.text.foreignObject) {\n                        label.graphic.text.css(cssPointer);\n                    }\n                });\n            }\n        }\n        if (!emitter.isUpdating) {\n            EventEmitter_fireEvent(emitter, 'add');\n        }\n    }\n    /**\n     * Destroy the event emitter.\n     */\n    destroy() {\n        this.removeDocEvents();\n        removeEvent(this);\n        this.hcEvents = null;\n    }\n    /**\n     * Map mouse move event to the radians.\n     * @private\n     */\n    mouseMoveToRadians(e, cx, cy) {\n        let prevDy = e.prevChartY - cy, prevDx = e.prevChartX - cx, dy = e.chartY - cy, dx = e.chartX - cx, temp;\n        if (this.chart.inverted) {\n            temp = prevDx;\n            prevDx = prevDy;\n            prevDy = temp;\n            temp = dx;\n            dx = dy;\n            dy = temp;\n        }\n        return Math.atan2(dy, dx) - Math.atan2(prevDy, prevDx);\n    }\n    /**\n     * Map mouse move to the scale factors.\n     * @private\n     */\n    mouseMoveToScale(e, cx, cy) {\n        const prevDx = e.prevChartX - cx, prevDy = e.prevChartY - cy, dx = e.chartX - cx, dy = e.chartY - cy;\n        let sx = (dx || 1) / (prevDx || 1), sy = (dy || 1) / (prevDy || 1);\n        if (this.chart.inverted) {\n            const temp = sy;\n            sy = sx;\n            sx = temp;\n        }\n        return {\n            x: sx,\n            y: sy\n        };\n    }\n    /**\n     * Map mouse move event to the distance between two following events.\n     * @private\n     */\n    mouseMoveToTranslation(e) {\n        let dx = e.chartX - e.prevChartX, dy = e.chartY - e.prevChartY, temp;\n        if (this.chart.inverted) {\n            temp = dy;\n            dy = dx;\n            dx = temp;\n        }\n        return {\n            x: dx,\n            y: dy\n        };\n    }\n    /**\n     * Drag and drop event. All basic annotations should share this\n     * capability as well as the extended ones.\n     * @private\n     */\n    onDrag(e) {\n        if (this.chart.isInsidePlot(e.chartX - this.chart.plotLeft, e.chartY - this.chart.plotTop, {\n            visiblePlotOnly: true\n        })) {\n            const translation = this.mouseMoveToTranslation(e);\n            if (this.options.draggable === 'x') {\n                translation.y = 0;\n            }\n            if (this.options.draggable === 'y') {\n                translation.x = 0;\n            }\n            const emitter = this;\n            if (emitter.points.length) {\n                emitter.translate(translation.x, translation.y);\n            }\n            else {\n                emitter.shapes.forEach((shape) => shape.translate(translation.x, translation.y));\n                emitter.labels.forEach((label) => label.translate(translation.x, translation.y));\n            }\n            this.redraw(false);\n        }\n    }\n    /**\n     * Mouse down handler.\n     * @private\n     */\n    onMouseDown(e) {\n        if (e.preventDefault) {\n            e.preventDefault();\n        }\n        // On right click, do nothing:\n        if (e.button === 2) {\n            return;\n        }\n        const emitter = this, pointer = emitter.chart.pointer, \n        // Using experimental property on event object to check if event was\n        // created by touch on screen on hybrid device (#18122)\n        firesTouchEvents = (e?.sourceCapabilities?.firesTouchEvents) || false;\n        e = pointer?.normalize(e) || e;\n        let prevChartX = e.chartX, prevChartY = e.chartY;\n        emitter.cancelClick = false;\n        emitter.chart.hasDraggedAnnotation = true;\n        emitter.removeDrag = EventEmitter_addEvent(doc, isTouchDevice || firesTouchEvents ? 'touchmove' : 'mousemove', function (e) {\n            emitter.hasDragged = true;\n            e = pointer?.normalize(e) || e;\n            e.prevChartX = prevChartX;\n            e.prevChartY = prevChartY;\n            EventEmitter_fireEvent(emitter, 'drag', e);\n            prevChartX = e.chartX;\n            prevChartY = e.chartY;\n        }, isTouchDevice || firesTouchEvents ? { passive: false } : void 0);\n        emitter.removeMouseUp = EventEmitter_addEvent(doc, isTouchDevice || firesTouchEvents ? 'touchend' : 'mouseup', function () {\n            // Sometimes the target is the annotation and sometimes its the\n            // controllable\n            const annotation = EventEmitter_pick(emitter.target && emitter.target.annotation, emitter.target);\n            if (annotation) {\n                // Keep annotation selected after dragging control point\n                annotation.cancelClick = emitter.hasDragged;\n            }\n            emitter.cancelClick = emitter.hasDragged;\n            emitter.chart.hasDraggedAnnotation = false;\n            if (emitter.hasDragged) {\n                // ControlPoints vs Annotation:\n                EventEmitter_fireEvent(EventEmitter_pick(annotation, // #15952\n                emitter), 'afterUpdate');\n            }\n            emitter.hasDragged = false;\n            emitter.onMouseUp();\n        }, isTouchDevice || firesTouchEvents ? { passive: false } : void 0);\n    }\n    /**\n     * Mouse up handler.\n     */\n    onMouseUp() {\n        this.removeDocEvents();\n    }\n    /**\n     * Remove emitter document events.\n     * @private\n     */\n    removeDocEvents() {\n        if (this.removeDrag) {\n            this.removeDrag = this.removeDrag();\n        }\n        if (this.removeMouseUp) {\n            this.removeMouseUp = this.removeMouseUp();\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Annotations_EventEmitter = (EventEmitter);\n\n;// ./code/es-modules/Extensions/Annotations/ControlPoint.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { merge, pick: ControlPoint_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * A control point class which is a connection between controllable\n * transform methods and a user actions.\n *\n * @requires modules/annotations\n *\n * @class\n * @name Highcharts.AnnotationControlPoint\n *\n * @hideconstructor\n *\n * @param {Highcharts.Chart} chart\n * A chart instance.\n *\n * @param {Highcharts.AnnotationControllable} target\n * A controllable instance which is a target for a control point.\n *\n * @param {Highcharts.AnnotationControlPointOptionsObject} options\n * An options object.\n *\n * @param {number} [index]\n * Point index.\n */\nclass ControlPoint extends Annotations_EventEmitter {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(chart, target, options, index) {\n        super();\n        /**\n         * List of events for `annotation.options.events` that should not be\n         * added to `annotation.graphic` but to the `annotation`.\n         * @private\n         * @name Highcharts.AnnotationControlPoint#nonDOMEvents\n         * @type {Array<string>}\n         */\n        this.nonDOMEvents = ['drag'];\n        this.chart = chart;\n        this.target = target;\n        this.options = options;\n        this.index = ControlPoint_pick(options.index, index);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Destroy the control point.\n     * @private\n     */\n    destroy() {\n        super.destroy();\n        if (this.graphic) {\n            this.graphic = this.graphic.destroy();\n        }\n        this.chart = null;\n        this.target = null;\n        this.options = null;\n    }\n    /**\n     * Redraw the control point.\n     * @private\n     * @param {boolean} [animation]\n     */\n    redraw(animation) {\n        this.graphic[animation ? 'animate' : 'attr'](this.options.positioner.call(this, this.target));\n    }\n    /**\n     * Render the control point.\n     * @private\n     */\n    render() {\n        const chart = this.chart, options = this.options;\n        this.graphic = chart.renderer\n            .symbol(options.symbol, 0, 0, options.width, options.height)\n            .add(chart.controlPointsGroup)\n            .css(options.style);\n        this.setVisibility(options.visible);\n        // `npm test -- --tests \"@highcharts/highcharts/annotations-advanced/*\"`\n        this.addEvents();\n    }\n    /**\n     * Set the visibility of the control point.\n     *\n     * @function Highcharts.AnnotationControlPoint#setVisibility\n     *\n     * @param {boolean} visible\n     * Visibility of the control point.\n     *\n     */\n    setVisibility(visible) {\n        this.graphic[visible ? 'show' : 'hide']();\n        this.options.visible = visible;\n    }\n    /**\n     * Update the control point.\n     *\n     * @function Highcharts.AnnotationControlPoint#update\n     *\n     * @param {Partial<Highcharts.AnnotationControlPointOptionsObject>} userOptions\n     * New options for the control point.\n     */\n    update(userOptions) {\n        const chart = this.chart, target = this.target, index = this.index, options = merge(true, this.options, userOptions);\n        this.destroy();\n        this.constructor(chart, target, options, index);\n        this.render(chart.controlPointsGroup);\n        this.redraw();\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Annotations_ControlPoint = (ControlPoint);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Callback to modify annotation's positioner controls.\n *\n * @callback Highcharts.AnnotationControlPointPositionerFunction\n * @param {Highcharts.AnnotationControlPoint} this\n * @param {Highcharts.AnnotationControllable} target\n * @return {Highcharts.PositionObject}\n */\n(''); // Keeps doclets above in JS file\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Extensions/Annotations/MockPoint.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { series: { prototype: seriesProto } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { defined: MockPoint_defined, fireEvent: MockPoint_fireEvent } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * A trimmed point object which imitates {@link Highchart.Point} class. It is\n * created when there is a need of pointing to some chart's position using axis\n * values or pixel values\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationMockPoint\n *\n * @hideconstructor\n *\n * @param {Highcharts.Chart} chart\n * The chart instance.\n *\n * @param {Highcharts.AnnotationControllable|null} target\n * The related controllable.\n *\n * @param {Highcharts.AnnotationMockPointOptionsObject|Function} options\n * The options object.\n */\nclass MockPoint {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Create a mock point from a real Highcharts point.\n     *\n     * @private\n     * @static\n     *\n     * @param {Highcharts.Point} point\n     *\n     * @return {Highcharts.AnnotationMockPoint}\n     * A mock point instance.\n     */\n    static fromPoint(point) {\n        return new MockPoint(point.series.chart, null, {\n            x: point.x,\n            y: point.y,\n            xAxis: point.series.xAxis,\n            yAxis: point.series.yAxis\n        });\n    }\n    /**\n     * Get the pixel position from the point like object.\n     *\n     * @private\n     * @static\n     *\n     * @param {Highcharts.AnnotationPointType} point\n     *\n     * @param {boolean} [paneCoordinates]\n     *        Whether the pixel position should be relative\n     *\n     * @return {Highcharts.PositionObject} pixel position\n     */\n    static pointToPixels(point, paneCoordinates) {\n        const series = point.series, chart = series.chart;\n        let x = point.plotX || 0, y = point.plotY || 0, plotBox;\n        if (chart.inverted) {\n            if (point.mock) {\n                x = point.plotY;\n                y = point.plotX;\n            }\n            else {\n                x = chart.plotWidth - (point.plotY || 0);\n                y = chart.plotHeight - (point.plotX || 0);\n            }\n        }\n        if (series && !paneCoordinates) {\n            plotBox = series.getPlotBox();\n            x += plotBox.translateX;\n            y += plotBox.translateY;\n        }\n        return {\n            x: x,\n            y: y\n        };\n    }\n    /**\n     * Get fresh mock point options from the point like object.\n     *\n     * @private\n     * @static\n     *\n     * @param {Highcharts.AnnotationPointType} point\n     *\n     * @return {Highcharts.AnnotationMockPointOptionsObject}\n     * A mock point's options.\n     */\n    static pointToOptions(point) {\n        return {\n            x: point.x,\n            y: point.y,\n            xAxis: point.series.xAxis,\n            yAxis: point.series.yAxis\n        };\n    }\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(chart, target, options) {\n        /* *\n         *\n         * Functions\n         *\n         * */\n        /**\n         * A flag indicating that a point is not the real one.\n         *\n         * @type {boolean}\n         * @default true\n         */\n        this.mock = true;\n        // Circular reference for formats and formatters\n        this.point = this;\n        /**\n         * A mock series instance imitating a real series from a real point.\n         *\n         * @name Annotation.AnnotationMockPoint#series\n         * @type {Highcharts.AnnotationMockSeries}\n         */\n        this.series = {\n            visible: true,\n            chart: chart,\n            getPlotBox: seriesProto.getPlotBox\n        };\n        /**\n         * @name Annotation.AnnotationMockPoint#target\n         * @type {Highcharts.AnnotationControllable|null}\n         */\n        this.target = target || null;\n        /**\n         * Options for the mock point.\n         *\n         * @name Annotation.AnnotationMockPoint#options\n         * @type {Highcharts.AnnotationsMockPointOptionsObject}\n         */\n        this.options = options;\n        /**\n         * If an xAxis is set it represents the point's value in terms of the\n         * xAxis.\n         *\n         * @name Annotation.AnnotationMockPoint#x\n         * @type {number|undefined}\n         */\n        /**\n         * If an yAxis is set it represents the point's value in terms of the\n         * yAxis.\n         *\n         * @name Annotation.AnnotationMockPoint#y\n         * @type {number|undefined}\n         */\n        /**\n         * It represents the point's pixel x coordinate relative to its plot\n         * box.\n         *\n         * @name Annotation.AnnotationMockPoint#plotX\n         * @type {number|undefined}\n         */\n        /**\n         * It represents the point's pixel y position relative to its plot box.\n         *\n         * @name Annotation.AnnotationMockPoint#plotY\n         * @type {number|undefined}\n         */\n        /**\n         * Whether the point is inside the plot box.\n         *\n         * @name Annotation.AnnotationMockPoint#isInside\n         * @type {boolean|undefined}\n         */\n        this.applyOptions(this.getOptions());\n    }\n    /**\n     * Apply options for the point.\n     * @private\n     * @param {Highcharts.AnnotationMockPointOptionsObject} options\n     */\n    applyOptions(options) {\n        this.command = options.command;\n        this.setAxis(options, 'x');\n        this.setAxis(options, 'y');\n        this.refresh();\n    }\n    /**\n     * Get the point's options.\n     * @private\n     * @return {Highcharts.AnnotationMockPointOptionsObject}\n     * The mock point's options.\n     */\n    getOptions() {\n        return this.hasDynamicOptions() ?\n            this.options(this.target) :\n            this.options;\n    }\n    /**\n     * Check if the point has dynamic options.\n     * @private\n     * @return {boolean}\n     * A positive flag if the point has dynamic options.\n     */\n    hasDynamicOptions() {\n        return typeof this.options === 'function';\n    }\n    /**\n     * Check if the point is inside its pane.\n     * @private\n     * @return {boolean} A flag indicating whether the point is inside the pane.\n     */\n    isInsidePlot() {\n        const plotX = this.plotX, plotY = this.plotY, xAxis = this.series.xAxis, yAxis = this.series.yAxis, e = {\n            x: plotX,\n            y: plotY,\n            isInsidePlot: true,\n            options: {}\n        };\n        if (xAxis) {\n            e.isInsidePlot = MockPoint_defined(plotX) && plotX >= 0 && plotX <= xAxis.len;\n        }\n        if (yAxis) {\n            e.isInsidePlot =\n                e.isInsidePlot &&\n                    MockPoint_defined(plotY) &&\n                    plotY >= 0 && plotY <= yAxis.len;\n        }\n        MockPoint_fireEvent(this.series.chart, 'afterIsInsidePlot', e);\n        return e.isInsidePlot;\n    }\n    /**\n     * Refresh point values and coordinates based on its options.\n     * @private\n     */\n    refresh() {\n        const series = this.series, xAxis = series.xAxis, yAxis = series.yAxis, options = this.getOptions();\n        if (xAxis) {\n            this.x = options.x;\n            this.plotX = xAxis.toPixels(options.x, true);\n        }\n        else {\n            this.x = void 0;\n            this.plotX = options.x;\n        }\n        if (yAxis) {\n            this.y = options.y;\n            this.plotY = yAxis.toPixels(options.y, true);\n        }\n        else {\n            this.y = null;\n            this.plotY = options.y;\n        }\n        this.isInside = this.isInsidePlot();\n    }\n    /**\n     * Refresh point options based on its plot coordinates.\n     * @private\n     */\n    refreshOptions() {\n        const series = this.series, xAxis = series.xAxis, yAxis = series.yAxis;\n        this.x = this.options.x = xAxis ?\n            this.options.x = xAxis.toValue(this.plotX, true) :\n            this.plotX;\n        this.y = this.options.y = yAxis ?\n            yAxis.toValue(this.plotY, true) :\n            this.plotY;\n    }\n    /**\n     * Rotate the point.\n     * @private\n     * @param {number} cx origin x rotation\n     * @param {number} cy origin y rotation\n     * @param {number} radians\n     */\n    rotate(cx, cy, radians) {\n        if (!this.hasDynamicOptions()) {\n            const cos = Math.cos(radians), sin = Math.sin(radians), x = this.plotX - cx, y = this.plotY - cy, tx = x * cos - y * sin, ty = x * sin + y * cos;\n            this.plotX = tx + cx;\n            this.plotY = ty + cy;\n            this.refreshOptions();\n        }\n    }\n    /**\n     * Scale the point.\n     *\n     * @private\n     *\n     * @param {number} cx\n     * Origin x transformation.\n     *\n     * @param {number} cy\n     * Origin y transformation.\n     *\n     * @param {number} sx\n     * Scale factor x.\n     *\n     * @param {number} sy\n     * Scale factor y.\n     */\n    scale(cx, cy, sx, sy) {\n        if (!this.hasDynamicOptions()) {\n            const x = this.plotX * sx, y = this.plotY * sy, tx = (1 - sx) * cx, ty = (1 - sy) * cy;\n            this.plotX = tx + x;\n            this.plotY = ty + y;\n            this.refreshOptions();\n        }\n    }\n    /**\n     * Set x or y axis.\n     * @private\n     * @param {Highcharts.AnnotationMockPointOptionsObject} options\n     * @param {string} xOrY\n     * 'x' or 'y' string literal\n     */\n    setAxis(options, xOrY) {\n        const axisName = (xOrY + 'Axis'), axisOptions = options[axisName], chart = this.series.chart;\n        this.series[axisName] =\n            typeof axisOptions === 'object' ?\n                axisOptions :\n                MockPoint_defined(axisOptions) ?\n                    (chart[axisName][axisOptions] ||\n                        // @todo v--- (axisName)[axisOptions] ?\n                        chart.get(axisOptions)) :\n                    null;\n    }\n    /**\n     * Transform the mock point to an anchor (relative position on the chart).\n     * @private\n     * @return {Array<number>}\n     * A quadruple of numbers which denotes x, y, width and height of the box\n     **/\n    toAnchor() {\n        const anchor = [this.plotX, this.plotY, 0, 0];\n        if (this.series.chart.inverted) {\n            anchor[0] = this.plotY;\n            anchor[1] = this.plotX;\n        }\n        return anchor;\n    }\n    /**\n     * Translate the point.\n     *\n     * @private\n     *\n     * @param {number|undefined} cx\n     * Origin x transformation.\n     *\n     * @param {number|undefined} cy\n     * Origin y transformation.\n     *\n     * @param {number} dx\n     * Translation for x coordinate.\n     *\n     * @param {number} dy\n     * Translation for y coordinate.\n     **/\n    translate(_cx, _cy, dx, dy) {\n        if (!this.hasDynamicOptions()) {\n            this.plotX += dx;\n            this.plotY += dy;\n            this.refreshOptions();\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Annotations_MockPoint = (MockPoint);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * @private\n * @interface Highcharts.AnnotationMockLabelOptionsObject\n */ /**\n* Point instance of the point.\n* @name Highcharts.AnnotationMockLabelOptionsObject#point\n* @type {Highcharts.AnnotationMockPoint}\n*/ /**\n* X value translated to x axis scale.\n* @name Highcharts.AnnotationMockLabelOptionsObject#x\n* @type {number|null}\n*/ /**\n* Y value translated to y axis scale.\n* @name Highcharts.AnnotationMockLabelOptionsObject#y\n* @type {number|null}\n*/\n/**\n * Object of shape point.\n *\n * @interface Highcharts.AnnotationMockPointOptionsObject\n */ /**\n* The x position of the point. Units can be either in axis\n* or chart pixel coordinates.\n*\n* @type      {number}\n* @name      Highcharts.AnnotationMockPointOptionsObject.x\n*/ /**\n* The y position of the point. Units can be either in axis\n* or chart pixel coordinates.\n*\n* @type      {number}\n* @name      Highcharts.AnnotationMockPointOptionsObject.y\n*/ /**\n* This number defines which xAxis the point is connected to.\n* It refers to either the axis id or the index of the axis in\n* the xAxis array. If the option is not configured or the axis\n* is not found the point's x coordinate refers to the chart\n* pixels.\n*\n* @type      {number|string|null}\n* @name      Highcharts.AnnotationMockPointOptionsObject.xAxis\n*/ /**\n* This number defines which yAxis the point is connected to.\n* It refers to either the axis id or the index of the axis in\n* the yAxis array. If the option is not configured or the axis\n* is not found the point's y coordinate refers to the chart\n* pixels.\n*\n* @type      {number|string|null}\n* @name      Highcharts.AnnotationMockPointOptionsObject.yAxis\n*/\n/**\n * Callback function that returns the annotation shape point.\n *\n * @callback Highcharts.AnnotationMockPointFunction\n *\n * @param  {Highcharts.Annotation} annotation\n *         An annotation instance.\n *\n * @return {Highcharts.AnnotationMockPointOptionsObject}\n *         Annotations shape point.\n */\n/**\n * A mock series instance imitating a real series from a real point.\n * @private\n * @interface Highcharts.AnnotationMockSeries\n */ /**\n* Whether a series is visible.\n* @name Highcharts.AnnotationMockSeries#visible\n* @type {boolean}\n*/ /**\n* A chart instance.\n* @name Highcharts.AnnotationMockSeries#chart\n* @type {Highcharts.Chart}\n*/ /**\n* @name Highcharts.AnnotationMockSeries#getPlotBox\n* @type {Function}\n*/\n/**\n * Indicates if this is a mock point for an annotation.\n * @name Highcharts.Point#mock\n * @type {boolean|undefined}\n */\n(''); // Keeps doclets above in JS file\n\n;// ./code/es-modules/Extensions/Annotations/ControlTarget.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n/* *\n *\n *  Composition Namespace\n *\n * */\nvar ControlTarget;\n(function (ControlTarget) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Add control points.\n     * @private\n     */\n    function addControlPoints() {\n        const controlPoints = this.controlPoints, controlPointsOptions = this.options.controlPoints || [];\n        controlPointsOptions.forEach((controlPointOptions, i) => {\n            const options = highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().merge(this.options.controlPointOptions, controlPointOptions);\n            if (!options.index) {\n                options.index = i;\n            }\n            controlPointsOptions[i] = options;\n            controlPoints.push(new Annotations_ControlPoint(this.chart, this, options));\n        });\n    }\n    /**\n     * Returns object which denotes anchor position - relative and absolute.\n     * @private\n     * @param {Highcharts.AnnotationPointType} point\n     * An annotation point.\n     *\n     * @return {Highcharts.AnnotationAnchorObject}\n     * An annotation anchor.\n     */\n    function anchor(point) {\n        const plotBox = point.series.getPlotBox(), chart = point.series.chart, box = point.mock ?\n            point.toAnchor() :\n            chart.tooltip &&\n                chart.tooltip.getAnchor.call({\n                    chart: point.series.chart\n                }, point) ||\n                [0, 0, 0, 0], anchor = {\n            x: box[0] + (this.options.x || 0),\n            y: box[1] + (this.options.y || 0),\n            height: box[2] || 0,\n            width: box[3] || 0\n        };\n        return {\n            relativePosition: anchor,\n            absolutePosition: highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().merge(anchor, {\n                x: anchor.x + (point.mock ? plotBox.translateX : chart.plotLeft),\n                y: anchor.y + (point.mock ? plotBox.translateY : chart.plotTop)\n            })\n        };\n    }\n    /**\n     * Adds shared functions to be used with targets of ControlPoint.\n     * @private\n     */\n    function compose(ControlTargetClass) {\n        const controlProto = ControlTargetClass.prototype;\n        if (!controlProto.addControlPoints) {\n            highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().merge(true, controlProto, {\n                addControlPoints,\n                anchor,\n                destroyControlTarget,\n                getPointsOptions,\n                linkPoints,\n                point,\n                redrawControlPoints,\n                renderControlPoints,\n                transform,\n                transformPoint,\n                translate,\n                translatePoint\n            });\n        }\n    }\n    ControlTarget.compose = compose;\n    /**\n     * Destroy control points.\n     * @private\n     */\n    function destroyControlTarget() {\n        this.controlPoints.forEach((controlPoint) => controlPoint.destroy());\n        this.chart = null;\n        this.controlPoints = null;\n        this.points = null;\n        this.options = null;\n        if (this.annotation) {\n            this.annotation = null;\n        }\n    }\n    /**\n     * Get the points options.\n     * @private\n     * @return {Array<Highcharts.PointOptionsObject>}\n     * An array of points' options.\n     */\n    function getPointsOptions() {\n        const options = this.options;\n        return (options.points ||\n            (options.point && highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().splat(options.point)));\n    }\n    /**\n     * Find point-like objects based on points options.\n     * @private\n     * @return {Array<Annotation.PointLike>}\n     *         An array of point-like objects.\n     */\n    function linkPoints() {\n        const pointsOptions = this.getPointsOptions(), points = this.points, len = (pointsOptions && pointsOptions.length) || 0;\n        let i, point;\n        for (i = 0; i < len; i++) {\n            point = this.point(pointsOptions[i], points[i]);\n            if (!point) {\n                points.length = 0;\n                return;\n            }\n            if (point.mock) {\n                point.refresh();\n            }\n            points[i] = point;\n        }\n        return points;\n    }\n    /**\n     * Map point's options to a point-like object.\n     * @private\n     * @param {string|Function|Highcharts.AnnotationMockPointOptionsObject|Highcharts.AnnotationPointType} pointOptions\n     *        Point's options.\n     * @param {Highcharts.AnnotationPointType} point\n     *        A point-like instance.\n     * @return {Highcharts.AnnotationPointType|null}\n     *         If the point is found/set returns this point, otherwise null\n     */\n    function point(pointOptions, point) {\n        if (pointOptions && pointOptions.series) {\n            return pointOptions;\n        }\n        if (!point || point.series === null) {\n            if (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().isObject(pointOptions)) {\n                point = new Annotations_MockPoint(this.chart, this, pointOptions);\n            }\n            else if (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().isString(pointOptions)) {\n                point = this.chart.get(pointOptions) || null;\n            }\n            else if (typeof pointOptions === 'function') {\n                const pointConfig = pointOptions.call(point, this);\n                point = pointConfig.series ?\n                    pointConfig :\n                    new Annotations_MockPoint(this.chart, this, pointOptions);\n            }\n        }\n        return point;\n    }\n    /**\n     * Redraw control points.\n     * @private\n     */\n    function redrawControlPoints(animation) {\n        this.controlPoints.forEach((controlPoint) => controlPoint.redraw(animation));\n    }\n    /**\n     * Render control points.\n     * @private\n     */\n    function renderControlPoints() {\n        this.controlPoints.forEach((controlPoint) => controlPoint.render());\n    }\n    /**\n     * Transform control points with a specific transformation.\n     * @private\n     * @param {string} transformation\n     *        A transformation name\n     * @param {number|null} cx\n     *        Origin x transformation\n     * @param {number|null} cy\n     *        Origin y transformation\n     * @param {number} p1\n     *        Param for the transformation\n     * @param {number} [p2]\n     *        Param for the transformation\n     */\n    function transform(transformation, cx, cy, p1, p2) {\n        if (this.chart.inverted) {\n            const temp = cx;\n            cx = cy;\n            cy = temp;\n        }\n        this.points.forEach((_point, i) => (this.transformPoint(transformation, cx, cy, p1, p2, i)), this);\n    }\n    /**\n     * Transform a point with a specific transformation\n     * If a transformed point is a real point it is replaced with\n     * the mock point.\n     * @private\n     * @param {string} transformation\n     *        A transformation name\n     * @param {number|null} cx\n     *        Origin x transformation\n     * @param {number|null} cy\n     *        Origin y transformation\n     * @param {number} p1\n     *        Param for the transformation\n     * @param {number|undefined} p2\n     *        Param for the transformation\n     * @param {number} i\n     *        Index of the point\n     */\n    function transformPoint(transformation, cx, cy, p1, p2, i) {\n        let point = this.points[i];\n        if (!point.mock) {\n            point = this.points[i] = Annotations_MockPoint.fromPoint(point);\n        }\n        point[transformation](cx, cy, p1, p2);\n    }\n    /**\n     * Translate control points.\n     * @private\n     * @param {number} dx\n     *        Translation for x coordinate\n     * @param {number} dy\n     *        Translation for y coordinate\n     **/\n    function translate(dx, dy) {\n        this.transform('translate', null, null, dx, dy);\n    }\n    /**\n     * Translate a specific control point.\n     * @private\n     * @param {number} dx\n     *        Translation for x coordinate\n     * @param {number} dy\n     *        Translation for y coordinate\n     * @param {number} i\n     *        Index of the point\n     **/\n    function translatePoint(dx, dy, i) {\n        this.transformPoint('translate', null, null, dx, dy, i);\n    }\n})(ControlTarget || (ControlTarget = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Annotations_ControlTarget = (ControlTarget);\n\n;// ./code/es-modules/Extensions/Annotations/Controllables/Controllable.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { merge: Controllable_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * It provides methods for handling points, control points\n * and points transformations.\n * @private\n */\nclass Controllable {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(annotation, options, index, itemType) {\n        this.annotation = annotation;\n        this.chart = annotation.chart;\n        this.collection = (itemType === 'label' ? 'labels' : 'shapes');\n        this.controlPoints = [];\n        this.options = options;\n        this.points = [];\n        this.index = index;\n        this.itemType = itemType;\n        this.init(annotation, options, index);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Redirect attr usage on the controllable graphic element.\n     * @private\n     */\n    attr(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    ..._args) {\n        this.graphic.attr.apply(this.graphic, arguments);\n    }\n    /**\n     * Utility function for mapping item's options\n     * to element's attribute\n     * @private\n     * @param {Highcharts.AnnotationsLabelsOptions|Highcharts.AnnotationsShapesOptions} options\n     * @return {Highcharts.SVGAttributes}\n     *         Mapped options.\n     */\n    attrsFromOptions(options) {\n        const map = this.constructor.attrsMap, attrs = {}, styledMode = this.chart.styledMode;\n        let key, mappedKey;\n        for (key in options) { // eslint-disable-line guard-for-in\n            mappedKey = map[key];\n            if (typeof map[key] !== 'undefined' &&\n                (!styledMode ||\n                    ['fill', 'stroke', 'stroke-width']\n                        .indexOf(mappedKey) === -1)) {\n                attrs[mappedKey] = options[key];\n            }\n        }\n        return attrs;\n    }\n    /**\n     * Destroy a controllable.\n     * @private\n     */\n    destroy() {\n        if (this.graphic) {\n            this.graphic = this.graphic.destroy();\n        }\n        if (this.tracker) {\n            this.tracker = this.tracker.destroy();\n        }\n        this.destroyControlTarget();\n    }\n    /**\n     * Init the controllable\n     * @private\n     */\n    init(annotation, options, index) {\n        this.annotation = annotation;\n        this.chart = annotation.chart;\n        this.options = options;\n        this.points = [];\n        this.controlPoints = [];\n        this.index = index;\n        this.linkPoints();\n        this.addControlPoints();\n    }\n    /**\n     * Redraw a controllable.\n     * @private\n     */\n    redraw(animation) {\n        this.redrawControlPoints(animation);\n    }\n    /**\n     * Render a controllable.\n     * @private\n     */\n    render(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _parentGroup) {\n        if (this.options.className && this.graphic) {\n            this.graphic.addClass(this.options.className);\n        }\n        this.renderControlPoints();\n    }\n    /**\n     * Rotate a controllable.\n     * @private\n     * @param {number} cx\n     *        Origin x rotation\n     * @param {number} cy\n     *        Origin y rotation\n     * @param {number} radians\n     **/\n    rotate(cx, cy, radians) {\n        this.transform('rotate', cx, cy, radians);\n    }\n    /**\n     * Scale a controllable.\n     * @private\n     * @param {number} cx\n     *        Origin x rotation\n     * @param {number} cy\n     *        Origin y rotation\n     * @param {number} sx\n     *        Scale factor x\n     * @param {number} sy\n     *        Scale factor y\n     */\n    scale(cx, cy, sx, sy) {\n        this.transform('scale', cx, cy, sx, sy);\n    }\n    /**\n     * Set control points' visibility.\n     * @private\n     */\n    setControlPointsVisibility(visible) {\n        this.controlPoints.forEach((controlPoint) => {\n            controlPoint.setVisibility(visible);\n        });\n    }\n    /**\n     * Check if a controllable should be rendered/redrawn.\n     * @private\n     * @return {boolean}\n     *         Whether a controllable should be drawn.\n     */\n    shouldBeDrawn() {\n        return !!this.points.length;\n    }\n    /**\n     * Translate shape within controllable item.\n     * Replaces `controllable.translate` method.\n     * @private\n     * @param {number} dx\n     *        Translation for x coordinate\n     * @param {number} dy\n     *        Translation for y coordinate\n     * @param {boolean|undefined} translateSecondPoint\n     *        If the shape has two points attached to it, this option allows you\n     *        to translate also the second point.\n     */\n    translateShape(dx, dy, translateSecondPoint) {\n        const chart = this.annotation.chart, \n        // Annotation.options\n        shapeOptions = this.annotation.userOptions, \n        // Chart.options.annotations\n        annotationIndex = chart.annotations.indexOf(this.annotation), chartOptions = chart.options.annotations[annotationIndex];\n        this.translatePoint(dx, dy, 0);\n        if (translateSecondPoint) {\n            this.translatePoint(dx, dy, 1);\n        }\n        // Options stored in:\n        // - chart (for exporting)\n        // - current config (for redraws)\n        chartOptions[this.collection][this.index]\n            .point = this.options.point;\n        shapeOptions[this.collection][this.index]\n            .point = this.options.point;\n    }\n    /**\n     * Update a controllable.\n     * @private\n     */\n    update(newOptions) {\n        const annotation = this.annotation, options = Controllable_merge(true, this.options, newOptions), parentGroup = this.graphic.parentGroup, Constructor = this.constructor;\n        this.destroy();\n        const newControllable = new Constructor(annotation, options, this.index, this.itemType);\n        Controllable_merge(true, this, newControllable);\n        this.render(parentGroup);\n        this.redraw();\n    }\n}\nAnnotations_ControlTarget.compose(Controllable);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Controllables_Controllable = (Controllable);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * An object which denotes a controllable's anchor positions - relative and\n * absolute.\n *\n * @private\n * @interface Highcharts.AnnotationAnchorObject\n */ /**\n* Relative to the plot area position\n* @name Highcharts.AnnotationAnchorObject#relativePosition\n* @type {Highcharts.BBoxObject}\n*/ /**\n* Absolute position\n* @name Highcharts.AnnotationAnchorObject#absolutePosition\n* @type {Highcharts.BBoxObject}\n*/\n/**\n * @interface Highcharts.AnnotationControllable\n */ /**\n* @name Highcharts.AnnotationControllable#annotation\n* @type {Highcharts.Annotation}\n*/ /**\n* @name Highcharts.AnnotationControllable#chart\n* @type {Highcharts.Chart}\n*/ /**\n* @name Highcharts.AnnotationControllable#collection\n* @type {string}\n*/ /**\n* @private\n* @name Highcharts.AnnotationControllable#controlPoints\n* @type {Array<Highcharts.AnnotationControlPoint>}\n*/ /**\n* @name Highcharts.AnnotationControllable#points\n* @type {Array<Highcharts.Point>}\n*/\n(''); // Keeps doclets above in JS file\n\n;// ./code/es-modules/Extensions/Annotations/Controllables/ControllableDefaults.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/**\n * Options for configuring markers for annotations.\n *\n * An example of the arrow marker:\n * <pre>\n * {\n *   arrow: {\n *     id: 'arrow',\n *     tagName: 'marker',\n *     refY: 5,\n *     refX: 5,\n *     markerWidth: 10,\n *     markerHeight: 10,\n *     children: [{\n *       tagName: 'path',\n *       attrs: {\n *         d: 'M 0 0 L 10 5 L 0 10 Z',\n *         'stroke-width': 0\n *       }\n *     }]\n *   }\n * }\n * </pre>\n *\n * @sample highcharts/annotations/custom-markers/\n *         Define a custom marker for annotations\n *\n * @sample highcharts/css/annotations-markers/\n *         Define markers in a styled mode\n *\n * @type         {Highcharts.Dictionary<Highcharts.ASTNode>}\n * @since        6.0.0\n * @optionparent defs\n */\nconst defaultMarkers = {\n    /**\n     * @type {Highcharts.ASTNode}\n     */\n    arrow: {\n        tagName: 'marker',\n        attributes: {\n            id: 'arrow',\n            refY: 5,\n            refX: 9,\n            markerWidth: 10,\n            markerHeight: 10\n        },\n        /**\n         * @type {Array<Highcharts.DefsOptions>}\n         */\n        children: [{\n                tagName: 'path',\n                attributes: {\n                    d: 'M 0 0 L 10 5 L 0 10 Z', // Triangle (used as an arrow)\n                    'stroke-width': 0\n                }\n            }]\n    },\n    /**\n     * @type {Highcharts.ASTNode}\n     */\n    'reverse-arrow': {\n        tagName: 'marker',\n        attributes: {\n            id: 'reverse-arrow',\n            refY: 5,\n            refX: 1,\n            markerWidth: 10,\n            markerHeight: 10\n        },\n        children: [{\n                tagName: 'path',\n                attributes: {\n                    // Reverse triangle (used as an arrow)\n                    d: 'M 0 5 L 10 0 L 10 10 Z',\n                    'stroke-width': 0\n                }\n            }]\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\nconst ControllableDefaults = {\n    defaultMarkers\n};\n/* harmony default export */ const Controllables_ControllableDefaults = (ControllableDefaults);\n\n;// ./code/es-modules/Extensions/Annotations/Controllables/ControllablePath.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { defaultMarkers: ControllablePath_defaultMarkers } = Controllables_ControllableDefaults;\n\n\nconst { addEvent: ControllablePath_addEvent, defined: ControllablePath_defined, extend, merge: ControllablePath_merge, uniqueKey } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\nconst markerEndSetter = createMarkerSetter('marker-end');\nconst markerStartSetter = createMarkerSetter('marker-start');\n// See TRACKER_FILL in highcharts.js\nconst TRACKER_FILL = 'rgba(192,192,192,' + ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).svg ? 0.0001 : 0.002) + ')';\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction createMarkerSetter(markerType) {\n    return function (value) {\n        this.attr(markerType, 'url(#' + value + ')');\n    };\n}\n/**\n * @private\n */\nfunction onChartAfterGetContainer() {\n    this.options.defs = ControllablePath_merge(ControllablePath_defaultMarkers, this.options.defs || {});\n    ///  objectEach(this.options.defs, function (def): void {\n    //     const attributes = def.attributes;\n    //     if (\n    //         def.tagName === 'marker' &&\n    //         attributes &&\n    //         attributes.id &&\n    //         attributes.display !== 'none'\n    //     ) {\n    //         this.renderer.addMarker(attributes.id, def);\n    //     }\n    // }, this);\n}\n/**\n * @private\n */\nfunction svgRendererAddMarker(id, markerOptions) {\n    const options = { attributes: { id } };\n    const attrs = {\n        stroke: markerOptions.color || 'none',\n        fill: markerOptions.color || 'rgba(0, 0, 0, 0.75)'\n    };\n    options.children = (markerOptions.children &&\n        markerOptions.children.map(function (child) {\n            return ControllablePath_merge(attrs, child);\n        }));\n    const ast = ControllablePath_merge(true, {\n        attributes: {\n            markerWidth: 20,\n            markerHeight: 20,\n            refX: 0,\n            refY: 0,\n            orient: 'auto'\n        }\n    }, markerOptions, options);\n    const marker = this.definition(ast);\n    marker.id = id;\n    return marker;\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * A controllable path class.\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationControllablePath\n *\n * @param {Highcharts.Annotation}\n * Related annotation.\n *\n * @param {Highcharts.AnnotationsShapeOptions} options\n * A path's options object.\n *\n * @param {number} index\n * Index of the path.\n */\nclass ControllablePath extends Controllables_Controllable {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static compose(ChartClass, SVGRendererClass) {\n        const svgRendererProto = SVGRendererClass.prototype;\n        if (!svgRendererProto.addMarker) {\n            ControllablePath_addEvent(ChartClass, 'afterGetContainer', onChartAfterGetContainer);\n            svgRendererProto.addMarker = svgRendererAddMarker;\n        }\n    }\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    constructor(annotation, options, index) {\n        super(annotation, options, index, 'shape');\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.type = 'path';\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Map the controllable path to 'd' path attribute.\n     *\n     * @return {Highcharts.SVGPathArray|null}\n     * A path's d attribute.\n     */\n    toD() {\n        const dOption = this.options.d;\n        if (dOption) {\n            return typeof dOption === 'function' ?\n                dOption.call(this) :\n                dOption;\n        }\n        const points = this.points, len = points.length, d = [];\n        let showPath = len, point = points[0], position = showPath && this.anchor(point).absolutePosition, pointIndex = 0, command;\n        if (position) {\n            d.push(['M', position.x, position.y]);\n            while (++pointIndex < len && showPath) {\n                point = points[pointIndex];\n                command = point.command || 'L';\n                position = this.anchor(point).absolutePosition;\n                if (command === 'M') {\n                    d.push([command, position.x, position.y]);\n                }\n                else if (command === 'L') {\n                    d.push([command, position.x, position.y]);\n                }\n                else if (command === 'Z') {\n                    d.push([command]);\n                }\n                showPath = point.series.visible;\n            }\n        }\n        return (showPath && this.graphic ?\n            this.chart.renderer.crispLine(d, this.graphic.strokeWidth()) :\n            null);\n    }\n    shouldBeDrawn() {\n        return super.shouldBeDrawn() || !!this.options.d;\n    }\n    render(parent) {\n        const options = this.options, attrs = this.attrsFromOptions(options);\n        this.graphic = this.annotation.chart.renderer\n            .path([['M', 0, 0]])\n            .attr(attrs)\n            .add(parent);\n        this.tracker = this.annotation.chart.renderer\n            .path([['M', 0, 0]])\n            .addClass('highcharts-tracker-line')\n            .attr({\n            zIndex: 2\n        })\n            .add(parent);\n        if (!this.annotation.chart.styledMode) {\n            this.tracker.attr({\n                'stroke-linejoin': 'round', // #1225\n                stroke: TRACKER_FILL,\n                fill: TRACKER_FILL,\n                'stroke-width': this.graphic.strokeWidth() +\n                    options.snap * 2\n            });\n        }\n        super.render();\n        extend(this.graphic, { markerStartSetter, markerEndSetter });\n        this.setMarkers(this);\n    }\n    redraw(animation) {\n        if (this.graphic) {\n            const d = this.toD(), action = animation ? 'animate' : 'attr';\n            if (d) {\n                this.graphic[action]({ d: d });\n                this.tracker[action]({ d: d });\n            }\n            else {\n                this.graphic.attr({ d: 'M 0 ' + -9e9 });\n                this.tracker.attr({ d: 'M 0 ' + -9e9 });\n            }\n            this.graphic.placed = this.tracker.placed = !!d;\n        }\n        super.redraw(animation);\n    }\n    /**\n     * Set markers.\n     * @private\n     * @param {Highcharts.AnnotationControllablePath} item\n     */\n    setMarkers(item) {\n        const itemOptions = item.options, chart = item.chart, defs = chart.options.defs, fill = itemOptions.fill, color = ControllablePath_defined(fill) && fill !== 'none' ?\n            fill :\n            itemOptions.stroke;\n        const setMarker = function (markerType) {\n            const markerId = itemOptions[markerType];\n            let def, predefinedMarker, key, marker;\n            if (markerId) {\n                for (key in defs) { // eslint-disable-line guard-for-in\n                    def = defs[key];\n                    if ((markerId === (def.attributes && def.attributes.id) ||\n                        // Legacy, for\n                        // unit-tests/annotations/annotations-shapes\n                        markerId === def.id) &&\n                        def.tagName === 'marker') {\n                        predefinedMarker = def;\n                        break;\n                    }\n                }\n                if (predefinedMarker) {\n                    marker = item[markerType] = chart.renderer\n                        .addMarker((itemOptions.id || uniqueKey()) + '-' + markerId, ControllablePath_merge(predefinedMarker, { color: color }));\n                    item.attr(markerType, marker.getAttribute('id'));\n                }\n            }\n        };\n        ['markerStart', 'markerEnd']\n            .forEach(setMarker);\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * A map object which allows to map options attributes to element attributes\n *\n * @name Highcharts.AnnotationControllablePath.attrsMap\n * @type {Highcharts.Dictionary<string>}\n */\nControllablePath.attrsMap = {\n    dashStyle: 'dashstyle',\n    strokeWidth: 'stroke-width',\n    stroke: 'stroke',\n    fill: 'fill',\n    zIndex: 'zIndex'\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Controllables_ControllablePath = (ControllablePath);\n\n;// ./code/es-modules/Extensions/Annotations/Controllables/ControllableRect.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { merge: ControllableRect_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * A controllable rect class.\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationControllableRect\n *\n * @param {Highcharts.Annotation} annotation\n * An annotation instance.\n *\n * @param {Highcharts.AnnotationsShapeOptions} options\n * A rect's options.\n *\n * @param {number} index\n * Index of the rectangle\n */\nclass ControllableRect extends Controllables_Controllable {\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    constructor(annotation, options, index) {\n        super(annotation, options, index, 'shape');\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.type = 'rect';\n        this.translate = super.translateShape;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    render(parent) {\n        const attrs = this.attrsFromOptions(this.options);\n        this.graphic = this.annotation.chart.renderer\n            .rect(0, -9e9, 0, 0)\n            .attr(attrs)\n            .add(parent);\n        super.render();\n    }\n    redraw(animation) {\n        if (this.graphic) {\n            const position = this.anchor(this.points[0]).absolutePosition;\n            if (position) {\n                this.graphic[animation ? 'animate' : 'attr']({\n                    x: position.x,\n                    y: position.y,\n                    width: this.options.width,\n                    height: this.options.height\n                });\n            }\n            else {\n                this.attr({\n                    x: 0,\n                    y: -9e9\n                });\n            }\n            this.graphic.placed = Boolean(position);\n        }\n        super.redraw(animation);\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * A map object which allows to map options attributes to element attributes\n *\n * @type {Annotation.ControllableRect.AttrsMap}\n */\nControllableRect.attrsMap = ControllableRect_merge(Controllables_ControllablePath.attrsMap, {\n    width: 'width',\n    height: 'height'\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Controllables_ControllableRect = (ControllableRect);\n\n;// ./code/es-modules/Extensions/Annotations/Controllables/ControllableCircle.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { merge: ControllableCircle_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * A controllable circle class.\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationControllableCircle\n *\n * @param {Highcharts.Annotation} annotation an annotation instance\n * @param {Highcharts.AnnotationsShapeOptions} options a shape's options\n * @param {number} index of the circle\n */\nclass ControllableCircle extends Controllables_Controllable {\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    constructor(annotation, options, index) {\n        super(annotation, options, index, 'shape');\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.type = 'circle';\n        this.translate = super.translateShape;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    redraw(animation) {\n        if (this.graphic) {\n            const position = this.anchor(this.points[0]).absolutePosition;\n            if (position) {\n                this.graphic[animation ? 'animate' : 'attr']({\n                    x: position.x,\n                    y: position.y,\n                    r: this.options.r\n                });\n            }\n            else {\n                this.graphic.attr({\n                    x: 0,\n                    y: -9e9\n                });\n            }\n            this.graphic.placed = !!position;\n        }\n        super.redraw.call(this, animation);\n    }\n    /**\n     * @private\n     */\n    render(parent) {\n        const attrs = this.attrsFromOptions(this.options);\n        this.graphic = this.annotation.chart.renderer\n            .circle(0, -9e9, 0)\n            .attr(attrs)\n            .add(parent);\n        super.render();\n    }\n    /**\n     * Set the radius.\n     * @private\n     * @param {number} r\n     *        A radius to be set\n     */\n    setRadius(r) {\n        this.options.r = r;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * A map object which allows to map options attributes to element\n * attributes.\n *\n * @name Highcharts.AnnotationControllableCircle.attrsMap\n * @type {Highcharts.Dictionary<string>}\n */\nControllableCircle.attrsMap = ControllableCircle_merge(Controllables_ControllablePath.attrsMap, { r: 'r' });\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Controllables_ControllableCircle = (ControllableCircle);\n\n;// ./code/es-modules/Extensions/Annotations/Controllables/ControllableEllipse.js\n/* *\n *\n * Author: Pawel Lysy\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { merge: ControllableEllipse_merge, defined: ControllableEllipse_defined } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * A controllable ellipse class.\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationControllableEllipse\n *\n * @param {Highcharts.Annotation} annotation an annotation instance\n * @param {Highcharts.AnnotationsShapeOptions} options a shape's options\n * @param {number} index of the Ellipse\n */\nclass ControllableEllipse extends Controllables_Controllable {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(annotation, options, index) {\n        super(annotation, options, index, 'shape');\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.type = 'ellipse';\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    init(annotation, options, index) {\n        if (ControllableEllipse_defined(options.yAxis)) {\n            options.points.forEach((point) => {\n                point.yAxis = options.yAxis;\n            });\n        }\n        if (ControllableEllipse_defined(options.xAxis)) {\n            options.points.forEach((point) => {\n                point.xAxis = options.xAxis;\n            });\n        }\n        super.init(annotation, options, index);\n    }\n    /**\n     * Render the element\n     * @private\n     * @param parent\n     *        Parent SVG element.\n     */\n    render(parent) {\n        this.graphic = this.annotation.chart.renderer.createElement('ellipse')\n            .attr(this.attrsFromOptions(this.options))\n            .add(parent);\n        super.render();\n    }\n    /**\n     * Translate the points. Mostly used to handle dragging of the ellipse.\n     * @private\n     */\n    translate(dx, dy) {\n        super.translateShape(dx, dy, true);\n    }\n    /**\n     * Get the distance from the line to the point.\n     * @private\n     * @param point1\n     *        First point which is on the line\n     * @param point2\n     *        Second point\n     * @param x0\n     *        Point's x value from which you want to calculate the distance from\n     * @param y0\n     *        Point's y value from which you want to calculate the distance from\n     */\n    getDistanceFromLine(point1, point2, x0, y0) {\n        return Math.abs((point2.y - point1.y) * x0 - (point2.x - point1.x) * y0 +\n            point2.x * point1.y - point2.y * point1.x) / Math.sqrt((point2.y - point1.y) * (point2.y - point1.y) +\n            (point2.x - point1.x) * (point2.x - point1.x));\n    }\n    /**\n     * The function calculates the svg attributes of the ellipse, and returns\n     * all parameters necessary to draw the ellipse.\n     * @private\n     * @param position\n     *        Absolute position of the first point in points array\n     * @param position2\n     *        Absolute position of the second point in points array\n     */\n    getAttrs(position, position2) {\n        const x1 = position.x, y1 = position.y, x2 = position2.x, y2 = position2.y, cx = (x1 + x2) / 2, cy = (y1 + y2) / 2, rx = Math.sqrt((x1 - x2) * (x1 - x2) / 4 + (y1 - y2) * (y1 - y2) / 4), tan = (y2 - y1) / (x2 - x1);\n        let angle = Math.atan(tan) * 180 / Math.PI;\n        if (cx < x1) {\n            angle += 180;\n        }\n        const ry = this.getRY();\n        return { cx, cy, rx, ry, angle };\n    }\n    /**\n     * Get the value of minor radius of the ellipse.\n     * @private\n     */\n    getRY() {\n        const yAxis = this.getYAxis();\n        return ControllableEllipse_defined(yAxis) ?\n            Math.abs(yAxis.toPixels(this.options.ry) - yAxis.toPixels(0)) :\n            this.options.ry;\n    }\n    /**\n     * Get the yAxis object to which the ellipse is pinned.\n     * @private\n     */\n    getYAxis() {\n        const yAxisIndex = this.options.yAxis;\n        return this.chart.yAxis[yAxisIndex];\n    }\n    /**\n     * Get the absolute coordinates of the MockPoint\n     * @private\n     * @param point\n     *        MockPoint that is added through options\n     */\n    getAbsolutePosition(point) {\n        return this.anchor(point).absolutePosition;\n    }\n    /**\n     * Redraw the element\n     * @private\n     * @param animation\n     *        Display an animation\n     */\n    redraw(animation) {\n        if (this.graphic) {\n            const position = this.getAbsolutePosition(this.points[0]), position2 = this.getAbsolutePosition(this.points[1]), attrs = this.getAttrs(position, position2);\n            if (position) {\n                this.graphic[animation ? 'animate' : 'attr']({\n                    cx: attrs.cx,\n                    cy: attrs.cy,\n                    rx: attrs.rx,\n                    ry: attrs.ry,\n                    rotation: attrs.angle,\n                    rotationOriginX: attrs.cx,\n                    rotationOriginY: attrs.cy\n                });\n            }\n            else {\n                this.graphic.attr({\n                    x: 0,\n                    y: -9e9\n                });\n            }\n            this.graphic.placed = Boolean(position);\n        }\n        super.redraw(animation);\n    }\n    /**\n     * Set the radius Y.\n     * @private\n     * @param {number} ry\n     *        A radius in y direction to be set\n     */\n    setYRadius(ry) {\n        const shapes = this.annotation.userOptions.shapes;\n        this.options.ry = ry;\n        if (shapes && shapes[0]) {\n            shapes[0].ry = ry;\n            shapes[0].ry = ry;\n        }\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * A map object which allows to map options attributes to element\n * attributes.\n *\n * @name Highcharts.AnnotationControllableEllipse.attrsMap\n * @type {Highcharts.Dictionary<string>}\n */\nControllableEllipse.attrsMap = ControllableEllipse_merge(Controllables_ControllablePath.attrsMap, {\n    ry: 'ry'\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Controllables_ControllableEllipse = (ControllableEllipse);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Templating\"],\"commonjs\":[\"highcharts\",\"Templating\"],\"commonjs2\":[\"highcharts\",\"Templating\"],\"root\":[\"Highcharts\",\"Templating\"]}\nvar highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_ = __webpack_require__(984);\nvar highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default = /*#__PURE__*/__webpack_require__.n(highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_);\n;// ./code/es-modules/Extensions/Annotations/Controllables/ControllableLabel.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { format } = (highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default());\n\n\nconst { extend: ControllableLabel_extend, getAlignFactor, isNumber, pick: ControllableLabel_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * General symbol definition for labels with connector\n * @private\n */\nfunction symbolConnector(x, y, w, h, options) {\n    const anchorX = options && options.anchorX, anchorY = options && options.anchorY;\n    let path, yOffset, lateral = w / 2;\n    if (isNumber(anchorX) && isNumber(anchorY)) {\n        path = [['M', anchorX, anchorY]];\n        // Prefer 45 deg connectors\n        yOffset = y - anchorY;\n        if (yOffset < 0) {\n            yOffset = -h - yOffset;\n        }\n        if (yOffset < w) {\n            lateral = anchorX < x + (w / 2) ? yOffset : w - yOffset;\n        }\n        // Anchor below label\n        if (anchorY > y + h) {\n            path.push(['L', x + lateral, y + h]);\n            // Anchor above label\n        }\n        else if (anchorY < y) {\n            path.push(['L', x + lateral, y]);\n            // Anchor left of label\n        }\n        else if (anchorX < x) {\n            path.push(['L', x, y + h / 2]);\n            // Anchor right of label\n        }\n        else if (anchorX > x + w) {\n            path.push(['L', x + w, y + h / 2]);\n        }\n    }\n    return path || [];\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * A controllable label class.\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationControllableLabel\n *\n * @param {Highcharts.Annotation} annotation\n * An annotation instance.\n * @param {Highcharts.AnnotationsLabelOptions} options\n * A label's options.\n * @param {number} index\n * Index of the label.\n */\nclass ControllableLabel extends Controllables_Controllable {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Returns new aligned position based alignment options and box to align to.\n     * It is almost a one-to-one copy from SVGElement.prototype.align\n     * except it does not use and mutate an element\n     *\n     * @param {Highcharts.AnnotationAlignObject} alignOptions\n     *\n     * @param {Highcharts.BBoxObject} box\n     *\n     * @return {Highcharts.PositionObject}\n     * Aligned position.\n     */\n    static alignedPosition(alignOptions, box) {\n        return {\n            x: Math.round((box.x || 0) + (alignOptions.x || 0) +\n                (box.width - (alignOptions.width || 0)) *\n                    getAlignFactor(alignOptions.align)),\n            y: Math.round((box.y || 0) + (alignOptions.y || 0) +\n                (box.height - (alignOptions.height || 0)) *\n                    getAlignFactor(alignOptions.verticalAlign))\n        };\n    }\n    static compose(SVGRendererClass) {\n        const symbols = SVGRendererClass.prototype.symbols;\n        symbols.connector = symbolConnector;\n    }\n    /**\n     * Returns new alignment options for a label if the label is outside the\n     * plot area. It is almost a one-to-one copy from\n     * Series.prototype.justifyDataLabel except it does not mutate the label and\n     * it works with absolute instead of relative position.\n     */\n    static justifiedOptions(chart, label, alignOptions, alignAttr) {\n        const align = alignOptions.align, verticalAlign = alignOptions.verticalAlign, padding = label.box ? 0 : (label.padding || 0), bBox = label.getBBox(), \n        //\n        options = {\n            align: align,\n            verticalAlign: verticalAlign,\n            x: alignOptions.x,\n            y: alignOptions.y,\n            width: label.width,\n            height: label.height\n        }, \n        //\n        x = (alignAttr.x || 0) - chart.plotLeft, y = (alignAttr.y || 0) - chart.plotTop;\n        let off;\n        // Off left\n        off = x + padding;\n        if (off < 0) {\n            if (align === 'right') {\n                options.align = 'left';\n            }\n            else {\n                options.x = (options.x || 0) - off;\n            }\n        }\n        // Off right\n        off = x + bBox.width - padding;\n        if (off > chart.plotWidth) {\n            if (align === 'left') {\n                options.align = 'right';\n            }\n            else {\n                options.x = (options.x || 0) + chart.plotWidth - off;\n            }\n        }\n        // Off top\n        off = y + padding;\n        if (off < 0) {\n            if (verticalAlign === 'bottom') {\n                options.verticalAlign = 'top';\n            }\n            else {\n                options.y = (options.y || 0) - off;\n            }\n        }\n        // Off bottom\n        off = y + bBox.height - padding;\n        if (off > chart.plotHeight) {\n            if (verticalAlign === 'top') {\n                options.verticalAlign = 'bottom';\n            }\n            else {\n                options.y = (options.y || 0) + chart.plotHeight - off;\n            }\n        }\n        return options;\n    }\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    constructor(annotation, options, index) {\n        super(annotation, options, index, 'label');\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Translate the point of the label by deltaX and deltaY translations.\n     * The point is the label's anchor.\n     *\n     * @param {number} dx translation for x coordinate\n     * @param {number} dy translation for y coordinate\n     */\n    translatePoint(dx, dy) {\n        super.translatePoint(dx, dy, 0);\n    }\n    /**\n     * Translate x and y position relative to the label's anchor.\n     *\n     * @param {number} dx translation for x coordinate\n     * @param {number} dy translation for y coordinate\n     */\n    translate(dx, dy) {\n        const chart = this.annotation.chart, \n        // Annotation.options\n        labelOptions = this.annotation.userOptions, \n        // Chart.options.annotations\n        annotationIndex = chart.annotations.indexOf(this.annotation), chartAnnotations = chart.options.annotations, chartOptions = chartAnnotations[annotationIndex];\n        if (chart.inverted) {\n            const temp = dx;\n            dx = dy;\n            dy = temp;\n        }\n        // Local options:\n        this.options.x += dx;\n        this.options.y += dy;\n        // Options stored in chart:\n        chartOptions[this.collection][this.index].x = this.options.x;\n        chartOptions[this.collection][this.index].y = this.options.y;\n        labelOptions[this.collection][this.index].x = this.options.x;\n        labelOptions[this.collection][this.index].y = this.options.y;\n    }\n    render(parent) {\n        const options = this.options, attrs = this.attrsFromOptions(options), style = options.style, optionsChart = this.annotation.chart.options.chart, chartBackground = optionsChart.plotBackgroundColor ||\n            optionsChart.backgroundColor;\n        this.graphic = this.annotation.chart.renderer\n            .label('', 0, -9999, // #10055\n        options.shape, void 0, void 0, options.useHTML, void 0, 'annotation-label')\n            .attr(attrs)\n            .add(parent);\n        if (!this.annotation.chart.styledMode) {\n            if (style.color === 'contrast') {\n                const background = (ControllableLabel.shapesWithoutBackground.indexOf(options.shape) > -1 ||\n                    options.backgroundColor === 'none') ?\n                    chartBackground :\n                    options.backgroundColor;\n                style.color = this.annotation.chart.renderer.getContrast(typeof background === 'string' ? background :\n                    typeof chartBackground === 'string' ? chartBackground :\n                        '#ffffff');\n            }\n            this.graphic\n                .css(options.style)\n                .shadow(options.shadow);\n        }\n        this.graphic.labelrank = options.labelrank;\n        super.render();\n    }\n    redraw(animation) {\n        const options = this.options, text = this.text || options.format || options.text, label = this.graphic, point = this.points[0];\n        if (!label) {\n            this.redraw(animation);\n            return;\n        }\n        label.attr({\n            text: text ?\n                format(String(text), point, this.annotation.chart) :\n                options.formatter.call(point, this)\n        });\n        const anchor = this.anchor(point);\n        const attrs = this.position(anchor);\n        if (attrs) {\n            label.alignAttr = attrs;\n            attrs.anchorX = anchor.absolutePosition.x;\n            attrs.anchorY = anchor.absolutePosition.y;\n            label[animation ? 'animate' : 'attr'](attrs);\n        }\n        else {\n            label.attr({\n                x: 0,\n                y: -9999 // #10055\n            });\n        }\n        label.placed = !!attrs;\n        super.redraw(animation);\n    }\n    /**\n     * All basic shapes don't support alignTo() method except label.\n     * For a controllable label, we need to subtract translation from\n     * options.\n     */\n    anchor(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _point) {\n        const anchor = super.anchor.apply(this, arguments), x = this.options.x || 0, y = this.options.y || 0;\n        anchor.absolutePosition.x -= x;\n        anchor.absolutePosition.y -= y;\n        anchor.relativePosition.x -= x;\n        anchor.relativePosition.y -= y;\n        return anchor;\n    }\n    /**\n     * Returns the label position relative to its anchor.\n     */\n    position(anchor) {\n        const item = this.graphic, chart = this.annotation.chart, tooltip = chart.tooltip, point = this.points[0], itemOptions = this.options, anchorAbsolutePosition = anchor.absolutePosition, anchorRelativePosition = anchor.relativePosition;\n        let itemPosition, alignTo, itemPosRelativeX, itemPosRelativeY, showItem = point.series.visible &&\n            Annotations_MockPoint.prototype.isInsidePlot.call(point);\n        if (item && showItem) {\n            const { width = 0, height = 0 } = item;\n            if (itemOptions.distance && tooltip) {\n                itemPosition = tooltip.getPosition.call({\n                    chart,\n                    distance: ControllableLabel_pick(itemOptions.distance, 16),\n                    getPlayingField: tooltip.getPlayingField,\n                    pointer: tooltip.pointer\n                }, width, height, {\n                    plotX: anchorRelativePosition.x,\n                    plotY: anchorRelativePosition.y,\n                    negative: point.negative,\n                    ttBelow: point.ttBelow,\n                    h: (anchorRelativePosition.height ||\n                        anchorRelativePosition.width)\n                });\n            }\n            else if (itemOptions.positioner) {\n                itemPosition = itemOptions.positioner.call(this);\n            }\n            else {\n                alignTo = {\n                    x: anchorAbsolutePosition.x,\n                    y: anchorAbsolutePosition.y,\n                    width: 0,\n                    height: 0\n                };\n                itemPosition = ControllableLabel.alignedPosition(ControllableLabel_extend(itemOptions, {\n                    width,\n                    height\n                }), alignTo);\n                if (this.options.overflow === 'justify') {\n                    itemPosition = ControllableLabel.alignedPosition(ControllableLabel.justifiedOptions(chart, item, itemOptions, itemPosition), alignTo);\n                }\n            }\n            if (itemOptions.crop) {\n                itemPosRelativeX = itemPosition.x - chart.plotLeft;\n                itemPosRelativeY = itemPosition.y - chart.plotTop;\n                showItem =\n                    chart.isInsidePlot(itemPosRelativeX, itemPosRelativeY) &&\n                        chart.isInsidePlot(itemPosRelativeX + width, itemPosRelativeY + height);\n            }\n        }\n        return showItem ? itemPosition : null;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * A map object which allows to map options attributes to element attributes\n *\n * @type {Highcharts.Dictionary<string>}\n */\nControllableLabel.attrsMap = {\n    backgroundColor: 'fill',\n    borderColor: 'stroke',\n    borderWidth: 'stroke-width',\n    zIndex: 'zIndex',\n    borderRadius: 'r',\n    padding: 'padding'\n};\n/**\n * Shapes which do not have background - the object is used for proper\n * setting of the contrast color.\n *\n * @type {Array<string>}\n */\nControllableLabel.shapesWithoutBackground = ['connector'];\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Controllables_ControllableLabel = (ControllableLabel);\n\n;// ./code/es-modules/Extensions/Annotations/Controllables/ControllableImage.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * A controllable image class.\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationControllableImage\n *\n * @param {Highcharts.Annotation} annotation\n * An annotation instance.\n *\n * @param {Highcharts.AnnotationsShapeOptions} options\n * A controllable's options.\n *\n * @param {number} index\n * Index of the image.\n */\nclass ControllableImage extends Controllables_Controllable {\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    constructor(annotation, options, index) {\n        super(annotation, options, index, 'shape');\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.type = 'image';\n        this.translate = super.translateShape;\n    }\n    render(parent) {\n        const attrs = this.attrsFromOptions(this.options), options = this.options;\n        this.graphic = this.annotation.chart.renderer\n            .image(options.src, 0, -9e9, options.width, options.height)\n            .attr(attrs)\n            .add(parent);\n        this.graphic.width = options.width;\n        this.graphic.height = options.height;\n        super.render();\n    }\n    redraw(animation) {\n        if (this.graphic) {\n            const anchor = this.anchor(this.points[0]), position = Controllables_ControllableLabel.prototype.position.call(this, anchor);\n            if (position) {\n                this.graphic[animation ? 'animate' : 'attr']({\n                    x: position.x,\n                    y: position.y\n                });\n            }\n            else {\n                this.graphic.attr({\n                    x: 0,\n                    y: -9e9\n                });\n            }\n            this.graphic.placed = Boolean(position);\n        }\n        super.redraw(animation);\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * A map object which allows to map options attributes to element attributes\n *\n * @name Highcharts.AnnotationControllableImage.attrsMap\n * @type {Highcharts.Dictionary<string>}\n */\nControllableImage.attrsMap = {\n    width: 'width',\n    height: 'height',\n    zIndex: 'zIndex'\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Controllables_ControllableImage = (ControllableImage);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"AST\"],\"commonjs\":[\"highcharts\",\"AST\"],\"commonjs2\":[\"highcharts\",\"AST\"],\"root\":[\"Highcharts\",\"AST\"]}\nvar highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_ = __webpack_require__(660);\nvar highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default = /*#__PURE__*/__webpack_require__.n(highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_);\n;// ./code/es-modules/Shared/BaseForm.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\n\nconst { addEvent: BaseForm_addEvent, createElement } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass BaseForm {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(parentDiv, iconsURL) {\n        this.iconsURL = iconsURL;\n        this.container = this.createPopupContainer(parentDiv);\n        this.closeButton = this.addCloseButton();\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Create popup div container.\n     *\n     * @param {HTMLElement} parentDiv\n     * Parent div to attach popup.\n     *\n     * @param  {string} className\n     * Class name of the popup.\n     *\n     * @return {HTMLElement}\n     * Popup div.\n     */\n    createPopupContainer(parentDiv, className = 'highcharts-popup highcharts-no-tooltip') {\n        return createElement('div', { className }, void 0, parentDiv);\n    }\n    /**\n     * Create HTML element and attach click event to close popup.\n     *\n     * @param {string} className\n     * Class name of the close button.\n     *\n     * @return {HTMLElement}\n     * Close button.\n     */\n    addCloseButton(className = 'highcharts-popup-close') {\n        const popup = this, iconsURL = this.iconsURL;\n        // Create close popup button.\n        const closeButton = createElement('button', { className }, void 0, this.container);\n        createElement('span', {\n            className: 'highcharts-icon'\n        }, {\n            backgroundImage: 'url(' + (iconsURL.match(/png|svg|jpeg|jpg|gif/ig) ?\n                iconsURL : iconsURL + 'close.svg') + ')'\n        }, closeButton);\n        ['click', 'touchstart'].forEach((eventName) => {\n            BaseForm_addEvent(closeButton, eventName, popup.closeButtonEvents.bind(popup));\n        });\n        // Close popup when press ESC\n        BaseForm_addEvent(document, 'keydown', function (event) {\n            if (event.code === 'Escape') {\n                popup.closeButtonEvents();\n            }\n        });\n        return closeButton;\n    }\n    /**\n     * Close button events.\n     * @return {void}\n     */\n    closeButtonEvents() {\n        this.closePopup();\n    }\n    /**\n     * Reset content of the current popup and show.\n     *\n     * @param {string} toolbarClass\n     * Class name of the toolbar which styles should be reset.\n     */\n    showPopup(toolbarClass = 'highcharts-annotation-toolbar') {\n        const popupDiv = this.container, popupCloseButton = this.closeButton;\n        this.type = void 0;\n        // Reset content.\n        popupDiv.innerHTML = (highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default()).emptyHTML;\n        // Reset toolbar styles if exists.\n        if (popupDiv.className.indexOf(toolbarClass) >= 0) {\n            popupDiv.classList.remove(toolbarClass);\n            // Reset toolbar inline styles\n            popupDiv.removeAttribute('style');\n        }\n        // Add close button.\n        popupDiv.appendChild(popupCloseButton);\n        popupDiv.style.display = 'block';\n        popupDiv.style.height = '';\n    }\n    /**\n     * Hide popup.\n     */\n    closePopup() {\n        this.container.style.display = 'none';\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Shared_BaseForm = (BaseForm);\n\n;// ./code/es-modules/Extensions/Annotations/Popup/PopupAnnotations.js\n/* *\n *\n *  Popup generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { doc: PopupAnnotations_doc, isFirefox } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { createElement: PopupAnnotations_createElement, isArray: PopupAnnotations_isArray, isObject: PopupAnnotations_isObject, objectEach: PopupAnnotations_objectEach, pick: PopupAnnotations_pick, stableSort } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Create annotation simple form.\n * It contains fields with param names.\n * @private\n * @param {Highcharts.Chart} chart\n * Chart\n * @param {Object} options\n * Options\n * @param {Function} callback\n * On click callback\n * @param {boolean} [isInit]\n * If it is a form declared for init annotation\n */\nfunction addForm(chart, options, callback, isInit) {\n    if (!chart) {\n        return;\n    }\n    const popupDiv = this.container, lang = this.lang;\n    // Create title of annotations\n    let lhsCol = PopupAnnotations_createElement('h2', {\n        className: 'highcharts-popup-main-title'\n    }, void 0, popupDiv);\n    lhsCol.appendChild(PopupAnnotations_doc.createTextNode(lang[options.langKey] || options.langKey || ''));\n    // Left column\n    lhsCol = PopupAnnotations_createElement('div', {\n        className: ('highcharts-popup-lhs-col highcharts-popup-lhs-full')\n    }, void 0, popupDiv);\n    const bottomRow = PopupAnnotations_createElement('div', {\n        className: 'highcharts-popup-bottom-row'\n    }, void 0, popupDiv);\n    addFormFields.call(this, lhsCol, chart, '', options, [], true);\n    this.addButton(bottomRow, isInit ?\n        (lang.addButton || 'Add') :\n        (lang.saveButton || 'Save'), isInit ? 'add' : 'save', popupDiv, callback);\n}\n/**\n * Create annotation simple form. It contains two buttons\n * (edit / remove) and text label.\n * @private\n * @param {Highcharts.Chart} - chart\n * @param {Highcharts.AnnotationsOptions} - options\n * @param {Function} - on click callback\n */\nfunction addToolbar(chart, options, callback) {\n    const lang = this.lang, popupDiv = this.container, showForm = this.showForm, toolbarClass = 'highcharts-annotation-toolbar';\n    // Set small size\n    if (popupDiv.className.indexOf(toolbarClass) === -1) {\n        popupDiv.className += ' ' + toolbarClass + ' highcharts-no-mousewheel';\n    }\n    // Set position\n    if (chart) {\n        popupDiv.style.top = chart.plotTop + 10 + 'px';\n    }\n    // Create label\n    const label = PopupAnnotations_createElement('p', {\n        className: 'highcharts-annotation-label'\n    }, void 0, popupDiv);\n    label.setAttribute('aria-label', 'Annotation type');\n    label.appendChild(PopupAnnotations_doc.createTextNode(PopupAnnotations_pick(\n    // Advanced annotations:\n    lang[options.langKey] || options.langKey, \n    // Basic shapes:\n    options.shapes && options.shapes[0].type, '')));\n    // Add buttons\n    let button = this.addButton(popupDiv, lang.editButton || 'Edit', 'edit', popupDiv, () => {\n        showForm.call(this, 'annotation-edit', chart, options, callback);\n    });\n    button.className += ' highcharts-annotation-edit-button';\n    PopupAnnotations_createElement('span', {\n        className: 'highcharts-icon'\n    }, {\n        backgroundImage: `url(${this.iconsURL}edit.svg)`\n    }, button);\n    button = this.addButton(popupDiv, lang.removeButton || 'Remove', 'remove', popupDiv, callback);\n    button.className += ' highcharts-annotation-remove-button';\n    PopupAnnotations_createElement('span', {\n        className: 'highcharts-icon'\n    }, {\n        backgroundImage: `url(${this.iconsURL}destroy.svg)`\n    }, button);\n}\n/**\n * Create annotation's form fields.\n * @private\n * @param {Highcharts.HTMLDOMElement} parentDiv\n * Div where inputs are placed\n * @param {Highcharts.Chart} chart\n * Chart\n * @param {string} parentNode\n * Name of parent to create chain of names\n * @param {Highcharts.AnnotationsOptions} options\n * Options\n * @param {Array<unknown>} storage\n * Array where all items are stored\n * @param {boolean} [isRoot]\n * Recursive flag for root\n */\nfunction addFormFields(parentDiv, chart, parentNode, options, storage, isRoot) {\n    if (!chart) {\n        return;\n    }\n    const addInput = this.addInput, lang = this.lang;\n    let parentFullName, titleName;\n    PopupAnnotations_objectEach(options, (value, option) => {\n        // Create name like params.styles.fontSize\n        parentFullName = parentNode !== '' ? parentNode + '.' + option : option;\n        if (PopupAnnotations_isObject(value)) {\n            if (\n            // Value is object of options\n            !PopupAnnotations_isArray(value) ||\n                // Array of objects with params. i.e labels in Fibonacci\n                (PopupAnnotations_isArray(value) && PopupAnnotations_isObject(value[0]))) {\n                titleName = lang[option] || option;\n                if (!titleName.match(/\\d/g)) {\n                    storage.push([\n                        true,\n                        titleName,\n                        parentDiv\n                    ]);\n                }\n                addFormFields.call(this, parentDiv, chart, parentFullName, value, storage, false);\n            }\n            else {\n                storage.push([\n                    this,\n                    parentFullName,\n                    'annotation',\n                    parentDiv,\n                    value\n                ]);\n            }\n        }\n    });\n    if (isRoot) {\n        stableSort(storage, (a) => (a[1].match(/format/g) ? -1 : 1));\n        if (isFirefox) {\n            storage.reverse(); // (#14691)\n        }\n        storage.forEach((genInput) => {\n            if (genInput[0] === true) {\n                PopupAnnotations_createElement('span', {\n                    className: 'highcharts-annotation-title'\n                }, void 0, genInput[2]).appendChild(PopupAnnotations_doc.createTextNode(genInput[1]));\n            }\n            else {\n                genInput[4] = {\n                    value: genInput[4][0],\n                    type: genInput[4][1]\n                };\n                addInput.apply(genInput[0], genInput.splice(1));\n            }\n        });\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst PopupAnnotations = {\n    addForm,\n    addToolbar\n};\n/* harmony default export */ const Popup_PopupAnnotations = (PopupAnnotations);\n\n;// ./code/es-modules/Extensions/Annotations/Popup/PopupIndicators.js\n/* *\n *\n *  Popup generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { doc: PopupIndicators_doc } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { seriesTypes } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { addEvent: PopupIndicators_addEvent, createElement: PopupIndicators_createElement, defined: PopupIndicators_defined, isArray: PopupIndicators_isArray, isObject: PopupIndicators_isObject, objectEach: PopupIndicators_objectEach, stableSort: PopupIndicators_stableSort } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Enums\n *\n * */\n/**\n * Enum for properties which should have dropdown list.\n * @private\n */\nvar DropdownProperties;\n(function (DropdownProperties) {\n    DropdownProperties[DropdownProperties[\"params.algorithm\"] = 0] = \"params.algorithm\";\n    DropdownProperties[DropdownProperties[\"params.average\"] = 1] = \"params.average\";\n})(DropdownProperties || (DropdownProperties = {}));\n/**\n * List of available algorithms for the specific indicator.\n * @private\n */\nconst dropdownParameters = {\n    'algorithm-pivotpoints': ['standard', 'fibonacci', 'camarilla'],\n    'average-disparityindex': ['sma', 'ema', 'dema', 'tema', 'wma']\n};\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Create two columns (divs) in HTML.\n * @private\n * @param {Highcharts.HTMLDOMElement} container\n * Container of columns\n * @return {Highcharts.Dictionary<Highcharts.HTMLDOMElement>}\n * Reference to two HTML columns (lhsCol, rhsCol)\n */\nfunction addColsContainer(container) {\n    // Left column\n    const lhsCol = PopupIndicators_createElement('div', {\n        className: 'highcharts-popup-lhs-col'\n    }, void 0, container);\n    // Right column\n    const rhsCol = PopupIndicators_createElement('div', {\n        className: 'highcharts-popup-rhs-col'\n    }, void 0, container);\n    // Wrapper content\n    PopupIndicators_createElement('div', {\n        className: 'highcharts-popup-rhs-col-wrapper'\n    }, void 0, rhsCol);\n    return {\n        lhsCol: lhsCol,\n        rhsCol: rhsCol\n    };\n}\n/**\n * Create indicator's form. It contains two tabs (ADD and EDIT) with\n * content.\n * @private\n */\nfunction PopupIndicators_addForm(chart, _options, callback) {\n    const lang = this.lang;\n    let buttonParentDiv;\n    if (!chart) {\n        return;\n    }\n    // Add tabs\n    this.tabs.init.call(this, chart);\n    // Get all tabs content divs\n    const tabsContainers = this.container\n        .querySelectorAll('.highcharts-tab-item-content');\n    // ADD tab\n    addColsContainer(tabsContainers[0]);\n    addSearchBox.call(this, chart, tabsContainers[0]);\n    addIndicatorList.call(this, chart, tabsContainers[0], 'add');\n    buttonParentDiv = tabsContainers[0]\n        .querySelectorAll('.highcharts-popup-rhs-col')[0];\n    this.addButton(buttonParentDiv, lang.addButton || 'add', 'add', buttonParentDiv, callback);\n    // EDIT tab\n    addColsContainer(tabsContainers[1]);\n    addIndicatorList.call(this, chart, tabsContainers[1], 'edit');\n    buttonParentDiv = tabsContainers[1]\n        .querySelectorAll('.highcharts-popup-rhs-col')[0];\n    this.addButton(buttonParentDiv, lang.saveButton || 'save', 'edit', buttonParentDiv, callback);\n    this.addButton(buttonParentDiv, lang.removeButton || 'remove', 'remove', buttonParentDiv, callback);\n}\n/**\n * Create typical inputs for chosen indicator. Fields are extracted from\n * defaultOptions (ADD mode) or current indicator (ADD mode). Two extra\n * fields are added:\n * - hidden input - contains indicator type (required for callback)\n * - select - list of series which can be linked with indicator\n * @private\n * @param {Highcharts.Chart} chart\n * Chart\n * @param {Highcharts.Series} series\n * Indicator\n * @param {string} seriesType\n * Indicator type like: sma, ema, etc.\n * @param {Highcharts.HTMLDOMElement} rhsColWrapper\n * Element where created HTML list is added\n */\nfunction PopupIndicators_addFormFields(chart, series, seriesType, rhsColWrapper) {\n    const fields = series.params || series.options.params;\n    // Reset current content\n    rhsColWrapper.innerHTML = (highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default()).emptyHTML;\n    // Create title (indicator name in the right column)\n    PopupIndicators_createElement('h3', {\n        className: 'highcharts-indicator-title'\n    }, void 0, rhsColWrapper).appendChild(PopupIndicators_doc.createTextNode(getNameType(series, seriesType).indicatorFullName));\n    // Input type\n    PopupIndicators_createElement('input', {\n        type: 'hidden',\n        name: 'highcharts-type-' + seriesType,\n        value: seriesType\n    }, void 0, rhsColWrapper);\n    // List all series with id\n    listAllSeries.call(this, seriesType, 'series', chart, rhsColWrapper, series, series.linkedParent && series.linkedParent.options.id);\n    if (fields.volumeSeriesID) {\n        listAllSeries.call(this, seriesType, 'volume', chart, rhsColWrapper, series, series.linkedParent && fields.volumeSeriesID);\n    }\n    // Add param fields\n    addParamInputs.call(this, chart, 'params', fields, seriesType, rhsColWrapper);\n}\n/**\n * Create HTML list of all indicators (ADD mode) or added indicators\n * (EDIT mode).\n *\n * @private\n *\n * @param {Highcharts.AnnotationChart} chart\n *        The chart object.\n *\n * @param {string} [optionName]\n *        Name of the option into which selection is being added.\n *\n * @param {HTMLDOMElement} [parentDiv]\n *        HTML parent element.\n *\n * @param {string} listType\n *        Type of list depending on the selected bookmark.\n *        Might be 'add' or 'edit'.\n *\n * @param {string|undefined} filter\n *        Applied filter string from the input.\n *        For the first iteration, it's an empty string.\n */\nfunction addIndicatorList(chart, parentDiv, listType, filter) {\n    /**\n     *\n     */\n    function selectIndicator(series, indicatorType) {\n        const button = rhsColWrapper.parentNode\n            .children[1];\n        PopupIndicators_addFormFields.call(popup, chart, series, indicatorType, rhsColWrapper);\n        if (button) {\n            button.style.display = 'block';\n        }\n        // Add hidden input with series.id\n        if (isEdit && series.options) {\n            PopupIndicators_createElement('input', {\n                type: 'hidden',\n                name: 'highcharts-id-' + indicatorType,\n                value: series.options.id\n            }, void 0, rhsColWrapper).setAttribute('highcharts-data-series-id', series.options.id);\n        }\n    }\n    const popup = this, lang = popup.lang, lhsCol = parentDiv.querySelectorAll('.highcharts-popup-lhs-col')[0], rhsCol = parentDiv.querySelectorAll('.highcharts-popup-rhs-col')[0], isEdit = listType === 'edit', series = (isEdit ?\n        chart.series : // EDIT mode\n        chart.options.plotOptions || {} // ADD mode\n    );\n    if (!chart && series) {\n        return;\n    }\n    let item, filteredSeriesArray = [];\n    // Filter and sort the series.\n    if (!isEdit && !PopupIndicators_isArray(series)) {\n        // Apply filters only for the 'add' indicator list.\n        filteredSeriesArray = filterSeries.call(this, series, filter);\n    }\n    else if (PopupIndicators_isArray(series)) {\n        filteredSeriesArray = filterSeriesArray.call(this, series);\n    }\n    // Sort indicators alphabetically.\n    PopupIndicators_stableSort(filteredSeriesArray, (a, b) => {\n        const seriesAName = a.indicatorFullName.toLowerCase(), seriesBName = b.indicatorFullName.toLowerCase();\n        return (seriesAName < seriesBName) ?\n            -1 : (seriesAName > seriesBName) ? 1 : 0;\n    });\n    // If the list exists remove it from the DOM\n    // in order to create a new one with different filters.\n    if (lhsCol.children[1]) {\n        lhsCol.children[1].remove();\n    }\n    // Create wrapper for list.\n    const indicatorList = PopupIndicators_createElement('ul', {\n        className: 'highcharts-indicator-list'\n    }, void 0, lhsCol);\n    const rhsColWrapper = rhsCol.querySelectorAll('.highcharts-popup-rhs-col-wrapper')[0];\n    filteredSeriesArray.forEach((seriesSet) => {\n        const { indicatorFullName, indicatorType, series } = seriesSet;\n        item = PopupIndicators_createElement('li', {\n            className: 'highcharts-indicator-list'\n        }, void 0, indicatorList);\n        const btn = PopupIndicators_createElement('button', {\n            className: 'highcharts-indicator-list-item',\n            textContent: indicatorFullName\n        }, void 0, item);\n        ['click', 'touchstart'].forEach((eventName) => {\n            PopupIndicators_addEvent(btn, eventName, function () {\n                selectIndicator(series, indicatorType);\n            });\n        });\n    });\n    // Select first item from the list\n    if (filteredSeriesArray.length > 0) {\n        const { series, indicatorType } = filteredSeriesArray[0];\n        selectIndicator(series, indicatorType);\n    }\n    else if (!isEdit) {\n        highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default().setElementHTML(rhsColWrapper.parentNode.children[0], lang.noFilterMatch || '');\n        rhsColWrapper.parentNode.children[1]\n            .style.display = 'none';\n    }\n}\n/**\n * Recurrent function which lists all fields, from params object and\n * create them as inputs. Each input has unique `data-name` attribute,\n * which keeps chain of fields i.e params.styles.fontSize.\n * @private\n * @param {Highcharts.Chart} chart\n * Chart\n * @param {string} parentNode\n * Name of parent to create chain of names\n * @param {Highcharts.PopupFieldsDictionary<string>} fields\n * Params which are based for input create\n * @param {string} type\n * Indicator type like: sma, ema, etc.\n * @param {Highcharts.HTMLDOMElement} parentDiv\n * Element where created HTML list is added\n */\nfunction addParamInputs(chart, parentNode, fields, type, parentDiv) {\n    if (!chart) {\n        return;\n    }\n    const addInput = this.addInput;\n    PopupIndicators_objectEach(fields, (value, fieldName) => {\n        // Create name like params.styles.fontSize\n        const parentFullName = parentNode + '.' + fieldName;\n        if (PopupIndicators_defined(value) && // Skip if field is unnecessary, #15362\n            parentFullName) {\n            if (PopupIndicators_isObject(value)) {\n                // (15733) 'Periods' has an arrayed value. Label must be\n                // created here.\n                addInput.call(this, parentFullName, type, parentDiv, {});\n                addParamInputs.call(this, chart, parentFullName, value, type, parentDiv);\n            }\n            // If the option is listed in dropdown enum,\n            // add the selection box for it.\n            if (parentFullName in DropdownProperties) {\n                // Add selection boxes.\n                const selectBox = addSelection.call(this, type, parentFullName, parentDiv);\n                // Add possible dropdown options.\n                addSelectionOptions.call(this, chart, parentNode, selectBox, type, fieldName, value);\n            }\n            else if (\n            // Skip volume field which is created by addFormFields.\n            parentFullName !== 'params.volumeSeriesID' &&\n                !PopupIndicators_isArray(value) // Skip params declared in array.\n            ) {\n                addInput.call(this, parentFullName, type, parentDiv, {\n                    value: value,\n                    type: 'number'\n                } // All inputs are text type\n                );\n            }\n        }\n    });\n}\n/**\n * Add searchbox HTML element and its' label.\n *\n * @private\n *\n * @param {Highcharts.AnnotationChart} chart\n *        The chart object.\n *\n * @param {HTMLDOMElement} parentDiv\n *        HTML parent element.\n */\nfunction addSearchBox(chart, parentDiv) {\n    const popup = this, lhsCol = parentDiv.querySelectorAll('.highcharts-popup-lhs-col')[0], options = 'searchIndicators', inputAttributes = {\n        value: '',\n        type: 'text',\n        htmlFor: 'search-indicators',\n        labelClassName: 'highcharts-input-search-indicators-label'\n    }, clearFilterText = this.lang.clearFilter, inputWrapper = PopupIndicators_createElement('div', {\n        className: 'highcharts-input-wrapper'\n    }, void 0, lhsCol);\n    const handleInputChange = function (inputText) {\n        // Apply some filters.\n        addIndicatorList.call(popup, chart, popup.container, 'add', inputText);\n    };\n    // Add input field with the label and button.\n    const input = this.addInput(options, 'input', inputWrapper, inputAttributes), button = PopupIndicators_createElement('a', {\n        textContent: clearFilterText\n    }, void 0, inputWrapper);\n    input.classList.add('highcharts-input-search-indicators');\n    button.classList.add('clear-filter-button');\n    // Add input change events.\n    PopupIndicators_addEvent(input, 'input', function () {\n        handleInputChange(this.value);\n        // Show clear filter button.\n        if (this.value.length) {\n            button.style.display = 'inline-block';\n        }\n        else {\n            button.style.display = 'none';\n        }\n    });\n    // Add clear filter click event.\n    ['click', 'touchstart'].forEach((eventName) => {\n        PopupIndicators_addEvent(button, eventName, function () {\n            // Clear the input.\n            input.value = '';\n            handleInputChange('');\n            // Hide clear filter button- no longer necessary.\n            button.style.display = 'none';\n        });\n    });\n}\n/**\n * Add selection HTML element and its' label.\n *\n * @private\n *\n * @param {string} indicatorType\n * Type of the indicator i.e. sma, ema...\n *\n * @param {string} [optionName]\n * Name of the option into which selection is being added.\n *\n * @param {HTMLDOMElement} [parentDiv]\n * HTML parent element.\n */\nfunction addSelection(indicatorType, optionName, parentDiv) {\n    const optionParamList = optionName.split('.'), labelText = optionParamList[optionParamList.length - 1], selectName = 'highcharts-' + optionName + '-type-' + indicatorType, lang = this.lang;\n    // Add a label for the selection box.\n    PopupIndicators_createElement('label', {\n        htmlFor: selectName\n    }, null, parentDiv).appendChild(PopupIndicators_doc.createTextNode(lang[labelText] || optionName));\n    // Create a selection box.\n    const selectBox = PopupIndicators_createElement('select', {\n        name: selectName,\n        className: 'highcharts-popup-field',\n        id: 'highcharts-select-' + optionName\n    }, null, parentDiv);\n    selectBox.setAttribute('id', 'highcharts-select-' + optionName);\n    return selectBox;\n}\n/**\n * Get and add selection options.\n *\n * @private\n *\n * @param {Highcharts.AnnotationChart} chart\n *        The chart object.\n *\n * @param {string} [optionName]\n *        Name of the option into which selection is being added.\n *\n * @param {HTMLSelectElement} [selectBox]\n *        HTML select box element to which the options are being added.\n *\n * @param {string|undefined} indicatorType\n *        Type of the indicator i.e. sma, ema...\n *\n * @param {string|undefined} parameterName\n *        Name of the parameter which should be applied.\n *\n * @param {string|undefined} selectedOption\n *        Default value in dropdown.\n */\nfunction addSelectionOptions(chart, optionName, selectBox, indicatorType, parameterName, selectedOption, currentSeries) {\n    // Get and apply selection options for the possible series.\n    if (optionName === 'series' || optionName === 'volume') {\n        // List all series which have id - mandatory for indicator.\n        chart.series.forEach((series) => {\n            const seriesOptions = series.options, seriesName = seriesOptions.name ||\n                seriesOptions.params ?\n                series.name :\n                seriesOptions.id || '';\n            if (seriesOptions.id !== 'highcharts-navigator-series' &&\n                seriesOptions.id !== (currentSeries &&\n                    currentSeries.options &&\n                    currentSeries.options.id)) {\n                if (!PopupIndicators_defined(selectedOption) &&\n                    optionName === 'volume' &&\n                    series.type === 'column') {\n                    selectedOption = seriesOptions.id;\n                }\n                PopupIndicators_createElement('option', {\n                    value: seriesOptions.id\n                }, void 0, selectBox).appendChild(PopupIndicators_doc.createTextNode(seriesName));\n            }\n        });\n    }\n    else if (indicatorType && parameterName) {\n        // Get and apply options for the possible parameters.\n        const dropdownKey = parameterName + '-' + indicatorType, parameterOption = dropdownParameters[dropdownKey];\n        parameterOption.forEach((element) => {\n            PopupIndicators_createElement('option', {\n                value: element\n            }, void 0, selectBox).appendChild(PopupIndicators_doc.createTextNode(element));\n        });\n    }\n    // Add the default dropdown value if defined.\n    if (PopupIndicators_defined(selectedOption)) {\n        selectBox.value = selectedOption;\n    }\n}\n/**\n * Filter object of series which are not indicators.\n * If the filter string exists, check against it.\n *\n * @private\n *\n * @param {Highcharts.FilteredSeries} series\n *        All series are available in the plotOptions.\n *\n * @param {string|undefined} filter\n *        Applied filter string from the input.\n *        For the first iteration, it's an empty string.\n *\n * @return {Array<Highcharts.FilteredSeries>} filteredSeriesArray\n *         Returns array of filtered series based on filter string.\n */\nfunction filterSeries(series, filter) {\n    const popup = this, lang = popup.chart && popup.chart.options.lang, indicatorAliases = lang &&\n        lang.navigation &&\n        lang.navigation.popup &&\n        lang.navigation.popup.indicatorAliases, filteredSeriesArray = [];\n    let filteredSeries;\n    PopupIndicators_objectEach(series, (series, value) => {\n        const seriesOptions = series && series.options;\n        // Allow only indicators.\n        if (series.params || seriesOptions &&\n            seriesOptions.params) {\n            const { indicatorFullName, indicatorType } = getNameType(series, value);\n            if (filter) {\n                // Replace invalid characters.\n                const validFilter = filter.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n                const regex = new RegExp(validFilter, 'i'), alias = indicatorAliases &&\n                    indicatorAliases[indicatorType] &&\n                    indicatorAliases[indicatorType].join(' ') || '';\n                if (indicatorFullName.match(regex) ||\n                    alias.match(regex)) {\n                    filteredSeries = {\n                        indicatorFullName,\n                        indicatorType,\n                        series: series\n                    };\n                    filteredSeriesArray.push(filteredSeries);\n                }\n            }\n            else {\n                filteredSeries = {\n                    indicatorFullName,\n                    indicatorType,\n                    series: series\n                };\n                filteredSeriesArray.push(filteredSeries);\n            }\n        }\n    });\n    return filteredSeriesArray;\n}\n/**\n * Filter an array of series and map its names and types.\n *\n * @private\n *\n * @param {Highcharts.FilteredSeries} series\n *        All series that are available in the plotOptions.\n *\n * @return {Array<Highcharts.FilteredSeries>} filteredSeriesArray\n *         Returns array of filtered series based on filter string.\n */\nfunction filterSeriesArray(series) {\n    const filteredSeriesArray = [];\n    // Allow only indicators.\n    series.forEach((series) => {\n        if (series.is('sma')) {\n            filteredSeriesArray.push({\n                indicatorFullName: series.name,\n                indicatorType: series.type,\n                series: series\n            });\n        }\n    });\n    return filteredSeriesArray;\n}\n/**\n * Get amount of indicators added to chart.\n * @private\n * @return {number} - Amount of indicators\n */\nfunction getAmount() {\n    let counter = 0;\n    this.series.forEach((serie) => {\n        if (serie.params ||\n            serie.options.params) {\n            counter++;\n        }\n    });\n    return counter;\n}\n/**\n * Extract full name and type of requested indicator.\n *\n * @private\n *\n * @param {Highcharts.Series} series\n * Series which name is needed(EDITmode - defaultOptions.series,\n * ADDmode - indicator series).\n *\n * @param {string} [indicatorType]\n * Type of the indicator i.e. sma, ema...\n *\n * @return {Highcharts.Dictionary<string>}\n * Full name and series type.\n */\nfunction getNameType(series, indicatorType) {\n    const options = series.options;\n    // Add mode\n    let seriesName = (seriesTypes[indicatorType] &&\n        seriesTypes[indicatorType].prototype.nameBase) ||\n        indicatorType.toUpperCase(), seriesType = indicatorType;\n    // Edit\n    if (options && options.type) {\n        seriesType = series.options.type;\n        seriesName = series.name;\n    }\n    return {\n        indicatorFullName: seriesName,\n        indicatorType: seriesType\n    };\n}\n/**\n * Create the selection box for the series,\n * add options and apply the default one.\n *\n * @private\n *\n * @param {string} indicatorType\n *        Type of the indicator i.e. sma, ema...\n *\n * @param {string} [optionName]\n *        Name of the option into which selection is being added.\n *\n * @param {Highcharts.AnnotationChart} chart\n *        The chart object.\n *\n * @param {HTMLDOMElement} [parentDiv]\n *        HTML parent element.\n *\n * @param {string|undefined} selectedOption\n *        Default value in dropdown.\n */\nfunction listAllSeries(indicatorType, optionName, chart, parentDiv, currentSeries, selectedOption) {\n    const popup = this;\n    // Won't work without the chart.\n    if (!chart) {\n        return;\n    }\n    // Add selection boxes.\n    const selectBox = addSelection.call(popup, indicatorType, optionName, parentDiv);\n    // Add possible dropdown options.\n    addSelectionOptions.call(popup, chart, optionName, selectBox, void 0, void 0, void 0, currentSeries);\n    // Add the default dropdown value if defined.\n    if (PopupIndicators_defined(selectedOption)) {\n        selectBox.value = selectedOption;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst PopupIndicators = {\n    addForm: PopupIndicators_addForm,\n    getAmount\n};\n/* harmony default export */ const Popup_PopupIndicators = (PopupIndicators);\n\n;// ./code/es-modules/Extensions/Annotations/Popup/PopupTabs.js\n/* *\n *\n *  Popup generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { doc: PopupTabs_doc } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent: PopupTabs_addEvent, createElement: PopupTabs_createElement } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Create tab content\n * @private\n * @return {HTMLDOMElement} - created HTML tab-content element\n */\nfunction addContentItem() {\n    const popupDiv = this.container;\n    return PopupTabs_createElement('div', {\n        // #12100\n        className: 'highcharts-tab-item-content highcharts-no-mousewheel'\n    }, void 0, popupDiv);\n}\n/**\n * Create tab menu item\n * @private\n * @param {string} tabName\n * `add` or `edit`\n * @param {number} [disableTab]\n * Disable tab when 0\n * @return {Highcharts.HTMLDOMElement}\n * Created HTML tab-menu element\n */\nfunction addMenuItem(tabName, disableTab) {\n    const popupDiv = this.container, lang = this.lang;\n    let className = 'highcharts-tab-item';\n    if (disableTab === 0) {\n        className += ' highcharts-tab-disabled';\n    }\n    // Tab 1\n    const menuItem = PopupTabs_createElement('button', {\n        className\n    }, void 0, popupDiv);\n    menuItem.appendChild(PopupTabs_doc.createTextNode(lang[tabName + 'Button'] || tabName));\n    menuItem.setAttribute('highcharts-data-tab-type', tabName);\n    return menuItem;\n}\n/**\n * Set all tabs as invisible.\n * @private\n */\nfunction deselectAll() {\n    const popupDiv = this.container, tabs = popupDiv\n        .querySelectorAll('.highcharts-tab-item'), tabsContent = popupDiv\n        .querySelectorAll('.highcharts-tab-item-content');\n    for (let i = 0; i < tabs.length; i++) {\n        tabs[i].classList.remove('highcharts-tab-item-active');\n        tabsContent[i].classList.remove('highcharts-tab-item-show');\n    }\n}\n/**\n * Init tabs. Create tab menu items, tabs containers\n * @private\n * @param {Highcharts.Chart} chart\n * Reference to current chart\n */\nfunction init(chart) {\n    if (!chart) {\n        return;\n    }\n    const indicatorsCount = this.indicators.getAmount.call(chart);\n    // Create menu items\n    const firstTab = addMenuItem.call(this, 'add'); // Run by default\n    addMenuItem.call(this, 'edit', indicatorsCount);\n    // Create tabs containers\n    addContentItem.call(this);\n    addContentItem.call(this);\n    switchTabs.call(this, indicatorsCount);\n    // Activate first tab\n    selectTab.call(this, firstTab, 0);\n}\n/**\n * Set tab as visible\n * @private\n * @param {globals.Element} - current tab\n * @param {number} - Index of tab in menu\n */\nfunction selectTab(tab, index) {\n    const allTabs = this.container\n        .querySelectorAll('.highcharts-tab-item-content');\n    tab.className += ' highcharts-tab-item-active';\n    allTabs[index].className += ' highcharts-tab-item-show';\n}\n/**\n * Add click event to each tab\n * @private\n * @param {number} disableTab\n * Disable tab when 0\n */\nfunction switchTabs(disableTab) {\n    const popup = this, popupDiv = this.container, tabs = popupDiv.querySelectorAll('.highcharts-tab-item');\n    tabs.forEach((tab, i) => {\n        if (disableTab === 0 &&\n            tab.getAttribute('highcharts-data-tab-type') === 'edit') {\n            return;\n        }\n        ['click', 'touchstart'].forEach((eventName) => {\n            PopupTabs_addEvent(tab, eventName, function () {\n                // Reset class on other elements\n                deselectAll.call(popup);\n                selectTab.call(popup, this, i);\n            });\n        });\n    });\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst PopupTabs = {\n    init\n};\n/* harmony default export */ const Popup_PopupTabs = (PopupTabs);\n\n;// ./code/es-modules/Extensions/Annotations/Popup/Popup.js\n/* *\n *\n *  Popup generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { doc: Popup_doc } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { getOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\n\n\nconst { addEvent: Popup_addEvent, createElement: Popup_createElement, extend: Popup_extend, fireEvent: Popup_fireEvent, pick: Popup_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Get values from all inputs and selections then create JSON.\n *\n * @private\n *\n * @param {Highcharts.HTMLDOMElement} parentDiv\n * The container where inputs and selections are created.\n *\n * @param {string} type\n * Type of the popup bookmark (add|edit|remove).\n */\nfunction getFields(parentDiv, type) {\n    const inputList = Array.prototype.slice.call(parentDiv.querySelectorAll('input')), selectList = Array.prototype.slice.call(parentDiv.querySelectorAll('select')), optionSeries = '#highcharts-select-series > option:checked', optionVolume = '#highcharts-select-volume > option:checked', linkedTo = parentDiv.querySelectorAll(optionSeries)[0], volumeTo = parentDiv.querySelectorAll(optionVolume)[0];\n    const fieldsOutput = {\n        actionType: type,\n        linkedTo: linkedTo && linkedTo.getAttribute('value') || '',\n        fields: {}\n    };\n    inputList.forEach((input) => {\n        const param = input.getAttribute('highcharts-data-name'), seriesId = input.getAttribute('highcharts-data-series-id');\n        // Params\n        if (seriesId) {\n            fieldsOutput.seriesId = input.value;\n        }\n        else if (param) {\n            fieldsOutput.fields[param] = input.value;\n        }\n        else {\n            // Type like sma / ema\n            fieldsOutput.type = input.value;\n        }\n    });\n    selectList.forEach((select) => {\n        const id = select.id;\n        // Get inputs only for the parameters, not for series and volume.\n        if (id !== 'highcharts-select-series' &&\n            id !== 'highcharts-select-volume') {\n            const parameter = id.split('highcharts-select-')[1];\n            fieldsOutput.fields[parameter] = select.value;\n        }\n    });\n    if (volumeTo) {\n        fieldsOutput.fields['params.volumeSeriesID'] = volumeTo\n            .getAttribute('value') || '';\n    }\n    return fieldsOutput;\n}\n/* *\n *\n *  Class\n *\n * */\nclass Popup extends Shared_BaseForm {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(parentDiv, iconsURL, chart) {\n        super(parentDiv, iconsURL);\n        this.chart = chart;\n        this.lang = (getOptions().lang.navigation || {}).popup || {};\n        Popup_addEvent(this.container, 'mousedown', () => {\n            const activeAnnotation = chart &&\n                chart.navigationBindings &&\n                chart.navigationBindings.activeAnnotation;\n            if (activeAnnotation) {\n                activeAnnotation.cancelClick = true;\n                const unbind = Popup_addEvent(Popup_doc, 'click', () => {\n                    setTimeout(() => {\n                        activeAnnotation.cancelClick = false;\n                    }, 0);\n                    unbind();\n                });\n            }\n        });\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Create input with label.\n     *\n     * @private\n     *\n     * @param {string} option\n     *        Chain of fields i.e params.styles.fontSize separated by the dot.\n     *\n     * @param {string} indicatorType\n     *        Type of the indicator i.e. sma, ema...\n     *\n     * @param {HTMLDOMElement} parentDiv\n     *        HTML parent element.\n     *\n     * @param {Highcharts.InputAttributes} inputAttributes\n     *        Attributes of the input.\n     *\n     * @return {HTMLInputElement}\n     *         Return created input element.\n     */\n    addInput(option, indicatorType, parentDiv, inputAttributes) {\n        const optionParamList = option.split('.'), optionName = optionParamList[optionParamList.length - 1], lang = this.lang, inputName = 'highcharts-' + indicatorType + '-' + Popup_pick(inputAttributes.htmlFor, optionName);\n        if (!optionName.match(/^\\d+$/)) {\n            // Add label\n            Popup_createElement('label', {\n                htmlFor: inputName,\n                className: inputAttributes.labelClassName\n            }, void 0, parentDiv).appendChild(Popup_doc.createTextNode(lang[optionName] || optionName));\n        }\n        // Add input\n        const input = Popup_createElement('input', {\n            name: inputName,\n            value: inputAttributes.value,\n            type: inputAttributes.type,\n            className: 'highcharts-popup-field'\n        }, void 0, parentDiv);\n        input.setAttribute('highcharts-data-name', option);\n        return input;\n    }\n    closeButtonEvents() {\n        if (this.chart) {\n            const navigationBindings = this.chart.navigationBindings;\n            Popup_fireEvent(navigationBindings, 'closePopup');\n            if (navigationBindings &&\n                navigationBindings.selectedButtonElement) {\n                Popup_fireEvent(navigationBindings, 'deselectButton', { button: navigationBindings.selectedButtonElement });\n            }\n        }\n        else {\n            super.closeButtonEvents();\n        }\n    }\n    /**\n     * Create button.\n     * @private\n     * @param {Highcharts.HTMLDOMElement} parentDiv\n     * Container where elements should be added\n     * @param {string} label\n     * Text placed as button label\n     * @param {string} type\n     * add | edit | remove\n     * @param {Function} callback\n     * On click callback\n     * @param {Highcharts.HTMLDOMElement} fieldsDiv\n     * Container where inputs are generated\n     * @return {Highcharts.HTMLDOMElement}\n     * HTML button\n     */\n    addButton(parentDiv, label, type, fieldsDiv, callback) {\n        const button = Popup_createElement('button', void 0, void 0, parentDiv);\n        button.appendChild(Popup_doc.createTextNode(label));\n        if (callback) {\n            ['click', 'touchstart'].forEach((eventName) => {\n                Popup_addEvent(button, eventName, () => {\n                    this.closePopup();\n                    return callback(getFields(fieldsDiv, type));\n                });\n            });\n        }\n        return button;\n    }\n    /**\n     * Create content and show popup.\n     * @private\n     * @param {string} - type of popup i.e indicators\n     * @param {Highcharts.Chart} - chart\n     * @param {Highcharts.AnnotationsOptions} - options\n     * @param {Function} - on click callback\n     */\n    showForm(type, chart, options, callback) {\n        if (!chart) {\n            return;\n        }\n        // Show blank popup\n        this.showPopup();\n        // Indicator form\n        if (type === 'indicators') {\n            this.indicators.addForm.call(this, chart, options, callback);\n        }\n        // Annotation small toolbar\n        if (type === 'annotation-toolbar') {\n            this.annotations.addToolbar.call(this, chart, options, callback);\n        }\n        // Annotation edit form\n        if (type === 'annotation-edit') {\n            this.annotations.addForm.call(this, chart, options, callback);\n        }\n        // Flags form - add / edit\n        if (type === 'flag') {\n            this.annotations.addForm.call(this, chart, options, callback, true);\n        }\n        this.type = type;\n        // Explicit height is needed to make inner elements scrollable\n        this.container.style.height = this.container.offsetHeight + 'px';\n    }\n}\nPopup_extend(Popup.prototype, {\n    annotations: Popup_PopupAnnotations,\n    indicators: Popup_PopupIndicators,\n    tabs: Popup_PopupTabs\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Popup_Popup = (Popup);\n\n;// ./code/es-modules/Extensions/Annotations/Popup/PopupComposition.js\n/* *\n *\n *  Popup generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nconst { addEvent: PopupComposition_addEvent, pushUnique, wrap: PopupComposition_wrap } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction compose(NagivationBindingsClass, PointerClass) {\n    if (pushUnique(composed, 'Popup')) {\n        PopupComposition_addEvent(NagivationBindingsClass, 'closePopup', onNavigationBindingsClosePopup);\n        PopupComposition_addEvent(NagivationBindingsClass, 'showPopup', onNavigationBindingsShowPopup);\n        PopupComposition_wrap(PointerClass.prototype, 'onContainerMouseDown', wrapPointerOnContainerMouserDown);\n    }\n}\n/**\n * @private\n */\nfunction onNavigationBindingsClosePopup() {\n    if (this.popup) {\n        this.popup.closePopup();\n    }\n}\n/**\n * @private\n */\nfunction onNavigationBindingsShowPopup(config) {\n    if (!this.popup) {\n        // Add popup to main container\n        this.popup = new Popup_Popup(this.chart.container, (this.chart.options.navigation.iconsURL ||\n            (this.chart.options.stockTools &&\n                this.chart.options.stockTools.gui.iconsURL) ||\n            'https://code.highcharts.com/12.3.0/gfx/stock-icons/'), this.chart);\n    }\n    this.popup.showForm(config.formType, this.chart, config.options, config.onSubmit);\n}\n/**\n * `onContainerMouseDown` blocks internal popup events, due to e.preventDefault.\n * Related issue #4606\n * @private\n */\nfunction wrapPointerOnContainerMouserDown(proceed, e) {\n    // Elements is not in popup\n    if (!this.inClass(e.target, 'highcharts-popup')) {\n        proceed.apply(this, Array.prototype.slice.call(arguments, 1));\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst PopupComposition = {\n    compose\n};\n/* harmony default export */ const Popup_PopupComposition = (PopupComposition);\n\n;// ./code/es-modules/Extensions/Annotations/Annotation.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { getDeferredAnimation } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\n\n\n\n\n\n\n\n\n\nconst { defaultOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\n\n\nconst { destroyObjectProperties, erase: Annotation_erase, fireEvent: Annotation_fireEvent, merge: Annotation_merge, pick: Annotation_pick, splat } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Hide or show annotation attached to points.\n * @private\n */\nfunction adjustVisibility(item) {\n    const label = item.graphic, hasVisiblePoints = item.points.some((point) => (point.series.visible !== false &&\n        point.visible !== false));\n    if (label) {\n        if (!hasVisiblePoints) {\n            label.hide();\n        }\n        else if (label.visibility === 'hidden') {\n            label.show();\n        }\n    }\n}\n/**\n * @private\n */\nfunction getLabelsAndShapesOptions(baseOptions, newOptions) {\n    const mergedOptions = {};\n    ['labels', 'shapes'].forEach((name) => {\n        const someBaseOptions = baseOptions[name], newOptionsValue = newOptions[name];\n        if (someBaseOptions) {\n            if (newOptionsValue) {\n                mergedOptions[name] = splat(newOptionsValue).map((basicOptions, i) => Annotation_merge(someBaseOptions[i], basicOptions));\n            }\n            else {\n                mergedOptions[name] = baseOptions[name];\n            }\n        }\n    });\n    return mergedOptions;\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * An annotation class which serves as a container for items like labels or\n * shapes. Created items are positioned on the chart either by linking them to\n * existing points or created mock points\n *\n * @requires modules/annotations\n *\n * @class\n * @name Highcharts.Annotation\n *\n * @param {Highcharts.Chart} chart\n *        A chart instance\n * @param {Highcharts.AnnotationsOptions} userOptions\n *        The annotation options\n */\nclass Annotation extends Annotations_EventEmitter {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    static compose(ChartClass, NavigationBindingsClass, PointerClass, SVGRendererClass) {\n        Annotations_AnnotationChart.compose(Annotation, ChartClass, PointerClass);\n        Controllables_ControllableLabel.compose(SVGRendererClass);\n        Controllables_ControllablePath.compose(ChartClass, SVGRendererClass);\n        NavigationBindingsClass.compose(Annotation, ChartClass);\n        Popup_PopupComposition.compose(NavigationBindingsClass, PointerClass);\n    }\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    constructor(chart, userOptions) {\n        super();\n        this.coll = 'annotations';\n        /**\n         * The chart that the annotation belongs to.\n         *\n         * @name Highcharts.Annotation#chart\n         * @type {Highcharts.Chart}\n         */\n        this.chart = chart;\n        /**\n         * The array of points which defines the annotation.\n         * @private\n         * @name Highcharts.Annotation#points\n         * @type {Array<Highcharts.Point>}\n         */\n        this.points = [];\n        /**\n         * The array of control points.\n         * @private\n         * @name Highcharts.Annotation#controlPoints\n         * @type {Array<Annotation.ControlPoint>}\n         */\n        this.controlPoints = [];\n        this.coll = 'annotations';\n        this.index = -1;\n        /**\n         * The array of labels which belong to the annotation.\n         * @private\n         * @name Highcharts.Annotation#labels\n         * @type {Array<Highcharts.AnnotationLabelType>}\n         */\n        this.labels = [];\n        /**\n         * The array of shapes which belong to the annotation.\n         * @private\n         * @name Highcharts.Annotation#shapes\n         * @type {Array<Highcharts.AnnotationShapeType>}\n         */\n        this.shapes = [];\n        /**\n         * The options for the annotations.\n         *\n         * @name Highcharts.Annotation#options\n         * @type {Highcharts.AnnotationsOptions}\n         */\n        this.setOptions(userOptions);\n        /**\n         * The user options for the annotations.\n         *\n         * @name Highcharts.Annotation#userOptions\n         * @type {Highcharts.AnnotationsOptions}\n         */\n        this.userOptions = userOptions;\n        // Handle labels and shapes - those are arrays\n        // Merging does not work with arrays (stores reference)\n        const labelsAndShapes = getLabelsAndShapesOptions(this.options, userOptions);\n        this.options.labels = labelsAndShapes.labels;\n        this.options.shapes = labelsAndShapes.shapes;\n        /**\n         * The callback that reports to the overlapping labels logic which\n         * labels it should account for.\n         * @private\n         * @name Highcharts.Annotation#labelCollector\n         * @type {Function}\n         */\n        /**\n         * The group svg element.\n         *\n         * @name Highcharts.Annotation#group\n         * @type {Highcharts.SVGElement}\n         */\n        /**\n         * The group svg element of the annotation's shapes.\n         *\n         * @name Highcharts.Annotation#shapesGroup\n         * @type {Highcharts.SVGElement}\n         */\n        /**\n         * The group svg element of the annotation's labels.\n         *\n         * @name Highcharts.Annotation#labelsGroup\n         * @type {Highcharts.SVGElement}\n         */\n        this.init(chart, this.options);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    addClipPaths() {\n        this.setClipAxes();\n        if (this.clipXAxis &&\n            this.clipYAxis &&\n            this.options.crop // #15399\n        ) {\n            this.clipRect = this.chart.renderer.clipRect(this.getClipBox());\n        }\n    }\n    /**\n     * @private\n     */\n    addLabels() {\n        const labelsOptions = (this.options.labels || []);\n        labelsOptions.forEach((labelOptions, i) => {\n            const label = this.initLabel(labelOptions, i);\n            Annotation_merge(true, labelsOptions[i], label.options);\n        });\n    }\n    /**\n     * @private\n     */\n    addShapes() {\n        const shapes = this.options.shapes || [];\n        shapes.forEach((shapeOptions, i) => {\n            const shape = this.initShape(shapeOptions, i);\n            Annotation_merge(true, shapes[i], shape.options);\n        });\n    }\n    /**\n     * Destroy the annotation. This function does not touch the chart\n     * that the annotation belongs to (all annotations are kept in\n     * the chart.annotations array) - it is recommended to use\n     * {@link Highcharts.Chart#removeAnnotation} instead.\n     * @private\n     */\n    destroy() {\n        const chart = this.chart, destroyItem = function (item) {\n            item.destroy();\n        };\n        this.labels.forEach(destroyItem);\n        this.shapes.forEach(destroyItem);\n        this.clipXAxis = null;\n        this.clipYAxis = null;\n        Annotation_erase(chart.labelCollectors, this.labelCollector);\n        super.destroy();\n        this.destroyControlTarget();\n        destroyObjectProperties(this, chart);\n    }\n    /**\n     * Destroy a single item.\n     * @private\n     */\n    destroyItem(item) {\n        // Erase from shapes or labels array\n        Annotation_erase(this[item.itemType + 's'], item);\n        item.destroy();\n    }\n    /**\n     * @private\n     */\n    getClipBox() {\n        if (this.clipXAxis && this.clipYAxis) {\n            return {\n                x: this.clipXAxis.left,\n                y: this.clipYAxis.top,\n                width: this.clipXAxis.width,\n                height: this.clipYAxis.height\n            };\n        }\n    }\n    /**\n     * Initialize the annotation properties.\n     * @private\n     */\n    initProperties(chart, userOptions) {\n        this.setOptions(userOptions);\n        const labelsAndShapes = getLabelsAndShapesOptions(this.options, userOptions);\n        this.options.labels = labelsAndShapes.labels;\n        this.options.shapes = labelsAndShapes.shapes;\n        this.chart = chart;\n        this.points = [];\n        this.controlPoints = [];\n        this.coll = 'annotations';\n        this.userOptions = userOptions;\n        this.labels = [];\n        this.shapes = [];\n    }\n    /**\n     * Initialize the annotation.\n     * @private\n     */\n    init(_annotationOrChart, _userOptions, index = this.index) {\n        const chart = this.chart, animOptions = this.options.animation;\n        this.index = index;\n        this.linkPoints();\n        this.addControlPoints();\n        this.addShapes();\n        this.addLabels();\n        this.setLabelCollector();\n        this.animationConfig = getDeferredAnimation(chart, animOptions);\n    }\n    /**\n     * Initialisation of a single label\n     * @private\n     */\n    initLabel(labelOptions, index) {\n        const options = Annotation_merge(this.options.labelOptions, {\n            controlPointOptions: this.options.controlPointOptions\n        }, labelOptions), label = new Controllables_ControllableLabel(this, options, index);\n        label.itemType = 'label';\n        this.labels.push(label);\n        return label;\n    }\n    /**\n     * Initialisation of a single shape\n     * @private\n     * @param {Object} shapeOptions\n     * a config object for a single shape\n     * @param {number} index\n     * annotation may have many shapes, this is the shape's index saved in\n     * shapes.index.\n     */\n    initShape(shapeOptions, index) {\n        const options = Annotation_merge(this.options.shapeOptions, {\n            controlPointOptions: this.options.controlPointOptions\n        }, shapeOptions), shape = new (Annotation.shapesMap[options.type])(this, options, index);\n        shape.itemType = 'shape';\n        this.shapes.push(shape);\n        return shape;\n    }\n    /**\n     * @private\n     */\n    redraw(animation) {\n        this.linkPoints();\n        if (!this.graphic) {\n            this.render();\n        }\n        if (this.clipRect) {\n            this.clipRect.animate(this.getClipBox());\n        }\n        this.redrawItems(this.shapes, animation);\n        this.redrawItems(this.labels, animation);\n        this.redrawControlPoints(animation);\n    }\n    /**\n     * Redraw a single item.\n     * @private\n     */\n    redrawItem(item, animation) {\n        item.linkPoints();\n        if (!item.shouldBeDrawn()) {\n            this.destroyItem(item);\n        }\n        else {\n            if (!item.graphic) {\n                this.renderItem(item);\n            }\n            item.redraw(Annotation_pick(animation, true) && item.graphic.placed);\n            if (item.points.length) {\n                adjustVisibility(item);\n            }\n        }\n    }\n    /**\n     * @private\n     */\n    redrawItems(items, animation) {\n        let i = items.length;\n        // Needs a backward loop. Labels/shapes array might be modified due to\n        // destruction of the item\n        while (i--) {\n            this.redrawItem(items[i], animation);\n        }\n    }\n    /**\n     * See {@link Highcharts.Chart#removeAnnotation}.\n     * @private\n     */\n    remove() {\n        // Let chart.update() remove annotations on demand\n        return this.chart.removeAnnotation(this);\n    }\n    /**\n     * @private\n     */\n    render() {\n        const renderer = this.chart.renderer;\n        this.graphic = renderer\n            .g('annotation')\n            .attr({\n            opacity: 0,\n            zIndex: this.options.zIndex,\n            visibility: this.options.visible ?\n                'inherit' :\n                'hidden'\n        })\n            .add();\n        this.shapesGroup = renderer\n            .g('annotation-shapes')\n            .add(this.graphic);\n        if (this.options.crop) { // #15399\n            this.shapesGroup.clip(this.chart.plotBoxClip);\n        }\n        this.labelsGroup = renderer\n            .g('annotation-labels')\n            .attr({\n            // `hideOverlappingLabels` requires translation\n            translateX: 0,\n            translateY: 0\n        })\n            .add(this.graphic);\n        this.addClipPaths();\n        if (this.clipRect) {\n            this.graphic.clip(this.clipRect);\n        }\n        // Render shapes and labels before adding events (#13070).\n        this.renderItems(this.shapes);\n        this.renderItems(this.labels);\n        this.addEvents();\n        this.renderControlPoints();\n    }\n    /**\n     * @private\n     */\n    renderItem(item) {\n        item.render(item.itemType === 'label' ?\n            this.labelsGroup :\n            this.shapesGroup);\n    }\n    /**\n     * @private\n     */\n    renderItems(items) {\n        let i = items.length;\n        while (i--) {\n            this.renderItem(items[i]);\n        }\n    }\n    /**\n     * @private\n     */\n    setClipAxes() {\n        const xAxes = this.chart.xAxis, yAxes = this.chart.yAxis, linkedAxes = (this.options.labels || [])\n            .concat(this.options.shapes || [])\n            .reduce((axes, labelOrShape) => {\n            const point = labelOrShape &&\n                (labelOrShape.point ||\n                    (labelOrShape.points && labelOrShape.points[0]));\n            return [\n                xAxes[point && point.xAxis] || axes[0],\n                yAxes[point && point.yAxis] || axes[1]\n            ];\n        }, []);\n        this.clipXAxis = linkedAxes[0];\n        this.clipYAxis = linkedAxes[1];\n    }\n    /**\n     * @private\n     */\n    setControlPointsVisibility(visible) {\n        const setItemControlPointsVisibility = function (item) {\n            item.setControlPointsVisibility(visible);\n        };\n        this.controlPoints.forEach((controlPoint) => {\n            controlPoint.setVisibility(visible);\n        });\n        this.shapes.forEach(setItemControlPointsVisibility);\n        this.labels.forEach(setItemControlPointsVisibility);\n    }\n    /**\n     * @private\n     */\n    setLabelCollector() {\n        const annotation = this;\n        annotation.labelCollector = function () {\n            return annotation.labels.reduce(function (labels, label) {\n                if (!label.options.allowOverlap) {\n                    labels.push(label.graphic);\n                }\n                return labels;\n            }, []);\n        };\n        annotation.chart.labelCollectors.push(annotation.labelCollector);\n    }\n    /**\n     * Set an annotation options.\n     * @private\n     * @param {Highcharts.AnnotationsOptions} userOptions\n     *        User options for an annotation\n     */\n    setOptions(userOptions) {\n        this.options = Annotation_merge(\n        // Shared for all annotation types\n        this.defaultOptions, \n        // The static typeOptions from the class\n        (userOptions.type &&\n            this.defaultOptions.types[userOptions.type]) || {}, userOptions);\n    }\n    /**\n     * Set the annotation's visibility.\n     * @private\n     * @param {boolean} [visible]\n     * Whether to show or hide an annotation. If the param is omitted, the\n     * annotation's visibility is toggled.\n     */\n    setVisibility(visible) {\n        const options = this.options, navigation = this.chart.navigationBindings, visibility = Annotation_pick(visible, !options.visible);\n        this.graphic.attr('visibility', visibility ? 'inherit' : 'hidden');\n        if (!visibility) {\n            const setItemControlPointsVisibility = function (item) {\n                item.setControlPointsVisibility(visibility);\n            };\n            this.shapes.forEach(setItemControlPointsVisibility);\n            this.labels.forEach(setItemControlPointsVisibility);\n            if (navigation.activeAnnotation === this &&\n                navigation.popup &&\n                navigation.popup.type === 'annotation-toolbar') {\n                Annotation_fireEvent(navigation, 'closePopup');\n            }\n        }\n        options.visible = visibility;\n    }\n    /**\n     * Updates an annotation.\n     *\n     * @function Highcharts.Annotation#update\n     *\n     * @param {Partial<Highcharts.AnnotationsOptions>} userOptions\n     *        New user options for the annotation.\n     *\n     */\n    update(userOptions, redraw) {\n        const chart = this.chart, labelsAndShapes = getLabelsAndShapesOptions(this.userOptions, userOptions), userOptionsIndex = chart.annotations.indexOf(this), options = Annotation_merge(true, this.userOptions, userOptions);\n        options.labels = labelsAndShapes.labels;\n        options.shapes = labelsAndShapes.shapes;\n        this.destroy();\n        this.initProperties(chart, options);\n        this.init(chart, options);\n        // Update options in chart options, used in exporting (#9767, #21507):\n        chart.options.annotations[userOptionsIndex] = this.options;\n        this.isUpdating = true;\n        if (Annotation_pick(redraw, true)) {\n            chart.drawAnnotations();\n        }\n        Annotation_fireEvent(this, 'afterUpdate');\n        this.isUpdating = false;\n    }\n}\n/**\n * @private\n */\nAnnotation.ControlPoint = Annotations_ControlPoint;\n/**\n * @private\n */\nAnnotation.MockPoint = Annotations_MockPoint;\n/**\n * An object uses for mapping between a shape type and a constructor.\n * To add a new shape type extend this object with type name as a key\n * and a constructor as its value.\n *\n * @private\n */\nAnnotation.shapesMap = {\n    'rect': Controllables_ControllableRect,\n    'circle': Controllables_ControllableCircle,\n    'ellipse': Controllables_ControllableEllipse,\n    'path': Controllables_ControllablePath,\n    'image': Controllables_ControllableImage\n};\n/**\n * @private\n */\nAnnotation.types = {};\nAnnotation.prototype.defaultOptions = Annotations_AnnotationDefaults;\ndefaultOptions.annotations = Annotations_AnnotationDefaults;\n/**\n * List of events for `annotation.options.events` that should not be\n * added to `annotation.graphic` but to the `annotation`.\n *\n * @private\n * @type {Array<string>}\n */\nAnnotation.prototype.nonDOMEvents = ['add', 'afterUpdate', 'drag', 'remove'];\nAnnotations_ControlTarget.compose(Annotation);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Annotations_Annotation = (Annotation);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Possible directions for draggable annotations. An empty string (`''`)\n * makes the annotation undraggable.\n *\n * @typedef {''|'x'|'xy'|'y'} Highcharts.AnnotationDraggableValue\n * @requires modules/annotations\n */\n/**\n * @private\n * @typedef {\n *          Highcharts.AnnotationControllableCircle|\n *          Highcharts.AnnotationControllableImage|\n *          Highcharts.AnnotationControllablePath|\n *          Highcharts.AnnotationControllableRect\n *     } Highcharts.AnnotationShapeType\n * @requires modules/annotations\n */\n/**\n * @private\n * @typedef {\n *          Highcharts.AnnotationControllableLabel\n *     } Highcharts.AnnotationLabelType\n * @requires modules/annotations\n */\n/**\n * A point-like object, a mock point or a point used in series.\n * @private\n * @typedef {\n *          Highcharts.AnnotationMockPoint|\n *          Highcharts.Point\n *     } Highcharts.AnnotationPointType\n * @requires modules/annotations\n */\n/**\n * Shape point as string, object or function.\n *\n * @typedef {\n *          string|\n *          Highcharts.AnnotationMockPointOptionsObject|\n *          Highcharts.AnnotationMockPointFunction\n *     } Highcharts.AnnotationShapePointOptions\n */\n(''); // Keeps doclets above in JS file\n\n;// ./code/es-modules/Core/Chart/ChartNavigationComposition.js\n/**\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Composition\n *\n * */\nvar ChartNavigationComposition;\n(function (ChartNavigationComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * @private\n     */\n    function compose(chart) {\n        if (!chart.navigation) {\n            chart.navigation = new Additions(chart);\n        }\n        return chart;\n    }\n    ChartNavigationComposition.compose = compose;\n    /* *\n     *\n     *  Class\n     *\n     * */\n    /**\n     * Initializes `chart.navigation` object which delegates `update()` methods\n     * to all other common classes (used in exporting and navigationBindings).\n     * @private\n     */\n    class Additions {\n        /* *\n         *\n         *  Constructor\n         *\n         * */\n        constructor(chart) {\n            this.updates = [];\n            this.chart = chart;\n        }\n        /* *\n         *\n         *  Functions\n         *\n         * */\n        /**\n         * Registers an `update()` method in the `chart.navigation` object.\n         *\n         * @private\n         * @param {UpdateFunction} updateFn\n         * The `update()` method that will be called in `chart.update()`.\n         */\n        addUpdate(updateFn) {\n            this.chart.navigation.updates.push(updateFn);\n        }\n        /**\n         * @private\n         */\n        update(options, redraw) {\n            this.updates.forEach((updateFn) => {\n                updateFn.call(this.chart, options, redraw);\n            });\n        }\n    }\n    ChartNavigationComposition.Additions = Additions;\n})(ChartNavigationComposition || (ChartNavigationComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Chart_ChartNavigationComposition = (ChartNavigationComposition);\n\n;// ./code/es-modules/Extensions/Annotations/NavigationBindingsUtilities.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { defined: NavigationBindingsUtilities_defined, isNumber: NavigationBindingsUtilities_isNumber, pick: NavigationBindingsUtilities_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\n/**\n * Define types for editable fields per annotation. There is no need to define\n * numbers, because they won't change their type to string.\n * @private\n */\nconst annotationsFieldsTypes = {\n    backgroundColor: 'string',\n    borderColor: 'string',\n    borderRadius: 'string',\n    color: 'string',\n    fill: 'string',\n    fontSize: 'string',\n    labels: 'string',\n    name: 'string',\n    stroke: 'string',\n    title: 'string'\n};\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Returns the first xAxis or yAxis that was clicked with its value.\n *\n * @private\n *\n * @param {Array<Highcharts.PointerAxisCoordinateObject>} coords\n *        All the chart's x or y axes with a current pointer's axis value.\n *\n * @return {Highcharts.PointerAxisCoordinateObject}\n *         Object with a first found axis and its value that pointer\n *         is currently pointing.\n */\nfunction getAssignedAxis(coords) {\n    return coords.filter((coord) => {\n        const extremes = coord.axis.getExtremes(), axisMin = extremes.min, axisMax = extremes.max, \n        // Correct axis edges when axis has series\n        // with pointRange (like column)\n        minPointOffset = NavigationBindingsUtilities_pick(coord.axis.minPointOffset, 0);\n        return NavigationBindingsUtilities_isNumber(axisMin) && NavigationBindingsUtilities_isNumber(axisMax) &&\n            coord.value >= (axisMin - minPointOffset) &&\n            coord.value <= (axisMax + minPointOffset) &&\n            // Don't count navigator axis\n            !coord.axis.options.isInternal;\n    })[0]; // If the axes overlap, return the first axis that was found.\n}\n/**\n * Get field type according to value\n *\n * @private\n *\n * @param {'boolean'|'number'|'string'} value\n * Atomic type (one of: string, number, boolean)\n *\n * @return {'checkbox'|'number'|'text'}\n * Field type (one of: text, number, checkbox)\n */\nfunction getFieldType(key, value) {\n    const predefinedType = annotationsFieldsTypes[key];\n    let fieldType = typeof value;\n    if (NavigationBindingsUtilities_defined(predefinedType)) {\n        fieldType = predefinedType;\n    }\n    return {\n        'string': 'text',\n        'number': 'number',\n        'boolean': 'checkbox'\n    }[fieldType];\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst NavigationBindingUtilities = {\n    annotationsFieldsTypes,\n    getAssignedAxis,\n    getFieldType\n};\n/* harmony default export */ const NavigationBindingsUtilities = (NavigationBindingUtilities);\n\n;// ./code/es-modules/Extensions/Annotations/NavigationBindingsDefaults.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { getAssignedAxis: NavigationBindingsDefaults_getAssignedAxis } = NavigationBindingsUtilities;\n\nconst { isNumber: NavigationBindingsDefaults_isNumber, merge: NavigationBindingsDefaults_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\n/**\n * @optionparent lang\n */\nconst lang = {\n    /**\n     * Configure the Popup strings in the chart. Requires the\n     * `annotations.js` or `annotations-advanced.js` module to be\n     * loaded.\n     * @since   7.0.0\n     * @product highcharts highstock\n     */\n    navigation: {\n        /**\n         * Translations for all field names used in popup.\n         *\n         * @product highcharts highstock\n         */\n        popup: {\n            simpleShapes: 'Simple shapes',\n            lines: 'Lines',\n            circle: 'Circle',\n            ellipse: 'Ellipse',\n            rectangle: 'Rectangle',\n            label: 'Label',\n            shapeOptions: 'Shape options',\n            typeOptions: 'Details',\n            fill: 'Fill',\n            format: 'Text',\n            strokeWidth: 'Line width',\n            stroke: 'Line color',\n            title: 'Title',\n            name: 'Name',\n            labelOptions: 'Label options',\n            labels: 'Labels',\n            backgroundColor: 'Background color',\n            backgroundColors: 'Background colors',\n            borderColor: 'Border color',\n            borderRadius: 'Border radius',\n            borderWidth: 'Border width',\n            style: 'Style',\n            padding: 'Padding',\n            fontSize: 'Font size',\n            color: 'Color',\n            height: 'Height',\n            shapes: 'Shape options'\n        }\n    }\n};\n/**\n * @optionparent navigation\n * @product      highcharts highstock\n */\nconst navigation = {\n    /**\n     * A CSS class name where all bindings will be attached to. Multiple\n     * charts on the same page should have separate class names to prevent\n     * duplicating events.\n     *\n     * Default value of versions < 7.0.4 `highcharts-bindings-wrapper`\n     *\n     * @since     7.0.0\n     * @type      {string}\n     */\n    bindingsClassName: 'highcharts-bindings-container',\n    /**\n     * Bindings definitions for custom HTML buttons. Each binding implements\n     * simple event-driven interface:\n     *\n     * - `className`: classname used to bind event to\n     *\n     * - `init`: initial event, fired on button click\n     *\n     * - `start`: fired on first click on a chart\n     *\n     * - `steps`: array of sequential events fired one after another on each\n     *   of users clicks\n     *\n     * - `end`: last event to be called after last step event\n     *\n     * @type         {Highcharts.Dictionary<Highcharts.NavigationBindingsOptionsObject>|*}\n     *\n     * @sample {highstock} stock/stocktools/stocktools-thresholds\n     *               Custom bindings\n     * @sample {highcharts} highcharts/annotations/bindings/\n     *               Simple binding\n     * @sample {highcharts} highcharts/annotations/bindings-custom-annotation/\n     *               Custom annotation binding\n     *\n     * @since        7.0.0\n     * @requires     modules/annotations\n     * @product      highcharts highstock\n     */\n    bindings: {\n        /**\n         * A circle annotation bindings. Includes `start` and one event in\n         * `steps` array.\n         *\n         * @type    {Highcharts.NavigationBindingsOptionsObject}\n         * @default {\"className\": \"highcharts-circle-annotation\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n         */\n        circleAnnotation: {\n            /** @ignore-option */\n            className: 'highcharts-circle-annotation',\n            /** @ignore-option */\n            start: function (e) {\n                const coords = this.chart.pointer?.getCoordinates(e), coordsX = coords && NavigationBindingsDefaults_getAssignedAxis(coords.xAxis), coordsY = coords && NavigationBindingsDefaults_getAssignedAxis(coords.yAxis), navigation = this.chart.options.navigation;\n                // Exit if clicked out of axes area\n                if (!coordsX || !coordsY) {\n                    return;\n                }\n                return this.chart.addAnnotation(NavigationBindingsDefaults_merge({\n                    langKey: 'circle',\n                    type: 'basicAnnotation',\n                    shapes: [{\n                            type: 'circle',\n                            point: {\n                                x: coordsX.value,\n                                y: coordsY.value,\n                                xAxis: coordsX.axis.index,\n                                yAxis: coordsY.axis.index\n                            },\n                            r: 5\n                        }]\n                }, navigation.annotationsOptions, navigation.bindings.circleAnnotation\n                    .annotationsOptions));\n            },\n            /** @ignore-option */\n            steps: [\n                function (e, annotation) {\n                    const shapes = annotation.options.shapes, mockPointOpts = ((shapes && shapes[0] && shapes[0].point) ||\n                        {});\n                    let distance;\n                    if (NavigationBindingsDefaults_isNumber(mockPointOpts.xAxis) &&\n                        NavigationBindingsDefaults_isNumber(mockPointOpts.yAxis)) {\n                        const inverted = this.chart.inverted, x = this.chart.xAxis[mockPointOpts.xAxis]\n                            .toPixels(mockPointOpts.x), y = this.chart.yAxis[mockPointOpts.yAxis]\n                            .toPixels(mockPointOpts.y);\n                        distance = Math.max(Math.sqrt(Math.pow(inverted ? y - e.chartX : x - e.chartX, 2) +\n                            Math.pow(inverted ? x - e.chartY : y - e.chartY, 2)), 5);\n                    }\n                    annotation.update({\n                        shapes: [{\n                                r: distance\n                            }]\n                    });\n                }\n            ]\n        },\n        /**\n         * A ellipse annotation bindings. Includes `start` and two events in\n         * `steps` array. First updates the second point, responsible for a\n         * rx width, and second updates the ry width.\n         *\n         * @type    {Highcharts.NavigationBindingsOptionsObject}\n         * @default {\"className\": \"highcharts-ellipse-annotation\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n         */\n        ellipseAnnotation: {\n            className: 'highcharts-ellipse-annotation',\n            start: function (e) {\n                const coords = this.chart.pointer?.getCoordinates(e), coordsX = coords && NavigationBindingsDefaults_getAssignedAxis(coords.xAxis), coordsY = coords && NavigationBindingsDefaults_getAssignedAxis(coords.yAxis), navigation = this.chart.options.navigation;\n                if (!coordsX || !coordsY) {\n                    return;\n                }\n                return this.chart.addAnnotation(NavigationBindingsDefaults_merge({\n                    langKey: 'ellipse',\n                    type: 'basicAnnotation',\n                    shapes: [\n                        {\n                            type: 'ellipse',\n                            xAxis: coordsX.axis.index,\n                            yAxis: coordsY.axis.index,\n                            points: [{\n                                    x: coordsX.value,\n                                    y: coordsY.value\n                                }, {\n                                    x: coordsX.value,\n                                    y: coordsY.value\n                                }],\n                            ry: 1\n                        }\n                    ]\n                }, navigation.annotationsOptions, navigation.bindings.ellipseAnnotation\n                    .annotationsOptions));\n            },\n            steps: [\n                function (e, annotation) {\n                    const target = annotation.shapes[0], position = target.getAbsolutePosition(target.points[1]);\n                    target.translatePoint(e.chartX - position.x, e.chartY - position.y, 1);\n                    target.redraw(false);\n                },\n                function (e, annotation) {\n                    const target = annotation.shapes[0], position = target.getAbsolutePosition(target.points[0]), position2 = target.getAbsolutePosition(target.points[1]), newR = target.getDistanceFromLine(position, position2, e.chartX, e.chartY), yAxis = target.getYAxis(), newRY = Math.abs(yAxis.toValue(0) - yAxis.toValue(newR));\n                    target.setYRadius(newRY);\n                    target.redraw(false);\n                }\n            ]\n        },\n        /**\n         * A rectangle annotation bindings. Includes `start` and one event\n         * in `steps` array.\n         *\n         * @type    {Highcharts.NavigationBindingsOptionsObject}\n         * @default {\"className\": \"highcharts-rectangle-annotation\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n         */\n        rectangleAnnotation: {\n            /** @ignore-option */\n            className: 'highcharts-rectangle-annotation',\n            /** @ignore-option */\n            start: function (e) {\n                const coords = this.chart.pointer?.getCoordinates(e), coordsX = coords && NavigationBindingsDefaults_getAssignedAxis(coords.xAxis), coordsY = coords && NavigationBindingsDefaults_getAssignedAxis(coords.yAxis);\n                // Exit if clicked out of axes area\n                if (!coordsX || !coordsY) {\n                    return;\n                }\n                const x = coordsX.value, y = coordsY.value, xAxis = coordsX.axis.index, yAxis = coordsY.axis.index, navigation = this.chart.options.navigation;\n                return this.chart.addAnnotation(NavigationBindingsDefaults_merge({\n                    langKey: 'rectangle',\n                    type: 'basicAnnotation',\n                    shapes: [{\n                            type: 'path',\n                            points: [\n                                { xAxis, yAxis, x, y },\n                                { xAxis, yAxis, x, y },\n                                { xAxis, yAxis, x, y },\n                                { xAxis, yAxis, x, y },\n                                { command: 'Z' }\n                            ]\n                        }]\n                }, navigation\n                    .annotationsOptions, navigation\n                    .bindings\n                    .rectangleAnnotation\n                    .annotationsOptions));\n            },\n            /** @ignore-option */\n            steps: [\n                function (e, annotation) {\n                    const shapes = annotation.options.shapes, points = ((shapes && shapes[0] && shapes[0].points) ||\n                        []), coords = this.chart.pointer?.getCoordinates(e), coordsX = coords && NavigationBindingsDefaults_getAssignedAxis(coords.xAxis), coordsY = coords && NavigationBindingsDefaults_getAssignedAxis(coords.yAxis);\n                    if (coordsX && coordsY) {\n                        const x = coordsX.value, y = coordsY.value;\n                        // Top right point\n                        points[1].x = x;\n                        // Bottom right point (cursor position)\n                        points[2].x = x;\n                        points[2].y = y;\n                        // Bottom left\n                        points[3].y = y;\n                        annotation.update({\n                            shapes: [{\n                                    points: points\n                                }]\n                        });\n                    }\n                }\n            ]\n        },\n        /**\n         * A label annotation bindings. Includes `start` event only.\n         *\n         * @type    {Highcharts.NavigationBindingsOptionsObject}\n         * @default {\"className\": \"highcharts-label-annotation\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n         */\n        labelAnnotation: {\n            /** @ignore-option */\n            className: 'highcharts-label-annotation',\n            /** @ignore-option */\n            start: function (e) {\n                const coords = this.chart.pointer?.getCoordinates(e), coordsX = coords && NavigationBindingsDefaults_getAssignedAxis(coords.xAxis), coordsY = coords && NavigationBindingsDefaults_getAssignedAxis(coords.yAxis), navigation = this.chart.options.navigation;\n                // Exit if clicked out of axes area\n                if (!coordsX || !coordsY) {\n                    return;\n                }\n                return this.chart.addAnnotation(NavigationBindingsDefaults_merge({\n                    langKey: 'label',\n                    type: 'basicAnnotation',\n                    labelOptions: {\n                        format: '{y:.2f}',\n                        overflow: 'none',\n                        crop: true\n                    },\n                    labels: [{\n                            point: {\n                                xAxis: coordsX.axis.index,\n                                yAxis: coordsY.axis.index,\n                                x: coordsX.value,\n                                y: coordsY.value\n                            }\n                        }]\n                }, navigation\n                    .annotationsOptions, navigation\n                    .bindings\n                    .labelAnnotation\n                    .annotationsOptions));\n            }\n        }\n    },\n    /**\n     * Path where Highcharts will look for icons. Change this to use icons\n     * from a different server.\n     *\n     * @type      {string}\n     * @default   https://code.highcharts.com/12.3.0/gfx/stock-icons/\n     * @since     7.1.3\n     * @apioption navigation.iconsURL\n     */\n    /**\n     * A `showPopup` event. Fired when selecting for example an annotation.\n     *\n     * @type      {Function}\n     * @apioption navigation.events.showPopup\n     */\n    /**\n     * A `closePopup` event. Fired when Popup should be hidden, for example\n     * when clicking on an annotation again.\n     *\n     * @type      {Function}\n     * @apioption navigation.events.closePopup\n     */\n    /**\n     * Event fired on a button click.\n     *\n     * @type      {Function}\n     * @sample    highcharts/annotations/gui/\n     *            Change icon in a dropddown on event\n     * @sample    highcharts/annotations/gui-buttons/\n     *            Change button class on event\n     * @apioption navigation.events.selectButton\n     */\n    /**\n     * Event fired when button state should change, for example after\n     * adding an annotation.\n     *\n     * @type      {Function}\n     * @sample    highcharts/annotations/gui/\n     *            Change icon in a dropddown on event\n     * @sample    highcharts/annotations/gui-buttons/\n     *            Change button class on event\n     * @apioption navigation.events.deselectButton\n     */\n    /**\n     * Events to communicate between Stock Tools and custom GUI.\n     *\n     * @since        7.0.0\n     * @product      highcharts highstock\n     * @optionparent navigation.events\n     */\n    events: {},\n    /**\n     * Additional options to be merged into all annotations.\n     *\n     * @sample stock/stocktools/navigation-annotation-options\n     *         Set red color of all line annotations\n     *\n     * @type      {Highcharts.AnnotationsOptions}\n     * @extends   annotations\n     * @exclude   crookedLine, elliottWave, fibonacci, infinityLine,\n     *            measure, pitchfork, tunnel, verticalLine, basicAnnotation\n     * @requires     modules/annotations\n     * @apioption navigation.annotationsOptions\n     */\n    annotationsOptions: {\n        animation: {\n            defer: 0\n        }\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\nconst NavigationBindingDefaults = {\n    lang,\n    navigation\n};\n/* harmony default export */ const NavigationBindingsDefaults = (NavigationBindingDefaults);\n\n;// ./code/es-modules/Extensions/Annotations/NavigationBindings.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { setOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { format: NavigationBindings_format } = (highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default());\n\nconst { composed: NavigationBindings_composed, doc: NavigationBindings_doc, win } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nconst { getAssignedAxis: NavigationBindings_getAssignedAxis, getFieldType: NavigationBindings_getFieldType } = NavigationBindingsUtilities;\n\nconst { addEvent: NavigationBindings_addEvent, attr, defined: NavigationBindings_defined, fireEvent: NavigationBindings_fireEvent, isArray: NavigationBindings_isArray, isFunction, isNumber: NavigationBindings_isNumber, isObject: NavigationBindings_isObject, merge: NavigationBindings_merge, objectEach: NavigationBindings_objectEach, pick: NavigationBindings_pick, pushUnique: NavigationBindings_pushUnique } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * IE 9-11 polyfill for Element.closest():\n * @private\n */\nfunction closestPolyfill(el, s) {\n    const ElementProto = win.Element.prototype, elementMatches = ElementProto.matches ||\n        ElementProto.msMatchesSelector ||\n        ElementProto.webkitMatchesSelector;\n    let ret = null;\n    if (ElementProto.closest) {\n        ret = ElementProto.closest.call(el, s);\n    }\n    else {\n        do {\n            if (elementMatches.call(el, s)) {\n                return el;\n            }\n            el = el.parentElement || el.parentNode;\n        } while (el !== null && el.nodeType === 1);\n    }\n    return ret;\n}\n/**\n * @private\n */\nfunction onAnnotationRemove() {\n    if (this.chart.navigationBindings) {\n        this.chart.navigationBindings.deselectAnnotation();\n    }\n}\n/**\n * @private\n */\nfunction onChartDestroy() {\n    if (this.navigationBindings) {\n        this.navigationBindings.destroy();\n    }\n}\n/**\n * @private\n */\nfunction onChartLoad() {\n    const options = this.options;\n    if (options && options.navigation && options.navigation.bindings) {\n        this.navigationBindings = new NavigationBindings(this, options.navigation);\n        this.navigationBindings.initEvents();\n        this.navigationBindings.initUpdate();\n    }\n}\n/**\n * @private\n */\nfunction onChartRender() {\n    const navigationBindings = this.navigationBindings, disabledClassName = 'highcharts-disabled-btn';\n    if (this && navigationBindings) {\n        // Check if the buttons should be enabled/disabled based on\n        // visible series.\n        let buttonsEnabled = false;\n        this.series.forEach((series) => {\n            if (!series.options.isInternal && series.visible) {\n                buttonsEnabled = true;\n            }\n        });\n        if (this.navigationBindings &&\n            this.navigationBindings.container &&\n            this.navigationBindings.container[0]) {\n            const container = this.navigationBindings.container[0];\n            NavigationBindings_objectEach(navigationBindings.boundClassNames, (value, key) => {\n                // Get the HTML element corresponding to the className taken\n                // from StockToolsBindings.\n                const buttonNode = container.querySelectorAll('.' + key);\n                if (buttonNode) {\n                    for (let i = 0; i < buttonNode.length; i++) {\n                        const button = buttonNode[i], cls = button.className;\n                        if (value.noDataState === 'normal') {\n                            // If button has noDataState: 'normal', and has\n                            // disabledClassName, remove this className.\n                            if (cls.indexOf(disabledClassName) !== -1) {\n                                button.classList.remove(disabledClassName);\n                            }\n                        }\n                        else if (!buttonsEnabled) {\n                            if (cls.indexOf(disabledClassName) === -1) {\n                                button.className += ' ' + disabledClassName;\n                            }\n                        }\n                        else {\n                            // Enable all buttons by deleting the className.\n                            if (cls.indexOf(disabledClassName) !== -1) {\n                                button.classList.remove(disabledClassName);\n                            }\n                        }\n                    }\n                }\n            });\n        }\n    }\n}\n/**\n * @private\n */\nfunction NavigationBindings_onNavigationBindingsClosePopup() {\n    this.deselectAnnotation();\n}\n/**\n * @private\n */\nfunction onNavigationBindingsDeselectButton() {\n    this.selectedButtonElement = null;\n}\n/**\n * Show edit-annotation form:\n * @private\n */\nfunction selectableAnnotation(annotationType) {\n    const originalClick = annotationType.prototype.defaultOptions.events &&\n        annotationType.prototype.defaultOptions.events.click;\n    /**\n     * Select and show popup\n     * @private\n     */\n    function selectAndShowPopup(eventArguments) {\n        const annotation = this, navigation = annotation.chart.navigationBindings, prevAnnotation = navigation.activeAnnotation;\n        if (originalClick) {\n            originalClick.call(annotation, eventArguments);\n        }\n        if (prevAnnotation !== annotation) {\n            // Select current:\n            navigation.deselectAnnotation();\n            navigation.activeAnnotation = annotation;\n            annotation.setControlPointsVisibility(true);\n            NavigationBindings_fireEvent(navigation, 'showPopup', {\n                annotation: annotation,\n                formType: 'annotation-toolbar',\n                options: navigation.annotationToFields(annotation),\n                onSubmit: function (data) {\n                    if (data.actionType === 'remove') {\n                        navigation.activeAnnotation = false;\n                        navigation.chart.removeAnnotation(annotation);\n                    }\n                    else {\n                        const config = {};\n                        navigation.fieldsToOptions(data.fields, config);\n                        navigation.deselectAnnotation();\n                        const typeOptions = config.typeOptions;\n                        if (annotation.options.type === 'measure') {\n                            // Manually disable crooshars according to\n                            // stroke width of the shape:\n                            typeOptions.crosshairY.enabled = (typeOptions.crosshairY\n                                .strokeWidth !== 0);\n                            typeOptions.crosshairX.enabled = (typeOptions.crosshairX\n                                .strokeWidth !== 0);\n                        }\n                        annotation.update(config);\n                    }\n                }\n            });\n        }\n        else {\n            // Deselect current:\n            NavigationBindings_fireEvent(navigation, 'closePopup');\n        }\n        // Let bubble event to chart.click:\n        eventArguments.activeAnnotation = true;\n    }\n    // #18276, show popup on touchend, but not on touchmove\n    let touchStartX, touchStartY;\n    /**\n     *\n     */\n    function saveCoords(e) {\n        touchStartX = e.touches[0].clientX;\n        touchStartY = e.touches[0].clientY;\n    }\n    /**\n     *\n     */\n    function checkForTouchmove(e) {\n        const hasMoved = touchStartX ? Math.sqrt(Math.pow(touchStartX - e.changedTouches[0].clientX, 2) +\n            Math.pow(touchStartY - e.changedTouches[0].clientY, 2)) >= 4 : false;\n        if (!hasMoved) {\n            selectAndShowPopup.call(this, e);\n        }\n    }\n    NavigationBindings_merge(true, annotationType.prototype.defaultOptions.events, {\n        click: selectAndShowPopup,\n        touchstart: saveCoords,\n        touchend: checkForTouchmove\n    });\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n */\nclass NavigationBindings {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static compose(AnnotationClass, ChartClass) {\n        if (NavigationBindings_pushUnique(NavigationBindings_composed, 'NavigationBindings')) {\n            NavigationBindings_addEvent(AnnotationClass, 'remove', onAnnotationRemove);\n            // Basic shapes:\n            selectableAnnotation(AnnotationClass);\n            // Advanced annotations:\n            NavigationBindings_objectEach(AnnotationClass.types, (annotationType) => {\n                selectableAnnotation(annotationType);\n            });\n            NavigationBindings_addEvent(ChartClass, 'destroy', onChartDestroy);\n            NavigationBindings_addEvent(ChartClass, 'load', onChartLoad);\n            NavigationBindings_addEvent(ChartClass, 'render', onChartRender);\n            NavigationBindings_addEvent(NavigationBindings, 'closePopup', NavigationBindings_onNavigationBindingsClosePopup);\n            NavigationBindings_addEvent(NavigationBindings, 'deselectButton', onNavigationBindingsDeselectButton);\n            setOptions(NavigationBindingsDefaults);\n        }\n    }\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(chart, options) {\n        this.boundClassNames = void 0;\n        this.chart = chart;\n        this.options = options;\n        this.eventsToUnbind = [];\n        this.container =\n            this.chart.container.getElementsByClassName(this.options.bindingsClassName || '');\n        if (!this.container.length) {\n            this.container = NavigationBindings_doc.getElementsByClassName(this.options.bindingsClassName || '');\n        }\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getCoords(e) {\n        const coords = this.chart.pointer?.getCoordinates(e);\n        return [\n            coords && NavigationBindings_getAssignedAxis(coords.xAxis),\n            coords && NavigationBindings_getAssignedAxis(coords.yAxis)\n        ];\n    }\n    /**\n     * Init all events connected to NavigationBindings.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#initEvents\n     */\n    initEvents() {\n        const navigation = this, chart = navigation.chart, bindingsContainer = navigation.container, options = navigation.options;\n        // Shorthand object for getting events for buttons:\n        navigation.boundClassNames = {};\n        NavigationBindings_objectEach((options.bindings || {}), (value) => {\n            navigation.boundClassNames[value.className] = value;\n        });\n        // Handle multiple containers with the same class names:\n        [].forEach.call(bindingsContainer, (subContainer) => {\n            navigation.eventsToUnbind.push(NavigationBindings_addEvent(subContainer, 'click', (event) => {\n                const bindings = navigation.getButtonEvents(subContainer, event);\n                if (bindings &&\n                    (!bindings.button.classList\n                        .contains('highcharts-disabled-btn'))) {\n                    navigation.bindingsButtonClick(bindings.button, bindings.events, event);\n                }\n            }));\n        });\n        NavigationBindings_objectEach((options.events || {}), (callback, eventName) => {\n            if (isFunction(callback)) {\n                navigation.eventsToUnbind.push(NavigationBindings_addEvent(navigation, eventName, callback, { passive: false }));\n            }\n        });\n        navigation.eventsToUnbind.push(NavigationBindings_addEvent(chart.container, 'click', function (e) {\n            if (!chart.cancelClick &&\n                chart.isInsidePlot(e.chartX - chart.plotLeft, e.chartY - chart.plotTop, {\n                    visiblePlotOnly: true\n                })) {\n                navigation.bindingsChartClick(this, e);\n            }\n        }));\n        navigation.eventsToUnbind.push(NavigationBindings_addEvent(chart.container, (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isTouchDevice ? 'touchmove' : 'mousemove', function (e) {\n            navigation.bindingsContainerMouseMove(this, e);\n        }, (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isTouchDevice ? { passive: false } : void 0));\n    }\n    /**\n     * Common chart.update() delegation, shared between bindings and exporting.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#initUpdate\n     */\n    initUpdate() {\n        const navigation = this;\n        Chart_ChartNavigationComposition\n            .compose(this.chart).navigation\n            .addUpdate((options) => {\n            navigation.update(options);\n        });\n    }\n    /**\n     * Hook for click on a button, method selects/unselects buttons,\n     * then calls `bindings.init` callback.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#bindingsButtonClick\n     *\n     * @param {Highcharts.HTMLDOMElement} [button]\n     *        Clicked button\n     *\n     * @param {Object} events\n     *        Events passed down from bindings (`init`, `start`, `step`, `end`)\n     *\n     * @param {Highcharts.PointerEventObject} clickEvent\n     *        Browser's click event\n     */\n    bindingsButtonClick(button, events, clickEvent) {\n        const navigation = this, chart = navigation.chart, svgContainer = chart.renderer.boxWrapper;\n        let shouldEventBeFired = true;\n        if (navigation.selectedButtonElement) {\n            if (navigation.selectedButtonElement.classList === button.classList) {\n                shouldEventBeFired = false;\n            }\n            NavigationBindings_fireEvent(navigation, 'deselectButton', { button: navigation.selectedButtonElement });\n            if (navigation.nextEvent) {\n                // Remove in-progress annotations adders:\n                if (navigation.currentUserDetails &&\n                    navigation.currentUserDetails.coll === 'annotations') {\n                    chart.removeAnnotation(navigation.currentUserDetails);\n                }\n                navigation.mouseMoveEvent = navigation.nextEvent = false;\n            }\n        }\n        if (shouldEventBeFired) {\n            navigation.selectedButton = events;\n            navigation.selectedButtonElement = button;\n            NavigationBindings_fireEvent(navigation, 'selectButton', { button: button });\n            // Call \"init\" event, for example to open modal window\n            if (events.init) {\n                events.init.call(navigation, button, clickEvent);\n            }\n            if (events.start || events.steps) {\n                chart.renderer.boxWrapper.addClass('highcharts-draw-mode');\n            }\n        }\n        else {\n            chart.stockTools && button.classList.remove('highcharts-active');\n            svgContainer.removeClass('highcharts-draw-mode');\n            navigation.nextEvent = false;\n            navigation.mouseMoveEvent = false;\n            navigation.selectedButton = null;\n        }\n    }\n    /**\n     * Hook for click on a chart, first click on a chart calls `start` event,\n     * then on all subsequent clicks iterate over `steps` array.\n     * When finished, calls `end` event.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#bindingsChartClick\n     *\n     * @param {Highcharts.Chart} chart\n     *        Chart that click was performed on.\n     *\n     * @param {Highcharts.PointerEventObject} clickEvent\n     *        Browser's click event.\n     */\n    bindingsChartClick(chart, clickEvent) {\n        chart = this.chart;\n        const navigation = this, activeAnnotation = navigation.activeAnnotation, selectedButton = navigation.selectedButton, svgContainer = chart.renderer.boxWrapper;\n        if (activeAnnotation) {\n            // Click outside popups, should close them and deselect the\n            // annotation\n            if (!activeAnnotation.cancelClick && // #15729\n                !clickEvent.activeAnnotation &&\n                // Element could be removed in the child action, e.g. button\n                clickEvent.target.parentNode &&\n                // TO DO: Polyfill for IE11?\n                !closestPolyfill(clickEvent.target, '.highcharts-popup')) {\n                NavigationBindings_fireEvent(navigation, 'closePopup');\n            }\n            else if (activeAnnotation.cancelClick) {\n                // Reset cancelClick after the other event handlers have run\n                setTimeout(() => {\n                    activeAnnotation.cancelClick = false;\n                }, 0);\n            }\n        }\n        if (!selectedButton || !selectedButton.start) {\n            return;\n        }\n        if (!navigation.nextEvent) {\n            // Call init method:\n            navigation.currentUserDetails = selectedButton.start.call(navigation, clickEvent);\n            // If steps exists (e.g. Annotations), bind them:\n            if (navigation.currentUserDetails && selectedButton.steps) {\n                navigation.stepIndex = 0;\n                navigation.steps = true;\n                navigation.mouseMoveEvent = navigation.nextEvent =\n                    selectedButton.steps[navigation.stepIndex];\n            }\n            else {\n                NavigationBindings_fireEvent(navigation, 'deselectButton', { button: navigation.selectedButtonElement });\n                svgContainer.removeClass('highcharts-draw-mode');\n                navigation.steps = false;\n                navigation.selectedButton = null;\n                // First click is also the last one:\n                if (selectedButton.end) {\n                    selectedButton.end.call(navigation, clickEvent, navigation.currentUserDetails);\n                }\n            }\n        }\n        else {\n            navigation.nextEvent(clickEvent, navigation.currentUserDetails);\n            if (navigation.steps) {\n                navigation.stepIndex++;\n                if (selectedButton.steps[navigation.stepIndex]) {\n                    // If we have more steps, bind them one by one:\n                    navigation.mouseMoveEvent = navigation.nextEvent = selectedButton.steps[navigation.stepIndex];\n                }\n                else {\n                    NavigationBindings_fireEvent(navigation, 'deselectButton', { button: navigation.selectedButtonElement });\n                    svgContainer.removeClass('highcharts-draw-mode');\n                    // That was the last step, call end():\n                    if (selectedButton.end) {\n                        selectedButton.end.call(navigation, clickEvent, navigation.currentUserDetails);\n                    }\n                    navigation.nextEvent = false;\n                    navigation.mouseMoveEvent = false;\n                    navigation.selectedButton = null;\n                }\n            }\n        }\n    }\n    /**\n     * Hook for mouse move on a chart's container. It calls current step.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#bindingsContainerMouseMove\n     *\n     * @param {Highcharts.HTMLDOMElement} container\n     *        Chart's container.\n     *\n     * @param {global.Event} moveEvent\n     *        Browser's move event.\n     */\n    bindingsContainerMouseMove(_container, moveEvent) {\n        if (this.mouseMoveEvent) {\n            this.mouseMoveEvent(moveEvent, this.currentUserDetails);\n        }\n    }\n    /**\n     * Translate fields (e.g. `params.period` or `marker.styles.color`) to\n     * Highcharts options object (e.g. `{ params: { period } }`).\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#fieldsToOptions<T>\n     *\n     * @param {Highcharts.Dictionary<string>} fields\n     *        Fields from popup form.\n     *\n     * @param {T} config\n     *        Default config to be modified.\n     *\n     * @return {T}\n     *         Modified config\n     */\n    fieldsToOptions(fields, config) {\n        NavigationBindings_objectEach(fields, (value, field) => {\n            const parsedValue = parseFloat(value), path = field.split('.'), pathLength = path.length - 1;\n            // If it's a number (not \"format\" options), parse it:\n            if (NavigationBindings_isNumber(parsedValue) &&\n                !value.match(/px|em/g) &&\n                !field.match(/format/g)) {\n                value = parsedValue;\n            }\n            // Remove values like 0\n            if (value !== 'undefined') {\n                let parent = config;\n                path.forEach((name, index) => {\n                    if (name !== '__proto__' && name !== 'constructor') {\n                        const nextName = NavigationBindings_pick(path[index + 1], '');\n                        if (pathLength === index) {\n                            // Last index, put value:\n                            parent[name] = value;\n                        }\n                        else if (!parent[name]) {\n                            // Create middle property:\n                            parent[name] = nextName.match(/\\d/g) ?\n                                [] :\n                                {};\n                            parent = parent[name];\n                        }\n                        else {\n                            // Jump into next property\n                            parent = parent[name];\n                        }\n                    }\n                });\n            }\n        });\n        return config;\n    }\n    /**\n     * Shorthand method to deselect an annotation.\n     *\n     * @function Highcharts.NavigationBindings#deselectAnnotation\n     */\n    deselectAnnotation() {\n        if (this.activeAnnotation) {\n            this.activeAnnotation.setControlPointsVisibility(false);\n            this.activeAnnotation = false;\n        }\n    }\n    /**\n     * Generates API config for popup in the same format as options for\n     * Annotation object.\n     *\n     * @function Highcharts.NavigationBindings#annotationToFields\n     *\n     * @param {Highcharts.Annotation} annotation\n     *        Annotations object\n     *\n     * @return {Highcharts.Dictionary<string>}\n     *         Annotation options to be displayed in popup box\n     */\n    annotationToFields(annotation) {\n        const options = annotation.options, editables = NavigationBindings.annotationsEditable, nestedEditables = editables.nestedOptions, type = NavigationBindings_pick(options.type, options.shapes && options.shapes[0] &&\n            options.shapes[0].type, options.labels && options.labels[0] &&\n            options.labels[0].type, 'label'), nonEditables = NavigationBindings.annotationsNonEditable[options.langKey] || [], visualOptions = {\n            langKey: options.langKey,\n            type: type\n        };\n        /**\n         * Nested options traversing. Method goes down to the options and copies\n         * allowed options (with values) to new object, which is last parameter:\n         * \"parent\".\n         *\n         * @private\n         *\n         * @param {*} option\n         *        Atomic type or object/array\n         *\n         * @param {string} key\n         *        Option name, for example \"visible\" or \"x\", \"y\"\n         *\n         * @param {Object} parentEditables\n         *        Editables from NavigationBindings.annotationsEditable\n         *\n         * @param {Object} parent\n         *        Where new options will be assigned\n         */\n        function traverse(option, key, parentEditables, parent, parentKey) {\n            let nextParent;\n            if (parentEditables &&\n                NavigationBindings_defined(option) &&\n                nonEditables.indexOf(key) === -1 &&\n                ((parentEditables.indexOf &&\n                    parentEditables.indexOf(key)) >= 0 ||\n                    parentEditables[key] || // Nested array\n                    parentEditables === true // Simple array\n                )) {\n                // Roots:\n                if (NavigationBindings_isArray(option)) {\n                    parent[key] = [];\n                    option.forEach((arrayOption, i) => {\n                        if (!NavigationBindings_isObject(arrayOption)) {\n                            // Simple arrays, e.g. [String, Number, Boolean]\n                            traverse(arrayOption, 0, nestedEditables[key], parent[key], key);\n                        }\n                        else {\n                            // Advanced arrays, e.g. [Object, Object]\n                            parent[key][i] = {};\n                            NavigationBindings_objectEach(arrayOption, (nestedOption, nestedKey) => {\n                                traverse(nestedOption, nestedKey, nestedEditables[key], parent[key][i], key);\n                            });\n                        }\n                    });\n                }\n                else if (NavigationBindings_isObject(option)) {\n                    nextParent = {};\n                    if (NavigationBindings_isArray(parent)) {\n                        parent.push(nextParent);\n                        nextParent[key] = {};\n                        nextParent = nextParent[key];\n                    }\n                    else {\n                        parent[key] = nextParent;\n                    }\n                    NavigationBindings_objectEach(option, (nestedOption, nestedKey) => {\n                        traverse(nestedOption, nestedKey, key === 0 ?\n                            parentEditables :\n                            nestedEditables[key], nextParent, key);\n                    });\n                }\n                else {\n                    // Leaf:\n                    if (key === 'format') {\n                        parent[key] = [\n                            NavigationBindings_format(option, annotation.labels[0].points[0]).toString(),\n                            'text'\n                        ];\n                    }\n                    else if (NavigationBindings_isArray(parent)) {\n                        parent.push([option, NavigationBindings_getFieldType(parentKey, option)]);\n                    }\n                    else {\n                        parent[key] = [option, NavigationBindings_getFieldType(key, option)];\n                    }\n                }\n            }\n        }\n        NavigationBindings_objectEach(options, (option, key) => {\n            if (key === 'typeOptions') {\n                visualOptions[key] = {};\n                NavigationBindings_objectEach(options[key], (typeOption, typeKey) => {\n                    traverse(typeOption, typeKey, nestedEditables, visualOptions[key], typeKey);\n                });\n            }\n            else {\n                traverse(option, key, editables[type], visualOptions, key);\n            }\n        });\n        return visualOptions;\n    }\n    /**\n     * Get all class names for all parents in the element. Iterates until finds\n     * main container.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#getClickedClassNames\n     *\n     * @param {Highcharts.HTMLDOMElement} container\n     * Container that event is bound to.\n     *\n     * @param {global.Event} event\n     * Browser's event.\n     *\n     * @return {Array<Array<string, Highcharts.HTMLDOMElement>>}\n     * Array of class names with corresponding elements\n     */\n    getClickedClassNames(container, event) {\n        let element = event.target, classNames = [], elemClassName;\n        while (element && element.tagName) {\n            elemClassName = attr(element, 'class');\n            if (elemClassName) {\n                classNames = classNames.concat(elemClassName\n                    .split(' ')\n                    // eslint-disable-next-line no-loop-func\n                    .map((name) => ([name, element])));\n            }\n            element = element.parentNode;\n            if (element === container) {\n                return classNames;\n            }\n        }\n        return classNames;\n    }\n    /**\n     * Get events bound to a button. It's a custom event delegation to find all\n     * events connected to the element.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#getButtonEvents\n     *\n     * @param {Highcharts.HTMLDOMElement} container\n     *        Container that event is bound to.\n     *\n     * @param {global.Event} event\n     *        Browser's event.\n     *\n     * @return {Object}\n     *         Object with events (init, start, steps, and end)\n     */\n    getButtonEvents(container, event) {\n        const navigation = this, classNames = this.getClickedClassNames(container, event);\n        let bindings;\n        classNames.forEach((className) => {\n            if (navigation.boundClassNames[className[0]] && !bindings) {\n                bindings = {\n                    events: navigation.boundClassNames[className[0]],\n                    button: className[1]\n                };\n            }\n        });\n        return bindings;\n    }\n    /**\n     * Bindings are just events, so the whole update process is simply\n     * removing old events and adding new ones.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#update\n     */\n    update(options) {\n        this.options = NavigationBindings_merge(true, this.options, options);\n        this.removeEvents();\n        this.initEvents();\n    }\n    /**\n     * Remove all events created in the navigation.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#removeEvents\n     */\n    removeEvents() {\n        this.eventsToUnbind.forEach((unbinder) => unbinder());\n    }\n    /**\n     * @private\n     * @function Highcharts.NavigationBindings#destroy\n     */\n    destroy() {\n        this.removeEvents();\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n// Define which options from annotations should show up in edit box:\nNavigationBindings.annotationsEditable = {\n    // `typeOptions` are always available\n    // Nested and shared options:\n    nestedOptions: {\n        labelOptions: ['style', 'format', 'backgroundColor'],\n        labels: ['style'],\n        label: ['style'],\n        style: ['fontSize', 'color'],\n        background: ['fill', 'strokeWidth', 'stroke'],\n        innerBackground: ['fill', 'strokeWidth', 'stroke'],\n        outerBackground: ['fill', 'strokeWidth', 'stroke'],\n        shapeOptions: ['fill', 'strokeWidth', 'stroke'],\n        shapes: ['fill', 'strokeWidth', 'stroke'],\n        line: ['strokeWidth', 'stroke'],\n        backgroundColors: [true],\n        connector: ['fill', 'strokeWidth', 'stroke'],\n        crosshairX: ['strokeWidth', 'stroke'],\n        crosshairY: ['strokeWidth', 'stroke']\n    },\n    // Simple shapes:\n    circle: ['shapes'],\n    ellipse: ['shapes'],\n    verticalLine: [],\n    label: ['labelOptions'],\n    // Measure\n    measure: ['background', 'crosshairY', 'crosshairX'],\n    // Others:\n    fibonacci: [],\n    tunnel: ['background', 'line', 'height'],\n    pitchfork: ['innerBackground', 'outerBackground'],\n    rect: ['shapes'],\n    // Crooked lines, elliots, arrows etc:\n    crookedLine: [],\n    basicAnnotation: ['shapes', 'labelOptions']\n};\n// Define non editable fields per annotation, for example Rectangle inherits\n// options from Measure, but crosshairs are not available\nNavigationBindings.annotationsNonEditable = {\n    rectangle: ['crosshairX', 'crosshairY', 'labelOptions'],\n    ellipse: ['labelOptions'],\n    circle: ['labelOptions']\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Annotations_NavigationBindings = (NavigationBindings);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * A config object for navigation bindings in annotations.\n *\n * @interface Highcharts.NavigationBindingsOptionsObject\n */ /**\n* ClassName of the element for a binding.\n* @name Highcharts.NavigationBindingsOptionsObject#className\n* @type {string|undefined}\n*/ /**\n* Last event to be fired after last step event.\n* @name Highcharts.NavigationBindingsOptionsObject#end\n* @type {Function|undefined}\n*/ /**\n* Initial event, fired on a button click.\n* @name Highcharts.NavigationBindingsOptionsObject#init\n* @type {Function|undefined}\n*/ /**\n* Event fired on first click on a chart.\n* @name Highcharts.NavigationBindingsOptionsObject#start\n* @type {Function|undefined}\n*/ /**\n* Last event to be fired after last step event. Array of step events to be\n* called sequentially after each user click.\n* @name Highcharts.NavigationBindingsOptionsObject#steps\n* @type {Array<Function>|undefined}\n*/\n(''); // Keeps doclets above in JS file\n\n;// ./code/es-modules/masters/modules/annotations.js\n\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nG.Annotation = G.Annotation || Annotations_Annotation;\nG.NavigationBindings = G.NavigationBindings || Annotations_NavigationBindings;\nG.Annotation.compose(G.Chart, G.NavigationBindings, G.Pointer, G.SVGRenderer);\n/* harmony default export */ const annotations_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__984__", "__WEBPACK_EXTERNAL_MODULE__660__", "AnnotationChart", "ControlTarget", "DropdownProperties", "ChartNavigationComposition", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "annotations_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "addEvent", "erase", "find", "fireEvent", "isArray", "isObject", "pick", "wrap", "chartAddAnnotation", "userOptions", "redraw", "annotation", "initAnnotation", "options", "annotations", "push", "graphic", "attr", "opacity", "chartCallback", "chart", "plotBoxClip", "renderer", "clipRect", "plotBox", "controlPointsGroup", "g", "zIndex", "clip", "add", "for<PERSON>ach", "annotationOptions", "i", "some", "drawAnnotations", "destroy", "event", "csvColumnHeaderFormatter", "exporting", "csv", "columnHeaderFormatter", "multiLevelHeaders", "dataRows", "xValues", "annotationHeader", "lang", "exportData", "startRowLength", "length", "annotationSeparator", "itemDelimiter", "joinAnnotations", "join", "labelOptions", "includeInDataExport", "labels", "label", "text", "annotationText", "points", "annotationX", "x", "xAxisIndex", "series", "xAxis", "index", "wasAdded", "newRow", "Array", "row", "maxRowLen", "Math", "max", "newRows", "header", "s", "columnTitle", "topLevelColumnTitle", "chartDrawAnnotations", "animate", "animationConfig", "chartRemoveAnnotation", "idOrAnnotation", "coll", "id", "onChartAfterInit", "annotationsOption", "annotationsUserOption", "wrapPointerOnContainerMouseDown", "proceed", "hasDraggedAnnotation", "apply", "slice", "arguments", "compose", "AnnotationClass", "ChartClass", "PointerClass", "chartProto", "addAnnotation", "pointer<PERSON><PERSON><PERSON>", "callbacks", "collectionsWithInit", "collectionsWithUpdate", "removeAnnotation", "types", "type", "Annotations_AnnotationChart", "defined", "Annotations_AnnotationDefaults", "visible", "animation", "crop", "draggable", "align", "allowOverlap", "backgroundColor", "borderColor", "borderRadius", "borderWidth", "className", "formatter", "y", "overflow", "padding", "shadow", "shape", "style", "fontSize", "fontWeight", "color", "useHTML", "verticalAlign", "shapeOptions", "stroke", "strokeWidth", "fill", "r", "snap", "controlPointOptions", "events", "cursor", "height", "symbol", "width", "doc", "isTouchDevice", "EventEmitter_addEvent", "EventEmitter_fireEvent", "objectEach", "EventEmitter_pick", "removeEvent", "Annotations_EventEmitter", "addEvents", "emitter", "addMouseDownEvent", "element", "e", "onMouseDown", "passive", "foreignObject", "<PERSON><PERSON><PERSON><PERSON>", "cancelClick", "pointer", "normalize", "target", "nonDOMEvents", "indexOf", "div", "onDrag", "styledMode", "cssPointer", "xy", "css", "isUpdating", "removeDocEvents", "hcEvents", "mouseMoveToRadians", "cx", "cy", "prevDy", "prevChartY", "prevDx", "prevChartX", "dy", "chartY", "dx", "chartX", "temp", "inverted", "atan2", "mouseMoveToScale", "sx", "sy", "mouseMoveToTranslation", "isInsidePlot", "plotLeft", "plotTop", "visiblePlotOnly", "translation", "translate", "shapes", "preventDefault", "button", "firesTouchEvents", "sourceCapabilities", "removeDrag", "hasDragged", "removeMouseUp", "onMouseUp", "merge", "ControlPoint_pick", "Annotations_ControlPoint", "constructor", "positioner", "render", "setVisibility", "update", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "seriesProto", "MockPoint_defined", "MockPoint_fireEvent", "MockPoint", "fromPoint", "point", "yAxis", "pointToPixels", "paneCoordinates", "plotX", "plotY", "mock", "plot<PERSON>id<PERSON>", "plotHeight", "getPlotBox", "translateX", "translateY", "pointToOptions", "applyOptions", "getOptions", "command", "setAxis", "refresh", "hasDynamicOptions", "len", "toPixels", "isInside", "refreshOptions", "toValue", "rotate", "radians", "cos", "sin", "tx", "ty", "scale", "xOrY", "axisName", "axisOptions", "toAnchor", "anchor", "_cx", "_cy", "addControlPoints", "controlPoints", "controlPointsOptions", "box", "tooltip", "getAnchor", "relativePosition", "absolutePosition", "destroyControlTarget", "controlPoint", "getPointsOptions", "splat", "linkPoints", "pointsOptions", "pointOptions", "isString", "pointConfig", "redrawControlPoints", "renderControlPoints", "transform", "transformation", "p1", "p2", "_point", "transformPoint", "Annotations_MockPoint", "translatePoint", "ControlTargetClass", "controlProto", "Annotations_ControlTarget", "Controllable_merge", "Controllable", "itemType", "collection", "init", "_args", "attrsFromOptions", "<PERSON><PERSON><PERSON>", "map", "attrsMap", "attrs", "tracker", "_parentGroup", "addClass", "setControlPointsVisibility", "shouldBeDrawn", "translateShape", "translateSecondPoint", "annotationIndex", "chartOptions", "newOptions", "parentGroup", "<PERSON><PERSON><PERSON><PERSON>", "Controllables_Controllable", "defaultMarkers", "ControllablePath_defaultMarkers", "arrow", "tagName", "attributes", "refY", "refX", "marker<PERSON>id<PERSON>", "markerHeight", "children", "ControllablePath_addEvent", "ControllablePath_defined", "extend", "ControllablePath_merge", "<PERSON><PERSON><PERSON>", "markerEndSetter", "createMarkerSetter", "markerStartSetter", "TRACKER_FILL", "svg", "markerType", "value", "onChartAfterGetContainer", "defs", "svgRendererAddMarker", "markerOptions", "child", "ast", "orient", "marker", "ControllablePath", "SVGRendererClass", "svgRendererProto", "add<PERSON><PERSON><PERSON>", "toD", "dOption", "showPath", "position", "pointIndex", "crispLine", "parent", "path", "setMarkers", "action", "placed", "item", "itemOptions", "def", "predefined<PERSON>ark<PERSON>", "markerId", "getAttribute", "dashStyle", "ControllableRect_merge", "ControllableRect", "rect", "Boolean", "Controllables_ControllablePath", "ControllableCircle_merge", "ControllableCircle", "circle", "setRadius", "ControllableEllipse_merge", "ControllableEllipse_defined", "ControllableEllipse", "createElement", "getDistanceFromLine", "point1", "point2", "x0", "y0", "abs", "sqrt", "getAttrs", "position2", "x1", "y1", "x2", "y2", "rx", "angle", "atan", "PI", "ry", "getRY", "getYAxis", "yAxisIndex", "getAbsolutePosition", "rotation", "rotationOriginX", "rotationOriginY", "setYRadius", "highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_", "highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default", "format", "ControllableLabel_extend", "getAlignFactor", "isNumber", "ControllableLabel_pick", "symbolConnector", "w", "h", "anchorX", "anchorY", "yOffset", "lateral", "ControllableLabel", "alignedPosition", "alignOptions", "round", "symbols", "connector", "justifiedOptions", "alignAttr", "off", "bBox", "getBBox", "chartAnnotations", "optionsChart", "chartBackground", "plotBackgroundColor", "background", "shapesWithoutBackground", "getContrast", "labelrank", "String", "anchorAbsolutePosition", "anchorRelativePosition", "itemPosition", "alignTo", "itemPosRelativeX", "itemPosRelativeY", "showItem", "distance", "getPosition", "getPlayingField", "negative", "ttBelow", "ControllableImage", "image", "src", "Controllables_ControllableLabel", "highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_", "highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default", "BaseForm_addEvent", "Shared_BaseForm", "parentDiv", "iconsURL", "container", "createPopupContainer", "closeButton", "addCloseButton", "popup", "backgroundImage", "match", "eventName", "closeButtonEvents", "bind", "document", "code", "closePopup", "showPopup", "toolbarClass", "popupDiv", "popupClose<PERSON><PERSON>on", "innerHTML", "emptyHTML", "classList", "remove", "removeAttribute", "append<PERSON><PERSON><PERSON>", "display", "PopupAnnotations_doc", "isFirefox", "PopupAnnotations_createElement", "PopupAnnotations_isArray", "PopupAnnotations_isObject", "PopupAnnotations_objectEach", "PopupAnnotations_pick", "stableSort", "addFormFields", "parentNode", "storage", "isRoot", "parentFullName", "<PERSON><PERSON><PERSON>", "addInput", "option", "reverse", "genInput", "createTextNode", "splice", "PopupIndicators_doc", "seriesTypes", "PopupIndicators_addEvent", "PopupIndicators_createElement", "PopupIndicators_defined", "PopupIndicators_isArray", "PopupIndicators_isObject", "PopupIndicators_objectEach", "PopupIndicators_stableSort", "dropdownParameters", "addColsContainer", "lhsCol", "rhsCol", "PopupIndicators_addFormFields", "seriesType", "rhsColWrapper", "fields", "params", "getNameType", "indicatorFullName", "name", "listAllSeries", "linkedParent", "volumeSeriesID", "addParamInputs", "addIndicatorList", "listType", "filter", "selectIndicator", "indicatorType", "isEdit", "setAttribute", "querySelectorAll", "plotOptions", "filteredSeriesArray", "filterSeriesArray", "filterSeries", "b", "seriesAName", "toLowerCase", "seriesBName", "indicatorList", "seriesSet", "btn", "textContent", "setElementHTML", "noFilterMatch", "fieldName", "selectBox", "addSelection", "addSelectionOptions", "addSearchBox", "clearFilterText", "clearFilter", "inputWrapper", "handleInputChange", "inputText", "input", "htmlFor", "labelClassName", "optionName", "optionParamList", "split", "labelText", "selectName", "parameterName", "selectedOption", "currentSeries", "seriesOptions", "seriesName", "parameterOption", "filteredSeries", "indicatorAliases", "navigation", "regex", "RegExp", "replace", "alias", "is", "nameBase", "toUpperCase", "PopupTabs_doc", "PopupTabs_addEvent", "PopupTabs_createElement", "addContentItem", "addMenuItem", "tabName", "disableTab", "menuItem", "deselectAll", "tabs", "tabsContent", "selectTab", "tab", "allTabs", "switchTabs", "Popup_doc", "Popup_addEvent", "Popup_createElement", "Popup_extend", "Popup_fireEvent", "Popup_pick", "Popup", "activeAnnotation", "navigationBindings", "unbind", "setTimeout", "inputAttributes", "inputName", "selectedButtonElement", "addButton", "fieldsDiv", "callback", "getFields", "inputList", "selectList", "linkedTo", "volumeTo", "fieldsOutput", "actionType", "param", "seriesId", "select", "parameter", "showForm", "indicators", "addForm", "addToolbar", "offsetHeight", "isInit", "lang<PERSON><PERSON>", "bottomRow", "saveButton", "top", "edit<PERSON><PERSON><PERSON>", "removeButton", "_options", "buttonParentDiv", "tabsContainers", "getAmount", "counter", "serie", "indicatorsCount", "firstTab", "composed", "PopupComposition_addEvent", "pushUnique", "PopupComposition_wrap", "onNavigationBindingsClosePopup", "onNavigationBindingsShowPopup", "config", "stockTools", "gui", "formType", "onSubmit", "wrapPointerOnContainerMouserDown", "inClass", "Popup_PopupComposition", "NagivationBindingsClass", "getDeferredAnimation", "defaultOptions", "destroyObjectProperties", "Annotation_erase", "Annotation_fireEvent", "Annotation_merge", "Annotation_pick", "getLabelsAndShapesOptions", "baseOptions", "mergedOptions", "someBaseOptions", "newOptionsValue", "basicOptions", "Annotation", "NavigationBindingsClass", "setOptions", "labelsAndShapes", "addClipPaths", "setClipAxes", "clipXAxis", "clipYAxis", "getClipBox", "addLabels", "labelsOptions", "initLabel", "addShapes", "initShape", "destroyItem", "labelCollectors", "labelCollector", "left", "initProperties", "_annotation<PERSON>r<PERSON>hart", "_userOptions", "animOptions", "set<PERSON>abelCollector", "shapesMap", "redrawItems", "redrawItem", "renderItem", "adjustVisibility", "hasVisiblePoints", "visibility", "show", "hide", "items", "shapesGroup", "labelsGroup", "renderItems", "xAxes", "yAxes", "linkedAxes", "concat", "reduce", "axes", "labelOrShape", "setItemControlPointsVisibility", "userOptionsIndex", "ControlPoint", "Additions", "updates", "addUpdate", "updateFn", "Chart_ChartNavigationComposition", "NavigationBindingsUtilities_defined", "NavigationBindingsUtilities_isNumber", "NavigationBindingsUtilities_pick", "annotationsFieldsTypes", "title", "NavigationBindingsUtilities", "getAssignedAxis", "coords", "coord", "extremes", "axis", "getExtremes", "axisMin", "min", "axisMax", "minPointOffset", "isInternal", "getFieldType", "predefinedType", "fieldType", "NavigationBindingsDefaults_getAssignedAxis", "NavigationBindingsDefaults_isNumber", "NavigationBindingsDefaults_merge", "NavigationBindingsDefaults", "simpleShapes", "lines", "ellipse", "rectangle", "typeOptions", "backgroundColors", "bindingsClassName", "bindings", "circleAnnotation", "start", "getCoordinates", "coordsX", "coordsY", "annotationsOptions", "steps", "mockPointOpts", "pow", "ellipseAnnotation", "newR", "newRY", "rectangleAnnotation", "labelAnnotation", "defer", "NavigationBindings_format", "NavigationBindings_composed", "NavigationBindings_doc", "win", "NavigationBindings_getAssignedAxis", "NavigationBindings_getFieldType", "NavigationBindings_addEvent", "NavigationBindings_defined", "NavigationBindings_fireEvent", "NavigationBindings_isArray", "isFunction", "NavigationBindings_isNumber", "NavigationBindings_isObject", "NavigationBindings_merge", "NavigationBindings_objectEach", "NavigationBindings_pick", "NavigationBindings_pushUnique", "onAnnotationRemove", "deselectAnnotation", "onChartDestroy", "onChartLoad", "NavigationBindings", "initEvents", "initUpdate", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disabledClassName", "buttonsEnabled", "boundClassNames", "buttonNode", "cls", "noDataState", "NavigationBindings_onNavigationBindingsClosePopup", "onNavigationBindingsDeselectButton", "selectableAnnotation", "annotationType", "touchStartX", "touchStartY", "originalClick", "click", "selectAndShowPopup", "eventArguments", "prevAnnotation", "annotationToFields", "data", "fieldsToOptions", "crosshairY", "enabled", "crosshairX", "touchstart", "touches", "clientX", "clientY", "touchend", "changedTouches", "eventsToUnbind", "getElementsByClassName", "getCoords", "bindingsContainer", "subContainer", "getButtonEvents", "contains", "bindingsButtonClick", "bindingsChartClick", "bindingsContainerMouseMove", "clickEvent", "svgContainer", "boxWrapper", "shouldEventBeFired", "nextEvent", "currentUserDetails", "mouseMoveEvent", "<PERSON><PERSON><PERSON><PERSON>", "removeClass", "closestPolyfill", "el", "ElementProto", "Element", "elementMatches", "matches", "msMatchesSelector", "webkitMatchesSelector", "ret", "closest", "parentElement", "nodeType", "stepIndex", "end", "_container", "moveEvent", "field", "parsedValue", "parseFloat", "<PERSON><PERSON><PERSON><PERSON>", "nextName", "editables", "annotationsEditable", "nestedEditables", "nestedOptions", "nonEditables", "annotationsNonEditable", "visualOptions", "traverse", "parentEditables", "parent<PERSON><PERSON>", "nextParent", "arrayOption", "nestedOption", "nested<PERSON><PERSON>", "toString", "typeOption", "typeKey", "getClickedClassNames", "classNames", "elemClassName", "removeEvents", "unbinder", "innerBackground", "outerBackground", "line", "verticalLine", "measure", "<PERSON><PERSON><PERSON><PERSON>", "tunnel", "pitchfork", "crookedLine", "basicAnnotation", "G", "Chart", "Pointer", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "CAWA,AAXA;;;;;;;;;;CAUC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,UAAa,CAAEA,EAAK,WAAc,CAAC,GAAM,EAC3I,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,iCAAkC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAACA,EAAK,UAAa,CAACA,EAAK,GAAM,CAAE,GACzJ,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,iCAAiC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,UAAa,CAAEA,EAAK,WAAc,CAAC,GAAM,EAE7KA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,UAAa,CAAEA,EAAK,UAAa,CAAC,GAAM,CACpJ,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,EAAkCC,IACzI,AAAC,CAAA,KACP,aACA,IAuVNC,EA4jDAC,EAslEAC,EA+pDAC,EAxoLUC,EAAuB,CAE/B,IACC,AAACb,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGS,CAEX,EAEA,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,AAACL,IAERA,EAAOD,OAAO,CAAGQ,CAEX,CAEI,EAGIO,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAalB,OAAO,CAG5B,IAAIC,EAASc,CAAwB,CAACE,EAAS,CAAG,CAGjDjB,QAAS,CAAC,CACX,EAMA,OAHAc,CAAmB,CAACG,EAAS,CAAChB,EAAQA,EAAOD,OAAO,CAAEgB,GAG/Cf,EAAOD,OAAO,AACtB,CAMCgB,EAAoBI,CAAC,CAAG,AAACnB,IACxB,IAAIoB,EAASpB,GAAUA,EAAOqB,UAAU,CACvC,IAAOrB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAe,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACvB,EAASyB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAAC3B,EAAS0B,IAC5EE,OAAOC,cAAc,CAAC7B,EAAS0B,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,EAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAarH,GAAM,CAAEE,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAEC,UAAAA,CAAS,CAAEC,QAAAA,CAAO,CAAEC,SAAAA,CAAQ,CAAEC,KAAAA,CAAI,CAAEC,KAAAA,CAAI,CAAE,CAAIR,IAsB7E,SAASS,EAAmBC,CAAW,CAAEC,CAAM,EAC3C,IAAMC,EAAa,IAAI,CAACC,cAAc,CAACH,GAQvC,OAPA,IAAI,CAACI,OAAO,CAACC,WAAW,CAACC,IAAI,CAACJ,EAAWE,OAAO,EAC5CP,EAAKI,EAAQ,CAAA,KACbC,EAAWD,MAAM,GACjBC,EAAWK,OAAO,CAACC,IAAI,CAAC,CACpBC,QAAS,CACb,IAEGP,CACX,CAIA,SAASQ,IACL,IAAMC,EAAQ,IAAI,AAClBA,CAAAA,EAAMC,WAAW,CAAG,IAAI,CAACC,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAACC,OAAO,EACvDJ,EAAMK,kBAAkB,CAAGL,EAAME,QAAQ,CACpCI,CAAC,CAAC,kBACFT,IAAI,CAAC,CAAEU,OAAQ,EAAG,GAClBC,IAAI,CAACR,EAAMC,WAAW,EACtBQ,GAAG,GACRT,EAAMP,OAAO,CAACC,WAAW,CAACgB,OAAO,CAAC,CAACC,EAAmBC,KAClD,GAEA,CAACZ,EAAMN,WAAW,CAACmB,IAAI,CAAC,AAACtB,GAAeA,EAAWE,OAAO,GAAKkB,GAAoB,CAC/E,IAAMpB,EAAaS,EAAMR,cAAc,CAACmB,EACxCX,CAAAA,EAAMP,OAAO,CAACC,WAAW,CAACkB,EAAE,CAAGrB,EAAWE,OAAO,AACrD,CACJ,GACAO,EAAMc,eAAe,GACrBlC,EAASoB,EAAO,SAAUA,EAAMc,eAAe,EAC/ClC,EAASoB,EAAO,UAAW,WACvBA,EAAMC,WAAW,CAACc,OAAO,GACzBf,EAAMK,kBAAkB,CAACU,OAAO,EACpC,GACAnC,EAASoB,EAAO,aAAc,SAAUgB,CAAK,EACzC,IAAMtB,EAAcM,EAAMN,WAAW,CAAEuB,EAA2B,AAAC,CAAA,AAAC,IAAI,CAACxB,OAAO,CAACyB,SAAS,EACtF,IAAI,CAACzB,OAAO,CAACyB,SAAS,CAACC,GAAG,EAC1B,CAAC,CAAA,EAAGC,qBAAqB,CAG7BC,EAAoB,CAACL,EAAMM,QAAQ,CAAC,EAAE,CAACC,OAAO,CAAEC,EAAoBxB,EAAMP,OAAO,CAACgC,IAAI,EAClFzB,EAAMP,OAAO,CAACgC,IAAI,CAACC,UAAU,EAC7B1B,EAAMP,OAAO,CAACgC,IAAI,CAACC,UAAU,CAACF,gBAAgB,CAgB/CG,EAAiBX,EAAMM,QAAQ,CAAC,EAAE,CAACM,MAAM,CAAEC,EAAuB7B,EAAMP,OAAO,CAACyB,SAAS,EACxFlB,EAAMP,OAAO,CAACyB,SAAS,CAACC,GAAG,EAC3BnB,EAAMP,OAAO,CAACyB,SAAS,CAACC,GAAG,CAACzB,WAAW,EACvCM,EAAMP,OAAO,CAACyB,SAAS,CAACC,GAAG,CAACzB,WAAW,CAACoC,aAAa,CAAGC,EAAmB/B,EAAMP,OAAO,CAACyB,SAAS,EAClGlB,EAAMP,OAAO,CAACyB,SAAS,CAACC,GAAG,EAC3BnB,EAAMP,OAAO,CAACyB,SAAS,CAACC,GAAG,CAACzB,WAAW,EACvCM,EAAMP,OAAO,CAACyB,SAAS,CAACC,GAAG,CAACzB,WAAW,CAACsC,IAAI,CAChDtC,EAAYgB,OAAO,CAAC,AAACnB,IACbA,EAAWE,OAAO,CAACwC,YAAY,EAC/B1C,EAAWE,OAAO,CAACwC,YAAY,CAACC,mBAAmB,EACnD3C,EAAW4C,MAAM,CAACzB,OAAO,CAAC,AAAC0B,IACvB,GAAIA,EAAM3C,OAAO,CAAC4C,IAAI,CAAE,CACpB,IAAMC,EAAiBF,EAAM3C,OAAO,CAAC4C,IAAI,CACzCD,EAAMG,MAAM,CAAC7B,OAAO,CAAC,AAAC6B,IAClB,IAAMC,EAAcD,EAAOE,CAAC,CAAEC,EAAaH,EAAOI,MAAM,CAACC,KAAK,CAC1DL,EAAOI,MAAM,CAACC,KAAK,CAACC,KAAK,CACzB,GACAC,EAAW,CAAA,EAGf,GAAIJ,AAAe,KAAfA,EAAmB,CACnB,IAAMnF,EAAIyD,EAAMM,QAAQ,CAAC,EAAE,CAACM,MAAM,CAAEmB,EAAS,AAAIC,MAAMzF,GACvD,IAAK,IAAIqD,EAAI,EAAGA,EAAIrD,EAAG,EAAEqD,EACrBmC,CAAM,CAACnC,EAAE,CAAG,GAEhBmC,EAAOpD,IAAI,CAAC2C,GACZS,EAAOxB,OAAO,CAAG,EAAE,CACnBwB,EAAOxB,OAAO,CAACmB,EAAW,CAAGF,EAC7BxB,EAAMM,QAAQ,CAAC3B,IAAI,CAACoD,GACpBD,EAAW,CAAA,CACf,CAuBA,GApBI,AAACA,GACD9B,EAAMM,QAAQ,CAACZ,OAAO,CAAC,AAACuC,IAChB,CAACH,GACDG,EAAI1B,OAAO,EACXmB,AAAe,KAAK,IAApBA,GACAF,IAAgBS,EAAI1B,OAAO,CAACmB,EAAW,GACnCX,GACAkB,EAAIrB,MAAM,CAAGD,EACbsB,CAAG,CAACA,EAAIrB,MAAM,CAAG,EAAE,EAAKC,EACpBS,EAGJW,EAAItD,IAAI,CAAC2C,GAEbQ,EAAW,CAAA,EAEnB,GAIA,CAACA,EAAU,CACX,IAAMvF,EAAIyD,EAAMM,QAAQ,CAAC,EAAE,CAACM,MAAM,CAAEmB,EAAS,AAAIC,MAAMzF,GACvD,IAAK,IAAIqD,EAAI,EAAGA,EAAIrD,EAAG,EAAEqD,EACrBmC,CAAM,CAACnC,EAAE,CAAG,EAEhBmC,CAAAA,CAAM,CAAC,EAAE,CAAGP,EACZO,EAAOpD,IAAI,CAAC2C,GACZS,EAAOxB,OAAO,CAAG,EAAE,CACfmB,AAAe,KAAK,IAApBA,GACAK,CAAAA,EAAOxB,OAAO,CAACmB,EAAW,CAAGF,CAAU,EAE3CxB,EAAMM,QAAQ,CAAC3B,IAAI,CAACoD,EACxB,CACJ,EACJ,CACJ,EAER,GACA,IAAIG,EAAY,EAChBlC,EAAMM,QAAQ,CAACZ,OAAO,CAAC,AAACuC,IACpBC,EAAYC,KAAKC,GAAG,CAACF,EAAWD,EAAIrB,MAAM,CAC9C,GACA,IAAMyB,EAAUH,EAAYlC,EAAMM,QAAQ,CAAC,EAAE,CAACM,MAAM,CACpD,IAAK,IAAIhB,EAAI,EAAGA,EAAIyC,EAASzC,IAAK,CAC9B,IAAM0C,EAASlC,AA7F0D,SAAUyB,CAAK,EACxF,IAAIU,SACJ,AAAItC,GAEIsC,AAAM,CAAA,IADVA,CAAAA,EAAItC,EAAyB4B,EAAK,EAEvBU,EAIf,CADAA,EAAI/B,EAAmB,IAAMqB,EACzBxB,GACO,CACHmC,YAAaD,EACbE,oBAAqBF,CACzB,EAEGA,CACX,EA6EyC3C,EAAI,GACrCS,GACAL,EAAMM,QAAQ,CAAC,EAAE,CAAC3B,IAAI,CAAC2D,EAAOG,mBAAmB,EACjDzC,EAAMM,QAAQ,CAAC,EAAE,CAAC3B,IAAI,CAAC2D,EAAOE,WAAW,GAGzCxC,EAAMM,QAAQ,CAAC,EAAE,CAAC3B,IAAI,CAAC2D,EAE/B,CACJ,EACJ,CAIA,SAASI,IACL,IAAI,CAACzD,WAAW,CAACJ,IAAI,CAAC,IAAI,CAACO,OAAO,EAClC,IAAI,CAACV,WAAW,CAACgB,OAAO,CAAC,AAACnB,IACtBA,EAAWD,MAAM,GACjBC,EAAWK,OAAO,CAAC+D,OAAO,CAAC,CACvB7D,QAAS,CACb,EAAGP,EAAWqE,eAAe,CACjC,EACJ,CASA,SAASC,EAAsBC,CAAc,EACzC,IAAMpE,EAAc,IAAI,CAACA,WAAW,CAAEH,EAAa,AAACuE,AAAwB,gBAAxBA,EAAeC,IAAI,CACnED,EACAhF,EAAKY,EAAa,SAAUH,CAAU,EAClC,OAAOA,EAAWE,OAAO,CAACuE,EAAE,GAAKF,CACrC,GACAvE,IACAR,EAAUQ,EAAY,UACtBV,EAAM,IAAI,CAACY,OAAO,CAACC,WAAW,CAAEH,EAAWE,OAAO,EAClDZ,EAAMa,EAAaH,GACnBA,EAAWwB,OAAO,GAE1B,CAKA,SAASkD,IACL,IAAoBC,EAAoB,IAAI,CAACzE,OAAO,CAACC,WAAW,CAAEyE,EAAwB,IAAI,CAAC9E,WAAW,CAACK,WAAW,AACtHM,CADc,IAAI,CACZN,WAAW,CAAG,EAAE,CAClB,AAACV,EAAQ,IAAI,CAACS,OAAO,CAACC,WAAW,GACjC,CAAA,IAAI,CAACD,OAAO,CAACC,WAAW,CAAG,EAAE,AAAD,EAE5BT,EAASkF,EAAuB,CAAA,IAChClF,EAASiF,EAAmB,CAAA,IAC5B,IAAI,CAACzE,OAAO,CAACC,WAAW,CAACC,IAAI,CAACuE,EAEtC,CAIA,SAASE,EAAgCC,CAAO,EACxC,AAAC,IAAI,CAACrE,KAAK,CAACsE,oBAAoB,EAChCD,EAAQE,KAAK,CAAC,IAAI,CAAEvB,MAAM3E,SAAS,CAACmG,KAAK,CAACjG,IAAI,CAACkG,UAAW,GAElE,CAuCI5H,AACDA,CAAAA,GAAoBA,CAAAA,EAAkB,CAAC,CAAA,CAAC,EADvB6H,OAAO,CApBvB,SAAiBC,CAAe,CAAEC,CAAU,CAAEC,CAAY,EACtD,IAAMC,EAAaF,EAAWvG,SAAS,CACvC,GAAI,CAACyG,EAAWC,aAAa,CAAE,CAC3B,IAAMC,EAAeH,EAAaxG,SAAS,CAC3CO,EAASgG,EAAY,YAAaX,GAClCa,EAAWC,aAAa,CAAG3F,EAC3B0F,EAAWG,SAAS,CAACtF,IAAI,CAACI,GAC1B+E,EAAWI,mBAAmB,CAACxF,WAAW,CAAG,CAACN,EAAmB,CACjE0F,EAAWK,qBAAqB,CAACxF,IAAI,CAAC,eACtCmF,EAAWhE,eAAe,CAAG4C,EAC7BoB,EAAWM,gBAAgB,CAAGvB,EAC9BiB,EAAWtF,cAAc,CAAG,SAA6BH,CAAW,EAChE,IACsBE,EAAa,GADdoF,CAAAA,EAAgBU,KAAK,CAAChG,EAAYiG,IAAI,CAAC,EACxDX,CAAc,EAAiC,IAAI,CAAEtF,GAEzD,OADA,IAAI,CAACK,WAAW,CAACC,IAAI,CAACJ,GACfA,CACX,EACAJ,EAAK6F,EAAc,uBAAwBZ,EAC/C,CACJ,EAQyB,IAAMmB,EAA+B1I,EAS5D,CAAE2I,QAAAA,CAAO,CAAE,CAAI7G,IA8oBc8G,EA9mBR,CA0BvBC,QAAS,CAAA,EAiBTC,UAAW,CAAC,EAUZC,KAAM,CAAA,EAmBNC,UAAW,KAQX5D,aAAc,CAUV6D,MAAO,SASPC,aAAc,CAAA,EAUdC,gBAAiB,sBASjBC,YAAa,UAObC,aAAc,EAOdC,YAAa,EASbC,UAAW,wBAQXR,KAAM,CAAA,EA8CNS,UAAW,WACP,OAAOb,EAAQ,IAAI,CAACc,CAAC,EAAI,GAAK,IAAI,CAACA,CAAC,CAAG,kBAC3C,EAWApE,oBAAqB,CAAA,EAWrBqE,SAAU,UAQVC,QAAS,EAWTC,OAAQ,CAAA,EASRC,MAAO,UAWPC,MAAO,CAEHC,SAAU,QAEVC,WAAY,SAEZC,MAAO,UACX,EAKAC,QAAS,CAAA,EASTC,cAAe,SASfvE,EAAG,EASH6D,EAAG,GACP,EAkHAW,aAAc,CAuFVC,OAAQ,sBAORC,YAAa,EASbC,KAAM,sBAONC,EAAG,EAKHC,KAAM,CACV,EAUAC,oBAAqB,CAQjBC,OAAQ,CAAC,EAITb,MAAO,CACHc,OAAQ,UACRL,KAAM,UACNF,OAAQ,UACR,eAAgB,CACpB,EACAQ,OAAQ,GACRC,OAAQ,SACRjC,QAAS,CAAA,EACTkC,MAAO,EACX,EAyCAJ,OAAQ,CAAC,EAUTnC,MAAO,CAAC,EAIR9E,OAAQ,CACZ,EAoBM,CAAEsH,IAAAA,CAAG,CAAEC,cAAAA,CAAa,CAAE,CAAInJ,IAE1B,CAAEC,SAAUmJ,CAAqB,CAAEhJ,UAAWiJ,CAAsB,CAAEC,WAAAA,CAAU,CAAE/I,KAAMgJ,CAAiB,CAAEC,YAAAA,CAAW,CAAE,CAAIxJ,IAsO/FyJ,EA7NnC,MAUIC,WAAY,CACR,IAAMC,EAAU,IAAI,CAAEC,EAAoB,SAAUC,CAAO,EACvDT,EAAsBS,EAASV,EAAgB,aAAe,YAAa,AAACW,IACxEH,EAAQI,WAAW,CAACD,EACxB,EAAG,CAAEE,QAAS,CAAA,CAAM,EACxB,EA0BA,GAzBAJ,EAAkB,IAAI,CAAC3I,OAAO,CAAC4I,OAAO,EACtC,AAACF,CAAAA,EAAQnG,MAAM,EAAI,EAAE,AAAD,EAAGzB,OAAO,CAAC,AAAC0B,IACxBA,EAAM3C,OAAO,CAACsH,OAAO,EACrB3E,EAAMxC,OAAO,CAACyC,IAAI,EAClB,CAACD,EAAMxC,OAAO,CAACyC,IAAI,CAACuG,aAAa,EAEjCL,EAAkBnG,EAAMxC,OAAO,CAACyC,IAAI,CAACmG,OAAO,CAEpD,GACAP,EAAWK,EAAQ7I,OAAO,CAAC+H,MAAM,CAAE,CAACxG,EAAOsE,KACvC,IAAMuD,EAAe,SAAUJ,CAAC,EACxBnD,AAAS,UAATA,GAAqBgD,EAAQQ,WAAW,EACxC9H,EAAMzC,IAAI,CAAC+J,EAASA,EAAQtI,KAAK,CAAC+I,OAAO,EAAEC,UAAUP,GAAIH,EAAQW,MAAM,CAE/E,CACI,AAA+C,CAAA,KAA/C,AAACX,CAAAA,EAAQY,YAAY,EAAI,EAAE,AAAD,EAAGC,OAAO,CAAC7D,IACrCyC,EAAsBO,EAAQ1I,OAAO,CAAC4I,OAAO,CAAElD,EAAMuD,EAAc,CAAEF,QAAS,CAAA,CAAM,GAChFL,EAAQ1I,OAAO,CAACwJ,GAAG,EACnBrB,EAAsBO,EAAQ1I,OAAO,CAACwJ,GAAG,CAAE9D,EAAMuD,EAAc,CAAEF,QAAS,CAAA,CAAM,IAIpFZ,EAAsBO,EAAShD,EAAMuD,EAAc,CAAEF,QAAS,CAAA,CAAM,EAE5E,GACIL,EAAQ7I,OAAO,CAACoG,SAAS,GACzBkC,EAAsBO,EAAS,OAAQA,EAAQe,MAAM,EACjD,CAACf,EAAQ1I,OAAO,CAACM,QAAQ,CAACoJ,UAAU,EAAE,CACtC,IAAMC,EAAa,CACf9B,OAAQ,CACJhF,EAAG,YACH6D,EAAG,YACHkD,GAAI,MACR,CAAC,CAAClB,EAAQ7I,OAAO,CAACoG,SAAS,CAAC,AAChC,EACAyC,EAAQ1I,OAAO,CAAC6J,GAAG,CAACF,GACpB,AAACjB,CAAAA,EAAQnG,MAAM,EAAI,EAAE,AAAD,EAAGzB,OAAO,CAAC,AAAC0B,IACxBA,EAAM3C,OAAO,CAACsH,OAAO,EACrB3E,EAAMxC,OAAO,CAACyC,IAAI,EAClB,CAACD,EAAMxC,OAAO,CAACyC,IAAI,CAACuG,aAAa,EACjCxG,EAAMxC,OAAO,CAACyC,IAAI,CAACoH,GAAG,CAACF,EAE/B,EACJ,CAEA,AAACjB,EAAQoB,UAAU,EACnB1B,EAAuBM,EAAS,MAExC,CAIAvH,SAAU,CACN,IAAI,CAAC4I,eAAe,GACpBxB,EAAY,IAAI,EAChB,IAAI,CAACyB,QAAQ,CAAG,IACpB,CAKAC,mBAAmBpB,CAAC,CAAEqB,CAAE,CAAEC,CAAE,CAAE,CAC1B,IAAIC,EAASvB,EAAEwB,UAAU,CAAGF,EAAIG,EAASzB,EAAE0B,UAAU,CAAGL,EAAIM,EAAK3B,EAAE4B,MAAM,CAAGN,EAAIO,EAAK7B,EAAE8B,MAAM,CAAGT,EAAIU,EASpG,OARI,IAAI,CAACxK,KAAK,CAACyK,QAAQ,GACnBD,EAAON,EACPA,EAASF,EACTA,EAASQ,EACTA,EAAOF,EACPA,EAAKF,EACLA,EAAKI,GAEFrH,KAAKuH,KAAK,CAACN,EAAIE,GAAMnH,KAAKuH,KAAK,CAACV,EAAQE,EACnD,CAKAS,iBAAiBlC,CAAC,CAAEqB,CAAE,CAAEC,CAAE,CAAE,CACxB,IAAMG,EAASzB,EAAE0B,UAAU,CAAGL,EAAIE,EAASvB,EAAEwB,UAAU,CAAGF,EAAIO,EAAK7B,EAAE8B,MAAM,CAAGT,EAAIM,EAAK3B,EAAE4B,MAAM,CAAGN,EAC9Fa,EAAK,AAACN,CAAAA,GAAM,CAAA,EAAMJ,CAAAA,GAAU,CAAA,EAAIW,EAAK,AAACT,CAAAA,GAAM,CAAA,EAAMJ,CAAAA,GAAU,CAAA,EAChE,GAAI,IAAI,CAAChK,KAAK,CAACyK,QAAQ,CAAE,CACrB,IAAMD,EAAOK,EACbA,EAAKD,EACLA,EAAKJ,CACT,CACA,MAAO,CACH/H,EAAGmI,EACHtE,EAAGuE,CACP,CACJ,CAKAC,uBAAuBrC,CAAC,CAAE,CACtB,IAAI6B,EAAK7B,EAAE8B,MAAM,CAAG9B,EAAE0B,UAAU,CAAEC,EAAK3B,EAAE4B,MAAM,CAAG5B,EAAEwB,UAAU,CAAEO,EAMhE,OALI,IAAI,CAACxK,KAAK,CAACyK,QAAQ,GACnBD,EAAOJ,EACPA,EAAKE,EACLA,EAAKE,GAEF,CACH/H,EAAG6H,EACHhE,EAAG8D,CACP,CACJ,CAMAf,OAAOZ,CAAC,CAAE,CACN,GAAI,IAAI,CAACzI,KAAK,CAAC+K,YAAY,CAACtC,EAAE8B,MAAM,CAAG,IAAI,CAACvK,KAAK,CAACgL,QAAQ,CAAEvC,EAAE4B,MAAM,CAAG,IAAI,CAACrK,KAAK,CAACiL,OAAO,CAAE,CACvFC,gBAAiB,CAAA,CACrB,GAAI,CACA,IAAMC,EAAc,IAAI,CAACL,sBAAsB,CAACrC,EAC5C,AAA2B,CAAA,MAA3B,IAAI,CAAChJ,OAAO,CAACoG,SAAS,EACtBsF,CAAAA,EAAY7E,CAAC,CAAG,CAAA,EAEhB,AAA2B,MAA3B,IAAI,CAAC7G,OAAO,CAACoG,SAAS,EACtBsF,CAAAA,EAAY1I,CAAC,CAAG,CAAA,EAGhB6F,AADY,IAAI,CACR/F,MAAM,CAACX,MAAM,CACrB0G,AAFY,IAAI,CAER8C,SAAS,CAACD,EAAY1I,CAAC,CAAE0I,EAAY7E,CAAC,GAG9CgC,AALY,IAAI,CAKR+C,MAAM,CAAC3K,OAAO,CAAC,AAACgG,GAAUA,EAAM0E,SAAS,CAACD,EAAY1I,CAAC,CAAE0I,EAAY7E,CAAC,GAC9EgC,AANY,IAAI,CAMRnG,MAAM,CAACzB,OAAO,CAAC,AAAC0B,GAAUA,EAAMgJ,SAAS,CAACD,EAAY1I,CAAC,CAAE0I,EAAY7E,CAAC,IAElF,IAAI,CAAChH,MAAM,CAAC,CAAA,EAChB,CACJ,CAKAoJ,YAAYD,CAAC,CAAE,CAKX,GAJIA,EAAE6C,cAAc,EAChB7C,EAAE6C,cAAc,GAGhB7C,AAAa,IAAbA,EAAE8C,MAAM,CACR,OAEJ,IAAMjD,EAAU,IAAI,CAAES,EAAUT,EAAQtI,KAAK,CAAC+I,OAAO,CAGrDyC,EAAmB,AAAC/C,GAAGgD,oBAAoBD,kBAAqB,CAAA,EAE5DrB,EAAa1B,AADjBA,CAAAA,EAAIM,GAASC,UAAUP,IAAMA,CAAAA,EACV8B,MAAM,CAAEN,EAAaxB,EAAE4B,MAAM,AAChD/B,CAAAA,EAAQQ,WAAW,CAAG,CAAA,EACtBR,EAAQtI,KAAK,CAACsE,oBAAoB,CAAG,CAAA,EACrCgE,EAAQoD,UAAU,CAAG3D,EAAsBF,EAAKC,GAAiB0D,EAAmB,YAAc,YAAa,SAAU/C,CAAC,EACtHH,EAAQqD,UAAU,CAAG,CAAA,EAErBlD,AADAA,CAAAA,EAAIM,GAASC,UAAUP,IAAMA,CAAAA,EAC3B0B,UAAU,CAAGA,EACf1B,EAAEwB,UAAU,CAAGA,EACfjC,EAAuBM,EAAS,OAAQG,GACxC0B,EAAa1B,EAAE8B,MAAM,CACrBN,EAAaxB,EAAE4B,MAAM,AACzB,EAAGvC,GAAiB0D,EAAmB,CAAE7C,QAAS,CAAA,CAAM,EAAI,KAAK,GACjEL,EAAQsD,aAAa,CAAG7D,EAAsBF,EAAKC,GAAiB0D,EAAmB,WAAa,UAAW,WAG3G,IAAMjM,EAAa2I,EAAkBI,EAAQW,MAAM,EAAIX,EAAQW,MAAM,CAAC1J,UAAU,CAAE+I,EAAQW,MAAM,CAC5F1J,CAAAA,GAEAA,CAAAA,EAAWuJ,WAAW,CAAGR,EAAQqD,UAAU,AAAD,EAE9CrD,EAAQQ,WAAW,CAAGR,EAAQqD,UAAU,CACxCrD,EAAQtI,KAAK,CAACsE,oBAAoB,CAAG,CAAA,EACjCgE,EAAQqD,UAAU,EAElB3D,EAAuBE,EAAkB3I,EACzC+I,GAAU,eAEdA,EAAQqD,UAAU,CAAG,CAAA,EACrBrD,EAAQuD,SAAS,EACrB,EAAG/D,GAAiB0D,EAAmB,CAAE7C,QAAS,CAAA,CAAM,EAAI,KAAK,EACrE,CAIAkD,WAAY,CACR,IAAI,CAAClC,eAAe,EACxB,CAKAA,iBAAkB,CACV,IAAI,CAAC+B,UAAU,EACf,CAAA,IAAI,CAACA,UAAU,CAAG,IAAI,CAACA,UAAU,EAAC,EAElC,IAAI,CAACE,aAAa,EAClB,CAAA,IAAI,CAACA,aAAa,CAAG,IAAI,CAACA,aAAa,EAAC,CAEhD,CACJ,EAiBM,CAAEE,MAAAA,CAAK,CAAE5M,KAAM6M,CAAiB,CAAE,CAAIpN,IA4HTqN,EA/FnC,cAA2B5D,EAMvB6D,YAAYjM,CAAK,CAAEiJ,CAAM,CAAExJ,CAAO,CAAEoD,CAAK,CAAE,CACvC,KAAK,GAQL,IAAI,CAACqG,YAAY,CAAG,CAAC,OAAO,CAC5B,IAAI,CAAClJ,KAAK,CAAGA,EACb,IAAI,CAACiJ,MAAM,CAAGA,EACd,IAAI,CAACxJ,OAAO,CAAGA,EACf,IAAI,CAACoD,KAAK,CAAGkJ,EAAkBtM,EAAQoD,KAAK,CAAEA,EAClD,CAUA9B,SAAU,CACN,KAAK,CAACA,UACF,IAAI,CAACnB,OAAO,EACZ,CAAA,IAAI,CAACA,OAAO,CAAG,IAAI,CAACA,OAAO,CAACmB,OAAO,EAAC,EAExC,IAAI,CAACf,KAAK,CAAG,KACb,IAAI,CAACiJ,MAAM,CAAG,KACd,IAAI,CAACxJ,OAAO,CAAG,IACnB,CAMAH,OAAOqG,CAAS,CAAE,CACd,IAAI,CAAC/F,OAAO,CAAC+F,EAAY,UAAY,OAAO,CAAC,IAAI,CAAClG,OAAO,CAACyM,UAAU,CAAC3N,IAAI,CAAC,IAAI,CAAE,IAAI,CAAC0K,MAAM,EAC/F,CAKAkD,QAAS,CACL,IAAMnM,EAAQ,IAAI,CAACA,KAAK,CAAEP,EAAU,IAAI,CAACA,OAAO,AAChD,CAAA,IAAI,CAACG,OAAO,CAAGI,EAAME,QAAQ,CACxByH,MAAM,CAAClI,EAAQkI,MAAM,CAAE,EAAG,EAAGlI,EAAQmI,KAAK,CAAEnI,EAAQiI,MAAM,EAC1DjH,GAAG,CAACT,EAAMK,kBAAkB,EAC5BoJ,GAAG,CAAChK,EAAQkH,KAAK,EACtB,IAAI,CAACyF,aAAa,CAAC3M,EAAQiG,OAAO,EAElC,IAAI,CAAC2C,SAAS,EAClB,CAUA+D,cAAc1G,CAAO,CAAE,CACnB,IAAI,CAAC9F,OAAO,CAAC8F,EAAU,OAAS,OAAO,GACvC,IAAI,CAACjG,OAAO,CAACiG,OAAO,CAAGA,CAC3B,CASA2G,OAAOhN,CAAW,CAAE,CAChB,IAAMW,EAAQ,IAAI,CAACA,KAAK,CAAEiJ,EAAS,IAAI,CAACA,MAAM,CAAEpG,EAAQ,IAAI,CAACA,KAAK,CAAEpD,EAAUqM,EAAM,CAAA,EAAM,IAAI,CAACrM,OAAO,CAAEJ,GACxG,IAAI,CAAC0B,OAAO,GACZ,IAAI,CAACkL,WAAW,CAACjM,EAAOiJ,EAAQxJ,EAASoD,GACzC,IAAI,CAACsJ,MAAM,CAACnM,EAAMK,kBAAkB,EACpC,IAAI,CAACf,MAAM,EACf,CACJ,EAuBA,IAAIgN,EAAmInP,EAAoB,KACvJoP,EAAuJpP,EAAoBI,CAAC,CAAC+O,GASjL,GAAM,CAAE3J,OAAQ,CAAEtE,UAAWmO,CAAW,CAAE,CAAE,CAAID,IAE1C,CAAE/G,QAASiH,CAAiB,CAAE1N,UAAW2N,CAAmB,CAAE,CAAI/N,GA4BxE,OAAMgO,EAiBF,OAAOC,UAAUC,CAAK,CAAE,CACpB,OAAO,IAAIF,EAAUE,EAAMlK,MAAM,CAAC3C,KAAK,CAAE,KAAM,CAC3CyC,EAAGoK,EAAMpK,CAAC,CACV6D,EAAGuG,EAAMvG,CAAC,CACV1D,MAAOiK,EAAMlK,MAAM,CAACC,KAAK,CACzBkK,MAAOD,EAAMlK,MAAM,CAACmK,KAAK,AAC7B,EACJ,CAcA,OAAOC,cAAcF,CAAK,CAAEG,CAAe,CAAE,CACzC,IAAMrK,EAASkK,EAAMlK,MAAM,CAAE3C,EAAQ2C,EAAO3C,KAAK,CAC7CyC,EAAIoK,EAAMI,KAAK,EAAI,EAAG3G,EAAIuG,EAAMK,KAAK,EAAI,EAAG9M,EAgBhD,OAfIJ,EAAMyK,QAAQ,GACVoC,EAAMM,IAAI,EACV1K,EAAIoK,EAAMK,KAAK,CACf5G,EAAIuG,EAAMI,KAAK,GAGfxK,EAAIzC,EAAMoN,SAAS,CAAIP,CAAAA,EAAMK,KAAK,EAAI,CAAA,EACtC5G,EAAItG,EAAMqN,UAAU,CAAIR,CAAAA,EAAMI,KAAK,EAAI,CAAA,IAG3CtK,GAAU,CAACqK,IAEXvK,GAAKrC,AADLA,CAAAA,EAAUuC,EAAO2K,UAAU,EAAC,EACfC,UAAU,CACvBjH,GAAKlG,EAAQoN,UAAU,EAEpB,CACH/K,EAAGA,EACH6D,EAAGA,CACP,CACJ,CAYA,OAAOmH,eAAeZ,CAAK,CAAE,CACzB,MAAO,CACHpK,EAAGoK,EAAMpK,CAAC,CACV6D,EAAGuG,EAAMvG,CAAC,CACV1D,MAAOiK,EAAMlK,MAAM,CAACC,KAAK,CACzBkK,MAAOD,EAAMlK,MAAM,CAACmK,KAAK,AAC7B,CACJ,CAMAb,YAAYjM,CAAK,CAAEiJ,CAAM,CAAExJ,CAAO,CAAE,CAYhC,IAAI,CAAC0N,IAAI,CAAG,CAAA,EAEZ,IAAI,CAACN,KAAK,CAAG,IAAI,CAOjB,IAAI,CAAClK,MAAM,CAAG,CACV+C,QAAS,CAAA,EACT1F,MAAOA,EACPsN,WAAYd,EAAYc,UAAU,AACtC,EAKA,IAAI,CAACrE,MAAM,CAAGA,GAAU,KAOxB,IAAI,CAACxJ,OAAO,CAAGA,EAkCf,IAAI,CAACiO,YAAY,CAAC,IAAI,CAACC,UAAU,GACrC,CAMAD,aAAajO,CAAO,CAAE,CAClB,IAAI,CAACmO,OAAO,CAAGnO,EAAQmO,OAAO,CAC9B,IAAI,CAACC,OAAO,CAACpO,EAAS,KACtB,IAAI,CAACoO,OAAO,CAACpO,EAAS,KACtB,IAAI,CAACqO,OAAO,EAChB,CAOAH,YAAa,CACT,OAAO,IAAI,CAACI,iBAAiB,GACzB,IAAI,CAACtO,OAAO,CAAC,IAAI,CAACwJ,MAAM,EACxB,IAAI,CAACxJ,OAAO,AACpB,CAOAsO,mBAAoB,CAChB,MAAO,AAAwB,YAAxB,OAAO,IAAI,CAACtO,OAAO,AAC9B,CAMAsL,cAAe,CACX,IAAMkC,EAAQ,IAAI,CAACA,KAAK,CAAEC,EAAQ,IAAI,CAACA,KAAK,CAAEtK,EAAQ,IAAI,CAACD,MAAM,CAACC,KAAK,CAAEkK,EAAQ,IAAI,CAACnK,MAAM,CAACmK,KAAK,CAAErE,EAAI,CACpGhG,EAAGwK,EACH3G,EAAG4G,EACHnC,aAAc,CAAA,EACdtL,QAAS,CAAC,CACd,EAWA,OAVImD,GACA6F,CAAAA,EAAEsC,YAAY,CAAG0B,EAAkBQ,IAAUA,GAAS,GAAKA,GAASrK,EAAMoL,GAAG,AAAD,EAE5ElB,GACArE,CAAAA,EAAEsC,YAAY,CACVtC,EAAEsC,YAAY,EACV0B,EAAkBS,IAClBA,GAAS,GAAKA,GAASJ,EAAMkB,GAAG,AAAD,EAE3CtB,EAAoB,IAAI,CAAC/J,MAAM,CAAC3C,KAAK,CAAE,oBAAqByI,GACrDA,EAAEsC,YAAY,AACzB,CAKA+C,SAAU,CACN,IAAMnL,EAAS,IAAI,CAACA,MAAM,CAAEC,EAAQD,EAAOC,KAAK,CAAEkK,EAAQnK,EAAOmK,KAAK,CAAErN,EAAU,IAAI,CAACkO,UAAU,GAC7F/K,GACA,IAAI,CAACH,CAAC,CAAGhD,EAAQgD,CAAC,CAClB,IAAI,CAACwK,KAAK,CAAGrK,EAAMqL,QAAQ,CAACxO,EAAQgD,CAAC,CAAE,CAAA,KAGvC,IAAI,CAACA,CAAC,CAAG,KAAK,EACd,IAAI,CAACwK,KAAK,CAAGxN,EAAQgD,CAAC,EAEtBqK,GACA,IAAI,CAACxG,CAAC,CAAG7G,EAAQ6G,CAAC,CAClB,IAAI,CAAC4G,KAAK,CAAGJ,EAAMmB,QAAQ,CAACxO,EAAQ6G,CAAC,CAAE,CAAA,KAGvC,IAAI,CAACA,CAAC,CAAG,KACT,IAAI,CAAC4G,KAAK,CAAGzN,EAAQ6G,CAAC,EAE1B,IAAI,CAAC4H,QAAQ,CAAG,IAAI,CAACnD,YAAY,EACrC,CAKAoD,gBAAiB,CACb,IAAMxL,EAAS,IAAI,CAACA,MAAM,CAAEC,EAAQD,EAAOC,KAAK,CAAEkK,EAAQnK,EAAOmK,KAAK,AACtE,CAAA,IAAI,CAACrK,CAAC,CAAG,IAAI,CAAChD,OAAO,CAACgD,CAAC,CAAGG,EACtB,IAAI,CAACnD,OAAO,CAACgD,CAAC,CAAGG,EAAMwL,OAAO,CAAC,IAAI,CAACnB,KAAK,CAAE,CAAA,GAC3C,IAAI,CAACA,KAAK,CACd,IAAI,CAAC3G,CAAC,CAAG,IAAI,CAAC7G,OAAO,CAAC6G,CAAC,CAAGwG,EACtBA,EAAMsB,OAAO,CAAC,IAAI,CAAClB,KAAK,CAAE,CAAA,GAC1B,IAAI,CAACA,KAAK,AAClB,CAQAmB,OAAOvE,CAAE,CAAEC,CAAE,CAAEuE,CAAO,CAAE,CACpB,GAAI,CAAC,IAAI,CAACP,iBAAiB,GAAI,CAC3B,IAAMQ,EAAMpL,KAAKoL,GAAG,CAACD,GAAUE,EAAMrL,KAAKqL,GAAG,CAACF,GAAU7L,EAAI,IAAI,CAACwK,KAAK,CAAGnD,EAAIxD,EAAI,IAAI,CAAC4G,KAAK,CAAGnD,CAC9F,CAAA,IAAI,CAACkD,KAAK,CAAGwB,AAD0FhM,EAAI8L,EAAMjI,EAAIkI,EACnG1E,EAClB,IAAI,CAACoD,KAAK,CAAGwB,AAFkHjM,EAAI+L,EAAMlI,EAAIiI,EAE3HxE,EAClB,IAAI,CAACoE,cAAc,EACvB,CACJ,CAkBAQ,MAAM7E,CAAE,CAAEC,CAAE,CAAEa,CAAE,CAAEC,CAAE,CAAE,CAClB,GAAI,CAAC,IAAI,CAACkD,iBAAiB,GAAI,CAC3B,IAAMtL,EAAI,IAAI,CAACwK,KAAK,CAAGrC,EAAItE,EAAI,IAAI,CAAC4G,KAAK,CAAGrC,CAC5C,CAAA,IAAI,CAACoC,KAAK,CAAGwB,AADyC,CAAA,EAAI7D,CAAC,EAAKd,EAC9CrH,EAClB,IAAI,CAACyK,KAAK,CAAGwB,AAF6D,CAAA,EAAI7D,CAAC,EAAKd,EAElEzD,EAClB,IAAI,CAAC6H,cAAc,EACvB,CACJ,CAQAN,QAAQpO,CAAO,CAAEmP,CAAI,CAAE,CACnB,IAAMC,EAAYD,EAAO,OAASE,EAAcrP,CAAO,CAACoP,EAAS,CAAE7O,EAAQ,IAAI,CAAC2C,MAAM,CAAC3C,KAAK,AAC5F,CAAA,IAAI,CAAC2C,MAAM,CAACkM,EAAS,CACjB,AAAuB,UAAvB,OAAOC,EACHA,EACArC,EAAkBqC,GACb9O,CAAK,CAAC6O,EAAS,CAACC,EAAY,EAEzB9O,EAAM9B,GAAG,CAAC4Q,GACd,IAChB,CAOAC,UAAW,CACP,IAAMC,EAAS,CAAC,IAAI,CAAC/B,KAAK,CAAE,IAAI,CAACC,KAAK,CAAE,EAAG,EAAE,CAK7C,OAJI,IAAI,CAACvK,MAAM,CAAC3C,KAAK,CAACyK,QAAQ,GAC1BuE,CAAM,CAAC,EAAE,CAAG,IAAI,CAAC9B,KAAK,CACtB8B,CAAM,CAAC,EAAE,CAAG,IAAI,CAAC/B,KAAK,EAEnB+B,CACX,CAkBA5D,UAAU6D,CAAG,CAAEC,CAAG,CAAE5E,CAAE,CAAEF,CAAE,CAAE,CACnB,IAAI,CAAC2D,iBAAiB,KACvB,IAAI,CAACd,KAAK,EAAI3C,EACd,IAAI,CAAC4C,KAAK,EAAI9C,EACd,IAAI,CAAC+D,cAAc,GAE3B,CACJ,EAiHA,AAAC,SAAUrR,CAAa,EAepB,SAASqS,IACL,IAAMC,EAAgB,IAAI,CAACA,aAAa,CAAEC,EAAuB,IAAI,CAAC5P,OAAO,CAAC2P,aAAa,EAAI,EAAE,CACjGC,EAAqB3O,OAAO,CAAC,CAAC6G,EAAqB3G,KAC/C,IAAMnB,EAAUd,IAA8EmN,KAAK,CAAC,IAAI,CAACrM,OAAO,CAAC8H,mBAAmB,CAAEA,EAClI,AAAC9H,CAAAA,EAAQoD,KAAK,EACdpD,CAAAA,EAAQoD,KAAK,CAAGjC,CAAAA,EAEpByO,CAAoB,CAACzO,EAAE,CAAGnB,EAC1B2P,EAAczP,IAAI,CAAC,IAAIqM,EAAyB,IAAI,CAAChM,KAAK,CAAE,IAAI,CAAEP,GACtE,EACJ,CAUA,SAASuP,EAAOnC,CAAK,EACjB,IAAMzM,EAAUyM,EAAMlK,MAAM,CAAC2K,UAAU,GAAItN,EAAQ6M,EAAMlK,MAAM,CAAC3C,KAAK,CAAEsP,EAAMzC,EAAMM,IAAI,CACnFN,EAAMkC,QAAQ,GACd/O,EAAMuP,OAAO,EACTvP,EAAMuP,OAAO,CAACC,SAAS,CAACjR,IAAI,CAAC,CACzByB,MAAO6M,EAAMlK,MAAM,CAAC3C,KAAK,AAC7B,EAAG6M,IACH,CAAC,EAAG,EAAG,EAAG,EAAE,CAAEmC,EAAS,CAC3BvM,EAAG6M,CAAG,CAAC,EAAE,CAAI,CAAA,IAAI,CAAC7P,OAAO,CAACgD,CAAC,EAAI,CAAA,EAC/B6D,EAAGgJ,CAAG,CAAC,EAAE,CAAI,CAAA,IAAI,CAAC7P,OAAO,CAAC6G,CAAC,EAAI,CAAA,EAC/BoB,OAAQ4H,CAAG,CAAC,EAAE,EAAI,EAClB1H,MAAO0H,CAAG,CAAC,EAAE,EAAI,CACrB,EACA,MAAO,CACHG,iBAAkBT,EAClBU,iBAAkB/Q,IAA8EmN,KAAK,CAACkD,EAAQ,CAC1GvM,EAAGuM,EAAOvM,CAAC,CAAIoK,CAAAA,EAAMM,IAAI,CAAG/M,EAAQmN,UAAU,CAAGvN,EAAMgL,QAAQ,AAAD,EAC9D1E,EAAG0I,EAAO1I,CAAC,CAAIuG,CAAAA,EAAMM,IAAI,CAAG/M,EAAQoN,UAAU,CAAGxN,EAAMiL,OAAO,AAAD,CACjE,EACJ,CACJ,CA6BA,SAAS0E,IACL,IAAI,CAACP,aAAa,CAAC1O,OAAO,CAAC,AAACkP,GAAiBA,EAAa7O,OAAO,IACjE,IAAI,CAACf,KAAK,CAAG,KACb,IAAI,CAACoP,aAAa,CAAG,KACrB,IAAI,CAAC7M,MAAM,CAAG,KACd,IAAI,CAAC9C,OAAO,CAAG,KACX,IAAI,CAACF,UAAU,EACf,CAAA,IAAI,CAACA,UAAU,CAAG,IAAG,CAE7B,CAOA,SAASsQ,IACL,IAAMpQ,EAAU,IAAI,CAACA,OAAO,CAC5B,OAAQA,EAAQ8C,MAAM,EACjB9C,EAAQoN,KAAK,EAAIlO,IAA8EmR,KAAK,CAACrQ,EAAQoN,KAAK,CAC3H,CAOA,SAASkD,IACL,IACInP,EAAGiM,EADDmD,EAAgB,IAAI,CAACH,gBAAgB,GAAItN,EAAS,IAAI,CAACA,MAAM,CAAEyL,EAAM,AAACgC,GAAiBA,EAAcpO,MAAM,EAAK,EAEtH,IAAKhB,EAAI,EAAGA,EAAIoN,EAAKpN,IAAK,CAEtB,GAAI,CADJiM,CAAAA,EAAQ,IAAI,CAACA,KAAK,CAACmD,CAAa,CAACpP,EAAE,CAAE2B,CAAM,CAAC3B,EAAE,CAAA,EAClC,CACR2B,EAAOX,MAAM,CAAG,EAChB,MACJ,CACIiL,EAAMM,IAAI,EACVN,EAAMiB,OAAO,GAEjBvL,CAAM,CAAC3B,EAAE,CAAGiM,CAChB,CACA,OAAOtK,CACX,CAWA,SAASsK,EAAMoD,CAAY,CAAEpD,CAAK,EAC9B,GAAIoD,GAAgBA,EAAatN,MAAM,CACnC,OAAOsN,EAEX,GAAI,CAACpD,GAASA,AAAiB,OAAjBA,EAAMlK,MAAM,CACtB,CAAA,GAAIhE,IAA8EM,QAAQ,CAACgR,GACvFpD,EAAQ,IA1PoCF,EA0PV,IAAI,CAAC3M,KAAK,CAAE,IAAI,CAAEiQ,QAEnD,GAAItR,IAA8EuR,QAAQ,CAACD,GAC5FpD,EAAQ,IAAI,CAAC7M,KAAK,CAAC9B,GAAG,CAAC+R,IAAiB,UAEvC,GAAI,AAAwB,YAAxB,OAAOA,EAA6B,CACzC,IAAME,EAAcF,EAAa1R,IAAI,CAACsO,EAAO,IAAI,EACjDA,EAAQsD,EAAYxN,MAAM,CACtBwN,EACA,IAnQwCxD,EAmQd,IAAI,CAAC3M,KAAK,CAAE,IAAI,CAAEiQ,EACpD,CAAA,CAEJ,OAAOpD,CACX,CAKA,SAASuD,EAAoBzK,CAAS,EAClC,IAAI,CAACyJ,aAAa,CAAC1O,OAAO,CAAC,AAACkP,GAAiBA,EAAatQ,MAAM,CAACqG,GACrE,CAKA,SAAS0K,IACL,IAAI,CAACjB,aAAa,CAAC1O,OAAO,CAAC,AAACkP,GAAiBA,EAAazD,MAAM,GACpE,CAeA,SAASmE,EAAUC,CAAc,CAAEzG,CAAE,CAAEC,CAAE,CAAEyG,CAAE,CAAEC,CAAE,EAC7C,GAAI,IAAI,CAACzQ,KAAK,CAACyK,QAAQ,CAAE,CACrB,IAAMD,EAAOV,EACbA,EAAKC,EACLA,EAAKS,CACT,CACA,IAAI,CAACjI,MAAM,CAAC7B,OAAO,CAAC,CAACgQ,EAAQ9P,IAAO,IAAI,CAAC+P,cAAc,CAACJ,EAAgBzG,EAAIC,EAAIyG,EAAIC,EAAI7P,GAAK,IAAI,CACrG,CAmBA,SAAS+P,EAAeJ,CAAc,CAAEzG,CAAE,CAAEC,CAAE,CAAEyG,CAAE,CAAEC,CAAE,CAAE7P,CAAC,EACrD,IAAIiM,EAAQ,IAAI,CAACtK,MAAM,CAAC3B,EAAE,AACtB,AAACiM,CAAAA,EAAMM,IAAI,EACXN,CAAAA,EAAQ,IAAI,CAACtK,MAAM,CAAC3B,EAAE,CAAGgQ,AAjUuBjE,EAiUDC,SAAS,CAACC,EAAK,EAElEA,CAAK,CAAC0D,EAAe,CAACzG,EAAIC,EAAIyG,EAAIC,EACtC,CASA,SAASrF,EAAUd,CAAE,CAAEF,CAAE,EACrB,IAAI,CAACkG,SAAS,CAAC,YAAa,KAAM,KAAMhG,EAAIF,EAChD,CAWA,SAASyG,EAAevG,CAAE,CAAEF,CAAE,CAAExJ,CAAC,EAC7B,IAAI,CAAC+P,cAAc,CAAC,YAAa,KAAM,KAAMrG,EAAIF,EAAIxJ,EACzD,CAlKA9D,EAAc4H,OAAO,CAnBrB,SAAiBoM,CAAkB,EAC/B,IAAMC,EAAeD,EAAmBzS,SAAS,AAC7C,AAAC0S,CAAAA,EAAa5B,gBAAgB,EAC9BxQ,IAA8EmN,KAAK,CAAC,CAAA,EAAMiF,EAAc,CACpG5B,iBAAAA,EACAH,OAAAA,EACAW,qBAAAA,EACAE,iBAAAA,EACAE,WAAAA,EACAlD,MAAAA,EACAuD,oBAAAA,EACAC,oBAAAA,EACAC,UAAAA,EACAK,eAAAA,EACAvF,UAAAA,EACAyF,eAAAA,CACJ,EAER,CAoKJ,EAAG/T,GAAkBA,CAAAA,EAAgB,CAAC,CAAA,GAMT,IAAMkU,EAA6BlU,EAW1D,CAAEgP,MAAOmF,CAAkB,CAAE,CAAItS,GAWvC,OAAMuS,EAMFjF,YAAY1M,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAEsO,CAAQ,CAAE,CAC9C,IAAI,CAAC5R,UAAU,CAAGA,EAClB,IAAI,CAACS,KAAK,CAAGT,EAAWS,KAAK,CAC7B,IAAI,CAACoR,UAAU,CAAID,AAAa,UAAbA,EAAuB,SAAW,SACrD,IAAI,CAAC/B,aAAa,CAAG,EAAE,CACvB,IAAI,CAAC3P,OAAO,CAAGA,EACf,IAAI,CAAC8C,MAAM,CAAG,EAAE,CAChB,IAAI,CAACM,KAAK,CAAGA,EACb,IAAI,CAACsO,QAAQ,CAAGA,EAChB,IAAI,CAACE,IAAI,CAAC9R,EAAYE,EAASoD,EACnC,CAUAhD,KAEA,GAAGyR,CAAK,CAAE,CACN,IAAI,CAAC1R,OAAO,CAACC,IAAI,CAAC0E,KAAK,CAAC,IAAI,CAAC3E,OAAO,CAAE6E,UAC1C,CASA8M,iBAAiB9R,CAAO,CAAE,CACtB,IACI5B,EAAK2T,EADHC,EAAM,IAAI,CAACxF,WAAW,CAACyF,QAAQ,CAAEC,EAAQ,CAAC,EAAGrI,EAAa,IAAI,CAACtJ,KAAK,CAACsJ,UAAU,CAErF,IAAKzL,KAAO4B,EACR+R,EAAYC,CAAG,CAAC5T,EAAI,CAChB,AAAoB,KAAA,IAAb4T,CAAG,CAAC5T,EAAI,EACd,AAACyL,GACE,AAC4B,KAD5B,CAAC,OAAQ,SAAU,eAAe,CAC7BH,OAAO,CAACqI,IACjBG,CAAAA,CAAK,CAACH,EAAU,CAAG/R,CAAO,CAAC5B,EAAI,AAAD,EAGtC,OAAO8T,CACX,CAKA5Q,SAAU,CACF,IAAI,CAACnB,OAAO,EACZ,CAAA,IAAI,CAACA,OAAO,CAAG,IAAI,CAACA,OAAO,CAACmB,OAAO,EAAC,EAEpC,IAAI,CAAC6Q,OAAO,EACZ,CAAA,IAAI,CAACA,OAAO,CAAG,IAAI,CAACA,OAAO,CAAC7Q,OAAO,EAAC,EAExC,IAAI,CAAC4O,oBAAoB,EAC7B,CAKA0B,KAAK9R,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE,CAC7B,IAAI,CAACtD,UAAU,CAAGA,EAClB,IAAI,CAACS,KAAK,CAAGT,EAAWS,KAAK,CAC7B,IAAI,CAACP,OAAO,CAAGA,EACf,IAAI,CAAC8C,MAAM,CAAG,EAAE,CAChB,IAAI,CAAC6M,aAAa,CAAG,EAAE,CACvB,IAAI,CAACvM,KAAK,CAAGA,EACb,IAAI,CAACkN,UAAU,GACf,IAAI,CAACZ,gBAAgB,EACzB,CAKA7P,OAAOqG,CAAS,CAAE,CACd,IAAI,CAACyK,mBAAmB,CAACzK,EAC7B,CAKAwG,OAEA0F,CAAY,CAAE,CACN,IAAI,CAACpS,OAAO,CAAC2G,SAAS,EAAI,IAAI,CAACxG,OAAO,EACtC,IAAI,CAACA,OAAO,CAACkS,QAAQ,CAAC,IAAI,CAACrS,OAAO,CAAC2G,SAAS,EAEhD,IAAI,CAACiK,mBAAmB,EAC5B,CAUAhC,OAAOvE,CAAE,CAAEC,CAAE,CAAEuE,CAAO,CAAE,CACpB,IAAI,CAACgC,SAAS,CAAC,SAAUxG,EAAIC,EAAIuE,EACrC,CAaAK,MAAM7E,CAAE,CAAEC,CAAE,CAAEa,CAAE,CAAEC,CAAE,CAAE,CAClB,IAAI,CAACyF,SAAS,CAAC,QAASxG,EAAIC,EAAIa,EAAIC,EACxC,CAKAkH,2BAA2BrM,CAAO,CAAE,CAChC,IAAI,CAAC0J,aAAa,CAAC1O,OAAO,CAAC,AAACkP,IACxBA,EAAaxD,aAAa,CAAC1G,EAC/B,EACJ,CAOAsM,eAAgB,CACZ,MAAO,CAAC,CAAC,IAAI,CAACzP,MAAM,CAACX,MAAM,AAC/B,CAaAqQ,eAAe3H,CAAE,CAAEF,CAAE,CAAE8H,CAAoB,CAAE,CACzC,IAAMlS,EAAQ,IAAI,CAACT,UAAU,CAACS,KAAK,CAEnCiH,EAAe,IAAI,CAAC1H,UAAU,CAACF,WAAW,CAE1C8S,EAAkBnS,EAAMN,WAAW,CAACyJ,OAAO,CAAC,IAAI,CAAC5J,UAAU,EAAG6S,EAAepS,EAAMP,OAAO,CAACC,WAAW,CAACyS,EAAgB,CACvH,IAAI,CAACtB,cAAc,CAACvG,EAAIF,EAAI,GACxB8H,GACA,IAAI,CAACrB,cAAc,CAACvG,EAAIF,EAAI,GAKhCgI,CAAY,CAAC,IAAI,CAAChB,UAAU,CAAC,CAAC,IAAI,CAACvO,KAAK,CAAC,CACpCgK,KAAK,CAAG,IAAI,CAACpN,OAAO,CAACoN,KAAK,CAC/B5F,CAAY,CAAC,IAAI,CAACmK,UAAU,CAAC,CAAC,IAAI,CAACvO,KAAK,CAAC,CACpCgK,KAAK,CAAG,IAAI,CAACpN,OAAO,CAACoN,KAAK,AACnC,CAKAR,OAAOgG,CAAU,CAAE,CACf,IAAM9S,EAAa,IAAI,CAACA,UAAU,CAAEE,EAAUwR,EAAmB,CAAA,EAAM,IAAI,CAACxR,OAAO,CAAE4S,GAAaC,EAAc,IAAI,CAAC1S,OAAO,CAAC0S,WAAW,CAAEC,EAAc,IAAI,CAACtG,WAAW,CACxK,IAAI,CAAClL,OAAO,GAEZkQ,EAAmB,CAAA,EAAM,IAAI,CADL,IAAIsB,EAAYhT,EAAYE,EAAS,IAAI,CAACoD,KAAK,CAAE,IAAI,CAACsO,QAAQ,GAEtF,IAAI,CAAChF,MAAM,CAACmG,GACZ,IAAI,CAAChT,MAAM,EACf,CACJ,CACA0R,EAA0BtM,OAAO,CAACwM,GAML,IAAMsB,EAA8BtB,EAoJ3D,CAAEuB,eAAgBC,CAA+B,CAAE,CAd5B,CACzBD,eApDmB,CAInBE,MAAO,CACHC,QAAS,SACTC,WAAY,CACR7O,GAAI,QACJ8O,KAAM,EACNC,KAAM,EACNC,YAAa,GACbC,aAAc,EAClB,EAIAC,SAAU,CAAC,CACHN,QAAS,OACTC,WAAY,CACRnV,EAAG,wBACH,eAAgB,CACpB,CACJ,EAAE,AACV,EAIA,gBAAiB,CACbkV,QAAS,SACTC,WAAY,CACR7O,GAAI,gBACJ8O,KAAM,EACNC,KAAM,EACNC,YAAa,GACbC,aAAc,EAClB,EACAC,SAAU,CAAC,CACHN,QAAS,OACTC,WAAY,CAERnV,EAAG,yBACH,eAAgB,CACpB,CACJ,EAAE,AACV,CACJ,CAQA,EAeM,CAAEkB,SAAUuU,CAAyB,CAAE3N,QAAS4N,EAAwB,CAAEC,OAAAA,EAAM,CAAEvH,MAAOwH,EAAsB,CAAEC,UAAAA,EAAS,CAAE,CAAI5U,IAMhI6U,GAAkBC,GAAmB,cACrCC,GAAoBD,GAAmB,gBAEvCE,GAAe,oBAAuB,CAAA,AAAChV,IAA+EiV,GAAG,CAAG,KAAS,IAAI,EAAK,IASpJ,SAASH,GAAmBI,CAAU,EAClC,OAAO,SAAUC,CAAK,EAClB,IAAI,CAACjU,IAAI,CAACgU,EAAY,QAAUC,EAAQ,IAC5C,CACJ,CAIA,SAASC,KACL,IAAI,CAACtU,OAAO,CAACuU,IAAI,CAAGV,GAAuBZ,EAAiC,IAAI,CAACjT,OAAO,CAACuU,IAAI,EAAI,CAAC,EAYtG,CAIA,SAASC,GAAqBjQ,CAAE,CAAEkQ,CAAa,EAC3C,IAAMzU,EAAU,CAAEoT,WAAY,CAAE7O,GAAAA,CAAG,CAAE,EAC/B2N,EAAQ,CACVzK,OAAQgN,EAAcpN,KAAK,EAAI,OAC/BM,KAAM8M,EAAcpN,KAAK,EAAI,qBACjC,CACArH,CAAAA,EAAQyT,QAAQ,CAAIgB,EAAchB,QAAQ,EACtCgB,EAAchB,QAAQ,CAACzB,GAAG,CAAC,SAAU0C,CAAK,EACtC,OAAOb,GAAuB3B,EAAOwC,EACzC,GACJ,IAAMC,EAAMd,GAAuB,CAAA,EAAM,CACrCT,WAAY,CACRG,YAAa,GACbC,aAAc,GACdF,KAAM,EACND,KAAM,EACNuB,OAAQ,MACZ,CACJ,EAAGH,EAAezU,GACZ6U,EAAS,IAAI,CAAC1W,UAAU,CAACwW,GAE/B,OADAE,EAAOtQ,EAAE,CAAGA,EACLsQ,CACX,CAwBA,MAAMC,WAAyB/B,EAM3B,OAAO9N,QAAQE,CAAU,CAAE4P,CAAgB,CAAE,CACzC,IAAMC,EAAmBD,EAAiBnW,SAAS,AAC9CoW,CAAAA,EAAiBC,SAAS,GAC3BvB,EAA0BvO,EAAY,oBAAqBmP,IAC3DU,EAAiBC,SAAS,CAAGT,GAErC,CAMAhI,YAAY1M,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE,CACpC,KAAK,CAACtD,EAAYE,EAASoD,EAAO,SAMlC,IAAI,CAACyC,IAAI,CAAG,MAChB,CAYAqP,KAAM,CACF,IAAMC,EAAU,IAAI,CAACnV,OAAO,CAAC/B,CAAC,CAC9B,GAAIkX,EACA,MAAO,AAAmB,YAAnB,OAAOA,EACVA,EAAQrW,IAAI,CAAC,IAAI,EACjBqW,EAER,IAAMrS,EAAS,IAAI,CAACA,MAAM,CAAEyL,EAAMzL,EAAOX,MAAM,CAAElE,EAAI,EAAE,CACnDmX,EAAW7G,EAAKnB,EAAQtK,CAAM,CAAC,EAAE,CAAEuS,EAAWD,GAAY,IAAI,CAAC7F,MAAM,CAACnC,GAAO6C,gBAAgB,CAAEqF,EAAa,EAAGnH,EACnH,GAAIkH,EAEA,IADApX,EAAEiC,IAAI,CAAC,CAAC,IAAKmV,EAASrS,CAAC,CAAEqS,EAASxO,CAAC,CAAC,EAC7B,EAAEyO,EAAa/G,GAAO6G,GAEzBjH,EAAUf,AADVA,CAAAA,EAAQtK,CAAM,CAACwS,EAAW,AAAD,EACTnH,OAAO,EAAI,IAC3BkH,EAAW,IAAI,CAAC9F,MAAM,CAACnC,GAAO6C,gBAAgB,CAC9B,MAAZ9B,GAGKA,AAAY,MAAZA,EACLlQ,EAAEiC,IAAI,CAAC,CAACiO,EAASkH,EAASrS,CAAC,CAAEqS,EAASxO,CAAC,CAAC,EAEnCsH,AAAY,MAAZA,GACLlQ,EAAEiC,IAAI,CAAC,CAACiO,EAAQ,EAEpBiH,EAAWhI,EAAMlK,MAAM,CAAC+C,OAAO,CAGvC,OAAQmP,GAAY,IAAI,CAACjV,OAAO,CAC5B,IAAI,CAACI,KAAK,CAACE,QAAQ,CAAC8U,SAAS,CAACtX,EAAG,IAAI,CAACkC,OAAO,CAACuH,WAAW,IACzD,IACR,CACA6K,eAAgB,CACZ,OAAO,KAAK,CAACA,iBAAmB,CAAC,CAAC,IAAI,CAACvS,OAAO,CAAC/B,CAAC,AACpD,CACAyO,OAAO8I,CAAM,CAAE,CACX,IAAMxV,EAAU,IAAI,CAACA,OAAO,CAAEkS,EAAQ,IAAI,CAACJ,gBAAgB,CAAC9R,EAC5D,CAAA,IAAI,CAACG,OAAO,CAAG,IAAI,CAACL,UAAU,CAACS,KAAK,CAACE,QAAQ,CACxCgV,IAAI,CAAC,CAAC,CAAC,IAAK,EAAG,EAAE,CAAC,EAClBrV,IAAI,CAAC8R,GACLlR,GAAG,CAACwU,GACT,IAAI,CAACrD,OAAO,CAAG,IAAI,CAACrS,UAAU,CAACS,KAAK,CAACE,QAAQ,CACxCgV,IAAI,CAAC,CAAC,CAAC,IAAK,EAAG,EAAE,CAAC,EAClBpD,QAAQ,CAAC,2BACTjS,IAAI,CAAC,CACNU,OAAQ,CACZ,GACKE,GAAG,CAACwU,GACL,AAAC,IAAI,CAAC1V,UAAU,CAACS,KAAK,CAACsJ,UAAU,EACjC,IAAI,CAACsI,OAAO,CAAC/R,IAAI,CAAC,CACd,kBAAmB,QACnBqH,OAAQyM,GACRvM,KAAMuM,GACN,eAAgB,IAAI,CAAC/T,OAAO,CAACuH,WAAW,GACpC1H,AAAe,EAAfA,EAAQ6H,IAAI,AACpB,GAEJ,KAAK,CAAC6E,SACNkH,GAAO,IAAI,CAACzT,OAAO,CAAE,CAAE8T,kBAAAA,GAAmBF,gBAAAA,EAAgB,GAC1D,IAAI,CAAC2B,UAAU,CAAC,IAAI,CACxB,CACA7V,OAAOqG,CAAS,CAAE,CACd,GAAI,IAAI,CAAC/F,OAAO,CAAE,CACd,IAAMlC,EAAI,IAAI,CAACiX,GAAG,GAAIS,EAASzP,EAAY,UAAY,OACnDjI,GACA,IAAI,CAACkC,OAAO,CAACwV,EAAO,CAAC,CAAE1X,EAAGA,CAAE,GAC5B,IAAI,CAACkU,OAAO,CAACwD,EAAO,CAAC,CAAE1X,EAAGA,CAAE,KAG5B,IAAI,CAACkC,OAAO,CAACC,IAAI,CAAC,CAAEnC,EAAG,iBAAc,GACrC,IAAI,CAACkU,OAAO,CAAC/R,IAAI,CAAC,CAAEnC,EAAG,iBAAc,IAEzC,IAAI,CAACkC,OAAO,CAACyV,MAAM,CAAG,IAAI,CAACzD,OAAO,CAACyD,MAAM,CAAG,CAAC,CAAC3X,CAClD,CACA,KAAK,CAAC4B,OAAOqG,EACjB,CAMAwP,WAAWG,CAAI,CAAE,CACb,IAAMC,EAAcD,EAAK7V,OAAO,CAAEO,EAAQsV,EAAKtV,KAAK,CAAEgU,EAAOhU,EAAMP,OAAO,CAACuU,IAAI,CAAE5M,EAAOmO,EAAYnO,IAAI,CAAEN,EAAQsM,GAAyBhM,IAASA,AAAS,SAATA,EAChJA,EACAmO,EAAYrO,MAAM,CAuBtB,CAAC,cAAe,YAAY,CACvBxG,OAAO,CAvBM,SAAUmT,CAAU,EAClC,IACI2B,EAAKC,EAAkB5X,EAAKyW,EAD1BoB,EAAWH,CAAW,CAAC1B,EAAW,CAExC,GAAI6B,EAAU,CACV,IAAK7X,KAAOmW,EAER,GAAI,AAAC0B,CAAAA,IAAcF,CAAAA,AADnBA,CAAAA,EAAMxB,CAAI,CAACnW,EAAI,AAAD,EACSgV,UAAU,EAAI2C,EAAI3C,UAAU,CAAC7O,EAAE,AAAD,GAGjD0R,IAAaF,EAAIxR,EAAE,AAAD,GAClBwR,AAAgB,WAAhBA,EAAI5C,OAAO,CAAe,CAC1B6C,EAAmBD,EACnB,KACJ,CAEAC,IACAnB,EAASgB,CAAI,CAACzB,EAAW,CAAG7T,EAAME,QAAQ,CACrCwU,SAAS,CAAC,AAACa,CAAAA,EAAYvR,EAAE,EAAIuP,IAAU,EAAK,IAAMmC,EAAUpC,GAAuBmC,EAAkB,CAAE3O,MAAOA,CAAM,IACzHwO,EAAKzV,IAAI,CAACgU,EAAYS,EAAOqB,YAAY,CAAC,OAElD,CACJ,EAGJ,CACJ,CAYApB,GAAiB7C,QAAQ,CAAG,CACxBkE,UAAW,YACXzO,YAAa,eACbD,OAAQ,SACRE,KAAM,OACN7G,OAAQ,QACZ,EAkBA,GAAM,CAAEuL,MAAO+J,EAAsB,CAAE,CAAIlX,GAwB3C,OAAMmX,WAAyBtD,EAM3BvG,YAAY1M,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE,CACpC,KAAK,CAACtD,EAAYE,EAASoD,EAAO,SAMlC,IAAI,CAACyC,IAAI,CAAG,OACZ,IAAI,CAAC8F,SAAS,CAAG,KAAK,CAAC6G,cAC3B,CAMA9F,OAAO8I,CAAM,CAAE,CACX,IAAMtD,EAAQ,IAAI,CAACJ,gBAAgB,CAAC,IAAI,CAAC9R,OAAO,CAChD,CAAA,IAAI,CAACG,OAAO,CAAG,IAAI,CAACL,UAAU,CAACS,KAAK,CAACE,QAAQ,CACxC6V,IAAI,CAAC,EAAG,KAAM,EAAG,GACjBlW,IAAI,CAAC8R,GACLlR,GAAG,CAACwU,GACT,KAAK,CAAC9I,QACV,CACA7M,OAAOqG,CAAS,CAAE,CACd,GAAI,IAAI,CAAC/F,OAAO,CAAE,CACd,IAAMkV,EAAW,IAAI,CAAC9F,MAAM,CAAC,IAAI,CAACzM,MAAM,CAAC,EAAE,EAAEmN,gBAAgB,CACzDoF,EACA,IAAI,CAAClV,OAAO,CAAC+F,EAAY,UAAY,OAAO,CAAC,CACzClD,EAAGqS,EAASrS,CAAC,CACb6D,EAAGwO,EAASxO,CAAC,CACbsB,MAAO,IAAI,CAACnI,OAAO,CAACmI,KAAK,CACzBF,OAAQ,IAAI,CAACjI,OAAO,CAACiI,MAAM,AAC/B,GAGA,IAAI,CAAC7H,IAAI,CAAC,CACN4C,EAAG,EACH6D,EAAG,IACP,GAEJ,IAAI,CAAC1G,OAAO,CAACyV,MAAM,CAAGW,CAAAA,CAAQlB,CAClC,CACA,KAAK,CAACxV,OAAOqG,EACjB,CACJ,CAWAmQ,GAAiBpE,QAAQ,CAAGmE,GAAuBI,AAjGkB1B,GAiGa7C,QAAQ,CAAE,CACxF9J,MAAO,QACPF,OAAQ,QACZ,GAkBA,GAAM,CAAEoE,MAAOoK,EAAwB,CAAE,CAAIvX,GAmB7C,OAAMwX,WAA2B3D,EAM7BvG,YAAY1M,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE,CACpC,KAAK,CAACtD,EAAYE,EAASoD,EAAO,SAMlC,IAAI,CAACyC,IAAI,CAAG,SACZ,IAAI,CAAC8F,SAAS,CAAG,KAAK,CAAC6G,cAC3B,CASA3S,OAAOqG,CAAS,CAAE,CACd,GAAI,IAAI,CAAC/F,OAAO,CAAE,CACd,IAAMkV,EAAW,IAAI,CAAC9F,MAAM,CAAC,IAAI,CAACzM,MAAM,CAAC,EAAE,EAAEmN,gBAAgB,CACzDoF,EACA,IAAI,CAAClV,OAAO,CAAC+F,EAAY,UAAY,OAAO,CAAC,CACzClD,EAAGqS,EAASrS,CAAC,CACb6D,EAAGwO,EAASxO,CAAC,CACbe,EAAG,IAAI,CAAC5H,OAAO,CAAC4H,CAAC,AACrB,GAGA,IAAI,CAACzH,OAAO,CAACC,IAAI,CAAC,CACd4C,EAAG,EACH6D,EAAG,IACP,GAEJ,IAAI,CAAC1G,OAAO,CAACyV,MAAM,CAAG,CAAC,CAACP,CAC5B,CACA,KAAK,CAACxV,OAAOf,IAAI,CAAC,IAAI,CAAEoH,EAC5B,CAIAwG,OAAO8I,CAAM,CAAE,CACX,IAAMtD,EAAQ,IAAI,CAACJ,gBAAgB,CAAC,IAAI,CAAC9R,OAAO,CAChD,CAAA,IAAI,CAACG,OAAO,CAAG,IAAI,CAACL,UAAU,CAACS,KAAK,CAACE,QAAQ,CACxCkW,MAAM,CAAC,EAAG,KAAM,GAChBvW,IAAI,CAAC8R,GACLlR,GAAG,CAACwU,GACT,KAAK,CAAC9I,QACV,CAOAkK,UAAUhP,CAAC,CAAE,CACT,IAAI,CAAC5H,OAAO,CAAC4H,CAAC,CAAGA,CACrB,CACJ,CAaA8O,GAAmBzE,QAAQ,CAAGwE,GAAyBD,AAtNc1B,GAsNiB7C,QAAQ,CAAE,CAAErK,EAAG,GAAI,GAoBzG,GAAM,CAAEyE,MAAOwK,EAAyB,CAAE9Q,QAAS+Q,EAA2B,CAAE,CAAI5X,GAmBpF,OAAM6X,WAA4BhE,EAM9BvG,YAAY1M,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE,CACpC,KAAK,CAACtD,EAAYE,EAASoD,EAAO,SAMlC,IAAI,CAACyC,IAAI,CAAG,SAChB,CASA+L,KAAK9R,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE,CACzB0T,GAA4B9W,EAAQqN,KAAK,GACzCrN,EAAQ8C,MAAM,CAAC7B,OAAO,CAAC,AAACmM,IACpBA,EAAMC,KAAK,CAAGrN,EAAQqN,KAAK,AAC/B,GAEAyJ,GAA4B9W,EAAQmD,KAAK,GACzCnD,EAAQ8C,MAAM,CAAC7B,OAAO,CAAC,AAACmM,IACpBA,EAAMjK,KAAK,CAAGnD,EAAQmD,KAAK,AAC/B,GAEJ,KAAK,CAACyO,KAAK9R,EAAYE,EAASoD,EACpC,CAOAsJ,OAAO8I,CAAM,CAAE,CACX,IAAI,CAACrV,OAAO,CAAG,IAAI,CAACL,UAAU,CAACS,KAAK,CAACE,QAAQ,CAACuW,aAAa,CAAC,WACvD5W,IAAI,CAAC,IAAI,CAAC0R,gBAAgB,CAAC,IAAI,CAAC9R,OAAO,GACvCgB,GAAG,CAACwU,GACT,KAAK,CAAC9I,QACV,CAKAf,UAAUd,CAAE,CAAEF,CAAE,CAAE,CACd,KAAK,CAAC6H,eAAe3H,EAAIF,EAAI,CAAA,EACjC,CAaAsM,oBAAoBC,CAAM,CAAEC,CAAM,CAAEC,CAAE,CAAEC,CAAE,CAAE,CACxC,OAAO3T,KAAK4T,GAAG,CAAC,AAACH,CAAAA,EAAOtQ,CAAC,CAAGqQ,EAAOrQ,CAAC,AAADA,EAAKuQ,EAAK,AAACD,CAAAA,EAAOnU,CAAC,CAAGkU,EAAOlU,CAAC,AAADA,EAAKqU,EACjEF,EAAOnU,CAAC,CAAGkU,EAAOrQ,CAAC,CAAGsQ,EAAOtQ,CAAC,CAAGqQ,EAAOlU,CAAC,EAAIU,KAAK6T,IAAI,CAAC,AAACJ,CAAAA,EAAOtQ,CAAC,CAAGqQ,EAAOrQ,CAAC,AAADA,EAAMsQ,CAAAA,EAAOtQ,CAAC,CAAGqQ,EAAOrQ,CAAC,AAADA,EAClG,AAACsQ,CAAAA,EAAOnU,CAAC,CAAGkU,EAAOlU,CAAC,AAADA,EAAMmU,CAAAA,EAAOnU,CAAC,CAAGkU,EAAOlU,CAAC,AAADA,EACnD,CAUAwU,SAASnC,CAAQ,CAAEoC,CAAS,CAAE,CAC1B,IAAMC,EAAKrC,EAASrS,CAAC,CAAE2U,EAAKtC,EAASxO,CAAC,CAAE+Q,EAAKH,EAAUzU,CAAC,CAAE6U,EAAKJ,EAAU5Q,CAAC,CAAEwD,EAAK,AAACqN,CAAAA,EAAKE,CAAC,EAAK,EAAuBE,EAAKpU,KAAK6T,IAAI,CAAC,AAACG,CAAAA,EAAKE,CAAC,EAAMF,CAAAA,EAAKE,CAAC,EAAK,EAAI,AAACD,CAAAA,EAAKE,CAAC,EAAMF,CAAAA,EAAKE,CAAC,EAAK,GACnLE,EAAQrU,AAAiB,IAAjBA,KAAKsU,IAAI,CAD4K,AAACH,CAAAA,EAAKF,CAAC,EAAMC,CAAAA,EAAKF,CAAC,GACjLhU,KAAKuU,EAAE,CAK1C,OAJI5N,EAAKqN,GACLK,CAAAA,GAAS,GAAE,EAGR,CAAE1N,GAAAA,EAAIC,GANwF,AAACqN,CAAAA,EAAKE,CAAC,EAAK,EAMhGC,GAAAA,EAAII,GADV,IAAI,CAACC,KAAK,GACIJ,MAAAA,CAAM,CACnC,CAKAI,OAAQ,CACJ,IAAM9K,EAAQ,IAAI,CAAC+K,QAAQ,GAC3B,OAAOtB,GAA4BzJ,GAC/B3J,KAAK4T,GAAG,CAACjK,EAAMmB,QAAQ,CAAC,IAAI,CAACxO,OAAO,CAACkY,EAAE,EAAI7K,EAAMmB,QAAQ,CAAC,IAC1D,IAAI,CAACxO,OAAO,CAACkY,EAAE,AACvB,CAKAE,UAAW,CACP,IAAMC,EAAa,IAAI,CAACrY,OAAO,CAACqN,KAAK,CACrC,OAAO,IAAI,CAAC9M,KAAK,CAAC8M,KAAK,CAACgL,EAAW,AACvC,CAOAC,oBAAoBlL,CAAK,CAAE,CACvB,OAAO,IAAI,CAACmC,MAAM,CAACnC,GAAO6C,gBAAgB,AAC9C,CAOApQ,OAAOqG,CAAS,CAAE,CACd,GAAI,IAAI,CAAC/F,OAAO,CAAE,CACd,IAAMkV,EAAW,IAAI,CAACiD,mBAAmB,CAAC,IAAI,CAACxV,MAAM,CAAC,EAAE,EAAG2U,EAAY,IAAI,CAACa,mBAAmB,CAAC,IAAI,CAACxV,MAAM,CAAC,EAAE,EAAGoP,EAAQ,IAAI,CAACsF,QAAQ,CAACnC,EAAUoC,GAC7IpC,EACA,IAAI,CAAClV,OAAO,CAAC+F,EAAY,UAAY,OAAO,CAAC,CACzCmE,GAAI6H,EAAM7H,EAAE,CACZC,GAAI4H,EAAM5H,EAAE,CACZwN,GAAI5F,EAAM4F,EAAE,CACZI,GAAIhG,EAAMgG,EAAE,CACZK,SAAUrG,EAAM6F,KAAK,CACrBS,gBAAiBtG,EAAM7H,EAAE,CACzBoO,gBAAiBvG,EAAM5H,EAAE,AAC7B,GAGA,IAAI,CAACnK,OAAO,CAACC,IAAI,CAAC,CACd4C,EAAG,EACH6D,EAAG,IACP,GAEJ,IAAI,CAAC1G,OAAO,CAACyV,MAAM,CAAGW,CAAAA,CAAQlB,CAClC,CACA,KAAK,CAACxV,OAAOqG,EACjB,CAOAwS,WAAWR,CAAE,CAAE,CACX,IAAMtM,EAAS,IAAI,CAAC9L,UAAU,CAACF,WAAW,CAACgM,MAAM,AACjD,CAAA,IAAI,CAAC5L,OAAO,CAACkY,EAAE,CAAGA,EACdtM,GAAUA,CAAM,CAAC,EAAE,GACnBA,CAAM,CAAC,EAAE,CAACsM,EAAE,CAAGA,EACftM,CAAM,CAAC,EAAE,CAACsM,EAAE,CAAGA,EAEvB,CACJ,CAaAnB,GAAoB9E,QAAQ,CAAG4E,GAA0BL,AA3aY1B,GA2amB7C,QAAQ,CAAE,CAC9FiG,GAAI,IACR,GASA,IAAIS,GAAmHjb,EAAoB,KACvIkb,GAAuIlb,EAAoBI,CAAC,CAAC6a,IAUjK,GAAM,CAAEE,OAAAA,EAAM,CAAE,CAAID,KAGd,CAAEhF,OAAQkF,EAAwB,CAAEC,eAAAA,EAAc,CAAEC,SAAAA,EAAQ,CAAEvZ,KAAMwZ,EAAsB,CAAE,CAAI/Z,IAUtG,SAASga,GAAgBlW,CAAC,CAAE6D,CAAC,CAAEsS,CAAC,CAAEC,CAAC,CAAEpZ,CAAO,EACxC,IAAMqZ,EAAUrZ,GAAWA,EAAQqZ,OAAO,CAAEC,EAAUtZ,GAAWA,EAAQsZ,OAAO,CAC5E7D,EAAM8D,EAASC,EAAUL,EAAI,EA4BjC,OA3BIH,GAASK,IAAYL,GAASM,KAC9B7D,EAAO,CAAC,CAAC,IAAK4D,EAASC,EAAQ,CAAC,CAG5BC,AADJA,CAAAA,EAAU1S,EAAIyS,CAAM,EACN,GACVC,CAAAA,EAAU,CAACH,EAAIG,CAAM,EAErBA,EAAUJ,GACVK,CAAAA,EAAUH,EAAUrW,EAAKmW,EAAI,EAAKI,EAAUJ,EAAII,CAAM,EAGtDD,EAAUzS,EAAIuS,EACd3D,EAAKvV,IAAI,CAAC,CAAC,IAAK8C,EAAIwW,EAAS3S,EAAIuS,EAAE,EAG9BE,EAAUzS,EACf4O,EAAKvV,IAAI,CAAC,CAAC,IAAK8C,EAAIwW,EAAS3S,EAAE,EAG1BwS,EAAUrW,EACfyS,EAAKvV,IAAI,CAAC,CAAC,IAAK8C,EAAG6D,EAAIuS,EAAI,EAAE,EAGxBC,EAAUrW,EAAImW,GACnB1D,EAAKvV,IAAI,CAAC,CAAC,IAAK8C,EAAImW,EAAGtS,EAAIuS,EAAI,EAAE,GAGlC3D,GAAQ,EAAE,AACrB,CAsBA,MAAMgE,WAA0B1G,EAkB5B,OAAO2G,gBAAgBC,CAAY,CAAE9J,CAAG,CAAE,CACtC,MAAO,CACH7M,EAAGU,KAAKkW,KAAK,CAAC,AAAC/J,CAAAA,EAAI7M,CAAC,EAAI,CAAA,EAAM2W,CAAAA,EAAa3W,CAAC,EAAI,CAAA,EAC5C,AAAC6M,CAAAA,EAAI1H,KAAK,CAAIwR,CAAAA,EAAaxR,KAAK,EAAI,CAAA,CAAC,EACjC4Q,GAAeY,EAAatT,KAAK,GACzCQ,EAAGnD,KAAKkW,KAAK,CAAC,AAAC/J,CAAAA,EAAIhJ,CAAC,EAAI,CAAA,EAAM8S,CAAAA,EAAa9S,CAAC,EAAI,CAAA,EAC5C,AAACgJ,CAAAA,EAAI5H,MAAM,CAAI0R,CAAAA,EAAa1R,MAAM,EAAI,CAAA,CAAC,EACnC8Q,GAAeY,EAAapS,aAAa,EACrD,CACJ,CACA,OAAOtC,QAAQ8P,CAAgB,CAAE,CAE7B8E,AADgB9E,EAAiBnW,SAAS,CAACib,OAAO,CAC1CC,SAAS,CAAGZ,EACxB,CAOA,OAAOa,iBAAiBxZ,CAAK,CAAEoC,CAAK,CAAEgX,CAAY,CAAEK,CAAS,CAAE,CAC3D,IAYIC,EAZE5T,EAAQsT,EAAatT,KAAK,CAAEkB,EAAgBoS,EAAapS,aAAa,CAAER,EAAUpE,EAAMkN,GAAG,CAAG,EAAKlN,EAAMoE,OAAO,EAAI,EAAImT,EAAOvX,EAAMwX,OAAO,GAElJna,EAAU,CACNqG,MAAOA,EACPkB,cAAeA,EACfvE,EAAG2W,EAAa3W,CAAC,CACjB6D,EAAG8S,EAAa9S,CAAC,CACjBsB,MAAOxF,EAAMwF,KAAK,CAClBF,OAAQtF,EAAMsF,MAAM,AACxB,EAEAjF,EAAI,AAACgX,CAAAA,EAAUhX,CAAC,EAAI,CAAA,EAAKzC,EAAMgL,QAAQ,CAAE1E,EAAI,AAACmT,CAAAA,EAAUnT,CAAC,EAAI,CAAA,EAAKtG,EAAMiL,OAAO,CA0C/E,MAvCAyO,CAAAA,EAAMjX,EAAI+D,CAAM,EACN,IACFV,AAAU,UAAVA,EACArG,EAAQqG,KAAK,CAAG,OAGhBrG,EAAQgD,CAAC,CAAG,AAAChD,CAAAA,EAAQgD,CAAC,EAAI,CAAA,EAAKiX,GAIvCA,CAAAA,EAAMjX,EAAIkX,EAAK/R,KAAK,CAAGpB,CAAM,EACnBxG,EAAMoN,SAAS,GACjBtH,AAAU,SAAVA,EACArG,EAAQqG,KAAK,CAAG,QAGhBrG,EAAQgD,CAAC,CAAG,AAAChD,CAAAA,EAAQgD,CAAC,EAAI,CAAA,EAAKzC,EAAMoN,SAAS,CAAGsM,GAIzDA,CAAAA,EAAMpT,EAAIE,CAAM,EACN,IACFQ,AAAkB,WAAlBA,EACAvH,EAAQuH,aAAa,CAAG,MAGxBvH,EAAQ6G,CAAC,CAAG,AAAC7G,CAAAA,EAAQ6G,CAAC,EAAI,CAAA,EAAKoT,GAIvCA,CAAAA,EAAMpT,EAAIqT,EAAKjS,MAAM,CAAGlB,CAAM,EACpBxG,EAAMqN,UAAU,GAClBrG,AAAkB,QAAlBA,EACAvH,EAAQuH,aAAa,CAAG,SAGxBvH,EAAQ6G,CAAC,CAAG,AAAC7G,CAAAA,EAAQ6G,CAAC,EAAI,CAAA,EAAKtG,EAAMqN,UAAU,CAAGqM,GAGnDja,CACX,CAMAwM,YAAY1M,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE,CACpC,KAAK,CAACtD,EAAYE,EAASoD,EAAO,QACtC,CAaAgO,eAAevG,CAAE,CAAEF,CAAE,CAAE,CACnB,KAAK,CAACyG,eAAevG,EAAIF,EAAI,EACjC,CAOAgB,UAAUd,CAAE,CAAEF,CAAE,CAAE,CACd,IAAMpK,EAAQ,IAAI,CAACT,UAAU,CAACS,KAAK,CAEnCiC,EAAe,IAAI,CAAC1C,UAAU,CAACF,WAAW,CAE1C8S,EAAkBnS,EAAMN,WAAW,CAACyJ,OAAO,CAAC,IAAI,CAAC5J,UAAU,EAAiD6S,EAAeyH,AAA1C7Z,EAAMP,OAAO,CAACC,WAAW,AAAiC,CAACyS,EAAgB,CAC5J,GAAInS,EAAMyK,QAAQ,CAAE,CAChB,IAAMD,EAAOF,EACbA,EAAKF,EACLA,EAAKI,CACT,CAEA,IAAI,CAAC/K,OAAO,CAACgD,CAAC,EAAI6H,EAClB,IAAI,CAAC7K,OAAO,CAAC6G,CAAC,EAAI8D,EAElBgI,CAAY,CAAC,IAAI,CAAChB,UAAU,CAAC,CAAC,IAAI,CAACvO,KAAK,CAAC,CAACJ,CAAC,CAAG,IAAI,CAAChD,OAAO,CAACgD,CAAC,CAC5D2P,CAAY,CAAC,IAAI,CAAChB,UAAU,CAAC,CAAC,IAAI,CAACvO,KAAK,CAAC,CAACyD,CAAC,CAAG,IAAI,CAAC7G,OAAO,CAAC6G,CAAC,CAC5DrE,CAAY,CAAC,IAAI,CAACmP,UAAU,CAAC,CAAC,IAAI,CAACvO,KAAK,CAAC,CAACJ,CAAC,CAAG,IAAI,CAAChD,OAAO,CAACgD,CAAC,CAC5DR,CAAY,CAAC,IAAI,CAACmP,UAAU,CAAC,CAAC,IAAI,CAACvO,KAAK,CAAC,CAACyD,CAAC,CAAG,IAAI,CAAC7G,OAAO,CAAC6G,CAAC,AAChE,CACA6F,OAAO8I,CAAM,CAAE,CACX,IAAMxV,EAAU,IAAI,CAACA,OAAO,CAAEkS,EAAQ,IAAI,CAACJ,gBAAgB,CAAC9R,GAAUkH,EAAQlH,EAAQkH,KAAK,CAAEmT,EAAe,IAAI,CAACva,UAAU,CAACS,KAAK,CAACP,OAAO,CAACO,KAAK,CAAE+Z,EAAkBD,EAAaE,mBAAmB,EAC/LF,EAAa9T,eAAe,CAMhC,GALA,IAAI,CAACpG,OAAO,CAAG,IAAI,CAACL,UAAU,CAACS,KAAK,CAACE,QAAQ,CACxCkC,KAAK,CAAC,GAAI,EAAG,MAClB3C,EAAQiH,KAAK,CAAE,KAAK,EAAG,KAAK,EAAGjH,EAAQsH,OAAO,CAAE,KAAK,EAAG,oBACnDlH,IAAI,CAAC8R,GACLlR,GAAG,CAACwU,GACL,CAAC,IAAI,CAAC1V,UAAU,CAACS,KAAK,CAACsJ,UAAU,CAAE,CACnC,GAAI3C,AAAgB,aAAhBA,EAAMG,KAAK,CAAiB,CAC5B,IAAMmT,EAAa,AAACf,GAAkBgB,uBAAuB,CAAC/Q,OAAO,CAAC1J,EAAQiH,KAAK,EAAI,IACnFjH,AAA4B,SAA5BA,EAAQuG,eAAe,CACvB+T,EACAta,EAAQuG,eAAe,AAC3BW,CAAAA,EAAMG,KAAK,CAAG,IAAI,CAACvH,UAAU,CAACS,KAAK,CAACE,QAAQ,CAACia,WAAW,CAAC,AAAsB,UAAtB,OAAOF,EAA0BA,EACtF,AAA2B,UAA3B,OAAOF,EAA+BA,EAClC,UACZ,CACA,IAAI,CAACna,OAAO,CACP6J,GAAG,CAAChK,EAAQkH,KAAK,EACjBF,MAAM,CAAChH,EAAQgH,MAAM,CAC9B,CACA,IAAI,CAAC7G,OAAO,CAACwa,SAAS,CAAG3a,EAAQ2a,SAAS,CAC1C,KAAK,CAACjO,QACV,CACA7M,OAAOqG,CAAS,CAAE,CACd,IAAMlG,EAAU,IAAI,CAACA,OAAO,CAAE4C,EAAO,IAAI,CAACA,IAAI,EAAI5C,EAAQ6Y,MAAM,EAAI7Y,EAAQ4C,IAAI,CAAED,EAAQ,IAAI,CAACxC,OAAO,CAAEiN,EAAQ,IAAI,CAACtK,MAAM,CAAC,EAAE,CAC9H,GAAI,CAACH,EAAO,YACR,IAAI,CAAC9C,MAAM,CAACqG,GAGhBvD,EAAMvC,IAAI,CAAC,CACPwC,KAAMA,EACFiW,GAAO+B,OAAOhY,GAAOwK,EAAO,IAAI,CAACtN,UAAU,CAACS,KAAK,EACjDP,EAAQ4G,SAAS,CAAC9H,IAAI,CAACsO,EAAO,IAAI,CAC1C,GACA,IAAMmC,EAAS,IAAI,CAACA,MAAM,CAACnC,GACrB8E,EAAQ,IAAI,CAACmD,QAAQ,CAAC9F,GACxB2C,GACAvP,EAAMqX,SAAS,CAAG9H,EAClBA,EAAMmH,OAAO,CAAG9J,EAAOU,gBAAgB,CAACjN,CAAC,CACzCkP,EAAMoH,OAAO,CAAG/J,EAAOU,gBAAgB,CAACpJ,CAAC,CACzClE,CAAK,CAACuD,EAAY,UAAY,OAAO,CAACgM,IAGtCvP,EAAMvC,IAAI,CAAC,CACP4C,EAAG,EACH6D,EAAG,KACP,GAEJlE,EAAMiT,MAAM,CAAG,CAAC,CAAC1D,EACjB,KAAK,CAACrS,OAAOqG,EACjB,CAMAqJ,OAEA0B,CAAM,CAAE,CACJ,IAAM1B,EAAS,KAAK,CAACA,OAAOzK,KAAK,CAAC,IAAI,CAAEE,WAAYhC,EAAI,IAAI,CAAChD,OAAO,CAACgD,CAAC,EAAI,EAAG6D,EAAI,IAAI,CAAC7G,OAAO,CAAC6G,CAAC,EAAI,EAKnG,OAJA0I,EAAOU,gBAAgB,CAACjN,CAAC,EAAIA,EAC7BuM,EAAOU,gBAAgB,CAACpJ,CAAC,EAAIA,EAC7B0I,EAAOS,gBAAgB,CAAChN,CAAC,EAAIA,EAC7BuM,EAAOS,gBAAgB,CAACnJ,CAAC,EAAIA,EACtB0I,CACX,CAIA8F,SAAS9F,CAAM,CAAE,CACb,IAAMsG,EAAO,IAAI,CAAC1V,OAAO,CAAEI,EAAQ,IAAI,CAACT,UAAU,CAACS,KAAK,CAAEuP,EAAUvP,EAAMuP,OAAO,CAAE1C,EAAQ,IAAI,CAACtK,MAAM,CAAC,EAAE,CAAEgT,EAAc,IAAI,CAAC9V,OAAO,CAAE6a,EAAyBtL,EAAOU,gBAAgB,CAAE6K,EAAyBvL,EAAOS,gBAAgB,CACrO+K,EAAcC,EAASC,EAAkBC,EAAkBC,EAAW/N,EAAMlK,MAAM,CAAC+C,OAAO,EAC1FkL,AAjrDgDjE,EAirD1BtO,SAAS,CAAC0M,YAAY,CAACxM,IAAI,CAACsO,GACtD,GAAIyI,GAAQsF,EAAU,CAClB,GAAM,CAAEhT,MAAAA,EAAQ,CAAC,CAAEF,OAAAA,EAAS,CAAC,CAAE,CAAG4N,CAC9BC,CAAAA,EAAYsF,QAAQ,EAAItL,EACxBiL,EAAejL,EAAQuL,WAAW,CAACvc,IAAI,CAAC,CACpCyB,MAAAA,EACA6a,SAAUnC,GAAuBnD,EAAYsF,QAAQ,CAAE,IACvDE,gBAAiBxL,EAAQwL,eAAe,CACxChS,QAASwG,EAAQxG,OAAO,AAC5B,EAAGnB,EAAOF,EAAQ,CACduF,MAAOsN,EAAuB9X,CAAC,CAC/ByK,MAAOqN,EAAuBjU,CAAC,CAC/B0U,SAAUnO,EAAMmO,QAAQ,CACxBC,QAASpO,EAAMoO,OAAO,CACtBpC,EAAI0B,EAAuB7S,MAAM,EAC7B6S,EAAuB3S,KAAK,AACpC,GAEK2N,EAAYrJ,UAAU,CAC3BsO,EAAejF,EAAYrJ,UAAU,CAAC3N,IAAI,CAAC,IAAI,GAG/Ckc,EAAU,CACNhY,EAAG6X,EAAuB7X,CAAC,CAC3B6D,EAAGgU,EAAuBhU,CAAC,CAC3BsB,MAAO,EACPF,OAAQ,CACZ,EACA8S,EAAetB,GAAkBC,eAAe,CAACZ,GAAyBhD,EAAa,CACnF3N,MAAAA,EACAF,OAAAA,CACJ,GAAI+S,GACA,AAA0B,YAA1B,IAAI,CAAChb,OAAO,CAAC8G,QAAQ,EACrBiU,CAAAA,EAAetB,GAAkBC,eAAe,CAACD,GAAkBM,gBAAgB,CAACxZ,EAAOsV,EAAMC,EAAaiF,GAAeC,EAAO,GAGxIlF,EAAY3P,IAAI,GAChB8U,EAAmBF,EAAa/X,CAAC,CAAGzC,EAAMgL,QAAQ,CAClD2P,EAAmBH,EAAalU,CAAC,CAAGtG,EAAMiL,OAAO,CACjD2P,EACI5a,EAAM+K,YAAY,CAAC2P,EAAkBC,IACjC3a,EAAM+K,YAAY,CAAC2P,EAAmB9S,EAAO+S,EAAmBjT,GAEhF,CACA,OAAOkT,EAAWJ,EAAe,IACrC,CACJ,CAWAtB,GAAkBxH,QAAQ,CAAG,CACzB1L,gBAAiB,OACjBC,YAAa,SACbE,YAAa,eACb5F,OAAQ,SACR2F,aAAc,IACdM,QAAS,SACb,EAOA0S,GAAkBgB,uBAAuB,CAAG,CAAC,YAAY,AAwCzD,OAAMgB,WAA0B1I,EAM5BvG,YAAY1M,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE,CACpC,KAAK,CAACtD,EAAYE,EAASoD,EAAO,SAMlC,IAAI,CAACyC,IAAI,CAAG,QACZ,IAAI,CAAC8F,SAAS,CAAG,KAAK,CAAC6G,cAC3B,CACA9F,OAAO8I,CAAM,CAAE,CACX,IAAMtD,EAAQ,IAAI,CAACJ,gBAAgB,CAAC,IAAI,CAAC9R,OAAO,EAAGA,EAAU,IAAI,CAACA,OAAO,AACzE,CAAA,IAAI,CAACG,OAAO,CAAG,IAAI,CAACL,UAAU,CAACS,KAAK,CAACE,QAAQ,CACxCib,KAAK,CAAC1b,EAAQ2b,GAAG,CAAE,EAAG,KAAM3b,EAAQmI,KAAK,CAAEnI,EAAQiI,MAAM,EACzD7H,IAAI,CAAC8R,GACLlR,GAAG,CAACwU,GACT,IAAI,CAACrV,OAAO,CAACgI,KAAK,CAAGnI,EAAQmI,KAAK,CAClC,IAAI,CAAChI,OAAO,CAAC8H,MAAM,CAAGjI,EAAQiI,MAAM,CACpC,KAAK,CAACyE,QACV,CACA7M,OAAOqG,CAAS,CAAE,CACd,GAAI,IAAI,CAAC/F,OAAO,CAAE,CACd,IAAMoP,EAAS,IAAI,CAACA,MAAM,CAAC,IAAI,CAACzM,MAAM,CAAC,EAAE,EAAGuS,EAAWuG,AA9DGnC,GA8D6B7a,SAAS,CAACyW,QAAQ,CAACvW,IAAI,CAAC,IAAI,CAAEyQ,GACjH8F,EACA,IAAI,CAAClV,OAAO,CAAC+F,EAAY,UAAY,OAAO,CAAC,CACzClD,EAAGqS,EAASrS,CAAC,CACb6D,EAAGwO,EAASxO,CAAC,AACjB,GAGA,IAAI,CAAC1G,OAAO,CAACC,IAAI,CAAC,CACd4C,EAAG,EACH6D,EAAG,IACP,GAEJ,IAAI,CAAC1G,OAAO,CAACyV,MAAM,CAAGW,CAAAA,CAAQlB,CAClC,CACA,KAAK,CAACxV,OAAOqG,EACjB,CACJ,CAYAuV,GAAkBxJ,QAAQ,CAAG,CACzB9J,MAAO,QACPF,OAAQ,SACRnH,OAAQ,QACZ,EASA,IAAI+a,GAAuFne,EAAoB,KAC3Goe,GAA2Gpe,EAAoBI,CAAC,CAAC+d,IAmBrI,GAAM,CAAE1c,SAAU4c,EAAiB,CAAE/E,cAAAA,EAAa,CAAE,CAAI9X,IA4GrB8c,GAtGnC,MAMIxP,YAAYyP,CAAS,CAAEC,CAAQ,CAAE,CAC7B,IAAI,CAACA,QAAQ,CAAGA,EAChB,IAAI,CAACC,SAAS,CAAG,IAAI,CAACC,oBAAoB,CAACH,GAC3C,IAAI,CAACI,WAAW,CAAG,IAAI,CAACC,cAAc,EAC1C,CAkBAF,qBAAqBH,CAAS,CAAEtV,EAAY,wCAAwC,CAAE,CAClF,OAAOqQ,GAAc,MAAO,CAAErQ,UAAAA,CAAU,EAAG,KAAK,EAAGsV,EACvD,CAUAK,eAAe3V,EAAY,wBAAwB,CAAE,CACjD,IAAM4V,EAAQ,IAAI,CAAEL,EAAW,IAAI,CAACA,QAAQ,CAEtCG,EAAcrF,GAAc,SAAU,CAAErQ,UAAAA,CAAU,EAAG,KAAK,EAAG,IAAI,CAACwV,SAAS,EAgBjF,OAfAnF,GAAc,OAAQ,CAClBrQ,UAAW,iBACf,EAAG,CACC6V,gBAAiB,OAAUN,CAAAA,EAASO,KAAK,CAAC,0BACtCP,EAAWA,EAAW,WAAU,EAAK,GAC7C,EAAGG,GACH,CAAC,QAAS,aAAa,CAACpb,OAAO,CAAC,AAACyb,IAC7BX,GAAkBM,EAAaK,EAAWH,EAAMI,iBAAiB,CAACC,IAAI,CAACL,GAC3E,GAEAR,GAAkBc,SAAU,UAAW,SAAUtb,CAAK,EAC9CA,AAAe,WAAfA,EAAMub,IAAI,EACVP,EAAMI,iBAAiB,EAE/B,GACON,CACX,CAKAM,mBAAoB,CAChB,IAAI,CAACI,UAAU,EACnB,CAOAC,UAAUC,EAAe,+BAA+B,CAAE,CACtD,IAAMC,EAAW,IAAI,CAACf,SAAS,CAAEgB,EAAmB,IAAI,CAACd,WAAW,AACpE,CAAA,IAAI,CAACxW,IAAI,CAAG,KAAK,EAEjBqX,EAASE,SAAS,CAAG,AAACtB,KAA+FuB,SAAS,CAE1HH,EAASvW,SAAS,CAAC+C,OAAO,CAACuT,IAAiB,IAC5CC,EAASI,SAAS,CAACC,MAAM,CAACN,GAE1BC,EAASM,eAAe,CAAC,UAG7BN,EAASO,WAAW,CAACN,GACrBD,EAAShW,KAAK,CAACwW,OAAO,CAAG,QACzBR,EAAShW,KAAK,CAACe,MAAM,CAAG,EAC5B,CAIA8U,YAAa,CACT,IAAI,CAACZ,SAAS,CAACjV,KAAK,CAACwW,OAAO,CAAG,MACnC,CACJ,EAsBM,CAAEtV,IAAKuV,EAAoB,CAAEC,UAAAA,EAAS,CAAE,CAAI1e,IAE5C,CAAE8X,cAAe6G,EAA8B,CAAEte,QAASue,EAAwB,CAAEte,SAAUue,EAAyB,CAAEvV,WAAYwV,EAA2B,CAAEve,KAAMwe,EAAqB,CAAEC,WAAAA,EAAU,CAAE,CAAIhf,IAuGrN,SAASif,GAAclC,CAAS,CAAE1b,CAAK,CAAE6d,CAAU,CAAEpe,CAAO,CAAEqe,CAAO,CAAEC,CAAM,MAKrEC,EAAgBC,EAJpB,GAAI,CAACje,EACD,OAEJ,IAAMke,EAAW,IAAI,CAACA,QAAQ,CAAEzc,EAAO,IAAI,CAACA,IAAI,CAEhDgc,GAA4Bhe,EAAS,CAACqU,EAAOqK,KAEzCH,EAAiBH,AAAe,KAAfA,EAAoBA,EAAa,IAAMM,EAASA,EAC7DX,GAA0B1J,KAG1B,CAACyJ,GAAyBzJ,IAErByJ,GAAyBzJ,IAAU0J,GAA0B1J,CAAK,CAAC,EAAE,GAElE,AAACmK,AADLA,CAAAA,EAAYxc,CAAI,CAAC0c,EAAO,EAAIA,CAAK,EAClBjC,KAAK,CAAC,QACjB4B,EAAQne,IAAI,CAAC,CACT,CAAA,EACAse,EACAvC,EACH,EAELkC,GAAcrf,IAAI,CAAC,IAAI,CAAEmd,EAAW1b,EAAOge,EAAgBlK,EAAOgK,EAAS,CAAA,IAG3EA,EAAQne,IAAI,CAAC,CACT,IAAI,CACJqe,EACA,aACAtC,EACA5H,EACH,EAGb,GACIiK,IACAJ,GAAWG,EAAS,AAACngB,GAAOA,CAAC,CAAC,EAAE,CAACue,KAAK,CAAC,WAAa,GAAK,GACrDmB,IACAS,EAAQM,OAAO,GAEnBN,EAAQpd,OAAO,CAAC,AAAC2d,IACTA,AAAgB,CAAA,IAAhBA,CAAQ,CAAC,EAAE,CACXf,GAA+B,OAAQ,CACnClX,UAAW,6BACf,EAAG,KAAK,EAAGiY,CAAQ,CAAC,EAAE,EAAEnB,WAAW,CAACE,GAAqBkB,cAAc,CAACD,CAAQ,CAAC,EAAE,IAGnFA,CAAQ,CAAC,EAAE,CAAG,CACVvK,MAAOuK,CAAQ,CAAC,EAAE,CAAC,EAAE,CACrB/Y,KAAM+Y,CAAQ,CAAC,EAAE,CAAC,EAAE,AACxB,EACAH,EAAS3Z,KAAK,CAAC8Z,CAAQ,CAAC,EAAE,CAAEA,EAASE,MAAM,CAAC,IAEpD,GAER,CA2BA,GAAM,CAAE1W,IAAK2W,EAAmB,CAAE,CAAI7f,IAEhC,CAAE8f,YAAAA,EAAW,CAAE,CAAIlS,IAEnB,CAAE3N,SAAU8f,EAAwB,CAAEjI,cAAekI,EAA6B,CAAEnZ,QAASoZ,EAAuB,CAAE5f,QAAS6f,EAAuB,CAAE5f,SAAU6f,EAAwB,CAAE7W,WAAY8W,EAA0B,CAAEpB,WAAYqB,EAA0B,CAAE,CAAIrgB,KAWtR,AAAC,SAAU5B,CAAkB,EACzBA,CAAkB,CAACA,CAAkB,CAAC,mBAAmB,CAAG,EAAE,CAAG,mBACjEA,CAAkB,CAACA,CAAkB,CAAC,iBAAiB,CAAG,EAAE,CAAG,gBACnE,EAAGA,GAAuBA,CAAAA,EAAqB,CAAC,CAAA,GAKhD,IAAMkiB,GAAqB,CACvB,wBAAyB,CAAC,WAAY,YAAa,YAAY,CAC/D,yBAA0B,CAAC,MAAO,MAAO,OAAQ,OAAQ,MAAM,AACnE,EAcA,SAASC,GAAiBtD,CAAS,EAE/B,IAAMuD,EAASR,GAA8B,MAAO,CAChDvY,UAAW,0BACf,EAAG,KAAK,EAAGwV,GAELwD,EAAST,GAA8B,MAAO,CAChDvY,UAAW,0BACf,EAAG,KAAK,EAAGwV,GAKX,OAHA+C,GAA8B,MAAO,CACjCvY,UAAW,kCACf,EAAG,KAAK,EAAGgZ,GACJ,CACHD,OAAQA,EACRC,OAAQA,CACZ,CACJ,CAgDA,SAASC,GAA8Brf,CAAK,CAAE2C,CAAM,CAAE2c,CAAU,CAAEC,CAAa,EAC3E,IAAMC,EAAS7c,EAAO8c,MAAM,EAAI9c,EAAOlD,OAAO,CAACggB,MAAM,AAErDF,CAAAA,EAAc1C,SAAS,CAAG,AAACtB,KAA+FuB,SAAS,CAEnI6B,GAA8B,KAAM,CAChCvY,UAAW,4BACf,EAAG,KAAK,EAAGmZ,GAAerC,WAAW,CAACsB,GAAoBF,cAAc,CAACoB,GAAY/c,EAAQ2c,GAAYK,iBAAiB,GAE1HhB,GAA8B,QAAS,CACnCrZ,KAAM,SACNsa,KAAM,mBAAqBN,EAC3BxL,MAAOwL,CACX,EAAG,KAAK,EAAGC,GAEXM,GAActhB,IAAI,CAAC,IAAI,CAAE+gB,EAAY,SAAUtf,EAAOuf,EAAe5c,EAAQA,EAAOmd,YAAY,EAAInd,EAAOmd,YAAY,CAACrgB,OAAO,CAACuE,EAAE,EAC9Hwb,EAAOO,cAAc,EACrBF,GAActhB,IAAI,CAAC,IAAI,CAAE+gB,EAAY,SAAUtf,EAAOuf,EAAe5c,EAAQA,EAAOmd,YAAY,EAAIN,EAAOO,cAAc,EAG7HC,GAAezhB,IAAI,CAAC,IAAI,CAAEyB,EAAO,SAAUwf,EAAQF,EAAYC,EACnE,CAwBA,SAASU,GAAiBjgB,CAAK,CAAE0b,CAAS,CAAEwE,CAAQ,CAAEC,CAAM,EAIxD,SAASC,EAAgBzd,CAAM,CAAE0d,CAAa,EAC1C,IAAM9U,EAASgU,EAAc1B,UAAU,CAClC3K,QAAQ,CAAC,EAAE,CAChBmM,GAA8B9gB,IAAI,CAACyd,EAAOhc,EAAO2C,EAAQ0d,EAAed,GACpEhU,GACAA,CAAAA,EAAO5E,KAAK,CAACwW,OAAO,CAAG,OAAM,EAG7BmD,GAAU3d,EAAOlD,OAAO,EACxBkf,GAA8B,QAAS,CACnCrZ,KAAM,SACNsa,KAAM,iBAAmBS,EACzBvM,MAAOnR,EAAOlD,OAAO,CAACuE,EAAE,AAC5B,EAAG,KAAK,EAAGub,GAAegB,YAAY,CAAC,4BAA6B5d,EAAOlD,OAAO,CAACuE,EAAE,CAE7F,CACA,IAAMgY,EAAQ,IAAI,CAAEva,EAAOua,EAAMva,IAAI,CAAE0d,EAASzD,EAAU8E,gBAAgB,CAAC,4BAA4B,CAAC,EAAE,CAAEpB,EAAS1D,EAAU8E,gBAAgB,CAAC,4BAA4B,CAAC,EAAE,CAAEF,EAASJ,AAAa,SAAbA,EAAqBvd,EAAU2d,EACrNtgB,EAAM2C,MAAM,CACZ3C,EAAMP,OAAO,CAACghB,WAAW,EAAI,CAAC,EAElC,GAAI,CAACzgB,GAAS2C,EACV,OAEJ,IAAI2S,EAAMoL,EAAsB,EAAE,AAE9B,CAACJ,GAAWzB,GAAwBlc,GAI/Bkc,GAAwBlc,IAC7B+d,CAAAA,EAAsBC,GAAkBpiB,IAAI,CAAC,IAAI,CAAEoE,EAAM,EAHzD+d,EAAsBE,GAAariB,IAAI,CAAC,IAAI,CAAEoE,EAAQwd,GAM1DnB,GAA2B0B,EAAqB,CAAC/iB,EAAGkjB,KAChD,IAAMC,EAAcnjB,EAAEgiB,iBAAiB,CAACoB,WAAW,GAAIC,EAAcH,EAAElB,iBAAiB,CAACoB,WAAW,GACpG,OAAO,AAACD,EAAcE,EAClB,GAAK,CAACF,CAAAA,EAAcE,CAAU,CACtC,GAGI7B,EAAOjM,QAAQ,CAAC,EAAE,EAClBiM,EAAOjM,QAAQ,CAAC,EAAE,CAAC8J,MAAM,GAG7B,IAAMiE,EAAgBtC,GAA8B,KAAM,CACtDvY,UAAW,2BACf,EAAG,KAAK,EAAG+Y,GACLI,EAAgBH,EAAOoB,gBAAgB,CAAC,oCAAoC,CAAC,EAAE,CAiBrF,GAhBAE,EAAoBhgB,OAAO,CAAC,AAACwgB,IACzB,GAAM,CAAEvB,kBAAAA,CAAiB,CAAEU,cAAAA,CAAa,CAAE1d,OAAAA,CAAM,CAAE,CAAGue,EACrD5L,EAAOqJ,GAA8B,KAAM,CACvCvY,UAAW,2BACf,EAAG,KAAK,EAAG6a,GACX,IAAME,EAAMxC,GAA8B,SAAU,CAChDvY,UAAW,iCACXgb,YAAazB,CACjB,EAAG,KAAK,EAAGrK,GACX,CAAC,QAAS,aAAa,CAAC5U,OAAO,CAAC,AAACyb,IAC7BuC,GAAyByC,EAAKhF,EAAW,WACrCiE,EAAgBzd,EAAQ0d,EAC5B,EACJ,EACJ,GAEIK,EAAoB9e,MAAM,CAAG,EAAG,CAChC,GAAM,CAAEe,OAAAA,CAAM,CAAE0d,cAAAA,CAAa,CAAE,CAAGK,CAAmB,CAAC,EAAE,CACxDN,EAAgBzd,EAAQ0d,EAC5B,MACUC,IACN/E,KAA8F8F,cAAc,CAAC9B,EAAc1B,UAAU,CAAC3K,QAAQ,CAAC,EAAE,CAAEzR,EAAK6f,aAAa,EAAI,IACzK/B,EAAc1B,UAAU,CAAC3K,QAAQ,CAAC,EAAE,CAC/BvM,KAAK,CAACwW,OAAO,CAAG,OAE7B,CAiBA,SAAS6C,GAAehgB,CAAK,CAAE6d,CAAU,CAAE2B,CAAM,CAAEla,CAAI,CAAEoW,CAAS,EAC9D,GAAI,CAAC1b,EACD,OAEJ,IAAMke,EAAW,IAAI,CAACA,QAAQ,CAC9Ba,GAA2BS,EAAQ,CAAC1L,EAAOyN,KAEvC,IAAMvD,EAAiBH,EAAa,IAAM0D,EAC1C,GAAI3C,GAAwB9K,IACxBkK,EASA,GARIc,GAAyBhL,KAGzBoK,EAAS3f,IAAI,CAAC,IAAI,CAAEyf,EAAgB1Y,EAAMoW,EAAW,CAAC,GACtDsE,GAAezhB,IAAI,CAAC,IAAI,CAAEyB,EAAOge,EAAgBlK,EAAOxO,EAAMoW,IAI9DsC,KAAkBjhB,EAAoB,CAEtC,IAAMykB,EAAYC,GAAaljB,IAAI,CAAC,IAAI,CAAE+G,EAAM0Y,EAAgBtC,GAEhEgG,GAAoBnjB,IAAI,CAAC,IAAI,CAAEyB,EAAO6d,EAAY2D,EAAWlc,EAAMic,EAAWzN,EAClF,KAGAkK,AAAmB,0BAAnBA,GACKa,GAAwB/K,IAEzBoK,EAAS3f,IAAI,CAAC,IAAI,CAAEyf,EAAgB1Y,EAAMoW,EAAW,CACjD5H,MAAOA,EACPxO,KAAM,QACV,EAIZ,EACJ,CAYA,SAASqc,GAAa3hB,CAAK,CAAE0b,CAAS,EAClC,IAAMM,EAAQ,IAAI,CAAEmD,EAASzD,EAAU8E,gBAAgB,CAAC,4BAA4B,CAAC,EAAE,CAKpFoB,EAAkB,IAAI,CAACngB,IAAI,CAACogB,WAAW,CAAEC,EAAenD,GAA8B,MAAO,CAC5FvY,UAAW,0BACf,EAAG,KAAK,EAAG+Y,GACL4C,EAAoB,SAAUC,CAAS,EAEzC/B,GAAiB1hB,IAAI,CAACyd,EAAOhc,EAAOgc,EAAMJ,SAAS,CAAE,MAAOoG,EAChE,EAEMC,EAAQ,IAAI,CAAC/D,QAAQ,CAbwE,mBAa9D,QAAS4D,EAb2F,CACrIhO,MAAO,GACPxO,KAAM,OACN4c,QAAS,oBACTC,eAAgB,0CACpB,GAQ8E5W,EAASoT,GAA8B,IAAK,CACtHyC,YAAaQ,CACjB,EAAG,KAAK,EAAGE,GACXG,EAAMlF,SAAS,CAACtc,GAAG,CAAC,sCACpB8K,EAAOwR,SAAS,CAACtc,GAAG,CAAC,uBAErBie,GAAyBuD,EAAO,QAAS,WACrCF,EAAkB,IAAI,CAACjO,KAAK,EAExB,IAAI,CAACA,KAAK,CAAClS,MAAM,CACjB2J,EAAO5E,KAAK,CAACwW,OAAO,CAAG,eAGvB5R,EAAO5E,KAAK,CAACwW,OAAO,CAAG,MAE/B,GAEA,CAAC,QAAS,aAAa,CAACzc,OAAO,CAAC,AAACyb,IAC7BuC,GAAyBnT,EAAQ4Q,EAAW,WAExC8F,EAAMnO,KAAK,CAAG,GACdiO,EAAkB,IAElBxW,EAAO5E,KAAK,CAACwW,OAAO,CAAG,MAC3B,EACJ,EACJ,CAeA,SAASsE,GAAapB,CAAa,CAAE+B,CAAU,CAAE1G,CAAS,EACtD,IAAM2G,EAAkBD,EAAWE,KAAK,CAAC,KAAMC,EAAYF,CAAe,CAACA,EAAgBzgB,MAAM,CAAG,EAAE,CAAE4gB,EAAa,cAAgBJ,EAAa,SAAW/B,EAAe5e,EAAO,IAAI,CAACA,IAAI,CAE5Lkd,GAA8B,QAAS,CACnCuD,QAASM,CACb,EAAG,KAAM9G,GAAWwB,WAAW,CAACsB,GAAoBF,cAAc,CAAC7c,CAAI,CAAC8gB,EAAU,EAAIH,IAEtF,IAAMZ,EAAY7C,GAA8B,SAAU,CACtDiB,KAAM4C,EACNpc,UAAW,yBACXpC,GAAI,qBAAuBoe,CAC/B,EAAG,KAAM1G,GAET,OADA8F,EAAUjB,YAAY,CAAC,KAAM,qBAAuB6B,GAC7CZ,CACX,CAwBA,SAASE,GAAoB1hB,CAAK,CAAEoiB,CAAU,CAAEZ,CAAS,CAAEnB,CAAa,CAAEoC,CAAa,CAAEC,CAAc,CAAEC,CAAa,EAE9GP,AAAe,WAAfA,GAA2BA,AAAe,WAAfA,EAE3BpiB,EAAM2C,MAAM,CAACjC,OAAO,CAAC,AAACiC,IAClB,IAAMigB,EAAgBjgB,EAAOlD,OAAO,CAAEojB,EAAaD,EAAchD,IAAI,EACjEgD,EAAcnD,MAAM,CACpB9c,EAAOid,IAAI,CACXgD,EAAc5e,EAAE,EAAI,EACC,CAAA,gCAArB4e,EAAc5e,EAAE,EAChB4e,EAAc5e,EAAE,GAAM2e,CAAAA,GAClBA,EAAcljB,OAAO,EACrBkjB,EAAcljB,OAAO,CAACuE,EAAE,AAAD,IACvB,AAAC4a,GAAwB8D,IACzBN,AAAe,WAAfA,GACAzf,AAAgB,WAAhBA,EAAO2C,IAAI,EACXod,CAAAA,EAAiBE,EAAc5e,EAAE,AAAD,EAEpC2a,GAA8B,SAAU,CACpC7K,MAAO8O,EAAc5e,EAAE,AAC3B,EAAG,KAAK,EAAGwd,GAAWtE,WAAW,CAACsB,GAAoBF,cAAc,CAACuE,IAE7E,GAEKxC,GAAiBoC,GAGtBK,AAD2E7D,EAAkB,CAAzEwD,EAAgB,IAAMpC,EAAgE,CAC1F3f,OAAO,CAAC,AAAC8H,IACrBmW,GAA8B,SAAU,CACpC7K,MAAOtL,CACX,EAAG,KAAK,EAAGgZ,GAAWtE,WAAW,CAACsB,GAAoBF,cAAc,CAAC9V,GACzE,GAGAoW,GAAwB8D,IACxBlB,CAAAA,EAAU1N,KAAK,CAAG4O,CAAa,CAEvC,CAiBA,SAAS9B,GAAaje,CAAM,CAAEwd,CAAM,EAChC,IAII4C,EAJgBthB,EAAOua,AAAb,IAAI,CAAehc,KAAK,EAAIgc,AAA5B,IAAI,CAA8Bhc,KAAK,CAACP,OAAO,CAACgC,IAAI,CAAEuhB,EAAmBvhB,GACnFA,EAAKwhB,UAAU,EACfxhB,EAAKwhB,UAAU,CAACjH,KAAK,EACrBva,EAAKwhB,UAAU,CAACjH,KAAK,CAACgH,gBAAgB,CAAEtC,EAAsB,EAAE,CAkCpE,OAhCA3B,GAA2Bpc,EAAQ,CAACA,EAAQmR,KACxC,IAAM8O,EAAgBjgB,GAAUA,EAAOlD,OAAO,CAE9C,GAAIkD,EAAO8c,MAAM,EAAImD,GACjBA,EAAcnD,MAAM,CAAE,CACtB,GAAM,CAAEE,kBAAAA,CAAiB,CAAEU,cAAAA,CAAa,CAAE,CAAGX,GAAY/c,EAAQmR,GACjE,GAAIqM,EAAQ,CAGR,IAAM+C,EAAQ,AAAIC,OADEhD,EAAOiD,OAAO,CAAC,sBAAuB,QACpB,KAAMC,EAAQL,GAChDA,CAAgB,CAAC3C,EAAc,EAC/B2C,CAAgB,CAAC3C,EAAc,CAACre,IAAI,CAAC,MAAQ,GAC7C2d,CAAAA,EAAkBzD,KAAK,CAACgH,IACxBG,EAAMnH,KAAK,CAACgH,EAAK,IACjBH,EAAiB,CACbpD,kBAAAA,EACAU,cAAAA,EACA1d,OAAQA,CACZ,EACA+d,EAAoB/gB,IAAI,CAACojB,GAEjC,MAEIA,EAAiB,CACbpD,kBAAAA,EACAU,cAAAA,EACA1d,OAAQA,CACZ,EACA+d,EAAoB/gB,IAAI,CAACojB,EAEjC,CACJ,GACOrC,CACX,CAYA,SAASC,GAAkBhe,CAAM,EAC7B,IAAM+d,EAAsB,EAAE,CAW9B,OATA/d,EAAOjC,OAAO,CAAC,AAACiC,IACRA,EAAO2gB,EAAE,CAAC,QACV5C,EAAoB/gB,IAAI,CAAC,CACrBggB,kBAAmBhd,EAAOid,IAAI,CAC9BS,cAAe1d,EAAO2C,IAAI,CAC1B3C,OAAQA,CACZ,EAER,GACO+d,CACX,CA+BA,SAAShB,GAAY/c,CAAM,CAAE0d,CAAa,EACtC,IAAM5gB,EAAUkD,EAAOlD,OAAO,CAE1BojB,EAAa,AAACpE,EAAW,CAAC4B,EAAc,EACxC5B,EAAW,CAAC4B,EAAc,CAAChiB,SAAS,CAACklB,QAAQ,EAC7ClD,EAAcmD,WAAW,GAAIlE,EAAae,EAM9C,OAJI5gB,GAAWA,EAAQ6F,IAAI,GACvBga,EAAa3c,EAAOlD,OAAO,CAAC6F,IAAI,CAChCud,EAAalgB,EAAOid,IAAI,EAErB,CACHD,kBAAmBkD,EACnBxC,cAAef,CACnB,CACJ,CAsBA,SAASO,GAAcQ,CAAa,CAAE+B,CAAU,CAAEpiB,CAAK,CAAE0b,CAAS,CAAEiH,CAAa,CAAED,CAAc,EAG7F,GAAI,CAAC1iB,EACD,OAGJ,IAAMwhB,EAAYC,GAAaljB,IAAI,CANrB,IAAI,CAMyB8hB,EAAe+B,EAAY1G,GAEtEgG,GAAoBnjB,IAAI,CARV,IAAI,CAQcyB,EAAOoiB,EAAYZ,EAAW,KAAK,EAAG,KAAK,EAAG,KAAK,EAAGmB,GAElF/D,GAAwB8D,IACxBlB,CAAAA,EAAU1N,KAAK,CAAG4O,CAAa,CAEvC,CA0BA,GAAM,CAAE7a,IAAK4b,EAAa,CAAE,CAAI9kB,IAE1B,CAAEC,SAAU8kB,EAAkB,CAAEjN,cAAekN,EAAuB,CAAE,CAAIhlB,IAWlF,SAASilB,KAEL,OAAOD,GAAwB,MAAO,CAElCvd,UAAW,sDACf,EAAG,KAAK,EAJS,IAAI,CAACwV,SAAS,CAKnC,CAWA,SAASiI,GAAYC,CAAO,CAAEC,CAAU,EACpC,IAAMpH,EAAW,IAAI,CAACf,SAAS,CAAEna,EAAO,IAAI,CAACA,IAAI,CAC7C2E,EAAY,qBACZ2d,AAAe,CAAA,IAAfA,GACA3d,CAAAA,GAAa,0BAAyB,EAG1C,IAAM4d,EAAWL,GAAwB,SAAU,CAC/Cvd,UAAAA,CACJ,EAAG,KAAK,EAAGuW,GAGX,OAFAqH,EAAS9G,WAAW,CAACuG,GAAcnF,cAAc,CAAC7c,CAAI,CAACqiB,EAAU,SAAS,EAAIA,IAC9EE,EAASzD,YAAY,CAAC,2BAA4BuD,GAC3CE,CACX,CAKA,SAASC,KACL,IAAMtH,EAAW,IAAI,CAACf,SAAS,CAAEsI,EAAOvH,EACnC6D,gBAAgB,CAAC,wBAAyB2D,EAAcxH,EACxD6D,gBAAgB,CAAC,gCACtB,IAAK,IAAI5f,EAAI,EAAGA,EAAIsjB,EAAKtiB,MAAM,CAAEhB,IAC7BsjB,CAAI,CAACtjB,EAAE,CAACmc,SAAS,CAACC,MAAM,CAAC,8BACzBmH,CAAW,CAACvjB,EAAE,CAACmc,SAAS,CAACC,MAAM,CAAC,2BAExC,CA4BA,SAASoH,GAAUC,CAAG,CAAExhB,CAAK,EACzB,IAAMyhB,EAAU,IAAI,CAAC1I,SAAS,CACzB4E,gBAAgB,CAAC,+BACtB6D,CAAAA,EAAIje,SAAS,EAAI,8BACjBke,CAAO,CAACzhB,EAAM,CAACuD,SAAS,EAAI,2BAChC,CAOA,SAASme,GAAWR,CAAU,EAC1B,IAAM/H,EAAQ,IAAI,CAClBkI,AADsDvH,AAAvB,IAAI,CAACf,SAAS,CAAkB4E,gBAAgB,CAAC,wBAC3E9f,OAAO,CAAC,CAAC2jB,EAAKzjB,KACXmjB,CAAAA,AAAe,IAAfA,GACAM,AAAiD,SAAjDA,EAAI1O,YAAY,CAAC,2BAAqC,GAG1D,CAAC,QAAS,aAAa,CAACjV,OAAO,CAAC,AAACyb,IAC7BuH,GAAmBW,EAAKlI,EAAW,WAE/B8H,GAAY1lB,IAAI,CAACyd,GACjBoI,GAAU7lB,IAAI,CAACyd,EAAO,IAAI,CAAEpb,EAChC,EACJ,EACJ,EACJ,CA0BA,GAAM,CAAEiH,IAAK2c,EAAS,CAAE,CAAI7lB,IAEtB,CAAEgP,WAAAA,EAAU,CAAE,CAAIhP,IAKlB,CAAEC,SAAU6lB,EAAc,CAAEhO,cAAeiO,EAAmB,CAAErR,OAAQsR,EAAY,CAAE5lB,UAAW6lB,EAAe,CAAE1lB,KAAM2lB,EAAU,CAAE,CAAIlmB,GA0D9I,OAAMmmB,WAAcrJ,GAMhBxP,YAAYyP,CAAS,CAAEC,CAAQ,CAAE3b,CAAK,CAAE,CACpC,KAAK,CAAC0b,EAAWC,GACjB,IAAI,CAAC3b,KAAK,CAAGA,EACb,IAAI,CAACyB,IAAI,CAAG,AAACkM,CAAAA,KAAalM,IAAI,CAACwhB,UAAU,EAAI,CAAC,CAAA,EAAGjH,KAAK,EAAI,CAAC,EAC3DyI,GAAe,IAAI,CAAC7I,SAAS,CAAE,YAAa,KACxC,IAAMmJ,EAAmB/kB,GACrBA,EAAMglB,kBAAkB,EACxBhlB,EAAMglB,kBAAkB,CAACD,gBAAgB,CAC7C,GAAIA,EAAkB,CAClBA,EAAiBjc,WAAW,CAAG,CAAA,EAC/B,IAAMmc,EAASR,GAAeD,GAAW,QAAS,KAC9CU,WAAW,KACPH,EAAiBjc,WAAW,CAAG,CAAA,CACnC,EAAG,GACHmc,GACJ,EACJ,CACJ,EACJ,CA0BA/G,SAASC,CAAM,CAAEkC,CAAa,CAAE3E,CAAS,CAAEyJ,CAAe,CAAE,CACxD,IAAM9C,EAAkBlE,EAAOmE,KAAK,CAAC,KAAMF,EAAaC,CAAe,CAACA,EAAgBzgB,MAAM,CAAG,EAAE,CAAEH,EAAO,IAAI,CAACA,IAAI,CAAE2jB,EAAY,cAAgB/E,EAAgB,IAAMwE,GAAWM,EAAgBjD,OAAO,CAAEE,EACzM,CAACA,EAAWlG,KAAK,CAAC,UAElBwI,GAAoB,QAAS,CACzBxC,QAASkD,EACThf,UAAW+e,EAAgBhD,cAAc,AAC7C,EAAG,KAAK,EAAGzG,GAAWwB,WAAW,CAACsH,GAAUlG,cAAc,CAAC7c,CAAI,CAAC2gB,EAAW,EAAIA,IAGnF,IAAMH,EAAQyC,GAAoB,QAAS,CACvC9E,KAAMwF,EACNtR,MAAOqR,EAAgBrR,KAAK,CAC5BxO,KAAM6f,EAAgB7f,IAAI,CAC1Bc,UAAW,wBACf,EAAG,KAAK,EAAGsV,GAEX,OADAuG,EAAM1B,YAAY,CAAC,uBAAwBpC,GACpC8D,CACX,CACA7F,mBAAoB,CAChB,GAAI,IAAI,CAACpc,KAAK,CAAE,CACZ,IAAMglB,EAAqB,IAAI,CAAChlB,KAAK,CAACglB,kBAAkB,CACxDJ,GAAgBI,EAAoB,cAChCA,GACAA,EAAmBK,qBAAqB,EACxCT,GAAgBI,EAAoB,iBAAkB,CAAEzZ,OAAQyZ,EAAmBK,qBAAqB,AAAC,EAEjH,MAEI,KAAK,CAACjJ,mBAEd,CAiBAkJ,UAAU5J,CAAS,CAAEtZ,CAAK,CAAEkD,CAAI,CAAEigB,CAAS,CAAEC,CAAQ,CAAE,CACnD,IAAMja,EAASmZ,GAAoB,SAAU,KAAK,EAAG,KAAK,EAAGhJ,GAU7D,OATAnQ,EAAO2R,WAAW,CAACsH,GAAUlG,cAAc,CAAClc,IACxCojB,GACA,CAAC,QAAS,aAAa,CAAC9kB,OAAO,CAAC,AAACyb,IAC7BsI,GAAelZ,EAAQ4Q,EAAW,KAC9B,IAAI,CAACK,UAAU,GACRgJ,EAASC,AAlJpC,SAAmB/J,CAAS,CAAEpW,CAAI,EAC9B,IAAMogB,EAAY1iB,MAAM3E,SAAS,CAACmG,KAAK,CAACjG,IAAI,CAACmd,EAAU8E,gBAAgB,CAAC,UAAWmF,EAAa3iB,MAAM3E,SAAS,CAACmG,KAAK,CAACjG,IAAI,CAACmd,EAAU8E,gBAAgB,CAAC,WAAsIoF,EAAWlK,EAAU8E,gBAAgB,CAAhJ,6CAA8J,CAAC,EAAE,CAAEqF,EAAWnK,EAAU8E,gBAAgB,CAA3I,6CAAyJ,CAAC,EAAE,CACpYsF,EAAe,CACjBC,WAAYzgB,EACZsgB,SAAUA,GAAYA,EAASjQ,YAAY,CAAC,UAAY,GACxD6J,OAAQ,CAAC,CACb,EA4BA,OA3BAkG,EAAUhlB,OAAO,CAAC,AAACuhB,IACf,IAAM+D,EAAQ/D,EAAMtM,YAAY,CAAC,wBAAoCsM,EAAMtM,YAAY,CAAC,6BAGpFmQ,EAAaG,QAAQ,CAAGhE,EAAMnO,KAAK,CAE9BkS,EACLF,EAAatG,MAAM,CAACwG,EAAM,CAAG/D,EAAMnO,KAAK,CAIxCgS,EAAaxgB,IAAI,CAAG2c,EAAMnO,KAAK,AAEvC,GACA6R,EAAWjlB,OAAO,CAAC,AAACwlB,IAChB,IAAMliB,EAAKkiB,EAAOliB,EAAE,CAEpB,GAAIA,AAAO,6BAAPA,GACAA,AAAO,6BAAPA,EAAmC,CACnC,IAAMmiB,EAAYniB,EAAGse,KAAK,CAAC,qBAAqB,CAAC,EAAE,AACnDwD,CAAAA,EAAatG,MAAM,CAAC2G,EAAU,CAAGD,EAAOpS,KAAK,AACjD,CACJ,GACI+R,GACAC,CAAAA,EAAatG,MAAM,CAAC,wBAAwB,CAAGqG,EAC1ClQ,YAAY,CAAC,UAAY,EAAC,EAE5BmQ,CACX,EA+G8CP,EAAWjgB,KAE7C,GAEGiG,CACX,CASA6a,SAAS9gB,CAAI,CAAEtF,CAAK,CAAEP,CAAO,CAAE+lB,CAAQ,CAAE,CAChCxlB,IAIL,IAAI,CAACyc,SAAS,GAEVnX,AAAS,eAATA,GACA,IAAI,CAAC+gB,UAAU,CAACC,OAAO,CAAC/nB,IAAI,CAAC,IAAI,CAAEyB,EAAOP,EAAS+lB,GAGnDlgB,AAAS,uBAATA,GACA,IAAI,CAAC5F,WAAW,CAAC6mB,UAAU,CAAChoB,IAAI,CAAC,IAAI,CAAEyB,EAAOP,EAAS+lB,GAGvDlgB,AAAS,oBAATA,GACA,IAAI,CAAC5F,WAAW,CAAC4mB,OAAO,CAAC/nB,IAAI,CAAC,IAAI,CAAEyB,EAAOP,EAAS+lB,GAGpDlgB,AAAS,SAATA,GACA,IAAI,CAAC5F,WAAW,CAAC4mB,OAAO,CAAC/nB,IAAI,CAAC,IAAI,CAAEyB,EAAOP,EAAS+lB,EAAU,CAAA,GAElE,IAAI,CAAClgB,IAAI,CAAGA,EAEZ,IAAI,CAACsW,SAAS,CAACjV,KAAK,CAACe,MAAM,CAAG,IAAI,CAACkU,SAAS,CAAC4K,YAAY,CAAG,KAChE,CACJ,CACA7B,GAAaG,GAAMzmB,SAAS,CAAE,CAC1BqB,YAt9BqB,CACrB4mB,QAnJJ,SAAiBtmB,CAAK,CAAEP,CAAO,CAAE+lB,CAAQ,CAAEiB,CAAM,EAC7C,GAAI,CAACzmB,EACD,OAEJ,IAAM2c,EAAW,IAAI,CAACf,SAAS,CAAEna,EAAO,IAAI,CAACA,IAAI,CAE7C0d,EAAS7B,GAA+B,KAAM,CAC9ClX,UAAW,6BACf,EAAG,KAAK,EAAGuW,GACXwC,EAAOjC,WAAW,CAACE,GAAqBkB,cAAc,CAAC7c,CAAI,CAAChC,EAAQinB,OAAO,CAAC,EAAIjnB,EAAQinB,OAAO,EAAI,KAEnGvH,EAAS7B,GAA+B,MAAO,CAC3ClX,UAAY,oDAChB,EAAG,KAAK,EAAGuW,GACX,IAAMgK,EAAYrJ,GAA+B,MAAO,CACpDlX,UAAW,6BACf,EAAG,KAAK,EAAGuW,GACXiB,GAAcrf,IAAI,CAAC,IAAI,CAAE4gB,EAAQnf,EAAO,GAAIP,EAAS,EAAE,CAAE,CAAA,GACzD,IAAI,CAAC6lB,SAAS,CAACqB,EAAWF,EACrBhlB,EAAK6jB,SAAS,EAAI,MAClB7jB,EAAKmlB,UAAU,EAAI,OAASH,EAAS,MAAQ,OAAQ9J,EAAU6I,EACxE,EA+HIe,WAtHJ,SAAoBvmB,CAAK,CAAEP,CAAO,CAAE+lB,CAAQ,EACxC,IAAM/jB,EAAO,IAAI,CAACA,IAAI,CAAEkb,EAAW,IAAI,CAACf,SAAS,CAAEwK,EAAW,IAAI,CAACA,QAAQ,CAAE1J,EAAe,+BAExFC,AAA6C,CAAA,KAA7CA,EAASvW,SAAS,CAAC+C,OAAO,CAACuT,IAC3BC,CAAAA,EAASvW,SAAS,EAAI,IAAMsW,EAAe,2BAA0B,EAGrE1c,GACA2c,CAAAA,EAAShW,KAAK,CAACkgB,GAAG,CAAG7mB,EAAMiL,OAAO,CAAG,GAAK,IAAG,EAGjD,IAAM7I,EAAQkb,GAA+B,IAAK,CAC9ClX,UAAW,6BACf,EAAG,KAAK,EAAGuW,GACXva,EAAMme,YAAY,CAAC,aAAc,mBACjCne,EAAM8a,WAAW,CAACE,GAAqBkB,cAAc,CAACZ,GAEtDjc,CAAI,CAAChC,EAAQinB,OAAO,CAAC,EAAIjnB,EAAQinB,OAAO,CAExCjnB,EAAQ4L,MAAM,EAAI5L,EAAQ4L,MAAM,CAAC,EAAE,CAAC/F,IAAI,CAAE,MAE1C,IAAIiG,EAAS,IAAI,CAAC+Z,SAAS,CAAC3I,EAAUlb,EAAKqlB,UAAU,EAAI,OAAQ,OAAQnK,EAAU,KAC/EyJ,EAAS7nB,IAAI,CAAC,IAAI,CAAE,kBAAmByB,EAAOP,EAAS+lB,EAC3D,EACAja,CAAAA,EAAOnF,SAAS,EAAI,qCACpBkX,GAA+B,OAAQ,CACnClX,UAAW,iBACf,EAAG,CACC6V,gBAAiB,CAAC,IAAI,EAAE,IAAI,CAACN,QAAQ,CAAC,SAAS,CAAC,AACpD,EAAGpQ,GACHA,EAAS,IAAI,CAAC+Z,SAAS,CAAC3I,EAAUlb,EAAKslB,YAAY,EAAI,SAAU,SAAUpK,EAAU6I,GACrFja,EAAOnF,SAAS,EAAI,uCACpBkX,GAA+B,OAAQ,CACnClX,UAAW,iBACf,EAAG,CACC6V,gBAAiB,CAAC,IAAI,EAAE,IAAI,CAACN,QAAQ,CAAC,YAAY,CAAC,AACvD,EAAGpQ,EACP,CAkFA,EAo9BI8a,WAlXoB,CACpBC,QAlhBJ,SAAiCtmB,CAAK,CAAEgnB,CAAQ,CAAExB,CAAQ,EACtD,IACIyB,EADExlB,EAAO,IAAI,CAACA,IAAI,CAEtB,GAAI,CAACzB,EACD,OAGJ,IAAI,CAACkkB,IAAI,CAAC7S,IAAI,CAAC9S,IAAI,CAAC,IAAI,CAAEyB,GAE1B,IAAMknB,EAAiB,IAAI,CAACtL,SAAS,CAChC4E,gBAAgB,CAAC,gCAEtBtB,GAAiBgI,CAAc,CAAC,EAAE,EAClCvF,GAAapjB,IAAI,CAAC,IAAI,CAAEyB,EAAOknB,CAAc,CAAC,EAAE,EAChDjH,GAAiB1hB,IAAI,CAAC,IAAI,CAAEyB,EAAOknB,CAAc,CAAC,EAAE,CAAE,OACtDD,EAAkBC,CAAc,CAAC,EAAE,CAC9B1G,gBAAgB,CAAC,4BAA4B,CAAC,EAAE,CACrD,IAAI,CAAC8E,SAAS,CAAC2B,EAAiBxlB,EAAK6jB,SAAS,EAAI,MAAO,MAAO2B,EAAiBzB,GAEjFtG,GAAiBgI,CAAc,CAAC,EAAE,EAClCjH,GAAiB1hB,IAAI,CAAC,IAAI,CAAEyB,EAAOknB,CAAc,CAAC,EAAE,CAAE,QACtDD,EAAkBC,CAAc,CAAC,EAAE,CAC9B1G,gBAAgB,CAAC,4BAA4B,CAAC,EAAE,CACrD,IAAI,CAAC8E,SAAS,CAAC2B,EAAiBxlB,EAAKmlB,UAAU,EAAI,OAAQ,OAAQK,EAAiBzB,GACpF,IAAI,CAACF,SAAS,CAAC2B,EAAiBxlB,EAAKslB,YAAY,EAAI,SAAU,SAAUE,EAAiBzB,EAC9F,EA0fI2B,UApFJ,WACI,IAAIC,EAAU,EAOd,OANA,IAAI,CAACzkB,MAAM,CAACjC,OAAO,CAAC,AAAC2mB,IACbA,CAAAA,EAAM5H,MAAM,EACZ4H,EAAM5nB,OAAO,CAACggB,MAAM,AAAD,GACnB2H,GAER,GACOA,CACX,CA4EA,EAgXIlD,KA1Oc,CACd7S,KAvDJ,SAAcrR,CAAK,EACf,GAAI,CAACA,EACD,OAEJ,IAAMsnB,EAAkB,IAAI,CAACjB,UAAU,CAACc,SAAS,CAAC5oB,IAAI,CAACyB,GAEjDunB,EAAW1D,GAAYtlB,IAAI,CAAC,IAAI,CAAE,OACxCslB,GAAYtlB,IAAI,CAAC,IAAI,CAAE,OAAQ+oB,GAE/B1D,GAAerlB,IAAI,CAAC,IAAI,EACxBqlB,GAAerlB,IAAI,CAAC,IAAI,EACxBgmB,GAAWhmB,IAAI,CAAC,IAAI,CAAE+oB,GAEtBlD,GAAU7lB,IAAI,CAAC,IAAI,CAAEgpB,EAAU,EACnC,CA0CA,CAyOA,GAsBA,GAAM,CAAEC,SAAAA,EAAQ,CAAE,CAAI7oB,IAGhB,CAAEC,SAAU6oB,EAAyB,CAAEC,WAAAA,EAAU,CAAEvoB,KAAMwoB,EAAqB,CAAE,CAAIhpB,IAmB1F,SAASipB,KACD,IAAI,CAAC5L,KAAK,EACV,IAAI,CAACA,KAAK,CAACQ,UAAU,EAE7B,CAIA,SAASqL,GAA8BC,CAAM,EACrC,AAAC,IAAI,CAAC9L,KAAK,EAEX,CAAA,IAAI,CAACA,KAAK,CAAG,IAjD6B8I,GAiDb,IAAI,CAAC9kB,KAAK,CAAC4b,SAAS,CAAG,IAAI,CAAC5b,KAAK,CAACP,OAAO,CAACwjB,UAAU,CAACtH,QAAQ,EACrF,IAAI,CAAC3b,KAAK,CAACP,OAAO,CAACsoB,UAAU,EAC1B,IAAI,CAAC/nB,KAAK,CAACP,OAAO,CAACsoB,UAAU,CAACC,GAAG,CAACrM,QAAQ,EAC9C,sDAAwD,IAAI,CAAC3b,KAAK,CAAA,EAE1E,IAAI,CAACgc,KAAK,CAACoK,QAAQ,CAAC0B,EAAOG,QAAQ,CAAE,IAAI,CAACjoB,KAAK,CAAE8nB,EAAOroB,OAAO,CAAEqoB,EAAOI,QAAQ,CACpF,CAMA,SAASC,GAAiC9jB,CAAO,CAAEoE,CAAC,EAE5C,AAAC,IAAI,CAAC2f,OAAO,CAAC3f,EAAEQ,MAAM,CAAE,qBACxB5E,EAAQE,KAAK,CAAC,IAAI,CAAEvB,MAAM3E,SAAS,CAACmG,KAAK,CAACjG,IAAI,CAACkG,UAAW,GAElE,CAS6B,IAAM4jB,GAHV,CACrB3jB,QA7CJ,SAAiB4jB,CAAuB,CAAEzjB,CAAY,EAC9C6iB,GAAWF,GAAU,WACrBC,GAA0Ba,EAAyB,aAAcV,IACjEH,GAA0Ba,EAAyB,YAAaT,IAChEF,GAAsB9iB,EAAaxG,SAAS,CAAE,uBAAwB8pB,IAE9E,CAwCA,EAeM,CAAEI,qBAAAA,EAAoB,CAAE,CAAI5pB,IAY5B,CAAE6pB,eAAAA,EAAc,CAAE,CAAI7pB,IAKtB,CAAE8pB,wBAAAA,EAAuB,CAAE5pB,MAAO6pB,EAAgB,CAAE3pB,UAAW4pB,EAAoB,CAAE7c,MAAO8c,EAAgB,CAAE1pB,KAAM2pB,EAAe,CAAE/Y,MAAAA,EAAK,CAAE,CAAInR,IAyBtJ,SAASmqB,GAA0BC,CAAW,CAAE1W,CAAU,EACtD,IAAM2W,EAAgB,CAAC,EAYvB,MAXA,CAAC,SAAU,SAAS,CAACtoB,OAAO,CAAC,AAACkf,IAC1B,IAAMqJ,EAAkBF,CAAW,CAACnJ,EAAK,CAAEsJ,EAAkB7W,CAAU,CAACuN,EAAK,CACzEqJ,IACIC,EACAF,CAAa,CAACpJ,EAAK,CAAG9P,GAAMoZ,GAAiBzX,GAAG,CAAC,CAAC0X,EAAcvoB,IAAMgoB,GAAiBK,CAAe,CAACroB,EAAE,CAAEuoB,IAG3GH,CAAa,CAACpJ,EAAK,CAAGmJ,CAAW,CAACnJ,EAAK,CAGnD,GACOoJ,CACX,CAqBA,MAAMI,WAAmBhhB,EASrB,OAAO1D,QAAQE,CAAU,CAAEykB,CAAuB,CAAExkB,CAAY,CAAE2P,CAAgB,CAAE,CAChFjP,EAA4Bb,OAAO,CAAC0kB,GAAYxkB,EAAYC,GAC5DwW,AA/iD8DnC,GA+iD9BxU,OAAO,CAAC8P,GACxCyB,AAz1E6D1B,GAy1E9B7P,OAAO,CAACE,EAAY4P,GACnD6U,EAAwB3kB,OAAO,CAAC0kB,GAAYxkB,GAC5CyjB,GAAuB3jB,OAAO,CAAC2kB,EAAyBxkB,EAC5D,CAMAoH,YAAYjM,CAAK,CAAEX,CAAW,CAAE,CAC5B,KAAK,GACL,IAAI,CAAC0E,IAAI,CAAG,cAOZ,IAAI,CAAC/D,KAAK,CAAGA,EAOb,IAAI,CAACuC,MAAM,CAAG,EAAE,CAOhB,IAAI,CAAC6M,aAAa,CAAG,EAAE,CACvB,IAAI,CAACrL,IAAI,CAAG,cACZ,IAAI,CAAClB,KAAK,CAAG,GAOb,IAAI,CAACV,MAAM,CAAG,EAAE,CAOhB,IAAI,CAACkJ,MAAM,CAAG,EAAE,CAOhB,IAAI,CAACie,UAAU,CAACjqB,GAOhB,IAAI,CAACA,WAAW,CAAGA,EAGnB,IAAMkqB,EAAkBT,GAA0B,IAAI,CAACrpB,OAAO,CAAEJ,EAChE,CAAA,IAAI,CAACI,OAAO,CAAC0C,MAAM,CAAGonB,EAAgBpnB,MAAM,CAC5C,IAAI,CAAC1C,OAAO,CAAC4L,MAAM,CAAGke,EAAgBle,MAAM,CA0B5C,IAAI,CAACgG,IAAI,CAACrR,EAAO,IAAI,CAACP,OAAO,CACjC,CASA+pB,cAAe,CACX,IAAI,CAACC,WAAW,GACZ,IAAI,CAACC,SAAS,EACd,IAAI,CAACC,SAAS,EACd,IAAI,CAAClqB,OAAO,CAACmG,IAAI,EAEjB,CAAA,IAAI,CAACzF,QAAQ,CAAG,IAAI,CAACH,KAAK,CAACE,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAACypB,UAAU,GAAE,CAEtE,CAIAC,WAAY,CACR,IAAMC,EAAiB,IAAI,CAACrqB,OAAO,CAAC0C,MAAM,EAAI,EAAE,CAChD2nB,EAAcppB,OAAO,CAAC,CAACuB,EAAcrB,KACjC,IAAMwB,EAAQ,IAAI,CAAC2nB,SAAS,CAAC9nB,EAAcrB,GAC3CgoB,GAAiB,CAAA,EAAMkB,CAAa,CAAClpB,EAAE,CAAEwB,EAAM3C,OAAO,CAC1D,EACJ,CAIAuqB,WAAY,CACR,IAAM3e,EAAS,IAAI,CAAC5L,OAAO,CAAC4L,MAAM,EAAI,EAAE,CACxCA,EAAO3K,OAAO,CAAC,CAACuG,EAAcrG,KAC1B,IAAM8F,EAAQ,IAAI,CAACujB,SAAS,CAAChjB,EAAcrG,GAC3CgoB,GAAiB,CAAA,EAAMvd,CAAM,CAACzK,EAAE,CAAE8F,EAAMjH,OAAO,CACnD,EACJ,CAQAsB,SAAU,CACN,IAAMf,EAAQ,IAAI,CAACA,KAAK,CAAEkqB,EAAc,SAAU5U,CAAI,EAClDA,EAAKvU,OAAO,EAChB,EACA,IAAI,CAACoB,MAAM,CAACzB,OAAO,CAACwpB,GACpB,IAAI,CAAC7e,MAAM,CAAC3K,OAAO,CAACwpB,GACpB,IAAI,CAACR,SAAS,CAAG,KACjB,IAAI,CAACC,SAAS,CAAG,KACjBjB,GAAiB1oB,EAAMmqB,eAAe,CAAE,IAAI,CAACC,cAAc,EAC3D,KAAK,CAACrpB,UACN,IAAI,CAAC4O,oBAAoB,GACzB8Y,GAAwB,IAAI,CAAEzoB,EAClC,CAKAkqB,YAAY5U,CAAI,CAAE,CAEdoT,GAAiB,IAAI,CAACpT,EAAKnE,QAAQ,CAAG,IAAI,CAAEmE,GAC5CA,EAAKvU,OAAO,EAChB,CAIA6oB,YAAa,CACT,GAAI,IAAI,CAACF,SAAS,EAAI,IAAI,CAACC,SAAS,CAChC,MAAO,CACHlnB,EAAG,IAAI,CAACinB,SAAS,CAACW,IAAI,CACtB/jB,EAAG,IAAI,CAACqjB,SAAS,CAAC9C,GAAG,CACrBjf,MAAO,IAAI,CAAC8hB,SAAS,CAAC9hB,KAAK,CAC3BF,OAAQ,IAAI,CAACiiB,SAAS,CAACjiB,MAAM,AACjC,CAER,CAKA4iB,eAAetqB,CAAK,CAAEX,CAAW,CAAE,CAC/B,IAAI,CAACiqB,UAAU,CAACjqB,GAChB,IAAMkqB,EAAkBT,GAA0B,IAAI,CAACrpB,OAAO,CAAEJ,EAChE,CAAA,IAAI,CAACI,OAAO,CAAC0C,MAAM,CAAGonB,EAAgBpnB,MAAM,CAC5C,IAAI,CAAC1C,OAAO,CAAC4L,MAAM,CAAGke,EAAgBle,MAAM,CAC5C,IAAI,CAACrL,KAAK,CAAGA,EACb,IAAI,CAACuC,MAAM,CAAG,EAAE,CAChB,IAAI,CAAC6M,aAAa,CAAG,EAAE,CACvB,IAAI,CAACrL,IAAI,CAAG,cACZ,IAAI,CAAC1E,WAAW,CAAGA,EACnB,IAAI,CAAC8C,MAAM,CAAG,EAAE,CAChB,IAAI,CAACkJ,MAAM,CAAG,EAAE,AACpB,CAKAgG,KAAKkZ,CAAkB,CAAEC,CAAY,CAAE3nB,EAAQ,IAAI,CAACA,KAAK,CAAE,CACvD,IAAM7C,EAAQ,IAAI,CAACA,KAAK,CAAEyqB,EAAc,IAAI,CAAChrB,OAAO,CAACkG,SAAS,AAC9D,CAAA,IAAI,CAAC9C,KAAK,CAAGA,EACb,IAAI,CAACkN,UAAU,GACf,IAAI,CAACZ,gBAAgB,GACrB,IAAI,CAAC6a,SAAS,GACd,IAAI,CAACH,SAAS,GACd,IAAI,CAACa,iBAAiB,GACtB,IAAI,CAAC9mB,eAAe,CAAG2kB,GAAqBvoB,EAAOyqB,EACvD,CAKAV,UAAU9nB,CAAY,CAAEY,CAAK,CAAE,CAC3B,IAEkBT,EAAQ,IApwDoC8W,GAowDA,IAAI,CAFlD0P,GAAiB,IAAI,CAACnpB,OAAO,CAACwC,YAAY,CAAE,CACxDsF,oBAAqB,IAAI,CAAC9H,OAAO,CAAC8H,mBAAmB,AACzD,EAAGtF,GAA0EY,GAG7E,OAFAT,EAAM+O,QAAQ,CAAG,QACjB,IAAI,CAAChP,MAAM,CAACxC,IAAI,CAACyC,GACVA,CACX,CAUA6nB,UAAUhjB,CAAY,CAAEpE,CAAK,CAAE,CAC3B,IAAMpD,EAAUmpB,GAAiB,IAAI,CAACnpB,OAAO,CAACwH,YAAY,CAAE,CACxDM,oBAAqB,IAAI,CAAC9H,OAAO,CAAC8H,mBAAmB,AACzD,EAAGN,GAAeP,EAAQ,IAAK0iB,GAAWuB,SAAS,CAAClrB,EAAQ6F,IAAI,CAAC,CAAE,IAAI,CAAE7F,EAASoD,GAGlF,OAFA6D,EAAMyK,QAAQ,CAAG,QACjB,IAAI,CAAC9F,MAAM,CAAC1L,IAAI,CAAC+G,GACVA,CACX,CAIApH,OAAOqG,CAAS,CAAE,CACd,IAAI,CAACoK,UAAU,GACX,AAAC,IAAI,CAACnQ,OAAO,EACb,IAAI,CAACuM,MAAM,GAEX,IAAI,CAAChM,QAAQ,EACb,IAAI,CAACA,QAAQ,CAACwD,OAAO,CAAC,IAAI,CAACimB,UAAU,IAEzC,IAAI,CAACgB,WAAW,CAAC,IAAI,CAACvf,MAAM,CAAE1F,GAC9B,IAAI,CAACilB,WAAW,CAAC,IAAI,CAACzoB,MAAM,CAAEwD,GAC9B,IAAI,CAACyK,mBAAmB,CAACzK,EAC7B,CAKAklB,WAAWvV,CAAI,CAAE3P,CAAS,CAAE,CACxB2P,EAAKvF,UAAU,GACVuF,EAAKtD,aAAa,IAIf,AAACsD,EAAK1V,OAAO,EACb,IAAI,CAACkrB,UAAU,CAACxV,GAEpBA,EAAKhW,MAAM,CAACupB,GAAgBljB,EAAW,CAAA,IAAS2P,EAAK1V,OAAO,CAACyV,MAAM,EAC/DC,EAAK/S,MAAM,CAACX,MAAM,EAClBmpB,AAtUhB,SAA0BzV,CAAI,EAC1B,IAAMlT,EAAQkT,EAAK1V,OAAO,CAAEorB,EAAmB1V,EAAK/S,MAAM,CAAC1B,IAAI,CAAC,AAACgM,GAAWA,AAAyB,CAAA,IAAzBA,EAAMlK,MAAM,CAAC+C,OAAO,EAC5FmH,AAAkB,CAAA,IAAlBA,EAAMnH,OAAO,EACbtD,IACK4oB,EAGI5oB,AAAqB,WAArBA,EAAM6oB,UAAU,EACrB7oB,EAAM8oB,IAAI,GAHV9oB,EAAM+oB,IAAI,GAMtB,EA2TiC7V,IARrB,IAAI,CAAC4U,WAAW,CAAC5U,EAWzB,CAIAsV,YAAYQ,CAAK,CAAEzlB,CAAS,CAAE,CAC1B,IAAI/E,EAAIwqB,EAAMxpB,MAAM,CAGpB,KAAOhB,KACH,IAAI,CAACiqB,UAAU,CAACO,CAAK,CAACxqB,EAAE,CAAE+E,EAElC,CAKAqX,QAAS,CAEL,OAAO,IAAI,CAAChd,KAAK,CAACoF,gBAAgB,CAAC,IAAI,CAC3C,CAIA+G,QAAS,CACL,IAAMjM,EAAW,IAAI,CAACF,KAAK,CAACE,QAAQ,AACpC,CAAA,IAAI,CAACN,OAAO,CAAGM,EACVI,CAAC,CAAC,cACFT,IAAI,CAAC,CACNC,QAAS,EACTS,OAAQ,IAAI,CAACd,OAAO,CAACc,MAAM,CAC3B0qB,WAAY,IAAI,CAACxrB,OAAO,CAACiG,OAAO,CAC5B,UACA,QACR,GACKjF,GAAG,GACR,IAAI,CAAC4qB,WAAW,CAAGnrB,EACdI,CAAC,CAAC,qBACFG,GAAG,CAAC,IAAI,CAACb,OAAO,EACjB,IAAI,CAACH,OAAO,CAACmG,IAAI,EACjB,IAAI,CAACylB,WAAW,CAAC7qB,IAAI,CAAC,IAAI,CAACR,KAAK,CAACC,WAAW,EAEhD,IAAI,CAACqrB,WAAW,CAAGprB,EACdI,CAAC,CAAC,qBACFT,IAAI,CAAC,CAEN0N,WAAY,EACZC,WAAY,CAChB,GACK/M,GAAG,CAAC,IAAI,CAACb,OAAO,EACrB,IAAI,CAAC4pB,YAAY,GACb,IAAI,CAACrpB,QAAQ,EACb,IAAI,CAACP,OAAO,CAACY,IAAI,CAAC,IAAI,CAACL,QAAQ,EAGnC,IAAI,CAACorB,WAAW,CAAC,IAAI,CAAClgB,MAAM,EAC5B,IAAI,CAACkgB,WAAW,CAAC,IAAI,CAACppB,MAAM,EAC5B,IAAI,CAACkG,SAAS,GACd,IAAI,CAACgI,mBAAmB,EAC5B,CAIAya,WAAWxV,CAAI,CAAE,CACbA,EAAKnJ,MAAM,CAACmJ,AAAkB,UAAlBA,EAAKnE,QAAQ,CACrB,IAAI,CAACma,WAAW,CAChB,IAAI,CAACD,WAAW,CACxB,CAIAE,YAAYH,CAAK,CAAE,CACf,IAAIxqB,EAAIwqB,EAAMxpB,MAAM,CACpB,KAAOhB,KACH,IAAI,CAACkqB,UAAU,CAACM,CAAK,CAACxqB,EAAE,CAEhC,CAIA6oB,aAAc,CACV,IAAM+B,EAAQ,IAAI,CAACxrB,KAAK,CAAC4C,KAAK,CAAE6oB,EAAQ,IAAI,CAACzrB,KAAK,CAAC8M,KAAK,CAAE4e,EAAa,AAAC,CAAA,IAAI,CAACjsB,OAAO,CAAC0C,MAAM,EAAI,EAAE,AAAD,EAC3FwpB,MAAM,CAAC,IAAI,CAAClsB,OAAO,CAAC4L,MAAM,EAAI,EAAE,EAChCugB,MAAM,CAAC,CAACC,EAAMC,KACf,IAAMjf,EAAQif,GACTA,CAAAA,EAAajf,KAAK,EACdif,EAAavpB,MAAM,EAAIupB,EAAavpB,MAAM,CAAC,EAAE,EACtD,MAAO,CACHipB,CAAK,CAAC3e,GAASA,EAAMjK,KAAK,CAAC,EAAIipB,CAAI,CAAC,EAAE,CACtCJ,CAAK,CAAC5e,GAASA,EAAMC,KAAK,CAAC,EAAI+e,CAAI,CAAC,EAAE,CACzC,AACL,EAAG,EAAE,CACL,CAAA,IAAI,CAACnC,SAAS,CAAGgC,CAAU,CAAC,EAAE,CAC9B,IAAI,CAAC/B,SAAS,CAAG+B,CAAU,CAAC,EAAE,AAClC,CAIA3Z,2BAA2BrM,CAAO,CAAE,CAChC,IAAMqmB,EAAiC,SAAUzW,CAAI,EACjDA,EAAKvD,0BAA0B,CAACrM,EACpC,EACA,IAAI,CAAC0J,aAAa,CAAC1O,OAAO,CAAC,AAACkP,IACxBA,EAAaxD,aAAa,CAAC1G,EAC/B,GACA,IAAI,CAAC2F,MAAM,CAAC3K,OAAO,CAACqrB,GACpB,IAAI,CAAC5pB,MAAM,CAACzB,OAAO,CAACqrB,EACxB,CAIArB,mBAAoB,CAChB,IAAMnrB,EAAa,IAAI,AACvBA,CAAAA,EAAW6qB,cAAc,CAAG,WACxB,OAAO7qB,EAAW4C,MAAM,CAACypB,MAAM,CAAC,SAAUzpB,CAAM,CAAEC,CAAK,EAInD,OAHI,AAACA,EAAM3C,OAAO,CAACsG,YAAY,EAC3B5D,EAAOxC,IAAI,CAACyC,EAAMxC,OAAO,EAEtBuC,CACX,EAAG,EAAE,CACT,EACA5C,EAAWS,KAAK,CAACmqB,eAAe,CAACxqB,IAAI,CAACJ,EAAW6qB,cAAc,CACnE,CAOAd,WAAWjqB,CAAW,CAAE,CACpB,IAAI,CAACI,OAAO,CAAGmpB,GAEf,IAAI,CAACJ,cAAc,CAEnB,AAACnpB,EAAYiG,IAAI,EACb,IAAI,CAACkjB,cAAc,CAACnjB,KAAK,CAAChG,EAAYiG,IAAI,CAAC,EAAK,CAAC,EAAGjG,EAC5D,CAQA+M,cAAc1G,CAAO,CAAE,CACnB,IAAMjG,EAAU,IAAI,CAACA,OAAO,CAAEwjB,EAAa,IAAI,CAACjjB,KAAK,CAACglB,kBAAkB,CAAEiG,EAAapC,GAAgBnjB,EAAS,CAACjG,EAAQiG,OAAO,EAEhI,GADA,IAAI,CAAC9F,OAAO,CAACC,IAAI,CAAC,aAAcorB,EAAa,UAAY,UACrD,CAACA,EAAY,CACb,IAAMc,EAAiC,SAAUzW,CAAI,EACjDA,EAAKvD,0BAA0B,CAACkZ,EACpC,EACA,IAAI,CAAC5f,MAAM,CAAC3K,OAAO,CAACqrB,GACpB,IAAI,CAAC5pB,MAAM,CAACzB,OAAO,CAACqrB,GAChB9I,EAAW8B,gBAAgB,GAAK,IAAI,EACpC9B,EAAWjH,KAAK,EAChBiH,AAA0B,uBAA1BA,EAAWjH,KAAK,CAAC1W,IAAI,EACrBqjB,GAAqB1F,EAAY,aAEzC,CACAxjB,EAAQiG,OAAO,CAAGulB,CACtB,CAUA5e,OAAOhN,CAAW,CAAEC,CAAM,CAAE,CACxB,IAAMU,EAAQ,IAAI,CAACA,KAAK,CAAEupB,EAAkBT,GAA0B,IAAI,CAACzpB,WAAW,CAAEA,GAAc2sB,EAAmBhsB,EAAMN,WAAW,CAACyJ,OAAO,CAAC,IAAI,EAAG1J,EAAUmpB,GAAiB,CAAA,EAAM,IAAI,CAACvpB,WAAW,CAAEA,EAC7MI,CAAAA,EAAQ0C,MAAM,CAAGonB,EAAgBpnB,MAAM,CACvC1C,EAAQ4L,MAAM,CAAGke,EAAgBle,MAAM,CACvC,IAAI,CAACtK,OAAO,GACZ,IAAI,CAACupB,cAAc,CAACtqB,EAAOP,GAC3B,IAAI,CAAC4R,IAAI,CAACrR,EAAOP,GAEjBO,EAAMP,OAAO,CAACC,WAAW,CAACssB,EAAiB,CAAG,IAAI,CAACvsB,OAAO,CAC1D,IAAI,CAACiK,UAAU,CAAG,CAAA,EACdmf,GAAgBvpB,EAAQ,CAAA,IACxBU,EAAMc,eAAe,GAEzB6nB,GAAqB,IAAI,CAAE,eAC3B,IAAI,CAACjf,UAAU,CAAG,CAAA,CACtB,CACJ,CAIA0f,GAAW6C,YAAY,CAAGjgB,EAI1Bod,GAAWzc,SAAS,CA1vHwCA,EAkwH5Dyc,GAAWuB,SAAS,CAAG,CACnB,KApsFiE7U,GAqsFjE,OAnlFmEK,GAolFnE,QA73EoEK,GA83EpE,KAjzFiEjC,GAkzFjE,MAp6DkE2G,EAq6DtE,EAIAkO,GAAW/jB,KAAK,CAAG,CAAC,EACpB+jB,GAAW/qB,SAAS,CAACmqB,cAAc,CAAG/iB,EACtC+iB,GAAe9oB,WAAW,CAAG+F,EAQ7B2jB,GAAW/qB,SAAS,CAAC6K,YAAY,CAAG,CAAC,MAAO,cAAe,OAAQ,SAAS,CAC5E8H,EAA0BtM,OAAO,CAAC0kB,KAyElC,AAAC,SAAUpsB,CAA0B,EAqBjCA,EAA2B0H,OAAO,CANlC,SAAiB1E,CAAK,EAIlB,OAHI,AAACA,EAAMijB,UAAU,EACjBjjB,CAAAA,EAAMijB,UAAU,CAAG,IAAIiJ,EAAUlsB,EAAK,EAEnCA,CACX,CAYA,OAAMksB,EAMFjgB,YAAYjM,CAAK,CAAE,CACf,IAAI,CAACmsB,OAAO,CAAG,EAAE,CACjB,IAAI,CAACnsB,KAAK,CAAGA,CACjB,CAaAosB,UAAUC,CAAQ,CAAE,CAChB,IAAI,CAACrsB,KAAK,CAACijB,UAAU,CAACkJ,OAAO,CAACxsB,IAAI,CAAC0sB,EACvC,CAIAhgB,OAAO5M,CAAO,CAAEH,CAAM,CAAE,CACpB,IAAI,CAAC6sB,OAAO,CAACzrB,OAAO,CAAC,AAAC2rB,IAClBA,EAAS9tB,IAAI,CAAC,IAAI,CAACyB,KAAK,CAAEP,EAASH,EACvC,EACJ,CACJ,CACAtC,EAA2BkvB,SAAS,CAAGA,CAC3C,EAAGlvB,GAA+BA,CAAAA,EAA6B,CAAC,CAAA,GAMnC,IAAMsvB,GAAoCtvB,EAcjE,CAAEwI,QAAS+mB,EAAmC,CAAE9T,SAAU+T,EAAoC,CAAEttB,KAAMutB,EAAgC,CAAE,CAAI9tB,IAW5I+tB,GAAyB,CAC3B1mB,gBAAiB,SACjBC,YAAa,SACbC,aAAc,SACdY,MAAO,SACPM,KAAM,SACNR,SAAU,SACVzE,OAAQ,SACRyd,KAAM,SACN1Y,OAAQ,SACRylB,MAAO,QACX,EAgEmCC,GALA,CAC/BF,uBAAAA,GACAG,gBA3CJ,SAAyBC,CAAM,EAC3B,OAAOA,EAAO3M,MAAM,CAAC,AAAC4M,IAClB,IAAMC,EAAWD,EAAME,IAAI,CAACC,WAAW,GAAIC,EAAUH,EAASI,GAAG,CAAEC,EAAUL,EAAS5pB,GAAG,CAGzFkqB,EAAiBb,GAAiCM,EAAME,IAAI,CAACK,cAAc,CAAE,GAC7E,OAAOd,GAAqCW,IAAYX,GAAqCa,IACzFN,EAAMjZ,KAAK,EAAKqZ,EAAUG,GAC1BP,EAAMjZ,KAAK,EAAKuZ,EAAUC,GAE1B,CAACP,EAAME,IAAI,CAACxtB,OAAO,CAAC8tB,UAAU,AACtC,EAAE,CAAC,EAAE,AACT,EAgCIC,aApBJ,SAAsB3vB,CAAG,CAAEiW,CAAK,EAC5B,IAAM2Z,EAAiBf,EAAsB,CAAC7uB,EAAI,CAC9C6vB,EAAY,OAAO5Z,EAIvB,OAHIyY,GAAoCkB,IACpCC,CAAAA,EAAYD,CAAa,EAEtB,CAAA,CACH,OAAU,OACV,OAAU,SACV,QAAW,UACf,CAAA,CAAC,CAACC,EAAU,AAChB,CAUA,EAeM,CAAEb,gBAAiBc,EAA0C,CAAE,CAAGf,GAElE,CAAEnU,SAAUmV,EAAmC,CAAE9hB,MAAO+hB,EAAgC,CAAE,CAAIlvB,IA8XjEmvB,GAJD,CAC9BrsB,KAlXS,CAQTwhB,WAAY,CAMRjH,MAAO,CACH+R,aAAc,gBACdC,MAAO,QACP5X,OAAQ,SACR6X,QAAS,UACTC,UAAW,YACX9rB,MAAO,QACP6E,aAAc,gBACdknB,YAAa,UACb/mB,KAAM,OACNkR,OAAQ,OACRnR,YAAa,aACbD,OAAQ,aACRylB,MAAO,QACP/M,KAAM,OACN3d,aAAc,gBACdE,OAAQ,SACR6D,gBAAiB,mBACjBooB,iBAAkB,oBAClBnoB,YAAa,eACbC,aAAc,gBACdC,YAAa,eACbQ,MAAO,QACPH,QAAS,UACTI,SAAU,YACVE,MAAO,QACPY,OAAQ,SACR2D,OAAQ,eACZ,CACJ,CACJ,EAuUI4X,WAlUe,CAWfoL,kBAAmB,gCA6BnBC,SAAU,CAQNC,iBAAkB,CAEdnoB,UAAW,+BAEXooB,MAAO,SAAU/lB,CAAC,EACd,IAAMqkB,EAAS,IAAI,CAAC9sB,KAAK,CAAC+I,OAAO,EAAE0lB,eAAehmB,GAAIimB,EAAU5B,GAAUa,GAA2Cb,EAAOlqB,KAAK,EAAG+rB,EAAU7B,GAAUa,GAA2Cb,EAAOhgB,KAAK,EAAGmW,EAAa,IAAI,CAACjjB,KAAK,CAACP,OAAO,CAACwjB,UAAU,CAE5P,GAAI,AAACyL,GAAYC,EAGjB,OAAO,IAAI,CAAC3uB,KAAK,CAAC+E,aAAa,CAAC8oB,GAAiC,CAC7DnH,QAAS,SACTphB,KAAM,kBACN+F,OAAQ,CAAC,CACD/F,KAAM,SACNuH,MAAO,CACHpK,EAAGisB,EAAQ5a,KAAK,CAChBxN,EAAGqoB,EAAQ7a,KAAK,CAChBlR,MAAO8rB,EAAQzB,IAAI,CAACpqB,KAAK,CACzBiK,MAAO6hB,EAAQ1B,IAAI,CAACpqB,KAAK,AAC7B,EACAwE,EAAG,CACP,EAAE,AACV,EAAG4b,EAAW2L,kBAAkB,CAAE3L,EAAWqL,QAAQ,CAACC,gBAAgB,CACjEK,kBAAkB,EAC3B,EAEAC,MAAO,CACH,SAAUpmB,CAAC,CAAElJ,CAAU,EACnB,IAEIsb,EAFExP,EAAS9L,EAAWE,OAAO,CAAC4L,MAAM,CAAEyjB,EAAiB,AAACzjB,GAAUA,CAAM,CAAC,EAAE,EAAIA,CAAM,CAAC,EAAE,CAACwB,KAAK,EAC9F,CAAC,EAEL,GAAI+gB,GAAoCkB,EAAclsB,KAAK,GACvDgrB,GAAoCkB,EAAchiB,KAAK,EAAG,CAC1D,IAAMrC,EAAW,IAAI,CAACzK,KAAK,CAACyK,QAAQ,CAAEhI,EAAI,IAAI,CAACzC,KAAK,CAAC4C,KAAK,CAACksB,EAAclsB,KAAK,CAAC,CAC1EqL,QAAQ,CAAC6gB,EAAcrsB,CAAC,EAAG6D,EAAI,IAAI,CAACtG,KAAK,CAAC8M,KAAK,CAACgiB,EAAchiB,KAAK,CAAC,CACpEmB,QAAQ,CAAC6gB,EAAcxoB,CAAC,EAC7BuU,EAAW1X,KAAKC,GAAG,CAACD,KAAK6T,IAAI,CAAC7T,KAAK4rB,GAAG,CAACtkB,EAAWnE,EAAImC,EAAE8B,MAAM,CAAG9H,EAAIgG,EAAE8B,MAAM,CAAE,GAC3EpH,KAAK4rB,GAAG,CAACtkB,EAAWhI,EAAIgG,EAAE4B,MAAM,CAAG/D,EAAImC,EAAE4B,MAAM,CAAE,IAAK,EAC9D,CACA9K,EAAW8M,MAAM,CAAC,CACdhB,OAAQ,CAAC,CACDhE,EAAGwT,CACP,EAAE,AACV,EACJ,EACH,AACL,EASAmU,kBAAmB,CACf5oB,UAAW,gCACXooB,MAAO,SAAU/lB,CAAC,EACd,IAAMqkB,EAAS,IAAI,CAAC9sB,KAAK,CAAC+I,OAAO,EAAE0lB,eAAehmB,GAAIimB,EAAU5B,GAAUa,GAA2Cb,EAAOlqB,KAAK,EAAG+rB,EAAU7B,GAAUa,GAA2Cb,EAAOhgB,KAAK,EAAGmW,EAAa,IAAI,CAACjjB,KAAK,CAACP,OAAO,CAACwjB,UAAU,CAC5P,GAAI,AAACyL,GAAYC,EAGjB,OAAO,IAAI,CAAC3uB,KAAK,CAAC+E,aAAa,CAAC8oB,GAAiC,CAC7DnH,QAAS,UACTphB,KAAM,kBACN+F,OAAQ,CACJ,CACI/F,KAAM,UACN1C,MAAO8rB,EAAQzB,IAAI,CAACpqB,KAAK,CACzBiK,MAAO6hB,EAAQ1B,IAAI,CAACpqB,KAAK,CACzBN,OAAQ,CAAC,CACDE,EAAGisB,EAAQ5a,KAAK,CAChBxN,EAAGqoB,EAAQ7a,KAAK,AACpB,EAAG,CACCrR,EAAGisB,EAAQ5a,KAAK,CAChBxN,EAAGqoB,EAAQ7a,KAAK,AACpB,EAAE,CACN6D,GAAI,CACR,EACH,AACL,EAAGsL,EAAW2L,kBAAkB,CAAE3L,EAAWqL,QAAQ,CAACU,iBAAiB,CAClEJ,kBAAkB,EAC3B,EACAC,MAAO,CACH,SAAUpmB,CAAC,CAAElJ,CAAU,EACnB,IAAM0J,EAAS1J,EAAW8L,MAAM,CAAC,EAAE,CAAEyJ,EAAW7L,EAAO8O,mBAAmB,CAAC9O,EAAO1G,MAAM,CAAC,EAAE,EAC3F0G,EAAO4H,cAAc,CAACpI,EAAE8B,MAAM,CAAGuK,EAASrS,CAAC,CAAEgG,EAAE4B,MAAM,CAAGyK,EAASxO,CAAC,CAAE,GACpE2C,EAAO3J,MAAM,CAAC,CAAA,EAClB,EACA,SAAUmJ,CAAC,CAAElJ,CAAU,EACnB,IAAM0J,EAAS1J,EAAW8L,MAAM,CAAC,EAAE,CAAEyJ,EAAW7L,EAAO8O,mBAAmB,CAAC9O,EAAO1G,MAAM,CAAC,EAAE,EAAG2U,EAAYjO,EAAO8O,mBAAmB,CAAC9O,EAAO1G,MAAM,CAAC,EAAE,EAAG0sB,EAAOhmB,EAAOyN,mBAAmB,CAAC5B,EAAUoC,EAAWzO,EAAE8B,MAAM,CAAE9B,EAAE4B,MAAM,EAAGyC,EAAQ7D,EAAO4O,QAAQ,GAAIqX,EAAQ/rB,KAAK4T,GAAG,CAACjK,EAAMsB,OAAO,CAAC,GAAKtB,EAAMsB,OAAO,CAAC6gB,IACjThmB,EAAOkP,UAAU,CAAC+W,GAClBjmB,EAAO3J,MAAM,CAAC,CAAA,EAClB,EACH,AACL,EAQA6vB,oBAAqB,CAEjB/oB,UAAW,kCAEXooB,MAAO,SAAU/lB,CAAC,EACd,IAAMqkB,EAAS,IAAI,CAAC9sB,KAAK,CAAC+I,OAAO,EAAE0lB,eAAehmB,GAAIimB,EAAU5B,GAAUa,GAA2Cb,EAAOlqB,KAAK,EAAG+rB,EAAU7B,GAAUa,GAA2Cb,EAAOhgB,KAAK,EAE/M,GAAI,CAAC4hB,GAAW,CAACC,EACb,OAEJ,IAAMlsB,EAAIisB,EAAQ5a,KAAK,CAAExN,EAAIqoB,EAAQ7a,KAAK,CAAElR,EAAQ8rB,EAAQzB,IAAI,CAACpqB,KAAK,CAAEiK,EAAQ6hB,EAAQ1B,IAAI,CAACpqB,KAAK,CAAEogB,EAAa,IAAI,CAACjjB,KAAK,CAACP,OAAO,CAACwjB,UAAU,CAC9I,OAAO,IAAI,CAACjjB,KAAK,CAAC+E,aAAa,CAAC8oB,GAAiC,CAC7DnH,QAAS,YACTphB,KAAM,kBACN+F,OAAQ,CAAC,CACD/F,KAAM,OACN/C,OAAQ,CACJ,CAAEK,MAAAA,EAAOkK,MAAAA,EAAOrK,EAAAA,EAAG6D,EAAAA,CAAE,EACrB,CAAE1D,MAAAA,EAAOkK,MAAAA,EAAOrK,EAAAA,EAAG6D,EAAAA,CAAE,EACrB,CAAE1D,MAAAA,EAAOkK,MAAAA,EAAOrK,EAAAA,EAAG6D,EAAAA,CAAE,EACrB,CAAE1D,MAAAA,EAAOkK,MAAAA,EAAOrK,EAAAA,EAAG6D,EAAAA,CAAE,EACrB,CAAEsH,QAAS,GAAI,EAClB,AACL,EAAE,AACV,EAAGqV,EACE2L,kBAAkB,CAAE3L,EACpBqL,QAAQ,CACRa,mBAAmB,CACnBP,kBAAkB,EAC3B,EAEAC,MAAO,CACH,SAAUpmB,CAAC,CAAElJ,CAAU,EACnB,IAAM8L,EAAS9L,EAAWE,OAAO,CAAC4L,MAAM,CAAE9I,EAAU,AAAC8I,GAAUA,CAAM,CAAC,EAAE,EAAIA,CAAM,CAAC,EAAE,CAAC9I,MAAM,EACxF,EAAE,CAAGuqB,EAAS,IAAI,CAAC9sB,KAAK,CAAC+I,OAAO,EAAE0lB,eAAehmB,GAAIimB,EAAU5B,GAAUa,GAA2Cb,EAAOlqB,KAAK,EAAG+rB,EAAU7B,GAAUa,GAA2Cb,EAAOhgB,KAAK,EAClN,GAAI4hB,GAAWC,EAAS,CACpB,IAAMlsB,EAAIisB,EAAQ5a,KAAK,CAAExN,EAAIqoB,EAAQ7a,KAAK,AAE1CvR,CAAAA,CAAM,CAAC,EAAE,CAACE,CAAC,CAAGA,EAEdF,CAAM,CAAC,EAAE,CAACE,CAAC,CAAGA,EACdF,CAAM,CAAC,EAAE,CAAC+D,CAAC,CAAGA,EAEd/D,CAAM,CAAC,EAAE,CAAC+D,CAAC,CAAGA,EACd/G,EAAW8M,MAAM,CAAC,CACdhB,OAAQ,CAAC,CACD9I,OAAQA,CACZ,EAAE,AACV,EACJ,CACJ,EACH,AACL,EAOA6sB,gBAAiB,CAEbhpB,UAAW,8BAEXooB,MAAO,SAAU/lB,CAAC,EACd,IAAMqkB,EAAS,IAAI,CAAC9sB,KAAK,CAAC+I,OAAO,EAAE0lB,eAAehmB,GAAIimB,EAAU5B,GAAUa,GAA2Cb,EAAOlqB,KAAK,EAAG+rB,EAAU7B,GAAUa,GAA2Cb,EAAOhgB,KAAK,EAAGmW,EAAa,IAAI,CAACjjB,KAAK,CAACP,OAAO,CAACwjB,UAAU,CAE5P,GAAI,AAACyL,GAAYC,EAGjB,OAAO,IAAI,CAAC3uB,KAAK,CAAC+E,aAAa,CAAC8oB,GAAiC,CAC7DnH,QAAS,QACTphB,KAAM,kBACNrD,aAAc,CACVqW,OAAQ,UACR/R,SAAU,OACVX,KAAM,CAAA,CACV,EACAzD,OAAQ,CAAC,CACD0K,MAAO,CACHjK,MAAO8rB,EAAQzB,IAAI,CAACpqB,KAAK,CACzBiK,MAAO6hB,EAAQ1B,IAAI,CAACpqB,KAAK,CACzBJ,EAAGisB,EAAQ5a,KAAK,CAChBxN,EAAGqoB,EAAQ7a,KAAK,AACpB,CACJ,EAAE,AACV,EAAGmP,EACE2L,kBAAkB,CAAE3L,EACpBqL,QAAQ,CACRc,eAAe,CACfR,kBAAkB,EAC3B,CACJ,CACJ,EAmDApnB,OAAQ,CAAC,EAcTonB,mBAAoB,CAChBjpB,UAAW,CACP0pB,MAAO,CACX,CACJ,CACJ,CASA,EAgBM,CAAE/F,WAAAA,EAAU,CAAE,CAAI3qB,IAElB,CAAE2Z,OAAQgX,EAAyB,CAAE,CAAIjX,KAEzC,CAAEmP,SAAU+H,EAA2B,CAAE1nB,IAAK2nB,EAAsB,CAAEC,IAAAA,EAAG,CAAE,CAAI9wB,IAG/E,CAAEkuB,gBAAiB6C,EAAkC,CAAElC,aAAcmC,EAA+B,CAAE,CAAG/C,GAEzG,CAAEhuB,SAAUgxB,EAA2B,CAAE/vB,KAAAA,EAAI,CAAE2F,QAASqqB,EAA0B,CAAE9wB,UAAW+wB,EAA4B,CAAE9wB,QAAS+wB,EAA0B,CAAEC,WAAAA,EAAU,CAAEvX,SAAUwX,EAA2B,CAAEhxB,SAAUixB,EAA2B,CAAEpkB,MAAOqkB,EAAwB,CAAEloB,WAAYmoB,EAA6B,CAAElxB,KAAMmxB,EAAuB,CAAE3I,WAAY4I,EAA6B,CAAE,CAAI3xB,IA+B5Z,SAAS4xB,KACD,IAAI,CAACvwB,KAAK,CAACglB,kBAAkB,EAC7B,IAAI,CAAChlB,KAAK,CAACglB,kBAAkB,CAACwL,kBAAkB,EAExD,CAIA,SAASC,KACD,IAAI,CAACzL,kBAAkB,EACvB,IAAI,CAACA,kBAAkB,CAACjkB,OAAO,EAEvC,CAIA,SAAS2vB,KACL,IAAMjxB,EAAU,IAAI,CAACA,OAAO,CACxBA,GAAWA,EAAQwjB,UAAU,EAAIxjB,EAAQwjB,UAAU,CAACqL,QAAQ,GAC5D,IAAI,CAACtJ,kBAAkB,CAAG,IAAI2L,GAAmB,IAAI,CAAElxB,EAAQwjB,UAAU,EACzE,IAAI,CAAC+B,kBAAkB,CAAC4L,UAAU,GAClC,IAAI,CAAC5L,kBAAkB,CAAC6L,UAAU,GAE1C,CAIA,SAASC,KACL,IAAM9L,EAAqB,IAAI,CAACA,kBAAkB,CAAE+L,EAAoB,0BACxE,GAAI,IAAI,EAAI/L,EAAoB,CAG5B,IAAIgM,EAAiB,CAAA,EAMrB,GALA,IAAI,CAACruB,MAAM,CAACjC,OAAO,CAAC,AAACiC,IACb,CAACA,EAAOlD,OAAO,CAAC8tB,UAAU,EAAI5qB,EAAO+C,OAAO,EAC5CsrB,CAAAA,EAAiB,CAAA,CAAG,CAE5B,GACI,IAAI,CAAChM,kBAAkB,EACvB,IAAI,CAACA,kBAAkB,CAACpJ,SAAS,EACjC,IAAI,CAACoJ,kBAAkB,CAACpJ,SAAS,CAAC,EAAE,CAAE,CACtC,IAAMA,EAAY,IAAI,CAACoJ,kBAAkB,CAACpJ,SAAS,CAAC,EAAE,CACtDwU,GAA8BpL,EAAmBiM,eAAe,CAAE,CAACnd,EAAOjW,KAGtE,IAAMqzB,EAAatV,EAAU4E,gBAAgB,CAAC,IAAM3iB,GACpD,GAAIqzB,EACA,IAAK,IAAItwB,EAAI,EAAGA,EAAIswB,EAAWtvB,MAAM,CAAEhB,IAAK,CACxC,IAAM2K,EAAS2lB,CAAU,CAACtwB,EAAE,CAAEuwB,EAAM5lB,EAAOnF,SAAS,AAC1B,CAAA,WAAtB0N,EAAMsd,WAAW,EAOXJ,EAOFG,AAAmC,KAAnCA,EAAIhoB,OAAO,CAAC4nB,IACZxlB,EAAOwR,SAAS,CAACC,MAAM,CAAC+T,GAPxBI,AAAmC,KAAnCA,EAAIhoB,OAAO,CAAC4nB,IACZxlB,CAAAA,EAAOnF,SAAS,EAAI,IAAM2qB,CAAgB,CAStD,CAER,EACJ,CACJ,CACJ,CAIA,SAASM,KACL,IAAI,CAACb,kBAAkB,EAC3B,CAIA,SAASc,KACL,IAAI,CAACjM,qBAAqB,CAAG,IACjC,CAKA,SAASkM,GAAqBC,CAAc,EACxC,IAmDIC,EAAaC,EAnDXC,EAAgBH,EAAenzB,SAAS,CAACmqB,cAAc,CAAChhB,MAAM,EAChEgqB,EAAenzB,SAAS,CAACmqB,cAAc,CAAChhB,MAAM,CAACoqB,KAAK,CAKxD,SAASC,EAAmBC,CAAc,EACtC,IAAMvyB,EAAa,IAAI,CAAE0jB,EAAa1jB,EAAWS,KAAK,CAACglB,kBAAkB,CAAE+M,EAAiB9O,EAAW8B,gBAAgB,AACnH4M,CAAAA,GACAA,EAAcpzB,IAAI,CAACgB,EAAYuyB,GAE/BC,IAAmBxyB,GAEnB0jB,EAAWuN,kBAAkB,GAC7BvN,EAAW8B,gBAAgB,CAAGxlB,EAC9BA,EAAWwS,0BAA0B,CAAC,CAAA,GACtC+d,GAA6B7M,EAAY,YAAa,CAClD1jB,WAAYA,EACZ0oB,SAAU,qBACVxoB,QAASwjB,EAAW+O,kBAAkB,CAACzyB,GACvC2oB,SAAU,SAAU+J,CAAI,EACpB,GAAIA,AAAoB,WAApBA,EAAKlM,UAAU,CACf9C,EAAW8B,gBAAgB,CAAG,CAAA,EAC9B9B,EAAWjjB,KAAK,CAACoF,gBAAgB,CAAC7F,OAEjC,CACD,IAAMuoB,EAAS,CAAC,EAChB7E,EAAWiP,eAAe,CAACD,EAAKzS,MAAM,CAAEsI,GACxC7E,EAAWuN,kBAAkB,GAC7B,IAAMrC,EAAcrG,EAAOqG,WAAW,AACN,CAAA,YAA5B5uB,EAAWE,OAAO,CAAC6F,IAAI,GAGvB6oB,EAAYgE,UAAU,CAACC,OAAO,CAAIjE,AACb,IADaA,EAAYgE,UAAU,CACnDhrB,WAAW,CAChBgnB,EAAYkE,UAAU,CAACD,OAAO,CAAIjE,AACb,IADaA,EAAYkE,UAAU,CACnDlrB,WAAW,EAEpB5H,EAAW8M,MAAM,CAACyb,EACtB,CACJ,CACJ,IAIAgI,GAA6B7M,EAAY,cAG7C6O,EAAe/M,gBAAgB,CAAG,CAAA,CACtC,CAoBAoL,GAAyB,CAAA,EAAMqB,EAAenzB,SAAS,CAACmqB,cAAc,CAAChhB,MAAM,CAAE,CAC3EoqB,MAAOC,EACPS,WAhBJ,SAAoB7pB,CAAC,EACjBgpB,EAAchpB,EAAE8pB,OAAO,CAAC,EAAE,CAACC,OAAO,CAClCd,EAAcjpB,EAAE8pB,OAAO,CAAC,EAAE,CAACE,OAAO,AACtC,EAcIC,SAVJ,SAA2BjqB,CAAC,EAGpB,AAFagpB,GAActuB,KAAK6T,IAAI,CAAC7T,KAAK4rB,GAAG,CAAC0C,EAAchpB,EAAEkqB,cAAc,CAAC,EAAE,CAACH,OAAO,CAAE,GACzFrvB,KAAK4rB,GAAG,CAAC2C,EAAcjpB,EAAEkqB,cAAc,CAAC,EAAE,CAACF,OAAO,CAAE,KAAO,GAE3DZ,EAAmBtzB,IAAI,CAAC,IAAI,CAAEkK,EAEtC,CAKA,EACJ,CASA,MAAMkoB,GAMF,OAAOjsB,QAAQC,CAAe,CAAEC,CAAU,CAAE,CACpC0rB,GAA8Bf,GAA6B,wBAC3DK,GAA4BjrB,EAAiB,SAAU4rB,IAEvDgB,GAAqB5sB,GAErByrB,GAA8BzrB,EAAgBU,KAAK,CAAE,AAACmsB,IAClDD,GAAqBC,EACzB,GACA5B,GAA4BhrB,EAAY,UAAW6rB,IACnDb,GAA4BhrB,EAAY,OAAQ8rB,IAChDd,GAA4BhrB,EAAY,SAAUksB,IAClDlB,GAA4Be,GAAoB,aAAcU,IAC9DzB,GAA4Be,GAAoB,iBAAkBW,IAClEhI,GAAWwE,IAEnB,CAMA7hB,YAAYjM,CAAK,CAAEP,CAAO,CAAE,CACxB,IAAI,CAACwxB,eAAe,CAAG,KAAK,EAC5B,IAAI,CAACjxB,KAAK,CAAGA,EACb,IAAI,CAACP,OAAO,CAAGA,EACf,IAAI,CAACmzB,cAAc,CAAG,EAAE,CACxB,IAAI,CAAChX,SAAS,CACV,IAAI,CAAC5b,KAAK,CAAC4b,SAAS,CAACiX,sBAAsB,CAAC,IAAI,CAACpzB,OAAO,CAAC4uB,iBAAiB,EAAI,IAC9E,AAAC,IAAI,CAACzS,SAAS,CAACha,MAAM,EACtB,CAAA,IAAI,CAACga,SAAS,CAAG4T,GAAuBqD,sBAAsB,CAAC,IAAI,CAACpzB,OAAO,CAAC4uB,iBAAiB,EAAI,GAAE,CAE3G,CAMAyE,UAAUrqB,CAAC,CAAE,CACT,IAAMqkB,EAAS,IAAI,CAAC9sB,KAAK,CAAC+I,OAAO,EAAE0lB,eAAehmB,GAClD,MAAO,CACHqkB,GAAU4C,GAAmC5C,EAAOlqB,KAAK,EACzDkqB,GAAU4C,GAAmC5C,EAAOhgB,KAAK,EAC5D,AACL,CAOA8jB,YAAa,CACT,IAAM3N,EAAa,IAAI,CAAEjjB,EAAQijB,EAAWjjB,KAAK,CAAE+yB,EAAoB9P,EAAWrH,SAAS,CAAEnc,EAAUwjB,EAAWxjB,OAAO,AAEzHwjB,CAAAA,EAAWgO,eAAe,CAAG,CAAC,EAC9Bb,GAA+B3wB,EAAQ6uB,QAAQ,EAAI,CAAC,EAAI,AAACxa,IACrDmP,EAAWgO,eAAe,CAACnd,EAAM1N,SAAS,CAAC,CAAG0N,CAClD,GAEA,EAAE,CAACpT,OAAO,CAACnC,IAAI,CAACw0B,EAAmB,AAACC,IAChC/P,EAAW2P,cAAc,CAACjzB,IAAI,CAACiwB,GAA4BoD,EAAc,QAAS,AAAChyB,IAC/E,IAAMstB,EAAWrL,EAAWgQ,eAAe,CAACD,EAAchyB,EACtDstB,CAAAA,GACC,CAACA,EAAS/iB,MAAM,CAACwR,SAAS,CACtBmW,QAAQ,CAAC,4BACdjQ,EAAWkQ,mBAAmB,CAAC7E,EAAS/iB,MAAM,CAAE+iB,EAAS9mB,MAAM,CAAExG,EAEzE,GACJ,GACAovB,GAA+B3wB,EAAQ+H,MAAM,EAAI,CAAC,EAAI,CAACge,EAAUrJ,KACzD6T,GAAWxK,IACXvC,EAAW2P,cAAc,CAACjzB,IAAI,CAACiwB,GAA4B3M,EAAY9G,EAAWqJ,EAAU,CAAE7c,QAAS,CAAA,CAAM,GAErH,GACAsa,EAAW2P,cAAc,CAACjzB,IAAI,CAACiwB,GAA4B5vB,EAAM4b,SAAS,CAAE,QAAS,SAAUnT,CAAC,EACxF,CAACzI,EAAM8I,WAAW,EAClB9I,EAAM+K,YAAY,CAACtC,EAAE8B,MAAM,CAAGvK,EAAMgL,QAAQ,CAAEvC,EAAE4B,MAAM,CAAGrK,EAAMiL,OAAO,CAAE,CACpEC,gBAAiB,CAAA,CACrB,IACA+X,EAAWmQ,kBAAkB,CAAC,IAAI,CAAE3qB,EAE5C,IACAwa,EAAW2P,cAAc,CAACjzB,IAAI,CAACiwB,GAA4B5vB,EAAM4b,SAAS,CAAE,AAACjd,IAA+EmJ,aAAa,CAAG,YAAc,YAAa,SAAUW,CAAC,EAC9Mwa,EAAWoQ,0BAA0B,CAAC,IAAI,CAAE5qB,EAChD,EAAG,AAAC9J,IAA+EmJ,aAAa,CAAG,CAAEa,QAAS,CAAA,CAAM,EAAI,KAAK,GACjI,CAOAkoB,YAAa,CACT,IAAM5N,EAAa,IAAI,CACvBqJ,GACK5nB,OAAO,CAAC,IAAI,CAAC1E,KAAK,EAAEijB,UAAU,CAC9BmJ,SAAS,CAAC,AAAC3sB,IACZwjB,EAAW5W,MAAM,CAAC5M,EACtB,EACJ,CAiBA0zB,oBAAoB5nB,CAAM,CAAE/D,CAAM,CAAE8rB,CAAU,CAAE,CAC5C,IAAyBtzB,EAAQijB,AAAd,IAAI,CAAqBjjB,KAAK,CAAEuzB,EAAevzB,EAAME,QAAQ,CAACszB,UAAU,CACvFC,EAAqB,CAAA,CACrBxQ,CAFe,IAAI,CAERoC,qBAAqB,GAC5BpC,AAHW,IAAI,CAGJoC,qBAAqB,CAACtI,SAAS,GAAKxR,EAAOwR,SAAS,EAC/D0W,CAAAA,EAAqB,CAAA,CAAI,EAE7B3D,GANe,IAAI,CAMsB,iBAAkB,CAAEvkB,OAAQ0X,AANtD,IAAI,CAM6DoC,qBAAqB,AAAC,GAClGpC,AAPW,IAAI,CAOJyQ,SAAS,GAEhBzQ,AATO,IAAI,CASA0Q,kBAAkB,EAC7B1Q,AAAuC,gBAAvCA,AAVO,IAAI,CAUA0Q,kBAAkB,CAAC5vB,IAAI,EAClC/D,EAAMoF,gBAAgB,CAAC6d,AAXhB,IAAI,CAWuB0Q,kBAAkB,EAExD1Q,AAbW,IAAI,CAaJ2Q,cAAc,CAAG3Q,AAbjB,IAAI,CAawByQ,SAAS,CAAG,CAAA,IAGvDD,GACAxQ,AAjBe,IAAI,CAiBR4Q,cAAc,CAAGrsB,EAC5Byb,AAlBe,IAAI,CAkBRoC,qBAAqB,CAAG9Z,EACnCukB,GAnBe,IAAI,CAmBsB,eAAgB,CAAEvkB,OAAQA,CAAO,GAEtE/D,EAAO6J,IAAI,EACX7J,EAAO6J,IAAI,CAAC9S,IAAI,CAtBL,IAAI,CAsBcgN,EAAQ+nB,GAErC9rB,CAAAA,EAAOgnB,KAAK,EAAIhnB,EAAOqnB,KAAK,AAAD,GAC3B7uB,EAAME,QAAQ,CAACszB,UAAU,CAAC1hB,QAAQ,CAAC,0BAIvC9R,EAAM+nB,UAAU,EAAIxc,EAAOwR,SAAS,CAACC,MAAM,CAAC,qBAC5CuW,EAAaO,WAAW,CAAC,wBACzB7Q,AA/Be,IAAI,CA+BRyQ,SAAS,CAAG,CAAA,EACvBzQ,AAhCe,IAAI,CAgCR2Q,cAAc,CAAG,CAAA,EAC5B3Q,AAjCe,IAAI,CAiCR4Q,cAAc,CAAG,KAEpC,CAeAT,mBAAmBpzB,CAAK,CAAEszB,CAAU,CAAE,CAClCtzB,EAAQ,IAAI,CAACA,KAAK,CAClB,IAAyB+kB,EAAmB9B,AAAzB,IAAI,CAAgC8B,gBAAgB,CAAE8O,EAAiB5Q,AAAvE,IAAI,CAA8E4Q,cAAc,CAAEN,EAAevzB,EAAME,QAAQ,CAACszB,UAAU,CACzJzO,IAGI,AAACA,EAAiBjc,WAAW,EAC5BwqB,EAAWvO,gBAAgB,GAE5BuO,EAAWrqB,MAAM,CAAC4U,UAAU,EAE3BkW,AA1XjB,SAAyBC,CAAE,CAAEzwB,CAAC,EAC1B,IAAM0wB,EAAexE,GAAIyE,OAAO,CAAC71B,SAAS,CAAE81B,EAAiBF,EAAaG,OAAO,EAC7EH,EAAaI,iBAAiB,EAC9BJ,EAAaK,qBAAqB,CAClCC,EAAM,KACV,GAAIN,EAAaO,OAAO,CACpBD,EAAMN,EAAaO,OAAO,CAACj2B,IAAI,CAACy1B,EAAIzwB,QAGpC,EAAG,CACC,GAAI4wB,EAAe51B,IAAI,CAACy1B,EAAIzwB,GACxB,OAAOywB,EAEXA,EAAKA,EAAGS,aAAa,EAAIT,EAAGnW,UAAU,AAC1C,OAASmW,AAAO,OAAPA,GAAeA,AAAgB,IAAhBA,EAAGU,QAAQ,CAAQ,CAE/C,OAAOH,CACX,EAyWiCjB,EAAWrqB,MAAM,CAAE,qBAG/B8b,EAAiBjc,WAAW,EAEjCoc,WAAW,KACPH,EAAiBjc,WAAW,CAAG,CAAA,CACnC,EAAG,GANHgnB,GAVW,IAAI,CAU0B,eAS5C+D,GAAmBA,EAAerF,KAAK,GAGvCvL,AAtBc,IAAI,CAsBPyQ,SAAS,EAsBrBzQ,AA5Ce,IAAI,CA4CRyQ,SAAS,CAACJ,EAAYrQ,AA5ClB,IAAI,CA4CyB0Q,kBAAkB,EAC1D1Q,AA7CW,IAAI,CA6CJ4L,KAAK,GAChB5L,AA9CW,IAAI,CA8CJ0R,SAAS,GAChBd,EAAehF,KAAK,CAAC5L,AA/Cd,IAAI,CA+CqB0R,SAAS,CAAC,CAE1C1R,AAjDO,IAAI,CAiDA2Q,cAAc,CAAG3Q,AAjDrB,IAAI,CAiD4ByQ,SAAS,CAAGG,EAAehF,KAAK,CAAC5L,AAjDjE,IAAI,CAiDwE0R,SAAS,CAAC,EAG7F7E,GApDO,IAAI,CAoD8B,iBAAkB,CAAEvkB,OAAQ0X,AApD9D,IAAI,CAoDqEoC,qBAAqB,AAAC,GACtGkO,EAAaO,WAAW,CAAC,wBAErBD,EAAee,GAAG,EAClBf,EAAee,GAAG,CAACr2B,IAAI,CAxDpB,IAAI,CAwD6B+0B,EAAYrQ,AAxD7C,IAAI,CAwDoD0Q,kBAAkB,EAEjF1Q,AA1DO,IAAI,CA0DAyQ,SAAS,CAAG,CAAA,EACvBzQ,AA3DO,IAAI,CA2DA2Q,cAAc,CAAG,CAAA,EAC5B3Q,AA5DO,IAAI,CA4DA4Q,cAAc,CAAG,SApCpC5Q,AAxBe,IAAI,CAwBR0Q,kBAAkB,CAAGE,EAAerF,KAAK,CAACjwB,IAAI,CAxB1C,IAAI,CAwBmD+0B,GAElErQ,AA1BW,IAAI,CA0BJ0Q,kBAAkB,EAAIE,EAAehF,KAAK,EACrD5L,AA3BW,IAAI,CA2BJ0R,SAAS,CAAG,EACvB1R,AA5BW,IAAI,CA4BJ4L,KAAK,CAAG,CAAA,EACnB5L,AA7BW,IAAI,CA6BJ2Q,cAAc,CAAG3Q,AA7BjB,IAAI,CA6BwByQ,SAAS,CAC5CG,EAAehF,KAAK,CAAC5L,AA9Bd,IAAI,CA8BqB0R,SAAS,CAAC,GAG9C7E,GAjCW,IAAI,CAiC0B,iBAAkB,CAAEvkB,OAAQ0X,AAjC1D,IAAI,CAiCiEoC,qBAAqB,AAAC,GACtGkO,EAAaO,WAAW,CAAC,wBACzB7Q,AAnCW,IAAI,CAmCJ4L,KAAK,CAAG,CAAA,EACnB5L,AApCW,IAAI,CAoCJ4Q,cAAc,CAAG,KAExBA,EAAee,GAAG,EAClBf,EAAee,GAAG,CAACr2B,IAAI,CAvChB,IAAI,CAuCyB+0B,EAAYrQ,AAvCzC,IAAI,CAuCgD0Q,kBAAkB,IAyB7F,CAaAN,2BAA2BwB,CAAU,CAAEC,CAAS,CAAE,CAC1C,IAAI,CAAClB,cAAc,EACnB,IAAI,CAACA,cAAc,CAACkB,EAAW,IAAI,CAACnB,kBAAkB,CAE9D,CAiBAzB,gBAAgB1S,CAAM,CAAEsI,CAAM,CAAE,CAkC5B,OAjCAsI,GAA8B5Q,EAAQ,CAAC1L,EAAOihB,KAC1C,IAAMC,EAAcC,WAAWnhB,GAAQoB,EAAO6f,EAAMzS,KAAK,CAAC,KAAM4S,EAAahgB,EAAKtT,MAAM,CAAG,EAQ3F,GANIquB,CAAAA,GAA4B+E,IAC3BlhB,EAAMoI,KAAK,CAAC,WACZ6Y,EAAM7Y,KAAK,CAAC,YACbpI,CAAAA,EAAQkhB,CAAU,EAGlBlhB,AAAU,cAAVA,EAAuB,CACvB,IAAImB,EAAS6S,EACb5S,EAAKxU,OAAO,CAAC,CAACkf,EAAM/c,KAChB,GAAI+c,AAAS,cAATA,GAAwBA,AAAS,gBAATA,EAAwB,CAChD,IAAMuV,EAAW9E,GAAwBnb,CAAI,CAACrS,EAAQ,EAAE,CAAE,GACtDqyB,CAAAA,IAAeryB,EAEfoS,CAAM,CAAC2K,EAAK,CAAG9L,GAETmB,CAAM,CAAC2K,EAAK,EAElB3K,CAAAA,CAAM,CAAC2K,EAAK,CAAGuV,EAASjZ,KAAK,CAAC,OAC1B,EAAE,CACF,CAAC,CAAA,EAKLjH,EAASA,CAAM,CAAC2K,EAAK,CAE7B,CACJ,EACJ,CACJ,GACOkI,CACX,CAMA0I,oBAAqB,CACb,IAAI,CAACzL,gBAAgB,GACrB,IAAI,CAACA,gBAAgB,CAAChT,0BAA0B,CAAC,CAAA,GACjD,IAAI,CAACgT,gBAAgB,CAAG,CAAA,EAEhC,CAaAiN,mBAAmBzyB,CAAU,CAAE,CAC3B,IAAME,EAAUF,EAAWE,OAAO,CAAE21B,EAAYzE,GAAmB0E,mBAAmB,CAAEC,EAAkBF,EAAUG,aAAa,CAAEjwB,EAAO+qB,GAAwB5wB,EAAQ6F,IAAI,CAAE7F,EAAQ4L,MAAM,EAAI5L,EAAQ4L,MAAM,CAAC,EAAE,EAC/M5L,EAAQ4L,MAAM,CAAC,EAAE,CAAC/F,IAAI,CAAE7F,EAAQ0C,MAAM,EAAI1C,EAAQ0C,MAAM,CAAC,EAAE,EAC3D1C,EAAQ0C,MAAM,CAAC,EAAE,CAACmD,IAAI,CAAE,SAAUkwB,EAAe7E,GAAmB8E,sBAAsB,CAACh2B,EAAQinB,OAAO,CAAC,EAAI,EAAE,CAAEgP,EAAgB,CACnIhP,QAASjnB,EAAQinB,OAAO,CACxBphB,KAAMA,CACV,EAoBA,SAASqwB,EAASxX,CAAM,CAAEtgB,CAAG,CAAE+3B,CAAe,CAAE3gB,CAAM,CAAE4gB,CAAS,EAC7D,IAAIC,EACAF,GACA/F,GAA2B1R,IAC3BqX,AAA8B,KAA9BA,EAAarsB,OAAO,CAACtL,IACpB,CAAA,AAAC+3B,CAAAA,EAAgBzsB,OAAO,EACrBysB,EAAgBzsB,OAAO,CAACtL,EAAG,GAAM,GACjC+3B,CAAe,CAAC/3B,EAAI,EACpB+3B,AAAoB,CAAA,IAApBA,CAAuB,IAGvB7F,GAA2B5R,IAC3BlJ,CAAM,CAACpX,EAAI,CAAG,EAAE,CAChBsgB,EAAOzd,OAAO,CAAC,CAACq1B,EAAan1B,KACpBsvB,GAA4B6F,IAM7B9gB,CAAM,CAACpX,EAAI,CAAC+C,EAAE,CAAG,CAAC,EAClBwvB,GAA8B2F,EAAa,CAACC,EAAcC,KACtDN,EAASK,EAAcC,EAAWX,CAAe,CAACz3B,EAAI,CAAEoX,CAAM,CAACpX,EAAI,CAAC+C,EAAE,CAAE/C,EAC5E,IAPA83B,EAASI,EAAa,EAAGT,CAAe,CAACz3B,EAAI,CAAEoX,CAAM,CAACpX,EAAI,CAAEA,EASpE,IAEKqyB,GAA4B/R,IACjC2X,EAAa,CAAC,EACV/F,GAA2B9a,IAC3BA,EAAOtV,IAAI,CAACm2B,GACZA,CAAU,CAACj4B,EAAI,CAAG,CAAC,EACnBi4B,EAAaA,CAAU,CAACj4B,EAAI,EAG5BoX,CAAM,CAACpX,EAAI,CAAGi4B,EAElB1F,GAA8BjS,EAAQ,CAAC6X,EAAcC,KACjDN,EAASK,EAAcC,EAAWp4B,AAAQ,IAARA,EAC9B+3B,EACAN,CAAe,CAACz3B,EAAI,CAAEi4B,EAAYj4B,EAC1C,IAIIA,AAAQ,WAARA,EACAoX,CAAM,CAACpX,EAAI,CAAG,CACVyxB,GAA0BnR,EAAQ5e,EAAW4C,MAAM,CAAC,EAAE,CAACI,MAAM,CAAC,EAAE,EAAE2zB,QAAQ,GAC1E,OACH,CAEInG,GAA2B9a,GAChCA,EAAOtV,IAAI,CAAC,CAACwe,EAAQwR,GAAgCkG,EAAW1X,GAAQ,EAGxElJ,CAAM,CAACpX,EAAI,CAAG,CAACsgB,EAAQwR,GAAgC9xB,EAAKsgB,GAAQ,CAIpF,CAYA,OAXAiS,GAA8B3wB,EAAS,CAAC0e,EAAQtgB,KACxCA,AAAQ,gBAARA,GACA63B,CAAa,CAAC73B,EAAI,CAAG,CAAC,EACtBuyB,GAA8B3wB,CAAO,CAAC5B,EAAI,CAAE,CAACs4B,EAAYC,KACrDT,EAASQ,EAAYC,EAASd,EAAiBI,CAAa,CAAC73B,EAAI,CAAEu4B,EACvE,IAGAT,EAASxX,EAAQtgB,EAAKu3B,CAAS,CAAC9vB,EAAK,CAAEowB,EAAe73B,EAE9D,GACO63B,CACX,CAiBAW,qBAAqBza,CAAS,CAAE5a,CAAK,CAAE,CACnC,IAAIwH,EAAUxH,EAAMiI,MAAM,CAAEqtB,EAAa,EAAE,CAAEC,EAC7C,KAAO/tB,GAAWA,EAAQoK,OAAO,GAEzB2jB,AADJA,CAAAA,EAAgB12B,GAAK2I,EAAS,QAAO,GAEjC8tB,CAAAA,EAAaA,EAAW3K,MAAM,CAAC4K,EAC1BjU,KAAK,CAAC,KAEN7Q,GAAG,CAAC,AAACmO,GAAU,CAACA,EAAMpX,EAAQ,EAAE,EAGrCA,AADJA,CAAAA,EAAUA,EAAQqV,UAAU,AAAD,IACXjC,KAIpB,OAAO0a,CACX,CAiBArD,gBAAgBrX,CAAS,CAAE5a,CAAK,CAAE,CAC9B,IACIstB,EADErL,EAAa,IAAI,CAUvB,OARAqT,AAFsC,IAAI,CAACD,oBAAoB,CAACza,EAAW5a,GAEhEN,OAAO,CAAC,AAAC0F,IACZ6c,EAAWgO,eAAe,CAAC7qB,CAAS,CAAC,EAAE,CAAC,EAAI,CAACkoB,GAC7CA,CAAAA,EAAW,CACP9mB,OAAQyb,EAAWgO,eAAe,CAAC7qB,CAAS,CAAC,EAAE,CAAC,CAChDmF,OAAQnF,CAAS,CAAC,EAAE,AACxB,CAAA,CAER,GACOkoB,CACX,CAQAjiB,OAAO5M,CAAO,CAAE,CACZ,IAAI,CAACA,OAAO,CAAG0wB,GAAyB,CAAA,EAAM,IAAI,CAAC1wB,OAAO,CAAEA,GAC5D,IAAI,CAAC+2B,YAAY,GACjB,IAAI,CAAC5F,UAAU,EACnB,CAOA4F,cAAe,CACX,IAAI,CAAC5D,cAAc,CAAClyB,OAAO,CAAC,AAAC+1B,GAAaA,IAC9C,CAKA11B,SAAU,CACN,IAAI,CAACy1B,YAAY,EACrB,CACJ,CAOA7F,GAAmB0E,mBAAmB,CAAG,CAGrCE,cAAe,CACXtzB,aAAc,CAAC,QAAS,SAAU,kBAAkB,CACpDE,OAAQ,CAAC,QAAQ,CACjBC,MAAO,CAAC,QAAQ,CAChBuE,MAAO,CAAC,WAAY,QAAQ,CAC5BsT,WAAY,CAAC,OAAQ,cAAe,SAAS,CAC7Cyc,gBAAiB,CAAC,OAAQ,cAAe,SAAS,CAClDC,gBAAiB,CAAC,OAAQ,cAAe,SAAS,CAClD1vB,aAAc,CAAC,OAAQ,cAAe,SAAS,CAC/CoE,OAAQ,CAAC,OAAQ,cAAe,SAAS,CACzCurB,KAAM,CAAC,cAAe,SAAS,CAC/BxI,iBAAkB,CAAC,CAAA,EAAK,CACxB7U,UAAW,CAAC,OAAQ,cAAe,SAAS,CAC5C8Y,WAAY,CAAC,cAAe,SAAS,CACrCF,WAAY,CAAC,cAAe,SAAS,AACzC,EAEA/b,OAAQ,CAAC,SAAS,CAClB6X,QAAS,CAAC,SAAS,CACnB4I,aAAc,EAAE,CAChBz0B,MAAO,CAAC,eAAe,CAEvB00B,QAAS,CAAC,aAAc,aAAc,aAAa,CAEnDC,UAAW,EAAE,CACbC,OAAQ,CAAC,aAAc,OAAQ,SAAS,CACxCC,UAAW,CAAC,kBAAmB,kBAAkB,CACjDlhB,KAAM,CAAC,SAAS,CAEhBmhB,YAAa,EAAE,CACfC,gBAAiB,CAAC,SAAU,eAAe,AAC/C,EAGAxG,GAAmB8E,sBAAsB,CAAG,CACxCvH,UAAW,CAAC,aAAc,aAAc,eAAe,CACvDD,QAAS,CAAC,eAAe,CACzB7X,OAAQ,CAAC,eAAe,AAC5B,EA8CA,IAAMghB,GAAKz4B,GACXy4B,CAAAA,GAAEhO,UAAU,CAAGgO,GAAEhO,UAAU,EA18CkCA,GA28C7DgO,GAAEzG,kBAAkB,CAAGyG,GAAEzG,kBAAkB,EA1C0BA,GA2CrEyG,GAAEhO,UAAU,CAAC1kB,OAAO,CAAC0yB,GAAEC,KAAK,CAAED,GAAEzG,kBAAkB,CAAEyG,GAAEE,OAAO,CAAEF,GAAEG,WAAW,EAC/C,IAAM94B,GAAoBE,IAG7C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}