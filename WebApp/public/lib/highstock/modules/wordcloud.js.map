{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/wordcloud\n * @requires highcharts\n *\n * (c) 2016-2025 Highsoft AS\n * Authors: <AUTHORS>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/wordcloud\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/wordcloud\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ wordcloud_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Series/DrawPointUtilities.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Handles the drawing of a component.\n * Can be used for any type of component that reserves the graphic property,\n * and provides a shouldDraw on its context.\n *\n * @private\n *\n * @todo add type checking.\n * @todo export this function to enable usage\n */\nfunction draw(point, params) {\n    const { animatableAttribs, onComplete, css, renderer } = params;\n    const animation = (point.series && point.series.chart.hasRendered) ?\n        // Chart-level animation on updates\n        void 0 :\n        // Series-level animation on new points\n        (point.series &&\n            point.series.options.animation);\n    let graphic = point.graphic;\n    params.attribs = {\n        ...params.attribs,\n        'class': point.getClassName()\n    } || {};\n    if ((point.shouldDraw())) {\n        if (!graphic) {\n            if (params.shapeType === 'text') {\n                graphic = renderer.text();\n            }\n            else if (params.shapeType === 'image') {\n                graphic = renderer.image(params.imageUrl || '')\n                    .attr(params.shapeArgs || {});\n            }\n            else {\n                graphic = renderer[params.shapeType](params.shapeArgs || {});\n            }\n            point.graphic = graphic;\n            graphic.add(params.group);\n        }\n        if (css) {\n            graphic.css(css);\n        }\n        graphic\n            .attr(params.attribs)\n            .animate(animatableAttribs, params.isNew ? false : animation, onComplete);\n    }\n    else if (graphic) {\n        const destroy = () => {\n            point.graphic = graphic = (graphic && graphic.destroy());\n            if (typeof onComplete === 'function') {\n                onComplete();\n            }\n        };\n        // Animate only runs complete callback if something was animated.\n        if (Object.keys(animatableAttribs).length) {\n            graphic.animate(animatableAttribs, void 0, () => destroy());\n        }\n        else {\n            destroy();\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst DrawPointUtilities = {\n    draw\n};\n/* harmony default export */ const Series_DrawPointUtilities = (DrawPointUtilities);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/Wordcloud/WordcloudPoint.js\n/* *\n *\n *  Experimental Highcharts module which enables visualization of a word cloud.\n *\n *  (c) 2016-2025 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n * */\n\n\nconst { column: { prototype: { pointClass: ColumnPoint } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { extend } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass WordcloudPoint extends ColumnPoint {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    isValid() {\n        return true;\n    }\n}\nextend(WordcloudPoint.prototype, {\n    weight: 1\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Wordcloud_WordcloudPoint = (WordcloudPoint);\n\n;// ./code/es-modules/Series/Wordcloud/WordcloudSeriesDefaults.js\n/* *\n *\n *  Experimental Highcharts module which enables visualization of a word cloud.\n *\n *  (c) 2016-2025 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A word cloud is a visualization of a set of words, where the size and\n * placement of a word is determined by how it is weighted.\n *\n * @sample highcharts/demo/wordcloud Word Cloud chart\n *\n * @extends      plotOptions.column\n * @excluding    allAreas, boostThreshold, clip, colorAxis, compare,\n *               compareBase, crisp, cropThreshold, dataGrouping,\n *               dataLabels, depth, dragDrop, edgeColor, findNearestPointBy,\n *               getExtremesFromAll, grouping, groupPadding, groupZPadding,\n *               joinBy, maxPointWidth, minPointLength, navigatorOptions,\n *               negativeColor, pointInterval, pointIntervalUnit,\n *               pointPadding, pointPlacement, pointRange, pointStart,\n *               pointWidth, pointStart, pointWidth, shadow, showCheckbox,\n *               showInNavigator, softThreshold, stacking, threshold,\n *               zoneAxis, zones, dataSorting, boostBlending\n * @product      highcharts\n * @since        6.0.0\n * @requires     modules/wordcloud\n * @optionparent plotOptions.wordcloud\n */\nconst WordcloudSeriesDefaults = {\n    /**\n     * If there is no space for a word on the playing field, then this\n     * option will allow the playing field to be extended to fit the word.\n     * If false then the word will be dropped from the visualization.\n     *\n     * NB! This option is currently not decided to be published in the API,\n     * and is therefore marked as private.\n     *\n     * @ignore-option\n     */\n    allowExtendPlayingField: true,\n    animation: {\n        /** @internal */\n        duration: 500\n    },\n    borderWidth: 0,\n    /**\n     * @ignore-option\n     */\n    clip: false, // Something goes wrong with clip. // @todo fix this\n    colorByPoint: true,\n    cropThreshold: Infinity,\n    /**\n     * A threshold determining the minimum font size that can be applied to\n     * a word.\n     */\n    minFontSize: 1,\n    /**\n     * The word with the largest weight will have a font size equal to this\n     * value. The font size of a word is the ratio between its weight and\n     * the largest occuring weight, multiplied with the value of\n     * maxFontSize.\n     */\n    maxFontSize: 25,\n    /**\n     * This option decides which algorithm is used for placement, and\n     * rotation of a word. The choice of algorith is therefore a crucial\n     * part of the resulting layout of the wordcloud. It is possible for\n     * users to add their own custom placement strategies for use in word\n     * cloud. Read more about it in our\n     * [documentation](https://www.highcharts.com/docs/chart-and-series-types/word-cloud-series#custom-placement-strategies)\n     *\n     * @validvalue [\"center\", \"random\"]\n     */\n    placementStrategy: 'center',\n    /**\n     * Rotation options for the words in the wordcloud.\n     *\n     * @sample highcharts/plotoptions/wordcloud-rotation\n     *         Word cloud with rotation\n     */\n    rotation: {\n        /**\n         * The smallest degree of rotation for a word.\n         */\n        from: 0,\n        /**\n         * The number of possible orientations for a word, within the range\n         * of `rotation.from` and `rotation.to`. Must be a number larger\n         * than 0.\n         */\n        orientations: 2,\n        /**\n         * The largest degree of rotation for a word.\n         */\n        to: 90\n    },\n    showInLegend: false,\n    /**\n     * Spiral used for placing a word after the initial position\n     * experienced a collision with either another word or the borders.\n     * It is possible for users to add their own custom spiralling\n     * algorithms for use in word cloud. Read more about it in our\n     * [documentation](https://www.highcharts.com/docs/chart-and-series-types/word-cloud-series#custom-spiralling-algorithm)\n     *\n     * @validvalue [\"archimedean\", \"rectangular\", \"square\"]\n     */\n    spiral: 'rectangular',\n    /**\n     * CSS styles for the words.\n     *\n     * @type    {Highcharts.CSSObject}\n     * @default {\"fontFamily\":\"sans-serif\", \"fontWeight\": \"900\"}\n     */\n    style: {\n        /** @ignore-option */\n        fontFamily: 'sans-serif',\n        /** @ignore-option */\n        fontWeight: '900',\n        /** @ignore-option */\n        whiteSpace: 'nowrap'\n    },\n    tooltip: {\n        followPointer: true,\n        pointFormat: '<span style=\"color:{point.color}\">\\u25CF</span> {series.name}: <b>{point.weight}</b><br/>'\n    }\n};\n/**\n * A `wordcloud` series. If the [type](#series.wordcloud.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.wordcloud\n * @exclude   dataSorting, boostThreshold, boostBlending\n * @product   highcharts\n * @requires  modules/wordcloud\n * @apioption series.wordcloud\n */\n/**\n * An array of data points for the series. For the `wordcloud` series type,\n * points can be given in the following ways:\n *\n * 1. An array of arrays with 2 values. In this case, the values correspond to\n *    `name,weight`.\n *    ```js\n *    data: [\n *        ['Lorem', 4],\n *        ['Ipsum', 1]\n *    ]\n *    ```\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.arearange.turboThreshold), this option is not\n *    available.\n *    ```js\n *    data: [{\n *        name: \"Lorem\",\n *        weight: 4\n *    }, {\n *        name: \"Ipsum\",\n *        weight: 1\n *    }]\n *    ```\n *\n * @type      {Array<Array<string,number>|*>}\n * @extends   series.line.data\n * @excluding drilldown, marker, x, y\n * @product   highcharts\n * @apioption series.wordcloud.data\n */\n/**\n * The name decides the text for a word.\n *\n * @type      {string}\n * @since     6.0.0\n * @product   highcharts\n * @apioption series.wordcloud.data.name\n */\n/**\n * The weighting of a word. The weight decides the relative size of a word\n * compared to the rest of the collection.\n *\n * @type      {number}\n * @since     6.0.0\n * @product   highcharts\n * @apioption series.wordcloud.data.weight\n */\n''; // Detach doclets above\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Wordcloud_WordcloudSeriesDefaults = (WordcloudSeriesDefaults);\n\n;// ./code/es-modules/Series/Wordcloud/WordcloudUtils.js\n/* *\n *\n *  Experimental Highcharts module which enables visualization of a word cloud.\n *\n *  (c) 2016-2025 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n * */\n\n\nconst { deg2rad } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { extend: WordcloudUtils_extend, find, isNumber, isObject, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n * Functions\n *\n * */\n/**\n * Detects if there is a collision between two rectangles.\n *\n * @private\n * @function isRectanglesIntersecting\n *\n * @param {Highcharts.PolygonBoxObject} r1\n * First rectangle.\n *\n * @param {Highcharts.PolygonBoxObject} r2\n * Second rectangle.\n *\n * @return {boolean}\n * Returns true if the rectangles overlap.\n */\nfunction isRectanglesIntersecting(r1, r2) {\n    return !(r2.left > r1.right ||\n        r2.right < r1.left ||\n        r2.top > r1.bottom ||\n        r2.bottom < r1.top);\n}\n/**\n * Calculates the normals to a line between two points.\n *\n * @private\n * @function getNormals\n * @param {Highcharts.PolygonPointObject} p1\n *        Start point for the line. Array of x and y value.\n * @param {Highcharts.PolygonPointObject} p2\n *        End point for the line. Array of x and y value.\n * @return {Highcharts.PolygonObject}\n *         Returns the two normals in an array.\n */\nfunction getNormals(p1, p2) {\n    const dx = p2[0] - p1[0], // X2 - x1\n    dy = p2[1] - p1[1]; // Y2 - y1\n    return [\n        [-dy, dx],\n        [dy, -dx]\n    ];\n}\n/**\n * @private\n */\nfunction getAxesFromPolygon(polygon) {\n    let points, axes = polygon.axes || [];\n    if (!axes.length) {\n        axes = [];\n        points = points = polygon.concat([polygon[0]]);\n        points.reduce((p1, p2) => {\n            const normals = getNormals(p1, p2), axis = normals[0]; // Use the left normal as axis.\n            // Check that the axis is unique.\n            if (!find(axes, (existing) => existing[0] === axis[0] &&\n                existing[1] === axis[1])) {\n                axes.push(axis);\n            }\n            // Return p2 to be used as p1 in next iteration.\n            return p2;\n        });\n        polygon.axes = axes;\n    }\n    return axes;\n}\n/**\n * Projects a polygon onto a coordinate.\n *\n * @private\n * @function project\n * @param {Highcharts.PolygonObject} polygon\n * Array of points in a polygon.\n * @param {Highcharts.PolygonPointObject} target\n * The coordinate of pr\n */\nfunction project(polygon, target) {\n    const products = polygon.map((point) => {\n        const ax = point[0], ay = point[1], bx = target[0], by = target[1];\n        return ax * bx + ay * by;\n    });\n    return {\n        min: Math.min.apply(this, products),\n        max: Math.max.apply(this, products)\n    };\n}\n/**\n * @private\n */\nfunction isPolygonsOverlappingOnAxis(axis, polygon1, polygon2) {\n    const projection1 = project(polygon1, axis), projection2 = project(polygon2, axis), isOverlapping = !(projection2.min > projection1.max ||\n        projection2.max < projection1.min);\n    return !isOverlapping;\n}\n/**\n * Checks whether two convex polygons are colliding by using the Separating\n * Axis Theorem.\n *\n * @private\n * @function isPolygonsColliding\n * @param {Highcharts.PolygonObject} polygon1\n *        First polygon.\n *\n * @param {Highcharts.PolygonObject} polygon2\n *        Second polygon.\n *\n * @return {boolean}\n *         Returns true if they are colliding, otherwise false.\n */\nfunction isPolygonsColliding(polygon1, polygon2) {\n    // Get the axis from both polygons.\n    const axes1 = getAxesFromPolygon(polygon1), axes2 = getAxesFromPolygon(polygon2), axes = axes1.concat(axes2), overlappingOnAllAxes = !find(axes, (axis) => isPolygonsOverlappingOnAxis(axis, polygon1, polygon2));\n    return overlappingOnAllAxes;\n}\n/**\n * Detects if a word collides with any previously placed words.\n *\n * @private\n * @function intersectsAnyWord\n *\n * @param {Highcharts.Point} point\n * Point which the word is connected to.\n *\n * @param {Array<Highcharts.Point>} points\n * Previously placed points to check against.\n *\n * @return {boolean}\n * Returns true if there is collision.\n */\nfunction intersectsAnyWord(point, points) {\n    const rect = point.rect, polygon = point.polygon, lastCollidedWith = point.lastCollidedWith, isIntersecting = function (p) {\n        let result = isRectanglesIntersecting(rect, p.rect);\n        if (result &&\n            (point.rotation % 90 || p.rotation % 90)) {\n            result = isPolygonsColliding(polygon, p.polygon);\n        }\n        return result;\n    };\n    let intersects = false;\n    // If the point has already intersected a different point, chances are\n    // they are still intersecting. So as an enhancement we check this\n    // first.\n    if (lastCollidedWith) {\n        intersects = isIntersecting(lastCollidedWith);\n        // If they no longer intersects, remove the cache from the point.\n        if (!intersects) {\n            delete point.lastCollidedWith;\n        }\n    }\n    // If not already found, then check if we can find a point that is\n    // intersecting.\n    if (!intersects) {\n        intersects = !!find(points, function (p) {\n            const result = isIntersecting(p);\n            if (result) {\n                point.lastCollidedWith = p;\n            }\n            return result;\n        });\n    }\n    return intersects;\n}\n/**\n * Gives a set of cordinates for an Archimedian Spiral.\n *\n * @private\n * @function archimedeanSpiral\n *\n * @param {number} attempt\n * How far along the spiral we have traversed.\n *\n * @param {Highcharts.WordcloudSpiralParamsObject} [params]\n * Additional parameters.\n *\n * @return {boolean|Highcharts.PositionObject}\n * Resulting coordinates, x and y. False if the word should be dropped from\n * the visualization.\n */\nfunction archimedeanSpiral(attempt, params) {\n    const field = params.field, maxDelta = (field.width * field.width) + (field.height * field.height), t = attempt * 0.8; // 0.2 * 4 = 0.8. Enlarging the spiral.\n    let result = false;\n    // Emergency brake. TODO make spiralling logic more foolproof.\n    if (attempt <= 10000) {\n        result = {\n            x: t * Math.cos(t),\n            y: t * Math.sin(t)\n        };\n        if (!(Math.min(Math.abs(result.x), Math.abs(result.y)) < maxDelta)) {\n            result = false;\n        }\n    }\n    return result;\n}\n/**\n * Gives a set of coordinates for an rectangular spiral.\n *\n * @private\n * @function squareSpiral\n *\n * @param {number} attempt\n * How far along the spiral we have traversed.\n *\n * @param {Highcharts.WordcloudSpiralParamsObject} [params]\n * Additional parameters.\n *\n * @return {boolean|Highcharts.PositionObject}\n * Resulting coordinates, x and y. False if the word should be dropped from\n * the visualization.\n */\nfunction squareSpiral(attempt, \n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nparams) {\n    const a = attempt * 4, k = Math.ceil((Math.sqrt(a) - 1) / 2), isBoolean = (x) => (typeof x === 'boolean');\n    let t = 2 * k + 1, m = Math.pow(t, 2), result = false;\n    t -= 1;\n    if (attempt <= 10000) {\n        if (isBoolean(result) && a >= m - t) {\n            result = {\n                x: k - (m - a),\n                y: -k\n            };\n        }\n        m -= t;\n        if (isBoolean(result) && a >= m - t) {\n            result = {\n                x: -k,\n                y: -k + (m - a)\n            };\n        }\n        m -= t;\n        if (isBoolean(result)) {\n            if (a >= m - t) {\n                result = {\n                    x: -k + (m - a),\n                    y: k\n                };\n            }\n            else {\n                result = {\n                    x: k,\n                    y: k - (m - a - t)\n                };\n            }\n        }\n        result.x *= 5;\n        result.y *= 5;\n    }\n    return result;\n}\n/**\n * Gives a set of coordinates for an rectangular spiral.\n *\n * @private\n * @function rectangularSpiral\n *\n * @param {number} attempt\n * How far along the spiral we have traversed.\n *\n * @param {Highcharts.WordcloudSpiralParamsObject} [params]\n * Additional parameters.\n *\n * @return {boolean|Higcharts.PositionObject}\n * Resulting coordinates, x and y. False if the word should be dropped from\n * the visualization.\n */\nfunction rectangularSpiral(attempt, params) {\n    const result = squareSpiral(attempt, params), field = params.field;\n    if (result) {\n        result.x *= field.ratioX;\n        result.y *= field.ratioY;\n    }\n    return result;\n}\n/**\n * @private\n * @function getRandomPosition\n *\n * @param {number} size\n * Random factor.\n *\n * @return {number}\n * Random position.\n */\nfunction getRandomPosition(size) {\n    return Math.round((size * (Math.random() + 0.5)) / 2);\n}\n/**\n * Calculates the proper scale to fit the cloud inside the plotting area.\n *\n * @private\n * @function getScale\n *\n * @param {number} targetWidth\n * Width of target area.\n *\n * @param {number} targetHeight\n * Height of target area.\n *\n * @param {Object} field\n * The playing field.\n *\n * @return {number}\n * Returns the value to scale the playing field up to the size of the target\n * area.\n */\nfunction getScale(targetWidth, targetHeight, field) {\n    const height = Math.max(Math.abs(field.top), Math.abs(field.bottom)) * 2, width = Math.max(Math.abs(field.left), Math.abs(field.right)) * 2, scaleX = width > 0 ? 1 / width * targetWidth : 1, scaleY = height > 0 ? 1 / height * targetHeight : 1;\n    return Math.min(scaleX, scaleY);\n}\n/**\n * Calculates what is called the playing field. The field is the area which\n * all the words are allowed to be positioned within. The area is\n * proportioned to match the target aspect ratio.\n *\n * @private\n * @function getPlayingField\n *\n * @param {number} targetWidth\n * Width of the target area.\n *\n * @param {number} targetHeight\n * Height of the target area.\n *\n * @param {Array<Highcharts.Point>} data\n * Array of points.\n *\n * @param {Object} data.dimensions\n * The height and width of the word.\n *\n * @return {Object}\n * The width and height of the playing field.\n */\nfunction getPlayingField(targetWidth, targetHeight, data) {\n    const info = data.reduce(function (obj, point) {\n        const dimensions = point.dimensions, x = Math.max(dimensions.width, dimensions.height);\n        // Find largest height.\n        obj.maxHeight = Math.max(obj.maxHeight, dimensions.height);\n        // Find largest width.\n        obj.maxWidth = Math.max(obj.maxWidth, dimensions.width);\n        // Sum up the total maximum area of all the words.\n        obj.area += x * x;\n        return obj;\n    }, {\n        maxHeight: 0,\n        maxWidth: 0,\n        area: 0\n    }), \n    /**\n     * Use largest width, largest height, or root of total area to give\n     * size to the playing field.\n     */\n    x = Math.max(info.maxHeight, // Have enough space for the tallest word\n    info.maxWidth, // Have enough space for the broadest word\n    // Adjust 15% to account for close packing of words\n    Math.sqrt(info.area) * 0.85), ratioX = targetWidth > targetHeight ? targetWidth / targetHeight : 1, ratioY = targetHeight > targetWidth ? targetHeight / targetWidth : 1;\n    return {\n        width: x * ratioX,\n        height: x * ratioY,\n        ratioX: ratioX,\n        ratioY: ratioY\n    };\n}\n/**\n * Calculates a number of degrees to rotate, based upon a number of\n * orientations within a range from-to.\n *\n * @private\n * @function getRotation\n *\n * @param {number} [orientations]\n * Number of orientations.\n *\n * @param {number} [index]\n * Index of point, used to decide orientation.\n *\n * @param {number} [from]\n * The smallest degree of rotation.\n *\n * @param {number} [to]\n * The largest degree of rotation.\n *\n * @return {boolean|number}\n * Returns the resulting rotation for the word. Returns false if invalid\n * input parameters.\n */\nfunction getRotation(orientations, index, from, to) {\n    let result = false, // Default to false\n    range, intervals, orientation;\n    // Check if we have valid input parameters.\n    if (isNumber(orientations) &&\n        isNumber(index) &&\n        isNumber(from) &&\n        isNumber(to) &&\n        orientations > 0 &&\n        index > -1 &&\n        to > from) {\n        range = to - from;\n        intervals = range / (orientations - 1 || 1);\n        orientation = index % orientations;\n        result = from + (orientation * intervals);\n    }\n    return result;\n}\n/**\n * Calculates the spiral positions and store them in scope for quick access.\n *\n * @private\n * @function getSpiral\n *\n * @param {Function} fn\n * The spiral function.\n *\n * @param {Object} params\n * Additional parameters for the spiral.\n *\n * @return {Function}\n * Function with access to spiral positions.\n */\nfunction getSpiral(fn, params) {\n    const length = 10000, arr = [];\n    for (let i = 1; i < length; i++) {\n        // @todo unnecessary amount of precalculation\n        arr.push(fn(i, params));\n    }\n    return (attempt) => (attempt <= length ? arr[attempt - 1] : false);\n}\n/**\n * Detects if a word is placed outside the playing field.\n *\n * @private\n * @function outsidePlayingField\n *\n * @param {Highcharts.PolygonBoxObject} rect\n * The word box.\n *\n * @param {Highcharts.WordcloudFieldObject} field\n * The width and height of the playing field.\n *\n * @return {boolean}\n * Returns true if the word is placed outside the field.\n */\nfunction outsidePlayingField(rect, field) {\n    const playingField = {\n        left: -(field.width / 2),\n        right: field.width / 2,\n        top: -(field.height / 2),\n        bottom: field.height / 2\n    };\n    return !(playingField.left < rect.left &&\n        playingField.right > rect.right &&\n        playingField.top < rect.top &&\n        playingField.bottom > rect.bottom);\n}\n/**\n * @private\n */\nfunction movePolygon(deltaX, deltaY, polygon) {\n    return polygon.map(function (point) {\n        return [\n            point[0] + deltaX,\n            point[1] + deltaY\n        ];\n    });\n}\n/**\n * Check if a point intersects with previously placed words, or if it goes\n * outside the field boundaries. If a collision, then try to adjusts the\n * position.\n *\n * @private\n * @function intersectionTesting\n *\n * @param {Highcharts.Point} point\n * Point to test for intersections.\n *\n * @param {Highcharts.WordcloudTestOptionsObject} options\n * Options object.\n *\n * @return {boolean|Highcharts.PositionObject}\n * Returns an object with how much to correct the positions. Returns false\n * if the word should not be placed at all.\n */\nfunction intersectionTesting(point, options) {\n    const placed = options.placed, field = options.field, rectangle = options.rectangle, polygon = options.polygon, spiral = options.spiral, \n    // Make a copy to update values during intersection testing.\n    rect = point.rect = WordcloudUtils_extend({}, rectangle);\n    let attempt = 1, delta = {\n        x: 0,\n        y: 0\n    };\n    point.polygon = polygon;\n    point.rotation = options.rotation;\n    /* While w intersects any previously placed words:\n        do {\n        move w a little bit along a spiral path\n        } while any part of w is outside the playing field and\n                the spiral radius is still smallish */\n    while (delta !== false &&\n        (intersectsAnyWord(point, placed) ||\n            outsidePlayingField(rect, field))) {\n        delta = spiral(attempt);\n        if (isObject(delta)) {\n            // Update the DOMRect with new positions.\n            rect.left = rectangle.left + delta.x;\n            rect.right = rectangle.right + delta.x;\n            rect.top = rectangle.top + delta.y;\n            rect.bottom = rectangle.bottom + delta.y;\n            point.polygon = movePolygon(delta.x, delta.y, polygon);\n        }\n        attempt++;\n    }\n    return delta;\n}\n/**\n * Extends the playing field to have enough space to fit a given word.\n *\n * @private\n * @function extendPlayingField\n *\n * @param {Highcharts.WordcloudFieldObject} field\n * The width, height and ratios of a playing field.\n *\n * @param {Highcharts.PolygonBoxObject} rectangle\n * The bounding box of the word to add space for.\n *\n * @return {Highcharts.WordcloudFieldObject}\n * Returns the extended playing field with updated height and width.\n */\nfunction extendPlayingField(field, rectangle) {\n    let height, width, ratioX, ratioY, x, extendWidth, extendHeight, result;\n    if (isObject(field) && isObject(rectangle)) {\n        height = (rectangle.bottom - rectangle.top);\n        width = (rectangle.right - rectangle.left);\n        ratioX = field.ratioX;\n        ratioY = field.ratioY;\n        // Use the same variable to extend both the height and width.\n        x = ((width * ratioX) > (height * ratioY)) ? width : height;\n        // Multiply variable with ratios to preserve aspect ratio.\n        extendWidth = x * ratioX;\n        extendHeight = x * ratioY;\n        // Calculate the size of the new field after adding\n        // space for the word.\n        result = merge(field, {\n            // Add space on the left and right.\n            width: field.width + (extendWidth * 2),\n            // Add space on the top and bottom.\n            height: field.height + (extendHeight * 2)\n        });\n    }\n    else {\n        result = field;\n    }\n    // Return the new extended field.\n    return result;\n}\n/**\n * If a rectangle is outside a give field, then the boundaries of the field\n * is adjusted accordingly. Modifies the field object which is passed as the\n * first parameter.\n *\n * @private\n * @function updateFieldBoundaries\n *\n * @param {Highcharts.WordcloudFieldObject} field\n * The bounding box of a playing field.\n *\n * @param {Highcharts.PolygonBoxObject} rectangle\n * The bounding box for a placed point.\n *\n * @return {Highcharts.WordcloudFieldObject}\n * Returns a modified field object.\n */\nfunction updateFieldBoundaries(field, rectangle) {\n    // @todo improve type checking.\n    if (!isNumber(field.left) || field.left > rectangle.left) {\n        field.left = rectangle.left;\n    }\n    if (!isNumber(field.right) || field.right < rectangle.right) {\n        field.right = rectangle.right;\n    }\n    if (!isNumber(field.top) || field.top > rectangle.top) {\n        field.top = rectangle.top;\n    }\n    if (!isNumber(field.bottom) || field.bottom < rectangle.bottom) {\n        field.bottom = rectangle.bottom;\n    }\n    return field;\n}\n/**\n * Alternative solution to correctFloat.\n * E.g Highcharts.correctFloat(123, 2) returns 120, when it should be 123.\n *\n * @private\n * @function correctFloat\n */\nfunction correctFloat(number, precision) {\n    const p = isNumber(precision) ? precision : 14, magnitude = Math.pow(10, p);\n    return Math.round(number * magnitude) / magnitude;\n}\n/**\n * @private\n */\nfunction getBoundingBoxFromPolygon(points) {\n    return points.reduce(function (obj, point) {\n        const x = point[0], y = point[1];\n        obj.left = Math.min(x, obj.left);\n        obj.right = Math.max(x, obj.right);\n        obj.bottom = Math.max(y, obj.bottom);\n        obj.top = Math.min(y, obj.top);\n        return obj;\n    }, {\n        left: Number.MAX_VALUE,\n        right: -Number.MAX_VALUE,\n        bottom: -Number.MAX_VALUE,\n        top: Number.MAX_VALUE\n    });\n}\n/**\n * @private\n */\nfunction getPolygon(x, y, width, height, rotation) {\n    const origin = [x, y], left = x - (width / 2), right = x + (width / 2), top = y - (height / 2), bottom = y + (height / 2), polygon = [\n        [left, top],\n        [right, top],\n        [right, bottom],\n        [left, bottom]\n    ];\n    return polygon.map(function (point) {\n        return rotate2DToPoint(point, origin, -rotation);\n    });\n}\n/**\n * Rotates a point clockwise around the origin.\n *\n * @private\n * @function rotate2DToOrigin\n * @param {Highcharts.PolygonPointObject} point\n *        The x and y coordinates for the point.\n * @param {number} angle\n *        The angle of rotation.\n * @return {Highcharts.PolygonPointObject}\n *         The x and y coordinate for the rotated point.\n */\nfunction rotate2DToOrigin(point, angle) {\n    const x = point[0], y = point[1], rad = deg2rad * -angle, cosAngle = Math.cos(rad), sinAngle = Math.sin(rad);\n    return [\n        correctFloat(x * cosAngle - y * sinAngle),\n        correctFloat(x * sinAngle + y * cosAngle)\n    ];\n}\n/**\n * Rotate a point clockwise around another point.\n *\n * @private\n * @function rotate2DToPoint\n * @param {Highcharts.PolygonPointObject} point\n *        The x and y coordinates for the point.\n * @param {Highcharts.PolygonPointObject} origin\n *        The point to rotate around.\n * @param {number} angle\n *        The angle of rotation.\n * @return {Highcharts.PolygonPointObject}\n *         The x and y coordinate for the rotated point.\n */\nfunction rotate2DToPoint(point, origin, angle) {\n    const x = point[0] - origin[0], y = point[1] - origin[1], rotated = rotate2DToOrigin([x, y], angle);\n    return [\n        rotated[0] + origin[0],\n        rotated[1] + origin[1]\n    ];\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst WordcloudUtils = {\n    archimedeanSpiral,\n    extendPlayingField,\n    getBoundingBoxFromPolygon,\n    getPlayingField,\n    getPolygon,\n    getRandomPosition,\n    getRotation,\n    getScale,\n    getSpiral,\n    intersectionTesting,\n    isPolygonsColliding,\n    isRectanglesIntersecting,\n    rectangularSpiral,\n    rotate2DToOrigin,\n    rotate2DToPoint,\n    squareSpiral,\n    updateFieldBoundaries\n};\n/* harmony default export */ const Wordcloud_WordcloudUtils = (WordcloudUtils);\n\n;// ./code/es-modules/Series/Wordcloud/WordcloudSeries.js\n/* *\n *\n *  Experimental Highcharts module which enables visualization of a word cloud.\n *\n *  (c) 2016-2025 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n * */\n\n\n\nconst { noop } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { column: ColumnSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: WordcloudSeries_extend, isArray, isNumber: WordcloudSeries_isNumber, isObject: WordcloudSeries_isObject, merge: WordcloudSeries_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\n\nconst { archimedeanSpiral: WordcloudSeries_archimedeanSpiral, extendPlayingField: WordcloudSeries_extendPlayingField, getBoundingBoxFromPolygon: WordcloudSeries_getBoundingBoxFromPolygon, getPlayingField: WordcloudSeries_getPlayingField, getPolygon: WordcloudSeries_getPolygon, getRandomPosition: WordcloudSeries_getRandomPosition, getRotation: WordcloudSeries_getRotation, getScale: WordcloudSeries_getScale, getSpiral: WordcloudSeries_getSpiral, intersectionTesting: WordcloudSeries_intersectionTesting, isPolygonsColliding: WordcloudSeries_isPolygonsColliding, rectangularSpiral: WordcloudSeries_rectangularSpiral, rotate2DToOrigin: WordcloudSeries_rotate2DToOrigin, rotate2DToPoint: WordcloudSeries_rotate2DToPoint, squareSpiral: WordcloudSeries_squareSpiral, updateFieldBoundaries: WordcloudSeries_updateFieldBoundaries } = Wordcloud_WordcloudUtils;\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.wordcloud\n *\n * @augments Highcharts.Series\n */\nclass WordcloudSeries extends ColumnSeries {\n    /**\n     *\n     * Functions\n     *\n     */\n    pointAttribs(point, state) {\n        const attribs = highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().seriesTypes.column.prototype\n            .pointAttribs.call(this, point, state);\n        delete attribs.stroke;\n        delete attribs['stroke-width'];\n        return attribs;\n    }\n    /**\n     * Calculates the fontSize of a word based on its weight.\n     *\n     * @private\n     * @function Highcharts.Series#deriveFontSize\n     *\n     * @param {number} [relativeWeight=0]\n     * The weight of the word, on a scale 0-1.\n     *\n     * @param {number} [maxFontSize=1]\n     * The maximum font size of a word.\n     *\n     * @param {number} [minFontSize=1]\n     * The minimum font size of a word.\n     *\n     * @return {number}\n     * Returns the resulting fontSize of a word. If minFontSize is larger then\n     * maxFontSize the result will equal minFontSize.\n     */\n    deriveFontSize(relativeWeight, maxFontSize, minFontSize) {\n        const weight = WordcloudSeries_isNumber(relativeWeight) ? relativeWeight : 0, max = WordcloudSeries_isNumber(maxFontSize) ? maxFontSize : 1, min = WordcloudSeries_isNumber(minFontSize) ? minFontSize : 1;\n        return Math.floor(Math.max(min, weight * max));\n    }\n    drawPoints() {\n        if (this.zooming ||\n            (this.defaultScale &&\n                this.group.scaleX !== this.defaultScale)) {\n            return;\n        }\n        const series = this, hasRendered = series.hasRendered, xAxis = series.xAxis, yAxis = series.yAxis, chart = series.chart, group = series.group, options = series.options, animation = options.animation, allowExtendPlayingField = options.allowExtendPlayingField, renderer = chart.renderer, placed = [], placementStrategy = series.placementStrategy[options.placementStrategy], rotation = options.rotation, weights = series.points.map(function (p) {\n            return p.weight;\n        }), maxWeight = Math.max.apply(null, weights), \n        // `concat()` prevents from sorting the original array.\n        points = series.points.concat().sort((a, b) => (b.weight - a.weight // Sort descending\n        ));\n        let testElement = renderer.text().add(group), field;\n        // Reset the scale before finding the dimensions (#11993).\n        // SVGGRaphicsElement.getBBox() (used in SVGElement.getBBox(boolean))\n        // returns slightly different values for the same element depending on\n        // whether it is rendered in a group which has already defined scale\n        // (e.g. 6) or in the group without a scale (scale = 1).\n        series.group.attr({\n            scaleX: 1,\n            scaleY: 1\n        });\n        // Get the dimensions for each word.\n        // Used in calculating the playing field.\n        for (const point of points) {\n            const relativeWeight = 1 / maxWeight * point.weight, fontSize = series.deriveFontSize(relativeWeight, options.maxFontSize, options.minFontSize), css = WordcloudSeries_extend({\n                fontSize: fontSize + 'px'\n            }, options.style);\n            testElement.css(css).attr({\n                x: 0,\n                y: 0,\n                text: point.name\n            });\n            const bBox = testElement.getBBox(true);\n            point.dimensions = {\n                height: bBox.height,\n                width: bBox.width\n            };\n        }\n        // Calculate the playing field.\n        field = WordcloudSeries_getPlayingField(xAxis.len, yAxis.len, points);\n        const spiral = WordcloudSeries_getSpiral(series.spirals[options.spiral], {\n            field: field\n        });\n        // Draw all the points.\n        for (const point of points) {\n            const relativeWeight = 1 / maxWeight * point.weight, fontSize = series.deriveFontSize(relativeWeight, options.maxFontSize, options.minFontSize), css = WordcloudSeries_extend({\n                fontSize: fontSize + 'px'\n            }, options.style), placement = placementStrategy(point, {\n                data: points,\n                field: field,\n                placed: placed,\n                rotation: rotation\n            }), attr = WordcloudSeries_extend(series.pointAttribs(point, (point.selected && 'select')), {\n                align: 'center',\n                'alignment-baseline': 'middle',\n                'dominant-baseline': 'middle', // #15973: Firefox\n                x: placement.x,\n                y: placement.y,\n                text: point.name,\n                rotation: WordcloudSeries_isNumber(placement.rotation) ?\n                    placement.rotation :\n                    void 0\n            }), polygon = WordcloudSeries_getPolygon(placement.x, placement.y, point.dimensions.width, point.dimensions.height, placement.rotation), rectangle = WordcloudSeries_getBoundingBoxFromPolygon(polygon);\n            let delta = WordcloudSeries_intersectionTesting(point, {\n                rectangle: rectangle,\n                polygon: polygon,\n                field: field,\n                placed: placed,\n                spiral: spiral,\n                rotation: placement.rotation\n            }), animate;\n            // If there is no space for the word, extend the playing field.\n            if (!delta && allowExtendPlayingField) {\n                // Extend the playing field to fit the word.\n                field = WordcloudSeries_extendPlayingField(field, rectangle);\n                // Run intersection testing one more time to place the word.\n                delta = WordcloudSeries_intersectionTesting(point, {\n                    rectangle: rectangle,\n                    polygon: polygon,\n                    field: field,\n                    placed: placed,\n                    spiral: spiral,\n                    rotation: placement.rotation\n                });\n            }\n            // Check if point was placed, if so delete it, otherwise place it\n            // on the correct positions.\n            if (WordcloudSeries_isObject(delta)) {\n                attr.x = (attr.x || 0) + delta.x;\n                attr.y = (attr.y || 0) + delta.y;\n                rectangle.left += delta.x;\n                rectangle.right += delta.x;\n                rectangle.top += delta.y;\n                rectangle.bottom += delta.y;\n                field = WordcloudSeries_updateFieldBoundaries(field, rectangle);\n                placed.push(point);\n                point.isNull = false;\n                point.isInside = true; // #15447\n            }\n            else {\n                point.isNull = true;\n            }\n            if (animation) {\n                // Animate to new positions\n                animate = {\n                    x: attr.x,\n                    y: attr.y\n                };\n                // Animate from center of chart\n                if (!hasRendered) {\n                    attr.x = 0;\n                    attr.y = 0;\n                    // Or animate from previous position\n                }\n                else {\n                    delete attr.x;\n                    delete attr.y;\n                }\n            }\n            Series_DrawPointUtilities.draw(point, {\n                animatableAttribs: animate,\n                attribs: attr,\n                css: css,\n                group: group,\n                renderer: renderer,\n                shapeArgs: void 0,\n                shapeType: 'text'\n            });\n        }\n        // Destroy the element after use.\n        testElement = testElement.destroy();\n        // Scale the series group to fit within the plotArea.\n        series.defaultScale = WordcloudSeries_getScale(xAxis.len, yAxis.len, field);\n        series.field = field;\n        series.group.attr({\n            scaleX: series.defaultScale,\n            scaleY: series.defaultScale\n        });\n    }\n    hasData() {\n        const series = this;\n        return (WordcloudSeries_isObject(series) &&\n            series.visible === true &&\n            isArray(series.points) &&\n            series.points.length > 0);\n    }\n    getPlotBox(name) {\n        const series = this, { chart, group, zooming } = this, { plotSizeX = 0, plotSizeY = 0, inverted } = chart, \n        // Swap axes for inverted (#2339)\n        xAxis = series[(inverted ? 'yAxis' : 'xAxis')], yAxis = series[(inverted ? 'xAxis' : 'yAxis')], width = xAxis ? xAxis.len : chart.plotWidth, height = yAxis ? yAxis.len : chart.plotHeight, x = xAxis ? xAxis.left : chart.plotLeft, y = yAxis ? yAxis.top : chart.plotTop, field = series.field;\n        let left = 0, top = 0, translateX = x + width / 2, translateY = y + height / 2, initLeft = translateX, initTop = translateY, scale = series.defaultScale || 1, seriesHeight = 0, seriesWidth = 0;\n        if (field) {\n            seriesHeight =\n                Math.max(Math.abs(field.top), Math.abs(field.bottom)) * 2;\n            seriesWidth =\n                Math.max(Math.abs(field.left), Math.abs(field.right)) * 2;\n        }\n        if (inverted) {\n            [seriesWidth, seriesHeight] = [seriesHeight, seriesWidth];\n        }\n        if (group && zooming) {\n            // Uncomment this block to visualize the zooming\n            // bounding box and the point, which is normalized\n            // position to zoom-in\n            // chart.renderer.rect(\n            //    (plotSizeX - seriesWidth) / 2 + zooming.x * plotSizeX +\n            //        chart.plotLeft,\n            //    (plotSizeY - seriesHeight) / 2 + zooming.y * plotSizeY +\n            //        chart.plotTop,\n            //    zooming.width * plotSizeX,\n            //    zooming.height * plotSizeY,\n            //    0,\n            //    2\n            // ).attr({\n            //    stroke: 'red'\n            // }).add();\n            // chart.renderer.circle(\n            //    (plotSizeX - seriesWidth) / 2 + zooming.zoomX * plotSizeX +\n            //        chart.plotLeft,\n            //    (plotSizeY - seriesHeight) / 2 + zooming.zoomY * plotSizeY +\n            //        chart.plotTop,\n            //    2\n            // ).attr({\n            //    stroke: 'blue'\n            // }).add();\n            scale = Math.max(zooming.scale, series.defaultScale || 1);\n            const newWidth = Math.max(seriesWidth * scale, width), newHeight = Math.max(seriesHeight * scale, height), newMiddleX = x + newWidth / 2, newMiddleY = y + newHeight / 2, scaleDiff = scale - (group.scaleX || 1);\n            left = scaleDiff * ((plotSizeX - seriesWidth) / 2 +\n                zooming.zoomX * plotSizeX - width / 2);\n            top = scaleDiff * ((plotSizeY - seriesHeight) / 2 +\n                zooming.zoomY * plotSizeY - height / 2);\n            if (name === 'series') {\n                zooming.x = Math.max(0, Math.min(1 - zooming.width, zooming.x + (zooming.panX / zooming.scale)));\n                left += zooming.panX * plotSizeX;\n                zooming.panX = 0;\n                zooming.y = Math.max(0, Math.min(1 - zooming.height, zooming.y + (zooming.panY / zooming.scale)));\n                top += zooming.panY * plotSizeY;\n                zooming.panY = 0;\n            }\n            if (WordcloudSeries_isNumber(group.translateX) && WordcloudSeries_isNumber(group.translateY)) {\n                initLeft = group.translateX;\n                initTop = group.translateY;\n            }\n            translateX = initLeft - left;\n            translateY = initTop - top;\n            // Do not allow to move outside the chart\n            // Vertical lock\n            if (translateY > newMiddleY) {\n                translateY = newMiddleY;\n            }\n            else if (translateY < 2 * y + height - newMiddleY) {\n                translateY = 2 * y + height - newMiddleY;\n            }\n            // Horizontal lock\n            if (translateX > newMiddleX) {\n                translateX = newMiddleX;\n            }\n            else if (translateX < 2 * x + width - newMiddleX) {\n                translateX = 2 * x + width - newMiddleX;\n            }\n        }\n        return {\n            translateX: translateX,\n            translateY: translateY,\n            scaleX: scale,\n            scaleY: scale\n        };\n    }\n}\n/* *\n *\n *  Static properties\n *\n * */\nWordcloudSeries.defaultOptions = WordcloudSeries_merge(ColumnSeries.defaultOptions, Wordcloud_WordcloudSeriesDefaults);\nWordcloudSeries_extend(WordcloudSeries.prototype, {\n    animate: noop,\n    animateDrilldown: noop,\n    animateDrillupFrom: noop,\n    isCartesian: false,\n    pointClass: Wordcloud_WordcloudPoint,\n    setClip: noop,\n    // Strategies used for deciding rotation and initial position of a word. To\n    // implement a custom strategy, have a look at the function random for\n    // example.\n    placementStrategy: {\n        random: function (point, options) {\n            const field = options.field, r = options.rotation;\n            return {\n                x: WordcloudSeries_getRandomPosition(field.width) - (field.width / 2),\n                y: WordcloudSeries_getRandomPosition(field.height) - (field.height / 2),\n                rotation: WordcloudSeries_getRotation(r.orientations, point.index, r.from, r.to)\n            };\n        },\n        center: function (point, options) {\n            const r = options.rotation;\n            return {\n                x: 0,\n                y: 0,\n                rotation: WordcloudSeries_getRotation(r.orientations, point.index, r.from, r.to)\n            };\n        }\n    },\n    pointArrayMap: ['weight'],\n    // Spirals used for placing a word after the initial position experienced a\n    // collision with either another word or the borders. To implement a custom\n    // spiral, look at the function archimedeanSpiral for example.\n    spirals: {\n        'archimedean': WordcloudSeries_archimedeanSpiral,\n        'rectangular': WordcloudSeries_rectangularSpiral,\n        'square': WordcloudSeries_squareSpiral\n    },\n    utils: {\n        extendPlayingField: WordcloudSeries_extendPlayingField,\n        getRotation: WordcloudSeries_getRotation,\n        isPolygonsColliding: WordcloudSeries_isPolygonsColliding,\n        rotate2DToOrigin: WordcloudSeries_rotate2DToOrigin,\n        rotate2DToPoint: WordcloudSeries_rotate2DToPoint\n    }\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('wordcloud', WordcloudSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Wordcloud_WordcloudSeries = ((/* unused pure expression or super */ null && (WordcloudSeries)));\n\n;// ./code/es-modules/masters/modules/wordcloud.js\n\n\n\n\n/* harmony default export */ const wordcloud_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "wordcloud_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "Series_DrawPointUtilities", "draw", "point", "params", "animatableAttribs", "onComplete", "css", "renderer", "animation", "series", "chart", "hasRendered", "options", "graphic", "attribs", "getClassName", "shouldDraw", "shapeType", "text", "image", "imageUrl", "attr", "shapeArgs", "add", "group", "animate", "isNew", "destroy", "keys", "length", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "column", "pointClass", "ColumnPoint", "seriesTypes", "extend", "WordcloudPoint", "<PERSON><PERSON><PERSON><PERSON>", "weight", "deg2rad", "WordcloudUtils_extend", "find", "isNumber", "isObject", "merge", "isRectanglesIntersecting", "r1", "r2", "left", "right", "top", "bottom", "getAxesFromPolygon", "polygon", "points", "axes", "concat", "reduce", "p1", "p2", "axis", "normals", "getNormals", "dx", "dy", "existing", "push", "project", "target", "products", "map", "ax", "ay", "min", "Math", "apply", "max", "isPolygonsColliding", "polygon1", "polygon2", "axes1", "axes2", "isPolygonsOverlappingOnAxis", "projection1", "projection2", "squareSpiral", "attempt", "k", "ceil", "sqrt", "t", "m", "pow", "result", "isBoolean", "x", "y", "correctFloat", "number", "precision", "magnitude", "round", "rotate2DToOrigin", "angle", "rad", "cosAngle", "cos", "sinAngle", "sin", "rotate2DToPoint", "origin", "rotated", "noop", "ColumnSeries", "WordcloudSeries_extend", "isArray", "WordcloudSeries_isNumber", "WordcloudSeries_isObject", "WordcloudSeries_merge", "archimedeanSpiral", "WordcloudSeries_archimedeanSpiral", "extendPlayingField", "WordcloudSeries_extendPlayingField", "getBoundingBoxFromPolygon", "WordcloudSeries_getBoundingBoxFromPolygon", "getPlayingField", "WordcloudSeries_getPlayingField", "getPolygon", "WordcloudSeries_getPolygon", "getRandomPosition", "WordcloudSeries_getRandomPosition", "getRotation", "WordcloudSeries_getRotation", "getScale", "WordcloudSeries_getScale", "getSpiral", "WordcloudSeries_getSpiral", "intersectionTesting", "WordcloudSeries_intersectionTesting", "WordcloudSeries_isPolygonsColliding", "rectangularSpiral", "WordcloudSeries_rectangularSpiral", "WordcloudSeries_rotate2DToOrigin", "WordcloudSeries_rotate2DToPoint", "WordcloudSeries_squareSpiral", "updateFieldBoundaries", "WordcloudSeries_updateFieldBoundaries", "field", "max<PERSON><PERSON><PERSON>", "width", "height", "abs", "rectangle", "ratioX", "ratioY", "extendWidth", "extendHeight", "Number", "MAX_VALUE", "targetWidth", "targetHeight", "data", "info", "dimensions", "maxHeight", "max<PERSON><PERSON><PERSON>", "area", "rotation", "size", "random", "orientations", "index", "from", "to", "intervals", "range", "orientation", "fn", "arr", "i", "placed", "spiral", "rect", "delta", "intersectsAnyWord", "lastCollidedWith", "isIntersecting", "p", "intersects", "outsidePlayingField", "playingField", "movePolygon", "deltaX", "deltaY", "WordcloudSeries", "pointAttribs", "state", "stroke", "deriveFontSize", "relativeWeight", "maxFontSize", "minFontSize", "floor", "drawPoints", "zooming", "defaultScale", "scaleX", "xAxis", "yAxis", "allowExtendPlayingField", "placementStrategy", "weights", "maxWeight", "sort", "b", "testElement", "scaleY", "fontSize", "style", "name", "bBox", "getBBox", "len", "spirals", "placement", "selected", "align", "isNull", "isInside", "hasData", "visible", "getPlotBox", "plotSizeX", "plotSizeY", "inverted", "plot<PERSON>id<PERSON>", "plotHeight", "plotLeft", "plotTop", "translateX", "translateY", "initLeft", "initTop", "scale", "seriesHeight", "seriesWidth", "newMiddleX", "newWidth", "newMiddleY", "newHeight", "scaleDiff", "zoomX", "zoomY", "panX", "panY", "defaultOptions", "duration", "borderWidth", "clip", "colorByPoint", "cropThreshold", "Infinity", "showInLegend", "fontFamily", "fontWeight", "whiteSpace", "tooltip", "followPointer", "pointFormat", "animateDrilldown", "animateDrillupFrom", "isCartesian", "setClip", "r", "center", "pointArrayMap", "utils", "registerSeriesType"], "mappings": "CAUA,AAVA;;;;;;;;;CASC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,+BAAgC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACxH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,+BAA+B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAE5GA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAkFxF,IAAME,EAHR,CACvBC,KAzDJ,SAAcC,CAAK,CAAEC,CAAM,EACvB,GAAM,CAAEC,kBAAAA,CAAiB,CAAEC,WAAAA,CAAU,CAAEC,IAAAA,CAAG,CAAEC,SAAAA,CAAQ,CAAE,CAAGJ,EACnDK,EAAY,AAACN,EAAMO,MAAM,EAAIP,EAAMO,MAAM,CAACC,KAAK,CAACC,WAAW,CAE7D,KAAK,EAEJT,EAAMO,MAAM,EACTP,EAAMO,MAAM,CAACG,OAAO,CAACJ,SAAS,CAClCK,EAAUX,EAAMW,OAAO,CAK3B,GAJAV,EAAOW,OAAO,CAAG,CACb,GAAGX,EAAOW,OAAO,CACjB,MAASZ,EAAMa,YAAY,EAC/B,EACKb,EAAMc,UAAU,GACZH,IAWDX,EAAMW,OAAO,CATTA,EADAV,AAAqB,SAArBA,EAAOc,SAAS,CACNV,EAASW,IAAI,GAElBf,AAAqB,UAArBA,EAAOc,SAAS,CACXV,EAASY,KAAK,CAAChB,EAAOiB,QAAQ,EAAI,IACvCC,IAAI,CAAClB,EAAOmB,SAAS,EAAI,CAAC,GAGrBf,CAAQ,CAACJ,EAAOc,SAAS,CAAC,CAACd,EAAOmB,SAAS,EAAI,CAAC,GAG9DT,EAAQU,GAAG,CAACpB,EAAOqB,KAAK,GAExBlB,GACAO,EAAQP,GAAG,CAACA,GAEhBO,EACKQ,IAAI,CAAClB,EAAOW,OAAO,EACnBW,OAAO,CAACrB,EAAmBD,CAAAA,EAAOuB,KAAK,EAAWlB,EAAWH,QAEjE,GAAIQ,EAAS,CACd,IAAMc,EAAU,KACZzB,EAAMW,OAAO,CAAGA,EAAWA,GAAWA,EAAQc,OAAO,GACjD,AAAsB,YAAtB,OAAOtB,GACPA,GAER,CAEIlB,CAAAA,OAAOyC,IAAI,CAACxB,GAAmByB,MAAM,CACrChB,EAAQY,OAAO,CAACrB,EAAmB,KAAK,EAAG,IAAMuB,KAGjDA,GAER,CACJ,CAQA,EAIA,IAAIG,EAAmIvD,EAAoB,KACvJwD,EAAuJxD,EAAoBI,CAAC,CAACmD,GAejL,GAAM,CAAEE,OAAQ,CAAEvC,UAAW,CAAEwC,WAAYC,CAAW,CAAE,CAAE,CAAE,CAAG,AAACH,IAA2II,WAAW,CAEhN,CAAEC,OAAAA,CAAM,CAAE,CAAIrC,GAMpB,OAAMsC,UAAuBH,EAMzBI,SAAU,CACN,MAAO,CAAA,CACX,CACJ,CACAF,EAAOC,EAAe5C,SAAS,CAAE,CAC7B8C,OAAQ,CACZ,GAqOA,GAAM,CAAEC,QAAAA,CAAO,CAAE,CAAIzC,IAEf,CAAEqC,OAAQK,CAAqB,CAAEC,KAAAA,CAAI,CAAEC,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAE,CAAI9C,IAqB5E,SAAS+C,EAAyBC,CAAE,CAAEC,CAAE,EACpC,MAAO,CAAEA,CAAAA,EAAGC,IAAI,CAAGF,EAAGG,KAAK,EACvBF,EAAGE,KAAK,CAAGH,EAAGE,IAAI,EAClBD,EAAGG,GAAG,CAAGJ,EAAGK,MAAM,EAClBJ,EAAGI,MAAM,CAAGL,EAAGI,GAAG,AAAD,CACzB,CAwBA,SAASE,EAAmBC,CAAO,EAC/B,IAAIC,EAAQC,EAAOF,EAAQE,IAAI,EAAI,EAAE,CAgBrC,OAfKA,EAAK3B,MAAM,GACZ2B,EAAO,EAAE,CAETD,AADAA,CAAAA,EAAkBD,EAAQG,MAAM,CAAC,CAACH,CAAO,CAAC,EAAE,CAAC,CAAA,EACtCI,MAAM,CAAC,CAACC,EAAIC,KACf,IAAoCC,EAAOC,AAA3BC,AAjB5B,SAAoBJ,CAAE,CAAEC,CAAE,EACtB,IAAMI,EAAKJ,CAAE,CAAC,EAAE,CAAGD,CAAE,CAAC,EAAE,CACxBM,EAAKL,CAAE,CAAC,EAAE,CAAGD,CAAE,CAAC,EAAE,CAClB,MAAO,CACH,CAAC,CAACM,EAAID,EAAG,CACT,CAACC,EAAI,CAACD,EAAG,CACZ,AACL,EAUuCL,EAAIC,EAAmB,CAAC,EAAE,CAOrD,OALI,AAAClB,EAAKc,EAAM,AAACU,GAAaA,CAAQ,CAAC,EAAE,GAAKL,CAAI,CAAC,EAAE,EACjDK,CAAQ,CAAC,EAAE,GAAKL,CAAI,CAAC,EAAE,GACvBL,EAAKW,IAAI,CAACN,GAGPD,CACX,GACAN,EAAQE,IAAI,CAAGA,GAEZA,CACX,CAWA,SAASY,EAAQd,CAAO,CAAEe,CAAM,EAC5B,IAAMC,EAAWhB,EAAQiB,GAAG,CAAC,AAACrE,IAC1B,IAAMsE,EAAKtE,CAAK,CAAC,EAAE,CAAEuE,EAAKvE,CAAK,CAAC,EAAE,CAClC,OAAOsE,EADkCH,CAAM,CAAC,EAAE,CACjCI,EADwCJ,CAAM,CAAC,EAAE,AAEtE,GACA,MAAO,CACHK,IAAKC,KAAKD,GAAG,CAACE,KAAK,CAAC,IAAI,CAAEN,GAC1BO,IAAKF,KAAKE,GAAG,CAACD,KAAK,CAAC,IAAI,CAAEN,EAC9B,CACJ,CAwBA,SAASQ,EAAoBC,CAAQ,CAAEC,CAAQ,EAE3C,IAAMC,EAAQ5B,EAAmB0B,GAAWG,EAAQ7B,EAAmB2B,GACvE,MADqI,CAACtC,EAA7CuC,EAAMxB,MAAM,CAACyB,GAA2C,AAACrB,GAASsB,AAtB/J,CAAA,SAAqCtB,CAAI,CAAEkB,CAAQ,CAAEC,CAAQ,EACzD,IAAMI,EAAchB,EAAQW,EAAUlB,GAAOwB,EAAcjB,EAAQY,EAAUnB,GAE7E,MAAO,CAF6F,CAAEwB,CAAAA,EAAYX,GAAG,CAAGU,EAAYP,GAAG,EACnIQ,EAAYR,GAAG,CAAGO,EAAYV,GAAG,AAAD,CAExC,CAAA,EAkB2Lb,EAAMkB,EAAUC,GAE3M,CAgGA,SAASM,EAAaC,CAAO,CAE7BpF,CAAM,EACF,IAAMpB,EAAIwG,AAAU,EAAVA,EAAaC,EAAIb,KAAKc,IAAI,CAAC,AAACd,CAAAA,KAAKe,IAAI,CAAC3G,GAAK,CAAA,EAAK,GACtD4G,EAAI,EAAIH,EAAI,EAAGI,EAAIjB,KAAKkB,GAAG,CAACF,EAAG,GAAIG,EAAS,CAAA,EAkChD,OAjCAH,GAAK,EACDJ,GAAW,MACPQ,AAJuF,WAAb,OAIhED,GAAW/G,GAAK6G,EAAID,GAC9BG,CAAAA,EAAS,CACLE,EAAGR,EAAKI,CAAAA,EAAI7G,CAAAA,EACZkH,EAAG,CAACT,CACR,CAAA,EAEJI,GAAKD,EACDI,AAXuF,WAAb,OAWhED,GAAW/G,GAAK6G,EAAID,GAC9BG,CAAAA,EAAS,CACLE,EAAG,CAACR,EACJS,EAAG,CAACT,EAAKI,CAAAA,EAAI7G,CAAAA,CACjB,CAAA,EAEJ6G,GAAKD,EAjByE,AAAa,WAAb,OAkBhEG,IAENA,EADA/G,GAAK6G,EAAID,EACA,CACLK,EAAG,CAACR,EAAKI,CAAAA,EAAI7G,CAAAA,EACbkH,EAAGT,CACP,EAGS,CACLQ,EAAGR,EACHS,EAAGT,EAAKI,CAAAA,EAAI7G,EAAI4G,CAAAA,CACpB,GAGRG,EAAOE,CAAC,EAAI,EACZF,EAAOG,CAAC,EAAI,GAETH,CACX,CA2VA,SAASI,EAAaC,CAAM,CAAEC,CAAS,EACnC,IAAgDC,EAAY1B,KAAKkB,GAAG,CAAC,GAA3DlD,EAASyD,GAAaA,EAAY,IAC5C,OAAOzB,KAAK2B,KAAK,CAACH,EAASE,GAAaA,CAC5C,CA6CA,SAASE,EAAiBrG,CAAK,CAAEsG,CAAK,EAClC,IAAMR,EAAI9F,CAAK,CAAC,EAAE,CAAE+F,EAAI/F,CAAK,CAAC,EAAE,CAAEuG,EAAMjE,CAAAA,CAAAA,EAAWgE,CAAI,EAAGE,EAAW/B,KAAKgC,GAAG,CAACF,GAAMG,EAAWjC,KAAKkC,GAAG,CAACJ,GACxG,MAAO,CACHP,EAAaF,EAAIU,EAAWT,EAAIW,GAChCV,EAAaF,EAAIY,EAAWX,EAAIS,GACnC,AACL,CAeA,SAASI,EAAgB5G,CAAK,CAAE6G,CAAM,CAAEP,CAAK,EACzC,IAA0DQ,EAAUT,EAAiB,CAA3ErG,CAAK,CAAC,EAAE,CAAG6G,CAAM,CAAC,EAAE,CAAM7G,CAAK,CAAC,EAAE,CAAG6G,CAAM,CAAC,EAAE,CAAmC,CAAEP,GAC7F,MAAO,CACHQ,CAAO,CAAC,EAAE,CAAGD,CAAM,CAAC,EAAE,CACtBC,CAAO,CAAC,EAAE,CAAGD,CAAM,CAAC,EAAE,CACzB,AACL,CA0CA,GAAM,CAAEE,KAAAA,CAAI,CAAE,CAAIlH,IAEZ,CAAEiC,OAAQkF,CAAY,CAAE,CAAG,AAACnF,IAA2II,WAAW,CAElL,CAAEC,OAAQ+E,CAAsB,CAAEC,QAAAA,CAAO,CAAEzE,SAAU0E,CAAwB,CAAEzE,SAAU0E,CAAwB,CAAEzE,MAAO0E,CAAqB,CAAE,CAAIxH,IAIrJ,CAAEyH,kBAAmBC,CAAiC,CAAEC,mBAAoBC,CAAkC,CAAEC,0BAA2BC,CAAyC,CAAEC,gBAAiBC,CAA+B,CAAEC,WAAYC,CAA0B,CAAEC,kBAAmBC,CAAiC,CAAEC,YAAaC,CAA2B,CAAEC,SAAUC,CAAwB,CAAEC,UAAWC,CAAyB,CAAEC,oBAAqBC,CAAmC,CAAE7D,oBAAqB8D,CAAmC,CAAEC,kBAAmBC,CAAiC,CAAEvC,iBAAkBwC,CAAgC,CAAEjC,gBAAiBkC,CAA+B,CAAE1D,aAAc2D,CAA4B,CAAEC,sBAAuBC,CAAqC,CAAE,CA5CnyB,CACnB3B,kBAnfJ,SAA2BjC,CAAO,CAAEpF,CAAM,EACtC,IAAMiJ,EAAQjJ,EAAOiJ,KAAK,CAAEC,EAAW,AAACD,EAAME,KAAK,CAAGF,EAAME,KAAK,CAAKF,EAAMG,MAAM,CAAGH,EAAMG,MAAM,CAAG5D,EAAIJ,AAAU,GAAVA,EACpGO,EAAS,CAAA,EAWb,OATIP,GAAW,KAKP,CAAA,AAAEZ,KAAKD,GAAG,CAACC,KAAK6E,GAAG,CAAC1D,AAJxBA,CAAAA,EAAS,CACLE,EAAGL,EAAIhB,KAAKgC,GAAG,CAAChB,GAChBM,EAAGN,EAAIhB,KAAKkC,GAAG,CAAClB,EACpB,CAAA,EAC+BK,CAAC,EAAGrB,KAAK6E,GAAG,CAAC1D,EAAOG,CAAC,GAAKoD,GACrDvD,CAAAA,EAAS,CAAA,CAAI,CADgD,EAI9DA,CACX,EAseI4B,mBAtJJ,SAA4B0B,CAAK,CAAEK,CAAS,EACxC,IAAIF,EAAQD,EAAOI,EAAQC,EAAQ3D,EAA8BF,EAwBjE,OAvBIlD,EAASwG,IAAUxG,EAAS6G,IAC5BF,EAAUE,EAAUrG,MAAM,CAAGqG,EAAUtG,GAAG,CAC1CmG,EAASG,EAAUvG,KAAK,CAAGuG,EAAUxG,IAAI,CAIzC+C,EAAI,AAAC,AAACsD,EAHNI,CAAAA,EAASN,EAAMM,MAAM,AAAD,EAGKH,EAFzBI,CAAAA,EAASP,EAAMO,MAAM,AAAD,EAEyBL,EAAQC,EAMrDzD,EAASjD,EAAMuG,EAAO,CAElBE,MAAOF,EAAME,KAAK,CAAIM,AANZ5D,EAAI0D,EAMsB,EAEpCH,OAAQH,EAAMG,MAAM,CAAIM,AAPb7D,EAAI2D,EAOwB,CAC3C,IAGA7D,EAASsD,EAGNtD,CACX,EA6HI8B,0BA7EJ,SAAmCrE,CAAM,EACrC,OAAOA,EAAOG,MAAM,CAAC,SAAUnE,CAAG,CAAEW,CAAK,EACrC,IAAM8F,EAAI9F,CAAK,CAAC,EAAE,CAAE+F,EAAI/F,CAAK,CAAC,EAAE,CAKhC,OAJAX,EAAI0D,IAAI,CAAG0B,KAAKD,GAAG,CAACsB,EAAGzG,EAAI0D,IAAI,EAC/B1D,EAAI2D,KAAK,CAAGyB,KAAKE,GAAG,CAACmB,EAAGzG,EAAI2D,KAAK,EACjC3D,EAAI6D,MAAM,CAAGuB,KAAKE,GAAG,CAACoB,EAAG1G,EAAI6D,MAAM,EACnC7D,EAAI4D,GAAG,CAAGwB,KAAKD,GAAG,CAACuB,EAAG1G,EAAI4D,GAAG,EACtB5D,CACX,EAAG,CACC0D,KAAM6G,OAAOC,SAAS,CACtB7G,MAAO,CAAC4G,OAAOC,SAAS,CACxB3G,OAAQ,CAAC0G,OAAOC,SAAS,CACzB5G,IAAK2G,OAAOC,SAAS,AACzB,EACJ,EAgEIjC,gBA5VJ,SAAyBkC,CAAW,CAAEC,CAAY,CAAEC,CAAI,EACpD,IAAMC,EAAOD,EAAKxG,MAAM,CAAC,SAAUnE,CAAG,CAAEW,CAAK,EACzC,IAAMkK,EAAalK,EAAMkK,UAAU,CAAEpE,EAAIrB,KAAKE,GAAG,CAACuF,EAAWd,KAAK,CAAEc,EAAWb,MAAM,EAOrF,OALAhK,EAAI8K,SAAS,CAAG1F,KAAKE,GAAG,CAACtF,EAAI8K,SAAS,CAAED,EAAWb,MAAM,EAEzDhK,EAAI+K,QAAQ,CAAG3F,KAAKE,GAAG,CAACtF,EAAI+K,QAAQ,CAAEF,EAAWd,KAAK,EAEtD/J,EAAIgL,IAAI,EAAIvE,EAAIA,EACTzG,CACX,EAAG,CACC8K,UAAW,EACXC,SAAU,EACVC,KAAM,CACV,GAKAvE,EAAIrB,KAAKE,GAAG,CAACsF,EAAKE,SAAS,CAC3BF,EAAKG,QAAQ,CAEb3F,AAAuB,IAAvBA,KAAKe,IAAI,CAACyE,EAAKI,IAAI,GAAWb,EAASM,EAAcC,EAAeD,EAAcC,EAAe,EAAGN,EAASM,EAAeD,EAAcC,EAAeD,EAAc,EACvK,MAAO,CACHV,MAAOtD,EAAI0D,EACXH,OAAQvD,EAAI2D,EACZD,OAAQA,EACRC,OAAQA,CACZ,CACJ,EAgUI3B,WA7DJ,SAAoBhC,CAAC,CAAEC,CAAC,CAAEqD,CAAK,CAAEC,CAAM,CAAEiB,CAAQ,EAC7C,IAAMzD,EAAS,CAACf,EAAGC,EAAE,CAAEhD,EAAO+C,EAAKsD,EAAQ,EAAIpG,EAAQ8C,EAAKsD,EAAQ,EAAInG,EAAM8C,EAAKsD,EAAS,EAAInG,EAAS6C,EAAKsD,EAAS,EAMvH,MAAOjG,AAN8H,CACjI,CAACL,EAAME,EAAI,CACX,CAACD,EAAOC,EAAI,CACZ,CAACD,EAAOE,EAAO,CACf,CAACH,EAAMG,EAAO,CACjB,CACcmB,GAAG,CAAC,SAAUrE,CAAK,EAC9B,OAAO4G,EAAgB5G,EAAO6G,EAAQ,CAACyD,EAC3C,EACJ,EAoDItC,kBA/YJ,SAA2BuC,CAAI,EAC3B,OAAO9F,KAAK2B,KAAK,CAAC,AAACmE,EAAQ9F,CAAAA,KAAK+F,MAAM,GAAK,EAAE,EAAM,EACvD,EA8YItC,YA1SJ,SAAqBuC,CAAY,CAAEC,CAAK,CAAEC,CAAI,CAAEC,CAAE,EAC9C,IAAIhF,EAAS,CAAA,EACNiF,EAcP,OAZIpI,EAASgI,IACThI,EAASiI,IACTjI,EAASkI,IACTlI,EAASmI,IACTH,EAAe,GACfC,EAAQ,IACRE,EAAKD,IAELE,EAAYC,AADJF,CAAAA,EAAKD,CAAG,EACKF,CAAAA,EAAe,GAAK,CAAA,EAEzC7E,EAAS+E,EAAQI,AADHL,EAAQD,EACSI,GAE5BjF,CACX,EA0RIwC,SA3XJ,SAAkB0B,CAAW,CAAEC,CAAY,CAAEb,CAAK,EAC9C,IAAMG,EAAS5E,AAAwD,EAAxDA,KAAKE,GAAG,CAACF,KAAK6E,GAAG,CAACJ,EAAMjG,GAAG,EAAGwB,KAAK6E,GAAG,CAACJ,EAAMhG,MAAM,GAAQkG,EAAQ3E,AAAwD,EAAxDA,KAAKE,GAAG,CAACF,KAAK6E,GAAG,CAACJ,EAAMnG,IAAI,EAAG0B,KAAK6E,GAAG,CAACJ,EAAMlG,KAAK,GACrI,OAAOyB,KAAKD,GAAG,CADuI4E,EAAQ,EAAI,EAAIA,EAAQU,EAAc,EAAYT,EAAS,EAAI,EAAIA,EAASU,EAAe,EAErP,EAyXIzB,UA3QJ,SAAmB0C,CAAE,CAAE/K,CAAM,EACzB,IAAsBgL,EAAM,EAAE,CAC9B,IAAK,IAAIC,EAAI,EAAGA,EADD,IACaA,IAExBD,EAAIhH,IAAI,CAAC+G,EAAGE,EAAGjL,IAEnB,OAAO,AAACoF,GAAaA,GALN,KAK0B4F,CAAG,CAAC5F,EAAU,EAAE,AAC7D,EAqQImD,oBA5MJ,SAA6BxI,CAAK,CAAEU,CAAO,EACvC,IAAMyK,EAASzK,EAAQyK,MAAM,CAAEjC,EAAQxI,EAAQwI,KAAK,CAAEK,EAAY7I,EAAQ6I,SAAS,CAAEnG,EAAU1C,EAAQ0C,OAAO,CAAEgI,EAAS1K,EAAQ0K,MAAM,CAEvIC,EAAOrL,EAAMqL,IAAI,CAAG9I,EAAsB,CAAC,EAAGgH,GAC1ClE,EAAU,EAAGiG,EAAQ,CACrBxF,EAAG,EACHC,EAAG,CACP,EAQA,IAPA/F,EAAMoD,OAAO,CAAGA,EAChBpD,EAAMsK,QAAQ,CAAG5J,EAAQ4J,QAAQ,CAM1BgB,AAAU,CAAA,IAAVA,GACFC,CAAAA,AAjXT,SAA2BvL,CAAK,CAAEqD,CAAM,EACpC,IAAMgI,EAAOrL,EAAMqL,IAAI,CAAEjI,EAAUpD,EAAMoD,OAAO,CAAEoI,EAAmBxL,EAAMwL,gBAAgB,CAAEC,EAAiB,SAAUC,CAAC,EACrH,IAAI9F,EAAShD,EAAyByI,EAAMK,EAAEL,IAAI,EAKlD,OAJIzF,GACC5F,CAAAA,EAAMsK,QAAQ,CAAG,IAAMoB,EAAEpB,QAAQ,CAAG,EAAC,GACtC1E,CAAAA,EAAShB,EAAoBxB,EAASsI,EAAEtI,OAAO,CAAA,EAE5CwC,CACX,EACI+F,EAAa,CAAA,EAsBjB,OAlBIH,GAGI,CAAA,AAFJG,CAAAA,EAAaF,EAAeD,EAAgB,GAGxC,OAAOxL,EAAMwL,gBAAgB,AADnB,EAMd,AAACG,GACDA,CAAAA,EAAa,CAAC,CAACnJ,EAAKa,EAAQ,SAAUqI,CAAC,EACnC,IAAM9F,EAAS6F,EAAeC,GAI9B,OAHI9F,GACA5F,CAAAA,EAAMwL,gBAAgB,CAAGE,CAAAA,EAEtB9F,CACX,EAAC,EAEE+F,CACX,EAiV2B3L,EAAOmL,IACtBS,AA1DZ,SAA6BP,CAAI,CAAEnC,CAAK,EACpC,IAAM2C,EAAe,CACjB9I,KAAM,CAAEmG,CAAAA,EAAME,KAAK,CAAG,CAAA,EACtBpG,MAAOkG,EAAME,KAAK,CAAG,EACrBnG,IAAK,CAAEiG,CAAAA,EAAMG,MAAM,CAAG,CAAA,EACtBnG,OAAQgG,EAAMG,MAAM,CAAG,CAC3B,EACA,MAAO,CAAEwC,CAAAA,EAAa9I,IAAI,CAAGsI,EAAKtI,IAAI,EAClC8I,EAAa7I,KAAK,CAAGqI,EAAKrI,KAAK,EAC/B6I,EAAa5I,GAAG,CAAGoI,EAAKpI,GAAG,EAC3B4I,EAAa3I,MAAM,CAAGmI,EAAKnI,MAAM,AAAD,CACxC,EA+CgCmI,EAAMnC,EAAK,GAE/BxG,EADJ4I,EAAQF,EAAO/F,MAGXgG,EAAKtI,IAAI,CAAGwG,EAAUxG,IAAI,CAAGuI,EAAMxF,CAAC,CACpCuF,EAAKrI,KAAK,CAAGuG,EAAUvG,KAAK,CAAGsI,EAAMxF,CAAC,CACtCuF,EAAKpI,GAAG,CAAGsG,EAAUtG,GAAG,CAAGqI,EAAMvF,CAAC,CAClCsF,EAAKnI,MAAM,CAAGqG,EAAUrG,MAAM,CAAGoI,EAAMvF,CAAC,CACxC/F,EAAMoD,OAAO,CAAG0I,AAnD5B,SAAqBC,CAAM,CAAEC,CAAM,CAAE5I,CAAO,EACxC,OAAOA,EAAQiB,GAAG,CAAC,SAAUrE,CAAK,EAC9B,MAAO,CACHA,CAAK,CAAC,EAAE,CAAG+L,EACX/L,CAAK,CAAC,EAAE,CAAGgM,EACd,AACL,EACJ,EA4CwCV,EAAMxF,CAAC,CAAEwF,EAAMvF,CAAC,CAAE3C,IAElDiC,IAEJ,OAAOiG,CACX,EA+KI1G,oBAAAA,EACAhC,yBAAAA,EACA+F,kBAxaJ,SAA2BtD,CAAO,CAAEpF,CAAM,EACtC,IAAM2F,EAASR,EAAaC,EAASpF,GAASiJ,EAAQjJ,EAAOiJ,KAAK,CAKlE,OAJItD,IACAA,EAAOE,CAAC,EAAIoD,EAAMM,MAAM,CACxB5D,EAAOG,CAAC,EAAImD,EAAMO,MAAM,EAErB7D,CACX,EAkaIS,iBAAAA,EACAO,gBAAAA,EACAxB,aAAAA,EACA4D,sBAzHJ,SAA+BE,CAAK,CAAEK,CAAS,EAc3C,MAZI,CAAA,CAAC9G,EAASyG,EAAMnG,IAAI,GAAKmG,EAAMnG,IAAI,CAAGwG,EAAUxG,IAAI,AAAD,GACnDmG,CAAAA,EAAMnG,IAAI,CAAGwG,EAAUxG,IAAI,AAAD,EAE1B,CAAA,CAACN,EAASyG,EAAMlG,KAAK,GAAKkG,EAAMlG,KAAK,CAAGuG,EAAUvG,KAAK,AAAD,GACtDkG,CAAAA,EAAMlG,KAAK,CAAGuG,EAAUvG,KAAK,AAAD,EAE5B,CAAA,CAACP,EAASyG,EAAMjG,GAAG,GAAKiG,EAAMjG,GAAG,CAAGsG,EAAUtG,GAAG,AAAD,GAChDiG,CAAAA,EAAMjG,GAAG,CAAGsG,EAAUtG,GAAG,AAAD,EAExB,CAAA,CAACR,EAASyG,EAAMhG,MAAM,GAAKgG,EAAMhG,MAAM,CAAGqG,EAAUrG,MAAM,AAAD,GACzDgG,CAAAA,EAAMhG,MAAM,CAAGqG,EAAUrG,MAAM,AAAD,EAE3BgG,CACX,CA2GA,CAuCA,OAAM+C,UAAwBjF,EAM1BkF,aAAalM,CAAK,CAAEmM,CAAK,CAAE,CACvB,IAAMvL,EAAUf,IAA8EoC,WAAW,CAACH,MAAM,CAACvC,SAAS,CACrH2M,YAAY,CAACzM,IAAI,CAAC,IAAI,CAAEO,EAAOmM,GAGpC,OAFA,OAAOvL,EAAQwL,MAAM,CACrB,OAAOxL,CAAO,CAAC,eAAe,CACvBA,CACX,CAoBAyL,eAAeC,CAAc,CAAEC,CAAW,CAAEC,CAAW,CAAE,CACrD,IAAMnK,EAAS8E,EAAyBmF,GAAkBA,EAAiB,EAAG3H,EAAMwC,EAAyBoF,GAAeA,EAAc,EAC1I,OAAO9H,KAAKgI,KAAK,CAAChI,KAAKE,GAAG,CADyHwC,EAAyBqF,GAAeA,EAAc,EACzKnK,EAASsC,GAC7C,CACA+H,YAAa,CACT,GAAI,IAAI,CAACC,OAAO,EACX,IAAI,CAACC,YAAY,EACd,IAAI,CAACtL,KAAK,CAACuL,MAAM,GAAK,IAAI,CAACD,YAAY,CAC3C,OAEJ,IAAqBnM,EAAcF,AAApB,IAAI,CAAuBE,WAAW,CAAEqM,EAAQvM,AAAhD,IAAI,CAAmDuM,KAAK,CAAEC,EAAQxM,AAAtE,IAAI,CAAyEwM,KAAK,CAAEvM,EAAQD,AAA5F,IAAI,CAA+FC,KAAK,CAAEc,EAAQf,AAAlH,IAAI,CAAqHe,KAAK,CAAEZ,EAAUH,AAA1I,IAAI,CAA6IG,OAAO,CAAEJ,EAAYI,EAAQJ,SAAS,CAAE0M,EAA0BtM,EAAQsM,uBAAuB,CAAE3M,EAAWG,EAAMH,QAAQ,CAAE8K,EAAS,EAAE,CAAE8B,EAAoB1M,AAAhT,IAAI,CAAmT0M,iBAAiB,CAACvM,EAAQuM,iBAAiB,CAAC,CAAE3C,EAAW5J,EAAQ4J,QAAQ,CAAE4C,EAAU3M,AAA5Y,IAAI,CAA+Y8C,MAAM,CAACgB,GAAG,CAAC,SAAUqH,CAAC,EACpb,OAAOA,EAAErJ,MAAM,AACnB,GAAI8K,EAAY1I,KAAKE,GAAG,CAACD,KAAK,CAAC,KAAMwI,GAErC7J,EAAS9C,AAJM,IAAI,CAIH8C,MAAM,CAACE,MAAM,GAAG6J,IAAI,CAAC,CAACvO,EAAGwO,IAAOA,EAAEhL,MAAM,CAAGxD,EAAEwD,MAAM,EAE/DiL,EAAcjN,EAASW,IAAI,GAAGK,GAAG,CAACC,GAAQ4H,EAY9C,IAAK,IAAMlJ,KANXO,AAZe,IAAI,CAYZe,KAAK,CAACH,IAAI,CAAC,CACd0L,OAAQ,EACRU,OAAQ,CACZ,GAGoBlK,GAAQ,CACxB,IAAMiJ,EAAiB,EAAIa,EAAYnN,EAAMqC,MAAM,CAA8FjC,EAAM6G,EAAuB,CAC1KuG,SAAUA,AADkDjN,AAnBrD,IAAI,CAmBwD8L,cAAc,CAACC,EAAgB5L,EAAQ6L,WAAW,CAAE7L,EAAQ8L,WAAW,EACrH,IACzB,EAAG9L,EAAQ+M,KAAK,EAChBH,EAAYlN,GAAG,CAACA,GAAKe,IAAI,CAAC,CACtB2E,EAAG,EACHC,EAAG,EACH/E,KAAMhB,EAAM0N,IAAI,AACpB,GACA,IAAMC,EAAOL,EAAYM,OAAO,CAAC,CAAA,EACjC5N,CAAAA,EAAMkK,UAAU,CAAG,CACfb,OAAQsE,EAAKtE,MAAM,CACnBD,MAAOuE,EAAKvE,KAAK,AACrB,CACJ,CAEAF,EAAQrB,EAAgCiF,EAAMe,GAAG,CAAEd,EAAMc,GAAG,CAAExK,GAC9D,IAAM+H,EAAS7C,EAA0BhI,AAnC1B,IAAI,CAmC6BuN,OAAO,CAACpN,EAAQ0K,MAAM,CAAC,CAAE,CACrElC,MAAOA,CACX,GAEA,IAAK,IAAMlJ,KAASqD,EAAQ,CACxB,IAAMiJ,EAAiB,EAAIa,EAAYnN,EAAMqC,MAAM,CAA8FjC,EAAM6G,EAAuB,CAC1KuG,SAAUA,AADkDjN,AAxCrD,IAAI,CAwCwD8L,cAAc,CAACC,EAAgB5L,EAAQ6L,WAAW,CAAE7L,EAAQ8L,WAAW,EACrH,IACzB,EAAG9L,EAAQ+M,KAAK,EAAGM,EAAYd,EAAkBjN,EAAO,CACpDgK,KAAM3G,EACN6F,MAAOA,EACPiC,OAAQA,EACRb,SAAUA,CACd,GAAInJ,EAAO8F,EAAuB1G,AA/CvB,IAAI,CA+C0B2L,YAAY,CAAClM,EAAQA,EAAMgO,QAAQ,EAAI,UAAY,CACxFC,MAAO,SACP,qBAAsB,SACtB,oBAAqB,SACrBnI,EAAGiI,EAAUjI,CAAC,CACdC,EAAGgI,EAAUhI,CAAC,CACd/E,KAAMhB,EAAM0N,IAAI,CAChBpD,SAAUnD,EAAyB4G,EAAUzD,QAAQ,EACjDyD,EAAUzD,QAAQ,CAClB,KAAK,CACb,GAAIlH,EAAU2E,EAA2BgG,EAAUjI,CAAC,CAAEiI,EAAUhI,CAAC,CAAE/F,EAAMkK,UAAU,CAACd,KAAK,CAAEpJ,EAAMkK,UAAU,CAACb,MAAM,CAAE0E,EAAUzD,QAAQ,EAAGf,EAAY5B,EAA0CvE,GAC3LkI,EAAQ7C,EAAoCzI,EAAO,CACnDuJ,UAAWA,EACXnG,QAASA,EACT8F,MAAOA,EACPiC,OAAQA,EACRC,OAAQA,EACRd,SAAUyD,EAAUzD,QAAQ,AAChC,GAAI/I,CAEA,EAAC+J,GAAS0B,IAEV9D,EAAQzB,EAAmCyB,EAAOK,GAElD+B,EAAQ7C,EAAoCzI,EAAO,CAC/CuJ,UAAWA,EACXnG,QAASA,EACT8F,MAAOA,EACPiC,OAAQA,EACRC,OAAQA,EACRd,SAAUyD,EAAUzD,QAAQ,AAChC,IAIAlD,EAAyBkE,IACzBnK,EAAK2E,CAAC,CAAG,AAAC3E,CAAAA,EAAK2E,CAAC,EAAI,CAAA,EAAKwF,EAAMxF,CAAC,CAChC3E,EAAK4E,CAAC,CAAG,AAAC5E,CAAAA,EAAK4E,CAAC,EAAI,CAAA,EAAKuF,EAAMvF,CAAC,CAChCwD,EAAUxG,IAAI,EAAIuI,EAAMxF,CAAC,CACzByD,EAAUvG,KAAK,EAAIsI,EAAMxF,CAAC,CAC1ByD,EAAUtG,GAAG,EAAIqI,EAAMvF,CAAC,CACxBwD,EAAUrG,MAAM,EAAIoI,EAAMvF,CAAC,CAC3BmD,EAAQD,EAAsCC,EAAOK,GACrD4B,EAAOlH,IAAI,CAACjE,GACZA,EAAMkO,MAAM,CAAG,CAAA,EACflO,EAAMmO,QAAQ,CAAG,CAAA,GAGjBnO,EAAMkO,MAAM,CAAG,CAAA,EAEf5N,IAEAiB,EAAU,CACNuE,EAAG3E,EAAK2E,CAAC,CACTC,EAAG5E,EAAK4E,CAAC,AACb,EAEKtF,GAMD,OAAOU,EAAK2E,CAAC,CACb,OAAO3E,EAAK4E,CAAC,GANb5E,EAAK2E,CAAC,CAAG,EACT3E,EAAK4E,CAAC,CAAG,IAQjBjG,EAA0BC,IAAI,CAACC,EAAO,CAClCE,kBAAmBqB,EACnBX,QAASO,EACTf,IAAKA,EACLkB,MAAOA,EACPjB,SAAUA,EACVe,UAAW,KAAK,EAChBL,UAAW,MACf,EACJ,CAEAuM,EAAcA,EAAY7L,OAAO,GAEjClB,AA/He,IAAI,CA+HZqM,YAAY,CAAGvE,EAAyByE,EAAMe,GAAG,CAAEd,EAAMc,GAAG,CAAE3E,GACrE3I,AAhIe,IAAI,CAgIZ2I,KAAK,CAAGA,EACf3I,AAjIe,IAAI,CAiIZe,KAAK,CAACH,IAAI,CAAC,CACd0L,OAAQtM,AAlIG,IAAI,CAkIAqM,YAAY,CAC3BW,OAAQhN,AAnIG,IAAI,CAmIAqM,YAAY,AAC/B,EACJ,CACAwB,SAAU,CAEN,OAAQhH,EADO,IAAI,GAEf7G,AAAmB,CAAA,IAAnBA,AAFW,IAAI,CAER8N,OAAO,EACdnH,EAAQ3G,AAHG,IAAI,CAGA8C,MAAM,GACrB9C,AAJW,IAAI,CAIR8C,MAAM,CAAC1B,MAAM,CAAG,CAC/B,CACA2M,WAAWZ,CAAI,CAAE,CACb,GAAqB,CAAElN,MAAAA,CAAK,CAAEc,MAAAA,CAAK,CAAEqL,QAAAA,CAAO,CAAE,CAAG,IAAI,CAAE,CAAE4B,UAAAA,EAAY,CAAC,CAAEC,UAAAA,EAAY,CAAC,CAAEC,SAAAA,CAAQ,CAAE,CAAGjO,EAEpGsM,EAAQvM,AAFO,IAAI,AAEL,CAAEkO,EAAW,QAAU,QAAS,CAAE1B,EAAQxM,AAFzC,IAAI,AAE2C,CAAEkO,EAAW,QAAU,QAAS,CAAErF,EAAQ0D,EAAQA,EAAMe,GAAG,CAAGrN,EAAMkO,SAAS,CAAErF,EAAS0D,EAAQA,EAAMc,GAAG,CAAGrN,EAAMmO,UAAU,CAAE7I,EAAIgH,EAAQA,EAAM/J,IAAI,CAAGvC,EAAMoO,QAAQ,CAAE7I,EAAIgH,EAAQA,EAAM9J,GAAG,CAAGzC,EAAMqO,OAAO,CAAE3F,EAAQ3I,AAFrQ,IAAI,CAEwQ2I,KAAK,CAC5RnG,EAAO,EAAGE,EAAM,EAAG6L,EAAahJ,EAAIsD,EAAQ,EAAG2F,EAAahJ,EAAIsD,EAAS,EAAG2F,EAAWF,EAAYG,EAAUF,EAAYG,EAAQ3O,AAHtH,IAAI,CAGyHqM,YAAY,EAAI,EAAGuC,EAAe,EAAGC,EAAc,EAU/L,GATIlG,IACAiG,EACI1K,AAAwD,EAAxDA,KAAKE,GAAG,CAACF,KAAK6E,GAAG,CAACJ,EAAMjG,GAAG,EAAGwB,KAAK6E,GAAG,CAACJ,EAAMhG,MAAM,GACvDkM,EACI3K,AAAwD,EAAxDA,KAAKE,GAAG,CAACF,KAAK6E,GAAG,CAACJ,EAAMnG,IAAI,EAAG0B,KAAK6E,GAAG,CAACJ,EAAMlG,KAAK,IAEvDyL,GACA,CAAA,CAACW,EAAaD,EAAa,CAAG,CAACA,EAAcC,EAAY,AAAD,EAExD9N,GAASqL,EAAS,CA0BlB,IAA2G0C,EAAavJ,EAAIwJ,AAA3G7K,KAAKE,GAAG,CAACyK,EAD1BF,CAAAA,EAAQzK,KAAKE,GAAG,CAACgI,EAAQuC,KAAK,CAAE3O,AAtCrB,IAAI,CAsCwBqM,YAAY,EAAI,EAAC,EACTxD,GAAwF,EAAGmG,EAAaxJ,EAAIyJ,AAAxF/K,KAAKE,GAAG,CAACwK,EAAeD,EAAO7F,GAAqE,EAAGoG,EAAYP,EAAS5N,CAAAA,EAAMuL,MAAM,EAAI,CAAA,EAC/M9J,EAAO0M,EAAa,CAAA,AAAClB,CAAAA,EAAYa,CAAU,EAAK,EAC5CzC,EAAQ+C,KAAK,CAAGnB,EAAYnF,EAAQ,CAAA,EACxCnG,EAAMwM,EAAa,CAAA,AAACjB,CAAAA,EAAYW,CAAW,EAAK,EAC5CxC,EAAQgD,KAAK,CAAGnB,EAAYnF,EAAS,CAAA,EAC5B,WAATqE,IACAf,EAAQ7G,CAAC,CAAGrB,KAAKE,GAAG,CAAC,EAAGF,KAAKD,GAAG,CAAC,EAAImI,EAAQvD,KAAK,CAAEuD,EAAQ7G,CAAC,CAAI6G,EAAQiD,IAAI,CAAGjD,EAAQuC,KAAK,GAC7FnM,GAAQ4J,EAAQiD,IAAI,CAAGrB,EACvB5B,EAAQiD,IAAI,CAAG,EACfjD,EAAQ5G,CAAC,CAAGtB,KAAKE,GAAG,CAAC,EAAGF,KAAKD,GAAG,CAAC,EAAImI,EAAQtD,MAAM,CAAEsD,EAAQ5G,CAAC,CAAI4G,EAAQkD,IAAI,CAAGlD,EAAQuC,KAAK,GAC9FjM,GAAO0J,EAAQkD,IAAI,CAAGrB,EACtB7B,EAAQkD,IAAI,CAAG,GAEf1I,EAAyB7F,EAAMwN,UAAU,GAAK3H,EAAyB7F,EAAMyN,UAAU,IACvFC,EAAW1N,EAAMwN,UAAU,CAC3BG,EAAU3N,EAAMyN,UAAU,EAE9BD,EAAaE,EAAWjM,EAIpBgM,AAHJA,CAAAA,EAAaE,EAAUhM,CAAE,EAGRsM,EACbR,EAAaQ,EAERR,EAAa,EAAIhJ,EAAIsD,EAASkG,GACnCR,CAAAA,EAAa,EAAIhJ,EAAIsD,EAASkG,CAAS,EAGvCT,EAAaO,EACbP,EAAaO,EAERP,EAAa,EAAIhJ,EAAIsD,EAAQiG,GAClCP,CAAAA,EAAa,EAAIhJ,EAAIsD,EAAQiG,CAAS,CAE9C,CACA,MAAO,CACHP,WAAYA,EACZC,WAAYA,EACZlC,OAAQqC,EACR3B,OAAQ2B,CACZ,CACJ,CACJ,CAMAjD,EAAgB6D,cAAc,CAAGzI,EAAsBL,EAAa8I,cAAc,CAtqClD,CAW5B9C,wBAAyB,CAAA,EACzB1M,UAAW,CAEPyP,SAAU,GACd,EACAC,YAAa,EAIbC,KAAM,CAAA,EACNC,aAAc,CAAA,EACdC,cAAeC,IAKf5D,YAAa,EAObD,YAAa,GAWbU,kBAAmB,SAOnB3C,SAAU,CAINK,KAAM,EAMNF,aAAc,EAIdG,GAAI,EACR,EACAyF,aAAc,CAAA,EAUdjF,OAAQ,cAORqC,MAAO,CAEH6C,WAAY,aAEZC,WAAY,MAEZC,WAAY,QAChB,EACAC,QAAS,CACLC,cAAe,CAAA,EACfC,YAAa,sFACjB,CACJ,GAskCA1J,EAAuBgF,EAAgB1M,SAAS,CAAE,CAC9CgC,QAASwF,EACT6J,iBAAkB7J,EAClB8J,mBAAoB9J,EACpB+J,YAAa,CAAA,EACb/O,WAttC2DI,EAutC3D4O,QAAShK,EAITkG,kBAAmB,CACfzC,OAAQ,SAAUxK,CAAK,CAAEU,CAAO,EAC5B,IAAMwI,EAAQxI,EAAQwI,KAAK,CAAE8H,EAAItQ,EAAQ4J,QAAQ,CACjD,MAAO,CACHxE,EAAGmC,EAAkCiB,EAAME,KAAK,EAAKF,EAAME,KAAK,CAAG,EACnErD,EAAGkC,EAAkCiB,EAAMG,MAAM,EAAKH,EAAMG,MAAM,CAAG,EACrEiB,SAAUnC,EAA4B6I,EAAEvG,YAAY,CAAEzK,EAAM0K,KAAK,CAAEsG,EAAErG,IAAI,CAAEqG,EAAEpG,EAAE,CACnF,CACJ,EACAqG,OAAQ,SAAUjR,CAAK,CAAEU,CAAO,EAC5B,IAAMsQ,EAAItQ,EAAQ4J,QAAQ,CAC1B,MAAO,CACHxE,EAAG,EACHC,EAAG,EACHuE,SAAUnC,EAA4B6I,EAAEvG,YAAY,CAAEzK,EAAM0K,KAAK,CAAEsG,EAAErG,IAAI,CAAEqG,EAAEpG,EAAE,CACnF,CACJ,CACJ,EACAsG,cAAe,CAAC,SAAS,CAIzBpD,QAAS,CACL,YAAevG,EACf,YAAeqB,EACf,OAAUG,CACd,EACAoI,MAAO,CACH3J,mBAAoBC,EACpBS,YAAaC,EACbvD,oBAAqB8D,EACrBrC,iBAAkBwC,EAClBjC,gBAAiBkC,CACrB,CACJ,GACAjH,IAA0IuP,kBAAkB,CAAC,YAAanF,GAa7I,IAAMtM,EAAkBE,IAG3C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}