{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/color-axis\n * @requires highcharts\n *\n * ColorAxis module\n *\n * (c) 2012-2025 Pawel <PERSON>ek\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Axis\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"LegendSymbol\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"SVGElement\"], root[\"_Highcharts\"][\"SVGRenderer\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/heatmap\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"Axis\"],amd1[\"Color\"],amd1[\"LegendSymbol\"],amd1[\"SeriesRegistry\"],amd1[\"SVGElement\"],amd1[\"SVGRenderer\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/heatmap\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Axis\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"LegendSymbol\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"SVGElement\"], root[\"_Highcharts\"][\"SVGRenderer\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Axis\"], root[\"Highcharts\"][\"Color\"], root[\"Highcharts\"][\"LegendSymbol\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"SVGElement\"], root[\"Highcharts\"][\"SVGRenderer\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__532__, __WEBPACK_EXTERNAL_MODULE__620__, __WEBPACK_EXTERNAL_MODULE__500__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__28__, __WEBPACK_EXTERNAL_MODULE__540__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 28:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__28__;\n\n/***/ }),\n\n/***/ 500:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__500__;\n\n/***/ }),\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 532:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__532__;\n\n/***/ }),\n\n/***/ 540:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__540__;\n\n/***/ }),\n\n/***/ 620:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__620__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ heatmap_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Axis\"],\"commonjs\":[\"highcharts\",\"Axis\"],\"commonjs2\":[\"highcharts\",\"Axis\"],\"root\":[\"Highcharts\",\"Axis\"]}\nvar highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_ = __webpack_require__(532);\nvar highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default = /*#__PURE__*/__webpack_require__.n(highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Color\"],\"commonjs\":[\"highcharts\",\"Color\"],\"commonjs2\":[\"highcharts\",\"Color\"],\"root\":[\"Highcharts\",\"Color\"]}\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_ = __webpack_require__(620);\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default = /*#__PURE__*/__webpack_require__.n(highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_);\n;// ./code/es-modules/Core/Axis/Color/ColorAxisComposition.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { parse: color } = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default());\n\nconst { addEvent, extend, merge, pick, splat } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\nvar ColorAxisComposition;\n(function (ColorAxisComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Variables\n     *\n     * */\n    let ColorAxisConstructor;\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    function compose(ColorAxisClass, ChartClass, FxClass, LegendClass, SeriesClass) {\n        const chartProto = ChartClass.prototype, fxProto = FxClass.prototype, seriesProto = SeriesClass.prototype;\n        if (!chartProto.collectionsWithUpdate.includes('colorAxis')) {\n            ColorAxisConstructor = ColorAxisClass;\n            chartProto.collectionsWithUpdate.push('colorAxis');\n            chartProto.collectionsWithInit.colorAxis = [\n                chartProto.addColorAxis\n            ];\n            addEvent(ChartClass, 'afterCreateAxes', onChartAfterCreateAxes);\n            wrapChartCreateAxis(ChartClass);\n            fxProto.fillSetter = wrapFxFillSetter;\n            fxProto.strokeSetter = wrapFxStrokeSetter;\n            addEvent(LegendClass, 'afterGetAllItems', onLegendAfterGetAllItems);\n            addEvent(LegendClass, 'afterColorizeItem', onLegendAfterColorizeItem);\n            addEvent(LegendClass, 'afterUpdate', onLegendAfterUpdate);\n            extend(seriesProto, {\n                optionalAxis: 'colorAxis',\n                translateColors: seriesTranslateColors\n            });\n            extend(seriesProto.pointClass.prototype, {\n                setVisible: pointSetVisible\n            });\n            addEvent(SeriesClass, 'afterTranslate', onSeriesAfterTranslate, { order: 1 });\n            addEvent(SeriesClass, 'bindAxes', onSeriesBindAxes);\n        }\n    }\n    ColorAxisComposition.compose = compose;\n    /**\n     * Extend the chart createAxes method to also make the color axis.\n     * @private\n     */\n    function onChartAfterCreateAxes() {\n        const { userOptions } = this;\n        this.colorAxis = [];\n        // If a `colorAxis` config is present in the user options (not in a\n        // theme), instanciate it.\n        if (userOptions.colorAxis) {\n            userOptions.colorAxis = splat(userOptions.colorAxis);\n            userOptions.colorAxis.map((axisOptions) => (new ColorAxisConstructor(this, axisOptions)));\n        }\n    }\n    /**\n     * Add the color axis. This also removes the axis' own series to prevent\n     * them from showing up individually.\n     * @private\n     */\n    function onLegendAfterGetAllItems(e) {\n        const colorAxes = this.chart.colorAxis || [], destroyItem = (item) => {\n            const i = e.allItems.indexOf(item);\n            if (i !== -1) {\n                // #15436\n                this.destroyItem(e.allItems[i]);\n                e.allItems.splice(i, 1);\n            }\n        };\n        let colorAxisItems = [], options, i;\n        colorAxes.forEach(function (colorAxis) {\n            options = colorAxis.options;\n            if (options?.showInLegend) {\n                // Data classes\n                if (options.dataClasses && options.visible) {\n                    colorAxisItems = colorAxisItems.concat(colorAxis.getDataClassLegendSymbols());\n                    // Gradient legend\n                }\n                else if (options.visible) {\n                    // Add this axis on top\n                    colorAxisItems.push(colorAxis);\n                }\n                // If dataClasses are defined or showInLegend option is not set\n                // to true, do not add color axis' series to legend.\n                colorAxis.series.forEach(function (series) {\n                    if (!series.options.showInLegend || options.dataClasses) {\n                        if (series.options.legendType === 'point') {\n                            series.points.forEach(function (point) {\n                                destroyItem(point);\n                            });\n                        }\n                        else {\n                            destroyItem(series);\n                        }\n                    }\n                });\n            }\n        });\n        i = colorAxisItems.length;\n        while (i--) {\n            e.allItems.unshift(colorAxisItems[i]);\n        }\n    }\n    /**\n     * @private\n     */\n    function onLegendAfterColorizeItem(e) {\n        if (e.visible && e.item.legendColor) {\n            e.item.legendItem.symbol.attr({\n                fill: e.item.legendColor\n            });\n        }\n    }\n    /**\n     * Updates in the legend need to be reflected in the color axis. (#6888)\n     * @private\n     */\n    function onLegendAfterUpdate(e) {\n        this.chart.colorAxis?.forEach((colorAxis) => {\n            colorAxis.update({}, e.redraw);\n        });\n    }\n    /**\n     * Calculate and set colors for points.\n     * @private\n     */\n    function onSeriesAfterTranslate() {\n        if (this.chart.colorAxis?.length ||\n            this.colorAttribs) {\n            this.translateColors();\n        }\n    }\n    /**\n     * Add colorAxis to series axisTypes.\n     * @private\n     */\n    function onSeriesBindAxes() {\n        const axisTypes = this.axisTypes;\n        if (!axisTypes) {\n            this.axisTypes = ['colorAxis'];\n        }\n        else if (axisTypes.indexOf('colorAxis') === -1) {\n            axisTypes.push('colorAxis');\n        }\n    }\n    /**\n     * Set the visibility of a single point\n     * @private\n     * @function Highcharts.colorPointMixin.setVisible\n     * @param {boolean} visible\n     */\n    function pointSetVisible(vis) {\n        const point = this, method = vis ? 'show' : 'hide';\n        point.visible = point.options.visible = Boolean(vis);\n        // Show and hide associated elements\n        ['graphic', 'dataLabel'].forEach(function (key) {\n            if (point[key]) {\n                point[key][method]();\n            }\n        });\n        this.series.buildKDTree(); // Rebuild kdtree #13195\n    }\n    ColorAxisComposition.pointSetVisible = pointSetVisible;\n    /**\n     * In choropleth maps, the color is a result of the value, so this needs\n     * translation too\n     * @private\n     * @function Highcharts.colorSeriesMixin.translateColors\n     */\n    function seriesTranslateColors() {\n        const series = this, points = this.getPointsCollection(), // #17945\n        nullColor = this.options.nullColor, colorAxis = this.colorAxis, colorKey = this.colorKey;\n        points.forEach((point) => {\n            const value = point.getNestedProperty(colorKey), color = point.options.color || (point.isNull || point.value === null ?\n                nullColor :\n                (colorAxis && typeof value !== 'undefined') ?\n                    colorAxis.toColor(value, point) :\n                    point.color || series.color);\n            if (color && point.color !== color) {\n                point.color = color;\n                if (series.options.legendType === 'point' &&\n                    point.legendItem &&\n                    point.legendItem.label) {\n                    series.chart.legend.colorizeItem(point, point.visible);\n                }\n            }\n        });\n    }\n    /**\n     * @private\n     */\n    function wrapChartCreateAxis(ChartClass) {\n        const superCreateAxis = ChartClass.prototype.createAxis;\n        ChartClass.prototype.createAxis = function (type, options) {\n            const chart = this;\n            if (type !== 'colorAxis') {\n                return superCreateAxis.apply(chart, arguments);\n            }\n            const axis = new ColorAxisConstructor(chart, merge(options.axis, {\n                index: chart[type].length,\n                isX: false\n            }));\n            chart.isDirtyLegend = true;\n            // Clear before 'bindAxes' (#11924)\n            chart.axes.forEach((axis) => {\n                axis.series = [];\n            });\n            chart.series.forEach((series) => {\n                series.bindAxes();\n                series.isDirtyData = true;\n            });\n            if (pick(options.redraw, true)) {\n                chart.redraw(options.animation);\n            }\n            return axis;\n        };\n    }\n    /**\n     * Handle animation of the color attributes directly.\n     * @private\n     */\n    function wrapFxFillSetter() {\n        this.elem.attr('fill', color(this.start).tweenTo(color(this.end), this.pos), void 0, true);\n    }\n    /**\n     * Handle animation of the color attributes directly.\n     * @private\n     */\n    function wrapFxStrokeSetter() {\n        this.elem.attr('stroke', color(this.start).tweenTo(color(this.end), this.pos), void 0, true);\n    }\n})(ColorAxisComposition || (ColorAxisComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Color_ColorAxisComposition = (ColorAxisComposition);\n\n;// ./code/es-modules/Core/Axis/Color/ColorAxisDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A color axis for series. Visually, the color\n * axis will appear as a gradient or as separate items inside the\n * legend, depending on whether the axis is scalar or based on data\n * classes.\n *\n * For supported color formats, see the\n * [docs article about colors](https://www.highcharts.com/docs/chart-design-and-style/colors).\n *\n * A scalar color axis is represented by a gradient. The colors either\n * range between the [minColor](#colorAxis.minColor) and the\n * [maxColor](#colorAxis.maxColor), or for more fine grained control the\n * colors can be defined in [stops](#colorAxis.stops). Often times, the\n * color axis needs to be adjusted to get the right color spread for the\n * data. In addition to stops, consider using a logarithmic\n * [axis type](#colorAxis.type), or setting [min](#colorAxis.min) and\n * [max](#colorAxis.max) to avoid the colors being determined by\n * outliers.\n *\n * When [dataClasses](#colorAxis.dataClasses) are used, the ranges are\n * subdivided into separate classes like categories based on their\n * values. This can be used for ranges between two values, but also for\n * a true category. However, when your data is categorized, it may be as\n * convenient to add each category to a separate series.\n *\n * Color axis does not work with: `sankey`, `sunburst`, `dependencywheel`,\n * `networkgraph`, `wordcloud`, `venn`, `gauge` and `solidgauge` series\n * types.\n *\n * Since v7.2.0 `colorAxis` can also be an array of options objects.\n *\n * See [the Axis object](/class-reference/Highcharts.Axis) for\n * programmatic access to the axis.\n *\n * @sample       {highcharts} highcharts/coloraxis/custom-color-key\n *               Column chart with color axis\n * @sample       {highcharts} highcharts/coloraxis/horizontal-layout\n *               Horizontal layout\n * @sample       {highmaps} maps/coloraxis/dataclasscolor\n *               With data classes\n * @sample       {highmaps} maps/coloraxis/mincolor-maxcolor\n *               Min color and max color\n *\n * @extends      xAxis\n * @excluding    alignTicks, allowDecimals, alternateGridColor, breaks,\n *               categories, crosshair, dateTimeLabelFormats, left,\n *               lineWidth, linkedTo, maxZoom, minRange, minTickInterval,\n *               offset, opposite, pane, plotBands, plotLines,\n *               reversedStacks, scrollbar, showEmpty, title, top,\n *               zoomEnabled\n * @product      highcharts highstock highmaps\n * @type         {*|Array<*>}\n * @optionparent colorAxis\n */\nconst colorAxisDefaults = {\n    /**\n     * Whether to allow decimals on the color axis.\n     * @type      {boolean}\n     * @default   true\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.allowDecimals\n     */\n    /**\n     * Determines how to set each data class' color if no individual\n     * color is set. The default value, `tween`, computes intermediate\n     * colors between `minColor` and `maxColor`. The other possible\n     * value, `category`, pulls colors from the global or chart specific\n     * [colors](#colors) array.\n     *\n     * @sample {highmaps} maps/coloraxis/dataclasscolor/\n     *         Category colors\n     *\n     * @type       {string}\n     * @default    tween\n     * @product    highcharts highstock highmaps\n     * @validvalue [\"tween\", \"category\"]\n     * @apioption  colorAxis.dataClassColor\n     */\n    /**\n     * An array of data classes or ranges for the choropleth map. If\n     * none given, the color axis is scalar and values are distributed\n     * as a gradient between the minimum and maximum colors.\n     *\n     * @sample {highmaps} maps/demo/data-class-ranges/\n     *         Multiple ranges\n     *\n     * @sample {highmaps} maps/demo/data-class-two-ranges/\n     *         Two ranges\n     *\n     * @type      {Array<*>}\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.dataClasses\n     */\n    /**\n     * The layout of the color axis. Can be `'horizontal'` or `'vertical'`.\n     * If none given, the color axis has the same layout as the legend.\n     *\n     * @sample highcharts/coloraxis/horizontal-layout/\n     *         Horizontal color axis layout with vertical legend\n     *\n     * @type      {string|undefined}\n     * @since     7.2.0\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.layout\n     */\n    /**\n     * The color of each data class. If not set, the color is pulled\n     * from the global or chart-specific [colors](#colors) array. In\n     * styled mode, this option is ignored. Instead, use colors defined\n     * in CSS.\n     *\n     * @sample {highmaps} maps/demo/data-class-two-ranges/\n     *         Explicit colors\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.dataClasses.color\n     */\n    /**\n     * The start of the value range that the data class represents,\n     * relating to the point value.\n     *\n     * The range of each `dataClass` is closed in both ends, but can be\n     * overridden by the next `dataClass`.\n     *\n     * @type      {number}\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.dataClasses.from\n     */\n    /**\n     * The name of the data class as it appears in the legend.\n     * If no name is given, it is automatically created based on the\n     * `from` and `to` values. For full programmatic control,\n     * [legend.labelFormatter](#legend.labelFormatter) can be used.\n     * In the formatter, `this.from` and `this.to` can be accessed.\n     *\n     * @sample {highmaps} maps/coloraxis/dataclasses-name/\n     *         Named data classes\n     *\n     * @sample {highmaps} maps/coloraxis/dataclasses-labelformatter/\n     *         Formatted data classes\n     *\n     * @type      {string}\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.dataClasses.name\n     */\n    /**\n     * The end of the value range that the data class represents,\n     * relating to the point value.\n     *\n     * The range of each `dataClass` is closed in both ends, but can be\n     * overridden by the next `dataClass`.\n     *\n     * @type      {number}\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.dataClasses.to\n     */\n    /** @ignore-option */\n    lineWidth: 0,\n    /**\n     * Padding of the min value relative to the length of the axis. A\n     * padding of 0.05 will make a 100px axis 5px longer.\n     *\n     * @product highcharts highstock highmaps\n     */\n    minPadding: 0,\n    /**\n     * The maximum value of the axis in terms of map point values. If\n     * `null`, the max value is automatically calculated. If the\n     * `endOnTick` option is true, the max value might be rounded up.\n     *\n     * @sample {highmaps} maps/coloraxis/gridlines/\n     *         Explicit min and max to reduce the effect of outliers\n     *\n     * @type      {number}\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.max\n     */\n    /**\n     * The minimum value of the axis in terms of map point values. If\n     * `null`, the min value is automatically calculated. If the\n     * `startOnTick` option is true, the min value might be rounded\n     * down.\n     *\n     * @sample {highmaps} maps/coloraxis/gridlines/\n     *         Explicit min and max to reduce the effect of outliers\n     *\n     * @type      {number}\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.min\n     */\n    /**\n     * Padding of the max value relative to the length of the axis. A\n     * padding of 0.05 will make a 100px axis 5px longer.\n     *\n     * @product highcharts highstock highmaps\n     */\n    maxPadding: 0,\n    /**\n     * Color of the grid lines extending from the axis across the\n     * gradient.\n     *\n     * @sample {highmaps} maps/coloraxis/gridlines/\n     *         Grid lines demonstrated\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @product   highcharts highstock highmaps\n     */\n    gridLineColor: \"#ffffff\" /* Palette.backgroundColor */,\n    /**\n     * The width of the grid lines extending from the axis across the\n     * gradient of a scalar color axis.\n     *\n     * @sample {highmaps} maps/coloraxis/gridlines/\n     *         Grid lines demonstrated\n     *\n     * @product highcharts highstock highmaps\n     */\n    gridLineWidth: 1,\n    /**\n     * The interval of the tick marks in axis units. When `null`, the\n     * tick interval is computed to approximately follow the\n     * `tickPixelInterval`.\n     *\n     * @type      {number}\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.tickInterval\n     */\n    /**\n     * If [tickInterval](#colorAxis.tickInterval) is `null` this option\n     * sets the approximate pixel interval of the tick marks.\n     *\n     * @product highcharts highstock highmaps\n     */\n    tickPixelInterval: 72,\n    /**\n     * Whether to force the axis to start on a tick. Use this option\n     * with the `maxPadding` option to control the axis start.\n     *\n     * @product highcharts highstock highmaps\n     */\n    startOnTick: true,\n    /**\n     * Whether to force the axis to end on a tick. Use this option with\n     * the [maxPadding](#colorAxis.maxPadding) option to control the\n     * axis end.\n     *\n     * @product highcharts highstock highmaps\n     */\n    endOnTick: true,\n    /** @ignore */\n    offset: 0,\n    /**\n     * The triangular marker on a scalar color axis that points to the\n     * value of the hovered area. To disable the marker, set\n     * `marker: null`.\n     *\n     * @sample {highmaps} maps/coloraxis/marker/\n     *         Black marker\n     *\n     * @declare Highcharts.PointMarkerOptionsObject\n     * @product highcharts highstock highmaps\n     */\n    marker: {\n        /**\n         * Animation for the marker as it moves between values. Set to\n         * `false` to disable animation. Defaults to `{ duration: 50 }`.\n         *\n         * @type    {boolean|Partial<Highcharts.AnimationOptionsObject>}\n         * @product highcharts highstock highmaps\n         */\n        animation: {\n            /** @internal */\n            duration: 50\n        },\n        /** @internal */\n        width: 0.01,\n        /**\n         * The color of the marker.\n         *\n         * @type    {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         * @product highcharts highstock highmaps\n         */\n        color: \"#999999\" /* Palette.neutralColor40 */\n    },\n    /**\n     * The axis labels show the number for each tick.\n     *\n     * For more live examples on label options, see [xAxis.labels in the\n     * Highcharts API.](/highcharts#xAxis.labels)\n     *\n     * @extends xAxis.labels\n     * @product highcharts highstock highmaps\n     */\n    labels: {\n        distance: 8,\n        /**\n         * How to handle overflowing labels on horizontal color axis. If set\n         * to `\"allow\"`, it will not be aligned at all. By default it\n         * `\"justify\"` labels inside the chart area. If there is room to\n         * move it, it will be aligned to the edge, else it will be removed.\n         *\n         * @validvalue [\"allow\", \"justify\"]\n         * @product    highcharts highstock highmaps\n         */\n        overflow: 'justify',\n        rotation: 0\n    },\n    /**\n     * The color to represent the minimum of the color axis. Unless\n     * [dataClasses](#colorAxis.dataClasses) or\n     * [stops](#colorAxis.stops) are set, the gradient starts at this\n     * value.\n     *\n     * If dataClasses are set, the color is based on minColor and\n     * maxColor unless a color is set for each data class, or the\n     * [dataClassColor](#colorAxis.dataClassColor) is set.\n     *\n     * @sample {highmaps} maps/coloraxis/mincolor-maxcolor/\n     *         Min and max colors on scalar (gradient) axis\n     * @sample {highmaps} maps/coloraxis/mincolor-maxcolor-dataclasses/\n     *         On data classes\n     *\n     * @type    {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @product highcharts highstock highmaps\n     */\n    minColor: \"#e6e9ff\" /* Palette.highlightColor10 */,\n    /**\n     * The color to represent the maximum of the color axis. Unless\n     * [dataClasses](#colorAxis.dataClasses) or\n     * [stops](#colorAxis.stops) are set, the gradient ends at this\n     * value.\n     *\n     * If dataClasses are set, the color is based on minColor and\n     * maxColor unless a color is set for each data class, or the\n     * [dataClassColor](#colorAxis.dataClassColor) is set.\n     *\n     * @sample {highmaps} maps/coloraxis/mincolor-maxcolor/\n     *         Min and max colors on scalar (gradient) axis\n     * @sample {highmaps} maps/coloraxis/mincolor-maxcolor-dataclasses/\n     *         On data classes\n     *\n     * @type    {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @product highcharts highstock highmaps\n     */\n    maxColor: \"#0022ff\" /* Palette.highlightColor100 */,\n    /**\n     * Color stops for the gradient of a scalar color axis. Use this in\n     * cases where a linear gradient between a `minColor` and `maxColor`\n     * is not sufficient. The stops is an array of tuples, where the\n     * first item is a float between 0 and 1 assigning the relative\n     * position in the gradient, and the second item is the color.\n     *\n     * @sample highcharts/coloraxis/coloraxis-stops/\n     *         Color axis stops\n     * @sample highcharts/coloraxis/color-key-with-stops/\n     *         Color axis stops with custom colorKey\n     * @sample {highmaps} maps/demo/heatmap/\n     *         Heatmap with three color stops\n     *\n     * @type      {Array<Array<number,Highcharts.ColorString>>}\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.stops\n     */\n    /**\n     * The pixel length of the main tick marks on the color axis.\n     */\n    tickLength: 5,\n    /**\n     * The type of interpolation to use for the color axis. Can be\n     * `linear` or `logarithmic`.\n     *\n     * @sample highcharts/coloraxis/logarithmic-with-emulate-negative-values/\n     *         Logarithmic color axis with extension to emulate negative\n     *         values\n     *\n     * @type      {Highcharts.ColorAxisTypeValue}\n     * @default   linear\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.type\n     */\n    /**\n     * Whether to reverse the axis so that the highest number is closest\n     * to the origin. Defaults to `false` in a horizontal legend and\n     * `true` in a vertical legend, where the smallest value starts on\n     * top.\n     *\n     * @type      {boolean}\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.reversed\n     */\n    /**\n     * @product   highcharts highstock highmaps\n     * @excluding afterBreaks, pointBreak, pointInBreak\n     * @apioption colorAxis.events\n     */\n    /**\n     * Fires when the legend item belonging to the colorAxis is clicked.\n     * One parameter, `event`, is passed to the function.\n     *\n     * **Note:** This option is deprecated in favor of\n     * [legend.events.itemClick](#legend.events.itemClick).\n     *\n     * @deprecated 11.4.4\n     * @type       {Function}\n     * @product    highcharts highstock highmaps\n     * @apioption  colorAxis.events.legendItemClick\n     */\n    /**\n     * The width of the color axis. If it's a number, it is interpreted as\n     * pixels.\n     *\n     * If it's a percentage string, it is interpreted as percentages of the\n     * total plot width.\n     *\n     * @sample    highcharts/coloraxis/width-and-height\n     *            Percentage width and pixel height for color axis\n     *\n     * @type      {number|string}\n     * @since     11.3.0\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.width\n     */\n    /**\n     * The height of the color axis. If it's a number, it is interpreted as\n     * pixels.\n     *\n     * If it's a percentage string, it is interpreted as percentages of the\n     * total plot height.\n     *\n     * @sample    highcharts/coloraxis/width-and-height\n     *            Percentage width and pixel height for color axis\n     *\n     * @type      {number|string}\n     * @since     11.3.0\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.height\n     */\n    /**\n     * Whether to display the colorAxis in the legend.\n     *\n     * @sample highcharts/coloraxis/hidden-coloraxis-with-3d-chart/\n     *         Hidden color axis with 3d chart\n     *\n     * @see [heatmap.showInLegend](#series.heatmap.showInLegend)\n     *\n     * @since   4.2.7\n     * @product highcharts highstock highmaps\n     */\n    showInLegend: true\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const ColorAxisDefaults = (colorAxisDefaults);\n\n;// ./code/es-modules/Core/Axis/Color/ColorAxisLike.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { parse: ColorAxisLike_color } = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default());\n\nconst { merge: ColorAxisLike_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Namespace\n *\n * */\nvar ColorAxisLike;\n(function (ColorAxisLike) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Initialize defined data classes.\n     * @private\n     */\n    function initDataClasses(userOptions) {\n        const axis = this, chart = axis.chart, legendItem = axis.legendItem = axis.legendItem || {}, options = axis.options, userDataClasses = userOptions.dataClasses || [];\n        let dataClass, dataClasses, colorCount = chart.options.chart.colorCount, colorCounter = 0, colors;\n        axis.dataClasses = dataClasses = [];\n        legendItem.labels = [];\n        for (let i = 0, iEnd = userDataClasses.length; i < iEnd; ++i) {\n            dataClass = userDataClasses[i];\n            dataClass = ColorAxisLike_merge(dataClass);\n            dataClasses.push(dataClass);\n            if (!chart.styledMode && dataClass.color) {\n                continue;\n            }\n            if (options.dataClassColor === 'category') {\n                if (!chart.styledMode) {\n                    colors = chart.options.colors || [];\n                    colorCount = colors.length;\n                    dataClass.color = colors[colorCounter];\n                }\n                dataClass.colorIndex = colorCounter;\n                // Loop back to zero\n                colorCounter++;\n                if (colorCounter === colorCount) {\n                    colorCounter = 0;\n                }\n            }\n            else {\n                dataClass.color = ColorAxisLike_color(options.minColor).tweenTo(ColorAxisLike_color(options.maxColor), iEnd < 2 ? 0.5 : i / (iEnd - 1) // #3219\n                );\n            }\n        }\n    }\n    ColorAxisLike.initDataClasses = initDataClasses;\n    /**\n     * Create initial color stops.\n     * @private\n     */\n    function initStops() {\n        const axis = this, options = axis.options, stops = axis.stops = options.stops || [\n            [0, options.minColor || ''],\n            [1, options.maxColor || '']\n        ];\n        for (let i = 0, iEnd = stops.length; i < iEnd; ++i) {\n            stops[i].color = ColorAxisLike_color(stops[i][1]);\n        }\n    }\n    ColorAxisLike.initStops = initStops;\n    /**\n     * Normalize logarithmic values.\n     * @private\n     */\n    function normalizedValue(value) {\n        const axis = this, max = axis.max || 0, min = axis.min || 0;\n        if (axis.logarithmic) {\n            value = axis.logarithmic.log2lin(value);\n        }\n        return 1 - ((max - value) /\n            ((max - min) || 1));\n    }\n    ColorAxisLike.normalizedValue = normalizedValue;\n    /**\n     * Translate from a value to a color.\n     * @private\n     */\n    function toColor(value, point) {\n        const axis = this;\n        const dataClasses = axis.dataClasses;\n        const stops = axis.stops;\n        let pos, from, to, color, dataClass, i;\n        if (dataClasses) {\n            i = dataClasses.length;\n            while (i--) {\n                dataClass = dataClasses[i];\n                from = dataClass.from;\n                to = dataClass.to;\n                if ((typeof from === 'undefined' || value >= from) &&\n                    (typeof to === 'undefined' || value <= to)) {\n                    color = dataClass.color;\n                    if (point) {\n                        point.dataClass = i;\n                        point.colorIndex = dataClass.colorIndex;\n                    }\n                    break;\n                }\n            }\n        }\n        else {\n            pos = axis.normalizedValue(value);\n            i = stops.length;\n            while (i--) {\n                if (pos > stops[i][0]) {\n                    break;\n                }\n            }\n            from = stops[i] || stops[i + 1];\n            to = stops[i + 1] || from;\n            // The position within the gradient\n            pos = 1 - (to[0] - pos) / ((to[0] - from[0]) || 1);\n            color = from.color.tweenTo(to.color, pos);\n        }\n        return color;\n    }\n    ColorAxisLike.toColor = toColor;\n})(ColorAxisLike || (ColorAxisLike = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Color_ColorAxisLike = (ColorAxisLike);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"LegendSymbol\"],\"commonjs\":[\"highcharts\",\"LegendSymbol\"],\"commonjs2\":[\"highcharts\",\"LegendSymbol\"],\"root\":[\"Highcharts\",\"LegendSymbol\"]}\nvar highcharts_LegendSymbol_commonjs_highcharts_LegendSymbol_commonjs2_highcharts_LegendSymbol_root_Highcharts_LegendSymbol_ = __webpack_require__(500);\nvar highcharts_LegendSymbol_commonjs_highcharts_LegendSymbol_commonjs2_highcharts_LegendSymbol_root_Highcharts_LegendSymbol_default = /*#__PURE__*/__webpack_require__.n(highcharts_LegendSymbol_commonjs_highcharts_LegendSymbol_commonjs2_highcharts_LegendSymbol_root_Highcharts_LegendSymbol_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Core/Axis/Color/ColorAxis.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\n\nconst { defaultOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nconst { series: Series } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { defined, extend: ColorAxis_extend, fireEvent, isArray, isNumber, merge: ColorAxis_merge, pick: ColorAxis_pick, relativeLength } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\ndefaultOptions.colorAxis = ColorAxis_merge(defaultOptions.xAxis, ColorAxisDefaults);\n/* *\n *\n *  Class\n *\n * */\n/**\n * The ColorAxis object for inclusion in gradient legends.\n *\n * @class\n * @name Highcharts.ColorAxis\n * @augments Highcharts.Axis\n *\n * @param {Highcharts.Chart} chart\n * The related chart of the color axis.\n *\n * @param {Highcharts.ColorAxisOptions} userOptions\n * The color axis options for initialization.\n */\nclass ColorAxis extends (highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default()) {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static compose(ChartClass, FxClass, LegendClass, SeriesClass) {\n        Color_ColorAxisComposition.compose(ColorAxis, ChartClass, FxClass, LegendClass, SeriesClass);\n    }\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    /**\n     * @private\n     */\n    constructor(chart, userOptions) {\n        super(chart, userOptions);\n        this.coll = 'colorAxis';\n        this.visible = true;\n        this.init(chart, userOptions);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Initializes the color axis.\n     *\n     * @function Highcharts.ColorAxis#init\n     *\n     * @param {Highcharts.Chart} chart\n     * The related chart of the color axis.\n     *\n     * @param {Highcharts.ColorAxisOptions} userOptions\n     * The color axis options for initialization.\n     */\n    init(chart, userOptions) {\n        const axis = this;\n        const legend = chart.options.legend || {}, horiz = userOptions.layout ?\n            userOptions.layout !== 'vertical' :\n            legend.layout !== 'vertical';\n        axis.side = userOptions.side || horiz ? 2 : 1;\n        axis.reversed = userOptions.reversed || !horiz;\n        axis.opposite = !horiz;\n        super.init(chart, userOptions, 'colorAxis');\n        // `super.init` saves the extended user options, now replace it with the\n        // originals\n        this.userOptions = userOptions;\n        if (isArray(chart.userOptions.colorAxis)) {\n            chart.userOptions.colorAxis[this.index] = userOptions;\n        }\n        // Prepare data classes\n        if (userOptions.dataClasses) {\n            axis.initDataClasses(userOptions);\n        }\n        axis.initStops();\n        // Override original axis properties\n        axis.horiz = horiz;\n        axis.zoomEnabled = false;\n    }\n    /**\n     * Returns true if the series has points at all.\n     *\n     * @function Highcharts.ColorAxis#hasData\n     *\n     * @return {boolean}\n     * True, if the series has points, otherwise false.\n     */\n    hasData() {\n        return !!(this.tickPositions || []).length;\n    }\n    /**\n     * Override so that ticks are not added in data class axes (#6914)\n     * @private\n     */\n    setTickPositions() {\n        if (!this.dataClasses) {\n            return super.setTickPositions();\n        }\n    }\n    /**\n     * Extend the setOptions method to process extreme colors and color stops.\n     * @private\n     */\n    setOptions(userOptions) {\n        const options = ColorAxis_merge(defaultOptions.colorAxis, userOptions, \n        // Forced options\n        {\n            showEmpty: false,\n            title: null,\n            visible: this.chart.options.legend.enabled &&\n                userOptions.visible !== false\n        });\n        super.setOptions(options);\n        this.options.crosshair = this.options.marker;\n    }\n    /**\n     * @private\n     */\n    setAxisSize() {\n        const axis = this, chart = axis.chart, symbol = axis.legendItem?.symbol;\n        let { width, height } = axis.getSize();\n        if (symbol) {\n            this.left = +symbol.attr('x');\n            this.top = +symbol.attr('y');\n            this.width = width = +symbol.attr('width');\n            this.height = height = +symbol.attr('height');\n            this.right = chart.chartWidth - this.left - width;\n            this.bottom = chart.chartHeight - this.top - height;\n            this.pos = this.horiz ? this.left : this.top;\n        }\n        // Fake length for disabled legend to avoid tick issues\n        // and such (#5205)\n        this.len = (this.horiz ? width : height) ||\n            ColorAxis.defaultLegendLength;\n    }\n    /**\n     * Override the getOffset method to add the whole axis groups inside the\n     * legend.\n     * @private\n     */\n    getOffset() {\n        const axis = this;\n        const group = axis.legendItem?.group;\n        const sideOffset = axis.chart.axisOffset[axis.side];\n        if (group) {\n            // Hook for the getOffset method to add groups to this parent\n            // group\n            axis.axisParent = group;\n            // Call the base\n            super.getOffset();\n            const legend = this.chart.legend;\n            // Adds `maxLabelLength` needed for label padding corrections done\n            // by `render()` and `getMargins()` (#15551).\n            legend.allItems.forEach(function (item) {\n                if (item instanceof ColorAxis) {\n                    item.drawLegendSymbol(legend, item);\n                }\n            });\n            legend.render();\n            this.chart.getMargins(true);\n            // If not drilling down/up\n            if (!this.chart.series.some((series) => series.isDrilling)) {\n                axis.isDirty = true; // Flag to fire drawChartBox\n            }\n            // First time only\n            if (!axis.added) {\n                axis.added = true;\n                axis.labelLeft = 0;\n                axis.labelRight = axis.width;\n            }\n            // Reset it to avoid color axis reserving space\n            axis.chart.axisOffset[axis.side] = sideOffset;\n        }\n    }\n    /**\n     * Create the color gradient.\n     * @private\n     */\n    setLegendColor() {\n        const axis = this;\n        const horiz = axis.horiz;\n        const reversed = axis.reversed;\n        const one = reversed ? 1 : 0;\n        const zero = reversed ? 0 : 1;\n        const grad = horiz ? [one, 0, zero, 0] : [0, zero, 0, one]; // #3190\n        axis.legendColor = {\n            linearGradient: {\n                x1: grad[0],\n                y1: grad[1],\n                x2: grad[2],\n                y2: grad[3]\n            },\n            stops: axis.stops\n        };\n    }\n    /**\n     * The color axis appears inside the legend and has its own legend symbol.\n     * @private\n     */\n    drawLegendSymbol(legend, item) {\n        const axis = this, legendItem = item.legendItem || {}, padding = legend.padding, legendOptions = legend.options, labelOptions = axis.options.labels, itemDistance = ColorAxis_pick(legendOptions.itemDistance, 10), horiz = axis.horiz, { width, height } = axis.getSize(), labelPadding = ColorAxis_pick(\n        // @todo: This option is not documented, nor implemented when\n        // vertical\n        legendOptions.labelPadding, horiz ? 16 : 30);\n        this.setLegendColor();\n        // Create the gradient\n        if (!legendItem.symbol) {\n            legendItem.symbol = this.chart.renderer.symbol('roundedRect')\n                .attr({\n                r: legendOptions.symbolRadius ?? 3,\n                zIndex: 1\n            }).add(legendItem.group);\n        }\n        legendItem.symbol.attr({\n            x: 0,\n            y: (legend.baseline || 0) - 11,\n            width: width,\n            height: height\n        });\n        // Set how much space this legend item takes up\n        legendItem.labelWidth = (width +\n            padding +\n            (horiz ?\n                itemDistance :\n                ColorAxis_pick(labelOptions.x, labelOptions.distance) +\n                    (this.maxLabelLength || 0)));\n        legendItem.labelHeight = height + padding + (horiz ? labelPadding : 0);\n    }\n    /**\n     * Fool the legend.\n     * @private\n     */\n    setState(state) {\n        this.series.forEach(function (series) {\n            series.setState(state);\n        });\n    }\n    /**\n     * @private\n     */\n    setVisible() {\n    }\n    /**\n     * @private\n     */\n    getSeriesExtremes() {\n        const axis = this;\n        const series = axis.series;\n        let colorValArray, colorKey, calculatedExtremes, cSeries, i = series.length;\n        this.dataMin = Infinity;\n        this.dataMax = -Infinity;\n        while (i--) { // X, y, value, other\n            cSeries = series[i];\n            colorKey = cSeries.colorKey = ColorAxis_pick(cSeries.options.colorKey, cSeries.colorKey, cSeries.pointValKey, cSeries.zoneAxis, 'y');\n            calculatedExtremes = cSeries[colorKey + 'Min'] &&\n                cSeries[colorKey + 'Max'];\n            // Find the first column that has values\n            for (const key of [colorKey, 'value', 'y']) {\n                colorValArray = cSeries.getColumn(key);\n                if (colorValArray.length) {\n                    break;\n                }\n            }\n            // If color key extremes are already calculated, use them.\n            if (calculatedExtremes) {\n                cSeries.minColorValue = cSeries[colorKey + 'Min'];\n                cSeries.maxColorValue = cSeries[colorKey + 'Max'];\n            }\n            else {\n                const cExtremes = Series.prototype.getExtremes.call(cSeries, colorValArray);\n                cSeries.minColorValue = cExtremes.dataMin;\n                cSeries.maxColorValue = cExtremes.dataMax;\n            }\n            if (defined(cSeries.minColorValue) &&\n                defined(cSeries.maxColorValue)) {\n                this.dataMin =\n                    Math.min(this.dataMin, cSeries.minColorValue);\n                this.dataMax =\n                    Math.max(this.dataMax, cSeries.maxColorValue);\n            }\n            if (!calculatedExtremes) {\n                Series.prototype.applyExtremes.call(cSeries);\n            }\n        }\n    }\n    /**\n     * Internal function to draw a crosshair.\n     *\n     * @function Highcharts.ColorAxis#drawCrosshair\n     *\n     * @param {Highcharts.PointerEventObject} [e]\n     *        The event arguments from the modified pointer event, extended with\n     *        `chartX` and `chartY`\n     *\n     * @param {Highcharts.Point} [point]\n     *        The Point object if the crosshair snaps to points.\n     *\n     * @emits Highcharts.ColorAxis#event:afterDrawCrosshair\n     * @emits Highcharts.ColorAxis#event:drawCrosshair\n     */\n    drawCrosshair(e, point) {\n        const axis = this, legendItem = axis.legendItem || {}, plotX = point?.plotX, plotY = point?.plotY, axisPos = axis.pos, axisLen = axis.len;\n        let crossPos;\n        if (point) {\n            crossPos = axis.toPixels(point.getNestedProperty(point.series.colorKey));\n            if (crossPos < axisPos) {\n                crossPos = axisPos - 2;\n            }\n            else if (crossPos > axisPos + axisLen) {\n                crossPos = axisPos + axisLen + 2;\n            }\n            point.plotX = crossPos;\n            point.plotY = axis.len - crossPos;\n            super.drawCrosshair(e, point);\n            point.plotX = plotX;\n            point.plotY = plotY;\n            if (axis.cross &&\n                !axis.cross.addedToColorAxis &&\n                legendItem.group) {\n                axis.cross\n                    .addClass('highcharts-coloraxis-marker')\n                    .add(legendItem.group);\n                axis.cross.addedToColorAxis = true;\n                if (!axis.chart.styledMode &&\n                    typeof axis.crosshair === 'object') {\n                    axis.cross.attr({\n                        fill: axis.crosshair.color\n                    });\n                }\n            }\n        }\n    }\n    /**\n     * @private\n     */\n    getPlotLinePath(options) {\n        const axis = this, left = axis.left, pos = options.translatedValue, top = axis.top;\n        // Crosshairs only\n        return isNumber(pos) ? // `pos` can be 0 (#3969)\n            (axis.horiz ? [\n                ['M', pos - 4, top - 6],\n                ['L', pos + 4, top - 6],\n                ['L', pos, top],\n                ['Z']\n            ] : [\n                ['M', left, pos],\n                ['L', left - 6, pos + 6],\n                ['L', left - 6, pos - 6],\n                ['Z']\n            ]) :\n            super.getPlotLinePath(options);\n    }\n    /**\n     * Updates a color axis instance with a new set of options. The options are\n     * merged with the existing options, so only new or altered options need to\n     * be specified.\n     *\n     * @function Highcharts.ColorAxis#update\n     *\n     * @param {Highcharts.ColorAxisOptions} newOptions\n     * The new options that will be merged in with existing options on the color\n     * axis.\n     *\n     * @param {boolean} [redraw]\n     * Whether to redraw the chart after the color axis is altered. If doing\n     * more operations on the chart, it is a good idea to set redraw to `false`\n     * and call {@link Highcharts.Chart#redraw} after.\n     */\n    update(newOptions, redraw) {\n        const axis = this, chart = axis.chart, legend = chart.legend;\n        this.series.forEach((series) => {\n            // Needed for Axis.update when choropleth colors change\n            series.isDirtyData = true;\n        });\n        // When updating data classes, destroy old items and make sure new\n        // ones are created (#3207)\n        if (newOptions.dataClasses && legend.allItems || axis.dataClasses) {\n            axis.destroyItems();\n        }\n        super.update(newOptions, redraw);\n        if (axis.legendItem?.label) {\n            axis.setLegendColor();\n            legend.colorizeItem(this, true);\n        }\n    }\n    /**\n     * Destroy color axis legend items.\n     * @private\n     */\n    destroyItems() {\n        const axis = this, chart = axis.chart, legendItem = axis.legendItem || {};\n        if (legendItem.label) {\n            chart.legend.destroyItem(axis);\n        }\n        else if (legendItem.labels) {\n            for (const item of legendItem.labels) {\n                chart.legend.destroyItem(item);\n            }\n        }\n        chart.isDirtyLegend = true;\n    }\n    //   Removing the whole axis (#14283)\n    destroy() {\n        this.chart.isDirtyLegend = true;\n        this.destroyItems();\n        super.destroy(...[].slice.call(arguments));\n    }\n    /**\n     * Removes the color axis and the related legend item.\n     *\n     * @function Highcharts.ColorAxis#remove\n     *\n     * @param {boolean} [redraw=true]\n     *        Whether to redraw the chart following the remove.\n     */\n    remove(redraw) {\n        this.destroyItems();\n        super.remove(redraw);\n    }\n    /**\n     * Get the legend item symbols for data classes.\n     * @private\n     */\n    getDataClassLegendSymbols() {\n        const axis = this, chart = axis.chart, legendItems = (axis.legendItem &&\n            axis.legendItem.labels ||\n            []), legendOptions = chart.options.legend, valueDecimals = ColorAxis_pick(legendOptions.valueDecimals, -1), valueSuffix = ColorAxis_pick(legendOptions.valueSuffix, '');\n        const getPointsInDataClass = (i) => axis.series.reduce((points, s) => {\n            points.push(...s.points.filter((point) => point.dataClass === i));\n            return points;\n        }, []);\n        let name;\n        if (!legendItems.length) {\n            axis.dataClasses.forEach((dataClass, i) => {\n                const from = dataClass.from, to = dataClass.to, { numberFormatter } = chart;\n                let vis = true;\n                // Assemble the default name. This can be overridden\n                // by legend.options.labelFormatter\n                name = '';\n                if (typeof from === 'undefined') {\n                    name = '< ';\n                }\n                else if (typeof to === 'undefined') {\n                    name = '> ';\n                }\n                if (typeof from !== 'undefined') {\n                    name += numberFormatter(from, valueDecimals) + valueSuffix;\n                }\n                if (typeof from !== 'undefined' && typeof to !== 'undefined') {\n                    name += ' - ';\n                }\n                if (typeof to !== 'undefined') {\n                    name += numberFormatter(to, valueDecimals) + valueSuffix;\n                }\n                // Add a mock object to the legend items\n                legendItems.push(ColorAxis_extend({\n                    chart,\n                    name,\n                    options: {},\n                    drawLegendSymbol: (highcharts_LegendSymbol_commonjs_highcharts_LegendSymbol_commonjs2_highcharts_LegendSymbol_root_Highcharts_LegendSymbol_default()).rectangle,\n                    visible: true,\n                    isDataClass: true,\n                    // Override setState to set either normal or inactive\n                    // state to all points in this data class\n                    setState: (state) => {\n                        for (const point of getPointsInDataClass(i)) {\n                            point.setState(state);\n                        }\n                    },\n                    // Override setState to show or hide all points in this\n                    // data class\n                    setVisible: function () {\n                        this.visible = vis = axis.visible = !vis;\n                        const affectedSeries = [];\n                        for (const point of getPointsInDataClass(i)) {\n                            point.setVisible(vis);\n                            point.hiddenInDataClass = !vis; // #20441\n                            if (affectedSeries.indexOf(point.series) === -1) {\n                                affectedSeries.push(point.series);\n                            }\n                        }\n                        chart.legend.colorizeItem(this, vis);\n                        affectedSeries.forEach((series) => {\n                            fireEvent(series, 'afterDataClassLegendClick');\n                        });\n                    }\n                }, dataClass));\n            });\n        }\n        return legendItems;\n    }\n    /**\n     * Get size of color axis symbol.\n     * @private\n     */\n    getSize() {\n        const axis = this, { chart, horiz } = axis, { height: colorAxisHeight, width: colorAxisWidth } = axis.options, { legend: legendOptions } = chart.options, width = ColorAxis_pick(defined(colorAxisWidth) ?\n            relativeLength(colorAxisWidth, chart.chartWidth) : void 0, legendOptions?.symbolWidth, horiz ? ColorAxis.defaultLegendLength : 12), height = ColorAxis_pick(defined(colorAxisHeight) ?\n            relativeLength(colorAxisHeight, chart.chartHeight) : void 0, legendOptions?.symbolHeight, horiz ? 12 : ColorAxis.defaultLegendLength);\n        return {\n            width,\n            height\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nColorAxis.defaultLegendLength = 200;\n/**\n * @private\n */\nColorAxis.keepProps = [\n    'legendItem'\n];\nColorAxis_extend(ColorAxis.prototype, Color_ColorAxisLike);\n/* *\n *\n *  Registry\n *\n * */\n// Properties to preserve after destroy, for Axis.update (#5881, #6025).\nArray.prototype.push.apply((highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default()).keepProps, ColorAxis.keepProps);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Color_ColorAxis = (ColorAxis);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Color axis types\n *\n * @typedef {\"linear\"|\"logarithmic\"} Highcharts.ColorAxisTypeValue\n */\n''; // Detach doclet above\n\n;// ./code/es-modules/masters/modules/coloraxis.js\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nG.ColorAxis = G.ColorAxis || Color_ColorAxis;\nG.ColorAxis.compose(G.Chart, G.Fx, G.Legend, G.Series);\n/* harmony default export */ const coloraxis_src = ((/* unused pure expression or super */ null && (Highcharts)));\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SVGElement\"],\"commonjs\":[\"highcharts\",\"SVGElement\"],\"commonjs2\":[\"highcharts\",\"SVGElement\"],\"root\":[\"Highcharts\",\"SVGElement\"]}\nvar highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_ = __webpack_require__(28);\nvar highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default = /*#__PURE__*/__webpack_require__.n(highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_);\n;// ./code/es-modules/Series/ColorMapComposition.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { column: { prototype: columnProto } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\n\nconst { addEvent: ColorMapComposition_addEvent, defined: ColorMapComposition_defined } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\nvar ColorMapComposition;\n(function (ColorMapComposition) {\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    ColorMapComposition.pointMembers = {\n        dataLabelOnNull: true,\n        moveToTopOnHover: true,\n        isValid: pointIsValid\n    };\n    ColorMapComposition.seriesMembers = {\n        colorKey: 'value',\n        axisTypes: ['xAxis', 'yAxis', 'colorAxis'],\n        parallelArrays: ['x', 'y', 'value'],\n        pointArrayMap: ['value'],\n        trackerGroups: ['group', 'markerGroup', 'dataLabelsGroup'],\n        colorAttribs: seriesColorAttribs,\n        pointAttribs: columnProto.pointAttribs\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    function compose(SeriesClass) {\n        const PointClass = SeriesClass.prototype.pointClass;\n        ColorMapComposition_addEvent(PointClass, 'afterSetState', onPointAfterSetState);\n        return SeriesClass;\n    }\n    ColorMapComposition.compose = compose;\n    /**\n     * Move points to the top of the z-index order when hovered.\n     * @private\n     */\n    function onPointAfterSetState(e) {\n        const point = this, series = point.series, renderer = series.chart.renderer;\n        if (point.moveToTopOnHover && point.graphic) {\n            if (!series.stateMarkerGraphic) {\n                // Create a `use` element and add it to the end of the group,\n                // which would make it appear on top of the other elements. This\n                // deals with z-index without reordering DOM elements (#13049).\n                series.stateMarkerGraphic = new (highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default())(renderer, 'use')\n                    .css({\n                    pointerEvents: 'none'\n                })\n                    .add(point.graphic.parentGroup);\n            }\n            if (e?.state === 'hover') {\n                // Give the graphic DOM element the same id as the Point\n                // instance\n                point.graphic.attr({\n                    id: this.id\n                });\n                series.stateMarkerGraphic.attr({\n                    href: `${renderer.url}#${this.id}`,\n                    visibility: 'visible'\n                });\n            }\n            else {\n                series.stateMarkerGraphic.attr({\n                    href: ''\n                });\n            }\n        }\n    }\n    /**\n     * Color points have a value option that determines whether or not it is\n     * a null point\n     * @private\n     */\n    function pointIsValid() {\n        return (this.value !== null &&\n            this.value !== Infinity &&\n            this.value !== -Infinity &&\n            // Undefined is allowed, but NaN is not (#17279)\n            (this.value === void 0 || !isNaN(this.value)));\n    }\n    /**\n     * Get the color attributes to apply on the graphic\n     * @private\n     * @function Highcharts.colorMapSeriesMixin.colorAttribs\n     * @param {Highcharts.Point} point\n     * @return {Highcharts.SVGAttributes}\n     *         The SVG attributes\n     */\n    function seriesColorAttribs(point) {\n        const ret = {};\n        if (ColorMapComposition_defined(point.color) &&\n            (!point.state || point.state === 'normal') // #15746\n        ) {\n            ret[this.colorProp || 'fill'] = point.color;\n        }\n        return ret;\n    }\n})(ColorMapComposition || (ColorMapComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Series_ColorMapComposition = (ColorMapComposition);\n\n;// ./code/es-modules/Series/Heatmap/HeatmapPoint.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { scatter: { prototype: { pointClass: ScatterPoint } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { clamp, defined: HeatmapPoint_defined, extend: HeatmapPoint_extend, pick: HeatmapPoint_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass HeatmapPoint extends ScatterPoint {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /** @private */\n    applyOptions(options, x) {\n        // #17970, if point is null remove its color, because it may be updated\n        if (this.isNull || this.value === null) {\n            delete this.color;\n        }\n        super.applyOptions(options, x);\n        this.formatPrefix = this.isNull || this.value === null ?\n            'null' : 'point';\n        return this;\n    }\n    /** @private */\n    getCellAttributes() {\n        const point = this, series = point.series, seriesOptions = series.options, xPad = (seriesOptions.colsize || 1) / 2, yPad = (seriesOptions.rowsize || 1) / 2, xAxis = series.xAxis, yAxis = series.yAxis, markerOptions = point.options.marker || series.options.marker, pointPlacement = series.pointPlacementToXValue(), // #7860\n        pointPadding = HeatmapPoint_pick(point.pointPadding, seriesOptions.pointPadding, 0), cellAttr = {\n            x1: clamp(Math.round(xAxis.len -\n                xAxis.translate(point.x - xPad, false, true, false, true, -pointPlacement)), -xAxis.len, 2 * xAxis.len),\n            x2: clamp(Math.round(xAxis.len -\n                xAxis.translate(point.x + xPad, false, true, false, true, -pointPlacement)), -xAxis.len, 2 * xAxis.len),\n            y1: clamp(Math.round(yAxis.translate(point.y - yPad, false, true, false, true)), -yAxis.len, 2 * yAxis.len),\n            y2: clamp(Math.round(yAxis.translate(point.y + yPad, false, true, false, true)), -yAxis.len, 2 * yAxis.len)\n        };\n        const dimensions = [['width', 'x'], ['height', 'y']];\n        // Handle marker's fixed width, and height values including border\n        // and pointPadding while calculating cell attributes.\n        for (const dimension of dimensions) {\n            const prop = dimension[0], direction = dimension[1];\n            let start = direction + '1', end = direction + '2';\n            const side = Math.abs(cellAttr[start] - cellAttr[end]), borderWidth = markerOptions &&\n                markerOptions.lineWidth || 0, plotPos = Math.abs(cellAttr[start] + cellAttr[end]) / 2, widthOrHeight = markerOptions && markerOptions[prop];\n            if (HeatmapPoint_defined(widthOrHeight) && widthOrHeight < side) {\n                const halfCellSize = widthOrHeight / 2 + borderWidth / 2;\n                cellAttr[start] = plotPos - halfCellSize;\n                cellAttr[end] = plotPos + halfCellSize;\n            }\n            // Handle pointPadding\n            if (pointPadding) {\n                if ((direction === 'x' && xAxis.reversed) ||\n                    (direction === 'y' && !yAxis.reversed)) {\n                    start = end;\n                    end = direction + '1';\n                }\n                cellAttr[start] += pointPadding;\n                cellAttr[end] -= pointPadding;\n            }\n        }\n        return cellAttr;\n    }\n    /**\n     * @private\n     */\n    haloPath(size) {\n        if (!size) {\n            return [];\n        }\n        const { x = 0, y = 0, width = 0, height = 0 } = this.shapeArgs || {};\n        return [\n            ['M', x - size, y - size],\n            ['L', x - size, y + height + size],\n            ['L', x + width + size, y + height + size],\n            ['L', x + width + size, y - size],\n            ['Z']\n        ];\n    }\n    /**\n     * Color points have a value option that determines whether or not it is\n     * a null point\n     * @private\n     */\n    isValid() {\n        // Undefined is allowed\n        return (this.value !== Infinity &&\n            this.value !== -Infinity);\n    }\n}\nHeatmapPoint_extend(HeatmapPoint.prototype, {\n    dataLabelOnNull: true,\n    moveToTopOnHover: true,\n    ttBelow: false\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Heatmap_HeatmapPoint = (HeatmapPoint);\n\n;// ./code/es-modules/Series/Heatmap/HeatmapSeriesDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { isNumber: HeatmapSeriesDefaults_isNumber } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A heatmap is a graphical representation of data where the individual\n * values contained in a matrix are represented as colors.\n *\n * @productdesc {highcharts}\n * Requires `modules/heatmap`.\n *\n * @sample highcharts/demo/heatmap/\n *         Simple heatmap\n * @sample highcharts/demo/heatmap-canvas/\n *         Heavy heatmap\n *\n * @extends      plotOptions.scatter\n * @excluding    animationLimit, cluster, connectEnds, connectNulls,\n *               cropThreshold, dashStyle, dragDrop, findNearestPointBy,\n *               getExtremesFromAll, jitter, legendSymbolColor, linecap,\n *               lineWidth, pointInterval, pointIntervalUnit, pointRange,\n *               pointStart, shadow, softThreshold, stacking, step, threshold\n * @product      highcharts highmaps\n * @optionparent plotOptions.heatmap\n */\nconst HeatmapSeriesDefaults = {\n    /**\n     * Animation is disabled by default on the heatmap series.\n     */\n    animation: false,\n    /**\n     * The border radius for each heatmap item. The border's color and\n     * width can be set in marker options.\n     *\n     * @see [lineColor](#plotOptions.heatmap.marker.lineColor)\n     * @see [lineWidth](#plotOptions.heatmap.marker.lineWidth)\n     */\n    borderRadius: 0,\n    /**\n     * The border width for each heatmap item.\n     */\n    borderWidth: 0,\n    /**\n     * Padding between the points in the heatmap.\n     *\n     * @type      {number}\n     * @default   0\n     * @since     6.0\n     * @apioption plotOptions.heatmap.pointPadding\n     */\n    /**\n     * @default   value\n     * @apioption plotOptions.heatmap.colorKey\n     */\n    /**\n     * The main color of the series. In heat maps this color is rarely used,\n     * as we mostly use the color to denote the value of each point. Unless\n     * options are set in the [colorAxis](#colorAxis), the default value\n     * is pulled from the [options.colors](#colors) array.\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @since     4.0\n     * @product   highcharts\n     * @apioption plotOptions.heatmap.color\n     */\n    /**\n     * The column size - how many X axis units each column in the heatmap\n     * should span.\n     *\n     * @sample {highcharts} maps/demo/heatmap/\n     *         One day\n     * @sample {highmaps} maps/demo/heatmap/\n     *         One day\n     *\n     * @type      {number}\n     * @default   1\n     * @since     4.0\n     * @product   highcharts highmaps\n     * @apioption plotOptions.heatmap.colsize\n     */\n    /**\n     * The row size - how many Y axis units each heatmap row should span.\n     *\n     * @sample {highcharts} maps/demo/heatmap/\n     *         1 by default\n     * @sample {highmaps} maps/demo/heatmap/\n     *         1 by default\n     *\n     * @type      {number}\n     * @default   1\n     * @since     4.0\n     * @product   highcharts highmaps\n     * @apioption plotOptions.heatmap.rowsize\n     */\n    /**\n     * Make the heatmap render its data points as an interpolated image.\n     *\n     * @sample highcharts/demo/heatmap-interpolation\n     *   Interpolated heatmap image displaying user activity on a website\n     * @sample highcharts/series-heatmap/interpolation\n     *   Interpolated heatmap toggle\n     *\n     */\n    interpolation: false,\n    /**\n     * The color applied to null points. In styled mode, a general CSS class\n     * is applied instead.\n     *\n     * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     */\n    nullColor: \"#f7f7f7\" /* Palette.neutralColor3 */,\n    dataLabels: {\n        formatter: function () {\n            const { numberFormatter } = this.series.chart;\n            const { value } = this.point;\n            return HeatmapSeriesDefaults_isNumber(value) ? numberFormatter(value, -1) : '';\n        },\n        inside: true,\n        verticalAlign: 'middle',\n        crop: false,\n        /**\n         * @ignore-option\n         */\n        overflow: 'allow',\n        padding: 0 // #3837\n    },\n    /**\n     * @excluding radius, enabledThreshold\n     * @since     8.1\n     */\n    marker: {\n        /**\n         * A predefined shape or symbol for the marker. When undefined, the\n         * symbol is pulled from options.symbols. Other possible values are\n         * `'circle'`, `'square'`,`'diamond'`, `'triangle'`,\n         * `'triangle-down'`, `'rect'`, and `'ellipse'`.\n         *\n         * Additionally, the URL to a graphic can be given on this form:\n         * `'url(graphic.png)'`. Note that for the image to be applied to\n         * exported charts, its URL needs to be accessible by the export\n         * server.\n         *\n         * Custom callbacks for symbol path generation can also be added to\n         * `Highcharts.SVGRenderer.prototype.symbols`. The callback is then\n         * used by its method name, as shown in the demo.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-marker-symbol/\n         *         Predefined, graphic and custom markers\n         * @sample {highstock} highcharts/plotoptions/series-marker-symbol/\n         *         Predefined, graphic and custom markers\n         */\n        symbol: 'rect',\n        /** @ignore-option */\n        radius: 0,\n        lineColor: void 0,\n        states: {\n            /**\n             * @excluding radius, radiusPlus\n             */\n            hover: {\n                /**\n                 * Set the marker's fixed width on hover state.\n                 *\n                 * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-width\n                 *         70px fixed marker's width and height on hover\n                 *\n                 * @type      {number|undefined}\n                 * @default   undefined\n                 * @product   highcharts highmaps\n                 * @apioption plotOptions.heatmap.marker.states.hover.width\n                 */\n                /**\n                 * Set the marker's fixed height on hover state.\n                 *\n                 * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-width\n                 *         70px fixed marker's width and height on hover\n                 *\n                 * @type      {number|undefined}\n                 * @default   undefined\n                 * @product   highcharts highmaps\n                 * @apioption plotOptions.heatmap.marker.states.hover.height\n                 */\n                /**\n                 * The number of pixels to increase the width of the\n                 * selected point.\n                 *\n                 * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-widthplus\n                 *         20px greater width and height on hover\n                 *\n                 * @type      {number|undefined}\n                 * @default   undefined\n                 * @product   highcharts highmaps\n                 * @apioption plotOptions.heatmap.marker.states.hover.widthPlus\n                 */\n                /**\n                 * The number of pixels to increase the height of the\n                 * selected point.\n                 *\n                 * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-widthplus\n                *          20px greater width and height on hover\n                    *\n                    * @type      {number|undefined}\n                    * @default   undefined\n                    * @product   highcharts highmaps\n                    * @apioption plotOptions.heatmap.marker.states.hover.heightPlus\n                    */\n                /**\n                 * The additional line width for a hovered point.\n                 *\n                 * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-linewidthplus\n                 *         5 pixels wider lineWidth on hover\n                 * @sample {highmaps} maps/plotoptions/heatmap-marker-states-hover-linewidthplus\n                 *         5 pixels wider lineWidth on hover\n                 */\n                lineWidthPlus: 0\n            },\n            /**\n             * @excluding radius\n             */\n            select: {\n            /**\n             * Set the marker's fixed width on select state.\n             *\n             * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-width\n             *         70px fixed marker's width and height on hover\n             *\n             * @type      {number|undefined}\n             * @default   undefined\n             * @product   highcharts highmaps\n             * @apioption plotOptions.heatmap.marker.states.select.width\n             */\n            /**\n             * Set the marker's fixed height on select state.\n             *\n             * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-width\n             *         70px fixed marker's width and height on hover\n             *\n             * @type      {number|undefined}\n             * @default   undefined\n             * @product   highcharts highmaps\n             * @apioption plotOptions.heatmap.marker.states.select.height\n             */\n            /**\n             * The number of pixels to increase the width of the\n             * selected point.\n             *\n             * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-widthplus\n             *         20px greater width and height on hover\n             *\n             * @type      {number|undefined}\n             * @default   undefined\n             * @product   highcharts highmaps\n             * @apioption plotOptions.heatmap.marker.states.select.widthPlus\n             */\n            /**\n             * The number of pixels to increase the height of the\n             * selected point.\n             *\n             * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-widthplus\n             *         20px greater width and height on hover\n             *\n             * @type      {number|undefined}\n             * @default   undefined\n             * @product   highcharts highmaps\n             * @apioption plotOptions.heatmap.marker.states.select.heightPlus\n             */\n            }\n        }\n    },\n    clip: true,\n    /** @ignore-option */\n    pointRange: null, // Dynamically set to colsize by default\n    tooltip: {\n        pointFormat: '{point.x}, {point.y}: {point.value}<br/>'\n    },\n    states: {\n        hover: {\n            /** @ignore-option */\n            halo: false, // #3406, halo is disabled on heatmaps by default\n            /**\n             * How much to brighten the point on interaction.\n             *\n             * In styled mode, the hover brightening is by default replaced\n             * with a fill-opacity set in the `.highcharts-point:hover`\n             * rule.\n             */\n            brightness: 0.2\n        }\n    },\n    legendSymbol: 'rectangle'\n};\n/**\n * A `heatmap` series. If the [type](#series.heatmap.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @productdesc {highcharts}\n * Requires `modules/heatmap`.\n *\n * @extends   series,plotOptions.heatmap\n * @excluding cropThreshold, dataParser, dataURL, dragDrop ,pointRange, stack,\n * @product   highcharts highmaps\n * @apioption series.heatmap\n */\n/**\n * An array of data points for the series. For the `heatmap` series\n * type, points can be given in the following ways:\n *\n * 1.  An array of arrays with 3 or 2 values. In this case, the values\n * correspond to `x,y,value`. If the first value is a string, it is\n * applied as the name of the point, and the `x` value is inferred.\n * The `x` value can also be omitted, in which case the inner arrays\n * should be of length 2\\. Then the `x` value is automatically calculated,\n * either starting at 0 and incremented by 1, or from `pointStart`\n * and `pointInterval` given in the series options.\n *\n *  ```js\n *     data: [\n *         [0, 9, 7],\n *         [1, 10, 4],\n *         [2, 6, 3]\n *     ]\n *  ```\n *\n * 2.  An array of objects with named values. The following snippet shows only a\n * few settings, see the complete options set below. If the total number of data\n * points exceeds the series' [turboThreshold](#series.heatmap.turboThreshold),\n * this option is not available.\n *\n *  ```js\n *     data: [{\n *         x: 1,\n *         y: 3,\n *         value: 10,\n *         name: \"Point2\",\n *         color: \"#00FF00\"\n *     }, {\n *         x: 1,\n *         y: 7,\n *         value: 10,\n *         name: \"Point1\",\n *         color: \"#FF00FF\"\n *     }]\n *  ```\n *\n * @sample {highcharts} highcharts/chart/reflow-true/\n *         Numerical values\n * @sample {highcharts} highcharts/series/data-array-of-arrays/\n *         Arrays of numeric x and y\n * @sample {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *         Arrays of datetime x and y\n * @sample {highcharts} highcharts/series/data-array-of-name-value/\n *         Arrays of point.name and y\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<Array<number>|*>}\n * @extends   series.line.data\n * @product   highcharts highmaps\n * @apioption series.heatmap.data\n */\n/**\n * The color of the point. In heat maps the point color is rarely set\n * explicitly, as we use the color to denote the `value`. Options for\n * this are set in the [colorAxis](#colorAxis) configuration.\n *\n * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n * @product   highcharts highmaps\n * @apioption series.heatmap.data.color\n */\n/**\n * The value of the point, resulting in a color controlled by options\n * as set in the [colorAxis](#colorAxis) configuration.\n *\n * @type      {number}\n * @product   highcharts highmaps\n * @apioption series.heatmap.data.value\n */\n/**\n * The x value of the point. For datetime axes,\n * the X value is the timestamp in milliseconds since 1970.\n *\n * @type      {number}\n * @product   highcharts highmaps\n * @apioption series.heatmap.data.x\n */\n/**\n * The y value of the point.\n *\n * @type      {number}\n * @product   highcharts highmaps\n * @apioption series.heatmap.data.y\n */\n/**\n * Point padding for a single point.\n *\n * @sample maps/plotoptions/tilemap-pointpadding\n *         Point padding on tiles\n *\n * @type      {number}\n * @product   highcharts highmaps\n * @apioption series.heatmap.data.pointPadding\n */\n/**\n * @excluding radius, enabledThreshold\n * @product   highcharts highmaps\n * @since     8.1\n * @apioption series.heatmap.data.marker\n */\n/**\n * @excluding radius, enabledThreshold\n * @product   highcharts highmaps\n * @since     8.1\n * @apioption series.heatmap.marker\n */\n/**\n * @excluding radius, radiusPlus\n * @product   highcharts highmaps\n * @apioption series.heatmap.marker.states.hover\n */\n/**\n * @excluding radius\n * @product   highcharts highmaps\n * @apioption series.heatmap.marker.states.select\n */\n/**\n * @excluding radius, radiusPlus\n * @product   highcharts highmaps\n * @apioption series.heatmap.data.marker.states.hover\n */\n/**\n * @excluding radius\n * @product   highcharts highmaps\n * @apioption series.heatmap.data.marker.states.select\n */\n/**\n* Set the marker's fixed width on hover state.\n*\n* @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-linewidthplus\n*         5 pixels wider lineWidth on hover\n*\n* @type      {number|undefined}\n* @default   0\n* @product   highcharts highmaps\n* @apioption series.heatmap.marker.states.hover.lineWidthPlus\n*/\n/**\n* Set the marker's fixed width on hover state.\n*\n* @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-width\n*         70px fixed marker's width and height on hover\n*\n* @type      {number|undefined}\n* @default   undefined\n* @product   highcharts highmaps\n* @apioption series.heatmap.marker.states.hover.width\n*/\n/**\n * Set the marker's fixed height on hover state.\n *\n * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-width\n *         70px fixed marker's width and height on hover\n *\n * @type      {number|undefined}\n * @default   undefined\n * @product   highcharts highmaps\n * @apioption series.heatmap.marker.states.hover.height\n */\n/**\n* The number of pixels to increase the width of the\n* hovered point.\n*\n* @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-widthplus\n*         One day\n*\n* @type      {number|undefined}\n* @default   undefined\n* @product   highcharts highmaps\n* @apioption series.heatmap.marker.states.hover.widthPlus\n*/\n/**\n * The number of pixels to increase the height of the\n * hovered point.\n *\n * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-widthplus\n *         One day\n *\n * @type      {number|undefined}\n * @default   undefined\n * @product   highcharts highmaps\n * @apioption series.heatmap.marker.states.hover.heightPlus\n */\n/**\n * The number of pixels to increase the width of the\n * hovered point.\n *\n * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-widthplus\n *         One day\n *\n * @type      {number|undefined}\n * @default   undefined\n * @product   highcharts highmaps\n * @apioption series.heatmap.marker.states.select.widthPlus\n */\n/**\n * The number of pixels to increase the height of the\n * hovered point.\n *\n * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-widthplus\n *         One day\n *\n * @type      {number|undefined}\n * @default   undefined\n * @product   highcharts highmaps\n * @apioption series.heatmap.marker.states.select.heightPlus\n */\n/**\n* Set the marker's fixed width on hover state.\n*\n* @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-linewidthplus\n*         5 pixels wider lineWidth on hover\n*\n* @type      {number|undefined}\n* @default   0\n* @product   highcharts highmaps\n* @apioption series.heatmap.data.marker.states.hover.lineWidthPlus\n*/\n/**\n * Set the marker's fixed width on hover state.\n *\n * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-width\n *         70px fixed marker's width and height on hover\n *\n * @type      {number|undefined}\n * @default   undefined\n * @product   highcharts highmaps\n * @apioption series.heatmap.data.marker.states.hover.width\n */\n/**\n * Set the marker's fixed height on hover state.\n *\n * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-width\n *         70px fixed marker's width and height on hover\n *\n * @type      {number|undefined}\n * @default   undefined\n * @product   highcharts highmaps\n * @apioption series.heatmap.data.marker.states.hover.height\n */\n/**\n * The number of pixels to increase the width of the\n * hovered point.\n *\n * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-widthplus\n *         One day\n *\n * @type      {number|undefined}\n * @default   undefined\n * @product   highcharts highstock\n * @apioption series.heatmap.data.marker.states.hover.widthPlus\n */\n/**\n * The number of pixels to increase the height of the\n * hovered point.\n *\n * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-widthplus\n *         One day\n *\n * @type      {number|undefined}\n * @default   undefined\n * @product   highcharts highstock\n * @apioption series.heatmap.data.marker.states.hover.heightPlus\n */\n/**\n* Set the marker's fixed width on select state.\n*\n* @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-width\n*         70px fixed marker's width and height on hover\n*\n* @type      {number|undefined}\n* @default   undefined\n* @product   highcharts highmaps\n* @apioption series.heatmap.data.marker.states.select.width\n*/\n/**\n * Set the marker's fixed height on select state.\n *\n * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-width\n *         70px fixed marker's width and height on hover\n *\n * @type      {number|undefined}\n * @default   undefined\n * @product   highcharts highmaps\n * @apioption series.heatmap.data.marker.states.select.height\n */\n/**\n * The number of pixels to increase the width of the\n * hovered point.\n *\n * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-widthplus\n *         One day\n *\n * @type      {number|undefined}\n * @default   undefined\n * @product   highcharts highstock\n * @apioption series.heatmap.data.marker.states.select.widthPlus\n */\n/**\n * The number of pixels to increase the height of the\n * hovered point.\n *\n * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-widthplus\n *         One day\n *\n * @type      {number|undefined}\n * @default   undefined\n * @product   highcharts highstock\n * @apioption series.heatmap.data.marker.states.select.heightPlus\n */\n''; // Keeps doclets above separate\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Heatmap_HeatmapSeriesDefaults = (HeatmapSeriesDefaults);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SVGRenderer\"],\"commonjs\":[\"highcharts\",\"SVGRenderer\"],\"commonjs2\":[\"highcharts\",\"SVGRenderer\"],\"root\":[\"Highcharts\",\"SVGRenderer\"]}\nvar highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_ = __webpack_require__(540);\nvar highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default = /*#__PURE__*/__webpack_require__.n(highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_);\n;// ./code/es-modules/Series/InterpolationUtilities.js\n/* *\n *\n *  (c) 2010-2025 Hubert Kozik\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { doc } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { defined: InterpolationUtilities_defined, pick: InterpolationUtilities_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Find color of point based on color axis.\n *\n * @function Highcharts.colorFromPoint\n *\n * @param {number | null} value\n *        Value to find corresponding color on the color axis.\n *\n * @param {Highcharts.Point} point\n *        Point to find it's color from color axis.\n *\n * @return {number[]}\n *        Color in RGBa array.\n */\nfunction colorFromPoint(value, point) {\n    const colorAxis = point.series.colorAxis;\n    if (colorAxis) {\n        const rgba = (colorAxis.toColor(value || 0, point)\n            .split(')')[0]\n            .split('(')[1]\n            .split(',')\n            .map((s) => InterpolationUtilities_pick(parseFloat(s), parseInt(s, 10))));\n        rgba[3] = InterpolationUtilities_pick(rgba[3], 1.0) * 255;\n        if (!InterpolationUtilities_defined(value) || !point.visible) {\n            rgba[3] = 0;\n        }\n        return rgba;\n    }\n    return [0, 0, 0, 0];\n}\n/**\n * Method responsible for creating a canvas for interpolation image.\n * @private\n */\nfunction getContext(series) {\n    const { canvas, context } = series;\n    if (canvas && context) {\n        context.clearRect(0, 0, canvas.width, canvas.height);\n    }\n    else {\n        series.canvas = doc.createElement('canvas');\n        series.context = series.canvas.getContext('2d', {\n            willReadFrequently: true\n        }) || void 0;\n        return series.context;\n    }\n    return context;\n}\nconst InterpolationUtilities = {\n    colorFromPoint,\n    getContext\n};\n/* harmony default export */ const Series_InterpolationUtilities = (InterpolationUtilities);\n\n;// ./code/es-modules/Series/Heatmap/HeatmapSeries.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\n\nconst { series: HeatmapSeries_Series, seriesTypes: { column: ColumnSeries, scatter: ScatterSeries } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { prototype: { symbols } } = (highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default());\n\nconst { addEvent: HeatmapSeries_addEvent, extend: HeatmapSeries_extend, fireEvent: HeatmapSeries_fireEvent, isNumber: HeatmapSeries_isNumber, merge: HeatmapSeries_merge, pick: HeatmapSeries_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { colorFromPoint: HeatmapSeries_colorFromPoint, getContext: HeatmapSeries_getContext } = Series_InterpolationUtilities;\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.heatmap\n *\n * @augments Highcharts.Series\n */\nclass HeatmapSeries extends ScatterSeries {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        this.valueMax = NaN;\n        this.valueMin = NaN;\n        this.isDirtyCanvas = true;\n        /* eslint-enable valid-jsdoc */\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    drawPoints() {\n        const series = this, seriesOptions = series.options, interpolation = seriesOptions.interpolation, seriesMarkerOptions = seriesOptions.marker || {};\n        if (interpolation) {\n            const { image, chart, xAxis, yAxis } = series, { reversed: xRev = false, len: width } = xAxis, { reversed: yRev = false, len: height } = yAxis, dimensions = { width, height };\n            if (!image || series.isDirtyData || series.isDirtyCanvas) {\n                const ctx = HeatmapSeries_getContext(series), { canvas, options: { colsize = 1, rowsize = 1 }, points, points: { length } } = series, pointsLen = length - 1, colorAxis = (chart.colorAxis && chart.colorAxis[0]);\n                if (canvas && ctx && colorAxis) {\n                    const { min: xMin, max: xMax } = xAxis.getExtremes(), { min: yMin, max: yMax } = yAxis.getExtremes(), xDelta = xMax - xMin, yDelta = yMax - yMin, imgMultiple = 8.0, lastX = Math.round(imgMultiple * ((xDelta / colsize) / imgMultiple)), lastY = Math.round(imgMultiple * ((yDelta / rowsize) / imgMultiple)), [transformX, transformY] = [\n                        [lastX, lastX / xDelta, xRev, 'ceil'],\n                        [lastY, lastY / yDelta, !yRev, 'floor']\n                    ].map(([last, scale, rev, rounding]) => (rev ?\n                        (v) => (Math[rounding](last -\n                            (scale * (v)))) :\n                        (v) => (Math[rounding](scale * v)))), canvasWidth = canvas.width = lastX + 1, canvasHeight = canvas.height = lastY + 1, canvasArea = canvasWidth * canvasHeight, pixelToPointScale = pointsLen / canvasArea, pixelData = new Uint8ClampedArray(canvasArea * 4), pointInPixels = (x, y) => (Math.ceil((canvasWidth * transformY(y - yMin)) +\n                        transformX(x - xMin)) * 4);\n                    series.buildKDTree();\n                    for (let i = 0; i < canvasArea; i++) {\n                        const point = points[Math.ceil(pixelToPointScale * i)], { x, y } = point;\n                        pixelData.set(HeatmapSeries_colorFromPoint(point.value, point), pointInPixels(x, y));\n                    }\n                    ctx.putImageData(new ImageData(pixelData, canvasWidth), 0, 0);\n                    if (image) {\n                        image.attr({\n                            ...dimensions,\n                            href: canvas.toDataURL('image/png', 1)\n                        });\n                    }\n                    else {\n                        series.directTouch = false;\n                        series.image = chart.renderer.image(canvas.toDataURL('image/png', 1))\n                            .attr(dimensions)\n                            .add(series.group);\n                    }\n                }\n                series.isDirtyCanvas = false;\n            }\n            else if (image.width !== width || image.height !== height) {\n                image.attr(dimensions);\n            }\n        }\n        else if (seriesMarkerOptions.enabled || series._hasPointMarkers) {\n            HeatmapSeries_Series.prototype.drawPoints.call(series);\n            series.points.forEach((point) => {\n                if (point.graphic) {\n                    // In styled mode, use CSS, otherwise the fill used in\n                    // the style sheet will take precedence over\n                    // the fill attribute.\n                    point.graphic[series.chart.styledMode ? 'css' : 'animate'](series.colorAttribs(point));\n                    if (point.value === null) { // #15708\n                        point.graphic.addClass('highcharts-null-point');\n                    }\n                }\n            });\n        }\n    }\n    /**\n     * @private\n     */\n    getExtremes() {\n        // Get the extremes from the value data\n        const { dataMin, dataMax } = HeatmapSeries_Series.prototype.getExtremes\n            .call(this, this.getColumn('value'));\n        if (HeatmapSeries_isNumber(dataMin)) {\n            this.valueMin = dataMin;\n        }\n        if (HeatmapSeries_isNumber(dataMax)) {\n            this.valueMax = dataMax;\n        }\n        // Get the extremes from the y data\n        return HeatmapSeries_Series.prototype.getExtremes.call(this);\n    }\n    /**\n     * Override to also allow null points, used when building the k-d-tree for\n     * tooltips in boost mode.\n     * @private\n     */\n    getValidPoints(points, insideOnly) {\n        return HeatmapSeries_Series.prototype.getValidPoints.call(this, points, insideOnly, true);\n    }\n    /**\n     * Define hasData function for non-cartesian series. Returns true if the\n     * series has points at all.\n     * @private\n     */\n    hasData() {\n        return !!this.dataTable.rowCount;\n    }\n    /**\n     * Override the init method to add point ranges on both axes.\n     * @private\n     */\n    init() {\n        super.init.apply(this, arguments);\n        const options = this.options;\n        // #3758, prevent resetting in setData\n        options.pointRange = HeatmapSeries_pick(options.pointRange, options.colsize || 1);\n        // General point range\n        this.yAxis.axisPointRange = options.rowsize || 1;\n        // Bind new symbol names\n        symbols.ellipse = symbols.circle;\n        // @todo\n        //\n        // Setting the border radius here is a workaround. It should be set in\n        // the shapeArgs or returned from `markerAttribs`. However,\n        // Series.drawPoints does not pick up markerAttribs to be passed over to\n        // `renderer.symbol`. Also, image symbols are not positioned by their\n        // top left corner like other symbols are. This should be refactored,\n        // then we could save ourselves some tests for .hasImage etc. And the\n        // evaluation of borderRadius would be moved to `markerAttribs`.\n        if (options.marker && HeatmapSeries_isNumber(options.borderRadius)) {\n            options.marker.r = options.borderRadius;\n        }\n    }\n    /**\n     * @private\n     */\n    markerAttribs(point, state) {\n        const shapeArgs = point.shapeArgs || {};\n        if (point.hasImage) {\n            return {\n                x: point.plotX,\n                y: point.plotY\n            };\n        }\n        // Setting width and height attributes on image does not affect on its\n        // dimensions.\n        if (state && state !== 'normal') {\n            const pointMarkerOptions = point.options.marker || {}, seriesMarkerOptions = this.options.marker || {}, seriesStateOptions = (seriesMarkerOptions.states?.[state]) || {}, pointStateOptions = (pointMarkerOptions.states?.[state]) || {};\n            // Set new width and height basing on state options.\n            const width = (pointStateOptions.width ||\n                seriesStateOptions.width ||\n                shapeArgs.width ||\n                0) + (pointStateOptions.widthPlus ||\n                seriesStateOptions.widthPlus ||\n                0);\n            const height = (pointStateOptions.height ||\n                seriesStateOptions.height ||\n                shapeArgs.height ||\n                0) + (pointStateOptions.heightPlus ||\n                seriesStateOptions.heightPlus ||\n                0);\n            // Align marker by the new size.\n            const x = (shapeArgs.x || 0) + ((shapeArgs.width || 0) - width) / 2, y = (shapeArgs.y || 0) + ((shapeArgs.height || 0) - height) / 2;\n            return { x, y, width, height };\n        }\n        return shapeArgs;\n    }\n    /**\n     * @private\n     */\n    pointAttribs(point, state) {\n        const series = this, attr = HeatmapSeries_Series.prototype.pointAttribs.call(series, point, state), seriesOptions = series.options || {}, plotOptions = series.chart.options.plotOptions || {}, seriesPlotOptions = plotOptions.series || {}, heatmapPlotOptions = plotOptions.heatmap || {}, \n        // Get old properties in order to keep backward compatibility\n        borderColor = point?.options.borderColor ||\n            seriesOptions.borderColor ||\n            heatmapPlotOptions.borderColor ||\n            seriesPlotOptions.borderColor, borderWidth = point?.options.borderWidth ||\n            seriesOptions.borderWidth ||\n            heatmapPlotOptions.borderWidth ||\n            seriesPlotOptions.borderWidth ||\n            attr['stroke-width'];\n        // Apply lineColor, or set it to default series color.\n        attr.stroke = (point?.marker?.lineColor ||\n            seriesOptions.marker?.lineColor ||\n            borderColor ||\n            this.color);\n        // Apply old borderWidth property if exists.\n        attr['stroke-width'] = borderWidth;\n        if (state && state !== 'normal') {\n            const stateOptions = HeatmapSeries_merge(seriesOptions.states?.[state], seriesOptions.marker?.states?.[state], point?.options.states?.[state] || {});\n            attr.fill =\n                stateOptions.color ||\n                    highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default().parse(attr.fill).brighten(stateOptions.brightness || 0).get();\n            attr.stroke = (stateOptions.lineColor || attr.stroke); // #17896\n        }\n        return attr;\n    }\n    /**\n     * @private\n     */\n    translate() {\n        const series = this, options = series.options, { borderRadius, marker } = options, symbol = marker?.symbol || 'rect', shape = symbols[symbol] ? symbol : 'rect', hasRegularShape = ['circle', 'square'].indexOf(shape) !== -1;\n        series.generatePoints();\n        for (const point of series.points) {\n            const cellAttr = point.getCellAttributes();\n            let x = Math.min(cellAttr.x1, cellAttr.x2), y = Math.min(cellAttr.y1, cellAttr.y2), width = Math.max(Math.abs(cellAttr.x2 - cellAttr.x1), 0), height = Math.max(Math.abs(cellAttr.y2 - cellAttr.y1), 0);\n            point.hasImage = (point.marker?.symbol || symbol || '').indexOf('url') === 0;\n            // If marker shape is regular (square), find the shorter cell's\n            // side.\n            if (hasRegularShape) {\n                const sizeDiff = Math.abs(width - height);\n                x = Math.min(cellAttr.x1, cellAttr.x2) +\n                    (width < height ? 0 : sizeDiff / 2);\n                y = Math.min(cellAttr.y1, cellAttr.y2) +\n                    (width < height ? sizeDiff / 2 : 0);\n                width = height = Math.min(width, height);\n            }\n            if (point.hasImage) {\n                point.marker = { width, height };\n            }\n            point.plotX = point.clientX = (cellAttr.x1 + cellAttr.x2) / 2;\n            point.plotY = (cellAttr.y1 + cellAttr.y2) / 2;\n            point.shapeType = 'path';\n            point.shapeArgs = HeatmapSeries_merge(true, { x, y, width, height }, {\n                d: symbols[shape](x, y, width, height, { r: HeatmapSeries_isNumber(borderRadius) ? borderRadius : 0 })\n            });\n        }\n        HeatmapSeries_fireEvent(series, 'afterTranslate');\n    }\n}\nHeatmapSeries.defaultOptions = HeatmapSeries_merge(ScatterSeries.defaultOptions, Heatmap_HeatmapSeriesDefaults);\nHeatmapSeries_addEvent(HeatmapSeries, 'afterDataClassLegendClick', function () {\n    this.isDirtyCanvas = true;\n    this.drawPoints();\n});\nHeatmapSeries_extend(HeatmapSeries.prototype, {\n    axisTypes: Series_ColorMapComposition.seriesMembers.axisTypes,\n    colorKey: Series_ColorMapComposition.seriesMembers.colorKey,\n    directTouch: true,\n    getExtremesFromAll: true,\n    keysAffectYAxis: ['y'],\n    parallelArrays: Series_ColorMapComposition.seriesMembers.parallelArrays,\n    pointArrayMap: ['y', 'value'],\n    pointClass: Heatmap_HeatmapPoint,\n    specialGroup: 'group',\n    trackerGroups: Series_ColorMapComposition.seriesMembers.trackerGroups,\n    /**\n     * @private\n     */\n    alignDataLabel: ColumnSeries.prototype.alignDataLabel,\n    colorAttribs: Series_ColorMapComposition.seriesMembers.colorAttribs,\n    getSymbol: HeatmapSeries_Series.prototype.getSymbol\n});\nSeries_ColorMapComposition.compose(HeatmapSeries);\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('heatmap', HeatmapSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Heatmap_HeatmapSeries = ((/* unused pure expression or super */ null && (HeatmapSeries)));\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Heatmap series only. Padding between the points in the heatmap.\n * @name Highcharts.Point#pointPadding\n * @type {number|undefined}\n */\n/**\n * Heatmap series only. The value of the point, resulting in a color\n * controlled by options as set in the colorAxis configuration.\n * @name Highcharts.Point#value\n * @type {number|null|undefined}\n */\n/* *\n * @interface Highcharts.PointOptionsObject in parts/Point.ts\n */ /**\n* Heatmap series only. Point padding for a single point.\n* @name Highcharts.PointOptionsObject#pointPadding\n* @type {number|undefined}\n*/ /**\n* Heatmap series only. The value of the point, resulting in a color controlled\n* by options as set in the colorAxis configuration.\n* @name Highcharts.PointOptionsObject#value\n* @type {number|null|undefined}\n*/\n''; // Detach doclets above\n\n;// ./code/es-modules/masters/modules/heatmap.js\n/**\n * @license Highmaps JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/heatmap\n * @requires highcharts\n *\n * (c) 2009-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n\n\n\n\n/* harmony default export */ const heatmap_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__532__", "__WEBPACK_EXTERNAL_MODULE__620__", "__WEBPACK_EXTERNAL_MODULE__500__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__28__", "__WEBPACK_EXTERNAL_MODULE__540__", "ColorAxisComposition", "ColorAxisLike", "ColorMapComposition", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "heatmap_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_", "highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default", "parse", "color", "addEvent", "extend", "merge", "pick", "splat", "ColorAxisConstructor", "onChartAfterCreateAxes", "userOptions", "colorAxis", "map", "axisOptions", "onLegendAfterGetAllItems", "e", "colorAxes", "chart", "destroyItem", "item", "i", "allItems", "indexOf", "splice", "colorAxisItems", "options", "for<PERSON>ach", "showInLegend", "dataClasses", "visible", "concat", "getDataClassLegendSymbols", "push", "series", "legendType", "points", "point", "length", "unshift", "onLegendAfterColorizeItem", "legendColor", "legendItem", "symbol", "attr", "fill", "onLegendAfterUpdate", "update", "redraw", "onSeriesAfterTranslate", "colorAttribs", "translateColors", "onSeriesBindAxes", "axisTypes", "pointSetVisible", "vis", "method", "Boolean", "buildKDTree", "seriesTranslateColors", "getPointsCollection", "nullColor", "colorKey", "value", "getNestedProperty", "isNull", "toColor", "label", "legend", "colorizeItem", "wrapFxFillSetter", "elem", "start", "tweenTo", "end", "pos", "wrapFxStrokeSetter", "compose", "ColorAxisClass", "ChartClass", "FxClass", "LegendClass", "SeriesClass", "chartProto", "fxProto", "seriesProto", "collectionsWithUpdate", "includes", "collectionsWithInit", "addColorAxis", "wrapChartCreateAxis", "superCreateAxis", "createAxis", "type", "apply", "arguments", "axis", "index", "isX", "isDirtyLegend", "axes", "bindAxes", "isDirtyData", "animation", "fillSetter", "strokeSetter", "optionalAxis", "pointClass", "setVisible", "order", "Color_ColorAxisComposition", "ColorAxisLike_color", "ColorAxisLike_merge", "initDataClasses", "userDataClasses", "dataClass", "colorCount", "colorCounter", "colors", "labels", "iEnd", "styledMode", "dataClassColor", "colorIndex", "minColor", "maxColor", "initStops", "stops", "normalizedValue", "max", "min", "logarithmic", "log2lin", "from", "to", "Color_ColorAxisLike", "highcharts_LegendSymbol_commonjs_highcharts_LegendSymbol_commonjs2_highcharts_LegendSymbol_root_Highcharts_LegendSymbol_", "highcharts_LegendSymbol_commonjs_highcharts_LegendSymbol_commonjs2_highcharts_LegendSymbol_root_Highcharts_LegendSymbol_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "defaultOptions", "Series", "defined", "ColorAxis_extend", "fireEvent", "isArray", "isNumber", "ColorAxis_merge", "ColorAxis_pick", "<PERSON><PERSON><PERSON><PERSON>", "xAxis", "lineWidth", "minPadding", "maxPadding", "gridLineColor", "gridLineWidth", "tickPixelInterval", "startOnTick", "endOnTick", "offset", "marker", "duration", "width", "distance", "overflow", "rotation", "tick<PERSON><PERSON>th", "ColorAxis", "constructor", "coll", "init", "horiz", "layout", "side", "reversed", "opposite", "zoomEnabled", "hasData", "tickPositions", "setTickPositions", "setOptions", "showEmpty", "title", "enabled", "crosshair", "setAxisSize", "height", "getSize", "left", "top", "right", "chartWidth", "bottom", "chartHeight", "len", "defaultLegendLength", "getOffset", "group", "sideOffset", "axisOffset", "axisParent", "drawLegendSymbol", "render", "<PERSON><PERSON><PERSON><PERSON>", "some", "isDrilling", "isDirty", "added", "labelLeft", "labelRight", "setLegendColor", "one", "zero", "grad", "linearGradient", "x1", "y1", "x2", "y2", "padding", "legendOptions", "labelOptions", "itemDistance", "labelPadding", "renderer", "r", "symbolRadius", "zIndex", "add", "x", "y", "baseline", "labelWidth", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "labelHeight", "setState", "state", "getSeriesExtremes", "colorValArray", "calculatedExtremes", "cSeries", "dataMin", "Infinity", "dataMax", "pointVal<PERSON>ey", "zoneAxis", "getColumn", "minColorValue", "maxColorValue", "cExtremes", "getExtremes", "Math", "applyExtremes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crossPos", "plotX", "plotY", "axisPos", "axisLen", "toPixels", "cross", "addedToColorAxis", "addClass", "getPlotLinePath", "translatedValue", "newOptions", "destroyItems", "destroy", "slice", "remove", "name", "legendItems", "valueDecimals", "valueSuffix", "getPointsInDataClass", "reduce", "s", "filter", "numberF<PERSON>atter", "rectangle", "isDataClass", "affectedSeries", "hiddenInDataClass", "colorAxisHeight", "colorAxis<PERSON>idth", "symbolWidth", "symbolHeight", "keepProps", "Array", "G", "Chart", "Fx", "Legend", "highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_", "highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default", "column", "columnProto", "seriesTypes", "ColorMapComposition_addEvent", "ColorMapComposition_defined", "onPointAfterSetState", "moveToTopOnHover", "graphic", "stateMarkerGraphic", "css", "pointerEvents", "parentGroup", "id", "href", "url", "visibility", "pointMembers", "dataLabelOnNull", "<PERSON><PERSON><PERSON><PERSON>", "isNaN", "seriesMembers", "parallelArrays", "pointArrayMap", "trackerGroups", "ret", "colorProp", "pointAttribs", "Series_ColorMapComposition", "scatter", "ScatterPoint", "clamp", "HeatmapPoint_defined", "HeatmapPoint_extend", "HeatmapPoint_pick", "HeatmapPoint", "applyOptions", "formatPrefix", "getCellAttributes", "seriesOptions", "xPad", "colsize", "yPad", "rowsize", "yAxis", "markerOptions", "pointPlacement", "pointPlacementToXValue", "pointPadding", "cellAttr", "round", "translate", "dimension", "direction", "abs", "borderWidth", "plotPos", "widthOrHeight", "halfCellSize", "haloPath", "size", "shapeArgs", "ttBelow", "HeatmapSeriesDefaults_isNumber", "highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_", "highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default", "doc", "InterpolationUtilities_defined", "InterpolationUtilities_pick", "HeatmapSeries_Series", "ColumnSeries", "ScatterSeries", "symbols", "HeatmapSeries_addEvent", "HeatmapSeries_extend", "HeatmapSeries_fireEvent", "HeatmapSeries_isNumber", "HeatmapSeries_merge", "HeatmapSeries_pick", "colorFromPoint", "HeatmapSeries_colorFromPoint", "getContext", "HeatmapSeries_getContext", "rgba", "split", "parseFloat", "parseInt", "canvas", "context", "clearRect", "createElement", "willReadFrequently", "HeatmapSeries", "valueMax", "NaN", "valueMin", "isDirtyCanvas", "drawPoints", "interpolation", "seriesMarkerOptions", "image", "xRev", "yRev", "dimensions", "ctx", "xMin", "xMax", "yMin", "yMax", "xDelta", "y<PERSON><PERSON><PERSON>", "lastX", "imgMultiple", "lastY", "transformX", "transformY", "last", "scale", "rev", "rounding", "v", "canvasWidth", "canvasArea", "pixelToPointScale", "pointsLen", "pixelData", "Uint8ClampedArray", "pointInPixels", "ceil", "set", "putImageData", "ImageData", "toDataURL", "directTouch", "_hasPointMarkers", "getValidPoints", "insideOnly", "dataTable", "rowCount", "pointRange", "axisPointRange", "ellipse", "circle", "borderRadius", "markerAttribs", "hasImage", "pointMarkerOptions", "seriesStateOptions", "states", "pointStateOptions", "widthPlus", "heightPlus", "plotOptions", "seriesPlotOptions", "heatmapPlotOptions", "heatmap", "borderColor", "stroke", "lineColor", "stateOptions", "brighten", "brightness", "shape", "hasRegularShape", "generatePoints", "sizeDiff", "clientX", "shapeType", "dataLabels", "formatter", "inside", "verticalAlign", "crop", "radius", "hover", "lineWidthPlus", "select", "clip", "tooltip", "pointFormat", "halo", "legendSymbol", "getExtremesFromAll", "keysAffectYAxis", "specialGroup", "alignDataLabel", "getSymbol", "registerSeriesType"], "mappings": "CAWA,AAXA;;;;;;;;;;CAUC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,IAAO,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,YAAe,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,UAAa,CAAEA,EAAK,WAAc,CAAC,WAAc,EACnP,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,6BAA8B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,IAAO,CAACA,EAAK,KAAQ,CAACA,EAAK,YAAe,CAACA,EAAK,cAAiB,CAACA,EAAK,UAAa,CAACA,EAAK,WAAc,CAAE,GAC7M,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,6BAA6B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,IAAO,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,YAAe,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,UAAa,CAAEA,EAAK,WAAc,CAAC,WAAc,EAEjRA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,IAAO,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,YAAe,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,UAAa,CAAEA,EAAK,UAAa,CAAC,WAAc,CACzP,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,EAAiCC,IAC9O,AAAC,CAAA,KACP,aACA,IAgJNC,EAouBAC,EA8tBAC,EAllDUC,EAAuB,CAE/B,GACC,AAACf,IAERA,EAAOD,OAAO,CAAGW,CAEX,EAEA,IACC,AAACV,IAERA,EAAOD,OAAO,CAAGS,CAEX,EAEA,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGU,CAEX,EAEA,IACC,AAACT,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGY,CAEX,EAEA,IACC,AAACX,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIW,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAapB,OAAO,CAG5B,IAAIC,EAASgB,CAAwB,CAACE,EAAS,CAAG,CAGjDnB,QAAS,CAAC,CACX,EAMA,OAHAgB,CAAmB,CAACG,EAAS,CAAClB,EAAQA,EAAOD,OAAO,CAAEkB,GAG/CjB,EAAOD,OAAO,AACtB,CAMCkB,EAAoBI,CAAC,CAAG,AAACrB,IACxB,IAAIsB,EAAStB,GAAUA,EAAOuB,UAAU,CACvC,IAAOvB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAiB,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACzB,EAAS2B,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAAC7B,EAAS4B,IAC5EE,OAAOC,cAAc,CAAC/B,EAAS4B,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,EAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAA2FzB,EAAoB,KAC/G0B,EAA+G1B,EAAoBI,CAAC,CAACqB,GAErIE,EAA+F3B,EAAoB,KACnH4B,EAAmH5B,EAAoBI,CAAC,CAACuB,GAa7I,GAAM,CAAEE,MAAOC,CAAK,CAAE,CAAIF,IAEpB,CAAEG,SAAAA,CAAQ,CAAEC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAEC,MAAAA,CAAK,CAAE,CAAIX,KAOlD,AAAC,SAAU7B,CAAoB,EAW3B,IAAIyC,EAwCJ,SAASC,IACL,GAAM,CAAEC,YAAAA,CAAW,CAAE,CAAG,IAAI,AAC5B,CAAA,IAAI,CAACC,SAAS,CAAG,EAAE,CAGfD,EAAYC,SAAS,GACrBD,EAAYC,SAAS,CAAGJ,EAAMG,EAAYC,SAAS,EACnDD,EAAYC,SAAS,CAACC,GAAG,CAAC,AAACC,GAAiB,IAAIL,EAAqB,IAAI,CAAEK,IAEnF,CAMA,SAASC,EAAyBC,CAAC,EAC/B,IAAMC,EAAY,IAAI,CAACC,KAAK,CAACN,SAAS,EAAI,EAAE,CAAEO,EAAc,AAACC,IACzD,IAAMC,EAAIL,EAAEM,QAAQ,CAACC,OAAO,CAACH,EACnB,CAAA,KAANC,IAEA,IAAI,CAACF,WAAW,CAACH,EAAEM,QAAQ,CAACD,EAAE,EAC9BL,EAAEM,QAAQ,CAACE,MAAM,CAACH,EAAG,GAE7B,EACII,EAAiB,EAAE,CAAEC,EAASL,EA8BlC,IA7BAJ,EAAUU,OAAO,CAAC,SAAUf,CAAS,EACjCc,EAAUd,EAAUc,OAAO,CACvBA,GAASE,eAELF,EAAQG,WAAW,EAAIH,EAAQI,OAAO,CACtCL,EAAiBA,EAAeM,MAAM,CAACnB,EAAUoB,yBAAyB,IAGrEN,EAAQI,OAAO,EAEpBL,EAAeQ,IAAI,CAACrB,GAIxBA,EAAUsB,MAAM,CAACP,OAAO,CAAC,SAAUO,CAAM,EACjC,CAAA,CAACA,EAAOR,OAAO,CAACE,YAAY,EAAIF,EAAQG,WAAW,AAAD,IAC9CK,AAA8B,UAA9BA,EAAOR,OAAO,CAACS,UAAU,CACzBD,EAAOE,MAAM,CAACT,OAAO,CAAC,SAAUU,CAAK,EACjClB,EAAYkB,EAChB,GAGAlB,EAAYe,GAGxB,GAER,GACAb,EAAII,EAAea,MAAM,CAClBjB,KACHL,EAAEM,QAAQ,CAACiB,OAAO,CAACd,CAAc,CAACJ,EAAE,CAE5C,CAIA,SAASmB,EAA0BxB,CAAC,EAC5BA,EAAEc,OAAO,EAAId,EAAEI,IAAI,CAACqB,WAAW,EAC/BzB,EAAEI,IAAI,CAACsB,UAAU,CAACC,MAAM,CAACC,IAAI,CAAC,CAC1BC,KAAM7B,EAAEI,IAAI,CAACqB,WAAW,AAC5B,EAER,CAKA,SAASK,EAAoB9B,CAAC,EAC1B,IAAI,CAACE,KAAK,CAACN,SAAS,EAAEe,QAAQ,AAACf,IAC3BA,EAAUmC,MAAM,CAAC,CAAC,EAAG/B,EAAEgC,MAAM,CACjC,EACJ,CAKA,SAASC,IACD,CAAA,IAAI,CAAC/B,KAAK,CAACN,SAAS,EAAE0B,QACtB,IAAI,CAACY,YAAY,AAAD,GAChB,IAAI,CAACC,eAAe,EAE5B,CAKA,SAASC,IACL,IAAMC,EAAY,IAAI,CAACA,SAAS,CAC3BA,EAGIA,AAAmC,KAAnCA,EAAU9B,OAAO,CAAC,cACvB8B,EAAUpB,IAAI,CAAC,aAHf,IAAI,CAACoB,SAAS,CAAG,CAAC,YAAY,AAKtC,CAOA,SAASC,EAAgBC,CAAG,EACxB,IAAMlB,EAAQ,IAAI,CAAEmB,EAASD,EAAM,OAAS,MAC5ClB,CAAAA,EAAMP,OAAO,CAAGO,EAAMX,OAAO,CAACI,OAAO,CAAG2B,CAAAA,CAAQF,EAEhD,CAAC,UAAW,YAAY,CAAC5B,OAAO,CAAC,SAAU5C,CAAG,EACtCsD,CAAK,CAACtD,EAAI,EACVsD,CAAK,CAACtD,EAAI,CAACyE,EAAO,EAE1B,GACA,IAAI,CAACtB,MAAM,CAACwB,WAAW,EAC3B,CAQA,SAASC,IACL,IAAMzB,EAAS,IAAI,CAAEE,EAAS,IAAI,CAACwB,mBAAmB,GACtDC,EAAY,IAAI,CAACnC,OAAO,CAACmC,SAAS,CAAEjD,EAAY,IAAI,CAACA,SAAS,CAAEkD,EAAW,IAAI,CAACA,QAAQ,CACxF1B,EAAOT,OAAO,CAAC,AAACU,IACZ,IAAM0B,EAAQ1B,EAAM2B,iBAAiB,CAACF,GAAW3D,EAAQkC,EAAMX,OAAO,CAACvB,KAAK,EAAKkC,CAAAA,EAAM4B,MAAM,EAAI5B,AAAgB,OAAhBA,EAAM0B,KAAK,CACxGF,EACA,AAACjD,GAAa,AAAiB,KAAA,IAAVmD,EACjBnD,EAAUsD,OAAO,CAACH,EAAO1B,GACzBA,EAAMlC,KAAK,EAAI+B,EAAO/B,KAAK,AAAD,EAC9BA,GAASkC,EAAMlC,KAAK,GAAKA,IACzBkC,EAAMlC,KAAK,CAAGA,EACV+B,AAA8B,UAA9BA,EAAOR,OAAO,CAACS,UAAU,EACzBE,EAAMK,UAAU,EAChBL,EAAMK,UAAU,CAACyB,KAAK,EACtBjC,EAAOhB,KAAK,CAACkD,MAAM,CAACC,YAAY,CAAChC,EAAOA,EAAMP,OAAO,EAGjE,EACJ,CAkCA,SAASwC,IACL,IAAI,CAACC,IAAI,CAAC3B,IAAI,CAAC,OAAQzC,EAAM,IAAI,CAACqE,KAAK,EAAEC,OAAO,CAACtE,EAAM,IAAI,CAACuE,GAAG,EAAG,IAAI,CAACC,GAAG,EAAG,KAAK,EAAG,CAAA,EACzF,CAKA,SAASC,IACL,IAAI,CAACL,IAAI,CAAC3B,IAAI,CAAC,SAAUzC,EAAM,IAAI,CAACqE,KAAK,EAAEC,OAAO,CAACtE,EAAM,IAAI,CAACuE,GAAG,EAAG,IAAI,CAACC,GAAG,EAAG,KAAK,EAAG,CAAA,EAC3F,CA9LA3G,EAAqB6G,OAAO,CA1B5B,SAAiBC,CAAc,CAAEC,CAAU,CAAEC,CAAO,CAAEC,CAAW,CAAEC,CAAW,EAC1E,IAAMC,EAAaJ,EAAWxF,SAAS,CAAE6F,EAAUJ,EAAQzF,SAAS,CAAE8F,EAAcH,EAAY3F,SAAS,CACpG4F,EAAWG,qBAAqB,CAACC,QAAQ,CAAC,eAC3C9E,EAAuBqE,EACvBK,EAAWG,qBAAqB,CAACrD,IAAI,CAAC,aACtCkD,EAAWK,mBAAmB,CAAC5E,SAAS,CAAG,CACvCuE,EAAWM,YAAY,CAC1B,CACDrF,EAAS2E,EAAY,kBAAmBrE,GACxCgF,AAwKR,SAA6BX,CAAU,EACnC,IAAMY,EAAkBZ,EAAWxF,SAAS,CAACqG,UAAU,AACvDb,CAAAA,EAAWxF,SAAS,CAACqG,UAAU,CAAG,SAAUC,CAAI,CAAEnE,CAAO,EAErD,GAAImE,AAAS,cAATA,EACA,OAAOF,EAAgBG,KAAK,CAFlB,IAAI,CAEsBC,WAExC,IAAMC,EAAO,IAAIvF,EAJH,IAAI,CAI2BH,EAAMoB,EAAQsE,IAAI,CAAE,CAC7DC,MAAO/E,AALG,IAAI,AAKF,CAAC2E,EAAK,CAACvD,MAAM,CACzB4D,IAAK,CAAA,CACT,IAaA,OAZAhF,AARc,IAAI,CAQZiF,aAAa,CAAG,CAAA,EAEtBjF,AAVc,IAAI,CAUZkF,IAAI,CAACzE,OAAO,CAAC,AAACqE,IAChBA,EAAK9D,MAAM,CAAG,EAAE,AACpB,GACAhB,AAbc,IAAI,CAaZgB,MAAM,CAACP,OAAO,CAAC,AAACO,IAClBA,EAAOmE,QAAQ,GACfnE,EAAOoE,WAAW,CAAG,CAAA,CACzB,GACI/F,EAAKmB,EAAQsB,MAAM,CAAE,CAAA,IACrB9B,AAlBU,IAAI,CAkBR8B,MAAM,CAACtB,EAAQ6E,SAAS,EAE3BP,CACX,CACJ,EAjM4BjB,GACpBK,EAAQoB,UAAU,CAAGlC,EACrBc,EAAQqB,YAAY,CAAG7B,EACvBxE,EAAS6E,EAAa,mBAAoBlE,GAC1CX,EAAS6E,EAAa,oBAAqBzC,GAC3CpC,EAAS6E,EAAa,cAAenC,GACrCzC,EAAOgF,EAAa,CAChBqB,aAAc,YACdvD,gBAAiBQ,CACrB,GACAtD,EAAOgF,EAAYsB,UAAU,CAACpH,SAAS,CAAE,CACrCqH,WAAYtD,CAChB,GACAlD,EAAS8E,EAAa,iBAAkBjC,EAAwB,CAAE4D,MAAO,CAAE,GAC3EzG,EAAS8E,EAAa,WAAY9B,GAE1C,EA2HApF,EAAqBsF,eAAe,CAAGA,CAqE3C,EAAGtF,GAAyBA,CAAAA,EAAuB,CAAC,CAAA,GAMvB,IAAM8I,EAA8B9I,EAwe3D,CAAEkC,MAAO6G,CAAmB,CAAE,CAAI9G,IAElC,CAAEK,MAAO0G,CAAmB,CAAE,CAAInH,KAOxC,AAAC,SAAU5B,CAAa,EA8CpBA,EAAcgJ,eAAe,CA/B7B,SAAyBtG,CAAW,EAChC,IAAmBO,EAAQ8E,AAAd,IAAI,CAAe9E,KAAK,CAAEwB,EAAasD,AAAvC,IAAI,CAAwCtD,UAAU,CAAGsD,AAAzD,IAAI,CAA0DtD,UAAU,EAAI,CAAC,EAAGhB,EAAUsE,AAA1F,IAAI,CAA2FtE,OAAO,CAAEwF,EAAkBvG,EAAYkB,WAAW,EAAI,EAAE,CAChKsF,EAAWtF,EAAauF,EAAalG,EAAMQ,OAAO,CAACR,KAAK,CAACkG,UAAU,CAAEC,EAAe,EAAGC,CAC3FtB,CAFa,IAAI,CAEZnE,WAAW,CAAGA,EAAc,EAAE,CACnCa,EAAW6E,MAAM,CAAG,EAAE,CACtB,IAAK,IAAIlG,EAAI,EAAGmG,EAAON,EAAgB5E,MAAM,CAAEjB,EAAImG,EAAM,EAAEnG,EAEvD8F,EAAYH,EADZG,EAAYD,CAAe,CAAC7F,EAAE,EAE9BQ,EAAYI,IAAI,CAACkF,GACb,CAAA,AAACjG,EAAMuG,UAAU,GAAIN,EAAUhH,KAAK,AAAD,IAGnCuB,AAA2B,aAA3BA,EAAQgG,cAAc,EACjBxG,EAAMuG,UAAU,GAEjBL,EAAaE,AADbA,CAAAA,EAASpG,EAAMQ,OAAO,CAAC4F,MAAM,EAAI,EAAE,AAAD,EACdhF,MAAM,CAC1B6E,EAAUhH,KAAK,CAAGmH,CAAM,CAACD,EAAa,EAE1CF,EAAUQ,UAAU,CAAGN,EAGnBA,EAAAA,IAAiBD,GACjBC,CAAAA,EAAe,CAAA,GAInBF,EAAUhH,KAAK,CAAG4G,EAAoBrF,EAAQkG,QAAQ,EAAEnD,OAAO,CAACsC,EAAoBrF,EAAQmG,QAAQ,EAAGL,EAAO,EAAI,GAAMnG,EAAKmG,CAAAA,EAAO,CAAA,GAIhJ,EAeAvJ,EAAc6J,SAAS,CATvB,WACI,IAAmBpG,EAAUsE,AAAhB,IAAI,CAAiBtE,OAAO,CAAEqG,EAAQ/B,AAAtC,IAAI,CAAuC+B,KAAK,CAAGrG,EAAQqG,KAAK,EAAI,CAC7E,CAAC,EAAGrG,EAAQkG,QAAQ,EAAI,GAAG,CAC3B,CAAC,EAAGlG,EAAQmG,QAAQ,EAAI,GAAG,CAC9B,CACD,IAAK,IAAIxG,EAAI,EAAGmG,EAAOO,EAAMzF,MAAM,CAAEjB,EAAImG,EAAM,EAAEnG,EAC7C0G,CAAK,CAAC1G,EAAE,CAAClB,KAAK,CAAG4G,EAAoBgB,CAAK,CAAC1G,EAAE,CAAC,EAAE,CAExD,EAcApD,EAAc+J,eAAe,CAR7B,SAAyBjE,CAAK,EAC1B,IAAmBkE,EAAMjC,AAAZ,IAAI,CAAaiC,GAAG,EAAI,EAAGC,EAAMlC,AAAjC,IAAI,CAAkCkC,GAAG,EAAI,EAI1D,OAHIlC,AADS,IAAI,CACRmC,WAAW,EAChBpE,CAAAA,EAAQiC,AAFC,IAAI,CAEAmC,WAAW,CAACC,OAAO,CAACrE,EAAK,EAEnC,EAAK,AAACkE,CAAAA,EAAMlE,CAAI,EAClB,CAAA,AAACkE,EAAMC,GAAQ,CAAA,CACxB,EA4CAjK,EAAciG,OAAO,CAtCrB,SAAiBH,CAAK,CAAE1B,CAAK,EAEzB,IAEIsC,EAAK0D,EAAMC,EAAInI,EAAOgH,EAAW9F,EAF/BQ,EAAcmE,AADP,IAAI,CACQnE,WAAW,CAC9BkG,EAAQ/B,AAFD,IAAI,CAEE+B,KAAK,CAExB,GAAIlG,EAEA,CAAA,IADAR,EAAIQ,EAAYS,MAAM,CACfjB,KAIH,GAFAgH,EAAOlB,AADPA,CAAAA,EAAYtF,CAAW,CAACR,EAAE,AAAD,EACRgH,IAAI,CACrBC,EAAKnB,EAAUmB,EAAE,CACb,AAAC,CAAA,AAAgB,KAAA,IAATD,GAAwBtE,GAASsE,CAAG,GAC3C,CAAA,AAAc,KAAA,IAAPC,GAAsBvE,GAASuE,CAAC,EAAI,CAC5CnI,EAAQgH,EAAUhH,KAAK,CACnBkC,IACAA,EAAM8E,SAAS,CAAG9F,EAClBgB,EAAMsF,UAAU,CAAGR,EAAUQ,UAAU,EAE3C,KACJ,CACJ,KAEC,CAGD,IAFAhD,EAAMqB,AAtBG,IAAI,CAsBFgC,eAAe,CAACjE,GAC3B1C,EAAI0G,EAAMzF,MAAM,CACTjB,MACCsD,CAAAA,EAAMoD,CAAK,CAAC1G,EAAE,CAAC,EAAE,AAAD,IAIxBgH,EAAON,CAAK,CAAC1G,EAAE,EAAI0G,CAAK,CAAC1G,EAAI,EAAE,CAG/BsD,EAAM,EAAI,AAAC2D,CAAAA,AAFXA,CAAAA,EAAKP,CAAK,CAAC1G,EAAI,EAAE,EAAIgH,CAAG,CAEX,CAAC,EAAE,CAAG1D,CAAE,EAAM,CAAA,AAAC2D,CAAE,CAAC,EAAE,CAAGD,CAAI,CAAC,EAAE,EAAK,CAAA,EAChDlI,EAAQkI,EAAKlI,KAAK,CAACsE,OAAO,CAAC6D,EAAGnI,KAAK,CAAEwE,EACzC,CACA,OAAOxE,CACX,CAEJ,EAAGlC,GAAkBA,CAAAA,EAAgB,CAAC,CAAA,GAMT,IAAMsK,EAAuBtK,EAG1D,IAAIuK,EAA2HnK,EAAoB,KAC/IoK,EAA+IpK,EAAoBI,CAAC,CAAC+J,GAErKE,EAAmIrK,EAAoB,KACvJsK,EAAuJtK,EAAoBI,CAAC,CAACiK,GAiBjL,GAAM,CAAEE,eAAAA,CAAc,CAAE,CAAI/I,IAGtB,CAAEqC,OAAQ2G,CAAM,CAAE,CAAIF,IAEtB,CAAEG,QAAAA,CAAO,CAAEzI,OAAQ0I,CAAgB,CAAEC,UAAAA,CAAS,CAAEC,QAAAA,CAAO,CAAEC,SAAAA,CAAQ,CAAE5I,MAAO6I,CAAe,CAAE5I,KAAM6I,CAAc,CAAEC,eAAAA,CAAc,CAAE,CAAIxJ,GAC3I+I,CAAAA,EAAehI,SAAS,CAAGuI,EAAgBP,EAAeU,KAAK,CAlkBrC,CAwGtBC,UAAW,EAOXC,WAAY,EAgCZC,WAAY,EAWZC,cAAe,UAUfC,cAAe,EAgBfC,kBAAmB,GAOnBC,YAAa,CAAA,EAQbC,UAAW,CAAA,EAEXC,OAAQ,EAYRC,OAAQ,CAQJzD,UAAW,CAEP0D,SAAU,EACd,EAEAC,MAAO,IAOP/J,MAAO,SACX,EAUAoH,OAAQ,CACJ4C,SAAU,EAUVC,SAAU,UACVC,SAAU,CACd,EAmBAzC,SAAU,UAmBVC,SAAU,UAsBVyC,WAAY,EAkFZ1I,aAAc,CAAA,CAClB,EAyMA,OAAM2I,UAAmBxK,IAMrB,OAAO8E,QAAQE,CAAU,CAAEC,CAAO,CAAEC,CAAW,CAAEC,CAAW,CAAE,CAC1D4B,EAA2BjC,OAAO,CAAC0F,EAAWxF,EAAYC,EAASC,EAAaC,EACpF,CASAsF,YAAYtJ,CAAK,CAAEP,CAAW,CAAE,CAC5B,KAAK,CAACO,EAAOP,GACb,IAAI,CAAC8J,IAAI,CAAG,YACZ,IAAI,CAAC3I,OAAO,CAAG,CAAA,EACf,IAAI,CAAC4I,IAAI,CAACxJ,EAAOP,EACrB,CAiBA+J,KAAKxJ,CAAK,CAAEP,CAAW,CAAE,CAErB,IAAMyD,EAASlD,EAAMQ,OAAO,CAAC0C,MAAM,EAAI,CAAC,EAAGuG,EAAQhK,EAAYiK,MAAM,CACjEjK,AAAuB,aAAvBA,EAAYiK,MAAM,CAClBxG,AAAkB,aAAlBA,EAAOwG,MAAM,AACjB5E,CAJa,IAAI,CAIZ6E,IAAI,CAAGlK,EAAYkK,IAAI,EAAIF,EAAQ,EAAI,EAC5C3E,AALa,IAAI,CAKZ8E,QAAQ,CAAGnK,EAAYmK,QAAQ,EAAI,CAACH,EACzC3E,AANa,IAAI,CAMZ+E,QAAQ,CAAG,CAACJ,EACjB,KAAK,CAACD,KAAKxJ,EAAOP,EAAa,aAG/B,IAAI,CAACA,WAAW,CAAGA,EACfsI,EAAQ/H,EAAMP,WAAW,CAACC,SAAS,GACnCM,CAAAA,EAAMP,WAAW,CAACC,SAAS,CAAC,IAAI,CAACqF,KAAK,CAAC,CAAGtF,CAAU,EAGpDA,EAAYkB,WAAW,EACvBmE,AAhBS,IAAI,CAgBRiB,eAAe,CAACtG,GAEzBqF,AAlBa,IAAI,CAkBZ8B,SAAS,GAEd9B,AApBa,IAAI,CAoBZ2E,KAAK,CAAGA,EACb3E,AArBa,IAAI,CAqBZgF,WAAW,CAAG,CAAA,CACvB,CASAC,SAAU,CACN,MAAO,CAAC,CAAC,AAAC,CAAA,IAAI,CAACC,aAAa,EAAI,EAAE,AAAD,EAAG5I,MAAM,AAC9C,CAKA6I,kBAAmB,CACf,GAAI,CAAC,IAAI,CAACtJ,WAAW,CACjB,OAAO,KAAK,CAACsJ,kBAErB,CAKAC,WAAWzK,CAAW,CAAE,CACpB,IAAMe,EAAUyH,EAAgBP,EAAehI,SAAS,CAAED,EAE1D,CACI0K,UAAW,CAAA,EACXC,MAAO,KACPxJ,QAAS,IAAI,CAACZ,KAAK,CAACQ,OAAO,CAAC0C,MAAM,CAACmH,OAAO,EACtC5K,AAAwB,CAAA,IAAxBA,EAAYmB,OAAO,AAC3B,GACA,KAAK,CAACsJ,WAAW1J,GACjB,IAAI,CAACA,OAAO,CAAC8J,SAAS,CAAG,IAAI,CAAC9J,OAAO,CAACsI,MAAM,AAChD,CAIAyB,aAAc,CACV,IAAmBvK,EAAQ8E,AAAd,IAAI,CAAe9E,KAAK,CAAEyB,EAASqD,AAAnC,IAAI,CAAoCtD,UAAU,EAAEC,OAC7D,CAAEuH,MAAAA,CAAK,CAAEwB,OAAAA,CAAM,CAAE,CAAG1F,AADX,IAAI,CACY2F,OAAO,GAChChJ,IACA,IAAI,CAACiJ,IAAI,CAAG,CAACjJ,EAAOC,IAAI,CAAC,KACzB,IAAI,CAACiJ,GAAG,CAAG,CAAClJ,EAAOC,IAAI,CAAC,KACxB,IAAI,CAACsH,KAAK,CAAGA,EAAQ,CAACvH,EAAOC,IAAI,CAAC,SAClC,IAAI,CAAC8I,MAAM,CAAGA,EAAS,CAAC/I,EAAOC,IAAI,CAAC,UACpC,IAAI,CAACkJ,KAAK,CAAG5K,EAAM6K,UAAU,CAAG,IAAI,CAACH,IAAI,CAAG1B,EAC5C,IAAI,CAAC8B,MAAM,CAAG9K,EAAM+K,WAAW,CAAG,IAAI,CAACJ,GAAG,CAAGH,EAC7C,IAAI,CAAC/G,GAAG,CAAG,IAAI,CAACgG,KAAK,CAAG,IAAI,CAACiB,IAAI,CAAG,IAAI,CAACC,GAAG,EAIhD,IAAI,CAACK,GAAG,CAAG,AAAC,CAAA,IAAI,CAACvB,KAAK,CAAGT,EAAQwB,CAAK,GAClCnB,EAAU4B,mBAAmB,AACrC,CAMAC,WAAY,CAER,IAAMC,EAAQrG,AADD,IAAI,CACEtD,UAAU,EAAE2J,MACzBC,EAAatG,AAFN,IAAI,CAEO9E,KAAK,CAACqL,UAAU,CAACvG,AAF5B,IAAI,CAE6B6E,IAAI,CAAC,CACnD,GAAIwB,EAAO,CAGPrG,AANS,IAAI,CAMRwG,UAAU,CAAGH,EAElB,KAAK,CAACD,YACN,IAAMhI,EAAS,IAAI,CAAClD,KAAK,CAACkD,MAAM,CAGhCA,EAAO9C,QAAQ,CAACK,OAAO,CAAC,SAAUP,CAAI,EAC9BA,aAAgBmJ,GAChBnJ,EAAKqL,gBAAgB,CAACrI,EAAQhD,EAEtC,GACAgD,EAAOsI,MAAM,GACb,IAAI,CAACxL,KAAK,CAACyL,UAAU,CAAC,CAAA,GAElB,AAAC,IAAI,CAACzL,KAAK,CAACgB,MAAM,CAAC0K,IAAI,CAAC,AAAC1K,GAAWA,EAAO2K,UAAU,GACrD7G,CAAAA,AArBK,IAAI,CAqBJ8G,OAAO,CAAG,CAAA,CAAG,EAGjB9G,AAxBI,IAAI,CAwBH+G,KAAK,GACX/G,AAzBK,IAAI,CAyBJ+G,KAAK,CAAG,CAAA,EACb/G,AA1BK,IAAI,CA0BJgH,SAAS,CAAG,EACjBhH,AA3BK,IAAI,CA2BJiH,UAAU,CAAGjH,AA3Bb,IAAI,CA2BckE,KAAK,EAGhClE,AA9BS,IAAI,CA8BR9E,KAAK,CAACqL,UAAU,CAACvG,AA9Bb,IAAI,CA8Bc6E,IAAI,CAAC,CAAGyB,CACvC,CACJ,CAKAY,gBAAiB,CAEb,IAAMvC,EAAQ3E,AADD,IAAI,CACE2E,KAAK,CAClBG,EAAW9E,AAFJ,IAAI,CAEK8E,QAAQ,CACxBqC,EAAMrC,GAAAA,EACNsC,EAAOtC,EAAAA,EACPuC,EAAO1C,EAAQ,CAACwC,EAAK,EAAGC,EAAM,EAAE,CAAG,CAAC,EAAGA,EAAM,EAAGD,EAAI,AAC1DnH,CANa,IAAI,CAMZvD,WAAW,CAAG,CACf6K,eAAgB,CACZC,GAAIF,CAAI,CAAC,EAAE,CACXG,GAAIH,CAAI,CAAC,EAAE,CACXI,GAAIJ,CAAI,CAAC,EAAE,CACXK,GAAIL,CAAI,CAAC,EAAE,AACf,EACAtF,MAAO/B,AAbE,IAAI,CAaD+B,KAAK,AACrB,CACJ,CAKA0E,iBAAiBrI,CAAM,CAAEhD,CAAI,CAAE,CAC3B,IAAmBsB,EAAatB,EAAKsB,UAAU,EAAI,CAAC,EAAGiL,EAAUvJ,EAAOuJ,OAAO,CAAEC,EAAgBxJ,EAAO1C,OAAO,CAAEmM,EAAe7H,AAAnH,IAAI,CAAoHtE,OAAO,CAAC6F,MAAM,CAAEuG,EAAe1E,EAAewE,EAAcE,YAAY,CAAE,IAAKnD,EAAQ3E,AAA/M,IAAI,CAAgN2E,KAAK,CAAE,CAAET,MAAAA,CAAK,CAAEwB,OAAAA,CAAM,CAAE,CAAG1F,AAA/O,IAAI,CAAgP2F,OAAO,GAAIoC,EAAe3E,EAG3RwE,EAAcG,YAAY,CAAEpD,EAAQ,GAAK,IACzC,IAAI,CAACuC,cAAc,GAEf,AAACxK,EAAWC,MAAM,EAClBD,CAAAA,EAAWC,MAAM,CAAG,IAAI,CAACzB,KAAK,CAAC8M,QAAQ,CAACrL,MAAM,CAAC,eAC1CC,IAAI,CAAC,CACNqL,EAAGL,EAAcM,YAAY,EAAI,EACjCC,OAAQ,CACZ,GAAGC,GAAG,CAAC1L,EAAW2J,KAAK,CAAA,EAE3B3J,EAAWC,MAAM,CAACC,IAAI,CAAC,CACnByL,EAAG,EACHC,EAAG,AAAClK,CAAAA,EAAOmK,QAAQ,EAAI,CAAA,EAAK,GAC5BrE,MAAOA,EACPwB,OAAQA,CACZ,GAEAhJ,EAAW8L,UAAU,CAAItE,EACrByD,EACChD,CAAAA,EACGmD,EACA1E,EAAeyE,EAAaQ,CAAC,CAAER,EAAa1D,QAAQ,EAC/C,CAAA,IAAI,CAACsE,cAAc,EAAI,CAAA,CAAC,EACrC/L,EAAWgM,WAAW,CAAGhD,EAASiC,EAAWhD,CAAAA,EAAQoD,EAAe,CAAA,CACxE,CAKAY,SAASC,CAAK,CAAE,CACZ,IAAI,CAAC1M,MAAM,CAACP,OAAO,CAAC,SAAUO,CAAM,EAChCA,EAAOyM,QAAQ,CAACC,EACpB,EACJ,CAIAhI,YAAa,CACb,CAIAiI,mBAAoB,CAEhB,IAAM3M,EAAS8D,AADF,IAAI,CACG9D,MAAM,CACtB4M,EAAehL,EAAUiL,EAAoBC,EAAS3N,EAAIa,EAAOI,MAAM,CAG3E,IAFA,IAAI,CAAC2M,OAAO,CAAGC,IACf,IAAI,CAACC,OAAO,CAAG,CAACD,IACT7N,KAAK,CAMR,IAAK,IAAMtC,KAJX+E,EAAWkL,AADXA,CAAAA,EAAU9M,CAAM,CAACb,EAAE,AAAD,EACCyC,QAAQ,CAAGsF,EAAe4F,EAAQtN,OAAO,CAACoC,QAAQ,CAAEkL,EAAQlL,QAAQ,CAAEkL,EAAQI,WAAW,CAAEJ,EAAQK,QAAQ,CAAE,KAChIN,EAAqBC,CAAO,CAAClL,EAAW,MAAM,EAC1CkL,CAAO,CAAClL,EAAW,MAAM,CAEX,CAACA,EAAU,QAAS,IAAI,EAEtC,GAAIgL,AADJA,CAAAA,EAAgBE,EAAQM,SAAS,CAACvQ,EAAG,EACnBuD,MAAM,CACpB,MAIR,GAAIyM,EACAC,EAAQO,aAAa,CAAGP,CAAO,CAAClL,EAAW,MAAM,CACjDkL,EAAQQ,aAAa,CAAGR,CAAO,CAAClL,EAAW,MAAM,KAEhD,CACD,IAAM2L,EAAY5G,EAAOtJ,SAAS,CAACmQ,WAAW,CAACjQ,IAAI,CAACuP,EAASF,EAC7DE,CAAAA,EAAQO,aAAa,CAAGE,EAAUR,OAAO,CACzCD,EAAQQ,aAAa,CAAGC,EAAUN,OAAO,AAC7C,CACIrG,EAAQkG,EAAQO,aAAa,GAC7BzG,EAAQkG,EAAQQ,aAAa,IAC7B,IAAI,CAACP,OAAO,CACRU,KAAKzH,GAAG,CAAC,IAAI,CAAC+G,OAAO,CAAED,EAAQO,aAAa,EAChD,IAAI,CAACJ,OAAO,CACRQ,KAAK1H,GAAG,CAAC,IAAI,CAACkH,OAAO,CAAEH,EAAQQ,aAAa,GAEhD,AAACT,GACDlG,EAAOtJ,SAAS,CAACqQ,aAAa,CAACnQ,IAAI,CAACuP,EAE5C,CACJ,CAgBAa,cAAc7O,CAAC,CAAEqB,CAAK,CAAE,CACpB,IACIyN,EADepN,EAAasD,AAAnB,IAAI,CAAoBtD,UAAU,EAAI,CAAC,EAAGqN,EAAQ1N,GAAO0N,MAAOC,EAAQ3N,GAAO2N,MAAOC,EAAUjK,AAAhG,IAAI,CAAiGrB,GAAG,CAAEuL,EAAUlK,AAApH,IAAI,CAAqHkG,GAAG,CAErI7J,IAEIyN,AADJA,CAAAA,EAAW9J,AAHF,IAAI,CAGGmK,QAAQ,CAAC9N,EAAM2B,iBAAiB,CAAC3B,EAAMH,MAAM,CAAC4B,QAAQ,EAAC,EACxDmM,EACXH,EAAWG,EAAU,EAEhBH,EAAWG,EAAUC,GAC1BJ,CAAAA,EAAWG,EAAUC,EAAU,CAAA,EAEnC7N,EAAM0N,KAAK,CAAGD,EACdzN,EAAM2N,KAAK,CAAGhK,AAXL,IAAI,CAWMkG,GAAG,CAAG4D,EACzB,KAAK,CAACD,cAAc7O,EAAGqB,GACvBA,EAAM0N,KAAK,CAAGA,EACd1N,EAAM2N,KAAK,CAAGA,EACVhK,AAfK,IAAI,CAeJoK,KAAK,EACV,CAACpK,AAhBI,IAAI,CAgBHoK,KAAK,CAACC,gBAAgB,EAC5B3N,EAAW2J,KAAK,GAChBrG,AAlBK,IAAI,CAkBJoK,KAAK,CACLE,QAAQ,CAAC,+BACTlC,GAAG,CAAC1L,EAAW2J,KAAK,EACzBrG,AArBK,IAAI,CAqBJoK,KAAK,CAACC,gBAAgB,CAAG,CAAA,EAC1B,AAACrK,AAtBA,IAAI,CAsBC9E,KAAK,CAACuG,UAAU,EACtB,AAA0B,UAA1B,OAAOzB,AAvBN,IAAI,CAuBOwF,SAAS,EACrBxF,AAxBC,IAAI,CAwBAoK,KAAK,CAACxN,IAAI,CAAC,CACZC,KAAMmD,AAzBT,IAAI,CAyBUwF,SAAS,CAACrL,KAAK,AAC9B,IAIhB,CAIAoQ,gBAAgB7O,CAAO,CAAE,CACrB,IAAmBkK,EAAO5F,AAAb,IAAI,CAAc4F,IAAI,CAAEjH,EAAMjD,EAAQ8O,eAAe,CAAE3E,EAAM7F,AAA7D,IAAI,CAA8D6F,GAAG,CAElF,OAAO3C,EAASvE,GACXqB,AAHQ,IAAI,CAGP2E,KAAK,CAAG,CACV,CAAC,IAAKhG,EAAM,EAAGkH,EAAM,EAAE,CACvB,CAAC,IAAKlH,EAAM,EAAGkH,EAAM,EAAE,CACvB,CAAC,IAAKlH,EAAKkH,EAAI,CACf,CAAC,IAAI,CACR,CAAG,CACA,CAAC,IAAKD,EAAMjH,EAAI,CAChB,CAAC,IAAKiH,EAAO,EAAGjH,EAAM,EAAE,CACxB,CAAC,IAAKiH,EAAO,EAAGjH,EAAM,EAAE,CACxB,CAAC,IAAI,CACR,CACD,KAAK,CAAC4L,gBAAgB7O,EAC9B,CAiBAqB,OAAO0N,CAAU,CAAEzN,CAAM,CAAE,CACvB,IAAuCoB,EAASlD,AAArB8E,AAAd,IAAI,CAAe9E,KAAK,CAAiBkD,MAAM,CAC5D,IAAI,CAAClC,MAAM,CAACP,OAAO,CAAC,AAACO,IAEjBA,EAAOoE,WAAW,CAAG,CAAA,CACzB,GAGImK,CAAAA,EAAW5O,WAAW,EAAIuC,EAAO9C,QAAQ,EAAI0E,AAPpC,IAAI,CAOqCnE,WAAW,AAAD,GAC5DmE,AARS,IAAI,CAQR0K,YAAY,GAErB,KAAK,CAAC3N,OAAO0N,EAAYzN,GACrBgD,AAXS,IAAI,CAWRtD,UAAU,EAAEyB,QACjB6B,AAZS,IAAI,CAYRkH,cAAc,GACnB9I,EAAOC,YAAY,CAAC,IAAI,CAAE,CAAA,GAElC,CAKAqM,cAAe,CACX,IAAmBxP,EAAQ8E,AAAd,IAAI,CAAe9E,KAAK,CAAEwB,EAAasD,AAAvC,IAAI,CAAwCtD,UAAU,EAAI,CAAC,EACxE,GAAIA,EAAWyB,KAAK,CAChBjD,EAAMkD,MAAM,CAACjD,WAAW,CAFf,IAAI,OAIZ,GAAIuB,EAAW6E,MAAM,CACtB,IAAK,IAAMnG,KAAQsB,EAAW6E,MAAM,CAChCrG,EAAMkD,MAAM,CAACjD,WAAW,CAACC,EAGjCF,CAAAA,EAAMiF,aAAa,CAAG,CAAA,CAC1B,CAEAwK,SAAU,CACN,IAAI,CAACzP,KAAK,CAACiF,aAAa,CAAG,CAAA,EAC3B,IAAI,CAACuK,YAAY,GACjB,KAAK,CAACC,WAAW,EAAE,CAACC,KAAK,CAACnR,IAAI,CAACsG,WACnC,CASA8K,OAAO7N,CAAM,CAAE,CACX,IAAI,CAAC0N,YAAY,GACjB,KAAK,CAACG,OAAO7N,EACjB,CAKAhB,2BAA4B,CACxB,IAOI8O,EAPE9K,EAAO,IAAI,CAAE9E,EAAQ8E,EAAK9E,KAAK,CAAE6P,EAAe/K,EAAKtD,UAAU,EACjEsD,EAAKtD,UAAU,CAAC6E,MAAM,EACtB,EAAE,CAAGqG,EAAgB1M,EAAMQ,OAAO,CAAC0C,MAAM,CAAE4M,EAAgB5H,EAAewE,EAAcoD,aAAa,CAAE,IAAKC,EAAc7H,EAAewE,EAAcqD,WAAW,CAAE,IAClKC,EAAuB,AAAC7P,GAAM2E,EAAK9D,MAAM,CAACiP,MAAM,CAAC,CAAC/O,EAAQgP,KAC5DhP,EAAOH,IAAI,IAAImP,EAAEhP,MAAM,CAACiP,MAAM,CAAC,AAAChP,GAAUA,EAAM8E,SAAS,GAAK9F,IACvDe,GACR,EAAE,EA2DL,OAzDI,AAAC2O,EAAYzO,MAAM,EACnB0D,EAAKnE,WAAW,CAACF,OAAO,CAAC,CAACwF,EAAW9F,KACjC,IAAMgH,EAAOlB,EAAUkB,IAAI,CAAEC,EAAKnB,EAAUmB,EAAE,CAAE,CAAEgJ,gBAAAA,CAAe,CAAE,CAAGpQ,EAClEqC,EAAM,CAAA,EAGVuN,EAAO,GACH,AAAgB,KAAA,IAATzI,EACPyI,EAAO,KAEF,AAAc,KAAA,IAAPxI,GACZwI,CAAAA,EAAO,IAAG,EAEV,AAAgB,KAAA,IAATzI,GACPyI,CAAAA,GAAQQ,EAAgBjJ,EAAM2I,GAAiBC,CAAU,EAEzD,AAAgB,KAAA,IAAT5I,GAAwB,AAAc,KAAA,IAAPC,GACtCwI,CAAAA,GAAQ,KAAI,EAEZ,AAAc,KAAA,IAAPxI,GACPwI,CAAAA,GAAQQ,EAAgBhJ,EAAI0I,GAAiBC,CAAU,EAG3DF,EAAY9O,IAAI,CAAC8G,EAAiB,CAC9B7H,MAAAA,EACA4P,KAAAA,EACApP,QAAS,CAAC,EACV+K,iBAAkB,AAAChE,IAAmI8I,SAAS,CAC/JzP,QAAS,CAAA,EACT0P,YAAa,CAAA,EAGb7C,SAAU,AAACC,IACP,IAAK,IAAMvM,KAAS6O,EAAqB7P,GACrCgB,EAAMsM,QAAQ,CAACC,EAEvB,EAGAhI,WAAY,WACR,IAAI,CAAC9E,OAAO,CAAGyB,EAAMyC,EAAKlE,OAAO,CAAG,CAACyB,EACrC,IAAMkO,EAAiB,EAAE,CACzB,IAAK,IAAMpP,KAAS6O,EAAqB7P,GACrCgB,EAAMuE,UAAU,CAACrD,GACjBlB,EAAMqP,iBAAiB,CAAG,CAACnO,EACvBkO,AAAyC,KAAzCA,EAAelQ,OAAO,CAACc,EAAMH,MAAM,GACnCuP,EAAexP,IAAI,CAACI,EAAMH,MAAM,EAGxChB,EAAMkD,MAAM,CAACC,YAAY,CAAC,IAAI,CAAEd,GAChCkO,EAAe9P,OAAO,CAAC,AAACO,IACpB8G,EAAU9G,EAAQ,4BACtB,EACJ,CACJ,EAAGiF,GACP,GAEG4J,CACX,CAKApF,SAAU,CACN,GAAmB,CAAEzK,MAAAA,CAAK,CAAEyJ,MAAAA,CAAK,CAAE,CAAtB,IAAI,CAA2B,CAAEe,OAAQiG,CAAe,CAAEzH,MAAO0H,CAAc,CAAE,CAAG5L,AAApF,IAAI,CAAqFtE,OAAO,CAAE,CAAE0C,OAAQwJ,CAAa,CAAE,CAAG1M,EAAMQ,OAAO,CAGxJ,MAAO,CACHwI,MAJ8Jd,EAAeN,EAAQ8I,GACrLvI,EAAeuI,EAAgB1Q,EAAM6K,UAAU,EAAI,KAAK,EAAG6B,GAAeiE,YAAalH,EAAQJ,EAAU4B,mBAAmB,CAAG,IAI/HT,OAJ6ItC,EAAeN,EAAQ6I,GACpKtI,EAAesI,EAAiBzQ,EAAM+K,WAAW,EAAI,KAAK,EAAG2B,GAAekE,aAAcnH,EAAQ,GAAKJ,EAAU4B,mBAAmB,CAIxI,CACJ,CACJ,CAMA5B,EAAU4B,mBAAmB,CAAG,IAIhC5B,EAAUwH,SAAS,CAAG,CAClB,aACH,CACDhJ,EAAiBwB,EAAUhL,SAAS,CAAEgJ,GAOtCyJ,MAAMzS,SAAS,CAAC0C,IAAI,CAAC6D,KAAK,CAAC,AAAC/F,IAAmGgS,SAAS,CAAExH,EAAUwH,SAAS,EAwB7J,IAAME,EAAKpS,GACXoS,CAAAA,EAAE1H,SAAS,CAAG0H,EAAE1H,SAAS,EAnB6BA,EAoBtD0H,EAAE1H,SAAS,CAAC1F,OAAO,CAACoN,EAAEC,KAAK,CAAED,EAAEE,EAAE,CAAEF,EAAEG,MAAM,CAAEH,EAAEpJ,MAAM,EAIrD,IAAIwJ,EAAmHhU,EAAoB,IACvIiU,EAAuIjU,EAAoBI,CAAC,CAAC4T,GAajK,GAAM,CAAEE,OAAQ,CAAEhT,UAAWiT,CAAW,CAAE,CAAE,CAAG,AAAC7J,IAA2I8J,WAAW,CAGhM,CAAErS,SAAUsS,CAA4B,CAAE5J,QAAS6J,CAA2B,CAAE,CAAI9S,KAO1F,AAAC,SAAU3B,CAAmB,EAsC1B,SAAS0U,EAAqB5R,CAAC,EAC3B,IAAoBkB,EAASG,AAAf,IAAI,CAAiBH,MAAM,CAAE8L,EAAW9L,EAAOhB,KAAK,CAAC8M,QAAQ,AACvE3L,CADU,IAAI,CACRwQ,gBAAgB,EAAIxQ,AADhB,IAAI,CACkByQ,OAAO,GACnC,AAAC5Q,EAAO6Q,kBAAkB,EAI1B7Q,CAAAA,EAAO6Q,kBAAkB,CAAG,GAAKT,CAAAA,GAAwH,EAAGtE,EAAU,OACjKgF,GAAG,CAAC,CACLC,cAAe,MACnB,GACK7E,GAAG,CAAC/L,AAVH,IAAI,CAUKyQ,OAAO,CAACI,WAAW,CAAA,EAElClS,GAAG4N,QAAU,SAGbvM,AAfM,IAAI,CAeJyQ,OAAO,CAAClQ,IAAI,CAAC,CACfuQ,GAAI,IAAI,CAACA,EAAE,AACf,GACAjR,EAAO6Q,kBAAkB,CAACnQ,IAAI,CAAC,CAC3BwQ,KAAM,CAAC,EAAEpF,EAASqF,GAAG,CAAC,CAAC,EAAE,IAAI,CAACF,EAAE,CAAC,CAAC,CAClCG,WAAY,SAChB,IAGApR,EAAO6Q,kBAAkB,CAACnQ,IAAI,CAAC,CAC3BwQ,KAAM,EACV,GAGZ,CA9DAlV,EAAoBqV,YAAY,CAAG,CAC/BC,gBAAiB,CAAA,EACjBX,iBAAkB,CAAA,EAClBY,QAiEJ,WACI,OAAQ,AAAe,OAAf,IAAI,CAAC1P,KAAK,EACd,IAAI,CAACA,KAAK,GAAKmL,KACf,IAAI,CAACnL,KAAK,GAAK,CAACmL,KAEf,CAAA,AAAe,KAAK,IAApB,IAAI,CAACnL,KAAK,EAAe,CAAC2P,MAAM,IAAI,CAAC3P,KAAK,CAAA,CACnD,CAtEA,EACA7F,EAAoByV,aAAa,CAAG,CAChC7P,SAAU,QACVT,UAAW,CAAC,QAAS,QAAS,YAAY,CAC1CuQ,eAAgB,CAAC,IAAK,IAAK,QAAQ,CACnCC,cAAe,CAAC,QAAQ,CACxBC,cAAe,CAAC,QAAS,cAAe,kBAAkB,CAC1D5Q,aAwEJ,SAA4Bb,CAAK,EAC7B,IAAM0R,EAAM,CAAC,EAMb,OALIpB,EAA4BtQ,EAAMlC,KAAK,GACtC,CAAA,CAACkC,EAAMuM,KAAK,EAAIvM,AAAgB,WAAhBA,EAAMuM,KAAK,AAAY,GAExCmF,CAAAA,CAAG,CAAC,IAAI,CAACC,SAAS,EAAI,OAAO,CAAG3R,EAAMlC,KAAK,AAAD,EAEvC4T,CACX,EA/EIE,aAAczB,EAAYyB,YAAY,AAC1C,EAcA/V,EAAoB2G,OAAO,CAL3B,SAAiBK,CAAW,EAGxB,OADAwN,EADmBxN,EAAY3F,SAAS,CAACoH,UAAU,CACV,gBAAiBiM,GACnD1N,CACX,CAkEJ,EAAGhH,GAAwBA,CAAAA,EAAsB,CAAC,CAAA,GAMrB,IAAMgW,EAA8BhW,EAc3D,CAAEiW,QAAS,CAAE5U,UAAW,CAAEoH,WAAYyN,CAAY,CAAE,CAAE,CAAE,CAAG,AAACzL,IAA2I8J,WAAW,CAElN,CAAE4B,MAAAA,CAAK,CAAEvL,QAASwL,EAAoB,CAAEjU,OAAQkU,EAAmB,CAAEhU,KAAMiU,EAAiB,CAAE,CAAI3U,GAMxG,OAAM4U,WAAqBL,EAOvBM,aAAahT,CAAO,CAAE2M,CAAC,CAAE,CAQrB,MANI,CAAA,IAAI,CAACpK,MAAM,EAAI,AAAe,OAAf,IAAI,CAACF,KAAK,AAAQ,GACjC,OAAO,IAAI,CAAC5D,KAAK,CAErB,KAAK,CAACuU,aAAahT,EAAS2M,GAC5B,IAAI,CAACsG,YAAY,CAAG,IAAI,CAAC1Q,MAAM,EAAI,AAAe,OAAf,IAAI,CAACF,KAAK,CACzC,OAAS,QACN,IAAI,AACf,CAEA6Q,mBAAoB,CAChB,IAAoB1S,EAASG,AAAf,IAAI,CAAiBH,MAAM,CAAE2S,EAAgB3S,EAAOR,OAAO,CAAEoT,EAAO,AAACD,CAAAA,EAAcE,OAAO,EAAI,CAAA,EAAK,EAAGC,EAAO,AAACH,CAAAA,EAAcI,OAAO,EAAI,CAAA,EAAK,EAAG3L,EAAQpH,EAAOoH,KAAK,CAAE4L,EAAQhT,EAAOgT,KAAK,CAAEC,EAAgB9S,AAA3M,IAAI,CAA6MX,OAAO,CAACsI,MAAM,EAAI9H,EAAOR,OAAO,CAACsI,MAAM,CAAEoL,EAAiBlT,EAAOmT,sBAAsB,GACtTC,EAAed,GAAkBnS,AADnB,IAAI,CACqBiT,YAAY,CAAET,EAAcS,YAAY,CAAE,GAAIC,EAAW,CAC5FhI,GAAI8G,EAAM1E,KAAK6F,KAAK,CAAClM,EAAM4C,GAAG,CAC1B5C,EAAMmM,SAAS,CAACpT,AAHV,IAAI,CAGYgM,CAAC,CAAGyG,EAAM,CAAA,EAAO,CAAA,EAAM,CAAA,EAAO,CAAA,EAAM,CAACM,IAAkB,CAAC9L,EAAM4C,GAAG,CAAE,EAAI5C,EAAM4C,GAAG,EAC1GuB,GAAI4G,EAAM1E,KAAK6F,KAAK,CAAClM,EAAM4C,GAAG,CAC1B5C,EAAMmM,SAAS,CAACpT,AALV,IAAI,CAKYgM,CAAC,CAAGyG,EAAM,CAAA,EAAO,CAAA,EAAM,CAAA,EAAO,CAAA,EAAM,CAACM,IAAkB,CAAC9L,EAAM4C,GAAG,CAAE,EAAI5C,EAAM4C,GAAG,EAC1GsB,GAAI6G,EAAM1E,KAAK6F,KAAK,CAACN,EAAMO,SAAS,CAACpT,AAN3B,IAAI,CAM6BiM,CAAC,CAAG0G,EAAM,CAAA,EAAO,CAAA,EAAM,CAAA,EAAO,CAAA,IAAQ,CAACE,EAAMhJ,GAAG,CAAE,EAAIgJ,EAAMhJ,GAAG,EAC1GwB,GAAI2G,EAAM1E,KAAK6F,KAAK,CAACN,EAAMO,SAAS,CAACpT,AAP3B,IAAI,CAO6BiM,CAAC,CAAG0G,EAAM,CAAA,EAAO,CAAA,EAAM,CAAA,EAAO,CAAA,IAAQ,CAACE,EAAMhJ,GAAG,CAAE,EAAIgJ,EAAMhJ,GAAG,CAC9G,EAIA,IAAK,IAAMwJ,IAHQ,CAAC,CAAC,QAAS,IAAI,CAAE,CAAC,SAAU,IAAI,CAAC,CAGhB,CAChC,IAAMpW,EAAOoW,CAAS,CAAC,EAAE,CAAEC,EAAYD,CAAS,CAAC,EAAE,CAC/ClR,EAAQmR,EAAY,IAAKjR,EAAMiR,EAAY,IACzC9K,EAAO8E,KAAKiG,GAAG,CAACL,CAAQ,CAAC/Q,EAAM,CAAG+Q,CAAQ,CAAC7Q,EAAI,EAAGmR,EAAcV,GAClEA,EAAc5L,SAAS,EAAI,EAAGuM,EAAUnG,KAAKiG,GAAG,CAACL,CAAQ,CAAC/Q,EAAM,CAAG+Q,CAAQ,CAAC7Q,EAAI,EAAI,EAAGqR,EAAgBZ,GAAiBA,CAAa,CAAC7V,EAAK,CAC/I,GAAIgV,GAAqByB,IAAkBA,EAAgBlL,EAAM,CAC7D,IAAMmL,EAAeD,EAAgB,EAAIF,EAAc,CACvDN,CAAAA,CAAQ,CAAC/Q,EAAM,CAAGsR,EAAUE,EAC5BT,CAAQ,CAAC7Q,EAAI,CAAGoR,EAAUE,CAC9B,CAEIV,IACI,CAAA,AAAe,MAAdK,GAAqBrM,EAAMwB,QAAQ,EACnC6K,AAAc,MAAdA,GAAqB,CAACT,EAAMpK,QAAQ,IACrCtG,EAAQE,EACRA,EAAMiR,EAAY,KAEtBJ,CAAQ,CAAC/Q,EAAM,EAAI8Q,EACnBC,CAAQ,CAAC7Q,EAAI,EAAI4Q,EAEzB,CACA,OAAOC,CACX,CAIAU,SAASC,CAAI,CAAE,CACX,GAAI,CAACA,EACD,MAAO,EAAE,CAEb,GAAM,CAAE7H,EAAAA,EAAI,CAAC,CAAEC,EAAAA,EAAI,CAAC,CAAEpE,MAAAA,EAAQ,CAAC,CAAEwB,OAAAA,EAAS,CAAC,CAAE,CAAG,IAAI,CAACyK,SAAS,EAAI,CAAC,EACnE,MAAO,CACH,CAAC,IAAK9H,EAAI6H,EAAM5H,EAAI4H,EAAK,CACzB,CAAC,IAAK7H,EAAI6H,EAAM5H,EAAI5C,EAASwK,EAAK,CAClC,CAAC,IAAK7H,EAAInE,EAAQgM,EAAM5H,EAAI5C,EAASwK,EAAK,CAC1C,CAAC,IAAK7H,EAAInE,EAAQgM,EAAM5H,EAAI4H,EAAK,CACjC,CAAC,IAAI,CACR,AACL,CAMAzC,SAAU,CAEN,OAAQ,IAAI,CAAC1P,KAAK,GAAKmL,KACnB,IAAI,CAACnL,KAAK,GAAK,CAACmL,GACxB,CACJ,CACAqF,GAAoBE,GAAalV,SAAS,CAAE,CACxCiU,gBAAiB,CAAA,EACjBX,iBAAkB,CAAA,EAClBuD,QAAS,CAAA,CACb,GAoBA,GAAM,CAAElN,SAAUmN,EAA8B,CAAE,CAAIxW,IAqnBtD,IAAIyW,GAAuHjY,EAAoB,KAC3IkY,GAA2IlY,EAAoBI,CAAC,CAAC6X,IAarK,GAAM,CAAEE,IAAAA,EAAG,CAAE,CAAI3W,IAEX,CAAEiJ,QAAS2N,EAA8B,CAAElW,KAAMmW,EAA2B,CAAE,CAAI7W,IA4ElF,CAAEqC,OAAQyU,EAAoB,CAAElE,YAAa,CAAEF,OAAQqE,EAAY,CAAEzC,QAAS0C,EAAa,CAAE,CAAE,CAAIlO,IAEnG,CAAEpJ,UAAW,CAAEuX,QAAAA,EAAO,CAAE,CAAE,CAAIP,KAE9B,CAAEnW,SAAU2W,EAAsB,CAAE1W,OAAQ2W,EAAoB,CAAEhO,UAAWiO,EAAuB,CAAE/N,SAAUgO,EAAsB,CAAE5W,MAAO6W,EAAmB,CAAE5W,KAAM6W,EAAkB,CAAE,CAAIvX,IAElM,CAAEwX,eAAgBC,EAA4B,CAAEC,WAAYC,EAAwB,CAAE,CA5B7D,CAC3BH,eAnCJ,SAAwBtT,CAAK,CAAE1B,CAAK,EAChC,IAAMzB,EAAYyB,EAAMH,MAAM,CAACtB,SAAS,CACxC,GAAIA,EAAW,CACX,IAAM6W,EAAQ7W,EAAUsD,OAAO,CAACH,GAAS,EAAG1B,GACvCqV,KAAK,CAAC,IAAI,CAAC,EAAE,CACbA,KAAK,CAAC,IAAI,CAAC,EAAE,CACbA,KAAK,CAAC,KACN7W,GAAG,CAAC,AAACuQ,GAAMsF,GAA4BiB,WAAWvG,GAAIwG,SAASxG,EAAG,MAKvE,OAJAqG,CAAI,CAAC,EAAE,CAAGf,AAA4C,IAA5CA,GAA4Be,CAAI,CAAC,EAAE,CAAE,GAC3C,AAAChB,GAA+B1S,IAAW1B,EAAMP,OAAO,EACxD2V,CAAAA,CAAI,CAAC,EAAE,CAAG,CAAA,EAEPA,CACX,CACA,MAAO,CAAC,EAAG,EAAG,EAAG,EAAE,AACvB,EAqBIF,WAhBJ,SAAoBrV,CAAM,EACtB,GAAM,CAAE2V,OAAAA,CAAM,CAAEC,QAAAA,CAAO,CAAE,CAAG5V,SAC5B,AAAI2V,GAAUC,GACVA,EAAQC,SAAS,CAAC,EAAG,EAAGF,EAAO3N,KAAK,CAAE2N,EAAOnM,MAAM,EAShDoM,IANH5V,EAAO2V,MAAM,CAAGrB,GAAIwB,aAAa,CAAC,UAClC9V,EAAO4V,OAAO,CAAG5V,EAAO2V,MAAM,CAACN,UAAU,CAAC,KAAM,CAC5CU,mBAAoB,CAAA,CACxB,IAAM,KAAK,EACJ/V,EAAO4V,OAAO,CAG7B,CAIA,CAsCA,OAAMI,WAAsBrB,GACxBrM,aAAc,CAMV,KAAK,IAAIzE,WACT,IAAI,CAACoS,QAAQ,CAAGC,IAChB,IAAI,CAACC,QAAQ,CAAGD,IAChB,IAAI,CAACE,aAAa,CAAG,CAAA,CAEzB,CASAC,YAAa,CACT,IAAMrW,EAAS,IAAI,CAAE2S,EAAgB3S,EAAOR,OAAO,CAAE8W,EAAgB3D,EAAc2D,aAAa,CAAEC,EAAsB5D,EAAc7K,MAAM,EAAI,CAAC,EACjJ,GAAIwO,EAAe,CACf,GAAM,CAAEE,MAAAA,CAAK,CAAExX,MAAAA,CAAK,CAAEoI,MAAAA,CAAK,CAAE4L,MAAAA,CAAK,CAAE,CAAGhT,EAAQ,CAAE4I,SAAU6N,EAAO,CAAA,CAAK,CAAEzM,IAAKhC,CAAK,CAAE,CAAGZ,EAAO,CAAEwB,SAAU8N,EAAO,CAAA,CAAK,CAAE1M,IAAKR,CAAM,CAAE,CAAGwJ,EAAO2D,EAAa,CAAE3O,MAAAA,EAAOwB,OAAAA,CAAO,EAC7K,GAAI,CAACgN,GAASxW,EAAOoE,WAAW,EAAIpE,EAAOoW,aAAa,CAAE,CACtD,IAAMQ,EAAMtB,GAAyBtV,GAAS,CAAE2V,OAAAA,CAAM,CAAEnW,QAAS,CAAEqT,QAAAA,EAAU,CAAC,CAAEE,QAAAA,EAAU,CAAC,CAAE,CAAE7S,OAAAA,CAAM,CAAEA,OAAQ,CAAEE,OAAAA,CAAM,CAAE,CAAE,CAAGJ,EAAgCtB,EAAaM,EAAMN,SAAS,EAAIM,EAAMN,SAAS,CAAC,EAAE,CAChN,GAAIiX,GAAUiB,GAAOlY,EAAW,CAC5B,GAAM,CAAEsH,IAAK6Q,CAAI,CAAE9Q,IAAK+Q,CAAI,CAAE,CAAG1P,EAAMoG,WAAW,GAAI,CAAExH,IAAK+Q,CAAI,CAAEhR,IAAKiR,CAAI,CAAE,CAAGhE,EAAMxF,WAAW,GAAIyJ,EAASH,EAAOD,EAAMK,EAASF,EAAOD,EAAyBI,EAAQ1J,KAAK6F,KAAK,CAAC8D,AAAgBH,EAASpE,EAAjD,EAAA,GAA2EwE,EAAQ5J,KAAK6F,KAAK,CAAC8D,AAAgBF,EAASnE,EAAvH,EAAA,GAAiJ,CAACuE,EAAYC,EAAW,CAAG,CACxU,CAACJ,EAAOA,EAAQF,EAAQR,EAAM,OAAO,CACrC,CAACY,EAAOA,EAAQH,EAAQ,CAACR,EAAM,QAAQ,CAC1C,CAAC/X,GAAG,CAAC,CAAC,CAAC6Y,EAAMC,EAAOC,EAAKC,EAAS,GAAMD,EACrC,AAACE,GAAOnK,IAAI,CAACkK,EAAS,CAACH,EAClBC,EAASG,GACd,AAACA,GAAOnK,IAAI,CAACkK,EAAS,CAACF,EAAQG,IAAOC,EAAclC,EAAO3N,KAAK,CAAGmP,EAAQ,EAA6CW,EAAaD,EAAxClC,CAAAA,EAAOnM,MAAM,CAAG6N,EAAQ,CAAA,EAA4CU,EAAoBC,AAR3C5X,CAAAA,EAAS,CAAA,EAQ8C0X,EAAYG,EAAY,IAAIC,kBAAkBJ,AAAa,EAAbA,GAAiBK,EAAgB,CAAChM,EAAGC,IAAOqB,AACnQ,EADmQA,KAAK2K,IAAI,CAAC,AAACP,EAAcN,EAAWnL,EAAI2K,GACnUO,EAAWnL,EAAI0K,IACnB7W,EAAOwB,WAAW,GAClB,IAAK,IAAIrC,EAAI,EAAGA,EAAI2Y,EAAY3Y,IAAK,CACjC,IAAMgB,EAAQD,CAAM,CAACuN,KAAK2K,IAAI,CAACL,EAAoB5Y,GAAG,CAAE,CAAEgN,EAAAA,CAAC,CAAEC,EAAAA,CAAC,CAAE,CAAGjM,EACnE8X,EAAUI,GAAG,CAACjD,GAA6BjV,EAAM0B,KAAK,CAAE1B,GAAQgY,EAAchM,EAAGC,GACrF,CACAwK,EAAI0B,YAAY,CAAC,IAAIC,UAAUN,EAAWJ,GAAc,EAAG,GACvDrB,EACAA,EAAM9V,IAAI,CAAC,CACP,GAAGiW,CAAU,CACbzF,KAAMyE,EAAO6C,SAAS,CAAC,YAAa,EACxC,IAGAxY,EAAOyY,WAAW,CAAG,CAAA,EACrBzY,EAAOwW,KAAK,CAAGxX,EAAM8M,QAAQ,CAAC0K,KAAK,CAACb,EAAO6C,SAAS,CAAC,YAAa,IAC7D9X,IAAI,CAACiW,GACLzK,GAAG,CAAClM,EAAOmK,KAAK,EAE7B,CACAnK,EAAOoW,aAAa,CAAG,CAAA,CAC3B,KACSI,CAAAA,EAAMxO,KAAK,GAAKA,GAASwO,EAAMhN,MAAM,GAAKA,CAAK,GACpDgN,EAAM9V,IAAI,CAACiW,EAEnB,KACSJ,CAAAA,EAAoBlN,OAAO,EAAIrJ,EAAO0Y,gBAAgB,AAAD,IAC1DjE,GAAqBpX,SAAS,CAACgZ,UAAU,CAAC9Y,IAAI,CAACyC,GAC/CA,EAAOE,MAAM,CAACT,OAAO,CAAC,AAACU,IACfA,EAAMyQ,OAAO,GAIbzQ,EAAMyQ,OAAO,CAAC5Q,EAAOhB,KAAK,CAACuG,UAAU,CAAG,MAAQ,UAAU,CAACvF,EAAOgB,YAAY,CAACb,IAC3EA,AAAgB,OAAhBA,EAAM0B,KAAK,EACX1B,EAAMyQ,OAAO,CAACxC,QAAQ,CAAC,yBAGnC,GAER,CAIAZ,aAAc,CAEV,GAAM,CAAET,QAAAA,CAAO,CAAEE,QAAAA,CAAO,CAAE,CAAGwH,GAAqBpX,SAAS,CAACmQ,WAAW,CAClEjQ,IAAI,CAAC,IAAI,CAAE,IAAI,CAAC6P,SAAS,CAAC,UAQ/B,OAPI4H,GAAuBjI,IACvB,CAAA,IAAI,CAACoJ,QAAQ,CAAGpJ,CAAM,EAEtBiI,GAAuB/H,IACvB,CAAA,IAAI,CAACgJ,QAAQ,CAAGhJ,CAAM,EAGnBwH,GAAqBpX,SAAS,CAACmQ,WAAW,CAACjQ,IAAI,CAAC,IAAI,CAC/D,CAMAob,eAAezY,CAAM,CAAE0Y,CAAU,CAAE,CAC/B,OAAOnE,GAAqBpX,SAAS,CAACsb,cAAc,CAACpb,IAAI,CAAC,IAAI,CAAE2C,EAAQ0Y,EAAY,CAAA,EACxF,CAMA7P,SAAU,CACN,MAAO,CAAC,CAAC,IAAI,CAAC8P,SAAS,CAACC,QAAQ,AACpC,CAKAtQ,MAAO,CACH,KAAK,CAACA,KAAK5E,KAAK,CAAC,IAAI,CAAEC,WACvB,IAAMrE,EAAU,IAAI,CAACA,OAAO,AAE5BA,CAAAA,EAAQuZ,UAAU,CAAG7D,GAAmB1V,EAAQuZ,UAAU,CAAEvZ,EAAQqT,OAAO,EAAI,GAE/E,IAAI,CAACG,KAAK,CAACgG,cAAc,CAAGxZ,EAAQuT,OAAO,EAAI,EAE/C6B,GAAQqE,OAAO,CAAGrE,GAAQsE,MAAM,CAU5B1Z,EAAQsI,MAAM,EAAIkN,GAAuBxV,EAAQ2Z,YAAY,GAC7D3Z,CAAAA,EAAQsI,MAAM,CAACiE,CAAC,CAAGvM,EAAQ2Z,YAAY,AAAD,CAE9C,CAIAC,cAAcjZ,CAAK,CAAEuM,CAAK,CAAE,CACxB,IAAMuH,EAAY9T,EAAM8T,SAAS,EAAI,CAAC,EACtC,GAAI9T,EAAMkZ,QAAQ,CACd,MAAO,CACHlN,EAAGhM,EAAM0N,KAAK,CACdzB,EAAGjM,EAAM2N,KAAK,AAClB,EAIJ,GAAIpB,GAASA,AAAU,WAAVA,EAAoB,CAC7B,IAAM4M,EAAqBnZ,EAAMX,OAAO,CAACsI,MAAM,EAAI,CAAC,EAAGyO,EAAsB,IAAI,CAAC/W,OAAO,CAACsI,MAAM,EAAI,CAAC,EAAGyR,EAAqB,AAAChD,EAAoBiD,MAAM,EAAE,CAAC9M,EAAM,EAAK,CAAC,EAAG+M,EAAoB,AAACH,EAAmBE,MAAM,EAAE,CAAC9M,EAAM,EAAK,CAAC,EAEjO1E,EAAQ,AAACyR,CAAAA,EAAkBzR,KAAK,EAClCuR,EAAmBvR,KAAK,EACxBiM,EAAUjM,KAAK,EACf,CAAA,EAAMyR,CAAAA,EAAkBC,SAAS,EACjCH,EAAmBG,SAAS,EAC5B,CAAA,EACElQ,EAAS,AAACiQ,CAAAA,EAAkBjQ,MAAM,EACpC+P,EAAmB/P,MAAM,EACzByK,EAAUzK,MAAM,EAChB,CAAA,EAAMiQ,CAAAA,EAAkBE,UAAU,EAClCJ,EAAmBI,UAAU,EAC7B,CAAA,EAGJ,MAAO,CAAExN,EADC,AAAC8H,CAAAA,EAAU9H,CAAC,EAAI,CAAA,EAAK,AAAC,CAAA,AAAC8H,CAAAA,EAAUjM,KAAK,EAAI,CAAA,EAAKA,CAAI,EAAK,EACtDoE,EAD6D,AAAC6H,CAAAA,EAAU7H,CAAC,EAAI,CAAA,EAAK,AAAC,CAAA,AAAC6H,CAAAA,EAAUzK,MAAM,EAAI,CAAA,EAAKA,CAAK,EAAK,EACpHxB,MAAAA,EAAOwB,OAAAA,CAAO,CACjC,CACA,OAAOyK,CACX,CAIAlC,aAAa5R,CAAK,CAAEuM,CAAK,CAAE,CACvB,IAAqBhM,EAAO+T,GAAqBpX,SAAS,CAAC0U,YAAY,CAACxU,IAAI,CAA7D,IAAI,CAAkE4C,EAAOuM,GAAQiG,EAAgB3S,AAArG,IAAI,CAAwGR,OAAO,EAAI,CAAC,EAAGoa,EAAc5Z,AAAzI,IAAI,CAA4IhB,KAAK,CAACQ,OAAO,CAACoa,WAAW,EAAI,CAAC,EAAGC,EAAoBD,EAAY5Z,MAAM,EAAI,CAAC,EAAG8Z,EAAqBF,EAAYG,OAAO,EAAI,CAAC,EAE3RC,EAAc7Z,GAAOX,QAAQwa,aACzBrH,EAAcqH,WAAW,EACzBF,EAAmBE,WAAW,EAC9BH,EAAkBG,WAAW,CAAErG,EAAcxT,GAAOX,QAAQmU,aAC5DhB,EAAcgB,WAAW,EACzBmG,EAAmBnG,WAAW,EAC9BkG,EAAkBlG,WAAW,EAC7BjT,CAAI,CAAC,eAAe,CAQxB,GANAA,EAAKuZ,MAAM,CAAI9Z,GAAO2H,QAAQoS,WAC1BvH,EAAc7K,MAAM,EAAEoS,WACtBF,GACA,IAAI,CAAC/b,KAAK,CAEdyC,CAAI,CAAC,eAAe,CAAGiT,EACnBjH,GAASA,AAAU,WAAVA,EAAoB,CAC7B,IAAMyN,EAAelF,GAAoBtC,EAAc6G,MAAM,EAAE,CAAC9M,EAAM,CAAEiG,EAAc7K,MAAM,EAAE0R,QAAQ,CAAC9M,EAAM,CAAEvM,GAAOX,QAAQga,QAAQ,CAAC9M,EAAM,EAAI,CAAC,EAClJhM,CAAAA,EAAKC,IAAI,CACLwZ,EAAalc,KAAK,EACdF,IAAsGC,KAAK,CAAC0C,EAAKC,IAAI,EAAEyZ,QAAQ,CAACD,EAAaE,UAAU,EAAI,GAAGnd,GAAG,GACzKwD,EAAKuZ,MAAM,CAAIE,EAAaD,SAAS,EAAIxZ,EAAKuZ,MAAM,AACxD,CACA,OAAOvZ,CACX,CAIA6S,WAAY,CACR,GAA+C,CAAE4F,aAAAA,CAAY,CAAErR,OAAAA,CAAM,CAAE,CAAxC9H,AAAhB,IAAI,CAAmBR,OAAO,CAAsCiB,EAASqH,GAAQrH,QAAU,OAAQ6Z,EAAQ1F,EAAO,CAACnU,EAAO,CAAGA,EAAS,OAAQ8Z,EAAkB,AAAwC,KAAxC,CAAC,SAAU,SAAS,CAAClb,OAAO,CAACib,GAEhN,IAAK,IAAMna,KADXH,AADe,IAAI,CACZwa,cAAc,GACDxa,AAFL,IAAI,CAEQE,MAAM,EAAE,CAC/B,IAAMmT,EAAWlT,EAAMuS,iBAAiB,GACpCvG,EAAIsB,KAAKzH,GAAG,CAACqN,EAAShI,EAAE,CAAEgI,EAAS9H,EAAE,EAAGa,EAAIqB,KAAKzH,GAAG,CAACqN,EAAS/H,EAAE,CAAE+H,EAAS7H,EAAE,EAAGxD,EAAQyF,KAAK1H,GAAG,CAAC0H,KAAKiG,GAAG,CAACL,EAAS9H,EAAE,CAAG8H,EAAShI,EAAE,EAAG,GAAI7B,EAASiE,KAAK1H,GAAG,CAAC0H,KAAKiG,GAAG,CAACL,EAAS7H,EAAE,CAAG6H,EAAS/H,EAAE,EAAG,GAIrM,GAHAnL,EAAMkZ,QAAQ,CAAG,AAA0D,IAA1D,AAAClZ,CAAAA,EAAM2H,MAAM,EAAErH,QAAUA,GAAU,EAAC,EAAGpB,OAAO,CAAC,OAG5Dkb,EAAiB,CACjB,IAAME,EAAWhN,KAAKiG,GAAG,CAAC1L,EAAQwB,GAClC2C,EAAIsB,KAAKzH,GAAG,CAACqN,EAAShI,EAAE,CAAEgI,EAAS9H,EAAE,EAChCvD,CAAAA,EAAQwB,EAAS,EAAIiR,EAAW,CAAA,EACrCrO,EAAIqB,KAAKzH,GAAG,CAACqN,EAAS/H,EAAE,CAAE+H,EAAS7H,EAAE,EAChCxD,CAAAA,EAAQwB,EAASiR,EAAW,EAAI,CAAA,EACrCzS,EAAQwB,EAASiE,KAAKzH,GAAG,CAACgC,EAAOwB,EACrC,CACIrJ,EAAMkZ,QAAQ,EACdlZ,CAAAA,EAAM2H,MAAM,CAAG,CAAEE,MAAAA,EAAOwB,OAAAA,CAAO,CAAA,EAEnCrJ,EAAM0N,KAAK,CAAG1N,EAAMua,OAAO,CAAG,AAACrH,CAAAA,EAAShI,EAAE,CAAGgI,EAAS9H,EAAE,AAAD,EAAK,EAC5DpL,EAAM2N,KAAK,CAAG,AAACuF,CAAAA,EAAS/H,EAAE,CAAG+H,EAAS7H,EAAE,AAAD,EAAK,EAC5CrL,EAAMwa,SAAS,CAAG,OAClBxa,EAAM8T,SAAS,CAAGgB,GAAoB,CAAA,EAAM,CAAE9I,EAAAA,EAAGC,EAAAA,EAAGpE,MAAAA,EAAOwB,OAAAA,CAAO,EAAG,CACjE9M,EAAGkY,EAAO,CAAC0F,EAAM,CAACnO,EAAGC,EAAGpE,EAAOwB,EAAQ,CAAEuC,EAAGiJ,GAAuBmE,GAAgBA,EAAe,CAAE,EACxG,EACJ,CACApE,GA1Be,IAAI,CA0Ba,iBACpC,CACJ,CACAiB,GAActP,cAAc,CAAGuO,GAAoBN,GAAcjO,cAAc,CAh7BjD,CAI1BrC,UAAW,CAAA,EAQX8U,aAAc,EAIdxF,YAAa,EA8Db2C,cAAe,CAAA,EAOf3U,UAAW,UACXiZ,WAAY,CACRC,UAAW,WACP,GAAM,CAAEzL,gBAAAA,CAAe,CAAE,CAAG,IAAI,CAACpP,MAAM,CAAChB,KAAK,CACvC,CAAE6C,MAAAA,CAAK,CAAE,CAAG,IAAI,CAAC1B,KAAK,CAC5B,OAAOgU,GAA+BtS,GAASuN,EAAgBvN,EAAO,IAAM,EAChF,EACAiZ,OAAQ,CAAA,EACRC,cAAe,SACfC,KAAM,CAAA,EAIN9S,SAAU,QACVuD,QAAS,CACb,EAKA3D,OAAQ,CAqBJrH,OAAQ,OAERwa,OAAQ,EACRf,UAAW,KAAK,EAChBV,OAAQ,CAIJ0B,MAAO,CAuDHC,cAAe,CACnB,EAIAC,OAAQ,CA+CR,CACJ,CACJ,EACAC,KAAM,CAAA,EAENtC,WAAY,KACZuC,QAAS,CACLC,YAAa,0CACjB,EACA/B,OAAQ,CACJ0B,MAAO,CAEHM,KAAM,CAAA,EAQNnB,WAAY,EAChB,CACJ,EACAoB,aAAc,WAClB,GAwqBA5G,GAAuBmB,GAAe,4BAA6B,WAC/D,IAAI,CAACI,aAAa,CAAG,CAAA,EACrB,IAAI,CAACC,UAAU,EACnB,GACAvB,GAAqBkB,GAAc3Y,SAAS,CAAE,CAC1C8D,UAAW6Q,EAA2BP,aAAa,CAACtQ,SAAS,CAC7DS,SAAUoQ,EAA2BP,aAAa,CAAC7P,QAAQ,CAC3D6W,YAAa,CAAA,EACbiD,mBAAoB,CAAA,EACpBC,gBAAiB,CAAC,IAAI,CACtBjK,eAAgBM,EAA2BP,aAAa,CAACC,cAAc,CACvEC,cAAe,CAAC,IAAK,QAAQ,CAC7BlN,WAt+BuD8N,GAu+BvDqJ,aAAc,QACdhK,cAAeI,EAA2BP,aAAa,CAACG,aAAa,CAIrEiK,eAAgBnH,GAAarX,SAAS,CAACwe,cAAc,CACrD7a,aAAcgR,EAA2BP,aAAa,CAACzQ,YAAY,CACnE8a,UAAWrH,GAAqBpX,SAAS,CAACye,SAAS,AACvD,GACA9J,EAA2BrP,OAAO,CAACqT,IACnCvP,IAA0IsV,kBAAkB,CAAC,UAAW/F,GAsCxK;;;;;;;;CAQC,EAK4B,IAAMvY,GAAgBE,IAGzC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}