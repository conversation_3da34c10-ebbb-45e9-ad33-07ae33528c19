{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/drag-panes\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Drag-panes module\n *\n * (c) 2010-2025 Highsoft AS\n * Author: Ka<PERSON><PERSON> Madej\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/drag-panes\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/drag-panes\"] = factory(root[\"_Highcharts\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ drag_panes_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Extensions/DragPanes/AxisResizerDefaults.js\n/* *\n *\n *  Plugin for resizing axes / panes in a chart.\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n/* *\n *\n *  API Options\n *\n * */\nconst AxisResizerDefaults = {\n    /**\n     * Minimal size of a resizable axis. Could be set as a percent\n     * of plot area or pixel size.\n     *\n     * @sample {highstock} stock/yaxis/resize-min-max-length\n     *         minLength and maxLength\n     *\n     * @type      {number|string}\n     * @product   highstock\n     * @requires  modules/drag-panes\n     * @apioption yAxis.minLength\n     */\n    minLength: '10%',\n    /**\n     * Maximal size of a resizable axis. Could be set as a percent\n     * of plot area or pixel size.\n     *\n     * @sample {highstock} stock/yaxis/resize-min-max-length\n     *         minLength and maxLength\n     *\n     * @type      {number|string}\n     * @product   highstock\n     * @requires  modules/drag-panes\n     * @apioption yAxis.maxLength\n     */\n    maxLength: '100%',\n    /**\n     * Options for axis resizing. It adds a thick line between panes which\n     * the user can drag in order to resize the panes.\n     *\n     * @sample {highstock} stock/demo/candlestick-and-volume\n     *         Axis resizing enabled\n     *\n     * @product      highstock\n     * @requires     modules/drag-panes\n     * @optionparent yAxis.resize\n     */\n    resize: {\n        /**\n         * Contains two arrays of axes that are controlled by control line\n         * of the axis.\n         *\n         * @requires modules/drag-panes\n         */\n        controlledAxis: {\n            /**\n             * Array of axes that should move out of the way of resizing\n             * being done for the current axis. If not set, the next axis\n             * will be used.\n             *\n             * @sample {highstock} stock/yaxis/multiple-resizers\n             *         Three panes with resizers\n             * @sample {highstock} stock/yaxis/resize-multiple-axes\n             *         One resizer controlling multiple axes\n             *\n             * @type     {Array<number|string>}\n             * @default  []\n             * @requires modules/drag-panes\n             */\n            next: [],\n            /**\n             * Array of axes that should move with the current axis\n             * while resizing.\n             *\n             * @sample {highstock} stock/yaxis/multiple-resizers\n             *         Three panes with resizers\n             * @sample {highstock} stock/yaxis/resize-multiple-axes\n             *         One resizer controlling multiple axes\n             *\n             * @type     {Array<number|string>}\n             * @default  []\n             * @requires modules/drag-panes\n             */\n            prev: []\n        },\n        /**\n         * Enable or disable resize by drag for the axis.\n         *\n         * @sample {highstock} stock/demo/candlestick-and-volume\n         *         Enabled resizer\n         *\n         * @requires modules/drag-panes\n         */\n        enabled: false,\n        /**\n         * Cursor style for the control line.\n         *\n         * In styled mode use class `highcharts-axis-resizer` instead.\n         *\n         * @requires modules/drag-panes\n         */\n        cursor: 'ns-resize',\n        /**\n         * Color of the control line.\n         *\n         * In styled mode use class `highcharts-axis-resizer` instead.\n         *\n         * @sample {highstock} stock/yaxis/styled-resizer\n         *         Styled resizer\n         *\n         * @type     {Highcharts.ColorString}\n         * @requires modules/drag-panes\n         */\n        lineColor: \"#cccccc\" /* Palette.neutralColor20 */,\n        /**\n         * Dash style of the control line.\n         *\n         * In styled mode use class `highcharts-axis-resizer` instead.\n         *\n         * @see For supported options check [dashStyle](#plotOptions.series.dashStyle)\n         *\n         * @sample {highstock} stock/yaxis/styled-resizer\n         *         Styled resizer\n         *\n         * @requires modules/drag-panes\n         */\n        lineDashStyle: 'Solid',\n        /**\n         * Width of the control line.\n         *\n         * In styled mode use class `highcharts-axis-resizer` instead.\n         *\n         * @sample {highstock} stock/yaxis/styled-resizer\n         *         Styled resizer\n         *\n         * @requires modules/drag-panes\n         */\n        lineWidth: 4,\n        /**\n         * Horizontal offset of the control line.\n         *\n         * @sample {highstock} stock/yaxis/styled-resizer\n         *         Styled resizer\n         *\n         * @requires modules/drag-panes\n         */\n        x: 0,\n        /**\n         * Vertical offset of the control line.\n         *\n         * @sample {highstock} stock/yaxis/styled-resizer\n         *         Styled resizer\n         *\n         * @requires modules/drag-panes\n         */\n        y: 0\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const DragPanes_AxisResizerDefaults = (AxisResizerDefaults);\n\n;// ./code/es-modules/Extensions/DragPanes/AxisResizer.js\n/* *\n *\n *  Plugin for resizing axes / panes in a chart.\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { addEvent, clamp, isNumber, relativeLength } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The AxisResizer class.\n *\n * @private\n * @class\n * @name Highcharts.AxisResizer\n *\n * @param {Highcharts.Axis} axis\n *        Main axis for the AxisResizer.\n */\nclass AxisResizer {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(axis) {\n        this.init(axis);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Initialize the AxisResizer object.\n     *\n     * @function Highcharts.AxisResizer#init\n     *\n     * @param {Highcharts.Axis} axis\n     *        Main axis for the AxisResizer.\n     */\n    init(axis, update) {\n        this.axis = axis;\n        this.options = axis.options.resize || {};\n        this.render();\n        if (!update) {\n            // Add mouse events.\n            this.addMouseEvents();\n        }\n    }\n    /**\n     * Render the AxisResizer\n     *\n     * @function Highcharts.AxisResizer#render\n     */\n    render() {\n        const resizer = this, axis = resizer.axis, chart = axis.chart, options = resizer.options, x = options.x || 0, y = options.y, \n        // Normalize control line position according to the plot area\n        pos = clamp(axis.top + axis.height + y, chart.plotTop, chart.plotTop + chart.plotHeight);\n        let attr = {};\n        if (!chart.styledMode) {\n            attr = {\n                cursor: options.cursor,\n                stroke: options.lineColor,\n                'stroke-width': options.lineWidth,\n                dashstyle: options.lineDashStyle\n            };\n        }\n        // Register current position for future reference.\n        resizer.lastPos = pos - y;\n        if (!resizer.controlLine) {\n            resizer.controlLine = chart.renderer.path()\n                .addClass('highcharts-axis-resizer');\n        }\n        // Add to axisGroup after axis update, because the group is recreated\n        // Do .add() before path is calculated because strokeWidth() needs it.\n        resizer.controlLine.add(axis.axisGroup);\n        const lineWidth = chart.styledMode ?\n            resizer.controlLine.strokeWidth() :\n            options.lineWidth;\n        attr.d = chart.renderer.crispLine([\n            ['M', axis.left + x, pos],\n            ['L', axis.left + axis.width + x, pos]\n        ], lineWidth);\n        resizer.controlLine.attr(attr);\n    }\n    /**\n     * Set up the mouse and touch events for the control line.\n     *\n     * @function Highcharts.AxisResizer#addMouseEvents\n     */\n    addMouseEvents() {\n        const resizer = this, ctrlLineElem = resizer.controlLine.element, container = resizer.axis.chart.container, eventsToUnbind = [];\n        let mouseMoveHandler, mouseUpHandler, mouseDownHandler;\n        // Create mouse events' handlers.\n        // Make them as separate functions to enable wrapping them:\n        resizer.mouseMoveHandler = mouseMoveHandler = (e) => (resizer.onMouseMove(e));\n        resizer.mouseUpHandler = mouseUpHandler = (e) => (resizer.onMouseUp(e));\n        resizer.mouseDownHandler = mouseDownHandler = () => (resizer.onMouseDown());\n        eventsToUnbind.push(\n        // Add mouse move and mouseup events. These are bind to doc/div,\n        // because resizer.grabbed flag is stored in mousedown events.\n        addEvent(container, 'mousemove', mouseMoveHandler), addEvent(container.ownerDocument, 'mouseup', mouseUpHandler), addEvent(ctrlLineElem, 'mousedown', mouseDownHandler), \n        // Touch events.\n        addEvent(container, 'touchmove', mouseMoveHandler), addEvent(container.ownerDocument, 'touchend', mouseUpHandler), addEvent(ctrlLineElem, 'touchstart', mouseDownHandler));\n        resizer.eventsToUnbind = eventsToUnbind;\n    }\n    /**\n     * Mouse move event based on x/y mouse position.\n     *\n     * @function Highcharts.AxisResizer#onMouseMove\n     *\n     * @param {Highcharts.PointerEventObject} e\n     *        Mouse event.\n     */\n    onMouseMove(e) {\n        /*\n         * In iOS, a mousemove event with e.pageX === 0 is fired when holding\n         * the finger down in the center of the scrollbar. This should\n         * be ignored. Borrowed from Navigator.\n         */\n        if (!e.touches || e.touches[0].pageX !== 0) {\n            const pointer = this.axis.chart.pointer;\n            // Drag the control line\n            if (this.grabbed && pointer) {\n                this.hasDragged = true;\n                this.updateAxes(pointer.normalize(e).chartY - (this.options.y || 0));\n            }\n        }\n    }\n    /**\n     * Mouse up event based on x/y mouse position.\n     *\n     * @function Highcharts.AxisResizer#onMouseUp\n     *\n     * @param {Highcharts.PointerEventObject} e\n     *        Mouse event.\n     */\n    onMouseUp(e) {\n        const pointer = this.axis.chart.pointer;\n        if (this.hasDragged && pointer) {\n            this.updateAxes(pointer.normalize(e).chartY - (this.options.y || 0));\n        }\n        // Restore runPointActions.\n        this.grabbed = this.hasDragged = this.axis.chart.activeResizer = void 0;\n    }\n    /**\n     * Mousedown on a control line.\n     * Will store necessary information for drag&drop.\n     *\n     * @function Highcharts.AxisResizer#onMouseDown\n     */\n    onMouseDown() {\n        // Clear all hover effects.\n        this.axis.chart.pointer?.reset(false, 0);\n        // Disable runPointActions.\n        this.grabbed = this.axis.chart.activeResizer = true;\n    }\n    /**\n     * Update all connected axes after a change of control line position\n     *\n     * @function Highcharts.AxisResizer#updateAxes\n     *\n     * @param {number} chartY\n     */\n    updateAxes(chartY) {\n        const resizer = this, chart = resizer.axis.chart, axes = resizer.options.controlledAxis, nextAxes = axes.next.length === 0 ?\n            [chart.yAxis.indexOf(resizer.axis) + 1] : axes.next, \n        // Main axis is included in the prev array by default\n        prevAxes = [resizer.axis].concat(axes.prev), \n        // Prev and next configs\n        axesConfigs = [], plotTop = chart.plotTop, plotHeight = chart.plotHeight, plotBottom = plotTop + plotHeight, calculatePercent = (value) => (value * 100 / plotHeight + '%'), normalize = (val, min, max) => (Math.round(clamp(val, min, max)));\n        // Normalize chartY to plot area limits\n        chartY = clamp(chartY, plotTop, plotBottom);\n        let stopDrag = false, yDelta = chartY - resizer.lastPos;\n        // Update on changes of at least 1 pixel in the desired direction\n        if (yDelta * yDelta < 1) {\n            return;\n        }\n        let isFirst = true;\n        // First gather info how axes should behave\n        for (const axesGroup of [prevAxes, nextAxes]) {\n            for (const axisInfo of axesGroup) {\n                // Axes given as array index, axis object or axis id\n                const axis = isNumber(axisInfo) ?\n                    // If it's a number - it's an index\n                    chart.yAxis[axisInfo] :\n                    (\n                    // If it's first elem. in first group\n                    isFirst ?\n                        // Then it's an Axis object\n                        axisInfo :\n                        // Else it should be an id\n                        chart.get(axisInfo)), axisOptions = axis && axis.options, optionsToUpdate = {};\n                let height, top;\n                // Skip if axis is not found\n                // or it is navigator's yAxis (#7732)\n                if (!axisOptions || axisOptions.isInternal) {\n                    isFirst = false;\n                    continue;\n                }\n                top = axis.top;\n                const minLength = Math.round(relativeLength(axisOptions.minLength || NaN, plotHeight)), maxLength = Math.round(relativeLength(axisOptions.maxLength || NaN, plotHeight));\n                if (!isFirst && axesGroup === nextAxes) {\n                    // Try to change height first. yDelta could had changed\n                    yDelta = chartY - resizer.lastPos;\n                    // Normalize height to option limits\n                    height = normalize(axis.len - yDelta, minLength, maxLength);\n                    // Adjust top, so the axis looks like shrinked from top\n                    top = axis.top + yDelta;\n                    // Check for plot area limits\n                    if (top + height > plotBottom) {\n                        const hDelta = plotBottom - height - top;\n                        chartY += hDelta;\n                        top += hDelta;\n                    }\n                    // Fit to plot - when overflowing on top\n                    if (top < plotTop) {\n                        top = plotTop;\n                        if (top + height > plotBottom) {\n                            height = plotHeight;\n                        }\n                    }\n                    // If next axis meets min length, stop dragging:\n                    if (height === minLength) {\n                        stopDrag = true;\n                    }\n                    axesConfigs.push({\n                        axis: axis,\n                        options: {\n                            top: calculatePercent(top - plotTop),\n                            height: calculatePercent(height)\n                        }\n                    });\n                }\n                else {\n                    // Normalize height to option limits\n                    height = normalize(chartY - top, minLength, maxLength);\n                    // If prev axis meets max length, stop dragging:\n                    if (height === maxLength) {\n                        stopDrag = true;\n                    }\n                    // Check axis size limits\n                    chartY = top + height;\n                    axesConfigs.push({\n                        axis: axis,\n                        options: {\n                            height: calculatePercent(height)\n                        }\n                    });\n                }\n                isFirst = false;\n                optionsToUpdate.height = height;\n            }\n        }\n        // If we hit the min/maxLength with dragging, don't do anything:\n        if (!stopDrag) {\n            // Now update axes:\n            for (const config of axesConfigs) {\n                config.axis.update(config.options, false);\n            }\n            chart.redraw(false);\n        }\n    }\n    /**\n     * Destroy AxisResizer. Clear outside references, clear events,\n     * destroy elements, nullify properties.\n     *\n     * @function Highcharts.AxisResizer#destroy\n     */\n    destroy() {\n        const resizer = this, axis = resizer.axis;\n        // Clear resizer in axis\n        delete axis.resizer;\n        // Clear control line events\n        if (this.eventsToUnbind) {\n            this.eventsToUnbind.forEach((unbind) => unbind());\n        }\n        // Destroy AxisResizer elements\n        resizer.controlLine.destroy();\n        // Nullify properties\n        for (const key of Object.keys(resizer)) {\n            resizer[key] = null;\n        }\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n// Default options for AxisResizer.\nAxisResizer.resizerOptions = DragPanes_AxisResizerDefaults;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const DragPanes_AxisResizer = (AxisResizer);\n\n;// ./code/es-modules/Extensions/DragPanes/DragPanes.js\n/* *\n *\n *  Plugin for resizing axes / panes in a chart.\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { defaultOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent: DragPanes_addEvent, merge, wrap } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction compose(AxisClass, PointerClass) {\n    if (!AxisClass.keepProps.includes('resizer')) {\n        merge(true, defaultOptions.yAxis, DragPanes_AxisResizer.resizerOptions);\n        // Keep resizer reference on axis update\n        AxisClass.keepProps.push('resizer');\n        DragPanes_addEvent(AxisClass, 'afterRender', onAxisAfterRender);\n        DragPanes_addEvent(AxisClass, 'destroy', onAxisDestroy);\n        wrap(PointerClass.prototype, 'runPointActions', wrapPointerRunPointActions);\n        wrap(PointerClass.prototype, 'drag', wrapPointerDrag);\n    }\n}\n/**\n * Add new AxisResizer, update or remove it\n * @private\n */\nfunction onAxisAfterRender() {\n    const axis = this, resizer = axis.resizer, resizerOptions = axis.options.resize;\n    if (resizerOptions) {\n        const enabled = resizerOptions.enabled !== false;\n        if (resizer) {\n            // Resizer present and enabled\n            if (enabled) {\n                // Update options\n                resizer.init(axis, true);\n                // Resizer present, but disabled\n            }\n            else {\n                // Destroy the resizer\n                resizer.destroy();\n            }\n        }\n        else {\n            // Resizer not present and enabled\n            if (enabled) {\n                // Add new resizer\n                axis.resizer = new DragPanes_AxisResizer(axis);\n            }\n            // Resizer not present and disabled, so do nothing\n        }\n    }\n}\n/**\n * Clear resizer on axis remove.\n * @private\n */\nfunction onAxisDestroy(e) {\n    const axis = this;\n    if (!e.keepEvents && axis.resizer) {\n        axis.resizer.destroy();\n    }\n}\n/**\n * Prevent default drag action detection while dragging a control line of\n * AxisResizer. (#7563)\n * @private\n */\nfunction wrapPointerDrag(proceed) {\n    const pointer = this;\n    if (!pointer.chart.activeResizer) {\n        proceed.apply(pointer, [].slice.call(arguments, 1));\n    }\n}\n/**\n * Prevent any hover effects while dragging a control line of AxisResizer.\n * @private\n */\nfunction wrapPointerRunPointActions(proceed) {\n    const pointer = this;\n    if (!pointer.chart.activeResizer) {\n        proceed.apply(pointer, [].slice.call(arguments, 1));\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst DragPanes = {\n    compose\n};\n/* harmony default export */ const DragPanes_DragPanes = (DragPanes);\n\n;// ./code/es-modules/masters/modules/drag-panes.js\n\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nG.AxisResizer = DragPanes_AxisResizer;\nDragPanes_DragPanes.compose(G.Axis, G.Pointer);\n/* harmony default export */ const drag_panes_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "drag_panes_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "addEvent", "clamp", "isNumber", "<PERSON><PERSON><PERSON><PERSON>", "AxisResizer", "constructor", "axis", "init", "update", "options", "resize", "render", "addMouseEvents", "resizer", "chart", "x", "y", "pos", "top", "height", "plotTop", "plotHeight", "attr", "styledMode", "cursor", "stroke", "lineColor", "lineWidth", "dashstyle", "lineDashStyle", "lastPos", "controlLine", "renderer", "path", "addClass", "add", "axisGroup", "strokeWidth", "crispLine", "left", "width", "mouseMoveHandler", "mouseUpHandler", "mouseDownHandler", "ctrlLineElem", "element", "container", "eventsToUnbind", "e", "onMouseMove", "onMouseUp", "onMouseDown", "push", "ownerDocument", "touches", "pageX", "pointer", "grabbed", "hasDragged", "updateAxes", "normalize", "chartY", "activeResizer", "reset", "axes", "controlledAxis", "nextAxes", "next", "length", "yAxis", "indexOf", "prevAxes", "concat", "prev", "axesConfigs", "plotBottom", "calculatePercent", "value", "val", "min", "max", "Math", "round", "stopDrag", "y<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "axesGroup", "axisInfo", "axisOptions", "optionsToUpdate", "isInternal", "<PERSON><PERSON><PERSON><PERSON>", "NaN", "max<PERSON><PERSON><PERSON>", "len", "h<PERSON><PERSON><PERSON>", "config", "redraw", "destroy", "for<PERSON>ach", "unbind", "keys", "resizerOptions", "enabled", "defaultOptions", "DragPanes_addEvent", "merge", "wrap", "onAxisAfterRender", "onAxisDestroy", "keepEvents", "wrapPointerDrag", "proceed", "apply", "slice", "arguments", "wrapPointerRunPointActions", "G", "DragPanes_DragPanes", "compose", "AxisClass", "PointerClass", "keepProps", "includes", "DragPanes_AxisResizer", "Axis", "Pointer"], "mappings": "CAaA,AAbA;;;;;;;;;;;;CAYC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,EACrC,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,gCAAiC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAM,GAClG,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,gCAAgC,CAAGD,EAAQD,EAAK,WAAc,EAEtEA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CACjD,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,AAACC,GACnC,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIE,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaX,OAAO,CAG5B,IAAIC,EAASO,CAAwB,CAACE,EAAS,CAAG,CAGjDV,QAAS,CAAC,CACX,EAMA,OAHAO,CAAmB,CAACG,EAAS,CAACT,EAAQA,EAAOD,OAAO,CAAES,GAG/CR,EAAOD,OAAO,AACtB,CAMCS,EAAoBI,CAAC,CAAG,AAACZ,IACxB,IAAIa,EAASb,GAAUA,EAAOc,UAAU,CACvC,IAAOd,EAAO,OAAU,CACxB,IAAOA,EAER,OADAQ,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAAChB,EAASkB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACpB,EAASmB,IAC5EE,OAAOC,cAAc,CAACtB,EAASmB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAiMrH,GAAM,CAAEE,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,SAAAA,CAAQ,CAAEC,eAAAA,CAAc,CAAE,CAAIJ,GAgBvD,OAAMK,EAMFC,YAAYC,CAAI,CAAE,CACd,IAAI,CAACC,IAAI,CAACD,EACd,CAcAC,KAAKD,CAAI,CAAEE,CAAM,CAAE,CACf,IAAI,CAACF,IAAI,CAAGA,EACZ,IAAI,CAACG,OAAO,CAAGH,EAAKG,OAAO,CAACC,MAAM,EAAI,CAAC,EACvC,IAAI,CAACC,MAAM,GACP,AAACH,GAED,IAAI,CAACI,cAAc,EAE3B,CAMAD,QAAS,CACL,IAAsBL,EAAOO,AAAb,IAAI,CAAiBP,IAAI,CAAEQ,EAAQR,EAAKQ,KAAK,CAAEL,EAAUI,AAAzD,IAAI,CAA6DJ,OAAO,CAAEM,EAAIN,EAAQM,CAAC,EAAI,EAAGC,EAAIP,EAAQO,CAAC,CAE3HC,EAAMhB,EAAMK,EAAKY,GAAG,CAAGZ,EAAKa,MAAM,CAAGH,EAAGF,EAAMM,OAAO,CAAEN,EAAMM,OAAO,CAAGN,EAAMO,UAAU,EACnFC,EAAO,CAAC,CACR,AAACR,CAAAA,EAAMS,UAAU,EACjBD,CAAAA,EAAO,CACHE,OAAQf,EAAQe,MAAM,CACtBC,OAAQhB,EAAQiB,SAAS,CACzB,eAAgBjB,EAAQkB,SAAS,CACjCC,UAAWnB,EAAQoB,aAAa,AACpC,CAAA,EAGJhB,AAbgB,IAAI,CAaZiB,OAAO,CAAGb,EAAMD,EACpB,AAACH,AAdW,IAAI,CAcPkB,WAAW,EACpBlB,CAAAA,AAfY,IAAI,CAeRkB,WAAW,CAAGjB,EAAMkB,QAAQ,CAACC,IAAI,GACpCC,QAAQ,CAAC,0BAAyB,EAI3CrB,AApBgB,IAAI,CAoBZkB,WAAW,CAACI,GAAG,CAAC7B,EAAK8B,SAAS,EACtC,IAAMT,EAAYb,EAAMS,UAAU,CAC9BV,AAtBY,IAAI,CAsBRkB,WAAW,CAACM,WAAW,GAC/B5B,EAAQkB,SAAS,AACrBL,CAAAA,EAAKxC,CAAC,CAAGgC,EAAMkB,QAAQ,CAACM,SAAS,CAAC,CAC9B,CAAC,IAAKhC,EAAKiC,IAAI,CAAGxB,EAAGE,EAAI,CACzB,CAAC,IAAKX,EAAKiC,IAAI,CAAGjC,EAAKkC,KAAK,CAAGzB,EAAGE,EAAI,CACzC,CAAEU,GACHd,AA5BgB,IAAI,CA4BZkB,WAAW,CAACT,IAAI,CAACA,EAC7B,CAMAV,gBAAiB,CACb,IACI6B,EAAkBC,EAAgBC,EADhC9B,EAAU,IAAI,CAAE+B,EAAe/B,EAAQkB,WAAW,CAACc,OAAO,CAAEC,EAAYjC,EAAQP,IAAI,CAACQ,KAAK,CAACgC,SAAS,CAAEC,EAAiB,EAAE,AAI/HlC,CAAAA,EAAQ4B,gBAAgB,CAAGA,EAAmB,AAACO,GAAOnC,EAAQoC,WAAW,CAACD,GAC1EnC,EAAQ6B,cAAc,CAAGA,EAAiB,AAACM,GAAOnC,EAAQqC,SAAS,CAACF,GACpEnC,EAAQ8B,gBAAgB,CAAGA,EAAmB,IAAO9B,EAAQsC,WAAW,GACxEJ,EAAeK,IAAI,CAGnBpD,EAAS8C,EAAW,YAAaL,GAAmBzC,EAAS8C,EAAUO,aAAa,CAAE,UAAWX,GAAiB1C,EAAS4C,EAAc,YAAaD,GAEtJ3C,EAAS8C,EAAW,YAAaL,GAAmBzC,EAAS8C,EAAUO,aAAa,CAAE,WAAYX,GAAiB1C,EAAS4C,EAAc,aAAcD,IACxJ9B,EAAQkC,cAAc,CAAGA,CAC7B,CASAE,YAAYD,CAAC,CAAE,CAMX,GAAI,CAACA,EAAEM,OAAO,EAAIN,AAAuB,IAAvBA,EAAEM,OAAO,CAAC,EAAE,CAACC,KAAK,CAAQ,CACxC,IAAMC,EAAU,IAAI,CAAClD,IAAI,CAACQ,KAAK,CAAC0C,OAAO,AAEnC,CAAA,IAAI,CAACC,OAAO,EAAID,IAChB,IAAI,CAACE,UAAU,CAAG,CAAA,EAClB,IAAI,CAACC,UAAU,CAACH,EAAQI,SAAS,CAACZ,GAAGa,MAAM,CAAI,CAAA,IAAI,CAACpD,OAAO,CAACO,CAAC,EAAI,CAAA,GAEzE,CACJ,CASAkC,UAAUF,CAAC,CAAE,CACT,IAAMQ,EAAU,IAAI,CAAClD,IAAI,CAACQ,KAAK,CAAC0C,OAAO,AACnC,CAAA,IAAI,CAACE,UAAU,EAAIF,GACnB,IAAI,CAACG,UAAU,CAACH,EAAQI,SAAS,CAACZ,GAAGa,MAAM,CAAI,CAAA,IAAI,CAACpD,OAAO,CAACO,CAAC,EAAI,CAAA,GAGrE,IAAI,CAACyC,OAAO,CAAG,IAAI,CAACC,UAAU,CAAG,IAAI,CAACpD,IAAI,CAACQ,KAAK,CAACgD,aAAa,CAAG,KAAK,CAC1E,CAOAX,aAAc,CAEV,IAAI,CAAC7C,IAAI,CAACQ,KAAK,CAAC0C,OAAO,EAAEO,MAAM,CAAA,EAAO,GAEtC,IAAI,CAACN,OAAO,CAAG,IAAI,CAACnD,IAAI,CAACQ,KAAK,CAACgD,aAAa,CAAG,CAAA,CACnD,CAQAH,WAAWE,CAAM,CAAE,CACf,IAAsB/C,EAAQD,AAAd,IAAI,CAAkBP,IAAI,CAACQ,KAAK,CAAEkD,EAAOnD,AAAzC,IAAI,CAA6CJ,OAAO,CAACwD,cAAc,CAAEC,EAAWF,AAAqB,IAArBA,EAAKG,IAAI,CAACC,MAAM,CAChH,CAACtD,EAAMuD,KAAK,CAACC,OAAO,CAACzD,AADT,IAAI,CACaP,IAAI,EAAI,EAAE,CAAG0D,EAAKG,IAAI,CAEvDI,EAAW,CAAC1D,AAHI,IAAI,CAGAP,IAAI,CAAC,CAACkE,MAAM,CAACR,EAAKS,IAAI,EAE1CC,EAAc,EAAE,CAAEtD,EAAUN,EAAMM,OAAO,CAAEC,EAAaP,EAAMO,UAAU,CAAEsD,EAAavD,EAAUC,EAAYuD,EAAmB,AAACC,GAAWA,AAAQ,IAARA,EAAcxD,EAAa,IAAMuC,EAAY,CAACkB,EAAKC,EAAKC,IAASC,KAAKC,KAAK,CAACjF,EAAM6E,EAAKC,EAAKC,IAExOnB,EAAS5D,EAAM4D,EAAQzC,EAASuD,GAChC,IAAIQ,EAAW,CAAA,EAAOC,EAASvB,EAAShD,AARxB,IAAI,CAQ4BiB,OAAO,CAEvD,GAAIsD,EAASA,EAAS,EAClB,OAEJ,IAAIC,EAAU,CAAA,EAEd,IAAK,IAAMC,IAAa,CAACf,EAAUL,EAAS,CACxC,IAAK,IAAMqB,KAAYD,EAAW,CAE9B,IAUInE,EAAQD,EAVNZ,EAAOJ,EAASqF,GAElBzE,EAAMuD,KAAK,CAACkB,EAAS,CAGrBF,EAEIE,EAEAzE,EAAMxB,GAAG,CAACiG,GAAYC,EAAclF,GAAQA,EAAKG,OAAO,CAAEgF,EAAkB,CAAC,EAIrF,GAAI,CAACD,GAAeA,EAAYE,UAAU,CAAE,CACxCL,EAAU,CAAA,EACV,QACJ,CACAnE,EAAMZ,EAAKY,GAAG,CACd,IAAMyE,EAAYV,KAAKC,KAAK,CAAC/E,EAAeqF,EAAYG,SAAS,EAAIC,IAAKvE,IAAcwE,EAAYZ,KAAKC,KAAK,CAAC/E,EAAeqF,EAAYK,SAAS,EAAID,IAAKvE,IAC5J,GAAI,AAACgE,GAAWC,IAAcpB,EAoCtB/C,AAFJA,CAAAA,EAASyC,EAAUC,EAAS3C,EAAKyE,EAAWE,EAAS,IAEtCA,GACXV,CAAAA,EAAW,CAAA,CAAG,EAGlBtB,EAAS3C,EAAMC,EACfuD,EAAYtB,IAAI,CAAC,CACb9C,KAAMA,EACNG,QAAS,CACLU,OAAQyD,EAAiBzD,EAC7B,CACJ,OA9CoC,CAQpC,GANAiE,EAASvB,EAAShD,AAvCd,IAAI,CAuCkBiB,OAAO,CAEjCX,EAASyC,EAAUtD,EAAKwF,GAAG,CAAGV,EAAQO,EAAWE,GAI7C3E,AAFJA,CAAAA,EAAMZ,EAAKY,GAAG,CAAGkE,CAAK,EAEZjE,EAASwD,EAAY,CAC3B,IAAMoB,EAASpB,EAAaxD,EAASD,EACrC2C,GAAUkC,EACV7E,GAAO6E,CACX,CAEI7E,EAAME,GAEFF,AADJA,CAAAA,EAAME,CAAM,EACFD,EAASwD,GACfxD,CAAAA,EAASE,CAAS,EAItBF,IAAWwE,GACXR,CAAAA,EAAW,CAAA,CAAG,EAElBT,EAAYtB,IAAI,CAAC,CACb9C,KAAMA,EACNG,QAAS,CACLS,IAAK0D,EAAiB1D,EAAME,GAC5BD,OAAQyD,EAAiBzD,EAC7B,CACJ,EACJ,CAiBAkE,EAAU,CAAA,EACVI,EAAgBtE,MAAM,CAAGA,CAC7B,CAGJ,GAAI,CAACgE,EAAU,CAEX,IAAK,IAAMa,KAAUtB,EACjBsB,EAAO1F,IAAI,CAACE,MAAM,CAACwF,EAAOvF,OAAO,CAAE,CAAA,GAEvCK,EAAMmF,MAAM,CAAC,CAAA,EACjB,CACJ,CAOAC,SAAU,CACN,IAAsB5F,EAAOO,AAAb,IAAI,CAAiBP,IAAI,CAUzC,IAAK,IAAMrB,KARX,OAAOqB,EAAKO,OAAO,CAEf,IAAI,CAACkC,cAAc,EACnB,IAAI,CAACA,cAAc,CAACoD,OAAO,CAAC,AAACC,GAAWA,KAG5CvF,AARgB,IAAI,CAQZkB,WAAW,CAACmE,OAAO,GAET/G,OAAOkH,IAAI,CAVb,IAAI,GAWhBxF,AAXY,IAAI,AAWT,CAAC5B,EAAI,CAAG,IAEvB,CACJ,CAOAmB,EAAYkG,cAAc,CA9cE,CAaxBX,UAAW,MAaXE,UAAW,OAYXnF,OAAQ,CAOJuD,eAAgB,CAeZE,KAAM,EAAE,CAcRM,KAAM,EAAE,AACZ,EASA8B,QAAS,CAAA,EAQT/E,OAAQ,YAYRE,UAAW,UAaXG,cAAe,QAWfF,UAAW,EASXZ,EAAG,EASHC,EAAG,CACP,CACJ,EAmVA,GAAM,CAAEwF,eAAAA,CAAc,CAAE,CAAIzG,IAEtB,CAAEC,SAAUyG,CAAkB,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAE,CAAI5G,IAwBvD,SAAS6G,IACL,IAAmB/F,EAAUP,AAAhB,IAAI,CAAiBO,OAAO,CAAEyF,EAAiBhG,AAA/C,IAAI,CAAgDG,OAAO,CAACC,MAAM,CAC/E,GAAI4F,EAAgB,CAChB,IAAMC,EAAUD,AAA2B,CAAA,IAA3BA,EAAeC,OAAO,CAClC1F,EAEI0F,EAEA1F,EAAQN,IAAI,CAPX,IAAI,CAOc,CAAA,GAKnBM,EAAQqF,OAAO,GAKfK,GAEAjG,CAAAA,AAnBC,IAAI,CAmBAO,OAAO,CAAG,IAjE6BT,EA8C3C,IAAI,CAmBwC,CAIzD,CACJ,CAKA,SAASyG,EAAc7D,CAAC,EAEhB,CAACA,EAAE8D,UAAU,EAAIxG,AADR,IAAI,CACSO,OAAO,EAC7BP,AAFS,IAAI,CAERO,OAAO,CAACqF,OAAO,EAE5B,CAMA,SAASa,EAAgBC,CAAO,EAExB,AAACxD,AADW,IAAI,CACP1C,KAAK,CAACgD,aAAa,EAC5BkD,EAAQC,KAAK,CAFD,IAAI,CAEO,EAAE,CAACC,KAAK,CAACvH,IAAI,CAACwH,UAAW,GAExD,CAKA,SAASC,EAA2BJ,CAAO,EAEnC,AAACxD,AADW,IAAI,CACP1C,KAAK,CAACgD,aAAa,EAC5BkD,EAAQC,KAAK,CAFD,IAAI,CAEO,EAAE,CAACC,KAAK,CAACvH,IAAI,CAACwH,UAAW,GAExD,CAiBA,IAAME,EAAKtH,GACXsH,CAAAA,EAAEjH,WAAW,CAvH+CA,EAwH5DkH,AAbkB,CAAA,CACdC,QA9EJ,SAAiBC,CAAS,CAAEC,CAAY,EAC/BD,EAAUE,SAAS,CAACC,QAAQ,CAAC,aAC9BjB,EAAM,CAAA,EAAMF,EAAenC,KAAK,CAAEuD,AAhCkBxH,EAgCIkG,cAAc,EAEtEkB,EAAUE,SAAS,CAACtE,IAAI,CAAC,WACzBqD,EAAmBe,EAAW,cAAeZ,GAC7CH,EAAmBe,EAAW,UAAWX,GACzCF,EAAKc,EAAahI,SAAS,CAAE,kBAAmB2H,GAChDT,EAAKc,EAAahI,SAAS,CAAE,OAAQsH,GAE7C,CAqEA,CAAA,EAWoBQ,OAAO,CAACF,EAAEQ,IAAI,CAAER,EAAES,OAAO,EAChB,IAAMjI,EAAmBE,IAG5C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}