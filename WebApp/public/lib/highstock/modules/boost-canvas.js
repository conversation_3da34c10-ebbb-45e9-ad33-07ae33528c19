!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/boost-canvas
 * @requires highcharts
 *
 * Boost module
 *
 * (c) 2010-2025 Highsoft AS
 * Author: Torstein Honsi
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.Color):"function"==typeof define&&define.amd?define("highcharts/modules/boost-canvas",["highcharts/highcharts"],function(t){return e(t,t.Color)}):"object"==typeof exports?exports["highcharts/modules/boost-canvas"]=e(t._Highcharts,t._Highcharts.Color):t.Highcharts=e(t.Highcharts,t.Highcharts.Color)}("undefined"==typeof window?this:window,(t,e)=>(()=>{"use strict";let i,s;var o,r,n={620:t=>{t.exports=e},944:e=>{e.exports=t}},a={};function l(t){var e=a[t];if(void 0!==e)return e.exports;var i=a[t]={exports:{}};return n[t](i,i.exports,l),i.exports}l.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return l.d(e,{a:e}),e},l.d=(t,e)=>{for(var i in e)l.o(e,i)&&!l.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},l.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var h={};l.d(h,{default:()=>tF});var d=l(944),u=l.n(d);let c=["area","areaspline","arearange","column","columnrange","bar","line","scatter","heatmap","bubble","treemap"],g={};c.forEach(t=>{g[t]=!0});let{composed:m}=u(),{addEvent:p,pick:f,pushUnique:b}=u();function x(t){let e=t.series,i=t.boost=t.boost||{},s=t.options.boost||{},o=f(s.seriesThreshold,50);if(e.length>=o)return!0;if(1===e.length)return!1;let r=s.allowForce;if(void 0===r){for(let e of(r=!0,t.xAxis))if(f(e.min,-1/0)>f(e.dataMin,-1/0)||f(e.max,1/0)<f(e.dataMax,1/0)){r=!1;break}}if(void 0!==i.forceChartBoost){if(r)return i.forceChartBoost;i.forceChartBoost=void 0}let n=0,a=0,l;for(let t of e)0!==(l=t.options).boostThreshold&&!1!==t.visible&&"heatmap"!==t.type&&(g[t.type]&&++n,function(...t){let e=-Number.MAX_VALUE;return t.forEach(t=>{if(null!=t&&void 0!==t.length&&t.length>0)return e=t.length,!0}),e}(t.getColumn("x",!0),l.data,t.points)>=(l.boostThreshold||Number.MAX_VALUE)&&++a);return i.forceChartBoost=r&&(n===e.length&&a===n||a>5),i.forceChartBoost}function A(t){function e(){t.boost&&t.boost.wgl&&x(t)&&t.boost.wgl.render(t)}p(t,"predraw",function(){t.boost=t.boost||{},t.boost.forceChartBoost=void 0,t.boosted=!1,t.axes.some(t=>t.isPanning)||t.boost.clear?.(),t.boost.canvas&&t.boost.wgl&&x(t)&&t.boost.wgl.allocateBuffer(t),t.boost.markerGroup&&t.xAxis&&t.xAxis.length>0&&t.yAxis&&t.yAxis.length>0&&t.boost.markerGroup.translate(t.xAxis[0].pos,t.yAxis[0].pos)}),p(t,"load",e,{order:-1}),p(t,"redraw",e);let i=-1,s=-1;p(t.pointer,"afterGetHoverData",e=>{let o=e.hoverPoint?.series;if(t.boost=t.boost||{},t.boost.markerGroup&&o){let e=t.inverted?o.yAxis:o.xAxis,r=t.inverted?o.xAxis:o.yAxis;(e&&e.pos!==i||r&&r.pos!==s)&&(t.series.forEach(t=>{t.halo?.hide()}),t.boost.markerGroup.translate(e.pos,r.pos),i=e.pos,s=r.pos)}})}let y={compose:function(t,e){return e&&b(m,"Boost.Chart")&&t.prototype.callbacks.push(A),t},getBoostClipRect:function(t,e){let i=t.navigator,s={x:t.plotLeft,y:t.plotTop,width:t.plotWidth,height:t.plotHeight};if(i&&t.inverted?(s.width+=i.top+i.height,i.opposite||(s.x=i.left)):i&&!t.inverted&&(s.height=i.top+i.height-t.plotTop),e.is){let{xAxis:i,yAxis:o}=e;if(s=t.getClipBox(e),t.inverted){let t=s.width;s.width=s.height,s.height=t,s.x=o.pos,s.y=i.pos}else s.x=i.pos,s.y=o.pos}if(e===t){let e=t.inverted?t.xAxis:t.yAxis;e.length<=1&&(s.y=Math.min(e[0].pos,s.y),s.height=e[0].pos-t.plotTop+e[0].len)}return s},isChartSeriesBoosting:x};var v=l(620),P=l.n(v);let T={area:"LINES",arearange:"LINES",areaspline:"LINES",column:"LINES",columnrange:"LINES",bar:"LINES",line:"LINE_STRIP",scatter:"POINTS",heatmap:"TRIANGLES",treemap:"TRIANGLES",bubble:"POINTS"},{clamp:C,error:S,pick:M}=u(),E=class{constructor(t){if(this.errors=[],this.uLocations={},this.gl=t,t&&!this.createShader())return}bind(){this.gl&&this.shaderProgram&&this.gl.useProgram(this.shaderProgram)}createShader(){let t=this.stringToProgram("#version 100\n#define LN10 2.302585092994046\nprecision highp float;\nattribute vec4 aVertexPosition;\nattribute vec4 aColor;\nvarying highp vec2 position;\nvarying highp vec4 vColor;\nuniform mat4 uPMatrix;\nuniform float pSize;\nuniform float translatedThreshold;\nuniform bool hasThreshold;\nuniform bool skipTranslation;\nuniform float xAxisTrans;\nuniform float xAxisMin;\nuniform float xAxisMinPad;\nuniform float xAxisPointRange;\nuniform float xAxisLen;\nuniform bool  xAxisPostTranslate;\nuniform float xAxisOrdinalSlope;\nuniform float xAxisOrdinalOffset;\nuniform float xAxisPos;\nuniform bool  xAxisCVSCoord;\nuniform bool  xAxisIsLog;\nuniform bool  xAxisReversed;\nuniform float yAxisTrans;\nuniform float yAxisMin;\nuniform float yAxisMinPad;\nuniform float yAxisPointRange;\nuniform float yAxisLen;\nuniform bool  yAxisPostTranslate;\nuniform float yAxisOrdinalSlope;\nuniform float yAxisOrdinalOffset;\nuniform float yAxisPos;\nuniform bool  yAxisCVSCoord;\nuniform bool  yAxisIsLog;\nuniform bool  yAxisReversed;\nuniform bool  isBubble;\nuniform bool  bubbleSizeByArea;\nuniform float bubbleZMin;\nuniform float bubbleZMax;\nuniform float bubbleZThreshold;\nuniform float bubbleMinSize;\nuniform float bubbleMaxSize;\nuniform bool  bubbleSizeAbs;\nuniform bool  isInverted;\nfloat bubbleRadius(){\nfloat value = aVertexPosition.w;\nfloat zMax = bubbleZMax;\nfloat zMin = bubbleZMin;\nfloat radius = 0.0;\nfloat pos = 0.0;\nfloat zRange = zMax - zMin;\nif (bubbleSizeAbs){\nvalue = value - bubbleZThreshold;\nzMax = max(zMax - bubbleZThreshold, zMin - bubbleZThreshold);\nzMin = 0.0;\n}\nif (value < zMin){\nradius = bubbleZMin / 2.0 - 1.0;\n} else {\npos = zRange > 0.0 ? (value - zMin) / zRange : 0.5;\nif (bubbleSizeByArea && pos > 0.0){\npos = sqrt(pos);\n}\nradius = ceil(bubbleMinSize + pos * (bubbleMaxSize - bubbleMinSize)) / 2.0;\n}\nreturn radius * 2.0;\n}\nfloat translate(float val,\nfloat pointPlacement,\nfloat localA,\nfloat localMin,\nfloat minPixelPadding,\nfloat pointRange,\nfloat len,\nbool  cvsCoord,\nbool  isLog,\nbool  reversed\n){\nfloat sign = 1.0;\nfloat cvsOffset = 0.0;\nif (cvsCoord) {\nsign *= -1.0;\ncvsOffset = len;\n}\nif (isLog) {\nval = log(val) / LN10;\n}\nif (reversed) {\nsign *= -1.0;\ncvsOffset -= sign * len;\n}\nreturn sign * (val - localMin) * localA + cvsOffset + \n(sign * minPixelPadding);\n}\nfloat xToPixels(float value) {\nif (skipTranslation){\nreturn value;// + xAxisPos;\n}\nreturn translate(value, 0.0, xAxisTrans, xAxisMin, xAxisMinPad, xAxisPointRange, xAxisLen, xAxisCVSCoord, xAxisIsLog, xAxisReversed);// + xAxisPos;\n}\nfloat yToPixels(float value, float checkTreshold) {\nfloat v;\nif (skipTranslation){\nv = value;// + yAxisPos;\n} else {\nv = translate(value, 0.0, yAxisTrans, yAxisMin, yAxisMinPad, yAxisPointRange, yAxisLen, yAxisCVSCoord, yAxisIsLog, yAxisReversed);// + yAxisPos;\nif (v > yAxisLen) {\nv = yAxisLen;\n}\n}\nif (checkTreshold > 0.0 && hasThreshold) {\nv = min(v, translatedThreshold);\n}\nreturn v;\n}\nvoid main(void) {\nif (isBubble){\ngl_PointSize = bubbleRadius();\n} else {\ngl_PointSize = pSize;\n}\nvColor = aColor;\nif (skipTranslation && isInverted) {\ngl_Position = uPMatrix * vec4(aVertexPosition.y + yAxisPos, aVertexPosition.x + xAxisPos, 0.0, 1.0);\n} else if (isInverted) {\ngl_Position = uPMatrix * vec4(yToPixels(aVertexPosition.y, aVertexPosition.z) + yAxisPos, xToPixels(aVertexPosition.x) + xAxisPos, 0.0, 1.0);\n} else {\ngl_Position = uPMatrix * vec4(xToPixels(aVertexPosition.x) + xAxisPos, yToPixels(aVertexPosition.y, aVertexPosition.z) + yAxisPos, 0.0, 1.0);\n}\n}","vertex"),e=this.stringToProgram("precision highp float;\nuniform vec4 fillColor;\nvarying highp vec2 position;\nvarying highp vec4 vColor;\nuniform sampler2D uSampler;\nuniform bool isCircle;\nuniform bool hasColor;\nvoid main(void) {\nvec4 col = fillColor;\nvec4 tcol = texture2D(uSampler, gl_PointCoord.st);\nif (hasColor) {\ncol = vColor;\n}\nif (isCircle) {\ncol *= tcol;\nif (tcol.r < 0.0) {\ndiscard;\n} else {\ngl_FragColor = col;\n}\n} else {\ngl_FragColor = col;\n}\n}","fragment"),i=t=>this.gl.getUniformLocation(this.shaderProgram,t);return t&&e?(this.shaderProgram=this.gl.createProgram(),this.gl.attachShader(this.shaderProgram,t),this.gl.attachShader(this.shaderProgram,e),this.gl.linkProgram(this.shaderProgram),this.gl.getProgramParameter(this.shaderProgram,this.gl.LINK_STATUS))?(this.gl.useProgram(this.shaderProgram),this.gl.bindAttribLocation(this.shaderProgram,0,"aVertexPosition"),this.pUniform=i("uPMatrix"),this.psUniform=i("pSize"),this.fcUniform=i("fillColor"),this.isBubbleUniform=i("isBubble"),this.bubbleSizeAbsUniform=i("bubbleSizeAbs"),this.bubbleSizeAreaUniform=i("bubbleSizeByArea"),this.uSamplerUniform=i("uSampler"),this.skipTranslationUniform=i("skipTranslation"),this.isCircleUniform=i("isCircle"),this.isInverted=i("isInverted"),!0):(this.errors.push(this.gl.getProgramInfoLog(this.shaderProgram)),this.handleErrors(),this.shaderProgram=!1,!1):(this.shaderProgram=!1,this.handleErrors(),!1)}handleErrors(){this.errors.length&&S("[highcharts boost] shader error - "+this.errors.join("\n"))}stringToProgram(t,e){let i=this.gl.createShader("vertex"===e?this.gl.VERTEX_SHADER:this.gl.FRAGMENT_SHADER);return(this.gl.shaderSource(i,t),this.gl.compileShader(i),this.gl.getShaderParameter(i,this.gl.COMPILE_STATUS))?i:(this.errors.push("when compiling "+e+" shader:\n"+this.gl.getShaderInfoLog(i)),!1)}destroy(){this.gl&&this.shaderProgram&&(this.gl.deleteProgram(this.shaderProgram),this.shaderProgram=!1)}fillColorUniform(){return this.fcUniform}getProgram(){return this.shaderProgram}pointSizeUniform(){return this.psUniform}perspectiveUniform(){return this.pUniform}reset(){this.gl&&this.shaderProgram&&(this.gl.uniform1i(this.isBubbleUniform,0),this.gl.uniform1i(this.isCircleUniform,0))}setBubbleUniforms(t,e,i,s=1){let o=t.options,r=Number.MAX_VALUE,n=-Number.MAX_VALUE;if(this.gl&&this.shaderProgram&&t.is("bubble")){let a=t.getPxExtremes();r=M(o.zMin,C(e,!1===o.displayNegative?o.zThreshold:-Number.MAX_VALUE,r)),n=M(o.zMax,Math.max(n,i)),this.gl.uniform1i(this.isBubbleUniform,1),this.gl.uniform1i(this.isCircleUniform,1),this.gl.uniform1i(this.bubbleSizeAreaUniform,"width"!==t.options.sizeBy),this.gl.uniform1i(this.bubbleSizeAbsUniform,t.options.sizeByAbsoluteValue),this.setUniform("bubbleMinSize",a.minPxSize*s),this.setUniform("bubbleMaxSize",a.maxPxSize*s),this.setUniform("bubbleZMin",r),this.setUniform("bubbleZMax",n),this.setUniform("bubbleZThreshold",t.options.zThreshold)}}setColor(t){this.gl&&this.shaderProgram&&this.gl.uniform4f(this.fcUniform,t[0]/255,t[1]/255,t[2]/255,t[3])}setDrawAsCircle(t){this.gl&&this.shaderProgram&&this.gl.uniform1i(this.isCircleUniform,+!!t)}setInverted(t){this.gl&&this.shaderProgram&&this.gl.uniform1i(this.isInverted,t)}setPMatrix(t){this.gl&&this.shaderProgram&&this.gl.uniformMatrix4fv(this.pUniform,!1,t)}setPointSize(t){this.gl&&this.shaderProgram&&this.gl.uniform1f(this.psUniform,t)}setSkipTranslation(t){this.gl&&this.shaderProgram&&this.gl.uniform1i(this.skipTranslationUniform,+(!0===t))}setTexture(t){this.gl&&this.shaderProgram&&this.gl.uniform1i(this.uSamplerUniform,t)}setUniform(t,e){if(this.gl&&this.shaderProgram){let i=this.uLocations[t]=this.uLocations[t]||this.gl.getUniformLocation(this.shaderProgram,t);this.gl.uniform1f(i,e)}}},k=class{constructor(t,e,i){this.buffer=!1,this.iterator=0,this.preAllocated=!1,this.vertAttribute=!1,this.components=i||2,this.dataComponents=i,this.gl=t,this.shader=e}allocate(t){this.iterator=-1,this.preAllocated=new Float32Array(4*t)}bind(){if(!this.buffer)return!1;this.gl.vertexAttribPointer(this.vertAttribute,this.components,this.gl.FLOAT,!1,0,0)}build(t,e,i){let s;return(this.data=t||[],this.data&&0!==this.data.length||this.preAllocated)?(this.components=i||this.components,this.buffer&&this.gl.deleteBuffer(this.buffer),this.preAllocated||(s=new Float32Array(this.data)),this.buffer=this.gl.createBuffer(),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,this.buffer),this.gl.bufferData(this.gl.ARRAY_BUFFER,this.preAllocated||s,this.gl.STATIC_DRAW),this.vertAttribute=this.gl.getAttribLocation(this.shader.getProgram(),e),this.gl.enableVertexAttribArray(this.vertAttribute),s=!1,!0):(this.destroy(),!1)}destroy(){this.buffer&&(this.gl.deleteBuffer(this.buffer),this.buffer=!1,this.vertAttribute=!1),this.iterator=0,this.components=this.dataComponents||2,this.data=[]}push(t,e,i,s){this.preAllocated&&(this.preAllocated[++this.iterator]=t,this.preAllocated[++this.iterator]=e,this.preAllocated[++this.iterator]=i,this.preAllocated[++this.iterator]=s)}render(t,e,i){let s=this.preAllocated?this.preAllocated.length:this.data.length;return!!this.buffer&&!!s&&((!t||t>s||t<0)&&(t=0),(!e||e>s)&&(e=s),!(t>=e)&&(i=i||"POINTS",this.gl.drawArrays(this.gl[i],t/this.components,(e-t)/this.components),!0))}},{parse:w}=P(),{doc:R,win:U}=u(),{isNumber:L,isObject:N,merge:_,objectEach:D,pick:z}=u(),G={column:!0,columnrange:!0,bar:!0,area:!0,areaspline:!0,arearange:!0},I={scatter:!0,bubble:!0},B=["webgl","experimental-webgl","moz-webgl","webkit-3d"];class O{static orthoMatrix(t,e){return[2/t,0,0,0,0,-(2/e),0,0,0,0,-2,0,-1,1,-1,1]}static seriesPointCount(t){let e,i,s;return t.boosted?(e=!!t.options.stacking,i=(t.getColumn("x").length?t.getColumn("x"):void 0)||t.options.xData||t.getColumn("x",!0),s=(e?t.data:i||t.options.data).length,"treemap"===t.type?s*=12:"heatmap"===t.type?s*=6:G[t.type]&&(s*=2),s):0}constructor(t){this.data=[],this.height=0,this.isInited=!1,this.markerData=[],this.series=[],this.textureHandles={},this.width=0,this.postRenderCallback=t,this.settings={pointSize:1,lineWidth:1,fillColor:"#AA00AA",useAlpha:!0,usePreallocated:!1,useGPUTranslations:!1,debug:{timeRendering:!1,timeSeriesProcessing:!1,timeSetup:!1,timeBufferCopy:!1,timeKDTree:!1,showSkipSummary:!1}}}getPixelRatio(){return this.settings.pixelRatio||U.devicePixelRatio||1}setOptions(t){"pixelRatio"in t||(t.pixelRatio=1),_(!0,this.settings,t)}allocateBuffer(t){let e=this.vbuffer,i=0;this.settings.usePreallocated&&(t.series.forEach(t=>{t.boosted&&(i+=O.seriesPointCount(t))}),e&&e.allocate(i))}allocateBufferForSingleSeries(t){let e=this.vbuffer,i=0;this.settings.usePreallocated&&(t.boosted&&(i=O.seriesPointCount(t)),e&&e.allocate(i))}clear(){let t=this.gl;t&&t.clear(t.COLOR_BUFFER_BIT|t.DEPTH_BUFFER_BIT)}pushSeriesData(t,e){let i=this.data,s=this.settings,o=this.vbuffer,r=t.pointArrayMap&&"low,high"===t.pointArrayMap.join(","),{chart:n,options:a,sorted:l,xAxis:h,yAxis:d}=t,u=!!a.stacking,c=a.data,g=t.xAxis.getExtremes(),m=g.min-(t.xAxis.minPointOffset||0),p=g.max+(t.xAxis.minPointOffset||0),f=t.yAxis.getExtremes(),b=f.min-(t.yAxis.minPointOffset||0),x=f.max+(t.yAxis.minPointOffset||0),A=(t.getColumn("x").length?t.getColumn("x"):void 0)||a.xData||t.getColumn("x",!0),y=(t.getColumn("y").length?t.getColumn("y"):void 0)||a.yData||t.getColumn("y",!0),v=(t.getColumn("z").length?t.getColumn("z"):void 0)||a.zData||t.getColumn("z",!0),P=!A||0===A.length,T=a.connectNulls,C=t.points||!1,S=u?t.data:A||c,M={x:Number.MAX_VALUE,y:0},E={x:-Number.MAX_VALUE,y:0},k=void 0===n.index,R=G[t.type],U=a.zoneAxis||"y",L=a.zones||!1,_=a.threshold,D=this.getPixelRatio(),z=t.chart.plotWidth,I=!1,B=!1,O,V,X=0,F=!1,H,Y,W,j,K=-1,Z=!1,q=!1,Q,J=!1,$=!1,tt=!1,te=!1,ti=!0,ts=!0,to,tr=!1,tn=!1,ta=0;if(a.boostData&&a.boostData.length>0)return;a.gapSize&&(tn="value"!==a.gapUnit?a.gapSize*t.closestPointRange:a.gapSize),L&&(to=[],L.forEach((t,e)=>{if(t.color){let i=w(t.color).rgba;i[0]/=255,i[1]/=255,i[2]/=255,to[e]=i,tr||void 0!==t.value||(tr=i)}}),tr||(tr=w(t.pointAttribs&&t.pointAttribs().fill||t.color).rgba,tr[0]/=255,tr[1]/=255,tr[2]/=255)),n.inverted&&(z=t.chart.plotHeight),t.closestPointRangePx=Number.MAX_VALUE;let tl=t=>{t&&(e.colorData.push(t[0]),e.colorData.push(t[1]),e.colorData.push(t[2]),e.colorData.push(t[3]))},th=(t,r,n,a=1,l)=>{tl(l),1!==D&&(!s.useGPUTranslations||e.skipTranslation)&&(t*=D,r*=D,a*=D),s.usePreallocated&&o?(o.push(t,r,+!!n,a),ta+=4):(i.push(t),i.push(r),i.push(n?D:0),i.push(a))},td=()=>{e.segments.length&&(e.segments[e.segments.length-1].to=i.length||ta)},tu=()=>{e.segments.length&&e.segments[e.segments.length-1].from===(i.length||ta)||(td(),e.segments.push({from:i.length||ta}))},tc=(t,e,i,s,o)=>{tl(o),th(t+i,e),tl(o),th(t,e),tl(o),th(t,e+s),tl(o),th(t,e+s),tl(o),th(t+i,e+s),tl(o),th(t+i,e)};if(tu(),C&&C.length>0){e.skipTranslation=!0,e.drawMode="TRIANGLES",C[0].node&&C[0].node.levelDynamic&&C.sort((t,e)=>{if(t.node){if(t.node.levelDynamic>e.node.levelDynamic)return 1;if(t.node.levelDynamic<e.node.levelDynamic)return -1}return 0}),C.forEach(e=>{let i,s,o=e.plotY;if(void 0!==o&&!isNaN(o)&&null!==e.y&&e.shapeArgs){let{x:o=0,y:r=0,width:a=0,height:l=0}=e.shapeArgs;i=(s=n.styledMode?e.series.colorAttribs(e):s=e.series.pointAttribs(e))["stroke-width"]||0,tt=w(s.fill).rgba,tt[0]/=255,tt[1]/=255,tt[2]/=255,t.is("treemap")&&(i=i||1,V=w(s.stroke).rgba,V[0]/=255,V[1]/=255,V[2]/=255,tc(o,r,a,l,V),i/=2),t.is("heatmap")&&n.inverted&&(o=h.len-o,r=d.len-r,a=-a,l=-l),tc(o+i,r+i,a-2*i,l-2*i,tt)}}),td();return}for(;K<S.length-1;){if(void 0===(W=S[++K]))continue;if(k)break;let i=c&&c[K];if(!P&&N(i,!0)&&i.color&&(tt=w(i.color).rgba,tt[0]/=255,tt[1]/=255,tt[2]/=255),P?(H=W[0],Y=W[1],S[K+1]&&(q=S[K+1][0]),S[K-1]&&(Z=S[K-1][0]),W.length>=3&&(j=W[2],W[2]>e.zMax&&(e.zMax=W[2]),W[2]<e.zMin&&(e.zMin=W[2]))):(H=W,Y=y?.[K],S[K+1]&&(q=S[K+1]),S[K-1]&&(Z=S[K-1]),v&&v.length&&(j=v[K],v[K]>e.zMax&&(e.zMax=v[K]),v[K]<e.zMin&&(e.zMin=v[K]))),!T&&(null===H||null===Y)){tu();continue}if(q&&q>=m&&q<=p&&(J=!0),Z&&Z>=m&&Z<=p&&($=!0),r?(P&&(Y=W.slice(1,3)),Q=t.getColumn("low",!0)?.[K],Y=t.getColumn("high",!0)?.[K]||0):u&&(H=W.x,Q=(Y=W.stackY)-W.y),null!=b&&null!=x&&(ti=Y>=b&&Y<=x),!l&&!ti||(H>p&&E.x<p&&(E.x=H,E.y=Y),H<m&&M.x>m&&(M.x=H,M.y=Y),null===Y&&T))continue;if(null===Y||!ti&&S.length>1&&!J&&!$){tu();continue}if((l&&(q>=m||H>=m)&&(Z<=p||H<=p)||!l&&H>=m&&H<=p)&&(te=!0),te||J||$){if(tn&&H-Z>tn&&tu(),L){let t;L.some((e,i)=>{let s=L[i-1];return"x"===U?void 0!==e.value&&H<=e.value&&(to[i]&&(!s||H>=s.value)&&(t=to[i]),!0):void 0!==e.value&&Y<=e.value&&(to[i]&&(!s||Y>=s.value)&&(t=to[i]),!0)}),tt=t||tr||tt}if(s.useGPUTranslations||(e.skipTranslation=!0,H=h.toPixels(H,!0),Y=d.toPixels(Y,!0),!(H>z)||"POINTS"!==e.drawMode)){if(e.hasMarkers&&te&&!1!==I&&(t.closestPointRangePx=Math.min(t.closestPointRangePx,Math.abs(H-I))),!s.useGPUTranslations&&!s.usePreallocated&&I&&1>Math.abs(H-I)&&B&&1>Math.abs(Y-B)){s.debug.showSkipSummary&&++X;continue}R&&(O=Q||0,(!1===Q||void 0===Q)&&(O=Y<0?Y:0),(r||u)&&!d.logarithmic||(O=Math.max(null===_?b:_,b)),s.useGPUTranslations||(O=d.toPixels(O,!0)),th(H,O,0,0,tt)),a.step&&!ts&&th(H,B,0,2,tt),th(H,Y,0,"bubble"===t.type?j||1:2,tt),I=H,B=Y,F=!0,ts=!1}}}s.debug.showSkipSummary&&console.log("skipped points:",X);let tg=(t,i)=>{if(s.useGPUTranslations||(e.skipTranslation=!0,t.x=h.toPixels(t.x,!0),t.y=d.toPixels(t.y,!0)),i){this.data=[t.x,t.y,0,2].concat(this.data);return}th(t.x,t.y,0,2)};!F&&!1!==T&&"line_strip"===t.drawMode&&(M.x<Number.MAX_VALUE&&tg(M,!0),E.x>-Number.MAX_VALUE&&tg(E)),td()}pushSeries(t){let e=this.markerData,i=this.series,s=this.settings;i.length>0&&i[i.length-1].hasMarkers&&(i[i.length-1].markerTo=e.length),s.debug.timeSeriesProcessing&&console.time("building "+t.type+" series");let o={segments:[],markerFrom:e.length,colorData:[],series:t,zMin:Number.MAX_VALUE,zMax:-Number.MAX_VALUE,hasMarkers:!!t.options.marker&&!1!==t.options.marker.enabled,showMarkers:!0,drawMode:T[t.type]||"LINE_STRIP"};t.index>=i.length?i.push(o):i[t.index]=o,this.pushSeriesData(t,o),s.debug.timeSeriesProcessing&&console.timeEnd("building "+t.type+" series")}flush(){let t=this.vbuffer;this.data=[],this.markerData=[],this.series=[],t&&t.destroy()}setXAxis(t){let e=this.shader;if(!e)return;let i=this.getPixelRatio();e.setUniform("xAxisTrans",t.transA*i),e.setUniform("xAxisMin",t.min),e.setUniform("xAxisMinPad",t.minPixelPadding*i),e.setUniform("xAxisPointRange",t.pointRange),e.setUniform("xAxisLen",t.len*i),e.setUniform("xAxisPos",t.pos*i),e.setUniform("xAxisCVSCoord",!t.horiz),e.setUniform("xAxisIsLog",!!t.logarithmic),e.setUniform("xAxisReversed",!!t.reversed)}setYAxis(t){let e=this.shader;if(!e)return;let i=this.getPixelRatio();e.setUniform("yAxisTrans",t.transA*i),e.setUniform("yAxisMin",t.min),e.setUniform("yAxisMinPad",t.minPixelPadding*i),e.setUniform("yAxisPointRange",t.pointRange),e.setUniform("yAxisLen",t.len*i),e.setUniform("yAxisPos",t.pos*i),e.setUniform("yAxisCVSCoord",!t.horiz),e.setUniform("yAxisIsLog",!!t.logarithmic),e.setUniform("yAxisReversed",!!t.reversed)}setThreshold(t,e){let i=this.shader;i&&(i.setUniform("hasThreshold",t),i.setUniform("translatedThreshold",e))}renderChart(t){let e=this.gl,i=this.settings,s=this.shader,o=this.vbuffer,r=this.getPixelRatio();if(!t)return!1;this.width=t.chartWidth*r,this.height=t.chartHeight*r;let n=this.height,a=this.width;if(!e||!s||!a||!n)return!1;i.debug.timeRendering&&console.time("gl rendering"),e.canvas.width=a,e.canvas.height=n,s.bind(),e.viewport(0,0,a,n),s.setPMatrix(O.orthoMatrix(a,n)),i.lineWidth>1&&!u().isMS&&e.lineWidth(i.lineWidth),o&&(o.build(this.data,"aVertexPosition",4),o.bind()),s.setInverted(t.inverted),this.series.forEach((n,a)=>{let l=n.series.options,h=l.marker,d=void 0!==l.lineWidth?l.lineWidth:1,u=l.threshold,c=L(u),g=n.series.yAxis.getThreshold(u),m=z(l.marker?l.marker.enabled:null,!!n.series.xAxis.isRadial||null,n.series.closestPointRangePx>2*((l.marker?l.marker.radius:10)||10)),p=this.textureHandles[h&&h.symbol||n.series.symbol]||this.textureHandles.circle,f,b,x,A=[];if(0!==n.segments.length&&n.segments[0].from!==n.segments[0].to&&(p.isReady&&(e.bindTexture(e.TEXTURE_2D,p.handle),s.setTexture(p.handle)),t.styledMode?n.series.markerGroup===n.series.chart.boost?.markerGroup?(delete n.series.markerGroup,n.series.markerGroup=n.series.plotGroup("markerGroup","markers","visible",1,t.seriesGroup).addClass("highcharts-tracker"),x=n.series.markerGroup.getStyle("fill"),n.series.markerGroup.destroy(),n.series.markerGroup=n.series.chart.boost?.markerGroup):x=n.series.markerGroup?.getStyle("fill"):(x="POINTS"===n.drawMode&&n.series.pointAttribs&&n.series.pointAttribs().fill||n.series.color,l.colorByPoint&&(x=n.series.chart.options.colors[a])),n.series.fillOpacity&&l.fillOpacity&&(x=new(P())(x).setOpacity(z(l.fillOpacity,1)).get()),A=w(x).rgba,i.useAlpha||(A[3]=1),"add"===l.boostBlending?(e.blendFunc(e.SRC_ALPHA,e.ONE),e.blendEquation(e.FUNC_ADD)):"mult"===l.boostBlending||"multiply"===l.boostBlending?e.blendFunc(e.DST_COLOR,e.ZERO):"darken"===l.boostBlending?(e.blendFunc(e.ONE,e.ONE),e.blendEquation(e.FUNC_MIN)):e.blendFuncSeparate(e.SRC_ALPHA,e.ONE_MINUS_SRC_ALPHA,e.ONE,e.ONE_MINUS_SRC_ALPHA),s.reset(),n.colorData.length>0?(s.setUniform("hasColor",1),(b=new k(e,s)).build(Array(n.segments[0].from).concat(n.colorData),"aColor",4),b.bind()):(s.setUniform("hasColor",0),e.disableVertexAttribArray(e.getAttribLocation(s.getProgram(),"aColor"))),s.setColor(A),this.setXAxis(n.series.xAxis),this.setYAxis(n.series.yAxis),this.setThreshold(c,g),"POINTS"===n.drawMode&&s.setPointSize(2*z(l.marker&&l.marker.radius,.5)*r),s.setSkipTranslation(n.skipTranslation),"bubble"===n.series.type&&s.setBubbleUniforms(n.series,n.zMin,n.zMax,r),s.setDrawAsCircle(I[n.series.type]||!1),o)){if(d>0||"LINE_STRIP"!==n.drawMode)for(f=0;f<n.segments.length;f++)o.render(n.segments[f].from,n.segments[f].to,n.drawMode);if(n.hasMarkers&&m)for(s.setPointSize(2*z(l.marker&&l.marker.radius,5)*r),s.setDrawAsCircle(!0),f=0;f<n.segments.length;f++)o.render(n.segments[f].from,n.segments[f].to,"POINTS")}}),i.debug.timeRendering&&console.timeEnd("gl rendering"),this.postRenderCallback&&this.postRenderCallback(this),this.flush()}render(t){if(this.clear(),t.renderer.forExport)return this.renderChart(t);this.isInited?this.renderChart(t):setTimeout(()=>{this.render(t)},1)}setSize(t,e){let i=this.shader;i&&(this.width!==t||this.height!==e)&&(this.width=t,this.height=e,i.bind(),i.setPMatrix(O.orthoMatrix(t,e)))}init(t,e){let i=this.settings;if(this.isInited=!1,!t)return!1;i.debug.timeSetup&&console.time("gl setup");for(let e=0;e<B.length&&(this.gl=t.getContext(B[e],{}),!this.gl);++e);let s=this.gl;if(!s)return!1;e||this.flush(),s.enable(s.BLEND),s.blendFunc(s.SRC_ALPHA,s.ONE_MINUS_SRC_ALPHA),s.disable(s.DEPTH_TEST),s.depthFunc(s.LESS);let o=this.shader=new E(s);if(!o)return!1;this.vbuffer=new k(s,o);let r=(t,e)=>{let i={isReady:!1,texture:R.createElement("canvas"),handle:s.createTexture()},o=i.texture.getContext("2d");this.textureHandles[t]=i,i.texture.width=512,i.texture.height=512,o.mozImageSmoothingEnabled=!1,o.webkitImageSmoothingEnabled=!1,o.msImageSmoothingEnabled=!1,o.imageSmoothingEnabled=!1,o.strokeStyle="rgba(255, 255, 255, 0)",o.fillStyle="#FFF",e(o);try{s.activeTexture(s.TEXTURE0),s.bindTexture(s.TEXTURE_2D,i.handle),s.texImage2D(s.TEXTURE_2D,0,s.RGBA,s.RGBA,s.UNSIGNED_BYTE,i.texture),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_WRAP_S,s.CLAMP_TO_EDGE),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_WRAP_T,s.CLAMP_TO_EDGE),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_MAG_FILTER,s.LINEAR),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_MIN_FILTER,s.LINEAR),s.bindTexture(s.TEXTURE_2D,null),i.isReady=!0}catch(t){}};return r("circle",t=>{t.beginPath(),t.arc(256,256,256,0,2*Math.PI),t.stroke(),t.fill()}),r("square",t=>{t.fillRect(0,0,512,512)}),r("diamond",t=>{t.beginPath(),t.moveTo(256,0),t.lineTo(512,256),t.lineTo(256,512),t.lineTo(0,256),t.lineTo(256,0),t.fill()}),r("triangle",t=>{t.beginPath(),t.moveTo(0,512),t.lineTo(256,0),t.lineTo(512,512),t.lineTo(0,512),t.fill()}),r("triangle-down",t=>{t.beginPath(),t.moveTo(0,0),t.lineTo(256,512),t.lineTo(512,0),t.lineTo(0,0),t.fill()}),this.isInited=!0,i.debug.timeSetup&&console.timeEnd("gl setup"),!0}destroy(){let t=this.gl,e=this.shader,i=this.vbuffer;this.flush(),i&&i.destroy(),e&&e.destroy(),t&&(D(this.textureHandles,e=>{e.handle&&t.deleteTexture(e.handle)}),t.canvas.width=1,t.canvas.height=1)}}!function(t){t.setLength=function(t,e,i){return Array.isArray(t)?(t.length=e,t):t[i?"subarray":"slice"](0,e)},t.splice=function(t,e,i,s,o=[]){if(Array.isArray(t))return Array.isArray(o)||(o=Array.from(o)),{removed:t.splice(e,i,...o),array:t};let r=Object.getPrototypeOf(t).constructor,n=t[s?"subarray":"slice"](e,e+i),a=new r(t.length-i+o.length);return a.set(t.subarray(0,e),0),a.set(o,e),a.set(t.subarray(e+i),e+o.length),{removed:n,array:a}}}(o||(o={}));let{setLength:V,splice:X}=o,{fireEvent:F,objectEach:H,uniqueKey:Y}=u(),W=class{constructor(t={}){this.autoId=!t.id,this.columns={},this.id=t.id||Y(),this.modified=this,this.rowCount=0,this.versionTag=Y();let e=0;H(t.columns||{},(t,i)=>{this.columns[i]=t.slice(),e=Math.max(e,t.length)}),this.applyRowCount(e)}applyRowCount(t){this.rowCount=t,H(this.columns,(e,i)=>{e.length!==t&&(this.columns[i]=V(e,t))})}deleteRows(t,e=1){if(e>0&&t<this.rowCount){let i=0;H(this.columns,(s,o)=>{this.columns[o]=X(s,t,e).array,i=s.length}),this.rowCount=i}F(this,"afterDeleteRows",{rowIndex:t,rowCount:e}),this.versionTag=Y()}getColumn(t,e){return this.columns[t]}getColumns(t,e){return(t||Object.keys(this.columns)).reduce((t,e)=>(t[e]=this.columns[e],t),{})}getRow(t,e){return(e||Object.keys(this.columns)).map(e=>this.columns[e]?.[t])}setColumn(t,e=[],i=0,s){this.setColumns({[t]:e},i,s)}setColumns(t,e,i){let s=this.rowCount;H(t,(t,e)=>{this.columns[e]=t.slice(),s=t.length}),this.applyRowCount(s),i?.silent||(F(this,"afterSetColumns"),this.versionTag=Y())}setRow(t,e=this.rowCount,i,s){let{columns:o}=this,r=i?this.rowCount+1:e+1;H(t,(t,n)=>{let a=o[n]||s?.addColumns!==!1&&Array(r);a&&(i?a=X(a,e,0,!0,[t]).array:a[e]=t,o[n]=a)}),r>this.rowCount&&this.applyRowCount(r),s?.silent||(F(this,"afterSetRows"),this.versionTag=Y())}},{getBoostClipRect:j,isChartSeriesBoosting:K}=y,{getOptions:Z}=u(),{composed:q,doc:Q,noop:J,win:$}=u(),{addEvent:tt,destroyObjectProperties:te,error:ti,extend:ts,fireEvent:to,isArray:tr,isNumber:tn,pick:ta,pushUnique:tl,wrap:th,defined:td}=u();function tu(t,e){let i=e.boost;t&&i&&i.target&&i.canvas&&!K(e.chart)&&t.allocateBufferForSingleSeries(e)}function tc(t){return ta(t&&t.options&&t.options.boost&&t.options.boost.enabled,!0)}function tg(t,e){let i=t.constructor,o=t.seriesGroup||e.group,r=t.chartWidth,n=t.chartHeight,a=t,l="undefined"!=typeof SVGForeignObjectElement,h=!1;K(t)?a=t:(a=e,h=!!(e.options.events?.click||e.options.point?.events?.click));let d=a.boost=a.boost||{};if(l=!1,s||(s=Q.createElement("canvas")),!d.target&&(d.canvas=s,t.renderer.forExport||!l?(a.renderTarget=d.target=t.renderer.image("",0,0,r,n).addClass("highcharts-boost-canvas").add(o),d.clear=function(){d.target.attr({href:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="})},d.copy=function(){d.resize(),d.target.attr({href:d.canvas.toDataURL("image/png")})}):(d.targetFo=t.renderer.createElement("foreignObject").add(o),a.renderTarget=d.target=Q.createElement("canvas"),d.targetCtx=d.target.getContext("2d"),d.targetFo.element.appendChild(d.target),d.clear=function(){d.target.width=d.canvas.width,d.target.height=d.canvas.height},d.copy=function(){d.target.width=d.canvas.width,d.target.height=d.canvas.height,d.targetCtx.drawImage(d.canvas,0,0)}),d.resize=function(){r=t.chartWidth,n=t.chartHeight,(d.targetFo||d.target).attr({x:0,y:0,width:r,height:n}).css({pointerEvents:h?void 0:"none",mixedBlendMode:"normal",opacity:1}).addClass(h?"highcharts-tracker":""),a instanceof i&&a.boost?.markerGroup?.translate(t.plotLeft,t.plotTop)},d.clipRect=t.renderer.clipRect(),(d.targetFo||d.target).attr({zIndex:e.options.zIndex}),a instanceof i&&(a.boost.markerGroup=a.renderer.g().add(o).translate(e.xAxis.pos,e.yAxis.pos))),d.canvas.width=r,d.canvas.height=n,d.clipRect){let e=j(t,a),i=e.width===t.clipBox.width&&e.height===t.clipBox.height?o:d.targetFo||d.target;d.clipRect.attr(e),i?.clip(d.clipRect)}return d.resize(),d.clear(),!d.wgl&&(d.wgl=new O(t=>{t.settings.debug.timeBufferCopy&&console.time("buffer copy"),d.copy(),t.settings.debug.timeBufferCopy&&console.timeEnd("buffer copy")}),d.wgl.init(d.canvas)||ti("[highcharts boost] - unable to init WebGL renderer"),d.wgl.setOptions(t.options.boost||{}),a instanceof i&&d.wgl.allocateBuffer(t)),d.wgl.setSize(r,n),d.wgl}function tm(t){let e=t.points;if(e){let t,i;for(i=0;i<e.length;i+=1)(t=e[i])&&t.destroyElements&&t.destroyElements()}for(let e of(["graph","area","tracker"].forEach(e=>{let i=t[e];i&&(t[e]=i.destroy())}),t.zones))te(e,void 0,!0)}function tp(t,e,i,s,o,r){let n=(o=o||0)+(s=s||3e3),a=!0;for(;a&&o<n&&o<t.length;)a=e(t[o],o),++o;a&&(o<t.length?r?tp(t,e,i,s,o,r):$.requestAnimationFrame?$.requestAnimationFrame(function(){tp(t,e,i,s,o)}):setTimeout(tp,0,t,e,i,s,o):i&&i())}function tf(t,e){let i=t.options,s=t.dataTable.modified.rowCount,o=t.xAxis&&t.xAxis.options,r=t.yAxis&&t.yAxis.options,n=t.colorAxis&&t.colorAxis.options;return s>(i.boostThreshold||Number.MAX_VALUE)&&tn(r.min)&&tn(r.max)&&(!e||tn(o.min)&&tn(o.max))&&(!n||tn(n.min)&&tn(n.max))}let tb=(t,e)=>!t.forceCrop&&(K(t.chart)||(e?e.length:0)>=(t.options.boostThreshold||Number.MAX_VALUE));function tx(){let t=this,e=t.chart;e.boost&&e.boost.markerGroup===t.markerGroup&&(t.markerGroup=void 0),e.hoverPoints&&(e.hoverPoints=e.hoverPoints.filter(function(e){return e.series===t})),e.hoverPoint&&e.hoverPoint.series===t&&(e.hoverPoint=void 0)}function tA(){let t=this.boost;t&&t.canvas&&t.target&&(t.wgl&&t.wgl.clear(),t.clear&&t.clear())}function ty(t){let e=t.boost;e&&e.canvas&&e.target&&e.wgl&&!K(t.chart)&&e.wgl.render(t.chart)}function tv(t,e){let i=t.options,s=t.xAxis,o=t.pointClass;if(e instanceof o)return e;let r=t.is("scatter"),n=(r&&t.getColumn("x",!0).length?t.getColumn("x",!0):void 0)||(t.getColumn("x").length?t.getColumn("x"):void 0)||i.xData||t.getColumn("x",!0)||!1,a=t.getColumn("y",!0)||i.yData||!1,l=new o(t,r&&n&&a?[n[e.i],a[e.i]]:(tr(t.options.data)?t.options.data:[])[e.i],n?n[e.i]:void 0);return l.category=ta(s.categories?s.categories[l.x]:l.x,l.x),l.key=l.name??l.category,l.dist=e.dist,l.distX=e.distX,l.plotX=e.plotX,l.plotY=e.plotY,l.index=e.i,l.percentage=e.percentage,l.isInside=t.isPointInside(l),l}function tP(t){let{options:e,xAxis:i,yAxis:s}=this;if(!this.isDirty&&!i.isDirty&&!s.isDirty&&!t)return!1;this.yAxis.setTickInterval();let o=e.boostThreshold||0,r=e.cropThreshold,n=this.getColumn("x"),a=i.getExtremes(),l=a.max??Number.MAX_VALUE,h=a.min??-Number.MAX_VALUE,d=this.getColumn("y"),u=s.getExtremes(),c=u.max??Number.MAX_VALUE,g=u.min??-Number.MAX_VALUE;if(!this.boosted&&i.old&&s.old&&h>=(i.old.min??-Number.MAX_VALUE)&&l<=(i.old.max??Number.MAX_VALUE)&&g>=(s.old.min??-Number.MAX_VALUE)&&c<=(s.old.max??Number.MAX_VALUE))return this.dataTable.modified.setColumns({x:n,y:d}),!0;let m=this.dataTable.rowCount;if(!o||m<o||r&&!this.forceCrop&&!this.getExtremesFromAll&&!e.getExtremesFromAll&&m<r)return this.dataTable.modified.setColumns({x:n,y:d}),!0;let p=[],f=[],b=[],x=!(tn(a.max)||tn(a.min)),A=!(tn(u.max)||tn(u.min)),y=!1,v,P=n[0],T=n[0],C,S=d?.[0],M=d?.[0];for(let t=0,e=n.length;t<e;++t)v=n[t],C=d?.[t],v>=h&&v<=l&&C>=g&&C<=c?(p.push({x:v,y:C}),f.push(v),b.push(C),x&&(P=Math.max(P,v),T=Math.min(T,v)),A&&(S=Math.max(S,C),M=Math.min(M,C))):y=!0;return x&&(i.dataMax=Math.max(P,i.dataMax||0),i.dataMin=Math.min(T,i.dataMin||0)),A&&(s.dataMax=Math.max(S,s.dataMax||0),s.dataMin=Math.min(M,s.dataMin||0)),this.cropped=y,this.cropStart=0,y&&this.dataTable.modified===this.dataTable&&(this.dataTable.modified=new W),this.dataTable.modified.setColumns({x:f,y:b}),tb(this,f)||(this.processedData=p),!0}function tT(){let t=this.options||{},e=this.chart,s=e.boost,o=this.boost,r=this.xAxis,n=this.yAxis,a=t.xData||this.getColumn("x",!0),l=t.yData||this.getColumn("y",!0),h=this.getColumn("low",!0),d=this.getColumn("high",!0),u=this.processedData||t.data,c=r.getExtremes(),g=c.min-(r.minPointOffset||0),m=c.max+(r.minPointOffset||0),p=n.getExtremes(),f=p.min-(n.minPointOffset||0),b=p.max+(n.minPointOffset||0),x={},A=!!this.sampling,y=t.enableMouseTracking,v=t.threshold,P=this.pointArrayMap&&"low,high"===this.pointArrayMap.join(","),T=!!t.stacking,C=this.cropStart||0,S=this.requireSorting,M=!a,E="x"===t.findNearestPointBy,k=(this.getColumn("x").length?this.getColumn("x"):void 0)||this.options.xData||this.getColumn("x",!0),w=ta(t.lineWidth,1),R=t.nullInteraction&&f,U=e.tooltip,L=!1,N,_=n.getThreshold(v),D,z,G,I;if(!this.boosted||(this.points?.forEach(t=>{t?.destroyElements?.()}),this.points=[],U&&!U.isHidden?(e.hoverPoint?.series===this||e.hoverPoints?.some(t=>t.series===this))&&(e.hoverPoint=e.hoverPoints=void 0,U.hide(0)):e.hoverPoints&&(e.hoverPoints=e.hoverPoints.filter(t=>t.series!==this)),r.isPanning||n.isPanning)||(L=tg(e,this),e.boosted=!0,!this.visible))return;(this.points||this.graph)&&tm(this),K(e)?(this.markerGroup&&this.markerGroup!==s?.markerGroup&&this.markerGroup.destroy(),this.markerGroup=s?.markerGroup,o&&o.target&&(this.renderTarget=o.target=o.target.destroy())):(this.markerGroup===s?.markerGroup&&(this.markerGroup=void 0),this.markerGroup=this.plotGroup("markerGroup","markers","visible",1,e.seriesGroup).addClass("highcharts-tracker"));let B=this.points=[],O=(t,s,o,a)=>{let l=!!k&&k[C+o],h=t=>{e.inverted&&(t=r.len-t,s=n.len-s),B.push({destroy:J,x:l,clientX:t,plotX:t,plotY:s,i:C+o,percentage:a})};t=Math.ceil(t),i=E?t:t+","+s,y&&(x[i]?l===k[k.length-1]&&(B.length--,h(t)):(x[i]=!0,h(t)))};this.buildKDTree=J,to(this,"renderCanvas"),this.is("line")&&w>1&&o?.target&&s&&!s.lineWidthFilter&&(s.lineWidthFilter=e.renderer.definition({tagName:"filter",children:[{tagName:"feMorphology",attributes:{operator:"dilate",radius:.25*w}}],attributes:{id:"linewidth"}}),o.target.attr({filter:"url(#linewidth)"})),L&&(tu(L,this),L.pushSeries(this),ty(this));let V=L.settings;e.renderer.forExport||(V.debug.timeKDTree&&console.time("kd tree building"),tp(T?this.data.slice(C):a||u,function(t,i){let s=void 0===e.index,o,a,u,c,p,x=!1,y=!0;return!td(t)||(!s&&(M?(o=t[0],a=t[1]):(o=t,a=l[i]??R??null),P?(M&&(a=t.slice(1,3)),x=h[i],a=d[i]):T&&(o=t.x,x=(a=t.stackY)-t.y,p=t.percentage),S||(y=(a||0)>=f&&a<=b),null!==a&&o>=g&&o<=m&&y&&(u=r.toPixels(o,!0),A?((void 0===G||u===N)&&(P||(x=a),(void 0===I||a>z)&&(z=a,I=i),(void 0===G||x<D)&&(D=x,G=i)),E&&u===N||(void 0!==G&&(c=n.toPixels(z,!0),_=n.toPixels(D,!0),O(u,c,I,p),_!==c&&O(u,_,G,p)),G=I=void 0,N=u)):O(u,c=Math.ceil(n.toPixels(a,!0)),i,p))),!s)},()=>{to(this,"renderedCanvas"),delete this.buildKDTree,this.options&&this.buildKDTree(),V.debug.timeKDTree&&console.timeEnd("kd tree building")}))}function tC(t){let e=!0;if(this.chart.options&&this.chart.options.boost&&(e=void 0===this.chart.options.boost.enabled||this.chart.options.boost.enabled),!e||!this.boosted)return t.call(this);this.chart.boosted=!0;let i=tg(this.chart,this);i&&(tu(i,this),i.pushSeries(this)),ty(this)}function tS(t){if(this.boosted){if(tf(this))return{};if(this.xAxis.isPanning||this.yAxis.isPanning)return this}return t.apply(this,[].slice.call(arguments,1))}function tM(t){let e=this.options.data;if(tc(this.chart)&&g[this.type]){let s=this.is("scatter")&&!this.is("bubble")&&!this.is("treemap")&&!this.is("heatmap");if(!tb(this,e)||s||this.is("treemap")||this.options.stacking||!tf(this,!0)){if(this.boosted&&(this.xAxis?.isPanning||this.yAxis?.isPanning))return;s&&"treegrid"!==this.yAxis.type?tP.call(this,arguments[1]):t.apply(this,[].slice.call(arguments,1)),e=this.getColumn("x",!0)}if(this.boosted=tb(this,e),this.boosted){let t;this.options.data?.length&&(tn(t=this.getFirstValidPoint(this.options.data))||tr(t)||this.is("treemap")||ti(12,!1,this.chart));var i=this;i.boost=i.boost||{getPoint:t=>tv(i,t)};let e=i.boost.altered=[];if(["allowDG","directTouch","stickyTracking"].forEach(t=>{e.push({prop:t,val:i[t],own:Object.hasOwnProperty.call(i,t)})}),i.allowDG=!1,i.directTouch=!1,i.stickyTracking=!0,i.finishedAnimating=!0,i.labelBySeries&&(i.labelBySeries=i.labelBySeries.destroy()),i.is("scatter")&&!i.is("treemap")&&i.data.length){for(let t of i.data)t?.destroy?.();i.data.length=0,i.points.length=0,delete i.processedData}}else!function(t){let e=t.boost,i=t.chart,s=i.boost;if(s?.markerGroup)for(let t of(s.markerGroup.destroy(),s.markerGroup=void 0,i.series))t.markerGroup=void 0,t.markerGroup=t.plotGroup("markerGroup","markers","visible",1,i.seriesGroup).addClass("highcharts-tracker");e&&((e.altered||[]).forEach(e=>{e.own?t[e.prop]=e.val:delete t[e.prop]}),e.clear&&e.clear()),(i.seriesGroup||t.group)?.clip()}(this)}else t.apply(this,[].slice.call(arguments,1))}function tE(t){let e=t.apply(this,[].slice.call(arguments,1));return this.boost&&e?this.boost.getPoint(e):e}let tk={compose:function(t,e,i,s){if(tl(q,"Boost.Series")){let o=Z().plotOptions,r=t.prototype;if(tt(t,"destroy",tx),tt(t,"hide",tA),s&&(r.renderCanvas=tT),th(r,"getExtremes",tS),th(r,"processData",tM),th(r,"searchPoint",tE),["translate","generatePoints","drawTracker","drawPoints","render"].forEach(t=>(function(t,e,i){function s(t){let e=this.options.stacking&&("translate"===i||"generatePoints"===i);this.boosted&&!e&&tc(this.chart)&&"heatmap"!==this.type&&"treemap"!==this.type&&g[this.type]&&0!==this.options.boostThreshold?"render"===i&&this.renderCanvas&&this.renderCanvas():t.call(this)}if(th(t,i,s),"translate"===i)for(let t of["column","arearange","columnrange","heatmap","treemap"])e[t]&&th(e[t].prototype,i,s)})(r,e,t)),th(i.prototype,"firePointEvent",function(t,e,i){if("click"===e&&this.series.boosted){let t=i.point;if((t.dist||t.distX)>=(t.series.options.marker?.radius??10))return}return t.apply(this,[].slice.call(arguments,1))}),c.forEach(t=>{let i=o[t];i&&(i.boostThreshold=5e3,i.boostData=[],e[t].prototype.fillOpacity=!0)}),s){let{area:t,areaspline:i,bubble:s,column:o,heatmap:r,scatter:n,treemap:a}=e;if(t&&ts(t.prototype,{fill:!0,fillOpacity:!0,sampling:!0}),i&&ts(i.prototype,{fill:!0,fillOpacity:!0,sampling:!0}),s){let t=s.prototype;delete t.buildKDTree,th(t,"markerAttribs",function(t){return!this.boosted&&t.apply(this,[].slice.call(arguments,1))})}o&&ts(o.prototype,{fill:!0,sampling:!0}),n&&(n.prototype.fill=!0),[r,a].forEach(t=>{t&&th(t.prototype,"drawPoints",tC)})}}return t},destroyGraphics:tm,eachAsync:tp,getPoint:tv},{getBoostClipRect:tw,isChartSeriesBoosting:tR}=y,{destroyGraphics:tU}=tk,{parse:tL}=P(),{doc:tN,noop:t_}=u(),{addEvent:tD,fireEvent:tz,isNumber:tG,merge:tI,pick:tB,wrap:tO}=u();!function(t){let e,i,s="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=";function o(t,e,i,s,o){o&&e!==o.clientX&&(t.moveTo(o.clientX,o.yBottom),t.lineTo(o.clientX,o.plotY),t.lineTo(e,i),t.lineTo(e,s))}function r(t,e,i,s,o){t.moveTo(e,i),t.arc(e,i,this.radii&&this.radii[o],0,2*Math.PI,!1)}function n(t,e,i,s){t.rect(e-1,i,1,s-i)}function a(){this.boost&&this.boost.copy&&this.boost.copy()}function l(){let t=this.boost||{};t.target&&t.target.attr({href:s}),t.canvas&&t.canvas.getContext("2d").clearRect(0,0,t.canvas.width,t.canvas.height)}function h(){tR(this.chart)?this.boost&&this.boost.clear&&this.boost.clear():this.boost&&this.boost.copy?this.boost.copy():this.chart.boost&&this.chart.boost.copy&&this.chart.boost.copy()}function d(t,e,i){t.lineTo(e,i)}function c(){let t,i=this.chart,o=tR(i)?i:this,r=o===i?i.seriesGroup:i.seriesGroup||this.group,n=i.chartWidth,a=i.chartHeight,l=function(t,e,i,s,o,r,n){t.call(this,i,e,s,o,r,n)},h=o.boost=o.boost||{};return t=h.targetCtx,h.canvas?!(o instanceof e):(h.canvas=tN.createElement("canvas"),h.target=i.renderer.image("",0,0,n,a).addClass("highcharts-boost-canvas").add(r),t=h.targetCtx=h.canvas.getContext("2d"),i.inverted&&["moveTo","lineTo","rect","arc"].forEach(e=>{tO(t,e,l)}),h.copy=function(){h.target.attr({href:h.canvas.toDataURL("image/png")})},h.clear=function(){t.clearRect(0,0,h.canvas.width,h.canvas.height),o===h.target&&h.target.attr({href:s})},h.clipRect=i.renderer.clipRect(),h.target.clip(h.clipRect)),h.canvas.width!==n&&(h.canvas.width=n),h.canvas.height!==a&&(h.canvas.height=a),h.target.attr({x:0,y:0,width:n,height:a,style:"pointer-events: none",href:s}),h.clipRect&&h.clipRect.attr(tw(i,o)),t}function g(){let t=this,e=t.options,o=t.chart,r=t.xAxis,n=t.yAxis,a=o.options.boost||{},l={timeRendering:a.timeRendering||!1,timeSeriesProcessing:a.timeSeriesProcessing||!1,timeSetup:a.timeSetup||!1},h=t.getColumn("x",!0),d=t.getColumn("y",!0),c=e.data,g=r.getExtremes(),m=g.min,p=g.max,f=n.getExtremes(),b=f.min,x=f.max,A={},y=!!t.sampling,v=e.marker&&e.marker.radius,T=t.cvsStrokeBatch||1e3,C=e.enableMouseTracking,S=e.threshold,M=tG(S),E=n.getThreshold(S),k=t.fill,w=t.pointArrayMap&&"low,high"===t.pointArrayMap.join(","),R=!!e.stacking,U=t.cropStart||0,L=o.options.loading,N=t.requireSorting,_=e.connectNulls,D=!h,z=R?t.data:h||c,G=t.fillOpacity?P().parse(t.color).setOpacity(tB(e.fillOpacity,.75)).get():t.color,I="x"===e.findNearestPointBy,B=this.boost||{},O=t.cvsDrawPoint,V=e.lineWidth?t.cvsLineTo:void 0,X=v&&v<=1?t.cvsMarkerSquare:t.cvsMarkerCircle;B.target&&B.target.attr({href:s}),(t.points||t.graph)&&tU(t),t.plotGroup("group","series",t.visible?"visible":"hidden",e.zIndex,o.seriesGroup),t.markerGroup=t.group,tD(t,"destroy",function(){t.markerGroup=null});let F=this.points=[],H=this.getContext();if(t.buildKDTree=t_,B.clear&&B.clear(),!t.visible)return;c.length>99999&&(o.options.loading=tI(L,{labelStyle:{backgroundColor:tL("#ffffff").setOpacity(.75).get(),padding:"1em",borderRadius:"0.5em"},style:{backgroundColor:"none",opacity:1}}),u().clearTimeout(i),o.showLoading("Drawing..."),o.options.loading=L),l.timeRendering&&console.time("canvas rendering");let Y=0,W,j,K=E,Z,q,Q,J,$,tt,te=function(){k?(H.fillStyle=G,H.fill()):(H.strokeStyle=t.color,H.lineWidth=e.lineWidth,H.stroke())},ti=function(e,i,s,r){0===Y&&(H.beginPath(),V&&(H.lineJoin="round")),o.scroller&&"highcharts-navigator-series"===t.options.className?(i+=o.scroller.top,s&&(s+=o.scroller.top)):i+=o.plotTop,e+=o.plotLeft,Z?H.moveTo(e,i):O?O(H,e,i,s,j):V?V(H,e,i):X&&X.call(t,H,e,i,v,r),(Y+=1)===T&&(te(),Y=0),j={clientX:e,plotY:i,yBottom:s}},ts=(this.getColumn("x").length?this.getColumn("x"):void 0)||this.options.xData||!!this.getColumn("x",!0).length&&this.getColumn("x",!0),to=function(t,e,i){tt=I?t:t+","+e,C&&!A[tt]&&(A[tt]=!0,o.inverted&&(t=r.len-t,e=n.len-e),F.push({x:!!ts&&ts[U+i],clientX:t,plotX:t,plotY:e,i:U+i}))};tk.eachAsync(z,(e,i)=>{let s=void 0===o.index,a,l,h,u,c,g,f=!1,A=!1,v=NaN,P=NaN,T=!0;return!s&&(D?(a=e[0],l=e[1],z[i+1]&&(v=z[i+1][0]),z[i-1]&&(P=z[i-1][0])):(a=e,l=d[i],z[i+1]&&(v=z[i+1]),z[i-1]&&(P=z[i-1])),v&&v>=m&&v<=p&&(f=!0),P&&P>=m&&P<=p&&(A=!0),w?(D&&(l=e.slice(1,3)),g=l[0],l=l[1]):R&&(a=e.x,g=(l=e.stackY)-e.y),c=null===l,N||(T=l>=b&&l<=x),!c&&(a>=m&&a<=p&&T||f||A)&&(h=Math.round(r.toPixels(a,!0)),y?((void 0===J||h===W)&&(w||(g=l),(void 0===$||l>Q)&&(Q=l,$=i),(void 0===J||g<q)&&(q=g,J=i)),h!==W&&(void 0!==J&&(u=n.toPixels(Q,!0),K=n.toPixels(q,!0),ti(h,M?Math.min(u,E):u,M?Math.max(K,E):K,i),to(h,u,$),K!==u&&to(h,K,J)),J=$=void 0,W=h)):(ti(h,u=Math.round(n.toPixels(l,!0)),K,i),to(h,u,i))),Z=c&&!_,i%5e4==0&&(t.boost&&t.boost.copy?t.boost.copy():t.chart.boost&&t.chart.boost.copy&&t.chart.boost.copy())),!s},function(){let e=o.loadingDiv,s=o.loadingShown;te(),t.canvasToSVG(),l.timeRendering&&console.timeEnd("canvas rendering"),tz(t,"renderedCanvas"),s&&(e.style.transition="opacity 250ms",e.opacity=0,o.loadingShown=!1,i=setTimeout(function(){e.parentNode&&e.parentNode.removeChild(e),o.loadingDiv=o.loadingSpan=null},250)),delete t.buildKDTree,t.buildKDTree()},o.renderer.forExport?Number.MAX_VALUE:void 0)}function m(t,e,i,s){t.moveTo(e,i),t.arc(e,i,s,0,2*Math.PI,!1)}function p(t,e,i,s){t.rect(e-s,i-s,2*s,2*s)}function f(){let t=this.chart,e=this.getContext(),i=this.chart.inverted,s=this.xAxis,o=this.yAxis;e?(this.points.forEach(r=>{let n,a=r.plotY;if(void 0!==a&&!isNaN(a)&&null!==r.y&&e){let{x:n=0,y:a=0,width:l=0,height:h=0}=r.shapeArgs||{};e.fillStyle=(t.styledMode?r.series.colorAttribs(r):r.series.pointAttribs(r)).fill,i?e.fillRect(o.len-a+s.left,s.len-n+o.top,-h,-l):e.fillRect(n+s.left,a+o.top,l,h)}}),this.canvasToSVG()):this.chart.showLoading("Your browser doesn't support HTML5 canvas, <br>please use a modern browser")}t.compose=function(t,i,s){let u=i.prototype;if(!u.renderCanvas){let{area:i,bubble:b,column:x,heatmap:A,scatter:y}=s;if(e=t,t.prototype.callbacks.push(t=>{tD(t,"predraw",l),tD(t,"render",a)}),u.canvasToSVG=h,u.cvsLineTo=d,u.getContext=c,u.renderCanvas=g,i){let t=i.prototype;t.cvsDrawPoint=o,t.fill=!0,t.fillOpacity=!0,t.sampling=!0}if(b){let t=b.prototype;t.cvsMarkerCircle=r,t.cvsStrokeBatch=1}if(x){let t=x.prototype;t.cvsDrawPoint=n,t.fill=!0,t.sampling=!0}if(A&&tO(A.prototype,"drawPoints",f),y){let t=y.prototype;t.cvsMarkerCircle=m,t.cvsMarkerSquare=p,t.fill=!0}}}}(r||(r={}));let tV=r,tX=u();tX.initCanvasBoost=function(){tV.compose(tX.Chart,tX.Series,tX.seriesTypes)};let tF=u();return h.default})());