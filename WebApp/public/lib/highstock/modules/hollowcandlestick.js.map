{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/hollowcandlestick\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Hollow Candlestick series type for Highcharts Stock\n *\n * (c) 2010-2025 <PERSON><PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"Axis\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/hollowcandlestick\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"],amd1[\"Axis\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/hollowcandlestick\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"Axis\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"Axis\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__532__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 532:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__532__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ hollowcandlestick_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/HollowCandlestick/HollowCandlestickPoint.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\nconst { seriesTypes: { candlestick: CandlestickSeries } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n/* *\n *\n *  Class\n *\n * */\nclass HollowCandlestickPoint extends CandlestickSeries.prototype.pointClass {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * Update class name if needed.\n     * @private\n     * @function Highcharts.seriesTypes.hollowcandlestick#getClassName\n     */\n    getClassName() {\n        let className = super.getClassName.apply(this);\n        const point = this, index = point.index, currentPoint = point.series.hollowCandlestickData[index];\n        if (!currentPoint.isBullish && currentPoint.trendDirection === 'up') {\n            className += '-bearish-up';\n        }\n        return className;\n    }\n}\n/* *\n *\n *  Class Namespace\n *\n * */\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const HollowCandlestick_HollowCandlestickPoint = (HollowCandlestickPoint);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Axis\"],\"commonjs\":[\"highcharts\",\"Axis\"],\"commonjs2\":[\"highcharts\",\"Axis\"],\"root\":[\"Highcharts\",\"Axis\"]}\nvar highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_ = __webpack_require__(532);\nvar highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default = /*#__PURE__*/__webpack_require__.n(highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_);\n;// ./code/es-modules/Series/HollowCandlestick/HollowCandlestickSeries.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\n\n\n\nconst { seriesTypes: { candlestick: HollowCandlestickSeries_CandlestickSeries } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\nconst { addEvent, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Code\n *\n * */\n/**\n * The hollowcandlestick series.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.hollowcandlestick\n *\n * @augments Highcharts.seriesTypes.candlestick\n */\nclass HollowCandlestickSeries extends HollowCandlestickSeries_CandlestickSeries {\n    constructor() {\n        /* *\n         *\n         * Static properties\n         *\n         * */\n        super(...arguments);\n        this.hollowCandlestickData = [];\n        /* eslint-disable valid-jsdoc */\n    }\n    /* *\n     *\n     * Functions\n     *\n     * */\n    /**\n     * Iterate through all points and get their type.\n     * @private\n     *\n     * @function Highcharts.seriesTypes.hollowcandlestick#getPriceMovement\n     *\n     *\n     */\n    getPriceMovement() {\n        const series = this, table = series.allGroupedTable || series.dataTable, dataLength = table.rowCount, hollowCandlestickData = this.hollowCandlestickData;\n        hollowCandlestickData.length = 0;\n        let previousDataArr;\n        for (let i = 0; i < dataLength; i++) {\n            const dataArr = table.getRow(i, this.pointArrayMap);\n            hollowCandlestickData.push(series.isBullish(dataArr, \n            // Determine the first point is bullish based on\n            // its open and close values.(#21683)\n            i ? previousDataArr : dataArr));\n            previousDataArr = dataArr;\n        }\n    }\n    /**\n     * Return line color based on candle type.\n     * @private\n     *\n     * @function Highcharts.seriesTypes.hollowcandlestick#getLineColor\n     *\n     * @param {string} trendDirection\n     * Type of candle direction (bearish/bullish)(down/up).\n     *\n     * @return {ColorType}\n     * Line color\n     */\n    getLineColor(trendDirection) {\n        const series = this;\n        // Return line color based on trend direction\n        return trendDirection === 'up' ?\n            series.options.upColor || \"#06b535\" /* Palette.positiveColor */ :\n            series.options.color || \"#f21313\" /* Palette.negativeColor */;\n    }\n    /**\n     * Return fill color based on candle type.\n     * @private\n     *\n     * @function Highcharts.seriesTypes.hollowcandlestick#getPointFill\n     *\n     * @param {HollowcandleInfo} hollowcandleInfo\n     *        Information about the current candle.\n     *\n     * @return {ColorType}\n     * Point fill color\n     */\n    getPointFill(hollowcandleInfo) {\n        const series = this;\n        // Return fill color only for bearish candles.\n        if (hollowcandleInfo.isBullish) {\n            return 'transparent';\n        }\n        return hollowcandleInfo.trendDirection === 'up' ?\n            series.options.upColor || \"#06b535\" /* Palette.positiveColor */ :\n            series.options.color || \"#f21313\" /* Palette.negativeColor */;\n    }\n    /**\n     * @private\n     * @function Highcharts.seriesTypes.hollowcandlestick#init\n     */\n    init() {\n        super.init.apply(this, arguments);\n        this.hollowCandlestickData = [];\n    }\n    /**\n     * Check if the candle is bearish or bullish. For bullish one, return true.\n     * For bearish, return string depending on the previous point.\n     *\n     * @function Highcharts.seriesTypes.hollowcandlestick#isBullish\n     *\n     * @param {Array<(number)>} dataPoint\n     * Current point which we calculate.\n     *\n     * @param {Array<(number)>} previousDataPoint\n     * Previous point.\n     */\n    isBullish(dataPoint, previousDataPoint) {\n        return {\n            // Compare points' open and close value.\n            isBullish: (dataPoint[0] || 0) <= (dataPoint[3] || 0),\n            // For bearish candles.\n            trendDirection: (dataPoint[3] || 0) < (previousDataPoint?.[3] || 0) ?\n                'down' : 'up'\n        };\n    }\n    /**\n     * Add color and fill attribute for each point.\n     *\n     * @private\n     *\n     * @function Highcharts.seriesTypes.hollowcandlestick#pointAttribs\n     *\n     * @param {HollowCandlestickPoint} point\n     * Point to which we are adding attributes.\n     *\n     * @param {StatesOptionsKey} state\n     * Current point state.\n     */\n    pointAttribs(point, state) {\n        const attribs = super.pointAttribs.call(this, point, state);\n        let stateOptions;\n        const index = point.index, hollowcandleInfo = this.hollowCandlestickData[index];\n        attribs.fill = this.getPointFill(hollowcandleInfo) || attribs.fill;\n        attribs.stroke = this.getLineColor(hollowcandleInfo.trendDirection) ||\n            attribs.stroke;\n        // Select or hover states\n        if (state) {\n            stateOptions = this.options.states[state];\n            attribs.fill = stateOptions.color || attribs.fill;\n            attribs.stroke = stateOptions.lineColor || attribs.stroke;\n            attribs['stroke-width'] =\n                stateOptions.lineWidth || attribs['stroke-width'];\n        }\n        return attribs;\n    }\n}\n/**\n * A hollow candlestick chart is a style of financial chart used to\n * describe price movements over time.\n *\n * @sample stock/demo/hollow-candlestick/\n *         Hollow Candlestick chart\n *\n * @extends      plotOptions.candlestick\n * @product      highstock\n * @requires     modules/hollowcandlestick\n * @optionparent plotOptions.hollowcandlestick\n */\nHollowCandlestickSeries.defaultOptions = merge(HollowCandlestickSeries_CandlestickSeries.defaultOptions, {\n    /**\n     * The fill color of the candlestick when the current\n     * close is lower than the previous one.\n     *\n     * @sample stock/plotoptions/hollow-candlestick-color/\n     *     Custom colors\n     * @sample {highstock} highcharts/css/hollow-candlestick/\n     *         Colors in styled mode\n     *\n     * @type    {ColorType}\n     * @product highstock\n     */\n    color: \"#f21313\" /* Palette.negativeColor */,\n    dataGrouping: {\n        groupAll: true,\n        groupPixelWidth: 10\n    },\n    /**\n     * The color of the line/border of the hollow candlestick when\n     * the current close is lower than the previous one.\n     *\n     * @sample stock/plotoptions/hollow-candlestick-color/\n     *     Custom colors\n     * @sample {highstock} highcharts/css/hollow-candlestick/\n     *         Colors in styled mode\n     *\n     * @type    {ColorType}\n     * @product highstock\n     */\n    lineColor: \"#f21313\" /* Palette.negativeColor */,\n    /**\n     * The fill color of the candlestick when the current\n     * close is higher than the previous one.\n     *\n     * @sample stock/plotoptions/hollow-candlestick-color/\n     *     Custom colors\n     * @sample {highstock} highcharts/css/hollow-candlestick/\n     *         Colors in styled mode\n     *\n     * @type    {ColorType}\n     * @product highstock\n     */\n    upColor: \"#06b535\" /* Palette.positiveColor */,\n    /**\n     * The color of the line/border of the hollow candlestick when\n     * the current close is higher than the previous one.\n     *\n     * @sample stock/plotoptions/hollow-candlestick-color/\n     *     Custom colors\n     * @sample {highstock} highcharts/css/hollow-candlestick/\n     *         Colors in styled mode\n     *\n     * @type    {ColorType}\n     * @product highstock\n     */\n    upLineColor: \"#06b535\" /* Palette.positiveColor */\n});\n// Force to recalculate the hollowcandlestick data set after updating data.\naddEvent(HollowCandlestickSeries, 'updatedData', function () {\n    if (this.hollowCandlestickData.length) {\n        this.hollowCandlestickData.length = 0;\n    }\n});\n// After processing and grouping the data,\n// check if the candle is bearish or bullish.\n// Required for further calculation.\naddEvent((highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default()), 'postProcessData', function () {\n    const axis = this, series = axis.series;\n    series.forEach(function (series) {\n        if (series.is('hollowcandlestick')) {\n            const hollowcandlestickSeries = series;\n            hollowcandlestickSeries.getPriceMovement();\n        }\n    });\n});\n/* *\n *\n *  Class Prototype\n *\n * */\nHollowCandlestickSeries.prototype.pointClass = HollowCandlestick_HollowCandlestickPoint;\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('hollowcandlestick', HollowCandlestickSeries);\n/* *\n *\n * Default Export\n *\n * */\n/* harmony default export */ const HollowCandlestick_HollowCandlestickSeries = ((/* unused pure expression or super */ null && (HollowCandlestickSeries)));\n/* *\n *\n * API Options\n *\n * */\n/**\n * A `hollowcandlestick` series. If the [type](#series.candlestick.type)\n * option is not specified, it is inherited from [chart.type](\n * #chart.type).\n *\n * @type      {*}\n * @extends   series,plotOptions.hollowcandlestick\n * @excluding dataParser, dataURL, marker\n * @product   highstock\n * @apioption series.hollowcandlestick\n */\n/**\n * An array of data points for the series. For the `hollowcandlestick` series\n * type, points can be given in the following ways:\n *\n * 1. An array of arrays with 5 or 4 values. In this case, the values correspond\n *    to `x,open,high,low,close`. If the first value is a string, it is applied\n *    as the name of the point, and the `x` value is inferred. The `x` value can\n *    also be omitted, in which case the inner arrays should be of length 4.\n *    Then the `x` value is automatically calculated, either starting at 0 and\n *    incremented by 1, or from `pointStart` and `pointInterval` given in the\n *    series options.\n *    ```js\n *    data: [\n *        [0, 7, 2, 0, 4],\n *        [1, 1, 4, 2, 8],\n *        [2, 3, 3, 9, 3]\n *    ]\n *    ```\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.hollowcandlestick.turboThreshold), this option is not\n *    available.\n *    ```js\n *    data: [{\n *        x: 1,\n *        open: 9,\n *        high: 2,\n *        low: 4,\n *        close: 6,\n *        name: \"Point2\",\n *        color: \"#00FF00\"\n *    }, {\n *        x: 1,\n *        open: 1,\n *        high: 4,\n *        low: 7,\n *        close: 7,\n *        name: \"Point1\",\n *        color: \"#FF00FF\"\n *    }]\n *    ```\n *\n * @type      {Array<Array<(number|string),number,number,number>|Array<(number|string),number,number,number,number>|*>}\n * @extends   series.candlestick.data\n * @excluding y\n * @product   highstock\n * @apioption series.hollowcandlestick.data\n */\n''; // Adds doclets above to transpiled\n\n;// ./code/es-modules/masters/modules/hollowcandlestick.js\n\n\n\n\n/* harmony default export */ const hollowcandlestick_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__532__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "hollowcandlestick_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "seriesTypes", "candlestick", "CandlestickSeries", "HollowCandlestickPoint", "pointClass", "getClassName", "className", "apply", "index", "point", "currentPoint", "series", "hollowCandlestickData", "isBullish", "trendDirection", "highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_", "highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default", "HollowCandlestickSeries_CandlestickSeries", "addEvent", "merge", "HollowCandlestickSeries", "constructor", "arguments", "getPriceMovement", "previousDataArr", "table", "allGroupedTable", "dataTable", "dataLength", "rowCount", "length", "i", "dataArr", "getRow", "pointArrayMap", "push", "getLineColor", "options", "upColor", "color", "getPointFill", "hollowcandleInfo", "init", "dataPoint", "previousDataPoint", "pointAttribs", "state", "stateOptions", "attribs", "fill", "stroke", "states", "lineColor", "lineWidth", "defaultOptions", "dataGrouping", "groupAll", "groupPixelWidth", "upLineColor", "axis", "for<PERSON>ach", "is", "hollowcandlestickSeries", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,IAAO,EACzG,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,uCAAwC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAACA,EAAK,IAAO,CAAE,GAC7I,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,uCAAuC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,IAAO,EAEjJA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,IAAO,CACnH,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,IACvG,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAab,OAAO,CAG5B,IAAIC,EAASS,CAAwB,CAACE,EAAS,CAAG,CAGjDZ,QAAS,CAAC,CACX,EAMA,OAHAS,CAAmB,CAACG,EAAS,CAACX,EAAQA,EAAOD,OAAO,CAAEW,GAG/CV,EAAOD,OAAO,AACtB,CAMCW,EAAoBI,CAAC,CAAG,AAACd,IACxB,IAAIe,EAASf,GAAUA,EAAOgB,UAAU,CACvC,IAAOhB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAU,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAAClB,EAASoB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACtB,EAASqB,IAC5EE,OAAOC,cAAc,CAACxB,EAASqB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAkBjL,GAAM,CAAEE,YAAa,CAAEC,YAAaC,CAAiB,CAAE,CAAE,CAAIH,GAM7D,OAAMI,UAA+BD,EAAkBX,SAAS,CAACa,UAAU,CAYvEC,cAAe,CACX,IAAIC,EAAY,KAAK,CAACD,aAAaE,KAAK,CAAC,IAAI,EACzBC,EAAQC,AAAd,IAAI,CAAgBD,KAAK,CAAEE,EAAeD,AAA1C,IAAI,CAA4CE,MAAM,CAACC,qBAAqB,CAACJ,EAAM,CAIjG,OAHI,AAACE,EAAaG,SAAS,EAAIH,AAAgC,OAAhCA,EAAaI,cAAc,EACtDR,CAAAA,GAAa,aAAY,EAEtBA,CACX,CACJ,CAcA,IAAIS,EAA2F1C,EAAoB,KAC/G2C,EAA+G3C,EAAoBI,CAAC,CAACsC,GAqBzI,GAAM,CAAEf,YAAa,CAAEC,YAAagB,CAAyC,CAAE,CAAE,CAAIlB,IAC/E,CAAEmB,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAE,CAAItB,GAe7B,OAAMuB,UAAgCH,EAClCI,aAAc,CAMV,KAAK,IAAIC,WACT,IAAI,CAACV,qBAAqB,CAAG,EAAE,AAEnC,CAcAW,kBAAmB,CACf,IAEIC,EAFiBC,EAAQd,AAAd,IAAI,CAAiBe,eAAe,EAAIf,AAAxC,IAAI,CAA2CgB,SAAS,CAAEC,EAAaH,EAAMI,QAAQ,CAAEjB,EAAwB,IAAI,CAACA,qBAAqB,AACxJA,CAAAA,EAAsBkB,MAAM,CAAG,EAE/B,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAYG,IAAK,CACjC,IAAMC,EAAUP,EAAMQ,MAAM,CAACF,EAAG,IAAI,CAACG,aAAa,EAClDtB,EAAsBuB,IAAI,CAACxB,AALhB,IAAI,CAKmBE,SAAS,CAACmB,EAG5CD,EAAIP,EAAkBQ,IACtBR,EAAkBQ,CACtB,CACJ,CAaAI,aAAatB,CAAc,CAAE,CAGzB,MAAOA,AAAmB,OAAnBA,EACHH,AAHW,IAAI,CAGR0B,OAAO,CAACC,OAAO,EAAI,UAC1B3B,AAJW,IAAI,CAIR0B,OAAO,CAACE,KAAK,EAAI,SAChC,CAaAC,aAAaC,CAAgB,CAAE,QAG3B,AAAIA,EAAiB5B,SAAS,CACnB,cAEJ4B,AAAoC,OAApCA,EAAiB3B,cAAc,CAClCH,AANW,IAAI,CAMR0B,OAAO,CAACC,OAAO,EAAI,UAC1B3B,AAPW,IAAI,CAOR0B,OAAO,CAACE,KAAK,EAAI,SAChC,CAKAG,MAAO,CACH,KAAK,CAACA,KAAKnC,KAAK,CAAC,IAAI,CAAEe,WACvB,IAAI,CAACV,qBAAqB,CAAG,EAAE,AACnC,CAaAC,UAAU8B,CAAS,CAAEC,CAAiB,CAAE,CACpC,MAAO,CAEH/B,UAAW,AAAC8B,CAAAA,CAAS,CAAC,EAAE,EAAI,CAAA,GAAOA,CAAAA,CAAS,CAAC,EAAE,EAAI,CAAA,EAEnD7B,eAAgB,AAAC6B,CAAAA,CAAS,CAAC,EAAE,EAAI,CAAA,EAAMC,CAAAA,GAAmB,CAAC,EAAE,EAAI,CAAA,EAC7D,OAAS,IACjB,CACJ,CAcAC,aAAapC,CAAK,CAAEqC,CAAK,CAAE,CACvB,IACIC,EADEC,EAAU,KAAK,CAACH,aAAapD,IAAI,CAAC,IAAI,CAAEgB,EAAOqC,GAE/CtC,EAAQC,EAAMD,KAAK,CAAEiC,EAAmB,IAAI,CAAC7B,qBAAqB,CAACJ,EAAM,CAY/E,OAXAwC,EAAQC,IAAI,CAAG,IAAI,CAACT,YAAY,CAACC,IAAqBO,EAAQC,IAAI,CAClED,EAAQE,MAAM,CAAG,IAAI,CAACd,YAAY,CAACK,EAAiB3B,cAAc,GAC9DkC,EAAQE,MAAM,CAEdJ,IAEAE,EAAQC,IAAI,CAAGF,AADfA,CAAAA,EAAe,IAAI,CAACV,OAAO,CAACc,MAAM,CAACL,EAAM,AAAD,EACZP,KAAK,EAAIS,EAAQC,IAAI,CACjDD,EAAQE,MAAM,CAAGH,EAAaK,SAAS,EAAIJ,EAAQE,MAAM,CACzDF,CAAO,CAAC,eAAe,CACnBD,EAAaM,SAAS,EAAIL,CAAO,CAAC,eAAe,EAElDA,CACX,CACJ,CAaA5B,EAAwBkC,cAAc,CAAGnC,EAAMF,EAA0CqC,cAAc,CAAE,CAarGf,MAAO,UACPgB,aAAc,CACVC,SAAU,CAAA,EACVC,gBAAiB,EACrB,EAaAL,UAAW,UAaXd,QAAS,UAaToB,YAAa,SACjB,GAEAxC,EAASE,EAAyB,cAAe,WACzC,IAAI,CAACR,qBAAqB,CAACkB,MAAM,EACjC,CAAA,IAAI,CAAClB,qBAAqB,CAACkB,MAAM,CAAG,CAAA,CAE5C,GAIAZ,EAAUF,IAAoG,kBAAmB,WAE7HL,AAD4BgD,AAAf,IAAI,CAAgBhD,MAAM,CAChCiD,OAAO,CAAC,SAAUjD,CAAM,EACvBA,EAAOkD,EAAE,CAAC,sBAEVC,AADgCnD,EACRY,gBAAgB,EAEhD,EACJ,GAMAH,EAAwB7B,SAAS,CAACa,UAAU,CAhRmCD,EAiR/EJ,IAA0IgE,kBAAkB,CAAC,oBAAqB3C,GAgFrJ,IAAMzB,EAA0BE,IAGnD,OADYH,EAAoB,OAAU,AAE3C,CAAA"}