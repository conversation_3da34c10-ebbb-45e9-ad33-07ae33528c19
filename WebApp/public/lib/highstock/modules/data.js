!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/data
 * @requires highcharts
 *
 * Data module
 *
 * (c) 2012-2025 Torstein Honsi
 *
 * License: www.highcharts.com/license
 */function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(e._Highcharts,e._Highcharts.Axis,e._Highcharts.Chart,e._Highcharts.Point,e._Highcharts.SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/modules/data",["highcharts/highcharts"],function(e){return t(e,e.Axis,e.Chart,e.Point,e.SeriesRegistry)}):"object"==typeof exports?exports["highcharts/modules/data"]=t(e._Highcharts,e._Highcharts.Axis,e._Highcharts.Chart,e._Highcharts.Point,e._Highcharts.SeriesRegistry):e.Highcharts=t(e.Highcharts,e.Highcharts.Axis,e.Highcharts.Chart,e.Highcharts.Point,e.Highcharts.SeriesRegistry)}("undefined"==typeof window?this:window,(e,t,s,i,a)=>(()=>{"use strict";var r={260:e=>{e.exports=i},512:e=>{e.exports=a},532:e=>{e.exports=t},944:t=>{t.exports=e},960:e=>{e.exports=s}},n={};function o(e){var t=n[e];if(void 0!==t)return t.exports;var s=n[e]={exports:{}};return r[e](s,s.exports,o),s.exports}o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},o.d=(e,t)=>{for(var s in t)o.o(t,s)&&!o.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var l={};o.d(l,{default:()=>$});var h=o(944),d=o.n(h);let{win:u}=d(),{discardElement:p,objectEach:c}=d(),m={ajax:function(e){let t={json:"application/json",xml:"application/xml",text:"text/plain",octet:"application/octet-stream"},s=new XMLHttpRequest;function i(t,s){e.error&&e.error(t,s)}if(!e.url)return!1;s.open((e.type||"get").toUpperCase(),e.url,!0),e.headers?.["Content-Type"]||s.setRequestHeader("Content-Type",t[e.dataType||"json"]||t.text),c(e.headers,function(e,t){s.setRequestHeader(t,e)}),e.responseType&&(s.responseType=e.responseType),s.onreadystatechange=function(){let t;if(4===s.readyState){if(200===s.status){if("blob"!==e.responseType&&(t=s.responseText,"json"===e.dataType))try{t=JSON.parse(t)}catch(e){if(e instanceof Error)return i(s,e)}return e.success?.(t,s)}i(s,s.responseText)}},e.data&&"string"!=typeof e.data&&(e.data=JSON.stringify(e.data)),s.send(e.data)},getJSON:function(e,t){m.ajax({url:e,success:t,dataType:"json",headers:{"Content-Type":"text/plain"}})},post:async function(e,t,s){let i=new u.FormData;c(t,function(e,t){i.append(t,e)}),i.append("b64","true");let a=await u.fetch(e,{method:"POST",body:i,...s});if(a.ok){let e=await a.text(),s=document.createElement("a");s.href=`data:${t.type};base64,${e}`,s.download=t.filename,s.click(),p(s)}}};var f=o(532),g=o.n(f),x=o(960),y=o.n(x),v=o(260),C=o.n(v),T=o(512),N=o.n(T);let{getOptions:R}=d(),{doc:A}=d(),{ajax:Y}=m,{seriesTypes:D}=N(),{addEvent:w,defined:U,extend:b,fireEvent:F,isNumber:O,merge:H,objectEach:S,pick:E,splat:j}=d();function L(e){return!!(e&&(e.rowsURL||e.csvURL||e.columnsURL))}class M{static data(e,t={},s){return new M(e,t,s)}static rowsToColumns(e){let t,s,i,a,r;if(e)for(t=0,r=[],s=e.length;t<s;t++)for(i=0,a=e[t].length;i<a;i++)r[i]||(r[i]=[]),r[i][t]=e[t][i];return r}constructor(e,t={},s){this.rowsToColumns=M.rowsToColumns,this.dateFormats={"YYYY/mm/dd":{regex:/^(\d{4})[\-\/\.](\d{1,2})[\-\/\.](\d{1,2})$/,parser:function(e){return e?Date.UTC(+e[1],e[2]-1,+e[3]):NaN}},"dd/mm/YYYY":{regex:/^(\d{1,2})[\-\/\.](\d{1,2})[\-\/\.](\d{4})$/,parser:function(e){return e?Date.UTC(+e[3],e[2]-1,+e[1]):NaN},alternative:"mm/dd/YYYY"},"mm/dd/YYYY":{regex:/^(\d{1,2})[\-\/\.](\d{1,2})[\-\/\.](\d{4})$/,parser:function(e){return e?Date.UTC(+e[3],e[1]-1,+e[2]):NaN}},"dd/mm/YY":{regex:/^(\d{1,2})[\-\/\.](\d{1,2})[\-\/\.](\d{2})$/,parser:function(e){if(!e)return NaN;let t=new Date,s=+e[3];return s>t.getFullYear()-2e3?s+=1900:s+=2e3,Date.UTC(s,e[2]-1,+e[1])},alternative:"mm/dd/YY"},"mm/dd/YY":{regex:/^(\d{1,2})[\-\/\.](\d{1,2})[\-\/\.](\d{2})$/,parser:function(e){return e?Date.UTC(+e[3]+2e3,e[1]-1,+e[2]):NaN}}},this.chart=s,this.chartOptions=t,this.options=e,this.rawColumns=[],this.init(e,t,s)}init(e,t,s){let i=e.decimalPoint,a;t&&(this.chartOptions=t),s&&(this.chart=s),"."!==i&&","!==i&&(i=void 0),this.options=e,this.columns=e.columns||this.rowsToColumns(e.rows)||[],this.firstRowAsNames=E(e.firstRowAsNames,this.firstRowAsNames,!0),this.decimalRegex=i&&RegExp("^(-?[0-9]+)"+i+"([0-9]+)$"),void 0!==this.liveDataTimeout&&clearTimeout(this.liveDataTimeout),this.rawColumns=[],this.columns.length&&(this.dataFound(),a=!L(e)),a||(a=this.fetchLiveData()),a||(a=!!this.parseCSV().length),a||(a=!!this.parseTable().length),a||(a=this.parseGoogleSpreadsheet()),!a&&e.afterComplete&&e.afterComplete(this)}getColumnDistribution(){let e=this.chartOptions,t=this.options,s=[],i=function(e="line"){return(D[e].prototype.pointArrayMap||[0]).length},a=function(e="line"){return D[e].prototype.pointArrayMap},r=e?.chart?.type,n=[],o=[],l=t?.seriesMapping||e?.series?.map(function(){return{x:0}})||[],h=0;(e?.series||[]).forEach(e=>{n.push(i(e.type||r))}),l.forEach(e=>{s.push(e.x||0)}),0===s.length&&s.push(0),l.forEach(t=>{let s=new I,l=n[h]||i(r),d=(e?.series??[])[h]??{},u=a(d.type||r),p=u??["y"];(U(t.x)||d.isCartesian||!u)&&s.addColumnReader(t.x,"x"),S(t,function(e,t){"x"!==t&&s.addColumnReader(e,t)});for(let e=0;e<l;e++)s.hasReader(p[e])||s.addColumnReader(void 0,p[e]);o.push(s),h++});let d=a(r);void 0===d&&(d=["y"]),this.valueCount={global:i(r),xColumns:s,individual:n,seriesBuilders:o,globalPointArrayMap:d}}dataFound(){this.options.switchRowsAndColumns&&(this.columns=this.rowsToColumns(this.columns)),this.getColumnDistribution(),this.parseTypes(),!1!==this.parsed()&&this.complete()}parseCSV(e){let t=this,s=this.columns=[],i=e||this.options,a=i.startColumn||0,r=i.endColumn||Number.MAX_VALUE,n=[],o={",":0,";":0,"	":0},l=i.csv,h=i.startRow||0,d=i.endRow||Number.MAX_VALUE,u,p,c=0;if(l&&i.beforeParse&&(l=i.beforeParse.call(this,l)),l){if(p=l.replace(/\r\n/g,"\n").replace(/\r/g,"\n").split(i.lineDelimiter||"\n"),(!h||h<0)&&(h=0),(!d||d>=p.length)&&(d=p.length-1),i.itemDelimiter)u=i.itemDelimiter;else{let e,s,a;e=0,s=0,a=!1,p.some(function(t,i){let a=!1,r,n,l,h="";if(i>13)return!0;for(let i=0;i<t.length;i++){if(r=t[i],n=t[i+1],l=t[i-1],"#"===r)return;if('"'===r)if(a){if('"'!==l&&'"'!==n){for(;" "===n&&i<t.length;)n=t[++i];void 0!==o[n]&&o[n]++,a=!1}}else a=!0;else void 0!==o[r]?(isNaN(Date.parse(h=h.trim()))?(isNaN(h)||!isFinite(h))&&o[r]++:o[r]++,h=""):h+=r;","===r&&s++,"."===r&&e++}}),o[";"]>o[","]?a=";":(o[","],o[";"],a=","),i.decimalPoint||(e>s?i.decimalPoint=".":i.decimalPoint=",",t.decimalRegex=RegExp("^(-?[0-9]+)"+i.decimalPoint+"([0-9]+)$")),u=a}let e=0;for(c=h;c<=d;c++)"#"===p[c][0]?e++:function(e,t,o,l){let h=0,d="",p="",c="",m="",f=0,g=0;function x(t){d=e[t],p=e[t-1],c=e[t+1]}function y(e){n.length<g+1&&n.push([e]),n[g][n[g].length-1]!==e&&n[g].push(e)}function v(){if(a>f||f>r){++f,m="";return}i.columnTypes||(!isNaN(parseFloat(m))&&isFinite(m)?(m=parseFloat(m),y("number")):isNaN(Date.parse(m))?y("string"):(m=m.replace(/\//g,"-"),y("date"))),s.length<g+1&&s.push([]),s[g][t]=m,m="",++g,++f}if(e.trim().length&&"#"!==e.trim()[0]){for(;h<e.length;h++)if(x(h),'"'===d)for(x(++h);h<e.length&&('"'!==d||'"'===p||'"'===c);)('"'!==d||'"'===d&&'"'!==p)&&(m+=d),x(++h);else d===u?v():m+=d;v()}}(p[c],c-h-e);(!i.columnTypes||0===i.columnTypes.length)&&n.length&&n[0].length&&"date"===n[0][1]&&!i.dateFormat&&(i.dateFormat=function(e,s){let a="YYYY/mm/dd",r=[],n=[],o,l=[],h,d=0,u=!1,p;for((!s||s>e.length)&&(s=e.length);d<s;d++)if(void 0!==e[d]&&e[d]?.length)for(p=0,o=e[d].trim().replace(/\//g," ").replace(/\-/g," ").replace(/\./g," ").split(" "),l=["","",""];p<o.length;p++)p<l.length&&(o[p]=parseInt(o[p],10),o[p]&&(n[p]=!n[p]||n[p]<o[p]?o[p]:n[p],void 0!==r[p]?r[p]!==o[p]&&(r[p]=!1):r[p]=o[p],o[p]>31?o[p]<100?l[p]="YY":l[p]="YYYY":o[p]>12&&o[p]<=31?(l[p]="dd",u=!0):l[p].length||(l[p]="mm")));if(u){for(p=0;p<r.length;p++)!1!==r[p]?n[p]>12&&"YY"!==l[p]&&"YYYY"!==l[p]&&(l[p]="YY"):n[p]>12&&"mm"===l[p]&&(l[p]="dd");return(3===l.length&&"dd"===l[1]&&"dd"===l[2]&&(l[2]="YY"),h=l.join("/"),(i.dateFormats||t.dateFormats)[h])?h:(F(t,"deduceDateFailed"),a)}return a}(s[0])),this.dataFound()}return s}parseTable(){let e=this.options,t=this.columns||[],s=e.startRow||0,i=e.endRow||Number.MAX_VALUE,a=e.startColumn||0,r=e.endColumn||Number.MAX_VALUE;if(e.table){let n=e.table;"string"==typeof n&&(n=A.getElementById(n)),[].forEach.call(n.getElementsByTagName("tr"),(e,n)=>{n>=s&&n<=i&&[].forEach.call(e.children,(e,i)=>{let o=t[i-a],l=1;if(("TD"===e.tagName||"TH"===e.tagName)&&i>=a&&i<=r)for(t[i-a]||(t[i-a]=[]),t[i-a][n-s]=e.innerHTML;n-s>=l&&void 0===o[n-s-l];)o[n-s-l]=null,l++})}),this.dataFound()}return t}fetchLiveData(){let e=this,t=this.chart,s=this.options,i=s.enablePolling,a=H(s),r=0,n=1e3*(s.dataRefreshRate||2);return!!L(s)&&(n<1e3&&(n=1e3),delete s.csvURL,delete s.rowsURL,delete s.columnsURL,!function o(l){function h(a,h,d){if(!a||!/^(http|\/|\.\/|\.\.\/)/.test(a))return a&&s.error&&s.error("Invalid URL"),!1;function u(){i&&t.liveDataURL===a&&(e.liveDataTimeout=setTimeout(o,n))}return l&&(clearTimeout(e.liveDataTimeout),t.liveDataURL=a),Y({url:a,dataType:d||"json",success:function(e){t?.series&&h(e),u()},error:function(e,t){return++r<3&&u(),s.error?.(t,e)}}),!0}h(a.csvURL,function(e){t.update({data:{csv:e}})},"text")||h(a.rowsURL,function(e){t.update({data:{rows:e}})})||h(a.columnsURL,function(e){t.update({data:{columns:e}})})}(!0),L(s))}parseGoogleSpreadsheet(){let e=this,t=this.options,s=t.googleSpreadsheetKey,i=this.chart,a=Math.max(1e3*(t.dataRefreshRate||2),4e3),r=()=>{if(t.googleSpreadsheetRange)return t.googleSpreadsheetRange;let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ",s=(e.charAt(t.startColumn||0)||"A")+((t.startRow||0)+1),i=e.charAt(E(t.endColumn,-1))||"ZZ";return U(t.endRow)&&(i+=t.endRow+1),`${s}:${i}`};return s&&(delete t.googleSpreadsheetKey,!function i(n){Y({url:["https://sheets.googleapis.com/v4/spreadsheets",s,"values",r(),"?alt=json&majorDimension=COLUMNS&valueRenderOption=UNFORMATTED_VALUE&dateTimeRenderOption=FORMATTED_STRING&key="+t.googleAPIKey].join("/"),dataType:"json",success:function(s){n(s),t.enablePolling&&(e.liveDataTimeout=setTimeout(function(){i(n)},a))},error:function(e,s){return t.error?.(s,e)}})}(function(t){let s=t.values;if(!s||0===s.length)return!1;let a=s.reduce((e,t)=>Math.max(e,t.length),0);s.forEach(e=>{for(let t=0;t<a;t++)void 0===e[t]&&(e[t]=null)}),i?.series?i.update({data:{columns:s}}):(e.columns=s,e.dataFound())})),!1}trim(e,t){return"string"==typeof e&&(e=e.replace(/^\s+|\s+$/g,""),t&&/[\d\s]+/.test(e)&&(e=e.replace(/\s/g,"")),this.decimalRegex&&(e=e.replace(this.decimalRegex,"$1.$2"))),e}parseTypes(){let e=this.columns||[],t=e.length;for(;t--;)this.parseColumn(e[t],t)}parseColumn(e,t){let s=this.rawColumns,i=this.columns=this.columns||[],a=this.firstRowAsNames,r=this.valueCount?.xColumns.indexOf(t)!==-1,n=[],o=this.chartOptions,l=(this.options.columnTypes||[])[t],h=r&&o?.xAxis&&"category"===j(o.xAxis)[0].type||"string"===l,d=U(e.name),u=e.length,p,c,m,f,g,x,y;for(s[t]||(s[t]=[]);u--;)p=n[u]||e[u],m=this.trim(p),c=parseFloat(f=this.trim(p,!0)),void 0===s[t][u]&&(s[t][u]=m),h||0===u&&a&&!d?e[u]=""+m:+f===c?(e[u]=c,c>31536e6&&"float"!==l?e.isDatetime=!0:e.isNumeric=!0,void 0!==e[u+1]&&(y=c>e[u+1])):(m?.length&&(g=this.parseDate(p)),r&&O(g)&&"float"!==l?(n[u]=p,e[u]=g,e.isDatetime=!0,void 0!==e[u+1]&&((x=g>e[u+1])!==y&&void 0!==y&&(this.alternativeFormat?(this.dateFormat=this.alternativeFormat,u=e.length,this.alternativeFormat=this.dateFormats[this.dateFormat].alternative):e.unsorted=!0),y=x)):(e[u]=""===m?null:m,0!==u&&(e.isDatetime||e.isNumeric)&&(e.mixed=!0)));if(r&&e.mixed&&(i[t]=s[t]),r&&y&&this.options.sort){for(t=0;t<i.length;t++)if(i[t].reverse(),a){let e=i[t].pop();e&&i[t].unshift(e)}}}parseDate(e){let t=this.options.parseDate,s,i,a,r=this.options.dateFormat||this.dateFormat,n;if(t)s=t(e);else if("string"==typeof e){if(r)(a=this.dateFormats[r])||(a=this.dateFormats["YYYY/mm/dd"]),(n=e.match(a.regex))&&(s=a.parser(n));else for(i in this.dateFormats)if(a=this.dateFormats[i],n=e.match(a.regex)){this.dateFormat=r=i,this.alternativeFormat=a.alternative,s=a.parser(n);break}!n&&(e.match(/:.+(GMT|UTC|[Z+\-])/)&&(e=e.replace(/\s*(?:GMT|UTC)?([+\-])(\d\d)(\d\d)$/,"$1$2:$3").replace(/(?:\s+|GMT|UTC)([+\-])/,"$1").replace(/(\d)\s*(?:GMT|UTC|Z)$/,"$1+00:00")),"object"==typeof(n=Date.parse(e))&&null!==n&&n.getTime?s=n.getTime()-6e4*n.getTimezoneOffset():O(n)&&(s=n-6e4*new Date(n).getTimezoneOffset()))}return s}getData(){if(this.columns)return this.rowsToColumns(this.columns)?.slice(1)}parsed(){if(this.options.parsed)return this.options.parsed.call(this,this.columns)}complete(){let e=this.columns=this.columns||[],t=this.options,s=[],i="linear",a,r,n,o,l,h,d,u,p,c,m;if([].length=e.length,t.complete||t.afterComplete){if(this.firstRowAsNames)for(n=0;n<e.length;n++){let t=e[n];U(t.name)||(t.name=E(t.shift(),"").toString())}for(h=0,a=[],p=function(e,t){let s,i,a,r=[],n=[];for(i=0;i<e;i+=1)r.push(!0);for(s=0;s<t.length;s+=1)for(i=0,a=t[s].getReferencedColumnIndexes();i<a.length;i+=1)r[a[i]]=!1;for(i=0;i<r.length;i+=1)r[i]&&n.push(i);return n}(e?.length||0,this.valueCount.seriesBuilders);h<this.valueCount.seriesBuilders.length;h++)(u=this.valueCount.seriesBuilders[h]).populateColumns(p)&&s.push(u);for(;p.length>0;){for((u=new I).addColumnReader(0,"x"),-1!==(m=p.indexOf(0))&&p.splice(m,1),n=0;n<this.valueCount.global;n++)u.addColumnReader(void 0,this.valueCount.globalPointArrayMap[n]);u.populateColumns(p)&&s.push(u)}if(s.length>0&&s[0].readers.length>0&&void 0!==(c=e?.[s[0].readers[0].columnIndex??-1])&&(c.isDatetime?i="datetime":c.isNumeric||(i="category")),"category"===i)for(h=0;h<s.length;h++)for(l=0,u=s[h];l<u.readers.length;l++)"x"===u.readers[l].configName&&(u.readers[l].configName="name");for(h=0;h<s.length;h++){for(o=0,u=s[h],r=[];o<e[0].length;o++)r[o]=u.read(e,o);a[h]={data:r,pointStart:r[0]&&(u.pointIsArray?r[0]?.[0]:r[0]?.x)||void 0},u.name&&(a[h].name=u.name),"category"===i&&(a[h].turboThreshold=0,a[h].pointStart=0)}d={series:a},"linear"!==i||this.xAxisOptions&&this.xAxisOptions.type!==i?(this.xAxisOptions={type:i},"category"===i&&(this.xAxisOptions.uniqueNames=!1)):i=this.xAxisOptions=void 0,this.chart||H(!0,d,{xAxis:this.xAxisOptions||{}}),t.complete?.(d),t.afterComplete?.(this,d)}}xAxisUpdateHandler(e){let t=this.xAxisOptions;t&&(!e.options.type&&t.type&&(e.type=t.type),e.options.uniqueNames||!1!==t.uniqueNames||(e.uniqueNames=t.uniqueNames))}update(e,t){let s=this.chart,i=s.options;e&&(e.afterComplete=function(e,i){if(!i)return;let a=s.xAxis[0],r=e.xAxisOptions;r&&a&&(a.type!==r.type&&!a.options.type||a.uniqueNames&&!1===r.uniqueNames&&void 0===a.options.uniqueNames)?a.update({},!1):(i?.series||[]).forEach(function(e){delete e.pointStart}),s.update(i,t,!0)},H(!0,i.data,e),i.data?.googleSpreadsheetKey&&!e.columns&&delete i.data.columns,this.init(i.data||{},i))}}w(g(),"afterSetOptions",function(){this.isXAxis&&(!this.chart.xAxis.length||this.chart.xAxis[0]===this)&&this.chart.data?.xAxisUpdateHandler(this)}),w(y(),"init",function(e){let t=this,s=e.args[1],i=R().data,a=e.args[0]||{};(i||a&&a.data)&&!t.hasDataDef&&(t.hasDataDef=!0,t.data=new M(b(H(i,a.data),{afterComplete:function(e,i){let r,n;if(Object.hasOwnProperty.call(a,"series"))if("object"==typeof a.series)for(r=Math.max(a.series.length,i?.series?.length??0);r--;)n=a.series[r]||{},a.series[r]=H(n,i?.series?.[r]??{});else delete a.series;a=H(i,a),t.data=e,t.init(a,s)}}),a,t),e.preventDefault())});class I{constructor(){this.readers=[],this.pointIsArray=!0}populateColumns(e){let t=!0;return this.readers.forEach(t=>{void 0===t.columnIndex&&(t.columnIndex=e.shift())}),this.readers.forEach(e=>{void 0===e.columnIndex&&(t=!1)}),t}read(e,t){let s=this.pointIsArray,i=s?[]:{};if(this.readers.forEach(a=>{let r=e[a.columnIndex][t];s?i.push(r):a.configName.indexOf(".")>0?C().prototype.setNestedProperty(i,r,a.configName):i[a.configName]=r}),void 0===this.name&&this.readers.length>=2){let t=[];this.readers.forEach(function(e){("x"===e.configName||"name"===e.configName||"y"===e.configName)&&void 0!==e.columnIndex&&t.push(e.columnIndex)}),t.length>=2&&(t.shift(),t.sort(function(e,t){return e-t})),this.name=e[E(t.shift(),0)].name}return i}addColumnReader(e,t){this.readers.push({columnIndex:e,configName:t}),"x"!==t&&"y"!==t&&void 0!==t&&(this.pointIsArray=!1)}getReferencedColumnIndexes(){let e,t,s=[];for(e=0;e<this.readers.length;e+=1)void 0!==(t=this.readers[e]).columnIndex&&s.push(t.columnIndex);return s}hasReader(e){let t;for(t=0;t<this.readers.length;t+=1)if(this.readers[t].configName===e)return!0}}let P=d();P.Data=P.Data||M,P.HttpUtilities=P.HttpUtilities||m,P.ajax=P.HttpUtilities.ajax,P.data=P.Data.data,P.getJSON=P.HttpUtilities.getJSON,P.post=P.HttpUtilities.post;let $=d();return l.default})());