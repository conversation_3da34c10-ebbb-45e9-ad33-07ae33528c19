!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/geoheatmap
 * @requires highcharts
 *
 * (c) 2009-2025
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/modules/geoheatmap",["highcharts/highcharts"],function(t){return e(t,t.SeriesRegistry)}):"object"==typeof exports?exports["highcharts/modules/geoheatmap"]=e(t._Highcharts,t._Highcharts.SeriesRegistry):t.Highcharts=e(t.Highcharts,t.Highcharts.SeriesRegistry)}("undefined"==typeof window?this:window,(t,e)=>(()=>{"use strict";var i={512:t=>{t.exports=e},944:e=>{e.exports=t}},o={};function a(t){var e=o[t];if(void 0!==e)return e.exports;var r=o[t]={exports:{}};return i[t](r,r.exports,a),r.exports}a.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return a.d(e,{a:e}),e},a.d=(t,e)=>{for(var i in e)a.o(e,i)&&!a.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},a.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var r={};a.d(r,{default:()=>O});var s=a(944),n=a.n(s),l=a(512),h=a.n(l);let{map:{prototype:{pointClass:p}}}=h().seriesTypes,{isNumber:d}=n(),c=class extends p{applyOptions(t,e){let i=super.applyOptions.call(this,t,e),{lat:o,lon:a}=i.options;if(d(a)&&d(o)){let{colsize:t=1,rowsize:e=1}=this.series.options,r=a-t/2,s=o-e/2;i.geometry=i.options.geometry={type:"Polygon",coordinates:[[[r,s],[r+t,s],[r+t,s+e],[r,s+e],[r,s]]]}}return i}},{doc:g}=n(),{defined:u,pick:y}=n(),{animObject:m,stop:f}=n(),{noop:x}=n(),{colorFromPoint:b,getContext:w}={colorFromPoint:function(t,e){let i=e.series.colorAxis;if(i){let o=i.toColor(t||0,e).split(")")[0].split("(")[1].split(",").map(t=>y(parseFloat(t),parseInt(t,10)));return o[3]=255*y(o[3],1),u(t)&&e.visible||(o[3]=0),o}return[0,0,0,0]},getContext:function(t){let{canvas:e,context:i}=t;return e&&i?(i.clearRect(0,0,e.width,e.height),i):(t.canvas=g.createElement("canvas"),t.context=t.canvas.getContext("2d",{willReadFrequently:!0})||void 0,t.context)}},{seriesTypes:{map:v}}=h(),{addEvent:D,error:C,extend:P,isNumber:I,isObject:j,merge:T,pick:L}=n();function R(t){return t-360*Math.floor((t+180)/360)}class z extends v{constructor(){super(...arguments),this.isDirtyCanvas=!0}update(){this.options=T(this.options,arguments[0]),this.getInterpolation().enabled&&(this.isDirtyCanvas=!0,this.points.forEach(t=>{t.graphic&&(t.graphic.destroy(),delete t.graphic)})),super.update.apply(this,arguments)}translate(){(!this.getInterpolation().enabled||!this.image||this.isDirty||this.isDirtyData)&&super.translate.apply(this,arguments)}getInterpolation(){return j(this.options.interpolation)?this.options.interpolation:{blur:1,enabled:this.options.interpolation}}drawPoints(){let t=this.chart.mapView,e=this.options;if(this.getInterpolation().enabled&&t&&this.bounds){let o=this.context||w(this),{canvas:a,colorAxis:r,image:s,chart:n,points:l}=this,[h,p]=[L(e.colsize,1),L(e.rowsize,1)],d=t.projectedUnitsToPixels({x:this.bounds.x1,y:this.bounds.y2}),c=t.projectedUnitsToPixels({x:this.bounds.x2,y:this.bounds.y1});if(a&&o&&r&&d&&c){let{x:e,y:r}=d,g=c.x-e,u=c.y-r,y={x:e,y:r,width:g,height:u};if(this.isDirtyCanvas||this.isDirtyData||"Orthographic"===t.projection.options.name){let s=a.width=~~(360/h)+1,n=a.height=~~(180/p)+1,d=new Uint8ClampedArray(s*n*4),{lat:c=0,lon:y=0}=l[0].options,m=y%p!=0,f=c%h!=0,x=m?t=>Math.round(t/p)*p:t=>t,w=f?t=>Math.round(t/h)*h:t=>t,v=l.length;(m||f)&&C("Highcharts Warning: For best performance, lon/lat datapoints should spaced by a single colsize/rowsize",!1,this.chart,{colsize:String(h),rowsize:String(p)}),this.directTouch=!1,this.isDirtyCanvas=!0;for(let t=0;t<v;t++){var i;let e=l[t],{lon:o,lat:a}=e.options;I(o)&&I(a)&&d.set(b(e.value,e),4*(i=x(o),Math.ceil(s*(n-1-(w(a)+90)/p)+(i+180)/h)))}let D=this.getInterpolation().blur,P=0===D?1:11*D,j=~~(s*P),T=~~(n*P),L=~~g,R=~~u,z=new ImageData(d,s,n);a.width=j,a.height=T,o.putImageData(z,0,0),o.globalCompositeOperation="copy",o.drawImage(a,0,0,z.width,z.height,0,0,j,T);let O=this.getProjectedImageData(t,L,R,o.getImageData(0,0,j,T),a,e,r);a.width=L,a.height=R,o.putImageData(new ImageData(O,L,R),0,0)}if(s)if(n.renderer.globalAnimation&&n.hasRendered){let t=Number(s.attr("x")),i=Number(s.attr("y")),o=Number(s.attr("width")),l=Number(s.attr("height")),h=(a,n)=>{let h=n.pos;s.attr({x:t+(e-t)*h,y:i+(r-i)*h,width:o+(g-o)*h,height:l+(u-l)*h})},p=T(m(n.renderer.globalAnimation)),d=p.step;p.step=function(){d&&d.apply(this,arguments),h.apply(this,arguments)},s.attr(T({animator:0},this.isDirtyCanvas?{href:a.toDataURL("image/png",1)}:void 0)).animate({animator:1},p)}else f(s),s.attr(T(y,this.isDirtyCanvas?{href:a.toDataURL("image/png",1)}:void 0));else this.image=n.renderer.image(a.toDataURL("image/png",1)).attr(y).add(this.group);this.isDirtyCanvas=!1}}else super.drawPoints.apply(this,arguments)}getProjectedImageData(t,e,i,o,a,r,s){let n=new Uint8ClampedArray(e*i*4),l=L(t.projection.options.rotation?.[0],0),h=a.width/360,p=-1*a.height/180,d=-1;for(let i=0;i<n.length;i+=4){let c=i/4%e;0===c&&d++;let g=t.pixelsToLonLat({x:r+c,y:s+d});if(g){g.lon>-180-l&&g.lon<180-l&&(g.lon=R(g.lon));let t=[g.lon,g.lat],e=t[0]*h+a.width/2,r=t[1]*p+a.height/2;if(e>=0&&e<=a.width&&r>=0&&r<=a.height){let t=Math.floor(r)*a.width*4+4*Math.round(e);n[i]=o.data[t],n[i+1]=o.data[t+1],n[i+2]=o.data[t+2],n[i+3]=o.data[t+3]}}}return n}searchPoint(t,e){let i=this.chart,o=i.mapView;if(o&&this.bounds&&this.image&&i.tooltip&&i.tooltip.options.enabled)if(!i.pointer.hasDragged&&(.01>=+this.image.attr("animator")||+this.image.attr("animator")>=.99)){let a=o.projectedUnitsToPixels({x:this.bounds.x1,y:this.bounds.y2}),r=o.projectedUnitsToPixels({x:this.bounds.x2,y:this.bounds.y1});if(i.pointer.normalize(t),t.lon&&t.lat&&a&&r&&t.chartX-i.plotLeft>a.x&&t.chartX-i.plotLeft<r.x&&t.chartY-i.plotTop>a.y&&t.chartY-i.plotTop<r.y)return this.searchKDTree({clientX:t.chartX,lon:R(t.lon),lat:t.lat},e,t)}else i.tooltip.destroy()}}z.defaultOptions=T(v.defaultOptions,{nullColor:"transparent",tooltip:{pointFormat:"Lat: {point.lat}, Lon: {point.lon}, Value: {point.value}<br/>"},borderWidth:0,colsize:1,rowsize:1,stickyTracking:!0,interpolation:{enabled:!1,blur:1}}),D(z,"afterDataClassLegendClick",function(){this.isDirtyCanvas=!0,this.drawPoints()}),P(z.prototype,{type:"geoheatmap",applyJitter:x,pointClass:c,pointArrayMap:["lon","lat","value"],kdAxisArray:["lon","lat"]}),h().registerSeriesType("geoheatmap",z);let O=n();return r.default})());