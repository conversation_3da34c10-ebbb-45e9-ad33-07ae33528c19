{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/variable-pie\n * @requires highcharts\n *\n * Variable Pie module for Highcharts\n *\n * (c) 2010-2025 <PERSON><PERSON><PERSON><PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/variable-pie\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/variable-pie\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ variable_pie_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/VariablePie/VariablePieSeriesDefaults.js\n/* *\n *\n *  Variable Pie module for Highcharts\n *\n *  (c) 2010-2025 Grzegorz Blachliński\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A variable pie series is a two dimensional series type, where each point\n * renders an Y and Z value.  Each point is drawn as a pie slice where the\n * size (arc) of the slice relates to the Y value and the radius of pie\n * slice relates to the Z value.\n *\n * @sample {highcharts} highcharts/demo/variable-radius-pie/\n *         Variable-radius pie chart\n *\n * @extends      plotOptions.pie\n * @excluding    dragDrop\n * @since        6.0.0\n * @product      highcharts\n * @requires     modules/variable-pie\n * @optionparent plotOptions.variablepie\n */\nconst VariablePieSeriesDefaults = {\n    /**\n     * The minimum size of the points' radius related to chart's `plotArea`.\n     * If a number is set, it applies in pixels.\n     *\n     * @sample {highcharts} highcharts/variable-radius-pie/min-max-point-size/\n     *         Example of minPointSize and maxPointSize\n     * @sample {highcharts} highcharts/variable-radius-pie/min-point-size-100/\n     *         minPointSize set to 100\n     *\n     * @type  {number|string}\n     * @since 6.0.0\n     */\n    minPointSize: '10%',\n    /**\n     * The maximum size of the points' radius related to chart's `plotArea`.\n     * If a number is set, it applies in pixels.\n     *\n     * @sample {highcharts} highcharts/variable-radius-pie/min-max-point-size/\n     *         Example of minPointSize and maxPointSize\n     *\n     * @type  {number|string}\n     * @since 6.0.0\n     */\n    maxPointSize: '100%',\n    /**\n     * The minimum possible z value for the point's radius calculation. If\n     * the point's Z value is smaller than zMin, the slice will be drawn\n     * according to the zMin value.\n     *\n     * @sample {highcharts} highcharts/variable-radius-pie/zmin-5/\n     *         zMin set to 5, smaller z values are treated as 5\n     * @sample {highcharts} highcharts/variable-radius-pie/zmin-zmax/\n     *         Series limited by both zMin and zMax\n     *\n     * @type  {number}\n     * @since 6.0.0\n     */\n    zMin: void 0,\n    /**\n     * The maximum possible z value for the point's radius calculation. If\n     * the point's Z value is bigger than zMax, the slice will be drawn\n     * according to the zMax value\n     *\n     * @sample {highcharts} highcharts/variable-radius-pie/zmin-zmax/\n     *         Series limited by both zMin and zMax\n     *\n     * @type  {number}\n     * @since 6.0.0\n     */\n    zMax: void 0,\n    /**\n     * Whether the pie slice's value should be represented by the area or\n     * the radius of the slice. Can be either `area` or `radius`. The\n     * default, `area`, corresponds best to the human perception of the size\n     * of each pie slice.\n     *\n     * @sample {highcharts} highcharts/variable-radius-pie/sizeby/\n     *         Difference between area and radius sizeBy\n     *\n     * @type  {Highcharts.VariablePieSizeByValue}\n     * @since 6.0.0\n     */\n    sizeBy: 'area',\n    tooltip: {\n        pointFormat: '<span style=\"color:{point.color}\">\\u25CF</span> {series.name}<br/>Value: {point.y}<br/>Size: {point.z}<br/>'\n    }\n};\n/**\n * A `variablepie` series. If the [type](#series.variablepie.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.variablepie\n * @excluding dataParser, dataURL, stack, xAxis, yAxis, dataSorting,\n *            boostThreshold, boostBlending\n * @product   highcharts\n * @requires  modules/variable-pie\n * @apioption series.variablepie\n */\n/**\n * An array of data points for the series. For the `variablepie` series type,\n * points can be given in the following ways:\n *\n * 1. An array of arrays with 2 values. In this case, the numerical values will\n *    be interpreted as `y, z` options. Example:\n *    ```js\n *    data: [\n *        [40, 75],\n *        [50, 50],\n *        [60, 40]\n *    ]\n *    ```\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.variablepie.turboThreshold), this option is not\n *    available.\n *    ```js\n *    data: [{\n *        y: 1,\n *        z: 4,\n *        name: \"Point2\",\n *        color: \"#00FF00\"\n *    }, {\n *        y: 7,\n *        z: 10,\n *        name: \"Point1\",\n *        color: \"#FF00FF\"\n *    }]\n *    ```\n *\n * @sample {highcharts} highcharts/series/data-array-of-arrays/\n *         Arrays of numeric x and y\n * @sample {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *         Arrays of datetime x and y\n * @sample {highcharts} highcharts/series/data-array-of-name-value/\n *         Arrays of point.name and y\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<Array<(number|string),number>|*>}\n * @extends   series.pie.data\n * @excluding marker, x\n * @product   highcharts\n * @apioption series.variablepie.data\n */\n''; // Keeps doclets above separate\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const VariablePie_VariablePieSeriesDefaults = (VariablePieSeriesDefaults);\n\n;// ./code/es-modules/Series/VariablePie/VariablePieSeries.js\n/* *\n *\n *  Variable Pie module for Highcharts\n *\n *  (c) 2010-2025 Grzegorz Blachliński\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { pie: PieSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { arrayMax, arrayMin, clamp, extend, fireEvent, merge, pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * The variablepie series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.variablepie\n *\n * @augments Highcharts.Series\n */\nclass VariablePieSeries extends PieSeries {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Before standard translate method for pie chart it is needed to calculate\n     * min/max radius of each pie slice based on its Z value.\n     * @private\n     */\n    calculateExtremes() {\n        const series = this, chart = series.chart, plotWidth = chart.plotWidth, plotHeight = chart.plotHeight, seriesOptions = series.options, slicingRoom = 2 * (seriesOptions.slicedOffset || 0), zData = series.getColumn('z'), smallestSize = Math.min(plotWidth, plotHeight) - slicingRoom, \n        // Min and max size of pie slice:\n        extremes = {}, \n        // In pie charts size of a pie is changed to make space for\n        // dataLabels, then series.center is changing.\n        positions = series.center || series.getCenter();\n        let zMin, zMax;\n        for (const prop of ['minPointSize', 'maxPointSize']) {\n            let length = seriesOptions[prop];\n            const isPercent = /%$/.test(length);\n            length = parseInt(length, 10);\n            extremes[prop] = isPercent ?\n                smallestSize * length / 100 :\n                length * 2; // Because it should be radius, not diameter.\n        }\n        series.minPxSize = positions[3] + extremes.minPointSize;\n        series.maxPxSize = clamp(positions[2], positions[3] + extremes.minPointSize, extremes.maxPointSize);\n        if (zData.length) {\n            zMin = pick(seriesOptions.zMin, arrayMin(zData.filter(series.zValEval)));\n            zMax = pick(seriesOptions.zMax, arrayMax(zData.filter(series.zValEval)));\n            this.getRadii(zMin, zMax, series.minPxSize, series.maxPxSize);\n        }\n    }\n    /**\n     * Finding radius of series points based on their Z value and min/max Z\n     * value for all series.\n     *\n     * @private\n     * @function Highcharts.Series#getRadii\n     *\n     * @param {number} zMin\n     * Min threshold for Z value. If point's Z value is smaller that zMin, point\n     * will have the smallest possible radius.\n     *\n     * @param {number} zMax\n     * Max threshold for Z value. If point's Z value is bigger that zMax, point\n     * will have the biggest possible radius.\n     *\n     * @param {number} minSize\n     * Minimal pixel size possible for radius.\n     *\n     * @param {numbner} maxSize\n     * Minimal pixel size possible for radius.\n     */\n    getRadii(zMin, zMax, minSize, maxSize) {\n        const zData = this.getColumn('z'), radii = [], options = this.options, sizeByArea = options.sizeBy !== 'radius', zRange = zMax - zMin;\n        let pos, value, radius;\n        // Calculate radius for all pie slice's based on their Z values\n        for (let i = 0; i < zData.length; i++) {\n            // If zData[i] is null/undefined/string we need to take zMin for\n            // smallest radius.\n            value = this.zValEval(zData[i]) ? zData[i] : zMin;\n            if (value <= zMin) {\n                radius = minSize / 2;\n            }\n            else if (value >= zMax) {\n                radius = maxSize / 2;\n            }\n            else {\n                // Relative size, a number between 0 and 1\n                pos = zRange > 0 ? (value - zMin) / zRange : 0.5;\n                if (sizeByArea) {\n                    pos = Math.sqrt(pos);\n                }\n                radius = Math.ceil(minSize + pos * (maxSize - minSize)) / 2;\n            }\n            radii.push(radius);\n        }\n        this.radii = radii;\n    }\n    /**\n     * It is needed to null series.center on chart redraw. Probably good idea\n     * will be to add this option in directly in pie series.\n     * @private\n     */\n    redraw() {\n        this.center = null;\n        super.redraw();\n    }\n    /** @private */\n    getDataLabelPosition(point, distance) {\n        const { center, options } = this, angle = point.angle || 0, r = this.radii[point.index], x = center[0] + Math.cos(angle) * r, y = center[1] + Math.sin(angle) * r, connectorOffset = (options.slicedOffset || 0) +\n            (options.borderWidth || 0), \n        // Set the anchor point for data labels. Use point.labelDistance\n        // instead of labelDistance // #1174\n        // finalConnectorOffset - not override connectorOffset value.\n        finalConnectorOffset = Math.min(connectorOffset, distance / 5); // #1678\n        return {\n            distance,\n            natural: {\n                // Initial position of the data label - it's utilized for\n                // finding the final position for the label\n                x: x + Math.cos(angle) * distance,\n                y: y + Math.sin(angle) * distance\n            },\n            computed: {\n            // Used for generating connector path - initialized later in\n            // drawDataLabels function x: undefined, y: undefined\n            },\n            // Left - pie on the left side of the data label\n            // Right - pie on the right side of the data label\n            alignment: point.half ? 'right' : 'left',\n            connectorPosition: {\n                breakAt: {\n                    x: x + Math.cos(angle) * finalConnectorOffset,\n                    y: y + Math.sin(angle) * finalConnectorOffset\n                },\n                touchingSliceAt: {\n                    x,\n                    y\n                }\n            }\n        };\n    }\n    /**\n     * Extend translate by updating radius for each pie slice instead of using\n     * one global radius.\n     * @private\n     */\n    translate(positions) {\n        this.generatePoints();\n        const series = this, precision = 1000, // Issue #172\n        options = series.options, slicedOffset = options.slicedOffset, startAngle = options.startAngle || 0, startAngleRad = Math.PI / 180 * (startAngle - 90), endAngleRad = Math.PI / 180 * (pick(options.endAngle, startAngle + 360) - 90), circ = endAngleRad - startAngleRad, // 2 * Math.PI,\n        points = series.points, ignoreHiddenPoint = options.ignoreHiddenPoint;\n        let cumulative = 0, start, end, angle, \n        // The x component of the radius vector for a given point\n        radiusX, radiusY, point, pointRadii;\n        series.startAngleRad = startAngleRad;\n        series.endAngleRad = endAngleRad;\n        // Use calculateExtremes to get series.radii array.\n        series.calculateExtremes();\n        // Get positions - either an integer or a percentage string must be\n        // given. If positions are passed as a parameter, we're in a\n        // recursive loop for adjusting space for data labels.\n        if (!positions) {\n            series.center = positions = series.getCenter();\n        }\n        // Calculate the geometry for each point\n        for (let i = 0; i < points.length; i++) {\n            point = points[i];\n            pointRadii = series.radii[i];\n            // Set start and end angle\n            start = startAngleRad + (cumulative * circ);\n            if (!ignoreHiddenPoint || point.visible) {\n                cumulative += point.percentage / 100;\n            }\n            end = startAngleRad + (cumulative * circ);\n            // Set the shape\n            point.shapeType = 'arc';\n            point.shapeArgs = {\n                x: positions[0],\n                y: positions[1],\n                r: pointRadii,\n                innerR: positions[3] / 2,\n                start: Math.round(start * precision) / precision,\n                end: Math.round(end * precision) / precision\n            };\n            // The angle must stay within -90 and 270 (#2645)\n            angle = (end + start) / 2;\n            if (angle > 1.5 * Math.PI) {\n                angle -= 2 * Math.PI;\n            }\n            else if (angle < -Math.PI / 2) {\n                angle += 2 * Math.PI;\n            }\n            // Center for the sliced out slice\n            point.slicedTranslation = {\n                translateX: Math.round(Math.cos(angle) * slicedOffset),\n                translateY: Math.round(Math.sin(angle) * slicedOffset)\n            };\n            // Set the anchor point for tooltips\n            radiusX = Math.cos(angle) * positions[2] / 2;\n            radiusY = Math.sin(angle) * positions[2] / 2;\n            point.tooltipPos = [\n                positions[0] + radiusX * 0.7,\n                positions[1] + radiusY * 0.7\n            ];\n            point.half = angle < -Math.PI / 2 || angle > Math.PI / 2 ?\n                1 :\n                0;\n            point.angle = angle;\n        }\n        fireEvent(series, 'afterTranslate');\n    }\n    /**\n     * For arrayMin and arrayMax calculations array shouldn't have\n     * null/undefined/string values. In this case it is needed to check if\n     * points Z value is a Number.\n     * @private\n     */\n    zValEval(zVal) {\n        if (typeof zVal === 'number' && !isNaN(zVal)) {\n            return true;\n        }\n        return null;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nVariablePieSeries.defaultOptions = merge(PieSeries.defaultOptions, VariablePie_VariablePieSeriesDefaults);\nextend(VariablePieSeries.prototype, {\n    pointArrayMap: ['y', 'z'],\n    parallelArrays: ['x', 'y', 'z']\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('variablepie', VariablePieSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const VariablePie_VariablePieSeries = ((/* unused pure expression or super */ null && (VariablePieSeries)));\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * @typedef {\"area\"|\"radius\"} Highcharts.VariablePieSizeByValue\n */\n''; // Adds doclets above to transpiled file\n\n;// ./code/es-modules/masters/modules/variable-pie.js\n\n\n\n\n/* harmony default export */ const variable_pie_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "variable_pie_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "pie", "PieSeries", "seriesTypes", "arrayMax", "arrayMin", "clamp", "extend", "fireEvent", "merge", "pick", "VariablePieSeries", "calculateExtremes", "zMin", "zMax", "chart", "series", "plot<PERSON>id<PERSON>", "plotHeight", "seriesOptions", "options", "slicingRoom", "slicedOffset", "zData", "getColumn", "smallestSize", "Math", "min", "extremes", "positions", "center", "getCenter", "length", "isPercent", "test", "parseInt", "minPxSize", "minPointSize", "maxPxSize", "maxPointSize", "filter", "zValEval", "getRadii", "minSize", "maxSize", "pos", "value", "radius", "radii", "sizeByArea", "sizeBy", "zRange", "i", "sqrt", "ceil", "push", "redraw", "getDataLabelPosition", "point", "distance", "angle", "r", "index", "x", "cos", "y", "sin", "finalConnectorOffset", "borderWidth", "natural", "computed", "alignment", "half", "connectorPosition", "breakAt", "touchingSliceAt", "translate", "generatePoints", "startAngle", "startAngleRad", "PI", "endAngleRad", "endAngle", "circ", "points", "ignoreHiddenPoint", "cumulative", "start", "end", "radiusX", "radiusY", "pointRadii", "visible", "percentage", "shapeType", "shapeArgs", "innerR", "round", "slicedTranslation", "translateX", "translateY", "tooltipPos", "zVal", "isNaN", "defaultOptions", "tooltip", "pointFormat", "pointArrayMap", "parallelArrays", "registerSeriesType"], "mappings": "CAWA,AAXA;;;;;;;;;;CAUC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,kCAAmC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GAC3H,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,kCAAkC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAE/GA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAwLjL,GAAM,CAAEE,IAAKC,CAAS,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAE5K,CAAEC,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAEC,UAAAA,CAAS,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAE,CAAIZ,GAgBvE,OAAMa,UAA0BT,EAW5BU,mBAAoB,CAChB,IAMIC,EAAMC,EANWC,EAAQC,AAAd,IAAI,CAAiBD,KAAK,CAAEE,EAAYF,EAAME,SAAS,CAAEC,EAAaH,EAAMG,UAAU,CAAEC,EAAgBH,AAAxG,IAAI,CAA2GI,OAAO,CAAEC,EAAc,EAAKF,CAAAA,EAAcG,YAAY,EAAI,CAAA,EAAIC,EAAQP,AAArL,IAAI,CAAwLQ,SAAS,CAAC,KAAMC,EAAeC,KAAKC,GAAG,CAACV,EAAWC,GAAcG,EAE5QO,EAAW,CAAC,EAGZC,EAAYb,AALG,IAAI,CAKAc,MAAM,EAAId,AALd,IAAI,CAKiBe,SAAS,GAE7C,IAAK,IAAMxC,IAAQ,CAAC,eAAgB,eAAe,CAAE,CACjD,IAAIyC,EAASb,CAAa,CAAC5B,EAAK,CAC1B0C,EAAY,KAAKC,IAAI,CAACF,GAC5BA,EAASG,SAASH,EAAQ,IAC1BJ,CAAQ,CAACrC,EAAK,CAAG0C,EACbR,EAAeO,EAAS,IACxBA,AAAS,EAATA,CACR,CACAhB,AAfe,IAAI,CAeZoB,SAAS,CAAGP,CAAS,CAAC,EAAE,CAAGD,EAASS,YAAY,CACvDrB,AAhBe,IAAI,CAgBZsB,SAAS,CAAGhC,EAAMuB,CAAS,CAAC,EAAE,CAAEA,CAAS,CAAC,EAAE,CAAGD,EAASS,YAAY,CAAET,EAASW,YAAY,EAC9FhB,EAAMS,MAAM,GACZnB,EAAOH,EAAKS,EAAcN,IAAI,CAAER,EAASkB,EAAMiB,MAAM,CAACxB,AAlB3C,IAAI,CAkB8CyB,QAAQ,IACrE3B,EAAOJ,EAAKS,EAAcL,IAAI,CAAEV,EAASmB,EAAMiB,MAAM,CAACxB,AAnB3C,IAAI,CAmB8CyB,QAAQ,IACrE,IAAI,CAACC,QAAQ,CAAC7B,EAAMC,EAAME,AApBf,IAAI,CAoBkBoB,SAAS,CAAEpB,AApBjC,IAAI,CAoBoCsB,SAAS,EAEpE,CAsBAI,SAAS7B,CAAI,CAAEC,CAAI,CAAE6B,CAAO,CAAEC,CAAO,CAAE,CACnC,IACIC,EAAKC,EAAOC,EADVxB,EAAQ,IAAI,CAACC,SAAS,CAAC,KAAMwB,EAAQ,EAAE,CAA0BC,EAAa7B,AAAmB,WAAnBA,AAA3B,IAAI,CAACA,OAAO,CAAuB8B,MAAM,CAAeC,EAASrC,EAAOD,EAGjI,IAAK,IAAIuC,EAAI,EAAGA,EAAI7B,EAAMS,MAAM,CAAEoB,IAI1BN,AADJA,CAAAA,EAAQ,IAAI,CAACL,QAAQ,CAAClB,CAAK,CAAC6B,EAAE,EAAI7B,CAAK,CAAC6B,EAAE,CAAGvC,CAAG,GACnCA,EACTkC,EAASJ,EAAU,EAEdG,GAAShC,EACdiC,EAASH,EAAU,GAInBC,EAAMM,EAAS,EAAI,AAACL,CAAAA,EAAQjC,CAAG,EAAKsC,EAAS,GACzCF,GACAJ,CAAAA,EAAMnB,KAAK2B,IAAI,CAACR,EAAG,EAEvBE,EAASrB,KAAK4B,IAAI,CAACX,EAAUE,EAAOD,CAAAA,EAAUD,CAAM,GAAM,GAE9DK,EAAMO,IAAI,CAACR,EAEf,CAAA,IAAI,CAACC,KAAK,CAAGA,CACjB,CAMAQ,QAAS,CACL,IAAI,CAAC1B,MAAM,CAAG,KACd,KAAK,CAAC0B,QACV,CAEAC,qBAAqBC,CAAK,CAAEC,CAAQ,CAAE,CAClC,GAAM,CAAE7B,OAAAA,CAAM,CAAEV,QAAAA,CAAO,CAAE,CAAG,IAAI,CAAEwC,EAAQF,EAAME,KAAK,EAAI,EAAGC,EAAI,IAAI,CAACb,KAAK,CAACU,EAAMI,KAAK,CAAC,CAAEC,EAAIjC,CAAM,CAAC,EAAE,CAAGJ,KAAKsC,GAAG,CAACJ,GAASC,EAAGI,EAAInC,CAAM,CAAC,EAAE,CAAGJ,KAAKwC,GAAG,CAACN,GAASC,EAKhKM,EAAuBzC,KAAKC,GAAG,CALsJ,AAACP,CAAAA,EAAQE,YAAY,EAAI,CAAA,EACzMF,CAAAA,EAAQgD,WAAW,EAAI,CAAA,EAIqBT,EAAW,GAC5D,MAAO,CACHA,SAAAA,EACAU,QAAS,CAGLN,EAAGA,EAAIrC,KAAKsC,GAAG,CAACJ,GAASD,EACzBM,EAAGA,EAAIvC,KAAKwC,GAAG,CAACN,GAASD,CAC7B,EACAW,SAAU,CAGV,EAGAC,UAAWb,EAAMc,IAAI,CAAG,QAAU,OAClCC,kBAAmB,CACfC,QAAS,CACLX,EAAGA,EAAIrC,KAAKsC,GAAG,CAACJ,GAASO,EACzBF,EAAGA,EAAIvC,KAAKwC,GAAG,CAACN,GAASO,CAC7B,EACAQ,gBAAiB,CACbZ,EAAAA,EACAE,EAAAA,CACJ,CACJ,CACJ,CACJ,CAMAW,UAAU/C,CAAS,CAAE,CACjB,IAAI,CAACgD,cAAc,GACnB,IACAzD,EAAUJ,AADK,IAAI,CACFI,OAAO,CAAEE,EAAeF,EAAQE,YAAY,CAAEwD,EAAa1D,EAAQ0D,UAAU,EAAI,EAAGC,EAAgBrD,KAAKsD,EAAE,CAAG,IAAOF,CAAAA,EAAa,EAAC,EAAIG,EAAcvD,KAAKsD,EAAE,CAAG,IAAOtE,CAAAA,EAAKU,EAAQ8D,QAAQ,CAAEJ,EAAa,KAAO,EAAC,EAAIK,EAAOF,EAAcF,EAC5PK,EAASpE,AAFM,IAAI,CAEHoE,MAAM,CAAEC,EAAoBjE,EAAQiE,iBAAiB,CACjEC,EAAa,EAAGC,EAAOC,EAAK5B,EAEhC6B,EAASC,EAAShC,EAAOiC,CACzB3E,CANe,IAAI,CAMZ+D,aAAa,CAAGA,EACvB/D,AAPe,IAAI,CAOZiE,WAAW,CAAGA,EAErBjE,AATe,IAAI,CASZJ,iBAAiB,GAIpB,AAACiB,GACDb,CAAAA,AAdW,IAAI,CAcRc,MAAM,CAAGD,EAAYb,AAdjB,IAAI,CAcoBe,SAAS,EAAC,EAGjD,IAAK,IAAIqB,EAAI,EAAGA,EAAIgC,EAAOpD,MAAM,CAAEoB,IAC/BM,EAAQ0B,CAAM,CAAChC,EAAE,CACjBuC,EAAa3E,AAnBF,IAAI,CAmBKgC,KAAK,CAACI,EAAE,CAE5BmC,EAAQR,EAAiBO,EAAaH,EAClC,CAAA,CAACE,GAAqB3B,EAAMkC,OAAO,AAAD,GAClCN,CAAAA,GAAc5B,EAAMmC,UAAU,CAAG,GAAE,EAEvCL,EAAMT,EAAiBO,EAAaH,EAEpCzB,EAAMoC,SAAS,CAAG,MAClBpC,EAAMqC,SAAS,CAAG,CACdhC,EAAGlC,CAAS,CAAC,EAAE,CACfoC,EAAGpC,CAAS,CAAC,EAAE,CACfgC,EAAG8B,EACHK,OAAQnE,CAAS,CAAC,EAAE,CAAG,EACvB0D,MAAO7D,KAAKuE,KAAK,CAACV,AAjCO,IAiCPA,GAjCO,IAkCzBC,IAAK9D,KAAKuE,KAAK,CAACT,AAlCS,IAkCTA,GAlCS,GAmC7B,EAGI5B,AADJA,CAAAA,EAAQ,AAAC4B,CAAAA,EAAMD,CAAI,EAAK,CAAA,EACZ,IAAM7D,KAAKsD,EAAE,CACrBpB,GAAS,EAAIlC,KAAKsD,EAAE,CAEfpB,EAAQ,CAAClC,KAAKsD,EAAE,CAAG,GACxBpB,CAAAA,GAAS,EAAIlC,KAAKsD,EAAE,AAAD,EAGvBtB,EAAMwC,iBAAiB,CAAG,CACtBC,WAAYzE,KAAKuE,KAAK,CAACvE,KAAKsC,GAAG,CAACJ,GAAStC,GACzC8E,WAAY1E,KAAKuE,KAAK,CAACvE,KAAKwC,GAAG,CAACN,GAAStC,EAC7C,EAEAmE,EAAU/D,KAAKsC,GAAG,CAACJ,GAAS/B,CAAS,CAAC,EAAE,CAAG,EAC3C6D,EAAUhE,KAAKwC,GAAG,CAACN,GAAS/B,CAAS,CAAC,EAAE,CAAG,EAC3C6B,EAAM2C,UAAU,CAAG,CACfxE,CAAS,CAAC,EAAE,CAAG4D,AAAU,GAAVA,EACf5D,CAAS,CAAC,EAAE,CAAG6D,AAAU,GAAVA,EAClB,CACDhC,EAAMc,IAAI,CAAGZ,CAAAA,CAAAA,EAAQ,CAAClC,KAAKsD,EAAE,CAAG,GAAKpB,EAAQlC,KAAKsD,EAAE,CAAG,CAAA,EAGvDtB,EAAME,KAAK,CAAGA,EAElBpD,EA7De,IAAI,CA6DD,iBACtB,CAOAiC,SAAS6D,CAAI,CAAE,OACX,CAAI,CAAA,AAAgB,UAAhB,OAAOA,GAAsBC,MAAMD,EAAI,GAGpC,IACX,CACJ,CAMA3F,EAAkB6F,cAAc,CAAG/F,EAAMP,EAAUsG,cAAc,CA7X/B,CAa9BnE,aAAc,MAWdE,aAAc,OAcd1B,KAAM,KAAK,EAYXC,KAAM,KAAK,EAaXoC,OAAQ,OACRuD,QAAS,CACLC,YAAa,wGACjB,CACJ,GA2TAnG,EAAOI,EAAkBnB,SAAS,CAAE,CAChCmH,cAAe,CAAC,IAAK,IAAI,CACzBC,eAAgB,CAAC,IAAK,IAAK,IAAI,AACnC,GACA5G,IAA0I6G,kBAAkB,CAAC,cAAelG,GAsB/I,IAAMf,EAAqBE,IAG9C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}