{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/offline-exporting\n * @requires highcharts\n * @requires highcharts/modules/exporting\n *\n * Client side exporting module\n *\n * (c) 2015-2025 Torstein Honsi / Oystein Moseng\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"AST\"], root[\"_Highcharts\"][\"Chart\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/offline-exporting\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"AST\"],amd1[\"Chart\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/offline-exporting\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"AST\"], root[\"_Highcharts\"][\"Chart\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"AST\"], root[\"Highcharts\"][\"Chart\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__660__, __WEBPACK_EXTERNAL_MODULE__960__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 660:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__660__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ }),\n\n/***/ 960:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__960__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ offline_exporting_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Extensions/DownloadURL.js\n/* *\n *\n *  (c) 2015-2025 Oystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Mixin for downloading content in the browser\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\nconst { isSafari, win, win: { document: doc } } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { error } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\nconst domurl = win.URL || win.webkitURL || win;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Convert base64 dataURL to Blob if supported, otherwise returns undefined.\n *\n * @private\n * @function Highcharts.dataURLtoBlob\n *\n * @param {string} dataURL\n * URL to convert.\n *\n * @return {string | undefined}\n * Blob.\n */\nfunction dataURLtoBlob(dataURL) {\n    const parts = dataURL\n        .replace(/filename=.*;/, '')\n        .match(/data:([^;]*)(;base64)?,([A-Z+\\d\\/]+)/i);\n    if (parts &&\n        parts.length > 3 &&\n        (win.atob) &&\n        win.ArrayBuffer &&\n        win.Uint8Array &&\n        win.Blob &&\n        (domurl.createObjectURL)) {\n        // Try to convert data URL to Blob\n        const binStr = win.atob(parts[3]), buf = new win.ArrayBuffer(binStr.length), binary = new win.Uint8Array(buf);\n        for (let i = 0; i < binary.length; ++i) {\n            binary[i] = binStr.charCodeAt(i);\n        }\n        return domurl\n            .createObjectURL(new win.Blob([binary], { 'type': parts[1] }));\n    }\n}\n/**\n * Download a data URL in the browser. Can also take a blob as first param.\n *\n * @private\n * @function Highcharts.downloadURL\n *\n * @param {string | global.URL} dataURL\n * The dataURL/Blob to download.\n * @param {string} filename\n * The name of the resulting file (w/extension).\n */\nfunction downloadURL(dataURL, filename) {\n    const nav = win.navigator, a = doc.createElement('a');\n    // IE specific blob implementation\n    // Don't use for normal dataURLs\n    if (typeof dataURL !== 'string' &&\n        !(dataURL instanceof String) &&\n        nav.msSaveOrOpenBlob) {\n        nav.msSaveOrOpenBlob(dataURL, filename);\n        return;\n    }\n    dataURL = '' + dataURL;\n    if (nav.userAgent.length > 1000 /* RegexLimits.shortLimit */) {\n        throw new Error('Input too long');\n    }\n    const // Some browsers have limitations for data URL lengths. Try to convert\n    // to Blob or fall back. Edge always needs that blob.\n    isOldEdgeBrowser = /Edge\\/\\d+/.test(nav.userAgent), \n    // Safari on iOS needs Blob in order to download PDF\n    safariBlob = (isSafari &&\n        typeof dataURL === 'string' &&\n        dataURL.indexOf('data:application/pdf') === 0);\n    if (safariBlob || isOldEdgeBrowser || dataURL.length > 2000000) {\n        dataURL = dataURLtoBlob(dataURL) || '';\n        if (!dataURL) {\n            throw new Error('Failed to convert to blob');\n        }\n    }\n    // Try HTML5 download attr if supported\n    if (typeof a.download !== 'undefined') {\n        a.href = dataURL;\n        a.download = filename; // HTML5 download attribute\n        doc.body.appendChild(a);\n        a.click();\n        doc.body.removeChild(a);\n    }\n    else {\n        // No download attr, just opening data URI\n        try {\n            if (!win.open(dataURL, 'chart')) {\n                throw new Error('Failed to open window');\n            }\n        }\n        catch {\n            // If window.open failed, try location.href\n            win.location.href = dataURL;\n        }\n    }\n}\n/**\n * Asynchronously downloads a script from a provided location.\n *\n * @private\n * @function Highcharts.getScript\n *\n * @param {string} scriptLocation\n * The location for the script to fetch.\n */\nfunction getScript(scriptLocation) {\n    return new Promise((resolve, reject) => {\n        const head = doc.getElementsByTagName('head')[0], script = doc.createElement('script');\n        // Set type and location for the script\n        script.type = 'text/javascript';\n        script.src = scriptLocation;\n        // Resolve in case of a succesful script fetching\n        script.onload = () => {\n            resolve();\n        };\n        // Reject in case of fail\n        script.onerror = () => {\n            reject(error(`Error loading script ${scriptLocation}`));\n        };\n        // Append the newly created script\n        head.appendChild(script);\n    });\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst DownloadURL = {\n    dataURLtoBlob,\n    downloadURL,\n    getScript\n};\n/* harmony default export */ const Extensions_DownloadURL = (DownloadURL);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"AST\"],\"commonjs\":[\"highcharts\",\"AST\"],\"commonjs2\":[\"highcharts\",\"AST\"],\"root\":[\"Highcharts\",\"AST\"]}\nvar highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_ = __webpack_require__(660);\nvar highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default = /*#__PURE__*/__webpack_require__.n(highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Chart\"],\"commonjs\":[\"highcharts\",\"Chart\"],\"commonjs2\":[\"highcharts\",\"Chart\"],\"root\":[\"Highcharts\",\"Chart\"]}\nvar highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_ = __webpack_require__(960);\nvar highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default = /*#__PURE__*/__webpack_require__.n(highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_);\n;// ./code/es-modules/Extensions/OfflineExporting/OfflineExportingDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * @optionparent exporting\n * @private\n */\nconst exporting = {};\n/* *\n *\n *  Default Export\n *\n * */\nconst OfflineExportingDefaults = {\n    exporting\n};\n/* harmony default export */ const OfflineExporting_OfflineExportingDefaults = (OfflineExportingDefaults);\n\n;// ./code/es-modules/Extensions/OfflineExporting/OfflineExporting.js\n/* *\n *\n *  Client side exporting module\n *\n *  (c) 2015 Torstein Honsi / Oystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { getOptions, setOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { downloadURL: OfflineExporting_downloadURL, getScript: OfflineExporting_getScript } = Extensions_DownloadURL;\n\nconst { composed, doc: OfflineExporting_doc, win: OfflineExporting_win } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nconst { addEvent, extend, pushUnique } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\nvar OfflineExporting;\n(function (OfflineExporting) {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Composition function.\n     *\n     * @private\n     * @function compose\n     *\n     * @param {ExportingClass} ExportingClass\n     * Exporting class.\n     *\n     * @requires modules/exporting\n     * @requires modules/offline-exporting\n     */\n    function compose(ExportingClass) {\n        // Add the downloadSVG event to the Exporting class for local PDF export\n        addEvent(ExportingClass, 'downloadSVG', async function (e) {\n            const { svg, exportingOptions, exporting, preventDefault } = e;\n            // Check if PDF export is requested\n            if (exportingOptions?.type === 'application/pdf') {\n                // Prevent the default export behavior\n                preventDefault?.();\n                // Run the PDF local export\n                try {\n                    // Get the final image options\n                    const { type, filename, scale, libURL } = highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().Exporting.prepareImageOptions(exportingOptions);\n                    // Local PDF download\n                    if (type === 'application/pdf') {\n                        // Must load pdf libraries first if not found. Don't\n                        // destroy the object URL yet since we are doing\n                        // things asynchronously\n                        if (!OfflineExporting_win.jspdf?.jsPDF) {\n                            // Get jspdf\n                            await OfflineExporting_getScript(`${libURL}jspdf.js`);\n                            // Get svg2pdf\n                            await OfflineExporting_getScript(`${libURL}svg2pdf.js`);\n                        }\n                        // Call the PDF download if SVG element found\n                        await downloadPDF(svg, scale, filename, exportingOptions?.pdfFont);\n                    }\n                }\n                catch (error) {\n                    // Try to fallback to the server\n                    await exporting?.fallbackToServer(exportingOptions, error);\n                }\n            }\n        });\n        // Check the composition registry for the OfflineExporting\n        if (!pushUnique(composed, 'OfflineExporting')) {\n            return;\n        }\n        // Adding wrappers for the deprecated functions\n        extend((highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default()).prototype, {\n            exportChartLocal: async function (exportingOptions, chartOptions) {\n                await this.exporting?.exportChart(exportingOptions, chartOptions);\n                return;\n            }\n        });\n        // Update with defaults of the offline exporting module\n        setOptions(OfflineExporting_OfflineExportingDefaults);\n        // Additionaly, extend the menuItems with the offline exporting variants\n        const menuItems = getOptions().exporting?.buttons?.contextButton?.menuItems;\n        menuItems && menuItems.push('downloadPDF');\n    }\n    OfflineExporting.compose = compose;\n    /**\n     * Get data URL to an image of an SVG and call download on it options\n     * object:\n     * - **filename:** Name of resulting downloaded file without extension.\n     * Default is `chart`.\n     *\n     * - **type:** File type of resulting download. Default is `image/png`.\n     *\n     * - **scale:** Scaling factor of downloaded image compared to source.\n     * Default is `1`.\n     * - **libURL:** URL pointing to location of dependency scripts to download\n     * on demand. Default is the exporting.libURL option of the global\n     * Highcharts options pointing to our server.\n     *\n     * @function Highcharts.downloadSVGLocal\n     * @deprecated\n     *\n     * @param {string} svg\n     * The generated SVG\n     *\n     * @param {Highcharts.ExportingOptions} options\n     * The exporting options\n     *\n     */\n    async function downloadSVGLocal(svg, options) {\n        await highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().Exporting.prototype.downloadSVG.call(void 0, svg, options);\n    }\n    OfflineExporting.downloadSVGLocal = downloadSVGLocal;\n    /**\n     * Converts an SVG string into a PDF file and triggers its download. This\n     * function processes the SVG, applies necessary font adjustments, converts\n     * it to a PDF, and initiates the file download.\n     *\n     * @private\n     * @async\n     * @function downloadPDF\n     *\n     * @param {string} svg\n     * A string representation of the SVG markup to be converted into a PDF.\n     * @param {number} scale\n     * The scaling factor for the PDF output.\n     * @param {string} filename\n     * The name of the downloaded PDF file.\n     * @param {Highcharts.PdfFontOptions} [pdfFont]\n     * An optional object specifying URLs for different font variants (normal,\n     * bold, italic, bolditalic).\n     *\n     * @return {Promise<void>}\n     * A promise that resolves when the PDF has been successfully generated and\n     * downloaded.\n     *\n     * @requires modules/exporting\n     * @requires modules/offline-exporting\n     */\n    async function downloadPDF(svg, scale, filename, pdfFont) {\n        const svgNode = preparePDF(svg, pdfFont);\n        if (svgNode) {\n            // Loads all required fonts\n            await loadPdfFonts(svgNode, pdfFont);\n            // Transform SVG to PDF\n            const pdfData = await svgToPdf(svgNode, 0, scale);\n            // Download the PDF\n            OfflineExporting_downloadURL(pdfData, filename);\n        }\n    }\n    /**\n     * Loads and registers custom fonts for PDF export if non-ASCII characters\n     * are detected in the given SVG element. This function ensures that text\n     * content with special characters is properly rendered in the exported PDF.\n     *\n     * It fetches font files (if provided in `pdfFont`), converts them to\n     * base64, and registers them with jsPDF.\n     *\n     * @private\n     * @function loadPdfFonts\n     *\n     * @param {SVGElement} svgElement\n     * The generated SVG element containing the text content to be exported.\n     * @param {Highcharts.PdfFontOptions} [pdfFont]\n     * An optional object specifying URLs for different font variants (normal,\n     * bold, italic, bolditalic). If non-ASCII characters are not detected,\n     * fonts are not loaded.\n     *\n     * @requires modules/exporting\n     * @requires modules/offline-exporting\n     */\n    async function loadPdfFonts(svgElement, pdfFont) {\n        const hasNonASCII = (s) => (\n        // eslint-disable-next-line no-control-regex\n        /[^\\u0000-\\u007F\\u200B]+/.test(s));\n        // Register an event in order to add the font once jsPDF is initialized\n        const addFont = (variant, base64) => {\n            OfflineExporting_win.jspdf.jsPDF.API.events.push([\n                'initialized',\n                function () {\n                    this.addFileToVFS(variant, base64);\n                    this.addFont(variant, 'HighchartsFont', variant);\n                    if (!this.getFontList()?.HighchartsFont) {\n                        this.setFont('HighchartsFont');\n                    }\n                }\n            ]);\n        };\n        // If there are no non-ASCII characters in the SVG, do not use bother\n        // downloading the font files\n        if (pdfFont && !hasNonASCII(svgElement.textContent || '')) {\n            pdfFont = void 0;\n        }\n        // Add new font if the URL is declared, #6417\n        const variants = ['normal', 'italic', 'bold', 'bolditalic'];\n        // Shift the first element off the variants and add as a font.\n        // Then asynchronously trigger the next variant until variants are empty\n        let normalBase64;\n        for (const variant of variants) {\n            const url = pdfFont?.[variant];\n            if (url) {\n                try {\n                    const response = await OfflineExporting_win.fetch(url);\n                    if (!response.ok) {\n                        throw new Error(`Failed to fetch font: ${url}`);\n                    }\n                    const blob = await response.blob(), reader = new FileReader();\n                    const base64 = await new Promise((resolve, reject) => {\n                        reader.onloadend = () => {\n                            if (typeof reader.result === 'string') {\n                                resolve(reader.result.split(',')[1]);\n                            }\n                            else {\n                                reject(new Error('Failed to read font as base64'));\n                            }\n                        };\n                        reader.onerror = reject;\n                        reader.readAsDataURL(blob);\n                    });\n                    addFont(variant, base64);\n                    if (variant === 'normal') {\n                        normalBase64 = base64;\n                    }\n                }\n                catch (e) {\n                    // If fetch or reading fails, fallback to next variant\n                }\n            }\n            else {\n                // For other variants, fall back to normal text weight/style\n                if (normalBase64) {\n                    addFont(variant, normalBase64);\n                }\n            }\n        }\n    }\n    /**\n     * Prepares an SVG for PDF export by ensuring proper text styling and\n     * removing unnecessary elements. This function extracts an SVG element from\n     * a given SVG string, applies font styles inherited from parent elements,\n     * and removes text outlines and title elements to improve PDF rendering.\n     *\n     * @private\n     * @function preparePDF\n     *\n     * @param {string} svg\n     * A string representation of the SVG markup.\n     * @param {Highcharts.PdfFontOptions} [pdfFont]\n     * An optional object specifying URLs for different font variants (normal,\n     * bold, italic, bolditalic). If provided, the text elements are assigned a\n     * custom PDF font.\n     *\n     * @return {SVGSVGElement | null}\n     * Returns the parsed SVG element from the container or `null` if the SVG is\n     * not found.\n     *\n     * @requires modules/exporting\n     * @requires modules/offline-exporting\n     */\n    function preparePDF(svg, pdfFont) {\n        const dummySVGContainer = OfflineExporting_doc.createElement('div');\n        highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default().setElementHTML(dummySVGContainer, svg);\n        const textElements = dummySVGContainer.getElementsByTagName('text'), \n        // Copy style property to element from parents if it's not there.\n        // Searches up hierarchy until it finds prop, or hits the chart\n        // container\n        setStylePropertyFromParents = function (el, propName) {\n            let curParent = el;\n            while (curParent && curParent !== dummySVGContainer) {\n                if (curParent.style[propName]) {\n                    let value = curParent.style[propName];\n                    if (propName === 'fontSize' && /em$/.test(value)) {\n                        value = Math.round(parseFloat(value) * 16) + 'px';\n                    }\n                    el.style[propName] = value;\n                    break;\n                }\n                curParent = curParent.parentNode;\n            }\n        };\n        let titleElements, outlineElements;\n        // Workaround for the text styling. Making sure it does pick up\n        // settings for parent elements.\n        [].forEach.call(textElements, function (el) {\n            // Workaround for the text styling. making sure it does pick up\n            // the root element\n            ['fontFamily', 'fontSize']\n                .forEach((property) => {\n                setStylePropertyFromParents(el, property);\n            });\n            el.style.fontFamily = pdfFont?.normal ?\n                // Custom PDF font\n                'HighchartsFont' :\n                // Generic font (serif, sans-serif etc)\n                String(el.style.fontFamily &&\n                    el.style.fontFamily.split(' ').splice(-1));\n            // Workaround for plotband with width, removing title from text\n            // nodes\n            titleElements = el.getElementsByTagName('title');\n            [].forEach.call(titleElements, function (titleElement) {\n                el.removeChild(titleElement);\n            });\n            // Remove all .highcharts-text-outline elements, #17170\n            outlineElements =\n                el.getElementsByClassName('highcharts-text-outline');\n            while (outlineElements.length > 0) {\n                const outline = outlineElements[0];\n                if (outline.parentNode) {\n                    outline.parentNode.removeChild(outline);\n                }\n            }\n        });\n        return dummySVGContainer.querySelector('svg');\n    }\n    /**\n     * Transform from PDF to SVG.\n     *\n     * @async\n     * @private\n     * @function svgToPdf\n     *\n     * @param {Highcharts.SVGElement} svgElement\n     * The SVG element to convert.\n     * @param {number} margin\n     * The margin to apply.\n     * @param {number} scale\n     * The scale of the SVG.\n     *\n     * @requires modules/exporting\n     * @requires modules/offline-exporting\n     */\n    async function svgToPdf(svgElement, margin, scale) {\n        const width = (Number(svgElement.getAttribute('width')) + 2 * margin) *\n            scale, height = (Number(svgElement.getAttribute('height')) + 2 * margin) *\n            scale, pdfDoc = new OfflineExporting_win.jspdf.jsPDF(// eslint-disable-line new-cap\n        // Setting orientation to portrait if height exceeds width\n        height > width ? 'p' : 'l', 'pt', [width, height]);\n        // Workaround for #7090, hidden elements were drawn anyway. It comes\n        // down to https://github.com/yWorks/svg2pdf.js/issues/28. Check this\n        // later.\n        [].forEach.call(svgElement.querySelectorAll('*[visibility=\"hidden\"]'), function (node) {\n            node.parentNode.removeChild(node);\n        });\n        // Workaround for #13948, multiple stops in linear gradient set to 0\n        // causing error in Acrobat\n        const gradients = svgElement.querySelectorAll('linearGradient');\n        for (let index = 0; index < gradients.length; index++) {\n            const gradient = gradients[index];\n            const stops = gradient.querySelectorAll('stop');\n            let i = 0;\n            while (i < stops.length &&\n                stops[i].getAttribute('offset') === '0' &&\n                stops[i + 1].getAttribute('offset') === '0') {\n                stops[i].remove();\n                i++;\n            }\n        }\n        // Workaround for #15135, zero width spaces, which Highcharts uses\n        // to break lines, are not correctly rendered in PDF. Replace it\n        // with a regular space and offset by some pixels to compensate.\n        [].forEach.call(svgElement.querySelectorAll('tspan'), (tspan) => {\n            if (tspan.textContent === '\\u200B') {\n                tspan.textContent = ' ';\n                tspan.setAttribute('dx', -5);\n            }\n        });\n        // Transform from PDF to SVG\n        await pdfDoc.svg(svgElement, {\n            x: 0,\n            y: 0,\n            width,\n            height,\n            removeInvalid: true\n        });\n        // Return the output\n        return pdfDoc.output('datauristring');\n    }\n})(OfflineExporting || (OfflineExporting = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const OfflineExporting_OfflineExporting = (OfflineExporting);\n\n;// ./code/es-modules/masters/modules/offline-exporting.js\n\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n// Compatibility\nG.dataURLtoBlob = G.dataURLtoBlob || Extensions_DownloadURL.dataURLtoBlob;\nG.downloadSVGLocal = OfflineExporting_OfflineExporting.downloadSVGLocal;\nG.downloadURL = G.downloadURL || Extensions_DownloadURL.downloadURL;\n// Compose\nOfflineExporting_OfflineExporting.compose(G.Exporting);\n/* harmony default export */ const offline_exporting_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__660__", "__WEBPACK_EXTERNAL_MODULE__960__", "OfflineExporting", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "offline_exporting_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "<PERSON><PERSON><PERSON><PERSON>", "win", "document", "doc", "error", "domurl", "URL", "webkitURL", "dataURLtoBlob", "dataURL", "parts", "replace", "match", "length", "atob", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Uint8Array", "Blob", "createObjectURL", "binStr", "buf", "binary", "i", "charCodeAt", "Extensions_DownloadURL", "downloadURL", "filename", "nav", "navigator", "createElement", "String", "msSaveOrOpenBlob", "userAgent", "Error", "isOldEdgeBrowser", "test", "safariBlob", "indexOf", "download", "href", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "open", "location", "getScript", "scriptLocation", "Promise", "resolve", "reject", "head", "getElementsByTagName", "script", "type", "src", "onload", "onerror", "highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_", "highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default", "highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_", "highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default", "OfflineExporting_OfflineExportingDefaults", "exporting", "getOptions", "setOptions", "OfflineExporting_downloadURL", "OfflineExporting_getScript", "composed", "OfflineExporting_doc", "OfflineExporting_win", "addEvent", "extend", "pushUnique", "downloadPDF", "svg", "scale", "pdfFont", "svgNode", "preparePDF", "titleElements", "outlineElements", "dummy<PERSON><PERSON><PERSON><PERSON>", "setElementHTML", "textElements", "setStylePropertyFromParents", "el", "propName", "curParent", "style", "value", "Math", "round", "parseFloat", "parentNode", "for<PERSON>ach", "property", "fontFamily", "normal", "split", "splice", "titleElement", "getElementsByClassName", "outline", "querySelector", "loadPdfFonts", "svgToPdf", "svgElement", "normalBase64", "s", "addFont", "variant", "base64", "jspdf", "jsPDF", "API", "events", "push", "addFileToVFS", "getFontList", "HighchartsFont", "setFont", "textContent", "url", "response", "fetch", "ok", "blob", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "e", "margin", "width", "Number", "getAttribute", "height", "pdfDoc", "querySelectorAll", "node", "gradients", "index", "stops", "gradient", "remove", "tspan", "setAttribute", "x", "y", "removeInvalid", "output", "compose", "ExportingClass", "exportingOptions", "preventDefault", "libURL", "Exporting", "prepareImageOptions", "fallbackToServer", "exportChartLocal", "chartOptions", "exportChart", "menuItems", "buttons", "contextButton", "downloadSVGLocal", "options", "downloadSVG", "OfflineExporting_OfflineExporting", "G"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,GAAM,CAAEA,EAAK,WAAc,CAAC,KAAQ,EAC/F,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,uCAAwC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,GAAM,CAACA,EAAK,KAAQ,CAAE,GACnI,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,uCAAuC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,GAAM,CAAEA,EAAK,WAAc,CAAC,KAAQ,EAEvIA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,GAAM,CAAEA,EAAK,UAAa,CAAC,KAAQ,CACzG,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,IACvG,AAAC,CAAA,KACP,aACA,IA+TNC,EA/TUC,EAAuB,CAE/B,IACC,AAACT,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,AAACL,IAERA,EAAOD,OAAO,CAAGQ,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAad,OAAO,CAG5B,IAAIC,EAASU,CAAwB,CAACE,EAAS,CAAG,CAGjDb,QAAS,CAAC,CACX,EAMA,OAHAU,CAAmB,CAACG,EAAS,CAACZ,EAAQA,EAAOD,OAAO,CAAEY,GAG/CX,EAAOD,OAAO,AACtB,CAMCY,EAAoBI,CAAC,CAAG,AAACf,IACxB,IAAIgB,EAAShB,GAAUA,EAAOiB,UAAU,CACvC,IAAOjB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAW,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACnB,EAASqB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACvB,EAASsB,IAC5EE,OAAOC,cAAc,CAACzB,EAASsB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAoBrH,GAAM,CAAEE,SAAAA,CAAQ,CAAEC,IAAAA,CAAG,CAAEA,IAAK,CAAEC,SAAUC,CAAG,CAAE,CAAE,CAAIJ,IAE7C,CAAEK,MAAAA,CAAK,CAAE,CAAIL,IAMbM,EAASJ,EAAIK,GAAG,EAAIL,EAAIM,SAAS,EAAIN,EAkB3C,SAASO,EAAcC,CAAO,EAC1B,IAAMC,EAAQD,EACTE,OAAO,CAAC,eAAgB,IACxBC,KAAK,CAAC,yCACX,GAAIF,GACAA,EAAMG,MAAM,CAAG,GACdZ,EAAIa,IAAI,EACTb,EAAIc,WAAW,EACfd,EAAIe,UAAU,EACdf,EAAIgB,IAAI,EACPZ,EAAOa,eAAe,CAAG,CAE1B,IAAMC,EAASlB,EAAIa,IAAI,CAACJ,CAAK,CAAC,EAAE,EAAGU,EAAM,IAAInB,EAAIc,WAAW,CAACI,EAAON,MAAM,EAAGQ,EAAS,IAAIpB,EAAIe,UAAU,CAACI,GACzG,IAAK,IAAIE,EAAI,EAAGA,EAAID,EAAOR,MAAM,CAAE,EAAES,EACjCD,CAAM,CAACC,EAAE,CAAGH,EAAOI,UAAU,CAACD,GAElC,OAAOjB,EACFa,eAAe,CAAC,IAAIjB,EAAIgB,IAAI,CAAC,CAACI,EAAO,CAAE,CAAE,KAAQX,CAAK,CAAC,EAAE,AAAC,GACnE,CACJ,CAiG6B,IAAMc,EALf,CAChBhB,cAAAA,EACAiB,YAlFJ,SAAqBhB,CAAO,CAAEiB,CAAQ,EAClC,IAAMC,EAAM1B,EAAI2B,SAAS,CAAE7C,EAAIoB,EAAI0B,aAAa,CAAC,KAGjD,GAAI,AAAmB,UAAnB,OAAOpB,GACP,CAAEA,CAAAA,aAAmBqB,MAAK,GAC1BH,EAAII,gBAAgB,CAAE,YACtBJ,EAAII,gBAAgB,CAACtB,EAASiB,GAIlC,GADAjB,EAAU,GAAKA,EACXkB,EAAIK,SAAS,CAACnB,MAAM,CAAG,IACvB,MAAM,AAAIoB,MAAM,kBAEpB,IAEAC,EAAmB,YAAYC,IAAI,CAACR,EAAIK,SAAS,EAKjD,GAAII,CAAAA,AAHUpC,GACV,AAAmB,UAAnB,OAAOS,GACPA,AAA4C,IAA5CA,EAAQ4B,OAAO,CAAC,yBACFH,GAAoBzB,EAAQI,MAAM,CAAG,GAAM,GAErD,CADJJ,CAAAA,EAAUD,EAAcC,IAAY,EAAC,EAEjC,MAAM,AAAIwB,MAAM,6BAIxB,GAAI,AAAsB,KAAA,IAAflD,EAAEuD,QAAQ,CACjBvD,EAAEwD,IAAI,CAAG9B,EACT1B,EAAEuD,QAAQ,CAAGZ,EACbvB,EAAIqC,IAAI,CAACC,WAAW,CAAC1D,GACrBA,EAAE2D,KAAK,GACPvC,EAAIqC,IAAI,CAACG,WAAW,CAAC5D,QAIrB,GAAI,CACA,GAAI,CAACkB,EAAI2C,IAAI,CAACnC,EAAS,SACnB,MAAM,AAAIwB,MAAM,wBAExB,CACA,KAAM,CAEFhC,EAAI4C,QAAQ,CAACN,IAAI,CAAG9B,CACxB,CAER,EAoCIqC,UA1BJ,SAAmBC,CAAc,EAC7B,OAAO,IAAIC,QAAQ,CAACC,EAASC,KACzB,IAAMC,EAAOhD,EAAIiD,oBAAoB,CAAC,OAAO,CAAC,EAAE,CAAEC,EAASlD,EAAI0B,aAAa,CAAC,SAE7EwB,CAAAA,EAAOC,IAAI,CAAG,kBACdD,EAAOE,GAAG,CAAGR,EAEbM,EAAOG,MAAM,CAAG,KACZP,GACJ,EAEAI,EAAOI,OAAO,CAAG,KACbP,EAAO9C,EAAM,CAAC,qBAAqB,EAAE2C,EAAe,CAAC,EACzD,EAEAI,EAAKV,WAAW,CAACY,EACrB,EACJ,CAUA,EAIA,IAAIK,EAAuFnF,EAAoB,KAC3GoF,EAA2GpF,EAAoBI,CAAC,CAAC+E,GAEjIE,EAA+FrF,EAAoB,KACnHsF,EAAmHtF,EAAoBI,CAAC,CAACiF,GA8BhH,IAAME,EAHF,CAC7BC,UAPc,CAAC,CAQnB,EAmBM,CAAEC,WAAAA,CAAU,CAAEC,WAAAA,CAAU,CAAE,CAAIlE,IAE9B,CAAE0B,YAAayC,CAA4B,CAAEpB,UAAWqB,CAA0B,CAAE,CAAG3C,EAEvF,CAAE4C,SAAAA,CAAQ,CAAEjE,IAAKkE,CAAoB,CAAEpE,IAAKqE,CAAoB,CAAE,CAAIvE,IAGtE,CAAEwE,SAAAA,CAAQ,CAAEC,OAAAA,CAAM,CAAEC,WAAAA,CAAU,CAAE,CAAI1E,KAO1C,AAAC,SAAU3B,CAAgB,EA2HvB,eAAesG,EAAYC,CAAG,CAAEC,CAAK,CAAElD,CAAQ,CAAEmD,CAAO,EACpD,IAAMC,EAAUC,AAuHpB,SAAoBJ,CAAG,CAAEE,CAAO,EAC5B,IAoBIG,EAAeC,EApBbC,EAAoBb,EAAqBxC,aAAa,CAAC,OAC7D8B,IAA8FwB,cAAc,CAACD,EAAmBP,GAChI,IAAMS,EAAeF,EAAkB9B,oBAAoB,CAAC,QAI5DiC,EAA8B,SAAUC,CAAE,CAAEC,CAAQ,EAChD,IAAIC,EAAYF,EAChB,KAAOE,GAAaA,IAAcN,GAAmB,CACjD,GAAIM,EAAUC,KAAK,CAACF,EAAS,CAAE,CAC3B,IAAIG,EAAQF,EAAUC,KAAK,CAACF,EAAS,AACjCA,AAAa,CAAA,aAAbA,GAA2B,MAAMpD,IAAI,CAACuD,IACtCA,CAAAA,EAAQC,KAAKC,KAAK,CAACC,AAAoB,GAApBA,WAAWH,IAAe,IAAG,EAEpDJ,EAAGG,KAAK,CAACF,EAAS,CAAGG,EACrB,KACJ,CACAF,EAAYA,EAAUM,UAAU,AACpC,CACJ,EAiCA,MA7BA,EAAE,CAACC,OAAO,CAACpG,IAAI,CAACyF,EAAc,SAAUE,CAAE,EAsBtC,IAnBA,CAAC,aAAc,WAAW,CACrBS,OAAO,CAAC,AAACC,IACVX,EAA4BC,EAAIU,EACpC,GACAV,EAAGG,KAAK,CAACQ,UAAU,CAAGpB,GAASqB,OAE3B,iBAEApE,OAAOwD,EAAGG,KAAK,CAACQ,UAAU,EACtBX,EAAGG,KAAK,CAACQ,UAAU,CAACE,KAAK,CAAC,KAAKC,MAAM,CAAC,KAG9CpB,EAAgBM,EAAGlC,oBAAoB,CAAC,SACxC,EAAE,CAAC2C,OAAO,CAACpG,IAAI,CAACqF,EAAe,SAAUqB,CAAY,EACjDf,EAAG3C,WAAW,CAAC0D,EACnB,GAEApB,EACIK,EAAGgB,sBAAsB,CAAC,2BACvBrB,EAAgBpE,MAAM,CAAG,GAAG,CAC/B,IAAM0F,EAAUtB,CAAe,CAAC,EAAE,AAC9BsB,CAAAA,EAAQT,UAAU,EAClBS,EAAQT,UAAU,CAACnD,WAAW,CAAC4D,EAEvC,CACJ,GACOrB,EAAkBsB,aAAa,CAAC,MAC3C,EA7K+B7B,EAAKE,GAC5BC,IAEA,MAAM2B,EAAa3B,EAASD,GAI5BX,EAFgB,MAAMwC,EAAS5B,EAAS,EAAGF,GAELlD,GAE9C,CAsBA,eAAe+E,EAAaE,CAAU,CAAE9B,CAAO,EAK3C,IAqBI+B,EAzBiBC,EAIfC,EAAU,CAACC,EAASC,KACtB1C,EAAqB2C,KAAK,CAACC,KAAK,CAACC,GAAG,CAACC,MAAM,CAACC,IAAI,CAAC,CAC7C,cACA,WACI,IAAI,CAACC,YAAY,CAACP,EAASC,GAC3B,IAAI,CAACF,OAAO,CAACC,EAAS,iBAAkBA,GACpC,AAAC,IAAI,CAACQ,WAAW,IAAIC,gBACrB,IAAI,CAACC,OAAO,CAAC,iBAErB,EACH,CACL,EAWA,IAAK,IAAMV,KARPlC,IAlBiBgC,EAkBOF,EAAWe,WAAW,EAAI,IAhBtD,0BAA0BvF,IAAI,CAAC0E,KAiB3BhC,CAAAA,EAAU,KAAK,CAAA,EAGF,CAAC,SAAU,SAAU,OAAQ,aAAa,EAI3B,CAC5B,IAAM8C,EAAM9C,GAAS,CAACkC,EAAQ,CAC9B,GAAIY,EACA,GAAI,CACA,IAAMC,EAAW,MAAMtD,EAAqBuD,KAAK,CAACF,GAClD,GAAI,CAACC,EAASE,EAAE,CACZ,MAAM,AAAI7F,MAAM,CAAC,sBAAsB,EAAE0F,EAAI,CAAC,EAElD,IAAMI,EAAO,MAAMH,EAASG,IAAI,GAAIC,EAAS,IAAIC,WAC3CjB,EAAS,MAAM,IAAIhE,QAAQ,CAACC,EAASC,KACvC8E,EAAOE,SAAS,CAAG,KACX,AAAyB,UAAzB,OAAOF,EAAOG,MAAM,CACpBlF,EAAQ+E,EAAOG,MAAM,CAAChC,KAAK,CAAC,IAAI,CAAC,EAAE,EAGnCjD,EAAO,AAAIjB,MAAM,iCAEzB,EACA+F,EAAOvE,OAAO,CAAGP,EACjB8E,EAAOI,aAAa,CAACL,EACzB,GACAjB,EAAQC,EAASC,GACbD,AAAY,WAAZA,GACAH,CAAAA,EAAeI,CAAK,CAE5B,CACA,MAAOqB,EAAG,CAEV,MAIIzB,GACAE,EAAQC,EAASH,EAG7B,CACJ,CAgGA,eAAeF,EAASC,CAAU,CAAE2B,CAAM,CAAE1D,CAAK,EAC7C,IAAM2D,EAAQ,AAACC,CAAAA,OAAO7B,EAAW8B,YAAY,CAAC,UAAY,EAAIH,CAAK,EAC/D1D,EAAO8D,EAAS,AAACF,CAAAA,OAAO7B,EAAW8B,YAAY,CAAC,WAAa,EAAIH,CAAK,EACtE1D,EAAO+D,EAAS,IAAIrE,EAAqB2C,KAAK,CAACC,KAAK,CAExDwB,EAASH,EAAQ,IAAM,IAAK,KAAM,CAACA,EAAOG,EAAO,EAIjD,EAAE,CAAC3C,OAAO,CAACpG,IAAI,CAACgH,EAAWiC,gBAAgB,CAAC,0BAA2B,SAAUC,CAAI,EACjFA,EAAK/C,UAAU,CAACnD,WAAW,CAACkG,EAChC,GAGA,IAAMC,EAAYnC,EAAWiC,gBAAgB,CAAC,kBAC9C,IAAK,IAAIG,EAAQ,EAAGA,EAAQD,EAAUjI,MAAM,CAAEkI,IAAS,CAEnD,IAAMC,EAAQC,AADGH,CAAS,CAACC,EAAM,CACVH,gBAAgB,CAAC,QACpCtH,EAAI,EACR,KAAOA,EAAI0H,EAAMnI,MAAM,EACnBmI,AAAoC,MAApCA,CAAK,CAAC1H,EAAE,CAACmH,YAAY,CAAC,WACtBO,AAAwC,MAAxCA,CAAK,CAAC1H,EAAI,EAAE,CAACmH,YAAY,CAAC,WAC1BO,CAAK,CAAC1H,EAAE,CAAC4H,MAAM,GACf5H,GAER,CAmBA,MAfA,EAAE,CAACyE,OAAO,CAACpG,IAAI,CAACgH,EAAWiC,gBAAgB,CAAC,SAAU,AAACO,IACzB,MAAtBA,EAAMzB,WAAW,GACjByB,EAAMzB,WAAW,CAAG,IACpByB,EAAMC,YAAY,CAAC,KAAM,IAEjC,GAEA,MAAMT,EAAOhE,GAAG,CAACgC,EAAY,CACzB0C,EAAG,EACHC,EAAG,EACHf,MAAAA,EACAG,OAAAA,EACAa,cAAe,CAAA,CACnB,GAEOZ,EAAOa,MAAM,CAAC,gBACzB,CApSApL,EAAiBqL,OAAO,CAlDxB,SAAiBC,CAAc,EAkC3B,GAhCAnF,EAASmF,EAAgB,cAAe,eAAgBrB,CAAC,EACrD,GAAM,CAAE1D,IAAAA,CAAG,CAAEgF,iBAAAA,CAAgB,CAAE5F,UAAAA,CAAS,CAAE6F,eAAAA,CAAc,CAAE,CAAGvB,EAE7D,GAAIsB,GAAkBrG,OAAS,kBAAmB,CAE9CsG,MAEA,GAAI,CAEA,GAAM,CAAEtG,KAAAA,CAAI,CAAE5B,SAAAA,CAAQ,CAAEkD,MAAAA,CAAK,CAAEiF,OAAAA,CAAM,CAAE,CAAG9J,IAA8E+J,SAAS,CAACC,mBAAmB,CAACJ,EAEzI,CAAA,oBAATrG,IAIKgB,EAAqB2C,KAAK,EAAEC,QAE7B,MAAM/C,EAA2B,CAAC,EAAE0F,EAAO,QAAQ,CAAC,EAEpD,MAAM1F,EAA2B,CAAC,EAAE0F,EAAO,UAAU,CAAC,GAG1D,MAAMnF,EAAYC,EAAKC,EAAOlD,EAAUiI,GAAkB9E,SAElE,CACA,MAAOzE,EAAO,CAEV,MAAM2D,GAAWiG,iBAAiBL,EAAkBvJ,EACxD,CACJ,CACJ,GAEI,CAACqE,EAAWL,EAAU,oBACtB,OAGJI,EAAO,AAACX,IAAuGpE,SAAS,CAAE,CACtHwK,iBAAkB,eAAgBN,CAAgB,CAAEO,CAAY,EAC5D,MAAM,IAAI,CAACnG,SAAS,EAAEoG,YAAYR,EAAkBO,EAExD,CACJ,GAEAjG,EAAWH,GAEX,IAAMsG,EAAYpG,IAAaD,SAAS,EAAEsG,SAASC,eAAeF,SAClEA,CAAAA,GAAaA,EAAU/C,IAAI,CAAC,cAChC,EA6BAjJ,EAAiBmM,gBAAgB,CAHjC,eAAgC5F,CAAG,CAAE6F,CAAO,EACxC,MAAMzK,IAA8E+J,SAAS,CAACrK,SAAS,CAACgL,WAAW,CAAC9K,IAAI,CAAC,KAAK,EAAGgF,EAAK6F,EAC1I,CA0QJ,EAAGpM,GAAqBA,CAAAA,EAAmB,CAAC,CAAA,GAMf,IAAMsM,EAAqCtM,EAQlEuM,EAAK5K,GAEX4K,CAAAA,EAAEnK,aAAa,CAAGmK,EAAEnK,aAAa,EAAIgB,EAAuBhB,aAAa,CACzEmK,EAAEJ,gBAAgB,CAAGG,EAAkCH,gBAAgB,CACvEI,EAAElJ,WAAW,CAAGkJ,EAAElJ,WAAW,EAAID,EAAuBC,WAAW,CAEnEiJ,EAAkCjB,OAAO,CAACkB,EAAEb,SAAS,EACxB,IAAMjK,EAA0BE,IAGnD,OADYH,EAAoB,OAAU,AAE3C,CAAA"}