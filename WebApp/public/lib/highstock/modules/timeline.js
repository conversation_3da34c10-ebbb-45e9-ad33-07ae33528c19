!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/timeline
 * @requires highcharts
 *
 * Timeline series
 *
 * (c) 2010-2025 Highsoft AS
 * Author: <PERSON>
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.SeriesRegistry,t._Highcharts.Point):"function"==typeof define&&define.amd?define("highcharts/modules/timeline",["highcharts/highcharts"],function(t){return e(t,t.SeriesRegistry,t.Point)}):"object"==typeof exports?exports["highcharts/modules/timeline"]=e(t._Highcharts,t._Highcharts.SeriesRegistry,t._Highcharts.Point):t.Highcharts=e(t.Highcharts,t.Highcharts.SeriesRegistry,t.Highcharts.Point)}("undefined"==typeof window?this:window,(t,e,i)=>(()=>{"use strict";var s={260:t=>{t.exports=i},512:t=>{t.exports=e},944:e=>{e.exports=t}},n={};function o(t){var e=n[t];if(void 0!==e)return e.exports;var i=n[t]={exports:{}};return s[t](i,i.exports,o),i.exports}o.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return o.d(e,{a:e}),e},o.d=(t,e)=>{for(var i in e)o.o(e,i)&&!o.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},o.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var r={};o.d(r,{default:()=>H});var a=o(944),l=o.n(a),h=o(512),p=o.n(h),d=o(260),c=o.n(d);let{line:{prototype:{pointClass:u}},pie:{prototype:{pointClass:y}}}=p().seriesTypes,{defined:g,isNumber:x,merge:f,objectEach:b,pick:m}=l(),w=class extends u{alignConnector(){let t=this.series,e=this.dataLabel,i=e.connector,s=e.options||{},n=s.connectorWidth||0,o=this.series.chart,r=i.getBBox(),a={x:r.x+(e.translateX||0),y:r.y+(e.translateY||0)};o.inverted?a.y-=n/2:a.x+=n/2,i[o.isInsidePlot(a.x,a.y)?"animate":"attr"]({d:this.getConnectorPath()}),i.addClass("highcharts-color-"+this.colorIndex),t.chart.styledMode||i.attr({stroke:s.connectorColor||this.color,"stroke-width":s.connectorWidth,opacity:e[g(e.newOpacity)?"newOpacity":"opacity"]})}drawConnector(){let{dataLabel:t,series:e}=this;t&&(t.connector||(t.connector=e.chart.renderer.path(this.getConnectorPath()).attr({zIndex:-1}).add(t)),this.series.chart.isInsidePlot(t.x||0,t.y||0)&&this.alignConnector())}getConnectorPath(){let{plotX:t=0,plotY:e=0,series:i,dataLabel:s}=this,n=i.chart,o=i.xAxis.len,r=n.inverted,a=r?"x2":"y2";if(s){let l=s.targetPosition,h=(s.alignAttr||s)[a[0]]<i.yAxis.len/2,p={x1:t,y1:e,x2:t,y2:x(l.y)?l.y:s.y};return r&&(p={x1:e,y1:o-t,x2:l.x||s.x,y2:o-t}),h&&(p[a]+=s[r?"width":"height"]||0),b(p,(t,e)=>{p[e]-=(s.alignAttr||s)[e[0]]}),n.renderer.crispLine([["M",p.x1,p.y1],["L",p.x2,p.y2]],s.options?.connectorWidth||0)}return[]}constructor(t,e){super(t,e),this.name??(this.name=(e&&null!==e.y||!t.options.nullInteraction)&&"Event"||"Null"),this.y=1}isValid(){return null!==this.options.y||this.series.options.nullInteraction||!0}setState(){let t=super.setState;(!this.isNull||this.series.options.nullInteraction)&&t.apply(this,arguments)}setVisible(t,e){let i=this.series;e=m(e,i.options.ignoreHiddenPoint),y.prototype.setVisible.call(this,t,!1),i.processData(),e&&i.chart.redraw()}applyOptions(t,e){let i=this.isNull||null===t||null===t.y,s=this.series;e||t?.x||(x(this.x)?e=this.x:x(s?.xIncrement)&&(e=s.xIncrement||0,s.autoIncrement())),t=c().prototype.optionsToObject.call(this,t??(s.options.nullInteraction&&{y:0}||null));let n=super.applyOptions(t,e);return this.userDLOptions=f(this.userDLOptions,t.dataLabels),n.isNull=i,n}},{column:v,line:P}=p().seriesTypes,{addEvent:L,arrayMax:O,arrayMin:A,defined:C,extend:I,merge:k,pick:M}=l();class T extends P{alignDataLabel(t,e,i,s){let n,o,r,a=this.chart.inverted,l=this.visibilityMap.filter(t=>!!t),h=this.visiblePointsCount||0,p=l.indexOf(t),d=this.options.dataLabels,c=t.userDLOptions||{},u=d.alternate?p&&p!==h-1?2:1.5:1,y=Math.floor(this.xAxis.len/h),g=e.padding;t.visible&&(n=Math.abs(c.x||t.options.dataLabels.x),a?(o=(n-g)*2-(t.itemHeight||0)/2,r={width:M(d.style?.width,`${.4*this.yAxis.len}px`),textOverflow:(e.width||0)/o*(e.height||0)/2>y*u?"ellipsis":"none"}):r={width:(c.width||d.width||y*u-2*g)+"px"},e.css(r),this.chart.styledMode||e.shadow(d.shadow)),super.alignDataLabel.apply(this,arguments)}bindAxes(){super.bindAxes(),this.xAxis.userOptions.type||(this.xAxis.categories=this.xAxis.hasNames=!0)}distributeDL(){let t=this.options.dataLabels,e=this.chart.inverted,i=1;if(t){let s=M(t.distance,e?20:100);for(let n of this.points){let o={[e?"x":"y"]:t.alternate&&i%2?-s:s};e&&(o.align=t.alternate&&i%2?"right":"left"),n.options.dataLabels=k(o,n.userDLOptions),i++}}}generatePoints(){super.generatePoints();let t=this.points,e=t.length,i=this.getColumn("x");for(let s=0;s<e;++s){let e=i[s];t[s].applyOptions({x:e},e)}}getVisibilityMap(){let t=this.options.nullInteraction;return((this.data.length?this.data:this.options.data)||[]).map(e=>!!e&&!1!==e.visible&&(!e.isNull||!!t)&&e)}getXExtremes(t){let e=this,i=t.filter((t,i)=>e.points[i].isValid()&&e.points[i].visible);return{min:A(i),max:O(i)}}init(){let t=this;super.init.apply(t,arguments),t.eventsToUnbind.push(L(t,"afterTranslate",function(){let e,i=Number.MAX_VALUE;for(let s of t.points)s.isInside=s.isInside&&s.visible,s.visible&&(!s.isNull||t.options.nullInteraction)&&(C(e)&&(i=Math.min(i,Math.abs(s.plotX-e))),e=s.plotX);t.closestPointRangePx=i})),t.eventsToUnbind.push(L(t,"drawDataLabels",function(){t.distributeDL()})),t.eventsToUnbind.push(L(t,"afterDrawDataLabels",function(){let e;for(let i of t.points)(e=i.dataLabel)&&(e.animate=function(t){return this.targetPosition&&(this.targetPosition=t),this.renderer.Element.prototype.animate.apply(this,arguments)},e.targetPosition||(e.targetPosition={}),i.drawConnector())})),t.eventsToUnbind.push(L(t.chart,"afterHideOverlappingLabel",function(){for(let e of t.points)e.dataLabel&&e.dataLabel.connector&&e.dataLabel.oldOpacity!==e.dataLabel.newOpacity&&e.alignConnector()}))}markerAttribs(t,e){let i=this.options.marker,s=t.marker||{},n=s.symbol||i.symbol,o=M(s.width,i.width,this.closestPointRangePx),r=M(s.height,i.height),a,l=0;if(this.xAxis.dateTime)return super.markerAttribs(t,e);e&&(a=i.states[e]||{},l=M((s.states&&s.states[e]||{}).radius,a.radius,l+(a.radiusPlus||0))),t.hasImage=n&&0===n.indexOf("url");let h={x:Math.floor(t.plotX)-o/2-l/2,y:t.plotY-r/2-l/2,width:o+l,height:r+l};return this.chart.inverted?{y:h.x&&h.width&&this.xAxis.len-h.x-h.width,x:h.y&&h.y,width:h.height,height:h.width}:h}}T.defaultOptions=k(P.defaultOptions,{colorByPoint:!0,stickyTracking:!1,ignoreHiddenPoint:!0,legendType:"point",lineWidth:4,tooltip:{headerFormat:'<span style="color:{point.color}">●</span> <span style="font-size: 0.8em"> {point.key}</span><br/>',pointFormat:"{point.description}"},states:{hover:{lineWidthPlus:0}},dataLabels:{enabled:!0,allowOverlap:!0,alternate:!0,backgroundColor:"#ffffff",borderWidth:1,borderColor:"#999999",borderRadius:3,color:"#333333",connectorWidth:1,distance:void 0,formatter:function(){let t;return(this.series.chart.styledMode?'<span class="highcharts-color-'+this.point.colorIndex+'">● </span>':'<span style="color:'+this.point.color+'">● </span>')+('<span class="highcharts-strong">'+(this.key||"")+"</span><br/>")+(this.label||"")},style:{textOutline:"none",fontWeight:"normal",fontSize:"0.8em",textAlign:"left"},shadow:!1,verticalAlign:"middle"},marker:{enabledThreshold:0,symbol:"square",radius:6,lineWidth:2,height:15},showInLegend:!1,colorKey:"x",legendSymbol:"rectangle"}),L(T,"afterProcessData",function(){let t=this.getColumn("x"),e=0;for(let t of(this.visibilityMap=this.getVisibilityMap(),this.visibilityMap))t&&e++;this.visiblePointsCount=e,this.dataTable.setColumn("y",Array(t.length).fill(1))}),I(T.prototype,{drawTracker:v.prototype.drawTracker,pointClass:w,trackerGroups:["markerGroup","dataLabelsGroup"]}),p().registerSeriesType("timeline",T);let H=l();return r.default})());