!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/broken-axis
 * @requires highcharts
 *
 * (c) 2009-2025 Torstein Honsi
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.Axis,t._Highcharts.Point,t._Highcharts.Series,t._Highcharts.Color,t._Highcharts.SeriesRegistry,t._Highcharts.RendererRegistry,t._Highcharts.SVGRenderer,t._Highcharts.SVGElement,t._Highcharts.Templating,t._Highcharts.Chart,t._Highcharts.Series.types.column,t._Highcharts.StackItem):"function"==typeof define&&define.amd?define("highcharts/modules/stock",["highcharts/highcharts"],function(t){return e(t,t.Axis,t.Point,t.Series,t.Color,t.<PERSON>,t.<PERSON>,t.<PERSON>,t.<PERSON>,t.Templating,t.Chart,t.Series,["types"],["column"],t.StackItem)}):"object"==typeof exports?exports["highcharts/modules/stock"]=e(t._Highcharts,t._Highcharts.Axis,t._Highcharts.Point,t._Highcharts.Series,t._Highcharts.Color,t._Highcharts.SeriesRegistry,t._Highcharts.RendererRegistry,t._Highcharts.SVGRenderer,t._Highcharts.SVGElement,t._Highcharts.Templating,t._Highcharts.Chart,t._Highcharts.Series.types.column,t._Highcharts.StackItem):t.Highcharts=e(t.Highcharts,t.Highcharts.Axis,t.Highcharts.Point,t.Highcharts.Series,t.Highcharts.Color,t.Highcharts.SeriesRegistry,t.Highcharts.RendererRegistry,t.Highcharts.SVGRenderer,t.Highcharts.SVGElement,t.Highcharts.Templating,t.Highcharts.Chart,t.Highcharts.Series.types.column,t.Highcharts.StackItem)}("undefined"==typeof window?this:window,(t,e,i,s,o,r,n,a,l,h,d,p,c)=>(()=>{"use strict";let u,g,m,f;var x,b,v,y,M,A,k,w,S={28:t=>{t.exports=l},184:t=>{t.exports=c},260:t=>{t.exports=i},448:t=>{t.exports=p},512:t=>{t.exports=r},532:t=>{t.exports=e},540:t=>{t.exports=a},608:t=>{t.exports=n},620:t=>{t.exports=o},820:t=>{t.exports=s},944:e=>{e.exports=t},960:t=>{t.exports=d},984:t=>{t.exports=h}},T={};function E(t){var e=T[t];if(void 0!==e)return e.exports;var i=T[t]={exports:{}};return S[t](i,i.exports,E),i.exports}E.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return E.d(e,{a:e}),e},E.d=(t,e)=>{for(var i in e)E.o(e,i)&&!E.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},E.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var O={};E.d(O,{default:()=>oL});var C=E(944),B=E.n(C),D=E(532),P=E.n(D),R=E(260),G=E.n(R),I=E(820),z=E.n(I);let{tooltipFormatter:L}=G().prototype,{addEvent:H,arrayMax:W,arrayMin:X,correctFloat:Y,defined:N,isArray:F,isNumber:U,isString:_,pick:V}=B();!function(t){function e(t,e,i){!this.isXAxis&&(this.series.forEach(function(i){"compare"===t&&"boolean"!=typeof e?i.setCompare(e,!1):"cumulative"!==t||_(e)||i.setCumulative(e,!1)}),V(i,!0)&&this.chart.redraw())}function i(t){let e=this,{numberFormatter:i}=e.series.chart,s=function(s){t=t.replace("{point."+s+"}",(e[s]>0&&"change"===s?"+":"")+i(e[s],V(e.series.tooltipOptions.changeDecimals,2)))};return N(e.change)&&s("change"),N(e.cumulativeSum)&&s("cumulativeSum"),L.apply(this,[t])}function s(){let t,e=this.options.compare;("percent"===e||"value"===e||this.options.cumulative)&&(t=new d(this),"percent"===e||"value"===e?t.initCompare(e):t.initCumulative()),this.dataModify=t}function o(t){let e=t.dataExtremes,i=e.activeYData;if(this.dataModify&&e){let t;this.options.compare?t=[this.dataModify.modifyValue(e.dataMin),this.dataModify.modifyValue(e.dataMax)]:this.options.cumulative&&F(i)&&i.length>=2&&(t=d.getCumulativeExtremes(i)),t&&(e.dataMin=X(t),e.dataMax=W(t))}}function r(t,e){this.options.compare=this.userOptions.compare=t,this.update({},V(e,!0)),this.dataModify&&("value"===t||"percent"===t)?this.dataModify.initCompare(t):this.points.forEach(t=>{delete t.change})}function n(){let t=this.getColumn(this.pointArrayMap&&(this.options.pointValKey||this.pointValKey)||"y",!0);if(this.xAxis&&t.length&&this.dataModify){let e=this.getColumn("x",!0),i=this.dataTable.rowCount,s=+(!0!==this.options.compareStart);for(let o=0;o<i-s;o++){let i=t[o];if(U(i)&&0!==i&&e[o+s]>=(this.xAxis.min||0)){this.dataModify.compareValue=i;break}}}}function a(t,e){this.setModifier("compare",t,e)}function l(t,e){t=V(t,!1),this.options.cumulative=this.userOptions.cumulative=t,this.update({},V(e,!0)),this.dataModify?this.dataModify.initCumulative():this.points.forEach(t=>{delete t.cumulativeSum})}function h(t,e){this.setModifier("cumulative",t,e)}t.compose=function(t,d,p){let c=d.prototype,u=p.prototype,g=t.prototype;return g.setCompare||(g.setCompare=r,g.setCumulative=l,H(t,"afterInit",s),H(t,"afterGetExtremes",o),H(t,"afterProcessData",n)),c.setCompare||(c.setCompare=a,c.setModifier=e,c.setCumulative=h,u.tooltipFormatter=i),t};class d{constructor(t){this.series=t}modifyValue(){return 0}static getCumulativeExtremes(t){let e=1/0,i=-1/0;return t.reduce((t,s)=>{let o=t+s;return e=Math.min(e,o,t),i=Math.max(i,o,t),o}),[e,i]}initCompare(t){this.modifyValue=function(e,i){null===e&&(e=0);let s=this.compareValue;if(void 0!==e&&void 0!==s){if("value"===t?e-=s:e=e/s*100-100*(100!==this.series.options.compareBase),void 0!==i){let t=this.series.points[i];t&&(t.change=e)}return e}return 0}}initCumulative(){this.modifyValue=function(t,e){if(null===t&&(t=0),void 0!==t&&void 0!==e){let i=e>0?this.series.points[e-1]:null;i&&i.cumulativeSum&&(t=Y(i.cumulativeSum+t));let s=this.series.points[e],o=s.series.options.cumulativeStart,r=s.x<=this.series.xAxis.max&&s.x>=this.series.xAxis.min;return s&&(!o||r?s.cumulativeSum=t:s.cumulativeSum=void 0),t}return 0}}}t.Additions=d}(x||(x={}));let Z=x,{isTouchDevice:j}=B(),{addEvent:q,merge:K,pick:$}=B(),J=[];function Q(){this.navigator&&this.navigator.setBaseSeries(null,!1)}function tt(){let t,e,i,s=this.legend,o=this.navigator;if(o){t=s&&s.options,e=o.xAxis,i=o.yAxis;let{scrollbarHeight:r,scrollButtonSize:n}=o;this.inverted?(o.left=o.opposite?this.chartWidth-r-o.height:this.spacing[3]+r,o.top=this.plotTop+n):(o.left=$(e.left,this.plotLeft+n),o.top=o.navigatorOptions.top||this.chartHeight-o.height-r-(this.scrollbar?.options.margin||0)-this.spacing[2]-(this.rangeSelector&&this.extraBottomMargin?this.rangeSelector.getHeight():0)-(t&&"bottom"===t.verticalAlign&&"proximate"!==t.layout&&t.enabled&&!t.floating?s.legendHeight+$(t.margin,10):0)-(this.titleOffset?this.titleOffset[2]:0)),e&&i&&(this.inverted?e.options.left=i.options.left=o.left:e.options.top=i.options.top=o.top,e.setAxisSize(),i.setAxisSize())}}function te(t){!this.navigator&&!this.scroller&&(this.options.navigator.enabled||this.options.scrollbar.enabled)&&(this.scroller=this.navigator=new u(this),$(t.redraw,!0)&&this.redraw(t.animation))}function ti(){let t=this.options;(t.navigator.enabled||t.scrollbar.enabled)&&(this.scroller=this.navigator=new u(this))}function ts(){let t=this.options,e=t.navigator,i=t.rangeSelector;if((e&&e.enabled||i&&i.enabled)&&(!j&&"x"===this.zooming.type||j&&"x"===this.zooming.pinchType))return!1}function to(t){let e=t.navigator;if(e&&t.xAxis[0]){let i=t.xAxis[0].getExtremes();e.render(i.min,i.max)}}function tr(t){let e=t.options.navigator||{},i=t.options.scrollbar||{};!this.navigator&&!this.scroller&&(e.enabled||i.enabled)&&(K(!0,this.options.navigator,e),K(!0,this.options.scrollbar,i),delete t.options.navigator,delete t.options.scrollbar)}let tn={compose:function(t,e){if(B().pushUnique(J,t)){let i=t.prototype;u=e,i.callbacks.push(to),q(t,"afterAddSeries",Q),q(t,"afterSetChartSize",tt),q(t,"afterUpdate",te),q(t,"beforeRender",ti),q(t,"beforeShowResetZoom",ts),q(t,"update",tr)}}},{isTouchDevice:ta}=B(),{addEvent:tl,correctFloat:th,defined:td,isNumber:tp,pick:tc}=B();function tu(){this.navigatorAxis||(this.navigatorAxis=new tm(this))}function tg(t){let e,i=this.chart,s=i.options,o=s.navigator,r=this.navigatorAxis,n=i.zooming.pinchType,a=s.rangeSelector,l=i.zooming.type;if(this.isXAxis&&(o?.enabled||a?.enabled)){if("y"===l&&"zoom"===t.trigger)e=!1;else if(("zoom"===t.trigger&&"xy"===l||ta&&"xy"===n)&&this.options.range){let e=r.previousZoom;td(t.min)?r.previousZoom=[this.min,this.max]:e&&(t.min=e[0],t.max=e[1],r.previousZoom=void 0)}}void 0!==e&&t.preventDefault()}class tm{static compose(t){t.keepProps.includes("navigatorAxis")||(t.keepProps.push("navigatorAxis"),tl(t,"init",tu),tl(t,"setExtremes",tg))}constructor(t){this.axis=t}destroy(){this.axis=void 0}toFixedRange(t,e,i,s){let o=this.axis,r=(o.pointRange||0)/2,n=tc(i,o.translate(t,!0,!o.horiz)),a=tc(s,o.translate(e,!0,!o.horiz));return td(i)||(n=th(n+r)),td(s)||(a=th(a-r)),tp(n)&&tp(a)||(n=a=void 0),{min:n,max:a}}}var tf=E(620),tx=E.n(tf),tb=E(512),tv=E.n(tb);let{parse:ty}=tx(),{seriesTypes:tM}=tv(),tA={height:40,margin:22,maskInside:!0,handles:{width:7,borderRadius:0,height:15,symbols:["navigator-handle","navigator-handle"],enabled:!0,lineWidth:1,backgroundColor:"#f2f2f2",borderColor:"#999999"},maskFill:ty("#667aff").setOpacity(.3).get(),outlineColor:"#999999",outlineWidth:1,series:{type:void 0===tM.areaspline?"line":"areaspline",fillOpacity:.05,lineWidth:1,compare:null,sonification:{enabled:!1},dataGrouping:{approximation:"average",enabled:!0,groupPixelWidth:2,firstAnchor:"firstPoint",anchor:"middle",lastAnchor:"lastPoint",units:[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1,2,3,4]],["week",[1,2,3]],["month",[1,3,6]],["year",null]]},dataLabels:{enabled:!1,zIndex:2},id:"highcharts-navigator-series",className:"highcharts-navigator-series",lineColor:null,marker:{enabled:!1},threshold:null},xAxis:{className:"highcharts-navigator-xaxis",tickLength:0,lineWidth:0,gridLineColor:"#e6e6e6",id:"navigator-x-axis",gridLineWidth:1,tickPixelInterval:200,labels:{align:"left",style:{color:"#000000",fontSize:"0.7em",opacity:.6,textOutline:"2px contrast"},x:3,y:-4},crosshair:!1},yAxis:{className:"highcharts-navigator-yaxis",gridLineWidth:0,startOnTick:!1,endOnTick:!1,minPadding:.1,id:"navigator-y-axis",maxPadding:.1,labels:{enabled:!1},crosshair:!1,title:{text:void 0},tickLength:0,tickWidth:0}},{defined:tk,isNumber:tw,pick:tS}=B(),tT={rect:function(t,e,i,s,o){return o?.r?function(t,e,i,s,o){let r=o?.r||0;return[["M",t+r,e],["L",t+i-r,e],["A",r,r,0,0,1,t+i,e+r],["L",t+i,e+s-r],["A",r,r,0,0,1,t+i-r,e+s],["L",t+r,e+s],["A",r,r,0,0,1,t,e+s-r],["L",t,e+r],["A",r,r,0,0,1,t+r,e],["Z"]]}(t,e,i,s,o):[["M",t,e],["L",t+i,e],["L",t+i,e+s],["L",t,e+s],["Z"]]}},{relativeLength:tE}=B(),tO={"navigator-handle":function(t,e,i,s,o={}){let r=o.width?o.width/2:i,n=tE(o.borderRadius||0,Math.min(2*r,s));return[["M",-1.5,(s=o.height||s)/2-3.5],["L",-1.5,s/2+4.5],["M",.5,s/2-3.5],["L",.5,s/2+4.5],...tT.rect(-r-1,.5,2*r+1,s,{r:n})]}};var tC=E(608),tB=E.n(tC);let{defined:tD}=B(),tP={setFixedRange:function(t){let e=this.xAxis[0];tD(e.dataMax)&&tD(e.dataMin)&&t?this.fixedRange=Math.min(t,e.dataMax-e.dataMin):this.fixedRange=t}},{defaultOptions:tR}=B(),{composed:tG}=B(),{getRendererType:tI}=tB(),{setFixedRange:tz}=tP,{addEvent:tL,extend:tH,pushUnique:tW}=B();function tX(){this.chart.navigator&&!this.options.isInternal&&this.chart.navigator.setBaseSeries(null,!1)}let tY={compose:function(t,e,i){tm.compose(e),tW(tG,"Navigator")&&(t.prototype.setFixedRange=tz,tH(tI().prototype.symbols,tO),tH(tR,{navigator:tA}),tL(i,"afterUpdate",tX))}},{composed:tN}=B(),{addEvent:tF,defined:tU,pick:t_,pushUnique:tV}=B();!function(t){let e;function i(t){let e=t_(t.options?.min,t.min),i=t_(t.options?.max,t.max);return{axisMin:e,axisMax:i,scrollMin:tU(t.dataMin)?Math.min(e,t.min,t.dataMin,t_(t.threshold,1/0)):e,scrollMax:tU(t.dataMax)?Math.max(i,t.max,t.dataMax,t_(t.threshold,-1/0)):i}}function s(){let t=this.scrollbar,e=t&&!t.options.opposite,i=this.horiz?2:e?3:1;t&&(this.chart.scrollbarsOffsets=[0,0],this.chart.axisOffset[i]+=t.size+(t.options.margin||0))}function o(){let t=this;t.options?.scrollbar?.enabled&&(t.options.scrollbar.vertical=!t.horiz,t.options.startOnTick=t.options.endOnTick=!1,t.scrollbar=new e(t.chart.renderer,t.options.scrollbar,t.chart),tF(t.scrollbar,"changed",function(e){let s,o,{axisMin:r,axisMax:n,scrollMin:a,scrollMax:l}=i(t),h=l-a;if(tU(r)&&tU(n))if(t.horiz&&!t.reversed||!t.horiz&&t.reversed?(s=a+h*this.to,o=a+h*this.from):(s=a+h*(1-this.from),o=a+h*(1-this.to)),this.shouldUpdateExtremes(e.DOMType)){let i="mousemove"!==e.DOMType&&"touchmove"!==e.DOMType&&void 0;t.setExtremes(o,s,!0,i,e)}else this.setRange(this.from,this.to)}))}function r(){let t,e,s,{scrollMin:o,scrollMax:r}=i(this),n=this.scrollbar,a=this.axisTitleMargin+(this.titleOffset||0),l=this.chart.scrollbarsOffsets,h=this.options.margin||0;if(n&&l){if(this.horiz)this.opposite||(l[1]+=a),n.position(this.left,this.top+this.height+2+l[1]-(this.opposite?h:0),this.width,this.height),this.opposite||(l[1]+=h),t=1;else{let e;this.opposite&&(l[0]+=a),e=n.options.opposite?this.left+this.width+2+l[0]-(this.opposite?0:h):this.opposite?0:h,n.position(e,this.top,this.width,this.height),this.opposite&&(l[0]+=h),t=0}if(l[t]+=n.size+(n.options.margin||0),isNaN(o)||isNaN(r)||!tU(this.min)||!tU(this.max)||this.dataMin===this.dataMax)n.setRange(0,1);else if(this.min===this.max){let t=this.pointRange/(this.dataMax+1);e=t*this.min,s=t*(this.max+1),n.setRange(e,s)}else e=(this.min-o)/(r-o),s=(this.max-o)/(r-o),this.horiz&&!this.reversed||!this.horiz&&this.reversed?n.setRange(e,s):n.setRange(1-s,1-e)}}t.compose=function(t,i){tV(tN,"Axis.Scrollbar")&&(e=i,tF(t,"afterGetOffset",s),tF(t,"afterInit",o),tF(t,"afterRender",r))}}(b||(b={}));let tZ=b,tj={height:10,barBorderRadius:5,buttonBorderRadius:0,buttonsEnabled:!1,liveRedraw:void 0,margin:void 0,minWidth:6,opposite:!0,step:.2,zIndex:3,barBackgroundColor:"#cccccc",barBorderWidth:0,barBorderColor:"#cccccc",buttonArrowColor:"#333333",buttonBackgroundColor:"#e6e6e6",buttonBorderColor:"#cccccc",buttonBorderWidth:1,rifleColor:"none",trackBackgroundColor:"rgba(255, 255, 255, 0.001)",trackBorderColor:"#cccccc",trackBorderRadius:5,trackBorderWidth:1},{defaultOptions:tq}=B(),{composed:tK}=B(),{addEvent:t$,correctFloat:tJ,crisp:tQ,defined:t0,destroyObjectProperties:t1,extend:t2,fireEvent:t5,merge:t3,pick:t6,pushUnique:t4,removeEvent:t8}=B();class t9{static compose(t){tZ.compose(t,t9),t4(tK,"Scrollbar")&&t2(tq,{scrollbar:tj})}static swapXY(t,e){return e&&t.forEach(t=>{let e,i=t.length;for(let s=0;s<i;s+=2)"number"==typeof(e=t[s+1])&&(t[s+1]=t[s+2],t[s+2]=e)}),t}constructor(t,e,i){this._events=[],this.chartX=0,this.chartY=0,this.from=0,this.scrollbarButtons=[],this.scrollbarLeft=0,this.scrollbarStrokeWidth=1,this.scrollbarTop=0,this.size=0,this.to=0,this.trackBorderWidth=1,this.x=0,this.y=0,this.init(t,e,i)}addEvents(){let t=this.options.inverted?[1,0]:[0,1],e=this.scrollbarButtons,i=this.scrollbarGroup.element,s=this.track.element,o=this.mouseDownHandler.bind(this),r=this.mouseMoveHandler.bind(this),n=this.mouseUpHandler.bind(this),a=[[e[t[0]].element,"click",this.buttonToMinClick.bind(this)],[e[t[1]].element,"click",this.buttonToMaxClick.bind(this)],[s,"click",this.trackClick.bind(this)],[i,"mousedown",o],[i.ownerDocument,"mousemove",r],[i.ownerDocument,"mouseup",n],[i,"touchstart",o],[i.ownerDocument,"touchmove",r],[i.ownerDocument,"touchend",n]];a.forEach(function(t){t$.apply(null,t)}),this._events=a}buttonToMaxClick(t){let e=(this.to-this.from)*t6(this.options.step,.2);this.updatePosition(this.from+e,this.to+e),t5(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})}buttonToMinClick(t){let e=tJ(this.to-this.from)*t6(this.options.step,.2);this.updatePosition(tJ(this.from-e),tJ(this.to-e)),t5(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})}cursorToScrollbarPosition(t){let e=this.options,i=e.minWidth>this.calculatedWidth?e.minWidth:0;return{chartX:(t.chartX-this.x-this.xOffset)/(this.barWidth-i),chartY:(t.chartY-this.y-this.yOffset)/(this.barWidth-i)}}destroy(){let t=this,e=t.chart.scroller;t.removeEvents(),["track","scrollbarRifles","scrollbar","scrollbarGroup","group"].forEach(function(e){t[e]&&t[e].destroy&&(t[e]=t[e].destroy())}),e&&t===e.scrollbar&&(e.scrollbar=null,t1(e.scrollbarButtons))}drawScrollbarButton(t){let e=this.renderer,i=this.scrollbarButtons,s=this.options,o=this.size,r=e.g().add(this.group);if(i.push(r),s.buttonsEnabled){let n=e.rect().addClass("highcharts-scrollbar-button").add(r);this.chart.styledMode||n.attr({stroke:s.buttonBorderColor,"stroke-width":s.buttonBorderWidth,fill:s.buttonBackgroundColor}),n.attr(n.crisp({x:-.5,y:-.5,width:o,height:o,r:s.buttonBorderRadius},n.strokeWidth()));let a=e.path(t9.swapXY([["M",o/2+(t?-1:1),o/2-3],["L",o/2+(t?-1:1),o/2+3],["L",o/2+(t?2:-2),o/2]],s.vertical)).addClass("highcharts-scrollbar-arrow").add(i[t]);this.chart.styledMode||a.attr({fill:s.buttonArrowColor})}}init(t,e,i){this.scrollbarButtons=[],this.renderer=t,this.userOptions=e,this.options=t3(tj,tq.scrollbar,e),this.options.margin=t6(this.options.margin,10),this.chart=i,this.size=t6(this.options.size,this.options.height),e.enabled&&(this.render(),this.addEvents())}mouseDownHandler(t){let e=this.chart.pointer?.normalize(t)||t,i=this.cursorToScrollbarPosition(e);this.chartX=i.chartX,this.chartY=i.chartY,this.initPositions=[this.from,this.to],this.grabbedCenter=!0}mouseMoveHandler(t){let e,i=this.chart.pointer?.normalize(t)||t,s=this.options.vertical?"chartY":"chartX",o=this.initPositions||[];this.grabbedCenter&&(!t.touches||0!==t.touches[0][s])&&(e=this.cursorToScrollbarPosition(i)[s]-this[s],this.hasDragged=!0,this.updatePosition(o[0]+e,o[1]+e),this.hasDragged&&t5(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMType:t.type,DOMEvent:t}))}mouseUpHandler(t){this.hasDragged&&t5(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMType:t.type,DOMEvent:t}),this.grabbedCenter=this.hasDragged=this.chartX=this.chartY=null}position(t,e,i,s){let{buttonsEnabled:o,margin:r=0,vertical:n}=this.options,a=this.rendered?"animate":"attr",l=s,h=0;this.group.show(),this.x=t,this.y=e+this.trackBorderWidth,this.width=i,this.height=s,this.xOffset=l,this.yOffset=h,n?(this.width=this.yOffset=i=h=this.size,this.xOffset=l=0,this.yOffset=h=o?this.size:0,this.barWidth=s-(o?2*i:0),this.x=t+=r):(this.height=s=this.size,this.xOffset=l=o?this.size:0,this.barWidth=i-(o?2*s:0),this.y=this.y+r),this.group[a]({translateX:t,translateY:this.y}),this.track[a]({width:i,height:s}),this.scrollbarButtons[1][a]({translateX:n?0:i-l,translateY:n?s-h:0})}removeEvents(){this._events.forEach(function(t){t8.apply(null,t)}),this._events.length=0}render(){let t=this.renderer,e=this.options,i=this.size,s=this.chart.styledMode,o=t.g("scrollbar").attr({zIndex:e.zIndex}).hide().add();this.group=o,this.track=t.rect().addClass("highcharts-scrollbar-track").attr({r:e.trackBorderRadius||0,height:i,width:i}).add(o),s||this.track.attr({fill:e.trackBackgroundColor,stroke:e.trackBorderColor,"stroke-width":e.trackBorderWidth});let r=this.trackBorderWidth=this.track.strokeWidth();this.track.attr({x:-tQ(0,r),y:-tQ(0,r)}),this.scrollbarGroup=t.g().add(o),this.scrollbar=t.rect().addClass("highcharts-scrollbar-thumb").attr({height:i-r,width:i-r,r:e.barBorderRadius||0}).add(this.scrollbarGroup),this.scrollbarRifles=t.path(t9.swapXY([["M",-3,i/4],["L",-3,2*i/3],["M",0,i/4],["L",0,2*i/3],["M",3,i/4],["L",3,2*i/3]],e.vertical)).addClass("highcharts-scrollbar-rifles").add(this.scrollbarGroup),s||(this.scrollbar.attr({fill:e.barBackgroundColor,stroke:e.barBorderColor,"stroke-width":e.barBorderWidth}),this.scrollbarRifles.attr({stroke:e.rifleColor,"stroke-width":1})),this.scrollbarStrokeWidth=this.scrollbar.strokeWidth(),this.scrollbarGroup.translate(-tQ(0,this.scrollbarStrokeWidth),-tQ(0,this.scrollbarStrokeWidth)),this.drawScrollbarButton(0),this.drawScrollbarButton(1)}setRange(t,e){let i,s,o=this.options,r=o.vertical,n=o.minWidth,a=this.barWidth,l=!this.rendered||this.hasDragged||this.chart.navigator&&this.chart.navigator.hasDragged?"attr":"animate";if(!t0(a))return;let h=a*Math.min(e,1);i=Math.ceil(a*(t=Math.max(t,0))),this.calculatedWidth=s=tJ(h-i),s<n&&(i=(a-n+s)*t,s=n);let d=Math.floor(i+this.xOffset+this.yOffset),p=s/2-.5;this.from=t,this.to=e,r?(this.scrollbarGroup[l]({translateY:d}),this.scrollbar[l]({height:s}),this.scrollbarRifles[l]({translateY:p}),this.scrollbarTop=d,this.scrollbarLeft=0):(this.scrollbarGroup[l]({translateX:d}),this.scrollbar[l]({width:s}),this.scrollbarRifles[l]({translateX:p}),this.scrollbarLeft=d,this.scrollbarTop=0),s<=12?this.scrollbarRifles.hide():this.scrollbarRifles.show(),!1===o.showFull&&(t<=0&&e>=1?this.group.hide():this.group.show()),this.rendered=!0}shouldUpdateExtremes(t){return t6(this.options.liveRedraw,B().svg&&!B().isTouchDevice&&!this.chart.boosted)||"mouseup"===t||"touchend"===t||!t0(t)}trackClick(t){let e=this.chart.pointer?.normalize(t)||t,i=this.to-this.from,s=this.y+this.scrollbarTop,o=this.x+this.scrollbarLeft;this.options.vertical&&e.chartY>s||!this.options.vertical&&e.chartX>o?this.updatePosition(this.from+i,this.to+i):this.updatePosition(this.from-i,this.to-i),t5(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})}update(t){this.destroy(),this.init(this.chart.renderer,t3(!0,this.options,t),this.chart)}updatePosition(t,e){e>1&&(t=tJ(1-tJ(e-t)),e=1),t<0&&(e=tJ(e-t),t=0),this.from=t,this.to=e}}t9.defaultOptions=tj;var t7=E(540),et=E.n(t7);let{defaultOptions:ee}=B(),{isTouchDevice:ei}=B(),{prototype:{symbols:es}}=et(),{addEvent:eo,clamp:er,correctFloat:en,defined:ea,destroyObjectProperties:el,erase:eh,extend:ed,find:ep,fireEvent:ec,isArray:eu,isNumber:eg,merge:em,pick:ef,removeEvent:ex,splat:eb}=B();function ev(t,...e){let i=[].filter.call(e,eg);if(i.length)return Math[t].apply(0,i)}class ey{static compose(t,e,i){tn.compose(t,ey),tY.compose(t,e,i)}constructor(t){this.isDirty=!1,this.scrollbarHeight=0,this.init(t)}drawHandle(t,e,i,s){let o=this.navigatorOptions.handles.height;this.handles[e][s](i?{translateX:Math.round(this.left+this.height/2),translateY:Math.round(this.top+parseInt(t,10)+.5-o)}:{translateX:Math.round(this.left+parseInt(t,10)),translateY:Math.round(this.top+this.height/2-o/2-1)})}drawOutline(t,e,i,s){let o=this.navigatorOptions.maskInside,r=this.outline.strokeWidth(),n=r/2,a=r%2/2,l=this.scrollButtonSize,h=this.size,d=this.top,p=this.height,c=d-n,u=d+p,g=this.left,m,f;i?(m=d+e+a,e=d+t+a,f=[["M",g+p,d-l-a],["L",g+p,m],["L",g,m],["M",g,e],["L",g+p,e],["L",g+p,d+h+l]],o&&f.push(["M",g+p,m-n],["L",g+p,e+n])):(g-=l,t+=g+l-a,e+=g+l-a,f=[["M",g,c],["L",t,c],["L",t,u],["M",e,u],["L",e,c],["L",g+h+2*l,c]],o&&f.push(["M",t-n,c],["L",e+n,c])),this.outline[s]({d:f})}drawMasks(t,e,i,s){let o,r,n,a,l=this.left,h=this.top,d=this.height;i?(n=[l,l,l],a=[h,h+t,h+e],r=[d,d,d],o=[t,e-t,this.size-e]):(n=[l,l+t,l+e],a=[h,h,h],r=[t,e-t,this.size-e],o=[d,d,d]),this.shades.forEach((t,e)=>{t[s]({x:n[e],y:a[e],width:r[e],height:o[e]})})}renderElements(){let t=this,e=t.navigatorOptions,i=e.maskInside,s=t.chart,o=s.inverted,r=s.renderer,n={cursor:o?"ns-resize":"ew-resize"},a=t.navigatorGroup??(t.navigatorGroup=r.g("navigator").attr({zIndex:8,visibility:"hidden"}).add());if([!i,i,!i].forEach((i,o)=>{let l=t.shades[o]??(t.shades[o]=r.rect().addClass("highcharts-navigator-mask"+(1===o?"-inside":"-outside")).add(a));s.styledMode||(l.attr({fill:i?e.maskFill:"rgba(0,0,0,0)"}),1===o&&l.css(n))}),t.outline||(t.outline=r.path().addClass("highcharts-navigator-outline").add(a)),s.styledMode||t.outline.attr({"stroke-width":e.outlineWidth,stroke:e.outlineColor}),e.handles?.enabled){let i=e.handles,{height:o,width:l}=i;[0,1].forEach(e=>{let h=i.symbols[e];if(t.handles[e]&&t.handles[e].symbolUrl===h){if(!t.handles[e].isImg&&t.handles[e].symbolName!==h){let i=es[h].call(es,-l/2-1,0,l,o);t.handles[e].attr({d:i}),t.handles[e].symbolName=h}}else t.handles[e]?.destroy(),t.handles[e]=r.symbol(h,-l/2-1,0,l,o,i),t.handles[e].attr({zIndex:7-e}).addClass("highcharts-navigator-handle highcharts-navigator-handle-"+["left","right"][e]).add(a),t.addMouseEvents();s.inverted&&t.handles[e].attr({rotation:90,rotationOriginX:Math.floor(-l/2),rotationOriginY:(o+l)/2}),s.styledMode||t.handles[e].attr({fill:i.backgroundColor,stroke:i.borderColor,"stroke-width":i.lineWidth,width:i.width,height:i.height,x:-l/2-1,y:0}).css(n)})}}update(t,e=!1){let i=this.chart,s=i.options.chart.inverted!==i.scrollbar?.options.vertical;if(em(!0,i.options.navigator,t),this.navigatorOptions=i.options.navigator||{},this.setOpposite(),ea(t.enabled)||s)return this.destroy(),this.navigatorEnabled=t.enabled||this.navigatorEnabled,this.init(i);if(this.navigatorEnabled&&(this.isDirty=!0,!1===t.adaptToUpdatedData&&this.baseSeries.forEach(t=>{ex(t,"updatedData",this.updatedDataHandler)},this),t.adaptToUpdatedData&&this.baseSeries.forEach(t=>{t.eventsToUnbind.push(eo(t,"updatedData",this.updatedDataHandler))},this),(t.series||t.baseSeries)&&this.setBaseSeries(void 0,!1),t.height||t.xAxis||t.yAxis)){this.height=t.height??this.height;let e=this.getXAxisOffsets();this.xAxis.update({...t.xAxis,offsets:e,[i.inverted?"width":"height"]:this.height,[i.inverted?"height":"width"]:void 0},!1),this.yAxis.update({...t.yAxis,[i.inverted?"width":"height"]:this.height},!1)}e&&i.redraw()}render(t,e,i,s){let o=this.chart,r=this.xAxis,n=r.pointRange||0,a=r.navigatorAxis.fake?o.xAxis[0]:r,l=this.navigatorEnabled,h=this.rendered,d=o.inverted,p=o.xAxis[0].minRange,c=o.xAxis[0].options.maxRange,u=this.scrollButtonSize,g,m,f,x=this.scrollbarHeight,b,v;if(this.hasDragged&&!ea(i))return;if(this.isDirty&&this.renderElements(),t=en(t-n/2),e=en(e+n/2),!eg(t)||!eg(e))if(!h)return;else i=0,s=ef(r.width,a.width);this.left=ef(r.left,o.plotLeft+u+(d?o.plotWidth:0));let y=this.size=b=ef(r.len,(d?o.plotHeight:o.plotWidth)-2*u);g=d?x:b+2*u,i=ef(i,r.toPixels(t,!0)),s=ef(s,r.toPixels(e,!0)),eg(i)&&Math.abs(i)!==1/0||(i=0,s=g);let M=r.toValue(i,!0),A=r.toValue(s,!0),k=Math.abs(en(A-M));k<p?this.grabbedLeft?i=r.toPixels(A-p-n,!0):this.grabbedRight&&(s=r.toPixels(M+p+n,!0)):ea(c)&&en(k-n)>c&&(this.grabbedLeft?i=r.toPixels(A-c-n,!0):this.grabbedRight&&(s=r.toPixels(M+c+n,!0))),this.zoomedMax=er(Math.max(i,s),0,y),this.zoomedMin=er(this.fixedWidth?this.zoomedMax-this.fixedWidth:Math.min(i,s),0,y),this.range=this.zoomedMax-this.zoomedMin,y=Math.round(this.zoomedMax);let w=Math.round(this.zoomedMin);l&&(this.navigatorGroup.attr({visibility:"inherit"}),v=h&&!this.hasDragged?"animate":"attr",this.drawMasks(w,y,d,v),this.drawOutline(w,y,d,v),this.navigatorOptions.handles.enabled&&(this.drawHandle(w,0,d,v),this.drawHandle(y,1,d,v))),this.scrollbar&&(d?(f=this.top-u,m=this.left-x+(l||!a.opposite?0:(a.titleOffset||0)+a.axisTitleMargin),x=b+2*u):(f=this.top+(l?this.height:-x),m=this.left-u),this.scrollbar.position(m,f,g,x),this.scrollbar.setRange(this.zoomedMin/(b||1),this.zoomedMax/(b||1))),this.rendered=!0,this.isDirty=!1,ec(this,"afterRender")}addMouseEvents(){let t=this,e=t.chart,i=e.container,s=[],o,r;t.mouseMoveHandler=o=function(e){t.onMouseMove(e)},t.mouseUpHandler=r=function(e){t.onMouseUp(e)},(s=t.getPartsEvents("mousedown")).push(eo(e.renderTo,"mousemove",o),eo(i.ownerDocument,"mouseup",r),eo(e.renderTo,"touchmove",o),eo(i.ownerDocument,"touchend",r)),s.concat(t.getPartsEvents("touchstart")),t.eventsToUnbind=s,t.series&&t.series[0]&&s.push(eo(t.series[0].xAxis,"foundExtremes",function(){e.navigator.modifyNavigatorAxisExtremes()}))}getPartsEvents(t){let e=this,i=[];return["shades","handles"].forEach(function(s){e[s].forEach(function(o,r){i.push(eo(o.element,t,function(t){e[s+"Mousedown"](t,r)}))})}),i}shadesMousedown(t,e){t=this.chart.pointer?.normalize(t)||t;let i=this.chart,s=this.xAxis,o=this.zoomedMin,r=this.size,n=this.range,a=this.left,l=t.chartX,h,d,p,c;i.inverted&&(l=t.chartY,a=this.top),1===e?(this.grabbedCenter=l,this.fixedWidth=n,this.dragOffset=l-o):(c=l-a-n/2,0===e?c=Math.max(0,c):2===e&&c+n>=r&&(c=r-n,this.reversedExtremes?(c-=n,d=this.getUnionExtremes().dataMin):h=this.getUnionExtremes().dataMax),c!==o&&(this.fixedWidth=n,ea((p=s.navigatorAxis.toFixedRange(c,c+n,d,h)).min)&&ec(this,"setRange",{min:Math.min(p.min,p.max),max:Math.max(p.min,p.max),redraw:!0,eventArguments:{trigger:"navigator"}})))}handlesMousedown(t,e){t=this.chart.pointer?.normalize(t)||t;let i=this.chart,s=i.xAxis[0],o=this.reversedExtremes;0===e?(this.grabbedLeft=!0,this.otherHandlePos=this.zoomedMax,this.fixedExtreme=o?s.min:s.max):(this.grabbedRight=!0,this.otherHandlePos=this.zoomedMin,this.fixedExtreme=o?s.max:s.min),i.setFixedRange(void 0)}onMouseMove(t){let e=this,i=e.chart,s=e.navigatorSize,o=e.range,r=e.dragOffset,n=i.inverted,a=e.left,l;(!t.touches||0!==t.touches[0].pageX)&&(l=(t=i.pointer?.normalize(t)||t).chartX,n&&(a=e.top,l=t.chartY),e.grabbedLeft?(e.hasDragged=!0,e.render(0,0,l-a,e.otherHandlePos)):e.grabbedRight?(e.hasDragged=!0,e.render(0,0,e.otherHandlePos,l-a)):e.grabbedCenter&&(e.hasDragged=!0,l<r?l=r:l>s+r-o&&(l=s+r-o),e.render(0,0,l-r,l-r+o)),e.hasDragged&&e.scrollbar&&ef(e.scrollbar.options.liveRedraw,!ei&&!this.chart.boosted)&&(t.DOMType=t.type,setTimeout(function(){e.onMouseUp(t)},0)))}onMouseUp(t){let e,i,s,o,r,n,a=this.chart,l=this.xAxis,h=this.scrollbar,d=t.DOMEvent||t,p=a.inverted,c=this.rendered&&!this.hasDragged?"animate":"attr";(this.hasDragged&&(!h||!h.hasDragged)||"scrollbar"===t.trigger)&&(s=this.getUnionExtremes(),this.zoomedMin===this.otherHandlePos?o=this.fixedExtreme:this.zoomedMax===this.otherHandlePos&&(r=this.fixedExtreme),this.zoomedMax===this.size&&(r=this.reversedExtremes?s.dataMin:s.dataMax),0===this.zoomedMin&&(o=this.reversedExtremes?s.dataMax:s.dataMin),ea((n=l.navigatorAxis.toFixedRange(this.zoomedMin,this.zoomedMax,o,r)).min)&&ec(this,"setRange",{min:Math.min(n.min,n.max),max:Math.max(n.min,n.max),redraw:!0,animation:!this.hasDragged&&null,eventArguments:{trigger:"navigator",triggerOp:"navigator-drag",DOMEvent:d}})),"mousemove"!==t.DOMType&&"touchmove"!==t.DOMType&&(this.grabbedLeft=this.grabbedRight=this.grabbedCenter=this.fixedWidth=this.fixedExtreme=this.otherHandlePos=this.hasDragged=this.dragOffset=null),this.navigatorEnabled&&eg(this.zoomedMin)&&eg(this.zoomedMax)&&(i=Math.round(this.zoomedMin),e=Math.round(this.zoomedMax),this.shades&&this.drawMasks(i,e,p,c),this.outline&&this.drawOutline(i,e,p,c),this.navigatorOptions.handles.enabled&&Object.keys(this.handles).length===this.handles.length&&(this.drawHandle(i,0,p,c),this.drawHandle(e,1,p,c)))}removeEvents(){this.eventsToUnbind&&(this.eventsToUnbind.forEach(function(t){t()}),this.eventsToUnbind=void 0),this.removeBaseSeriesEvents()}removeBaseSeriesEvents(){let t=this.baseSeries||[];this.navigatorEnabled&&t[0]&&(!1!==this.navigatorOptions.adaptToUpdatedData&&t.forEach(function(t){ex(t,"updatedData",this.updatedDataHandler)},this),t[0].xAxis&&ex(t[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes))}getXAxisOffsets(){return this.chart.inverted?[this.scrollButtonSize,0,-this.scrollButtonSize,0]:[0,-this.scrollButtonSize,0,this.scrollButtonSize]}init(t){let e=t.options,i=e.navigator||{},s=i.enabled,o=e.scrollbar||{},r=o.enabled,n=s&&i.height||0,a=r&&o.height||0,l=o.buttonsEnabled&&a||0;this.handles=[],this.shades=[],this.chart=t,this.setBaseSeries(),this.height=n,this.scrollbarHeight=a,this.scrollButtonSize=l,this.scrollbarEnabled=r,this.navigatorEnabled=s,this.navigatorOptions=i,this.scrollbarOptions=o,this.setOpposite();let h=this,d=h.baseSeries,p=t.xAxis.length,c=t.yAxis.length,u=d&&d[0]&&d[0].xAxis||t.xAxis[0]||{options:{}};if(t.isDirtyBox=!0,h.navigatorEnabled){let e=this.getXAxisOffsets();h.xAxis=new(P())(t,em({breaks:u.options.breaks,ordinal:u.options.ordinal,overscroll:u.options.overscroll},i.xAxis,{type:"datetime",yAxis:i.yAxis?.id,index:p,isInternal:!0,offset:0,keepOrdinalPadding:!0,startOnTick:!1,endOnTick:!1,minPadding:u.options.ordinal?0:u.options.minPadding,maxPadding:u.options.ordinal?0:u.options.maxPadding,zoomEnabled:!1},t.inverted?{offsets:e,width:n}:{offsets:e,height:n}),"xAxis"),h.yAxis=new(P())(t,em(i.yAxis,{alignTicks:!1,offset:0,index:c,isInternal:!0,reversed:ef(i.yAxis&&i.yAxis.reversed,t.yAxis[0]&&t.yAxis[0].reversed,!1),zoomEnabled:!1},t.inverted?{width:n}:{height:n}),"yAxis"),d||i.series.data?h.updateNavigatorSeries(!1):0===t.series.length&&(h.unbindRedraw=eo(t,"beforeRedraw",function(){t.series.length>0&&!h.series&&(h.setBaseSeries(),h.unbindRedraw())})),h.reversedExtremes=t.inverted&&!h.xAxis.reversed||!t.inverted&&h.xAxis.reversed,h.renderElements(),h.addMouseEvents()}else h.xAxis={chart:t,navigatorAxis:{fake:!0},translate:function(e,i){let s=t.xAxis[0],o=s.getExtremes(),r=s.len-2*l,n=ev("min",s.options.min,o.dataMin),a=ev("max",s.options.max,o.dataMax)-n;return i?e*a/r+n:r*(e-n)/a},toPixels:function(t){return this.translate(t)},toValue:function(t){return this.translate(t,!0)}},h.xAxis.navigatorAxis.axis=h.xAxis,h.xAxis.navigatorAxis.toFixedRange=tm.prototype.toFixedRange.bind(h.xAxis.navigatorAxis);if(t.options.scrollbar?.enabled){let e=em(t.options.scrollbar,{vertical:t.inverted});eg(e.margin)||(e.margin=t.inverted?-3:3),t.scrollbar=h.scrollbar=new t9(t.renderer,e,t),eo(h.scrollbar,"changed",function(t){let e=h.size,i=e*this.to,s=e*this.from;h.hasDragged=h.scrollbar.hasDragged,h.render(0,0,s,i),this.shouldUpdateExtremes(t.DOMType)&&setTimeout(function(){h.onMouseUp(t)})})}h.addBaseSeriesEvents(),h.addChartEvents()}setOpposite(){let t=this.navigatorOptions,e=this.navigatorEnabled,i=this.chart;this.opposite=ef(t.opposite,!!(!e&&i.inverted))}getUnionExtremes(t){let e,i=this.chart.xAxis[0],s=this.chart.time,o=this.xAxis,r=o.options,n=i.options;return t&&null===i.dataMin||(e={dataMin:ef(s.parse(r?.min),ev("min",s.parse(n.min),i.dataMin,o.dataMin,o.min)),dataMax:ef(s.parse(r?.max),ev("max",s.parse(n.max),i.dataMax,o.dataMax,o.max))}),e}setBaseSeries(t,e){let i=this.chart,s=this.baseSeries=[];t=t||i.options&&i.options.navigator.baseSeries||(i.series.length?ep(i.series,t=>!t.options.isInternal).index:0),(i.series||[]).forEach((e,i)=>{!e.options.isInternal&&(e.options.showInNavigator||(i===t||e.options.id===t)&&!1!==e.options.showInNavigator)&&s.push(e)}),this.xAxis&&!this.xAxis.navigatorAxis.fake&&this.updateNavigatorSeries(!0,e)}updateNavigatorSeries(t,e){let i=this,s=i.chart,o=i.baseSeries,r={enableMouseTracking:!1,index:null,linkedTo:null,group:"nav",padXAxis:!1,xAxis:this.navigatorOptions.xAxis?.id,yAxis:this.navigatorOptions.yAxis?.id,showInLegend:!1,stacking:void 0,isInternal:!0,states:{inactive:{opacity:1}}},n=i.series=(i.series||[]).filter(t=>{let e=t.baseSeries;return!(0>o.indexOf(e))||(e&&(ex(e,"updatedData",i.updatedDataHandler),delete e.navigatorSeries),t.chart&&t.destroy(),!1)}),a,l,h=i.navigatorOptions.series,d;o&&o.length&&o.forEach(t=>{let p=t.navigatorSeries,c=ed({color:t.color,visible:t.visible},eu(h)?ee.navigator.series:h);if(p&&!1===i.navigatorOptions.adaptToUpdatedData)return;r.name="Navigator "+o.length,d=(a=t.options||{}).navigatorOptions||{},c.dataLabels=eb(c.dataLabels),(l=em(a,r,c,d)).pointRange=ef(c.pointRange,d.pointRange,ee.plotOptions[l.type||"line"].pointRange);let u=d.data||c.data;i.hasNavigatorData=i.hasNavigatorData||!!u,l.data=u||a.data?.slice(0),p&&p.options?p.update(l,e):(t.navigatorSeries=s.initSeries(l),s.setSortedData(),t.navigatorSeries.baseSeries=t,n.push(t.navigatorSeries))}),(h.data&&!(o&&o.length)||eu(h))&&(i.hasNavigatorData=!1,(h=eb(h)).forEach((t,e)=>{r.name="Navigator "+(n.length+1),(l=em(ee.navigator.series,{color:s.series[e]&&!s.series[e].options.isInternal&&s.series[e].color||s.options.colors[e]||s.options.colors[0]},r,t)).data=t.data,l.data&&(i.hasNavigatorData=!0,n.push(s.initSeries(l)))})),t&&this.addBaseSeriesEvents()}addBaseSeriesEvents(){let t=this,e=t.baseSeries||[];e[0]&&e[0].xAxis&&e[0].eventsToUnbind.push(eo(e[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes)),e.forEach(i=>{i.eventsToUnbind.push(eo(i,"show",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!0,!1)})),i.eventsToUnbind.push(eo(i,"hide",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!1,!1)})),!1!==this.navigatorOptions.adaptToUpdatedData&&i.xAxis&&i.eventsToUnbind.push(eo(i,"updatedData",this.updatedDataHandler)),i.eventsToUnbind.push(eo(i,"remove",function(){e&&eh(e,i),this.navigatorSeries&&t.series&&(eh(t.series,this.navigatorSeries),ea(this.navigatorSeries.options)&&this.navigatorSeries.remove(!1),delete this.navigatorSeries)}))})}getBaseSeriesMin(t){return this.baseSeries.reduce(function(t,e){return Math.min(t,e.getColumn("x")[0]??t)},t)}modifyNavigatorAxisExtremes(){let t=this.xAxis;if(void 0!==t.getExtremes){let e=this.getUnionExtremes(!0);e&&(e.dataMin!==t.min||e.dataMax!==t.max)&&(t.min=e.dataMin,t.max=e.dataMax)}}modifyBaseAxisExtremes(){let t,e,i=this.chart.navigator,s=this.getExtremes(),o=s.min,r=s.max,n=s.dataMin,a=s.dataMax,l=r-o,h=i.stickToMin,d=i.stickToMax,p=ef(this.ordinal?.convertOverscroll(this.options.overscroll),0),c=i.series&&i.series[0],u=!!this.setExtremes;!(this.eventArgs&&"rangeSelectorButton"===this.eventArgs.trigger)&&(h&&(t=(e=n)+l),d&&(t=a+p,h||(e=Math.max(n,t-l,i.getBaseSeriesMin(c&&c.xData?c.xData[0]:-Number.MAX_VALUE)))),u&&(h||d)&&eg(e)&&(this.min=this.userMin=e,this.max=this.userMax=t)),i.stickToMin=i.stickToMax=null}updatedDataHandler(){let t=this.chart.navigator,e=this.navigatorSeries,i=t.reversedExtremes?0===Math.round(t.zoomedMin):Math.round(t.zoomedMax)>=Math.round(t.size);t.stickToMax=ef(this.chart.options.navigator&&this.chart.options.navigator.stickToMax,i),t.stickToMin=t.shouldStickToMin(this,t),e&&!t.hasNavigatorData&&(e.options.pointStart=this.getColumn("x")[0],e.setData(this.options.data,!1,null,!1))}shouldStickToMin(t,e){let i=e.getBaseSeriesMin(t.getColumn("x")[0]),s=t.xAxis,o=s.max,r=s.min,n=s.options.range,a=!0;return!!(eg(o)&&eg(r))&&(n&&o-i>0?o-i<n:r<=i)}addChartEvents(){this.eventsToUnbind||(this.eventsToUnbind=[]),this.eventsToUnbind.push(eo(this.chart,"redraw",function(){let t=this.navigator,e=t&&(t.baseSeries&&t.baseSeries[0]&&t.baseSeries[0].xAxis||this.xAxis[0]);e&&t.render(e.min,e.max)}),eo(this.chart,"getMargins",function(){let t=this.navigator,e=t.opposite?"plotTop":"marginBottom";this.inverted&&(e=t.opposite?"marginRight":"plotLeft"),this[e]=(this[e]||0)+(t.navigatorEnabled||!this.inverted?t.height+(this.scrollbar?.options.margin||0)+t.scrollbarHeight:0)+(t.navigatorOptions.margin||0)}),eo(ey,"setRange",function(t){this.chart.xAxis[0].setExtremes(t.min,t.max,t.redraw,t.animation,t.eventArguments)}))}destroy(){this.removeEvents(),this.xAxis&&(eh(this.chart.xAxis,this.xAxis),eh(this.chart.axes,this.xAxis)),this.yAxis&&(eh(this.chart.yAxis,this.yAxis),eh(this.chart.axes,this.yAxis)),(this.series||[]).forEach(t=>{t.destroy&&t.destroy()}),["series","xAxis","yAxis","shades","outline","scrollbarTrack","scrollbarRifles","scrollbarGroup","scrollbar","navigatorGroup","rendered"].forEach(t=>{this[t]&&this[t].destroy&&this[t].destroy(),this[t]=null}),[this.handles].forEach(t=>{el(t)}),this.baseSeries.forEach(t=>{t.navigatorSeries=void 0}),this.navigatorEnabled=!1}}!function(t){t.setLength=function(t,e,i){return Array.isArray(t)?(t.length=e,t):t[i?"subarray":"slice"](0,e)},t.splice=function(t,e,i,s,o=[]){if(Array.isArray(t))return Array.isArray(o)||(o=Array.from(o)),{removed:t.splice(e,i,...o),array:t};let r=Object.getPrototypeOf(t).constructor,n=t[s?"subarray":"slice"](e,e+i),a=new r(t.length-i+o.length);return a.set(t.subarray(0,e),0),a.set(o,e),a.set(t.subarray(e+i),e+o.length),{removed:n,array:a}}}(v||(v={}));let{setLength:eM,splice:eA}=v,{fireEvent:ek,objectEach:ew,uniqueKey:eS}=B(),eT=class{constructor(t={}){this.autoId=!t.id,this.columns={},this.id=t.id||eS(),this.modified=this,this.rowCount=0,this.versionTag=eS();let e=0;ew(t.columns||{},(t,i)=>{this.columns[i]=t.slice(),e=Math.max(e,t.length)}),this.applyRowCount(e)}applyRowCount(t){this.rowCount=t,ew(this.columns,(e,i)=>{e.length!==t&&(this.columns[i]=eM(e,t))})}deleteRows(t,e=1){if(e>0&&t<this.rowCount){let i=0;ew(this.columns,(s,o)=>{this.columns[o]=eA(s,t,e).array,i=s.length}),this.rowCount=i}ek(this,"afterDeleteRows",{rowIndex:t,rowCount:e}),this.versionTag=eS()}getColumn(t,e){return this.columns[t]}getColumns(t,e){return(t||Object.keys(this.columns)).reduce((t,e)=>(t[e]=this.columns[e],t),{})}getRow(t,e){return(e||Object.keys(this.columns)).map(e=>this.columns[e]?.[t])}setColumn(t,e=[],i=0,s){this.setColumns({[t]:e},i,s)}setColumns(t,e,i){let s=this.rowCount;ew(t,(t,e)=>{this.columns[e]=t.slice(),s=t.length}),this.applyRowCount(s),i?.silent||(ek(this,"afterSetColumns"),this.versionTag=eS())}setRow(t,e=this.rowCount,i,s){let{columns:o}=this,r=i?this.rowCount+1:e+1;ew(t,(t,n)=>{let a=o[n]||s?.addColumns!==!1&&Array(r);a&&(i?a=eA(a,e,0,!0,[t]).array:a[e]=t,o[n]=a)}),r>this.rowCount&&this.applyRowCount(r),s?.silent||(ek(this,"afterSetRows"),this.versionTag=eS())}},{addEvent:eE,correctFloat:eO,css:eC,defined:eB,error:eD,isNumber:eP,pick:eR,timeUnits:eG,isString:eI}=B();!function(t){function e(t,i,s,o,r=[],n=0,a){let l={},h=this.options.tickPixelInterval,d=this.chart.time,p=[],c,u,g,m,f,x=0,b=[],v=-Number.MAX_VALUE;if(!this.options.ordinal&&!this.options.breaks||!r||r.length<3||void 0===i)return d.getTimeTicks.apply(d,arguments);let y=r.length;for(c=0;c<y;c++){if(f=c&&r[c-1]>s,r[c]<i&&(x=c),c===y-1||r[c+1]-r[c]>5*n||f){if(r[c]>v){for(u=d.getTimeTicks(t,r[x],r[c],o);u.length&&u[0]<=v;)u.shift();u.length&&(v=u[u.length-1]),p.push(b.length),b=b.concat(u)}x=c+1}if(f)break}if(u){if(m=u.info,a&&m.unitRange<=eG.hour){for(x=1,c=b.length-1;x<c;x++)d.dateFormat("%d",b[x])!==d.dateFormat("%d",b[x-1])&&(l[b[x]]="day",g=!0);g&&(l[b[0]]="day"),m.higherRanks=l}m.segmentStarts=p,b.info=m}else eD(12,!1,this.chart);if(a&&eB(h)){let t=b.length,e=[],i=[],o,r,n,a,d,p=t;for(;p--;)r=this.translate(b[p]),n&&(i[p]=n-r),e[p]=n=r;for(i.sort((t,e)=>t-e),(a=i[Math.floor(i.length/2)])<.6*h&&(a=null),p=b[t-1]>s?t-1:t,n=void 0;p--;)d=Math.abs(n-(r=e[p])),n&&d<.8*h&&(null===a||d<.8*a)?(l[b[p]]&&!l[b[p+1]]?(o=p+1,n=r):o=p,b.splice(o,1)):n=r}return b}function i(t){let e=this.ordinal.positions;if(!e)return t;let i=e.length-1,s;return(t<0?t=e[0]:t>i?t=e[i]:(i=Math.floor(t),s=t-i),void 0!==s&&void 0!==e[i])?e[i]+(s?s*(e[i+1]-e[i]):0):t}function s(t){let e=this.ordinal,i=this.old?this.old.min:this.min,s=this.old?this.old.transA:this.transA,o=e.getExtendedPositions();if(o?.length){let r=eO((t-i)*s+this.minPixelPadding),n=eO(e.getIndexOfPoint(r,o)),a=eO(n%1);if(n>=0&&n<=o.length-1){let t=o[Math.floor(n)],e=o[Math.ceil(n)];return o[Math.floor(n)]+a*(e-t)}}return t}function o(e,i){let s=t.Additions.findIndexOf(e,i,!0);if(e[s]===i)return s;let o=(i-e[s])/(e[s+1]-e[s]);return s+o}function r(){this.ordinal||(this.ordinal=new t.Additions(this))}function n(){let{eventArgs:t,options:e}=this;if(this.isXAxis&&eB(e.overscroll)&&0!==e.overscroll&&eP(this.max)&&eP(this.min)&&(this.options.ordinal&&!this.ordinal.originalOrdinalRange&&this.ordinal.getExtendedPositions(!1),this.max===this.dataMax&&(t?.trigger!=="pan"||this.isInternal)&&t?.trigger!=="navigator")){let i=this.ordinal.convertOverscroll(e.overscroll);this.max+=i,!this.isInternal&&eB(this.userMin)&&t?.trigger!=="mousewheel"&&(this.min+=i)}}function a(){this.horiz&&!this.isDirty&&(this.isDirty=this.isOrdinal&&this.chart.navigator&&!this.chart.navigator.adaptToUpdatedData)}function l(){this.ordinal&&(this.ordinal.beforeSetTickPositions(),this.tickInterval=this.ordinal.postProcessTickInterval(this.tickInterval))}function h(t){let e=this.xAxis[0],i=e.ordinal.convertOverscroll(e.options.overscroll),s=t.originalEvent.chartX,o=this.options.chart.panning,r=!1;if(o?.type!=="y"&&e.options.ordinal&&e.series.length&&(!t.touches||t.touches.length<=1)){let o,n,a=this.mouseDownX,l=e.getExtremes(),h=l.dataMin,d=l.dataMax,p=l.min,c=l.max,u=this.hoverPoints,g=e.closestPointRange||e.ordinal?.overscrollPointsRange,m=Math.round((a-s)/(e.translationSlope*(e.ordinal.slope||g))),f=e.ordinal.getExtendedPositions(),x={ordinal:{positions:f,extendedOrdinalPositions:f}},b=e.index2val,v=e.val2lin;if(p<=h&&m<=0||c>=d+i&&m>=0)return void t.preventDefault();x.ordinal.positions?Math.abs(m)>1&&(u&&u.forEach(function(t){t.setState()}),n=x.ordinal.positions,i&&(n=x.ordinal.positions=n.concat(e.ordinal.getOverscrollPositions())),d>n[n.length-1]&&n.push(d),this.setFixedRange(c-p),(o=e.navigatorAxis.toFixedRange(void 0,void 0,b.apply(x,[v.apply(x,[p,!0])+m]),b.apply(x,[v.apply(x,[c,!0])+m]))).min>=Math.min(n[0],p)&&o.max<=Math.max(n[n.length-1],c)+i&&e.setExtremes(o.min,o.max,!0,!1,{trigger:"pan"}),this.mouseDownX=s,eC(this.container,{cursor:"move"})):r=!0}else r=!0;r||o&&/y/.test(o.type)?i&&eP(e.dataMax)&&(e.max=e.dataMax+i):t.preventDefault()}function d(){let t=this.xAxis;t?.options.ordinal&&(delete t.ordinal.index,delete t.ordinal.originalOrdinalRange)}function p(t,e){let i,s=this.ordinal,r=s.positions,n=s.slope,a;if(!r)return t;let l=r.length;if(r[0]<=t&&r[l-1]>=t)i=o(r,t);else{if(a=s.getExtendedPositions?.(),!a?.length)return t;let l=a.length;n||(n=(a[l-1]-a[0])/l);let h=o(a,r[0]);if(t>=a[0]&&t<=a[l-1])i=o(a,t)-h;else{if(!e)return t;i=t<a[0]?-h-(a[0]-t)/n:(t-a[l-1])/n+l-h}}return e?i:n*(i||0)+s.offset}t.compose=function(t,o,c){let u=t.prototype;return u.ordinal2lin||(u.getTimeTicks=e,u.index2val=i,u.lin2val=s,u.val2lin=p,u.ordinal2lin=u.val2lin,eE(t,"afterInit",r),eE(t,"foundExtremes",n),eE(t,"afterSetScale",a),eE(t,"initialAxisTranslation",l),eE(c,"pan",h),eE(c,"touchpan",h),eE(o,"updatedData",d)),t},t.Additions=class{constructor(t){this.index={},this.axis=t}beforeSetTickPositions(){let t=this.axis,e=t.ordinal,i=t.getExtremes(),s=i.min,o=i.max,r=t.brokenAxis?.hasBreaks,n=t.options.ordinal,a=t.options.overscroll&&t.ordinal.convertOverscroll(t.options.overscroll)||0,l,h,d,p,c,u,g,m=[],f=Number.MAX_VALUE,x=!1,b=!1,v=!1;if(n||r){let i=0;if(t.series.forEach(function(t,e){let s=t.getColumn("x",!0);if(h=[],e>0&&"highcharts-navigator-series"!==t.options.id&&s.length>1&&(b=i!==s[1]-s[0]),i=s[1]-s[0],t.boosted&&(v=t.boosted),t.reserveSpace()&&(!1!==t.takeOrdinalPosition||r)&&(l=(m=m.concat(s)).length,m.sort(function(t,e){return t-e}),f=Math.min(f,eR(t.closestPointRange,f)),l)){for(e=0;e<l-1;)m[e]!==m[e+1]&&h.push(m[e+1]),e++;h[0]!==m[0]&&h.unshift(m[0]),m=h}}),t.ordinal.originalOrdinalRange||(t.ordinal.originalOrdinalRange=(m.length-1)*f),b&&v&&(m.pop(),m.shift()),(l=m.length)>2){for(d=m[1]-m[0],g=l-1;g--&&!x;)m[g+1]-m[g]!==d&&(x=!0);!t.options.keepOrdinalPadding&&(m[0]-s>d||o-a-m[l-1]>d)&&(x=!0)}else t.options.overscroll&&(2===l?f=m[1]-m[0]:1===l?(f=a,m=[m[0],m[0]+f]):f=e.overscrollPointsRange);x||t.forceOrdinal?(t.options.overscroll&&(e.overscrollPointsRange=f,m=m.concat(e.getOverscrollPositions())),e.positions=m,p=t.ordinal2lin(Math.max(s,m[0]),!0),c=Math.max(t.ordinal2lin(Math.min(o,m[m.length-1]),!0),1),e.slope=u=(o-s)/(c-p),e.offset=s-p*u):(e.overscrollPointsRange=eR(t.closestPointRange,e.overscrollPointsRange),e.positions=t.ordinal.slope=e.offset=void 0)}t.isOrdinal=n&&x,e.groupIntervalFactor=null}static findIndexOf(t,e,i){let s=0,o=t.length-1,r;for(;s<o;)t[r=Math.ceil((s+o)/2)]<=e?s=r:o=r-1;return t[s]===e||i?s:-1}getExtendedPositions(t=!0){let e=this,i=e.axis,s=i.constructor.prototype,o=i.chart,r=i.series.reduce((t,e)=>{let i=e.currentDataGrouping;return t+(i?i.count+i.unitName:"raw")},""),n=t?i.ordinal.convertOverscroll(i.options.overscroll):0,a=i.getExtremes(),l,h,d=e.index;return d||(d=e.index={}),!d[r]&&((l={series:[],chart:o,forceOrdinal:!1,getExtremes:function(){return{min:a.dataMin,max:a.dataMax+n}},applyGrouping:s.applyGrouping,getGroupPixelWidth:s.getGroupPixelWidth,getTimeTicks:s.getTimeTicks,options:{ordinal:!0},ordinal:{getGroupIntervalFactor:this.getGroupIntervalFactor},ordinal2lin:s.ordinal2lin,getIndexOfPoint:s.getIndexOfPoint,val2lin:s.val2lin}).ordinal.axis=l,i.series.forEach(i=>{if(!1===i.takeOrdinalPosition)return;h={xAxis:l,chart:o,groupPixelWidth:i.groupPixelWidth,destroyGroupedData:B().noop,getColumn:i.getColumn,applyGrouping:i.applyGrouping,getProcessedData:i.getProcessedData,reserveSpace:i.reserveSpace,visible:i.visible};let s=i.getColumn("x").concat(t?e.getOverscrollPositions():[]);h.dataTable=new eT({columns:{x:s}}),h.options={...i.options,dataGrouping:i.currentDataGrouping?{firstAnchor:i.options.dataGrouping?.firstAnchor,anchor:i.options.dataGrouping?.anchor,lastAnchor:i.options.dataGrouping?.firstAnchor,enabled:!0,forced:!0,approximation:"open",units:[[i.currentDataGrouping.unitName,[i.currentDataGrouping.count]]]}:{enabled:!1}},l.series.push(h),i.processData.apply(h)}),l.applyGrouping({hasExtremesChanged:!0}),h?.closestPointRange!==h?.basePointRange&&h.currentDataGrouping&&(l.forceOrdinal=!0),i.ordinal.beforeSetTickPositions.apply({axis:l}),!i.ordinal.originalOrdinalRange&&l.ordinal.originalOrdinalRange&&(i.ordinal.originalOrdinalRange=l.ordinal.originalOrdinalRange),l.ordinal.positions&&(d[r]=l.ordinal.positions)),d[r]}getGroupIntervalFactor(t,e,i){let s=i.getColumn("x",!0),o=s.length,r=[],n,a,l=this.groupIntervalFactor;if(!l){for(a=0;a<o-1;a++)r[a]=s[a+1]-s[a];r.sort(function(t,e){return t-e}),n=r[Math.floor(o/2)],t=Math.max(t,s[0]),e=Math.min(e,s[o-1]),this.groupIntervalFactor=l=o*n/(e-t)}return l}getIndexOfPoint(t,e){let i=this.axis,s=i.min,r=i.minPixelPadding;return o(e,s)+eO((t-r)/(i.translationSlope*(this.slope||i.closestPointRange||this.overscrollPointsRange)))}getOverscrollPositions(){let t=this.axis,e=this.convertOverscroll(t.options.overscroll),i=this.overscrollPointsRange,s=[],o=t.dataMax;if(eB(i))for(;o<t.dataMax+e;)s.push(o+=i);return s}postProcessTickInterval(t){let e,i=this.axis,s=this.slope,o=i.closestPointRange;return s&&o?i.options.breaks?o||t:t/(s/o):t}convertOverscroll(t=0){let e=this,i=e.axis,s=function(t){return eR(e.originalOrdinalRange,eB(i.dataMax)&&eB(i.dataMin)?i.dataMax-i.dataMin:0)*t};if(eI(t)){let e,o=parseInt(t,10);if(eB(i.min)&&eB(i.max)&&eB(i.dataMin)&&eB(i.dataMax)&&((e=i.max-i.min==i.dataMax-i.dataMin)||(this.originalOrdinalRange=i.max-i.min)),/%$/.test(t))return s(o/100);if(/px/.test(t)){let t=Math.min(o,.9*i.len)/i.len;return s(t/(e?1-t:1))}return 0}return t}}}(y||(y={}));let ez=y,eL={lang:{rangeSelectorZoom:"Zoom",rangeSelectorFrom:"",rangeSelectorTo:"→",rangeSelector:{allText:"All",allTitle:"View all",monthText:"{count}m",monthTitle:"View {count} {#eq count 1}month{else}months{/eq}",yearText:"{count}y",yearTitle:"View {count} {#eq count 1}year{else}years{/eq}",ytdText:"YTD",ytdTitle:"View year to date"}},rangeSelector:{allButtonsEnabled:!1,buttons:[{type:"month",count:1},{type:"month",count:3},{type:"month",count:6},{type:"ytd"},{type:"year",count:1},{type:"all"}],buttonSpacing:5,dropdown:"responsive",enabled:void 0,verticalAlign:"top",buttonTheme:{width:28,height:18,padding:2,zIndex:7},floating:!1,x:0,y:0,height:void 0,inputBoxBorderColor:"none",inputBoxHeight:17,inputBoxWidth:void 0,inputDateFormat:"%[ebY]",inputDateParser:void 0,inputEditDateFormat:"%Y-%m-%d",inputEnabled:!0,inputPosition:{align:"right",x:0,y:0},inputSpacing:5,selected:void 0,buttonPosition:{align:"left",x:0,y:0},inputStyle:{color:"#334eff",cursor:"pointer",fontSize:"0.8em"},labelStyle:{color:"#666666",fontSize:"0.8em"}}},{defaultOptions:eH}=B(),{composed:eW}=B(),{addEvent:eX,defined:eY,extend:eN,isNumber:eF,merge:eU,pick:e_,pushUnique:eV}=B(),eZ=[];function ej(){let t,e,i=this.range,s=i.type,o=this.max,r=this.chart.time,n=function(t,e){let i=r.toParts(t),o=i.slice();"year"===s?o[0]+=e:o[1]+=e;let n=r.makeTime.apply(r,o),a=r.toParts(n);return"month"===s&&i[1]===a[1]&&1===Math.abs(e)&&(o[0]=i[0],o[1]=i[1],o[2]=0),(n=r.makeTime.apply(r,o))-t};eF(i)?(t=o-i,e=i):i&&(t=o+n(o,-(i.count||1)),this.chart&&this.chart.setFixedRange(o-t));let a=e_(this.dataMin,Number.MIN_VALUE);return eF(t)||(t=a),t<=a&&(t=a,void 0===e&&(e=n(t,i.count)),this.newMax=Math.min(t+e,e_(this.dataMax,Number.MAX_VALUE))),eF(o)?!eF(i)&&i&&i._offsetMin&&(t+=i._offsetMin):t=void 0,t}function eq(){this.rangeSelector?.redrawElements()}function eK(){this.options.rangeSelector&&this.options.rangeSelector.enabled&&(this.rangeSelector=new g(this))}function e$(){let t=this.rangeSelector;if(t){eF(t.deferredYTDClick)&&(t.clickButton(t.deferredYTDClick),delete t.deferredYTDClick);let e=t.options.verticalAlign;t.options.floating||("bottom"===e?this.extraBottomMargin=!0:"top"===e&&(this.extraTopMargin=!0))}}function eJ(){let t,e=this.rangeSelector;if(!e)return;let i=this.xAxis[0].getExtremes(),s=this.legend,o=e&&e.options.verticalAlign;eF(i.min)&&e.render(i.min,i.max),s.display&&"top"===o&&o===s.options.verticalAlign&&(t=eU(this.spacingBox),"vertical"===s.options.layout?t.y=this.plotTop:t.y+=e.getHeight(),s.group.placed=!1,s.align(t))}function eQ(){for(let t=0,e=eZ.length;t<e;++t){let e=eZ[t];if(e[0]===this){e[1].forEach(t=>t()),eZ.splice(t,1);return}}}function e0(){let t=this.rangeSelector;if(t?.options?.enabled){let e=t.getHeight(),i=t.options.verticalAlign;t.options.floating||("bottom"===i?this.marginBottom+=e:"middle"!==i&&(this.plotTop+=e))}}function e1(t){let e=t.options.rangeSelector,i=this.extraBottomMargin,s=this.extraTopMargin,o=this.rangeSelector;if(e&&e.enabled&&!eY(o)&&this.options.rangeSelector&&(this.options.rangeSelector.enabled=!0,this.rangeSelector=o=new g(this)),this.extraBottomMargin=!1,this.extraTopMargin=!1,o){let t=e&&e.verticalAlign||o.options&&o.options.verticalAlign;o.options.floating||("bottom"===t?this.extraBottomMargin=!0:"middle"!==t&&(this.extraTopMargin=!0)),(this.extraBottomMargin!==i||this.extraTopMargin!==s)&&(this.isDirtyBox=!0)}}let e2={compose:function(t,e,i){if(g=i,eV(eW,"RangeSelector")){let i=e.prototype;t.prototype.minFromRange=ej,eX(e,"afterGetContainer",eK),eX(e,"beforeRender",e$),eX(e,"destroy",eQ),eX(e,"getMargins",e0),eX(e,"redraw",eJ),eX(e,"update",e1),eX(e,"beforeRedraw",eq),i.callbacks.push(eJ),eN(eH,{rangeSelector:eL.rangeSelector}),eN(eH.lang,eL.lang)}}};var e5=E(28),e3=E.n(e5),e6=E(984),e4=E.n(e6);let{defaultOptions:e8}=B(),{format:e9}=e4(),{addEvent:e7,createElement:it,css:ie,defined:ii,destroyObjectProperties:is,diffObjects:io,discardElement:ir,extend:ia,fireEvent:il,isNumber:ih,isString:id,merge:ip,objectEach:ic,pick:iu,splat:ig}=B();function im(t){let e=e=>RegExp(`%[[a-zA-Z]*${e}`).test(t);if(id(t)?-1!==t.indexOf("%L"):t.fractionalSecondDigits)return"text";let i=id(t)?["a","A","d","e","w","b","B","m","o","y","Y"].some(e):t.dateStyle||t.day||t.month||t.year,s=id(t)?["H","k","I","l","M","S"].some(e):t.timeStyle||t.hour||t.minute||t.second;return i&&s?"datetime-local":i?"date":s?"time":"text"}class ix{static compose(t,e){e2.compose(t,e,ix)}constructor(t){this.isDirty=!1,this.buttonOptions=[],this.initialButtonGroupWidth=0,this.maxButtonWidth=()=>{let t=0;return this.buttons.forEach(e=>{let i=e.getBBox();i.width>t&&(t=i.width)}),t},this.init(t)}clickButton(t,e){let i=this.chart,s=this.buttonOptions[t],o=i.xAxis[0],r=i.scroller&&i.scroller.getUnionExtremes()||o||{},n=s.type,a=s.dataGrouping,l=r.dataMin,h=r.dataMax,d,p=ih(o?.max)?Math.round(Math.min(o.max,h??o.max)):void 0,c,u=s._range,g,m,f,x=!0;if(null!==l&&null!==h){if(this.setSelected(t),a&&(this.forcedDataGrouping=!0,P().prototype.setDataGrouping.call(o||{chart:this.chart},a,!1),this.frozenStates=s.preserveDataGrouping),"month"===n||"year"===n)o?(m={range:s,max:p,chart:i,dataMin:l,dataMax:h},d=o.minFromRange.call(m),ih(m.newMax)&&(p=m.newMax),x=!1):u=s;else if(u)ih(p)&&(p=Math.min((d=Math.max(p-u,l))+u,h),x=!1);else if("ytd"===n)if(o)!o.hasData()||ih(h)&&ih(l)||(l=Number.MAX_VALUE,h=-Number.MAX_VALUE,i.series.forEach(t=>{let e=t.getColumn("x");e.length&&(l=Math.min(e[0],l),h=Math.max(e[e.length-1],h))}),e=!1),ih(h)&&ih(l)&&(d=g=(f=this.getYTDExtremes(h,l)).min,p=f.max);else{this.deferredYTDClick=t;return}else"all"===n&&o&&(i.navigator&&i.navigator.baseSeries[0]&&(i.navigator.baseSeries[0].xAxis.options.range=void 0),d=l,p=h);if(x&&s._offsetMin&&ii(d)&&(d+=s._offsetMin),s._offsetMax&&ii(p)&&(p+=s._offsetMax),this.dropdown&&(this.dropdown.selectedIndex=t+1),o)ih(d)&&ih(p)&&(o.setExtremes(d,p,iu(e,!0),void 0,{trigger:"rangeSelectorButton",rangeSelectorButton:s}),i.setFixedRange(s._range));else{c=ig(i.options.xAxis||{})[0];let t=e7(i,"afterCreateAxes",function(){let t=i.xAxis[0];t.range=t.options.range=u,t.min=t.options.min=g});e7(i,"load",function(){let e=i.xAxis[0];i.setFixedRange(s._range),e.options.range=c.range,e.options.min=c.min,t()})}il(this,"afterBtnClick")}}setSelected(t){this.selected=this.options.selected=t}init(t){let e=this,i=t.options.rangeSelector,s=t.options.lang,o=i.buttons,r=i.selected,n=function(){let t=e.minInput,i=e.maxInput;t&&t.blur&&il(t,"blur"),i&&i.blur&&il(i,"blur")};e.chart=t,e.options=i,e.buttons=[],e.buttonOptions=o.map(t=>(t.type&&s.rangeSelector&&(t.text??(t.text=s.rangeSelector[`${t.type}Text`]),t.title??(t.title=s.rangeSelector[`${t.type}Title`])),t.text=e9(t.text,{count:t.count||1}),t.title=e9(t.title,{count:t.count||1}),t)),this.eventsToUnbind=[],this.eventsToUnbind.push(e7(t.container,"mousedown",n)),this.eventsToUnbind.push(e7(t,"resize",n)),o.forEach(e.computeButtonRange),void 0!==r&&o[r]&&this.clickButton(r,!1),this.eventsToUnbind.push(e7(t,"load",function(){t.xAxis&&t.xAxis[0]&&e7(t.xAxis[0],"setExtremes",function(i){ih(this.max)&&ih(this.min)&&this.max-this.min!==t.fixedRange&&"rangeSelectorButton"!==i.trigger&&"updatedData"!==i.trigger&&e.forcedDataGrouping&&!e.frozenStates&&this.setDataGrouping(!1,!1)})})),this.createElements()}updateButtonStates(){let t=this,e=this.chart,i=this.dropdown,s=this.dropdownLabel,o=e.xAxis[0],r=Math.round(o.max-o.min),n=!o.hasVisibleSeries,a=24*36e5,l=e.scroller&&e.scroller.getUnionExtremes()||o,h=l.dataMin,d=l.dataMax,p=t.getYTDExtremes(d,h),c=p.min,u=p.max,g=t.selected,m=t.options.allButtonsEnabled,f=Array(t.buttonOptions.length).fill(0),x=ih(g),b=t.buttons,v=!1,y=null;t.buttonOptions.forEach((e,i)=>{let s=e._range,l=e.type,p=e.count||1,b=e._offsetMax-e._offsetMin,M=i===g,A=s>d-h,k=s<o.minRange,w=!1,S=s===r;if(M&&A&&(v=!0),o.isOrdinal&&o.ordinal?.positions&&s&&r<s){let t=o.ordinal.positions,e=ez.Additions.findIndexOf(t,o.min,!0),i=Math.min(ez.Additions.findIndexOf(t,o.max,!0)+1,t.length-1);t[i]-t[e]>s&&(S=!0)}else("month"===l||"year"===l)&&r+36e5>=({month:28,year:365})[l]*a*p-b&&r-36e5<=({month:31,year:366})[l]*a*p+b?S=!0:"ytd"===l?(S=u-c+b===r,w=!M):"all"===l&&(S=o.max-o.min>=d-h);let T=!m&&!(v&&"all"===l)&&(A||k||n),E=v&&"all"===l||!w&&S||M&&t.frozenStates;T?f[i]=3:E&&(!x||i===g)&&(y=i)}),null!==y?(f[y]=2,t.setSelected(y),this.dropdown&&(this.dropdown.selectedIndex=y+1)):(t.setSelected(),this.dropdown&&(this.dropdown.selectedIndex=-1),s&&(s.setState(0),s.attr({text:(e8.lang.rangeSelectorZoom||"")+" ▾"})));for(let e=0;e<f.length;e++){let o=f[e],r=b[e];if(r.state!==o&&(r.setState(o),i)){i.options[e+1].disabled=3===o,2===o&&(s&&(s.setState(2),s.attr({text:t.buttonOptions[e].text+" ▾"})),i.selectedIndex=e+1);let r=s.getBBox();ie(i,{width:`${r.width}px`,height:`${r.height}px`})}}}computeButtonRange(t){let e=t.type,i=t.count||1,s={millisecond:1,second:1e3,minute:6e4,hour:36e5,day:864e5,week:6048e5};s[e]?t._range=s[e]*i:("month"===e||"year"===e)&&(t._range=24*({month:30,year:365})[e]*36e5*i),t._offsetMin=iu(t.offsetMin,0),t._offsetMax=iu(t.offsetMax,0),t._range+=t._offsetMax-t._offsetMin}getInputValue(t){let e="min"===t?this.minInput:this.maxInput,i=this.chart.options.rangeSelector,s=this.chart.time;return e?("text"===e.type&&i.inputDateParser||this.defaultInputDateParser)(e.value,"UTC"===s.timezone,s):0}setInputValue(t,e){let i=this.options,s=this.chart.time,o="min"===t?this.minInput:this.maxInput,r="min"===t?this.minDateBox:this.maxDateBox;if(o){o.setAttribute("type",im(i.inputDateFormat||"%e %b %Y"));let t=o.getAttribute("data-hc-time"),n=ii(t)?Number(t):void 0;if(ii(e)){let t=n;ii(t)&&o.setAttribute("data-hc-time-previous",t),o.setAttribute("data-hc-time",e),n=e}o.value=s.dateFormat(this.inputTypeFormats[o.type]||i.inputEditDateFormat,n),r&&r.attr({text:s.dateFormat(i.inputDateFormat,n)})}}setInputExtremes(t,e,i){let s="min"===t?this.minInput:this.maxInput;if(s){let t=this.inputTypeFormats[s.type],o=this.chart.time;if(t){let r=o.dateFormat(t,e);s.min!==r&&(s.min=r);let n=o.dateFormat(t,i);s.max!==n&&(s.max=n)}}}showInput(t){let e="min"===t?this.minDateBox:this.maxDateBox,i="min"===t?this.minInput:this.maxInput;if(i&&e&&this.inputGroup){let t="text"===i.type,{translateX:s=0,translateY:o=0}=this.inputGroup,{x:r=0,width:n=0,height:a=0}=e,{inputBoxWidth:l}=this.options;ie(i,{width:t?n+(l?-2:20)+"px":"auto",height:a-2+"px",border:"2px solid silver"}),t&&l?ie(i,{left:s+r+"px",top:o+"px"}):ie(i,{left:Math.min(Math.round(r+s-(i.offsetWidth-n)/2),this.chart.chartWidth-i.offsetWidth)+"px",top:o-(i.offsetHeight-a)/2+"px"})}}hideInput(t){let e="min"===t?this.minInput:this.maxInput;e&&ie(e,{top:"-9999em",border:0,width:"1px",height:"1px"})}defaultInputDateParser(t,e,i){return i?.parse(t)||0}drawInput(t){let{chart:e,div:i,inputGroup:s}=this,o=this,r=e.renderer.style||{},n=e.renderer,a=e.options.rangeSelector,l=e8.lang,h="min"===t;function d(t){let{maxInput:i,minInput:s}=o,r=e.xAxis[0],n=e.scroller?.getUnionExtremes()||r,a=n.dataMin,l=n.dataMax,d=e.xAxis[0].getExtremes()[t],p=o.getInputValue(t);ih(p)&&p!==d&&(h&&i&&ih(a)?p>Number(i.getAttribute("data-hc-time"))?p=void 0:p<a&&(p=a):s&&ih(l)&&(p<Number(s.getAttribute("data-hc-time"))?p=void 0:p>l&&(p=l)),void 0!==p&&r.setExtremes(h?p:r.min,h?r.max:p,void 0,void 0,{trigger:"rangeSelectorInput"}))}let p=l[h?"rangeSelectorFrom":"rangeSelectorTo"]||"",c=n.label(p,0).addClass("highcharts-range-label").attr({padding:2*!!p,height:p?a.inputBoxHeight:0}).add(s),u=n.label("",0).addClass("highcharts-range-input").attr({padding:2,width:a.inputBoxWidth,height:a.inputBoxHeight,"text-align":"center"}).on("click",function(){o.showInput(t),o[t+"Input"].focus()});e.styledMode||u.attr({stroke:a.inputBoxBorderColor,"stroke-width":1}),u.add(s);let g=it("input",{name:t,className:"highcharts-range-selector"},void 0,i);g.setAttribute("type",im(a.inputDateFormat||"%e %b %Y")),e.styledMode||(c.css(ip(r,a.labelStyle)),u.css(ip({color:"#333333"},r,a.inputStyle)),ie(g,ia({position:"absolute",border:0,boxShadow:"0 0 15px rgba(0,0,0,0.3)",width:"1px",height:"1px",padding:0,textAlign:"center",fontSize:r.fontSize,fontFamily:r.fontFamily,top:"-9999em"},a.inputStyle))),g.onfocus=()=>{o.showInput(t)},g.onblur=()=>{g===B().doc.activeElement&&d(t),o.hideInput(t),o.setInputValue(t),g.blur()};let m=!1;return g.onchange=()=>{m||(d(t),o.hideInput(t),g.blur())},g.onkeypress=e=>{13===e.keyCode&&d(t)},g.onkeydown=e=>{m=!0,("ArrowUp"===e.key||"ArrowDown"===e.key||"Tab"===e.key)&&d(t)},g.onkeyup=()=>{m=!1},{dateBox:u,input:g,label:c}}getPosition(){let t=this.chart,e=t.options.rangeSelector,i="top"===e.verticalAlign?t.plotTop-t.axisOffset[0]:0;return{buttonTop:i+e.buttonPosition.y,inputTop:i+e.inputPosition.y-10}}getYTDExtremes(t,e){let i=this.chart.time,s=i.toParts(t)[0];return{max:t,min:Math.max(e,i.makeTime(s,0))}}createElements(){let t=this.chart,e=t.renderer,i=t.container,s=t.options,o=s.rangeSelector,r=o.inputEnabled,n=iu(s.chart.style?.zIndex,0)+1;!1!==o.enabled&&(this.group=e.g("range-selector-group").attr({zIndex:7}).add(),this.div=it("div",void 0,{position:"relative",height:0,zIndex:n}),this.buttonOptions.length&&this.renderButtons(),i.parentNode&&i.parentNode.insertBefore(this.div,i),r&&this.createInputs())}createInputs(){this.inputGroup=this.chart.renderer.g("input-group").add(this.group);let t=this.drawInput("min");this.minDateBox=t.dateBox,this.minLabel=t.label,this.minInput=t.input;let e=this.drawInput("max");this.maxDateBox=e.dateBox,this.maxLabel=e.label,this.maxInput=e.input}render(t,e){if(!1===this.options.enabled)return;let i=this.chart,s=i.options.rangeSelector;if(s.inputEnabled){this.inputGroup||this.createInputs(),this.setInputValue("min",t),this.setInputValue("max",e),this.chart.styledMode||(this.maxLabel?.css(s.labelStyle),this.minLabel?.css(s.labelStyle));let o=i.scroller&&i.scroller.getUnionExtremes()||i.xAxis[0]||{};if(ii(o.dataMin)&&ii(o.dataMax)){let t=i.xAxis[0].minRange||0;this.setInputExtremes("min",o.dataMin,Math.min(o.dataMax,this.getInputValue("max"))-t),this.setInputExtremes("max",Math.max(o.dataMin,this.getInputValue("min"))+t,o.dataMax)}if(this.inputGroup){let t=0;[this.minLabel,this.minDateBox,this.maxLabel,this.maxDateBox].forEach(e=>{if(e){let{width:i}=e.getBBox();i&&(e.attr({x:t}),t+=i+s.inputSpacing)}})}}else this.inputGroup&&(this.inputGroup.destroy(),delete this.inputGroup);!this.chart.styledMode&&this.zoomText&&this.zoomText.css(s.labelStyle),this.alignElements(),this.updateButtonStates()}renderButtons(){var t;let{chart:e,options:i}=this,s=e8.lang,o=e.renderer,r=ip(i.buttonTheme),n=r&&r.states;delete r.width,delete r.states,this.buttonGroup=o.g("range-selector-buttons").add(this.group);let a=this.dropdown=it("select",void 0,{position:"absolute",padding:0,border:0,cursor:"pointer",opacity:1e-4},this.div),l=e.userOptions.rangeSelector?.buttonTheme;this.dropdownLabel=o.button("",0,0,()=>{},ip(r,{"stroke-width":iu(r["stroke-width"],0),width:"auto",paddingLeft:iu(i.buttonTheme.paddingLeft,l?.padding,8),paddingRight:iu(i.buttonTheme.paddingRight,l?.padding,8)}),n&&n.hover,n&&n.select,n&&n.disabled).hide().add(this.group),e7(a,"touchstart",()=>{a.style.fontSize="16px"});let h=B().isMS?"mouseover":"mouseenter",d=B().isMS?"mouseout":"mouseleave";e7(a,h,()=>{il(this.dropdownLabel.element,h)}),e7(a,d,()=>{il(this.dropdownLabel.element,d)}),e7(a,"change",()=>{il(this.buttons[a.selectedIndex-1].element,"click")}),this.zoomText=o.label(s.rangeSelectorZoom||"",0).attr({padding:i.buttonTheme.padding,height:i.buttonTheme.height,paddingLeft:0,paddingRight:0}).add(this.buttonGroup),this.chart.styledMode||(this.zoomText.css(i.labelStyle),(t=i.buttonTheme)["stroke-width"]??(t["stroke-width"]=0)),it("option",{textContent:this.zoomText.textStr,disabled:!0},void 0,a),this.createButtons()}createButtons(){let{options:t}=this,e=ip(t.buttonTheme),i=e&&e.states,s=e.width||28;delete e.width,delete e.states,this.buttonOptions.forEach((t,e)=>{this.createButton(t,e,s,i)})}createButton(t,e,i,s){let{dropdown:o,buttons:r,chart:n,options:a}=this,l=n.renderer,h=ip(a.buttonTheme);o?.add(it("option",{textContent:t.title||t.text}),e+2),r[e]=l.button(t.text??"",0,0,i=>{let s,o=t.events&&t.events.click;o&&(s=o.call(t,i)),!1!==s&&this.clickButton(e),this.isActive=!0},h,s&&s.hover,s&&s.select,s&&s.disabled).attr({"text-align":"center",width:i}).add(this.buttonGroup),t.title&&r[e].attr("title",t.title)}alignElements(){let{buttonGroup:t,buttons:e,chart:i,group:s,inputGroup:o,options:r,zoomText:n}=this,a=i.options,l=a.exporting&&!1!==a.exporting.enabled&&a.navigation&&a.navigation.buttonOptions,{buttonPosition:h,inputPosition:d,verticalAlign:p}=r,c=(t,e,s)=>l&&this.titleCollision(i)&&"top"===p&&s&&e.y-t.getBBox().height-12<(l.y||0)+(l.height||0)+i.spacing[0]?-40:0,u=i.plotLeft;if(s&&h&&d){let a=h.x-i.spacing[3];if(t){if(this.positionButtons(),!this.initialButtonGroupWidth){let t=0;n&&(t+=n.getBBox().width+5),e.forEach((i,s)=>{t+=i.width||0,s!==e.length-1&&(t+=r.buttonSpacing)}),this.initialButtonGroupWidth=t}u-=i.spacing[3];let o=c(t,h,"right"===h.align||"right"===d.align);this.alignButtonGroup(o),this.buttonGroup?.translateY&&this.dropdownLabel.attr({y:this.buttonGroup.translateY}),s.placed=t.placed=i.hasLoaded}let l=0;r.inputEnabled&&o&&(l=c(o,d,"right"===h.align||"right"===d.align),"left"===d.align?a=u:"right"===d.align&&(a=-Math.max(i.axisOffset[1],-l)),o.align({y:d.y,width:o.getBBox().width,align:d.align,x:d.x+a-2},!0,i.spacingBox),o.placed=i.hasLoaded),this.handleCollision(l),s.align({verticalAlign:p},!0,i.spacingBox);let g=s.alignAttr.translateY,m=s.getBBox().height+20,f=0;if("bottom"===p){let t=i.legend&&i.legend.options;f=g-(m=m+(t&&"bottom"===t.verticalAlign&&t.enabled&&!t.floating?i.legend.legendHeight+iu(t.margin,10):0)-20)-(r.floating?0:r.y)-(i.titleOffset?i.titleOffset[2]:0)-10}"top"===p?(r.floating&&(f=0),i.titleOffset&&i.titleOffset[0]&&(f=i.titleOffset[0]),f+=i.margin[0]-i.spacing[0]||0):"middle"===p&&(d.y===h.y?f=g:(d.y||h.y)&&(d.y<0||h.y<0?f-=Math.min(d.y,h.y):f=g-m)),s.translate(r.x,r.y+Math.floor(f));let{minInput:x,maxInput:b,dropdown:v}=this;r.inputEnabled&&x&&b&&(x.style.marginTop=s.translateY+"px",b.style.marginTop=s.translateY+"px"),v&&(v.style.marginTop=s.translateY+"px")}}redrawElements(){let t=this.chart,{inputBoxHeight:e,inputBoxBorderColor:i}=this.options;if(this.maxDateBox?.attr({height:e}),this.minDateBox?.attr({height:e}),t.styledMode||(this.maxDateBox?.attr({stroke:i}),this.minDateBox?.attr({stroke:i})),this.isDirty){this.isDirty=!1,this.isCollapsed=void 0;let t=this.options.buttons??[],e=Math.min(t.length,this.buttonOptions.length),{dropdown:i,options:s}=this,o=ip(s.buttonTheme),r=o&&o.states,n=o.width||28;if(t.length<this.buttonOptions.length)for(let e=this.buttonOptions.length-1;e>=t.length;e--){let t=this.buttons.pop();t?.destroy(),this.dropdown?.options.remove(e+1)}for(let s=e-1;s>=0;s--)if(0!==Object.keys(io(t[s],this.buttonOptions[s])).length){let e=t[s];this.buttons[s].destroy(),i?.options.remove(s+1),this.createButton(e,s,n,r),this.computeButtonRange(e)}if(t.length>this.buttonOptions.length)for(let e=this.buttonOptions.length;e<t.length;e++)this.createButton(t[e],e,n,r),this.computeButtonRange(t[e]);this.buttonOptions=this.options.buttons??[],ii(this.options.selected)&&this.buttons.length&&this.clickButton(this.options.selected,!1)}}alignButtonGroup(t,e){let{chart:i,options:s,buttonGroup:o,dropdown:r,dropdownLabel:n}=this,{buttonPosition:a}=s,l=i.plotLeft-i.spacing[3],h=a.x-i.spacing[3],d=i.plotLeft;"right"===a.align?(h+=t-l,this.hasVisibleDropdown&&(d=i.chartWidth+t-this.maxButtonWidth()-20)):"center"===a.align&&(h-=l/2,this.hasVisibleDropdown&&(d=i.chartWidth/2-this.maxButtonWidth())),r&&ie(r,{left:d+"px",top:o?.translateY+"px"}),n?.attr({x:d}),o&&o.align({y:a.y,width:iu(e,this.initialButtonGroupWidth),align:a.align,x:h},!0,i.spacingBox)}positionButtons(){let{buttons:t,chart:e,options:i,zoomText:s}=this,o=e.hasLoaded?"animate":"attr",{buttonPosition:r}=i,n=e.plotLeft,a=n;s&&"hidden"!==s.visibility&&(s[o]({x:iu(n+r.x,n)}),a+=r.x+s.getBBox().width+5);for(let e=0,s=this.buttonOptions.length;e<s;++e)"hidden"!==t[e].visibility?(t[e][o]({x:a}),a+=(t[e].width||0)+i.buttonSpacing):t[e][o]({x:n})}handleCollision(t){let{chart:e,buttonGroup:i,inputGroup:s,initialButtonGroupWidth:o}=this,{buttonPosition:r,dropdown:n,inputPosition:a}=this.options,l=()=>{s&&i&&s.attr({translateX:s.alignAttr.translateX+(e.axisOffset[1]>=-t?0:-t),translateY:s.alignAttr.translateY+i.getBBox().height+10})};s&&i?a.align===r.align?(l(),o>e.plotWidth+t-20?this.collapseButtons():this.expandButtons()):o-t+s.getBBox().width>e.plotWidth?"responsive"===n?this.collapseButtons():l():this.expandButtons():i&&"responsive"===n&&(o>e.plotWidth?this.collapseButtons():this.expandButtons()),i&&("always"===n&&this.collapseButtons(),"never"===n&&this.expandButtons()),this.alignButtonGroup(t)}collapseButtons(){let{buttons:t,zoomText:e}=this;!0!==this.isCollapsed&&(this.isCollapsed=!0,e.hide(),t.forEach(t=>void t.hide()),this.showDropdown())}expandButtons(){let{buttons:t,zoomText:e}=this;!1!==this.isCollapsed&&(this.isCollapsed=!1,this.hideDropdown(),e.show(),t.forEach(t=>void t.show()),this.positionButtons())}showDropdown(){let{buttonGroup:t,dropdownLabel:e,dropdown:i}=this;t&&i&&(e.show(),ie(i,{visibility:"inherit"}),this.hasVisibleDropdown=!0)}hideDropdown(){let{dropdown:t}=this;t&&(this.dropdownLabel.hide(),ie(t,{visibility:"hidden",width:"1px",height:"1px"}),this.hasVisibleDropdown=!1)}getHeight(){let t=this.options,e=this.group,i=t.inputPosition,s=t.buttonPosition,o=t.y,r=s.y,n=i.y,a=0;if(t.height)return t.height;this.alignElements(),a=e?e.getBBox(!0).height+13+o:0;let l=Math.min(n,r);return(n<0&&r<0||n>0&&r>0)&&(a+=Math.abs(l)),a}titleCollision(t){return!(t.options.title.text||t.options.subtitle.text)}update(t,e=!0){let i=this.chart;if(ip(!0,this.options,t),this.options.selected&&this.options.selected>=this.options.buttons.length&&(this.options.selected=void 0,i.options.rangeSelector.selected=void 0),ii(t.enabled))return this.destroy(),this.init(i);this.isDirty=!!t.buttons,e&&this.render()}destroy(){let t=this,e=t.minInput,i=t.maxInput;t.eventsToUnbind&&(t.eventsToUnbind.forEach(t=>t()),t.eventsToUnbind=void 0),is(t.buttons),e&&(e.onfocus=e.onblur=e.onchange=null),i&&(i.onfocus=i.onblur=i.onchange=null),ic(t,function(e,i){e&&"chart"!==i&&(e instanceof e3()?e.destroy():e instanceof window.HTMLElement&&ir(e),delete t[i]),e!==ix.prototype[i]&&(t[i]=null)},this),this.buttons=[]}}ia(ix.prototype,{inputTypeFormats:{"datetime-local":"%Y-%m-%dT%H:%M:%S",date:"%Y-%m-%d",time:"%H:%M:%S"}});var ib=E(960),iv=E.n(ib);let{format:iy}=e4(),{getOptions:iM}=B(),{setFixedRange:iA}=tP,{addEvent:ik,clamp:iw,crisp:iS,defined:iT,extend:iE,find:iO,isNumber:iC,isString:iB,merge:iD,pick:iP,splat:iR}=B();function iG(t,e,i){return"xAxis"===t?{minPadding:0,maxPadding:0,overscroll:0,ordinal:!0}:"yAxis"===t?{labels:{y:-2},opposite:i.opposite??e.opposite??!0,showLastLabel:!!(e.categories||"category"===e.type),title:{text:void 0}}:{}}function iI(t,e){if("xAxis"===t){let t=iP(e.navigator?.enabled,tA.enabled,!0),i={type:"datetime",categories:void 0};return t&&(i.startOnTick=!1,i.endOnTick=!1),i}return{}}class iz extends iv(){init(t,e){let i=iM(),s=t.xAxis,o=t.yAxis,r=iP(t.navigator?.enabled,tA.enabled,!0);t.xAxis=t.yAxis=void 0;let n=iD({chart:{panning:{enabled:!0,type:"x"},zooming:{pinchType:"x",mouseWheel:{type:"x"}}},navigator:{enabled:r},scrollbar:{enabled:iP(tj.enabled,!0)},rangeSelector:{enabled:iP(eL.rangeSelector.enabled,!0)},title:{text:null},tooltip:{split:iP(i.tooltip?.split,!0),crosshairs:!0},legend:{enabled:!1}},t,{isStock:!0});t.xAxis=s,t.yAxis=o,n.xAxis=iR(t.xAxis||{}).map(e=>iD(iG("xAxis",e,i.xAxis),e,iI("xAxis",t))),n.yAxis=iR(t.yAxis||{}).map(t=>iD(iG("yAxis",t,i.yAxis),t)),super.init(n,e)}createAxis(t,e){return e.axis=iD(iG(t,e.axis,iM()[t]),e.axis,iI(t,this.userOptions)),super.createAxis(t,e)}}ik(iv(),"update",function(t){let e=t.options;"scrollbar"in e&&this.navigator&&(iD(!0,this.options.scrollbar,e.scrollbar),this.navigator.update({enabled:!!this.navigator.navigatorEnabled}),delete e.scrollbar)}),function(t){function e(t){if(!(this.crosshair?.label?.enabled&&this.cross&&iC(this.min)&&iC(this.max)))return;let e=this.chart,i=this.logarithmic,s=this.crosshair.label,o=this.horiz,r=this.opposite,n=this.left,a=this.top,l=this.width,h="inside"===this.options.tickPosition,d=!1!==this.crosshair.snap,p=t.e||this.cross?.e,c=t.point,u=this.crossLabel,g,m,f=s.format,x="",b,v=0,y=this.min,M=this.max;i&&(y=i.lin2log(this.min),M=i.lin2log(this.max));let A=o?"center":r?"right"===this.labelAlign?"right":"left":"left"===this.labelAlign?"left":"center";!u&&(u=this.crossLabel=e.renderer.label("",0,void 0,s.shape||"callout").addClass("highcharts-crosshair-label highcharts-color-"+(c?.series?c.series.colorIndex:this.series[0]&&this.series[0].colorIndex)).attr({align:s.align||A,padding:iP(s.padding,8),r:iP(s.borderRadius,3),zIndex:2}).add(this.labelGroup),e.styledMode||u.attr({fill:s.backgroundColor||c?.series?.color||"#666666",stroke:s.borderColor||"","stroke-width":s.borderWidth||0}).css(iE({color:"#ffffff",fontWeight:"normal",fontSize:"0.7em",textAlign:"center"},s.style||{}))),o?(g=d?(c.plotX||0)+n:p.chartX,m=a+(r?0:this.height)):(g=n+this.offset+(r?l:0),m=d?(c.plotY||0)+a:p.chartY),f||s.formatter||(this.dateTime&&(x="%b %d, %Y"),f="{value"+(x?":"+x:"")+"}");let k=d?this.isXAxis?c.x:c.y:this.toValue(o?p.chartX:p.chartY),w=c?.series?c.series.isPointInside(c):iC(k)&&k>y&&k<M,S="";f?S=iy(f,{value:k},e):s.formatter&&iC(k)&&(S=s.formatter.call(this,k)),u.attr({text:S,x:g,y:m,visibility:w?"inherit":"hidden"});let T=u.getBBox();!iC(u.x)||o||r||(g=u.x-T.width/2),iC(u.y)&&(o?(h&&!r||!h&&r)&&(m=u.y-T.height):m=u.y-T.height/2),b=o?{left:n,right:n+this.width}:{left:"left"===this.labelAlign?n:0,right:"right"===this.labelAlign?n+this.width:e.chartWidth};let E=u.translateX||0;E<b.left&&(v=b.left-E),E+T.width>=b.right&&(v=-(E+T.width-b.right)),u.attr({x:Math.max(0,g+v),y:Math.max(0,m),anchorX:o?g:this.opposite?0:e.chartWidth,anchorY:o?this.opposite?e.chartHeight:0:m+T.height/2})}function i(){this.crossLabel&&(this.crossLabel=this.crossLabel.hide())}function s(t){let e=this.chart,i=this.options,s=e._labelPanes=e._labelPanes||{},o=i.labels;if(e.options.isStock&&"yAxis"===this.coll){let e=i.top+","+i.height;!s[e]&&o.enabled&&(15===o.distance&&1===this.side&&(o.distance=0),void 0===o.align&&(o.align="right"),s[e]=this,t.align="right",t.preventDefault())}}function o(){let t=this.chart,e=this.options&&this.options.top+","+this.options.height;e&&t._labelPanes&&t._labelPanes[e]===this&&delete t._labelPanes[e]}function r(t){let e=this,i=e.isLinked&&!e.series&&e.linkedParent?e.linkedParent.series:e.series,s=e.chart,o=s.renderer,r=e.left,n=e.top,a=[],l=t.translatedValue,h=t.value,d=t.force,p,c,u,g,m=[],f,x;if(s.options.isStock&&!1!==t.acrossPanes&&"xAxis"===e.coll||"yAxis"===e.coll){for(let o of(t.preventDefault(),m=(t=>{let o="xAxis"===t?"yAxis":"xAxis",r=e.options[o];return iC(r)?[s[o][r]]:iB(r)?[s.get(r)]:i.map(t=>t[o])})(e.coll),e.isXAxis?s.yAxis:s.xAxis))if(!o.options.isInternal){let t=o.isXAxis?"yAxis":"xAxis";e===(iT(o.options[t])?s[t][o.options[t]]:s[t][0])&&m.push(o)}for(let t of(f=m.length?[]:[e.isXAxis?s.yAxis[0]:s.xAxis[0]],m))-1!==f.indexOf(t)||iO(f,e=>e.pos===t.pos&&e.len===t.len)||f.push(t);if(iC(x=iP(l,e.translate(h||0,void 0,void 0,t.old))))if(e.horiz)for(let t of f){let i;g=(c=t.pos)+t.len,p=u=Math.round(x+e.transB),"pass"!==d&&(p<r||p>r+e.width)&&(d?p=u=iw(p,r,r+e.width):i=!0),i||a.push(["M",p,c],["L",u,g])}else for(let t of f){let i;u=(p=t.pos)+t.len,c=g=n+e.height-x,"pass"!==d&&(c<n||c>n+e.height)&&(d?c=g=iw(c,n,n+e.height):i=!0),i||a.push(["M",p,c],["L",u,g])}t.path=a.length>0?o.crispPolyLine(a,t.lineWidth||1):void 0}}function n(t){if(this.chart.options.isStock){let e;this.is("column")||this.is("columnrange")?e={borderWidth:0,shadow:!1}:this.is("scatter")||this.is("sma")||(e={marker:{enabled:!1,radius:2}}),e&&(t.plotOptions[this.type]=iD(t.plotOptions[this.type],e))}}function a(){let t=this.chart,e=this.options.dataGrouping;return!1!==this.allowDG&&e&&iP(e.enabled,t.options.isStock)}function l(t,e){for(let i=0;i<t.length;i+=2){let s=t[i],o=t[i+1];iT(s[1])&&s[1]===o[1]&&(s[1]=o[1]=iS(s[1],e)),iT(s[2])&&s[2]===o[2]&&(s[2]=o[2]=iS(s[2],e))}return t}t.compose=function(t,h,d,p){let c=d.prototype;c.forceCropping||(ik(h,"afterDrawCrosshair",e),ik(h,"afterHideCrosshair",i),ik(h,"autoLabelAlign",s),ik(h,"destroy",o),ik(h,"getPlotLinePath",r),t.prototype.setFixedRange=iA,c.forceCropping=a,ik(d,"setOptions",n),p.prototype.crispPolyLine=l)},t.stockChart=function(e,i,s){return new t(e,i,s)}}(iz||(iz={}));let iL=iz,{column:{prototype:{pointClass:iH}}}=tv().seriesTypes,{column:iW}=tv().seriesTypes,{crisp:iX,extend:iY,merge:iN}=B(),{defaultOptions:iF}=B();class iU extends iW{extendStem(t,e,i){let s=t[0],o=t[1];"number"==typeof s[2]&&(s[2]=Math.max(i+e,s[2])),"number"==typeof o[2]&&(o[2]=Math.min(i-e,o[2]))}getPointPath(t,e){let i=e.strokeWidth(),s=t.series,o=iX(t.plotX||0,i),r=Math.round(t.shapeArgs.width/2),n=[["M",o,Math.round(t.yBottom)],["L",o,Math.round(t.plotHigh)]];if(null!==t.close){let e=iX(t.plotClose,i);n.push(["M",o,e],["L",o+r,e]),s.extendStem(n,i/2,e)}return n}drawSinglePoint(t){let e=t.series,i=e.chart,s,o=t.graphic;void 0!==t.plotY&&(o||(t.graphic=o=i.renderer.path().add(e.group)),i.styledMode||o.attr(e.pointAttribs(t,t.selected&&"select")),s=e.getPointPath(t,o),o[!o?"attr":"animate"]({d:s}).addClass(t.getClassName(),!0))}drawPoints(){this.points.forEach(this.drawSinglePoint)}init(){super.init.apply(this,arguments),this.options.stacking=void 0}pointAttribs(t,e){let i=super.pointAttribs.call(this,t,e);return delete i.fill,i}toYData(t){return[t.high,t.low,t.close]}translate(){let t=this,e=t.yAxis,i=this.pointArrayMap&&this.pointArrayMap.slice()||[],s=i.map(t=>`plot${t.charAt(0).toUpperCase()+t.slice(1)}`);s.push("yBottom"),i.push("low"),super.translate.apply(t),t.points.forEach(function(o){i.forEach(function(i,r){let n=o[i];null!==n&&(t.dataModify&&(n=t.dataModify.modifyValue(n)),o[s[r]]=e.toPixels(n,!0))}),o.tooltipPos[1]=o.plotHigh+e.pos-t.chart.plotTop})}}iU.defaultOptions=iN(iW.defaultOptions,{lineWidth:1,tooltip:{pointFormat:'<span style="color:{point.color}">●</span> <b> {series.name}</b><br/>{series.chart.options.lang.stockHigh}: {point.high}<br/>{series.chart.options.lang.stockLow}: {point.low}<br/>{series.chart.options.lang.stockClose}: {point.close}<br/>'},threshold:null,states:{hover:{lineWidth:3}},stickyTracking:!0}),iY(iU.prototype,{pointClass:class extends iH{},animate:null,directTouch:!1,keysAffectYAxis:["low","high"],pointArrayMap:["high","low","close"],pointAttrToOptions:{stroke:"color","stroke-width":"lineWidth"},pointValKey:"close"}),iY(iF.lang,{stockOpen:"Open",stockHigh:"High",stockLow:"Low",stockClose:"Close"}),tv().registerSeriesType("hlc",iU);let{seriesTypes:{hlc:i_}}=tv();class iV extends i_.prototype.pointClass{getClassName(){return super.getClassName.call(this)+(this.open<this.close?" highcharts-point-up":" highcharts-point-down")}resolveUpColor(){this.open<this.close&&!this.options.color&&this.series.options.upColor&&(this.color=this.series.options.upColor)}resolveColor(){super.resolveColor(),this.series.is("heikinashi")||this.resolveUpColor()}getZone(){let t=super.getZone();return this.resolveUpColor(),t}applyOptions(){return super.applyOptions.apply(this,arguments),this.resolveColor&&this.resolveColor(),this}}let{composed:iZ}=B(),{hlc:ij}=tv().seriesTypes,{addEvent:iq,crisp:iK,extend:i$,merge:iJ,pushUnique:iQ}=B();function i0(t){let e=t.options,i=e.dataGrouping;i&&e.useOhlcData&&"highcharts-navigator-series"!==e.id&&(i.approximation="ohlc")}function i1(t){let e=t.options;e.useOhlcData&&"highcharts-navigator-series"!==e.id&&i$(this,{pointValKey:i2.prototype.pointValKey,pointArrayMap:i2.prototype.pointArrayMap,toYData:i2.prototype.toYData})}class i2 extends ij{static compose(t,...e){iQ(iZ,"OHLCSeries")&&(iq(t,"afterSetOptions",i0),iq(t,"init",i1))}getPointPath(t,e){let i=super.getPointPath(t,e),s=e.strokeWidth(),o=iK(t.plotX||0,s),r=Math.round(t.shapeArgs.width/2);if(null!==t.open){let e=iK(t.plotOpen,s);i.push(["M",o,e],["L",o-r,e]),super.extendStem(i,s/2,e)}return i}pointAttribs(t,e){let i=super.pointAttribs.call(this,t,e),s=this.options;return delete i.fill,!t.options.color&&s.upColor&&t.open<t.close&&(i.stroke=s.upColor),i}toYData(t){return[t.open,t.high,t.low,t.close]}}i2.defaultOptions=iJ(ij.defaultOptions,{tooltip:{pointFormat:'<span style="color:{point.color}">●</span> <b> {series.name}</b><br/>{series.chart.options.lang.stockOpen}: {point.open}<br/>{series.chart.options.lang.stockHigh}: {point.high}<br/>{series.chart.options.lang.stockLow}: {point.low}<br/>{series.chart.options.lang.stockClose}: {point.close}<br/>'}}),i$(i2.prototype,{pointClass:iV,pointArrayMap:["open","high","low","close"]}),tv().registerSeriesType("ohlc",i2);let{column:i5,ohlc:i3}=tv().seriesTypes,{crisp:i6,merge:i4}=B();class i8 extends i3{pointAttribs(t,e){let i=i5.prototype.pointAttribs.call(this,t,e),s=this.options,o=t.open<t.close,r=s.lineColor||this.color,n=t.color||this.color;if(i["stroke-width"]=s.lineWidth,i.fill=t.options.color||o&&s.upColor||n,i.stroke=t.options.lineColor||o&&s.upLineColor||r,e){let t=s.states[e];i.fill=t.color||i.fill,i.stroke=t.lineColor||i.stroke,i["stroke-width"]=t.lineWidth||i["stroke-width"]}return i}drawPoints(){let t=this.points,e=this.chart,i=this.yAxis.reversed;for(let s of t){let t=s.graphic,o,r,n,a,l,h,d,p,c,u=!t;if(void 0!==s.plotY){t||(s.graphic=t=e.renderer.path().add(this.group)),this.chart.styledMode||t.attr(this.pointAttribs(s,s.selected&&"select")).shadow(this.options.shadow);let g=t.strokeWidth();d=i6(s.plotX||0,g),n=Math.min(o=s.plotOpen,r=s.plotClose),a=Math.max(o,r),c=Math.round(s.shapeArgs.width/2),l=i?a!==s.yBottom:Math.round(n)!==Math.round(s.plotHigh||0),h=i?Math.round(n)!==Math.round(s.plotHigh||0):a!==s.yBottom,n=i6(n,g),a=i6(a,g),(p=[]).push(["M",d-c,a],["L",d-c,n],["L",d+c,n],["L",d+c,a],["Z"],["M",d,n],["L",d,l?Math.round(i?s.yBottom:s.plotHigh):n],["M",d,a],["L",d,h?Math.round(i?s.plotHigh:s.yBottom):a]),t[u?"attr":"animate"]({d:p}).addClass(s.getClassName(),!0)}}}}i8.defaultOptions=i4(i3.defaultOptions,{tooltip:i3.defaultOptions.tooltip},{states:{hover:{lineWidth:2}},threshold:null,lineColor:"#000000",lineWidth:1,upColor:"#ffffff",stickyTracking:!0}),tv().registerSeriesType("candlestick",i8);let{column:{prototype:{pointClass:i9}}}=tv().seriesTypes,{isNumber:i7}=B(),st=class extends i9{constructor(){super(...arguments),this.ttBelow=!1}isValid(){return i7(this.y)||void 0===this.y}hasNewShapeType(){let t=this.options.shape||this.series.options.shape;return this.graphic&&t&&t!==this.graphic.symbolKey}};!function(t){let e=[];function i(t,e,i,s,o){let r=o&&o.anchorX||t,n=o&&o.anchorY||e,a=this.circle(r-1,n-1,2,2);return a.push(["M",r,n],["L",t,e+s],["L",t,e],["L",t+i,e],["L",t+i,e+s],["L",t,e+s],["Z"]),a}function s(t,e){t[e+"pin"]=function(i,s,o,r,n){let a,l=n&&n.anchorX,h=n&&n.anchorY;if("circle"===e&&r>o&&(i-=Math.round((r-o)/2),o=r),a=t[e](i,s,o,r,n),l&&h){let n=l;if("circle"===e)n=i+o/2;else{let t=a[0],e=a[1];"M"===t[0]&&"L"===e[0]&&(n=(t[1]+e[1])/2)}let d=s>h?s:s+r;a.push(["M",n,d],["L",l,h]),a=a.concat(t.circle(l-1,h-1,2,2))}return a}}t.compose=function(t){if(-1===e.indexOf(t)){e.push(t);let o=t.prototype.symbols;o.flag=i,s(o,"circle"),s(o,"square")}let o=tB().getRendererType();e.indexOf(o)&&e.push(o)}}(M||(M={}));let se=M;var si=E(448),ss=E.n(si);let{composed:so}=B(),{prototype:sr}=ss(),{prototype:sn}=z(),{defined:sa,pushUnique:sl,stableSort:sh}=B();!function(t){function e(t){return sn.getPlotBox.call(this.options.onSeries&&this.chart.get(this.options.onSeries)||this,t)}function i(){sr.translate.apply(this);let t=this,e=t.options,i=t.chart,s=t.points,o=e.onSeries,r=o&&i.get(o),n=r&&r.options.step,a=r&&r.points,l=i.inverted,h=t.xAxis,d=t.yAxis,p=s.length-1,c,u,g=e.onKey||"y",m=a&&a.length,f=0,x,b,v,y,M;if(r&&r.visible&&m){for(f=(r.pointXOffset||0)+(r.barW||0)/2,y=r.currentDataGrouping,b=a[m-1].x+(y?y.totalRange:0),sh(s,(t,e)=>t.x-e.x),g="plot"+g[0].toUpperCase()+g.substr(1);m--&&s[p];)if(x=a[m],(c=s[p]).y=x.y,x.x<=c.x&&void 0!==x[g]){if(c.x<=b&&(c.plotY=x[g],x.x<c.x&&!n&&(v=a[m+1]))&&void 0!==v[g])if(sa(c.plotX)&&r.is("spline")){let t=[x.plotX||0,x.plotY||0],e=[v.plotX||0,v.plotY||0],i=x.controlPoints?.high||t,s=v.controlPoints?.low||e,o=(o,r)=>Math.pow(1-o,3)*t[r]+3*(1-o)*(1-o)*o*i[r]+3*(1-o)*o*o*s[r]+o*o*o*e[r],r=0,n=1,a;for(let t=0;t<100;t++){let t=(r+n)/2,e=o(t,0);if(null===e)break;if(.25>Math.abs(e-c.plotX)){a=t;break}e<c.plotX?r=t:n=t}sa(a)&&(c.plotY=o(a,1),c.y=d.toValue(c.plotY,!0))}else M=(c.x-x.x)/(v.x-x.x),c.plotY+=M*(v[g]-x[g]),c.y+=M*(v.y-x.y);if(p--,m++,p<0)break}}s.forEach((e,i)=>{let o;e.plotX+=f,(void 0===e.plotY||l)&&(e.plotX>=0&&e.plotX<=h.len?l?(e.plotY=h.translate(e.x,0,1,0,1),e.plotX=sa(e.y)?d.translate(e.y,0,0,0,1):0):e.plotY=(h.opposite?0:t.yAxis.len)+h.offset:e.shapeArgs={}),(u=s[i-1])&&u.plotX===e.plotX&&(void 0===u.stackIndex&&(u.stackIndex=0),o=u.stackIndex+1),e.stackIndex=o}),this.onSeries=r}t.compose=function(t){if(sl(so,"OnSeries")){let s=t.prototype;s.getPlotBox=e,s.translate=i}return t},t.getPlotBox=e,t.translate=i}(A||(A={}));let sd=A,{noop:sp}=B(),{distribute:sc}=B(),{series:su,seriesTypes:{column:sg}}=tv(),{addEvent:sm,defined:sf,extend:sx,isNumber:sb,merge:sv,objectEach:sy,wrap:sM}=B();class sA extends sg{animate(t){t&&this.setClip()}drawPoints(){let t,e,i,s,o,r,n,a,l,h,d,p=this.points,c=this.chart,u=c.renderer,g=c.inverted,m=this.options,f=m.y,x=this.yAxis,b={},v=[],y=sb(m.borderRadius)?m.borderRadius:0;for(s=p.length;s--;)o=p[s],h=(g?o.plotY:o.plotX)>this.xAxis.len,t=o.plotX,n=o.stackIndex,i=o.options.shape||m.shape,void 0!==(e=o.plotY)&&(e=o.plotY+f-(void 0!==n&&n*m.stackDistance)),o.anchorX=n?void 0:o.plotX,a=n?void 0:o.plotY,d="flag"!==i,r=o.graphic,void 0!==e&&t>=0&&!h?(r&&o.hasNewShapeType()&&(r=r.destroy()),r||(r=o.graphic=u.label("",0,void 0,i,void 0,void 0,m.useHTML).addClass("highcharts-point").add(this.markerGroup),o.graphic.div&&(o.graphic.div.point=o),r.isNew=!0),r.attr({align:d?"center":"left",width:m.width,height:m.height,"text-align":m.textAlign,r:y}),c.styledMode||r.attr(this.pointAttribs(o)).css(sv(m.style,o.style)).shadow(m.shadow),t>0&&(t-=r.strokeWidth()%2),l={y:e,anchorY:a},m.allowOverlapX&&(l.x=t,l.anchorX=o.anchorX),r.attr({text:o.options.title??m.title??"A"})[r.isNew?"attr":"animate"](l),m.allowOverlapX||(b[o.plotX]?b[o.plotX].size=Math.max(b[o.plotX].size,r.width||0):b[o.plotX]={align:.5*!!d,size:r.width||0,target:t,anchorX:t}),o.tooltipPos=[t,e+x.pos-c.plotTop]):r&&(o.graphic=r.destroy());if(!m.allowOverlapX){let t=100;for(let e of(sy(b,function(e){e.plotX=e.anchorX,v.push(e),t=Math.max(e.size,t)}),sc(v,g?x.len:this.xAxis.len,t),p)){let t=e.plotX,i=e.graphic,s=i&&b[t];s&&i&&(sf(s.pos)?i[i.isNew?"attr":"animate"]({x:s.pos+(s.align||0)*s.size,anchorX:e.anchorX}).show().isNew=!1:i.hide().isNew=!0)}}m.useHTML&&this.markerGroup&&sM(this.markerGroup,"on",function(t){return e3().prototype.on.apply(t.apply(this,[].slice.call(arguments,1)),[].slice.call(arguments,1))})}drawTracker(){let t=this.points;for(let e of(super.drawTracker(),t)){let i=e.graphic;i&&(e.unbindMouseOver&&e.unbindMouseOver(),e.unbindMouseOver=sm(i.element,"mouseover",function(){for(let s of(e.stackIndex>0&&!e.raised&&(e._y=i.y,i.attr({y:e._y-8}),e.raised=!0),t))s!==e&&s.raised&&s.graphic&&(s.graphic.attr({y:s._y}),s.raised=!1)}))}}pointAttribs(t,e){let i=this.options,s=t&&t.color||this.color,o=i.lineColor,r=t&&t.lineWidth,n=t&&t.fillColor||i.fillColor;return e&&(n=i.states[e].fillColor,o=i.states[e].lineColor,r=i.states[e].lineWidth),{fill:n||s,stroke:o||s,"stroke-width":r||i.lineWidth||0}}setClip(){su.prototype.setClip.apply(this,arguments),!1!==this.options.clip&&this.sharedClipKey&&this.markerGroup&&this.markerGroup.clip(this.chart.sharedClips[this.sharedClipKey])}}sA.compose=se.compose,sA.defaultOptions=sv(sg.defaultOptions,{borderRadius:0,pointRange:0,allowOverlapX:!1,shape:"flag",stackDistance:12,textAlign:"center",tooltip:{pointFormat:"{point.text}"},threshold:null,y:-30,fillColor:"#ffffff",lineWidth:1,states:{hover:{lineColor:"#000000",fillColor:"#ccd3ff"}},style:{color:"#000000",fontSize:"0.7em",fontWeight:"bold"}}),sd.compose(sA),sx(sA.prototype,{allowDG:!1,forceCrop:!0,invertible:!1,noSharedTooltip:!0,pointClass:st,sorted:!1,takeOrdinalPosition:!1,trackerGroups:["markerGroup"],buildKDTree:sp,init:su.prototype.init}),tv().registerSeriesType("flags",sA);var sk=E(184),sw=E.n(sk);let{addEvent:sS,find:sT,fireEvent:sE,isArray:sO,isNumber:sC,pick:sB}=B();!function(t){function e(){void 0!==this.brokenAxis&&this.brokenAxis.setBreaks(this.options.breaks,!1)}function i(){this.brokenAxis?.hasBreaks&&(this.options.ordinal=!1)}function s(){let t=this.brokenAxis;if(t?.hasBreaks){let e=this.tickPositions,i=this.tickPositions.info,s=[];for(let i=0;i<e.length;i++)t.isInAnyBreak(e[i])||s.push(e[i]);this.tickPositions=s,this.tickPositions.info=i}}function o(){this.brokenAxis||(this.brokenAxis=new h(this))}function r(){let{isDirty:t,options:{connectNulls:e},points:i,xAxis:s,yAxis:o}=this;if(t){let t=i.length;for(;t--;){let r=i[t],n=(null!==r.y||!1!==e)&&(s?.brokenAxis?.isInAnyBreak(r.x,!0)||o?.brokenAxis?.isInAnyBreak(r.y,!0));r.visible=!n&&!1!==r.options.visible}}}function n(){this.drawBreaks(this.xAxis,["x"]),this.drawBreaks(this.yAxis,sB(this.pointArrayMap,["y"]))}function a(t,e){let i,s,o,r=this,n=r.points;if(t?.brokenAxis?.hasBreaks){let a=t.brokenAxis;e.forEach(function(e){i=a?.breakArray||[],s=t.isXAxis?t.min:sB(r.options.threshold,t.min);let l=t?.options?.breaks?.filter(function(t){let e=!0;for(let s=0;s<i.length;s++){let o=i[s];if(o.from===t.from&&o.to===t.to){e=!1;break}}return e});n.forEach(function(r){o=sB(r["stack"+e.toUpperCase()],r[e]),i.forEach(function(e){if(sC(s)&&sC(o)){let i="";s<e.from&&o>e.to||s>e.from&&o<e.from?i="pointBreak":(s<e.from&&o>e.from&&o<e.to||s>e.from&&o>e.to&&o<e.from)&&(i="pointInBreak"),i&&sE(t,i,{point:r,brk:e})}}),l?.forEach(function(e){sE(t,"pointOutsideOfBreak",{point:r,brk:e})})})})}}function l(){let t=this.currentDataGrouping,e=t?.gapSize,i=this.points.slice(),s=this.yAxis,o=this.options.gapSize,r=i.length-1;if(o&&r>0){let t,n;for("value"!==this.options.gapUnit&&(o*=this.basePointRange),e&&e>o&&e>=this.basePointRange&&(o=e);r--;)if(n&&!1!==n.visible||(n=i[r+1]),t=i[r],!1!==n.visible&&!1!==t.visible){if(n.x-t.x>o){let e=(t.x+n.x)/2;i.splice(r+1,0,{isNull:!0,x:e}),s.stacking&&this.options.stacking&&((s.stacking.stacks[this.stackKey][e]=new(sw())(s,s.options.stackLabels,!1,e,this.stack)).total=0)}n=t}}return this.getGraphPath(i)}t.compose=function(t,h){if(!t.keepProps.includes("brokenAxis")){t.keepProps.push("brokenAxis"),sS(t,"init",o),sS(t,"afterInit",e),sS(t,"afterSetTickPositions",s),sS(t,"afterSetOptions",i);let d=h.prototype;d.drawBreaks=a,d.gappedPath=l,sS(h,"afterGeneratePoints",r),sS(h,"afterRender",n)}return t};class h{static isInBreak(t,e){let i,s=t.repeat||1/0,o=t.from,r=t.to-t.from,n=e>=o?(e-o)%s:s-(o-e)%s;return t.inclusive?n<=r:n<r&&0!==n}static lin2Val(t){let e=this.brokenAxis,i=e?.breakArray;if(!i||!sC(t))return t;let s=t,o,r;for(r=0;r<i.length&&!((o=i[r]).from>=s);r++)o.to<s?s+=o.len:h.isInBreak(o,s)&&(s+=o.len);return s}static val2Lin(t){let e=this.brokenAxis,i=e?.breakArray;if(!i||!sC(t))return t;let s=t,o,r;for(r=0;r<i.length;r++)if((o=i[r]).to<=t)s-=o.len;else if(o.from>=t)break;else if(h.isInBreak(o,t)){s-=t-o.from;break}return s}constructor(t){this.hasBreaks=!1,this.axis=t}findBreakAt(t,e){return sT(e,function(e){return e.from<t&&t<e.to})}isInAnyBreak(t,e){let i=this.axis,s=i.options.breaks||[],o=s.length,r,n,a;if(o&&sC(t)){for(;o--;)h.isInBreak(s[o],t)&&(r=!0,n||(n=sB(s[o].showPoints,!i.isXAxis)));a=r&&e?r&&!n:r}return a}setBreaks(t,e){let i=this,s=i.axis,o=s.chart.time,r=sO(t)&&!!t.length&&!!Object.keys(t[0]).length;s.isDirty=i.hasBreaks!==r,i.hasBreaks=r,t?.forEach(t=>{t.from=o.parse(t.from)||0,t.to=o.parse(t.to)||0}),t!==s.options.breaks&&(s.options.breaks=s.userOptions.breaks=t),s.forceRedraw=!0,s.series.forEach(function(t){t.isDirty=!0}),r||s.val2lin!==h.val2Lin||(delete s.val2lin,delete s.lin2val),r&&(s.userOptions.ordinal=!1,s.lin2val=h.lin2Val,s.val2lin=h.val2Lin,s.setExtremes=function(t,e,o,r,n){if(i.hasBreaks){let s,o=this.options.breaks||[];for(;s=i.findBreakAt(t,o);)t=s.to;for(;s=i.findBreakAt(e,o);)e=s.from;e<t&&(e=t)}s.constructor.prototype.setExtremes.call(this,t,e,o,r,n)},s.setAxisTranslation=function(){if(s.constructor.prototype.setAxisTranslation.call(this),i.unitLength=void 0,i.hasBreaks){let t=s.options.breaks||[],e=[],o=[],r=sB(s.pointRangePadding,0),n=0,a,l,d=s.userMin||s.min,p=s.userMax||s.max,c,u;t.forEach(function(t){l=t.repeat||1/0,sC(d)&&sC(p)&&(h.isInBreak(t,d)&&(d+=t.to%l-d%l),h.isInBreak(t,p)&&(p-=p%l-t.from%l))}),t.forEach(function(t){if(c=t.from,l=t.repeat||1/0,sC(d)&&sC(p)){for(;c-l>d;)c-=l;for(;c<d;)c+=l;for(u=c;u<p;u+=l)e.push({value:u,move:"in"}),e.push({value:u+t.to-t.from,move:"out",size:t.breakSize})}}),e.sort(function(t,e){return t.value===e.value?("in"!==t.move)-("in"!==e.move):t.value-e.value}),a=0,c=d,e.forEach(function(t){1===(a+="in"===t.move?1:-1)&&"in"===t.move&&(c=t.value),0===a&&sC(c)&&(o.push({from:c,to:t.value,len:t.value-c-(t.size||0)}),n+=t.value-c-(t.size||0))}),i.breakArray=o,sC(d)&&sC(p)&&sC(s.min)&&(i.unitLength=p-d-n+r,sE(s,"afterBreaks"),s.staticScale?s.transA=s.staticScale:i.unitLength&&(s.transA*=(p-s.min+r)/i.unitLength),r&&(s.minPixelPadding=s.transA*(s.minPointOffset||0)),s.min=d,s.max=p)}}),sB(e,!0)&&s.chart.redraw()}}t.Additions=h}(k||(k={}));let sD=k,sP=B();sP.BrokenAxis=sP.BrokenAxis||sD,sP.BrokenAxis.compose(sP.Axis,sP.Series);let sR={},{arrayMax:sG,arrayMin:sI,correctFloat:sz,extend:sL,isNumber:sH}=B();function sW(t){let e=t.length,i=sX(t);return sH(i)&&e&&(i=sz(i/e)),i}function sX(t){let e=t.length,i;if(!e&&t.hasNulls)i=null;else if(e)for(i=0;e--;)i+=t[e];return i}let sY={average:sW,averages:function(){let t=[];return[].forEach.call(arguments,function(e){t.push(sW(e))}),void 0===t[0]?void 0:t},close:function(t){return t.length?t[t.length-1]:t.hasNulls?null:void 0},high:function(t){return t.length?sG(t):t.hasNulls?null:void 0},hlc:function(t,e,i){if(t=sR.high(t),e=sR.low(e),i=sR.close(i),sH(t)||sH(e)||sH(i))return[t,e,i]},low:function(t){return t.length?sI(t):t.hasNulls?null:void 0},ohlc:function(t,e,i,s){if(t=sR.open(t),e=sR.high(e),i=sR.low(i),s=sR.close(s),sH(t)||sH(e)||sH(i)||sH(s))return[t,e,i,s]},open:function(t){return t.length?t[0]:t.hasNulls?null:void 0},range:function(t,e){return(t=sR.low(t),e=sR.high(e),sH(t)||sH(e))?[t,e]:null===t&&null===e?null:void 0},sum:sX};sL(sR,sY);let sN={common:{groupPixelWidth:2,dateTimeLabelFormats:{millisecond:["%[AebHMSL]","%[AebHMSL]","-%[HMSL]"],second:["%[AebHMS]","%[AebHMS]","-%[HMS]"],minute:["%[AebHM]","%[AebHM]","-%[HM]"],hour:["%[AebHM]","%[AebHM]","-%[HM]"],day:["%[AebY]","%[Aeb]","-%[AebY]"],week:["%v %[AebY]","%[Aeb]","-%[AebY]"],month:["%[BY]","%[B]","-%[BY]"],year:["%Y","%Y","-%Y"]}},seriesSpecific:{line:{},spline:{},area:{},areaspline:{},arearange:{},column:{groupPixelWidth:10},columnrange:{groupPixelWidth:10},candlestick:{groupPixelWidth:10},ohlc:{groupPixelWidth:5},hlc:{groupPixelWidth:5},heikinashi:{groupPixelWidth:10}},units:[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1]],["week",[1]],["month",[1,3,6]],["year",null]]},{addEvent:sF,extend:sU,merge:s_,pick:sV}=B();function sZ(t){let e=this,i=e.series;i.forEach(function(t){t.groupPixelWidth=void 0}),i.forEach(function(i){i.groupPixelWidth=e.getGroupPixelWidth&&e.getGroupPixelWidth(),i.groupPixelWidth&&(i.hasProcessed=!0),i.applyGrouping(!!t.hasExtremesChanged)})}function sj(){let t=this.series,e=t.length,i=0,s=!1,o,r;for(;e--;)(r=t[e].options.dataGrouping)&&(i=Math.max(i,sV(r.groupPixelWidth,sN.common.groupPixelWidth)),o=(t[e].dataTable.modified||t[e].dataTable).rowCount,(t[e].groupPixelWidth||o>this.chart.plotSizeX/i||o&&r.forced)&&(s=!0));return s?i:0}function sq(){this.series.forEach(function(t){t.hasProcessed=!1})}function sK(t,e){let i;if(e=sV(e,!0),t||(t={forced:!1,units:null}),this instanceof m)for(i=this.series.length;i--;)this.series[i].update({dataGrouping:t},!1);else this.chart.options.series.forEach(function(e){e.dataGrouping="boolean"==typeof t?t:s_(t,e.dataGrouping)});this.ordinal&&(this.ordinal.slope=void 0),e&&this.chart.redraw()}let s$={compose:function(t){m=t;let e=t.prototype;e.applyGrouping||(sF(t,"afterSetScale",sq),sF(t,"postProcessData",sZ),sU(e,{applyGrouping:sZ,getGroupPixelWidth:sj,setDataGrouping:sK}))}},{addEvent:sJ,getMagnitude:sQ,normalizeTickInterval:s0,timeUnits:s1}=B();!function(t){function e(){return this.chart.time.getTimeTicks.apply(this.chart.time,arguments)}function i(){if("datetime"!==this.type){this.dateTime=void 0;return}this.dateTime||(this.dateTime=new s(this))}t.compose=function(t){return t.keepProps.includes("dateTime")||(t.keepProps.push("dateTime"),t.prototype.getTimeTicks=e,sJ(t,"afterSetType",i)),t};class s{constructor(t){this.axis=t}normalizeTimeTickInterval(t,e){let i=e||[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1,2]],["week",[1,2]],["month",[1,2,3,4,6]],["year",null]],s=i[i.length-1],o=s1[s[0]],r=s[1],n;for(n=0;n<i.length&&(o=s1[(s=i[n])[0]],r=s[1],!i[n+1]||!(t<=(o*r[r.length-1]+s1[i[n+1][0]])/2));n++);o===s1.year&&t<5*o&&(r=[1,2,5]);let a=s0(t/o,r,"year"===s[0]?Math.max(sQ(t/o),1):1);return{unitRange:o,count:a,unitName:s[0]}}getXDateFormat(t,e){let{axis:i}=this,s=i.chart.time;return i.closestPointRange?s.getDateFormat(i.closestPointRange,t,i.options.startOfWeek,e)||s.resolveDTLFormat(e.year).main:s.resolveDTLFormat(e.day).main}}t.Additions=s}(w||(w={}));let s2=w,{series:{prototype:s5}}=tv(),{addEvent:s3,defined:s6,error:s4,extend:s8,isNumber:s9,merge:s7,pick:ot,splat:oe}=B(),oi=s5.generatePoints;function os(t){var e,i,s;let o,r,n=this.chart,a=this.options.dataGrouping,l=!1!==this.allowDG&&a&&ot(a.enabled,n.options.isStock),h=this.reserveSpace(),d=this.currentDataGrouping,p,c,u=!1;l&&!this.requireSorting&&(this.requireSorting=u=!0);let g=!1==(e=this,i=t,!(e.isCartesian&&!e.isDirty&&!e.xAxis.isDirty&&!e.yAxis.isDirty&&!i))||!l;if(u&&(this.requireSorting=!1),g)return;this.destroyGroupedData();let m=a.groupAll?this.dataTable:this.dataTable.modified||this.dataTable,f=this.getColumn("x",!a.groupAll),x=n.plotSizeX,b=this.xAxis,v=b.getExtremes(),y=b.options.ordinal,M=this.groupPixelWidth;if(M&&f&&m.rowCount&&x&&s9(v.min)){r=!0,this.isDirty=!0,this.points=null;let t=v.min,e=v.max,i=y&&b.ordinal&&b.ordinal.getGroupIntervalFactor(t,e,this)||1,l=M*(e-t)/x*i,d=b.getTimeTicks(s2.Additions.prototype.normalizeTimeTickInterval(l,a.units||sN.units),Math.min(t,f[0]),Math.max(e,f[f.length-1]),b.options.startOfWeek,f,this.closestPointRange),u=s5.groupData.apply(this,[m,d,a.approximation]),g=u.modified,A=g.getColumn("x",!0),k=0;for(a?.smoothed&&g.rowCount&&(a.firstAnchor="firstPoint",a.anchor="middle",a.lastAnchor="lastPoint",s4(32,!1,n,{"dataGrouping.smoothed":"use dataGrouping.anchor"})),o=1;o<d.length;o++)d.info.segmentStarts&&-1!==d.info.segmentStarts.indexOf(o)||(k=Math.max(d[o]-d[o-1],k));(p=d.info).gapSize=k,this.closestPointRange=d.info.totalRange,this.groupMap=u.groupMap,this.currentDataGrouping=p,function(t,e,i){let s=t.options.dataGrouping,o=t.currentDataGrouping&&t.currentDataGrouping.gapSize,r=t.getColumn("x");if(!(s&&r.length&&o&&t.groupMap))return;let n=e.length-1,a=s.anchor,l=s.firstAnchor,h=s.lastAnchor,d=e.length-1,p=0;if(l&&r[0]>=e[0]){let i;p++;let s=t.groupMap[0].start,n=t.groupMap[0].length;s9(s)&&s9(n)&&(i=s+(n-1)),e[0]=({start:e[0],middle:e[0]+.5*o,end:e[0]+o,firstPoint:r[0],lastPoint:i&&r[i]})[l]}if(n>0&&h&&o&&e[n]>=i-o){d--;let i=t.groupMap[t.groupMap.length-1].start;e[n]=({start:e[n],middle:e[n]+.5*o,end:e[n]+o,firstPoint:i&&r[i],lastPoint:r[r.length-1]})[h]}if(a&&"start"!==a){let t=o*({middle:.5,end:1})[a];for(;d>=p;)e[d]+=t,d--}}(this,A||[],e),h&&A&&(s6((s=A)[0])&&s9(b.min)&&s9(b.dataMin)&&s[0]<b.min&&((!s6(b.options.min)&&b.min<=b.dataMin||b.min===b.dataMin)&&(b.min=Math.min(s[0],b.min)),b.dataMin=Math.min(s[0],b.dataMin)),s6(s[s.length-1])&&s9(b.max)&&s9(b.dataMax)&&s[s.length-1]>b.max&&((!s6(b.options.max)&&s9(b.dataMax)&&b.max>=b.dataMax||b.max===b.dataMax)&&(b.max=Math.max(s[s.length-1],b.max)),b.dataMax=Math.max(s[s.length-1],b.dataMax))),a.groupAll&&(this.allGroupedTable=g,A=(g=(c=this.cropData(g,b.min||0,b.max||0)).modified).getColumn("x"),this.cropStart=c.start),this.dataTable.modified=g}else this.groupMap=void 0,this.currentDataGrouping=void 0;this.hasGroupedData=r,this.preventGraphAnimation=(d&&d.totalRange)!==(p&&p.totalRange)}function oo(){this.groupedData&&(this.groupedData.forEach(function(t,e){t&&(this.groupedData[e]=t.destroy?t.destroy():null)},this),this.groupedData.length=0,delete this.allGroupedTable)}function or(){oi.apply(this),this.destroyGroupedData(),this.groupedData=this.hasGroupedData?this.points:null}function on(){return this.is("arearange")?"range":this.is("ohlc")?"ohlc":this.is("hlc")?"hlc":this.is("column")||this.options.cumulative?"sum":"average"}function oa(t,e,i){let s=t.getColumn("x",!0)||[],o=t.getColumn("y",!0),r=this,n=r.data,a=r.options&&r.options.data,l=[],h=new eT,d=[],p=t.rowCount,c=!!o,u=[],g=r.pointArrayMap,m=g&&g.length,f=["x"].concat(g||["y"]),x=(g||["y"]).map(()=>[]),b=this.options.dataGrouping&&this.options.dataGrouping.groupAll,v,y,M,A=0,k=0,w="function"==typeof i?i:i&&sR[i]?sR[i]:sR[r.getDGApproximation&&r.getDGApproximation()||"average"];if(m){let t=g.length;for(;t--;)u.push([])}else u.push([]);let S=m||1;for(let t=0;t<=p;t++)if(!(s[t]<e[0])){for(;void 0!==e[A+1]&&s[t]>=e[A+1]||t===p;){if(v=e[A],r.dataGroupInfo={start:b?k:r.cropStart+k,length:u[0].length,groupStart:v},M=w.apply(r,u),r.pointClass&&!s6(r.dataGroupInfo.options)&&(r.dataGroupInfo.options=s7(r.pointClass.prototype.optionsToObject.call({series:r},r.options.data[r.cropStart+k])),f.forEach(function(t){delete r.dataGroupInfo.options[t]})),void 0!==M){l.push(v);let t=oe(M);for(let e=0;e<t.length;e++)x[e].push(t[e]);d.push(r.dataGroupInfo)}k=t;for(let t=0;t<S;t++)u[t].length=0,u[t].hasNulls=!1;if(A+=1,t===p)break}if(t===p)break;if(g){let e,i=b?t:r.cropStart+t,s=n&&n[i]||r.pointClass.prototype.applyOptions.apply({series:r},[a[i]]);for(let t=0;t<m;t++)s9(e=s[g[t]])?u[t].push(e):null===e&&(u[t].hasNulls=!0)}else s9(y=c?o[t]:null)?u[0].push(y):null===y&&(u[0].hasNulls=!0)}let T={x:l};return(g||["y"]).forEach((t,e)=>{T[t]=x[e]}),h.setColumns(T),{groupMap:d,modified:h}}function ol(t){let e=t.options,i=this.type,s=this.chart.options.plotOptions,o=this.useCommonDataGrouping&&sN.common,r=sN.seriesSpecific,n=B().defaultOptions.plotOptions[i].dataGrouping;if(s&&(r[i]||o)){let t=this.chart.rangeSelector;n||(n=s7(sN.common,r[i])),e.dataGrouping=s7(o,n,s.series&&s.series.dataGrouping,s[i].dataGrouping,this.userOptions.dataGrouping,!e.isInternal&&t&&s9(t.selected)&&t.buttonOptions[t.selected].dataGrouping)}}let oh={compose:function(t){let e=t.prototype;e.applyGrouping||(s3(t.prototype.pointClass,"update",function(){if(this.dataGroup)return s4(24,!1,this.series.chart),!1}),s3(t,"afterSetOptions",ol),s3(t,"destroy",oo),s8(e,{applyGrouping:os,destroyGroupedData:oo,generatePoints:or,getDGApproximation:on,groupData:oa}))},groupData:oa},{format:od}=e4(),{composed:op}=B(),{addEvent:oc,extend:ou,isNumber:og,pick:om,pushUnique:of}=B();function ox(t){let e=this.chart,i=e.time,s=t.point,o=s.series,r=o.options,n=o.tooltipOptions,a=r.dataGrouping,l=o.xAxis,h=n.xDateFormat||"",d,p,c,u,g,m=n[t.isFooter?"footerFormat":"headerFormat"];if(l&&"datetime"===l.options.type&&a&&og(s.key)){p=o.currentDataGrouping,c=a.dateTimeLabelFormats||sN.common.dateTimeLabelFormats,p?(u=c[p.unitName],1===p.count?h=u[0]:(h=u[1],d=u[2])):!h&&c&&l.dateTime&&(h=l.dateTime.getXDateFormat(s.x,n.dateTimeLabelFormats));let r=om(o.groupMap?.[s.index].groupStart,s.key),f=r+(p?.totalRange||0)-1;g=i.dateFormat(h,r),d&&(g+=i.dateFormat(d,f)),o.chart.styledMode&&(m=this.styledModeFormat(m)),t.text=od(m,{point:ou(s,{key:g}),series:o},e),t.preventDefault()}}let ob={compose:function(t,e,i){s$.compose(t),oh.compose(e),i&&of(op,"DataGrouping")&&oc(i,"headerFormatter",ox)},groupData:oh.groupData},ov=B();ov.dataGrouping=ov.dataGrouping||{},ov.dataGrouping.approximationDefaults=ov.dataGrouping.approximationDefaults||sY,ov.dataGrouping.approximations=ov.dataGrouping.approximations||sR,ob.compose(ov.Axis,ov.Series,ov.Tooltip);let{defined:oy,isNumber:oM,pick:oA}=B(),ok={backgroundColor:"string",borderColor:"string",borderRadius:"string",color:"string",fill:"string",fontSize:"string",labels:"string",name:"string",stroke:"string",title:"string"},{addEvent:ow,isObject:oS,pick:oT,defined:oE,merge:oO}=B(),{getAssignedAxis:oC}={annotationsFieldsTypes:ok,getAssignedAxis:function(t){return t.filter(t=>{let e=t.axis.getExtremes(),i=e.min,s=e.max,o=oA(t.axis.minPointOffset,0);return oM(i)&&oM(s)&&t.value>=i-o&&t.value<=s+o&&!t.axis.options.isInternal})[0]},getFieldType:function(t,e){let i=ok[t],s=typeof e;return oy(i)&&(s=i),({string:"text",number:"number",boolean:"checkbox"})[s]}},oB=[],oD={enabled:!0,sensitivity:1.1},oP=t=>(oS(t)||(t={enabled:t??!0}),oO(oD,t)),oR=function(t,e,i,s,o,r,n){let a=oT(n.type,t.zooming.type,""),l=[];"x"===a?l=i:"y"===a?l=s:"xy"===a&&(l=t.axes);let h=t.transform({axes:l,to:{x:o-5,y:r-5,width:10,height:10},from:{x:o-5*e,y:r-5*e,width:10*e,height:10*e},trigger:"mousewheel"});return h&&(oE(f)&&clearTimeout(f),f=setTimeout(()=>{t.pointer?.drop()},400)),h};function oG(){let t=oP(this.zooming.mouseWheel);t.enabled&&ow(this.container,"wheel",e=>{e=this.pointer?.normalize(e)||e;let{pointer:i}=this,s=i&&!i.inClass(e.target,"highcharts-no-mousewheel");if(this.isInsidePlot(e.chartX-this.plotLeft,e.chartY-this.plotTop)&&s){let s=t.sensitivity||1.1,o=e.detail||(e.deltaY||0)/120,r=oC(i.getCoordinates(e).xAxis),n=oC(i.getCoordinates(e).yAxis);oR(this,Math.pow(s,o),r?[r.axis]:this.xAxis,n?[n.axis]:this.yAxis,e.chartX,e.chartY,t)&&e.preventDefault?.()}})}/**
 * @license Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/mouse-wheel-zoom
 * @requires highcharts
 *
 * Mousewheel zoom module
 *
 * (c) 2023 Askel Eirik Johansson
 *
 * License: www.highcharts.com/license
 */let oI=B();oI.MouseWheelZoom=oI.MouseWheelZoom||{compose:function(t){-1===oB.indexOf(t)&&(oB.push(t),ow(t,"afterGetContainer",oG))}},oI.MouseWheelZoom.compose(oI.Chart);/**
 * @license Highstock JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/stock
 * @requires highcharts
 *
 * Highcharts Stock as a plugin for Highcharts
 *
 * (c) 2010-2025 Torstein Honsi
 *
 * License: www.highcharts.com/license
 */let oz=B();oz.Navigator=oz.Navigator||ey,oz.OrdinalAxis=oz.OrdinalAxis||ez,oz.RangeSelector=oz.RangeSelector||ix,oz.Scrollbar=oz.Scrollbar||t9,oz.stockChart=oz.stockChart||iL.stockChart,oz.StockChart=oz.StockChart||oz.stockChart,oz.extend(oz.StockChart,iL),Z.compose(oz.Series,oz.Axis,oz.Point),sA.compose(oz.Renderer),i2.compose(oz.Series),oz.Navigator.compose(oz.Chart,oz.Axis,oz.Series),oz.OrdinalAxis.compose(oz.Axis,oz.Series,oz.Chart),oz.RangeSelector.compose(oz.Axis,oz.Chart),oz.Scrollbar.compose(oz.Axis),oz.StockChart.compose(oz.Chart,oz.Axis,oz.Series,oz.SVGRenderer);let oL=B();return O.default})());