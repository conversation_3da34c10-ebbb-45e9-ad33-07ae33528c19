!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/xrange
 * @requires highcharts
 *
 * X-range series
 *
 * (c) 2010-2025 <PERSON><PERSON>, Lars <PERSON>
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.Color,t._Highcharts.SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/modules/xrange",["highcharts/highcharts"],function(t){return e(t,t.Color,t.SeriesRegistry)}):"object"==typeof exports?exports["highcharts/modules/xrange"]=e(t._Highcharts,t._Highcharts.Color,t._Highcharts.SeriesRegistry):t.Highcharts=e(t.Highcharts,t.Highcharts.Color,t.Highcharts.SeriesRegistry)}("undefined"==typeof window?this:window,(t,e,i)=>(()=>{"use strict";var s={512:t=>{t.exports=i},620:t=>{t.exports=e},944:e=>{e.exports=t}},o={};function r(t){var e=o[t];if(void 0!==e)return e.exports;var i=o[t]={exports:{}};return s[t](i,i.exports,r),i.exports}r.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return r.d(e,{a:e}),e},r.d=(t,e)=>{for(var i in e)r.o(e,i)&&!r.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var a={};r.d(a,{default:()=>D});var l=r(944),n=r.n(l),h=r(620),p=r.n(h),c=r(512),d=r.n(c);let{correctFloat:g,isNumber:u,isObject:x}=n(),{column:{prototype:{pointClass:y}}}=d().seriesTypes,{extend:f}=n();class m extends y{static getColorByCategory(t,e){let i=t.options.colors||t.chart.options.colors,s=i?i.length:t.chart.options.chart.colorCount,o=e.y%s,r=i?.[o];return{colorIndex:o,color:r}}resolveColor(){let t=this.series;if(t.options.colorByPoint&&!this.options.color){let e=m.getColorByCategory(t,this);t.chart.styledMode||(this.color=e.color),this.options.colorIndex||(this.colorIndex=e.colorIndex)}else this.color=this.options.color||t.color}constructor(t,e){super(t,e),this.y||(this.y=0)}applyOptions(t,e){return super.applyOptions(t,e),this.x2=this.series.chart.time.parse(this.x2),this.isNull=!this.isValid?.(),this}setState(){super.setState.apply(this,arguments),this.series.drawPoint(this,this.series.getAnimationVerb())}isValid(){return"number"==typeof this.x&&"number"==typeof this.x2}}f(m.prototype,{ttBelow:!1,tooltipDateKeys:["x","x2"]});let{composed:b,noop:A}=n(),{parse:C}=p(),{column:w}=d().seriesTypes,{addEvent:v,clamp:M,crisp:P,defined:R,extend:S,find:X,isNumber:H,isObject:L,merge:O,pick:_,pushUnique:B,relativeLength:F}=n();function I(){let t,e;if(this.isXAxis){for(let i of(t=_(this.dataMax,-Number.MAX_VALUE),this.series)){let s=i.dataTable.getColumn("x2",!0)||i.dataTable.getColumn("end",!0);if(s)for(let i of s)H(i)&&i>t&&(t=i,e=!0)}e&&(this.dataMax=t)}}class T extends w{static compose(t){B(b,"Series.XRange")&&v(t,"afterGetSeriesExtremes",I)}init(){super.init.apply(this,arguments),this.options.stacking=void 0}getColumnMetrics(){let t=()=>{for(let t of this.chart.series){let e=t.xAxis;t.xAxis=t.yAxis,t.yAxis=e}};t();let e=super.getColumnMetrics();return t(),e}cropData(t,e,i){let s=t.getColumn("x")||[],o=t.getColumn("x2");t.setColumn("x",o,void 0,{silent:!0});let r=super.cropData(t,e,i);return t.setColumn("x",s.slice(r.start,r.end),void 0,{silent:!0}),r}findPointIndex(t){let e,{cropStart:i,points:s}=this,{id:o}=t;if(o){let t=X(s,t=>t.id===o);e=t?t.index:void 0}if(void 0===e){let i=X(s,e=>e.x===t.x&&e.x2===t.x2&&!e.touched);e=i?i.index:void 0}return this.cropped&&H(e)&&H(i)&&e>=i&&(e-=i),e}alignDataLabel(t){let e=t.plotX;t.plotX=_(t.dlBox?.centerX,t.plotX),t.dataLabel&&t.shapeArgs?.width&&t.dataLabel.css({width:`${t.shapeArgs.width}px`}),super.alignDataLabel.apply(this,arguments),t.plotX=e}translatePoint(t){let e=this.xAxis,i=this.yAxis,s=this.columnMetrics,o=this.options,r=o.minPointLength||0,a=(t.shapeArgs?.width||0)/2,l=this.pointXOffset=s.offset,n=_(t.x2,t.x+(t.len||0)),h=o.borderRadius,p=this.chart.plotTop,c=this.chart.plotLeft,d=t.plotX,g=e.translate(n,0,0,0,1),u=Math.abs(g-d),x=this.chart.inverted,y=_(o.borderWidth,1),f,m,b=s.offset,A=Math.round(s.width),C,w,v,S;r&&((f=r-u)<0&&(f=0),d-=f/2,g+=f/2),d=Math.max(d,-10),g=M(g,-10,e.len+10),R(t.options.pointWidth)&&(b-=(Math.ceil(t.options.pointWidth)-A)/2,A=Math.ceil(t.options.pointWidth)),o.pointPlacement&&H(t.plotY)&&i.categories&&(t.plotY=i.translate(t.y,0,1,0,1,o.pointPlacement));let X=P(Math.min(d,g),y),B=P(Math.max(d,g),y)-X,I=Math.min(F("object"==typeof h?h.radius:h||0,A),Math.min(B,A)/2),T={x:X,y:P((t.plotY||0)+b,y),width:B,height:A,r:I};t.shapeArgs=T,x?t.tooltipPos[1]+=l+a:t.tooltipPos[0]-=a+l-T.width/2,w=(C=T.x)+T.width,C<0||w>e.len?(C=M(C,0,e.len),v=(w=M(w,0,e.len))-C,t.dlBox=O(T,{x:C,width:w-C,centerX:v?v/2:null})):t.dlBox=null;let j=t.tooltipPos,D=+!!x,N=+!x,V=this.columnMetrics?this.columnMetrics.offset:-s.width/2;x?j[D]+=T.width/2:j[D]=M(j[D]+(e.reversed?-1:0)*T.width,e.left-c,e.left+e.len-c-1),j[N]=M(j[N]+(x?-1:1)*V,i.top-p,i.top+i.len-p-1),(m=t.partialFill)&&(L(m)&&(m=m.amount),H(m)||(m=0),t.partShapeArgs=O(T),S=Math.max(Math.round(u*m+t.plotX-d),0),t.clipRectArgs={x:e.reversed?T.x+u-S:T.x,y:T.y,width:S,height:T.height}),t.key=t.category||t.name,t.yCategory=i.categories?.[t.y??-1]}translate(){for(let t of(super.translate.apply(this,arguments),this.points))this.translatePoint(t)}drawPoint(t,e){let i=this.options,s=this.chart.renderer,o=t.shapeType,r=t.shapeArgs,a=t.partShapeArgs,l=t.clipRectArgs,n=t.state,h=i.states[n||"normal"]||{},p=void 0===n?"attr":e,c=this.pointAttribs(t,n),d=_(this.chart.options.chart.animation,h.animation),g=t.graphic,u=t.partialFill;if(t.isNull||!1===t.visible)g&&(t.graphic=g.destroy());else if(g?g.rect[e](r):(t.graphic=g=s.g("point").addClass(t.getClassName()).add(t.group||this.group),g.rect=s[o](O(r)).addClass(t.getClassName()).addClass("highcharts-partfill-original").add(g)),a&&(g.partRect?(g.partRect[e](O(a)),g.partialClipRect[e](O(l))):(g.partialClipRect=s.clipRect(l.x,l.y,l.width,l.height),g.partRect=s[o](a).addClass("highcharts-partfill-overlay").add(g).clip(g.partialClipRect))),!this.chart.styledMode&&(g.rect[e](c,d).shadow(i.shadow),a)){L(u)||(u={}),L(i.partialFill)&&(u=O(i.partialFill,u));let e=u.fill||C(c.fill).brighten(-.3).get()||C(t.color||this.color).brighten(-.3).get();c.fill=e,g.partRect[p](c,d).shadow(i.shadow)}}drawPoints(){let t=this.getAnimationVerb();for(let e of this.points)this.drawPoint(e,t)}getAnimationVerb(){return this.chart.pointCount<(this.options.animationLimit||250)?"animate":"attr"}isPointInside(t){let e=t.shapeArgs,i=t.plotX,s=t.plotY;return e?void 0!==i&&void 0!==s&&s>=0&&s<=this.yAxis.len&&(e.x||0)+(e.width||0)>=0&&i<=this.xAxis.len:super.isPointInside.apply(this,arguments)}}T.defaultOptions=O(w.defaultOptions,{colorByPoint:!0,dataLabels:{formatter:function(){let t=this.partialFill;if(x(t)&&(t=t.amount),u(t)&&t>0)return g(100*t)+"%"},inside:!0,verticalAlign:"middle",style:{whiteSpace:"nowrap"}},tooltip:{headerFormat:'<span style="font-size: 0.8em">{ucfirst point.x} - {point.x2}</span><br/>',pointFormat:'<span style="color:{point.color}">●</span> {series.name}: <b>{point.yCategory}</b><br/>'},borderRadius:3,pointRange:0}),S(T.prototype,{pointClass:m,pointArrayMap:["x2","y"],getExtremesFromAll:!0,keysAffectYAxis:["y"],parallelArrays:["x","x2","y"],requireSorting:!1,type:"xrange",animate:d().series.prototype.animate,autoIncrement:A,buildKDTree:A}),d().registerSeriesType("xrange",T);let j=n();T.compose(j.Axis);let D=n();return a.default})());