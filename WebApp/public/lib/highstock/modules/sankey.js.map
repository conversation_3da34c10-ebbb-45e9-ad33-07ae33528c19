{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/sankey\n * @requires highcharts\n *\n * Sankey diagram module\n *\n * (c) 2010-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"Point\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"SVGElement\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/sankey\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"],amd1[\"Point\"],amd1[\"Color\"],amd1[\"SVGElement\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/sankey\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"Point\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"SVGElement\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"Point\"], root[\"Highcharts\"][\"Color\"], root[\"Highcharts\"][\"SVGElement\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__260__, __WEBPACK_EXTERNAL_MODULE__620__, __WEBPACK_EXTERNAL_MODULE__28__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 28:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__28__;\n\n/***/ }),\n\n/***/ 260:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__260__;\n\n/***/ }),\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 620:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__620__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ sankey_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/NodesComposition.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { series: { prototype: seriesProto, prototype: { pointClass: { prototype: pointProto } } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { defined, extend, find, merge, pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\nvar NodesComposition;\n(function (NodesComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    function compose(PointClass, SeriesClass) {\n        const pointProto = PointClass.prototype, seriesProto = SeriesClass.prototype;\n        pointProto.setNodeState = setNodeState;\n        pointProto.setState = setNodeState;\n        pointProto.update = updateNode;\n        seriesProto.destroy = destroy;\n        seriesProto.setData = setData;\n        return SeriesClass;\n    }\n    NodesComposition.compose = compose;\n    /**\n     * Create a single node that holds information on incoming and outgoing\n     * links.\n     * @private\n     */\n    function createNode(id) {\n        const PointClass = this.pointClass, findById = (nodes, id) => find(nodes, (node) => node.id === id);\n        let node = findById(this.nodes, id), options;\n        if (!node) {\n            options = this.options.nodes && findById(this.options.nodes, id);\n            const newNode = new PointClass(this, extend({\n                className: 'highcharts-node',\n                isNode: true,\n                id: id,\n                y: 1 // Pass isNull test\n            }, options));\n            newNode.linksTo = [];\n            newNode.linksFrom = [];\n            /**\n             * Return the largest sum of either the incoming or outgoing links.\n             * @private\n             */\n            newNode.getSum = function () {\n                let sumTo = 0, sumFrom = 0;\n                newNode.linksTo.forEach((link) => {\n                    sumTo += link.weight || 0;\n                });\n                newNode.linksFrom.forEach((link) => {\n                    sumFrom += link.weight || 0;\n                });\n                return Math.max(sumTo, sumFrom);\n            };\n            /**\n             * Get the offset in weight values of a point/link.\n             * @private\n             */\n            newNode.offset = function (point, coll) {\n                let offset = 0;\n                for (let i = 0; i < newNode[coll].length; i++) {\n                    if (newNode[coll][i] === point) {\n                        return offset;\n                    }\n                    offset += newNode[coll][i].weight;\n                }\n            };\n            // Return true if the node has a shape, otherwise all links are\n            // outgoing.\n            newNode.hasShape = function () {\n                let outgoing = 0;\n                newNode.linksTo.forEach((link) => {\n                    if (link.outgoing) {\n                        outgoing++;\n                    }\n                });\n                return (!newNode.linksTo.length ||\n                    outgoing !== newNode.linksTo.length);\n            };\n            newNode.index = this.nodes.push(newNode) - 1;\n            node = newNode;\n        }\n        node.formatPrefix = 'node';\n        // For use in formats\n        node.name = node.name || node.options.id || '';\n        // Mass is used in networkgraph:\n        node.mass = pick(\n        // Node:\n        node.options.mass, node.options.marker && node.options.marker.radius, \n        // Series:\n        this.options.marker && this.options.marker.radius, \n        // Default:\n        4);\n        return node;\n    }\n    NodesComposition.createNode = createNode;\n    /**\n     * Destroy all nodes and links.\n     * @private\n     */\n    function destroy() {\n        // Nodes must also be destroyed (#8682, #9300)\n        this.data = []\n            .concat(this.points || [], this.nodes);\n        return seriesProto.destroy.apply(this, arguments);\n    }\n    NodesComposition.destroy = destroy;\n    /**\n     * Extend generatePoints by adding the nodes, which are Point objects but\n     * pushed to the this.nodes array.\n     * @private\n     */\n    function generatePoints() {\n        const chart = this.chart, nodeLookup = {};\n        seriesProto.generatePoints.call(this);\n        if (!this.nodes) {\n            this.nodes = []; // List of Point-like node items\n        }\n        this.colorCounter = 0;\n        // Reset links from previous run\n        this.nodes.forEach((node) => {\n            node.linksFrom.length = 0;\n            node.linksTo.length = 0;\n            node.level = node.options.level;\n        });\n        // Create the node list and set up links\n        this.points.forEach((point) => {\n            if (defined(point.from)) {\n                if (!nodeLookup[point.from]) {\n                    nodeLookup[point.from] = this.createNode(point.from);\n                }\n                nodeLookup[point.from].linksFrom.push(point);\n                point.fromNode = nodeLookup[point.from];\n                // Point color defaults to the fromNode's color\n                if (chart.styledMode) {\n                    point.colorIndex = pick(point.options.colorIndex, nodeLookup[point.from].colorIndex);\n                }\n                else {\n                    point.color =\n                        point.options.color || nodeLookup[point.from].color;\n                }\n            }\n            if (defined(point.to)) {\n                if (!nodeLookup[point.to]) {\n                    nodeLookup[point.to] = this.createNode(point.to);\n                }\n                nodeLookup[point.to].linksTo.push(point);\n                point.toNode = nodeLookup[point.to];\n            }\n            point.name = point.name || point.id; // For use in formats\n        }, this);\n        // Store lookup table for later use\n        this.nodeLookup = nodeLookup;\n    }\n    NodesComposition.generatePoints = generatePoints;\n    /**\n     * Destroy all nodes on setting new data\n     * @private\n     */\n    function setData() {\n        if (this.nodes) {\n            this.nodes.forEach((node) => {\n                node.destroy();\n            });\n            this.nodes.length = 0;\n        }\n        seriesProto.setData.apply(this, arguments);\n    }\n    /**\n     * When hovering node, highlight all connected links. When hovering a link,\n     * highlight all connected nodes.\n     * @private\n     */\n    function setNodeState(state) {\n        const args = arguments, others = this.isNode ? this.linksTo.concat(this.linksFrom) :\n            [this.fromNode, this.toNode];\n        if (state !== 'select') {\n            others.forEach((linkOrNode) => {\n                if (linkOrNode && linkOrNode.series) {\n                    pointProto.setState.apply(linkOrNode, args);\n                    if (!linkOrNode.isNode) {\n                        if (linkOrNode.fromNode.graphic) {\n                            pointProto.setState.apply(linkOrNode.fromNode, args);\n                        }\n                        if (linkOrNode.toNode && linkOrNode.toNode.graphic) {\n                            pointProto.setState.apply(linkOrNode.toNode, args);\n                        }\n                    }\n                }\n            });\n        }\n        pointProto.setState.apply(this, args);\n    }\n    NodesComposition.setNodeState = setNodeState;\n    /**\n     * When updating a node, don't update `series.options.data`, but\n     * `series.options.nodes`\n     * @private\n     */\n    function updateNode(options, redraw, animation, runEvent) {\n        const nodes = this.series.options.nodes, data = this.series.options.data, dataLength = data?.length || 0, linkConfig = data?.[this.index];\n        pointProto.update.call(this, options, this.isNode ? false : redraw, // Hold the redraw for nodes\n        animation, runEvent);\n        if (this.isNode) {\n            // `this.index` refers to `series.nodes`, not `options.nodes` array\n            const nodeIndex = (nodes || [])\n                .reduce(// Array.findIndex needs a polyfill\n            (prevIndex, n, index) => (this.id === n.id ? index : prevIndex), -1), \n            // Merge old config with new config. New config is stored in\n            // options.data, because of default logic in point.update()\n            nodeConfig = merge(nodes && nodes[nodeIndex] || {}, data?.[this.index] || {});\n            // Restore link config\n            if (data) {\n                if (linkConfig) {\n                    data[this.index] = linkConfig;\n                }\n                else {\n                    // Remove node from config if there's more nodes than links\n                    data.length = dataLength;\n                }\n            }\n            // Set node config\n            if (nodes) {\n                if (nodeIndex >= 0) {\n                    nodes[nodeIndex] = nodeConfig;\n                }\n                else {\n                    nodes.push(nodeConfig);\n                }\n            }\n            else {\n                this.series.options.nodes = [nodeConfig];\n            }\n            if (pick(redraw, true)) {\n                this.series.chart.redraw(animation);\n            }\n        }\n    }\n    NodesComposition.updateNode = updateNode;\n})(NodesComposition || (NodesComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Series_NodesComposition = (NodesComposition);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Point\"],\"commonjs\":[\"highcharts\",\"Point\"],\"commonjs2\":[\"highcharts\",\"Point\"],\"root\":[\"Highcharts\",\"Point\"]}\nvar highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_ = __webpack_require__(260);\nvar highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default = /*#__PURE__*/__webpack_require__.n(highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_);\n;// ./code/es-modules/Series/Sankey/SankeyPoint.js\n/* *\n *\n *  Sankey diagram module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { column: ColumnSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { defined: SankeyPoint_defined } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass SankeyPoint extends ColumnSeries.prototype.pointClass {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    applyOptions(options, x) {\n        highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default().prototype.applyOptions.call(this, options, x);\n        // Treat point.level as a synonym of point.column\n        if (SankeyPoint_defined(this.options.level)) {\n            this.options.column = this.column = this.options.level;\n        }\n        return this;\n    }\n    /**\n     * @private\n     */\n    getClassName() {\n        return (this.isNode ? 'highcharts-node ' : 'highcharts-link ') +\n            highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default().prototype.getClassName.call(this);\n    }\n    /**\n     * If there are incoming links, place it to the right of the\n     * highest order column that links to this one.\n     *\n     * @private\n     */\n    getFromNode() {\n        const node = this;\n        let fromColumn = -1, fromNode;\n        for (let i = 0; i < node.linksTo.length; i++) {\n            const point = node.linksTo[i];\n            if (point.fromNode.column > fromColumn &&\n                point.fromNode !== node // #16080\n            ) {\n                fromNode = point.fromNode;\n                fromColumn = fromNode.column;\n            }\n        }\n        return { fromNode, fromColumn };\n    }\n    /**\n     * Calculate node.column if it's not set by user\n     * @private\n     */\n    setNodeColumn() {\n        const node = this;\n        if (!SankeyPoint_defined(node.options.column)) {\n            // No links to this node, place it left\n            if (node.linksTo.length === 0) {\n                node.column = 0;\n            }\n            else {\n                node.column = node.getFromNode().fromColumn + 1;\n            }\n        }\n    }\n    /**\n     * @private\n     */\n    isValid() {\n        return this.isNode || typeof this.weight === 'number';\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sankey_SankeyPoint = (SankeyPoint);\n\n;// ./code/es-modules/Series/Sankey/SankeySeriesDefaults.js\n/* *\n *\n *  Sankey diagram module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A sankey diagram is a type of flow diagram, in which the width of the\n * link between two nodes is shown proportionally to the flow quantity.\n *\n * @sample highcharts/demo/sankey-diagram/\n *         Sankey diagram\n * @sample highcharts/plotoptions/sankey-inverted/\n *         Inverted sankey diagram\n * @sample highcharts/plotoptions/sankey-outgoing\n *         Sankey diagram with outgoing links\n *\n * @extends      plotOptions.column\n * @since        6.0.0\n * @product      highcharts\n * @excluding    animationLimit, boostBlending, boostThreshold, borderRadius,\n *               crisp, cropThreshold, colorAxis, colorKey, dataSorting, depth,\n *               dragDrop, edgeColor, edgeWidth, findNearestPointBy, grouping,\n *               groupPadding, groupZPadding, legendSymbolColor, maxPointWidth,\n *               minPointLength, negativeColor, pointInterval,\n *               pointIntervalUnit, pointPadding, pointPlacement, pointRange,\n *               pointStart, pointWidth, shadow, softThreshold, stacking,\n *               threshold, zoneAxis, zones\n * @requires     modules/sankey\n * @optionparent plotOptions.sankey\n *\n * @private\n */\nconst SankeySeriesDefaults = {\n    borderWidth: 0,\n    colorByPoint: true,\n    /**\n     * Higher numbers makes the links in a sankey diagram or dependency\n     * wheelrender more curved. A `curveFactor` of 0 makes the lines\n     * straight.\n     *\n     * @private\n     */\n    curveFactor: 0.33,\n    /**\n     * Options for the data labels appearing on top of the nodes and links.\n     * For sankey charts, data labels are visible for the nodes by default,\n     * but hidden for links. This is controlled by modifying the\n     * `nodeFormat`, and the `format` that applies to links and is an empty\n     * string by default.\n     *\n     * @declare Highcharts.SeriesSankeyDataLabelsOptionsObject\n     *\n     * @private\n     */\n    dataLabels: {\n        enabled: true,\n        backgroundColor: 'none', // Enable padding\n        crop: false,\n        /**\n         * The\n         * [format string](https://www.highcharts.com/docs/chart-concepts/labels-and-string-formatting)\n         * specifying what to show for _nodes_ in the sankey diagram. By\n         * default the `nodeFormatter` returns `{point.name}`.\n         *\n         * @sample highcharts/plotoptions/sankey-link-datalabels/\n         *         Node and link data labels\n         *\n         * @type {string}\n         */\n        nodeFormat: void 0,\n        /**\n         * Callback to format data labels for _nodes_ in the sankey diagram.\n         * The `nodeFormat` option takes precedence over the\n         * `nodeFormatter`.\n         *\n         * @type  {Highcharts.SeriesSankeyDataLabelsFormatterCallbackFunction}\n         * @since 6.0.2\n         */\n        nodeFormatter: function () {\n            return this.point.name;\n        },\n        format: void 0,\n        /**\n         * @type {Highcharts.SeriesSankeyDataLabelsFormatterCallbackFunction}\n         */\n        formatter: function () {\n            return;\n        },\n        inside: true\n    },\n    /**\n     * @default   true\n     * @extends   plotOptions.series.inactiveOtherPoints\n     * @private\n     */\n    inactiveOtherPoints: true,\n    /**\n     * Set options on specific levels. Takes precedence over series options,\n     * but not node and link options.\n     *\n     * @sample highcharts/demo/sunburst\n     *         Sunburst chart\n     *\n     * @type      {Array<*>}\n     * @since     7.1.0\n     * @apioption plotOptions.sankey.levels\n     */\n    /**\n     * Can set `borderColor` on all nodes which lay on the same level.\n     *\n     * @type      {Highcharts.ColorString}\n     * @apioption plotOptions.sankey.levels.borderColor\n     */\n    /**\n     * Can set `borderWidth` on all nodes which lay on the same level.\n     *\n     * @type      {number}\n     * @apioption plotOptions.sankey.levels.borderWidth\n     */\n    /**\n     * Can set `color` on all nodes which lay on the same level.\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @apioption plotOptions.sankey.levels.color\n     */\n    /**\n     * Can set `colorByPoint` on all nodes which lay on the same level.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @apioption plotOptions.sankey.levels.colorByPoint\n     */\n    /**\n     * Can set `dataLabels` on all points which lay on the same level.\n     *\n     * @extends   plotOptions.sankey.dataLabels\n     * @apioption plotOptions.sankey.levels.dataLabels\n     */\n    /**\n     * Decides which level takes effect from the options set in the levels\n     * object.\n     *\n     * @type      {number}\n     * @apioption plotOptions.sankey.levels.level\n     */\n    /**\n     * Can set `linkOpacity` on all points which lay on the same level.\n     *\n     * @type      {number}\n     * @default   0.5\n     * @apioption plotOptions.sankey.levels.linkOpacity\n     */\n    /**\n     * Can set `states` on all nodes and points which lay on the same level.\n     *\n     * @extends   plotOptions.sankey.states\n     * @apioption plotOptions.sankey.levels.states\n     */\n    /**\n     * Determines color mode for sankey links. Available options:\n     *\n     * - `from` color of the sankey link will be the same as the 'from node'\n     *\n     * - `gradient` color of the sankey link will be set to gradient between\n     * colors of 'from node' and 'to node'\n     *\n     * - `to` color of the sankey link will be same as the 'to node'.\n     *\n     * @sample highcharts/demo/vertical-sankey\n     *         Vertical sankey diagram with gradients\n     * @sample highcharts/series-sankey/link-color-mode\n     *         Sankey diagram with gradients and explanation\n     *\n     * @type      {('from'|'gradient'|'to')}\n     * @since     11.2.0\n     */\n    linkColorMode: 'from',\n    /**\n     * Opacity for the links between nodes in the sankey diagram.\n     *\n     * @private\n     */\n    linkOpacity: 0.5,\n    /**\n     * Opacity for the nodes in the sankey diagram.\n     *\n     * @private\n     */\n    opacity: 1,\n    /**\n     * The minimal width for a line of a sankey. By default,\n     * 0 values are not shown.\n     *\n     * @sample highcharts/plotoptions/sankey-minlinkwidth\n     *         Sankey diagram with minimal link height\n     *\n     * @type      {number}\n     * @since     7.1.3\n     * @default   0\n     * @apioption plotOptions.sankey.minLinkWidth\n     *\n     * @private\n     */\n    minLinkWidth: 0,\n    /**\n     * Determines which side of the chart the nodes are to be aligned to. When\n     * the chart is inverted, `top` aligns to the left and `bottom` to the\n     * right.\n     *\n     * @sample highcharts/plotoptions/sankey-nodealignment\n     *         Node alignment demonstrated\n     *\n     * @type      {'top'|'center'|'bottom'}\n     * @apioption plotOptions.sankey.nodeAlignment\n     */\n    nodeAlignment: 'center',\n    /**\n     * The pixel width of each node in a sankey diagram or dependency wheel, or\n     * the height in case the chart is inverted.\n     *\n     * Can be a number or a percentage string.\n     *\n     * Sankey series also support setting it to `auto`. With this setting, the\n     * nodes are sized to fill up the plot area in the longitudinal direction,\n     * regardless of the number of levels.\n     *\n     * @see    [sankey.nodeDistance](#nodeDistance)\n     * @sample highcharts/series-sankey/node-distance\n     *         Sankey with auto node width combined with node distance\n     * @sample highcharts/series-organization/node-distance\n     *         Organization chart with node distance of 50%\n     *\n     * @type {number|string}\n     */\n    nodeWidth: 20,\n    /**\n     * The padding between nodes in a sankey diagram or dependency wheel, in\n     * pixels. For sankey charts, this applies to the nodes of the same column,\n     * so vertical distance by default, or horizontal distance in an inverted\n     * (vertical) sankey.\n     *\n     * If the number of nodes is so great that it is impossible to lay them out\n     * within the plot area with the given `nodePadding`, they will be rendered\n     * with a smaller padding as a strategy to avoid overflow.\n     */\n    nodePadding: 10,\n    /**\n     * The distance between nodes in a sankey diagram in the longitudinal\n     * direction. The longitudinal direction means the direction that the chart\n     * flows - in a horizontal chart the distance is horizontal, in an inverted\n     * chart (vertical), the distance is vertical.\n     *\n     * If a number is given, it denotes pixels. If a percentage string is given,\n     * the distance is a percentage of the rendered node width. A `nodeDistance`\n     * of `100%` will render equal widths for the nodes and the gaps between\n     * them.\n     *\n     * This option applies only when the `nodeWidth` option is `auto`, making\n     * the node width respond to the number of columns.\n     *\n     * @since 11.4.0\n     * @sample highcharts/series-sankey/node-distance\n     *         Sankey with dnode distance of 100% means equal to node width\n     * @sample highcharts/series-organization/node-distance\n     *         Organization chart with node distance of 50%\n     * @type   {number|string}\n     */\n    nodeDistance: 30,\n    showInLegend: false,\n    states: {\n        hover: {\n            /**\n             * Opacity for the links between nodes in the sankey diagram in\n             * hover mode.\n             */\n            linkOpacity: 1,\n            /**\n             * Opacity for the nodes in the sankey diagram in hover mode.\n             */\n            opacity: 1\n        },\n        /**\n         * The opposite state of a hover for a single point node/link.\n         *\n         * @declare Highcharts.SeriesStatesInactiveOptionsObject\n         */\n        inactive: {\n            /**\n             * Opacity for the links between nodes in the sankey diagram in\n             * inactive mode.\n             */\n            linkOpacity: 0.1,\n            /**\n             * Opacity of the nodes in the sankey diagram in inactive mode.\n             */\n            opacity: 0.1,\n            /**\n             * Animation when not hovering over the marker.\n             *\n             * @type      {boolean|Partial<Highcharts.AnimationOptionsObject>}\n             * @apioption plotOptions.series.states.inactive.animation\n             */\n            animation: {\n                /** @internal */\n                duration: 50\n            }\n        }\n    },\n    tooltip: {\n        /**\n         * A callback for defining the format for _nodes_ in the chart's\n         * tooltip, as opposed to links.\n         *\n         * @type      {Highcharts.FormatterCallbackFunction<Highcharts.SankeyNodeObject>}\n         * @since     6.0.2\n         * @apioption plotOptions.sankey.tooltip.nodeFormatter\n         */\n        /**\n         * Whether the tooltip should follow the pointer or stay fixed on\n         * the item.\n         */\n        followPointer: true,\n        headerFormat: '<span style=\"font-size: 0.8em\">{series.name}</span><br/>',\n        pointFormat: '{point.fromNode.name} \\u2192 {point.toNode.name}: <b>{point.weight}</b><br/>',\n        /**\n         * The\n         * [format string](https://www.highcharts.com/docs/chart-concepts/labels-and-string-formatting)\n         * specifying what to show for _nodes_ in tooltip of a diagram\n         * series, as opposed to links.\n         */\n        nodeFormat: '{point.name}: <b>{point.sum}</b><br/>'\n    }\n};\n/**\n * A `sankey` series. If the [type](#series.sankey.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.sankey\n * @excluding animationLimit, boostBlending, boostThreshold, borderColor,\n *            borderRadius, borderWidth, crisp, cropThreshold, dataParser,\n *            dataURL, depth, dragDrop, edgeColor, edgeWidth,\n *            findNearestPointBy, getExtremesFromAll, grouping, groupPadding,\n *            groupZPadding, label, maxPointWidth, negativeColor, pointInterval,\n *            pointIntervalUnit, pointPadding, pointPlacement, pointRange,\n *            pointStart, pointWidth, shadow, softThreshold, stacking,\n *            threshold, zoneAxis, zones, dataSorting\n * @product   highcharts\n * @requires  modules/sankey\n * @apioption series.sankey\n */\n/**\n * A collection of options for the individual nodes. The nodes in a sankey\n * diagram are auto-generated instances of `Highcharts.Point`, but options can\n * be applied here and linked by the `id`.\n *\n * @sample highcharts/css/sankey/\n *         Sankey diagram with node options\n *\n * @declare   Highcharts.SeriesSankeyNodesOptionsObject\n * @type      {Array<*>}\n * @product   highcharts\n * @apioption series.sankey.nodes\n */\n/**\n * The id of the auto-generated node, referring to the `from` or `to` setting of\n * the link.\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.sankey.nodes.id\n */\n/**\n * The color of the auto generated node.\n *\n * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n * @product   highcharts\n * @apioption series.sankey.nodes.color\n */\n/**\n * The color index of the auto generated node, especially for use in styled\n * mode.\n *\n * @type      {number}\n * @product   highcharts\n * @apioption series.sankey.nodes.colorIndex\n */\n/**\n * An optional column index of where to place the node. The default behaviour is\n * to place it next to the preceding node. Note that this option name is\n * counter intuitive in inverted charts, like for example an organization chart\n * rendered top down. In this case the \"columns\" are horizontal.\n *\n * @sample highcharts/plotoptions/sankey-node-column/\n *         Specified node column\n *\n * @type      {number}\n * @since     6.0.5\n * @product   highcharts\n * @apioption series.sankey.nodes.column\n */\n/**\n * Individual data label for each node. The options are the same as\n * the ones for [series.sankey.dataLabels](#series.sankey.dataLabels).\n *\n * @extends   plotOptions.sankey.dataLabels\n * @apioption series.sankey.nodes.dataLabels\n */\n/**\n * The height of the node.\n *\n * @sample highcharts/series-sankey/height/\n *         Sankey diagram with height options\n *\n * @type      {number}\n * @since     11.3.0\n * @apioption series.sankey.nodes.height\n */\n/**\n * An optional level index of where to place the node. The default behaviour is\n * to place it next to the preceding node. Alias of `nodes.column`, but in\n * inverted sankeys and org charts, the levels are laid out as rows.\n *\n * @type      {number}\n * @since     7.1.0\n * @product   highcharts\n * @apioption series.sankey.nodes.level\n */\n/**\n * The name to display for the node in data labels and tooltips. Use this when\n * the name is different from the `id`. Where the id must be unique for each\n * node, this is not necessary for the name.\n *\n * @sample highcharts/css/sankey/\n *         Sankey diagram with node options\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.sankey.nodes.name\n */\n/**\n * This option is deprecated, use\n * [offsetHorizontal](#series.sankey.nodes.offsetHorizontal) and\n * [offsetVertical](#series.sankey.nodes.offsetVertical) instead.\n *\n * In a horizontal layout, the vertical offset of a node in terms of weight.\n * Positive values shift the node downwards, negative shift it upwards. In a\n * vertical layout, like organization chart, the offset is horizontal.\n *\n * If a percentage string is given, the node is offset by the percentage of the\n * node size plus `nodePadding`.\n *\n * @deprecated\n * @type      {number|string}\n * @default   0\n * @since     6.0.5\n * @product   highcharts\n * @apioption series.sankey.nodes.offset\n */\n/**\n * The horizontal offset of a node. Positive values shift the node right,\n * negative shift it left.\n *\n * If a percentage string is given, the node is offset by the percentage of the\n * node size.\n *\n * @sample highcharts/plotoptions/sankey-node-column/\n *         Specified node offset\n *\n * @type      {number|string}\n * @since 9.3.0\n * @product   highcharts\n * @apioption series.sankey.nodes.offsetHorizontal\n */\n/**\n * The vertical offset of a node. Positive values shift the node down,\n * negative shift it up.\n *\n * If a percentage string is given, the node is offset by the percentage of the\n * node size.\n *\n * @sample highcharts/plotoptions/sankey-node-column/\n *         Specified node offset\n *\n * @type      {number|string}\n * @since 9.3.0\n * @product   highcharts\n * @apioption series.sankey.nodes.offsetVertical\n */\n/**\n * An array of data points for the series. For the `sankey` series type,\n * points can be given in the following way:\n *\n * An array of objects with named values. The following snippet shows only a\n * few settings, see the complete options set below. If the total number of data\n * points exceeds the series' [turboThreshold](#series.area.turboThreshold),\n * this option is not available.\n *\n *  ```js\n *     data: [{\n *         from: 'Category1',\n *         to: 'Category2',\n *         weight: 2\n *     }, {\n *         from: 'Category1',\n *         to: 'Category3',\n *         weight: 5\n *     }]\n *  ```\n *\n *  When you provide the data as tuples, the keys option has to be set as well.\n *\n *  ```js\n *     keys: ['from', 'to', 'weight'],\n *     data: [\n *         ['Category1', 'Category2', 2],\n *         ['Category1', 'Category3', 5]\n *     ]\n *  ```\n *\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @declare   Highcharts.SeriesSankeyPointOptionsObject\n * @type      {Array<*>|Array<Array<(string|number)>>}\n * @extends   series.line.data\n * @excluding dragDrop, drilldown, marker, x, y\n * @product   highcharts\n * @apioption series.sankey.data\n */\n/**\n * The color for the individual _link_. By default, the link color is the same\n * as the node it extends from. The `series.fillOpacity` option also applies to\n * the points, so when setting a specific link color, consider setting the\n * `fillOpacity` to 1.\n *\n * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n * @product   highcharts\n * @apioption series.sankey.data.color\n */\n/**\n * @type      {Highcharts.SeriesSankeyDataLabelsOptionsObject|Array<Highcharts.SeriesSankeyDataLabelsOptionsObject>}\n * @product   highcharts\n * @apioption series.sankey.data.dataLabels\n */\n/**\n * The node that the link runs from.\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.sankey.data.from\n */\n/**\n * The node that the link runs to.\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.sankey.data.to\n */\n/**\n * Whether the link goes out of the system.\n *\n * @sample highcharts/plotoptions/sankey-outgoing\n *         Sankey chart with outgoing links\n *\n * @type      {boolean}\n * @default   false\n * @product   highcharts\n * @apioption series.sankey.data.outgoing\n */\n/**\n * The weight of the link.\n *\n * @type      {number|null}\n * @product   highcharts\n * @apioption series.sankey.data.weight\n */\n''; // Adds doclets above to transpiled file\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sankey_SankeySeriesDefaults = (SankeySeriesDefaults);\n\n;// ./code/es-modules/Series/Sankey/SankeyColumnComposition.js\n/* *\n *\n *  Sankey diagram module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { defined: SankeyColumnComposition_defined, getAlignFactor, relativeLength } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\nvar SankeyColumnComposition;\n(function (SankeyColumnComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * SankeyColumn Composition\n     * @private\n     * @function Highcharts.SankeyColumn#compose\n     *\n     * @param {Array<SankeyPoint>} points\n     * The array of nodes\n     * @param {SankeySeries} series\n     * Series connected to column\n     * @return {ArrayComposition} SankeyColumnArray\n     */\n    function compose(points, series) {\n        const sankeyColumnArray = points;\n        sankeyColumnArray.sankeyColumn =\n            new SankeyColumnAdditions(sankeyColumnArray, series);\n        return sankeyColumnArray;\n    }\n    SankeyColumnComposition.compose = compose;\n    /* *\n     *\n     *  Classes\n     *\n     * */\n    class SankeyColumnAdditions {\n        /* *\n         *\n         *  Constructor\n         *\n         * */\n        constructor(points, series) {\n            this.points = points;\n            this.series = series;\n        }\n        /* *\n         *\n         *  Functions\n         *\n         * */\n        /**\n         * Calculate translation factor used in column and nodes distribution\n         * @private\n         * @function Highcharts.SankeyColumn#getTranslationFactor\n         *\n         * @param {SankeySeries} series\n         * The Series\n         * @return {number} TranslationFactor\n         * Translation Factor\n         */\n        getTranslationFactor(series) {\n            const column = this.points, nodes = column.slice(), chart = series.chart, minLinkWidth = series.options.minLinkWidth || 0;\n            let skipPoint, factor = 0, i, remainingHeight = ((chart.plotSizeY || 0) -\n                (series.options.borderWidth || 0) -\n                (column.length - 1) * series.nodePadding);\n            // Because the minLinkWidth option doesn't obey the direct\n            // translation, we need to run translation iteratively, check\n            // node heights, remove those nodes affected by minLinkWidth,\n            // check again, etc.\n            while (column.length) {\n                factor = remainingHeight / column.sankeyColumn.sum();\n                skipPoint = false;\n                i = column.length;\n                while (i--) {\n                    if (column[i].getSum() * factor < minLinkWidth) {\n                        column.splice(i, 1);\n                        remainingHeight =\n                            Math.max(0, remainingHeight - minLinkWidth);\n                        skipPoint = true;\n                    }\n                }\n                if (!skipPoint) {\n                    break;\n                }\n            }\n            // Re-insert original nodes\n            column.length = 0;\n            for (const node of nodes) {\n                column.push(node);\n            }\n            return factor;\n        }\n        /**\n         * Get the top position of the column in pixels\n         * @private\n         * @function Highcharts.SankeyColumn#top\n         *\n         * @param {number} factor\n         * The Translation Factor\n         * @return {number} top\n         * The top position of the column\n         */\n        top(factor) {\n            const series = this.series, nodePadding = series.nodePadding, height = this.points.reduce((height, node) => {\n                if (height > 0) {\n                    height += nodePadding;\n                }\n                const nodeHeight = Math.max(node.getSum() * factor, series.options.minLinkWidth || 0);\n                height += nodeHeight;\n                return height;\n            }, 0);\n            // Node alignment option handling #19096\n            return getAlignFactor(series.options.nodeAlignment || 'center') * ((series.chart.plotSizeY || 0) - height);\n        }\n        /**\n         * Get the left position of the column in pixels\n         * @private\n         * @function Highcharts.SankeyColumn#top\n         *\n         * @param {number} factor\n         * The Translation Factor\n         * @return {number} left\n         * The left position of the column\n         */\n        left(factor) {\n            const series = this.series, chart = series.chart, equalNodes = series.options.equalNodes, maxNodesLength = (chart.inverted ? chart.plotHeight : chart.plotWidth), nodePadding = series.nodePadding, width = this.points.reduce((width, node) => {\n                if (width > 0) {\n                    width += nodePadding;\n                }\n                const nodeWidth = equalNodes ?\n                    maxNodesLength / node.series.nodes.length -\n                        nodePadding :\n                    Math.max(node.getSum() * factor, series.options.minLinkWidth || 0);\n                width += nodeWidth;\n                return width;\n            }, 0);\n            return ((chart.plotSizeX || 0) - Math.round(width)) / 2;\n        }\n        /**\n         * Calculate sum of all nodes inside specific column\n         * @private\n         * @function Highcharts.SankeyColumn#sum\n         *\n         * @param {ArrayComposition} this\n         * Sankey Column Array\n         *\n         * @return {number} sum\n         * Sum of all nodes inside column\n         */\n        sum() {\n            return this.points.reduce((sum, node) => (sum + node.getSum()), 0);\n        }\n        /**\n         * Get the offset in pixels of a node inside the column\n         * @private\n         * @function Highcharts.SankeyColumn#offset\n         *\n         * @param {SankeyPoint} node\n         * Sankey node\n         * @param {number} factor\n         * Translation Factor\n         * @return {number} offset\n         * Offset of a node inside column\n         */\n        offset(node, factor) {\n            const column = this.points, series = this.series, nodePadding = series.nodePadding;\n            let offset = 0, totalNodeOffset;\n            if (series.is('organization') && node.hangsFrom) {\n                return {\n                    absoluteTop: node.hangsFrom.nodeY\n                };\n            }\n            for (let i = 0; i < column.length; i++) {\n                const sum = column[i].getSum();\n                const height = Math.max(sum * factor, series.options.minLinkWidth || 0);\n                const directionOffset = node.options[series.chart.inverted ?\n                    'offsetHorizontal' :\n                    'offsetVertical'], optionOffset = node.options.offset || 0;\n                if (sum) {\n                    totalNodeOffset = height + nodePadding;\n                }\n                else {\n                    // If node sum equals 0 nodePadding is missed #12453\n                    totalNodeOffset = 0;\n                }\n                if (column[i] === node) {\n                    return {\n                        relativeTop: offset + (SankeyColumnComposition_defined(directionOffset) ?\n                            // `directionOffset` is a percent of the node\n                            // height\n                            relativeLength(directionOffset, height) :\n                            relativeLength(optionOffset, totalNodeOffset))\n                    };\n                }\n                offset += totalNodeOffset;\n            }\n        }\n    }\n    SankeyColumnComposition.SankeyColumnAdditions = SankeyColumnAdditions;\n})(SankeyColumnComposition || (SankeyColumnComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sankey_SankeyColumnComposition = (SankeyColumnComposition);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Color\"],\"commonjs\":[\"highcharts\",\"Color\"],\"commonjs2\":[\"highcharts\",\"Color\"],\"root\":[\"Highcharts\",\"Color\"]}\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_ = __webpack_require__(620);\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default = /*#__PURE__*/__webpack_require__.n(highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_);\n;// ./code/es-modules/Series/TreeUtilities.js\n/* *\n *\n *  (c) 2014-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { extend: TreeUtilities_extend, isArray, isNumber, isObject, merge: TreeUtilities_merge, pick: TreeUtilities_pick, relativeLength: TreeUtilities_relativeLength } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/* eslint-disable valid-jsdoc */\n/**\n * @private\n */\nfunction getColor(node, options) {\n    const index = options.index, mapOptionsToLevel = options.mapOptionsToLevel, parentColor = options.parentColor, parentColorIndex = options.parentColorIndex, series = options.series, colors = options.colors, siblings = options.siblings, points = series.points, chartOptionsChart = series.chart.options.chart;\n    let getColorByPoint, point, level, colorByPoint, colorIndexByPoint, color, colorIndex;\n    /**\n     * @private\n     */\n    const variateColor = (color) => {\n        const colorVariation = level && level.colorVariation;\n        if (colorVariation &&\n            colorVariation.key === 'brightness' &&\n            index &&\n            siblings) {\n            return highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default().parse(color).brighten(colorVariation.to * (index / siblings)).get();\n        }\n        return color;\n    };\n    if (node) {\n        point = points[node.i];\n        level = mapOptionsToLevel[node.level] || {};\n        getColorByPoint = point && level.colorByPoint;\n        if (getColorByPoint) {\n            colorIndexByPoint = point.index % (colors ?\n                colors.length :\n                chartOptionsChart.colorCount);\n            colorByPoint = colors && colors[colorIndexByPoint];\n        }\n        // Select either point color, level color or inherited color.\n        if (!series.chart.styledMode) {\n            color = TreeUtilities_pick(point && point.options.color, level && level.color, colorByPoint, parentColor && variateColor(parentColor), series.color);\n        }\n        colorIndex = TreeUtilities_pick(point && point.options.colorIndex, level && level.colorIndex, colorIndexByPoint, parentColorIndex, options.colorIndex);\n    }\n    return {\n        color: color,\n        colorIndex: colorIndex\n    };\n}\n/**\n * Creates a map from level number to its given options.\n *\n * @private\n *\n * @param {Object} params\n * Object containing parameters.\n * - `defaults` Object containing default options. The default options are\n *   merged with the userOptions to get the final options for a specific\n *   level.\n * - `from` The lowest level number.\n * - `levels` User options from series.levels.\n * - `to` The highest level number.\n *\n * @return {Highcharts.Dictionary<object>|null}\n * Returns a map from level number to its given options.\n */\nfunction getLevelOptions(params) {\n    const result = {};\n    let defaults, converted, i, from, to, levels;\n    if (isObject(params)) {\n        from = isNumber(params.from) ? params.from : 1;\n        levels = params.levels;\n        converted = {};\n        defaults = isObject(params.defaults) ? params.defaults : {};\n        if (isArray(levels)) {\n            converted = levels.reduce((obj, item) => {\n                let level, levelIsConstant, options;\n                if (isObject(item) && isNumber(item.level)) {\n                    options = TreeUtilities_merge({}, item);\n                    levelIsConstant = TreeUtilities_pick(options.levelIsConstant, defaults.levelIsConstant);\n                    // Delete redundant properties.\n                    delete options.levelIsConstant;\n                    delete options.level;\n                    // Calculate which level these options apply to.\n                    level = item.level + (levelIsConstant ? 0 : from - 1);\n                    if (isObject(obj[level])) {\n                        TreeUtilities_merge(true, obj[level], options); // #16329\n                    }\n                    else {\n                        obj[level] = options;\n                    }\n                }\n                return obj;\n            }, {});\n        }\n        to = isNumber(params.to) ? params.to : 1;\n        for (i = 0; i <= to; i++) {\n            result[i] = TreeUtilities_merge({}, defaults, isObject(converted[i]) ? converted[i] : {});\n        }\n    }\n    return result;\n}\n/**\n * @private\n * @todo Combine buildTree and buildNode with setTreeValues\n * @todo Remove logic from Treemap and make it utilize this mixin.\n */\nfunction setTreeValues(tree, options) {\n    const before = options.before, idRoot = options.idRoot, mapIdToNode = options.mapIdToNode, nodeRoot = mapIdToNode[idRoot], levelIsConstant = (options.levelIsConstant !== false), points = options.points, point = points[tree.i], optionsPoint = point && point.options || {}, children = [];\n    let childrenTotal = 0;\n    tree.levelDynamic = tree.level - (levelIsConstant ? 0 : nodeRoot.level);\n    tree.name = TreeUtilities_pick(point && point.name, '');\n    tree.visible = (idRoot === tree.id ||\n        options.visible === true);\n    if (typeof before === 'function') {\n        tree = before(tree, options);\n    }\n    // First give the children some values\n    tree.children.forEach((child, i) => {\n        const newOptions = TreeUtilities_extend({}, options);\n        TreeUtilities_extend(newOptions, {\n            index: i,\n            siblings: tree.children.length,\n            visible: tree.visible\n        });\n        child = setTreeValues(child, newOptions);\n        children.push(child);\n        if (child.visible) {\n            childrenTotal += child.val;\n        }\n    });\n    // Set the values\n    const value = TreeUtilities_pick(optionsPoint.value, childrenTotal);\n    tree.visible = value >= 0 && (childrenTotal > 0 || tree.visible);\n    tree.children = children;\n    tree.childrenTotal = childrenTotal;\n    tree.isLeaf = tree.visible && !childrenTotal;\n    tree.val = value;\n    return tree;\n}\n/**\n * Update the rootId property on the series. Also makes sure that it is\n * accessible to exporting.\n *\n * @private\n *\n * @param {Object} series\n * The series to operate on.\n *\n * @return {string}\n * Returns the resulting rootId after update.\n */\nfunction updateRootId(series) {\n    let rootId, options;\n    if (isObject(series)) {\n        // Get the series options.\n        options = isObject(series.options) ? series.options : {};\n        // Calculate the rootId.\n        rootId = TreeUtilities_pick(series.rootNode, options.rootId, '');\n        // Set rootId on series.userOptions to pick it up in exporting.\n        if (isObject(series.userOptions)) {\n            series.userOptions.rootId = rootId;\n        }\n        // Set rootId on series to pick it up on next update.\n        series.rootNode = rootId;\n    }\n    return rootId;\n}\n/**\n * Get the node width, which relies on the plot width and the nodeDistance\n * option.\n *\n * @private\n */\nfunction getNodeWidth(series, columnCount) {\n    const { chart, options } = series, { nodeDistance = 0, nodeWidth = 0 } = options, { plotSizeX = 1 } = chart;\n    // Node width auto means they are evenly distributed along the width of\n    // the plot area\n    if (nodeWidth === 'auto') {\n        if (typeof nodeDistance === 'string' && /%$/.test(nodeDistance)) {\n            const fraction = parseFloat(nodeDistance) / 100, total = columnCount + fraction * (columnCount - 1);\n            return plotSizeX / total;\n        }\n        const nDistance = Number(nodeDistance);\n        return ((plotSizeX + nDistance) /\n            (columnCount || 1)) - nDistance;\n    }\n    return TreeUtilities_relativeLength(nodeWidth, plotSizeX);\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst TreeUtilities = {\n    getColor,\n    getLevelOptions,\n    getNodeWidth,\n    setTreeValues,\n    updateRootId\n};\n/* harmony default export */ const Series_TreeUtilities = (TreeUtilities);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SVGElement\"],\"commonjs\":[\"highcharts\",\"SVGElement\"],\"commonjs2\":[\"highcharts\",\"SVGElement\"],\"root\":[\"Highcharts\",\"SVGElement\"]}\nvar highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_ = __webpack_require__(28);\nvar highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default = /*#__PURE__*/__webpack_require__.n(highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_);\n;// ./code/es-modules/Extensions/TextPath.js\n/* *\n *\n *  Highcharts module with textPath functionality.\n *\n *  (c) 2009-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { deg2rad } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nconst { addEvent, merge: TextPath_merge, uniqueKey, defined: TextPath_defined, extend: TextPath_extend } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/**\n * Set a text path for a `text` or `label` element, allowing the text to\n * flow along a path.\n *\n * In order to unset the path for an existing element, call `setTextPath`\n * with `{ enabled: false }` as the second argument.\n *\n * Text path support is not bundled into `highcharts.js`, and requires the\n * `modules/textpath.js` file. However, it is included in the script files of\n * those series types that use it by default\n *\n * @sample highcharts/members/renderer-textpath/ Text path demonstrated\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {Highcharts.SVGElement|undefined} path\n *        Path to follow. If undefined, it allows changing options for the\n *        existing path.\n *\n * @param {Highcharts.DataLabelsTextPathOptionsObject} textPathOptions\n *        Options.\n *\n * @return {Highcharts.SVGElement} Returns the SVGElement for chaining.\n */\nfunction setTextPath(path, textPathOptions) {\n    // Defaults\n    textPathOptions = TextPath_merge(true, {\n        enabled: true,\n        attributes: {\n            dy: -5,\n            startOffset: '50%',\n            textAnchor: 'middle'\n        }\n    }, textPathOptions);\n    const url = this.renderer.url, textWrapper = this.text || this, textPath = textWrapper.textPath, { attributes, enabled } = textPathOptions;\n    path = path || (textPath && textPath.path);\n    // Remove previously added event\n    if (textPath) {\n        textPath.undo();\n    }\n    if (path && enabled) {\n        const undo = addEvent(textWrapper, 'afterModifyTree', (e) => {\n            if (path && enabled) {\n                // Set ID for the path\n                let textPathId = path.attr('id');\n                if (!textPathId) {\n                    path.attr('id', textPathId = uniqueKey());\n                }\n                // Set attributes for the <text>\n                const textAttribs = {\n                    // `dx`/`dy` options must by set on <text> (parent), the\n                    // rest should be set on <textPath>\n                    x: 0,\n                    y: 0\n                };\n                if (TextPath_defined(attributes.dx)) {\n                    textAttribs.dx = attributes.dx;\n                    delete attributes.dx;\n                }\n                if (TextPath_defined(attributes.dy)) {\n                    textAttribs.dy = attributes.dy;\n                    delete attributes.dy;\n                }\n                textWrapper.attr(textAttribs);\n                // Handle label properties\n                this.attr({ transform: '' });\n                if (this.box) {\n                    this.box = this.box.destroy();\n                }\n                // Wrap the nodes in a textPath\n                const children = e.nodes.slice(0);\n                e.nodes.length = 0;\n                e.nodes[0] = {\n                    tagName: 'textPath',\n                    attributes: TextPath_extend(attributes, {\n                        'text-anchor': attributes.textAnchor,\n                        href: `${url}#${textPathId}`\n                    }),\n                    children\n                };\n            }\n        });\n        // Set the reference\n        textWrapper.textPath = { path, undo };\n    }\n    else {\n        textWrapper.attr({ dx: 0, dy: 0 });\n        delete textWrapper.textPath;\n    }\n    if (this.added) {\n        // Rebuild text after added\n        textWrapper.textCache = '';\n        this.renderer.buildText(textWrapper);\n    }\n    return this;\n}\n/**\n * Attach a polygon to a bounding box if the element contains a textPath.\n *\n * @function Highcharts.SVGElement#setPolygon\n *\n * @param {any} event\n *        An event containing a bounding box object\n *\n * @return {Highcharts.BBoxObject} Returns the bounding box object.\n */\nfunction setPolygon(event) {\n    const bBox = event.bBox, tp = this.element?.querySelector('textPath');\n    if (tp) {\n        const polygon = [], { b, h } = this.renderer.fontMetrics(this.element), descender = h - b, lineCleanerRegex = new RegExp('(<tspan>|' +\n            '<tspan(?!\\\\sclass=\"highcharts-br\")[^>]*>|' +\n            '<\\\\/tspan>)', 'g'), lines = tp\n            .innerHTML\n            .replace(lineCleanerRegex, '')\n            .split(/<tspan class=\"highcharts-br\"[^>]*>/), numOfLines = lines.length;\n        // Calculate top and bottom coordinates for\n        // either the start or the end of a single\n        // character, and append it to the polygon.\n        const appendTopAndBottom = (charIndex, positionOfChar) => {\n            const { x, y } = positionOfChar, rotation = (tp.getRotationOfChar(charIndex) - 90) * deg2rad, cosRot = Math.cos(rotation), sinRot = Math.sin(rotation);\n            return [\n                [\n                    x - descender * cosRot,\n                    y - descender * sinRot\n                ],\n                [\n                    x + b * cosRot,\n                    y + b * sinRot\n                ]\n            ];\n        };\n        for (let i = 0, lineIndex = 0; lineIndex < numOfLines; lineIndex++) {\n            const line = lines[lineIndex], lineLen = line.length;\n            for (let lineCharIndex = 0; lineCharIndex < lineLen; lineCharIndex += 5) {\n                try {\n                    const srcCharIndex = (i +\n                        lineCharIndex +\n                        lineIndex), [lower, upper] = appendTopAndBottom(srcCharIndex, tp.getStartPositionOfChar(srcCharIndex));\n                    if (lineCharIndex === 0) {\n                        polygon.push(upper);\n                        polygon.push(lower);\n                    }\n                    else {\n                        if (lineIndex === 0) {\n                            polygon.unshift(upper);\n                        }\n                        if (lineIndex === numOfLines - 1) {\n                            polygon.push(lower);\n                        }\n                    }\n                }\n                catch (e) {\n                    // Safari fails on getStartPositionOfChar even if the\n                    // character is within the `textContent.length`\n                    break;\n                }\n            }\n            i += lineLen - 1;\n            try {\n                const srcCharIndex = i + lineIndex, charPos = tp.getEndPositionOfChar(srcCharIndex), [lower, upper] = appendTopAndBottom(srcCharIndex, charPos);\n                polygon.unshift(upper);\n                polygon.unshift(lower);\n            }\n            catch (e) {\n                // Safari fails on getStartPositionOfChar even if the character\n                // is within the `textContent.length`\n                break;\n            }\n        }\n        // Close it\n        if (polygon.length) {\n            polygon.push(polygon[0].slice());\n        }\n        bBox.polygon = polygon;\n    }\n    return bBox;\n}\n/**\n * Draw text along a textPath for a dataLabel.\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {any} event\n *        An event containing label options\n *\n * @return {void}\n */\nfunction drawTextPath(event) {\n    const labelOptions = event.labelOptions, point = event.point, textPathOptions = (labelOptions[point.formatPrefix + 'TextPath'] ||\n        labelOptions.textPath);\n    if (textPathOptions && !labelOptions.useHTML) {\n        this.setTextPath(point.getDataLabelPath?.(this) || point.graphic, textPathOptions);\n        if (point.dataLabelPath &&\n            !textPathOptions.enabled) {\n            // Clean the DOM\n            point.dataLabelPath = (point.dataLabelPath.destroy());\n        }\n    }\n}\nfunction compose(SVGElementClass) {\n    addEvent(SVGElementClass, 'afterGetBBox', setPolygon);\n    addEvent(SVGElementClass, 'beforeAddingDataLabel', drawTextPath);\n    const svgElementProto = SVGElementClass.prototype;\n    if (!svgElementProto.setTextPath) {\n        svgElementProto.setTextPath = setTextPath;\n    }\n}\nconst TextPath = {\n    compose\n};\n/* harmony default export */ const Extensions_TextPath = (TextPath);\n\n;// ./code/es-modules/Series/Sankey/SankeySeries.js\n/* *\n *\n *  Sankey diagram module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\n\n\nconst { column: SankeySeries_ColumnSeries, line: LineSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { parse: color } = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default());\n\nconst { getLevelOptions: SankeySeries_getLevelOptions, getNodeWidth: SankeySeries_getNodeWidth } = Series_TreeUtilities;\n\nconst { clamp, crisp, extend: SankeySeries_extend, isObject: SankeySeries_isObject, merge: SankeySeries_merge, pick: SankeySeries_pick, relativeLength: SankeySeries_relativeLength, stableSort } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nExtensions_TextPath.compose((highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default()));\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.sankey\n *\n * @augments Highcharts.Series\n */\nclass SankeySeries extends SankeySeries_ColumnSeries {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    static getDLOptions(params) {\n        const optionsPoint = (SankeySeries_isObject(params.optionsPoint) ?\n            params.optionsPoint.dataLabels :\n            {}), optionsLevel = (SankeySeries_isObject(params.level) ?\n            params.level.dataLabels :\n            {}), options = SankeySeries_merge({\n            style: {}\n        }, optionsLevel, optionsPoint);\n        return options;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Create node columns by analyzing the nodes and the relations between\n     * incoming and outgoing links.\n     * @private\n     */\n    createNodeColumns() {\n        const columns = [];\n        for (const node of this.nodes) {\n            node.setNodeColumn();\n            if (!columns[node.column]) {\n                columns[node.column] =\n                    Sankey_SankeyColumnComposition.compose([], this);\n            }\n            columns[node.column].push(node);\n        }\n        // Fill in empty columns (#8865)\n        for (let i = 0; i < columns.length; i++) {\n            if (typeof columns[i] === 'undefined') {\n                columns[i] =\n                    Sankey_SankeyColumnComposition.compose([], this);\n            }\n        }\n        return columns;\n    }\n    /**\n     * Order the nodes, starting with the root node(s). (#9818)\n     * @private\n     */\n    order(node, level) {\n        const series = this;\n        // Prevents circular recursion:\n        if (typeof node.level === 'undefined') {\n            node.level = level;\n            for (const link of node.linksFrom) {\n                if (link.toNode) {\n                    series.order(link.toNode, level + 1);\n                }\n            }\n        }\n    }\n    /**\n     * Extend generatePoints by adding the nodes, which are Point objects\n     * but pushed to the this.nodes array.\n     * @private\n     */\n    generatePoints() {\n        Series_NodesComposition.generatePoints.apply(this, arguments);\n        if (this.orderNodes) {\n            for (const node of this.nodes) {\n                // Identify the root node(s)\n                if (node.linksTo.length === 0) {\n                    // Start by the root node(s) and recursively set the level\n                    // on all following nodes.\n                    this.order(node, 0);\n                }\n            }\n            stableSort(this.nodes, (a, b) => (a.level - b.level));\n        }\n    }\n    /**\n     * Overridable function to get node padding, overridden in dependency\n     * wheel series type.\n     * @private\n     */\n    getNodePadding() {\n        let nodePadding = this.options.nodePadding || 0;\n        // If the number of columns is so great that they will overflow with\n        // the given nodePadding, we sacrifice the padding in order to\n        // render all nodes within the plot area (#11917).\n        if (this.nodeColumns) {\n            const maxLength = this.nodeColumns.reduce((acc, col) => Math.max(acc, col.length), 0);\n            if (maxLength * nodePadding > this.chart.plotSizeY) {\n                nodePadding = this.chart.plotSizeY / maxLength;\n            }\n        }\n        return nodePadding;\n    }\n    /**\n     * Define hasData function for non-cartesian series.\n     * @private\n     * @return {boolean}\n     *         Returns true if the series has points at all.\n     */\n    hasData() {\n        return !!this.dataTable.rowCount;\n    }\n    /**\n     * Return the presentational attributes.\n     * @private\n     */\n    pointAttribs(point, state) {\n        if (!point) {\n            return {};\n        }\n        const series = this, level = point.isNode ? point.level : point.fromNode.level, levelOptions = series.mapOptionsToLevel[level || 0] || {}, options = point.options, stateOptions = (levelOptions.states && levelOptions.states[state || '']) || {}, values = [\n            'colorByPoint',\n            'borderColor',\n            'borderWidth',\n            'linkOpacity',\n            'opacity'\n        ].reduce((obj, key) => {\n            obj[key] = SankeySeries_pick(stateOptions[key], options[key], levelOptions[key], series.options[key]);\n            return obj;\n        }, {}), color = SankeySeries_pick(stateOptions.color, options.color, values.colorByPoint ? point.color : levelOptions.color);\n        // Node attributes\n        if (point.isNode) {\n            return {\n                fill: color,\n                stroke: values.borderColor,\n                'stroke-width': values.borderWidth,\n                opacity: values.opacity\n            };\n        }\n        // Link attributes\n        return {\n            fill: color,\n            'fill-opacity': values.linkOpacity\n        };\n    }\n    drawTracker() {\n        SankeySeries_ColumnSeries.prototype.drawTracker.call(this, this.points);\n        SankeySeries_ColumnSeries.prototype.drawTracker.call(this, this.nodes);\n    }\n    drawPoints() {\n        SankeySeries_ColumnSeries.prototype.drawPoints.call(this, this.points);\n        SankeySeries_ColumnSeries.prototype.drawPoints.call(this, this.nodes);\n    }\n    drawDataLabels() {\n        SankeySeries_ColumnSeries.prototype.drawDataLabels.call(this, this.points);\n        SankeySeries_ColumnSeries.prototype.drawDataLabels.call(this, this.nodes);\n    }\n    /**\n     * Run pre-translation by generating the nodeColumns.\n     * @private\n     */\n    translate() {\n        this.generatePoints();\n        this.nodeColumns = this.createNodeColumns();\n        const series = this, chart = this.chart, options = this.options, nodeColumns = this.nodeColumns, columnCount = nodeColumns.length;\n        this.nodeWidth = SankeySeries_getNodeWidth(this, columnCount);\n        this.nodePadding = this.getNodePadding();\n        // Find out how much space is needed. Base it on the translation\n        // factor of the most spacious column.\n        this.translationFactor = nodeColumns.reduce((translationFactor, column) => Math.min(translationFactor, column.sankeyColumn.getTranslationFactor(series)), Infinity);\n        this.colDistance =\n            (chart.plotSizeX - this.nodeWidth -\n                options.borderWidth) / Math.max(1, nodeColumns.length - 1);\n        // Calculate level options used in sankey and organization\n        series.mapOptionsToLevel = SankeySeries_getLevelOptions({\n            // NOTE: if support for allowTraversingTree is added, then from\n            // should be the level of the root node.\n            from: 1,\n            levels: options.levels,\n            to: nodeColumns.length - 1, // Height of the tree\n            defaults: {\n                borderColor: options.borderColor,\n                borderRadius: options.borderRadius, // Organization series\n                borderWidth: options.borderWidth,\n                color: series.color,\n                colorByPoint: options.colorByPoint,\n                // NOTE: if support for allowTraversingTree is added, then\n                // levelIsConstant should be optional.\n                levelIsConstant: true,\n                linkColor: options.linkColor, // Organization series\n                linkLineWidth: options.linkLineWidth, // Organization series\n                linkOpacity: options.linkOpacity,\n                states: options.states\n            }\n        });\n        // First translate all nodes so we can use them when drawing links\n        for (const column of nodeColumns) {\n            for (const node of column) {\n                series.translateNode(node, column);\n            }\n        }\n        // Then translate links\n        for (const node of this.nodes) {\n            // Translate the links from this node\n            for (const linkPoint of node.linksFrom) {\n                // If weight is 0 - don't render the link path #12453,\n                // render null points (for organization chart)\n                if ((linkPoint.weight || linkPoint.isNull) && linkPoint.to) {\n                    series.translateLink(linkPoint);\n                    linkPoint.allowShadow = false;\n                }\n            }\n        }\n    }\n    /**\n     * Run translation operations for one link.\n     * @private\n     */\n    translateLink(point) {\n        const getY = (node, fromOrTo) => {\n            const linkTop = (node.offset(point, fromOrTo) *\n                translationFactor);\n            const y = Math.min(node.nodeY + linkTop, \n            // Prevent links from spilling below the node (#12014)\n            node.nodeY + (node.shapeArgs && node.shapeArgs.height || 0) - linkHeight);\n            return y;\n        };\n        const fromNode = point.fromNode, toNode = point.toNode, chart = this.chart, { inverted } = chart, translationFactor = this.translationFactor, options = this.options, linkColorMode = SankeySeries_pick(point.linkColorMode, options.linkColorMode), curvy = ((chart.inverted ? -this.colDistance : this.colDistance) *\n            options.curveFactor), nodeLeft = fromNode.nodeX, right = toNode.nodeX, outgoing = point.outgoing;\n        let linkHeight = Math.max(point.weight * translationFactor, this.options.minLinkWidth), fromY = getY(fromNode, 'linksFrom'), toY = getY(toNode, 'linksTo'), nodeW = this.nodeWidth, straight = right > nodeLeft + nodeW;\n        if (chart.inverted) {\n            fromY = chart.plotSizeY - fromY;\n            toY = (chart.plotSizeY || 0) - toY;\n            nodeW = -nodeW;\n            linkHeight = -linkHeight;\n            straight = nodeLeft > right;\n        }\n        point.shapeType = 'path';\n        point.linkBase = [\n            fromY,\n            fromY + linkHeight,\n            toY,\n            toY + linkHeight\n        ];\n        // Links going from left to right\n        if (straight && typeof toY === 'number') {\n            point.shapeArgs = {\n                d: [\n                    ['M', nodeLeft + nodeW, fromY],\n                    [\n                        'C',\n                        nodeLeft + nodeW + curvy,\n                        fromY,\n                        right - curvy,\n                        toY,\n                        right,\n                        toY\n                    ],\n                    ['L', right + (outgoing ? nodeW : 0), toY + linkHeight / 2],\n                    ['L', right, toY + linkHeight],\n                    [\n                        'C',\n                        right - curvy,\n                        toY + linkHeight,\n                        nodeLeft + nodeW + curvy,\n                        fromY + linkHeight,\n                        nodeLeft + nodeW, fromY + linkHeight\n                    ],\n                    ['Z']\n                ]\n            };\n            // Experimental: Circular links pointing backwards. In\n            // v6.1.0 this breaks the rendering completely, so even\n            // this experimental rendering is an improvement. #8218.\n            // @todo\n            // - Make room for the link in the layout\n            // - Automatically determine if the link should go up or\n            //   down.\n        }\n        else if (typeof toY === 'number') {\n            const bend = 20, vDist = chart.plotHeight - fromY - linkHeight, x1 = right - bend - linkHeight, x2 = right - bend, x3 = right, x4 = nodeLeft + nodeW, x5 = x4 + bend, x6 = x5 + linkHeight, fy1 = fromY, fy2 = fromY + linkHeight, fy3 = fy2 + bend, y4 = fy3 + vDist, y5 = y4 + bend, y6 = y5 + linkHeight, ty1 = toY, ty2 = ty1 + linkHeight, ty3 = ty2 + bend, cfy1 = fy2 - linkHeight * 0.7, cy2 = y5 + linkHeight * 0.7, cty1 = ty2 - linkHeight * 0.7, cx1 = x3 - linkHeight * 0.7, cx2 = x4 + linkHeight * 0.7;\n            point.shapeArgs = {\n                d: [\n                    ['M', x4, fy1],\n                    ['C', cx2, fy1, x6, cfy1, x6, fy3],\n                    ['L', x6, y4],\n                    ['C', x6, cy2, cx2, y6, x4, y6],\n                    ['L', x3, y6],\n                    ['C', cx1, y6, x1, cy2, x1, y4],\n                    ['L', x1, ty3],\n                    ['C', x1, cty1, cx1, ty1, x3, ty1],\n                    ['L', x3, ty2],\n                    ['C', x2, ty2, x2, ty2, x2, ty3],\n                    ['L', x2, y4],\n                    ['C', x2, y5, x2, y5, x3, y5],\n                    ['L', x4, y5],\n                    ['C', x5, y5, x5, y5, x5, y4],\n                    ['L', x5, fy3],\n                    ['C', x5, fy2, x5, fy2, x4, fy2],\n                    ['Z']\n                ]\n            };\n        }\n        // Place data labels in the middle\n        point.dlBox = {\n            x: nodeLeft + (right - nodeLeft + nodeW) / 2,\n            y: fromY + (toY - fromY) / 2,\n            height: linkHeight,\n            width: 0\n        };\n        // And set the tooltip anchor in the middle\n        point.tooltipPos = chart.inverted ? [\n            chart.plotSizeY - point.dlBox.y - linkHeight / 2,\n            chart.plotSizeX - point.dlBox.x\n        ] : [\n            point.dlBox.x,\n            point.dlBox.y + linkHeight / 2\n        ];\n        // Pass test in drawPoints. plotX/Y needs to be defined for dataLabels.\n        // #15863\n        point.y = point.plotY = 1;\n        point.x = point.plotX = 1;\n        if (!point.options.color) {\n            if (linkColorMode === 'from') {\n                point.color = fromNode.color;\n            }\n            else if (linkColorMode === 'to') {\n                point.color = toNode.color;\n            }\n            else if (linkColorMode === 'gradient') {\n                const fromColor = color(fromNode.color).get(), toColor = color(toNode.color).get();\n                point.color = {\n                    linearGradient: {\n                        x1: 1,\n                        x2: 0,\n                        y1: 0,\n                        y2: 0\n                    },\n                    stops: [\n                        [0, inverted ? fromColor : toColor],\n                        [1, inverted ? toColor : fromColor]\n                    ]\n                };\n            }\n        }\n    }\n    /**\n     * Run translation operations for one node.\n     * @private\n     */\n    translateNode(node, column) {\n        const translationFactor = this.translationFactor, chart = this.chart, options = this.options, { borderRadius, borderWidth = 0 } = options, sum = node.getSum(), nodeHeight = Math.max(Math.round(sum * translationFactor), this.options.minLinkWidth), nodeWidth = Math.round(this.nodeWidth), nodeOffset = column.sankeyColumn.offset(node, translationFactor), fromNodeTop = crisp(SankeySeries_pick(nodeOffset.absoluteTop, (column.sankeyColumn.top(translationFactor) +\n            nodeOffset.relativeTop)), borderWidth), left = crisp(this.colDistance * node.column +\n            borderWidth / 2, borderWidth) + SankeySeries_relativeLength(node.options[chart.inverted ?\n            'offsetVertical' :\n            'offsetHorizontal'] || 0, nodeWidth), nodeLeft = chart.inverted ?\n            chart.plotSizeX - left :\n            left;\n        node.sum = sum;\n        // If node sum is 0, don't render the rect #12453\n        if (sum) {\n            // Draw the node\n            node.shapeType = 'roundedRect';\n            node.nodeX = nodeLeft;\n            node.nodeY = fromNodeTop;\n            let x = nodeLeft, y = fromNodeTop, width = node.options.width || options.width || nodeWidth, height = node.options.height || options.height || nodeHeight;\n            // Border radius should not greater than half the height of the node\n            // #18956\n            const r = clamp(SankeySeries_relativeLength((typeof borderRadius === 'object' ?\n                borderRadius.radius :\n                borderRadius || 0), width), 0, nodeHeight / 2);\n            if (chart.inverted) {\n                x = nodeLeft - nodeWidth;\n                y = chart.plotSizeY - fromNodeTop - nodeHeight;\n                width = node.options.height || options.height || nodeWidth;\n                height = node.options.width || options.width || nodeHeight;\n            }\n            // Calculate data label options for the point\n            node.dlOptions = SankeySeries.getDLOptions({\n                level: this.mapOptionsToLevel[node.level],\n                optionsPoint: node.options\n            });\n            // Pass test in drawPoints\n            node.plotX = 1;\n            node.plotY = 1;\n            // Set the anchor position for tooltips\n            node.tooltipPos = chart.inverted ? [\n                chart.plotSizeY - y - height / 2,\n                chart.plotSizeX - x - width / 2\n            ] : [\n                x + width / 2,\n                y + height / 2\n            ];\n            node.shapeArgs = {\n                x,\n                y,\n                width,\n                height,\n                r,\n                display: node.hasShape() ? '' : 'none'\n            };\n        }\n        else {\n            node.dlOptions = {\n                enabled: false\n            };\n        }\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nSankeySeries.defaultOptions = SankeySeries_merge(SankeySeries_ColumnSeries.defaultOptions, Sankey_SankeySeriesDefaults);\nSeries_NodesComposition.compose(Sankey_SankeyPoint, SankeySeries);\nSankeySeries_extend(SankeySeries.prototype, {\n    animate: LineSeries.prototype.animate,\n    // Create a single node that holds information on incoming and outgoing\n    // links.\n    createNode: Series_NodesComposition.createNode,\n    forceDL: true,\n    invertible: true,\n    isCartesian: false,\n    orderNodes: true,\n    noSharedTooltip: true,\n    pointArrayMap: ['from', 'to', 'weight'],\n    pointClass: Sankey_SankeyPoint,\n    searchPoint: (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).noop\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('sankey', SankeySeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sankey_SankeySeries = ((/* unused pure expression or super */ null && (SankeySeries)));\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * A node in a sankey diagram.\n *\n * @interface Highcharts.SankeyNodeObject\n * @extends Highcharts.Point\n * @product highcharts\n */ /**\n* The color of the auto generated node.\n*\n* @name Highcharts.SankeyNodeObject#color\n* @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n*/ /**\n* The color index of the auto generated node, especially for use in styled\n* mode.\n*\n* @name Highcharts.SankeyNodeObject#colorIndex\n* @type {number}\n*/ /**\n* An optional column index of where to place the node. The default behaviour is\n* to place it next to the preceding node.\n*\n* @see {@link https://jsfiddle.net/gh/get/library/pure/highcharts/highcharts/tree/master/samples/highcharts/plotoptions/sankey-node-column/|Highcharts-Demo:}\n*      Specified node column\n*\n* @name Highcharts.SankeyNodeObject#column\n* @type {number}\n* @since 6.0.5\n*/ /**\n* The id of the auto-generated node, refering to the `from` or `to` setting of\n* the link.\n*\n* @name Highcharts.SankeyNodeObject#id\n* @type {string}\n*/ /**\n* The name to display for the node in data labels and tooltips. Use this when\n* the name is different from the `id`. Where the id must be unique for each\n* node, this is not necessary for the name.\n*\n* @see {@link https://jsfiddle.net/gh/get/library/pure/highcharts/highcharts/tree/master/samples/highcharts/css/sankey/|Highcharts-Demo:}\n*         Sankey diagram with node options\n*\n* @name Highcharts.SankeyNodeObject#name\n* @type {string}\n* @product highcharts\n*/ /**\n* This option is deprecated, use\n* {@link Highcharts.SankeyNodeObject#offsetHorizontal} and\n* {@link Highcharts.SankeyNodeObject#offsetVertical} instead.\n*\n* The vertical offset of a node in terms of weight. Positive values shift the\n* node downwards, negative shift it upwards.\n*\n* If a percentage string is given, the node is offset by the percentage of the\n* node size plus `nodePadding`.\n*\n* @see {@link https://jsfiddle.net/gh/get/library/pure/highcharts/highcharts/tree/master/samples/highcharts/plotoptions/sankey-node-column/|Highcharts-Demo:}\n*         Specified node offset\n*\n* @deprecated\n* @name Highcharts.SankeyNodeObject#offset\n* @type {number|string}\n* @default 0\n* @since 6.0.5\n*/ /**\n* The horizontal offset of a node. Positive values shift the node right,\n* negative shift it left.\n*\n* If a percentage string is given, the node is offset by the percentage of the\n* node size.\n*\n* @see {@link https://jsfiddle.net/gh/get/library/pure/highcharts/highcharts/tree/master/samples/highcharts/plotoptions/sankey-node-column/|Highcharts-Demo:}\n*         Specified node offset\n*\n* @name Highcharts.SankeyNodeObject#offsetHorizontal\n* @type {number|string}\n* @since 9.3.0\n*/ /**\n* The vertical offset of a node. Positive values shift the node down,\n* negative shift it up.\n*\n* If a percentage string is given, the node is offset by the percentage of the\n* node size.\n*\n* @see {@link https://jsfiddle.net/gh/get/library/pure/highcharts/highcharts/tree/master/samples/highcharts/plotoptions/sankey-node-column/|Highcharts-Demo:}\n*         Specified node offset\n*\n* @name Highcharts.SankeyNodeObject#offsetVertical\n* @type {number|string}\n* @since 9.3.0\n*/\n/**\n * Formatter callback function.\n *\n * @callback Highcharts.SeriesSankeyDataLabelsFormatterCallbackFunction\n *\n * @param {Highcharts.Point} this\n *        Data label context to format\n *\n * @return {string|undefined}\n *         Formatted data label text\n */\n''; // Detach doclets above\n\n;// ./code/es-modules/masters/modules/sankey.js\n\n\n\n\n/* harmony default export */ const sankey_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__260__", "__WEBPACK_EXTERNAL_MODULE__620__", "__WEBPACK_EXTERNAL_MODULE__28__", "NodesComposition", "SankeyColumnComposition", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "sankey_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "series", "seriesProto", "pointClass", "pointProto", "defined", "extend", "find", "merge", "pick", "destroy", "data", "concat", "points", "nodes", "apply", "arguments", "setData", "for<PERSON>ach", "node", "length", "setNodeState", "state", "args", "others", "isNode", "linksTo", "linksFrom", "fromNode", "toNode", "linkOrNode", "setState", "graphic", "updateNode", "options", "redraw", "animation", "runEvent", "dataLength", "linkConfig", "index", "update", "nodeIndex", "reduce", "prevIndex", "id", "nodeConfig", "push", "chart", "compose", "PointClass", "SeriesClass", "createNode", "findById", "newNode", "className", "y", "getSum", "sumTo", "sumFrom", "link", "weight", "Math", "max", "offset", "point", "coll", "i", "<PERSON><PERSON><PERSON><PERSON>", "outgoing", "formatPrefix", "name", "mass", "marker", "radius", "generatePoints", "nodeLookup", "colorCounter", "level", "from", "styledMode", "colorIndex", "color", "to", "Series_NodesComposition", "highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_", "highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default", "column", "ColumnSeries", "seriesTypes", "SankeyPoint_defined", "SankeyPoint", "applyOptions", "x", "getClassName", "getFromNode", "fromColumn", "setNodeColumn", "<PERSON><PERSON><PERSON><PERSON>", "SankeyColumnComposition_defined", "getAlignFactor", "<PERSON><PERSON><PERSON><PERSON>", "sankeyColumnArray", "sankeyColumn", "SankeyColumnAdditions", "constructor", "getTranslationFactor", "slice", "minLinkWidth", "skipPoint", "factor", "remainingHeight", "plotSizeY", "borderWidth", "nodePadding", "sum", "splice", "top", "height", "nodeAlignment", "left", "equalNodes", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "inverted", "plotHeight", "plot<PERSON>id<PERSON>", "width", "plotSizeX", "round", "totalNodeOffset", "is", "hangsFrom", "absoluteTop", "nodeY", "directionOffset", "optionOffset", "relativeTop", "Sankey_SankeyColumnComposition", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default", "TreeUtilities_extend", "isArray", "isNumber", "isObject", "TreeUtilities_merge", "TreeUtilities_pick", "TreeUtilities_relativeLength", "highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_", "highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default", "deg2rad", "addEvent", "TextPath_merge", "<PERSON><PERSON><PERSON>", "TextPath_defined", "TextPath_extend", "setTextPath", "path", "textPathOptions", "enabled", "attributes", "dy", "startOffset", "textAnchor", "url", "renderer", "textWrapper", "text", "textPath", "undo", "e", "textPathId", "attr", "textAttribs", "dx", "transform", "box", "children", "tagName", "href", "added", "textCache", "buildText", "setPolygon", "event", "bBox", "tp", "element", "querySelector", "polygon", "b", "h", "fontMetrics", "descender", "lineCleanerRegex", "RegExp", "lines", "innerHTML", "replace", "split", "numOfLines", "appendTopAndBottom", "charIndex", "positionOfChar", "rotation", "getRotationOfChar", "cosRot", "cos", "sinRot", "sin", "lineIndex", "lineLen", "line", "lineCharIndex", "srcCharIndex", "lower", "upper", "getStartPositionOfChar", "unshift", "char<PERSON><PERSON>", "getEndPositionOfChar", "drawTextPath", "labelOptions", "useHTML", "getDataLabelPath", "dataLabelPath", "SankeySeries_ColumnSeries", "LineSeries", "parse", "getLevelOptions", "SankeySeries_getLevelOptions", "getNodeWidth", "SankeySeries_getNodeWidth", "getColor", "colorByPoint", "colorIndexByPoint", "mapOptionsToLevel", "parentColor", "parentColorIndex", "colors", "siblings", "chartOptionsChart", "colorCount", "variateColor", "colorVariation", "brighten", "params", "defaults", "converted", "levels", "result", "item", "levelIsConstant", "columnCount", "nodeDistance", "nodeWidth", "test", "fraction", "parseFloat", "nDistance", "Number", "setTreeValues", "tree", "before", "idRoot", "nodeRoot", "mapIdToNode", "optionsPoint", "childrenTotal", "levelDynamic", "visible", "child", "newOptions", "val", "value", "<PERSON><PERSON><PERSON><PERSON>", "updateRootId", "rootId", "rootNode", "userOptions", "clamp", "crisp", "SankeySeries_extend", "SankeySeries_isObject", "SankeySeries_merge", "SankeySeries_pick", "SankeySeries_relativeLength", "stableSort", "Extensions_TextPath", "SVGElementClass", "svgElementProto", "SankeySeries", "getDLOptions", "dataLabels", "style", "createNodeColumns", "columns", "order", "orderNodes", "getNodePadding", "nodeColumns", "max<PERSON><PERSON><PERSON>", "acc", "col", "hasData", "dataTable", "rowCount", "pointAttribs", "levelOptions", "stateOptions", "states", "values", "fill", "stroke", "borderColor", "opacity", "linkOpacity", "drawTracker", "drawPoints", "drawDataLabels", "translate", "translationFactor", "min", "Infinity", "colDistance", "borderRadius", "linkColor", "linkLineWidth", "translateNode", "linkPoint", "isNull", "translateLink", "allowShadow", "getY", "fromOrTo", "linkTop", "shapeArgs", "linkHeight", "linkColorMode", "curvy", "curveFactor", "nodeLeft", "nodeX", "right", "fromY", "toY", "nodeW", "straight", "shapeType", "linkBase", "vDist", "x1", "x2", "x4", "x5", "x6", "fy1", "fy2", "fy3", "y4", "y5", "y6", "ty1", "ty2", "ty3", "cfy1", "cy2", "cty1", "cx1", "x3", "cx2", "dlBox", "tooltipPos", "plotY", "plotX", "fromColor", "toColor", "linearGradient", "y1", "y2", "stops", "nodeHeight", "nodeOffset", "fromNodeTop", "r", "dlOptions", "display", "defaultOptions", "backgroundColor", "crop", "nodeFormat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "format", "formatter", "inside", "inactiveOtherPoints", "showInLegend", "hover", "inactive", "duration", "tooltip", "followPointer", "headerFormat", "pointFormat", "animate", "forceDL", "invertible", "isCartesian", "noSharedTooltip", "pointArrayMap", "searchPoint", "noop", "registerSeriesType"], "mappings": "CAWA,AAXA;;;;;;;;;;CAUC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,UAAa,EAC3K,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,4BAA6B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAACA,EAAK,KAAQ,CAACA,EAAK,KAAQ,CAACA,EAAK,UAAa,CAAE,GACpK,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,4BAA4B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,UAAa,EAExMA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,UAAa,CACnL,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,IAC3K,AAAC,CAAA,KACP,aACA,IA2HNC,EAu8BAC,EAlkCUC,EAAuB,CAE/B,GACC,AAACZ,IAERA,EAAOD,OAAO,CAAGU,CAEX,EAEA,IACC,AAACT,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGS,CAEX,EAEA,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIQ,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAajB,OAAO,CAG5B,IAAIC,EAASa,CAAwB,CAACE,EAAS,CAAG,CAGjDhB,QAAS,CAAC,CACX,EAMA,OAHAa,CAAmB,CAACG,EAAS,CAACf,EAAQA,EAAOD,OAAO,CAAEe,GAG/Cd,EAAOD,OAAO,AACtB,CAMCe,EAAoBI,CAAC,CAAG,AAAClB,IACxB,IAAImB,EAASnB,GAAUA,EAAOoB,UAAU,CACvC,IAAOpB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAc,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACtB,EAASwB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAAC1B,EAASyB,IAC5EE,OAAOC,cAAc,CAAC5B,EAASyB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,EAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GASjL,GAAM,CAAEE,OAAQ,CAAET,UAAWU,CAAW,CAAEV,UAAW,CAAEW,WAAY,CAAEX,UAAWY,CAAU,CAAE,CAAE,CAAE,CAAE,CAAIJ,IAEhG,CAAEK,QAAAA,CAAO,CAAEC,OAAAA,CAAM,CAAEC,KAAAA,CAAI,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAE,CAAIX,KAOhD,AAAC,SAAU5B,CAAgB,EAsGvB,SAASwC,IAIL,OAFA,IAAI,CAACC,IAAI,CAAG,EAAE,CACTC,MAAM,CAAC,IAAI,CAACC,MAAM,EAAI,EAAE,CAAE,IAAI,CAACC,KAAK,EAClCZ,EAAYQ,OAAO,CAACK,KAAK,CAAC,IAAI,CAAEC,UAC3C,CAsDA,SAASC,IACD,IAAI,CAACH,KAAK,GACV,IAAI,CAACA,KAAK,CAACI,OAAO,CAAC,AAACC,IAChBA,EAAKT,OAAO,EAChB,GACA,IAAI,CAACI,KAAK,CAACM,MAAM,CAAG,GAExBlB,EAAYe,OAAO,CAACF,KAAK,CAAC,IAAI,CAAEC,UACpC,CAMA,SAASK,EAAaC,CAAK,EACvB,IAAMC,EAAOP,UAAWQ,EAAS,IAAI,CAACC,MAAM,CAAG,IAAI,CAACC,OAAO,CAACd,MAAM,CAAC,IAAI,CAACe,SAAS,EAC7E,CAAC,IAAI,CAACC,QAAQ,CAAE,IAAI,CAACC,MAAM,CAAC,AAC5BP,AAAU,CAAA,WAAVA,GACAE,EAAON,OAAO,CAAC,AAACY,IACRA,GAAcA,EAAW7B,MAAM,GAC/BG,EAAW2B,QAAQ,CAAChB,KAAK,CAACe,EAAYP,GAClC,CAACO,EAAWL,MAAM,GACdK,EAAWF,QAAQ,CAACI,OAAO,EAC3B5B,EAAW2B,QAAQ,CAAChB,KAAK,CAACe,EAAWF,QAAQ,CAAEL,GAE/CO,EAAWD,MAAM,EAAIC,EAAWD,MAAM,CAACG,OAAO,EAC9C5B,EAAW2B,QAAQ,CAAChB,KAAK,CAACe,EAAWD,MAAM,CAAEN,IAI7D,GAEJnB,EAAW2B,QAAQ,CAAChB,KAAK,CAAC,IAAI,CAAEQ,EACpC,CAOA,SAASU,EAAWC,CAAO,CAAEC,CAAM,CAAEC,CAAS,CAAEC,CAAQ,EACpD,IAAMvB,EAAQ,IAAI,CAACb,MAAM,CAACiC,OAAO,CAACpB,KAAK,CAAEH,EAAO,IAAI,CAACV,MAAM,CAACiC,OAAO,CAACvB,IAAI,CAAE2B,EAAa3B,GAAMS,QAAU,EAAGmB,EAAa5B,GAAM,CAAC,IAAI,CAAC6B,KAAK,CAAC,CAGzI,GAFApC,EAAWqC,MAAM,CAAC/C,IAAI,CAAC,IAAI,CAAEwC,EAAS,CAAA,IAAI,CAACT,MAAM,EAAWU,EAC5DC,EAAWC,GACP,IAAI,CAACZ,MAAM,CAAE,CAEb,IAAMiB,EAAY,AAAC5B,CAAAA,GAAS,EAAE,AAAD,EACxB6B,MAAM,CACX,CAACC,EAAWlE,EAAG8D,IAAW,IAAI,CAACK,EAAE,GAAKnE,EAAEmE,EAAE,CAAGL,EAAQI,EAAY,IAGjEE,EAAatC,EAAMM,GAASA,CAAK,CAAC4B,EAAU,EAAI,CAAC,EAAG/B,GAAM,CAAC,IAAI,CAAC6B,KAAK,CAAC,EAAI,CAAC,GAEvE7B,IACI4B,EACA5B,CAAI,CAAC,IAAI,CAAC6B,KAAK,CAAC,CAAGD,EAInB5B,EAAKS,MAAM,CAAGkB,GAIlBxB,EACI4B,GAAa,EACb5B,CAAK,CAAC4B,EAAU,CAAGI,EAGnBhC,EAAMiC,IAAI,CAACD,GAIf,IAAI,CAAC7C,MAAM,CAACiC,OAAO,CAACpB,KAAK,CAAG,CAACgC,EAAW,CAExCrC,EAAK0B,EAAQ,CAAA,IACb,IAAI,CAAClC,MAAM,CAAC+C,KAAK,CAACb,MAAM,CAACC,EAEjC,CACJ,CAxNAlE,EAAiB+E,OAAO,CATxB,SAAiBC,CAAU,CAAEC,CAAW,EACpC,IAAM/C,EAAa8C,EAAW1D,SAAS,CAAEU,EAAciD,EAAY3D,SAAS,CAM5E,OALAY,EAAWiB,YAAY,CAAGA,EAC1BjB,EAAW2B,QAAQ,CAAGV,EACtBjB,EAAWqC,MAAM,CAAGR,EACpB/B,EAAYQ,OAAO,CAAGA,EACtBR,EAAYe,OAAO,CAAGA,EACfkC,CACX,EA2EAjF,EAAiBkF,UAAU,CApE3B,SAAoBP,CAAE,EAClB,IAAMK,EAAa,IAAI,CAAC/C,UAAU,CAAEkD,EAAW,CAACvC,EAAO+B,IAAOtC,EAAKO,EAAO,AAACK,GAASA,EAAK0B,EAAE,GAAKA,GAC5F1B,EAAOkC,EAAS,IAAI,CAACvC,KAAK,CAAE+B,GAAKX,EACrC,GAAI,CAACf,EAAM,CACPe,EAAU,IAAI,CAACA,OAAO,CAACpB,KAAK,EAAIuC,EAAS,IAAI,CAACnB,OAAO,CAACpB,KAAK,CAAE+B,GAC7D,IAAMS,EAAU,IAAIJ,EAAW,IAAI,CAAE5C,EAAO,CACxCiD,UAAW,kBACX9B,OAAQ,CAAA,EACRoB,GAAIA,EACJW,EAAG,CACP,EAAGtB,GACHoB,CAAAA,EAAQ5B,OAAO,CAAG,EAAE,CACpB4B,EAAQ3B,SAAS,CAAG,EAAE,CAKtB2B,EAAQG,MAAM,CAAG,WACb,IAAIC,EAAQ,EAAGC,EAAU,EAOzB,OANAL,EAAQ5B,OAAO,CAACR,OAAO,CAAC,AAAC0C,IACrBF,GAASE,EAAKC,MAAM,EAAI,CAC5B,GACAP,EAAQ3B,SAAS,CAACT,OAAO,CAAC,AAAC0C,IACvBD,GAAWC,EAAKC,MAAM,EAAI,CAC9B,GACOC,KAAKC,GAAG,CAACL,EAAOC,EAC3B,EAKAL,EAAQU,MAAM,CAAG,SAAUC,CAAK,CAAEC,CAAI,EAClC,IAAIF,EAAS,EACb,IAAK,IAAIG,EAAI,EAAGA,EAAIb,CAAO,CAACY,EAAK,CAAC9C,MAAM,CAAE+C,IAAK,CAC3C,GAAIb,CAAO,CAACY,EAAK,CAACC,EAAE,GAAKF,EACrB,OAAOD,EAEXA,GAAUV,CAAO,CAACY,EAAK,CAACC,EAAE,CAACN,MAAM,AACrC,CACJ,EAGAP,EAAQc,QAAQ,CAAG,WACf,IAAIC,EAAW,EAMf,OALAf,EAAQ5B,OAAO,CAACR,OAAO,CAAC,AAAC0C,IACjBA,EAAKS,QAAQ,EACbA,GAER,GACQ,CAACf,EAAQ5B,OAAO,CAACN,MAAM,EAC3BiD,IAAaf,EAAQ5B,OAAO,CAACN,MAAM,AAC3C,EACAkC,EAAQd,KAAK,CAAG,IAAI,CAAC1B,KAAK,CAACiC,IAAI,CAACO,GAAW,EAC3CnC,EAAOmC,CACX,CAYA,OAXAnC,EAAKmD,YAAY,CAAG,OAEpBnD,EAAKoD,IAAI,CAAGpD,EAAKoD,IAAI,EAAIpD,EAAKe,OAAO,CAACW,EAAE,EAAI,GAE5C1B,EAAKqD,IAAI,CAAG/D,EAEZU,EAAKe,OAAO,CAACsC,IAAI,CAAErD,EAAKe,OAAO,CAACuC,MAAM,EAAItD,EAAKe,OAAO,CAACuC,MAAM,CAACC,MAAM,CAEpE,IAAI,CAACxC,OAAO,CAACuC,MAAM,EAAI,IAAI,CAACvC,OAAO,CAACuC,MAAM,CAACC,MAAM,CAEjD,GACOvD,CACX,EAYAjD,EAAiBwC,OAAO,CAAGA,EAgD3BxC,EAAiByG,cAAc,CA1C/B,WACI,IAAM3B,EAAQ,IAAI,CAACA,KAAK,CAAE4B,EAAa,CAAC,EACxC1E,EAAYyE,cAAc,CAACjF,IAAI,CAAC,IAAI,EAChC,AAAC,IAAI,CAACoB,KAAK,EACX,CAAA,IAAI,CAACA,KAAK,CAAG,EAAE,AAAD,EAElB,IAAI,CAAC+D,YAAY,CAAG,EAEpB,IAAI,CAAC/D,KAAK,CAACI,OAAO,CAAC,AAACC,IAChBA,EAAKQ,SAAS,CAACP,MAAM,CAAG,EACxBD,EAAKO,OAAO,CAACN,MAAM,CAAG,EACtBD,EAAK2D,KAAK,CAAG3D,EAAKe,OAAO,CAAC4C,KAAK,AACnC,GAEA,IAAI,CAACjE,MAAM,CAACK,OAAO,CAAC,AAAC+C,IACb5D,EAAQ4D,EAAMc,IAAI,IACd,AAACH,CAAU,CAACX,EAAMc,IAAI,CAAC,EACvBH,CAAAA,CAAU,CAACX,EAAMc,IAAI,CAAC,CAAG,IAAI,CAAC3B,UAAU,CAACa,EAAMc,IAAI,CAAA,EAEvDH,CAAU,CAACX,EAAMc,IAAI,CAAC,CAACpD,SAAS,CAACoB,IAAI,CAACkB,GACtCA,EAAMrC,QAAQ,CAAGgD,CAAU,CAACX,EAAMc,IAAI,CAAC,CAEnC/B,EAAMgC,UAAU,CAChBf,EAAMgB,UAAU,CAAGxE,EAAKwD,EAAM/B,OAAO,CAAC+C,UAAU,CAAEL,CAAU,CAACX,EAAMc,IAAI,CAAC,CAACE,UAAU,EAGnFhB,EAAMiB,KAAK,CACPjB,EAAM/B,OAAO,CAACgD,KAAK,EAAIN,CAAU,CAACX,EAAMc,IAAI,CAAC,CAACG,KAAK,EAG3D7E,EAAQ4D,EAAMkB,EAAE,IACZ,AAACP,CAAU,CAACX,EAAMkB,EAAE,CAAC,EACrBP,CAAAA,CAAU,CAACX,EAAMkB,EAAE,CAAC,CAAG,IAAI,CAAC/B,UAAU,CAACa,EAAMkB,EAAE,CAAA,EAEnDP,CAAU,CAACX,EAAMkB,EAAE,CAAC,CAACzD,OAAO,CAACqB,IAAI,CAACkB,GAClCA,EAAMpC,MAAM,CAAG+C,CAAU,CAACX,EAAMkB,EAAE,CAAC,EAEvClB,EAAMM,IAAI,CAAGN,EAAMM,IAAI,EAAIN,EAAMpB,EAAE,AACvC,EAAG,IAAI,EAEP,IAAI,CAAC+B,UAAU,CAAGA,CACtB,EAwCA1G,EAAiBmD,YAAY,CAAGA,EA6ChCnD,EAAiB+D,UAAU,CAAGA,CAClC,EAAG/D,GAAqBA,CAAAA,EAAmB,CAAC,CAAA,GAMf,IAAMkH,EAA2BlH,EAG9D,IAAImH,EAA+F/G,EAAoB,KACnHgH,EAAmHhH,EAAoBI,CAAC,CAAC2G,GAgB7I,GAAM,CAAEE,OAAQC,CAAY,CAAE,CAAG,AAACxF,IAA2IyF,WAAW,CAElL,CAAEpF,QAASqF,CAAmB,CAAE,CAAI5F,GAM1C,OAAM6F,UAAoBH,EAAahG,SAAS,CAACW,UAAU,CASvDyF,aAAa1D,CAAO,CAAE2D,CAAC,CAAE,CAMrB,OALAP,IAAsG9F,SAAS,CAACoG,YAAY,CAAClG,IAAI,CAAC,IAAI,CAAEwC,EAAS2D,GAE7IH,EAAoB,IAAI,CAACxD,OAAO,CAAC4C,KAAK,GACtC,CAAA,IAAI,CAAC5C,OAAO,CAACqD,MAAM,CAAG,IAAI,CAACA,MAAM,CAAG,IAAI,CAACrD,OAAO,CAAC4C,KAAK,AAAD,EAElD,IAAI,AACf,CAIAgB,cAAe,CACX,MAAO,AAAC,CAAA,IAAI,CAACrE,MAAM,CAAG,mBAAqB,kBAAiB,EACxD6D,IAAsG9F,SAAS,CAACsG,YAAY,CAACpG,IAAI,CAAC,IAAI,CAC9I,CAOAqG,aAAc,CAEV,IAAIC,EAAa,GAAIpE,EACrB,IAAK,IAAIuC,EAAI,EAAGA,EAAIhD,AAFP,IAAI,CAEQO,OAAO,CAACN,MAAM,CAAE+C,IAAK,CAC1C,IAAMF,EAAQ9C,AAHL,IAAI,CAGMO,OAAO,CAACyC,EAAE,AACzBF,CAAAA,EAAMrC,QAAQ,CAAC2D,MAAM,CAAGS,GACxB/B,EAAMrC,QAAQ,GALT,IAAI,EAQToE,CAAAA,EAAapE,AADbA,CAAAA,EAAWqC,EAAMrC,QAAQ,AAAD,EACF2D,MAAM,AAAD,CAEnC,CACA,MAAO,CAAE3D,SAAAA,EAAUoE,WAAAA,CAAW,CAClC,CAKAC,eAAgB,CAEPP,EAAoBvE,AADZ,IAAI,CACae,OAAO,CAACqD,MAAM,IAEpCpE,AAAwB,IAAxBA,AAHK,IAAI,CAGJO,OAAO,CAACN,MAAM,CACnBD,AAJK,IAAI,CAIJoE,MAAM,CAAG,EAGdpE,AAPK,IAAI,CAOJoE,MAAM,CAAGpE,AAPT,IAAI,CAOU4E,WAAW,GAAGC,UAAU,CAAG,EAG1D,CAIAE,SAAU,CACN,OAAO,IAAI,CAACzE,MAAM,EAAI,AAAuB,UAAvB,OAAO,IAAI,CAACoC,MAAM,AAC5C,CACJ,CA2mBA,GAAM,CAAExD,QAAS8F,CAA+B,CAAEC,eAAAA,CAAc,CAAEC,eAAAA,CAAc,CAAE,CAAIvG,KAOtF,AAAC,SAAU3B,CAAuB,EA4B9BA,EAAwB8E,OAAO,CAN/B,SAAiBpC,CAAM,CAAEZ,CAAM,EAI3B,OAFAqG,AAD0BzF,EACR0F,YAAY,CAC1B,IAAIC,EAFkB3F,EAEuBZ,GAFvBY,CAI9B,CAOA,OAAM2F,EAMFC,YAAY5F,CAAM,CAAEZ,CAAM,CAAE,CACxB,IAAI,CAACY,MAAM,CAAGA,EACd,IAAI,CAACZ,MAAM,CAAGA,CAClB,CAgBAyG,qBAAqBzG,CAAM,CAAE,CACzB,IAAMsF,EAAS,IAAI,CAAC1E,MAAM,CAAEC,EAAQyE,EAAOoB,KAAK,GAAI3D,EAAQ/C,EAAO+C,KAAK,CAAE4D,EAAe3G,EAAOiC,OAAO,CAAC0E,YAAY,EAAI,EACpHC,EAAWC,EAAS,EAAG3C,EAAG4C,EAAmB,AAAC/D,CAAAA,EAAMgE,SAAS,EAAI,CAAA,EAChE/G,CAAAA,EAAOiC,OAAO,CAAC+E,WAAW,EAAI,CAAA,EAC/B,AAAC1B,CAAAA,EAAOnE,MAAM,CAAG,CAAA,EAAKnB,EAAOiH,WAAW,CAK5C,KAAO3B,EAAOnE,MAAM,EAAE,CAIlB,IAHA0F,EAASC,EAAkBxB,EAAOgB,YAAY,CAACY,GAAG,GAClDN,EAAY,CAAA,EACZ1C,EAAIoB,EAAOnE,MAAM,CACV+C,KACCoB,CAAM,CAACpB,EAAE,CAACV,MAAM,GAAKqD,EAASF,IAC9BrB,EAAO6B,MAAM,CAACjD,EAAG,GACjB4C,EACIjD,KAAKC,GAAG,CAAC,EAAGgD,EAAkBH,GAClCC,EAAY,CAAA,GAGpB,GAAI,CAACA,EACD,KAER,CAGA,IAAK,IAAM1F,KADXoE,EAAOnE,MAAM,CAAG,EACGN,GACfyE,EAAOxC,IAAI,CAAC5B,GAEhB,OAAO2F,CACX,CAWAO,IAAIP,CAAM,CAAE,CACR,IAAM7G,EAAS,IAAI,CAACA,MAAM,CAAEiH,EAAcjH,EAAOiH,WAAW,CAAEI,EAAS,IAAI,CAACzG,MAAM,CAAC8B,MAAM,CAAC,CAAC2E,EAAQnG,KAC3FmG,EAAS,GACTA,CAAAA,GAAUJ,CAAU,EAGxBI,GADmBxD,KAAKC,GAAG,CAAC5C,EAAKsC,MAAM,GAAKqD,EAAQ7G,EAAOiC,OAAO,CAAC0E,YAAY,EAAI,IAGpF,GAEH,OAAOR,EAAenG,EAAOiC,OAAO,CAACqF,aAAa,EAAI,UAAa,CAAA,AAACtH,CAAAA,EAAO+C,KAAK,CAACgE,SAAS,EAAI,CAAA,EAAKM,CAAK,CAC5G,CAWAE,KAAKV,CAAM,CAAE,CACT,IAAM7G,EAAS,IAAI,CAACA,MAAM,CAAE+C,EAAQ/C,EAAO+C,KAAK,CAAEyE,EAAaxH,EAAOiC,OAAO,CAACuF,UAAU,CAAEC,EAAkB1E,EAAM2E,QAAQ,CAAG3E,EAAM4E,UAAU,CAAG5E,EAAM6E,SAAS,CAAGX,EAAcjH,EAAOiH,WAAW,CAAEY,EAAQ,IAAI,CAACjH,MAAM,CAAC8B,MAAM,CAAC,CAACmF,EAAO3G,KAC/N2G,EAAQ,GACRA,CAAAA,GAASZ,CAAU,EAMvBY,GAJkBL,EACdC,EAAiBvG,EAAKlB,MAAM,CAACa,KAAK,CAACM,MAAM,CACrC8F,EACJpD,KAAKC,GAAG,CAAC5C,EAAKsC,MAAM,GAAKqD,EAAQ7G,EAAOiC,OAAO,CAAC0E,YAAY,EAAI,IAGrE,GACH,MAAO,AAAC,CAAA,AAAC5D,CAAAA,EAAM+E,SAAS,EAAI,CAAA,EAAKjE,KAAKkE,KAAK,CAACF,EAAK,EAAK,CAC1D,CAYAX,KAAM,CACF,OAAO,IAAI,CAACtG,MAAM,CAAC8B,MAAM,CAAC,CAACwE,EAAKhG,IAAUgG,EAAMhG,EAAKsC,MAAM,GAAK,EACpE,CAaAO,OAAO7C,CAAI,CAAE2F,CAAM,CAAE,CACjB,IAAMvB,EAAS,IAAI,CAAC1E,MAAM,CAAEZ,EAAS,IAAI,CAACA,MAAM,CAAEiH,EAAcjH,EAAOiH,WAAW,CAC9ElD,EAAS,EAAGiE,EAChB,GAAIhI,EAAOiI,EAAE,CAAC,iBAAmB/G,EAAKgH,SAAS,CAC3C,MAAO,CACHC,YAAajH,EAAKgH,SAAS,CAACE,KAAK,AACrC,EAEJ,IAAK,IAAIlE,EAAI,EAAGA,EAAIoB,EAAOnE,MAAM,CAAE+C,IAAK,CACpC,IAAMgD,EAAM5B,CAAM,CAACpB,EAAE,CAACV,MAAM,GACtB6D,EAASxD,KAAKC,GAAG,CAACoD,EAAML,EAAQ7G,EAAOiC,OAAO,CAAC0E,YAAY,EAAI,GAC/D0B,EAAkBnH,EAAKe,OAAO,CAACjC,EAAO+C,KAAK,CAAC2E,QAAQ,CACtD,mBACA,iBAAiB,CAAEY,EAAepH,EAAKe,OAAO,CAAC8B,MAAM,EAAI,EAQ7D,GANIiE,EADAd,EACkBG,EAASJ,EAIT,EAElB3B,CAAM,CAACpB,EAAE,GAAKhD,EACd,MAAO,CACHqH,YAAaxE,EAAUmC,CAAAA,EAAgCmC,GAGnDjC,EAAeiC,EAAiBhB,GAChCjB,EAAekC,EAAcN,EAAe,CACpD,EAEJjE,GAAUiE,CACd,CACJ,CACJ,CACA9J,EAAwBqI,qBAAqB,CAAGA,CACpD,EAAGrI,GAA4BA,CAAAA,EAA0B,CAAC,CAAA,GAM7B,IAAMsK,EAAkCtK,EAGrE,IAAIuK,EAA+FpK,EAAoB,KACnHqK,EAAmHrK,EAAoBI,CAAC,CAACgK,GAgB7I,GAAM,CAAEpI,OAAQsI,CAAoB,CAAEC,QAAAA,CAAO,CAAEC,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAEvI,MAAOwI,CAAmB,CAAEvI,KAAMwI,CAAkB,CAAE5C,eAAgB6C,CAA4B,CAAE,CAAIpJ,IA0M3K,IAAIqJ,EAAmH7K,EAAoB,IACvI8K,EAAuI9K,EAAoBI,CAAC,CAACyK,GAgBjK,GAAM,CAAEE,QAAAA,CAAO,CAAE,CAAIvJ,IACf,CAAEwJ,SAAAA,CAAQ,CAAE9I,MAAO+I,CAAc,CAAEC,UAAAA,CAAS,CAAEnJ,QAASoJ,CAAgB,CAAEnJ,OAAQoJ,CAAe,CAAE,CAAI5J,IAyB5G,SAAS6J,EAAYC,CAAI,CAAEC,CAAe,EAEtCA,EAAkBN,EAAe,CAAA,EAAM,CACnCO,QAAS,CAAA,EACTC,WAAY,CACRC,GAAI,GACJC,YAAa,MACbC,WAAY,QAChB,CACJ,EAAGL,GACH,IAAMM,EAAM,IAAI,CAACC,QAAQ,CAACD,GAAG,CAAEE,EAAc,IAAI,CAACC,IAAI,EAAI,IAAI,CAAEC,EAAWF,EAAYE,QAAQ,CAAE,CAAER,WAAAA,CAAU,CAAED,QAAAA,CAAO,CAAE,CAAGD,EAM3H,GALAD,EAAOA,GAASW,GAAYA,EAASX,IAAI,CAErCW,GACAA,EAASC,IAAI,GAEbZ,GAAQE,EAAS,CACjB,IAAMU,EAAOlB,EAASe,EAAa,kBAAmB,AAACI,IACnD,GAAIb,GAAQE,EAAS,CAEjB,IAAIY,EAAad,EAAKe,IAAI,CAAC,KACvB,CAACD,GACDd,EAAKe,IAAI,CAAC,KAAMD,EAAalB,KAGjC,IAAMoB,EAAc,CAGhB/E,EAAG,EACHrC,EAAG,CACP,EACIiG,EAAiBM,EAAWc,EAAE,IAC9BD,EAAYC,EAAE,CAAGd,EAAWc,EAAE,CAC9B,OAAOd,EAAWc,EAAE,EAEpBpB,EAAiBM,EAAWC,EAAE,IAC9BY,EAAYZ,EAAE,CAAGD,EAAWC,EAAE,CAC9B,OAAOD,EAAWC,EAAE,EAExBK,EAAYM,IAAI,CAACC,GAEjB,IAAI,CAACD,IAAI,CAAC,CAAEG,UAAW,EAAG,GACtB,IAAI,CAACC,GAAG,EACR,CAAA,IAAI,CAACA,GAAG,CAAG,IAAI,CAACA,GAAG,CAACrK,OAAO,EAAC,EAGhC,IAAMsK,EAAWP,EAAE3J,KAAK,CAAC6F,KAAK,CAAC,EAC/B8D,CAAAA,EAAE3J,KAAK,CAACM,MAAM,CAAG,EACjBqJ,EAAE3J,KAAK,CAAC,EAAE,CAAG,CACTmK,QAAS,WACTlB,WAAYL,EAAgBK,EAAY,CACpC,cAAeA,EAAWG,UAAU,CACpCgB,KAAM,CAAC,EAAEf,EAAI,CAAC,EAAEO,EAAW,CAAC,AAChC,GACAM,SAAAA,CACJ,CACJ,CACJ,EAEAX,CAAAA,EAAYE,QAAQ,CAAG,CAAEX,KAAAA,EAAMY,KAAAA,CAAK,CACxC,MAEIH,EAAYM,IAAI,CAAC,CAAEE,GAAI,EAAGb,GAAI,CAAE,GAChC,OAAOK,EAAYE,QAAQ,CAO/B,OALI,IAAI,CAACY,KAAK,GAEVd,EAAYe,SAAS,CAAG,GACxB,IAAI,CAAChB,QAAQ,CAACiB,SAAS,CAAChB,IAErB,IAAI,AACf,CAWA,SAASiB,EAAWC,CAAK,EACrB,IAAMC,EAAOD,EAAMC,IAAI,CAAEC,EAAK,IAAI,CAACC,OAAO,EAAEC,cAAc,YAC1D,GAAIF,EAAI,CACJ,IAAMG,EAAU,EAAE,CAAE,CAAEC,EAAAA,CAAC,CAAEC,EAAAA,CAAC,CAAE,CAAG,IAAI,CAAC1B,QAAQ,CAAC2B,WAAW,CAAC,IAAI,CAACL,OAAO,EAAGM,EAAYF,EAAID,EAAGI,EAAmB,AAAIC,OAAO,gEAEtG,KAAMC,EAAQV,EAC5BW,SAAS,CACTC,OAAO,CAACJ,EAAkB,IAC1BK,KAAK,CAAC,sCAAuCC,EAAaJ,EAAM/K,MAAM,CAIrEoL,EAAqB,CAACC,EAAWC,KACnC,GAAM,CAAE7G,EAAAA,CAAC,CAAErC,EAAAA,CAAC,CAAE,CAAGkJ,EAAgBC,EAAW,AAAClB,CAAAA,EAAGmB,iBAAiB,CAACH,GAAa,EAAC,EAAKpD,EAASwD,EAAS/I,KAAKgJ,GAAG,CAACH,GAAWI,EAASjJ,KAAKkJ,GAAG,CAACL,GAC7I,MAAO,CACH,CACI9G,EAAImG,EAAYa,EAChBrJ,EAAIwI,EAAYe,EACnB,CACD,CACIlH,EAAIgG,EAAIgB,EACRrJ,EAAIqI,EAAIkB,EACX,CACJ,AACL,EACA,IAAK,IAAI5I,EAAI,EAAG8I,EAAY,EAAGA,EAAYV,EAAYU,IAAa,CAChE,IAA+BC,EAAUC,AAA5BhB,CAAK,CAACc,EAAU,CAAiB7L,MAAM,CACpD,IAAK,IAAIgM,EAAgB,EAAGA,EAAgBF,EAASE,GAAiB,EAClE,GAAI,CACA,IAAMC,EAAgBlJ,EAClBiJ,EACAH,EAAY,CAACK,EAAOC,EAAM,CAAGf,EAAmBa,EAAc5B,EAAG+B,sBAAsB,CAACH,GACxFD,AAAkB,CAAA,IAAlBA,GACAxB,EAAQ7I,IAAI,CAACwK,GACb3B,EAAQ7I,IAAI,CAACuK,KAGTL,AAAc,IAAdA,GACArB,EAAQ6B,OAAO,CAACF,GAEhBN,IAAcV,EAAa,GAC3BX,EAAQ7I,IAAI,CAACuK,GAGzB,CACA,MAAO7C,EAAG,CAGN,KACJ,CAEJtG,GAAK+I,EAAU,EACf,GAAI,CACA,IAAMG,EAAelJ,EAAI8I,EAAWS,EAAUjC,EAAGkC,oBAAoB,CAACN,GAAe,CAACC,EAAOC,EAAM,CAAGf,EAAmBa,EAAcK,GACvI9B,EAAQ6B,OAAO,CAACF,GAChB3B,EAAQ6B,OAAO,CAACH,EACpB,CACA,MAAO7C,EAAG,CAGN,KACJ,CACJ,CAEImB,EAAQxK,MAAM,EACdwK,EAAQ7I,IAAI,CAAC6I,CAAO,CAAC,EAAE,CAACjF,KAAK,IAEjC6E,EAAKI,OAAO,CAAGA,CACnB,CACA,OAAOJ,CACX,CAWA,SAASoC,EAAarC,CAAK,EACvB,IAAMsC,EAAetC,EAAMsC,YAAY,CAAE5J,EAAQsH,EAAMtH,KAAK,CAAE4F,EAAmBgE,CAAY,CAAC5J,EAAMK,YAAY,CAAG,WAAW,EAC1HuJ,EAAatD,QAAQ,CACrBV,GAAmB,CAACgE,EAAaC,OAAO,GACxC,IAAI,CAACnE,WAAW,CAAC1F,EAAM8J,gBAAgB,GAAG,IAAI,GAAK9J,EAAMjC,OAAO,CAAE6H,GAC9D5F,EAAM+J,aAAa,EACnB,CAACnE,EAAgBC,OAAO,EAExB7F,CAAAA,EAAM+J,aAAa,CAAI/J,EAAM+J,aAAa,CAACtN,OAAO,EAAE,EAGhE,CAiCA,GAAM,CAAE6E,OAAQ0I,CAAyB,CAAEd,KAAMe,CAAU,CAAE,CAAG,AAAClO,IAA2IyF,WAAW,CAEjN,CAAE0I,MAAOjJ,EAAK,CAAE,CAAIyD,IAEpB,CAAEyF,gBAAiBC,EAA4B,CAAEC,aAAcC,EAAyB,CAAE,CAxQ1E,CAClBC,SAvLJ,SAAkBrN,CAAI,CAAEe,CAAO,EAC3B,IACqB+B,EAAOa,EAAO2J,EAAcC,EAAmBxJ,EAAOD,EADrEzC,EAAQN,EAAQM,KAAK,CAAEmM,EAAoBzM,EAAQyM,iBAAiB,CAAEC,EAAc1M,EAAQ0M,WAAW,CAAEC,EAAmB3M,EAAQ2M,gBAAgB,CAAE5O,EAASiC,EAAQjC,MAAM,CAAE6O,EAAS5M,EAAQ4M,MAAM,CAAEC,EAAW7M,EAAQ6M,QAAQ,CAAElO,EAASZ,EAAOY,MAAM,CAAEmO,EAAoB/O,EAAO+C,KAAK,CAACd,OAAO,CAACc,KAAK,CA+BjT,OAhBI7B,IACA8C,EAAQpD,CAAM,CAACM,EAAKgD,CAAC,CAAC,CACtBW,EAAQ6J,CAAiB,CAACxN,EAAK2D,KAAK,CAAC,EAAI,CAAC,EACxBb,GAASa,EAAM2J,YAAY,GAEzCC,EAAoBzK,EAAMzB,KAAK,CAAIsM,CAAAA,EAC/BA,EAAO1N,MAAM,CACb4N,EAAkBC,UAAU,AAAD,EAC/BR,EAAeK,GAAUA,CAAM,CAACJ,EAAkB,EAGlD,AAACzO,EAAO+C,KAAK,CAACgC,UAAU,EACxBE,CAAAA,EAAQ+D,EAAmBhF,GAASA,EAAM/B,OAAO,CAACgD,KAAK,CAAEJ,GAASA,EAAMI,KAAK,CAAEuJ,EAAcG,GAAeM,AAtB/F,CAAA,AAAChK,IAClB,IAAMiK,EAAiBrK,GAASA,EAAMqK,cAAc,QACpD,AAAIA,GACAA,AAAuB,eAAvBA,EAAenQ,GAAG,EAClBwD,GACAuM,EACOpG,IAAsGwF,KAAK,CAACjJ,GAAOkK,QAAQ,CAACD,EAAehK,EAAE,CAAI3C,CAAAA,EAAQuM,CAAO,GAAI1P,GAAG,GAE3K6F,CACX,CAAA,EAaiI0J,GAAc3O,EAAOiF,KAAK,CAAA,EAEvJD,EAAagE,EAAmBhF,GAASA,EAAM/B,OAAO,CAAC+C,UAAU,CAAEH,GAASA,EAAMG,UAAU,CAAEyJ,EAAmBG,EAAkB3M,EAAQ+C,UAAU,GAElJ,CACHC,MAAOA,EACPD,WAAYA,CAChB,CACJ,EAoJImJ,gBAlIJ,SAAyBiB,CAAM,EAC3B,IACIC,EAAUC,EAAWpL,EAAGY,EAAMI,EAAIqK,EADhCC,EAAS,CAAC,EAEhB,GAAI1G,EAASsG,GA2BT,IA1BAtK,EAAO+D,EAASuG,EAAOtK,IAAI,EAAIsK,EAAOtK,IAAI,CAAG,EAC7CyK,EAASH,EAAOG,MAAM,CACtBD,EAAY,CAAC,EACbD,EAAWvG,EAASsG,EAAOC,QAAQ,EAAID,EAAOC,QAAQ,CAAG,CAAC,EACtDzG,EAAQ2G,IACRD,CAAAA,EAAYC,EAAO7M,MAAM,CAAC,CAACrD,EAAKoQ,KAC5B,IAAI5K,EAAO6K,EAAiBzN,EAgB5B,OAfI6G,EAAS2G,IAAS5G,EAAS4G,EAAK5K,KAAK,IAErC6K,EAAkB1G,EAAmB/G,AADrCA,CAAAA,EAAU8G,EAAoB,CAAC,EAAG0G,EAAI,EACOC,eAAe,CAAEL,EAASK,eAAe,EAEtF,OAAOzN,EAAQyN,eAAe,CAC9B,OAAOzN,EAAQ4C,KAAK,CAGhBiE,EAASzJ,CAAG,CADhBwF,EAAQ4K,EAAK5K,KAAK,CAAI6K,CAAAA,EAAkB,EAAI5K,EAAO,CAAA,EAC5B,EACnBiE,EAAoB,CAAA,EAAM1J,CAAG,CAACwF,EAAM,CAAE5C,GAGtC5C,CAAG,CAACwF,EAAM,CAAG5C,GAGd5C,CACX,EAAG,CAAC,EAAC,EAET6F,EAAK2D,EAASuG,EAAOlK,EAAE,EAAIkK,EAAOlK,EAAE,CAAG,EAClChB,EAAI,EAAGA,GAAKgB,EAAIhB,IACjBsL,CAAM,CAACtL,EAAE,CAAG6E,EAAoB,CAAC,EAAGsG,EAAUvG,EAASwG,CAAS,CAACpL,EAAE,EAAIoL,CAAS,CAACpL,EAAE,CAAG,CAAC,GAG/F,OAAOsL,CACX,EAgGInB,aAvBJ,SAAsBrO,CAAM,CAAE2P,CAAW,EACrC,GAAM,CAAE5M,MAAAA,CAAK,CAAEd,QAAAA,CAAO,CAAE,CAAGjC,EAAQ,CAAE4P,aAAAA,EAAe,CAAC,CAAEC,UAAAA,EAAY,CAAC,CAAE,CAAG5N,EAAS,CAAE6F,UAAAA,EAAY,CAAC,CAAE,CAAG/E,EAGtG,GAAI8M,AAAc,SAAdA,EAAsB,CACtB,GAAI,AAAwB,UAAxB,OAAOD,GAA6B,KAAKE,IAAI,CAACF,GAE9C,OAAO9H,EADkD6H,CAAAA,EAAcI,AAAtDC,WAAWJ,GAAgB,IAAuCD,CAAAA,EAAc,CAAA,CAAC,EAGtG,IAAMM,EAAYC,OAAON,GACzB,MAAO,AAAE9H,CAAAA,EAAYmI,CAAQ,EACxBN,CAAAA,GAAe,CAAA,EAAMM,CAC9B,CACA,OAAOhH,EAA6B4G,EAAW/H,EACnD,EAUIqI,cA3FJ,SAASA,EAAcC,CAAI,CAAEnO,CAAO,EAChC,IAAMoO,EAASpO,EAAQoO,MAAM,CAAEC,EAASrO,EAAQqO,MAAM,CAAqCC,EAAWC,AAAhCvO,EAAQuO,WAAW,AAAwB,CAACF,EAAO,CAAEZ,EAAmBzN,AAA4B,CAAA,IAA5BA,EAAQyN,eAAe,CAAsC1L,EAAQpD,AAAxBqB,EAAQrB,MAAM,AAAgB,CAACwP,EAAKlM,CAAC,CAAC,CAAEuM,EAAezM,GAASA,EAAM/B,OAAO,EAAI,CAAC,EAAG8I,EAAW,EAAE,CACzR2F,EAAgB,CACpBN,CAAAA,EAAKO,YAAY,CAAGP,EAAKvL,KAAK,CAAI6K,CAAAA,EAAkB,EAAIa,EAAS1L,KAAK,AAAD,EACrEuL,EAAK9L,IAAI,CAAG0E,EAAmBhF,GAASA,EAAMM,IAAI,CAAE,IACpD8L,EAAKQ,OAAO,CAAIN,IAAWF,EAAKxN,EAAE,EAC9BX,AAAoB,CAAA,IAApBA,EAAQ2O,OAAO,CACf,AAAkB,YAAlB,OAAOP,GACPD,CAAAA,EAAOC,EAAOD,EAAMnO,EAAO,EAG/BmO,EAAKrF,QAAQ,CAAC9J,OAAO,CAAC,CAAC4P,EAAO3M,KAC1B,IAAM4M,EAAanI,EAAqB,CAAC,EAAG1G,GAC5C0G,EAAqBmI,EAAY,CAC7BvO,MAAO2B,EACP4K,SAAUsB,EAAKrF,QAAQ,CAAC5J,MAAM,CAC9ByP,QAASR,EAAKQ,OAAO,AACzB,GACAC,EAAQV,EAAcU,EAAOC,GAC7B/F,EAASjI,IAAI,CAAC+N,GACVA,EAAMD,OAAO,EACbF,CAAAA,GAAiBG,EAAME,GAAG,AAAD,CAEjC,GAEA,IAAMC,EAAQhI,EAAmByH,EAAaO,KAAK,CAAEN,GAMrD,OALAN,EAAKQ,OAAO,CAAGI,GAAS,GAAMN,CAAAA,EAAgB,GAAKN,EAAKQ,OAAO,AAAD,EAC9DR,EAAKrF,QAAQ,CAAGA,EAChBqF,EAAKM,aAAa,CAAGA,EACrBN,EAAKa,MAAM,CAAGb,EAAKQ,OAAO,EAAI,CAACF,EAC/BN,EAAKW,GAAG,CAAGC,EACJZ,CACX,EA4DIc,aA/CJ,SAAsBlR,CAAM,EACxB,IAAImR,EAAQlP,EAaZ,OAZI6G,EAAS9I,KAETiC,EAAU6G,EAAS9I,EAAOiC,OAAO,EAAIjC,EAAOiC,OAAO,CAAG,CAAC,EAEvDkP,EAASnI,EAAmBhJ,EAAOoR,QAAQ,CAAEnP,EAAQkP,MAAM,CAAE,IAEzDrI,EAAS9I,EAAOqR,WAAW,GAC3BrR,CAAAA,EAAOqR,WAAW,CAACF,MAAM,CAAGA,CAAK,EAGrCnR,EAAOoR,QAAQ,CAAGD,GAEfA,CACX,CAiCA,EAoQM,CAAEG,MAAAA,EAAK,CAAEC,MAAAA,EAAK,CAAElR,OAAQmR,EAAmB,CAAE1I,SAAU2I,EAAqB,CAAElR,MAAOmR,EAAkB,CAAElR,KAAMmR,EAAiB,CAAEvL,eAAgBwL,EAA2B,CAAEC,WAAAA,EAAU,CAAE,CAAIhS,IAGrMiS,AAjCiB,CAAA,CACb9O,QATJ,SAAiB+O,CAAe,EAC5B1I,EAAS0I,EAAiB,eAAgB1G,GAC1ChC,EAAS0I,EAAiB,wBAAyBpE,GACnD,IAAMqE,EAAkBD,EAAgBxS,SAAS,AAC7C,AAACyS,CAAAA,EAAgBtI,WAAW,EAC5BsI,CAAAA,EAAgBtI,WAAW,CAAGA,CAAU,CAEhD,CAGA,CAAA,EA+BoB1G,OAAO,CAAEmG,IAa7B,OAAM8I,WAAqBjE,EASvB,OAAOkE,aAAa9C,CAAM,CAAE,CACxB,IAAMqB,EAAgBgB,GAAsBrC,EAAOqB,YAAY,EAC3DrB,EAAOqB,YAAY,CAAC0B,UAAU,CAC9B,CAAC,EAKL,OAHmBT,GAAmB,CAClCU,MAAO,CAAC,CACZ,EAJyBX,GAAsBrC,EAAOvK,KAAK,EACvDuK,EAAOvK,KAAK,CAACsN,UAAU,CACvB,CAAC,EAEY1B,EAErB,CAWA4B,mBAAoB,CAChB,IAAMC,EAAU,EAAE,CAClB,IAAK,IAAMpR,KAAQ,IAAI,CAACL,KAAK,CACzBK,EAAK8E,aAAa,GACd,AAACsM,CAAO,CAACpR,EAAKoE,MAAM,CAAC,EACrBgN,CAAAA,CAAO,CAACpR,EAAKoE,MAAM,CAAC,CAChBkD,EAA+BxF,OAAO,CAAC,EAAE,CAAE,IAAI,CAAA,EAEvDsP,CAAO,CAACpR,EAAKoE,MAAM,CAAC,CAACxC,IAAI,CAAC5B,GAG9B,IAAK,IAAIgD,EAAI,EAAGA,EAAIoO,EAAQnR,MAAM,CAAE+C,IAC5B,AAAsB,KAAA,IAAfoO,CAAO,CAACpO,EAAE,EACjBoO,CAAAA,CAAO,CAACpO,EAAE,CACNsE,EAA+BxF,OAAO,CAAC,EAAE,CAAE,IAAI,CAAA,EAG3D,OAAOsP,CACX,CAKAC,MAAMrR,CAAI,CAAE2D,CAAK,CAAE,CAGf,GAAI,AAAsB,KAAA,IAAf3D,EAAK2D,KAAK,CAEjB,IAAK,IAAMlB,KADXzC,EAAK2D,KAAK,CAAGA,EACM3D,EAAKQ,SAAS,EACzBiC,EAAK/B,MAAM,EACX5B,AANG,IAAI,CAMAuS,KAAK,CAAC5O,EAAK/B,MAAM,CAAEiD,EAAQ,EAIlD,CAMAH,gBAAiB,CAEb,GADAS,EAAwBT,cAAc,CAAC5D,KAAK,CAAC,IAAI,CAAEC,WAC/C,IAAI,CAACyR,UAAU,CAAE,CACjB,IAAK,IAAMtR,KAAQ,IAAI,CAACL,KAAK,CAErBK,AAAwB,IAAxBA,EAAKO,OAAO,CAACN,MAAM,EAGnB,IAAI,CAACoR,KAAK,CAACrR,EAAM,GAGzB2Q,GAAW,IAAI,CAAChR,KAAK,CAAE,CAAChC,EAAG+M,IAAO/M,EAAEgG,KAAK,CAAG+G,EAAE/G,KAAK,CACvD,CACJ,CAMA4N,gBAAiB,CACb,IAAIxL,EAAc,IAAI,CAAChF,OAAO,CAACgF,WAAW,EAAI,EAI9C,GAAI,IAAI,CAACyL,WAAW,CAAE,CAClB,IAAMC,EAAY,IAAI,CAACD,WAAW,CAAChQ,MAAM,CAAC,CAACkQ,EAAKC,IAAQhP,KAAKC,GAAG,CAAC8O,EAAKC,EAAI1R,MAAM,EAAG,EAC/EwR,CAAAA,EAAY1L,EAAc,IAAI,CAAClE,KAAK,CAACgE,SAAS,EAC9CE,CAAAA,EAAc,IAAI,CAAClE,KAAK,CAACgE,SAAS,CAAG4L,CAAQ,CAErD,CACA,OAAO1L,CACX,CAOA6L,SAAU,CACN,MAAO,CAAC,CAAC,IAAI,CAACC,SAAS,CAACC,QAAQ,AACpC,CAKAC,aAAajP,CAAK,CAAE3C,CAAK,CAAE,CACvB,GAAI,CAAC2C,EACD,MAAO,CAAC,EAEZ,IAAMhE,EAAS,IAAI,CAAE6E,EAAQb,EAAMxC,MAAM,CAAGwC,EAAMa,KAAK,CAAGb,EAAMrC,QAAQ,CAACkD,KAAK,CAAEqO,EAAelT,EAAO0O,iBAAiB,CAAC7J,GAAS,EAAE,EAAI,CAAC,EAAG5C,EAAU+B,EAAM/B,OAAO,CAAEkR,EAAe,AAACD,EAAaE,MAAM,EAAIF,EAAaE,MAAM,CAAC/R,GAAS,GAAG,EAAK,CAAC,EAAGgS,EAAS,CACzP,eACA,cACA,cACA,cACA,UACH,CAAC3Q,MAAM,CAAC,CAACrD,EAAKN,KACXM,CAAG,CAACN,EAAI,CAAG4S,GAAkBwB,CAAY,CAACpU,EAAI,CAAEkD,CAAO,CAAClD,EAAI,CAAEmU,CAAY,CAACnU,EAAI,CAAEiB,EAAOiC,OAAO,CAAClD,EAAI,EAC7FM,GACR,CAAC,GAAI4F,EAAQ0M,GAAkBwB,EAAalO,KAAK,CAAEhD,EAAQgD,KAAK,CAAEoO,EAAO7E,YAAY,CAAGxK,EAAMiB,KAAK,CAAGiO,EAAajO,KAAK,SAE3H,AAAIjB,EAAMxC,MAAM,CACL,CACH8R,KAAMrO,EACNsO,OAAQF,EAAOG,WAAW,CAC1B,eAAgBH,EAAOrM,WAAW,CAClCyM,QAASJ,EAAOI,OAAO,AAC3B,EAGG,CACHH,KAAMrO,EACN,eAAgBoO,EAAOK,WAAW,AACtC,CACJ,CACAC,aAAc,CACV3F,EAA0BzO,SAAS,CAACoU,WAAW,CAAClU,IAAI,CAAC,IAAI,CAAE,IAAI,CAACmB,MAAM,EACtEoN,EAA0BzO,SAAS,CAACoU,WAAW,CAAClU,IAAI,CAAC,IAAI,CAAE,IAAI,CAACoB,KAAK,CACzE,CACA+S,YAAa,CACT5F,EAA0BzO,SAAS,CAACqU,UAAU,CAACnU,IAAI,CAAC,IAAI,CAAE,IAAI,CAACmB,MAAM,EACrEoN,EAA0BzO,SAAS,CAACqU,UAAU,CAACnU,IAAI,CAAC,IAAI,CAAE,IAAI,CAACoB,KAAK,CACxE,CACAgT,gBAAiB,CACb7F,EAA0BzO,SAAS,CAACsU,cAAc,CAACpU,IAAI,CAAC,IAAI,CAAE,IAAI,CAACmB,MAAM,EACzEoN,EAA0BzO,SAAS,CAACsU,cAAc,CAACpU,IAAI,CAAC,IAAI,CAAE,IAAI,CAACoB,KAAK,CAC5E,CAKAiT,WAAY,CACR,IAAI,CAACpP,cAAc,GACnB,IAAI,CAACgO,WAAW,CAAG,IAAI,CAACL,iBAAiB,GACzC,IAAMrS,EAAS,IAAI,CAAE+C,EAAQ,IAAI,CAACA,KAAK,CAAEd,EAAU,IAAI,CAACA,OAAO,CAAEyQ,EAAc,IAAI,CAACA,WAAW,CAAE/C,EAAc+C,EAAYvR,MAAM,CAgCjI,IAAK,IAAMmE,KA/BX,IAAI,CAACuK,SAAS,CAAGvB,GAA0B,IAAI,CAAEqB,GACjD,IAAI,CAAC1I,WAAW,CAAG,IAAI,CAACwL,cAAc,GAGtC,IAAI,CAACsB,iBAAiB,CAAGrB,EAAYhQ,MAAM,CAAC,CAACqR,EAAmBzO,IAAWzB,KAAKmQ,GAAG,CAACD,EAAmBzO,EAAOgB,YAAY,CAACG,oBAAoB,CAACzG,IAAUiU,KAC1J,IAAI,CAACC,WAAW,CACZ,AAACnR,CAAAA,EAAM+E,SAAS,CAAG,IAAI,CAAC+H,SAAS,CAC7B5N,EAAQ+E,WAAW,AAAD,EAAKnD,KAAKC,GAAG,CAAC,EAAG4O,EAAYvR,MAAM,CAAG,GAEhEnB,EAAO0O,iBAAiB,CAAGN,GAA6B,CAGpDtJ,KAAM,EACNyK,OAAQtN,EAAQsN,MAAM,CACtBrK,GAAIwN,EAAYvR,MAAM,CAAG,EACzBkO,SAAU,CACNmE,YAAavR,EAAQuR,WAAW,CAChCW,aAAclS,EAAQkS,YAAY,CAClCnN,YAAa/E,EAAQ+E,WAAW,CAChC/B,MAAOjF,EAAOiF,KAAK,CACnBuJ,aAAcvM,EAAQuM,YAAY,CAGlCkB,gBAAiB,CAAA,EACjB0E,UAAWnS,EAAQmS,SAAS,CAC5BC,cAAepS,EAAQoS,aAAa,CACpCX,YAAazR,EAAQyR,WAAW,CAChCN,OAAQnR,EAAQmR,MAAM,AAC1B,CACJ,GAEqBV,GACjB,IAAK,IAAMxR,KAAQoE,EACftF,EAAOsU,aAAa,CAACpT,EAAMoE,GAInC,IAAK,IAAMpE,KAAQ,IAAI,CAACL,KAAK,CAEzB,IAAK,IAAM0T,KAAarT,EAAKQ,SAAS,CAG7B6S,CAAAA,EAAU3Q,MAAM,EAAI2Q,EAAUC,MAAM,AAAD,GAAMD,EAAUrP,EAAE,GACtDlF,EAAOyU,aAAa,CAACF,GACrBA,EAAUG,WAAW,CAAG,CAAA,EAIxC,CAKAD,cAAczQ,CAAK,CAAE,CACjB,IAAM2Q,EAAO,CAACzT,EAAM0T,KAChB,IAAMC,EAAW3T,EAAK6C,MAAM,CAACC,EAAO4Q,GAChCb,EAIJ,OAHUlQ,KAAKmQ,GAAG,CAAC9S,EAAKkH,KAAK,CAAGyM,EAEhC3T,EAAKkH,KAAK,CAAIlH,CAAAA,EAAK4T,SAAS,EAAI5T,EAAK4T,SAAS,CAACzN,MAAM,EAAI,CAAA,EAAK0N,EAElE,EACMpT,EAAWqC,EAAMrC,QAAQ,CAAEC,EAASoC,EAAMpC,MAAM,CAAEmB,EAAQ,IAAI,CAACA,KAAK,CAAE,CAAE2E,SAAAA,CAAQ,CAAE,CAAG3E,EAAOgR,EAAoB,IAAI,CAACA,iBAAiB,CAAE9R,EAAU,IAAI,CAACA,OAAO,CAAE+S,EAAgBrD,GAAkB3N,EAAMgR,aAAa,CAAE/S,EAAQ+S,aAAa,EAAGC,EAAS,AAAClS,CAAAA,EAAM2E,QAAQ,CAAG,CAAC,IAAI,CAACwM,WAAW,CAAG,IAAI,CAACA,WAAW,AAAD,EAC/SjS,EAAQiT,WAAW,CAAGC,EAAWxT,EAASyT,KAAK,CAAEC,EAAQzT,EAAOwT,KAAK,CAAEhR,EAAWJ,EAAMI,QAAQ,CAChG2Q,EAAalR,KAAKC,GAAG,CAACE,EAAMJ,MAAM,CAAGmQ,EAAmB,IAAI,CAAC9R,OAAO,CAAC0E,YAAY,EAAG2O,EAAQX,EAAKhT,EAAU,aAAc4T,EAAMZ,EAAK/S,EAAQ,WAAY4T,EAAQ,IAAI,CAAC3F,SAAS,CAAE4F,EAAWJ,EAAQF,EAAWK,EAgBlN,GAfIzS,EAAM2E,QAAQ,GACd4N,EAAQvS,EAAMgE,SAAS,CAAGuO,EAC1BC,EAAM,AAACxS,CAAAA,EAAMgE,SAAS,EAAI,CAAA,EAAKwO,EAC/BC,EAAQ,CAACA,EACTT,EAAa,CAACA,EACdU,EAAWN,EAAWE,GAE1BrR,EAAM0R,SAAS,CAAG,OAClB1R,EAAM2R,QAAQ,CAAG,CACbL,EACAA,EAAQP,EACRQ,EACAA,EAAMR,EACT,CAEGU,GAAY,AAAe,UAAf,OAAOF,EACnBvR,EAAM8Q,SAAS,CAAG,CACdlW,EAAG,CACC,CAAC,IAAKuW,EAAWK,EAAOF,EAAM,CAC9B,CACI,IACAH,EAAWK,EAAQP,EACnBK,EACAD,EAAQJ,EACRM,EACAF,EACAE,EACH,CACD,CAAC,IAAKF,EAASjR,CAAAA,EAAWoR,EAAQ,CAAA,EAAID,EAAMR,EAAa,EAAE,CAC3D,CAAC,IAAKM,EAAOE,EAAMR,EAAW,CAC9B,CACI,IACAM,EAAQJ,EACRM,EAAMR,EACNI,EAAWK,EAAQP,EACnBK,EAAQP,EACRI,EAAWK,EAAOF,EAAQP,EAC7B,CACD,CAAC,IAAI,CACR,AACL,OASC,GAAI,AAAe,UAAf,OAAOQ,EAAkB,CAC9B,IAAiBK,EAAQ7S,EAAM4E,UAAU,CAAG2N,EAAQP,EAAYc,EAAKR,EAAxD,GAAuEN,EAAYe,EAAKT,EAAxF,GAAkHU,EAAKZ,EAAWK,EAAOQ,EAAKD,EAA9I,GAAyJE,EAAKD,EAAKjB,EAAYmB,EAAMZ,EAAOa,EAAMb,EAAQP,EAAYqB,EAAMD,EAA5N,GAAwOE,EAAKD,EAAMR,EAAOU,EAAKD,EAA/P,GAA0QE,EAAKD,EAAKvB,EAAYyB,EAAMjB,EAAKkB,EAAMD,EAAMzB,EAAY2B,EAAMD,EAAzU,GAAqVE,EAAOR,EAAMpB,AAAa,GAAbA,EAAkB6B,EAAMN,EAAKvB,AAAa,GAAbA,EAAkB8B,EAAOJ,EAAM1B,AAAa,GAAbA,EAAkB+B,EAAMC,AAA3U1B,EAAgVN,AAAa,GAAbA,EAAkBiC,EAAMjB,EAAKhB,AAAa,GAAbA,CACre/Q,CAAAA,EAAM8Q,SAAS,CAAG,CACdlW,EAAG,CACC,CAAC,IAAKmX,EAAIG,EAAI,CACd,CAAC,IAAKc,EAAKd,EAAKD,EAAIU,EAAMV,EAAIG,EAAI,CAClC,CAAC,IAAKH,EAAII,EAAG,CACb,CAAC,IAAKJ,EAAIW,EAAKI,EAAKT,EAAIR,EAAIQ,EAAG,CAC/B,CAAC,IAP+GlB,EAOtGkB,EAAG,CACb,CAAC,IAAKO,EAAKP,EAAIV,EAAIe,EAAKf,EAAIQ,EAAG,CAC/B,CAAC,IAAKR,EAAIa,EAAI,CACd,CAAC,IAAKb,EAAIgB,EAAMC,EAAKN,EAV2FnB,EAUlFmB,EAAI,CAClC,CAAC,IAX+GnB,EAWtGoB,EAAI,CACd,CAAC,IAAKX,EAAIW,EAAKX,EAAIW,EAAKX,EAAIY,EAAI,CAChC,CAAC,IAAKZ,EAAIO,EAAG,CACb,CAAC,IAAKP,EAAIQ,EAAIR,EAAIQ,EAd8FjB,EActFiB,EAAG,CAC7B,CAAC,IAAKP,EAAIO,EAAG,CACb,CAAC,IAAKN,EAAIM,EAAIN,EAAIM,EAAIN,EAAIK,EAAG,CAC7B,CAAC,IAAKL,EAAII,EAAI,CACd,CAAC,IAAKJ,EAAIG,EAAKH,EAAIG,EAAKJ,EAAII,EAAI,CAChC,CAAC,IAAI,CACR,AACL,CACJ,CAoBA,GAlBAnS,EAAMiT,KAAK,CAAG,CACVrR,EAAGuP,EAAW,AAACE,CAAAA,EAAQF,EAAWK,CAAI,EAAK,EAC3CjS,EAAG+R,EAAQ,AAACC,CAAAA,EAAMD,CAAI,EAAK,EAC3BjO,OAAQ0N,EACRlN,MAAO,CACX,EAEA7D,EAAMkT,UAAU,CAAGnU,EAAM2E,QAAQ,CAAG,CAChC3E,EAAMgE,SAAS,CAAG/C,EAAMiT,KAAK,CAAC1T,CAAC,CAAGwR,EAAa,EAC/ChS,EAAM+E,SAAS,CAAG9D,EAAMiT,KAAK,CAACrR,CAAC,CAClC,CAAG,CACA5B,EAAMiT,KAAK,CAACrR,CAAC,CACb5B,EAAMiT,KAAK,CAAC1T,CAAC,CAAGwR,EAAa,EAChC,CAGD/Q,EAAMT,CAAC,CAAGS,EAAMmT,KAAK,CAAG,EACxBnT,EAAM4B,CAAC,CAAG5B,EAAMoT,KAAK,CAAG,EACpB,CAACpT,EAAM/B,OAAO,CAACgD,KAAK,CACpB,CAAA,GAAI+P,AAAkB,SAAlBA,EACAhR,EAAMiB,KAAK,CAAGtD,EAASsD,KAAK,MAE3B,GAAI+P,AAAkB,OAAlBA,EACLhR,EAAMiB,KAAK,CAAGrD,EAAOqD,KAAK,MAEzB,GAAI+P,AAAkB,aAAlBA,EAA8B,CACnC,IAAMqC,EAAYpS,GAAMtD,EAASsD,KAAK,EAAE7F,GAAG,GAAIkY,EAAUrS,GAAMrD,EAAOqD,KAAK,EAAE7F,GAAG,EAChF4E,CAAAA,EAAMiB,KAAK,CAAG,CACVsS,eAAgB,CACZ1B,GAAI,EACJC,GAAI,EACJ0B,GAAI,EACJC,GAAI,CACR,EACAC,MAAO,CACH,CAAC,EAAGhQ,EAAW2P,EAAYC,EAAQ,CACnC,CAAC,EAAG5P,EAAW4P,EAAUD,EAAU,CACtC,AACL,CACJ,CAAA,CAER,CAKA/C,cAAcpT,CAAI,CAAEoE,CAAM,CAAE,CACxB,IAAMyO,EAAoB,IAAI,CAACA,iBAAiB,CAAEhR,EAAQ,IAAI,CAACA,KAAK,CAAEd,EAAU,IAAI,CAACA,OAAO,CAAE,CAAEkS,aAAAA,CAAY,CAAEnN,YAAAA,EAAc,CAAC,CAAE,CAAG/E,EAASiF,EAAMhG,EAAKsC,MAAM,GAAImU,EAAa9T,KAAKC,GAAG,CAACD,KAAKkE,KAAK,CAACb,EAAM6M,GAAoB,IAAI,CAAC9R,OAAO,CAAC0E,YAAY,EAAGkJ,EAAYhM,KAAKkE,KAAK,CAAC,IAAI,CAAC8H,SAAS,EAAG+H,EAAatS,EAAOgB,YAAY,CAACvC,MAAM,CAAC7C,EAAM6S,GAAoB8D,EAActG,GAAMI,GAAkBiG,EAAWzP,WAAW,CAAG7C,EAAOgB,YAAY,CAACc,GAAG,CAAC2M,GACpb6D,EAAWrP,WAAW,EAAIvB,GAAcO,EAAOgK,GAAM,IAAI,CAAC2C,WAAW,CAAGhT,EAAKoE,MAAM,CACnF0B,EAAc,EAAGA,GAAe4K,GAA4B1Q,EAAKe,OAAO,CAACc,EAAM2E,QAAQ,CACvF,iBACA,mBAAmB,EAAI,EAAGmI,GAAYsF,EAAWpS,EAAM2E,QAAQ,CAC/D3E,EAAM+E,SAAS,CAAGP,EAClBA,EAGJ,GAFArG,EAAKgG,GAAG,CAAGA,EAEPA,EAAK,CAELhG,EAAKwU,SAAS,CAAG,cACjBxU,EAAKkU,KAAK,CAAGD,EACbjU,EAAKkH,KAAK,CAAGyP,EACb,IAAIjS,EAAIuP,EAAU5R,EAAIsU,EAAahQ,EAAQ3G,EAAKe,OAAO,CAAC4F,KAAK,EAAI5F,EAAQ4F,KAAK,EAAIgI,EAAWxI,EAASnG,EAAKe,OAAO,CAACoF,MAAM,EAAIpF,EAAQoF,MAAM,EAAIsQ,EAGzIG,EAAIxG,GAAMM,GAA6B,AAAwB,UAAxB,OAAOuC,EAChDA,EAAa1P,MAAM,CACnB0P,GAAgB,EAAItM,GAAQ,EAAG8P,EAAa,EAC5C5U,CAAAA,EAAM2E,QAAQ,GACd9B,EAAIuP,EAAWtF,EACftM,EAAIR,EAAMgE,SAAS,CAAG8Q,EAAcF,EACpC9P,EAAQ3G,EAAKe,OAAO,CAACoF,MAAM,EAAIpF,EAAQoF,MAAM,EAAIwI,EACjDxI,EAASnG,EAAKe,OAAO,CAAC4F,KAAK,EAAI5F,EAAQ4F,KAAK,EAAI8P,GAGpDzW,EAAK6W,SAAS,CAAG9F,GAAaC,YAAY,CAAC,CACvCrN,MAAO,IAAI,CAAC6J,iBAAiB,CAACxN,EAAK2D,KAAK,CAAC,CACzC4L,aAAcvP,EAAKe,OAAO,AAC9B,GAEAf,EAAKkW,KAAK,CAAG,EACblW,EAAKiW,KAAK,CAAG,EAEbjW,EAAKgW,UAAU,CAAGnU,EAAM2E,QAAQ,CAAG,CAC/B3E,EAAMgE,SAAS,CAAGxD,EAAI8D,EAAS,EAC/BtE,EAAM+E,SAAS,CAAGlC,EAAIiC,EAAQ,EACjC,CAAG,CACAjC,EAAIiC,EAAQ,EACZtE,EAAI8D,EAAS,EAChB,CACDnG,EAAK4T,SAAS,CAAG,CACblP,EAAAA,EACArC,EAAAA,EACAsE,MAAAA,EACAR,OAAAA,EACAyQ,EAAAA,EACAE,QAAS9W,EAAKiD,QAAQ,GAAK,GAAK,MACpC,CACJ,MAEIjD,EAAK6W,SAAS,CAAG,CACblO,QAAS,CAAA,CACb,CAER,CACJ,CAMAoI,GAAagG,cAAc,CAAGvG,GAAmB1D,EAA0BiK,cAAc,CAnpD5D,CACzBjR,YAAa,EACbwH,aAAc,CAAA,EAQd0G,YAAa,IAYb/C,WAAY,CACRtI,QAAS,CAAA,EACTqO,gBAAiB,OACjBC,KAAM,CAAA,EAYNC,WAAY,KAAK,EASjBC,cAAe,WACX,OAAO,IAAI,CAACrU,KAAK,CAACM,IAAI,AAC1B,EACAgU,OAAQ,KAAK,EAIbC,UAAW,WAEX,EACAC,OAAQ,CAAA,CACZ,EAMAC,oBAAqB,CAAA,EAiFrBzD,cAAe,OAMftB,YAAa,GAMbD,QAAS,EAeT9M,aAAc,EAYdW,cAAe,SAmBfuI,UAAW,GAWX5I,YAAa,GAsBb2I,aAAc,GACd8I,aAAc,CAAA,EACdtF,OAAQ,CACJuF,MAAO,CAKHjF,YAAa,EAIbD,QAAS,CACb,EAMAmF,SAAU,CAKNlF,YAAa,GAIbD,QAAS,GAOTtR,UAAW,CAEP0W,SAAU,EACd,CACJ,CACJ,EACAC,QAAS,CAaLC,cAAe,CAAA,EACfC,aAAc,2DACdC,YAAa,0EAObb,WAAY,uCAChB,CACJ,GAw2CAjT,EAAwBnC,OAAO,CAnsD0B0C,EAmsDLuM,IACpDT,GAAoBS,GAAa1S,SAAS,CAAE,CACxC2Z,QAASjL,EAAW1O,SAAS,CAAC2Z,OAAO,CAGrC/V,WAAYgC,EAAwBhC,UAAU,CAC9CgW,QAAS,CAAA,EACTC,WAAY,CAAA,EACZC,YAAa,CAAA,EACb7G,WAAY,CAAA,EACZ8G,gBAAiB,CAAA,EACjBC,cAAe,CAAC,OAAQ,KAAM,SAAS,CACvCrZ,WA/sDqDwF,EAgtDrD8T,YAAa,AAAC3Z,IAA+E4Z,IAAI,AACrG,GACA1Z,IAA0I2Z,kBAAkB,CAAC,SAAUzH,IAwH1I,IAAMtS,GAAeE,IAGxC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}