{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/networkgraph\n * @requires highcharts\n *\n * Force directed graph module\n *\n * (c) 2010-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SVGElement\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/networkgraph\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SVGElement\"],amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/networkgraph\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SVGElement\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SVGElement\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__563__, __WEBPACK_EXTERNAL_MODULE__28__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 28:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__28__;\n\n/***/ }),\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 563:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__563__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ networkgraph_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(563);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SVGElement\"],\"commonjs\":[\"highcharts\",\"SVGElement\"],\"commonjs2\":[\"highcharts\",\"SVGElement\"],\"root\":[\"Highcharts\",\"SVGElement\"]}\nvar highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_ = __webpack_require__(28);\nvar highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default = /*#__PURE__*/__webpack_require__.n(highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_);\n;// ./code/es-modules/Series/DragNodesComposition.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent, pushUnique } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction compose(ChartClass) {\n    if (pushUnique(composed, 'DragNodes')) {\n        addEvent(ChartClass, 'load', onChartLoad);\n    }\n}\n/**\n * Draggable mode:\n * @private\n */\nfunction onChartLoad() {\n    const chart = this;\n    let mousedownUnbinder, mousemoveUnbinder, mouseupUnbinder, point;\n    if (chart.container) {\n        mousedownUnbinder = addEvent(chart.container, 'mousedown', (event) => {\n            if (mousemoveUnbinder) {\n                mousemoveUnbinder();\n            }\n            if (mouseupUnbinder) {\n                mouseupUnbinder();\n            }\n            point = chart.hoverPoint;\n            if (point &&\n                point.series &&\n                point.series.hasDraggableNodes &&\n                point.series.options.draggable) {\n                point.series.onMouseDown(point, event);\n                mousemoveUnbinder = addEvent(chart.container, 'mousemove', (e) => (point &&\n                    point.series &&\n                    point.series.onMouseMove(point, e)));\n                mouseupUnbinder = addEvent(chart.container.ownerDocument, 'mouseup', (e) => {\n                    mousemoveUnbinder();\n                    mouseupUnbinder();\n                    return point &&\n                        point.series &&\n                        point.series.onMouseUp(point, e);\n                });\n            }\n        });\n    }\n    addEvent(chart, 'destroy', function () {\n        mousedownUnbinder();\n    });\n}\n/**\n * Mouse down action, initializing drag&drop mode.\n *\n * @private\n * @param {Highcharts.Point} point\n *        The point that event occurred.\n * @param {Highcharts.PointerEventObject} event\n *        Browser event, before normalization.\n */\nfunction onMouseDown(point, event) {\n    const { panKey } = this.chart.options.chart, panKeyPressed = panKey && event[`${panKey}Key`];\n    if (panKeyPressed) {\n        return;\n    }\n    const normalizedEvent = this.chart.pointer?.normalize(event) || event;\n    point.fixedPosition = {\n        chartX: normalizedEvent.chartX,\n        chartY: normalizedEvent.chartY,\n        plotX: point.plotX,\n        plotY: point.plotY\n    };\n    point.inDragMode = true;\n}\n/**\n * Mouse move action during drag&drop.\n *\n * @private\n *\n * @param {Highcharts.Point} point\n *        The point that event occurred.\n * @param {global.Event} event\n *        Browser event, before normalization.\n */\nfunction onMouseMove(point, event) {\n    if (point.fixedPosition && point.inDragMode) {\n        const series = this, chart = series.chart, normalizedEvent = chart.pointer?.normalize(event) || event, diffX = point.fixedPosition.chartX - normalizedEvent.chartX, diffY = point.fixedPosition.chartY - normalizedEvent.chartY, graphLayoutsLookup = chart.graphLayoutsLookup;\n        let newPlotX, newPlotY;\n        // At least 5px to apply change (avoids simple click):\n        if (Math.abs(diffX) > 5 || Math.abs(diffY) > 5) {\n            newPlotX = point.fixedPosition.plotX - diffX;\n            newPlotY = point.fixedPosition.plotY - diffY;\n            if (chart.isInsidePlot(newPlotX, newPlotY)) {\n                point.plotX = newPlotX;\n                point.plotY = newPlotY;\n                point.hasDragged = true;\n                this.redrawHalo(point);\n                graphLayoutsLookup.forEach((layout) => {\n                    layout.restartSimulation();\n                });\n            }\n        }\n    }\n}\n/**\n * Mouse up action, finalizing drag&drop.\n *\n * @private\n * @param {Highcharts.Point} point\n *        The point that event occurred.\n */\nfunction onMouseUp(point) {\n    if (point.fixedPosition) {\n        if (point.hasDragged) {\n            if (this.layout.enableSimulation) {\n                this.layout.start();\n            }\n            else {\n                this.chart.redraw();\n            }\n        }\n        point.inDragMode = point.hasDragged = false;\n        if (!this.options.fixedDraggable) {\n            delete point.fixedPosition;\n        }\n    }\n}\n/**\n * Redraw halo on mousemove during the drag&drop action.\n *\n * @private\n * @param {Highcharts.Point} point\n *        The point that should show halo.\n */\nfunction redrawHalo(point) {\n    if (point && this.halo) {\n        this.halo.attr({\n            d: point.haloPath(this.options.states.hover.halo.size)\n        });\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst DragNodesComposition = {\n    compose,\n    onMouseDown,\n    onMouseMove,\n    onMouseUp,\n    redrawHalo\n};\n/* harmony default export */ const Series_DragNodesComposition = (DragNodesComposition);\n\n;// ./code/es-modules/Series/GraphLayoutComposition.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { setAnimation } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { composed: GraphLayoutComposition_composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent: GraphLayoutComposition_addEvent, pushUnique: GraphLayoutComposition_pushUnique } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\nconst integrations = {};\nconst layouts = {};\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction GraphLayoutComposition_compose(ChartClass) {\n    if (GraphLayoutComposition_pushUnique(GraphLayoutComposition_composed, 'GraphLayout')) {\n        GraphLayoutComposition_addEvent(ChartClass, 'afterPrint', onChartAfterPrint);\n        GraphLayoutComposition_addEvent(ChartClass, 'beforePrint', onChartBeforePrint);\n        GraphLayoutComposition_addEvent(ChartClass, 'predraw', onChartPredraw);\n        GraphLayoutComposition_addEvent(ChartClass, 'render', onChartRender);\n    }\n}\n/**\n * Re-enable simulation after print.\n * @private\n */\nfunction onChartAfterPrint() {\n    if (this.graphLayoutsLookup) {\n        this.graphLayoutsLookup.forEach((layout) => {\n            // Return to default simulation\n            layout.updateSimulation();\n        });\n        this.redraw();\n    }\n}\n/**\n * Disable simulation before print if enabled.\n * @private\n */\nfunction onChartBeforePrint() {\n    if (this.graphLayoutsLookup) {\n        this.graphLayoutsLookup.forEach((layout) => {\n            layout.updateSimulation(false);\n        });\n        this.redraw();\n    }\n}\n/**\n * Clear previous layouts.\n * @private\n */\nfunction onChartPredraw() {\n    if (this.graphLayoutsLookup) {\n        this.graphLayoutsLookup.forEach((layout) => {\n            layout.stop();\n        });\n    }\n}\n/**\n * @private\n */\nfunction onChartRender() {\n    let systemsStable, afterRender = false;\n    const layoutStep = (layout) => {\n        if (layout.maxIterations-- &&\n            isFinite(layout.temperature) &&\n            !layout.isStable() &&\n            !layout.enableSimulation) {\n            // Hook similar to build-in addEvent, but instead of\n            // creating whole events logic, use just a function.\n            // It's faster which is important for rAF code.\n            // Used e.g. in packed-bubble series for bubble radius\n            // calculations\n            if (layout.beforeStep) {\n                layout.beforeStep();\n            }\n            layout.step();\n            systemsStable = false;\n            afterRender = true;\n        }\n    };\n    // Don't animate layout when series is dragged\n    if (this.graphLayoutsLookup && !this.pointer?.hasDragged) {\n        setAnimation(false, this);\n        // Start simulation\n        this.graphLayoutsLookup.forEach((layout) => layout.start());\n        // Just one sync step, to run different layouts similar to\n        // async mode.\n        while (!systemsStable) {\n            systemsStable = true;\n            this.graphLayoutsLookup.forEach(layoutStep);\n        }\n        if (afterRender) {\n            this.series.forEach((series) => {\n                if (series && series.layout) {\n                    series.render();\n                }\n            });\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst GraphLayoutComposition = {\n    compose: GraphLayoutComposition_compose,\n    integrations,\n    layouts\n};\n/* harmony default export */ const Series_GraphLayoutComposition = (GraphLayoutComposition);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/NodesComposition.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { series: { prototype: seriesProto, prototype: { pointClass: { prototype: pointProto } } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { defined, extend, find, merge, pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\nvar NodesComposition;\n(function (NodesComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    function compose(PointClass, SeriesClass) {\n        const pointProto = PointClass.prototype, seriesProto = SeriesClass.prototype;\n        pointProto.setNodeState = setNodeState;\n        pointProto.setState = setNodeState;\n        pointProto.update = updateNode;\n        seriesProto.destroy = destroy;\n        seriesProto.setData = setData;\n        return SeriesClass;\n    }\n    NodesComposition.compose = compose;\n    /**\n     * Create a single node that holds information on incoming and outgoing\n     * links.\n     * @private\n     */\n    function createNode(id) {\n        const PointClass = this.pointClass, findById = (nodes, id) => find(nodes, (node) => node.id === id);\n        let node = findById(this.nodes, id), options;\n        if (!node) {\n            options = this.options.nodes && findById(this.options.nodes, id);\n            const newNode = new PointClass(this, extend({\n                className: 'highcharts-node',\n                isNode: true,\n                id: id,\n                y: 1 // Pass isNull test\n            }, options));\n            newNode.linksTo = [];\n            newNode.linksFrom = [];\n            /**\n             * Return the largest sum of either the incoming or outgoing links.\n             * @private\n             */\n            newNode.getSum = function () {\n                let sumTo = 0, sumFrom = 0;\n                newNode.linksTo.forEach((link) => {\n                    sumTo += link.weight || 0;\n                });\n                newNode.linksFrom.forEach((link) => {\n                    sumFrom += link.weight || 0;\n                });\n                return Math.max(sumTo, sumFrom);\n            };\n            /**\n             * Get the offset in weight values of a point/link.\n             * @private\n             */\n            newNode.offset = function (point, coll) {\n                let offset = 0;\n                for (let i = 0; i < newNode[coll].length; i++) {\n                    if (newNode[coll][i] === point) {\n                        return offset;\n                    }\n                    offset += newNode[coll][i].weight;\n                }\n            };\n            // Return true if the node has a shape, otherwise all links are\n            // outgoing.\n            newNode.hasShape = function () {\n                let outgoing = 0;\n                newNode.linksTo.forEach((link) => {\n                    if (link.outgoing) {\n                        outgoing++;\n                    }\n                });\n                return (!newNode.linksTo.length ||\n                    outgoing !== newNode.linksTo.length);\n            };\n            newNode.index = this.nodes.push(newNode) - 1;\n            node = newNode;\n        }\n        node.formatPrefix = 'node';\n        // For use in formats\n        node.name = node.name || node.options.id || '';\n        // Mass is used in networkgraph:\n        node.mass = pick(\n        // Node:\n        node.options.mass, node.options.marker && node.options.marker.radius, \n        // Series:\n        this.options.marker && this.options.marker.radius, \n        // Default:\n        4);\n        return node;\n    }\n    NodesComposition.createNode = createNode;\n    /**\n     * Destroy all nodes and links.\n     * @private\n     */\n    function destroy() {\n        // Nodes must also be destroyed (#8682, #9300)\n        this.data = []\n            .concat(this.points || [], this.nodes);\n        return seriesProto.destroy.apply(this, arguments);\n    }\n    NodesComposition.destroy = destroy;\n    /**\n     * Extend generatePoints by adding the nodes, which are Point objects but\n     * pushed to the this.nodes array.\n     * @private\n     */\n    function generatePoints() {\n        const chart = this.chart, nodeLookup = {};\n        seriesProto.generatePoints.call(this);\n        if (!this.nodes) {\n            this.nodes = []; // List of Point-like node items\n        }\n        this.colorCounter = 0;\n        // Reset links from previous run\n        this.nodes.forEach((node) => {\n            node.linksFrom.length = 0;\n            node.linksTo.length = 0;\n            node.level = node.options.level;\n        });\n        // Create the node list and set up links\n        this.points.forEach((point) => {\n            if (defined(point.from)) {\n                if (!nodeLookup[point.from]) {\n                    nodeLookup[point.from] = this.createNode(point.from);\n                }\n                nodeLookup[point.from].linksFrom.push(point);\n                point.fromNode = nodeLookup[point.from];\n                // Point color defaults to the fromNode's color\n                if (chart.styledMode) {\n                    point.colorIndex = pick(point.options.colorIndex, nodeLookup[point.from].colorIndex);\n                }\n                else {\n                    point.color =\n                        point.options.color || nodeLookup[point.from].color;\n                }\n            }\n            if (defined(point.to)) {\n                if (!nodeLookup[point.to]) {\n                    nodeLookup[point.to] = this.createNode(point.to);\n                }\n                nodeLookup[point.to].linksTo.push(point);\n                point.toNode = nodeLookup[point.to];\n            }\n            point.name = point.name || point.id; // For use in formats\n        }, this);\n        // Store lookup table for later use\n        this.nodeLookup = nodeLookup;\n    }\n    NodesComposition.generatePoints = generatePoints;\n    /**\n     * Destroy all nodes on setting new data\n     * @private\n     */\n    function setData() {\n        if (this.nodes) {\n            this.nodes.forEach((node) => {\n                node.destroy();\n            });\n            this.nodes.length = 0;\n        }\n        seriesProto.setData.apply(this, arguments);\n    }\n    /**\n     * When hovering node, highlight all connected links. When hovering a link,\n     * highlight all connected nodes.\n     * @private\n     */\n    function setNodeState(state) {\n        const args = arguments, others = this.isNode ? this.linksTo.concat(this.linksFrom) :\n            [this.fromNode, this.toNode];\n        if (state !== 'select') {\n            others.forEach((linkOrNode) => {\n                if (linkOrNode && linkOrNode.series) {\n                    pointProto.setState.apply(linkOrNode, args);\n                    if (!linkOrNode.isNode) {\n                        if (linkOrNode.fromNode.graphic) {\n                            pointProto.setState.apply(linkOrNode.fromNode, args);\n                        }\n                        if (linkOrNode.toNode && linkOrNode.toNode.graphic) {\n                            pointProto.setState.apply(linkOrNode.toNode, args);\n                        }\n                    }\n                }\n            });\n        }\n        pointProto.setState.apply(this, args);\n    }\n    NodesComposition.setNodeState = setNodeState;\n    /**\n     * When updating a node, don't update `series.options.data`, but\n     * `series.options.nodes`\n     * @private\n     */\n    function updateNode(options, redraw, animation, runEvent) {\n        const nodes = this.series.options.nodes, data = this.series.options.data, dataLength = data?.length || 0, linkConfig = data?.[this.index];\n        pointProto.update.call(this, options, this.isNode ? false : redraw, // Hold the redraw for nodes\n        animation, runEvent);\n        if (this.isNode) {\n            // `this.index` refers to `series.nodes`, not `options.nodes` array\n            const nodeIndex = (nodes || [])\n                .reduce(// Array.findIndex needs a polyfill\n            (prevIndex, n, index) => (this.id === n.id ? index : prevIndex), -1), \n            // Merge old config with new config. New config is stored in\n            // options.data, because of default logic in point.update()\n            nodeConfig = merge(nodes && nodes[nodeIndex] || {}, data?.[this.index] || {});\n            // Restore link config\n            if (data) {\n                if (linkConfig) {\n                    data[this.index] = linkConfig;\n                }\n                else {\n                    // Remove node from config if there's more nodes than links\n                    data.length = dataLength;\n                }\n            }\n            // Set node config\n            if (nodes) {\n                if (nodeIndex >= 0) {\n                    nodes[nodeIndex] = nodeConfig;\n                }\n                else {\n                    nodes.push(nodeConfig);\n                }\n            }\n            else {\n                this.series.options.nodes = [nodeConfig];\n            }\n            if (pick(redraw, true)) {\n                this.series.chart.redraw(animation);\n            }\n        }\n    }\n    NodesComposition.updateNode = updateNode;\n})(NodesComposition || (NodesComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Series_NodesComposition = (NodesComposition);\n\n;// ./code/es-modules/Series/Networkgraph/NetworkgraphPoint.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { series: { prototype: NetworkgraphPoint_seriesProto, prototype: { pointClass: Point } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { addEvent: NetworkgraphPoint_addEvent, css, defined: NetworkgraphPoint_defined, extend: NetworkgraphPoint_extend, pick: NetworkgraphPoint_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass NetworkgraphPoint extends Point {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Destroy point. If it's a node, remove all links coming out of this\n     * node. Then remove point from the layout.\n     * @private\n     */\n    destroy() {\n        if (this.isNode) {\n            this.linksFrom.concat(this.linksTo).forEach(function (link) {\n                // Removing multiple nodes at the same time\n                // will try to remove link between nodes twice\n                if (link.destroyElements) {\n                    link.destroyElements();\n                }\n            });\n        }\n        this.series.layout.removeElementFromCollection(this, this.series.layout[this.isNode ? 'nodes' : 'links']);\n        return Point.prototype.destroy.apply(this, arguments);\n    }\n    /**\n     * Return degree of a node. If node has no connections, it still has\n     * deg=1.\n     * @private\n     */\n    getDegree() {\n        const deg = this.isNode ?\n            this.linksFrom.length + this.linksTo.length :\n            0;\n        return deg === 0 ? 1 : deg;\n    }\n    /**\n     * Get presentational attributes of link connecting two nodes.\n     * @private\n     */\n    getLinkAttributes() {\n        const linkOptions = this.series.options.link, pointOptions = this.options;\n        return {\n            'stroke-width': NetworkgraphPoint_pick(pointOptions.width, linkOptions.width),\n            stroke: (pointOptions.color || linkOptions.color),\n            dashstyle: (pointOptions.dashStyle || linkOptions.dashStyle),\n            opacity: NetworkgraphPoint_pick(pointOptions.opacity, linkOptions.opacity, 1)\n        };\n    }\n    /**\n     * Get link path connecting two nodes.\n     * @private\n     * @return {Array<Highcharts.SVGPathArray>}\n     *         Path: `['M', x, y, 'L', x, y]`\n     */\n    getLinkPath() {\n        let left = this.fromNode, right = this.toNode;\n        // Start always from left to the right node, to prevent rendering\n        // labels upside down\n        if (left.plotX > right.plotX) {\n            left = this.toNode;\n            right = this.fromNode;\n        }\n        return [\n            ['M', left.plotX || 0, left.plotY || 0],\n            ['L', right.plotX || 0, right.plotY || 0]\n        ];\n        /*\n        IDEA: different link shapes?\n        return [\n            'M',\n            from.plotX,\n            from.plotY,\n            'Q',\n            (to.plotX + from.plotX) / 2,\n            (to.plotY + from.plotY) / 2 + 15,\n            to.plotX,\n            to.plotY\n        ];*/\n    }\n    /**\n     * Get mass fraction applied on two nodes connected to each other. By\n     * default, when mass is equal to `1`, mass fraction for both nodes\n     * equal to 0.5.\n     * @private\n     * @return {Highcharts.Dictionary<number>}\n     *         For example `{ fromNode: 0.5, toNode: 0.5 }`\n     */\n    getMass() {\n        const m1 = this.fromNode.mass, m2 = this.toNode.mass, sum = m1 + m2;\n        return {\n            fromNode: 1 - m1 / sum,\n            toNode: 1 - m2 / sum\n        };\n    }\n    /**\n     * Basic `point.init()` and additional styles applied when\n     * `series.draggable` is enabled.\n     * @private\n     */\n    constructor(series, options, x) {\n        super(series, options, x);\n        if (this.series.options.draggable &&\n            !this.series.chart.styledMode) {\n            NetworkgraphPoint_addEvent(this, 'mouseOver', function () {\n                css(this.series.chart.container, { cursor: 'move' });\n            });\n            NetworkgraphPoint_addEvent(this, 'mouseOut', function () {\n                css(this.series.chart.container, { cursor: 'default' });\n            });\n        }\n    }\n    /**\n     * @private\n     */\n    isValid() {\n        return !this.isNode || NetworkgraphPoint_defined(this.id);\n    }\n    /**\n     * Redraw link's path.\n     * @private\n     */\n    redrawLink() {\n        const path = this.getLinkPath();\n        let attribs;\n        if (this.graphic) {\n            this.shapeArgs = {\n                d: path\n            };\n            if (!this.series.chart.styledMode) {\n                attribs = this.series.pointAttribs(this);\n                this.graphic.attr(attribs);\n                (this.dataLabels || []).forEach(function (label) {\n                    if (label) {\n                        label.attr({\n                            opacity: attribs.opacity\n                        });\n                    }\n                });\n            }\n            this.graphic.animate(this.shapeArgs);\n            // Required for dataLabels\n            const start = path[0];\n            const end = path[1];\n            if (start[0] === 'M' && end[0] === 'L') {\n                this.plotX = (start[1] + end[1]) / 2;\n                this.plotY = (start[2] + end[2]) / 2;\n            }\n        }\n    }\n    /**\n     * Common method for removing points and nodes in networkgraph. To\n     * remove `link`, use `series.data[index].remove()`. To remove `node`\n     * with all connections, use `series.nodes[index].remove()`.\n     * @private\n     * @param {boolean} [redraw=true]\n     *        Whether to redraw the chart or wait for an explicit call. When\n     *        doing more operations on the chart, for example running\n     *        `point.remove()` in a loop, it is best practice to set\n     *        `redraw` to false and call `chart.redraw()` after.\n     * @param {boolean|Partial<Highcharts.AnimationOptionsObject>} [animation=false]\n     *        Whether to apply animation, and optionally animation\n     *        configuration.\n     */\n    remove(redraw, animation) {\n        const point = this, series = point.series, nodesOptions = series.options.nodes || [];\n        let index, i = nodesOptions.length;\n        // For nodes, remove all connected links:\n        if (point.isNode) {\n            // Temporary disable series.points array, because\n            // Series.removePoint() modifies it\n            series.points = [];\n            // Remove link from all nodes collections:\n            []\n                .concat(point.linksFrom)\n                .concat(point.linksTo)\n                .forEach(function (linkFromTo) {\n                // Incoming links\n                index = linkFromTo.fromNode.linksFrom.indexOf(linkFromTo);\n                if (index > -1) {\n                    linkFromTo.fromNode.linksFrom.splice(index, 1);\n                }\n                // Outcoming links\n                index = linkFromTo.toNode.linksTo.indexOf(linkFromTo);\n                if (index > -1) {\n                    linkFromTo.toNode.linksTo.splice(index, 1);\n                }\n                // Remove link from data/points collections\n                NetworkgraphPoint_seriesProto.removePoint.call(series, series.data.indexOf(linkFromTo), false, false);\n            });\n            // Restore points array, after links are removed\n            series.points = series.data.slice();\n            // Proceed with removing node. It's similar to\n            // Series.removePoint() method, but doesn't modify other arrays\n            series.nodes.splice(series.nodes.indexOf(point), 1);\n            // Remove node options from config\n            while (i--) {\n                if (nodesOptions[i].id === point.options.id) {\n                    series.options.nodes.splice(i, 1);\n                    break;\n                }\n            }\n            if (point) {\n                point.destroy();\n            }\n            // Run redraw if requested\n            series.isDirty = true;\n            series.isDirtyData = true;\n            if (redraw) {\n                series.chart.redraw(redraw);\n            }\n        }\n        else {\n            series.removePoint(series.data.indexOf(point), redraw, animation);\n        }\n    }\n    /**\n     * Render link and add it to the DOM.\n     * @private\n     */\n    renderLink() {\n        let attribs;\n        if (!this.graphic) {\n            this.graphic = this.series.chart.renderer\n                .path(this.getLinkPath())\n                .addClass(this.getClassName(), true)\n                .add(this.series.group);\n            if (!this.series.chart.styledMode) {\n                attribs = this.series.pointAttribs(this);\n                this.graphic.attr(attribs);\n                (this.dataLabels || []).forEach(function (label) {\n                    if (label) {\n                        label.attr({\n                            opacity: attribs.opacity\n                        });\n                    }\n                });\n            }\n        }\n    }\n}\nNetworkgraphPoint_extend(NetworkgraphPoint.prototype, {\n    setState: Series_NodesComposition.setNodeState\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Networkgraph_NetworkgraphPoint = (NetworkgraphPoint);\n\n;// ./code/es-modules/Series/Networkgraph/NetworkgraphSeriesDefaults.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n/**\n * A networkgraph is a type of relationship chart, where connnections\n * (links) attracts nodes (points) and other nodes repulse each other.\n *\n * @extends      plotOptions.line\n * @product      highcharts\n * @sample       highcharts/demo/network-graph/\n *               Networkgraph\n * @since        7.0.0\n * @excluding    boostThreshold, animation, animationLimit, connectEnds,\n *               colorAxis, colorKey, connectNulls, cropThreshold, dragDrop,\n *               getExtremesFromAll, label, linecap, negativeColor,\n *               pointInterval, pointIntervalUnit, pointPlacement,\n *               pointStart, softThreshold, stack, stacking, step,\n *               threshold, xAxis, yAxis, zoneAxis, dataSorting,\n *               boostBlending\n * @requires     modules/networkgraph\n * @optionparent plotOptions.networkgraph\n *\n * @private\n */\nconst NetworkgraphSeriesDefaults = {\n    stickyTracking: false,\n    /**\n     * @default   true\n     * @extends   plotOptions.series.inactiveOtherPoints\n     * @private\n     */\n    inactiveOtherPoints: true,\n    marker: {\n        enabled: true,\n        states: {\n            /**\n             * The opposite state of a hover for a single point node.\n             * Applied to all not connected nodes to the hovered one.\n             *\n             * @declare Highcharts.PointStatesInactiveOptionsObject\n             */\n            inactive: {\n                /**\n                 * Opacity of inactive markers.\n                 */\n                opacity: 0.3,\n                /**\n                 * Animation when not hovering over the node.\n                 *\n                 * @type {boolean|Partial<Highcharts.AnimationOptionsObject>}\n                 */\n                animation: {\n                    /** @internal */\n                    duration: 50\n                }\n            }\n        }\n    },\n    states: {\n        /**\n         * The opposite state of a hover for a single point link. Applied\n         * to all links that are not coming from the hovered node.\n         *\n         * @declare Highcharts.SeriesStatesInactiveOptionsObject\n         */\n        inactive: {\n            /**\n             * Opacity of inactive links.\n             */\n            linkOpacity: 0.3,\n            /**\n             * Animation when not hovering over the node.\n             *\n             * @type {boolean|Partial<Highcharts.AnimationOptionsObject>}\n             */\n            animation: {\n                /** @internal */\n                duration: 50\n            }\n        }\n    },\n    /**\n     * @sample highcharts/series-networkgraph/link-datalabels\n     *         Networkgraph with labels on links\n     * @sample highcharts/series-networkgraph/textpath-datalabels\n     *         Networkgraph with labels around nodes\n     * @sample highcharts/series-networkgraph/link-datalabels\n     *         Data labels moved into the nodes\n     * @sample highcharts/series-networkgraph/link-datalabels\n     *         Data labels moved under the links\n     *\n     * @declare Highcharts.SeriesNetworkgraphDataLabelsOptionsObject\n     *\n     * @private\n     */\n    dataLabels: {\n        /**\n         * The\n         * [format string](https://www.highcharts.com/docs/chart-concepts/labels-and-string-formatting)\n         * specifying what to show for _node_ in the networkgraph. In v7.0\n         * defaults to `{key}`, since v7.1 defaults to `undefined` and\n         * `formatter` is used instead.\n         *\n         * @type      {string}\n         * @since     7.0.0\n         * @apioption plotOptions.networkgraph.dataLabels.format\n         */\n        // eslint-disable-next-line valid-jsdoc\n        /**\n         * Callback JavaScript function to format the data label for a node.\n         * Note that if a `format` is defined, the format takes precedence\n         * and the formatter is ignored.\n         *\n         * @since 7.0.0\n         */\n        formatter: function () {\n            return String(this.key ?? '');\n        },\n        /**\n         * The\n         * [format string](https://www.highcharts.com/docs/chart-concepts/labels-and-string-formatting)\n         * specifying what to show for _links_ in the networkgraph.\n         * (Default: `undefined`)\n         *\n         * @type      {string}\n         * @since     7.1.0\n         * @apioption plotOptions.networkgraph.dataLabels.linkFormat\n         */\n        // eslint-disable-next-line valid-jsdoc\n        /**\n         * Callback to format data labels for _links_ in the sankey diagram.\n         * The `linkFormat` option takes precedence over the\n         * `linkFormatter`.\n         *\n         * @since 7.1.0\n         */\n        linkFormatter: function () {\n            return (this.fromNode.name +\n                '<br>' +\n                this.toNode.name);\n        },\n        /**\n         * Options for a _link_ label text which should follow link\n         * connection. Border and background are disabled for a label that\n         * follows a path.\n         *\n         * **Note:** Only SVG-based renderer supports this option. Setting\n         * `useHTML` to true will disable this option.\n         *\n         * @extends plotOptions.networkgraph.dataLabels.textPath\n         * @since   7.1.0\n         */\n        linkTextPath: {\n            enabled: true\n        },\n        textPath: {\n            enabled: false\n        },\n        style: {\n            transition: 'opacity 2000ms'\n        },\n        defer: true,\n        animation: {\n            defer: 1000\n        }\n    },\n    /**\n     * Link style options\n     * @private\n     */\n    link: {\n        /**\n         * A name for the dash style to use for links.\n         *\n         * @type      {string}\n         * @apioption plotOptions.networkgraph.link.dashStyle\n         */\n        /**\n         * Opacity of the link between two nodes.\n         *\n         * @type      {number}\n         * @default   1\n         * @apioption plotOptions.networkgraph.link.opacity\n         */\n        /**\n         * Color of the link between two nodes.\n         */\n        color: 'rgba(100, 100, 100, 0.5)',\n        /**\n         * Width (px) of the link between two nodes.\n         */\n        width: 1\n    },\n    /**\n     * Flag to determine if nodes are draggable or not.\n     * @private\n     */\n    draggable: true,\n    layoutAlgorithm: {\n        /**\n         * Repulsive force applied on a node. Passed are two arguments:\n         * - `d` - which is current distance between two nodes\n         * - `k` - which is desired distance between two nodes\n         *\n         * In `verlet` integration, defaults to:\n         * `function (d, k) { return (k - d) / d * (k > d ? 1 : 0) }`\n         *\n         * @see [layoutAlgorithm.integration](#series.networkgraph.layoutAlgorithm.integration)\n         *\n         * @sample highcharts/series-networkgraph/forces/\n         *         Custom forces with Euler integration\n         * @sample highcharts/series-networkgraph/cuboids/\n         *         Custom forces with Verlet integration\n         *\n         * @type      {Function}\n         * @default   function (d, k) { return k * k / d; }\n         * @apioption plotOptions.networkgraph.layoutAlgorithm.repulsiveForce\n         */\n        /**\n         * Attraction force applied on a node which is conected to another\n         * node by a link. Passed are two arguments:\n         * - `d` - which is current distance between two nodes\n         * - `k` - which is desired distance between two nodes\n         *\n         * In `verlet` integration, defaults to:\n         * `function (d, k) { return (k - d) / d; }`\n         *\n         * @see [layoutAlgorithm.integration](#series.networkgraph.layoutAlgorithm.integration)\n         *\n         * @sample highcharts/series-networkgraph/forces/\n         *         Custom forces with Euler integration\n         * @sample highcharts/series-networkgraph/cuboids/\n         *         Custom forces with Verlet integration\n         *\n         * @type      {Function}\n         * @default   function (d, k) { return k * k / d; }\n         * @apioption plotOptions.networkgraph.layoutAlgorithm.attractiveForce\n         */\n        /**\n         * Ideal length (px) of the link between two nodes. When not\n         * defined, length is calculated as:\n         * `Math.pow(availableWidth * availableHeight / nodesLength, 0.4);`\n         *\n         * Note: Because of the algorithm specification, length of each link\n         * might be not exactly as specified.\n         *\n         * @sample highcharts/series-networkgraph/styled-links/\n         *         Numerical values\n         *\n         * @type      {number}\n         * @apioption plotOptions.networkgraph.layoutAlgorithm.linkLength\n         */\n        /**\n         * Initial layout algorithm for positioning nodes. Can be one of\n         * built-in options (\"circle\", \"random\") or a function where\n         * positions should be set on each node (`this.nodes`) as\n         * `node.plotX` and `node.plotY`\n         *\n         * @sample highcharts/series-networkgraph/initial-positions/\n         *         Initial positions with callback\n         *\n         * @type {\"circle\"|\"random\"|Function}\n         */\n        initialPositions: 'circle',\n        /**\n         * When `initialPositions` are set to 'circle',\n         * `initialPositionRadius` is a distance from the center of circle,\n         * in which nodes are created.\n         *\n         * @type    {number}\n         * @default 1\n         * @since   7.1.0\n         */\n        initialPositionRadius: 1,\n        /**\n         * Experimental. Enables live simulation of the algorithm\n         * implementation. All nodes are animated as the forces applies on\n         * them.\n         *\n         * @sample highcharts/demo/network-graph/\n         *         Live simulation enabled\n         */\n        enableSimulation: false,\n        /**\n         * Barnes-Hut approximation only.\n         * Deteremines when distance between cell and node is small enough\n         * to calculate forces. Value of `theta` is compared directly with\n         * quotient `s / d`, where `s` is the size of the cell, and `d` is\n         * distance between center of cell's mass and currently compared\n         * node.\n         *\n         * @see [layoutAlgorithm.approximation](#series.networkgraph.layoutAlgorithm.approximation)\n         *\n         * @since 7.1.0\n         */\n        theta: 0.5,\n        /**\n         * Verlet integration only.\n         * Max speed that node can get in one iteration. In terms of\n         * simulation, it's a maximum translation (in pixels) that node can\n         * move (in both, x and y, dimensions). While `friction` is applied\n         * on all nodes, max speed is applied only for nodes that move very\n         * fast, for example small or disconnected ones.\n         *\n         * @see [layoutAlgorithm.integration](#series.networkgraph.layoutAlgorithm.integration)\n         * @see [layoutAlgorithm.friction](#series.networkgraph.layoutAlgorithm.friction)\n         *\n         * @since 7.1.0\n         */\n        maxSpeed: 10,\n        /**\n         * Approximation used to calculate repulsive forces affecting nodes.\n         * By default, when calculating net force, nodes are compared\n         * against each other, which gives O(N^2) complexity. Using\n         * Barnes-Hut approximation, we decrease this to O(N log N), but the\n         * resulting graph will have different layout. Barnes-Hut\n         * approximation divides space into rectangles via quad tree, where\n         * forces exerted on nodes are calculated directly for nearby cells,\n         * and for all others, cells are treated as a separate node with\n         * center of mass.\n         *\n         * @see [layoutAlgorithm.theta](#series.networkgraph.layoutAlgorithm.theta)\n         *\n         * @sample highcharts/series-networkgraph/barnes-hut-approximation/\n         *         A graph with Barnes-Hut approximation\n         *\n         * @type       {string}\n         * @validvalue [\"barnes-hut\", \"none\"]\n         * @since      7.1.0\n         */\n        approximation: 'none',\n        /**\n         * Type of the algorithm used when positioning nodes.\n         *\n         * @type       {string}\n         * @validvalue [\"reingold-fruchterman\"]\n         */\n        type: 'reingold-fruchterman',\n        /**\n         * Integration type. Available options are `'euler'` and `'verlet'`.\n         * Integration determines how forces are applied on particles. In\n         * Euler integration, force is applied direct as\n         * `newPosition += velocity;`.\n         * In Verlet integration, new position is based on a previous\n         * position without velocity:\n         * `newPosition += previousPosition - newPosition`.\n         *\n         * Note that different integrations give different results as forces\n         * are different.\n         *\n         * In Highcharts v7.0.x only `'euler'` integration was supported.\n         *\n         * @sample highcharts/series-networkgraph/integration-comparison/\n         *         Comparison of Verlet and Euler integrations\n         *\n         * @type       {string}\n         * @validvalue [\"euler\", \"verlet\"]\n         * @since      7.1.0\n         */\n        integration: 'euler',\n        /**\n         * Max number of iterations before algorithm will stop. In general,\n         * algorithm should find positions sooner, but when rendering huge\n         * number of nodes, it is recommended to increase this value as\n         * finding perfect graph positions can require more time.\n         */\n        maxIterations: 1000,\n        /**\n         * Gravitational const used in the barycenter force of the\n         * algorithm.\n         *\n         * @sample highcharts/series-networkgraph/forces/\n         *         Custom forces with Euler integration\n         */\n        gravitationalConstant: 0.0625,\n        /**\n         * Friction applied on forces to prevent nodes rushing to fast to\n         * the desired positions.\n         */\n        friction: -0.981\n    },\n    showInLegend: false\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Networkgraph_NetworkgraphSeriesDefaults = (NetworkgraphSeriesDefaults);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Fires after the simulation is ended and the layout is stable.\n *\n * @type      {Highcharts.NetworkgraphAfterSimulationCallbackFunction}\n * @product   highcharts\n * @apioption series.networkgraph.events.afterSimulation\n */\n/**\n * A `networkgraph` series. If the [type](#series.networkgraph.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.networkgraph\n * @excluding boostThreshold, animation, animationLimit, connectEnds,\n *            connectNulls, cropThreshold, dragDrop, getExtremesFromAll, label,\n *            linecap, negativeColor, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointStart, softThreshold, stack, stacking,\n *            step, threshold, xAxis, yAxis, zoneAxis, dataSorting,\n *            boostBlending\n * @product   highcharts\n * @requires  modules/networkgraph\n * @apioption series.networkgraph\n */\n/**\n * An array of data points for the series. For the `networkgraph` series type,\n * points can be given in the following way:\n *\n * An array of objects with named values. The following snippet shows only a\n * few settings, see the complete options set below. If the total number of\n * data points exceeds the series'\n * [turboThreshold](#series.area.turboThreshold), this option is not available.\n *\n *  ```js\n *     data: [{\n *         from: 'Category1',\n *         to: 'Category2'\n *     }, {\n *         from: 'Category1',\n *         to: 'Category3'\n *     }]\n *  ```\n *\n * @type      {Array<Object|Array|number>}\n * @extends   series.line.data\n * @excluding drilldown,marker,x,y,dragDrop\n * @sample    {highcharts} highcharts/chart/reflow-true/\n *            Numerical values\n * @sample    {highcharts} highcharts/series/data-array-of-arrays/\n *            Arrays of numeric x and y\n * @sample    {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *            Arrays of datetime x and y\n * @sample    {highcharts} highcharts/series/data-array-of-name-value/\n *            Arrays of point.name and y\n * @sample    {highcharts} highcharts/series/data-array-of-objects/\n *            Config objects\n * @product   highcharts\n * @apioption series.networkgraph.data\n */\n/**\n * @type      {Highcharts.SeriesNetworkgraphDataLabelsOptionsObject|Array<Highcharts.SeriesNetworkgraphDataLabelsOptionsObject>}\n * @product   highcharts\n * @apioption series.networkgraph.data.dataLabels\n */\n/**\n * The node that the link runs from.\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.networkgraph.data.from\n */\n/**\n * The node that the link runs to.\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.networkgraph.data.to\n */\n/**\n * A collection of options for the individual nodes. The nodes in a\n * networkgraph diagram are auto-generated instances of `Highcharts.Point`,\n * but options can be applied here and linked by the `id`.\n *\n * @sample highcharts/series-networkgraph/data-options/\n *         Networkgraph diagram with node options\n *\n * @type      {Array<*>}\n * @product   highcharts\n * @apioption series.networkgraph.nodes\n */\n/**\n * The id of the auto-generated node, referring to the `from` or `to` setting of\n * the link.\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.networkgraph.nodes.id\n */\n/**\n * The color of the auto generated node.\n *\n * @type      {Highcharts.ColorString}\n * @product   highcharts\n * @apioption series.networkgraph.nodes.color\n */\n/**\n * The color index of the auto generated node, especially for use in styled\n * mode.\n *\n * @type      {number}\n * @product   highcharts\n * @apioption series.networkgraph.nodes.colorIndex\n */\n/**\n * The name to display for the node in data labels and tooltips. Use this when\n * the name is different from the `id`. Where the id must be unique for each\n * node, this is not necessary for the name.\n *\n * @sample highcharts/series-networkgraph/data-options/\n *         Networkgraph diagram with node options\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.networkgraph.nodes.name\n */\n/**\n * Mass of the node. By default, each node has mass equal to it's marker radius\n * . Mass is used to determine how two connected nodes should affect\n * each other:\n *\n * Attractive force is multiplied by the ratio of two connected\n * nodes; if a big node has weights twice as the small one, then the small one\n * will move towards the big one twice faster than the big one to the small one\n * .\n *\n * @sample highcharts/series-networkgraph/ragdoll/\n *         Mass determined by marker.radius\n *\n * @type      {number}\n * @product   highcharts\n * @apioption series.networkgraph.nodes.mass\n */\n/**\n * Options for the node markers.\n *\n * @extends   plotOptions.networkgraph.marker\n * @apioption series.networkgraph.nodes.marker\n */\n/**\n * Individual data label for each node. The options are the same as\n * the ones for [series.networkgraph.dataLabels](#series.networkgraph.dataLabels).\n *\n * @type      {Highcharts.SeriesNetworkgraphDataLabelsOptionsObject|Array<Highcharts.SeriesNetworkgraphDataLabelsOptionsObject>}\n *\n * @apioption series.networkgraph.nodes.dataLabels\n */\n''; // Adds doclets above to transpiled file\n\n;// ./code/es-modules/Series/Networkgraph/EulerIntegration.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Attractive force.\n *\n * In Euler integration, force is stored in a node, not changing it's\n * position. Later, in `integrate()` forces are applied on nodes.\n *\n * @private\n * @param {Highcharts.Point} link\n *        Link that connects two nodes\n * @param {number} force\n *        Force calculated in `repulsiveForceFunction`\n * @param {Highcharts.PositionObject} distanceXY\n *        Distance between two nodes e.g. `{x, y}`\n * @param {number} distanceR\n     */\nfunction attractive(link, force, distanceXY, distanceR) {\n    const massFactor = link.getMass(), translatedX = (distanceXY.x / distanceR) * force, translatedY = (distanceXY.y / distanceR) * force;\n    if (!link.fromNode.fixedPosition) {\n        link.fromNode.dispX -=\n            translatedX * massFactor.fromNode / link.fromNode.degree;\n        link.fromNode.dispY -=\n            translatedY * massFactor.fromNode / link.fromNode.degree;\n    }\n    if (!link.toNode.fixedPosition) {\n        link.toNode.dispX +=\n            translatedX * massFactor.toNode / link.toNode.degree;\n        link.toNode.dispY +=\n            translatedY * massFactor.toNode / link.toNode.degree;\n    }\n}\n/**\n * Attractive force function. Can be replaced by API's\n * `layoutAlgorithm.attractiveForce`\n *\n * Other forces that can be used:\n *\n * basic, not recommended:\n *    `function (d, k) { return d / k }`\n *\n * @private\n * @param {number} d current distance between two nodes\n * @param {number} k expected distance between two nodes\n * @return {number} force\n */\nfunction attractiveForceFunction(d, k) {\n    return d * d / k;\n}\n/**\n * Barycenter force. Calculate and applys barycenter forces on the\n * nodes. Making them closer to the center of their barycenter point.\n *\n * In Euler integration, force is stored in a node, not changing it's\n * position. Later, in `integrate()` forces are applied on nodes.\n *\n * @private\n */\nfunction barycenter() {\n    const gravitationalConstant = this.options.gravitationalConstant, xFactor = this.barycenter.xFactor, yFactor = this.barycenter.yFactor;\n    this.nodes.forEach(function (node) {\n        if (!node.fixedPosition) {\n            const degree = node.getDegree(), phi = degree * (1 + degree / 2);\n            node.dispX += ((xFactor - node.plotX) *\n                gravitationalConstant *\n                phi / node.degree);\n            node.dispY += ((yFactor - node.plotY) *\n                gravitationalConstant *\n                phi / node.degree);\n        }\n    });\n}\n/**\n * Estimate the best possible distance between two nodes, making graph\n * readable.\n * @private\n */\nfunction getK(layout) {\n    return Math.pow(layout.box.width * layout.box.height / layout.nodes.length, 0.3);\n}\n/**\n * Integration method.\n *\n * In Euler integration, force were stored in a node, not changing it's\n * position. Now, in the integrator method, we apply changes.\n *\n * Euler:\n *\n * Basic form: `x(n+1) = x(n) + v(n)`\n *\n * With Rengoild-Fruchterman we get:\n * `x(n+1) = x(n) + v(n) / length(v(n)) * min(v(n), temperature(n))`\n * where:\n * - `x(n+1)`: next position\n * - `x(n)`: current position\n * - `v(n)`: velocity (comes from net force)\n * - `temperature(n)`: current temperature\n *\n * Known issues:\n * Oscillations when force vector has the same magnitude but opposite\n * direction in the next step. Potentially solved by decreasing force by\n * `v * (1 / node.degree)`\n *\n * Note:\n * Actually `min(v(n), temperature(n))` replaces simulated annealing.\n *\n * @private\n * @param {Highcharts.NetworkgraphLayout} layout\n *        Layout object\n * @param {Highcharts.Point} node\n *        Node that should be translated\n */\nfunction integrate(layout, node) {\n    node.dispX +=\n        node.dispX * layout.options.friction;\n    node.dispY +=\n        node.dispY * layout.options.friction;\n    const distanceR = node.temperature = layout.vectorLength({\n        x: node.dispX,\n        y: node.dispY\n    });\n    if (distanceR !== 0) {\n        node.plotX += (node.dispX / distanceR *\n            Math.min(Math.abs(node.dispX), layout.temperature));\n        node.plotY += (node.dispY / distanceR *\n            Math.min(Math.abs(node.dispY), layout.temperature));\n    }\n}\n/**\n * Repulsive force.\n *\n * @private\n * @param {Highcharts.Point} node\n *        Node that should be translated by force.\n * @param {number} force\n *        Force calculated in `repulsiveForceFunction`\n * @param {Highcharts.PositionObject} distanceXY\n *        Distance between two nodes e.g. `{x, y}`\n */\nfunction repulsive(node, force, distanceXY, distanceR) {\n    node.dispX +=\n        (distanceXY.x / distanceR) * force / node.degree;\n    node.dispY +=\n        (distanceXY.y / distanceR) * force / node.degree;\n}\n/**\n * Repulsive force function. Can be replaced by API's\n * `layoutAlgorithm.repulsiveForce`.\n *\n * Other forces that can be used:\n *\n * basic, not recommended:\n *    `function (d, k) { return k / d }`\n *\n * standard:\n *    `function (d, k) { return k * k / d }`\n *\n * grid-variant:\n *    `function (d, k) { return k * k / d * (2 * k - d > 0 ? 1 : 0) }`\n *\n * @private\n * @param {number} d current distance between two nodes\n * @param {number} k expected distance between two nodes\n * @return {number} force\n */\nfunction repulsiveForceFunction(d, k) {\n    return k * k / d;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst EulerIntegration = {\n    attractive,\n    attractiveForceFunction,\n    barycenter,\n    getK,\n    integrate,\n    repulsive,\n    repulsiveForceFunction\n};\n/* harmony default export */ const Networkgraph_EulerIntegration = (EulerIntegration);\n\n;// ./code/es-modules/Series/Networkgraph/QuadTreeNode.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * The QuadTree node class. Used in Networkgraph chart as a base for Barnes-Hut\n * approximation.\n *\n * @private\n * @class\n * @name Highcharts.QuadTreeNode\n *\n * @param {Highcharts.Dictionary<number>} box\n *        Available space for the node\n */\nclass QuadTreeNode {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(box) {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        /**\n         * Read only. If QuadTreeNode is an external node, Point is stored in\n         * `this.body`.\n         *\n         * @name Highcharts.QuadTreeNode#body\n         * @type {boolean|Highcharts.Point}\n         */\n        this.body = false;\n        /**\n         * Read only. Internal nodes when created are empty to reserve the\n         * space. If Point is added to this QuadTreeNode, QuadTreeNode is no\n         * longer empty.\n         *\n         * @name Highcharts.QuadTreeNode#isEmpty\n         * @type {boolean}\n         */\n        this.isEmpty = false;\n        /**\n         * Read only. Flag to determine if QuadTreeNode is internal (and has\n         * subnodes with mass and central position) or external (bound to\n         * Point).\n         *\n         * @name Highcharts.QuadTreeNode#isInternal\n         * @type {boolean}\n         */\n        this.isInternal = false;\n        /**\n         * Read only. Array of subnodes. Empty if QuadTreeNode has just one\n         * Point. When added another Point to this QuadTreeNode, array is\n         * filled with four subnodes.\n         *\n         * @name Highcharts.QuadTreeNode#nodes\n         * @type {Array<Highcharts.QuadTreeNode>}\n         */\n        this.nodes = [];\n        /**\n         * Read only. The available space for node.\n         *\n         * @name Highcharts.QuadTreeNode#box\n         * @type {Highcharts.Dictionary<number>}\n         */\n        this.box = box;\n        /**\n         * Read only. The minium of width and height values.\n         *\n         * @name Highcharts.QuadTreeNode#boxSize\n         * @type {number}\n         */\n        this.boxSize = Math.min(box.width, box.height);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * When inserting another node into the box, that already hove one node,\n     * divide the available space into another four quadrants.\n     *\n     * Indexes of quadrants are:\n     * ```\n     * -------------               -------------\n     * |           |               |     |     |\n     * |           |               |  0  |  1  |\n     * |           |   divide()    |     |     |\n     * |     1     | ----------->  -------------\n     * |           |               |     |     |\n     * |           |               |  3  |  2  |\n     * |           |               |     |     |\n     * -------------               -------------\n     * ```\n     */\n    divideBox() {\n        const halfWidth = this.box.width / 2, halfHeight = this.box.height / 2;\n        // Top left\n        this.nodes[0] = new QuadTreeNode({\n            left: this.box.left,\n            top: this.box.top,\n            width: halfWidth,\n            height: halfHeight\n        });\n        // Top right\n        this.nodes[1] = new QuadTreeNode({\n            left: this.box.left + halfWidth,\n            top: this.box.top,\n            width: halfWidth,\n            height: halfHeight\n        });\n        // Bottom right\n        this.nodes[2] = new QuadTreeNode({\n            left: this.box.left + halfWidth,\n            top: this.box.top + halfHeight,\n            width: halfWidth,\n            height: halfHeight\n        });\n        // Bottom left\n        this.nodes[3] = new QuadTreeNode({\n            left: this.box.left,\n            top: this.box.top + halfHeight,\n            width: halfWidth,\n            height: halfHeight\n        });\n    }\n    /**\n     * Determine which of the quadrants should be used when placing node in\n     * the QuadTree. Returned index is always in range `< 0 , 3 >`.\n     * @private\n     */\n    getBoxPosition(point) {\n        const left = point.plotX < this.box.left + this.box.width / 2, top = point.plotY < this.box.top + this.box.height / 2;\n        let index;\n        if (left) {\n            if (top) {\n                // Top left\n                index = 0;\n            }\n            else {\n                // Bottom left\n                index = 3;\n            }\n        }\n        else {\n            if (top) {\n                // Top right\n                index = 1;\n            }\n            else {\n                // Bottom right\n                index = 2;\n            }\n        }\n        return index;\n    }\n    /**\n     * Insert recursively point(node) into the QuadTree. If the given\n     * quadrant is already occupied, divide it into smaller quadrants.\n     *\n     * @param {Highcharts.Point} point\n     *        Point/node to be inserted\n     * @param {number} depth\n     *        Max depth of the QuadTree\n     */\n    insert(point, depth) {\n        let newQuadTreeNode;\n        if (this.isInternal) {\n            // Internal node:\n            this.nodes[this.getBoxPosition(point)].insert(point, depth - 1);\n        }\n        else {\n            this.isEmpty = false;\n            if (!this.body) {\n                // First body in a quadrant:\n                this.isInternal = false;\n                this.body = point;\n            }\n            else {\n                if (depth) {\n                    // Every other body in a quadrant:\n                    this.isInternal = true;\n                    this.divideBox();\n                    // Reinsert main body only once:\n                    if (this.body !== true) {\n                        this.nodes[this.getBoxPosition(this.body)]\n                            .insert(this.body, depth - 1);\n                        this.body = true;\n                    }\n                    // Add second body:\n                    this.nodes[this.getBoxPosition(point)]\n                        .insert(point, depth - 1);\n                }\n                else {\n                    // We are below max allowed depth. That means either:\n                    // - really huge number of points\n                    // - falling two points into exactly the same position\n                    // In this case, create another node in the QuadTree.\n                    //\n                    // Alternatively we could add some noise to the\n                    // position, but that could result in different\n                    // rendered chart in exporting.\n                    newQuadTreeNode = new QuadTreeNode({\n                        top: point.plotX || NaN,\n                        left: point.plotY || NaN,\n                        // Width/height below 1px\n                        width: 0.1,\n                        height: 0.1\n                    });\n                    newQuadTreeNode.body = point;\n                    newQuadTreeNode.isInternal = false;\n                    this.nodes.push(newQuadTreeNode);\n                }\n            }\n        }\n    }\n    /**\n     * Each quad node requires it's mass and center position. That mass and\n     * position is used to imitate real node in the layout by approximation.\n     */\n    updateMassAndCenter() {\n        let mass = 0, plotX = 0, plotY = 0;\n        if (this.isInternal) {\n            // Calculate weightened mass of the quad node:\n            for (const pointMass of this.nodes) {\n                if (!pointMass.isEmpty) {\n                    mass += pointMass.mass;\n                    plotX += pointMass.plotX * pointMass.mass;\n                    plotY += pointMass.plotY * pointMass.mass;\n                }\n            }\n            plotX /= mass;\n            plotY /= mass;\n        }\n        else if (this.body) {\n            // Just one node, use coordinates directly:\n            mass = this.body.mass;\n            plotX = this.body.plotX;\n            plotY = this.body.plotY;\n        }\n        // Store details:\n        this.mass = mass;\n        this.plotX = plotX;\n        this.plotY = plotY;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Networkgraph_QuadTreeNode = (QuadTreeNode);\n\n;// ./code/es-modules/Series/Networkgraph/QuadTree.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * The QuadTree class. Used in Networkgraph chart as a base for Barnes-Hut\n * approximation.\n *\n * @private\n * @class\n * @name Highcharts.QuadTree\n *\n * @param {number} x\n *        Left position of the plotting area\n * @param {number} y\n *        Top position of the plotting area\n * @param {number} width\n *        Width of the plotting area\n * @param {number} height\n *        Height of the plotting area\n */\nclass QuadTree {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(x, y, width, height) {\n        // Boundary rectangle:\n        this.box = {\n            left: x,\n            top: y,\n            width: width,\n            height: height\n        };\n        this.maxDepth = 25;\n        this.root = new Networkgraph_QuadTreeNode(this.box);\n        this.root.isInternal = true;\n        this.root.isRoot = true;\n        this.root.divideBox();\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Calculate mass of the each QuadNode in the tree.\n     */\n    calculateMassAndCenter() {\n        this.visitNodeRecursive(null, null, function (node) {\n            node.updateMassAndCenter();\n        });\n    }\n    /**\n     * Insert nodes into the QuadTree\n     *\n     * @param {Array<Highcharts.Point>} points\n     *        Points as nodes\n     */\n    insertNodes(points) {\n        for (const point of points) {\n            this.root.insert(point, this.maxDepth);\n        }\n    }\n    /**\n     * Depth first treversal (DFS). Using `before` and `after` callbacks,\n     * we can get two results: preorder and postorder traversals, reminder:\n     *\n     * ```\n     *     (a)\n     *     / \\\n     *   (b) (c)\n     *   / \\\n     * (d) (e)\n     * ```\n     *\n     * DFS (preorder): `a -> b -> d -> e -> c`\n     *\n     * DFS (postorder): `d -> e -> b -> c -> a`\n     *\n     * @param {Highcharts.QuadTreeNode|null} node\n     *        QuadTree node\n     * @param {Function} [beforeCallback]\n     *        Function to be called before visiting children nodes.\n     * @param {Function} [afterCallback]\n     *        Function to be called after visiting children nodes.\n     */\n    visitNodeRecursive(node, beforeCallback, afterCallback) {\n        let goFurther;\n        if (!node) {\n            node = this.root;\n        }\n        if (node === this.root && beforeCallback) {\n            goFurther = beforeCallback(node);\n        }\n        if (goFurther === false) {\n            return;\n        }\n        for (const qtNode of node.nodes) {\n            if (qtNode.isInternal) {\n                if (beforeCallback) {\n                    goFurther = beforeCallback(qtNode);\n                }\n                if (goFurther === false) {\n                    continue;\n                }\n                this.visitNodeRecursive(qtNode, beforeCallback, afterCallback);\n            }\n            else if (qtNode.body) {\n                if (beforeCallback) {\n                    beforeCallback(qtNode.body);\n                }\n            }\n            if (afterCallback) {\n                afterCallback(qtNode);\n            }\n        }\n        if (node === this.root && afterCallback) {\n            afterCallback(node);\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Networkgraph_QuadTree = (QuadTree);\n\n;// ./code/es-modules/Series/Networkgraph/VerletIntegration.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Attractive force.\n *\n * In Verlet integration, force is applied on a node immediately to it's\n * `plotX` and `plotY` position.\n *\n * @private\n * @param {Highcharts.Point} link\n *        Link that connects two nodes\n * @param {number} force\n *        Force calculated in `repulsiveForceFunction`\n * @param {Highcharts.PositionObject} distanceXY\n *        Distance between two nodes e.g. `{x, y}`\n */\nfunction VerletIntegration_attractive(link, force, distanceXY) {\n    const massFactor = link.getMass(), translatedX = -distanceXY.x * force * this.diffTemperature, translatedY = -distanceXY.y * force * this.diffTemperature;\n    if (!link.fromNode.fixedPosition) {\n        link.fromNode.plotX -=\n            translatedX * massFactor.fromNode / link.fromNode.degree;\n        link.fromNode.plotY -=\n            translatedY * massFactor.fromNode / link.fromNode.degree;\n    }\n    if (!link.toNode.fixedPosition) {\n        link.toNode.plotX +=\n            translatedX * massFactor.toNode / link.toNode.degree;\n        link.toNode.plotY +=\n            translatedY * massFactor.toNode / link.toNode.degree;\n    }\n}\n/**\n * Attractive force function. Can be replaced by API's\n * `layoutAlgorithm.attractiveForce`\n *\n * @private\n * @param {number} d current distance between two nodes\n * @param {number} k expected distance between two nodes\n * @return {number} force\n */\nfunction VerletIntegration_attractiveForceFunction(d, k) {\n    // Used in API:\n    return (k - d) / d;\n}\n/**\n * Barycenter force. Calculate and applys barycenter forces on the\n * nodes. Making them closer to the center of their barycenter point.\n *\n * In Verlet integration, force is applied on a node immediately to it's\n * `plotX` and `plotY` position.\n *\n * @private\n */\nfunction VerletIntegration_barycenter() {\n    const gravitationalConstant = this.options.gravitationalConstant || 0, xFactor = (this.barycenter.xFactor -\n        (this.box.left + this.box.width) / 2) * gravitationalConstant, yFactor = (this.barycenter.yFactor -\n        (this.box.top + this.box.height) / 2) * gravitationalConstant;\n    this.nodes.forEach(function (node) {\n        if (!node.fixedPosition) {\n            node.plotX -=\n                xFactor / node.mass / node.degree;\n            node.plotY -=\n                yFactor / node.mass / node.degree;\n        }\n    });\n}\n/**\n * Estiamte the best possible distance between two nodes, making graph\n * readable.\n * @private\n */\nfunction VerletIntegration_getK(layout) {\n    return Math.pow(layout.box.width * layout.box.height / layout.nodes.length, 0.5);\n}\n/**\n * Integration method.\n *\n * In Verlet integration, forces are applied on node immediately to it's\n * `plotX` and `plotY` position.\n *\n * Verlet without velocity:\n *\n *    x(n+1) = 2 * x(n) - x(n-1) + A(T) * deltaT ^ 2\n *\n * where:\n *     - x(n+1) - new position\n *     - x(n) - current position\n *     - x(n-1) - previous position\n *\n * Assuming A(t) = 0 (no acceleration) and (deltaT = 1) we get:\n *\n *     x(n+1) = x(n) + (x(n) - x(n-1))\n *\n * where:\n *     - (x(n) - x(n-1)) - position change\n *\n * TO DO:\n * Consider Verlet with velocity to support additional\n * forces. Or even Time-Corrected Verlet by Jonathan\n * \"lonesock\" Dummer\n *\n * @private\n * @param {Highcharts.NetworkgraphLayout} layout layout object\n * @param {Highcharts.Point} node node that should be translated\n */\nfunction VerletIntegration_integrate(layout, node) {\n    const friction = -layout.options.friction, maxSpeed = layout.options.maxSpeed, prevX = node.prevX, prevY = node.prevY, \n    // Apply friction:\n    frictionX = ((node.plotX + node.dispX -\n        prevX) * friction), frictionY = ((node.plotY + node.dispY -\n        prevY) * friction), abs = Math.abs, signX = abs(frictionX) / (frictionX || 1), // Need to deal with 0\n    signY = abs(frictionY) / (frictionY || 1), \n    // Apply max speed:\n    diffX = signX * Math.min(maxSpeed, Math.abs(frictionX)), diffY = signY * Math.min(maxSpeed, Math.abs(frictionY));\n    // Store for the next iteration:\n    node.prevX = node.plotX + node.dispX;\n    node.prevY = node.plotY + node.dispY;\n    // Update positions:\n    node.plotX += diffX;\n    node.plotY += diffY;\n    node.temperature = layout.vectorLength({\n        x: diffX,\n        y: diffY\n    });\n}\n/**\n * Repulsive force.\n *\n * In Verlet integration, force is applied on a node immediately to it's\n * `plotX` and `plotY` position.\n *\n * @private\n * @param {Highcharts.Point} node\n *        Node that should be translated by force.\n * @param {number} force\n *        Force calculated in `repulsiveForceFunction`\n * @param {Highcharts.PositionObject} distanceXY\n *        Distance between two nodes e.g. `{x, y}`\n */\nfunction VerletIntegration_repulsive(node, force, distanceXY) {\n    const factor = force * this.diffTemperature / node.mass / node.degree;\n    if (!node.fixedPosition) {\n        node.plotX += distanceXY.x * factor;\n        node.plotY += distanceXY.y * factor;\n    }\n}\n/**\n * Repulsive force function. Can be replaced by API's\n * `layoutAlgorithm.repulsiveForce`\n *\n * @private\n * @param {number} d current distance between two nodes\n * @param {number} k expected distance between two nodes\n * @return {number} force\n */\nfunction VerletIntegration_repulsiveForceFunction(d, k) {\n    // Used in API:\n    return (k - d) / d * (k > d ? 1 : 0); // Force only for close nodes\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst VerletIntegration = {\n    attractive: VerletIntegration_attractive,\n    attractiveForceFunction: VerletIntegration_attractiveForceFunction,\n    barycenter: VerletIntegration_barycenter,\n    getK: VerletIntegration_getK,\n    integrate: VerletIntegration_integrate,\n    repulsive: VerletIntegration_repulsive,\n    repulsiveForceFunction: VerletIntegration_repulsiveForceFunction\n};\n/* harmony default export */ const Networkgraph_VerletIntegration = (VerletIntegration);\n\n;// ./code/es-modules/Series/Networkgraph/ReingoldFruchtermanLayout.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { win } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\n\nconst { clamp, defined: ReingoldFruchtermanLayout_defined, isFunction, fireEvent, pick: ReingoldFruchtermanLayout_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * Reingold-Fruchterman algorithm from\n * \"Graph Drawing by Force-directed Placement\" paper.\n * @private\n */\nclass ReingoldFruchtermanLayout {\n    constructor() {\n        /* *\n         *\n         *  Static Functions\n         *\n         * */\n        this.box = {};\n        this.currentStep = 0;\n        this.initialRendering = true;\n        this.links = [];\n        this.nodes = [];\n        this.series = [];\n        this.simulation = false;\n    }\n    static compose(ChartClass) {\n        Series_GraphLayoutComposition.compose(ChartClass);\n        Series_GraphLayoutComposition.integrations.euler = Networkgraph_EulerIntegration;\n        Series_GraphLayoutComposition.integrations.verlet = Networkgraph_VerletIntegration;\n        Series_GraphLayoutComposition.layouts['reingold-fruchterman'] =\n            ReingoldFruchtermanLayout;\n    }\n    init(options) {\n        this.options = options;\n        this.nodes = [];\n        this.links = [];\n        this.series = [];\n        this.box = {\n            x: 0,\n            y: 0,\n            width: 0,\n            height: 0\n        };\n        this.setInitialRendering(true);\n        this.integration =\n            Series_GraphLayoutComposition.integrations[options.integration];\n        this.enableSimulation = options.enableSimulation;\n        this.attractiveForce = ReingoldFruchtermanLayout_pick(options.attractiveForce, this.integration.attractiveForceFunction);\n        this.repulsiveForce = ReingoldFruchtermanLayout_pick(options.repulsiveForce, this.integration.repulsiveForceFunction);\n        this.approximation = options.approximation;\n    }\n    updateSimulation(enable) {\n        this.enableSimulation = ReingoldFruchtermanLayout_pick(enable, this.options.enableSimulation);\n    }\n    start() {\n        const layout = this, series = this.series, options = this.options;\n        layout.currentStep = 0;\n        layout.forces = series[0] && series[0].forces || [];\n        layout.chart = series[0] && series[0].chart;\n        if (layout.initialRendering) {\n            layout.initPositions();\n            // Render elements in initial positions:\n            series.forEach(function (s) {\n                s.finishedAnimating = true; // #13169\n                s.render();\n            });\n        }\n        layout.setK();\n        layout.resetSimulation(options);\n        if (layout.enableSimulation) {\n            layout.step();\n        }\n    }\n    step() {\n        const anyLayout = this, allSeries = this.series;\n        // Algorithm:\n        this.currentStep++;\n        if (this.approximation === 'barnes-hut') {\n            this.createQuadTree();\n            this.quadTree.calculateMassAndCenter();\n        }\n        for (const forceName of this.forces || []) {\n            anyLayout[forceName + 'Forces'](this.temperature);\n        }\n        // Limit to the plotting area and cool down:\n        this.applyLimits();\n        // Cool down the system:\n        this.temperature = this.coolDown(this.startTemperature, this.diffTemperature, this.currentStep);\n        this.prevSystemTemperature = this.systemTemperature;\n        this.systemTemperature = this.getSystemTemperature();\n        if (this.enableSimulation) {\n            for (const series of allSeries) {\n                // Chart could be destroyed during the simulation\n                if (series.chart) {\n                    series.render();\n                }\n            }\n            if (this.maxIterations-- &&\n                isFinite(this.temperature) &&\n                !this.isStable()) {\n                if (this.simulation) {\n                    win.cancelAnimationFrame(this.simulation);\n                }\n                this.simulation = win.requestAnimationFrame(() => this.step());\n            }\n            else {\n                this.simulation = false;\n                this.series.forEach((s) => {\n                    fireEvent(s, 'afterSimulation');\n                });\n            }\n        }\n    }\n    stop() {\n        if (this.simulation) {\n            win.cancelAnimationFrame(this.simulation);\n        }\n    }\n    setArea(x, y, w, h) {\n        this.box = {\n            left: x,\n            top: y,\n            width: w,\n            height: h\n        };\n    }\n    setK() {\n        // Optimal distance between nodes,\n        // available space around the node:\n        this.k = this.options.linkLength || this.integration.getK(this);\n    }\n    addElementsToCollection(elements, collection) {\n        for (const element of elements) {\n            if (collection.indexOf(element) === -1) {\n                collection.push(element);\n            }\n        }\n    }\n    removeElementFromCollection(element, collection) {\n        const index = collection.indexOf(element);\n        if (index !== -1) {\n            collection.splice(index, 1);\n        }\n    }\n    clear() {\n        this.nodes.length = 0;\n        this.links.length = 0;\n        this.series.length = 0;\n        this.resetSimulation();\n    }\n    resetSimulation() {\n        this.forcedStop = false;\n        this.systemTemperature = 0;\n        this.setMaxIterations();\n        this.setTemperature();\n        this.setDiffTemperature();\n    }\n    restartSimulation() {\n        if (!this.simulation) {\n            // When dragging nodes, we don't need to calculate\n            // initial positions and rendering nodes:\n            this.setInitialRendering(false);\n            // Start new simulation:\n            if (!this.enableSimulation) {\n                // Run only one iteration to speed things up:\n                this.setMaxIterations(1);\n            }\n            else {\n                this.start();\n            }\n            if (this.chart) {\n                this.chart.redraw();\n            }\n            // Restore defaults:\n            this.setInitialRendering(true);\n        }\n        else {\n            // Extend current simulation:\n            this.resetSimulation();\n        }\n    }\n    setMaxIterations(maxIterations) {\n        this.maxIterations = ReingoldFruchtermanLayout_pick(maxIterations, this.options.maxIterations);\n    }\n    setTemperature() {\n        this.temperature = this.startTemperature =\n            Math.sqrt(this.nodes.length);\n    }\n    setDiffTemperature() {\n        this.diffTemperature = this.startTemperature /\n            (this.options.maxIterations + 1);\n    }\n    setInitialRendering(enable) {\n        this.initialRendering = enable;\n    }\n    createQuadTree() {\n        this.quadTree = new Networkgraph_QuadTree(this.box.left, this.box.top, this.box.width, this.box.height);\n        this.quadTree.insertNodes(this.nodes);\n    }\n    initPositions() {\n        const initialPositions = this.options.initialPositions;\n        if (isFunction(initialPositions)) {\n            initialPositions.call(this);\n            for (const node of this.nodes) {\n                if (!ReingoldFruchtermanLayout_defined(node.prevX)) {\n                    node.prevX = node.plotX;\n                }\n                if (!ReingoldFruchtermanLayout_defined(node.prevY)) {\n                    node.prevY = node.plotY;\n                }\n                node.dispX = 0;\n                node.dispY = 0;\n            }\n        }\n        else if (initialPositions === 'circle') {\n            this.setCircularPositions();\n        }\n        else {\n            this.setRandomPositions();\n        }\n    }\n    setCircularPositions() {\n        const box = this.box, nodes = this.nodes, nodesLength = nodes.length + 1, angle = 2 * Math.PI / nodesLength, rootNodes = nodes.filter(function (node) {\n            return node.linksTo.length === 0;\n        }), visitedNodes = {}, radius = this.options.initialPositionRadius, addToNodes = (node) => {\n            for (const link of node.linksFrom || []) {\n                if (!visitedNodes[link.toNode.id]) {\n                    visitedNodes[link.toNode.id] = true;\n                    sortedNodes.push(link.toNode);\n                    addToNodes(link.toNode);\n                }\n            }\n        };\n        let sortedNodes = [];\n        // Start with identified root nodes an sort the nodes by their\n        // hierarchy. In trees, this ensures that branches don't cross\n        // eachother.\n        for (const rootNode of rootNodes) {\n            sortedNodes.push(rootNode);\n            addToNodes(rootNode);\n        }\n        // Cyclic tree, no root node found\n        if (!sortedNodes.length) {\n            sortedNodes = nodes;\n            // Dangling, cyclic trees\n        }\n        else {\n            for (const node of nodes) {\n                if (sortedNodes.indexOf(node) === -1) {\n                    sortedNodes.push(node);\n                }\n            }\n        }\n        let node;\n        // Initial positions are laid out along a small circle, appearing\n        // as a cluster in the middle\n        for (let i = 0, iEnd = sortedNodes.length; i < iEnd; ++i) {\n            node = sortedNodes[i];\n            node.plotX = node.prevX = ReingoldFruchtermanLayout_pick(node.plotX, box.width / 2 + radius * Math.cos(i * angle));\n            node.plotY = node.prevY = ReingoldFruchtermanLayout_pick(node.plotY, box.height / 2 + radius * Math.sin(i * angle));\n            node.dispX = 0;\n            node.dispY = 0;\n        }\n    }\n    setRandomPositions() {\n        const box = this.box, nodes = this.nodes, nodesLength = nodes.length + 1, \n        /**\n         * Return a repeatable, quasi-random number based on an integer\n         * input. For the initial positions\n         * @private\n         */\n        unrandom = (n) => {\n            let rand = n * n / Math.PI;\n            rand = rand - Math.floor(rand);\n            return rand;\n        };\n        let node;\n        // Initial positions:\n        for (let i = 0, iEnd = nodes.length; i < iEnd; ++i) {\n            node = nodes[i];\n            node.plotX = node.prevX = ReingoldFruchtermanLayout_pick(node.plotX, box.width * unrandom(i));\n            node.plotY = node.prevY = ReingoldFruchtermanLayout_pick(node.plotY, box.height * unrandom(nodesLength + i));\n            node.dispX = 0;\n            node.dispY = 0;\n        }\n    }\n    force(name, ...args) {\n        this.integration[name].apply(this, args);\n    }\n    barycenterForces() {\n        this.getBarycenter();\n        this.force('barycenter');\n    }\n    getBarycenter() {\n        let systemMass = 0, cx = 0, cy = 0;\n        for (const node of this.nodes) {\n            cx += node.plotX * node.mass;\n            cy += node.plotY * node.mass;\n            systemMass += node.mass;\n        }\n        this.barycenter = {\n            x: cx,\n            y: cy,\n            xFactor: cx / systemMass,\n            yFactor: cy / systemMass\n        };\n        return this.barycenter;\n    }\n    barnesHutApproximation(node, quadNode) {\n        const distanceXY = this.getDistXY(node, quadNode), distanceR = this.vectorLength(distanceXY);\n        let goDeeper, force;\n        if (node !== quadNode && distanceR !== 0) {\n            if (quadNode.isInternal) {\n                // Internal node:\n                if (quadNode.boxSize / distanceR <\n                    this.options.theta &&\n                    distanceR !== 0) {\n                    // Treat as an external node:\n                    force = this.repulsiveForce(distanceR, this.k);\n                    this.force('repulsive', node, force * quadNode.mass, distanceXY, distanceR);\n                    goDeeper = false;\n                }\n                else {\n                    // Go deeper:\n                    goDeeper = true;\n                }\n            }\n            else {\n                // External node, direct force:\n                force = this.repulsiveForce(distanceR, this.k);\n                this.force('repulsive', node, force * quadNode.mass, distanceXY, distanceR);\n            }\n        }\n        return goDeeper;\n    }\n    repulsiveForces() {\n        if (this.approximation === 'barnes-hut') {\n            for (const node of this.nodes) {\n                this.quadTree.visitNodeRecursive(null, (quadNode) => (this.barnesHutApproximation(node, quadNode)));\n            }\n        }\n        else {\n            let force, distanceR, distanceXY;\n            for (const node of this.nodes) {\n                for (const repNode of this.nodes) {\n                    if (\n                    // Node cannot repulse itself:\n                    node !== repNode &&\n                        // Only close nodes affect each other:\n                        // layout.getDistR(node, repNode) < 2 * k &&\n                        // Not dragged:\n                        !node.fixedPosition) {\n                        distanceXY = this.getDistXY(node, repNode);\n                        distanceR = this.vectorLength(distanceXY);\n                        if (distanceR !== 0) {\n                            force = this.repulsiveForce(distanceR, this.k);\n                            this.force('repulsive', node, force * repNode.mass, distanceXY, distanceR);\n                        }\n                    }\n                }\n            }\n        }\n    }\n    attractiveForces() {\n        let distanceXY, distanceR, force;\n        for (const link of this.links) {\n            if (link.fromNode && link.toNode) {\n                distanceXY = this.getDistXY(link.fromNode, link.toNode);\n                distanceR = this.vectorLength(distanceXY);\n                if (distanceR !== 0) {\n                    force = this.attractiveForce(distanceR, this.k);\n                    this.force('attractive', link, force, distanceXY, distanceR);\n                }\n            }\n        }\n    }\n    applyLimits() {\n        const nodes = this.nodes;\n        for (const node of nodes) {\n            if (node.fixedPosition) {\n                continue;\n            }\n            this.integration.integrate(this, node);\n            this.applyLimitBox(node, this.box);\n            // Reset displacement:\n            node.dispX = 0;\n            node.dispY = 0;\n        }\n    }\n    /**\n     * External box that nodes should fall. When hitting an edge, node\n     * should stop or bounce.\n     * @private\n     */\n    applyLimitBox(node, box) {\n        const radius = node.radius;\n        /*\n        TO DO: Consider elastic collision instead of stopping.\n        o' means end position when hitting plotting area edge:\n\n        - \"inelastic\":\n        o\n            \\\n        ______\n        |  o'\n        |   \\\n        |    \\\n\n        - \"elastic\"/\"bounced\":\n        o\n            \\\n        ______\n        |  ^\n        | / \\\n        |o'  \\\n\n        Euler sample:\n        if (plotX < 0) {\n            plotX = 0;\n            dispX *= -1;\n        }\n\n        if (plotX > box.width) {\n            plotX = box.width;\n            dispX *= -1;\n        }\n\n        */\n        // Limit X-coordinates:\n        node.plotX = clamp(node.plotX, box.left + radius, box.width - radius);\n        // Limit Y-coordinates:\n        node.plotY = clamp(node.plotY, box.top + radius, box.height - radius);\n    }\n    /**\n     * From \"A comparison of simulated annealing cooling strategies\" by\n     * Nourani and Andresen work.\n     * @private\n     */\n    coolDown(temperature, temperatureStep, currentStep) {\n        // Logarithmic:\n        /*\n        return Math.sqrt(this.nodes.length) -\n            Math.log(\n                currentStep * layout.diffTemperature\n            );\n        */\n        // Exponential:\n        /*\n        let alpha = 0.1;\n        layout.temperature = Math.sqrt(layout.nodes.length) *\n            Math.pow(alpha, layout.diffTemperature);\n        */\n        // Linear:\n        return temperature - temperatureStep * currentStep;\n    }\n    isStable() {\n        return Math.abs(this.systemTemperature -\n            this.prevSystemTemperature) < 0.00001 || this.temperature <= 0;\n    }\n    getSystemTemperature() {\n        let value = 0;\n        for (const node of this.nodes) {\n            value += node.temperature;\n        }\n        return value;\n    }\n    vectorLength(vector) {\n        return Math.sqrt(vector.x * vector.x + vector.y * vector.y);\n    }\n    getDistR(nodeA, nodeB) {\n        const distance = this.getDistXY(nodeA, nodeB);\n        return this.vectorLength(distance);\n    }\n    getDistXY(nodeA, nodeB) {\n        const xDist = nodeA.plotX - nodeB.plotX, yDist = nodeA.plotY - nodeB.plotY;\n        return {\n            x: xDist,\n            y: yDist,\n            absX: Math.abs(xDist),\n            absY: Math.abs(yDist)\n        };\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Networkgraph_ReingoldFruchtermanLayout = (ReingoldFruchtermanLayout);\n\n;// ./code/es-modules/Series/SimulationSeriesUtilities.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { merge: SimulationSeriesUtilities_merge, syncTimeout } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { animObject } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/**\n * Create a setTimeout for the first drawDataLabels()\n * based on the dataLabels.animation.defer value\n * for series which have enabled simulation.\n * @private\n */\nfunction initDataLabelsDefer() {\n    const dlOptions = this.options.dataLabels;\n    // Method drawDataLabels() fires for the first time after\n    // dataLabels.animation.defer time unless\n    // the dataLabels.animation = false or dataLabels.defer = false\n    // or if the simulation is disabled\n    if (!dlOptions?.defer ||\n        !this.options.layoutAlgorithm?.enableSimulation) {\n        this.deferDataLabels = false;\n    }\n    else {\n        syncTimeout(() => {\n            this.deferDataLabels = false;\n        }, dlOptions ? animObject(dlOptions.animation).defer : 0);\n    }\n}\n/**\n * Initialize the SVG group for the DataLabels with correct opacities\n * and correct styles so that the animation for the series that have\n * simulation enabled works fine.\n * @private\n */\nfunction initDataLabels() {\n    const series = this, dlOptions = series.options.dataLabels;\n    if (!series.dataLabelsGroup) {\n        const dataLabelsGroup = this.initDataLabelsGroup();\n        // Apply the dataLabels.style not only to the\n        // individual dataLabels but also to the entire group\n        if (!series.chart.styledMode && dlOptions?.style) {\n            dataLabelsGroup.css(dlOptions.style);\n        }\n        // Initialize the opacity of the group to 0 (start of animation)\n        dataLabelsGroup.attr({ opacity: 0 });\n        if (series.visible) { // #2597, #3023, #3024\n            dataLabelsGroup.show();\n        }\n        return dataLabelsGroup;\n    }\n    // Place it on first and subsequent (redraw) calls\n    series.dataLabelsGroup.attr(SimulationSeriesUtilities_merge({ opacity: 1 }, this.getPlotBox('data-labels')));\n    return series.dataLabelsGroup;\n}\nconst DataLabelsDeferUtils = {\n    initDataLabels,\n    initDataLabelsDefer\n};\n/* harmony default export */ const SimulationSeriesUtilities = (DataLabelsDeferUtils);\n\n;// ./code/es-modules/Extensions/TextPath.js\n/* *\n *\n *  Highcharts module with textPath functionality.\n *\n *  (c) 2009-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { deg2rad } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nconst { addEvent: TextPath_addEvent, merge: TextPath_merge, uniqueKey, defined: TextPath_defined, extend: TextPath_extend } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/**\n * Set a text path for a `text` or `label` element, allowing the text to\n * flow along a path.\n *\n * In order to unset the path for an existing element, call `setTextPath`\n * with `{ enabled: false }` as the second argument.\n *\n * Text path support is not bundled into `highcharts.js`, and requires the\n * `modules/textpath.js` file. However, it is included in the script files of\n * those series types that use it by default\n *\n * @sample highcharts/members/renderer-textpath/ Text path demonstrated\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {Highcharts.SVGElement|undefined} path\n *        Path to follow. If undefined, it allows changing options for the\n *        existing path.\n *\n * @param {Highcharts.DataLabelsTextPathOptionsObject} textPathOptions\n *        Options.\n *\n * @return {Highcharts.SVGElement} Returns the SVGElement for chaining.\n */\nfunction setTextPath(path, textPathOptions) {\n    // Defaults\n    textPathOptions = TextPath_merge(true, {\n        enabled: true,\n        attributes: {\n            dy: -5,\n            startOffset: '50%',\n            textAnchor: 'middle'\n        }\n    }, textPathOptions);\n    const url = this.renderer.url, textWrapper = this.text || this, textPath = textWrapper.textPath, { attributes, enabled } = textPathOptions;\n    path = path || (textPath && textPath.path);\n    // Remove previously added event\n    if (textPath) {\n        textPath.undo();\n    }\n    if (path && enabled) {\n        const undo = TextPath_addEvent(textWrapper, 'afterModifyTree', (e) => {\n            if (path && enabled) {\n                // Set ID for the path\n                let textPathId = path.attr('id');\n                if (!textPathId) {\n                    path.attr('id', textPathId = uniqueKey());\n                }\n                // Set attributes for the <text>\n                const textAttribs = {\n                    // `dx`/`dy` options must by set on <text> (parent), the\n                    // rest should be set on <textPath>\n                    x: 0,\n                    y: 0\n                };\n                if (TextPath_defined(attributes.dx)) {\n                    textAttribs.dx = attributes.dx;\n                    delete attributes.dx;\n                }\n                if (TextPath_defined(attributes.dy)) {\n                    textAttribs.dy = attributes.dy;\n                    delete attributes.dy;\n                }\n                textWrapper.attr(textAttribs);\n                // Handle label properties\n                this.attr({ transform: '' });\n                if (this.box) {\n                    this.box = this.box.destroy();\n                }\n                // Wrap the nodes in a textPath\n                const children = e.nodes.slice(0);\n                e.nodes.length = 0;\n                e.nodes[0] = {\n                    tagName: 'textPath',\n                    attributes: TextPath_extend(attributes, {\n                        'text-anchor': attributes.textAnchor,\n                        href: `${url}#${textPathId}`\n                    }),\n                    children\n                };\n            }\n        });\n        // Set the reference\n        textWrapper.textPath = { path, undo };\n    }\n    else {\n        textWrapper.attr({ dx: 0, dy: 0 });\n        delete textWrapper.textPath;\n    }\n    if (this.added) {\n        // Rebuild text after added\n        textWrapper.textCache = '';\n        this.renderer.buildText(textWrapper);\n    }\n    return this;\n}\n/**\n * Attach a polygon to a bounding box if the element contains a textPath.\n *\n * @function Highcharts.SVGElement#setPolygon\n *\n * @param {any} event\n *        An event containing a bounding box object\n *\n * @return {Highcharts.BBoxObject} Returns the bounding box object.\n */\nfunction setPolygon(event) {\n    const bBox = event.bBox, tp = this.element?.querySelector('textPath');\n    if (tp) {\n        const polygon = [], { b, h } = this.renderer.fontMetrics(this.element), descender = h - b, lineCleanerRegex = new RegExp('(<tspan>|' +\n            '<tspan(?!\\\\sclass=\"highcharts-br\")[^>]*>|' +\n            '<\\\\/tspan>)', 'g'), lines = tp\n            .innerHTML\n            .replace(lineCleanerRegex, '')\n            .split(/<tspan class=\"highcharts-br\"[^>]*>/), numOfLines = lines.length;\n        // Calculate top and bottom coordinates for\n        // either the start or the end of a single\n        // character, and append it to the polygon.\n        const appendTopAndBottom = (charIndex, positionOfChar) => {\n            const { x, y } = positionOfChar, rotation = (tp.getRotationOfChar(charIndex) - 90) * deg2rad, cosRot = Math.cos(rotation), sinRot = Math.sin(rotation);\n            return [\n                [\n                    x - descender * cosRot,\n                    y - descender * sinRot\n                ],\n                [\n                    x + b * cosRot,\n                    y + b * sinRot\n                ]\n            ];\n        };\n        for (let i = 0, lineIndex = 0; lineIndex < numOfLines; lineIndex++) {\n            const line = lines[lineIndex], lineLen = line.length;\n            for (let lineCharIndex = 0; lineCharIndex < lineLen; lineCharIndex += 5) {\n                try {\n                    const srcCharIndex = (i +\n                        lineCharIndex +\n                        lineIndex), [lower, upper] = appendTopAndBottom(srcCharIndex, tp.getStartPositionOfChar(srcCharIndex));\n                    if (lineCharIndex === 0) {\n                        polygon.push(upper);\n                        polygon.push(lower);\n                    }\n                    else {\n                        if (lineIndex === 0) {\n                            polygon.unshift(upper);\n                        }\n                        if (lineIndex === numOfLines - 1) {\n                            polygon.push(lower);\n                        }\n                    }\n                }\n                catch (e) {\n                    // Safari fails on getStartPositionOfChar even if the\n                    // character is within the `textContent.length`\n                    break;\n                }\n            }\n            i += lineLen - 1;\n            try {\n                const srcCharIndex = i + lineIndex, charPos = tp.getEndPositionOfChar(srcCharIndex), [lower, upper] = appendTopAndBottom(srcCharIndex, charPos);\n                polygon.unshift(upper);\n                polygon.unshift(lower);\n            }\n            catch (e) {\n                // Safari fails on getStartPositionOfChar even if the character\n                // is within the `textContent.length`\n                break;\n            }\n        }\n        // Close it\n        if (polygon.length) {\n            polygon.push(polygon[0].slice());\n        }\n        bBox.polygon = polygon;\n    }\n    return bBox;\n}\n/**\n * Draw text along a textPath for a dataLabel.\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {any} event\n *        An event containing label options\n *\n * @return {void}\n */\nfunction drawTextPath(event) {\n    const labelOptions = event.labelOptions, point = event.point, textPathOptions = (labelOptions[point.formatPrefix + 'TextPath'] ||\n        labelOptions.textPath);\n    if (textPathOptions && !labelOptions.useHTML) {\n        this.setTextPath(point.getDataLabelPath?.(this) || point.graphic, textPathOptions);\n        if (point.dataLabelPath &&\n            !textPathOptions.enabled) {\n            // Clean the DOM\n            point.dataLabelPath = (point.dataLabelPath.destroy());\n        }\n    }\n}\nfunction TextPath_compose(SVGElementClass) {\n    TextPath_addEvent(SVGElementClass, 'afterGetBBox', setPolygon);\n    TextPath_addEvent(SVGElementClass, 'beforeAddingDataLabel', drawTextPath);\n    const svgElementProto = SVGElementClass.prototype;\n    if (!svgElementProto.setTextPath) {\n        svgElementProto.setTextPath = setTextPath;\n    }\n}\nconst TextPath = {\n    compose: TextPath_compose\n};\n/* harmony default export */ const Extensions_TextPath = (TextPath);\n\n;// ./code/es-modules/Series/Networkgraph/NetworkgraphSeries.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\nconst { noop } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\n\n\n\nconst { series: Series, seriesTypes: { column: { prototype: columnProto }, line: { prototype: lineProto } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { initDataLabels: NetworkgraphSeries_initDataLabels, initDataLabelsDefer: NetworkgraphSeries_initDataLabelsDefer } = SimulationSeriesUtilities;\n\nconst { addEvent: NetworkgraphSeries_addEvent, defined: NetworkgraphSeries_defined, extend: NetworkgraphSeries_extend, merge: NetworkgraphSeries_merge, pick: NetworkgraphSeries_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nExtensions_TextPath.compose((highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default()));\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.networkgraph\n *\n * @extends Highcharts.Series\n */\nclass NetworkgraphSeries extends Series {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        this.deferDataLabels = true;\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static compose(ChartClass) {\n        Series_DragNodesComposition.compose(ChartClass);\n        Networkgraph_ReingoldFruchtermanLayout.compose(ChartClass);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Defer the layout.\n     * Each series first registers all nodes and links, then layout\n     * calculates all nodes positions and calls `series.render()` in every\n     * simulation step.\n     *\n     * Note:\n     * Animation is done through `requestAnimationFrame` directly, without\n     * `Highcharts.animate()` use.\n     * @private\n     */\n    deferLayout() {\n        const layoutOptions = this.options.layoutAlgorithm, chartOptions = this.chart.options.chart;\n        let layout, graphLayoutsStorage = this.chart.graphLayoutsStorage, graphLayoutsLookup = this.chart.graphLayoutsLookup;\n        if (!this.visible) {\n            return;\n        }\n        if (!graphLayoutsStorage) {\n            this.chart.graphLayoutsStorage = graphLayoutsStorage = {};\n            this.chart.graphLayoutsLookup = graphLayoutsLookup = [];\n        }\n        layout = graphLayoutsStorage[layoutOptions.type];\n        if (!layout) {\n            layoutOptions.enableSimulation =\n                !NetworkgraphSeries_defined(chartOptions.forExport) ?\n                    layoutOptions.enableSimulation :\n                    !chartOptions.forExport;\n            graphLayoutsStorage[layoutOptions.type] = layout =\n                new Series_GraphLayoutComposition.layouts[layoutOptions.type]();\n            layout.init(layoutOptions);\n            graphLayoutsLookup.splice(layout.index, 0, layout);\n        }\n        this.layout = layout;\n        layout.setArea(0, 0, this.chart.plotWidth, this.chart.plotHeight);\n        layout.addElementsToCollection([this], layout.series);\n        layout.addElementsToCollection(this.nodes, layout.nodes);\n        layout.addElementsToCollection(this.points, layout.links);\n    }\n    /**\n     * @private\n     */\n    destroy() {\n        if (this.layout) {\n            this.layout.removeElementFromCollection(this, this.layout.series);\n        }\n        Series_NodesComposition.destroy.call(this);\n    }\n    /**\n     * Networkgraph has two separate collections of nodes and lines, render\n     * dataLabels for both sets:\n     * @private\n     */\n    drawDataLabels() {\n        // We defer drawing the dataLabels\n        // until dataLabels.animation.defer time passes\n        if (this.deferDataLabels) {\n            return;\n        }\n        const dlOptions = this.options.dataLabels;\n        let textPath;\n        if (dlOptions?.textPath) {\n            textPath = dlOptions.textPath;\n        }\n        // Render node labels:\n        Series.prototype.drawDataLabels.call(this, this.nodes);\n        // Render link labels:\n        if (dlOptions?.linkTextPath) {\n            // If linkTextPath is set, render link labels with linkTextPath\n            dlOptions.textPath = dlOptions.linkTextPath;\n        }\n        Series.prototype.drawDataLabels.call(this, this.data);\n        // Go back to textPath for nodes\n        if (dlOptions?.textPath) {\n            dlOptions.textPath = textPath;\n        }\n    }\n    /**\n     * Extend generatePoints by adding the nodes, which are Point objects\n     * but pushed to the this.nodes array.\n     * @private\n     */\n    generatePoints() {\n        let node, i;\n        Series_NodesComposition.generatePoints.apply(this, arguments);\n        // In networkgraph, it's fine to define standalone nodes, create\n        // them:\n        if (this.options.nodes) {\n            this.options.nodes.forEach(function (nodeOptions) {\n                if (!this.nodeLookup[nodeOptions.id]) {\n                    this.nodeLookup[nodeOptions.id] =\n                        this.createNode(nodeOptions.id);\n                }\n            }, this);\n        }\n        for (i = this.nodes.length - 1; i >= 0; i--) {\n            node = this.nodes[i];\n            node.degree = node.getDegree();\n            node.radius = NetworkgraphSeries_pick(node.marker && node.marker.radius, this.options.marker && this.options.marker.radius, 0);\n            node.key = node.name;\n            // If node exists, but it's not available in nodeLookup,\n            // then it's leftover from previous runs (e.g. setData)\n            if (!this.nodeLookup[node.id]) {\n                node.remove();\n            }\n        }\n        this.data.forEach(function (link) {\n            link.formatPrefix = 'link';\n        });\n        this.indexateNodes();\n    }\n    /**\n     * In networkgraph, series.points refers to links,\n     * but series.nodes refers to actual points.\n     * @private\n     */\n    getPointsCollection() {\n        return this.nodes || [];\n    }\n    /**\n     * Set index for each node. Required for proper `node.update()`.\n     * Note that links are indexated out of the box in `generatePoints()`.\n     *\n     * @private\n     */\n    indexateNodes() {\n        this.nodes.forEach(function (node, index) {\n            node.index = index;\n        });\n    }\n    /**\n     * Extend init with base event, which should stop simulation during\n     * update. After data is updated, `chart.render` resumes the simulation.\n     * @private\n     */\n    init(chart, options) {\n        super.init(chart, options);\n        NetworkgraphSeries_initDataLabelsDefer.call(this);\n        NetworkgraphSeries_addEvent(this, 'updatedData', () => {\n            if (this.layout) {\n                this.layout.stop();\n            }\n        });\n        NetworkgraphSeries_addEvent(this, 'afterUpdate', () => {\n            this.nodes.forEach((node) => {\n                if (node && node.series) {\n                    node.resolveColor();\n                }\n            });\n        });\n        // If the dataLabels.animation.defer time is longer than\n        // the time it takes for the layout to become stable then\n        // drawDataLabels would never be called (that's why we force it here)\n        NetworkgraphSeries_addEvent(this, 'afterSimulation', function () {\n            this.deferDataLabels = false;\n            this.drawDataLabels();\n        });\n        return this;\n    }\n    /**\n     * Extend the default marker attribs by using a non-rounded X position,\n     * otherwise the nodes will jump from pixel to pixel which looks a bit\n     * jaggy when approaching equilibrium.\n     * @private\n     */\n    markerAttribs(point, state) {\n        const attribs = Series.prototype.markerAttribs.call(this, point, state);\n        // Series.render() is called before initial positions are set:\n        if (!NetworkgraphSeries_defined(point.plotY)) {\n            attribs.y = 0;\n        }\n        attribs.x = (point.plotX || 0) - (attribs.width || 0) / 2;\n        return attribs;\n    }\n    /**\n     * Return the presentational attributes.\n     * @private\n     */\n    pointAttribs(point, state) {\n        // By default, only `selected` state is passed on\n        const pointState = state || point && point.state || 'normal', stateOptions = this.options.states[pointState];\n        let attribs = Series.prototype.pointAttribs.call(this, point, pointState);\n        if (point && !point.isNode) {\n            attribs = point.getLinkAttributes();\n            // For link, get prefixed names:\n            if (stateOptions) {\n                attribs = {\n                    // TO DO: API?\n                    stroke: stateOptions.linkColor || attribs.stroke,\n                    dashstyle: (stateOptions.linkDashStyle || attribs.dashstyle),\n                    opacity: NetworkgraphSeries_pick(stateOptions.linkOpacity, attribs.opacity),\n                    'stroke-width': stateOptions.linkColor ||\n                        attribs['stroke-width']\n                };\n            }\n        }\n        return attribs;\n    }\n    /**\n     * Extend the render function to also render this.nodes together with\n     * the points.\n     * @private\n     */\n    render() {\n        const series = this, points = series.points, hoverPoint = series.chart.hoverPoint, dataLabels = [];\n        // Render markers:\n        series.points = series.nodes;\n        lineProto.render.call(this);\n        series.points = points;\n        points.forEach(function (point) {\n            if (point.fromNode && point.toNode) {\n                point.renderLink();\n                point.redrawLink();\n            }\n        });\n        if (hoverPoint && hoverPoint.series === series) {\n            series.redrawHalo(hoverPoint);\n        }\n        if (series.chart.hasRendered &&\n            !series.options.dataLabels.allowOverlap) {\n            series.nodes.concat(series.points).forEach(function (node) {\n                if (node.dataLabel) {\n                    dataLabels.push(node.dataLabel);\n                }\n            });\n            series.chart.hideOverlappingLabels(dataLabels);\n        }\n    }\n    /**\n     * When state should be passed down to all points, concat nodes and\n     * links and apply this state to all of them.\n     * @private\n     */\n    setState(state, inherit) {\n        if (inherit) {\n            this.points = this.nodes.concat(this.data);\n            Series.prototype.setState.apply(this, arguments);\n            this.points = this.data;\n        }\n        else {\n            Series.prototype.setState.apply(this, arguments);\n        }\n        // If simulation is done, re-render points with new states:\n        if (!this.layout.simulation && !state) {\n            this.render();\n        }\n    }\n    /**\n     * Run pre-translation and register nodes&links to the deffered layout.\n     * @private\n     */\n    translate() {\n        this.generatePoints();\n        this.deferLayout();\n        this.nodes.forEach(function (node) {\n            // Draw the links from this node\n            node.isInside = true;\n            node.linksFrom.forEach(function (point) {\n                point.shapeType = 'path';\n                // Pass test in drawPoints\n                point.y = 1;\n            });\n        });\n    }\n}\nNetworkgraphSeries.defaultOptions = NetworkgraphSeries_merge(Series.defaultOptions, Networkgraph_NetworkgraphSeriesDefaults);\nNetworkgraphSeries_extend(NetworkgraphSeries.prototype, {\n    pointClass: Networkgraph_NetworkgraphPoint,\n    animate: void 0, // Animation is run in `series.simulation`\n    directTouch: true,\n    drawGraph: void 0,\n    forces: ['barycenter', 'repulsive', 'attractive'],\n    hasDraggableNodes: true,\n    isCartesian: false,\n    noSharedTooltip: true,\n    pointArrayMap: ['from', 'to'],\n    requireSorting: false,\n    trackerGroups: ['group', 'markerGroup', 'dataLabelsGroup'],\n    initDataLabels: NetworkgraphSeries_initDataLabels,\n    buildKDTree: noop,\n    createNode: Series_NodesComposition.createNode,\n    drawTracker: columnProto.drawTracker,\n    onMouseDown: Series_DragNodesComposition.onMouseDown,\n    onMouseMove: Series_DragNodesComposition.onMouseMove,\n    onMouseUp: Series_DragNodesComposition.onMouseUp,\n    redrawHalo: Series_DragNodesComposition.redrawHalo\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('networkgraph', NetworkgraphSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Networkgraph_NetworkgraphSeries = (NetworkgraphSeries);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Callback that fires after the end of Networkgraph series simulation\n * when the layout is stable.\n *\n * @callback Highcharts.NetworkgraphAfterSimulationCallbackFunction\n *\n * @param {Highcharts.Series} this\n *        The series where the event occurred.\n *\n * @param {global.Event} event\n *        The event that occurred.\n */\n''; // Detach doclets above\n\n;// ./code/es-modules/masters/modules/networkgraph.js\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nNetworkgraph_NetworkgraphSeries.compose(G.Chart);\n/* harmony default export */ const networkgraph_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__563__", "__WEBPACK_EXTERNAL_MODULE__28__", "__WEBPACK_EXTERNAL_MODULE__512__", "NodesComposition", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "networkgraph_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_", "highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default", "composed", "addEvent", "pushUnique", "onChartLoad", "mousedownUnbinder", "mousemoveUnbinder", "mouseupUnbinder", "point", "chart", "container", "event", "hoverPoint", "series", "hasDraggableNodes", "options", "draggable", "onMouseDown", "e", "onMouseMove", "ownerDocument", "onMouseUp", "Series_DragNodesComposition", "compose", "ChartClass", "panKey", "normalizedEvent", "pointer", "normalize", "fixedPosition", "chartX", "chartY", "plotX", "plotY", "inDragMode", "newPlotX", "newPlotY", "diffX", "diffY", "graphLayoutsLookup", "Math", "abs", "isInsidePlot", "hasDragged", "redrawHalo", "for<PERSON>ach", "layout", "restartSimulation", "enableSimulation", "start", "redraw", "fixedDraggable", "halo", "attr", "haloPath", "states", "hover", "size", "setAnimation", "GraphLayoutComposition_composed", "GraphLayoutComposition_addEvent", "GraphLayoutComposition_pushUnique", "onChartAfterPrint", "updateSimulation", "onChartBeforePrint", "onChartPredraw", "stop", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "systemsStable", "afterRender", "layoutStep", "maxIterations", "isFinite", "temperature", "isStable", "beforeStep", "step", "render", "Series_GraphLayoutComposition", "integrations", "layouts", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "seriesProto", "pointClass", "pointProto", "defined", "extend", "find", "merge", "pick", "destroy", "data", "concat", "points", "nodes", "apply", "arguments", "setData", "node", "length", "setNodeState", "state", "args", "others", "isNode", "linksTo", "linksFrom", "fromNode", "toNode", "linkOrNode", "setState", "graphic", "updateNode", "animation", "runEvent", "dataLength", "linkConfig", "index", "update", "nodeIndex", "reduce", "prevIndex", "id", "nodeConfig", "push", "PointClass", "SeriesClass", "createNode", "findById", "newNode", "className", "y", "getSum", "sumTo", "sumFrom", "link", "weight", "max", "offset", "coll", "i", "<PERSON><PERSON><PERSON><PERSON>", "outgoing", "formatPrefix", "name", "mass", "marker", "radius", "generatePoints", "nodeLookup", "colorCounter", "level", "from", "styledMode", "colorIndex", "color", "to", "Series_NodesComposition", "NetworkgraphPoint_seriesProto", "Point", "NetworkgraphPoint_addEvent", "css", "NetworkgraphPoint_defined", "NetworkgraphPoint_extend", "NetworkgraphPoint_pick", "NetworkgraphPoint", "destroyElements", "removeElementFromCollection", "getDegree", "deg", "getLinkAttributes", "linkOptions", "pointOptions", "width", "stroke", "dashstyle", "dashStyle", "opacity", "getLinkPath", "left", "right", "getMass", "m1", "m2", "sum", "constructor", "x", "cursor", "<PERSON><PERSON><PERSON><PERSON>", "redrawLink", "attribs", "path", "shapeArgs", "pointAttribs", "dataLabels", "label", "animate", "end", "remove", "nodesOptions", "linkFromTo", "indexOf", "splice", "removePoint", "slice", "isDirty", "isDirtyData", "renderLink", "renderer", "addClass", "getClassName", "add", "group", "Networkgraph_EulerIntegration", "attractive", "force", "distanceXY", "distanceR", "massFactor", "translatedX", "translatedY", "dispX", "degree", "dispY", "attractiveForceFunction", "k", "barycenter", "gravitationalConstant", "xFactor", "yFactor", "phi", "getK", "pow", "box", "height", "integrate", "friction", "vectorLength", "min", "repulsive", "repulsiveForceFunction", "QuadTreeNode", "body", "isEmpty", "isInternal", "boxSize", "divideBox", "halfWidth", "halfHeight", "top", "getBoxPosition", "insert", "depth", "newQuadTreeNode", "NaN", "updateMassAndCenter", "pointMass", "Networkgraph_QuadTree", "max<PERSON><PERSON><PERSON>", "isRoot", "calculateMassAndCenter", "visitNodeRecursive", "insertNodes", "beforeCallback", "afterCallback", "<PERSON><PERSON><PERSON><PERSON>", "qtNode", "Networkgraph_VerletIntegration", "diffTemperature", "maxSpeed", "prevX", "prevY", "frictionX", "frictionY", "signX", "signY", "factor", "win", "clamp", "ReingoldFruchtermanLayout_defined", "isFunction", "fireEvent", "ReingoldFruchtermanLayout_pick", "ReingoldFruchtermanLayout", "currentStep", "initialRendering", "links", "simulation", "euler", "verlet", "init", "setInitialRendering", "integration", "attractive<PERSON><PERSON><PERSON>", "repulsiveForce", "approximation", "enable", "forces", "initPositions", "s", "finishedAnimating", "setK", "resetSimulation", "allSeries", "forceName", "createQuadTree", "quadTree", "anyLayout", "applyLimits", "coolDown", "startTemperature", "prevSystemTemperature", "systemTemperature", "getSystemTemperature", "cancelAnimationFrame", "requestAnimationFrame", "<PERSON><PERSON><PERSON>", "w", "h", "linkLength", "addElementsToCollection", "elements", "collection", "element", "clear", "forcedStop", "setMaxIterations", "setTemperature", "setDiffTemperature", "sqrt", "initialPositions", "setCircularPositions", "setRandomPositions", "angle", "PI", "rootNodes", "filter", "visitedNodes", "initialPositionRadius", "addToNodes", "sortedNodes", "rootNode", "iEnd", "cos", "sin", "<PERSON><PERSON><PERSON><PERSON>", "unrandom", "rand", "floor", "barycenterForces", "getBarycenter", "systemMass", "cx", "cy", "barnesHutApproximation", "quadNode", "goDeeper", "getDistXY", "theta", "repulsiveForces", "repNode", "attractiveForces", "applyLimitBox", "temperatureStep", "value", "vector", "getDistR", "nodeA", "nodeB", "distance", "xDist", "yDist", "absX", "absY", "SimulationSeriesUtilities_merge", "syncTimeout", "animObject", "deg2rad", "TextPath_addEvent", "TextPath_merge", "<PERSON><PERSON><PERSON>", "TextPath_defined", "TextPath_extend", "setTextPath", "textPathOptions", "enabled", "attributes", "dy", "startOffset", "textAnchor", "url", "textWrapper", "text", "textPath", "undo", "textPathId", "textAttribs", "dx", "transform", "children", "tagName", "href", "added", "textCache", "buildText", "setPolygon", "bBox", "tp", "querySelector", "polygon", "b", "fontMetrics", "descender", "lineCleanerRegex", "RegExp", "lines", "innerHTML", "replace", "split", "numOfLines", "appendTopAndBottom", "charIndex", "positionOfChar", "rotation", "getRotationOfChar", "cosRot", "sinRot", "lineIndex", "lineLen", "line", "lineCharIndex", "srcCharIndex", "lower", "upper", "getStartPositionOfChar", "unshift", "char<PERSON><PERSON>", "getEndPositionOfChar", "drawTextPath", "labelOptions", "useHTML", "getDataLabelPath", "dataLabelPath", "noop", "Series", "seriesTypes", "column", "columnProto", "lineProto", "initDataLabels", "NetworkgraphSeries_initDataLabels", "initDataLabelsDefer", "NetworkgraphSeries_initDataLabelsDefer", "dlOptions", "dataLabelsGroup", "initDataLabelsGroup", "style", "visible", "show", "getPlotBox", "defer", "layoutAlgorithm", "deferDataLabels", "NetworkgraphSeries_addEvent", "NetworkgraphSeries_defined", "NetworkgraphSeries_extend", "NetworkgraphSeries_merge", "NetworkgraphSeries_pick", "Extensions_TextPath", "SVGElementClass", "svgElementProto", "NetworkgraphSeries", "Networkgraph_ReingoldFruchtermanLayout", "deferLayout", "layoutOptions", "chartOptions", "graphLayoutsStorage", "type", "forExport", "plot<PERSON>id<PERSON>", "plotHeight", "drawDataLabels", "linkTextPath", "nodeOptions", "indexateNodes", "getPointsCollection", "resolveColor", "markerAttribs", "pointState", "stateOptions", "linkColor", "linkDashStyle", "linkOpacity", "hasRendered", "allowOverlap", "dataLabel", "hideOverlappingLabels", "inherit", "translate", "isInside", "shapeType", "defaultOptions", "stickyTracking", "inactiveOtherPoints", "inactive", "duration", "formatter", "String", "linkFormatter", "transition", "showInLegend", "directTouch", "drawGraph", "isCartesian", "noSharedTooltip", "pointArrayMap", "requireSorting", "trackerGroups", "buildKDTree", "drawTracker", "registerSeriesType", "G", "Networkgraph_NetworkgraphSeries", "Chart"], "mappings": "CAWA,AAXA;;;;;;;;;;CAUC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,UAAa,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC/G,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,kCAAmC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,UAAa,CAACA,EAAK,cAAiB,CAAE,GAC9I,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,kCAAkC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,UAAa,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAElJA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACzH,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAiCC,IACtG,AAAC,CAAA,KACP,aACA,IAiaNC,EAjaUC,EAAuB,CAE/B,GACC,AAACT,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIK,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAad,OAAO,CAG5B,IAAIC,EAASU,CAAwB,CAACE,EAAS,CAAG,CAGjDb,QAAS,CAAC,CACX,EAMA,OAHAU,CAAmB,CAACG,EAAS,CAACZ,EAAQA,EAAOD,OAAO,CAAEY,GAG/CX,EAAOD,OAAO,AACtB,CAMCY,EAAoBI,CAAC,CAAG,AAACf,IACxB,IAAIgB,EAAShB,GAAUA,EAAOiB,UAAU,CACvC,IAAOjB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAW,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACnB,EAASqB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACvB,EAASsB,IAC5EE,OAAOC,cAAc,CAACzB,EAASsB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,EAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmHzB,EAAoB,IACvI0B,EAAuI1B,EAAoBI,CAAC,CAACqB,GAejK,GAAM,CAAEE,SAAAA,CAAQ,CAAE,CAAIH,IAEhB,CAAEI,SAAAA,CAAQ,CAAEC,WAAAA,CAAU,CAAE,CAAIL,IAkBlC,SAASM,IACL,IACIC,EAAmBC,EAAmBC,EAAiBC,EADrDC,EAAQ,IAAI,AAEdA,CAAAA,EAAMC,SAAS,EACfL,CAAAA,EAAoBH,EAASO,EAAMC,SAAS,CAAE,YAAa,AAACC,IACpDL,GACAA,IAEAC,GACAA,IAEJC,CAAAA,EAAQC,EAAMG,UAAU,AAAD,GAEnBJ,EAAMK,MAAM,EACZL,EAAMK,MAAM,CAACC,iBAAiB,EAC9BN,EAAMK,MAAM,CAACE,OAAO,CAACC,SAAS,GAC9BR,EAAMK,MAAM,CAACI,WAAW,CAACT,EAAOG,GAChCL,EAAoBJ,EAASO,EAAMC,SAAS,CAAE,YAAa,AAACQ,GAAOV,GAC/DA,EAAMK,MAAM,EACZL,EAAMK,MAAM,CAACM,WAAW,CAACX,EAAOU,IACpCX,EAAkBL,EAASO,EAAMC,SAAS,CAACU,aAAa,CAAE,UAAW,AAACF,IAClEZ,IACAC,IACOC,GACHA,EAAMK,MAAM,EACZL,EAAMK,MAAM,CAACQ,SAAS,CAACb,EAAOU,KAG9C,EAAC,EAELhB,EAASO,EAAO,UAAW,WACvBJ,GACJ,EACJ,CAuG6B,IAAMiB,EAPN,CACzBC,QA3IJ,SAAiBC,CAAU,EACnBrB,EAAWF,EAAU,cACrBC,EAASsB,EAAY,OAAQpB,EAErC,EAwIIa,YAxFJ,SAAqBT,CAAK,CAAEG,CAAK,EAC7B,GAAM,CAAEc,OAAAA,CAAM,CAAE,CAAG,IAAI,CAAChB,KAAK,CAACM,OAAO,CAACN,KAAK,CAC3C,GAD6DgB,GAAUd,CAAK,CAAC,CAAC,EAAEc,EAAO,GAAG,CAAC,CAAC,CAExF,OAEJ,IAAMC,EAAkB,IAAI,CAACjB,KAAK,CAACkB,OAAO,EAAEC,UAAUjB,IAAUA,CAChEH,CAAAA,EAAMqB,aAAa,CAAG,CAClBC,OAAQJ,EAAgBI,MAAM,CAC9BC,OAAQL,EAAgBK,MAAM,CAC9BC,MAAOxB,EAAMwB,KAAK,CAClBC,MAAOzB,EAAMyB,KAAK,AACtB,EACAzB,EAAM0B,UAAU,CAAG,CAAA,CACvB,EA4EIf,YAjEJ,SAAqBX,CAAK,CAAEG,CAAK,EAC7B,GAAIH,EAAMqB,aAAa,EAAIrB,EAAM0B,UAAU,CAAE,CACzC,IACIC,EAAUC,EADO3B,EAAQI,AAAd,IAAI,CAAiBJ,KAAK,CAAEiB,EAAkBjB,EAAMkB,OAAO,EAAEC,UAAUjB,IAAUA,EAAO0B,EAAQ7B,EAAMqB,aAAa,CAACC,MAAM,CAAGJ,EAAgBI,MAAM,CAAEQ,EAAQ9B,EAAMqB,aAAa,CAACE,MAAM,CAAGL,EAAgBK,MAAM,CAAEQ,EAAqB9B,EAAM8B,kBAAkB,CAG1QC,CAAAA,KAAKC,GAAG,CAACJ,GAAS,GAAKG,KAAKC,GAAG,CAACH,GAAS,CAAA,IACzCH,EAAW3B,EAAMqB,aAAa,CAACG,KAAK,CAAGK,EACvCD,EAAW5B,EAAMqB,aAAa,CAACI,KAAK,CAAGK,EACnC7B,EAAMiC,YAAY,CAACP,EAAUC,KAC7B5B,EAAMwB,KAAK,CAAGG,EACd3B,EAAMyB,KAAK,CAAGG,EACd5B,EAAMmC,UAAU,CAAG,CAAA,EACnB,IAAI,CAACC,UAAU,CAACpC,GAChB+B,EAAmBM,OAAO,CAAC,AAACC,IACxBA,EAAOC,iBAAiB,EAC5B,IAGZ,CACJ,EA+CI1B,UAvCJ,SAAmBb,CAAK,EAChBA,EAAMqB,aAAa,GACfrB,EAAMmC,UAAU,GACZ,IAAI,CAACG,MAAM,CAACE,gBAAgB,CAC5B,IAAI,CAACF,MAAM,CAACG,KAAK,GAGjB,IAAI,CAACxC,KAAK,CAACyC,MAAM,IAGzB1C,EAAM0B,UAAU,CAAG1B,EAAMmC,UAAU,CAAG,CAAA,EAClC,AAAC,IAAI,CAAC5B,OAAO,CAACoC,cAAc,EAC5B,OAAO3C,EAAMqB,aAAa,CAGtC,EAyBIe,WAjBJ,SAAoBpC,CAAK,EACjBA,GAAS,IAAI,CAAC4C,IAAI,EAClB,IAAI,CAACA,IAAI,CAACC,IAAI,CAAC,CACXxE,EAAG2B,EAAM8C,QAAQ,CAAC,IAAI,CAACvC,OAAO,CAACwC,MAAM,CAACC,KAAK,CAACJ,IAAI,CAACK,IAAI,CACzD,EAER,CAYA,EAiBM,CAAEC,aAAAA,CAAY,CAAE,CAAI5D,IAEpB,CAAEG,SAAU0D,CAA+B,CAAE,CAAI7D,IAEjD,CAAEI,SAAU0D,CAA+B,CAAEzD,WAAY0D,CAAiC,CAAE,CAAI/D,IA4BtG,SAASgE,IACD,IAAI,CAACvB,kBAAkB,GACvB,IAAI,CAACA,kBAAkB,CAACM,OAAO,CAAC,AAACC,IAE7BA,EAAOiB,gBAAgB,EAC3B,GACA,IAAI,CAACb,MAAM,GAEnB,CAKA,SAASc,IACD,IAAI,CAACzB,kBAAkB,GACvB,IAAI,CAACA,kBAAkB,CAACM,OAAO,CAAC,AAACC,IAC7BA,EAAOiB,gBAAgB,CAAC,CAAA,EAC5B,GACA,IAAI,CAACb,MAAM,GAEnB,CAKA,SAASe,IACD,IAAI,CAAC1B,kBAAkB,EACvB,IAAI,CAACA,kBAAkB,CAACM,OAAO,CAAC,AAACC,IAC7BA,EAAOoB,IAAI,EACf,EAER,CAIA,SAASC,IACL,IAAIC,EAAeC,EAAc,CAAA,EAC3BC,EAAa,AAACxB,IACZA,EAAOyB,aAAa,IACpBC,SAAS1B,EAAO2B,WAAW,GAC3B,CAAC3B,EAAO4B,QAAQ,IAChB,CAAC5B,EAAOE,gBAAgB,GAMpBF,EAAO6B,UAAU,EACjB7B,EAAO6B,UAAU,GAErB7B,EAAO8B,IAAI,GACXR,EAAgB,CAAA,EAChBC,EAAc,CAAA,EAEtB,EAEA,GAAI,IAAI,CAAC9B,kBAAkB,EAAI,CAAC,IAAI,CAACZ,OAAO,EAAEgB,WAAY,CAMtD,IALAe,EAAa,CAAA,EAAO,IAAI,EAExB,IAAI,CAACnB,kBAAkB,CAACM,OAAO,CAAC,AAACC,GAAWA,EAAOG,KAAK,IAGjD,CAACmB,GACJA,EAAgB,CAAA,EAChB,IAAI,CAAC7B,kBAAkB,CAACM,OAAO,CAACyB,EAEhCD,CAAAA,GACA,IAAI,CAACxD,MAAM,CAACgC,OAAO,CAAC,AAAChC,IACbA,GAAUA,EAAOiC,MAAM,EACvBjC,EAAOgE,MAAM,EAErB,EAER,CACJ,CAW6B,IAAMC,EALJ,CAC3BvD,QA7FJ,SAAwCC,CAAU,EAC1CqC,EAAkCF,EAAiC,iBACnEC,EAAgCpC,EAAY,aAAcsC,GAC1DF,EAAgCpC,EAAY,cAAewC,GAC3DJ,EAAgCpC,EAAY,UAAWyC,GACvDL,EAAgCpC,EAAY,SAAU2C,GAE9D,EAuFIY,aAxGiB,CAAC,EAyGlBC,QAxGY,CAAC,CAyGjB,EAIA,IAAIC,EAAmI3G,EAAoB,KACvJ4G,EAAuJ5G,EAAoBI,CAAC,CAACuG,GASjL,GAAM,CAAEpE,OAAQ,CAAErB,UAAW2F,CAAW,CAAE3F,UAAW,CAAE4F,WAAY,CAAE5F,UAAW6F,CAAU,CAAE,CAAE,CAAE,CAAE,CAAIH,IAEhG,CAAEI,QAAAA,CAAO,CAAEC,OAAAA,CAAM,CAAEC,KAAAA,CAAI,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAE,CAAI5F,KAOhD,AAAC,SAAU3B,CAAgB,EAsGvB,SAASwH,IAIL,OAFA,IAAI,CAACC,IAAI,CAAG,EAAE,CACTC,MAAM,CAAC,IAAI,CAACC,MAAM,EAAI,EAAE,CAAE,IAAI,CAACC,KAAK,EAClCZ,EAAYQ,OAAO,CAACK,KAAK,CAAC,IAAI,CAAEC,UAC3C,CAsDA,SAASC,IACD,IAAI,CAACH,KAAK,GACV,IAAI,CAACA,KAAK,CAAClD,OAAO,CAAC,AAACsD,IAChBA,EAAKR,OAAO,EAChB,GACA,IAAI,CAACI,KAAK,CAACK,MAAM,CAAG,GAExBjB,EAAYe,OAAO,CAACF,KAAK,CAAC,IAAI,CAAEC,UACpC,CAMA,SAASI,EAAaC,CAAK,EACvB,IAAMC,EAAON,UAAWO,EAAS,IAAI,CAACC,MAAM,CAAG,IAAI,CAACC,OAAO,CAACb,MAAM,CAAC,IAAI,CAACc,SAAS,EAC7E,CAAC,IAAI,CAACC,QAAQ,CAAE,IAAI,CAACC,MAAM,CAAC,AAC5BP,AAAU,CAAA,WAAVA,GACAE,EAAO3D,OAAO,CAAC,AAACiE,IACRA,GAAcA,EAAWjG,MAAM,GAC/BwE,EAAW0B,QAAQ,CAACf,KAAK,CAACc,EAAYP,GAClC,CAACO,EAAWL,MAAM,GACdK,EAAWF,QAAQ,CAACI,OAAO,EAC3B3B,EAAW0B,QAAQ,CAACf,KAAK,CAACc,EAAWF,QAAQ,CAAEL,GAE/CO,EAAWD,MAAM,EAAIC,EAAWD,MAAM,CAACG,OAAO,EAC9C3B,EAAW0B,QAAQ,CAACf,KAAK,CAACc,EAAWD,MAAM,CAAEN,IAI7D,GAEJlB,EAAW0B,QAAQ,CAACf,KAAK,CAAC,IAAI,CAAEO,EACpC,CAOA,SAASU,EAAWlG,CAAO,CAAEmC,CAAM,CAAEgE,CAAS,CAAEC,CAAQ,EACpD,IAAMpB,EAAQ,IAAI,CAAClF,MAAM,CAACE,OAAO,CAACgF,KAAK,CAAEH,EAAO,IAAI,CAAC/E,MAAM,CAACE,OAAO,CAAC6E,IAAI,CAAEwB,EAAaxB,GAAMQ,QAAU,EAAGiB,EAAazB,GAAM,CAAC,IAAI,CAAC0B,KAAK,CAAC,CAGzI,GAFAjC,EAAWkC,MAAM,CAAC7H,IAAI,CAAC,IAAI,CAAEqB,EAAS,CAAA,IAAI,CAAC0F,MAAM,EAAWvD,EAC5DgE,EAAWC,GACP,IAAI,CAACV,MAAM,CAAE,CAEb,IAAMe,EAAY,AAACzB,CAAAA,GAAS,EAAE,AAAD,EACxB0B,MAAM,CACX,CAACC,EAAWhJ,EAAG4I,IAAW,IAAI,CAACK,EAAE,GAAKjJ,EAAEiJ,EAAE,CAAGL,EAAQI,EAAY,IAGjEE,EAAanC,EAAMM,GAASA,CAAK,CAACyB,EAAU,EAAI,CAAC,EAAG5B,GAAM,CAAC,IAAI,CAAC0B,KAAK,CAAC,EAAI,CAAC,GAEvE1B,IACIyB,EACAzB,CAAI,CAAC,IAAI,CAAC0B,KAAK,CAAC,CAAGD,EAInBzB,EAAKQ,MAAM,CAAGgB,GAIlBrB,EACIyB,GAAa,EACbzB,CAAK,CAACyB,EAAU,CAAGI,EAGnB7B,EAAM8B,IAAI,CAACD,GAIf,IAAI,CAAC/G,MAAM,CAACE,OAAO,CAACgF,KAAK,CAAG,CAAC6B,EAAW,CAExClC,EAAKxC,EAAQ,CAAA,IACb,IAAI,CAACrC,MAAM,CAACJ,KAAK,CAACyC,MAAM,CAACgE,EAEjC,CACJ,CAxNA/I,EAAiBoD,OAAO,CATxB,SAAiBuG,CAAU,CAAEC,CAAW,EACpC,IAAM1C,EAAayC,EAAWtI,SAAS,CAAE2F,EAAc4C,EAAYvI,SAAS,CAM5E,OALA6F,EAAWgB,YAAY,CAAGA,EAC1BhB,EAAW0B,QAAQ,CAAGV,EACtBhB,EAAWkC,MAAM,CAAGN,EACpB9B,EAAYQ,OAAO,CAAGA,EACtBR,EAAYe,OAAO,CAAGA,EACf6B,CACX,EA2EA5J,EAAiB6J,UAAU,CApE3B,SAAoBL,CAAE,EAClB,IAAMG,EAAa,IAAI,CAAC1C,UAAU,CAAE6C,EAAW,CAAClC,EAAO4B,IAAOnC,EAAKO,EAAO,AAACI,GAASA,EAAKwB,EAAE,GAAKA,GAC5FxB,EAAO8B,EAAS,IAAI,CAAClC,KAAK,CAAE4B,GAAK5G,EACrC,GAAI,CAACoF,EAAM,CACPpF,EAAU,IAAI,CAACA,OAAO,CAACgF,KAAK,EAAIkC,EAAS,IAAI,CAAClH,OAAO,CAACgF,KAAK,CAAE4B,GAC7D,IAAMO,EAAU,IAAIJ,EAAW,IAAI,CAAEvC,EAAO,CACxC4C,UAAW,kBACX1B,OAAQ,CAAA,EACRkB,GAAIA,EACJS,EAAG,CACP,EAAGrH,GACHmH,CAAAA,EAAQxB,OAAO,CAAG,EAAE,CACpBwB,EAAQvB,SAAS,CAAG,EAAE,CAKtBuB,EAAQG,MAAM,CAAG,WACb,IAAIC,EAAQ,EAAGC,EAAU,EAOzB,OANAL,EAAQxB,OAAO,CAAC7D,OAAO,CAAC,AAAC2F,IACrBF,GAASE,EAAKC,MAAM,EAAI,CAC5B,GACAP,EAAQvB,SAAS,CAAC9D,OAAO,CAAC,AAAC2F,IACvBD,GAAWC,EAAKC,MAAM,EAAI,CAC9B,GACOjG,KAAKkG,GAAG,CAACJ,EAAOC,EAC3B,EAKAL,EAAQS,MAAM,CAAG,SAAUnI,CAAK,CAAEoI,CAAI,EAClC,IAAID,EAAS,EACb,IAAK,IAAIE,EAAI,EAAGA,EAAIX,CAAO,CAACU,EAAK,CAACxC,MAAM,CAAEyC,IAAK,CAC3C,GAAIX,CAAO,CAACU,EAAK,CAACC,EAAE,GAAKrI,EACrB,OAAOmI,EAEXA,GAAUT,CAAO,CAACU,EAAK,CAACC,EAAE,CAACJ,MAAM,AACrC,CACJ,EAGAP,EAAQY,QAAQ,CAAG,WACf,IAAIC,EAAW,EAMf,OALAb,EAAQxB,OAAO,CAAC7D,OAAO,CAAC,AAAC2F,IACjBA,EAAKO,QAAQ,EACbA,GAER,GACQ,CAACb,EAAQxB,OAAO,CAACN,MAAM,EAC3B2C,IAAab,EAAQxB,OAAO,CAACN,MAAM,AAC3C,EACA8B,EAAQZ,KAAK,CAAG,IAAI,CAACvB,KAAK,CAAC8B,IAAI,CAACK,GAAW,EAC3C/B,EAAO+B,CACX,CAYA,OAXA/B,EAAK6C,YAAY,CAAG,OAEpB7C,EAAK8C,IAAI,CAAG9C,EAAK8C,IAAI,EAAI9C,EAAKpF,OAAO,CAAC4G,EAAE,EAAI,GAE5CxB,EAAK+C,IAAI,CAAGxD,EAEZS,EAAKpF,OAAO,CAACmI,IAAI,CAAE/C,EAAKpF,OAAO,CAACoI,MAAM,EAAIhD,EAAKpF,OAAO,CAACoI,MAAM,CAACC,MAAM,CAEpE,IAAI,CAACrI,OAAO,CAACoI,MAAM,EAAI,IAAI,CAACpI,OAAO,CAACoI,MAAM,CAACC,MAAM,CAEjD,GACOjD,CACX,EAYAhI,EAAiBwH,OAAO,CAAGA,EAgD3BxH,EAAiBkL,cAAc,CA1C/B,WACI,IAAM5I,EAAQ,IAAI,CAACA,KAAK,CAAE6I,EAAa,CAAC,EACxCnE,EAAYkE,cAAc,CAAC3J,IAAI,CAAC,IAAI,EAChC,AAAC,IAAI,CAACqG,KAAK,EACX,CAAA,IAAI,CAACA,KAAK,CAAG,EAAE,AAAD,EAElB,IAAI,CAACwD,YAAY,CAAG,EAEpB,IAAI,CAACxD,KAAK,CAAClD,OAAO,CAAC,AAACsD,IAChBA,EAAKQ,SAAS,CAACP,MAAM,CAAG,EACxBD,EAAKO,OAAO,CAACN,MAAM,CAAG,EACtBD,EAAKqD,KAAK,CAAGrD,EAAKpF,OAAO,CAACyI,KAAK,AACnC,GAEA,IAAI,CAAC1D,MAAM,CAACjD,OAAO,CAAC,AAACrC,IACb8E,EAAQ9E,EAAMiJ,IAAI,IACd,AAACH,CAAU,CAAC9I,EAAMiJ,IAAI,CAAC,EACvBH,CAAAA,CAAU,CAAC9I,EAAMiJ,IAAI,CAAC,CAAG,IAAI,CAACzB,UAAU,CAACxH,EAAMiJ,IAAI,CAAA,EAEvDH,CAAU,CAAC9I,EAAMiJ,IAAI,CAAC,CAAC9C,SAAS,CAACkB,IAAI,CAACrH,GACtCA,EAAMoG,QAAQ,CAAG0C,CAAU,CAAC9I,EAAMiJ,IAAI,CAAC,CAEnChJ,EAAMiJ,UAAU,CAChBlJ,EAAMmJ,UAAU,CAAGjE,EAAKlF,EAAMO,OAAO,CAAC4I,UAAU,CAAEL,CAAU,CAAC9I,EAAMiJ,IAAI,CAAC,CAACE,UAAU,EAGnFnJ,EAAMoJ,KAAK,CACPpJ,EAAMO,OAAO,CAAC6I,KAAK,EAAIN,CAAU,CAAC9I,EAAMiJ,IAAI,CAAC,CAACG,KAAK,EAG3DtE,EAAQ9E,EAAMqJ,EAAE,IACZ,AAACP,CAAU,CAAC9I,EAAMqJ,EAAE,CAAC,EACrBP,CAAAA,CAAU,CAAC9I,EAAMqJ,EAAE,CAAC,CAAG,IAAI,CAAC7B,UAAU,CAACxH,EAAMqJ,EAAE,CAAA,EAEnDP,CAAU,CAAC9I,EAAMqJ,EAAE,CAAC,CAACnD,OAAO,CAACmB,IAAI,CAACrH,GAClCA,EAAMqG,MAAM,CAAGyC,CAAU,CAAC9I,EAAMqJ,EAAE,CAAC,EAEvCrJ,EAAMyI,IAAI,CAAGzI,EAAMyI,IAAI,EAAIzI,EAAMmH,EAAE,AACvC,EAAG,IAAI,EAEP,IAAI,CAAC2B,UAAU,CAAGA,CACtB,EAwCAnL,EAAiBkI,YAAY,CAAGA,EA6ChClI,EAAiB8I,UAAU,CAAGA,CAClC,EAAG9I,GAAqBA,CAAAA,EAAmB,CAAC,CAAA,GAMf,IAAM2L,EAA2B3L,EAiBxD,CAAE0C,OAAQ,CAAErB,UAAWuK,CAA6B,CAAEvK,UAAW,CAAE4F,WAAY4E,CAAK,CAAE,CAAE,CAAE,CAAI9E,IAE9F,CAAEhF,SAAU+J,CAA0B,CAAEC,IAAAA,CAAG,CAAE5E,QAAS6E,CAAyB,CAAE5E,OAAQ6E,CAAwB,CAAE1E,KAAM2E,CAAsB,CAAE,CAAIvK,GAM3J,OAAMwK,UAA0BN,EAW5BrE,SAAU,CAWN,OAVI,IAAI,CAACc,MAAM,EACX,IAAI,CAACE,SAAS,CAACd,MAAM,CAAC,IAAI,CAACa,OAAO,EAAE7D,OAAO,CAAC,SAAU2F,CAAI,EAGlDA,EAAK+B,eAAe,EACpB/B,EAAK+B,eAAe,EAE5B,GAEJ,IAAI,CAAC1J,MAAM,CAACiC,MAAM,CAAC0H,2BAA2B,CAAC,IAAI,CAAE,IAAI,CAAC3J,MAAM,CAACiC,MAAM,CAAC,IAAI,CAAC2D,MAAM,CAAG,QAAU,QAAQ,EACjGuD,EAAMxK,SAAS,CAACmG,OAAO,CAACK,KAAK,CAAC,IAAI,CAAEC,UAC/C,CAMAwE,WAAY,CACR,IAAMC,EAAM,IAAI,CAACjE,MAAM,CACnB,IAAI,CAACE,SAAS,CAACP,MAAM,CAAG,IAAI,CAACM,OAAO,CAACN,MAAM,CAC3C,EACJ,OAAOsE,AAAQ,IAARA,EAAY,EAAIA,CAC3B,CAKAC,mBAAoB,CAChB,IAAMC,EAAc,IAAI,CAAC/J,MAAM,CAACE,OAAO,CAACyH,IAAI,CAAEqC,EAAe,IAAI,CAAC9J,OAAO,CACzE,MAAO,CACH,eAAgBsJ,EAAuBQ,EAAaC,KAAK,CAAEF,EAAYE,KAAK,EAC5EC,OAASF,EAAajB,KAAK,EAAIgB,EAAYhB,KAAK,CAChDoB,UAAYH,EAAaI,SAAS,EAAIL,EAAYK,SAAS,CAC3DC,QAASb,EAAuBQ,EAAaK,OAAO,CAAEN,EAAYM,OAAO,CAAE,EAC/E,CACJ,CAOAC,aAAc,CACV,IAAIC,EAAO,IAAI,CAACxE,QAAQ,CAAEyE,EAAQ,IAAI,CAACxE,MAAM,CAO7C,OAJIuE,EAAKpJ,KAAK,CAAGqJ,EAAMrJ,KAAK,GACxBoJ,EAAO,IAAI,CAACvE,MAAM,CAClBwE,EAAQ,IAAI,CAACzE,QAAQ,EAElB,CACH,CAAC,IAAKwE,EAAKpJ,KAAK,EAAI,EAAGoJ,EAAKnJ,KAAK,EAAI,EAAE,CACvC,CAAC,IAAKoJ,EAAMrJ,KAAK,EAAI,EAAGqJ,EAAMpJ,KAAK,EAAI,EAAE,CAC5C,AAaL,CASAqJ,SAAU,CACN,IAAMC,EAAK,IAAI,CAAC3E,QAAQ,CAACsC,IAAI,CAAEsC,EAAK,IAAI,CAAC3E,MAAM,CAACqC,IAAI,CAAEuC,EAAMF,EAAKC,EACjE,MAAO,CACH5E,SAAU,EAAI2E,EAAKE,EACnB5E,OAAQ,EAAI2E,EAAKC,CACrB,CACJ,CAMAC,YAAY7K,CAAM,CAAEE,CAAO,CAAE4K,CAAC,CAAE,CAC5B,KAAK,CAAC9K,EAAQE,EAAS4K,GACnB,IAAI,CAAC9K,MAAM,CAACE,OAAO,CAACC,SAAS,EAC7B,CAAC,IAAI,CAACH,MAAM,CAACJ,KAAK,CAACiJ,UAAU,GAC7BO,EAA2B,IAAI,CAAE,YAAa,WAC1CC,EAAI,IAAI,CAACrJ,MAAM,CAACJ,KAAK,CAACC,SAAS,CAAE,CAAEkL,OAAQ,MAAO,EACtD,GACA3B,EAA2B,IAAI,CAAE,WAAY,WACzCC,EAAI,IAAI,CAACrJ,MAAM,CAACJ,KAAK,CAACC,SAAS,CAAE,CAAEkL,OAAQ,SAAU,EACzD,GAER,CAIAC,SAAU,CACN,MAAO,CAAC,IAAI,CAACpF,MAAM,EAAI0D,EAA0B,IAAI,CAACxC,EAAE,CAC5D,CAKAmE,YAAa,CACT,IACIC,EADEC,EAAO,IAAI,CAACb,WAAW,GAE7B,GAAI,IAAI,CAACnE,OAAO,CAAE,CACd,IAAI,CAACiF,SAAS,CAAG,CACbpN,EAAGmN,CACP,EACK,IAAI,CAACnL,MAAM,CAACJ,KAAK,CAACiJ,UAAU,GAC7BqC,EAAU,IAAI,CAAClL,MAAM,CAACqL,YAAY,CAAC,IAAI,EACvC,IAAI,CAAClF,OAAO,CAAC3D,IAAI,CAAC0I,GAClB,AAAC,CAAA,IAAI,CAACI,UAAU,EAAI,EAAE,AAAD,EAAGtJ,OAAO,CAAC,SAAUuJ,CAAK,EACvCA,GACAA,EAAM/I,IAAI,CAAC,CACP6H,QAASa,EAAQb,OAAO,AAC5B,EAER,IAEJ,IAAI,CAAClE,OAAO,CAACqF,OAAO,CAAC,IAAI,CAACJ,SAAS,EAEnC,IAAMhJ,EAAQ+I,CAAI,CAAC,EAAE,CACfM,EAAMN,CAAI,CAAC,EAAE,AACF,CAAA,MAAb/I,CAAK,CAAC,EAAE,EAAYqJ,AAAW,MAAXA,CAAG,CAAC,EAAE,GAC1B,IAAI,CAACtK,KAAK,CAAG,AAACiB,CAAAA,CAAK,CAAC,EAAE,CAAGqJ,CAAG,CAAC,EAAE,AAAD,EAAK,EACnC,IAAI,CAACrK,KAAK,CAAG,AAACgB,CAAAA,CAAK,CAAC,EAAE,CAAGqJ,CAAG,CAAC,EAAE,AAAD,EAAK,EAE3C,CACJ,CAeAC,OAAOrJ,CAAM,CAAEgE,CAAS,CAAE,CACtB,IAAoBrG,EAASL,AAAf,IAAI,CAAiBK,MAAM,CAAE2L,EAAe3L,EAAOE,OAAO,CAACgF,KAAK,EAAI,EAAE,CAChFuB,EAAOuB,EAAI2D,EAAapG,MAAM,CAElC,GAAI5F,AAHU,IAAI,CAGRiG,MAAM,CAAE,CA4Bd,IAzBA5F,EAAOiF,MAAM,CAAG,EAAE,CAElB,EAAE,CACGD,MAAM,CAACrF,AATF,IAAI,CASImG,SAAS,EACtBd,MAAM,CAACrF,AAVF,IAAI,CAUIkG,OAAO,EACpB7D,OAAO,CAAC,SAAU4J,CAAU,EAGzBnF,AADJA,CAAAA,EAAQmF,EAAW7F,QAAQ,CAACD,SAAS,CAAC+F,OAAO,CAACD,EAAU,EAC5C,IACRA,EAAW7F,QAAQ,CAACD,SAAS,CAACgG,MAAM,CAACrF,EAAO,GAI5CA,AADJA,CAAAA,EAAQmF,EAAW5F,MAAM,CAACH,OAAO,CAACgG,OAAO,CAACD,EAAU,EACxC,IACRA,EAAW5F,MAAM,CAACH,OAAO,CAACiG,MAAM,CAACrF,EAAO,GAG5CyC,EAA8B6C,WAAW,CAAClN,IAAI,CAACmB,EAAQA,EAAO+E,IAAI,CAAC8G,OAAO,CAACD,GAAa,CAAA,EAAO,CAAA,EACnG,GAEA5L,EAAOiF,MAAM,CAAGjF,EAAO+E,IAAI,CAACiH,KAAK,GAGjChM,EAAOkF,KAAK,CAAC4G,MAAM,CAAC9L,EAAOkF,KAAK,CAAC2G,OAAO,CA7B9B,IAAI,EA6BmC,GAE1C7D,KACH,GAAI2D,CAAY,CAAC3D,EAAE,CAAClB,EAAE,GAAKnH,AAhCrB,IAAI,CAgCuBO,OAAO,CAAC4G,EAAE,CAAE,CACzC9G,EAAOE,OAAO,CAACgF,KAAK,CAAC4G,MAAM,CAAC9D,EAAG,GAC/B,KACJ,CAEArI,AArCM,IAAI,EAsCVA,AAtCM,IAAI,CAsCJmF,OAAO,GAGjB9E,EAAOiM,OAAO,CAAG,CAAA,EACjBjM,EAAOkM,WAAW,CAAG,CAAA,EACjB7J,GACArC,EAAOJ,KAAK,CAACyC,MAAM,CAACA,EAE5B,MAEIrC,EAAO+L,WAAW,CAAC/L,EAAO+E,IAAI,CAAC8G,OAAO,CAhD5B,IAAI,EAgDiCxJ,EAAQgE,EAE/D,CAKA8F,YAAa,CACT,IAAIjB,CACA,EAAC,IAAI,CAAC/E,OAAO,GACb,IAAI,CAACA,OAAO,CAAG,IAAI,CAACnG,MAAM,CAACJ,KAAK,CAACwM,QAAQ,CACpCjB,IAAI,CAAC,IAAI,CAACb,WAAW,IACrB+B,QAAQ,CAAC,IAAI,CAACC,YAAY,GAAI,CAAA,GAC9BC,GAAG,CAAC,IAAI,CAACvM,MAAM,CAACwM,KAAK,EACrB,IAAI,CAACxM,MAAM,CAACJ,KAAK,CAACiJ,UAAU,GAC7BqC,EAAU,IAAI,CAAClL,MAAM,CAACqL,YAAY,CAAC,IAAI,EACvC,IAAI,CAAClF,OAAO,CAAC3D,IAAI,CAAC0I,GAClB,AAAC,CAAA,IAAI,CAACI,UAAU,EAAI,EAAE,AAAD,EAAGtJ,OAAO,CAAC,SAAUuJ,CAAK,EACvCA,GACAA,EAAM/I,IAAI,CAAC,CACP6H,QAASa,EAAQb,OAAO,AAC5B,EAER,IAGZ,CACJ,CACAd,EAAyBE,EAAkB9K,SAAS,CAAE,CAClDuH,SAAU+C,EAAwBzD,YAAY,AAClD,GAmwB6B,IAAMiH,EATV,CACrBC,WA7JJ,SAAoB/E,CAAI,CAAEgF,CAAK,CAAEC,CAAU,CAAEC,CAAS,EAClD,IAAMC,EAAanF,EAAK8C,OAAO,GAAIsC,EAAc,AAACH,EAAW9B,CAAC,CAAG+B,EAAaF,EAAOK,EAAc,AAACJ,EAAWrF,CAAC,CAAGsF,EAAaF,CAC3HhF,CAAAA,EAAK5B,QAAQ,CAAC/E,aAAa,GAC5B2G,EAAK5B,QAAQ,CAACkH,KAAK,EACfF,EAAcD,EAAW/G,QAAQ,CAAG4B,EAAK5B,QAAQ,CAACmH,MAAM,CAC5DvF,EAAK5B,QAAQ,CAACoH,KAAK,EACfH,EAAcF,EAAW/G,QAAQ,CAAG4B,EAAK5B,QAAQ,CAACmH,MAAM,EAE3DvF,EAAK3B,MAAM,CAAChF,aAAa,GAC1B2G,EAAK3B,MAAM,CAACiH,KAAK,EACbF,EAAcD,EAAW9G,MAAM,CAAG2B,EAAK3B,MAAM,CAACkH,MAAM,CACxDvF,EAAK3B,MAAM,CAACmH,KAAK,EACbH,EAAcF,EAAW9G,MAAM,CAAG2B,EAAK3B,MAAM,CAACkH,MAAM,CAEhE,EAgJIE,wBAjIJ,SAAiCpP,CAAC,CAAEqP,CAAC,EACjC,OAAOrP,EAAIA,EAAIqP,CACnB,EAgIIC,WAtHJ,WACI,IAAMC,EAAwB,IAAI,CAACrN,OAAO,CAACqN,qBAAqB,CAAEC,EAAU,IAAI,CAACF,UAAU,CAACE,OAAO,CAAEC,EAAU,IAAI,CAACH,UAAU,CAACG,OAAO,CACtI,IAAI,CAACvI,KAAK,CAAClD,OAAO,CAAC,SAAUsD,CAAI,EAC7B,GAAI,CAACA,EAAKtE,aAAa,CAAE,CACrB,IAAMkM,EAAS5H,EAAKsE,SAAS,GAAI8D,EAAMR,EAAU,CAAA,EAAIA,EAAS,CAAA,CAC9D5H,CAAAA,EAAK2H,KAAK,EAAK,AAACO,CAAAA,EAAUlI,EAAKnE,KAAK,AAAD,EAC/BoM,EACAG,EAAMpI,EAAK4H,MAAM,CACrB5H,EAAK6H,KAAK,EAAK,AAACM,CAAAA,EAAUnI,EAAKlE,KAAK,AAAD,EAC/BmM,EACAG,EAAMpI,EAAK4H,MAAM,AACzB,CACJ,EACJ,EA0GIS,KApGJ,SAAc1L,CAAM,EAChB,OAAON,KAAKiM,GAAG,CAAC3L,EAAO4L,GAAG,CAAC5D,KAAK,CAAGhI,EAAO4L,GAAG,CAACC,MAAM,CAAG7L,EAAOiD,KAAK,CAACK,MAAM,CAAE,GAChF,EAmGIwI,UAlEJ,SAAmB9L,CAAM,CAAEqD,CAAI,EAC3BA,EAAK2H,KAAK,EACN3H,EAAK2H,KAAK,CAAGhL,EAAO/B,OAAO,CAAC8N,QAAQ,CACxC1I,EAAK6H,KAAK,EACN7H,EAAK6H,KAAK,CAAGlL,EAAO/B,OAAO,CAAC8N,QAAQ,CACxC,IAAMnB,EAAYvH,EAAK1B,WAAW,CAAG3B,EAAOgM,YAAY,CAAC,CACrDnD,EAAGxF,EAAK2H,KAAK,CACb1F,EAAGjC,EAAK6H,KAAK,AACjB,EACkB,CAAA,IAAdN,IACAvH,EAAKnE,KAAK,EAAKmE,EAAK2H,KAAK,CAAGJ,EACxBlL,KAAKuM,GAAG,CAACvM,KAAKC,GAAG,CAAC0D,EAAK2H,KAAK,EAAGhL,EAAO2B,WAAW,EACrD0B,EAAKlE,KAAK,EAAKkE,EAAK6H,KAAK,CAAGN,EACxBlL,KAAKuM,GAAG,CAACvM,KAAKC,GAAG,CAAC0D,EAAK6H,KAAK,EAAGlL,EAAO2B,WAAW,EAE7D,EAoDIuK,UAxCJ,SAAmB7I,CAAI,CAAEqH,CAAK,CAAEC,CAAU,CAAEC,CAAS,EACjDvH,EAAK2H,KAAK,EACN,AAACL,EAAW9B,CAAC,CAAG+B,EAAaF,EAAQrH,EAAK4H,MAAM,CACpD5H,EAAK6H,KAAK,EACN,AAACP,EAAWrF,CAAC,CAAGsF,EAAaF,EAAQrH,EAAK4H,MAAM,AACxD,EAoCIkB,uBAfJ,SAAgCpQ,CAAC,CAAEqP,CAAC,EAChC,OAAOA,EAAIA,EAAIrP,CACnB,CAcA,CAgCA,OAAMqQ,EAMFxD,YAAYgD,CAAG,CAAE,CAab,IAAI,CAACS,IAAI,CAAG,CAAA,EASZ,IAAI,CAACC,OAAO,CAAG,CAAA,EASf,IAAI,CAACC,UAAU,CAAG,CAAA,EASlB,IAAI,CAACtJ,KAAK,CAAG,EAAE,CAOf,IAAI,CAAC2I,GAAG,CAAGA,EAOX,IAAI,CAACY,OAAO,CAAG9M,KAAKuM,GAAG,CAACL,EAAI5D,KAAK,CAAE4D,EAAIC,MAAM,CACjD,CAuBAY,WAAY,CACR,IAAMC,EAAY,IAAI,CAACd,GAAG,CAAC5D,KAAK,CAAG,EAAG2E,EAAa,IAAI,CAACf,GAAG,CAACC,MAAM,CAAG,CAErE,CAAA,IAAI,CAAC5I,KAAK,CAAC,EAAE,CAAG,IAAImJ,EAAa,CAC7B9D,KAAM,IAAI,CAACsD,GAAG,CAACtD,IAAI,CACnBsE,IAAK,IAAI,CAAChB,GAAG,CAACgB,GAAG,CACjB5E,MAAO0E,EACPb,OAAQc,CACZ,GAEA,IAAI,CAAC1J,KAAK,CAAC,EAAE,CAAG,IAAImJ,EAAa,CAC7B9D,KAAM,IAAI,CAACsD,GAAG,CAACtD,IAAI,CAAGoE,EACtBE,IAAK,IAAI,CAAChB,GAAG,CAACgB,GAAG,CACjB5E,MAAO0E,EACPb,OAAQc,CACZ,GAEA,IAAI,CAAC1J,KAAK,CAAC,EAAE,CAAG,IAAImJ,EAAa,CAC7B9D,KAAM,IAAI,CAACsD,GAAG,CAACtD,IAAI,CAAGoE,EACtBE,IAAK,IAAI,CAAChB,GAAG,CAACgB,GAAG,CAAGD,EACpB3E,MAAO0E,EACPb,OAAQc,CACZ,GAEA,IAAI,CAAC1J,KAAK,CAAC,EAAE,CAAG,IAAImJ,EAAa,CAC7B9D,KAAM,IAAI,CAACsD,GAAG,CAACtD,IAAI,CACnBsE,IAAK,IAAI,CAAChB,GAAG,CAACgB,GAAG,CAAGD,EACpB3E,MAAO0E,EACPb,OAAQc,CACZ,EACJ,CAMAE,eAAenP,CAAK,CAAE,CAClB,IAAM4K,EAAO5K,EAAMwB,KAAK,CAAG,IAAI,CAAC0M,GAAG,CAACtD,IAAI,CAAG,IAAI,CAACsD,GAAG,CAAC5D,KAAK,CAAG,EAAG4E,EAAMlP,EAAMyB,KAAK,CAAG,IAAI,CAACyM,GAAG,CAACgB,GAAG,CAAG,IAAI,CAAChB,GAAG,CAACC,MAAM,CAAG,EAsBpH,OApBIvD,EAOY,GANRsE,EAUAA,EAEQ,EAIA,CAIpB,CAUAE,OAAOpP,CAAK,CAAEqP,CAAK,CAAE,CACjB,IAAIC,CACA,CAAA,IAAI,CAACT,UAAU,CAEf,IAAI,CAACtJ,KAAK,CAAC,IAAI,CAAC4J,cAAc,CAACnP,GAAO,CAACoP,MAAM,CAACpP,EAAOqP,EAAQ,IAG7D,IAAI,CAACT,OAAO,CAAG,CAAA,EACV,IAAI,CAACD,IAAI,CAMNU,GAEA,IAAI,CAACR,UAAU,CAAG,CAAA,EAClB,IAAI,CAACE,SAAS,GAEI,CAAA,IAAd,IAAI,CAACJ,IAAI,GACT,IAAI,CAACpJ,KAAK,CAAC,IAAI,CAAC4J,cAAc,CAAC,IAAI,CAACR,IAAI,EAAE,CACrCS,MAAM,CAAC,IAAI,CAACT,IAAI,CAAEU,EAAQ,GAC/B,IAAI,CAACV,IAAI,CAAG,CAAA,GAGhB,IAAI,CAACpJ,KAAK,CAAC,IAAI,CAAC4J,cAAc,CAACnP,GAAO,CACjCoP,MAAM,CAACpP,EAAOqP,EAAQ,KAkB3BC,AAPAA,CAAAA,EAAkB,IAAIZ,EAAa,CAC/BQ,IAAKlP,EAAMwB,KAAK,EAAI+N,IACpB3E,KAAM5K,EAAMyB,KAAK,EAAI8N,IAErBjF,MAAO,GACP6D,OAAQ,EACZ,EAAC,EACeQ,IAAI,CAAG3O,EACvBsP,EAAgBT,UAAU,CAAG,CAAA,EAC7B,IAAI,CAACtJ,KAAK,CAAC8B,IAAI,CAACiI,KApCpB,IAAI,CAACT,UAAU,CAAG,CAAA,EAClB,IAAI,CAACF,IAAI,CAAG3O,GAuCxB,CAKAwP,qBAAsB,CAClB,IAAI9G,EAAO,EAAGlH,EAAQ,EAAGC,EAAQ,EACjC,GAAI,IAAI,CAACoN,UAAU,CAAE,CAEjB,IAAK,IAAMY,KAAa,IAAI,CAAClK,KAAK,CACzBkK,EAAUb,OAAO,GAClBlG,GAAQ+G,EAAU/G,IAAI,CACtBlH,GAASiO,EAAUjO,KAAK,CAAGiO,EAAU/G,IAAI,CACzCjH,GAASgO,EAAUhO,KAAK,CAAGgO,EAAU/G,IAAI,EAGjDlH,GAASkH,EACTjH,GAASiH,CACb,MACS,IAAI,CAACiG,IAAI,GAEdjG,EAAO,IAAI,CAACiG,IAAI,CAACjG,IAAI,CACrBlH,EAAQ,IAAI,CAACmN,IAAI,CAACnN,KAAK,CACvBC,EAAQ,IAAI,CAACkN,IAAI,CAAClN,KAAK,CAG3B,CAAA,IAAI,CAACiH,IAAI,CAAGA,EACZ,IAAI,CAAClH,KAAK,CAAGA,EACb,IAAI,CAACC,KAAK,CAAGA,CACjB,CACJ,CAuJ6B,IAAMiO,EA3GnC,MAMIxE,YAAYC,CAAC,CAAEvD,CAAC,CAAE0C,CAAK,CAAE6D,CAAM,CAAE,CAE7B,IAAI,CAACD,GAAG,CAAG,CACPtD,KAAMO,EACN+D,IAAKtH,EACL0C,MAAOA,EACP6D,OAAQA,CACZ,EACA,IAAI,CAACwB,QAAQ,CAAG,GAChB,IAAI,CAAC3S,IAAI,CAAG,IArD4C0R,EAqDd,IAAI,CAACR,GAAG,EAClD,IAAI,CAAClR,IAAI,CAAC6R,UAAU,CAAG,CAAA,EACvB,IAAI,CAAC7R,IAAI,CAAC4S,MAAM,CAAG,CAAA,EACnB,IAAI,CAAC5S,IAAI,CAAC+R,SAAS,EACvB,CASAc,wBAAyB,CACrB,IAAI,CAACC,kBAAkB,CAAC,KAAM,KAAM,SAAUnK,CAAI,EAC9CA,EAAK6J,mBAAmB,EAC5B,EACJ,CAOAO,YAAYzK,CAAM,CAAE,CAChB,IAAK,IAAMtF,KAASsF,EAChB,IAAI,CAACtI,IAAI,CAACoS,MAAM,CAACpP,EAAO,IAAI,CAAC2P,QAAQ,CAE7C,CAwBAG,mBAAmBnK,CAAI,CAAEqK,CAAc,CAAEC,CAAa,CAAE,CACpD,IAAIC,EAOJ,GANI,AAACvK,GACDA,CAAAA,EAAO,IAAI,CAAC3I,IAAI,AAAD,EAEf2I,IAAS,IAAI,CAAC3I,IAAI,EAAIgT,GACtBE,CAAAA,EAAYF,EAAerK,EAAI,EAE/BuK,AAAc,CAAA,IAAdA,GAGJ,IAAK,IAAMC,KAAUxK,EAAKJ,KAAK,CAAE,CAC7B,GAAI4K,EAAOtB,UAAU,CAAE,CAInB,GAHImB,GACAE,CAAAA,EAAYF,EAAeG,EAAM,EAEjCD,AAAc,CAAA,IAAdA,EACA,SAEJ,IAAI,CAACJ,kBAAkB,CAACK,EAAQH,EAAgBC,EACpD,MACSE,EAAOxB,IAAI,EACZqB,GACAA,EAAeG,EAAOxB,IAAI,CAG9BsB,CAAAA,GACAA,EAAcE,EAEtB,CACIxK,IAAS,IAAI,CAAC3I,IAAI,EAAIiT,GACtBA,EAActK,GAEtB,CACJ,EAqMmCyK,EATT,CACtBrD,WArJJ,SAAsC/E,CAAI,CAAEgF,CAAK,CAAEC,CAAU,EACzD,IAAME,EAAanF,EAAK8C,OAAO,GAAIsC,EAAc,CAACH,EAAW9B,CAAC,CAAG6B,EAAQ,IAAI,CAACqD,eAAe,CAAEhD,EAAc,CAACJ,EAAWrF,CAAC,CAAGoF,EAAQ,IAAI,CAACqD,eAAe,AACpJrI,CAAAA,EAAK5B,QAAQ,CAAC/E,aAAa,GAC5B2G,EAAK5B,QAAQ,CAAC5E,KAAK,EACf4L,EAAcD,EAAW/G,QAAQ,CAAG4B,EAAK5B,QAAQ,CAACmH,MAAM,CAC5DvF,EAAK5B,QAAQ,CAAC3E,KAAK,EACf4L,EAAcF,EAAW/G,QAAQ,CAAG4B,EAAK5B,QAAQ,CAACmH,MAAM,EAE3DvF,EAAK3B,MAAM,CAAChF,aAAa,GAC1B2G,EAAK3B,MAAM,CAAC7E,KAAK,EACb4L,EAAcD,EAAW9G,MAAM,CAAG2B,EAAK3B,MAAM,CAACkH,MAAM,CACxDvF,EAAK3B,MAAM,CAAC5E,KAAK,EACb4L,EAAcF,EAAW9G,MAAM,CAAG2B,EAAK3B,MAAM,CAACkH,MAAM,CAEhE,EAwIIE,wBA9HJ,SAAmDpP,CAAC,CAAEqP,CAAC,EAEnD,MAAO,AAACA,CAAAA,EAAIrP,CAAAA,EAAKA,CACrB,EA4HIsP,WAlHJ,WACI,IAAMC,EAAwB,IAAI,CAACrN,OAAO,CAACqN,qBAAqB,EAAI,EAAGC,EAAU,AAAC,CAAA,IAAI,CAACF,UAAU,CAACE,OAAO,CACrG,AAAC,CAAA,IAAI,CAACK,GAAG,CAACtD,IAAI,CAAG,IAAI,CAACsD,GAAG,CAAC5D,KAAK,AAAD,EAAK,CAAA,EAAKsD,EAAuBE,EAAU,AAAC,CAAA,IAAI,CAACH,UAAU,CAACG,OAAO,CACjG,AAAC,CAAA,IAAI,CAACI,GAAG,CAACgB,GAAG,CAAG,IAAI,CAAChB,GAAG,CAACC,MAAM,AAAD,EAAK,CAAA,EAAKP,EAC5C,IAAI,CAACrI,KAAK,CAAClD,OAAO,CAAC,SAAUsD,CAAI,EACxBA,EAAKtE,aAAa,GACnBsE,EAAKnE,KAAK,EACNqM,EAAUlI,EAAK+C,IAAI,CAAG/C,EAAK4H,MAAM,CACrC5H,EAAKlE,KAAK,EACNqM,EAAUnI,EAAK+C,IAAI,CAAG/C,EAAK4H,MAAM,CAE7C,EACJ,EAuGIS,KAjGJ,SAAgC1L,CAAM,EAClC,OAAON,KAAKiM,GAAG,CAAC3L,EAAO4L,GAAG,CAAC5D,KAAK,CAAGhI,EAAO4L,GAAG,CAACC,MAAM,CAAG7L,EAAOiD,KAAK,CAACK,MAAM,CAAE,GAChF,EAgGIwI,UAhEJ,SAAqC9L,CAAM,CAAEqD,CAAI,EAC7C,IAAM0I,EAAW,CAAC/L,EAAO/B,OAAO,CAAC8N,QAAQ,CAAEiC,EAAWhO,EAAO/B,OAAO,CAAC+P,QAAQ,CAAEC,EAAQ5K,EAAK4K,KAAK,CAAEC,EAAQ7K,EAAK6K,KAAK,CAErHC,EAAa,AAAC9K,CAAAA,EAAKnE,KAAK,CAAGmE,EAAK2H,KAAK,CACjCiD,CAAI,EAAKlC,EAAWqC,EAAa,AAAC/K,CAAAA,EAAKlE,KAAK,CAAGkE,EAAK6H,KAAK,CACzDgD,CAAI,EAAKnC,EAAWpM,EAAMD,KAAKC,GAAG,CAAE0O,EAAQ1O,EAAIwO,GAAcA,CAAAA,GAAa,CAAA,EAC/EG,EAAQ3O,EAAIyO,GAAcA,CAAAA,GAAa,CAAA,EAEvC7O,EAAQ8O,EAAQ3O,KAAKuM,GAAG,CAAC+B,EAAUtO,KAAKC,GAAG,CAACwO,IAAa3O,EAAQ8O,EAAQ5O,KAAKuM,GAAG,CAAC+B,EAAUtO,KAAKC,GAAG,CAACyO,GAErG/K,CAAAA,EAAK4K,KAAK,CAAG5K,EAAKnE,KAAK,CAAGmE,EAAK2H,KAAK,CACpC3H,EAAK6K,KAAK,CAAG7K,EAAKlE,KAAK,CAAGkE,EAAK6H,KAAK,CAEpC7H,EAAKnE,KAAK,EAAIK,EACd8D,EAAKlE,KAAK,EAAIK,EACd6D,EAAK1B,WAAW,CAAG3B,EAAOgM,YAAY,CAAC,CACnCnD,EAAGtJ,EACH+F,EAAG9F,CACP,EACJ,EA8CI0M,UA/BJ,SAAqC7I,CAAI,CAAEqH,CAAK,CAAEC,CAAU,EACxD,IAAM4D,EAAS7D,EAAQ,IAAI,CAACqD,eAAe,CAAG1K,EAAK+C,IAAI,CAAG/C,EAAK4H,MAAM,AAChE5H,CAAAA,EAAKtE,aAAa,GACnBsE,EAAKnE,KAAK,EAAIyL,EAAW9B,CAAC,CAAG0F,EAC7BlL,EAAKlE,KAAK,EAAIwL,EAAWrF,CAAC,CAAGiJ,EAErC,EA0BIpC,uBAhBJ,SAAkDpQ,CAAC,CAAEqP,CAAC,EAElD,MAAO,AAACA,CAAAA,EAAIrP,CAAAA,EAAKA,EAAKqP,CAAAA,EAAIrP,CAAAA,CAC9B,CAcA,EAkBM,CAAEyS,IAAAA,CAAG,CAAE,CAAIxR,IAIX,CAAEyR,MAAAA,CAAK,CAAEjM,QAASkM,CAAiC,CAAEC,WAAAA,CAAU,CAAEC,UAAAA,CAAS,CAAEhM,KAAMiM,CAA8B,CAAE,CAAI7R,GAY5H,OAAM8R,GACFlG,aAAc,CAMV,IAAI,CAACgD,GAAG,CAAG,CAAC,EACZ,IAAI,CAACmD,WAAW,CAAG,EACnB,IAAI,CAACC,gBAAgB,CAAG,CAAA,EACxB,IAAI,CAACC,KAAK,CAAG,EAAE,CACf,IAAI,CAAChM,KAAK,CAAG,EAAE,CACf,IAAI,CAAClF,MAAM,CAAG,EAAE,CAChB,IAAI,CAACmR,UAAU,CAAG,CAAA,CACtB,CACA,OAAOzQ,QAAQC,CAAU,CAAE,CACvBsD,EAA8BvD,OAAO,CAACC,GACtCsD,EAA8BC,YAAY,CAACkN,KAAK,CAAG3E,EACnDxI,EAA8BC,YAAY,CAACmN,MAAM,CAAGtB,EACpD9L,EAA8BE,OAAO,CAAC,uBAAuB,CACzD4M,EACR,CACAO,KAAKpR,CAAO,CAAE,CACV,IAAI,CAACA,OAAO,CAAGA,EACf,IAAI,CAACgF,KAAK,CAAG,EAAE,CACf,IAAI,CAACgM,KAAK,CAAG,EAAE,CACf,IAAI,CAAClR,MAAM,CAAG,EAAE,CAChB,IAAI,CAAC6N,GAAG,CAAG,CACP/C,EAAG,EACHvD,EAAG,EACH0C,MAAO,EACP6D,OAAQ,CACZ,EACA,IAAI,CAACyD,mBAAmB,CAAC,CAAA,GACzB,IAAI,CAACC,WAAW,CACZvN,EAA8BC,YAAY,CAAChE,EAAQsR,WAAW,CAAC,CACnE,IAAI,CAACrP,gBAAgB,CAAGjC,EAAQiC,gBAAgB,CAChD,IAAI,CAACsP,eAAe,CAAGX,EAA+B5Q,EAAQuR,eAAe,CAAE,IAAI,CAACD,WAAW,CAACpE,uBAAuB,EACvH,IAAI,CAACsE,cAAc,CAAGZ,EAA+B5Q,EAAQwR,cAAc,CAAE,IAAI,CAACF,WAAW,CAACpD,sBAAsB,EACpH,IAAI,CAACuD,aAAa,CAAGzR,EAAQyR,aAAa,AAC9C,CACAzO,iBAAiB0O,CAAM,CAAE,CACrB,IAAI,CAACzP,gBAAgB,CAAG2O,EAA+Bc,EAAQ,IAAI,CAAC1R,OAAO,CAACiC,gBAAgB,CAChG,CACAC,OAAQ,CACJ,IAAqBpC,EAAS,IAAI,CAACA,MAAM,CAAEE,EAAU,IAAI,CAACA,OAAO,AACjE+B,CADe,IAAI,CACZ+O,WAAW,CAAG,EACrB/O,AAFe,IAAI,CAEZ4P,MAAM,CAAG7R,CAAM,CAAC,EAAE,EAAIA,CAAM,CAAC,EAAE,CAAC6R,MAAM,EAAI,EAAE,CACnD5P,AAHe,IAAI,CAGZrC,KAAK,CAAGI,CAAM,CAAC,EAAE,EAAIA,CAAM,CAAC,EAAE,CAACJ,KAAK,CACvCqC,AAJW,IAAI,CAIRgP,gBAAgB,GACvBhP,AALW,IAAI,CAKR6P,aAAa,GAEpB9R,EAAOgC,OAAO,CAAC,SAAU+P,CAAC,EACtBA,EAAEC,iBAAiB,CAAG,CAAA,EACtBD,EAAE/N,MAAM,EACZ,IAEJ/B,AAZe,IAAI,CAYZgQ,IAAI,GACXhQ,AAbe,IAAI,CAaZiQ,eAAe,CAAChS,GACnB+B,AAdW,IAAI,CAcRE,gBAAgB,EACvBF,AAfW,IAAI,CAeR8B,IAAI,EAEnB,CACAA,MAAO,CACH,IAAwBoO,EAAY,IAAI,CAACnS,MAAM,CAO/C,IAAK,IAAMoS,KALX,IAAI,CAACpB,WAAW,GACW,eAAvB,IAAI,CAACW,aAAa,GAClB,IAAI,CAACU,cAAc,GACnB,IAAI,CAACC,QAAQ,CAAC9C,sBAAsB,IAEhB,IAAI,CAACqC,MAAM,EAAI,EAAE,EACrCU,AARc,IAAI,AAQT,CAACH,EAAY,SAAS,CAAC,IAAI,CAACxO,WAAW,EAQpD,GALA,IAAI,CAAC4O,WAAW,GAEhB,IAAI,CAAC5O,WAAW,CAAG,IAAI,CAAC6O,QAAQ,CAAC,IAAI,CAACC,gBAAgB,CAAE,IAAI,CAAC1C,eAAe,CAAE,IAAI,CAACgB,WAAW,EAC9F,IAAI,CAAC2B,qBAAqB,CAAG,IAAI,CAACC,iBAAiB,CACnD,IAAI,CAACA,iBAAiB,CAAG,IAAI,CAACC,oBAAoB,GAC9C,IAAI,CAAC1Q,gBAAgB,CAAE,CACvB,IAAK,IAAMnC,KAAUmS,EAEbnS,EAAOJ,KAAK,EACZI,EAAOgE,MAAM,EAGjB,CAAA,IAAI,CAACN,aAAa,IAClBC,SAAS,IAAI,CAACC,WAAW,GACzB,CAAC,IAAI,CAACC,QAAQ,IACV,IAAI,CAACsN,UAAU,EACfV,EAAIqC,oBAAoB,CAAC,IAAI,CAAC3B,UAAU,EAE5C,IAAI,CAACA,UAAU,CAAGV,EAAIsC,qBAAqB,CAAC,IAAM,IAAI,CAAChP,IAAI,MAG3D,IAAI,CAACoN,UAAU,CAAG,CAAA,EAClB,IAAI,CAACnR,MAAM,CAACgC,OAAO,CAAC,AAAC+P,IACjBlB,EAAUkB,EAAG,kBACjB,GAER,CACJ,CACA1O,MAAO,CACC,IAAI,CAAC8N,UAAU,EACfV,EAAIqC,oBAAoB,CAAC,IAAI,CAAC3B,UAAU,CAEhD,CACA6B,QAAQlI,CAAC,CAAEvD,CAAC,CAAE0L,CAAC,CAAEC,CAAC,CAAE,CAChB,IAAI,CAACrF,GAAG,CAAG,CACPtD,KAAMO,EACN+D,IAAKtH,EACL0C,MAAOgJ,EACPnF,OAAQoF,CACZ,CACJ,CACAjB,MAAO,CAGH,IAAI,CAAC5E,CAAC,CAAG,IAAI,CAACnN,OAAO,CAACiT,UAAU,EAAI,IAAI,CAAC3B,WAAW,CAAC7D,IAAI,CAAC,IAAI,CAClE,CACAyF,wBAAwBC,CAAQ,CAAEC,CAAU,CAAE,CAC1C,IAAK,IAAMC,KAAWF,EACdC,AAAgC,KAAhCA,EAAWzH,OAAO,CAAC0H,IACnBD,EAAWtM,IAAI,CAACuM,EAG5B,CACA5J,4BAA4B4J,CAAO,CAAED,CAAU,CAAE,CAC7C,IAAM7M,EAAQ6M,EAAWzH,OAAO,CAAC0H,EAC7B9M,AAAU,CAAA,KAAVA,GACA6M,EAAWxH,MAAM,CAACrF,EAAO,EAEjC,CACA+M,OAAQ,CACJ,IAAI,CAACtO,KAAK,CAACK,MAAM,CAAG,EACpB,IAAI,CAAC2L,KAAK,CAAC3L,MAAM,CAAG,EACpB,IAAI,CAACvF,MAAM,CAACuF,MAAM,CAAG,EACrB,IAAI,CAAC2M,eAAe,EACxB,CACAA,iBAAkB,CACd,IAAI,CAACuB,UAAU,CAAG,CAAA,EAClB,IAAI,CAACb,iBAAiB,CAAG,EACzB,IAAI,CAACc,gBAAgB,GACrB,IAAI,CAACC,cAAc,GACnB,IAAI,CAACC,kBAAkB,EAC3B,CACA1R,mBAAoB,CACX,IAAI,CAACiP,UAAU,CAoBhB,IAAI,CAACe,eAAe,IAjBpB,IAAI,CAACX,mBAAmB,CAAC,CAAA,GAEpB,IAAI,CAACpP,gBAAgB,CAKtB,IAAI,CAACC,KAAK,GAHV,IAAI,CAACsR,gBAAgB,CAAC,GAKtB,IAAI,CAAC9T,KAAK,EACV,IAAI,CAACA,KAAK,CAACyC,MAAM,GAGrB,IAAI,CAACkP,mBAAmB,CAAC,CAAA,GAMjC,CACAmC,iBAAiBhQ,CAAa,CAAE,CAC5B,IAAI,CAACA,aAAa,CAAGoN,EAA+BpN,EAAe,IAAI,CAACxD,OAAO,CAACwD,aAAa,CACjG,CACAiQ,gBAAiB,CACb,IAAI,CAAC/P,WAAW,CAAG,IAAI,CAAC8O,gBAAgB,CACpC/Q,KAAKkS,IAAI,CAAC,IAAI,CAAC3O,KAAK,CAACK,MAAM,CACnC,CACAqO,oBAAqB,CACjB,IAAI,CAAC5D,eAAe,CAAG,IAAI,CAAC0C,gBAAgB,CACvC,CAAA,IAAI,CAACxS,OAAO,CAACwD,aAAa,CAAG,CAAA,CACtC,CACA6N,oBAAoBK,CAAM,CAAE,CACxB,IAAI,CAACX,gBAAgB,CAAGW,CAC5B,CACAS,gBAAiB,CACb,IAAI,CAACC,QAAQ,CAAG,IAAIjD,EAAsB,IAAI,CAACxB,GAAG,CAACtD,IAAI,CAAE,IAAI,CAACsD,GAAG,CAACgB,GAAG,CAAE,IAAI,CAAChB,GAAG,CAAC5D,KAAK,CAAE,IAAI,CAAC4D,GAAG,CAACC,MAAM,EACtG,IAAI,CAACwE,QAAQ,CAAC5C,WAAW,CAAC,IAAI,CAACxK,KAAK,CACxC,CACA4M,eAAgB,CACZ,IAAMgC,EAAmB,IAAI,CAAC5T,OAAO,CAAC4T,gBAAgB,CACtD,GAAIlD,EAAWkD,GAEX,IAAK,IAAMxO,KADXwO,EAAiBjV,IAAI,CAAC,IAAI,EACP,IAAI,CAACqG,KAAK,EACrB,AAACyL,EAAkCrL,EAAK4K,KAAK,GAC7C5K,CAAAA,EAAK4K,KAAK,CAAG5K,EAAKnE,KAAK,AAAD,EAEtB,AAACwP,EAAkCrL,EAAK6K,KAAK,GAC7C7K,CAAAA,EAAK6K,KAAK,CAAG7K,EAAKlE,KAAK,AAAD,EAE1BkE,EAAK2H,KAAK,CAAG,EACb3H,EAAK6H,KAAK,CAAG,MAGZ2G,AAAqB,WAArBA,EACL,IAAI,CAACC,oBAAoB,GAGzB,IAAI,CAACC,kBAAkB,EAE/B,CACAD,sBAAuB,CACnB,IA+BIzO,EA/BEuI,EAAM,IAAI,CAACA,GAAG,CAAE3I,EAAQ,IAAI,CAACA,KAAK,CAAkC+O,EAAQ,EAAItS,KAAKuS,EAAE,CAArChP,CAAAA,EAAMK,MAAM,CAAG,CAAA,EAAsC4O,EAAYjP,EAAMkP,MAAM,CAAC,SAAU9O,CAAI,EAChJ,OAAOA,AAAwB,IAAxBA,EAAKO,OAAO,CAACN,MAAM,AAC9B,GAAI8O,EAAe,CAAC,EAAG9L,EAAS,IAAI,CAACrI,OAAO,CAACoU,qBAAqB,CAAEC,EAAa,AAACjP,IAC9E,IAAK,IAAMqC,KAAQrC,EAAKQ,SAAS,EAAI,EAAE,CAC9BuO,CAAY,CAAC1M,EAAK3B,MAAM,CAACc,EAAE,CAAC,GAC7BuN,CAAY,CAAC1M,EAAK3B,MAAM,CAACc,EAAE,CAAC,CAAG,CAAA,EAC/B0N,EAAYxN,IAAI,CAACW,EAAK3B,MAAM,EAC5BuO,EAAW5M,EAAK3B,MAAM,EAGlC,EACIwO,EAAc,EAAE,CAIpB,IAAK,IAAMC,KAAYN,EACnBK,EAAYxN,IAAI,CAACyN,GACjBF,EAAWE,GAGf,GAAKD,EAAYjP,MAAM,CAKnB,IAAK,IAAMD,KAAQJ,EACXsP,AAA8B,KAA9BA,EAAY3I,OAAO,CAACvG,IACpBkP,EAAYxN,IAAI,CAAC1B,QANzBkP,EAActP,EAalB,IAAK,IAAI8C,EAAI,EAAG0M,EAAOF,EAAYjP,MAAM,CAAEyC,EAAI0M,EAAM,EAAE1M,EAEnD1C,AADAA,CAAAA,EAAOkP,CAAW,CAACxM,EAAE,AAAD,EACf7G,KAAK,CAAGmE,EAAK4K,KAAK,CAAGY,EAA+BxL,EAAKnE,KAAK,CAAE0M,EAAI5D,KAAK,CAAG,EAAI1B,EAAS5G,KAAKgT,GAAG,CAAC3M,EAAIiM,IAC3G3O,EAAKlE,KAAK,CAAGkE,EAAK6K,KAAK,CAAGW,EAA+BxL,EAAKlE,KAAK,CAAEyM,EAAIC,MAAM,CAAG,EAAIvF,EAAS5G,KAAKiT,GAAG,CAAC5M,EAAIiM,IAC5G3O,EAAK2H,KAAK,CAAG,EACb3H,EAAK6H,KAAK,CAAG,CAErB,CACA6G,oBAAqB,CACjB,IAWI1O,EAXEuI,EAAM,IAAI,CAACA,GAAG,CAAE3I,EAAQ,IAAI,CAACA,KAAK,CAAE2P,EAAc3P,EAAMK,MAAM,CAAG,EAMvEuP,EAAW,AAACjX,IACR,IAAIkX,EAAOlX,EAAIA,EAAI8D,KAAKuS,EAAE,CAE1B,OADAa,EAAcpT,KAAKqT,KAAK,CAACD,EAE7B,EAGA,IAAK,IAAI/M,EAAI,EAAG0M,EAAOxP,EAAMK,MAAM,CAAEyC,EAAI0M,EAAM,EAAE1M,EAE7C1C,AADAA,CAAAA,EAAOJ,CAAK,CAAC8C,EAAE,AAAD,EACT7G,KAAK,CAAGmE,EAAK4K,KAAK,CAAGY,EAA+BxL,EAAKnE,KAAK,CAAE0M,EAAI5D,KAAK,CAAG6K,EAAS9M,IAC1F1C,EAAKlE,KAAK,CAAGkE,EAAK6K,KAAK,CAAGW,EAA+BxL,EAAKlE,KAAK,CAAEyM,EAAIC,MAAM,CAAGgH,EAASD,EAAc7M,IACzG1C,EAAK2H,KAAK,CAAG,EACb3H,EAAK6H,KAAK,CAAG,CAErB,CACAR,MAAMvE,CAAI,CAAE,GAAG1C,CAAI,CAAE,CACjB,IAAI,CAAC8L,WAAW,CAACpJ,EAAK,CAACjD,KAAK,CAAC,IAAI,CAAEO,EACvC,CACAuP,kBAAmB,CACf,IAAI,CAACC,aAAa,GAClB,IAAI,CAACvI,KAAK,CAAC,aACf,CACAuI,eAAgB,CACZ,IAAIC,EAAa,EAAGC,EAAK,EAAGC,EAAK,EACjC,IAAK,IAAM/P,KAAQ,IAAI,CAACJ,KAAK,CACzBkQ,GAAM9P,EAAKnE,KAAK,CAAGmE,EAAK+C,IAAI,CAC5BgN,GAAM/P,EAAKlE,KAAK,CAAGkE,EAAK+C,IAAI,CAC5B8M,GAAc7P,EAAK+C,IAAI,CAQ3B,OANA,IAAI,CAACiF,UAAU,CAAG,CACdxC,EAAGsK,EACH7N,EAAG8N,EACH7H,QAAS4H,EAAKD,EACd1H,QAAS4H,EAAKF,CAClB,EACO,IAAI,CAAC7H,UAAU,AAC1B,CACAgI,uBAAuBhQ,CAAI,CAAEiQ,CAAQ,CAAE,CACnC,IACIC,EAAU7I,EADRC,EAAa,IAAI,CAAC6I,SAAS,CAACnQ,EAAMiQ,GAAW1I,EAAY,IAAI,CAACoB,YAAY,CAACrB,GAwBjF,OAtBItH,IAASiQ,GAAY1I,AAAc,IAAdA,IACjB0I,EAAS/G,UAAU,CAEf+G,EAAS9G,OAAO,CAAG5B,EACnB,IAAI,CAAC3M,OAAO,CAACwV,KAAK,EAClB7I,AAAc,IAAdA,GAEAF,EAAQ,IAAI,CAAC+E,cAAc,CAAC7E,EAAW,IAAI,CAACQ,CAAC,EAC7C,IAAI,CAACV,KAAK,CAAC,YAAarH,EAAMqH,EAAQ4I,EAASlN,IAAI,CAAEuE,EAAYC,GACjE2I,EAAW,CAAA,GAIXA,EAAW,CAAA,GAKf7I,EAAQ,IAAI,CAAC+E,cAAc,CAAC7E,EAAW,IAAI,CAACQ,CAAC,EAC7C,IAAI,CAACV,KAAK,CAAC,YAAarH,EAAMqH,EAAQ4I,EAASlN,IAAI,CAAEuE,EAAYC,KAGlE2I,CACX,CACAG,iBAAkB,CACd,GAAI,AAAuB,eAAvB,IAAI,CAAChE,aAAa,CAClB,IAAK,IAAMrM,KAAQ,IAAI,CAACJ,KAAK,CACzB,IAAI,CAACoN,QAAQ,CAAC7C,kBAAkB,CAAC,KAAM,AAAC8F,GAAc,IAAI,CAACD,sBAAsB,CAAChQ,EAAMiQ,QAG3F,CACD,IAAI5I,EAAOE,EAAWD,EACtB,IAAK,IAAMtH,KAAQ,IAAI,CAACJ,KAAK,CACzB,IAAK,IAAM0Q,KAAW,IAAI,CAAC1Q,KAAK,CAG5BI,IAASsQ,GAIJtQ,EAAKtE,aAAa,GACnB4L,EAAa,IAAI,CAAC6I,SAAS,CAACnQ,EAAMsQ,GAEhB,IADlB/I,CAAAA,EAAY,IAAI,CAACoB,YAAY,CAACrB,EAAU,IAEpCD,EAAQ,IAAI,CAAC+E,cAAc,CAAC7E,EAAW,IAAI,CAACQ,CAAC,EAC7C,IAAI,CAACV,KAAK,CAAC,YAAarH,EAAMqH,EAAQiJ,EAAQvN,IAAI,CAAEuE,EAAYC,IAKpF,CACJ,CACAgJ,kBAAmB,CACf,IAAIjJ,EAAYC,EAAWF,EAC3B,IAAK,IAAMhF,KAAQ,IAAI,CAACuJ,KAAK,CACrBvJ,EAAK5B,QAAQ,EAAI4B,EAAK3B,MAAM,GAC5B4G,EAAa,IAAI,CAAC6I,SAAS,CAAC9N,EAAK5B,QAAQ,CAAE4B,EAAK3B,MAAM,EAEpC,IADlB6G,CAAAA,EAAY,IAAI,CAACoB,YAAY,CAACrB,EAAU,IAEpCD,EAAQ,IAAI,CAAC8E,eAAe,CAAC5E,EAAW,IAAI,CAACQ,CAAC,EAC9C,IAAI,CAACV,KAAK,CAAC,aAAchF,EAAMgF,EAAOC,EAAYC,IAIlE,CACA2F,aAAc,CAEV,IAAK,IAAMlN,KADG,IAAI,CAACJ,KAAK,CAEhBI,EAAKtE,aAAa,GAGtB,IAAI,CAACwQ,WAAW,CAACzD,SAAS,CAAC,IAAI,CAAEzI,GACjC,IAAI,CAACwQ,aAAa,CAACxQ,EAAM,IAAI,CAACuI,GAAG,EAEjCvI,EAAK2H,KAAK,CAAG,EACb3H,EAAK6H,KAAK,CAAG,EAErB,CAMA2I,cAAcxQ,CAAI,CAAEuI,CAAG,CAAE,CACrB,IAAMtF,EAASjD,EAAKiD,MAAM,AAkC1BjD,CAAAA,EAAKnE,KAAK,CAAGuP,EAAMpL,EAAKnE,KAAK,CAAE0M,EAAItD,IAAI,CAAGhC,EAAQsF,EAAI5D,KAAK,CAAG1B,GAE9DjD,EAAKlE,KAAK,CAAGsP,EAAMpL,EAAKlE,KAAK,CAAEyM,EAAIgB,GAAG,CAAGtG,EAAQsF,EAAIC,MAAM,CAAGvF,EAClE,CAMAkK,SAAS7O,CAAW,CAAEmS,CAAe,CAAE/E,CAAW,CAAE,CAehD,OAAOpN,EAAcmS,EAAkB/E,CAC3C,CACAnN,UAAW,CACP,OAAOlC,AAC2B,KAD3BA,KAAKC,GAAG,CAAC,IAAI,CAACgR,iBAAiB,CAClC,IAAI,CAACD,qBAAqB,GAAe,IAAI,CAAC/O,WAAW,EAAI,CACrE,CACAiP,sBAAuB,CACnB,IAAImD,EAAQ,EACZ,IAAK,IAAM1Q,KAAQ,IAAI,CAACJ,KAAK,CACzB8Q,GAAS1Q,EAAK1B,WAAW,CAE7B,OAAOoS,CACX,CACA/H,aAAagI,CAAM,CAAE,CACjB,OAAOtU,KAAKkS,IAAI,CAACoC,EAAOnL,CAAC,CAAGmL,EAAOnL,CAAC,CAAGmL,EAAO1O,CAAC,CAAG0O,EAAO1O,CAAC,CAC9D,CACA2O,SAASC,CAAK,CAAEC,CAAK,CAAE,CACnB,IAAMC,EAAW,IAAI,CAACZ,SAAS,CAACU,EAAOC,GACvC,OAAO,IAAI,CAACnI,YAAY,CAACoI,EAC7B,CACAZ,UAAUU,CAAK,CAAEC,CAAK,CAAE,CACpB,IAAME,EAAQH,EAAMhV,KAAK,CAAGiV,EAAMjV,KAAK,CAAEoV,EAAQJ,EAAM/U,KAAK,CAAGgV,EAAMhV,KAAK,CAC1E,MAAO,CACH0J,EAAGwL,EACH/O,EAAGgP,EACHC,KAAM7U,KAAKC,GAAG,CAAC0U,GACfG,KAAM9U,KAAKC,GAAG,CAAC2U,EACnB,CACJ,CACJ,CAgBA,GAAM,CAAE3R,MAAO8R,EAA+B,CAAEC,YAAAA,EAAW,CAAE,CAAI1X,IAE3D,CAAE2X,WAAAA,EAAU,CAAE,CAAI3X,IAsElB,CAAE4X,QAAAA,EAAO,CAAE,CAAI5X,IACf,CAAEI,SAAUyX,EAAiB,CAAElS,MAAOmS,EAAc,CAAEC,UAAAA,EAAS,CAAEvS,QAASwS,EAAgB,CAAEvS,OAAQwS,EAAe,CAAE,CAAIjY,IAyB/H,SAASkY,GAAYhM,CAAI,CAAEiM,CAAe,EAEtCA,EAAkBL,GAAe,CAAA,EAAM,CACnCM,QAAS,CAAA,EACTC,WAAY,CACRC,GAAI,GACJC,YAAa,MACbC,WAAY,QAChB,CACJ,EAAGL,GACH,IAAMM,EAAM,IAAI,CAACtL,QAAQ,CAACsL,GAAG,CAAEC,EAAc,IAAI,CAACC,IAAI,EAAI,IAAI,CAAEC,EAAWF,EAAYE,QAAQ,CAAE,CAAEP,WAAAA,CAAU,CAAED,QAAAA,CAAO,CAAE,CAAGD,EAM3H,GALAjM,EAAOA,GAAS0M,GAAYA,EAAS1M,IAAI,CAErC0M,GACAA,EAASC,IAAI,GAEb3M,GAAQkM,EAAS,CACjB,IAAMS,EAAOhB,GAAkBa,EAAa,kBAAmB,AAACtX,IAC5D,GAAI8K,GAAQkM,EAAS,CAEjB,IAAIU,EAAa5M,EAAK3I,IAAI,CAAC,KACvB,CAACuV,GACD5M,EAAK3I,IAAI,CAAC,KAAMuV,EAAaf,MAGjC,IAAMgB,EAAc,CAGhBlN,EAAG,EACHvD,EAAG,CACP,EACI0P,GAAiBK,EAAWW,EAAE,IAC9BD,EAAYC,EAAE,CAAGX,EAAWW,EAAE,CAC9B,OAAOX,EAAWW,EAAE,EAEpBhB,GAAiBK,EAAWC,EAAE,IAC9BS,EAAYT,EAAE,CAAGD,EAAWC,EAAE,CAC9B,OAAOD,EAAWC,EAAE,EAExBI,EAAYnV,IAAI,CAACwV,GAEjB,IAAI,CAACxV,IAAI,CAAC,CAAE0V,UAAW,EAAG,GACtB,IAAI,CAACrK,GAAG,EACR,CAAA,IAAI,CAACA,GAAG,CAAG,IAAI,CAACA,GAAG,CAAC/I,OAAO,EAAC,EAGhC,IAAMqT,EAAW9X,EAAE6E,KAAK,CAAC8G,KAAK,CAAC,EAC/B3L,CAAAA,EAAE6E,KAAK,CAACK,MAAM,CAAG,EACjBlF,EAAE6E,KAAK,CAAC,EAAE,CAAG,CACTkT,QAAS,WACTd,WAAYJ,GAAgBI,EAAY,CACpC,cAAeA,EAAWG,UAAU,CACpCY,KAAM,CAAC,EAAEX,EAAI,CAAC,EAAEK,EAAW,CAAC,AAChC,GACAI,SAAAA,CACJ,CACJ,CACJ,EAEAR,CAAAA,EAAYE,QAAQ,CAAG,CAAE1M,KAAAA,EAAM2M,KAAAA,CAAK,CACxC,MAEIH,EAAYnV,IAAI,CAAC,CAAEyV,GAAI,EAAGV,GAAI,CAAE,GAChC,OAAOI,EAAYE,QAAQ,CAO/B,OALI,IAAI,CAACS,KAAK,GAEVX,EAAYY,SAAS,CAAG,GACxB,IAAI,CAACnM,QAAQ,CAACoM,SAAS,CAACb,IAErB,IAAI,AACf,CAWA,SAASc,GAAW3Y,CAAK,EACrB,IAAM4Y,EAAO5Y,EAAM4Y,IAAI,CAAEC,EAAK,IAAI,CAACpF,OAAO,EAAEqF,cAAc,YAC1D,GAAID,EAAI,CACJ,IAAME,EAAU,EAAE,CAAE,CAAEC,EAAAA,CAAC,CAAE5F,EAAAA,CAAC,CAAE,CAAG,IAAI,CAAC9G,QAAQ,CAAC2M,WAAW,CAAC,IAAI,CAACxF,OAAO,EAAGyF,EAAY9F,EAAI4F,EAAGG,EAAmB,AAAIC,OAAO,gEAEtG,KAAMC,EAAQR,EAC5BS,SAAS,CACTC,OAAO,CAACJ,EAAkB,IAC1BK,KAAK,CAAC,sCAAuCC,EAAaJ,EAAM5T,MAAM,CAIrEiU,EAAqB,CAACC,EAAWC,KACnC,GAAM,CAAE5O,EAAAA,CAAC,CAAEvD,EAAAA,CAAC,CAAE,CAAGmS,EAAgBC,EAAW,AAAChB,CAAAA,EAAGiB,iBAAiB,CAACH,GAAa,EAAC,EAAK5C,GAASgD,EAASlY,KAAKgT,GAAG,CAACgF,GAAWG,EAASnY,KAAKiT,GAAG,CAAC+E,GAC7I,MAAO,CACH,CACI7O,EAAIkO,EAAYa,EAChBtS,EAAIyR,EAAYc,EACnB,CACD,CACIhP,EAAIgO,EAAIe,EACRtS,EAAIuR,EAAIgB,EACX,CACJ,AACL,EACA,IAAK,IAAI9R,EAAI,EAAG+R,EAAY,EAAGA,EAAYR,EAAYQ,IAAa,CAChE,IAA+BC,EAAUC,AAA5Bd,CAAK,CAACY,EAAU,CAAiBxU,MAAM,CACpD,IAAK,IAAI2U,EAAgB,EAAGA,EAAgBF,EAASE,GAAiB,EAClE,GAAI,CACA,IAAMC,EAAgBnS,EAClBkS,EACAH,EAAY,CAACK,EAAOC,EAAM,CAAGb,EAAmBW,EAAcxB,EAAG2B,sBAAsB,CAACH,GACxFD,AAAkB,CAAA,IAAlBA,GACArB,EAAQ7R,IAAI,CAACqT,GACbxB,EAAQ7R,IAAI,CAACoT,KAGTL,AAAc,IAAdA,GACAlB,EAAQ0B,OAAO,CAACF,GAEhBN,IAAcR,EAAa,GAC3BV,EAAQ7R,IAAI,CAACoT,GAGzB,CACA,MAAO/Z,EAAG,CAGN,KACJ,CAEJ2H,GAAKgS,EAAU,EACf,GAAI,CACA,IAAMG,EAAenS,EAAI+R,EAAWS,EAAU7B,EAAG8B,oBAAoB,CAACN,GAAe,CAACC,EAAOC,EAAM,CAAGb,EAAmBW,EAAcK,GACvI3B,EAAQ0B,OAAO,CAACF,GAChBxB,EAAQ0B,OAAO,CAACH,EACpB,CACA,MAAO/Z,EAAG,CAGN,KACJ,CACJ,CAEIwY,EAAQtT,MAAM,EACdsT,EAAQ7R,IAAI,CAAC6R,CAAO,CAAC,EAAE,CAAC7M,KAAK,IAEjC0M,EAAKG,OAAO,CAAGA,CACnB,CACA,OAAOH,CACX,CAWA,SAASgC,GAAa5a,CAAK,EACvB,IAAM6a,EAAe7a,EAAM6a,YAAY,CAAEhb,EAAQG,EAAMH,KAAK,CAAEyX,EAAmBuD,CAAY,CAAChb,EAAMwI,YAAY,CAAG,WAAW,EAC1HwS,EAAa9C,QAAQ,CACrBT,GAAmB,CAACuD,EAAaC,OAAO,GACxC,IAAI,CAACzD,WAAW,CAACxX,EAAMkb,gBAAgB,GAAG,IAAI,GAAKlb,EAAMwG,OAAO,CAAEiR,GAC9DzX,EAAMmb,aAAa,EACnB,CAAC1D,EAAgBC,OAAO,EAExB1X,CAAAA,EAAMmb,aAAa,CAAInb,EAAMmb,aAAa,CAAChW,OAAO,EAAE,EAGhE,CA+BA,GAAM,CAAEiW,KAAAA,EAAI,CAAE,CAAI9b,IAMZ,CAAEe,OAAQgb,EAAM,CAAEC,YAAa,CAAEC,OAAQ,CAAEvc,UAAWwc,EAAW,CAAE,CAAElB,KAAM,CAAEtb,UAAWyc,EAAS,CAAE,CAAE,CAAE,CAAI/W,IAE3G,CAAEgX,eAAgBC,EAAiC,CAAEC,oBAAqBC,EAAsC,CAAE,CApQ3F,CACzBH,eArBJ,WACI,IAAqBI,EAAYzb,AAAlB,IAAI,CAAqBE,OAAO,CAACoL,UAAU,CAC1D,GAAI,CAACtL,AADU,IAAI,CACP0b,eAAe,CAAE,CACzB,IAAMA,EAAkB,IAAI,CAACC,mBAAmB,GAWhD,MARI,CAAC3b,AALM,IAAI,CAKHJ,KAAK,CAACiJ,UAAU,EAAI4S,GAAWG,OACvCF,EAAgBrS,GAAG,CAACoS,EAAUG,KAAK,EAGvCF,EAAgBlZ,IAAI,CAAC,CAAE6H,QAAS,CAAE,GAC9BrK,AAVO,IAAI,CAUJ6b,OAAO,EACdH,EAAgBI,IAAI,GAEjBJ,CACX,CAGA,OADA1b,AAhBe,IAAI,CAgBZ0b,eAAe,CAAClZ,IAAI,CAACkU,GAAgC,CAAErM,QAAS,CAAE,EAAG,IAAI,CAAC0R,UAAU,CAAC,iBACrF/b,AAjBQ,IAAI,CAiBL0b,eAAe,AACjC,EAGIH,oBA5CJ,WACI,IAAME,EAAY,IAAI,CAACvb,OAAO,CAACoL,UAAU,AAKrC,CAACmQ,GAAWO,OACX,IAAI,CAAC9b,OAAO,CAAC+b,eAAe,EAAE9Z,iBAI/BwU,GAAY,KACR,IAAI,CAACuF,eAAe,CAAG,CAAA,CAC3B,EAAGT,EAAY7E,GAAW6E,EAAUpV,SAAS,EAAE2V,KAAK,CAAG,GALvD,IAAI,CAACE,eAAe,CAAG,CAAA,CAO/B,CA8BA,EAmQM,CAAE7c,SAAU8c,EAA2B,CAAE1X,QAAS2X,EAA0B,CAAE1X,OAAQ2X,EAAyB,CAAEzX,MAAO0X,EAAwB,CAAEzX,KAAM0X,EAAuB,CAAE,CAAItd,IAE3Lud,AAlCiB,CAAA,CACb9b,QATJ,SAA0B+b,CAAe,EACrC3F,GAAkB2F,EAAiB,eAAgBhE,IACnD3B,GAAkB2F,EAAiB,wBAAyB/B,IAC5D,IAAMgC,EAAkBD,EAAgB9d,SAAS,AAC7C,AAAC+d,CAAAA,EAAgBvF,WAAW,EAC5BuF,CAAAA,EAAgBvF,WAAW,CAAGA,EAAU,CAEhD,CAGA,CAAA,EAgCoBzW,OAAO,CAAEvB,IAa7B,OAAMwd,WAA2B3B,GAC7BnQ,aAAc,CAMV,KAAK,IAAIzF,WACT,IAAI,CAAC8W,eAAe,CAAG,CAAA,CAC3B,CAMA,OAAOxb,QAAQC,CAAU,CAAE,CACvBF,EAA4BC,OAAO,CAACC,GACpCic,AAnWqE7L,GAmW9BrQ,OAAO,CAACC,EACnD,CAiBAkc,aAAc,CACV,IAAMC,EAAgB,IAAI,CAAC5c,OAAO,CAAC+b,eAAe,CAAEc,EAAe,IAAI,CAACnd,KAAK,CAACM,OAAO,CAACN,KAAK,CACvFqC,EAAQ+a,EAAsB,IAAI,CAACpd,KAAK,CAACod,mBAAmB,CAAEtb,EAAqB,IAAI,CAAC9B,KAAK,CAAC8B,kBAAkB,AAC/G,CAAA,IAAI,CAACma,OAAO,GAGZmB,IACD,IAAI,CAACpd,KAAK,CAACod,mBAAmB,CAAGA,EAAsB,CAAC,EACxD,IAAI,CAACpd,KAAK,CAAC8B,kBAAkB,CAAGA,EAAqB,EAAE,EAE3DO,CAAAA,EAAS+a,CAAmB,CAACF,EAAcG,IAAI,CAAC,AAAD,IAE3CH,EAAc3a,gBAAgB,CAC1B,AAACia,GAA2BW,EAAaG,SAAS,EAE9C,CAACH,EAAaG,SAAS,CADvBJ,EAAc3a,gBAAgB,CAEtC6a,CAAmB,CAACF,EAAcG,IAAI,CAAC,CAAGhb,EACtC,IAAIgC,EAA8BE,OAAO,CAAC2Y,EAAcG,IAAI,CAAC,CACjEhb,EAAOqP,IAAI,CAACwL,GACZpb,EAAmBoK,MAAM,CAAC7J,EAAOwE,KAAK,CAAE,EAAGxE,IAE/C,IAAI,CAACA,MAAM,CAAGA,EACdA,EAAO+Q,OAAO,CAAC,EAAG,EAAG,IAAI,CAACpT,KAAK,CAACud,SAAS,CAAE,IAAI,CAACvd,KAAK,CAACwd,UAAU,EAChEnb,EAAOmR,uBAAuB,CAAC,CAAC,IAAI,CAAC,CAAEnR,EAAOjC,MAAM,EACpDiC,EAAOmR,uBAAuB,CAAC,IAAI,CAAClO,KAAK,CAAEjD,EAAOiD,KAAK,EACvDjD,EAAOmR,uBAAuB,CAAC,IAAI,CAACnO,MAAM,CAAEhD,EAAOiP,KAAK,EAC5D,CAIApM,SAAU,CACF,IAAI,CAAC7C,MAAM,EACX,IAAI,CAACA,MAAM,CAAC0H,2BAA2B,CAAC,IAAI,CAAE,IAAI,CAAC1H,MAAM,CAACjC,MAAM,EAEpEiJ,EAAwBnE,OAAO,CAACjG,IAAI,CAAC,IAAI,CAC7C,CAMAwe,gBAAiB,KAOTxF,EAJJ,GAAI,IAAI,CAACqE,eAAe,CACpB,OAEJ,IAAMT,EAAY,IAAI,CAACvb,OAAO,CAACoL,UAAU,AAErCmQ,CAAAA,GAAW5D,UACXA,CAAAA,EAAW4D,EAAU5D,QAAQ,AAAD,EAGhCmD,GAAOrc,SAAS,CAAC0e,cAAc,CAACxe,IAAI,CAAC,IAAI,CAAE,IAAI,CAACqG,KAAK,EAEjDuW,GAAW6B,cAEX7B,CAAAA,EAAU5D,QAAQ,CAAG4D,EAAU6B,YAAY,AAAD,EAE9CtC,GAAOrc,SAAS,CAAC0e,cAAc,CAACxe,IAAI,CAAC,IAAI,CAAE,IAAI,CAACkG,IAAI,EAEhD0W,GAAW5D,UACX4D,CAAAA,EAAU5D,QAAQ,CAAGA,CAAO,CAEpC,CAMArP,gBAAiB,CACb,IAAIlD,EAAM0C,EAYV,IAXAiB,EAAwBT,cAAc,CAACrD,KAAK,CAAC,IAAI,CAAEC,WAG/C,IAAI,CAAClF,OAAO,CAACgF,KAAK,EAClB,IAAI,CAAChF,OAAO,CAACgF,KAAK,CAAClD,OAAO,CAAC,SAAUub,CAAW,EACxC,AAAC,IAAI,CAAC9U,UAAU,CAAC8U,EAAYzW,EAAE,CAAC,EAChC,CAAA,IAAI,CAAC2B,UAAU,CAAC8U,EAAYzW,EAAE,CAAC,CAC3B,IAAI,CAACK,UAAU,CAACoW,EAAYzW,EAAE,CAAA,CAE1C,EAAG,IAAI,EAENkB,EAAI,IAAI,CAAC9C,KAAK,CAACK,MAAM,CAAG,EAAGyC,GAAK,EAAGA,IAEpC1C,AADAA,CAAAA,EAAO,IAAI,CAACJ,KAAK,CAAC8C,EAAE,AAAD,EACdkF,MAAM,CAAG5H,EAAKsE,SAAS,GAC5BtE,EAAKiD,MAAM,CAAGgU,GAAwBjX,EAAKgD,MAAM,EAAIhD,EAAKgD,MAAM,CAACC,MAAM,CAAE,IAAI,CAACrI,OAAO,CAACoI,MAAM,EAAI,IAAI,CAACpI,OAAO,CAACoI,MAAM,CAACC,MAAM,CAAE,GAC5HjD,EAAKnH,GAAG,CAAGmH,EAAK8C,IAAI,CAGhB,AAAC,IAAI,CAACK,UAAU,CAACnD,EAAKwB,EAAE,CAAC,EACzBxB,EAAKoG,MAAM,GAGnB,IAAI,CAAC3G,IAAI,CAAC/C,OAAO,CAAC,SAAU2F,CAAI,EAC5BA,EAAKQ,YAAY,CAAG,MACxB,GACA,IAAI,CAACqV,aAAa,EACtB,CAMAC,qBAAsB,CAClB,OAAO,IAAI,CAACvY,KAAK,EAAI,EAAE,AAC3B,CAOAsY,eAAgB,CACZ,IAAI,CAACtY,KAAK,CAAClD,OAAO,CAAC,SAAUsD,CAAI,CAAEmB,CAAK,EACpCnB,EAAKmB,KAAK,CAAGA,CACjB,EACJ,CAMA6K,KAAK1R,CAAK,CAAEM,CAAO,CAAE,CAsBjB,OArBA,KAAK,CAACoR,KAAK1R,EAAOM,GAClBsb,GAAuC3c,IAAI,CAAC,IAAI,EAChDsd,GAA4B,IAAI,CAAE,cAAe,KACzC,IAAI,CAACla,MAAM,EACX,IAAI,CAACA,MAAM,CAACoB,IAAI,EAExB,GACA8Y,GAA4B,IAAI,CAAE,cAAe,KAC7C,IAAI,CAACjX,KAAK,CAAClD,OAAO,CAAC,AAACsD,IACZA,GAAQA,EAAKtF,MAAM,EACnBsF,EAAKoY,YAAY,EAEzB,EACJ,GAIAvB,GAA4B,IAAI,CAAE,kBAAmB,WACjD,IAAI,CAACD,eAAe,CAAG,CAAA,EACvB,IAAI,CAACmB,cAAc,EACvB,GACO,IAAI,AACf,CAOAM,cAAche,CAAK,CAAE8F,CAAK,CAAE,CACxB,IAAMyF,EAAU8P,GAAOrc,SAAS,CAACgf,aAAa,CAAC9e,IAAI,CAAC,IAAI,CAAEc,EAAO8F,GAMjE,OAJI,AAAC2W,GAA2Bzc,EAAMyB,KAAK,GACvC8J,CAAAA,EAAQ3D,CAAC,CAAG,CAAA,EAEhB2D,EAAQJ,CAAC,CAAG,AAACnL,CAAAA,EAAMwB,KAAK,EAAI,CAAA,EAAK,AAAC+J,CAAAA,EAAQjB,KAAK,EAAI,CAAA,EAAK,EACjDiB,CACX,CAKAG,aAAa1L,CAAK,CAAE8F,CAAK,CAAE,CAEvB,IAAMmY,EAAanY,GAAS9F,GAASA,EAAM8F,KAAK,EAAI,SAAUoY,EAAe,IAAI,CAAC3d,OAAO,CAACwC,MAAM,CAACkb,EAAW,CACxG1S,EAAU8P,GAAOrc,SAAS,CAAC0M,YAAY,CAACxM,IAAI,CAAC,IAAI,CAAEc,EAAOie,GAe9D,OAdIje,GAAS,CAACA,EAAMiG,MAAM,GACtBsF,EAAUvL,EAAMmK,iBAAiB,GAE7B+T,GACA3S,CAAAA,EAAU,CAENhB,OAAQ2T,EAAaC,SAAS,EAAI5S,EAAQhB,MAAM,CAChDC,UAAY0T,EAAaE,aAAa,EAAI7S,EAAQf,SAAS,CAC3DE,QAASkS,GAAwBsB,EAAaG,WAAW,CAAE9S,EAAQb,OAAO,EAC1E,eAAgBwT,EAAaC,SAAS,EAClC5S,CAAO,CAAC,eAAe,AAC/B,CAAA,GAGDA,CACX,CAMAlH,QAAS,CACL,IAAqBiB,EAASjF,AAAf,IAAI,CAAkBiF,MAAM,CAAElF,EAAaC,AAA3C,IAAI,CAA8CJ,KAAK,CAACG,UAAU,CAAEuL,EAAa,EAAE,AAElGtL,CAFe,IAAI,CAEZiF,MAAM,CAAGjF,AAFD,IAAI,CAEIkF,KAAK,CAC5BkW,GAAUpX,MAAM,CAACnF,IAAI,CAAC,IAAI,EAC1BmB,AAJe,IAAI,CAIZiF,MAAM,CAAGA,EAChBA,EAAOjD,OAAO,CAAC,SAAUrC,CAAK,EACtBA,EAAMoG,QAAQ,EAAIpG,EAAMqG,MAAM,GAC9BrG,EAAMwM,UAAU,GAChBxM,EAAMsL,UAAU,GAExB,GACIlL,GAAcA,EAAWC,MAAM,GAXpB,IAAI,EAYfA,AAZW,IAAI,CAYR+B,UAAU,CAAChC,GAElBC,AAdW,IAAI,CAcRJ,KAAK,CAACqe,WAAW,EACxB,CAACje,AAfU,IAAI,CAePE,OAAO,CAACoL,UAAU,CAAC4S,YAAY,GACvCle,AAhBW,IAAI,CAgBRkF,KAAK,CAACF,MAAM,CAAChF,AAhBT,IAAI,CAgBYiF,MAAM,EAAEjD,OAAO,CAAC,SAAUsD,CAAI,EACjDA,EAAK6Y,SAAS,EACd7S,EAAWtE,IAAI,CAAC1B,EAAK6Y,SAAS,CAEtC,GACAne,AArBW,IAAI,CAqBRJ,KAAK,CAACwe,qBAAqB,CAAC9S,GAE3C,CAMApF,SAAST,CAAK,CAAE4Y,CAAO,CAAE,CACjBA,GACA,IAAI,CAACpZ,MAAM,CAAG,IAAI,CAACC,KAAK,CAACF,MAAM,CAAC,IAAI,CAACD,IAAI,EACzCiW,GAAOrc,SAAS,CAACuH,QAAQ,CAACf,KAAK,CAAC,IAAI,CAAEC,WACtC,IAAI,CAACH,MAAM,CAAG,IAAI,CAACF,IAAI,EAGvBiW,GAAOrc,SAAS,CAACuH,QAAQ,CAACf,KAAK,CAAC,IAAI,CAAEC,WAGtC,AAAC,IAAI,CAACnD,MAAM,CAACkP,UAAU,EAAK1L,GAC5B,IAAI,CAACzB,MAAM,EAEnB,CAKAsa,WAAY,CACR,IAAI,CAAC9V,cAAc,GACnB,IAAI,CAACqU,WAAW,GAChB,IAAI,CAAC3X,KAAK,CAAClD,OAAO,CAAC,SAAUsD,CAAI,EAE7BA,EAAKiZ,QAAQ,CAAG,CAAA,EAChBjZ,EAAKQ,SAAS,CAAC9D,OAAO,CAAC,SAAUrC,CAAK,EAClCA,EAAM6e,SAAS,CAAG,OAElB7e,EAAM4H,CAAC,CAAG,CACd,EACJ,EACJ,CACJ,CACAoV,GAAmB8B,cAAc,CAAGnC,GAAyBtB,GAAOyD,cAAc,CAp6E/C,CAC/BC,eAAgB,CAAA,EAMhBC,oBAAqB,CAAA,EACrBrW,OAAQ,CACJ+O,QAAS,CAAA,EACT3U,OAAQ,CAOJkc,SAAU,CAINvU,QAAS,GAMThE,UAAW,CAEPwY,SAAU,EACd,CACJ,CACJ,CACJ,EACAnc,OAAQ,CAOJkc,SAAU,CAINZ,YAAa,GAMb3X,UAAW,CAEPwY,SAAU,EACd,CACJ,CACJ,EAeAvT,WAAY,CAoBRwT,UAAW,WACP,OAAOC,OAAO,IAAI,CAAC5gB,GAAG,EAAI,GAC9B,EAmBA6gB,cAAe,WACX,OAAQ,IAAI,CAACjZ,QAAQ,CAACqC,IAAI,CACtB,OACA,IAAI,CAACpC,MAAM,CAACoC,IAAI,AACxB,EAYAkV,aAAc,CACVjG,QAAS,CAAA,CACb,EACAQ,SAAU,CACNR,QAAS,CAAA,CACb,EACAuE,MAAO,CACHqD,WAAY,gBAChB,EACAjD,MAAO,CAAA,EACP3V,UAAW,CACP2V,MAAO,GACX,CACJ,EAKArU,KAAM,CAiBFoB,MAAO,2BAIPkB,MAAO,CACX,EAKA9J,UAAW,CAAA,EACX8b,gBAAiB,CAiEbnI,iBAAkB,SAUlBQ,sBAAuB,EASvBnS,iBAAkB,CAAA,EAalBuT,MAAO,GAcPzF,SAAU,GAqBV0B,cAAe,OAOfsL,KAAM,uBAsBNzL,YAAa,QAOb9N,cAAe,IAQf6J,sBAAuB,MAKvBS,SAAU,KACd,EACAkR,aAAc,CAAA,CAClB,GA+jEA7C,GAA0BM,GAAmBhe,SAAS,CAAE,CACpD4F,WA/8EiEkF,EAg9EjE+B,QAAS,KAAK,EACd2T,YAAa,CAAA,EACbC,UAAW,KAAK,EAChBvN,OAAQ,CAAC,aAAc,YAAa,aAAa,CACjD5R,kBAAmB,CAAA,EACnBof,YAAa,CAAA,EACbC,gBAAiB,CAAA,EACjBC,cAAe,CAAC,OAAQ,KAAK,CAC7BC,eAAgB,CAAA,EAChBC,cAAe,CAAC,QAAS,cAAe,kBAAkB,CAC1DpE,eAAgBC,GAChBoE,YAAa3E,GACb5T,WAAY8B,EAAwB9B,UAAU,CAC9CwY,YAAaxE,GAAYwE,WAAW,CACpCvf,YAAaK,EAA4BL,WAAW,CACpDE,YAAaG,EAA4BH,WAAW,CACpDE,UAAWC,EAA4BD,SAAS,CAChDuB,WAAYtB,EAA4BsB,UAAU,AACtD,GACAsC,IAA0Iub,kBAAkB,CAAC,eAAgBjD,IA+B7K,IAAMkD,GAAK5gB,IACX6gB,AA1BsEnD,GA0BtCjc,OAAO,CAACmf,GAAEE,KAAK,EAClB,IAAMhhB,GAAqBE,IAG9C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}