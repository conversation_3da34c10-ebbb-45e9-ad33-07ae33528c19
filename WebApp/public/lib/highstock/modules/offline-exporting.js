!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/offline-exporting
 * @requires highcharts
 * @requires highcharts/modules/exporting
 *
 * Client side exporting module
 *
 * (c) 2015-2025 Torstein Honsi / Oystein Moseng
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.AST,t._Highcharts.Chart):"function"==typeof define&&define.amd?define("highcharts/modules/offline-exporting",["highcharts/highcharts"],function(t){return e(t,t.AST,t.Chart)}):"object"==typeof exports?exports["highcharts/modules/offline-exporting"]=e(t._Highcharts,t._Highcharts.AST,t._Highcharts.Chart):t.Highcharts=e(t.Highcharts,t.Highcharts.AST,t.Highcharts.Chart)}("undefined"==typeof window?this:window,(t,e,o)=>(()=>{"use strict";var r,n={660:t=>{t.exports=e},944:e=>{e.exports=t},960:t=>{t.exports=o}},a={};function i(t){var e=a[t];if(void 0!==e)return e.exports;var o=a[t]={exports:{}};return n[t](o,o.exports,i),o.exports}i.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return i.d(e,{a:e}),e},i.d=(t,e)=>{for(var o in e)i.o(e,o)&&!i.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var l={};i.d(l,{default:()=>N});var s=i(944),d=i.n(s);let{isSafari:c,win:f,win:{document:h}}=d(),{error:p}=d(),u=f.URL||f.webkitURL||f;function g(t){let e=t.replace(/filename=.*;/,"").match(/data:([^;]*)(;base64)?,([A-Z+\d\/]+)/i);if(e&&e.length>3&&f.atob&&f.ArrayBuffer&&f.Uint8Array&&f.Blob&&u.createObjectURL){let t=f.atob(e[3]),o=new f.ArrayBuffer(t.length),r=new f.Uint8Array(o);for(let e=0;e<r.length;++e)r[e]=t.charCodeAt(e);return u.createObjectURL(new f.Blob([r],{type:e[1]}))}}let y={dataURLtoBlob:g,downloadURL:function(t,e){let o=f.navigator,r=h.createElement("a");if("string"!=typeof t&&!(t instanceof String)&&o.msSaveOrOpenBlob)return void o.msSaveOrOpenBlob(t,e);if(t=""+t,o.userAgent.length>1e3)throw Error("Input too long");let n=/Edge\/\d+/.test(o.userAgent);if((c&&"string"==typeof t&&0===t.indexOf("data:application/pdf")||n||t.length>2e6)&&!(t=g(t)||""))throw Error("Failed to convert to blob");if(void 0!==r.download)r.href=t,r.download=e,h.body.appendChild(r),r.click(),h.body.removeChild(r);else try{if(!f.open(t,"chart"))throw Error("Failed to open window")}catch{f.location.href=t}},getScript:function(t){return new Promise((e,o)=>{let r=h.getElementsByTagName("head")[0],n=h.createElement("script");n.type="text/javascript",n.src=t,n.onload=()=>{e()},n.onerror=()=>{o(p(`Error loading script ${t}`))},r.appendChild(n)})}};var w=i(660),m=i.n(w),b=i(960),x=i.n(b);let v={exporting:{}},{getOptions:F,setOptions:E}=d(),{downloadURL:S,getScript:A}=y,{composed:L,doc:j,win:C}=d(),{addEvent:B,extend:H,pushUnique:U}=d();!function(t){async function e(t,e,n,a){let i=function(t,e){let o,r,n=j.createElement("div");m().setElementHTML(n,t);let a=n.getElementsByTagName("text"),i=function(t,e){let o=t;for(;o&&o!==n;){if(o.style[e]){let r=o.style[e];"fontSize"===e&&/em$/.test(r)&&(r=Math.round(16*parseFloat(r))+"px"),t.style[e]=r;break}o=o.parentNode}};return[].forEach.call(a,function(t){for(["fontFamily","fontSize"].forEach(e=>{i(t,e)}),t.style.fontFamily=e?.normal?"HighchartsFont":String(t.style.fontFamily&&t.style.fontFamily.split(" ").splice(-1)),o=t.getElementsByTagName("title"),[].forEach.call(o,function(e){t.removeChild(e)}),r=t.getElementsByClassName("highcharts-text-outline");r.length>0;){let t=r[0];t.parentNode&&t.parentNode.removeChild(t)}}),n.querySelector("svg")}(t,a);i&&(await o(i,a),S(await r(i,0,e),n))}async function o(t,e){let o,r,n=(t,e)=>{C.jspdf.jsPDF.API.events.push(["initialized",function(){this.addFileToVFS(t,e),this.addFont(t,"HighchartsFont",t),this.getFontList()?.HighchartsFont||this.setFont("HighchartsFont")}])};for(let a of(e&&(r=t.textContent||"",!/[^\u0000-\u007F\u200B]+/.test(r))&&(e=void 0),["normal","italic","bold","bolditalic"])){let t=e?.[a];if(t)try{let e=await C.fetch(t);if(!e.ok)throw Error(`Failed to fetch font: ${t}`);let r=await e.blob(),i=new FileReader,l=await new Promise((t,e)=>{i.onloadend=()=>{"string"==typeof i.result?t(i.result.split(",")[1]):e(Error("Failed to read font as base64"))},i.onerror=e,i.readAsDataURL(r)});n(a,l),"normal"===a&&(o=l)}catch(t){}else o&&n(a,o)}}async function r(t,e,o){let r=(Number(t.getAttribute("width"))+2*e)*o,n=(Number(t.getAttribute("height"))+2*e)*o,a=new C.jspdf.jsPDF(n>r?"p":"l","pt",[r,n]);[].forEach.call(t.querySelectorAll('*[visibility="hidden"]'),function(t){t.parentNode.removeChild(t)});let i=t.querySelectorAll("linearGradient");for(let t=0;t<i.length;t++){let e=i[t].querySelectorAll("stop"),o=0;for(;o<e.length&&"0"===e[o].getAttribute("offset")&&"0"===e[o+1].getAttribute("offset");)e[o].remove(),o++}return[].forEach.call(t.querySelectorAll("tspan"),t=>{"​"===t.textContent&&(t.textContent=" ",t.setAttribute("dx",-5))}),await a.svg(t,{x:0,y:0,width:r,height:n,removeInvalid:!0}),a.output("datauristring")}t.compose=function(t){if(B(t,"downloadSVG",async function(t){let{svg:o,exportingOptions:r,exporting:n,preventDefault:a}=t;if(r?.type==="application/pdf"){a?.();try{let{type:t,filename:n,scale:a,libURL:i}=d().Exporting.prepareImageOptions(r);"application/pdf"===t&&(C.jspdf?.jsPDF||(await A(`${i}jspdf.js`),await A(`${i}svg2pdf.js`)),await e(o,a,n,r?.pdfFont))}catch(t){await n?.fallbackToServer(r,t)}}}),!U(L,"OfflineExporting"))return;H(x().prototype,{exportChartLocal:async function(t,e){await this.exporting?.exportChart(t,e)}}),E(v);let o=F().exporting?.buttons?.contextButton?.menuItems;o&&o.push("downloadPDF")},t.downloadSVGLocal=async function(t,e){await d().Exporting.prototype.downloadSVG.call(void 0,t,e)}}(r||(r={}));let R=r,O=d();O.dataURLtoBlob=O.dataURLtoBlob||y.dataURLtoBlob,O.downloadSVGLocal=R.downloadSVGLocal,O.downloadURL=O.downloadURL||y.downloadURL,R.compose(O.Exporting);let N=d();return l.default})());