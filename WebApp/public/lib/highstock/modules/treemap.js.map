{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/treemap\n * @requires highcharts\n *\n * (c) 2014-2025 Highsoft AS\n * Authors: <AUTHORS>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Templating\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"SVGElement\"], root[\"_Highcharts\"][\"Series\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/treemap\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"Templating\"],amd1[\"Color\"],amd1[\"SeriesRegistry\"],amd1[\"SVGElement\"],amd1[\"Series\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/treemap\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Templating\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"SVGElement\"], root[\"_Highcharts\"][\"Series\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Templating\"], root[\"Highcharts\"][\"Color\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"SVGElement\"], root[\"Highcharts\"][\"Series\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__984__, __WEBPACK_EXTERNAL_MODULE__620__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__28__, __WEBPACK_EXTERNAL_MODULE__820__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 28:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__28__;\n\n/***/ }),\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 620:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__620__;\n\n/***/ }),\n\n/***/ 820:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__820__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ }),\n\n/***/ 984:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__984__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ treemap_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Extensions/Breadcrumbs/BreadcrumbsDefaults.js\n/* *\n *\n *  Highcharts Breadcrumbs module\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n/**\n * @optionparent lang\n */\nconst lang = {\n    /**\n     * @since   10.0.0\n     * @product highcharts highmaps\n     *\n     * @private\n     */\n    mainBreadcrumb: 'Main'\n};\n/**\n * Options for breadcrumbs. Breadcrumbs general options are defined in\n * `navigation.breadcrumbs`. Specific options for drilldown are set in\n * `drilldown.breadcrumbs` and for tree-like series traversing, in\n * `plotOptions[series].breadcrumbs`.\n *\n * @since        10.0.0\n * @product      highcharts\n * @optionparent navigation.breadcrumbs\n */\nconst options = {\n    /**\n     * A collection of attributes for the buttons. The object takes SVG\n     * attributes like `fill`, `stroke`, `stroke-width`, as well as `style`,\n     * a collection of CSS properties for the text.\n     *\n     * The object can also be extended with states, so you can set\n     * presentational options for `hover`, `select` or `disabled` button\n     * states.\n     *\n     * @sample {highcharts} highcharts/breadcrumbs/single-button\n     *         Themed, single button\n     *\n     * @type    {Highcharts.SVGAttributes}\n     * @since   10.0.0\n     * @product highcharts\n     */\n    buttonTheme: {\n        /** @ignore */\n        fill: 'none',\n        /** @ignore */\n        height: 18,\n        /** @ignore */\n        padding: 2,\n        /** @ignore */\n        'stroke-width': 0,\n        /** @ignore */\n        zIndex: 7,\n        /** @ignore */\n        states: {\n            select: {\n                fill: 'none'\n            }\n        },\n        style: {\n            color: \"#334eff\" /* Palette.highlightColor80 */\n        }\n    },\n    /**\n     * The default padding for each button and separator in each direction.\n     *\n     * @type  {number}\n     * @since 10.0.0\n     */\n    buttonSpacing: 5,\n    /**\n     * Fires when clicking on the breadcrumbs button. Two arguments are\n     * passed to the function. First breadcrumb button as an SVG element.\n     * Second is the breadcrumbs class, containing reference to the chart,\n     * series etc.\n     *\n     * ```js\n     * click: function(button, breadcrumbs) {\n     *   console.log(button);\n     * }\n     * ```\n     *\n     * Return false to stop default buttons click action.\n     *\n     * @type      {Highcharts.BreadcrumbsClickCallbackFunction}\n     * @since     10.0.0\n     * @apioption navigation.breadcrumbs.events.click\n     */\n    /**\n     * When the breadcrumbs are floating, the plot area will not move to\n     * make space for it. By default, the chart will not make space for the\n     * buttons. This property won't work when positioned in the middle.\n     *\n     * @sample highcharts/breadcrumbs/single-button\n     *         Floating button\n     *\n     * @type  {boolean}\n     * @since 10.0.0\n     */\n    floating: false,\n    /**\n     * A format string for the breadcrumbs button. Variables are enclosed by\n     * curly brackets. Available values are passed in the declared point\n     * options.\n     *\n     * @type      {string|undefined}\n     * @since 10.0.0\n     * @default   undefined\n     * @sample {highcharts} highcharts/breadcrumbs/format Display custom\n     *          values in breadcrumb button.\n     */\n    format: void 0,\n    /**\n     * Callback function to format the breadcrumb text from scratch.\n     *\n     * @type      {Highcharts.BreadcrumbsFormatterCallbackFunction}\n     * @since     10.0.0\n     * @default   undefined\n     * @apioption navigation.breadcrumbs.formatter\n     */\n    /**\n     * What box to align the button to. Can be either `plotBox` or\n     * `spacingBox`.\n     *\n     * @type    {Highcharts.ButtonRelativeToValue}\n     * @default plotBox\n     * @since   10.0.0\n     * @product highcharts highmaps\n     */\n    relativeTo: 'plotBox',\n    /**\n     * Whether to reverse the order of buttons. This is common in Arabic\n     * and Hebrew.\n     *\n     * @sample {highcharts} highcharts/breadcrumbs/rtl\n     *         Breadcrumbs in RTL\n     *\n     * @type  {boolean}\n     * @since 10.2.0\n     */\n    rtl: false,\n    /**\n     * Positioning for the button row. The breadcrumbs buttons will be\n     * aligned properly for the default chart layout (title,  subtitle,\n     * legend, range selector) for the custom chart layout set the position\n     * properties.\n     *\n     * @sample  {highcharts} highcharts/breadcrumbs/single-button\n     *          Single, right aligned button\n     *\n     * @type    {Highcharts.BreadcrumbsAlignOptions}\n     * @since   10.0.0\n     * @product highcharts highmaps\n     */\n    position: {\n        /**\n         * Horizontal alignment of the breadcrumbs buttons.\n         *\n         * @type {Highcharts.AlignValue}\n         */\n        align: 'left',\n        /**\n         * Vertical alignment of the breadcrumbs buttons.\n         *\n         * @type {Highcharts.VerticalAlignValue}\n         */\n        verticalAlign: 'top',\n        /**\n         * The X offset of the breadcrumbs button group.\n         *\n         * @type {number}\n         */\n        x: 0,\n        /**\n         * The Y offset of the breadcrumbs button group. When `undefined`,\n         * and `floating` is `false`, the `y` position is adapted so that\n         * the breadcrumbs are rendered outside the target area.\n         *\n         * @type {number|undefined}\n         */\n        y: void 0\n    },\n    /**\n     * Options object for Breadcrumbs separator.\n     *\n     * @since 10.0.0\n     */\n    separator: {\n        /**\n         * @type    {string}\n         * @since   10.0.0\n         * @product highcharts\n         */\n        text: '/',\n        /**\n         * CSS styles for the breadcrumbs separator.\n         *\n         * In styled mode, the breadcrumbs separators are styled by the\n         * `.highcharts-separator` rule with its different states.\n         *  @type  {Highcharts.CSSObject}\n         *  @since 10.0.0\n         */\n        style: {\n            color: \"#666666\" /* Palette.neutralColor60 */,\n            fontSize: '0.8em'\n        }\n    },\n    /**\n     * Show full path or only a single button.\n     *\n     * @sample {highcharts} highcharts/breadcrumbs/single-button\n     *         Single, styled button\n     *\n     * @type  {boolean}\n     * @since 10.0.0\n     */\n    showFullPath: true,\n    /**\n     * CSS styles for all breadcrumbs.\n     *\n     * In styled mode, the breadcrumbs buttons are styled by the\n     * `.highcharts-breadcrumbs-buttons .highcharts-button` rule with its\n     * different states.\n     *\n     * @type  {Highcharts.SVGAttributes}\n     * @since 10.0.0\n     */\n    style: {},\n    /**\n     * Whether to use HTML to render the breadcrumbs items texts.\n     *\n     * @type  {boolean}\n     * @since 10.0.0\n     */\n    useHTML: false,\n    /**\n     * The z index of the breadcrumbs group.\n     *\n     * @type  {number}\n     * @since 10.0.0\n     */\n    zIndex: 7\n};\n/* *\n *\n *  Default Export\n *\n * */\nconst BreadcrumbsDefaults = {\n    lang,\n    options\n};\n/* harmony default export */ const Breadcrumbs_BreadcrumbsDefaults = (BreadcrumbsDefaults);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Templating\"],\"commonjs\":[\"highcharts\",\"Templating\"],\"commonjs2\":[\"highcharts\",\"Templating\"],\"root\":[\"Highcharts\",\"Templating\"]}\nvar highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_ = __webpack_require__(984);\nvar highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default = /*#__PURE__*/__webpack_require__.n(highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_);\n;// ./code/es-modules/Extensions/Breadcrumbs/Breadcrumbs.js\n/* *\n *\n *  Highcharts Breadcrumbs module\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { format } = (highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default());\n\nconst { composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent, defined, extend, fireEvent, isString, merge, objectEach, pick, pushUnique } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Shift the drillUpButton to make the space for resetZoomButton, #8095.\n * @private\n */\nfunction onChartAfterShowResetZoom() {\n    const chart = this;\n    if (chart.breadcrumbs) {\n        const bbox = chart.resetZoomButton &&\n            chart.resetZoomButton.getBBox(), breadcrumbsOptions = chart.breadcrumbs.options;\n        if (bbox &&\n            breadcrumbsOptions.position.align === 'right' &&\n            breadcrumbsOptions.relativeTo === 'plotBox') {\n            chart.breadcrumbs.alignBreadcrumbsGroup(-bbox.width - breadcrumbsOptions.buttonSpacing);\n        }\n    }\n}\n/**\n * Remove resize/afterSetExtremes at chart destroy.\n * @private\n */\nfunction onChartDestroy() {\n    if (this.breadcrumbs) {\n        this.breadcrumbs.destroy();\n        this.breadcrumbs = void 0;\n    }\n}\n/**\n * Logic for making space for the buttons above the plot area\n * @private\n */\nfunction onChartGetMargins() {\n    const breadcrumbs = this.breadcrumbs;\n    if (breadcrumbs &&\n        !breadcrumbs.options.floating &&\n        breadcrumbs.level) {\n        const breadcrumbsOptions = breadcrumbs.options, buttonTheme = breadcrumbsOptions.buttonTheme, breadcrumbsHeight = ((buttonTheme.height || 0) +\n            2 * (buttonTheme.padding || 0) +\n            breadcrumbsOptions.buttonSpacing), verticalAlign = breadcrumbsOptions.position.verticalAlign;\n        if (verticalAlign === 'bottom') {\n            this.marginBottom = (this.marginBottom || 0) + breadcrumbsHeight;\n            breadcrumbs.yOffset = breadcrumbsHeight;\n        }\n        else if (verticalAlign !== 'middle') {\n            this.plotTop += breadcrumbsHeight;\n            breadcrumbs.yOffset = -breadcrumbsHeight;\n        }\n        else {\n            breadcrumbs.yOffset = void 0;\n        }\n    }\n}\n/**\n * @private\n */\nfunction onChartRedraw() {\n    this.breadcrumbs && this.breadcrumbs.redraw();\n}\n/**\n * After zooming out, shift the drillUpButton to the previous position, #8095.\n * @private\n */\nfunction onChartSelection(event) {\n    if (event.resetSelection === true &&\n        this.breadcrumbs) {\n        this.breadcrumbs.alignBreadcrumbsGroup();\n    }\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Breadcrumbs class\n *\n * @private\n * @class\n * @name Highcharts.Breadcrumbs\n *\n * @param {Highcharts.Chart} chart\n *        Chart object\n * @param {Highcharts.Options} userOptions\n *        User options\n */\nclass Breadcrumbs {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    static compose(ChartClass, highchartsDefaultOptions) {\n        if (pushUnique(composed, 'Breadcrumbs')) {\n            addEvent(ChartClass, 'destroy', onChartDestroy);\n            addEvent(ChartClass, 'afterShowResetZoom', onChartAfterShowResetZoom);\n            addEvent(ChartClass, 'getMargins', onChartGetMargins);\n            addEvent(ChartClass, 'redraw', onChartRedraw);\n            addEvent(ChartClass, 'selection', onChartSelection);\n            // Add language support.\n            extend(highchartsDefaultOptions.lang, Breadcrumbs_BreadcrumbsDefaults.lang);\n        }\n    }\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(chart, userOptions) {\n        this.elementList = {};\n        this.isDirty = true;\n        this.level = 0;\n        this.list = [];\n        const chartOptions = merge(chart.options.drilldown &&\n            chart.options.drilldown.drillUpButton, Breadcrumbs.defaultOptions, chart.options.navigation && chart.options.navigation.breadcrumbs, userOptions);\n        this.chart = chart;\n        this.options = chartOptions || {};\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Update Breadcrumbs properties, like level and list.\n     *\n     * @function Highcharts.Breadcrumbs#updateProperties\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    updateProperties(list) {\n        this.setList(list);\n        this.setLevel();\n        this.isDirty = true;\n    }\n    /**\n     * Set breadcrumbs list.\n     * @function Highcharts.Breadcrumbs#setList\n     *\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     * @param {Highcharts.BreadcrumbsOptions} list\n     *        Breadcrumbs list.\n     */\n    setList(list) {\n        this.list = list;\n    }\n    /**\n     * Calculate level on which chart currently is.\n     *\n     * @function Highcharts.Breadcrumbs#setLevel\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    setLevel() {\n        this.level = this.list.length && this.list.length - 1;\n    }\n    /**\n     * Get Breadcrumbs level\n     *\n     * @function Highcharts.Breadcrumbs#getLevel\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    getLevel() {\n        return this.level;\n    }\n    /**\n     * Default button text formatter.\n     *\n     * @function Highcharts.Breadcrumbs#getButtonText\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     * @param {Highcharts.Breadcrumbs} breadcrumb\n     *        Breadcrumb.\n     * @return {string}\n     *         Formatted text.\n     */\n    getButtonText(breadcrumb) {\n        const breadcrumbs = this, chart = breadcrumbs.chart, breadcrumbsOptions = breadcrumbs.options, lang = chart.options.lang, textFormat = pick(breadcrumbsOptions.format, breadcrumbsOptions.showFullPath ?\n            '{level.name}' : '← {level.name}'), defaultText = lang && pick(lang.drillUpText, lang.mainBreadcrumb);\n        let returnText = breadcrumbsOptions.formatter &&\n            breadcrumbsOptions.formatter(breadcrumb) ||\n            format(textFormat, { level: breadcrumb.levelOptions }, chart) || '';\n        if (((isString(returnText) &&\n            !returnText.length) ||\n            returnText === '← ') &&\n            defined(defaultText)) {\n            returnText = !breadcrumbsOptions.showFullPath ?\n                '← ' + defaultText :\n                defaultText;\n        }\n        return returnText;\n    }\n    /**\n     * Redraw.\n     *\n     * @function Highcharts.Breadcrumbs#redraw\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    redraw() {\n        if (this.isDirty) {\n            this.render();\n        }\n        if (this.group) {\n            this.group.align();\n        }\n        this.isDirty = false;\n    }\n    /**\n     * Create a group, then draw breadcrumbs together with the separators.\n     *\n     * @function Highcharts.Breadcrumbs#render\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    render() {\n        const breadcrumbs = this, chart = breadcrumbs.chart, breadcrumbsOptions = breadcrumbs.options;\n        // A main group for the breadcrumbs.\n        if (!breadcrumbs.group && breadcrumbsOptions) {\n            breadcrumbs.group = chart.renderer\n                .g('breadcrumbs-group')\n                .addClass('highcharts-no-tooltip highcharts-breadcrumbs')\n                .attr({\n                zIndex: breadcrumbsOptions.zIndex\n            })\n                .add();\n        }\n        // Draw breadcrumbs.\n        if (breadcrumbsOptions.showFullPath) {\n            this.renderFullPathButtons();\n        }\n        else {\n            this.renderSingleButton();\n        }\n        this.alignBreadcrumbsGroup();\n    }\n    /**\n     * Draw breadcrumbs together with the separators.\n     *\n     * @function Highcharts.Breadcrumbs#renderFullPathButtons\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    renderFullPathButtons() {\n        // Make sure that only one type of button is visible.\n        this.destroySingleButton();\n        this.resetElementListState();\n        this.updateListElements();\n        this.destroyListElements();\n    }\n    /**\n     * Render Single button - when showFullPath is not used. The button is\n     * similar to the old drillUpButton\n     *\n     * @function Highcharts.Breadcrumbs#renderSingleButton\n     * @param {Highcharts.Breadcrumbs} this Breadcrumbs class.\n     */\n    renderSingleButton() {\n        const breadcrumbs = this, chart = breadcrumbs.chart, list = breadcrumbs.list, breadcrumbsOptions = breadcrumbs.options, buttonSpacing = breadcrumbsOptions.buttonSpacing;\n        // Make sure that only one type of button is visible.\n        this.destroyListElements();\n        // Draw breadcrumbs. Initial position for calculating the breadcrumbs\n        // group.\n        const posX = breadcrumbs.group ?\n            breadcrumbs.group.getBBox().width :\n            buttonSpacing, posY = buttonSpacing;\n        const previousBreadcrumb = list[list.length - 2];\n        if (!chart.drillUpButton && (this.level > 0)) {\n            chart.drillUpButton = breadcrumbs.renderButton(previousBreadcrumb, posX, posY);\n        }\n        else if (chart.drillUpButton) {\n            if (this.level > 0) {\n                // Update button.\n                this.updateSingleButton();\n            }\n            else {\n                this.destroySingleButton();\n            }\n        }\n    }\n    /**\n     * Update group position based on align and it's width.\n     *\n     * @function Highcharts.Breadcrumbs#renderSingleButton\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    alignBreadcrumbsGroup(xOffset) {\n        const breadcrumbs = this;\n        if (breadcrumbs.group) {\n            const breadcrumbsOptions = breadcrumbs.options, buttonTheme = breadcrumbsOptions.buttonTheme, positionOptions = breadcrumbsOptions.position, alignTo = (breadcrumbsOptions.relativeTo === 'chart' ||\n                breadcrumbsOptions.relativeTo === 'spacingBox' ?\n                void 0 :\n                'plotBox'), bBox = breadcrumbs.group.getBBox(), additionalSpace = 2 * (buttonTheme.padding || 0) +\n                breadcrumbsOptions.buttonSpacing;\n            // Store positionOptions\n            positionOptions.width = bBox.width + additionalSpace;\n            positionOptions.height = bBox.height + additionalSpace;\n            const newPositions = merge(positionOptions);\n            // Add x offset if specified.\n            if (xOffset) {\n                newPositions.x += xOffset;\n            }\n            if (breadcrumbs.options.rtl) {\n                newPositions.x += positionOptions.width;\n            }\n            newPositions.y = pick(newPositions.y, this.yOffset, 0);\n            breadcrumbs.group.align(newPositions, true, alignTo);\n        }\n    }\n    /**\n     * Render a button.\n     *\n     * @function Highcharts.Breadcrumbs#renderButton\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     * @param {Highcharts.Breadcrumbs} breadcrumb\n     *        Current breadcrumb\n     * @param {Highcharts.Breadcrumbs} posX\n     *        Initial horizontal position\n     * @param {Highcharts.Breadcrumbs} posY\n     *        Initial vertical position\n     * @return {SVGElement|void}\n     *        Returns the SVG button\n     */\n    renderButton(breadcrumb, posX, posY) {\n        const breadcrumbs = this, chart = this.chart, breadcrumbsOptions = breadcrumbs.options, buttonTheme = merge(breadcrumbsOptions.buttonTheme);\n        const button = chart.renderer\n            .button(breadcrumbs.getButtonText(breadcrumb), posX, posY, function (e) {\n            // Extract events from button object and call\n            const buttonEvents = breadcrumbsOptions.events &&\n                breadcrumbsOptions.events.click;\n            let callDefaultEvent;\n            if (buttonEvents) {\n                callDefaultEvent = buttonEvents.call(breadcrumbs, e, breadcrumb);\n            }\n            // (difference in behaviour of showFullPath and drillUp)\n            if (callDefaultEvent !== false) {\n                // For single button we are not going to the button\n                // level, but the one level up\n                if (!breadcrumbsOptions.showFullPath) {\n                    e.newLevel = breadcrumbs.level - 1;\n                }\n                else {\n                    e.newLevel = breadcrumb.level;\n                }\n                fireEvent(breadcrumbs, 'up', e);\n            }\n        }, buttonTheme)\n            .addClass('highcharts-breadcrumbs-button')\n            .add(breadcrumbs.group);\n        if (!chart.styledMode) {\n            button.attr(breadcrumbsOptions.style);\n        }\n        return button;\n    }\n    /**\n     * Render a separator.\n     *\n     * @function Highcharts.Breadcrumbs#renderSeparator\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     * @param {Highcharts.Breadcrumbs} posX\n     *        Initial horizontal position\n     * @param {Highcharts.Breadcrumbs} posY\n     *        Initial vertical position\n     * @return {Highcharts.SVGElement}\n     *        Returns the SVG button\n     */\n    renderSeparator(posX, posY) {\n        const breadcrumbs = this, chart = this.chart, breadcrumbsOptions = breadcrumbs.options, separatorOptions = breadcrumbsOptions.separator;\n        const separator = chart.renderer\n            .label(separatorOptions.text, posX, posY, void 0, void 0, void 0, false)\n            .addClass('highcharts-breadcrumbs-separator')\n            .add(breadcrumbs.group);\n        if (!chart.styledMode) {\n            separator.css(separatorOptions.style);\n        }\n        return separator;\n    }\n    /**\n     * Update.\n     * @function Highcharts.Breadcrumbs#update\n     *\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     * @param {Highcharts.BreadcrumbsOptions} options\n     *        Breadcrumbs class.\n     * @param {boolean} redraw\n     *        Redraw flag\n     */\n    update(options) {\n        merge(true, this.options, options);\n        this.destroy();\n        this.isDirty = true;\n    }\n    /**\n     * Update button text when the showFullPath set to false.\n     * @function Highcharts.Breadcrumbs#updateSingleButton\n     *\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    updateSingleButton() {\n        const chart = this.chart, currentBreadcrumb = this.list[this.level - 1];\n        if (chart.drillUpButton) {\n            chart.drillUpButton.attr({\n                text: this.getButtonText(currentBreadcrumb)\n            });\n        }\n    }\n    /**\n     * Destroy the chosen breadcrumbs group\n     *\n     * @function Highcharts.Breadcrumbs#destroy\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    destroy() {\n        this.destroySingleButton();\n        // Destroy elements one by one. It's necessary because\n        // g().destroy() does not remove added HTML\n        this.destroyListElements(true);\n        // Then, destroy the group itself.\n        if (this.group) {\n            this.group.destroy();\n        }\n        this.group = void 0;\n    }\n    /**\n     * Destroy the elements' buttons and separators.\n     *\n     * @function Highcharts.Breadcrumbs#destroyListElements\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    destroyListElements(force) {\n        const elementList = this.elementList;\n        objectEach(elementList, (element, level) => {\n            if (force ||\n                !elementList[level].updated) {\n                element = elementList[level];\n                element.button && element.button.destroy();\n                element.separator && element.separator.destroy();\n                delete element.button;\n                delete element.separator;\n                delete elementList[level];\n            }\n        });\n        if (force) {\n            this.elementList = {};\n        }\n    }\n    /**\n     * Destroy the single button if exists.\n     *\n     * @function Highcharts.Breadcrumbs#destroySingleButton\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    destroySingleButton() {\n        if (this.chart.drillUpButton) {\n            this.chart.drillUpButton.destroy();\n            this.chart.drillUpButton = void 0;\n        }\n    }\n    /**\n     * Reset state for all buttons in elementList.\n     *\n     * @function Highcharts.Breadcrumbs#resetElementListState\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    resetElementListState() {\n        objectEach(this.elementList, (element) => {\n            element.updated = false;\n        });\n    }\n    /**\n     * Update rendered elements inside the elementList.\n     *\n     * @function Highcharts.Breadcrumbs#updateListElements\n     *\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    updateListElements() {\n        const breadcrumbs = this, elementList = breadcrumbs.elementList, buttonSpacing = breadcrumbs.options.buttonSpacing, posY = buttonSpacing, list = breadcrumbs.list, rtl = breadcrumbs.options.rtl, rtlFactor = rtl ? -1 : 1, updateXPosition = function (element, spacing) {\n            return rtlFactor * element.getBBox().width +\n                rtlFactor * spacing;\n        }, adjustToRTL = function (element, posX, posY) {\n            element.translate(posX - element.getBBox().width, posY);\n        };\n        // Initial position for calculating the breadcrumbs group.\n        let posX = breadcrumbs.group ?\n            updateXPosition(breadcrumbs.group, buttonSpacing) :\n            buttonSpacing, currentBreadcrumb, breadcrumb;\n        for (let i = 0, iEnd = list.length; i < iEnd; ++i) {\n            const isLast = i === iEnd - 1;\n            let button, separator;\n            breadcrumb = list[i];\n            if (elementList[breadcrumb.level]) {\n                currentBreadcrumb = elementList[breadcrumb.level];\n                button = currentBreadcrumb.button;\n                // Render a separator if it was not created before.\n                if (!currentBreadcrumb.separator &&\n                    !isLast) {\n                    // Add spacing for the next separator\n                    posX += rtlFactor * buttonSpacing;\n                    currentBreadcrumb.separator =\n                        breadcrumbs.renderSeparator(posX, posY);\n                    if (rtl) {\n                        adjustToRTL(currentBreadcrumb.separator, posX, posY);\n                    }\n                    posX += updateXPosition(currentBreadcrumb.separator, buttonSpacing);\n                }\n                else if (currentBreadcrumb.separator &&\n                    isLast) {\n                    currentBreadcrumb.separator.destroy();\n                    delete currentBreadcrumb.separator;\n                }\n                elementList[breadcrumb.level].updated = true;\n            }\n            else {\n                // Render a button.\n                button = breadcrumbs.renderButton(breadcrumb, posX, posY);\n                if (rtl) {\n                    adjustToRTL(button, posX, posY);\n                }\n                posX += updateXPosition(button, buttonSpacing);\n                // Render a separator.\n                if (!isLast) {\n                    separator = breadcrumbs.renderSeparator(posX, posY);\n                    if (rtl) {\n                        adjustToRTL(separator, posX, posY);\n                    }\n                    posX += updateXPosition(separator, buttonSpacing);\n                }\n                elementList[breadcrumb.level] = {\n                    button,\n                    separator,\n                    updated: true\n                };\n            }\n            if (button) {\n                button.setState(isLast ? 2 : 0);\n            }\n        }\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nBreadcrumbs.defaultOptions = Breadcrumbs_BreadcrumbsDefaults.options;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Breadcrumbs_Breadcrumbs = (Breadcrumbs);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Callback function to react on button clicks.\n *\n * @callback Highcharts.BreadcrumbsClickCallbackFunction\n *\n * @param {Highcharts.Event} event\n * Event.\n *\n * @param {Highcharts.BreadcrumbOptions} options\n * Breadcrumb options.\n *\n * @param {global.Event} e\n * Event arguments.\n */\n/**\n * Callback function to format the breadcrumb text from scratch.\n *\n * @callback Highcharts.BreadcrumbsFormatterCallbackFunction\n *\n * @param {Highcharts.BreadcrumbOptions} options\n * Breadcrumb options.\n *\n * @return {string}\n * Formatted text or false\n */\n/**\n * Options for the one breadcrumb.\n *\n * @interface Highcharts.BreadcrumbOptions\n */\n/**\n * Level connected to a specific breadcrumb.\n * @name Highcharts.BreadcrumbOptions#level\n * @type {number}\n */\n/**\n * Options for series or point connected to a specific breadcrumb.\n * @name Highcharts.BreadcrumbOptions#levelOptions\n * @type {SeriesOptions|PointOptionsObject}\n */\n/**\n * Options for aligning breadcrumbs group.\n *\n * @interface Highcharts.BreadcrumbsAlignOptions\n */\n/**\n * Align of a Breadcrumb group.\n * @default right\n * @name Highcharts.BreadcrumbsAlignOptions#align\n * @type {AlignValue}\n */\n/**\n * Vertical align of a Breadcrumb group.\n * @default top\n * @name Highcharts.BreadcrumbsAlignOptions#verticalAlign\n * @type {VerticalAlignValue}\n */\n/**\n * X offset of a Breadcrumbs group.\n * @name Highcharts.BreadcrumbsAlignOptions#x\n * @type {number}\n */\n/**\n * Y offset of a Breadcrumbs group.\n * @name Highcharts.BreadcrumbsAlignOptions#y\n * @type {number}\n */\n/**\n * Options for all breadcrumbs.\n *\n * @interface Highcharts.BreadcrumbsOptions\n */\n/**\n * Button theme.\n * @name Highcharts.BreadcrumbsOptions#buttonTheme\n * @type { SVGAttributes | undefined }\n */\n(''); // Keeps doclets above in JS file\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Color\"],\"commonjs\":[\"highcharts\",\"Color\"],\"commonjs2\":[\"highcharts\",\"Color\"],\"root\":[\"Highcharts\",\"Color\"]}\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_ = __webpack_require__(620);\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default = /*#__PURE__*/__webpack_require__.n(highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SVGElement\"],\"commonjs\":[\"highcharts\",\"SVGElement\"],\"commonjs2\":[\"highcharts\",\"SVGElement\"],\"root\":[\"Highcharts\",\"SVGElement\"]}\nvar highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_ = __webpack_require__(28);\nvar highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default = /*#__PURE__*/__webpack_require__.n(highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_);\n;// ./code/es-modules/Series/ColorMapComposition.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { column: { prototype: columnProto } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\n\nconst { addEvent: ColorMapComposition_addEvent, defined: ColorMapComposition_defined } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\nvar ColorMapComposition;\n(function (ColorMapComposition) {\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    ColorMapComposition.pointMembers = {\n        dataLabelOnNull: true,\n        moveToTopOnHover: true,\n        isValid: pointIsValid\n    };\n    ColorMapComposition.seriesMembers = {\n        colorKey: 'value',\n        axisTypes: ['xAxis', 'yAxis', 'colorAxis'],\n        parallelArrays: ['x', 'y', 'value'],\n        pointArrayMap: ['value'],\n        trackerGroups: ['group', 'markerGroup', 'dataLabelsGroup'],\n        colorAttribs: seriesColorAttribs,\n        pointAttribs: columnProto.pointAttribs\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    function compose(SeriesClass) {\n        const PointClass = SeriesClass.prototype.pointClass;\n        ColorMapComposition_addEvent(PointClass, 'afterSetState', onPointAfterSetState);\n        return SeriesClass;\n    }\n    ColorMapComposition.compose = compose;\n    /**\n     * Move points to the top of the z-index order when hovered.\n     * @private\n     */\n    function onPointAfterSetState(e) {\n        const point = this, series = point.series, renderer = series.chart.renderer;\n        if (point.moveToTopOnHover && point.graphic) {\n            if (!series.stateMarkerGraphic) {\n                // Create a `use` element and add it to the end of the group,\n                // which would make it appear on top of the other elements. This\n                // deals with z-index without reordering DOM elements (#13049).\n                series.stateMarkerGraphic = new (highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default())(renderer, 'use')\n                    .css({\n                    pointerEvents: 'none'\n                })\n                    .add(point.graphic.parentGroup);\n            }\n            if (e?.state === 'hover') {\n                // Give the graphic DOM element the same id as the Point\n                // instance\n                point.graphic.attr({\n                    id: this.id\n                });\n                series.stateMarkerGraphic.attr({\n                    href: `${renderer.url}#${this.id}`,\n                    visibility: 'visible'\n                });\n            }\n            else {\n                series.stateMarkerGraphic.attr({\n                    href: ''\n                });\n            }\n        }\n    }\n    /**\n     * Color points have a value option that determines whether or not it is\n     * a null point\n     * @private\n     */\n    function pointIsValid() {\n        return (this.value !== null &&\n            this.value !== Infinity &&\n            this.value !== -Infinity &&\n            // Undefined is allowed, but NaN is not (#17279)\n            (this.value === void 0 || !isNaN(this.value)));\n    }\n    /**\n     * Get the color attributes to apply on the graphic\n     * @private\n     * @function Highcharts.colorMapSeriesMixin.colorAttribs\n     * @param {Highcharts.Point} point\n     * @return {Highcharts.SVGAttributes}\n     *         The SVG attributes\n     */\n    function seriesColorAttribs(point) {\n        const ret = {};\n        if (ColorMapComposition_defined(point.color) &&\n            (!point.state || point.state === 'normal') // #15746\n        ) {\n            ret[this.colorProp || 'fill'] = point.color;\n        }\n        return ret;\n    }\n})(ColorMapComposition || (ColorMapComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Series_ColorMapComposition = (ColorMapComposition);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Series\"],\"commonjs\":[\"highcharts\",\"Series\"],\"commonjs2\":[\"highcharts\",\"Series\"],\"root\":[\"Highcharts\",\"Series\"]}\nvar highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_ = __webpack_require__(820);\nvar highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default = /*#__PURE__*/__webpack_require__.n(highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_);\n;// ./code/es-modules/Series/Treemap/TreemapAlgorithmGroup.js\n/* *\n *\n *  (c) 2014-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Class\n *\n * */\nclass TreemapAlgorithmGroup {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(h, w, d, p) {\n        this.height = h;\n        this.width = w;\n        this.plot = p;\n        this.direction = d;\n        this.startDirection = d;\n        this.total = 0;\n        this.nW = 0;\n        this.lW = 0;\n        this.nH = 0;\n        this.lH = 0;\n        this.elArr = [];\n        this.lP = {\n            total: 0,\n            lH: 0,\n            nH: 0,\n            lW: 0,\n            nW: 0,\n            nR: 0,\n            lR: 0,\n            aspectRatio: function (w, h) {\n                return Math.max((w / h), (h / w));\n            }\n        };\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    addElement(el) {\n        this.lP.total = this.elArr[this.elArr.length - 1];\n        this.total = this.total + el;\n        if (this.direction === 0) {\n            // Calculate last point old aspect ratio\n            this.lW = this.nW;\n            this.lP.lH = this.lP.total / this.lW;\n            this.lP.lR = this.lP.aspectRatio(this.lW, this.lP.lH);\n            // Calculate last point new aspect ratio\n            this.nW = this.total / this.height;\n            this.lP.nH = this.lP.total / this.nW;\n            this.lP.nR = this.lP.aspectRatio(this.nW, this.lP.nH);\n        }\n        else {\n            // Calculate last point old aspect ratio\n            this.lH = this.nH;\n            this.lP.lW = this.lP.total / this.lH;\n            this.lP.lR = this.lP.aspectRatio(this.lP.lW, this.lH);\n            // Calculate last point new aspect ratio\n            this.nH = this.total / this.width;\n            this.lP.nW = this.lP.total / this.nH;\n            this.lP.nR = this.lP.aspectRatio(this.lP.nW, this.nH);\n        }\n        this.elArr.push(el);\n    }\n    reset() {\n        this.nW = 0;\n        this.lW = 0;\n        this.elArr = [];\n        this.total = 0;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treemap_TreemapAlgorithmGroup = (TreemapAlgorithmGroup);\n\n;// ./code/es-modules/Series/Treemap/TreemapNode.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Class\n *\n * */\nclass TreemapNode {\n    constructor() {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.childrenTotal = 0;\n        this.visible = false;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    init(id, i, children, height, level, series, parent) {\n        this.id = id;\n        this.i = i;\n        this.children = children;\n        this.height = height;\n        this.level = level;\n        this.series = series;\n        this.parent = parent;\n        return this;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treemap_TreemapNode = (TreemapNode);\n\n;// ./code/es-modules/Series/DrawPointUtilities.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Handles the drawing of a component.\n * Can be used for any type of component that reserves the graphic property,\n * and provides a shouldDraw on its context.\n *\n * @private\n *\n * @todo add type checking.\n * @todo export this function to enable usage\n */\nfunction draw(point, params) {\n    const { animatableAttribs, onComplete, css, renderer } = params;\n    const animation = (point.series && point.series.chart.hasRendered) ?\n        // Chart-level animation on updates\n        void 0 :\n        // Series-level animation on new points\n        (point.series &&\n            point.series.options.animation);\n    let graphic = point.graphic;\n    params.attribs = {\n        ...params.attribs,\n        'class': point.getClassName()\n    } || {};\n    if ((point.shouldDraw())) {\n        if (!graphic) {\n            if (params.shapeType === 'text') {\n                graphic = renderer.text();\n            }\n            else if (params.shapeType === 'image') {\n                graphic = renderer.image(params.imageUrl || '')\n                    .attr(params.shapeArgs || {});\n            }\n            else {\n                graphic = renderer[params.shapeType](params.shapeArgs || {});\n            }\n            point.graphic = graphic;\n            graphic.add(params.group);\n        }\n        if (css) {\n            graphic.css(css);\n        }\n        graphic\n            .attr(params.attribs)\n            .animate(animatableAttribs, params.isNew ? false : animation, onComplete);\n    }\n    else if (graphic) {\n        const destroy = () => {\n            point.graphic = graphic = (graphic && graphic.destroy());\n            if (typeof onComplete === 'function') {\n                onComplete();\n            }\n        };\n        // Animate only runs complete callback if something was animated.\n        if (Object.keys(animatableAttribs).length) {\n            graphic.animate(animatableAttribs, void 0, () => destroy());\n        }\n        else {\n            destroy();\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst DrawPointUtilities = {\n    draw\n};\n/* harmony default export */ const Series_DrawPointUtilities = (DrawPointUtilities);\n\n;// ./code/es-modules/Series/Treemap/TreemapPoint.js\n/* *\n *\n *  (c) 2014-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { pie: { prototype: { pointClass: PiePoint } }, scatter: { prototype: { pointClass: ScatterPoint } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: TreemapPoint_extend, isNumber, pick: TreemapPoint_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass TreemapPoint extends ScatterPoint {\n    constructor() {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        super(...arguments);\n        this.groupedPointsAmount = 0;\n        this.shapeType = 'rect';\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    draw(params) {\n        Series_DrawPointUtilities.draw(this, params);\n    }\n    getClassName() {\n        const series = this.series, options = series.options;\n        let className = super.getClassName();\n        // Above the current level\n        if (this.node.level <= series.nodeMap[series.rootNode].level &&\n            this.node.children.length) {\n            className += ' highcharts-above-level';\n        }\n        else if (!this.node.isGroup &&\n            !this.node.isLeaf &&\n            !series.nodeMap[series.rootNode].isGroup &&\n            !TreemapPoint_pick(options.interactByLeaf, !options.allowTraversingTree)) {\n            className += ' highcharts-internal-node-interactive';\n        }\n        else if (!this.node.isGroup &&\n            !this.node.isLeaf &&\n            !series.nodeMap[series.rootNode].isGroup) {\n            className += ' highcharts-internal-node';\n        }\n        return className;\n    }\n    /**\n     * A tree point is valid if it has han id too, assume it may be a parent\n     * item.\n     *\n     * @private\n     * @function Highcharts.Point#isValid\n     */\n    isValid() {\n        return Boolean(this.id || isNumber(this.value));\n    }\n    setState(state) {\n        super.setState.apply(this, arguments);\n        // Graphic does not exist when point is not visible.\n        if (this.graphic) {\n            this.graphic.attr({\n                zIndex: state === 'hover' ? 1 : 0\n            });\n        }\n    }\n    shouldDraw() {\n        return isNumber(this.plotY) && this.y !== null;\n    }\n}\nTreemapPoint_extend(TreemapPoint.prototype, {\n    setVisible: PiePoint.prototype.setVisible\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treemap_TreemapPoint = (TreemapPoint);\n\n;// ./code/es-modules/Series/Treemap/TreemapSeriesDefaults.js\n/* *\n *\n *  (c) 2014-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { isString: TreemapSeriesDefaults_isString } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A treemap displays hierarchical data using nested rectangles. The data\n * can be laid out in varying ways depending on options.\n *\n * @sample highcharts/demo/treemap-large-dataset/\n *         Treemap\n *\n * @extends      plotOptions.scatter\n * @excluding    connectEnds, connectNulls, dataSorting, dragDrop, jitter, marker\n * @product      highcharts\n * @requires     modules/treemap\n * @optionparent plotOptions.treemap\n */\nconst TreemapSeriesDefaults = {\n    /**\n     * When enabled the user can click on a point which is a parent and\n     * zoom in on its children. Deprecated and replaced by\n     * [allowTraversingTree](#plotOptions.treemap.allowTraversingTree).\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-allowdrilltonode/\n     *         Enabled\n     *\n     * @deprecated\n     * @type      {boolean}\n     * @default   false\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.allowDrillToNode\n     */\n    /**\n     * When enabled the user can click on a point which is a parent and\n     * zoom in on its children.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-allowtraversingtree/\n     *         Enabled\n     * @sample {highcharts} highcharts/plotoptions/treemap-grouping-traversing/\n     *         Traversing to Grouped Points node\n     *\n     * @since     7.0.3\n     * @product   highcharts\n     */\n    allowTraversingTree: false,\n    animationLimit: 250,\n    /**\n     * The border radius for each treemap item.\n     */\n    borderRadius: 0,\n    /**\n     * Options for the breadcrumbs, the navigation at the top leading the\n     * way up through the traversed levels.\n     *\n     *\n     * @since 10.0.0\n     * @product   highcharts\n     * @extends   navigation.breadcrumbs\n     * @apioption plotOptions.treemap.breadcrumbs\n     */\n    /**\n     * When the series contains less points than the crop threshold, all\n     * points are drawn, event if the points fall outside the visible plot\n     * area at the current zoom. The advantage of drawing all points\n     * (including markers and columns), is that animation is performed on\n     * updates. On the other hand, when the series contains more points than\n     * the crop threshold, the series data is cropped to only contain points\n     * that fall within the plot area. The advantage of cropping away\n     * invisible points is to increase performance on large series.\n     *\n     * @type      {number}\n     * @default   300\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.cropThreshold\n     */\n    /**\n     * Fires on a request for change of root node for the tree, before the\n     * update is made. An event object is passed to the function, containing\n     * additional properties `newRootId`, `previousRootId`, `redraw` and\n     * `trigger`.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-events-setrootnode/\n     *         Alert update information on setRootNode event.\n     *\n     * @type {Function}\n     * @default undefined\n     * @since 7.0.3\n     * @product highcharts\n     * @apioption plotOptions.treemap.events.setRootNode\n     */\n    /**\n     * This option decides if the user can interact with the parent nodes\n     * or just the leaf nodes. When this option is undefined, it will be\n     * true by default. However when allowTraversingTree is true, then it\n     * will be false by default.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-interactbyleaf-false/\n     *         False\n     * @sample {highcharts} highcharts/plotoptions/treemap-interactbyleaf-true-and-allowtraversingtree/\n     *         InteractByLeaf and allowTraversingTree is true\n     *\n     * @type      {boolean}\n     * @since     4.1.2\n     * @product   highcharts\n     * @apioption plotOptions.treemap.interactByLeaf\n     */\n    /**\n     * The sort index of the point inside the treemap level.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-sortindex/\n     *         Sort by years\n     *\n     * @type      {number}\n     * @since     4.1.10\n     * @product   highcharts\n     * @apioption plotOptions.treemap.sortIndex\n     */\n    /**\n     * A series specific or series type specific color set to apply instead\n     * of the global [colors](#colors) when\n     * [colorByPoint](#plotOptions.treemap.colorByPoint) is true.\n     *\n     * @type      {Array<Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject>}\n     * @since     3.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.colors\n     */\n    /**\n     * Whether to display this series type or specific series item in the\n     * legend.\n     */\n    showInLegend: false,\n    /**\n     * @ignore-option\n     */\n    marker: void 0,\n    /**\n     * When using automatic point colors pulled from the `options.colors`\n     * collection, this option determines whether the chart should receive\n     * one color per series or one color per point.\n     *\n     * @see [series colors](#plotOptions.treemap.colors)\n     *\n     * @since     2.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.colorByPoint\n     */\n    colorByPoint: false,\n    /**\n     * @since 4.1.0\n     */\n    dataLabels: {\n        enabled: true,\n        formatter: function () {\n            const point = this && this.point ?\n                this.point :\n                {}, name = TreemapSeriesDefaults_isString(point.name) ? point.name : '';\n            return name;\n        },\n        /**\n         * Whether the data label should act as a group-level header. For leaf\n         * nodes, headers are not supported and the data label will be rendered\n         * inside.\n         *\n         * @sample {highcharts} highcharts/series-treemap/headers\n         *         Headers for parent nodes\n         *\n         * @since 12.2.0\n         */\n        headers: false,\n        inside: true,\n        padding: 2,\n        verticalAlign: 'middle',\n        style: {\n            textOverflow: 'ellipsis'\n        }\n    },\n    tooltip: {\n        headerFormat: '',\n        pointFormat: '<b>{point.name}</b>: {point.value}<br/>',\n        /**\n         * The HTML of the grouped point's nodes in the tooltip. Works only for\n         * Treemap series grouping and analogously to\n         * [pointFormat](#tooltip.pointFormat).\n         *\n         * The grouped nodes point tooltip can be also formatted using\n         * `tooltip.formatter` callback function and `point.isGroupNode` flag.\n         *\n         * @type      {string}\n         * @default   '+ {point.groupedPointsAmount} more...'\n         * @apioption tooltip.clusterFormat\n         */\n        clusterFormat: '+ {point.groupedPointsAmount} more...<br/>'\n    },\n    /**\n     * Whether to ignore hidden points when the layout algorithm runs.\n     * If `false`, hidden points will leave open spaces.\n     *\n     * @since 5.0.8\n     */\n    ignoreHiddenPoint: true,\n    /**\n     * This option decides which algorithm is used for setting position\n     * and dimensions of the points.\n     *\n     * @see [How to write your own algorithm](https://www.highcharts.com/docs/chart-and-series-types/treemap)\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-layoutalgorithm-sliceanddice/\n     *         SliceAndDice by default\n     * @sample {highcharts} highcharts/plotoptions/treemap-layoutalgorithm-stripes/\n     *         Stripes\n     * @sample {highcharts} highcharts/plotoptions/treemap-layoutalgorithm-squarified/\n     *         Squarified\n     * @sample {highcharts} highcharts/plotoptions/treemap-layoutalgorithm-strip/\n     *         Strip\n     *\n     * @since      4.1.0\n     * @validvalue [\"sliceAndDice\", \"stripes\", \"squarified\", \"strip\"]\n     */\n    layoutAlgorithm: 'sliceAndDice',\n    /**\n     * Defines which direction the layout algorithm will start drawing.\n     *\n     * @since       4.1.0\n     * @validvalue [\"vertical\", \"horizontal\"]\n     */\n    layoutStartingDirection: 'vertical',\n    /**\n     * Enabling this option will make the treemap alternate the drawing\n     * direction between vertical and horizontal. The next levels starting\n     * direction will always be the opposite of the previous.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-alternatestartingdirection-true/\n     *         Enabled\n     *\n     * @since 4.1.0\n     */\n    alternateStartingDirection: false,\n    /**\n     * Used together with the levels and allowTraversingTree options. When\n     * set to false the first level visible to be level one, which is\n     * dynamic when traversing the tree. Otherwise the level will be the\n     * same as the tree structure.\n     *\n     * @since 4.1.0\n     */\n    levelIsConstant: true,\n    /**\n     * Options for the button appearing when traversing down in a treemap.\n     *\n     * Since v9.3.3 the `traverseUpButton` is replaced by `breadcrumbs`.\n     *\n     * @deprecated\n     */\n    traverseUpButton: {\n        /**\n         * The position of the button.\n         */\n        position: {\n            /**\n             * Vertical alignment of the button.\n             *\n             * @type      {Highcharts.VerticalAlignValue}\n             * @default   top\n             * @product   highcharts\n             * @apioption plotOptions.treemap.traverseUpButton.position.verticalAlign\n             */\n            /**\n             * Horizontal alignment of the button.\n             *\n             * @type {Highcharts.AlignValue}\n             */\n            align: 'right',\n            /**\n             * Horizontal offset of the button.\n             */\n            x: -10,\n            /**\n             * Vertical offset of the button.\n             */\n            y: 10\n        }\n    },\n    /**\n     * Group padding for parent elements in terms of pixels. See also the\n     * `nodeSizeBy` option that controls how the leaf nodes' size is affected by\n     * the padding.\n     *\n     * @sample    {highcharts} highcharts/series-treemap/grouppadding/\n     *            Group padding\n     * @type      {number}\n     * @since 12.2.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.groupPadding\n     */\n    /**\n     * Set options on specific levels. Takes precedence over series options,\n     * but not point options.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-levels/\n     *         Styling dataLabels and borders\n     * @sample {highcharts} highcharts/demo/treemap-with-levels/\n     *         Different layoutAlgorithm\n     *\n     * @type      {Array<*>}\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels\n     */\n    /**\n     * Experimental. How to set the size of child nodes when a header or padding\n     * is present. When `leaf`, the group is expanded to make room for headers\n     * and padding in order to preserve the relative sizes between leaves. When\n     * `group`, the leaves are naïvely fit into the remaining area after the\n     * header and padding are subtracted.\n     *\n     * @sample    {highcharts} highcharts/series-treemap/nodesizeby/\n     *            Node sizing\n     * @since 12.2.0\n     * @type      {string}\n     * @validvalue [\"group\", \"leaf\"]\n     * @default   group\n     * @apioption plotOptions.treemap.nodeSizeBy\n     */\n    /**\n     * Can set a `borderColor` on all points which lies on the same level.\n     *\n     * @type      {Highcharts.ColorString}\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels.borderColor\n     */\n    /**\n     * Set the dash style of the border of all the point which lies on the\n     * level. See\n     * [plotOptions.scatter.dashStyle](#plotoptions.scatter.dashstyle)\n     * for possible options.\n     *\n     * @type      {Highcharts.DashStyleValue}\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels.borderDashStyle\n     */\n    /**\n     * Can set the borderWidth on all points which lies on the same level.\n     *\n     * @type      {number}\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels.borderWidth\n     */\n    /**\n     * Can set a color on all points which lies on the same level.\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels.color\n     */\n    /**\n     * A configuration object to define how the color of a child varies from\n     * the parent's color. The variation is distributed among the children\n     * of node. For example when setting brightness, the brightness change\n     * will range from the parent's original brightness on the first child,\n     * to the amount set in the `to` setting on the last node. This allows a\n     * gradient-like color scheme that sets children out from each other\n     * while highlighting the grouping on treemaps and sectors on sunburst\n     * charts.\n     *\n     * @sample highcharts/demo/sunburst/\n     *         Sunburst with color variation\n     *\n     * @sample highcharts/series-treegraph/color-variation\n     *         Treegraph nodes with color variation\n     *\n     * @since     6.0.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels.colorVariation\n     */\n    /**\n     * The key of a color variation. Currently supports `brightness` only.\n     *\n     * @type       {string}\n     * @since      6.0.0\n     * @product    highcharts\n     * @validvalue [\"brightness\"]\n     * @apioption  plotOptions.treemap.levels.colorVariation.key\n     */\n    /**\n     * The ending value of a color variation. The last sibling will receive\n     * this value.\n     *\n     * @type      {number}\n     * @since     6.0.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels.colorVariation.to\n     */\n    /**\n     * Can set the options of dataLabels on each point which lies on the\n     * level.\n     * [plotOptions.treemap.dataLabels](#plotOptions.treemap.dataLabels) for\n     * possible values.\n     *\n     * @extends   plotOptions.treemap.dataLabels\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels.dataLabels\n     */\n    /**\n     * Can set the layoutAlgorithm option on a specific level.\n     *\n     * @type       {string}\n     * @since      4.1.0\n     * @product    highcharts\n     * @validvalue [\"sliceAndDice\", \"stripes\", \"squarified\", \"strip\"]\n     * @apioption  plotOptions.treemap.levels.layoutAlgorithm\n     */\n    /**\n     * Can set the layoutStartingDirection option on a specific level.\n     *\n     * @type       {string}\n     * @since      4.1.0\n     * @product    highcharts\n     * @validvalue [\"vertical\", \"horizontal\"]\n     * @apioption  plotOptions.treemap.levels.layoutStartingDirection\n     */\n    /**\n     * Decides which level takes effect from the options set in the levels\n     * object.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-levels/\n     *         Styling of both levels\n     *\n     * @type      {number}\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels.level\n     */\n    // Presentational options\n    /**\n     * The color of the border surrounding each tree map item.\n     *\n     * @type {Highcharts.ColorString}\n     */\n    borderColor: \"#e6e6e6\" /* Palette.neutralColor10 */,\n    /**\n     * The width of the border surrounding each tree map item.\n     */\n    borderWidth: 1,\n    colorKey: 'colorValue',\n    /**\n     * The opacity of grouped points in treemap. When a point has children, the\n     * group point is covering the children, and is given this opacity. The\n     * visibility of the children is determined by the opacity.\n     *\n     * @since 4.2.4\n     */\n    opacity: 0.15,\n    /**\n     * A wrapper object for all the series options in specific states.\n     *\n     * @extends plotOptions.heatmap.states\n     */\n    states: {\n        /**\n         * Options for the hovered series\n         *\n         * @extends   plotOptions.heatmap.states.hover\n         * @excluding halo\n         */\n        hover: {\n            /**\n             * The border color for the hovered state.\n             */\n            borderColor: \"#999999\" /* Palette.neutralColor40 */,\n            /**\n             * Brightness for the hovered point. Defaults to 0 if the\n             * heatmap series is loaded first, otherwise 0.1.\n             *\n             * @type    {number}\n             * @default undefined\n             */\n            brightness: (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.heatmap ? 0 : 0.1,\n            /**\n             * @extends plotOptions.heatmap.states.hover.halo\n             */\n            halo: false,\n            /**\n             * The opacity of a point in treemap. When a point has children,\n             * the visibility of the children is determined by the opacity.\n             *\n             * @since 4.2.4\n             */\n            opacity: 0.75,\n            /**\n             * The shadow option for hovered state.\n             */\n            shadow: false\n        }\n    },\n    legendSymbol: 'rectangle',\n    /**\n     * This option enables automatic traversing to the last child level upon\n     * node interaction. This feature simplifies navigation by immediately\n     * focusing on the deepest layer of the data structure without intermediate\n     * steps.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-traverse-to-leaf/\n     *         Traverse to leaf enabled\n     *\n     * @since   11.4.4\n     *\n     * @product highcharts\n     */\n    traverseToLeaf: false,\n    /**\n     * An option to optimize treemap series rendering by grouping smaller leaf\n     * nodes below a certain square area threshold in pixels. If the square area\n     * of a point becomes smaller than the specified threshold, determined by\n     * the `pixelWidth` and/or `pixelHeight` options, then this point is moved\n     * into one group point per series.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-grouping-simple\n     *         Simple demo of Treemap grouping\n     * @sample {highcharts} highcharts/plotoptions/treemap-grouping-multiple-parents\n     *         Treemap grouping with multiple parents\n     * @sample {highcharts} highcharts/plotoptions/treemap-grouping-advanced\n     *         Advanced demo of Treemap grouping\n     *\n     * @since 12.1.0\n     *\n     * @excluding allowOverlap, animation, dataLabels, drillToCluster, events,\n     * layoutAlgorithm, marker, states, zones\n     *\n     * @product highcharts\n     */\n    cluster: {\n        /**\n         * An additional, individual class name for the grouped point's graphic\n         * representation.\n         *\n         * @type      string\n         * @product   highcharts\n         */\n        className: void 0,\n        /**\n         * Individual color for the grouped point. By default the color is\n         * pulled from the parent color.\n         *\n         * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         * @product   highcharts\n         */\n        color: void 0,\n        /**\n         * Enable or disable Treemap grouping.\n         *\n         * @type {boolean}\n         * @since 12.1.0\n         * @product highcharts\n         */\n        enabled: false,\n        /**\n         * The pixel threshold width of area, which is used in Treemap grouping.\n         *\n         * @type {number}\n         * @since 12.1.0\n         * @product highcharts\n         */\n        pixelWidth: void 0,\n        /**\n         * The pixel threshold height of area, which is used in Treemap\n         * grouping.\n         *\n         * @type {number}\n         * @since 12.1.0\n         * @product highcharts\n         */\n        pixelHeight: void 0,\n        /**\n         * The name of the point of grouped nodes shown in the tooltip,\n         * dataLabels, etc. By default it is set to '+ n', where n is number of\n         * grouped points.\n         *\n         * @type {string}\n         * @since 12.1.0\n         * @product highcharts\n         */\n        name: void 0,\n        /**\n         * A configuration property that specifies the factor by which the value\n         * and size of a grouped node are reduced. This can be particularly\n         * useful when a grouped node occupies a disproportionately large\n         * portion of the graph, ensuring better visual balance and readability.\n         *\n         * @type {number}\n         * @since 12.1.0\n         * @product highcharts\n         */\n        reductionFactor: void 0,\n        /**\n         * Defines the minimum number of child nodes required to create a group\n         * of small nodes.\n         *\n         * @type {number}\n         * @since 12.1.0\n         * @product highcharts\n         */\n        minimumClusterSize: 5,\n        layoutAlgorithm: {\n            distance: 0,\n            gridSize: 0,\n            kmeansThreshold: 0\n        },\n        marker: {\n            lineWidth: 0,\n            radius: 0\n        }\n    }\n};\n/**\n * A `treemap` series. If the [type](#series.treemap.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.treemap\n * @excluding dataParser, dataURL, stack, dataSorting\n * @product   highcharts\n * @requires  modules/treemap\n * @apioption series.treemap\n */\n/**\n * An array of data points for the series. For the `treemap` series\n * type, points can be given in the following ways:\n *\n * 1. An array of numerical values. In this case, the numerical values will be\n *    interpreted as `value` options. Example:\n *    ```js\n *    data: [0, 5, 3, 5]\n *    ```\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.treemap.turboThreshold),\n *    this option is not available.\n *    ```js\n *      data: [{\n *        value: 9,\n *        name: \"Point2\",\n *        color: \"#00FF00\"\n *      }, {\n *        value: 6,\n *        name: \"Point1\",\n *        color: \"#FF00FF\"\n *      }]\n *    ```\n *\n * @sample {highcharts} highcharts/chart/reflow-true/\n *         Numerical values\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<number|null|*>}\n * @extends   series.heatmap.data\n * @excluding x, y, pointPadding\n * @product   highcharts\n * @apioption series.treemap.data\n */\n/**\n * The value of the point, resulting in a relative area of the point\n * in the treemap.\n *\n * @type      {number|null}\n * @product   highcharts\n * @apioption series.treemap.data.value\n */\n/**\n * Serves a purpose only if a `colorAxis` object is defined in the chart\n * options. This value will decide which color the point gets from the\n * scale of the colorAxis.\n *\n * @type      {number}\n * @since     4.1.0\n * @product   highcharts\n * @apioption series.treemap.data.colorValue\n */\n/**\n * Only for treemap. Use this option to build a tree structure. The\n * value should be the id of the point which is the parent. If no points\n * has a matching id, or this option is undefined, then the parent will\n * be set to the root.\n *\n * @sample {highcharts} highcharts/point/parent/\n *         Point parent\n * @sample {highcharts} highcharts/demo/treemap-with-levels/\n *         Example where parent id is not matching\n *\n * @type      {string}\n * @since     4.1.0\n * @product   highcharts\n * @apioption series.treemap.data.parent\n */\n''; // Keeps doclets above detached\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treemap_TreemapSeriesDefaults = (TreemapSeriesDefaults);\n\n;// ./code/es-modules/Series/Treemap/TreemapUtilities.js\n/* *\n *\n *  (c) 2014-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Namespace\n *\n * */\nvar TreemapUtilities;\n(function (TreemapUtilities) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @todo find correct name for this function.\n     * @todo Similar to reduce, this function is likely redundant\n     */\n    function recursive(item, func, context) {\n        const next = func.call(context || this, item);\n        if (next !== false) {\n            recursive(next, func, context);\n        }\n    }\n    TreemapUtilities.recursive = recursive;\n})(TreemapUtilities || (TreemapUtilities = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treemap_TreemapUtilities = (TreemapUtilities);\n\n;// ./code/es-modules/Series/TreeUtilities.js\n/* *\n *\n *  (c) 2014-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { extend: TreeUtilities_extend, isArray, isNumber: TreeUtilities_isNumber, isObject, merge: TreeUtilities_merge, pick: TreeUtilities_pick, relativeLength } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/* eslint-disable valid-jsdoc */\n/**\n * @private\n */\nfunction getColor(node, options) {\n    const index = options.index, mapOptionsToLevel = options.mapOptionsToLevel, parentColor = options.parentColor, parentColorIndex = options.parentColorIndex, series = options.series, colors = options.colors, siblings = options.siblings, points = series.points, chartOptionsChart = series.chart.options.chart;\n    let getColorByPoint, point, level, colorByPoint, colorIndexByPoint, color, colorIndex;\n    /**\n     * @private\n     */\n    const variateColor = (color) => {\n        const colorVariation = level && level.colorVariation;\n        if (colorVariation &&\n            colorVariation.key === 'brightness' &&\n            index &&\n            siblings) {\n            return highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default().parse(color).brighten(colorVariation.to * (index / siblings)).get();\n        }\n        return color;\n    };\n    if (node) {\n        point = points[node.i];\n        level = mapOptionsToLevel[node.level] || {};\n        getColorByPoint = point && level.colorByPoint;\n        if (getColorByPoint) {\n            colorIndexByPoint = point.index % (colors ?\n                colors.length :\n                chartOptionsChart.colorCount);\n            colorByPoint = colors && colors[colorIndexByPoint];\n        }\n        // Select either point color, level color or inherited color.\n        if (!series.chart.styledMode) {\n            color = TreeUtilities_pick(point && point.options.color, level && level.color, colorByPoint, parentColor && variateColor(parentColor), series.color);\n        }\n        colorIndex = TreeUtilities_pick(point && point.options.colorIndex, level && level.colorIndex, colorIndexByPoint, parentColorIndex, options.colorIndex);\n    }\n    return {\n        color: color,\n        colorIndex: colorIndex\n    };\n}\n/**\n * Creates a map from level number to its given options.\n *\n * @private\n *\n * @param {Object} params\n * Object containing parameters.\n * - `defaults` Object containing default options. The default options are\n *   merged with the userOptions to get the final options for a specific\n *   level.\n * - `from` The lowest level number.\n * - `levels` User options from series.levels.\n * - `to` The highest level number.\n *\n * @return {Highcharts.Dictionary<object>|null}\n * Returns a map from level number to its given options.\n */\nfunction getLevelOptions(params) {\n    const result = {};\n    let defaults, converted, i, from, to, levels;\n    if (isObject(params)) {\n        from = TreeUtilities_isNumber(params.from) ? params.from : 1;\n        levels = params.levels;\n        converted = {};\n        defaults = isObject(params.defaults) ? params.defaults : {};\n        if (isArray(levels)) {\n            converted = levels.reduce((obj, item) => {\n                let level, levelIsConstant, options;\n                if (isObject(item) && TreeUtilities_isNumber(item.level)) {\n                    options = TreeUtilities_merge({}, item);\n                    levelIsConstant = TreeUtilities_pick(options.levelIsConstant, defaults.levelIsConstant);\n                    // Delete redundant properties.\n                    delete options.levelIsConstant;\n                    delete options.level;\n                    // Calculate which level these options apply to.\n                    level = item.level + (levelIsConstant ? 0 : from - 1);\n                    if (isObject(obj[level])) {\n                        TreeUtilities_merge(true, obj[level], options); // #16329\n                    }\n                    else {\n                        obj[level] = options;\n                    }\n                }\n                return obj;\n            }, {});\n        }\n        to = TreeUtilities_isNumber(params.to) ? params.to : 1;\n        for (i = 0; i <= to; i++) {\n            result[i] = TreeUtilities_merge({}, defaults, isObject(converted[i]) ? converted[i] : {});\n        }\n    }\n    return result;\n}\n/**\n * @private\n * @todo Combine buildTree and buildNode with setTreeValues\n * @todo Remove logic from Treemap and make it utilize this mixin.\n */\nfunction setTreeValues(tree, options) {\n    const before = options.before, idRoot = options.idRoot, mapIdToNode = options.mapIdToNode, nodeRoot = mapIdToNode[idRoot], levelIsConstant = (options.levelIsConstant !== false), points = options.points, point = points[tree.i], optionsPoint = point && point.options || {}, children = [];\n    let childrenTotal = 0;\n    tree.levelDynamic = tree.level - (levelIsConstant ? 0 : nodeRoot.level);\n    tree.name = TreeUtilities_pick(point && point.name, '');\n    tree.visible = (idRoot === tree.id ||\n        options.visible === true);\n    if (typeof before === 'function') {\n        tree = before(tree, options);\n    }\n    // First give the children some values\n    tree.children.forEach((child, i) => {\n        const newOptions = TreeUtilities_extend({}, options);\n        TreeUtilities_extend(newOptions, {\n            index: i,\n            siblings: tree.children.length,\n            visible: tree.visible\n        });\n        child = setTreeValues(child, newOptions);\n        children.push(child);\n        if (child.visible) {\n            childrenTotal += child.val;\n        }\n    });\n    // Set the values\n    const value = TreeUtilities_pick(optionsPoint.value, childrenTotal);\n    tree.visible = value >= 0 && (childrenTotal > 0 || tree.visible);\n    tree.children = children;\n    tree.childrenTotal = childrenTotal;\n    tree.isLeaf = tree.visible && !childrenTotal;\n    tree.val = value;\n    return tree;\n}\n/**\n * Update the rootId property on the series. Also makes sure that it is\n * accessible to exporting.\n *\n * @private\n *\n * @param {Object} series\n * The series to operate on.\n *\n * @return {string}\n * Returns the resulting rootId after update.\n */\nfunction updateRootId(series) {\n    let rootId, options;\n    if (isObject(series)) {\n        // Get the series options.\n        options = isObject(series.options) ? series.options : {};\n        // Calculate the rootId.\n        rootId = TreeUtilities_pick(series.rootNode, options.rootId, '');\n        // Set rootId on series.userOptions to pick it up in exporting.\n        if (isObject(series.userOptions)) {\n            series.userOptions.rootId = rootId;\n        }\n        // Set rootId on series to pick it up on next update.\n        series.rootNode = rootId;\n    }\n    return rootId;\n}\n/**\n * Get the node width, which relies on the plot width and the nodeDistance\n * option.\n *\n * @private\n */\nfunction getNodeWidth(series, columnCount) {\n    const { chart, options } = series, { nodeDistance = 0, nodeWidth = 0 } = options, { plotSizeX = 1 } = chart;\n    // Node width auto means they are evenly distributed along the width of\n    // the plot area\n    if (nodeWidth === 'auto') {\n        if (typeof nodeDistance === 'string' && /%$/.test(nodeDistance)) {\n            const fraction = parseFloat(nodeDistance) / 100, total = columnCount + fraction * (columnCount - 1);\n            return plotSizeX / total;\n        }\n        const nDistance = Number(nodeDistance);\n        return ((plotSizeX + nDistance) /\n            (columnCount || 1)) - nDistance;\n    }\n    return relativeLength(nodeWidth, plotSizeX);\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst TreeUtilities = {\n    getColor,\n    getLevelOptions,\n    getNodeWidth,\n    setTreeValues,\n    updateRootId\n};\n/* harmony default export */ const Series_TreeUtilities = (TreeUtilities);\n\n;// ./code/es-modules/Series/Treemap/TreemapSeries.js\n/* *\n *\n *  (c) 2014-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { parse: color } = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default());\n\n\nconst { composed: TreemapSeries_composed, noop } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nconst { column: ColumnSeries, scatter: ScatterSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\n\n\n\n\n\nconst { getColor: TreemapSeries_getColor, getLevelOptions: TreemapSeries_getLevelOptions, updateRootId: TreemapSeries_updateRootId } = Series_TreeUtilities;\n\nconst { addEvent: TreemapSeries_addEvent, arrayMax, clamp, correctFloat, crisp, defined: TreemapSeries_defined, error, extend: TreemapSeries_extend, fireEvent: TreemapSeries_fireEvent, isArray: TreemapSeries_isArray, isNumber: TreemapSeries_isNumber, isObject: TreemapSeries_isObject, isString: TreemapSeries_isString, merge: TreemapSeries_merge, pick: TreemapSeries_pick, pushUnique: TreemapSeries_pushUnique, splat, stableSort } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nhighcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default().keepProps.push('simulation', 'hadOutsideDataLabels');\n/* *\n *\n *  Constants\n *\n * */\nconst axisMax = 100;\n/* *\n *\n *  Variables\n *\n * */\nlet treemapAxisDefaultValues = false;\n/* *\n *\n *  Functions\n *\n * */\n/** @private */\nfunction onSeriesAfterBindAxes() {\n    const series = this, xAxis = series.xAxis, yAxis = series.yAxis;\n    let treeAxis;\n    if (xAxis && yAxis) {\n        if (series.is('treemap')) {\n            treeAxis = {\n                endOnTick: false,\n                gridLineWidth: 0,\n                lineWidth: 0,\n                min: 0,\n                minPadding: 0,\n                max: axisMax,\n                maxPadding: 0,\n                startOnTick: false,\n                title: void 0,\n                tickPositions: []\n            };\n            TreemapSeries_extend(yAxis.options, treeAxis);\n            TreemapSeries_extend(xAxis.options, treeAxis);\n            treemapAxisDefaultValues = true;\n        }\n        else if (treemapAxisDefaultValues) {\n            yAxis.setOptions(yAxis.userOptions);\n            xAxis.setOptions(xAxis.userOptions);\n            treemapAxisDefaultValues = false;\n        }\n    }\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.treemap\n *\n * @augments Highcharts.Series\n */\nclass TreemapSeries extends ScatterSeries {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        this.simulation = 0;\n        /* eslint-enable valid-jsdoc */\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static compose(SeriesClass) {\n        if (TreemapSeries_pushUnique(TreemapSeries_composed, 'TreemapSeries')) {\n            TreemapSeries_addEvent(SeriesClass, 'afterBindAxes', onSeriesAfterBindAxes);\n        }\n    }\n    /* *\n     *\n     *  Function\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    algorithmCalcPoints(directionChange, last, group, childrenArea) {\n        const plot = group.plot, end = group.elArr.length - 1;\n        let pX, pY, pW, pH, gW = group.lW, gH = group.lH, keep, i = 0;\n        if (last) {\n            gW = group.nW;\n            gH = group.nH;\n        }\n        else {\n            keep = group.elArr[end];\n        }\n        for (const p of group.elArr) {\n            if (last || (i < end)) {\n                if (group.direction === 0) {\n                    pX = plot.x;\n                    pY = plot.y;\n                    pW = gW;\n                    pH = p / pW;\n                }\n                else {\n                    pX = plot.x;\n                    pY = plot.y;\n                    pH = gH;\n                    pW = p / pH;\n                }\n                childrenArea.push({\n                    x: pX,\n                    y: pY,\n                    width: pW,\n                    height: correctFloat(pH)\n                });\n                if (group.direction === 0) {\n                    plot.y = plot.y + pH;\n                }\n                else {\n                    plot.x = plot.x + pW;\n                }\n            }\n            i = i + 1;\n        }\n        // Reset variables\n        group.reset();\n        if (group.direction === 0) {\n            group.width = group.width - gW;\n        }\n        else {\n            group.height = group.height - gH;\n        }\n        plot.y = plot.parent.y + (plot.parent.height - group.height);\n        plot.x = plot.parent.x + (plot.parent.width - group.width);\n        if (directionChange) {\n            group.direction = 1 - group.direction;\n        }\n        // If not last, then add uncalculated element\n        if (!last) {\n            group.addElement(keep);\n        }\n    }\n    algorithmFill(directionChange, parent, children) {\n        const childrenArea = [];\n        let pTot, direction = parent.direction, x = parent.x, y = parent.y, width = parent.width, height = parent.height, pX, pY, pW, pH;\n        for (const child of children) {\n            pTot =\n                (parent.width * parent.height) * (child.val / parent.val);\n            pX = x;\n            pY = y;\n            if (direction === 0) {\n                pH = height;\n                pW = pTot / pH;\n                width = width - pW;\n                x = x + pW;\n            }\n            else {\n                pW = width;\n                pH = pTot / pW;\n                height = height - pH;\n                y = y + pH;\n            }\n            childrenArea.push({\n                x: pX,\n                y: pY,\n                width: pW,\n                height: pH,\n                direction: 0,\n                val: 0\n            });\n            if (directionChange) {\n                direction = 1 - direction;\n            }\n        }\n        return childrenArea;\n    }\n    algorithmLowAspectRatio(directionChange, parent, children) {\n        const series = this, childrenArea = [], plot = {\n            x: parent.x,\n            y: parent.y,\n            parent: parent\n        }, direction = parent.direction, end = children.length - 1, group = new Treemap_TreemapAlgorithmGroup(parent.height, parent.width, direction, plot);\n        let pTot, i = 0;\n        // Loop through and calculate all areas\n        for (const child of children) {\n            pTot =\n                (parent.width * parent.height) * (child.val / parent.val);\n            group.addElement(pTot);\n            if (group.lP.nR > group.lP.lR) {\n                series.algorithmCalcPoints(directionChange, false, group, childrenArea, plot // @todo no supported\n                );\n            }\n            // If last child, then calculate all remaining areas\n            if (i === end) {\n                series.algorithmCalcPoints(directionChange, true, group, childrenArea, plot // @todo not supported\n                );\n            }\n            ++i;\n        }\n        return childrenArea;\n    }\n    /**\n     * Over the alignment method by setting z index.\n     * @private\n     */\n    alignDataLabel(point, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    dataLabel, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    labelOptions) {\n        ColumnSeries.prototype.alignDataLabel.apply(this, arguments);\n        if (point.dataLabel) {\n            // `point.node.zIndex` could be undefined (#6956)\n            point.dataLabel.attr({ zIndex: (point.node.zIndex || 0) + 1 });\n        }\n    }\n    applyTreeGrouping() {\n        const series = this, parentList = series.parentList || {}, { cluster } = series.options, minimumClusterSize = cluster?.minimumClusterSize || 5;\n        if (cluster?.enabled) {\n            const parentGroups = {};\n            const checkIfHide = (node) => {\n                if (node?.point?.shapeArgs) {\n                    const { width = 0, height = 0 } = node.point.shapeArgs, area = width * height;\n                    const { pixelWidth = 0, pixelHeight = 0 } = cluster, compareHeight = TreemapSeries_defined(pixelHeight), thresholdArea = pixelHeight ?\n                        pixelWidth * pixelHeight :\n                        pixelWidth * pixelWidth;\n                    if (width < pixelWidth ||\n                        height < (compareHeight ? pixelHeight : pixelWidth) ||\n                        area < thresholdArea) {\n                        if (!node.isGroup && TreemapSeries_defined(node.parent)) {\n                            if (!parentGroups[node.parent]) {\n                                parentGroups[node.parent] = [];\n                            }\n                            parentGroups[node.parent].push(node);\n                        }\n                    }\n                }\n                node?.children.forEach((child) => {\n                    checkIfHide(child);\n                });\n            };\n            checkIfHide(series.tree);\n            for (const parent in parentGroups) {\n                if (parentGroups[parent]) {\n                    if (parentGroups[parent].length > minimumClusterSize) {\n                        parentGroups[parent].forEach((node) => {\n                            const index = parentList[parent].indexOf(node.i);\n                            if (index !== -1) {\n                                parentList[parent].splice(index, 1);\n                                const id = `highcharts-grouped-treemap-points-${node.parent || 'root'}`;\n                                let groupPoint = series.points\n                                    .find((p) => p.id === id);\n                                if (!groupPoint) {\n                                    const PointClass = series.pointClass, pointIndex = series.points.length;\n                                    groupPoint = new PointClass(series, {\n                                        className: cluster.className,\n                                        color: cluster.color,\n                                        id,\n                                        index: pointIndex,\n                                        isGroup: true,\n                                        value: 0\n                                    });\n                                    TreemapSeries_extend(groupPoint, {\n                                        formatPrefix: 'cluster'\n                                    });\n                                    series.points.push(groupPoint);\n                                    parentList[parent].push(pointIndex);\n                                    parentList[id] = [];\n                                }\n                                const amount = groupPoint.groupedPointsAmount + 1, val = series.points[groupPoint.index]\n                                    .options.value || 0, name = cluster.name ||\n                                    `+ ${amount}`;\n                                // Update the point directly in points array to\n                                // prevent wrong instance update\n                                series.points[groupPoint.index]\n                                    .groupedPointsAmount = amount;\n                                series.points[groupPoint.index].options.value =\n                                    val + (node.point.value || 0);\n                                series.points[groupPoint.index].name = name;\n                                parentList[id].push(node.point.index);\n                            }\n                        });\n                    }\n                }\n            }\n            series.nodeMap = {};\n            series.nodeList = [];\n            series.parentList = parentList;\n            const tree = series.buildTree('', -1, 0, series.parentList);\n            series.translate(tree);\n        }\n    }\n    /**\n     * Recursive function which calculates the area for all children of a\n     * node.\n     *\n     * @private\n     * @function Highcharts.Series#calculateChildrenAreas\n     *\n     * @param {Object} parent\n     * The node which is parent to the children.\n     *\n     * @param {Object} area\n     * The rectangular area of the parent.\n     */\n    calculateChildrenAreas(parent, area) {\n        const series = this, options = series.options, mapOptionsToLevel = series.mapOptionsToLevel, level = mapOptionsToLevel[parent.level + 1], algorithm = TreemapSeries_pick((level?.layoutAlgorithm &&\n            series[level?.layoutAlgorithm] &&\n            level.layoutAlgorithm), options.layoutAlgorithm), alternate = options.alternateStartingDirection, \n        // Collect all children which should be included\n        children = parent.children.filter((n) => parent.isGroup || !n.ignore), groupPadding = level?.groupPadding ?? options.groupPadding ?? 0, rootNode = series.nodeMap[series.rootNode];\n        if (!algorithm) {\n            return;\n        }\n        let childrenValues = [], axisWidth = rootNode.pointValues?.width || 0, axisHeight = rootNode.pointValues?.height || 0;\n        if (level?.layoutStartingDirection) {\n            area.direction = level.layoutStartingDirection === 'vertical' ?\n                0 :\n                1;\n        }\n        childrenValues = series[algorithm](area, children);\n        let i = -1;\n        for (const child of children) {\n            const values = childrenValues[++i];\n            if (child === rootNode) {\n                axisWidth = axisWidth || values.width;\n                axisHeight = values.height;\n            }\n            const groupPaddingXValues = groupPadding / (series.xAxis.len / axisHeight), groupPaddingYValues = groupPadding / (series.yAxis.len / axisHeight);\n            child.values = TreemapSeries_merge(values, {\n                val: child.childrenTotal,\n                direction: (alternate ? 1 - area.direction : area.direction)\n            });\n            // Make room for outside data labels\n            if (child.children.length &&\n                child.point.dataLabels?.length) {\n                const dlHeight = arrayMax(child.point.dataLabels.map((dl) => dl.options\n                    ?.headers && dl.height || 0)) / (series.yAxis.len / axisHeight);\n                // Make room for data label unless the group is too small\n                if (dlHeight < child.values.height / 2) {\n                    child.values.y += dlHeight;\n                    child.values.height -= dlHeight;\n                }\n            }\n            if (groupPadding) {\n                const xPad = Math.min(groupPaddingXValues, child.values.width / 4), yPad = Math.min(groupPaddingYValues, child.values.height / 4);\n                child.values.x += xPad;\n                child.values.width -= 2 * xPad;\n                child.values.y += yPad;\n                child.values.height -= 2 * yPad;\n            }\n            child.pointValues = TreemapSeries_merge(values, {\n                x: (values.x / series.axisRatio),\n                // Flip y-values to avoid visual regression with csvCoord in\n                // Axis.translate at setPointValues. #12488\n                y: axisMax - values.y - values.height,\n                width: (values.width / series.axisRatio)\n            });\n            // If node has children, then call method recursively\n            if (child.children.length) {\n                series.calculateChildrenAreas(child, child.values);\n            }\n        }\n        const getChildrenRecursive = (node, result = [], getLeaves = true) => {\n            node.children.forEach((child) => {\n                if (getLeaves && child.isLeaf) {\n                    result.push(child.point);\n                }\n                else if (!getLeaves && !child.isLeaf) {\n                    result.push(child.point);\n                }\n                if (child.children.length) {\n                    getChildrenRecursive(child, result, getLeaves);\n                }\n            });\n            return result;\n        };\n        // Experimental block to make space for the outside data labels\n        if (options.nodeSizeBy === 'leaf' &&\n            parent === rootNode &&\n            this.hasOutsideDataLabels &&\n            // Sizing by leaf value is not possible if any of the groups have\n            // explicit values\n            !getChildrenRecursive(rootNode, void 0, false)\n                .some((point) => TreemapSeries_isNumber(point.options.value)) &&\n            !TreemapSeries_isNumber(rootNode.point?.options.value)) {\n            const leaves = getChildrenRecursive(rootNode), values = leaves.map((point) => point.options.value || 0), \n            // Areas in terms of axis units squared\n            areas = leaves.map(({ node: { pointValues } }) => (pointValues ?\n                pointValues.width * pointValues.height :\n                0)), valueSum = values.reduce((sum, value) => sum + value, 0), areaSum = areas.reduce((sum, value) => sum + value, 0), expectedAreaPerValue = areaSum / valueSum;\n            let minMiss = 0, maxMiss = 0;\n            leaves.forEach((point, i) => {\n                const areaPerValue = values[i] ? (areas[i] / values[i]) : 1, \n                // Less than 1 => rendered too small, greater than 1 =>\n                // rendered too big\n                fit = clamp(areaPerValue / expectedAreaPerValue, 0.8, 1.4);\n                let miss = 1 - fit;\n                if (point.value) {\n                    // Very small areas are more sensitive, and matter less to\n                    // the visual impression. Give them less weight.\n                    if (areas[i] < 20) {\n                        miss *= areas[i] / 20;\n                    }\n                    if (miss > maxMiss) {\n                        maxMiss = miss;\n                    }\n                    if (miss < minMiss) {\n                        minMiss = miss;\n                    }\n                    point.simulatedValue = (point.simulatedValue || point.value) / fit;\n                }\n            });\n            /* /\n            console.log('--- simulation',\n                this.simulation,\n                'worstMiss',\n                minMiss,\n                maxMiss\n            );\n            // */\n            if (\n            // An area error less than 5% is acceptable, the human ability\n            // to assess area size is not that accurate\n            (minMiss < -0.05 || maxMiss > 0.05) &&\n                // In case an eternal loop is brewing, pull the emergency brake\n                this.simulation < 10) {\n                this.simulation++;\n                this.setTreeValues(parent);\n                area.val = parent.val;\n                this.calculateChildrenAreas(parent, area);\n                // Simulation is settled, proceed to rendering. Reset the simulated\n                // values and set the tree values with real data.\n            }\n            else {\n                leaves.forEach((point) => {\n                    delete point.simulatedValue;\n                });\n                this.setTreeValues(parent);\n                this.simulation = 0;\n            }\n        }\n    }\n    /**\n     * Create level list.\n     * @private\n     */\n    createList(e) {\n        const chart = this.chart, breadcrumbs = chart.breadcrumbs, list = [];\n        if (breadcrumbs) {\n            let currentLevelNumber = 0;\n            list.push({\n                level: currentLevelNumber,\n                levelOptions: chart.series[0]\n            });\n            let node = e.target.nodeMap[e.newRootId];\n            const extraNodes = [];\n            // When the root node is set and has parent,\n            // recreate the path from the node tree.\n            while (node.parent || node.parent === '') {\n                extraNodes.push(node);\n                node = e.target.nodeMap[node.parent];\n            }\n            for (const node of extraNodes.reverse()) {\n                list.push({\n                    level: ++currentLevelNumber,\n                    levelOptions: node\n                });\n            }\n            // If the list has only first element, we should clear it\n            if (list.length <= 1) {\n                list.length = 0;\n            }\n        }\n        return list;\n    }\n    /**\n     * Extend drawDataLabels with logic to handle custom options related to\n     * the treemap series:\n     *\n     * - Points which is not a leaf node, has dataLabels disabled by\n     *   default.\n     *\n     * - Options set on series.levels is merged in.\n     *\n     * - Width of the dataLabel is set to match the width of the point\n     *   shape.\n     *\n     * @private\n     */\n    drawDataLabels() {\n        const series = this, mapOptionsToLevel = series.mapOptionsToLevel, points = series.points.filter(function (n) {\n            return n.node.visible || TreemapSeries_defined(n.dataLabel);\n        }), padding = splat(series.options.dataLabels || {})[0]?.padding, positionsAreSet = points.some((p) => TreemapSeries_isNumber(p.plotY));\n        for (const point of points) {\n            const style = {}, \n            // Set options to new object to avoid problems with scope\n            options = { style }, level = mapOptionsToLevel[point.node.level];\n            // If not a leaf, then label should be disabled as default\n            if (!point.node.isLeaf &&\n                !point.node.isGroup ||\n                (point.node.isGroup &&\n                    point.node.level <= series.nodeMap[series.rootNode].level)) {\n                options.enabled = false;\n            }\n            // If options for level exists, include them as well\n            if (level?.dataLabels) {\n                TreemapSeries_merge(true, options, splat(level.dataLabels)[0]);\n                series.hasDataLabels = () => true;\n            }\n            // Headers are always top-aligned. Leaf nodes no not support\n            // headers.\n            if (point.node.isLeaf) {\n                options.inside = true;\n            }\n            else if (options.headers) {\n                options.verticalAlign = 'top';\n            }\n            // Set dataLabel width to the width of the point shape minus the\n            // padding\n            if (point.shapeArgs && positionsAreSet) {\n                const { height = 0, width = 0 } = point.shapeArgs;\n                if (width > 32 && height > 16 && point.shouldDraw()) {\n                    const dataLabelWidth = width -\n                        2 * (options.padding || padding || 0);\n                    style.width = `${dataLabelWidth}px`;\n                    style.lineClamp ?? (style.lineClamp = Math.floor(height / 16));\n                    style.visibility = 'inherit';\n                    // Make the label box itself fill the width\n                    if (options.headers) {\n                        point.dataLabel?.attr({\n                            width: dataLabelWidth\n                        });\n                    }\n                    // Hide labels for shapes that are too small\n                }\n                else {\n                    style.width = `${width}px`;\n                    style.visibility = 'hidden';\n                }\n            }\n            // Merge custom options with point options\n            point.dlOptions = TreemapSeries_merge(options, point.options.dataLabels);\n        }\n        super.drawDataLabels(points);\n    }\n    /**\n     * Override drawPoints\n     * @private\n     */\n    drawPoints(points = this.points) {\n        const series = this, chart = series.chart, renderer = chart.renderer, styledMode = chart.styledMode, options = series.options, shadow = styledMode ? {} : options.shadow, borderRadius = options.borderRadius, withinAnimationLimit = chart.pointCount < options.animationLimit, allowTraversingTree = options.allowTraversingTree;\n        for (const point of points) {\n            const levelDynamic = point.node.levelDynamic, animatableAttribs = {}, attribs = {}, css = {}, groupKey = 'level-group-' + point.node.level, hasGraphic = !!point.graphic, shouldAnimate = withinAnimationLimit && hasGraphic, shapeArgs = point.shapeArgs;\n            // Don't bother with calculate styling if the point is not drawn\n            if (point.shouldDraw()) {\n                point.isInside = true;\n                if (borderRadius) {\n                    attribs.r = borderRadius;\n                }\n                TreemapSeries_merge(true, // Extend object\n                // Which object to extend\n                shouldAnimate ? animatableAttribs : attribs, \n                // Add shapeArgs to animate/attr if graphic exists\n                hasGraphic ? shapeArgs : {}, \n                // Add style attribs if !styleMode\n                styledMode ?\n                    {} :\n                    series.pointAttribs(point, point.selected ? 'select' : void 0));\n                // In styled mode apply point.color. Use CSS, otherwise the\n                // fill used in the style sheet will take precedence over\n                // the fill attribute.\n                if (series.colorAttribs && styledMode) {\n                    // Heatmap is loaded\n                    TreemapSeries_extend(css, series.colorAttribs(point));\n                }\n                if (!series[groupKey]) {\n                    series[groupKey] = renderer.g(groupKey)\n                        .attr({\n                        // @todo Set the zIndex based upon the number of\n                        // levels, instead of using 1000\n                        zIndex: 1000 - (levelDynamic || 0)\n                    })\n                        .add(series.group);\n                    series[groupKey].survive = true;\n                }\n            }\n            // Draw the point\n            point.draw({\n                animatableAttribs,\n                attribs,\n                css,\n                group: series[groupKey],\n                imageUrl: point.imageUrl,\n                renderer,\n                shadow,\n                shapeArgs,\n                shapeType: point.shapeType\n            });\n            // If setRootNode is allowed, set a point cursor on clickables &\n            // add drillId to point\n            if (allowTraversingTree && point.graphic) {\n                point.drillId = options.interactByLeaf ?\n                    series.drillToByLeaf(point) :\n                    series.drillToByGroup(point);\n            }\n        }\n    }\n    /**\n     * Finds the drill id for a parent node. Returns false if point should\n     * not have a click event.\n     * @private\n     */\n    drillToByGroup(point) {\n        return (!point.node.isLeaf || point.node.isGroup) ?\n            point.id : false;\n    }\n    /**\n     * Finds the drill id for a leaf node. Returns false if point should not\n     * have a click event\n     * @private\n     */\n    drillToByLeaf(point) {\n        const { traverseToLeaf } = point.series.options;\n        let drillId = false, nodeParent;\n        if ((point.node.parent !== this.rootNode) &&\n            point.node.isLeaf) {\n            if (traverseToLeaf) {\n                drillId = point.id;\n            }\n            else {\n                nodeParent = point.node;\n                while (!drillId) {\n                    if (typeof nodeParent.parent !== 'undefined') {\n                        nodeParent = this.nodeMap[nodeParent.parent];\n                    }\n                    if (nodeParent.parent === this.rootNode) {\n                        drillId = nodeParent.id;\n                    }\n                }\n            }\n        }\n        return drillId;\n    }\n    /**\n     * @todo remove this function at a suitable version.\n     * @private\n     */\n    drillToNode(id, redraw) {\n        error(32, false, void 0, { 'treemap.drillToNode': 'use treemap.setRootNode' });\n        this.setRootNode(id, redraw);\n    }\n    drillUp() {\n        const series = this, node = series.nodeMap[series.rootNode];\n        if (node && TreemapSeries_isString(node.parent)) {\n            series.setRootNode(node.parent, true, { trigger: 'traverseUpButton' });\n        }\n    }\n    getExtremes() {\n        // Get the extremes from the value data\n        const { dataMin, dataMax } = super.getExtremes(this.colorValueData);\n        this.valueMin = dataMin;\n        this.valueMax = dataMax;\n        // Get the extremes from the y data\n        return super.getExtremes();\n    }\n    /**\n     * Creates an object map from parent id to childrens index.\n     *\n     * @private\n     * @function Highcharts.Series#getListOfParents\n     *\n     * @param {Highcharts.SeriesTreemapDataOptions} [data]\n     *        List of points set in options.\n     *\n     * @param {Array<string>} [existingIds]\n     *        List of all point ids.\n     *\n     * @return {Object}\n     *         Map from parent id to children index in data.\n     */\n    getListOfParents(data, existingIds) {\n        const arr = TreemapSeries_isArray(data) ? data : [], ids = TreemapSeries_isArray(existingIds) ? existingIds : [], listOfParents = arr.reduce(function (prev, curr, i) {\n            const parent = TreemapSeries_pick(curr.parent, '');\n            if (typeof prev[parent] === 'undefined') {\n                prev[parent] = [];\n            }\n            prev[parent].push(i);\n            return prev;\n        }, {\n            '': [] // Root of tree\n        });\n        // If parent does not exist, hoist parent to root of tree.\n        for (const parent of Object.keys(listOfParents)) {\n            const children = listOfParents[parent];\n            if ((parent !== '') && (ids.indexOf(parent) === -1)) {\n                for (const child of children) {\n                    listOfParents[''].push(child);\n                }\n                delete listOfParents[parent];\n            }\n        }\n        return listOfParents;\n    }\n    /**\n     * Creates a tree structured object from the series points.\n     * @private\n     */\n    getTree() {\n        const series = this, allIds = this.data.map(function (d) {\n            return d.id;\n        });\n        series.parentList = series.getListOfParents(this.data, allIds);\n        series.nodeMap = {};\n        series.nodeList = [];\n        return series.buildTree('', -1, 0, series.parentList || {});\n    }\n    buildTree(id, index, level, list, parent) {\n        const series = this, children = [], point = series.points[index];\n        let height = 0, child;\n        // Actions\n        for (const i of (list[id] || [])) {\n            child = series.buildTree(series.points[i].id, i, level + 1, list, id);\n            height = Math.max(child.height + 1, height);\n            children.push(child);\n        }\n        const node = new series.NodeClass().init(id, index, children, height, level, series, parent);\n        for (const child of children) {\n            child.parentNode = node;\n        }\n        series.nodeMap[node.id] = node;\n        series.nodeList.push(node);\n        if (point) {\n            point.node = node;\n            node.point = point;\n        }\n        return node;\n    }\n    /**\n     * Define hasData function for non-cartesian series. Returns true if the\n     * series has points at all.\n     * @private\n     */\n    hasData() {\n        return !!this.dataTable.rowCount;\n    }\n    init(chart, options) {\n        const series = this, breadcrumbsOptions = TreemapSeries_merge(options.drillUpButton, options.breadcrumbs), setOptionsEvent = TreemapSeries_addEvent(series, 'setOptions', (event) => {\n            const options = event.userOptions;\n            // Deprecated options\n            if (TreemapSeries_defined(options.allowDrillToNode) &&\n                !TreemapSeries_defined(options.allowTraversingTree)) {\n                options.allowTraversingTree = options.allowDrillToNode;\n                delete options.allowDrillToNode;\n            }\n            if (TreemapSeries_defined(options.drillUpButton) &&\n                !TreemapSeries_defined(options.traverseUpButton)) {\n                options.traverseUpButton = options.drillUpButton;\n                delete options.drillUpButton;\n            }\n            // Check if we need to reserve space for headers\n            const dataLabels = splat(options.dataLabels || {});\n            options.levels?.forEach((level) => {\n                dataLabels.push.apply(dataLabels, splat(level.dataLabels || {}));\n            });\n            this.hasOutsideDataLabels = dataLabels.some((dl) => dl.headers);\n        });\n        super.init(chart, options);\n        // Treemap's opacity is a different option from other series\n        delete series.opacity;\n        // Handle deprecated options.\n        series.eventsToUnbind.push(setOptionsEvent);\n        if (series.options.allowTraversingTree) {\n            series.eventsToUnbind.push(TreemapSeries_addEvent(series, 'click', series.onClickDrillToNode));\n            series.eventsToUnbind.push(TreemapSeries_addEvent(series, 'setRootNode', function (e) {\n                const chart = series.chart;\n                if (chart.breadcrumbs) {\n                    // Create a list using the event after drilldown.\n                    chart.breadcrumbs.updateProperties(series.createList(e));\n                }\n            }));\n            series.eventsToUnbind.push(TreemapSeries_addEvent(series, 'update', \n            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n            function (e, redraw) {\n                const breadcrumbs = this.chart.breadcrumbs;\n                if (breadcrumbs && e.options.breadcrumbs) {\n                    breadcrumbs.update(e.options.breadcrumbs);\n                }\n                this.hadOutsideDataLabels = this.hasOutsideDataLabels;\n            }));\n            series.eventsToUnbind.push(TreemapSeries_addEvent(series, 'destroy', function destroyEvents(e) {\n                const chart = this.chart;\n                if (chart.breadcrumbs && !e.keepEventsForUpdate) {\n                    chart.breadcrumbs.destroy();\n                    chart.breadcrumbs = void 0;\n                }\n            }));\n        }\n        if (!chart.breadcrumbs) {\n            chart.breadcrumbs = new Breadcrumbs_Breadcrumbs(chart, breadcrumbsOptions);\n        }\n        series.eventsToUnbind.push(TreemapSeries_addEvent(chart.breadcrumbs, 'up', function (e) {\n            const drillUpsNumber = this.level - e.newLevel;\n            for (let i = 0; i < drillUpsNumber; i++) {\n                series.drillUp();\n            }\n        }));\n    }\n    /**\n     * Add drilling on the suitable points.\n     * @private\n     */\n    onClickDrillToNode(event) {\n        const series = this, point = event.point, drillId = point?.drillId;\n        // If a drill id is returned, add click event and cursor.\n        if (TreemapSeries_isString(drillId)) {\n            point.setState(''); // Remove hover\n            series.setRootNode(drillId, true, { trigger: 'click' });\n        }\n    }\n    /**\n     * Get presentational attributes\n     * @private\n     */\n    pointAttribs(point, state) {\n        const series = this, mapOptionsToLevel = (TreemapSeries_isObject(series.mapOptionsToLevel) ?\n            series.mapOptionsToLevel :\n            {}), level = point && mapOptionsToLevel[point.node.level] || {}, options = this.options, stateOptions = state && options.states && options.states[state] || {}, className = point?.getClassName() || '', \n        // Set attributes by precedence. Point trumps level trumps series.\n        // Stroke width uses pick because it can be 0.\n        attr = {\n            'stroke': (point && point.borderColor) ||\n                level.borderColor ||\n                stateOptions.borderColor ||\n                options.borderColor,\n            'stroke-width': TreemapSeries_pick(point && point.borderWidth, level.borderWidth, stateOptions.borderWidth, options.borderWidth),\n            'dashstyle': point?.borderDashStyle ||\n                level.borderDashStyle ||\n                stateOptions.borderDashStyle ||\n                options.borderDashStyle,\n            'fill': point?.color || this.color\n        };\n        // Hide levels above the current view\n        if (className.indexOf('highcharts-above-level') !== -1) {\n            attr.fill = 'none';\n            attr['stroke-width'] = 0;\n            // Nodes with children that accept interaction\n        }\n        else if (className.indexOf('highcharts-internal-node-interactive') !== -1) {\n            attr['fill-opacity'] = stateOptions.opacity ?? options.opacity ?? 1;\n            attr.cursor = 'pointer';\n            // Hide nodes that have children\n        }\n        else if (className.indexOf('highcharts-internal-node') !== -1) {\n            attr.fill = 'none';\n        }\n        else if (state && stateOptions.brightness) {\n            // Brighten and hoist the hover nodes\n            attr.fill = color(attr.fill)\n                .brighten(stateOptions.brightness)\n                .get();\n        }\n        return attr;\n    }\n    /**\n     * Set the node's color recursively, from the parent down.\n     * @private\n     */\n    setColorRecursive(node, parentColor, colorIndex, index, siblings) {\n        const series = this, chart = series?.chart, colors = chart?.options?.colors;\n        if (node) {\n            const colorInfo = TreemapSeries_getColor(node, {\n                colors: colors,\n                index: index,\n                mapOptionsToLevel: series.mapOptionsToLevel,\n                parentColor: parentColor,\n                parentColorIndex: colorIndex,\n                series: series,\n                siblings: siblings\n            }), point = series.points[node.i];\n            if (point) {\n                point.color = colorInfo.color;\n                point.colorIndex = colorInfo.colorIndex;\n            }\n            let i = -1;\n            // Do it all again with the children\n            for (const child of (node.children || [])) {\n                series.setColorRecursive(child, colorInfo.color, colorInfo.colorIndex, ++i, node.children.length);\n            }\n        }\n    }\n    setPointValues() {\n        const series = this;\n        const { points, xAxis, yAxis } = series;\n        const styledMode = series.chart.styledMode;\n        // Get the crisp correction in classic mode. For this to work in\n        // styled mode, we would need to first add the shape (without x,\n        // y, width and height), then read the rendered stroke width\n        // using point.graphic.strokeWidth(), then modify and apply the\n        // shapeArgs. This applies also to column series, but the\n        // downside is performance and code complexity.\n        const getStrokeWidth = (point) => (styledMode ?\n            0 :\n            (series.pointAttribs(point)['stroke-width'] || 0));\n        for (const point of points) {\n            const { pointValues: values, visible } = point.node;\n            // Points which is ignored, have no values.\n            if (values && visible) {\n                const { height, width, x, y } = values, strokeWidth = getStrokeWidth(point), xValue = xAxis.toPixels(x, true), x2Value = xAxis.toPixels(x + width, true), yValue = yAxis.toPixels(y, true), y2Value = yAxis.toPixels(y + height, true), \n                // If the edge of a rectangle is on the edge, make sure it\n                // stays within the plot area by adding or substracting half\n                // of the stroke width.\n                x1 = xValue === 0 ?\n                    strokeWidth / 2 :\n                    crisp(xAxis.toPixels(x, true), strokeWidth, true), x2 = x2Value === xAxis.len ?\n                    xAxis.len - strokeWidth / 2 :\n                    crisp(xAxis.toPixels(x + width, true), strokeWidth, true), y1 = yValue === yAxis.len ?\n                    yAxis.len - strokeWidth / 2 :\n                    crisp(yAxis.toPixels(y, true), strokeWidth, true), y2 = y2Value === 0 ?\n                    strokeWidth / 2 :\n                    crisp(yAxis.toPixels(y + height, true), strokeWidth, true);\n                // Set point values\n                const shapeArgs = {\n                    x: Math.min(x1, x2),\n                    y: Math.min(y1, y2),\n                    width: Math.abs(x2 - x1),\n                    height: Math.abs(y2 - y1)\n                };\n                point.plotX = shapeArgs.x + (shapeArgs.width / 2);\n                point.plotY = shapeArgs.y + (shapeArgs.height / 2);\n                point.shapeArgs = shapeArgs;\n            }\n            else {\n                // Reset visibility\n                delete point.plotX;\n                delete point.plotY;\n            }\n        }\n    }\n    /**\n     * Sets a new root node for the series.\n     *\n     * @private\n     * @function Highcharts.Series#setRootNode\n     *\n     * @param {string} id\n     * The id of the new root node.\n     *\n     * @param {boolean} [redraw=true]\n     * Whether to redraw the chart or not.\n     *\n     * @param {Object} [eventArguments]\n     * Arguments to be accessed in event handler.\n     *\n     * @param {string} [eventArguments.newRootId]\n     * Id of the new root.\n     *\n     * @param {string} [eventArguments.previousRootId]\n     * Id of the previous root.\n     *\n     * @param {boolean} [eventArguments.redraw]\n     * Whether to redraw the chart after.\n     *\n     * @param {Object} [eventArguments.series]\n     * The series to update the root of.\n     *\n     * @param {string} [eventArguments.trigger]\n     * The action which triggered the event. Undefined if the setRootNode is\n     * called directly.\n     *\n     * @emits Highcharts.Series#event:setRootNode\n     */\n    setRootNode(id, redraw, eventArguments) {\n        const series = this, eventArgs = TreemapSeries_extend({\n            newRootId: id,\n            previousRootId: series.rootNode,\n            redraw: TreemapSeries_pick(redraw, true),\n            series: series\n        }, eventArguments);\n        /**\n         * The default functionality of the setRootNode event.\n         *\n         * @private\n         * @param {Object} args The event arguments.\n         * @param {string} args.newRootId Id of the new root.\n         * @param {string} args.previousRootId Id of the previous root.\n         * @param {boolean} args.redraw Whether to redraw the chart after.\n         * @param {Object} args.series The series to update the root of.\n         * @param {string} [args.trigger=undefined] The action which\n         * triggered the event. Undefined if the setRootNode is called\n         * directly.\n             */\n        const defaultFn = function (args) {\n            const series = args.series;\n            // Store previous and new root ids on the series.\n            series.idPreviousRoot = args.previousRootId;\n            series.rootNode = args.newRootId;\n            // Redraw the chart\n            series.isDirty = true; // Force redraw\n            if (args.redraw) {\n                series.chart.redraw();\n            }\n        };\n        // Fire setRootNode event.\n        TreemapSeries_fireEvent(series, 'setRootNode', eventArgs, defaultFn);\n    }\n    /**\n     * Workaround for `inactive` state. Since `series.opacity` option is\n     * already reserved, don't use that state at all by disabling\n     * `inactiveOtherPoints` and not inheriting states by points.\n     * @private\n     */\n    setState(state) {\n        this.options.inactiveOtherPoints = true;\n        super.setState(state, false);\n        this.options.inactiveOtherPoints = false;\n    }\n    setTreeValues(tree) {\n        const series = this, options = series.options, idRoot = series.rootNode, mapIdToNode = series.nodeMap, nodeRoot = mapIdToNode[idRoot], levelIsConstant = (typeof options.levelIsConstant === 'boolean' ?\n            options.levelIsConstant :\n            true), children = [], point = series.points[tree.i];\n        // First give the children some values\n        let childrenTotal = 0;\n        for (let child of tree.children) {\n            child = series.setTreeValues(child);\n            children.push(child);\n            if (!child.ignore) {\n                childrenTotal += child.val;\n            }\n        }\n        // Sort the children\n        stableSort(children, (a, b) => ((a.sortIndex || 0) - (b.sortIndex || 0)));\n        // Set the values\n        let val = TreemapSeries_pick(point?.simulatedValue, point?.options.value, childrenTotal);\n        if (point) {\n            point.value = val;\n        }\n        if (point?.isGroup && options.cluster?.reductionFactor) {\n            val /= options.cluster.reductionFactor;\n        }\n        if (tree.parentNode?.point?.isGroup && series.rootNode !== tree.parent) {\n            tree.visible = false;\n        }\n        TreemapSeries_extend(tree, {\n            children: children,\n            childrenTotal: childrenTotal,\n            // Ignore this node if point is not visible\n            ignore: !(TreemapSeries_pick(point?.visible, true) && (val > 0)),\n            isLeaf: tree.visible && !childrenTotal,\n            isGroup: point?.isGroup,\n            levelDynamic: (tree.level - (levelIsConstant ? 0 : nodeRoot.level)),\n            name: TreemapSeries_pick(point?.name, ''),\n            sortIndex: TreemapSeries_pick(point?.sortIndex, -val),\n            val: val\n        });\n        return tree;\n    }\n    sliceAndDice(parent, children) {\n        return this.algorithmFill(true, parent, children);\n    }\n    squarified(parent, children) {\n        return this.algorithmLowAspectRatio(true, parent, children);\n    }\n    strip(parent, children) {\n        return this.algorithmLowAspectRatio(false, parent, children);\n    }\n    stripes(parent, children) {\n        return this.algorithmFill(false, parent, children);\n    }\n    translate(tree) {\n        const series = this, options = series.options, applyGrouping = !tree;\n        let // NOTE: updateRootId modifies series.\n        rootId = TreemapSeries_updateRootId(series), rootNode, pointValues, seriesArea, val;\n        if (!tree && !rootId.startsWith('highcharts-grouped-treemap-points-')) {\n            // Group points are removed, but not destroyed during generatePoints\n            (this.points || []).forEach((point) => {\n                if (point.isGroup) {\n                    point.destroy();\n                }\n            });\n            // Call prototype function\n            super.translate();\n            // @todo Only if series.isDirtyData is true\n            tree = series.getTree();\n        }\n        // Ensure `tree` and `series.tree` are synchronized\n        series.tree = tree = tree || series.tree;\n        rootNode = series.nodeMap[rootId];\n        if (rootId !== '' && !rootNode) {\n            series.setRootNode('', false);\n            rootId = series.rootNode;\n            rootNode = series.nodeMap[rootId];\n        }\n        if (!rootNode.point?.isGroup) {\n            series.mapOptionsToLevel = TreemapSeries_getLevelOptions({\n                from: rootNode.level + 1,\n                levels: options.levels,\n                to: tree.height,\n                defaults: {\n                    levelIsConstant: series.options.levelIsConstant,\n                    colorByPoint: options.colorByPoint\n                }\n            });\n        }\n        // Parents of the root node is by default visible\n        Treemap_TreemapUtilities.recursive(series.nodeMap[series.rootNode], (node) => {\n            const p = node.parent;\n            let next = false;\n            node.visible = true;\n            if (p || p === '') {\n                next = series.nodeMap[p];\n            }\n            return next;\n        });\n        // Children of the root node is by default visible\n        Treemap_TreemapUtilities.recursive(series.nodeMap[series.rootNode].children, (children) => {\n            let next = false;\n            for (const child of children) {\n                child.visible = true;\n                if (child.children.length) {\n                    next = (next || []).concat(child.children);\n                }\n            }\n            return next;\n        });\n        series.setTreeValues(tree);\n        // Calculate plotting values.\n        series.axisRatio = (series.xAxis.len / series.yAxis.len);\n        series.nodeMap[''].pointValues = pointValues = {\n            x: 0,\n            y: 0,\n            width: axisMax,\n            height: axisMax\n        };\n        series.nodeMap[''].values = seriesArea = TreemapSeries_merge(pointValues, {\n            width: (pointValues.width * series.axisRatio),\n            direction: (options.layoutStartingDirection === 'vertical' ? 0 : 1),\n            val: tree.val\n        });\n        // We need to pre-render the data labels in order to measure the height\n        // of data label group\n        if (this.hasOutsideDataLabels || this.hadOutsideDataLabels) {\n            this.drawDataLabels();\n        }\n        series.calculateChildrenAreas(tree, seriesArea);\n        // Logic for point colors\n        if (!series.colorAxis &&\n            !options.colorByPoint) {\n            series.setColorRecursive(series.tree);\n        }\n        // Update axis extremes according to the root node.\n        if (options.allowTraversingTree && rootNode.pointValues) {\n            val = rootNode.pointValues;\n            series.xAxis.setExtremes(val.x, val.x + val.width, false);\n            series.yAxis.setExtremes(val.y, val.y + val.height, false);\n            series.xAxis.setScale();\n            series.yAxis.setScale();\n        }\n        // Assign values to points.\n        series.setPointValues();\n        if (applyGrouping) {\n            series.applyTreeGrouping();\n        }\n    }\n}\nTreemapSeries.defaultOptions = TreemapSeries_merge(ScatterSeries.defaultOptions, Treemap_TreemapSeriesDefaults);\nTreemapSeries_extend(TreemapSeries.prototype, {\n    buildKDTree: noop,\n    colorAttribs: Series_ColorMapComposition.seriesMembers.colorAttribs,\n    colorKey: 'colorValue', // Point color option key\n    directTouch: true,\n    getExtremesFromAll: true,\n    getSymbol: noop,\n    optionalAxis: 'colorAxis',\n    parallelArrays: ['x', 'y', 'value', 'colorValue'],\n    pointArrayMap: ['value', 'colorValue'],\n    pointClass: Treemap_TreemapPoint,\n    NodeClass: Treemap_TreemapNode,\n    trackerGroups: ['group', 'dataLabelsGroup'],\n    utils: Treemap_TreemapUtilities\n});\nSeries_ColorMapComposition.compose(TreemapSeries);\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('treemap', TreemapSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treemap_TreemapSeries = (TreemapSeries);\n\n;// ./code/es-modules/masters/modules/treemap.js\n\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nG.Breadcrumbs = G.Breadcrumbs || Breadcrumbs_Breadcrumbs;\nG.Breadcrumbs.compose(G.Chart, G.defaultOptions);\nTreemap_TreemapSeries.compose(G.Series);\n/* harmony default export */ const treemap_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__984__", "__WEBPACK_EXTERNAL_MODULE__620__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__28__", "__WEBPACK_EXTERNAL_MODULE__820__", "ColorMapComposition", "TreemapUtilities", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "treemap_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "Breadcrumbs_BreadcrumbsDefaults", "lang", "mainBreadcrumb", "options", "buttonTheme", "fill", "height", "padding", "zIndex", "states", "select", "style", "color", "buttonSpacing", "floating", "format", "relativeTo", "rtl", "position", "align", "verticalAlign", "x", "y", "separator", "text", "fontSize", "show<PERSON>ull<PERSON>ath", "useHTML", "highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_", "highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default", "composed", "addEvent", "defined", "extend", "fireEvent", "isString", "merge", "objectEach", "pick", "pushUnique", "onChartAfterShowResetZoom", "chart", "breadcrumbs", "bbox", "resetZoomButton", "getBBox", "breadcrumbsOptions", "alignBreadcrumbsGroup", "width", "onChartDestroy", "destroy", "onChart<PERSON><PERSON><PERSON><PERSON><PERSON>", "level", "breadcrumbsHeight", "marginBottom", "yOffset", "plotTop", "onChartRedraw", "redraw", "onChartSelection", "event", "resetSelection", "Breadcrumbs", "compose", "ChartClass", "highchartsDefaultOptions", "constructor", "userOptions", "elementList", "isDirty", "list", "chartOptions", "drilldown", "drillUpButton", "defaultOptions", "navigation", "updateProperties", "setList", "setLevel", "length", "getLevel", "getButtonText", "breadcrumb", "textFormat", "defaultText", "drillUpText", "returnText", "formatter", "levelOptions", "render", "group", "renderer", "g", "addClass", "attr", "add", "renderFullPathButtons", "renderSingleButton", "destroySingleButton", "resetElementListState", "updateListElements", "destroyListElements", "posX", "previousBreadcrumb", "renderButton", "updateSingleButton", "xOffset", "positionOptions", "alignTo", "bBox", "additionalSpace", "newPositions", "posY", "button", "e", "callDefaultEvent", "buttonEvents", "events", "click", "newLevel", "styledMode", "renderSeparator", "separatorOptions", "label", "css", "update", "currentBreadcrumb", "force", "element", "updated", "rtlFactor", "updateXPosition", "spacing", "adjustToRTL", "translate", "i", "iEnd", "isLast", "setState", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_", "highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default", "column", "columnProto", "seriesTypes", "ColorMapComposition_addEvent", "ColorMapComposition_defined", "onPointAfterSetState", "series", "point", "moveToTopOnHover", "graphic", "stateMarkerGraphic", "pointerEvents", "parentGroup", "state", "id", "href", "url", "visibility", "pointMembers", "dataLabelOnNull", "<PERSON><PERSON><PERSON><PERSON>", "value", "Infinity", "isNaN", "seriesMembers", "colorKey", "axisTypes", "parallelArrays", "pointArrayMap", "trackerGroups", "colorAttribs", "ret", "colorProp", "pointAttribs", "SeriesClass", "pointClass", "Series_ColorMapComposition", "highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_", "highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default", "Treemap_TreemapAlgorithmGroup", "h", "w", "p", "plot", "direction", "startDirection", "total", "nW", "lW", "nH", "lH", "el<PERSON>rr", "lP", "nR", "lR", "aspectRatio", "Math", "max", "addElement", "el", "push", "reset", "Series_DrawPointUtilities", "draw", "params", "animatableAttribs", "onComplete", "animation", "hasRendered", "attribs", "getClassName", "shouldDraw", "shapeType", "image", "imageUrl", "shapeArgs", "animate", "isNew", "keys", "pie", "PiePoint", "scatter", "ScatterPoint", "TreemapPoint_extend", "isNumber", "TreemapPoint_pick", "TreemapPoint", "arguments", "groupedPointsAmount", "className", "node", "nodeMap", "rootNode", "children", "isGroup", "<PERSON><PERSON><PERSON><PERSON>", "interactByLeaf", "allowTraversingTree", "Boolean", "apply", "plotY", "setVisible", "TreemapSeriesDefaults_isString", "TreemapSeriesDefaults", "animationLimit", "borderRadius", "showInLegend", "marker", "colorByPoint", "dataLabels", "enabled", "name", "headers", "inside", "textOverflow", "tooltip", "headerFormat", "pointFormat", "clusterFormat", "ignoreHiddenPoint", "layoutAlgorithm", "layoutStartingDirection", "alternateStartingDirection", "levelIsConstant", "traverseUpButton", "borderColor", "borderWidth", "opacity", "hover", "brightness", "heatmap", "halo", "shadow", "legendSymbol", "traverseToLeaf", "cluster", "pixelWidth", "pixelHeight", "reductionFactor", "minimumClusterSize", "distance", "gridSize", "kmeansT<PERSON><PERSON>old", "lineWidth", "radius", "recursive", "item", "func", "context", "next", "Treemap_TreemapUtilities", "TreeUtilities_extend", "isArray", "TreeUtilities_isNumber", "isObject", "TreeUtilities_merge", "TreeUtilities_pick", "<PERSON><PERSON><PERSON><PERSON>", "parse", "TreemapSeries_composed", "noop", "ColumnSeries", "ScatterSeries", "getColor", "TreemapSeries_getColor", "getLevelOptions", "TreemapSeries_getLevelOptions", "updateRootId", "TreemapSeries_updateRootId", "colorIndexByPoint", "colorIndex", "index", "mapOptionsToLevel", "parentColor", "parentColorIndex", "colors", "siblings", "points", "chartOptionsChart", "colorCount", "variateColor", "colorVariation", "brighten", "to", "defaults", "converted", "from", "levels", "result", "reduce", "getNodeWidth", "columnCount", "nodeDistance", "nodeWidth", "plotSizeX", "test", "fraction", "parseFloat", "nDistance", "Number", "setTreeValues", "tree", "before", "idRoot", "nodeRoot", "mapIdToNode", "optionsPoint", "childrenTotal", "levelDynamic", "visible", "for<PERSON>ach", "child", "newOptions", "val", "rootId", "TreemapSeries_addEvent", "arrayMax", "clamp", "correctFloat", "crisp", "TreemapSeries_defined", "error", "TreemapSeries_extend", "TreemapSeries_fireEvent", "TreemapSeries_isArray", "TreemapSeries_isNumber", "TreemapSeries_isObject", "TreemapSeries_isString", "TreemapSeries_merge", "TreemapSeries_pick", "TreemapSeries_pushUnique", "splat", "stableSort", "keepProps", "treemapAxisDefaultValues", "onSeriesAfterBindAxes", "treeAxis", "xAxis", "yAxis", "is", "endOnTick", "gridLineWidth", "min", "minPadding", "maxPadding", "startOnTick", "title", "tickPositions", "setOptions", "TreemapSeries", "simulation", "algorithmCalcPoints", "directionChange", "last", "children<PERSON>rea", "end", "pX", "pY", "pW", "pH", "gW", "gH", "keep", "parent", "algorithmFill", "pTot", "algorithmLowAspectRatio", "alignDataLabel", "dataLabel", "labelOptions", "applyTreeGrouping", "parentList", "parentGroups", "checkIfHide", "compareHeight", "thresholdArea", "area", "indexOf", "splice", "groupPoint", "find", "PointClass", "pointIndex", "formatPrefix", "amount", "nodeList", "buildTree", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "algorithm", "alternate", "filter", "ignore", "groupPadding", "children<PERSON><PERSON><PERSON>", "axisWidth", "pointV<PERSON>ues", "axisHeight", "values", "groupPaddingXValues", "len", "groupPaddingYValues", "dlH<PERSON>ght", "map", "dl", "xPad", "yPad", "axisRatio", "axisMax", "getChildrenRecursive", "getLeaves", "nodeSizeBy", "hasOutsideDataLabels", "some", "leaves", "areas", "valueSum", "sum", "expectedAreaPerValue", "areaSum", "minMiss", "max<PERSON><PERSON>", "fit", "areaPerValue", "miss", "simulatedValue", "createList", "currentLevelNumber", "target", "newRootId", "extraNodes", "reverse", "drawDataLabels", "positionsAreSet", "hasDataLabels", "dataLabelWidth", "lineClamp", "floor", "dlOptions", "drawPoints", "withinAnimationLimit", "pointCount", "groupKey", "hasGraphic", "shouldAnimate", "isInside", "r", "selected", "survive", "drillId", "drillToByLeaf", "drillToByGroup", "nodeParent", "drillToNode", "setRootNode", "drillUp", "trigger", "getExtremes", "dataMin", "dataMax", "colorValueData", "valueMin", "valueMax", "getListOfParents", "data", "existingIds", "arr", "ids", "listOfParents", "prev", "curr", "getTree", "allIds", "NodeClass", "init", "parentNode", "hasData", "dataTable", "rowCount", "setOptionsEvent", "allowDrillToNode", "eventsToUnbind", "onClickDrillToNode", "hadOutsideDataLabels", "keepEventsForUpdate", "drillUpsNumber", "stateOptions", "borderDashStyle", "cursor", "setColorRecursive", "colorInfo", "setPointV<PERSON>ues", "getStrokeWidth", "strokeWidth", "xValue", "toPixels", "x2Value", "yValue", "y2Value", "x1", "x2", "y1", "y2", "abs", "plotX", "eventArguments", "previousRootId", "args", "idPreviousRoot", "inactiveOtherPoints", "b", "sortIndex", "sliceAndDice", "squarified", "strip", "stripes", "applyGrouping", "seriesArea", "startsWith", "concat", "colorAxis", "setExtremes", "setScale", "buildKDTree", "directTouch", "getExtremesFromAll", "getSymbol", "optionalAxis", "utils", "registerSeriesType", "G", "Chart", "Treemap_TreemapSeries", "Series"], "mappings": "CAUA,AAVA;;;;;;;;;CASC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,UAAa,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,UAAa,CAAEA,EAAK,WAAc,CAAC,MAAS,EAC/M,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,6BAA8B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,UAAa,CAACA,EAAK,KAAQ,CAACA,EAAK,cAAiB,CAACA,EAAK,UAAa,CAACA,EAAK,MAAS,CAAE,GACzL,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,6BAA6B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,UAAa,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,UAAa,CAAEA,EAAK,WAAc,CAAC,MAAS,EAE7OA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,UAAa,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,UAAa,CAAEA,EAAK,UAAa,CAAC,MAAS,CACtN,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,EAAiCC,IAC5M,AAAC,CAAA,KACP,aACA,IA4jCNC,EAypCAC,EArtEUC,EAAuB,CAE/B,GACC,AAACb,IAERA,EAAOD,OAAO,CAAGU,CAEX,EAEA,IACC,AAACT,IAERA,EAAOD,OAAO,CAAGS,CAEX,EAEA,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGW,CAEX,EAEA,IACC,AAACV,IAERA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,AAACL,IAERA,EAAOD,OAAO,CAAGO,CAEX,CAEI,EAGIQ,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAalB,OAAO,CAG5B,IAAIC,EAASc,CAAwB,CAACE,EAAS,CAAG,CAGjDjB,QAAS,CAAC,CACX,EAMA,OAHAc,CAAmB,CAACG,EAAS,CAAChB,EAAQA,EAAOD,OAAO,CAAEgB,GAG/Cf,EAAOD,OAAO,AACtB,CAMCgB,EAAoBI,CAAC,CAAG,AAACnB,IACxB,IAAIoB,EAASpB,GAAUA,EAAOqB,UAAU,CACvC,IAAOrB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAe,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACvB,EAASyB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAAC3B,EAAS0B,IAC5EE,OAAOC,cAAc,CAAC7B,EAAS0B,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,EAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GA4QxF,IAAME,EAJP,CACxBC,KAnPS,CAOTC,eAAgB,MACpB,EA4OIC,QAjOY,CAiBZC,YAAa,CAETC,KAAM,OAENC,OAAQ,GAERC,QAAS,EAET,eAAgB,EAEhBC,OAAQ,EAERC,OAAQ,CACJC,OAAQ,CACJL,KAAM,MACV,CACJ,EACAM,MAAO,CACHC,MAAO,SACX,CACJ,EAOAC,cAAe,EA8BfC,SAAU,CAAA,EAYVC,OAAQ,KAAK,EAkBbC,WAAY,UAWZC,IAAK,CAAA,EAcLC,SAAU,CAMNC,MAAO,OAMPC,cAAe,MAMfC,EAAG,EAQHC,EAAG,KAAK,CACZ,EAMAC,UAAW,CAMPC,KAAM,IASNb,MAAO,CACHC,MAAO,UACPa,SAAU,OACd,CACJ,EAUAC,aAAc,CAAA,EAWdf,MAAO,CAAC,EAORgB,QAAS,CAAA,EAOTnB,OAAQ,CACZ,CASA,EAIA,IAAIoB,EAAmHrD,EAAoB,KAiB3I,GAAM,CAAEwC,OAAAA,CAAM,CAAE,CAAIc,AAhBuHtD,EAAoBI,CAAC,CAACiD,KAkB3J,CAAEE,SAAAA,CAAQ,CAAE,CAAI/B,IAEhB,CAAEgC,SAAAA,CAAQ,CAAEC,QAAAA,CAAO,CAAEC,OAAAA,CAAM,CAAEC,UAAAA,CAAS,CAAEC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,WAAAA,CAAU,CAAEC,KAAAA,CAAI,CAAEC,WAAAA,CAAU,CAAE,CAAIxC,IAUjG,SAASyC,IAEL,GAAIC,AADU,IAAI,CACRC,WAAW,CAAE,CACnB,IAAMC,EAAOF,AAFH,IAAI,CAEKG,eAAe,EAC9BH,AAHM,IAAI,CAGJG,eAAe,CAACC,OAAO,GAAIC,EAAqBL,AAHhD,IAAI,CAGkDC,WAAW,CAACvC,OAAO,AAC/EwC,CAAAA,GACAG,AAAsC,UAAtCA,EAAmB5B,QAAQ,CAACC,KAAK,EACjC2B,AAAkC,YAAlCA,EAAmB9B,UAAU,EAC7ByB,AAPM,IAAI,CAOJC,WAAW,CAACK,qBAAqB,CAAC,CAACJ,EAAKK,KAAK,CAAGF,EAAmBjC,aAAa,CAE9F,CACJ,CAKA,SAASoC,IACD,IAAI,CAACP,WAAW,GAChB,IAAI,CAACA,WAAW,CAACQ,OAAO,GACxB,IAAI,CAACR,WAAW,CAAG,KAAK,EAEhC,CAKA,SAASS,IACL,IAAMT,EAAc,IAAI,CAACA,WAAW,CACpC,GAAIA,GACA,CAACA,EAAYvC,OAAO,CAACW,QAAQ,EAC7B4B,EAAYU,KAAK,CAAE,CACnB,IAAMN,EAAqBJ,EAAYvC,OAAO,CAAEC,EAAc0C,EAAmB1C,WAAW,CAAEiD,EAAqB,AAACjD,CAAAA,EAAYE,MAAM,EAAI,CAAA,EACtI,EAAKF,CAAAA,EAAYG,OAAO,EAAI,CAAA,EAC5BuC,EAAmBjC,aAAa,CAAGO,EAAgB0B,EAAmB5B,QAAQ,CAACE,aAAa,AAC5FA,AAAkB,CAAA,WAAlBA,GACA,IAAI,CAACkC,YAAY,CAAG,AAAC,CAAA,IAAI,CAACA,YAAY,EAAI,CAAA,EAAKD,EAC/CX,EAAYa,OAAO,CAAGF,GAEjBjC,AAAkB,WAAlBA,GACL,IAAI,CAACoC,OAAO,EAAIH,EAChBX,EAAYa,OAAO,CAAG,CAACF,GAGvBX,EAAYa,OAAO,CAAG,KAAK,CAEnC,CACJ,CAIA,SAASE,IACL,IAAI,CAACf,WAAW,EAAI,IAAI,CAACA,WAAW,CAACgB,MAAM,EAC/C,CAKA,SAASC,EAAiBC,CAAK,EACvBA,AAAyB,CAAA,IAAzBA,EAAMC,cAAc,EACpB,IAAI,CAACnB,WAAW,EAChB,IAAI,CAACA,WAAW,CAACK,qBAAqB,EAE9C,CAkBA,MAAMe,EAMF,OAAOC,QAAQC,CAAU,CAAEC,CAAwB,CAAE,CAC7C1B,EAAWT,EAAU,iBACrBC,EAASiC,EAAY,UAAWf,GAChClB,EAASiC,EAAY,qBAAsBxB,GAC3CT,EAASiC,EAAY,aAAcb,GACnCpB,EAASiC,EAAY,SAAUP,GAC/B1B,EAASiC,EAAY,YAAaL,GAElC1B,EAAOgC,EAAyBhE,IAAI,CAAED,EAAgCC,IAAI,EAElF,CAMAiE,YAAYzB,CAAK,CAAE0B,CAAW,CAAE,CAC5B,IAAI,CAACC,WAAW,CAAG,CAAC,EACpB,IAAI,CAACC,OAAO,CAAG,CAAA,EACf,IAAI,CAACjB,KAAK,CAAG,EACb,IAAI,CAACkB,IAAI,CAAG,EAAE,CACd,IAAMC,EAAenC,EAAMK,EAAMtC,OAAO,CAACqE,SAAS,EAC9C/B,EAAMtC,OAAO,CAACqE,SAAS,CAACC,aAAa,CAAEX,EAAYY,cAAc,CAAEjC,EAAMtC,OAAO,CAACwE,UAAU,EAAIlC,EAAMtC,OAAO,CAACwE,UAAU,CAACjC,WAAW,CAAEyB,EACzI,CAAA,IAAI,CAAC1B,KAAK,CAAGA,EACb,IAAI,CAACtC,OAAO,CAAGoE,GAAgB,CAAC,CACpC,CAaAK,iBAAiBN,CAAI,CAAE,CACnB,IAAI,CAACO,OAAO,CAACP,GACb,IAAI,CAACQ,QAAQ,GACb,IAAI,CAACT,OAAO,CAAG,CAAA,CACnB,CAUAQ,QAAQP,CAAI,CAAE,CACV,IAAI,CAACA,IAAI,CAAGA,CAChB,CAQAQ,UAAW,CACP,IAAI,CAAC1B,KAAK,CAAG,IAAI,CAACkB,IAAI,CAACS,MAAM,EAAI,IAAI,CAACT,IAAI,CAACS,MAAM,CAAG,CACxD,CAQAC,UAAW,CACP,OAAO,IAAI,CAAC5B,KAAK,AACrB,CAYA6B,cAAcC,CAAU,CAAE,CACtB,IAA0BzC,EAAQC,AAAd,IAAI,CAAsBD,KAAK,CAAEK,EAAqBJ,AAAtD,IAAI,CAA8DvC,OAAO,CAAEF,EAAOwC,EAAMtC,OAAO,CAACF,IAAI,CAAEkF,EAAa7C,EAAKQ,EAAmB/B,MAAM,CAAE+B,EAAmBpB,YAAY,CAClM,eAAiB,kBAAmB0D,EAAcnF,GAAQqC,EAAKrC,EAAKoF,WAAW,CAAEpF,EAAKC,cAAc,EACpGoF,EAAaxC,EAAmByC,SAAS,EACzCzC,EAAmByC,SAAS,CAACL,IAC7BnE,EAAOoE,EAAY,CAAE/B,MAAO8B,EAAWM,YAAY,AAAC,EAAG/C,IAAU,GASrE,MARI,AAAC,CAAA,AAACN,EAASmD,IACX,CAACA,EAAWP,MAAM,EAClBO,AAAe,OAAfA,CAAkB,GAClBtD,EAAQoD,IACRE,CAAAA,EAAa,AAACxC,EAAmBpB,YAAY,CAEzC0D,EADA,KAAOA,CACG,EAEXE,CACX,CAQA5B,QAAS,CACD,IAAI,CAACW,OAAO,EACZ,IAAI,CAACoB,MAAM,GAEX,IAAI,CAACC,KAAK,EACV,IAAI,CAACA,KAAK,CAACvE,KAAK,GAEpB,IAAI,CAACkD,OAAO,CAAG,CAAA,CACnB,CAQAoB,QAAS,CACL,IAA0BhD,EAAQC,AAAd,IAAI,CAAsBD,KAAK,CAAEK,EAAqBJ,AAAtD,IAAI,CAA8DvC,OAAO,AAEzF,EAACuC,AAFe,IAAI,CAEPgD,KAAK,EAAI5C,GACtBJ,CAAAA,AAHgB,IAAI,CAGRgD,KAAK,CAAGjD,EAAMkD,QAAQ,CAC7BC,CAAC,CAAC,qBACFC,QAAQ,CAAC,gDACTC,IAAI,CAAC,CACNtF,OAAQsC,EAAmBtC,MAAM,AACrC,GACKuF,GAAG,EAAC,EAGTjD,EAAmBpB,YAAY,CAC/B,IAAI,CAACsE,qBAAqB,GAG1B,IAAI,CAACC,kBAAkB,GAE3B,IAAI,CAAClD,qBAAqB,EAC9B,CAQAiD,uBAAwB,CAEpB,IAAI,CAACE,mBAAmB,GACxB,IAAI,CAACC,qBAAqB,GAC1B,IAAI,CAACC,kBAAkB,GACvB,IAAI,CAACC,mBAAmB,EAC5B,CAQAJ,oBAAqB,CACjB,IAA0BxD,EAAQC,AAAd,IAAI,CAAsBD,KAAK,CAAE6B,EAAO5B,AAAxC,IAAI,CAAgD4B,IAAI,CAA4CzD,EAAgBiC,AAArCJ,AAA/E,IAAI,CAAuFvC,OAAO,CAAqCU,aAAa,CAExK,IAAI,CAACwF,mBAAmB,GAGxB,IAAMC,EAAO5D,AALO,IAAI,CAKCgD,KAAK,CAC1BhD,AANgB,IAAI,CAMRgD,KAAK,CAAC7C,OAAO,GAAGG,KAAK,CACjCnC,EACE0F,EAAqBjC,CAAI,CAACA,EAAKS,MAAM,CAAG,EAAE,AAC5C,EAACtC,EAAMgC,aAAa,EAAK,IAAI,CAACrB,KAAK,CAAG,EACtCX,EAAMgC,aAAa,CAAG/B,AAVN,IAAI,CAUc8D,YAAY,CAACD,EAAoBD,EAH7CzF,GAKjB4B,EAAMgC,aAAa,GACpB,IAAI,CAACrB,KAAK,CAAG,EAEb,IAAI,CAACqD,kBAAkB,GAGvB,IAAI,CAACP,mBAAmB,GAGpC,CAQAnD,sBAAsB2D,CAAO,CAAE,CAE3B,GAAIhE,AADgB,IAAI,CACRgD,KAAK,CAAE,CACnB,IAAM5C,EAAqBJ,AAFX,IAAI,CAEmBvC,OAAO,CAAEC,EAAc0C,EAAmB1C,WAAW,CAAEuG,EAAkB7D,EAAmB5B,QAAQ,CAAE0F,EAAW9D,AAAkC,UAAlCA,EAAmB9B,UAAU,EACjL8B,AAAkC,eAAlCA,EAAmB9B,UAAU,CAC7B,KAAK,EACL,UAAY6F,EAAOnE,AALP,IAAI,CAKegD,KAAK,CAAC7C,OAAO,GAAIiE,EAAkB,EAAK1G,CAAAA,EAAYG,OAAO,EAAI,CAAA,EAC9FuC,EAAmBjC,aAAa,AAEpC8F,CAAAA,EAAgB3D,KAAK,CAAG6D,EAAK7D,KAAK,CAAG8D,EACrCH,EAAgBrG,MAAM,CAAGuG,EAAKvG,MAAM,CAAGwG,EACvC,IAAMC,EAAe3E,EAAMuE,EAEvBD,CAAAA,GACAK,CAAAA,EAAa1F,CAAC,EAAIqF,CAAM,EAExBhE,AAfY,IAAI,CAeJvC,OAAO,CAACc,GAAG,EACvB8F,CAAAA,EAAa1F,CAAC,EAAIsF,EAAgB3D,KAAK,AAAD,EAE1C+D,EAAazF,CAAC,CAAGgB,EAAKyE,EAAazF,CAAC,CAAE,IAAI,CAACiC,OAAO,CAAE,GACpDb,AAnBgB,IAAI,CAmBRgD,KAAK,CAACvE,KAAK,CAAC4F,EAAc,CAAA,EAAMH,EAChD,CACJ,CAgBAJ,aAAatB,CAAU,CAAEoB,CAAI,CAAEU,CAAI,CAAE,CACjC,IAAMtE,EAAc,IAAI,CAAED,EAAQ,IAAI,CAACA,KAAK,CAAEK,EAAqBJ,EAAYvC,OAAO,CAAEC,EAAcgC,EAAMU,EAAmB1C,WAAW,EACpI6G,EAASxE,EAAMkD,QAAQ,CACxBsB,MAAM,CAACvE,EAAYuC,aAAa,CAACC,GAAaoB,EAAMU,EAAM,SAAUE,CAAC,EAEtE,IAEIC,EAFEC,EAAetE,EAAmBuE,MAAM,EAC1CvE,EAAmBuE,MAAM,CAACC,KAAK,AAE/BF,CAAAA,GACAD,CAAAA,EAAmBC,EAAazH,IAAI,CAAC+C,EAAawE,EAAGhC,EAAU,EAG1C,CAAA,IAArBiC,IAGKrE,EAAmBpB,YAAY,CAIhCwF,EAAEK,QAAQ,CAAGrC,EAAW9B,KAAK,CAH7B8D,EAAEK,QAAQ,CAAG7E,EAAYU,KAAK,CAAG,EAKrClB,EAAUQ,EAAa,KAAMwE,GAErC,EAAG9G,GACEyF,QAAQ,CAAC,iCACTE,GAAG,CAACrD,EAAYgD,KAAK,EAI1B,OAHI,AAACjD,EAAM+E,UAAU,EACjBP,EAAOnB,IAAI,CAAChD,EAAmBnC,KAAK,EAEjCsG,CACX,CAcAQ,gBAAgBnB,CAAI,CAAEU,CAAI,CAAE,CACxB,IAA0BvE,EAAQ,IAAI,CAACA,KAAK,CAA4CiF,EAAmB5E,AAAxCJ,AAA/C,IAAI,CAAuDvC,OAAO,CAAwCoB,SAAS,CACjIA,EAAYkB,EAAMkD,QAAQ,CAC3BgC,KAAK,CAACD,EAAiBlG,IAAI,CAAE8E,EAAMU,EAAM,KAAK,EAAG,KAAK,EAAG,KAAK,EAAG,CAAA,GACjEnB,QAAQ,CAAC,oCACTE,GAAG,CAACrD,AAJW,IAAI,CAIHgD,KAAK,EAI1B,OAHI,AAACjD,EAAM+E,UAAU,EACjBjG,EAAUqG,GAAG,CAACF,EAAiB/G,KAAK,EAEjCY,CACX,CAYAsG,OAAO1H,CAAO,CAAE,CACZiC,EAAM,CAAA,EAAM,IAAI,CAACjC,OAAO,CAAEA,GAC1B,IAAI,CAAC+C,OAAO,GACZ,IAAI,CAACmB,OAAO,CAAG,CAAA,CACnB,CAQAoC,oBAAqB,CACjB,IAAMhE,EAAQ,IAAI,CAACA,KAAK,CAAEqF,EAAoB,IAAI,CAACxD,IAAI,CAAC,IAAI,CAAClB,KAAK,CAAG,EAAE,AACnEX,CAAAA,EAAMgC,aAAa,EACnBhC,EAAMgC,aAAa,CAACqB,IAAI,CAAC,CACrBtE,KAAM,IAAI,CAACyD,aAAa,CAAC6C,EAC7B,EAER,CAQA5E,SAAU,CACN,IAAI,CAACgD,mBAAmB,GAGxB,IAAI,CAACG,mBAAmB,CAAC,CAAA,GAErB,IAAI,CAACX,KAAK,EACV,IAAI,CAACA,KAAK,CAACxC,OAAO,GAEtB,IAAI,CAACwC,KAAK,CAAG,KAAK,CACtB,CAQAW,oBAAoB0B,CAAK,CAAE,CACvB,IAAM3D,EAAc,IAAI,CAACA,WAAW,CACpC/B,EAAW+B,EAAa,CAAC4D,EAAS5E,KAC1B2E,CAAAA,GACA,CAAC3D,CAAW,CAAChB,EAAM,CAAC6E,OAAO,AAAD,IAE1BD,AADAA,CAAAA,EAAU5D,CAAW,CAAChB,EAAM,AAAD,EACnB6D,MAAM,EAAIe,EAAQf,MAAM,CAAC/D,OAAO,GACxC8E,EAAQzG,SAAS,EAAIyG,EAAQzG,SAAS,CAAC2B,OAAO,GAC9C,OAAO8E,EAAQf,MAAM,CACrB,OAAOe,EAAQzG,SAAS,CACxB,OAAO6C,CAAW,CAAChB,EAAM,CAEjC,GACI2E,GACA,CAAA,IAAI,CAAC3D,WAAW,CAAG,CAAC,CAAA,CAE5B,CAQA8B,qBAAsB,CACd,IAAI,CAACzD,KAAK,CAACgC,aAAa,GACxB,IAAI,CAAChC,KAAK,CAACgC,aAAa,CAACvB,OAAO,GAChC,IAAI,CAACT,KAAK,CAACgC,aAAa,CAAG,KAAK,EAExC,CAQA0B,uBAAwB,CACpB9D,EAAW,IAAI,CAAC+B,WAAW,CAAE,AAAC4D,IAC1BA,EAAQC,OAAO,CAAG,CAAA,CACtB,EACJ,CASA7B,oBAAqB,CACjB,IAA0BhC,EAAc1B,AAApB,IAAI,CAA4B0B,WAAW,CAAEvD,EAAgB6B,AAA7D,IAAI,CAAqEvC,OAAO,CAACU,aAAa,CAAwByD,EAAO5B,AAA7H,IAAI,CAAqI4B,IAAI,CAAErD,EAAMyB,AAArJ,IAAI,CAA6JvC,OAAO,CAACc,GAAG,CAAEiH,EAAYjH,EAAM,GAAK,EAAGkH,EAAkB,SAAUH,CAAO,CAAEI,CAAO,EACpQ,OAAOF,EAAYF,EAAQnF,OAAO,GAAGG,KAAK,CACtCkF,EAAYE,CACpB,EAAGC,EAAc,SAAUL,CAAO,CAAE1B,CAAI,CAAEU,CAAI,EAC1CgB,EAAQM,SAAS,CAAChC,EAAO0B,EAAQnF,OAAO,GAAGG,KAAK,CAAEgE,EACtD,EAEIV,EAAO5D,AAPS,IAAI,CAODgD,KAAK,CACxByC,EAAgBzF,AARA,IAAI,CAQQgD,KAAK,CAAE7E,GACnCA,EAAeiH,EAAmB5C,EACtC,IAAK,IAAIqD,EAAI,EAAGC,EAAOlE,EAAKS,MAAM,CAAEwD,EAAIC,EAAM,EAAED,EAAG,CAC/C,IACItB,EAAQ1F,EADNkH,EAASF,IAAMC,EAAO,CAGxBpE,CAAAA,CAAW,CAACc,AADhBA,CAAAA,EAAaZ,CAAI,CAACiE,EAAE,AAAD,EACQnF,KAAK,CAAC,EAE7B6D,EAASa,AADTA,CAAAA,EAAoB1D,CAAW,CAACc,EAAW9B,KAAK,CAAC,AAAD,EACrB6D,MAAM,CAE7B,AAACa,EAAkBvG,SAAS,EAC3BkH,EAUIX,EAAkBvG,SAAS,EAChCkH,IACAX,EAAkBvG,SAAS,CAAC2B,OAAO,GACnC,OAAO4E,EAAkBvG,SAAS,GAXlC+E,GAAQ4B,EAAYrH,EACpBiH,EAAkBvG,SAAS,CACvBmB,AAvBI,IAAI,CAuBI+E,eAAe,CAACnB,EAvB+EzF,GAwB3GI,GACAoH,EAAYP,EAAkBvG,SAAS,CAAE+E,EAzBkEzF,GA2B/GyF,GAAQ6B,EAAgBL,EAAkBvG,SAAS,CAAEV,IAOzDuD,CAAW,CAACc,EAAW9B,KAAK,CAAC,CAAC6E,OAAO,CAAG,CAAA,IAIxChB,EAASvE,AAtCG,IAAI,CAsCK8D,YAAY,CAACtB,EAAYoB,EAtCqEzF,GAuC/GI,GACAoH,EAAYpB,EAAQX,EAxC2FzF,GA0CnHyF,GAAQ6B,EAAgBlB,EAAQpG,GAE3B4H,IACDlH,EAAYmB,AA7CJ,IAAI,CA6CY+E,eAAe,CAACnB,EA7CuEzF,GA8C3GI,GACAoH,EAAY9G,EAAW+E,EA/CoFzF,GAiD/GyF,GAAQ6B,EAAgB5G,EAAWV,IAEvCuD,CAAW,CAACc,EAAW9B,KAAK,CAAC,CAAG,CAC5B6D,OAAAA,EACA1F,UAAAA,EACA0G,QAAS,CAAA,CACb,GAEAhB,GACAA,EAAOyB,QAAQ,CAACD,AAAS,IAATA,EAExB,CACJ,CACJ,CAMA3E,EAAYY,cAAc,CAAG1E,EAAgCG,OAAO,CA4FpE,IAAIwI,EAA+FpK,EAAoB,KACnHqK,EAAmHrK,EAAoBI,CAAC,CAACgK,GAEzIE,EAAmItK,EAAoB,KACvJuK,EAAuJvK,EAAoBI,CAAC,CAACkK,GAE7KE,EAAmHxK,EAAoB,IACvIyK,EAAuIzK,EAAoBI,CAAC,CAACoK,GAajK,GAAM,CAAEE,OAAQ,CAAExJ,UAAWyJ,CAAW,CAAE,CAAE,CAAG,AAACJ,IAA2IK,WAAW,CAGhM,CAAEpH,SAAUqH,CAA4B,CAAEpH,QAASqH,CAA2B,CAAE,CAAItJ,KAO1F,AAAC,SAAU5B,CAAmB,EAsC1B,SAASmL,EAAqBpC,CAAC,EAC3B,IAAoBqC,EAASC,AAAf,IAAI,CAAiBD,MAAM,CAAE5D,EAAW4D,EAAO9G,KAAK,CAACkD,QAAQ,AACvE6D,CADU,IAAI,CACRC,gBAAgB,EAAID,AADhB,IAAI,CACkBE,OAAO,GACnC,AAACH,EAAOI,kBAAkB,EAI1BJ,CAAAA,EAAOI,kBAAkB,CAAG,GAAKX,CAAAA,GAAwH,EAAGrD,EAAU,OACjKiC,GAAG,CAAC,CACLgC,cAAe,MACnB,GACK7D,GAAG,CAACyD,AAVH,IAAI,CAUKE,OAAO,CAACG,WAAW,CAAA,EAElC3C,GAAG4C,QAAU,SAGbN,AAfM,IAAI,CAeJE,OAAO,CAAC5D,IAAI,CAAC,CACfiE,GAAI,IAAI,CAACA,EAAE,AACf,GACAR,EAAOI,kBAAkB,CAAC7D,IAAI,CAAC,CAC3BkE,KAAM,CAAC,EAAErE,EAASsE,GAAG,CAAC,CAAC,EAAE,IAAI,CAACF,EAAE,CAAC,CAAC,CAClCG,WAAY,SAChB,IAGAX,EAAOI,kBAAkB,CAAC7D,IAAI,CAAC,CAC3BkE,KAAM,EACV,GAGZ,CA9DA7L,EAAoBgM,YAAY,CAAG,CAC/BC,gBAAiB,CAAA,EACjBX,iBAAkB,CAAA,EAClBY,QAiEJ,WACI,OAAQ,AAAe,OAAf,IAAI,CAACC,KAAK,EACd,IAAI,CAACA,KAAK,GAAKC,KACf,IAAI,CAACD,KAAK,GAAK,CAACC,KAEf,CAAA,AAAe,KAAK,IAApB,IAAI,CAACD,KAAK,EAAe,CAACE,MAAM,IAAI,CAACF,KAAK,CAAA,CACnD,CAtEA,EACAnM,EAAoBsM,aAAa,CAAG,CAChCC,SAAU,QACVC,UAAW,CAAC,QAAS,QAAS,YAAY,CAC1CC,eAAgB,CAAC,IAAK,IAAK,QAAQ,CACnCC,cAAe,CAAC,QAAQ,CACxBC,cAAe,CAAC,QAAS,cAAe,kBAAkB,CAC1DC,aAwEJ,SAA4BvB,CAAK,EAC7B,IAAMwB,EAAM,CAAC,EAMb,OALI3B,EAA4BG,EAAM5I,KAAK,GACtC,CAAA,CAAC4I,EAAMM,KAAK,EAAIN,AAAgB,WAAhBA,EAAMM,KAAK,AAAY,GAExCkB,CAAAA,CAAG,CAAC,IAAI,CAACC,SAAS,EAAI,OAAO,CAAGzB,EAAM5I,KAAK,AAAD,EAEvCoK,CACX,EA/EIE,aAAchC,EAAYgC,YAAY,AAC1C,EAcA/M,EAAoB4F,OAAO,CAL3B,SAAiBoH,CAAW,EAGxB,OADA/B,EADmB+B,EAAY1L,SAAS,CAAC2L,UAAU,CACV,gBAAiB9B,GACnD6B,CACX,CAkEJ,EAAGhN,GAAwBA,CAAAA,EAAsB,CAAC,CAAA,GAMrB,IAAMkN,EAA8BlN,EAGjE,IAAImN,EAAmG/M,EAAoB,KACvHgN,EAAuHhN,EAAoBI,CAAC,CAAC2M,GA4FpH,IAAME,EAzEnC,MAMItH,YAAYuH,CAAC,CAAEC,CAAC,CAAE5M,CAAC,CAAE6M,CAAC,CAAE,CACpB,IAAI,CAACrL,MAAM,CAAGmL,EACd,IAAI,CAACzI,KAAK,CAAG0I,EACb,IAAI,CAACE,IAAI,CAAGD,EACZ,IAAI,CAACE,SAAS,CAAG/M,EACjB,IAAI,CAACgN,cAAc,CAAGhN,EACtB,IAAI,CAACiN,KAAK,CAAG,EACb,IAAI,CAACC,EAAE,CAAG,EACV,IAAI,CAACC,EAAE,CAAG,EACV,IAAI,CAACC,EAAE,CAAG,EACV,IAAI,CAACC,EAAE,CAAG,EACV,IAAI,CAACC,KAAK,CAAG,EAAE,CACf,IAAI,CAACC,EAAE,CAAG,CACNN,MAAO,EACPI,GAAI,EACJD,GAAI,EACJD,GAAI,EACJD,GAAI,EACJM,GAAI,EACJC,GAAI,EACJC,YAAa,SAAUd,CAAC,CAAED,CAAC,EACvB,OAAOgB,KAAKC,GAAG,CAAEhB,EAAID,EAAKA,EAAIC,EAClC,CACJ,CACJ,CAMAiB,WAAWC,CAAE,CAAE,CACX,IAAI,CAACP,EAAE,CAACN,KAAK,CAAG,IAAI,CAACK,KAAK,CAAC,IAAI,CAACA,KAAK,CAACrH,MAAM,CAAG,EAAE,CACjD,IAAI,CAACgH,KAAK,CAAG,IAAI,CAACA,KAAK,CAAGa,EACtB,AAAmB,IAAnB,IAAI,CAACf,SAAS,EAEd,IAAI,CAACI,EAAE,CAAG,IAAI,CAACD,EAAE,CACjB,IAAI,CAACK,EAAE,CAACF,EAAE,CAAG,IAAI,CAACE,EAAE,CAACN,KAAK,CAAG,IAAI,CAACE,EAAE,CACpC,IAAI,CAACI,EAAE,CAACE,EAAE,CAAG,IAAI,CAACF,EAAE,CAACG,WAAW,CAAC,IAAI,CAACP,EAAE,CAAE,IAAI,CAACI,EAAE,CAACF,EAAE,EAEpD,IAAI,CAACH,EAAE,CAAG,IAAI,CAACD,KAAK,CAAG,IAAI,CAACzL,MAAM,CAClC,IAAI,CAAC+L,EAAE,CAACH,EAAE,CAAG,IAAI,CAACG,EAAE,CAACN,KAAK,CAAG,IAAI,CAACC,EAAE,CACpC,IAAI,CAACK,EAAE,CAACC,EAAE,CAAG,IAAI,CAACD,EAAE,CAACG,WAAW,CAAC,IAAI,CAACR,EAAE,CAAE,IAAI,CAACK,EAAE,CAACH,EAAE,IAIpD,IAAI,CAACC,EAAE,CAAG,IAAI,CAACD,EAAE,CACjB,IAAI,CAACG,EAAE,CAACJ,EAAE,CAAG,IAAI,CAACI,EAAE,CAACN,KAAK,CAAG,IAAI,CAACI,EAAE,CACpC,IAAI,CAACE,EAAE,CAACE,EAAE,CAAG,IAAI,CAACF,EAAE,CAACG,WAAW,CAAC,IAAI,CAACH,EAAE,CAACJ,EAAE,CAAE,IAAI,CAACE,EAAE,EAEpD,IAAI,CAACD,EAAE,CAAG,IAAI,CAACH,KAAK,CAAG,IAAI,CAAC/I,KAAK,CACjC,IAAI,CAACqJ,EAAE,CAACL,EAAE,CAAG,IAAI,CAACK,EAAE,CAACN,KAAK,CAAG,IAAI,CAACG,EAAE,CACpC,IAAI,CAACG,EAAE,CAACC,EAAE,CAAG,IAAI,CAACD,EAAE,CAACG,WAAW,CAAC,IAAI,CAACH,EAAE,CAACL,EAAE,CAAE,IAAI,CAACE,EAAE,GAExD,IAAI,CAACE,KAAK,CAACS,IAAI,CAACD,EACpB,CACAE,OAAQ,CACJ,IAAI,CAACd,EAAE,CAAG,EACV,IAAI,CAACC,EAAE,CAAG,EACV,IAAI,CAACG,KAAK,CAAG,EAAE,CACf,IAAI,CAACL,KAAK,CAAG,CACjB,CACJ,EA0ImCgB,EAHR,CACvBC,KAzDJ,SAAcxD,CAAK,CAAEyD,CAAM,EACvB,GAAM,CAAEC,kBAAAA,CAAiB,CAAEC,WAAAA,CAAU,CAAEvF,IAAAA,CAAG,CAAEjC,SAAAA,CAAQ,CAAE,CAAGsH,EACnDG,EAAY,AAAC5D,EAAMD,MAAM,EAAIC,EAAMD,MAAM,CAAC9G,KAAK,CAAC4K,WAAW,CAE7D,KAAK,EAEJ7D,EAAMD,MAAM,EACTC,EAAMD,MAAM,CAACpJ,OAAO,CAACiN,SAAS,CAClC1D,EAAUF,EAAME,OAAO,CAK3B,GAJAuD,EAAOK,OAAO,CAAG,CACb,GAAGL,EAAOK,OAAO,CACjB,MAAS9D,EAAM+D,YAAY,EAC/B,EACK/D,EAAMgE,UAAU,GACZ9D,IAWDF,EAAME,OAAO,CATTA,EADAuD,AAAqB,SAArBA,EAAOQ,SAAS,CACN9H,EAASnE,IAAI,GAElByL,AAAqB,UAArBA,EAAOQ,SAAS,CACX9H,EAAS+H,KAAK,CAACT,EAAOU,QAAQ,EAAI,IACvC7H,IAAI,CAACmH,EAAOW,SAAS,EAAI,CAAC,GAGrBjI,CAAQ,CAACsH,EAAOQ,SAAS,CAAC,CAACR,EAAOW,SAAS,EAAI,CAAC,GAG9DlE,EAAQ3D,GAAG,CAACkH,EAAOvH,KAAK,GAExBkC,GACA8B,EAAQ9B,GAAG,CAACA,GAEhB8B,EACK5D,IAAI,CAACmH,EAAOK,OAAO,EACnBO,OAAO,CAACX,EAAmBD,CAAAA,EAAOa,KAAK,EAAWV,EAAWD,QAEjE,GAAIzD,EAAS,CACd,IAAMxG,EAAU,KACZsG,EAAME,OAAO,CAAGA,EAAWA,GAAWA,EAAQxG,OAAO,GACjD,AAAsB,YAAtB,OAAOiK,GACPA,GAER,CAEIhO,CAAAA,OAAO4O,IAAI,CAACb,GAAmBnI,MAAM,CACrC2E,EAAQmE,OAAO,CAACX,EAAmB,KAAK,EAAG,IAAMhK,KAGjDA,GAER,CACJ,CAQA,EAkBM,CAAE8K,IAAK,CAAEvO,UAAW,CAAE2L,WAAY6C,CAAQ,CAAE,CAAE,CAAEC,QAAS,CAAEzO,UAAW,CAAE2L,WAAY+C,CAAY,CAAE,CAAE,CAAE,CAAG,AAACrF,IAA2IK,WAAW,CAEhQ,CAAElH,OAAQmM,CAAmB,CAAEC,SAAAA,CAAQ,CAAE/L,KAAMgM,CAAiB,CAAE,CAAIvO,GAM5E,OAAMwO,UAAqBJ,EACvBjK,aAAc,CAMV,KAAK,IAAIsK,WACT,IAAI,CAACC,mBAAmB,CAAG,EAC3B,IAAI,CAAChB,SAAS,CAAG,MACrB,CAMAT,KAAKC,CAAM,CAAE,CACTF,EAA0BC,IAAI,CAAC,IAAI,CAAEC,EACzC,CACAM,cAAe,CACX,IAAMhE,EAAS,IAAI,CAACA,MAAM,CAAEpJ,EAAUoJ,EAAOpJ,OAAO,CAChDuO,EAAY,KAAK,CAACnB,eAiBtB,OAfI,IAAI,CAACoB,IAAI,CAACvL,KAAK,EAAImG,EAAOqF,OAAO,CAACrF,EAAOsF,QAAQ,CAAC,CAACzL,KAAK,EACxD,IAAI,CAACuL,IAAI,CAACG,QAAQ,CAAC/J,MAAM,CACzB2J,GAAa,0BAER,AAAC,IAAI,CAACC,IAAI,CAACI,OAAO,EACtB,IAAI,CAACJ,IAAI,CAACK,MAAM,EAChBzF,EAAOqF,OAAO,CAACrF,EAAOsF,QAAQ,CAAC,CAACE,OAAO,EACvCT,EAAkBnO,EAAQ8O,cAAc,CAAE,CAAC9O,EAAQ+O,mBAAmB,EAGlE,AAAC,IAAI,CAACP,IAAI,CAACI,OAAO,EACtB,IAAI,CAACJ,IAAI,CAACK,MAAM,EAChBzF,EAAOqF,OAAO,CAACrF,EAAOsF,QAAQ,CAAC,CAACE,OAAO,EACxCL,CAAAA,GAAa,2BAA0B,EALvCA,GAAa,wCAOVA,CACX,CAQArE,SAAU,CACN,MAAO8E,CAAAA,CAAQ,CAAA,IAAI,CAACpF,EAAE,EAAIsE,EAAS,IAAI,CAAC/D,KAAK,CAAA,CACjD,CACA5B,SAASoB,CAAK,CAAE,CACZ,KAAK,CAACpB,SAAS0G,KAAK,CAAC,IAAI,CAAEZ,WAEvB,IAAI,CAAC9E,OAAO,EACZ,IAAI,CAACA,OAAO,CAAC5D,IAAI,CAAC,CACdtF,OAAQsJ,CAAAA,CAAAA,AAAU,UAAVA,CAAgB,CAC5B,EAER,CACA0D,YAAa,CACT,OAAOa,EAAS,IAAI,CAACgB,KAAK,GAAK,AAAW,OAAX,IAAI,CAAC/N,CAAC,AACzC,CACJ,CACA8M,EAAoBG,EAAa9O,SAAS,CAAE,CACxC6P,WAAYrB,EAASxO,SAAS,CAAC6P,UAAU,AAC7C,GAuBA,GAAM,CAAEnN,SAAUoN,CAA8B,CAAE,CAAIxP,IAmBhDyP,GAAwB,CA4B1BN,oBAAqB,CAAA,EACrBO,eAAgB,IAIhBC,aAAc,EAmFdC,aAAc,CAAA,EAIdC,OAAQ,KAAK,EAYbC,aAAc,CAAA,EAIdC,WAAY,CACRC,QAAS,CAAA,EACTxK,UAAW,WACP,IAAMiE,EAAQ,IAAI,EAAI,IAAI,CAACA,KAAK,CAC5B,IAAI,CAACA,KAAK,CACV,CAAC,EACL,OADe+F,EAA+B/F,EAAMwG,IAAI,EAAIxG,EAAMwG,IAAI,CAAG,EAE7E,EAWAC,QAAS,CAAA,EACTC,OAAQ,CAAA,EACR3P,QAAS,EACTa,cAAe,SACfT,MAAO,CACHwP,aAAc,UAClB,CACJ,EACAC,QAAS,CACLC,aAAc,GACdC,YAAa,0CAabC,cAAe,4CACnB,EAOAC,kBAAmB,CAAA,EAmBnBC,gBAAiB,eAOjBC,wBAAyB,WAWzBC,2BAA4B,CAAA,EAS5BC,gBAAiB,CAAA,EAQjBC,iBAAkB,CAId3P,SAAU,CAcNC,MAAO,QAIPE,EAAG,IAIHC,EAAG,EACP,CACJ,EAkKAwP,YAAa,UAIbC,YAAa,EACbrG,SAAU,aAQVsG,QAAS,IAMTvQ,OAAQ,CAOJwQ,MAAO,CAIHH,YAAa,UAQbI,WAAY,AAAsK,IAAtK,AAACpI,IAA2IK,WAAW,CAACgI,OAAO,CAI3KC,KAAM,CAAA,EAONJ,QAAS,IAITK,OAAQ,CAAA,CACZ,CACJ,EACAC,aAAc,YAcdC,eAAgB,CAAA,EAsBhBC,QAAS,CAQL9C,UAAW,KAAK,EAQhB9N,MAAO,KAAK,EAQZmP,QAAS,CAAA,EAQT0B,WAAY,KAAK,EASjBC,YAAa,KAAK,EAUlB1B,KAAM,KAAK,EAWX2B,gBAAiB,KAAK,EAStBC,mBAAoB,EACpBnB,gBAAiB,CACboB,SAAU,EACVC,SAAU,EACVC,gBAAiB,CACrB,EACAnC,OAAQ,CACJoC,UAAW,EACXC,OAAQ,CACZ,CACJ,CACJ,CAmII7T,CACDA,CAAAA,GAAqBA,CAAAA,EAAmB,CAAC,CAAA,CAAC,EADxB8T,SAAS,CAN1B,SAASA,EAAUC,CAAI,CAAEC,CAAI,CAAEC,CAAO,EAClC,IAAMC,EAAOF,EAAKzS,IAAI,CAAC0S,GAAW,IAAI,CAAEF,EACpCG,AAAS,EAAA,IAATA,GACAJ,EAAUI,EAAMF,EAAMC,EAE9B,EAQyB,IAAME,GAA4BnU,EAiBzD,CAAE6D,OAAQuQ,EAAoB,CAAEC,QAAAA,EAAO,CAAEpE,SAAUqE,EAAsB,CAAEC,SAAAA,EAAQ,CAAEvQ,MAAOwQ,EAAmB,CAAEtQ,KAAMuQ,EAAkB,CAAEC,eAAAA,EAAc,CAAE,CAAI/S,IAwN/J,CAAEgT,MAAOnS,EAAK,CAAE,CAAIgI,IAGpB,CAAE9G,SAAUkR,EAAsB,CAAEC,KAAAA,EAAI,CAAE,CAAIlT,IAG9C,CAAEkJ,OAAQiK,EAAY,CAAEhF,QAASiF,EAAa,CAAE,CAAG,AAACrK,IAA2IK,WAAW,CAO1M,CAAEiK,SAAUC,EAAsB,CAAEC,gBAAiBC,EAA6B,CAAEC,aAAcC,EAA0B,CAAE,CArC9G,CAClBL,SAvLJ,SAAkBzE,CAAI,CAAExO,CAAO,EAC3B,IACqBqJ,EAAOpG,EAAOyM,EAAc6D,EAAmB9S,EAAO+S,EADrEC,EAAQzT,EAAQyT,KAAK,CAAEC,EAAoB1T,EAAQ0T,iBAAiB,CAAEC,EAAc3T,EAAQ2T,WAAW,CAAEC,EAAmB5T,EAAQ4T,gBAAgB,CAAExK,EAASpJ,EAAQoJ,MAAM,CAAEyK,EAAS7T,EAAQ6T,MAAM,CAAEC,EAAW9T,EAAQ8T,QAAQ,CAAEC,EAAS3K,EAAO2K,MAAM,CAAEC,EAAoB5K,EAAO9G,KAAK,CAACtC,OAAO,CAACsC,KAAK,CA+BjT,OAhBIkM,IACAnF,EAAQ0K,CAAM,CAACvF,EAAKpG,CAAC,CAAC,CACtBnF,EAAQyQ,CAAiB,CAAClF,EAAKvL,KAAK,CAAC,EAAI,CAAC,EACxBoG,GAASpG,EAAMyM,YAAY,GAEzC6D,EAAoBlK,EAAMoK,KAAK,CAAII,CAAAA,EAC/BA,EAAOjP,MAAM,CACboP,EAAkBC,UAAU,AAAD,EAC/BvE,EAAemE,GAAUA,CAAM,CAACN,EAAkB,EAGlD,AAACnK,EAAO9G,KAAK,CAAC+E,UAAU,EACxB5G,CAAAA,EAAQiS,GAAmBrJ,GAASA,EAAMrJ,OAAO,CAACS,KAAK,CAAEwC,GAASA,EAAMxC,KAAK,CAAEiP,EAAciE,GAAeO,AAtB/F,CAAA,AAACzT,IAClB,IAAM0T,EAAiBlR,GAASA,EAAMkR,cAAc,QACpD,AAAIA,GACAA,AAAuB,eAAvBA,EAAerV,GAAG,EAClB2U,GACAK,EACOrL,IAAsGmK,KAAK,CAACnS,GAAO2T,QAAQ,CAACD,EAAeE,EAAE,CAAIZ,CAAAA,EAAQK,CAAO,GAAI3U,GAAG,GAE3KsB,CACX,CAAA,EAaiIkT,GAAcvK,EAAO3I,KAAK,CAAA,EAEvJ+S,EAAad,GAAmBrJ,GAASA,EAAMrJ,OAAO,CAACwT,UAAU,CAAEvQ,GAASA,EAAMuQ,UAAU,CAAED,EAAmBK,EAAkB5T,EAAQwT,UAAU,GAElJ,CACH/S,MAAOA,EACP+S,WAAYA,CAChB,CACJ,EAoJIL,gBAlIJ,SAAyBrG,CAAM,EAC3B,IACIwH,EAAUC,EAAWnM,EAAGoM,EAAMH,EAAII,EADhCC,EAAS,CAAC,EAEhB,GAAIlC,GAAS1F,GA2BT,IA1BA0H,EAAOjC,GAAuBzF,EAAO0H,IAAI,EAAI1H,EAAO0H,IAAI,CAAG,EAC3DC,EAAS3H,EAAO2H,MAAM,CACtBF,EAAY,CAAC,EACbD,EAAW9B,GAAS1F,EAAOwH,QAAQ,EAAIxH,EAAOwH,QAAQ,CAAG,CAAC,EACtDhC,GAAQmC,IACRF,CAAAA,EAAYE,EAAOE,MAAM,CAAC,CAACvV,EAAK4S,KAC5B,IAAI/O,EAAOwN,EAAiBzQ,EAgB5B,OAfIwS,GAASR,IAASO,GAAuBP,EAAK/O,KAAK,IAEnDwN,EAAkBiC,GAAmB1S,AADrCA,CAAAA,EAAUyS,GAAoB,CAAC,EAAGT,EAAI,EACOvB,eAAe,CAAE6D,EAAS7D,eAAe,EAEtF,OAAOzQ,EAAQyQ,eAAe,CAC9B,OAAOzQ,EAAQiD,KAAK,CAGhBuP,GAASpT,CAAG,CADhB6D,EAAQ+O,EAAK/O,KAAK,CAAIwN,CAAAA,EAAkB,EAAI+D,EAAO,CAAA,EAC5B,EACnB/B,GAAoB,CAAA,EAAMrT,CAAG,CAAC6D,EAAM,CAAEjD,GAGtCZ,CAAG,CAAC6D,EAAM,CAAGjD,GAGdZ,CACX,EAAG,CAAC,EAAC,EAETiV,EAAK9B,GAAuBzF,EAAOuH,EAAE,EAAIvH,EAAOuH,EAAE,CAAG,EAChDjM,EAAI,EAAGA,GAAKiM,EAAIjM,IACjBsM,CAAM,CAACtM,EAAE,CAAGqK,GAAoB,CAAC,EAAG6B,EAAU9B,GAAS+B,CAAS,CAACnM,EAAE,EAAImM,CAAS,CAACnM,EAAE,CAAG,CAAC,GAG/F,OAAOsM,CACX,EAgGIE,aAvBJ,SAAsBxL,CAAM,CAAEyL,CAAW,EACrC,GAAM,CAAEvS,MAAAA,CAAK,CAAEtC,QAAAA,CAAO,CAAE,CAAGoJ,EAAQ,CAAE0L,aAAAA,EAAe,CAAC,CAAEC,UAAAA,EAAY,CAAC,CAAE,CAAG/U,EAAS,CAAEgV,UAAAA,EAAY,CAAC,CAAE,CAAG1S,EAGtG,GAAIyS,AAAc,SAAdA,EAAsB,CACtB,GAAI,AAAwB,UAAxB,OAAOD,GAA6B,KAAKG,IAAI,CAACH,GAE9C,OAAOE,EADkDH,CAAAA,EAAcK,AAAtDC,WAAWL,GAAgB,IAAuCD,CAAAA,EAAc,CAAA,CAAC,EAGtG,IAAMO,EAAYC,OAAOP,GACzB,MAAO,AAAEE,CAAAA,EAAYI,CAAQ,EACxBP,CAAAA,GAAe,CAAA,EAAMO,CAC9B,CACA,OAAOzC,GAAeoC,EAAWC,EACrC,EAUIM,cA3FJ,SAASA,EAAcC,CAAI,CAAEvV,CAAO,EAChC,IAAMwV,EAASxV,EAAQwV,MAAM,CAAEC,EAASzV,EAAQyV,MAAM,CAAqCC,EAAWC,AAAhC3V,EAAQ2V,WAAW,AAAwB,CAACF,EAAO,CAAEhF,EAAmBzQ,AAA4B,CAAA,IAA5BA,EAAQyQ,eAAe,CAAsCpH,EAAQ0K,AAAxB/T,EAAQ+T,MAAM,AAAgB,CAACwB,EAAKnN,CAAC,CAAC,CAAEwN,EAAevM,GAASA,EAAMrJ,OAAO,EAAI,CAAC,EAAG2O,EAAW,EAAE,CACzRkH,EAAgB,CACpBN,CAAAA,EAAKO,YAAY,CAAGP,EAAKtS,KAAK,CAAIwN,CAAAA,EAAkB,EAAIiF,EAASzS,KAAK,AAAD,EACrEsS,EAAK1F,IAAI,CAAG6C,GAAmBrJ,GAASA,EAAMwG,IAAI,CAAE,IACpD0F,EAAKQ,OAAO,CAAIN,IAAWF,EAAK3L,EAAE,EAC9B5J,AAAoB,CAAA,IAApBA,EAAQ+V,OAAO,CACf,AAAkB,YAAlB,OAAOP,GACPD,CAAAA,EAAOC,EAAOD,EAAMvV,EAAO,EAG/BuV,EAAK5G,QAAQ,CAACqH,OAAO,CAAC,CAACC,EAAO7N,KAC1B,IAAM8N,EAAa7D,GAAqB,CAAC,EAAGrS,GAC5CqS,GAAqB6D,EAAY,CAC7BzC,MAAOrL,EACP0L,SAAUyB,EAAK5G,QAAQ,CAAC/J,MAAM,CAC9BmR,QAASR,EAAKQ,OAAO,AACzB,GACAE,EAAQX,EAAcW,EAAOC,GAC7BvH,EAASjC,IAAI,CAACuJ,GACVA,EAAMF,OAAO,EACbF,CAAAA,GAAiBI,EAAME,GAAG,AAAD,CAEjC,GAEA,IAAMhM,EAAQuI,GAAmBkD,EAAazL,KAAK,CAAE0L,GAMrD,OALAN,EAAKQ,OAAO,CAAG5L,GAAS,GAAM0L,CAAAA,EAAgB,GAAKN,EAAKQ,OAAO,AAAD,EAC9DR,EAAK5G,QAAQ,CAAGA,EAChB4G,EAAKM,aAAa,CAAGA,EACrBN,EAAK1G,MAAM,CAAG0G,EAAKQ,OAAO,EAAI,CAACF,EAC/BN,EAAKY,GAAG,CAAGhM,EACJoL,CACX,EA4DIlC,aA/CJ,SAAsBjK,CAAM,EACxB,IAAIgN,EAAQpW,EAaZ,OAZIwS,GAASpJ,KAETpJ,EAAUwS,GAASpJ,EAAOpJ,OAAO,EAAIoJ,EAAOpJ,OAAO,CAAG,CAAC,EAEvDoW,EAAS1D,GAAmBtJ,EAAOsF,QAAQ,CAAE1O,EAAQoW,MAAM,CAAE,IAEzD5D,GAASpJ,EAAOpF,WAAW,GAC3BoF,CAAAA,EAAOpF,WAAW,CAACoS,MAAM,CAAGA,CAAK,EAGrChN,EAAOsF,QAAQ,CAAG0H,GAEfA,CACX,CAiCA,EAiCM,CAAExU,SAAUyU,EAAsB,CAAEC,SAAAA,EAAQ,CAAEC,MAAAA,EAAK,CAAEC,aAAAA,EAAY,CAAEC,MAAAA,EAAK,CAAE5U,QAAS6U,EAAqB,CAAEC,MAAAA,EAAK,CAAE7U,OAAQ8U,EAAoB,CAAE7U,UAAW8U,EAAuB,CAAEvE,QAASwE,EAAqB,CAAE5I,SAAU6I,EAAsB,CAAEvE,SAAUwE,EAAsB,CAAEhV,SAAUiV,EAAsB,CAAEhV,MAAOiV,EAAmB,CAAE/U,KAAMgV,EAAkB,CAAE/U,WAAYgV,EAAwB,CAAEC,MAAAA,EAAK,CAAEC,WAAAA,EAAU,CAAE,CAAI1X,IAClbwL,IAA0GmM,SAAS,CAAC7K,IAAI,CAAC,aAAc,wBAYvI,IAAI8K,GAA2B,CAAA,EAO/B,SAASC,KACL,IACIC,EADiBC,EAAQvO,AAAd,IAAI,CAAiBuO,KAAK,CAAEC,EAAQxO,AAApC,IAAI,CAAuCwO,KAAK,CAE3DD,GAASC,IACLxO,AAHO,IAAI,CAGJyO,EAAE,CAAC,YACVH,EAAW,CACPI,UAAW,CAAA,EACXC,cAAe,EACflG,UAAW,EACXmG,IAAK,EACLC,WAAY,EACZ1L,IAxBA,IAyBA2L,WAAY,EACZC,YAAa,CAAA,EACbC,MAAO,KAAK,EACZC,cAAe,EAAE,AACrB,EACAzB,GAAqBgB,EAAM5X,OAAO,CAAE0X,GACpCd,GAAqBe,EAAM3X,OAAO,CAAE0X,GACpCF,GAA2B,CAAA,GAEtBA,KACLI,EAAMU,UAAU,CAACV,EAAM5T,WAAW,EAClC2T,EAAMW,UAAU,CAACX,EAAM3T,WAAW,EAClCwT,GAA2B,CAAA,GAGvC,CAaA,MAAMe,WAAsBvF,GACxBjP,aAAc,CAMV,KAAK,IAAIsK,WACT,IAAI,CAACmK,UAAU,CAAG,CAEtB,CAMA,OAAO5U,QAAQoH,CAAW,CAAE,CACpBoM,GAAyBvE,GAAwB,kBACjDwD,GAAuBrL,EAAa,gBAAiByM,GAE7D,CAOAgB,oBAAoBC,CAAe,CAAEC,CAAI,CAAEpT,CAAK,CAAEqT,CAAY,CAAE,CAC5D,IAAMnN,EAAOlG,EAAMkG,IAAI,CAAEoN,EAAMtT,EAAM0G,KAAK,CAACrH,MAAM,CAAG,EAChDkU,EAAIC,EAAIC,EAAIC,EAAIC,EAAK3T,EAAMuG,EAAE,CAAEqN,EAAK5T,EAAMyG,EAAE,CAAEoN,EAAMhR,EAAI,EAQ5D,IAAK,IAAMoD,KAPPmN,GACAO,EAAK3T,EAAMsG,EAAE,CACbsN,EAAK5T,EAAMwG,EAAE,EAGbqN,EAAO7T,EAAM0G,KAAK,CAAC4M,EAAI,CAEXtT,EAAM0G,KAAK,EACnB0M,CAAAA,GAASvQ,EAAIyQ,CAAG,IACZtT,AAAoB,IAApBA,EAAMmG,SAAS,EACfoN,EAAKrN,EAAKvK,CAAC,CACX6X,EAAKtN,EAAKtK,CAAC,CAEX8X,EAAKzN,EADLwN,CAAAA,EAAKE,CAAC,IAINJ,EAAKrN,EAAKvK,CAAC,CACX6X,EAAKtN,EAAKtK,CAAC,CAEX6X,EAAKxN,EADLyN,CAAAA,EAAKE,CAAC,GAGVP,EAAalM,IAAI,CAAC,CACdxL,EAAG4X,EACH3X,EAAG4X,EACHlW,MAAOmW,EACP7Y,OAAQqW,GAAayC,EACzB,GACI1T,AAAoB,IAApBA,EAAMmG,SAAS,CACfD,EAAKtK,CAAC,CAAGsK,EAAKtK,CAAC,CAAG8X,EAGlBxN,EAAKvK,CAAC,CAAGuK,EAAKvK,CAAC,CAAG8X,GAG1B5Q,GAAQ,EAGZ7C,EAAMoH,KAAK,GACPpH,AAAoB,IAApBA,EAAMmG,SAAS,CACfnG,EAAM1C,KAAK,CAAG0C,EAAM1C,KAAK,CAAGqW,EAG5B3T,EAAMpF,MAAM,CAAGoF,EAAMpF,MAAM,CAAGgZ,EAElC1N,EAAKtK,CAAC,CAAGsK,EAAK4N,MAAM,CAAClY,CAAC,CAAIsK,CAAAA,EAAK4N,MAAM,CAAClZ,MAAM,CAAGoF,EAAMpF,MAAM,AAAD,EAC1DsL,EAAKvK,CAAC,CAAGuK,EAAK4N,MAAM,CAACnY,CAAC,CAAIuK,CAAAA,EAAK4N,MAAM,CAACxW,KAAK,CAAG0C,EAAM1C,KAAK,AAAD,EACpD6V,GACAnT,CAAAA,EAAMmG,SAAS,CAAG,EAAInG,EAAMmG,SAAS,AAAD,EAGpC,AAACiN,GACDpT,EAAMiH,UAAU,CAAC4M,EAEzB,CACAE,cAAcZ,CAAe,CAAEW,CAAM,CAAE1K,CAAQ,CAAE,CAC7C,IAAMiK,EAAe,EAAE,CACnBW,EAAM7N,EAAY2N,EAAO3N,SAAS,CAAExK,EAAImY,EAAOnY,CAAC,CAAEC,EAAIkY,EAAOlY,CAAC,CAAE0B,EAAQwW,EAAOxW,KAAK,CAAE1C,EAASkZ,EAAOlZ,MAAM,CAAE2Y,EAAIC,EAAIC,EAAIC,EAC9H,IAAK,IAAMhD,KAAStH,EAChB4K,EACI,AAACF,EAAOxW,KAAK,CAAGwW,EAAOlZ,MAAM,CAAK8V,CAAAA,EAAME,GAAG,CAAGkD,EAAOlD,GAAG,AAAD,EAC3D2C,EAAK5X,EACL6X,EAAK5X,EACDuK,AAAc,IAAdA,GAGA7I,GADAmW,EAAKO,EADLN,CAAAA,EAAK9Y,CAAK,EAGVe,GAAQ8X,IAKR7Y,GADA8Y,EAAKM,EADLP,CAAAA,EAAKnW,CAAI,EAGT1B,GAAQ8X,GAEZL,EAAalM,IAAI,CAAC,CACdxL,EAAG4X,EACH3X,EAAG4X,EACHlW,MAAOmW,EACP7Y,OAAQ8Y,EACRvN,UAAW,EACXyK,IAAK,CACT,GACIuC,GACAhN,CAAAA,EAAY,EAAIA,CAAQ,EAGhC,OAAOkN,CACX,CACAY,wBAAwBd,CAAe,CAAEW,CAAM,CAAE1K,CAAQ,CAAE,CACvD,IAAqBiK,EAAe,EAAE,CAAEnN,EAAO,CAC3CvK,EAAGmY,EAAOnY,CAAC,CACXC,EAAGkY,EAAOlY,CAAC,CACXkY,OAAQA,CACZ,EAAG3N,EAAY2N,EAAO3N,SAAS,CAAEmN,EAAMlK,EAAS/J,MAAM,CAAG,EAAGW,EAAQ,IAAI8F,EAA8BgO,EAAOlZ,MAAM,CAAEkZ,EAAOxW,KAAK,CAAE6I,EAAWD,GAC1I8N,EAAMnR,EAAI,EAEd,IAAK,IAAM6N,KAAStH,EAChB4K,EACI,AAACF,EAAOxW,KAAK,CAAGwW,EAAOlZ,MAAM,CAAK8V,CAAAA,EAAME,GAAG,CAAGkD,EAAOlD,GAAG,AAAD,EAC3D5Q,EAAMiH,UAAU,CAAC+M,GACbhU,EAAM2G,EAAE,CAACC,EAAE,CAAG5G,EAAM2G,EAAE,CAACE,EAAE,EACzBhD,AAZO,IAAI,CAYJqP,mBAAmB,CAACC,EAAiB,CAAA,EAAOnT,EAAOqT,EAAcnN,GAIxErD,IAAMyQ,GACNzP,AAjBO,IAAI,CAiBJqP,mBAAmB,CAACC,EAAiB,CAAA,EAAMnT,EAAOqT,EAAcnN,GAG3E,EAAErD,EAEN,OAAOwQ,CACX,CAKAa,eAAepQ,CAAK,CAEpBqQ,CAAS,CAETC,CAAY,CAAE,CACV5G,GAAazT,SAAS,CAACma,cAAc,CAACxK,KAAK,CAAC,IAAI,CAAEZ,WAC9ChF,EAAMqQ,SAAS,EAEfrQ,EAAMqQ,SAAS,CAAC/T,IAAI,CAAC,CAAEtF,OAAQ,AAACgJ,CAAAA,EAAMmF,IAAI,CAACnO,MAAM,EAAI,CAAA,EAAK,CAAE,EAEpE,CACAuZ,mBAAoB,CAChB,IAAMxQ,EAAS,IAAI,CAAEyQ,EAAazQ,EAAOyQ,UAAU,EAAI,CAAC,EAAG,CAAExI,QAAAA,CAAO,CAAE,CAAGjI,EAAOpJ,OAAO,CAAEyR,EAAqBJ,GAASI,oBAAsB,EAC7I,GAAIJ,GAASzB,QAAS,CAClB,IAAMkK,EAAe,CAAC,EAChBC,EAAc,AAACvL,IACjB,GAAIA,GAAMnF,OAAOoE,UAAW,CACxB,GAAM,CAAE5K,MAAAA,EAAQ,CAAC,CAAE1C,OAAAA,EAAS,CAAC,CAAE,CAAGqO,EAAKnF,KAAK,CAACoE,SAAS,CAChD,CAAE6D,WAAAA,EAAa,CAAC,CAAEC,YAAAA,EAAc,CAAC,CAAE,CAAGF,EAAS2I,EAAgBtD,GAAsBnF,GAAc0I,EAAgB1I,EACrHD,EAAaC,EACbD,EAAaA,EACbzO,CAAAA,EAAQyO,GACRnR,EAAU6Z,CAAAA,EAAgBzI,EAAcD,CAAS,GACjD4I,AAN2DrX,EAAQ1C,EAM5D8Z,CAAY,GACf,CAACzL,EAAKI,OAAO,EAAI8H,GAAsBlI,EAAK6K,MAAM,IAC9C,AAACS,CAAY,CAACtL,EAAK6K,MAAM,CAAC,EAC1BS,CAAAA,CAAY,CAACtL,EAAK6K,MAAM,CAAC,CAAG,EAAE,AAAD,EAEjCS,CAAY,CAACtL,EAAK6K,MAAM,CAAC,CAAC3M,IAAI,CAAC8B,GAG3C,CACAA,GAAMG,SAASqH,QAAQ,AAACC,IACpB8D,EAAY9D,EAChB,EACJ,EAEA,IAAK,IAAMoD,KADXU,EAAY3Q,EAAOmM,IAAI,EACFuE,EACbA,CAAY,CAACT,EAAO,EAChBS,CAAY,CAACT,EAAO,CAACzU,MAAM,CAAG6M,GAC9BqI,CAAY,CAACT,EAAO,CAACrD,OAAO,CAAC,AAACxH,IAC1B,IAAMiF,EAAQoG,CAAU,CAACR,EAAO,CAACc,OAAO,CAAC3L,EAAKpG,CAAC,EAC/C,GAAIqL,AAAU,KAAVA,EAAc,CACdoG,CAAU,CAACR,EAAO,CAACe,MAAM,CAAC3G,EAAO,GACjC,IAAM7J,EAAK,CAAC,kCAAkC,EAAE4E,EAAK6K,MAAM,EAAI,OAAO,CAAC,CACnEgB,EAAajR,EAAO2K,MAAM,CACzBuG,IAAI,CAAC,AAAC9O,GAAMA,EAAE5B,EAAE,GAAKA,GAC1B,GAAI,CAACyQ,EAAY,CACb,IAAME,EAAanR,EAAO6B,UAAU,CAAEuP,EAAapR,EAAO2K,MAAM,CAACnP,MAAM,CASvEgS,GARAyD,EAAa,IAAIE,EAAWnR,EAAQ,CAChCmF,UAAW8C,EAAQ9C,SAAS,CAC5B9N,MAAO4Q,EAAQ5Q,KAAK,CACpBmJ,GAAAA,EACA6J,MAAO+G,EACP5L,QAAS,CAAA,EACTzE,MAAO,CACX,GACiC,CAC7BsQ,aAAc,SAClB,GACArR,EAAO2K,MAAM,CAACrH,IAAI,CAAC2N,GACnBR,CAAU,CAACR,EAAO,CAAC3M,IAAI,CAAC8N,GACxBX,CAAU,CAACjQ,EAAG,CAAG,EAAE,AACvB,CACA,IAAM8Q,EAASL,EAAW/L,mBAAmB,CAAG,EAAG6H,EAAM/M,EAAO2K,MAAM,CAACsG,EAAW5G,KAAK,CAAC,CACnFzT,OAAO,CAACmK,KAAK,EAAI,EAAG0F,EAAOwB,EAAQxB,IAAI,EACxC,CAAC,EAAE,EAAE6K,EAAO,CAAC,AAGjBtR,CAAAA,EAAO2K,MAAM,CAACsG,EAAW5G,KAAK,CAAC,CAC1BnF,mBAAmB,CAAGoM,EAC3BtR,EAAO2K,MAAM,CAACsG,EAAW5G,KAAK,CAAC,CAACzT,OAAO,CAACmK,KAAK,CACzCgM,EAAO3H,CAAAA,EAAKnF,KAAK,CAACc,KAAK,EAAI,CAAA,EAC/Bf,EAAO2K,MAAM,CAACsG,EAAW5G,KAAK,CAAC,CAAC5D,IAAI,CAAGA,EACvCgK,CAAU,CAACjQ,EAAG,CAAC8C,IAAI,CAAC8B,EAAKnF,KAAK,CAACoK,KAAK,CACxC,CACJ,EAIZrK,CAAAA,EAAOqF,OAAO,CAAG,CAAC,EAClBrF,EAAOuR,QAAQ,CAAG,EAAE,CACpBvR,EAAOyQ,UAAU,CAAGA,EACpB,IAAMtE,EAAOnM,EAAOwR,SAAS,CAAC,GAAI,GAAI,EAAGxR,EAAOyQ,UAAU,EAC1DzQ,EAAOjB,SAAS,CAACoN,EACrB,CACJ,CAcAsF,uBAAuBxB,CAAM,CAAEa,CAAI,CAAE,CACjC,IAAqBla,EAAUoJ,AAAhB,IAAI,CAAmBpJ,OAAO,CAAgDiD,EAAQyQ,AAAlCtK,AAApD,IAAI,CAAuDsK,iBAAiB,AAA2B,CAAC2F,EAAOpW,KAAK,CAAG,EAAE,CAAE6X,EAAY3D,GAAoBlU,GAAOqN,iBAC7KlH,AADW,IAAI,AACT,CAACnG,GAAOqN,gBAAgB,EAC9BrN,EAAMqN,eAAe,CAAGtQ,EAAQsQ,eAAe,EAAGyK,EAAY/a,EAAQwQ,0BAA0B,CAEpG7B,EAAW0K,EAAO1K,QAAQ,CAACqM,MAAM,CAAC,AAACxc,GAAM6a,EAAOzK,OAAO,EAAI,CAACpQ,EAAEyc,MAAM,EAAGC,EAAejY,GAAOiY,cAAgBlb,EAAQkb,YAAY,EAAI,EAAGxM,EAAWtF,AAJpI,IAAI,CAIuIqF,OAAO,CAACrF,AAJnJ,IAAI,CAIsJsF,QAAQ,CAAC,CAClL,GAAI,CAACoM,EACD,OAEJ,IAAIK,EAAiB,EAAE,CAAEC,EAAY1M,EAAS2M,WAAW,EAAExY,OAAS,EAAGyY,EAAa5M,EAAS2M,WAAW,EAAElb,QAAU,CAChH8C,CAAAA,GAAOsN,yBACP2J,CAAAA,EAAKxO,SAAS,CAAGzI,CAAAA,CAAAA,AAAkC,aAAlCA,EAAMsN,uBAAuB,AAAc,CAExD,EAER4K,EAAiB/R,AAdF,IAAI,AAcI,CAAC0R,EAAU,CAACZ,EAAMvL,GACzC,IAAIvG,EAAI,GACR,IAAK,IAAM6N,KAAStH,EAAU,CAC1B,IAAM4M,EAASJ,CAAc,CAAC,EAAE/S,EAAE,CAC9B6N,IAAUvH,IACV0M,EAAYA,GAAaG,EAAO1Y,KAAK,CACrCyY,EAAaC,EAAOpb,MAAM,EAE9B,IAAMqb,EAAsBN,EAAgB9R,CAAAA,AAtBjC,IAAI,CAsBoCuO,KAAK,CAAC8D,GAAG,CAAGH,CAAS,EAAII,EAAsBR,EAAgB9R,CAAAA,AAtBvG,IAAI,CAsB0GwO,KAAK,CAAC6D,GAAG,CAAGH,CAAS,EAM9I,GALArF,EAAMsF,MAAM,CAAGrE,GAAoBqE,EAAQ,CACvCpF,IAAKF,EAAMJ,aAAa,CACxBnK,UAAYqP,EAAY,EAAIb,EAAKxO,SAAS,CAAGwO,EAAKxO,SAAS,AAC/D,GAEIuK,EAAMtH,QAAQ,CAAC/J,MAAM,EACrBqR,EAAM5M,KAAK,CAACsG,UAAU,EAAE/K,OAAQ,CAChC,IAAM+W,EAAWrF,GAASL,EAAM5M,KAAK,CAACsG,UAAU,CAACiM,GAAG,CAAC,AAACC,GAAOA,EAAG7b,OAAO,EACjE8P,SAAW+L,EAAG1b,MAAM,EAAI,IAAOiJ,CAAAA,AA/B9B,IAAI,CA+BiCwO,KAAK,CAAC6D,GAAG,CAAGH,CAAS,EAE7DK,EAAW1F,EAAMsF,MAAM,CAACpb,MAAM,CAAG,IACjC8V,EAAMsF,MAAM,CAACpa,CAAC,EAAIwa,EAClB1F,EAAMsF,MAAM,CAACpb,MAAM,EAAIwb,EAE/B,CACA,GAAIT,EAAc,CACd,IAAMY,EAAOxP,KAAK0L,GAAG,CAACwD,EAAqBvF,EAAMsF,MAAM,CAAC1Y,KAAK,CAAG,GAAIkZ,EAAOzP,KAAK0L,GAAG,CAAC0D,EAAqBzF,EAAMsF,MAAM,CAACpb,MAAM,CAAG,EAC/H8V,CAAAA,EAAMsF,MAAM,CAACra,CAAC,EAAI4a,EAClB7F,EAAMsF,MAAM,CAAC1Y,KAAK,EAAI,EAAIiZ,EAC1B7F,EAAMsF,MAAM,CAACpa,CAAC,EAAI4a,EAClB9F,EAAMsF,MAAM,CAACpb,MAAM,EAAI,EAAI4b,CAC/B,CACA9F,EAAMoF,WAAW,CAAGnE,GAAoBqE,EAAQ,CAC5Cra,EAAIqa,EAAOra,CAAC,CAAGkI,AA9CR,IAAI,CA8CW4S,SAAS,CAG/B7a,EAAG8a,AA9VH,IA8VaV,EAAOpa,CAAC,CAAGoa,EAAOpb,MAAM,CACrC0C,MAAQ0Y,EAAO1Y,KAAK,CAAGuG,AAlDhB,IAAI,CAkDmB4S,SAAS,AAC3C,GAEI/F,EAAMtH,QAAQ,CAAC/J,MAAM,EACrBwE,AAtDO,IAAI,CAsDJyR,sBAAsB,CAAC5E,EAAOA,EAAMsF,MAAM,CAEzD,CACA,IAAMW,EAAuB,CAAC1N,EAAMkG,EAAS,EAAE,CAAEyH,EAAY,CAAA,CAAI,IAC7D3N,EAAKG,QAAQ,CAACqH,OAAO,CAAC,AAACC,IACfkG,GAAalG,EAAMpH,MAAM,CACzB6F,EAAOhI,IAAI,CAACuJ,EAAM5M,KAAK,EAElB,AAAC8S,GAAclG,EAAMpH,MAAM,EAChC6F,EAAOhI,IAAI,CAACuJ,EAAM5M,KAAK,EAEvB4M,EAAMtH,QAAQ,CAAC/J,MAAM,EACrBsX,EAAqBjG,EAAOvB,EAAQyH,EAE5C,GACOzH,GAGX,GAAI1U,AAAuB,SAAvBA,EAAQoc,UAAU,EAClB/C,IAAW3K,GACX,IAAI,CAAC2N,oBAAoB,EAGzB,CAACH,EAAqBxN,EAAU,KAAK,EAAG,CAAA,GACnC4N,IAAI,CAAC,AAACjT,GAAU0N,GAAuB1N,EAAMrJ,OAAO,CAACmK,KAAK,IAC/D,CAAC4M,GAAuBrI,EAASrF,KAAK,EAAErJ,QAAQmK,OAAQ,CACxD,IAAMoS,EAASL,EAAqBxN,GAAW6M,EAASgB,EAAOX,GAAG,CAAC,AAACvS,GAAUA,EAAMrJ,OAAO,CAACmK,KAAK,EAAI,GAErGqS,EAAQD,EAAOX,GAAG,CAAC,CAAC,CAAEpN,KAAM,CAAE6M,YAAAA,CAAW,CAAE,CAAE,GAAMA,EAC/CA,EAAYxY,KAAK,CAAGwY,EAAYlb,MAAM,CACtC,GAAKsc,EAAWlB,EAAO5G,MAAM,CAAC,CAAC+H,EAAKvS,IAAUuS,EAAMvS,EAAO,GAA4DwS,EAAuBC,AAArEJ,EAAM7H,MAAM,CAAC,CAAC+H,EAAKvS,IAAUuS,EAAMvS,EAAO,GAAqCsS,EACxJI,EAAU,EAAGC,EAAU,EAC3BP,EAAOvG,OAAO,CAAC,CAAC3M,EAAOjB,KACnB,IAGA2U,EAAMxG,GAAMyG,AAHSzB,CAAAA,CAAM,CAACnT,EAAE,CAAIoU,CAAK,CAACpU,EAAE,CAAGmT,CAAM,CAACnT,EAAE,CAAI,CAAA,EAG/BuU,EAAsB,GAAK,KAClDM,EAAO,EAAIF,CACX1T,CAAAA,EAAMc,KAAK,GAGPqS,CAAK,CAACpU,EAAE,CAAG,IACX6U,CAAAA,GAAQT,CAAK,CAACpU,EAAE,CAAG,EAAC,EAEpB6U,EAAOH,GACPA,CAAAA,EAAUG,CAAG,EAEbA,EAAOJ,GACPA,CAAAA,EAAUI,CAAG,EAEjB5T,EAAM6T,cAAc,CAAG,AAAC7T,CAAAA,EAAM6T,cAAc,EAAI7T,EAAMc,KAAK,AAAD,EAAK4S,EAEvE,GAYA,AAACF,CAAAA,EAAU,MAASC,EAAU,GAAG,GAE7B,IAAI,CAACtE,UAAU,CAAG,IAClB,IAAI,CAACA,UAAU,GACf,IAAI,CAAClD,aAAa,CAAC+D,GACnBa,EAAK/D,GAAG,CAAGkD,EAAOlD,GAAG,CACrB,IAAI,CAAC0E,sBAAsB,CAACxB,EAAQa,KAKpCqC,EAAOvG,OAAO,CAAC,AAAC3M,IACZ,OAAOA,EAAM6T,cAAc,AAC/B,GACA,IAAI,CAAC5H,aAAa,CAAC+D,GACnB,IAAI,CAACb,UAAU,CAAG,EAE1B,CACJ,CAKA2E,WAAWpW,CAAC,CAAE,CACV,IAAMzE,EAAQ,IAAI,CAACA,KAAK,CAAEC,EAAcD,EAAMC,WAAW,CAAE4B,EAAO,EAAE,CACpE,GAAI5B,EAAa,CACb,IAAI6a,EAAqB,EACzBjZ,EAAKuI,IAAI,CAAC,CACNzJ,MAAOma,EACP/X,aAAc/C,EAAM8G,MAAM,CAAC,EAAE,AACjC,GACA,IAAIoF,EAAOzH,EAAEsW,MAAM,CAAC5O,OAAO,CAAC1H,EAAEuW,SAAS,CAAC,CAClCC,EAAa,EAAE,CAGrB,KAAO/O,EAAK6K,MAAM,EAAI7K,AAAgB,KAAhBA,EAAK6K,MAAM,EAC7BkE,EAAW7Q,IAAI,CAAC8B,GAChBA,EAAOzH,EAAEsW,MAAM,CAAC5O,OAAO,CAACD,EAAK6K,MAAM,CAAC,CAExC,IAAK,IAAM7K,KAAQ+O,EAAWC,OAAO,GACjCrZ,EAAKuI,IAAI,CAAC,CACNzJ,MAAO,EAAEma,EACT/X,aAAcmJ,CAClB,EAGArK,CAAAA,EAAKS,MAAM,EAAI,GACfT,CAAAA,EAAKS,MAAM,CAAG,CAAA,CAEtB,CACA,OAAOT,CACX,CAeAsZ,gBAAiB,CACb,IAAqB/J,EAAoBtK,AAA1B,IAAI,CAA6BsK,iBAAiB,CAAEK,EAAS3K,AAA7D,IAAI,CAAgE2K,MAAM,CAACiH,MAAM,CAAC,SAAUxc,CAAC,EACxG,OAAOA,EAAEgQ,IAAI,CAACuH,OAAO,EAAIW,GAAsBlY,EAAEkb,SAAS,CAC9D,GAAItZ,EAAUiX,GAAMjO,AAFL,IAAI,CAEQpJ,OAAO,CAAC2P,UAAU,EAAI,CAAC,EAAE,CAAC,EAAE,EAAEvP,QAASsd,EAAkB3J,EAAOuI,IAAI,CAAC,AAAC9Q,GAAMuL,GAAuBvL,EAAE0D,KAAK,GACrI,IAAK,IAAM7F,KAAS0K,EAAQ,CACxB,IAAMvT,EAAQ,CAAC,EAEfR,EAAU,CAAEQ,MAAAA,CAAM,EAAGyC,EAAQyQ,CAAiB,CAACrK,EAAMmF,IAAI,CAACvL,KAAK,CAAC,CAuBhE,GArBI,CAAA,CAACoG,EAAMmF,IAAI,CAACK,MAAM,EAClB,CAACxF,EAAMmF,IAAI,CAACI,OAAO,EAClBvF,EAAMmF,IAAI,CAACI,OAAO,EACfvF,EAAMmF,IAAI,CAACvL,KAAK,EAAImG,AAXjB,IAAI,CAWoBqF,OAAO,CAACrF,AAXhC,IAAI,CAWmCsF,QAAQ,CAAC,CAACzL,KAAK,GAC7DjD,CAAAA,EAAQ4P,OAAO,CAAG,CAAA,CAAI,EAGtB3M,GAAO0M,aACPuH,GAAoB,CAAA,EAAMlX,EAASqX,GAAMpU,EAAM0M,UAAU,CAAC,CAAC,EAAE,EAC7DvG,AAjBO,IAAI,CAiBJuU,aAAa,CAAG,IAAM,CAAA,GAI7BtU,EAAMmF,IAAI,CAACK,MAAM,CACjB7O,EAAQ+P,MAAM,CAAG,CAAA,EAEZ/P,EAAQ8P,OAAO,EACpB9P,CAAAA,EAAQiB,aAAa,CAAG,KAAI,EAI5BoI,EAAMoE,SAAS,EAAIiQ,EAAiB,CACpC,GAAM,CAAEvd,OAAAA,EAAS,CAAC,CAAE0C,MAAAA,EAAQ,CAAC,CAAE,CAAGwG,EAAMoE,SAAS,CACjD,GAAI5K,EAAQ,IAAM1C,EAAS,IAAMkJ,EAAMgE,UAAU,GAAI,CACjD,IAAMuQ,EAAiB/a,EACnB,EAAK7C,CAAAA,EAAQI,OAAO,EAAIA,GAAW,CAAA,CACvCI,CAAAA,EAAMqC,KAAK,CAAG,CAAC,EAAE+a,EAAe,EAAE,CAAC,CACnCpd,EAAMqd,SAAS,EAAKrd,CAAAA,EAAMqd,SAAS,CAAGvR,KAAKwR,KAAK,CAAC3d,EAAS,GAAE,EAC5DK,EAAMuJ,UAAU,CAAG,UAEf/J,EAAQ8P,OAAO,EACfzG,EAAMqQ,SAAS,EAAE/T,KAAK,CAClB9C,MAAO+a,CACX,EAGR,MAEIpd,EAAMqC,KAAK,CAAG,CAAC,EAAEA,EAAM,EAAE,CAAC,CAC1BrC,EAAMuJ,UAAU,CAAG,QAE3B,CAEAV,EAAM0U,SAAS,CAAG7G,GAAoBlX,EAASqJ,EAAMrJ,OAAO,CAAC2P,UAAU,CAC3E,CACA,KAAK,CAAC8N,eAAe1J,EACzB,CAKAiK,WAAWjK,EAAS,IAAI,CAACA,MAAM,CAAE,CAC7B,IAAqBzR,EAAQ8G,AAAd,IAAI,CAAiB9G,KAAK,CAAEkD,EAAWlD,EAAMkD,QAAQ,CAAE6B,EAAa/E,EAAM+E,UAAU,CAAErH,EAAUoJ,AAAhG,IAAI,CAAmGpJ,OAAO,CAAEkR,EAAS7J,EAAa,CAAC,EAAIrH,EAAQkR,MAAM,CAAE3B,EAAevP,EAAQuP,YAAY,CAAE0O,EAAuB3b,EAAM4b,UAAU,CAAGle,EAAQsP,cAAc,CAAEP,EAAsB/O,EAAQ+O,mBAAmB,CAClU,IAAK,IAAM1F,KAAS0K,EAAQ,CACxB,IAAM+B,EAAezM,EAAMmF,IAAI,CAACsH,YAAY,CAAE/I,EAAoB,CAAC,EAAGI,EAAU,CAAC,EAAG1F,EAAM,CAAC,EAAG0W,EAAW,eAAiB9U,EAAMmF,IAAI,CAACvL,KAAK,CAAEmb,EAAa,CAAC,CAAC/U,EAAME,OAAO,CAAE8U,EAAgBJ,GAAwBG,EAAY3Q,EAAYpE,EAAMoE,SAAS,CAErPpE,EAAMgE,UAAU,KAChBhE,EAAMiV,QAAQ,CAAG,CAAA,EACb/O,GACApC,CAAAA,EAAQoR,CAAC,CAAGhP,CAAW,EAE3B2H,GAAoB,CAAA,EAEpBmH,EAAgBtR,EAAoBI,EAEpCiR,EAAa3Q,EAAY,CAAC,EAE1BpG,EACI,CAAC,EACD+B,AAjBG,IAAI,CAiBA2B,YAAY,CAAC1B,EAAOA,EAAMmV,QAAQ,CAAG,SAAW,KAAK,IAI5DpV,AArBG,IAAI,CAqBAwB,YAAY,EAAIvD,GAEvBuP,GAAqBnP,EAAK2B,AAvBvB,IAAI,CAuB0BwB,YAAY,CAACvB,IAE7CD,AAzBE,IAAI,AAyBA,CAAC+U,EAAS,GACjB/U,AA1BG,IAAI,AA0BD,CAAC+U,EAAS,CAAG3Y,EAASC,CAAC,CAAC0Y,GACzBxY,IAAI,CAAC,CAGNtF,OAAQ,IAAQyV,CAAAA,GAAgB,CAAA,CACpC,GACKlQ,GAAG,CAACwD,AAhCN,IAAI,CAgCS7D,KAAK,EACrB6D,AAjCG,IAAI,AAiCD,CAAC+U,EAAS,CAACM,OAAO,CAAG,CAAA,IAInCpV,EAAMwD,IAAI,CAAC,CACPE,kBAAAA,EACAI,QAAAA,EACA1F,IAAAA,EACAlC,MAAO6D,AAzCA,IAAI,AAyCE,CAAC+U,EAAS,CACvB3Q,SAAUnE,EAAMmE,QAAQ,CACxBhI,SAAAA,EACA0L,OAAAA,EACAzD,UAAAA,EACAH,UAAWjE,EAAMiE,SAAS,AAC9B,GAGIyB,GAAuB1F,EAAME,OAAO,EACpCF,CAAAA,EAAMqV,OAAO,CAAG1e,EAAQ8O,cAAc,CAClC1F,AApDG,IAAI,CAoDAuV,aAAa,CAACtV,GACrBD,AArDG,IAAI,CAqDAwV,cAAc,CAACvV,EAAK,CAEvC,CACJ,CAMAuV,eAAevV,CAAK,CAAE,CAClB,MAAO,AAAC,CAAA,CAACA,EAAMmF,IAAI,CAACK,MAAM,IAAIxF,EAAMmF,IAAI,CAACI,OAAO,AAAD,GAC3CvF,EAAMO,EAAE,AAChB,CAMA+U,cAActV,CAAK,CAAE,CACjB,GAAM,CAAE+H,eAAAA,CAAc,CAAE,CAAG/H,EAAMD,MAAM,CAACpJ,OAAO,CAC3C0e,EAAU,CAAA,EAAOG,EACrB,GAAI,AAACxV,EAAMmF,IAAI,CAAC6K,MAAM,GAAK,IAAI,CAAC3K,QAAQ,EACpCrF,EAAMmF,IAAI,CAACK,MAAM,CACjB,GAAIuC,EACAsN,EAAUrV,EAAMO,EAAE,MAIlB,IADAiV,EAAaxV,EAAMmF,IAAI,CAChB,CAACkQ,GACA,AAA6B,KAAA,IAAtBG,EAAWxF,MAAM,EACxBwF,CAAAA,EAAa,IAAI,CAACpQ,OAAO,CAACoQ,EAAWxF,MAAM,CAAC,AAAD,EAE3CwF,EAAWxF,MAAM,GAAK,IAAI,CAAC3K,QAAQ,EACnCgQ,CAAAA,EAAUG,EAAWjV,EAAE,AAAD,EAKtC,OAAO8U,CACX,CAKAI,YAAYlV,CAAE,CAAErG,CAAM,CAAE,CACpBoT,GAAM,GAAI,CAAA,EAAO,KAAK,EAAG,CAAE,sBAAuB,yBAA0B,GAC5E,IAAI,CAACoI,WAAW,CAACnV,EAAIrG,EACzB,CACAyb,SAAU,CACN,IAAqBxQ,EAAOpF,AAAb,IAAI,CAAgBqF,OAAO,CAACrF,AAA5B,IAAI,CAA+BsF,QAAQ,CAAC,AACvDF,CAAAA,GAAQyI,GAAuBzI,EAAK6K,MAAM,GAC1CjQ,AAFW,IAAI,CAER2V,WAAW,CAACvQ,EAAK6K,MAAM,CAAE,CAAA,EAAM,CAAE4F,QAAS,kBAAmB,EAE5E,CACAC,aAAc,CAEV,GAAM,CAAEC,QAAAA,CAAO,CAAEC,QAAAA,CAAO,CAAE,CAAG,KAAK,CAACF,YAAY,IAAI,CAACG,cAAc,EAIlE,OAHA,IAAI,CAACC,QAAQ,CAAGH,EAChB,IAAI,CAACI,QAAQ,CAAGH,EAET,KAAK,CAACF,aACjB,CAgBAM,iBAAiBC,CAAI,CAAEC,CAAW,CAAE,CAChC,IAAMC,EAAM7I,GAAsB2I,GAAQA,EAAO,EAAE,CAAEG,EAAM9I,GAAsB4I,GAAeA,EAAc,EAAE,CAAEG,EAAgBF,EAAIhL,MAAM,CAAC,SAAUmL,CAAI,CAAEC,CAAI,CAAE3X,CAAC,EAChK,IAAMiR,EAASlC,GAAmB4I,EAAK1G,MAAM,CAAE,IAK/C,OAJI,AAAwB,KAAA,IAAjByG,CAAI,CAACzG,EAAO,EACnByG,CAAAA,CAAI,CAACzG,EAAO,CAAG,EAAE,AAAD,EAEpByG,CAAI,CAACzG,EAAO,CAAC3M,IAAI,CAACtE,GACX0X,CACX,EAAG,CACC,GAAI,EAAE,AACV,GAEA,IAAK,IAAMzG,KAAUra,OAAO4O,IAAI,CAACiS,GAAgB,CAC7C,IAAMlR,EAAWkR,CAAa,CAACxG,EAAO,CACtC,GAAI,AAAY,KAAXA,GAAmBuG,AAAwB,KAAxBA,EAAIzF,OAAO,CAACd,GAAiB,CACjD,IAAK,IAAMpD,KAAStH,EAChBkR,CAAa,CAAC,GAAG,CAACnT,IAAI,CAACuJ,EAE3B,QAAO4J,CAAa,CAACxG,EAAO,AAChC,CACJ,CACA,OAAOwG,CACX,CAKAG,SAAU,CACN,IAAqBC,EAAS,IAAI,CAACR,IAAI,CAAC7D,GAAG,CAAC,SAAUjd,CAAC,EACnD,OAAOA,EAAEiL,EAAE,AACf,GAIA,OAHAR,AAHe,IAAI,CAGZyQ,UAAU,CAAGzQ,AAHL,IAAI,CAGQoW,gBAAgB,CAAC,IAAI,CAACC,IAAI,CAAEQ,GACvD7W,AAJe,IAAI,CAIZqF,OAAO,CAAG,CAAC,EAClBrF,AALe,IAAI,CAKZuR,QAAQ,CAAG,EAAE,CACbvR,AANQ,IAAI,CAMLwR,SAAS,CAAC,GAAI,GAAI,EAAGxR,AANpB,IAAI,CAMuByQ,UAAU,EAAI,CAAC,EAC7D,CACAe,UAAUhR,CAAE,CAAE6J,CAAK,CAAExQ,CAAK,CAAEkB,CAAI,CAAEkV,CAAM,CAAE,CACtC,IAAqB1K,EAAW,EAAE,CAAEtF,EAAQD,AAA7B,IAAI,CAAgC2K,MAAM,CAACN,EAAM,CAC5DtT,EAAS,EAAG8V,EAEhB,IAAK,IAAM7N,KAAMjE,CAAI,CAACyF,EAAG,EAAI,EAAE,CAE3BzJ,EAASmM,KAAKC,GAAG,CAAC0J,AADlBA,CAAAA,EAAQ7M,AAJG,IAAI,CAIAwR,SAAS,CAACxR,AAJd,IAAI,CAIiB2K,MAAM,CAAC3L,EAAE,CAACwB,EAAE,CAAExB,EAAGnF,EAAQ,EAAGkB,EAAMyF,EAAE,EAC5CzJ,MAAM,CAAG,EAAGA,GACpCwO,EAASjC,IAAI,CAACuJ,GAElB,IAAMzH,EAAO,IAAIpF,AARF,IAAI,CAQK8W,SAAS,GAAGC,IAAI,CAACvW,EAAI6J,EAAO9E,EAAUxO,EAAQ8C,EARvD,IAAI,CAQkEoW,GACrF,IAAK,IAAMpD,KAAStH,EAChBsH,EAAMmK,UAAU,CAAG5R,EAQvB,OANApF,AAZe,IAAI,CAYZqF,OAAO,CAACD,EAAK5E,EAAE,CAAC,CAAG4E,EAC1BpF,AAbe,IAAI,CAaZuR,QAAQ,CAACjO,IAAI,CAAC8B,GACjBnF,IACAA,EAAMmF,IAAI,CAAGA,EACbA,EAAKnF,KAAK,CAAGA,GAEVmF,CACX,CAMA6R,SAAU,CACN,MAAO,CAAC,CAAC,IAAI,CAACC,SAAS,CAACC,QAAQ,AACpC,CACAJ,KAAK7d,CAAK,CAAEtC,CAAO,CAAE,CACjB,IAAMoJ,EAAS,IAAI,CAAEzG,EAAqBuU,GAAoBlX,EAAQsE,aAAa,CAAEtE,EAAQuC,WAAW,EAAGie,EAAkBnK,GAAuBjN,EAAQ,aAAc,AAAC3F,IACvK,IAAMzD,EAAUyD,EAAMO,WAAW,CAE7B0S,GAAsB1W,EAAQygB,gBAAgB,GAC9C,CAAC/J,GAAsB1W,EAAQ+O,mBAAmB,IAClD/O,EAAQ+O,mBAAmB,CAAG/O,EAAQygB,gBAAgB,CACtD,OAAOzgB,EAAQygB,gBAAgB,EAE/B/J,GAAsB1W,EAAQsE,aAAa,GAC3C,CAACoS,GAAsB1W,EAAQ0Q,gBAAgB,IAC/C1Q,EAAQ0Q,gBAAgB,CAAG1Q,EAAQsE,aAAa,CAChD,OAAOtE,EAAQsE,aAAa,EAGhC,IAAMqL,EAAa0H,GAAMrX,EAAQ2P,UAAU,EAAI,CAAC,EAChD3P,CAAAA,EAAQyU,MAAM,EAAEuB,QAAQ,AAAC/S,IACrB0M,EAAWjD,IAAI,CAACuC,KAAK,CAACU,EAAY0H,GAAMpU,EAAM0M,UAAU,EAAI,CAAC,GACjE,GACA,IAAI,CAAC0M,oBAAoB,CAAG1M,EAAW2M,IAAI,CAAC,AAACT,GAAOA,EAAG/L,OAAO,CAClE,GACA,KAAK,CAACqQ,KAAK7d,EAAOtC,GAElB,OAAOoJ,EAAOyH,OAAO,CAErBzH,EAAOsX,cAAc,CAAChU,IAAI,CAAC8T,GACvBpX,EAAOpJ,OAAO,CAAC+O,mBAAmB,GAClC3F,EAAOsX,cAAc,CAAChU,IAAI,CAAC2J,GAAuBjN,EAAQ,QAASA,EAAOuX,kBAAkB,GAC5FvX,EAAOsX,cAAc,CAAChU,IAAI,CAAC2J,GAAuBjN,EAAQ,cAAe,SAAUrC,CAAC,EAChF,IAAMzE,EAAQ8G,EAAO9G,KAAK,AACtBA,CAAAA,EAAMC,WAAW,EAEjBD,EAAMC,WAAW,CAACkC,gBAAgB,CAAC2E,EAAO+T,UAAU,CAACpW,GAE7D,IACAqC,EAAOsX,cAAc,CAAChU,IAAI,CAAC2J,GAAuBjN,EAAQ,SAE1D,SAAUrC,CAAC,CAAExD,CAAM,EACf,IAAMhB,EAAc,IAAI,CAACD,KAAK,CAACC,WAAW,AACtCA,CAAAA,GAAewE,EAAE/G,OAAO,CAACuC,WAAW,EACpCA,EAAYmF,MAAM,CAACX,EAAE/G,OAAO,CAACuC,WAAW,EAE5C,IAAI,CAACqe,oBAAoB,CAAG,IAAI,CAACvE,oBAAoB,AACzD,IACAjT,EAAOsX,cAAc,CAAChU,IAAI,CAAC2J,GAAuBjN,EAAQ,UAAW,SAAuBrC,CAAC,EACzF,IAAMzE,EAAQ,IAAI,CAACA,KAAK,AACpBA,CAAAA,EAAMC,WAAW,EAAI,CAACwE,EAAE8Z,mBAAmB,GAC3Cve,EAAMC,WAAW,CAACQ,OAAO,GACzBT,EAAMC,WAAW,CAAG,KAAK,EAEjC,KAEA,AAACD,EAAMC,WAAW,EAClBD,CAAAA,EAAMC,WAAW,CAAG,IAl0E8BoB,EAk0EFrB,EAAOK,EAAkB,EAE7EyG,EAAOsX,cAAc,CAAChU,IAAI,CAAC2J,GAAuB/T,EAAMC,WAAW,CAAE,KAAM,SAAUwE,CAAC,EAClF,IAAM+Z,EAAiB,IAAI,CAAC7d,KAAK,CAAG8D,EAAEK,QAAQ,CAC9C,IAAK,IAAIgB,EAAI,EAAGA,EAAI0Y,EAAgB1Y,IAChCgB,EAAO4V,OAAO,EAEtB,GACJ,CAKA2B,mBAAmBld,CAAK,CAAE,CACtB,IAAqB4F,EAAQ5F,EAAM4F,KAAK,CAAEqV,EAAUrV,GAAOqV,QAEvDzH,GAAuByH,KACvBrV,EAAMd,QAAQ,CAAC,IACfa,AAJW,IAAI,CAIR2V,WAAW,CAACL,EAAS,CAAA,EAAM,CAAEO,QAAS,OAAQ,GAE7D,CAKAlU,aAAa1B,CAAK,CAAEM,CAAK,CAAE,CACvB,IAAqB+J,EAAqBsD,GAAuB5N,AAAlD,IAAI,CAAqDsK,iBAAiB,EACrFtK,AADW,IAAI,CACRsK,iBAAiB,CACxB,CAAC,EAAIzQ,EAAQoG,GAASqK,CAAiB,CAACrK,EAAMmF,IAAI,CAACvL,KAAK,CAAC,EAAI,CAAC,EAAGjD,EAAU,IAAI,CAACA,OAAO,CAAE+gB,EAAepX,GAAS3J,EAAQM,MAAM,EAAIN,EAAQM,MAAM,CAACqJ,EAAM,EAAI,CAAC,EAAG4E,EAAYlF,GAAO+D,gBAAkB,GAGzMzH,EAAO,CACH,OAAU,AAAC0D,GAASA,EAAMsH,WAAW,EACjC1N,EAAM0N,WAAW,EACjBoQ,EAAapQ,WAAW,EACxB3Q,EAAQ2Q,WAAW,CACvB,eAAgBwG,GAAmB9N,GAASA,EAAMuH,WAAW,CAAE3N,EAAM2N,WAAW,CAAEmQ,EAAanQ,WAAW,CAAE5Q,EAAQ4Q,WAAW,EAC/H,UAAavH,GAAO2X,iBAChB/d,EAAM+d,eAAe,EACrBD,EAAaC,eAAe,EAC5BhhB,EAAQghB,eAAe,CAC3B,KAAQ3X,GAAO5I,OAAS,IAAI,CAACA,KAAK,AACtC,EAqBA,OAnBI8N,AAAgD,KAAhDA,EAAU4L,OAAO,CAAC,2BAClBxU,EAAKzF,IAAI,CAAG,OACZyF,CAAI,CAAC,eAAe,CAAG,GAGlB4I,AAA8D,KAA9DA,EAAU4L,OAAO,CAAC,yCACvBxU,CAAI,CAAC,eAAe,CAAGob,EAAalQ,OAAO,EAAI7Q,EAAQ6Q,OAAO,EAAI,EAClElL,EAAKsb,MAAM,CAAG,WAGT1S,AAAkD,KAAlDA,EAAU4L,OAAO,CAAC,4BACvBxU,EAAKzF,IAAI,CAAG,OAEPyJ,GAASoX,EAAahQ,UAAU,EAErCpL,CAAAA,EAAKzF,IAAI,CAAGO,GAAMkF,EAAKzF,IAAI,EACtBkU,QAAQ,CAAC2M,EAAahQ,UAAU,EAChC5R,GAAG,EAAC,EAENwG,CACX,CAKAub,kBAAkB1S,CAAI,CAAEmF,CAAW,CAAEH,CAAU,CAAEC,CAAK,CAAEK,CAAQ,CAAE,CAC9D,IAAqBxR,EAAN,IAAI,EAAkBA,MAAOuR,EAASvR,GAAOtC,SAAS6T,OACrE,GAAIrF,EAAM,CACN,IAAM2S,EAAYjO,GAAuB1E,EAAM,CAC3CqF,OAAQA,EACRJ,MAAOA,EACPC,kBAAmBtK,AALZ,IAAI,CAKesK,iBAAiB,CAC3CC,YAAaA,EACbC,iBAAkBJ,EAClBpK,OARO,IAAI,CASX0K,SAAUA,CACd,GAAIzK,EAAQD,AAVD,IAAI,CAUI2K,MAAM,CAACvF,EAAKpG,CAAC,CAAC,CAC7BiB,IACAA,EAAM5I,KAAK,CAAG0gB,EAAU1gB,KAAK,CAC7B4I,EAAMmK,UAAU,CAAG2N,EAAU3N,UAAU,EAE3C,IAAIpL,EAAI,GAER,IAAK,IAAM6N,KAAUzH,EAAKG,QAAQ,EAAI,EAAE,CACpCvF,AAlBO,IAAI,CAkBJ8X,iBAAiB,CAACjL,EAAOkL,EAAU1gB,KAAK,CAAE0gB,EAAU3N,UAAU,CAAE,EAAEpL,EAAGoG,EAAKG,QAAQ,CAAC/J,MAAM,CAExG,CACJ,CACAwc,gBAAiB,CACb,IAAMhY,EAAS,IAAI,CACb,CAAE2K,OAAAA,CAAM,CAAE4D,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAE,CAAGxO,EAC3B/B,EAAa+B,EAAO9G,KAAK,CAAC+E,UAAU,CAOpCga,EAAiB,AAAChY,GAAWhC,EAC/B,EACC+B,EAAO2B,YAAY,CAAC1B,EAAM,CAAC,eAAe,EAAI,EACnD,IAAK,IAAMA,KAAS0K,EAAQ,CACxB,GAAM,CAAEsH,YAAaE,CAAM,CAAExF,QAAAA,CAAO,CAAE,CAAG1M,EAAMmF,IAAI,CAEnD,GAAI+M,GAAUxF,EAAS,CACnB,GAAM,CAAE5V,OAAAA,CAAM,CAAE0C,MAAAA,CAAK,CAAE3B,EAAAA,CAAC,CAAEC,EAAAA,CAAC,CAAE,CAAGoa,EAAQ+F,EAAcD,EAAehY,GAAQkY,EAAS5J,EAAM6J,QAAQ,CAACtgB,EAAG,CAAA,GAAOugB,EAAU9J,EAAM6J,QAAQ,CAACtgB,EAAI2B,EAAO,CAAA,GAAO6e,EAAS9J,EAAM4J,QAAQ,CAACrgB,EAAG,CAAA,GAAOwgB,EAAU/J,EAAM4J,QAAQ,CAACrgB,EAAIhB,EAAQ,CAAA,GAIjOyhB,EAAKL,AAAW,IAAXA,EACDD,EAAc,EACd7K,GAAMkB,EAAM6J,QAAQ,CAACtgB,EAAG,CAAA,GAAOogB,EAAa,CAAA,GAAOO,EAAKJ,IAAY9J,EAAM8D,GAAG,CAC7E9D,EAAM8D,GAAG,CAAG6F,EAAc,EAC1B7K,GAAMkB,EAAM6J,QAAQ,CAACtgB,EAAI2B,EAAO,CAAA,GAAOye,EAAa,CAAA,GAAOQ,EAAKJ,IAAW9J,EAAM6D,GAAG,CACpF7D,EAAM6D,GAAG,CAAG6F,EAAc,EAC1B7K,GAAMmB,EAAM4J,QAAQ,CAACrgB,EAAG,CAAA,GAAOmgB,EAAa,CAAA,GAAOS,EAAKJ,AAAY,IAAZA,EACxDL,EAAc,EACd7K,GAAMmB,EAAM4J,QAAQ,CAACrgB,EAAIhB,EAAQ,CAAA,GAAOmhB,EAAa,CAAA,GAEnD7T,EAAY,CACdvM,EAAGoL,KAAK0L,GAAG,CAAC4J,EAAIC,GAChB1gB,EAAGmL,KAAK0L,GAAG,CAAC8J,EAAIC,GAChBlf,MAAOyJ,KAAK0V,GAAG,CAACH,EAAKD,GACrBzhB,OAAQmM,KAAK0V,GAAG,CAACD,EAAKD,EAC1B,CACAzY,CAAAA,EAAM4Y,KAAK,CAAGxU,EAAUvM,CAAC,CAAIuM,EAAU5K,KAAK,CAAG,EAC/CwG,EAAM6F,KAAK,CAAGzB,EAAUtM,CAAC,CAAIsM,EAAUtN,MAAM,CAAG,EAChDkJ,EAAMoE,SAAS,CAAGA,CACtB,MAGI,OAAOpE,EAAM4Y,KAAK,CAClB,OAAO5Y,EAAM6F,KAAK,AAE1B,CACJ,CAkCA6P,YAAYnV,CAAE,CAAErG,CAAM,CAAE2e,CAAc,CAAE,CAgCpCrL,GA/Be,IAAI,CA+Ba,cA/BCD,GAAqB,CAClD0G,UAAW1T,EACXuY,eAAgB/Y,AAFL,IAAI,CAEQsF,QAAQ,CAC/BnL,OAAQ4T,GAAmB5T,EAAQ,CAAA,GACnC6F,OAJW,IAAI,AAKnB,EAAG8Y,GAce,SAAUE,CAAI,EAC5B,IAAMhZ,EAASgZ,EAAKhZ,MAAM,AAE1BA,CAAAA,EAAOiZ,cAAc,CAAGD,EAAKD,cAAc,CAC3C/Y,EAAOsF,QAAQ,CAAG0T,EAAK9E,SAAS,CAEhClU,EAAOlF,OAAO,CAAG,CAAA,EACbke,EAAK7e,MAAM,EACX6F,EAAO9G,KAAK,CAACiB,MAAM,EAE3B,EAGJ,CAOAgF,SAASoB,CAAK,CAAE,CACZ,IAAI,CAAC3J,OAAO,CAACsiB,mBAAmB,CAAG,CAAA,EACnC,KAAK,CAAC/Z,SAASoB,EAAO,CAAA,GACtB,IAAI,CAAC3J,OAAO,CAACsiB,mBAAmB,CAAG,CAAA,CACvC,CACAhN,cAAcC,CAAI,CAAE,CAChB,IAAqBvV,EAAUoJ,AAAhB,IAAI,CAAmBpJ,OAAO,CAAEyV,EAASrM,AAAzC,IAAI,CAA4CsF,QAAQ,CAAgCgH,EAAWC,AAA3BvM,AAAxE,IAAI,CAA2EqF,OAAO,AAAwB,CAACgH,EAAO,CAAEhF,EAAmB,AAAmC,WAAnC,OAAOzQ,EAAQyQ,eAAe,EACpLzQ,EAAQyQ,eAAe,CAChB9B,EAAW,EAAE,CAAEtF,EAAQD,AAFnB,IAAI,CAEsB2K,MAAM,CAACwB,EAAKnN,CAAC,CAAC,CAEnDyN,EAAgB,EACpB,IAAK,IAAII,KAASV,EAAK5G,QAAQ,CAC3BsH,EAAQ7M,AANG,IAAI,CAMAkM,aAAa,CAACW,GAC7BtH,EAASjC,IAAI,CAACuJ,GACV,AAACA,EAAMgF,MAAM,EACbpF,CAAAA,GAAiBI,EAAME,GAAG,AAAD,EAIjCmB,GAAW3I,EAAU,CAAC/P,EAAG2jB,IAAO,AAAC3jB,CAAAA,EAAE4jB,SAAS,EAAI,CAAA,EAAMD,CAAAA,EAAEC,SAAS,EAAI,CAAA,GAErE,IAAIrM,EAAMgB,GAAmB9N,GAAO6T,eAAgB7T,GAAOrJ,QAAQmK,MAAO0L,GAsB1E,OArBIxM,GACAA,CAAAA,EAAMc,KAAK,CAAGgM,CAAE,EAEhB9M,GAAOuF,SAAW5O,EAAQqR,OAAO,EAAEG,iBACnC2E,CAAAA,GAAOnW,EAAQqR,OAAO,CAACG,eAAe,AAAD,EAErC+D,EAAK6K,UAAU,EAAE/W,OAAOuF,SAAWxF,AAtBxB,IAAI,CAsB2BsF,QAAQ,GAAK6G,EAAK8D,MAAM,EAClE9D,CAAAA,EAAKQ,OAAO,CAAG,CAAA,CAAI,EAEvBa,GAAqBrB,EAAM,CACvB5G,SAAUA,EACVkH,cAAeA,EAEfoF,OAAQ,CAAE9D,CAAAA,GAAmB9N,GAAO0M,QAAS,CAAA,IAAUI,EAAM,CAAC,EAC9DtH,OAAQ0G,EAAKQ,OAAO,EAAI,CAACF,EACzBjH,QAASvF,GAAOuF,QAChBkH,aAAeP,EAAKtS,KAAK,CAAIwN,CAAAA,EAAkB,EAAIiF,EAASzS,KAAK,AAAD,EAChE4M,KAAMsH,GAAmB9N,GAAOwG,KAAM,IACtC2S,UAAWrL,GAAmB9N,GAAOmZ,UAAW,CAACrM,GACjDA,IAAKA,CACT,GACOZ,CACX,CACAkN,aAAapJ,CAAM,CAAE1K,CAAQ,CAAE,CAC3B,OAAO,IAAI,CAAC2K,aAAa,CAAC,CAAA,EAAMD,EAAQ1K,EAC5C,CACA+T,WAAWrJ,CAAM,CAAE1K,CAAQ,CAAE,CACzB,OAAO,IAAI,CAAC6K,uBAAuB,CAAC,CAAA,EAAMH,EAAQ1K,EACtD,CACAgU,MAAMtJ,CAAM,CAAE1K,CAAQ,CAAE,CACpB,OAAO,IAAI,CAAC6K,uBAAuB,CAAC,CAAA,EAAOH,EAAQ1K,EACvD,CACAiU,QAAQvJ,CAAM,CAAE1K,CAAQ,CAAE,CACtB,OAAO,IAAI,CAAC2K,aAAa,CAAC,CAAA,EAAOD,EAAQ1K,EAC7C,CACAxG,UAAUoN,CAAI,CAAE,CACZ,IAAMnM,EAAS,IAAI,CAAEpJ,EAAUoJ,EAAOpJ,OAAO,CAAE6iB,EAAgB,CAACtN,EAEhEa,EAAS9C,GAA2BlK,GAASsF,EAAU2M,EAAayH,EAAY3M,EAC3EZ,GAASa,EAAO2M,UAAU,CAAC,wCAE5B,AAAC,CAAA,IAAI,CAAChP,MAAM,EAAI,EAAE,AAAD,EAAGiC,OAAO,CAAC,AAAC3M,IACrBA,EAAMuF,OAAO,EACbvF,EAAMtG,OAAO,EAErB,GAEA,KAAK,CAACoF,YAENoN,EAAOnM,EAAO4W,OAAO,IAGzB5W,EAAOmM,IAAI,CAAGA,EAAOA,GAAQnM,EAAOmM,IAAI,CACxC7G,EAAWtF,EAAOqF,OAAO,CAAC2H,EAAO,CAClB,KAAXA,GAAkB1H,IAClBtF,EAAO2V,WAAW,CAAC,GAAI,CAAA,GACvB3I,EAAShN,EAAOsF,QAAQ,CACxBA,EAAWtF,EAAOqF,OAAO,CAAC2H,EAAO,EAEjC,AAAC1H,EAASrF,KAAK,EAAEuF,SACjBxF,CAAAA,EAAOsK,iBAAiB,CAAGN,GAA8B,CACrDoB,KAAM9F,EAASzL,KAAK,CAAG,EACvBwR,OAAQzU,EAAQyU,MAAM,CACtBJ,GAAIkB,EAAKpV,MAAM,CACfmU,SAAU,CACN7D,gBAAiBrH,EAAOpJ,OAAO,CAACyQ,eAAe,CAC/Cf,aAAc1P,EAAQ0P,YAAY,AACtC,CACJ,EAAC,EAGL0C,GAAyBL,SAAS,CAAC3I,EAAOqF,OAAO,CAACrF,EAAOsF,QAAQ,CAAC,CAAE,AAACF,IACjE,IAAMhD,EAAIgD,EAAK6K,MAAM,CACjBlH,EAAO,CAAA,EAKX,OAJA3D,EAAKuH,OAAO,CAAG,CAAA,EACXvK,CAAAA,GAAKA,AAAM,KAANA,CAAO,GACZ2G,CAAAA,EAAO/I,EAAOqF,OAAO,CAACjD,EAAE,AAAD,EAEpB2G,CACX,GAEAC,GAAyBL,SAAS,CAAC3I,EAAOqF,OAAO,CAACrF,EAAOsF,QAAQ,CAAC,CAACC,QAAQ,CAAE,AAACA,IAC1E,IAAIwD,EAAO,CAAA,EACX,IAAK,IAAM8D,KAAStH,EAChBsH,EAAMF,OAAO,CAAG,CAAA,EACZE,EAAMtH,QAAQ,CAAC/J,MAAM,EACrBuN,CAAAA,EAAO,AAACA,CAAAA,GAAQ,EAAE,AAAD,EAAG6Q,MAAM,CAAC/M,EAAMtH,QAAQ,CAAA,EAGjD,OAAOwD,CACX,GACA/I,EAAOkM,aAAa,CAACC,GAErBnM,EAAO4S,SAAS,CAAI5S,EAAOuO,KAAK,CAAC8D,GAAG,CAAGrS,EAAOwO,KAAK,CAAC6D,GAAG,CACvDrS,EAAOqF,OAAO,CAAC,GAAG,CAAC4M,WAAW,CAAGA,EAAc,CAC3Cna,EAAG,EACHC,EAAG,EACH0B,MAtmCI,IAumCJ1C,OAvmCI,GAwmCR,EACAiJ,EAAOqF,OAAO,CAAC,GAAG,CAAC8M,MAAM,CAAGuH,EAAa5L,GAAoBmE,EAAa,CACtExY,MAAQwY,EAAYxY,KAAK,CAAGuG,EAAO4S,SAAS,CAC5CtQ,UAAY1L,CAAAA,CAAAA,AAAoC,aAApCA,EAAQuQ,uBAAuB,AAAc,EACzD4F,IAAKZ,EAAKY,GAAG,AACjB,GAGI,CAAA,IAAI,CAACkG,oBAAoB,EAAI,IAAI,CAACuE,oBAAoB,AAAD,GACrD,IAAI,CAACnD,cAAc,GAEvBrU,EAAOyR,sBAAsB,CAACtF,EAAMuN,GAEhC,AAAC1Z,EAAO6Z,SAAS,EAChBjjB,EAAQ0P,YAAY,EACrBtG,EAAO8X,iBAAiB,CAAC9X,EAAOmM,IAAI,EAGpCvV,EAAQ+O,mBAAmB,EAAIL,EAAS2M,WAAW,GACnDlF,EAAMzH,EAAS2M,WAAW,CAC1BjS,EAAOuO,KAAK,CAACuL,WAAW,CAAC/M,EAAIjV,CAAC,CAAEiV,EAAIjV,CAAC,CAAGiV,EAAItT,KAAK,CAAE,CAAA,GACnDuG,EAAOwO,KAAK,CAACsL,WAAW,CAAC/M,EAAIhV,CAAC,CAAEgV,EAAIhV,CAAC,CAAGgV,EAAIhW,MAAM,CAAE,CAAA,GACpDiJ,EAAOuO,KAAK,CAACwL,QAAQ,GACrB/Z,EAAOwO,KAAK,CAACuL,QAAQ,IAGzB/Z,EAAOgY,cAAc,GACjByB,GACAzZ,EAAOwQ,iBAAiB,EAEhC,CACJ,CACArB,GAAchU,cAAc,CAAG2S,GAAoBlE,GAAczO,cAAc,CAx7CX8K,IAy7CpEuH,GAAqB2B,GAAcjZ,SAAS,CAAE,CAC1C8jB,YAAatQ,GACblI,aAAcM,EAA2BZ,aAAa,CAACM,YAAY,CACnEL,SAAU,aACV8Y,YAAa,CAAA,EACbC,mBAAoB,CAAA,EACpBC,UAAWzQ,GACX0Q,aAAc,YACd/Y,eAAgB,CAAC,IAAK,IAAK,QAAS,aAAa,CACjDC,cAAe,CAAC,QAAS,aAAa,CACtCO,WA3pEuDmD,EA4pEvD8R,UA92EJ,MACInc,aAAc,CAMV,IAAI,CAAC8R,aAAa,CAAG,EACrB,IAAI,CAACE,OAAO,CAAG,CAAA,CACnB,CAMAoK,KAAKvW,CAAE,CAAExB,CAAC,CAAEuG,CAAQ,CAAExO,CAAM,CAAE8C,CAAK,CAAEmG,CAAM,CAAEiQ,CAAM,CAAE,CAQjD,OAPA,IAAI,CAACzP,EAAE,CAAGA,EACV,IAAI,CAACxB,CAAC,CAAGA,EACT,IAAI,CAACuG,QAAQ,CAAGA,EAChB,IAAI,CAACxO,MAAM,CAAGA,EACd,IAAI,CAAC8C,KAAK,CAAGA,EACb,IAAI,CAACmG,MAAM,CAAGA,EACd,IAAI,CAACiQ,MAAM,CAAGA,EACP,IAAI,AACf,CACJ,EAs1EI1O,cAAe,CAAC,QAAS,kBAAkB,CAC3C8Y,MAAOrR,EACX,GACAlH,EAA2BtH,OAAO,CAAC2U,IACnC5P,IAA0I+a,kBAAkB,CAAC,UAAWnL,IAcxK,IAAMoL,GAAK/jB,GACX+jB,CAAAA,GAAEhgB,WAAW,CAAGggB,GAAEhgB,WAAW,EAhtFiCA,EAitF9DggB,GAAEhgB,WAAW,CAACC,OAAO,CAAC+f,GAAEC,KAAK,CAAED,GAAEpf,cAAc,EAC/Csf,AAX4DtL,GAWtC3U,OAAO,CAAC+f,GAAEG,MAAM,EACT,IAAMpkB,GAAgBE,IAGzC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}