{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts Gantt JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/grid-axis\n * @requires highcharts\n *\n * GridAxis\n *\n * (c) 2016-2025 Lars A. <PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Axis\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/grid-axis\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"Axis\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/grid-axis\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Axis\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Axis\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__532__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 532:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__532__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ grid_axis_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Axis\"],\"commonjs\":[\"highcharts\",\"Axis\"],\"commonjs2\":[\"highcharts\",\"Axis\"],\"root\":[\"Highcharts\",\"Axis\"]}\nvar highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_ = __webpack_require__(532);\nvar highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default = /*#__PURE__*/__webpack_require__.n(highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_);\n;// ./code/es-modules/Core/Axis/GridAxis.js\n/* *\n *\n *  (c) 2016 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { dateFormats } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent, defined, erase, find, isArray, isNumber, merge, pick, timeUnits, wrap } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Enums\n *\n * */\n/**\n * Enum for which side the axis is on. Maps to axis.side.\n * @private\n */\nvar GridAxisSide;\n(function (GridAxisSide) {\n    GridAxisSide[GridAxisSide[\"top\"] = 0] = \"top\";\n    GridAxisSide[GridAxisSide[\"right\"] = 1] = \"right\";\n    GridAxisSide[GridAxisSide[\"bottom\"] = 2] = \"bottom\";\n    GridAxisSide[GridAxisSide[\"left\"] = 3] = \"left\";\n})(GridAxisSide || (GridAxisSide = {}));\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction argsToArray(args) {\n    return Array.prototype.slice.call(args, 1);\n}\n/**\n * @private\n */\nfunction isObject(x) {\n    // Always use strict mode\n    return highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().isObject(x, true);\n}\n/**\n * @private\n */\nfunction applyGridOptions(axis) {\n    const options = axis.options;\n    // Center-align by default\n    /*\n    if (!options.labels) {\n        options.labels = {};\n    }\n    */\n    options.labels.align = pick(options.labels.align, 'center');\n    // @todo: Check against tickLabelPlacement between/on etc\n    /* Prevents adding the last tick label if the axis is not a category\n       axis.\n       Since numeric labels are normally placed at starts and ends of a\n       range of value, and this module makes the label point at the value,\n       an \"extra\" label would appear. */\n    if (!axis.categories) {\n        options.showLastLabel = false;\n    }\n    // Prevents rotation of labels when squished, as rotating them would not\n    // help.\n    axis.labelRotation = 0;\n    options.labels.rotation = 0;\n    // Allow putting ticks closer than their data points.\n    options.minTickInterval = 1;\n}\n/**\n * Extends axis class with grid support.\n * @private\n */\nfunction compose(AxisClass, ChartClass, TickClass) {\n    if (!AxisClass.keepProps.includes('grid')) {\n        AxisClass.keepProps.push('grid');\n        AxisClass.prototype.getMaxLabelDimensions = getMaxLabelDimensions;\n        wrap(AxisClass.prototype, 'unsquish', wrapUnsquish);\n        wrap(AxisClass.prototype, 'getOffset', wrapGetOffset);\n        // Add event handlers\n        addEvent(AxisClass, 'init', onInit);\n        addEvent(AxisClass, 'afterGetTitlePosition', onAfterGetTitlePosition);\n        addEvent(AxisClass, 'afterInit', onAfterInit);\n        addEvent(AxisClass, 'afterRender', onAfterRender);\n        addEvent(AxisClass, 'afterSetAxisTranslation', onAfterSetAxisTranslation);\n        addEvent(AxisClass, 'afterSetOptions', onAfterSetOptions);\n        addEvent(AxisClass, 'afterSetOptions', onAfterSetOptions2);\n        addEvent(AxisClass, 'afterSetScale', onAfterSetScale);\n        addEvent(AxisClass, 'afterTickSize', onAfterTickSize);\n        addEvent(AxisClass, 'trimTicks', onTrimTicks);\n        addEvent(AxisClass, 'destroy', onDestroy);\n        addEvent(ChartClass, 'afterSetChartSize', onChartAfterSetChartSize);\n        addEvent(TickClass, 'afterGetLabelPosition', onTickAfterGetLabelPosition);\n        addEvent(TickClass, 'labelFormat', onTickLabelFormat);\n    }\n    return AxisClass;\n}\n/**\n * Get the largest label width and height.\n *\n * @private\n * @function Highcharts.Axis#getMaxLabelDimensions\n *\n * @param {Highcharts.Dictionary<Highcharts.Tick>} ticks\n * All the ticks on one axis.\n *\n * @param {Array<number|string>} tickPositions\n * All the tick positions on one axis.\n *\n * @return {Highcharts.SizeObject}\n * Object containing the properties height and width.\n *\n * @todo Move this to the generic axis implementation, as it is used there.\n */\nfunction getMaxLabelDimensions(ticks, tickPositions) {\n    const dimensions = {\n        width: 0,\n        height: 0\n    };\n    tickPositions.forEach(function (pos) {\n        const tick = ticks[pos];\n        let labelHeight = 0, labelWidth = 0, label;\n        if (isObject(tick)) {\n            label = isObject(tick.label) ? tick.label : {};\n            // Find width and height of label\n            labelHeight = label.getBBox ? label.getBBox().height : 0;\n            if (label.textStr && !isNumber(label.textPxLength)) {\n                label.textPxLength = label.getBBox().width;\n            }\n            labelWidth = isNumber(label.textPxLength) ?\n                // Math.round ensures crisp lines\n                Math.round(label.textPxLength) :\n                0;\n            if (label.textStr) {\n                // Set the tickWidth same as the label width after ellipsis\n                // applied #10281\n                labelWidth = Math.round(label.getBBox().width);\n            }\n            // Update the result if width and/or height are larger\n            dimensions.height = Math.max(labelHeight, dimensions.height);\n            dimensions.width = Math.max(labelWidth, dimensions.width);\n        }\n    });\n    // For tree grid, add indentation\n    if (this.type === 'treegrid' &&\n        this.treeGrid &&\n        this.treeGrid.mapOfPosToGridNode) {\n        const treeDepth = this.treeGrid.mapOfPosToGridNode[-1].height || 0;\n        dimensions.width += (this.options.labels.indentation *\n            (treeDepth - 1));\n    }\n    return dimensions;\n}\n/**\n * Handle columns and getOffset.\n * @private\n */\nfunction wrapGetOffset(proceed) {\n    const { grid } = this, \n    // On the left side we handle the columns first because the offset is\n    // calculated from the plot area and out\n    columnsFirst = this.side === 3;\n    if (!columnsFirst) {\n        proceed.apply(this);\n    }\n    if (!grid?.isColumn) {\n        let columns = grid?.columns || [];\n        if (columnsFirst) {\n            columns = columns.slice().reverse();\n        }\n        columns\n            .forEach((column) => {\n            column.getOffset();\n        });\n    }\n    if (columnsFirst) {\n        proceed.apply(this);\n    }\n}\n/**\n * @private\n */\nfunction onAfterGetTitlePosition(e) {\n    const axis = this;\n    const options = axis.options;\n    const gridOptions = options.grid || {};\n    if (gridOptions.enabled === true) {\n        // Compute anchor points for each of the title align options\n        const { axisTitle, height: axisHeight, horiz, left: axisLeft, offset, opposite, options, top: axisTop, width: axisWidth } = axis;\n        const tickSize = axis.tickSize();\n        const titleWidth = axisTitle?.getBBox().width;\n        const xOption = options.title.x;\n        const yOption = options.title.y;\n        const titleMargin = pick(options.title.margin, horiz ? 5 : 10);\n        const titleFontSize = axisTitle ? axis.chart.renderer.fontMetrics(axisTitle).f : 0;\n        const crispCorr = tickSize ? tickSize[0] / 2 : 0;\n        // TODO account for alignment\n        // the position in the perpendicular direction of the axis\n        const offAxis = ((horiz ? axisTop + axisHeight : axisLeft) +\n            (horiz ? 1 : -1) * // Horizontal axis reverses the margin\n                (opposite ? -1 : 1) * // So does opposite axes\n                crispCorr +\n            (axis.side === GridAxisSide.bottom ? titleFontSize : 0));\n        e.titlePosition.x = horiz ?\n            axisLeft - (titleWidth || 0) / 2 - titleMargin + xOption :\n            offAxis + (opposite ? axisWidth : 0) + offset + xOption;\n        e.titlePosition.y = horiz ?\n            (offAxis -\n                (opposite ? axisHeight : 0) +\n                (opposite ? titleFontSize : -titleFontSize) / 2 +\n                offset +\n                yOption) :\n            axisTop - titleMargin + yOption;\n    }\n}\n/**\n * @private\n */\nfunction onAfterInit() {\n    const axis = this;\n    const { chart, options: { grid: gridOptions = {} }, userOptions } = axis;\n    if (gridOptions.enabled) {\n        applyGridOptions(axis);\n    }\n    if (gridOptions.columns) {\n        const columns = axis.grid.columns = [];\n        let columnIndex = axis.grid.columnIndex = 0;\n        // Handle columns, each column is a grid axis\n        while (++columnIndex < gridOptions.columns.length) {\n            const columnOptions = merge(userOptions, gridOptions.columns[columnIndex], {\n                isInternal: true,\n                linkedTo: 0,\n                // Disable by default the scrollbar on the grid axis\n                scrollbar: {\n                    enabled: false\n                }\n            }, \n            // Avoid recursion\n            {\n                grid: {\n                    columns: void 0\n                }\n            });\n            const column = new (highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default())(axis.chart, columnOptions, 'yAxis');\n            column.grid.isColumn = true;\n            column.grid.columnIndex = columnIndex;\n            // Remove column axis from chart axes array, and place it\n            // in the columns array.\n            erase(chart.axes, column);\n            erase(chart[axis.coll] || [], column);\n            columns.push(column);\n        }\n    }\n}\n/**\n * Draw an extra line on the far side of the outermost axis,\n * creating floor/roof/wall of a grid. And some padding.\n * ```\n * Make this:\n *             (axis.min) __________________________ (axis.max)\n *                           |    |    |    |    |\n * Into this:\n *             (axis.min) __________________________ (axis.max)\n *                        ___|____|____|____|____|__\n * ```\n * @private\n */\nfunction onAfterRender() {\n    const axis = this, { axisTitle, grid, options } = axis, gridOptions = options.grid || {};\n    if (gridOptions.enabled === true) {\n        const min = axis.min || 0, max = axis.max || 0, firstTick = axis.ticks[axis.tickPositions[0]];\n        // Adjust the title max width to the column width (#19657)\n        if (axisTitle &&\n            !axis.chart.styledMode &&\n            firstTick?.slotWidth &&\n            !axis.options.title.style.width) {\n            axisTitle.css({ width: `${firstTick.slotWidth}px` });\n        }\n        // @todo actual label padding (top, bottom, left, right)\n        axis.maxLabelDimensions = axis.getMaxLabelDimensions(axis.ticks, axis.tickPositions);\n        // Remove right wall before rendering if updating\n        if (axis.rightWall) {\n            axis.rightWall.destroy();\n        }\n        /*\n        Draw an extra axis line on outer axes\n                    >\n        Make this:    |______|______|______|___\n\n                    > _________________________\n        Into this:    |______|______|______|__|\n                                                */\n        if (axis.grid?.isOuterAxis() && axis.axisLine) {\n            const lineWidth = options.lineWidth;\n            if (lineWidth) {\n                const linePath = axis.getLinePath(lineWidth), startPoint = linePath[0], endPoint = linePath[1], \n                // Negate distance if top or left axis\n                // Subtract 1px to draw the line at the end of the tick\n                tickLength = (axis.tickSize('tick') || [1])[0], distance = tickLength * ((axis.side === GridAxisSide.top ||\n                    axis.side === GridAxisSide.left) ? -1 : 1);\n                // If axis is horizontal, reposition line path vertically\n                if (startPoint[0] === 'M' && endPoint[0] === 'L') {\n                    if (axis.horiz) {\n                        startPoint[2] += distance;\n                        endPoint[2] += distance;\n                    }\n                    else {\n                        startPoint[1] += distance;\n                        endPoint[1] += distance;\n                    }\n                }\n                // If it doesn't exist, add an upper and lower border\n                // for the vertical grid axis.\n                if (!axis.horiz && axis.chart.marginRight) {\n                    const upperBorderStartPoint = startPoint, upperBorderEndPoint = [\n                        'L',\n                        axis.left,\n                        startPoint[2] || 0\n                    ], upperBorderPath = [\n                        upperBorderStartPoint,\n                        upperBorderEndPoint\n                    ], lowerBorderEndPoint = [\n                        'L',\n                        axis.chart.chartWidth - axis.chart.marginRight,\n                        axis.toPixels(max + axis.tickmarkOffset)\n                    ], lowerBorderStartPoint = [\n                        'M',\n                        endPoint[1] || 0,\n                        axis.toPixels(max + axis.tickmarkOffset)\n                    ], lowerBorderPath = [\n                        lowerBorderStartPoint,\n                        lowerBorderEndPoint\n                    ];\n                    if (!axis.grid.upperBorder && min % 1 !== 0) {\n                        axis.grid.upperBorder = axis.grid.renderBorder(upperBorderPath);\n                    }\n                    if (axis.grid.upperBorder) {\n                        axis.grid.upperBorder.attr({\n                            stroke: options.lineColor,\n                            'stroke-width': options.lineWidth\n                        });\n                        axis.grid.upperBorder.animate({\n                            d: upperBorderPath\n                        });\n                    }\n                    if (!axis.grid.lowerBorder && max % 1 !== 0) {\n                        axis.grid.lowerBorder = axis.grid.renderBorder(lowerBorderPath);\n                    }\n                    if (axis.grid.lowerBorder) {\n                        axis.grid.lowerBorder.attr({\n                            stroke: options.lineColor,\n                            'stroke-width': options.lineWidth\n                        });\n                        axis.grid.lowerBorder.animate({\n                            d: lowerBorderPath\n                        });\n                    }\n                }\n                // Render an extra line parallel to the existing axes, to\n                // close the grid.\n                if (!axis.grid.axisLineExtra) {\n                    axis.grid.axisLineExtra = axis.grid.renderBorder(linePath);\n                }\n                else {\n                    axis.grid.axisLineExtra.attr({\n                        stroke: options.lineColor,\n                        'stroke-width': options.lineWidth\n                    });\n                    axis.grid.axisLineExtra.animate({\n                        d: linePath\n                    });\n                }\n                // Show or hide the line depending on options.showEmpty\n                axis.axisLine[axis.showAxis ? 'show' : 'hide']();\n            }\n        }\n        (grid?.columns || []).forEach((column) => column.render());\n        // Manipulate the tick mark visibility\n        // based on the axis.max- allows smooth scrolling.\n        if (!axis.horiz &&\n            axis.chart.hasRendered &&\n            (axis.scrollbar || axis.linkedParent?.scrollbar) &&\n            axis.tickPositions.length) {\n            const tickmarkOffset = axis.tickmarkOffset, lastTick = axis.tickPositions[axis.tickPositions.length - 1], firstTick = axis.tickPositions[0];\n            let label, tickMark;\n            while ((label = axis.hiddenLabels.pop()) && label.element) {\n                label.show(); // #15453\n            }\n            while ((tickMark = axis.hiddenMarks.pop()) &&\n                tickMark.element) {\n                tickMark.show(); // #16439\n            }\n            // Hide/show first tick label.\n            label = axis.ticks[firstTick].label;\n            if (label) {\n                if (min - firstTick > tickmarkOffset) {\n                    axis.hiddenLabels.push(label.hide());\n                }\n                else {\n                    label.show();\n                }\n            }\n            // Hide/show last tick mark/label.\n            label = axis.ticks[lastTick].label;\n            if (label) {\n                if (lastTick - max > tickmarkOffset) {\n                    axis.hiddenLabels.push(label.hide());\n                }\n                else {\n                    label.show();\n                }\n            }\n            const mark = axis.ticks[lastTick].mark;\n            if (mark &&\n                lastTick - max < tickmarkOffset &&\n                lastTick - max > 0 && axis.ticks[lastTick].isLast) {\n                axis.hiddenMarks.push(mark.hide());\n            }\n        }\n    }\n}\n/**\n * @private\n */\nfunction onAfterSetAxisTranslation() {\n    const axis = this;\n    const tickInfo = axis.tickPositions?.info;\n    const options = axis.options;\n    const gridOptions = options.grid || {};\n    const userLabels = axis.userOptions.labels || {};\n    // Fire this only for the Gantt type chart, #14868.\n    if (gridOptions.enabled) {\n        if (axis.horiz) {\n            axis.series.forEach((series) => {\n                series.options.pointRange = 0;\n            });\n            // Lower level time ticks, like hours or minutes, represent\n            // points in time and not ranges. These should be aligned\n            // left in the grid cell by default. The same applies to\n            // years of higher order.\n            if (tickInfo &&\n                options.dateTimeLabelFormats &&\n                options.labels &&\n                !defined(userLabels.align) &&\n                (options.dateTimeLabelFormats[tickInfo.unitName]\n                    .range === false ||\n                    tickInfo.count > 1 // Years\n                )) {\n                options.labels.align = 'left';\n                if (!defined(userLabels.x)) {\n                    options.labels.x = 3;\n                }\n            }\n        }\n        else {\n            // Don't trim ticks which not in min/max range but\n            // they are still in the min/max plus tickInterval.\n            if (this.type !== 'treegrid' &&\n                axis.grid &&\n                axis.grid.columns) {\n                this.minPointOffset = this.tickInterval;\n            }\n        }\n    }\n}\n/**\n * Creates a left and right wall on horizontal axes:\n * - Places leftmost tick at the start of the axis, to create a left\n *   wall\n * - Ensures that the rightmost tick is at the end of the axis, to\n *   create a right wall.\n * @private\n */\nfunction onAfterSetOptions(e) {\n    const options = this.options, userOptions = e.userOptions, gridOptions = ((options && isObject(options.grid)) ? options.grid : {});\n    let gridAxisOptions;\n    if (gridOptions.enabled === true) {\n        // Merge the user options into default grid axis options so\n        // that when a user option is set, it takes precedence.\n        gridAxisOptions = merge(true, {\n            className: ('highcharts-grid-axis ' + (userOptions.className || '')),\n            dateTimeLabelFormats: {\n                hour: {\n                    list: ['%[HM]', '%[H]']\n                },\n                day: {\n                    list: ['%[AeB]', '%[aeb]', '%[E]']\n                },\n                week: {\n                    list: ['Week %W', 'W%W']\n                },\n                month: {\n                    list: ['%[B]', '%[b]', '%o']\n                }\n            },\n            grid: {\n                borderWidth: 1\n            },\n            labels: {\n                padding: 2,\n                style: {\n                    fontSize: '0.9em'\n                }\n            },\n            margin: 0,\n            title: {\n                text: null,\n                reserveSpace: false,\n                rotation: 0,\n                style: {\n                    textOverflow: 'ellipsis'\n                }\n            },\n            // In a grid axis, only allow one unit of certain types,\n            // for example we shouldn't have one grid cell spanning\n            // two days.\n            units: [[\n                    'millisecond', // Unit name\n                    [1, 10, 100]\n                ], [\n                    'second',\n                    [1, 10]\n                ], [\n                    'minute',\n                    [1, 5, 15]\n                ], [\n                    'hour',\n                    [1, 6]\n                ], [\n                    'day',\n                    [1]\n                ], [\n                    'week',\n                    [1]\n                ], [\n                    'month',\n                    [1]\n                ], [\n                    'year',\n                    null\n                ]]\n        }, userOptions);\n        // X-axis specific options\n        if (this.coll === 'xAxis') {\n            // For linked axes, tickPixelInterval is used only if\n            // the tickPositioner below doesn't run or returns\n            // undefined (like multiple years)\n            if (defined(userOptions.linkedTo) &&\n                !defined(userOptions.tickPixelInterval)) {\n                gridAxisOptions.tickPixelInterval = 350;\n            }\n            // For the secondary grid axis, use the primary axis'\n            // tick intervals and return ticks one level higher.\n            if (\n            // Check for tick pixel interval in options\n            !defined(userOptions.tickPixelInterval) &&\n                // Only for linked axes\n                defined(userOptions.linkedTo) &&\n                !defined(userOptions.tickPositioner) &&\n                !defined(userOptions.tickInterval) &&\n                !defined(userOptions.units)) {\n                gridAxisOptions.tickPositioner = function (min, max) {\n                    const parentInfo = this.linkedParent?.tickPositions?.info;\n                    if (parentInfo) {\n                        const units = (gridAxisOptions.units || []);\n                        let unitIdx, count = 1, unitName = 'year';\n                        for (let i = 0; i < units.length; i++) {\n                            const unit = units[i];\n                            if (unit && unit[0] === parentInfo.unitName) {\n                                unitIdx = i;\n                                break;\n                            }\n                        }\n                        // Get the first allowed count on the next unit.\n                        const unit = (isNumber(unitIdx) && units[unitIdx + 1]);\n                        if (unit) {\n                            unitName = unit[0] || 'year';\n                            const counts = unit[1];\n                            count = counts?.[0] || 1;\n                            // In case the base X axis shows years, make the\n                            // secondary axis show ten times the years (#11427)\n                        }\n                        else if (parentInfo.unitName === 'year') {\n                            // `unitName` is 'year'\n                            count = parentInfo.count * 10;\n                        }\n                        const unitRange = timeUnits[unitName];\n                        this.tickInterval = unitRange * count;\n                        return this.chart.time.getTimeTicks({ unitRange, count, unitName }, min, max, this.options.startOfWeek);\n                    }\n                };\n            }\n        }\n        // Now merge the combined options into the axis options\n        merge(true, this.options, gridAxisOptions);\n        if (this.horiz) {\n            /*               _________________________\n            Make this:    ___|_____|_____|_____|__|\n                            ^                     ^\n                            _________________________\n            Into this:    |_____|_____|_____|_____|\n                                ^                 ^    */\n            options.minPadding = pick(userOptions.minPadding, 0);\n            options.maxPadding = pick(userOptions.maxPadding, 0);\n        }\n        // If borderWidth is set, then use its value for tick and\n        // line width.\n        if (isNumber(options.grid.borderWidth)) {\n            options.tickWidth = options.lineWidth =\n                gridOptions.borderWidth;\n        }\n    }\n}\n/**\n * @private\n */\nfunction onAfterSetOptions2(e) {\n    const axis = this;\n    const userOptions = e.userOptions;\n    const gridOptions = userOptions?.grid || {};\n    const columns = gridOptions.columns;\n    // Add column options to the parent axis. Children has their column options\n    // set on init in onGridAxisAfterInit.\n    if (gridOptions.enabled && columns) {\n        merge(true, axis.options, columns[0]);\n    }\n}\n/**\n * Handle columns and setScale.\n * @private\n */\nfunction onAfterSetScale() {\n    const axis = this;\n    (axis.grid.columns || []).forEach((column) => column.setScale());\n}\n/**\n * Draw vertical axis ticks extra long to create cell floors and roofs.\n * Overrides the tickLength for vertical axes.\n * @private\n */\nfunction onAfterTickSize(e) {\n    const { horiz, maxLabelDimensions, options: { grid: gridOptions = {} } } = this;\n    if (gridOptions.enabled && maxLabelDimensions) {\n        const labelPadding = this.options.labels.distance * 2;\n        const distance = horiz ?\n            (gridOptions.cellHeight ||\n                labelPadding + maxLabelDimensions.height) :\n            labelPadding + maxLabelDimensions.width;\n        if (isArray(e.tickSize)) {\n            e.tickSize[0] = distance;\n        }\n        else {\n            e.tickSize = [distance, 0];\n        }\n    }\n}\n/**\n * @private\n */\nfunction onChartAfterSetChartSize() {\n    this.axes.forEach((axis) => {\n        (axis.grid?.columns || []).forEach((column) => {\n            column.setAxisSize();\n            column.setAxisTranslation();\n        });\n    });\n}\n/**\n * @private\n */\nfunction onDestroy(e) {\n    const { grid } = this;\n    (grid.columns || []).forEach((column) => column.destroy(e.keepEvents));\n    grid.columns = void 0;\n}\n/**\n * Wraps axis init to draw cell walls on vertical axes.\n * @private\n */\nfunction onInit(e) {\n    const axis = this;\n    const userOptions = e.userOptions || {};\n    const gridOptions = userOptions.grid || {};\n    if (gridOptions.enabled && defined(gridOptions.borderColor)) {\n        userOptions.tickColor = userOptions.lineColor = (gridOptions.borderColor);\n    }\n    if (!axis.grid) {\n        axis.grid = new GridAxisAdditions(axis);\n    }\n    axis.hiddenLabels = [];\n    axis.hiddenMarks = [];\n}\n/**\n * Center tick labels in cells.\n * @private\n */\nfunction onTickAfterGetLabelPosition(e) {\n    const tick = this, label = tick.label, axis = tick.axis, reversed = axis.reversed, chart = axis.chart, options = axis.options, gridOptions = options.grid || {}, labelOpts = axis.options.labels, align = labelOpts.align, \n    // `verticalAlign` is currently not supported for axis.labels.\n    verticalAlign = 'middle', // LabelOpts.verticalAlign,\n    side = GridAxisSide[axis.side], tickmarkOffset = e.tickmarkOffset, tickPositions = axis.tickPositions, tickPos = tick.pos - tickmarkOffset, nextTickPos = (isNumber(tickPositions[e.index + 1]) ?\n        tickPositions[e.index + 1] - tickmarkOffset :\n        (axis.max || 0) + tickmarkOffset), tickSize = axis.tickSize('tick'), tickWidth = tickSize ? tickSize[0] : 0, crispCorr = tickSize ? tickSize[1] / 2 : 0;\n    // Only center tick labels in grid axes\n    if (gridOptions.enabled === true) {\n        let bottom, top, left, right;\n        // Calculate top and bottom positions of the cell.\n        if (side === 'top') {\n            bottom = axis.top + axis.offset;\n            top = bottom - tickWidth;\n        }\n        else if (side === 'bottom') {\n            top = chart.chartHeight - axis.bottom + axis.offset;\n            bottom = top + tickWidth;\n        }\n        else {\n            bottom = axis.top + axis.len - (axis.translate(reversed ? nextTickPos : tickPos) || 0);\n            top = axis.top + axis.len - (axis.translate(reversed ? tickPos : nextTickPos) || 0);\n        }\n        // Calculate left and right positions of the cell.\n        if (side === 'right') {\n            left = chart.chartWidth - axis.right + axis.offset;\n            right = left + tickWidth;\n        }\n        else if (side === 'left') {\n            right = axis.left + axis.offset;\n            left = right - tickWidth;\n        }\n        else {\n            left = Math.round(axis.left + (axis.translate(reversed ? nextTickPos : tickPos) || 0)) - crispCorr;\n            right = Math.min(// #15742\n            Math.round(axis.left + (axis.translate(reversed ? tickPos : nextTickPos) || 0)) - crispCorr, axis.left + axis.len);\n        }\n        tick.slotWidth = right - left;\n        // Calculate the positioning of the label based on\n        // alignment.\n        e.pos.x = (align === 'left' ?\n            left :\n            align === 'right' ?\n                right :\n                left + ((right - left) / 2) // Default to center\n        );\n        e.pos.y = (verticalAlign === 'top' ?\n            top :\n            verticalAlign === 'bottom' ?\n                bottom :\n                top + ((bottom - top) / 2) // Default to middle\n        );\n        if (label) {\n            const lblMetrics = chart.renderer.fontMetrics(label), labelHeight = label.getBBox().height;\n            // Adjustment to y position to align the label correctly.\n            // Would be better to have a setter or similar for this.\n            if (!labelOpts.useHTML) {\n                const lines = Math.round(labelHeight / lblMetrics.h);\n                e.pos.y += (\n                // Center the label\n                // TODO: why does this actually center the label?\n                ((lblMetrics.b - (lblMetrics.h - lblMetrics.f)) / 2) +\n                    // Adjust for height of additional lines.\n                    -(((lines - 1) * lblMetrics.h) / 2));\n            }\n            else {\n                e.pos.y += (\n                // Readjust yCorr in htmlUpdateTransform\n                lblMetrics.b +\n                    // Adjust for height of html label\n                    -(labelHeight / 2));\n            }\n        }\n        e.pos.x += (axis.horiz && labelOpts.x) || 0;\n    }\n}\n/**\n * @private\n */\nfunction onTickLabelFormat(ctx) {\n    const { axis, value } = ctx;\n    if (axis.options.grid?.enabled) {\n        const tickPos = axis.tickPositions;\n        const series = (axis.linkedParent || axis).series[0];\n        const isFirst = value === tickPos[0];\n        const isLast = value === tickPos[tickPos.length - 1];\n        const point = series && find(series.options.data, function (p) {\n            return p[axis.isXAxis ? 'x' : 'y'] === value;\n        });\n        let pointCopy;\n        if (point && series.is('gantt')) {\n            // For the Gantt set point aliases to the pointCopy\n            // to do not change the original point\n            pointCopy = merge(point);\n            highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().seriesTypes.gantt.prototype.pointClass\n                .setGanttPointAliases(pointCopy, axis.chart);\n        }\n        // Make additional properties available for the\n        // formatter\n        ctx.isFirst = isFirst;\n        ctx.isLast = isLast;\n        ctx.point = pointCopy;\n    }\n}\n/**\n * Makes tick labels which are usually ignored in a linked axis\n * displayed if they are within range of linkedParent.min.\n * ```\n *                        _____________________________\n *                        |   |       |       |       |\n * Make this:             |   |   2   |   3   |   4   |\n *                        |___|_______|_______|_______|\n *                          ^\n *                        _____________________________\n *                        |   |       |       |       |\n * Into this:             | 1 |   2   |   3   |   4   |\n *                        |___|_______|_______|_______|\n *                          ^\n * ```\n * @private\n * @todo Does this function do what the drawing says? Seems to affect\n *       ticks and not the labels directly?\n */\nfunction onTrimTicks() {\n    const axis = this, options = axis.options, gridOptions = options.grid || {}, categoryAxis = axis.categories, tickPositions = axis.tickPositions, firstPos = tickPositions[0], secondPos = tickPositions[1], lastPos = tickPositions[tickPositions.length - 1], beforeLastPos = tickPositions[tickPositions.length - 2], linkedMin = axis.linkedParent?.min, linkedMax = axis.linkedParent?.max, min = linkedMin || axis.min, max = linkedMax || axis.max, tickInterval = axis.tickInterval, startLessThanMin = ( // #19845\n    isNumber(min) &&\n        min >= firstPos + tickInterval &&\n        min < secondPos), endMoreThanMin = (isNumber(min) &&\n        firstPos < min &&\n        firstPos + tickInterval > min), startLessThanMax = (isNumber(max) &&\n        lastPos > max &&\n        lastPos - tickInterval < max), endMoreThanMax = (isNumber(max) &&\n        max <= lastPos - tickInterval &&\n        max > beforeLastPos);\n    if (gridOptions.enabled === true &&\n        !categoryAxis &&\n        (axis.isXAxis || axis.isLinked)) {\n        if ((endMoreThanMin || startLessThanMin) && !options.startOnTick) {\n            tickPositions[0] = min;\n        }\n        if ((startLessThanMax || endMoreThanMax) && !options.endOnTick) {\n            tickPositions[tickPositions.length - 1] = max;\n        }\n    }\n}\n/**\n * Avoid altering tickInterval when reserving space.\n * @private\n */\nfunction wrapUnsquish(proceed) {\n    const axis = this;\n    const { options: { grid: gridOptions = {} } } = axis;\n    if (gridOptions.enabled === true && axis.categories) {\n        return axis.tickInterval;\n    }\n    return proceed.apply(axis, argsToArray(arguments));\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * Additions for grid axes.\n * @private\n * @class\n */\nclass GridAxisAdditions {\n    /* *\n    *\n    *  Constructors\n    *\n    * */\n    constructor(axis) {\n        this.axis = axis;\n    }\n    /* *\n    *\n    *  Functions\n    *\n    * */\n    /**\n     * Checks if an axis is the outer axis in its dimension. Since\n     * axes are placed outwards in order, the axis with the highest\n     * index is the outermost axis.\n     *\n     * Example: If there are multiple x-axes at the top of the chart,\n     * this function returns true if the axis supplied is the last\n     * of the x-axes.\n     *\n     * @private\n     *\n     * @return {boolean}\n     * True if the axis is the outermost axis in its dimension; false if\n     * not.\n     */\n    isOuterAxis() {\n        const axis = this.axis;\n        const chart = axis.chart;\n        const columnIndex = axis.grid.columnIndex;\n        const columns = (axis.linkedParent?.grid.columns ||\n            axis.grid.columns ||\n            []);\n        const parentAxis = columnIndex ? axis.linkedParent : axis;\n        let thisIndex = -1, lastIndex = 0;\n        // On the left side, when we have columns (not only multiple axes), the\n        // main axis is to the left\n        if (axis.side === 3 && !chart.inverted && columns.length) {\n            return !axis.linkedParent;\n        }\n        (chart[axis.coll] || []).forEach((otherAxis, index) => {\n            if (otherAxis.side === axis.side &&\n                !otherAxis.options.isInternal) {\n                lastIndex = index;\n                if (otherAxis === parentAxis) {\n                    // Get the index of the axis in question\n                    thisIndex = index;\n                }\n            }\n        });\n        return (lastIndex === thisIndex &&\n            (isNumber(columnIndex) ?\n                columns.length === columnIndex :\n                true));\n    }\n    /**\n     * Add extra border based on the provided path.\n     * @private\n     * @param {SVGPath} path\n     * The path of the border.\n     * @return {Highcharts.SVGElement}\n     * Border\n     */\n    renderBorder(path) {\n        const axis = this.axis, renderer = axis.chart.renderer, options = axis.options, extraBorderLine = renderer.path(path)\n            .addClass('highcharts-axis-line')\n            .add(axis.axisGroup);\n        if (!renderer.styledMode) {\n            extraBorderLine.attr({\n                stroke: options.lineColor,\n                'stroke-width': options.lineWidth,\n                zIndex: 7\n            });\n        }\n        return extraBorderLine;\n    }\n}\n/* *\n *\n *  Registry\n *\n * */\n// First letter of the day of the week, e.g. 'M' for 'Monday'.\ndateFormats.E = function (timestamp) {\n    return this.dateFormat('%a', timestamp, true).charAt(0);\n};\n// Adds week date format\ndateFormats.W = function (timestamp) {\n    const d = this.toParts(timestamp), firstDay = (d[7] + 6) % 7, thursday = d.slice(0);\n    thursday[2] = d[2] - firstDay + 3;\n    const firstThursday = this.toParts(this.makeTime(thursday[0], 0, 1));\n    if (firstThursday[7] !== 4) {\n        d[1] = 0; // Set month to January\n        d[2] = 1 + (11 - firstThursday[7]) % 7;\n    }\n    const thursdayTS = this.makeTime(thursday[0], thursday[1], thursday[2]), firstThursdayTS = this.makeTime(firstThursday[0], firstThursday[1], firstThursday[2]);\n    return (1 +\n        Math.floor((thursdayTS - firstThursdayTS) / 604800000)).toString();\n};\n/* *\n *\n *  Default Export\n *\n * */\nconst GridAxis = {\n    compose\n};\n/* harmony default export */ const Axis_GridAxis = (GridAxis);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * @productdesc {gantt}\n * For grid axes (like in Gantt charts),\n * it is possible to declare as a list to provide different\n * formats depending on available space.\n *\n * Defaults to:\n * ```js\n * {\n *     hour: { list: ['%H:%M', '%H'] },\n *     day: { list: ['%A, %e. %B', '%a, %e. %b', '%E'] },\n *     week: { list: ['Week %W', 'W%W'] },\n *     month: { list: ['%B', '%b', '%o'] }\n * }\n * ```\n *\n * @sample {gantt} gantt/grid-axis/date-time-label-formats\n *         Gantt chart with custom axis date format.\n *\n * @apioption xAxis.dateTimeLabelFormats\n */\n/**\n * Set grid options for the axis labels. Requires Highcharts Gantt.\n *\n * @since     6.2.0\n * @product   gantt\n * @apioption xAxis.grid\n */\n/**\n * Enable grid on the axis labels. Defaults to true for Gantt charts.\n *\n * @type      {boolean}\n * @default   true\n * @since     6.2.0\n * @product   gantt\n * @apioption xAxis.grid.enabled\n */\n/**\n * Set specific options for each column (or row for horizontal axes) in the\n * grid. Each extra column/row is its own axis, and the axis options can be set\n * here.\n *\n * @sample gantt/demo/left-axis-table\n *         Left axis as a table\n * @sample gantt/demo/treegrid-columns\n *         Collapsible tree grid with columns\n *\n * @type      {Array<Highcharts.XAxisOptions>}\n * @apioption xAxis.grid.columns\n */\n/**\n * Set border color for the label grid lines.\n *\n * @type      {Highcharts.ColorString}\n * @default   #e6e6e6\n * @apioption xAxis.grid.borderColor\n */\n/**\n * Set border width of the label grid lines.\n *\n * @type      {number}\n * @default   1\n * @apioption xAxis.grid.borderWidth\n */\n/**\n * Set cell height for grid axis labels. By default this is calculated from font\n * size. This option only applies to horizontal axes. For vertical axes, check\n * the [#yAxis.staticScale](yAxis.staticScale) option.\n *\n * @sample gantt/grid-axis/cellheight\n *         Gant chart with custom cell height\n * @type      {number}\n * @apioption xAxis.grid.cellHeight\n */\n''; // Keeps doclets above in JS file\n\n;// ./code/es-modules/masters/modules/grid-axis.js\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n// Compositions\nAxis_GridAxis.compose(G.Axis, G.Chart, G.Tick);\n/* harmony default export */ const grid_axis_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__532__", "GridAxisSide", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "grid_axis_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_", "highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default", "dateFormats", "addEvent", "defined", "erase", "find", "isArray", "isNumber", "merge", "pick", "timeUnits", "wrap", "isObject", "x", "getMaxLabelDimensions", "ticks", "tickPositions", "dimensions", "width", "height", "for<PERSON>ach", "pos", "tick", "labelHeight", "labelWidth", "label", "getBBox", "textStr", "textPxLength", "Math", "round", "max", "type", "treeGrid", "mapOfPosToGridNode", "<PERSON><PERSON><PERSON><PERSON>", "options", "labels", "indentation", "wrapGetOffset", "proceed", "grid", "columnsFirst", "side", "apply", "isColumn", "columns", "slice", "reverse", "column", "getOffset", "onAfterGetTitlePosition", "e", "gridOptions", "axis", "enabled", "axisTitle", "axisHeight", "horiz", "left", "axisLeft", "offset", "opposite", "top", "axisTop", "axisWidth", "tickSize", "titleWidth", "xOption", "title", "yOption", "y", "<PERSON><PERSON><PERSON><PERSON>", "margin", "titleFontSize", "chart", "renderer", "fontMetrics", "f", "offAxis", "bottom", "titlePosition", "onAfterInit", "userOptions", "applyGridOptions", "align", "categories", "showLastLabel", "labelRotation", "rotation", "minTickInterval", "columnIndex", "length", "columnOptions", "isInternal", "linkedTo", "scrollbar", "axes", "coll", "push", "onAfterRender", "min", "firstTick", "styledMode", "slotWidth", "style", "css", "maxLabelDimensions", "rightWall", "destroy", "isOuterAxis", "axisLine", "lineWidth", "linePath", "get<PERSON>inePath", "startPoint", "endPoint", "distance", "tick<PERSON><PERSON>th", "marginRight", "upperBorderEndPoint", "upperBorderPath", "lowerBorderEndPoint", "chartWidth", "toPixels", "tickmarkOffset", "lowerBorderPath", "upperBorder", "renderBorder", "attr", "stroke", "lineColor", "animate", "lowerBorder", "axisLineExtra", "showAxis", "render", "hasRendered", "linkedParent", "tickMark", "lastTick", "hiddenLabels", "pop", "element", "show", "hiddenMarks", "hide", "mark", "isLast", "onAfterSetAxisTranslation", "tickInfo", "info", "userLabels", "series", "pointRange", "dateTimeLabelFormats", "unitName", "range", "count", "minPointOffset", "tickInterval", "onAfterSetOptions", "gridAxisOptions", "className", "hour", "list", "day", "week", "month", "borderWidth", "padding", "fontSize", "text", "reserveSpace", "textOverflow", "units", "tickPixelInterval", "tickPositioner", "parentInfo", "unitIdx", "i", "unit", "counts", "unitRange", "time", "getTimeTicks", "startOfWeek", "minPadding", "maxPadding", "tickWidth", "onAfterSetOptions2", "onAfterSetScale", "setScale", "onAfterTickSize", "labelPadding", "cellHeight", "onChartAfterSetChartSize", "setAxisSize", "setAxisTranslation", "onDestroy", "keepEvents", "onInit", "borderColor", "tickColor", "GridAxisAdditions", "onTickAfterGetLabelPosition", "reversed", "labelOpts", "tickPos", "nextTickPos", "index", "crispCorr", "right", "chartHeight", "len", "translate", "lblMetrics", "useHTML", "b", "lines", "h", "onTickLabelFormat", "ctx", "value", "pointCopy", "<PERSON><PERSON><PERSON><PERSON>", "point", "data", "p", "isXAxis", "is", "seriesTypes", "gantt", "pointClass", "setGanttPointAliases", "onTrimTicks", "categoryAxis", "firstPos", "secondPos", "lastPos", "beforeLastPos", "linkedMin", "linkedMax", "startLessThanMin", "endMoreThanMin", "startLessThanMax", "endMoreThanMax", "isLinked", "startOnTick", "endOnTick", "wrapUnsquish", "args", "arguments", "Array", "constructor", "parentAxis", "thisIndex", "lastIndex", "inverted", "otherAxis", "path", "extraBorderLine", "addClass", "add", "axisGroup", "zIndex", "E", "timestamp", "dateFormat", "char<PERSON>t", "W", "toParts", "firstDay", "thursday", "firstThursday", "makeTime", "floor", "thursdayTS", "toString", "G", "Axis_GridAxis", "compose", "AxisClass", "ChartClass", "TickClass", "keepProps", "includes", "Axis", "Chart", "Tick"], "mappings": "CAWA,AAXA;;;;;;;;;;CAUC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,IAAO,EAClE,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,+BAAgC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,IAAO,CAAE,GAC9G,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,+BAA+B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,IAAO,EAElGA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,IAAO,CAC7E,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAgHNC,EAhHUC,EAAuB,CAE/B,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAab,OAAO,CAG5B,IAAIC,EAASS,CAAwB,CAACE,EAAS,CAAG,CAGjDZ,QAAS,CAAC,CACX,EAMA,OAHAS,CAAmB,CAACG,EAAS,CAACX,EAAQA,EAAOD,OAAO,CAAEW,GAG/CV,EAAOD,OAAO,AACtB,CAMCW,EAAoBI,CAAC,CAAG,AAACd,IACxB,IAAIe,EAASf,GAAUA,EAAOgB,UAAU,CACvC,IAAOhB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAU,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAAClB,EAASoB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACtB,EAASqB,IAC5EE,OAAOC,cAAc,CAACxB,EAASqB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAA2FzB,EAAoB,KAC/G0B,EAA+G1B,EAAoBI,CAAC,CAACqB,GAezI,GAAM,CAAEE,YAAAA,CAAW,CAAE,CAAIH,IAEnB,CAAEI,SAAAA,CAAQ,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAEC,QAAAA,CAAO,CAAEC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAEC,UAAAA,CAAS,CAAEC,KAAAA,CAAI,CAAE,CAAIb,IA+B7F,SAASc,EAASC,CAAC,EAEf,OAAOf,IAA8Ec,QAAQ,CAACC,EAAG,CAAA,EACrG,CA0EA,SAASC,EAAsBC,CAAK,CAAEC,CAAa,EAC/C,IAAMC,EAAa,CACfC,MAAO,EACPC,OAAQ,CACZ,EA0BA,GAzBAH,EAAcI,OAAO,CAAC,SAAUC,CAAG,EAC/B,IAAMC,EAAOP,CAAK,CAACM,EAAI,CACnBE,EAAc,EAAGC,EAAa,EAAGC,EACjCb,EAASU,KAGTC,EAAcE,AAFdA,CAAAA,EAAQb,EAASU,EAAKG,KAAK,EAAIH,EAAKG,KAAK,CAAG,CAAC,CAAA,EAEzBC,OAAO,CAAGD,EAAMC,OAAO,GAAGP,MAAM,CAAG,EACnDM,EAAME,OAAO,EAAI,CAACpB,EAASkB,EAAMG,YAAY,GAC7CH,CAAAA,EAAMG,YAAY,CAAGH,EAAMC,OAAO,GAAGR,KAAK,AAAD,EAE7CM,EAAajB,EAASkB,EAAMG,YAAY,EAEpCC,KAAKC,KAAK,CAACL,EAAMG,YAAY,EAC7B,EACAH,EAAME,OAAO,EAGbH,CAAAA,EAAaK,KAAKC,KAAK,CAACL,EAAMC,OAAO,GAAGR,KAAK,CAAA,EAGjDD,EAAWE,MAAM,CAAGU,KAAKE,GAAG,CAACR,EAAaN,EAAWE,MAAM,EAC3DF,EAAWC,KAAK,CAAGW,KAAKE,GAAG,CAACP,EAAYP,EAAWC,KAAK,EAEhE,GAEI,AAAc,aAAd,IAAI,CAACc,IAAI,EACT,IAAI,CAACC,QAAQ,EACb,IAAI,CAACA,QAAQ,CAACC,kBAAkB,CAAE,CAClC,IAAMC,EAAY,IAAI,CAACF,QAAQ,CAACC,kBAAkB,CAAC,GAAG,CAACf,MAAM,EAAI,CACjEF,CAAAA,EAAWC,KAAK,EAAK,IAAI,CAACkB,OAAO,CAACC,MAAM,CAACC,WAAW,CAC/CH,CAAAA,EAAY,CAAA,CACrB,CACA,OAAOlB,CACX,CAKA,SAASsB,EAAcC,CAAO,EAC1B,GAAM,CAAEC,KAAAA,CAAI,CAAE,CAAG,IAAI,CAGrBC,EAAe,AAAc,IAAd,IAAI,CAACC,IAAI,CAIxB,GAHI,AAACD,GACDF,EAAQI,KAAK,CAAC,IAAI,EAElB,CAACH,GAAMI,SAAU,CACjB,IAAIC,EAAUL,GAAMK,SAAW,EAAE,AAC7BJ,CAAAA,GACAI,CAAAA,EAAUA,EAAQC,KAAK,GAAGC,OAAO,EAAC,EAEtCF,EACK1B,OAAO,CAAC,AAAC6B,IACVA,EAAOC,SAAS,EACpB,EACJ,CACIR,GACAF,EAAQI,KAAK,CAAC,IAAI,CAE1B,CAIA,SAASO,EAAwBC,CAAC,EAI9B,GAAIC,AAAwB,CAAA,IAAxBA,AADgBjB,CAAAA,AADJkB,AADH,IAAI,CACIlB,OAAO,CACAK,IAAI,EAAI,CAAC,CAAA,EACrBc,OAAO,CAAW,CAE9B,GAAM,CAAEC,UAAAA,CAAS,CAAErC,OAAQsC,CAAU,CAAEC,MAAAA,CAAK,CAAEC,KAAMC,CAAQ,CAAEC,OAAAA,CAAM,CAAEC,SAAAA,CAAQ,CAAE1B,QAAAA,CAAO,CAAE2B,IAAKC,CAAO,CAAE9C,MAAO+C,CAAS,CAAE,CALhH,IAAI,CAMPC,EAAWZ,AANR,IAAI,CAMSY,QAAQ,GACxBC,EAAaX,GAAW9B,UAAUR,MAClCkD,EAAUhC,EAAQiC,KAAK,CAACxD,CAAC,CACzByD,EAAUlC,EAAQiC,KAAK,CAACE,CAAC,CACzBC,EAAc/D,EAAK2B,EAAQiC,KAAK,CAACI,MAAM,CAAEf,EAAQ,EAAI,IACrDgB,EAAgBlB,EAAYF,AAXzB,IAAI,CAW0BqB,KAAK,CAACC,QAAQ,CAACC,WAAW,CAACrB,GAAWsB,CAAC,CAAG,EAI3EC,EAAW,AAACrB,CAAAA,EAAQM,EAAUP,EAAaG,CAAO,EACpD,AAACF,CAAAA,EAAQ,EAAI,EAAC,EACTI,CAAAA,EAAW,GAAK,CAAA,EALPI,CAAAA,EAAWA,CAAQ,CAAC,EAAE,CAAG,EAAI,CAAA,EAO1CZ,CAAAA,AAnBI,IAAI,CAmBHX,IAAI,GAAKxE,EAAa6G,MAAM,CAAGN,EAAgB,CAAA,CACzDtB,CAAAA,EAAE6B,aAAa,CAACpE,CAAC,CAAG6C,EAChBE,EAAW,AAACO,CAAAA,GAAc,CAAA,EAAK,EAAIK,EAAcJ,EACjDW,EAAWjB,CAAAA,EAAWG,EAAY,CAAA,EAAKJ,EAASO,EACpDhB,EAAE6B,aAAa,CAACV,CAAC,CAAGb,EACfqB,EACIjB,CAAAA,EAAWL,EAAa,CAAA,EACzB,AAACK,CAAAA,EAAWY,EAAgB,CAACA,CAAY,EAAK,EAC9Cb,EACAS,EACJN,EAAUQ,EAAcF,CAChC,CACJ,CAIA,SAASY,IAEL,GAAM,CAAEP,MAAAA,CAAK,CAAEvC,QAAS,CAAEK,KAAMY,EAAc,CAAC,CAAC,CAAE,CAAE8B,YAAAA,CAAW,CAAE,CADpD,IAAI,CAKjB,GAHI9B,EAAYE,OAAO,EACnB6B,AAlLR,SAA0B9B,CAAI,EAC1B,IAAMlB,EAAUkB,EAAKlB,OAAO,AAO5BA,CAAAA,EAAQC,MAAM,CAACgD,KAAK,CAAG5E,EAAK2B,EAAQC,MAAM,CAACgD,KAAK,CAAE,UAO9C,AAAC/B,EAAKgC,UAAU,EAChBlD,CAAAA,EAAQmD,aAAa,CAAG,CAAA,CAAI,EAIhCjC,EAAKkC,aAAa,CAAG,EACrBpD,EAAQC,MAAM,CAACoD,QAAQ,CAAG,EAE1BrD,EAAQsD,eAAe,CAAG,CAC9B,EAuJiB,IAAI,EAKbrC,EAAYP,OAAO,CAAE,CACrB,IAAMA,EAAUQ,AANP,IAAI,CAMQb,IAAI,CAACK,OAAO,CAAG,EAAE,CAClC6C,EAAcrC,AAPT,IAAI,CAOUb,IAAI,CAACkD,WAAW,CAAG,EAE1C,KAAO,EAAEA,EAActC,EAAYP,OAAO,CAAC8C,MAAM,EAAE,CAC/C,IAAMC,EAAgBrF,EAAM2E,EAAa9B,EAAYP,OAAO,CAAC6C,EAAY,CAAE,CACvEG,WAAY,CAAA,EACZC,SAAU,EAEVC,UAAW,CACPzC,QAAS,CAAA,CACb,CACJ,EAEA,CACId,KAAM,CACFK,QAAS,KAAK,CAClB,CACJ,GACMG,EAAS,GAAKjD,CAAAA,GAAgG,EAAGsD,AAxBlH,IAAI,CAwBmHqB,KAAK,CAAEkB,EAAe,QAClJ5C,CAAAA,EAAOR,IAAI,CAACI,QAAQ,CAAG,CAAA,EACvBI,EAAOR,IAAI,CAACkD,WAAW,CAAGA,EAG1BvF,EAAMuE,EAAMsB,IAAI,CAAEhD,GAClB7C,EAAMuE,CAAK,CAACrB,AA9BP,IAAI,CA8BQ4C,IAAI,CAAC,EAAI,EAAE,CAAEjD,GAC9BH,EAAQqD,IAAI,CAAClD,EACjB,CACJ,CACJ,CAcA,SAASmD,IACL,GAAmB,CAAE5C,UAAAA,CAAS,CAAEf,KAAAA,CAAI,CAAEL,QAAAA,CAAO,CAAE,CAAlC,IAAI,CACjB,GAAIiB,AAAwB,CAAA,IAAxBA,AADkEjB,CAAAA,EAAQK,IAAI,EAAI,CAAC,CAAA,EACvEc,OAAO,CAAW,CAC9B,IAAM8C,EAAM/C,AAFH,IAAI,CAEI+C,GAAG,EAAI,EAAGtE,EAAMuB,AAFxB,IAAI,CAEyBvB,GAAG,EAAI,EAAGuE,EAAYhD,AAFnD,IAAI,CAEoDvC,KAAK,CAACuC,AAF9D,IAAI,CAE+DtC,aAAa,CAAC,EAAE,CAAC,CAsB7F,GApBIwC,GACA,CAACF,AALI,IAAI,CAKHqB,KAAK,CAAC4B,UAAU,EACtBD,GAAWE,WACX,CAAClD,AAPI,IAAI,CAOHlB,OAAO,CAACiC,KAAK,CAACoC,KAAK,CAACvF,KAAK,EAC/BsC,EAAUkD,GAAG,CAAC,CAAExF,MAAO,CAAC,EAAEoF,EAAUE,SAAS,CAAC,EAAE,CAAC,AAAC,GAGtDlD,AAXS,IAAI,CAWRqD,kBAAkB,CAAGrD,AAXjB,IAAI,CAWkBxC,qBAAqB,CAACwC,AAX5C,IAAI,CAW6CvC,KAAK,CAAEuC,AAXxD,IAAI,CAWyDtC,aAAa,EAE/EsC,AAbK,IAAI,CAaJsD,SAAS,EACdtD,AAdK,IAAI,CAcJsD,SAAS,CAACC,OAAO,GAUtBvD,AAxBK,IAAI,CAwBJb,IAAI,EAAEqE,eAAiBxD,AAxBvB,IAAI,CAwBwByD,QAAQ,CAAE,CAC3C,IAAMC,EAAY5E,EAAQ4E,SAAS,CACnC,GAAIA,EAAW,CACX,IAAMC,EAAW3D,AA3BhB,IAAI,CA2BiB4D,WAAW,CAACF,GAAYG,EAAaF,CAAQ,CAAC,EAAE,CAAEG,EAAWH,CAAQ,CAAC,EAAE,CAG9CI,EAAWC,AAA9C,AAAChE,CAAAA,AA9Bb,IAAI,CA8BcY,QAAQ,CAAC,SAAW,CAAC,EAAE,AAAD,CAAE,CAAC,EAAE,CAA2B,CAAA,AAACZ,AA9BzE,IAAI,CA8B0EX,IAAI,GAAKxE,EAAa4F,GAAG,EACpGT,AA/BH,IAAI,CA+BIX,IAAI,GAAKxE,EAAawF,IAAI,CAAI,GAAK,CAAA,EAc5C,GAZsB,MAAlBwD,CAAU,CAAC,EAAE,EAAYC,AAAgB,MAAhBA,CAAQ,CAAC,EAAE,GAChC9D,AAlCP,IAAI,CAkCQI,KAAK,EACVyD,CAAU,CAAC,EAAE,EAAIE,EACjBD,CAAQ,CAAC,EAAE,EAAIC,IAGfF,CAAU,CAAC,EAAE,EAAIE,EACjBD,CAAQ,CAAC,EAAE,EAAIC,IAKnB,CAAC/D,AA7CJ,IAAI,CA6CKI,KAAK,EAAIJ,AA7ClB,IAAI,CA6CmBqB,KAAK,CAAC4C,WAAW,CAAE,CACvC,IAA0CC,EAAsB,CAC5D,IACAlE,AAhDP,IAAI,CAgDQK,IAAI,CACTwD,CAAU,CAAC,EAAE,EAAI,EACpB,CAAEM,EAAkB,CAJSN,EAM1BK,EACH,CAAEE,EAAsB,CACrB,IACApE,AAvDP,IAAI,CAuDQqB,KAAK,CAACgD,UAAU,CAAGrE,AAvD/B,IAAI,CAuDgCqB,KAAK,CAAC4C,WAAW,CAC9CjE,AAxDP,IAAI,CAwDQsE,QAAQ,CAAC7F,EAAMuB,AAxD3B,IAAI,CAwD4BuE,cAAc,EAC1C,CAIEC,EAAkB,CAJM,CACvB,IACAV,CAAQ,CAAC,EAAE,EAAI,EACf9D,AA5DP,IAAI,CA4DQsE,QAAQ,CAAC7F,EAAMuB,AA5D3B,IAAI,CA4D4BuE,cAAc,EAC1C,CAEGH,EACH,AACG,AAACpE,CAjER,IAAI,CAiESb,IAAI,CAACsF,WAAW,EAAI1B,EAAM,GAAM,GACtC/C,CAAAA,AAlEP,IAAI,CAkEQb,IAAI,CAACsF,WAAW,CAAGzE,AAlE/B,IAAI,CAkEgCb,IAAI,CAACuF,YAAY,CAACP,EAAe,EAE9DnE,AApEP,IAAI,CAoEQb,IAAI,CAACsF,WAAW,GACrBzE,AArEP,IAAI,CAqEQb,IAAI,CAACsF,WAAW,CAACE,IAAI,CAAC,CACvBC,OAAQ9F,EAAQ+F,SAAS,CACzB,eAAgB/F,EAAQ4E,SAAS,AACrC,GACA1D,AAzEP,IAAI,CAyEQb,IAAI,CAACsF,WAAW,CAACK,OAAO,CAAC,CAC1BvJ,EAAG4I,CACP,IAEA,AAACnE,AA7ER,IAAI,CA6ESb,IAAI,CAAC4F,WAAW,EAAItG,EAAM,GAAM,GACtCuB,CAAAA,AA9EP,IAAI,CA8EQb,IAAI,CAAC4F,WAAW,CAAG/E,AA9E/B,IAAI,CA8EgCb,IAAI,CAACuF,YAAY,CAACF,EAAe,EAE9DxE,AAhFP,IAAI,CAgFQb,IAAI,CAAC4F,WAAW,GACrB/E,AAjFP,IAAI,CAiFQb,IAAI,CAAC4F,WAAW,CAACJ,IAAI,CAAC,CACvBC,OAAQ9F,EAAQ+F,SAAS,CACzB,eAAgB/F,EAAQ4E,SAAS,AACrC,GACA1D,AArFP,IAAI,CAqFQb,IAAI,CAAC4F,WAAW,CAACD,OAAO,CAAC,CAC1BvJ,EAAGiJ,CACP,GAER,CAGKxE,AA5FJ,IAAI,CA4FKb,IAAI,CAAC6F,aAAa,EAIxBhF,AAhGH,IAAI,CAgGIb,IAAI,CAAC6F,aAAa,CAACL,IAAI,CAAC,CACzBC,OAAQ9F,EAAQ+F,SAAS,CACzB,eAAgB/F,EAAQ4E,SAAS,AACrC,GACA1D,AApGH,IAAI,CAoGIb,IAAI,CAAC6F,aAAa,CAACF,OAAO,CAAC,CAC5BvJ,EAAGoI,CACP,IATA3D,AA7FH,IAAI,CA6FIb,IAAI,CAAC6F,aAAa,CAAGhF,AA7F7B,IAAI,CA6F8Bb,IAAI,CAACuF,YAAY,CAACf,GAYrD3D,AAzGC,IAAI,CAyGAyD,QAAQ,CAACzD,AAzGb,IAAI,CAyGciF,QAAQ,CAAG,OAAS,OAAO,EAClD,CACJ,CAIA,GAHA,AAAC9F,CAAAA,GAAMK,SAAW,EAAE,AAAD,EAAG1B,OAAO,CAAC,AAAC6B,GAAWA,EAAOuF,MAAM,IAGnD,CAAClF,AA/GI,IAAI,CA+GHI,KAAK,EACXJ,AAhHK,IAAI,CAgHJqB,KAAK,CAAC8D,WAAW,EACrBnF,CAAAA,AAjHI,IAAI,CAiHH0C,SAAS,EAAI1C,AAjHd,IAAI,CAiHeoF,YAAY,EAAE1C,SAAQ,GAC9C1C,AAlHK,IAAI,CAkHJtC,aAAa,CAAC4E,MAAM,CAAE,CAC3B,IACInE,EAAOkH,EADLd,EAAiBvE,AAnHlB,IAAI,CAmHmBuE,cAAc,CAAEe,EAAWtF,AAnHlD,IAAI,CAmHmDtC,aAAa,CAACsC,AAnHrE,IAAI,CAmHsEtC,aAAa,CAAC4E,MAAM,CAAG,EAAE,CAAEU,EAAYhD,AAnHjH,IAAI,CAmHkHtC,aAAa,CAAC,EAAE,CAE3I,KAAO,AAACS,CAAAA,EAAQ6B,AArHX,IAAI,CAqHYuF,YAAY,CAACC,GAAG,EAAC,GAAMrH,EAAMsH,OAAO,EACrDtH,EAAMuH,IAAI,GAEd,KAAO,AAACL,CAAAA,EAAWrF,AAxHd,IAAI,CAwHe2F,WAAW,CAACH,GAAG,EAAC,GACpCH,EAASI,OAAO,EAChBJ,EAASK,IAAI,GAGjBvH,CAAAA,EAAQ6B,AA7HH,IAAI,CA6HIvC,KAAK,CAACuF,EAAU,CAAC7E,KAAK,AAAD,IAE1B4E,EAAMC,EAAYuB,EAClBvE,AAhIH,IAAI,CAgIIuF,YAAY,CAAC1C,IAAI,CAAC1E,EAAMyH,IAAI,IAGjCzH,EAAMuH,IAAI,IAIlBvH,CAAAA,EAAQ6B,AAvIH,IAAI,CAuIIvC,KAAK,CAAC6H,EAAS,CAACnH,KAAK,AAAD,IAEzBmH,EAAW7G,EAAM8F,EACjBvE,AA1IH,IAAI,CA0IIuF,YAAY,CAAC1C,IAAI,CAAC1E,EAAMyH,IAAI,IAGjCzH,EAAMuH,IAAI,IAGlB,IAAMG,EAAO7F,AAhJR,IAAI,CAgJSvC,KAAK,CAAC6H,EAAS,CAACO,IAAI,AAClCA,CAAAA,GACAP,EAAW7G,EAAM8F,GACjBe,EAAW7G,EAAM,GAAKuB,AAnJrB,IAAI,CAmJsBvC,KAAK,CAAC6H,EAAS,CAACQ,MAAM,EACjD9F,AApJC,IAAI,CAoJA2F,WAAW,CAAC9C,IAAI,CAACgD,EAAKD,IAAI,GAEvC,CACJ,CACJ,CAIA,SAASG,IAEL,IAAMC,EAAWhG,AADJ,IAAI,CACKtC,aAAa,EAAEuI,KAC/BnH,EAAUkB,AAFH,IAAI,CAEIlB,OAAO,CACtBiB,EAAcjB,EAAQK,IAAI,EAAI,CAAC,EAC/B+G,EAAalG,AAJN,IAAI,CAIO6B,WAAW,CAAC9C,MAAM,EAAI,CAAC,CAE3CgB,CAAAA,EAAYE,OAAO,GACfD,AAPK,IAAI,CAOJI,KAAK,EACVJ,AARK,IAAI,CAQJmG,MAAM,CAACrI,OAAO,CAAC,AAACqI,IACjBA,EAAOrH,OAAO,CAACsH,UAAU,CAAG,CAChC,GAKIJ,GACAlH,EAAQuH,oBAAoB,EAC5BvH,EAAQC,MAAM,EACd,CAAClC,EAAQqJ,EAAWnE,KAAK,GACxBjD,CAAAA,AACc,CAAA,IADdA,EAAQuH,oBAAoB,CAACL,EAASM,QAAQ,CAAC,CAC3CC,KAAK,EACNP,EAASQ,KAAK,CAAG,CAAA,IAErB1H,EAAQC,MAAM,CAACgD,KAAK,CAAG,OACnB,AAAClF,EAAQqJ,EAAW3I,CAAC,GACrBuB,CAAAA,EAAQC,MAAM,CAACxB,CAAC,CAAG,CAAA,IAOvB,AAAc,aAAd,IAAI,CAACmB,IAAI,EACTsB,AAjCC,IAAI,CAiCAb,IAAI,EACTa,AAlCC,IAAI,CAkCAb,IAAI,CAACK,OAAO,EACjB,CAAA,IAAI,CAACiH,cAAc,CAAG,IAAI,CAACC,YAAY,AAAD,EAItD,CASA,SAASC,EAAkB7G,CAAC,EACxB,IACI8G,EADE9H,EAAU,IAAI,CAACA,OAAO,CAAE+C,EAAc/B,EAAE+B,WAAW,CAAE9B,EAAe,AAACjB,GAAWxB,EAASwB,EAAQK,IAAI,EAAKL,EAAQK,IAAI,CAAG,CAAC,CAEpG,EAAA,IAAxBY,EAAYE,OAAO,GAGnB2G,EAAkB1J,EAAM,CAAA,EAAM,CAC1B2J,UAAY,wBAA2BhF,CAAAA,EAAYgF,SAAS,EAAI,EAAC,EACjER,qBAAsB,CAClBS,KAAM,CACFC,KAAM,CAAC,QAAS,OAAO,AAC3B,EACAC,IAAK,CACDD,KAAM,CAAC,SAAU,SAAU,OAAO,AACtC,EACAE,KAAM,CACFF,KAAM,CAAC,UAAW,MAAM,AAC5B,EACAG,MAAO,CACHH,KAAM,CAAC,OAAQ,OAAQ,KAAK,AAChC,CACJ,EACA5H,KAAM,CACFgI,YAAa,CACjB,EACApI,OAAQ,CACJqI,QAAS,EACTjE,MAAO,CACHkE,SAAU,OACd,CACJ,EACAlG,OAAQ,EACRJ,MAAO,CACHuG,KAAM,KACNC,aAAc,CAAA,EACdpF,SAAU,EACVgB,MAAO,CACHqE,aAAc,UAClB,CACJ,EAIAC,MAAO,CAAC,CACA,cACA,CAAC,EAAG,GAAI,IAAI,CACf,CAAE,CACC,SACA,CAAC,EAAG,GAAG,CACV,CAAE,CACC,SACA,CAAC,EAAG,EAAG,GAAG,CACb,CAAE,CACC,OACA,CAAC,EAAG,EAAE,CACT,CAAE,CACC,MACA,CAAC,EAAE,CACN,CAAE,CACC,OACA,CAAC,EAAE,CACN,CAAE,CACC,QACA,CAAC,EAAE,CACN,CAAE,CACC,OACA,KACH,CAAC,AACV,EAAG5F,GAEe,UAAd,IAAI,CAACe,IAAI,GAIL/F,EAAQgF,EAAYY,QAAQ,GAC5B,CAAC5F,EAAQgF,EAAY6F,iBAAiB,GACtCd,CAAAA,EAAgBc,iBAAiB,CAAG,GAAE,EAM1C,CAAA,CAAA,CAAC7K,EAAQgF,EAAY6F,iBAAiB,GAElC7K,EAAQgF,EAAYY,QAAQ,CAAA,GAC3B5F,EAAQgF,EAAY8F,cAAc,GAClC9K,EAAQgF,EAAY6E,YAAY,GAChC7J,EAAQgF,EAAY4F,KAAK,GAC1Bb,CAAAA,EAAgBe,cAAc,CAAG,SAAU5E,CAAG,CAAEtE,CAAG,EAC/C,IAAMmJ,EAAa,IAAI,CAACxC,YAAY,EAAE1H,eAAeuI,KACrD,GAAI2B,EAAY,CACZ,IAAMH,EAASb,EAAgBa,KAAK,EAAI,EAAE,CACtCI,EAASrB,EAAQ,EAAGF,EAAW,OACnC,IAAK,IAAIwB,EAAI,EAAGA,EAAIL,EAAMnF,MAAM,CAAEwF,IAAK,CACnC,IAAMC,EAAON,CAAK,CAACK,EAAE,CACrB,GAAIC,GAAQA,CAAI,CAAC,EAAE,GAAKH,EAAWtB,QAAQ,CAAE,CACzCuB,EAAUC,EACV,KACJ,CACJ,CAEA,IAAMC,EAAQ9K,EAAS4K,IAAYJ,CAAK,CAACI,EAAU,EAAE,CACrD,GAAIE,EAAM,CACNzB,EAAWyB,CAAI,CAAC,EAAE,EAAI,OACtB,IAAMC,EAASD,CAAI,CAAC,EAAE,CACtBvB,EAAQwB,GAAQ,CAAC,EAAE,EAAI,CAG3B,KACSJ,AAAwB,SAAxBA,EAAWtB,QAAQ,EAExBE,CAAAA,EAAQoB,AAAmB,GAAnBA,EAAWpB,KAAK,AAAI,EAEhC,IAAMyB,EAAY7K,CAAS,CAACkJ,EAAS,CAErC,OADA,IAAI,CAACI,YAAY,CAAGuB,EAAYzB,EACzB,IAAI,CAACnF,KAAK,CAAC6G,IAAI,CAACC,YAAY,CAAC,CAAEF,UAAAA,EAAWzB,MAAAA,EAAOF,SAAAA,CAAS,EAAGvD,EAAKtE,EAAK,IAAI,CAACK,OAAO,CAACsJ,WAAW,CAC1G,CACJ,CAAA,GAIRlL,EAAM,CAAA,EAAM,IAAI,CAAC4B,OAAO,CAAE8H,GACtB,IAAI,CAACxG,KAAK,GAOVtB,EAAQuJ,UAAU,CAAGlL,EAAK0E,EAAYwG,UAAU,CAAE,GAClDvJ,EAAQwJ,UAAU,CAAGnL,EAAK0E,EAAYyG,UAAU,CAAE,IAIlDrL,EAAS6B,EAAQK,IAAI,CAACgI,WAAW,GACjCrI,CAAAA,EAAQyJ,SAAS,CAAGzJ,EAAQ4E,SAAS,CACjC3D,EAAYoH,WAAW,AAAD,EAGtC,CAIA,SAASqB,EAAmB1I,CAAC,EAEzB,IAAM+B,EAAc/B,EAAE+B,WAAW,CAC3B9B,EAAc8B,GAAa1C,MAAQ,CAAC,EACpCK,EAAUO,EAAYP,OAAO,AAG/BO,CAAAA,EAAYE,OAAO,EAAIT,GACvBtC,EAAM,CAAA,EAAM8C,AAPH,IAAI,CAOIlB,OAAO,CAAEU,CAAO,CAAC,EAAE,CAE5C,CAKA,SAASiJ,IAEL,AAACzI,CAAAA,AADY,IAAI,CACXb,IAAI,CAACK,OAAO,EAAI,EAAE,AAAD,EAAG1B,OAAO,CAAC,AAAC6B,GAAWA,EAAO+I,QAAQ,GACjE,CAMA,SAASC,EAAgB7I,CAAC,EACtB,GAAM,CAAEM,MAAAA,CAAK,CAAEiD,mBAAAA,CAAkB,CAAEvE,QAAS,CAAEK,KAAMY,EAAc,CAAC,CAAC,CAAE,CAAE,CAAG,IAAI,CAC/E,GAAIA,EAAYE,OAAO,EAAIoD,EAAoB,CAC3C,IAAMuF,EAAe,AAA+B,EAA/B,IAAI,CAAC9J,OAAO,CAACC,MAAM,CAACgF,QAAQ,CAC3CA,EAAW3D,EACZL,EAAY8I,UAAU,EACnBD,EAAevF,EAAmBxF,MAAM,CAC5C+K,EAAevF,EAAmBzF,KAAK,CACvCZ,EAAQ8C,EAAEc,QAAQ,EAClBd,EAAEc,QAAQ,CAAC,EAAE,CAAGmD,EAGhBjE,EAAEc,QAAQ,CAAG,CAACmD,EAAU,EAAE,AAElC,CACJ,CAIA,SAAS+E,IACL,IAAI,CAACnG,IAAI,CAAC7E,OAAO,CAAC,AAACkC,IACf,AAACA,CAAAA,EAAKb,IAAI,EAAEK,SAAW,EAAE,AAAD,EAAG1B,OAAO,CAAC,AAAC6B,IAChCA,EAAOoJ,WAAW,GAClBpJ,EAAOqJ,kBAAkB,EAC7B,EACJ,EACJ,CAIA,SAASC,EAAUnJ,CAAC,EAChB,GAAM,CAAEX,KAAAA,CAAI,CAAE,CAAG,IAAI,CACrB,AAACA,CAAAA,EAAKK,OAAO,EAAI,EAAE,AAAD,EAAG1B,OAAO,CAAC,AAAC6B,GAAWA,EAAO4D,OAAO,CAACzD,EAAEoJ,UAAU,GACpE/J,EAAKK,OAAO,CAAG,KAAK,CACxB,CAKA,SAAS2J,EAAOrJ,CAAC,EAEb,IAAM+B,EAAc/B,EAAE+B,WAAW,EAAI,CAAC,EAChC9B,EAAc8B,EAAY1C,IAAI,EAAI,CAAC,CACrCY,CAAAA,EAAYE,OAAO,EAAIpD,EAAQkD,EAAYqJ,WAAW,GACtDvH,CAAAA,EAAYwH,SAAS,CAAGxH,EAAYgD,SAAS,CAAI9E,EAAYqJ,WAAW,EAExE,AAACpJ,AANQ,IAAI,CAMPb,IAAI,EACVa,CAAAA,AAPS,IAAI,CAORb,IAAI,CAAG,IAAImK,EAPP,IAAI,CAOyB,EAE1CtJ,AATa,IAAI,CASZuF,YAAY,CAAG,EAAE,CACtBvF,AAVa,IAAI,CAUZ2F,WAAW,CAAG,EAAE,AACzB,CAKA,SAAS4D,EAA4BzJ,CAAC,EAClC,IAAmB3B,EAAQH,AAAd,IAAI,CAAeG,KAAK,CAAE6B,EAAOhC,AAAjC,IAAI,CAAkCgC,IAAI,CAAEwJ,EAAWxJ,EAAKwJ,QAAQ,CAAEnI,EAAQrB,EAAKqB,KAAK,CAA0BtB,EAAcjB,AAA5BkB,EAAKlB,OAAO,CAAwBK,IAAI,EAAI,CAAC,EAAGsK,EAAYzJ,EAAKlB,OAAO,CAACC,MAAM,CAAEgD,EAAQ0H,EAAU1H,KAAK,CAGzN1C,EAAOxE,CAAY,CAACmF,EAAKX,IAAI,CAAC,CAAEkF,EAAiBzE,EAAEyE,cAAc,CAAE7G,EAAgBsC,EAAKtC,aAAa,CAAEgM,EAAU1L,AAHpG,IAAI,CAGqGD,GAAG,CAAGwG,EAAgBoF,EAAe1M,EAASS,CAAa,CAACoC,EAAE8J,KAAK,CAAG,EAAE,EAC1LlM,CAAa,CAACoC,EAAE8J,KAAK,CAAG,EAAE,CAAGrF,EAC7B,AAACvE,CAAAA,EAAKvB,GAAG,EAAI,CAAA,EAAK8F,EAAiB3D,EAAWZ,EAAKY,QAAQ,CAAC,QAAS2H,EAAY3H,EAAWA,CAAQ,CAAC,EAAE,CAAG,EAAGiJ,EAAYjJ,EAAWA,CAAQ,CAAC,EAAE,CAAG,EAAI,EAE1J,GAAIb,AAAwB,CAAA,IAAxBA,EAAYE,OAAO,CAAW,CAC9B,IAAIyB,EAAQjB,EAAKJ,EAAMyJ,EA2CvB,GAzCIzK,AAAS,QAATA,EAEAoB,EAAMiB,AADNA,CAAAA,EAAS1B,EAAKS,GAAG,CAAGT,EAAKO,MAAM,AAAD,EACfgI,EAEVlJ,AAAS,WAATA,EAELqC,EAASjB,AADTA,CAAAA,EAAMY,EAAM0I,WAAW,CAAG/J,EAAK0B,MAAM,CAAG1B,EAAKO,MAAM,AAAD,EACnCgI,GAGf7G,EAAS1B,EAAKS,GAAG,CAAGT,EAAKgK,GAAG,CAAIhK,CAAAA,EAAKiK,SAAS,CAACT,EAAWG,EAAcD,IAAY,CAAA,EACpFjJ,EAAMT,EAAKS,GAAG,CAAGT,EAAKgK,GAAG,CAAIhK,CAAAA,EAAKiK,SAAS,CAACT,EAAWE,EAAUC,IAAgB,CAAA,GAGjFtK,AAAS,UAATA,EAEAyK,EAAQzJ,AADRA,CAAAA,EAAOgB,EAAMgD,UAAU,CAAGrE,EAAK8J,KAAK,CAAG9J,EAAKO,MAAM,AAAD,EAClCgI,EAEVlJ,AAAS,SAATA,EAELgB,EAAOyJ,AADPA,CAAAA,EAAQ9J,EAAKK,IAAI,CAAGL,EAAKO,MAAM,AAAD,EACfgI,GAGflI,EAAO9B,KAAKC,KAAK,CAACwB,EAAKK,IAAI,CAAIL,CAAAA,EAAKiK,SAAS,CAACT,EAAWG,EAAcD,IAAY,CAAA,GAAMG,EACzFC,EAAQvL,KAAKwE,GAAG,CAChBxE,KAAKC,KAAK,CAACwB,EAAKK,IAAI,CAAIL,CAAAA,EAAKiK,SAAS,CAACT,EAAWE,EAAUC,IAAgB,CAAA,GAAME,EAAW7J,EAAKK,IAAI,CAAGL,EAAKgK,GAAG,GAErHhM,AApCS,IAAI,CAoCRkF,SAAS,CAAG4G,EAAQzJ,EAGzBP,EAAE/B,GAAG,CAACR,CAAC,CAAIwE,AAAU,SAAVA,EACP1B,EACA0B,AAAU,UAAVA,EACI+H,EACAzJ,EAAQ,AAACyJ,CAAAA,EAAQzJ,CAAG,EAAK,EAEjCP,EAAE/B,GAAG,CAACkD,CAAC,CAICR,EAAO,AAACiB,CAAAA,EAASjB,CAAE,EAAK,EAE5BtC,EAAO,CACP,IAAM+L,EAAa7I,EAAMC,QAAQ,CAACC,WAAW,CAACpD,GAAQF,EAAcE,EAAMC,OAAO,GAAGP,MAAM,CAG1F,GAAK4L,EAAUU,OAAO,CAUlBrK,EAAE/B,GAAG,CAACkD,CAAC,EAEPiJ,EAAWE,CAAC,CAER,CAAEnM,CAAAA,EAAc,CAAA,MAdA,CACpB,IAAMoM,EAAQ9L,KAAKC,KAAK,CAACP,EAAciM,EAAWI,CAAC,CACnDxK,CAAAA,EAAE/B,GAAG,CAACkD,CAAC,EAGP,AAAEiJ,CAAAA,EAAWE,CAAC,CAAIF,CAAAA,EAAWI,CAAC,CAAGJ,EAAW1I,CAAC,AAADA,CAAC,EAAK,EAE9C,CAAE,CAAA,AAAE6I,CAAAA,EAAQ,CAAA,EAAKH,EAAWI,CAAC,CAAI,CAAA,CACzC,CAQJ,CACAxK,EAAE/B,GAAG,CAACR,CAAC,EAAI,AAACyC,EAAKI,KAAK,EAAIqJ,EAAUlM,CAAC,EAAK,CAC9C,CACJ,CAIA,SAASgN,EAAkBC,CAAG,EAC1B,GAAM,CAAExK,KAAAA,CAAI,CAAEyK,MAAAA,CAAK,CAAE,CAAGD,EACxB,GAAIxK,EAAKlB,OAAO,CAACK,IAAI,EAAEc,QAAS,CAC5B,IAOIyK,EAPEhB,EAAU1J,EAAKtC,aAAa,CAC5ByI,EAAS,AAACnG,CAAAA,EAAKoF,YAAY,EAAIpF,CAAG,EAAGmG,MAAM,CAAC,EAAE,CAC9CwE,EAAUF,IAAUf,CAAO,CAAC,EAAE,CAC9B5D,EAAS2E,IAAUf,CAAO,CAACA,EAAQpH,MAAM,CAAG,EAAE,CAC9CsI,EAAQzE,GAAUpJ,EAAKoJ,EAAOrH,OAAO,CAAC+L,IAAI,CAAE,SAAUC,CAAC,EACzD,OAAOA,CAAC,CAAC9K,EAAK+K,OAAO,CAAG,IAAM,IAAI,GAAKN,CAC3C,GAEIG,GAASzE,EAAO6E,EAAE,CAAC,WAGnBN,EAAYxN,EAAM0N,GAClBpO,IAA8EyO,WAAW,CAACC,KAAK,CAAChP,SAAS,CAACiP,UAAU,CAC/GC,oBAAoB,CAACV,EAAW1K,EAAKqB,KAAK,GAInDmJ,EAAIG,OAAO,CAAGA,EACdH,EAAI1E,MAAM,CAAGA,EACb0E,EAAII,KAAK,CAAGF,CAChB,CACJ,CAoBA,SAASW,IACL,IAAmBvM,EAAUkB,AAAhB,IAAI,CAAiBlB,OAAO,CAAEiB,EAAcjB,EAAQK,IAAI,EAAI,CAAC,EAAGmM,EAAetL,AAA/E,IAAI,CAAgFgC,UAAU,CAAEtE,EAAgBsC,AAAhH,IAAI,CAAiHtC,aAAa,CAAE6N,EAAW7N,CAAa,CAAC,EAAE,CAAE8N,EAAY9N,CAAa,CAAC,EAAE,CAAE+N,EAAU/N,CAAa,CAACA,EAAc4E,MAAM,CAAG,EAAE,CAAEoJ,EAAgBhO,CAAa,CAACA,EAAc4E,MAAM,CAAG,EAAE,CAAEqJ,EAAY3L,AAAvT,IAAI,CAAwToF,YAAY,EAAErC,IAAK6I,EAAY5L,AAA3V,IAAI,CAA4VoF,YAAY,EAAE3G,IAAKsE,EAAM4I,GAAa3L,AAAtY,IAAI,CAAuY+C,GAAG,CAAEtE,EAAMmN,GAAa5L,AAAna,IAAI,CAAoavB,GAAG,CAAEiI,EAAe1G,AAA5b,IAAI,CAA6b0G,YAAY,CAAEmF,EAC5d5O,EAAS8F,IACLA,GAAOwI,EAAW7E,GAClB3D,EAAMyI,EAAYM,EAAkB7O,EAAS8F,IAC7CwI,EAAWxI,GACXwI,EAAW7E,EAAe3D,EAAMgJ,EAAoB9O,EAASwB,IAC7DgN,EAAUhN,GACVgN,EAAU/E,EAAejI,EAAMuN,EAAkB/O,EAASwB,IAC1DA,GAAOgN,EAAU/E,GACjBjI,EAAMiN,CACkB,EAAA,IAAxB3L,EAAYE,OAAO,EACnB,CAACqL,GACAtL,CAAAA,AAZQ,IAAI,CAYP+K,OAAO,EAAI/K,AAZR,IAAI,CAYSiM,QAAQ,AAAD,IACzB,AAACH,CAAAA,GAAkBD,CAAe,GAAM,CAAC/M,EAAQoN,WAAW,EAC5DxO,CAAAA,CAAa,CAAC,EAAE,CAAGqF,CAAE,EAErB,AAACgJ,CAAAA,GAAoBC,CAAa,GAAM,CAAClN,EAAQqN,SAAS,EAC1DzO,CAAAA,CAAa,CAACA,EAAc4E,MAAM,CAAG,EAAE,CAAG7D,CAAE,EAGxD,CAKA,SAAS2N,EAAalN,CAAO,MA9yBRmN,EAgzBjB,GAAM,CAAEvN,QAAS,CAAEK,KAAMY,EAAc,CAAC,CAAC,CAAE,CAAE,CADhC,IAAI,OAEjB,AAAIA,AAAwB,CAAA,IAAxBA,EAAYE,OAAO,EAAaD,AAFvB,IAAI,CAEwBgC,UAAU,CACxChC,AAHE,IAAI,CAGD0G,YAAY,CAErBxH,EAAQI,KAAK,CALP,IAAI,EA/yBA+M,EAozBsBC,UAnzBhCC,MAAMrQ,SAAS,CAACuD,KAAK,CAACrD,IAAI,CAACiQ,EAAM,IAozB5C,EAn0BA,AAAC,SAAUxR,CAAY,EACnBA,CAAY,CAACA,EAAa,GAAM,CAAG,EAAE,CAAG,MACxCA,CAAY,CAACA,EAAa,KAAQ,CAAG,EAAE,CAAG,QAC1CA,CAAY,CAACA,EAAa,MAAS,CAAG,EAAE,CAAG,SAC3CA,CAAY,CAACA,EAAa,IAAO,CAAG,EAAE,CAAG,MAC7C,EAAGA,GAAiBA,CAAAA,EAAe,CAAC,CAAA,EAy0BpC,OAAMyO,EAMFkD,YAAYxM,CAAI,CAAE,CACd,IAAI,CAACA,IAAI,CAAGA,CAChB,CAqBAwD,aAAc,CACV,IAAMxD,EAAO,IAAI,CAACA,IAAI,CAChBqB,EAAQrB,EAAKqB,KAAK,CAClBgB,EAAcrC,EAAKb,IAAI,CAACkD,WAAW,CACnC7C,EAAWQ,EAAKoF,YAAY,EAAEjG,KAAKK,SACrCQ,EAAKb,IAAI,CAACK,OAAO,EACjB,EAAE,CACAiN,EAAapK,EAAcrC,EAAKoF,YAAY,CAAGpF,EACjD0M,EAAY,GAAIC,EAAY,SAGhC,AAAI3M,AAAc,IAAdA,EAAKX,IAAI,EAAU,CAACgC,EAAMuL,QAAQ,EAAIpN,EAAQ8C,MAAM,CAC7C,CAACtC,EAAKoF,YAAY,EAE7B,AAAC/D,CAAAA,CAAK,CAACrB,EAAK4C,IAAI,CAAC,EAAI,EAAE,AAAD,EAAG9E,OAAO,CAAC,CAAC+O,EAAWjD,KACrCiD,EAAUxN,IAAI,GAAKW,EAAKX,IAAI,EAC3BwN,EAAU/N,OAAO,CAAC0D,UAAU,GAC7BmK,EAAY/C,EACRiD,IAAcJ,GAEdC,CAAAA,EAAY9C,CAAI,EAG5B,GACQ+C,IAAcD,GACjBzP,CAAAA,CAAAA,EAASoF,IACN7C,EAAQ8C,MAAM,GAAKD,CAChB,EACf,CASAqC,aAAaoI,CAAI,CAAE,CACf,IAAM9M,EAAO,IAAI,CAACA,IAAI,CAAEsB,EAAWtB,EAAKqB,KAAK,CAACC,QAAQ,CAAExC,EAAUkB,EAAKlB,OAAO,CAAEiO,EAAkBzL,EAASwL,IAAI,CAACA,GAC3GE,QAAQ,CAAC,wBACTC,GAAG,CAACjN,EAAKkN,SAAS,EAQvB,OAPI,AAAC5L,EAAS2B,UAAU,EACpB8J,EAAgBpI,IAAI,CAAC,CACjBC,OAAQ9F,EAAQ+F,SAAS,CACzB,eAAgB/F,EAAQ4E,SAAS,CACjCyJ,OAAQ,CACZ,GAEGJ,CACX,CACJ,CAOApQ,EAAYyQ,CAAC,CAAG,SAAUC,CAAS,EAC/B,OAAO,IAAI,CAACC,UAAU,CAAC,KAAMD,EAAW,CAAA,GAAME,MAAM,CAAC,EACzD,EAEA5Q,EAAY6Q,CAAC,CAAG,SAAUH,CAAS,EAC/B,IAAM9R,EAAI,IAAI,CAACkS,OAAO,CAACJ,GAAYK,EAAW,AAACnS,CAAAA,CAAC,CAAC,EAAE,CAAG,CAAA,EAAK,EAAGoS,EAAWpS,EAAEkE,KAAK,CAAC,EACjFkO,CAAAA,CAAQ,CAAC,EAAE,CAAGpS,CAAC,CAAC,EAAE,CAAGmS,EAAW,EAChC,IAAME,EAAgB,IAAI,CAACH,OAAO,CAAC,IAAI,CAACI,QAAQ,CAACF,CAAQ,CAAC,EAAE,CAAE,EAAG,IAMjE,OALyB,IAArBC,CAAa,CAAC,EAAE,GAChBrS,CAAC,CAAC,EAAE,CAAG,EACPA,CAAC,CAAC,EAAE,CAAG,EAAI,AAAC,CAAA,GAAKqS,CAAa,CAAC,EAAE,AAAD,EAAK,GAGlC,AAAC,CAAA,EACJrP,KAAKuP,KAAK,CAAC,AAACC,CAAAA,AAFG,IAAI,CAACF,QAAQ,CAACF,CAAQ,CAAC,EAAE,CAAEA,CAAQ,CAAC,EAAE,CAAEA,CAAQ,CAAC,EAAE,EAAqB,IAAI,CAACE,QAAQ,CAACD,CAAa,CAAC,EAAE,CAAEA,CAAa,CAAC,EAAE,CAAEA,CAAa,CAAC,EAAE,CAElH,EAAK,OAAS,EAAGI,QAAQ,EACxE,EAgGA,IAAMC,EAAKzR,IAEX0R,AA5FiB,CAAA,CACbC,QAl4BJ,SAAiBC,CAAS,CAAEC,CAAU,CAAEC,CAAS,EAsB7C,OArBKF,EAAUG,SAAS,CAACC,QAAQ,CAAC,UAC9BJ,EAAUG,SAAS,CAAC1L,IAAI,CAAC,QACzBuL,EAAUlS,SAAS,CAACsB,qBAAqB,CAAGA,EAC5CH,EAAK+Q,EAAUlS,SAAS,CAAE,WAAYkQ,GACtC/O,EAAK+Q,EAAUlS,SAAS,CAAE,YAAa+C,GAEvCrC,EAASwR,EAAW,OAAQjF,GAC5BvM,EAASwR,EAAW,wBAAyBvO,GAC7CjD,EAASwR,EAAW,YAAaxM,GACjChF,EAASwR,EAAW,cAAetL,GACnClG,EAASwR,EAAW,0BAA2BrI,GAC/CnJ,EAASwR,EAAW,kBAAmBzH,GACvC/J,EAASwR,EAAW,kBAAmB5F,GACvC5L,EAASwR,EAAW,gBAAiB3F,GACrC7L,EAASwR,EAAW,gBAAiBzF,GACrC/L,EAASwR,EAAW,YAAa/C,GACjCzO,EAASwR,EAAW,UAAWnF,GAC/BrM,EAASyR,EAAY,oBAAqBvF,GAC1ClM,EAAS0R,EAAW,wBAAyB/E,GAC7C3M,EAAS0R,EAAW,cAAe/D,IAEhC6D,CACX,CA42BA,CAAA,EA0FcD,OAAO,CAACF,EAAEQ,IAAI,CAAER,EAAES,KAAK,CAAET,EAAEU,IAAI,EAChB,IAAMrS,EAAkBE,IAG3C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}