!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/pareto
 * @requires highcharts
 *
 * Pareto series type for Highcharts
 *
 * (c) 2010-2025 <PERSON>
 *
 * License: www.highcharts.com/license
 */function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(e._Highcharts,e._Highcharts.Series,e._Highcharts.SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/modules/pareto",["highcharts/highcharts"],function(e){return t(e,e.Series,e.SeriesRegistry)}):"object"==typeof exports?exports["highcharts/modules/pareto"]=t(e._Highcharts,e._Highcharts.Series,e._Highcharts.SeriesRegistry):e.Highcharts=t(e.Highcharts,e.Highcharts.Series,e.Highcharts.SeriesRegistry)}("undefined"==typeof window?this:window,(e,t,s)=>(()=>{"use strict";var i,r={512:e=>{e.exports=s},820:e=>{e.exports=t},944:t=>{t.exports=e}},a={};function n(e){var t=a[e];if(void 0!==t)return t.exports;var s=a[e]={exports:{}};return r[e](s,s.exports,n),s.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var s in t)n.o(t,s)&&!n.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var o={};n.d(o,{default:()=>H});var h=n(944),d=n.n(h),p=n(820),u=n.n(p);let{noop:l}=d(),{addEvent:c,defined:f}=d();!function(e){function t(){u().prototype.init.apply(this,arguments),this.initialised=!1,this.baseSeries=null,this.eventRemovers=[],this.addEvents()}function s(){let e=this.chart,t=this.options.baseSeries,s=f(t)&&(e.series[t]||e.get(t));this.baseSeries=s||null}function i(){this.eventRemovers.push(c(this.chart,"afterLinkSeries",()=>{this.setBaseSeries(),this.baseSeries&&!this.initialised&&(this.setDerivedData(),this.addBaseSeriesEvents(),this.initialised=!0)}))}function r(){this.eventRemovers.push(c(this.baseSeries,"updatedData",()=>{this.setDerivedData()}),c(this.baseSeries,"destroy",()=>{this.baseSeries=null,this.initialised=!1}))}function a(){this.eventRemovers.forEach(e=>{e()}),u().prototype.destroy.apply(this,arguments)}e.hasDerivedData=!0,e.setDerivedData=l,e.compose=function(e){let n=e.prototype;return n.addBaseSeriesEvents=r,n.addEvents=i,n.destroy=a,n.init=t,n.setBaseSeries=s,e},e.init=t,e.setBaseSeries=s,e.addEvents=i,e.addBaseSeriesEvents=r,e.destroy=a}(i||(i={}));let v=i;var y=n(512),S=n.n(y);let{line:g}=S().seriesTypes,{correctFloat:m,merge:D,extend:b}=d();class x extends g{sumPointsPercents(e,t,s,i){let r=[],a=0,n=0,o=0,h;for(let d of e)null!==d&&(i?n+=d:(h=d/s*100,r.push([t[a],m(o+h)]),o+=h)),++a;return i?n:r}setDerivedData(){let e=this.baseSeries?.getColumn("x")||[],t=this.baseSeries?.getColumn("y")||[],s=this.sumPointsPercents(t,e,null,!0);this.setData(this.sumPointsPercents(t,e,s,!1),!1)}}x.defaultOptions=D(g.defaultOptions,{zIndex:3}),b(x.prototype,{hasDerivedData:v.hasDerivedData}),v.compose(x),S().registerSeriesType("pareto",x);let H=d();return o.default})());