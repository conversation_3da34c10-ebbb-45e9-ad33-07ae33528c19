!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/vector
 * @requires highcharts
 *
 * Vector plot series module
 *
 * (c) 2010-2025 Torstein Honsi
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/modules/vector",["highcharts/highcharts"],function(t){return e(t,t.SeriesRegistry)}):"object"==typeof exports?exports["highcharts/modules/vector"]=e(t._Highcharts,t._Highcharts.SeriesRegistry):t.Highcharts=e(t.Highcharts,t.Highcharts.SeriesRegistry)}("undefined"==typeof window?this:window,(t,e)=>(()=>{"use strict";var r={512:t=>{t.exports=e},944:e=>{e.exports=t}},i={};function o(t){var e=i[t];if(void 0!==e)return e.exports;var s=i[t]={exports:{}};return r[t](s,s.exports,o),s.exports}o.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return o.d(e,{a:e}),e},o.d=(t,e)=>{for(var r in e)o.o(e,r)&&!o.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},o.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var s={};o.d(s,{default:()=>m});var n=o(944),a=o.n(n),h=o(512),p=o.n(h);let{animObject:l}=a(),{series:c,seriesTypes:{scatter:d}}=p(),{arrayMax:g,extend:u,merge:y,pick:f}=a();class b extends d{animate(t){t?this.markerGroup.attr({opacity:.01}):this.markerGroup.animate({opacity:1},l(this.options.animation))}arrow(t){let e=t.length/this.lengthMax*this.options.vectorLength/20,r={start:10*e,center:0,end:-10*e}[this.options.rotationOrigin]||0;return[["M",0,7*e+r],["L",-1.5*e,7*e+r],["L",0,10*e+r],["L",1.5*e,7*e+r],["L",0,7*e+r],["L",0,-10*e+r]]}drawPoints(){let t=this.chart;for(let e of this.points){let r=e.plotX,i=e.plotY;!1===this.options.clip||t.isInsidePlot(r,i,{inverted:t.inverted})?(e.graphic||(e.graphic=this.chart.renderer.path().add(this.markerGroup).addClass("highcharts-point highcharts-color-"+f(e.colorIndex,e.series.colorIndex))),e.graphic.attr({d:this.arrow(e),translateX:r,translateY:i,rotation:e.direction}),this.chart.styledMode||e.graphic.attr(this.pointAttribs(e))):e.graphic&&(e.graphic=e.graphic.destroy())}}pointAttribs(t,e){let r=this.options,i=t?.color||this.color,o=this.options.lineWidth;return e&&(i=r.states[e].color||i,o=(r.states[e].lineWidth||o)+(r.states[e].lineWidthPlus||0)),{stroke:i,"stroke-width":o}}translate(){c.prototype.translate.call(this),this.lengthMax=g(this.getColumn("length"))}}b.defaultOptions=y(d.defaultOptions,{lineWidth:2,marker:void 0,rotationOrigin:"center",states:{hover:{lineWidthPlus:1}},tooltip:{pointFormat:"<b>[{point.x}, {point.y}]</b><br/>Length: <b>{point.length}</b><br/>Direction: <b>{point.direction}\xb0</b><br/>"},vectorLength:20}),u(b.prototype,{drawGraph:a().noop,getSymbol:a().noop,markerAttribs:a().noop,parallelArrays:["x","y","length","direction"],pointArrayMap:["y","length","direction"]}),p().registerSeriesType("vector",b);let m=a();return s.default})());