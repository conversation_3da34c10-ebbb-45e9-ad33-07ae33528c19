{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/datagrouping\n * @requires highcharts\n *\n * Data grouping module\n *\n * (c) 2010-2025 Torstein Hønsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"Templating\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/datagrouping\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"],amd1[\"Templating\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/datagrouping\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"Templating\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"Templating\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__984__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ }),\n\n/***/ 984:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__984__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ datagrouping_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Extensions/DataGrouping/ApproximationRegistry.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n/**\n * Define the available approximation types. The data grouping\n * approximations takes an array or numbers as the first parameter. In case\n * of ohlc, four arrays are sent in as four parameters. Each array consists\n * only of numbers. In case null values belong to the group, the property\n * .hasNulls will be set to true on the array.\n *\n * @product highstock\n *\n * @private\n */\nconst ApproximationRegistry = {\n// Approximations added programmatically\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const DataGrouping_ApproximationRegistry = (ApproximationRegistry);\n\n;// ./code/es-modules/Extensions/DataGrouping/ApproximationDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { arrayMax, arrayMin, correctFloat, extend, isNumber } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction average(arr) {\n    const len = arr.length;\n    let ret = sum(arr);\n    // If we have a number, return it divided by the length. If not,\n    // return null or undefined based on what the sum method finds.\n    if (isNumber(ret) && len) {\n        ret = correctFloat(ret / len);\n    }\n    return ret;\n}\n/**\n * The same as average, but for series with multiple values, like area ranges.\n * @private\n */\nfunction averages() {\n    const ret = [];\n    [].forEach.call(arguments, function (arr) {\n        ret.push(average(arr));\n    });\n    // Return undefined when first elem. is undefined and let\n    // sum method handle null (#7377)\n    return typeof ret[0] === 'undefined' ? void 0 : ret;\n}\n/**\n * @private\n */\nfunction ApproximationDefaults_close(arr) {\n    return arr.length ?\n        arr[arr.length - 1] :\n        (arr.hasNulls ? null : void 0);\n}\n/**\n * @private\n */\nfunction high(arr) {\n    return arr.length ?\n        arrayMax(arr) :\n        (arr.hasNulls ? null : void 0);\n}\n/**\n * HLC, OHLC and range are special cases where a multidimensional array is input\n * and an array is output.\n * @private\n */\nfunction hlc(high, low, close) {\n    high = DataGrouping_ApproximationRegistry.high(high);\n    low = DataGrouping_ApproximationRegistry.low(low);\n    close = DataGrouping_ApproximationRegistry.close(close);\n    if (isNumber(high) ||\n        isNumber(low) ||\n        isNumber(close)) {\n        return [high, low, close];\n    }\n}\n/**\n * @private\n */\nfunction low(arr) {\n    return arr.length ?\n        arrayMin(arr) :\n        (arr.hasNulls ? null : void 0);\n}\n/**\n * @private\n */\nfunction ohlc(open, high, low, close) {\n    open = DataGrouping_ApproximationRegistry.open(open);\n    high = DataGrouping_ApproximationRegistry.high(high);\n    low = DataGrouping_ApproximationRegistry.low(low);\n    close = DataGrouping_ApproximationRegistry.close(close);\n    if (isNumber(open) ||\n        isNumber(high) ||\n        isNumber(low) ||\n        isNumber(close)) {\n        return [open, high, low, close];\n    }\n}\n/**\n * @private\n */\nfunction ApproximationDefaults_open(arr) {\n    return arr.length ? arr[0] : (arr.hasNulls ? null : void 0);\n}\n/**\n * @private\n */\nfunction range(low, high) {\n    low = DataGrouping_ApproximationRegistry.low(low);\n    high = DataGrouping_ApproximationRegistry.high(high);\n    if (isNumber(low) || isNumber(high)) {\n        return [low, high];\n    }\n    if (low === null && high === null) {\n        return null;\n    }\n    // Else, return is undefined\n}\n/**\n * @private\n */\nfunction sum(arr) {\n    let len = arr.length, ret;\n    // 1. it consists of nulls exclusive\n    if (!len && arr.hasNulls) {\n        ret = null;\n        // 2. it has a length and real values\n    }\n    else if (len) {\n        ret = 0;\n        while (len--) {\n            ret += arr[len];\n        }\n    }\n    // 3. it has zero length, so just return undefined\n    // => doNothing()\n    return ret;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst ApproximationDefaults = {\n    average,\n    averages,\n    close: ApproximationDefaults_close,\n    high,\n    hlc,\n    low,\n    ohlc,\n    open: ApproximationDefaults_open,\n    range,\n    sum\n};\nextend(DataGrouping_ApproximationRegistry, ApproximationDefaults);\n/* harmony default export */ const DataGrouping_ApproximationDefaults = (ApproximationDefaults);\n\n;// ./code/es-modules/Extensions/DataGrouping/DataGroupingDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n/**\n * Common options\n * @private\n */\nconst common = {\n    /// enabled: null, // (true for stock charts, false for basic),\n    // forced: undefined,\n    groupPixelWidth: 2,\n    // The first one is the point or start value, the second is the start\n    // value if we're dealing with range, the third one is the end value if\n    // dealing with a range\n    dateTimeLabelFormats: {\n        millisecond: [\n            '%[AebHMSL]',\n            '%[AebHMSL]',\n            '-%[HMSL]'\n        ],\n        second: [\n            '%[AebHMS]',\n            '%[AebHMS]',\n            '-%[HMS]'\n        ],\n        minute: [\n            '%[AebHM]',\n            '%[AebHM]',\n            '-%[HM]'\n        ],\n        hour: [\n            '%[AebHM]',\n            '%[AebHM]',\n            '-%[HM]'\n        ],\n        day: [\n            '%[AebY]',\n            '%[Aeb]',\n            '-%[AebY]'\n        ],\n        week: [\n            '%v %[AebY]',\n            '%[Aeb]',\n            '-%[AebY]'\n        ],\n        month: [\n            '%[BY]',\n            '%[B]',\n            '-%[BY]'\n        ],\n        year: [\n            '%Y',\n            '%Y',\n            '-%Y'\n        ]\n    }\n    /// smoothed = false, // enable this for navigator series only\n};\n/**\n * Extends common options\n * @private\n */\nconst seriesSpecific = {\n    line: {},\n    spline: {},\n    area: {},\n    areaspline: {},\n    arearange: {},\n    column: {\n        groupPixelWidth: 10\n    },\n    columnrange: {\n        groupPixelWidth: 10\n    },\n    candlestick: {\n        groupPixelWidth: 10\n    },\n    ohlc: {\n        groupPixelWidth: 5\n    },\n    hlc: {\n        groupPixelWidth: 5\n        // Move to HeikinAshiSeries.ts after refactoring data grouping.\n    },\n    heikinashi: {\n        groupPixelWidth: 10\n    }\n};\n/**\n * Units are defined in a separate array to allow complete overriding in\n * case of a user option.\n * @private\n */\nconst units = [\n    [\n        'millisecond', // Unit name\n        [1, 2, 5, 10, 20, 25, 50, 100, 200, 500] // Allowed multiples\n    ], [\n        'second',\n        [1, 2, 5, 10, 15, 30]\n    ], [\n        'minute',\n        [1, 2, 5, 10, 15, 30]\n    ], [\n        'hour',\n        [1, 2, 3, 4, 6, 8, 12]\n    ], [\n        'day',\n        [1]\n    ], [\n        'week',\n        [1]\n    ], [\n        'month',\n        [1, 3, 6]\n    ], [\n        'year',\n        null\n    ]\n];\n/* *\n *\n *  Default Export\n *\n * */\nconst DataGroupingDefaults = {\n    common,\n    seriesSpecific,\n    units\n};\n/* harmony default export */ const DataGrouping_DataGroupingDefaults = (DataGroupingDefaults);\n\n;// ./code/es-modules/Extensions/DataGrouping/DataGroupingAxisComposition.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { addEvent, extend: DataGroupingAxisComposition_extend, merge, pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Variables\n *\n * */\nlet AxisConstructor;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Check the groupPixelWidth and apply the grouping if needed.\n * Fired only after processing the data.\n *\n * @product highstock\n *\n * @function Highcharts.Axis#applyGrouping\n */\nfunction applyGrouping(e) {\n    const axis = this, series = axis.series;\n    // Reset the groupPixelWidth for all series, #17141.\n    series.forEach(function (series) {\n        series.groupPixelWidth = void 0; // #2110\n    });\n    series.forEach(function (series) {\n        series.groupPixelWidth = (axis.getGroupPixelWidth &&\n            axis.getGroupPixelWidth());\n        if (series.groupPixelWidth) {\n            series.hasProcessed = true; // #2692\n        }\n        // Fire independing on series.groupPixelWidth to always set a proper\n        // dataGrouping state, (#16238)\n        series.applyGrouping(!!e.hasExtremesChanged);\n    });\n}\n/**\n * @private\n */\nfunction compose(AxisClass) {\n    AxisConstructor = AxisClass;\n    const axisProto = AxisClass.prototype;\n    if (!axisProto.applyGrouping) {\n        addEvent(AxisClass, 'afterSetScale', onAfterSetScale);\n        // When all series are processed, calculate the group pixel width and\n        // then if this value is different than zero apply groupings.\n        addEvent(AxisClass, 'postProcessData', applyGrouping);\n        DataGroupingAxisComposition_extend(axisProto, {\n            applyGrouping,\n            getGroupPixelWidth,\n            setDataGrouping\n        });\n    }\n}\n/**\n * Get the data grouping pixel width based on the greatest defined individual\n * width of the axis' series, and if whether one of the axes need grouping.\n * @private\n */\nfunction getGroupPixelWidth() {\n    const series = this.series;\n    let i = series.length, groupPixelWidth = 0, doGrouping = false, dataLength, dgOptions;\n    // If one of the series needs grouping, apply it to all (#1634)\n    while (i--) {\n        dgOptions = series[i].options.dataGrouping;\n        if (dgOptions) { // #2692\n            // If multiple series are compared on the same x axis, give them the\n            // same group pixel width (#334)\n            groupPixelWidth = Math.max(groupPixelWidth, \n            // Fallback to commonOptions (#9693)\n            pick(dgOptions.groupPixelWidth, DataGrouping_DataGroupingDefaults.common.groupPixelWidth));\n            dataLength = (series[i].dataTable.modified ||\n                series[i].dataTable).rowCount;\n            // Execute grouping if the amount of points is greater than the\n            // limit defined in groupPixelWidth\n            if (series[i].groupPixelWidth ||\n                (dataLength >\n                    (this.chart.plotSizeX / groupPixelWidth)) ||\n                (dataLength && dgOptions.forced)) {\n                doGrouping = true;\n            }\n        }\n    }\n    return doGrouping ? groupPixelWidth : 0;\n}\n/**\n * When resetting the scale reset the hasProcessed flag to avoid taking\n * previous data grouping of neighbour series into account when determining\n * group pixel width (#2692).\n * @private\n */\nfunction onAfterSetScale() {\n    this.series.forEach(function (series) {\n        series.hasProcessed = false;\n    });\n}\n/**\n * Highcharts Stock only. Force data grouping on all the axis' series.\n *\n * @product highstock\n *\n * @function Highcharts.Axis#setDataGrouping\n *\n * @param {boolean|Highcharts.DataGroupingOptionsObject} [dataGrouping]\n *        A `dataGrouping` configuration. Use `false` to disable data grouping\n *        dynamically.\n *\n * @param {boolean} [redraw=true]\n *        Whether to redraw the chart or wait for a later call to\n *        {@link Chart#redraw}.\n */\nfunction setDataGrouping(dataGrouping, redraw) {\n    const axis = this;\n    let i;\n    redraw = pick(redraw, true);\n    if (!dataGrouping) {\n        dataGrouping = {\n            forced: false,\n            units: null\n        };\n    }\n    // Axis is instantiated, update all series\n    if (this instanceof AxisConstructor) {\n        i = this.series.length;\n        while (i--) {\n            this.series[i].update({\n                dataGrouping: dataGrouping\n            }, false);\n        }\n        // Axis not yet instantiated, alter series options\n    }\n    else {\n        this.chart.options.series.forEach(function (seriesOptions) {\n            // Merging dataGrouping options with already defined options #16759\n            seriesOptions.dataGrouping = typeof dataGrouping === 'boolean' ?\n                dataGrouping :\n                merge(dataGrouping, seriesOptions.dataGrouping);\n        });\n    }\n    // Clear ordinal slope, so we won't accidentally use the old one (#7827)\n    if (axis.ordinal) {\n        axis.ordinal.slope = void 0;\n    }\n    if (redraw) {\n        this.chart.redraw();\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst DataGroupingAxisComposition = {\n    compose\n};\n/* harmony default export */ const DataGrouping_DataGroupingAxisComposition = (DataGroupingAxisComposition);\n\n;// ./code/es-modules/Data/ColumnUtils.js\n/* *\n *\n *  (c) 2020-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n/**\n * Utility functions for columns that can be either arrays or typed arrays.\n * @private\n */\nvar ColumnUtils;\n(function (ColumnUtils) {\n    /* *\n    *\n    *  Declarations\n    *\n    * */\n    /* *\n    *\n    * Functions\n    *\n    * */\n    /**\n     * Sets the length of the column array.\n     *\n     * @param {DataTable.Column} column\n     * Column to be modified.\n     *\n     * @param {number} length\n     * New length of the column.\n     *\n     * @param {boolean} asSubarray\n     * If column is a typed array, return a subarray instead of a new array. It\n     * is faster `O(1)`, but the entire buffer will be kept in memory until all\n     * views to it are destroyed. Default is `false`.\n     *\n     * @return {DataTable.Column}\n     * Modified column.\n     *\n     * @private\n     */\n    function setLength(column, length, asSubarray) {\n        if (Array.isArray(column)) {\n            column.length = length;\n            return column;\n        }\n        return column[asSubarray ? 'subarray' : 'slice'](0, length);\n    }\n    ColumnUtils.setLength = setLength;\n    /**\n     * Splices a column array.\n     *\n     * @param {DataTable.Column} column\n     * Column to be modified.\n     *\n     * @param {number} start\n     * Index at which to start changing the array.\n     *\n     * @param {number} deleteCount\n     * An integer indicating the number of old array elements to remove.\n     *\n     * @param {boolean} removedAsSubarray\n     * If column is a typed array, return a subarray instead of a new array. It\n     * is faster `O(1)`, but the entire buffer will be kept in memory until all\n     * views to it are destroyed. Default is `true`.\n     *\n     * @param {Array<number>|TypedArray} items\n     * The elements to add to the array, beginning at the start index. If you\n     * don't specify any elements, `splice()` will only remove elements from the\n     * array.\n     *\n     * @return {SpliceResult}\n     * Object containing removed elements and the modified column.\n     *\n     * @private\n     */\n    function splice(column, start, deleteCount, removedAsSubarray, items = []) {\n        if (Array.isArray(column)) {\n            if (!Array.isArray(items)) {\n                items = Array.from(items);\n            }\n            return {\n                removed: column.splice(start, deleteCount, ...items),\n                array: column\n            };\n        }\n        const Constructor = Object.getPrototypeOf(column)\n            .constructor;\n        const removed = column[removedAsSubarray ? 'subarray' : 'slice'](start, start + deleteCount);\n        const newLength = column.length - deleteCount + items.length;\n        const result = new Constructor(newLength);\n        result.set(column.subarray(0, start), 0);\n        result.set(items, start);\n        result.set(column.subarray(start + deleteCount), start + items.length);\n        return {\n            removed: removed,\n            array: result\n        };\n    }\n    ColumnUtils.splice = splice;\n})(ColumnUtils || (ColumnUtils = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Data_ColumnUtils = (ColumnUtils);\n\n;// ./code/es-modules/Data/DataTableCore.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Gøran Slettemark\n *  - Torstein Hønsi\n *\n * */\n\n\nconst { setLength, splice } = Data_ColumnUtils;\n\nconst { fireEvent, objectEach, uniqueKey } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * Class to manage columns and rows in a table structure. It provides methods\n * to add, remove, and manipulate columns and rows, as well as to retrieve data\n * from specific cells.\n *\n * @class\n * @name Highcharts.DataTable\n *\n * @param {Highcharts.DataTableOptions} [options]\n * Options to initialize the new DataTable instance.\n */\nclass DataTableCore {\n    /**\n     * Constructs an instance of the DataTable class.\n     *\n     * @example\n     * const dataTable = new Highcharts.DataTableCore({\n     *   columns: {\n     *     year: [2020, 2021, 2022, 2023],\n     *     cost: [11, 13, 12, 14],\n     *     revenue: [12, 15, 14, 18]\n     *   }\n     * });\n\n     *\n     * @param {Highcharts.DataTableOptions} [options]\n     * Options to initialize the new DataTable instance.\n     */\n    constructor(options = {}) {\n        /**\n         * Whether the ID was automatic generated or given in the constructor.\n         *\n         * @name Highcharts.DataTable#autoId\n         * @type {boolean}\n         */\n        this.autoId = !options.id;\n        this.columns = {};\n        /**\n         * ID of the table for identification purposes.\n         *\n         * @name Highcharts.DataTable#id\n         * @type {string}\n         */\n        this.id = (options.id || uniqueKey());\n        this.modified = this;\n        this.rowCount = 0;\n        this.versionTag = uniqueKey();\n        let rowCount = 0;\n        objectEach(options.columns || {}, (column, columnName) => {\n            this.columns[columnName] = column.slice();\n            rowCount = Math.max(rowCount, column.length);\n        });\n        this.applyRowCount(rowCount);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Applies a row count to the table by setting the `rowCount` property and\n     * adjusting the length of all columns.\n     *\n     * @private\n     * @param {number} rowCount The new row count.\n     */\n    applyRowCount(rowCount) {\n        this.rowCount = rowCount;\n        objectEach(this.columns, (column, columnName) => {\n            if (column.length !== rowCount) {\n                this.columns[columnName] = setLength(column, rowCount);\n            }\n        });\n    }\n    /**\n     * Delete rows. Simplified version of the full\n     * `DataTable.deleteRows` method.\n     *\n     * @param {number} rowIndex\n     * The start row index\n     *\n     * @param {number} [rowCount=1]\n     * The number of rows to delete\n     *\n     * @return {void}\n     *\n     * @emits #afterDeleteRows\n     */\n    deleteRows(rowIndex, rowCount = 1) {\n        if (rowCount > 0 && rowIndex < this.rowCount) {\n            let length = 0;\n            objectEach(this.columns, (column, columnName) => {\n                this.columns[columnName] =\n                    splice(column, rowIndex, rowCount).array;\n                length = column.length;\n            });\n            this.rowCount = length;\n        }\n        fireEvent(this, 'afterDeleteRows', { rowIndex, rowCount });\n        this.versionTag = uniqueKey();\n    }\n    /**\n     * Fetches the given column by the canonical column name. Simplified version\n     * of the full `DataTable.getRow` method, always returning by reference.\n     *\n     * @param {string} columnName\n     * Name of the column to get.\n     *\n     * @return {Highcharts.DataTableColumn|undefined}\n     * A copy of the column, or `undefined` if not found.\n     */\n    getColumn(columnName, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    asReference) {\n        return this.columns[columnName];\n    }\n    /**\n     * Retrieves all or the given columns. Simplified version of the full\n     * `DataTable.getColumns` method, always returning by reference.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names to retrieve.\n     *\n     * @return {Highcharts.DataTableColumnCollection}\n     * Collection of columns. If a requested column was not found, it is\n     * `undefined`.\n     */\n    getColumns(columnNames, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    asReference) {\n        return (columnNames || Object.keys(this.columns)).reduce((columns, columnName) => {\n            columns[columnName] = this.columns[columnName];\n            return columns;\n        }, {});\n    }\n    /**\n     * Retrieves the row at a given index.\n     *\n     * @param {number} rowIndex\n     * Row index to retrieve. First row has index 0.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names to retrieve.\n     *\n     * @return {Record<string, number|string|undefined>|undefined}\n     * Returns the row values, or `undefined` if not found.\n     */\n    getRow(rowIndex, columnNames) {\n        return (columnNames || Object.keys(this.columns)).map((key) => this.columns[key]?.[rowIndex]);\n    }\n    /**\n     * Sets cell values for a column. Will insert a new column, if not found.\n     *\n     * @param {string} columnName\n     * Column name to set.\n     *\n     * @param {Highcharts.DataTableColumn} [column]\n     * Values to set in the column.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to change. (Default: 0)\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setColumns\n     * @emits #afterSetColumns\n     */\n    setColumn(columnName, column = [], rowIndex = 0, eventDetail) {\n        this.setColumns({ [columnName]: column }, rowIndex, eventDetail);\n    }\n    /**\n     * Sets cell values for multiple columns. Will insert new columns, if not\n     * found. Simplified version of the full `DataTableCore.setColumns`, limited\n     * to full replacement of the columns (undefined `rowIndex`).\n     *\n     * @param {Highcharts.DataTableColumnCollection} columns\n     * Columns as a collection, where the keys are the column names.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to change. Ignored in the `DataTableCore`, as it\n     * always replaces the full column.\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setColumns\n     * @emits #afterSetColumns\n     */\n    setColumns(columns, rowIndex, eventDetail) {\n        let rowCount = this.rowCount;\n        objectEach(columns, (column, columnName) => {\n            this.columns[columnName] = column.slice();\n            rowCount = column.length;\n        });\n        this.applyRowCount(rowCount);\n        if (!eventDetail?.silent) {\n            fireEvent(this, 'afterSetColumns');\n            this.versionTag = uniqueKey();\n        }\n    }\n    /**\n     * Sets cell values of a row. Will insert a new row if no index was\n     * provided, or if the index is higher than the total number of table rows.\n     * A simplified version of the full `DateTable.setRow`, limited to objects.\n     *\n     * @param {Record<string, number|string|undefined>} row\n     * Cell values to set.\n     *\n     * @param {number} [rowIndex]\n     * Index of the row to set. Leave `undefined` to add as a new row.\n     *\n     * @param {boolean} [insert]\n     * Whether to insert the row at the given index, or to overwrite the row.\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #afterSetRows\n     */\n    setRow(row, rowIndex = this.rowCount, insert, eventDetail) {\n        const { columns } = this, indexRowCount = insert ? this.rowCount + 1 : rowIndex + 1;\n        objectEach(row, (cellValue, columnName) => {\n            let column = columns[columnName] ||\n                eventDetail?.addColumns !== false && new Array(indexRowCount);\n            if (column) {\n                if (insert) {\n                    column = splice(column, rowIndex, 0, true, [cellValue]).array;\n                }\n                else {\n                    column[rowIndex] = cellValue;\n                }\n                columns[columnName] = column;\n            }\n        });\n        if (indexRowCount > this.rowCount) {\n            this.applyRowCount(indexRowCount);\n        }\n        if (!eventDetail?.silent) {\n            fireEvent(this, 'afterSetRows');\n            this.versionTag = uniqueKey();\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Data_DataTableCore = (DataTableCore);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * A typed array.\n * @typedef {Int8Array|Uint8Array|Uint8ClampedArray|Int16Array|Uint16Array|Int32Array|Uint32Array|Float32Array|Float64Array} Highcharts.TypedArray\n * //**\n * A column of values in a data table.\n * @typedef {Array<boolean|null|number|string|undefined>|Highcharts.TypedArray} Highcharts.DataTableColumn\n */ /**\n* A collection of data table columns defined by a object where the key is the\n* column name and the value is an array of the column values.\n* @typedef {Record<string, Highcharts.DataTableColumn>} Highcharts.DataTableColumnCollection\n*/\n/**\n * Options for the `DataTable` or `DataTableCore` classes.\n * @interface Highcharts.DataTableOptions\n */ /**\n* The column options for the data table. The columns are defined by an object\n* where the key is the column ID and the value is an array of the column\n* values.\n*\n* @name Highcharts.DataTableOptions.columns\n* @type {Highcharts.DataTableColumnCollection|undefined}\n*/ /**\n* Custom ID to identify the new DataTable instance.\n*\n* @name Highcharts.DataTableOptions.id\n* @type {string|undefined}\n*/\n(''); // Keeps doclets above in JS file\n\n;// ./code/es-modules/Core/Axis/DateTimeAxis.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { addEvent: DateTimeAxis_addEvent, getMagnitude, normalizeTickInterval, timeUnits } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\n/* eslint-disable valid-jsdoc */\nvar DateTimeAxis;\n(function (DateTimeAxis) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Extends axis class with date and time support.\n     * @private\n     */\n    function compose(AxisClass) {\n        if (!AxisClass.keepProps.includes('dateTime')) {\n            AxisClass.keepProps.push('dateTime');\n            const axisProto = AxisClass.prototype;\n            axisProto.getTimeTicks = getTimeTicks;\n            DateTimeAxis_addEvent(AxisClass, 'afterSetType', onAfterSetType);\n        }\n        return AxisClass;\n    }\n    DateTimeAxis.compose = compose;\n    /**\n     * Set the tick positions to a time unit that makes sense, for example\n     * on the first of each month or on every Monday. Return an array with\n     * the time positions. Used in datetime axes as well as for grouping\n     * data on a datetime axis.\n     *\n     * @private\n     * @function Highcharts.Axis#getTimeTicks\n     * @param {Highcharts.TimeNormalizeObject} normalizedInterval\n     * The interval in axis values (ms) and the count.\n     * @param {number} min\n     * The minimum in axis values.\n     * @param {number} max\n     * The maximum in axis values.\n     */\n    function getTimeTicks() {\n        return this.chart.time.getTimeTicks.apply(this.chart.time, arguments);\n    }\n    /**\n     * @private\n     */\n    function onAfterSetType() {\n        if (this.type !== 'datetime') {\n            this.dateTime = void 0;\n            return;\n        }\n        if (!this.dateTime) {\n            this.dateTime = new Additions(this);\n        }\n    }\n    /* *\n     *\n     *  Classes\n     *\n     * */\n    class Additions {\n        /* *\n         *\n         *  Constructors\n         *\n         * */\n        constructor(axis) {\n            this.axis = axis;\n        }\n        /* *\n         *\n         *  Functions\n         *\n         * */\n        /**\n         * Get a normalized tick interval for dates. Returns a configuration\n         * object with unit range (interval), count and name. Used to prepare\n         * data for `getTimeTicks`. Previously this logic was part of\n         * getTimeTicks, but as `getTimeTicks` now runs of segments in stock\n         * charts, the normalizing logic was extracted in order to prevent it\n         * for running over again for each segment having the same interval.\n         * #662, #697.\n         * @private\n         */\n        normalizeTimeTickInterval(tickInterval, unitsOption) {\n            const units = (unitsOption || [[\n                    // Unit name\n                    'millisecond',\n                    // Allowed multiples\n                    [1, 2, 5, 10, 20, 25, 50, 100, 200, 500]\n                ], [\n                    'second',\n                    [1, 2, 5, 10, 15, 30]\n                ], [\n                    'minute',\n                    [1, 2, 5, 10, 15, 30]\n                ], [\n                    'hour',\n                    [1, 2, 3, 4, 6, 8, 12]\n                ], [\n                    'day',\n                    [1, 2]\n                ], [\n                    'week',\n                    [1, 2]\n                ], [\n                    'month',\n                    [1, 2, 3, 4, 6]\n                ], [\n                    'year',\n                    null\n                ]]);\n            let unit = units[units.length - 1], // Default unit is years\n            interval = timeUnits[unit[0]], multiples = unit[1], i;\n            // Loop through the units to find the one that best fits the\n            // tickInterval\n            for (i = 0; i < units.length; i++) {\n                unit = units[i];\n                interval = timeUnits[unit[0]];\n                multiples = unit[1];\n                if (units[i + 1]) {\n                    // `lessThan` is in the middle between the highest multiple\n                    // and the next unit.\n                    const lessThan = (interval *\n                        multiples[multiples.length - 1] +\n                        timeUnits[units[i + 1][0]]) / 2;\n                    // Break and keep the current unit\n                    if (tickInterval <= lessThan) {\n                        break;\n                    }\n                }\n            }\n            // Prevent 2.5 years intervals, though 25, 250 etc. are allowed\n            if (interval === timeUnits.year && tickInterval < 5 * interval) {\n                multiples = [1, 2, 5];\n            }\n            // Get the count\n            const count = normalizeTickInterval(tickInterval / interval, multiples, unit[0] === 'year' ? // #1913, #2360\n                Math.max(getMagnitude(tickInterval / interval), 1) :\n                1);\n            return {\n                unitRange: interval,\n                count: count,\n                unitName: unit[0]\n            };\n        }\n        /**\n         * Get the best date format for a specific X value based on the closest\n         * point range on the axis.\n         *\n         * @private\n         */\n        getXDateFormat(x, dateTimeLabelFormats) {\n            const { axis } = this, time = axis.chart.time;\n            return axis.closestPointRange ?\n                time.getDateFormat(axis.closestPointRange, x, axis.options.startOfWeek, dateTimeLabelFormats) ||\n                    // #2546, 2581\n                    time.resolveDTLFormat(dateTimeLabelFormats.year).main :\n                time.resolveDTLFormat(dateTimeLabelFormats.day).main;\n        }\n    }\n    DateTimeAxis.Additions = Additions;\n})(DateTimeAxis || (DateTimeAxis = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Axis_DateTimeAxis = (DateTimeAxis);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Extensions/DataGrouping/DataGroupingSeriesComposition.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\n\n\nconst { series: { prototype: seriesProto } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { addEvent: DataGroupingSeriesComposition_addEvent, defined, error, extend: DataGroupingSeriesComposition_extend, isNumber: DataGroupingSeriesComposition_isNumber, merge: DataGroupingSeriesComposition_merge, pick: DataGroupingSeriesComposition_pick, splat } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\nconst baseGeneratePoints = seriesProto.generatePoints;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction adjustExtremes(xAxis, groupedXData) {\n    // Make sure the X axis extends to show the first group (#2533)\n    // But only for visible series (#5493, #6393)\n    if (defined(groupedXData[0]) &&\n        DataGroupingSeriesComposition_isNumber(xAxis.min) &&\n        DataGroupingSeriesComposition_isNumber(xAxis.dataMin) &&\n        groupedXData[0] < xAxis.min) {\n        if ((!defined(xAxis.options.min) &&\n            xAxis.min <= xAxis.dataMin) ||\n            xAxis.min === xAxis.dataMin) {\n            xAxis.min = Math.min(groupedXData[0], xAxis.min);\n        }\n        xAxis.dataMin = Math.min(groupedXData[0], xAxis.dataMin);\n    }\n    // When the last anchor set, change the extremes that\n    // the last point is visible (#12455).\n    if (defined(groupedXData[groupedXData.length - 1]) &&\n        DataGroupingSeriesComposition_isNumber(xAxis.max) &&\n        DataGroupingSeriesComposition_isNumber(xAxis.dataMax) &&\n        groupedXData[groupedXData.length - 1] > xAxis.max) {\n        if ((!defined(xAxis.options.max) &&\n            DataGroupingSeriesComposition_isNumber(xAxis.dataMax) &&\n            xAxis.max >= xAxis.dataMax) || xAxis.max === xAxis.dataMax) {\n            xAxis.max = Math.max(groupedXData[groupedXData.length - 1], xAxis.max);\n        }\n        xAxis.dataMax = Math.max(groupedXData[groupedXData.length - 1], xAxis.dataMax);\n    }\n}\n/**\n * @private\n */\nfunction anchorPoints(series, groupedXData, xMax) {\n    const options = series.options, dataGroupingOptions = options.dataGrouping, totalRange = (series.currentDataGrouping && series.currentDataGrouping.gapSize), xData = series.getColumn('x');\n    if (!(dataGroupingOptions &&\n        xData.length &&\n        totalRange &&\n        series.groupMap)) {\n        return;\n    }\n    const groupedDataLastIndex = groupedXData.length - 1, anchor = dataGroupingOptions.anchor, firstAnchor = dataGroupingOptions.firstAnchor, lastAnchor = dataGroupingOptions.lastAnchor;\n    let anchorIndexIterator = groupedXData.length - 1, anchorFirstIndex = 0;\n    // Change the first point position, but only when it is\n    // the first point in the data set not in the current zoom.\n    if (firstAnchor && xData[0] >= groupedXData[0]) {\n        anchorFirstIndex++;\n        const groupStart = series.groupMap[0].start, groupLength = series.groupMap[0].length;\n        let firstGroupEnd;\n        if (DataGroupingSeriesComposition_isNumber(groupStart) && DataGroupingSeriesComposition_isNumber(groupLength)) {\n            firstGroupEnd = groupStart + (groupLength - 1);\n        }\n        groupedXData[0] = {\n            start: groupedXData[0],\n            middle: groupedXData[0] + 0.5 * totalRange,\n            end: groupedXData[0] + totalRange,\n            firstPoint: xData[0],\n            lastPoint: firstGroupEnd && xData[firstGroupEnd]\n        }[firstAnchor];\n    }\n    // Change the last point position but only when it is\n    // the last point in the data set not in the current zoom,\n    // or if it is not the 1st point simultaneously.\n    if (groupedDataLastIndex > 0 &&\n        lastAnchor &&\n        totalRange &&\n        groupedXData[groupedDataLastIndex] >= xMax - totalRange) {\n        anchorIndexIterator--;\n        const lastGroupStart = series.groupMap[series.groupMap.length - 1].start;\n        groupedXData[groupedDataLastIndex] = {\n            start: groupedXData[groupedDataLastIndex],\n            middle: groupedXData[groupedDataLastIndex] + 0.5 * totalRange,\n            end: groupedXData[groupedDataLastIndex] + totalRange,\n            firstPoint: lastGroupStart && xData[lastGroupStart],\n            lastPoint: xData[xData.length - 1]\n        }[lastAnchor];\n    }\n    if (anchor && anchor !== 'start') {\n        const shiftInterval = (totalRange *\n            { middle: 0.5, end: 1 }[anchor]);\n        // Anchor the rest of the points apart from the ones, that were\n        // previously moved.\n        while (anchorIndexIterator >= anchorFirstIndex) {\n            groupedXData[anchorIndexIterator] += shiftInterval;\n            anchorIndexIterator--;\n        }\n    }\n}\n/**\n * For the processed data, calculate the grouped data if needed.\n *\n * @private\n * @function Highcharts.Series#applyGrouping\n */\nfunction DataGroupingSeriesComposition_applyGrouping(hasExtremesChanged) {\n    const series = this, chart = series.chart, options = series.options, dataGroupingOptions = options.dataGrouping, groupingEnabled = series.allowDG !== false && dataGroupingOptions &&\n        DataGroupingSeriesComposition_pick(dataGroupingOptions.enabled, chart.options.isStock), reserveSpace = series.reserveSpace(), lastDataGrouping = this.currentDataGrouping;\n    let currentDataGrouping, croppedData, revertRequireSorting = false;\n    // Data needs to be sorted for dataGrouping\n    if (groupingEnabled && !series.requireSorting) {\n        series.requireSorting = revertRequireSorting = true;\n    }\n    // Skip if skipDataGrouping method returns false or if grouping is disabled\n    // (in that order).\n    const skip = skipDataGrouping(series, hasExtremesChanged) === false || !groupingEnabled;\n    // Revert original requireSorting value if changed\n    if (revertRequireSorting) {\n        series.requireSorting = false;\n    }\n    if (skip) {\n        return;\n    }\n    series.destroyGroupedData();\n    const table = dataGroupingOptions.groupAll ?\n        series.dataTable :\n        series.dataTable.modified || series.dataTable, processedXData = series.getColumn('x', !dataGroupingOptions.groupAll), xData = processedXData, plotSizeX = chart.plotSizeX, xAxis = series.xAxis, extremes = xAxis.getExtremes(), ordinal = xAxis.options.ordinal, groupPixelWidth = series.groupPixelWidth;\n    let i, hasGroupedData;\n    // Execute grouping if the amount of points is greater than the limit\n    // defined in groupPixelWidth\n    if (groupPixelWidth &&\n        xData &&\n        table.rowCount &&\n        plotSizeX &&\n        DataGroupingSeriesComposition_isNumber(extremes.min)) {\n        hasGroupedData = true;\n        // Force recreation of point instances in series.translate, #5699\n        series.isDirty = true;\n        series.points = null; // #6709\n        const xMin = extremes.min, xMax = extremes.max, groupIntervalFactor = (ordinal &&\n            xAxis.ordinal &&\n            xAxis.ordinal.getGroupIntervalFactor(xMin, xMax, series)) || 1, interval = (groupPixelWidth * (xMax - xMin) / plotSizeX) *\n            groupIntervalFactor, groupPositions = xAxis.getTimeTicks(Axis_DateTimeAxis.Additions.prototype.normalizeTimeTickInterval(interval, dataGroupingOptions.units ||\n            DataGrouping_DataGroupingDefaults.units), \n        // Processed data may extend beyond axis (#4907)\n        Math.min(xMin, xData[0]), Math.max(xMax, xData[xData.length - 1]), xAxis.options.startOfWeek, processedXData, series.closestPointRange), groupedData = seriesProto.groupData.apply(series, [\n            table,\n            groupPositions,\n            dataGroupingOptions.approximation\n        ]);\n        let modified = groupedData.modified, groupedXData = modified.getColumn('x', true), gapSize = 0;\n        // The smoothed option is deprecated, instead, there is a fallback\n        // to the new anchoring mechanism. #12455.\n        if (dataGroupingOptions?.smoothed &&\n            modified.rowCount) {\n            dataGroupingOptions.firstAnchor = 'firstPoint';\n            dataGroupingOptions.anchor = 'middle';\n            dataGroupingOptions.lastAnchor = 'lastPoint';\n            error(32, false, chart, {\n                'dataGrouping.smoothed': 'use dataGrouping.anchor'\n            });\n        }\n        // Record what data grouping values were used\n        for (i = 1; i < groupPositions.length; i++) {\n            // The grouped gapSize needs to be the largest distance between\n            // the group to capture varying group sizes like months or DST\n            // crossing (#10000). Also check that the gap is not at the\n            // start of a segment.\n            if (!groupPositions.info.segmentStarts ||\n                groupPositions.info.segmentStarts.indexOf(i) === -1) {\n                gapSize = Math.max(groupPositions[i] - groupPositions[i - 1], gapSize);\n            }\n        }\n        currentDataGrouping = groupPositions.info;\n        currentDataGrouping.gapSize = gapSize;\n        series.closestPointRange = groupPositions.info.totalRange;\n        series.groupMap = groupedData.groupMap;\n        series.currentDataGrouping = currentDataGrouping;\n        anchorPoints(series, groupedXData || [], xMax);\n        if (reserveSpace && groupedXData) {\n            adjustExtremes(xAxis, groupedXData);\n        }\n        // We calculated all group positions but we should render only the ones\n        // within the visible range\n        if (dataGroupingOptions.groupAll) {\n            // Keep the reference to all grouped points for further calculation,\n            // used in Heikin-Ashi and hollow candlestick series.\n            series.allGroupedTable = modified;\n            croppedData = series.cropData(modified, xAxis.min || 0, xAxis.max || 0);\n            modified = croppedData.modified;\n            groupedXData = modified.getColumn('x');\n            series.cropStart = croppedData.start; // #15005\n        }\n        // Set the modified table\n        series.dataTable.modified = modified;\n    }\n    else {\n        series.groupMap = void 0;\n        series.currentDataGrouping = void 0;\n    }\n    series.hasGroupedData = hasGroupedData;\n    series.preventGraphAnimation =\n        (lastDataGrouping && lastDataGrouping.totalRange) !==\n            (currentDataGrouping && currentDataGrouping.totalRange);\n}\n/**\n * @private\n */\nfunction DataGroupingSeriesComposition_compose(SeriesClass) {\n    const seriesProto = SeriesClass.prototype;\n    if (!seriesProto.applyGrouping) {\n        const PointClass = SeriesClass.prototype.pointClass;\n        // Override point prototype to throw a warning when trying to update\n        // grouped points.\n        DataGroupingSeriesComposition_addEvent(PointClass, 'update', function () {\n            if (this.dataGroup) {\n                error(24, false, this.series.chart);\n                return false;\n            }\n        });\n        DataGroupingSeriesComposition_addEvent(SeriesClass, 'afterSetOptions', onAfterSetOptions);\n        DataGroupingSeriesComposition_addEvent(SeriesClass, 'destroy', destroyGroupedData);\n        DataGroupingSeriesComposition_extend(seriesProto, {\n            applyGrouping: DataGroupingSeriesComposition_applyGrouping,\n            destroyGroupedData,\n            generatePoints,\n            getDGApproximation,\n            groupData\n        });\n    }\n}\n/**\n * Destroy the grouped data points. #622, #740\n * @private\n */\nfunction destroyGroupedData() {\n    // Clear previous groups\n    if (this.groupedData) {\n        this.groupedData.forEach(function (point, i) {\n            if (point) {\n                this.groupedData[i] = point.destroy ?\n                    point.destroy() : null;\n            }\n        }, this);\n        // Clears all:\n        // - `this.groupedData`\n        // - `this.points`\n        // - `preserve` object in series.update()\n        this.groupedData.length = 0;\n        delete this.allGroupedTable;\n    }\n}\n/**\n * Override the generatePoints method by adding a reference to grouped data\n * @private\n */\nfunction generatePoints() {\n    baseGeneratePoints.apply(this);\n    // Record grouped data in order to let it be destroyed the next time\n    // processData runs\n    this.destroyGroupedData(); // #622\n    this.groupedData = this.hasGroupedData ? this.points : null;\n}\n/**\n * Set default approximations to the prototypes if present. Properties are\n * inherited down. Can be overridden for individual series types.\n * @private\n */\nfunction getDGApproximation() {\n    if (this.is('arearange')) {\n        return 'range';\n    }\n    if (this.is('ohlc')) {\n        return 'ohlc';\n    }\n    if (this.is('hlc')) {\n        return 'hlc';\n    }\n    if (\n    // #18974, default approximation for cumulative\n    // should be `sum` when `dataGrouping` is enabled\n    this.is('column') ||\n        this.options.cumulative) {\n        return 'sum';\n    }\n    return 'average';\n}\n/**\n * Highcharts Stock only. Takes parallel arrays of x and y data and groups the\n * data into intervals defined by groupPositions, a collection of starting x\n * values for each group.\n *\n * @product highstock\n *\n * @function Highcharts.Series#groupData\n * @param {Highcharts.DataTable} table\n *        The series data table.\n * @param {Array<number>} groupPositions\n *        Group positions.\n * @param {string|Function} [approximation]\n *        Approximation to use.\n * @return {Highcharts.DataGroupingResultObject}\n *         Mapped groups.\n */\nfunction groupData(table, groupPositions, approximation) {\n    const xData = table.getColumn('x', true) || [], yData = table.getColumn('y', true), series = this, data = series.data, dataOptions = series.options && series.options.data, groupedXData = [], modified = new Data_DataTableCore(), groupMap = [], dataLength = table.rowCount, \n    // When grouping the fake extended axis for panning, we don't need to\n    // consider y\n    handleYData = !!yData, values = [], pointArrayMap = series.pointArrayMap, pointArrayMapLength = pointArrayMap && pointArrayMap.length, extendedPointArrayMap = ['x'].concat(pointArrayMap || ['y']), \n    // Data columns to be applied to the modified data table at the end\n    valueColumns = (pointArrayMap || ['y']).map(() => []), groupAll = (this.options.dataGrouping &&\n        this.options.dataGrouping.groupAll);\n    let pointX, pointY, groupedY, pos = 0, start = 0;\n    const approximationFn = (typeof approximation === 'function' ?\n        approximation :\n        approximation && DataGrouping_ApproximationRegistry[approximation] ?\n            DataGrouping_ApproximationRegistry[approximation] :\n            DataGrouping_ApproximationRegistry[(series.getDGApproximation && series.getDGApproximation() ||\n                'average')]);\n    // Calculate values array size from pointArrayMap length\n    if (pointArrayMapLength) {\n        let len = pointArrayMap.length;\n        while (len--) {\n            values.push([]);\n        }\n    }\n    else {\n        values.push([]);\n    }\n    const valuesLen = pointArrayMapLength || 1;\n    for (let i = 0; i <= dataLength; i++) {\n        // Start with the first point within the X axis range (#2696)\n        if (xData[i] < groupPositions[0]) {\n            continue; // With next point\n        }\n        // When a new group is entered, summarize and initialize\n        // the previous group\n        while ((typeof groupPositions[pos + 1] !== 'undefined' &&\n            xData[i] >= groupPositions[pos + 1]) ||\n            i === dataLength) { // Get the last group\n            // get group x and y\n            pointX = groupPositions[pos];\n            series.dataGroupInfo = {\n                start: groupAll ? start : (series.cropStart + start),\n                length: values[0].length,\n                groupStart: pointX\n            };\n            groupedY = approximationFn.apply(series, values);\n            // By default, let options of the first grouped point be passed over\n            // to the grouped point. This allows preserving properties like\n            // `name` and `color` or custom properties. Implementers can\n            // override this from the approximation function, where they can\n            // write custom options to `this.dataGroupInfo.options`.\n            if (series.pointClass && !defined(series.dataGroupInfo.options)) {\n                // Convert numbers and arrays into objects\n                series.dataGroupInfo.options = DataGroupingSeriesComposition_merge(series.pointClass.prototype\n                    .optionsToObject.call({ series: series }, series.options.data[series.cropStart + start]));\n                // Make sure the raw data (x, y, open, high etc) is not copied\n                // over and overwriting approximated data.\n                extendedPointArrayMap.forEach(function (key) {\n                    delete series.dataGroupInfo.options[key];\n                });\n            }\n            // Push the grouped data\n            if (typeof groupedY !== 'undefined') {\n                groupedXData.push(pointX);\n                // Push the grouped values to the parallel columns\n                const groupedValuesArr = splat(groupedY);\n                for (let j = 0; j < groupedValuesArr.length; j++) {\n                    valueColumns[j].push(groupedValuesArr[j]);\n                }\n                groupMap.push(series.dataGroupInfo);\n            }\n            // Reset the aggregate arrays\n            start = i;\n            for (let j = 0; j < valuesLen; j++) {\n                values[j].length = 0; // Faster than values[j] = []\n                values[j].hasNulls = false;\n            }\n            // Advance on the group positions\n            pos += 1;\n            // Don't loop beyond the last group\n            if (i === dataLength) {\n                break;\n            }\n        }\n        // Break out\n        if (i === dataLength) {\n            break;\n        }\n        // For each raw data point, push it to an array that contains all values\n        // for this specific group\n        if (pointArrayMap) {\n            const index = groupAll ? i : series.cropStart + i, point = (data && data[index]) ||\n                series.pointClass.prototype.applyOptions.apply({\n                    series: series\n                }, [dataOptions[index]]);\n            let val;\n            for (let j = 0; j < pointArrayMapLength; j++) {\n                val = point[pointArrayMap[j]];\n                if (DataGroupingSeriesComposition_isNumber(val)) {\n                    values[j].push(val);\n                }\n                else if (val === null) {\n                    values[j].hasNulls = true;\n                }\n            }\n        }\n        else {\n            pointY = handleYData ? yData[i] : null;\n            if (DataGroupingSeriesComposition_isNumber(pointY)) {\n                values[0].push(pointY);\n            }\n            else if (pointY === null) {\n                values[0].hasNulls = true;\n            }\n        }\n    }\n    const columns = {\n        x: groupedXData\n    };\n    (pointArrayMap || ['y']).forEach((key, i) => {\n        columns[key] = valueColumns[i];\n    });\n    modified.setColumns(columns);\n    return {\n        groupMap,\n        modified\n    };\n}\n/**\n * Handle default options for data grouping. This must be set at runtime because\n * some series types are defined after this.\n * @private\n */\nfunction onAfterSetOptions(e) {\n    const options = e.options, type = this.type, plotOptions = this.chart.options.plotOptions, \n    // External series, for example technical indicators should also inherit\n    // commonOptions which are not available outside this module\n    baseOptions = (this.useCommonDataGrouping &&\n        DataGrouping_DataGroupingDefaults.common), seriesSpecific = DataGrouping_DataGroupingDefaults.seriesSpecific;\n    let defaultOptions = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defaultOptions.plotOptions[type].dataGrouping;\n    if (plotOptions && (seriesSpecific[type] || baseOptions)) { // #1284\n        const rangeSelector = this.chart.rangeSelector;\n        if (!defaultOptions) {\n            defaultOptions = DataGroupingSeriesComposition_merge(DataGrouping_DataGroupingDefaults.common, seriesSpecific[type]);\n        }\n        options.dataGrouping = DataGroupingSeriesComposition_merge(baseOptions, defaultOptions, plotOptions.series && plotOptions.series.dataGrouping, // #1228\n        // Set by the StockChart constructor:\n        plotOptions[type].dataGrouping, this.userOptions.dataGrouping, !options.isInternal &&\n            rangeSelector &&\n            DataGroupingSeriesComposition_isNumber(rangeSelector.selected) &&\n            rangeSelector.buttonOptions[rangeSelector.selected].dataGrouping);\n    }\n}\n/**\n * @private\n */\nfunction skipDataGrouping(series, force) {\n    return !(series.isCartesian &&\n        !series.isDirty &&\n        !series.xAxis.isDirty &&\n        !series.yAxis.isDirty &&\n        !force);\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst DataGroupingSeriesComposition = {\n    compose: DataGroupingSeriesComposition_compose,\n    groupData\n};\n/* harmony default export */ const DataGrouping_DataGroupingSeriesComposition = (DataGroupingSeriesComposition);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Templating\"],\"commonjs\":[\"highcharts\",\"Templating\"],\"commonjs2\":[\"highcharts\",\"Templating\"],\"root\":[\"Highcharts\",\"Templating\"]}\nvar highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_ = __webpack_require__(984);\nvar highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default = /*#__PURE__*/__webpack_require__.n(highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_);\n;// ./code/es-modules/Extensions/DataGrouping/DataGrouping.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\nconst { format } = (highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default());\n\nconst { composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent: DataGrouping_addEvent, extend: DataGrouping_extend, isNumber: DataGrouping_isNumber, pick: DataGrouping_pick, pushUnique } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction DataGrouping_compose(AxisClass, SeriesClass, TooltipClass) {\n    DataGrouping_DataGroupingAxisComposition.compose(AxisClass);\n    DataGrouping_DataGroupingSeriesComposition.compose(SeriesClass);\n    if (TooltipClass &&\n        pushUnique(composed, 'DataGrouping')) {\n        DataGrouping_addEvent(TooltipClass, 'headerFormatter', onTooltipHeaderFormatter);\n    }\n}\n/**\n * Extend the original method, make the tooltip's header reflect the grouped\n * range.\n * @private\n */\nfunction onTooltipHeaderFormatter(e) {\n    const chart = this.chart, time = chart.time, point = e.point, series = point.series, options = series.options, tooltipOptions = series.tooltipOptions, dataGroupingOptions = options.dataGrouping, xAxis = series.xAxis;\n    let xDateFormat = tooltipOptions.xDateFormat || '', xDateFormatEnd, currentDataGrouping, dateTimeLabelFormats, labelFormats, formattedKey, formatString = tooltipOptions[e.isFooter ? 'footerFormat' : 'headerFormat'];\n    // Apply only to grouped series\n    if (xAxis &&\n        xAxis.options.type === 'datetime' &&\n        dataGroupingOptions &&\n        DataGrouping_isNumber(point.key)) {\n        // Set variables\n        currentDataGrouping = series.currentDataGrouping;\n        dateTimeLabelFormats = dataGroupingOptions.dateTimeLabelFormats ||\n            // Fallback to commonOptions (#9693)\n            DataGrouping_DataGroupingDefaults.common.dateTimeLabelFormats;\n        // If we have grouped data, use the grouping information to get the\n        // right format\n        if (currentDataGrouping) {\n            labelFormats = dateTimeLabelFormats[currentDataGrouping.unitName];\n            if (currentDataGrouping.count === 1) {\n                xDateFormat = labelFormats[0];\n            }\n            else {\n                xDateFormat = labelFormats[1];\n                xDateFormatEnd = labelFormats[2];\n            }\n            // If not grouped, and we don't have set the xDateFormat option, get the\n            // best fit, so if the least distance between points is one minute, show\n            // it, but if the least distance is one day, skip hours and minutes etc.\n        }\n        else if (!xDateFormat && dateTimeLabelFormats && xAxis.dateTime) {\n            xDateFormat = xAxis.dateTime.getXDateFormat(point.x, tooltipOptions.dateTimeLabelFormats);\n        }\n        const groupStart = DataGrouping_pick(series.groupMap?.[point.index].groupStart, point.key), groupEnd = groupStart + (currentDataGrouping?.totalRange || 0) - 1;\n        formattedKey = time.dateFormat(xDateFormat, groupStart);\n        if (xDateFormatEnd) {\n            formattedKey += time.dateFormat(xDateFormatEnd, groupEnd);\n        }\n        // Replace default header style with class name\n        if (series.chart.styledMode) {\n            formatString = this.styledModeFormat(formatString);\n        }\n        // Return the replaced format\n        e.text = format(formatString, {\n            point: DataGrouping_extend(point, { key: formattedKey }),\n            series\n        }, chart);\n        e.preventDefault();\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst DataGroupingComposition = {\n    compose: DataGrouping_compose,\n    groupData: DataGrouping_DataGroupingSeriesComposition.groupData\n};\n/* harmony default export */ const DataGrouping = (DataGroupingComposition);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * @typedef {\"average\"|\"averages\"|\"open\"|\"high\"|\"low\"|\"close\"|\"sum\"} Highcharts.DataGroupingApproximationValue\n */\n/**\n * The position of the point inside the group.\n *\n * @typedef    {\"start\"|\"middle\"|\"end\"} Highcharts.DataGroupingAnchor\n */\n/**\n * The position of the first or last point in the series inside the group.\n *\n * @typedef    {\"start\"|\"middle\"|\"end\"|\"firstPoint\"|\"lastPoint\"} Highcharts.DataGroupingAnchorExtremes\n */\n/**\n * Highcharts Stock only.\n *\n * @product highstock\n * @interface Highcharts.DataGroupingInfoObject\n */ /**\n* @name Highcharts.DataGroupingInfoObject#length\n* @type {number}\n*/ /**\n* @name Highcharts.DataGroupingInfoObject#options\n* @type {Highcharts.SeriesOptionsType|undefined}\n*/ /**\n* @name Highcharts.DataGroupingInfoObject#start\n* @type {number}\n*/\n/**\n * Highcharts Stock only.\n *\n * @product highstock\n * @interface Highcharts.DataGroupingResultObject\n */ /**\n* @name Highcharts.DataGroupingResultObject#groupedXData\n* @type {Array<number>}\n*/ /**\n* @name Highcharts.DataGroupingResultObject#groupedYData\n* @type {Array<(number|null|undefined)>|Array<Array<(number|null|undefined)>>}\n*/ /**\n* @name Highcharts.DataGroupingResultObject#groupMap\n* @type {Array<DataGroupingInfoObject>}\n*/\n/**\n * Highcharts Stock only. If a point object is created by data\n * grouping, it doesn't reflect actual points in the raw\n * data. In this case, the `dataGroup` property holds\n * information that points back to the raw data.\n *\n * - `dataGroup.start` is the index of the first raw data\n *   point in the group.\n *\n * - `dataGroup.length` is the amount of points in the\n *   group.\n *\n * @sample stock/members/point-datagroup\n *         Click to inspect raw data points\n *\n * @product highstock\n *\n * @name Highcharts.Point#dataGroup\n * @type {Highcharts.DataGroupingInfoObject|undefined}\n */\n(''); // Detach doclets above\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Data grouping is the concept of sampling the data values into larger\n * blocks in order to ease readability and increase performance of the\n * JavaScript charts. Highcharts Stock by default applies data grouping when\n * the points become closer than a certain pixel value, determined by\n * the `groupPixelWidth` option.\n *\n * If data grouping is applied, the grouping information of grouped\n * points can be read from the [Point.dataGroup](\n * /class-reference/Highcharts.Point#dataGroup). If point options other than\n * the data itself are set, for example `name` or `color` or custom properties,\n * the grouping logic doesn't know how to group it. In this case the options of\n * the first point instance are copied over to the group point. This can be\n * altered through a custom `approximation` callback function.\n *\n * @declare   Highcharts.DataGroupingOptionsObject\n * @product   highstock\n * @requires  modules/stock\n * @apioption plotOptions.series.dataGrouping\n */\n/**\n * Specifies how the points should be located on the X axis inside the group.\n * Points that are extremes can be set separately. Available options:\n *\n * - `start` places the point at the beginning of the group\n * (e.g. range 00:00:00 - 23:59:59 -> 00:00:00)\n *\n * - `middle` places the point in the middle of the group\n * (e.g. range 00:00:00 - 23:59:59 -> 12:00:00)\n *\n * - `end` places the point at the end of the group\n * (e.g. range 00:00:00 - 23:59:59 -> 23:59:59)\n *\n * @sample {highstock} stock/plotoptions/series-datagrouping-anchor\n *         Changing the point x-coordinate inside the group.\n *\n * @see [dataGrouping.firstAnchor](#plotOptions.series.dataGrouping.firstAnchor)\n * @see [dataGrouping.lastAnchor](#plotOptions.series.dataGrouping.lastAnchor)\n *\n * @type       {Highcharts.DataGroupingAnchor}\n * @since 9.1.0\n * @default    start\n * @apioption  plotOptions.series.dataGrouping.anchor\n */\n/**\n * The method of approximation inside a group. When for example 30 days\n * are grouped into one month, this determines what value should represent\n * the group. Possible values are \"average\", \"averages\", \"open\", \"high\",\n * \"low\", \"close\" and \"sum\". For OHLC and candlestick series the approximation\n * is \"ohlc\" by default, which finds the open, high, low and close values\n * within all the grouped data. For ranges, the approximation is \"range\",\n * which finds the low and high values. For multi-dimensional data,\n * like ranges and OHLC, \"averages\" will compute the average for each\n * dimension.\n *\n * Custom aggregate methods can be added by assigning a callback function\n * as the approximation. This function takes a numeric array as the\n * argument and should return a single numeric value or `null`. Note\n * that the numeric array will never contain null values, only true\n * numbers. Instead, if null values are present in the raw data, the\n * numeric array will have an `.hasNulls` property set to `true`. For\n * single-value data sets the data is available in the first argument\n * of the callback function. For OHLC data sets, all the open values\n * are in the first argument, all high values in the second etc.\n *\n * Since v4.2.7, grouping meta data is available in the approximation\n * callback from `this.dataGroupInfo`. It can be used to extract information\n * from the raw data.\n *\n * Defaults to `average` for line-type series, `sum` for columns, `range`\n * for range series, `hlc` for HLC, and `ohlc` for OHLC and candlestick.\n *\n * @sample {highstock} stock/plotoptions/series-datagrouping-approximation\n *         Approximation callback with custom data\n * @sample {highstock} stock/plotoptions/series-datagrouping-simple-approximation\n *         Simple approximation demo\n *\n * @type       {Highcharts.DataGroupingApproximationValue|Function}\n * @apioption  plotOptions.series.dataGrouping.approximation\n */\n/**\n * Datetime formats for the header of the tooltip in a stock chart.\n * The format can vary within a chart depending on the currently selected\n * time range and the current data grouping.\n *\n * The default formats are:\n * ```js\n * {\n *     millisecond: [\n *         '%A, %e %b, %H:%M:%S.%L', '%A, %e %b, %H:%M:%S.%L', '-%H:%M:%S.%L'\n *     ],\n *     second: ['%A, %e %b, %H:%M:%S', '%A, %e %b, %H:%M:%S', '-%H:%M:%S'],\n *     minute: ['%A, %e %b, %H:%M', '%A, %e %b, %H:%M', '-%H:%M'],\n *     hour: ['%A, %e %b, %H:%M', '%A, %e %b, %H:%M', '-%H:%M'],\n *     day: ['%A, %e %b %Y', '%A, %e %b', '-%A, %e %b %Y'],\n *     week: ['%v %A, %e %b %Y', '%A, %e %b', '-%A, %e %b %Y'],\n *     month: ['%B %Y', '%B', '-%B %Y'],\n *     year: ['%Y', '%Y', '-%Y']\n * }\n * ```\n *\n * For each of these array definitions, the first item is the format\n * used when the active time span is one unit. For instance, if the\n * current data applies to one week, the first item of the week array\n * is used. The second and third items are used when the active time\n * span is more than two units. For instance, if the current data applies\n * to two weeks, the second and third item of the week array are used,\n *  and applied to the start and end date of the time span.\n *\n * @type      {Object}\n * @apioption plotOptions.series.dataGrouping.dateTimeLabelFormats\n */\n/**\n * Enable or disable data grouping.\n *\n * @type      {boolean}\n * @default   true\n * @apioption plotOptions.series.dataGrouping.enabled\n */\n/**\n * Specifies how the first grouped point is positioned on the xAxis.\n * If firstAnchor and/or lastAnchor are defined, then those options take\n * precedence over anchor for the first and/or last grouped points.\n * Available options:\n *\n * -`start` places the point at the beginning of the group\n * (e.g. range 00:00:00 - 23:59:59 -> 00:00:00)\n *\n * -`middle` places the point in the middle of the group\n * (e.g. range 00:00:00 - 23:59:59 -> 12:00:00)\n *\n * -`end` places the point at the end of the group\n * (e.g. range 00:00:00 - 23:59:59 -> 23:59:59)\n *\n * -`firstPoint` the first point in the group\n * (e.g. points at 00:13, 00:35, 00:59 -> 00:13)\n *\n * -`lastPoint` the last point in the group\n * (e.g. points at 00:13, 00:35, 00:59 -> 00:59)\n *\n * @sample {highstock} stock/plotoptions/series-datagrouping-first-anchor\n *         Applying first and last anchor.\n *\n * @see [dataGrouping.anchor](#plotOptions.series.dataGrouping.anchor)\n *\n * @type       {Highcharts.DataGroupingAnchorExtremes}\n * @since 9.1.0\n * @default    start\n * @apioption  plotOptions.series.dataGrouping.firstAnchor\n */\n/**\n * When data grouping is forced, it runs no matter how small the intervals\n * are. This can be handy for example when the sum should be calculated\n * for values appearing at random times within each hour.\n *\n * @type      {boolean}\n * @default   false\n * @apioption plotOptions.series.dataGrouping.forced\n */\n/**\n * The approximate pixel width of each group. If for example a series\n * with 30 points is displayed over a 600 pixel wide plot area, no grouping\n * is performed. If however the series contains so many points that\n * the spacing is less than the groupPixelWidth, Highcharts will try\n * to group it into appropriate groups so that each is more or less\n * two pixels wide. If multiple series with different group pixel widths\n * are drawn on the same x axis, all series will take the greatest width.\n * For example, line series have 2px default group width, while column\n * series have 10px. If combined, both the line and the column will\n * have 10px by default.\n *\n * @type      {number}\n * @default   2\n * @apioption plotOptions.series.dataGrouping.groupPixelWidth\n */\n/**\n * By default only points within the visible range are grouped. Enabling this\n * option will force data grouping to calculate all grouped points for a given\n * dataset. That option prevents for example a column series from calculating\n * a grouped point partially. The effect is similar to\n * [Series.getExtremesFromAll](#plotOptions.series.getExtremesFromAll) but does\n * not affect yAxis extremes.\n *\n * @sample {highstock} stock/plotoptions/series-datagrouping-groupall/\n *         Two series with the same data but different groupAll setting\n *\n * @type      {boolean}\n * @default   false\n * @since     6.1.0\n * @apioption plotOptions.series.dataGrouping.groupAll\n */\n/**\n * Specifies how the last grouped point is positioned on the xAxis.\n * If firstAnchor and/or lastAnchor are defined, then those options take\n * precedence over anchor for the first and/or last grouped points.\n * Available options:\n *\n * -`start` places the point at the beginning of the group\n * (e.g. range 00:00:00 - 23:59:59 -> 00:00:00)\n *\n * -`middle` places the point in the middle of the group\n * (e.g. range 00:00:00 - 23:59:59 -> 12:00:00)\n *\n * -`end` places the point at the end of the group\n * (e.g. range 00:00:00 - 23:59:59 -> 23:59:59)\n *\n * -`firstPoint` the first point in the group\n * (e.g. points at 00:13, 00:35, 00:59 -> 00:13)\n *\n * -`lastPoint` the last point in the group\n * (e.g. points at 00:13, 00:35, 00:59 -> 00:59)\n *\n * @sample {highstock} stock/plotoptions/series-datagrouping-first-anchor\n *         Applying first and last anchor.\n *\n * @sample {highstock} stock/plotoptions/series-datagrouping-last-anchor\n *         Applying the last anchor in the chart with live data.\n *\n * @see [dataGrouping.anchor](#plotOptions.series.dataGrouping.anchor)\n *\n * @type       {Highcharts.DataGroupingAnchorExtremes}\n * @since 9.1.0\n * @default    start\n * @apioption  plotOptions.series.dataGrouping.lastAnchor\n */\n/**\n * Normally, a group is indexed by the start of that group, so for example\n * when 30 daily values are grouped into one month, that month's x value\n * will be the 1st of the month. This apparently shifts the data to\n * the left. When the smoothed option is true, this is compensated for.\n * The data is shifted to the middle of the group, and min and max\n * values are preserved. Internally, this is used in the Navigator series.\n *\n * @type      {boolean}\n * @default   false\n * @deprecated\n * @apioption plotOptions.series.dataGrouping.smoothed\n */\n/**\n * An array determining what time intervals the data is allowed to be\n * grouped to. Each array item is an array where the first value is\n * the time unit and the second value another array of allowed multiples.\n *\n * Defaults to:\n * ```js\n * units: [[\n *     'millisecond', // unit name\n *     [1, 2, 5, 10, 20, 25, 50, 100, 200, 500] // allowed multiples\n * ], [\n *     'second',\n *     [1, 2, 5, 10, 15, 30]\n * ], [\n *     'minute',\n *     [1, 2, 5, 10, 15, 30]\n * ], [\n *     'hour',\n *     [1, 2, 3, 4, 6, 8, 12]\n * ], [\n *     'day',\n *     [1]\n * ], [\n *     'week',\n *     [1]\n * ], [\n *     'month',\n *     [1, 3, 6]\n * ], [\n *     'year',\n *     null\n * ]]\n * ```\n *\n * @type      {Array<Array<string,(Array<number>|null)>>}\n * @apioption plotOptions.series.dataGrouping.units\n */\n/**\n * The approximate pixel width of each group. If for example a series\n * with 30 points is displayed over a 600 pixel wide plot area, no grouping\n * is performed. If however the series contains so many points that\n * the spacing is less than the groupPixelWidth, Highcharts will try\n * to group it into appropriate groups so that each is more or less\n * two pixels wide. Defaults to `10`.\n *\n * @sample {highstock} stock/plotoptions/series-datagrouping-grouppixelwidth/\n *         Two series with the same data density but different groupPixelWidth\n *\n * @type      {number}\n * @default   10\n * @apioption plotOptions.column.dataGrouping.groupPixelWidth\n */\n''; // Required by JSDoc parsing\n\n;// ./code/es-modules/masters/modules/datagrouping.js\n\n\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nG.dataGrouping = G.dataGrouping || {};\nG.dataGrouping.approximationDefaults = (G.dataGrouping.approximationDefaults ||\n    DataGrouping_ApproximationDefaults);\nG.dataGrouping.approximations = (G.dataGrouping.approximations ||\n    DataGrouping_ApproximationRegistry);\nDataGrouping.compose(G.Axis, G.Series, G.Tooltip);\n/* harmony default export */ const datagrouping_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__984__", "AxisConstructor", "Column<PERSON><PERSON><PERSON>", "DateTimeAxis", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "datagrouping_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "DataGrouping_ApproximationRegistry", "arrayMax", "arrayMin", "correctFloat", "extend", "isNumber", "average", "arr", "len", "length", "ret", "sum", "has<PERSON><PERSON>s", "ApproximationDefaults", "averages", "for<PERSON>ach", "arguments", "push", "close", "high", "hlc", "low", "ohlc", "open", "range", "DataGrouping_DataGroupingDefaults", "common", "groupPixelWidth", "dateTimeLabelFormats", "millisecond", "second", "minute", "hour", "day", "week", "month", "year", "seriesSpecific", "line", "spline", "area", "areaspline", "arearange", "column", "columnrange", "candlestick", "<PERSON><PERSON><PERSON><PERSON>", "units", "addEvent", "DataGroupingAxisComposition_extend", "merge", "pick", "applyGrouping", "e", "axis", "series", "getGroupPixelWidth", "hasProcessed", "hasExtremesChanged", "i", "doGrouping", "dataLength", "dgOptions", "options", "dataGrouping", "Math", "max", "dataTable", "modified", "rowCount", "chart", "plotSizeX", "forced", "onAfterSetScale", "setDataGrouping", "redraw", "update", "seriesOptions", "ordinal", "slope", "DataGrouping_DataGroupingAxisComposition", "compose", "AxisClass", "axisProto", "<PERSON><PERSON><PERSON><PERSON>", "as<PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "splice", "start", "deleteCount", "removedAsSubarray", "items", "from", "removed", "array", "<PERSON><PERSON><PERSON><PERSON>", "getPrototypeOf", "constructor", "result", "set", "subarray", "fireEvent", "objectEach", "<PERSON><PERSON><PERSON>", "Data_DataTableCore", "autoId", "id", "columns", "versionTag", "columnName", "slice", "applyRowCount", "deleteRows", "rowIndex", "getColumn", "asReference", "getColumns", "columnNames", "keys", "reduce", "getRow", "map", "setColumn", "eventDetail", "setColumns", "silent", "setRow", "row", "insert", "indexRowCount", "cellValue", "addColumns", "DateTimeAxis_addEvent", "getMagnitude", "normalizeTickInterval", "timeUnits", "getTimeTicks", "time", "apply", "onAfterSetType", "type", "dateTime", "Additions", "keepProps", "includes", "normalizeTimeTickInterval", "tickInterval", "unitsOption", "unit", "interval", "multiples", "count", "unitRange", "unitName", "getXDateFormat", "x", "closestPointRange", "getDateFormat", "startOfWeek", "resolveDTLFormat", "main", "Axis_DateTimeAxis", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "seriesProto", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "DataGroupingSeriesComposition_addEvent", "defined", "error", "DataGroupingSeriesComposition_extend", "DataGroupingSeriesComposition_isNumber", "DataGroupingSeriesComposition_merge", "DataGroupingSeriesComposition_pick", "splat", "baseGeneratePoints", "generatePoints", "DataGroupingSeriesComposition_applyGrouping", "force", "groupedXData", "hasGroupedData", "dataGroupingOptions", "groupingEnabled", "allowDG", "enabled", "isStock", "reserveSpace", "lastDataGrouping", "currentDataGrouping", "croppedData", "revertRequireSorting", "requireSorting", "skip", "skipDataGrouping", "isCartesian", "isDirty", "xAxis", "yAxis", "destroyGroupedData", "table", "groupAll", "processedXData", "extremes", "getExtremes", "min", "points", "xMin", "xMax", "groupIntervalFactor", "getGroupIntervalFactor", "groupPositions", "xData", "groupedData", "groupData", "approximation", "gapSize", "smoothed", "firstAnchor", "anchor", "lastAnchor", "info", "segmentStarts", "indexOf", "totalRange", "groupMap", "anchorPoints", "groupedDataLastIndex", "anchorIndexIterator", "anchorFirstIndex", "firstGroupEnd", "groupStart", "groupLength", "middle", "end", "firstPoint", "lastPoint", "lastGroupStart", "shiftInterval", "dataMin", "dataMax", "allGroupedTable", "cropData", "cropStart", "preventGraphAnimation", "point", "destroy", "getDGApproximation", "is", "cumulative", "yData", "data", "dataOptions", "handleYData", "values", "pointArrayMap", "pointArrayMapLength", "extendedPointArrayMap", "concat", "valueColumns", "pointX", "pointY", "groupedY", "pos", "approximationFn", "valuesLen", "dataGroupInfo", "pointClass", "optionsToObject", "groupedValuesArr", "j", "val", "index", "applyOptions", "onAfterSetOptions", "plotOptions", "baseOptions", "useCommonDataGrouping", "defaultOptions", "rangeSelector", "userOptions", "isInternal", "selected", "buttonOptions", "DataGrouping_DataGroupingSeriesComposition", "SeriesClass", "dataGroup", "highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_", "format", "highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default", "composed", "DataGrouping_addEvent", "DataGrouping_extend", "DataGrouping_isNumber", "DataGrouping_pick", "pushUnique", "onTooltipHeaderFormatter", "tooltipOptions", "xDateFormat", "xDateFormatEnd", "labelFormats", "formattedKey", "formatString", "<PERSON><PERSON>ooter", "groupEnd", "dateFormat", "styledMode", "styledModeFormat", "text", "preventDefault", "DataGroupingComposition", "TooltipClass", "G", "approximationDefaults", "approximations", "DataGrouping", "Axis", "Series", "<PERSON><PERSON><PERSON>"], "mappings": "CAWA,AAXA;;;;;;;;;;CAUC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,UAAa,EAC/G,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,kCAAmC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAACA,EAAK,UAAa,CAAE,GAC9I,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,kCAAkC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,UAAa,EAElJA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,UAAa,CACzH,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,IACvG,AAAC,CAAA,KACP,iBAmcNC,EAlcM,IA2mBNC,EA0aAC,EArhCUC,EAAuB,CAE/B,IACC,AAACX,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,AAACL,IAERA,EAAOD,OAAO,CAAGQ,CAEX,CAEI,EAGIK,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAahB,OAAO,CAG5B,IAAIC,EAASY,CAAwB,CAACE,EAAS,CAAG,CAGjDf,QAAS,CAAC,CACX,EAMA,OAHAY,CAAmB,CAACG,EAAS,CAACd,EAAQA,EAAOD,OAAO,CAAEc,GAG/Cb,EAAOD,OAAO,AACtB,CAMCc,EAAoBI,CAAC,CAAG,AAACjB,IACxB,IAAIkB,EAASlB,GAAUA,EAAOmB,UAAU,CACvC,IAAOnB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAa,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACrB,EAASuB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACzB,EAASwB,IAC5EE,OAAOC,cAAc,CAAC3B,EAASwB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,EAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAoCxF,IAAME,EARL,CAE9B,EAqBM,CAAEC,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAEC,aAAAA,CAAY,CAAEC,OAAAA,CAAM,CAAEC,SAAAA,CAAQ,CAAE,CAAIN,IAShE,SAASO,EAAQC,CAAG,EAChB,IAAMC,EAAMD,EAAIE,MAAM,CAClBC,EAAMC,EAAIJ,GAMd,OAHIF,EAASK,IAAQF,GACjBE,CAAAA,EAAMP,EAAaO,EAAMF,EAAG,EAEzBE,CACX,CA2FA,SAASC,EAAIJ,CAAG,EACZ,IAAIC,EAAMD,EAAIE,MAAM,CAAEC,EAEtB,GAAI,CAACF,GAAOD,EAAIK,QAAQ,CACpBF,EAAM,UAGL,GAAIF,EAEL,IADAE,EAAM,EACCF,KACHE,GAAOH,CAAG,CAACC,EAAI,CAKvB,OAAOE,CACX,CAMA,IAAMG,EAAwB,CAC1BP,QAAAA,EACAQ,SA9GJ,WACI,IAAMJ,EAAM,EAAE,CAMd,MALA,EAAE,CAACK,OAAO,CAACpB,IAAI,CAACqB,UAAW,SAAUT,CAAG,EACpCG,EAAIO,IAAI,CAACX,EAAQC,GACrB,GAGO,AAAkB,KAAA,IAAXG,CAAG,CAAC,EAAE,CAAmB,KAAK,EAAIA,CACpD,EAuGIQ,MAnGJ,SAAqCX,CAAG,EACpC,OAAOA,EAAIE,MAAM,CACbF,CAAG,CAACA,EAAIE,MAAM,CAAG,EAAE,CAClBF,EAAIK,QAAQ,CAAG,KAAO,KAAK,CACpC,EAgGIO,KA5FJ,SAAcZ,CAAG,EACb,OAAOA,EAAIE,MAAM,CACbR,EAASM,GACRA,EAAIK,QAAQ,CAAG,KAAO,KAAK,CACpC,EAyFIQ,IAnFJ,SAAaD,CAAI,CAAEE,CAAG,CAAEH,CAAK,EAIzB,GAHAC,EAAOnB,EAAmCmB,IAAI,CAACA,GAC/CE,EAAMrB,EAAmCqB,GAAG,CAACA,GAC7CH,EAAQlB,EAAmCkB,KAAK,CAACA,GAC7Cb,EAASc,IACTd,EAASgB,IACThB,EAASa,GACT,MAAO,CAACC,EAAME,EAAKH,EAAM,AAEjC,EA2EIG,IAvEJ,SAAad,CAAG,EACZ,OAAOA,EAAIE,MAAM,CACbP,EAASK,GACRA,EAAIK,QAAQ,CAAG,KAAO,KAAK,CACpC,EAoEIU,KAhEJ,SAAcC,CAAI,CAAEJ,CAAI,CAAEE,CAAG,CAAEH,CAAK,EAKhC,GAJAK,EAAOvB,EAAmCuB,IAAI,CAACA,GAC/CJ,EAAOnB,EAAmCmB,IAAI,CAACA,GAC/CE,EAAMrB,EAAmCqB,GAAG,CAACA,GAC7CH,EAAQlB,EAAmCkB,KAAK,CAACA,GAC7Cb,EAASkB,IACTlB,EAASc,IACTd,EAASgB,IACThB,EAASa,GACT,MAAO,CAACK,EAAMJ,EAAME,EAAKH,EAAM,AAEvC,EAsDIK,KAlDJ,SAAoChB,CAAG,EACnC,OAAOA,EAAIE,MAAM,CAAGF,CAAG,CAAC,EAAE,CAAIA,EAAIK,QAAQ,CAAG,KAAO,KAAK,CAC7D,EAiDIY,MA7CJ,SAAeH,CAAG,CAAEF,CAAI,QAGpB,CAFAE,EAAMrB,EAAmCqB,GAAG,CAACA,GAC7CF,EAAOnB,EAAmCmB,IAAI,CAACA,GAC3Cd,EAASgB,IAAQhB,EAASc,IACnB,CAACE,EAAKF,EAAK,CAElBE,AAAQ,OAARA,GAAgBF,AAAS,OAATA,EACT,WAGf,EAoCIR,IAAAA,CACJ,EACAP,EAAOJ,EAAoCa,GAkJd,IAAMY,EALN,CACzBC,OAvHW,CAGXC,gBAAiB,EAIjBC,qBAAsB,CAClBC,YAAa,CACT,aACA,aACA,WACH,CACDC,OAAQ,CACJ,YACA,YACA,UACH,CACDC,OAAQ,CACJ,WACA,WACA,SACH,CACDC,KAAM,CACF,WACA,WACA,SACH,CACDC,IAAK,CACD,UACA,SACA,WACH,CACDC,KAAM,CACF,aACA,SACA,WACH,CACDC,MAAO,CACH,QACA,OACA,SACH,CACDC,KAAM,CACF,KACA,KACA,MACH,AACL,CAEJ,EAsEIC,eAjEmB,CACnBC,KAAM,CAAC,EACPC,OAAQ,CAAC,EACTC,KAAM,CAAC,EACPC,WAAY,CAAC,EACbC,UAAW,CAAC,EACZC,OAAQ,CACJhB,gBAAiB,EACrB,EACAiB,YAAa,CACTjB,gBAAiB,EACrB,EACAkB,YAAa,CACTlB,gBAAiB,EACrB,EACAL,KAAM,CACFK,gBAAiB,CACrB,EACAP,IAAK,CACDO,gBAAiB,CAErB,EACAmB,WAAY,CACRnB,gBAAiB,EACrB,CACJ,EAyCIoB,MAnCU,CACV,CACI,cACA,CAAC,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,IAAK,IAAK,IAAI,CAC3C,CAAE,CACC,SACA,CAAC,EAAG,EAAG,EAAG,GAAI,GAAI,GAAG,CACxB,CAAE,CACC,SACA,CAAC,EAAG,EAAG,EAAG,GAAI,GAAI,GAAG,CACxB,CAAE,CACC,OACA,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAG,CACzB,CAAE,CACC,MACA,CAAC,EAAE,CACN,CAAE,CACC,OACA,CAAC,EAAE,CACN,CAAE,CACC,QACA,CAAC,EAAG,EAAG,EAAE,CACZ,CAAE,CACC,OACA,KACH,CACJ,AAUD,EAgBM,CAAEC,SAAAA,CAAQ,CAAE5C,OAAQ6C,CAAkC,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAE,CAAIpD,IAoB/E,SAASqD,EAAcC,CAAC,EACpB,IAAMC,EAAO,IAAI,CAAEC,EAASD,EAAKC,MAAM,CAEvCA,EAAOxC,OAAO,CAAC,SAAUwC,CAAM,EAC3BA,EAAO5B,eAAe,CAAG,KAAK,CAClC,GACA4B,EAAOxC,OAAO,CAAC,SAAUwC,CAAM,EAC3BA,EAAO5B,eAAe,CAAI2B,EAAKE,kBAAkB,EAC7CF,EAAKE,kBAAkB,GACvBD,EAAO5B,eAAe,EACtB4B,CAAAA,EAAOE,YAAY,CAAG,CAAA,CAAG,EAI7BF,EAAOH,aAAa,CAAC,CAAC,CAACC,EAAEK,kBAAkB,CAC/C,EACJ,CAwBA,SAASF,IACL,IAAMD,EAAS,IAAI,CAACA,MAAM,CACtBI,EAAIJ,EAAO9C,MAAM,CAAEkB,EAAkB,EAAGiC,EAAa,CAAA,EAAOC,EAAYC,EAE5E,KAAOH,KACHG,CAAAA,EAAYP,CAAM,CAACI,EAAE,CAACI,OAAO,CAACC,YAAY,AAAD,IAIrCrC,EAAkBsC,KAAKC,GAAG,CAACvC,EAE3BwB,EAAKW,EAAUnC,eAAe,CAAEF,EAAkCC,MAAM,CAACC,eAAe,GACxFkC,EAAa,AAACN,CAAAA,CAAM,CAACI,EAAE,CAACQ,SAAS,CAACC,QAAQ,EACtCb,CAAM,CAACI,EAAE,CAACQ,SAAS,AAAD,EAAGE,QAAQ,CAG7Bd,CAAAA,CAAM,CAACI,EAAE,CAAChC,eAAe,EACxBkC,EACI,IAAI,CAACS,KAAK,CAACC,SAAS,CAAG5C,GAC3BkC,GAAcC,EAAUU,MAAM,GAC/BZ,CAAAA,EAAa,CAAA,CAAG,GAI5B,OAAOA,EAAajC,EAAkB,CAC1C,CAOA,SAAS8C,IACL,IAAI,CAAClB,MAAM,CAACxC,OAAO,CAAC,SAAUwC,CAAM,EAChCA,EAAOE,YAAY,CAAG,CAAA,CAC1B,EACJ,CAgBA,SAASiB,EAAgBV,CAAY,CAAEW,CAAM,MAErChB,EASJ,GARAgB,EAASxB,EAAKwB,EAAQ,CAAA,GAClB,AAACX,GACDA,CAAAA,EAAe,CACXQ,OAAQ,CAAA,EACRzB,MAAO,IACX,CAAA,EAGA,IAAI,YAAY7E,EAEhB,IADAyF,EAAI,IAAI,CAACJ,MAAM,CAAC9C,MAAM,CACfkD,KACH,IAAI,CAACJ,MAAM,CAACI,EAAE,CAACiB,MAAM,CAAC,CAClBZ,aAAcA,CAClB,EAAG,CAAA,QAKP,IAAI,CAACM,KAAK,CAACP,OAAO,CAACR,MAAM,CAACxC,OAAO,CAAC,SAAU8D,CAAa,EAErDA,EAAcb,YAAY,CAAG,AAAwB,WAAxB,OAAOA,EAChCA,EACAd,EAAMc,EAAca,EAAcb,YAAY,CACtD,EAGAV,CA5BS,IAAI,CA4BRwB,OAAO,EACZxB,CAAAA,AA7BS,IAAI,CA6BRwB,OAAO,CAACC,KAAK,CAAG,KAAK,CAAA,EAE1BJ,GACA,IAAI,CAACL,KAAK,CAACK,MAAM,EAEzB,CAS6B,IAAMK,EAHC,CAChCC,QAlHJ,SAAiBC,CAAS,EACtBhH,EAAkBgH,EAClB,IAAMC,EAAYD,EAAUzF,SAAS,AAChC0F,CAAAA,EAAU/B,aAAa,GACxBJ,EAASkC,EAAW,gBAAiBT,GAGrCzB,EAASkC,EAAW,kBAAmB9B,GACvCH,EAAmCkC,EAAW,CAC1C/B,cAAAA,EACAI,mBAAAA,EACAkB,gBAAAA,CACJ,GAER,CAqGA,GAqBA,AAAC,SAAUvG,CAAW,EAqClBA,EAAYiH,SAAS,CAPrB,SAAmBzC,CAAM,CAAElC,CAAM,CAAE4E,CAAU,SACzC,AAAIC,MAAMC,OAAO,CAAC5C,IACdA,EAAOlC,MAAM,CAAGA,EACTkC,GAEJA,CAAM,CAAC0C,EAAa,WAAa,QAAQ,CAAC,EAAG5E,EACxD,EAoDAtC,EAAYqH,MAAM,CAvBlB,SAAgB7C,CAAM,CAAE8C,CAAK,CAAEC,CAAW,CAAEC,CAAiB,CAAEC,EAAQ,EAAE,EACrE,GAAIN,MAAMC,OAAO,CAAC5C,GAId,OAHI,AAAC2C,MAAMC,OAAO,CAACK,IACfA,CAAAA,EAAQN,MAAMO,IAAI,CAACD,EAAK,EAErB,CACHE,QAASnD,EAAO6C,MAAM,CAACC,EAAOC,KAAgBE,GAC9CG,MAAOpD,CACX,EAEJ,IAAMqD,EAAc7G,OAAO8G,cAAc,CAACtD,GACrCuD,WAAW,CACVJ,EAAUnD,CAAM,CAACgD,EAAoB,WAAa,QAAQ,CAACF,EAAOA,EAAQC,GAE1ES,EAAS,IAAIH,EADDrD,EAAOlC,MAAM,CAAGiF,EAAcE,EAAMnF,MAAM,EAK5D,OAHA0F,EAAOC,GAAG,CAACzD,EAAO0D,QAAQ,CAAC,EAAGZ,GAAQ,GACtCU,EAAOC,GAAG,CAACR,EAAOH,GAClBU,EAAOC,GAAG,CAACzD,EAAO0D,QAAQ,CAACZ,EAAQC,GAAcD,EAAQG,EAAMnF,MAAM,EAC9D,CACHqF,QAASA,EACTC,MAAOI,CACX,CACJ,CAEJ,EAAGhI,GAAgBA,CAAAA,EAAc,CAAC,CAAA,GAyBlC,GAAM,CAAEiH,UAAAA,CAAS,CAAEI,OAAAA,CAAM,CAAE,CAnB4BrH,EAqBjD,CAAEmI,UAAAA,CAAS,CAAEC,WAAAA,CAAU,CAAEC,UAAAA,CAAS,CAAE,CAAIzG,IA+PX0G,EA9OnC,MAiBIP,YAAYnC,EAAU,CAAC,CAAC,CAAE,CAOtB,IAAI,CAAC2C,MAAM,CAAG,CAAC3C,EAAQ4C,EAAE,CACzB,IAAI,CAACC,OAAO,CAAG,CAAC,EAOhB,IAAI,CAACD,EAAE,CAAI5C,EAAQ4C,EAAE,EAAIH,IACzB,IAAI,CAACpC,QAAQ,CAAG,IAAI,CACpB,IAAI,CAACC,QAAQ,CAAG,EAChB,IAAI,CAACwC,UAAU,CAAGL,IAClB,IAAInC,EAAW,EACfkC,EAAWxC,EAAQ6C,OAAO,EAAI,CAAC,EAAG,CAACjE,EAAQmE,KACvC,IAAI,CAACF,OAAO,CAACE,EAAW,CAAGnE,EAAOoE,KAAK,GACvC1C,EAAWJ,KAAKC,GAAG,CAACG,EAAU1B,EAAOlC,MAAM,CAC/C,GACA,IAAI,CAACuG,aAAa,CAAC3C,EACvB,CAaA2C,cAAc3C,CAAQ,CAAE,CACpB,IAAI,CAACA,QAAQ,CAAGA,EAChBkC,EAAW,IAAI,CAACK,OAAO,CAAE,CAACjE,EAAQmE,KAC1BnE,EAAOlC,MAAM,GAAK4D,GAClB,CAAA,IAAI,CAACuC,OAAO,CAACE,EAAW,CAAG1B,EAAUzC,EAAQ0B,EAAQ,CAE7D,EACJ,CAeA4C,WAAWC,CAAQ,CAAE7C,EAAW,CAAC,CAAE,CAC/B,GAAIA,EAAW,GAAK6C,EAAW,IAAI,CAAC7C,QAAQ,CAAE,CAC1C,IAAI5D,EAAS,EACb8F,EAAW,IAAI,CAACK,OAAO,CAAE,CAACjE,EAAQmE,KAC9B,IAAI,CAACF,OAAO,CAACE,EAAW,CACpBtB,EAAO7C,EAAQuE,EAAU7C,GAAU0B,KAAK,CAC5CtF,EAASkC,EAAOlC,MAAM,AAC1B,GACA,IAAI,CAAC4D,QAAQ,CAAG5D,CACpB,CACA6F,EAAU,IAAI,CAAE,kBAAmB,CAAEY,SAAAA,EAAU7C,SAAAA,CAAS,GACxD,IAAI,CAACwC,UAAU,CAAGL,GACtB,CAWAW,UAAUL,CAAU,CAEpBM,CAAW,CAAE,CACT,OAAO,IAAI,CAACR,OAAO,CAACE,EAAW,AACnC,CAYAO,WAAWC,CAAW,CAEtBF,CAAW,CAAE,CACT,MAAO,AAACE,CAAAA,GAAenI,OAAOoI,IAAI,CAAC,IAAI,CAACX,OAAO,CAAA,EAAGY,MAAM,CAAC,CAACZ,EAASE,KAC/DF,CAAO,CAACE,EAAW,CAAG,IAAI,CAACF,OAAO,CAACE,EAAW,CACvCF,GACR,CAAC,EACR,CAaAa,OAAOP,CAAQ,CAAEI,CAAW,CAAE,CAC1B,MAAO,AAACA,CAAAA,GAAenI,OAAOoI,IAAI,CAAC,IAAI,CAACX,OAAO,CAAA,EAAGc,GAAG,CAAC,AAACzI,GAAQ,IAAI,CAAC2H,OAAO,CAAC3H,EAAI,EAAE,CAACiI,EAAS,CAChG,CAmBAS,UAAUb,CAAU,CAAEnE,EAAS,EAAE,CAAEuE,EAAW,CAAC,CAAEU,CAAW,CAAE,CAC1D,IAAI,CAACC,UAAU,CAAC,CAAE,CAACf,EAAW,CAAEnE,CAAO,EAAGuE,EAAUU,EACxD,CAmBAC,WAAWjB,CAAO,CAAEM,CAAQ,CAAEU,CAAW,CAAE,CACvC,IAAIvD,EAAW,IAAI,CAACA,QAAQ,CAC5BkC,EAAWK,EAAS,CAACjE,EAAQmE,KACzB,IAAI,CAACF,OAAO,CAACE,EAAW,CAAGnE,EAAOoE,KAAK,GACvC1C,EAAW1B,EAAOlC,MAAM,AAC5B,GACA,IAAI,CAACuG,aAAa,CAAC3C,GACduD,GAAaE,SACdxB,EAAU,IAAI,CAAE,mBAChB,IAAI,CAACO,UAAU,CAAGL,IAE1B,CAoBAuB,OAAOC,CAAG,CAAEd,EAAW,IAAI,CAAC7C,QAAQ,CAAE4D,CAAM,CAAEL,CAAW,CAAE,CACvD,GAAM,CAAEhB,QAAAA,CAAO,CAAE,CAAG,IAAI,CAAEsB,EAAgBD,EAAS,IAAI,CAAC5D,QAAQ,CAAG,EAAI6C,EAAW,EAClFX,EAAWyB,EAAK,CAACG,EAAWrB,KACxB,IAAInE,EAASiE,CAAO,CAACE,EAAW,EAC5Bc,GAAaQ,aAAe,CAAA,GAAS,AAAI9C,MAAM4C,GAC/CvF,IACIsF,EACAtF,EAAS6C,EAAO7C,EAAQuE,EAAU,EAAG,CAAA,EAAM,CAACiB,EAAU,EAAEpC,KAAK,CAG7DpD,CAAM,CAACuE,EAAS,CAAGiB,EAEvBvB,CAAO,CAACE,EAAW,CAAGnE,EAE9B,GACIuF,EAAgB,IAAI,CAAC7D,QAAQ,EAC7B,IAAI,CAAC2C,aAAa,CAACkB,GAElBN,GAAaE,SACdxB,EAAU,IAAI,CAAE,gBAChB,IAAI,CAACO,UAAU,CAAGL,IAE1B,CACJ,EAqDM,CAAExD,SAAUqF,CAAqB,CAAEC,aAAAA,CAAY,CAAEC,sBAAAA,CAAqB,CAAEC,UAAAA,CAAS,CAAE,CAAIzI,KAQ7F,AAAC,SAAU3B,CAAY,EAwCnB,SAASqK,IACL,OAAO,IAAI,CAACnE,KAAK,CAACoE,IAAI,CAACD,YAAY,CAACE,KAAK,CAAC,IAAI,CAACrE,KAAK,CAACoE,IAAI,CAAE1H,UAC/D,CAIA,SAAS4H,IACL,GAAI,AAAc,aAAd,IAAI,CAACC,IAAI,CAAiB,CAC1B,IAAI,CAACC,QAAQ,CAAG,KAAK,EACrB,MACJ,CACI,AAAC,IAAI,CAACA,QAAQ,EACd,CAAA,IAAI,CAACA,QAAQ,CAAG,IAAIC,EAAU,IAAI,CAAA,CAE1C,CA9BA3K,EAAa6G,OAAO,CATpB,SAAiBC,CAAS,EAOtB,OANKA,EAAU8D,SAAS,CAACC,QAAQ,CAAC,cAC9B/D,EAAU8D,SAAS,CAAC/H,IAAI,CAAC,YAEzBkE,AADkBD,EAAUzF,SAAS,CAC3BgJ,YAAY,CAAGA,EACzBJ,EAAsBnD,EAAW,eAAgB0D,IAE9C1D,CACX,CAqCA,OAAM6D,EAMF7C,YAAY5C,CAAI,CAAE,CACd,IAAI,CAACA,IAAI,CAAGA,CAChB,CAgBA4F,0BAA0BC,CAAY,CAAEC,CAAW,CAAE,CACjD,IAAMrG,EAASqG,GAAe,CAAC,CAEvB,cAEA,CAAC,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,IAAK,IAAK,IAAI,CAC3C,CAAE,CACC,SACA,CAAC,EAAG,EAAG,EAAG,GAAI,GAAI,GAAG,CACxB,CAAE,CACC,SACA,CAAC,EAAG,EAAG,EAAG,GAAI,GAAI,GAAG,CACxB,CAAE,CACC,OACA,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAG,CACzB,CAAE,CACC,MACA,CAAC,EAAG,EAAE,CACT,CAAE,CACC,OACA,CAAC,EAAG,EAAE,CACT,CAAE,CACC,QACA,CAAC,EAAG,EAAG,EAAG,EAAG,EAAE,CAClB,CAAE,CACC,OACA,KACH,CAAC,CACFC,EAAOtG,CAAK,CAACA,EAAMtC,MAAM,CAAG,EAAE,CAClC6I,EAAWd,CAAS,CAACa,CAAI,CAAC,EAAE,CAAC,CAAEE,EAAYF,CAAI,CAAC,EAAE,CAAE1F,EAGpD,IAAKA,EAAI,EAAGA,EAAIZ,EAAMtC,MAAM,GAExB6I,EAAWd,CAAS,CAACa,AADrBA,CAAAA,EAAOtG,CAAK,CAACY,EAAE,AAAD,CACW,CAAC,EAAE,CAAC,CAC7B4F,EAAYF,CAAI,CAAC,EAAE,EACftG,CAAK,CAACY,EAAI,EAAE,GAORwF,CAAAA,GAJa,AAACG,CAAAA,EACdC,CAAS,CAACA,EAAU9I,MAAM,CAAG,EAAE,CAC/B+H,CAAS,CAACzF,CAAK,CAACY,EAAI,EAAE,CAAC,EAAE,CAAC,AAAD,EAAK,CAEP,GAXLA,KAiB1B2F,IAAad,EAAUpG,IAAI,EAAI+G,EAAe,EAAIG,GAClDC,CAAAA,EAAY,CAAC,EAAG,EAAG,EAAE,AAAD,EAGxB,IAAMC,EAAQjB,EAAsBY,EAAeG,EAAUC,EAAWF,AAAY,SAAZA,CAAI,CAAC,EAAE,CAC3EpF,KAAKC,GAAG,CAACoE,EAAaa,EAAeG,GAAW,GAChD,GACJ,MAAO,CACHG,UAAWH,EACXE,MAAOA,EACPE,SAAUL,CAAI,CAAC,EAAE,AACrB,CACJ,CAOAM,eAAeC,CAAC,CAAEhI,CAAoB,CAAE,CACpC,GAAM,CAAE0B,KAAAA,CAAI,CAAE,CAAG,IAAI,CAAEoF,EAAOpF,EAAKgB,KAAK,CAACoE,IAAI,CAC7C,OAAOpF,EAAKuG,iBAAiB,CACzBnB,EAAKoB,aAAa,CAACxG,EAAKuG,iBAAiB,CAAED,EAAGtG,EAAKS,OAAO,CAACgG,WAAW,CAAEnI,IAEpE8G,EAAKsB,gBAAgB,CAACpI,EAAqBQ,IAAI,EAAE6H,IAAI,CACzDvB,EAAKsB,gBAAgB,CAACpI,EAAqBK,GAAG,EAAEgI,IAAI,AAC5D,CACJ,CACA7L,EAAa2K,SAAS,CAAGA,CAC7B,EAAG3K,GAAiBA,CAAAA,EAAe,CAAC,CAAA,GAMP,IAAM8L,EAAqB9L,EAGxD,IAAI+L,EAAmI5L,EAAoB,KAmB3J,GAAM,CAAEgF,OAAQ,CAAE9D,UAAW2K,CAAW,CAAE,CAAE,CAAIC,AAlB2G9L,EAAoBI,CAAC,CAACwL,KAoB3K,CAAEnH,SAAUsH,CAAsC,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAEpK,OAAQqK,CAAoC,CAAEpK,SAAUqK,CAAsC,CAAExH,MAAOyH,CAAmC,CAAExH,KAAMyH,CAAkC,CAAEC,MAAAA,CAAK,CAAE,CAAI9K,IAMrQ+K,EAAqBV,EAAYW,cAAc,CAqGrD,SAASC,EAA4CtH,CAAkB,MAiW7CH,EAAQ0H,EA7bHC,EA6F3B,IAqBIvH,EAAGwH,EArBc7G,EAAQf,AAAd,IAAI,CAAiBe,KAAK,CAA4B8G,EAAsBrH,AAAtCR,AAAtC,IAAI,CAAyCQ,OAAO,CAAgCC,YAAY,CAAEqH,EAAkB9H,AAAmB,CAAA,IAAnBA,AAApH,IAAI,CAAuH+H,OAAO,EAAcF,GAC3JR,EAAmCQ,EAAoBG,OAAO,CAAEjH,EAAMP,OAAO,CAACyH,OAAO,EAAGC,EAAelI,AAD5F,IAAI,CAC+FkI,YAAY,GAAIC,EAAmB,IAAI,CAACC,mBAAmB,CACzKA,EAAqBC,EAAaC,EAAuB,CAAA,CAEzDR,CAAAA,GAAmB,CAAC9H,AAJT,IAAI,CAIYuI,cAAc,EACzCvI,CAAAA,AALW,IAAI,CAKRuI,cAAc,CAAGD,EAAuB,CAAA,CAAG,EAItD,IAAME,EAAOC,AAAiD,CAAA,IAuVxCzI,EAhWP,IAAI,CAgWW0H,EAvVQvH,EAwV/B,CAAEH,CAAAA,EAAO0I,WAAW,EACvB,CAAC1I,EAAO2I,OAAO,EACf,CAAC3I,EAAO4I,KAAK,CAACD,OAAO,EACrB,CAAC3I,EAAO6I,KAAK,CAACF,OAAO,EACrB,CAACjB,CAAI,IA5V8D,CAACI,EAKxE,GAHIQ,GACAtI,CAAAA,AAZW,IAAI,CAYRuI,cAAc,CAAG,CAAA,CAAI,EAE5BC,EACA,OAEJxI,AAjBe,IAAI,CAiBZ8I,kBAAkB,GACzB,IAAMC,EAAQlB,EAAoBmB,QAAQ,CACtChJ,AAnBW,IAAI,CAmBRY,SAAS,CAChBZ,AApBW,IAAI,CAoBRY,SAAS,CAACC,QAAQ,EAAIb,AApBlB,IAAI,CAoBqBY,SAAS,CAAEqI,EAAiBjJ,AApBrD,IAAI,CAoBwD4D,SAAS,CAAC,IAAK,CAACiE,EAAoBmB,QAAQ,EAA2BhI,EAAYD,EAAMC,SAAS,CAAE4H,EAAQ5I,AApBxK,IAAI,CAoB2K4I,KAAK,CAAEM,EAAWN,EAAMO,WAAW,GAAI5H,EAAUqH,EAAMpI,OAAO,CAACe,OAAO,CAAEnD,EAAkB4B,AApBzQ,IAAI,CAoB4Q5B,eAAe,CAI9S,GAAIA,GAJ8H6K,GAM9HF,EAAMjI,QAAQ,EACdE,GACAmG,EAAuC+B,EAASE,GAAG,EAAG,CACtDxB,EAAiB,CAAA,EAEjB5H,AA/BW,IAAI,CA+BR2I,OAAO,CAAG,CAAA,EACjB3I,AAhCW,IAAI,CAgCRqJ,MAAM,CAAG,KAChB,IAAMC,EAAOJ,EAASE,GAAG,CAAEG,EAAOL,EAASvI,GAAG,CAAE6I,EAAsB,AAACjI,GACnEqH,EAAMrH,OAAO,EACbqH,EAAMrH,OAAO,CAACkI,sBAAsB,CAACH,EAAMC,EAnCpC,IAAI,GAmCkD,EAAGxD,EAAW,AAAC3H,EAAmBmL,CAAAA,EAAOD,CAAG,EAAKtI,EAC9GwI,EAAqBE,EAAiBd,EAAM1D,YAAY,CAACyB,EAAkBnB,SAAS,CAACtJ,SAAS,CAACyJ,yBAAyB,CAACI,EAAU8B,EAAoBrI,KAAK,EAC5JtB,EAAkCsB,KAAK,EAE3CkB,KAAK0I,GAAG,CAACE,EAAMK,AAnB+GV,CAmB1G,CAAC,EAAE,EAAGvI,KAAKC,GAAG,CAAC4I,EAAMI,AAnBqFV,CAmBhF,CAACU,AAnB+EV,EAmBzE/L,MAAM,CAAG,EAAE,EAAG0L,EAAMpI,OAAO,CAACgG,WAAW,CAAEyC,EAAgBjJ,AAvCnG,IAAI,CAuCsGsG,iBAAiB,EAAGsD,EAAc/C,EAAYgD,SAAS,CAACzE,KAAK,CAvCvK,IAAI,CAuC4K,CACvL2D,EACAW,EACA7B,EAAoBiC,aAAa,CACpC,EACGjJ,EAAW+I,EAAY/I,QAAQ,CAAE8G,EAAe9G,EAAS+C,SAAS,CAAC,IAAK,CAAA,GAAOmG,EAAU,EAa7F,IAVIlC,GAAqBmC,UACrBnJ,EAASC,QAAQ,GACjB+G,EAAoBoC,WAAW,CAAG,aAClCpC,EAAoBqC,MAAM,CAAG,SAC7BrC,EAAoBsC,UAAU,CAAG,YACjClD,EAAM,GAAI,CAAA,EAAOlG,EAAO,CACpB,wBAAyB,yBAC7B,IAGCX,EAAI,EAAGA,EAAIsJ,EAAexM,MAAM,CAAEkD,IAK/B,AAACsJ,EAAeU,IAAI,CAACC,aAAa,EAClCX,AAAiD,KAAjDA,EAAeU,IAAI,CAACC,aAAa,CAACC,OAAO,CAAClK,IAC1C2J,CAAAA,EAAUrJ,KAAKC,GAAG,CAAC+I,CAAc,CAACtJ,EAAE,CAAGsJ,CAAc,CAACtJ,EAAI,EAAE,CAAE2J,EAAO,CAI7E3B,CADAA,CAAAA,EAAsBsB,EAAeU,IAAI,AAAD,EACpBL,OAAO,CAAGA,EAC9B/J,AArEW,IAAI,CAqERsG,iBAAiB,CAAGoD,EAAeU,IAAI,CAACG,UAAU,CACzDvK,AAtEW,IAAI,CAsERwK,QAAQ,CAAGZ,EAAYY,QAAQ,CACtCxK,AAvEW,IAAI,CAuERoI,mBAAmB,CAAGA,EAC7BqC,AAtIR,SAAsBzK,CAAM,CAAE2H,CAAY,CAAE4B,CAAI,EAC5C,IAAgC1B,EAAsBrH,AAAtCR,EAAOQ,OAAO,CAAgCC,YAAY,CAAE8J,EAAcvK,EAAOoI,mBAAmB,EAAIpI,EAAOoI,mBAAmB,CAAC2B,OAAO,CAAGJ,EAAQ3J,EAAO4D,SAAS,CAAC,KACtL,GAAI,CAAEiE,CAAAA,GACF8B,EAAMzM,MAAM,EACZqN,GACAvK,EAAOwK,QAAQ,AAAD,EACd,OAEJ,IAAME,EAAuB/C,EAAazK,MAAM,CAAG,EAAGgN,EAASrC,EAAoBqC,MAAM,CAAED,EAAcpC,EAAoBoC,WAAW,CAAEE,EAAatC,EAAoBsC,UAAU,CACjLQ,EAAsBhD,EAAazK,MAAM,CAAG,EAAG0N,EAAmB,EAGtE,GAAIX,GAAeN,CAAK,CAAC,EAAE,EAAIhC,CAAY,CAAC,EAAE,CAAE,KAGxCkD,CAFJD,CAAAA,IACA,IAAME,EAAa9K,EAAOwK,QAAQ,CAAC,EAAE,CAACtI,KAAK,CAAE6I,EAAc/K,EAAOwK,QAAQ,CAAC,EAAE,CAACtN,MAAM,AAEhFiK,CAAAA,EAAuC2D,IAAe3D,EAAuC4D,IAC7FF,CAAAA,EAAgBC,EAAcC,CAAAA,EAAc,CAAA,CAAC,EAEjDpD,CAAY,CAAC,EAAE,CAAG,CAAA,CACdzF,MAAOyF,CAAY,CAAC,EAAE,CACtBqD,OAAQrD,CAAY,CAAC,EAAE,CAAG,GAAM4C,EAChCU,IAAKtD,CAAY,CAAC,EAAE,CAAG4C,EACvBW,WAAYvB,CAAK,CAAC,EAAE,CACpBwB,UAAWN,GAAiBlB,CAAK,CAACkB,EAAc,AACpD,CAAA,CAAC,CAACZ,EAAY,AAClB,CAIA,GAAIS,EAAuB,GACvBP,GACAI,GACA5C,CAAY,CAAC+C,EAAqB,EAAInB,EAAOgB,EAAY,CACzDI,IACA,IAAMS,EAAiBpL,EAAOwK,QAAQ,CAACxK,EAAOwK,QAAQ,CAACtN,MAAM,CAAG,EAAE,CAACgF,KAAK,AACxEyF,CAAAA,CAAY,CAAC+C,EAAqB,CAAG,CAAA,CACjCxI,MAAOyF,CAAY,CAAC+C,EAAqB,CACzCM,OAAQrD,CAAY,CAAC+C,EAAqB,CAAG,GAAMH,EACnDU,IAAKtD,CAAY,CAAC+C,EAAqB,CAAGH,EAC1CW,WAAYE,GAAkBzB,CAAK,CAACyB,EAAe,CACnDD,UAAWxB,CAAK,CAACA,EAAMzM,MAAM,CAAG,EAAE,AACtC,CAAA,CAAC,CAACiN,EAAW,AACjB,CACA,GAAID,GAAUA,AAAW,UAAXA,EAAoB,CAC9B,IAAMmB,EAAiBd,EACnB,CAAA,CAAES,OAAQ,GAAKC,IAAK,CAAE,CAAA,CAAC,CAACf,EAAO,CAGnC,KAAOS,GAAuBC,GAC1BjD,CAAY,CAACgD,EAAoB,EAAIU,EACrCV,GAER,CACJ,EAQmB,IAAI,CAwEMhD,GAAgB,EAAE,CAAE4B,GACrCrB,GAAgBP,IAnKpBX,EAAQW,CAHeA,EAuKGA,EApKN,CAAC,EAAE,GACvBR,EAAuCyB,AAmKpBA,EAnK0BQ,GAAG,GAChDjC,EAAuCyB,AAkKpBA,EAlK0B0C,OAAO,GACpD3D,CAAY,CAAC,EAAE,CAAGiB,AAiKCA,EAjKKQ,GAAG,GACvB,CAAA,AAAC,CAACpC,EAAQ4B,AAgKKA,EAhKCpI,OAAO,CAAC4I,GAAG,GAC3BR,AA+JeA,EA/JTQ,GAAG,EAAIR,AA+JEA,EA/JI0C,OAAO,EAC1B1C,AA8JeA,EA9JTQ,GAAG,GAAKR,AA8JCA,EA9JK0C,OAAO,AAAD,GAC1B1C,CAAAA,AA6JeA,EA7JTQ,GAAG,CAAG1I,KAAK0I,GAAG,CAACzB,CAAY,CAAC,EAAE,CAAEiB,AA6JvBA,EA7J6BQ,GAAG,CAAA,EAEnDR,AA2JmBA,EA3Jb0C,OAAO,CAAG5K,KAAK0I,GAAG,CAACzB,CAAY,CAAC,EAAE,CAAEiB,AA2JvBA,EA3J6B0C,OAAO,GAIvDtE,EAAQW,CAAY,CAACA,EAAazK,MAAM,CAAG,EAAE,GAC7CiK,EAAuCyB,AAsJpBA,EAtJ0BjI,GAAG,GAChDwG,EAAuCyB,AAqJpBA,EArJ0B2C,OAAO,GACpD5D,CAAY,CAACA,EAAazK,MAAM,CAAG,EAAE,CAAG0L,AAoJrBA,EApJ2BjI,GAAG,GAC7C,CAAA,AAAC,CAACqG,EAAQ4B,AAmJKA,EAnJCpI,OAAO,CAACG,GAAG,GAC3BwG,EAAuCyB,AAkJxBA,EAlJ8B2C,OAAO,GACpD3C,AAiJeA,EAjJTjI,GAAG,EAAIiI,AAiJEA,EAjJI2C,OAAO,EAAK3C,AAiJhBA,EAjJsBjI,GAAG,GAAKiI,AAiJ9BA,EAjJoC2C,OAAO,AAAD,GACzD3C,CAAAA,AAgJeA,EAhJTjI,GAAG,CAAGD,KAAKC,GAAG,CAACgH,CAAY,CAACA,EAAazK,MAAM,CAAG,EAAE,CAAE0L,AAgJ7CA,EAhJmDjI,GAAG,CAAA,EAEzEiI,AA8ImBA,EA9Ib2C,OAAO,CAAG7K,KAAKC,GAAG,CAACgH,CAAY,CAACA,EAAazK,MAAM,CAAG,EAAE,CAAE0L,AA8I7CA,EA9ImD2C,OAAO,IAkJzE1D,EAAoBmB,QAAQ,GAG5BhJ,AAjFO,IAAI,CAiFJwL,eAAe,CAAG3K,EAGzB8G,EAAe9G,AADfA,CAAAA,EAAWwH,AADXA,CAAAA,EAAcrI,AAlFP,IAAI,CAkFUyL,QAAQ,CAAC5K,EAAU+H,EAAMQ,GAAG,EAAI,EAAGR,EAAMjI,GAAG,EAAI,EAAC,EAC/CE,QAAQ,AAAD,EACN+C,SAAS,CAAC,KAClC5D,AArFO,IAAI,CAqFJ0L,SAAS,CAAGrD,EAAYnG,KAAK,EAGxClC,AAxFW,IAAI,CAwFRY,SAAS,CAACC,QAAQ,CAAGA,CAChC,MAEIb,AA3FW,IAAI,CA2FRwK,QAAQ,CAAG,KAAK,EACvBxK,AA5FW,IAAI,CA4FRoI,mBAAmB,CAAG,KAAK,CAEtCpI,CA9Fe,IAAI,CA8FZ4H,cAAc,CAAGA,EACxB5H,AA/Fe,IAAI,CA+FZ2L,qBAAqB,CACxB,AAACxD,CAAAA,GAAoBA,EAAiBoC,UAAU,AAAD,IAC1CnC,CAAAA,GAAuBA,EAAoBmC,UAAU,AAAD,CACjE,CA+BA,SAASzB,KAED,IAAI,CAACc,WAAW,GAChB,IAAI,CAACA,WAAW,CAACpM,OAAO,CAAC,SAAUoO,CAAK,CAAExL,CAAC,EACnCwL,GACA,CAAA,IAAI,CAAChC,WAAW,CAACxJ,EAAE,CAAGwL,EAAMC,OAAO,CAC/BD,EAAMC,OAAO,GAAK,IAAG,CAEjC,EAAG,IAAI,EAKP,IAAI,CAACjC,WAAW,CAAC1M,MAAM,CAAG,EAC1B,OAAO,IAAI,CAACsO,eAAe,CAEnC,CAKA,SAAShE,KACLD,EAAmBnC,KAAK,CAAC,IAAI,EAG7B,IAAI,CAAC0D,kBAAkB,GACvB,IAAI,CAACc,WAAW,CAAG,IAAI,CAAChC,cAAc,CAAG,IAAI,CAACyB,MAAM,CAAG,IAC3D,CAMA,SAASyC,YACL,AAAI,IAAI,CAACC,EAAE,CAAC,aACD,QAEP,IAAI,CAACA,EAAE,CAAC,QACD,OAEP,IAAI,CAACA,EAAE,CAAC,OACD,MAKX,IAAI,CAACA,EAAE,CAAC,WACJ,IAAI,CAACvL,OAAO,CAACwL,UAAU,CAChB,MAEJ,SACX,CAkBA,SAASnC,GAAUd,CAAK,CAAEW,CAAc,CAAEI,CAAa,EACnD,IAAMH,EAAQZ,EAAMnF,SAAS,CAAC,IAAK,CAAA,IAAS,EAAE,CAAEqI,EAAQlD,EAAMnF,SAAS,CAAC,IAAK,CAAA,GAAO5D,EAAS,IAAI,CAAEkM,EAAOlM,EAAOkM,IAAI,CAAEC,EAAcnM,EAAOQ,OAAO,EAAIR,EAAOQ,OAAO,CAAC0L,IAAI,CAAEvE,EAAe,EAAE,CAAE9G,EAAW,IAAIqC,EAAsBsH,EAAW,EAAE,CAAElK,EAAayI,EAAMjI,QAAQ,CAG9QsL,EAAc,CAAC,CAACH,EAAOI,EAAS,EAAE,CAAEC,EAAgBtM,EAAOsM,aAAa,CAAEC,EAAsBD,GAAiBA,EAAcpP,MAAM,CAAEsP,EAAwB,CAAC,IAAI,CAACC,MAAM,CAACH,GAAiB,CAAC,IAAI,EAElMI,EAAe,AAACJ,CAAAA,GAAiB,CAAC,IAAI,AAAD,EAAGnI,GAAG,CAAC,IAAM,EAAE,EAAG6E,EAAY,IAAI,CAACxI,OAAO,CAACC,YAAY,EACxF,IAAI,CAACD,OAAO,CAACC,YAAY,CAACuI,QAAQ,CAClC2D,EAAQC,EAAQC,EAAUC,EAAM,EAAG5K,EAAQ,EACzC6K,EAAmB,AAAyB,YAAzB,OAAOjD,EAC5BA,EACAA,GAAiBrN,CAAkC,CAACqN,EAAc,CAC9DrN,CAAkC,CAACqN,EAAc,CACjDrN,CAAkC,CAAEuD,EAAO8L,kBAAkB,EAAI9L,EAAO8L,kBAAkB,IACtF,UAAW,CAEvB,GAAIS,EAAqB,CACrB,IAAItP,EAAMqP,EAAcpP,MAAM,CAC9B,KAAOD,KACHoP,EAAO3O,IAAI,CAAC,EAAE,CAEtB,MAEI2O,EAAO3O,IAAI,CAAC,EAAE,EAElB,IAAMsP,EAAYT,GAAuB,EACzC,IAAK,IAAInM,EAAI,EAAGA,GAAKE,EAAYF,IAE7B,IAAIuJ,CAAAA,CAAK,CAACvJ,EAAE,CAAGsJ,CAAc,CAAC,EAAE,AAAD,GAK/B,KAAO,AAAoC,KAAA,IAA5BA,CAAc,CAACoD,EAAM,EAAE,EAClCnD,CAAK,CAACvJ,EAAE,EAAIsJ,CAAc,CAACoD,EAAM,EAAE,EACnC1M,IAAME,GAAY,CAyBlB,GAvBAqM,EAASjD,CAAc,CAACoD,EAAI,CAC5B9M,EAAOiN,aAAa,CAAG,CACnB/K,MAAO8G,EAAW9G,EAASlC,EAAO0L,SAAS,CAAGxJ,EAC9ChF,OAAQmP,CAAM,CAAC,EAAE,CAACnP,MAAM,CACxB4N,WAAY6B,CAChB,EACAE,EAAWE,EAAgB3H,KAAK,CAACpF,EAAQqM,GAMrCrM,EAAOkN,UAAU,EAAI,CAAClG,EAAQhH,EAAOiN,aAAa,CAACzM,OAAO,IAE1DR,EAAOiN,aAAa,CAACzM,OAAO,CAAG4G,EAAoCpH,EAAOkN,UAAU,CAAChR,SAAS,CACzFiR,eAAe,CAAC/Q,IAAI,CAAC,CAAE4D,OAAQA,CAAO,EAAGA,EAAOQ,OAAO,CAAC0L,IAAI,CAAClM,EAAO0L,SAAS,CAAGxJ,EAAM,GAG3FsK,EAAsBhP,OAAO,CAAC,SAAU9B,CAAG,EACvC,OAAOsE,EAAOiN,aAAa,CAACzM,OAAO,CAAC9E,EAAI,AAC5C,IAGA,AAAoB,KAAA,IAAbmR,EAA0B,CACjClF,EAAajK,IAAI,CAACiP,GAElB,IAAMS,EAAmB9F,EAAMuF,GAC/B,IAAK,IAAIQ,EAAI,EAAGA,EAAID,EAAiBlQ,MAAM,CAAEmQ,IACzCX,CAAY,CAACW,EAAE,CAAC3P,IAAI,CAAC0P,CAAgB,CAACC,EAAE,EAE5C7C,EAAS9M,IAAI,CAACsC,EAAOiN,aAAa,CACtC,CAEA/K,EAAQ9B,EACR,IAAK,IAAIiN,EAAI,EAAGA,EAAIL,EAAWK,IAC3BhB,CAAM,CAACgB,EAAE,CAACnQ,MAAM,CAAG,EACnBmP,CAAM,CAACgB,EAAE,CAAChQ,QAAQ,CAAG,CAAA,EAKzB,GAFAyP,GAAO,EAEH1M,IAAME,EACN,KAER,CAEA,GAAIF,IAAME,EACN,MAIJ,GAAIgM,EAAe,CACf,IAIIgB,EAJEC,EAAQvE,EAAW5I,EAAIJ,EAAO0L,SAAS,CAAGtL,EAAGwL,EAAQ,AAACM,GAAQA,CAAI,CAACqB,EAAM,EAC3EvN,EAAOkN,UAAU,CAAChR,SAAS,CAACsR,YAAY,CAACpI,KAAK,CAAC,CAC3CpF,OAAQA,CACZ,EAAG,CAACmM,CAAW,CAACoB,EAAM,CAAC,EAE3B,IAAK,IAAIF,EAAI,EAAGA,EAAId,EAAqBc,IAEjClG,EADJmG,EAAM1B,CAAK,CAACU,CAAa,CAACe,EAAE,CAAC,EAEzBhB,CAAM,CAACgB,EAAE,CAAC3P,IAAI,CAAC4P,GAEVA,AAAQ,OAARA,GACLjB,CAAAA,CAAM,CAACgB,EAAE,CAAChQ,QAAQ,CAAG,CAAA,CAAG,CAGpC,MAGQ8J,EADJyF,EAASR,EAAcH,CAAK,CAAC7L,EAAE,CAAG,MAE9BiM,CAAM,CAAC,EAAE,CAAC3O,IAAI,CAACkP,GAEVA,AAAW,OAAXA,GACLP,CAAAA,CAAM,CAAC,EAAE,CAAChP,QAAQ,CAAG,CAAA,CAAG,EAIpC,IAAMgG,EAAU,CACZgD,EAAGsB,CACP,EAKA,MAJA,AAAC2E,CAAAA,GAAiB,CAAC,IAAI,AAAD,EAAG9O,OAAO,CAAC,CAAC9B,EAAK0E,KACnCiD,CAAO,CAAC3H,EAAI,CAAGgR,CAAY,CAACtM,EAAE,AAClC,GACAS,EAASyD,UAAU,CAACjB,GACb,CACHmH,SAAAA,EACA3J,SAAAA,CACJ,CACJ,CAMA,SAAS4M,GAAkB3N,CAAC,EACxB,IAAMU,EAAUV,EAAEU,OAAO,CAAE8E,EAAO,IAAI,CAACA,IAAI,CAAEoI,EAAc,IAAI,CAAC3M,KAAK,CAACP,OAAO,CAACkN,WAAW,CAGzFC,EAAe,IAAI,CAACC,qBAAqB,EACrC1P,EAAkCC,MAAM,CAAGW,EAAiBZ,EAAkCY,cAAc,CAC5G+O,EAAiB,AAACrR,IAA+EqR,cAAc,CAACH,WAAW,CAACpI,EAAK,CAAC7E,YAAY,CAClJ,GAAIiN,GAAgB5O,CAAAA,CAAc,CAACwG,EAAK,EAAIqI,CAAU,EAAI,CACtD,IAAMG,EAAgB,IAAI,CAAC/M,KAAK,CAAC+M,aAAa,AAC1C,CAACD,GACDA,CAAAA,EAAiBzG,EAAoClJ,EAAkCC,MAAM,CAAEW,CAAc,CAACwG,EAAK,CAAA,EAEvH9E,EAAQC,YAAY,CAAG2G,EAAoCuG,EAAaE,EAAgBH,EAAY1N,MAAM,EAAI0N,EAAY1N,MAAM,CAACS,YAAY,CAE7IiN,CAAW,CAACpI,EAAK,CAAC7E,YAAY,CAAE,IAAI,CAACsN,WAAW,CAACtN,YAAY,CAAE,CAACD,EAAQwN,UAAU,EAC9EF,GACA3G,EAAuC2G,EAAcG,QAAQ,GAC7DH,EAAcI,aAAa,CAACJ,EAAcG,QAAQ,CAAC,CAACxN,YAAY,CACxE,CACJ,CAoB6B,IAAM0N,GAJG,CAClCzM,QAvQJ,SAA+C0M,CAAW,EACtD,IAAMvH,EAAcuH,EAAYlS,SAAS,AACpC2K,CAAAA,EAAYhH,aAAa,GAI1BkH,EAHmBqH,EAAYlS,SAAS,CAACgR,UAAU,CAGA,SAAU,WACzD,GAAI,IAAI,CAACmB,SAAS,CAEd,OADApH,EAAM,GAAI,CAAA,EAAO,IAAI,CAACjH,MAAM,CAACe,KAAK,EAC3B,CAAA,CAEf,GACAgG,EAAuCqH,EAAa,kBAAmBX,IACvE1G,EAAuCqH,EAAa,UAAWtF,IAC/D5B,EAAqCL,EAAa,CAC9ChH,cAAe4H,EACfqB,mBAAAA,GACAtB,eAAAA,GACAsE,mBAAAA,GACAjC,UAAAA,EACJ,GAER,EAkPIA,UAAAA,EACJ,EAIA,IAAIyE,GAAmHtT,EAAoB,KAiB3I,GAAM,CAAEuT,OAAAA,EAAM,CAAE,CAAIC,AAhBuHxT,EAAoBI,CAAC,CAACkT,MAkB3J,CAAEG,SAAAA,EAAQ,CAAE,CAAIjS,IAEhB,CAAEiD,SAAUiP,EAAqB,CAAE7R,OAAQ8R,EAAmB,CAAE7R,SAAU8R,EAAqB,CAAEhP,KAAMiP,EAAiB,CAAEC,WAAAA,EAAU,CAAE,CAAItS,IAsBhJ,SAASuS,GAAyBjP,CAAC,EAC/B,IAAMiB,EAAQ,IAAI,CAACA,KAAK,CAAEoE,EAAOpE,EAAMoE,IAAI,CAAEyG,EAAQ9L,EAAE8L,KAAK,CAAE5L,EAAS4L,EAAM5L,MAAM,CAAEQ,EAAUR,EAAOQ,OAAO,CAAEwO,EAAiBhP,EAAOgP,cAAc,CAAEnH,EAAsBrH,EAAQC,YAAY,CAAEmI,EAAQ5I,EAAO4I,KAAK,CACnNqG,EAAcD,EAAeC,WAAW,EAAI,GAAIC,EAAgB9G,EAAqB/J,EAAsB8Q,EAAcC,EAAcC,EAAeL,CAAc,CAAClP,EAAEwP,QAAQ,CAAG,eAAiB,eAAe,CAEtN,GAAI1G,GACAA,AAAuB,aAAvBA,EAAMpI,OAAO,CAAC8E,IAAI,EAClBuC,GACA+G,GAAsBhD,EAAMlQ,GAAG,EAAG,CAElC0M,EAAsBpI,EAAOoI,mBAAmB,CAChD/J,EAAuBwJ,EAAoBxJ,oBAAoB,EAE3DH,EAAkCC,MAAM,CAACE,oBAAoB,CAG7D+J,GACA+G,EAAe9Q,CAAoB,CAAC+J,EAAoBjC,QAAQ,CAAC,CAC7DiC,AAA8B,IAA9BA,EAAoBnC,KAAK,CACzBgJ,EAAcE,CAAY,CAAC,EAAE,EAG7BF,EAAcE,CAAY,CAAC,EAAE,CAC7BD,EAAiBC,CAAY,CAAC,EAAE,GAM/B,CAACF,GAAe5Q,GAAwBuK,EAAMrD,QAAQ,EAC3D0J,CAAAA,EAAcrG,EAAMrD,QAAQ,CAACa,cAAc,CAACwF,EAAMvF,CAAC,CAAE2I,EAAe3Q,oBAAoB,CAAA,EAE5F,IAAMyM,EAAa+D,GAAkB7O,EAAOwK,QAAQ,EAAE,CAACoB,EAAM2B,KAAK,CAAC,CAACzC,WAAYc,EAAMlQ,GAAG,EAAG6T,EAAWzE,EAAc1C,CAAAA,GAAqBmC,YAAc,CAAA,EAAK,EAC7J6E,EAAejK,EAAKqK,UAAU,CAACP,EAAanE,GACxCoE,GACAE,CAAAA,GAAgBjK,EAAKqK,UAAU,CAACN,EAAgBK,EAAQ,EAGxDvP,EAAOe,KAAK,CAAC0O,UAAU,EACvBJ,CAAAA,EAAe,IAAI,CAACK,gBAAgB,CAACL,EAAY,EAGrDvP,EAAE6P,IAAI,CAAGpB,GAAOc,EAAc,CAC1BzD,MAAO+C,GAAoB/C,EAAO,CAAElQ,IAAK0T,CAAa,GACtDpP,OAAAA,CACJ,EAAGe,GACHjB,EAAE8P,cAAc,EACpB,CACJ,CAMA,IAAMC,GAA0B,CAC5BnO,QAnEJ,SAA8BC,CAAS,CAAEyM,CAAW,CAAE0B,CAAY,EAC9DrO,EAAyCC,OAAO,CAACC,GACjDwM,GAA2CzM,OAAO,CAAC0M,GAC/C0B,GACAhB,GAAWL,GAAU,iBACrBC,GAAsBoB,EAAc,kBAAmBf,GAE/D,EA6DIlF,UAAWsE,GAA2CtE,SAAS,AACnE,EAuXMkG,GAAKvT,GACXuT,CAAAA,GAAEtP,YAAY,CAAGsP,GAAEtP,YAAY,EAAI,CAAC,EACpCsP,GAAEtP,YAAY,CAACuP,qBAAqB,CAAID,GAAEtP,YAAY,CAACuP,qBAAqB,EAr3DH1S,EAu3DzEyS,GAAEtP,YAAY,CAACwP,cAAc,CAAIF,GAAEtP,YAAY,CAACwP,cAAc,EAC1DxT,EACJyT,AA5XmDL,GA4XtCnO,OAAO,CAACqO,GAAEI,IAAI,CAAEJ,GAAEK,MAAM,CAAEL,GAAEM,OAAO,EACnB,IAAM/T,GAAqBE,IAG9C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}