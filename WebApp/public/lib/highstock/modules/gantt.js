!/**
 * Highcharts Gantt JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/pathfinder
 * @requires highcharts
 *
 * Pathfinder
 *
 * (c) 2016-2025 Øystein Moseng
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.Chart,t._Highcharts.Axis,t._Highcharts.Color,t._Highcharts.SeriesRegistry,t._Highcharts.RendererRegistry,t._Highcharts.SVGRenderer,t._Highcharts.SVGElement,t._Highcharts.Templating,t._Highcharts.Point,t._Highcharts.StackItem):"function"==typeof define&&define.amd?define("highcharts/modules/gantt",["highcharts/highcharts"],function(t){return e(t,t.Chart,t.Axis,t.Color,t.SeriesRegistry,t.<PERSON>,t<PERSON>,t.<PERSON>,t.Te<PERSON>lating,t.Point,t.StackItem)}):"object"==typeof exports?exports["highcharts/modules/gantt"]=e(t._Highcharts,t._Highcharts.Chart,t._Highcharts.Axis,t._Highcharts.Color,t._Highcharts.SeriesRegistry,t._Highcharts.RendererRegistry,t._Highcharts.SVGRenderer,t._Highcharts.SVGElement,t._Highcharts.Templating,t._Highcharts.Point,t._Highcharts.StackItem):t.Highcharts=e(t.Highcharts,t.Highcharts.Chart,t.Highcharts.Axis,t.Highcharts.Color,t.Highcharts.SeriesRegistry,t.Highcharts.RendererRegistry,t.Highcharts.SVGRenderer,t.Highcharts.SVGElement,t.Highcharts.Templating,t.Highcharts.Point,t.Highcharts.StackItem)}("undefined"==typeof window?this:window,(t,e,i,s,o,r,n,a,l,h,d)=>(()=>{"use strict";let c,p,u;var g,x,f,m,b,y,v={28:t=>{t.exports=a},184:t=>{t.exports=d},260:t=>{t.exports=h},512:t=>{t.exports=o},532:t=>{t.exports=i},540:t=>{t.exports=n},608:t=>{t.exports=r},620:t=>{t.exports=s},944:e=>{e.exports=t},960:t=>{t.exports=e},984:t=>{t.exports=l}},M={};function k(t){var e=M[t];if(void 0!==e)return e.exports;var i=M[t]={exports:{}};return v[t](i,i.exports,k),i.exports}k.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return k.d(e,{a:e}),e},k.d=(t,e)=>{for(var i in e)k.o(e,i)&&!k.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},k.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var A={};k.d(A,{default:()=>oJ});var w=k(944),P=k.n(w);function S(t,e,i,s){return[["M",t,e+s/2],["L",t+i,e],["L",t,e+s/2],["L",t+i,e+s]]}function O(t,e,i,s){return S(t,e,i/2,s)}function E(t,e,i,s){return[["M",t+i,e],["L",t,e+s/2],["L",t+i,e+s],["Z"]]}function B(t,e,i,s){return E(t,e,i/2,s)}let T={compose:function(t){let e=t.prototype.symbols;e.arrow=S,e["arrow-filled"]=E,e["arrow-filled-half"]=B,e["arrow-half"]=O,e["triangle-left"]=E,e["triangle-left-half"]=B}},{defined:C,error:I,merge:R,objectEach:D}=P(),L=P().deg2rad,G=Math.max,z=Math.min,N=class{constructor(t,e,i){this.init(t,e,i)}init(t,e,i){this.fromPoint=t,this.toPoint=e,this.options=i,this.chart=t.series.chart,this.pathfinder=this.chart.pathfinder}renderPath(t,e){let i=this.chart,s=i.styledMode,o=this.pathfinder,r={},n=this.graphics&&this.graphics.path;o.group||(o.group=i.renderer.g().addClass("highcharts-pathfinder-group").attr({zIndex:-1}).add(i.seriesGroup)),o.group.translate(i.plotLeft,i.plotTop),!(n&&n.renderer)&&(n=i.renderer.path().add(o.group),s||n.attr({opacity:0})),n.attr(e),r.d=t,s||(r.opacity=1),n.animate(r),this.graphics=this.graphics||{},this.graphics.path=n}addMarker(t,e,i){let s,o,r,n,a,l,h,d=this.fromPoint.series.chart,c=d.pathfinder,p=d.renderer,u="start"===t?this.fromPoint:this.toPoint,g=u.getPathfinderAnchorPoint(e);e.enabled&&((h="start"===t?i[1]:i[i.length-2])&&"M"===h[0]||"L"===h[0])&&(l={x:h[1],y:h[2]},o=u.getRadiansToVector(l,g),s=u.getMarkerVector(o,e.radius,g),e.width&&e.height?(n=e.width,a=e.height):n=a=2*e.radius,this.graphics=this.graphics||{},r={x:s.x-n/2,y:s.y-a/2,width:n,height:a,rotation:-o/L,rotationOriginX:s.x,rotationOriginY:s.y},this.graphics[t]?this.graphics[t].animate(r):(this.graphics[t]=p.symbol(e.symbol).addClass("highcharts-point-connecting-path-"+t+"-marker highcharts-color-"+this.fromPoint.colorIndex).attr(r).add(c.group),p.styledMode||this.graphics[t].attr({fill:e.color||this.fromPoint.color,stroke:e.lineColor,"stroke-width":e.lineWidth,opacity:0}).animate({opacity:1},u.series.options.animation)))}getPath(t){let e=this.pathfinder,i=this.chart,s=e.algorithms[t.type],o=e.chartObstacles;return"function"!=typeof s?(I('"'+t.type+'" is not a Pathfinder algorithm.'),{path:[],obstacles:[]}):(s.requiresObstacles&&!o&&(o=e.chartObstacles=e.getChartObstacles(t),i.options.connectors.algorithmMargin=t.algorithmMargin,e.chartObstacleMetrics=e.getObstacleMetrics(o)),s(this.fromPoint.getPathfinderAnchorPoint(t.startMarker),this.toPoint.getPathfinderAnchorPoint(t.endMarker),R({chartObstacles:o,lineObstacles:e.lineObstacles||[],obstacleMetrics:e.chartObstacleMetrics,hardBounds:{xMin:0,xMax:i.plotWidth,yMin:0,yMax:i.plotHeight},obstacleOptions:{margin:t.algorithmMargin},startDirectionX:e.getAlgorithmStartDirection(t.startMarker)},t)))}render(){let t=this.fromPoint,e=t.series,i=e.chart,s=i.pathfinder,o={},r=R(i.options.connectors,e.options.connectors,t.options.connectors,this.options);!i.styledMode&&(o.stroke=r.lineColor||t.color,o["stroke-width"]=r.lineWidth,r.dashStyle&&(o.dashstyle=r.dashStyle)),o.class="highcharts-point-connecting-path highcharts-color-"+t.colorIndex,C((r=R(o,r)).marker.radius)||(r.marker.radius=z(G(Math.ceil((r.algorithmMargin||8)/2)-1,1),5));let n=this.getPath(r),a=n.path;n.obstacles&&(s.lineObstacles=s.lineObstacles||[],s.lineObstacles=s.lineObstacles.concat(n.obstacles)),this.renderPath(a,o),this.addMarker("start",R(r.marker,r.startMarker),a),this.addMarker("end",R(r.marker,r.endMarker),a)}destroy(){this.graphics&&(D(this.graphics,function(t){t.destroy()}),delete this.graphics)}},{composed:W}=P(),{addEvent:H,merge:F,pushUnique:U,wrap:X}=P(),Y={color:"#ccd3ff",width:2,label:{format:"%[abdYHM]",formatter:function(t,e){return this.axis.chart.time.dateFormat(e||"",t,!0)},rotation:0,style:{fontSize:"0.7em"}}};function V(){let t=this.options,e=t.currentDateIndicator;if(e){let i="object"==typeof e?F(Y,e):F(Y);i.value=Date.now(),i.className="highcharts-current-date-indicator",t.plotLines||(t.plotLines=[]),t.plotLines.push(i)}}function _(){this.label&&this.label.attr({text:this.getLabelText(this.options.label)})}function j(t,e){let i=this.options;return i&&i.className&&-1!==i.className.indexOf("highcharts-current-date-indicator")&&i.label&&"function"==typeof i.label.formatter?(i.value=Date.now(),i.label.formatter.call(this,i.value,i.label.format)):t.call(this,e)}var q=k(960),Z=k.n(q);let{defaultOptions:$}=P(),{isArray:K,merge:J,splat:Q}=P();class tt extends Z(){init(t,e){let i,s=t.xAxis,o=t.yAxis;t.xAxis=t.yAxis=void 0;let r=J(!0,{chart:{type:"gantt"},title:{text:""},legend:{enabled:!1},navigator:{series:{type:"gantt"},yAxis:{type:"category"}}},t,{isGantt:!0});t.xAxis=s,t.yAxis=o,r.xAxis=(!K(t.xAxis)?[t.xAxis||{},{}]:t.xAxis).map((t,e)=>(1===e&&(i=0),J({grid:{borderColor:$.xAxis?.grid?.borderColor||"#cccccc",enabled:!0},opposite:$.xAxis?.opposite??t.opposite??!0,linkedTo:i},t,{type:"datetime"}))),r.yAxis=Q(t.yAxis||{}).map(t=>J({grid:{borderColor:$.yAxis?.grid?.borderColor||"#cccccc",enabled:!0},staticScale:50,reversed:!0,type:t.categories?t.type:"treegrid"},t)),super.init(r,e)}}!function(t){t.ganttChart=function(e,i,s){return new t(e,i,s)}}(tt||(tt={}));let te=tt;var ti=k(532),ts=k.n(ti);let{isTouchDevice:to}=P(),{addEvent:tr,merge:tn,pick:ta}=P(),tl=[];function th(){this.navigator&&this.navigator.setBaseSeries(null,!1)}function td(){let t,e,i,s=this.legend,o=this.navigator;if(o){t=s&&s.options,e=o.xAxis,i=o.yAxis;let{scrollbarHeight:r,scrollButtonSize:n}=o;this.inverted?(o.left=o.opposite?this.chartWidth-r-o.height:this.spacing[3]+r,o.top=this.plotTop+n):(o.left=ta(e.left,this.plotLeft+n),o.top=o.navigatorOptions.top||this.chartHeight-o.height-r-(this.scrollbar?.options.margin||0)-this.spacing[2]-(this.rangeSelector&&this.extraBottomMargin?this.rangeSelector.getHeight():0)-(t&&"bottom"===t.verticalAlign&&"proximate"!==t.layout&&t.enabled&&!t.floating?s.legendHeight+ta(t.margin,10):0)-(this.titleOffset?this.titleOffset[2]:0)),e&&i&&(this.inverted?e.options.left=i.options.left=o.left:e.options.top=i.options.top=o.top,e.setAxisSize(),i.setAxisSize())}}function tc(t){!this.navigator&&!this.scroller&&(this.options.navigator.enabled||this.options.scrollbar.enabled)&&(this.scroller=this.navigator=new c(this),ta(t.redraw,!0)&&this.redraw(t.animation))}function tp(){let t=this.options;(t.navigator.enabled||t.scrollbar.enabled)&&(this.scroller=this.navigator=new c(this))}function tu(){let t=this.options,e=t.navigator,i=t.rangeSelector;if((e&&e.enabled||i&&i.enabled)&&(!to&&"x"===this.zooming.type||to&&"x"===this.zooming.pinchType))return!1}function tg(t){let e=t.navigator;if(e&&t.xAxis[0]){let i=t.xAxis[0].getExtremes();e.render(i.min,i.max)}}function tx(t){let e=t.options.navigator||{},i=t.options.scrollbar||{};!this.navigator&&!this.scroller&&(e.enabled||i.enabled)&&(tn(!0,this.options.navigator,e),tn(!0,this.options.scrollbar,i),delete t.options.navigator,delete t.options.scrollbar)}let tf={compose:function(t,e){if(P().pushUnique(tl,t)){let i=t.prototype;c=e,i.callbacks.push(tg),tr(t,"afterAddSeries",th),tr(t,"afterSetChartSize",td),tr(t,"afterUpdate",tc),tr(t,"beforeRender",tp),tr(t,"beforeShowResetZoom",tu),tr(t,"update",tx)}}},{isTouchDevice:tm}=P(),{addEvent:tb,correctFloat:ty,defined:tv,isNumber:tM,pick:tk}=P();function tA(){this.navigatorAxis||(this.navigatorAxis=new tP(this))}function tw(t){let e,i=this.chart,s=i.options,o=s.navigator,r=this.navigatorAxis,n=i.zooming.pinchType,a=s.rangeSelector,l=i.zooming.type;if(this.isXAxis&&(o?.enabled||a?.enabled)){if("y"===l&&"zoom"===t.trigger)e=!1;else if(("zoom"===t.trigger&&"xy"===l||tm&&"xy"===n)&&this.options.range){let e=r.previousZoom;tv(t.min)?r.previousZoom=[this.min,this.max]:e&&(t.min=e[0],t.max=e[1],r.previousZoom=void 0)}}void 0!==e&&t.preventDefault()}class tP{static compose(t){t.keepProps.includes("navigatorAxis")||(t.keepProps.push("navigatorAxis"),tb(t,"init",tA),tb(t,"setExtremes",tw))}constructor(t){this.axis=t}destroy(){this.axis=void 0}toFixedRange(t,e,i,s){let o=this.axis,r=(o.pointRange||0)/2,n=tk(i,o.translate(t,!0,!o.horiz)),a=tk(s,o.translate(e,!0,!o.horiz));return tv(i)||(n=ty(n+r)),tv(s)||(a=ty(a-r)),tM(n)&&tM(a)||(n=a=void 0),{min:n,max:a}}}var tS=k(620),tO=k.n(tS),tE=k(512),tB=k.n(tE);let{parse:tT}=tO(),{seriesTypes:tC}=tB(),tI={height:40,margin:22,maskInside:!0,handles:{width:7,borderRadius:0,height:15,symbols:["navigator-handle","navigator-handle"],enabled:!0,lineWidth:1,backgroundColor:"#f2f2f2",borderColor:"#999999"},maskFill:tT("#667aff").setOpacity(.3).get(),outlineColor:"#999999",outlineWidth:1,series:{type:void 0===tC.areaspline?"line":"areaspline",fillOpacity:.05,lineWidth:1,compare:null,sonification:{enabled:!1},dataGrouping:{approximation:"average",enabled:!0,groupPixelWidth:2,firstAnchor:"firstPoint",anchor:"middle",lastAnchor:"lastPoint",units:[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1,2,3,4]],["week",[1,2,3]],["month",[1,3,6]],["year",null]]},dataLabels:{enabled:!1,zIndex:2},id:"highcharts-navigator-series",className:"highcharts-navigator-series",lineColor:null,marker:{enabled:!1},threshold:null},xAxis:{className:"highcharts-navigator-xaxis",tickLength:0,lineWidth:0,gridLineColor:"#e6e6e6",id:"navigator-x-axis",gridLineWidth:1,tickPixelInterval:200,labels:{align:"left",style:{color:"#000000",fontSize:"0.7em",opacity:.6,textOutline:"2px contrast"},x:3,y:-4},crosshair:!1},yAxis:{className:"highcharts-navigator-yaxis",gridLineWidth:0,startOnTick:!1,endOnTick:!1,minPadding:.1,id:"navigator-y-axis",maxPadding:.1,labels:{enabled:!1},crosshair:!1,title:{text:void 0},tickLength:0,tickWidth:0}},{defined:tR,isNumber:tD,pick:tL}=P(),tG={rect:function(t,e,i,s,o){return o?.r?function(t,e,i,s,o){let r=o?.r||0;return[["M",t+r,e],["L",t+i-r,e],["A",r,r,0,0,1,t+i,e+r],["L",t+i,e+s-r],["A",r,r,0,0,1,t+i-r,e+s],["L",t+r,e+s],["A",r,r,0,0,1,t,e+s-r],["L",t,e+r],["A",r,r,0,0,1,t+r,e],["Z"]]}(t,e,i,s,o):[["M",t,e],["L",t+i,e],["L",t+i,e+s],["L",t,e+s],["Z"]]}},{relativeLength:tz}=P(),tN={"navigator-handle":function(t,e,i,s,o={}){let r=o.width?o.width/2:i,n=tz(o.borderRadius||0,Math.min(2*r,s));return[["M",-1.5,(s=o.height||s)/2-3.5],["L",-1.5,s/2+4.5],["M",.5,s/2-3.5],["L",.5,s/2+4.5],...tG.rect(-r-1,.5,2*r+1,s,{r:n})]}};var tW=k(608),tH=k.n(tW);let{defined:tF}=P(),{defaultOptions:tU}=P(),{composed:tX}=P(),{getRendererType:tY}=tH(),{setFixedRange:tV}={setFixedRange:function(t){let e=this.xAxis[0];tF(e.dataMax)&&tF(e.dataMin)&&t?this.fixedRange=Math.min(t,e.dataMax-e.dataMin):this.fixedRange=t}},{addEvent:t_,extend:tj,pushUnique:tq}=P();function tZ(){this.chart.navigator&&!this.options.isInternal&&this.chart.navigator.setBaseSeries(null,!1)}let t$={compose:function(t,e,i){tP.compose(e),tq(tX,"Navigator")&&(t.prototype.setFixedRange=tV,tj(tY().prototype.symbols,tN),tj(tU,{navigator:tI}),t_(i,"afterUpdate",tZ))}},{composed:tK}=P(),{addEvent:tJ,defined:tQ,pick:t0,pushUnique:t1}=P();!function(t){let e;function i(t){let e=t0(t.options?.min,t.min),i=t0(t.options?.max,t.max);return{axisMin:e,axisMax:i,scrollMin:tQ(t.dataMin)?Math.min(e,t.min,t.dataMin,t0(t.threshold,1/0)):e,scrollMax:tQ(t.dataMax)?Math.max(i,t.max,t.dataMax,t0(t.threshold,-1/0)):i}}function s(){let t=this.scrollbar,e=t&&!t.options.opposite,i=this.horiz?2:e?3:1;t&&(this.chart.scrollbarsOffsets=[0,0],this.chart.axisOffset[i]+=t.size+(t.options.margin||0))}function o(){let t=this;t.options?.scrollbar?.enabled&&(t.options.scrollbar.vertical=!t.horiz,t.options.startOnTick=t.options.endOnTick=!1,t.scrollbar=new e(t.chart.renderer,t.options.scrollbar,t.chart),tJ(t.scrollbar,"changed",function(e){let s,o,{axisMin:r,axisMax:n,scrollMin:a,scrollMax:l}=i(t),h=l-a;if(tQ(r)&&tQ(n))if(t.horiz&&!t.reversed||!t.horiz&&t.reversed?(s=a+h*this.to,o=a+h*this.from):(s=a+h*(1-this.from),o=a+h*(1-this.to)),this.shouldUpdateExtremes(e.DOMType)){let i="mousemove"!==e.DOMType&&"touchmove"!==e.DOMType&&void 0;t.setExtremes(o,s,!0,i,e)}else this.setRange(this.from,this.to)}))}function r(){let t,e,s,{scrollMin:o,scrollMax:r}=i(this),n=this.scrollbar,a=this.axisTitleMargin+(this.titleOffset||0),l=this.chart.scrollbarsOffsets,h=this.options.margin||0;if(n&&l){if(this.horiz)this.opposite||(l[1]+=a),n.position(this.left,this.top+this.height+2+l[1]-(this.opposite?h:0),this.width,this.height),this.opposite||(l[1]+=h),t=1;else{let e;this.opposite&&(l[0]+=a),e=n.options.opposite?this.left+this.width+2+l[0]-(this.opposite?0:h):this.opposite?0:h,n.position(e,this.top,this.width,this.height),this.opposite&&(l[0]+=h),t=0}if(l[t]+=n.size+(n.options.margin||0),isNaN(o)||isNaN(r)||!tQ(this.min)||!tQ(this.max)||this.dataMin===this.dataMax)n.setRange(0,1);else if(this.min===this.max){let t=this.pointRange/(this.dataMax+1);e=t*this.min,s=t*(this.max+1),n.setRange(e,s)}else e=(this.min-o)/(r-o),s=(this.max-o)/(r-o),this.horiz&&!this.reversed||!this.horiz&&this.reversed?n.setRange(e,s):n.setRange(1-s,1-e)}}t.compose=function(t,i){t1(tK,"Axis.Scrollbar")&&(e=i,tJ(t,"afterGetOffset",s),tJ(t,"afterInit",o),tJ(t,"afterRender",r))}}(g||(g={}));let t2=g,t3={height:10,barBorderRadius:5,buttonBorderRadius:0,buttonsEnabled:!1,liveRedraw:void 0,margin:void 0,minWidth:6,opposite:!0,step:.2,zIndex:3,barBackgroundColor:"#cccccc",barBorderWidth:0,barBorderColor:"#cccccc",buttonArrowColor:"#333333",buttonBackgroundColor:"#e6e6e6",buttonBorderColor:"#cccccc",buttonBorderWidth:1,rifleColor:"none",trackBackgroundColor:"rgba(255, 255, 255, 0.001)",trackBorderColor:"#cccccc",trackBorderRadius:5,trackBorderWidth:1},{defaultOptions:t5}=P(),{composed:t6}=P(),{addEvent:t4,correctFloat:t8,crisp:t9,defined:t7,destroyObjectProperties:et,extend:ee,fireEvent:ei,merge:es,pick:eo,pushUnique:er,removeEvent:en}=P();class ea{static compose(t){t2.compose(t,ea),er(t6,"Scrollbar")&&ee(t5,{scrollbar:t3})}static swapXY(t,e){return e&&t.forEach(t=>{let e,i=t.length;for(let s=0;s<i;s+=2)"number"==typeof(e=t[s+1])&&(t[s+1]=t[s+2],t[s+2]=e)}),t}constructor(t,e,i){this._events=[],this.chartX=0,this.chartY=0,this.from=0,this.scrollbarButtons=[],this.scrollbarLeft=0,this.scrollbarStrokeWidth=1,this.scrollbarTop=0,this.size=0,this.to=0,this.trackBorderWidth=1,this.x=0,this.y=0,this.init(t,e,i)}addEvents(){let t=this.options.inverted?[1,0]:[0,1],e=this.scrollbarButtons,i=this.scrollbarGroup.element,s=this.track.element,o=this.mouseDownHandler.bind(this),r=this.mouseMoveHandler.bind(this),n=this.mouseUpHandler.bind(this),a=[[e[t[0]].element,"click",this.buttonToMinClick.bind(this)],[e[t[1]].element,"click",this.buttonToMaxClick.bind(this)],[s,"click",this.trackClick.bind(this)],[i,"mousedown",o],[i.ownerDocument,"mousemove",r],[i.ownerDocument,"mouseup",n],[i,"touchstart",o],[i.ownerDocument,"touchmove",r],[i.ownerDocument,"touchend",n]];a.forEach(function(t){t4.apply(null,t)}),this._events=a}buttonToMaxClick(t){let e=(this.to-this.from)*eo(this.options.step,.2);this.updatePosition(this.from+e,this.to+e),ei(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})}buttonToMinClick(t){let e=t8(this.to-this.from)*eo(this.options.step,.2);this.updatePosition(t8(this.from-e),t8(this.to-e)),ei(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})}cursorToScrollbarPosition(t){let e=this.options,i=e.minWidth>this.calculatedWidth?e.minWidth:0;return{chartX:(t.chartX-this.x-this.xOffset)/(this.barWidth-i),chartY:(t.chartY-this.y-this.yOffset)/(this.barWidth-i)}}destroy(){let t=this,e=t.chart.scroller;t.removeEvents(),["track","scrollbarRifles","scrollbar","scrollbarGroup","group"].forEach(function(e){t[e]&&t[e].destroy&&(t[e]=t[e].destroy())}),e&&t===e.scrollbar&&(e.scrollbar=null,et(e.scrollbarButtons))}drawScrollbarButton(t){let e=this.renderer,i=this.scrollbarButtons,s=this.options,o=this.size,r=e.g().add(this.group);if(i.push(r),s.buttonsEnabled){let n=e.rect().addClass("highcharts-scrollbar-button").add(r);this.chart.styledMode||n.attr({stroke:s.buttonBorderColor,"stroke-width":s.buttonBorderWidth,fill:s.buttonBackgroundColor}),n.attr(n.crisp({x:-.5,y:-.5,width:o,height:o,r:s.buttonBorderRadius},n.strokeWidth()));let a=e.path(ea.swapXY([["M",o/2+(t?-1:1),o/2-3],["L",o/2+(t?-1:1),o/2+3],["L",o/2+(t?2:-2),o/2]],s.vertical)).addClass("highcharts-scrollbar-arrow").add(i[t]);this.chart.styledMode||a.attr({fill:s.buttonArrowColor})}}init(t,e,i){this.scrollbarButtons=[],this.renderer=t,this.userOptions=e,this.options=es(t3,t5.scrollbar,e),this.options.margin=eo(this.options.margin,10),this.chart=i,this.size=eo(this.options.size,this.options.height),e.enabled&&(this.render(),this.addEvents())}mouseDownHandler(t){let e=this.chart.pointer?.normalize(t)||t,i=this.cursorToScrollbarPosition(e);this.chartX=i.chartX,this.chartY=i.chartY,this.initPositions=[this.from,this.to],this.grabbedCenter=!0}mouseMoveHandler(t){let e,i=this.chart.pointer?.normalize(t)||t,s=this.options.vertical?"chartY":"chartX",o=this.initPositions||[];this.grabbedCenter&&(!t.touches||0!==t.touches[0][s])&&(e=this.cursorToScrollbarPosition(i)[s]-this[s],this.hasDragged=!0,this.updatePosition(o[0]+e,o[1]+e),this.hasDragged&&ei(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMType:t.type,DOMEvent:t}))}mouseUpHandler(t){this.hasDragged&&ei(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMType:t.type,DOMEvent:t}),this.grabbedCenter=this.hasDragged=this.chartX=this.chartY=null}position(t,e,i,s){let{buttonsEnabled:o,margin:r=0,vertical:n}=this.options,a=this.rendered?"animate":"attr",l=s,h=0;this.group.show(),this.x=t,this.y=e+this.trackBorderWidth,this.width=i,this.height=s,this.xOffset=l,this.yOffset=h,n?(this.width=this.yOffset=i=h=this.size,this.xOffset=l=0,this.yOffset=h=o?this.size:0,this.barWidth=s-(o?2*i:0),this.x=t+=r):(this.height=s=this.size,this.xOffset=l=o?this.size:0,this.barWidth=i-(o?2*s:0),this.y=this.y+r),this.group[a]({translateX:t,translateY:this.y}),this.track[a]({width:i,height:s}),this.scrollbarButtons[1][a]({translateX:n?0:i-l,translateY:n?s-h:0})}removeEvents(){this._events.forEach(function(t){en.apply(null,t)}),this._events.length=0}render(){let t=this.renderer,e=this.options,i=this.size,s=this.chart.styledMode,o=t.g("scrollbar").attr({zIndex:e.zIndex}).hide().add();this.group=o,this.track=t.rect().addClass("highcharts-scrollbar-track").attr({r:e.trackBorderRadius||0,height:i,width:i}).add(o),s||this.track.attr({fill:e.trackBackgroundColor,stroke:e.trackBorderColor,"stroke-width":e.trackBorderWidth});let r=this.trackBorderWidth=this.track.strokeWidth();this.track.attr({x:-t9(0,r),y:-t9(0,r)}),this.scrollbarGroup=t.g().add(o),this.scrollbar=t.rect().addClass("highcharts-scrollbar-thumb").attr({height:i-r,width:i-r,r:e.barBorderRadius||0}).add(this.scrollbarGroup),this.scrollbarRifles=t.path(ea.swapXY([["M",-3,i/4],["L",-3,2*i/3],["M",0,i/4],["L",0,2*i/3],["M",3,i/4],["L",3,2*i/3]],e.vertical)).addClass("highcharts-scrollbar-rifles").add(this.scrollbarGroup),s||(this.scrollbar.attr({fill:e.barBackgroundColor,stroke:e.barBorderColor,"stroke-width":e.barBorderWidth}),this.scrollbarRifles.attr({stroke:e.rifleColor,"stroke-width":1})),this.scrollbarStrokeWidth=this.scrollbar.strokeWidth(),this.scrollbarGroup.translate(-t9(0,this.scrollbarStrokeWidth),-t9(0,this.scrollbarStrokeWidth)),this.drawScrollbarButton(0),this.drawScrollbarButton(1)}setRange(t,e){let i,s,o=this.options,r=o.vertical,n=o.minWidth,a=this.barWidth,l=!this.rendered||this.hasDragged||this.chart.navigator&&this.chart.navigator.hasDragged?"attr":"animate";if(!t7(a))return;let h=a*Math.min(e,1);i=Math.ceil(a*(t=Math.max(t,0))),this.calculatedWidth=s=t8(h-i),s<n&&(i=(a-n+s)*t,s=n);let d=Math.floor(i+this.xOffset+this.yOffset),c=s/2-.5;this.from=t,this.to=e,r?(this.scrollbarGroup[l]({translateY:d}),this.scrollbar[l]({height:s}),this.scrollbarRifles[l]({translateY:c}),this.scrollbarTop=d,this.scrollbarLeft=0):(this.scrollbarGroup[l]({translateX:d}),this.scrollbar[l]({width:s}),this.scrollbarRifles[l]({translateX:c}),this.scrollbarLeft=d,this.scrollbarTop=0),s<=12?this.scrollbarRifles.hide():this.scrollbarRifles.show(),!1===o.showFull&&(t<=0&&e>=1?this.group.hide():this.group.show()),this.rendered=!0}shouldUpdateExtremes(t){return eo(this.options.liveRedraw,P().svg&&!P().isTouchDevice&&!this.chart.boosted)||"mouseup"===t||"touchend"===t||!t7(t)}trackClick(t){let e=this.chart.pointer?.normalize(t)||t,i=this.to-this.from,s=this.y+this.scrollbarTop,o=this.x+this.scrollbarLeft;this.options.vertical&&e.chartY>s||!this.options.vertical&&e.chartX>o?this.updatePosition(this.from+i,this.to+i):this.updatePosition(this.from-i,this.to-i),ei(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})}update(t){this.destroy(),this.init(this.chart.renderer,es(!0,this.options,t),this.chart)}updatePosition(t,e){e>1&&(t=t8(1-t8(e-t)),e=1),t<0&&(e=t8(e-t),t=0),this.from=t,this.to=e}}ea.defaultOptions=t3;var el=k(540),eh=k.n(el);let{defaultOptions:ed}=P(),{isTouchDevice:ec}=P(),{prototype:{symbols:ep}}=eh(),{addEvent:eu,clamp:eg,correctFloat:ex,defined:ef,destroyObjectProperties:em,erase:eb,extend:ey,find:ev,fireEvent:eM,isArray:ek,isNumber:eA,merge:ew,pick:eP,removeEvent:eS,splat:eO}=P();function eE(t,...e){let i=[].filter.call(e,eA);if(i.length)return Math[t].apply(0,i)}class eB{static compose(t,e,i){tf.compose(t,eB),t$.compose(t,e,i)}constructor(t){this.isDirty=!1,this.scrollbarHeight=0,this.init(t)}drawHandle(t,e,i,s){let o=this.navigatorOptions.handles.height;this.handles[e][s](i?{translateX:Math.round(this.left+this.height/2),translateY:Math.round(this.top+parseInt(t,10)+.5-o)}:{translateX:Math.round(this.left+parseInt(t,10)),translateY:Math.round(this.top+this.height/2-o/2-1)})}drawOutline(t,e,i,s){let o=this.navigatorOptions.maskInside,r=this.outline.strokeWidth(),n=r/2,a=r%2/2,l=this.scrollButtonSize,h=this.size,d=this.top,c=this.height,p=d-n,u=d+c,g=this.left,x,f;i?(x=d+e+a,e=d+t+a,f=[["M",g+c,d-l-a],["L",g+c,x],["L",g,x],["M",g,e],["L",g+c,e],["L",g+c,d+h+l]],o&&f.push(["M",g+c,x-n],["L",g+c,e+n])):(g-=l,t+=g+l-a,e+=g+l-a,f=[["M",g,p],["L",t,p],["L",t,u],["M",e,u],["L",e,p],["L",g+h+2*l,p]],o&&f.push(["M",t-n,p],["L",e+n,p])),this.outline[s]({d:f})}drawMasks(t,e,i,s){let o,r,n,a,l=this.left,h=this.top,d=this.height;i?(n=[l,l,l],a=[h,h+t,h+e],r=[d,d,d],o=[t,e-t,this.size-e]):(n=[l,l+t,l+e],a=[h,h,h],r=[t,e-t,this.size-e],o=[d,d,d]),this.shades.forEach((t,e)=>{t[s]({x:n[e],y:a[e],width:r[e],height:o[e]})})}renderElements(){let t=this,e=t.navigatorOptions,i=e.maskInside,s=t.chart,o=s.inverted,r=s.renderer,n={cursor:o?"ns-resize":"ew-resize"},a=t.navigatorGroup??(t.navigatorGroup=r.g("navigator").attr({zIndex:8,visibility:"hidden"}).add());if([!i,i,!i].forEach((i,o)=>{let l=t.shades[o]??(t.shades[o]=r.rect().addClass("highcharts-navigator-mask"+(1===o?"-inside":"-outside")).add(a));s.styledMode||(l.attr({fill:i?e.maskFill:"rgba(0,0,0,0)"}),1===o&&l.css(n))}),t.outline||(t.outline=r.path().addClass("highcharts-navigator-outline").add(a)),s.styledMode||t.outline.attr({"stroke-width":e.outlineWidth,stroke:e.outlineColor}),e.handles?.enabled){let i=e.handles,{height:o,width:l}=i;[0,1].forEach(e=>{let h=i.symbols[e];if(t.handles[e]&&t.handles[e].symbolUrl===h){if(!t.handles[e].isImg&&t.handles[e].symbolName!==h){let i=ep[h].call(ep,-l/2-1,0,l,o);t.handles[e].attr({d:i}),t.handles[e].symbolName=h}}else t.handles[e]?.destroy(),t.handles[e]=r.symbol(h,-l/2-1,0,l,o,i),t.handles[e].attr({zIndex:7-e}).addClass("highcharts-navigator-handle highcharts-navigator-handle-"+["left","right"][e]).add(a),t.addMouseEvents();s.inverted&&t.handles[e].attr({rotation:90,rotationOriginX:Math.floor(-l/2),rotationOriginY:(o+l)/2}),s.styledMode||t.handles[e].attr({fill:i.backgroundColor,stroke:i.borderColor,"stroke-width":i.lineWidth,width:i.width,height:i.height,x:-l/2-1,y:0}).css(n)})}}update(t,e=!1){let i=this.chart,s=i.options.chart.inverted!==i.scrollbar?.options.vertical;if(ew(!0,i.options.navigator,t),this.navigatorOptions=i.options.navigator||{},this.setOpposite(),ef(t.enabled)||s)return this.destroy(),this.navigatorEnabled=t.enabled||this.navigatorEnabled,this.init(i);if(this.navigatorEnabled&&(this.isDirty=!0,!1===t.adaptToUpdatedData&&this.baseSeries.forEach(t=>{eS(t,"updatedData",this.updatedDataHandler)},this),t.adaptToUpdatedData&&this.baseSeries.forEach(t=>{t.eventsToUnbind.push(eu(t,"updatedData",this.updatedDataHandler))},this),(t.series||t.baseSeries)&&this.setBaseSeries(void 0,!1),t.height||t.xAxis||t.yAxis)){this.height=t.height??this.height;let e=this.getXAxisOffsets();this.xAxis.update({...t.xAxis,offsets:e,[i.inverted?"width":"height"]:this.height,[i.inverted?"height":"width"]:void 0},!1),this.yAxis.update({...t.yAxis,[i.inverted?"width":"height"]:this.height},!1)}e&&i.redraw()}render(t,e,i,s){let o=this.chart,r=this.xAxis,n=r.pointRange||0,a=r.navigatorAxis.fake?o.xAxis[0]:r,l=this.navigatorEnabled,h=this.rendered,d=o.inverted,c=o.xAxis[0].minRange,p=o.xAxis[0].options.maxRange,u=this.scrollButtonSize,g,x,f,m=this.scrollbarHeight,b,y;if(this.hasDragged&&!ef(i))return;if(this.isDirty&&this.renderElements(),t=ex(t-n/2),e=ex(e+n/2),!eA(t)||!eA(e))if(!h)return;else i=0,s=eP(r.width,a.width);this.left=eP(r.left,o.plotLeft+u+(d?o.plotWidth:0));let v=this.size=b=eP(r.len,(d?o.plotHeight:o.plotWidth)-2*u);g=d?m:b+2*u,i=eP(i,r.toPixels(t,!0)),s=eP(s,r.toPixels(e,!0)),eA(i)&&Math.abs(i)!==1/0||(i=0,s=g);let M=r.toValue(i,!0),k=r.toValue(s,!0),A=Math.abs(ex(k-M));A<c?this.grabbedLeft?i=r.toPixels(k-c-n,!0):this.grabbedRight&&(s=r.toPixels(M+c+n,!0)):ef(p)&&ex(A-n)>p&&(this.grabbedLeft?i=r.toPixels(k-p-n,!0):this.grabbedRight&&(s=r.toPixels(M+p+n,!0))),this.zoomedMax=eg(Math.max(i,s),0,v),this.zoomedMin=eg(this.fixedWidth?this.zoomedMax-this.fixedWidth:Math.min(i,s),0,v),this.range=this.zoomedMax-this.zoomedMin,v=Math.round(this.zoomedMax);let w=Math.round(this.zoomedMin);l&&(this.navigatorGroup.attr({visibility:"inherit"}),y=h&&!this.hasDragged?"animate":"attr",this.drawMasks(w,v,d,y),this.drawOutline(w,v,d,y),this.navigatorOptions.handles.enabled&&(this.drawHandle(w,0,d,y),this.drawHandle(v,1,d,y))),this.scrollbar&&(d?(f=this.top-u,x=this.left-m+(l||!a.opposite?0:(a.titleOffset||0)+a.axisTitleMargin),m=b+2*u):(f=this.top+(l?this.height:-m),x=this.left-u),this.scrollbar.position(x,f,g,m),this.scrollbar.setRange(this.zoomedMin/(b||1),this.zoomedMax/(b||1))),this.rendered=!0,this.isDirty=!1,eM(this,"afterRender")}addMouseEvents(){let t=this,e=t.chart,i=e.container,s=[],o,r;t.mouseMoveHandler=o=function(e){t.onMouseMove(e)},t.mouseUpHandler=r=function(e){t.onMouseUp(e)},(s=t.getPartsEvents("mousedown")).push(eu(e.renderTo,"mousemove",o),eu(i.ownerDocument,"mouseup",r),eu(e.renderTo,"touchmove",o),eu(i.ownerDocument,"touchend",r)),s.concat(t.getPartsEvents("touchstart")),t.eventsToUnbind=s,t.series&&t.series[0]&&s.push(eu(t.series[0].xAxis,"foundExtremes",function(){e.navigator.modifyNavigatorAxisExtremes()}))}getPartsEvents(t){let e=this,i=[];return["shades","handles"].forEach(function(s){e[s].forEach(function(o,r){i.push(eu(o.element,t,function(t){e[s+"Mousedown"](t,r)}))})}),i}shadesMousedown(t,e){t=this.chart.pointer?.normalize(t)||t;let i=this.chart,s=this.xAxis,o=this.zoomedMin,r=this.size,n=this.range,a=this.left,l=t.chartX,h,d,c,p;i.inverted&&(l=t.chartY,a=this.top),1===e?(this.grabbedCenter=l,this.fixedWidth=n,this.dragOffset=l-o):(p=l-a-n/2,0===e?p=Math.max(0,p):2===e&&p+n>=r&&(p=r-n,this.reversedExtremes?(p-=n,d=this.getUnionExtremes().dataMin):h=this.getUnionExtremes().dataMax),p!==o&&(this.fixedWidth=n,ef((c=s.navigatorAxis.toFixedRange(p,p+n,d,h)).min)&&eM(this,"setRange",{min:Math.min(c.min,c.max),max:Math.max(c.min,c.max),redraw:!0,eventArguments:{trigger:"navigator"}})))}handlesMousedown(t,e){t=this.chart.pointer?.normalize(t)||t;let i=this.chart,s=i.xAxis[0],o=this.reversedExtremes;0===e?(this.grabbedLeft=!0,this.otherHandlePos=this.zoomedMax,this.fixedExtreme=o?s.min:s.max):(this.grabbedRight=!0,this.otherHandlePos=this.zoomedMin,this.fixedExtreme=o?s.max:s.min),i.setFixedRange(void 0)}onMouseMove(t){let e=this,i=e.chart,s=e.navigatorSize,o=e.range,r=e.dragOffset,n=i.inverted,a=e.left,l;(!t.touches||0!==t.touches[0].pageX)&&(l=(t=i.pointer?.normalize(t)||t).chartX,n&&(a=e.top,l=t.chartY),e.grabbedLeft?(e.hasDragged=!0,e.render(0,0,l-a,e.otherHandlePos)):e.grabbedRight?(e.hasDragged=!0,e.render(0,0,e.otherHandlePos,l-a)):e.grabbedCenter&&(e.hasDragged=!0,l<r?l=r:l>s+r-o&&(l=s+r-o),e.render(0,0,l-r,l-r+o)),e.hasDragged&&e.scrollbar&&eP(e.scrollbar.options.liveRedraw,!ec&&!this.chart.boosted)&&(t.DOMType=t.type,setTimeout(function(){e.onMouseUp(t)},0)))}onMouseUp(t){let e,i,s,o,r,n,a=this.chart,l=this.xAxis,h=this.scrollbar,d=t.DOMEvent||t,c=a.inverted,p=this.rendered&&!this.hasDragged?"animate":"attr";(this.hasDragged&&(!h||!h.hasDragged)||"scrollbar"===t.trigger)&&(s=this.getUnionExtremes(),this.zoomedMin===this.otherHandlePos?o=this.fixedExtreme:this.zoomedMax===this.otherHandlePos&&(r=this.fixedExtreme),this.zoomedMax===this.size&&(r=this.reversedExtremes?s.dataMin:s.dataMax),0===this.zoomedMin&&(o=this.reversedExtremes?s.dataMax:s.dataMin),ef((n=l.navigatorAxis.toFixedRange(this.zoomedMin,this.zoomedMax,o,r)).min)&&eM(this,"setRange",{min:Math.min(n.min,n.max),max:Math.max(n.min,n.max),redraw:!0,animation:!this.hasDragged&&null,eventArguments:{trigger:"navigator",triggerOp:"navigator-drag",DOMEvent:d}})),"mousemove"!==t.DOMType&&"touchmove"!==t.DOMType&&(this.grabbedLeft=this.grabbedRight=this.grabbedCenter=this.fixedWidth=this.fixedExtreme=this.otherHandlePos=this.hasDragged=this.dragOffset=null),this.navigatorEnabled&&eA(this.zoomedMin)&&eA(this.zoomedMax)&&(i=Math.round(this.zoomedMin),e=Math.round(this.zoomedMax),this.shades&&this.drawMasks(i,e,c,p),this.outline&&this.drawOutline(i,e,c,p),this.navigatorOptions.handles.enabled&&Object.keys(this.handles).length===this.handles.length&&(this.drawHandle(i,0,c,p),this.drawHandle(e,1,c,p)))}removeEvents(){this.eventsToUnbind&&(this.eventsToUnbind.forEach(function(t){t()}),this.eventsToUnbind=void 0),this.removeBaseSeriesEvents()}removeBaseSeriesEvents(){let t=this.baseSeries||[];this.navigatorEnabled&&t[0]&&(!1!==this.navigatorOptions.adaptToUpdatedData&&t.forEach(function(t){eS(t,"updatedData",this.updatedDataHandler)},this),t[0].xAxis&&eS(t[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes))}getXAxisOffsets(){return this.chart.inverted?[this.scrollButtonSize,0,-this.scrollButtonSize,0]:[0,-this.scrollButtonSize,0,this.scrollButtonSize]}init(t){let e=t.options,i=e.navigator||{},s=i.enabled,o=e.scrollbar||{},r=o.enabled,n=s&&i.height||0,a=r&&o.height||0,l=o.buttonsEnabled&&a||0;this.handles=[],this.shades=[],this.chart=t,this.setBaseSeries(),this.height=n,this.scrollbarHeight=a,this.scrollButtonSize=l,this.scrollbarEnabled=r,this.navigatorEnabled=s,this.navigatorOptions=i,this.scrollbarOptions=o,this.setOpposite();let h=this,d=h.baseSeries,c=t.xAxis.length,p=t.yAxis.length,u=d&&d[0]&&d[0].xAxis||t.xAxis[0]||{options:{}};if(t.isDirtyBox=!0,h.navigatorEnabled){let e=this.getXAxisOffsets();h.xAxis=new(ts())(t,ew({breaks:u.options.breaks,ordinal:u.options.ordinal,overscroll:u.options.overscroll},i.xAxis,{type:"datetime",yAxis:i.yAxis?.id,index:c,isInternal:!0,offset:0,keepOrdinalPadding:!0,startOnTick:!1,endOnTick:!1,minPadding:u.options.ordinal?0:u.options.minPadding,maxPadding:u.options.ordinal?0:u.options.maxPadding,zoomEnabled:!1},t.inverted?{offsets:e,width:n}:{offsets:e,height:n}),"xAxis"),h.yAxis=new(ts())(t,ew(i.yAxis,{alignTicks:!1,offset:0,index:p,isInternal:!0,reversed:eP(i.yAxis&&i.yAxis.reversed,t.yAxis[0]&&t.yAxis[0].reversed,!1),zoomEnabled:!1},t.inverted?{width:n}:{height:n}),"yAxis"),d||i.series.data?h.updateNavigatorSeries(!1):0===t.series.length&&(h.unbindRedraw=eu(t,"beforeRedraw",function(){t.series.length>0&&!h.series&&(h.setBaseSeries(),h.unbindRedraw())})),h.reversedExtremes=t.inverted&&!h.xAxis.reversed||!t.inverted&&h.xAxis.reversed,h.renderElements(),h.addMouseEvents()}else h.xAxis={chart:t,navigatorAxis:{fake:!0},translate:function(e,i){let s=t.xAxis[0],o=s.getExtremes(),r=s.len-2*l,n=eE("min",s.options.min,o.dataMin),a=eE("max",s.options.max,o.dataMax)-n;return i?e*a/r+n:r*(e-n)/a},toPixels:function(t){return this.translate(t)},toValue:function(t){return this.translate(t,!0)}},h.xAxis.navigatorAxis.axis=h.xAxis,h.xAxis.navigatorAxis.toFixedRange=tP.prototype.toFixedRange.bind(h.xAxis.navigatorAxis);if(t.options.scrollbar?.enabled){let e=ew(t.options.scrollbar,{vertical:t.inverted});eA(e.margin)||(e.margin=t.inverted?-3:3),t.scrollbar=h.scrollbar=new ea(t.renderer,e,t),eu(h.scrollbar,"changed",function(t){let e=h.size,i=e*this.to,s=e*this.from;h.hasDragged=h.scrollbar.hasDragged,h.render(0,0,s,i),this.shouldUpdateExtremes(t.DOMType)&&setTimeout(function(){h.onMouseUp(t)})})}h.addBaseSeriesEvents(),h.addChartEvents()}setOpposite(){let t=this.navigatorOptions,e=this.navigatorEnabled,i=this.chart;this.opposite=eP(t.opposite,!!(!e&&i.inverted))}getUnionExtremes(t){let e,i=this.chart.xAxis[0],s=this.chart.time,o=this.xAxis,r=o.options,n=i.options;return t&&null===i.dataMin||(e={dataMin:eP(s.parse(r?.min),eE("min",s.parse(n.min),i.dataMin,o.dataMin,o.min)),dataMax:eP(s.parse(r?.max),eE("max",s.parse(n.max),i.dataMax,o.dataMax,o.max))}),e}setBaseSeries(t,e){let i=this.chart,s=this.baseSeries=[];t=t||i.options&&i.options.navigator.baseSeries||(i.series.length?ev(i.series,t=>!t.options.isInternal).index:0),(i.series||[]).forEach((e,i)=>{!e.options.isInternal&&(e.options.showInNavigator||(i===t||e.options.id===t)&&!1!==e.options.showInNavigator)&&s.push(e)}),this.xAxis&&!this.xAxis.navigatorAxis.fake&&this.updateNavigatorSeries(!0,e)}updateNavigatorSeries(t,e){let i=this,s=i.chart,o=i.baseSeries,r={enableMouseTracking:!1,index:null,linkedTo:null,group:"nav",padXAxis:!1,xAxis:this.navigatorOptions.xAxis?.id,yAxis:this.navigatorOptions.yAxis?.id,showInLegend:!1,stacking:void 0,isInternal:!0,states:{inactive:{opacity:1}}},n=i.series=(i.series||[]).filter(t=>{let e=t.baseSeries;return!(0>o.indexOf(e))||(e&&(eS(e,"updatedData",i.updatedDataHandler),delete e.navigatorSeries),t.chart&&t.destroy(),!1)}),a,l,h=i.navigatorOptions.series,d;o&&o.length&&o.forEach(t=>{let c=t.navigatorSeries,p=ey({color:t.color,visible:t.visible},ek(h)?ed.navigator.series:h);if(c&&!1===i.navigatorOptions.adaptToUpdatedData)return;r.name="Navigator "+o.length,d=(a=t.options||{}).navigatorOptions||{},p.dataLabels=eO(p.dataLabels),(l=ew(a,r,p,d)).pointRange=eP(p.pointRange,d.pointRange,ed.plotOptions[l.type||"line"].pointRange);let u=d.data||p.data;i.hasNavigatorData=i.hasNavigatorData||!!u,l.data=u||a.data?.slice(0),c&&c.options?c.update(l,e):(t.navigatorSeries=s.initSeries(l),s.setSortedData(),t.navigatorSeries.baseSeries=t,n.push(t.navigatorSeries))}),(h.data&&!(o&&o.length)||ek(h))&&(i.hasNavigatorData=!1,(h=eO(h)).forEach((t,e)=>{r.name="Navigator "+(n.length+1),(l=ew(ed.navigator.series,{color:s.series[e]&&!s.series[e].options.isInternal&&s.series[e].color||s.options.colors[e]||s.options.colors[0]},r,t)).data=t.data,l.data&&(i.hasNavigatorData=!0,n.push(s.initSeries(l)))})),t&&this.addBaseSeriesEvents()}addBaseSeriesEvents(){let t=this,e=t.baseSeries||[];e[0]&&e[0].xAxis&&e[0].eventsToUnbind.push(eu(e[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes)),e.forEach(i=>{i.eventsToUnbind.push(eu(i,"show",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!0,!1)})),i.eventsToUnbind.push(eu(i,"hide",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!1,!1)})),!1!==this.navigatorOptions.adaptToUpdatedData&&i.xAxis&&i.eventsToUnbind.push(eu(i,"updatedData",this.updatedDataHandler)),i.eventsToUnbind.push(eu(i,"remove",function(){e&&eb(e,i),this.navigatorSeries&&t.series&&(eb(t.series,this.navigatorSeries),ef(this.navigatorSeries.options)&&this.navigatorSeries.remove(!1),delete this.navigatorSeries)}))})}getBaseSeriesMin(t){return this.baseSeries.reduce(function(t,e){return Math.min(t,e.getColumn("x")[0]??t)},t)}modifyNavigatorAxisExtremes(){let t=this.xAxis;if(void 0!==t.getExtremes){let e=this.getUnionExtremes(!0);e&&(e.dataMin!==t.min||e.dataMax!==t.max)&&(t.min=e.dataMin,t.max=e.dataMax)}}modifyBaseAxisExtremes(){let t,e,i=this.chart.navigator,s=this.getExtremes(),o=s.min,r=s.max,n=s.dataMin,a=s.dataMax,l=r-o,h=i.stickToMin,d=i.stickToMax,c=eP(this.ordinal?.convertOverscroll(this.options.overscroll),0),p=i.series&&i.series[0],u=!!this.setExtremes;!(this.eventArgs&&"rangeSelectorButton"===this.eventArgs.trigger)&&(h&&(t=(e=n)+l),d&&(t=a+c,h||(e=Math.max(n,t-l,i.getBaseSeriesMin(p&&p.xData?p.xData[0]:-Number.MAX_VALUE)))),u&&(h||d)&&eA(e)&&(this.min=this.userMin=e,this.max=this.userMax=t)),i.stickToMin=i.stickToMax=null}updatedDataHandler(){let t=this.chart.navigator,e=this.navigatorSeries,i=t.reversedExtremes?0===Math.round(t.zoomedMin):Math.round(t.zoomedMax)>=Math.round(t.size);t.stickToMax=eP(this.chart.options.navigator&&this.chart.options.navigator.stickToMax,i),t.stickToMin=t.shouldStickToMin(this,t),e&&!t.hasNavigatorData&&(e.options.pointStart=this.getColumn("x")[0],e.setData(this.options.data,!1,null,!1))}shouldStickToMin(t,e){let i=e.getBaseSeriesMin(t.getColumn("x")[0]),s=t.xAxis,o=s.max,r=s.min,n=s.options.range,a=!0;return!!(eA(o)&&eA(r))&&(n&&o-i>0?o-i<n:r<=i)}addChartEvents(){this.eventsToUnbind||(this.eventsToUnbind=[]),this.eventsToUnbind.push(eu(this.chart,"redraw",function(){let t=this.navigator,e=t&&(t.baseSeries&&t.baseSeries[0]&&t.baseSeries[0].xAxis||this.xAxis[0]);e&&t.render(e.min,e.max)}),eu(this.chart,"getMargins",function(){let t=this.navigator,e=t.opposite?"plotTop":"marginBottom";this.inverted&&(e=t.opposite?"marginRight":"plotLeft"),this[e]=(this[e]||0)+(t.navigatorEnabled||!this.inverted?t.height+(this.scrollbar?.options.margin||0)+t.scrollbarHeight:0)+(t.navigatorOptions.margin||0)}),eu(eB,"setRange",function(t){this.chart.xAxis[0].setExtremes(t.min,t.max,t.redraw,t.animation,t.eventArguments)}))}destroy(){this.removeEvents(),this.xAxis&&(eb(this.chart.xAxis,this.xAxis),eb(this.chart.axes,this.xAxis)),this.yAxis&&(eb(this.chart.yAxis,this.yAxis),eb(this.chart.axes,this.yAxis)),(this.series||[]).forEach(t=>{t.destroy&&t.destroy()}),["series","xAxis","yAxis","shades","outline","scrollbarTrack","scrollbarRifles","scrollbarGroup","scrollbar","navigatorGroup","rendered"].forEach(t=>{this[t]&&this[t].destroy&&this[t].destroy(),this[t]=null}),[this.handles].forEach(t=>{em(t)}),this.baseSeries.forEach(t=>{t.navigatorSeries=void 0}),this.navigatorEnabled=!1}}let eT={lang:{rangeSelectorZoom:"Zoom",rangeSelectorFrom:"",rangeSelectorTo:"→",rangeSelector:{allText:"All",allTitle:"View all",monthText:"{count}m",monthTitle:"View {count} {#eq count 1}month{else}months{/eq}",yearText:"{count}y",yearTitle:"View {count} {#eq count 1}year{else}years{/eq}",ytdText:"YTD",ytdTitle:"View year to date"}},rangeSelector:{allButtonsEnabled:!1,buttons:[{type:"month",count:1},{type:"month",count:3},{type:"month",count:6},{type:"ytd"},{type:"year",count:1},{type:"all"}],buttonSpacing:5,dropdown:"responsive",enabled:void 0,verticalAlign:"top",buttonTheme:{width:28,height:18,padding:2,zIndex:7},floating:!1,x:0,y:0,height:void 0,inputBoxBorderColor:"none",inputBoxHeight:17,inputBoxWidth:void 0,inputDateFormat:"%[ebY]",inputDateParser:void 0,inputEditDateFormat:"%Y-%m-%d",inputEnabled:!0,inputPosition:{align:"right",x:0,y:0},inputSpacing:5,selected:void 0,buttonPosition:{align:"left",x:0,y:0},inputStyle:{color:"#334eff",cursor:"pointer",fontSize:"0.8em"},labelStyle:{color:"#666666",fontSize:"0.8em"}}},{defaultOptions:eC}=P(),{composed:eI}=P(),{addEvent:eR,defined:eD,extend:eL,isNumber:eG,merge:ez,pick:eN,pushUnique:eW}=P(),eH=[];function eF(){let t,e,i=this.range,s=i.type,o=this.max,r=this.chart.time,n=function(t,e){let i=r.toParts(t),o=i.slice();"year"===s?o[0]+=e:o[1]+=e;let n=r.makeTime.apply(r,o),a=r.toParts(n);return"month"===s&&i[1]===a[1]&&1===Math.abs(e)&&(o[0]=i[0],o[1]=i[1],o[2]=0),(n=r.makeTime.apply(r,o))-t};eG(i)?(t=o-i,e=i):i&&(t=o+n(o,-(i.count||1)),this.chart&&this.chart.setFixedRange(o-t));let a=eN(this.dataMin,Number.MIN_VALUE);return eG(t)||(t=a),t<=a&&(t=a,void 0===e&&(e=n(t,i.count)),this.newMax=Math.min(t+e,eN(this.dataMax,Number.MAX_VALUE))),eG(o)?!eG(i)&&i&&i._offsetMin&&(t+=i._offsetMin):t=void 0,t}function eU(){this.rangeSelector?.redrawElements()}function eX(){this.options.rangeSelector&&this.options.rangeSelector.enabled&&(this.rangeSelector=new p(this))}function eY(){let t=this.rangeSelector;if(t){eG(t.deferredYTDClick)&&(t.clickButton(t.deferredYTDClick),delete t.deferredYTDClick);let e=t.options.verticalAlign;t.options.floating||("bottom"===e?this.extraBottomMargin=!0:"top"===e&&(this.extraTopMargin=!0))}}function eV(){let t,e=this.rangeSelector;if(!e)return;let i=this.xAxis[0].getExtremes(),s=this.legend,o=e&&e.options.verticalAlign;eG(i.min)&&e.render(i.min,i.max),s.display&&"top"===o&&o===s.options.verticalAlign&&(t=ez(this.spacingBox),"vertical"===s.options.layout?t.y=this.plotTop:t.y+=e.getHeight(),s.group.placed=!1,s.align(t))}function e_(){for(let t=0,e=eH.length;t<e;++t){let e=eH[t];if(e[0]===this){e[1].forEach(t=>t()),eH.splice(t,1);return}}}function ej(){let t=this.rangeSelector;if(t?.options?.enabled){let e=t.getHeight(),i=t.options.verticalAlign;t.options.floating||("bottom"===i?this.marginBottom+=e:"middle"!==i&&(this.plotTop+=e))}}function eq(t){let e=t.options.rangeSelector,i=this.extraBottomMargin,s=this.extraTopMargin,o=this.rangeSelector;if(e&&e.enabled&&!eD(o)&&this.options.rangeSelector&&(this.options.rangeSelector.enabled=!0,this.rangeSelector=o=new p(this)),this.extraBottomMargin=!1,this.extraTopMargin=!1,o){let t=e&&e.verticalAlign||o.options&&o.options.verticalAlign;o.options.floating||("bottom"===t?this.extraBottomMargin=!0:"middle"!==t&&(this.extraTopMargin=!0)),(this.extraBottomMargin!==i||this.extraTopMargin!==s)&&(this.isDirtyBox=!0)}}let eZ={compose:function(t,e,i){if(p=i,eW(eI,"RangeSelector")){let i=e.prototype;t.prototype.minFromRange=eF,eR(e,"afterGetContainer",eX),eR(e,"beforeRender",eY),eR(e,"destroy",e_),eR(e,"getMargins",ej),eR(e,"redraw",eV),eR(e,"update",eq),eR(e,"beforeRedraw",eU),i.callbacks.push(eV),eL(eC,{rangeSelector:eT.rangeSelector}),eL(eC.lang,eT.lang)}}};var e$=k(28),eK=k.n(e$),eJ=k(984),eQ=k.n(eJ);!function(t){t.setLength=function(t,e,i){return Array.isArray(t)?(t.length=e,t):t[i?"subarray":"slice"](0,e)},t.splice=function(t,e,i,s,o=[]){if(Array.isArray(t))return Array.isArray(o)||(o=Array.from(o)),{removed:t.splice(e,i,...o),array:t};let r=Object.getPrototypeOf(t).constructor,n=t[s?"subarray":"slice"](e,e+i),a=new r(t.length-i+o.length);return a.set(t.subarray(0,e),0),a.set(o,e),a.set(t.subarray(e+i),e+o.length),{removed:n,array:a}}}(x||(x={}));let{setLength:e0,splice:e1}=x,{fireEvent:e2,objectEach:e3,uniqueKey:e5}=P(),e6=class{constructor(t={}){this.autoId=!t.id,this.columns={},this.id=t.id||e5(),this.modified=this,this.rowCount=0,this.versionTag=e5();let e=0;e3(t.columns||{},(t,i)=>{this.columns[i]=t.slice(),e=Math.max(e,t.length)}),this.applyRowCount(e)}applyRowCount(t){this.rowCount=t,e3(this.columns,(e,i)=>{e.length!==t&&(this.columns[i]=e0(e,t))})}deleteRows(t,e=1){if(e>0&&t<this.rowCount){let i=0;e3(this.columns,(s,o)=>{this.columns[o]=e1(s,t,e).array,i=s.length}),this.rowCount=i}e2(this,"afterDeleteRows",{rowIndex:t,rowCount:e}),this.versionTag=e5()}getColumn(t,e){return this.columns[t]}getColumns(t,e){return(t||Object.keys(this.columns)).reduce((t,e)=>(t[e]=this.columns[e],t),{})}getRow(t,e){return(e||Object.keys(this.columns)).map(e=>this.columns[e]?.[t])}setColumn(t,e=[],i=0,s){this.setColumns({[t]:e},i,s)}setColumns(t,e,i){let s=this.rowCount;e3(t,(t,e)=>{this.columns[e]=t.slice(),s=t.length}),this.applyRowCount(s),i?.silent||(e2(this,"afterSetColumns"),this.versionTag=e5())}setRow(t,e=this.rowCount,i,s){let{columns:o}=this,r=i?this.rowCount+1:e+1;e3(t,(t,n)=>{let a=o[n]||s?.addColumns!==!1&&Array(r);a&&(i?a=e1(a,e,0,!0,[t]).array:a[e]=t,o[n]=a)}),r>this.rowCount&&this.applyRowCount(r),s?.silent||(e2(this,"afterSetRows"),this.versionTag=e5())}},{addEvent:e4,correctFloat:e8,css:e9,defined:e7,error:it,isNumber:ie,pick:ii,timeUnits:is,isString:io}=P();!function(t){function e(t,i,s,o,r=[],n=0,a){let l={},h=this.options.tickPixelInterval,d=this.chart.time,c=[],p,u,g,x,f,m=0,b=[],y=-Number.MAX_VALUE;if(!this.options.ordinal&&!this.options.breaks||!r||r.length<3||void 0===i)return d.getTimeTicks.apply(d,arguments);let v=r.length;for(p=0;p<v;p++){if(f=p&&r[p-1]>s,r[p]<i&&(m=p),p===v-1||r[p+1]-r[p]>5*n||f){if(r[p]>y){for(u=d.getTimeTicks(t,r[m],r[p],o);u.length&&u[0]<=y;)u.shift();u.length&&(y=u[u.length-1]),c.push(b.length),b=b.concat(u)}m=p+1}if(f)break}if(u){if(x=u.info,a&&x.unitRange<=is.hour){for(m=1,p=b.length-1;m<p;m++)d.dateFormat("%d",b[m])!==d.dateFormat("%d",b[m-1])&&(l[b[m]]="day",g=!0);g&&(l[b[0]]="day"),x.higherRanks=l}x.segmentStarts=c,b.info=x}else it(12,!1,this.chart);if(a&&e7(h)){let t=b.length,e=[],i=[],o,r,n,a,d,c=t;for(;c--;)r=this.translate(b[c]),n&&(i[c]=n-r),e[c]=n=r;for(i.sort((t,e)=>t-e),(a=i[Math.floor(i.length/2)])<.6*h&&(a=null),c=b[t-1]>s?t-1:t,n=void 0;c--;)d=Math.abs(n-(r=e[c])),n&&d<.8*h&&(null===a||d<.8*a)?(l[b[c]]&&!l[b[c+1]]?(o=c+1,n=r):o=c,b.splice(o,1)):n=r}return b}function i(t){let e=this.ordinal.positions;if(!e)return t;let i=e.length-1,s;return(t<0?t=e[0]:t>i?t=e[i]:(i=Math.floor(t),s=t-i),void 0!==s&&void 0!==e[i])?e[i]+(s?s*(e[i+1]-e[i]):0):t}function s(t){let e=this.ordinal,i=this.old?this.old.min:this.min,s=this.old?this.old.transA:this.transA,o=e.getExtendedPositions();if(o?.length){let r=e8((t-i)*s+this.minPixelPadding),n=e8(e.getIndexOfPoint(r,o)),a=e8(n%1);if(n>=0&&n<=o.length-1){let t=o[Math.floor(n)],e=o[Math.ceil(n)];return o[Math.floor(n)]+a*(e-t)}}return t}function o(e,i){let s=t.Additions.findIndexOf(e,i,!0);if(e[s]===i)return s;let o=(i-e[s])/(e[s+1]-e[s]);return s+o}function r(){this.ordinal||(this.ordinal=new t.Additions(this))}function n(){let{eventArgs:t,options:e}=this;if(this.isXAxis&&e7(e.overscroll)&&0!==e.overscroll&&ie(this.max)&&ie(this.min)&&(this.options.ordinal&&!this.ordinal.originalOrdinalRange&&this.ordinal.getExtendedPositions(!1),this.max===this.dataMax&&(t?.trigger!=="pan"||this.isInternal)&&t?.trigger!=="navigator")){let i=this.ordinal.convertOverscroll(e.overscroll);this.max+=i,!this.isInternal&&e7(this.userMin)&&t?.trigger!=="mousewheel"&&(this.min+=i)}}function a(){this.horiz&&!this.isDirty&&(this.isDirty=this.isOrdinal&&this.chart.navigator&&!this.chart.navigator.adaptToUpdatedData)}function l(){this.ordinal&&(this.ordinal.beforeSetTickPositions(),this.tickInterval=this.ordinal.postProcessTickInterval(this.tickInterval))}function h(t){let e=this.xAxis[0],i=e.ordinal.convertOverscroll(e.options.overscroll),s=t.originalEvent.chartX,o=this.options.chart.panning,r=!1;if(o?.type!=="y"&&e.options.ordinal&&e.series.length&&(!t.touches||t.touches.length<=1)){let o,n,a=this.mouseDownX,l=e.getExtremes(),h=l.dataMin,d=l.dataMax,c=l.min,p=l.max,u=this.hoverPoints,g=e.closestPointRange||e.ordinal?.overscrollPointsRange,x=Math.round((a-s)/(e.translationSlope*(e.ordinal.slope||g))),f=e.ordinal.getExtendedPositions(),m={ordinal:{positions:f,extendedOrdinalPositions:f}},b=e.index2val,y=e.val2lin;if(c<=h&&x<=0||p>=d+i&&x>=0)return void t.preventDefault();m.ordinal.positions?Math.abs(x)>1&&(u&&u.forEach(function(t){t.setState()}),n=m.ordinal.positions,i&&(n=m.ordinal.positions=n.concat(e.ordinal.getOverscrollPositions())),d>n[n.length-1]&&n.push(d),this.setFixedRange(p-c),(o=e.navigatorAxis.toFixedRange(void 0,void 0,b.apply(m,[y.apply(m,[c,!0])+x]),b.apply(m,[y.apply(m,[p,!0])+x]))).min>=Math.min(n[0],c)&&o.max<=Math.max(n[n.length-1],p)+i&&e.setExtremes(o.min,o.max,!0,!1,{trigger:"pan"}),this.mouseDownX=s,e9(this.container,{cursor:"move"})):r=!0}else r=!0;r||o&&/y/.test(o.type)?i&&ie(e.dataMax)&&(e.max=e.dataMax+i):t.preventDefault()}function d(){let t=this.xAxis;t?.options.ordinal&&(delete t.ordinal.index,delete t.ordinal.originalOrdinalRange)}function c(t,e){let i,s=this.ordinal,r=s.positions,n=s.slope,a;if(!r)return t;let l=r.length;if(r[0]<=t&&r[l-1]>=t)i=o(r,t);else{if(a=s.getExtendedPositions?.(),!a?.length)return t;let l=a.length;n||(n=(a[l-1]-a[0])/l);let h=o(a,r[0]);if(t>=a[0]&&t<=a[l-1])i=o(a,t)-h;else{if(!e)return t;i=t<a[0]?-h-(a[0]-t)/n:(t-a[l-1])/n+l-h}}return e?i:n*(i||0)+s.offset}t.compose=function(t,o,p){let u=t.prototype;return u.ordinal2lin||(u.getTimeTicks=e,u.index2val=i,u.lin2val=s,u.val2lin=c,u.ordinal2lin=u.val2lin,e4(t,"afterInit",r),e4(t,"foundExtremes",n),e4(t,"afterSetScale",a),e4(t,"initialAxisTranslation",l),e4(p,"pan",h),e4(p,"touchpan",h),e4(o,"updatedData",d)),t},t.Additions=class{constructor(t){this.index={},this.axis=t}beforeSetTickPositions(){let t=this.axis,e=t.ordinal,i=t.getExtremes(),s=i.min,o=i.max,r=t.brokenAxis?.hasBreaks,n=t.options.ordinal,a=t.options.overscroll&&t.ordinal.convertOverscroll(t.options.overscroll)||0,l,h,d,c,p,u,g,x=[],f=Number.MAX_VALUE,m=!1,b=!1,y=!1;if(n||r){let i=0;if(t.series.forEach(function(t,e){let s=t.getColumn("x",!0);if(h=[],e>0&&"highcharts-navigator-series"!==t.options.id&&s.length>1&&(b=i!==s[1]-s[0]),i=s[1]-s[0],t.boosted&&(y=t.boosted),t.reserveSpace()&&(!1!==t.takeOrdinalPosition||r)&&(l=(x=x.concat(s)).length,x.sort(function(t,e){return t-e}),f=Math.min(f,ii(t.closestPointRange,f)),l)){for(e=0;e<l-1;)x[e]!==x[e+1]&&h.push(x[e+1]),e++;h[0]!==x[0]&&h.unshift(x[0]),x=h}}),t.ordinal.originalOrdinalRange||(t.ordinal.originalOrdinalRange=(x.length-1)*f),b&&y&&(x.pop(),x.shift()),(l=x.length)>2){for(d=x[1]-x[0],g=l-1;g--&&!m;)x[g+1]-x[g]!==d&&(m=!0);!t.options.keepOrdinalPadding&&(x[0]-s>d||o-a-x[l-1]>d)&&(m=!0)}else t.options.overscroll&&(2===l?f=x[1]-x[0]:1===l?(f=a,x=[x[0],x[0]+f]):f=e.overscrollPointsRange);m||t.forceOrdinal?(t.options.overscroll&&(e.overscrollPointsRange=f,x=x.concat(e.getOverscrollPositions())),e.positions=x,c=t.ordinal2lin(Math.max(s,x[0]),!0),p=Math.max(t.ordinal2lin(Math.min(o,x[x.length-1]),!0),1),e.slope=u=(o-s)/(p-c),e.offset=s-c*u):(e.overscrollPointsRange=ii(t.closestPointRange,e.overscrollPointsRange),e.positions=t.ordinal.slope=e.offset=void 0)}t.isOrdinal=n&&m,e.groupIntervalFactor=null}static findIndexOf(t,e,i){let s=0,o=t.length-1,r;for(;s<o;)t[r=Math.ceil((s+o)/2)]<=e?s=r:o=r-1;return t[s]===e||i?s:-1}getExtendedPositions(t=!0){let e=this,i=e.axis,s=i.constructor.prototype,o=i.chart,r=i.series.reduce((t,e)=>{let i=e.currentDataGrouping;return t+(i?i.count+i.unitName:"raw")},""),n=t?i.ordinal.convertOverscroll(i.options.overscroll):0,a=i.getExtremes(),l,h,d=e.index;return d||(d=e.index={}),!d[r]&&((l={series:[],chart:o,forceOrdinal:!1,getExtremes:function(){return{min:a.dataMin,max:a.dataMax+n}},applyGrouping:s.applyGrouping,getGroupPixelWidth:s.getGroupPixelWidth,getTimeTicks:s.getTimeTicks,options:{ordinal:!0},ordinal:{getGroupIntervalFactor:this.getGroupIntervalFactor},ordinal2lin:s.ordinal2lin,getIndexOfPoint:s.getIndexOfPoint,val2lin:s.val2lin}).ordinal.axis=l,i.series.forEach(i=>{if(!1===i.takeOrdinalPosition)return;h={xAxis:l,chart:o,groupPixelWidth:i.groupPixelWidth,destroyGroupedData:P().noop,getColumn:i.getColumn,applyGrouping:i.applyGrouping,getProcessedData:i.getProcessedData,reserveSpace:i.reserveSpace,visible:i.visible};let s=i.getColumn("x").concat(t?e.getOverscrollPositions():[]);h.dataTable=new e6({columns:{x:s}}),h.options={...i.options,dataGrouping:i.currentDataGrouping?{firstAnchor:i.options.dataGrouping?.firstAnchor,anchor:i.options.dataGrouping?.anchor,lastAnchor:i.options.dataGrouping?.firstAnchor,enabled:!0,forced:!0,approximation:"open",units:[[i.currentDataGrouping.unitName,[i.currentDataGrouping.count]]]}:{enabled:!1}},l.series.push(h),i.processData.apply(h)}),l.applyGrouping({hasExtremesChanged:!0}),h?.closestPointRange!==h?.basePointRange&&h.currentDataGrouping&&(l.forceOrdinal=!0),i.ordinal.beforeSetTickPositions.apply({axis:l}),!i.ordinal.originalOrdinalRange&&l.ordinal.originalOrdinalRange&&(i.ordinal.originalOrdinalRange=l.ordinal.originalOrdinalRange),l.ordinal.positions&&(d[r]=l.ordinal.positions)),d[r]}getGroupIntervalFactor(t,e,i){let s=i.getColumn("x",!0),o=s.length,r=[],n,a,l=this.groupIntervalFactor;if(!l){for(a=0;a<o-1;a++)r[a]=s[a+1]-s[a];r.sort(function(t,e){return t-e}),n=r[Math.floor(o/2)],t=Math.max(t,s[0]),e=Math.min(e,s[o-1]),this.groupIntervalFactor=l=o*n/(e-t)}return l}getIndexOfPoint(t,e){let i=this.axis,s=i.min,r=i.minPixelPadding;return o(e,s)+e8((t-r)/(i.translationSlope*(this.slope||i.closestPointRange||this.overscrollPointsRange)))}getOverscrollPositions(){let t=this.axis,e=this.convertOverscroll(t.options.overscroll),i=this.overscrollPointsRange,s=[],o=t.dataMax;if(e7(i))for(;o<t.dataMax+e;)s.push(o+=i);return s}postProcessTickInterval(t){let e,i=this.axis,s=this.slope,o=i.closestPointRange;return s&&o?i.options.breaks?o||t:t/(s/o):t}convertOverscroll(t=0){let e=this,i=e.axis,s=function(t){return ii(e.originalOrdinalRange,e7(i.dataMax)&&e7(i.dataMin)?i.dataMax-i.dataMin:0)*t};if(io(t)){let e,o=parseInt(t,10);if(e7(i.min)&&e7(i.max)&&e7(i.dataMin)&&e7(i.dataMax)&&((e=i.max-i.min==i.dataMax-i.dataMin)||(this.originalOrdinalRange=i.max-i.min)),/%$/.test(t))return s(o/100);if(/px/.test(t)){let t=Math.min(o,.9*i.len)/i.len;return s(t/(e?1-t:1))}return 0}return t}}}(f||(f={}));let ir=f,{defaultOptions:ia}=P(),{format:il}=eQ(),{addEvent:ih,createElement:id,css:ic,defined:ip,destroyObjectProperties:iu,diffObjects:ig,discardElement:ix,extend:im,fireEvent:ib,isNumber:iy,isString:iv,merge:iM,objectEach:ik,pick:iA,splat:iw}=P();function iP(t){let e=e=>RegExp(`%[[a-zA-Z]*${e}`).test(t);if(iv(t)?-1!==t.indexOf("%L"):t.fractionalSecondDigits)return"text";let i=iv(t)?["a","A","d","e","w","b","B","m","o","y","Y"].some(e):t.dateStyle||t.day||t.month||t.year,s=iv(t)?["H","k","I","l","M","S"].some(e):t.timeStyle||t.hour||t.minute||t.second;return i&&s?"datetime-local":i?"date":s?"time":"text"}class iS{static compose(t,e){eZ.compose(t,e,iS)}constructor(t){this.isDirty=!1,this.buttonOptions=[],this.initialButtonGroupWidth=0,this.maxButtonWidth=()=>{let t=0;return this.buttons.forEach(e=>{let i=e.getBBox();i.width>t&&(t=i.width)}),t},this.init(t)}clickButton(t,e){let i=this.chart,s=this.buttonOptions[t],o=i.xAxis[0],r=i.scroller&&i.scroller.getUnionExtremes()||o||{},n=s.type,a=s.dataGrouping,l=r.dataMin,h=r.dataMax,d,c=iy(o?.max)?Math.round(Math.min(o.max,h??o.max)):void 0,p,u=s._range,g,x,f,m=!0;if(null!==l&&null!==h){if(this.setSelected(t),a&&(this.forcedDataGrouping=!0,ts().prototype.setDataGrouping.call(o||{chart:this.chart},a,!1),this.frozenStates=s.preserveDataGrouping),"month"===n||"year"===n)o?(x={range:s,max:c,chart:i,dataMin:l,dataMax:h},d=o.minFromRange.call(x),iy(x.newMax)&&(c=x.newMax),m=!1):u=s;else if(u)iy(c)&&(c=Math.min((d=Math.max(c-u,l))+u,h),m=!1);else if("ytd"===n)if(o)!o.hasData()||iy(h)&&iy(l)||(l=Number.MAX_VALUE,h=-Number.MAX_VALUE,i.series.forEach(t=>{let e=t.getColumn("x");e.length&&(l=Math.min(e[0],l),h=Math.max(e[e.length-1],h))}),e=!1),iy(h)&&iy(l)&&(d=g=(f=this.getYTDExtremes(h,l)).min,c=f.max);else{this.deferredYTDClick=t;return}else"all"===n&&o&&(i.navigator&&i.navigator.baseSeries[0]&&(i.navigator.baseSeries[0].xAxis.options.range=void 0),d=l,c=h);if(m&&s._offsetMin&&ip(d)&&(d+=s._offsetMin),s._offsetMax&&ip(c)&&(c+=s._offsetMax),this.dropdown&&(this.dropdown.selectedIndex=t+1),o)iy(d)&&iy(c)&&(o.setExtremes(d,c,iA(e,!0),void 0,{trigger:"rangeSelectorButton",rangeSelectorButton:s}),i.setFixedRange(s._range));else{p=iw(i.options.xAxis||{})[0];let t=ih(i,"afterCreateAxes",function(){let t=i.xAxis[0];t.range=t.options.range=u,t.min=t.options.min=g});ih(i,"load",function(){let e=i.xAxis[0];i.setFixedRange(s._range),e.options.range=p.range,e.options.min=p.min,t()})}ib(this,"afterBtnClick")}}setSelected(t){this.selected=this.options.selected=t}init(t){let e=this,i=t.options.rangeSelector,s=t.options.lang,o=i.buttons,r=i.selected,n=function(){let t=e.minInput,i=e.maxInput;t&&t.blur&&ib(t,"blur"),i&&i.blur&&ib(i,"blur")};e.chart=t,e.options=i,e.buttons=[],e.buttonOptions=o.map(t=>(t.type&&s.rangeSelector&&(t.text??(t.text=s.rangeSelector[`${t.type}Text`]),t.title??(t.title=s.rangeSelector[`${t.type}Title`])),t.text=il(t.text,{count:t.count||1}),t.title=il(t.title,{count:t.count||1}),t)),this.eventsToUnbind=[],this.eventsToUnbind.push(ih(t.container,"mousedown",n)),this.eventsToUnbind.push(ih(t,"resize",n)),o.forEach(e.computeButtonRange),void 0!==r&&o[r]&&this.clickButton(r,!1),this.eventsToUnbind.push(ih(t,"load",function(){t.xAxis&&t.xAxis[0]&&ih(t.xAxis[0],"setExtremes",function(i){iy(this.max)&&iy(this.min)&&this.max-this.min!==t.fixedRange&&"rangeSelectorButton"!==i.trigger&&"updatedData"!==i.trigger&&e.forcedDataGrouping&&!e.frozenStates&&this.setDataGrouping(!1,!1)})})),this.createElements()}updateButtonStates(){let t=this,e=this.chart,i=this.dropdown,s=this.dropdownLabel,o=e.xAxis[0],r=Math.round(o.max-o.min),n=!o.hasVisibleSeries,a=24*36e5,l=e.scroller&&e.scroller.getUnionExtremes()||o,h=l.dataMin,d=l.dataMax,c=t.getYTDExtremes(d,h),p=c.min,u=c.max,g=t.selected,x=t.options.allButtonsEnabled,f=Array(t.buttonOptions.length).fill(0),m=iy(g),b=t.buttons,y=!1,v=null;t.buttonOptions.forEach((e,i)=>{let s=e._range,l=e.type,c=e.count||1,b=e._offsetMax-e._offsetMin,M=i===g,k=s>d-h,A=s<o.minRange,w=!1,P=s===r;if(M&&k&&(y=!0),o.isOrdinal&&o.ordinal?.positions&&s&&r<s){let t=o.ordinal.positions,e=ir.Additions.findIndexOf(t,o.min,!0),i=Math.min(ir.Additions.findIndexOf(t,o.max,!0)+1,t.length-1);t[i]-t[e]>s&&(P=!0)}else("month"===l||"year"===l)&&r+36e5>=({month:28,year:365})[l]*a*c-b&&r-36e5<=({month:31,year:366})[l]*a*c+b?P=!0:"ytd"===l?(P=u-p+b===r,w=!M):"all"===l&&(P=o.max-o.min>=d-h);let S=!x&&!(y&&"all"===l)&&(k||A||n),O=y&&"all"===l||!w&&P||M&&t.frozenStates;S?f[i]=3:O&&(!m||i===g)&&(v=i)}),null!==v?(f[v]=2,t.setSelected(v),this.dropdown&&(this.dropdown.selectedIndex=v+1)):(t.setSelected(),this.dropdown&&(this.dropdown.selectedIndex=-1),s&&(s.setState(0),s.attr({text:(ia.lang.rangeSelectorZoom||"")+" ▾"})));for(let e=0;e<f.length;e++){let o=f[e],r=b[e];if(r.state!==o&&(r.setState(o),i)){i.options[e+1].disabled=3===o,2===o&&(s&&(s.setState(2),s.attr({text:t.buttonOptions[e].text+" ▾"})),i.selectedIndex=e+1);let r=s.getBBox();ic(i,{width:`${r.width}px`,height:`${r.height}px`})}}}computeButtonRange(t){let e=t.type,i=t.count||1,s={millisecond:1,second:1e3,minute:6e4,hour:36e5,day:864e5,week:6048e5};s[e]?t._range=s[e]*i:("month"===e||"year"===e)&&(t._range=24*({month:30,year:365})[e]*36e5*i),t._offsetMin=iA(t.offsetMin,0),t._offsetMax=iA(t.offsetMax,0),t._range+=t._offsetMax-t._offsetMin}getInputValue(t){let e="min"===t?this.minInput:this.maxInput,i=this.chart.options.rangeSelector,s=this.chart.time;return e?("text"===e.type&&i.inputDateParser||this.defaultInputDateParser)(e.value,"UTC"===s.timezone,s):0}setInputValue(t,e){let i=this.options,s=this.chart.time,o="min"===t?this.minInput:this.maxInput,r="min"===t?this.minDateBox:this.maxDateBox;if(o){o.setAttribute("type",iP(i.inputDateFormat||"%e %b %Y"));let t=o.getAttribute("data-hc-time"),n=ip(t)?Number(t):void 0;if(ip(e)){let t=n;ip(t)&&o.setAttribute("data-hc-time-previous",t),o.setAttribute("data-hc-time",e),n=e}o.value=s.dateFormat(this.inputTypeFormats[o.type]||i.inputEditDateFormat,n),r&&r.attr({text:s.dateFormat(i.inputDateFormat,n)})}}setInputExtremes(t,e,i){let s="min"===t?this.minInput:this.maxInput;if(s){let t=this.inputTypeFormats[s.type],o=this.chart.time;if(t){let r=o.dateFormat(t,e);s.min!==r&&(s.min=r);let n=o.dateFormat(t,i);s.max!==n&&(s.max=n)}}}showInput(t){let e="min"===t?this.minDateBox:this.maxDateBox,i="min"===t?this.minInput:this.maxInput;if(i&&e&&this.inputGroup){let t="text"===i.type,{translateX:s=0,translateY:o=0}=this.inputGroup,{x:r=0,width:n=0,height:a=0}=e,{inputBoxWidth:l}=this.options;ic(i,{width:t?n+(l?-2:20)+"px":"auto",height:a-2+"px",border:"2px solid silver"}),t&&l?ic(i,{left:s+r+"px",top:o+"px"}):ic(i,{left:Math.min(Math.round(r+s-(i.offsetWidth-n)/2),this.chart.chartWidth-i.offsetWidth)+"px",top:o-(i.offsetHeight-a)/2+"px"})}}hideInput(t){let e="min"===t?this.minInput:this.maxInput;e&&ic(e,{top:"-9999em",border:0,width:"1px",height:"1px"})}defaultInputDateParser(t,e,i){return i?.parse(t)||0}drawInput(t){let{chart:e,div:i,inputGroup:s}=this,o=this,r=e.renderer.style||{},n=e.renderer,a=e.options.rangeSelector,l=ia.lang,h="min"===t;function d(t){let{maxInput:i,minInput:s}=o,r=e.xAxis[0],n=e.scroller?.getUnionExtremes()||r,a=n.dataMin,l=n.dataMax,d=e.xAxis[0].getExtremes()[t],c=o.getInputValue(t);iy(c)&&c!==d&&(h&&i&&iy(a)?c>Number(i.getAttribute("data-hc-time"))?c=void 0:c<a&&(c=a):s&&iy(l)&&(c<Number(s.getAttribute("data-hc-time"))?c=void 0:c>l&&(c=l)),void 0!==c&&r.setExtremes(h?c:r.min,h?r.max:c,void 0,void 0,{trigger:"rangeSelectorInput"}))}let c=l[h?"rangeSelectorFrom":"rangeSelectorTo"]||"",p=n.label(c,0).addClass("highcharts-range-label").attr({padding:2*!!c,height:c?a.inputBoxHeight:0}).add(s),u=n.label("",0).addClass("highcharts-range-input").attr({padding:2,width:a.inputBoxWidth,height:a.inputBoxHeight,"text-align":"center"}).on("click",function(){o.showInput(t),o[t+"Input"].focus()});e.styledMode||u.attr({stroke:a.inputBoxBorderColor,"stroke-width":1}),u.add(s);let g=id("input",{name:t,className:"highcharts-range-selector"},void 0,i);g.setAttribute("type",iP(a.inputDateFormat||"%e %b %Y")),e.styledMode||(p.css(iM(r,a.labelStyle)),u.css(iM({color:"#333333"},r,a.inputStyle)),ic(g,im({position:"absolute",border:0,boxShadow:"0 0 15px rgba(0,0,0,0.3)",width:"1px",height:"1px",padding:0,textAlign:"center",fontSize:r.fontSize,fontFamily:r.fontFamily,top:"-9999em"},a.inputStyle))),g.onfocus=()=>{o.showInput(t)},g.onblur=()=>{g===P().doc.activeElement&&d(t),o.hideInput(t),o.setInputValue(t),g.blur()};let x=!1;return g.onchange=()=>{x||(d(t),o.hideInput(t),g.blur())},g.onkeypress=e=>{13===e.keyCode&&d(t)},g.onkeydown=e=>{x=!0,("ArrowUp"===e.key||"ArrowDown"===e.key||"Tab"===e.key)&&d(t)},g.onkeyup=()=>{x=!1},{dateBox:u,input:g,label:p}}getPosition(){let t=this.chart,e=t.options.rangeSelector,i="top"===e.verticalAlign?t.plotTop-t.axisOffset[0]:0;return{buttonTop:i+e.buttonPosition.y,inputTop:i+e.inputPosition.y-10}}getYTDExtremes(t,e){let i=this.chart.time,s=i.toParts(t)[0];return{max:t,min:Math.max(e,i.makeTime(s,0))}}createElements(){let t=this.chart,e=t.renderer,i=t.container,s=t.options,o=s.rangeSelector,r=o.inputEnabled,n=iA(s.chart.style?.zIndex,0)+1;!1!==o.enabled&&(this.group=e.g("range-selector-group").attr({zIndex:7}).add(),this.div=id("div",void 0,{position:"relative",height:0,zIndex:n}),this.buttonOptions.length&&this.renderButtons(),i.parentNode&&i.parentNode.insertBefore(this.div,i),r&&this.createInputs())}createInputs(){this.inputGroup=this.chart.renderer.g("input-group").add(this.group);let t=this.drawInput("min");this.minDateBox=t.dateBox,this.minLabel=t.label,this.minInput=t.input;let e=this.drawInput("max");this.maxDateBox=e.dateBox,this.maxLabel=e.label,this.maxInput=e.input}render(t,e){if(!1===this.options.enabled)return;let i=this.chart,s=i.options.rangeSelector;if(s.inputEnabled){this.inputGroup||this.createInputs(),this.setInputValue("min",t),this.setInputValue("max",e),this.chart.styledMode||(this.maxLabel?.css(s.labelStyle),this.minLabel?.css(s.labelStyle));let o=i.scroller&&i.scroller.getUnionExtremes()||i.xAxis[0]||{};if(ip(o.dataMin)&&ip(o.dataMax)){let t=i.xAxis[0].minRange||0;this.setInputExtremes("min",o.dataMin,Math.min(o.dataMax,this.getInputValue("max"))-t),this.setInputExtremes("max",Math.max(o.dataMin,this.getInputValue("min"))+t,o.dataMax)}if(this.inputGroup){let t=0;[this.minLabel,this.minDateBox,this.maxLabel,this.maxDateBox].forEach(e=>{if(e){let{width:i}=e.getBBox();i&&(e.attr({x:t}),t+=i+s.inputSpacing)}})}}else this.inputGroup&&(this.inputGroup.destroy(),delete this.inputGroup);!this.chart.styledMode&&this.zoomText&&this.zoomText.css(s.labelStyle),this.alignElements(),this.updateButtonStates()}renderButtons(){var t;let{chart:e,options:i}=this,s=ia.lang,o=e.renderer,r=iM(i.buttonTheme),n=r&&r.states;delete r.width,delete r.states,this.buttonGroup=o.g("range-selector-buttons").add(this.group);let a=this.dropdown=id("select",void 0,{position:"absolute",padding:0,border:0,cursor:"pointer",opacity:1e-4},this.div),l=e.userOptions.rangeSelector?.buttonTheme;this.dropdownLabel=o.button("",0,0,()=>{},iM(r,{"stroke-width":iA(r["stroke-width"],0),width:"auto",paddingLeft:iA(i.buttonTheme.paddingLeft,l?.padding,8),paddingRight:iA(i.buttonTheme.paddingRight,l?.padding,8)}),n&&n.hover,n&&n.select,n&&n.disabled).hide().add(this.group),ih(a,"touchstart",()=>{a.style.fontSize="16px"});let h=P().isMS?"mouseover":"mouseenter",d=P().isMS?"mouseout":"mouseleave";ih(a,h,()=>{ib(this.dropdownLabel.element,h)}),ih(a,d,()=>{ib(this.dropdownLabel.element,d)}),ih(a,"change",()=>{ib(this.buttons[a.selectedIndex-1].element,"click")}),this.zoomText=o.label(s.rangeSelectorZoom||"",0).attr({padding:i.buttonTheme.padding,height:i.buttonTheme.height,paddingLeft:0,paddingRight:0}).add(this.buttonGroup),this.chart.styledMode||(this.zoomText.css(i.labelStyle),(t=i.buttonTheme)["stroke-width"]??(t["stroke-width"]=0)),id("option",{textContent:this.zoomText.textStr,disabled:!0},void 0,a),this.createButtons()}createButtons(){let{options:t}=this,e=iM(t.buttonTheme),i=e&&e.states,s=e.width||28;delete e.width,delete e.states,this.buttonOptions.forEach((t,e)=>{this.createButton(t,e,s,i)})}createButton(t,e,i,s){let{dropdown:o,buttons:r,chart:n,options:a}=this,l=n.renderer,h=iM(a.buttonTheme);o?.add(id("option",{textContent:t.title||t.text}),e+2),r[e]=l.button(t.text??"",0,0,i=>{let s,o=t.events&&t.events.click;o&&(s=o.call(t,i)),!1!==s&&this.clickButton(e),this.isActive=!0},h,s&&s.hover,s&&s.select,s&&s.disabled).attr({"text-align":"center",width:i}).add(this.buttonGroup),t.title&&r[e].attr("title",t.title)}alignElements(){let{buttonGroup:t,buttons:e,chart:i,group:s,inputGroup:o,options:r,zoomText:n}=this,a=i.options,l=a.exporting&&!1!==a.exporting.enabled&&a.navigation&&a.navigation.buttonOptions,{buttonPosition:h,inputPosition:d,verticalAlign:c}=r,p=(t,e,s)=>l&&this.titleCollision(i)&&"top"===c&&s&&e.y-t.getBBox().height-12<(l.y||0)+(l.height||0)+i.spacing[0]?-40:0,u=i.plotLeft;if(s&&h&&d){let a=h.x-i.spacing[3];if(t){if(this.positionButtons(),!this.initialButtonGroupWidth){let t=0;n&&(t+=n.getBBox().width+5),e.forEach((i,s)=>{t+=i.width||0,s!==e.length-1&&(t+=r.buttonSpacing)}),this.initialButtonGroupWidth=t}u-=i.spacing[3];let o=p(t,h,"right"===h.align||"right"===d.align);this.alignButtonGroup(o),this.buttonGroup?.translateY&&this.dropdownLabel.attr({y:this.buttonGroup.translateY}),s.placed=t.placed=i.hasLoaded}let l=0;r.inputEnabled&&o&&(l=p(o,d,"right"===h.align||"right"===d.align),"left"===d.align?a=u:"right"===d.align&&(a=-Math.max(i.axisOffset[1],-l)),o.align({y:d.y,width:o.getBBox().width,align:d.align,x:d.x+a-2},!0,i.spacingBox),o.placed=i.hasLoaded),this.handleCollision(l),s.align({verticalAlign:c},!0,i.spacingBox);let g=s.alignAttr.translateY,x=s.getBBox().height+20,f=0;if("bottom"===c){let t=i.legend&&i.legend.options;f=g-(x=x+(t&&"bottom"===t.verticalAlign&&t.enabled&&!t.floating?i.legend.legendHeight+iA(t.margin,10):0)-20)-(r.floating?0:r.y)-(i.titleOffset?i.titleOffset[2]:0)-10}"top"===c?(r.floating&&(f=0),i.titleOffset&&i.titleOffset[0]&&(f=i.titleOffset[0]),f+=i.margin[0]-i.spacing[0]||0):"middle"===c&&(d.y===h.y?f=g:(d.y||h.y)&&(d.y<0||h.y<0?f-=Math.min(d.y,h.y):f=g-x)),s.translate(r.x,r.y+Math.floor(f));let{minInput:m,maxInput:b,dropdown:y}=this;r.inputEnabled&&m&&b&&(m.style.marginTop=s.translateY+"px",b.style.marginTop=s.translateY+"px"),y&&(y.style.marginTop=s.translateY+"px")}}redrawElements(){let t=this.chart,{inputBoxHeight:e,inputBoxBorderColor:i}=this.options;if(this.maxDateBox?.attr({height:e}),this.minDateBox?.attr({height:e}),t.styledMode||(this.maxDateBox?.attr({stroke:i}),this.minDateBox?.attr({stroke:i})),this.isDirty){this.isDirty=!1,this.isCollapsed=void 0;let t=this.options.buttons??[],e=Math.min(t.length,this.buttonOptions.length),{dropdown:i,options:s}=this,o=iM(s.buttonTheme),r=o&&o.states,n=o.width||28;if(t.length<this.buttonOptions.length)for(let e=this.buttonOptions.length-1;e>=t.length;e--){let t=this.buttons.pop();t?.destroy(),this.dropdown?.options.remove(e+1)}for(let s=e-1;s>=0;s--)if(0!==Object.keys(ig(t[s],this.buttonOptions[s])).length){let e=t[s];this.buttons[s].destroy(),i?.options.remove(s+1),this.createButton(e,s,n,r),this.computeButtonRange(e)}if(t.length>this.buttonOptions.length)for(let e=this.buttonOptions.length;e<t.length;e++)this.createButton(t[e],e,n,r),this.computeButtonRange(t[e]);this.buttonOptions=this.options.buttons??[],ip(this.options.selected)&&this.buttons.length&&this.clickButton(this.options.selected,!1)}}alignButtonGroup(t,e){let{chart:i,options:s,buttonGroup:o,dropdown:r,dropdownLabel:n}=this,{buttonPosition:a}=s,l=i.plotLeft-i.spacing[3],h=a.x-i.spacing[3],d=i.plotLeft;"right"===a.align?(h+=t-l,this.hasVisibleDropdown&&(d=i.chartWidth+t-this.maxButtonWidth()-20)):"center"===a.align&&(h-=l/2,this.hasVisibleDropdown&&(d=i.chartWidth/2-this.maxButtonWidth())),r&&ic(r,{left:d+"px",top:o?.translateY+"px"}),n?.attr({x:d}),o&&o.align({y:a.y,width:iA(e,this.initialButtonGroupWidth),align:a.align,x:h},!0,i.spacingBox)}positionButtons(){let{buttons:t,chart:e,options:i,zoomText:s}=this,o=e.hasLoaded?"animate":"attr",{buttonPosition:r}=i,n=e.plotLeft,a=n;s&&"hidden"!==s.visibility&&(s[o]({x:iA(n+r.x,n)}),a+=r.x+s.getBBox().width+5);for(let e=0,s=this.buttonOptions.length;e<s;++e)"hidden"!==t[e].visibility?(t[e][o]({x:a}),a+=(t[e].width||0)+i.buttonSpacing):t[e][o]({x:n})}handleCollision(t){let{chart:e,buttonGroup:i,inputGroup:s,initialButtonGroupWidth:o}=this,{buttonPosition:r,dropdown:n,inputPosition:a}=this.options,l=()=>{s&&i&&s.attr({translateX:s.alignAttr.translateX+(e.axisOffset[1]>=-t?0:-t),translateY:s.alignAttr.translateY+i.getBBox().height+10})};s&&i?a.align===r.align?(l(),o>e.plotWidth+t-20?this.collapseButtons():this.expandButtons()):o-t+s.getBBox().width>e.plotWidth?"responsive"===n?this.collapseButtons():l():this.expandButtons():i&&"responsive"===n&&(o>e.plotWidth?this.collapseButtons():this.expandButtons()),i&&("always"===n&&this.collapseButtons(),"never"===n&&this.expandButtons()),this.alignButtonGroup(t)}collapseButtons(){let{buttons:t,zoomText:e}=this;!0!==this.isCollapsed&&(this.isCollapsed=!0,e.hide(),t.forEach(t=>void t.hide()),this.showDropdown())}expandButtons(){let{buttons:t,zoomText:e}=this;!1!==this.isCollapsed&&(this.isCollapsed=!1,this.hideDropdown(),e.show(),t.forEach(t=>void t.show()),this.positionButtons())}showDropdown(){let{buttonGroup:t,dropdownLabel:e,dropdown:i}=this;t&&i&&(e.show(),ic(i,{visibility:"inherit"}),this.hasVisibleDropdown=!0)}hideDropdown(){let{dropdown:t}=this;t&&(this.dropdownLabel.hide(),ic(t,{visibility:"hidden",width:"1px",height:"1px"}),this.hasVisibleDropdown=!1)}getHeight(){let t=this.options,e=this.group,i=t.inputPosition,s=t.buttonPosition,o=t.y,r=s.y,n=i.y,a=0;if(t.height)return t.height;this.alignElements(),a=e?e.getBBox(!0).height+13+o:0;let l=Math.min(n,r);return(n<0&&r<0||n>0&&r>0)&&(a+=Math.abs(l)),a}titleCollision(t){return!(t.options.title.text||t.options.subtitle.text)}update(t,e=!0){let i=this.chart;if(iM(!0,this.options,t),this.options.selected&&this.options.selected>=this.options.buttons.length&&(this.options.selected=void 0,i.options.rangeSelector.selected=void 0),ip(t.enabled))return this.destroy(),this.init(i);this.isDirty=!!t.buttons,e&&this.render()}destroy(){let t=this,e=t.minInput,i=t.maxInput;t.eventsToUnbind&&(t.eventsToUnbind.forEach(t=>t()),t.eventsToUnbind=void 0),iu(t.buttons),e&&(e.onfocus=e.onblur=e.onchange=null),i&&(i.onfocus=i.onblur=i.onchange=null),ik(t,function(e,i){e&&"chart"!==i&&(e instanceof eK()?e.destroy():e instanceof window.HTMLElement&&ix(e),delete t[i]),e!==iS.prototype[i]&&(t[i]=null)},this),this.buttons=[]}}im(iS.prototype,{inputTypeFormats:{"datetime-local":"%Y-%m-%dT%H:%M:%S",date:"%Y-%m-%d",time:"%H:%M:%S"}});let iO={applyRadius:function(t,e){let i=[];for(let s=0;s<t.length;s++){let o=t[s][1],r=t[s][2];if("number"==typeof o&&"number"==typeof r)if(0===s)i.push(["M",o,r]);else if(s===t.length-1)i.push(["L",o,r]);else if(e){let n=t[s-1],a=t[s+1];if(n&&a){let t=n[1],s=n[2],l=a[1],h=a[2];if("number"==typeof t&&"number"==typeof l&&"number"==typeof s&&"number"==typeof h&&t!==l&&s!==h){let n=t<l?1:-1,a=s<h?1:-1;i.push(["L",o-n*Math.min(Math.abs(o-t),e),r-a*Math.min(Math.abs(r-s),e)],["C",o,r,o,r,o+n*Math.min(Math.abs(o-l),e),r+a*Math.min(Math.abs(r-h),e)])}}}else i.push(["L",o,r])}return i}},{pick:iE}=P(),{min:iB,max:iT,abs:iC}=Math;function iI(t,e,i){let s=e-1e-7,o=i||0,r=t.length-1,n,a;for(;o<=r;)if((a=s-t[n=r+o>>1].xMin)>0)o=n+1;else{if(!(a<0))return n;r=n-1}return o>0?o-1:0}function iR(t,e){let i=iI(t,e.x+1)+1;for(;i--;){var s;if(t[i].xMax>=e.x&&(s=t[i],e.x<=s.xMax&&e.x>=s.xMin&&e.y<=s.yMax&&e.y>=s.yMin))return i}return -1}function iD(t){let e=[];if(t.length){e.push(["M",t[0].start.x,t[0].start.y]);for(let i=0;i<t.length;++i)e.push(["L",t[i].end.x,t[i].end.y])}return e}function iL(t,e){t.yMin=iT(t.yMin,e.yMin),t.yMax=iB(t.yMax,e.yMax),t.xMin=iT(t.xMin,e.xMin),t.xMax=iB(t.xMax,e.xMax)}let iG=function(t,e,i){let s=[],o=i.chartObstacles,r=iR(o,t),n=iR(o,e),a,l=iE(i.startDirectionX,iC(e.x-t.x)>iC(e.y-t.y))?"x":"y",h,d,c,p;function u(t,e,i,s,o){let r={x:t.x,y:t.y};return r[e]=i[s||e]+(o||0),r}function g(t,e,i){let s=iC(e[i]-t[i+"Min"])>iC(e[i]-t[i+"Max"]);return u(e,i,t,i+(s?"Max":"Min"),s?1:-1)}n>-1?(a={start:d=g(o[n],e,l),end:e},p=d):p=e,r>-1&&(d=g(h=o[r],t,l),s.push({start:t,end:d}),d[l]>=t[l]==d[l]>=p[l]&&(c=t[l="y"===l?"x":"y"]<e[l],s.push({start:d,end:u(d,l,h,l+(c?"Max":"Min"),c?1:-1)}),l="y"===l?"x":"y"));let x=s.length?s[s.length-1].end:t;d=u(x,l,p),s.push({start:x,end:d});let f=u(d,l="y"===l?"x":"y",p);return s.push({start:d,end:f}),s.push(a),{path:iO.applyRadius(iD(s),i.radius),obstacles:s}};function iz(t,e,i){let s=iE(i.startDirectionX,iC(e.x-t.x)>iC(e.y-t.y)),o=s?"x":"y",r=[],n=i.obstacleMetrics,a=iB(t.x,e.x)-n.maxWidth-10,l=iT(t.x,e.x)+n.maxWidth+10,h=iB(t.y,e.y)-n.maxHeight-10,d=iT(t.y,e.y)+n.maxHeight+10,c,p,u,g=!1,x=i.chartObstacles,f=iI(x,l),m=iI(x,a);function b(t,e,i){let s,o,r,n,a=t.x<e.x?1:-1;t.x<e.x?(s=t,o=e):(s=e,o=t),t.y<e.y?(n=t,r=e):(n=e,r=t);let l=a<0?iB(iI(x,o.x),x.length-1):0;for(;x[l]&&(a>0&&x[l].xMin<=o.x||a<0&&x[l].xMax>=s.x);){if(x[l].xMin<=o.x&&x[l].xMax>=s.x&&x[l].yMin<=r.y&&x[l].yMax>=n.y){if(i)return{y:t.y,x:t.x<e.x?x[l].xMin-1:x[l].xMax+1,obstacle:x[l]};return{x:t.x,y:t.y<e.y?x[l].yMin-1:x[l].yMax+1,obstacle:x[l]}}l+=a}return e}function y(t,e,i,s,o){let r=o.soft,n=o.hard,a=s?"x":"y",l={x:e.x,y:e.y},h={x:e.x,y:e.y},d=t[a+"Max"]>=r[a+"Max"],c=t[a+"Min"]<=r[a+"Min"],p=t[a+"Max"]>=n[a+"Max"],u=t[a+"Min"]<=n[a+"Min"],g=iC(t[a+"Min"]-e[a]),x=iC(t[a+"Max"]-e[a]),f=10>iC(g-x)?e[a]<i[a]:x<g;h[a]=t[a+"Min"],l[a]=t[a+"Max"];let m=b(e,h,s)[a]!==h[a],y=b(e,l,s)[a]!==l[a];return f=m?!y||f:!y&&f,f=c?!d||f:!d&&f,f=u?!p||f:!p&&f}for((f=iR(x=x.slice(m,f+1),e))>-1&&(u=function(t,e,s){let o=iB(t.xMax-e.x,e.x-t.xMin)<iB(t.yMax-e.y,e.y-t.yMin),r=y(t,e,s,o,{soft:i.hardBounds,hard:i.hardBounds});return o?{y:e.y,x:t[r?"xMax":"xMin"]+(r?1:-1)}:{x:e.x,y:t[r?"yMax":"yMin"]+(r?1:-1)}}(x[f],e,t),r.push({end:e,start:u}),e=u);(f=iR(x,e))>-1;)p=e[o]-t[o]<0,(u={x:e.x,y:e.y})[o]=x[f][p?o+"Max":o+"Min"]+(p?1:-1),r.push({end:e,start:u}),e=u;return{path:iD(c=(c=function t(e,s,o){let r,n,c,p,u,f,m;if(e.x===s.x&&e.y===s.y)return[];let v=o?"x":"y",M=i.obstacleOptions.margin,k={soft:{xMin:a,xMax:l,yMin:h,yMax:d},hard:i.hardBounds};return(u=iR(x,e))>-1?(p=y(u=x[u],e,s,o,k),iL(u,i.hardBounds),m=o?{y:e.y,x:u[p?"xMax":"xMin"]+(p?1:-1)}:{x:e.x,y:u[p?"yMax":"yMin"]+(p?1:-1)},(f=iR(x,m))>-1&&(iL(f=x[f],i.hardBounds),m[v]=p?iT(u[v+"Max"]-M+1,(f[v+"Min"]+u[v+"Max"])/2):iB(u[v+"Min"]+M-1,(f[v+"Max"]+u[v+"Min"])/2),e.x===m.x&&e.y===m.y?(g&&(m[v]=p?iT(u[v+"Max"],f[v+"Max"])+1:iB(u[v+"Min"],f[v+"Min"])-1),g=!g):g=!1),n=[{start:e,end:m}]):(r=b(e,{x:o?s.x:e.x,y:o?e.y:s.y},o),n=[{start:e,end:{x:r.x,y:r.y}}],r[o?"x":"y"]!==s[o?"x":"y"]&&(p=y(r.obstacle,r,s,!o,k),iL(r.obstacle,i.hardBounds),c={x:o?r.x:r.obstacle[p?"xMax":"xMin"]+(p?1:-1),y:o?r.obstacle[p?"yMax":"yMin"]+(p?1:-1):r.y},o=!o,n=n.concat(t({x:r.x,y:r.y},c,o)))),n=n.concat(t(n[n.length-1].end,s,!o))}(t,e,s)).concat(r.reverse())),obstacles:c}}iG.requiresObstacles=!0,iz.requiresObstacles=!0;let iN={connectors:{type:"straight",radius:0,lineWidth:1,marker:{enabled:!1,align:"center",verticalAlign:"middle",inside:!1,lineWidth:1},startMarker:{symbol:"diamond"},endMarker:{symbol:"arrow-filled"}}},{setOptions:iW}=P(),{defined:iH,error:iF,merge:iU}=P();function iX(t){let e=t.shapeArgs;if(e)return{xMin:e.x||0,xMax:(e.x||0)+(e.width||0),yMin:e.y||0,yMax:(e.y||0)+(e.height||0)};let i=t.graphic&&t.graphic.getBBox();return i?{xMin:t.plotX-i.width/2,xMax:t.plotX+i.width/2,yMin:t.plotY-i.height/2,yMax:t.plotY+i.height/2}:null}!function(t){function e(t){let e,i,s=iX(this);switch(t.align){case"right":e="xMax";break;case"left":e="xMin"}switch(t.verticalAlign){case"top":i="yMin";break;case"bottom":i="yMax"}return{x:e?s[e]:(s.xMin+s.xMax)/2,y:i?s[i]:(s.yMin+s.yMax)/2}}function i(t,e){let i;return!iH(e)&&(i=iX(this))&&(e={x:(i.xMin+i.xMax)/2,y:(i.yMin+i.yMax)/2}),Math.atan2(e.y-t.y,t.x-e.x)}function s(t,e,i){let s=2*Math.PI,o=iX(this),r=o.xMax-o.xMin,n=o.yMax-o.yMin,a=Math.atan2(n,r),l=r/2,h=n/2,d=o.xMin+l,c=o.yMin+h,p={x:d,y:c},u=t,g=1,x=!1,f=1,m=1;for(;u<-Math.PI;)u+=s;for(;u>Math.PI;)u-=s;return g=Math.tan(u),u>-a&&u<=a?(m=-1,x=!0):u>a&&u<=Math.PI-a?m=-1:u>Math.PI-a||u<=-(Math.PI-a)?(f=-1,x=!0):f=-1,x?(p.x+=f*l,p.y+=m*l*g):(p.x+=n/(2*g)*f,p.y+=m*h),i.x!==d&&(p.x=i.x),i.y!==c&&(p.y=i.y),{x:p.x+e*Math.cos(u),y:p.y-e*Math.sin(u)}}t.compose=function(t,o,r){let n=r.prototype;n.getPathfinderAnchorPoint||(t.prototype.callbacks.push(function(t){!1!==t.options.connectors.enabled&&((t.options.pathfinder||t.series.reduce(function(t,e){return e.options&&iU(!0,e.options.connectors=e.options.connectors||{},e.options.pathfinder),t||e.options&&e.options.pathfinder},!1))&&(iU(!0,t.options.connectors=t.options.connectors||{},t.options.pathfinder),iF('WARNING: Pathfinder options have been renamed. Use "chart.connectors" or "series.connectors" instead.')),this.pathfinder=new o(this),this.pathfinder.update(!0))}),n.getMarkerVector=s,n.getPathfinderAnchorPoint=e,n.getRadiansToVector=i,iW(iN))}}(m||(m={}));let iY=m;var iV=k(260),i_=k.n(iV);let{addEvent:ij,defined:iq,pick:iZ,splat:i$}=P(),iK=Math.max,iJ=Math.min;class iQ{static compose(t,e){iY.compose(t,iQ,e)}constructor(t){this.init(t)}init(t){this.chart=t,this.connections=[],ij(t,"redraw",function(){this.pathfinder.update()})}update(t){let e=this.chart,i=this,s=i.connections;i.connections=[],e.series.forEach(function(t){t.visible&&!t.options.isInternal&&t.points.forEach(function(t){let s,o=t.options;o&&o.dependency&&(o.connect=o.dependency);let r=t.options?.connect?i$(t.options.connect):[];t.visible&&!1!==t.isInside&&r.forEach(o=>{let r="string"==typeof o?o:o.to;r&&(s=e.get(r)),s instanceof i_()&&s.series.visible&&s.visible&&!1!==s.isInside&&i.connections.push(new N(t,s,"string"==typeof o?{}:o))})})});for(let t=0,e,o,r=s.length,n=i.connections.length;t<r;++t){o=!1;let r=s[t];for(e=0;e<n;++e){let t=i.connections[e];if((r.options&&r.options.type)===(t.options&&t.options.type)&&r.fromPoint===t.fromPoint&&r.toPoint===t.toPoint){t.graphics=r.graphics,o=!0;break}}o||r.destroy()}delete this.chartObstacles,delete this.lineObstacles,i.renderConnections(t)}renderConnections(t){t?this.chart.series.forEach(function(t){let e=function(){let e=t.chart.pathfinder;(e&&e.connections||[]).forEach(function(e){e.fromPoint&&e.fromPoint.series===t&&e.render()}),t.pathfinderRemoveRenderEvent&&(t.pathfinderRemoveRenderEvent(),delete t.pathfinderRemoveRenderEvent)};!1===t.options.animation?e():t.pathfinderRemoveRenderEvent=ij(t,"afterAnimate",e)}):this.connections.forEach(function(t){t.render()})}getChartObstacles(t){let e=this.chart.series,i=iZ(t.algorithmMargin,0),s=[],o;for(let t=0,o=e.length;t<o;++t)if(e[t].visible&&!e[t].options.isInternal)for(let o=0,r=e[t].points.length,n,a;o<r;++o)(a=e[t].points[o]).visible&&(n=function(t){let e=t.shapeArgs;if(e)return{xMin:e.x||0,xMax:(e.x||0)+(e.width||0),yMin:e.y||0,yMax:(e.y||0)+(e.height||0)};let i=t.graphic&&t.graphic.getBBox();return i?{xMin:t.plotX-i.width/2,xMax:t.plotX+i.width/2,yMin:t.plotY-i.height/2,yMax:t.plotY+i.height/2}:null}(a))&&s.push({xMin:n.xMin-i,xMax:n.xMax+i,yMin:n.yMin-i,yMax:n.yMax+i});return s=s.sort(function(t,e){return t.xMin-e.xMin}),iq(t.algorithmMargin)||(o=t.algorithmMargin=function(t){let e,i=t.length,s=[];for(let o=0;o<i;++o)for(let r=o+1;r<i;++r)(e=function t(e,i,s){let o=iZ(s,10),r=e.yMax+o>i.yMin-o&&e.yMin-o<i.yMax+o,n=e.xMax+o>i.xMin-o&&e.xMin-o<i.xMax+o,a=r?e.xMin>i.xMax?e.xMin-i.xMax:i.xMin-e.xMax:1/0,l=n?e.yMin>i.yMax?e.yMin-i.yMax:i.yMin-e.yMax:1/0;return n&&r?o?t(e,i,Math.floor(o/2)):1/0:iJ(a,l)}(t[o],t[r]))<80&&s.push(e);return s.push(80),iK(Math.floor(s.sort(function(t,e){return t-e})[Math.floor(s.length/10)]/2-1),1)}(s),s.forEach(function(t){t.xMin-=o,t.xMax+=o,t.yMin-=o,t.yMax+=o})),s}getObstacleMetrics(t){let e=0,i=0,s,o,r=t.length;for(;r--;)s=t[r].xMax-t[r].xMin,o=t[r].yMax-t[r].yMin,e<s&&(e=s),i<o&&(i=o);return{maxHeight:i,maxWidth:e}}getAlgorithmStartDirection(t){let e="left"!==t.align&&"right"!==t.align,i="top"!==t.verticalAlign&&"bottom"!==t.verticalAlign;return e?!!i&&void 0:!!i||void 0}}iQ.prototype.algorithms={fastAvoid:iz,straight:function(t,e){return{path:[["M",t.x,t.y],["L",e.x,e.y]],obstacles:[{start:t,end:e}]}},simpleConnect:iG};let i0=P();i0.Pathfinder=i0.Pathfinder||iQ,T.compose(i0.SVGRenderer),i0.Pathfinder.compose(i0.Chart,i0.Point);let{addEvent:i1,defined:i2,isNumber:i3,pick:i5}=P();function i6(){let t=this.chart.options.chart;!this.horiz&&i3(this.options.staticScale)&&(!t.height||t.scrollablePlotArea&&t.scrollablePlotArea.minHeight)&&(this.staticScale=this.options.staticScale)}function i4(){if("adjustHeight"!==this.redrawTrigger){for(let t of this.axes||[]){let e=t.chart,i=!!e.initiatedScale&&e.options.animation,s=t.options.staticScale;if(t.staticScale&&i2(t.min)){let o=i5(t.brokenAxis&&t.brokenAxis.unitLength,t.max+t.tickInterval-t.min)*s,r=(o=Math.max(o,s))-e.plotHeight;!e.scrollablePixelsY&&Math.abs(r)>=1&&(e.plotHeight=o,e.redrawTrigger="adjustHeight",e.setSize(void 0,e.chartHeight+r,i)),t.series.forEach(function(t){let i=t.sharedClipKey&&e.sharedClips[t.sharedClipKey];i&&i.attr(e.inverted?{width:e.plotHeight}:{height:e.plotHeight})})}}this.initiatedScale=!0}this.redrawTrigger=null}let i8={compose:function(t,e){let i=e.prototype;i.adjustHeight||(i1(t,"afterSetOptions",i6),i.adjustHeight=i4,i1(e,"render",i.adjustHeight))}},i9=P();i8.compose(i9.Axis,i9.Chart);let{correctFloat:i7,isNumber:st,isObject:se}=P(),{column:{prototype:{pointClass:si}}}=tB().seriesTypes,{extend:ss}=P();class so extends si{static getColorByCategory(t,e){let i=t.options.colors||t.chart.options.colors,s=i?i.length:t.chart.options.chart.colorCount,o=e.y%s,r=i?.[o];return{colorIndex:o,color:r}}resolveColor(){let t=this.series;if(t.options.colorByPoint&&!this.options.color){let e=so.getColorByCategory(t,this);t.chart.styledMode||(this.color=e.color),this.options.colorIndex||(this.colorIndex=e.colorIndex)}else this.color=this.options.color||t.color}constructor(t,e){super(t,e),this.y||(this.y=0)}applyOptions(t,e){return super.applyOptions(t,e),this.x2=this.series.chart.time.parse(this.x2),this.isNull=!this.isValid?.(),this}setState(){super.setState.apply(this,arguments),this.series.drawPoint(this,this.series.getAnimationVerb())}isValid(){return"number"==typeof this.x&&"number"==typeof this.x2}}ss(so.prototype,{ttBelow:!1,tooltipDateKeys:["x","x2"]});let{composed:sr,noop:sn}=P(),{parse:sa}=tO(),{column:sl}=tB().seriesTypes,{addEvent:sh,clamp:sd,crisp:sc,defined:sp,extend:su,find:sg,isNumber:sx,isObject:sf,merge:sm,pick:sb,pushUnique:sy,relativeLength:sv}=P();function sM(){let t,e;if(this.isXAxis){for(let i of(t=sb(this.dataMax,-Number.MAX_VALUE),this.series)){let s=i.dataTable.getColumn("x2",!0)||i.dataTable.getColumn("end",!0);if(s)for(let i of s)sx(i)&&i>t&&(t=i,e=!0)}e&&(this.dataMax=t)}}class sk extends sl{static compose(t){sy(sr,"Series.XRange")&&sh(t,"afterGetSeriesExtremes",sM)}init(){super.init.apply(this,arguments),this.options.stacking=void 0}getColumnMetrics(){let t=()=>{for(let t of this.chart.series){let e=t.xAxis;t.xAxis=t.yAxis,t.yAxis=e}};t();let e=super.getColumnMetrics();return t(),e}cropData(t,e,i){let s=t.getColumn("x")||[],o=t.getColumn("x2");t.setColumn("x",o,void 0,{silent:!0});let r=super.cropData(t,e,i);return t.setColumn("x",s.slice(r.start,r.end),void 0,{silent:!0}),r}findPointIndex(t){let e,{cropStart:i,points:s}=this,{id:o}=t;if(o){let t=sg(s,t=>t.id===o);e=t?t.index:void 0}if(void 0===e){let i=sg(s,e=>e.x===t.x&&e.x2===t.x2&&!e.touched);e=i?i.index:void 0}return this.cropped&&sx(e)&&sx(i)&&e>=i&&(e-=i),e}alignDataLabel(t){let e=t.plotX;t.plotX=sb(t.dlBox?.centerX,t.plotX),t.dataLabel&&t.shapeArgs?.width&&t.dataLabel.css({width:`${t.shapeArgs.width}px`}),super.alignDataLabel.apply(this,arguments),t.plotX=e}translatePoint(t){let e=this.xAxis,i=this.yAxis,s=this.columnMetrics,o=this.options,r=o.minPointLength||0,n=(t.shapeArgs?.width||0)/2,a=this.pointXOffset=s.offset,l=sb(t.x2,t.x+(t.len||0)),h=o.borderRadius,d=this.chart.plotTop,c=this.chart.plotLeft,p=t.plotX,u=e.translate(l,0,0,0,1),g=Math.abs(u-p),x=this.chart.inverted,f=sb(o.borderWidth,1),m,b,y=s.offset,v=Math.round(s.width),M,k,A,w;r&&((m=r-g)<0&&(m=0),p-=m/2,u+=m/2),p=Math.max(p,-10),u=sd(u,-10,e.len+10),sp(t.options.pointWidth)&&(y-=(Math.ceil(t.options.pointWidth)-v)/2,v=Math.ceil(t.options.pointWidth)),o.pointPlacement&&sx(t.plotY)&&i.categories&&(t.plotY=i.translate(t.y,0,1,0,1,o.pointPlacement));let P=sc(Math.min(p,u),f),S=sc(Math.max(p,u),f)-P,O=Math.min(sv("object"==typeof h?h.radius:h||0,v),Math.min(S,v)/2),E={x:P,y:sc((t.plotY||0)+y,f),width:S,height:v,r:O};t.shapeArgs=E,x?t.tooltipPos[1]+=a+n:t.tooltipPos[0]-=n+a-E.width/2,k=(M=E.x)+E.width,M<0||k>e.len?(M=sd(M,0,e.len),A=(k=sd(k,0,e.len))-M,t.dlBox=sm(E,{x:M,width:k-M,centerX:A?A/2:null})):t.dlBox=null;let B=t.tooltipPos,T=+!!x,C=+!x,I=this.columnMetrics?this.columnMetrics.offset:-s.width/2;x?B[T]+=E.width/2:B[T]=sd(B[T]+(e.reversed?-1:0)*E.width,e.left-c,e.left+e.len-c-1),B[C]=sd(B[C]+(x?-1:1)*I,i.top-d,i.top+i.len-d-1),(b=t.partialFill)&&(sf(b)&&(b=b.amount),sx(b)||(b=0),t.partShapeArgs=sm(E),w=Math.max(Math.round(g*b+t.plotX-p),0),t.clipRectArgs={x:e.reversed?E.x+g-w:E.x,y:E.y,width:w,height:E.height}),t.key=t.category||t.name,t.yCategory=i.categories?.[t.y??-1]}translate(){for(let t of(super.translate.apply(this,arguments),this.points))this.translatePoint(t)}drawPoint(t,e){let i=this.options,s=this.chart.renderer,o=t.shapeType,r=t.shapeArgs,n=t.partShapeArgs,a=t.clipRectArgs,l=t.state,h=i.states[l||"normal"]||{},d=void 0===l?"attr":e,c=this.pointAttribs(t,l),p=sb(this.chart.options.chart.animation,h.animation),u=t.graphic,g=t.partialFill;if(t.isNull||!1===t.visible)u&&(t.graphic=u.destroy());else if(u?u.rect[e](r):(t.graphic=u=s.g("point").addClass(t.getClassName()).add(t.group||this.group),u.rect=s[o](sm(r)).addClass(t.getClassName()).addClass("highcharts-partfill-original").add(u)),n&&(u.partRect?(u.partRect[e](sm(n)),u.partialClipRect[e](sm(a))):(u.partialClipRect=s.clipRect(a.x,a.y,a.width,a.height),u.partRect=s[o](n).addClass("highcharts-partfill-overlay").add(u).clip(u.partialClipRect))),!this.chart.styledMode&&(u.rect[e](c,p).shadow(i.shadow),n)){sf(g)||(g={}),sf(i.partialFill)&&(g=sm(i.partialFill,g));let e=g.fill||sa(c.fill).brighten(-.3).get()||sa(t.color||this.color).brighten(-.3).get();c.fill=e,u.partRect[d](c,p).shadow(i.shadow)}}drawPoints(){let t=this.getAnimationVerb();for(let e of this.points)this.drawPoint(e,t)}getAnimationVerb(){return this.chart.pointCount<(this.options.animationLimit||250)?"animate":"attr"}isPointInside(t){let e=t.shapeArgs,i=t.plotX,s=t.plotY;return e?void 0!==i&&void 0!==s&&s>=0&&s<=this.yAxis.len&&(e.x||0)+(e.width||0)>=0&&i<=this.xAxis.len:super.isPointInside.apply(this,arguments)}}sk.defaultOptions=sm(sl.defaultOptions,{colorByPoint:!0,dataLabels:{formatter:function(){let t=this.partialFill;if(se(t)&&(t=t.amount),st(t)&&t>0)return i7(100*t)+"%"},inside:!0,verticalAlign:"middle",style:{whiteSpace:"nowrap"}},tooltip:{headerFormat:'<span style="font-size: 0.8em">{ucfirst point.x} - {point.x2}</span><br/>',pointFormat:'<span style="color:{point.color}">●</span> {series.name}: <b>{point.yCategory}</b><br/>'},borderRadius:3,pointRange:0}),su(sk.prototype,{pointClass:so,pointArrayMap:["x2","y"],getExtremesFromAll:!0,keysAffectYAxis:["y"],parallelArrays:["x","x2","y"],requireSorting:!1,type:"xrange",animate:tB().series.prototype.animate,autoIncrement:sn,buildKDTree:sn}),tB().registerSeriesType("xrange",sk);/**
 * @license Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/xrange
 * @requires highcharts
 *
 * X-range series
 *
 * (c) 2010-2025 Torstein Honsi, Lars A. V. Cabrera
 *
 * License: www.highcharts.com/license
 */let sA=P();sk.compose(sA.Axis);let{xrange:{prototype:{pointClass:sw}}}=tB().seriesTypes;class sP extends sw{static setGanttPointAliases(t,e){t.x=t.start=e.time.parse(t.start??t.x),t.x2=t.end=e.time.parse(t.end??t.x2),t.partialFill=t.completed=t.completed??t.partialFill}applyOptions(t,e){let i=super.applyOptions(t,e);return sP.setGanttPointAliases(i,i.series.chart),this.isNull=!this.isValid?.(),i}isValid(){return("number"==typeof this.start||"number"==typeof this.x)&&("number"==typeof this.end||"number"==typeof this.x2||this.milestone)}}let{isNumber:sS}=P();var sO=k(184),sE=k.n(sO);let{addEvent:sB,find:sT,fireEvent:sC,isArray:sI,isNumber:sR,pick:sD}=P();!function(t){function e(){void 0!==this.brokenAxis&&this.brokenAxis.setBreaks(this.options.breaks,!1)}function i(){this.brokenAxis?.hasBreaks&&(this.options.ordinal=!1)}function s(){let t=this.brokenAxis;if(t?.hasBreaks){let e=this.tickPositions,i=this.tickPositions.info,s=[];for(let i=0;i<e.length;i++)t.isInAnyBreak(e[i])||s.push(e[i]);this.tickPositions=s,this.tickPositions.info=i}}function o(){this.brokenAxis||(this.brokenAxis=new h(this))}function r(){let{isDirty:t,options:{connectNulls:e},points:i,xAxis:s,yAxis:o}=this;if(t){let t=i.length;for(;t--;){let r=i[t],n=(null!==r.y||!1!==e)&&(s?.brokenAxis?.isInAnyBreak(r.x,!0)||o?.brokenAxis?.isInAnyBreak(r.y,!0));r.visible=!n&&!1!==r.options.visible}}}function n(){this.drawBreaks(this.xAxis,["x"]),this.drawBreaks(this.yAxis,sD(this.pointArrayMap,["y"]))}function a(t,e){let i,s,o,r=this,n=r.points;if(t?.brokenAxis?.hasBreaks){let a=t.brokenAxis;e.forEach(function(e){i=a?.breakArray||[],s=t.isXAxis?t.min:sD(r.options.threshold,t.min);let l=t?.options?.breaks?.filter(function(t){let e=!0;for(let s=0;s<i.length;s++){let o=i[s];if(o.from===t.from&&o.to===t.to){e=!1;break}}return e});n.forEach(function(r){o=sD(r["stack"+e.toUpperCase()],r[e]),i.forEach(function(e){if(sR(s)&&sR(o)){let i="";s<e.from&&o>e.to||s>e.from&&o<e.from?i="pointBreak":(s<e.from&&o>e.from&&o<e.to||s>e.from&&o>e.to&&o<e.from)&&(i="pointInBreak"),i&&sC(t,i,{point:r,brk:e})}}),l?.forEach(function(e){sC(t,"pointOutsideOfBreak",{point:r,brk:e})})})})}}function l(){let t=this.currentDataGrouping,e=t?.gapSize,i=this.points.slice(),s=this.yAxis,o=this.options.gapSize,r=i.length-1;if(o&&r>0){let t,n;for("value"!==this.options.gapUnit&&(o*=this.basePointRange),e&&e>o&&e>=this.basePointRange&&(o=e);r--;)if(n&&!1!==n.visible||(n=i[r+1]),t=i[r],!1!==n.visible&&!1!==t.visible){if(n.x-t.x>o){let e=(t.x+n.x)/2;i.splice(r+1,0,{isNull:!0,x:e}),s.stacking&&this.options.stacking&&((s.stacking.stacks[this.stackKey][e]=new(sE())(s,s.options.stackLabels,!1,e,this.stack)).total=0)}n=t}}return this.getGraphPath(i)}t.compose=function(t,h){if(!t.keepProps.includes("brokenAxis")){t.keepProps.push("brokenAxis"),sB(t,"init",o),sB(t,"afterInit",e),sB(t,"afterSetTickPositions",s),sB(t,"afterSetOptions",i);let d=h.prototype;d.drawBreaks=a,d.gappedPath=l,sB(h,"afterGeneratePoints",r),sB(h,"afterRender",n)}return t};class h{static isInBreak(t,e){let i,s=t.repeat||1/0,o=t.from,r=t.to-t.from,n=e>=o?(e-o)%s:s-(o-e)%s;return t.inclusive?n<=r:n<r&&0!==n}static lin2Val(t){let e=this.brokenAxis,i=e?.breakArray;if(!i||!sR(t))return t;let s=t,o,r;for(r=0;r<i.length&&!((o=i[r]).from>=s);r++)o.to<s?s+=o.len:h.isInBreak(o,s)&&(s+=o.len);return s}static val2Lin(t){let e=this.brokenAxis,i=e?.breakArray;if(!i||!sR(t))return t;let s=t,o,r;for(r=0;r<i.length;r++)if((o=i[r]).to<=t)s-=o.len;else if(o.from>=t)break;else if(h.isInBreak(o,t)){s-=t-o.from;break}return s}constructor(t){this.hasBreaks=!1,this.axis=t}findBreakAt(t,e){return sT(e,function(e){return e.from<t&&t<e.to})}isInAnyBreak(t,e){let i=this.axis,s=i.options.breaks||[],o=s.length,r,n,a;if(o&&sR(t)){for(;o--;)h.isInBreak(s[o],t)&&(r=!0,n||(n=sD(s[o].showPoints,!i.isXAxis)));a=r&&e?r&&!n:r}return a}setBreaks(t,e){let i=this,s=i.axis,o=s.chart.time,r=sI(t)&&!!t.length&&!!Object.keys(t[0]).length;s.isDirty=i.hasBreaks!==r,i.hasBreaks=r,t?.forEach(t=>{t.from=o.parse(t.from)||0,t.to=o.parse(t.to)||0}),t!==s.options.breaks&&(s.options.breaks=s.userOptions.breaks=t),s.forceRedraw=!0,s.series.forEach(function(t){t.isDirty=!0}),r||s.val2lin!==h.val2Lin||(delete s.val2lin,delete s.lin2val),r&&(s.userOptions.ordinal=!1,s.lin2val=h.lin2Val,s.val2lin=h.val2Lin,s.setExtremes=function(t,e,o,r,n){if(i.hasBreaks){let s,o=this.options.breaks||[];for(;s=i.findBreakAt(t,o);)t=s.to;for(;s=i.findBreakAt(e,o);)e=s.from;e<t&&(e=t)}s.constructor.prototype.setExtremes.call(this,t,e,o,r,n)},s.setAxisTranslation=function(){if(s.constructor.prototype.setAxisTranslation.call(this),i.unitLength=void 0,i.hasBreaks){let t=s.options.breaks||[],e=[],o=[],r=sD(s.pointRangePadding,0),n=0,a,l,d=s.userMin||s.min,c=s.userMax||s.max,p,u;t.forEach(function(t){l=t.repeat||1/0,sR(d)&&sR(c)&&(h.isInBreak(t,d)&&(d+=t.to%l-d%l),h.isInBreak(t,c)&&(c-=c%l-t.from%l))}),t.forEach(function(t){if(p=t.from,l=t.repeat||1/0,sR(d)&&sR(c)){for(;p-l>d;)p-=l;for(;p<d;)p+=l;for(u=p;u<c;u+=l)e.push({value:u,move:"in"}),e.push({value:u+t.to-t.from,move:"out",size:t.breakSize})}}),e.sort(function(t,e){return t.value===e.value?("in"!==t.move)-("in"!==e.move):t.value-e.value}),a=0,p=d,e.forEach(function(t){1===(a+="in"===t.move?1:-1)&&"in"===t.move&&(p=t.value),0===a&&sR(p)&&(o.push({from:p,to:t.value,len:t.value-p-(t.size||0)}),n+=t.value-p-(t.size||0))}),i.breakArray=o,sR(d)&&sR(c)&&sR(s.min)&&(i.unitLength=c-d-n+r,sC(s,"afterBreaks"),s.staticScale?s.transA=s.staticScale:i.unitLength&&(s.transA*=(c-s.min+r)/i.unitLength),r&&(s.minPixelPadding=s.transA*(s.minPointOffset||0)),s.min=d,s.max=c)}}),sD(e,!0)&&s.chart.redraw()}}t.Additions=h}(b||(b={}));let sL=b,{dateFormats:sG}=P(),{addEvent:sz,defined:sN,erase:sW,find:sH,isArray:sF,isNumber:sU,merge:sX,pick:sY,timeUnits:sV,wrap:s_}=P();function sj(t){return P().isObject(t,!0)}function sq(t,e){let i={width:0,height:0};if(e.forEach(function(e){let s=t[e],o=0,r=0,n;sj(s)&&(o=(n=sj(s.label)?s.label:{}).getBBox?n.getBBox().height:0,n.textStr&&!sU(n.textPxLength)&&(n.textPxLength=n.getBBox().width),r=sU(n.textPxLength)?Math.round(n.textPxLength):0,n.textStr&&(r=Math.round(n.getBBox().width)),i.height=Math.max(o,i.height),i.width=Math.max(r,i.width))}),"treegrid"===this.type&&this.treeGrid&&this.treeGrid.mapOfPosToGridNode){let t=this.treeGrid.mapOfPosToGridNode[-1].height||0;i.width+=this.options.labels.indentation*(t-1)}return i}function sZ(t){let{grid:e}=this,i=3===this.side;if(i||t.apply(this),!e?.isColumn){let t=e?.columns||[];i&&(t=t.slice().reverse()),t.forEach(t=>{t.getOffset()})}i&&t.apply(this)}function s$(t){if(!0===(this.options.grid||{}).enabled){let{axisTitle:e,height:i,horiz:s,left:o,offset:r,opposite:n,options:a,top:l,width:h}=this,d=this.tickSize(),c=e?.getBBox().width,p=a.title.x,u=a.title.y,g=sY(a.title.margin,s?5:10),x=e?this.chart.renderer.fontMetrics(e).f:0,f=(s?l+i:o)+(s?1:-1)*(n?-1:1)*(d?d[0]/2:0)+(this.side===y.bottom?x:0);t.titlePosition.x=s?o-(c||0)/2-g+p:f+(n?h:0)+r+p,t.titlePosition.y=s?f-(n?i:0)+(n?x:-x)/2+r+u:l-g+u}}function sK(){let{chart:t,options:{grid:e={}},userOptions:i}=this;if(e.enabled&&function(t){let e=t.options;e.labels.align=sY(e.labels.align,"center"),t.categories||(e.showLastLabel=!1),t.labelRotation=0,e.labels.rotation=0,e.minTickInterval=1}(this),e.columns){let s=this.grid.columns=[],o=this.grid.columnIndex=0;for(;++o<e.columns.length;){let r=sX(i,e.columns[o],{isInternal:!0,linkedTo:0,scrollbar:{enabled:!1}},{grid:{columns:void 0}}),n=new(ts())(this.chart,r,"yAxis");n.grid.isColumn=!0,n.grid.columnIndex=o,sW(t.axes,n),sW(t[this.coll]||[],n),s.push(n)}}}function sJ(){let{axisTitle:t,grid:e,options:i}=this;if(!0===(i.grid||{}).enabled){let s=this.min||0,o=this.max||0,r=this.ticks[this.tickPositions[0]];if(t&&!this.chart.styledMode&&r?.slotWidth&&!this.options.title.style.width&&t.css({width:`${r.slotWidth}px`}),this.maxLabelDimensions=this.getMaxLabelDimensions(this.ticks,this.tickPositions),this.rightWall&&this.rightWall.destroy(),this.grid?.isOuterAxis()&&this.axisLine){let t=i.lineWidth;if(t){let e=this.getLinePath(t),r=e[0],n=e[1],a=(this.tickSize("tick")||[1])[0]*(this.side===y.top||this.side===y.left?-1:1);if("M"===r[0]&&"L"===n[0]&&(this.horiz?(r[2]+=a,n[2]+=a):(r[1]+=a,n[1]+=a)),!this.horiz&&this.chart.marginRight){let t=["L",this.left,r[2]||0],e=[r,t],a=["L",this.chart.chartWidth-this.chart.marginRight,this.toPixels(o+this.tickmarkOffset)],l=[["M",n[1]||0,this.toPixels(o+this.tickmarkOffset)],a];this.grid.upperBorder||s%1==0||(this.grid.upperBorder=this.grid.renderBorder(e)),this.grid.upperBorder&&(this.grid.upperBorder.attr({stroke:i.lineColor,"stroke-width":i.lineWidth}),this.grid.upperBorder.animate({d:e})),this.grid.lowerBorder||o%1==0||(this.grid.lowerBorder=this.grid.renderBorder(l)),this.grid.lowerBorder&&(this.grid.lowerBorder.attr({stroke:i.lineColor,"stroke-width":i.lineWidth}),this.grid.lowerBorder.animate({d:l}))}this.grid.axisLineExtra?(this.grid.axisLineExtra.attr({stroke:i.lineColor,"stroke-width":i.lineWidth}),this.grid.axisLineExtra.animate({d:e})):this.grid.axisLineExtra=this.grid.renderBorder(e),this.axisLine[this.showAxis?"show":"hide"]()}}if((e?.columns||[]).forEach(t=>t.render()),!this.horiz&&this.chart.hasRendered&&(this.scrollbar||this.linkedParent?.scrollbar)&&this.tickPositions.length){let t,e,i=this.tickmarkOffset,r=this.tickPositions[this.tickPositions.length-1],n=this.tickPositions[0];for(;(t=this.hiddenLabels.pop())&&t.element;)t.show();for(;(e=this.hiddenMarks.pop())&&e.element;)e.show();(t=this.ticks[n].label)&&(s-n>i?this.hiddenLabels.push(t.hide()):t.show()),(t=this.ticks[r].label)&&(r-o>i?this.hiddenLabels.push(t.hide()):t.show());let a=this.ticks[r].mark;a&&r-o<i&&r-o>0&&this.ticks[r].isLast&&this.hiddenMarks.push(a.hide())}}}function sQ(){let t=this.tickPositions?.info,e=this.options,i=e.grid||{},s=this.userOptions.labels||{};i.enabled&&(this.horiz?(this.series.forEach(t=>{t.options.pointRange=0}),t&&e.dateTimeLabelFormats&&e.labels&&!sN(s.align)&&(!1===e.dateTimeLabelFormats[t.unitName].range||t.count>1)&&(e.labels.align="left",sN(s.x)||(e.labels.x=3))):"treegrid"!==this.type&&this.grid&&this.grid.columns&&(this.minPointOffset=this.tickInterval))}function s0(t){let e,i=this.options,s=t.userOptions,o=i&&sj(i.grid)?i.grid:{};!0===o.enabled&&(e=sX(!0,{className:"highcharts-grid-axis "+(s.className||""),dateTimeLabelFormats:{hour:{list:["%[HM]","%[H]"]},day:{list:["%[AeB]","%[aeb]","%[E]"]},week:{list:["Week %W","W%W"]},month:{list:["%[B]","%[b]","%o"]}},grid:{borderWidth:1},labels:{padding:2,style:{fontSize:"0.9em"}},margin:0,title:{text:null,reserveSpace:!1,rotation:0,style:{textOverflow:"ellipsis"}},units:[["millisecond",[1,10,100]],["second",[1,10]],["minute",[1,5,15]],["hour",[1,6]],["day",[1]],["week",[1]],["month",[1]],["year",null]]},s),"xAxis"===this.coll&&(sN(s.linkedTo)&&!sN(s.tickPixelInterval)&&(e.tickPixelInterval=350),!(!sN(s.tickPixelInterval)&&sN(s.linkedTo))||sN(s.tickPositioner)||sN(s.tickInterval)||sN(s.units)||(e.tickPositioner=function(t,i){let s=this.linkedParent?.tickPositions?.info;if(s){let o=e.units||[],r,n=1,a="year";for(let t=0;t<o.length;t++){let e=o[t];if(e&&e[0]===s.unitName){r=t;break}}let l=sU(r)&&o[r+1];if(l){a=l[0]||"year";let t=l[1];n=t?.[0]||1}else"year"===s.unitName&&(n=10*s.count);let h=sV[a];return this.tickInterval=h*n,this.chart.time.getTimeTicks({unitRange:h,count:n,unitName:a},t,i,this.options.startOfWeek)}})),sX(!0,this.options,e),this.horiz&&(i.minPadding=sY(s.minPadding,0),i.maxPadding=sY(s.maxPadding,0)),sU(i.grid.borderWidth)&&(i.tickWidth=i.lineWidth=o.borderWidth))}function s1(t){let e=t.userOptions,i=e?.grid||{},s=i.columns;i.enabled&&s&&sX(!0,this.options,s[0])}function s2(){(this.grid.columns||[]).forEach(t=>t.setScale())}function s3(t){let{horiz:e,maxLabelDimensions:i,options:{grid:s={}}}=this;if(s.enabled&&i){let o=2*this.options.labels.distance,r=e?s.cellHeight||o+i.height:o+i.width;sF(t.tickSize)?t.tickSize[0]=r:t.tickSize=[r,0]}}function s5(){this.axes.forEach(t=>{(t.grid?.columns||[]).forEach(t=>{t.setAxisSize(),t.setAxisTranslation()})})}function s6(t){let{grid:e}=this;(e.columns||[]).forEach(e=>e.destroy(t.keepEvents)),e.columns=void 0}function s4(t){let e=t.userOptions||{},i=e.grid||{};i.enabled&&sN(i.borderColor)&&(e.tickColor=e.lineColor=i.borderColor),this.grid||(this.grid=new oe(this)),this.hiddenLabels=[],this.hiddenMarks=[]}function s8(t){let e=this.label,i=this.axis,s=i.reversed,o=i.chart,r=i.options.grid||{},n=i.options.labels,a=n.align,l=y[i.side],h=t.tickmarkOffset,d=i.tickPositions,c=this.pos-h,p=sU(d[t.index+1])?d[t.index+1]-h:(i.max||0)+h,u=i.tickSize("tick"),g=u?u[0]:0,x=u?u[1]/2:0;if(!0===r.enabled){let r,h,d,u;if("top"===l?h=(r=i.top+i.offset)-g:"bottom"===l?r=(h=o.chartHeight-i.bottom+i.offset)+g:(r=i.top+i.len-(i.translate(s?p:c)||0),h=i.top+i.len-(i.translate(s?c:p)||0)),"right"===l?u=(d=o.chartWidth-i.right+i.offset)+g:"left"===l?d=(u=i.left+i.offset)-g:(d=Math.round(i.left+(i.translate(s?p:c)||0))-x,u=Math.min(Math.round(i.left+(i.translate(s?c:p)||0))-x,i.left+i.len)),this.slotWidth=u-d,t.pos.x="left"===a?d:"right"===a?u:d+(u-d)/2,t.pos.y=h+(r-h)/2,e){let i=o.renderer.fontMetrics(e),s=e.getBBox().height;if(n.useHTML)t.pos.y+=i.b+-(s/2);else{let e=Math.round(s/i.h);t.pos.y+=(i.b-(i.h-i.f))/2+-((e-1)*i.h/2)}}t.pos.x+=i.horiz&&n.x||0}}function s9(t){let{axis:e,value:i}=t;if(e.options.grid?.enabled){let s,o=e.tickPositions,r=(e.linkedParent||e).series[0],n=i===o[0],a=i===o[o.length-1],l=r&&sH(r.options.data,function(t){return t[e.isXAxis?"x":"y"]===i});l&&r.is("gantt")&&(s=sX(l),P().seriesTypes.gantt.prototype.pointClass.setGanttPointAliases(s,e.chart)),t.isFirst=n,t.isLast=a,t.point=s}}function s7(){let t=this.options,e=t.grid||{},i=this.categories,s=this.tickPositions,o=s[0],r=s[1],n=s[s.length-1],a=s[s.length-2],l=this.linkedParent?.min,h=this.linkedParent?.max,d=l||this.min,c=h||this.max,p=this.tickInterval,u=sU(d)&&d>=o+p&&d<r,g=sU(d)&&o<d&&o+p>d,x=sU(c)&&n>c&&n-p<c,f=sU(c)&&c<=n-p&&c>a;!0===e.enabled&&!i&&(this.isXAxis||this.isLinked)&&((g||u)&&!t.startOnTick&&(s[0]=d),(x||f)&&!t.endOnTick&&(s[s.length-1]=c))}function ot(t){var e;let{options:{grid:i={}}}=this;return!0===i.enabled&&this.categories?this.tickInterval:t.apply(this,(e=arguments,Array.prototype.slice.call(e,1)))}!function(t){t[t.top=0]="top",t[t.right=1]="right",t[t.bottom=2]="bottom",t[t.left=3]="left"}(y||(y={}));class oe{constructor(t){this.axis=t}isOuterAxis(){let t=this.axis,e=t.chart,i=t.grid.columnIndex,s=t.linkedParent?.grid.columns||t.grid.columns||[],o=i?t.linkedParent:t,r=-1,n=0;return 3===t.side&&!e.inverted&&s.length?!t.linkedParent:((e[t.coll]||[]).forEach((e,i)=>{e.side!==t.side||e.options.isInternal||(n=i,e===o&&(r=i))}),n===r&&(!sU(i)||s.length===i))}renderBorder(t){let e=this.axis,i=e.chart.renderer,s=e.options,o=i.path(t).addClass("highcharts-axis-line").add(e.axisGroup);return i.styledMode||o.attr({stroke:s.lineColor,"stroke-width":s.lineWidth,zIndex:7}),o}}sG.E=function(t){return this.dateFormat("%a",t,!0).charAt(0)},sG.W=function(t){let e=this.toParts(t),i=(e[7]+6)%7,s=e.slice(0);s[2]=e[2]-i+3;let o=this.toParts(this.makeTime(s[0],0,1));return 4!==o[7]&&(e[1]=0,e[2]=1+(11-o[7])%7),(1+Math.floor((this.makeTime(s[0],s[1],s[2])-this.makeTime(o[0],o[1],o[2]))/6048e5)).toString()};let oi={compose:function(t,e,i){return t.keepProps.includes("grid")||(t.keepProps.push("grid"),t.prototype.getMaxLabelDimensions=sq,s_(t.prototype,"unsquish",ot),s_(t.prototype,"getOffset",sZ),sz(t,"init",s4),sz(t,"afterGetTitlePosition",s$),sz(t,"afterInit",sK),sz(t,"afterRender",sJ),sz(t,"afterSetAxisTranslation",sQ),sz(t,"afterSetOptions",s0),sz(t,"afterSetOptions",s1),sz(t,"afterSetScale",s2),sz(t,"afterTickSize",s3),sz(t,"trimTicks",s7),sz(t,"destroy",s6),sz(e,"afterSetChartSize",s5),sz(i,"afterGetLabelPosition",s8),sz(i,"labelFormat",s9)),t}},{extend:os,isNumber:oo,pick:or}=P();function on(t,e,i,s,o,r){let n=r&&r.after,a=r&&r.before,l={data:s,depth:i-1,id:t,level:i,parent:e||""},h=0,d=0,c,p;"function"==typeof a&&a(l,r);let u=(o[t]||[]).map(e=>{let s=on(e.id,t,i+1,e,o,r),n=e.start||NaN,a=!0===e.milestone?n:e.end||NaN;return c=!oo(c)||n<c?n:c,p=!oo(p)||a>p?a:p,h=h+1+s.descendants,d=Math.max(s.height+1,d),s});return s&&(s.start=or(s.start,c),s.end=or(s.end,p)),os(l,{children:u,descendants:h,height:d}),"function"==typeof n&&n(l,r),l}let oa={getNode:on,getTree:function(t,e){return on("",null,1,null,function(t){let e=[],i=t.reduce((t,i)=>{let{parent:s="",id:o}=i;return void 0===t[s]&&(t[s]=[]),t[s].push(i),o&&e.push(o),t},{});return Object.keys(i).forEach(t=>{if(""!==t&&-1===e.indexOf(t)){let e=i[t].map(function(t){let{...e}=t;return e});i[""].push(...e),delete i[t]}}),i}(t),e)}},{addEvent:ol,removeEvent:oh,isObject:od,isNumber:oc,pick:op,wrap:ou}=P();function og(){this.treeGrid||(this.treeGrid=new om(this))}function ox(t,e,i,s,o,r,n,a,l){let h,d,c,p=op(this.options?.labels,r),u=this.pos,g=this.axis,x="treegrid"===g.type,f=t.apply(this,[e,i,s,o,p,n,a,l]);if(x){let{width:t=0,padding:e=5*!g.linkedParent}=p&&od(p.symbol,!0)?p.symbol:{},i=p&&oc(p.indentation)?p.indentation:0;h=g.treeGrid.mapOfPosToGridNode,d=h?.[u],c=d?.depth||1,f.x+=t+2*e+(c-1)*i}return f}function of(t){let e,{pos:i,axis:s,label:o,treeGrid:r,options:n}=this,a=r?.labelIcon,l=o?.element,{treeGrid:h,options:d,chart:c,tickPositions:p}=s,u=h.mapOfPosToGridNode,g=op(n?.labels,d?.labels),x=g&&od(g.symbol,!0)?g.symbol:{},f=u?.[i],{descendants:m,depth:b}=f||{},y=f&&m&&m>0,v="treegrid"===s.type&&l,M=p.indexOf(i)>-1,k="highcharts-treegrid-node-",A=k+"level-",w=c.styledMode;(v&&f&&o.removeClass(RegExp(A+".*")).addClass(A+b),t.apply(this,Array.prototype.slice.call(arguments,1)),v&&y)?(e=h.isCollapsed(f),function(t,e){let i=t.treeGrid,s=!i.labelIcon,o=e.renderer,r=e.xy,n=e.options,a=n.width||0,l=n.height||0,h=n.padding??t.axis.linkedParent?0:5,d={x:r.x-a/2-h,y:r.y-l/2},c=e.collapsed?90:180,p=e.show&&oc(d.y),u=i.labelIcon;u||(i.labelIcon=u=o.path(o.symbols[n.type](n.x||0,n.y||0,a,l)).addClass("highcharts-label-icon").add(e.group)),u[p?"show":"hide"](),o.styledMode||u.attr({cursor:"pointer",fill:op(e.color,"#666666"),"stroke-width":1,stroke:n.lineColor,strokeWidth:n.lineWidth||0}),u[s?"attr":"animate"]({translateX:d.x,translateY:d.y,rotation:c})}(this,{color:!w&&o.styles.color||"",collapsed:e,group:o.parentGroup,options:x,renderer:o.renderer,show:M,xy:o.xy}),o.addClass(k+(e?"collapsed":"expanded")).removeClass(k+(e?"expanded":"collapsed")),w||o.css({cursor:"pointer"}),[o,a].forEach(t=>{t&&!t.attachedTreeGridEvents&&(ol(t.element,"mouseover",function(){o.addClass("highcharts-treegrid-node-active"),o.renderer.styledMode||o.css({textDecoration:"underline"})}),ol(t.element,"mouseout",function(){let t=od(g.style)?g.style:{};o.removeClass("highcharts-treegrid-node-active"),o.renderer.styledMode||o.css({textDecoration:t.textDecoration||"none"})}),ol(t.element,"click",function(){r.toggleCollapse()}),t.attachedTreeGridEvents=!0)})):a&&(oh(l),o?.css({cursor:"default"}),a.destroy())}class om{static compose(t){let e=t.prototype;e.toggleCollapse||(ol(t,"init",og),ou(e,"getLabelPosition",ox),ou(e,"renderLabel",of),e.collapse=function(t){this.treeGrid.collapse(t)},e.expand=function(t){this.treeGrid.expand(t)},e.toggleCollapse=function(t){this.treeGrid.toggleCollapse(t)})}constructor(t){this.tick=t}collapse(t){let e=this.tick,i=e.axis,s=i.brokenAxis;if(s&&i.treeGrid.mapOfPosToGridNode){let o=e.pos,r=i.treeGrid.mapOfPosToGridNode[o],n=i.treeGrid.collapse(r);s.setBreaks(n,op(t,!0))}}destroy(){this.labelIcon&&this.labelIcon.destroy()}expand(t){let{pos:e,axis:i}=this.tick,{treeGrid:s,brokenAxis:o}=i,r=s.mapOfPosToGridNode;if(o&&r){let i=r[e],n=s.expand(i);o.setBreaks(n,op(t,!0))}}toggleCollapse(t){let e=this.tick,i=e.axis,s=i.brokenAxis;if(s&&i.treeGrid.mapOfPosToGridNode){let o=e.pos,r=i.treeGrid.mapOfPosToGridNode[o],n=i.treeGrid.toggleCollapse(r);s.setBreaks(n,op(t,!0))}}}let{extend:ob,isArray:oy,isNumber:ov,isObject:oM,merge:ok,pick:oA,relativeLength:ow}=P(),{getLevelOptions:oP}={getColor:function(t,e){let i,s,o,r,n,a,l=e.index,h=e.mapOptionsToLevel,d=e.parentColor,c=e.parentColorIndex,p=e.series,u=e.colors,g=e.siblings,x=p.points,f=p.chart.options.chart;return t&&(i=x[t.i],s=h[t.level]||{},i&&s.colorByPoint&&(r=i.index%(u?u.length:f.colorCount),o=u&&u[r]),p.chart.styledMode||(n=oA(i&&i.options.color,s&&s.color,o,d&&(t=>{let e=s&&s.colorVariation;return e&&"brightness"===e.key&&l&&g?tO().parse(t).brighten(e.to*(l/g)).get():t})(d),p.color)),a=oA(i&&i.options.colorIndex,s&&s.colorIndex,r,c,e.colorIndex)),{color:n,colorIndex:a}},getLevelOptions:function(t){let e,i,s,o,r,n,a={};if(oM(t))for(o=ov(t.from)?t.from:1,n=t.levels,i={},e=oM(t.defaults)?t.defaults:{},oy(n)&&(i=n.reduce((t,i)=>{let s,r,n;return oM(i)&&ov(i.level)&&(r=oA((n=ok({},i)).levelIsConstant,e.levelIsConstant),delete n.levelIsConstant,delete n.level,oM(t[s=i.level+(r?0:o-1)])?ok(!0,t[s],n):t[s]=n),t},{})),r=ov(t.to)?t.to:1,s=0;s<=r;s++)a[s]=ok({},e,oM(i[s])?i[s]:{});return a},getNodeWidth:function(t,e){let{chart:i,options:s}=t,{nodeDistance:o=0,nodeWidth:r=0}=s,{plotSizeX:n=1}=i;if("auto"===r){if("string"==typeof o&&/%$/.test(o))return n/(e+parseFloat(o)/100*(e-1));let t=Number(o);return(n+t)/(e||1)-t}return ow(r,n)},setTreeValues:function t(e,i){let s=i.before,o=i.idRoot,r=i.mapIdToNode[o],n=!1!==i.levelIsConstant,a=i.points[e.i],l=a&&a.options||{},h=[],d=0;e.levelDynamic=e.level-(n?0:r.level),e.name=oA(a&&a.name,""),e.visible=o===e.id||!0===i.visible,"function"==typeof s&&(e=s(e,i)),e.children.forEach((s,o)=>{let r=ob({},i);ob(r,{index:o,siblings:e.children.length,visible:e.visible}),s=t(s,r),h.push(s),s.visible&&(d+=s.val)});let c=oA(l.value,d);return e.visible=c>=0&&(d>0||e.visible),e.children=h,e.childrenTotal=d,e.isLeaf=e.visible&&!d,e.val=c,e},updateRootId:function(t){let e,i;return oM(t)&&(i=oM(t.options)?t.options:{},e=oA(t.rootNode,i.rootId,""),oM(t.userOptions)&&(t.userOptions.rootId=e),t.rootNode=e),e}},{addEvent:oS,isArray:oO,splat:oE,find:oB,fireEvent:oT,isObject:oC,isString:oI,merge:oR,pick:oD,removeEvent:oL,wrap:oG}=P();function oz(t,e){let i=t.collapseEnd||0,s=t.collapseStart||0;return i>=e&&(s-=.5),{from:s,to:i,showPoints:!1}}function oN(t,e,i){let s=[],o=[],r={},n=e||!1,a={},l=-1,h=oa.getTree(t,{after:function(t){let e=a[t.pos],i=0,s=0;e.children.forEach(function(t){s+=(t.descendants||0)+1,i=Math.max((t.height||0)+1,i)}),e.descendants=s,e.height=i,e.collapsed&&o.push(e)},before:function(t){let e,i,o=oC(t.data,!0)?t.data:{},h=oI(o.name)?o.name:"",d=r[t.parent],c=oC(d,!0)?a[d.pos]:null;n&&oC(c,!0)&&(e=oB(c.children,function(t){return t.name===h}))?(i=e.pos,e.nodes.push(t)):i=l++,!a[i]&&(a[i]=e={depth:c?c.depth+1:0,name:h,id:o.id,nodes:[t],children:[],pos:i},-1!==i&&s.push(h),oC(c,!0)&&c.children.push(e)),oI(t.id)&&(r[t.id]=t),e&&!0===o.collapsed&&(e.collapsed=!0),t.pos=i}});return{categories:s,mapOfIdToNode:r,mapOfPosToGridNode:a=function(t,e){let i=function(t,s,o){let r=t.nodes,n=s+(-1===s?0:e-1),a=(n-s)/2,l=s+a;return r.forEach(function(t){let e=t.data;oC(e,!0)&&(e.y=s+(e.seriesIndex||0),delete e.seriesIndex),t.pos=l}),o[l]=t,t.pos=l,t.tickmarkOffset=a+.5,t.collapseStart=n+.5,t.children.forEach(function(t){i(t,n+1,o),n=(t.collapseEnd||0)-.5}),t.collapseEnd=n+.5,o};return i(t["-1"],-1,{})}(a,i),collapsedNodes:o,tree:h}}function oW(t){let e=t.target;e.axes.filter(t=>"treegrid"===t.type).forEach(function(i){let s=i.options||{},o=s.labels,r=i.uniqueNames,n=e.time.parse(s.max),a=!i.treeGrid.mapOfPosToGridNode||i.series.some(function(t){return!t.hasRendered||t.isDirtyData||t.isDirty}),l=0,h,d;if(a){let s=[];if(h=i.series.reduce(function(t,i){let o=i.options.data||[],n=o[0],a=Array.isArray(n)&&!n.find(t=>"object"==typeof t);return s.push(a),i.visible&&(o.forEach(function(s){(a||i.options.keys?.length)&&(s=i.pointClass.prototype.optionsToObject.call({series:i},s),i.pointClass.setGanttPointAliases(s,e)),oC(s,!0)&&(s.seriesIndex=l,t.push(s))}),!0===r&&l++),t},[]),n&&h.length<n)for(let t=h.length;t<=n;t++)h.push({name:t+"​"});i.categories=(d=oN(h,r||!1,!0===r?l:1)).categories,i.treeGrid.mapOfPosToGridNode=d.mapOfPosToGridNode,i.hasNames=!0,i.treeGrid.tree=d.tree,i.series.forEach(function(t,e){let i=(t.options.data||[]).map(function(i){return(s[e]||oO(i)&&t.options.keys&&t.options.keys.length)&&h.forEach(function(t){let e=oE(i);e.indexOf(t.x||0)>=0&&e.indexOf(t.x2||0)>=0&&(i=t)}),oC(i,!0)?oR(i):i});t.visible&&t.setData(i,!1)}),i.treeGrid.mapOptionsToLevel=oP({defaults:o,from:1,levels:o?.levels,to:i.treeGrid.tree?.height}),"beforeRender"===t.type&&(i.treeGrid.collapsedNodes=d.collapsedNodes)}})}function oH(t,e){let i=this.treeGrid.mapOptionsToLevel||{},s="treegrid"===this.type,o=this.ticks,r=o[e],n,a,l;s&&this.treeGrid.mapOfPosToGridNode?((n=i[(l=this.treeGrid.mapOfPosToGridNode[e]).depth])&&(a={labels:n}),!r&&u?o[e]=r=new u(this,e,void 0,void 0,{category:l.name,tickmarkOffset:l.tickmarkOffset,options:a}):(r.parameters.category=l.name,r.options=a,r.addLabel())):t.apply(this,Array.prototype.slice.call(arguments,1))}function oF(t,e,i,s){let o=this,r="treegrid"===i.type;o.treeGrid||(o.treeGrid=new oY(o)),r&&(oS(e,"beforeRender",oW),oS(e,"beforeRedraw",oW),oS(e,"addSeries",function(t){if(t.options.data){let e=oN(t.options.data,i.uniqueNames||!1,1);o.treeGrid.collapsedNodes=(o.treeGrid.collapsedNodes||[]).concat(e.collapsedNodes)}}),oS(o,"foundExtremes",function(){o.treeGrid.collapsedNodes&&o.treeGrid.collapsedNodes.forEach(function(t){let e=o.treeGrid.collapse(t);o.brokenAxis&&(o.brokenAxis.setBreaks(e,!1),o.treeGrid.collapsedNodes&&(o.treeGrid.collapsedNodes=o.treeGrid.collapsedNodes.filter(e=>t.collapseStart!==e.collapseStart||t.collapseEnd!==e.collapseEnd)))})}),oS(o,"afterBreaks",function(){"yAxis"===o.coll&&!o.staticScale&&o.chart.options.chart.height&&(o.isDirty=!0)}),i=oR({grid:{enabled:!0},labels:{align:"left",levels:[{level:void 0},{level:1,style:{fontWeight:"bold"}}],symbol:{type:"triangle",x:-5,y:-5,height:10,width:10}},uniqueNames:!1},i,{reversed:!0})),t.apply(o,[e,i,s]),r&&(o.hasNames=!0,o.options.showLastLabel=!0)}function oU(t){let e=this.options,i=this.chart.time,s="number"==typeof e.linkedTo?this.chart[this.coll]?.[e.linkedTo]:void 0;if("treegrid"===this.type){if(this.min=this.userMin??i.parse(e.min)??this.dataMin,this.max=this.userMax??i.parse(e.max)??this.dataMax,oT(this,"foundExtremes"),this.setAxisTranslation(),this.tickInterval=1,this.tickmarkOffset=.5,this.tickPositions=this.treeGrid.mapOfPosToGridNode?this.treeGrid.getTickPositions():[],s){let t=s.getExtremes();this.min=oD(t.min,t.dataMin),this.max=oD(t.max,t.dataMax),this.tickPositions=s.tickPositions}this.linkedParent=s}else t.apply(this,Array.prototype.slice.call(arguments,1))}function oX(t){let e=this;"treegrid"===this.type&&e.visible&&e.tickPositions.forEach(function(t){let i=e.ticks[t];i.label?.attachedTreeGridEvents&&(oL(i.label.element),i.label.attachedTreeGridEvents=!1)}),t.apply(e,Array.prototype.slice.call(arguments,1))}class oY{static compose(t,e,i,s){if(!t.keepProps.includes("treeGrid")){let e=t.prototype;t.keepProps.push("treeGrid"),oG(e,"generateTick",oH),oG(e,"init",oF),oG(e,"setTickInterval",oU),oG(e,"redraw",oX),e.utils={getNode:oa.getNode},u||(u=s)}return oi.compose(t,e,s),sL.compose(t,i),om.compose(s),t}constructor(t){this.axis=t}setCollapsedStatus(t){let e=this.axis,i=e.chart;e.series.forEach(function(e){let s=e.options.data;if(t.id&&s){let o=i.get(t.id),r=s[e.data.indexOf(o)];o&&r&&(o.collapsed=t.collapsed,r.collapsed=t.collapsed)}})}collapse(t){let e=this.axis,i=e.options.breaks||[],s=oz(t,e.max);return i.push(s),t.collapsed=!0,e.treeGrid.setCollapsedStatus(t),i}expand(t){let e=this.axis,i=e.options.breaks||[],s=oz(t,e.max);return t.collapsed=!1,e.treeGrid.setCollapsedStatus(t),i.reduce(function(t,e){return(e.to!==s.to||e.from!==s.from)&&t.push(e),t},[])}getTickPositions(){let t=this.axis,e=Math.floor(t.min/t.tickInterval)*t.tickInterval,i=Math.ceil(t.max/t.tickInterval)*t.tickInterval;return Object.keys(t.treeGrid.mapOfPosToGridNode||{}).reduce(function(s,o){let r=+o;return r>=e&&r<=i&&!t.brokenAxis?.isInAnyBreak(r)&&s.push(r),s},[])}isCollapsed(t){let e=this.axis,i=e.options.breaks||[],s=oz(t,e.max);return i.some(function(t){return t.from===s.from&&t.to===s.to})}toggleCollapse(t){return this.isCollapsed(t)?this.expand(t):this.collapse(t)}}let{series:oV,seriesTypes:{xrange:o_}}=tB(),{extend:oj,isNumber:oq,merge:oZ}=P();class o$ extends o_{static compose(t,e,i,s){if(o_.compose(t),e)i8.compose(t,e),i&&(iQ.compose(e,i.prototype.pointClass),s&&oY.compose(t,e,i,s))}drawPoint(t,e){let i=this.options,s=this.chart.renderer,o=t.shapeArgs,r=t.plotY,n=t.selected&&"select",a=i.stacking&&!i.borderRadius,l=t.graphic,h;t.options.milestone?oq(r)&&null!==t.y&&!1!==t.visible?(h=s.symbols.diamond(o.x||0,o.y||0,o.width||0,o.height||0),l?l[e]({d:h}):t.graphic=l=s.path(h).addClass(t.getClassName(),!0).add(t.group||this.group),this.chart.styledMode||t.graphic.attr(this.pointAttribs(t,n)).shadow(i.shadow,null,a)):l&&(t.graphic=l.destroy()):super.drawPoint(t,e)}translatePoint(t){let e,i;super.translatePoint(t),t.options.milestone&&(i=(e=t.shapeArgs).height||0,t.shapeArgs={x:(e.x||0)-i/2,y:e.y,width:i,height:i})}}o$.defaultOptions=oZ(o_.defaultOptions,{grouping:!1,dataLabels:{enabled:!0},tooltip:{headerFormat:'<span style="font-size: 0.8em">{series.name}</span><br/>',pointFormat:null,pointFormatter:function(){let t=this.series,e=t.xAxis,i=t.tooltipOptions.dateTimeLabelFormats,s=e.options.startOfWeek,o=t.tooltipOptions,r=this.options.milestone,n=o.xDateFormat,a="<b>"+(this.name||this.yCategory)+"</b>";if(o.pointFormat)return this.tooltipFormatter(o.pointFormat);!n&&sS(this.start)&&(n=t.chart.time.getDateFormat(e.closestPointRange,this.start,s,i||{}));let l=t.chart.time.dateFormat(n,this.start),h=t.chart.time.dateFormat(n,this.end);return a+="<br/>",r?a+=l+"<br/>":a+="Start: "+l+"<br/>"+("End: "+h)+"<br/>",a}},connectors:{type:"simpleConnect",animation:{reversed:!0},radius:0,startMarker:{enabled:!0,symbol:"arrow-filled",radius:4,fill:"#fa0",align:"left"},endMarker:{enabled:!1,align:"right"}}}),oj(o$.prototype,{pointArrayMap:["start","end","y"],pointClass:sP,setData:oV.prototype.setData}),tB().registerSeriesType("gantt",o$);/**
 * @license Highcharts Gantt JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/gantt
 * @requires highcharts
 *
 * Gantt series
 *
 * (c) 2016-2025 Lars A. V. Cabrera
 *
 * License: www.highcharts.com/license
 */let oK=P();oK.Connection=oK.Connection||N,oK.GanttChart=oK.GanttChart||te,oK.Navigator=oK.Navigator||eB,oK.RangeSelector=oK.RangeSelector||iS,oK.Scrollbar=oK.Scrollbar||ea,oK.ganttChart=oK.GanttChart.ganttChart,T.compose(oK.SVGRenderer),({compose:function(t,e){U(W,"CurrentDateIndication")&&(H(t,"afterSetOptions",V),H(e,"render",_),X(e.prototype,"getLabelText",j))}}).compose(oK.Axis,oK.PlotLineOrBand),o$.compose(oK.Axis,oK.Chart,oK.Series,oK.Tick),oK.Navigator.compose(oK.Chart,oK.Axis,oK.Series),oK.RangeSelector.compose(oK.Axis,oK.Chart),oK.Scrollbar.compose(oK.Axis);let oJ=P();return A.default})());