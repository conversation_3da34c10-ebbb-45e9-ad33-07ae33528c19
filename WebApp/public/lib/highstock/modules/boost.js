!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/boost
 * @requires highcharts
 *
 * Boost module
 *
 * (c) 2010-2025 Highsoft AS
 * Author: Torstein Honsi
 *
 * License: www.highcharts.com/license
 *
 * */function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(e._Highcharts,e._Highcharts.Color):"function"==typeof define&&define.amd?define("highcharts/modules/boost",["highcharts/highcharts"],function(e){return t(e,e.Color)}):"object"==typeof exports?exports["highcharts/modules/boost"]=t(e._Highcharts,e._Highcharts.Color):e.Highcharts=t(e.Highcharts,e.Highcharts.Color)}("undefined"==typeof window?this:window,(e,t)=>(()=>{"use strict";let i,s;var r,o={620:e=>{e.exports=t},944:t=>{t.exports=e}},n={};function a(e){var t=n[e];if(void 0!==t)return t.exports;var i=n[e]={exports:{}};return o[e](i,i.exports,a),i.exports}a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var i in t)a.o(t,i)&&!a.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var l={};a.d(l,{default:()=>eI});var h=a(944),d=a.n(h);let f=["area","areaspline","arearange","column","columnrange","bar","line","scatter","heatmap","bubble","treemap"],u={};f.forEach(e=>{u[e]=!0});let{composed:g}=d(),{addEvent:m,pick:c,pushUnique:p}=d();function b(e){let t=e.series,i=e.boost=e.boost||{},s=e.options.boost||{},r=c(s.seriesThreshold,50);if(t.length>=r)return!0;if(1===t.length)return!1;let o=s.allowForce;if(void 0===o){for(let t of(o=!0,e.xAxis))if(c(t.min,-1/0)>c(t.dataMin,-1/0)||c(t.max,1/0)<c(t.dataMax,1/0)){o=!1;break}}if(void 0!==i.forceChartBoost){if(o)return i.forceChartBoost;i.forceChartBoost=void 0}let n=0,a=0,l;for(let e of t)0!==(l=e.options).boostThreshold&&!1!==e.visible&&"heatmap"!==e.type&&(u[e.type]&&++n,function(...e){let t=-Number.MAX_VALUE;return e.forEach(e=>{if(null!=e&&void 0!==e.length&&e.length>0)return t=e.length,!0}),t}(e.getColumn("x",!0),l.data,e.points)>=(l.boostThreshold||Number.MAX_VALUE)&&++a);return i.forceChartBoost=o&&(n===t.length&&a===n||a>5),i.forceChartBoost}function x(e){function t(){e.boost&&e.boost.wgl&&b(e)&&e.boost.wgl.render(e)}m(e,"predraw",function(){e.boost=e.boost||{},e.boost.forceChartBoost=void 0,e.boosted=!1,e.axes.some(e=>e.isPanning)||e.boost.clear?.(),e.boost.canvas&&e.boost.wgl&&b(e)&&e.boost.wgl.allocateBuffer(e),e.boost.markerGroup&&e.xAxis&&e.xAxis.length>0&&e.yAxis&&e.yAxis.length>0&&e.boost.markerGroup.translate(e.xAxis[0].pos,e.yAxis[0].pos)}),m(e,"load",t,{order:-1}),m(e,"redraw",t);let i=-1,s=-1;m(e.pointer,"afterGetHoverData",t=>{let r=t.hoverPoint?.series;if(e.boost=e.boost||{},e.boost.markerGroup&&r){let t=e.inverted?r.yAxis:r.xAxis,o=e.inverted?r.xAxis:r.yAxis;(t&&t.pos!==i||o&&o.pos!==s)&&(e.series.forEach(e=>{e.halo?.hide()}),e.boost.markerGroup.translate(t.pos,o.pos),i=t.pos,s=o.pos)}})}let A={compose:function(e,t){return t&&p(g,"Boost.Chart")&&e.prototype.callbacks.push(x),e},getBoostClipRect:function(e,t){let i=e.navigator,s={x:e.plotLeft,y:e.plotTop,width:e.plotWidth,height:e.plotHeight};if(i&&e.inverted?(s.width+=i.top+i.height,i.opposite||(s.x=i.left)):i&&!e.inverted&&(s.height=i.top+i.height-e.plotTop),t.is){let{xAxis:i,yAxis:r}=t;if(s=e.getClipBox(t),e.inverted){let e=s.width;s.width=s.height,s.height=e,s.x=r.pos,s.y=i.pos}else s.x=i.pos,s.y=r.pos}if(t===e){let t=e.inverted?e.xAxis:e.yAxis;t.length<=1&&(s.y=Math.min(t[0].pos,s.y),s.height=t[0].pos-e.plotTop+t[0].len)}return s},isChartSeriesBoosting:b};var y=a(620),v=a.n(y);let P={area:"LINES",arearange:"LINES",areaspline:"LINES",column:"LINES",columnrange:"LINES",bar:"LINES",line:"LINE_STRIP",scatter:"POINTS",heatmap:"TRIANGLES",treemap:"TRIANGLES",bubble:"POINTS"},{clamp:T,error:C,pick:k}=d(),E=class{constructor(e){if(this.errors=[],this.uLocations={},this.gl=e,e&&!this.createShader())return}bind(){this.gl&&this.shaderProgram&&this.gl.useProgram(this.shaderProgram)}createShader(){let e=this.stringToProgram("#version 100\n#define LN10 2.302585092994046\nprecision highp float;\nattribute vec4 aVertexPosition;\nattribute vec4 aColor;\nvarying highp vec2 position;\nvarying highp vec4 vColor;\nuniform mat4 uPMatrix;\nuniform float pSize;\nuniform float translatedThreshold;\nuniform bool hasThreshold;\nuniform bool skipTranslation;\nuniform float xAxisTrans;\nuniform float xAxisMin;\nuniform float xAxisMinPad;\nuniform float xAxisPointRange;\nuniform float xAxisLen;\nuniform bool  xAxisPostTranslate;\nuniform float xAxisOrdinalSlope;\nuniform float xAxisOrdinalOffset;\nuniform float xAxisPos;\nuniform bool  xAxisCVSCoord;\nuniform bool  xAxisIsLog;\nuniform bool  xAxisReversed;\nuniform float yAxisTrans;\nuniform float yAxisMin;\nuniform float yAxisMinPad;\nuniform float yAxisPointRange;\nuniform float yAxisLen;\nuniform bool  yAxisPostTranslate;\nuniform float yAxisOrdinalSlope;\nuniform float yAxisOrdinalOffset;\nuniform float yAxisPos;\nuniform bool  yAxisCVSCoord;\nuniform bool  yAxisIsLog;\nuniform bool  yAxisReversed;\nuniform bool  isBubble;\nuniform bool  bubbleSizeByArea;\nuniform float bubbleZMin;\nuniform float bubbleZMax;\nuniform float bubbleZThreshold;\nuniform float bubbleMinSize;\nuniform float bubbleMaxSize;\nuniform bool  bubbleSizeAbs;\nuniform bool  isInverted;\nfloat bubbleRadius(){\nfloat value = aVertexPosition.w;\nfloat zMax = bubbleZMax;\nfloat zMin = bubbleZMin;\nfloat radius = 0.0;\nfloat pos = 0.0;\nfloat zRange = zMax - zMin;\nif (bubbleSizeAbs){\nvalue = value - bubbleZThreshold;\nzMax = max(zMax - bubbleZThreshold, zMin - bubbleZThreshold);\nzMin = 0.0;\n}\nif (value < zMin){\nradius = bubbleZMin / 2.0 - 1.0;\n} else {\npos = zRange > 0.0 ? (value - zMin) / zRange : 0.5;\nif (bubbleSizeByArea && pos > 0.0){\npos = sqrt(pos);\n}\nradius = ceil(bubbleMinSize + pos * (bubbleMaxSize - bubbleMinSize)) / 2.0;\n}\nreturn radius * 2.0;\n}\nfloat translate(float val,\nfloat pointPlacement,\nfloat localA,\nfloat localMin,\nfloat minPixelPadding,\nfloat pointRange,\nfloat len,\nbool  cvsCoord,\nbool  isLog,\nbool  reversed\n){\nfloat sign = 1.0;\nfloat cvsOffset = 0.0;\nif (cvsCoord) {\nsign *= -1.0;\ncvsOffset = len;\n}\nif (isLog) {\nval = log(val) / LN10;\n}\nif (reversed) {\nsign *= -1.0;\ncvsOffset -= sign * len;\n}\nreturn sign * (val - localMin) * localA + cvsOffset + \n(sign * minPixelPadding);\n}\nfloat xToPixels(float value) {\nif (skipTranslation){\nreturn value;// + xAxisPos;\n}\nreturn translate(value, 0.0, xAxisTrans, xAxisMin, xAxisMinPad, xAxisPointRange, xAxisLen, xAxisCVSCoord, xAxisIsLog, xAxisReversed);// + xAxisPos;\n}\nfloat yToPixels(float value, float checkTreshold) {\nfloat v;\nif (skipTranslation){\nv = value;// + yAxisPos;\n} else {\nv = translate(value, 0.0, yAxisTrans, yAxisMin, yAxisMinPad, yAxisPointRange, yAxisLen, yAxisCVSCoord, yAxisIsLog, yAxisReversed);// + yAxisPos;\nif (v > yAxisLen) {\nv = yAxisLen;\n}\n}\nif (checkTreshold > 0.0 && hasThreshold) {\nv = min(v, translatedThreshold);\n}\nreturn v;\n}\nvoid main(void) {\nif (isBubble){\ngl_PointSize = bubbleRadius();\n} else {\ngl_PointSize = pSize;\n}\nvColor = aColor;\nif (skipTranslation && isInverted) {\ngl_Position = uPMatrix * vec4(aVertexPosition.y + yAxisPos, aVertexPosition.x + xAxisPos, 0.0, 1.0);\n} else if (isInverted) {\ngl_Position = uPMatrix * vec4(yToPixels(aVertexPosition.y, aVertexPosition.z) + yAxisPos, xToPixels(aVertexPosition.x) + xAxisPos, 0.0, 1.0);\n} else {\ngl_Position = uPMatrix * vec4(xToPixels(aVertexPosition.x) + xAxisPos, yToPixels(aVertexPosition.y, aVertexPosition.z) + yAxisPos, 0.0, 1.0);\n}\n}","vertex"),t=this.stringToProgram("precision highp float;\nuniform vec4 fillColor;\nvarying highp vec2 position;\nvarying highp vec4 vColor;\nuniform sampler2D uSampler;\nuniform bool isCircle;\nuniform bool hasColor;\nvoid main(void) {\nvec4 col = fillColor;\nvec4 tcol = texture2D(uSampler, gl_PointCoord.st);\nif (hasColor) {\ncol = vColor;\n}\nif (isCircle) {\ncol *= tcol;\nif (tcol.r < 0.0) {\ndiscard;\n} else {\ngl_FragColor = col;\n}\n} else {\ngl_FragColor = col;\n}\n}","fragment"),i=e=>this.gl.getUniformLocation(this.shaderProgram,e);return e&&t?(this.shaderProgram=this.gl.createProgram(),this.gl.attachShader(this.shaderProgram,e),this.gl.attachShader(this.shaderProgram,t),this.gl.linkProgram(this.shaderProgram),this.gl.getProgramParameter(this.shaderProgram,this.gl.LINK_STATUS))?(this.gl.useProgram(this.shaderProgram),this.gl.bindAttribLocation(this.shaderProgram,0,"aVertexPosition"),this.pUniform=i("uPMatrix"),this.psUniform=i("pSize"),this.fcUniform=i("fillColor"),this.isBubbleUniform=i("isBubble"),this.bubbleSizeAbsUniform=i("bubbleSizeAbs"),this.bubbleSizeAreaUniform=i("bubbleSizeByArea"),this.uSamplerUniform=i("uSampler"),this.skipTranslationUniform=i("skipTranslation"),this.isCircleUniform=i("isCircle"),this.isInverted=i("isInverted"),!0):(this.errors.push(this.gl.getProgramInfoLog(this.shaderProgram)),this.handleErrors(),this.shaderProgram=!1,!1):(this.shaderProgram=!1,this.handleErrors(),!1)}handleErrors(){this.errors.length&&C("[highcharts boost] shader error - "+this.errors.join("\n"))}stringToProgram(e,t){let i=this.gl.createShader("vertex"===t?this.gl.VERTEX_SHADER:this.gl.FRAGMENT_SHADER);return(this.gl.shaderSource(i,e),this.gl.compileShader(i),this.gl.getShaderParameter(i,this.gl.COMPILE_STATUS))?i:(this.errors.push("when compiling "+t+" shader:\n"+this.gl.getShaderInfoLog(i)),!1)}destroy(){this.gl&&this.shaderProgram&&(this.gl.deleteProgram(this.shaderProgram),this.shaderProgram=!1)}fillColorUniform(){return this.fcUniform}getProgram(){return this.shaderProgram}pointSizeUniform(){return this.psUniform}perspectiveUniform(){return this.pUniform}reset(){this.gl&&this.shaderProgram&&(this.gl.uniform1i(this.isBubbleUniform,0),this.gl.uniform1i(this.isCircleUniform,0))}setBubbleUniforms(e,t,i,s=1){let r=e.options,o=Number.MAX_VALUE,n=-Number.MAX_VALUE;if(this.gl&&this.shaderProgram&&e.is("bubble")){let a=e.getPxExtremes();o=k(r.zMin,T(t,!1===r.displayNegative?r.zThreshold:-Number.MAX_VALUE,o)),n=k(r.zMax,Math.max(n,i)),this.gl.uniform1i(this.isBubbleUniform,1),this.gl.uniform1i(this.isCircleUniform,1),this.gl.uniform1i(this.bubbleSizeAreaUniform,"width"!==e.options.sizeBy),this.gl.uniform1i(this.bubbleSizeAbsUniform,e.options.sizeByAbsoluteValue),this.setUniform("bubbleMinSize",a.minPxSize*s),this.setUniform("bubbleMaxSize",a.maxPxSize*s),this.setUniform("bubbleZMin",o),this.setUniform("bubbleZMax",n),this.setUniform("bubbleZThreshold",e.options.zThreshold)}}setColor(e){this.gl&&this.shaderProgram&&this.gl.uniform4f(this.fcUniform,e[0]/255,e[1]/255,e[2]/255,e[3])}setDrawAsCircle(e){this.gl&&this.shaderProgram&&this.gl.uniform1i(this.isCircleUniform,+!!e)}setInverted(e){this.gl&&this.shaderProgram&&this.gl.uniform1i(this.isInverted,e)}setPMatrix(e){this.gl&&this.shaderProgram&&this.gl.uniformMatrix4fv(this.pUniform,!1,e)}setPointSize(e){this.gl&&this.shaderProgram&&this.gl.uniform1f(this.psUniform,e)}setSkipTranslation(e){this.gl&&this.shaderProgram&&this.gl.uniform1i(this.skipTranslationUniform,+(!0===e))}setTexture(e){this.gl&&this.shaderProgram&&this.gl.uniform1i(this.uSamplerUniform,e)}setUniform(e,t){if(this.gl&&this.shaderProgram){let i=this.uLocations[e]=this.uLocations[e]||this.gl.getUniformLocation(this.shaderProgram,e);this.gl.uniform1f(i,t)}}},M=class{constructor(e,t,i){this.buffer=!1,this.iterator=0,this.preAllocated=!1,this.vertAttribute=!1,this.components=i||2,this.dataComponents=i,this.gl=e,this.shader=t}allocate(e){this.iterator=-1,this.preAllocated=new Float32Array(4*e)}bind(){if(!this.buffer)return!1;this.gl.vertexAttribPointer(this.vertAttribute,this.components,this.gl.FLOAT,!1,0,0)}build(e,t,i){let s;return(this.data=e||[],this.data&&0!==this.data.length||this.preAllocated)?(this.components=i||this.components,this.buffer&&this.gl.deleteBuffer(this.buffer),this.preAllocated||(s=new Float32Array(this.data)),this.buffer=this.gl.createBuffer(),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,this.buffer),this.gl.bufferData(this.gl.ARRAY_BUFFER,this.preAllocated||s,this.gl.STATIC_DRAW),this.vertAttribute=this.gl.getAttribLocation(this.shader.getProgram(),t),this.gl.enableVertexAttribArray(this.vertAttribute),s=!1,!0):(this.destroy(),!1)}destroy(){this.buffer&&(this.gl.deleteBuffer(this.buffer),this.buffer=!1,this.vertAttribute=!1),this.iterator=0,this.components=this.dataComponents||2,this.data=[]}push(e,t,i,s){this.preAllocated&&(this.preAllocated[++this.iterator]=e,this.preAllocated[++this.iterator]=t,this.preAllocated[++this.iterator]=i,this.preAllocated[++this.iterator]=s)}render(e,t,i){let s=this.preAllocated?this.preAllocated.length:this.data.length;return!!this.buffer&&!!s&&((!e||e>s||e<0)&&(e=0),(!t||t>s)&&(t=s),!(e>=t)&&(i=i||"POINTS",this.gl.drawArrays(this.gl[i],e/this.components,(t-e)/this.components),!0))}},{parse:S}=v(),{doc:w,win:U}=d(),{isNumber:R,isObject:L,merge:_,objectEach:z,pick:D}=d(),N={column:!0,columnrange:!0,bar:!0,area:!0,areaspline:!0,arearange:!0},I={scatter:!0,bubble:!0},G=["webgl","experimental-webgl","moz-webgl","webkit-3d"];class B{static orthoMatrix(e,t){return[2/e,0,0,0,0,-(2/t),0,0,0,0,-2,0,-1,1,-1,1]}static seriesPointCount(e){let t,i,s;return e.boosted?(t=!!e.options.stacking,i=(e.getColumn("x").length?e.getColumn("x"):void 0)||e.options.xData||e.getColumn("x",!0),s=(t?e.data:i||e.options.data).length,"treemap"===e.type?s*=12:"heatmap"===e.type?s*=6:N[e.type]&&(s*=2),s):0}constructor(e){this.data=[],this.height=0,this.isInited=!1,this.markerData=[],this.series=[],this.textureHandles={},this.width=0,this.postRenderCallback=e,this.settings={pointSize:1,lineWidth:1,fillColor:"#AA00AA",useAlpha:!0,usePreallocated:!1,useGPUTranslations:!1,debug:{timeRendering:!1,timeSeriesProcessing:!1,timeSetup:!1,timeBufferCopy:!1,timeKDTree:!1,showSkipSummary:!1}}}getPixelRatio(){return this.settings.pixelRatio||U.devicePixelRatio||1}setOptions(e){"pixelRatio"in e||(e.pixelRatio=1),_(!0,this.settings,e)}allocateBuffer(e){let t=this.vbuffer,i=0;this.settings.usePreallocated&&(e.series.forEach(e=>{e.boosted&&(i+=B.seriesPointCount(e))}),t&&t.allocate(i))}allocateBufferForSingleSeries(e){let t=this.vbuffer,i=0;this.settings.usePreallocated&&(e.boosted&&(i=B.seriesPointCount(e)),t&&t.allocate(i))}clear(){let e=this.gl;e&&e.clear(e.COLOR_BUFFER_BIT|e.DEPTH_BUFFER_BIT)}pushSeriesData(e,t){let i=this.data,s=this.settings,r=this.vbuffer,o=e.pointArrayMap&&"low,high"===e.pointArrayMap.join(","),{chart:n,options:a,sorted:l,xAxis:h,yAxis:d}=e,f=!!a.stacking,u=a.data,g=e.xAxis.getExtremes(),m=g.min-(e.xAxis.minPointOffset||0),c=g.max+(e.xAxis.minPointOffset||0),p=e.yAxis.getExtremes(),b=p.min-(e.yAxis.minPointOffset||0),x=p.max+(e.yAxis.minPointOffset||0),A=(e.getColumn("x").length?e.getColumn("x"):void 0)||a.xData||e.getColumn("x",!0),y=(e.getColumn("y").length?e.getColumn("y"):void 0)||a.yData||e.getColumn("y",!0),v=(e.getColumn("z").length?e.getColumn("z"):void 0)||a.zData||e.getColumn("z",!0),P=!A||0===A.length,T=a.connectNulls,C=e.points||!1,k=f?e.data:A||u,E={x:Number.MAX_VALUE,y:0},M={x:-Number.MAX_VALUE,y:0},w=void 0===n.index,U=N[e.type],R=a.zoneAxis||"y",_=a.zones||!1,z=a.threshold,D=this.getPixelRatio(),I=e.chart.plotWidth,G=!1,B=!1,O,V,X=0,F=!1,H,W,j,q,Y=-1,Z=!1,K=!1,Q,J=!1,$=!1,ee=!1,et=!1,ei=!0,es=!0,er,eo=!1,en=!1,ea=0;if(a.boostData&&a.boostData.length>0)return;a.gapSize&&(en="value"!==a.gapUnit?a.gapSize*e.closestPointRange:a.gapSize),_&&(er=[],_.forEach((e,t)=>{if(e.color){let i=S(e.color).rgba;i[0]/=255,i[1]/=255,i[2]/=255,er[t]=i,eo||void 0!==e.value||(eo=i)}}),eo||(eo=S(e.pointAttribs&&e.pointAttribs().fill||e.color).rgba,eo[0]/=255,eo[1]/=255,eo[2]/=255)),n.inverted&&(I=e.chart.plotHeight),e.closestPointRangePx=Number.MAX_VALUE;let el=e=>{e&&(t.colorData.push(e[0]),t.colorData.push(e[1]),t.colorData.push(e[2]),t.colorData.push(e[3]))},eh=(e,o,n,a=1,l)=>{el(l),1!==D&&(!s.useGPUTranslations||t.skipTranslation)&&(e*=D,o*=D,a*=D),s.usePreallocated&&r?(r.push(e,o,+!!n,a),ea+=4):(i.push(e),i.push(o),i.push(n?D:0),i.push(a))},ed=()=>{t.segments.length&&(t.segments[t.segments.length-1].to=i.length||ea)},ef=()=>{t.segments.length&&t.segments[t.segments.length-1].from===(i.length||ea)||(ed(),t.segments.push({from:i.length||ea}))},eu=(e,t,i,s,r)=>{el(r),eh(e+i,t),el(r),eh(e,t),el(r),eh(e,t+s),el(r),eh(e,t+s),el(r),eh(e+i,t+s),el(r),eh(e+i,t)};if(ef(),C&&C.length>0){t.skipTranslation=!0,t.drawMode="TRIANGLES",C[0].node&&C[0].node.levelDynamic&&C.sort((e,t)=>{if(e.node){if(e.node.levelDynamic>t.node.levelDynamic)return 1;if(e.node.levelDynamic<t.node.levelDynamic)return -1}return 0}),C.forEach(t=>{let i,s,r=t.plotY;if(void 0!==r&&!isNaN(r)&&null!==t.y&&t.shapeArgs){let{x:r=0,y:o=0,width:a=0,height:l=0}=t.shapeArgs;i=(s=n.styledMode?t.series.colorAttribs(t):s=t.series.pointAttribs(t))["stroke-width"]||0,ee=S(s.fill).rgba,ee[0]/=255,ee[1]/=255,ee[2]/=255,e.is("treemap")&&(i=i||1,V=S(s.stroke).rgba,V[0]/=255,V[1]/=255,V[2]/=255,eu(r,o,a,l,V),i/=2),e.is("heatmap")&&n.inverted&&(r=h.len-r,o=d.len-o,a=-a,l=-l),eu(r+i,o+i,a-2*i,l-2*i,ee)}}),ed();return}for(;Y<k.length-1;){if(void 0===(j=k[++Y]))continue;if(w)break;let i=u&&u[Y];if(!P&&L(i,!0)&&i.color&&(ee=S(i.color).rgba,ee[0]/=255,ee[1]/=255,ee[2]/=255),P?(H=j[0],W=j[1],k[Y+1]&&(K=k[Y+1][0]),k[Y-1]&&(Z=k[Y-1][0]),j.length>=3&&(q=j[2],j[2]>t.zMax&&(t.zMax=j[2]),j[2]<t.zMin&&(t.zMin=j[2]))):(H=j,W=y?.[Y],k[Y+1]&&(K=k[Y+1]),k[Y-1]&&(Z=k[Y-1]),v&&v.length&&(q=v[Y],v[Y]>t.zMax&&(t.zMax=v[Y]),v[Y]<t.zMin&&(t.zMin=v[Y]))),!T&&(null===H||null===W)){ef();continue}if(K&&K>=m&&K<=c&&(J=!0),Z&&Z>=m&&Z<=c&&($=!0),o?(P&&(W=j.slice(1,3)),Q=e.getColumn("low",!0)?.[Y],W=e.getColumn("high",!0)?.[Y]||0):f&&(H=j.x,Q=(W=j.stackY)-j.y),null!=b&&null!=x&&(ei=W>=b&&W<=x),!l&&!ei||(H>c&&M.x<c&&(M.x=H,M.y=W),H<m&&E.x>m&&(E.x=H,E.y=W),null===W&&T))continue;if(null===W||!ei&&k.length>1&&!J&&!$){ef();continue}if((l&&(K>=m||H>=m)&&(Z<=c||H<=c)||!l&&H>=m&&H<=c)&&(et=!0),et||J||$){if(en&&H-Z>en&&ef(),_){let e;_.some((t,i)=>{let s=_[i-1];return"x"===R?void 0!==t.value&&H<=t.value&&(er[i]&&(!s||H>=s.value)&&(e=er[i]),!0):void 0!==t.value&&W<=t.value&&(er[i]&&(!s||W>=s.value)&&(e=er[i]),!0)}),ee=e||eo||ee}if(s.useGPUTranslations||(t.skipTranslation=!0,H=h.toPixels(H,!0),W=d.toPixels(W,!0),!(H>I)||"POINTS"!==t.drawMode)){if(t.hasMarkers&&et&&!1!==G&&(e.closestPointRangePx=Math.min(e.closestPointRangePx,Math.abs(H-G))),!s.useGPUTranslations&&!s.usePreallocated&&G&&1>Math.abs(H-G)&&B&&1>Math.abs(W-B)){s.debug.showSkipSummary&&++X;continue}U&&(O=Q||0,(!1===Q||void 0===Q)&&(O=W<0?W:0),(o||f)&&!d.logarithmic||(O=Math.max(null===z?b:z,b)),s.useGPUTranslations||(O=d.toPixels(O,!0)),eh(H,O,0,0,ee)),a.step&&!es&&eh(H,B,0,2,ee),eh(H,W,0,"bubble"===e.type?q||1:2,ee),G=H,B=W,F=!0,es=!1}}}s.debug.showSkipSummary&&console.log("skipped points:",X);let eg=(e,i)=>{if(s.useGPUTranslations||(t.skipTranslation=!0,e.x=h.toPixels(e.x,!0),e.y=d.toPixels(e.y,!0)),i){this.data=[e.x,e.y,0,2].concat(this.data);return}eh(e.x,e.y,0,2)};!F&&!1!==T&&"line_strip"===e.drawMode&&(E.x<Number.MAX_VALUE&&eg(E,!0),M.x>-Number.MAX_VALUE&&eg(M)),ed()}pushSeries(e){let t=this.markerData,i=this.series,s=this.settings;i.length>0&&i[i.length-1].hasMarkers&&(i[i.length-1].markerTo=t.length),s.debug.timeSeriesProcessing&&console.time("building "+e.type+" series");let r={segments:[],markerFrom:t.length,colorData:[],series:e,zMin:Number.MAX_VALUE,zMax:-Number.MAX_VALUE,hasMarkers:!!e.options.marker&&!1!==e.options.marker.enabled,showMarkers:!0,drawMode:P[e.type]||"LINE_STRIP"};e.index>=i.length?i.push(r):i[e.index]=r,this.pushSeriesData(e,r),s.debug.timeSeriesProcessing&&console.timeEnd("building "+e.type+" series")}flush(){let e=this.vbuffer;this.data=[],this.markerData=[],this.series=[],e&&e.destroy()}setXAxis(e){let t=this.shader;if(!t)return;let i=this.getPixelRatio();t.setUniform("xAxisTrans",e.transA*i),t.setUniform("xAxisMin",e.min),t.setUniform("xAxisMinPad",e.minPixelPadding*i),t.setUniform("xAxisPointRange",e.pointRange),t.setUniform("xAxisLen",e.len*i),t.setUniform("xAxisPos",e.pos*i),t.setUniform("xAxisCVSCoord",!e.horiz),t.setUniform("xAxisIsLog",!!e.logarithmic),t.setUniform("xAxisReversed",!!e.reversed)}setYAxis(e){let t=this.shader;if(!t)return;let i=this.getPixelRatio();t.setUniform("yAxisTrans",e.transA*i),t.setUniform("yAxisMin",e.min),t.setUniform("yAxisMinPad",e.minPixelPadding*i),t.setUniform("yAxisPointRange",e.pointRange),t.setUniform("yAxisLen",e.len*i),t.setUniform("yAxisPos",e.pos*i),t.setUniform("yAxisCVSCoord",!e.horiz),t.setUniform("yAxisIsLog",!!e.logarithmic),t.setUniform("yAxisReversed",!!e.reversed)}setThreshold(e,t){let i=this.shader;i&&(i.setUniform("hasThreshold",e),i.setUniform("translatedThreshold",t))}renderChart(e){let t=this.gl,i=this.settings,s=this.shader,r=this.vbuffer,o=this.getPixelRatio();if(!e)return!1;this.width=e.chartWidth*o,this.height=e.chartHeight*o;let n=this.height,a=this.width;if(!t||!s||!a||!n)return!1;i.debug.timeRendering&&console.time("gl rendering"),t.canvas.width=a,t.canvas.height=n,s.bind(),t.viewport(0,0,a,n),s.setPMatrix(B.orthoMatrix(a,n)),i.lineWidth>1&&!d().isMS&&t.lineWidth(i.lineWidth),r&&(r.build(this.data,"aVertexPosition",4),r.bind()),s.setInverted(e.inverted),this.series.forEach((n,a)=>{let l=n.series.options,h=l.marker,d=void 0!==l.lineWidth?l.lineWidth:1,f=l.threshold,u=R(f),g=n.series.yAxis.getThreshold(f),m=D(l.marker?l.marker.enabled:null,!!n.series.xAxis.isRadial||null,n.series.closestPointRangePx>2*((l.marker?l.marker.radius:10)||10)),c=this.textureHandles[h&&h.symbol||n.series.symbol]||this.textureHandles.circle,p,b,x,A=[];if(0!==n.segments.length&&n.segments[0].from!==n.segments[0].to&&(c.isReady&&(t.bindTexture(t.TEXTURE_2D,c.handle),s.setTexture(c.handle)),e.styledMode?n.series.markerGroup===n.series.chart.boost?.markerGroup?(delete n.series.markerGroup,n.series.markerGroup=n.series.plotGroup("markerGroup","markers","visible",1,e.seriesGroup).addClass("highcharts-tracker"),x=n.series.markerGroup.getStyle("fill"),n.series.markerGroup.destroy(),n.series.markerGroup=n.series.chart.boost?.markerGroup):x=n.series.markerGroup?.getStyle("fill"):(x="POINTS"===n.drawMode&&n.series.pointAttribs&&n.series.pointAttribs().fill||n.series.color,l.colorByPoint&&(x=n.series.chart.options.colors[a])),n.series.fillOpacity&&l.fillOpacity&&(x=new(v())(x).setOpacity(D(l.fillOpacity,1)).get()),A=S(x).rgba,i.useAlpha||(A[3]=1),"add"===l.boostBlending?(t.blendFunc(t.SRC_ALPHA,t.ONE),t.blendEquation(t.FUNC_ADD)):"mult"===l.boostBlending||"multiply"===l.boostBlending?t.blendFunc(t.DST_COLOR,t.ZERO):"darken"===l.boostBlending?(t.blendFunc(t.ONE,t.ONE),t.blendEquation(t.FUNC_MIN)):t.blendFuncSeparate(t.SRC_ALPHA,t.ONE_MINUS_SRC_ALPHA,t.ONE,t.ONE_MINUS_SRC_ALPHA),s.reset(),n.colorData.length>0?(s.setUniform("hasColor",1),(b=new M(t,s)).build(Array(n.segments[0].from).concat(n.colorData),"aColor",4),b.bind()):(s.setUniform("hasColor",0),t.disableVertexAttribArray(t.getAttribLocation(s.getProgram(),"aColor"))),s.setColor(A),this.setXAxis(n.series.xAxis),this.setYAxis(n.series.yAxis),this.setThreshold(u,g),"POINTS"===n.drawMode&&s.setPointSize(2*D(l.marker&&l.marker.radius,.5)*o),s.setSkipTranslation(n.skipTranslation),"bubble"===n.series.type&&s.setBubbleUniforms(n.series,n.zMin,n.zMax,o),s.setDrawAsCircle(I[n.series.type]||!1),r)){if(d>0||"LINE_STRIP"!==n.drawMode)for(p=0;p<n.segments.length;p++)r.render(n.segments[p].from,n.segments[p].to,n.drawMode);if(n.hasMarkers&&m)for(s.setPointSize(2*D(l.marker&&l.marker.radius,5)*o),s.setDrawAsCircle(!0),p=0;p<n.segments.length;p++)r.render(n.segments[p].from,n.segments[p].to,"POINTS")}}),i.debug.timeRendering&&console.timeEnd("gl rendering"),this.postRenderCallback&&this.postRenderCallback(this),this.flush()}render(e){if(this.clear(),e.renderer.forExport)return this.renderChart(e);this.isInited?this.renderChart(e):setTimeout(()=>{this.render(e)},1)}setSize(e,t){let i=this.shader;i&&(this.width!==e||this.height!==t)&&(this.width=e,this.height=t,i.bind(),i.setPMatrix(B.orthoMatrix(e,t)))}init(e,t){let i=this.settings;if(this.isInited=!1,!e)return!1;i.debug.timeSetup&&console.time("gl setup");for(let t=0;t<G.length&&(this.gl=e.getContext(G[t],{}),!this.gl);++t);let s=this.gl;if(!s)return!1;t||this.flush(),s.enable(s.BLEND),s.blendFunc(s.SRC_ALPHA,s.ONE_MINUS_SRC_ALPHA),s.disable(s.DEPTH_TEST),s.depthFunc(s.LESS);let r=this.shader=new E(s);if(!r)return!1;this.vbuffer=new M(s,r);let o=(e,t)=>{let i={isReady:!1,texture:w.createElement("canvas"),handle:s.createTexture()},r=i.texture.getContext("2d");this.textureHandles[e]=i,i.texture.width=512,i.texture.height=512,r.mozImageSmoothingEnabled=!1,r.webkitImageSmoothingEnabled=!1,r.msImageSmoothingEnabled=!1,r.imageSmoothingEnabled=!1,r.strokeStyle="rgba(255, 255, 255, 0)",r.fillStyle="#FFF",t(r);try{s.activeTexture(s.TEXTURE0),s.bindTexture(s.TEXTURE_2D,i.handle),s.texImage2D(s.TEXTURE_2D,0,s.RGBA,s.RGBA,s.UNSIGNED_BYTE,i.texture),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_WRAP_S,s.CLAMP_TO_EDGE),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_WRAP_T,s.CLAMP_TO_EDGE),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_MAG_FILTER,s.LINEAR),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_MIN_FILTER,s.LINEAR),s.bindTexture(s.TEXTURE_2D,null),i.isReady=!0}catch(e){}};return o("circle",e=>{e.beginPath(),e.arc(256,256,256,0,2*Math.PI),e.stroke(),e.fill()}),o("square",e=>{e.fillRect(0,0,512,512)}),o("diamond",e=>{e.beginPath(),e.moveTo(256,0),e.lineTo(512,256),e.lineTo(256,512),e.lineTo(0,256),e.lineTo(256,0),e.fill()}),o("triangle",e=>{e.beginPath(),e.moveTo(0,512),e.lineTo(256,0),e.lineTo(512,512),e.lineTo(0,512),e.fill()}),o("triangle-down",e=>{e.beginPath(),e.moveTo(0,0),e.lineTo(256,512),e.lineTo(512,0),e.lineTo(0,0),e.fill()}),this.isInited=!0,i.debug.timeSetup&&console.timeEnd("gl setup"),!0}destroy(){let e=this.gl,t=this.shader,i=this.vbuffer;this.flush(),i&&i.destroy(),t&&t.destroy(),e&&(z(this.textureHandles,t=>{t.handle&&e.deleteTexture(t.handle)}),e.canvas.width=1,e.canvas.height=1)}}!function(e){e.setLength=function(e,t,i){return Array.isArray(e)?(e.length=t,e):e[i?"subarray":"slice"](0,t)},e.splice=function(e,t,i,s,r=[]){if(Array.isArray(e))return Array.isArray(r)||(r=Array.from(r)),{removed:e.splice(t,i,...r),array:e};let o=Object.getPrototypeOf(e).constructor,n=e[s?"subarray":"slice"](t,t+i),a=new o(e.length-i+r.length);return a.set(e.subarray(0,t),0),a.set(r,t),a.set(e.subarray(t+i),t+r.length),{removed:n,array:a}}}(r||(r={}));let{setLength:O,splice:V}=r,{fireEvent:X,objectEach:F,uniqueKey:H}=d(),W=class{constructor(e={}){this.autoId=!e.id,this.columns={},this.id=e.id||H(),this.modified=this,this.rowCount=0,this.versionTag=H();let t=0;F(e.columns||{},(e,i)=>{this.columns[i]=e.slice(),t=Math.max(t,e.length)}),this.applyRowCount(t)}applyRowCount(e){this.rowCount=e,F(this.columns,(t,i)=>{t.length!==e&&(this.columns[i]=O(t,e))})}deleteRows(e,t=1){if(t>0&&e<this.rowCount){let i=0;F(this.columns,(s,r)=>{this.columns[r]=V(s,e,t).array,i=s.length}),this.rowCount=i}X(this,"afterDeleteRows",{rowIndex:e,rowCount:t}),this.versionTag=H()}getColumn(e,t){return this.columns[e]}getColumns(e,t){return(e||Object.keys(this.columns)).reduce((e,t)=>(e[t]=this.columns[t],e),{})}getRow(e,t){return(t||Object.keys(this.columns)).map(t=>this.columns[t]?.[e])}setColumn(e,t=[],i=0,s){this.setColumns({[e]:t},i,s)}setColumns(e,t,i){let s=this.rowCount;F(e,(e,t)=>{this.columns[t]=e.slice(),s=e.length}),this.applyRowCount(s),i?.silent||(X(this,"afterSetColumns"),this.versionTag=H())}setRow(e,t=this.rowCount,i,s){let{columns:r}=this,o=i?this.rowCount+1:t+1;F(e,(e,n)=>{let a=r[n]||s?.addColumns!==!1&&Array(o);a&&(i?a=V(a,t,0,!0,[e]).array:a[t]=e,r[n]=a)}),o>this.rowCount&&this.applyRowCount(o),s?.silent||(X(this,"afterSetRows"),this.versionTag=H())}},{getBoostClipRect:j,isChartSeriesBoosting:q}=A,{getOptions:Y}=d(),{composed:Z,doc:K,noop:Q,win:J}=d(),{addEvent:$,destroyObjectProperties:ee,error:et,extend:ei,fireEvent:es,isArray:er,isNumber:eo,pick:en,pushUnique:ea,wrap:el,defined:eh}=d();function ed(e,t){let i=t.boost;e&&i&&i.target&&i.canvas&&!q(t.chart)&&e.allocateBufferForSingleSeries(t)}function ef(e){return en(e&&e.options&&e.options.boost&&e.options.boost.enabled,!0)}function eu(e,t){let i=e.constructor,r=e.seriesGroup||t.group,o=e.chartWidth,n=e.chartHeight,a=e,l="undefined"!=typeof SVGForeignObjectElement,h=!1;q(e)?a=e:(a=t,h=!!(t.options.events?.click||t.options.point?.events?.click));let d=a.boost=a.boost||{};if(l=!1,s||(s=K.createElement("canvas")),!d.target&&(d.canvas=s,e.renderer.forExport||!l?(a.renderTarget=d.target=e.renderer.image("",0,0,o,n).addClass("highcharts-boost-canvas").add(r),d.clear=function(){d.target.attr({href:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="})},d.copy=function(){d.resize(),d.target.attr({href:d.canvas.toDataURL("image/png")})}):(d.targetFo=e.renderer.createElement("foreignObject").add(r),a.renderTarget=d.target=K.createElement("canvas"),d.targetCtx=d.target.getContext("2d"),d.targetFo.element.appendChild(d.target),d.clear=function(){d.target.width=d.canvas.width,d.target.height=d.canvas.height},d.copy=function(){d.target.width=d.canvas.width,d.target.height=d.canvas.height,d.targetCtx.drawImage(d.canvas,0,0)}),d.resize=function(){o=e.chartWidth,n=e.chartHeight,(d.targetFo||d.target).attr({x:0,y:0,width:o,height:n}).css({pointerEvents:h?void 0:"none",mixedBlendMode:"normal",opacity:1}).addClass(h?"highcharts-tracker":""),a instanceof i&&a.boost?.markerGroup?.translate(e.plotLeft,e.plotTop)},d.clipRect=e.renderer.clipRect(),(d.targetFo||d.target).attr({zIndex:t.options.zIndex}),a instanceof i&&(a.boost.markerGroup=a.renderer.g().add(r).translate(t.xAxis.pos,t.yAxis.pos))),d.canvas.width=o,d.canvas.height=n,d.clipRect){let t=j(e,a),i=t.width===e.clipBox.width&&t.height===e.clipBox.height?r:d.targetFo||d.target;d.clipRect.attr(t),i?.clip(d.clipRect)}return d.resize(),d.clear(),!d.wgl&&(d.wgl=new B(e=>{e.settings.debug.timeBufferCopy&&console.time("buffer copy"),d.copy(),e.settings.debug.timeBufferCopy&&console.timeEnd("buffer copy")}),d.wgl.init(d.canvas)||et("[highcharts boost] - unable to init WebGL renderer"),d.wgl.setOptions(e.options.boost||{}),a instanceof i&&d.wgl.allocateBuffer(e)),d.wgl.setSize(o,n),d.wgl}function eg(e){let t=e.points;if(t){let e,i;for(i=0;i<t.length;i+=1)(e=t[i])&&e.destroyElements&&e.destroyElements()}for(let t of(["graph","area","tracker"].forEach(t=>{let i=e[t];i&&(e[t]=i.destroy())}),e.zones))ee(t,void 0,!0)}function em(e,t,i,s,r,o){let n=(r=r||0)+(s=s||3e3),a=!0;for(;a&&r<n&&r<e.length;)a=t(e[r],r),++r;a&&(r<e.length?o?em(e,t,i,s,r,o):J.requestAnimationFrame?J.requestAnimationFrame(function(){em(e,t,i,s,r)}):setTimeout(em,0,e,t,i,s,r):i&&i())}function ec(e,t){let i=e.options,s=e.dataTable.modified.rowCount,r=e.xAxis&&e.xAxis.options,o=e.yAxis&&e.yAxis.options,n=e.colorAxis&&e.colorAxis.options;return s>(i.boostThreshold||Number.MAX_VALUE)&&eo(o.min)&&eo(o.max)&&(!t||eo(r.min)&&eo(r.max))&&(!n||eo(n.min)&&eo(n.max))}let ep=(e,t)=>!e.forceCrop&&(q(e.chart)||(t?t.length:0)>=(e.options.boostThreshold||Number.MAX_VALUE));function eb(){let e=this,t=e.chart;t.boost&&t.boost.markerGroup===e.markerGroup&&(e.markerGroup=void 0),t.hoverPoints&&(t.hoverPoints=t.hoverPoints.filter(function(t){return t.series===e})),t.hoverPoint&&t.hoverPoint.series===e&&(t.hoverPoint=void 0)}function ex(){let e=this.boost;e&&e.canvas&&e.target&&(e.wgl&&e.wgl.clear(),e.clear&&e.clear())}function eA(e){let t=e.boost;t&&t.canvas&&t.target&&t.wgl&&!q(e.chart)&&t.wgl.render(e.chart)}function ey(e,t){let i=e.options,s=e.xAxis,r=e.pointClass;if(t instanceof r)return t;let o=e.is("scatter"),n=(o&&e.getColumn("x",!0).length?e.getColumn("x",!0):void 0)||(e.getColumn("x").length?e.getColumn("x"):void 0)||i.xData||e.getColumn("x",!0)||!1,a=e.getColumn("y",!0)||i.yData||!1,l=new r(e,o&&n&&a?[n[t.i],a[t.i]]:(er(e.options.data)?e.options.data:[])[t.i],n?n[t.i]:void 0);return l.category=en(s.categories?s.categories[l.x]:l.x,l.x),l.key=l.name??l.category,l.dist=t.dist,l.distX=t.distX,l.plotX=t.plotX,l.plotY=t.plotY,l.index=t.i,l.percentage=t.percentage,l.isInside=e.isPointInside(l),l}function ev(e){let{options:t,xAxis:i,yAxis:s}=this;if(!this.isDirty&&!i.isDirty&&!s.isDirty&&!e)return!1;this.yAxis.setTickInterval();let r=t.boostThreshold||0,o=t.cropThreshold,n=this.getColumn("x"),a=i.getExtremes(),l=a.max??Number.MAX_VALUE,h=a.min??-Number.MAX_VALUE,d=this.getColumn("y"),f=s.getExtremes(),u=f.max??Number.MAX_VALUE,g=f.min??-Number.MAX_VALUE;if(!this.boosted&&i.old&&s.old&&h>=(i.old.min??-Number.MAX_VALUE)&&l<=(i.old.max??Number.MAX_VALUE)&&g>=(s.old.min??-Number.MAX_VALUE)&&u<=(s.old.max??Number.MAX_VALUE))return this.dataTable.modified.setColumns({x:n,y:d}),!0;let m=this.dataTable.rowCount;if(!r||m<r||o&&!this.forceCrop&&!this.getExtremesFromAll&&!t.getExtremesFromAll&&m<o)return this.dataTable.modified.setColumns({x:n,y:d}),!0;let c=[],p=[],b=[],x=!(eo(a.max)||eo(a.min)),A=!(eo(f.max)||eo(f.min)),y=!1,v,P=n[0],T=n[0],C,k=d?.[0],E=d?.[0];for(let e=0,t=n.length;e<t;++e)v=n[e],C=d?.[e],v>=h&&v<=l&&C>=g&&C<=u?(c.push({x:v,y:C}),p.push(v),b.push(C),x&&(P=Math.max(P,v),T=Math.min(T,v)),A&&(k=Math.max(k,C),E=Math.min(E,C))):y=!0;return x&&(i.dataMax=Math.max(P,i.dataMax||0),i.dataMin=Math.min(T,i.dataMin||0)),A&&(s.dataMax=Math.max(k,s.dataMax||0),s.dataMin=Math.min(E,s.dataMin||0)),this.cropped=y,this.cropStart=0,y&&this.dataTable.modified===this.dataTable&&(this.dataTable.modified=new W),this.dataTable.modified.setColumns({x:p,y:b}),ep(this,p)||(this.processedData=c),!0}function eP(){let e=this.options||{},t=this.chart,s=t.boost,r=this.boost,o=this.xAxis,n=this.yAxis,a=e.xData||this.getColumn("x",!0),l=e.yData||this.getColumn("y",!0),h=this.getColumn("low",!0),d=this.getColumn("high",!0),f=this.processedData||e.data,u=o.getExtremes(),g=u.min-(o.minPointOffset||0),m=u.max+(o.minPointOffset||0),c=n.getExtremes(),p=c.min-(n.minPointOffset||0),b=c.max+(n.minPointOffset||0),x={},A=!!this.sampling,y=e.enableMouseTracking,v=e.threshold,P=this.pointArrayMap&&"low,high"===this.pointArrayMap.join(","),T=!!e.stacking,C=this.cropStart||0,k=this.requireSorting,E=!a,M="x"===e.findNearestPointBy,S=(this.getColumn("x").length?this.getColumn("x"):void 0)||this.options.xData||this.getColumn("x",!0),w=en(e.lineWidth,1),U=e.nullInteraction&&p,R=t.tooltip,L=!1,_,z=n.getThreshold(v),D,N,I,G;if(!this.boosted||(this.points?.forEach(e=>{e?.destroyElements?.()}),this.points=[],R&&!R.isHidden?(t.hoverPoint?.series===this||t.hoverPoints?.some(e=>e.series===this))&&(t.hoverPoint=t.hoverPoints=void 0,R.hide(0)):t.hoverPoints&&(t.hoverPoints=t.hoverPoints.filter(e=>e.series!==this)),o.isPanning||n.isPanning)||(L=eu(t,this),t.boosted=!0,!this.visible))return;(this.points||this.graph)&&eg(this),q(t)?(this.markerGroup&&this.markerGroup!==s?.markerGroup&&this.markerGroup.destroy(),this.markerGroup=s?.markerGroup,r&&r.target&&(this.renderTarget=r.target=r.target.destroy())):(this.markerGroup===s?.markerGroup&&(this.markerGroup=void 0),this.markerGroup=this.plotGroup("markerGroup","markers","visible",1,t.seriesGroup).addClass("highcharts-tracker"));let B=this.points=[],O=(e,s,r,a)=>{let l=!!S&&S[C+r],h=e=>{t.inverted&&(e=o.len-e,s=n.len-s),B.push({destroy:Q,x:l,clientX:e,plotX:e,plotY:s,i:C+r,percentage:a})};e=Math.ceil(e),i=M?e:e+","+s,y&&(x[i]?l===S[S.length-1]&&(B.length--,h(e)):(x[i]=!0,h(e)))};this.buildKDTree=Q,es(this,"renderCanvas"),this.is("line")&&w>1&&r?.target&&s&&!s.lineWidthFilter&&(s.lineWidthFilter=t.renderer.definition({tagName:"filter",children:[{tagName:"feMorphology",attributes:{operator:"dilate",radius:.25*w}}],attributes:{id:"linewidth"}}),r.target.attr({filter:"url(#linewidth)"})),L&&(ed(L,this),L.pushSeries(this),eA(this));let V=L.settings;t.renderer.forExport||(V.debug.timeKDTree&&console.time("kd tree building"),em(T?this.data.slice(C):a||f,function(e,i){let s=void 0===t.index,r,a,f,u,c,x=!1,y=!0;return!eh(e)||(!s&&(E?(r=e[0],a=e[1]):(r=e,a=l[i]??U??null),P?(E&&(a=e.slice(1,3)),x=h[i],a=d[i]):T&&(r=e.x,x=(a=e.stackY)-e.y,c=e.percentage),k||(y=(a||0)>=p&&a<=b),null!==a&&r>=g&&r<=m&&y&&(f=o.toPixels(r,!0),A?((void 0===I||f===_)&&(P||(x=a),(void 0===G||a>N)&&(N=a,G=i),(void 0===I||x<D)&&(D=x,I=i)),M&&f===_||(void 0!==I&&(u=n.toPixels(N,!0),z=n.toPixels(D,!0),O(f,u,G,c),z!==u&&O(f,z,I,c)),I=G=void 0,_=f)):O(f,u=Math.ceil(n.toPixels(a,!0)),i,c))),!s)},()=>{es(this,"renderedCanvas"),delete this.buildKDTree,this.options&&this.buildKDTree(),V.debug.timeKDTree&&console.timeEnd("kd tree building")}))}function eT(e){let t=!0;if(this.chart.options&&this.chart.options.boost&&(t=void 0===this.chart.options.boost.enabled||this.chart.options.boost.enabled),!t||!this.boosted)return e.call(this);this.chart.boosted=!0;let i=eu(this.chart,this);i&&(ed(i,this),i.pushSeries(this)),eA(this)}function eC(e){if(this.boosted){if(ec(this))return{};if(this.xAxis.isPanning||this.yAxis.isPanning)return this}return e.apply(this,[].slice.call(arguments,1))}function ek(e){let t=this.options.data;if(ef(this.chart)&&u[this.type]){let s=this.is("scatter")&&!this.is("bubble")&&!this.is("treemap")&&!this.is("heatmap");if(!ep(this,t)||s||this.is("treemap")||this.options.stacking||!ec(this,!0)){if(this.boosted&&(this.xAxis?.isPanning||this.yAxis?.isPanning))return;s&&"treegrid"!==this.yAxis.type?ev.call(this,arguments[1]):e.apply(this,[].slice.call(arguments,1)),t=this.getColumn("x",!0)}if(this.boosted=ep(this,t),this.boosted){let e;this.options.data?.length&&(eo(e=this.getFirstValidPoint(this.options.data))||er(e)||this.is("treemap")||et(12,!1,this.chart));var i=this;i.boost=i.boost||{getPoint:e=>ey(i,e)};let t=i.boost.altered=[];if(["allowDG","directTouch","stickyTracking"].forEach(e=>{t.push({prop:e,val:i[e],own:Object.hasOwnProperty.call(i,e)})}),i.allowDG=!1,i.directTouch=!1,i.stickyTracking=!0,i.finishedAnimating=!0,i.labelBySeries&&(i.labelBySeries=i.labelBySeries.destroy()),i.is("scatter")&&!i.is("treemap")&&i.data.length){for(let e of i.data)e?.destroy?.();i.data.length=0,i.points.length=0,delete i.processedData}}else!function(e){let t=e.boost,i=e.chart,s=i.boost;if(s?.markerGroup)for(let e of(s.markerGroup.destroy(),s.markerGroup=void 0,i.series))e.markerGroup=void 0,e.markerGroup=e.plotGroup("markerGroup","markers","visible",1,i.seriesGroup).addClass("highcharts-tracker");t&&((t.altered||[]).forEach(t=>{t.own?e[t.prop]=t.val:delete e[t.prop]}),t.clear&&t.clear()),(i.seriesGroup||e.group)?.clip()}(this)}else e.apply(this,[].slice.call(arguments,1))}function eE(e){let t=e.apply(this,[].slice.call(arguments,1));return this.boost&&t?this.boost.getPoint(t):t}let eM={compose:function(e,t,i,s){if(ea(Z,"Boost.Series")){let r=Y().plotOptions,o=e.prototype;if($(e,"destroy",eb),$(e,"hide",ex),s&&(o.renderCanvas=eP),el(o,"getExtremes",eC),el(o,"processData",ek),el(o,"searchPoint",eE),["translate","generatePoints","drawTracker","drawPoints","render"].forEach(e=>(function(e,t,i){function s(e){let t=this.options.stacking&&("translate"===i||"generatePoints"===i);this.boosted&&!t&&ef(this.chart)&&"heatmap"!==this.type&&"treemap"!==this.type&&u[this.type]&&0!==this.options.boostThreshold?"render"===i&&this.renderCanvas&&this.renderCanvas():e.call(this)}if(el(e,i,s),"translate"===i)for(let e of["column","arearange","columnrange","heatmap","treemap"])t[e]&&el(t[e].prototype,i,s)})(o,t,e)),el(i.prototype,"firePointEvent",function(e,t,i){if("click"===t&&this.series.boosted){let e=i.point;if((e.dist||e.distX)>=(e.series.options.marker?.radius??10))return}return e.apply(this,[].slice.call(arguments,1))}),f.forEach(e=>{let i=r[e];i&&(i.boostThreshold=5e3,i.boostData=[],t[e].prototype.fillOpacity=!0)}),s){let{area:e,areaspline:i,bubble:s,column:r,heatmap:o,scatter:n,treemap:a}=t;if(e&&ei(e.prototype,{fill:!0,fillOpacity:!0,sampling:!0}),i&&ei(i.prototype,{fill:!0,fillOpacity:!0,sampling:!0}),s){let e=s.prototype;delete e.buildKDTree,el(e,"markerAttribs",function(e){return!this.boosted&&e.apply(this,[].slice.call(arguments,1))})}r&&ei(r.prototype,{fill:!0,sampling:!0}),n&&(n.prototype.fill=!0),[o,a].forEach(e=>{e&&el(e.prototype,"drawPoints",eT)})}}return e},destroyGraphics:eg,eachAsync:em,getPoint:ey},eS={defaultHTMLColorMap:{aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dodgerblue:"#1e90ff",feldspar:"#d19275",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslateblue:"#8470ff",lightslategray:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370d8",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#d87093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",violetred:"#d02090",wheat:"#f5deb3",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"}},{doc:ew,win:eU}=d(),{addEvent:eR,error:eL}=d(),e_=["webgl","experimental-webgl","moz-webgl","webkit-3d"];function ez(){let e,t=!1;if(void 0!==eU.WebGLRenderingContext){e=ew.createElement("canvas");for(let t=0;t<e_.length;++t)try{if(null!=e.getContext(e_[t]))return!0}catch(e){}}return!1}let eD={compose:function(e,t,i,s,r,o){let n=ez();n||(void 0!==d().initCanvasBoost?d().initCanvasBoost():eL(26)),o&&!o.names.lightgoldenrodyellow&&(o.names={...o.names,...eS.defaultHTMLColorMap}),A.compose(e,n),eM.compose(i,s,r,n),eR(t,"setExtremes",function(e){for(let t of[this.chart,...this.series].map(e=>e.renderTarget).filter(Boolean)){let{horiz:i,pos:s}=this,r=i?"scaleX":"scaleY",o=i?"translateX":"translateY",n=t?.[r]??1,a=1,l=0,h=1,d="none";this.isPanning&&(a=(e.scale??1)*n,l=(t?.[o]||0)-a*(e.move||0)+n*s-a*s,h=.7,d="blur(3px)"),t?.attr({[r]:a,[o]:l}).css({transition:"250ms filter, 250ms opacity",filter:d,opacity:h})}})},hasWebGLSupport:ez},eN=d();eN.hasWebGLSupport=eD.hasWebGLSupport,eD.compose(eN.Chart,eN.Axis,eN.Series,eN.seriesTypes,eN.Point,eN.Color);let eI=d();return l.default})());