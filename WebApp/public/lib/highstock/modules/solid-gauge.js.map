{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/solid-gauge\n * @requires highcharts\n * @requires highcharts/highcharts-more\n *\n * Solid angular gauge module\n *\n * (c) 2010-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"Color\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/solid-gauge\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"],amd1[\"Color\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/solid-gauge\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"Color\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"Color\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__620__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 620:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__620__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ solid_gauge_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Extensions/BorderRadius.js\n/* *\n *\n *  Highcharts Border Radius module\n *\n *  Author: Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { defaultOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { noop } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent, extend, isObject, merge, relativeLength } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\nconst defaultBorderRadiusOptions = {\n    radius: 0,\n    scope: 'stack',\n    where: void 0\n};\n/* *\n *\n *  Variables\n *\n * */\nlet oldArc = noop;\nlet oldRoundedRect = noop;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction applyBorderRadius(path, i, r) {\n    const a = path[i];\n    let b = path[i + 1];\n    if (b[0] === 'Z') {\n        b = path[0];\n    }\n    let line, arc, fromLineToArc;\n    // From straight line to arc\n    if ((a[0] === 'M' || a[0] === 'L') && b[0] === 'A') {\n        line = a;\n        arc = b;\n        fromLineToArc = true;\n        // From arc to straight line\n    }\n    else if (a[0] === 'A' && (b[0] === 'M' || b[0] === 'L')) {\n        line = b;\n        arc = a;\n    }\n    if (line && arc && arc.params) {\n        const bigR = arc[1], \n        // In our use cases, outer pie slice arcs are clockwise and inner\n        // arcs (donut/sunburst etc) are anti-clockwise\n        clockwise = arc[5], params = arc.params, { start, end, cx, cy } = params;\n        // Some geometric constants\n        const relativeR = clockwise ? (bigR - r) : (bigR + r), \n        // The angle, on the big arc, that the border radius arc takes up\n        angleOfBorderRadius = relativeR ? Math.asin(r / relativeR) : 0, angleOffset = clockwise ?\n            angleOfBorderRadius :\n            -angleOfBorderRadius, \n        // The distance along the radius of the big arc to the starting\n        // point of the small border radius arc\n        distanceBigCenterToStartArc = (Math.cos(angleOfBorderRadius) *\n            relativeR);\n        // From line to arc\n        if (fromLineToArc) {\n            // Update the cache\n            params.start = start + angleOffset;\n            // First move to the start position at the radial line. We want to\n            // start one borderRadius closer to the center.\n            line[1] = cx + distanceBigCenterToStartArc * Math.cos(start);\n            line[2] = cy + distanceBigCenterToStartArc * Math.sin(start);\n            // Now draw an arc towards the point where the small circle touches\n            // the great circle.\n            path.splice(i + 1, 0, [\n                'A',\n                r,\n                r,\n                0, // Slanting,\n                0, // Long arc\n                1, // Clockwise\n                cx + bigR * Math.cos(params.start),\n                cy + bigR * Math.sin(params.start)\n            ]);\n            // From arc to line\n        }\n        else {\n            // Update the cache\n            params.end = end - angleOffset;\n            // End the big arc a bit earlier\n            arc[6] = cx + bigR * Math.cos(params.end);\n            arc[7] = cy + bigR * Math.sin(params.end);\n            // Draw a small arc towards a point on the end angle, but one\n            // borderRadius closer to the center relative to the perimeter.\n            path.splice(i + 1, 0, [\n                'A',\n                r,\n                r,\n                0,\n                0,\n                1,\n                cx + distanceBigCenterToStartArc * Math.cos(end),\n                cy + distanceBigCenterToStartArc * Math.sin(end)\n            ]);\n        }\n        // Long or short arc must be reconsidered because we have modified the\n        // start and end points\n        arc[4] = Math.abs(params.end - params.start) < Math.PI ? 0 : 1;\n    }\n}\n/**\n * Extend arc with borderRadius.\n * @private\n */\nfunction arc(x, y, w, h, options = {}) {\n    const path = oldArc(x, y, w, h, options), { innerR = 0, r = w, start = 0, end = 0 } = options;\n    if (options.open || !options.borderRadius) {\n        return path;\n    }\n    const alpha = end - start, sinHalfAlpha = Math.sin(alpha / 2), borderRadius = Math.max(Math.min(relativeLength(options.borderRadius || 0, r - innerR), \n    // Cap to half the sector radius\n    (r - innerR) / 2, \n    // For smaller pie slices, cap to the largest small circle that\n    // can be fitted within the sector\n    (r * sinHalfAlpha) / (1 + sinHalfAlpha)), 0), \n    // For the inner radius, we need an extra cap because the inner arc\n    // is shorter than the outer arc\n    innerBorderRadius = Math.min(borderRadius, 2 * (alpha / Math.PI) * innerR);\n    // Apply turn-by-turn border radius. Start at the end since we're\n    // splicing in arc segments.\n    let i = path.length - 1;\n    while (i--) {\n        applyBorderRadius(path, i, i > 1 ? innerBorderRadius : borderRadius);\n    }\n    return path;\n}\n/** @private */\nfunction seriesOnAfterColumnTranslate() {\n    if (this.options.borderRadius &&\n        !(this.chart.is3d && this.chart.is3d())) {\n        const { options, yAxis } = this, percent = options.stacking === 'percent', seriesDefault = defaultOptions.plotOptions?.[this.type]\n            ?.borderRadius, borderRadius = optionsToObject(options.borderRadius, isObject(seriesDefault) ? seriesDefault : {}), reversed = yAxis.options.reversed;\n        for (const point of this.points) {\n            const { shapeArgs } = point;\n            if (point.shapeType === 'roundedRect' && shapeArgs) {\n                const { width = 0, height = 0, y = 0 } = shapeArgs;\n                let brBoxY = y, brBoxHeight = height;\n                // It would be nice to refactor StackItem.getStackBox/\n                // setOffset so that we could get a reliable box out of\n                // it. Currently it is close if we remove the label\n                // offset, but we still need to run crispCol and also\n                // flip it if inverted, so atm it is simpler to do it\n                // like the below.\n                if (borderRadius.scope === 'stack' &&\n                    point.stackTotal) {\n                    const stackEnd = yAxis.translate(percent ? 100 : point.stackTotal, false, true, false, true), stackThreshold = yAxis.translate(options.threshold || 0, false, true, false, true), box = this.crispCol(0, Math.min(stackEnd, stackThreshold), 0, Math.abs(stackEnd - stackThreshold));\n                    brBoxY = box.y;\n                    brBoxHeight = box.height;\n                }\n                const flip = (point.negative ? -1 : 1) *\n                    (reversed ? -1 : 1) === -1;\n                // Handle the where option\n                let where = borderRadius.where;\n                // Waterfall, hanging columns should have rounding on\n                // all sides\n                if (!where &&\n                    this.is('waterfall') &&\n                    Math.abs((point.yBottom || 0) -\n                        (this.translatedThreshold || 0)) > this.borderWidth) {\n                    where = 'all';\n                }\n                if (!where) {\n                    where = 'end';\n                }\n                // Get the radius\n                const r = Math.min(relativeLength(borderRadius.radius, width), width / 2, \n                // Cap to the height, but not if where is `end`\n                where === 'all' ? height / 2 : Infinity) || 0;\n                // If the `where` option is 'end', cut off the\n                // rectangles by making the border-radius box one r\n                // greater, so that the imaginary radius falls outside\n                // the rectangle.\n                if (where === 'end') {\n                    if (flip) {\n                        brBoxY -= r;\n                        brBoxHeight += r;\n                    }\n                    else {\n                        brBoxHeight += r;\n                    }\n                }\n                extend(shapeArgs, { brBoxHeight, brBoxY, r });\n            }\n        }\n    }\n}\n/** @private */\nfunction compose(SeriesClass, SVGElementClass, SVGRendererClass) {\n    const PieSeriesClass = SeriesClass.types.pie;\n    if (!SVGElementClass.symbolCustomAttribs.includes('borderRadius')) {\n        const symbols = SVGRendererClass.prototype.symbols;\n        addEvent(SeriesClass, 'afterColumnTranslate', seriesOnAfterColumnTranslate, {\n            // After columnrange and polar column modifications\n            order: 9\n        });\n        addEvent(PieSeriesClass, 'afterTranslate', pieSeriesOnAfterTranslate);\n        SVGElementClass.symbolCustomAttribs.push('borderRadius', 'brBoxHeight', 'brBoxY');\n        oldArc = symbols.arc;\n        oldRoundedRect = symbols.roundedRect;\n        symbols.arc = arc;\n        symbols.roundedRect = roundedRect;\n    }\n}\n/** @private */\nfunction optionsToObject(options, seriesBROptions) {\n    if (!isObject(options)) {\n        options = { radius: options || 0 };\n    }\n    return merge(defaultBorderRadiusOptions, seriesBROptions, options);\n}\n/** @private */\nfunction pieSeriesOnAfterTranslate() {\n    const borderRadius = optionsToObject(this.options.borderRadius);\n    for (const point of this.points) {\n        const shapeArgs = point.shapeArgs;\n        if (shapeArgs) {\n            shapeArgs.borderRadius = relativeLength(borderRadius.radius, (shapeArgs.r || 0) - ((shapeArgs.innerR) || 0));\n        }\n    }\n}\n/**\n * Extend roundedRect with individual cutting through rOffset.\n * @private\n */\nfunction roundedRect(x, y, width, height, options = {}) {\n    const path = oldRoundedRect(x, y, width, height, options), { r = 0, brBoxHeight = height, brBoxY = y } = options, brOffsetTop = y - brBoxY, brOffsetBtm = (brBoxY + brBoxHeight) - (y + height), \n    // When the distance to the border-radius box is greater than the r\n    // itself, it means no border radius. The -0.1 accounts for float\n    // rounding errors.\n    rTop = (brOffsetTop - r) > -0.1 ? 0 : r, rBtm = (brOffsetBtm - r) > -0.1 ? 0 : r, cutTop = Math.max(rTop && brOffsetTop, 0), cutBtm = Math.max(rBtm && brOffsetBtm, 0);\n    /*\n\n    The naming of control points:\n\n      / a -------- b \\\n     /                \\\n    h                  c\n    |                  |\n    |                  |\n    |                  |\n    g                  d\n     \\                /\n      \\ f -------- e /\n\n    */\n    const a = [x + rTop, y], b = [x + width - rTop, y], c = [x + width, y + rTop], d = [\n        x + width, y + height - rBtm\n    ], e = [\n        x + width - rBtm,\n        y + height\n    ], f = [x + rBtm, y + height], g = [x, y + height - rBtm], h = [x, y + rTop];\n    const applyPythagoras = (r, altitude) => Math.sqrt(Math.pow(r, 2) - Math.pow(altitude, 2));\n    // Inside stacks, cut off part of the top\n    if (cutTop) {\n        const base = applyPythagoras(rTop, rTop - cutTop);\n        a[0] -= base;\n        b[0] += base;\n        c[1] = h[1] = y + rTop - cutTop;\n    }\n    // Column is lower than the radius. Cut off bottom inside the top\n    // radius.\n    if (height < rTop - cutTop) {\n        const base = applyPythagoras(rTop, rTop - cutTop - height);\n        c[0] = d[0] = x + width - rTop + base;\n        e[0] = Math.min(c[0], e[0]);\n        f[0] = Math.max(d[0], f[0]);\n        g[0] = h[0] = x + rTop - base;\n        c[1] = h[1] = y + height;\n    }\n    // Inside stacks, cut off part of the bottom\n    if (cutBtm) {\n        const base = applyPythagoras(rBtm, rBtm - cutBtm);\n        e[0] += base;\n        f[0] -= base;\n        d[1] = g[1] = y + height - rBtm + cutBtm;\n    }\n    // Cut off top inside the bottom radius\n    if (height < rBtm - cutBtm) {\n        const base = applyPythagoras(rBtm, rBtm - cutBtm - height);\n        c[0] = d[0] = x + width - rBtm + base;\n        b[0] = Math.min(c[0], b[0]);\n        a[0] = Math.max(d[0], a[0]);\n        g[0] = h[0] = x + rBtm - base;\n        d[1] = g[1] = y;\n    }\n    // Preserve the box for data labels\n    path.length = 0;\n    path.push(['M', ...a], \n    // Top side\n    ['L', ...b], \n    // Top right corner\n    ['A', rTop, rTop, 0, 0, 1, ...c], \n    // Right side\n    ['L', ...d], \n    // Bottom right corner\n    ['A', rBtm, rBtm, 0, 0, 1, ...e], \n    // Bottom side\n    ['L', ...f], \n    // Bottom left corner\n    ['A', rBtm, rBtm, 0, 0, 1, ...g], \n    // Left side\n    ['L', ...h], \n    // Top left corner\n    ['A', rTop, rTop, 0, 0, 1, ...a], ['Z']);\n    return path;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst BorderRadius = {\n    compose,\n    optionsToObject\n};\n/* harmony default export */ const Extensions_BorderRadius = (BorderRadius);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Detailed options for border radius.\n *\n * @sample  {highcharts} highcharts/plotoptions/column-borderradius/\n *          Rounded columns\n * @sample  highcharts/plotoptions/series-border-radius\n *          Column and pie with rounded border\n *\n * @interface Highcharts.BorderRadiusOptionsObject\n */ /**\n* The border radius. A number signifies pixels. A percentage string, like for\n* example `50%`, signifies a relative size. For columns this is relative to the\n* column width, for pies it is relative to the radius and the inner radius.\n*\n* @name Highcharts.BorderRadiusOptionsObject#radius\n* @type {string|number}\n*/ /**\n* The scope of the rounding for column charts. In a stacked column chart, the\n* value `point` means each single point will get rounded corners. The value\n* `stack` means the rounding will apply to the full stack, so that only points\n* close to the top or bottom will receive rounding.\n*\n* @name Highcharts.BorderRadiusOptionsObject#scope\n* @validvalue [\"point\", \"stack\"]\n* @type {string}\n*/ /**\n* For column charts, where in the point or stack to apply rounding. The `end`\n* value means only those corners at the point value will be rounded, leaving\n* the corners at the base or threshold unrounded. This is the most intuitive\n* behaviour. The `all` value means also the base will be rounded.\n*\n* @name Highcharts.BorderRadiusOptionsObject#where\n* @validvalue [\"all\", \"end\"]\n* @type {string}\n* @default end\n*/\n(''); // Keeps doclets above in JS file\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Color\"],\"commonjs\":[\"highcharts\",\"Color\"],\"commonjs2\":[\"highcharts\",\"Color\"],\"root\":[\"Highcharts\",\"Color\"]}\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_ = __webpack_require__(620);\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default = /*#__PURE__*/__webpack_require__.n(highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_);\n;// ./code/es-modules/Core/Axis/Color/ColorAxisLike.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { parse: color } = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default());\n\nconst { merge: ColorAxisLike_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Namespace\n *\n * */\nvar ColorAxisLike;\n(function (ColorAxisLike) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Initialize defined data classes.\n     * @private\n     */\n    function initDataClasses(userOptions) {\n        const axis = this, chart = axis.chart, legendItem = axis.legendItem = axis.legendItem || {}, options = axis.options, userDataClasses = userOptions.dataClasses || [];\n        let dataClass, dataClasses, colorCount = chart.options.chart.colorCount, colorCounter = 0, colors;\n        axis.dataClasses = dataClasses = [];\n        legendItem.labels = [];\n        for (let i = 0, iEnd = userDataClasses.length; i < iEnd; ++i) {\n            dataClass = userDataClasses[i];\n            dataClass = ColorAxisLike_merge(dataClass);\n            dataClasses.push(dataClass);\n            if (!chart.styledMode && dataClass.color) {\n                continue;\n            }\n            if (options.dataClassColor === 'category') {\n                if (!chart.styledMode) {\n                    colors = chart.options.colors || [];\n                    colorCount = colors.length;\n                    dataClass.color = colors[colorCounter];\n                }\n                dataClass.colorIndex = colorCounter;\n                // Loop back to zero\n                colorCounter++;\n                if (colorCounter === colorCount) {\n                    colorCounter = 0;\n                }\n            }\n            else {\n                dataClass.color = color(options.minColor).tweenTo(color(options.maxColor), iEnd < 2 ? 0.5 : i / (iEnd - 1) // #3219\n                );\n            }\n        }\n    }\n    ColorAxisLike.initDataClasses = initDataClasses;\n    /**\n     * Create initial color stops.\n     * @private\n     */\n    function initStops() {\n        const axis = this, options = axis.options, stops = axis.stops = options.stops || [\n            [0, options.minColor || ''],\n            [1, options.maxColor || '']\n        ];\n        for (let i = 0, iEnd = stops.length; i < iEnd; ++i) {\n            stops[i].color = color(stops[i][1]);\n        }\n    }\n    ColorAxisLike.initStops = initStops;\n    /**\n     * Normalize logarithmic values.\n     * @private\n     */\n    function normalizedValue(value) {\n        const axis = this, max = axis.max || 0, min = axis.min || 0;\n        if (axis.logarithmic) {\n            value = axis.logarithmic.log2lin(value);\n        }\n        return 1 - ((max - value) /\n            ((max - min) || 1));\n    }\n    ColorAxisLike.normalizedValue = normalizedValue;\n    /**\n     * Translate from a value to a color.\n     * @private\n     */\n    function toColor(value, point) {\n        const axis = this;\n        const dataClasses = axis.dataClasses;\n        const stops = axis.stops;\n        let pos, from, to, color, dataClass, i;\n        if (dataClasses) {\n            i = dataClasses.length;\n            while (i--) {\n                dataClass = dataClasses[i];\n                from = dataClass.from;\n                to = dataClass.to;\n                if ((typeof from === 'undefined' || value >= from) &&\n                    (typeof to === 'undefined' || value <= to)) {\n                    color = dataClass.color;\n                    if (point) {\n                        point.dataClass = i;\n                        point.colorIndex = dataClass.colorIndex;\n                    }\n                    break;\n                }\n            }\n        }\n        else {\n            pos = axis.normalizedValue(value);\n            i = stops.length;\n            while (i--) {\n                if (pos > stops[i][0]) {\n                    break;\n                }\n            }\n            from = stops[i] || stops[i + 1];\n            to = stops[i + 1] || from;\n            // The position within the gradient\n            pos = 1 - (to[0] - pos) / ((to[0] - from[0]) || 1);\n            color = from.color.tweenTo(to.color, pos);\n        }\n        return color;\n    }\n    ColorAxisLike.toColor = toColor;\n})(ColorAxisLike || (ColorAxisLike = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Color_ColorAxisLike = (ColorAxisLike);\n\n;// ./code/es-modules/Core/Axis/SolidGaugeAxis.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { extend: SolidGaugeAxis_extend } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction init(axis) {\n    SolidGaugeAxis_extend(axis, Color_ColorAxisLike);\n}\n/* *\n *\n *  Default export\n *\n * */\nconst SolidGaugeAxis = {\n    init\n};\n/* harmony default export */ const Axis_SolidGaugeAxis = (SolidGaugeAxis);\n\n;// ./code/es-modules/Series/SolidGauge/SolidGaugeSeriesDefaults.js\n/* *\n *\n *  Solid angular gauge module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A solid gauge is a circular gauge where the value is indicated by a filled\n * arc, and the color of the arc may variate with the value.\n *\n * @sample highcharts/demo/gauge-solid/\n *         Solid gauges\n *\n * @extends      plotOptions.gauge\n * @excluding    dial, pivot, wrap\n * @product      highcharts\n * @requires     modules/solid-gauge\n * @optionparent plotOptions.solidgauge\n */\nconst SolidGaugeSeriesDefaults = {\n    /**\n     * The inner radius for points in a solid gauge. Can be given only in\n     * percentage, either as a number or a string like `\"50%\"`.\n     *\n     * @sample {highcharts} highcharts/plotoptions/solidgauge-radius/\n     *         Individual radius and innerRadius\n     *\n     * @type      {string}\n     * @default   \"60%\"\n     * @since     4.1.6\n     * @product   highcharts\n     * @apioption plotOptions.solidgauge.innerRadius\n     */\n    /**\n     * Whether the strokes of the solid gauge should be `round` or `square`.\n     *\n     * @sample {highcharts} highcharts/demo/gauge-multiple-kpi/\n     *         Rounded gauge\n     *\n     * @type       {string}\n     * @default    round\n     * @since      4.2.2\n     * @product    highcharts\n     * @validvalue [\"square\", \"round\"]\n     * @apioption  plotOptions.solidgauge.linecap\n     */\n    /**\n     * Allow the gauge to overshoot the end of the perimeter axis by this\n     * many degrees. Say if the gauge axis goes from 0 to 60, a value of\n     * 100, or 1000, will show 5 degrees beyond the end of the axis when this\n     * option is set to 5.\n     *\n     * @type      {number}\n     * @default   0\n     * @since     3.0.10\n     * @product   highcharts\n     * @apioption plotOptions.solidgauge.overshoot\n     */\n    /**\n     * The outer radius for points in a solid gauge. Can be given only in\n     * percentage, either as a number or a string like `\"100%\"`.\n     *\n     * @sample {highcharts} highcharts/plotoptions/solidgauge-radius/\n     *         Individual radius and innerRadius\n     *\n     * @type      {string}\n     * @default   \"100%\"\n     * @since     4.1.6\n     * @product   highcharts\n     * @apioption plotOptions.solidgauge.radius\n     */\n    /**\n     * Whether to draw rounded edges on the gauge. This options adds the radius\n     * of the rounding to the ends of the arc, so it extends past the actual\n     * values. When `borderRadius` is set, it takes precedence over `rounded`. A\n     * `borderRadius` of 50% behaves like `rounded`, except the shape is not\n     * extended past its value.\n     *\n     * @sample {highcharts} highcharts/demo/gauge-multiple-kpi/\n     *         Gauge showing multiple KPIs\n     *\n     * @type      {boolean}\n     * @default   false\n     * @since     5.0.8\n     * @product   highcharts\n     * @apioption plotOptions.solidgauge.rounded\n     */\n    /**\n     * The threshold or base level for the gauge.\n     *\n     * @sample {highcharts} highcharts/plotoptions/solidgauge-threshold/\n     *         Zero threshold with negative and positive values\n     *\n     * @type      {number|null}\n     * @since     5.0.3\n     * @product   highcharts\n     * @apioption plotOptions.solidgauge.threshold\n     */\n    /**\n     * Whether to give each point an individual color.\n     */\n    colorByPoint: true,\n    dataLabels: {\n        y: 0\n    }\n};\n/**\n * A `solidgauge` series. If the [type](#series.solidgauge.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n *\n * @extends   series,plotOptions.solidgauge\n * @excluding animationLimit, boostThreshold, connectEnds, connectNulls,\n *            cropThreshold, dashStyle, dataParser, dataURL, dial,\n *            findNearestPointBy, getExtremesFromAll, marker, negativeColor,\n *            pointPlacement, pivot, shadow, softThreshold, stack, stacking,\n *            states, step, threshold, turboThreshold, wrap, zoneAxis, zones,\n *            dataSorting, boostBlending\n * @product   highcharts\n * @requires  modules/solid-gauge\n * @apioption series.solidgauge\n */\n/**\n * An array of data points for the series. For the `solidgauge` series\n * type, points can be given in the following ways:\n *\n * 1. An array of numerical values. In this case, the numerical values will be\n *    interpreted as `y` options. Example:\n *    ```js\n *    data: [0, 5, 3, 5]\n *    ```\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.solidgauge.turboThreshold), this option is not\n *    available.\n *    ```js\n *    data: [{\n *        y: 5,\n *        name: \"Point2\",\n *        color: \"#00FF00\"\n *    }, {\n *        y: 7,\n *        name: \"Point1\",\n *        color: \"#FF00FF\"\n *    }]\n *    ```\n *\n * The typical gauge only contains a single data value.\n *\n * @sample {highcharts} highcharts/chart/reflow-true/\n *         Numerical values\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<number|null|*>}\n * @extends   series.gauge.data\n * @product   highcharts\n * @apioption series.solidgauge.data\n */\n/**\n * The inner radius of an individual point in a solid gauge. Can be given only\n * in percentage, either as a number or a string like `\"50%\"`.\n *\n * @sample {highcharts} highcharts/plotoptions/solidgauge-radius/\n *         Individual radius and innerRadius\n *\n * @type      {string}\n * @since     4.1.6\n * @product   highcharts\n * @apioption series.solidgauge.data.innerRadius\n */\n/**\n * The outer radius of an individual point in a solid gauge. Can be\n * given only in percentage, either as a number or a string like `\"100%\"`.\n *\n * @sample {highcharts} highcharts/plotoptions/solidgauge-radius/\n *         Individual radius and innerRadius\n *\n * @type      {string}\n * @since     4.1.6\n * @product   highcharts\n * @apioption series.solidgauge.data.radius\n */\n''; // Keeps doclets above separate\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const SolidGauge_SolidGaugeSeriesDefaults = (SolidGaugeSeriesDefaults);\n\n;// ./code/es-modules/Series/SolidGauge/SolidGaugeSeries.js\n/* *\n *\n *  Solid angular gauge module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { gauge: GaugeSeries, pie: PieSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\n\n\nconst { clamp, extend: SolidGaugeSeries_extend, isNumber, merge: SolidGaugeSeries_merge, pick, pInt } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * SolidGauge series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.solidgauge\n *\n * @augments Highcarts.Series\n */\nclass SolidGaugeSeries extends GaugeSeries {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    // Extend the translate function to extend the Y axis with the necessary\n    // decoration (#5895).\n    translate() {\n        const axis = this.yAxis;\n        Axis_SolidGaugeAxis.init(axis);\n        // Prepare data classes\n        if (!axis.dataClasses && axis.options.dataClasses) {\n            axis.initDataClasses(axis.options);\n        }\n        axis.initStops();\n        // Generate points and inherit data label position\n        GaugeSeries.prototype.translate.call(this);\n    }\n    // Draw the points where each point is one needle.\n    drawPoints() {\n        const series = this, yAxis = series.yAxis, center = yAxis.center, options = series.options, renderer = series.chart.renderer, overshoot = options.overshoot, rounded = options.rounded && options.borderRadius === void 0, overshootVal = isNumber(overshoot) ?\n            overshoot / 180 * Math.PI :\n            0;\n        let thresholdAngleRad;\n        // Handle the threshold option\n        if (isNumber(options.threshold)) {\n            thresholdAngleRad = yAxis.startAngleRad + yAxis.translate(options.threshold, void 0, void 0, void 0, true);\n        }\n        this.thresholdAngleRad = pick(thresholdAngleRad, yAxis.startAngleRad);\n        for (const point of series.points) {\n            // #10630 null point should not be draw\n            if (!point.isNull) { // Condition like in pie chart\n                const radius = ((pInt(pick(point.options.radius, options.radius, 100 // %\n                )) * center[2]) / 200), innerRadius = ((pInt(pick(point.options.innerRadius, options.innerRadius, 60 // %\n                )) * center[2]) / 200), axisMinAngle = Math.min(yAxis.startAngleRad, yAxis.endAngleRad), axisMaxAngle = Math.max(yAxis.startAngleRad, yAxis.endAngleRad);\n                let graphic = point.graphic, rotation = (yAxis.startAngleRad +\n                    yAxis.translate(point.y, void 0, void 0, void 0, true)), shapeArgs, d, toColor = yAxis.toColor(point.y, point), className = point.getClassName();\n                if (toColor === 'none') { // #3708\n                    toColor = point.color || series.color || 'none';\n                }\n                if (toColor !== 'none') {\n                    point.color = toColor;\n                }\n                // Handle overshoot and clipping to axis max/min\n                rotation = clamp(rotation, axisMinAngle - overshootVal, axisMaxAngle + overshootVal);\n                // Handle the wrap option\n                if (options.wrap === false) {\n                    rotation = clamp(rotation, axisMinAngle, axisMaxAngle);\n                }\n                const angleOfRounding = rounded ?\n                    ((radius - innerRadius) / 2) / radius :\n                    0, start = Math.min(rotation, series.thresholdAngleRad) -\n                    angleOfRounding;\n                let end = Math.max(rotation, series.thresholdAngleRad) +\n                    angleOfRounding;\n                if (end - start > 2 * Math.PI) {\n                    end = start + 2 * Math.PI;\n                }\n                let borderRadius = rounded ? '50%' : 0;\n                if (options.borderRadius) {\n                    borderRadius = Extensions_BorderRadius.optionsToObject(options.borderRadius).radius;\n                }\n                point.shapeArgs = shapeArgs = {\n                    x: center[0],\n                    y: center[1],\n                    r: radius,\n                    innerR: innerRadius,\n                    start,\n                    end,\n                    borderRadius\n                };\n                point.startR = radius; // For PieSeries.animate\n                if (graphic) {\n                    d = shapeArgs.d;\n                    graphic.animate(SolidGaugeSeries_extend({ fill: toColor }, shapeArgs));\n                    if (d) {\n                        shapeArgs.d = d; // Animate alters it\n                    }\n                }\n                else {\n                    point.graphic = graphic = renderer.arc(shapeArgs)\n                        .attr({\n                        fill: toColor,\n                        'sweep-flag': 0\n                    })\n                        .add(series.group);\n                }\n                if (!series.chart.styledMode) {\n                    if (options.linecap !== 'square') {\n                        graphic.attr({\n                            'stroke-linecap': 'round',\n                            'stroke-linejoin': 'round'\n                        });\n                    }\n                    graphic.attr({\n                        stroke: options.borderColor || 'none',\n                        'stroke-width': options.borderWidth || 0\n                    });\n                }\n                else if (series.yAxis?.stops) {\n                    className = className\n                        .replace(/highcharts-color-\\d/gm, '')\n                        .trim();\n                }\n                if (graphic) {\n                    graphic.addClass(className);\n                }\n            }\n        }\n    }\n    // Extend the pie slice animation by animating from start angle and up.\n    animate(init) {\n        if (!init) {\n            this.startAngleRad = this.thresholdAngleRad;\n            PieSeries.prototype.animate.call(this, init);\n        }\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nSolidGaugeSeries.defaultOptions = SolidGaugeSeries_merge(GaugeSeries.defaultOptions, SolidGauge_SolidGaugeSeriesDefaults);\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('solidgauge', SolidGaugeSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const SolidGauge_SolidGaugeSeries = ((/* unused pure expression or super */ null && (SolidGaugeSeries)));\n\n;// ./code/es-modules/masters/modules/solid-gauge.js\n\n\n\n\n/* harmony default export */ const solid_gauge_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__620__", "ColorAxisLike", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "solid_gauge_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "defaultOptions", "noop", "addEvent", "extend", "isObject", "merge", "<PERSON><PERSON><PERSON><PERSON>", "defaultBorderRadiusOptions", "radius", "scope", "where", "optionsToObject", "options", "seriesBROptions", "Extensions_BorderRadius", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_", "parse", "color", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default", "ColorAxisLike_merge", "initDataClasses", "userOptions", "chart", "axis", "legendItem", "userDataClasses", "dataClasses", "dataClass", "colorCount", "colorCounter", "colors", "labels", "i", "iEnd", "length", "push", "styledMode", "dataClassColor", "colorIndex", "minColor", "tweenTo", "maxColor", "initStops", "stops", "normalizedValue", "value", "max", "min", "logarithmic", "log2lin", "toColor", "point", "pos", "from", "to", "Color_ColorAxisLike", "SolidGaugeAxis_extend", "Axis_SolidGaugeAxis", "init", "gauge", "GaugeSeries", "pie", "PieSeries", "seriesTypes", "clamp", "SolidGaugeSeries_extend", "isNumber", "SolidGaugeSeries_merge", "pick", "pInt", "SolidGaugeSeries", "translate", "yAxis", "drawPoints", "thresholdAngleRad", "series", "center", "renderer", "overshoot", "rounded", "borderRadius", "overshootVal", "Math", "PI", "threshold", "startAngleRad", "points", "isNull", "innerRadius", "axisMinAngle", "endAngleRad", "axisMaxAngle", "graphic", "rotation", "y", "shapeArgs", "className", "getClassName", "wrap", "angleOfRounding", "start", "end", "x", "r", "innerR", "startR", "animate", "fill", "arc", "attr", "add", "group", "replace", "trim", "linecap", "stroke", "borderColor", "borderWidth", "addClass", "colorByPoint", "dataLabels", "registerSeriesType"], "mappings": "CAYA,AAZA;;;;;;;;;;;CAWC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,KAAQ,EAC1G,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,iCAAkC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAACA,EAAK,KAAQ,CAAE,GACxI,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,iCAAiC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,KAAQ,EAE5IA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,KAAQ,CACpH,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,IACvG,AAAC,CAAA,KACP,aACA,IAkfNC,EAlfUC,EAAuB,CAE/B,IACC,AAACT,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIK,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAad,OAAO,CAG5B,IAAIC,EAASU,CAAwB,CAACE,EAAS,CAAG,CAGjDb,QAAS,CAAC,CACX,EAMA,OAHAU,CAAmB,CAACG,EAAS,CAACZ,EAAQA,EAAOD,OAAO,CAAEY,GAG/CX,EAAOD,OAAO,AACtB,CAMCY,EAAoBI,CAAC,CAAG,AAACf,IACxB,IAAIgB,EAAShB,GAAUA,EAAOiB,UAAU,CACvC,IAAOjB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAW,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACnB,EAASqB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACvB,EAASsB,IAC5EE,OAAOC,cAAc,CAACzB,EAASsB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAerH,GAAM,CAAEE,eAAAA,CAAc,CAAE,CAAID,IAEtB,CAAEE,KAAAA,CAAI,CAAE,CAAIF,IAEZ,CAAEG,SAAAA,CAAQ,CAAEC,OAAAA,CAAM,CAAEC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,eAAAA,CAAc,CAAE,CAAIP,IAMzDQ,EAA6B,CAC/BC,OAAQ,EACRC,MAAO,QACPC,MAAO,KAAK,CAChB,EAuMA,SAASC,EAAgBC,CAAO,CAAEC,CAAe,EAI7C,OAHI,AAACT,EAASQ,IACVA,CAAAA,EAAU,CAAEJ,OAAQI,GAAW,CAAE,CAAA,EAE9BP,EAAME,EAA4BM,EAAiBD,EAC9D,CA0G6B,IAAME,EAJd,CAEjBH,gBAAAA,CACJ,EA8CA,IAAII,EAAmIxC,EAAoB,KACvJyC,EAAuJzC,EAAoBI,CAAC,CAACoC,GAE7KE,EAA+F1C,EAAoB,KAcvH,GAAM,CAAE2C,MAAOC,CAAK,CAAE,CAAIC,AAb6F7C,EAAoBI,CAAC,CAACsC,KAevI,CAAEZ,MAAOgB,CAAmB,CAAE,CAAItB,KAOxC,AAAC,SAAU3B,CAAa,EA8CpBA,EAAckD,eAAe,CA/B7B,SAAyBC,CAAW,EAChC,IAAmBC,EAAQC,AAAd,IAAI,CAAeD,KAAK,CAAEE,EAAaD,AAAvC,IAAI,CAAwCC,UAAU,CAAGD,AAAzD,IAAI,CAA0DC,UAAU,EAAI,CAAC,EAAGd,EAAUa,AAA1F,IAAI,CAA2Fb,OAAO,CAAEe,EAAkBJ,EAAYK,WAAW,EAAI,EAAE,CAChKC,EAAWD,EAAaE,EAAaN,EAAMZ,OAAO,CAACY,KAAK,CAACM,UAAU,CAAEC,EAAe,EAAGC,CAC3FP,CAFa,IAAI,CAEZG,WAAW,CAAGA,EAAc,EAAE,CACnCF,EAAWO,MAAM,CAAG,EAAE,CACtB,IAAK,IAAIC,EAAI,EAAGC,EAAOR,EAAgBS,MAAM,CAAEF,EAAIC,EAAM,EAAED,EAEvDL,EAAYR,EADZQ,EAAYF,CAAe,CAACO,EAAE,EAE9BN,EAAYS,IAAI,CAACR,GACb,CAAA,AAACL,EAAMc,UAAU,GAAIT,EAAUV,KAAK,AAAD,IAGnCP,AAA2B,aAA3BA,EAAQ2B,cAAc,EACjBf,EAAMc,UAAU,GAEjBR,EAAaE,AADbA,CAAAA,EAASR,EAAMZ,OAAO,CAACoB,MAAM,EAAI,EAAE,AAAD,EACdI,MAAM,CAC1BP,EAAUV,KAAK,CAAGa,CAAM,CAACD,EAAa,EAE1CF,EAAUW,UAAU,CAAGT,EAGnBA,EAAAA,IAAiBD,GACjBC,CAAAA,EAAe,CAAA,GAInBF,EAAUV,KAAK,CAAGA,EAAMP,EAAQ6B,QAAQ,EAAEC,OAAO,CAACvB,EAAMP,EAAQ+B,QAAQ,EAAGR,EAAO,EAAI,GAAMD,EAAKC,CAAAA,EAAO,CAAA,GAIpH,EAeA/D,EAAcwE,SAAS,CATvB,WACI,IAAmBhC,EAAUa,AAAhB,IAAI,CAAiBb,OAAO,CAAEiC,EAAQpB,AAAtC,IAAI,CAAuCoB,KAAK,CAAGjC,EAAQiC,KAAK,EAAI,CAC7E,CAAC,EAAGjC,EAAQ6B,QAAQ,EAAI,GAAG,CAC3B,CAAC,EAAG7B,EAAQ+B,QAAQ,EAAI,GAAG,CAC9B,CACD,IAAK,IAAIT,EAAI,EAAGC,EAAOU,EAAMT,MAAM,CAAEF,EAAIC,EAAM,EAAED,EAC7CW,CAAK,CAACX,EAAE,CAACf,KAAK,CAAGA,EAAM0B,CAAK,CAACX,EAAE,CAAC,EAAE,CAE1C,EAcA9D,EAAc0E,eAAe,CAR7B,SAAyBC,CAAK,EAC1B,IAAmBC,EAAMvB,AAAZ,IAAI,CAAauB,GAAG,EAAI,EAAGC,EAAMxB,AAAjC,IAAI,CAAkCwB,GAAG,EAAI,EAI1D,OAHIxB,AADS,IAAI,CACRyB,WAAW,EAChBH,CAAAA,EAAQtB,AAFC,IAAI,CAEAyB,WAAW,CAACC,OAAO,CAACJ,EAAK,EAEnC,EAAK,AAACC,CAAAA,EAAMD,CAAI,EAClB,CAAA,AAACC,EAAMC,GAAQ,CAAA,CACxB,EA4CA7E,EAAcgF,OAAO,CAtCrB,SAAiBL,CAAK,CAAEM,CAAK,EAEzB,IAEIC,EAAKC,EAAMC,EAAIrC,EAAOU,EAAWK,EAF/BN,EAAcH,AADP,IAAI,CACQG,WAAW,CAC9BiB,EAAQpB,AAFD,IAAI,CAEEoB,KAAK,CAExB,GAAIjB,EAEA,CAAA,IADAM,EAAIN,EAAYQ,MAAM,CACfF,KAIH,GAFAqB,EAAO1B,AADPA,CAAAA,EAAYD,CAAW,CAACM,EAAE,AAAD,EACRqB,IAAI,CACrBC,EAAK3B,EAAU2B,EAAE,CACb,AAAC,CAAA,AAAgB,KAAA,IAATD,GAAwBR,GAASQ,CAAG,GAC3C,CAAA,AAAc,KAAA,IAAPC,GAAsBT,GAASS,CAAC,EAAI,CAC5CrC,EAAQU,EAAUV,KAAK,CACnBkC,IACAA,EAAMxB,SAAS,CAAGK,EAClBmB,EAAMb,UAAU,CAAGX,EAAUW,UAAU,EAE3C,KACJ,CACJ,KAEC,CAGD,IAFAc,EAAM7B,AAtBG,IAAI,CAsBFqB,eAAe,CAACC,GAC3Bb,EAAIW,EAAMT,MAAM,CACTF,MACCoB,CAAAA,EAAMT,CAAK,CAACX,EAAE,CAAC,EAAE,AAAD,IAIxBqB,EAAOV,CAAK,CAACX,EAAE,EAAIW,CAAK,CAACX,EAAI,EAAE,CAG/BoB,EAAM,EAAI,AAACE,CAAAA,AAFXA,CAAAA,EAAKX,CAAK,CAACX,EAAI,EAAE,EAAIqB,CAAG,CAEX,CAAC,EAAE,CAAGD,CAAE,EAAM,CAAA,AAACE,CAAE,CAAC,EAAE,CAAGD,CAAI,CAAC,EAAE,EAAK,CAAA,EAChDpC,EAAQoC,EAAKpC,KAAK,CAACuB,OAAO,CAACc,EAAGrC,KAAK,CAAEmC,EACzC,CACA,OAAOnC,CACX,CAEJ,EAAG/C,GAAkBA,CAAAA,EAAgB,CAAC,CAAA,GAMT,IAAMqF,EAAuBrF,EAepD,CAAE+B,OAAQuD,CAAqB,CAAE,CAAI3D,IAoBR4D,EAHZ,CACnBC,KATJ,SAAcnC,CAAI,EACdiC,EAAsBjC,EAAMgC,EAChC,CAQA,EA+NM,CAAEI,MAAOC,CAAW,CAAEC,IAAKC,CAAS,CAAE,CAAG,AAAChD,IAA2IiD,WAAW,CAIhM,CAAEC,MAAAA,CAAK,CAAE/D,OAAQgE,CAAuB,CAAEC,SAAAA,CAAQ,CAAE/D,MAAOgE,CAAsB,CAAEC,KAAAA,CAAI,CAAEC,KAAAA,CAAI,CAAE,CAAIxE,GAezG,OAAMyE,UAAyBV,EAQ3BW,WAAY,CACR,IAAMhD,EAAO,IAAI,CAACiD,KAAK,CACvBf,EAAoBC,IAAI,CAACnC,GAErB,CAACA,EAAKG,WAAW,EAAIH,EAAKb,OAAO,CAACgB,WAAW,EAC7CH,EAAKH,eAAe,CAACG,EAAKb,OAAO,EAErCa,EAAKmB,SAAS,GAEdkB,EAAYrE,SAAS,CAACgF,SAAS,CAAC9E,IAAI,CAAC,IAAI,CAC7C,CAEAgF,YAAa,CACT,IAGIC,EAHiBF,EAAQG,AAAd,IAAI,CAAiBH,KAAK,CAAEI,EAASJ,EAAMI,MAAM,CAAElE,EAAUiE,AAA7D,IAAI,CAAgEjE,OAAO,CAAEmE,EAAWF,AAAxF,IAAI,CAA2FrD,KAAK,CAACuD,QAAQ,CAAEC,EAAYpE,EAAQoE,SAAS,CAAEC,EAAUrE,EAAQqE,OAAO,EAAIrE,AAAyB,KAAK,IAA9BA,EAAQsE,YAAY,CAAaC,EAAef,EAASY,GAC/OA,EAAY,IAAMI,KAAKC,EAAE,CACzB,EAOJ,IAAK,IAAMhC,KAJPe,EAASxD,EAAQ0E,SAAS,GAC1BV,CAAAA,EAAoBF,EAAMa,aAAa,CAAGb,EAAMD,SAAS,CAAC7D,EAAQ0E,SAAS,CAAE,KAAK,EAAG,KAAK,EAAG,KAAK,EAAG,CAAA,EAAI,EAE7G,IAAI,CAACV,iBAAiB,CAAGN,EAAKM,EAAmBF,EAAMa,aAAa,EAChDV,AATL,IAAI,CASQW,MAAM,EAE7B,GAAI,CAACnC,EAAMoC,MAAM,CAAE,CACf,IAAMjF,EAAU,AAAC+D,EAAKD,EAAKjB,EAAMzC,OAAO,CAACJ,MAAM,CAAEI,EAAQJ,MAAM,CAAE,MAC5DsE,CAAM,CAAC,EAAE,CAAI,IAAMY,EAAe,AAACnB,EAAKD,EAAKjB,EAAMzC,OAAO,CAAC8E,WAAW,CAAE9E,EAAQ8E,WAAW,CAAE,KAC7FZ,CAAM,CAAC,EAAE,CAAI,IAAMa,EAAeP,KAAKnC,GAAG,CAACyB,EAAMa,aAAa,CAAEb,EAAMkB,WAAW,EAAGC,EAAeT,KAAKpC,GAAG,CAAC0B,EAAMa,aAAa,CAAEb,EAAMkB,WAAW,EACnJE,EAAUzC,EAAMyC,OAAO,CAAEC,EAAYrB,EAAMa,aAAa,CACxDb,EAAMD,SAAS,CAACpB,EAAM2C,CAAC,CAAE,KAAK,EAAG,KAAK,EAAG,KAAK,EAAG,CAAA,GAAQC,EAAWnH,EAAGsE,EAAUsB,EAAMtB,OAAO,CAACC,EAAM2C,CAAC,CAAE3C,GAAQ6C,EAAY7C,EAAM8C,YAAY,EAC9I/C,AAAY,CAAA,SAAZA,GACAA,CAAAA,EAAUC,EAAMlC,KAAK,EAAI0D,AAlBtB,IAAI,CAkByB1D,KAAK,EAAI,MAAK,EAE9CiC,AAAY,SAAZA,GACAC,CAAAA,EAAMlC,KAAK,CAAGiC,CAAM,EAGxB2C,EAAW7B,EAAM6B,EAAUJ,EAAeR,EAAcU,EAAeV,GAEnEvE,AAAiB,CAAA,IAAjBA,EAAQwF,IAAI,EACZL,CAAAA,EAAW7B,EAAM6B,EAAUJ,EAAcE,EAAY,EAEzD,IAAMQ,EAAkBpB,EACpB,AAAEzE,CAAAA,EAASkF,CAAU,EAAK,EAAKlF,EAC/B,EAAG8F,EAAQlB,KAAKnC,GAAG,CAAC8C,EAAUlB,AA/B3B,IAAI,CA+B8BD,iBAAiB,EACtDyB,EACAE,EAAMnB,KAAKpC,GAAG,CAAC+C,EAAUlB,AAjCtB,IAAI,CAiCyBD,iBAAiB,EACjDyB,CACAE,CAAAA,EAAMD,EAAQ,EAAIlB,KAAKC,EAAE,EACzBkB,CAAAA,EAAMD,EAAQ,EAAIlB,KAAKC,EAAE,AAAD,EAE5B,IAAIH,EAAeD,EAAU,MAAQ,CACjCrE,CAAAA,EAAQsE,YAAY,EACpBA,CAAAA,EAAepE,EAAwBH,eAAe,CAACC,EAAQsE,YAAY,EAAE1E,MAAM,AAAD,EAEtF6C,EAAM4C,SAAS,CAAGA,EAAY,CAC1BO,EAAG1B,CAAM,CAAC,EAAE,CACZkB,EAAGlB,CAAM,CAAC,EAAE,CACZ2B,EAAGjG,EACHkG,OAAQhB,EACRY,MAAAA,EACAC,IAAAA,EACArB,aAAAA,CACJ,EACA7B,EAAMsD,MAAM,CAAGnG,EACXsF,GACAhH,EAAImH,EAAUnH,CAAC,CACfgH,EAAQc,OAAO,CAACzC,EAAwB,CAAE0C,KAAMzD,CAAQ,EAAG6C,IACvDnH,GACAmH,CAAAA,EAAUnH,CAAC,CAAGA,CAAAA,GAIlBuE,EAAMyC,OAAO,CAAGA,EAAUf,EAAS+B,GAAG,CAACb,GAClCc,IAAI,CAAC,CACNF,KAAMzD,EACN,aAAc,CAClB,GACK4D,GAAG,CAACnC,AAjEN,IAAI,CAiESoC,KAAK,EAEpBpC,AAnEE,IAAI,CAmECrD,KAAK,CAACc,UAAU,CAYnBuC,AA/EF,IAAI,CA+EKH,KAAK,EAAE7B,OACnBqD,CAAAA,EAAYA,EACPgB,OAAO,CAAC,wBAAyB,IACjCC,IAAI,EAAC,GAdNvG,AAAoB,WAApBA,EAAQwG,OAAO,EACftB,EAAQiB,IAAI,CAAC,CACT,iBAAkB,QAClB,kBAAmB,OACvB,GAEJjB,EAAQiB,IAAI,CAAC,CACTM,OAAQzG,EAAQ0G,WAAW,EAAI,OAC/B,eAAgB1G,EAAQ2G,WAAW,EAAI,CAC3C,IAOAzB,GACAA,EAAQ0B,QAAQ,CAACtB,EAEzB,CAER,CAEAU,QAAQhD,CAAI,CAAE,CACLA,IACD,IAAI,CAAC2B,aAAa,CAAG,IAAI,CAACX,iBAAiB,CAC3CZ,EAAUvE,SAAS,CAACmH,OAAO,CAACjH,IAAI,CAAC,IAAI,CAAEiE,GAE/C,CACJ,CAMAY,EAAiBxE,cAAc,CAAGqE,EAAuBP,EAAY9D,cAAc,CA5UlD,CAkF7ByH,aAAc,CAAA,EACdC,WAAY,CACR1B,EAAG,CACP,CACJ,GAuPAhF,IAA0I2G,kBAAkB,CAAC,aAAcnD,GAa9I,IAAM3E,EAAoBE,IAG7C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}