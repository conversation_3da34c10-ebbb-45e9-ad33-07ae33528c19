!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/mouse-wheel-zoom
 * @requires highcharts
 *
 * Non-cartesian series zoom module
 *
 * (c) 2024 <PERSON>
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts):"function"==typeof define&&define.amd?define("highcharts/modules/non-cartesian-zoom",["highcharts/highcharts"],function(t){return e(t)}):"object"==typeof exports?exports["highcharts/modules/non-cartesian-zoom"]=e(t._Highcharts):t.Highcharts=e(t.Highcharts)}("undefined"==typeof window?this:window,t=>(()=>{"use strict";var e={944:e=>{e.exports=t}},o={};function i(t){var s=o[t];if(void 0!==s)return s.exports;var n=o[t]={exports:{}};return e[t](n,n.exports,i),n.exports}i.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return i.d(e,{a:e}),e},i.d=(t,e)=>{for(var o in e)i.o(e,o)&&!i.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var s={};i.d(s,{default:()=>z});var n=i(944),a=i.n(n);let{composed:r}=a(),{addEvent:h,pushUnique:p}=a();function l(t){let e=this,{trigger:o,selection:i,reset:s,from:n={},to:a={}}=t;"xy"===e.zooming.type&&("mousewheel"===o||"pan"===o||i||s)&&e.series.forEach(i=>{if(!i.isCartesian&&!1!==i.options.zoomEnabled){i.isDirty=!0,e.isDirtyBox=!0,t.hasZoomed=!0;let{plotSizeX:s=0,plotSizeY:r=0}=e;if("pan"===o&&i.zooming)i.zooming.panX-=(a.x||0)/s,i.zooming.panY-=(a.y||0)/r;else if(Object.keys(n).length){let{width:t=1,height:o=1}=a,h=Math.abs(i.group?.scaleX||1),{x:p=0,y:l=0,width:c=1,height:m=1}=n,g=p,f=l,d=i.zooming?.scale||i.group?.scaleX||1,u=(i.zooming?.width||1)*s,z=(i.zooming?.height||1)*r;if(Object.keys(a).length)u=c/t*u,z=c/o*z,p-=e.plotLeft,l-=e.plotTop,g=p-u/2,f=l-z/2,d=Math.min(s/u,r/z);else{c/=h,m/=h,d=Math.min(s/c,r/m);let t=0,o=0;i.zooming&&(t=i.zooming.x*s,o=i.zooming.y*r);let n=(p-e.plotLeft)/(s-c*h||1),a=(l-e.plotTop)/(r-m*h||1);u=c,z=m,p-=e.plotLeft,l-=e.plotTop,p/=h,l/=h,p+=t+c*n,l+=o+m*a,g-=e.plotLeft,f-=e.plotTop,g/=h,f/=h,g+=t,f+=o}i.zooming={x:g/s,y:f/r,zoomX:p/s,zoomY:l/r,width:u/s,height:z/r,scale:d,panX:0,panY:0},d<1&&delete i.zooming}else delete i.zooming}})}function c(t){let{chart:e,group:o,zooming:i}=this,{plotSizeX:s=0,plotSizeY:n=0}=e,{scale:a,translateX:r,translateY:h,name:p}=t,l=0,c=0,m=r,g=h;e.inverted&&([s,n]=[n,s]),o&&i&&(a=i.scale,l=i.zoomX*s*(a-Math.abs(o.scaleX||1)),c=i.zoomY*n*(a-Math.abs(o.scaleY||1)),"series"===p&&(i.x=Math.max(0,Math.min(1-i.width,i.x+i.panX/a)),l+=i.panX*s,i.panX=0,i.y=Math.max(0,Math.min(1-i.height,i.y+i.panY/a)),c+=i.panY*n,i.panY=0),r=(o.translateX||m)-l,(h=(o.translateY||g)-c)>g?h=g:(o.translateY||g)-c<n*(1-a)+g&&(h=n*(1-a)+g),r>m?r=m:r<s*(1-a)+m&&(r=s*(1-a)+m),t.scale=a,t.translateX=r,t.translateY=h)}function m(){let t;this.series.find(t=>!!t.zooming)&&(this.zoomClipRect||(this.zoomClipRect=this.renderer.clipRect()),this.zoomClipRect.attr({x:this.plotLeft,y:this.plotTop,width:this.inverted?this.clipBox.height:this.clipBox.width,height:this.inverted?this.clipBox.width:this.clipBox.height}),t=this.zoomClipRect),this.seriesGroup?.clip(t),this.dataLabelsGroup?.clip(t)}function g(t){if(t.point.series&&!t.point.series.isCartesian&&t.point.series.group&&t.point.series.zooming){let e=t.point.series.chart,o=t.point.series.zooming.scale,i=t.point.series.group.translateX||0,s=t.point.series.group.translateY||0;t.ret[0]=t.ret[0]*o+i-e.plotLeft,t.ret[1]=t.ret[1]*o+s-e.plotTop}}function f(t){t.skipAxes&&this.series.forEach(t=>{t.group&&t.zooming&&t.group.attr({translateX:0,translateY:0,scaleX:1,scaleY:1})})}let d=class{static compose(t,e,o){p(r,"NonCartesianSeriesZoom")&&(h(t,"afterDrawChartBox",m),h(t,"transform",l),h(t,"afterSetChartSize",f),h(e,"getPlotBox",c),h(o,"getAnchor",g))}},u=a();u.NonCartesianSeriesZoom=u.NonCartesianSeriesZoom||d,u.NonCartesianSeriesZoom.compose(u.Chart,u.Series,u.Tooltip);let z=a();return s.default})());