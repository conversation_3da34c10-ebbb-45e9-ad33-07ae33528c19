{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts Gantt JS v12.3.0 (2025-06-21)\n * @module highcharts/modules/pathfinder\n * @requires highcharts\n *\n * Pathfinder\n *\n * (c) 2016-2025 Øystein Moseng\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Point\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/pathfinder\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"Point\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/pathfinder\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Point\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Point\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__260__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 260:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__260__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ pathfinder_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Gantt/Connection.js\n/* *\n *\n *  (c) 2016 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { defined, error, merge, objectEach } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\nconst deg2rad = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).deg2rad, max = Math.max, min = Math.min;\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Connection class. Used internally to represent a connection between two\n * points.\n *\n * @private\n * @class\n * @name Highcharts.Connection\n *\n * @param {Highcharts.Point} from\n *        Connection runs from this Point.\n *\n * @param {Highcharts.Point} to\n *        Connection runs to this Point.\n *\n * @param {Highcharts.ConnectorsOptions} [options]\n *        Connection options.\n */\nclass Connection {\n    constructor(from, to, options) {\n        this.init(from, to, options);\n    }\n    /**\n     * Initialize the Connection object. Used as constructor only.\n     *\n     * @function Highcharts.Connection#init\n     *\n     * @param {Highcharts.Point} from\n     *        Connection runs from this Point.\n     *\n     * @param {Highcharts.Point} to\n     *        Connection runs to this Point.\n     *\n     * @param {Highcharts.ConnectorsOptions} [options]\n     *        Connection options.\n     */\n    init(from, to, options) {\n        this.fromPoint = from;\n        this.toPoint = to;\n        this.options = options;\n        this.chart = from.series.chart;\n        this.pathfinder = this.chart.pathfinder;\n    }\n    /**\n     * Add (or update) this connection's path on chart. Stores reference to the\n     * created element on this.graphics.path.\n     *\n     * @function Highcharts.Connection#renderPath\n     *\n     * @param {Highcharts.SVGPathArray} path\n     *        Path to render, in array format. E.g. ['M', 0, 0, 'L', 10, 10]\n     *\n     * @param {Highcharts.SVGAttributes} [attribs]\n     *        SVG attributes for the path.\n     *\n     * @param {Partial<Highcharts.AnimationOptionsObject>} [animation]\n     *        Animation options for the rendering.\n     */\n    renderPath(path, attribs) {\n        const connection = this, chart = this.chart, styledMode = chart.styledMode, pathfinder = this.pathfinder, anim = {};\n        let pathGraphic = connection.graphics && connection.graphics.path;\n        // Add the SVG element of the pathfinder group if it doesn't exist\n        if (!pathfinder.group) {\n            pathfinder.group = chart.renderer.g()\n                .addClass('highcharts-pathfinder-group')\n                .attr({ zIndex: -1 })\n                .add(chart.seriesGroup);\n        }\n        // Shift the group to compensate for plot area.\n        // Note: Do this always (even when redrawing a path) to avoid issues\n        // when updating chart in a way that changes plot metrics.\n        pathfinder.group.translate(chart.plotLeft, chart.plotTop);\n        // Create path if does not exist\n        if (!(pathGraphic && pathGraphic.renderer)) {\n            pathGraphic = chart.renderer.path()\n                .add(pathfinder.group);\n            if (!styledMode) {\n                pathGraphic.attr({\n                    opacity: 0\n                });\n            }\n        }\n        // Set path attribs and animate to the new path\n        pathGraphic.attr(attribs);\n        anim.d = path;\n        if (!styledMode) {\n            anim.opacity = 1;\n        }\n        pathGraphic.animate(anim);\n        // Store reference on connection\n        this.graphics = this.graphics || {};\n        this.graphics.path = pathGraphic;\n    }\n    /**\n     * Calculate and add marker graphics for connection to the chart. The\n     * created/updated elements are stored on this.graphics.start and\n     * this.graphics.end.\n     *\n     * @function Highcharts.Connection#addMarker\n     *\n     * @param {string} type\n     *        Marker type, either 'start' or 'end'.\n     *\n     * @param {Highcharts.ConnectorsMarkerOptions} options\n     *        All options for this marker. Not calculated or merged with other\n     *        options.\n     *\n     * @param {Highcharts.SVGPathArray} path\n     *        Connection path in array format. This is used to calculate the\n     *        rotation angle of the markers.\n     */\n    addMarker(type, options, path) {\n        const connection = this, chart = connection.fromPoint.series.chart, pathfinder = chart.pathfinder, renderer = chart.renderer, point = (type === 'start' ?\n            connection.fromPoint :\n            connection.toPoint), anchor = point.getPathfinderAnchorPoint(options);\n        let markerVector, radians, rotation, box, width, height, pathVector, segment;\n        if (!options.enabled) {\n            return;\n        }\n        // Last vector before start/end of path, used to get angle\n        if (type === 'start') {\n            segment = path[1];\n        }\n        else { // 'end'\n            segment = path[path.length - 2];\n        }\n        if (segment && segment[0] === 'M' || segment[0] === 'L') {\n            pathVector = {\n                x: segment[1],\n                y: segment[2]\n            };\n            // Get angle between pathVector and anchor point and use it to\n            // create marker position.\n            radians = point.getRadiansToVector(pathVector, anchor);\n            markerVector = point.getMarkerVector(radians, options.radius, anchor);\n            // Rotation of marker is calculated from angle between pathVector\n            // and markerVector.\n            // (Note:\n            //  Used to recalculate radians between markerVector and pathVector,\n            //  but this should be the same as between pathVector and anchor.)\n            rotation = -radians / deg2rad;\n            if (options.width && options.height) {\n                width = options.width;\n                height = options.height;\n            }\n            else {\n                width = height = options.radius * 2;\n            }\n            // Add graphics object if it does not exist\n            connection.graphics = connection.graphics || {};\n            box = {\n                x: markerVector.x - (width / 2),\n                y: markerVector.y - (height / 2),\n                width: width,\n                height: height,\n                rotation: rotation,\n                rotationOriginX: markerVector.x,\n                rotationOriginY: markerVector.y\n            };\n            if (!connection.graphics[type]) {\n                // Create new marker element\n                connection.graphics[type] = renderer\n                    .symbol(options.symbol)\n                    .addClass('highcharts-point-connecting-path-' + type + '-marker' +\n                    ' highcharts-color-' + this.fromPoint.colorIndex)\n                    .attr(box)\n                    .add(pathfinder.group);\n                if (!renderer.styledMode) {\n                    connection.graphics[type].attr({\n                        fill: options.color || connection.fromPoint.color,\n                        stroke: options.lineColor,\n                        'stroke-width': options.lineWidth,\n                        opacity: 0\n                    })\n                        .animate({\n                        opacity: 1\n                    }, point.series.options.animation);\n                }\n            }\n            else {\n                connection.graphics[type].animate(box);\n            }\n        }\n    }\n    /**\n     * Calculate and return connection path.\n     * Note: Recalculates chart obstacles on demand if they aren't calculated.\n     *\n     * @function Highcharts.Connection#getPath\n     *\n     * @param {Highcharts.ConnectorsOptions} options\n     *        Connector options. Not calculated or merged with other options.\n     *\n     * @return {object|undefined}\n     *         Calculated SVG path data in array format.\n     */\n    getPath(options) {\n        const pathfinder = this.pathfinder, chart = this.chart, algorithm = pathfinder.algorithms[options.type];\n        let chartObstacles = pathfinder.chartObstacles;\n        if (typeof algorithm !== 'function') {\n            error('\"' + options.type + '\" is not a Pathfinder algorithm.');\n            return {\n                path: [],\n                obstacles: []\n            };\n        }\n        // This function calculates obstacles on demand if they don't exist\n        if (algorithm.requiresObstacles && !chartObstacles) {\n            chartObstacles =\n                pathfinder.chartObstacles =\n                    pathfinder.getChartObstacles(options);\n            // If the algorithmMargin was computed, store the result in default\n            // options.\n            chart.options.connectors.algorithmMargin =\n                options.algorithmMargin;\n            // Cache some metrics too\n            pathfinder.chartObstacleMetrics =\n                pathfinder.getObstacleMetrics(chartObstacles);\n        }\n        // Get the SVG path\n        return algorithm(\n        // From\n        this.fromPoint.getPathfinderAnchorPoint(options.startMarker), \n        // To\n        this.toPoint.getPathfinderAnchorPoint(options.endMarker), merge({\n            chartObstacles: chartObstacles,\n            lineObstacles: pathfinder.lineObstacles || [],\n            obstacleMetrics: pathfinder.chartObstacleMetrics,\n            hardBounds: {\n                xMin: 0,\n                xMax: chart.plotWidth,\n                yMin: 0,\n                yMax: chart.plotHeight\n            },\n            obstacleOptions: {\n                margin: options.algorithmMargin\n            },\n            startDirectionX: pathfinder.getAlgorithmStartDirection(options.startMarker)\n        }, options));\n    }\n    /**\n     * (re)Calculate and (re)draw the connection.\n     *\n     * @function Highcharts.Connection#render\n     */\n    render() {\n        const connection = this, fromPoint = connection.fromPoint, series = fromPoint.series, chart = series.chart, pathfinder = chart.pathfinder, attribs = {};\n        let options = merge(chart.options.connectors, series.options.connectors, fromPoint.options.connectors, connection.options);\n        // Set path attribs\n        if (!chart.styledMode) {\n            attribs.stroke = options.lineColor || fromPoint.color;\n            attribs['stroke-width'] = options.lineWidth;\n            if (options.dashStyle) {\n                attribs.dashstyle = options.dashStyle;\n            }\n        }\n        attribs['class'] = // eslint-disable-line dot-notation\n            'highcharts-point-connecting-path ' +\n                'highcharts-color-' + fromPoint.colorIndex;\n        options = merge(attribs, options);\n        // Set common marker options\n        if (!defined(options.marker.radius)) {\n            options.marker.radius = min(max(Math.ceil((options.algorithmMargin || 8) / 2) - 1, 1), 5);\n        }\n        // Get the path\n        const pathResult = connection.getPath(options), path = pathResult.path;\n        // Always update obstacle storage with obstacles from this path.\n        // We don't know if future calls will need this for their algorithm.\n        if (pathResult.obstacles) {\n            pathfinder.lineObstacles =\n                pathfinder.lineObstacles || [];\n            pathfinder.lineObstacles =\n                pathfinder.lineObstacles.concat(pathResult.obstacles);\n        }\n        // Add the calculated path to the pathfinder group\n        connection.renderPath(path, attribs);\n        // Render the markers\n        connection.addMarker('start', merge(options.marker, options.startMarker), path);\n        connection.addMarker('end', merge(options.marker, options.endMarker), path);\n    }\n    /**\n     * Destroy connection by destroying the added graphics elements.\n     *\n     * @function Highcharts.Connection#destroy\n     */\n    destroy() {\n        if (this.graphics) {\n            objectEach(this.graphics, function (val) {\n                val.destroy();\n            });\n            delete this.graphics;\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Gantt_Connection = (Connection);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * The default pathfinder algorithm to use for a chart. It is possible to define\n * your own algorithms by adding them to the\n * `Highcharts.Pathfinder.prototype.algorithms`\n * object before the chart has been created.\n *\n * The default algorithms are as follows:\n *\n * `straight`:      Draws a straight line between the connecting\n *                  points. Does not avoid other points when drawing.\n *\n * `simpleConnect`: Finds a path between the points using right angles\n *                  only. Takes only starting/ending points into\n *                  account, and will not avoid other points.\n *\n * `fastAvoid`:     Finds a path between the points using right angles\n *                  only. Will attempt to avoid other points, but its\n *                  focus is performance over accuracy. Works well with\n *                  less dense datasets.\n *\n * @typedef {\"fastAvoid\"|\"simpleConnect\"|\"straight\"|string} Highcharts.PathfinderTypeValue\n */\n''; // Keeps doclets above in JS file\n\n;// ./code/es-modules/Series/PathUtilities.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nconst getLinkPath = {\n    'default': getDefaultPath,\n    straight: getStraightPath,\n    curved: getCurvedPath\n};\n/**\n *\n */\nfunction getDefaultPath(pathParams) {\n    const { x1, y1, x2, y2, width = 0, inverted = false, radius, parentVisible } = pathParams;\n    const path = [\n        ['M', x1, y1],\n        ['L', x1, y1],\n        ['C', x1, y1, x1, y2, x1, y2],\n        ['L', x1, y2],\n        ['C', x1, y1, x1, y2, x1, y2],\n        ['L', x1, y2]\n    ];\n    return parentVisible ?\n        applyRadius([\n            ['M', x1, y1],\n            ['L', x1 + width * (inverted ? -0.5 : 0.5), y1],\n            ['L', x1 + width * (inverted ? -0.5 : 0.5), y2],\n            ['L', x2, y2]\n        ], radius) :\n        path;\n}\n/**\n *\n */\nfunction getStraightPath(pathParams) {\n    const { x1, y1, x2, y2, width = 0, inverted = false, parentVisible } = pathParams;\n    return parentVisible ? [\n        ['M', x1, y1],\n        ['L', x1 + width * (inverted ? -1 : 1), y2],\n        ['L', x2, y2]\n    ] : [\n        ['M', x1, y1],\n        ['L', x1, y2],\n        ['L', x1, y2]\n    ];\n}\n/**\n *\n */\nfunction getCurvedPath(pathParams) {\n    const { x1, y1, x2, y2, offset = 0, width = 0, inverted = false, parentVisible } = pathParams;\n    return parentVisible ?\n        [\n            ['M', x1, y1],\n            [\n                'C',\n                x1 + offset,\n                y1,\n                x1 - offset + width * (inverted ? -1 : 1),\n                y2,\n                x1 + width * (inverted ? -1 : 1),\n                y2\n            ],\n            ['L', x2, y2]\n        ] :\n        [\n            ['M', x1, y1],\n            ['C', x1, y1, x1, y2, x1, y2],\n            ['L', x2, y2]\n        ];\n}\n/**\n * General function to apply corner radius to a path\n * @private\n */\nfunction applyRadius(path, r) {\n    const d = [];\n    for (let i = 0; i < path.length; i++) {\n        const x = path[i][1];\n        const y = path[i][2];\n        if (typeof x === 'number' && typeof y === 'number') {\n            // MoveTo\n            if (i === 0) {\n                d.push(['M', x, y]);\n            }\n            else if (i === path.length - 1) {\n                d.push(['L', x, y]);\n                // CurveTo\n            }\n            else if (r) {\n                const prevSeg = path[i - 1];\n                const nextSeg = path[i + 1];\n                if (prevSeg && nextSeg) {\n                    const x1 = prevSeg[1], y1 = prevSeg[2], x2 = nextSeg[1], y2 = nextSeg[2];\n                    // Only apply to breaks\n                    if (typeof x1 === 'number' &&\n                        typeof x2 === 'number' &&\n                        typeof y1 === 'number' &&\n                        typeof y2 === 'number' &&\n                        x1 !== x2 &&\n                        y1 !== y2) {\n                        const directionX = x1 < x2 ? 1 : -1, directionY = y1 < y2 ? 1 : -1;\n                        d.push([\n                            'L',\n                            x - directionX * Math.min(Math.abs(x - x1), r),\n                            y - directionY * Math.min(Math.abs(y - y1), r)\n                        ], [\n                            'C',\n                            x,\n                            y,\n                            x,\n                            y,\n                            x + directionX * Math.min(Math.abs(x - x2), r),\n                            y + directionY * Math.min(Math.abs(y - y2), r)\n                        ]);\n                    }\n                }\n                // LineTo\n            }\n            else {\n                d.push(['L', x, y]);\n            }\n        }\n    }\n    return d;\n}\nconst PathUtilities = {\n    applyRadius,\n    getLinkPath\n};\n/* harmony default export */ const Series_PathUtilities = (PathUtilities);\n\n;// ./code/es-modules/Gantt/PathfinderAlgorithms.js\n/* *\n *\n *  (c) 2016 Highsoft AS\n *  Author: Øystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\nconst { min: PathfinderAlgorithms_min, max: PathfinderAlgorithms_max, abs } = Math;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Get index of last obstacle before xMin. Employs a type of binary search, and\n * thus requires that obstacles are sorted by xMin value.\n *\n * @private\n * @function findLastObstacleBefore\n *\n * @param {Array<object>} obstacles\n *        Array of obstacles to search in.\n *\n * @param {number} xMin\n *        The xMin threshold.\n *\n * @param {number} [startIx]\n *        Starting index to search from. Must be within array range.\n *\n * @return {number}\n *         The index of the last obstacle element before xMin.\n */\nfunction findLastObstacleBefore(obstacles, xMin, startIx) {\n    const min = xMin - 0.0000001; // Make sure we include all obstacles at xMin\n    let left = startIx || 0, // Left limit\n    right = obstacles.length - 1, // Right limit\n    cursor, cmp;\n    while (left <= right) {\n        cursor = (right + left) >> 1;\n        cmp = min - obstacles[cursor].xMin;\n        if (cmp > 0) {\n            left = cursor + 1;\n        }\n        else if (cmp < 0) {\n            right = cursor - 1;\n        }\n        else {\n            return cursor;\n        }\n    }\n    return left > 0 ? left - 1 : 0;\n}\n/**\n * Test if a point lays within an obstacle.\n *\n * @private\n * @function pointWithinObstacle\n *\n * @param {Object} obstacle\n *        Obstacle to test.\n *\n * @param {Highcharts.Point} point\n *        Point with x/y props.\n *\n * @return {boolean}\n *         Whether point is within the obstacle or not.\n */\nfunction pointWithinObstacle(obstacle, point) {\n    return (point.x <= obstacle.xMax &&\n        point.x >= obstacle.xMin &&\n        point.y <= obstacle.yMax &&\n        point.y >= obstacle.yMin);\n}\n/**\n * Find the index of an obstacle that wraps around a point.\n * Returns -1 if not found.\n *\n * @private\n * @function findObstacleFromPoint\n *\n * @param {Array<object>} obstacles\n *        Obstacles to test.\n *\n * @param {Highcharts.Point} point\n *        Point with x/y props.\n *\n * @return {number}\n *         Ix of the obstacle in the array, or -1 if not found.\n */\nfunction findObstacleFromPoint(obstacles, point) {\n    let i = findLastObstacleBefore(obstacles, point.x + 1) + 1;\n    while (i--) {\n        if (obstacles[i].xMax >= point.x &&\n            // Optimization using lazy evaluation\n            pointWithinObstacle(obstacles[i], point)) {\n            return i;\n        }\n    }\n    return -1;\n}\n/**\n * Get SVG path array from array of line segments.\n *\n * @private\n * @function pathFromSegments\n *\n * @param {Array<object>} segments\n *        The segments to build the path from.\n *\n * @return {Highcharts.SVGPathArray}\n *         SVG path array as accepted by the SVG Renderer.\n */\nfunction pathFromSegments(segments) {\n    const path = [];\n    if (segments.length) {\n        path.push(['M', segments[0].start.x, segments[0].start.y]);\n        for (let i = 0; i < segments.length; ++i) {\n            path.push(['L', segments[i].end.x, segments[i].end.y]);\n        }\n    }\n    return path;\n}\n/**\n * Limits obstacle max/mins in all directions to bounds. Modifies input\n * obstacle.\n *\n * @private\n * @function limitObstacleToBounds\n *\n * @param {Object} obstacle\n *        Obstacle to limit.\n *\n * @param {Object} bounds\n *        Bounds to use as limit.\n *\n * @return {void}\n */\nfunction limitObstacleToBounds(obstacle, bounds) {\n    obstacle.yMin = PathfinderAlgorithms_max(obstacle.yMin, bounds.yMin);\n    obstacle.yMax = PathfinderAlgorithms_min(obstacle.yMax, bounds.yMax);\n    obstacle.xMin = PathfinderAlgorithms_max(obstacle.xMin, bounds.xMin);\n    obstacle.xMax = PathfinderAlgorithms_min(obstacle.xMax, bounds.xMax);\n}\n/**\n * Get an SVG path from a starting coordinate to an ending coordinate.\n * Draws a straight line.\n *\n * @function Highcharts.Pathfinder.algorithms.straight\n *\n * @param {Highcharts.PositionObject} start\n *        Starting coordinate, object with x/y props.\n *\n * @param {Highcharts.PositionObject} end\n *        Ending coordinate, object with x/y props.\n *\n * @return {Object}\n *         An object with the SVG path in Array form as accepted by the SVG\n *         renderer, as well as an array of new obstacles making up this\n *         path.\n */\nfunction straight(start, end) {\n    return {\n        path: [\n            ['M', start.x, start.y],\n            ['L', end.x, end.y]\n        ],\n        obstacles: [{ start: start, end: end }]\n    };\n}\n/**\n * Find a path from a starting coordinate to an ending coordinate, using\n * right angles only, and taking only starting/ending obstacle into\n * consideration.\n *\n * @function Highcharts.Pathfinder.algorithms.simpleConnect\n *\n * @param {Highcharts.PositionObject} start\n *        Starting coordinate, object with x/y props.\n *\n * @param {Highcharts.PositionObject} end\n *        Ending coordinate, object with x/y props.\n *\n * @param {Object} options\n *        Options for the algorithm:\n *        - chartObstacles: Array of chart obstacles to avoid\n *        - startDirectionX: Optional. True if starting in the X direction.\n *          If not provided, the algorithm starts in the direction that is\n *          the furthest between start/end.\n *\n * @return {Object}\n *         An object with the SVG path in Array form as accepted by the SVG\n *         renderer, as well as an array of new obstacles making up this\n *         path.\n */\nconst simpleConnect = function (start, end, options) {\n    const segments = [], chartObstacles = options.chartObstacles, startObstacleIx = findObstacleFromPoint(chartObstacles, start), endObstacleIx = findObstacleFromPoint(chartObstacles, end);\n    let endSegment, dir = pick(options.startDirectionX, abs(end.x - start.x) > abs(end.y - start.y)) ? 'x' : 'y', startObstacle, endObstacle, waypoint, useMax, endPoint;\n    // eslint-disable-next-line valid-jsdoc\n    /**\n     * Return a clone of a point with a property set from a target object,\n     * optionally with an offset\n     * @private\n     */\n    function copyFromPoint(from, fromKey, to, toKey, offset) {\n        const point = {\n            x: from.x,\n            y: from.y\n        };\n        point[fromKey] = to[toKey || fromKey] + (offset || 0);\n        return point;\n    }\n    // eslint-disable-next-line valid-jsdoc\n    /**\n     * Return waypoint outside obstacle.\n     * @private\n     */\n    function getMeOut(obstacle, point, direction) {\n        const useMax = abs(point[direction] - obstacle[direction + 'Min']) >\n            abs(point[direction] - obstacle[direction + 'Max']);\n        return copyFromPoint(point, direction, obstacle, direction + (useMax ? 'Max' : 'Min'), useMax ? 1 : -1);\n    }\n    // Pull out end point\n    if (endObstacleIx > -1) {\n        endObstacle = chartObstacles[endObstacleIx];\n        waypoint = getMeOut(endObstacle, end, dir);\n        endSegment = {\n            start: waypoint,\n            end: end\n        };\n        endPoint = waypoint;\n    }\n    else {\n        endPoint = end;\n    }\n    // If an obstacle envelops the start point, add a segment to get out,\n    // and around it.\n    if (startObstacleIx > -1) {\n        startObstacle = chartObstacles[startObstacleIx];\n        waypoint = getMeOut(startObstacle, start, dir);\n        segments.push({\n            start: start,\n            end: waypoint\n        });\n        // If we are going back again, switch direction to get around start\n        // obstacle.\n        if (\n        // Going towards max from start:\n        waypoint[dir] >= start[dir] ===\n            // Going towards min to end:\n            waypoint[dir] >= endPoint[dir]) {\n            dir = dir === 'y' ? 'x' : 'y';\n            useMax = start[dir] < end[dir];\n            segments.push({\n                start: waypoint,\n                end: copyFromPoint(waypoint, dir, startObstacle, dir + (useMax ? 'Max' : 'Min'), useMax ? 1 : -1)\n            });\n            // Switch direction again\n            dir = dir === 'y' ? 'x' : 'y';\n        }\n    }\n    // We are around the start obstacle. Go towards the end in one\n    // direction.\n    const prevWaypoint = segments.length ?\n        segments[segments.length - 1].end :\n        start;\n    waypoint = copyFromPoint(prevWaypoint, dir, endPoint);\n    segments.push({\n        start: prevWaypoint,\n        end: waypoint\n    });\n    // Final run to end point in the other direction\n    dir = dir === 'y' ? 'x' : 'y';\n    const waypoint2 = copyFromPoint(waypoint, dir, endPoint);\n    segments.push({\n        start: waypoint,\n        end: waypoint2\n    });\n    // Finally add the endSegment\n    segments.push(endSegment);\n    const path = Series_PathUtilities.applyRadius(pathFromSegments(segments), options.radius);\n    return {\n        path,\n        obstacles: segments\n    };\n};\nsimpleConnect.requiresObstacles = true;\n/**\n * Find a path from a starting coordinate to an ending coordinate, taking\n * obstacles into consideration. Might not always find the optimal path,\n * but is fast, and usually good enough.\n *\n * @function Highcharts.Pathfinder.algorithms.fastAvoid\n *\n * @param {Highcharts.PositionObject} start\n *        Starting coordinate, object with x/y props.\n *\n * @param {Highcharts.PositionObject} end\n *        Ending coordinate, object with x/y props.\n *\n * @param {Object} options\n *        Options for the algorithm.\n *        - chartObstacles:  Array of chart obstacles to avoid\n *        - lineObstacles:   Array of line obstacles to jump over\n *        - obstacleMetrics: Object with metrics of chartObstacles cached\n *        - hardBounds:      Hard boundaries to not cross\n *        - obstacleOptions: Options for the obstacles, including margin\n *        - startDirectionX: Optional. True if starting in the X direction.\n *                           If not provided, the algorithm starts in the\n *                           direction that is the furthest between\n *                           start/end.\n *\n * @return {Object}\n *         An object with the SVG path in Array form as accepted by the SVG\n *         renderer, as well as an array of new obstacles making up this\n *         path.\n */\nfunction fastAvoid(start, end, options) {\n    /*\n        Algorithm rules/description\n        - Find initial direction\n        - Determine soft/hard max for each direction.\n        - Move along initial direction until obstacle.\n        - Change direction.\n        - If hitting obstacle, first try to change length of previous line\n            before changing direction again.\n\n        Soft min/max x = start/destination x +/- widest obstacle + margin\n        Soft min/max y = start/destination y +/- tallest obstacle + margin\n\n        @todo:\n            - Make retrospective, try changing prev segment to reduce\n                corners\n            - Fix logic for breaking out of end-points - not always picking\n                the best direction currently\n            - When going around the end obstacle we should not always go the\n                shortest route, rather pick the one closer to the end point\n    */\n    const dirIsX = pick(options.startDirectionX, abs(end.x - start.x) > abs(end.y - start.y)), dir = dirIsX ? 'x' : 'y', endSegments = [], \n    // Boundaries to stay within. If beyond soft boundary, prefer to\n    // change direction ASAP. If at hard max, always change immediately.\n    metrics = options.obstacleMetrics, softMinX = PathfinderAlgorithms_min(start.x, end.x) - metrics.maxWidth - 10, softMaxX = PathfinderAlgorithms_max(start.x, end.x) + metrics.maxWidth + 10, softMinY = PathfinderAlgorithms_min(start.y, end.y) - metrics.maxHeight - 10, softMaxY = PathfinderAlgorithms_max(start.y, end.y) + metrics.maxHeight + 10;\n    let segments, useMax, extractedEndPoint, forceObstacleBreak = false, // Used in clearPathTo to keep track of\n    // when to force break through an obstacle.\n    // Obstacles\n    chartObstacles = options.chartObstacles, endObstacleIx = findLastObstacleBefore(chartObstacles, softMaxX);\n    const startObstacleIx = findLastObstacleBefore(chartObstacles, softMinX);\n    // eslint-disable-next-line valid-jsdoc\n    /**\n     * How far can you go between two points before hitting an obstacle?\n     * Does not work for diagonal lines (because it doesn't have to).\n     * @private\n     */\n    function pivotPoint(fromPoint, toPoint, directionIsX) {\n        const searchDirection = fromPoint.x < toPoint.x ? 1 : -1;\n        let firstPoint, lastPoint, highestPoint, lowestPoint;\n        if (fromPoint.x < toPoint.x) {\n            firstPoint = fromPoint;\n            lastPoint = toPoint;\n        }\n        else {\n            firstPoint = toPoint;\n            lastPoint = fromPoint;\n        }\n        if (fromPoint.y < toPoint.y) {\n            lowestPoint = fromPoint;\n            highestPoint = toPoint;\n        }\n        else {\n            lowestPoint = toPoint;\n            highestPoint = fromPoint;\n        }\n        // Go through obstacle range in reverse if toPoint is before\n        // fromPoint in the X-dimension.\n        let i = searchDirection < 0 ?\n            // Searching backwards, start at last obstacle before last point\n            PathfinderAlgorithms_min(findLastObstacleBefore(chartObstacles, lastPoint.x), chartObstacles.length - 1) :\n            // Forwards. Since we're not sorted by xMax, we have to look\n            // at all obstacles.\n            0;\n        // Go through obstacles in this X range\n        while (chartObstacles[i] && (searchDirection > 0 && chartObstacles[i].xMin <= lastPoint.x ||\n            searchDirection < 0 && chartObstacles[i].xMax >= firstPoint.x)) {\n            // If this obstacle is between from and to points in a straight\n            // line, pivot at the intersection.\n            if (chartObstacles[i].xMin <= lastPoint.x &&\n                chartObstacles[i].xMax >= firstPoint.x &&\n                chartObstacles[i].yMin <= highestPoint.y &&\n                chartObstacles[i].yMax >= lowestPoint.y) {\n                if (directionIsX) {\n                    return {\n                        y: fromPoint.y,\n                        x: fromPoint.x < toPoint.x ?\n                            chartObstacles[i].xMin - 1 :\n                            chartObstacles[i].xMax + 1,\n                        obstacle: chartObstacles[i]\n                    };\n                }\n                // Else ...\n                return {\n                    x: fromPoint.x,\n                    y: fromPoint.y < toPoint.y ?\n                        chartObstacles[i].yMin - 1 :\n                        chartObstacles[i].yMax + 1,\n                    obstacle: chartObstacles[i]\n                };\n            }\n            i += searchDirection;\n        }\n        return toPoint;\n    }\n    /**\n     * Decide in which direction to dodge or get out of an obstacle.\n     * Considers desired direction, which way is shortest, soft and hard\n     * bounds.\n     *\n     * (? Returns a string, either xMin, xMax, yMin or yMax.)\n     *\n     * @private\n     * @function\n     *\n     * @param {Object} obstacle\n     *        Obstacle to dodge/escape.\n     *\n     * @param {Object} fromPoint\n     *        Point with x/y props that's dodging/escaping.\n     *\n     * @param {Object} toPoint\n     *        Goal point.\n     *\n     * @param {boolean} dirIsX\n     *        Dodge in X dimension.\n     *\n     * @param {Object} bounds\n     *        Hard and soft boundaries.\n     *\n     * @return {boolean}\n     *         Use max or not.\n     */\n    function getDodgeDirection(obstacle, fromPoint, toPoint, dirIsX, bounds) {\n        const softBounds = bounds.soft, hardBounds = bounds.hard, dir = dirIsX ? 'x' : 'y', toPointMax = { x: fromPoint.x, y: fromPoint.y }, toPointMin = { x: fromPoint.x, y: fromPoint.y }, maxOutOfSoftBounds = obstacle[dir + 'Max'] >=\n            softBounds[dir + 'Max'], minOutOfSoftBounds = obstacle[dir + 'Min'] <=\n            softBounds[dir + 'Min'], maxOutOfHardBounds = obstacle[dir + 'Max'] >=\n            hardBounds[dir + 'Max'], minOutOfHardBounds = obstacle[dir + 'Min'] <=\n            hardBounds[dir + 'Min'], \n        // Find out if we should prefer one direction over the other if\n        // we can choose freely\n        minDistance = abs(obstacle[dir + 'Min'] - fromPoint[dir]), maxDistance = abs(obstacle[dir + 'Max'] - fromPoint[dir]);\n        let // If it's a small difference, pick the one leading towards dest\n        // point. Otherwise pick the shortest distance\n        useMax = abs(minDistance - maxDistance) < 10 ?\n            fromPoint[dir] < toPoint[dir] :\n            maxDistance < minDistance;\n        // Check if we hit any obstacles trying to go around in either\n        // direction.\n        toPointMin[dir] = obstacle[dir + 'Min'];\n        toPointMax[dir] = obstacle[dir + 'Max'];\n        const minPivot = pivotPoint(fromPoint, toPointMin, dirIsX)[dir] !==\n            toPointMin[dir], maxPivot = pivotPoint(fromPoint, toPointMax, dirIsX)[dir] !==\n            toPointMax[dir];\n        useMax = minPivot ?\n            (maxPivot ? useMax : true) :\n            (maxPivot ? false : useMax);\n        // `useMax` now contains our preferred choice, bounds not taken into\n        // account. If both or neither direction is out of bounds we want to\n        // use this.\n        // Deal with soft bounds\n        useMax = minOutOfSoftBounds ?\n            (maxOutOfSoftBounds ? useMax : true) : // Out on min\n            (maxOutOfSoftBounds ? false : useMax); // Not out on min\n        // Deal with hard bounds\n        useMax = minOutOfHardBounds ?\n            (maxOutOfHardBounds ? useMax : true) : // Out on min\n            (maxOutOfHardBounds ? false : useMax); // Not out on min\n        return useMax;\n    }\n    // eslint-disable-next-line valid-jsdoc\n    /**\n     * Find a clear path between point.\n     * @private\n     */\n    function clearPathTo(fromPoint, toPoint, dirIsX) {\n        // Don't waste time if we've hit goal\n        if (fromPoint.x === toPoint.x && fromPoint.y === toPoint.y) {\n            return [];\n        }\n        const dir = dirIsX ? 'x' : 'y', obstacleMargin = options.obstacleOptions.margin, bounds = {\n            soft: {\n                xMin: softMinX,\n                xMax: softMaxX,\n                yMin: softMinY,\n                yMax: softMaxY\n            },\n            hard: options.hardBounds\n        };\n        let pivot, segments, waypoint, waypointUseMax, envelopingObstacle, secondEnvelopingObstacle, envelopWaypoint;\n        // If fromPoint is inside an obstacle we have a problem. Break out\n        // by just going to the outside of this obstacle. We prefer to go to\n        // the nearest edge in the chosen direction.\n        envelopingObstacle =\n            findObstacleFromPoint(chartObstacles, fromPoint);\n        if (envelopingObstacle > -1) {\n            envelopingObstacle = chartObstacles[envelopingObstacle];\n            waypointUseMax = getDodgeDirection(envelopingObstacle, fromPoint, toPoint, dirIsX, bounds);\n            // Cut obstacle to hard bounds to make sure we stay within\n            limitObstacleToBounds(envelopingObstacle, options.hardBounds);\n            envelopWaypoint = dirIsX ? {\n                y: fromPoint.y,\n                x: envelopingObstacle[waypointUseMax ? 'xMax' : 'xMin'] +\n                    (waypointUseMax ? 1 : -1)\n            } : {\n                x: fromPoint.x,\n                y: envelopingObstacle[waypointUseMax ? 'yMax' : 'yMin'] +\n                    (waypointUseMax ? 1 : -1)\n            };\n            // If we crashed into another obstacle doing this, we put the\n            // waypoint between them instead\n            secondEnvelopingObstacle = findObstacleFromPoint(chartObstacles, envelopWaypoint);\n            if (secondEnvelopingObstacle > -1) {\n                secondEnvelopingObstacle = chartObstacles[secondEnvelopingObstacle];\n                // Cut obstacle to hard bounds\n                limitObstacleToBounds(secondEnvelopingObstacle, options.hardBounds);\n                // Modify waypoint to lay between obstacles\n                envelopWaypoint[dir] = waypointUseMax ? PathfinderAlgorithms_max(envelopingObstacle[dir + 'Max'] - obstacleMargin + 1, (secondEnvelopingObstacle[dir + 'Min'] +\n                    envelopingObstacle[dir + 'Max']) / 2) :\n                    PathfinderAlgorithms_min((envelopingObstacle[dir + 'Min'] + obstacleMargin - 1), ((secondEnvelopingObstacle[dir + 'Max'] +\n                        envelopingObstacle[dir + 'Min']) / 2));\n                // We are not going anywhere. If this happens for the first\n                // time, do nothing. Otherwise, try to go to the extreme of\n                // the obstacle pair in the current direction.\n                if (fromPoint.x === envelopWaypoint.x &&\n                    fromPoint.y === envelopWaypoint.y) {\n                    if (forceObstacleBreak) {\n                        envelopWaypoint[dir] = waypointUseMax ?\n                            PathfinderAlgorithms_max(envelopingObstacle[dir + 'Max'], secondEnvelopingObstacle[dir + 'Max']) + 1 :\n                            PathfinderAlgorithms_min(envelopingObstacle[dir + 'Min'], secondEnvelopingObstacle[dir + 'Min']) - 1;\n                    }\n                    // Toggle on if off, and the opposite\n                    forceObstacleBreak = !forceObstacleBreak;\n                }\n                else {\n                    // This point is not identical to previous.\n                    // Clear break trigger.\n                    forceObstacleBreak = false;\n                }\n            }\n            segments = [{\n                    start: fromPoint,\n                    end: envelopWaypoint\n                }];\n        }\n        else { // If not enveloping, use standard pivot calculation\n            pivot = pivotPoint(fromPoint, {\n                x: dirIsX ? toPoint.x : fromPoint.x,\n                y: dirIsX ? fromPoint.y : toPoint.y\n            }, dirIsX);\n            segments = [{\n                    start: fromPoint,\n                    end: {\n                        x: pivot.x,\n                        y: pivot.y\n                    }\n                }];\n            // Pivot before goal, use a waypoint to dodge obstacle\n            if (pivot[dirIsX ? 'x' : 'y'] !== toPoint[dirIsX ? 'x' : 'y']) {\n                // Find direction of waypoint\n                waypointUseMax = getDodgeDirection(pivot.obstacle, pivot, toPoint, !dirIsX, bounds);\n                // Cut waypoint to hard bounds\n                limitObstacleToBounds(pivot.obstacle, options.hardBounds);\n                waypoint = {\n                    x: dirIsX ?\n                        pivot.x :\n                        pivot.obstacle[waypointUseMax ? 'xMax' : 'xMin'] +\n                            (waypointUseMax ? 1 : -1),\n                    y: dirIsX ?\n                        pivot.obstacle[waypointUseMax ? 'yMax' : 'yMin'] +\n                            (waypointUseMax ? 1 : -1) :\n                        pivot.y\n                };\n                // We're changing direction here, store that to make sure we\n                // also change direction when adding the last segment array\n                // after handling waypoint.\n                dirIsX = !dirIsX;\n                segments = segments.concat(clearPathTo({\n                    x: pivot.x,\n                    y: pivot.y\n                }, waypoint, dirIsX));\n            }\n        }\n        // Get segments for the other direction too\n        // Recursion is our friend\n        segments = segments.concat(clearPathTo(segments[segments.length - 1].end, toPoint, !dirIsX));\n        return segments;\n    }\n    // eslint-disable-next-line valid-jsdoc\n    /**\n     * Extract point to outside of obstacle in whichever direction is\n     * closest. Returns new point outside obstacle.\n     * @private\n     */\n    function extractFromObstacle(obstacle, point, goalPoint) {\n        const dirIsX = PathfinderAlgorithms_min(obstacle.xMax - point.x, point.x - obstacle.xMin) <\n            PathfinderAlgorithms_min(obstacle.yMax - point.y, point.y - obstacle.yMin), bounds = {\n            soft: options.hardBounds,\n            hard: options.hardBounds\n        }, useMax = getDodgeDirection(obstacle, point, goalPoint, dirIsX, bounds);\n        return dirIsX ? {\n            y: point.y,\n            x: obstacle[useMax ? 'xMax' : 'xMin'] + (useMax ? 1 : -1)\n        } : {\n            x: point.x,\n            y: obstacle[useMax ? 'yMax' : 'yMin'] + (useMax ? 1 : -1)\n        };\n    }\n    // Cut the obstacle array to soft bounds for optimization in large\n    // datasets.\n    chartObstacles =\n        chartObstacles.slice(startObstacleIx, endObstacleIx + 1);\n    // If an obstacle envelops the end point, move it out of there and add\n    // a little segment to where it was.\n    if ((endObstacleIx = findObstacleFromPoint(chartObstacles, end)) > -1) {\n        extractedEndPoint = extractFromObstacle(chartObstacles[endObstacleIx], end, start);\n        endSegments.push({\n            end: end,\n            start: extractedEndPoint\n        });\n        end = extractedEndPoint;\n    }\n    // If it's still inside one or more obstacles, get out of there by\n    // force-moving towards the start point.\n    while ((endObstacleIx = findObstacleFromPoint(chartObstacles, end)) > -1) {\n        useMax = end[dir] - start[dir] < 0;\n        extractedEndPoint = {\n            x: end.x,\n            y: end.y\n        };\n        extractedEndPoint[dir] = chartObstacles[endObstacleIx][useMax ? dir + 'Max' : dir + 'Min'] + (useMax ? 1 : -1);\n        endSegments.push({\n            end: end,\n            start: extractedEndPoint\n        });\n        end = extractedEndPoint;\n    }\n    // Find the path\n    segments = clearPathTo(start, end, dirIsX);\n    // Add the end-point segments\n    segments = segments.concat(endSegments.reverse());\n    return {\n        path: pathFromSegments(segments),\n        obstacles: segments\n    };\n}\nfastAvoid.requiresObstacles = true;\n/* *\n *\n *  Default Export\n *\n * */\n// Define the available pathfinding algorithms.\n// Algorithms take up to 3 arguments: starting point, ending point, and an\n// options object.\nconst algorithms = {\n    fastAvoid,\n    straight,\n    simpleConnect\n};\n/* harmony default export */ const PathfinderAlgorithms = (algorithms);\n\n;// ./code/es-modules/Gantt/ConnectorsDefaults.js\n/* *\n *\n *  (c) 2016 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Defaults\n *\n * */\n/*\n @todo:\n     - Document how to write your own algorithms\n     - Consider adding a Point.pathTo method that wraps creating a connection\n       and rendering it\n*/\nconst connectorsDefaults = {\n    /**\n     * The Pathfinder module allows you to define connections between any two\n     * points, represented as lines - optionally with markers for the start\n     * and/or end points. Multiple algorithms are available for calculating how\n     * the connecting lines are drawn.\n     *\n     * Connector functionality requires Highcharts Gantt to be loaded. In Gantt\n     * charts, the connectors are used to draw dependencies between tasks.\n     *\n     * @see [dependency](series.gantt.data.dependency)\n     *\n     * @sample gantt/pathfinder/demo\n     *         Pathfinder connections\n     *\n     * @declare      Highcharts.ConnectorsOptions\n     * @product      gantt\n     * @optionparent connectors\n     */\n    connectors: {\n        /**\n         * Enable connectors for this chart. Requires Highcharts Gantt.\n         *\n         * @type      {boolean}\n         * @default   true\n         * @since     6.2.0\n         * @apioption connectors.enabled\n         */\n        /**\n         * Set the default dash style for this chart's connecting lines.\n         *\n         * @type      {string}\n         * @default   solid\n         * @since     6.2.0\n         * @apioption connectors.dashStyle\n         */\n        /**\n         * Set the default color for this chart's Pathfinder connecting lines.\n         * Defaults to the color of the point being connected.\n         *\n         * @type      {Highcharts.ColorString}\n         * @since     6.2.0\n         * @apioption connectors.lineColor\n         */\n        /**\n         * Set the default pathfinder margin to use, in pixels. Some Pathfinder\n         * algorithms attempt to avoid obstacles, such as other points in the\n         * chart. These algorithms use this margin to determine how close lines\n         * can be to an obstacle. The default is to compute this automatically\n         * from the size of the obstacles in the chart.\n         *\n         * To draw connecting lines close to existing points, set this to a low\n         * number. For more space around existing points, set this number\n         * higher.\n         *\n         * @sample gantt/pathfinder/algorithm-margin\n         *         Small algorithmMargin\n         *\n         * @type      {number}\n         * @since     6.2.0\n         * @apioption connectors.algorithmMargin\n         */\n        /**\n         * Set the default pathfinder algorithm to use for this chart. It is\n         * possible to define your own algorithms by adding them to the\n         * Highcharts.Pathfinder.prototype.algorithms object before the chart\n         * has been created.\n         *\n         * The default algorithms are as follows:\n         *\n         * `straight`:      Draws a straight line between the connecting\n         *                  points. Does not avoid other points when drawing.\n         *\n         * `simpleConnect`: Finds a path between the points using right angles\n         *                  only. Takes only starting/ending points into\n         *                  account, and will not avoid other points.\n         *\n         * `fastAvoid`:     Finds a path between the points using right angles\n         *                  only. Will attempt to avoid other points, but its\n         *                  focus is performance over accuracy. Works well with\n         *                  less dense datasets.\n         *\n         * Default value: `straight` is used as default for most series types,\n         * while `simpleConnect` is used as default for Gantt series, to show\n         * dependencies between points.\n         *\n         * @sample gantt/pathfinder/demo\n         *         Different types used\n         *\n         * @type    {Highcharts.PathfinderTypeValue}\n         * @default undefined\n         * @since   6.2.0\n         */\n        type: 'straight',\n        /**\n         * The corner radius for the connector line.\n         *\n         * @since 11.2.0\n         */\n        radius: 0,\n        /**\n         * Set the default pixel width for this chart's Pathfinder connecting\n         * lines.\n         *\n         * @since 6.2.0\n         */\n        lineWidth: 1,\n        /**\n         * Marker options for this chart's Pathfinder connectors. Note that\n         * this option is overridden by the `startMarker` and `endMarker`\n         * options.\n         *\n         * @declare Highcharts.ConnectorsMarkerOptions\n         * @since   6.2.0\n         */\n        marker: {\n            /**\n             * Set the radius of the connector markers. The default is\n             * automatically computed based on the algorithmMargin setting.\n             *\n             * Setting marker.width and marker.height will override this\n             * setting.\n             *\n             * @type      {number}\n             * @since     6.2.0\n             * @apioption connectors.marker.radius\n             */\n            /**\n             * Set the width of the connector markers. If not supplied, this\n             * is inferred from the marker radius.\n             *\n             * @type      {number}\n             * @since     6.2.0\n             * @apioption connectors.marker.width\n             */\n            /**\n             * Set the height of the connector markers. If not supplied, this\n             * is inferred from the marker radius.\n             *\n             * @type      {number}\n             * @since     6.2.0\n             * @apioption connectors.marker.height\n             */\n            /**\n             * Set the color of the connector markers. By default this is the\n             * same as the connector color.\n             *\n             * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n             * @since     6.2.0\n             * @apioption connectors.marker.color\n             */\n            /**\n             * Set the line/border color of the connector markers. By default\n             * this is the same as the marker color.\n             *\n             * @type      {Highcharts.ColorString}\n             * @since     6.2.0\n             * @apioption connectors.marker.lineColor\n             */\n            /**\n             * Enable markers for the connectors.\n             */\n            enabled: false,\n            /**\n             * Horizontal alignment of the markers relative to the points.\n             *\n             * @type {Highcharts.AlignValue}\n             */\n            align: 'center',\n            /**\n             * Vertical alignment of the markers relative to the points.\n             *\n             * @type {Highcharts.VerticalAlignValue}\n             */\n            verticalAlign: 'middle',\n            /**\n             * Whether or not to draw the markers inside the points.\n             */\n            inside: false,\n            /**\n             * Set the line/border width of the pathfinder markers.\n             */\n            lineWidth: 1\n        },\n        /**\n         * Marker options specific to the start markers for this chart's\n         * Pathfinder connectors. Overrides the generic marker options.\n         *\n         * @declare Highcharts.ConnectorsStartMarkerOptions\n         * @extends connectors.marker\n         * @since   6.2.0\n         */\n        startMarker: {\n            /**\n             * Set the symbol of the connector start markers.\n             */\n            symbol: 'diamond'\n        },\n        /**\n         * Marker options specific to the end markers for this chart's\n         * Pathfinder connectors. Overrides the generic marker options.\n         *\n         * @declare Highcharts.ConnectorsEndMarkerOptions\n         * @extends connectors.marker\n         * @since   6.2.0\n         */\n        endMarker: {\n            /**\n             * Set the symbol of the connector end markers.\n             */\n            symbol: 'arrow-filled'\n        }\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const ConnectorsDefaults = (connectorsDefaults);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Override Pathfinder connector options for a series. Requires Highcharts Gantt\n * to be loaded.\n *\n * @declare   Highcharts.SeriesConnectorsOptionsObject\n * @extends   connectors\n * @since     6.2.0\n * @excluding enabled, algorithmMargin\n * @product   gantt\n * @apioption plotOptions.series.connectors\n */\n/**\n * Connect to a point. This option can be either a string, referring to the ID\n * of another point, or an object, or an array of either. If the option is an\n * array, each element defines a connection.\n *\n * @sample gantt/pathfinder/demo\n *         Different connection types\n *\n * @declare   Highcharts.XrangePointConnectorsOptionsObject\n * @type      {string|Array<string|*>|*}\n * @extends   plotOptions.series.connectors\n * @since     6.2.0\n * @excluding enabled\n * @product   gantt\n * @requires  highcharts-gantt\n * @apioption series.xrange.data.connect\n */\n/**\n * The ID of the point to connect to.\n *\n * @type      {string}\n * @since     6.2.0\n * @product   gantt\n * @apioption series.xrange.data.connect.to\n */\n''; // Keeps doclets above in JS file\n\n;// ./code/es-modules/Gantt/PathfinderComposition.js\n/* *\n *\n *  (c) 2016 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { setOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { defined: PathfinderComposition_defined, error: PathfinderComposition_error, merge: PathfinderComposition_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Get point bounding box using plotX/plotY and shapeArgs. If using\n * graphic.getBBox() directly, the bbox will be affected by animation.\n *\n * @private\n * @function\n *\n * @param {Highcharts.Point} point\n *        The point to get BB of.\n *\n * @return {Highcharts.Dictionary<number>|null}\n *         Result xMax, xMin, yMax, yMin.\n */\nfunction getPointBB(point) {\n    const shapeArgs = point.shapeArgs;\n    // Prefer using shapeArgs (columns)\n    if (shapeArgs) {\n        return {\n            xMin: shapeArgs.x || 0,\n            xMax: (shapeArgs.x || 0) + (shapeArgs.width || 0),\n            yMin: shapeArgs.y || 0,\n            yMax: (shapeArgs.y || 0) + (shapeArgs.height || 0)\n        };\n    }\n    // Otherwise use plotX/plotY and bb\n    const bb = point.graphic && point.graphic.getBBox();\n    return bb ? {\n        xMin: point.plotX - bb.width / 2,\n        xMax: point.plotX + bb.width / 2,\n        yMin: point.plotY - bb.height / 2,\n        yMax: point.plotY + bb.height / 2\n    } : null;\n}\n/**\n * Warn if using legacy options. Copy the options over. Note that this will\n * still break if using the legacy options in chart.update, addSeries etc.\n * @private\n */\nfunction warnLegacy(chart) {\n    if (chart.options.pathfinder ||\n        chart.series.reduce(function (acc, series) {\n            if (series.options) {\n                PathfinderComposition_merge(true, (series.options.connectors = series.options.connectors ||\n                    {}), series.options.pathfinder);\n            }\n            return acc || series.options && series.options.pathfinder;\n        }, false)) {\n        PathfinderComposition_merge(true, (chart.options.connectors = chart.options.connectors || {}), chart.options.pathfinder);\n        PathfinderComposition_error('WARNING: Pathfinder options have been renamed. ' +\n            'Use \"chart.connectors\" or \"series.connectors\" instead.');\n    }\n}\n/* *\n *\n *  Composition\n *\n * */\nvar ConnectionComposition;\n(function (ConnectionComposition) {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /** @private */\n    function compose(ChartClass, PathfinderClass, PointClass) {\n        const pointProto = PointClass.prototype;\n        if (!pointProto.getPathfinderAnchorPoint) {\n            // Initialize Pathfinder for charts\n            ChartClass.prototype.callbacks.push(function (chart) {\n                const options = chart.options;\n                if (options.connectors.enabled !== false) {\n                    warnLegacy(chart);\n                    this.pathfinder = new PathfinderClass(this);\n                    this.pathfinder.update(true); // First draw, defer render\n                }\n            });\n            pointProto.getMarkerVector = pointGetMarkerVector;\n            pointProto.getPathfinderAnchorPoint = pointGetPathfinderAnchorPoint;\n            pointProto.getRadiansToVector = pointGetRadiansToVector;\n            // Set default Pathfinder options\n            setOptions(ConnectorsDefaults);\n        }\n    }\n    ConnectionComposition.compose = compose;\n    /**\n     * Get coordinates of anchor point for pathfinder connection.\n     *\n     * @private\n     * @function Highcharts.Point#getPathfinderAnchorPoint\n     *\n     * @param {Highcharts.ConnectorsMarkerOptions} markerOptions\n     *        Connection options for position on point.\n     *\n     * @return {Highcharts.PositionObject}\n     *         An object with x/y properties for the position. Coordinates are\n     *         in plot values, not relative to point.\n     */\n    function pointGetPathfinderAnchorPoint(markerOptions) {\n        const bb = getPointBB(this);\n        let x, y;\n        switch (markerOptions.align) { // eslint-disable-line default-case\n            case 'right':\n                x = 'xMax';\n                break;\n            case 'left':\n                x = 'xMin';\n        }\n        switch (markerOptions.verticalAlign) { // eslint-disable-line default-case\n            case 'top':\n                y = 'yMin';\n                break;\n            case 'bottom':\n                y = 'yMax';\n        }\n        return {\n            x: x ? bb[x] : (bb.xMin + bb.xMax) / 2,\n            y: y ? bb[y] : (bb.yMin + bb.yMax) / 2\n        };\n    }\n    /**\n     * Utility to get the angle from one point to another.\n     *\n     * @private\n     * @function Highcharts.Point#getRadiansToVector\n     *\n     * @param {Highcharts.PositionObject} v1\n     *        The first vector, as an object with x/y properties.\n     *\n     * @param {Highcharts.PositionObject} v2\n     *        The second vector, as an object with x/y properties.\n     *\n     * @return {number}\n     *         The angle in degrees\n     */\n    function pointGetRadiansToVector(v1, v2) {\n        let box;\n        if (!PathfinderComposition_defined(v2)) {\n            box = getPointBB(this);\n            if (box) {\n                v2 = {\n                    x: (box.xMin + box.xMax) / 2,\n                    y: (box.yMin + box.yMax) / 2\n                };\n            }\n        }\n        return Math.atan2(v2.y - v1.y, v1.x - v2.x);\n    }\n    /**\n     * Utility to get the position of the marker, based on the path angle and\n     * the marker's radius.\n     *\n     * @private\n     * @function Highcharts.Point#getMarkerVector\n     *\n     * @param {number} radians\n     *        The angle in radians from the point center to another vector.\n     *\n     * @param {number} markerRadius\n     *        The radius of the marker, to calculate the additional distance to\n     *        the center of the marker.\n     *\n     * @param {Object} anchor\n     *        The anchor point of the path and marker as an object with x/y\n     *        properties.\n     *\n     * @return {Object}\n     *         The marker vector as an object with x/y properties.\n     */\n    function pointGetMarkerVector(radians, markerRadius, anchor) {\n        const twoPI = Math.PI * 2.0, bb = getPointBB(this), rectWidth = bb.xMax - bb.xMin, rectHeight = bb.yMax - bb.yMin, rAtan = Math.atan2(rectHeight, rectWidth), rectHalfWidth = rectWidth / 2.0, rectHalfHeight = rectHeight / 2.0, rectHorizontalCenter = bb.xMin + rectHalfWidth, rectVerticalCenter = bb.yMin + rectHalfHeight, edgePoint = {\n            x: rectHorizontalCenter,\n            y: rectVerticalCenter\n        };\n        let theta = radians, tanTheta = 1, leftOrRightRegion = false, xFactor = 1, yFactor = 1;\n        while (theta < -Math.PI) {\n            theta += twoPI;\n        }\n        while (theta > Math.PI) {\n            theta -= twoPI;\n        }\n        tanTheta = Math.tan(theta);\n        if ((theta > -rAtan) && (theta <= rAtan)) {\n            // Right side\n            yFactor = -1;\n            leftOrRightRegion = true;\n        }\n        else if (theta > rAtan && theta <= (Math.PI - rAtan)) {\n            // Top side\n            yFactor = -1;\n        }\n        else if (theta > (Math.PI - rAtan) || theta <= -(Math.PI - rAtan)) {\n            // Left side\n            xFactor = -1;\n            leftOrRightRegion = true;\n        }\n        else {\n            // Bottom side\n            xFactor = -1;\n        }\n        // Correct the edgePoint according to the placement of the marker\n        if (leftOrRightRegion) {\n            edgePoint.x += xFactor * (rectHalfWidth);\n            edgePoint.y += yFactor * (rectHalfWidth) * tanTheta;\n        }\n        else {\n            edgePoint.x += xFactor * (rectHeight / (2.0 * tanTheta));\n            edgePoint.y += yFactor * (rectHalfHeight);\n        }\n        if (anchor.x !== rectHorizontalCenter) {\n            edgePoint.x = anchor.x;\n        }\n        if (anchor.y !== rectVerticalCenter) {\n            edgePoint.y = anchor.y;\n        }\n        return {\n            x: edgePoint.x + (markerRadius * Math.cos(theta)),\n            y: edgePoint.y - (markerRadius * Math.sin(theta))\n        };\n    }\n})(ConnectionComposition || (ConnectionComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const PathfinderComposition = (ConnectionComposition);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Point\"],\"commonjs\":[\"highcharts\",\"Point\"],\"commonjs2\":[\"highcharts\",\"Point\"],\"root\":[\"Highcharts\",\"Point\"]}\nvar highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_ = __webpack_require__(260);\nvar highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default = /*#__PURE__*/__webpack_require__.n(highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_);\n;// ./code/es-modules/Gantt/Pathfinder.js\n/* *\n *\n *  (c) 2016 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\n\nconst { addEvent, defined: Pathfinder_defined, pick: Pathfinder_pick, splat } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\nconst Pathfinder_max = Math.max, Pathfinder_min = Math.min;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Get point bounding box using plotX/plotY and shapeArgs. If using\n * graphic.getBBox() directly, the bbox will be affected by animation.\n *\n * @private\n * @function\n *\n * @param {Highcharts.Point} point\n *        The point to get BB of.\n *\n * @return {Highcharts.Dictionary<number>|null}\n *         Result xMax, xMin, yMax, yMin.\n */\nfunction Pathfinder_getPointBB(point) {\n    const shapeArgs = point.shapeArgs;\n    // Prefer using shapeArgs (columns)\n    if (shapeArgs) {\n        return {\n            xMin: shapeArgs.x || 0,\n            xMax: (shapeArgs.x || 0) + (shapeArgs.width || 0),\n            yMin: shapeArgs.y || 0,\n            yMax: (shapeArgs.y || 0) + (shapeArgs.height || 0)\n        };\n    }\n    // Otherwise use plotX/plotY and bb\n    const bb = point.graphic && point.graphic.getBBox();\n    return bb ? {\n        xMin: point.plotX - bb.width / 2,\n        xMax: point.plotX + bb.width / 2,\n        yMin: point.plotY - bb.height / 2,\n        yMax: point.plotY + bb.height / 2\n    } : null;\n}\n/**\n * Compute smallest distance between two rectangles.\n * @private\n */\nfunction calculateObstacleDistance(a, b, bbMargin) {\n    // Count the distance even if we are slightly off\n    const margin = Pathfinder_pick(bbMargin, 10), yOverlap = a.yMax + margin > b.yMin - margin &&\n        a.yMin - margin < b.yMax + margin, xOverlap = a.xMax + margin > b.xMin - margin &&\n        a.xMin - margin < b.xMax + margin, xDistance = yOverlap ? (a.xMin > b.xMax ? a.xMin - b.xMax : b.xMin - a.xMax) : Infinity, yDistance = xOverlap ? (a.yMin > b.yMax ? a.yMin - b.yMax : b.yMin - a.yMax) : Infinity;\n    // If the rectangles collide, try recomputing with smaller margin.\n    // If they collide anyway, discard the obstacle.\n    if (xOverlap && yOverlap) {\n        return (margin ?\n            calculateObstacleDistance(a, b, Math.floor(margin / 2)) :\n            Infinity);\n    }\n    return Pathfinder_min(xDistance, yDistance);\n}\n/**\n * Calculate margin to place around obstacles for the pathfinder in pixels.\n * Returns a minimum of 1 pixel margin.\n *\n * @private\n * @function\n *\n * @param {Array<object>} obstacles\n *        Obstacles to calculate margin from.\n *\n * @return {number}\n *         The calculated margin in pixels. At least 1.\n */\nfunction calculateObstacleMargin(obstacles) {\n    const len = obstacles.length, distances = [];\n    let onstacleDistance;\n    // Go over all obstacles and compare them to the others.\n    for (let i = 0; i < len; ++i) {\n        // Compare to all obstacles ahead. We will already have compared this\n        // obstacle to the ones before.\n        for (let j = i + 1; j < len; ++j) {\n            onstacleDistance =\n                calculateObstacleDistance(obstacles[i], obstacles[j]);\n            // TODO: Magic number 80\n            if (onstacleDistance < 80) { // Ignore large distances\n                distances.push(onstacleDistance);\n            }\n        }\n    }\n    // Ensure we always have at least one value, even in very spacious charts\n    distances.push(80);\n    return Pathfinder_max(Math.floor(distances.sort(function (a, b) {\n        return (a - b);\n    })[\n    // Discard first 10% of the relevant distances, and then grab\n    // the smallest one.\n    Math.floor(distances.length / 10)] / 2 - 1 // Divide the distance by 2 and subtract 1.\n    ), 1 // 1 is the minimum margin\n    );\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Pathfinder class.\n *\n * @private\n * @class\n * @name Highcharts.Pathfinder\n *\n * @param {Highcharts.Chart} chart\n *        The chart to operate on.\n */\nclass Pathfinder {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static compose(ChartClass, PointClass) {\n        PathfinderComposition.compose(ChartClass, Pathfinder, PointClass);\n    }\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(chart) {\n        this.init(chart);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Initialize the Pathfinder object.\n     *\n     * @function Highcharts.Pathfinder#init\n     *\n     * @param {Highcharts.Chart} chart\n     *        The chart context.\n     */\n    init(chart) {\n        // Initialize pathfinder with chart context\n        this.chart = chart;\n        // Init connection reference list\n        this.connections = [];\n        // Recalculate paths/obstacles on chart redraw\n        addEvent(chart, 'redraw', function () {\n            this.pathfinder.update();\n        });\n    }\n    /**\n     * Update Pathfinder connections from scratch.\n     *\n     * @function Highcharts.Pathfinder#update\n     *\n     * @param {boolean} [deferRender]\n     *        Whether or not to defer rendering of connections until\n     *        series.afterAnimate event has fired. Used on first render.\n     */\n    update(deferRender) {\n        const chart = this.chart, pathfinder = this, oldConnections = pathfinder.connections;\n        // Rebuild pathfinder connections from options\n        pathfinder.connections = [];\n        chart.series.forEach(function (series) {\n            if (series.visible && !series.options.isInternal) {\n                series.points.forEach(function (point) {\n                    const ganttPointOptions = point.options;\n                    // For Gantt series the connect could be\n                    // defined as a dependency\n                    if (ganttPointOptions && ganttPointOptions.dependency) {\n                        ganttPointOptions.connect = ganttPointOptions\n                            .dependency;\n                    }\n                    const connects = point.options?.connect ?\n                        splat(point.options.connect) :\n                        [];\n                    let to;\n                    if (point.visible && point.isInside !== false) {\n                        connects.forEach((connect) => {\n                            const toId = typeof connect === 'string' ?\n                                connect :\n                                connect.to;\n                            if (toId) {\n                                to = chart.get(toId);\n                            }\n                            if (to instanceof (highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default()) &&\n                                to.series.visible &&\n                                to.visible &&\n                                to.isInside !== false) {\n                                // Add new connection\n                                pathfinder.connections.push(new Gantt_Connection(point, // From\n                                to, typeof connect === 'string' ?\n                                    {} :\n                                    connect));\n                            }\n                        });\n                    }\n                });\n            }\n        });\n        // Clear connections that should not be updated, and move old info over\n        // to new connections.\n        for (let j = 0, k, found, lenOld = oldConnections.length, lenNew = pathfinder.connections.length; j < lenOld; ++j) {\n            found = false;\n            const oldCon = oldConnections[j];\n            for (k = 0; k < lenNew; ++k) {\n                const newCon = pathfinder.connections[k];\n                if ((oldCon.options && oldCon.options.type) ===\n                    (newCon.options && newCon.options.type) &&\n                    oldCon.fromPoint === newCon.fromPoint &&\n                    oldCon.toPoint === newCon.toPoint) {\n                    newCon.graphics = oldCon.graphics;\n                    found = true;\n                    break;\n                }\n            }\n            if (!found) {\n                oldCon.destroy();\n            }\n        }\n        // Clear obstacles to force recalculation. This must be done on every\n        // redraw in case positions have changed. Recalculation is handled in\n        // Connection.getPath on demand.\n        delete this.chartObstacles;\n        delete this.lineObstacles;\n        // Draw the pending connections\n        pathfinder.renderConnections(deferRender);\n    }\n    /**\n     * Draw the chart's connecting paths.\n     *\n     * @function Highcharts.Pathfinder#renderConnections\n     *\n     * @param {boolean} [deferRender]\n     *        Whether or not to defer render until series animation is finished.\n     *        Used on first render.\n     */\n    renderConnections(deferRender) {\n        if (deferRender) {\n            // Render after series are done animating\n            this.chart.series.forEach(function (series) {\n                const render = function () {\n                    // Find pathfinder connections belonging to this series\n                    // that haven't rendered, and render them now.\n                    const pathfinder = series.chart.pathfinder, conns = pathfinder && pathfinder.connections || [];\n                    conns.forEach(function (connection) {\n                        if (connection.fromPoint &&\n                            connection.fromPoint.series === series) {\n                            connection.render();\n                        }\n                    });\n                    if (series.pathfinderRemoveRenderEvent) {\n                        series.pathfinderRemoveRenderEvent();\n                        delete series.pathfinderRemoveRenderEvent;\n                    }\n                };\n                if (series.options.animation === false) {\n                    render();\n                }\n                else {\n                    series.pathfinderRemoveRenderEvent = addEvent(series, 'afterAnimate', render);\n                }\n            });\n        }\n        else {\n            // Go through connections and render them\n            this.connections.forEach(function (connection) {\n                connection.render();\n            });\n        }\n    }\n    /**\n     * Get obstacles for the points in the chart. Does not include connecting\n     * lines from Pathfinder. Applies algorithmMargin to the obstacles.\n     *\n     * @function Highcharts.Pathfinder#getChartObstacles\n     *\n     * @param {Object} options\n     *        Options for the calculation. Currently only\n     *        `options.algorithmMargin`.\n     *\n     * @param {number} options.algorithmMargin\n     *        The algorithm margin to use for the obstacles.\n\n    * @return {Array<object>}\n     *         An array of calculated obstacles. Each obstacle is defined as an\n     *         object with xMin, xMax, yMin and yMax properties.\n     */\n    getChartObstacles(options) {\n        const series = this.chart.series, margin = Pathfinder_pick(options.algorithmMargin, 0);\n        let obstacles = [], calculatedMargin;\n        for (let i = 0, sLen = series.length; i < sLen; ++i) {\n            if (series[i].visible && !series[i].options.isInternal) {\n                for (let j = 0, pLen = series[i].points.length, bb, point; j < pLen; ++j) {\n                    point = series[i].points[j];\n                    if (point.visible) {\n                        bb = Pathfinder_getPointBB(point);\n                        if (bb) {\n                            obstacles.push({\n                                xMin: bb.xMin - margin,\n                                xMax: bb.xMax + margin,\n                                yMin: bb.yMin - margin,\n                                yMax: bb.yMax + margin\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        // Sort obstacles by xMin for optimization\n        obstacles = obstacles.sort(function (a, b) {\n            return a.xMin - b.xMin;\n        });\n        // Add auto-calculated margin if the option is not defined\n        if (!Pathfinder_defined(options.algorithmMargin)) {\n            calculatedMargin =\n                options.algorithmMargin =\n                    calculateObstacleMargin(obstacles);\n            obstacles.forEach(function (obstacle) {\n                obstacle.xMin -= calculatedMargin;\n                obstacle.xMax += calculatedMargin;\n                obstacle.yMin -= calculatedMargin;\n                obstacle.yMax += calculatedMargin;\n            });\n        }\n        return obstacles;\n    }\n    /**\n     * Utility function to get metrics for obstacles:\n     * - Widest obstacle width\n     * - Tallest obstacle height\n     *\n     * @function Highcharts.Pathfinder#getObstacleMetrics\n     *\n     * @param {Array<object>} obstacles\n     *        An array of obstacles to inspect.\n     *\n     * @return {Object}\n     *         The calculated metrics, as an object with maxHeight and maxWidth\n     *         properties.\n     */\n    getObstacleMetrics(obstacles) {\n        let maxWidth = 0, maxHeight = 0, width, height, i = obstacles.length;\n        while (i--) {\n            width = obstacles[i].xMax - obstacles[i].xMin;\n            height = obstacles[i].yMax - obstacles[i].yMin;\n            if (maxWidth < width) {\n                maxWidth = width;\n            }\n            if (maxHeight < height) {\n                maxHeight = height;\n            }\n        }\n        return {\n            maxHeight: maxHeight,\n            maxWidth: maxWidth\n        };\n    }\n    /**\n     * Utility to get which direction to start the pathfinding algorithm\n     * (X vs Y), calculated from a set of marker options.\n     *\n     * @function Highcharts.Pathfinder#getAlgorithmStartDirection\n     *\n     * @param {Highcharts.ConnectorsMarkerOptions} markerOptions\n     *        Marker options to calculate from.\n     *\n     * @return {boolean}\n     *         Returns true for X, false for Y, and undefined for autocalculate.\n     */\n    getAlgorithmStartDirection(markerOptions) {\n        const xCenter = markerOptions.align !== 'left' &&\n            markerOptions.align !== 'right', yCenter = markerOptions.verticalAlign !== 'top' &&\n            markerOptions.verticalAlign !== 'bottom';\n        return xCenter ?\n            (yCenter ? void 0 : false) : // When x is centered\n            (yCenter ? true : void 0); // When x is off-center\n    }\n}\n/**\n * @name Highcharts.Pathfinder#algorithms\n * @type {Highcharts.Dictionary<Function>}\n */\nPathfinder.prototype.algorithms = PathfinderAlgorithms;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Gantt_Pathfinder = (Pathfinder);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * The default pathfinder algorithm to use for a chart. It is possible to define\n * your own algorithms by adding them to the\n * `Highcharts.Pathfinder.prototype.algorithms`\n * object before the chart has been created.\n *\n * The default algorithms are as follows:\n *\n * `straight`:      Draws a straight line between the connecting\n *                  points. Does not avoid other points when drawing.\n *\n * `simpleConnect`: Finds a path between the points using right angles\n *                  only. Takes only starting/ending points into\n *                  account, and will not avoid other points.\n *\n * `fastAvoid`:     Finds a path between the points using right angles\n *                  only. Will attempt to avoid other points, but its\n *                  focus is performance over accuracy. Works well with\n *                  less dense datasets.\n *\n * @typedef {\"fastAvoid\"|\"simpleConnect\"|\"straight\"|string} Highcharts.PathfinderTypeValue\n */\n''; // Keeps doclets above in JS file\n\n;// ./code/es-modules/Extensions/ArrowSymbols.js\n/* *\n *\n *  (c) 2017 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Creates an arrow symbol. Like a triangle, except not filled.\n * ```\n *                   o\n *             o\n *       o\n * o\n *       o\n *             o\n *                   o\n * ```\n *\n * @private\n * @function\n *\n * @param {number} x\n *        x position of the arrow\n *\n * @param {number} y\n *        y position of the arrow\n *\n * @param {number} w\n *        width of the arrow\n *\n * @param {number} h\n *        height of the arrow\n *\n * @return {Highcharts.SVGPathArray}\n *         Path array\n */\nfunction arrow(x, y, w, h) {\n    return [\n        ['M', x, y + h / 2],\n        ['L', x + w, y],\n        ['L', x, y + h / 2],\n        ['L', x + w, y + h]\n    ];\n}\n/**\n * Creates a half-width arrow symbol. Like a triangle, except not filled.\n * ```\n *       o\n *    o\n * o\n *    o\n *       o\n * ```\n *\n * @private\n * @function\n *\n * @param {number} x\n *        x position of the arrow\n *\n * @param {number} y\n *        y position of the arrow\n *\n * @param {number} w\n *        width of the arrow\n *\n * @param {number} h\n *        height of the arrow\n *\n * @return {Highcharts.SVGPathArray}\n *         Path array\n */\nfunction arrowHalf(x, y, w, h) {\n    return arrow(x, y, w / 2, h);\n}\n/**\n * @private\n */\nfunction compose(SVGRendererClass) {\n    const symbols = SVGRendererClass.prototype.symbols;\n    symbols.arrow = arrow;\n    symbols['arrow-filled'] = triangleLeft;\n    symbols['arrow-filled-half'] = triangleLeftHalf;\n    symbols['arrow-half'] = arrowHalf;\n    symbols['triangle-left'] = triangleLeft;\n    symbols['triangle-left-half'] = triangleLeftHalf;\n}\n/**\n * Creates a left-oriented triangle.\n * ```\n *             o\n *       ooooooo\n * ooooooooooooo\n *       ooooooo\n *             o\n * ```\n *\n * @private\n * @function\n *\n * @param {number} x\n *        x position of the triangle\n *\n * @param {number} y\n *        y position of the triangle\n *\n * @param {number} w\n *        width of the triangle\n *\n * @param {number} h\n *        height of the triangle\n *\n * @return {Highcharts.SVGPathArray}\n *         Path array\n */\nfunction triangleLeft(x, y, w, h) {\n    return [\n        ['M', x + w, y],\n        ['L', x, y + h / 2],\n        ['L', x + w, y + h],\n        ['Z']\n    ];\n}\n/**\n * Creates a half-width, left-oriented triangle.\n * ```\n *       o\n *    oooo\n * ooooooo\n *    oooo\n *       o\n * ```\n *\n * @private\n * @function\n *\n * @param {number} x\n *        x position of the triangle\n *\n * @param {number} y\n *        y position of the triangle\n *\n * @param {number} w\n *        width of the triangle\n *\n * @param {number} h\n *        height of the triangle\n *\n * @return {Highcharts.SVGPathArray}\n *         Path array\n */\nfunction triangleLeftHalf(x, y, w, h) {\n    return triangleLeft(x, y, w / 2, h);\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst ArrowSymbols = {\n    compose\n};\n/* harmony default export */ const Extensions_ArrowSymbols = (ArrowSymbols);\n\n;// ./code/es-modules/masters/modules/pathfinder.js\n\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nG.Pathfinder = G.Pathfinder || Gantt_Pathfinder;\nExtensions_ArrowSymbols.compose(G.SVGRenderer);\nG.Pathfinder.compose(G.Chart, G.Point);\n/* harmony default export */ const pathfinder_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__260__", "ConnectionComposition", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "pathfinder_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "defined", "error", "merge", "objectEach", "deg2rad", "max", "Math", "min", "Gantt_Connection", "constructor", "from", "to", "options", "init", "fromPoint", "toPoint", "chart", "series", "pathfinder", "<PERSON><PERSON><PERSON>", "path", "attribs", "styledMode", "anim", "pathGraphic", "connection", "graphics", "group", "renderer", "g", "addClass", "attr", "zIndex", "add", "seriesGroup", "translate", "plotLeft", "plotTop", "opacity", "animate", "add<PERSON><PERSON><PERSON>", "type", "markerVector", "radians", "box", "width", "height", "pathVector", "segment", "point", "anchor", "getPathfinderAnchorPoint", "enabled", "length", "x", "y", "getRadiansToVector", "getMarkerVector", "radius", "rotation", "rotationOriginX", "rotationOriginY", "symbol", "colorIndex", "fill", "color", "stroke", "lineColor", "lineWidth", "animation", "<PERSON><PERSON><PERSON>", "algorithm", "algorithms", "chartObstacles", "obstacles", "requiresObstacles", "getChartObstacles", "connectors", "<PERSON><PERSON><PERSON>gin", "chartObstacleMetrics", "getObstacleMetrics", "startMarker", "<PERSON><PERSON><PERSON><PERSON>", "lineObstacles", "obstacleMetrics", "hardBounds", "xMin", "xMax", "plot<PERSON>id<PERSON>", "yMin", "yMax", "plotHeight", "obstacleOptions", "margin", "startDirectionX", "getAlgorithmStartDirection", "render", "dashStyle", "dashstyle", "marker", "ceil", "pathResult", "concat", "destroy", "val", "Series_PathUtilities", "applyRadius", "r", "i", "push", "prevSeg", "nextSeg", "x1", "y1", "x2", "y2", "directionX", "directionY", "abs", "pick", "PathfinderAlgorithms_min", "PathfinderAlgorithms_max", "findLastObstacleBefore", "startIx", "left", "right", "cursor", "cmp", "findObstacleFromPoint", "obstacle", "pathFromSegments", "segments", "start", "end", "limitObstacleToBounds", "bounds", "simpleConnect", "startObstacleIx", "endObstacleIx", "endSegment", "dir", "startObstacle", "waypoint", "useMax", "endPoint", "copyFromPoint", "fromKey", "to<PERSON><PERSON>", "offset", "getMeOut", "direction", "prevWaypoint", "waypoint2", "fastAvoid", "dirIsX", "endSegments", "metrics", "softMinX", "max<PERSON><PERSON><PERSON>", "softMaxX", "softMinY", "maxHeight", "softMaxY", "extractedEndPoint", "forceObstacleBreak", "pivotPoint", "directionIsX", "firstPoint", "lastPoint", "highestPoint", "lowestPoint", "searchDirection", "getDodgeDirection", "softBounds", "soft", "hard", "toPointMax", "toPointMin", "maxOutOfSoftBounds", "minOutOfSoftBounds", "maxOutOfHardBounds", "minOutOfHardBounds", "minDistance", "maxDistance", "minPivot", "maxPivot", "slice", "extractFromObstacle", "goalPoint", "clearPathTo", "pivot", "waypointUseMax", "envelopingObstacle", "secondEnvelopingObstacle", "envelopWaypoint", "<PERSON><PERSON><PERSON><PERSON>", "reverse", "ConnectorsDefaults", "align", "verticalAlign", "inside", "setOptions", "PathfinderComposition_defined", "PathfinderComposition_error", "PathfinderComposition_merge", "getPointBB", "shapeArgs", "bb", "graphic", "getBBox", "plotX", "plotY", "pointGetPathfinderAnchorPoint", "markerOptions", "pointGetRadiansToVector", "v1", "v2", "atan2", "pointGetMarkerVector", "markerRadius", "twoPI", "PI", "rectWidth", "rectHeight", "rAtan", "rect<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rectHalfHeight", "rectHorizontalCenter", "rectVerticalCenter", "edgePoint", "theta", "tan<PERSON><PERSON><PERSON>", "leftOrRightRegion", "xFactor", "yFactor", "tan", "cos", "sin", "compose", "ChartClass", "PathfinderClass", "PointClass", "pointProto", "callbacks", "reduce", "acc", "update", "PathfinderComposition", "highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_", "highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default", "addEvent", "Pathfinder_defined", "Pathfinder_pick", "splat", "Pathfinder_max", "Pathfinder_min", "Pathfinder", "connections", "deferRender", "oldConnections", "for<PERSON>ach", "visible", "isInternal", "points", "ganttPointOptions", "dependency", "connect", "connects", "isInside", "toId", "j", "k", "found", "lenOld", "len<PERSON><PERSON>", "oldCon", "newCon", "renderConnections", "conns", "pathfinderRemoveRenderEvent", "<PERSON><PERSON><PERSON><PERSON>", "sLen", "pLen", "Pathfinder_getPointBB", "sort", "b", "calculateObstacleMargin", "onstacleDistance", "len", "distances", "calculateObstacleDistance", "bb<PERSON><PERSON><PERSON>", "yOverlap", "xOverlap", "xDistance", "Infinity", "yDistance", "floor", "xCenter", "yCenter", "arrow", "w", "h", "arrow<PERSON>alf", "triangleLeft", "triangleLeftHalf", "straight", "G", "Extensions_ArrowSymbols", "SVGRendererClass", "symbols", "<PERSON><PERSON><PERSON><PERSON>", "Chart", "Point"], "mappings": "CAWA,AAXA;;;;;;;;;;CAUC,EACA,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,KAAQ,EACnE,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,gCAAiC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,KAAQ,CAAE,GAChH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,gCAAgC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,KAAQ,EAEpGA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAC9E,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAqlDNC,EArlDUC,EAAuB,CAE/B,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAab,OAAO,CAG5B,IAAIC,EAASS,CAAwB,CAACE,EAAS,CAAG,CAGjDZ,QAAS,CAAC,CACX,EAMA,OAHAS,CAAmB,CAACG,EAAS,CAACX,EAAQA,EAAOD,OAAO,CAAEW,GAG/CV,EAAOD,OAAO,AACtB,CAMCW,EAAoBI,CAAC,CAAG,AAACd,IACxB,IAAIe,EAASf,GAAUA,EAAOgB,UAAU,CACvC,IAAOhB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAU,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAAClB,EAASoB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACtB,EAASqB,IAC5EE,OAAOC,cAAc,CAACxB,EAASqB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAerH,GAAM,CAAEE,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAEC,WAAAA,CAAU,CAAE,CAAIJ,IAMzCK,EAAU,AAACL,IAA+EK,OAAO,CAAEC,EAAMC,KAAKD,GAAG,CAAEE,EAAMD,KAAKC,GAAG,CAgTpGC,EAzRnC,MACIC,YAAYC,CAAI,CAAEC,CAAE,CAAEC,CAAO,CAAE,CAC3B,IAAI,CAACC,IAAI,CAACH,EAAMC,EAAIC,EACxB,CAeAC,KAAKH,CAAI,CAAEC,CAAE,CAAEC,CAAO,CAAE,CACpB,IAAI,CAACE,SAAS,CAAGJ,EACjB,IAAI,CAACK,OAAO,CAAGJ,EACf,IAAI,CAACC,OAAO,CAAGA,EACf,IAAI,CAACI,KAAK,CAAGN,EAAKO,MAAM,CAACD,KAAK,CAC9B,IAAI,CAACE,UAAU,CAAG,IAAI,CAACF,KAAK,CAACE,UAAU,AAC3C,CAgBAC,WAAWC,CAAI,CAAEC,CAAO,CAAE,CACtB,IAAyBL,EAAQ,IAAI,CAACA,KAAK,CAAEM,EAAaN,EAAMM,UAAU,CAAEJ,EAAa,IAAI,CAACA,UAAU,CAAEK,EAAO,CAAC,EAC9GC,EAAcC,AADC,IAAI,CACMC,QAAQ,EAAID,AADtB,IAAI,CAC6BC,QAAQ,CAACN,IAAI,AAE7D,AAACF,CAAAA,EAAWS,KAAK,EACjBT,CAAAA,EAAWS,KAAK,CAAGX,EAAMY,QAAQ,CAACC,CAAC,GAC9BC,QAAQ,CAAC,+BACTC,IAAI,CAAC,CAAEC,OAAQ,EAAG,GAClBC,GAAG,CAACjB,EAAMkB,WAAW,CAAA,EAK9BhB,EAAWS,KAAK,CAACQ,SAAS,CAACnB,EAAMoB,QAAQ,CAAEpB,EAAMqB,OAAO,EAEpD,CAAEb,CAAAA,GAAeA,EAAYI,QAAQ,AAAD,IACpCJ,EAAcR,EAAMY,QAAQ,CAACR,IAAI,GAC5Ba,GAAG,CAACf,EAAWS,KAAK,EACrB,AAACL,GACDE,EAAYO,IAAI,CAAC,CACbO,QAAS,CACb,IAIRd,EAAYO,IAAI,CAACV,GACjBE,EAAKzC,CAAC,CAAGsC,EACL,AAACE,GACDC,CAAAA,EAAKe,OAAO,CAAG,CAAA,EAEnBd,EAAYe,OAAO,CAAChB,GAEpB,IAAI,CAACG,QAAQ,CAAG,IAAI,CAACA,QAAQ,EAAI,CAAC,EAClC,IAAI,CAACA,QAAQ,CAACN,IAAI,CAAGI,CACzB,CAmBAgB,UAAUC,CAAI,CAAE7B,CAAO,CAAEQ,CAAI,CAAE,CAC3B,IAGIsB,EAAcC,EAAmBC,EAAKC,EAAOC,EAAQC,EAAYC,EAH5ChC,EAAQS,AAAd,IAAI,CAAqBX,SAAS,CAACG,MAAM,CAACD,KAAK,CAAEE,EAAaF,EAAME,UAAU,CAAEU,EAAWZ,EAAMY,QAAQ,CAAEqB,EAASR,AAAS,UAATA,EACnIhB,AADe,IAAI,CACRX,SAAS,CACpBW,AAFe,IAAI,CAERV,OAAO,CAAGmC,EAASD,EAAME,wBAAwB,CAACvC,EAE5DA,CAAAA,EAAQwC,OAAO,EAUhBJ,CAAAA,CALAA,EADAP,AAAS,UAATA,EACUrB,CAAI,CAAC,EAAE,CAGPA,CAAI,CAACA,EAAKiC,MAAM,CAAG,EAAE,GAEpBL,AAAe,MAAfA,CAAO,CAAC,EAAE,EAAYA,AAAe,MAAfA,CAAO,CAAC,EAAE,AAAO,IAClDD,EAAa,CACTO,EAAGN,CAAO,CAAC,EAAE,CACbO,EAAGP,CAAO,CAAC,EAAE,AACjB,EAGAL,EAAUM,EAAMO,kBAAkB,CAACT,EAAYG,GAC/CR,EAAeO,EAAMQ,eAAe,CAACd,EAAS/B,EAAQ8C,MAAM,CAAER,GAO1DtC,EAAQiC,KAAK,EAAIjC,EAAQkC,MAAM,EAC/BD,EAAQjC,EAAQiC,KAAK,CACrBC,EAASlC,EAAQkC,MAAM,EAGvBD,EAAQC,EAASlC,AAAiB,EAAjBA,EAAQ8C,MAAM,CAGnCjC,AArCe,IAAI,CAqCRC,QAAQ,CAAGD,AArCP,IAAI,CAqCcC,QAAQ,EAAI,CAAC,EAC9CkB,EAAM,CACFU,EAAGZ,EAAaY,CAAC,CAAIT,EAAQ,EAC7BU,EAAGb,EAAaa,CAAC,CAAIT,EAAS,EAC9BD,MAAOA,EACPC,OAAQA,EACRa,SAfO,CAAChB,EAAUvC,EAgBlBwD,gBAAiBlB,EAAaY,CAAC,CAC/BO,gBAAiBnB,EAAaa,CAAC,AACnC,EACK9B,AA/CU,IAAI,CA+CHC,QAAQ,CAACe,EAAK,CAqB1BhB,AApEW,IAAI,CAoEJC,QAAQ,CAACe,EAAK,CAACF,OAAO,CAACK,IAnBlCnB,AAjDW,IAAI,CAiDJC,QAAQ,CAACe,EAAK,CAAGb,EACvBkC,MAAM,CAAClD,EAAQkD,MAAM,EACrBhC,QAAQ,CAAC,oCAAsCW,EAAtC,4BACa,IAAI,CAAC3B,SAAS,CAACiD,UAAU,EAC/ChC,IAAI,CAACa,GACLX,GAAG,CAACf,EAAWS,KAAK,EACrB,AAACC,EAASN,UAAU,EACpBG,AAxDO,IAAI,CAwDAC,QAAQ,CAACe,EAAK,CAACV,IAAI,CAAC,CAC3BiC,KAAMpD,EAAQqD,KAAK,EAAIxC,AAzDpB,IAAI,CAyD2BX,SAAS,CAACmD,KAAK,CACjDC,OAAQtD,EAAQuD,SAAS,CACzB,eAAgBvD,EAAQwD,SAAS,CACjC9B,QAAS,CACb,GACKC,OAAO,CAAC,CACTD,QAAS,CACb,EAAGW,EAAMhC,MAAM,CAACL,OAAO,CAACyD,SAAS,GAOjD,CAaAC,QAAQ1D,CAAO,CAAE,CACb,IAAMM,EAAa,IAAI,CAACA,UAAU,CAAEF,EAAQ,IAAI,CAACA,KAAK,CAAEuD,EAAYrD,EAAWsD,UAAU,CAAC5D,EAAQ6B,IAAI,CAAC,CACnGgC,EAAiBvD,EAAWuD,cAAc,OAC9C,AAAI,AAAqB,YAArB,OAAOF,GACPtE,EAAM,IAAMW,EAAQ6B,IAAI,CAAG,oCACpB,CACHrB,KAAM,EAAE,CACRsD,UAAW,EAAE,AACjB,IAGAH,EAAUI,iBAAiB,EAAI,CAACF,IAChCA,EACIvD,EAAWuD,cAAc,CACrBvD,EAAW0D,iBAAiB,CAAChE,GAGrCI,EAAMJ,OAAO,CAACiE,UAAU,CAACC,eAAe,CACpClE,EAAQkE,eAAe,CAE3B5D,EAAW6D,oBAAoB,CAC3B7D,EAAW8D,kBAAkB,CAACP,IAG/BF,EAEP,IAAI,CAACzD,SAAS,CAACqC,wBAAwB,CAACvC,EAAQqE,WAAW,EAE3D,IAAI,CAAClE,OAAO,CAACoC,wBAAwB,CAACvC,EAAQsE,SAAS,EAAGhF,EAAM,CAC5DuE,eAAgBA,EAChBU,cAAejE,EAAWiE,aAAa,EAAI,EAAE,CAC7CC,gBAAiBlE,EAAW6D,oBAAoB,CAChDM,WAAY,CACRC,KAAM,EACNC,KAAMvE,EAAMwE,SAAS,CACrBC,KAAM,EACNC,KAAM1E,EAAM2E,UAAU,AAC1B,EACAC,gBAAiB,CACbC,OAAQjF,EAAQkE,eAAe,AACnC,EACAgB,gBAAiB5E,EAAW6E,0BAA0B,CAACnF,EAAQqE,WAAW,CAC9E,EAAGrE,IACP,CAMAoF,QAAS,CACL,IAAyBlF,EAAYW,AAAlB,IAAI,CAAyBX,SAAS,CAAEG,EAASH,EAAUG,MAAM,CAAED,EAAQC,EAAOD,KAAK,CAAEE,EAAaF,EAAME,UAAU,CAAEG,EAAU,CAAC,EAClJT,EAAUV,EAAMc,EAAMJ,OAAO,CAACiE,UAAU,CAAE5D,EAAOL,OAAO,CAACiE,UAAU,CAAE/D,EAAUF,OAAO,CAACiE,UAAU,CAAEpD,AADpF,IAAI,CAC2Fb,OAAO,CAErH,EAACI,EAAMM,UAAU,GACjBD,EAAQ6C,MAAM,CAAGtD,EAAQuD,SAAS,EAAIrD,EAAUmD,KAAK,CACrD5C,CAAO,CAAC,eAAe,CAAGT,EAAQwD,SAAS,CACvCxD,EAAQqF,SAAS,EACjB5E,CAAAA,EAAQ6E,SAAS,CAAGtF,EAAQqF,SAAS,AAAD,GAG5C5E,EAAQ,KAAQ,CACZ,qDAC0BP,EAAUiD,UAAU,CAG9C,AAAC/D,EAAQY,AAFbA,CAAAA,EAAUV,EAAMmB,EAAST,EAAO,EAEXuF,MAAM,CAACzC,MAAM,GAC9B9C,CAAAA,EAAQuF,MAAM,CAACzC,MAAM,CAAGnD,EAAIF,EAAIC,KAAK8F,IAAI,CAAC,AAACxF,CAAAA,EAAQkE,eAAe,EAAI,CAAA,EAAK,GAAK,EAAG,GAAI,EAAC,EAG5F,IAAMuB,EAAa5E,AAnBA,IAAI,CAmBO6C,OAAO,CAAC1D,GAAUQ,EAAOiF,EAAWjF,IAAI,AAGlEiF,CAAAA,EAAW3B,SAAS,GACpBxD,EAAWiE,aAAa,CACpBjE,EAAWiE,aAAa,EAAI,EAAE,CAClCjE,EAAWiE,aAAa,CACpBjE,EAAWiE,aAAa,CAACmB,MAAM,CAACD,EAAW3B,SAAS,GAG5DjD,AA7BmB,IAAI,CA6BZN,UAAU,CAACC,EAAMC,GAE5BI,AA/BmB,IAAI,CA+BZe,SAAS,CAAC,QAAStC,EAAMU,EAAQuF,MAAM,CAAEvF,EAAQqE,WAAW,EAAG7D,GAC1EK,AAhCmB,IAAI,CAgCZe,SAAS,CAAC,MAAOtC,EAAMU,EAAQuF,MAAM,CAAEvF,EAAQsE,SAAS,EAAG9D,EAC1E,CAMAmF,SAAU,CACF,IAAI,CAAC7E,QAAQ,GACbvB,EAAW,IAAI,CAACuB,QAAQ,CAAE,SAAU8E,CAAG,EACnCA,EAAID,OAAO,EACf,GACA,OAAO,IAAI,CAAC7E,QAAQ,CAE5B,CACJ,EA6KmC+E,EAJb,CAClBC,YApDJ,SAAqBtF,CAAI,CAAEuF,CAAC,EACxB,IAAM7H,EAAI,EAAE,CACZ,IAAK,IAAI8H,EAAI,EAAGA,EAAIxF,EAAKiC,MAAM,CAAEuD,IAAK,CAClC,IAAMtD,EAAIlC,CAAI,CAACwF,EAAE,CAAC,EAAE,CACdrD,EAAInC,CAAI,CAACwF,EAAE,CAAC,EAAE,CACpB,GAAI,AAAa,UAAb,OAAOtD,GAAkB,AAAa,UAAb,OAAOC,EAEhC,GAAIqD,AAAM,IAANA,EACA9H,EAAE+H,IAAI,CAAC,CAAC,IAAKvD,EAAGC,EAAE,OAEjB,GAAIqD,IAAMxF,EAAKiC,MAAM,CAAG,EACzBvE,EAAE+H,IAAI,CAAC,CAAC,IAAKvD,EAAGC,EAAE,OAGjB,GAAIoD,EAAG,CACR,IAAMG,EAAU1F,CAAI,CAACwF,EAAI,EAAE,CACrBG,EAAU3F,CAAI,CAACwF,EAAI,EAAE,CAC3B,GAAIE,GAAWC,EAAS,CACpB,IAAMC,EAAKF,CAAO,CAAC,EAAE,CAAEG,EAAKH,CAAO,CAAC,EAAE,CAAEI,EAAKH,CAAO,CAAC,EAAE,CAAEI,EAAKJ,CAAO,CAAC,EAAE,CAExE,GAAI,AAAc,UAAd,OAAOC,GACP,AAAc,UAAd,OAAOE,GACP,AAAc,UAAd,OAAOD,GACP,AAAc,UAAd,OAAOE,GACPH,IAAOE,GACPD,IAAOE,EAAI,CACX,IAAMC,EAAaJ,EAAKE,EAAK,EAAI,GAAIG,EAAaJ,EAAKE,EAAK,EAAI,GAChErI,EAAE+H,IAAI,CAAC,CACH,IACAvD,EAAI8D,EAAa9G,KAAKC,GAAG,CAACD,KAAKgH,GAAG,CAAChE,EAAI0D,GAAKL,GAC5CpD,EAAI8D,EAAa/G,KAAKC,GAAG,CAACD,KAAKgH,GAAG,CAAC/D,EAAI0D,GAAKN,GAC/C,CAAE,CACC,IACArD,EACAC,EACAD,EACAC,EACAD,EAAI8D,EAAa9G,KAAKC,GAAG,CAACD,KAAKgH,GAAG,CAAChE,EAAI4D,GAAKP,GAC5CpD,EAAI8D,EAAa/G,KAAKC,GAAG,CAACD,KAAKgH,GAAG,CAAC/D,EAAI4D,GAAKR,GAC/C,CACL,CACJ,CAEJ,MAEI7H,EAAE+H,IAAI,CAAC,CAAC,IAAKvD,EAAGC,EAAE,CAG9B,CACA,OAAOzE,CACX,CAIA,EAiBM,CAAEyI,KAAAA,CAAI,CAAE,CAAIxH,IAMZ,CAAEQ,IAAKiH,CAAwB,CAAEnH,IAAKoH,CAAwB,CAAEH,IAAAA,CAAG,CAAE,CAAGhH,KAyB9E,SAASoH,EAAuBhD,CAAS,CAAEY,CAAI,CAAEqC,CAAO,EACpD,IAAMpH,EAAM+E,EAAO,KACfsC,EAAOD,GAAW,EACtBE,EAAQnD,EAAUrB,MAAM,CAAG,EAC3ByE,EAAQC,EACR,KAAOH,GAAQC,GAGX,GAAIE,AADJA,CAAAA,EAAMxH,EAAMmE,CAAS,CADrBoD,EAAS,AAACD,EAAQD,GAAS,EACE,CAACtC,IAAI,AAAD,EACvB,EACNsC,EAAOE,EAAS,OAEf,IAAIC,CAAAA,EAAM,CAAA,EAIX,OAAOD,EAHPD,EAAQC,EAAS,EAMzB,OAAOF,EAAO,EAAIA,EAAO,EAAI,CACjC,CAsCA,SAASI,EAAsBtD,CAAS,CAAEzB,CAAK,EAC3C,IAAI2D,EAAIc,EAAuBhD,EAAWzB,EAAMK,CAAC,CAAG,GAAK,EACzD,KAAOsD,KACH,KAzBqBqB,EAyBrB,GAAIvD,CAAS,CAACkC,EAAE,CAACrB,IAAI,EAAItC,EAAMK,CAAC,GAzBX2E,EA2BGvD,CAAS,CAACkC,EAAE,CA1BhC3D,AA0BkCA,EA1B5BK,CAAC,EAAI2E,EAAS1C,IAAI,EAC5BtC,AAyBsCA,EAzBhCK,CAAC,EAAI2E,EAAS3C,IAAI,EACxBrC,AAwBsCA,EAxBhCM,CAAC,EAAI0E,EAASvC,IAAI,EACxBzC,AAuBsCA,EAvBhCM,CAAC,EAAI0E,EAASxC,IAAI,EAwBpB,OAAOmB,CACX,CAEJ,OAAO,EACX,CAaA,SAASsB,EAAiBC,CAAQ,EAC9B,IAAM/G,EAAO,EAAE,CACf,GAAI+G,EAAS9E,MAAM,CAAE,CACjBjC,EAAKyF,IAAI,CAAC,CAAC,IAAKsB,CAAQ,CAAC,EAAE,CAACC,KAAK,CAAC9E,CAAC,CAAE6E,CAAQ,CAAC,EAAE,CAACC,KAAK,CAAC7E,CAAC,CAAC,EACzD,IAAK,IAAIqD,EAAI,EAAGA,EAAIuB,EAAS9E,MAAM,CAAE,EAAEuD,EACnCxF,EAAKyF,IAAI,CAAC,CAAC,IAAKsB,CAAQ,CAACvB,EAAE,CAACyB,GAAG,CAAC/E,CAAC,CAAE6E,CAAQ,CAACvB,EAAE,CAACyB,GAAG,CAAC9E,CAAC,CAAC,CAE7D,CACA,OAAOnC,CACX,CAgBA,SAASkH,EAAsBL,CAAQ,CAAEM,CAAM,EAC3CN,EAASxC,IAAI,CAAGgC,EAAyBQ,EAASxC,IAAI,CAAE8C,EAAO9C,IAAI,EACnEwC,EAASvC,IAAI,CAAG8B,EAAyBS,EAASvC,IAAI,CAAE6C,EAAO7C,IAAI,EACnEuC,EAAS3C,IAAI,CAAGmC,EAAyBQ,EAAS3C,IAAI,CAAEiD,EAAOjD,IAAI,EACnE2C,EAAS1C,IAAI,CAAGiC,EAAyBS,EAAS1C,IAAI,CAAEgD,EAAOhD,IAAI,CACvE,CAoDA,IAAMiD,EAAgB,SAAUJ,CAAK,CAAEC,CAAG,CAAEzH,CAAO,EAC/C,IAAMuH,EAAW,EAAE,CAAE1D,EAAiB7D,EAAQ6D,cAAc,CAAEgE,EAAkBT,EAAsBvD,EAAgB2D,GAAQM,EAAgBV,EAAsBvD,EAAgB4D,GAChLM,EAAYC,EAAMrB,EAAK3G,EAAQkF,eAAe,CAAEwB,EAAIe,EAAI/E,CAAC,CAAG8E,EAAM9E,CAAC,EAAIgE,EAAIe,EAAI9E,CAAC,CAAG6E,EAAM7E,CAAC,GAAK,IAAM,IAAKsF,EAA4BC,EAAUC,EAAQC,EAO5J,SAASC,EAAcvI,CAAI,CAAEwI,CAAO,CAAEvI,CAAE,CAAEwI,CAAK,CAAEC,CAAM,EACnD,IAAMnG,EAAQ,CACVK,EAAG5C,EAAK4C,CAAC,CACTC,EAAG7C,EAAK6C,CAAC,AACb,EAEA,OADAN,CAAK,CAACiG,EAAQ,CAAGvI,CAAE,CAACwI,GAASD,EAAQ,CAAIE,CAAAA,GAAU,CAAA,EAC5CnG,CACX,CAMA,SAASoG,EAASpB,CAAQ,CAAEhF,CAAK,CAAEqG,CAAS,EACxC,IAAMP,EAASzB,EAAIrE,CAAK,CAACqG,EAAU,CAAGrB,CAAQ,CAACqB,EAAY,MAAM,EAC7DhC,EAAIrE,CAAK,CAACqG,EAAU,CAAGrB,CAAQ,CAACqB,EAAY,MAAM,EACtD,OAAOL,EAAchG,EAAOqG,EAAWrB,EAAUqB,EAAaP,CAAAA,EAAS,MAAQ,KAAI,EAAIA,EAAS,EAAI,GACxG,CAEIL,EAAgB,IAGhBC,EAAa,CACTP,MAFJU,EAAWO,EADG5E,CAAc,CAACiE,EAAc,CACVL,EAAKO,GAGlCP,IAAKA,CACT,EACAW,EAAWF,GAGXE,EAAWX,EAIXI,EAAkB,KAElBK,EAAWO,EADXR,EAAgBpE,CAAc,CAACgE,EAAgB,CACZL,EAAOQ,GAC1CT,EAAStB,IAAI,CAAC,CACVuB,MAAOA,EACPC,IAAKS,CACT,GAKAA,CAAQ,CAACF,EAAI,EAAIR,CAAK,CAACQ,EAAI,EAEvBE,CAAQ,CAACF,EAAI,EAAII,CAAQ,CAACJ,EAAI,GAE9BG,EAASX,CAAK,CADdQ,EAAMA,AAAQ,MAARA,EAAc,IAAM,IACP,CAAGP,CAAG,CAACO,EAAI,CAC9BT,EAAStB,IAAI,CAAC,CACVuB,MAAOU,EACPT,IAAKY,EAAcH,EAAUF,EAAKC,EAAeD,EAAOG,CAAAA,EAAS,MAAQ,KAAI,EAAIA,EAAS,EAAI,GAClG,GAEAH,EAAMA,AAAQ,MAARA,EAAc,IAAM,MAKlC,IAAMW,EAAepB,EAAS9E,MAAM,CAChC8E,CAAQ,CAACA,EAAS9E,MAAM,CAAG,EAAE,CAACgF,GAAG,CACjCD,EACJU,EAAWG,EAAcM,EAAcX,EAAKI,GAC5Cb,EAAStB,IAAI,CAAC,CACVuB,MAAOmB,EACPlB,IAAKS,CACT,GAGA,IAAMU,EAAYP,EAAcH,EADhCF,EAAMA,AAAQ,MAARA,EAAc,IAAM,IACqBI,GAQ/C,OAPAb,EAAStB,IAAI,CAAC,CACVuB,MAAOU,EACPT,IAAKmB,CACT,GAEArB,EAAStB,IAAI,CAAC8B,GAEP,CACHvH,KAFSqF,EAAqBC,WAAW,CAACwB,EAAiBC,GAAWvH,EAAQ8C,MAAM,EAGpFgB,UAAWyD,CACf,CACJ,EAgCA,SAASsB,EAAUrB,CAAK,CAAEC,CAAG,CAAEzH,CAAO,EAqBlC,IAAM8I,EAASnC,EAAK3G,EAAQkF,eAAe,CAAEwB,EAAIe,EAAI/E,CAAC,CAAG8E,EAAM9E,CAAC,EAAIgE,EAAIe,EAAI9E,CAAC,CAAG6E,EAAM7E,CAAC,GAAIqF,EAAMc,EAAS,IAAM,IAAKC,EAAc,EAAE,CAGrIC,EAAUhJ,EAAQwE,eAAe,CAAEyE,EAAWrC,EAAyBY,EAAM9E,CAAC,CAAE+E,EAAI/E,CAAC,EAAIsG,EAAQE,QAAQ,CAAG,GAAIC,EAAWtC,EAAyBW,EAAM9E,CAAC,CAAE+E,EAAI/E,CAAC,EAAIsG,EAAQE,QAAQ,CAAG,GAAIE,EAAWxC,EAAyBY,EAAM7E,CAAC,CAAE8E,EAAI9E,CAAC,EAAIqG,EAAQK,SAAS,CAAG,GAAIC,EAAWzC,EAAyBW,EAAM7E,CAAC,CAAE8E,EAAI9E,CAAC,EAAIqG,EAAQK,SAAS,CAAG,GACjV9B,EAAUY,EAAQoB,EAAmBC,EAAqB,CAAA,EAG9D3F,EAAiB7D,EAAQ6D,cAAc,CAAEiE,EAAgBhB,EAAuBjD,EAAgBsF,GAC1FtB,EAAkBf,EAAuBjD,EAAgBoF,GAO/D,SAASQ,EAAWvJ,CAAS,CAAEC,CAAO,CAAEuJ,CAAY,EAChD,IACIC,EAAYC,EAAWC,EAAcC,EADnCC,EAAkB7J,EAAUwC,CAAC,CAAGvC,EAAQuC,CAAC,CAAG,EAAI,EAElDxC,CAAAA,EAAUwC,CAAC,CAAGvC,EAAQuC,CAAC,EACvBiH,EAAazJ,EACb0J,EAAYzJ,IAGZwJ,EAAaxJ,EACbyJ,EAAY1J,GAEZA,EAAUyC,CAAC,CAAGxC,EAAQwC,CAAC,EACvBmH,EAAc5J,EACd2J,EAAe1J,IAGf2J,EAAc3J,EACd0J,EAAe3J,GAInB,IAAI8F,EAAI+D,EAAkB,EAEtBnD,EAAyBE,EAAuBjD,EAAgB+F,EAAUlH,CAAC,EAAGmB,EAAepB,MAAM,CAAG,GAGtG,EAEJ,KAAOoB,CAAc,CAACmC,EAAE,EAAK+D,CAAAA,EAAkB,GAAKlG,CAAc,CAACmC,EAAE,CAACtB,IAAI,EAAIkF,EAAUlH,CAAC,EACrFqH,EAAkB,GAAKlG,CAAc,CAACmC,EAAE,CAACrB,IAAI,EAAIgF,EAAWjH,CAAC,AAADA,GAAI,CAGhE,GAAImB,CAAc,CAACmC,EAAE,CAACtB,IAAI,EAAIkF,EAAUlH,CAAC,EACrCmB,CAAc,CAACmC,EAAE,CAACrB,IAAI,EAAIgF,EAAWjH,CAAC,EACtCmB,CAAc,CAACmC,EAAE,CAACnB,IAAI,EAAIgF,EAAalH,CAAC,EACxCkB,CAAc,CAACmC,EAAE,CAAClB,IAAI,EAAIgF,EAAYnH,CAAC,CAAE,CACzC,GAAI+G,EACA,MAAO,CACH/G,EAAGzC,EAAUyC,CAAC,CACdD,EAAGxC,EAAUwC,CAAC,CAAGvC,EAAQuC,CAAC,CACtBmB,CAAc,CAACmC,EAAE,CAACtB,IAAI,CAAG,EACzBb,CAAc,CAACmC,EAAE,CAACrB,IAAI,CAAG,EAC7B0C,SAAUxD,CAAc,CAACmC,EAAE,AAC/B,EAGJ,MAAO,CACHtD,EAAGxC,EAAUwC,CAAC,CACdC,EAAGzC,EAAUyC,CAAC,CAAGxC,EAAQwC,CAAC,CACtBkB,CAAc,CAACmC,EAAE,CAACnB,IAAI,CAAG,EACzBhB,CAAc,CAACmC,EAAE,CAAClB,IAAI,CAAG,EAC7BuC,SAAUxD,CAAc,CAACmC,EAAE,AAC/B,CACJ,CACAA,GAAK+D,CACT,CACA,OAAO5J,CACX,CA6BA,SAAS6J,EAAkB3C,CAAQ,CAAEnH,CAAS,CAAEC,CAAO,CAAE2I,CAAM,CAAEnB,CAAM,EACnE,IAAMsC,EAAatC,EAAOuC,IAAI,CAAEzF,EAAakD,EAAOwC,IAAI,CAAEnC,EAAMc,EAAS,IAAM,IAAKsB,EAAa,CAAE1H,EAAGxC,EAAUwC,CAAC,CAAEC,EAAGzC,EAAUyC,CAAC,AAAC,EAAG0H,EAAa,CAAE3H,EAAGxC,EAAUwC,CAAC,CAAEC,EAAGzC,EAAUyC,CAAC,AAAC,EAAG2H,EAAqBjD,CAAQ,CAACW,EAAM,MAAM,EAC5NiC,CAAU,CAACjC,EAAM,MAAM,CAAEuC,EAAqBlD,CAAQ,CAACW,EAAM,MAAM,EACnEiC,CAAU,CAACjC,EAAM,MAAM,CAAEwC,EAAqBnD,CAAQ,CAACW,EAAM,MAAM,EACnEvD,CAAU,CAACuD,EAAM,MAAM,CAAEyC,EAAqBpD,CAAQ,CAACW,EAAM,MAAM,EACnEvD,CAAU,CAACuD,EAAM,MAAM,CAG3B0C,EAAchE,EAAIW,CAAQ,CAACW,EAAM,MAAM,CAAG9H,CAAS,CAAC8H,EAAI,EAAG2C,EAAcjE,EAAIW,CAAQ,CAACW,EAAM,MAAM,CAAG9H,CAAS,CAAC8H,EAAI,EAGnHG,EAASzB,AAAiC,GAAjCA,EAAIgE,EAAcC,GACvBzK,CAAS,CAAC8H,EAAI,CAAG7H,CAAO,CAAC6H,EAAI,CAC7B2C,EAAcD,CAGlBL,CAAAA,CAAU,CAACrC,EAAI,CAAGX,CAAQ,CAACW,EAAM,MAAM,CACvCoC,CAAU,CAACpC,EAAI,CAAGX,CAAQ,CAACW,EAAM,MAAM,CACvC,IAAM4C,EAAWnB,EAAWvJ,EAAWmK,EAAYvB,EAAO,CAACd,EAAI,GAC3DqC,CAAU,CAACrC,EAAI,CAAE6C,EAAWpB,EAAWvJ,EAAWkK,EAAYtB,EAAO,CAACd,EAAI,GAC1EoC,CAAU,CAACpC,EAAI,CAenB,OAdAG,EAASyC,EACJC,CAAAA,GAAW1C,EACX0C,CAAAA,GAAmB1C,EAKxBA,EAASoC,EACJD,CAAAA,GAAqBnC,EACrBmC,CAAAA,GAA6BnC,EAElCA,EAASsC,EACJD,CAAAA,GAAqBrC,EACrBqC,CAAAA,GAA6BrC,CAEtC,CA2JA,IAVKL,CAAAA,EAAgBV,EAJrBvD,EACIA,EAAeiH,KAAK,CAACjD,EAAiBC,EAAgB,GAGCL,EAAG,EAAK,KAC/D8B,EAAoBwB,AArBxB,SAA6B1D,CAAQ,CAAEhF,CAAK,CAAE2I,CAAS,EACnD,IAAMlC,EAASlC,EAAyBS,EAAS1C,IAAI,CAAGtC,EAAMK,CAAC,CAAEL,EAAMK,CAAC,CAAG2E,EAAS3C,IAAI,EACpFkC,EAAyBS,EAASvC,IAAI,CAAGzC,EAAMM,CAAC,CAAEN,EAAMM,CAAC,CAAG0E,EAASxC,IAAI,EAG1EsD,EAAS6B,EAAkB3C,EAAUhF,EAAO2I,EAAWlC,EAH+B,CACrFoB,KAAMlK,EAAQyE,UAAU,CACxB0F,KAAMnK,EAAQyE,UAAU,AAC5B,GACA,OAAOqE,EAAS,CACZnG,EAAGN,EAAMM,CAAC,CACVD,EAAG2E,CAAQ,CAACc,EAAS,OAAS,OAAO,CAAIA,CAAAA,EAAS,EAAI,EAAC,CAC3D,EAAI,CACAzF,EAAGL,EAAMK,CAAC,CACVC,EAAG0E,CAAQ,CAACc,EAAS,OAAS,OAAO,CAAIA,CAAAA,EAAS,EAAI,EAAC,CAC3D,CACJ,EAQ4CtE,CAAc,CAACiE,EAAc,CAAEL,EAAKD,GAC5EuB,EAAY9C,IAAI,CAAC,CACbwB,IAAKA,EACLD,MAAO+B,CACX,GACA9B,EAAM8B,GAIH,AAACzB,CAAAA,EAAgBV,EAAsBvD,EAAgB4D,EAAG,EAAK,IAClEU,EAASV,CAAG,CAACO,EAAI,CAAGR,CAAK,CAACQ,EAAI,CAAG,EAKjCuB,AAJAA,CAAAA,EAAoB,CAChB7G,EAAG+E,EAAI/E,CAAC,CACRC,EAAG8E,EAAI9E,CAAC,AACZ,CAAA,CACiB,CAACqF,EAAI,CAAGnE,CAAc,CAACiE,EAAc,CAACK,EAASH,EAAM,MAAQA,EAAM,MAAM,CAAIG,CAAAA,EAAS,EAAI,EAAC,EAC5GY,EAAY9C,IAAI,CAAC,CACbwB,IAAKA,EACLD,MAAO+B,CACX,GACA9B,EAAM8B,EAMV,MAAO,CACH/I,KAAM8G,EAFVC,EAAWA,AAFXA,CAAAA,EAAW0D,AAnKX,SAASA,EAAY/K,CAAS,CAAEC,CAAO,CAAE2I,CAAM,MAcvCoC,EAAO3D,EAAUW,EAAUiD,EAAgBC,EAAoBC,EAA0BC,EAZ7F,GAAIpL,EAAUwC,CAAC,GAAKvC,EAAQuC,CAAC,EAAIxC,EAAUyC,CAAC,GAAKxC,EAAQwC,CAAC,CACtD,MAAO,EAAE,CAEb,IAAMqF,EAAMc,EAAS,IAAM,IAAKyC,EAAiBvL,EAAQgF,eAAe,CAACC,MAAM,CAAE0C,EAAS,CACtFuC,KAAM,CACFxF,KAAMuE,EACNtE,KAAMwE,EACNtE,KAAMuE,EACNtE,KAAMwE,CACV,EACAa,KAAMnK,EAAQyE,UAAU,AAC5B,EAkGA,MA3FI2G,AAFJA,CAAAA,EACIhE,EAAsBvD,EAAgB3D,EAAS,EAC1B,IAErBiL,EAAiBnB,EADjBoB,EAAqBvH,CAAc,CAACuH,EAAmB,CACAlL,EAAWC,EAAS2I,EAAQnB,GAEnFD,EAAsB0D,EAAoBpL,EAAQyE,UAAU,EAC5D6G,EAAkBxC,EAAS,CACvBnG,EAAGzC,EAAUyC,CAAC,CACdD,EAAG0I,CAAkB,CAACD,EAAiB,OAAS,OAAO,CAClDA,CAAAA,EAAiB,EAAI,EAAC,CAC/B,EAAI,CACAzI,EAAGxC,EAAUwC,CAAC,CACdC,EAAGyI,CAAkB,CAACD,EAAiB,OAAS,OAAO,CAClDA,CAAAA,EAAiB,EAAI,EAAC,CAC/B,EAGAE,CAAAA,EAA2BjE,EAAsBvD,EAAgByH,EAAe,EACjD,KAG3B5D,EAFA2D,EAA2BxH,CAAc,CAACwH,EAAyB,CAEnBrL,EAAQyE,UAAU,EAElE6G,CAAe,CAACtD,EAAI,CAAGmD,EAAiBtE,EAAyBuE,CAAkB,CAACpD,EAAM,MAAM,CAAGuD,EAAiB,EAAG,AAACF,CAAAA,CAAwB,CAACrD,EAAM,MAAM,CACzJoD,CAAkB,CAACpD,EAAM,MAAM,AAAD,EAAK,GACnCpB,EAA0BwE,CAAkB,CAACpD,EAAM,MAAM,CAAGuD,EAAiB,EAAK,AAACF,CAAAA,CAAwB,CAACrD,EAAM,MAAM,CACpHoD,CAAkB,CAACpD,EAAM,MAAM,AAAD,EAAK,GAIvC9H,EAAUwC,CAAC,GAAK4I,EAAgB5I,CAAC,EACjCxC,EAAUyC,CAAC,GAAK2I,EAAgB3I,CAAC,EAC7B6G,GACA8B,CAAAA,CAAe,CAACtD,EAAI,CAAGmD,EACnBtE,EAAyBuE,CAAkB,CAACpD,EAAM,MAAM,CAAEqD,CAAwB,CAACrD,EAAM,MAAM,EAAI,EACnGpB,EAAyBwE,CAAkB,CAACpD,EAAM,MAAM,CAAEqD,CAAwB,CAACrD,EAAM,MAAM,EAAI,CAAA,EAG3GwB,EAAqB,CAACA,GAKtBA,EAAqB,CAAA,GAG7BjC,EAAW,CAAC,CACJC,MAAOtH,EACPuH,IAAK6D,CACT,EAAE,GAGNJ,EAAQzB,EAAWvJ,EAAW,CAC1BwC,EAAGoG,EAAS3I,EAAQuC,CAAC,CAAGxC,EAAUwC,CAAC,CACnCC,EAAGmG,EAAS5I,EAAUyC,CAAC,CAAGxC,EAAQwC,CAAC,AACvC,EAAGmG,GACHvB,EAAW,CAAC,CACJC,MAAOtH,EACPuH,IAAK,CACD/E,EAAGwI,EAAMxI,CAAC,CACVC,EAAGuI,EAAMvI,CAAC,AACd,CACJ,EAAE,CAEFuI,CAAK,CAACpC,EAAS,IAAM,IAAI,GAAK3I,CAAO,CAAC2I,EAAS,IAAM,IAAI,GAEzDqC,EAAiBnB,EAAkBkB,EAAM7D,QAAQ,CAAE6D,EAAO/K,EAAS,CAAC2I,EAAQnB,GAE5ED,EAAsBwD,EAAM7D,QAAQ,CAAErH,EAAQyE,UAAU,EACxDyD,EAAW,CACPxF,EAAGoG,EACCoC,EAAMxI,CAAC,CACPwI,EAAM7D,QAAQ,CAAC8D,EAAiB,OAAS,OAAO,CAC3CA,CAAAA,EAAiB,EAAI,EAAC,EAC/BxI,EAAGmG,EACCoC,EAAM7D,QAAQ,CAAC8D,EAAiB,OAAS,OAAO,CAC3CA,CAAAA,EAAiB,EAAI,EAAC,EAC3BD,EAAMvI,CAAC,AACf,EAIAmG,EAAS,CAACA,EACVvB,EAAWA,EAAS7B,MAAM,CAACuF,EAAY,CACnCvI,EAAGwI,EAAMxI,CAAC,CACVC,EAAGuI,EAAMvI,CAAC,AACd,EAAGuF,EAAUY,MAKrBvB,EAAWA,EAAS7B,MAAM,CAACuF,EAAY1D,CAAQ,CAACA,EAAS9E,MAAM,CAAG,EAAE,CAACgF,GAAG,CAAEtH,EAAS,CAAC2I,GAExF,EAmDuBtB,EAAOC,EAAKqB,EAAM,EAErBpD,MAAM,CAACqD,EAAYyC,OAAO,KAG1C1H,UAAWyD,CACf,CACJ,CA7WAK,EAAc7D,iBAAiB,CAAG,CAAA,EA8WlC8E,EAAU9E,iBAAiB,CAAG,CAAA,EAkQD,IAAM0H,EA3NR,CAmBvBxH,WAAY,CA0ERpC,KAAM,WAMNiB,OAAQ,EAORU,UAAW,EASX+B,OAAQ,CA+CJ/C,QAAS,CAAA,EAMTkJ,MAAO,SAMPC,cAAe,SAIfC,OAAQ,CAAA,EAIRpI,UAAW,CACf,EASAa,YAAa,CAITnB,OAAQ,SACZ,EASAoB,UAAW,CAIPpB,OAAQ,cACZ,CACJ,CACJ,EAgEM,CAAE2I,WAAAA,CAAU,CAAE,CAAI1M,IAElB,CAAEC,QAAS0M,CAA6B,CAAEzM,MAAO0M,CAA2B,CAAEzM,MAAO0M,CAA2B,CAAE,CAAI7M,IAmB5H,SAAS8M,EAAW5J,CAAK,EACrB,IAAM6J,EAAY7J,EAAM6J,SAAS,CAEjC,GAAIA,EACA,MAAO,CACHxH,KAAMwH,EAAUxJ,CAAC,EAAI,EACrBiC,KAAM,AAACuH,CAAAA,EAAUxJ,CAAC,EAAI,CAAA,EAAMwJ,CAAAA,EAAUjK,KAAK,EAAI,CAAA,EAC/C4C,KAAMqH,EAAUvJ,CAAC,EAAI,EACrBmC,KAAM,AAACoH,CAAAA,EAAUvJ,CAAC,EAAI,CAAA,EAAMuJ,CAAAA,EAAUhK,MAAM,EAAI,CAAA,CACpD,EAGJ,IAAMiK,EAAK9J,EAAM+J,OAAO,EAAI/J,EAAM+J,OAAO,CAACC,OAAO,GACjD,OAAOF,EAAK,CACRzH,KAAMrC,EAAMiK,KAAK,CAAGH,EAAGlK,KAAK,CAAG,EAC/B0C,KAAMtC,EAAMiK,KAAK,CAAGH,EAAGlK,KAAK,CAAG,EAC/B4C,KAAMxC,EAAMkK,KAAK,CAAGJ,EAAGjK,MAAM,CAAG,EAChC4C,KAAMzC,EAAMkK,KAAK,CAAGJ,EAAGjK,MAAM,CAAG,CACpC,EAAI,IACR,EA0BA,AAAC,SAAU1E,CAAqB,EAwC5B,SAASgP,EAA8BC,CAAa,EAChD,IACI/J,EAAGC,EADDwJ,EAAKF,EAAW,IAAI,EAE1B,OAAQQ,EAAcf,KAAK,EACvB,IAAK,QACDhJ,EAAI,OACJ,KACJ,KAAK,OACDA,EAAI,MACZ,CACA,OAAQ+J,EAAcd,aAAa,EAC/B,IAAK,MACDhJ,EAAI,OACJ,KACJ,KAAK,SACDA,EAAI,MACZ,CACA,MAAO,CACHD,EAAGA,EAAIyJ,CAAE,CAACzJ,EAAE,CAAG,AAACyJ,CAAAA,EAAGzH,IAAI,CAAGyH,EAAGxH,IAAI,AAAD,EAAK,EACrChC,EAAGA,EAAIwJ,CAAE,CAACxJ,EAAE,CAAG,AAACwJ,CAAAA,EAAGtH,IAAI,CAAGsH,EAAGrH,IAAI,AAAD,EAAK,CACzC,CACJ,CAgBA,SAAS4H,EAAwBC,CAAE,CAAEC,CAAE,EACnC,IAAI5K,EAUJ,MATI,CAAC8J,EAA8Bc,IAC/B5K,CAAAA,EAAMiK,EAAW,IAAI,CAAA,GAEjBW,CAAAA,EAAK,CACDlK,EAAG,AAACV,CAAAA,EAAI0C,IAAI,CAAG1C,EAAI2C,IAAI,AAAD,EAAK,EAC3BhC,EAAG,AAACX,CAAAA,EAAI6C,IAAI,CAAG7C,EAAI8C,IAAI,AAAD,EAAK,CAC/B,CAAA,EAGDpF,KAAKmN,KAAK,CAACD,EAAGjK,CAAC,CAAGgK,EAAGhK,CAAC,CAAEgK,EAAGjK,CAAC,CAAGkK,EAAGlK,CAAC,CAC9C,CAsBA,SAASoK,EAAqB/K,CAAO,CAAEgL,CAAY,CAAEzK,CAAM,EACvD,IAAM0K,EAAQtN,AAAU,EAAVA,KAAKuN,EAAE,CAAQd,EAAKF,EAAW,IAAI,EAAGiB,EAAYf,EAAGxH,IAAI,CAAGwH,EAAGzH,IAAI,CAAEyI,EAAahB,EAAGrH,IAAI,CAAGqH,EAAGtH,IAAI,CAAEuI,EAAQ1N,KAAKmN,KAAK,CAACM,EAAYD,GAAYG,EAAgBH,EAAY,EAAKI,EAAiBH,EAAa,EAAKI,EAAuBpB,EAAGzH,IAAI,CAAG2I,EAAeG,EAAqBrB,EAAGtH,IAAI,CAAGyI,EAAgBG,EAAY,CACzU/K,EAAG6K,EACH5K,EAAG6K,CACP,EACIE,EAAQ3L,EAAS4L,EAAW,EAAGC,EAAoB,CAAA,EAAOC,EAAU,EAAGC,EAAU,EACrF,KAAOJ,EAAQ,CAAChO,KAAKuN,EAAE,EACnBS,GAASV,EAEb,KAAOU,EAAQhO,KAAKuN,EAAE,EAClBS,GAASV,EAoCb,OAlCAW,EAAWjO,KAAKqO,GAAG,CAACL,GAChB,AAACA,EAAQ,CAACN,GAAWM,GAASN,GAE9BU,EAAU,GACVF,EAAoB,CAAA,GAEfF,EAAQN,GAASM,GAAUhO,KAAKuN,EAAE,CAAGG,EAE1CU,EAAU,GAELJ,EAAShO,KAAKuN,EAAE,CAAGG,GAAUM,GAAS,CAAEhO,CAAAA,KAAKuN,EAAE,CAAGG,CAAI,GAE3DS,EAAU,GACVD,EAAoB,CAAA,GAIpBC,EAAU,GAGVD,GACAH,EAAU/K,CAAC,EAAImL,EAAWR,EAC1BI,EAAU9K,CAAC,EAAImL,EAAWT,EAAiBM,IAG3CF,EAAU/K,CAAC,EAAImL,AAAWV,EAAc,CAAA,EAAMQ,CAAO,EAAtCE,EACfJ,EAAU9K,CAAC,EAAImL,EAAWR,GAE1BhL,EAAOI,CAAC,GAAK6K,GACbE,CAAAA,EAAU/K,CAAC,CAAGJ,EAAOI,CAAC,AAADA,EAErBJ,EAAOK,CAAC,GAAK6K,GACbC,CAAAA,EAAU9K,CAAC,CAAGL,EAAOK,CAAC,AAADA,EAElB,CACHD,EAAG+K,EAAU/K,CAAC,CAAIqK,EAAerN,KAAKsO,GAAG,CAACN,GAC1C/K,EAAG8K,EAAU9K,CAAC,CAAIoK,EAAerN,KAAKuO,GAAG,CAACP,EAC9C,CACJ,CAvIAlQ,EAAsB0Q,OAAO,CAnB7B,SAAiBC,CAAU,CAAEC,CAAe,CAAEC,CAAU,EACpD,IAAMC,EAAaD,EAAWxP,SAAS,AAClCyP,CAAAA,EAAW/L,wBAAwB,GAEpC4L,EAAWtP,SAAS,CAAC0P,SAAS,CAACtI,IAAI,CAAC,SAAU7F,CAAK,EAE3CJ,AAA+B,CAAA,IAA/BA,AADYI,EAAMJ,OAAO,CACjBiE,UAAU,CAACzB,OAAO,GAhCtCpC,CAAAA,AAiCuBA,EAjCjBJ,OAAO,CAACM,UAAU,EACxBF,AAgCuBA,EAhCjBC,MAAM,CAACmO,MAAM,CAAC,SAAUC,CAAG,CAAEpO,CAAM,EAKrC,OAJIA,EAAOL,OAAO,EACdgM,EAA4B,CAAA,EAAO3L,EAAOL,OAAO,CAACiE,UAAU,CAAG5D,EAAOL,OAAO,CAACiE,UAAU,EACpF,CAAC,EAAI5D,EAAOL,OAAO,CAACM,UAAU,EAE/BmO,GAAOpO,EAAOL,OAAO,EAAIK,EAAOL,OAAO,CAACM,UAAU,AAC7D,EAAG,CAAA,EAAK,IACR0L,EAA4B,CAAA,EAAO5L,AAyBZA,EAzBkBJ,OAAO,CAACiE,UAAU,CAAG7D,AAyBvCA,EAzB6CJ,OAAO,CAACiE,UAAU,EAAI,CAAC,EAAI7D,AAyBxEA,EAzB8EJ,OAAO,CAACM,UAAU,EACvHyL,EAA4B,0GAyBhB,IAAI,CAACzL,UAAU,CAAG,IAAI8N,EAAgB,IAAI,EAC1C,IAAI,CAAC9N,UAAU,CAACoO,MAAM,CAAC,CAAA,GAE/B,GACAJ,EAAWzL,eAAe,CAAGiK,EAC7BwB,EAAW/L,wBAAwB,CAAGiK,EACtC8B,EAAW1L,kBAAkB,CAAG8J,EAEhCb,EAAWJ,GAEnB,CAyIJ,EAAGjO,GAA0BA,CAAAA,EAAwB,CAAC,CAAA,GAMzB,IAAMmR,EAAyBnR,EAG5D,IAAIoR,EAA+FjR,EAAoB,KACnHkR,EAAmHlR,EAAoBI,CAAC,CAAC6Q,GAkB7I,GAAM,CAAEE,SAAAA,CAAQ,CAAE1P,QAAS2P,CAAkB,CAAEpI,KAAMqI,CAAe,CAAEC,MAAAA,CAAK,CAAE,CAAI9P,IAM3E+P,EAAiBxP,KAAKD,GAAG,CAAE0P,EAAiBzP,KAAKC,GAAG,AAgH1D,OAAMyP,EAMF,OAAOlB,QAAQC,CAAU,CAAEE,CAAU,CAAE,CACnCM,EAAsBT,OAAO,CAACC,EAAYiB,EAAYf,EAC1D,CAMAxO,YAAYO,CAAK,CAAE,CACf,IAAI,CAACH,IAAI,CAACG,EACd,CAcAH,KAAKG,CAAK,CAAE,CAER,IAAI,CAACA,KAAK,CAAGA,EAEb,IAAI,CAACiP,WAAW,CAAG,EAAE,CAErBP,EAAS1O,EAAO,SAAU,WACtB,IAAI,CAACE,UAAU,CAACoO,MAAM,EAC1B,EACJ,CAUAA,OAAOY,CAAW,CAAE,CAChB,IAAMlP,EAAQ,IAAI,CAACA,KAAK,CAAEE,EAAa,IAAI,CAAEiP,EAAiBjP,EAAW+O,WAAW,AAEpF/O,CAAAA,EAAW+O,WAAW,CAAG,EAAE,CAC3BjP,EAAMC,MAAM,CAACmP,OAAO,CAAC,SAAUnP,CAAM,EAC7BA,EAAOoP,OAAO,EAAI,CAACpP,EAAOL,OAAO,CAAC0P,UAAU,EAC5CrP,EAAOsP,MAAM,CAACH,OAAO,CAAC,SAAUnN,CAAK,EACjC,IAUItC,EAVE6P,EAAoBvN,EAAMrC,OAAO,AAGnC4P,CAAAA,GAAqBA,EAAkBC,UAAU,EACjDD,CAAAA,EAAkBE,OAAO,CAAGF,EACvBC,UAAU,AAAD,EAElB,IAAME,EAAW1N,EAAMrC,OAAO,EAAE8P,QAC5Bb,EAAM5M,EAAMrC,OAAO,CAAC8P,OAAO,EAC3B,EAAE,AAEFzN,CAAAA,EAAMoN,OAAO,EAAIpN,AAAmB,CAAA,IAAnBA,EAAM2N,QAAQ,EAC/BD,EAASP,OAAO,CAAC,AAACM,IACd,IAAMG,EAAO,AAAmB,UAAnB,OAAOH,EAChBA,EACAA,EAAQ/P,EAAE,AACVkQ,CAAAA,GACAlQ,CAAAA,EAAKK,EAAM1B,GAAG,CAACuR,EAAI,EAEnBlQ,aAAe8O,KACf9O,EAAGM,MAAM,CAACoP,OAAO,EACjB1P,EAAG0P,OAAO,EACV1P,AAAgB,CAAA,IAAhBA,EAAGiQ,QAAQ,EAEX1P,EAAW+O,WAAW,CAACpJ,IAAI,CAAC,IAAIrG,EAAiByC,EACjDtC,EAAI,AAAmB,UAAnB,OAAO+P,EACP,CAAC,EACDA,GAEZ,EAER,EAER,GAGA,IAAK,IAAII,EAAI,EAAGC,EAAGC,EAAOC,EAASd,EAAe9M,MAAM,CAAE6N,EAAShQ,EAAW+O,WAAW,CAAC5M,MAAM,CAAEyN,EAAIG,EAAQ,EAAEH,EAAG,CAC/GE,EAAQ,CAAA,EACR,IAAMG,EAAShB,CAAc,CAACW,EAAE,CAChC,IAAKC,EAAI,EAAGA,EAAIG,EAAQ,EAAEH,EAAG,CACzB,IAAMK,EAASlQ,EAAW+O,WAAW,CAACc,EAAE,CACxC,GAAI,AAACI,CAAAA,EAAOvQ,OAAO,EAAIuQ,EAAOvQ,OAAO,CAAC6B,IAAI,AAAD,IACpC2O,CAAAA,EAAOxQ,OAAO,EAAIwQ,EAAOxQ,OAAO,CAAC6B,IAAI,AAAD,GACrC0O,EAAOrQ,SAAS,GAAKsQ,EAAOtQ,SAAS,EACrCqQ,EAAOpQ,OAAO,GAAKqQ,EAAOrQ,OAAO,CAAE,CACnCqQ,EAAO1P,QAAQ,CAAGyP,EAAOzP,QAAQ,CACjCsP,EAAQ,CAAA,EACR,KACJ,CACJ,CACI,AAACA,GACDG,EAAO5K,OAAO,EAEtB,CAIA,OAAO,IAAI,CAAC9B,cAAc,CAC1B,OAAO,IAAI,CAACU,aAAa,CAEzBjE,EAAWmQ,iBAAiB,CAACnB,EACjC,CAUAmB,kBAAkBnB,CAAW,CAAE,CACvBA,EAEA,IAAI,CAAClP,KAAK,CAACC,MAAM,CAACmP,OAAO,CAAC,SAAUnP,CAAM,EACtC,IAAM+E,EAAS,WAGX,IAAM9E,EAAaD,EAAOD,KAAK,CAACE,UAAU,CAC1CoQ,AADoDpQ,CAAAA,GAAcA,EAAW+O,WAAW,EAAI,EAAE,AAAD,EACvFG,OAAO,CAAC,SAAU3O,CAAU,EAC1BA,EAAWX,SAAS,EACpBW,EAAWX,SAAS,CAACG,MAAM,GAAKA,GAChCQ,EAAWuE,MAAM,EAEzB,GACI/E,EAAOsQ,2BAA2B,GAClCtQ,EAAOsQ,2BAA2B,GAClC,OAAOtQ,EAAOsQ,2BAA2B,CAEjD,CACItQ,AAA6B,EAAA,IAA7BA,EAAOL,OAAO,CAACyD,SAAS,CACxB2B,IAGA/E,EAAOsQ,2BAA2B,CAAG7B,EAASzO,EAAQ,eAAgB+E,EAE9E,GAIA,IAAI,CAACiK,WAAW,CAACG,OAAO,CAAC,SAAU3O,CAAU,EACzCA,EAAWuE,MAAM,EACrB,EAER,CAkBApB,kBAAkBhE,CAAO,CAAE,CACvB,IAAMK,EAAS,IAAI,CAACD,KAAK,CAACC,MAAM,CAAE4E,EAAS+J,EAAgBhP,EAAQkE,eAAe,CAAE,GAChFJ,EAAY,EAAE,CAAE8M,EACpB,IAAK,IAAI5K,EAAI,EAAG6K,EAAOxQ,EAAOoC,MAAM,CAAEuD,EAAI6K,EAAM,EAAE7K,EAC9C,GAAI3F,CAAM,CAAC2F,EAAE,CAACyJ,OAAO,EAAI,CAACpP,CAAM,CAAC2F,EAAE,CAAChG,OAAO,CAAC0P,UAAU,CAClD,IAAK,IAAIQ,EAAI,EAAGY,EAAOzQ,CAAM,CAAC2F,EAAE,CAAC2J,MAAM,CAAClN,MAAM,CAAE0J,EAAI9J,EAAO6N,EAAIY,EAAM,EAAEZ,EAE/D7N,AADJA,CAAAA,EAAQhC,CAAM,CAAC2F,EAAE,CAAC2J,MAAM,CAACO,EAAE,AAAD,EAChBT,OAAO,EACbtD,CAAAA,EAAK4E,AAvR7B,SAA+B1O,CAAK,EAChC,IAAM6J,EAAY7J,EAAM6J,SAAS,CAEjC,GAAIA,EACA,MAAO,CACHxH,KAAMwH,EAAUxJ,CAAC,EAAI,EACrBiC,KAAM,AAACuH,CAAAA,EAAUxJ,CAAC,EAAI,CAAA,EAAMwJ,CAAAA,EAAUjK,KAAK,EAAI,CAAA,EAC/C4C,KAAMqH,EAAUvJ,CAAC,EAAI,EACrBmC,KAAM,AAACoH,CAAAA,EAAUvJ,CAAC,EAAI,CAAA,EAAMuJ,CAAAA,EAAUhK,MAAM,EAAI,CAAA,CACpD,EAGJ,IAAMiK,EAAK9J,EAAM+J,OAAO,EAAI/J,EAAM+J,OAAO,CAACC,OAAO,GACjD,OAAOF,EAAK,CACRzH,KAAMrC,EAAMiK,KAAK,CAAGH,EAAGlK,KAAK,CAAG,EAC/B0C,KAAMtC,EAAMiK,KAAK,CAAGH,EAAGlK,KAAK,CAAG,EAC/B4C,KAAMxC,EAAMkK,KAAK,CAAGJ,EAAGjK,MAAM,CAAG,EAChC4C,KAAMzC,EAAMkK,KAAK,CAAGJ,EAAGjK,MAAM,CAAG,CACpC,EAAI,IACR,EAoQmDG,EAAK,GAE5ByB,EAAUmC,IAAI,CAAC,CACXvB,KAAMyH,EAAGzH,IAAI,CAAGO,EAChBN,KAAMwH,EAAGxH,IAAI,CAAGM,EAChBJ,KAAMsH,EAAGtH,IAAI,CAAGI,EAChBH,KAAMqH,EAAGrH,IAAI,CAAGG,CACpB,GAsBpB,OAfAnB,EAAYA,EAAUkN,IAAI,CAAC,SAAU7S,CAAC,CAAE8S,CAAC,EACrC,OAAO9S,EAAEuG,IAAI,CAAGuM,EAAEvM,IAAI,AAC1B,GAEKqK,EAAmB/O,EAAQkE,eAAe,IAC3C0M,EACI5Q,EAAQkE,eAAe,CACnBgN,AAzPpB,SAAiCpN,CAAS,EACtC,IACIqN,EADEC,EAAMtN,EAAUrB,MAAM,CAAE4O,EAAY,EAAE,CAG5C,IAAK,IAAIrL,EAAI,EAAGA,EAAIoL,EAAK,EAAEpL,EAGvB,IAAK,IAAIkK,EAAIlK,EAAI,EAAGkK,EAAIkB,EAAK,EAAElB,EAIvBiB,AAHJA,CAAAA,EACIG,AApChB,SAASA,EAA0BnT,CAAC,CAAE8S,CAAC,CAAEM,CAAQ,EAE7C,IAAMtM,EAAS+J,EAAgBuC,EAAU,IAAKC,EAAWrT,EAAE2G,IAAI,CAAGG,EAASgM,EAAEpM,IAAI,CAAGI,GAChF9G,EAAE0G,IAAI,CAAGI,EAASgM,EAAEnM,IAAI,CAAGG,EAAQwM,EAAWtT,EAAEwG,IAAI,CAAGM,EAASgM,EAAEvM,IAAI,CAAGO,GACzE9G,EAAEuG,IAAI,CAAGO,EAASgM,EAAEtM,IAAI,CAAGM,EAAQyM,EAAYF,EAAYrT,EAAEuG,IAAI,CAAGuM,EAAEtM,IAAI,CAAGxG,EAAEuG,IAAI,CAAGuM,EAAEtM,IAAI,CAAGsM,EAAEvM,IAAI,CAAGvG,EAAEwG,IAAI,CAAIgN,IAAUC,EAAYH,EAAYtT,EAAE0G,IAAI,CAAGoM,EAAEnM,IAAI,CAAG3G,EAAE0G,IAAI,CAAGoM,EAAEnM,IAAI,CAAGmM,EAAEpM,IAAI,CAAG1G,EAAE2G,IAAI,CAAI6M,WAG/M,AAAIF,GAAYD,EACJvM,EACJqM,EAA0BnT,EAAG8S,EAAGvR,KAAKmS,KAAK,CAAC5M,EAAS,IACpD0M,IAEDxC,EAAeuC,EAAWE,EACrC,EAuB0C9N,CAAS,CAACkC,EAAE,CAAElC,CAAS,CAACoM,EAAE,CAAA,EAEjC,IACnBmB,EAAUpL,IAAI,CAACkL,GAM3B,OADAE,EAAUpL,IAAI,CAAC,IACRiJ,EAAexP,KAAKmS,KAAK,CAACR,EAAUL,IAAI,CAAC,SAAU7S,CAAC,CAAE8S,CAAC,EAC1D,OAAQ9S,EAAI8S,CAChB,EAAE,CAGFvR,KAAKmS,KAAK,CAACR,EAAU5O,MAAM,CAAG,IAAI,CAAG,EAAI,GACtC,EAEP,EA+N4CqB,GAChCA,EAAU0L,OAAO,CAAC,SAAUnI,CAAQ,EAChCA,EAAS3C,IAAI,EAAIkM,EACjBvJ,EAAS1C,IAAI,EAAIiM,EACjBvJ,EAASxC,IAAI,EAAI+L,EACjBvJ,EAASvC,IAAI,EAAI8L,CACrB,IAEG9M,CACX,CAeAM,mBAAmBN,CAAS,CAAE,CAC1B,IAAIoF,EAAW,EAAGG,EAAY,EAAGpH,EAAOC,EAAQ8D,EAAIlC,EAAUrB,MAAM,CACpE,KAAOuD,KACH/D,EAAQ6B,CAAS,CAACkC,EAAE,CAACrB,IAAI,CAAGb,CAAS,CAACkC,EAAE,CAACtB,IAAI,CAC7CxC,EAAS4B,CAAS,CAACkC,EAAE,CAAClB,IAAI,CAAGhB,CAAS,CAACkC,EAAE,CAACnB,IAAI,CAC1CqE,EAAWjH,GACXiH,CAAAA,EAAWjH,CAAI,EAEfoH,EAAYnH,GACZmH,CAAAA,EAAYnH,CAAK,EAGzB,MAAO,CACHmH,UAAWA,EACXH,SAAUA,CACd,CACJ,CAaA/D,2BAA2BsH,CAAa,CAAE,CACtC,IAAMqF,EAAUrF,AAAwB,SAAxBA,EAAcf,KAAK,EAC/Be,AAAwB,UAAxBA,EAAcf,KAAK,CAAcqG,EAAUtF,AAAgC,QAAhCA,EAAcd,aAAa,EACtEc,AAAgC,WAAhCA,EAAcd,aAAa,CAC/B,OAAOmG,EACFC,EAAAA,GAAU,KAAK,EACfA,EAAAA,GAAiB,KAAK,CAC/B,CACJ,CAwFA,SAASC,EAAMtP,CAAC,CAAEC,CAAC,CAAEsP,CAAC,CAAEC,CAAC,EACrB,MAAO,CACH,CAAC,IAAKxP,EAAGC,EAAIuP,EAAI,EAAE,CACnB,CAAC,IAAKxP,EAAIuP,EAAGtP,EAAE,CACf,CAAC,IAAKD,EAAGC,EAAIuP,EAAI,EAAE,CACnB,CAAC,IAAKxP,EAAIuP,EAAGtP,EAAIuP,EAAE,CACtB,AACL,CA6BA,SAASC,EAAUzP,CAAC,CAAEC,CAAC,CAAEsP,CAAC,CAAEC,CAAC,EACzB,OAAOF,EAAMtP,EAAGC,EAAGsP,EAAI,EAAGC,EAC9B,CAyCA,SAASE,EAAa1P,CAAC,CAAEC,CAAC,CAAEsP,CAAC,CAAEC,CAAC,EAC5B,MAAO,CACH,CAAC,IAAKxP,EAAIuP,EAAGtP,EAAE,CACf,CAAC,IAAKD,EAAGC,EAAIuP,EAAI,EAAE,CACnB,CAAC,IAAKxP,EAAIuP,EAAGtP,EAAIuP,EAAE,CACnB,CAAC,IAAI,CACR,AACL,CA6BA,SAASG,EAAiB3P,CAAC,CAAEC,CAAC,CAAEsP,CAAC,CAAEC,CAAC,EAChC,OAAOE,EAAa1P,EAAGC,EAAGsP,EAAI,EAAGC,EACrC,CAxMA9C,EAAWvQ,SAAS,CAAC+E,UAAU,CA17BZ,CACfiF,UAAAA,EACAyJ,SAtfJ,SAAkB9K,CAAK,CAAEC,CAAG,EACxB,MAAO,CACHjH,KAAM,CACF,CAAC,IAAKgH,EAAM9E,CAAC,CAAE8E,EAAM7E,CAAC,CAAC,CACvB,CAAC,IAAK8E,EAAI/E,CAAC,CAAE+E,EAAI9E,CAAC,CAAC,CACtB,CACDmB,UAAW,CAAC,CAAE0D,MAAOA,EAAOC,IAAKA,CAAI,EAAE,AAC3C,CACJ,EA+eIG,cAAAA,CACJ,EA+oCA,IAAM2K,EAAKpT,GACXoT,CAAAA,EAAEnD,UAAU,CAAGmD,EAAEnD,UAAU,EApN4BA,EAqNvDoD,AAbqB,CAAA,CACjBtE,QAlFJ,SAAiBuE,CAAgB,EAC7B,IAAMC,EAAUD,EAAiB5T,SAAS,CAAC6T,OAAO,AAClDA,CAAAA,EAAQV,KAAK,CAAGA,EAChBU,CAAO,CAAC,eAAe,CAAGN,EAC1BM,CAAO,CAAC,oBAAoB,CAAGL,EAC/BK,CAAO,CAAC,aAAa,CAAGP,EACxBO,CAAO,CAAC,gBAAgB,CAAGN,EAC3BM,CAAO,CAAC,qBAAqB,CAAGL,CACpC,CA2EA,CAAA,EAWwBnE,OAAO,CAACqE,EAAEI,WAAW,EAC7CJ,EAAEnD,UAAU,CAAClB,OAAO,CAACqE,EAAEK,KAAK,CAAEL,EAAEM,KAAK,EACR,IAAM5T,EAAmBE,IAG5C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}