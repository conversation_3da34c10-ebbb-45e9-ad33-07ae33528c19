{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n* @license Highcharts JS v12.3.0 (2025-06-21)\n* @module highcharts/i18n/zh-CN\n* @requires highcharts\n*\n* zh-CN language pack\n*\n* (c) 2009-2025 Torstein Honsi\n*\n* License: www.highcharts.com/license\n*\n* **Do not edit this file!** This file is generated using the 'gulp lang-build' task.\n*/\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/i18n/zh-CN\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/i18n/zh-CN\"] = factory(root[\"_Highcharts\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (zh_CN_src)\n/* harmony export */ });\n/* harmony import */ var _Core_Defaults_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(944);\n/* harmony import */ var _Core_Defaults_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Core_Defaults_js__WEBPACK_IMPORTED_MODULE_0__);\n\n\nconst { setOptions } = (_Core_Defaults_js__WEBPACK_IMPORTED_MODULE_0___default());\nconst languageOptions = {\n    \"locale\": \"zh-CN\",\n    \"chartTitle\": \"图表标题\",\n    \"pieSliceName\": \"扇区\",\n    \"seriesName\": \"系列 {add index 1}\",\n    \"yAxisTitle\": \"值\",\n    \"rangeSelector\": {\n        \"allText\": \"全部\",\n        \"allTitle\": \"查看全部\",\n        \"monthText\": \"{count}个月\",\n        \"monthTitle\": \"查看 {count} 个月\",\n        \"yearText\": \"{count}年\",\n        \"yearTitle\": \"查看 {count} 年\",\n        \"ytdText\": \"年初至今\",\n        \"ytdTitle\": \"查看年初至今\"\n    },\n    \"weekFrom\": \"从星期一开始的一周\",\n    \"stockOpen\": \"开盘\",\n    \"stockHigh\": \"最高\",\n    \"stockLow\": \"最低\",\n    \"stockClose\": \"收盘\",\n    \"viewFullscreen\": \"全屏查看\",\n    \"exitFullscreen\": \"退出全屏\",\n    \"printChart\": \"打印图表\",\n    \"downloadPNG\": \"下载 PNG 图片\",\n    \"downloadJPEG\": \"下载 JPEG 图片\",\n    \"downloadPDF\": \"下载 PDF 文档\",\n    \"downloadSVG\": \"下载 SVG 矢量图像\",\n    \"contextButtonTitle\": \"图表上下文菜单\",\n    \"loading\": \"加载中...\",\n    \"numericSymbols\": [\n        \"千\",\n        \"百万\",\n        \"十亿\",\n        \"万亿\",\n        \"P\",\n        \"E\"\n    ],\n    \"resetZoom\": \"重置缩放\",\n    \"resetZoomTitle\": \"重置缩放级别 1:1\",\n    \"rangeSelectorZoom\": \"缩放\",\n    \"rangeSelectorFrom\": \"\",\n    \"rangeSelectorTo\": \"→\",\n    \"zoomIn\": \"放大\",\n    \"zoomOut\": \"缩小\",\n    \"downloadCSV\": \"下载 CSV\",\n    \"downloadXLS\": \"下载 XLS\",\n    \"exportData\": {\n        \"annotationHeader\": \"注释\",\n        \"categoryHeader\": \"类别\",\n        \"categoryDatetimeHeader\": \"日期时间\"\n    },\n    \"viewData\": \"查看数据表\",\n    \"hideData\": \"隐藏数据表\",\n    \"exportInProgress\": \"正在导出...\",\n    \"accessibility\": {\n        \"defaultChartTitle\": \"图表\",\n        \"chartContainerLabel\": \"{title}. Highcharts 交互式图表。\",\n        \"svgContainerLabel\": \"交互式图表\",\n        \"drillUpButton\": \"{buttonText}\",\n        \"credits\": \"图表致谢: {creditsStr}\",\n        \"thousandsSep\": \",\",\n        \"svgContainerTitle\": \"\",\n        \"graphicContainerLabel\": \"\",\n        \"screenReaderSection\": {\n            \"beforeRegionLabel\": \"\",\n            \"afterRegionLabel\": \"\",\n            \"annotations\": {\n                \"heading\": \"图表注释总结\",\n                \"descriptionSinglePoint\": \"{annotationText}. 关联至 {annotationPoint}\",\n                \"descriptionMultiplePoints\": \"{annotationText}. 关联至 {annotationPoint}{#each additionalAnnotationPoints}, 也关联至 {this}{/each}\",\n                \"descriptionNoPoints\": \"{annotationText}\"\n            },\n            \"endOfChartMarker\": \"交互式图表结束。\"\n        },\n        \"sonification\": {\n            \"playAsSoundButtonText\": \"以声音播放, {chartTitle}\",\n            \"playAsSoundClickAnnouncement\": \"播放\"\n        },\n        \"legend\": {\n            \"legendLabelNoTitle\": \"切换系列可见性, {chartTitle}\",\n            \"legendLabel\": \"图表图例: {legendTitle}\",\n            \"legendItem\": \"显示 {itemName}\"\n        },\n        \"zoom\": {\n            \"mapZoomIn\": \"放大图表\",\n            \"mapZoomOut\": \"缩小图表\",\n            \"resetZoomButton\": \"重置缩放\"\n        },\n        \"rangeSelector\": {\n            \"dropdownLabel\": \"{rangeTitle}\",\n            \"minInputLabel\": \"选择开始日期。\",\n            \"maxInputLabel\": \"选择结束日期。\",\n            \"clickButtonAnnouncement\": \"正在查看 {axisRangeDescription}\"\n        },\n        \"navigator\": {\n            \"handleLabel\": \"{#eq handleIx 0}开始, 百分比{else}结束, 百分比{/eq}\",\n            \"groupLabel\": \"轴缩放\",\n            \"changeAnnouncement\": \"{axisRangeDescription}\"\n        },\n        \"table\": {\n            \"viewAsDataTableButtonText\": \"以数据表形式查看, {chartTitle}\",\n            \"tableSummary\": \"图表的表格表示。\"\n        },\n        \"announceNewData\": {\n            \"newDataAnnounce\": \"图表 {chartTitle} 的更新数据\",\n            \"newSeriesAnnounceSingle\": \"新数据系列: {seriesDesc}\",\n            \"newPointAnnounceSingle\": \"新数据点: {pointDesc}\",\n            \"newSeriesAnnounceMultiple\": \"图表 {chartTitle} 中的新数据系列: {seriesDesc}\",\n            \"newPointAnnounceMultiple\": \"图表 {chartTitle} 中的新数据点: {pointDesc}\"\n        },\n        \"seriesTypeDescriptions\": {\n            \"boxplot\": \"箱形图通常用于显示一组统计数据。图表中的每个数据点最多可以有5个值：最小值、下四分位数、中位数、上四分位数和最大值。\",\n            \"arearange\": \"面积范围图是显示每个点的较低和较高值范围的线图。\",\n            \"areasplinerange\": \"这些图表是显示每个点的较低和较高值范围的线图。\",\n            \"bubble\": \"气泡图是每个数据点也具有大小值的散点图。\",\n            \"columnrange\": \"柱状范围图是显示每个点的较低和较高值范围的柱状图。\",\n            \"errorbar\": \"误差线系列用于显示数据的可变性。\",\n            \"funnel\": \"漏斗图用于分阶段显示数据的减少。\",\n            \"pyramid\": \"金字塔图是由多个塔层组成的金字塔形状的图形，其中每个塔层的高度和其点的值有关。\",\n            \"waterfall\": \"瀑布图由一系列柱状的图形组成，其柱状图形的高度和位置表示数值变化情况。\"\n        },\n        \"chartTypes\": {\n            \"emptyChart\": \"空图表\",\n            \"mapTypeDescription\": \"含有 {numSeries} 个数据系列的 {mapTitle} 地图。\",\n            \"unknownMap\": \"含有 {numSeries} 个数据系列的未指定区域地图。\",\n            \"combinationChart\": \"含有 {numPoints} 个数据点的图表。\",\n            \"defaultSingle\": \"含有 {numSeries} 个数据系列的图表。\",\n            \"defaultMultiple\": \"含有 {numSeries} 个数据系列的图表。\",\n            \"splineSingle\": \"含有 {numPoints} 个数据点的曲线图。\",\n            \"splineMultiple\": \"含有 {numSeries} 条曲线的曲线图。\",\n            \"lineSingle\": \"含有 {numPoints} 个数据点的折线图。\",\n            \"lineMultiple\": \"含有 {numSeries} 条折线的折线图。\",\n            \"columnSingle\": \"含有 {numPoints}个柱状数据列的柱状图。\",\n            \"columnMultiple\": \"含有 {numSeries} 个数据系列的柱状图。\",\n            \"barSingle\": \"含有 {numPoints} 个柱状数据行的条形图。\",\n            \"barMultiple\": \"含有 {numSeries} 个数据系列的条形图。\",\n            \"pieSingle\": \"含有 {numPoints} 个扇区的饼状图。\",\n            \"pieMultiple\": \"含有 {numSeries} 个数据系列的饼状图。\",\n            \"scatterSingle\": \"含有 {numPoints} 个点的散点图。\",\n            \"scatterMultiple\": \"含有 {numSeries} 个数据系列的散点图。\",\n            \"boxplotSingle\": \"含有 {numPoints} 个数据箱的箱线图。\",\n            \"boxplotMultiple\": \"含有 {numSeries} 个数据系列的箱线图。\",\n            \"bubbleSingle\": \"含有 {numPoints} 个气泡的气泡图。\",\n            \"bubbleMultiple\": \"含有 {numSeries} 个数据系列的气泡图。\"\n        },\n        \"axis\": {\n            \"xAxisDescriptionSingular\": \"图表有1个X轴显示 {names[0]}. {ranges[0]}\",\n            \"xAxisDescriptionPlural\": \"图表有 {numAxes} 个X轴显示 {#each names}{#unless @first},{/unless}{#if @last}和{/if} {this}{/each}。\",\n            \"yAxisDescriptionSingular\": \"图表有1个Y轴显示 {names[0]}. {ranges[0]}\",\n            \"yAxisDescriptionPlural\": \"图表有 {numAxes} 个Y轴显示 {#each names}{#unless @first},{/unless}{#if @last}和{/if} {this}{/each}。\",\n            \"timeRangeDays\": \"数据范围：{range} 天。\",\n            \"timeRangeHours\": \"数据范围：{range} 小时。\",\n            \"timeRangeMinutes\": \"数据范围：{range} 分钟。\",\n            \"timeRangeSeconds\": \"数据范围：{range} 秒。\",\n            \"rangeFromTo\": \"数据范围从 {rangeFrom} 到 {rangeTo}。\",\n            \"rangeCategories\": \"数据范围：{numCategories} 个类别。\"\n        },\n        \"exporting\": {\n            \"chartMenuLabel\": \"图表菜单\",\n            \"menuButtonLabel\": \"查看图表菜单, {chartTitle}\"\n        },\n        \"series\": {\n            \"summary\": {\n                \"default\": \"{series.name}，是{chart.series.length}个图标数据系列中的第{seriesNumber}个， 有 {series.points.length} 个数据点。\",\n                \"defaultCombination\": \"{series.name}，是{chart.series.length}个图标数据系列中的第{seriesNumber}个， 有 {series.points.length} 个数据点。\",\n                \"line\": \"{series.name}，是{chart.series.length}个折线图数据系列中的第{seriesNumber}个， 有 {series.points.length} 个数据点。\",\n                \"lineCombination\": \"{series.name}，是{chart.series.length}个图数据系列中的第{seriesNumber}个， 折线图有 {series.points.length} 个数据点。\",\n                \"spline\": \"{series.name}，是{chart.series.length}个曲线图数据系列中的第{seriesNumber}个， 有 {series.points.length} 个数据点。\",\n                \"splineCombination\": \"{series.name}，是{chart.series.length}个图数据系列中的第{seriesNumber}个， 曲线图有 {series.points.length} 个数据点。\",\n                \"column\": \"{series.name}，是{chart.series.length}个柱状图数据系列中的第{seriesNumber}个， 有 {series.points.length} 个数据点。\",\n                \"columnCombination\": \"{series.name}，是{chart.series.length}个图数据系列中的第{seriesNumber}个， 柱状图有 {series.points.length} 个数据点。\",\n                \"bar\": \"{series.name}，是{chart.series.length}个柱状图数据系列中的第{seriesNumber}个， 有 {series.points.length} 个数据点。\",\n                \"barCombination\": \"{series.name}，是{chart.series.length}个图数据系列中的第{seriesNumber}个， 柱状图有 {series.points.length} 个数据点。\",\n                \"pie\": \"{series.name}，是{chart.series.length}个饼状图数据系列中的第{seriesNumber}个， 有 {series.points.length} 个数据点。\",\n                \"pieCombination\": \"{series.name}，是{chart.series.length}个图数据系列中的第{seriesNumber}个， 饼状图有 {series.points.length} 个数据点。\",\n                \"scatter\": \"{series.name}，是{chart.series.length}个散点图数据系列中的第{seriesNumber}个， 有 {series.points.length} 个数据点。\",\n                \"scatterCombination\": \"{series.name}，是{chart.series.length}个图数据系列中的第{seriesNumber}个， 散点图有 {series.points.length} 个数据点。\",\n                \"boxplot\": \"{series.name}，是{chart.series.length}个箱线图数据系列中的第{seriesNumber}个， 有 {series.points.length} 个数据点。\",\n                \"boxplotCombination\": \"{series.name}，是{chart.series.length}个图数据系列中的第{seriesNumber}个， 箱线图有 {series.points.length} 个数据点。\",\n                \"bubble\": \"{series.name}，是{chart.series.length}个气泡图数据系列中的第{seriesNumber}个， 有 {series.points.length} 个数据点。\",\n                \"bubbleCombination\": \"{series.name}，是{chart.series.length}个图数据系列中的第{seriesNumber}个， 气泡图有 {series.points.length} 个数据点。\",\n                \"map\": \"{series.name}，是{chart.series.length}个地图数据系列中的第{seriesNumber}个， 有 {series.points.length} 个数据点。\",\n                \"mapCombination\": \"{series.name}，是{chart.series.length}个图数据系列中的第{seriesNumber}个， 地图有 {series.points.length} 个数据点。\",\n                \"mapline\": \"{series.name}，是{chart.series.length}个折线图数据系列中的第{seriesNumber}个， 有 {series.points.length} 个数据点。\",\n                \"maplineCombination\": \"{series.name}，是{chart.series.length}个图数据系列中的第{seriesNumber}个， 折线图有 {series.points.length} 个数据点。\",\n                \"mapbubble\": \"{series.name}，是{chart.series.length}个气泡图数据系列中的第{seriesNumber}个， 有 {series.points.length} 个数据点。\",\n                \"mapbubbleCombination\": \"{series.name}，是{chart.series.length}个图数据系列中的第{seriesNumber}个， 气泡图有 {series.points.length} 个数据点。\"\n            },\n            \"description\": \"{description}\",\n            \"xAxisDescription\": \"X轴, {name}\",\n            \"yAxisDescription\": \"Y轴, {name}\",\n            \"nullPointValue\": \"无值\",\n            \"pointAnnotationsDescription\": \"{#each annotations}注释: {this}{/each}\"\n        }\n    },\n    \"navigation\": {\n        \"popup\": {\n            \"simpleShapes\": \"简单形状\",\n            \"lines\": \"线条\",\n            \"circle\": \"圆形\",\n            \"ellipse\": \"椭圆形\",\n            \"rectangle\": \"矩形\",\n            \"label\": \"标签\",\n            \"shapeOptions\": \"形状选项\",\n            \"typeOptions\": \"详细\",\n            \"fill\": \"填充\",\n            \"format\": \"文本\",\n            \"strokeWidth\": \"线宽\",\n            \"stroke\": \"线条颜色\",\n            \"title\": \"标题\",\n            \"name\": \"名称\",\n            \"labelOptions\": \"标签选项\",\n            \"labels\": \"标签\",\n            \"backgroundColor\": \"背景颜色\",\n            \"backgroundColors\": \"背景颜色\",\n            \"borderColor\": \"边框颜色\",\n            \"borderRadius\": \"边框半径\",\n            \"borderWidth\": \"边框宽度\",\n            \"style\": \"风格\",\n            \"padding\": \"填充\",\n            \"fontSize\": \"字体大小\",\n            \"color\": \"颜色\",\n            \"height\": \"高度\",\n            \"shapes\": \"形状选项\",\n            \"segment\": \"段\",\n            \"arrowSegment\": \"箭头段\",\n            \"ray\": \"射线\",\n            \"arrowRay\": \"箭头射线\",\n            \"line\": \"线\",\n            \"arrowInfinityLine\": \"箭头线\",\n            \"horizontalLine\": \"水平线\",\n            \"verticalLine\": \"垂直线\",\n            \"crooked3\": \"弯曲3线\",\n            \"crooked5\": \"弯曲5线\",\n            \"elliott3\": \"艾略特3线\",\n            \"elliott5\": \"艾略特5线\",\n            \"verticalCounter\": \"垂直计数器\",\n            \"verticalLabel\": \"垂直标签\",\n            \"verticalArrow\": \"垂直箭头\",\n            \"fibonacci\": \"斐波那契\",\n            \"fibonacciTimeZones\": \"斐波那契时间区\",\n            \"pitchfork\": \"叉子\",\n            \"parallelChannel\": \"平行通道\",\n            \"infinityLine\": \"无限线\",\n            \"measure\": \"测量\",\n            \"measureXY\": \"测量 XY\",\n            \"measureX\": \"测量 X\",\n            \"measureY\": \"测量 Y\",\n            \"timeCycles\": \"时间周期\",\n            \"flags\": \"标志\",\n            \"addButton\": \"添加\",\n            \"saveButton\": \"保存\",\n            \"editButton\": \"编辑\",\n            \"removeButton\": \"移除\",\n            \"series\": \"系列\",\n            \"volume\": \"音量\",\n            \"connector\": \"连接器\",\n            \"innerBackground\": \"内部背景\",\n            \"outerBackground\": \"外部背景\",\n            \"crosshairX\": \"十字线 X\",\n            \"crosshairY\": \"十字线 Y\",\n            \"tunnel\": \"隧道\",\n            \"background\": \"背景\",\n            \"noFilterMatch\": \"无匹配项\",\n            \"searchIndicators\": \"搜索指标\",\n            \"clearFilter\": \"✕ 清除筛选\",\n            \"index\": \"索引\",\n            \"period\": \"周期\",\n            \"periods\": \"周期\",\n            \"standardDeviation\": \"标准偏差\",\n            \"periodTenkan\": \"天干周期\",\n            \"periodSenkouSpanB\": \"云图跨度B周期\",\n            \"periodATR\": \"ATR周期\",\n            \"multiplierATR\": \"ATR倍数\",\n            \"shortPeriod\": \"短周期\",\n            \"longPeriod\": \"长周期\",\n            \"signalPeriod\": \"信号周期\",\n            \"decimals\": \"小数\",\n            \"algorithm\": \"算法\",\n            \"topBand\": \"上带\",\n            \"bottomBand\": \"下带\",\n            \"initialAccelerationFactor\": \"初始加速因子\",\n            \"maxAccelerationFactor\": \"最大加速因子\",\n            \"increment\": \"增量\",\n            \"multiplier\": \"乘数\",\n            \"ranges\": \"范围\",\n            \"highIndex\": \"高指数\",\n            \"lowIndex\": \"低指数\",\n            \"deviation\": \"偏差\",\n            \"xAxisUnit\": \"x轴单位\",\n            \"factor\": \"因子\",\n            \"fastAvgPeriod\": \"快速平均周期\",\n            \"slowAvgPeriod\": \"慢速平均周期\",\n            \"average\": \"平均\",\n            \"indicatorAliases\": {\n                \"abands\": [\n                    \"加速带\"\n                ],\n                \"bb\": [\n                    \"布林带\"\n                ],\n                \"dema\": [\n                    \"双指数移动平均\"\n                ],\n                \"ema\": [\n                    \"指数移动平均\"\n                ],\n                \"ikh\": [\n                    \"一目均衡表\"\n                ],\n                \"keltnerchannels\": [\n                    \"肯特纳通道\"\n                ],\n                \"linearRegression\": [\n                    \"线性回归\"\n                ],\n                \"pivotpoints\": [\n                    \"支点\"\n                ],\n                \"pc\": [\n                    \"价格通道\"\n                ],\n                \"priceenvelopes\": [\n                    \"价格信封\"\n                ],\n                \"psar\": [\n                    \"抛物线指标\"\n                ],\n                \"sma\": [\n                    \"简单移动平均\"\n                ],\n                \"supertrend\": [\n                    \"超级趋势\"\n                ],\n                \"tema\": [\n                    \"三重指数移动平均\"\n                ],\n                \"vbp\": [\n                    \"按价量\"\n                ],\n                \"vwap\": [\n                    \"成交量加权平均价\"\n                ],\n                \"wma\": [\n                    \"加权移动平均\"\n                ],\n                \"zigzag\": [\n                    \"之字形\"\n                ],\n                \"apo\": [\n                    \"绝对价格振荡器\"\n                ],\n                \"ad\": [\n                    \"累积/分布\"\n                ],\n                \"aroon\": [\n                    \"阿隆\"\n                ],\n                \"aroonoscillator\": [\n                    \"阿隆振荡器\"\n                ],\n                \"atr\": [\n                    \"平均真实范围\"\n                ],\n                \"ao\": [\n                    \"超棒振荡器\"\n                ],\n                \"cci\": [\n                    \"商品通道指数\"\n                ],\n                \"chaikin\": [\n                    \"蔡金\"\n                ],\n                \"cmf\": [\n                    \"蔡金资金流\"\n                ],\n                \"cmo\": [\n                    \"钱德动量振荡器\"\n                ],\n                \"disparityindex\": [\n                    \"差异指数\"\n                ],\n                \"dmi\": [\n                    \"趋向指标\"\n                ],\n                \"dpo\": [\n                    \"去趋势价格振荡器\"\n                ],\n                \"klinger\": [\n                    \"克林格振荡器\"\n                ],\n                \"linearRegressionAngle\": [\n                    \"线性回归角度\"\n                ],\n                \"linearRegressionIntercept\": [\n                    \"线性回归截距\"\n                ],\n                \"linearRegressionSlope\": [\n                    \"线性回归斜率\"\n                ],\n                \"macd\": [\n                    \"移动平均收敛/发散\"\n                ],\n                \"mfi\": [\n                    \"资金流量指数\"\n                ],\n                \"momentum\": [\n                    \"动量\"\n                ],\n                \"natr\": [\n                    \"归一化平均真实范围\"\n                ],\n                \"obv\": [\n                    \"能量潮\"\n                ],\n                \"ppo\": [\n                    \"价格振荡百分比\"\n                ],\n                \"roc\": [\n                    \"变化率\"\n                ],\n                \"rsi\": [\n                    \"相对强弱指数\"\n                ],\n                \"slowstochastic\": [\n                    \"慢速随机\"\n                ],\n                \"stochastic\": [\n                    \"随机振荡器\"\n                ],\n                \"trix\": [\n                    \"TRIX\"\n                ],\n                \"williamsr\": [\n                    \"威廉%R\"\n                ]\n            }\n        }\n    },\n    \"mainBreadcrumb\": \"主页\",\n    \"downloadMIDI\": \"下载 MIDI\",\n    \"playAsSound\": \"作为声音播放\",\n    \"stockTools\": {\n        \"gui\": {\n            \"simpleShapes\": \"简单形状\",\n            \"lines\": \"线条\",\n            \"crookedLines\": \"弯曲线条\",\n            \"measure\": \"测量\",\n            \"advanced\": \"高级\",\n            \"toggleAnnotations\": \"切换注释\",\n            \"verticalLabels\": \"垂直标签\",\n            \"flags\": \"标记\",\n            \"zoomChange\": \"缩放更改\",\n            \"typeChange\": \"类型更改\",\n            \"saveChart\": \"保存图表\",\n            \"indicators\": \"指标\",\n            \"currentPriceIndicator\": \"当前价格指标\",\n            \"zoomX\": \"缩放 X\",\n            \"zoomY\": \"缩放 Y\",\n            \"zoomXY\": \"缩放 XY\",\n            \"fullScreen\": \"全屏\",\n            \"typeOHLC\": \"OHLC\",\n            \"typeLine\": \"线型\",\n            \"typeCandlestick\": \"蜡烛图\",\n            \"typeHLC\": \"HLC\",\n            \"typeHollowCandlestick\": \"空心蜡烛图\",\n            \"typeHeikinAshi\": \"平均K线图\",\n            \"circle\": \"圆形\",\n            \"ellipse\": \"椭圆\",\n            \"label\": \"标签\",\n            \"rectangle\": \"矩形\",\n            \"flagCirclepin\": \"圆形标记\",\n            \"flagDiamondpin\": \"菱形标记\",\n            \"flagSquarepin\": \"方形标记\",\n            \"flagSimplepin\": \"简单标记\",\n            \"measureXY\": \"测量 XY\",\n            \"measureX\": \"测量 X\",\n            \"measureY\": \"测量 Y\",\n            \"segment\": \"段\",\n            \"arrowSegment\": \"箭头段\",\n            \"ray\": \"射线\",\n            \"arrowRay\": \"箭头射线\",\n            \"line\": \"线条\",\n            \"arrowInfinityLine\": \"无限线\",\n            \"horizontalLine\": \"水平线\",\n            \"verticalLine\": \"垂直线\",\n            \"infinityLine\": \"无限线\",\n            \"crooked3\": \"弯曲3线\",\n            \"crooked5\": \"弯曲5线\",\n            \"elliott3\": \"艾略特3线\",\n            \"elliott5\": \"艾略特5线\",\n            \"verticalCounter\": \"垂直计数器\",\n            \"verticalLabel\": \"垂直标签\",\n            \"verticalArrow\": \"垂直箭头\",\n            \"fibonacci\": \"斐波那契\",\n            \"fibonacciTimeZones\": \"斐波那契时间区\",\n            \"pitchfork\": \"叉子\",\n            \"parallelChannel\": \"平行通道\",\n            \"timeCycles\": \"时间周期\"\n        }\n    },\n    \"noData\": \"没有数据显示\"\n};\nsetOptions({\n    lang: languageOptions\n});\n// Export Highcharts\n/* harmony default export */ const zh_CN_src = ((_Core_Defaults_js__WEBPACK_IMPORTED_MODULE_0___default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "zh_CN_src", "_Core_Defaults_js__WEBPACK_IMPORTED_MODULE_0__", "_Core_Defaults_js__WEBPACK_IMPORTED_MODULE_0___default", "setOptions", "lang"], "mappings": "CAaA,AAbA;;;;;;;;;;;;AAYA,EACC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,EACrC,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,wBAAyB,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAM,GAC1F,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,wBAAwB,CAAGD,EAAQD,EAAK,WAAc,EAE9DA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CACjD,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,AAACC,GACnC,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIE,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaX,OAAO,CAG5B,IAAIC,EAASO,CAAwB,CAACE,EAAS,CAAG,CAGjDV,QAAS,CAAC,CACX,EAMA,OAHAO,CAAmB,CAACG,EAAS,CAACT,EAAQA,EAAOD,OAAO,CAAES,GAG/CR,EAAOD,OAAO,AACtB,CAMCS,EAAoBI,CAAC,CAAG,AAACZ,IACxB,IAAIa,EAASb,GAAUA,EAAOc,UAAU,CACvC,IAAOd,EAAO,OAAU,CACxB,IAAOA,EAER,OADAQ,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAAChB,EAASkB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACpB,EAASmB,IAC5EE,OAAOC,cAAc,CAACtB,EAASmB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EACNrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAOC,CACpB,GACA,IAAIC,EAAiDvB,EAAoB,KACrEwB,EAAsExB,EAAoBI,CAAC,CAACmB,GAGrH,GAAM,CAAEE,WAAAA,CAAU,CAAE,CAAID,IAyfxBC,EAAW,CACPC,KAzfoB,CACpB,OAAU,QACV,WAAc,OACd,aAAgB,KAChB,WAAc,mBACd,WAAc,IACd,cAAiB,CACb,QAAW,KACX,SAAY,OACZ,UAAa,YACb,WAAc,gBACd,SAAY,WACZ,UAAa,eACb,QAAW,OACX,SAAY,QAChB,EACA,SAAY,YACZ,UAAa,KACb,UAAa,KACb,SAAY,KACZ,WAAc,KACd,eAAkB,OAClB,eAAkB,OAClB,WAAc,OACd,YAAe,YACf,aAAgB,aAChB,YAAe,YACf,YAAe,cACf,mBAAsB,UACtB,QAAW,SACX,eAAkB,CACd,IACA,KACA,KACA,KACA,IACA,IACH,CACD,UAAa,OACb,eAAkB,aAClB,kBAAqB,KACrB,kBAAqB,GACrB,gBAAmB,IACnB,OAAU,KACV,QAAW,KACX,YAAe,SACf,YAAe,SACf,WAAc,CACV,iBAAoB,KACpB,eAAkB,KAClB,uBAA0B,MAC9B,EACA,SAAY,QACZ,SAAY,QACZ,iBAAoB,UACpB,cAAiB,CACb,kBAAqB,KACrB,oBAAuB,6BACvB,kBAAqB,QACrB,cAAiB,eACjB,QAAW,qBACX,aAAgB,IAChB,kBAAqB,GACrB,sBAAyB,GACzB,oBAAuB,CACnB,kBAAqB,GACrB,iBAAoB,GACpB,YAAe,CACX,QAAW,SACX,uBAA0B,0CAC1B,0BAA6B,gGAC7B,oBAAuB,kBAC3B,EACA,iBAAoB,UACxB,EACA,aAAgB,CACZ,sBAAyB,sBACzB,6BAAgC,IACpC,EACA,OAAU,CACN,mBAAsB,wBACtB,YAAe,sBACf,WAAc,eAClB,EACA,KAAQ,CACJ,UAAa,OACb,WAAc,OACd,gBAAmB,MACvB,EACA,cAAiB,CACb,cAAiB,eACjB,cAAiB,UACjB,cAAiB,UACjB,wBAA2B,6BAC/B,EACA,UAAa,CACT,YAAe,4CACf,WAAc,MACd,mBAAsB,wBAC1B,EACA,MAAS,CACL,0BAA6B,yBAC7B,aAAgB,UACpB,EACA,gBAAmB,CACf,gBAAmB,wBACnB,wBAA2B,sBAC3B,uBAA0B,oBAC1B,0BAA6B,wCAC7B,yBAA4B,qCAChC,EACA,uBAA0B,CACtB,QAAW,6DACX,UAAa,2BACb,gBAAmB,0BACnB,OAAU,uBACV,YAAe,4BACf,SAAY,mBACZ,OAAU,mBACV,QAAW,0CACX,UAAa,qCACjB,EACA,WAAc,CACV,WAAc,MACd,mBAAsB,uCACtB,WAAc,gCACd,iBAAoB,0BACpB,cAAiB,2BACjB,gBAAmB,2BACnB,aAAgB,2BAChB,eAAkB,0BAClB,WAAc,2BACd,aAAgB,0BAChB,aAAgB,4BAChB,eAAkB,4BAClB,UAAa,6BACb,YAAe,4BACf,UAAa,0BACb,YAAe,4BACf,cAAiB,yBACjB,gBAAmB,4BACnB,cAAiB,2BACjB,gBAAmB,4BACnB,aAAgB,0BAChB,eAAkB,2BACtB,EACA,KAAQ,CACJ,yBAA4B,oCAC5B,uBAA0B,8FAC1B,yBAA4B,oCAC5B,uBAA0B,8FAC1B,cAAiB,kBACjB,eAAkB,mBAClB,iBAAoB,mBACpB,iBAAoB,kBACpB,YAAe,iCACf,gBAAmB,2BACvB,EACA,UAAa,CACT,eAAkB,OAClB,gBAAmB,sBACvB,EACA,OAAU,CACN,QAAW,CACP,QAAW,gGACX,mBAAsB,gGACtB,KAAQ,iGACR,gBAAmB,kGACnB,OAAU,iGACV,kBAAqB,kGACrB,OAAU,iGACV,kBAAqB,kGACrB,IAAO,iGACP,eAAkB,kGAClB,IAAO,iGACP,eAAkB,kGAClB,QAAW,iGACX,mBAAsB,kGACtB,QAAW,iGACX,mBAAsB,kGACtB,OAAU,iGACV,kBAAqB,kGACrB,IAAO,gGACP,eAAkB,iGAClB,QAAW,iGACX,mBAAsB,kGACtB,UAAa,iGACb,qBAAwB,iGAC5B,EACA,YAAe,gBACf,iBAAoB,aACpB,iBAAoB,aACpB,eAAkB,KAClB,4BAA+B,sCACnC,CACJ,EACA,WAAc,CACV,MAAS,CACL,aAAgB,OAChB,MAAS,KACT,OAAU,KACV,QAAW,MACX,UAAa,KACb,MAAS,KACT,aAAgB,OAChB,YAAe,KACf,KAAQ,KACR,OAAU,KACV,YAAe,KACf,OAAU,OACV,MAAS,KACT,KAAQ,KACR,aAAgB,OAChB,OAAU,KACV,gBAAmB,OACnB,iBAAoB,OACpB,YAAe,OACf,aAAgB,OAChB,YAAe,OACf,MAAS,KACT,QAAW,KACX,SAAY,OACZ,MAAS,KACT,OAAU,KACV,OAAU,OACV,QAAW,IACX,aAAgB,MAChB,IAAO,KACP,SAAY,OACZ,KAAQ,IACR,kBAAqB,MACrB,eAAkB,MAClB,aAAgB,MAChB,SAAY,OACZ,SAAY,OACZ,SAAY,QACZ,SAAY,QACZ,gBAAmB,QACnB,cAAiB,OACjB,cAAiB,OACjB,UAAa,OACb,mBAAsB,UACtB,UAAa,KACb,gBAAmB,OACnB,aAAgB,MAChB,QAAW,KACX,UAAa,QACb,SAAY,OACZ,SAAY,OACZ,WAAc,OACd,MAAS,KACT,UAAa,KACb,WAAc,KACd,WAAc,KACd,aAAgB,KAChB,OAAU,KACV,OAAU,KACV,UAAa,MACb,gBAAmB,OACnB,gBAAmB,OACnB,WAAc,QACd,WAAc,QACd,OAAU,KACV,WAAc,KACd,cAAiB,OACjB,iBAAoB,OACpB,YAAe,SACf,MAAS,KACT,OAAU,KACV,QAAW,KACX,kBAAqB,OACrB,aAAgB,OAChB,kBAAqB,UACrB,UAAa,QACb,cAAiB,QACjB,YAAe,MACf,WAAc,MACd,aAAgB,OAChB,SAAY,KACZ,UAAa,KACb,QAAW,KACX,WAAc,KACd,0BAA6B,SAC7B,sBAAyB,SACzB,UAAa,KACb,WAAc,KACd,OAAU,KACV,UAAa,MACb,SAAY,MACZ,UAAa,KACb,UAAa,OACb,OAAU,KACV,cAAiB,SACjB,cAAiB,SACjB,QAAW,KACX,iBAAoB,CAChB,OAAU,CACN,MACH,CACD,GAAM,CACF,MACH,CACD,KAAQ,CACJ,UACH,CACD,IAAO,CACH,SACH,CACD,IAAO,CACH,QACH,CACD,gBAAmB,CACf,QACH,CACD,iBAAoB,CAChB,OACH,CACD,YAAe,CACX,KACH,CACD,GAAM,CACF,OACH,CACD,eAAkB,CACd,OACH,CACD,KAAQ,CACJ,QACH,CACD,IAAO,CACH,SACH,CACD,WAAc,CACV,OACH,CACD,KAAQ,CACJ,WACH,CACD,IAAO,CACH,MACH,CACD,KAAQ,CACJ,WACH,CACD,IAAO,CACH,SACH,CACD,OAAU,CACN,MACH,CACD,IAAO,CACH,UACH,CACD,GAAM,CACF,QACH,CACD,MAAS,CACL,KACH,CACD,gBAAmB,CACf,QACH,CACD,IAAO,CACH,SACH,CACD,GAAM,CACF,QACH,CACD,IAAO,CACH,SACH,CACD,QAAW,CACP,KACH,CACD,IAAO,CACH,QACH,CACD,IAAO,CACH,UACH,CACD,eAAkB,CACd,OACH,CACD,IAAO,CACH,OACH,CACD,IAAO,CACH,WACH,CACD,QAAW,CACP,SACH,CACD,sBAAyB,CACrB,SACH,CACD,0BAA6B,CACzB,SACH,CACD,sBAAyB,CACrB,SACH,CACD,KAAQ,CACJ,YACH,CACD,IAAO,CACH,SACH,CACD,SAAY,CACR,KACH,CACD,KAAQ,CACJ,YACH,CACD,IAAO,CACH,MACH,CACD,IAAO,CACH,UACH,CACD,IAAO,CACH,MACH,CACD,IAAO,CACH,SACH,CACD,eAAkB,CACd,OACH,CACD,WAAc,CACV,QACH,CACD,KAAQ,CACJ,OACH,CACD,UAAa,CACT,OACH,AACL,CACJ,CACJ,EACA,eAAkB,KAClB,aAAgB,UAChB,YAAe,SACf,WAAc,CACV,IAAO,CACH,aAAgB,OAChB,MAAS,KACT,aAAgB,OAChB,QAAW,KACX,SAAY,KACZ,kBAAqB,OACrB,eAAkB,OAClB,MAAS,KACT,WAAc,OACd,WAAc,OACd,UAAa,OACb,WAAc,KACd,sBAAyB,SACzB,MAAS,OACT,MAAS,OACT,OAAU,QACV,WAAc,KACd,SAAY,OACZ,SAAY,KACZ,gBAAmB,MACnB,QAAW,MACX,sBAAyB,QACzB,eAAkB,QAClB,OAAU,KACV,QAAW,KACX,MAAS,KACT,UAAa,KACb,cAAiB,OACjB,eAAkB,OAClB,cAAiB,OACjB,cAAiB,OACjB,UAAa,QACb,SAAY,OACZ,SAAY,OACZ,QAAW,IACX,aAAgB,MAChB,IAAO,KACP,SAAY,OACZ,KAAQ,KACR,kBAAqB,MACrB,eAAkB,MAClB,aAAgB,MAChB,aAAgB,MAChB,SAAY,OACZ,SAAY,OACZ,SAAY,QACZ,SAAY,QACZ,gBAAmB,QACnB,cAAiB,OACjB,cAAiB,OACjB,UAAa,OACb,mBAAsB,UACtB,UAAa,KACb,gBAAmB,OACnB,WAAc,MAClB,CACJ,EACA,OAAU,QACd,CAGA,GAE6B,IAAMJ,EAAcE,IAGvC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}