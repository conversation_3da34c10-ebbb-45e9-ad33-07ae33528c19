!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/highcharts-more
 * @requires highcharts
 *
 * (c) 2009-2025 Torstein Honsi
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.SeriesRegistry,t._Highcharts.Series,t._Highcharts.Series.types.column,t._Highcharts.Templating,t._Highcharts.Point,t._Highcharts.Color,t._Highcharts.Chart,t._Highcharts.SVGElement,t._Highcharts.StackItem):"function"==typeof define&&define.amd?define("highcharts/highcharts-more",["highcharts/highcharts"],function(t){return e(t,t.SeriesRegistry,t.Series,t.Series,["types"],["column"],t.Templating,t.Point,t.Color,t.Chart,t.<PERSON>lement,t.Stack<PERSON>)}):"object"==typeof exports?exports["highcharts/highcharts-more"]=e(t._Highcharts,t._Highcharts.SeriesRegistry,t._Highcharts.Series,t._Highcharts.Series.types.column,t._Highcharts.Templating,t._Highcharts.Point,t._Highcharts.Color,t._Highcharts.Chart,t._Highcharts.SVGElement,t._Highcharts.StackItem):t.Highcharts=e(t.Highcharts,t.Highcharts.SeriesRegistry,t.Highcharts.Series,t.Highcharts.Series.types.column,t.Highcharts.Templating,t.Highcharts.Point,t.Highcharts.Color,t.Highcharts.Chart,t.Highcharts.SVGElement,t.Highcharts.StackItem)}("undefined"==typeof window?this:window,(t,e,i,s,o,a,r,n,l,h)=>(()=>{"use strict";var p,d,c,u={28:t=>{t.exports=l},184:t=>{t.exports=h},260:t=>{t.exports=a},448:t=>{t.exports=s},512:t=>{t.exports=e},620:t=>{t.exports=r},820:t=>{t.exports=i},944:e=>{e.exports=t},960:t=>{t.exports=n},984:t=>{t.exports=o}},g={};function f(t){var e=g[t];if(void 0!==e)return e.exports;var i=g[t]={exports:{}};return u[t](i,i.exports,f),i.exports}f.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return f.d(e,{a:e}),e},f.d=(t,e)=>{for(var i in e)f.o(e,i)&&!f.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},f.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var m={};f.d(m,{default:()=>oa});var b=f(944),y=f.n(b),x=f(512),P=f.n(x),v=f(820),M=f.n(v);let{deg2rad:L}=y(),{fireEvent:k,isNumber:w,pick:S,relativeLength:A}=y();!function(t){t.getCenter=function(){let t=this.options,e=this.chart,i=2*(t.slicedOffset||0),s=e.plotWidth-2*i,o=e.plotHeight-2*i,a=t.center,r=Math.min(s,o),n=t.thickness,l,h=t.size,p=t.innerSize||0,d,c;"string"==typeof h&&(h=parseFloat(h)),"string"==typeof p&&(p=parseFloat(p));let u=[S(a?.[0],"50%"),S(a?.[1],"50%"),S(h&&h<0?void 0:t.size,"100%"),S(p&&p<0?void 0:t.innerSize||0,"0%")];for(!e.angular||this instanceof M()||(u[3]=0),d=0;d<4;++d)c=u[d],l=d<2||2===d&&/%$/.test(c),u[d]=A(c,[s,o,r,u[2]][d])+(l?i:0);return u[3]>u[2]&&(u[3]=u[2]),w(n)&&2*n<u[2]&&n>0&&(u[3]=u[2]-2*n),k(this,"afterGetCenter",{positions:u}),u},t.getStartAndEndRadians=function(t,e){let i=w(t)?t:0,s=w(e)&&e>i&&e-i<360?e:i+360;return{start:L*(i+-90),end:L*(s+-90)}}}(p||(p={}));let N=p,{addEvent:T,correctFloat:C,defined:X,pick:Y}=y();function I(t){let e,i=this;return t&&i.pane.forEach(s=>{R(t.chartX-i.plotLeft,t.chartY-i.plotTop,s.center)&&(e=s)}),e}function R(t,e,i,s,o){let a=!0,r=i[0],n=i[1],l=Math.sqrt(Math.pow(t-r,2)+Math.pow(e-n,2));if(X(s)&&X(o)){let i=Math.atan2(C(e-n,8),C(t-r,8));o!==s&&(a=s>o?i>=s&&i<=Math.PI||i<=o&&i>=-Math.PI:i>=s&&i<=C(o,8))}return l<=Math.ceil(i[2]/2)&&a}function E(t){this.polar&&(t.options.inverted&&([t.x,t.y]=[t.y,t.x]),t.isInsidePlot=this.pane.some(e=>R(t.x,t.y,e.center,e.axis&&e.axis.normalizedStartAngleRad,e.axis&&e.axis.normalizedEndAngleRad)))}function D(t){let e=this.chart;t.hoverPoint&&t.hoverPoint.plotX&&t.hoverPoint.plotY&&e.hoverPane&&!R(t.hoverPoint.plotX,t.hoverPoint.plotY,e.hoverPane.center)&&(t.hoverPoint=void 0)}function z(t){let e=this.chart;e.polar?(e.hoverPane=e.getHoverPane(t),t.filter=function(i){return i.visible&&!(!t.shared&&i.directTouch)&&Y(i.options.enableMouseTracking,!0)&&(!e.hoverPane||i.xAxis.pane===e.hoverPane)}):e.hoverPane=void 0}let{defaultOptions:O}=y(),H={shape:"circle",borderRadius:0,borderWidth:1,borderColor:"#cccccc",backgroundColor:{linearGradient:{x1:0,y1:0,x2:0,y2:1},stops:[[0,"#ffffff"],[1,"#e6e6e6"]]},from:-Number.MAX_VALUE,innerRadius:0,to:Number.MAX_VALUE,outerRadius:"105%"},B={background:H,center:["50%","50%"],size:"85%",innerSize:"0%",startAngle:0};O.pane=B;let W={pane:B,background:H},{extend:F,merge:G,splat:q}=y();class V{constructor(t,e){this.coll="pane",this.init(t,e)}init(t,e){this.chart=e,this.background=[],e.pane.push(this),this.setOptions(t)}setOptions(t){this.options=t=G(W.pane,{background:this.chart.angular?{}:void 0},t)}render(){let t=this.options,e=this.chart.renderer;this.group||(this.group=e.g("pane-group").attr({zIndex:t.zIndex||0}).add()),this.updateCenter();let i=this.options.background;if(i){let t=Math.max((i=q(i)).length,this.background.length||0);for(let e=0;e<t;e++)i[e]&&this.axis?this.renderBackground(G(W.background,i[e]),e):this.background[e]&&(this.background[e]=this.background[e].destroy(),this.background.splice(e,1))}}renderBackground(t,e){let i={class:"highcharts-pane "+(t.className||"")},s="animate";this.chart.styledMode||F(i,{fill:t.backgroundColor,stroke:t.borderColor,"stroke-width":t.borderWidth}),this.background[e]||(this.background[e]=this.chart.renderer.path().add(this.group),s="attr"),this.background[e][s]({d:this.axis.getPlotBandPath(t.from,t.to,t)}).attr(i)}updateCenter(t){this.center=(t||this.axis||{}).center=N.getCenter.call(this)}update(t,e){G(!0,this.options,t),this.setOptions(this.options),this.render(),this.chart.axes.forEach(function(t){t.pane===this&&(t.pane=null,t.update({},e))},this)}}V.compose=function(t,e){let i=t.prototype;i.getHoverPane||(i.collectionsWithUpdate.push("pane"),i.getHoverPane=I,T(t,"afterIsInsidePlot",E),T(e,"afterGetHoverData",D),T(e,"beforeGetHoverData",z))};let{area:{prototype:{pointClass:_,pointClass:{prototype:U}}}}=P().seriesTypes,{defined:K,isNumber:Z}=y(),j=class extends _{setState(){let t=this.state,e=this.series,i=e.chart.polar;K(this.plotHigh)||(this.plotHigh=e.yAxis.toPixels(this.high,!0)),K(this.plotLow)||(this.plotLow=this.plotY=e.yAxis.toPixels(this.low,!0)),e.lowerStateMarkerGraphic=e.stateMarkerGraphic,e.stateMarkerGraphic=e.upperStateMarkerGraphic,this.graphic=this.graphics&&this.graphics[1],this.plotY=this.plotHigh,i&&Z(this.plotHighX)&&(this.plotX=this.plotHighX),U.setState.apply(this,arguments),this.state=t,this.plotY=this.plotLow,this.graphic=this.graphics&&this.graphics[0],i&&Z(this.plotLowX)&&(this.plotX=this.plotLowX),e.upperStateMarkerGraphic=e.stateMarkerGraphic,e.stateMarkerGraphic=e.lowerStateMarkerGraphic,e.lowerStateMarkerGraphic=void 0;let s=e.modifyMarkerSettings();U.setState.apply(this,arguments),e.restoreMarkerSettings(s)}haloPath(){let t=this.series.chart.polar,e=[];return this.plotY=this.plotLow,t&&Z(this.plotLowX)&&(this.plotX=this.plotLowX),this.isInside&&(e=U.haloPath.apply(this,arguments)),this.plotY=this.plotHigh,t&&Z(this.plotHighX)&&(this.plotX=this.plotHighX),this.isTopInside&&(e=e.concat(U.haloPath.apply(this,arguments))),e}isValid(){return Z(this.low)&&Z(this.high)}},{noop:$}=y(),{area:Q,area:{prototype:J},column:{prototype:tt}}=P().seriesTypes,{addEvent:te,defined:ti,extend:ts,isArray:to,isNumber:ta,pick:tr,merge:tn}=y();class tl extends Q{toYData(t){return[t.low,t.high]}highToXY(t){let e=this.chart,i=this.xAxis.postTranslate(t.rectPlotX||0,this.yAxis.len-(t.plotHigh||0));t.plotHighX=i.x-e.plotLeft,t.plotHigh=i.y-e.plotTop,t.plotLowX=t.plotX}getGraphPath(t){let e=[],i=[],s=J.getGraphPath,o=this.options,a=this.chart.polar,r=a&&!1!==o.connectEnds,n=o.connectNulls,l,h,p,d=o.step;for(l=(t=t||this.points).length;l--;){h=t[l];let s=a?{plotX:h.rectPlotX,plotY:h.yBottom,doCurve:!1}:{plotX:h.plotX,plotY:h.plotY,doCurve:!1};h.isNull||r||n||t[l+1]&&!t[l+1].isNull||i.push(s),p={polarPlotY:h.polarPlotY,rectPlotX:h.rectPlotX,yBottom:h.yBottom,plotX:tr(h.plotHighX,h.plotX),plotY:h.plotHigh,isNull:h.isNull},i.push(p),e.push(p),h.isNull||r||n||t[l-1]&&!t[l-1].isNull||i.push(s)}let c=s.call(this,t);d&&(!0===d&&(d="left"),o.step=({left:"right",center:"center",right:"left"})[d]);let u=s.call(this,e),g=s.call(this,i);o.step=d;let f=[].concat(c,u);return!this.chart.polar&&g[0]&&"M"===g[0][0]&&(g[0]=["L",g[0][1],g[0][2]]),this.graphPath=f,this.areaPath=c.concat(g),f.isArea=!0,f.xMap=c.xMap,this.areaPath.xMap=c.xMap,f}drawDataLabels(){let t,e,i,s,o,a=this.points,r=a.length,n=[],l=this.options.dataLabels,h=this.chart.inverted;if(l){if(to(l)?(s=l[0]||{enabled:!1},o=l[1]||{enabled:!1}):((s=ts({},l)).x=l.xHigh,s.y=l.yHigh,(o=ts({},l)).x=l.xLow,o.y=l.yLow),s.enabled||this.hasDataLabels?.()){for(t=r;t--;)if(e=a[t]){let{plotHigh:o=0,plotLow:a=0}=e;i=s.inside?o<a:o>a,e.y=e.high,e._plotY=e.plotY,e.plotY=o,n[t]=e.dataLabel,e.dataLabel=e.dataLabelUpper,e.below=i,h?s.align||(s.align=i?"right":"left"):s.verticalAlign||(s.verticalAlign=i?"top":"bottom")}for(this.options.dataLabels=s,J.drawDataLabels&&J.drawDataLabels.apply(this,arguments),t=r;t--;)(e=a[t])&&(e.dataLabelUpper=e.dataLabel,e.dataLabel=n[t],delete e.dataLabels,e.y=e.low,e.plotY=e._plotY)}if(o.enabled||this.hasDataLabels?.()){for(t=r;t--;)if(e=a[t]){let{plotHigh:t=0,plotLow:s=0}=e;i=o.inside?t<s:t>s,e.below=!i,h?o.align||(o.align=i?"left":"right"):o.verticalAlign||(o.verticalAlign=i?"bottom":"top")}this.options.dataLabels=o,J.drawDataLabels&&J.drawDataLabels.apply(this,arguments)}if(s.enabled)for(t=r;t--;)(e=a[t])&&(e.dataLabels=[e.dataLabelUpper,e.dataLabel].filter(function(t){return!!t}));this.options.dataLabels=l}}alignDataLabel(){tt.alignDataLabel.apply(this,arguments)}modifyMarkerSettings(){let t={marker:this.options.marker,symbol:this.symbol};if(this.options.lowMarker){let{options:{marker:t,lowMarker:e}}=this;this.options.marker=tn(t,e),e.symbol&&(this.symbol=e.symbol)}return t}restoreMarkerSettings(t){this.options.marker=t.marker,this.symbol=t.symbol}drawPoints(){let t,e,i=this.points.length,s=this.modifyMarkerSettings();for(J.drawPoints.apply(this,arguments),this.restoreMarkerSettings(s),t=0;t<i;)(e=this.points[t]).graphics=e.graphics||[],e.origProps={plotY:e.plotY,plotX:e.plotX,isInside:e.isInside,negative:e.negative,zone:e.zone,y:e.y},(e.graphic||e.graphics[0])&&(e.graphics[0]=e.graphic),e.graphic=e.graphics[1],e.plotY=e.plotHigh,ti(e.plotHighX)&&(e.plotX=e.plotHighX),e.y=tr(e.high,e.origProps.y),e.negative=e.y<(this.options.threshold||0),this.zones.length&&(e.zone=e.getZone()),this.chart.polar||(e.isInside=e.isTopInside=void 0!==e.plotY&&e.plotY>=0&&e.plotY<=this.yAxis.len&&e.plotX>=0&&e.plotX<=this.xAxis.len),t++;for(J.drawPoints.apply(this,arguments),t=0;t<i;)(e=this.points[t]).graphics=e.graphics||[],(e.graphic||e.graphics[1])&&(e.graphics[1]=e.graphic),e.graphic=e.graphics[0],e.origProps&&(ts(e,e.origProps),delete e.origProps),t++}hasMarkerChanged(t,e){let i=t.lowMarker,s=e.lowMarker||{};return i&&(!1===i.enabled||s.symbol!==i.symbol||s.height!==i.height||s.width!==i.width)||super.hasMarkerChanged(t,e)}}tl.defaultOptions=tn(Q.defaultOptions,{lineWidth:1,threshold:null,tooltip:{pointFormat:'<span style="color:{series.color}">●</span> {series.name}: <b>{point.low}</b> - <b>{point.high}</b><br/>'},trackByArea:!0,dataLabels:{align:void 0,verticalAlign:void 0,xLow:0,xHigh:0,yLow:0,yHigh:0}}),te(tl,"afterTranslate",function(){"low,high"===this.pointArrayMap.join(",")&&this.points.forEach(t=>{let e=t.high,i=t.plotY;t.isNull?t.plotY=void 0:(t.plotLow=i,t.plotHigh=ta(e)?this.yAxis.translate(this.dataModify?this.dataModify.modifyValue(e):e,!1,!0,void 0,!0):void 0,this.dataModify&&(t.yBottom=t.plotHigh))})},{order:0}),te(tl,"afterTranslate",function(){this.points.forEach(t=>{if(this.chart.polar)this.highToXY(t),t.plotLow=t.plotY,t.tooltipPos=[((t.plotHighX||0)+(t.plotLowX||0))/2,((t.plotHigh||0)+(t.plotLow||0))/2];else{let e=t.pos(!1,t.plotLow),i=t.pos(!1,t.plotHigh);e&&i&&(e[0]=(e[0]+i[0])/2,e[1]=(e[1]+i[1])/2),t.tooltipPos=e}})},{order:3}),ts(tl.prototype,{deferTranslatePolar:!0,pointArrayMap:["low","high"],pointClass:j,pointValKey:"low",setStackedPoints:$}),P().registerSeriesType("arearange",tl);let th=tl,{spline:{prototype:tp}}=P().seriesTypes,{merge:td,extend:tc}=y();class tu extends th{}tu.defaultOptions=td(th.defaultOptions),tc(tu.prototype,{getPointSpline:tp.getPointSpline}),P().registerSeriesType("areasplinerange",tu);var tg=f(448),tf=f.n(tg);let{noop:tm}=y(),{crisp:tb,extend:ty,merge:tx,pick:tP,relativeLength:tv}=y();class tM extends tf(){pointAttribs(){return{}}getWhiskerPair(t,e,i,s,o){let a=o.whiskers.strokeWidth(),r=(i,s)=>{let o=tv(i,2*t)/2,r=tb(s,a);return[["M",tb(e-o),r],["L",tb(e+o),r]]};return[...r(i,o.highPlot),...r(s,o.lowPlot)]}translate(){let t=this.yAxis,e=this.pointArrayMap;super.translate.apply(this),this.points.forEach(function(i){e.forEach(function(e){null!==i[e]&&(i[e+"Plot"]=t.translate(i[e],0,1,0,1))}),i.plotHigh=i.highPlot})}drawPoints(){let t,e,i,s,o,a,r,n,l,h,p,d=this.points,c=this.options,u=this.chart,g=u.renderer,f=!1!==this.doQuartiles,m=this.options.whiskerLength;for(let b of d){let d=(n=b.graphic)?"animate":"attr",y=b.shapeArgs,x={},P={},v={},M={},L=b.color||this.color,k=b.options.whiskerLength||m;if(void 0!==b.plotY){let w;l=y.width,p=(h=y.x)+l,t=f?b.q1Plot:b.lowPlot,e=f?b.q3Plot:b.lowPlot,i=b.highPlot,s=b.lowPlot,n||(b.graphic=n=g.g("point").add(this.group),b.stem=g.path().addClass("highcharts-boxplot-stem").add(n),m&&(b.whiskers=g.path().addClass("highcharts-boxplot-whisker").add(n)),f&&(b.box=g.path(r).addClass("highcharts-boxplot-box").add(n)),b.medianShape=g.path(a).addClass("highcharts-boxplot-median").add(n)),u.styledMode||(P.stroke=b.stemColor||c.stemColor||L,P["stroke-width"]=tP(b.stemWidth,c.stemWidth,c.lineWidth),P.dashstyle=b.stemDashStyle||c.stemDashStyle||c.dashStyle,b.stem.attr(P),k&&(v.stroke=b.whiskerColor||c.whiskerColor||L,v["stroke-width"]=tP(b.whiskerWidth,c.whiskerWidth,c.lineWidth),v.dashstyle=b.whiskerDashStyle||c.whiskerDashStyle||c.dashStyle,b.whiskers.attr(v)),f&&(x.fill=b.fillColor||c.fillColor||L,x.stroke=c.lineColor||L,x["stroke-width"]=c.lineWidth||0,x.dashstyle=b.boxDashStyle||c.boxDashStyle||c.dashStyle,b.box.attr(x)),M.stroke=b.medianColor||c.medianColor||L,M["stroke-width"]=tP(b.medianWidth,c.medianWidth,c.lineWidth),M.dashstyle=b.medianDashStyle||c.medianDashStyle||c.dashStyle,b.medianShape.attr(M));let S=tb((b.plotX||0)+(this.pointXOffset||0)+(this.barW||0)/2,b.stem.strokeWidth());if(w=[["M",S,e],["L",S,i],["M",S,t],["L",S,s]],b.stem[d]({d:w}),f){let i=b.box.strokeWidth();t=tb(t,i),e=tb(e,i),w=[["M",h=tb(h,i),e],["L",h,t],["L",p=tb(p,i),t],["L",p,e],["L",h,e],["Z"]],b.box[d]({d:w})}if(k){let t=l/2,e=this.getWhiskerPair(t,S,b.upperWhiskerLength??c.upperWhiskerLength??k,b.lowerWhiskerLength??c.lowerWhiskerLength??k,b);b.whiskers[d]({d:e})}w=[["M",h,o=tb(b.medianPlot,b.medianShape.strokeWidth())],["L",p,o]],b.medianShape[d]({d:w})}}}toYData(t){return[t.low,t.q1,t.median,t.q3,t.high]}}tM.defaultOptions=tx(tf().defaultOptions,{threshold:null,tooltip:{pointFormat:'<span style="color:{point.color}">●</span> <b>{series.name}</b><br/>Maximum: {point.high}<br/>Upper quartile: {point.q3}<br/>Median: {point.median}<br/>Lower quartile: {point.q1}<br/>Minimum: {point.low}<br/>'},whiskerLength:"50%",fillColor:"#ffffff",lineWidth:1,medianWidth:2,whiskerWidth:2}),ty(tM.prototype,{pointArrayMap:["low","q1","median","q3","high"],pointValKey:"high",drawDataLabels:tm,setStackedPoints:tm}),P().registerSeriesType("boxplot",tM);let tL=tM,tk={borderColor:void 0,borderWidth:2,className:void 0,color:void 0,connectorClassName:void 0,connectorColor:void 0,connectorDistance:60,connectorWidth:1,enabled:!1,labels:{className:void 0,allowOverlap:!1,format:"",formatter:void 0,align:"right",style:{fontSize:"0.9em",color:"#000000"},x:0,y:0},maxSize:60,minSize:10,legendIndex:0,ranges:{value:void 0,borderColor:void 0,color:void 0,connectorColor:void 0},sizeBy:"area",sizeByAbsoluteValue:!1,zIndex:1,zThreshold:0};var tw=f(984),tS=f.n(tw);let{noop:tA}=y(),{arrayMax:tN,arrayMin:tT,isNumber:tC,merge:tX,pick:tY,stableSort:tI}=y(),tR=class{constructor(t,e){this.setState=tA,this.init(t,e)}init(t,e){this.options=t,this.visible=!0,this.chart=e.chart,this.legend=e}addToLegend(t){t.splice(this.options.legendIndex,0,this)}drawLegendSymbol(t){let e,i=tY(t.options.itemDistance,20),s=this.legendItem||{},o=this.options,a=o.ranges,r=o.connectorDistance;if(!a||!a.length||!tC(a[0].value)){t.options.bubbleLegend.autoRanges=!0;return}tI(a,function(t,e){return e.value-t.value}),this.ranges=a,this.setOptions(),this.render();let n=this.getMaxLabelSize(),l=this.ranges[0].radius,h=2*l;e=(e=r-l+n.width)>0?e:0,this.maxLabel=n,this.movementX="left"===o.labels.align?e:0,s.labelWidth=h+e+i,s.labelHeight=h+n.height/2}setOptions(){let t=this.ranges,e=this.options,i=this.chart.series[e.seriesIndex],s=this.legend.baseline,o={zIndex:e.zIndex,"stroke-width":e.borderWidth},a={zIndex:e.zIndex,"stroke-width":e.connectorWidth},r={align:this.legend.options.rtl||"left"===e.labels.align?"right":"left",zIndex:e.zIndex},n=i.options.marker.fillOpacity,l=this.chart.styledMode;t.forEach(function(h,p){l||(o.stroke=tY(h.borderColor,e.borderColor,i.color),o.fill=h.color||e.color,o.fill||(o.fill=i.color,o["fill-opacity"]=n??1),a.stroke=tY(h.connectorColor,e.connectorColor,i.color)),t[p].radius=this.getRangeRadius(h.value),t[p]=tX(t[p],{center:t[0].radius-t[p].radius+s}),l||tX(!0,t[p],{bubbleAttribs:tX(o),connectorAttribs:tX(a),labelAttribs:r})},this)}getRangeRadius(t){let e=this.options,i=this.options.seriesIndex,s=this.chart.series[i],o=e.ranges[0].value,a=e.ranges[e.ranges.length-1].value,r=e.minSize,n=e.maxSize;return s.getRadius.call(this,a,o,r,n,t)}render(){let t=this.legendItem||{},e=this.chart.renderer,i=this.options.zThreshold;for(let s of(this.symbols||(this.symbols={connectors:[],bubbleItems:[],labels:[]}),t.symbol=e.g("bubble-legend"),t.label=e.g("bubble-legend-item").css(this.legend.itemStyle||{}),t.symbol.translateX=0,t.symbol.translateY=0,t.symbol.add(t.label),t.label.add(t.group),this.ranges))s.value>=i&&this.renderRange(s);this.hideOverlappingLabels()}renderRange(t){let e=this.ranges[0],i=this.legend,s=this.options,o=s.labels,a=this.chart,r=a.series[s.seriesIndex],n=a.renderer,l=this.symbols,h=l.labels,p=t.center,d=Math.abs(t.radius),c=s.connectorDistance||0,u=o.align,g=i.options.rtl,f=s.borderWidth,m=s.connectorWidth,b=e.radius||0,y=p-d-f/2+m/2,x=(y%1?1:.5)-(m%2?0:.5),P=n.styledMode,v=g||"left"===u?-c:c;"center"===u&&(v=0,s.connectorDistance=0,t.labelAttribs.align="center"),l.bubbleItems.push(n.circle(b,p+x,d).attr(P?{}:t.bubbleAttribs).addClass((P?"highcharts-color-"+r.colorIndex+" ":"")+"highcharts-bubble-legend-symbol "+(s.className||"")).add(this.legendItem.symbol)),l.connectors.push(n.path(n.crispLine([["M",b,y],["L",b+v,y]],s.connectorWidth)).attr(P?{}:t.connectorAttribs).addClass((P?"highcharts-color-"+this.options.seriesIndex+" ":"")+"highcharts-bubble-legend-connectors "+(s.connectorClassName||"")).add(this.legendItem.symbol));let M=n.text(this.formatLabel(t)).attr(P?{}:t.labelAttribs).css(P?{}:o.style).addClass("highcharts-bubble-legend-labels "+(s.labels.className||"")).add(this.legendItem.symbol),L={x:b+v+s.labels.x,y:y+s.labels.y+.4*M.getBBox().height};M.attr(L),h.push(M),M.placed=!0,M.alignAttr=L}getMaxLabelSize(){let t,e;return this.symbols.labels.forEach(function(i){e=i.getBBox(!0),t=t?e.width>t.width?e:t:e}),t||{}}formatLabel(t){let e=this.options,i=e.labels.formatter,s=e.labels.format,{numberFormatter:o}=this.chart;return s?tS().format(s,t,this.chart):i?i.call(t):o(t.value,1)}hideOverlappingLabels(){let t=this.chart,e=this.options.labels.allowOverlap,i=this.symbols;!e&&i&&(t.hideOverlappingLabels(i.labels),i.labels.forEach(function(t,e){t.newOpacity?t.newOpacity!==t.oldOpacity&&i.connectors[e].show():i.connectors[e].hide()}))}getRanges(){let t=this.legend.bubbleLegend,e=t.chart.series,i=t.options.ranges,s,o,a=Number.MAX_VALUE,r=-Number.MAX_VALUE;return e.forEach(function(t){t.isBubble&&!t.ignoreSeries&&(o=t.getColumn("z").filter(tC)).length&&(a=tY(t.options.zMin,Math.min(a,Math.max(tT(o),!1===t.options.displayNegative?t.options.zThreshold:-Number.MAX_VALUE))),r=tY(t.options.zMax,Math.max(r,tN(o))))}),s=a===r?[{value:r}]:[{value:a},{value:(a+r)/2},{value:r,autoRanges:!0}],i.length&&i[0].radius&&s.reverse(),s.forEach(function(t,e){i&&i[e]&&(s[e]=tX(i[e],t))}),s}predictBubbleSizes(){let t=this.chart,e=t.legend.options,i=e.floating,s="horizontal"===e.layout,o=s?t.legend.lastLineHeight:0,a=t.plotSizeX,r=t.plotSizeY,n=t.series[this.options.seriesIndex],l=n.getPxExtremes(),h=Math.ceil(l.minPxSize),p=Math.ceil(l.maxPxSize),d=Math.min(r,a),c,u=n.options.maxSize;return i||!/%$/.test(u)?c=p:(c=(d+o)*(u=parseFloat(u))/100/(u/100+1),(s&&r-c>=a||!s&&a-c>=r)&&(c=p)),[h,Math.ceil(c)]}updateRanges(t,e){let i=this.legend.options.bubbleLegend;i.minSize=t,i.maxSize=e,i.ranges=this.getRanges()}correctSizes(){let t=this.legend,e=this.chart.series[this.options.seriesIndex].getPxExtremes();Math.abs(Math.ceil(e.maxPxSize)-this.options.maxSize)>1&&(this.updateRanges(this.options.minSize,e.maxPxSize),t.render())}},{setOptions:tE}=y(),{composed:tD}=y(),{addEvent:tz,objectEach:tO,pushUnique:tH,wrap:tB}=y();function tW(t,e,i){let s,o,a,r=this.legend,n=tF(this)>=0;r&&r.options.enabled&&r.bubbleLegend&&r.options.bubbleLegend.autoRanges&&n?(s=r.bubbleLegend.options,o=r.bubbleLegend.predictBubbleSizes(),r.bubbleLegend.updateRanges(o[0],o[1]),s.placed||(r.group.placed=!1,r.allItems.forEach(t=>{(a=t.legendItem||{}).group&&(a.group.translateY=void 0)})),r.render(),s.placed||(this.getMargins(),this.axes.forEach(t=>{t.setScale(),t.updateNames(),tO(t.ticks,function(t){t.isNew=!0,t.isNewLabel=!0})}),this.getMargins()),s.placed=!0,t.call(this,e,i),r.bubbleLegend.correctSizes(),t_(r,tG(r))):(t.call(this,e,i),r&&r.options.enabled&&r.bubbleLegend&&(r.render(),t_(r,tG(r))))}function tF(t){let e=t.series,i=0;for(;i<e.length;){if(e[i]&&e[i].isBubble&&e[i].visible&&e[i].dataTable.rowCount)return i;i++}return -1}function tG(t){let e=t.allItems,i=[],s=e.length,o,a,r,n=0,l=0;for(n=0;n<s;n++)if(a=e[n].legendItem||{},r=(e[n+1]||{}).legendItem||{},a.labelHeight&&(e[n].itemHeight=a.labelHeight),e[n]===e[s-1]||a.y!==r.y){for(i.push({height:0}),o=i[i.length-1];l<=n;l++)e[l].itemHeight>o.height&&(o.height=e[l].itemHeight);o.step=n}return i}function tq(t){let e=this.bubbleLegend,i=this.options,s=i.bubbleLegend,o=tF(this.chart);e&&e.ranges&&e.ranges.length&&(s.ranges.length&&(s.autoRanges=!!s.ranges[0].autoRanges),this.destroyItem(e)),o>=0&&i.enabled&&s.enabled&&(s.seriesIndex=o,this.bubbleLegend=new tR(s,this),this.bubbleLegend.addToLegend(t.allItems))}function tV(t){let e;if(t.defaultPrevented)return!1;let i=t.legendItem,s=this.chart,o=i.visible;this&&this.bubbleLegend&&(i.visible=!o,i.ignoreSeries=o,e=tF(s)>=0,this.bubbleLegend.visible!==e&&(this.update({bubbleLegend:{enabled:e}}),this.bubbleLegend.visible=e),i.visible=o)}function t_(t,e){let i=t.allItems,s=t.options.rtl,o,a,r,n,l=0;i.forEach((t,i)=>{(n=t.legendItem||{}).group&&(o=n.group.translateX||0,a=n.y||0,((r=t.movementX)||s&&t.ranges)&&(r=s?o-t.options.maxSize/2:o+r,n.group.attr({translateX:r})),i>e[l].step&&l++,n.group.attr({translateY:Math.round(a+e[l].height/2)}),n.y=a+e[l].height/2)})}let tU={compose:function(t,e){tH(tD,"Series.BubbleLegend")&&(tE({legend:{bubbleLegend:tk}}),tB(t.prototype,"drawChartBox",tW),tz(e,"afterGetAllItems",tq),tz(e,"itemClick",tV))}};var tK=f(260),tZ=f.n(tK);let{seriesTypes:{scatter:{prototype:{pointClass:tj}}}}=P(),{extend:t$}=y();class tQ extends tj{haloPath(t){let e=(t&&this.marker&&this.marker.radius||0)+t;if(this.series.chart.inverted){let t=this.pos()||[0,0],{xAxis:i,yAxis:s,chart:o}=this.series,a=2*e;return o.renderer.symbols.circle((i?.len||0)-t[1]-e,(s?.len||0)-t[0]-e,a,a)}return tZ().prototype.haloPath.call(this,e)}}t$(tQ.prototype,{ttBelow:!1});let{composed:tJ,noop:t0}=y(),{series:t1,seriesTypes:{column:{prototype:t2},scatter:t3}}=P(),{addEvent:t5,arrayMax:t8,arrayMin:t6,clamp:t4,extend:t9,isNumber:t7,merge:et,pick:ee,pushUnique:ei}=y();function es(){let t=this.len,{coll:e,isXAxis:i,min:s}=this,o=(this.max||0)-(s||0),a=0,r=t,n=t/o,l;("xAxis"===e||"yAxis"===e)&&(this.series.forEach(t=>{if(t.bubblePadding&&t.reserveSpace()){this.allowZoomOutside=!0,l=!0;let e=t.getColumn(i?"x":"y");if(i&&((t.onPoint||t).getRadii(0,0,t),t.onPoint&&(t.radii=t.onPoint.radii)),o>0){let i=e.length;for(;i--;)if(t7(e[i])&&this.dataMin<=e[i]&&e[i]<=this.max){let o=t.radii&&t.radii[i]||0;a=Math.min((e[i]-s)*n-o,a),r=Math.max((e[i]-s)*n+o,r)}}}}),l&&o>0&&!this.logarithmic&&(r-=t,n*=(t+Math.max(0,a)-Math.min(r,t))/t,[["min","userMin",a],["max","userMax",r]].forEach(t=>{void 0===ee(this.options[t[0]],this[t[1]])&&(this[t[0]]+=t[2]/n)})))}function eo(){let{ticks:t,tickPositions:e,dataMin:i=0,dataMax:s=0,categories:o}=this,a=this.options.type;if((o?.length||"category"===a)&&this.series.find(t=>t.bubblePadding)){let o=e.length;for(;o--;){let a=t[e[o]],r=a.pos||0;(r>s||r<i)&&a.label?.hide()}}}class ea extends t3{static compose(t,e,i){tU.compose(e,i),ei(tJ,"Series.Bubble")&&(t5(t,"foundExtremes",es),t5(t,"afterRender",eo))}animate(t){!t&&this.points.length<this.options.animationLimit&&this.points.forEach(function(t){let{graphic:e,plotX:i=0,plotY:s=0}=t;e&&e.width&&(this.hasRendered||e.attr({x:i,y:s,width:1,height:1}),e.animate(this.markerAttribs(t),this.options.animation))},this)}getRadii(){let t=this.getColumn("z"),e=this.getColumn("y"),i=[],s,o,a,r=this.chart.bubbleZExtremes,{minPxSize:n,maxPxSize:l}=this.getPxExtremes();if(!r){let t,e=Number.MAX_VALUE,i=-Number.MAX_VALUE;this.chart.series.forEach(s=>{if(s.bubblePadding&&s.reserveSpace()){let o=(s.onPoint||s).getZExtremes();o&&(e=Math.min(ee(e,o.zMin),o.zMin),i=Math.max(ee(i,o.zMax),o.zMax),t=!0)}}),t?(r={zMin:e,zMax:i},this.chart.bubbleZExtremes=r):r={zMin:0,zMax:0}}for(o=0,s=t.length;o<s;o++)a=t[o],i.push(this.getRadius(r.zMin,r.zMax,n,l,a,e&&e[o]));this.radii=i}getRadius(t,e,i,s,o,a){let r=this.options,n="width"!==r.sizeBy,l=r.zThreshold,h=e-t,p=.5;if(null===a||null===o)return null;if(t7(o)){if(r.sizeByAbsoluteValue&&(o=Math.abs(o-l),e=h=Math.max(e-l,Math.abs(t-l)),t=0),o<t)return i/2-1;h>0&&(p=(o-t)/h)}return n&&p>=0&&(p=Math.sqrt(p)),Math.ceil(i+p*(s-i))/2}hasData(){return!!this.dataTable.rowCount}markerAttribs(t,e){let i=super.markerAttribs(t,e),{height:s=0,width:o=0}=i;return this.chart.inverted?t9(i,{x:(t.plotX||0)-o/2,y:(t.plotY||0)-s/2}):i}pointAttribs(t,e){let i=this.options.marker,s=i?.fillOpacity,o=t1.prototype.pointAttribs.call(this,t,e);return o["fill-opacity"]=s??1,o}translate(){super.translate.call(this),this.getRadii(),this.translateBubble()}translateBubble(){let{data:t,options:e,radii:i}=this,{minPxSize:s}=this.getPxExtremes(),o=t.length;for(;o--;){let a=t[o],r=i?i[o]:0;"z"===this.zoneAxis&&(a.negative=(a.z||0)<(e.zThreshold||0)),t7(r)&&r>=s/2?(a.marker=t9(a.marker,{radius:r,width:2*r,height:2*r}),a.dlBox={x:a.plotX-r,y:a.plotY-r,width:2*r,height:2*r}):(a.shapeArgs=a.plotY=a.dlBox=void 0,a.isInside=!1)}}getPxExtremes(){let t=Math.min(this.chart.plotWidth,this.chart.plotHeight),e=e=>{let i;return"string"==typeof e&&(i=/%$/.test(e),e=parseInt(e,10)),i?t*e/100:e},i=e(ee(this.options.minSize,8)),s=Math.max(e(ee(this.options.maxSize,"20%")),i);return{minPxSize:i,maxPxSize:s}}getZExtremes(){let t=this.options,e=this.getColumn("z").filter(t7);if(e.length){let i=ee(t.zMin,t4(t6(e),!1===t.displayNegative?t.zThreshold||0:-Number.MAX_VALUE,Number.MAX_VALUE)),s=ee(t.zMax,t8(e));if(t7(i)&&t7(s))return{zMin:i,zMax:s}}}searchKDTree(t,e,i,s=t0,o=t0){return s=(t,e,i)=>{let s=t[i]||0,o=e[i]||0,a,r=!1;return s===o?a=t.index>e.index?t:e:s<0&&o<0?(a=s-(t.marker?.radius||0)>=o-(e.marker?.radius||0)?t:e,r=!0):a=s<o?t:e,[a,r]},o=(t,e,i)=>!i&&t>e||t<e,super.searchKDTree(t,e,i,s,o)}}ea.defaultOptions=et(t3.defaultOptions,{dataLabels:{formatter:function(){let{numberFormatter:t}=this.series.chart,{z:e}=this.point;return t7(e)?t(e,-1):""},inside:!0,verticalAlign:"middle"},animationLimit:250,marker:{lineColor:null,lineWidth:1,fillOpacity:.5,radius:null,states:{hover:{radiusPlus:0}},symbol:"circle"},minSize:8,maxSize:"20%",softThreshold:!1,states:{hover:{halo:{size:5}}},tooltip:{pointFormat:"({point.x}, {point.y}), Size: {point.z}"},turboThreshold:0,zThreshold:0,zoneAxis:"z"}),t9(ea.prototype,{alignDataLabel:t2.alignDataLabel,applyZones:t0,bubblePadding:!0,isBubble:!0,keysAffectYAxis:["y"],pointArrayMap:["y","z"],pointClass:tQ,parallelArrays:["x","y","z"],trackerGroups:["group","dataLabelsGroup"],specialGroup:"group",zoneAxis:"z"}),t5(ea,"updatedData",t=>{delete t.target.chart.bubbleZExtremes}),t5(ea,"remove",t=>{delete t.target.chart.bubbleZExtremes}),P().registerSeriesType("bubble",ea);let{seriesTypes:{column:{prototype:{pointClass:{prototype:er}}},arearange:{prototype:{pointClass:en}}}}=P(),{extend:el,isNumber:eh}=y();class ep extends en{isValid(){return eh(this.low)}}el(ep.prototype,{setState:er.setState});let{noop:ed}=y(),{seriesTypes:{arearange:ec,column:eu,column:{prototype:eg}}}=P(),{addEvent:ef,clamp:em,extend:eb,isNumber:ey,merge:ex,pick:eP}=y();class ev extends ec{setOptions(){return ex(!0,arguments[0],{stacking:void 0}),ec.prototype.setOptions.apply(this,arguments)}translate(){return eg.translate.apply(this)}pointAttribs(){return eg.pointAttribs.apply(this,arguments)}translate3dPoints(){return eg.translate3dPoints.apply(this,arguments)}translate3dShapes(){return eg.translate3dShapes.apply(this,arguments)}afterColumnTranslate(){let t,e,i,s,o=this.yAxis,a=this.xAxis,r=a.startAngleRad,n=this.chart,l=this.xAxis.isRadial,h=Math.max(n.chartWidth,n.chartHeight)+999;this.points.forEach(p=>{let d=p.shapeArgs||{},c=this.options.minPointLength,u=p.plotY,g=o.translate(p.high,0,1,0,1);if(ey(g)&&ey(u))if(p.plotHigh=em(g,-h,h),p.plotLow=em(u,-h,h),s=p.plotHigh,Math.abs(t=eP(p.rectPlotY,p.plotY)-p.plotHigh)<c?(e=c-t,t+=e,s-=e/2):t<0&&(t*=-1,s-=t),l&&this.polar)i=p.barX+r,p.shapeType="arc",p.shapeArgs=this.polar.arc(s+t,s,i,i+p.pointWidth);else{d.height=t,d.y=s;let{x:e=0,width:i=0}=d;p.shapeArgs=ex(p.shapeArgs,this.crispCol(e,s,i,t)),p.tooltipPos=n.inverted?[o.len+o.pos-n.plotLeft-s-t/2,a.len+a.pos-n.plotTop-e-i/2,t]:[a.left-n.plotLeft+e+i/2,o.pos-n.plotTop+s+t/2,t]}})}}ev.defaultOptions=ex(eu.defaultOptions,ec.defaultOptions,{borderRadius:{where:"all"},pointRange:null,legendSymbol:"rectangle",marker:null,states:{hover:{halo:!1}}}),ef(ev,"afterColumnTranslate",function(){ev.prototype.afterColumnTranslate.apply(this)},{order:5}),eb(ev.prototype,{directTouch:!0,pointClass:ep,trackerGroups:["group","dataLabelsGroup"],adjustForMissingColumns:eg.adjustForMissingColumns,animate:eg.animate,crispCol:eg.crispCol,drawGraph:ed,drawPoints:eg.drawPoints,getSymbol:ed,drawTracker:eg.drawTracker,getColumnMetrics:eg.getColumnMetrics}),P().registerSeriesType("columnrange",ev);let{column:eM}=P().seriesTypes,{clamp:eL,merge:ek,pick:ew}=y();class eS extends eM{translate(){let t=this.chart,e=this.options,i=this.dense=this.closestPointRange*this.xAxis.transA<2,s=this.borderWidth=ew(e.borderWidth,+!i),o=this.yAxis,a=e.threshold,r=ew(e.minPointLength,5),n=this.getColumnMetrics(),l=n.width,h=this.pointXOffset=n.offset,p=this.translatedThreshold=o.getThreshold(a),d=this.barW=Math.max(l,1+2*s);for(let i of(t.inverted&&(p-=.5),e.pointPadding&&(d=Math.ceil(d)),super.translate(),this.points)){let s=ew(i.yBottom,p),c=999+Math.abs(s),u=eL(i.plotY,-c,o.len+c),g=d/2,f=Math.min(u,s),m=Math.max(u,s)-f,b=i.plotX+h,y,x,P,v,M,L,k,w,S,A,N;e.centerInCategory&&(b=this.adjustForMissingColumns(b,l,i,n)),i.barX=b,i.pointWidth=l,i.tooltipPos=t.inverted?[o.len+o.pos-t.plotLeft-u,this.xAxis.len-b-g,m]:[b+g,u+o.pos-t.plotTop,m],y=a+(i.total||i.y),"percent"===e.stacking&&(y=a+(i.y<0)?-100:100);let T=o.toPixels(y,!0);P=(x=t.plotHeight-T-(t.plotHeight-p))?g*(f-T)/x:0,v=x?g*(f+m-T)/x:0,L=b-P+g,k=b+P+g,w=b+v+g,S=b-v+g,A=f-r,N=f+m,i.y<0&&(A=f,N=f+m+r),t.inverted&&(M=o.width-f,x=T-(o.width-p),P=g*(T-M)/x,v=g*(T-(M-m))/x,k=(L=b+g+P)-2*P,w=b-v+g,S=b+v+g,A=f,N=f+m-r,i.y<0&&(N=f+m+r)),i.shapeType="path",i.shapeArgs={x:L,y:A,width:k-L,height:m,d:[["M",L,A],["L",k,A],["L",w,N],["L",S,N],["Z"]]}}}}eS.defaultOptions=ek(eM.defaultOptions,{}),P().registerSeriesType("columnpyramid",eS);let{arearange:eA}=P().seriesTypes,{addEvent:eN,merge:eT,extend:eC}=y();class eX extends tL{getColumnMetrics(){return this.linkedParent&&this.linkedParent.columnMetrics||tf().prototype.getColumnMetrics.call(this)}drawDataLabels(){let t=this.pointValKey;if(eA)for(let e of(eA.prototype.drawDataLabels.call(this),this.points))e.y=e[t]}toYData(t){return[t.low,t.high]}}eX.defaultOptions=eT(tL.defaultOptions,{color:"#000000",grouping:!1,linkedTo:":previous",tooltip:{pointFormat:'<span style="color:{point.color}">●</span> {series.name}: <b>{point.low}</b> - <b>{point.high}</b><br/>'},whiskerWidth:null}),eN(eX,"afterTranslate",function(){for(let t of this.points)t.plotLow=t.plotY},{order:0}),eC(eX.prototype,{pointArrayMap:["low","high"],pointValKey:"high",doQuartiles:!1}),P().registerSeriesType("errorbar",eX);let{series:{prototype:{pointClass:eY}}}=P(),{noop:eI}=y(),{series:eR,seriesTypes:{column:eE}}=P(),{clamp:eD,isNumber:ez,extend:eO,merge:eH,pick:eB,pInt:eW,defined:eF}=y();class eG extends eR{translate(){let t=this.yAxis,e=this.options,i=t.center;this.generatePoints(),this.points.forEach(s=>{let o=eH(e.dial,s.dial),a=eW(o.radius)*i[2]/200,r=eW(o.baseLength)*a/100,n=eW(o.rearLength)*a/100,l=o.baseWidth,h=o.topWidth,p=e.overshoot,d=t.startAngleRad+t.translate(s.y,void 0,void 0,void 0,!0);(ez(p)||!1===e.wrap)&&(p=ez(p)?p/180*Math.PI:0,d=eD(d,t.startAngleRad-p,t.endAngleRad+p)),d=180*d/Math.PI,s.shapeType="path",s.shapeArgs={d:o.path||[["M",-n,-l/2],["L",r,-l/2],["L",a,-h/2],["L",a,h/2],["L",r,l/2],["L",-n,l/2],["Z"]],translateX:i[0],translateY:i[1],rotation:d},s.plotX=i[0],s.plotY=i[1],eF(s.y)&&t.max-t.min&&(s.percentage=(s.y-t.min)/(t.max-t.min)*100)})}drawPoints(){let t=this,e=t.chart,i=t.yAxis.center,s=t.pivot,o=t.options,a=o.pivot,r=e.renderer;t.points.forEach(i=>{let s=i.graphic,a=i.shapeArgs,n=a.d,l=eH(o.dial,i.dial);s?(s.animate(a),a.d=n):i.graphic=r[i.shapeType](a).addClass("highcharts-dial").add(t.group),e.styledMode||i.graphic[s?"animate":"attr"]({stroke:l.borderColor,"stroke-width":l.borderWidth,fill:l.backgroundColor})}),s?s.animate({translateX:i[0],translateY:i[1]}):a&&(t.pivot=r.circle(0,0,a.radius).attr({zIndex:2}).addClass("highcharts-pivot").translate(i[0],i[1]).add(t.group),e.styledMode||t.pivot.attr({fill:a.backgroundColor,stroke:a.borderColor,"stroke-width":a.borderWidth}))}animate(t){let e=this;t||e.points.forEach(t=>{let i=t.graphic;i&&(i.attr({rotation:180*e.yAxis.startAngleRad/Math.PI}),i.animate({rotation:t.shapeArgs.rotation},e.options.animation))})}render(){this.group=this.plotGroup("group","series",this.visible?"inherit":"hidden",this.options.zIndex,this.chart.seriesGroup),eR.prototype.render.call(this),this.group.clip(this.chart.clipRect)}setData(t,e){eR.prototype.setData.call(this,t,!1),this.processData(),this.generatePoints(),eB(e,!0)&&this.chart.redraw()}hasData(){return!!this.points.length}}eG.defaultOptions=eH(eR.defaultOptions,{dataLabels:{borderColor:"#cccccc",borderRadius:3,borderWidth:1,crop:!1,defer:!1,enabled:!0,verticalAlign:"top",y:15,zIndex:2},dial:{backgroundColor:"#000000",baseLength:"70%",baseWidth:3,borderColor:"#cccccc",borderWidth:0,radius:"80%",rearLength:"10%",topWidth:1},pivot:{radius:5,borderWidth:0,borderColor:"#cccccc",backgroundColor:"#000000"},tooltip:{headerFormat:""},showInLegend:!1}),eO(eG.prototype,{angular:!0,directTouch:!0,drawGraph:eI,drawTracker:eE.prototype.drawTracker,fixedBox:!0,forceDL:!0,noSharedTooltip:!0,pointClass:class extends eY{setState(t){this.state=t}},trackerGroups:["group","dataLabelsGroup"]}),P().registerSeriesType("gauge",eG);var eq=f(620),eV=f.n(eq);let{composed:e_}=y(),{addEvent:eU,pushUnique:eK}=y();function eZ(){let t,e,i,s,o=this;o.container&&(t=eU(o.container,"mousedown",t=>{e&&e(),i&&i(),(s=o.hoverPoint)&&s.series&&s.series.hasDraggableNodes&&s.series.options.draggable&&(s.series.onMouseDown(s,t),e=eU(o.container,"mousemove",t=>s&&s.series&&s.series.onMouseMove(s,t)),i=eU(o.container.ownerDocument,"mouseup",t=>(e(),i(),s&&s.series&&s.series.onMouseUp(s,t))))})),eU(o,"destroy",function(){t()})}let ej={compose:function(t){eK(e_,"DragNodes")&&eU(t,"load",eZ)},onMouseDown:function(t,e){let{panKey:i}=this.chart.options.chart;if(i&&e[`${i}Key`])return;let s=this.chart.pointer?.normalize(e)||e;t.fixedPosition={chartX:s.chartX,chartY:s.chartY,plotX:t.plotX,plotY:t.plotY},t.inDragMode=!0},onMouseMove:function(t,e){if(t.fixedPosition&&t.inDragMode){let i,s,o=this.chart,a=o.pointer?.normalize(e)||e,r=t.fixedPosition.chartX-a.chartX,n=t.fixedPosition.chartY-a.chartY,l=o.graphLayoutsLookup;(Math.abs(r)>5||Math.abs(n)>5)&&(i=t.fixedPosition.plotX-r,s=t.fixedPosition.plotY-n,o.isInsidePlot(i,s)&&(t.plotX=i,t.plotY=s,t.hasDragged=!0,this.redrawHalo(t),l.forEach(t=>{t.restartSimulation()})))}},onMouseUp:function(t){t.fixedPosition&&(t.hasDragged&&(this.layout.enableSimulation?this.layout.start():this.chart.redraw()),t.inDragMode=t.hasDragged=!1,this.options.fixedDraggable||delete t.fixedPosition)},redrawHalo:function(t){t&&this.halo&&this.halo.attr({d:t.haloPath(this.options.states.hover.halo.size)})}},{setAnimation:e$}=y(),{composed:eQ}=y(),{addEvent:eJ,pushUnique:e0}=y();function e1(){this.graphLayoutsLookup&&(this.graphLayoutsLookup.forEach(t=>{t.updateSimulation()}),this.redraw())}function e2(){this.graphLayoutsLookup&&(this.graphLayoutsLookup.forEach(t=>{t.updateSimulation(!1)}),this.redraw())}function e3(){this.graphLayoutsLookup&&this.graphLayoutsLookup.forEach(t=>{t.stop()})}function e5(){let t,e=!1,i=i=>{i.maxIterations--&&isFinite(i.temperature)&&!i.isStable()&&!i.enableSimulation&&(i.beforeStep&&i.beforeStep(),i.step(),t=!1,e=!0)};if(this.graphLayoutsLookup&&!this.pointer?.hasDragged){for(e$(!1,this),this.graphLayoutsLookup.forEach(t=>t.start());!t;)t=!0,this.graphLayoutsLookup.forEach(i);e&&this.series.forEach(t=>{t&&t.layout&&t.render()})}}let e8={compose:function(t){e0(eQ,"GraphLayout")&&(eJ(t,"afterPrint",e1),eJ(t,"beforePrint",e2),eJ(t,"predraw",e3),eJ(t,"render",e5))},integrations:{},layouts:{}};var e6=f(960),e4=f.n(e6);let{seriesTypes:{bubble:{prototype:{pointClass:e9}}}}=P(),e7=class extends e9{destroy(){return this.series?.layout&&this.series.layout.removeElementFromCollection(this,this.series.layout.nodes),tZ().prototype.destroy.apply(this,arguments)}firePointEvent(){let t=this.series.options;if(this.isParentNode&&t.parentNode){let e=t.allowPointSelect;t.allowPointSelect=t.parentNode.allowPointSelect,tZ().prototype.firePointEvent.apply(this,arguments),t.allowPointSelect=e}else tZ().prototype.firePointEvent.apply(this,arguments)}select(){let t=this.series.chart;this.isParentNode?(t.getSelectedPoints=t.getSelectedParentNodes,tZ().prototype.select.apply(this,arguments),t.getSelectedPoints=e4().prototype.getSelectedPoints):tZ().prototype.select.apply(this,arguments)}setState(t,e){this?.graphic?.parentGroup?.element&&super.setState(t,e)}},{isNumber:it}=y(),ie={attractive:function(t,e,i){let s=t.getMass(),o=-i.x*e*this.diffTemperature,a=-i.y*e*this.diffTemperature;t.fromNode.fixedPosition||(t.fromNode.plotX-=o*s.fromNode/t.fromNode.degree,t.fromNode.plotY-=a*s.fromNode/t.fromNode.degree),t.toNode.fixedPosition||(t.toNode.plotX+=o*s.toNode/t.toNode.degree,t.toNode.plotY+=a*s.toNode/t.toNode.degree)},attractiveForceFunction:function(t,e){return(e-t)/t},barycenter:function(){let t=this.options.gravitationalConstant||0,e=(this.barycenter.xFactor-(this.box.left+this.box.width)/2)*t,i=(this.barycenter.yFactor-(this.box.top+this.box.height)/2)*t;this.nodes.forEach(function(t){t.fixedPosition||(t.plotX-=e/t.mass/t.degree,t.plotY-=i/t.mass/t.degree)})},getK:function(t){return Math.pow(t.box.width*t.box.height/t.nodes.length,.5)},integrate:function(t,e){let i=-t.options.friction,s=t.options.maxSpeed,o=e.prevX,a=e.prevY,r=(e.plotX+e.dispX-o)*i,n=(e.plotY+e.dispY-a)*i,l=Math.abs,h=l(r)/(r||1),p=l(n)/(n||1),d=h*Math.min(s,Math.abs(r)),c=p*Math.min(s,Math.abs(n));e.prevX=e.plotX+e.dispX,e.prevY=e.plotY+e.dispY,e.plotX+=d,e.plotY+=c,e.temperature=t.vectorLength({x:d,y:c})},repulsive:function(t,e,i){let s=e*this.diffTemperature/t.mass/t.degree;t.fixedPosition||(t.plotX+=i.x*s,t.plotY+=i.y*s)},repulsiveForceFunction:function(t,e){return(e-t)/t*(e>t)}},{noop:ii}=y(),is={barycenter:function(){let t,e,i=this.options.gravitationalConstant||0,s=this.box,o=this.nodes,a=Math.sqrt(o.length);for(let r of o)if(!r.fixedPosition){let o=r.mass*a,n=r.plotX||0,l=r.plotY||0,h=r.series,p=h.parentNode;this.resolveSplitSeries(r)&&p&&!r.isParentNode?(t=p.plotX||0,e=p.plotY||0):(t=s.width/2,e=s.height/2),r.plotX=n-(n-t)*i/o,r.plotY=l-(l-e)*i/o,h.chart.hoverPoint===r&&h.redrawHalo&&h.halo&&h.redrawHalo(r)}},getK:ii,integrate:ie.integrate,repulsive:function(t,e,i,s){let o=e*this.diffTemperature/t.mass/t.degree,a=i.x*o,r=i.y*o;t.fixedPosition||(t.plotX+=a,t.plotY+=r),s.fixedPosition||(s.plotX-=a,s.plotY-=r)},repulsiveForceFunction:function(t,e,i,s){return Math.min(t,(i.marker.radius+s.marker.radius)/2)}},io={attractive:function(t,e,i,s){let o=t.getMass(),a=i.x/s*e,r=i.y/s*e;t.fromNode.fixedPosition||(t.fromNode.dispX-=a*o.fromNode/t.fromNode.degree,t.fromNode.dispY-=r*o.fromNode/t.fromNode.degree),t.toNode.fixedPosition||(t.toNode.dispX+=a*o.toNode/t.toNode.degree,t.toNode.dispY+=r*o.toNode/t.toNode.degree)},attractiveForceFunction:function(t,e){return t*t/e},barycenter:function(){let t=this.options.gravitationalConstant,e=this.barycenter.xFactor,i=this.barycenter.yFactor;this.nodes.forEach(function(s){if(!s.fixedPosition){let o=s.getDegree(),a=o*(1+o/2);s.dispX+=(e-s.plotX)*t*a/s.degree,s.dispY+=(i-s.plotY)*t*a/s.degree}})},getK:function(t){return Math.pow(t.box.width*t.box.height/t.nodes.length,.3)},integrate:function(t,e){e.dispX+=e.dispX*t.options.friction,e.dispY+=e.dispY*t.options.friction;let i=e.temperature=t.vectorLength({x:e.dispX,y:e.dispY});0!==i&&(e.plotX+=e.dispX/i*Math.min(Math.abs(e.dispX),t.temperature),e.plotY+=e.dispY/i*Math.min(Math.abs(e.dispY),t.temperature))},repulsive:function(t,e,i,s){t.dispX+=i.x/s*e/t.degree,t.dispY+=i.y/s*e/t.degree},repulsiveForceFunction:function(t,e){return e*e/t}};class ia{constructor(t){this.body=!1,this.isEmpty=!1,this.isInternal=!1,this.nodes=[],this.box=t,this.boxSize=Math.min(t.width,t.height)}divideBox(){let t=this.box.width/2,e=this.box.height/2;this.nodes[0]=new ia({left:this.box.left,top:this.box.top,width:t,height:e}),this.nodes[1]=new ia({left:this.box.left+t,top:this.box.top,width:t,height:e}),this.nodes[2]=new ia({left:this.box.left+t,top:this.box.top+e,width:t,height:e}),this.nodes[3]=new ia({left:this.box.left,top:this.box.top+e,width:t,height:e})}getBoxPosition(t){let e=t.plotX<this.box.left+this.box.width/2,i=t.plotY<this.box.top+this.box.height/2;return e?3*!i:i?1:2}insert(t,e){let i;this.isInternal?this.nodes[this.getBoxPosition(t)].insert(t,e-1):(this.isEmpty=!1,this.body?e?(this.isInternal=!0,this.divideBox(),!0!==this.body&&(this.nodes[this.getBoxPosition(this.body)].insert(this.body,e-1),this.body=!0),this.nodes[this.getBoxPosition(t)].insert(t,e-1)):((i=new ia({top:t.plotX||NaN,left:t.plotY||NaN,width:.1,height:.1})).body=t,i.isInternal=!1,this.nodes.push(i)):(this.isInternal=!1,this.body=t))}updateMassAndCenter(){let t=0,e=0,i=0;if(this.isInternal){for(let s of this.nodes)s.isEmpty||(t+=s.mass,e+=s.plotX*s.mass,i+=s.plotY*s.mass);e/=t,i/=t}else this.body&&(t=this.body.mass,e=this.body.plotX,i=this.body.plotY);this.mass=t,this.plotX=e,this.plotY=i}}let ir=class{constructor(t,e,i,s){this.box={left:t,top:e,width:i,height:s},this.maxDepth=25,this.root=new ia(this.box),this.root.isInternal=!0,this.root.isRoot=!0,this.root.divideBox()}calculateMassAndCenter(){this.visitNodeRecursive(null,null,function(t){t.updateMassAndCenter()})}insertNodes(t){for(let e of t)this.root.insert(e,this.maxDepth)}visitNodeRecursive(t,e,i){let s;if(t||(t=this.root),t===this.root&&e&&(s=e(t)),!1!==s){for(let o of t.nodes){if(o.isInternal){if(e&&(s=e(o)),!1===s)continue;this.visitNodeRecursive(o,e,i)}else o.body&&e&&e(o.body);i&&i(o)}t===this.root&&i&&i(t)}}},{win:il}=y(),{clamp:ih,defined:ip,isFunction:id,fireEvent:ic,pick:iu}=y();class ig{constructor(){this.box={},this.currentStep=0,this.initialRendering=!0,this.links=[],this.nodes=[],this.series=[],this.simulation=!1}static compose(t){e8.compose(t),e8.integrations.euler=io,e8.integrations.verlet=ie,e8.layouts["reingold-fruchterman"]=ig}init(t){this.options=t,this.nodes=[],this.links=[],this.series=[],this.box={x:0,y:0,width:0,height:0},this.setInitialRendering(!0),this.integration=e8.integrations[t.integration],this.enableSimulation=t.enableSimulation,this.attractiveForce=iu(t.attractiveForce,this.integration.attractiveForceFunction),this.repulsiveForce=iu(t.repulsiveForce,this.integration.repulsiveForceFunction),this.approximation=t.approximation}updateSimulation(t){this.enableSimulation=iu(t,this.options.enableSimulation)}start(){let t=this.series,e=this.options;this.currentStep=0,this.forces=t[0]&&t[0].forces||[],this.chart=t[0]&&t[0].chart,this.initialRendering&&(this.initPositions(),t.forEach(function(t){t.finishedAnimating=!0,t.render()})),this.setK(),this.resetSimulation(e),this.enableSimulation&&this.step()}step(){let t=this.series;for(let t of(this.currentStep++,"barnes-hut"===this.approximation&&(this.createQuadTree(),this.quadTree.calculateMassAndCenter()),this.forces||[]))this[t+"Forces"](this.temperature);if(this.applyLimits(),this.temperature=this.coolDown(this.startTemperature,this.diffTemperature,this.currentStep),this.prevSystemTemperature=this.systemTemperature,this.systemTemperature=this.getSystemTemperature(),this.enableSimulation){for(let e of t)e.chart&&e.render();this.maxIterations--&&isFinite(this.temperature)&&!this.isStable()?(this.simulation&&il.cancelAnimationFrame(this.simulation),this.simulation=il.requestAnimationFrame(()=>this.step())):(this.simulation=!1,this.series.forEach(t=>{ic(t,"afterSimulation")}))}}stop(){this.simulation&&il.cancelAnimationFrame(this.simulation)}setArea(t,e,i,s){this.box={left:t,top:e,width:i,height:s}}setK(){this.k=this.options.linkLength||this.integration.getK(this)}addElementsToCollection(t,e){for(let i of t)-1===e.indexOf(i)&&e.push(i)}removeElementFromCollection(t,e){let i=e.indexOf(t);-1!==i&&e.splice(i,1)}clear(){this.nodes.length=0,this.links.length=0,this.series.length=0,this.resetSimulation()}resetSimulation(){this.forcedStop=!1,this.systemTemperature=0,this.setMaxIterations(),this.setTemperature(),this.setDiffTemperature()}restartSimulation(){this.simulation?this.resetSimulation():(this.setInitialRendering(!1),this.enableSimulation?this.start():this.setMaxIterations(1),this.chart&&this.chart.redraw(),this.setInitialRendering(!0))}setMaxIterations(t){this.maxIterations=iu(t,this.options.maxIterations)}setTemperature(){this.temperature=this.startTemperature=Math.sqrt(this.nodes.length)}setDiffTemperature(){this.diffTemperature=this.startTemperature/(this.options.maxIterations+1)}setInitialRendering(t){this.initialRendering=t}createQuadTree(){this.quadTree=new ir(this.box.left,this.box.top,this.box.width,this.box.height),this.quadTree.insertNodes(this.nodes)}initPositions(){let t=this.options.initialPositions;if(id(t))for(let e of(t.call(this),this.nodes))ip(e.prevX)||(e.prevX=e.plotX),ip(e.prevY)||(e.prevY=e.plotY),e.dispX=0,e.dispY=0;else"circle"===t?this.setCircularPositions():this.setRandomPositions()}setCircularPositions(){let t,e=this.box,i=this.nodes,s=2*Math.PI/(i.length+1),o=i.filter(function(t){return 0===t.linksTo.length}),a={},r=this.options.initialPositionRadius,n=t=>{for(let e of t.linksFrom||[])a[e.toNode.id]||(a[e.toNode.id]=!0,l.push(e.toNode),n(e.toNode))},l=[];for(let t of o)l.push(t),n(t);if(l.length)for(let t of i)-1===l.indexOf(t)&&l.push(t);else l=i;for(let i=0,o=l.length;i<o;++i)(t=l[i]).plotX=t.prevX=iu(t.plotX,e.width/2+r*Math.cos(i*s)),t.plotY=t.prevY=iu(t.plotY,e.height/2+r*Math.sin(i*s)),t.dispX=0,t.dispY=0}setRandomPositions(){let t,e=this.box,i=this.nodes,s=i.length+1,o=t=>{let e=t*t/Math.PI;return e-Math.floor(e)};for(let a=0,r=i.length;a<r;++a)(t=i[a]).plotX=t.prevX=iu(t.plotX,e.width*o(a)),t.plotY=t.prevY=iu(t.plotY,e.height*o(s+a)),t.dispX=0,t.dispY=0}force(t,...e){this.integration[t].apply(this,e)}barycenterForces(){this.getBarycenter(),this.force("barycenter")}getBarycenter(){let t=0,e=0,i=0;for(let s of this.nodes)e+=s.plotX*s.mass,i+=s.plotY*s.mass,t+=s.mass;return this.barycenter={x:e,y:i,xFactor:e/t,yFactor:i/t},this.barycenter}barnesHutApproximation(t,e){let i,s,o=this.getDistXY(t,e),a=this.vectorLength(o);return t!==e&&0!==a&&(e.isInternal?e.boxSize/a<this.options.theta&&0!==a?(s=this.repulsiveForce(a,this.k),this.force("repulsive",t,s*e.mass,o,a),i=!1):i=!0:(s=this.repulsiveForce(a,this.k),this.force("repulsive",t,s*e.mass,o,a))),i}repulsiveForces(){if("barnes-hut"===this.approximation)for(let t of this.nodes)this.quadTree.visitNodeRecursive(null,e=>this.barnesHutApproximation(t,e));else{let t,e,i;for(let s of this.nodes)for(let o of this.nodes)s===o||s.fixedPosition||(i=this.getDistXY(s,o),0!==(e=this.vectorLength(i))&&(t=this.repulsiveForce(e,this.k),this.force("repulsive",s,t*o.mass,i,e)))}}attractiveForces(){let t,e,i;for(let s of this.links)s.fromNode&&s.toNode&&(t=this.getDistXY(s.fromNode,s.toNode),0!==(e=this.vectorLength(t))&&(i=this.attractiveForce(e,this.k),this.force("attractive",s,i,t,e)))}applyLimits(){for(let t of this.nodes)t.fixedPosition||(this.integration.integrate(this,t),this.applyLimitBox(t,this.box),t.dispX=0,t.dispY=0)}applyLimitBox(t,e){let i=t.radius;t.plotX=ih(t.plotX,e.left+i,e.width-i),t.plotY=ih(t.plotY,e.top+i,e.height-i)}coolDown(t,e,i){return t-e*i}isStable(){return 1e-5>Math.abs(this.systemTemperature-this.prevSystemTemperature)||this.temperature<=0}getSystemTemperature(){let t=0;for(let e of this.nodes)t+=e.temperature;return t}vectorLength(t){return Math.sqrt(t.x*t.x+t.y*t.y)}getDistR(t,e){let i=this.getDistXY(t,e);return this.vectorLength(i)}getDistXY(t,e){let i=t.plotX-e.plotX,s=t.plotY-e.plotY;return{x:i,y:s,absX:Math.abs(i),absY:Math.abs(s)}}}let im=ig,{addEvent:ib,defined:iy,pick:ix}=y();function iP(){let t=this.series,e=[];return t.forEach(t=>{t.parentNode&&t.parentNode.selected&&e.push(t.parentNode)}),e}function iv(){this.allDataPoints&&delete this.allDataPoints}class iM extends im{constructor(){super(...arguments),this.index=NaN,this.nodes=[],this.series=[]}static compose(t){im.compose(t),e8.integrations.packedbubble=is,e8.layouts.packedbubble=iM;let e=t.prototype;e.getSelectedParentNodes||(ib(t,"beforeRedraw",iv),e.getSelectedParentNodes=iP),e.allParentNodes||(e.allParentNodes=[])}beforeStep(){this.options.marker&&this.series.forEach(t=>{t&&t.calculateParentRadius()})}isStable(){let t=Math.abs(this.prevSystemTemperature-this.systemTemperature);return 1>Math.abs(10*this.systemTemperature/Math.sqrt(this.nodes.length))&&t<1e-5||this.temperature<=0}setCircularPositions(){let t=this.box,e=[...this.nodes,...this?.chart?.allParentNodes||[]],i=2*Math.PI/(e.length+1),s=this.options.initialPositionRadius,o,a,r=0;for(let n of e)this.resolveSplitSeries(n)&&!n.isParentNode?(o=n.series.parentNode.plotX,a=n.series.parentNode.plotY):(o=t.width/2,a=t.height/2),n.plotX=n.prevX=ix(n.plotX,o+s*Math.cos(n.index||r*i)),n.plotY=n.prevY=ix(n.plotY,a+s*Math.sin(n.index||r*i)),n.dispX=0,n.dispY=0,r++}repulsiveForces(){let{options:t,k:e}=this,{bubblePadding:i=0,seriesInteraction:s}=t,o=[...this.nodes,...this?.chart?.allParentNodes||[]];for(let t of o){let a=t.series,r=t.fixedPosition,n=(t.marker?.radius||0)+i;for(let i of(t.degree=t.mass,t.neighbours=0,o)){let o=i.series;if(t!==i&&!r&&(s||a===o)&&!(a===o&&(i.isParentNode||t.isParentNode))){let s,o=this.getDistXY(t,i),a=this.vectorLength(o)-(n+(i.marker?.radius||0));a<0&&(t.degree+=.01,s=this.repulsiveForce(-a/Math.sqrt(++t.neighbours),e,t,i)*i.mass),this.force("repulsive",t,s||0,o,i,a)}}}}resolveSplitSeries(t){let e=t.series?.options?.layoutAlgorithm?.splitSeries;return!iy(e)&&t.series.chart?.options?.plotOptions?.packedbubble?.layoutAlgorithm?.splitSeries||e||!1}applyLimitBox(t,e){let i,s;this.resolveSplitSeries(t)&&!t.isParentNode&&this.options.parentNodeLimit&&(i=this.getDistXY(t,t.series.parentNode),(s=t.series.parentNodeRadius-t.marker.radius-this.vectorLength(i))<0&&s>-2*t.marker.radius&&(t.plotX-=.01*i.x,t.plotY-=.01*i.y)),super.applyLimitBox(t,e)}}e8.layouts.packedbubble=iM;let{merge:iL,syncTimeout:ik}=y(),{animObject:iw}=y();var iS=f(28),iA=f.n(iS);let{deg2rad:iN}=y(),{addEvent:iT,merge:iC,uniqueKey:iX,defined:iY,extend:iI}=y();function iR(t,e){e=iC(!0,{enabled:!0,attributes:{dy:-5,startOffset:"50%",textAnchor:"middle"}},e);let i=this.renderer.url,s=this.text||this,o=s.textPath,{attributes:a,enabled:r}=e;if(t=t||o&&o.path,o&&o.undo(),t&&r){let e=iT(s,"afterModifyTree",e=>{if(t&&r){let o=t.attr("id");o||t.attr("id",o=iX());let r={x:0,y:0};iY(a.dx)&&(r.dx=a.dx,delete a.dx),iY(a.dy)&&(r.dy=a.dy,delete a.dy),s.attr(r),this.attr({transform:""}),this.box&&(this.box=this.box.destroy());let n=e.nodes.slice(0);e.nodes.length=0,e.nodes[0]={tagName:"textPath",attributes:iI(a,{"text-anchor":a.textAnchor,href:`${i}#${o}`}),children:n}}});s.textPath={path:t,undo:e}}else s.attr({dx:0,dy:0}),delete s.textPath;return this.added&&(s.textCache="",this.renderer.buildText(s)),this}function iE(t){let e=t.bBox,i=this.element?.querySelector("textPath");if(i){let t=[],{b:s,h:o}=this.renderer.fontMetrics(this.element),a=o-s,r=RegExp('(<tspan>|<tspan(?!\\sclass="highcharts-br")[^>]*>|<\\/tspan>)',"g"),n=i.innerHTML.replace(r,"").split(/<tspan class="highcharts-br"[^>]*>/),l=n.length,h=(t,e)=>{let{x:o,y:r}=e,n=(i.getRotationOfChar(t)-90)*iN,l=Math.cos(n),h=Math.sin(n);return[[o-a*l,r-a*h],[o+s*l,r+s*h]]};for(let e=0,s=0;s<l;s++){let o=n[s].length;for(let a=0;a<o;a+=5)try{let o=e+a+s,[r,n]=h(o,i.getStartPositionOfChar(o));0===a?(t.push(n),t.push(r)):(0===s&&t.unshift(n),s===l-1&&t.push(r))}catch(t){break}e+=o-1;try{let o=e+s,a=i.getEndPositionOfChar(o),[r,n]=h(o,a);t.unshift(n),t.unshift(r)}catch(t){break}}t.length&&t.push(t[0].slice()),e.polygon=t}return e}function iD(t){let e=t.labelOptions,i=t.point,s=e[i.formatPrefix+"TextPath"]||e.textPath;s&&!e.useHTML&&(this.setTextPath(i.getDataLabelPath?.(this)||i.graphic,s),i.dataLabelPath&&!s.enabled&&(i.dataLabelPath=i.dataLabelPath.destroy()))}let{parse:iz}=eV(),{noop:iO}=y(),{series:{prototype:iH},seriesTypes:{bubble:iB}}=P(),{initDataLabels:iW,initDataLabelsDefer:iF}={initDataLabels:function(){let t=this.options.dataLabels;if(!this.dataLabelsGroup){let e=this.initDataLabelsGroup();return!this.chart.styledMode&&t?.style&&e.css(t.style),e.attr({opacity:0}),this.visible&&e.show(),e}return this.dataLabelsGroup.attr(iL({opacity:1},this.getPlotBox("data-labels"))),this.dataLabelsGroup},initDataLabelsDefer:function(){let t=this.options.dataLabels;t?.defer&&this.options.layoutAlgorithm?.enableSimulation?ik(()=>{this.deferDataLabels=!1},t?iw(t.animation).defer:0):this.deferDataLabels=!1}},{addEvent:iG,clamp:iq,defined:iV,extend:i_,fireEvent:iU,isArray:iK,isNumber:iZ,merge:ij,pick:i$}=y();({compose:function(t){iT(t,"afterGetBBox",iE),iT(t,"beforeAddingDataLabel",iD);let e=t.prototype;e.setTextPath||(e.setTextPath=iR)}}).compose(iA());class iQ extends iB{constructor(){super(...arguments),this.parentNodeMass=0,this.deferDataLabels=!0}static compose(t,e,i){iB.compose(t,e,i),ej.compose(e),iM.compose(e)}accumulateAllPoints(){let t=this.chart,e=[];for(let i of t.series)if(i.is("packedbubble")&&i.reserveSpace()){let t=i.getColumn("value");for(let s=0;s<t.length;s++)e.push([null,null,t[s],i.index,s,{id:s,marker:{radius:0}}])}return e}addLayout(){let t=this.options.layoutAlgorithm=this.options.layoutAlgorithm||{},e=t.type||"packedbubble",i=this.chart.options.chart,s=this.chart.graphLayoutsStorage,o=this.chart.graphLayoutsLookup,a;s||(this.chart.graphLayoutsStorage=s={},this.chart.graphLayoutsLookup=o=[]),(a=s[e])||(t.enableSimulation=iV(i.forExport)?!i.forExport:t.enableSimulation,s[e]=a=new e8.layouts[e],a.init(t),o.splice(a.index,0,a)),this.layout=a,this.points.forEach(t=>{t.mass=2,t.degree=1,t.collisionNmb=1}),a.setArea(0,0,this.chart.plotWidth,this.chart.plotHeight),a.addElementsToCollection([this],a.series),a.addElementsToCollection(this.points,a.nodes)}addSeriesLayout(){let t=this.options.layoutAlgorithm=this.options.layoutAlgorithm||{},e=t.type||"packedbubble",i=this.chart.graphLayoutsStorage,s=this.chart.graphLayoutsLookup,o=ij(t,t.parentNodeOptions,{enableSimulation:this.layout.options.enableSimulation}),a=i[e+"-series"];a||(i[e+"-series"]=a=new e8.layouts[e],a.init(o),s.splice(a.index,0,a)),this.parentNodeLayout=a,this.createParentNodes()}calculateParentRadius(){let t=this.seriesBox();this.parentNodeRadius=iq(Math.sqrt(2*this.parentNodeMass/Math.PI)+20,20,t?Math.max(Math.sqrt(Math.pow(t.width,2)+Math.pow(t.height,2))/2+20,20):Math.sqrt(2*this.parentNodeMass/Math.PI)+20),this.parentNode&&(this.parentNode.marker.radius=this.parentNode.radius=this.parentNodeRadius)}calculateZExtremes(){let t=this.chart.series,e=this.options.zMin,i=this.options.zMax,s=1/0,o=-1/0;return e&&i?[e,i]:(t.forEach(t=>{t.getColumn("value").forEach(t=>{iV(t)&&(t>o&&(o=t),t<s&&(s=t))})}),[e=i$(e,s),i=i$(i,o)])}checkOverlap(t,e){let i=t[0]-e[0],s=t[1]-e[1];return Math.sqrt(i*i+s*s)-Math.abs(t[2]+e[2])<-.001}createParentNodes(){let t=this.pointClass,e=this.chart,i=this.parentNodeLayout,s=this.layout.options,o,a=this.parentNode,r={radius:this.parentNodeRadius,lineColor:this.color,fillColor:iz(this.color).brighten(.4).get()};s.parentNodeOptions&&(r=ij(s.parentNodeOptions.marker||{},r)),this.parentNodeMass=0,this.points.forEach(t=>{this.parentNodeMass+=Math.PI*Math.pow(t.marker.radius,2)}),this.calculateParentRadius(),i.nodes.forEach(t=>{t.seriesIndex===this.index&&(o=!0)}),i.setArea(0,0,e.plotWidth,e.plotHeight),o||(a||(a=new t(this,{mass:this.parentNodeRadius/2,marker:r,dataLabels:{inside:!1},states:{normal:{marker:r},hover:{marker:r}},dataLabelOnNull:!0,degree:this.parentNodeRadius,isParentNode:!0,seriesIndex:this.index}),this.chart.allParentNodes.push(a)),this.parentNode&&(a.plotX=this.parentNode.plotX,a.plotY=this.parentNode.plotY),this.parentNode=a,i.addElementsToCollection([this],i.series),i.addElementsToCollection([a],i.nodes))}deferLayout(){let t=this.options.layoutAlgorithm;this.visible&&(this.addLayout(),t.splitSeries&&this.addSeriesLayout())}destroy(){this.chart.graphLayoutsLookup&&this.chart.graphLayoutsLookup.forEach(t=>{t.removeElementFromCollection(this,t.series)},this),this.parentNode&&this.parentNodeLayout&&(this.parentNodeLayout.removeElementFromCollection(this.parentNode,this.parentNodeLayout.nodes),this.parentNode.dataLabel&&(this.parentNode.dataLabel=this.parentNode.dataLabel.destroy())),iH.destroy.apply(this,arguments)}drawDataLabels(){!this.deferDataLabels&&(iH.drawDataLabels.call(this,this.points),this.parentNode&&(this.parentNode.formatPrefix="parentNode",iH.drawDataLabels.call(this,[this.parentNode])))}drawGraph(){if(!this.layout||!this.layout.options.splitSeries)return;let t=this.chart,e=this.layout.options.parentNodeOptions.marker,i={fill:e.fillColor||iz(this.color).brighten(.4).get(),opacity:e.fillOpacity,stroke:e.lineColor||this.color,"stroke-width":i$(e.lineWidth,this.options.lineWidth)},s={};this.parentNodesGroup=this.plotGroup("parentNodesGroup","parentNode",this.visible?"inherit":"hidden",.1,t.seriesGroup),this.group?.attr({zIndex:2}),this.calculateParentRadius(),this.parentNode&&iV(this.parentNode.plotX)&&iV(this.parentNode.plotY)&&iV(this.parentNodeRadius)&&(s=ij({x:this.parentNode.plotX-this.parentNodeRadius,y:this.parentNode.plotY-this.parentNodeRadius,width:2*this.parentNodeRadius,height:2*this.parentNodeRadius},i),this.parentNode.graphic||(this.graph=this.parentNode.graphic=t.renderer.symbol(i.symbol).add(this.parentNodesGroup)),this.parentNode.graphic.attr(s))}drawTracker(){let t,e=this.parentNode;super.drawTracker(),e&&(t=iK(e.dataLabels)?e.dataLabels:e.dataLabel?[e.dataLabel]:[],e.graphic&&(e.graphic.element.point=e),t.forEach(t=>{(t.div||t.element).point=e}))}getPointRadius(){let t,e,i,s,o=this.chart,a=o.plotWidth,r=o.plotHeight,n=this.options,l=n.useSimulation,h=Math.min(a,r),p={},d=[],c=o.allDataPoints||[],u=c.length;["minSize","maxSize"].forEach(t=>{let e=parseInt(n[t],10),i=/%$/.test(n[t]);p[t]=i?h*e/100:e*Math.sqrt(u)}),o.minRadius=t=p.minSize/Math.sqrt(u),o.maxRadius=e=p.maxSize/Math.sqrt(u);let g=l?this.calculateZExtremes():[t,e];c.forEach((o,a)=>{i=l?iq(o[2],g[0],g[1]):o[2],0===(s=this.getRadius(g[0],g[1],t,e,i))&&(s=null),c[a][2]=s,d.push(s)}),this.radii=d}init(){return iH.init.apply(this,arguments),iF.call(this),this.eventsToUnbind.push(iG(this,"updatedData",function(){this.chart.series.forEach(t=>{t.type===this.type&&(t.isDirty=!0)},this)})),this}onMouseUp(t){if(t.fixedPosition&&!t.removed){let e,i=this.layout,s=this.parentNodeLayout;!t.isParentNode&&s&&i.options.dragBetweenSeries&&s.nodes.forEach(s=>{t&&t.marker&&s!==t.series.parentNode&&(e=i.getDistXY(t,s),i.vectorLength(e)-s.marker.radius-t.marker.radius<0&&(s.series.addPoint(ij(t.options,{plotX:t.plotX,plotY:t.plotY}),!1),i.removeElementFromCollection(t,i.nodes),t.remove()))}),ej.onMouseUp.apply(this,arguments)}}placeBubbles(t){let e=this.checkOverlap,i=this.positionBubble,s=[],o=1,a=0,r=0,n,l=[],h,p=t.sort((t,e)=>e[2]-t[2]);if(p.length){if(s.push([[0,0,p[0][2],p[0][3],p[0][4]]]),p.length>1)for(s.push([[0,0-p[1][2]-p[0][2],p[1][2],p[1][3],p[1][4]]]),h=2;h<p.length;h++)p[h][2]=p[h][2]||1,e(n=i(s[o][a],s[o-1][r],p[h]),s[o][0])?(s.push([]),r=0,s[o+1].push(i(s[o][a],s[o][0],p[h])),o++,a=0):o>1&&s[o-1][r+1]&&e(n,s[o-1][r+1])?(r++,s[o].push(i(s[o][a],s[o-1][r],p[h])),a++):(a++,s[o].push(n));this.chart.stages=s,this.chart.rawPositions=[].concat.apply([],s),this.resizeRadius(),l=this.chart.rawPositions}return l}pointAttribs(t,e){let i=this.options,s=t&&t.isParentNode,o=i.marker;s&&i.layoutAlgorithm&&i.layoutAlgorithm.parentNodeOptions&&(o=i.layoutAlgorithm.parentNodeOptions.marker);let a=o.fillOpacity,r=iH.pointAttribs.call(this,t,e);return 1!==a&&(r["fill-opacity"]=a),r}positionBubble(t,e,i){let s=Math.asin,o=Math.acos,a=Math.pow,r=Math.abs,n=(0,Math.sqrt)(a(t[0]-e[0],2)+a(t[1]-e[1],2)),l=o((a(n,2)+a(i[2]+e[2],2)-a(i[2]+t[2],2))/(2*(i[2]+e[2])*n)),h=s(r(t[0]-e[0])/n),p=(t[1]-e[1]<0?0:Math.PI)+l+h*((t[0]-e[0])*(t[1]-e[1])<0?1:-1),d=Math.cos(p),c=Math.sin(p);return[e[0]+(e[2]+i[2])*c,e[1]-(e[2]+i[2])*d,i[2],i[3],i[4]]}render(){let t=[];iH.render.apply(this,arguments),!this.options.dataLabels.allowOverlap&&(this.data.forEach(e=>{iK(e.dataLabels)&&e.dataLabels.forEach(e=>{t.push(e)})}),this.options.useSimulation&&this.chart.hideOverlappingLabels(t))}resizeRadius(){let t,e,i,s,o,a=this.chart,r=a.rawPositions,n=Math.min,l=Math.max,h=a.plotLeft,p=a.plotTop,d=a.plotHeight,c=a.plotWidth;for(let a of(t=i=Number.POSITIVE_INFINITY,e=s=Number.NEGATIVE_INFINITY,r))o=a[2],t=n(t,a[0]-o),e=l(e,a[0]+o),i=n(i,a[1]-o),s=l(s,a[1]+o);let u=[e-t,s-i],g=[(c-h)/u[0],(d-p)/u[1]],f=n.apply([],g);if(Math.abs(f-1)>1e-10){for(let t of r)t[2]*=f;this.placeBubbles(r)}else a.diffY=d/2+p-i-(s-i)/2,a.diffX=c/2+h-t-(e-t)/2}seriesBox(){let t,e=this.chart,i=this.data,s=Math.max,o=Math.min,a=[e.plotLeft,e.plotLeft+e.plotWidth,e.plotTop,e.plotTop+e.plotHeight];return i.forEach(e=>{iV(e.plotX)&&iV(e.plotY)&&e.marker.radius&&(t=e.marker.radius,a[0]=o(a[0],e.plotX-t),a[1]=s(a[1],e.plotX+t),a[2]=o(a[2],e.plotY-t),a[3]=s(a[3],e.plotY+t))}),iZ(a.width/a.height)?a:null}setVisible(){let t=this;iH.setVisible.apply(t,arguments),t.parentNodeLayout&&t.graph?t.visible?(t.graph.show(),t.parentNode.dataLabel&&t.parentNode.dataLabel.show()):(t.graph.hide(),t.parentNodeLayout.removeElementFromCollection(t.parentNode,t.parentNodeLayout.nodes),t.parentNode.dataLabel&&t.parentNode.dataLabel.hide()):t.layout&&(t.visible?t.layout.addElementsToCollection(t.points,t.layout.nodes):t.points.forEach(e=>{t.layout.removeElementFromCollection(e,t.layout.nodes)}))}translate(){let t,e,i,s=this.chart,o=this.data,a=this.index,r=this.options.useSimulation;for(let n of(this.generatePoints(),iV(s.allDataPoints)||(s.allDataPoints=this.accumulateAllPoints(),this.getPointRadius()),r?i=s.allDataPoints:(i=this.placeBubbles(s.allDataPoints),this.options.draggable=!1),i))n[3]===a&&(t=o[n[4]],e=i$(n[2],void 0),r||(t.plotX=n[0]-s.plotLeft+s.diffX,t.plotY=n[1]-s.plotTop+s.diffY),iZ(e)&&(t.marker=i_(t.marker,{radius:e,width:2*e,height:2*e}),t.radius=e));r&&this.deferLayout(),iU(this,"afterTranslate")}}iQ.defaultOptions=ij(iB.defaultOptions,{minSize:"10%",maxSize:"50%",sizeBy:"area",zoneAxis:"y",crisp:!1,tooltip:{pointFormat:"Value: {point.value}"},draggable:!0,useSimulation:!0,parentNode:{allowPointSelect:!1},dataLabels:{formatter:function(){let{numberFormatter:t}=this.series.chart,{value:e}=this.point;return it(e)?t(e,-1):""},parentNodeFormatter:function(){return this.name||""},parentNodeTextPath:{enabled:!0},padding:0,style:{transition:"opacity 2000ms"}},layoutAlgorithm:{initialPositions:"circle",initialPositionRadius:20,bubblePadding:5,parentNodeLimit:!1,seriesInteraction:!0,dragBetweenSeries:!1,parentNodeOptions:{maxIterations:400,gravitationalConstant:.03,maxSpeed:50,initialPositionRadius:100,seriesInteraction:!0,marker:{fillColor:null,fillOpacity:1,lineWidth:null,lineColor:null,symbol:"circle"}},enableSimulation:!0,type:"packedbubble",integration:"packedbubble",maxIterations:1e3,splitSeries:!1,maxSpeed:5,gravitationalConstant:.01,friction:-.981},stickyTracking:!1}),i_(iQ.prototype,{pointClass:e7,axisTypes:[],directTouch:!0,forces:["barycenter","repulsive"],hasDraggableNodes:!0,invertible:!1,isCartesian:!1,noSharedTooltip:!0,pointArrayMap:["value"],pointValKey:"value",requireSorting:!1,trackerGroups:["group","dataLabelsGroup","parentNodesGroup"],initDataLabels:iW,alignDataLabel:iH.alignDataLabel,indexateNodes:iO,onMouseDown:ej.onMouseDown,onMouseMove:ej.onMouseMove,redrawHalo:ej.redrawHalo,searchPoint:iO}),P().registerSeriesType("packedbubble",iQ);let{noop:iJ}=y(),{area:i0,line:i1,scatter:i2}=P().seriesTypes,{extend:i3,merge:i5}=y();class i8 extends i2{getGraphPath(){let t=i1.prototype.getGraphPath.call(this),e=t.length+1;for(;e--;)(e===t.length||"M"===t[e][0])&&e>0&&t.splice(e,0,["Z"]);return this.areaPath=t,t}drawGraph(){this.options.fillColor=this.color,i0.prototype.drawGraph.call(this)}}i8.defaultOptions=i5(i2.defaultOptions,{marker:{enabled:!1,states:{hover:{enabled:!1}}},stickyTracking:!1,tooltip:{followPointer:!0,pointFormat:""},trackByArea:!0,legendSymbol:"rectangle"}),i3(i8.prototype,{type:"polygon",drawTracker:i1.prototype.drawTracker,setStackedPoints:iJ}),P().registerSeriesType("polygon",i8);let i6={circular:{gridLineWidth:1,labels:{align:void 0,x:0,y:void 0},maxPadding:0,minPadding:0,showLastLabel:!1,tickLength:0},radial:{gridLineInterpolation:"circle",gridLineWidth:1,labels:{align:"right",padding:5,x:-3,y:-2},showLastLabel:!1,title:{x:4,text:null,rotation:90}},radialGauge:{endOnTick:!1,gridLineWidth:0,labels:{align:"center",distance:-25,x:0,y:void 0},lineWidth:1,minorGridLineWidth:0,minorTickInterval:"auto",minorTickLength:10,minorTickPosition:"inside",minorTickWidth:1,startOnTick:!1,tickLength:10,tickPixelInterval:100,tickPosition:"inside",tickWidth:2,title:{rotation:0,text:""},zIndex:2}},{defaultOptions:i4}=y(),{composed:i9,noop:i7}=y(),{addEvent:st,correctFloat:se,defined:si,extend:ss,fireEvent:so,isObject:sa,merge:sr,pick:sn,pushUnique:sl,relativeLength:sh,splat:sp,wrap:sd}=y();!function(t){function e(){this.autoConnect=this.isCircular&&void 0===sn(this.userMax,this.options.max)&&se(this.endAngleRad-this.startAngleRad)===se(2*Math.PI),!this.isCircular&&this.chart.inverted&&this.max++,this.autoConnect&&(this.max+=this.categories&&1||this.pointRange||this.closestPointRange||0)}function i(){return()=>{if(this.isRadial&&this.tickPositions&&this.options.labels&&!0!==this.options.labels.allowOverlap)return this.tickPositions.map(t=>this.ticks[t]?.label).filter(t=>!!t)}}function s(){return i7}function o(t,e,i){let s=this.pane.center,o=t.value,a,r,n;return this.isCircular?(si(o)?t.point&&(t.point.shapeArgs||{}).start&&(o=this.chart.inverted?this.translate(t.point.rectPlotY,!0):t.point.x):(r=t.chartX||0,n=t.chartY||0,o=this.translate(Math.atan2(n-i,r-e)-this.startAngleRad,!0)),r=(a=this.getPosition(o)).x,n=a.y):(si(o)||(r=t.chartX,n=t.chartY),si(r)&&si(n)&&(i=s[1]+this.chart.plotTop,o=this.translate(Math.min(Math.sqrt(Math.pow(r-e,2)+Math.pow(n-i,2)),s[2]/2)-s[3]/2,!0))),[o,r||0,n||0]}function a(t,e,i){let s=this.pane.center,o=this.chart,a=this.left||0,r=this.top||0,n,l=sn(e,s[2]/2-this.offset),h;return void 0===i&&(i=this.horiz?0:this.center&&-this.center[3]/2),i&&(l+=i),this.isCircular||void 0!==e?((h=this.chart.renderer.symbols.arc(a+s[0],r+s[1],l,l,{start:this.startAngleRad,end:this.endAngleRad,open:!0,innerR:0})).xBounds=[a+s[0]],h.yBounds=[r+s[1]-l]):(n=this.postTranslate(this.angleRad,l),h=[["M",this.center[0]+o.plotLeft,this.center[1]+o.plotTop],["L",n.x,n.y]]),h}function r(){this.constructor.prototype.getOffset.call(this),this.chart.axisOffset[this.side]=0}function n(t,e,i){let s=this.chart,o=t=>{if("string"==typeof t){let e=parseInt(t,10);return d.test(t)&&(e=e*n/100),e}return t},a=this.center,r=this.startAngleRad,n=a[2]/2,l=Math.min(this.offset,0),h=this.left||0,p=this.top||0,d=/%$/,c=this.isCircular,u,g,f,m,b,y,x=sn(o(i.outerRadius),n),P=o(i.innerRadius),v=sn(o(i.thickness),10);if("polygon"===this.options.gridLineInterpolation)y=this.getPlotLinePath({value:t}).concat(this.getPlotLinePath({value:e,reverse:!0}));else{t=Math.max(t,this.min),e=Math.min(e,this.max);let o=this.translate(t),n=this.translate(e);c||(x=o||0,P=n||0),"circle"!==i.shape&&c?(u=r+(o||0),g=r+(n||0)):(u=-Math.PI/2,g=1.5*Math.PI,b=!0),x-=l,v-=l,y=s.renderer.symbols.arc(h+a[0],p+a[1],x,x,{start:Math.min(u,g),end:Math.max(u,g),innerR:sn(P,x-v),open:b,borderRadius:i.borderRadius}),c&&(f=(g+u)/2,m=h+a[0]+a[2]/2*Math.cos(f),y.xBounds=f>-Math.PI/2&&f<Math.PI/2?[m,s.plotWidth]:[0,m],y.yBounds=[p+a[1]+a[2]/2*Math.sin(f)],y.yBounds[0]+=f>-Math.PI&&f<0||f>Math.PI?-10:10)}return y}function l(t){let e=this.pane.center,i=this.chart,s=i.inverted,o=t.reverse,a=this.pane.options.background,r=a?sp(a)[0]:{},n=r.innerRadius||"0%",l=r.outerRadius||"100%",h=e[0]+i.plotLeft,p=e[1]+i.plotTop,d=this.height,c=t.isCrosshair,u=e[3]/2,g=t.value,f,m,b,y,x,P,v,M,L,k=this.getPosition(g),w=k.x,S=k.y;if(c&&(g=(M=this.getCrosshairPosition(t,h,p))[0],w=M[1],S=M[2]),this.isCircular)m=Math.sqrt(Math.pow(w-h,2)+Math.pow(S-p,2)),b="string"==typeof n?sh(n,1):n/m,y="string"==typeof l?sh(l,1):l/m,e&&u&&(b<(f=u/m)&&(b=f),y<f&&(y=f)),L=[["M",h+b*(w-h),p-b*(p-S)],["L",w-(1-y)*(w-h),S+(1-y)*(p-S)]];else if((g=this.translate(g))&&(g<0||g>d)&&(g=0),"circle"===this.options.gridLineInterpolation)L=this.getLinePath(0,g,u);else if(L=[],i[s?"yAxis":"xAxis"].forEach(t=>{t.pane===this.pane&&(x=t)}),x){v=x.tickPositions,x.autoConnect&&(v=v.concat([v[0]])),o&&(v=v.slice().reverse()),g&&(g+=u);for(let t=0;t<v.length;t++)P=x.getPosition(v[t],g),L.push(t?["L",P.x,P.y]:["M",P.x,P.y])}return L}function h(t,e){let i=this.translate(t);return this.postTranslate(this.isCircular?i:this.angleRad,sn(this.isCircular?e:i<0?0:i,this.center[2]/2)-this.offset)}function p(){let t=this.center,e=this.chart,i=this.options.title;return{x:e.plotLeft+t[0]+(i.x||0),y:e.plotTop+t[1]-({high:.5,middle:.25,low:0})[i.align]*t[2]+(i.y||0)}}function d(t){t.beforeSetTickPositions=e,t.createLabelCollector=i,t.getCrosshairPosition=o,t.getLinePath=a,t.getOffset=r,t.getPlotBandPath=n,t.getPlotLinePath=l,t.getPosition=h,t.getTitlePosition=p,t.postTranslate=v,t.setAxisSize=L,t.setAxisTranslation=k,t.setOptions=w}function c(){let t=this.chart,e=this.options,i=t.angular&&this.isXAxis,s=this.pane,o=s?.options;if(!i&&s&&(t.angular||t.polar)){let t=2*Math.PI,i=(sn(o.startAngle,0)-90)*Math.PI/180,s=(sn(o.endAngle,sn(o.startAngle,0)+360)-90)*Math.PI/180;this.angleRad=(e.angle||0)*Math.PI/180,this.startAngleRad=i,this.endAngleRad=s,this.offset=e.offset||0;let a=(i%t+t)%t,r=(s%t+t)%t;a>Math.PI&&(a-=t),r>Math.PI&&(r-=t),this.normalizedStartAngleRad=a,this.normalizedEndAngleRad=r}}function u(t){this.isRadial&&(t.align=void 0,t.preventDefault())}function g(){if(this.chart?.labelCollectors){let t=this.labelCollector?this.chart.labelCollectors.indexOf(this.labelCollector):-1;t>=0&&this.chart.labelCollectors.splice(t,1)}}function f(t){let e,i=this.chart,o=i.angular,a=i.polar,r=this.isXAxis,n=this.coll,l=t.userOptions.pane||0,h=this.pane=i.pane&&i.pane[l];if("colorAxis"===n){this.isRadial=!1;return}if(o){if(o&&r)this.isHidden=!0,this.createLabelCollector=s,this.getOffset=i7,this.redraw=M,this.render=M,this.setScale=i7,this.setCategories=i7,this.setTitle=i7;else d(this);e=!r}else a&&(d(this),e=this.horiz);o||a?(this.isRadial=!0,this.labelCollector||(this.labelCollector=this.createLabelCollector()),this.labelCollector&&i.labelCollectors.push(this.labelCollector)):this.isRadial=!1,h&&e&&(h.axis=this),this.isCircular=e}function m(){this.isRadial&&this.beforeSetTickPositions()}function b(t){let e=this.label;if(!e)return;let i=this.axis,s=e.getBBox(),o=i.options.labels,a=(i.translate(this.pos)+i.startAngleRad+Math.PI/2)/Math.PI*180%360,r=Math.round(a),n=si(o.y)?0:-(.3*s.height),l=o.y,h,p=20,d=o.align,c="end",u=r<0?r+360:r,g=u,f=0,m=0;i.isRadial&&(h=i.getPosition(this.pos,i.center[2]/2+sh(sn(o.distance,-25),i.center[2]/2,-i.center[2]/2)),"auto"===o.rotation?e.attr({rotation:a}):si(l)||(l=i.chart.renderer.fontMetrics(e).b-s.height/2),si(d)||(i.isCircular?(s.width>i.len*i.tickInterval/(i.max-i.min)&&(p=0),d=a>p&&a<180-p?"left":a>180+p&&a<360-p?"right":"center"):d="center",e.attr({align:d})),"auto"===d&&2===i.tickPositions.length&&i.isCircular&&(u>90&&u<180?u=180-u:u>270&&u<=360&&(u=540-u),g>180&&g<=360&&(g=360-g),(i.pane.options.startAngle===r||i.pane.options.startAngle===r+360||i.pane.options.startAngle===r-360)&&(c="start"),d=r>=-90&&r<=90||r>=-360&&r<=-270||r>=270&&r<=360?"start"===c?"right":"left":"start"===c?"left":"right",g>70&&g<110&&(d="center"),u<15||u>=180&&u<195?f=.3*s.height:u>=15&&u<=35?f="start"===c?0:.75*s.height:u>=195&&u<=215?f="start"===c?.75*s.height:0:u>35&&u<=90?f="start"===c?-(.25*s.height):s.height:u>215&&u<=270&&(f="start"===c?s.height:-(.25*s.height)),g<15?m="start"===c?-(.15*s.height):.15*s.height:g>165&&g<=180&&(m="start"===c?.15*s.height:-(.15*s.height)),e.attr({align:d}),e.translate(m,f+n)),t.pos.x=h.x+(o.x||0),t.pos.y=h.y+(l||0))}function x(t){this.axis.getPosition&&ss(t.pos,this.axis.getPosition(this.pos))}function P({options:e}){e.xAxis&&sr(!0,t.radialDefaultOptions.circular,e.xAxis),e.yAxis&&sr(!0,t.radialDefaultOptions.radialGauge,e.yAxis)}function v(t,e){let i=this.chart,s=this.center;return t=this.startAngleRad+t,{x:i.plotLeft+s[0]+Math.cos(t)*e,y:i.plotTop+s[1]+Math.sin(t)*e}}function M(){this.isDirty=!1}function L(){let t,e;this.constructor.prototype.setAxisSize.call(this),this.isRadial&&(this.pane.updateCenter(this),t=this.center=this.pane.center.slice(),this.isCircular?this.sector=this.endAngleRad-this.startAngleRad:(e=this.postTranslate(this.angleRad,t[3]/2),t[0]=e.x-this.chart.plotLeft,t[1]=e.y-this.chart.plotTop),this.len=this.width=this.height=(t[2]-t[3])*sn(this.sector,1)/2)}function k(){this.constructor.prototype.setAxisTranslation.call(this),this.center&&(this.isCircular?this.transA=(this.endAngleRad-this.startAngleRad)/(this.max-this.min||1):this.transA=(this.center[2]-this.center[3])/2/(this.max-this.min||1),this.isXAxis?this.minPixelPadding=this.transA*this.minPointOffset:this.minPixelPadding=0)}function w(e){let{coll:i}=this,{angular:s,inverted:o,polar:a}=this.chart,r={};s?this.isXAxis||(r=sr(i4.yAxis,t.radialDefaultOptions.radialGauge)):a&&(r=this.horiz?sr(i4.xAxis,t.radialDefaultOptions.circular):sr("xAxis"===i?i4.xAxis:i4.yAxis,t.radialDefaultOptions.radial)),o&&"yAxis"===i&&(r.stackLabels=sa(i4.yAxis,!0)?i4.yAxis.stackLabels:{},r.reversedStacks=!0);let n=this.options=sr(r,e);n.plotBands||(n.plotBands=[]),so(this,"afterSetOptions")}function S(t,e,i,s,o,a,r){let n,l,h=this.axis;return h.isRadial?["M",e,i,"L",(n=h.getPosition(this.pos,h.center[2]/2+s)).x,n.y]:t.call(this,e,i,s,o,a,r)}t.radialDefaultOptions=sr(i6),t.compose=function(t,e){return sl(i9,"Axis.Radial")&&(st(t,"afterInit",c),st(t,"autoLabelAlign",u),st(t,"destroy",g),st(t,"init",f),st(t,"initialAxisTranslation",m),st(e,"afterGetLabelPosition",b),st(e,"afterGetPosition",x),st(y(),"setOptions",P),sd(e.prototype,"getMarkPath",S)),t}}(d||(d={}));let sc=d,{animObject:su}=y(),{composed:sg}=y(),{addEvent:sf,defined:sm,find:sb,isNumber:sy,merge:sx,pick:sP,pushUnique:sv,relativeLength:sM,splat:sL,uniqueKey:sk,wrap:sw}=y();function sS(){(this.pane||[]).forEach(t=>{t.render()})}function sA(t){let e=t.args[0].xAxis,i=t.args[0].yAxis,s=t.args[0].chart;e&&i&&("polygon"===i.gridLineInterpolation?(e.startOnTick=!0,e.endOnTick=!0):"polygon"===e.gridLineInterpolation&&s.inverted&&(i.startOnTick=!0,i.endOnTick=!0))}function sN(){this.pane||(this.pane=[]),this.options.pane=sL(this.options.pane||{}),sL(this.userOptions.pane||{}).forEach(t=>{new V(t,this)},this)}function sT(t){let e=t.args.marker,i=this.chart.xAxis[0],s=this.chart.yAxis[0],o=this.chart.inverted,a=o?s:i,r=o?i:s;if(this.chart.polar){t.preventDefault();let i=(e.attr?e.attr("start"):e.start)-a.startAngleRad,s=e.attr?e.attr("r"):e.r,o=(e.attr?e.attr("end"):e.end)-a.startAngleRad,n=e.attr?e.attr("innerR"):e.innerR;t.result.x=i+a.pos,t.result.width=o-i,t.result.y=r.len+r.pos-s,t.result.height=s-n}}function sC(t){let e=this.chart;if(e.polar&&e.hoverPane&&e.hoverPane.axis){t.preventDefault();let i=e.hoverPane.center,s=e.mouseDownX||0,o=e.mouseDownY||0,a=t.args.chartY,r=t.args.chartX,n=2*Math.PI,l=e.hoverPane.axis.startAngleRad,h=e.hoverPane.axis.endAngleRad,p=e.inverted?e.xAxis[0]:e.yAxis[0],d={},c="arc";if(d.x=i[0]+e.plotLeft,d.y=i[1]+e.plotTop,this.zoomHor){let t=l>0?h-l:Math.abs(l)+Math.abs(h),u=Math.atan2(o-e.plotTop-i[1],s-e.plotLeft-i[0])-l,g=Math.atan2(a-e.plotTop-i[1],r-e.plotLeft-i[0])-l;d.r=i[2]/2,d.innerR=i[3]/2,u<=0&&(u+=n),g<=0&&(g+=n),g<u&&(g=[u,u=g][0]),t<n&&l+g>h+(n-t)/2&&(g=u,u=l<=0?l:0);let f=d.start=Math.max(u+l,l),m=d.end=Math.min(g+l,h);if("polygon"===p.options.gridLineInterpolation){let t=e.hoverPane.axis,s=f-t.startAngleRad+t.pos,o=p.getPlotLinePath({value:p.max}),a=t.toValue(s),r=t.toValue(s+(m-f));if(a<t.getExtremes().min){let{min:e,max:i}=t.getExtremes();a=i-(e-a)}if(r<t.getExtremes().min){let{min:e,max:i}=t.getExtremes();r=i-(e-r)}r<a&&(r=[a,a=r][0]),(o=sR(o,a,r,t)).push(["L",i[0]+e.plotLeft,e.plotTop+i[1]]),d.d=o,c="path"}}if(this.zoomVert){let t=e.inverted?e.xAxis[0]:e.yAxis[0],n=Math.sqrt(Math.pow(s-e.plotLeft-i[0],2)+Math.pow(o-e.plotTop-i[1],2)),p=Math.sqrt(Math.pow(r-e.plotLeft-i[0],2)+Math.pow(a-e.plotTop-i[1],2));if(p<n&&(n=[p,p=n][0]),p>i[2]/2&&(p=i[2]/2),n<i[3]/2&&(n=i[3]/2),this.zoomHor||(d.start=l,d.end=h),d.r=p,d.innerR=n,"polygon"===t.options.gridLineInterpolation){let e=t.toValue(t.len+t.pos-n),i=t.toValue(t.len+t.pos-p);d.d=t.getPlotLinePath({value:i}).concat(t.getPlotLinePath({value:e,reverse:!0})),c="path"}}if(this.zoomHor&&this.zoomVert&&"polygon"===p.options.gridLineInterpolation){let t=e.hoverPane.axis,i=d.start||0,s=d.end||0,o=i-t.startAngleRad+t.pos,a=t.toValue(o),r=t.toValue(o+(s-i));if(d.d instanceof Array){let t=d.d.slice(0,d.d.length/2),i=d.d.slice(d.d.length/2,d.d.length);i=[...i].reverse();let s=e.hoverPane.axis;t=sR(t,a,r,s),(i=sR(i,a,r,s))&&(i[0][0]="L"),i=[...i].reverse(),d.d=t.concat(i),c="path"}}t.attrs=d,t.shapeType=c}}function sX(){let t=this.chart;t.polar&&(this.polar=new sq(this),t.inverted&&(this.isRadialSeries=!0,this.is("column")&&(this.isRadialBar=!0)))}function sY(){if(this.chart.polar&&this.xAxis){let{xAxis:t,yAxis:e}=this,i=this.chart;this.kdByAngle=i.tooltip&&i.tooltip.shared,this.kdByAngle||i.inverted?this.searchPoint=sI:this.options.findNearestPointBy="xy";let s=this.points,o=s.length;for(;o--;)this.is("column")||this.is("columnrange")||this.polar.toXY(s[o]),i.hasParallelCoordinates||this.yAxis.reversed||(sP(s[o].y,Number.MIN_VALUE)<e.min||s[o].x<t.min||s[o].x>t.max?(s[o].isNull=!0,s[o].plotY=NaN):s[o].isNull=s[o].isValid&&!s[o].isValid());this.hasClipCircleSetter||(this.hasClipCircleSetter=!!this.eventsToUnbind.push(sf(this,"afterRender",function(){let t;i.polar&&!1!==this.options.clip&&(t=this.yAxis.pane.center,this.clipCircle?this.clipCircle.animate({x:t[0],y:t[1],r:t[2]/2,innerR:t[3]/2}):this.clipCircle=function(t,e,i,s,o){let a=sk(),r=t.createElement("clipPath").attr({id:a}).add(t.defs),n=o?t.arc(e,i,s,o,0,2*Math.PI).add(r):t.circle(e,i,s).add(r);return n.id=a,n.clipPath=r,n}(i.renderer,t[0],t[1],t[2]/2,t[3]/2),this.group.clip(this.clipCircle),this.setClip=y().noop)})))}}function sI(t){let e=this.chart,i=this.xAxis,s=this.yAxis,o=i.pane&&i.pane.center,a=t.chartX-(o&&o[0]||0)-e.plotLeft,r=t.chartY-(o&&o[1]||0)-e.plotTop,n=e.inverted?{clientX:t.chartX-s.pos,plotY:t.chartY-i.pos}:{clientX:180+-180/Math.PI*Math.atan2(a,r)};return this.searchKDTree(n)}function sR(t,e,i,s){let o=s.tickInterval,a=s.tickPositions,r=sb(a,t=>t>=i),n=sb([...a].reverse(),t=>t<=e);return sm(r)||(r=a[a.length-1]),sm(n)||(n=a[0],r+=o,t[0][0]="L",t.unshift(t[t.length-3])),(t=t.slice(a.indexOf(n),a.indexOf(r)+1))[0][0]="M",t}function sE(t,e){return sb(this.pane||[],t=>t.options.id===e)||t.call(this,e)}function sD(t,e,i,s,o,a){let r,n,l,h=this.chart,p=sP(s.inside,!!this.options.stacking);if(h.polar){if(r=e.rectPlotX/Math.PI*180,h.inverted)this.forceDL=h.isInsidePlot(e.plotX,e.plotY),p&&e.shapeArgs?(n=e.shapeArgs,o=sx(o,{x:(l=this.yAxis.postTranslate(((n.start||0)+(n.end||0))/2-this.xAxis.startAngleRad,e.barX+e.pointWidth/2)).x-h.plotLeft,y:l.y-h.plotTop})):e.tooltipPos&&(o=sx(o,{x:e.tooltipPos[0],y:e.tooltipPos[1]})),s.align=sP(s.align,"center"),s.verticalAlign=sP(s.verticalAlign,"middle");else{var d;null===(d=s).align&&(d.align=r>20&&r<160?"left":r>200&&r<340?"right":"center"),null===d.verticalAlign&&(d.verticalAlign=r<45||r>315?"bottom":r>135&&r<225?"top":"middle"),s=d}M().prototype.alignDataLabel.call(this,e,i,s,o,a),this.isRadialBar&&e.shapeArgs&&e.shapeArgs.start===e.shapeArgs.end?i.hide():i.show()}else t.call(this,e,i,s,o,a)}function sz(){let t=this.options,e=t.stacking,i=this.chart,s=this.xAxis,o=this.yAxis,a=o.reversed,r=o.center,n=s.startAngleRad,l=s.endAngleRad-n,h=t.threshold,p=0,d,c,u,g,f,m=0,b=0,x,P,v,M,L,k,w,S;if(s.isRadial)for(u=(d=this.points).length,g=o.translate(o.min),f=o.translate(o.max),h=t.threshold||0,i.inverted&&sy(h)&&sm(p=o.translate(h))&&(p<0?p=0:p>l&&(p=l),this.translatedThreshold=p+n);u--;){if(k=(c=d[u]).barX,P=c.x,v=c.y,c.shapeType="arc",i.inverted){c.plotY=o.translate(v),e&&o.stacking?(L=o.stacking.stacks[(v<0?"-":"")+this.stackKey],this.visible&&L&&L[P]&&!c.isNull&&(M=L[P].points[this.getStackIndicator(void 0,P,this.index).key],m=o.translate(M[0]),b=o.translate(M[1]),sm(m)&&(m=y().clamp(m,0,l)))):(m=p,b=c.plotY),m>b&&(b=[m,m=b][0]),a?b>g?b=g:m<f?m=f:(m>g||b<f)&&(m=b=l):m<g?m=g:b>f?b=f:(b<g||m>f)&&(m=b=0),o.min>o.max&&(m=b=a?l:0),m+=n,b+=n,r&&(c.barX=k+=r[3]/2),w=Math.max(k,0),S=Math.max(k+c.pointWidth,0);let i=t.borderRadius,s=sM(("object"==typeof i?i.radius:i)||0,S-w);c.shapeArgs={x:r[0],y:r[1],r:S,innerR:w,start:m,end:b,borderRadius:s},c.opacity=m===b?0:void 0,c.plotY=(sm(this.translatedThreshold)&&(m<this.translatedThreshold?m:b))-n}else m=k+n,c.shapeArgs=this.polar.arc(c.yBottom,c.plotY,m,m+c.pointWidth),c.shapeArgs.borderRadius=0;this.polar.toXY(c),i.inverted?(x=o.postTranslate(c.rectPlotY,k+c.pointWidth/2),c.tooltipPos=[x.x-i.plotLeft,x.y-i.plotTop]):c.tooltipPos=[c.plotX,c.plotY],r&&(c.ttBelow=c.plotY>r[1])}}function sO(t,e){let i,s,o=this;if(this.chart.polar){e=e||this.points;for(let t=0;t<e.length;t++)if(!e[t].isNull){i=t;break}!1!==this.options.connectEnds&&void 0!==i&&(this.connectEnds=!0,e.splice(e.length,0,e[i]),s=!0),e.forEach(t=>{void 0===t.polarPlotY&&o.polar.toXY(t)})}let a=t.apply(this,[].slice.call(arguments,1));return s&&e.pop(),a}function sH(t,e){let i=this.chart,s={xAxis:[],yAxis:[]};return i.polar?i.axes.forEach(t=>{if("colorAxis"===t.coll)return;let o=t.isXAxis,a=t.center,r=e.chartX-a[0]-i.plotLeft,n=e.chartY-a[1]-i.plotTop;s[o?"xAxis":"yAxis"].push({axis:t,value:t.translate(o?Math.PI-Math.atan2(r,n):Math.sqrt(Math.pow(r,2)+Math.pow(n,2)),!0)})}):s=t.call(this,e),s}function sB(t,e){this.chart.polar||t.call(this,e)}function sW(t,e){let i=this,s=this.chart,o=this.group,a=this.markerGroup,r=this.xAxis&&this.xAxis.center,n=s.plotLeft,l=s.plotTop,h=this.options.animation,p,d,c,u,g,f;s.polar?i.isRadialBar?e||(i.startAngleRad=sP(i.translatedThreshold,i.xAxis.startAngleRad),y().seriesTypes.pie.prototype.animate.call(i,e)):(h=su(h),i.is("column")?e||(d=r[3]/2,i.points.forEach(t=>{c=t.graphic,g=(u=t.shapeArgs)&&u.r,f=u&&u.innerR,c&&u&&(c.attr({r:d,innerR:d}),c.animate({r:g,innerR:f},i.options.animation))})):e?(p={translateX:r[0]+n,translateY:r[1]+l,scaleX:.001,scaleY:.001},o.attr(p),a&&a.attr(p)):(p={translateX:n,translateY:l,scaleX:1,scaleY:1},o.animate(p,h),a&&a.animate(p,h))):t.call(this,e)}function sF(t,e,i,s){let o,a;if(this.chart.polar)if(s){let t=(a=function t(e,i,s,o){let a,r,n,l,h,p,d=+!!o,c=(a=i>=0&&i<=e.length-1?i:i<0?e.length-1+i:0)-1<0?e.length-(1+d):a-1,u=a+1>e.length-1?d:a+1,g=e[c],f=e[u],m=g.plotX,b=g.plotY,y=f.plotX,x=f.plotY,P=e[a].plotX,v=e[a].plotY;r=(1.5*P+m)/2.5,n=(1.5*v+b)/2.5,l=(1.5*P+y)/2.5,h=(1.5*v+x)/2.5;let M=Math.sqrt(Math.pow(r-P,2)+Math.pow(n-v,2)),L=Math.sqrt(Math.pow(l-P,2)+Math.pow(h-v,2)),k=Math.atan2(n-v,r-P);p=Math.PI/2+(k+Math.atan2(h-v,l-P))/2,Math.abs(k-p)>Math.PI/2&&(p-=Math.PI),r=P+Math.cos(p)*M,n=v+Math.sin(p)*M;let w={rightContX:l=P+Math.cos(Math.PI+p)*L,rightContY:h=v+Math.sin(Math.PI+p)*L,leftContX:r,leftContY:n,plotX:P,plotY:v};return s&&(w.prevPointCont=t(e,c,!1,o)),w}(e,s,!0,this.connectEnds)).prevPointCont&&a.prevPointCont.rightContX,i=a.prevPointCont&&a.prevPointCont.rightContY;o=["C",sy(t)?t:a.plotX,sy(i)?i:a.plotY,sy(a.leftContX)?a.leftContX:a.plotX,sy(a.leftContY)?a.leftContY:a.plotY,a.plotX,a.plotY]}else o=["M",i.plotX,i.plotY];else o=t.call(this,e,i,s);return o}function sG(t,e,i=this.plotY){if(!this.destroyed){let{plotX:s,series:o}=this,{chart:a}=o;return a.polar&&sy(s)&&sy(i)?[s+(e?a.plotLeft:0),i+(e?a.plotTop:0)]:t.call(this,e,i)}}class sq{static compose(t,e,i,s,o,a,r,n,l,h){if(V.compose(e,i),sc.compose(t,o),sv(sg,"Polar")){let t=e.prototype,o=a.prototype,p=i.prototype,d=s.prototype;if(sf(e,"afterDrawChartBox",sS),sf(e,"createAxes",sN),sf(e,"init",sA),sw(t,"get",sE),sw(p,"getCoordinates",sH),sw(p,"pinch",sB),sf(i,"getSelectionMarkerAttrs",sC),sf(i,"getSelectionBox",sT),sf(s,"afterInit",sX),sf(s,"afterTranslate",sY,{order:2}),sf(s,"afterColumnTranslate",sz,{order:4}),sw(d,"animate",sW),sw(o,"pos",sG),n){let t=n.prototype;sw(t,"alignDataLabel",sD),sw(t,"animate",sW)}if(l&&sw(l.prototype,"getGraphPath",sO),h){let t=h.prototype;sw(t,"getPointSpline",sF),r&&(r.prototype.getPointSpline=t.getPointSpline)}}}constructor(t){this.series=t}arc(t,e,i,s){let o=this.series,a=o.xAxis.center,r=o.yAxis.len,n=a[3]/2,l=r-e+n,h=r-sP(t,r)+n;return o.yAxis.reversed&&(l<0&&(l=n),h<0&&(h=n)),{x:a[0],y:a[1],r:l,innerR:h,start:i,end:s}}toXY(t){let e=this.series,i=e.chart,s=e.xAxis,o=e.yAxis,a=t.plotX,r=i.inverted,n=t.y,l=t.plotY,h=r?a:o.len-l,p;if(r&&e&&!e.isRadialBar&&(t.plotY=l=sy(n)?o.translate(n):0),t.rectPlotX=a,t.rectPlotY=l,o.center&&(h+=o.center[3]/2),sy(l)){let e=r?o.postTranslate(l,h):s.postTranslate(a,h);t.plotX=t.polarPlotX=e.x-i.plotLeft,t.plotY=t.polarPlotY=e.y-i.plotTop}e.kdByAngle?((p=(a/Math.PI*180+s.pane.options.startAngle)%360)<0&&(p+=360),t.clientX=p):t.clientX=t.plotX}}var sV=f(184),s_=f.n(sV);let{composed:sU}=y(),{addEvent:sK,objectEach:sZ,pushUnique:sj}=y();!function(t){function e(){let t=this.waterfall?.stacks;t&&(t.changed=!1,delete t.alreadyChanged)}function i(){let t=this.options.stackLabels;t?.enabled&&this.waterfall?.stacks&&this.waterfall.renderStackTotals()}function s(){this.waterfall||(this.waterfall=new a(this))}function o(){let t=this.axes;for(let e of this.series)if(e.options.stacking){for(let e of t)!e.isXAxis&&e.waterfall&&(e.waterfall.stacks.changed=!0);break}}t.compose=function(t,a){sj(sU,"Axis.Waterfall")&&(sK(t,"init",s),sK(t,"afterBuildStacks",e),sK(t,"afterRender",i),sK(a,"beforeRedraw",o))};class a{constructor(t){this.axis=t,this.stacks={changed:!1}}renderStackTotals(){let t=this.axis,e=t.waterfall?.stacks,i=t.stacking?.stackTotalGroup,s=new(s_())(t,t.options.stackLabels||{},!1,0,void 0);this.dummyStackItem=s,i&&sZ(e,t=>{sZ(t,(t,e)=>{s.total=t.stackTotal,s.x=+e,t.label&&(s.label=t.label),s_().prototype.render.call(s,i),t.label=s.label,delete s.label})}),s.total=null}}t.Composition=a}(c||(c={}));let s$=c,{isNumber:sQ}=y();class sJ extends tf().prototype.pointClass{getClassName(){let t=tZ().prototype.getClassName.call(this);return this.isSum?t+=" highcharts-sum":this.isIntermediateSum&&(t+=" highcharts-intermediate-sum"),t}isValid(){return sQ(this.y)||this.isSum||!!this.isIntermediateSum}}let{column:s0,line:s1}=P().seriesTypes,{addEvent:s2,arrayMax:s3,arrayMin:s5,correctFloat:s8,crisp:s6,extend:s4,isNumber:s9,merge:s7,objectEach:ot,pick:oe}=y();function oi(t,e){return Object.hasOwnProperty.call(t,e)}class os extends s0{generatePoints(){s0.prototype.generatePoints.apply(this);let t=this.getColumn("y",!0);for(let e=0,i=this.points.length;e<i;e++){let i=this.points[e],s=t[e];s9(s)&&(i.isIntermediateSum||i.isSum)&&(i.y=s8(s))}}processData(t){let e,i,s,o,a,r,n=this.options,l=this.getColumn("y"),h=n.data,p=l.length,d=n.threshold||0;s=i=o=a=0;for(let t=0;t<p;t++)r=l[t],e=h?.[t]||{},"sum"===r||e.isSum?l[t]=s8(s):"intermediateSum"===r||e.isIntermediateSum?(l[t]=s8(i),i=0):(s+=r,i+=r),o=Math.min(s,o),a=Math.max(s,a);super.processData.call(this,t),n.stacking||(this.dataMin=o+d,this.dataMax=a)}toYData(t){return t.isSum?"sum":t.isIntermediateSum?"intermediateSum":t.y}pointAttribs(t,e){let i=this.options.upColor;i&&!t.options.color&&s9(t.y)&&(t.color=t.y>0?i:void 0);let s=s0.prototype.pointAttribs.call(this,t,e);return delete s.dashstyle,s}getGraphPath(){return[["M",0,0]]}getCrispPath(){let t=this.data.filter(t=>s9(t.y)),e=this.yAxis,i=t.length,s=this.graph?.strokeWidth()||0,o=this.xAxis.reversed,a=this.yAxis.reversed,r=this.options.stacking,n=[];for(let l=1;l<i;l++){if(!(this.options.connectNulls||s9(this.data[t[l].index-1].y)))continue;let i=t[l].box,h=t[l-1],p=h.y||0,d=t[l-1].box;if(!i||!d)continue;let c=e.waterfall?.stacks[this.stackKey],u=p>0?-d.height:0;if(c&&d&&i){let t,p=c[l-1];if(r){let i=p.connectorThreshold;t=s6(e.translate(i,!1,!0,!1,!0)+(a?u:0),s)}else t=s6(d.y+(h.minPointLengthOffset||0),s);n.push(["M",(d.x||0)+(o?0:d.width||0),t],["L",(i.x||0)+(o&&i.width||0),t])}if(d&&n.length&&(!r&&p<0&&!a||p>0&&a)){let t=n[n.length-2];t&&"number"==typeof t[2]&&(t[2]+=d.height||0);let e=n[n.length-1];e&&"number"==typeof e[2]&&(e[2]+=d.height||0)}}return n}drawGraph(){s1.prototype.drawGraph.call(this),this.graph&&this.graph.attr({d:this.getCrispPath()})}setStackedPoints(t){let e=this.options,i=t.waterfall?.stacks,s=e.threshold||0,o=this.stackKey,a=this.getColumn("x"),r=this.getColumn("y"),n=a.length,l=s,h=l,p,d=0,c=0,u=0,g,f,m,b,y,x,P,v,M=(t,e,i,s)=>{if(p){if(g)for(;i<g;i++)p.stackState[i]+=s;else p.stackState[0]=t,g=p.stackState.length;p.stackState.push(p.stackState[g-1]+e)}};if(t.stacking&&i&&this.reserveSpace()){v=i.changed,(P=i.alreadyChanged)&&0>P.indexOf(o)&&(v=!0),i[o]||(i[o]={});let t=i[o];if(t)for(let i=0;i<n;i++)(!t[x=a[i]]||v)&&(t[x]={negTotal:0,posTotal:0,stackTotal:0,threshold:0,stateIndex:0,stackState:[],label:v&&t[x]?t[x].label:void 0}),p=t[x],(y=r[i])>=0?p.posTotal+=y:p.negTotal+=y,b=e.data[i],f=p.absolutePos=p.posTotal,m=p.absoluteNeg=p.negTotal,p.stackTotal=f+m,g=p.stackState.length,b?.isIntermediateSum?(M(u,c,0,u),u=c,c=s,l^=h,h^=l,l^=h):b?.isSum?(M(s,d,g,0),l=s):(M(l,y,0,d),b&&(d+=y,c+=y)),p.stateIndex++,p.threshold=l,l+=p.stackTotal;i.changed=!1,i.alreadyChanged||(i.alreadyChanged=[]),i.alreadyChanged.push(o)}}getExtremes(){let t,e,i=this.options.stacking,s=this.yAxis,o=s.waterfall?.stacks;return i&&o?(t=this.stackedYNeg=[],e=this.stackedYPos=[],"overlap"===i?ot(o[this.stackKey],function(i){t.push(s5(i.stackState)),e.push(s3(i.stackState))}):ot(o[this.stackKey],function(i){t.push(i.negTotal+i.threshold),e.push(i.posTotal+i.threshold)}),{dataMin:s5(t),dataMax:s3(e)}):{dataMin:this.dataMin,dataMax:this.dataMax}}}os.defaultOptions=s7(s0.defaultOptions,{dataLabels:{inside:!0},lineWidth:1,lineColor:"#333333",dashStyle:"Dot",borderColor:"#333333",states:{hover:{lineWidthPlus:0}}}),os.compose=s$.compose,s4(os.prototype,{pointValKey:"y",showLine:!0,pointClass:sJ}),s2(os,"afterColumnTranslate",function(){let{options:t,points:e,yAxis:i}=this,s=oe(t.minPointLength,5),o=s/2,a=t.threshold||0,r=t.stacking,n=i.waterfall?.stacks[this.stackKey],l=this.getColumn("y",!0),h=a,p=a,d,c,u,g;for(let t=0;t<e.length;t++){let f=e[t],m=l[t],b=s4({x:0,y:0,width:0,height:0},f.shapeArgs||{});f.box=b;let y=[0,m],x=f.y||0;if(r){if(n){let e=n[t];"overlap"===r?(c=e.stackState[e.stateIndex--],d=x>=0?c:c-x,oi(e,"absolutePos")&&delete e.absolutePos,oi(e,"absoluteNeg")&&delete e.absoluteNeg):(x>=0?(c=e.threshold+e.posTotal,e.posTotal-=x,d=c):(c=e.threshold+e.negTotal,e.negTotal-=x,d=c-x),!e.posTotal&&s9(e.absolutePos)&&oi(e,"absolutePos")&&(e.posTotal=e.absolutePos,delete e.absolutePos),!e.negTotal&&s9(e.absoluteNeg)&&oi(e,"absoluteNeg")&&(e.negTotal=e.absoluteNeg,delete e.absoluteNeg)),f.isSum||(e.connectorThreshold=e.threshold+e.stackTotal),i.reversed?(u=x>=0?d-x:d+x,g=d):(u=d,g=d-x),f.below=u<=a,b.y=i.translate(u,!1,!0,!1,!0),b.height=Math.abs(b.y-i.translate(g,!1,!0,!1,!0));let s=i.waterfall?.dummyStackItem;s&&(s.x=t,s.label=n[t].label,s.setOffset(this.pointXOffset||0,this.barW||0,this.stackedYNeg[t],this.stackedYPos[t],void 0,this.xAxis))}}else d=Math.max(p,p+x)+y[0],b.y=i.translate(d,!1,!0,!1,!0),f.isSum?(b.y=i.translate(y[1],!1,!0,!1,!0),b.height=Math.min(i.translate(y[0],!1,!0,!1,!0),i.len)-b.y,f.below=y[1]<=a):f.isIntermediateSum?(x>=0?(u=y[1]+h,g=h):(u=h,g=y[1]+h),i.reversed&&(u^=g,g^=u,u^=g),b.y=i.translate(u,!1,!0,!1,!0),b.height=Math.abs(b.y-Math.min(i.translate(g,!1,!0,!1,!0),i.len)),h+=y[1],f.below=u<=a):(b.height=m>0?i.translate(p,!1,!0,!1,!0)-b.y:i.translate(p,!1,!0,!1,!0)-i.translate(p-m,!1,!0,!1,!0),f.below=(p+=m)<a),b.height<0&&(b.y+=b.height,b.height*=-1);f.plotY=b.y,f.yBottom=b.y+b.height,b.height<=s&&!f.isNull?(b.height=s,b.y-=o,f.yBottom=b.y+b.height,f.plotY=b.y,x<0?f.minPointLengthOffset=-o:f.minPointLengthOffset=o):(f.isNull&&(b.width=0),f.minPointLengthOffset=0);let P=f.plotY+(f.negative?b.height:0);f.below&&(f.plotY+=b.height),f.tooltipPos&&(this.chart.inverted?f.tooltipPos[0]=i.len-P:f.tooltipPos[1]=P),f.isInside=this.isPointInside(f);let v=s6(f.yBottom,this.borderWidth);b.y=s6(b.y,this.borderWidth),b.height=v-b.y,s7(!0,f.shapeArgs,b)}},{order:2}),P().registerSeriesType("waterfall",os);let oo=y();oo.RadialAxis=sc,ea.compose(oo.Axis,oo.Chart,oo.Legend),iQ.compose(oo.Axis,oo.Chart,oo.Legend),V.compose(oo.Chart,oo.Pointer),sq.compose(oo.Axis,oo.Chart,oo.Pointer,oo.Series,oo.Tick,oo.Point,P().seriesTypes.areasplinerange,P().seriesTypes.column,P().seriesTypes.line,P().seriesTypes.spline),os.compose(oo.Axis,oo.Chart);let oa=oo;return m.default})());